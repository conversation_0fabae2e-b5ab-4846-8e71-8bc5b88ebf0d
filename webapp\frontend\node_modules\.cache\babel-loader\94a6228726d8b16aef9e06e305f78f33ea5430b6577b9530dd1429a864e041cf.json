{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\parco\\\\CreaBobinaPage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, Typography, Paper, Button, IconButton, Alert, Snackbar } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport ParcoCavi from '../../../components/cavi/ParcoCavi';\nimport { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreaBobinaPage = () => {\n  _s();\n  const {\n    isImpersonating\n  } = useAuth();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    tipo_cavo: '',\n    costruttore: '',\n    lotto: '',\n    anno_costruzione: '',\n    lunghezza_totale: '',\n    lunghezza_residua: '',\n    note: ''\n  });\n  const [errors, setErrors] = useState({});\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Gestisce il cambio dei valori del form\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Rimuovi l'errore quando l'utente inizia a digitare\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: null\n      });\n    }\n  };\n\n  // Valida il form\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.numero_bobina) {\n      newErrors.numero_bobina = 'Il numero bobina è obbligatorio';\n    }\n    if (!formData.tipo_cavo) {\n      newErrors.tipo_cavo = 'Il tipo di cavo è obbligatorio';\n    }\n    if (!formData.lunghezza_totale) {\n      newErrors.lunghezza_totale = 'La lunghezza totale è obbligatoria';\n    } else if (isNaN(formData.lunghezza_totale)) {\n      newErrors.lunghezza_totale = 'La lunghezza deve essere un numero';\n    }\n    if (!formData.lunghezza_residua) {\n      newErrors.lunghezza_residua = 'La lunghezza residua è obbligatoria';\n    } else if (isNaN(formData.lunghezza_residua)) {\n      newErrors.lunghezza_residua = 'La lunghezza deve essere un numero';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Salva la nuova bobina\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n    setLoading(true);\n    try {\n      // Converti i valori numerici\n      const bobinaData = {\n        ...formData,\n        lunghezza_totale: parseFloat(formData.lunghezza_totale),\n        lunghezza_residua: parseFloat(formData.lunghezza_residua),\n        anno_costruzione: formData.anno_costruzione ? parseInt(formData.anno_costruzione, 10) : null\n      };\n      await parcoCaviService.createBobina(cantiereId, bobinaData);\n      handleSuccess('Bobina creata con successo');\n\n      // Resetta il form\n      setFormData({\n        numero_bobina: '',\n        tipo_cavo: '',\n        costruttore: '',\n        lotto: '',\n        anno_costruzione: '',\n        lunghezza_totale: '',\n        lunghezza_residua: '',\n        note: ''\n      });\n    } catch (error) {\n      handleError(error.message || 'Errore nella creazione della bobina');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce le notifiche di successo\n  const handleSuccess = message => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce le notifiche di errore\n  const handleError = message => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToCantieri,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          children: \"Crea Nuova Bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserisci i dati della nuova bobina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Numero Bobina *\",\n            name: \"numero_bobina\",\n            value: formData.numero_bobina,\n            onChange: handleChange,\n            error: !!errors.numero_bobina,\n            helperText: errors.numero_bobina,\n            variant: \"outlined\",\n            margin: \"normal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Tipo Cavo *\",\n            name: \"tipo_cavo\",\n            value: formData.tipo_cavo,\n            onChange: handleChange,\n            error: !!errors.tipo_cavo,\n            helperText: errors.tipo_cavo,\n            variant: \"outlined\",\n            margin: \"normal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Costruttore\",\n            name: \"costruttore\",\n            value: formData.costruttore,\n            onChange: handleChange,\n            variant: \"outlined\",\n            margin: \"normal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Lotto\",\n            name: \"lotto\",\n            value: formData.lotto,\n            onChange: handleChange,\n            variant: \"outlined\",\n            margin: \"normal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 4,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Anno Costruzione\",\n            name: \"anno_costruzione\",\n            value: formData.anno_costruzione,\n            onChange: handleChange,\n            variant: \"outlined\",\n            margin: \"normal\",\n            type: \"number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 4,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Lunghezza Totale (m) *\",\n            name: \"lunghezza_totale\",\n            value: formData.lunghezza_totale,\n            onChange: handleChange,\n            error: !!errors.lunghezza_totale,\n            helperText: errors.lunghezza_totale,\n            variant: \"outlined\",\n            margin: \"normal\",\n            type: \"number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 4,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Lunghezza Residua (m) *\",\n            name: \"lunghezza_residua\",\n            value: formData.lunghezza_residua,\n            onChange: handleChange,\n            error: !!errors.lunghezza_residua,\n            helperText: errors.lunghezza_residua,\n            variant: \"outlined\",\n            margin: \"normal\",\n            type: \"number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Note\",\n            name: \"note\",\n            value: formData.note,\n            onChange: handleChange,\n            variant: \"outlined\",\n            margin: \"normal\",\n            multiline: true,\n            rows: 3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'flex-end'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 34\n          }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 67\n          }, this),\n          onClick: handleSave,\n          disabled: loading,\n          children: loading ? 'Salvataggio...' : 'Salva Bobina'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openSnackbar,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: alertSeverity,\n        sx: {\n          width: '100%'\n        },\n        children: alertMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n};\n_s(CreaBobinaPage, \"3Tk8h58vSLHOqm8Gt61ZraSqvaQ=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = CreaBobinaPage;\nexport default CreaBobinaPage;\nvar _c;\n$RefreshReg$(_c, \"CreaBobinaPage\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "Snackbar", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "useNavigate", "useAuth", "AdminHomeButton", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useState", "jsxDEV", "_jsxDEV", "CreaBobinaPage", "_s", "isImpersonating", "navigate", "loading", "setLoading", "alertMessage", "setAlertMessage", "alertSeverity", "setAlertSeverity", "openSnackbar", "setOpenSnackbar", "formData", "setFormData", "numero_bobina", "tipo_cavo", "costru<PERSON><PERSON>", "lotto", "anno_costruzione", "lunghezza_totale", "lunghezza_residua", "note", "errors", "setErrors", "cantiereId", "parseInt", "localStorage", "getItem", "cantiereName", "handleChange", "e", "name", "value", "target", "validateForm", "newErrors", "isNaN", "Object", "keys", "length", "handleSave", "bobina<PERSON><PERSON>", "parseFloat", "parcoCaviService", "createBobina", "handleSuccess", "error", "handleError", "message", "handleBackToCantieri", "handleCloseSnackbar", "children", "sx", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "window", "location", "reload", "ml", "color", "title", "p", "gutterBottom", "Grid", "container", "spacing", "mt", "item", "xs", "sm", "TextField", "fullWidth", "label", "onChange", "helperText", "margin", "type", "multiline", "rows", "startIcon", "CircularProgress", "size", "SaveIcon", "disabled", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/parco/CreaBobinaPage.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert,\n  Snackbar\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport ParcoCavi from '../../../components/cavi/ParcoCavi';\nimport { useState } from 'react';\n\nconst CreaBobinaPage = () => {\n  const { isImpersonating } = useAuth();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    tipo_cavo: '',\n    costruttore: '',\n    lotto: '',\n    anno_costruzione: '',\n    lunghezza_totale: '',\n    lunghezza_residua: '',\n    note: ''\n  });\n  const [errors, setErrors] = useState({});\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Gestisce il cambio dei valori del form\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Rimuovi l'errore quando l'utente inizia a digitare\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: null\n      });\n    }\n  };\n\n  // Valida il form\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.numero_bobina) {\n      newErrors.numero_bobina = 'Il numero bobina è obbligatorio';\n    }\n\n    if (!formData.tipo_cavo) {\n      newErrors.tipo_cavo = 'Il tipo di cavo è obbligatorio';\n    }\n\n    if (!formData.lunghezza_totale) {\n      newErrors.lunghezza_totale = 'La lunghezza totale è obbligatoria';\n    } else if (isNaN(formData.lunghezza_totale)) {\n      newErrors.lunghezza_totale = 'La lunghezza deve essere un numero';\n    }\n\n    if (!formData.lunghezza_residua) {\n      newErrors.lunghezza_residua = 'La lunghezza residua è obbligatoria';\n    } else if (isNaN(formData.lunghezza_residua)) {\n      newErrors.lunghezza_residua = 'La lunghezza deve essere un numero';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Salva la nuova bobina\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Converti i valori numerici\n      const bobinaData = {\n        ...formData,\n        lunghezza_totale: parseFloat(formData.lunghezza_totale),\n        lunghezza_residua: parseFloat(formData.lunghezza_residua),\n        anno_costruzione: formData.anno_costruzione ? parseInt(formData.anno_costruzione, 10) : null\n      };\n\n      await parcoCaviService.createBobina(cantiereId, bobinaData);\n      handleSuccess('Bobina creata con successo');\n\n      // Resetta il form\n      setFormData({\n        numero_bobina: '',\n        tipo_cavo: '',\n        costruttore: '',\n        lotto: '',\n        anno_costruzione: '',\n        lunghezza_totale: '',\n        lunghezza_residua: '',\n        note: ''\n      });\n    } catch (error) {\n      handleError(error.message || 'Errore nella creazione della bobina');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce le notifiche di successo\n  const handleSuccess = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce le notifiche di errore\n  const handleError = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Crea Nuova Bobina\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserisci i dati della nuova bobina\n        </Typography>\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12} sm={6}>\n            <TextField\n              fullWidth\n              label=\"Numero Bobina *\"\n              name=\"numero_bobina\"\n              value={formData.numero_bobina}\n              onChange={handleChange}\n              error={!!errors.numero_bobina}\n              helperText={errors.numero_bobina}\n              variant=\"outlined\"\n              margin=\"normal\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={6}>\n            <TextField\n              fullWidth\n              label=\"Tipo Cavo *\"\n              name=\"tipo_cavo\"\n              value={formData.tipo_cavo}\n              onChange={handleChange}\n              error={!!errors.tipo_cavo}\n              helperText={errors.tipo_cavo}\n              variant=\"outlined\"\n              margin=\"normal\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={6}>\n            <TextField\n              fullWidth\n              label=\"Costruttore\"\n              name=\"costruttore\"\n              value={formData.costruttore}\n              onChange={handleChange}\n              variant=\"outlined\"\n              margin=\"normal\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={6}>\n            <TextField\n              fullWidth\n              label=\"Lotto\"\n              name=\"lotto\"\n              value={formData.lotto}\n              onChange={handleChange}\n              variant=\"outlined\"\n              margin=\"normal\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={4}>\n            <TextField\n              fullWidth\n              label=\"Anno Costruzione\"\n              name=\"anno_costruzione\"\n              value={formData.anno_costruzione}\n              onChange={handleChange}\n              variant=\"outlined\"\n              margin=\"normal\"\n              type=\"number\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={4}>\n            <TextField\n              fullWidth\n              label=\"Lunghezza Totale (m) *\"\n              name=\"lunghezza_totale\"\n              value={formData.lunghezza_totale}\n              onChange={handleChange}\n              error={!!errors.lunghezza_totale}\n              helperText={errors.lunghezza_totale}\n              variant=\"outlined\"\n              margin=\"normal\"\n              type=\"number\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={4}>\n            <TextField\n              fullWidth\n              label=\"Lunghezza Residua (m) *\"\n              name=\"lunghezza_residua\"\n              value={formData.lunghezza_residua}\n              onChange={handleChange}\n              error={!!errors.lunghezza_residua}\n              helperText={errors.lunghezza_residua}\n              variant=\"outlined\"\n              margin=\"normal\"\n              type=\"number\"\n            />\n          </Grid>\n          <Grid item xs={12}>\n            <TextField\n              fullWidth\n              label=\"Note\"\n              name=\"note\"\n              value={formData.note}\n              onChange={handleChange}\n              variant=\"outlined\"\n              margin=\"normal\"\n              multiline\n              rows={3}\n            />\n          </Grid>\n        </Grid>\n\n        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={loading ? <CircularProgress size={24} /> : <SaveIcon />}\n            onClick={handleSave}\n            disabled={loading}\n          >\n            {loading ? 'Salvataggio...' : 'Salva Bobina'}\n          </Button>\n        </Box>\n      </Paper>\n\n      <Snackbar\n        open={openSnackbar}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>\n          {alertMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default CreaBobinaPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,QAAQ,QACH,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,SAASC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAgB,CAAC,GAAGR,OAAO,CAAC,CAAC;EACrC,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC;IACvCiB,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,gBAAgB,EAAE,EAAE;IACpBC,gBAAgB,EAAE,EAAE;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAM2B,UAAU,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;EAC3E,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAAI,YAAYH,UAAU,EAAE;;EAE7F;EACA,MAAMK,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCpB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACmB,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,IAAIV,MAAM,CAACS,IAAI,CAAC,EAAE;MAChBR,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACS,IAAI,GAAG;MACV,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACvB,QAAQ,CAACE,aAAa,EAAE;MAC3BqB,SAAS,CAACrB,aAAa,GAAG,iCAAiC;IAC7D;IAEA,IAAI,CAACF,QAAQ,CAACG,SAAS,EAAE;MACvBoB,SAAS,CAACpB,SAAS,GAAG,gCAAgC;IACxD;IAEA,IAAI,CAACH,QAAQ,CAACO,gBAAgB,EAAE;MAC9BgB,SAAS,CAAChB,gBAAgB,GAAG,oCAAoC;IACnE,CAAC,MAAM,IAAIiB,KAAK,CAACxB,QAAQ,CAACO,gBAAgB,CAAC,EAAE;MAC3CgB,SAAS,CAAChB,gBAAgB,GAAG,oCAAoC;IACnE;IAEA,IAAI,CAACP,QAAQ,CAACQ,iBAAiB,EAAE;MAC/Be,SAAS,CAACf,iBAAiB,GAAG,qCAAqC;IACrE,CAAC,MAAM,IAAIgB,KAAK,CAACxB,QAAQ,CAACQ,iBAAiB,CAAC,EAAE;MAC5Ce,SAAS,CAACf,iBAAiB,GAAG,oCAAoC;IACpE;IAEAG,SAAS,CAACY,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA7B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAMoC,UAAU,GAAG;QACjB,GAAG7B,QAAQ;QACXO,gBAAgB,EAAEuB,UAAU,CAAC9B,QAAQ,CAACO,gBAAgB,CAAC;QACvDC,iBAAiB,EAAEsB,UAAU,CAAC9B,QAAQ,CAACQ,iBAAiB,CAAC;QACzDF,gBAAgB,EAAEN,QAAQ,CAACM,gBAAgB,GAAGO,QAAQ,CAACb,QAAQ,CAACM,gBAAgB,EAAE,EAAE,CAAC,GAAG;MAC1F,CAAC;MAED,MAAMyB,gBAAgB,CAACC,YAAY,CAACpB,UAAU,EAAEiB,UAAU,CAAC;MAC3DI,aAAa,CAAC,4BAA4B,CAAC;;MAE3C;MACAhC,WAAW,CAAC;QACVC,aAAa,EAAE,EAAE;QACjBC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,EAAE;QACTC,gBAAgB,EAAE,EAAE;QACpBC,gBAAgB,EAAE,EAAE;QACpBC,iBAAiB,EAAE,EAAE;QACrBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdC,WAAW,CAACD,KAAK,CAACE,OAAO,IAAI,qCAAqC,CAAC;IACrE,CAAC,SAAS;MACR3C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4C,oBAAoB,GAAGA,CAAA,KAAM;IACjC9C,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAED;EACA,MAAM0C,aAAa,GAAIG,OAAO,IAAK;IACjCzC,eAAe,CAACyC,OAAO,CAAC;IACxBvC,gBAAgB,CAAC,SAAS,CAAC;IAC3BE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMoC,WAAW,GAAIC,OAAO,IAAK;IAC/BzC,eAAe,CAACyC,OAAO,CAAC;IACxBvC,gBAAgB,CAAC,OAAO,CAAC;IACzBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuC,mBAAmB,GAAGA,CAAA,KAAM;IAChCvC,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,oBACEZ,OAAA,CAACjB,GAAG;IAAAqE,QAAA,gBACFpD,OAAA,CAACjB,GAAG;MAACsE,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAL,QAAA,gBACzFpD,OAAA,CAACjB,GAAG;QAACsE,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBACjDpD,OAAA,CAACb,UAAU;UAACuE,OAAO,EAAER,oBAAqB;UAACG,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,eACvDpD,OAAA,CAACT,aAAa;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACb/D,OAAA,CAAChB,UAAU;UAACgF,OAAO,EAAC,IAAI;UAAAZ,QAAA,EAAC;QAEzB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/D,OAAA,CAACb,UAAU;UACTuE,OAAO,EAAEA,CAAA,KAAMO,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCd,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE,CAAE;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAC,oBAAoB;UAAAlB,QAAA,eAE1BpD,OAAA,CAACP,WAAW;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN/D,OAAA,CAACJ,eAAe;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAEN/D,OAAA,CAACf,KAAK;MAACoE,EAAE,EAAE;QAAEkB,CAAC,EAAE;MAAE,CAAE;MAAAnB,QAAA,gBAClBpD,OAAA,CAAChB,UAAU;QAACgF,OAAO,EAAC,IAAI;QAACQ,YAAY;QAAApB,QAAA,EAAC;MAEtC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb/D,OAAA,CAACyE,IAAI;QAACC,SAAS;QAACC,OAAO,EAAE,CAAE;QAACtB,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAE,CAAE;QAAAxB,QAAA,gBACxCpD,OAAA,CAACyE,IAAI;UAACI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBpD,OAAA,CAACgF,SAAS;YACRC,SAAS;YACTC,KAAK,EAAC,iBAAiB;YACvBlD,IAAI,EAAC,eAAe;YACpBC,KAAK,EAAEpB,QAAQ,CAACE,aAAc;YAC9BoE,QAAQ,EAAErD,YAAa;YACvBiB,KAAK,EAAE,CAAC,CAACxB,MAAM,CAACR,aAAc;YAC9BqE,UAAU,EAAE7D,MAAM,CAACR,aAAc;YACjCiD,OAAO,EAAC,UAAU;YAClBqB,MAAM,EAAC;UAAQ;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/D,OAAA,CAACyE,IAAI;UAACI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBpD,OAAA,CAACgF,SAAS;YACRC,SAAS;YACTC,KAAK,EAAC,aAAa;YACnBlD,IAAI,EAAC,WAAW;YAChBC,KAAK,EAAEpB,QAAQ,CAACG,SAAU;YAC1BmE,QAAQ,EAAErD,YAAa;YACvBiB,KAAK,EAAE,CAAC,CAACxB,MAAM,CAACP,SAAU;YAC1BoE,UAAU,EAAE7D,MAAM,CAACP,SAAU;YAC7BgD,OAAO,EAAC,UAAU;YAClBqB,MAAM,EAAC;UAAQ;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/D,OAAA,CAACyE,IAAI;UAACI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBpD,OAAA,CAACgF,SAAS;YACRC,SAAS;YACTC,KAAK,EAAC,aAAa;YACnBlD,IAAI,EAAC,aAAa;YAClBC,KAAK,EAAEpB,QAAQ,CAACI,WAAY;YAC5BkE,QAAQ,EAAErD,YAAa;YACvBkC,OAAO,EAAC,UAAU;YAClBqB,MAAM,EAAC;UAAQ;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/D,OAAA,CAACyE,IAAI;UAACI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBpD,OAAA,CAACgF,SAAS;YACRC,SAAS;YACTC,KAAK,EAAC,OAAO;YACblD,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEpB,QAAQ,CAACK,KAAM;YACtBiE,QAAQ,EAAErD,YAAa;YACvBkC,OAAO,EAAC,UAAU;YAClBqB,MAAM,EAAC;UAAQ;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/D,OAAA,CAACyE,IAAI;UAACI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBpD,OAAA,CAACgF,SAAS;YACRC,SAAS;YACTC,KAAK,EAAC,kBAAkB;YACxBlD,IAAI,EAAC,kBAAkB;YACvBC,KAAK,EAAEpB,QAAQ,CAACM,gBAAiB;YACjCgE,QAAQ,EAAErD,YAAa;YACvBkC,OAAO,EAAC,UAAU;YAClBqB,MAAM,EAAC,QAAQ;YACfC,IAAI,EAAC;UAAQ;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/D,OAAA,CAACyE,IAAI;UAACI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBpD,OAAA,CAACgF,SAAS;YACRC,SAAS;YACTC,KAAK,EAAC,wBAAwB;YAC9BlD,IAAI,EAAC,kBAAkB;YACvBC,KAAK,EAAEpB,QAAQ,CAACO,gBAAiB;YACjC+D,QAAQ,EAAErD,YAAa;YACvBiB,KAAK,EAAE,CAAC,CAACxB,MAAM,CAACH,gBAAiB;YACjCgE,UAAU,EAAE7D,MAAM,CAACH,gBAAiB;YACpC4C,OAAO,EAAC,UAAU;YAClBqB,MAAM,EAAC,QAAQ;YACfC,IAAI,EAAC;UAAQ;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/D,OAAA,CAACyE,IAAI;UAACI,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBpD,OAAA,CAACgF,SAAS;YACRC,SAAS;YACTC,KAAK,EAAC,yBAAyB;YAC/BlD,IAAI,EAAC,mBAAmB;YACxBC,KAAK,EAAEpB,QAAQ,CAACQ,iBAAkB;YAClC8D,QAAQ,EAAErD,YAAa;YACvBiB,KAAK,EAAE,CAAC,CAACxB,MAAM,CAACF,iBAAkB;YAClC+D,UAAU,EAAE7D,MAAM,CAACF,iBAAkB;YACrC2C,OAAO,EAAC,UAAU;YAClBqB,MAAM,EAAC,QAAQ;YACfC,IAAI,EAAC;UAAQ;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/D,OAAA,CAACyE,IAAI;UAACI,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA1B,QAAA,eAChBpD,OAAA,CAACgF,SAAS;YACRC,SAAS;YACTC,KAAK,EAAC,MAAM;YACZlD,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEpB,QAAQ,CAACS,IAAK;YACrB6D,QAAQ,EAAErD,YAAa;YACvBkC,OAAO,EAAC,UAAU;YAClBqB,MAAM,EAAC,QAAQ;YACfE,SAAS;YACTC,IAAI,EAAE;UAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP/D,OAAA,CAACjB,GAAG;QAACsE,EAAE,EAAE;UAAEuB,EAAE,EAAE,CAAC;UAAErB,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE;QAAW,CAAE;QAAAL,QAAA,eAC9DpD,OAAA,CAACd,MAAM;UACL8E,OAAO,EAAC,WAAW;UACnBK,KAAK,EAAC,SAAS;UACfoB,SAAS,EAAEpF,OAAO,gBAAGL,OAAA,CAAC0F,gBAAgB;YAACC,IAAI,EAAE;UAAG;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG/D,OAAA,CAAC4F,QAAQ;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnEL,OAAO,EAAEjB,UAAW;UACpBoD,QAAQ,EAAExF,OAAQ;UAAA+C,QAAA,EAEjB/C,OAAO,GAAG,gBAAgB,GAAG;QAAc;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAER/D,OAAA,CAACX,QAAQ;MACPyG,IAAI,EAAEnF,YAAa;MACnBoF,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAE7C,mBAAoB;MAC7B8C,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA/C,QAAA,eAE3DpD,OAAA,CAACZ,KAAK;QAAC4G,OAAO,EAAE7C,mBAAoB;QAACiD,QAAQ,EAAE3F,aAAc;QAAC4C,EAAE,EAAE;UAAEgD,KAAK,EAAE;QAAO,CAAE;QAAAjD,QAAA,EACjF7C;MAAY;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC7D,EAAA,CA7RID,cAAc;EAAA,QACUN,OAAO,EAClBD,WAAW;AAAA;AAAA4G,EAAA,GAFxBrG,cAAc;AA+RpB,eAAeA,cAAc;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}