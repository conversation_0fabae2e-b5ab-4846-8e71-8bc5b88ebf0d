{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"せんしゅうのeeeeのp\",\n  yesterday: \"きのうのp\",\n  today: \"きょうのp\",\n  tomorrow: \"あしたのp\",\n  nextWeek: \"よくしゅうのeeeeのp\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => {\n  return formatRelativeLocale[token];\n};", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/ja-Hira/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"せんしゅうのeeeeのp\",\n  yesterday: \"きのうのp\",\n  today: \"きょうのp\",\n  tomorrow: \"あしたのp\",\n  nextWeek: \"よくしゅうのeeeeのp\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) => {\n  return formatRelativeLocale[token];\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,cAAc;EACxBC,SAAS,EAAE,OAAO;EAClBC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,cAAc;EACxBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAAK;EACnE,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}