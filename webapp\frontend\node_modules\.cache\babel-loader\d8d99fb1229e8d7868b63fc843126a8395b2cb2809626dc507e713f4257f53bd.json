{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\charts\\\\ProgressChart.js\";\nimport React from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line } from 'recharts';\nimport { Box, Typography, Grid, Paper } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COLORS = {\n  primary: '#2c3e50',\n  secondary: '#34495e',\n  success: '#27ae60',\n  warning: '#f39c12',\n  info: '#3498db',\n  error: '#e74c3c',\n  light: '#ecf0f1',\n  dark: '#2c3e50',\n  accent: '#9b59b6'\n};\nconst ProgressChart = ({\n  data\n}) => {\n  var _data$posa_recente;\n  if (!data) return null;\n\n  // Dati per il grafico a torta dell'avanzamento\n  const progressData = [{\n    name: 'Metri Posati',\n    value: data.metri_posati,\n    color: COLORS.success\n  }, {\n    name: 'Metri Rimanenti',\n    value: data.metri_da_posare,\n    color: COLORS.warning\n  }];\n\n  // Dati per il grafico a torta dei cavi\n  const caviData = [{\n    name: 'Cavi Posati',\n    value: data.cavi_posati,\n    color: COLORS.success\n  }, {\n    name: 'Cavi Rimanenti',\n    value: data.cavi_rimanenti,\n    color: COLORS.warning\n  }];\n\n  // Dati per il grafico a barre delle metriche principali\n  const metricsData = [{\n    name: 'Metri',\n    Totali: data.metri_totali,\n    Posati: data.metri_posati,\n    Rimanenti: data.metri_da_posare\n  }, {\n    name: 'Cavi',\n    Totali: data.totale_cavi,\n    Posati: data.cavi_posati,\n    Rimanenti: data.cavi_rimanenti\n  }];\n\n  // Dati per il grafico temporale della posa recente\n  const posaTrendData = ((_data$posa_recente = data.posa_recente) === null || _data$posa_recente === void 0 ? void 0 : _data$posa_recente.map(posa => ({\n    data: posa.data,\n    metri: posa.metri\n  }))) || [];\n  const CustomTooltip = ({\n    active,\n    payload,\n    label\n  }) => {\n    if (active && payload && payload.length) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1,\n          border: '1px solid #ccc'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `${label}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), payload.map((entry, index) => /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          style: {\n            color: entry.color\n          },\n          children: `${entry.name}: ${entry.value}`\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderCustomizedLabel = ({\n    cx,\n    cy,\n    midAngle,\n    innerRadius,\n    outerRadius,\n    percent\n  }) => {\n    if (percent < 0.05) return null; // Non mostrare etichette per fette troppo piccole\n\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n    return /*#__PURE__*/_jsxDEV(\"text\", {\n      x: x,\n      y: y,\n      fill: \"white\",\n      textAnchor: x > cx ? 'start' : 'end',\n      dominantBaseline: \"central\",\n      fontSize: \"12\",\n      fontWeight: \"bold\",\n      children: `${(percent * 100).toFixed(0)}%`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      sx: {\n        fontWeight: 600,\n        mb: 3\n      },\n      children: \"Grafici di Avanzamento\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: 350,\n            bgcolor: 'grey.50'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            align: \"center\",\n            sx: {\n              fontWeight: 600,\n              mb: 2\n            },\n            children: \"Avanzamento Metri\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            align: \"center\",\n            sx: {\n              mb: 2,\n              color: 'text.secondary'\n            },\n            children: [data.percentuale_avanzamento, \"% completato\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 250,\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: progressData,\n                cx: \"50%\",\n                cy: \"50%\",\n                labelLine: false,\n                label: renderCustomizedLabel,\n                outerRadius: 90,\n                innerRadius: 40,\n                fill: \"#8884d8\",\n                dataKey: \"value\",\n                strokeWidth: 2,\n                children: progressData.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: entry.color\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {\n                verticalAlign: \"bottom\",\n                height: 36,\n                iconType: \"circle\",\n                wrapperStyle: {\n                  fontSize: '14px',\n                  fontWeight: 'bold'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: 350,\n            bgcolor: 'grey.50'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            align: \"center\",\n            sx: {\n              fontWeight: 600,\n              mb: 2\n            },\n            children: \"Avanzamento Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            align: \"center\",\n            sx: {\n              mb: 2,\n              color: 'text.secondary'\n            },\n            children: [data.percentuale_cavi, \"% completato\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 250,\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: caviData,\n                cx: \"50%\",\n                cy: \"50%\",\n                labelLine: false,\n                label: renderCustomizedLabel,\n                outerRadius: 90,\n                innerRadius: 40,\n                fill: \"#8884d8\",\n                dataKey: \"value\",\n                strokeWidth: 2,\n                children: caviData.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: entry.color\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {\n                verticalAlign: \"bottom\",\n                height: 36,\n                iconType: \"circle\",\n                wrapperStyle: {\n                  fontSize: '14px',\n                  fontWeight: 'bold'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: 350,\n            bgcolor: 'grey.50'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            align: \"center\",\n            sx: {\n              fontWeight: 600,\n              mb: 2\n            },\n            children: \"Confronto Metriche\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 280,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: metricsData,\n              margin: {\n                top: 20,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\",\n                stroke: \"#e0e0e0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"name\",\n                tick: {\n                  fontSize: 12,\n                  fontWeight: 'bold'\n                },\n                axisLine: {\n                  stroke: '#666'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                tick: {\n                  fontSize: 12\n                },\n                axisLine: {\n                  stroke: '#666'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {\n                wrapperStyle: {\n                  fontSize: '12px',\n                  fontWeight: 'bold'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"Totali\",\n                fill: COLORS.primary,\n                radius: [4, 4, 0, 0]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"Posati\",\n                fill: COLORS.success,\n                radius: [4, 4, 0, 0]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"Rimanenti\",\n                fill: COLORS.warning,\n                radius: [4, 4, 0, 0]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), posaTrendData.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: 350,\n            bgcolor: 'grey.50'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            align: \"center\",\n            sx: {\n              fontWeight: 600,\n              mb: 2\n            },\n            children: \"Trend Posa Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            align: \"center\",\n            sx: {\n              mb: 2,\n              color: 'text.secondary'\n            },\n            children: [\"Media: \", data.media_giornaliera, \"m/giorno\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 280,\n            children: /*#__PURE__*/_jsxDEV(LineChart, {\n              data: posaTrendData,\n              margin: {\n                top: 20,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\",\n                stroke: \"#e0e0e0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"data\",\n                tick: {\n                  fontSize: 10\n                },\n                axisLine: {\n                  stroke: '#666'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                tick: {\n                  fontSize: 12\n                },\n                axisLine: {\n                  stroke: '#666'\n                },\n                label: {\n                  value: 'Metri',\n                  angle: -90,\n                  position: 'insideLeft'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {\n                wrapperStyle: {\n                  fontSize: '12px',\n                  fontWeight: 'bold'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"metri\",\n                stroke: COLORS.primary,\n                strokeWidth: 3,\n                dot: {\n                  fill: COLORS.primary,\n                  strokeWidth: 2,\n                  r: 5\n                },\n                activeDot: {\n                  r: 8,\n                  stroke: COLORS.primary,\n                  strokeWidth: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n};\n_c = ProgressChart;\nexport default ProgressChart;\nvar _c;\n$RefreshReg$(_c, \"ProgressChart\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "Line<PERSON>hart", "Line", "Box", "Typography", "Grid", "Paper", "jsxDEV", "_jsxDEV", "COLORS", "primary", "secondary", "success", "warning", "info", "error", "light", "dark", "accent", "ProgressChart", "data", "_data$posa_recente", "progressData", "name", "value", "metri_posati", "color", "metri_da_posare", "caviData", "cavi_posati", "cavi_rimanenti", "metricsData", "Totali", "metri_totali", "Posati", "<PERSON><PERSON><PERSON><PERSON>", "totale_cavi", "posaTrendData", "posa_recente", "map", "posa", "metri", "CustomTooltip", "active", "payload", "label", "length", "sx", "p", "border", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entry", "index", "style", "renderCustomizedLabel", "cx", "cy", "midAngle", "innerRadius", "outerRadius", "percent", "RADIAN", "Math", "PI", "radius", "x", "cos", "y", "sin", "fill", "textAnchor", "dominantBaseline", "fontSize", "fontWeight", "toFixed", "gutterBottom", "mb", "container", "spacing", "item", "xs", "md", "height", "bgcolor", "align", "percentuale_avanzamento", "width", "labelLine", "dataKey", "strokeWidth", "content", "verticalAlign", "iconType", "wrapperStyle", "percentuale_cavi", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stroke", "tick", "axisLine", "media_giornaliera", "angle", "position", "type", "dot", "r", "activeDot", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/charts/ProgressChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Pie,\n  Cell,\n  BarChart,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  <PERSON>lt<PERSON>,\n  Legend,\n  ResponsiveContainer,\n  LineChart,\n  Line\n} from 'recharts';\nimport { Box, Typography, Grid, Paper } from '@mui/material';\n\nconst COLORS = {\n  primary: '#2c3e50',\n  secondary: '#34495e',\n  success: '#27ae60',\n  warning: '#f39c12',\n  info: '#3498db',\n  error: '#e74c3c',\n  light: '#ecf0f1',\n  dark: '#2c3e50',\n  accent: '#9b59b6'\n};\n\nconst ProgressChart = ({ data }) => {\n  if (!data) return null;\n\n  // Dati per il grafico a torta dell'avanzamento\n  const progressData = [\n    {\n      name: 'Metri Posati',\n      value: data.metri_posati,\n      color: COLORS.success\n    },\n    {\n      name: 'Metri Rimanenti',\n      value: data.metri_da_posare,\n      color: COLORS.warning\n    }\n  ];\n\n  // Dati per il grafico a torta dei cavi\n  const caviData = [\n    {\n      name: '<PERSON><PERSON>',\n      value: data.cavi_posati,\n      color: COLORS.success\n    },\n    {\n      name: '<PERSON><PERSON>',\n      value: data.cavi_rimanenti,\n      color: COLORS.warning\n    }\n  ];\n\n  // Dati per il grafico a barre delle metriche principali\n  const metricsData = [\n    {\n      name: 'Metri',\n      Totali: data.metri_totali,\n      Posati: data.metri_posati,\n      Rimanenti: data.metri_da_posare\n    },\n    {\n      name: 'Cavi',\n      Totali: data.totale_cavi,\n      Posati: data.cavi_posati,\n      Rimanenti: data.cavi_rimanenti\n    }\n  ];\n\n  // Dati per il grafico temporale della posa recente\n  const posaTrendData = data.posa_recente?.map(posa => ({\n    data: posa.data,\n    metri: posa.metri\n  })) || [];\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${entry.value}`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {\n    if (percent < 0.05) return null; // Non mostrare etichette per fette troppo piccole\n\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n\n    return (\n      <text\n        x={x}\n        y={y}\n        fill=\"white\"\n        textAnchor={x > cx ? 'start' : 'end'}\n        dominantBaseline=\"central\"\n        fontSize=\"12\"\n        fontWeight=\"bold\"\n      >\n        {`${(percent * 100).toFixed(0)}%`}\n      </text>\n    );\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h6\" gutterBottom sx={{ fontWeight: 600, mb: 3 }}>\n        Grafici di Avanzamento\n      </Typography>\n\n      <Grid container spacing={3}>\n        {/* Grafici a torta - Avanzamento Metri e Cavi */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3, height: 350, bgcolor: 'grey.50' }}>\n            <Typography variant=\"h6\" gutterBottom align=\"center\" sx={{ fontWeight: 600, mb: 2 }}>\n              Avanzamento Metri\n            </Typography>\n            <Typography variant=\"body2\" align=\"center\" sx={{ mb: 2, color: 'text.secondary' }}>\n              {data.percentuale_avanzamento}% completato\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={250}>\n              <PieChart>\n                <Pie\n                  data={progressData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={renderCustomizedLabel}\n                  outerRadius={90}\n                  innerRadius={40}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                  strokeWidth={2}\n                >\n                  {progressData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip content={<CustomTooltip />} />\n                <Legend\n                  verticalAlign=\"bottom\"\n                  height={36}\n                  iconType=\"circle\"\n                  wrapperStyle={{ fontSize: '14px', fontWeight: 'bold' }}\n                />\n              </PieChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3, height: 350, bgcolor: 'grey.50' }}>\n            <Typography variant=\"h6\" gutterBottom align=\"center\" sx={{ fontWeight: 600, mb: 2 }}>\n              Avanzamento Cavi\n            </Typography>\n            <Typography variant=\"body2\" align=\"center\" sx={{ mb: 2, color: 'text.secondary' }}>\n              {data.percentuale_cavi}% completato\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={250}>\n              <PieChart>\n                <Pie\n                  data={caviData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={renderCustomizedLabel}\n                  outerRadius={90}\n                  innerRadius={40}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                  strokeWidth={2}\n                >\n                  {caviData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip content={<CustomTooltip />} />\n                <Legend\n                  verticalAlign=\"bottom\"\n                  height={36}\n                  iconType=\"circle\"\n                  wrapperStyle={{ fontSize: '14px', fontWeight: 'bold' }}\n                />\n              </PieChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico a barre - Confronto Metriche */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3, height: 350, bgcolor: 'grey.50' }}>\n            <Typography variant=\"h6\" gutterBottom align=\"center\" sx={{ fontWeight: 600, mb: 2 }}>\n              Confronto Metriche\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <BarChart data={metricsData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#e0e0e0\" />\n                <XAxis\n                  dataKey=\"name\"\n                  tick={{ fontSize: 12, fontWeight: 'bold' }}\n                  axisLine={{ stroke: '#666' }}\n                />\n                <YAxis\n                  tick={{ fontSize: 12 }}\n                  axisLine={{ stroke: '#666' }}\n                />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend\n                  wrapperStyle={{ fontSize: '12px', fontWeight: 'bold' }}\n                />\n                <Bar dataKey=\"Totali\" fill={COLORS.primary} radius={[4, 4, 0, 0]} />\n                <Bar dataKey=\"Posati\" fill={COLORS.success} radius={[4, 4, 0, 0]} />\n                <Bar dataKey=\"Rimanenti\" fill={COLORS.warning} radius={[4, 4, 0, 0]} />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico temporale - Posa Recente (solo se ci sono dati) */}\n        {posaTrendData.length > 0 && (\n          <Grid item xs={12} md={6}>\n            <Paper sx={{ p: 3, height: 350, bgcolor: 'grey.50' }}>\n              <Typography variant=\"h6\" gutterBottom align=\"center\" sx={{ fontWeight: 600, mb: 2 }}>\n                Trend Posa Recente\n              </Typography>\n              <Typography variant=\"body2\" align=\"center\" sx={{ mb: 2, color: 'text.secondary' }}>\n                Media: {data.media_giornaliera}m/giorno\n              </Typography>\n              <ResponsiveContainer width=\"100%\" height={280}>\n                <LineChart data={posaTrendData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                  <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#e0e0e0\" />\n                  <XAxis\n                    dataKey=\"data\"\n                    tick={{ fontSize: 10 }}\n                    axisLine={{ stroke: '#666' }}\n                  />\n                  <YAxis\n                    tick={{ fontSize: 12 }}\n                    axisLine={{ stroke: '#666' }}\n                    label={{ value: 'Metri', angle: -90, position: 'insideLeft' }}\n                  />\n                  <Tooltip content={<CustomTooltip />} />\n                  <Legend wrapperStyle={{ fontSize: '12px', fontWeight: 'bold' }} />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"metri\"\n                    stroke={COLORS.primary}\n                    strokeWidth={3}\n                    dot={{ fill: COLORS.primary, strokeWidth: 2, r: 5 }}\n                    activeDot={{ r: 8, stroke: COLORS.primary, strokeWidth: 2 }}\n                  />\n                </LineChart>\n              </ResponsiveContainer>\n            </Paper>\n          </Grid>\n        )}\n      </Grid>\n    </Box>\n  );\n};\n\nexport default ProgressChart;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,SAAS,EACTC,IAAI,QACC,UAAU;AACjB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,MAAM,GAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAA,IAAAC,kBAAA;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA,MAAME,YAAY,GAAG,CACnB;IACEC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAEJ,IAAI,CAACK,YAAY;IACxBC,KAAK,EAAEjB,MAAM,CAACG;EAChB,CAAC,EACD;IACEW,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAEJ,IAAI,CAACO,eAAe;IAC3BD,KAAK,EAAEjB,MAAM,CAACI;EAChB,CAAC,CACF;;EAED;EACA,MAAMe,QAAQ,GAAG,CACf;IACEL,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAEJ,IAAI,CAACS,WAAW;IACvBH,KAAK,EAAEjB,MAAM,CAACG;EAChB,CAAC,EACD;IACEW,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAEJ,IAAI,CAACU,cAAc;IAC1BJ,KAAK,EAAEjB,MAAM,CAACI;EAChB,CAAC,CACF;;EAED;EACA,MAAMkB,WAAW,GAAG,CAClB;IACER,IAAI,EAAE,OAAO;IACbS,MAAM,EAAEZ,IAAI,CAACa,YAAY;IACzBC,MAAM,EAAEd,IAAI,CAACK,YAAY;IACzBU,SAAS,EAAEf,IAAI,CAACO;EAClB,CAAC,EACD;IACEJ,IAAI,EAAE,MAAM;IACZS,MAAM,EAAEZ,IAAI,CAACgB,WAAW;IACxBF,MAAM,EAAEd,IAAI,CAACS,WAAW;IACxBM,SAAS,EAAEf,IAAI,CAACU;EAClB,CAAC,CACF;;EAED;EACA,MAAMO,aAAa,GAAG,EAAAhB,kBAAA,GAAAD,IAAI,CAACkB,YAAY,cAAAjB,kBAAA,uBAAjBA,kBAAA,CAAmBkB,GAAG,CAACC,IAAI,KAAK;IACpDpB,IAAI,EAAEoB,IAAI,CAACpB,IAAI;IACfqB,KAAK,EAAED,IAAI,CAACC;EACd,CAAC,CAAC,CAAC,KAAI,EAAE;EAET,MAAMC,aAAa,GAAGA,CAAC;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAM,CAAC,KAAK;IACpD,IAAIF,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACE,MAAM,EAAE;MACvC,oBACEtC,OAAA,CAACF,KAAK;QAACyC,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAiB,CAAE;QAAAC,QAAA,gBAC5C1C,OAAA,CAACJ,UAAU;UAAC+C,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,GAAGL,KAAK;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,EACpDX,OAAO,CAACL,GAAG,CAAC,CAACiB,KAAK,EAAEC,KAAK,kBACxBjD,OAAA,CAACJ,UAAU;UAAa+C,OAAO,EAAC,OAAO;UAACO,KAAK,EAAE;YAAEhC,KAAK,EAAE8B,KAAK,CAAC9B;UAAM,CAAE;UAAAwB,QAAA,EACnE,GAAGM,KAAK,CAACjC,IAAI,KAAKiC,KAAK,CAAChC,KAAK;QAAE,GADjBiC,KAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMI,qBAAqB,GAAGA,CAAC;IAAEC,EAAE;IAAEC,EAAE;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,WAAW;IAAEC;EAAQ,CAAC,KAAK;IACzF,IAAIA,OAAO,GAAG,IAAI,EAAE,OAAO,IAAI,CAAC,CAAC;;IAEjC,MAAMC,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IAC5B,MAAMC,MAAM,GAAGN,WAAW,GAAG,CAACC,WAAW,GAAGD,WAAW,IAAI,GAAG;IAC9D,MAAMO,CAAC,GAAGV,EAAE,GAAGS,MAAM,GAAGF,IAAI,CAACI,GAAG,CAAC,CAACT,QAAQ,GAAGI,MAAM,CAAC;IACpD,MAAMM,CAAC,GAAGX,EAAE,GAAGQ,MAAM,GAAGF,IAAI,CAACM,GAAG,CAAC,CAACX,QAAQ,GAAGI,MAAM,CAAC;IAEpD,oBACE1D,OAAA;MACE8D,CAAC,EAAEA,CAAE;MACLE,CAAC,EAAEA,CAAE;MACLE,IAAI,EAAC,OAAO;MACZC,UAAU,EAAEL,CAAC,GAAGV,EAAE,GAAG,OAAO,GAAG,KAAM;MACrCgB,gBAAgB,EAAC,SAAS;MAC1BC,QAAQ,EAAC,IAAI;MACbC,UAAU,EAAC,MAAM;MAAA5B,QAAA,EAEhB,GAAG,CAACe,OAAO,GAAG,GAAG,EAAEc,OAAO,CAAC,CAAC,CAAC;IAAG;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEX,CAAC;EAED,oBACE/C,OAAA,CAACL,GAAG;IAAA+C,QAAA,gBACF1C,OAAA,CAACJ,UAAU;MAAC+C,OAAO,EAAC,IAAI;MAAC6B,YAAY;MAACjC,EAAE,EAAE;QAAE+B,UAAU,EAAE,GAAG;QAAEG,EAAE,EAAE;MAAE,CAAE;MAAA/B,QAAA,EAAC;IAEtE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb/C,OAAA,CAACH,IAAI;MAAC6E,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAjC,QAAA,gBAEzB1C,OAAA,CAACH,IAAI;QAAC+E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eACvB1C,OAAA,CAACF,KAAK;UAACyC,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEuC,MAAM,EAAE,GAAG;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAAtC,QAAA,gBACnD1C,OAAA,CAACJ,UAAU;YAAC+C,OAAO,EAAC,IAAI;YAAC6B,YAAY;YAACS,KAAK,EAAC,QAAQ;YAAC1C,EAAE,EAAE;cAAE+B,UAAU,EAAE,GAAG;cAAEG,EAAE,EAAE;YAAE,CAAE;YAAA/B,QAAA,EAAC;UAErF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAACJ,UAAU;YAAC+C,OAAO,EAAC,OAAO;YAACsC,KAAK,EAAC,QAAQ;YAAC1C,EAAE,EAAE;cAAEkC,EAAE,EAAE,CAAC;cAAEvD,KAAK,EAAE;YAAiB,CAAE;YAAAwB,QAAA,GAC/E9B,IAAI,CAACsE,uBAAuB,EAAC,cAChC;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAACR,mBAAmB;YAAC2F,KAAK,EAAC,MAAM;YAACJ,MAAM,EAAE,GAAI;YAAArC,QAAA,eAC5C1C,OAAA,CAAClB,QAAQ;cAAA4D,QAAA,gBACP1C,OAAA,CAACjB,GAAG;gBACF6B,IAAI,EAAEE,YAAa;gBACnBsC,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACR+B,SAAS,EAAE,KAAM;gBACjB/C,KAAK,EAAEc,qBAAsB;gBAC7BK,WAAW,EAAE,EAAG;gBAChBD,WAAW,EAAE,EAAG;gBAChBW,IAAI,EAAC,SAAS;gBACdmB,OAAO,EAAC,OAAO;gBACfC,WAAW,EAAE,CAAE;gBAAA5C,QAAA,EAEd5B,YAAY,CAACiB,GAAG,CAAC,CAACiB,KAAK,EAAEC,KAAK,kBAC7BjD,OAAA,CAAChB,IAAI;kBAAuBkF,IAAI,EAAElB,KAAK,CAAC9B;gBAAM,GAAnC,QAAQ+B,KAAK,EAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAsB,CACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN/C,OAAA,CAACV,OAAO;gBAACiG,OAAO,eAAEvF,OAAA,CAACkC,aAAa;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC/C,OAAA,CAACT,MAAM;gBACLiG,aAAa,EAAC,QAAQ;gBACtBT,MAAM,EAAE,EAAG;gBACXU,QAAQ,EAAC,QAAQ;gBACjBC,YAAY,EAAE;kBAAErB,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAO;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEP/C,OAAA,CAACH,IAAI;QAAC+E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eACvB1C,OAAA,CAACF,KAAK;UAACyC,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEuC,MAAM,EAAE,GAAG;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAAtC,QAAA,gBACnD1C,OAAA,CAACJ,UAAU;YAAC+C,OAAO,EAAC,IAAI;YAAC6B,YAAY;YAACS,KAAK,EAAC,QAAQ;YAAC1C,EAAE,EAAE;cAAE+B,UAAU,EAAE,GAAG;cAAEG,EAAE,EAAE;YAAE,CAAE;YAAA/B,QAAA,EAAC;UAErF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAACJ,UAAU;YAAC+C,OAAO,EAAC,OAAO;YAACsC,KAAK,EAAC,QAAQ;YAAC1C,EAAE,EAAE;cAAEkC,EAAE,EAAE,CAAC;cAAEvD,KAAK,EAAE;YAAiB,CAAE;YAAAwB,QAAA,GAC/E9B,IAAI,CAAC+E,gBAAgB,EAAC,cACzB;UAAA;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAACR,mBAAmB;YAAC2F,KAAK,EAAC,MAAM;YAACJ,MAAM,EAAE,GAAI;YAAArC,QAAA,eAC5C1C,OAAA,CAAClB,QAAQ;cAAA4D,QAAA,gBACP1C,OAAA,CAACjB,GAAG;gBACF6B,IAAI,EAAEQ,QAAS;gBACfgC,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACR+B,SAAS,EAAE,KAAM;gBACjB/C,KAAK,EAAEc,qBAAsB;gBAC7BK,WAAW,EAAE,EAAG;gBAChBD,WAAW,EAAE,EAAG;gBAChBW,IAAI,EAAC,SAAS;gBACdmB,OAAO,EAAC,OAAO;gBACfC,WAAW,EAAE,CAAE;gBAAA5C,QAAA,EAEdtB,QAAQ,CAACW,GAAG,CAAC,CAACiB,KAAK,EAAEC,KAAK,kBACzBjD,OAAA,CAAChB,IAAI;kBAAuBkF,IAAI,EAAElB,KAAK,CAAC9B;gBAAM,GAAnC,QAAQ+B,KAAK,EAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAsB,CACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN/C,OAAA,CAACV,OAAO;gBAACiG,OAAO,eAAEvF,OAAA,CAACkC,aAAa;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC/C,OAAA,CAACT,MAAM;gBACLiG,aAAa,EAAC,QAAQ;gBACtBT,MAAM,EAAE,EAAG;gBACXU,QAAQ,EAAC,QAAQ;gBACjBC,YAAY,EAAE;kBAAErB,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAO;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP/C,OAAA,CAACH,IAAI;QAAC+E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eACvB1C,OAAA,CAACF,KAAK;UAACyC,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEuC,MAAM,EAAE,GAAG;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAAtC,QAAA,gBACnD1C,OAAA,CAACJ,UAAU;YAAC+C,OAAO,EAAC,IAAI;YAAC6B,YAAY;YAACS,KAAK,EAAC,QAAQ;YAAC1C,EAAE,EAAE;cAAE+B,UAAU,EAAE,GAAG;cAAEG,EAAE,EAAE;YAAE,CAAE;YAAA/B,QAAA,EAAC;UAErF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAACR,mBAAmB;YAAC2F,KAAK,EAAC,MAAM;YAACJ,MAAM,EAAE,GAAI;YAAArC,QAAA,eAC5C1C,OAAA,CAACf,QAAQ;cAAC2B,IAAI,EAAEW,WAAY;cAACqE,MAAM,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAAtD,QAAA,gBAC/E1C,OAAA,CAACX,aAAa;gBAAC4G,eAAe,EAAC,KAAK;gBAACC,MAAM,EAAC;cAAS;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxD/C,OAAA,CAACb,KAAK;gBACJkG,OAAO,EAAC,MAAM;gBACdc,IAAI,EAAE;kBAAE9B,QAAQ,EAAE,EAAE;kBAAEC,UAAU,EAAE;gBAAO,CAAE;gBAC3C8B,QAAQ,EAAE;kBAAEF,MAAM,EAAE;gBAAO;cAAE;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACF/C,OAAA,CAACZ,KAAK;gBACJ+G,IAAI,EAAE;kBAAE9B,QAAQ,EAAE;gBAAG,CAAE;gBACvB+B,QAAQ,EAAE;kBAAEF,MAAM,EAAE;gBAAO;cAAE;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACF/C,OAAA,CAACV,OAAO;gBAACiG,OAAO,eAAEvF,OAAA,CAACkC,aAAa;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC/C,OAAA,CAACT,MAAM;gBACLmG,YAAY,EAAE;kBAAErB,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAO;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACF/C,OAAA,CAACd,GAAG;gBAACmG,OAAO,EAAC,QAAQ;gBAACnB,IAAI,EAAEjE,MAAM,CAACC,OAAQ;gBAAC2D,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;cAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpE/C,OAAA,CAACd,GAAG;gBAACmG,OAAO,EAAC,QAAQ;gBAACnB,IAAI,EAAEjE,MAAM,CAACG,OAAQ;gBAACyD,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;cAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpE/C,OAAA,CAACd,GAAG;gBAACmG,OAAO,EAAC,WAAW;gBAACnB,IAAI,EAAEjE,MAAM,CAACI,OAAQ;gBAACwD,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;cAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAGNlB,aAAa,CAACS,MAAM,GAAG,CAAC,iBACvBtC,OAAA,CAACH,IAAI;QAAC+E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eACvB1C,OAAA,CAACF,KAAK;UAACyC,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEuC,MAAM,EAAE,GAAG;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAAtC,QAAA,gBACnD1C,OAAA,CAACJ,UAAU;YAAC+C,OAAO,EAAC,IAAI;YAAC6B,YAAY;YAACS,KAAK,EAAC,QAAQ;YAAC1C,EAAE,EAAE;cAAE+B,UAAU,EAAE,GAAG;cAAEG,EAAE,EAAE;YAAE,CAAE;YAAA/B,QAAA,EAAC;UAErF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAACJ,UAAU;YAAC+C,OAAO,EAAC,OAAO;YAACsC,KAAK,EAAC,QAAQ;YAAC1C,EAAE,EAAE;cAAEkC,EAAE,EAAE,CAAC;cAAEvD,KAAK,EAAE;YAAiB,CAAE;YAAAwB,QAAA,GAAC,SAC1E,EAAC9B,IAAI,CAACyF,iBAAiB,EAAC,UACjC;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/C,OAAA,CAACR,mBAAmB;YAAC2F,KAAK,EAAC,MAAM;YAACJ,MAAM,EAAE,GAAI;YAAArC,QAAA,eAC5C1C,OAAA,CAACP,SAAS;cAACmB,IAAI,EAAEiB,aAAc;cAAC+D,MAAM,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAAtD,QAAA,gBAClF1C,OAAA,CAACX,aAAa;gBAAC4G,eAAe,EAAC,KAAK;gBAACC,MAAM,EAAC;cAAS;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxD/C,OAAA,CAACb,KAAK;gBACJkG,OAAO,EAAC,MAAM;gBACdc,IAAI,EAAE;kBAAE9B,QAAQ,EAAE;gBAAG,CAAE;gBACvB+B,QAAQ,EAAE;kBAAEF,MAAM,EAAE;gBAAO;cAAE;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACF/C,OAAA,CAACZ,KAAK;gBACJ+G,IAAI,EAAE;kBAAE9B,QAAQ,EAAE;gBAAG,CAAE;gBACvB+B,QAAQ,EAAE;kBAAEF,MAAM,EAAE;gBAAO,CAAE;gBAC7B7D,KAAK,EAAE;kBAAErB,KAAK,EAAE,OAAO;kBAAEsF,KAAK,EAAE,CAAC,EAAE;kBAAEC,QAAQ,EAAE;gBAAa;cAAE;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACF/C,OAAA,CAACV,OAAO;gBAACiG,OAAO,eAAEvF,OAAA,CAACkC,aAAa;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC/C,OAAA,CAACT,MAAM;gBAACmG,YAAY,EAAE;kBAAErB,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAO;cAAE;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClE/C,OAAA,CAACN,IAAI;gBACH8G,IAAI,EAAC,UAAU;gBACfnB,OAAO,EAAC,OAAO;gBACfa,MAAM,EAAEjG,MAAM,CAACC,OAAQ;gBACvBoF,WAAW,EAAE,CAAE;gBACfmB,GAAG,EAAE;kBAAEvC,IAAI,EAAEjE,MAAM,CAACC,OAAO;kBAAEoF,WAAW,EAAE,CAAC;kBAAEoB,CAAC,EAAE;gBAAE,CAAE;gBACpDC,SAAS,EAAE;kBAAED,CAAC,EAAE,CAAC;kBAAER,MAAM,EAAEjG,MAAM,CAACC,OAAO;kBAAEoF,WAAW,EAAE;gBAAE;cAAE;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC6D,EAAA,GAvPIjG,aAAa;AAyPnB,eAAeA,aAAa;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}