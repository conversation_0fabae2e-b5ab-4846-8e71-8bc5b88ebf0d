{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\parco\\\\CreaBobinaPage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, Typography, IconButton, Alert, Snackbar } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport ParcoCavi from '../../../components/cavi/ParcoCavi';\nimport { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreaBobinaPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    selectedCantiere\n  } = useAuth();\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [openSuccess, setOpenSuccess] = useState(false);\n  const [openError, setOpenError] = useState(false);\n\n  // Verifica se un cantiere è selezionato\n  if (!selectedCantiere) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"Nessun cantiere selezionato. Seleziona un cantiere per gestire il parco cavi.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this);\n  }\n  const cantiereId = selectedCantiere.id_cantiere;\n\n  // Gestisce il ritorno alla pagina dei cavi\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cavi');\n  };\n\n  // Gestisce i messaggi di successo\n  const handleSuccess = message => {\n    setSuccessMessage(message);\n    setOpenSuccess(true);\n  };\n\n  // Gestisce i messaggi di errore\n  const handleError = message => {\n    setErrorMessage(message);\n    setOpenError(true);\n  };\n\n  // Chiude i messaggi\n  const handleCloseSuccess = () => {\n    setOpenSuccess(false);\n  };\n  const handleCloseError = () => {\n    setOpenError(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToCantieri,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          children: \"Crea Nuova Bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ParcoCavi, {\n      cantiereId: cantiereId,\n      onSuccess: handleSuccess,\n      onError: handleError,\n      initialOption: \"creaBobina\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openSuccess,\n      autoHideDuration: 6000,\n      onClose: handleCloseSuccess,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSuccess,\n        severity: \"success\",\n        sx: {\n          width: '100%'\n        },\n        children: successMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openError,\n      autoHideDuration: 6000,\n      onClose: handleCloseError,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseError,\n        severity: \"error\",\n        sx: {\n          width: '100%'\n        },\n        children: errorMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_s(CreaBobinaPage, \"alPhJDWny/l2XY6014qd2C0Vy3U=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = CreaBobinaPage;\nexport default CreaBobinaPage;\nvar _c;\n$RefreshReg$(_c, \"CreaBobinaPage\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "IconButton", "<PERSON><PERSON>", "Snackbar", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "useNavigate", "useAuth", "AdminHomeButton", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useState", "jsxDEV", "_jsxDEV", "CreaBobinaPage", "_s", "navigate", "selected<PERSON><PERSON><PERSON>", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "openSuccess", "setOpenSuccess", "openError", "set<PERSON>pen<PERSON>rror", "sx", "p", "children", "severity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "cantiereId", "id_cantiere", "handleBackToCantieri", "handleSuccess", "message", "handleError", "handleCloseSuccess", "handleCloseError", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "variant", "window", "location", "reload", "ml", "color", "title", "onSuccess", "onError", "initialOption", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/parco/CreaBobinaPage.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n  Alert,\n  Snackbar\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport ParcoCavi from '../../../components/cavi/ParcoCavi';\nimport { useState } from 'react';\n\nconst CreaBobinaPage = () => {\n  const navigate = useNavigate();\n  const { selectedCantiere } = useAuth();\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [openSuccess, setOpenSuccess] = useState(false);\n  const [openError, setOpenError] = useState(false);\n\n  // Verifica se un cantiere è selezionato\n  if (!selectedCantiere) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Alert severity=\"warning\">\n          Nessun cantiere selezionato. Seleziona un cantiere per gestire il parco cavi.\n        </Alert>\n      </Box>\n    );\n  }\n\n  const cantiereId = selectedCantiere.id_cantiere;\n\n  // Gestisce il ritorno alla pagina dei cavi\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cavi');\n  };\n\n  // Gestisce i messaggi di successo\n  const handleSuccess = (message) => {\n    setSuccessMessage(message);\n    setOpenSuccess(true);\n  };\n\n  // Gestisce i messaggi di errore\n  const handleError = (message) => {\n    setErrorMessage(message);\n    setOpenError(true);\n  };\n\n  // Chiude i messaggi\n  const handleCloseSuccess = () => {\n    setOpenSuccess(false);\n  };\n\n  const handleCloseError = () => {\n    setOpenError(false);\n  };\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Crea Nuova Bobina\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      <ParcoCavi\n        cantiereId={cantiereId}\n        onSuccess={handleSuccess}\n        onError={handleError}\n        initialOption=\"creaBobina\"\n      />\n\n      {/* Snackbar per i messaggi di successo */}\n      <Snackbar\n        open={openSuccess}\n        autoHideDuration={6000}\n        onClose={handleCloseSuccess}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSuccess} severity=\"success\" sx={{ width: '100%' }}>\n          {successMessage}\n        </Alert>\n      </Snackbar>\n\n      {/* Snackbar per i messaggi di errore */}\n      <Snackbar\n        open={openError}\n        autoHideDuration={6000}\n        onClose={handleCloseError}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseError} severity=\"error\" sx={{ width: '100%' }}>\n          {errorMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default CreaBobinaPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,KAAK,EACLC,QAAQ,QACH,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,SAASC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU;EAAiB,CAAC,GAAGT,OAAO,CAAC,CAAC;EACtC,MAAM,CAACU,cAAc,EAAEC,iBAAiB,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,IAAI,CAACM,gBAAgB,EAAE;IACrB,oBACEJ,OAAA,CAACf,GAAG;MAAC4B,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,eAChBf,OAAA,CAACZ,KAAK;QAAC4B,QAAQ,EAAC,SAAS;QAAAD,QAAA,EAAC;MAE1B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;EAEA,MAAMC,UAAU,GAAGjB,gBAAgB,CAACkB,WAAW;;EAE/C;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCpB,QAAQ,CAAC,iBAAiB,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMqB,aAAa,GAAIC,OAAO,IAAK;IACjCnB,iBAAiB,CAACmB,OAAO,CAAC;IAC1Bf,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMgB,WAAW,GAAID,OAAO,IAAK;IAC/BjB,eAAe,CAACiB,OAAO,CAAC;IACxBb,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMe,kBAAkB,GAAGA,CAAA,KAAM;IAC/BjB,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMkB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhB,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,oBACEZ,OAAA,CAACf,GAAG;IAAA8B,QAAA,gBACFf,OAAA,CAACf,GAAG;MAAC4B,EAAE,EAAE;QAAEgB,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAjB,QAAA,gBACzFf,OAAA,CAACf,GAAG;QAAC4B,EAAE,EAAE;UAAEiB,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAhB,QAAA,gBACjDf,OAAA,CAACb,UAAU;UAAC8C,OAAO,EAAEV,oBAAqB;UAACV,EAAE,EAAE;YAAEqB,EAAE,EAAE;UAAE,CAAE;UAAAnB,QAAA,eACvDf,OAAA,CAACT,aAAa;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACbpB,OAAA,CAACd,UAAU;UAACiD,OAAO,EAAC,IAAI;UAAApB,QAAA,EAAC;QAEzB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpB,OAAA,CAACb,UAAU;UACT8C,OAAO,EAAEA,CAAA,KAAMG,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCzB,EAAE,EAAE;YAAE0B,EAAE,EAAE;UAAE,CAAE;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAC,oBAAoB;UAAA1B,QAAA,eAE1Bf,OAAA,CAACP,WAAW;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNpB,OAAA,CAACJ,eAAe;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAENpB,OAAA,CAACH,SAAS;MACRwB,UAAU,EAAEA,UAAW;MACvBqB,SAAS,EAAElB,aAAc;MACzBmB,OAAO,EAAEjB,WAAY;MACrBkB,aAAa,EAAC;IAAY;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAGFpB,OAAA,CAACX,QAAQ;MACPwD,IAAI,EAAEpC,WAAY;MAClBqC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEpB,kBAAmB;MAC5BqB,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAnC,QAAA,eAE3Df,OAAA,CAACZ,KAAK;QAAC2D,OAAO,EAAEpB,kBAAmB;QAACX,QAAQ,EAAC,SAAS;QAACH,EAAE,EAAE;UAAEsC,KAAK,EAAE;QAAO,CAAE;QAAApC,QAAA,EAC1EV;MAAc;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGXpB,OAAA,CAACX,QAAQ;MACPwD,IAAI,EAAElC,SAAU;MAChBmC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEnB,gBAAiB;MAC1BoB,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAnC,QAAA,eAE3Df,OAAA,CAACZ,KAAK;QAAC2D,OAAO,EAAEnB,gBAAiB;QAACZ,QAAQ,EAAC,OAAO;QAACH,EAAE,EAAE;UAAEsC,KAAK,EAAE;QAAO,CAAE;QAAApC,QAAA,EACtER;MAAY;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAClB,EAAA,CArGID,cAAc;EAAA,QACDP,WAAW,EACCC,OAAO;AAAA;AAAAyD,EAAA,GAFhCnD,cAAc;AAuGpB,eAAeA,cAAc;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}