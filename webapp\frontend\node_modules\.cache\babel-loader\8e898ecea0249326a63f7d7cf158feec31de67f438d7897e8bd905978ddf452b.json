{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\common\\\\RedirectToCaviVisualizza.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport { useAuth } from '../../context/AuthContext';\n\n// Componente che reindirizza alla pagina di visualizzazione cavi\n// e opzionalmente apre il dialog di modifica cavo\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RedirectToCaviVisualizza = ({\n  openModificaDialog = false\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    setOpenModificaCavoDialog\n  } = useGlobalContext();\n  const {\n    user\n  } = useAuth();\n\n  // Verifica che il cantiereId sia presente nel localStorage\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  console.log('RedirectToCaviVisualizza - cantiereId dal localStorage:', cantiereId);\n\n  // Se non c'è un cantiereId nel localStorage e l'utente è un utente cantiere,\n  // prova a recuperare l'ID del cantiere dai dati utente\n  if (!cantiereId && (user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user' && user !== null && user !== void 0 && user.cantiere_id) {\n    console.log('RedirectToCaviVisualizza - cantiereId dai dati utente:', user.cantiere_id);\n    localStorage.setItem('selectedCantiereId', user.cantiere_id.toString());\n    localStorage.setItem('selectedCantiereName', user.cantiere_name || `Cantiere ${user.cantiere_id}`);\n  }\n  useEffect(() => {\n    // Prima reindirizza alla pagina di visualizzazione cavi\n    navigate('/dashboard/cavi/visualizza');\n\n    // Se richiesto, apre il dialog di modifica cavo dopo un breve ritardo\n    // per assicurarsi che la navigazione sia completata\n    if (openModificaDialog) {\n      setTimeout(() => {\n        setOpenModificaCavoDialog(true);\n      }, 500);\n    }\n  }, [navigate, setOpenModificaCavoDialog, openModificaDialog]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      height: '100vh'\n    },\n    children: \"Reindirizzamento in corso...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(RedirectToCaviVisualizza, \"4hXdUpsaPQV4vbtVDrqSdmV7fPg=\", false, function () {\n  return [useNavigate, useGlobalContext, useAuth];\n});\n_c = RedirectToCaviVisualizza;\nexport default RedirectToCaviVisualizza;\nvar _c;\n$RefreshReg$(_c, \"RedirectToCaviVisualizza\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useGlobalContext", "useAuth", "jsxDEV", "_jsxDEV", "RedirectToCaviVisualizza", "openModificaDialog", "_s", "navigate", "setOpenModificaCavoDialog", "user", "cantiereId", "localStorage", "getItem", "console", "log", "role", "cantiere_id", "setItem", "toString", "cantiere_name", "setTimeout", "style", "display", "justifyContent", "alignItems", "height", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/common/RedirectToCaviVisualizza.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport { useAuth } from '../../context/AuthContext';\n\n// Componente che reindirizza alla pagina di visualizzazione cavi\n// e opzionalmente apre il dialog di modifica cavo\nconst RedirectToCaviVisualizza = ({ openModificaDialog = false }) => {\n  const navigate = useNavigate();\n  const { setOpenModificaCavoDialog } = useGlobalContext();\n  const { user } = useAuth();\n\n  // Verifica che il cantiereId sia presente nel localStorage\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  console.log('RedirectToCaviVisualizza - cantiereId dal localStorage:', cantiereId);\n\n  // Se non c'è un cantiereId nel localStorage e l'utente è un utente cantiere,\n  // prova a recuperare l'ID del cantiere dai dati utente\n  if (!cantiereId && user?.role === 'cantieri_user' && user?.cantiere_id) {\n    console.log('RedirectToCaviVisualizza - cantiereId dai dati utente:', user.cantiere_id);\n    localStorage.setItem('selectedCantiereId', user.cantiere_id.toString());\n    localStorage.setItem('selectedCantiereName', user.cantiere_name || `Cantiere ${user.cantiere_id}`);\n  }\n\n  useEffect(() => {\n    // Prima reindirizza alla pagina di visualizzazione cavi\n    navigate('/dashboard/cavi/visualizza');\n\n    // Se richiesto, apre il dialog di modifica cavo dopo un breve ritardo\n    // per assicurarsi che la navigazione sia completata\n    if (openModificaDialog) {\n      setTimeout(() => {\n        setOpenModificaCavoDialog(true);\n      }, 500);\n    }\n  }, [navigate, setOpenModificaCavoDialog, openModificaDialog]);\n\n  return (\n    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\n      Reindirizzamento in corso...\n    </div>\n  );\n};\n\nexport default RedirectToCaviVisualizza;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,OAAO,QAAQ,2BAA2B;;AAEnD;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,kBAAkB,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACnE,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES;EAA0B,CAAC,GAAGR,gBAAgB,CAAC,CAAC;EACxD,MAAM;IAAES;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;;EAE1B;EACA,MAAMS,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EAC7DC,OAAO,CAACC,GAAG,CAAC,yDAAyD,EAAEJ,UAAU,CAAC;;EAElF;EACA;EACA,IAAI,CAACA,UAAU,IAAI,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,IAAI,MAAK,eAAe,IAAIN,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEO,WAAW,EAAE;IACtEH,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEL,IAAI,CAACO,WAAW,CAAC;IACvFL,YAAY,CAACM,OAAO,CAAC,oBAAoB,EAAER,IAAI,CAACO,WAAW,CAACE,QAAQ,CAAC,CAAC,CAAC;IACvEP,YAAY,CAACM,OAAO,CAAC,sBAAsB,EAAER,IAAI,CAACU,aAAa,IAAI,YAAYV,IAAI,CAACO,WAAW,EAAE,CAAC;EACpG;EAEAlB,SAAS,CAAC,MAAM;IACd;IACAS,QAAQ,CAAC,4BAA4B,CAAC;;IAEtC;IACA;IACA,IAAIF,kBAAkB,EAAE;MACtBe,UAAU,CAAC,MAAM;QACfZ,yBAAyB,CAAC,IAAI,CAAC;MACjC,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EAAE,CAACD,QAAQ,EAAEC,yBAAyB,EAAEH,kBAAkB,CAAC,CAAC;EAE7D,oBACEF,OAAA;IAAKkB,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,cAAc,EAAE,QAAQ;MAAEC,UAAU,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAQ,CAAE;IAAAC,QAAA,EAAC;EAElG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAEV,CAAC;AAACxB,EAAA,CAnCIF,wBAAwB;EAAA,QACXL,WAAW,EACUC,gBAAgB,EACrCC,OAAO;AAAA;AAAA8B,EAAA,GAHpB3B,wBAAwB;AAqC9B,eAAeA,wBAAwB;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}