{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Grid, Card, CardContent, CardActions, Button, Chip, Alert, CircularProgress, Divider, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails, Switch, FormControlLabel } from '@mui/material';\nimport { Assessment as AssessmentIcon, BarChart as BarChartIcon, PieChart as PieChartIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon, ArrowBack as ArrowBackIcon, DateRange as DateRangeIcon, Cable as CableIcon, Inventory as InventoryIcon, ExpandMore as ExpandMoreIcon, ShowChart as ShowChartIcon } from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    cantiereId\n  } = useParams();\n  const {\n    user\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading progress report:', err);\n          return {\n            content: null\n          };\n        });\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video').catch(err => {\n          console.error('Error loading BOQ report:', err);\n          return {\n            content: null\n          };\n        });\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading bobine report:', err);\n          return {\n            content: null\n          };\n        });\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading cavi stato report:', err);\n          return {\n            content: null\n          };\n        });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([progressPromise, boqPromise, bobinePromise, caviStatoPromise]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [{\n    id: 'progress',\n    title: 'Report Avanzamento',\n    description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n    icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 13\n    }, this),\n    color: 'primary',\n    features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n  }, {\n    id: 'boq',\n    title: 'Bill of Quantities',\n    description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n    icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 13\n    }, this),\n    color: 'secondary',\n    features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n  }, {\n    id: 'bobine',\n    title: 'Report Utilizzo Bobine',\n    description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n    icon: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 13\n    }, this),\n    color: 'success',\n    features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n  }, {\n    id: 'bobina-specifica',\n    title: 'Report Bobina Specifica',\n    description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n    icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 13\n    }, this),\n    color: 'info',\n    features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n  }, {\n    id: 'posa-periodo',\n    title: 'Report Posa per Periodo',\n    description: 'Analisi temporale della posa con trend e pattern di lavoro',\n    icon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 13\n    }, this),\n    color: 'warning',\n    features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n  }, {\n    id: 'cavi-stato',\n    title: 'Report Cavi per Stato',\n    description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n    icon: /*#__PURE__*/_jsxDEV(BarChartIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 13\n    }, this),\n    color: 'error',\n    features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n  }];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReportSelect = reportType => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderReportContent = () => {\n    if (!reportData) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title, \" - \", reportData.nome_cantiere]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'pdf'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"primary\",\n            children: \"PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'excel'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"success\",\n            children: \"Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 26\n            }, this),\n            onClick: () => setReportData(null),\n            variant: \"outlined\",\n            size: \"small\",\n            children: \"Nuovo Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), dialogType === 'progress' && renderProgressReport(reportData), dialogType === 'boq' && renderBoqReport(reportData), dialogType === 'bobine' && renderBobineReport(reportData), dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData), dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData), dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this);\n  };\n  const renderProgressReport = data => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          control: /*#__PURE__*/_jsxDEV(Switch, {\n            checked: showCharts,\n            onChange: e => setShowCharts(e.target.checked),\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 15\n          }, this),\n          label: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), \"Mostra Grafici\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(ProgressChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Avanzamento Generale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: [{\n              metrica: 'Metri Totali',\n              valore: `${data.metri_totali}m`,\n              percentuale: '100%'\n            }, {\n              metrica: 'Metri Posati',\n              valore: `${data.metri_posati}m`,\n              percentuale: `${data.percentuale_avanzamento}%`\n            }, {\n              metrica: 'Metri Rimanenti',\n              valore: `${data.metri_da_posare}m`,\n              percentuale: `${100 - data.percentuale_avanzamento}%`\n            }],\n            columns: [{\n              field: 'metrica',\n              headerName: 'Metrica',\n              width: 200\n            }, {\n              field: 'valore',\n              headerName: 'Valore',\n              width: 150,\n              align: 'right'\n            }, {\n              field: 'percentuale',\n              headerName: 'Percentuale',\n              width: 150,\n              align: 'right'\n            }],\n            pagination: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: [{\n              metrica: 'Totale Cavi',\n              valore: data.totale_cavi,\n              percentuale: '100%'\n            }, {\n              metrica: 'Cavi Posati',\n              valore: data.cavi_posati,\n              percentuale: `${data.percentuale_cavi}%`\n            }, {\n              metrica: 'Cavi Rimanenti',\n              valore: data.cavi_rimanenti,\n              percentuale: `${100 - data.percentuale_cavi}%`\n            }],\n            columns: [{\n              field: 'metrica',\n              headerName: 'Metrica',\n              width: 200\n            }, {\n              field: 'valore',\n              headerName: 'Valore',\n              width: 150,\n              align: 'right'\n            }, {\n              field: 'percentuale',\n              headerName: 'Percentuale',\n              width: 150,\n              align: 'right'\n            }],\n            pagination: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: [{\n              metrica: 'Media Giornaliera',\n              valore: `${data.media_giornaliera}m/giorno`\n            }, ...(data.giorni_stimati ? [{\n              metrica: 'Giorni Stimati',\n              valore: `${data.giorni_stimati} giorni`\n            }, {\n              metrica: 'Data Completamento',\n              valore: data.data_completamento\n            }] : [])],\n            columns: [{\n              field: 'metrica',\n              headerName: 'Metrica',\n              width: 200\n            }, {\n              field: 'valore',\n              headerName: 'Valore',\n              width: 250,\n              align: 'right'\n            }],\n            pagination: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 7\n    }, this), data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 43\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Posa Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.posa_recente.map(posa => ({\n              data: posa.data,\n              metri: `${posa.metri}m`\n            })),\n            columns: [{\n              field: 'data',\n              headerName: 'Data',\n              width: 200\n            }, {\n              field: 'metri',\n              headerName: 'Metri Posati',\n              width: 150,\n              align: 'right'\n            }],\n            pagination: data.posa_recente.length > 10\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 375,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = data => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [showCharts && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(BoqChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavi per Tipologia\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.cavi_per_tipo || [],\n            columns: [{\n              field: 'tipologia',\n              headerName: 'Tipologia',\n              width: 150\n            }, {\n              field: 'sezione',\n              headerName: 'Sezione',\n              width: 100\n            }, {\n              field: 'num_cavi',\n              headerName: 'Cavi',\n              width: 80,\n              align: 'right',\n              dataType: 'number'\n            }, {\n              field: 'metri_teorici',\n              headerName: 'Metri Teorici',\n              width: 120,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_teorici}m`\n            }, {\n              field: 'metri_reali',\n              headerName: 'Metri Reali',\n              width: 120,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_reali}m`\n            }, {\n              field: 'metri_da_posare',\n              headerName: 'Da Posare',\n              width: 120,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_da_posare}m`\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Bobine Disponibili\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.bobine_per_tipo || [],\n            columns: [{\n              field: 'tipologia',\n              headerName: 'Tipologia',\n              width: 150\n            }, {\n              field: 'sezione',\n              headerName: 'Sezione',\n              width: 100\n            }, {\n              field: 'num_bobine',\n              headerName: 'Bobine',\n              width: 100,\n              align: 'right',\n              dataType: 'number'\n            }, {\n              field: 'metri_disponibili',\n              headerName: 'Metri Disponibili',\n              width: 150,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_disponibili}m`\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 537,\n    columnNumber: 5\n  }, this);\n  const renderBobineReport = data => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [showCharts && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(BobineChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 598,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 597,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [\"Bobine del Cantiere (\", data.totale_bobine, \" totali)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.bobine || [],\n            columns: [{\n              field: 'id_bobina',\n              headerName: 'ID Bobina',\n              width: 120\n            }, {\n              field: 'tipologia',\n              headerName: 'Tipologia',\n              width: 150\n            }, {\n              field: 'sezione',\n              headerName: 'Sezione',\n              width: 100\n            }, {\n              field: 'stato',\n              headerName: 'Stato',\n              width: 120,\n              renderCell: row => /*#__PURE__*/_jsxDEV(Chip, {\n                label: row.stato,\n                color: row.stato === 'DISPONIBILE' ? 'success' : 'warning',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 21\n              }, this)\n            }, {\n              field: 'metri_totali',\n              headerName: 'Metri Totali',\n              width: 120,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_totali}m`\n            }, {\n              field: 'metri_residui',\n              headerName: 'Metri Residui',\n              width: 120,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_residui}m`\n            }, {\n              field: 'metri_utilizzati',\n              headerName: 'Metri Utilizzati',\n              width: 140,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_utilizzati}m`\n            }, {\n              field: 'percentuale_utilizzo',\n              headerName: 'Utilizzo',\n              width: 100,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.percentuale_utilizzo}%`\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 604,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 603,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 594,\n    columnNumber: 5\n  }, this);\n  const renderBobinaSpecificaReport = data => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Dettagli Bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 647,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.bobina ? [{\n              proprietà: 'ID Bobina',\n              valore: data.bobina.id_bobina\n            }, {\n              proprietà: 'Tipologia',\n              valore: data.bobina.tipologia\n            }, {\n              proprietà: 'Sezione',\n              valore: data.bobina.sezione\n            }, {\n              proprietà: 'Stato',\n              valore: data.bobina.stato,\n              renderSpecial: true\n            }, {\n              proprietà: 'Metri Totali',\n              valore: `${data.bobina.metri_totali}m`\n            }, {\n              proprietà: 'Metri Residui',\n              valore: `${data.bobina.metri_residui}m`\n            }, {\n              proprietà: 'Metri Utilizzati',\n              valore: `${data.bobina.metri_utilizzati}m`\n            }, {\n              proprietà: 'Percentuale Utilizzo',\n              valore: `${data.bobina.percentuale_utilizzo}%`\n            }] : [],\n            columns: [{\n              field: 'proprietà',\n              headerName: 'Proprietà',\n              width: 200\n            }, {\n              field: 'valore',\n              headerName: 'Valore',\n              width: 250,\n              renderCell: row => row.renderSpecial ? /*#__PURE__*/_jsxDEV(Chip, {\n                label: row.valore,\n                color: row.valore === 'DISPONIBILE' ? 'success' : 'warning',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 21\n              }, this) : row.valore\n            }],\n            pagination: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 645,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [\"Cavi Associati (\", data.totale_cavi, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 708,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.cavi_associati || [],\n            columns: [{\n              field: 'id_cavo',\n              headerName: 'ID Cavo',\n              width: 120\n            }, {\n              field: 'sistema',\n              headerName: 'Sistema',\n              width: 120\n            }, {\n              field: 'utility',\n              headerName: 'Utility',\n              width: 120\n            }, {\n              field: 'tipologia',\n              headerName: 'Tipologia',\n              width: 150\n            }, {\n              field: 'metri_teorici',\n              headerName: 'Metri Teorici',\n              width: 120,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_teorici}m`\n            }, {\n              field: 'metri_reali',\n              headerName: 'Metri Reali',\n              width: 120,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_reali}m`\n            }, {\n              field: 'stato',\n              headerName: 'Stato',\n              width: 120,\n              renderCell: row => /*#__PURE__*/_jsxDEV(Chip, {\n                label: row.stato,\n                color: row.stato === 'POSATO' ? 'success' : 'warning',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 21\n              }, this)\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 707,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 706,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 643,\n    columnNumber: 5\n  }, this);\n  const renderPosaPeriodoReport = data => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [showCharts && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(TimelineChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 747,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 746,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Statistiche Periodo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 754,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: [{\n              statistica: 'Periodo',\n              valore: `${data.data_inizio} - ${data.data_fine}`\n            }, {\n              statistica: 'Totale Metri',\n              valore: `${data.totale_metri_periodo}m`\n            }, {\n              statistica: 'Giorni Attivi',\n              valore: data.giorni_attivi\n            }, {\n              statistica: 'Media Giornaliera',\n              valore: `${data.media_giornaliera}m/giorno`\n            }],\n            columns: [{\n              field: 'statistica',\n              headerName: 'Statistica',\n              width: 200\n            }, {\n              field: 'valore',\n              headerName: 'Valore',\n              width: 250,\n              align: 'right'\n            }],\n            pagination: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 758,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 757,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Posa Giornaliera\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 790,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.posa_giornaliera || [],\n            columns: [{\n              field: 'data',\n              headerName: 'Data',\n              width: 200\n            }, {\n              field: 'metri',\n              headerName: 'Metri Posati',\n              width: 150,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri}m`\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 794,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 793,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 789,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 788,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 743,\n    columnNumber: 5\n  }, this);\n  const renderCaviStatoReport = data => {\n    var _data$cavi_per_stato;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [showCharts && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(CaviStatoChart, {\n          data: data\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 813,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 812,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Cavi per Stato di Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: (_data$cavi_per_stato = data.cavi_per_stato) === null || _data$cavi_per_stato === void 0 ? void 0 : _data$cavi_per_stato.map((stato, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                lg: 3,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      gutterBottom: true,\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        label: stato.stato,\n                        color: stato.stato === 'Installato' ? 'success' : 'warning',\n                        sx: {\n                          mb: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 829,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 828,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Numero Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: stato.num_cavi\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 835,\n                        columnNumber: 48\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 835,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Teorici: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [stato.metri_teorici, \"m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 836,\n                        columnNumber: 50\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 836,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Reali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [stato.metri_reali, \"m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 837,\n                        columnNumber: 48\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 837,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 827,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 822,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 817,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 809,\n      columnNumber: 5\n    }, this);\n  };\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 851,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 856,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 871,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 865,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 862,\n          columnNumber: 11\n        }, this), dialogType === 'bobina-specifica' && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Bobina\",\n            value: formData.id_bobina,\n            onChange: e => setFormData({\n              ...formData,\n              id_bobina: e.target.value\n            }),\n            placeholder: \"Es: 1, 2, A, B...\",\n            helperText: \"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 878,\n          columnNumber: 13\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 892,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 903,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 902,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 854,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 917,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 922,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 922,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 918,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 916,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 850,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => navigate(-1),\n          color: \"primary\",\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 935,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Report e Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 938,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 934,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 942,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 933,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 948,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 947,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 956,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                mr: 1,\n                color: 'primary.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Avanzamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 956,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.progress ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                control: /*#__PURE__*/_jsxDEV(Switch, {\n                  checked: showCharts,\n                  onChange: e => setShowCharts(e.target.checked),\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 968,\n                  columnNumber: 23\n                }, this),\n                label: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n                    sx: {\n                      mr: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 976,\n                    columnNumber: 25\n                  }, this), \"Mostra Grafici\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 975,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 966,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 983,\n                    columnNumber: 34\n                  }, this),\n                  onClick: () => generateReportWithFormat('progress', 'pdf'),\n                  variant: \"outlined\",\n                  size: \"small\",\n                  color: \"primary\",\n                  sx: {\n                    mr: 1\n                  },\n                  children: \"PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 982,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 993,\n                    columnNumber: 34\n                  }, this),\n                  onClick: () => generateReportWithFormat('progress', 'excel'),\n                  variant: \"outlined\",\n                  size: \"small\",\n                  color: \"success\",\n                  children: \"Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 992,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 17\n            }, this), renderProgressReport(reportsData.progress)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1007,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1008,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1006,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1012,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1018,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getProgressReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    progress: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying progress report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1015,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1011,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 962,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 955,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1045,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n              sx: {\n                mr: 1,\n                color: 'secondary.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Bill of Quantities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1048,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1046,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1045,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.boq ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1056,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1055,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1066,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1054,\n              columnNumber: 17\n            }, this), renderBoqReport(reportsData.boq)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1053,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1079,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1080,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1078,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1084,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1090,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getBillOfQuantities(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    boq: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying BOQ report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1087,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1083,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1051,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1117,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(InventoryIcon, {\n              sx: {\n                mr: 1,\n                color: 'success.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Utilizzo Bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1118,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.bobine ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1128,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobine', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1127,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1138,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobine', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1137,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1126,\n              columnNumber: 17\n            }, this), renderBobineReport(reportsData.bobine)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1125,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1152,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1150,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1162,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getBobineReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    bobine: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying bobine report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1159,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1155,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1189,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(BarChartIcon, {\n              sx: {\n                mr: 1,\n                color: 'error.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Cavi per Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1190,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.caviStato ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1200,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('cavi-stato', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1199,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1210,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('cavi-stato', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1209,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1198,\n              columnNumber: 17\n            }, this), renderCaviStatoReport(reportsData.caviStato)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1197,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1223,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1224,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1222,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1234,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getCaviStatoReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    caviStato: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying cavi stato report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1231,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1227,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Report Speciali\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1261,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: \"Questi report richiedono parametri aggiuntivi per essere generati.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Report Bobina Specifica\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1271,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: \"Dettaglio approfondito di una singola bobina con tutti i cavi associati.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1272,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  color: \"info\",\n                  onClick: () => {\n                    setDialogType('bobina-specifica');\n                    setOpenDialog(true);\n                  },\n                  children: \"Genera Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1277,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1276,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1269,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Report Posa per Periodo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1296,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: \"Analisi temporale della posa con trend e pattern di lavoro.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1297,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  color: \"warning\",\n                  onClick: () => {\n                    setDialogType('posa-periodo');\n                    // Set default date range (last month to today)\n                    const today = new Date();\n                    const lastMonth = new Date();\n                    lastMonth.setMonth(today.getMonth() - 1);\n                    setFormData({\n                      ...formData,\n                      data_inizio: lastMonth.toISOString().split('T')[0],\n                      data_fine: today.toISOString().split('T')[0]\n                    });\n                    setOpenDialog(true);\n                  },\n                  children: \"Genera Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1302,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1301,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1294,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1293,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1266,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1260,\n        columnNumber: 9\n      }, this), reportsData.bobinaSpecifica && /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2,\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1332,\n            columnNumber: 43\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n              sx: {\n                mr: 1,\n                color: 'info.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1334,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Bobina Specifica\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1335,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1333,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1332,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1342,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobina-specifica', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1341,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1352,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobina-specifica', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1351,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1340,\n              columnNumber: 17\n            }, this), renderBobinaSpecificaReport(reportsData.bobinaSpecifica)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1339,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1338,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1331,\n        columnNumber: 11\n      }, this), reportsData.posaPeriodo && /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2,\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1369,\n            columnNumber: 43\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n              sx: {\n                mr: 1,\n                color: 'warning.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1371,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Posa per Periodo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1372,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1370,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1369,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1379,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1378,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1389,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1388,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1377,\n              columnNumber: 17\n            }, this), renderPosaPeriodoReport(reportsData.posaPeriodo)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1376,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1375,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1368,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 953,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 931,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageNew, \"Qo68i3NRMGbRWcOk2D5QM9YbhYQ=\", false, function () {\n  return [useNavigate, useParams, useAuth];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Switch", "FormControlLabel", "Assessment", "AssessmentIcon", "<PERSON><PERSON><PERSON>", "BarChartIcon", "<PERSON><PERSON><PERSON>", "PieChartIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "ArrowBack", "ArrowBackIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "ShowChart", "ShowChartIcon", "useNavigate", "useParams", "useAuth", "AdminHomeButton", "reportService", "FilterableTable", "ProgressChart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TimelineChart", "Cavi<PERSON>tato<PERSON>hart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s", "navigate", "cantiereId", "user", "loading", "setLoading", "error", "setError", "reportData", "setReportData", "selectedReport", "setSelectedReport", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportsData", "setReportsData", "progress", "boq", "bobine", "caviStato", "bobinaSpecifica", "posaPeriodo", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAllReports", "progressPromise", "getProgressReport", "catch", "err", "console", "content", "boq<PERSON><PERSON><PERSON>", "getBillOfQuantities", "bob<PERSON><PERSON><PERSON><PERSON>", "getBobineReport", "caviStatoPromise", "getCaviStatoReport", "progressData", "boqData", "bobine<PERSON><PERSON>", "caviStatoData", "Promise", "all", "reportTypes", "id", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "features", "generateReportWithFormat", "reportType", "format", "response", "getBobinaReport", "getPosaPerPeriodoReport", "Error", "prev", "file_url", "window", "open", "detail", "message", "handleReportSelect", "today", "Date", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "handleGenerateReport", "handleCloseDialog", "renderReportContent", "sx", "p", "mt", "children", "display", "justifyContent", "alignItems", "mb", "variant", "nome_cantiere", "gap", "startIcon", "onClick", "size", "renderProgressReport", "renderBoqReport", "renderBobineReport", "renderBobinaSpecificaReport", "renderPosaPeriodoReport", "renderCaviStatoReport", "data", "container", "spacing", "item", "xs", "control", "checked", "onChange", "e", "target", "label", "mr", "defaultExpanded", "expandIcon", "metrica", "valore", "metri_totali", "percentuale", "metri_posati", "percentuale_avanzamento", "metri_da_posare", "columns", "field", "headerName", "width", "align", "pagination", "totale_cavi", "cavi_posati", "percentuale_cavi", "cavi_rimanenti", "media_giornaliera", "giorni_stimati", "data_completamento", "posa_recente", "length", "map", "posa", "metri", "cavi_per_tipo", "dataType", "renderCell", "row", "metri_te<PERSON>ci", "metri_reali", "bobine_per_tipo", "metri_disponibili", "totale_bobine", "stato", "metri_residui", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "percentuale_utilizzo", "bobina", "proprietà", "tipologia", "sezione", "renderSpecial", "cavi_associati", "statistica", "totale_metri_periodo", "giorni_attivi", "posa_giornal<PERSON>", "_data$cavi_per_stato", "cavi_per_stato", "index", "md", "lg", "gutterBottom", "num_cavi", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "value", "placeholder", "helperText", "type", "InputLabelProps", "shrink", "disabled", "component", "my", "ml", "then", "finally", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n  IconButton,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n  BarChart as BarChartIcon,\n  <PERSON><PERSON><PERSON> as PieChartIcon,\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n  ArrowBack as ArrowBackIcon,\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon,\n  ShowChart as ShowChartIcon\n} from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\n\nconst ReportCaviPageNew = () => {\n  const navigate = useNavigate();\n  const { cantiereId } = useParams();\n  const { user } = useAuth();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading progress report:', err);\n            return { content: null };\n          });\n\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading BOQ report:', err);\n            return { content: null };\n          });\n\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading bobine report:', err);\n            return { content: null };\n          });\n\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading cavi stato report:', err);\n            return { content: null };\n          });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          bobinePromise,\n          caviStatoPromise\n        ]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [\n    {\n      id: 'progress',\n      title: 'Report Avanzamento',\n      description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n      icon: <AssessmentIcon />,\n      color: 'primary',\n      features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n    },\n    {\n      id: 'boq',\n      title: 'Bill of Quantities',\n      description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n      icon: <ListIcon />,\n      color: 'secondary',\n      features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n    },\n    {\n      id: 'bobine',\n      title: 'Report Utilizzo Bobine',\n      description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n      icon: <InventoryIcon />,\n      color: 'success',\n      features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n    },\n    {\n      id: 'bobina-specifica',\n      title: 'Report Bobina Specifica',\n      description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n      icon: <CableIcon />,\n      color: 'info',\n      features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n    },\n    {\n      id: 'posa-periodo',\n      title: 'Report Posa per Periodo',\n      description: 'Analisi temporale della posa con trend e pattern di lavoro',\n      icon: <TimelineIcon />,\n      color: 'warning',\n      features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n    },\n    {\n      id: 'cavi-stato',\n      title: 'Report Cavi per Stato',\n      description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n      icon: <BarChartIcon />,\n      color: 'error',\n      features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n    }\n  ];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReportSelect = (reportType) => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n  const renderReportContent = () => {\n    if (!reportData) return null;\n\n    return (\n      <Paper sx={{ p: 3, mt: 3 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\">\n            {selectedReport?.title} - {reportData.nome_cantiere}\n          </Typography>\n          <Box sx={{ display: 'flex', gap: 1 }}>\n            {/* Export buttons */}\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'pdf')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"primary\"\n            >\n              PDF\n            </Button>\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'excel')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"success\"\n            >\n              Excel\n            </Button>\n            <Button\n              startIcon={<RefreshIcon />}\n              onClick={() => setReportData(null)}\n              variant=\"outlined\"\n              size=\"small\"\n            >\n              Nuovo Report\n            </Button>\n          </Box>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Renderizza il contenuto specifico del report */}\n        {dialogType === 'progress' && renderProgressReport(reportData)}\n        {dialogType === 'boq' && renderBoqReport(reportData)}\n        {dialogType === 'bobine' && renderBobineReport(reportData)}\n        {dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData)}\n        {dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData)}\n        {dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)}\n      </Paper>\n    );\n  };\n\n  const renderProgressReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Controllo visualizzazione grafici */}\n      <Grid item xs={12}>\n        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n          <FormControlLabel\n            control={\n              <Switch\n                checked={showCharts}\n                onChange={(e) => setShowCharts(e.target.checked)}\n                color=\"primary\"\n              />\n            }\n            label={\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <ShowChartIcon sx={{ mr: 1 }} />\n                Mostra Grafici\n              </Box>\n            }\n          />\n        </Box>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <ProgressChart data={data} />\n        </Grid>\n      )}\n\n      {/* Avanzamento Generale - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Avanzamento Generale</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={[\n                {\n                  metrica: 'Metri Totali',\n                  valore: `${data.metri_totali}m`,\n                  percentuale: '100%'\n                },\n                {\n                  metrica: 'Metri Posati',\n                  valore: `${data.metri_posati}m`,\n                  percentuale: `${data.percentuale_avanzamento}%`\n                },\n                {\n                  metrica: 'Metri Rimanenti',\n                  valore: `${data.metri_da_posare}m`,\n                  percentuale: `${100 - data.percentuale_avanzamento}%`\n                }\n              ]}\n              columns={[\n                { field: 'metrica', headerName: 'Metrica', width: 200 },\n                { field: 'valore', headerName: 'Valore', width: 150, align: 'right' },\n                { field: 'percentuale', headerName: 'Percentuale', width: 150, align: 'right' }\n              ]}\n              pagination={false}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Cavi - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={[\n                {\n                  metrica: 'Totale Cavi',\n                  valore: data.totale_cavi,\n                  percentuale: '100%'\n                },\n                {\n                  metrica: 'Cavi Posati',\n                  valore: data.cavi_posati,\n                  percentuale: `${data.percentuale_cavi}%`\n                },\n                {\n                  metrica: 'Cavi Rimanenti',\n                  valore: data.cavi_rimanenti,\n                  percentuale: `${100 - data.percentuale_cavi}%`\n                }\n              ]}\n              columns={[\n                { field: 'metrica', headerName: 'Metrica', width: 200 },\n                { field: 'valore', headerName: 'Valore', width: 150, align: 'right' },\n                { field: 'percentuale', headerName: 'Percentuale', width: 150, align: 'right' }\n              ]}\n              pagination={false}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Performance - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Performance</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={[\n                {\n                  metrica: 'Media Giornaliera',\n                  valore: `${data.media_giornaliera}m/giorno`\n                },\n                ...(data.giorni_stimati ? [\n                  {\n                    metrica: 'Giorni Stimati',\n                    valore: `${data.giorni_stimati} giorni`\n                  },\n                  {\n                    metrica: 'Data Completamento',\n                    valore: data.data_completamento\n                  }\n                ] : [])\n              ]}\n              columns={[\n                { field: 'metrica', headerName: 'Metrica', width: 200 },\n                { field: 'valore', headerName: 'Valore', width: 250, align: 'right' }\n              ]}\n              pagination={false}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Posa Recente - Tabella invece di elenco */}\n      {data.posa_recente && data.posa_recente.length > 0 && (\n        <Grid item xs={12}>\n          <Accordion defaultExpanded>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Typography variant=\"h6\">Posa Recente</Typography>\n            </AccordionSummary>\n            <AccordionDetails>\n              <FilterableTable\n                data={data.posa_recente.map(posa => ({\n                  data: posa.data,\n                  metri: `${posa.metri}m`\n                }))}\n                columns={[\n                  { field: 'data', headerName: 'Data', width: 200 },\n                  { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }\n                ]}\n                pagination={data.posa_recente.length > 10}\n              />\n            </AccordionDetails>\n          </Accordion>\n        </Grid>\n      )}\n    </Grid>\n  );\n\n  const renderBoqReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <BoqChart data={data} />\n        </Grid>\n      )}\n\n      {/* Cavi per Tipologia - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi per Tipologia</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.cavi_per_tipo || []}\n              columns={[\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'sezione', headerName: 'Sezione', width: 100 },\n                { field: 'num_cavi', headerName: 'Cavi', width: 80, align: 'right', dataType: 'number' },\n                { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_teorici}m` },\n                { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_reali}m` },\n                { field: 'metri_da_posare', headerName: 'Da Posare', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_da_posare}m` }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Bobine Disponibili - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Bobine Disponibili</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.bobine_per_tipo || []}\n              columns={[\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'sezione', headerName: 'Sezione', width: 100 },\n                { field: 'num_bobine', headerName: 'Bobine', width: 100, align: 'right', dataType: 'number' },\n                { field: 'metri_disponibili', headerName: 'Metri Disponibili', width: 150, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_disponibili}m` }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobineReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <BobineChart data={data} />\n        </Grid>\n      )}\n\n      {/* Bobine del Cantiere - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">\n              Bobine del Cantiere ({data.totale_bobine} totali)\n            </Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.bobine || []}\n              columns={[\n                { field: 'id_bobina', headerName: 'ID Bobina', width: 120 },\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'sezione', headerName: 'Sezione', width: 100 },\n                { field: 'stato', headerName: 'Stato', width: 120,\n                  renderCell: (row) => (\n                    <Chip\n                      label={row.stato}\n                      color={row.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  )\n                },\n                { field: 'metri_totali', headerName: 'Metri Totali', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_totali}m` },\n                { field: 'metri_residui', headerName: 'Metri Residui', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_residui}m` },\n                { field: 'metri_utilizzati', headerName: 'Metri Utilizzati', width: 140, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_utilizzati}m` },\n                { field: 'percentuale_utilizzo', headerName: 'Utilizzo', width: 100, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.percentuale_utilizzo}%` }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobinaSpecificaReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Dettagli Bobina - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Dettagli Bobina</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.bobina ? [\n                {\n                  proprietà: 'ID Bobina',\n                  valore: data.bobina.id_bobina\n                },\n                {\n                  proprietà: 'Tipologia',\n                  valore: data.bobina.tipologia\n                },\n                {\n                  proprietà: 'Sezione',\n                  valore: data.bobina.sezione\n                },\n                {\n                  proprietà: 'Stato',\n                  valore: data.bobina.stato,\n                  renderSpecial: true\n                },\n                {\n                  proprietà: 'Metri Totali',\n                  valore: `${data.bobina.metri_totali}m`\n                },\n                {\n                  proprietà: 'Metri Residui',\n                  valore: `${data.bobina.metri_residui}m`\n                },\n                {\n                  proprietà: 'Metri Utilizzati',\n                  valore: `${data.bobina.metri_utilizzati}m`\n                },\n                {\n                  proprietà: 'Percentuale Utilizzo',\n                  valore: `${data.bobina.percentuale_utilizzo}%`\n                }\n              ] : []}\n              columns={[\n                { field: 'proprietà', headerName: 'Proprietà', width: 200 },\n                { field: 'valore', headerName: 'Valore', width: 250, \n                  renderCell: (row) => row.renderSpecial ? (\n                    <Chip\n                      label={row.valore}\n                      color={row.valore === 'DISPONIBILE' ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  ) : row.valore\n                }\n              ]}\n              pagination={false}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Cavi Associati - Tabella invece di elenco */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">\n              Cavi Associati ({data.totale_cavi})\n            </Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.cavi_associati || []}\n              columns={[\n                { field: 'id_cavo', headerName: 'ID Cavo', width: 120 },\n                { field: 'sistema', headerName: 'Sistema', width: 120 },\n                { field: 'utility', headerName: 'Utility', width: 120 },\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_teorici}m` },\n                { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_reali}m` },\n                { field: 'stato', headerName: 'Stato', width: 120,\n                  renderCell: (row) => (\n                    <Chip\n                      label={row.stato}\n                      color={row.stato === 'POSATO' ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  )\n                }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderPosaPeriodoReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <TimelineChart data={data} />\n        </Grid>\n      )}\n\n      {/* Statistiche Periodo - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Statistiche Periodo</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={[\n                {\n                  statistica: 'Periodo',\n                  valore: `${data.data_inizio} - ${data.data_fine}`\n                },\n                {\n                  statistica: 'Totale Metri',\n                  valore: `${data.totale_metri_periodo}m`\n                },\n                {\n                  statistica: 'Giorni Attivi',\n                  valore: data.giorni_attivi\n                },\n                {\n                  statistica: 'Media Giornaliera',\n                  valore: `${data.media_giornaliera}m/giorno`\n                }\n              ]}\n              columns={[\n                { field: 'statistica', headerName: 'Statistica', width: 200 },\n                { field: 'valore', headerName: 'Valore', width: 250, align: 'right' }\n              ]}\n              pagination={false}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Posa Giornaliera - Tabella invece di elenco */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Posa Giornaliera</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.posa_giornaliera || []}\n              columns={[\n                { field: 'data', headerName: 'Data', width: 200 },\n                { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri}m` }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderCaviStatoReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <CaviStatoChart data={data} />\n        </Grid>\n      )}\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi per Stato di Installazione</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Grid container spacing={2}>\n              {data.cavi_per_stato?.map((stato, index) => (\n                <Grid item xs={12} md={6} lg={3} key={index}>\n                  <Card>\n                    <CardContent>\n                      <Typography variant=\"h6\" gutterBottom>\n                        <Chip\n                          label={stato.stato}\n                          color={stato.stato === 'Installato' ? 'success' : 'warning'}\n                          sx={{ mb: 1 }}\n                        />\n                      </Typography>\n                      <Typography>Numero Cavi: <strong>{stato.num_cavi}</strong></Typography>\n                      <Typography>Metri Teorici: <strong>{stato.metri_teorici}m</strong></Typography>\n                      <Typography>Metri Reali: <strong>{stato.metri_reali}m</strong></Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {selectedReport?.title}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n          {dialogType === 'bobina-specifica' && (\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"ID Bobina\"\n                value={formData.id_bobina}\n                onChange={(e) => setFormData({ ...formData, id_bobina: e.target.value })}\n                placeholder=\"Es: 1, 2, A, B...\"\n                helperText=\"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n              />\n            </Grid>\n          )}\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <IconButton onClick={() => navigate(-1)} color=\"primary\">\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\" component=\"h1\">\n            Report e Analytics\n          </Typography>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Loading indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Reports */}\n      <Box sx={{ mt: 3 }}>\n        {/* Progress Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <AssessmentIcon sx={{ mr: 1, color: 'primary.main' }} />\n              <Typography variant=\"h6\">Report Avanzamento</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.progress ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n                  <FormControlLabel\n                    control={\n                      <Switch\n                        checked={showCharts}\n                        onChange={(e) => setShowCharts(e.target.checked)}\n                        color=\"primary\"\n                      />\n                    }\n                    label={\n                      <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                        <ShowChartIcon sx={{ mr: 1 }} />\n                        Mostra Grafici\n                      </Box>\n                    }\n                  />\n                  <Box>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                </Box>\n                {renderProgressReport(reportsData.progress)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getProgressReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          progress: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying progress report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Bill of Quantities */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ListIcon sx={{ mr: 1, color: 'secondary.main' }} />\n              <Typography variant=\"h6\">Bill of Quantities</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.boq ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('boq', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('boq', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBoqReport(reportsData.boq)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getBillOfQuantities(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          boq: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying BOQ report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Bobine Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <InventoryIcon sx={{ mr: 1, color: 'success.main' }} />\n              <Typography variant=\"h6\">Report Utilizzo Bobine</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.bobine ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobine', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobine', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBobineReport(reportsData.bobine)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getBobineReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          bobine: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying bobine report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Cavi Stato Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <BarChartIcon sx={{ mr: 1, color: 'error.main' }} />\n              <Typography variant=\"h6\">Report Cavi per Stato</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.caviStato ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('cavi-stato', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('cavi-stato', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderCaviStatoReport(reportsData.caviStato)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getCaviStatoReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          caviStato: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying cavi stato report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Special Reports Section */}\n        <Paper sx={{ p: 3, mt: 4 }}>\n          <Typography variant=\"h6\" gutterBottom>Report Speciali</Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n            Questi report richiedono parametri aggiuntivi per essere generati.\n          </Typography>\n\n          <Grid container spacing={3}>\n            {/* Bobina Specifica Report */}\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\">Report Bobina Specifica</Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    Dettaglio approfondito di una singola bobina con tutti i cavi associati.\n                  </Typography>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    fullWidth\n                    variant=\"outlined\"\n                    color=\"info\"\n                    onClick={() => {\n                      setDialogType('bobina-specifica');\n                      setOpenDialog(true);\n                    }}\n                  >\n                    Genera Report\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n\n            {/* Posa per Periodo Report */}\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\">Report Posa per Periodo</Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    Analisi temporale della posa con trend e pattern di lavoro.\n                  </Typography>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    fullWidth\n                    variant=\"outlined\"\n                    color=\"warning\"\n                    onClick={() => {\n                      setDialogType('posa-periodo');\n                      // Set default date range (last month to today)\n                      const today = new Date();\n                      const lastMonth = new Date();\n                      lastMonth.setMonth(today.getMonth() - 1);\n\n                      setFormData({\n                        ...formData,\n                        data_inizio: lastMonth.toISOString().split('T')[0],\n                        data_fine: today.toISOString().split('T')[0]\n                      });\n                      setOpenDialog(true);\n                    }}\n                  >\n                    Genera Report\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Display special reports if they exist */}\n        {reportsData.bobinaSpecifica && (\n          <Accordion defaultExpanded sx={{ mb: 2, mt: 2 }}>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <CableIcon sx={{ mr: 1, color: 'info.main' }} />\n                <Typography variant=\"h6\">Report Bobina Specifica</Typography>\n              </Box>\n            </AccordionSummary>\n            <AccordionDetails>\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobina-specifica', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobina-specifica', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBobinaSpecificaReport(reportsData.bobinaSpecifica)}\n              </Box>\n            </AccordionDetails>\n          </Accordion>\n        )}\n\n        {reportsData.posaPeriodo && (\n          <Accordion defaultExpanded sx={{ mb: 2, mt: 2 }}>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <TimelineIcon sx={{ mr: 1, color: 'warning.main' }} />\n                <Typography variant=\"h6\">Report Posa per Periodo</Typography>\n              </Box>\n            </AccordionSummary>\n            <AccordionDetails>\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('posa-periodo', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderPosaPeriodoReport(reportsData.posaPeriodo)}\n              </Box>\n            </AccordionDetails>\n          </Accordion>\n        )}\n      </Box>\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,eAAe,MAAM,yCAAyC;;AAErE;AACA,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,cAAc,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB;EAAW,CAAC,GAAGjB,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEkB;EAAK,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAE1B,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgF,KAAK,EAAEC,QAAQ,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkF,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACoF,cAAc,EAAEC,iBAAiB,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsF,UAAU,EAAEC,aAAa,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwF,UAAU,EAAEC,aAAa,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0F,QAAQ,EAAEC,WAAW,CAAC,GAAG3F,QAAQ,CAAC;IACvC4F,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjG,QAAQ,CAAC;IAC7CkG,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,IAAI;IACrBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMyG,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC3B,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAM4B,eAAe,GAAG7C,aAAa,CAAC8C,iBAAiB,CAAChC,UAAU,EAAE,OAAO,CAAC,CACzEiC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,gCAAgC,EAAE8B,GAAG,CAAC;UACpD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMC,UAAU,GAAGnD,aAAa,CAACoD,mBAAmB,CAACtC,UAAU,EAAE,OAAO,CAAC,CACtEiC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,2BAA2B,EAAE8B,GAAG,CAAC;UAC/C,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMG,aAAa,GAAGrD,aAAa,CAACsD,eAAe,CAACxC,UAAU,EAAE,OAAO,CAAC,CACrEiC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,8BAA8B,EAAE8B,GAAG,CAAC;UAClD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMK,gBAAgB,GAAGvD,aAAa,CAACwD,kBAAkB,CAAC1C,UAAU,EAAE,OAAO,CAAC,CAC3EiC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,kCAAkC,EAAE8B,GAAG,CAAC;UACtD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;;QAEJ;QACA,MAAM,CAACO,YAAY,EAAEC,OAAO,EAAEC,UAAU,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3EjB,eAAe,EACfM,UAAU,EACVE,aAAa,EACbE,gBAAgB,CACjB,CAAC;;QAEF;QACApB,cAAc,CAAC;UACbC,QAAQ,EAAEqB,YAAY,CAACP,OAAO;UAC9Bb,GAAG,EAAEqB,OAAO,CAACR,OAAO;UACpBZ,MAAM,EAAEqB,UAAU,CAACT,OAAO;UAC1BX,SAAS,EAAEqB,aAAa,CAACV,OAAO;UAChCV,eAAe,EAAE,IAAI;UACrBC,WAAW,EAAE;QACf,CAAC,CAAC;;QAEF;QACA,IAAIgB,YAAY,CAACP,OAAO,IAAIQ,OAAO,CAACR,OAAO,IAAIS,UAAU,CAACT,OAAO,IAAIU,aAAa,CAACV,OAAO,EAAE;UAC1F/B,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,MAAM;UACLA,QAAQ,CAAC,uDAAuD,CAAC;QACnE;MACF,CAAC,CAAC,OAAO6B,GAAG,EAAE;QACZ;QACAC,OAAO,CAAC/B,KAAK,CAAC,mCAAmC,EAAE8B,GAAG,CAAC;QACvD7B,QAAQ,CAAC,uDAAuD,CAAC;MACnE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIH,UAAU,EAAE;MACd8B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC9B,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMiD,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,2FAA2F;IACxGC,IAAI,eAAE3D,OAAA,CAACvC,cAAc;MAAAmG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,yBAAyB;EACrH,CAAC,EACD;IACET,EAAE,EAAE,KAAK;IACTC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,wEAAwE;IACrFC,IAAI,eAAE3D,OAAA,CAAC/B,QAAQ;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,0BAA0B,EAAE,qBAAqB,EAAE,eAAe;EAC1G,CAAC,EACD;IACET,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,uEAAuE;IACpFC,IAAI,eAAE3D,OAAA,CAACjB,aAAa;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,iBAAiB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,yEAAyE;IACtFC,IAAI,eAAE3D,OAAA,CAACnB,SAAS;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,oBAAoB;EAC7F,CAAC,EACD;IACET,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,eAAE3D,OAAA,CAACjC,YAAY;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,mBAAmB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,iFAAiF;IAC9FC,IAAI,eAAE3D,OAAA,CAACrC,YAAY;MAAAiG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,2BAA2B,EAAE,eAAe,EAAE,kBAAkB;EAC/F,CAAC,CACF;;EAED;EACA,MAAMC,wBAAwB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,MAAM,KAAK;IAC7D,IAAI;MACF3D,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI0D,QAAQ;MAEZ,QAAQF,UAAU;QAChB,KAAK,UAAU;UACbE,QAAQ,GAAG,MAAM7E,aAAa,CAAC8C,iBAAiB,CAAChC,UAAU,EAAE8D,MAAM,CAAC;UACpE;QACF,KAAK,KAAK;UACRC,QAAQ,GAAG,MAAM7E,aAAa,CAACoD,mBAAmB,CAACtC,UAAU,EAAE8D,MAAM,CAAC;UACtE;QACF,KAAK,QAAQ;UACXC,QAAQ,GAAG,MAAM7E,aAAa,CAACsD,eAAe,CAACxC,UAAU,EAAE8D,MAAM,CAAC;UAClE;QACF,KAAK,YAAY;UACfC,QAAQ,GAAG,MAAM7E,aAAa,CAACwD,kBAAkB,CAAC1C,UAAU,EAAE8D,MAAM,CAAC;UACrE;QACF,KAAK,kBAAkB;UACrB,IAAI,CAAChD,QAAQ,CAACK,SAAS,EAAE;YACvBd,QAAQ,CAAC,8BAA8B,CAAC;YACxC;UACF;UACA0D,QAAQ,GAAG,MAAM7E,aAAa,CAAC8E,eAAe,CAAChE,UAAU,EAAEc,QAAQ,CAACK,SAAS,EAAE2C,MAAM,CAAC;UACtF;QACF,KAAK,cAAc;UACjB,IAAI,CAAChD,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDb,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACA0D,QAAQ,GAAG,MAAM7E,aAAa,CAAC+E,uBAAuB,CACpDjE,UAAU,EACVc,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClB4C,MACF,CAAC;UACD;QACF;UACE,MAAM,IAAII,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIJ,MAAM,KAAK,OAAO,EAAE;QACtB;QACA,IAAID,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,cAAc,EAAE;UACtExC,cAAc,CAAC8C,IAAI,KAAK;YACtB,GAAGA,IAAI;YACP,CAACN,UAAU,KAAK,kBAAkB,GAAG,iBAAiB,GAAG,aAAa,GAAGE,QAAQ,CAAC3B;UACpF,CAAC,CAAC,CAAC;QACL;QACA7B,aAAa,CAACwD,QAAQ,CAAC3B,OAAO,CAAC;MACjC,CAAC,MAAM;QACL;QACA,IAAI2B,QAAQ,CAACK,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAACP,QAAQ,CAACK,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;IACF,CAAC,CAAC,OAAOlC,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,sCAAsC,EAAE8B,GAAG,CAAC;MAC1D7B,QAAQ,CAAC6B,GAAG,CAACqC,MAAM,IAAIrC,GAAG,CAACsC,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACRrE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsE,kBAAkB,GAAIZ,UAAU,IAAK;IACzCpD,iBAAiB,CAACoD,UAAU,CAAC;IAC7BhD,aAAa,CAACgD,UAAU,CAACX,EAAE,CAAC;;IAE5B;IACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,IAAIW,UAAU,CAACX,EAAE,KAAK,kBAAkB,EAAE;MAC5E;MACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,EAAE;QACpC,MAAMwB,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;QACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;QAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAExC/D,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXG,WAAW,EAAE2D,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAClD9D,SAAS,EAAEwD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC;MACJ;MAEArE,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACL;MACAiD,wBAAwB,CAACC,UAAU,CAACX,EAAE,EAAE,OAAO,CAAC;IAClD;EACF,CAAC;EAED,MAAM+B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMrB,wBAAwB,CAAChD,UAAU,EAAEE,QAAQ,CAACE,OAAO,CAAC;IAC5DL,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMuE,iBAAiB,GAAGA,CAAA,KAAM;IAC9BvE,aAAa,CAAC,KAAK,CAAC;IACpBN,QAAQ,CAAC,IAAI,CAAC;IACdU,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgE,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC7E,UAAU,EAAE,OAAO,IAAI;IAE5B,oBACEZ,OAAA,CAAClE,KAAK;MAAC4J,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzB7F,OAAA,CAACpE,GAAG;QAAC8J,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzF7F,OAAA,CAACnE,UAAU;UAACqK,OAAO,EAAC,IAAI;UAAAL,QAAA,GACrB/E,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE2C,KAAK,EAAC,KAAG,EAAC7C,UAAU,CAACuF,aAAa;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACb/D,OAAA,CAACpE,GAAG;UAAC8J,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE;UAAE,CAAE;UAAAP,QAAA,gBAEnC7F,OAAA,CAAC7D,MAAM;YACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAChD,UAAU,EAAE,KAAK,CAAE;YAC3DgF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZvC,KAAK,EAAC,SAAS;YAAA6B,QAAA,EAChB;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;YACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAChD,UAAU,EAAE,OAAO,CAAE;YAC7DgF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZvC,KAAK,EAAC,SAAS;YAAA6B,QAAA,EAChB;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;YACLkK,SAAS,eAAErG,OAAA,CAACzB,WAAW;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BuC,OAAO,EAAEA,CAAA,KAAMzF,aAAa,CAAC,IAAI,CAAE;YACnCqF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YAAAV,QAAA,EACb;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/D,OAAA,CAACzD,OAAO;QAACmJ,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE;MAAE;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGzB7C,UAAU,KAAK,UAAU,IAAIsF,oBAAoB,CAAC5F,UAAU,CAAC,EAC7DM,UAAU,KAAK,KAAK,IAAIuF,eAAe,CAAC7F,UAAU,CAAC,EACnDM,UAAU,KAAK,QAAQ,IAAIwF,kBAAkB,CAAC9F,UAAU,CAAC,EACzDM,UAAU,KAAK,kBAAkB,IAAIyF,2BAA2B,CAAC/F,UAAU,CAAC,EAC5EM,UAAU,KAAK,cAAc,IAAI0F,uBAAuB,CAAChG,UAAU,CAAC,EACpEM,UAAU,KAAK,YAAY,IAAI2F,qBAAqB,CAACjG,UAAU,CAAC;IAAA;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC;EAEZ,CAAC;EAED,MAAMyC,oBAAoB,GAAIM,IAAI,iBAChC9G,OAAA,CAACjE,IAAI;IAACgL,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAnB,QAAA,gBAEzB7F,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAACpE,GAAG;QAAC8J,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,eAC9D7F,OAAA,CAACzC,gBAAgB;UACf4J,OAAO,eACLnH,OAAA,CAAC1C,MAAM;YACL8J,OAAO,EAAElF,UAAW;YACpBmF,QAAQ,EAAGC,CAAC,IAAKnF,aAAa,CAACmF,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;YACjDpD,KAAK,EAAC;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CACF;UACDyD,KAAK,eACHxH,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAACb,aAAa;cAACuG,EAAE,EAAE;gBAAE+B,EAAE,EAAE;cAAE;YAAE;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGN7B,UAAU,iBACTlC,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAACN,aAAa;QAACoH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CACP,eAGD/D,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAoB;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAE,CACJ;cACEc,OAAO,EAAE,cAAc;cACvBC,MAAM,EAAE,GAAGf,IAAI,CAACgB,YAAY,GAAG;cAC/BC,WAAW,EAAE;YACf,CAAC,EACD;cACEH,OAAO,EAAE,cAAc;cACvBC,MAAM,EAAE,GAAGf,IAAI,CAACkB,YAAY,GAAG;cAC/BD,WAAW,EAAE,GAAGjB,IAAI,CAACmB,uBAAuB;YAC9C,CAAC,EACD;cACEL,OAAO,EAAE,iBAAiB;cAC1BC,MAAM,EAAE,GAAGf,IAAI,CAACoB,eAAe,GAAG;cAClCH,WAAW,EAAE,GAAG,GAAG,GAAGjB,IAAI,CAACmB,uBAAuB;YACpD,CAAC,CACD;YACFE,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,QAAQ;cAAEC,UAAU,EAAE,QAAQ;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAQ,CAAC,EACrE;cAAEH,KAAK,EAAE,aAAa;cAAEC,UAAU,EAAE,aAAa;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAQ,CAAC,CAC/E;YACFC,UAAU,EAAE;UAAM;YAAA5E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGP/D,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAI;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAE,CACJ;cACEc,OAAO,EAAE,aAAa;cACtBC,MAAM,EAAEf,IAAI,CAAC2B,WAAW;cACxBV,WAAW,EAAE;YACf,CAAC,EACD;cACEH,OAAO,EAAE,aAAa;cACtBC,MAAM,EAAEf,IAAI,CAAC4B,WAAW;cACxBX,WAAW,EAAE,GAAGjB,IAAI,CAAC6B,gBAAgB;YACvC,CAAC,EACD;cACEf,OAAO,EAAE,gBAAgB;cACzBC,MAAM,EAAEf,IAAI,CAAC8B,cAAc;cAC3Bb,WAAW,EAAE,GAAG,GAAG,GAAGjB,IAAI,CAAC6B,gBAAgB;YAC7C,CAAC,CACD;YACFR,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,QAAQ;cAAEC,UAAU,EAAE,QAAQ;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAQ,CAAC,EACrE;cAAEH,KAAK,EAAE,aAAa;cAAEC,UAAU,EAAE,aAAa;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAQ,CAAC,CAC/E;YACFC,UAAU,EAAE;UAAM;YAAA5E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGP/D,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAW;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAE,CACJ;cACEc,OAAO,EAAE,mBAAmB;cAC5BC,MAAM,EAAE,GAAGf,IAAI,CAAC+B,iBAAiB;YACnC,CAAC,EACD,IAAI/B,IAAI,CAACgC,cAAc,GAAG,CACxB;cACElB,OAAO,EAAE,gBAAgB;cACzBC,MAAM,EAAE,GAAGf,IAAI,CAACgC,cAAc;YAChC,CAAC,EACD;cACElB,OAAO,EAAE,oBAAoB;cAC7BC,MAAM,EAAEf,IAAI,CAACiC;YACf,CAAC,CACF,GAAG,EAAE,CAAC,CACP;YACFZ,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,QAAQ;cAAEC,UAAU,EAAE,QAAQ;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAQ,CAAC,CACrE;YACFC,UAAU,EAAE;UAAM;YAAA5E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAGN+C,IAAI,CAACkC,YAAY,IAAIlC,IAAI,CAACkC,YAAY,CAACC,MAAM,GAAG,CAAC,iBAChDjJ,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAY;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAACkC,YAAY,CAACE,GAAG,CAACC,IAAI,KAAK;cACnCrC,IAAI,EAAEqC,IAAI,CAACrC,IAAI;cACfsC,KAAK,EAAE,GAAGD,IAAI,CAACC,KAAK;YACtB,CAAC,CAAC,CAAE;YACJjB,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAI,CAAC,EACjD;cAAEF,KAAK,EAAE,OAAO;cAAEC,UAAU,EAAE,cAAc;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAQ,CAAC,CAC1E;YACFC,UAAU,EAAE1B,IAAI,CAACkC,YAAY,CAACC,MAAM,GAAG;UAAG;YAAArF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CACP;EAED,MAAM0C,eAAe,GAAIK,IAAI,iBAC3B9G,OAAA,CAACjE,IAAI;IAACgL,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAnB,QAAA,GAExB3D,UAAU,iBACTlC,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAACJ,QAAQ;QAACkH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CACP,eAGD/D,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAkB;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAACuC,aAAa,IAAI,EAAG;YAC/BlB,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,WAAW;cAAEC,UAAU,EAAE,WAAW;cAAEC,KAAK,EAAE;YAAI,CAAC,EAC3D;cAAEF,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,UAAU;cAAEC,UAAU,EAAE,MAAM;cAAEC,KAAK,EAAE,EAAE;cAAEC,KAAK,EAAE,OAAO;cAAEe,QAAQ,EAAE;YAAS,CAAC,EACxF;cAAElB,KAAK,EAAE,eAAe;cAAEC,UAAU,EAAE,eAAe;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEe,QAAQ,EAAE,QAAQ;cACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;YAAI,CAAC,EAChD;cAAErB,KAAK,EAAE,aAAa;cAAEC,UAAU,EAAE,aAAa;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEe,QAAQ,EAAE,QAAQ;cAC/FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;YAAI,CAAC,EAC9C;cAAEtB,KAAK,EAAE,iBAAiB;cAAEC,UAAU,EAAE,WAAW;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEe,QAAQ,EAAE,QAAQ;cACjGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACtB,eAAe;YAAI,CAAC;UAClD;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGP/D,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAkB;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAAC6C,eAAe,IAAI,EAAG;YACjCxB,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,WAAW;cAAEC,UAAU,EAAE,WAAW;cAAEC,KAAK,EAAE;YAAI,CAAC,EAC3D;cAAEF,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,YAAY;cAAEC,UAAU,EAAE,QAAQ;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEe,QAAQ,EAAE;YAAS,CAAC,EAC7F;cAAElB,KAAK,EAAE,mBAAmB;cAAEC,UAAU,EAAE,mBAAmB;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEe,QAAQ,EAAE,QAAQ;cAC3GC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACI,iBAAiB;YAAI,CAAC;UACpD;YAAAhG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;EAED,MAAM2C,kBAAkB,GAAII,IAAI,iBAC9B9G,OAAA,CAACjE,IAAI;IAACgL,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAnB,QAAA,GAExB3D,UAAU,iBACTlC,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAACL,WAAW;QAACmH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CACP,eAGD/D,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,GAAC,uBACF,EAACiB,IAAI,CAAC+C,aAAa,EAAC,UAC3C;UAAA;YAAAjG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAAChF,MAAM,IAAI,EAAG;YACxBqG,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,WAAW;cAAEC,UAAU,EAAE,WAAW;cAAEC,KAAK,EAAE;YAAI,CAAC,EAC3D;cAAEF,KAAK,EAAE,WAAW;cAAEC,UAAU,EAAE,WAAW;cAAEC,KAAK,EAAE;YAAI,CAAC,EAC3D;cAAEF,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,OAAO;cAAEC,UAAU,EAAE,OAAO;cAAEC,KAAK,EAAE,GAAG;cAC/CiB,UAAU,EAAGC,GAAG,iBACdxJ,OAAA,CAAC5D,IAAI;gBACHoL,KAAK,EAAEgC,GAAG,CAACM,KAAM;gBACjB9F,KAAK,EAAEwF,GAAG,CAACM,KAAK,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;gBAC3DvD,IAAI,EAAC;cAAO;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAEL,CAAC,EACD;cAAEqE,KAAK,EAAE,cAAc;cAAEC,UAAU,EAAE,cAAc;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEe,QAAQ,EAAE,QAAQ;cACjGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAAC1B,YAAY;YAAI,CAAC,EAC/C;cAAEM,KAAK,EAAE,eAAe;cAAEC,UAAU,EAAE,eAAe;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEe,QAAQ,EAAE,QAAQ;cACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACO,aAAa;YAAI,CAAC,EAChD;cAAE3B,KAAK,EAAE,kBAAkB;cAAEC,UAAU,EAAE,kBAAkB;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEe,QAAQ,EAAE,QAAQ;cACzGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACQ,gBAAgB;YAAI,CAAC,EACnD;cAAE5B,KAAK,EAAE,sBAAsB;cAAEC,UAAU,EAAE,UAAU;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEe,QAAQ,EAAE,QAAQ;cACrGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACS,oBAAoB;YAAI,CAAC;UACvD;YAAArG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;EAED,MAAM4C,2BAA2B,GAAIG,IAAI,iBACvC9G,OAAA,CAACjE,IAAI;IAACgL,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAnB,QAAA,gBAEzB7F,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAe;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAACoD,MAAM,GAAG,CAClB;cACEC,SAAS,EAAE,WAAW;cACtBtC,MAAM,EAAEf,IAAI,CAACoD,MAAM,CAACzI;YACtB,CAAC,EACD;cACE0I,SAAS,EAAE,WAAW;cACtBtC,MAAM,EAAEf,IAAI,CAACoD,MAAM,CAACE;YACtB,CAAC,EACD;cACED,SAAS,EAAE,SAAS;cACpBtC,MAAM,EAAEf,IAAI,CAACoD,MAAM,CAACG;YACtB,CAAC,EACD;cACEF,SAAS,EAAE,OAAO;cAClBtC,MAAM,EAAEf,IAAI,CAACoD,MAAM,CAACJ,KAAK;cACzBQ,aAAa,EAAE;YACjB,CAAC,EACD;cACEH,SAAS,EAAE,cAAc;cACzBtC,MAAM,EAAE,GAAGf,IAAI,CAACoD,MAAM,CAACpC,YAAY;YACrC,CAAC,EACD;cACEqC,SAAS,EAAE,eAAe;cAC1BtC,MAAM,EAAE,GAAGf,IAAI,CAACoD,MAAM,CAACH,aAAa;YACtC,CAAC,EACD;cACEI,SAAS,EAAE,kBAAkB;cAC7BtC,MAAM,EAAE,GAAGf,IAAI,CAACoD,MAAM,CAACF,gBAAgB;YACzC,CAAC,EACD;cACEG,SAAS,EAAE,sBAAsB;cACjCtC,MAAM,EAAE,GAAGf,IAAI,CAACoD,MAAM,CAACD,oBAAoB;YAC7C,CAAC,CACF,GAAG,EAAG;YACP9B,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,WAAW;cAAEC,UAAU,EAAE,WAAW;cAAEC,KAAK,EAAE;YAAI,CAAC,EAC3D;cAAEF,KAAK,EAAE,QAAQ;cAAEC,UAAU,EAAE,QAAQ;cAAEC,KAAK,EAAE,GAAG;cACjDiB,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACc,aAAa,gBACpCtK,OAAA,CAAC5D,IAAI;gBACHoL,KAAK,EAAEgC,GAAG,CAAC3B,MAAO;gBAClB7D,KAAK,EAAEwF,GAAG,CAAC3B,MAAM,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;gBAC5DtB,IAAI,EAAC;cAAO;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,GACAyF,GAAG,CAAC3B;YACV,CAAC,CACD;YACFW,UAAU,EAAE;UAAM;YAAA5E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGP/D,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,GAAC,kBACP,EAACiB,IAAI,CAAC2B,WAAW,EAAC,GACpC;UAAA;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAACyD,cAAc,IAAI,EAAG;YAChCpC,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,WAAW;cAAEC,UAAU,EAAE,WAAW;cAAEC,KAAK,EAAE;YAAI,CAAC,EAC3D;cAAEF,KAAK,EAAE,eAAe;cAAEC,UAAU,EAAE,eAAe;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEe,QAAQ,EAAE,QAAQ;cACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;YAAI,CAAC,EAChD;cAAErB,KAAK,EAAE,aAAa;cAAEC,UAAU,EAAE,aAAa;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEe,QAAQ,EAAE,QAAQ;cAC/FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;YAAI,CAAC,EAC9C;cAAEtB,KAAK,EAAE,OAAO;cAAEC,UAAU,EAAE,OAAO;cAAEC,KAAK,EAAE,GAAG;cAC/CiB,UAAU,EAAGC,GAAG,iBACdxJ,OAAA,CAAC5D,IAAI;gBACHoL,KAAK,EAAEgC,GAAG,CAACM,KAAM;gBACjB9F,KAAK,EAAEwF,GAAG,CAACM,KAAK,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAU;gBACtDvD,IAAI,EAAC;cAAO;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAEL,CAAC;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;EAED,MAAM6C,uBAAuB,GAAIE,IAAI,iBACnC9G,OAAA,CAACjE,IAAI;IAACgL,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAnB,QAAA,GAExB3D,UAAU,iBACTlC,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAACH,aAAa;QAACiH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CACP,eAGD/D,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAmB;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAE,CACJ;cACE0D,UAAU,EAAE,SAAS;cACrB3C,MAAM,EAAE,GAAGf,IAAI,CAACvF,WAAW,MAAMuF,IAAI,CAACtF,SAAS;YACjD,CAAC,EACD;cACEgJ,UAAU,EAAE,cAAc;cAC1B3C,MAAM,EAAE,GAAGf,IAAI,CAAC2D,oBAAoB;YACtC,CAAC,EACD;cACED,UAAU,EAAE,eAAe;cAC3B3C,MAAM,EAAEf,IAAI,CAAC4D;YACf,CAAC,EACD;cACEF,UAAU,EAAE,mBAAmB;cAC/B3C,MAAM,EAAE,GAAGf,IAAI,CAAC+B,iBAAiB;YACnC,CAAC,CACD;YACFV,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,YAAY;cAAEC,UAAU,EAAE,YAAY;cAAEC,KAAK,EAAE;YAAI,CAAC,EAC7D;cAAEF,KAAK,EAAE,QAAQ;cAAEC,UAAU,EAAE,QAAQ;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAQ,CAAC,CACrE;YACFC,UAAU,EAAE;UAAM;YAAA5E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGP/D,OAAA,CAACjE,IAAI;MAACkL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAgB;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAAC6D,gBAAgB,IAAI,EAAG;YAClCxC,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAI,CAAC,EACjD;cAAEF,KAAK,EAAE,OAAO;cAAEC,UAAU,EAAE,cAAc;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEe,QAAQ,EAAE,QAAQ;cAC1FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACJ,KAAK;YAAI,CAAC;UACxC;YAAAxF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;EAED,MAAM8C,qBAAqB,GAAIC,IAAI;IAAA,IAAA8D,oBAAA;IAAA,oBACjC5K,OAAA,CAACjE,IAAI;MAACgL,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,GAExB3D,UAAU,iBACTlC,OAAA,CAACjE,IAAI;QAACkL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB7F,OAAA,CAACF,cAAc;UAACgH,IAAI,EAAEA;QAAK;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CACP,eAED/D,OAAA,CAACjE,IAAI;QAACkL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;UAACuK,eAAe;UAAA7B,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;YAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAA+B;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;YAAAwI,QAAA,eACf7F,OAAA,CAACjE,IAAI;cAACgL,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnB,QAAA,GAAA+E,oBAAA,GACxB9D,IAAI,CAAC+D,cAAc,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqB1B,GAAG,CAAC,CAACY,KAAK,EAAEgB,KAAK,kBACrC9K,OAAA,CAACjE,IAAI;gBAACkL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAC6D,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAAnF,QAAA,eAC9B7F,OAAA,CAAChE,IAAI;kBAAA6J,QAAA,eACH7F,OAAA,CAAC/D,WAAW;oBAAA4J,QAAA,gBACV7F,OAAA,CAACnE,UAAU;sBAACqK,OAAO,EAAC,IAAI;sBAAC+E,YAAY;sBAAApF,QAAA,eACnC7F,OAAA,CAAC5D,IAAI;wBACHoL,KAAK,EAAEsC,KAAK,CAACA,KAAM;wBACnB9F,KAAK,EAAE8F,KAAK,CAACA,KAAK,KAAK,YAAY,GAAG,SAAS,GAAG,SAAU;wBAC5DpE,EAAE,EAAE;0BAAEO,EAAE,EAAE;wBAAE;sBAAE;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eACb/D,OAAA,CAACnE,UAAU;sBAAAgK,QAAA,GAAC,eAAa,eAAA7F,OAAA;wBAAA6F,QAAA,EAASiE,KAAK,CAACoB;sBAAQ;wBAAAtH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvE/D,OAAA,CAACnE,UAAU;sBAAAgK,QAAA,GAAC,iBAAe,eAAA7F,OAAA;wBAAA6F,QAAA,GAASiE,KAAK,CAACL,aAAa,EAAC,GAAC;sBAAA;wBAAA7F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC/E/D,OAAA,CAACnE,UAAU;sBAAAgK,QAAA,GAAC,eAAa,eAAA7F,OAAA;wBAAA6F,QAAA,GAASiE,KAAK,CAACJ,WAAW,EAAC,GAAC;sBAAA;wBAAA9F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAd6B+G,KAAK;gBAAAlH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAerC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAMoH,YAAY,GAAGA,CAAA,kBACnBnL,OAAA,CAACtD,MAAM;IAACkI,IAAI,EAAE5D,UAAW;IAACoK,OAAO,EAAE5F,iBAAkB;IAAC6F,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAzF,QAAA,gBAC3E7F,OAAA,CAACrD,WAAW;MAAAkJ,QAAA,EACT/E,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE2C;IAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACd/D,OAAA,CAACpD,aAAa;MAAAiJ,QAAA,GACXnF,KAAK,iBACJV,OAAA,CAAC3D,KAAK;QAACkP,QAAQ,EAAC,OAAO;QAAC7F,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EACnCnF;MAAK;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAED/D,OAAA,CAACjE,IAAI;QAACgL,SAAS;QAACC,OAAO,EAAE,CAAE;QAACtB,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACxC7F,OAAA,CAACjE,IAAI;UAACkL,IAAI;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAChB7F,OAAA,CAAClD,WAAW;YAACwO,SAAS;YAAAzF,QAAA,gBACpB7F,OAAA,CAACjD,UAAU;cAAA8I,QAAA,EAAC;YAAO;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChC/D,OAAA,CAAChD,MAAM;cACLwO,KAAK,EAAEpK,QAAQ,CAACE,OAAQ;cACxBkG,KAAK,EAAC,SAAS;cACfH,QAAQ,EAAGC,CAAC,IAAKjG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAEgG,CAAC,CAACC,MAAM,CAACiE;cAAM,CAAC,CAAE;cAAA3F,QAAA,gBAEvE7F,OAAA,CAAC/C,QAAQ;gBAACuO,KAAK,EAAC,OAAO;gBAAA3F,QAAA,EAAC;cAAoB;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvD/D,OAAA,CAAC/C,QAAQ;gBAACuO,KAAK,EAAC,KAAK;gBAAA3F,QAAA,EAAC;cAAY;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7C/D,OAAA,CAAC/C,QAAQ;gBAACuO,KAAK,EAAC,OAAO;gBAAA3F,QAAA,EAAC;cAAc;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAEN7C,UAAU,KAAK,kBAAkB,iBAChClB,OAAA,CAACjE,IAAI;UAACkL,IAAI;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAChB7F,OAAA,CAAC9C,SAAS;YACRoO,SAAS;YACT9D,KAAK,EAAC,WAAW;YACjBgE,KAAK,EAAEpK,QAAQ,CAACK,SAAU;YAC1B4F,QAAQ,EAAGC,CAAC,IAAKjG,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEK,SAAS,EAAE6F,CAAC,CAACC,MAAM,CAACiE;YAAM,CAAC,CAAE;YACzEC,WAAW,EAAC,mBAAmB;YAC/BC,UAAU,EAAC;UAA0D;YAAA9H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP,EAEA7C,UAAU,KAAK,cAAc,iBAC5BlB,OAAA,CAAAE,SAAA;UAAA2F,QAAA,gBACE7F,OAAA,CAACjE,IAAI;YAACkL,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACf7F,OAAA,CAAC9C,SAAS;cACRoO,SAAS;cACTK,IAAI,EAAC,MAAM;cACXnE,KAAK,EAAC,aAAa;cACnBgE,KAAK,EAAEpK,QAAQ,CAACG,WAAY;cAC5B8F,QAAQ,EAAGC,CAAC,IAAKjG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAE+F,CAAC,CAACC,MAAM,CAACiE;cAAM,CAAC,CAAE;cAC3EI,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP/D,OAAA,CAACjE,IAAI;YAACkL,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACf7F,OAAA,CAAC9C,SAAS;cACRoO,SAAS;cACTK,IAAI,EAAC,MAAM;cACXnE,KAAK,EAAC,WAAW;cACjBgE,KAAK,EAAEpK,QAAQ,CAACI,SAAU;cAC1B6F,QAAQ,EAAGC,CAAC,IAAKjG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAE8F,CAAC,CAACC,MAAM,CAACiE;cAAM,CAAC,CAAE;cACzEI,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAjI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChB/D,OAAA,CAACnD,aAAa;MAAAgJ,QAAA,gBACZ7F,OAAA,CAAC7D,MAAM;QAACmK,OAAO,EAAEd,iBAAkB;QAAAK,QAAA,EAAC;MAAO;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpD/D,OAAA,CAAC7D,MAAM;QACLmK,OAAO,EAAEf,oBAAqB;QAC9BW,OAAO,EAAC,WAAW;QACnB4F,QAAQ,EAAEtL,OAAQ;QAClB6F,SAAS,EAAE7F,OAAO,gBAAGR,OAAA,CAAC1D,gBAAgB;UAACiK,IAAI,EAAE;QAAG;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG/D,OAAA,CAAC3B,cAAc;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAA8B,QAAA,EAExErF,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACE/D,OAAA,CAACpE,GAAG;IAAC8J,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAE,QAAA,gBAEhB7F,OAAA,CAACpE,GAAG;MAAC8J,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF7F,OAAA,CAACpE,GAAG;QAAC8J,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEI,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACzD7F,OAAA,CAACxD,UAAU;UAAC8J,OAAO,EAAEA,CAAA,KAAMjG,QAAQ,CAAC,CAAC,CAAC,CAAE;UAAC2D,KAAK,EAAC,SAAS;UAAA6B,QAAA,eACtD7F,OAAA,CAACvB,aAAa;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACb/D,OAAA,CAACnE,UAAU;UAACqK,OAAO,EAAC,IAAI;UAAC6F,SAAS,EAAC,IAAI;UAAAlG,QAAA,EAAC;QAExC;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN/D,OAAA,CAACT,eAAe;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGLvD,OAAO,iBACNR,OAAA,CAACpE,GAAG;MAAC8J,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEiG,EAAE,EAAE;MAAE,CAAE;MAAAnG,QAAA,eAC5D7F,OAAA,CAAC1D,gBAAgB;QAAAsH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGD/D,OAAA,CAACpE,GAAG;MAAC8J,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAEjB7F,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAChC,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAACvC,cAAc;cAACiI,EAAE,EAAE;gBAAE+B,EAAE,EAAE,CAAC;gBAAEzD,KAAK,EAAE;cAAe;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,EACdnE,WAAW,CAACE,QAAQ,gBACnB5B,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAACpE,GAAG;cAAC8J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACzF7F,OAAA,CAACzC,gBAAgB;gBACf4J,OAAO,eACLnH,OAAA,CAAC1C,MAAM;kBACL8J,OAAO,EAAElF,UAAW;kBACpBmF,QAAQ,EAAGC,CAAC,IAAKnF,aAAa,CAACmF,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;kBACjDpD,KAAK,EAAC;gBAAS;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CACF;gBACDyD,KAAK,eACHxH,OAAA,CAACpE,GAAG;kBAAC8J,EAAE,EAAE;oBAAEI,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAH,QAAA,gBACjD7F,OAAA,CAACb,aAAa;oBAACuG,EAAE,EAAE;sBAAE+B,EAAE,EAAE;oBAAE;kBAAE;oBAAA7D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,kBAElC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF/D,OAAA,CAACpE,GAAG;gBAAAiK,QAAA,gBACF7F,OAAA,CAAC7D,MAAM;kBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,UAAU,EAAE,KAAK,CAAE;kBAC3DgC,OAAO,EAAC,UAAU;kBAClBK,IAAI,EAAC,OAAO;kBACZvC,KAAK,EAAC,SAAS;kBACf0B,EAAE,EAAE;oBAAE+B,EAAE,EAAE;kBAAE,CAAE;kBAAA5B,QAAA,EACf;gBAED;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;kBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAE;kBAC7DgC,OAAO,EAAC,UAAU;kBAClBK,IAAI,EAAC,OAAO;kBACZvC,KAAK,EAAC,SAAS;kBAAA6B,QAAA,EAChB;gBAED;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLyC,oBAAoB,CAAC9E,WAAW,CAACE,QAAQ,CAAC;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,GACJvD,OAAO,gBACTR,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEgG,EAAE,EAAE;YAAE,CAAE;YAAAnG,QAAA,gBACxD7F,OAAA,CAAC1D,gBAAgB;cAACiK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B/D,OAAA,CAACnE,UAAU;cAAC6J,EAAE,EAAE;gBAAEuG,EAAE,EAAE;cAAE,CAAE;cAAApG,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN/D,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAAC3D,KAAK;cAACkP,QAAQ,EAAC,OAAO;cAAC7F,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA,CAAC7D,MAAM;cACL+J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAErG,OAAA,CAACzB,WAAW;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb7F,UAAU,CAAC,IAAI,CAAC;gBAChBjB,aAAa,CAAC8C,iBAAiB,CAAChC,UAAU,EAAE,OAAO,CAAC,CACjD4L,IAAI,CAACpF,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP7C,QAAQ,EAAEkF,IAAI,CAACpE;kBACjB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC/B,KAAK,CAAC,iCAAiC,EAAE8B,GAAG,CAAC;gBACvD,CAAC,CAAC,CACD2J,OAAO,CAAC,MAAM;kBACb1L,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAoF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ/D,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAChC,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAAC/B,QAAQ;cAACyH,EAAE,EAAE;gBAAE+B,EAAE,EAAE,CAAC;gBAAEzD,KAAK,EAAE;cAAiB;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,EACdnE,WAAW,CAACG,GAAG,gBACd7B,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAACpE,GAAG;cAAC8J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAE;gBACtDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAE;gBACxDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL0C,eAAe,CAAC/E,WAAW,CAACG,GAAG,CAAC;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,GACJvD,OAAO,gBACTR,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEgG,EAAE,EAAE;YAAE,CAAE;YAAAnG,QAAA,gBACxD7F,OAAA,CAAC1D,gBAAgB;cAACiK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B/D,OAAA,CAACnE,UAAU;cAAC6J,EAAE,EAAE;gBAAEuG,EAAE,EAAE;cAAE,CAAE;cAAApG,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN/D,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAAC3D,KAAK;cAACkP,QAAQ,EAAC,OAAO;cAAC7F,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA,CAAC7D,MAAM;cACL+J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAErG,OAAA,CAACzB,WAAW;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb7F,UAAU,CAAC,IAAI,CAAC;gBAChBjB,aAAa,CAACoD,mBAAmB,CAACtC,UAAU,EAAE,OAAO,CAAC,CACnD4L,IAAI,CAACpF,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP5C,GAAG,EAAEiF,IAAI,CAACpE;kBACZ,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,EAAE8B,GAAG,CAAC;gBAClD,CAAC,CAAC,CACD2J,OAAO,CAAC,MAAM;kBACb1L,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAoF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ/D,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAChC,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAACjB,aAAa;cAAC2G,EAAE,EAAE;gBAAE+B,EAAE,EAAE,CAAC;gBAAEzD,KAAK,EAAE;cAAe;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAsB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,EACdnE,WAAW,CAACI,MAAM,gBACjB9B,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAACpE,GAAG;cAAC8J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,QAAQ,EAAE,KAAK,CAAE;gBACzDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,QAAQ,EAAE,OAAO,CAAE;gBAC3DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL2C,kBAAkB,CAAChF,WAAW,CAACI,MAAM,CAAC;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,GACJvD,OAAO,gBACTR,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEgG,EAAE,EAAE;YAAE,CAAE;YAAAnG,QAAA,gBACxD7F,OAAA,CAAC1D,gBAAgB;cAACiK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B/D,OAAA,CAACnE,UAAU;cAAC6J,EAAE,EAAE;gBAAEuG,EAAE,EAAE;cAAE,CAAE;cAAApG,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN/D,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAAC3D,KAAK;cAACkP,QAAQ,EAAC,OAAO;cAAC7F,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA,CAAC7D,MAAM;cACL+J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAErG,OAAA,CAACzB,WAAW;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb7F,UAAU,CAAC,IAAI,CAAC;gBAChBjB,aAAa,CAACsD,eAAe,CAACxC,UAAU,EAAE,OAAO,CAAC,CAC/C4L,IAAI,CAACpF,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP3C,MAAM,EAAEgF,IAAI,CAACpE;kBACf,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC/B,KAAK,CAAC,+BAA+B,EAAE8B,GAAG,CAAC;gBACrD,CAAC,CAAC,CACD2J,OAAO,CAAC,MAAM;kBACb1L,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAoF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ/D,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAChC,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAACrC,YAAY;cAAC+H,EAAE,EAAE;gBAAE+B,EAAE,EAAE,CAAC;gBAAEzD,KAAK,EAAE;cAAa;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAqB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,EACdnE,WAAW,CAACK,SAAS,gBACpB/B,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAACpE,GAAG;cAAC8J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,YAAY,EAAE,KAAK,CAAE;gBAC7DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,YAAY,EAAE,OAAO,CAAE;gBAC/DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL8C,qBAAqB,CAACnF,WAAW,CAACK,SAAS,CAAC;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,GACJvD,OAAO,gBACTR,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEgG,EAAE,EAAE;YAAE,CAAE;YAAAnG,QAAA,gBACxD7F,OAAA,CAAC1D,gBAAgB;cAACiK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B/D,OAAA,CAACnE,UAAU;cAAC6J,EAAE,EAAE;gBAAEuG,EAAE,EAAE;cAAE,CAAE;cAAApG,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN/D,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAAC3D,KAAK;cAACkP,QAAQ,EAAC,OAAO;cAAC7F,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA,CAAC7D,MAAM;cACL+J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAErG,OAAA,CAACzB,WAAW;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb7F,UAAU,CAAC,IAAI,CAAC;gBAChBjB,aAAa,CAACwD,kBAAkB,CAAC1C,UAAU,EAAE,OAAO,CAAC,CAClD4L,IAAI,CAACpF,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP1C,SAAS,EAAE+E,IAAI,CAACpE;kBAClB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC/B,KAAK,CAAC,mCAAmC,EAAE8B,GAAG,CAAC;gBACzD,CAAC,CAAC,CACD2J,OAAO,CAAC,MAAM;kBACb1L,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAoF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ/D,OAAA,CAAClE,KAAK;QAAC4J,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACzB7F,OAAA,CAACnE,UAAU;UAACqK,OAAO,EAAC,IAAI;UAAC+E,YAAY;UAAApF,QAAA,EAAC;QAAe;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAClE/D,OAAA,CAACnE,UAAU;UAACqK,OAAO,EAAC,OAAO;UAAClC,KAAK,EAAC,gBAAgB;UAAC0B,EAAE,EAAE;YAAEO,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAElE;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb/D,OAAA,CAACjE,IAAI;UAACgL,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAnB,QAAA,gBAEzB7F,OAAA,CAACjE,IAAI;YAACkL,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6D,EAAE,EAAE,CAAE;YAAAlF,QAAA,eACvB7F,OAAA,CAAChE,IAAI;cAAA6J,QAAA,gBACH7F,OAAA,CAAC/D,WAAW;gBAAA4J,QAAA,gBACV7F,OAAA,CAACnE,UAAU;kBAACqK,OAAO,EAAC,IAAI;kBAAAL,QAAA,EAAC;gBAAuB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7D/D,OAAA,CAACnE,UAAU;kBAACqK,OAAO,EAAC,OAAO;kBAAClC,KAAK,EAAC,gBAAgB;kBAAC0B,EAAE,EAAE;oBAAEO,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAElE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACd/D,OAAA,CAAC9D,WAAW;gBAAA2J,QAAA,eACV7F,OAAA,CAAC7D,MAAM;kBACLmP,SAAS;kBACTpF,OAAO,EAAC,UAAU;kBAClBlC,KAAK,EAAC,MAAM;kBACZsC,OAAO,EAAEA,CAAA,KAAM;oBACbnF,aAAa,CAAC,kBAAkB,CAAC;oBACjCF,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBAAA4E,QAAA,EACH;gBAED;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGP/D,OAAA,CAACjE,IAAI;YAACkL,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6D,EAAE,EAAE,CAAE;YAAAlF,QAAA,eACvB7F,OAAA,CAAChE,IAAI;cAAA6J,QAAA,gBACH7F,OAAA,CAAC/D,WAAW;gBAAA4J,QAAA,gBACV7F,OAAA,CAACnE,UAAU;kBAACqK,OAAO,EAAC,IAAI;kBAAAL,QAAA,EAAC;gBAAuB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7D/D,OAAA,CAACnE,UAAU;kBAACqK,OAAO,EAAC,OAAO;kBAAClC,KAAK,EAAC,gBAAgB;kBAAC0B,EAAE,EAAE;oBAAEO,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAElE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACd/D,OAAA,CAAC9D,WAAW;gBAAA2J,QAAA,eACV7F,OAAA,CAAC7D,MAAM;kBACLmP,SAAS;kBACTpF,OAAO,EAAC,UAAU;kBAClBlC,KAAK,EAAC,SAAS;kBACfsC,OAAO,EAAEA,CAAA,KAAM;oBACbnF,aAAa,CAAC,cAAc,CAAC;oBAC7B;oBACA,MAAM6D,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;oBACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;oBAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;oBAExC/D,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXG,WAAW,EAAE2D,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;sBAClD9D,SAAS,EAAEwD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC7C,CAAC,CAAC;oBACFrE,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBAAA4E,QAAA,EACH;gBAED;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGPrC,WAAW,CAACM,eAAe,iBAC1BhC,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAChC,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAEL,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBAC9C7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAACnB,SAAS;cAAC6G,EAAE,EAAE;gBAAE+B,EAAE,EAAE,CAAC;gBAAEzD,KAAK,EAAE;cAAY;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAACpE,GAAG;cAAC8J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,kBAAkB,EAAE,KAAK,CAAE;gBACnEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,kBAAkB,EAAE,OAAO,CAAE;gBACrEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL4C,2BAA2B,CAACjF,WAAW,CAACM,eAAe,CAAC;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACZ,EAEArC,WAAW,CAACO,WAAW,iBACtBjC,OAAA,CAAC7C,SAAS;QAACuK,eAAe;QAAChC,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAEL,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBAC9C7F,OAAA,CAAC5C,gBAAgB;UAACuK,UAAU,eAAE3H,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAACjC,YAAY;cAAC2H,EAAE,EAAE;gBAAE+B,EAAE,EAAE,CAAC;gBAAEzD,KAAK,EAAE;cAAe;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAACpE,GAAG;cAAC8J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,cAAc,EAAE,KAAK,CAAE;gBAC/DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAE;gBACjEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL6C,uBAAuB,CAAClF,WAAW,CAACO,WAAW,CAAC;UAAA;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACZ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLoH,YAAY,CAAC,CAAC;EAAA;IAAAvH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC3D,EAAA,CAp0CID,iBAAiB;EAAA,QACJf,WAAW,EACLC,SAAS,EACfC,OAAO;AAAA;AAAA8M,EAAA,GAHpBjM,iBAAiB;AAs0CvB,eAAeA,iBAAiB;AAAC,IAAAiM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}