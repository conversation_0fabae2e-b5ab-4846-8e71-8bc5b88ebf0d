{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 4h-4c-.55 0-1 .45-1 1s.45 1 1 1h1.58l-3.97 3.97C11.73 9.36 10.66 9 9.5 9 6.46 9 4 11.46 4 14.5S6.46 20 9.5 20s5.5-2.46 5.5-5.5c0-1.16-.36-2.23-.97-3.12L18 7.42V9c0 .55.45 1 1 1s1-.45 1-1V5c0-.55-.45-1-1-1M9.5 18C7.57 18 6 16.43 6 14.5S7.57 11 9.5 11s3.5 1.57 3.5 3.5S11.43 18 9.5 18\"\n}), 'MaleRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/icons-material/esm/MaleRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 4h-4c-.55 0-1 .45-1 1s.45 1 1 1h1.58l-3.97 3.97C11.73 9.36 10.66 9 9.5 9 6.46 9 4 11.46 4 14.5S6.46 20 9.5 20s5.5-2.46 5.5-5.5c0-1.16-.36-2.23-.97-3.12L18 7.42V9c0 .55.45 1 1 1s1-.45 1-1V5c0-.55-.45-1-1-1M9.5 18C7.57 18 6 16.43 6 14.5S7.57 11 9.5 11s3.5 1.57 3.5 3.5S11.43 18 9.5 18\"\n}), 'MaleRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}