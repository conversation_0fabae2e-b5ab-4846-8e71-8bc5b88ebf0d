{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\nconst eraValues = {\n  narrow: [\"aK\", \"pK\"],\n  abbreviated: [\"a.K.E.\", \"p.K.E.\"],\n  wide: [\"anta<PERSON>\", \"<PERSON><PERSON><PERSON>\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"K1\", \"K2\", \"K3\", \"K4\"],\n  wide: [\"1-a kvaronjaro\", \"2-a kvaronjaro\", \"3-a kvaronjaro\", \"4-a kvaronjaro\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"jan\", \"feb\", \"mar\", \"apr\", \"maj\", \"jun\", \"jul\", \"aŭg\", \"sep\", \"okt\", \"nov\", \"dec\"],\n  wide: [\"januaro\", \"februaro\", \"marto\", \"aprilo\", \"majo\", \"junio\", \"julio\", \"aŭgusto\", \"septembro\", \"oktobro\", \"novembro\", \"decembro\"]\n};\nconst dayValues = {\n  narrow: [\"D\", \"L\", \"M\", \"M\", \"Ĵ\", \"V\", \"S\"],\n  short: [\"di\", \"lu\", \"ma\", \"me\", \"ĵa\", \"ve\", \"sa\"],\n  abbreviated: [\"dim\", \"lun\", \"mar\", \"mer\", \"ĵaŭ\", \"ven\", \"sab\"],\n  wide: [\"dimanĉo\", \"lundo\", \"mardo\", \"merkredo\", \"ĵaŭdo\", \"vendredo\", \"sabato\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"noktomezo\",\n    noon: \"tagmezo\",\n    morning: \"matene\",\n    afternoon: \"posttagmeze\",\n    evening: \"vespere\",\n    night: \"nokte\"\n  },\n  abbreviated: {\n    am: \"a.t.m.\",\n    pm: \"p.t.m.\",\n    midnight: \"noktomezo\",\n    noon: \"tagmezo\",\n    morning: \"matene\",\n    afternoon: \"posttagmeze\",\n    evening: \"vespere\",\n    night: \"nokte\"\n  },\n  wide: {\n    am: \"antaŭtagmeze\",\n    pm: \"posttagmeze\",\n    midnight: \"noktomezo\",\n    noon: \"tagmezo\",\n    morning: \"matene\",\n    afternoon: \"posttagmeze\",\n    evening: \"vespere\",\n    night: \"nokte\"\n  }\n};\nconst ordinalNumber = dirtyNumber => {\n  const number = Number(dirtyNumber);\n  return number + \"-a\";\n};\nexport const localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: function (quarter) {\n      return Number(quarter) - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "ordinalNumber", "dirtyNumber", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/eo/_lib/localize.mjs"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\nconst eraValues = {\n  narrow: [\"aK\", \"pK\"],\n  abbreviated: [\"a.K.E.\", \"p.K.E.\"],\n  wide: [\"anta<PERSON>\", \"<PERSON><PERSON><PERSON>\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"K1\", \"K2\", \"K3\", \"K4\"],\n  wide: [\n    \"1-a kvaronjaro\",\n    \"2-a kvaronjaro\",\n    \"3-a kvaronjaro\",\n    \"4-a kvaronjaro\",\n  ],\n};\n\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\n    \"jan\",\n    \"feb\",\n    \"mar\",\n    \"apr\",\n    \"maj\",\n    \"jun\",\n    \"jul\",\n    \"aŭg\",\n    \"sep\",\n    \"okt\",\n    \"nov\",\n    \"dec\",\n  ],\n\n  wide: [\n    \"januaro\",\n    \"februaro\",\n    \"marto\",\n    \"aprilo\",\n    \"majo\",\n    \"junio\",\n    \"julio\",\n    \"aŭgusto\",\n    \"septembro\",\n    \"oktobro\",\n    \"novembro\",\n    \"decembro\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"D\", \"L\", \"M\", \"M\", \"Ĵ\", \"V\", \"S\"],\n  short: [\"di\", \"lu\", \"ma\", \"me\", \"ĵa\", \"ve\", \"sa\"],\n  abbreviated: [\"dim\", \"lun\", \"mar\", \"mer\", \"ĵaŭ\", \"ven\", \"sab\"],\n  wide: [\n    \"dimanĉo\",\n    \"lundo\",\n    \"mardo\",\n    \"merkredo\",\n    \"ĵaŭdo\",\n    \"vendredo\",\n    \"sabato\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"noktomezo\",\n    noon: \"tagmezo\",\n    morning: \"matene\",\n    afternoon: \"posttagmeze\",\n    evening: \"vespere\",\n    night: \"nokte\",\n  },\n  abbreviated: {\n    am: \"a.t.m.\",\n    pm: \"p.t.m.\",\n    midnight: \"noktomezo\",\n    noon: \"tagmezo\",\n    morning: \"matene\",\n    afternoon: \"posttagmeze\",\n    evening: \"vespere\",\n    night: \"nokte\",\n  },\n  wide: {\n    am: \"antaŭtagmeze\",\n    pm: \"posttagmeze\",\n    midnight: \"noktomezo\",\n    noon: \"tagmezo\",\n    morning: \"matene\",\n    afternoon: \"posttagmeze\",\n    evening: \"vespere\",\n    night: \"nokte\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber) => {\n  const number = Number(dirtyNumber);\n  return number + \"-a\";\n};\n\nexport const localize = {\n  ordinalNumber: ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: function (quarter) {\n      return Number(quarter) - 1;\n    },\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAEhE,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACpBC,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EACjCC,IAAI,EAAE,CAAC,mBAAmB,EAAE,aAAa;AAC3C,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CACJ,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB;AAEpB,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,SAAS,EACT,UAAU,EACV,OAAO,EACP,QAAQ,EACR,MAAM,EACN,OAAO,EACP,OAAO,EACP,SAAS,EACT,WAAW,EACX,SAAS,EACT,UAAU,EACV,UAAU;AAEd,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CACJ,SAAS,EACT,OAAO,EACP,OAAO,EACP,UAAU,EACV,OAAO,EACP,UAAU,EACV,QAAQ;AAEZ,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,QAAQ;IACZC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,cAAc;IAClBC,EAAE,EAAE,aAAa;IACjBC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,aAAa,GAAIC,WAAW,IAAK;EACrC,MAAMC,MAAM,GAAGC,MAAM,CAACF,WAAW,CAAC;EAClC,OAAOC,MAAM,GAAG,IAAI;AACtB,CAAC;AAED,OAAO,MAAME,QAAQ,GAAG;EACtBJ,aAAa,EAAEA,aAAa;EAE5BK,GAAG,EAAEvB,eAAe,CAAC;IACnBwB,MAAM,EAAEvB,SAAS;IACjBwB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE1B,eAAe,CAAC;IACvBwB,MAAM,EAAEnB,aAAa;IACrBoB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAAAA,CAAUD,OAAO,EAAE;MACnC,OAAOL,MAAM,CAACK,OAAO,CAAC,GAAG,CAAC;IAC5B;EACF,CAAC,CAAC;EAEFE,KAAK,EAAE5B,eAAe,CAAC;IACrBwB,MAAM,EAAElB,WAAW;IACnBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE7B,eAAe,CAAC;IACnBwB,MAAM,EAAEjB,SAAS;IACjBkB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAE9B,eAAe,CAAC;IACzBwB,MAAM,EAAEf,eAAe;IACvBgB,YAAY,EAAE;EAChB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}