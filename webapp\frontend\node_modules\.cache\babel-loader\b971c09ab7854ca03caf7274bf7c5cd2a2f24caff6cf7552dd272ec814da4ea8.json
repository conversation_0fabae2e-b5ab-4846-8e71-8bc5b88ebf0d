{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Grid, Card, CardContent, CardActions, Button, Chip, Alert, CircularProgress, Divider, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails, Switch, FormControlLabel } from '@mui/material';\nimport { Assessment as AssessmentIcon, BarChart as BarChartIcon, PieChart as PieChartIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon, ArrowBack as ArrowBackIcon, DateRange as DateRangeIcon, Cable as CableIcon, Inventory as InventoryIcon, ExpandMore as ExpandMoreIcon, ShowChart as ShowChartIcon } from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    cantiereId\n  } = useParams();\n  const {\n    user\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading progress report:', err);\n          return {\n            content: null\n          };\n        });\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video').catch(err => {\n          console.error('Error loading BOQ report:', err);\n          return {\n            content: null\n          };\n        });\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading bobine report:', err);\n          return {\n            content: null\n          };\n        });\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading cavi stato report:', err);\n          return {\n            content: null\n          };\n        });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([progressPromise, boqPromise, bobinePromise, caviStatoPromise]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [{\n    id: 'progress',\n    title: 'Report Avanzamento',\n    description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n    icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 13\n    }, this),\n    color: 'primary',\n    features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n  }, {\n    id: 'boq',\n    title: 'Bill of Quantities',\n    description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n    icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 13\n    }, this),\n    color: 'secondary',\n    features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n  }, {\n    id: 'bobine',\n    title: 'Report Utilizzo Bobine',\n    description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n    icon: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 13\n    }, this),\n    color: 'success',\n    features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n  }, {\n    id: 'bobina-specifica',\n    title: 'Report Bobina Specifica',\n    description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n    icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 13\n    }, this),\n    color: 'info',\n    features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n  }, {\n    id: 'posa-periodo',\n    title: 'Report Posa per Periodo',\n    description: 'Analisi temporale della posa con trend e pattern di lavoro',\n    icon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 13\n    }, this),\n    color: 'warning',\n    features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n  }, {\n    id: 'cavi-stato',\n    title: 'Report Cavi per Stato',\n    description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n    icon: /*#__PURE__*/_jsxDEV(BarChartIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 13\n    }, this),\n    color: 'error',\n    features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n  }];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReportSelect = reportType => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderReportContent = () => {\n    if (!reportData) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title, \" - \", reportData.nome_cantiere]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'pdf'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"primary\",\n            children: \"PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'excel'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"success\",\n            children: \"Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 26\n            }, this),\n            onClick: () => setReportData(null),\n            variant: \"outlined\",\n            size: \"small\",\n            children: \"Nuovo Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this), dialogType === 'progress' && renderProgressReport(reportData), dialogType === 'boq' && renderBoqReport(reportData), dialogType === 'bobine' && renderBobineReport(reportData), dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData), dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData), dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 7\n    }, this);\n  };\n  const renderProgressReport = data => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Avanzamento Generale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Metri Totali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.metri_totali, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 43\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Metri Posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.metri_posati, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 43\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Metri Rimanenti: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.metri_da_posare, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 46\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Avanzamento: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.percentuale_avanzamento, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Totale Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: data.totale_cavi\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Cavi Posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: data.cavi_posati\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Cavi Rimanenti: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: data.cavi_rimanenti\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Percentuale Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.percentuale_cavi, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 47\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Media Giornaliera: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.media_giornaliera, \"m/giorno\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 48\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this), data.giorni_stimati && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Giorni Stimati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [data.giorni_stimati, \" giorni\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 418,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Data Completamento: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: data.data_completamento\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 7\n    }, this), data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 43\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Posa Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: data.posa_recente.slice(0, 5).map((posa, index) => /*#__PURE__*/_jsxDEV(Typography, {\n                children: [posa.data, \": \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [posa.metri, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 36\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 429,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 370,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = data => {\n    var _data$cavi_per_tipo, _data$bobine_per_tipo;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Cavi per Tipologia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: (_data$cavi_per_tipo = data.cavi_per_tipo) === null || _data$cavi_per_tipo === void 0 ? void 0 : _data$cavi_per_tipo.map((cavo, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                lg: 4,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle1\",\n                      children: cavo.tipologia\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [\"Sezione: \", cavo.sezione]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 465,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Cavi: \", cavo.num_cavi]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 466,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Teorici: \", cavo.metri_teorici, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 467,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Reali: \", cavo.metri_reali, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 468,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Da Posare: \", cavo.metri_da_posare, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 469,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Bobine Disponibili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: (_data$bobine_per_tipo = data.bobine_per_tipo) === null || _data$bobine_per_tipo === void 0 ? void 0 : _data$bobine_per_tipo.map((bobina, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                lg: 4,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle1\",\n                      children: bobina.tipologia\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [\"Sezione: \", bobina.sezione]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 491,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Bobine: \", bobina.num_bobine]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Disponibili: \", bobina.metri_disponibili, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 493,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 5\n    }, this);\n  };\n  const renderBobineReport = data => {\n    var _data$bobine;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: [\"Bobine del Cantiere (\", data.totale_bobine, \" totali)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: (_data$bobine = data.bobine) === null || _data$bobine === void 0 ? void 0 : _data$bobine.map((bobina, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                lg: 4,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle1\",\n                      children: bobina.id_bobina\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 520,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [bobina.tipologia, \" - \", bobina.sezione]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 521,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: bobina.stato,\n                      color: bobina.stato === 'DISPONIBILE' ? 'success' : 'warning',\n                      size: \"small\",\n                      sx: {\n                        mb: 1\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 522,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Totali: \", bobina.metri_totali, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 528,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Residui: \", bobina.metri_residui, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Utilizzati: \", bobina.metri_utilizzati, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Utilizzo: \", bobina.percentuale_utilizzo, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 506,\n      columnNumber: 5\n    }, this);\n  };\n  const renderBobinaSpecificaReport = data => {\n    var _data$bobina, _data$bobina2, _data$bobina3, _data$bobina4, _data$bobina5, _data$bobina6, _data$bobina7, _data$bobina8, _data$bobina9, _data$cavi_associati;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Dettagli Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"ID: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: (_data$bobina = data.bobina) === null || _data$bobina === void 0 ? void 0 : _data$bobina.id_bobina\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 33\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Tipologia: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: (_data$bobina2 = data.bobina) === null || _data$bobina2 === void 0 ? void 0 : _data$bobina2.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 40\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Sezione: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: (_data$bobina3 = data.bobina) === null || _data$bobina3 === void 0 ? void 0 : _data$bobina3.sezione\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 38\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: (_data$bobina4 = data.bobina) === null || _data$bobina4 === void 0 ? void 0 : _data$bobina4.stato,\n                  color: ((_data$bobina5 = data.bobina) === null || _data$bobina5 === void 0 ? void 0 : _data$bobina5.stato) === 'DISPONIBILE' ? 'success' : 'warning',\n                  sx: {\n                    my: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Totali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [(_data$bobina6 = data.bobina) === null || _data$bobina6 === void 0 ? void 0 : _data$bobina6.metri_totali, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 43\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Residui: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [(_data$bobina7 = data.bobina) === null || _data$bobina7 === void 0 ? void 0 : _data$bobina7.metri_residui, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 562,\n                    columnNumber: 44\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Utilizzati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [(_data$bobina8 = data.bobina) === null || _data$bobina8 === void 0 ? void 0 : _data$bobina8.metri_utilizzati, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 47\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Utilizzo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [(_data$bobina9 = data.bobina) === null || _data$bobina9 === void 0 ? void 0 : _data$bobina9.percentuale_utilizzo, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 39\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: [\"Cavi Associati (\", data.totale_cavi, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    maxHeight: 300,\n                    overflow: 'auto'\n                  },\n                  children: (_data$cavi_associati = data.cavi_associati) === null || _data$cavi_associati === void 0 ? void 0 : _data$cavi_associati.map((cavo, index) => /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mb: 1,\n                      p: 1,\n                      border: '1px solid #e0e0e0',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: cavo.id_cavo\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 584,\n                        columnNumber: 51\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 584,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: [cavo.sistema, \" - \", cavo.utility, \" - \", cavo.tipologia]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 585,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      display: \"block\",\n                      children: [\"Teorici: \", cavo.metri_teorici, \"m | Reali: \", cavo.metri_reali, \"m | Stato: \", cavo.stato]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 581,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 544,\n      columnNumber: 5\n    }, this);\n  };\n  const renderPosaPeriodoReport = data => {\n    var _data$posa_giornalier;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Statistiche Periodo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Periodo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [data.data_inizio, \" - \", data.data_fine]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 38\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 612,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Totale Metri: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [data.totale_metri_periodo, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 613,\n                    columnNumber: 43\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Giorni Attivi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: data.giorni_attivi\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 44\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Media Giornaliera: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [data.media_giornaliera, \"m/giorno\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 48\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 604,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Posa Giornaliera\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    maxHeight: 300,\n                    overflow: 'auto'\n                  },\n                  children: (_data$posa_giornalier = data.posa_giornaliera) === null || _data$posa_giornalier === void 0 ? void 0 : _data$posa_giornalier.map((posa, index) => /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      py: 0.5\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      children: posa.data\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 633,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [posa.metri, \"m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 634,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 634,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 632,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 622,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 603,\n      columnNumber: 5\n    }, this);\n  };\n  const renderCaviStatoReport = data => {\n    var _data$cavi_per_stato;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Cavi per Stato di Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: (_data$cavi_per_stato = data.cavi_per_stato) === null || _data$cavi_per_stato === void 0 ? void 0 : _data$cavi_per_stato.map((stato, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                lg: 3,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      gutterBottom: true,\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        label: stato.stato,\n                        color: stato.stato === 'Installato' ? 'success' : 'warning',\n                        sx: {\n                          mb: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 660,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 659,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Numero Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: stato.num_cavi\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 666,\n                        columnNumber: 48\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 666,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Teorici: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [stato.metri_teorici, \"m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 667,\n                        columnNumber: 50\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 667,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Reali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [stato.metri_reali, \"m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 668,\n                        columnNumber: 48\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 668,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 647,\n      columnNumber: 5\n    }, this);\n  };\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 682,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 687,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 693,\n          columnNumber: 11\n        }, this), dialogType === 'bobina-specifica' && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Bobina\",\n            value: formData.id_bobina,\n            onChange: e => setFormData({\n              ...formData,\n              id_bobina: e.target.value\n            }),\n            placeholder: \"Es: 1, 2, A, B...\",\n            helperText: \"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 13\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 692,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 685,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 748,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 753,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 753,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 749,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 747,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 681,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => navigate(-1),\n          color: \"primary\",\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 766,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Report e Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 769,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 765,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 773,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 764,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 779,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 778,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                mr: 1,\n                color: 'primary.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Avanzamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 790,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 787,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.progress ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 798,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 797,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 807,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 796,\n              columnNumber: 17\n            }, this), renderProgressReport(reportsData.progress)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getProgressReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    progress: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying progress report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 829,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 825,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 793,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 786,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n              sx: {\n                mr: 1,\n                color: 'secondary.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Bill of Quantities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 862,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 860,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 859,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.boq ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 870,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 880,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 17\n            }, this), renderBoqReport(reportsData.boq)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 892,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 898,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 904,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getBillOfQuantities(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    boq: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying BOQ report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 897,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 865,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 858,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 931,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(InventoryIcon, {\n              sx: {\n                mr: 1,\n                color: 'success.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Utilizzo Bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 934,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 932,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 931,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.bobine ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 942,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobine', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 941,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 952,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobine', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 951,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 940,\n              columnNumber: 17\n            }, this), renderBobineReport(reportsData.bobine)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 939,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 970,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 976,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getBobineReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    bobine: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying bobine report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 973,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 969,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 937,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 930,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1003,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(BarChartIcon, {\n              sx: {\n                mr: 1,\n                color: 'error.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1005,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Cavi per Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1006,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1004,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1003,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.caviStato ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1014,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('cavi-stato', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1013,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1024,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('cavi-stato', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1023,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1012,\n              columnNumber: 17\n            }, this), renderCaviStatoReport(reportsData.caviStato)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1011,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1038,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1036,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1042,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1048,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getCaviStatoReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    caviStato: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying cavi stato report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1045,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1041,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1009,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1002,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Report Speciali\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1075,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: \"Questi report richiedono parametri aggiuntivi per essere generati.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1076,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Report Bobina Specifica\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: \"Dettaglio approfondito di una singola bobina con tutti i cavi associati.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1086,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1084,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  color: \"info\",\n                  onClick: () => {\n                    setDialogType('bobina-specifica');\n                    setOpenDialog(true);\n                  },\n                  children: \"Genera Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1091,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1090,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1083,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1082,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Report Posa per Periodo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1110,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: \"Analisi temporale della posa con trend e pattern di lavoro.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1111,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  color: \"warning\",\n                  onClick: () => {\n                    setDialogType('posa-periodo');\n                    // Set default date range (last month to today)\n                    const today = new Date();\n                    const lastMonth = new Date();\n                    lastMonth.setMonth(today.getMonth() - 1);\n                    setFormData({\n                      ...formData,\n                      data_inizio: lastMonth.toISOString().split('T')[0],\n                      data_fine: today.toISOString().split('T')[0]\n                    });\n                    setOpenDialog(true);\n                  },\n                  children: \"Genera Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1116,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1115,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1108,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1107,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1080,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1074,\n        columnNumber: 9\n      }, this), reportsData.bobinaSpecifica && /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2,\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1146,\n            columnNumber: 43\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n              sx: {\n                mr: 1,\n                color: 'info.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1148,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Bobina Specifica\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1149,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1147,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1146,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1156,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobina-specifica', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1155,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1166,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobina-specifica', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1165,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1154,\n              columnNumber: 17\n            }, this), renderBobinaSpecificaReport(reportsData.bobinaSpecifica)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1153,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1152,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1145,\n        columnNumber: 11\n      }, this), reportsData.posaPeriodo && /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2,\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1183,\n            columnNumber: 43\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n              sx: {\n                mr: 1,\n                color: 'warning.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1185,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Posa per Periodo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1186,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1184,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1183,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1193,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1203,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1202,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1191,\n              columnNumber: 17\n            }, this), renderPosaPeriodoReport(reportsData.posaPeriodo)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1190,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1189,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1182,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 784,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 762,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageNew, \"8wJhDlMpstDJI8p20f2IqC2nUho=\", false, function () {\n  return [useNavigate, useParams, useAuth];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Switch", "FormControlLabel", "Assessment", "AssessmentIcon", "<PERSON><PERSON><PERSON>", "BarChartIcon", "<PERSON><PERSON><PERSON>", "PieChartIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "ArrowBack", "ArrowBackIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "ShowChart", "ShowChartIcon", "useNavigate", "useParams", "useAuth", "AdminHomeButton", "reportService", "ProgressChart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TimelineChart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s", "navigate", "cantiereId", "user", "loading", "setLoading", "error", "setError", "reportData", "setReportData", "selectedReport", "setSelectedReport", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportsData", "setReportsData", "progress", "boq", "bobine", "caviStato", "bobinaSpecifica", "posaPeriodo", "loadAllReports", "progressPromise", "getProgressReport", "catch", "err", "console", "content", "boq<PERSON><PERSON><PERSON>", "getBillOfQuantities", "bob<PERSON><PERSON><PERSON><PERSON>", "getBobineReport", "caviStatoPromise", "getCaviStatoReport", "progressData", "boqData", "bobine<PERSON><PERSON>", "caviStatoData", "Promise", "all", "reportTypes", "id", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "features", "generateReportWithFormat", "reportType", "format", "response", "getBobinaReport", "getPosaPerPeriodoReport", "Error", "prev", "file_url", "window", "open", "detail", "message", "handleReportSelect", "today", "Date", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "handleGenerateReport", "handleCloseDialog", "renderReportContent", "sx", "p", "mt", "children", "display", "justifyContent", "alignItems", "mb", "variant", "nome_cantiere", "gap", "startIcon", "onClick", "size", "renderProgressReport", "renderBoqReport", "renderBobineReport", "renderBobinaSpecificaReport", "renderPosaPeriodoReport", "renderCaviStatoReport", "data", "container", "spacing", "item", "xs", "defaultExpanded", "expandIcon", "metri_totali", "metri_posati", "metri_da_posare", "percentuale_avanzamento", "totale_cavi", "cavi_posati", "cavi_rimanenti", "percentuale_cavi", "media_giornaliera", "giorni_stimati", "data_completamento", "posa_recente", "length", "slice", "map", "posa", "index", "metri", "_data$cavi_per_tipo", "_data$bobine_per_tipo", "cavi_per_tipo", "cavo", "md", "lg", "tipologia", "sezione", "num_cavi", "metri_te<PERSON>ci", "metri_reali", "bobine_per_tipo", "bobina", "num_bobine", "metri_disponibili", "_data$bobine", "totale_bobine", "label", "stato", "metri_residui", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "percentuale_utilizzo", "_data$bobina", "_data$bobina2", "_data$bobina3", "_data$bobina4", "_data$bobina5", "_data$bobina6", "_data$bobina7", "_data$bobina8", "_data$bobina9", "_data$cavi_associati", "my", "maxHeight", "overflow", "cavi_associati", "border", "borderRadius", "id_cavo", "sistema", "utility", "_data$posa_giornalier", "totale_metri_periodo", "giorni_attivi", "posa_giornal<PERSON>", "py", "_data$cavi_per_stato", "cavi_per_stato", "gutterBottom", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "value", "onChange", "e", "target", "placeholder", "helperText", "type", "InputLabelProps", "shrink", "disabled", "component", "mr", "ml", "then", "finally", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n  IconButton,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n  BarChart as BarChartIcon,\n  <PERSON><PERSON><PERSON> as PieChartIcon,\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n  ArrowBack as ArrowBackIcon,\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon,\n  ShowChart as ShowChartIcon\n} from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\n\nconst ReportCaviPageNew = () => {\n  const navigate = useNavigate();\n  const { cantiereId } = useParams();\n  const { user } = useAuth();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading progress report:', err);\n            return { content: null };\n          });\n\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading BOQ report:', err);\n            return { content: null };\n          });\n\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading bobine report:', err);\n            return { content: null };\n          });\n\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading cavi stato report:', err);\n            return { content: null };\n          });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          bobinePromise,\n          caviStatoPromise\n        ]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [\n    {\n      id: 'progress',\n      title: 'Report Avanzamento',\n      description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n      icon: <AssessmentIcon />,\n      color: 'primary',\n      features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n    },\n    {\n      id: 'boq',\n      title: 'Bill of Quantities',\n      description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n      icon: <ListIcon />,\n      color: 'secondary',\n      features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n    },\n    {\n      id: 'bobine',\n      title: 'Report Utilizzo Bobine',\n      description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n      icon: <InventoryIcon />,\n      color: 'success',\n      features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n    },\n    {\n      id: 'bobina-specifica',\n      title: 'Report Bobina Specifica',\n      description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n      icon: <CableIcon />,\n      color: 'info',\n      features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n    },\n    {\n      id: 'posa-periodo',\n      title: 'Report Posa per Periodo',\n      description: 'Analisi temporale della posa con trend e pattern di lavoro',\n      icon: <TimelineIcon />,\n      color: 'warning',\n      features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n    },\n    {\n      id: 'cavi-stato',\n      title: 'Report Cavi per Stato',\n      description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n      icon: <BarChartIcon />,\n      color: 'error',\n      features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n    }\n  ];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReportSelect = (reportType) => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n  const renderReportContent = () => {\n    if (!reportData) return null;\n\n    return (\n      <Paper sx={{ p: 3, mt: 3 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\">\n            {selectedReport?.title} - {reportData.nome_cantiere}\n          </Typography>\n          <Box sx={{ display: 'flex', gap: 1 }}>\n            {/* Export buttons */}\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'pdf')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"primary\"\n            >\n              PDF\n            </Button>\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'excel')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"success\"\n            >\n              Excel\n            </Button>\n            <Button\n              startIcon={<RefreshIcon />}\n              onClick={() => setReportData(null)}\n              variant=\"outlined\"\n              size=\"small\"\n            >\n              Nuovo Report\n            </Button>\n          </Box>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Renderizza il contenuto specifico del report */}\n        {dialogType === 'progress' && renderProgressReport(reportData)}\n        {dialogType === 'boq' && renderBoqReport(reportData)}\n        {dialogType === 'bobine' && renderBobineReport(reportData)}\n        {dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData)}\n        {dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData)}\n        {dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)}\n      </Paper>\n    );\n  };\n\n  const renderProgressReport = (data) => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Avanzamento Generale</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>Metri Totali: <strong>{data.metri_totali}m</strong></Typography>\n                <Typography>Metri Posati: <strong>{data.metri_posati}m</strong></Typography>\n                <Typography>Metri Rimanenti: <strong>{data.metri_da_posare}m</strong></Typography>\n                <Typography>Avanzamento: <strong>{data.percentuale_avanzamento}%</strong></Typography>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>Totale Cavi: <strong>{data.totale_cavi}</strong></Typography>\n                <Typography>Cavi Posati: <strong>{data.cavi_posati}</strong></Typography>\n                <Typography>Cavi Rimanenti: <strong>{data.cavi_rimanenti}</strong></Typography>\n                <Typography>Percentuale Cavi: <strong>{data.percentuale_cavi}%</strong></Typography>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Performance</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>Media Giornaliera: <strong>{data.media_giornaliera}m/giorno</strong></Typography>\n                {data.giorni_stimati && (\n                  <>\n                    <Typography>Giorni Stimati: <strong>{data.giorni_stimati} giorni</strong></Typography>\n                    <Typography>Data Completamento: <strong>{data.data_completamento}</strong></Typography>\n                  </>\n                )}\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {data.posa_recente && data.posa_recente.length > 0 && (\n        <Grid item xs={12}>\n          <Accordion defaultExpanded>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Typography variant=\"h6\">Posa Recente</Typography>\n            </AccordionSummary>\n            <AccordionDetails>\n              <Card>\n                <CardContent>\n                  {data.posa_recente.slice(0, 5).map((posa, index) => (\n                    <Typography key={index}>\n                      {posa.data}: <strong>{posa.metri}m</strong>\n                    </Typography>\n                  ))}\n                </CardContent>\n              </Card>\n            </AccordionDetails>\n          </Accordion>\n        </Grid>\n      )}\n    </Grid>\n  );\n\n  const renderBoqReport = (data) => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi per Tipologia</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Grid container spacing={2}>\n              {data.cavi_per_tipo?.map((cavo, index) => (\n                <Grid item xs={12} md={6} lg={4} key={index}>\n                  <Card>\n                    <CardContent>\n                      <Typography variant=\"subtitle1\">{cavo.tipologia}</Typography>\n                      <Typography variant=\"body2\">Sezione: {cavo.sezione}</Typography>\n                      <Typography>Cavi: {cavo.num_cavi}</Typography>\n                      <Typography>Metri Teorici: {cavo.metri_teorici}m</Typography>\n                      <Typography>Metri Reali: {cavo.metri_reali}m</Typography>\n                      <Typography>Da Posare: {cavo.metri_da_posare}m</Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Bobine Disponibili</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Grid container spacing={2}>\n              {data.bobine_per_tipo?.map((bobina, index) => (\n                <Grid item xs={12} md={6} lg={4} key={index}>\n                  <Card>\n                    <CardContent>\n                      <Typography variant=\"subtitle1\">{bobina.tipologia}</Typography>\n                      <Typography variant=\"body2\">Sezione: {bobina.sezione}</Typography>\n                      <Typography>Bobine: {bobina.num_bobine}</Typography>\n                      <Typography>Metri Disponibili: {bobina.metri_disponibili}m</Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobineReport = (data) => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">\n              Bobine del Cantiere ({data.totale_bobine} totali)\n            </Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Grid container spacing={2}>\n              {data.bobine?.map((bobina, index) => (\n                <Grid item xs={12} md={6} lg={4} key={index}>\n                  <Card>\n                    <CardContent>\n                      <Typography variant=\"subtitle1\">{bobina.id_bobina}</Typography>\n                      <Typography variant=\"body2\">{bobina.tipologia} - {bobina.sezione}</Typography>\n                      <Chip\n                        label={bobina.stato}\n                        color={bobina.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n                        size=\"small\"\n                        sx={{ mb: 1 }}\n                      />\n                      <Typography>Metri Totali: {bobina.metri_totali}m</Typography>\n                      <Typography>Metri Residui: {bobina.metri_residui}m</Typography>\n                      <Typography>Metri Utilizzati: {bobina.metri_utilizzati}m</Typography>\n                      <Typography>Utilizzo: {bobina.percentuale_utilizzo}%</Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobinaSpecificaReport = (data) => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Dettagli Bobina</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>ID: <strong>{data.bobina?.id_bobina}</strong></Typography>\n                <Typography>Tipologia: <strong>{data.bobina?.tipologia}</strong></Typography>\n                <Typography>Sezione: <strong>{data.bobina?.sezione}</strong></Typography>\n                <Chip\n                  label={data.bobina?.stato}\n                  color={data.bobina?.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n                  sx={{ my: 1 }}\n                />\n                <Typography>Metri Totali: <strong>{data.bobina?.metri_totali}m</strong></Typography>\n                <Typography>Metri Residui: <strong>{data.bobina?.metri_residui}m</strong></Typography>\n                <Typography>Metri Utilizzati: <strong>{data.bobina?.metri_utilizzati}m</strong></Typography>\n                <Typography>Utilizzo: <strong>{data.bobina?.percentuale_utilizzo}%</strong></Typography>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">\n              Cavi Associati ({data.totale_cavi})\n            </Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n                  {data.cavi_associati?.map((cavo, index) => (\n                    <Box key={index} sx={{ mb: 1, p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                      <Typography variant=\"body2\"><strong>{cavo.id_cavo}</strong></Typography>\n                      <Typography variant=\"caption\">\n                        {cavo.sistema} - {cavo.utility} - {cavo.tipologia}\n                      </Typography>\n                      <Typography variant=\"caption\" display=\"block\">\n                        Teorici: {cavo.metri_teorici}m | Reali: {cavo.metri_reali}m | Stato: {cavo.stato}\n                      </Typography>\n                    </Box>\n                  ))}\n                </Box>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderPosaPeriodoReport = (data) => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Statistiche Periodo</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>Periodo: <strong>{data.data_inizio} - {data.data_fine}</strong></Typography>\n                <Typography>Totale Metri: <strong>{data.totale_metri_periodo}m</strong></Typography>\n                <Typography>Giorni Attivi: <strong>{data.giorni_attivi}</strong></Typography>\n                <Typography>Media Giornaliera: <strong>{data.media_giornaliera}m/giorno</strong></Typography>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Posa Giornaliera</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n                  {data.posa_giornaliera?.map((posa, index) => (\n                    <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>\n                      <Typography>{posa.data}</Typography>\n                      <Typography><strong>{posa.metri}m</strong></Typography>\n                    </Box>\n                  ))}\n                </Box>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderCaviStatoReport = (data) => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi per Stato di Installazione</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Grid container spacing={2}>\n              {data.cavi_per_stato?.map((stato, index) => (\n                <Grid item xs={12} md={6} lg={3} key={index}>\n                  <Card>\n                    <CardContent>\n                      <Typography variant=\"h6\" gutterBottom>\n                        <Chip\n                          label={stato.stato}\n                          color={stato.stato === 'Installato' ? 'success' : 'warning'}\n                          sx={{ mb: 1 }}\n                        />\n                      </Typography>\n                      <Typography>Numero Cavi: <strong>{stato.num_cavi}</strong></Typography>\n                      <Typography>Metri Teorici: <strong>{stato.metri_teorici}m</strong></Typography>\n                      <Typography>Metri Reali: <strong>{stato.metri_reali}m</strong></Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {selectedReport?.title}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n          {dialogType === 'bobina-specifica' && (\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"ID Bobina\"\n                value={formData.id_bobina}\n                onChange={(e) => setFormData({ ...formData, id_bobina: e.target.value })}\n                placeholder=\"Es: 1, 2, A, B...\"\n                helperText=\"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n              />\n            </Grid>\n          )}\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <IconButton onClick={() => navigate(-1)} color=\"primary\">\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\" component=\"h1\">\n            Report e Analytics\n          </Typography>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Loading indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Reports */}\n      <Box sx={{ mt: 3 }}>\n        {/* Progress Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <AssessmentIcon sx={{ mr: 1, color: 'primary.main' }} />\n              <Typography variant=\"h6\">Report Avanzamento</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.progress ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('progress', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('progress', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderProgressReport(reportsData.progress)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getProgressReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          progress: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying progress report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Bill of Quantities */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ListIcon sx={{ mr: 1, color: 'secondary.main' }} />\n              <Typography variant=\"h6\">Bill of Quantities</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.boq ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('boq', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('boq', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBoqReport(reportsData.boq)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getBillOfQuantities(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          boq: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying BOQ report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Bobine Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <InventoryIcon sx={{ mr: 1, color: 'success.main' }} />\n              <Typography variant=\"h6\">Report Utilizzo Bobine</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.bobine ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobine', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobine', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBobineReport(reportsData.bobine)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getBobineReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          bobine: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying bobine report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Cavi Stato Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <BarChartIcon sx={{ mr: 1, color: 'error.main' }} />\n              <Typography variant=\"h6\">Report Cavi per Stato</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.caviStato ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('cavi-stato', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('cavi-stato', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderCaviStatoReport(reportsData.caviStato)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getCaviStatoReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          caviStato: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying cavi stato report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Special Reports Section */}\n        <Paper sx={{ p: 3, mt: 4 }}>\n          <Typography variant=\"h6\" gutterBottom>Report Speciali</Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n            Questi report richiedono parametri aggiuntivi per essere generati.\n          </Typography>\n\n          <Grid container spacing={3}>\n            {/* Bobina Specifica Report */}\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\">Report Bobina Specifica</Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    Dettaglio approfondito di una singola bobina con tutti i cavi associati.\n                  </Typography>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    fullWidth\n                    variant=\"outlined\"\n                    color=\"info\"\n                    onClick={() => {\n                      setDialogType('bobina-specifica');\n                      setOpenDialog(true);\n                    }}\n                  >\n                    Genera Report\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n\n            {/* Posa per Periodo Report */}\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\">Report Posa per Periodo</Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    Analisi temporale della posa con trend e pattern di lavoro.\n                  </Typography>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    fullWidth\n                    variant=\"outlined\"\n                    color=\"warning\"\n                    onClick={() => {\n                      setDialogType('posa-periodo');\n                      // Set default date range (last month to today)\n                      const today = new Date();\n                      const lastMonth = new Date();\n                      lastMonth.setMonth(today.getMonth() - 1);\n\n                      setFormData({\n                        ...formData,\n                        data_inizio: lastMonth.toISOString().split('T')[0],\n                        data_fine: today.toISOString().split('T')[0]\n                      });\n                      setOpenDialog(true);\n                    }}\n                  >\n                    Genera Report\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Display special reports if they exist */}\n        {reportsData.bobinaSpecifica && (\n          <Accordion defaultExpanded sx={{ mb: 2, mt: 2 }}>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <CableIcon sx={{ mr: 1, color: 'info.main' }} />\n                <Typography variant=\"h6\">Report Bobina Specifica</Typography>\n              </Box>\n            </AccordionSummary>\n            <AccordionDetails>\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobina-specifica', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobina-specifica', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBobinaSpecificaReport(reportsData.bobinaSpecifica)}\n              </Box>\n            </AccordionDetails>\n          </Accordion>\n        )}\n\n        {reportsData.posaPeriodo && (\n          <Accordion defaultExpanded sx={{ mb: 2, mt: 2 }}>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <TimelineIcon sx={{ mr: 1, color: 'warning.main' }} />\n                <Typography variant=\"h6\">Report Posa per Periodo</Typography>\n              </Box>\n            </AccordionSummary>\n            <AccordionDetails>\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('posa-periodo', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderPosaPeriodoReport(reportsData.posaPeriodo)}\n              </Box>\n            </AccordionDetails>\n          </Accordion>\n        )}\n      </Box>\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;;AAExD;AACA,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAOC,aAAa,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEgB;EAAW,CAAC,GAAGf,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEgB;EAAK,CAAC,GAAGf,OAAO,CAAC,CAAC;EAE1B,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8E,KAAK,EAAEC,QAAQ,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACgF,UAAU,EAAEC,aAAa,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACkF,cAAc,EAAEC,iBAAiB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoF,UAAU,EAAEC,aAAa,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsF,UAAU,EAAEC,aAAa,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwF,QAAQ,EAAEC,WAAW,CAAC,GAAGzF,QAAQ,CAAC;IACvC0F,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/F,QAAQ,CAAC;IAC7CgG,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,IAAI;IACrBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACApG,SAAS,CAAC,MAAM;IACd,MAAMqG,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjCzB,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAM0B,eAAe,GAAGzC,aAAa,CAAC0C,iBAAiB,CAAC9B,UAAU,EAAE,OAAO,CAAC,CACzE+B,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC7B,KAAK,CAAC,gCAAgC,EAAE4B,GAAG,CAAC;UACpD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMC,UAAU,GAAG/C,aAAa,CAACgD,mBAAmB,CAACpC,UAAU,EAAE,OAAO,CAAC,CACtE+B,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC7B,KAAK,CAAC,2BAA2B,EAAE4B,GAAG,CAAC;UAC/C,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMG,aAAa,GAAGjD,aAAa,CAACkD,eAAe,CAACtC,UAAU,EAAE,OAAO,CAAC,CACrE+B,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC7B,KAAK,CAAC,8BAA8B,EAAE4B,GAAG,CAAC;UAClD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMK,gBAAgB,GAAGnD,aAAa,CAACoD,kBAAkB,CAACxC,UAAU,EAAE,OAAO,CAAC,CAC3E+B,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC7B,KAAK,CAAC,kCAAkC,EAAE4B,GAAG,CAAC;UACtD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;;QAEJ;QACA,MAAM,CAACO,YAAY,EAAEC,OAAO,EAAEC,UAAU,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3EjB,eAAe,EACfM,UAAU,EACVE,aAAa,EACbE,gBAAgB,CACjB,CAAC;;QAEF;QACAlB,cAAc,CAAC;UACbC,QAAQ,EAAEmB,YAAY,CAACP,OAAO;UAC9BX,GAAG,EAAEmB,OAAO,CAACR,OAAO;UACpBV,MAAM,EAAEmB,UAAU,CAACT,OAAO;UAC1BT,SAAS,EAAEmB,aAAa,CAACV,OAAO;UAChCR,eAAe,EAAE,IAAI;UACrBC,WAAW,EAAE;QACf,CAAC,CAAC;;QAEF;QACA,IAAIc,YAAY,CAACP,OAAO,IAAIQ,OAAO,CAACR,OAAO,IAAIS,UAAU,CAACT,OAAO,IAAIU,aAAa,CAACV,OAAO,EAAE;UAC1F7B,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,MAAM;UACLA,QAAQ,CAAC,uDAAuD,CAAC;QACnE;MACF,CAAC,CAAC,OAAO2B,GAAG,EAAE;QACZ;QACAC,OAAO,CAAC7B,KAAK,CAAC,mCAAmC,EAAE4B,GAAG,CAAC;QACvD3B,QAAQ,CAAC,uDAAuD,CAAC;MACnE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIH,UAAU,EAAE;MACd4B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC5B,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAM+C,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,2FAA2F;IACxGC,IAAI,eAAEzD,OAAA,CAACrC,cAAc;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,yBAAyB;EACrH,CAAC,EACD;IACET,EAAE,EAAE,KAAK;IACTC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,wEAAwE;IACrFC,IAAI,eAAEzD,OAAA,CAAC7B,QAAQ;MAAAuF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,0BAA0B,EAAE,qBAAqB,EAAE,eAAe;EAC1G,CAAC,EACD;IACET,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,uEAAuE;IACpFC,IAAI,eAAEzD,OAAA,CAACf,aAAa;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,iBAAiB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,yEAAyE;IACtFC,IAAI,eAAEzD,OAAA,CAACjB,SAAS;MAAA2E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,oBAAoB;EAC7F,CAAC,EACD;IACET,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,eAAEzD,OAAA,CAAC/B,YAAY;MAAAyF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,mBAAmB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,iFAAiF;IAC9FC,IAAI,eAAEzD,OAAA,CAACnC,YAAY;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,2BAA2B,EAAE,eAAe,EAAE,kBAAkB;EAC/F,CAAC,CACF;;EAED;EACA,MAAMC,wBAAwB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,MAAM,KAAK;IAC7D,IAAI;MACFzD,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAIwD,QAAQ;MAEZ,QAAQF,UAAU;QAChB,KAAK,UAAU;UACbE,QAAQ,GAAG,MAAMzE,aAAa,CAAC0C,iBAAiB,CAAC9B,UAAU,EAAE4D,MAAM,CAAC;UACpE;QACF,KAAK,KAAK;UACRC,QAAQ,GAAG,MAAMzE,aAAa,CAACgD,mBAAmB,CAACpC,UAAU,EAAE4D,MAAM,CAAC;UACtE;QACF,KAAK,QAAQ;UACXC,QAAQ,GAAG,MAAMzE,aAAa,CAACkD,eAAe,CAACtC,UAAU,EAAE4D,MAAM,CAAC;UAClE;QACF,KAAK,YAAY;UACfC,QAAQ,GAAG,MAAMzE,aAAa,CAACoD,kBAAkB,CAACxC,UAAU,EAAE4D,MAAM,CAAC;UACrE;QACF,KAAK,kBAAkB;UACrB,IAAI,CAAC9C,QAAQ,CAACK,SAAS,EAAE;YACvBd,QAAQ,CAAC,8BAA8B,CAAC;YACxC;UACF;UACAwD,QAAQ,GAAG,MAAMzE,aAAa,CAAC0E,eAAe,CAAC9D,UAAU,EAAEc,QAAQ,CAACK,SAAS,EAAEyC,MAAM,CAAC;UACtF;QACF,KAAK,cAAc;UACjB,IAAI,CAAC9C,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDb,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACAwD,QAAQ,GAAG,MAAMzE,aAAa,CAAC2E,uBAAuB,CACpD/D,UAAU,EACVc,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClB0C,MACF,CAAC;UACD;QACF;UACE,MAAM,IAAII,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIJ,MAAM,KAAK,OAAO,EAAE;QACtB;QACA,IAAID,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,cAAc,EAAE;UACtEtC,cAAc,CAAC4C,IAAI,KAAK;YACtB,GAAGA,IAAI;YACP,CAACN,UAAU,KAAK,kBAAkB,GAAG,iBAAiB,GAAG,aAAa,GAAGE,QAAQ,CAAC3B;UACpF,CAAC,CAAC,CAAC;QACL;QACA3B,aAAa,CAACsD,QAAQ,CAAC3B,OAAO,CAAC;MACjC,CAAC,MAAM;QACL;QACA,IAAI2B,QAAQ,CAACK,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAACP,QAAQ,CAACK,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;IACF,CAAC,CAAC,OAAOlC,GAAG,EAAE;MACZC,OAAO,CAAC7B,KAAK,CAAC,sCAAsC,EAAE4B,GAAG,CAAC;MAC1D3B,QAAQ,CAAC2B,GAAG,CAACqC,MAAM,IAAIrC,GAAG,CAACsC,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACRnE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoE,kBAAkB,GAAIZ,UAAU,IAAK;IACzClD,iBAAiB,CAACkD,UAAU,CAAC;IAC7B9C,aAAa,CAAC8C,UAAU,CAACX,EAAE,CAAC;;IAE5B;IACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,IAAIW,UAAU,CAACX,EAAE,KAAK,kBAAkB,EAAE;MAC5E;MACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,EAAE;QACpC,MAAMwB,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;QACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;QAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAExC7D,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXG,WAAW,EAAEyD,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAClD5D,SAAS,EAAEsD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC;MACJ;MAEAnE,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACL;MACA+C,wBAAwB,CAACC,UAAU,CAACX,EAAE,EAAE,OAAO,CAAC;IAClD;EACF,CAAC;EAED,MAAM+B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMrB,wBAAwB,CAAC9C,UAAU,EAAEE,QAAQ,CAACE,OAAO,CAAC;IAC5DL,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMqE,iBAAiB,GAAGA,CAAA,KAAM;IAC9BrE,aAAa,CAAC,KAAK,CAAC;IACpBN,QAAQ,CAAC,IAAI,CAAC;IACdU,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAM8D,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC3E,UAAU,EAAE,OAAO,IAAI;IAE5B,oBACEZ,OAAA,CAAChE,KAAK;MAACwJ,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzB3F,OAAA,CAAClE,GAAG;QAAC0J,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzF3F,OAAA,CAACjE,UAAU;UAACiK,OAAO,EAAC,IAAI;UAAAL,QAAA,GACrB7E,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyC,KAAK,EAAC,KAAG,EAAC3C,UAAU,CAACqF,aAAa;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACb7D,OAAA,CAAClE,GAAG;UAAC0J,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE;UAAE,CAAE;UAAAP,QAAA,gBAEnC3F,OAAA,CAAC3D,MAAM;YACL8J,SAAS,eAAEnG,OAAA,CAAC3B,YAAY;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC9C,UAAU,EAAE,KAAK,CAAE;YAC3D8E,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZvC,KAAK,EAAC,SAAS;YAAA6B,QAAA,EAChB;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7D,OAAA,CAAC3D,MAAM;YACL8J,SAAS,eAAEnG,OAAA,CAAC3B,YAAY;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC9C,UAAU,EAAE,OAAO,CAAE;YAC7D8E,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZvC,KAAK,EAAC,SAAS;YAAA6B,QAAA,EAChB;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7D,OAAA,CAAC3D,MAAM;YACL8J,SAAS,eAAEnG,OAAA,CAACvB,WAAW;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BuC,OAAO,EAAEA,CAAA,KAAMvF,aAAa,CAAC,IAAI,CAAE;YACnCmF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YAAAV,QAAA,EACb;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7D,OAAA,CAACvD,OAAO;QAAC+I,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE;MAAE;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGzB3C,UAAU,KAAK,UAAU,IAAIoF,oBAAoB,CAAC1F,UAAU,CAAC,EAC7DM,UAAU,KAAK,KAAK,IAAIqF,eAAe,CAAC3F,UAAU,CAAC,EACnDM,UAAU,KAAK,QAAQ,IAAIsF,kBAAkB,CAAC5F,UAAU,CAAC,EACzDM,UAAU,KAAK,kBAAkB,IAAIuF,2BAA2B,CAAC7F,UAAU,CAAC,EAC5EM,UAAU,KAAK,cAAc,IAAIwF,uBAAuB,CAAC9F,UAAU,CAAC,EACpEM,UAAU,KAAK,YAAY,IAAIyF,qBAAqB,CAAC/F,UAAU,CAAC;IAAA;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC;EAEZ,CAAC;EAED,MAAMyC,oBAAoB,GAAIM,IAAI,iBAChC5G,OAAA,CAAC/D,IAAI;IAAC4K,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAnB,QAAA,gBACzB3F,OAAA,CAAC/D,IAAI;MAAC8K,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB3F,OAAA,CAAC3C,SAAS;QAAC4J,eAAe;QAAAtB,QAAA,gBACxB3F,OAAA,CAAC1C,gBAAgB;UAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C3F,OAAA,CAACjE,UAAU;YAACiK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAoB;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;UAAAoI,QAAA,eACf3F,OAAA,CAAC9D,IAAI;YAAAyJ,QAAA,eACH3F,OAAA,CAAC7D,WAAW;cAAAwJ,QAAA,gBACV3F,OAAA,CAACjE,UAAU;gBAAA4J,QAAA,GAAC,gBAAc,eAAA3F,OAAA;kBAAA2F,QAAA,GAASiB,IAAI,CAACO,YAAY,EAAC,GAAC;gBAAA;kBAAAzD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5E7D,OAAA,CAACjE,UAAU;gBAAA4J,QAAA,GAAC,gBAAc,eAAA3F,OAAA;kBAAA2F,QAAA,GAASiB,IAAI,CAACQ,YAAY,EAAC,GAAC;gBAAA;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5E7D,OAAA,CAACjE,UAAU;gBAAA4J,QAAA,GAAC,mBAAiB,eAAA3F,OAAA;kBAAA2F,QAAA,GAASiB,IAAI,CAACS,eAAe,EAAC,GAAC;gBAAA;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClF7D,OAAA,CAACjE,UAAU;gBAAA4J,QAAA,GAAC,eAAa,eAAA3F,OAAA;kBAAA2F,QAAA,GAASiB,IAAI,CAACU,uBAAuB,EAAC,GAAC;gBAAA;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEP7D,OAAA,CAAC/D,IAAI;MAAC8K,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB3F,OAAA,CAAC3C,SAAS;QAAC4J,eAAe;QAAAtB,QAAA,gBACxB3F,OAAA,CAAC1C,gBAAgB;UAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C3F,OAAA,CAACjE,UAAU;YAACiK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAI;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;UAAAoI,QAAA,eACf3F,OAAA,CAAC9D,IAAI;YAAAyJ,QAAA,eACH3F,OAAA,CAAC7D,WAAW;cAAAwJ,QAAA,gBACV3F,OAAA,CAACjE,UAAU;gBAAA4J,QAAA,GAAC,eAAa,eAAA3F,OAAA;kBAAA2F,QAAA,EAASiB,IAAI,CAACW;gBAAW;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzE7D,OAAA,CAACjE,UAAU;gBAAA4J,QAAA,GAAC,eAAa,eAAA3F,OAAA;kBAAA2F,QAAA,EAASiB,IAAI,CAACY;gBAAW;kBAAA9D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzE7D,OAAA,CAACjE,UAAU;gBAAA4J,QAAA,GAAC,kBAAgB,eAAA3F,OAAA;kBAAA2F,QAAA,EAASiB,IAAI,CAACa;gBAAc;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/E7D,OAAA,CAACjE,UAAU;gBAAA4J,QAAA,GAAC,oBAAkB,eAAA3F,OAAA;kBAAA2F,QAAA,GAASiB,IAAI,CAACc,gBAAgB,EAAC,GAAC;gBAAA;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEP7D,OAAA,CAAC/D,IAAI;MAAC8K,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB3F,OAAA,CAAC3C,SAAS;QAAC4J,eAAe;QAAAtB,QAAA,gBACxB3F,OAAA,CAAC1C,gBAAgB;UAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C3F,OAAA,CAACjE,UAAU;YAACiK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAW;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;UAAAoI,QAAA,eACf3F,OAAA,CAAC9D,IAAI;YAAAyJ,QAAA,eACH3F,OAAA,CAAC7D,WAAW;cAAAwJ,QAAA,gBACV3F,OAAA,CAACjE,UAAU;gBAAA4J,QAAA,GAAC,qBAAmB,eAAA3F,OAAA;kBAAA2F,QAAA,GAASiB,IAAI,CAACe,iBAAiB,EAAC,UAAQ;gBAAA;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAC5F+C,IAAI,CAACgB,cAAc,iBAClB5H,OAAA,CAAAE,SAAA;gBAAAyF,QAAA,gBACE3F,OAAA,CAACjE,UAAU;kBAAA4J,QAAA,GAAC,kBAAgB,eAAA3F,OAAA;oBAAA2F,QAAA,GAASiB,IAAI,CAACgB,cAAc,EAAC,SAAO;kBAAA;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtF7D,OAAA,CAACjE,UAAU;kBAAA4J,QAAA,GAAC,sBAAoB,eAAA3F,OAAA;oBAAA2F,QAAA,EAASiB,IAAI,CAACiB;kBAAkB;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA,eACvF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAEN+C,IAAI,CAACkB,YAAY,IAAIlB,IAAI,CAACkB,YAAY,CAACC,MAAM,GAAG,CAAC,iBAChD/H,OAAA,CAAC/D,IAAI;MAAC8K,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB3F,OAAA,CAAC3C,SAAS;QAAC4J,eAAe;QAAAtB,QAAA,gBACxB3F,OAAA,CAAC1C,gBAAgB;UAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C3F,OAAA,CAACjE,UAAU;YAACiK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAY;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;UAAAoI,QAAA,eACf3F,OAAA,CAAC9D,IAAI;YAAAyJ,QAAA,eACH3F,OAAA,CAAC7D,WAAW;cAAAwJ,QAAA,EACTiB,IAAI,CAACkB,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7CnI,OAAA,CAACjE,UAAU;gBAAA4J,QAAA,GACRuC,IAAI,CAACtB,IAAI,EAAC,IAAE,eAAA5G,OAAA;kBAAA2F,QAAA,GAASuC,IAAI,CAACE,KAAK,EAAC,GAAC;gBAAA;kBAAA1E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GAD5BsE,KAAK;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CACP;EAED,MAAM0C,eAAe,GAAIK,IAAI;IAAA,IAAAyB,mBAAA,EAAAC,qBAAA;IAAA,oBAC3BtI,OAAA,CAAC/D,IAAI;MAAC4K,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,gBACzB3F,OAAA,CAAC/D,IAAI;QAAC8K,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB3F,OAAA,CAAC3C,SAAS;UAAC4J,eAAe;UAAAtB,QAAA,gBACxB3F,OAAA,CAAC1C,gBAAgB;YAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C3F,OAAA,CAACjE,UAAU;cAACiK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;YAAAoI,QAAA,eACf3F,OAAA,CAAC/D,IAAI;cAAC4K,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnB,QAAA,GAAA0C,mBAAA,GACxBzB,IAAI,CAAC2B,aAAa,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoBJ,GAAG,CAAC,CAACO,IAAI,EAAEL,KAAK,kBACnCnI,OAAA,CAAC/D,IAAI;gBAAC8K,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACyB,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA/C,QAAA,eAC9B3F,OAAA,CAAC9D,IAAI;kBAAAyJ,QAAA,eACH3F,OAAA,CAAC7D,WAAW;oBAAAwJ,QAAA,gBACV3F,OAAA,CAACjE,UAAU;sBAACiK,OAAO,EAAC,WAAW;sBAAAL,QAAA,EAAE6C,IAAI,CAACG;oBAAS;sBAAAjF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC7D7D,OAAA,CAACjE,UAAU;sBAACiK,OAAO,EAAC,OAAO;sBAAAL,QAAA,GAAC,WAAS,EAAC6C,IAAI,CAACI,OAAO;oBAAA;sBAAAlF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAChE7D,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,GAAC,QAAM,EAAC6C,IAAI,CAACK,QAAQ;oBAAA;sBAAAnF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC9C7D,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,GAAC,iBAAe,EAAC6C,IAAI,CAACM,aAAa,EAAC,GAAC;oBAAA;sBAAApF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC7D7D,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,GAAC,eAAa,EAAC6C,IAAI,CAACO,WAAW,EAAC,GAAC;oBAAA;sBAAArF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzD7D,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,GAAC,aAAW,EAAC6C,IAAI,CAACnB,eAAe,EAAC,GAAC;oBAAA;sBAAA3D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAV6BsE,KAAK;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWrC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEP7D,OAAA,CAAC/D,IAAI;QAAC8K,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB3F,OAAA,CAAC3C,SAAS;UAAC4J,eAAe;UAAAtB,QAAA,gBACxB3F,OAAA,CAAC1C,gBAAgB;YAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C3F,OAAA,CAACjE,UAAU;cAACiK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;YAAAoI,QAAA,eACf3F,OAAA,CAAC/D,IAAI;cAAC4K,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnB,QAAA,GAAA2C,qBAAA,GACxB1B,IAAI,CAACoC,eAAe,cAAAV,qBAAA,uBAApBA,qBAAA,CAAsBL,GAAG,CAAC,CAACgB,MAAM,EAAEd,KAAK,kBACvCnI,OAAA,CAAC/D,IAAI;gBAAC8K,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACyB,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA/C,QAAA,eAC9B3F,OAAA,CAAC9D,IAAI;kBAAAyJ,QAAA,eACH3F,OAAA,CAAC7D,WAAW;oBAAAwJ,QAAA,gBACV3F,OAAA,CAACjE,UAAU;sBAACiK,OAAO,EAAC,WAAW;sBAAAL,QAAA,EAAEsD,MAAM,CAACN;oBAAS;sBAAAjF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC/D7D,OAAA,CAACjE,UAAU;sBAACiK,OAAO,EAAC,OAAO;sBAAAL,QAAA,GAAC,WAAS,EAACsD,MAAM,CAACL,OAAO;oBAAA;sBAAAlF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAClE7D,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,GAAC,UAAQ,EAACsD,MAAM,CAACC,UAAU;oBAAA;sBAAAxF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACpD7D,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,GAAC,qBAAmB,EAACsD,MAAM,CAACE,iBAAiB,EAAC,GAAC;oBAAA;sBAAAzF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAR6BsE,KAAK;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASrC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM2C,kBAAkB,GAAII,IAAI;IAAA,IAAAwC,YAAA;IAAA,oBAC9BpJ,OAAA,CAAC/D,IAAI;MAAC4K,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,eACzB3F,OAAA,CAAC/D,IAAI;QAAC8K,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB3F,OAAA,CAAC3C,SAAS;UAAC4J,eAAe;UAAAtB,QAAA,gBACxB3F,OAAA,CAAC1C,gBAAgB;YAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C3F,OAAA,CAACjE,UAAU;cAACiK,OAAO,EAAC,IAAI;cAAAL,QAAA,GAAC,uBACF,EAACiB,IAAI,CAACyC,aAAa,EAAC,UAC3C;YAAA;cAAA3F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;YAAAoI,QAAA,eACf3F,OAAA,CAAC/D,IAAI;cAAC4K,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnB,QAAA,GAAAyD,YAAA,GACxBxC,IAAI,CAAC9E,MAAM,cAAAsH,YAAA,uBAAXA,YAAA,CAAanB,GAAG,CAAC,CAACgB,MAAM,EAAEd,KAAK,kBAC9BnI,OAAA,CAAC/D,IAAI;gBAAC8K,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACyB,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA/C,QAAA,eAC9B3F,OAAA,CAAC9D,IAAI;kBAAAyJ,QAAA,eACH3F,OAAA,CAAC7D,WAAW;oBAAAwJ,QAAA,gBACV3F,OAAA,CAACjE,UAAU;sBAACiK,OAAO,EAAC,WAAW;sBAAAL,QAAA,EAAEsD,MAAM,CAACxH;oBAAS;sBAAAiC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC/D7D,OAAA,CAACjE,UAAU;sBAACiK,OAAO,EAAC,OAAO;sBAAAL,QAAA,GAAEsD,MAAM,CAACN,SAAS,EAAC,KAAG,EAACM,MAAM,CAACL,OAAO;oBAAA;sBAAAlF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC9E7D,OAAA,CAAC1D,IAAI;sBACHgN,KAAK,EAAEL,MAAM,CAACM,KAAM;sBACpBzF,KAAK,EAAEmF,MAAM,CAACM,KAAK,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;sBAC9DlD,IAAI,EAAC,OAAO;sBACZb,EAAE,EAAE;wBAAEO,EAAE,EAAE;sBAAE;oBAAE;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACF7D,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,GAAC,gBAAc,EAACsD,MAAM,CAAC9B,YAAY,EAAC,GAAC;oBAAA;sBAAAzD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC7D7D,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,GAAC,iBAAe,EAACsD,MAAM,CAACO,aAAa,EAAC,GAAC;oBAAA;sBAAA9F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC/D7D,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,GAAC,oBAAkB,EAACsD,MAAM,CAACQ,gBAAgB,EAAC,GAAC;oBAAA;sBAAA/F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACrE7D,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,GAAC,YAAU,EAACsD,MAAM,CAACS,oBAAoB,EAAC,GAAC;oBAAA;sBAAAhG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAhB6BsE,KAAK;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBrC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM4C,2BAA2B,GAAIG,IAAI;IAAA,IAAA+C,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,oBAAA;IAAA,oBACvCpK,OAAA,CAAC/D,IAAI;MAAC4K,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,gBACzB3F,OAAA,CAAC/D,IAAI;QAAC8K,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB3F,OAAA,CAAC3C,SAAS;UAAC4J,eAAe;UAAAtB,QAAA,gBACxB3F,OAAA,CAAC1C,gBAAgB;YAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C3F,OAAA,CAACjE,UAAU;cAACiK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAe;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;YAAAoI,QAAA,eACf3F,OAAA,CAAC9D,IAAI;cAAAyJ,QAAA,eACH3F,OAAA,CAAC7D,WAAW;gBAAAwJ,QAAA,gBACV3F,OAAA,CAACjE,UAAU;kBAAA4J,QAAA,GAAC,MAAI,eAAA3F,OAAA;oBAAA2F,QAAA,GAAAgE,YAAA,GAAS/C,IAAI,CAACqC,MAAM,cAAAU,YAAA,uBAAXA,YAAA,CAAalI;kBAAS;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtE7D,OAAA,CAACjE,UAAU;kBAAA4J,QAAA,GAAC,aAAW,eAAA3F,OAAA;oBAAA2F,QAAA,GAAAiE,aAAA,GAAShD,IAAI,CAACqC,MAAM,cAAAW,aAAA,uBAAXA,aAAA,CAAajB;kBAAS;oBAAAjF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7E7D,OAAA,CAACjE,UAAU;kBAAA4J,QAAA,GAAC,WAAS,eAAA3F,OAAA;oBAAA2F,QAAA,GAAAkE,aAAA,GAASjD,IAAI,CAACqC,MAAM,cAAAY,aAAA,uBAAXA,aAAA,CAAajB;kBAAO;oBAAAlF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzE7D,OAAA,CAAC1D,IAAI;kBACHgN,KAAK,GAAAQ,aAAA,GAAElD,IAAI,CAACqC,MAAM,cAAAa,aAAA,uBAAXA,aAAA,CAAaP,KAAM;kBAC1BzF,KAAK,EAAE,EAAAiG,aAAA,GAAAnD,IAAI,CAACqC,MAAM,cAAAc,aAAA,uBAAXA,aAAA,CAAaR,KAAK,MAAK,aAAa,GAAG,SAAS,GAAG,SAAU;kBACpE/D,EAAE,EAAE;oBAAE6E,EAAE,EAAE;kBAAE;gBAAE;kBAAA3G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACF7D,OAAA,CAACjE,UAAU;kBAAA4J,QAAA,GAAC,gBAAc,eAAA3F,OAAA;oBAAA2F,QAAA,IAAAqE,aAAA,GAASpD,IAAI,CAACqC,MAAM,cAAAe,aAAA,uBAAXA,aAAA,CAAa7C,YAAY,EAAC,GAAC;kBAAA;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpF7D,OAAA,CAACjE,UAAU;kBAAA4J,QAAA,GAAC,iBAAe,eAAA3F,OAAA;oBAAA2F,QAAA,IAAAsE,aAAA,GAASrD,IAAI,CAACqC,MAAM,cAAAgB,aAAA,uBAAXA,aAAA,CAAaT,aAAa,EAAC,GAAC;kBAAA;oBAAA9F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtF7D,OAAA,CAACjE,UAAU;kBAAA4J,QAAA,GAAC,oBAAkB,eAAA3F,OAAA;oBAAA2F,QAAA,IAAAuE,aAAA,GAAStD,IAAI,CAACqC,MAAM,cAAAiB,aAAA,uBAAXA,aAAA,CAAaT,gBAAgB,EAAC,GAAC;kBAAA;oBAAA/F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5F7D,OAAA,CAACjE,UAAU;kBAAA4J,QAAA,GAAC,YAAU,eAAA3F,OAAA;oBAAA2F,QAAA,IAAAwE,aAAA,GAASvD,IAAI,CAACqC,MAAM,cAAAkB,aAAA,uBAAXA,aAAA,CAAaT,oBAAoB,EAAC,GAAC;kBAAA;oBAAAhG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEP7D,OAAA,CAAC/D,IAAI;QAAC8K,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB3F,OAAA,CAAC3C,SAAS;UAAC4J,eAAe;UAAAtB,QAAA,gBACxB3F,OAAA,CAAC1C,gBAAgB;YAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C3F,OAAA,CAACjE,UAAU;cAACiK,OAAO,EAAC,IAAI;cAAAL,QAAA,GAAC,kBACP,EAACiB,IAAI,CAACW,WAAW,EAAC,GACpC;YAAA;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;YAAAoI,QAAA,eACf3F,OAAA,CAAC9D,IAAI;cAAAyJ,QAAA,eACH3F,OAAA,CAAC7D,WAAW;gBAAAwJ,QAAA,eACV3F,OAAA,CAAClE,GAAG;kBAAC0J,EAAE,EAAE;oBAAE8E,SAAS,EAAE,GAAG;oBAAEC,QAAQ,EAAE;kBAAO,CAAE;kBAAA5E,QAAA,GAAAyE,oBAAA,GAC3CxD,IAAI,CAAC4D,cAAc,cAAAJ,oBAAA,uBAAnBA,oBAAA,CAAqBnC,GAAG,CAAC,CAACO,IAAI,EAAEL,KAAK,kBACpCnI,OAAA,CAAClE,GAAG;oBAAa0J,EAAE,EAAE;sBAAEO,EAAE,EAAE,CAAC;sBAAEN,CAAC,EAAE,CAAC;sBAAEgF,MAAM,EAAE,mBAAmB;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAAA/E,QAAA,gBACjF3F,OAAA,CAACjE,UAAU;sBAACiK,OAAO,EAAC,OAAO;sBAAAL,QAAA,eAAC3F,OAAA;wBAAA2F,QAAA,EAAS6C,IAAI,CAACmC;sBAAO;wBAAAjH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACxE7D,OAAA,CAACjE,UAAU;sBAACiK,OAAO,EAAC,SAAS;sBAAAL,QAAA,GAC1B6C,IAAI,CAACoC,OAAO,EAAC,KAAG,EAACpC,IAAI,CAACqC,OAAO,EAAC,KAAG,EAACrC,IAAI,CAACG,SAAS;oBAAA;sBAAAjF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACb7D,OAAA,CAACjE,UAAU;sBAACiK,OAAO,EAAC,SAAS;sBAACJ,OAAO,EAAC,OAAO;sBAAAD,QAAA,GAAC,WACnC,EAAC6C,IAAI,CAACM,aAAa,EAAC,aAAW,EAACN,IAAI,CAACO,WAAW,EAAC,aAAW,EAACP,IAAI,CAACe,KAAK;oBAAA;sBAAA7F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA,GAPLsE,KAAK;oBAAAzE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM6C,uBAAuB,GAAIE,IAAI;IAAA,IAAAkE,qBAAA;IAAA,oBACnC9K,OAAA,CAAC/D,IAAI;MAAC4K,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,gBACzB3F,OAAA,CAAC/D,IAAI;QAAC8K,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB3F,OAAA,CAAC3C,SAAS;UAAC4J,eAAe;UAAAtB,QAAA,gBACxB3F,OAAA,CAAC1C,gBAAgB;YAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C3F,OAAA,CAACjE,UAAU;cAACiK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAmB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;YAAAoI,QAAA,eACf3F,OAAA,CAAC9D,IAAI;cAAAyJ,QAAA,eACH3F,OAAA,CAAC7D,WAAW;gBAAAwJ,QAAA,gBACV3F,OAAA,CAACjE,UAAU;kBAAA4J,QAAA,GAAC,WAAS,eAAA3F,OAAA;oBAAA2F,QAAA,GAASiB,IAAI,CAACrF,WAAW,EAAC,KAAG,EAACqF,IAAI,CAACpF,SAAS;kBAAA;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxF7D,OAAA,CAACjE,UAAU;kBAAA4J,QAAA,GAAC,gBAAc,eAAA3F,OAAA;oBAAA2F,QAAA,GAASiB,IAAI,CAACmE,oBAAoB,EAAC,GAAC;kBAAA;oBAAArH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpF7D,OAAA,CAACjE,UAAU;kBAAA4J,QAAA,GAAC,iBAAe,eAAA3F,OAAA;oBAAA2F,QAAA,EAASiB,IAAI,CAACoE;kBAAa;oBAAAtH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7E7D,OAAA,CAACjE,UAAU;kBAAA4J,QAAA,GAAC,qBAAmB,eAAA3F,OAAA;oBAAA2F,QAAA,GAASiB,IAAI,CAACe,iBAAiB,EAAC,UAAQ;kBAAA;oBAAAjE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEP7D,OAAA,CAAC/D,IAAI;QAAC8K,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB3F,OAAA,CAAC3C,SAAS;UAAC4J,eAAe;UAAAtB,QAAA,gBACxB3F,OAAA,CAAC1C,gBAAgB;YAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C3F,OAAA,CAACjE,UAAU;cAACiK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAgB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;YAAAoI,QAAA,eACf3F,OAAA,CAAC9D,IAAI;cAAAyJ,QAAA,eACH3F,OAAA,CAAC7D,WAAW;gBAAAwJ,QAAA,eACV3F,OAAA,CAAClE,GAAG;kBAAC0J,EAAE,EAAE;oBAAE8E,SAAS,EAAE,GAAG;oBAAEC,QAAQ,EAAE;kBAAO,CAAE;kBAAA5E,QAAA,GAAAmF,qBAAA,GAC3ClE,IAAI,CAACqE,gBAAgB,cAAAH,qBAAA,uBAArBA,qBAAA,CAAuB7C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACtCnI,OAAA,CAAClE,GAAG;oBAAa0J,EAAE,EAAE;sBAAEI,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAEqF,EAAE,EAAE;oBAAI,CAAE;oBAAAvF,QAAA,gBACjF3F,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,EAAEuC,IAAI,CAACtB;oBAAI;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACpC7D,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,eAAC3F,OAAA;wBAAA2F,QAAA,GAASuC,IAAI,CAACE,KAAK,EAAC,GAAC;sBAAA;wBAAA1E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA,GAF/CsE,KAAK;oBAAAzE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM8C,qBAAqB,GAAIC,IAAI;IAAA,IAAAuE,oBAAA;IAAA,oBACjCnL,OAAA,CAAC/D,IAAI;MAAC4K,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,eACzB3F,OAAA,CAAC/D,IAAI;QAAC8K,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB3F,OAAA,CAAC3C,SAAS;UAAC4J,eAAe;UAAAtB,QAAA,gBACxB3F,OAAA,CAAC1C,gBAAgB;YAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C3F,OAAA,CAACjE,UAAU;cAACiK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAA+B;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;YAAAoI,QAAA,eACf3F,OAAA,CAAC/D,IAAI;cAAC4K,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnB,QAAA,GAAAwF,oBAAA,GACxBvE,IAAI,CAACwE,cAAc,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBlD,GAAG,CAAC,CAACsB,KAAK,EAAEpB,KAAK,kBACrCnI,OAAA,CAAC/D,IAAI;gBAAC8K,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACyB,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA/C,QAAA,eAC9B3F,OAAA,CAAC9D,IAAI;kBAAAyJ,QAAA,eACH3F,OAAA,CAAC7D,WAAW;oBAAAwJ,QAAA,gBACV3F,OAAA,CAACjE,UAAU;sBAACiK,OAAO,EAAC,IAAI;sBAACqF,YAAY;sBAAA1F,QAAA,eACnC3F,OAAA,CAAC1D,IAAI;wBACHgN,KAAK,EAAEC,KAAK,CAACA,KAAM;wBACnBzF,KAAK,EAAEyF,KAAK,CAACA,KAAK,KAAK,YAAY,GAAG,SAAS,GAAG,SAAU;wBAC5D/D,EAAE,EAAE;0BAAEO,EAAE,EAAE;wBAAE;sBAAE;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eACb7D,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,GAAC,eAAa,eAAA3F,OAAA;wBAAA2F,QAAA,EAAS4D,KAAK,CAACV;sBAAQ;wBAAAnF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvE7D,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,GAAC,iBAAe,eAAA3F,OAAA;wBAAA2F,QAAA,GAAS4D,KAAK,CAACT,aAAa,EAAC,GAAC;sBAAA;wBAAApF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC/E7D,OAAA,CAACjE,UAAU;sBAAA4J,QAAA,GAAC,eAAa,eAAA3F,OAAA;wBAAA2F,QAAA,GAAS4D,KAAK,CAACR,WAAW,EAAC,GAAC;sBAAA;wBAAArF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAd6BsE,KAAK;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAerC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAMyH,YAAY,GAAGA,CAAA,kBACnBtL,OAAA,CAACpD,MAAM;IAAC8H,IAAI,EAAE1D,UAAW;IAACuK,OAAO,EAAEjG,iBAAkB;IAACkG,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAA9F,QAAA,gBAC3E3F,OAAA,CAACnD,WAAW;MAAA8I,QAAA,EACT7E,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyC;IAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACd7D,OAAA,CAAClD,aAAa;MAAA6I,QAAA,GACXjF,KAAK,iBACJV,OAAA,CAACzD,KAAK;QAACmP,QAAQ,EAAC,OAAO;QAAClG,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EACnCjF;MAAK;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAED7D,OAAA,CAAC/D,IAAI;QAAC4K,SAAS;QAACC,OAAO,EAAE,CAAE;QAACtB,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACxC3F,OAAA,CAAC/D,IAAI;UAAC8K,IAAI;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAChB3F,OAAA,CAAChD,WAAW;YAACyO,SAAS;YAAA9F,QAAA,gBACpB3F,OAAA,CAAC/C,UAAU;cAAA0I,QAAA,EAAC;YAAO;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChC7D,OAAA,CAAC9C,MAAM;cACLyO,KAAK,EAAEvK,QAAQ,CAACE,OAAQ;cACxBgI,KAAK,EAAC,SAAS;cACfsC,QAAQ,EAAGC,CAAC,IAAKxK,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAEuK,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAAAhG,QAAA,gBAEvE3F,OAAA,CAAC7C,QAAQ;gBAACwO,KAAK,EAAC,OAAO;gBAAAhG,QAAA,EAAC;cAAoB;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvD7D,OAAA,CAAC7C,QAAQ;gBAACwO,KAAK,EAAC,KAAK;gBAAAhG,QAAA,EAAC;cAAY;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7C7D,OAAA,CAAC7C,QAAQ;gBAACwO,KAAK,EAAC,OAAO;gBAAAhG,QAAA,EAAC;cAAc;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAEN3C,UAAU,KAAK,kBAAkB,iBAChClB,OAAA,CAAC/D,IAAI;UAAC8K,IAAI;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAChB3F,OAAA,CAAC5C,SAAS;YACRqO,SAAS;YACTnC,KAAK,EAAC,WAAW;YACjBqC,KAAK,EAAEvK,QAAQ,CAACK,SAAU;YAC1BmK,QAAQ,EAAGC,CAAC,IAAKxK,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEK,SAAS,EAAEoK,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzEI,WAAW,EAAC,mBAAmB;YAC/BC,UAAU,EAAC;UAA0D;YAAAtI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP,EAEA3C,UAAU,KAAK,cAAc,iBAC5BlB,OAAA,CAAAE,SAAA;UAAAyF,QAAA,gBACE3F,OAAA,CAAC/D,IAAI;YAAC8K,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACf3F,OAAA,CAAC5C,SAAS;cACRqO,SAAS;cACTQ,IAAI,EAAC,MAAM;cACX3C,KAAK,EAAC,aAAa;cACnBqC,KAAK,EAAEvK,QAAQ,CAACG,WAAY;cAC5BqK,QAAQ,EAAGC,CAAC,IAAKxK,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAEsK,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC3EO,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAzI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP7D,OAAA,CAAC/D,IAAI;YAAC8K,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACf3F,OAAA,CAAC5C,SAAS;cACRqO,SAAS;cACTQ,IAAI,EAAC,MAAM;cACX3C,KAAK,EAAC,WAAW;cACjBqC,KAAK,EAAEvK,QAAQ,CAACI,SAAU;cAC1BoK,QAAQ,EAAGC,CAAC,IAAKxK,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAEqK,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACzEO,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAzI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChB7D,OAAA,CAACjD,aAAa;MAAA4I,QAAA,gBACZ3F,OAAA,CAAC3D,MAAM;QAAC+J,OAAO,EAAEd,iBAAkB;QAAAK,QAAA,EAAC;MAAO;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpD7D,OAAA,CAAC3D,MAAM;QACL+J,OAAO,EAAEf,oBAAqB;QAC9BW,OAAO,EAAC,WAAW;QACnBoG,QAAQ,EAAE5L,OAAQ;QAClB2F,SAAS,EAAE3F,OAAO,gBAAGR,OAAA,CAACxD,gBAAgB;UAAC6J,IAAI,EAAE;QAAG;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG7D,OAAA,CAACzB,cAAc;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAA8B,QAAA,EAExEnF,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACE7D,OAAA,CAAClE,GAAG;IAAC0J,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAE,QAAA,gBAEhB3F,OAAA,CAAClE,GAAG;MAAC0J,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF3F,OAAA,CAAClE,GAAG;QAAC0J,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEI,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACzD3F,OAAA,CAACtD,UAAU;UAAC0J,OAAO,EAAEA,CAAA,KAAM/F,QAAQ,CAAC,CAAC,CAAC,CAAE;UAACyD,KAAK,EAAC,SAAS;UAAA6B,QAAA,eACtD3F,OAAA,CAACrB,aAAa;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACb7D,OAAA,CAACjE,UAAU;UAACiK,OAAO,EAAC,IAAI;UAACqG,SAAS,EAAC,IAAI;UAAA1G,QAAA,EAAC;QAExC;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN7D,OAAA,CAACP,eAAe;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGLrD,OAAO,iBACNR,OAAA,CAAClE,GAAG;MAAC0J,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEwE,EAAE,EAAE;MAAE,CAAE;MAAA1E,QAAA,eAC5D3F,OAAA,CAACxD,gBAAgB;QAAAkH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGD7D,OAAA,CAAClE,GAAG;MAAC0J,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAEjB3F,OAAA,CAAC3C,SAAS;QAAC4J,eAAe;QAACzB,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC3F,OAAA,CAAC1C,gBAAgB;UAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C3F,OAAA,CAAClE,GAAG;YAAC0J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD3F,OAAA,CAACrC,cAAc;cAAC6H,EAAE,EAAE;gBAAE8G,EAAE,EAAE,CAAC;gBAAExI,KAAK,EAAE;cAAe;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxD7D,OAAA,CAACjE,UAAU;cAACiK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;UAAAoI,QAAA,EACdjE,WAAW,CAACE,QAAQ,gBACnB5B,OAAA,CAAClE,GAAG;YAAA6J,QAAA,gBACF3F,OAAA,CAAClE,GAAG;cAAC0J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D3F,OAAA,CAAC3D,MAAM;gBACL8J,SAAS,eAAEnG,OAAA,CAAC3B,YAAY;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,UAAU,EAAE,KAAK,CAAE;gBAC3DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE8G,EAAE,EAAE;gBAAE,CAAE;gBAAA3G,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7D,OAAA,CAAC3D,MAAM;gBACL8J,SAAS,eAAEnG,OAAA,CAAC3B,YAAY;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAE;gBAC7DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLyC,oBAAoB,CAAC5E,WAAW,CAACE,QAAQ,CAAC;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,GACJrD,OAAO,gBACTR,OAAA,CAAClE,GAAG;YAAC0J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEuE,EAAE,EAAE;YAAE,CAAE;YAAA1E,QAAA,gBACxD3F,OAAA,CAACxD,gBAAgB;cAAC6J,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B7D,OAAA,CAACjE,UAAU;cAACyJ,EAAE,EAAE;gBAAE+G,EAAE,EAAE;cAAE,CAAE;cAAA5G,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN7D,OAAA,CAAClE,GAAG;YAAA6J,QAAA,gBACF3F,OAAA,CAACzD,KAAK;cAACmP,QAAQ,EAAC,OAAO;cAAClG,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7D,OAAA,CAAC3D,MAAM;cACL2J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAEnG,OAAA,CAACvB,WAAW;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb3F,UAAU,CAAC,IAAI,CAAC;gBAChBf,aAAa,CAAC0C,iBAAiB,CAAC9B,UAAU,EAAE,OAAO,CAAC,CACjDkM,IAAI,CAAC5F,IAAI,IAAI;kBACZjF,cAAc,CAAC4C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP3C,QAAQ,EAAEgF,IAAI,CAACpE;kBACjB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC7B,KAAK,CAAC,iCAAiC,EAAE4B,GAAG,CAAC;gBACvD,CAAC,CAAC,CACDmK,OAAO,CAAC,MAAM;kBACbhM,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAkF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ7D,OAAA,CAAC3C,SAAS;QAAC4J,eAAe;QAACzB,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC3F,OAAA,CAAC1C,gBAAgB;UAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C3F,OAAA,CAAClE,GAAG;YAAC0J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD3F,OAAA,CAAC7B,QAAQ;cAACqH,EAAE,EAAE;gBAAE8G,EAAE,EAAE,CAAC;gBAAExI,KAAK,EAAE;cAAiB;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD7D,OAAA,CAACjE,UAAU;cAACiK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;UAAAoI,QAAA,EACdjE,WAAW,CAACG,GAAG,gBACd7B,OAAA,CAAClE,GAAG;YAAA6J,QAAA,gBACF3F,OAAA,CAAClE,GAAG;cAAC0J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D3F,OAAA,CAAC3D,MAAM;gBACL8J,SAAS,eAAEnG,OAAA,CAAC3B,YAAY;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAE;gBACtDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE8G,EAAE,EAAE;gBAAE,CAAE;gBAAA3G,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7D,OAAA,CAAC3D,MAAM;gBACL8J,SAAS,eAAEnG,OAAA,CAAC3B,YAAY;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAE;gBACxDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL0C,eAAe,CAAC7E,WAAW,CAACG,GAAG,CAAC;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,GACJrD,OAAO,gBACTR,OAAA,CAAClE,GAAG;YAAC0J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEuE,EAAE,EAAE;YAAE,CAAE;YAAA1E,QAAA,gBACxD3F,OAAA,CAACxD,gBAAgB;cAAC6J,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B7D,OAAA,CAACjE,UAAU;cAACyJ,EAAE,EAAE;gBAAE+G,EAAE,EAAE;cAAE,CAAE;cAAA5G,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN7D,OAAA,CAAClE,GAAG;YAAA6J,QAAA,gBACF3F,OAAA,CAACzD,KAAK;cAACmP,QAAQ,EAAC,OAAO;cAAClG,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7D,OAAA,CAAC3D,MAAM;cACL2J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAEnG,OAAA,CAACvB,WAAW;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb3F,UAAU,CAAC,IAAI,CAAC;gBAChBf,aAAa,CAACgD,mBAAmB,CAACpC,UAAU,EAAE,OAAO,CAAC,CACnDkM,IAAI,CAAC5F,IAAI,IAAI;kBACZjF,cAAc,CAAC4C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP1C,GAAG,EAAE+E,IAAI,CAACpE;kBACZ,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC7B,KAAK,CAAC,4BAA4B,EAAE4B,GAAG,CAAC;gBAClD,CAAC,CAAC,CACDmK,OAAO,CAAC,MAAM;kBACbhM,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAkF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ7D,OAAA,CAAC3C,SAAS;QAAC4J,eAAe;QAACzB,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC3F,OAAA,CAAC1C,gBAAgB;UAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C3F,OAAA,CAAClE,GAAG;YAAC0J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD3F,OAAA,CAACf,aAAa;cAACuG,EAAE,EAAE;gBAAE8G,EAAE,EAAE,CAAC;gBAAExI,KAAK,EAAE;cAAe;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvD7D,OAAA,CAACjE,UAAU;cAACiK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAsB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;UAAAoI,QAAA,EACdjE,WAAW,CAACI,MAAM,gBACjB9B,OAAA,CAAClE,GAAG;YAAA6J,QAAA,gBACF3F,OAAA,CAAClE,GAAG;cAAC0J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D3F,OAAA,CAAC3D,MAAM;gBACL8J,SAAS,eAAEnG,OAAA,CAAC3B,YAAY;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,QAAQ,EAAE,KAAK,CAAE;gBACzDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE8G,EAAE,EAAE;gBAAE,CAAE;gBAAA3G,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7D,OAAA,CAAC3D,MAAM;gBACL8J,SAAS,eAAEnG,OAAA,CAAC3B,YAAY;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,QAAQ,EAAE,OAAO,CAAE;gBAC3DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL2C,kBAAkB,CAAC9E,WAAW,CAACI,MAAM,CAAC;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,GACJrD,OAAO,gBACTR,OAAA,CAAClE,GAAG;YAAC0J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEuE,EAAE,EAAE;YAAE,CAAE;YAAA1E,QAAA,gBACxD3F,OAAA,CAACxD,gBAAgB;cAAC6J,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B7D,OAAA,CAACjE,UAAU;cAACyJ,EAAE,EAAE;gBAAE+G,EAAE,EAAE;cAAE,CAAE;cAAA5G,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN7D,OAAA,CAAClE,GAAG;YAAA6J,QAAA,gBACF3F,OAAA,CAACzD,KAAK;cAACmP,QAAQ,EAAC,OAAO;cAAClG,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7D,OAAA,CAAC3D,MAAM;cACL2J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAEnG,OAAA,CAACvB,WAAW;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb3F,UAAU,CAAC,IAAI,CAAC;gBAChBf,aAAa,CAACkD,eAAe,CAACtC,UAAU,EAAE,OAAO,CAAC,CAC/CkM,IAAI,CAAC5F,IAAI,IAAI;kBACZjF,cAAc,CAAC4C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACPzC,MAAM,EAAE8E,IAAI,CAACpE;kBACf,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC7B,KAAK,CAAC,+BAA+B,EAAE4B,GAAG,CAAC;gBACrD,CAAC,CAAC,CACDmK,OAAO,CAAC,MAAM;kBACbhM,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAkF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ7D,OAAA,CAAC3C,SAAS;QAAC4J,eAAe;QAACzB,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC3F,OAAA,CAAC1C,gBAAgB;UAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C3F,OAAA,CAAClE,GAAG;YAAC0J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD3F,OAAA,CAACnC,YAAY;cAAC2H,EAAE,EAAE;gBAAE8G,EAAE,EAAE,CAAC;gBAAExI,KAAK,EAAE;cAAa;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD7D,OAAA,CAACjE,UAAU;cAACiK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAqB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;UAAAoI,QAAA,EACdjE,WAAW,CAACK,SAAS,gBACpB/B,OAAA,CAAClE,GAAG;YAAA6J,QAAA,gBACF3F,OAAA,CAAClE,GAAG;cAAC0J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D3F,OAAA,CAAC3D,MAAM;gBACL8J,SAAS,eAAEnG,OAAA,CAAC3B,YAAY;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,YAAY,EAAE,KAAK,CAAE;gBAC7DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE8G,EAAE,EAAE;gBAAE,CAAE;gBAAA3G,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7D,OAAA,CAAC3D,MAAM;gBACL8J,SAAS,eAAEnG,OAAA,CAAC3B,YAAY;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,YAAY,EAAE,OAAO,CAAE;gBAC/DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL8C,qBAAqB,CAACjF,WAAW,CAACK,SAAS,CAAC;UAAA;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,GACJrD,OAAO,gBACTR,OAAA,CAAClE,GAAG;YAAC0J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAEuE,EAAE,EAAE;YAAE,CAAE;YAAA1E,QAAA,gBACxD3F,OAAA,CAACxD,gBAAgB;cAAC6J,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B7D,OAAA,CAACjE,UAAU;cAACyJ,EAAE,EAAE;gBAAE+G,EAAE,EAAE;cAAE,CAAE;cAAA5G,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN7D,OAAA,CAAClE,GAAG;YAAA6J,QAAA,gBACF3F,OAAA,CAACzD,KAAK;cAACmP,QAAQ,EAAC,OAAO;cAAClG,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR7D,OAAA,CAAC3D,MAAM;cACL2J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAEnG,OAAA,CAACvB,WAAW;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb3F,UAAU,CAAC,IAAI,CAAC;gBAChBf,aAAa,CAACoD,kBAAkB,CAACxC,UAAU,EAAE,OAAO,CAAC,CAClDkM,IAAI,CAAC5F,IAAI,IAAI;kBACZjF,cAAc,CAAC4C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACPxC,SAAS,EAAE6E,IAAI,CAACpE;kBAClB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC7B,KAAK,CAAC,mCAAmC,EAAE4B,GAAG,CAAC;gBACzD,CAAC,CAAC,CACDmK,OAAO,CAAC,MAAM;kBACbhM,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAkF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ7D,OAAA,CAAChE,KAAK;QAACwJ,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACzB3F,OAAA,CAACjE,UAAU;UAACiK,OAAO,EAAC,IAAI;UAACqF,YAAY;UAAA1F,QAAA,EAAC;QAAe;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAClE7D,OAAA,CAACjE,UAAU;UAACiK,OAAO,EAAC,OAAO;UAAClC,KAAK,EAAC,gBAAgB;UAAC0B,EAAE,EAAE;YAAEO,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAElE;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7D,OAAA,CAAC/D,IAAI;UAAC4K,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAnB,QAAA,gBAEzB3F,OAAA,CAAC/D,IAAI;YAAC8K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACyB,EAAE,EAAE,CAAE;YAAA9C,QAAA,eACvB3F,OAAA,CAAC9D,IAAI;cAAAyJ,QAAA,gBACH3F,OAAA,CAAC7D,WAAW;gBAAAwJ,QAAA,gBACV3F,OAAA,CAACjE,UAAU;kBAACiK,OAAO,EAAC,IAAI;kBAAAL,QAAA,EAAC;gBAAuB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7D7D,OAAA,CAACjE,UAAU;kBAACiK,OAAO,EAAC,OAAO;kBAAClC,KAAK,EAAC,gBAAgB;kBAAC0B,EAAE,EAAE;oBAAEO,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAElE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACd7D,OAAA,CAAC5D,WAAW;gBAAAuJ,QAAA,eACV3F,OAAA,CAAC3D,MAAM;kBACLoP,SAAS;kBACTzF,OAAO,EAAC,UAAU;kBAClBlC,KAAK,EAAC,MAAM;kBACZsC,OAAO,EAAEA,CAAA,KAAM;oBACbjF,aAAa,CAAC,kBAAkB,CAAC;oBACjCF,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBAAA0E,QAAA,EACH;gBAED;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGP7D,OAAA,CAAC/D,IAAI;YAAC8K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACyB,EAAE,EAAE,CAAE;YAAA9C,QAAA,eACvB3F,OAAA,CAAC9D,IAAI;cAAAyJ,QAAA,gBACH3F,OAAA,CAAC7D,WAAW;gBAAAwJ,QAAA,gBACV3F,OAAA,CAACjE,UAAU;kBAACiK,OAAO,EAAC,IAAI;kBAAAL,QAAA,EAAC;gBAAuB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7D7D,OAAA,CAACjE,UAAU;kBAACiK,OAAO,EAAC,OAAO;kBAAClC,KAAK,EAAC,gBAAgB;kBAAC0B,EAAE,EAAE;oBAAEO,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAElE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACd7D,OAAA,CAAC5D,WAAW;gBAAAuJ,QAAA,eACV3F,OAAA,CAAC3D,MAAM;kBACLoP,SAAS;kBACTzF,OAAO,EAAC,UAAU;kBAClBlC,KAAK,EAAC,SAAS;kBACfsC,OAAO,EAAEA,CAAA,KAAM;oBACbjF,aAAa,CAAC,cAAc,CAAC;oBAC7B;oBACA,MAAM2D,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;oBACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;oBAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;oBAExC7D,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXG,WAAW,EAAEyD,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;sBAClD5D,SAAS,EAAEsD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC7C,CAAC,CAAC;oBACFnE,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBAAA0E,QAAA,EACH;gBAED;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGPnC,WAAW,CAACM,eAAe,iBAC1BhC,OAAA,CAAC3C,SAAS;QAAC4J,eAAe;QAACzB,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAEL,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBAC9C3F,OAAA,CAAC1C,gBAAgB;UAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C3F,OAAA,CAAClE,GAAG;YAAC0J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD3F,OAAA,CAACjB,SAAS;cAACyG,EAAE,EAAE;gBAAE8G,EAAE,EAAE,CAAC;gBAAExI,KAAK,EAAE;cAAY;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD7D,OAAA,CAACjE,UAAU;cAACiK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;UAAAoI,QAAA,eACf3F,OAAA,CAAClE,GAAG;YAAA6J,QAAA,gBACF3F,OAAA,CAAClE,GAAG;cAAC0J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D3F,OAAA,CAAC3D,MAAM;gBACL8J,SAAS,eAAEnG,OAAA,CAAC3B,YAAY;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,kBAAkB,EAAE,KAAK,CAAE;gBACnEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE8G,EAAE,EAAE;gBAAE,CAAE;gBAAA3G,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7D,OAAA,CAAC3D,MAAM;gBACL8J,SAAS,eAAEnG,OAAA,CAAC3B,YAAY;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,kBAAkB,EAAE,OAAO,CAAE;gBACrEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL4C,2BAA2B,CAAC/E,WAAW,CAACM,eAAe,CAAC;UAAA;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACZ,EAEAnC,WAAW,CAACO,WAAW,iBACtBjC,OAAA,CAAC3C,SAAS;QAAC4J,eAAe;QAACzB,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAEL,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBAC9C3F,OAAA,CAAC1C,gBAAgB;UAAC4J,UAAU,eAAElH,OAAA,CAACb,cAAc;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C3F,OAAA,CAAClE,GAAG;YAAC0J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD3F,OAAA,CAAC/B,YAAY;cAACuH,EAAE,EAAE;gBAAE8G,EAAE,EAAE,CAAC;gBAAExI,KAAK,EAAE;cAAe;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtD7D,OAAA,CAACjE,UAAU;cAACiK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB7D,OAAA,CAACzC,gBAAgB;UAAAoI,QAAA,eACf3F,OAAA,CAAClE,GAAG;YAAA6J,QAAA,gBACF3F,OAAA,CAAClE,GAAG;cAAC0J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D3F,OAAA,CAAC3D,MAAM;gBACL8J,SAAS,eAAEnG,OAAA,CAAC3B,YAAY;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,cAAc,EAAE,KAAK,CAAE;gBAC/DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE8G,EAAE,EAAE;gBAAE,CAAE;gBAAA3G,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7D,OAAA,CAAC3D,MAAM;gBACL8J,SAAS,eAAEnG,OAAA,CAAC3B,YAAY;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAE;gBACjEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL6C,uBAAuB,CAAChF,WAAW,CAACO,WAAW,CAAC;UAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACZ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLyH,YAAY,CAAC,CAAC;EAAA;IAAA5H,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAACzD,EAAA,CA5oCID,iBAAiB;EAAA,QACJb,WAAW,EACLC,SAAS,EACfC,OAAO;AAAA;AAAAkN,EAAA,GAHpBvM,iBAAiB;AA8oCvB,eAAeA,iBAAiB;AAAC,IAAAuM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}