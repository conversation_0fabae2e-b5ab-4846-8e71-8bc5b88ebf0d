{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CollegamentiCavo.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, TextField, List, ListItem, ListItemText, Divider, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, FormControlLabel, Radio, RadioGroup, CircularProgress, Alert, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CollegamentiCavo = ({\n  cantiereId,\n  selectedCavo = null,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [cavi, setCavi] = useState([]);\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [internalSelectedCavo, setInternalSelectedCavo] = useState(selectedCavo);\n  const [openDialog, setOpenDialog] = useState(!!selectedCavo);\n  const [formData, setFormData] = useState({\n    lato: 'partenza',\n    responsabile: 'cantiere'\n  });\n\n  // Carica i cavi installati\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n  const loadCavi = async () => {\n    try {\n      setLoading(true);\n      console.log(`Caricamento cavi installati per cantiere ${cantiereId}...`);\n\n      // Ottieni solo i cavi installati\n      const response = await caviService.getCaviInstallati(cantiereId);\n      if (response && Array.isArray(response)) {\n        console.log(`Ricevuti ${response.length} cavi installati`);\n        setCavi(response);\n        setFilteredCavi(response);\n        if (response.length === 0) {\n          console.log('Nessun cavo installato trovato per questo cantiere');\n          // Non mostriamo un errore qui, l'interfaccia mostrerà già un messaggio appropriato\n        }\n      } else {\n        console.error('Risposta non valida dal server:', response);\n        setCavi([]);\n        setFilteredCavi([]);\n        onError('Formato risposta non valido dal server. Contattare l\\'amministratore.');\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      setCavi([]);\n      setFilteredCavi([]);\n\n      // Messaggio di errore più dettagliato e user-friendly\n      let errorMessage = 'Errore nel caricamento dei cavi: ';\n      if (error.detail) {\n        errorMessage += error.detail;\n      } else if (error.message) {\n        // Rimuovi dettagli tecnici dal messaggio di errore\n        const cleanMessage = error.message.replace(/http:\\/\\/localhost:\\d+/g, 'server').replace(/network error/i, 'errore di connessione');\n        errorMessage += cleanMessage;\n      } else {\n        errorMessage += 'Errore sconosciuto';\n      }\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const handleSearch = event => {\n    const term = event.target.value;\n    setSearchTerm(term);\n    if (!term.trim()) {\n      setFilteredCavi(cavi);\n    } else {\n      const filtered = cavi.filter(cavo => cavo.id_cavo.toLowerCase().includes(term.toLowerCase()));\n      setFilteredCavi(filtered);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    setInternalSelectedCavo(cavo);\n    setOpenDialog(true);\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce il salvataggio del collegamento\n  const handleSaveCollegamento = async () => {\n    try {\n      setLoading(true);\n\n      // Determina se il lato è già collegato\n      const collegamenti = internalSelectedCavo.collegamenti || 0;\n      const latoPartenzaCollegato = (collegamenti & 1) === 1;\n      const latoArrivoCollegato = (collegamenti & 2) === 2;\n      if (formData.lato === 'entrambi') {\n        // Gestione di entrambi i lati - obiettivo: collegare entrambi i lati\n        let operazioniEffettuate = [];\n\n        // Collega lato partenza se non è già collegato\n        if (!latoPartenzaCollegato) {\n          await caviService.collegaCavo(cantiereId, internalSelectedCavo.id_cavo, 'partenza', formData.responsabile);\n          operazioniEffettuate.push('Lato partenza collegato');\n        }\n\n        // Collega lato arrivo se non è già collegato\n        if (!latoArrivoCollegato) {\n          await caviService.collegaCavo(cantiereId, internalSelectedCavo.id_cavo, 'arrivo', formData.responsabile);\n          operazioniEffettuate.push('Lato arrivo collegato');\n        }\n\n        // Se entrambi erano già collegati, scollegali\n        if (latoPartenzaCollegato && latoArrivoCollegato) {\n          await caviService.scollegaCavo(cantiereId, internalSelectedCavo.id_cavo, 'partenza');\n          await caviService.scollegaCavo(cantiereId, internalSelectedCavo.id_cavo, 'arrivo');\n          onSuccess(`Cavo ${internalSelectedCavo.id_cavo}: Entrambi i lati scollegati`);\n        } else if (operazioniEffettuate.length > 0) {\n          onSuccess(`Cavo ${internalSelectedCavo.id_cavo}: ${operazioniEffettuate.join(', ')}`);\n        }\n      } else {\n        // Gestione di un singolo lato (logica originale)\n        if (formData.lato === 'partenza' && latoPartenzaCollegato || formData.lato === 'arrivo' && latoArrivoCollegato) {\n          // Se è già collegato, chiedi se vuole scollegarlo\n          await caviService.scollegaCavo(cantiereId, internalSelectedCavo.id_cavo, formData.lato);\n          onSuccess(`Lato ${formData.lato} del cavo ${internalSelectedCavo.id_cavo} scollegato con successo`);\n        } else {\n          // Altrimenti collega il lato\n          await caviService.collegaCavo(cantiereId, internalSelectedCavo.id_cavo, formData.lato, formData.responsabile);\n          onSuccess(`Lato ${formData.lato} del cavo ${internalSelectedCavo.id_cavo} collegato con successo`);\n        }\n      }\n\n      // Ricarica i cavi per aggiornare lo stato\n      await loadCavi();\n      setOpenDialog(false);\n      setInternalSelectedCavo(null);\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Formatta lo stato dei collegamenti\n  const formatStatoCollegamenti = collegamenti => {\n    collegamenti = collegamenti || 0;\n    if (collegamenti === 0) return \"Non collegato\";\n    if (collegamenti === 1) return \"Solo partenza\";\n    if (collegamenti === 2) return \"Solo arrivo\";\n    if (collegamenti === 3) return \"Completo\";\n    return `Sconosciuto (${collegamenti})`;\n  };\n\n  // Formatta lo stato di un singolo lato\n  const formatStatoLato = (collegamenti, lato) => {\n    collegamenti = collegamenti || 0;\n    if (lato === 'partenza') {\n      return collegamenti & 1 ? \"Collegato\" : \"Non collegato\";\n    } else {\n      return collegamenti & 2 ? \"Collegato\" : \"Non collegato\";\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [!selectedCavo && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        paragraph: true,\n        children: \"Visualizza e gestisci i collegamenti dei cavi installati.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        label: \"Cerca cavo per ID\",\n        variant: \"outlined\",\n        fullWidth: true,\n        margin: \"normal\",\n        value: searchTerm,\n        onChange: handleSearch,\n        placeholder: \"Inserisci l'ID del cavo da cercare\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: loadCavi,\n        disabled: loading,\n        sx: {\n          mt: 2,\n          mb: 3\n        },\n        children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 24\n        }, this) : \"Aggiorna lista\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this) : filteredCavi.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: searchTerm ? `Nessun cavo installato trovato con ID contenente \"${searchTerm}\".` : \"Nessun cavo installato trovato in questo cantiere.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 2,\n            mb: 1\n          },\n          children: \"Possibili motivi:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Non ci sono cavi nello stato \\\"INSTALLATO\\\" in questo cantiere.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Il cavo che stai cercando potrebbe essere in uno stato diverso da \\\"INSTALLATO\\\" (es. \\\"DA INSTALLARE\\\", \\\"POSATO\\\").\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Il cavo potrebbe essere marcato come SPARE.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 2\n          },\n          children: \"Suggerimenti:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Verifica lo stato del cavo nella pagina di gestione cavi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Assicurati che il cavo sia stato installato correttamente.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Lato Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Lato Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Resp. Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Resp. Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: filteredCavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatStatoCollegamenti(cavo.collegamenti)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatStatoLato(cavo.collegamenti, 'partenza')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatStatoLato(cavo.collegamenti, 'arrivo')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.responsabile_partenza || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.responsabile_arrivo || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  size: \"small\",\n                  onClick: () => handleCavoSelect(cavo),\n                  children: \"Gestisci\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 21\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: () => setOpenDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Gestione Collegamenti Cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: internalSelectedCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: [\"Cavo selezionato: \", internalSelectedCavo.id_cavo]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              my: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Informazioni Cavo:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Partenza (FROM): \", internalSelectedCavo.ubicazione_partenza || 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Arrivo (TO): \", internalSelectedCavo.ubicazione_arrivo || 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              my: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Stato Collegamenti:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Lato Partenza: \", formatStatoLato(internalSelectedCavo.collegamenti, 'partenza'), internalSelectedCavo.collegamenti & 1 ? ` (Responsabile: ${internalSelectedCavo.responsabile_partenza || 'N/A'})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Lato Arrivo: \", formatStatoLato(internalSelectedCavo.collegamenti, 'arrivo'), internalSelectedCavo.collegamenti & 2 ? ` (Responsabile: ${internalSelectedCavo.responsabile_arrivo || 'N/A'})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            sx: {\n              my: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Gestisci Collegamenti:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            component: \"fieldset\",\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: \"Seleziona l'operazione da eseguire:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n              name: \"lato\",\n              value: formData.lato,\n              onChange: handleFormChange,\n              row: true,\n              children: [!latoPartenzaCollegato && /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"partenza\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 32\n                }, this),\n                label: \"Collega Lato Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 21\n              }, this), !latoArrivoCollegato && /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"arrivo\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 32\n                }, this),\n                label: \"Collega Lato Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 21\n              }, this), (!latoPartenzaCollegato || !latoArrivoCollegato) && /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"entrambi\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 32\n                }, this),\n                label: \"Collega Entrambi i lati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 21\n              }, this), latoPartenzaCollegato && /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"partenza\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 32\n                }, this),\n                label: \"Scollega Lato Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 21\n              }, this), latoArrivoCollegato && /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"arrivo\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 32\n                }, this),\n                label: \"Scollega Lato Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 21\n              }, this), latoPartenzaCollegato && latoArrivoCollegato && /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                value: \"entrambi\",\n                control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 32\n                }, this),\n                label: \"Scollega Entrambi i lati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"dense\",\n            name: \"responsabile\",\n            label: \"Responsabile del collegamento\",\n            fullWidth: true,\n            variant: \"outlined\",\n            value: formData.responsabile,\n            onChange: handleFormChange,\n            sx: {\n              mt: 2\n            },\n            helperText: \"Lascia vuoto per usare 'cantiere' come valore predefinito\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"primary\",\n            sx: {\n              mt: 2\n            },\n            children: formData.lato === 'entrambi' ? (() => {\n              const collegamenti = internalSelectedCavo.collegamenti || 0;\n              const latoPartenzaCollegato = (collegamenti & 1) === 1;\n              const latoArrivoCollegato = (collegamenti & 2) === 2;\n              if (latoPartenzaCollegato && latoArrivoCollegato) {\n                return \"Entrambi i lati sono collegati. Procedendo verranno scollegati entrambi.\";\n              } else if (latoPartenzaCollegato) {\n                return \"Il lato partenza è già collegato. Verrà collegato solo il lato arrivo.\";\n              } else if (latoArrivoCollegato) {\n                return \"Il lato arrivo è già collegato. Verrà collegato solo il lato partenza.\";\n              } else {\n                return \"Procedendo verranno collegati entrambi i lati.\";\n              }\n            })() : formData.lato === 'partenza' && internalSelectedCavo.collegamenti & 1 ? \"Attenzione: Il lato partenza è già collegato. Procedendo verrà scollegato.\" : formData.lato === 'arrivo' && internalSelectedCavo.collegamenti & 2 ? \"Attenzione: Il lato arrivo è già collegato. Procedendo verrà scollegato.\" : `Procedendo verrà collegato il lato ${formData.lato}.`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setOpenDialog(false);\n            // Chiama onSuccess con null per chiudere anche il dialog padre\n            if (onSuccess) {\n              onSuccess(null);\n            }\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSaveCollegamento,\n          disabled: loading || !internalSelectedCavo,\n          variant: \"contained\",\n          color: \"primary\",\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 24\n          }, this) : formData.lato === 'entrambi' ? \"Gestisci Entrambi\" : formData.lato === 'partenza' && (internalSelectedCavo === null || internalSelectedCavo === void 0 ? void 0 : internalSelectedCavo.collegamenti) & 1 || formData.lato === 'arrivo' && (internalSelectedCavo === null || internalSelectedCavo === void 0 ? void 0 : internalSelectedCavo.collegamenti) & 2 ? \"Scollega\" : \"Collega\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 219,\n    columnNumber: 5\n  }, this);\n};\n_s(CollegamentiCavo, \"KRTAOdb+Gkj+YGFNCM8lcI7XxAM=\");\n_c = CollegamentiCavo;\nexport default CollegamentiCavo;\nvar _c;\n$RefreshReg$(_c, \"CollegamentiCavo\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "TextField", "List", "ListItem", "ListItemText", "Divider", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "FormControlLabel", "Radio", "RadioGroup", "CircularProgress", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "caviService", "jsxDEV", "_jsxDEV", "CollegamentiCavo", "cantiereId", "selected<PERSON><PERSON><PERSON>", "onSuccess", "onError", "_s", "loading", "setLoading", "cavi", "<PERSON><PERSON><PERSON>", "filteredCavi", "setFilteredCavi", "searchTerm", "setSearchTerm", "internalSelectedCavo", "setInternalSelectedCavo", "openDialog", "setOpenDialog", "formData", "setFormData", "lato", "responsabile", "loadCavi", "console", "log", "response", "getCaviInstallati", "Array", "isArray", "length", "error", "errorMessage", "detail", "message", "cleanMessage", "replace", "handleSearch", "event", "term", "target", "value", "trim", "filtered", "filter", "cavo", "id_cavo", "toLowerCase", "includes", "handleCavoSelect", "handleFormChange", "e", "name", "handleSaveCollegamento", "colle<PERSON>nti", "latoPartenzaCollegato", "latoArrivoCollegato", "operazioniEffettuate", "collegaCavo", "push", "scollegaCavo", "join", "formatStatoCollegamenti", "formatStatoLato", "children", "sx", "p", "mb", "variant", "paragraph", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "fullWidth", "margin", "onChange", "placeholder", "color", "onClick", "disabled", "mt", "size", "display", "justifyContent", "my", "severity", "component", "map", "responsabile_partenza", "responsabile_arrivo", "open", "onClose", "max<PERSON><PERSON><PERSON>", "gutterBottom", "ubicazione_partenza", "ubicazione_arrivo", "row", "control", "helperText", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CollegamentiCavo.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  TextField,\n  List,\n  ListItem,\n  ListItemText,\n  Divider,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  FormControlLabel,\n  Radio,\n  RadioGroup,\n  CircularProgress,\n  Alert,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow\n} from '@mui/material';\nimport caviService from '../../services/caviService';\n\nconst CollegamentiCavo = ({ cantiereId, selectedCavo = null, onSuccess, onError }) => {\n  const [loading, setLoading] = useState(false);\n  const [cavi, setCavi] = useState([]);\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [internalSelectedCavo, setInternalSelectedCavo] = useState(selectedCavo);\n  const [openDialog, setOpenDialog] = useState(!!selectedCavo);\n  const [formData, setFormData] = useState({\n    lato: 'partenza',\n    responsabile: 'cantiere'\n  });\n\n  // Carica i cavi installati\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  const loadCavi = async () => {\n    try {\n      setLoading(true);\n      console.log(`Caricamento cavi installati per cantiere ${cantiereId}...`);\n\n      // Ottieni solo i cavi installati\n      const response = await caviService.getCaviInstallati(cantiereId);\n\n      if (response && Array.isArray(response)) {\n        console.log(`Ricevuti ${response.length} cavi installati`);\n        setCavi(response);\n        setFilteredCavi(response);\n\n        if (response.length === 0) {\n          console.log('Nessun cavo installato trovato per questo cantiere');\n          // Non mostriamo un errore qui, l'interfaccia mostrerà già un messaggio appropriato\n        }\n      } else {\n        console.error('Risposta non valida dal server:', response);\n        setCavi([]);\n        setFilteredCavi([]);\n        onError('Formato risposta non valido dal server. Contattare l\\'amministratore.');\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      setCavi([]);\n      setFilteredCavi([]);\n\n      // Messaggio di errore più dettagliato e user-friendly\n      let errorMessage = 'Errore nel caricamento dei cavi: ';\n\n      if (error.detail) {\n        errorMessage += error.detail;\n      } else if (error.message) {\n        // Rimuovi dettagli tecnici dal messaggio di errore\n        const cleanMessage = error.message\n          .replace(/http:\\/\\/localhost:\\d+/g, 'server')\n          .replace(/network error/i, 'errore di connessione');\n        errorMessage += cleanMessage;\n      } else {\n        errorMessage += 'Errore sconosciuto';\n      }\n\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const handleSearch = (event) => {\n    const term = event.target.value;\n    setSearchTerm(term);\n\n    if (!term.trim()) {\n      setFilteredCavi(cavi);\n    } else {\n      const filtered = cavi.filter(cavo => \n        cavo.id_cavo.toLowerCase().includes(term.toLowerCase())\n      );\n      setFilteredCavi(filtered);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setInternalSelectedCavo(cavo);\n    setOpenDialog(true);\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce il salvataggio del collegamento\n  const handleSaveCollegamento = async () => {\n    try {\n      setLoading(true);\n\n      // Determina se il lato è già collegato\n      const collegamenti = internalSelectedCavo.collegamenti || 0;\n      const latoPartenzaCollegato = (collegamenti & 1) === 1;\n      const latoArrivoCollegato = (collegamenti & 2) === 2;\n\n      if (formData.lato === 'entrambi') {\n        // Gestione di entrambi i lati - obiettivo: collegare entrambi i lati\n        let operazioniEffettuate = [];\n\n        // Collega lato partenza se non è già collegato\n        if (!latoPartenzaCollegato) {\n          await caviService.collegaCavo(cantiereId, internalSelectedCavo.id_cavo, 'partenza', formData.responsabile);\n          operazioniEffettuate.push('Lato partenza collegato');\n        }\n\n        // Collega lato arrivo se non è già collegato\n        if (!latoArrivoCollegato) {\n          await caviService.collegaCavo(cantiereId, internalSelectedCavo.id_cavo, 'arrivo', formData.responsabile);\n          operazioniEffettuate.push('Lato arrivo collegato');\n        }\n\n        // Se entrambi erano già collegati, scollegali\n        if (latoPartenzaCollegato && latoArrivoCollegato) {\n          await caviService.scollegaCavo(cantiereId, internalSelectedCavo.id_cavo, 'partenza');\n          await caviService.scollegaCavo(cantiereId, internalSelectedCavo.id_cavo, 'arrivo');\n          onSuccess(`Cavo ${internalSelectedCavo.id_cavo}: Entrambi i lati scollegati`);\n        } else if (operazioniEffettuate.length > 0) {\n          onSuccess(`Cavo ${internalSelectedCavo.id_cavo}: ${operazioniEffettuate.join(', ')}`);\n        }\n      } else {\n        // Gestione di un singolo lato (logica originale)\n        if ((formData.lato === 'partenza' && latoPartenzaCollegato) ||\n            (formData.lato === 'arrivo' && latoArrivoCollegato)) {\n          // Se è già collegato, chiedi se vuole scollegarlo\n          await caviService.scollegaCavo(\n            cantiereId,\n            internalSelectedCavo.id_cavo,\n            formData.lato\n          );\n          onSuccess(`Lato ${formData.lato} del cavo ${internalSelectedCavo.id_cavo} scollegato con successo`);\n        } else {\n          // Altrimenti collega il lato\n          await caviService.collegaCavo(\n            cantiereId,\n            internalSelectedCavo.id_cavo,\n            formData.lato,\n            formData.responsabile\n          );\n          onSuccess(`Lato ${formData.lato} del cavo ${internalSelectedCavo.id_cavo} collegato con successo`);\n        }\n      }\n\n      // Ricarica i cavi per aggiornare lo stato\n      await loadCavi();\n      setOpenDialog(false);\n      setInternalSelectedCavo(null);\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Formatta lo stato dei collegamenti\n  const formatStatoCollegamenti = (collegamenti) => {\n    collegamenti = collegamenti || 0;\n\n    if (collegamenti === 0) return \"Non collegato\";\n    if (collegamenti === 1) return \"Solo partenza\";\n    if (collegamenti === 2) return \"Solo arrivo\";\n    if (collegamenti === 3) return \"Completo\";\n    return `Sconosciuto (${collegamenti})`;\n  };\n\n  // Formatta lo stato di un singolo lato\n  const formatStatoLato = (collegamenti, lato) => {\n    collegamenti = collegamenti || 0;\n\n    if (lato === 'partenza') {\n      return (collegamenti & 1) ? \"Collegato\" : \"Non collegato\";\n    } else {\n      return (collegamenti & 2) ? \"Collegato\" : \"Non collegato\";\n    }\n  };\n\n  return (\n    <Box>\n      {/* Mostra la ricerca e lista solo se non c'è un cavo preselezionato */}\n      {!selectedCavo && (\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"body2\" paragraph>\n            Visualizza e gestisci i collegamenti dei cavi installati.\n          </Typography>\n\n          <TextField\n            label=\"Cerca cavo per ID\"\n            variant=\"outlined\"\n            fullWidth\n            margin=\"normal\"\n            value={searchTerm}\n            onChange={handleSearch}\n            placeholder=\"Inserisci l'ID del cavo da cercare\"\n          />\n\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={loadCavi}\n            disabled={loading}\n            sx={{ mt: 2, mb: 3 }}\n          >\n            {loading ? <CircularProgress size={24} /> : \"Aggiorna lista\"}\n          </Button>\n\n        {loading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n            <CircularProgress />\n          </Box>\n        ) : filteredCavi.length === 0 ? (\n          <Box sx={{ mt: 2 }}>\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              {searchTerm ? \n                `Nessun cavo installato trovato con ID contenente \"${searchTerm}\".` : \n                \"Nessun cavo installato trovato in questo cantiere.\"}\n            </Alert>\n            <Typography variant=\"body2\" sx={{ mt: 2, mb: 1 }}>\n              Possibili motivi:\n            </Typography>\n            <ul>\n              <li>\n                <Typography variant=\"body2\">\n                  Non ci sono cavi nello stato \"INSTALLATO\" in questo cantiere.\n                </Typography>\n              </li>\n              <li>\n                <Typography variant=\"body2\">\n                  Il cavo che stai cercando potrebbe essere in uno stato diverso da \"INSTALLATO\" (es. \"DA INSTALLARE\", \"POSATO\").\n                </Typography>\n              </li>\n              <li>\n                <Typography variant=\"body2\">\n                  Il cavo potrebbe essere marcato come SPARE.\n                </Typography>\n              </li>\n            </ul>\n            <Typography variant=\"body2\" sx={{ mt: 2 }}>\n              Suggerimenti:\n            </Typography>\n            <ul>\n              <li>\n                <Typography variant=\"body2\">\n                  Verifica lo stato del cavo nella pagina di gestione cavi.\n                </Typography>\n              </li>\n              <li>\n                <Typography variant=\"body2\">\n                  Assicurati che il cavo sia stato installato correttamente.\n                </Typography>\n              </li>\n            </ul>\n          </Box>\n        ) : (\n          <TableContainer component={Paper} sx={{ mt: 2 }}>\n            <Table>\n              <TableHead>\n                <TableRow>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell>Lato Partenza</TableCell>\n                  <TableCell>Lato Arrivo</TableCell>\n                  <TableCell>Resp. Partenza</TableCell>\n                  <TableCell>Resp. Arrivo</TableCell>\n                  <TableCell>Azioni</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {filteredCavi.map((cavo) => (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>{cavo.id_cavo}</TableCell>\n                    <TableCell>{formatStatoCollegamenti(cavo.collegamenti)}</TableCell>\n                    <TableCell>{formatStatoLato(cavo.collegamenti, 'partenza')}</TableCell>\n                    <TableCell>{formatStatoLato(cavo.collegamenti, 'arrivo')}</TableCell>\n                    <TableCell>{cavo.responsabile_partenza || '-'}</TableCell>\n                    <TableCell>{cavo.responsabile_arrivo || '-'}</TableCell>\n                    <TableCell>\n                      <Button\n                        variant=\"outlined\"\n                        size=\"small\"\n                        onClick={() => handleCavoSelect(cavo)}\n                      >\n                        Gestisci\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        )}\n        </Paper>\n      )}\n\n      {/* Dialog per la gestione dei collegamenti */}\n      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth=\"md\" fullWidth>\n        <DialogTitle>Gestione Collegamenti Cavo</DialogTitle>\n        <DialogContent>\n          {internalSelectedCavo && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"subtitle1\" gutterBottom>\n                Cavo selezionato: {internalSelectedCavo.id_cavo}\n              </Typography>\n\n              <Divider sx={{ my: 2 }} />\n\n              <Typography variant=\"subtitle2\" gutterBottom>\n                Informazioni Cavo:\n              </Typography>\n              <Typography variant=\"body2\">\n                Partenza (FROM): {internalSelectedCavo.ubicazione_partenza || 'N/A'}\n              </Typography>\n              <Typography variant=\"body2\">\n                Arrivo (TO): {internalSelectedCavo.ubicazione_arrivo || 'N/A'}\n              </Typography>\n\n              <Divider sx={{ my: 2 }} />\n\n              <Typography variant=\"subtitle2\" gutterBottom>\n                Stato Collegamenti:\n              </Typography>\n              <Typography variant=\"body2\">\n                Lato Partenza: {formatStatoLato(internalSelectedCavo.collegamenti, 'partenza')}\n                {(internalSelectedCavo.collegamenti & 1) ? ` (Responsabile: ${internalSelectedCavo.responsabile_partenza || 'N/A'})` : ''}\n              </Typography>\n              <Typography variant=\"body2\">\n                Lato Arrivo: {formatStatoLato(internalSelectedCavo.collegamenti, 'arrivo')}\n                {(internalSelectedCavo.collegamenti & 2) ? ` (Responsabile: ${internalSelectedCavo.responsabile_arrivo || 'N/A'})` : ''}\n              </Typography>\n\n              <Divider sx={{ my: 2 }} />\n\n              <Typography variant=\"subtitle2\" gutterBottom>\n                Gestisci Collegamenti:\n              </Typography>\n\n              <FormControl component=\"fieldset\" sx={{ mt: 2 }}>\n                <Typography variant=\"body2\" gutterBottom>\n                  Seleziona l'operazione da eseguire:\n                </Typography>\n                <RadioGroup\n                  name=\"lato\"\n                  value={formData.lato}\n                  onChange={handleFormChange}\n                  row\n                >\n                  {/* Mostra opzioni intelligenti basate sullo stato del cavo */}\n                  {!latoPartenzaCollegato && (\n                    <FormControlLabel\n                      value=\"partenza\"\n                      control={<Radio />}\n                      label=\"Collega Lato Partenza\"\n                    />\n                  )}\n                  {!latoArrivoCollegato && (\n                    <FormControlLabel\n                      value=\"arrivo\"\n                      control={<Radio />}\n                      label=\"Collega Lato Arrivo\"\n                    />\n                  )}\n                  {(!latoPartenzaCollegato || !latoArrivoCollegato) && (\n                    <FormControlLabel\n                      value=\"entrambi\"\n                      control={<Radio />}\n                      label=\"Collega Entrambi i lati\"\n                    />\n                  )}\n                  {latoPartenzaCollegato && (\n                    <FormControlLabel\n                      value=\"partenza\"\n                      control={<Radio />}\n                      label=\"Scollega Lato Partenza\"\n                    />\n                  )}\n                  {latoArrivoCollegato && (\n                    <FormControlLabel\n                      value=\"arrivo\"\n                      control={<Radio />}\n                      label=\"Scollega Lato Arrivo\"\n                    />\n                  )}\n                  {(latoPartenzaCollegato && latoArrivoCollegato) && (\n                    <FormControlLabel\n                      value=\"entrambi\"\n                      control={<Radio />}\n                      label=\"Scollega Entrambi i lati\"\n                    />\n                  )}\n                </RadioGroup>\n              </FormControl>\n\n              <TextField\n                margin=\"dense\"\n                name=\"responsabile\"\n                label=\"Responsabile del collegamento\"\n                fullWidth\n                variant=\"outlined\"\n                value={formData.responsabile}\n                onChange={handleFormChange}\n                sx={{ mt: 2 }}\n                helperText=\"Lascia vuoto per usare 'cantiere' come valore predefinito\"\n              />\n\n              <Typography variant=\"body2\" color=\"primary\" sx={{ mt: 2 }}>\n                {formData.lato === 'entrambi' ?\n                  (() => {\n                    const collegamenti = internalSelectedCavo.collegamenti || 0;\n                    const latoPartenzaCollegato = (collegamenti & 1) === 1;\n                    const latoArrivoCollegato = (collegamenti & 2) === 2;\n\n                    if (latoPartenzaCollegato && latoArrivoCollegato) {\n                      return \"Entrambi i lati sono collegati. Procedendo verranno scollegati entrambi.\";\n                    } else if (latoPartenzaCollegato) {\n                      return \"Il lato partenza è già collegato. Verrà collegato solo il lato arrivo.\";\n                    } else if (latoArrivoCollegato) {\n                      return \"Il lato arrivo è già collegato. Verrà collegato solo il lato partenza.\";\n                    } else {\n                      return \"Procedendo verranno collegati entrambi i lati.\";\n                    }\n                  })() :\n                  formData.lato === 'partenza' && (internalSelectedCavo.collegamenti & 1) ?\n                    \"Attenzione: Il lato partenza è già collegato. Procedendo verrà scollegato.\" :\n                    formData.lato === 'arrivo' && (internalSelectedCavo.collegamenti & 2) ?\n                    \"Attenzione: Il lato arrivo è già collegato. Procedendo verrà scollegato.\" :\n                    `Procedendo verrà collegato il lato ${formData.lato}.`}\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => {\n            setOpenDialog(false);\n            // Chiama onSuccess con null per chiudere anche il dialog padre\n            if (onSuccess) {\n              onSuccess(null);\n            }\n          }}>Annulla</Button>\n          <Button\n            onClick={handleSaveCollegamento}\n            disabled={loading || !internalSelectedCavo}\n            variant=\"contained\"\n            color=\"primary\"\n          >\n            {loading ? <CircularProgress size={24} /> :\n              formData.lato === 'entrambi' ?\n                \"Gestisci Entrambi\" :\n                ((formData.lato === 'partenza' && (internalSelectedCavo?.collegamenti & 1)) ||\n                 (formData.lato === 'arrivo' && (internalSelectedCavo?.collegamenti & 2))) ?\n                  \"Scollega\" : \"Collega\"}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default CollegamentiCavo;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,gBAAgB,EAChBC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,QACH,eAAe;AACtB,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,YAAY,GAAG,IAAI;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,IAAI,EAAEC,OAAO,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7C,QAAQ,CAACgC,YAAY,CAAC;EAC9E,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAACgC,YAAY,CAAC;EAC5D,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC;IACvCkD,IAAI,EAAE,UAAU;IAChBC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACAlD,SAAS,CAAC,MAAM;IACdmD,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACrB,UAAU,CAAC,CAAC;EAEhB,MAAMqB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChBgB,OAAO,CAACC,GAAG,CAAC,4CAA4CvB,UAAU,KAAK,CAAC;;MAExE;MACA,MAAMwB,QAAQ,GAAG,MAAM5B,WAAW,CAAC6B,iBAAiB,CAACzB,UAAU,CAAC;MAEhE,IAAIwB,QAAQ,IAAIE,KAAK,CAACC,OAAO,CAACH,QAAQ,CAAC,EAAE;QACvCF,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACI,MAAM,kBAAkB,CAAC;QAC1DpB,OAAO,CAACgB,QAAQ,CAAC;QACjBd,eAAe,CAACc,QAAQ,CAAC;QAEzB,IAAIA,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;UACzBN,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;UACjE;QACF;MACF,CAAC,MAAM;QACLD,OAAO,CAACO,KAAK,CAAC,iCAAiC,EAAEL,QAAQ,CAAC;QAC1DhB,OAAO,CAAC,EAAE,CAAC;QACXE,eAAe,CAAC,EAAE,CAAC;QACnBP,OAAO,CAAC,uEAAuE,CAAC;MAClF;IACF,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDrB,OAAO,CAAC,EAAE,CAAC;MACXE,eAAe,CAAC,EAAE,CAAC;;MAEnB;MACA,IAAIoB,YAAY,GAAG,mCAAmC;MAEtD,IAAID,KAAK,CAACE,MAAM,EAAE;QAChBD,YAAY,IAAID,KAAK,CAACE,MAAM;MAC9B,CAAC,MAAM,IAAIF,KAAK,CAACG,OAAO,EAAE;QACxB;QACA,MAAMC,YAAY,GAAGJ,KAAK,CAACG,OAAO,CAC/BE,OAAO,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAC5CA,OAAO,CAAC,gBAAgB,EAAE,uBAAuB,CAAC;QACrDJ,YAAY,IAAIG,YAAY;MAC9B,CAAC,MAAM;QACLH,YAAY,IAAI,oBAAoB;MACtC;MAEA3B,OAAO,CAAC2B,YAAY,CAAC;IACvB,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6B,YAAY,GAAIC,KAAK,IAAK;IAC9B,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;IAC/B3B,aAAa,CAACyB,IAAI,CAAC;IAEnB,IAAI,CAACA,IAAI,CAACG,IAAI,CAAC,CAAC,EAAE;MAChB9B,eAAe,CAACH,IAAI,CAAC;IACvB,CAAC,MAAM;MACL,MAAMkC,QAAQ,GAAGlC,IAAI,CAACmC,MAAM,CAACC,IAAI,IAC/BA,IAAI,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,IAAI,CAACQ,WAAW,CAAC,CAAC,CACxD,CAAC;MACDnC,eAAe,CAAC+B,QAAQ,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMM,gBAAgB,GAAIJ,IAAI,IAAK;IACjC7B,uBAAuB,CAAC6B,IAAI,CAAC;IAC7B3B,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,MAAMgC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEX;IAAM,CAAC,GAAGU,CAAC,CAACX,MAAM;IAChCpB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACiC,IAAI,GAAGX;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMY,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF7C,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM8C,YAAY,GAAGvC,oBAAoB,CAACuC,YAAY,IAAI,CAAC;MAC3D,MAAMC,qBAAqB,GAAG,CAACD,YAAY,GAAG,CAAC,MAAM,CAAC;MACtD,MAAME,mBAAmB,GAAG,CAACF,YAAY,GAAG,CAAC,MAAM,CAAC;MAEpD,IAAInC,QAAQ,CAACE,IAAI,KAAK,UAAU,EAAE;QAChC;QACA,IAAIoC,oBAAoB,GAAG,EAAE;;QAE7B;QACA,IAAI,CAACF,qBAAqB,EAAE;UAC1B,MAAMzD,WAAW,CAAC4D,WAAW,CAACxD,UAAU,EAAEa,oBAAoB,CAAC+B,OAAO,EAAE,UAAU,EAAE3B,QAAQ,CAACG,YAAY,CAAC;UAC1GmC,oBAAoB,CAACE,IAAI,CAAC,yBAAyB,CAAC;QACtD;;QAEA;QACA,IAAI,CAACH,mBAAmB,EAAE;UACxB,MAAM1D,WAAW,CAAC4D,WAAW,CAACxD,UAAU,EAAEa,oBAAoB,CAAC+B,OAAO,EAAE,QAAQ,EAAE3B,QAAQ,CAACG,YAAY,CAAC;UACxGmC,oBAAoB,CAACE,IAAI,CAAC,uBAAuB,CAAC;QACpD;;QAEA;QACA,IAAIJ,qBAAqB,IAAIC,mBAAmB,EAAE;UAChD,MAAM1D,WAAW,CAAC8D,YAAY,CAAC1D,UAAU,EAAEa,oBAAoB,CAAC+B,OAAO,EAAE,UAAU,CAAC;UACpF,MAAMhD,WAAW,CAAC8D,YAAY,CAAC1D,UAAU,EAAEa,oBAAoB,CAAC+B,OAAO,EAAE,QAAQ,CAAC;UAClF1C,SAAS,CAAC,QAAQW,oBAAoB,CAAC+B,OAAO,8BAA8B,CAAC;QAC/E,CAAC,MAAM,IAAIW,oBAAoB,CAAC3B,MAAM,GAAG,CAAC,EAAE;UAC1C1B,SAAS,CAAC,QAAQW,oBAAoB,CAAC+B,OAAO,KAAKW,oBAAoB,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACvF;MACF,CAAC,MAAM;QACL;QACA,IAAK1C,QAAQ,CAACE,IAAI,KAAK,UAAU,IAAIkC,qBAAqB,IACrDpC,QAAQ,CAACE,IAAI,KAAK,QAAQ,IAAImC,mBAAoB,EAAE;UACvD;UACA,MAAM1D,WAAW,CAAC8D,YAAY,CAC5B1D,UAAU,EACVa,oBAAoB,CAAC+B,OAAO,EAC5B3B,QAAQ,CAACE,IACX,CAAC;UACDjB,SAAS,CAAC,QAAQe,QAAQ,CAACE,IAAI,aAAaN,oBAAoB,CAAC+B,OAAO,0BAA0B,CAAC;QACrG,CAAC,MAAM;UACL;UACA,MAAMhD,WAAW,CAAC4D,WAAW,CAC3BxD,UAAU,EACVa,oBAAoB,CAAC+B,OAAO,EAC5B3B,QAAQ,CAACE,IAAI,EACbF,QAAQ,CAACG,YACX,CAAC;UACDlB,SAAS,CAAC,QAAQe,QAAQ,CAACE,IAAI,aAAaN,oBAAoB,CAAC+B,OAAO,yBAAyB,CAAC;QACpG;MACF;;MAEA;MACA,MAAMvB,QAAQ,CAAC,CAAC;MAChBL,aAAa,CAAC,KAAK,CAAC;MACpBF,uBAAuB,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOe,KAAK,EAAE;MACd1B,OAAO,CAAC,gCAAgC,IAAI0B,KAAK,CAACG,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACnFV,OAAO,CAACO,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsD,uBAAuB,GAAIR,YAAY,IAAK;IAChDA,YAAY,GAAGA,YAAY,IAAI,CAAC;IAEhC,IAAIA,YAAY,KAAK,CAAC,EAAE,OAAO,eAAe;IAC9C,IAAIA,YAAY,KAAK,CAAC,EAAE,OAAO,eAAe;IAC9C,IAAIA,YAAY,KAAK,CAAC,EAAE,OAAO,aAAa;IAC5C,IAAIA,YAAY,KAAK,CAAC,EAAE,OAAO,UAAU;IACzC,OAAO,gBAAgBA,YAAY,GAAG;EACxC,CAAC;;EAED;EACA,MAAMS,eAAe,GAAGA,CAACT,YAAY,EAAEjC,IAAI,KAAK;IAC9CiC,YAAY,GAAGA,YAAY,IAAI,CAAC;IAEhC,IAAIjC,IAAI,KAAK,UAAU,EAAE;MACvB,OAAQiC,YAAY,GAAG,CAAC,GAAI,WAAW,GAAG,eAAe;IAC3D,CAAC,MAAM;MACL,OAAQA,YAAY,GAAG,CAAC,GAAI,WAAW,GAAG,eAAe;IAC3D;EACF,CAAC;EAED,oBACEtD,OAAA,CAAC3B,GAAG;IAAA2F,QAAA,GAED,CAAC7D,YAAY,iBACZH,OAAA,CAACzB,KAAK;MAAC0F,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACzBhE,OAAA,CAAC1B,UAAU;QAAC8F,OAAO,EAAC,OAAO;QAACC,SAAS;QAAAL,QAAA,EAAC;MAEtC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbzE,OAAA,CAACvB,SAAS;QACRiG,KAAK,EAAC,mBAAmB;QACzBN,OAAO,EAAC,UAAU;QAClBO,SAAS;QACTC,MAAM,EAAC,QAAQ;QACfnC,KAAK,EAAE5B,UAAW;QAClBgE,QAAQ,EAAExC,YAAa;QACvByC,WAAW,EAAC;MAAoC;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAEFzE,OAAA,CAACxB,MAAM;QACL4F,OAAO,EAAC,WAAW;QACnBW,KAAK,EAAC,SAAS;QACfC,OAAO,EAAEzD,QAAS;QAClB0D,QAAQ,EAAE1E,OAAQ;QAClB0D,EAAE,EAAE;UAAEiB,EAAE,EAAE,CAAC;UAAEf,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAEpBzD,OAAO,gBAAGP,OAAA,CAACV,gBAAgB;UAAC6F,IAAI,EAAE;QAAG;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAAG;MAAgB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,EAEVlE,OAAO,gBACNP,OAAA,CAAC3B,GAAG;QAAC4F,EAAE,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAtB,QAAA,eAC5DhE,OAAA,CAACV,gBAAgB;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GACJ9D,YAAY,CAACmB,MAAM,KAAK,CAAC,gBAC3B9B,OAAA,CAAC3B,GAAG;QAAC4F,EAAE,EAAE;UAAEiB,EAAE,EAAE;QAAE,CAAE;QAAAlB,QAAA,gBACjBhE,OAAA,CAACT,KAAK;UAACgG,QAAQ,EAAC,MAAM;UAACtB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EAClCnD,UAAU,GACT,qDAAqDA,UAAU,IAAI,GACnE;QAAoD;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACRzE,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,OAAO;UAACH,EAAE,EAAE;YAAEiB,EAAE,EAAE,CAAC;YAAEf,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EAAC;QAElD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzE,OAAA;UAAAgE,QAAA,gBACEhE,OAAA;YAAAgE,QAAA,eACEhE,OAAA,CAAC1B,UAAU;cAAC8F,OAAO,EAAC,OAAO;cAAAJ,QAAA,EAAC;YAE5B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACLzE,OAAA;YAAAgE,QAAA,eACEhE,OAAA,CAAC1B,UAAU;cAAC8F,OAAO,EAAC,OAAO;cAAAJ,QAAA,EAAC;YAE5B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACLzE,OAAA;YAAAgE,QAAA,eACEhE,OAAA,CAAC1B,UAAU;cAAC8F,OAAO,EAAC,OAAO;cAAAJ,QAAA,EAAC;YAE5B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACLzE,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,OAAO;UAACH,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,EAAC;QAE3C;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzE,OAAA;UAAAgE,QAAA,gBACEhE,OAAA;YAAAgE,QAAA,eACEhE,OAAA,CAAC1B,UAAU;cAAC8F,OAAO,EAAC,OAAO;cAAAJ,QAAA,EAAC;YAE5B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACLzE,OAAA;YAAAgE,QAAA,eACEhE,OAAA,CAAC1B,UAAU;cAAC8F,OAAO,EAAC,OAAO;cAAAJ,QAAA,EAAC;YAE5B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,gBAENzE,OAAA,CAACL,cAAc;QAAC6F,SAAS,EAAEjH,KAAM;QAAC0F,EAAE,EAAE;UAAEiB,EAAE,EAAE;QAAE,CAAE;QAAAlB,QAAA,eAC9ChE,OAAA,CAACR,KAAK;UAAAwE,QAAA,gBACJhE,OAAA,CAACJ,SAAS;YAAAoE,QAAA,eACRhE,OAAA,CAACH,QAAQ;cAAAmE,QAAA,gBACPhE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAC;cAAO;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BzE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BzE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAC;cAAa;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpCzE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAC;cAAW;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCzE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAC;cAAc;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACrCzE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAC;cAAY;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnCzE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAC;cAAM;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZzE,OAAA,CAACP,SAAS;YAAAuE,QAAA,EACPrD,YAAY,CAAC8E,GAAG,CAAE5C,IAAI,iBACrB7C,OAAA,CAACH,QAAQ;cAAAmE,QAAA,gBACPhE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAEnB,IAAI,CAACC;cAAO;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCzE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAEF,uBAAuB,CAACjB,IAAI,CAACS,YAAY;cAAC;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnEzE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAED,eAAe,CAAClB,IAAI,CAACS,YAAY,EAAE,UAAU;cAAC;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvEzE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAED,eAAe,CAAClB,IAAI,CAACS,YAAY,EAAE,QAAQ;cAAC;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrEzE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAEnB,IAAI,CAAC6C,qBAAqB,IAAI;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1DzE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,EAAEnB,IAAI,CAAC8C,mBAAmB,IAAI;cAAG;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxDzE,OAAA,CAACN,SAAS;gBAAAsE,QAAA,eACRhE,OAAA,CAACxB,MAAM;kBACL4F,OAAO,EAAC,UAAU;kBAClBe,IAAI,EAAC,OAAO;kBACZH,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAACJ,IAAI,CAAE;kBAAAmB,QAAA,EACvC;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAfC5B,IAAI,CAACC,OAAO;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CACjB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACR,eAGDzE,OAAA,CAAClB,MAAM;MAAC8G,IAAI,EAAE3E,UAAW;MAAC4E,OAAO,EAAEA,CAAA,KAAM3E,aAAa,CAAC,KAAK,CAAE;MAAC4E,QAAQ,EAAC,IAAI;MAACnB,SAAS;MAAAX,QAAA,gBACpFhE,OAAA,CAACjB,WAAW;QAAAiF,QAAA,EAAC;MAA0B;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACrDzE,OAAA,CAAChB,aAAa;QAAAgF,QAAA,EACXjD,oBAAoB,iBACnBf,OAAA,CAAC3B,GAAG;UAAC4F,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,gBACjBhE,OAAA,CAAC1B,UAAU;YAAC8F,OAAO,EAAC,WAAW;YAAC2B,YAAY;YAAA/B,QAAA,GAAC,oBACzB,EAACjD,oBAAoB,CAAC+B,OAAO;UAAA;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eAEbzE,OAAA,CAACnB,OAAO;YAACoF,EAAE,EAAE;cAAEqB,EAAE,EAAE;YAAE;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE1BzE,OAAA,CAAC1B,UAAU;YAAC8F,OAAO,EAAC,WAAW;YAAC2B,YAAY;YAAA/B,QAAA,EAAC;UAE7C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzE,OAAA,CAAC1B,UAAU;YAAC8F,OAAO,EAAC,OAAO;YAAAJ,QAAA,GAAC,mBACT,EAACjD,oBAAoB,CAACiF,mBAAmB,IAAI,KAAK;UAAA;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACbzE,OAAA,CAAC1B,UAAU;YAAC8F,OAAO,EAAC,OAAO;YAAAJ,QAAA,GAAC,eACb,EAACjD,oBAAoB,CAACkF,iBAAiB,IAAI,KAAK;UAAA;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAEbzE,OAAA,CAACnB,OAAO;YAACoF,EAAE,EAAE;cAAEqB,EAAE,EAAE;YAAE;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE1BzE,OAAA,CAAC1B,UAAU;YAAC8F,OAAO,EAAC,WAAW;YAAC2B,YAAY;YAAA/B,QAAA,EAAC;UAE7C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzE,OAAA,CAAC1B,UAAU;YAAC8F,OAAO,EAAC,OAAO;YAAAJ,QAAA,GAAC,iBACX,EAACD,eAAe,CAAChD,oBAAoB,CAACuC,YAAY,EAAE,UAAU,CAAC,EAC5EvC,oBAAoB,CAACuC,YAAY,GAAG,CAAC,GAAI,mBAAmBvC,oBAAoB,CAAC2E,qBAAqB,IAAI,KAAK,GAAG,GAAG,EAAE;UAAA;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/G,CAAC,eACbzE,OAAA,CAAC1B,UAAU;YAAC8F,OAAO,EAAC,OAAO;YAAAJ,QAAA,GAAC,eACb,EAACD,eAAe,CAAChD,oBAAoB,CAACuC,YAAY,EAAE,QAAQ,CAAC,EACxEvC,oBAAoB,CAACuC,YAAY,GAAG,CAAC,GAAI,mBAAmBvC,oBAAoB,CAAC4E,mBAAmB,IAAI,KAAK,GAAG,GAAG,EAAE;UAAA;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7G,CAAC,eAEbzE,OAAA,CAACnB,OAAO;YAACoF,EAAE,EAAE;cAAEqB,EAAE,EAAE;YAAE;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE1BzE,OAAA,CAAC1B,UAAU;YAAC8F,OAAO,EAAC,WAAW;YAAC2B,YAAY;YAAA/B,QAAA,EAAC;UAE7C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbzE,OAAA,CAACd,WAAW;YAACsG,SAAS,EAAC,UAAU;YAACvB,EAAE,EAAE;cAAEiB,EAAE,EAAE;YAAE,CAAE;YAAAlB,QAAA,gBAC9ChE,OAAA,CAAC1B,UAAU;cAAC8F,OAAO,EAAC,OAAO;cAAC2B,YAAY;cAAA/B,QAAA,EAAC;YAEzC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzE,OAAA,CAACX,UAAU;cACT+D,IAAI,EAAC,MAAM;cACXX,KAAK,EAAEtB,QAAQ,CAACE,IAAK;cACrBwD,QAAQ,EAAE3B,gBAAiB;cAC3BgD,GAAG;cAAAlC,QAAA,GAGF,CAACT,qBAAqB,iBACrBvD,OAAA,CAACb,gBAAgB;gBACfsD,KAAK,EAAC,UAAU;gBAChB0D,OAAO,eAAEnG,OAAA,CAACZ,KAAK;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBC,KAAK,EAAC;cAAuB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CACF,EACA,CAACjB,mBAAmB,iBACnBxD,OAAA,CAACb,gBAAgB;gBACfsD,KAAK,EAAC,QAAQ;gBACd0D,OAAO,eAAEnG,OAAA,CAACZ,KAAK;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBC,KAAK,EAAC;cAAqB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CACF,EACA,CAAC,CAAClB,qBAAqB,IAAI,CAACC,mBAAmB,kBAC9CxD,OAAA,CAACb,gBAAgB;gBACfsD,KAAK,EAAC,UAAU;gBAChB0D,OAAO,eAAEnG,OAAA,CAACZ,KAAK;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBC,KAAK,EAAC;cAAyB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACF,EACAlB,qBAAqB,iBACpBvD,OAAA,CAACb,gBAAgB;gBACfsD,KAAK,EAAC,UAAU;gBAChB0D,OAAO,eAAEnG,OAAA,CAACZ,KAAK;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBC,KAAK,EAAC;cAAwB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CACF,EACAjB,mBAAmB,iBAClBxD,OAAA,CAACb,gBAAgB;gBACfsD,KAAK,EAAC,QAAQ;gBACd0D,OAAO,eAAEnG,OAAA,CAACZ,KAAK;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBC,KAAK,EAAC;cAAsB;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CACF,EACClB,qBAAqB,IAAIC,mBAAmB,iBAC5CxD,OAAA,CAACb,gBAAgB;gBACfsD,KAAK,EAAC,UAAU;gBAChB0D,OAAO,eAAEnG,OAAA,CAACZ,KAAK;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACnBC,KAAK,EAAC;cAA0B;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEdzE,OAAA,CAACvB,SAAS;YACRmG,MAAM,EAAC,OAAO;YACdxB,IAAI,EAAC,cAAc;YACnBsB,KAAK,EAAC,+BAA+B;YACrCC,SAAS;YACTP,OAAO,EAAC,UAAU;YAClB3B,KAAK,EAAEtB,QAAQ,CAACG,YAAa;YAC7BuD,QAAQ,EAAE3B,gBAAiB;YAC3Be,EAAE,EAAE;cAAEiB,EAAE,EAAE;YAAE,CAAE;YACdkB,UAAU,EAAC;UAA2D;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAEFzE,OAAA,CAAC1B,UAAU;YAAC8F,OAAO,EAAC,OAAO;YAACW,KAAK,EAAC,SAAS;YAACd,EAAE,EAAE;cAAEiB,EAAE,EAAE;YAAE,CAAE;YAAAlB,QAAA,EACvD7C,QAAQ,CAACE,IAAI,KAAK,UAAU,GAC3B,CAAC,MAAM;cACL,MAAMiC,YAAY,GAAGvC,oBAAoB,CAACuC,YAAY,IAAI,CAAC;cAC3D,MAAMC,qBAAqB,GAAG,CAACD,YAAY,GAAG,CAAC,MAAM,CAAC;cACtD,MAAME,mBAAmB,GAAG,CAACF,YAAY,GAAG,CAAC,MAAM,CAAC;cAEpD,IAAIC,qBAAqB,IAAIC,mBAAmB,EAAE;gBAChD,OAAO,0EAA0E;cACnF,CAAC,MAAM,IAAID,qBAAqB,EAAE;gBAChC,OAAO,wEAAwE;cACjF,CAAC,MAAM,IAAIC,mBAAmB,EAAE;gBAC9B,OAAO,wEAAwE;cACjF,CAAC,MAAM;gBACL,OAAO,gDAAgD;cACzD;YACF,CAAC,EAAE,CAAC,GACJrC,QAAQ,CAACE,IAAI,KAAK,UAAU,IAAKN,oBAAoB,CAACuC,YAAY,GAAG,CAAE,GACrE,4EAA4E,GAC5EnC,QAAQ,CAACE,IAAI,KAAK,QAAQ,IAAKN,oBAAoB,CAACuC,YAAY,GAAG,CAAE,GACrE,0EAA0E,GAC1E,sCAAsCnC,QAAQ,CAACE,IAAI;UAAG;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBzE,OAAA,CAACf,aAAa;QAAA+E,QAAA,gBACZhE,OAAA,CAACxB,MAAM;UAACwG,OAAO,EAAEA,CAAA,KAAM;YACrB9D,aAAa,CAAC,KAAK,CAAC;YACpB;YACA,IAAId,SAAS,EAAE;cACbA,SAAS,CAAC,IAAI,CAAC;YACjB;UACF,CAAE;UAAA4D,QAAA,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACnBzE,OAAA,CAACxB,MAAM;UACLwG,OAAO,EAAE3B,sBAAuB;UAChC4B,QAAQ,EAAE1E,OAAO,IAAI,CAACQ,oBAAqB;UAC3CqD,OAAO,EAAC,WAAW;UACnBW,KAAK,EAAC,SAAS;UAAAf,QAAA,EAEdzD,OAAO,gBAAGP,OAAA,CAACV,gBAAgB;YAAC6F,IAAI,EAAE;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GACvCtD,QAAQ,CAACE,IAAI,KAAK,UAAU,GAC1B,mBAAmB,GACjBF,QAAQ,CAACE,IAAI,KAAK,UAAU,IAAK,CAAAN,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEuC,YAAY,IAAG,CAAE,IACxEnC,QAAQ,CAACE,IAAI,KAAK,QAAQ,IAAK,CAAAN,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEuC,YAAY,IAAG,CAAG,GACvE,UAAU,GAAG;QAAS;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACnE,EAAA,CAjdIL,gBAAgB;AAAAoG,EAAA,GAAhBpG,gBAAgB;AAmdtB,eAAeA,gBAAgB;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}