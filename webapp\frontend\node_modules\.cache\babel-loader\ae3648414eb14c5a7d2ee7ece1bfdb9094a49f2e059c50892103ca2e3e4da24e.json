{"ast": null, "code": "import axiosInstance from './axiosConfig';\nconst rapportiGeneraliService = {\n  // Ottiene la lista dei rapporti generali di un cantiere\n  getRapporti: async (cantiereId, skip = 0, limit = 100) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/rapporti`, {\n        params: {\n          skip,\n          limit\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Get rapporti error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea un nuovo rapporto generale\n  createRapporto: async (cantiereId, rapportoData) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/rapporti`, rapportoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create rapporto error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i dettagli di un rapporto generale\n  getRapporto: async (cantiereId, rapportoId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/rapporti/${rapportoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get rapporto error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna un rapporto generale\n  updateRapporto: async (cantiereId, rapportoId, rapportoData) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/rapporti/${rapportoId}`, rapportoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update rapporto error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un rapporto generale\n  deleteRapporto: async (cantiereId, rapportoId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/rapporti/${rapportoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete rapporto error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna le statistiche di un rapporto\n  aggiornaStatistiche: async (cantiereId, rapportoId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/rapporti/${rapportoId}/aggiorna-statistiche`);\n      return response.data;\n    } catch (error) {\n      console.error('Aggiorna statistiche error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default rapportiGeneraliService;", "map": {"version": 3, "names": ["axiosInstance", "rapportiGeneraliService", "getRapporti", "cantiereId", "skip", "limit", "cantiereIdNum", "parseInt", "isNaN", "Error", "response", "get", "params", "data", "error", "console", "createRapporto", "rapportoData", "post", "getRapporto", "rapportoId", "updateRapporto", "put", "deleteRapporto", "delete", "aggiornaStatistiche"], "sources": ["C:/CMS/webapp/frontend/src/services/rapportiGeneraliService.js"], "sourcesContent": ["import axiosInstance from './axiosConfig';\n\nconst rapportiGeneraliService = {\n  // Ottiene la lista dei rapporti generali di un cantiere\n  getRapporti: async (cantiereId, skip = 0, limit = 100) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/rapporti`, {\n        params: { skip, limit }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Get rapporti error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea un nuovo rapporto generale\n  createRapporto: async (cantiereId, rapportoData) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/rapporti`, rapportoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create rapporto error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene i dettagli di un rapporto generale\n  getRapporto: async (cantiereId, rapportoId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/rapporti/${rapportoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get rapporto error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna un rapporto generale\n  updateRapporto: async (cantiereId, rapportoId, rapportoData) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/rapporti/${rapportoId}`, rapportoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update rapporto error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina un rapporto generale\n  deleteRapporto: async (cantiereId, rapportoId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/rapporti/${rapportoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete rapporto error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna le statistiche di un rapporto\n  aggiornaStatistiche: async (cantiereId, rapportoId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/rapporti/${rapportoId}/aggiorna-statistiche`);\n      return response.data;\n    } catch (error) {\n      console.error('Aggiorna statistiche error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default rapportiGeneraliService;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,eAAe;AAEzC,MAAMC,uBAAuB,GAAG;EAC9B;EACAC,WAAW,EAAE,MAAAA,CAAOC,UAAU,EAAEC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,GAAG,KAAK;IACxD,IAAI;MACF,MAAMC,aAAa,GAAGC,QAAQ,CAACJ,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIK,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,aAAaL,aAAa,WAAW,EAAE;QAC9EM,MAAM,EAAE;UAAER,IAAI;UAAEC;QAAM;MACxB,CAAC,CAAC;MACF,OAAOK,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACJ,QAAQ,GAAGI,KAAK,CAACJ,QAAQ,CAACG,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAE,cAAc,EAAE,MAAAA,CAAOb,UAAU,EAAEc,YAAY,KAAK;IAClD,IAAI;MACF,MAAMX,aAAa,GAAGC,QAAQ,CAACJ,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIK,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACkB,IAAI,CAAC,aAAaZ,aAAa,WAAW,EAAEW,YAAY,CAAC;MAC9F,OAAOP,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK,CAACJ,QAAQ,GAAGI,KAAK,CAACJ,QAAQ,CAACG,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAK,WAAW,EAAE,MAAAA,CAAOhB,UAAU,EAAEiB,UAAU,KAAK;IAC7C,IAAI;MACF,MAAMd,aAAa,GAAGC,QAAQ,CAACJ,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIK,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,aAAaL,aAAa,aAAac,UAAU,EAAE,CAAC;MAC7F,OAAOV,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACJ,QAAQ,GAAGI,KAAK,CAACJ,QAAQ,CAACG,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAO,cAAc,EAAE,MAAAA,CAAOlB,UAAU,EAAEiB,UAAU,EAAEH,YAAY,KAAK;IAC9D,IAAI;MACF,MAAMX,aAAa,GAAGC,QAAQ,CAACJ,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIK,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACsB,GAAG,CAAC,aAAahB,aAAa,aAAac,UAAU,EAAE,EAAEH,YAAY,CAAC;MAC3G,OAAOP,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK,CAACJ,QAAQ,GAAGI,KAAK,CAACJ,QAAQ,CAACG,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAS,cAAc,EAAE,MAAAA,CAAOpB,UAAU,EAAEiB,UAAU,KAAK;IAChD,IAAI;MACF,MAAMd,aAAa,GAAGC,QAAQ,CAACJ,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIK,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACwB,MAAM,CAAC,aAAalB,aAAa,aAAac,UAAU,EAAE,CAAC;MAChG,OAAOV,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK,CAACJ,QAAQ,GAAGI,KAAK,CAACJ,QAAQ,CAACG,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAW,mBAAmB,EAAE,MAAAA,CAAOtB,UAAU,EAAEiB,UAAU,KAAK;IACrD,IAAI;MACF,MAAMd,aAAa,GAAGC,QAAQ,CAACJ,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIK,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMO,QAAQ,GAAG,MAAMV,aAAa,CAACkB,IAAI,CAAC,aAAaZ,aAAa,aAAac,UAAU,uBAAuB,CAAC;MACnH,OAAOV,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK,CAACJ,QAAQ,GAAGI,KAAK,CAACJ,QAAQ,CAACG,IAAI,GAAGC,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeb,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}