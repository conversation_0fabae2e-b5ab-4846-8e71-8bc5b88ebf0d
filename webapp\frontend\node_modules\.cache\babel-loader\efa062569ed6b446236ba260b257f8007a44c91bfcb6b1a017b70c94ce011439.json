{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"کمتر از یک ثانیه\",\n    other: \"کمتر از {{count}} ثانیه\"\n  },\n  xSeconds: {\n    one: \"1 ثانیه\",\n    other: \"{{count}} ثانیه\"\n  },\n  halfAMinute: \"نیم دقیقه\",\n  lessThanXMinutes: {\n    one: \"کمتر از یک دقیقه\",\n    other: \"کمتر از {{count}} دقیقه\"\n  },\n  xMinutes: {\n    one: \"1 دقیقه\",\n    other: \"{{count}} دقیقه\"\n  },\n  aboutXHours: {\n    one: \"حدود 1 ساعت\",\n    other: \"حدود {{count}} ساعت\"\n  },\n  xHours: {\n    one: \"1 ساعت\",\n    other: \"{{count}} ساعت\"\n  },\n  xDays: {\n    one: \"1 روز\",\n    other: \"{{count}} روز\"\n  },\n  aboutXWeeks: {\n    one: \"حدود 1 هفته\",\n    other: \"حدود {{count}} هفته\"\n  },\n  xWeeks: {\n    one: \"1 هفته\",\n    other: \"{{count}} هفته\"\n  },\n  aboutXMonths: {\n    one: \"حدود 1 ماه\",\n    other: \"حدود {{count}} ماه\"\n  },\n  xMonths: {\n    one: \"1 ماه\",\n    other: \"{{count}} ماه\"\n  },\n  aboutXYears: {\n    one: \"حدود 1 سال\",\n    other: \"حدود {{count}} سال\"\n  },\n  xYears: {\n    one: \"1 سال\",\n    other: \"{{count}} سال\"\n  },\n  overXYears: {\n    one: \"بیشتر از 1 سال\",\n    other: \"بیشتر از {{count}} سال\"\n  },\n  almostXYears: {\n    one: \"نزدیک 1 سال\",\n    other: \"نزدیک {{count}} سال\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"در \" + result;\n    } else {\n      return result + \" قبل\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/fa-IR/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"کمتر از یک ثانیه\",\n    other: \"کمتر از {{count}} ثانیه\",\n  },\n\n  xSeconds: {\n    one: \"1 ثانیه\",\n    other: \"{{count}} ثانیه\",\n  },\n\n  halfAMinute: \"نیم دقیقه\",\n\n  lessThanXMinutes: {\n    one: \"کمتر از یک دقیقه\",\n    other: \"کمتر از {{count}} دقیقه\",\n  },\n\n  xMinutes: {\n    one: \"1 دقیقه\",\n    other: \"{{count}} دقیقه\",\n  },\n\n  aboutXHours: {\n    one: \"حدود 1 ساعت\",\n    other: \"حدود {{count}} ساعت\",\n  },\n\n  xHours: {\n    one: \"1 ساعت\",\n    other: \"{{count}} ساعت\",\n  },\n\n  xDays: {\n    one: \"1 روز\",\n    other: \"{{count}} روز\",\n  },\n\n  aboutXWeeks: {\n    one: \"حدود 1 هفته\",\n    other: \"حدود {{count}} هفته\",\n  },\n\n  xWeeks: {\n    one: \"1 هفته\",\n    other: \"{{count}} هفته\",\n  },\n\n  aboutXMonths: {\n    one: \"حدود 1 ماه\",\n    other: \"حدود {{count}} ماه\",\n  },\n\n  xMonths: {\n    one: \"1 ماه\",\n    other: \"{{count}} ماه\",\n  },\n\n  aboutXYears: {\n    one: \"حدود 1 سال\",\n    other: \"حدود {{count}} سال\",\n  },\n\n  xYears: {\n    one: \"1 سال\",\n    other: \"{{count}} سال\",\n  },\n\n  overXYears: {\n    one: \"بیشتر از 1 سال\",\n    other: \"بیشتر از {{count}} سال\",\n  },\n\n  almostXYears: {\n    one: \"نزدیک 1 سال\",\n    other: \"نزدیک {{count}} سال\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"در \" + result;\n    } else {\n      return result + \" قبل\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,WAAW;EAExBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,MAAM;IACxB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}