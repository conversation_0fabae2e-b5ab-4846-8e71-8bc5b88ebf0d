{"ast": null, "code": "import { formatDistance } from \"./el/_lib/formatDistance.js\";\nimport { formatLong } from \"./el/_lib/formatLong.js\";\nimport { formatRelative } from \"./el/_lib/formatRelative.js\";\nimport { localize } from \"./el/_lib/localize.js\";\nimport { match } from \"./el/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Greek locale.\n * @language Greek\n * @iso-639-2 ell\n * <AUTHOR> [@fanixk](https://github.com/fanixk)\n * <AUTHOR> [@teoulas](https://github.com/teoulas)\n */\nexport const el = {\n  code: \"el\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default el;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "el", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/el.js"], "sourcesContent": ["import { formatDistance } from \"./el/_lib/formatDistance.js\";\nimport { formatLong } from \"./el/_lib/formatLong.js\";\nimport { formatRelative } from \"./el/_lib/formatRelative.js\";\nimport { localize } from \"./el/_lib/localize.js\";\nimport { match } from \"./el/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Greek locale.\n * @language Greek\n * @iso-639-2 ell\n * <AUTHOR> [@fanixk](https://github.com/fanixk)\n * <AUTHOR> [@teoulas](https://github.com/teoulas)\n */\nexport const el = {\n  code: \"el\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default el;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}