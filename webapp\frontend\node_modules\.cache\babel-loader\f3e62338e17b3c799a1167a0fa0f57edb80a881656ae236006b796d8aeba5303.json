{"ast": null, "code": "const visuallyHidden = {\n  border: 0,\n  clip: 'rect(0 0 0 0)',\n  height: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'absolute',\n  whiteSpace: 'nowrap',\n  width: '1px'\n};\nexport default visuallyHidden;", "map": {"version": 3, "names": ["visuallyHidden", "border", "clip", "height", "margin", "overflow", "padding", "position", "whiteSpace", "width"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/utils/esm/visuallyHidden/visuallyHidden.js"], "sourcesContent": ["const visuallyHidden = {\n  border: 0,\n  clip: 'rect(0 0 0 0)',\n  height: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'absolute',\n  whiteSpace: 'nowrap',\n  width: '1px'\n};\nexport default visuallyHidden;"], "mappings": "AAAA,MAAMA,cAAc,GAAG;EACrBC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,eAAe;EACrBC,MAAM,EAAE,KAAK;EACbC,MAAM,EAAE,MAAM;EACdC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,QAAQ;EACpBC,KAAK,EAAE;AACT,CAAC;AACD,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}