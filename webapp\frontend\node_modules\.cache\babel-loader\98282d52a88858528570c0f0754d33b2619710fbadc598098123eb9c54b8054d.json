{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\CertificazioneCEI64_8Page.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { useParams, useLocation } from 'react-router-dom';\nimport { Box, Typography, Paper, Grid, Card, CardContent, Button, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Divider, Alert, Snackbar } from '@mui/material';\nimport { Assignment as AssignmentIcon, Science as ScienceIcon, Warning as WarningIcon, Assessment as ReportIcon, Build as BuildIcon, Visibility as ViewIcon, Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Dashboard as DashboardIcon } from '@mui/icons-material';\nimport CertificazioneCEI64_8 from '../../components/certificazione/CertificazioneCEI64_8';\nimport SelectedCantiereDisplay from '../../components/common/SelectedCantiereDisplay';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CertificazioneCEI64_8Page = () => {\n  _s();\n  const {\n    cantiereId\n  } = useParams();\n  const location = useLocation();\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Ref per il componente principale\n  const certificazioneCEIRef = useRef();\n\n  // Determina l'azione iniziale basata sulla route\n  const getInitialAction = () => {\n    const path = location.pathname;\n    if (path.includes('/dashboard')) return 'dashboardCEI';\n    if (path.includes('/rapporti')) return 'visualizzaRapporti';\n    if (path.includes('/prove')) return 'visualizzaProve';\n    if (path.includes('/non-conformita')) return 'visualizzaNonConformita';\n    if (path.includes('/strumenti')) return 'gestioneStrumenti';\n    return 'dashboardCEI';\n  };\n\n  // Gestisce i messaggi di successo\n  const handleSuccess = message => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'success'\n    });\n  };\n\n  // Gestisce i messaggi di errore\n  const handleError = message => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'error'\n    });\n  };\n\n  // Chiude il snackbar\n  const handleCloseSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n\n  // Opzioni del menu per la certificazione CEI 64-8\n  const menuOptions = [{\n    category: 'Dashboard',\n    items: [{\n      key: 'dashboardCEI',\n      label: 'Dashboard CEI 64-8',\n      icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 17\n      }, this),\n      description: 'Panoramica generale del sistema di certificazione'\n    }]\n  }, {\n    category: 'Rapporti Generali',\n    items: [{\n      key: 'visualizzaRapporti',\n      label: 'Visualizza Rapporti',\n      icon: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 17\n      }, this),\n      description: 'Visualizza tutti i rapporti generali di collaudo'\n    }, {\n      key: 'creaRapporto',\n      label: 'Nuovo Rapporto',\n      icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 17\n      }, this),\n      description: 'Crea un nuovo rapporto generale di collaudo'\n    }, {\n      key: 'modificaRapporto',\n      label: 'Modifica Rapporto',\n      icon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 17\n      }, this),\n      description: 'Modifica un rapporto esistente'\n    }, {\n      key: 'eliminaRapporto',\n      label: 'Elimina Rapporto',\n      icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 17\n      }, this),\n      description: 'Elimina un rapporto generale'\n    }]\n  }, {\n    category: 'Certificazioni e Prove',\n    items: [{\n      key: 'visualizzaCertificazioni',\n      label: 'Visualizza Certificazioni',\n      icon: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 17\n      }, this),\n      description: 'Visualizza tutte le certificazioni cavi'\n    }, {\n      key: 'creaCertificazione',\n      label: 'Nuova Certificazione',\n      icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 17\n      }, this),\n      description: 'Crea una nuova certificazione cavo'\n    }, {\n      key: 'visualizzaProve',\n      label: 'Prove Dettagliate',\n      icon: /*#__PURE__*/_jsxDEV(ScienceIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 17\n      }, this),\n      description: 'Gestisci le prove dettagliate per conformità CEI 64-8'\n    }, {\n      key: 'creaProva',\n      label: 'Nuova Prova',\n      icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 17\n      }, this),\n      description: 'Crea una nuova prova dettagliata'\n    }]\n  }, {\n    category: 'Non Conformità',\n    items: [{\n      key: 'visualizzaNonConformita',\n      label: 'Visualizza Non Conformità',\n      icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 17\n      }, this),\n      description: 'Gestisci le non conformità rilevate'\n    }]\n  }, {\n    category: 'Strumenti',\n    items: [{\n      key: 'gestioneStrumenti',\n      label: 'Gestione Strumenti',\n      icon: /*#__PURE__*/_jsxDEV(BuildIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 17\n      }, this),\n      description: 'Gestisci gli strumenti di misura certificati'\n    }]\n  }];\n\n  // Gestisce la selezione di un'opzione\n  const handleOptionSelect = optionKey => {\n    if (certificazioneCEIRef.current) {\n      certificazioneCEIRef.current.handleOptionSelect(optionKey);\n    }\n  };\n\n  // Esegue l'azione iniziale\n  React.useEffect(() => {\n    const initialAction = getInitialAction();\n    if (initialAction && certificazioneCEIRef.current) {\n      setTimeout(() => {\n        certificazioneCEIRef.current.handleOptionSelect(initialAction);\n      }, 100);\n    }\n  }, [location.pathname]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(SelectedCantiereDisplay, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        sx: {\n          mt: 2\n        },\n        children: \"Certificazione Cavi CEI 64-8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"text.secondary\",\n        children: \"Sistema di certificazione conforme alle normative CEI 64-8 e IEC 60364\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Menu Certificazione\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), menuOptions.map((category, categoryIndex) => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"primary\",\n              sx: {\n                mb: 1,\n                fontWeight: 'bold'\n              },\n              children: category.category\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: category.items.map(item => /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => handleOptionSelect(item.key),\n                sx: {\n                  borderRadius: 1,\n                  mb: 0.5,\n                  '&:hover': {\n                    backgroundColor: 'action.hover'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  sx: {\n                    minWidth: 36\n                  },\n                  children: item.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: item.label,\n                  secondary: item.description,\n                  primaryTypographyProps: {\n                    fontSize: '0.875rem'\n                  },\n                  secondaryTypographyProps: {\n                    fontSize: '0.75rem'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 23\n                }, this)]\n              }, item.key, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), categoryIndex < menuOptions.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 60\n            }, this)]\n          }, categoryIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 9,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            minHeight: '600px'\n          },\n          children: cantiereId ? /*#__PURE__*/_jsxDEV(CertificazioneCEI64_8, {\n            ref: certificazioneCEIRef,\n            cantiereId: cantiereId,\n            onSuccess: handleSuccess,\n            onError: handleError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            children: \"Seleziona un cantiere per accedere alle funzionalit\\xE0 di certificazione CEI 64-8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        sx: {\n          width: '100%'\n        },\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 183,\n    columnNumber: 5\n  }, this);\n};\n_s(CertificazioneCEI64_8Page, \"caJMekwWV5dJmck7xWJ0zqbbNp8=\", false, function () {\n  return [useParams, useLocation];\n});\n_c = CertificazioneCEI64_8Page;\nexport default CertificazioneCEI64_8Page;\nvar _c;\n$RefreshReg$(_c, \"CertificazioneCEI64_8Page\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useParams", "useLocation", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Divider", "<PERSON><PERSON>", "Snackbar", "Assignment", "AssignmentIcon", "Science", "ScienceIcon", "Warning", "WarningIcon", "Assessment", "ReportIcon", "Build", "BuildIcon", "Visibility", "ViewIcon", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Dashboard", "DashboardIcon", "CertificazioneCEI64_8", "SelectedCantiereDisplay", "jsxDEV", "_jsxDEV", "CertificazioneCEI64_8Page", "_s", "cantiereId", "location", "snackbar", "setSnackbar", "open", "message", "severity", "certificazioneCEIRef", "getInitialAction", "path", "pathname", "includes", "handleSuccess", "handleError", "handleCloseSnackbar", "menuOptions", "category", "items", "key", "label", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "handleOptionSelect", "optionKey", "current", "useEffect", "initialAction", "setTimeout", "sx", "p", "children", "mb", "variant", "gutterBottom", "mt", "color", "container", "spacing", "item", "xs", "md", "map", "categoryIndex", "fontWeight", "dense", "onClick", "borderRadius", "backgroundColor", "min<PERSON><PERSON><PERSON>", "primary", "secondary", "primaryTypographyProps", "fontSize", "secondaryTypographyProps", "length", "my", "minHeight", "ref", "onSuccess", "onError", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/CertificazioneCEI64_8Page.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { useParams, useLocation } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  Button,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Divider,\n  Alert,\n  Snackbar\n} from '@mui/material';\nimport {\n  Assignment as AssignmentIcon,\n  Science as ScienceIcon,\n  Warning as WarningIcon,\n  Assessment as ReportIcon,\n  Build as BuildIcon,\n  Visibility as ViewIcon,\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Dashboard as DashboardIcon\n} from '@mui/icons-material';\n\nimport CertificazioneCEI64_8 from '../../components/certificazione/CertificazioneCEI64_8';\nimport SelectedCantiereDisplay from '../../components/common/SelectedCantiereDisplay';\n\nconst CertificazioneCEI64_8Page = () => {\n  const { cantiereId } = useParams();\n  const location = useLocation();\n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });\n  \n  // Ref per il componente principale\n  const certificazioneCEIRef = useRef();\n\n  // Determina l'azione iniziale basata sulla route\n  const getInitialAction = () => {\n    const path = location.pathname;\n    if (path.includes('/dashboard')) return 'dashboardCEI';\n    if (path.includes('/rapporti')) return 'visualizzaRapporti';\n    if (path.includes('/prove')) return 'visualizzaProve';\n    if (path.includes('/non-conformita')) return 'visualizzaNonConformita';\n    if (path.includes('/strumenti')) return 'gestioneStrumenti';\n    return 'dashboardCEI';\n  };\n\n  // Gestisce i messaggi di successo\n  const handleSuccess = (message) => {\n    setSnackbar({ open: true, message, severity: 'success' });\n  };\n\n  // Gestisce i messaggi di errore\n  const handleError = (message) => {\n    setSnackbar({ open: true, message, severity: 'error' });\n  };\n\n  // Chiude il snackbar\n  const handleCloseSnackbar = () => {\n    setSnackbar({ ...snackbar, open: false });\n  };\n\n  // Opzioni del menu per la certificazione CEI 64-8\n  const menuOptions = [\n    {\n      category: 'Dashboard',\n      items: [\n        {\n          key: 'dashboardCEI',\n          label: 'Dashboard CEI 64-8',\n          icon: <DashboardIcon />,\n          description: 'Panoramica generale del sistema di certificazione'\n        }\n      ]\n    },\n    {\n      category: 'Rapporti Generali',\n      items: [\n        {\n          key: 'visualizzaRapporti',\n          label: 'Visualizza Rapporti',\n          icon: <ViewIcon />,\n          description: 'Visualizza tutti i rapporti generali di collaudo'\n        },\n        {\n          key: 'creaRapporto',\n          label: 'Nuovo Rapporto',\n          icon: <AddIcon />,\n          description: 'Crea un nuovo rapporto generale di collaudo'\n        },\n        {\n          key: 'modificaRapporto',\n          label: 'Modifica Rapporto',\n          icon: <EditIcon />,\n          description: 'Modifica un rapporto esistente'\n        },\n        {\n          key: 'eliminaRapporto',\n          label: 'Elimina Rapporto',\n          icon: <DeleteIcon />,\n          description: 'Elimina un rapporto generale'\n        }\n      ]\n    },\n    {\n      category: 'Certificazioni e Prove',\n      items: [\n        {\n          key: 'visualizzaCertificazioni',\n          label: 'Visualizza Certificazioni',\n          icon: <ViewIcon />,\n          description: 'Visualizza tutte le certificazioni cavi'\n        },\n        {\n          key: 'creaCertificazione',\n          label: 'Nuova Certificazione',\n          icon: <AddIcon />,\n          description: 'Crea una nuova certificazione cavo'\n        },\n        {\n          key: 'visualizzaProve',\n          label: 'Prove Dettagliate',\n          icon: <ScienceIcon />,\n          description: 'Gestisci le prove dettagliate per conformità CEI 64-8'\n        },\n        {\n          key: 'creaProva',\n          label: 'Nuova Prova',\n          icon: <AddIcon />,\n          description: 'Crea una nuova prova dettagliata'\n        }\n      ]\n    },\n    {\n      category: 'Non Conformità',\n      items: [\n        {\n          key: 'visualizzaNonConformita',\n          label: 'Visualizza Non Conformità',\n          icon: <WarningIcon />,\n          description: 'Gestisci le non conformità rilevate'\n        }\n      ]\n    },\n    {\n      category: 'Strumenti',\n      items: [\n        {\n          key: 'gestioneStrumenti',\n          label: 'Gestione Strumenti',\n          icon: <BuildIcon />,\n          description: 'Gestisci gli strumenti di misura certificati'\n        }\n      ]\n    }\n  ];\n\n  // Gestisce la selezione di un'opzione\n  const handleOptionSelect = (optionKey) => {\n    if (certificazioneCEIRef.current) {\n      certificazioneCEIRef.current.handleOptionSelect(optionKey);\n    }\n  };\n\n  // Esegue l'azione iniziale\n  React.useEffect(() => {\n    const initialAction = getInitialAction();\n    if (initialAction && certificazioneCEIRef.current) {\n      setTimeout(() => {\n        certificazioneCEIRef.current.handleOptionSelect(initialAction);\n      }, 100);\n    }\n  }, [location.pathname]);\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ mb: 3 }}>\n        <SelectedCantiereDisplay />\n        <Typography variant=\"h4\" gutterBottom sx={{ mt: 2 }}>\n          Certificazione Cavi CEI 64-8\n        </Typography>\n        <Typography variant=\"body1\" color=\"text.secondary\">\n          Sistema di certificazione conforme alle normative CEI 64-8 e IEC 60364\n        </Typography>\n      </Box>\n\n      <Grid container spacing={3}>\n        {/* Menu laterale */}\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Menu Certificazione\n            </Typography>\n            \n            {menuOptions.map((category, categoryIndex) => (\n              <Box key={categoryIndex} sx={{ mb: 2 }}>\n                <Typography variant=\"subtitle2\" color=\"primary\" sx={{ mb: 1, fontWeight: 'bold' }}>\n                  {category.category}\n                </Typography>\n                <List dense>\n                  {category.items.map((item) => (\n                    <ListItemButton\n                      key={item.key}\n                      onClick={() => handleOptionSelect(item.key)}\n                      sx={{ \n                        borderRadius: 1, \n                        mb: 0.5,\n                        '&:hover': {\n                          backgroundColor: 'action.hover'\n                        }\n                      }}\n                    >\n                      <ListItemIcon sx={{ minWidth: 36 }}>\n                        {item.icon}\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={item.label}\n                        secondary={item.description}\n                        primaryTypographyProps={{ fontSize: '0.875rem' }}\n                        secondaryTypographyProps={{ fontSize: '0.75rem' }}\n                      />\n                    </ListItemButton>\n                  ))}\n                </List>\n                {categoryIndex < menuOptions.length - 1 && <Divider sx={{ my: 1 }} />}\n              </Box>\n            ))}\n          </Paper>\n        </Grid>\n\n        {/* Contenuto principale */}\n        <Grid item xs={12} md={9}>\n          <Paper sx={{ p: 3, minHeight: '600px' }}>\n            {cantiereId ? (\n              <CertificazioneCEI64_8\n                ref={certificazioneCEIRef}\n                cantiereId={cantiereId}\n                onSuccess={handleSuccess}\n                onError={handleError}\n              />\n            ) : (\n              <Alert severity=\"warning\">\n                Seleziona un cantiere per accedere alle funzionalità di certificazione CEI 64-8\n              </Alert>\n            )}\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Snackbar per messaggi */}\n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert \n          onClose={handleCloseSnackbar} \n          severity={snackbar.severity}\n          sx={{ width: '100%' }}\n        >\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default CertificazioneCEI64_8Page;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,QAAQ,QACH,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,UAAU,EACxBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,QAAQ,EACtBC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAE5B,OAAOC,qBAAqB,MAAM,uDAAuD;AACzF,OAAOC,uBAAuB,MAAM,iDAAiD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtF,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM;IAAEC;EAAW,CAAC,GAAG3C,SAAS,CAAC,CAAC;EAClC,MAAM4C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC;IAAEiD,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;;EAE3F;EACA,MAAMC,oBAAoB,GAAGnD,MAAM,CAAC,CAAC;;EAErC;EACA,MAAMoD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,IAAI,GAAGR,QAAQ,CAACS,QAAQ;IAC9B,IAAID,IAAI,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,cAAc;IACtD,IAAIF,IAAI,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,oBAAoB;IAC3D,IAAIF,IAAI,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,iBAAiB;IACrD,IAAIF,IAAI,CAACE,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,yBAAyB;IACtE,IAAIF,IAAI,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,mBAAmB;IAC3D,OAAO,cAAc;EACvB,CAAC;;EAED;EACA,MAAMC,aAAa,GAAIP,OAAO,IAAK;IACjCF,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMO,WAAW,GAAIR,OAAO,IAAK;IAC/BF,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC,QAAQ,EAAE;IAAQ,CAAC,CAAC;EACzD,CAAC;;EAED;EACA,MAAMQ,mBAAmB,GAAGA,CAAA,KAAM;IAChCX,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEE,IAAI,EAAE;IAAM,CAAC,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMW,WAAW,GAAG,CAClB;IACEC,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAE,CACL;MACEC,GAAG,EAAE,cAAc;MACnBC,KAAK,EAAE,oBAAoB;MAC3BC,IAAI,eAAEvB,OAAA,CAACJ,aAAa;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvBC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC,EACD;IACET,QAAQ,EAAE,mBAAmB;IAC7BC,KAAK,EAAE,CACL;MACEC,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE,qBAAqB;MAC5BC,IAAI,eAAEvB,OAAA,CAACZ,QAAQ;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAClBC,WAAW,EAAE;IACf,CAAC,EACD;MACEP,GAAG,EAAE,cAAc;MACnBC,KAAK,EAAE,gBAAgB;MACvBC,IAAI,eAAEvB,OAAA,CAACV,OAAO;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACjBC,WAAW,EAAE;IACf,CAAC,EACD;MACEP,GAAG,EAAE,kBAAkB;MACvBC,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,eAAEvB,OAAA,CAACR,QAAQ;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAClBC,WAAW,EAAE;IACf,CAAC,EACD;MACEP,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE,kBAAkB;MACzBC,IAAI,eAAEvB,OAAA,CAACN,UAAU;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpBC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC,EACD;IACET,QAAQ,EAAE,wBAAwB;IAClCC,KAAK,EAAE,CACL;MACEC,GAAG,EAAE,0BAA0B;MAC/BC,KAAK,EAAE,2BAA2B;MAClCC,IAAI,eAAEvB,OAAA,CAACZ,QAAQ;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAClBC,WAAW,EAAE;IACf,CAAC,EACD;MACEP,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE,sBAAsB;MAC7BC,IAAI,eAAEvB,OAAA,CAACV,OAAO;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACjBC,WAAW,EAAE;IACf,CAAC,EACD;MACEP,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,eAAEvB,OAAA,CAACpB,WAAW;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrBC,WAAW,EAAE;IACf,CAAC,EACD;MACEP,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE,aAAa;MACpBC,IAAI,eAAEvB,OAAA,CAACV,OAAO;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACjBC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC,EACD;IACET,QAAQ,EAAE,gBAAgB;IAC1BC,KAAK,EAAE,CACL;MACEC,GAAG,EAAE,yBAAyB;MAC9BC,KAAK,EAAE,2BAA2B;MAClCC,IAAI,eAAEvB,OAAA,CAAClB,WAAW;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrBC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC,EACD;IACET,QAAQ,EAAE,WAAW;IACrBC,KAAK,EAAE,CACL;MACEC,GAAG,EAAE,mBAAmB;MACxBC,KAAK,EAAE,oBAAoB;MAC3BC,IAAI,eAAEvB,OAAA,CAACd,SAAS;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnBC,WAAW,EAAE;IACf,CAAC;EAEL,CAAC,CACF;;EAED;EACA,MAAMC,kBAAkB,GAAIC,SAAS,IAAK;IACxC,IAAIpB,oBAAoB,CAACqB,OAAO,EAAE;MAChCrB,oBAAoB,CAACqB,OAAO,CAACF,kBAAkB,CAACC,SAAS,CAAC;IAC5D;EACF,CAAC;;EAED;EACAzE,KAAK,CAAC2E,SAAS,CAAC,MAAM;IACpB,MAAMC,aAAa,GAAGtB,gBAAgB,CAAC,CAAC;IACxC,IAAIsB,aAAa,IAAIvB,oBAAoB,CAACqB,OAAO,EAAE;MACjDG,UAAU,CAAC,MAAM;QACfxB,oBAAoB,CAACqB,OAAO,CAACF,kBAAkB,CAACI,aAAa,CAAC;MAChE,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EAAE,CAAC7B,QAAQ,CAACS,QAAQ,CAAC,CAAC;EAEvB,oBACEb,OAAA,CAACtC,GAAG;IAACyE,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAEhBrC,OAAA,CAACtC,GAAG;MAACyE,EAAE,EAAE;QAAEG,EAAE,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACjBrC,OAAA,CAACF,uBAAuB;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3B3B,OAAA,CAACrC,UAAU;QAAC4E,OAAO,EAAC,IAAI;QAACC,YAAY;QAACL,EAAE,EAAE;UAAEM,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EAAC;MAErD;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb3B,OAAA,CAACrC,UAAU;QAAC4E,OAAO,EAAC,OAAO;QAACG,KAAK,EAAC,gBAAgB;QAAAL,QAAA,EAAC;MAEnD;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEN3B,OAAA,CAACnC,IAAI;MAAC8E,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAP,QAAA,gBAEzBrC,OAAA,CAACnC,IAAI;QAACgF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACvBrC,OAAA,CAACpC,KAAK;UAACuE,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAC,QAAA,gBAClBrC,OAAA,CAACrC,UAAU;YAAC4E,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAH,QAAA,EAAC;UAEtC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZT,WAAW,CAAC8B,GAAG,CAAC,CAAC7B,QAAQ,EAAE8B,aAAa,kBACvCjD,OAAA,CAACtC,GAAG;YAAqByE,EAAE,EAAE;cAAEG,EAAE,EAAE;YAAE,CAAE;YAAAD,QAAA,gBACrCrC,OAAA,CAACrC,UAAU;cAAC4E,OAAO,EAAC,WAAW;cAACG,KAAK,EAAC,SAAS;cAACP,EAAE,EAAE;gBAAEG,EAAE,EAAE,CAAC;gBAAEY,UAAU,EAAE;cAAO,CAAE;cAAAb,QAAA,EAC/ElB,QAAQ,CAACA;YAAQ;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACb3B,OAAA,CAAC/B,IAAI;cAACkF,KAAK;cAAAd,QAAA,EACRlB,QAAQ,CAACC,KAAK,CAAC4B,GAAG,CAAEH,IAAI,iBACvB7C,OAAA,CAAC3B,cAAc;gBAEb+E,OAAO,EAAEA,CAAA,KAAMvB,kBAAkB,CAACgB,IAAI,CAACxB,GAAG,CAAE;gBAC5Cc,EAAE,EAAE;kBACFkB,YAAY,EAAE,CAAC;kBACff,EAAE,EAAE,GAAG;kBACP,SAAS,EAAE;oBACTgB,eAAe,EAAE;kBACnB;gBACF,CAAE;gBAAAjB,QAAA,gBAEFrC,OAAA,CAAC5B,YAAY;kBAAC+D,EAAE,EAAE;oBAAEoB,QAAQ,EAAE;kBAAG,CAAE;kBAAAlB,QAAA,EAChCQ,IAAI,CAACtB;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACf3B,OAAA,CAAC7B,YAAY;kBACXqF,OAAO,EAAEX,IAAI,CAACvB,KAAM;kBACpBmC,SAAS,EAAEZ,IAAI,CAACjB,WAAY;kBAC5B8B,sBAAsB,EAAE;oBAAEC,QAAQ,EAAE;kBAAW,CAAE;kBACjDC,wBAAwB,EAAE;oBAAED,QAAQ,EAAE;kBAAU;gBAAE;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA,GAlBGkB,IAAI,CAACxB,GAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBC,CACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACNsB,aAAa,GAAG/B,WAAW,CAAC2C,MAAM,GAAG,CAAC,iBAAI7D,OAAA,CAAC1B,OAAO;cAAC6D,EAAE,EAAE;gBAAE2B,EAAE,EAAE;cAAE;YAAE;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GA7B7DsB,aAAa;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8BlB,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP3B,OAAA,CAACnC,IAAI;QAACgF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACvBrC,OAAA,CAACpC,KAAK;UAACuE,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAE2B,SAAS,EAAE;UAAQ,CAAE;UAAA1B,QAAA,EACrClC,UAAU,gBACTH,OAAA,CAACH,qBAAqB;YACpBmE,GAAG,EAAEtD,oBAAqB;YAC1BP,UAAU,EAAEA,UAAW;YACvB8D,SAAS,EAAElD,aAAc;YACzBmD,OAAO,EAAElD;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,gBAEF3B,OAAA,CAACzB,KAAK;YAACkC,QAAQ,EAAC,SAAS;YAAA4B,QAAA,EAAC;UAE1B;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP3B,OAAA,CAACxB,QAAQ;MACP+B,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpB4D,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEnD,mBAAoB;MAC7BoD,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAlC,QAAA,eAE1DrC,OAAA,CAACzB,KAAK;QACJ6F,OAAO,EAAEnD,mBAAoB;QAC7BR,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAC5B0B,EAAE,EAAE;UAAEqC,KAAK,EAAE;QAAO,CAAE;QAAAnC,QAAA,EAErBhC,QAAQ,CAACG;MAAO;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACzB,EAAA,CA/OID,yBAAyB;EAAA,QACNzC,SAAS,EACfC,WAAW;AAAA;AAAAgH,EAAA,GAFxBxE,yBAAyB;AAiP/B,eAAeA,yBAAyB;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}