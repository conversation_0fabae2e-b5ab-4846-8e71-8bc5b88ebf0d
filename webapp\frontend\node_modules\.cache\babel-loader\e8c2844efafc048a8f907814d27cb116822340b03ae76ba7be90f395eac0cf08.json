{"ast": null, "code": "'use client';\n\nimport PropTypes from 'prop-types';\nimport createContainer from './createContainer';\n\n/**\n *\n * Demos:\n *\n * - [Container (Material UI)](https://mui.com/material-ui/react-container/)\n * - [Container (MUI System)](https://mui.com/system/react-container/)\n *\n * API:\n *\n * - [Container API](https://mui.com/system/api/container/)\n */\nconst Container = createContainer();\nprocess.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * Set the max-width to match the min-width of the current breakpoint.\n   * This is useful if you'd prefer to design for a fixed set of sizes\n   * instead of trying to accommodate a fully fluid viewport.\n   * It's fluid by default.\n   * @default false\n   */\n  fixed: PropTypes.bool,\n  /**\n   * Determine the max-width of the container.\n   * The container width grows with the size of the screen.\n   * Set to `false` to disable `maxWidth`.\n   * @default 'lg'\n   */\n  maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Container;", "map": {"version": 3, "names": ["PropTypes", "createContainer", "Container", "process", "env", "NODE_ENV", "propTypes", "children", "node", "classes", "object", "component", "elementType", "disableGutters", "bool", "fixed", "max<PERSON><PERSON><PERSON>", "oneOfType", "oneOf", "string", "sx", "arrayOf", "func"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/system/esm/Container/Container.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport createContainer from './createContainer';\n\n/**\n *\n * Demos:\n *\n * - [Container (Material UI)](https://mui.com/material-ui/react-container/)\n * - [Container (MUI System)](https://mui.com/system/react-container/)\n *\n * API:\n *\n * - [Container API](https://mui.com/system/api/container/)\n */\nconst Container = createContainer();\nprocess.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * Set the max-width to match the min-width of the current breakpoint.\n   * This is useful if you'd prefer to design for a fixed set of sizes\n   * instead of trying to accommodate a fully fluid viewport.\n   * It's fluid by default.\n   * @default false\n   */\n  fixed: PropTypes.bool,\n  /**\n   * Determine the max-width of the container.\n   * The container width grows with the size of the screen.\n   * Set to `false` to disable `maxWidth`.\n   * @default 'lg'\n   */\n  maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Container;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAOC,eAAe,MAAM,mBAAmB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAGD,eAAe,CAAC,CAAC;AACnCE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGH,SAAS,CAACI,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEP,SAAS,CAACQ,IAAI;EACxB;AACF;AACA;EACEC,OAAO,EAAET,SAAS,CAACU,MAAM;EACzB;AACF;AACA;AACA;EACEC,SAAS,EAAEX,SAAS,CAACY,WAAW;EAChC;AACF;AACA;AACA;EACEC,cAAc,EAAEb,SAAS,CAACc,IAAI;EAC9B;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,KAAK,EAAEf,SAAS,CAACc,IAAI;EACrB;AACF;AACA;AACA;AACA;AACA;EACEE,QAAQ,EAAEhB,SAAS,CAAC,sCAAsCiB,SAAS,CAAC,CAACjB,SAAS,CAACkB,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,EAAElB,SAAS,CAACmB,MAAM,CAAC,CAAC;EAC/I;AACF;AACA;EACEC,EAAE,EAAEpB,SAAS,CAACiB,SAAS,CAAC,CAACjB,SAAS,CAACqB,OAAO,CAACrB,SAAS,CAACiB,SAAS,CAAC,CAACjB,SAAS,CAACsB,IAAI,EAAEtB,SAAS,CAACU,MAAM,EAAEV,SAAS,CAACc,IAAI,CAAC,CAAC,CAAC,EAAEd,SAAS,CAACsB,IAAI,EAAEtB,SAAS,CAACU,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAeR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}