{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\TopNavbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { AppBar, Toolbar, Box, Button, Menu, MenuItem, Typography, IconButton, Divider, Avatar, Tooltip } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon, Logout as LogoutIcon, KeyboardArrowDown as ArrowDownIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport './TopNavbar.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TopNavbar = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    logout,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  const [posaAnchorEl, setPosaAnchorEl] = useState(null);\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n  const [reportAnchorEl, setReportAnchorEl] = useState(null);\n  const [certificazioneAnchorEl, setCertificazioneAnchorEl] = useState(null);\n  const [comandeAnchorEl, setComandeAnchorEl] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = setAnchorEl => {\n    setAnchorEl(null);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    // Gestione speciale per il percorso Home\n    if (path === '/dashboard') {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n        // Recupera l'ID del cantiere dal localStorage\n        const cantiereId = localStorage.getItem('selectedCantiereId');\n        if (cantiereId) {\n          navigate(`/dashboard/cantieri/${cantiereId}`);\n        } else {\n          navigate('/dashboard/cantieri');\n        }\n      }\n      // Fallback per altri tipi di utenti\n      else {\n        navigate(path);\n      }\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    handleMenuClose(setPosaAnchorEl);\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n    handleMenuClose(setReportAnchorEl);\n    handleMenuClose(setCertificazioneAnchorEl);\n    handleMenuClose(setComandeAnchorEl);\n  };\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = path => {\n    return location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/_jsxDEV(AppBar, {\n    position: \"static\",\n    color: \"default\",\n    elevation: 1,\n    sx: {\n      zIndex: 1100\n    },\n    className: \"excel-style-menu\",\n    children: /*#__PURE__*/_jsxDEV(Toolbar, {\n      variant: \"dense\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        color: \"inherit\",\n        onClick: () => navigateTo('/dashboard'),\n        startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 22\n        }, this),\n        sx: {\n          mr: 1\n        },\n        className: isActive('/dashboard') ? 'active-button' : '',\n        children: isImpersonating ? \"Torna al Menu Admin\" : (user === null || user === void 0 ? void 0 : user.role) === 'owner' ? \"Pannello Admin\" : (user === null || user === void 0 ? void 0 : user.role) === 'user' ? \"I Miei Cantieri\" : (user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        orientation: \"vertical\",\n        flexItem: true,\n        sx: {\n          mx: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), (user === null || user === void 0 ? void 0 : user.role) === 'owner' && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          \"aria-controls\": \"admin-menu\",\n          \"aria-haspopup\": \"true\",\n          onClick: e => handleMenuOpen(e, setAdminAnchorEl),\n          endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 24\n          }, this),\n          sx: {\n            mr: 1\n          },\n          className: isPartOfActive('/dashboard/admin') ? 'active-button' : '',\n          children: \"Amministrazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Menu, {\n          id: \"admin-menu\",\n          anchorEl: adminAnchorEl,\n          keepMounted: true,\n          open: Boolean(adminAnchorEl),\n          onClose: () => handleMenuClose(setAdminAnchorEl),\n          className: \"excel-style-submenu\",\n          children: /*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: () => navigateTo('/dashboard/admin'),\n            children: \"Pannello Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), ((user === null || user === void 0 ? void 0 : user.role) !== 'owner' || (user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          \"aria-controls\": \"cantieri-menu\",\n          \"aria-haspopup\": \"true\",\n          onClick: e => handleMenuOpen(e, setCantieriAnchorEl),\n          endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 24\n          }, this),\n          sx: {\n            mr: 1\n          },\n          className: isPartOfActive('/dashboard/cantieri') ? 'active-button' : '',\n          children: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Menu, {\n          id: \"cantieri-menu\",\n          anchorEl: cantieriAnchorEl,\n          keepMounted: true,\n          open: Boolean(cantieriAnchorEl),\n          onClose: () => handleMenuClose(setCantieriAnchorEl),\n          className: \"excel-style-submenu\",\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: () => navigateTo('/dashboard/cantieri'),\n            className: isActive('/dashboard/cantieri') ? 'active-item' : '',\n            children: \"Lista Cantieri\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: () => navigateTo(`/dashboard/cantieri/${selectedCantiereId}`),\n            className: isActive(`/dashboard/cantieri/${selectedCantiereId}`) ? 'active-item' : '',\n            children: [\"Cantiere: \", selectedCantiereName || selectedCantiereId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"cavi-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setCaviAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi') ? 'active-button' : '',\n            children: \"Gestione Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"cavi-menu\",\n            anchorEl: caviAnchorEl,\n            keepMounted: true,\n            open: Boolean(caviAnchorEl),\n            onClose: () => handleMenuClose(setCaviAnchorEl),\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/visualizza'),\n              children: \"Visualizza Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: e => {\n                e.stopPropagation();\n                handleMenuOpen(e, setPosaAnchorEl);\n              },\n              sx: {\n                position: 'relative'\n              },\n              children: [\"Posa e Collegamenti \", /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n                fontSize: \"small\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: e => {\n                e.stopPropagation();\n                handleMenuOpen(e, setParcoAnchorEl);\n              },\n              sx: {\n                position: 'relative'\n              },\n              children: [\"Parco Cavi \", /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n                fontSize: \"small\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 32\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: e => {\n                e.stopPropagation();\n                handleMenuOpen(e, setExcelAnchorEl);\n              },\n              sx: {\n                position: 'relative'\n              },\n              children: [\"Gestione Excel \", /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n                fontSize: \"small\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 36\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: e => {\n                e.stopPropagation();\n                handleMenuOpen(e, setReportAnchorEl);\n              },\n              sx: {\n                position: 'relative'\n              },\n              children: [\"Report \", /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n                fontSize: \"small\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 28\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: e => {\n                e.stopPropagation();\n                handleMenuOpen(e, setCertificazioneAnchorEl);\n              },\n              sx: {\n                position: 'relative'\n              },\n              children: [\"Certificazione Cavi \", /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n                fontSize: \"small\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: e => {\n                e.stopPropagation();\n                handleMenuOpen(e, setComandeAnchorEl);\n              },\n              sx: {\n                position: 'relative'\n              },\n              children: [\"Gestione Comande \", /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n                fontSize: \"small\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 38\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"posa-menu\",\n            anchorEl: posaAnchorEl,\n            keepMounted: true,\n            open: Boolean(posaAnchorEl),\n            onClose: () => handleMenuClose(setPosaAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/inserisci-metri'),\n              children: \"Inserisci metri posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/modifica-cavo'),\n              children: \"Modifica cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/aggiungi-cavo'),\n              children: \"Aggiungi nuovo cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/elimina-cavo'),\n              children: \"Elimina cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/modifica-bobina'),\n              children: \"Modifica bobina cavo posato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/collegamenti'),\n              children: \"Gestisci collegamenti cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"parco-menu\",\n            anchorEl: parcoAnchorEl,\n            keepMounted: true,\n            open: Boolean(parcoAnchorEl),\n            onClose: () => handleMenuClose(setParcoAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/visualizza'),\n              children: \"Visualizza Bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/crea'),\n              children: \"Crea Nuova Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/modifica'),\n              children: \"Modifica Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/elimina'),\n              children: \"Elimina Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/storico'),\n              children: \"Storico Utilizzo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"excel-menu\",\n            anchorEl: excelAnchorEl,\n            keepMounted: true,\n            open: Boolean(excelAnchorEl),\n            onClose: () => handleMenuClose(setExcelAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/importa-cavi'),\n              children: \"Importa cavi da Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/importa-bobine'),\n              children: \"Importa parco bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/esporta-cavi'),\n              children: \"Esporta cavi in Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/esporta-bobine'),\n              children: \"Esporta parco bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"report-menu\",\n            anchorEl: reportAnchorEl,\n            keepMounted: true,\n            open: Boolean(reportAnchorEl),\n            onClose: () => handleMenuClose(setReportAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/avanzamento'),\n              children: \"Report Avanzamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/boq'),\n              children: \"Bill of Quantities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/utilizzo-bobine'),\n              children: \"Report Utilizzo Bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/statistiche'),\n              children: \"Statistiche Cantiere\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"certificazione-menu\",\n            anchorEl: certificazioneAnchorEl,\n            keepMounted: true,\n            open: Boolean(certificazioneAnchorEl),\n            onClose: () => handleMenuClose(setCertificazioneAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/visualizza'),\n              children: \"Visualizza certificazioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/filtra'),\n              children: \"Filtra per cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/crea'),\n              children: \"Crea certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/modifica'),\n              children: \"Modifica certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/elimina'),\n              children: \"Elimina certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/strumenti'),\n              children: \"Gestione strumenti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"comande-menu\",\n            anchorEl: comandeAnchorEl,\n            keepMounted: true,\n            open: Boolean(comandeAnchorEl),\n            onClose: () => handleMenuClose(setComandeAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/visualizza'),\n              children: \"Visualizza comande\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/crea'),\n              children: \"Crea nuova comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/modifica'),\n              children: \"Modifica comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/elimina'),\n              children: \"Elimina comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/stampa'),\n              children: \"Stampa comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/assegna'),\n              children: \"Assegna comanda a cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flexGrow: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [isImpersonating && impersonatedUser && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          sx: {\n            mr: 2\n          },\n          children: [\"Accesso come: \", /*#__PURE__*/_jsxDEV(\"b\", {\n            children: impersonatedUser.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mr: 2\n          },\n          children: (user === null || user === void 0 ? void 0 : user.username) || ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Logout\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            color: \"inherit\",\n            onClick: handleLogout,\n            edge: \"end\",\n            children: /*#__PURE__*/_jsxDEV(LogoutIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_s(TopNavbar, \"KcFPmHsEUY/2zSYWn487+PtaWNk=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = TopNavbar;\nexport default TopNavbar;\nvar _c;\n$RefreshReg$(_c, \"TopNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "AppBar", "<PERSON><PERSON><PERSON>", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Typography", "IconButton", "Divider", "Avatar", "<PERSON><PERSON><PERSON>", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "Logout", "LogoutIcon", "KeyboardArrowDown", "ArrowDownIcon", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TopNavbar", "_s", "navigate", "location", "user", "logout", "isImpersonating", "impersonated<PERSON><PERSON>", "homeAnchorEl", "setHomeAnchorEl", "adminAnchorEl", "setAdminAnchorEl", "cantieriAnchorEl", "setCantieriAnchorEl", "caviAnchorEl", "setCaviAnchorEl", "posaAnchorEl", "setPosaAnchorEl", "parcoAnchorEl", "setParcoAnchorEl", "excelAnchorEl", "setExcelAnchorEl", "reportAnchorEl", "setReportAnchorEl", "certificazioneAnchorEl", "setCertificazioneAnchorEl", "comandeAnchorEl", "setComandeAnchorEl", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "handleMenuOpen", "event", "setAnchorEl", "currentTarget", "handleMenuClose", "navigateTo", "path", "role", "cantiereId", "handleLogout", "isActive", "pathname", "isPartOfActive", "startsWith", "position", "color", "elevation", "sx", "zIndex", "className", "children", "variant", "onClick", "startIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mr", "orientation", "flexItem", "mx", "e", "endIcon", "id", "anchorEl", "keepMounted", "open", "Boolean", "onClose", "username", "stopPropagation", "fontSize", "ml", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "flexGrow", "display", "alignItems", "title", "edge", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/TopNavbar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  AppBar,\n  Toolbar,\n  Box,\n  Button,\n  Menu,\n  MenuItem,\n  Typography,\n  IconButton,\n  Divider,\n  Avatar,\n  Tooltip\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon,\n  Logout as LogoutIcon,\n  KeyboardArrowDown as ArrowDownIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport './TopNavbar.css';\n\nconst TopNavbar = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, logout, isImpersonating, impersonatedUser } = useAuth();\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  const [posaAnchorEl, setPosaAnchorEl] = useState(null);\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n  const [reportAnchorEl, setReportAnchorEl] = useState(null);\n  const [certificazioneAnchorEl, setCertificazioneAnchorEl] = useState(null);\n  const [comandeAnchorEl, setComandeAnchorEl] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = (setAnchorEl) => {\n    setAnchorEl(null);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    // Gestione speciale per il percorso Home\n    if (path === '/dashboard') {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if (user?.role === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if (user?.role === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if (user?.role === 'cantieri_user') {\n        // Recupera l'ID del cantiere dal localStorage\n        const cantiereId = localStorage.getItem('selectedCantiereId');\n        if (cantiereId) {\n          navigate(`/dashboard/cantieri/${cantiereId}`);\n        } else {\n          navigate('/dashboard/cantieri');\n        }\n      }\n      // Fallback per altri tipi di utenti\n      else {\n        navigate(path);\n      }\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    handleMenuClose(setPosaAnchorEl);\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n    handleMenuClose(setReportAnchorEl);\n    handleMenuClose(setCertificazioneAnchorEl);\n    handleMenuClose(setComandeAnchorEl);\n  };\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  return (\n    <AppBar position=\"static\" color=\"default\" elevation={1} sx={{ zIndex: 1100 }} className=\"excel-style-menu\">\n      <Toolbar variant=\"dense\">\n        {/* Logo/Home - Testo personalizzato in base al tipo di utente */}\n        <Button\n          color=\"inherit\"\n          onClick={() => navigateTo('/dashboard')}\n          startIcon={<HomeIcon />}\n          sx={{ mr: 1 }}\n          className={isActive('/dashboard') ? 'active-button' : ''}\n        >\n          {isImpersonating ? \"Torna al Menu Admin\" :\n           user?.role === 'owner' ? \"Pannello Admin\" :\n           user?.role === 'user' ? \"I Miei Cantieri\" :\n           user?.role === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\"}\n        </Button>\n        <Divider orientation=\"vertical\" flexItem sx={{ mx: 0.5 }} />\n\n        {/* Menu Amministratore (solo per admin) */}\n        {user?.role === 'owner' && (\n          <>\n            <Button\n              color=\"inherit\"\n              aria-controls=\"admin-menu\"\n              aria-haspopup=\"true\"\n              onClick={(e) => handleMenuOpen(e, setAdminAnchorEl)}\n              endIcon={<ArrowDownIcon />}\n              sx={{ mr: 1 }}\n              className={isPartOfActive('/dashboard/admin') ? 'active-button' : ''}\n            >\n              Amministrazione\n            </Button>\n            <Menu\n              id=\"admin-menu\"\n              anchorEl={adminAnchorEl}\n              keepMounted\n              open={Boolean(adminAnchorEl)}\n              onClose={() => handleMenuClose(setAdminAnchorEl)}\n              className=\"excel-style-submenu\"\n            >\n              <MenuItem onClick={() => navigateTo('/dashboard/admin')}>Pannello Admin</MenuItem>\n            </Menu>\n          </>\n        )}\n\n        {/* Menu per utenti standard e cantieri */}\n        {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (\n          <>\n            <Button\n              color=\"inherit\"\n              aria-controls=\"cantieri-menu\"\n              aria-haspopup=\"true\"\n              onClick={(e) => handleMenuOpen(e, setCantieriAnchorEl)}\n              endIcon={<ArrowDownIcon />}\n              sx={{ mr: 1 }}\n              className={isPartOfActive('/dashboard/cantieri') ? 'active-button' : ''}\n            >\n              {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"}\n            </Button>\n            <Menu\n              id=\"cantieri-menu\"\n              anchorEl={cantieriAnchorEl}\n              keepMounted\n              open={Boolean(cantieriAnchorEl)}\n              onClose={() => handleMenuClose(setCantieriAnchorEl)}\n              className=\"excel-style-submenu\"\n            >\n              <MenuItem\n                onClick={() => navigateTo('/dashboard/cantieri')}\n                className={isActive('/dashboard/cantieri') ? 'active-item' : ''}\n              >Lista Cantieri</MenuItem>\n              {selectedCantiereId && (\n                <MenuItem\n                  onClick={() => navigateTo(`/dashboard/cantieri/${selectedCantiereId}`)}\n                  className={isActive(`/dashboard/cantieri/${selectedCantiereId}`) ? 'active-item' : ''}\n                >\n                  Cantiere: {selectedCantiereName || selectedCantiereId}\n                </MenuItem>\n              )}\n            </Menu>\n\n            {/* Menu Cavi con sottomenu - visibile solo se un cantiere è selezionato */}\n            {selectedCantiereId && (\n              <>\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"cavi-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setCaviAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi') ? 'active-button' : ''}\n                >\n                  Gestione Cavi\n                </Button>\n                <Menu\n                  id=\"cavi-menu\"\n                  anchorEl={caviAnchorEl}\n                  keepMounted\n                  open={Boolean(caviAnchorEl)}\n                  onClose={() => handleMenuClose(setCaviAnchorEl)}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/visualizza')}>Visualizza Cavi</MenuItem>\n\n                  {/* Posa e Collegamenti */}\n                  <MenuItem\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleMenuOpen(e, setPosaAnchorEl);\n                    }}\n                    sx={{ position: 'relative' }}\n                  >\n                    Posa e Collegamenti <ArrowDownIcon fontSize=\"small\" sx={{ ml: 1 }} />\n                  </MenuItem>\n\n                  {/* Parco Cavi */}\n                  <MenuItem\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleMenuOpen(e, setParcoAnchorEl);\n                    }}\n                    sx={{ position: 'relative' }}\n                  >\n                    Parco Cavi <ArrowDownIcon fontSize=\"small\" sx={{ ml: 1 }} />\n                  </MenuItem>\n\n                  {/* Gestione Excel */}\n                  <MenuItem\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleMenuOpen(e, setExcelAnchorEl);\n                    }}\n                    sx={{ position: 'relative' }}\n                  >\n                    Gestione Excel <ArrowDownIcon fontSize=\"small\" sx={{ ml: 1 }} />\n                  </MenuItem>\n\n                  {/* Report */}\n                  <MenuItem\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleMenuOpen(e, setReportAnchorEl);\n                    }}\n                    sx={{ position: 'relative' }}\n                  >\n                    Report <ArrowDownIcon fontSize=\"small\" sx={{ ml: 1 }} />\n                  </MenuItem>\n\n                  {/* Certificazione Cavi */}\n                  <MenuItem\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleMenuOpen(e, setCertificazioneAnchorEl);\n                    }}\n                    sx={{ position: 'relative' }}\n                  >\n                    Certificazione Cavi <ArrowDownIcon fontSize=\"small\" sx={{ ml: 1 }} />\n                  </MenuItem>\n\n                  {/* Gestione Comande */}\n                  <MenuItem\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleMenuOpen(e, setComandeAnchorEl);\n                    }}\n                    sx={{ position: 'relative' }}\n                  >\n                    Gestione Comande <ArrowDownIcon fontSize=\"small\" sx={{ ml: 1 }} />\n                  </MenuItem>\n                </Menu>\n\n                {/* Sottomenu Posa e Collegamenti */}\n                <Menu\n                  id=\"posa-menu\"\n                  anchorEl={posaAnchorEl}\n                  keepMounted\n                  open={Boolean(posaAnchorEl)}\n                  onClose={() => handleMenuClose(setPosaAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/inserisci-metri')}>Inserisci metri posati</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/modifica-cavo')}>Modifica cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/aggiungi-cavo')}>Aggiungi nuovo cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/elimina-cavo')}>Elimina cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/modifica-bobina')}>Modifica bobina cavo posato</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/collegamenti')}>Gestisci collegamenti cavo</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Parco Cavi */}\n                <Menu\n                  id=\"parco-menu\"\n                  anchorEl={parcoAnchorEl}\n                  keepMounted\n                  open={Boolean(parcoAnchorEl)}\n                  onClose={() => handleMenuClose(setParcoAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/visualizza')}>Visualizza Bobine</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/crea')}>Crea Nuova Bobina</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/modifica')}>Modifica Bobina</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/elimina')}>Elimina Bobina</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/storico')}>Storico Utilizzo</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Gestione Excel */}\n                <Menu\n                  id=\"excel-menu\"\n                  anchorEl={excelAnchorEl}\n                  keepMounted\n                  open={Boolean(excelAnchorEl)}\n                  onClose={() => handleMenuClose(setExcelAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/importa-cavi')}>Importa cavi da Excel</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/importa-bobine')}>Importa parco bobine</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/esporta-cavi')}>Esporta cavi in Excel</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/esporta-bobine')}>Esporta parco bobine</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Report */}\n                <Menu\n                  id=\"report-menu\"\n                  anchorEl={reportAnchorEl}\n                  keepMounted\n                  open={Boolean(reportAnchorEl)}\n                  onClose={() => handleMenuClose(setReportAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/avanzamento')}>Report Avanzamento</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/boq')}>Bill of Quantities</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/utilizzo-bobine')}>Report Utilizzo Bobine</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/statistiche')}>Statistiche Cantiere</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Certificazione Cavi */}\n                <Menu\n                  id=\"certificazione-menu\"\n                  anchorEl={certificazioneAnchorEl}\n                  keepMounted\n                  open={Boolean(certificazioneAnchorEl)}\n                  onClose={() => handleMenuClose(setCertificazioneAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/visualizza')}>Visualizza certificazioni</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/filtra')}>Filtra per cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/crea')}>Crea certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/modifica')}>Modifica certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/elimina')}>Elimina certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/strumenti')}>Gestione strumenti</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Gestione Comande */}\n                <Menu\n                  id=\"comande-menu\"\n                  anchorEl={comandeAnchorEl}\n                  keepMounted\n                  open={Boolean(comandeAnchorEl)}\n                  onClose={() => handleMenuClose(setComandeAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/visualizza')}>Visualizza comande</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/crea')}>Crea nuova comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/modifica')}>Modifica comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/elimina')}>Elimina comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/stampa')}>Stampa comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/assegna')}>Assegna comanda a cavo</MenuItem>\n                </Menu>\n              </>\n            )}\n          </>\n        )}\n\n        {/* Spacer */}\n        <Box sx={{ flexGrow: 1 }} />\n\n        {/* Informazioni utente e logout */}\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          {isImpersonating && impersonatedUser && (\n            <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mr: 2 }}>\n              Accesso come: <b>{impersonatedUser.username}</b>\n            </Typography>\n          )}\n          <Typography variant=\"body2\" sx={{ mr: 2 }}>\n            {user?.username || ''}\n          </Typography>\n          <Tooltip title=\"Logout\">\n            <IconButton color=\"inherit\" onClick={handleLogout} edge=\"end\">\n              <LogoutIcon />\n            </IconButton>\n          </Tooltip>\n        </Box>\n      </Toolbar>\n    </AppBar>\n  );\n};\n\nexport default TopNavbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,MAAM,IAAIC,UAAU,EACpBC,iBAAiB,IAAIC,aAAa,QAC7B,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAMmC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmC,IAAI;IAAEC,MAAM;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGZ,OAAO,CAAC,CAAC;;EAErE;EACA,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2C,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC6C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACyD,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC1E,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM6D,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEzE;EACA,MAAME,cAAc,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;IAC7CA,WAAW,CAACD,KAAK,CAACE,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAIF,WAAW,IAAK;IACvCA,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAIC,IAAI,IAAK;IAC3B;IACA,IAAIA,IAAI,KAAK,YAAY,EAAE;MACzB;MACA,IAAIhC,eAAe,EAAE;QACnBJ,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,OAAO,EAAE;QAC/BrC,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,MAAM,EAAE;QAC9BrC,QAAQ,CAAC,qBAAqB,CAAC;MACjC;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,eAAe,EAAE;QACvC;QACA,MAAMC,UAAU,GAAGX,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QAC7D,IAAIU,UAAU,EAAE;UACdtC,QAAQ,CAAC,uBAAuBsC,UAAU,EAAE,CAAC;QAC/C,CAAC,MAAM;UACLtC,QAAQ,CAAC,qBAAqB,CAAC;QACjC;MACF;MACA;MAAA,KACK;QACHA,QAAQ,CAACoC,IAAI,CAAC;MAChB;IACF,CAAC,MAAM;MACLpC,QAAQ,CAACoC,IAAI,CAAC;IAChB;;IAEA;IACAF,eAAe,CAAC3B,eAAe,CAAC;IAChC2B,eAAe,CAACzB,gBAAgB,CAAC;IACjCyB,eAAe,CAACvB,mBAAmB,CAAC;IACpCuB,eAAe,CAACrB,eAAe,CAAC;IAChCqB,eAAe,CAACnB,eAAe,CAAC;IAChCmB,eAAe,CAACjB,gBAAgB,CAAC;IACjCiB,eAAe,CAACf,gBAAgB,CAAC;IACjCe,eAAe,CAACb,iBAAiB,CAAC;IAClCa,eAAe,CAACX,yBAAyB,CAAC;IAC1CW,eAAe,CAACT,kBAAkB,CAAC;EACrC,CAAC;EAED,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzBpC,MAAM,CAAC,CAAC;EACV,CAAC;;EAED;EACA,MAAMqC,QAAQ,GAAIJ,IAAI,IAAK;IACzB,OAAOnC,QAAQ,CAACwC,QAAQ,KAAKL,IAAI;EACnC,CAAC;;EAED;EACA,MAAMM,cAAc,GAAIN,IAAI,IAAK;IAC/B,OAAOnC,QAAQ,CAACwC,QAAQ,CAACE,UAAU,CAACP,IAAI,CAAC;EAC3C,CAAC;EAED,oBACEzC,OAAA,CAAC3B,MAAM;IAAC4E,QAAQ,EAAC,QAAQ;IAACC,KAAK,EAAC,SAAS;IAACC,SAAS,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAK,CAAE;IAACC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eACxGvD,OAAA,CAAC1B,OAAO;MAACkF,OAAO,EAAC,OAAO;MAAAD,QAAA,gBAEtBvD,OAAA,CAACxB,MAAM;QACL0E,KAAK,EAAC,SAAS;QACfO,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,YAAY,CAAE;QACxCkB,SAAS,eAAE1D,OAAA,CAACf,QAAQ;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBV,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QACdT,SAAS,EAAET,QAAQ,CAAC,YAAY,CAAC,GAAG,eAAe,GAAG,EAAG;QAAAU,QAAA,EAExD9C,eAAe,GAAG,qBAAqB,GACvC,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,OAAO,GAAG,gBAAgB,GACzC,CAAAnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,MAAM,GAAG,iBAAiB,GACzC,CAAAnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,eAAe,GAAG,eAAe,GAAG;MAAM;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACT9D,OAAA,CAACnB,OAAO;QAACmF,WAAW,EAAC,UAAU;QAACC,QAAQ;QAACb,EAAE,EAAE;UAAEc,EAAE,EAAE;QAAI;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAG3D,CAAAvD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,OAAO,iBACrB1C,OAAA,CAAAE,SAAA;QAAAqD,QAAA,gBACEvD,OAAA,CAACxB,MAAM;UACL0E,KAAK,EAAC,SAAS;UACf,iBAAc,YAAY;UAC1B,iBAAc,MAAM;UACpBO,OAAO,EAAGU,CAAC,IAAKhC,cAAc,CAACgC,CAAC,EAAErD,gBAAgB,CAAE;UACpDsD,OAAO,eAAEpE,OAAA,CAACH,aAAa;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BV,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UACdT,SAAS,EAAEP,cAAc,CAAC,kBAAkB,CAAC,GAAG,eAAe,GAAG,EAAG;UAAAQ,QAAA,EACtE;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9D,OAAA,CAACvB,IAAI;UACH4F,EAAE,EAAC,YAAY;UACfC,QAAQ,EAAEzD,aAAc;UACxB0D,WAAW;UACXC,IAAI,EAAEC,OAAO,CAAC5D,aAAa,CAAE;UAC7B6D,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACzB,gBAAgB,CAAE;UACjDwC,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAE/BvD,OAAA,CAACtB,QAAQ;YAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,kBAAkB,CAAE;YAAAe,QAAA,EAAC;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC;MAAA,eACP,CACH,EAGA,CAAC,CAAAvD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,OAAO,IAAK,CAAAnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,OAAO,IAAIjC,eAAe,IAAIC,gBAAiB,kBACzFV,OAAA,CAAAE,SAAA;QAAAqD,QAAA,gBACEvD,OAAA,CAACxB,MAAM;UACL0E,KAAK,EAAC,SAAS;UACf,iBAAc,eAAe;UAC7B,iBAAc,MAAM;UACpBO,OAAO,EAAGU,CAAC,IAAKhC,cAAc,CAACgC,CAAC,EAAEnD,mBAAmB,CAAE;UACvDoD,OAAO,eAAEpE,OAAA,CAACH,aAAa;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BV,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UACdT,SAAS,EAAEP,cAAc,CAAC,qBAAqB,CAAC,GAAG,eAAe,GAAG,EAAG;UAAAQ,QAAA,EAEvE9C,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAACiE,QAAQ,EAAE,GAAG;QAAiB;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC,eACT9D,OAAA,CAACvB,IAAI;UACH4F,EAAE,EAAC,eAAe;UAClBC,QAAQ,EAAEvD,gBAAiB;UAC3BwD,WAAW;UACXC,IAAI,EAAEC,OAAO,CAAC1D,gBAAgB,CAAE;UAChC2D,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACvB,mBAAmB,CAAE;UACpDsC,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAE/BvD,OAAA,CAACtB,QAAQ;YACP+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,qBAAqB,CAAE;YACjDc,SAAS,EAAET,QAAQ,CAAC,qBAAqB,CAAC,GAAG,aAAa,GAAG,EAAG;YAAAU,QAAA,EACjE;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,EACzB/B,kBAAkB,iBACjB/B,OAAA,CAACtB,QAAQ;YACP+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,uBAAuBT,kBAAkB,EAAE,CAAE;YACvEuB,SAAS,EAAET,QAAQ,CAAC,uBAAuBd,kBAAkB,EAAE,CAAC,GAAG,aAAa,GAAG,EAAG;YAAAwB,QAAA,GACvF,YACW,EAACrB,oBAAoB,IAAIH,kBAAkB;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CACX;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,EAGN/B,kBAAkB,iBACjB/B,OAAA,CAAAE,SAAA;UAAAqD,QAAA,gBACEvD,OAAA,CAACxB,MAAM;YACL0E,KAAK,EAAC,SAAS;YACf,iBAAc,WAAW;YACzB,iBAAc,MAAM;YACpBO,OAAO,EAAGU,CAAC,IAAKhC,cAAc,CAACgC,CAAC,EAAEjD,eAAe,CAAE;YACnDkD,OAAO,eAAEpE,OAAA,CAACH,aAAa;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BV,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAEP,cAAc,CAAC,iBAAiB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAQ,QAAA,EACrE;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9D,OAAA,CAACvB,IAAI;YACH4F,EAAE,EAAC,WAAW;YACdC,QAAQ,EAAErD,YAAa;YACvBsD,WAAW;YACXC,IAAI,EAAEC,OAAO,CAACxD,YAAY,CAAE;YAC5ByD,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACrB,eAAe,CAAE;YAChDoC,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BvD,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,4BAA4B,CAAE;cAAAe,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAG7F9D,OAAA,CAACtB,QAAQ;cACP+E,OAAO,EAAGU,CAAC,IAAK;gBACdA,CAAC,CAACS,eAAe,CAAC,CAAC;gBACnBzC,cAAc,CAACgC,CAAC,EAAE/C,eAAe,CAAC;cACpC,CAAE;cACFgC,EAAE,EAAE;gBAAEH,QAAQ,EAAE;cAAW,CAAE;cAAAM,QAAA,GAC9B,sBACqB,eAAAvD,OAAA,CAACH,aAAa;gBAACgF,QAAQ,EAAC,OAAO;gBAACzB,EAAE,EAAE;kBAAE0B,EAAE,EAAE;gBAAE;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eAGX9D,OAAA,CAACtB,QAAQ;cACP+E,OAAO,EAAGU,CAAC,IAAK;gBACdA,CAAC,CAACS,eAAe,CAAC,CAAC;gBACnBzC,cAAc,CAACgC,CAAC,EAAE7C,gBAAgB,CAAC;cACrC,CAAE;cACF8B,EAAE,EAAE;gBAAEH,QAAQ,EAAE;cAAW,CAAE;cAAAM,QAAA,GAC9B,aACY,eAAAvD,OAAA,CAACH,aAAa;gBAACgF,QAAQ,EAAC,OAAO;gBAACzB,EAAE,EAAE;kBAAE0B,EAAE,EAAE;gBAAE;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAGX9D,OAAA,CAACtB,QAAQ;cACP+E,OAAO,EAAGU,CAAC,IAAK;gBACdA,CAAC,CAACS,eAAe,CAAC,CAAC;gBACnBzC,cAAc,CAACgC,CAAC,EAAE3C,gBAAgB,CAAC;cACrC,CAAE;cACF4B,EAAE,EAAE;gBAAEH,QAAQ,EAAE;cAAW,CAAE;cAAAM,QAAA,GAC9B,iBACgB,eAAAvD,OAAA,CAACH,aAAa;gBAACgF,QAAQ,EAAC,OAAO;gBAACzB,EAAE,EAAE;kBAAE0B,EAAE,EAAE;gBAAE;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eAGX9D,OAAA,CAACtB,QAAQ;cACP+E,OAAO,EAAGU,CAAC,IAAK;gBACdA,CAAC,CAACS,eAAe,CAAC,CAAC;gBACnBzC,cAAc,CAACgC,CAAC,EAAEzC,iBAAiB,CAAC;cACtC,CAAE;cACF0B,EAAE,EAAE;gBAAEH,QAAQ,EAAE;cAAW,CAAE;cAAAM,QAAA,GAC9B,SACQ,eAAAvD,OAAA,CAACH,aAAa;gBAACgF,QAAQ,EAAC,OAAO;gBAACzB,EAAE,EAAE;kBAAE0B,EAAE,EAAE;gBAAE;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eAGX9D,OAAA,CAACtB,QAAQ;cACP+E,OAAO,EAAGU,CAAC,IAAK;gBACdA,CAAC,CAACS,eAAe,CAAC,CAAC;gBACnBzC,cAAc,CAACgC,CAAC,EAAEvC,yBAAyB,CAAC;cAC9C,CAAE;cACFwB,EAAE,EAAE;gBAAEH,QAAQ,EAAE;cAAW,CAAE;cAAAM,QAAA,GAC9B,sBACqB,eAAAvD,OAAA,CAACH,aAAa;gBAACgF,QAAQ,EAAC,OAAO;gBAACzB,EAAE,EAAE;kBAAE0B,EAAE,EAAE;gBAAE;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eAGX9D,OAAA,CAACtB,QAAQ;cACP+E,OAAO,EAAGU,CAAC,IAAK;gBACdA,CAAC,CAACS,eAAe,CAAC,CAAC;gBACnBzC,cAAc,CAACgC,CAAC,EAAErC,kBAAkB,CAAC;cACvC,CAAE;cACFsB,EAAE,EAAE;gBAAEH,QAAQ,EAAE;cAAW,CAAE;cAAAM,QAAA,GAC9B,mBACkB,eAAAvD,OAAA,CAACH,aAAa;gBAACgF,QAAQ,EAAC,OAAO;gBAACzB,EAAE,EAAE;kBAAE0B,EAAE,EAAE;gBAAE;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAGP9D,OAAA,CAACvB,IAAI;YACH4F,EAAE,EAAC,WAAW;YACdC,QAAQ,EAAEnD,YAAa;YACvBoD,WAAW;YACXC,IAAI,EAAEC,OAAO,CAACtD,YAAY,CAAE;YAC5BuD,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACnB,eAAe,CAAE;YAChD2D,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACF3B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BvD,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,sCAAsC,CAAE;cAAAe,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC9G9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,oCAAoC,CAAE;cAAAe,QAAA,EAAC;YAAa;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACnG9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,oCAAoC,CAAE;cAAAe,QAAA,EAAC;YAAmB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACzG9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,mCAAmC,CAAE;cAAAe,QAAA,EAAC;YAAY;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACjG9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,sCAAsC,CAAE;cAAAe,QAAA,EAAC;YAA2B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACnH9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,mCAAmC,CAAE;cAAAe,QAAA,EAAC;YAA0B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G,CAAC,eAGP9D,OAAA,CAACvB,IAAI;YACH4F,EAAE,EAAC,YAAY;YACfC,QAAQ,EAAEjD,aAAc;YACxBkD,WAAW;YACXC,IAAI,EAAEC,OAAO,CAACpD,aAAa,CAAE;YAC7BqD,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACjB,gBAAgB,CAAE;YACjDyD,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACF3B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BvD,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,kCAAkC,CAAE;cAAAe,QAAA,EAAC;YAAiB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACrG9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,4BAA4B,CAAE;cAAAe,QAAA,EAAC;YAAiB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC/F9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,gCAAgC,CAAE;cAAAe,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACjG9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,+BAA+B,CAAE;cAAAe,QAAA,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC/F9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,+BAA+B,CAAE;cAAAe,QAAA,EAAC;YAAgB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,eAGP9D,OAAA,CAACvB,IAAI;YACH4F,EAAE,EAAC,YAAY;YACfC,QAAQ,EAAE/C,aAAc;YACxBgD,WAAW;YACXC,IAAI,EAAEC,OAAO,CAAClD,aAAa,CAAE;YAC7BmD,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACf,gBAAgB,CAAE;YACjDuD,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACF3B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BvD,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,oCAAoC,CAAE;cAAAe,QAAA,EAAC;YAAqB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC3G9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,sCAAsC,CAAE;cAAAe,QAAA,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC5G9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,oCAAoC,CAAE;cAAAe,QAAA,EAAC;YAAqB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC3G9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,sCAAsC,CAAE;cAAAe,QAAA,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxG,CAAC,eAGP9D,OAAA,CAACvB,IAAI;YACH4F,EAAE,EAAC,aAAa;YAChBC,QAAQ,EAAE7C,cAAe;YACzB8C,WAAW;YACXC,IAAI,EAAEC,OAAO,CAAChD,cAAc,CAAE;YAC9BiD,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACb,iBAAiB,CAAE;YAClDqD,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACF3B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BvD,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,oCAAoC,CAAE;cAAAe,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxG9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,4BAA4B,CAAE;cAAAe,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChG9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,wCAAwC,CAAE;cAAAe,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChH9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,oCAAoC,CAAE;cAAAe,QAAA,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC,eAGP9D,OAAA,CAACvB,IAAI;YACH4F,EAAE,EAAC,qBAAqB;YACxBC,QAAQ,EAAE3C,sBAAuB;YACjC4C,WAAW;YACXC,IAAI,EAAEC,OAAO,CAAC9C,sBAAsB,CAAE;YACtC+C,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACX,yBAAyB,CAAE;YAC1DmD,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACF3B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BvD,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,2CAA2C,CAAE;cAAAe,QAAA,EAAC;YAAyB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtH9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,uCAAuC,CAAE;cAAAe,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxG9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,qCAAqC,CAAE;cAAAe,QAAA,EAAC;YAAmB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1G9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,yCAAyC,CAAE;cAAAe,QAAA,EAAC;YAAuB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClH9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,wCAAwC,CAAE;cAAAe,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChH9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,0CAA0C,CAAE;cAAAe,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CAAC,eAGP9D,OAAA,CAACvB,IAAI;YACH4F,EAAE,EAAC,cAAc;YACjBC,QAAQ,EAAEzC,eAAgB;YAC1B0C,WAAW;YACXC,IAAI,EAAEC,OAAO,CAAC5C,eAAe,CAAE;YAC/B6C,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACT,kBAAkB,CAAE;YACnDiD,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACF3B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BvD,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,oCAAoC,CAAE;cAAAe,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxG9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,8BAA8B,CAAE;cAAAe,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClG9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,kCAAkC,CAAE;cAAAe,QAAA,EAAC;YAAgB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpG9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,iCAAiC,CAAE;cAAAe,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClG9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,gCAAgC,CAAE;cAAAe,QAAA,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChG9D,OAAA,CAACtB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAAC,iCAAiC,CAAE;cAAAe,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC;QAAA,eACP,CACH;MAAA,eACD,CACH,eAGD9D,OAAA,CAACzB,GAAG;QAAC6E,EAAE,EAAE;UAAE+B,QAAQ,EAAE;QAAE;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG5B9D,OAAA,CAACzB,GAAG;QAAC6E,EAAE,EAAE;UAAEgC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAA9B,QAAA,GAChD9C,eAAe,IAAIC,gBAAgB,iBAClCV,OAAA,CAACrB,UAAU;UAAC6E,OAAO,EAAC,OAAO;UAACN,KAAK,EAAC,eAAe;UAACE,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,GAAC,gBACjD,eAAAvD,OAAA;YAAAuD,QAAA,EAAI7C,gBAAgB,CAACiE;UAAQ;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACb,eACD9D,OAAA,CAACrB,UAAU;UAAC6E,OAAO,EAAC,OAAO;UAACJ,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EACvC,CAAAhD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,QAAQ,KAAI;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACb9D,OAAA,CAACjB,OAAO;UAACuG,KAAK,EAAC,QAAQ;UAAA/B,QAAA,eACrBvD,OAAA,CAACpB,UAAU;YAACsE,KAAK,EAAC,SAAS;YAACO,OAAO,EAAEb,YAAa;YAAC2C,IAAI,EAAC,KAAK;YAAAhC,QAAA,eAC3DvD,OAAA,CAACL,UAAU;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEb,CAAC;AAAC1D,EAAA,CAlbID,SAAS;EAAA,QACIhC,WAAW,EACXC,WAAW,EACgC0B,OAAO;AAAA;AAAA0F,EAAA,GAH/DrF,SAAS;AAobf,eAAeA,SAAS;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}