{"ast": null, "code": "import { formatDistance } from \"./vi/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./vi/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./vi/_lib/formatRelative.mjs\";\nimport { localize } from \"./vi/_lib/localize.mjs\";\nimport { match } from \"./vi/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Vietnamese locale (Vietnam).\n * @language Vietnamese\n * @iso-639-2 vie\n * <AUTHOR> [@trongthanh](https://github.com/trongthanh)\n * <AUTHOR> [@lihop](https://github.com/lihop)\n */\nexport const vi = {\n  code: \"vi\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1 /* First week of new year contains Jan 1st  */\n  }\n};\n\n// Fallback for modularized imports:\nexport default vi;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "vi", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/vi.mjs"], "sourcesContent": ["import { formatDistance } from \"./vi/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./vi/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./vi/_lib/formatRelative.mjs\";\nimport { localize } from \"./vi/_lib/localize.mjs\";\nimport { match } from \"./vi/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Vietnamese locale (Vietnam).\n * @language Vietnamese\n * @iso-639-2 vie\n * <AUTHOR> [@trongthanh](https://github.com/trongthanh)\n * <AUTHOR> [@lihop](https://github.com/lihop)\n */\nexport const vi = {\n  code: \"vi\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1 /* First week of new year contains Jan 1st  */,\n  },\n};\n\n// Fallback for modularized imports:\nexport default vi;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE,CAAC,CAAC;EAC3B;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}