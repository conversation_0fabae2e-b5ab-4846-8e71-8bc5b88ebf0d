{"ast": null, "code": "/**\n * Utility per la navigazione nell'applicazione\n */\n\n/**\n * Reindirizza alla pagina di visualizzazione cavi con un ritardo opzionale\n * @param {function} navigate - Funzione di navigazione di React Router\n * @param {number} delay - Ritardo in millisecondi prima del reindirizzamento (default: 0)\n */\nexport const redirectToVisualizzaCavi = (navigate, delay = 0) => {\n  console.log('Tentativo di reindirizzamento a /dashboard/cavi/visualizza');\n\n  // Funzione di reindirizzamento\n  const doRedirect = () => {\n    try {\n      // Prima prova con navigate\n      navigate('/dashboard/cavi/visualizza');\n      console.log('Reindirizzamento con navigate eseguito');\n    } catch (error) {\n      console.error('Errore durante il reindirizzamento con navigate:', error);\n      // Se fallisce, usa window.location come fallback\n      try {\n        window.location.href = '/dashboard/cavi/visualizza';\n        console.log('Reindirizzamento con window.location eseguito');\n      } catch (locationError) {\n        console.error('Errore anche con window.location:', locationError);\n        // Ultimo tentativo: ricarica la pagina\n        window.location.reload();\n      }\n    }\n  };\n\n  // Esegui con o senza ritardo\n  if (delay > 0) {\n    console.log(`Reindirizzamento programmato con ritardo di ${delay}ms`);\n    setTimeout(doRedirect, delay);\n  } else {\n    doRedirect();\n  }\n};\n\n/**\n * Ricarica la pagina corrente con un ritardo opzionale\n * @param {number} delay - Ritardo in millisecondi prima del ricaricamento (default: 0)\n */\nexport const reloadPage = (delay = 0) => {\n  if (delay > 0) {\n    setTimeout(() => {\n      window.location.reload();\n    }, delay);\n  } else {\n    window.location.reload();\n  }\n};", "map": {"version": 3, "names": ["redirectToVisualizzaCavi", "navigate", "delay", "console", "log", "doRedirect", "error", "window", "location", "href", "locationError", "reload", "setTimeout", "reloadPage"], "sources": ["C:/CMS/webapp/frontend/src/utils/navigationUtils.js"], "sourcesContent": ["/**\n * Utility per la navigazione nell'applicazione\n */\n\n/**\n * Reindirizza alla pagina di visualizzazione cavi con un ritardo opzionale\n * @param {function} navigate - Funzione di navigazione di React Router\n * @param {number} delay - Ritardo in millisecondi prima del reindirizzamento (default: 0)\n */\nexport const redirectToVisualizzaCavi = (navigate, delay = 0) => {\n  console.log('Tentativo di reindirizzamento a /dashboard/cavi/visualizza');\n\n  // Funzione di reindirizzamento\n  const doRedirect = () => {\n    try {\n      // Prima prova con navigate\n      navigate('/dashboard/cavi/visualizza');\n      console.log('Reindirizzamento con navigate eseguito');\n    } catch (error) {\n      console.error('Errore durante il reindirizzamento con navigate:', error);\n      // Se fallisce, usa window.location come fallback\n      try {\n        window.location.href = '/dashboard/cavi/visualizza';\n        console.log('Reindirizzamento con window.location eseguito');\n      } catch (locationError) {\n        console.error('Errore anche con window.location:', locationError);\n        // Ultimo tentativo: ricarica la pagina\n        window.location.reload();\n      }\n    }\n  };\n\n  // Esegui con o senza ritardo\n  if (delay > 0) {\n    console.log(`Reindirizzamento programmato con ritardo di ${delay}ms`);\n    setTimeout(doRedirect, delay);\n  } else {\n    doRedirect();\n  }\n};\n\n/**\n * Ricarica la pagina corrente con un ritardo opzionale\n * @param {number} delay - Ritardo in millisecondi prima del ricaricamento (default: 0)\n */\nexport const reloadPage = (delay = 0) => {\n  if (delay > 0) {\n    setTimeout(() => {\n      window.location.reload();\n    }, delay);\n  } else {\n    window.location.reload();\n  }\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,wBAAwB,GAAGA,CAACC,QAAQ,EAAEC,KAAK,GAAG,CAAC,KAAK;EAC/DC,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;;EAEzE;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI;MACF;MACAJ,QAAQ,CAAC,4BAA4B,CAAC;MACtCE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACvD,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE;MACA,IAAI;QACFC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,4BAA4B;QACnDN,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC9D,CAAC,CAAC,OAAOM,aAAa,EAAE;QACtBP,OAAO,CAACG,KAAK,CAAC,mCAAmC,EAAEI,aAAa,CAAC;QACjE;QACAH,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC;MAC1B;IACF;EACF,CAAC;;EAED;EACA,IAAIT,KAAK,GAAG,CAAC,EAAE;IACbC,OAAO,CAACC,GAAG,CAAC,+CAA+CF,KAAK,IAAI,CAAC;IACrEU,UAAU,CAACP,UAAU,EAAEH,KAAK,CAAC;EAC/B,CAAC,MAAM;IACLG,UAAU,CAAC,CAAC;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMQ,UAAU,GAAGA,CAACX,KAAK,GAAG,CAAC,KAAK;EACvC,IAAIA,KAAK,GAAG,CAAC,EAAE;IACbU,UAAU,CAAC,MAAM;MACfL,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC;IAC1B,CAAC,EAAET,KAAK,CAAC;EACX,CAAC,MAAM;IACLK,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC;EAC1B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}