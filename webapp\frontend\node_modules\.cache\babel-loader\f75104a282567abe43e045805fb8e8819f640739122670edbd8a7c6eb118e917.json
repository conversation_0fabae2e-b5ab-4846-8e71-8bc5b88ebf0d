{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Grid, Card, CardContent, CardActions, Button, Chip, Alert, CircularProgress, Divider, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails, Switch, FormControlLabel } from '@mui/material';\nimport { Assessment as AssessmentIcon, BarChart as BarChartIcon, PieChart as PieChartIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon, ArrowBack as ArrowBackIcon, DateRange as DateRangeIcon, Cable as CableIcon, Inventory as InventoryIcon, ExpandMore as ExpandMoreIcon, ShowChart as ShowChartIcon } from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    cantiereId\n  } = useParams();\n  const {\n    user\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading progress report:', err);\n          return {\n            content: null\n          };\n        });\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video').catch(err => {\n          console.error('Error loading BOQ report:', err);\n          return {\n            content: null\n          };\n        });\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading bobine report:', err);\n          return {\n            content: null\n          };\n        });\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading cavi stato report:', err);\n          return {\n            content: null\n          };\n        });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([progressPromise, boqPromise, bobinePromise, caviStatoPromise]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [{\n    id: 'progress',\n    title: 'Report Avanzamento',\n    description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n    icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 13\n    }, this),\n    color: 'primary',\n    features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n  }, {\n    id: 'boq',\n    title: 'Bill of Quantities',\n    description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n    icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 13\n    }, this),\n    color: 'secondary',\n    features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n  }, {\n    id: 'bobine',\n    title: 'Report Utilizzo Bobine',\n    description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n    icon: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 13\n    }, this),\n    color: 'success',\n    features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n  }, {\n    id: 'bobina-specifica',\n    title: 'Report Bobina Specifica',\n    description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n    icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 13\n    }, this),\n    color: 'info',\n    features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n  }, {\n    id: 'posa-periodo',\n    title: 'Report Posa per Periodo',\n    description: 'Analisi temporale della posa con trend e pattern di lavoro',\n    icon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 13\n    }, this),\n    color: 'warning',\n    features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n  }, {\n    id: 'cavi-stato',\n    title: 'Report Cavi per Stato',\n    description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n    icon: /*#__PURE__*/_jsxDEV(BarChartIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 13\n    }, this),\n    color: 'error',\n    features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n  }];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReportSelect = reportType => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderReportContent = () => {\n    if (!reportData) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title, \" - \", reportData.nome_cantiere]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'pdf'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"primary\",\n            children: \"PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'excel'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"success\",\n            children: \"Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 26\n            }, this),\n            onClick: () => setReportData(null),\n            variant: \"outlined\",\n            size: \"small\",\n            children: \"Nuovo Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), dialogType === 'progress' && renderProgressReport(reportData), dialogType === 'boq' && renderBoqReport(reportData), dialogType === 'bobine' && renderBobineReport(reportData), dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData), dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData), dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this);\n  };\n  const renderProgressReport = data => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          control: /*#__PURE__*/_jsxDEV(Switch, {\n            checked: showCharts,\n            onChange: e => setShowCharts(e.target.checked),\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this),\n          label: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this), \"Mostra Grafici\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(ProgressChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Avanzamento Generale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Metri Totali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.metri_totali, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 43\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Metri Posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.metri_posati, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 43\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Metri Rimanenti: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.metri_da_posare, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 46\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Avanzamento: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.percentuale_avanzamento, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Totale Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: data.totale_cavi\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Cavi Posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: data.cavi_posati\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 42\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Cavi Rimanenti: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: data.cavi_rimanenti\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Percentuale Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.percentuale_cavi, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 47\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                children: [\"Media Giornaliera: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.media_giornaliera, \"m/giorno\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 48\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 17\n              }, this), data.giorni_stimati && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Giorni Stimati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [data.giorni_stimati, \" giorni\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Data Completamento: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: data.data_completamento\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 53\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 451,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 439,\n      columnNumber: 7\n    }, this), data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 43\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Posa Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: data.posa_recente.slice(0, 5).map((posa, index) => /*#__PURE__*/_jsxDEV(Typography, {\n                children: [posa.data, \": \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [posa.metri, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 36\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 374,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = data => {\n    var _data$cavi_per_tipo, _data$bobine_per_tipo;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [showCharts && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(BoqChart, {\n          data: data\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Cavi per Tipologia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: (_data$cavi_per_tipo = data.cavi_per_tipo) === null || _data$cavi_per_tipo === void 0 ? void 0 : _data$cavi_per_tipo.map((cavo, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                lg: 4,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle1\",\n                      children: cavo.tipologia\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 503,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [\"Sezione: \", cavo.sezione]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 504,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Cavi: \", cavo.num_cavi]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 505,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Teorici: \", cavo.metri_teorici, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Reali: \", cavo.metri_reali, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Da Posare: \", cavo.metri_da_posare, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 508,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Bobine Disponibili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: (_data$bobine_per_tipo = data.bobine_per_tipo) === null || _data$bobine_per_tipo === void 0 ? void 0 : _data$bobine_per_tipo.map((bobina, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                lg: 4,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle1\",\n                      children: bobina.tipologia\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [\"Sezione: \", bobina.sezione]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Bobine: \", bobina.num_bobine]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Disponibili: \", bobina.metri_disponibili, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 532,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 528,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 5\n    }, this);\n  };\n  const renderBobineReport = data => {\n    var _data$bobine;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [showCharts && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(BobineChart, {\n          data: data\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: [\"Bobine del Cantiere (\", data.totale_bobine, \" totali)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: (_data$bobine = data.bobine) === null || _data$bobine === void 0 ? void 0 : _data$bobine.map((bobina, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                lg: 4,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"subtitle1\",\n                      children: bobina.id_bobina\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 566,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [bobina.tipologia, \" - \", bobina.sezione]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 567,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: bobina.stato,\n                      color: bobina.stato === 'DISPONIBILE' ? 'success' : 'warning',\n                      size: \"small\",\n                      sx: {\n                        mb: 1\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 568,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Totali: \", bobina.metri_totali, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 574,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Residui: \", bobina.metri_residui, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 575,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Utilizzati: \", bobina.metri_utilizzati, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 576,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Utilizzo: \", bobina.percentuale_utilizzo, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 577,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 5\n    }, this);\n  };\n  const renderBobinaSpecificaReport = data => {\n    var _data$bobina, _data$bobina2, _data$bobina3, _data$bobina4, _data$bobina5, _data$bobina6, _data$bobina7, _data$bobina8, _data$bobina9, _data$cavi_associati;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Dettagli Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"ID: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: (_data$bobina = data.bobina) === null || _data$bobina === void 0 ? void 0 : _data$bobina.id_bobina\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 33\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Tipologia: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: (_data$bobina2 = data.bobina) === null || _data$bobina2 === void 0 ? void 0 : _data$bobina2.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 40\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Sezione: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: (_data$bobina3 = data.bobina) === null || _data$bobina3 === void 0 ? void 0 : _data$bobina3.sezione\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 38\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: (_data$bobina4 = data.bobina) === null || _data$bobina4 === void 0 ? void 0 : _data$bobina4.stato,\n                  color: ((_data$bobina5 = data.bobina) === null || _data$bobina5 === void 0 ? void 0 : _data$bobina5.stato) === 'DISPONIBILE' ? 'success' : 'warning',\n                  sx: {\n                    my: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Totali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [(_data$bobina6 = data.bobina) === null || _data$bobina6 === void 0 ? void 0 : _data$bobina6.metri_totali, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 43\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Residui: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [(_data$bobina7 = data.bobina) === null || _data$bobina7 === void 0 ? void 0 : _data$bobina7.metri_residui, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 44\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Utilizzati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [(_data$bobina8 = data.bobina) === null || _data$bobina8 === void 0 ? void 0 : _data$bobina8.metri_utilizzati, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 47\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Utilizzo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [(_data$bobina9 = data.bobina) === null || _data$bobina9 === void 0 ? void 0 : _data$bobina9.percentuale_utilizzo, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 39\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: [\"Cavi Associati (\", data.totale_cavi, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    maxHeight: 300,\n                    overflow: 'auto'\n                  },\n                  children: (_data$cavi_associati = data.cavi_associati) === null || _data$cavi_associati === void 0 ? void 0 : _data$cavi_associati.map((cavo, index) => /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      mb: 1,\n                      p: 1,\n                      border: '1px solid #e0e0e0',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: cavo.id_cavo\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 630,\n                        columnNumber: 51\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 630,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: [cavo.sistema, \" - \", cavo.utility, \" - \", cavo.tipologia]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 631,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      display: \"block\",\n                      children: [\"Teorici: \", cavo.metri_teorici, \"m | Reali: \", cavo.metri_reali, \"m | Stato: \", cavo.stato]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 634,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 629,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 617,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 5\n    }, this);\n  };\n  const renderPosaPeriodoReport = data => {\n    var _data$posa_giornalier;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [showCharts && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(TimelineChart, {\n          data: data\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 652,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Statistiche Periodo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Periodo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [data.data_inizio, \" - \", data.data_fine]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 665,\n                    columnNumber: 38\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Totale Metri: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [data.totale_metri_periodo, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 43\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Giorni Attivi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: data.giorni_attivi\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 44\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Media Giornaliera: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [data.media_giornaliera, \"m/giorno\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 48\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 657,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Posa Giornaliera\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    maxHeight: 300,\n                    overflow: 'auto'\n                  },\n                  children: (_data$posa_giornalier = data.posa_giornaliera) === null || _data$posa_giornalier === void 0 ? void 0 : _data$posa_giornalier.map((posa, index) => /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      py: 0.5\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      children: posa.data\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 686,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [posa.metri, \"m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 687,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 687,\n                      columnNumber: 23\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 685,\n                    columnNumber: 21\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 675,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 649,\n      columnNumber: 5\n    }, this);\n  };\n  const renderCaviStatoReport = data => {\n    var _data$cavi_per_stato;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Accordion, {\n          defaultExpanded: true,\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 41\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Cavi per Stato di Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: (_data$cavi_per_stato = data.cavi_per_stato) === null || _data$cavi_per_stato === void 0 ? void 0 : _data$cavi_per_stato.map((stato, index) => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 6,\n                lg: 3,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  children: /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      gutterBottom: true,\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        label: stato.stato,\n                        color: stato.stato === 'Installato' ? 'success' : 'warning',\n                        sx: {\n                          mb: 1\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 713,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 712,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Numero Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: stato.num_cavi\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 719,\n                        columnNumber: 48\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 719,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Teorici: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [stato.metri_teorici, \"m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 720,\n                        columnNumber: 50\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 720,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: [\"Metri Reali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: [stato.metri_reali, \"m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 721,\n                        columnNumber: 48\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 721,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 711,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 710,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 701,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 700,\n      columnNumber: 5\n    }, this);\n  };\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 735,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 740,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 11\n        }, this), dialogType === 'bobina-specifica' && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Bobina\",\n            value: formData.id_bobina,\n            onChange: e => setFormData({\n              ...formData,\n              id_bobina: e.target.value\n            }),\n            placeholder: \"Es: 1, 2, A, B...\",\n            helperText: \"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 762,\n          columnNumber: 13\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 745,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 738,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 801,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 802,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 800,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 734,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => navigate(-1),\n          color: \"primary\",\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Report e Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 818,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 826,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 817,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 832,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 831,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 840,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                mr: 1,\n                color: 'primary.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 842,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Avanzamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 841,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 840,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.progress ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                control: /*#__PURE__*/_jsxDEV(Switch, {\n                  checked: showCharts,\n                  onChange: e => setShowCharts(e.target.checked),\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 852,\n                  columnNumber: 23\n                }, this),\n                label: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n                    sx: {\n                      mr: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 25\n                  }, this), \"Mostra Grafici\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 859,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 850,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 867,\n                    columnNumber: 34\n                  }, this),\n                  onClick: () => generateReportWithFormat('progress', 'pdf'),\n                  variant: \"outlined\",\n                  size: \"small\",\n                  color: \"primary\",\n                  sx: {\n                    mr: 1\n                  },\n                  children: \"PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 866,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 877,\n                    columnNumber: 34\n                  }, this),\n                  onClick: () => generateReportWithFormat('progress', 'excel'),\n                  variant: \"outlined\",\n                  size: \"small\",\n                  color: \"success\",\n                  children: \"Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 849,\n              columnNumber: 17\n            }, this), renderProgressReport(reportsData.progress)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 892,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 890,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 896,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 902,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getProgressReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    progress: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying progress report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 899,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 895,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 846,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 839,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n              sx: {\n                mr: 1,\n                color: 'secondary.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 931,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Bill of Quantities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 932,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 929,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.boq ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 940,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 939,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 950,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 938,\n              columnNumber: 17\n            }, this), renderBoqReport(reportsData.boq)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 962,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 974,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getBillOfQuantities(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    boq: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying BOQ report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 971,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 967,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 935,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 928,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1001,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(InventoryIcon, {\n              sx: {\n                mr: 1,\n                color: 'success.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1003,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Utilizzo Bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1004,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1002,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1001,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.bobine ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1012,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobine', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1011,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1022,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobine', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1021,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1010,\n              columnNumber: 17\n            }, this), renderBobineReport(reportsData.bobine)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1009,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1035,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1034,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1046,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getBobineReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    bobine: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying bobine report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1043,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1039,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1007,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1000,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1073,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(BarChartIcon, {\n              sx: {\n                mr: 1,\n                color: 'error.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1075,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Cavi per Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1076,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1074,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1073,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.caviStato ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1084,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('cavi-stato', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1083,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1094,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('cavi-stato', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1093,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1082,\n              columnNumber: 17\n            }, this), renderCaviStatoReport(reportsData.caviStato)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1081,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1107,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1108,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1106,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1112,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1118,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getCaviStatoReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    caviStato: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying cavi stato report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1115,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1111,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1079,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1072,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Report Speciali\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: \"Questi report richiedono parametri aggiuntivi per essere generati.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Report Bobina Specifica\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1155,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: \"Dettaglio approfondito di una singola bobina con tutti i cavi associati.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1156,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  color: \"info\",\n                  onClick: () => {\n                    setDialogType('bobina-specifica');\n                    setOpenDialog(true);\n                  },\n                  children: \"Genera Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1161,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1160,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1153,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Report Posa per Periodo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1180,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: \"Analisi temporale della posa con trend e pattern di lavoro.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1181,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  color: \"warning\",\n                  onClick: () => {\n                    setDialogType('posa-periodo');\n                    // Set default date range (last month to today)\n                    const today = new Date();\n                    const lastMonth = new Date();\n                    lastMonth.setMonth(today.getMonth() - 1);\n                    setFormData({\n                      ...formData,\n                      data_inizio: lastMonth.toISOString().split('T')[0],\n                      data_fine: today.toISOString().split('T')[0]\n                    });\n                    setOpenDialog(true);\n                  },\n                  children: \"Genera Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1186,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1185,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1178,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1144,\n        columnNumber: 9\n      }, this), reportsData.bobinaSpecifica && /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2,\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1216,\n            columnNumber: 43\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n              sx: {\n                mr: 1,\n                color: 'info.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1218,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Bobina Specifica\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1219,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1217,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1216,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1226,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobina-specifica', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1225,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1236,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobina-specifica', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1235,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1224,\n              columnNumber: 17\n            }, this), renderBobinaSpecificaReport(reportsData.bobinaSpecifica)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1223,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1222,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1215,\n        columnNumber: 11\n      }, this), reportsData.posaPeriodo && /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2,\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1253,\n            columnNumber: 43\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n              sx: {\n                mr: 1,\n                color: 'warning.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1255,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Posa per Periodo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1256,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1254,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1263,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1262,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1273,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1272,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1261,\n              columnNumber: 17\n            }, this), renderPosaPeriodoReport(reportsData.posaPeriodo)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1260,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1259,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1252,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 837,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 815,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageNew, \"Qo68i3NRMGbRWcOk2D5QM9YbhYQ=\", false, function () {\n  return [useNavigate, useParams, useAuth];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Switch", "FormControlLabel", "Assessment", "AssessmentIcon", "<PERSON><PERSON><PERSON>", "BarChartIcon", "<PERSON><PERSON><PERSON>", "PieChartIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "ArrowBack", "ArrowBackIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "ShowChart", "ShowChartIcon", "useNavigate", "useParams", "useAuth", "AdminHomeButton", "reportService", "ProgressChart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TimelineChart", "Cavi<PERSON>tato<PERSON>hart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s", "navigate", "cantiereId", "user", "loading", "setLoading", "error", "setError", "reportData", "setReportData", "selectedReport", "setSelectedReport", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportsData", "setReportsData", "progress", "boq", "bobine", "caviStato", "bobinaSpecifica", "posaPeriodo", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAllReports", "progressPromise", "getProgressReport", "catch", "err", "console", "content", "boq<PERSON><PERSON><PERSON>", "getBillOfQuantities", "bob<PERSON><PERSON><PERSON><PERSON>", "getBobineReport", "caviStatoPromise", "getCaviStatoReport", "progressData", "boqData", "bobine<PERSON><PERSON>", "caviStatoData", "Promise", "all", "reportTypes", "id", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "features", "generateReportWithFormat", "reportType", "format", "response", "getBobinaReport", "getPosaPerPeriodoReport", "Error", "prev", "file_url", "window", "open", "detail", "message", "handleReportSelect", "today", "Date", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "handleGenerateReport", "handleCloseDialog", "renderReportContent", "sx", "p", "mt", "children", "display", "justifyContent", "alignItems", "mb", "variant", "nome_cantiere", "gap", "startIcon", "onClick", "size", "renderProgressReport", "renderBoqReport", "renderBobineReport", "renderBobinaSpecificaReport", "renderPosaPeriodoReport", "renderCaviStatoReport", "data", "container", "spacing", "item", "xs", "control", "checked", "onChange", "e", "target", "label", "mr", "defaultExpanded", "expandIcon", "metri_totali", "metri_posati", "metri_da_posare", "percentuale_avanzamento", "totale_cavi", "cavi_posati", "cavi_rimanenti", "percentuale_cavi", "media_giornaliera", "giorni_stimati", "data_completamento", "posa_recente", "length", "slice", "map", "posa", "index", "metri", "_data$cavi_per_tipo", "_data$bobine_per_tipo", "cavi_per_tipo", "cavo", "md", "lg", "tipologia", "sezione", "num_cavi", "metri_te<PERSON>ci", "metri_reali", "bobine_per_tipo", "bobina", "num_bobine", "metri_disponibili", "_data$bobine", "totale_bobine", "stato", "metri_residui", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "percentuale_utilizzo", "_data$bobina", "_data$bobina2", "_data$bobina3", "_data$bobina4", "_data$bobina5", "_data$bobina6", "_data$bobina7", "_data$bobina8", "_data$bobina9", "_data$cavi_associati", "my", "maxHeight", "overflow", "cavi_associati", "border", "borderRadius", "id_cavo", "sistema", "utility", "_data$posa_giornalier", "totale_metri_periodo", "giorni_attivi", "posa_giornal<PERSON>", "py", "_data$cavi_per_stato", "cavi_per_stato", "gutterBottom", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "value", "placeholder", "helperText", "type", "InputLabelProps", "shrink", "disabled", "component", "ml", "then", "finally", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n  IconButton,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n  BarChart as BarChartIcon,\n  <PERSON><PERSON><PERSON> as PieChartIcon,\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n  ArrowBack as ArrowBackIcon,\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon,\n  ShowChart as ShowChartIcon\n} from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\n\nconst ReportCaviPageNew = () => {\n  const navigate = useNavigate();\n  const { cantiereId } = useParams();\n  const { user } = useAuth();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading progress report:', err);\n            return { content: null };\n          });\n\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading BOQ report:', err);\n            return { content: null };\n          });\n\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading bobine report:', err);\n            return { content: null };\n          });\n\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading cavi stato report:', err);\n            return { content: null };\n          });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          bobinePromise,\n          caviStatoPromise\n        ]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [\n    {\n      id: 'progress',\n      title: 'Report Avanzamento',\n      description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n      icon: <AssessmentIcon />,\n      color: 'primary',\n      features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n    },\n    {\n      id: 'boq',\n      title: 'Bill of Quantities',\n      description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n      icon: <ListIcon />,\n      color: 'secondary',\n      features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n    },\n    {\n      id: 'bobine',\n      title: 'Report Utilizzo Bobine',\n      description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n      icon: <InventoryIcon />,\n      color: 'success',\n      features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n    },\n    {\n      id: 'bobina-specifica',\n      title: 'Report Bobina Specifica',\n      description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n      icon: <CableIcon />,\n      color: 'info',\n      features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n    },\n    {\n      id: 'posa-periodo',\n      title: 'Report Posa per Periodo',\n      description: 'Analisi temporale della posa con trend e pattern di lavoro',\n      icon: <TimelineIcon />,\n      color: 'warning',\n      features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n    },\n    {\n      id: 'cavi-stato',\n      title: 'Report Cavi per Stato',\n      description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n      icon: <BarChartIcon />,\n      color: 'error',\n      features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n    }\n  ];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReportSelect = (reportType) => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n  const renderReportContent = () => {\n    if (!reportData) return null;\n\n    return (\n      <Paper sx={{ p: 3, mt: 3 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\">\n            {selectedReport?.title} - {reportData.nome_cantiere}\n          </Typography>\n          <Box sx={{ display: 'flex', gap: 1 }}>\n            {/* Export buttons */}\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'pdf')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"primary\"\n            >\n              PDF\n            </Button>\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'excel')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"success\"\n            >\n              Excel\n            </Button>\n            <Button\n              startIcon={<RefreshIcon />}\n              onClick={() => setReportData(null)}\n              variant=\"outlined\"\n              size=\"small\"\n            >\n              Nuovo Report\n            </Button>\n          </Box>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Renderizza il contenuto specifico del report */}\n        {dialogType === 'progress' && renderProgressReport(reportData)}\n        {dialogType === 'boq' && renderBoqReport(reportData)}\n        {dialogType === 'bobine' && renderBobineReport(reportData)}\n        {dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData)}\n        {dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData)}\n        {dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)}\n      </Paper>\n    );\n  };\n\n  const renderProgressReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Controllo visualizzazione grafici */}\n      <Grid item xs={12}>\n        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n          <FormControlLabel\n            control={\n              <Switch\n                checked={showCharts}\n                onChange={(e) => setShowCharts(e.target.checked)}\n                color=\"primary\"\n              />\n            }\n            label={\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <ShowChartIcon sx={{ mr: 1 }} />\n                Mostra Grafici\n              </Box>\n            }\n          />\n        </Box>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <ProgressChart data={data} />\n        </Grid>\n      )}\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Avanzamento Generale</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>Metri Totali: <strong>{data.metri_totali}m</strong></Typography>\n                <Typography>Metri Posati: <strong>{data.metri_posati}m</strong></Typography>\n                <Typography>Metri Rimanenti: <strong>{data.metri_da_posare}m</strong></Typography>\n                <Typography>Avanzamento: <strong>{data.percentuale_avanzamento}%</strong></Typography>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>Totale Cavi: <strong>{data.totale_cavi}</strong></Typography>\n                <Typography>Cavi Posati: <strong>{data.cavi_posati}</strong></Typography>\n                <Typography>Cavi Rimanenti: <strong>{data.cavi_rimanenti}</strong></Typography>\n                <Typography>Percentuale Cavi: <strong>{data.percentuale_cavi}%</strong></Typography>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Performance</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>Media Giornaliera: <strong>{data.media_giornaliera}m/giorno</strong></Typography>\n                {data.giorni_stimati && (\n                  <>\n                    <Typography>Giorni Stimati: <strong>{data.giorni_stimati} giorni</strong></Typography>\n                    <Typography>Data Completamento: <strong>{data.data_completamento}</strong></Typography>\n                  </>\n                )}\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {data.posa_recente && data.posa_recente.length > 0 && (\n        <Grid item xs={12}>\n          <Accordion defaultExpanded>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Typography variant=\"h6\">Posa Recente</Typography>\n            </AccordionSummary>\n            <AccordionDetails>\n              <Card>\n                <CardContent>\n                  {data.posa_recente.slice(0, 5).map((posa, index) => (\n                    <Typography key={index}>\n                      {posa.data}: <strong>{posa.metri}m</strong>\n                    </Typography>\n                  ))}\n                </CardContent>\n              </Card>\n            </AccordionDetails>\n          </Accordion>\n        </Grid>\n      )}\n    </Grid>\n  );\n\n  const renderBoqReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <BoqChart data={data} />\n        </Grid>\n      )}\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi per Tipologia</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Grid container spacing={2}>\n              {data.cavi_per_tipo?.map((cavo, index) => (\n                <Grid item xs={12} md={6} lg={4} key={index}>\n                  <Card>\n                    <CardContent>\n                      <Typography variant=\"subtitle1\">{cavo.tipologia}</Typography>\n                      <Typography variant=\"body2\">Sezione: {cavo.sezione}</Typography>\n                      <Typography>Cavi: {cavo.num_cavi}</Typography>\n                      <Typography>Metri Teorici: {cavo.metri_teorici}m</Typography>\n                      <Typography>Metri Reali: {cavo.metri_reali}m</Typography>\n                      <Typography>Da Posare: {cavo.metri_da_posare}m</Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Bobine Disponibili</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Grid container spacing={2}>\n              {data.bobine_per_tipo?.map((bobina, index) => (\n                <Grid item xs={12} md={6} lg={4} key={index}>\n                  <Card>\n                    <CardContent>\n                      <Typography variant=\"subtitle1\">{bobina.tipologia}</Typography>\n                      <Typography variant=\"body2\">Sezione: {bobina.sezione}</Typography>\n                      <Typography>Bobine: {bobina.num_bobine}</Typography>\n                      <Typography>Metri Disponibili: {bobina.metri_disponibili}m</Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobineReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <BobineChart data={data} />\n        </Grid>\n      )}\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">\n              Bobine del Cantiere ({data.totale_bobine} totali)\n            </Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Grid container spacing={2}>\n              {data.bobine?.map((bobina, index) => (\n                <Grid item xs={12} md={6} lg={4} key={index}>\n                  <Card>\n                    <CardContent>\n                      <Typography variant=\"subtitle1\">{bobina.id_bobina}</Typography>\n                      <Typography variant=\"body2\">{bobina.tipologia} - {bobina.sezione}</Typography>\n                      <Chip\n                        label={bobina.stato}\n                        color={bobina.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n                        size=\"small\"\n                        sx={{ mb: 1 }}\n                      />\n                      <Typography>Metri Totali: {bobina.metri_totali}m</Typography>\n                      <Typography>Metri Residui: {bobina.metri_residui}m</Typography>\n                      <Typography>Metri Utilizzati: {bobina.metri_utilizzati}m</Typography>\n                      <Typography>Utilizzo: {bobina.percentuale_utilizzo}%</Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobinaSpecificaReport = (data) => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Dettagli Bobina</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>ID: <strong>{data.bobina?.id_bobina}</strong></Typography>\n                <Typography>Tipologia: <strong>{data.bobina?.tipologia}</strong></Typography>\n                <Typography>Sezione: <strong>{data.bobina?.sezione}</strong></Typography>\n                <Chip\n                  label={data.bobina?.stato}\n                  color={data.bobina?.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n                  sx={{ my: 1 }}\n                />\n                <Typography>Metri Totali: <strong>{data.bobina?.metri_totali}m</strong></Typography>\n                <Typography>Metri Residui: <strong>{data.bobina?.metri_residui}m</strong></Typography>\n                <Typography>Metri Utilizzati: <strong>{data.bobina?.metri_utilizzati}m</strong></Typography>\n                <Typography>Utilizzo: <strong>{data.bobina?.percentuale_utilizzo}%</strong></Typography>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">\n              Cavi Associati ({data.totale_cavi})\n            </Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n                  {data.cavi_associati?.map((cavo, index) => (\n                    <Box key={index} sx={{ mb: 1, p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                      <Typography variant=\"body2\"><strong>{cavo.id_cavo}</strong></Typography>\n                      <Typography variant=\"caption\">\n                        {cavo.sistema} - {cavo.utility} - {cavo.tipologia}\n                      </Typography>\n                      <Typography variant=\"caption\" display=\"block\">\n                        Teorici: {cavo.metri_teorici}m | Reali: {cavo.metri_reali}m | Stato: {cavo.stato}\n                      </Typography>\n                    </Box>\n                  ))}\n                </Box>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderPosaPeriodoReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <TimelineChart data={data} />\n        </Grid>\n      )}\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Statistiche Periodo</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Typography>Periodo: <strong>{data.data_inizio} - {data.data_fine}</strong></Typography>\n                <Typography>Totale Metri: <strong>{data.totale_metri_periodo}m</strong></Typography>\n                <Typography>Giorni Attivi: <strong>{data.giorni_attivi}</strong></Typography>\n                <Typography>Media Giornaliera: <strong>{data.media_giornaliera}m/giorno</strong></Typography>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Posa Giornaliera</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Card>\n              <CardContent>\n                <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n                  {data.posa_giornaliera?.map((posa, index) => (\n                    <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>\n                      <Typography>{posa.data}</Typography>\n                      <Typography><strong>{posa.metri}m</strong></Typography>\n                    </Box>\n                  ))}\n                </Box>\n              </CardContent>\n            </Card>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderCaviStatoReport = (data) => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi per Stato di Installazione</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <Grid container spacing={2}>\n              {data.cavi_per_stato?.map((stato, index) => (\n                <Grid item xs={12} md={6} lg={3} key={index}>\n                  <Card>\n                    <CardContent>\n                      <Typography variant=\"h6\" gutterBottom>\n                        <Chip\n                          label={stato.stato}\n                          color={stato.stato === 'Installato' ? 'success' : 'warning'}\n                          sx={{ mb: 1 }}\n                        />\n                      </Typography>\n                      <Typography>Numero Cavi: <strong>{stato.num_cavi}</strong></Typography>\n                      <Typography>Metri Teorici: <strong>{stato.metri_teorici}m</strong></Typography>\n                      <Typography>Metri Reali: <strong>{stato.metri_reali}m</strong></Typography>\n                    </CardContent>\n                  </Card>\n                </Grid>\n              ))}\n            </Grid>\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {selectedReport?.title}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n          {dialogType === 'bobina-specifica' && (\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"ID Bobina\"\n                value={formData.id_bobina}\n                onChange={(e) => setFormData({ ...formData, id_bobina: e.target.value })}\n                placeholder=\"Es: 1, 2, A, B...\"\n                helperText=\"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n              />\n            </Grid>\n          )}\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <IconButton onClick={() => navigate(-1)} color=\"primary\">\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\" component=\"h1\">\n            Report e Analytics\n          </Typography>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Loading indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Reports */}\n      <Box sx={{ mt: 3 }}>\n        {/* Progress Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <AssessmentIcon sx={{ mr: 1, color: 'primary.main' }} />\n              <Typography variant=\"h6\">Report Avanzamento</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.progress ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n                  <FormControlLabel\n                    control={\n                      <Switch\n                        checked={showCharts}\n                        onChange={(e) => setShowCharts(e.target.checked)}\n                        color=\"primary\"\n                      />\n                    }\n                    label={\n                      <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                        <ShowChartIcon sx={{ mr: 1 }} />\n                        Mostra Grafici\n                      </Box>\n                    }\n                  />\n                  <Box>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                </Box>\n                {renderProgressReport(reportsData.progress)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getProgressReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          progress: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying progress report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Bill of Quantities */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ListIcon sx={{ mr: 1, color: 'secondary.main' }} />\n              <Typography variant=\"h6\">Bill of Quantities</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.boq ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('boq', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('boq', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBoqReport(reportsData.boq)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getBillOfQuantities(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          boq: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying BOQ report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Bobine Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <InventoryIcon sx={{ mr: 1, color: 'success.main' }} />\n              <Typography variant=\"h6\">Report Utilizzo Bobine</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.bobine ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobine', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobine', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBobineReport(reportsData.bobine)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getBobineReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          bobine: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying bobine report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Cavi Stato Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <BarChartIcon sx={{ mr: 1, color: 'error.main' }} />\n              <Typography variant=\"h6\">Report Cavi per Stato</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.caviStato ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('cavi-stato', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('cavi-stato', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderCaviStatoReport(reportsData.caviStato)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getCaviStatoReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          caviStato: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying cavi stato report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Special Reports Section */}\n        <Paper sx={{ p: 3, mt: 4 }}>\n          <Typography variant=\"h6\" gutterBottom>Report Speciali</Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n            Questi report richiedono parametri aggiuntivi per essere generati.\n          </Typography>\n\n          <Grid container spacing={3}>\n            {/* Bobina Specifica Report */}\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\">Report Bobina Specifica</Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    Dettaglio approfondito di una singola bobina con tutti i cavi associati.\n                  </Typography>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    fullWidth\n                    variant=\"outlined\"\n                    color=\"info\"\n                    onClick={() => {\n                      setDialogType('bobina-specifica');\n                      setOpenDialog(true);\n                    }}\n                  >\n                    Genera Report\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n\n            {/* Posa per Periodo Report */}\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\">Report Posa per Periodo</Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    Analisi temporale della posa con trend e pattern di lavoro.\n                  </Typography>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    fullWidth\n                    variant=\"outlined\"\n                    color=\"warning\"\n                    onClick={() => {\n                      setDialogType('posa-periodo');\n                      // Set default date range (last month to today)\n                      const today = new Date();\n                      const lastMonth = new Date();\n                      lastMonth.setMonth(today.getMonth() - 1);\n\n                      setFormData({\n                        ...formData,\n                        data_inizio: lastMonth.toISOString().split('T')[0],\n                        data_fine: today.toISOString().split('T')[0]\n                      });\n                      setOpenDialog(true);\n                    }}\n                  >\n                    Genera Report\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Display special reports if they exist */}\n        {reportsData.bobinaSpecifica && (\n          <Accordion defaultExpanded sx={{ mb: 2, mt: 2 }}>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <CableIcon sx={{ mr: 1, color: 'info.main' }} />\n                <Typography variant=\"h6\">Report Bobina Specifica</Typography>\n              </Box>\n            </AccordionSummary>\n            <AccordionDetails>\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobina-specifica', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobina-specifica', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBobinaSpecificaReport(reportsData.bobinaSpecifica)}\n              </Box>\n            </AccordionDetails>\n          </Accordion>\n        )}\n\n        {reportsData.posaPeriodo && (\n          <Accordion defaultExpanded sx={{ mb: 2, mt: 2 }}>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <TimelineIcon sx={{ mr: 1, color: 'warning.main' }} />\n                <Typography variant=\"h6\">Report Posa per Periodo</Typography>\n              </Box>\n            </AccordionSummary>\n            <AccordionDetails>\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('posa-periodo', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderPosaPeriodoReport(reportsData.posaPeriodo)}\n              </Box>\n            </AccordionDetails>\n          </Accordion>\n        )}\n      </Box>\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;;AAExD;AACA,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,cAAc,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB;EAAW,CAAC,GAAGhB,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEiB;EAAK,CAAC,GAAGhB,OAAO,CAAC,CAAC;EAE1B,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+E,KAAK,EAAEC,QAAQ,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiF,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACmF,cAAc,EAAEC,iBAAiB,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqF,UAAU,EAAEC,aAAa,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuF,UAAU,EAAEC,aAAa,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyF,QAAQ,EAAEC,WAAW,CAAC,GAAG1F,QAAQ,CAAC;IACvC2F,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhG,QAAQ,CAAC;IAC7CiG,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,IAAI;IACrBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxG,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwG,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC3B,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAM4B,eAAe,GAAG5C,aAAa,CAAC6C,iBAAiB,CAAChC,UAAU,EAAE,OAAO,CAAC,CACzEiC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,gCAAgC,EAAE8B,GAAG,CAAC;UACpD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMC,UAAU,GAAGlD,aAAa,CAACmD,mBAAmB,CAACtC,UAAU,EAAE,OAAO,CAAC,CACtEiC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,2BAA2B,EAAE8B,GAAG,CAAC;UAC/C,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMG,aAAa,GAAGpD,aAAa,CAACqD,eAAe,CAACxC,UAAU,EAAE,OAAO,CAAC,CACrEiC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,8BAA8B,EAAE8B,GAAG,CAAC;UAClD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMK,gBAAgB,GAAGtD,aAAa,CAACuD,kBAAkB,CAAC1C,UAAU,EAAE,OAAO,CAAC,CAC3EiC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,kCAAkC,EAAE8B,GAAG,CAAC;UACtD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;;QAEJ;QACA,MAAM,CAACO,YAAY,EAAEC,OAAO,EAAEC,UAAU,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3EjB,eAAe,EACfM,UAAU,EACVE,aAAa,EACbE,gBAAgB,CACjB,CAAC;;QAEF;QACApB,cAAc,CAAC;UACbC,QAAQ,EAAEqB,YAAY,CAACP,OAAO;UAC9Bb,GAAG,EAAEqB,OAAO,CAACR,OAAO;UACpBZ,MAAM,EAAEqB,UAAU,CAACT,OAAO;UAC1BX,SAAS,EAAEqB,aAAa,CAACV,OAAO;UAChCV,eAAe,EAAE,IAAI;UACrBC,WAAW,EAAE;QACf,CAAC,CAAC;;QAEF;QACA,IAAIgB,YAAY,CAACP,OAAO,IAAIQ,OAAO,CAACR,OAAO,IAAIS,UAAU,CAACT,OAAO,IAAIU,aAAa,CAACV,OAAO,EAAE;UAC1F/B,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,MAAM;UACLA,QAAQ,CAAC,uDAAuD,CAAC;QACnE;MACF,CAAC,CAAC,OAAO6B,GAAG,EAAE;QACZ;QACAC,OAAO,CAAC/B,KAAK,CAAC,mCAAmC,EAAE8B,GAAG,CAAC;QACvD7B,QAAQ,CAAC,uDAAuD,CAAC;MACnE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIH,UAAU,EAAE;MACd8B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC9B,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMiD,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,2FAA2F;IACxGC,IAAI,eAAE3D,OAAA,CAACtC,cAAc;MAAAkG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,yBAAyB;EACrH,CAAC,EACD;IACET,EAAE,EAAE,KAAK;IACTC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,wEAAwE;IACrFC,IAAI,eAAE3D,OAAA,CAAC9B,QAAQ;MAAA0F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,0BAA0B,EAAE,qBAAqB,EAAE,eAAe;EAC1G,CAAC,EACD;IACET,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,uEAAuE;IACpFC,IAAI,eAAE3D,OAAA,CAAChB,aAAa;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,iBAAiB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,yEAAyE;IACtFC,IAAI,eAAE3D,OAAA,CAAClB,SAAS;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,oBAAoB;EAC7F,CAAC,EACD;IACET,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,eAAE3D,OAAA,CAAChC,YAAY;MAAA4F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,mBAAmB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,iFAAiF;IAC9FC,IAAI,eAAE3D,OAAA,CAACpC,YAAY;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,2BAA2B,EAAE,eAAe,EAAE,kBAAkB;EAC/F,CAAC,CACF;;EAED;EACA,MAAMC,wBAAwB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,MAAM,KAAK;IAC7D,IAAI;MACF3D,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI0D,QAAQ;MAEZ,QAAQF,UAAU;QAChB,KAAK,UAAU;UACbE,QAAQ,GAAG,MAAM5E,aAAa,CAAC6C,iBAAiB,CAAChC,UAAU,EAAE8D,MAAM,CAAC;UACpE;QACF,KAAK,KAAK;UACRC,QAAQ,GAAG,MAAM5E,aAAa,CAACmD,mBAAmB,CAACtC,UAAU,EAAE8D,MAAM,CAAC;UACtE;QACF,KAAK,QAAQ;UACXC,QAAQ,GAAG,MAAM5E,aAAa,CAACqD,eAAe,CAACxC,UAAU,EAAE8D,MAAM,CAAC;UAClE;QACF,KAAK,YAAY;UACfC,QAAQ,GAAG,MAAM5E,aAAa,CAACuD,kBAAkB,CAAC1C,UAAU,EAAE8D,MAAM,CAAC;UACrE;QACF,KAAK,kBAAkB;UACrB,IAAI,CAAChD,QAAQ,CAACK,SAAS,EAAE;YACvBd,QAAQ,CAAC,8BAA8B,CAAC;YACxC;UACF;UACA0D,QAAQ,GAAG,MAAM5E,aAAa,CAAC6E,eAAe,CAAChE,UAAU,EAAEc,QAAQ,CAACK,SAAS,EAAE2C,MAAM,CAAC;UACtF;QACF,KAAK,cAAc;UACjB,IAAI,CAAChD,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDb,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACA0D,QAAQ,GAAG,MAAM5E,aAAa,CAAC8E,uBAAuB,CACpDjE,UAAU,EACVc,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClB4C,MACF,CAAC;UACD;QACF;UACE,MAAM,IAAII,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIJ,MAAM,KAAK,OAAO,EAAE;QACtB;QACA,IAAID,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,cAAc,EAAE;UACtExC,cAAc,CAAC8C,IAAI,KAAK;YACtB,GAAGA,IAAI;YACP,CAACN,UAAU,KAAK,kBAAkB,GAAG,iBAAiB,GAAG,aAAa,GAAGE,QAAQ,CAAC3B;UACpF,CAAC,CAAC,CAAC;QACL;QACA7B,aAAa,CAACwD,QAAQ,CAAC3B,OAAO,CAAC;MACjC,CAAC,MAAM;QACL;QACA,IAAI2B,QAAQ,CAACK,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAACP,QAAQ,CAACK,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;IACF,CAAC,CAAC,OAAOlC,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,sCAAsC,EAAE8B,GAAG,CAAC;MAC1D7B,QAAQ,CAAC6B,GAAG,CAACqC,MAAM,IAAIrC,GAAG,CAACsC,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACRrE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsE,kBAAkB,GAAIZ,UAAU,IAAK;IACzCpD,iBAAiB,CAACoD,UAAU,CAAC;IAC7BhD,aAAa,CAACgD,UAAU,CAACX,EAAE,CAAC;;IAE5B;IACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,IAAIW,UAAU,CAACX,EAAE,KAAK,kBAAkB,EAAE;MAC5E;MACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,EAAE;QACpC,MAAMwB,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;QACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;QAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAExC/D,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXG,WAAW,EAAE2D,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAClD9D,SAAS,EAAEwD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC;MACJ;MAEArE,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACL;MACAiD,wBAAwB,CAACC,UAAU,CAACX,EAAE,EAAE,OAAO,CAAC;IAClD;EACF,CAAC;EAED,MAAM+B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMrB,wBAAwB,CAAChD,UAAU,EAAEE,QAAQ,CAACE,OAAO,CAAC;IAC5DL,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMuE,iBAAiB,GAAGA,CAAA,KAAM;IAC9BvE,aAAa,CAAC,KAAK,CAAC;IACpBN,QAAQ,CAAC,IAAI,CAAC;IACdU,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgE,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC7E,UAAU,EAAE,OAAO,IAAI;IAE5B,oBACEZ,OAAA,CAACjE,KAAK;MAAC2J,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzB7F,OAAA,CAACnE,GAAG;QAAC6J,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzF7F,OAAA,CAAClE,UAAU;UAACoK,OAAO,EAAC,IAAI;UAAAL,QAAA,GACrB/E,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE2C,KAAK,EAAC,KAAG,EAAC7C,UAAU,CAACuF,aAAa;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACb/D,OAAA,CAACnE,GAAG;UAAC6J,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE;UAAE,CAAE;UAAAP,QAAA,gBAEnC7F,OAAA,CAAC5D,MAAM;YACLiK,SAAS,eAAErG,OAAA,CAAC5B,YAAY;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAChD,UAAU,EAAE,KAAK,CAAE;YAC3DgF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZvC,KAAK,EAAC,SAAS;YAAA6B,QAAA,EAChB;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC5D,MAAM;YACLiK,SAAS,eAAErG,OAAA,CAAC5B,YAAY;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAChD,UAAU,EAAE,OAAO,CAAE;YAC7DgF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZvC,KAAK,EAAC,SAAS;YAAA6B,QAAA,EAChB;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC5D,MAAM;YACLiK,SAAS,eAAErG,OAAA,CAACxB,WAAW;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BuC,OAAO,EAAEA,CAAA,KAAMzF,aAAa,CAAC,IAAI,CAAE;YACnCqF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YAAAV,QAAA,EACb;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/D,OAAA,CAACxD,OAAO;QAACkJ,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE;MAAE;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGzB7C,UAAU,KAAK,UAAU,IAAIsF,oBAAoB,CAAC5F,UAAU,CAAC,EAC7DM,UAAU,KAAK,KAAK,IAAIuF,eAAe,CAAC7F,UAAU,CAAC,EACnDM,UAAU,KAAK,QAAQ,IAAIwF,kBAAkB,CAAC9F,UAAU,CAAC,EACzDM,UAAU,KAAK,kBAAkB,IAAIyF,2BAA2B,CAAC/F,UAAU,CAAC,EAC5EM,UAAU,KAAK,cAAc,IAAI0F,uBAAuB,CAAChG,UAAU,CAAC,EACpEM,UAAU,KAAK,YAAY,IAAI2F,qBAAqB,CAACjG,UAAU,CAAC;IAAA;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC;EAEZ,CAAC;EAED,MAAMyC,oBAAoB,GAAIM,IAAI,iBAChC9G,OAAA,CAAChE,IAAI;IAAC+K,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAnB,QAAA,gBAEzB7F,OAAA,CAAChE,IAAI;MAACiL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAACnE,GAAG;QAAC6J,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,eAC9D7F,OAAA,CAACxC,gBAAgB;UACf2J,OAAO,eACLnH,OAAA,CAACzC,MAAM;YACL6J,OAAO,EAAElF,UAAW;YACpBmF,QAAQ,EAAGC,CAAC,IAAKnF,aAAa,CAACmF,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;YACjDpD,KAAK,EAAC;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CACF;UACDyD,KAAK,eACHxH,OAAA,CAACnE,GAAG;YAAC6J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAACZ,aAAa;cAACsG,EAAE,EAAE;gBAAE+B,EAAE,EAAE;cAAE;YAAE;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAElC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGN7B,UAAU,iBACTlC,OAAA,CAAChE,IAAI;MAACiL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAACN,aAAa;QAACoH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CACP,eAED/D,OAAA,CAAChE,IAAI;MAACiL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC5C,SAAS;QAACsK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC3C,gBAAgB;UAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAAClE,UAAU;YAACoK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAoB;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;UAAAuI,QAAA,eACf7F,OAAA,CAAC/D,IAAI;YAAA4J,QAAA,eACH7F,OAAA,CAAC9D,WAAW;cAAA2J,QAAA,gBACV7F,OAAA,CAAClE,UAAU;gBAAA+J,QAAA,GAAC,gBAAc,eAAA7F,OAAA;kBAAA6F,QAAA,GAASiB,IAAI,CAACc,YAAY,EAAC,GAAC;gBAAA;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5E/D,OAAA,CAAClE,UAAU;gBAAA+J,QAAA,GAAC,gBAAc,eAAA7F,OAAA;kBAAA6F,QAAA,GAASiB,IAAI,CAACe,YAAY,EAAC,GAAC;gBAAA;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5E/D,OAAA,CAAClE,UAAU;gBAAA+J,QAAA,GAAC,mBAAiB,eAAA7F,OAAA;kBAAA6F,QAAA,GAASiB,IAAI,CAACgB,eAAe,EAAC,GAAC;gBAAA;kBAAAlE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClF/D,OAAA,CAAClE,UAAU;gBAAA+J,QAAA,GAAC,eAAa,eAAA7F,OAAA;kBAAA6F,QAAA,GAASiB,IAAI,CAACiB,uBAAuB,EAAC,GAAC;gBAAA;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEP/D,OAAA,CAAChE,IAAI;MAACiL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC5C,SAAS;QAACsK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC3C,gBAAgB;UAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAAClE,UAAU;YAACoK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAI;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;UAAAuI,QAAA,eACf7F,OAAA,CAAC/D,IAAI;YAAA4J,QAAA,eACH7F,OAAA,CAAC9D,WAAW;cAAA2J,QAAA,gBACV7F,OAAA,CAAClE,UAAU;gBAAA+J,QAAA,GAAC,eAAa,eAAA7F,OAAA;kBAAA6F,QAAA,EAASiB,IAAI,CAACkB;gBAAW;kBAAApE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzE/D,OAAA,CAAClE,UAAU;gBAAA+J,QAAA,GAAC,eAAa,eAAA7F,OAAA;kBAAA6F,QAAA,EAASiB,IAAI,CAACmB;gBAAW;kBAAArE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzE/D,OAAA,CAAClE,UAAU;gBAAA+J,QAAA,GAAC,kBAAgB,eAAA7F,OAAA;kBAAA6F,QAAA,EAASiB,IAAI,CAACoB;gBAAc;kBAAAtE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/E/D,OAAA,CAAClE,UAAU;gBAAA+J,QAAA,GAAC,oBAAkB,eAAA7F,OAAA;kBAAA6F,QAAA,GAASiB,IAAI,CAACqB,gBAAgB,EAAC,GAAC;gBAAA;kBAAAvE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEP/D,OAAA,CAAChE,IAAI;MAACiL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC5C,SAAS;QAACsK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC3C,gBAAgB;UAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAAClE,UAAU;YAACoK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAW;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;UAAAuI,QAAA,eACf7F,OAAA,CAAC/D,IAAI;YAAA4J,QAAA,eACH7F,OAAA,CAAC9D,WAAW;cAAA2J,QAAA,gBACV7F,OAAA,CAAClE,UAAU;gBAAA+J,QAAA,GAAC,qBAAmB,eAAA7F,OAAA;kBAAA6F,QAAA,GAASiB,IAAI,CAACsB,iBAAiB,EAAC,UAAQ;gBAAA;kBAAAxE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAC5F+C,IAAI,CAACuB,cAAc,iBAClBrI,OAAA,CAAAE,SAAA;gBAAA2F,QAAA,gBACE7F,OAAA,CAAClE,UAAU;kBAAA+J,QAAA,GAAC,kBAAgB,eAAA7F,OAAA;oBAAA6F,QAAA,GAASiB,IAAI,CAACuB,cAAc,EAAC,SAAO;kBAAA;oBAAAzE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtF/D,OAAA,CAAClE,UAAU;kBAAA+J,QAAA,GAAC,sBAAoB,eAAA7F,OAAA;oBAAA6F,QAAA,EAASiB,IAAI,CAACwB;kBAAkB;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA,eACvF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAEN+C,IAAI,CAACyB,YAAY,IAAIzB,IAAI,CAACyB,YAAY,CAACC,MAAM,GAAG,CAAC,iBAChDxI,OAAA,CAAChE,IAAI;MAACiL,IAAI;MAACC,EAAE,EAAE,EAAG;MAAArB,QAAA,eAChB7F,OAAA,CAAC5C,SAAS;QAACsK,eAAe;QAAA7B,QAAA,gBACxB7F,OAAA,CAAC3C,gBAAgB;UAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAAClE,UAAU;YAACoK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAY;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;UAAAuI,QAAA,eACf7F,OAAA,CAAC/D,IAAI;YAAA4J,QAAA,eACH7F,OAAA,CAAC9D,WAAW;cAAA2J,QAAA,EACTiB,IAAI,CAACyB,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7C5I,OAAA,CAAClE,UAAU;gBAAA+J,QAAA,GACR8C,IAAI,CAAC7B,IAAI,EAAC,IAAE,eAAA9G,OAAA;kBAAA6F,QAAA,GAAS8C,IAAI,CAACE,KAAK,EAAC,GAAC;gBAAA;kBAAAjF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GAD5B6E,KAAK;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CACP;EAED,MAAM0C,eAAe,GAAIK,IAAI;IAAA,IAAAgC,mBAAA,EAAAC,qBAAA;IAAA,oBAC3B/I,OAAA,CAAChE,IAAI;MAAC+K,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,GAExB3D,UAAU,iBACTlC,OAAA,CAAChE,IAAI;QAACiL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB7F,OAAA,CAACJ,QAAQ;UAACkH,IAAI,EAAEA;QAAK;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CACP,eAED/D,OAAA,CAAChE,IAAI;QAACiL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB7F,OAAA,CAAC5C,SAAS;UAACsK,eAAe;UAAA7B,QAAA,gBACxB7F,OAAA,CAAC3C,gBAAgB;YAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C7F,OAAA,CAAClE,UAAU;cAACoK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;YAAAuI,QAAA,eACf7F,OAAA,CAAChE,IAAI;cAAC+K,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnB,QAAA,GAAAiD,mBAAA,GACxBhC,IAAI,CAACkC,aAAa,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoBJ,GAAG,CAAC,CAACO,IAAI,EAAEL,KAAK,kBACnC5I,OAAA,CAAChE,IAAI;gBAACiL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACgC,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAAtD,QAAA,eAC9B7F,OAAA,CAAC/D,IAAI;kBAAA4J,QAAA,eACH7F,OAAA,CAAC9D,WAAW;oBAAA2J,QAAA,gBACV7F,OAAA,CAAClE,UAAU;sBAACoK,OAAO,EAAC,WAAW;sBAAAL,QAAA,EAAEoD,IAAI,CAACG;oBAAS;sBAAAxF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC7D/D,OAAA,CAAClE,UAAU;sBAACoK,OAAO,EAAC,OAAO;sBAAAL,QAAA,GAAC,WAAS,EAACoD,IAAI,CAACI,OAAO;oBAAA;sBAAAzF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAChE/D,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,GAAC,QAAM,EAACoD,IAAI,CAACK,QAAQ;oBAAA;sBAAA1F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC9C/D,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,GAAC,iBAAe,EAACoD,IAAI,CAACM,aAAa,EAAC,GAAC;oBAAA;sBAAA3F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC7D/D,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,GAAC,eAAa,EAACoD,IAAI,CAACO,WAAW,EAAC,GAAC;oBAAA;sBAAA5F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzD/D,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,GAAC,aAAW,EAACoD,IAAI,CAACnB,eAAe,EAAC,GAAC;oBAAA;sBAAAlE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAV6B6E,KAAK;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWrC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEP/D,OAAA,CAAChE,IAAI;QAACiL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB7F,OAAA,CAAC5C,SAAS;UAACsK,eAAe;UAAA7B,QAAA,gBACxB7F,OAAA,CAAC3C,gBAAgB;YAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C7F,OAAA,CAAClE,UAAU;cAACoK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;YAAAuI,QAAA,eACf7F,OAAA,CAAChE,IAAI;cAAC+K,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnB,QAAA,GAAAkD,qBAAA,GACxBjC,IAAI,CAAC2C,eAAe,cAAAV,qBAAA,uBAApBA,qBAAA,CAAsBL,GAAG,CAAC,CAACgB,MAAM,EAAEd,KAAK,kBACvC5I,OAAA,CAAChE,IAAI;gBAACiL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACgC,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAAtD,QAAA,eAC9B7F,OAAA,CAAC/D,IAAI;kBAAA4J,QAAA,eACH7F,OAAA,CAAC9D,WAAW;oBAAA2J,QAAA,gBACV7F,OAAA,CAAClE,UAAU;sBAACoK,OAAO,EAAC,WAAW;sBAAAL,QAAA,EAAE6D,MAAM,CAACN;oBAAS;sBAAAxF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC/D/D,OAAA,CAAClE,UAAU;sBAACoK,OAAO,EAAC,OAAO;sBAAAL,QAAA,GAAC,WAAS,EAAC6D,MAAM,CAACL,OAAO;oBAAA;sBAAAzF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAClE/D,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,GAAC,UAAQ,EAAC6D,MAAM,CAACC,UAAU;oBAAA;sBAAA/F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACpD/D,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,GAAC,qBAAmB,EAAC6D,MAAM,CAACE,iBAAiB,EAAC,GAAC;oBAAA;sBAAAhG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAR6B6E,KAAK;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASrC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM2C,kBAAkB,GAAII,IAAI;IAAA,IAAA+C,YAAA;IAAA,oBAC9B7J,OAAA,CAAChE,IAAI;MAAC+K,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,GAExB3D,UAAU,iBACTlC,OAAA,CAAChE,IAAI;QAACiL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB7F,OAAA,CAACL,WAAW;UAACmH,IAAI,EAAEA;QAAK;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CACP,eAED/D,OAAA,CAAChE,IAAI;QAACiL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB7F,OAAA,CAAC5C,SAAS;UAACsK,eAAe;UAAA7B,QAAA,gBACxB7F,OAAA,CAAC3C,gBAAgB;YAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C7F,OAAA,CAAClE,UAAU;cAACoK,OAAO,EAAC,IAAI;cAAAL,QAAA,GAAC,uBACF,EAACiB,IAAI,CAACgD,aAAa,EAAC,UAC3C;YAAA;cAAAlG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;YAAAuI,QAAA,eACf7F,OAAA,CAAChE,IAAI;cAAC+K,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnB,QAAA,GAAAgE,YAAA,GACxB/C,IAAI,CAAChF,MAAM,cAAA+H,YAAA,uBAAXA,YAAA,CAAanB,GAAG,CAAC,CAACgB,MAAM,EAAEd,KAAK,kBAC9B5I,OAAA,CAAChE,IAAI;gBAACiL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACgC,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAAtD,QAAA,eAC9B7F,OAAA,CAAC/D,IAAI;kBAAA4J,QAAA,eACH7F,OAAA,CAAC9D,WAAW;oBAAA2J,QAAA,gBACV7F,OAAA,CAAClE,UAAU;sBAACoK,OAAO,EAAC,WAAW;sBAAAL,QAAA,EAAE6D,MAAM,CAACjI;oBAAS;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC/D/D,OAAA,CAAClE,UAAU;sBAACoK,OAAO,EAAC,OAAO;sBAAAL,QAAA,GAAE6D,MAAM,CAACN,SAAS,EAAC,KAAG,EAACM,MAAM,CAACL,OAAO;oBAAA;sBAAAzF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eAC9E/D,OAAA,CAAC3D,IAAI;sBACHmL,KAAK,EAAEkC,MAAM,CAACK,KAAM;sBACpB/F,KAAK,EAAE0F,MAAM,CAACK,KAAK,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;sBAC9DxD,IAAI,EAAC,OAAO;sBACZb,EAAE,EAAE;wBAAEO,EAAE,EAAE;sBAAE;oBAAE;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC,eACF/D,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,GAAC,gBAAc,EAAC6D,MAAM,CAAC9B,YAAY,EAAC,GAAC;oBAAA;sBAAAhE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC7D/D,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,GAAC,iBAAe,EAAC6D,MAAM,CAACM,aAAa,EAAC,GAAC;oBAAA;sBAAApG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC/D/D,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,GAAC,oBAAkB,EAAC6D,MAAM,CAACO,gBAAgB,EAAC,GAAC;oBAAA;sBAAArG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACrE/D,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,GAAC,YAAU,EAAC6D,MAAM,CAACQ,oBAAoB,EAAC,GAAC;oBAAA;sBAAAtG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAhB6B6E,KAAK;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBrC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM4C,2BAA2B,GAAIG,IAAI;IAAA,IAAAqD,YAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,oBAAA;IAAA,oBACvC5K,OAAA,CAAChE,IAAI;MAAC+K,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,gBACzB7F,OAAA,CAAChE,IAAI;QAACiL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB7F,OAAA,CAAC5C,SAAS;UAACsK,eAAe;UAAA7B,QAAA,gBACxB7F,OAAA,CAAC3C,gBAAgB;YAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C7F,OAAA,CAAClE,UAAU;cAACoK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAe;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;YAAAuI,QAAA,eACf7F,OAAA,CAAC/D,IAAI;cAAA4J,QAAA,eACH7F,OAAA,CAAC9D,WAAW;gBAAA2J,QAAA,gBACV7F,OAAA,CAAClE,UAAU;kBAAA+J,QAAA,GAAC,MAAI,eAAA7F,OAAA;oBAAA6F,QAAA,GAAAsE,YAAA,GAASrD,IAAI,CAAC4C,MAAM,cAAAS,YAAA,uBAAXA,YAAA,CAAa1I;kBAAS;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtE/D,OAAA,CAAClE,UAAU;kBAAA+J,QAAA,GAAC,aAAW,eAAA7F,OAAA;oBAAA6F,QAAA,GAAAuE,aAAA,GAAStD,IAAI,CAAC4C,MAAM,cAAAU,aAAA,uBAAXA,aAAA,CAAahB;kBAAS;oBAAAxF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7E/D,OAAA,CAAClE,UAAU;kBAAA+J,QAAA,GAAC,WAAS,eAAA7F,OAAA;oBAAA6F,QAAA,GAAAwE,aAAA,GAASvD,IAAI,CAAC4C,MAAM,cAAAW,aAAA,uBAAXA,aAAA,CAAahB;kBAAO;oBAAAzF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzE/D,OAAA,CAAC3D,IAAI;kBACHmL,KAAK,GAAA8C,aAAA,GAAExD,IAAI,CAAC4C,MAAM,cAAAY,aAAA,uBAAXA,aAAA,CAAaP,KAAM;kBAC1B/F,KAAK,EAAE,EAAAuG,aAAA,GAAAzD,IAAI,CAAC4C,MAAM,cAAAa,aAAA,uBAAXA,aAAA,CAAaR,KAAK,MAAK,aAAa,GAAG,SAAS,GAAG,SAAU;kBACpErE,EAAE,EAAE;oBAAEmF,EAAE,EAAE;kBAAE;gBAAE;kBAAAjH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACF/D,OAAA,CAAClE,UAAU;kBAAA+J,QAAA,GAAC,gBAAc,eAAA7F,OAAA;oBAAA6F,QAAA,IAAA2E,aAAA,GAAS1D,IAAI,CAAC4C,MAAM,cAAAc,aAAA,uBAAXA,aAAA,CAAa5C,YAAY,EAAC,GAAC;kBAAA;oBAAAhE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpF/D,OAAA,CAAClE,UAAU;kBAAA+J,QAAA,GAAC,iBAAe,eAAA7F,OAAA;oBAAA6F,QAAA,IAAA4E,aAAA,GAAS3D,IAAI,CAAC4C,MAAM,cAAAe,aAAA,uBAAXA,aAAA,CAAaT,aAAa,EAAC,GAAC;kBAAA;oBAAApG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtF/D,OAAA,CAAClE,UAAU;kBAAA+J,QAAA,GAAC,oBAAkB,eAAA7F,OAAA;oBAAA6F,QAAA,IAAA6E,aAAA,GAAS5D,IAAI,CAAC4C,MAAM,cAAAgB,aAAA,uBAAXA,aAAA,CAAaT,gBAAgB,EAAC,GAAC;kBAAA;oBAAArG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5F/D,OAAA,CAAClE,UAAU;kBAAA+J,QAAA,GAAC,YAAU,eAAA7F,OAAA;oBAAA6F,QAAA,IAAA8E,aAAA,GAAS7D,IAAI,CAAC4C,MAAM,cAAAiB,aAAA,uBAAXA,aAAA,CAAaT,oBAAoB,EAAC,GAAC;kBAAA;oBAAAtG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEP/D,OAAA,CAAChE,IAAI;QAACiL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB7F,OAAA,CAAC5C,SAAS;UAACsK,eAAe;UAAA7B,QAAA,gBACxB7F,OAAA,CAAC3C,gBAAgB;YAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C7F,OAAA,CAAClE,UAAU;cAACoK,OAAO,EAAC,IAAI;cAAAL,QAAA,GAAC,kBACP,EAACiB,IAAI,CAACkB,WAAW,EAAC,GACpC;YAAA;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;YAAAuI,QAAA,eACf7F,OAAA,CAAC/D,IAAI;cAAA4J,QAAA,eACH7F,OAAA,CAAC9D,WAAW;gBAAA2J,QAAA,eACV7F,OAAA,CAACnE,GAAG;kBAAC6J,EAAE,EAAE;oBAAEoF,SAAS,EAAE,GAAG;oBAAEC,QAAQ,EAAE;kBAAO,CAAE;kBAAAlF,QAAA,GAAA+E,oBAAA,GAC3C9D,IAAI,CAACkE,cAAc,cAAAJ,oBAAA,uBAAnBA,oBAAA,CAAqBlC,GAAG,CAAC,CAACO,IAAI,EAAEL,KAAK,kBACpC5I,OAAA,CAACnE,GAAG;oBAAa6J,EAAE,EAAE;sBAAEO,EAAE,EAAE,CAAC;sBAAEN,CAAC,EAAE,CAAC;sBAAEsF,MAAM,EAAE,mBAAmB;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAAArF,QAAA,gBACjF7F,OAAA,CAAClE,UAAU;sBAACoK,OAAO,EAAC,OAAO;sBAAAL,QAAA,eAAC7F,OAAA;wBAAA6F,QAAA,EAASoD,IAAI,CAACkC;sBAAO;wBAAAvH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACxE/D,OAAA,CAAClE,UAAU;sBAACoK,OAAO,EAAC,SAAS;sBAAAL,QAAA,GAC1BoD,IAAI,CAACmC,OAAO,EAAC,KAAG,EAACnC,IAAI,CAACoC,OAAO,EAAC,KAAG,EAACpC,IAAI,CAACG,SAAS;oBAAA;sBAAAxF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACb/D,OAAA,CAAClE,UAAU;sBAACoK,OAAO,EAAC,SAAS;sBAACJ,OAAO,EAAC,OAAO;sBAAAD,QAAA,GAAC,WACnC,EAACoD,IAAI,CAACM,aAAa,EAAC,aAAW,EAACN,IAAI,CAACO,WAAW,EAAC,aAAW,EAACP,IAAI,CAACc,KAAK;oBAAA;sBAAAnG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA,GAPL6E,KAAK;oBAAAhF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM6C,uBAAuB,GAAIE,IAAI;IAAA,IAAAwE,qBAAA;IAAA,oBACnCtL,OAAA,CAAChE,IAAI;MAAC+K,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,GAExB3D,UAAU,iBACTlC,OAAA,CAAChE,IAAI;QAACiL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB7F,OAAA,CAACH,aAAa;UAACiH,IAAI,EAAEA;QAAK;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CACP,eAED/D,OAAA,CAAChE,IAAI;QAACiL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB7F,OAAA,CAAC5C,SAAS;UAACsK,eAAe;UAAA7B,QAAA,gBACxB7F,OAAA,CAAC3C,gBAAgB;YAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C7F,OAAA,CAAClE,UAAU;cAACoK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAmB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;YAAAuI,QAAA,eACf7F,OAAA,CAAC/D,IAAI;cAAA4J,QAAA,eACH7F,OAAA,CAAC9D,WAAW;gBAAA2J,QAAA,gBACV7F,OAAA,CAAClE,UAAU;kBAAA+J,QAAA,GAAC,WAAS,eAAA7F,OAAA;oBAAA6F,QAAA,GAASiB,IAAI,CAACvF,WAAW,EAAC,KAAG,EAACuF,IAAI,CAACtF,SAAS;kBAAA;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxF/D,OAAA,CAAClE,UAAU;kBAAA+J,QAAA,GAAC,gBAAc,eAAA7F,OAAA;oBAAA6F,QAAA,GAASiB,IAAI,CAACyE,oBAAoB,EAAC,GAAC;kBAAA;oBAAA3H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpF/D,OAAA,CAAClE,UAAU;kBAAA+J,QAAA,GAAC,iBAAe,eAAA7F,OAAA;oBAAA6F,QAAA,EAASiB,IAAI,CAAC0E;kBAAa;oBAAA5H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7E/D,OAAA,CAAClE,UAAU;kBAAA+J,QAAA,GAAC,qBAAmB,eAAA7F,OAAA;oBAAA6F,QAAA,GAASiB,IAAI,CAACsB,iBAAiB,EAAC,UAAQ;kBAAA;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEP/D,OAAA,CAAChE,IAAI;QAACiL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB7F,OAAA,CAAC5C,SAAS;UAACsK,eAAe;UAAA7B,QAAA,gBACxB7F,OAAA,CAAC3C,gBAAgB;YAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C7F,OAAA,CAAClE,UAAU;cAACoK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAgB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;YAAAuI,QAAA,eACf7F,OAAA,CAAC/D,IAAI;cAAA4J,QAAA,eACH7F,OAAA,CAAC9D,WAAW;gBAAA2J,QAAA,eACV7F,OAAA,CAACnE,GAAG;kBAAC6J,EAAE,EAAE;oBAAEoF,SAAS,EAAE,GAAG;oBAAEC,QAAQ,EAAE;kBAAO,CAAE;kBAAAlF,QAAA,GAAAyF,qBAAA,GAC3CxE,IAAI,CAAC2E,gBAAgB,cAAAH,qBAAA,uBAArBA,qBAAA,CAAuB5C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACtC5I,OAAA,CAACnE,GAAG;oBAAa6J,EAAE,EAAE;sBAAEI,OAAO,EAAE,MAAM;sBAAEC,cAAc,EAAE,eAAe;sBAAE2F,EAAE,EAAE;oBAAI,CAAE;oBAAA7F,QAAA,gBACjF7F,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,EAAE8C,IAAI,CAAC7B;oBAAI;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC,eACpC/D,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,eAAC7F,OAAA;wBAAA6F,QAAA,GAAS8C,IAAI,CAACE,KAAK,EAAC,GAAC;sBAAA;wBAAAjF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA,GAF/C6E,KAAK;oBAAAhF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM8C,qBAAqB,GAAIC,IAAI;IAAA,IAAA6E,oBAAA;IAAA,oBACjC3L,OAAA,CAAChE,IAAI;MAAC+K,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,eACzB7F,OAAA,CAAChE,IAAI;QAACiL,IAAI;QAACC,EAAE,EAAE,EAAG;QAAArB,QAAA,eAChB7F,OAAA,CAAC5C,SAAS;UAACsK,eAAe;UAAA7B,QAAA,gBACxB7F,OAAA,CAAC3C,gBAAgB;YAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA8B,QAAA,eAC/C7F,OAAA,CAAClE,UAAU;cAACoK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAA+B;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;YAAAuI,QAAA,eACf7F,OAAA,CAAChE,IAAI;cAAC+K,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAnB,QAAA,GAAA8F,oBAAA,GACxB7E,IAAI,CAAC8E,cAAc,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBjD,GAAG,CAAC,CAACqB,KAAK,EAAEnB,KAAK,kBACrC5I,OAAA,CAAChE,IAAI;gBAACiL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACgC,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAAtD,QAAA,eAC9B7F,OAAA,CAAC/D,IAAI;kBAAA4J,QAAA,eACH7F,OAAA,CAAC9D,WAAW;oBAAA2J,QAAA,gBACV7F,OAAA,CAAClE,UAAU;sBAACoK,OAAO,EAAC,IAAI;sBAAC2F,YAAY;sBAAAhG,QAAA,eACnC7F,OAAA,CAAC3D,IAAI;wBACHmL,KAAK,EAAEuC,KAAK,CAACA,KAAM;wBACnB/F,KAAK,EAAE+F,KAAK,CAACA,KAAK,KAAK,YAAY,GAAG,SAAS,GAAG,SAAU;wBAC5DrE,EAAE,EAAE;0BAAEO,EAAE,EAAE;wBAAE;sBAAE;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACf;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ,CAAC,eACb/D,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,GAAC,eAAa,eAAA7F,OAAA;wBAAA6F,QAAA,EAASkE,KAAK,CAACT;sBAAQ;wBAAA1F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvE/D,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,GAAC,iBAAe,eAAA7F,OAAA;wBAAA6F,QAAA,GAASkE,KAAK,CAACR,aAAa,EAAC,GAAC;sBAAA;wBAAA3F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC/E/D,OAAA,CAAClE,UAAU;sBAAA+J,QAAA,GAAC,eAAa,eAAA7F,OAAA;wBAAA6F,QAAA,GAASkE,KAAK,CAACP,WAAW,EAAC,GAAC;sBAAA;wBAAA5F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAd6B6E,KAAK;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAerC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM+H,YAAY,GAAGA,CAAA,kBACnB9L,OAAA,CAACrD,MAAM;IAACiI,IAAI,EAAE5D,UAAW;IAAC+K,OAAO,EAAEvG,iBAAkB;IAACwG,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAApG,QAAA,gBAC3E7F,OAAA,CAACpD,WAAW;MAAAiJ,QAAA,EACT/E,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE2C;IAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACd/D,OAAA,CAACnD,aAAa;MAAAgJ,QAAA,GACXnF,KAAK,iBACJV,OAAA,CAAC1D,KAAK;QAAC4P,QAAQ,EAAC,OAAO;QAACxG,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EACnCnF;MAAK;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAED/D,OAAA,CAAChE,IAAI;QAAC+K,SAAS;QAACC,OAAO,EAAE,CAAE;QAACtB,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACxC7F,OAAA,CAAChE,IAAI;UAACiL,IAAI;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAChB7F,OAAA,CAACjD,WAAW;YAACkP,SAAS;YAAApG,QAAA,gBACpB7F,OAAA,CAAChD,UAAU;cAAA6I,QAAA,EAAC;YAAO;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChC/D,OAAA,CAAC/C,MAAM;cACLkP,KAAK,EAAE/K,QAAQ,CAACE,OAAQ;cACxBkG,KAAK,EAAC,SAAS;cACfH,QAAQ,EAAGC,CAAC,IAAKjG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAEgG,CAAC,CAACC,MAAM,CAAC4E;cAAM,CAAC,CAAE;cAAAtG,QAAA,gBAEvE7F,OAAA,CAAC9C,QAAQ;gBAACiP,KAAK,EAAC,OAAO;gBAAAtG,QAAA,EAAC;cAAoB;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvD/D,OAAA,CAAC9C,QAAQ;gBAACiP,KAAK,EAAC,KAAK;gBAAAtG,QAAA,EAAC;cAAY;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7C/D,OAAA,CAAC9C,QAAQ;gBAACiP,KAAK,EAAC,OAAO;gBAAAtG,QAAA,EAAC;cAAc;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAEN7C,UAAU,KAAK,kBAAkB,iBAChClB,OAAA,CAAChE,IAAI;UAACiL,IAAI;UAACC,EAAE,EAAE,EAAG;UAAArB,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;YACR8O,SAAS;YACTzE,KAAK,EAAC,WAAW;YACjB2E,KAAK,EAAE/K,QAAQ,CAACK,SAAU;YAC1B4F,QAAQ,EAAGC,CAAC,IAAKjG,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEK,SAAS,EAAE6F,CAAC,CAACC,MAAM,CAAC4E;YAAM,CAAC,CAAE;YACzEC,WAAW,EAAC,mBAAmB;YAC/BC,UAAU,EAAC;UAA0D;YAAAzI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP,EAEA7C,UAAU,KAAK,cAAc,iBAC5BlB,OAAA,CAAAE,SAAA;UAAA2F,QAAA,gBACE7F,OAAA,CAAChE,IAAI;YAACiL,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACf7F,OAAA,CAAC7C,SAAS;cACR8O,SAAS;cACTK,IAAI,EAAC,MAAM;cACX9E,KAAK,EAAC,aAAa;cACnB2E,KAAK,EAAE/K,QAAQ,CAACG,WAAY;cAC5B8F,QAAQ,EAAGC,CAAC,IAAKjG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAE+F,CAAC,CAACC,MAAM,CAAC4E;cAAM,CAAC,CAAE;cAC3EI,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA5I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP/D,OAAA,CAAChE,IAAI;YAACiL,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACf7F,OAAA,CAAC7C,SAAS;cACR8O,SAAS;cACTK,IAAI,EAAC,MAAM;cACX9E,KAAK,EAAC,WAAW;cACjB2E,KAAK,EAAE/K,QAAQ,CAACI,SAAU;cAC1B6F,QAAQ,EAAGC,CAAC,IAAKjG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAE8F,CAAC,CAACC,MAAM,CAAC4E;cAAM,CAAC,CAAE;cACzEI,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA5I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChB/D,OAAA,CAAClD,aAAa;MAAA+I,QAAA,gBACZ7F,OAAA,CAAC5D,MAAM;QAACkK,OAAO,EAAEd,iBAAkB;QAAAK,QAAA,EAAC;MAAO;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpD/D,OAAA,CAAC5D,MAAM;QACLkK,OAAO,EAAEf,oBAAqB;QAC9BW,OAAO,EAAC,WAAW;QACnBuG,QAAQ,EAAEjM,OAAQ;QAClB6F,SAAS,EAAE7F,OAAO,gBAAGR,OAAA,CAACzD,gBAAgB;UAACgK,IAAI,EAAE;QAAG;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG/D,OAAA,CAAC1B,cAAc;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAA8B,QAAA,EAExErF,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACE/D,OAAA,CAACnE,GAAG;IAAC6J,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAE,QAAA,gBAEhB7F,OAAA,CAACnE,GAAG;MAAC6J,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF7F,OAAA,CAACnE,GAAG;QAAC6J,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEI,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACzD7F,OAAA,CAACvD,UAAU;UAAC6J,OAAO,EAAEA,CAAA,KAAMjG,QAAQ,CAAC,CAAC,CAAC,CAAE;UAAC2D,KAAK,EAAC,SAAS;UAAA6B,QAAA,eACtD7F,OAAA,CAACtB,aAAa;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACb/D,OAAA,CAAClE,UAAU;UAACoK,OAAO,EAAC,IAAI;UAACwG,SAAS,EAAC,IAAI;UAAA7G,QAAA,EAAC;QAExC;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN/D,OAAA,CAACR,eAAe;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGLvD,OAAO,iBACNR,OAAA,CAACnE,GAAG;MAAC6J,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAE8E,EAAE,EAAE;MAAE,CAAE;MAAAhF,QAAA,eAC5D7F,OAAA,CAACzD,gBAAgB;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGD/D,OAAA,CAACnE,GAAG;MAAC6J,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAEjB7F,OAAA,CAAC5C,SAAS;QAACsK,eAAe;QAAChC,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC7F,OAAA,CAAC3C,gBAAgB;UAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,GAAG;YAAC6J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAACtC,cAAc;cAACgI,EAAE,EAAE;gBAAE+B,EAAE,EAAE,CAAC;gBAAEzD,KAAK,EAAE;cAAe;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxD/D,OAAA,CAAClE,UAAU;cAACoK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;UAAAuI,QAAA,EACdnE,WAAW,CAACE,QAAQ,gBACnB5B,OAAA,CAACnE,GAAG;YAAAgK,QAAA,gBACF7F,OAAA,CAACnE,GAAG;cAAC6J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACzF7F,OAAA,CAACxC,gBAAgB;gBACf2J,OAAO,eACLnH,OAAA,CAACzC,MAAM;kBACL6J,OAAO,EAAElF,UAAW;kBACpBmF,QAAQ,EAAGC,CAAC,IAAKnF,aAAa,CAACmF,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;kBACjDpD,KAAK,EAAC;gBAAS;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CACF;gBACDyD,KAAK,eACHxH,OAAA,CAACnE,GAAG;kBAAC6J,EAAE,EAAE;oBAAEI,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAH,QAAA,gBACjD7F,OAAA,CAACZ,aAAa;oBAACsG,EAAE,EAAE;sBAAE+B,EAAE,EAAE;oBAAE;kBAAE;oBAAA7D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,kBAElC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF/D,OAAA,CAACnE,GAAG;gBAAAgK,QAAA,gBACF7F,OAAA,CAAC5D,MAAM;kBACLiK,SAAS,eAAErG,OAAA,CAAC5B,YAAY;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,UAAU,EAAE,KAAK,CAAE;kBAC3DgC,OAAO,EAAC,UAAU;kBAClBK,IAAI,EAAC,OAAO;kBACZvC,KAAK,EAAC,SAAS;kBACf0B,EAAE,EAAE;oBAAE+B,EAAE,EAAE;kBAAE,CAAE;kBAAA5B,QAAA,EACf;gBAED;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC5D,MAAM;kBACLiK,SAAS,eAAErG,OAAA,CAAC5B,YAAY;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAE;kBAC7DgC,OAAO,EAAC,UAAU;kBAClBK,IAAI,EAAC,OAAO;kBACZvC,KAAK,EAAC,SAAS;kBAAA6B,QAAA,EAChB;gBAED;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLyC,oBAAoB,CAAC9E,WAAW,CAACE,QAAQ,CAAC;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,GACJvD,OAAO,gBACTR,OAAA,CAACnE,GAAG;YAAC6J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE6E,EAAE,EAAE;YAAE,CAAE;YAAAhF,QAAA,gBACxD7F,OAAA,CAACzD,gBAAgB;cAACgK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B/D,OAAA,CAAClE,UAAU;cAAC4J,EAAE,EAAE;gBAAEiH,EAAE,EAAE;cAAE,CAAE;cAAA9G,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN/D,OAAA,CAACnE,GAAG;YAAAgK,QAAA,gBACF7F,OAAA,CAAC1D,KAAK;cAAC4P,QAAQ,EAAC,OAAO;cAACxG,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA,CAAC5D,MAAM;cACL8J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAErG,OAAA,CAACxB,WAAW;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb7F,UAAU,CAAC,IAAI,CAAC;gBAChBhB,aAAa,CAAC6C,iBAAiB,CAAChC,UAAU,EAAE,OAAO,CAAC,CACjDsM,IAAI,CAAC9F,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP7C,QAAQ,EAAEkF,IAAI,CAACpE;kBACjB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC/B,KAAK,CAAC,iCAAiC,EAAE8B,GAAG,CAAC;gBACvD,CAAC,CAAC,CACDqK,OAAO,CAAC,MAAM;kBACbpM,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAoF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ/D,OAAA,CAAC5C,SAAS;QAACsK,eAAe;QAAChC,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC7F,OAAA,CAAC3C,gBAAgB;UAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,GAAG;YAAC6J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAAC9B,QAAQ;cAACwH,EAAE,EAAE;gBAAE+B,EAAE,EAAE,CAAC;gBAAEzD,KAAK,EAAE;cAAiB;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD/D,OAAA,CAAClE,UAAU;cAACoK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;UAAAuI,QAAA,EACdnE,WAAW,CAACG,GAAG,gBACd7B,OAAA,CAACnE,GAAG;YAAAgK,QAAA,gBACF7F,OAAA,CAACnE,GAAG;cAAC6J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC5D,MAAM;gBACLiK,SAAS,eAAErG,OAAA,CAAC5B,YAAY;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAE;gBACtDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC5D,MAAM;gBACLiK,SAAS,eAAErG,OAAA,CAAC5B,YAAY;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAE;gBACxDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL0C,eAAe,CAAC/E,WAAW,CAACG,GAAG,CAAC;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,GACJvD,OAAO,gBACTR,OAAA,CAACnE,GAAG;YAAC6J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE6E,EAAE,EAAE;YAAE,CAAE;YAAAhF,QAAA,gBACxD7F,OAAA,CAACzD,gBAAgB;cAACgK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B/D,OAAA,CAAClE,UAAU;cAAC4J,EAAE,EAAE;gBAAEiH,EAAE,EAAE;cAAE,CAAE;cAAA9G,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN/D,OAAA,CAACnE,GAAG;YAAAgK,QAAA,gBACF7F,OAAA,CAAC1D,KAAK;cAAC4P,QAAQ,EAAC,OAAO;cAACxG,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA,CAAC5D,MAAM;cACL8J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAErG,OAAA,CAACxB,WAAW;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb7F,UAAU,CAAC,IAAI,CAAC;gBAChBhB,aAAa,CAACmD,mBAAmB,CAACtC,UAAU,EAAE,OAAO,CAAC,CACnDsM,IAAI,CAAC9F,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP5C,GAAG,EAAEiF,IAAI,CAACpE;kBACZ,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,EAAE8B,GAAG,CAAC;gBAClD,CAAC,CAAC,CACDqK,OAAO,CAAC,MAAM;kBACbpM,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAoF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ/D,OAAA,CAAC5C,SAAS;QAACsK,eAAe;QAAChC,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC7F,OAAA,CAAC3C,gBAAgB;UAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,GAAG;YAAC6J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAAChB,aAAa;cAAC0G,EAAE,EAAE;gBAAE+B,EAAE,EAAE,CAAC;gBAAEzD,KAAK,EAAE;cAAe;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvD/D,OAAA,CAAClE,UAAU;cAACoK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAsB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;UAAAuI,QAAA,EACdnE,WAAW,CAACI,MAAM,gBACjB9B,OAAA,CAACnE,GAAG;YAAAgK,QAAA,gBACF7F,OAAA,CAACnE,GAAG;cAAC6J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC5D,MAAM;gBACLiK,SAAS,eAAErG,OAAA,CAAC5B,YAAY;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,QAAQ,EAAE,KAAK,CAAE;gBACzDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC5D,MAAM;gBACLiK,SAAS,eAAErG,OAAA,CAAC5B,YAAY;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,QAAQ,EAAE,OAAO,CAAE;gBAC3DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL2C,kBAAkB,CAAChF,WAAW,CAACI,MAAM,CAAC;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,GACJvD,OAAO,gBACTR,OAAA,CAACnE,GAAG;YAAC6J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE6E,EAAE,EAAE;YAAE,CAAE;YAAAhF,QAAA,gBACxD7F,OAAA,CAACzD,gBAAgB;cAACgK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B/D,OAAA,CAAClE,UAAU;cAAC4J,EAAE,EAAE;gBAAEiH,EAAE,EAAE;cAAE,CAAE;cAAA9G,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN/D,OAAA,CAACnE,GAAG;YAAAgK,QAAA,gBACF7F,OAAA,CAAC1D,KAAK;cAAC4P,QAAQ,EAAC,OAAO;cAACxG,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA,CAAC5D,MAAM;cACL8J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAErG,OAAA,CAACxB,WAAW;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb7F,UAAU,CAAC,IAAI,CAAC;gBAChBhB,aAAa,CAACqD,eAAe,CAACxC,UAAU,EAAE,OAAO,CAAC,CAC/CsM,IAAI,CAAC9F,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP3C,MAAM,EAAEgF,IAAI,CAACpE;kBACf,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC/B,KAAK,CAAC,+BAA+B,EAAE8B,GAAG,CAAC;gBACrD,CAAC,CAAC,CACDqK,OAAO,CAAC,MAAM;kBACbpM,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAoF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ/D,OAAA,CAAC5C,SAAS;QAACsK,eAAe;QAAChC,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC7F,OAAA,CAAC3C,gBAAgB;UAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,GAAG;YAAC6J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAACpC,YAAY;cAAC8H,EAAE,EAAE;gBAAE+B,EAAE,EAAE,CAAC;gBAAEzD,KAAK,EAAE;cAAa;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD/D,OAAA,CAAClE,UAAU;cAACoK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAqB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;UAAAuI,QAAA,EACdnE,WAAW,CAACK,SAAS,gBACpB/B,OAAA,CAACnE,GAAG;YAAAgK,QAAA,gBACF7F,OAAA,CAACnE,GAAG;cAAC6J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC5D,MAAM;gBACLiK,SAAS,eAAErG,OAAA,CAAC5B,YAAY;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,YAAY,EAAE,KAAK,CAAE;gBAC7DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC5D,MAAM;gBACLiK,SAAS,eAAErG,OAAA,CAAC5B,YAAY;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,YAAY,EAAE,OAAO,CAAE;gBAC/DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL8C,qBAAqB,CAACnF,WAAW,CAACK,SAAS,CAAC;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,GACJvD,OAAO,gBACTR,OAAA,CAACnE,GAAG;YAAC6J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE6E,EAAE,EAAE;YAAE,CAAE;YAAAhF,QAAA,gBACxD7F,OAAA,CAACzD,gBAAgB;cAACgK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B/D,OAAA,CAAClE,UAAU;cAAC4J,EAAE,EAAE;gBAAEiH,EAAE,EAAE;cAAE,CAAE;cAAA9G,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN/D,OAAA,CAACnE,GAAG;YAAAgK,QAAA,gBACF7F,OAAA,CAAC1D,KAAK;cAAC4P,QAAQ,EAAC,OAAO;cAACxG,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA,CAAC5D,MAAM;cACL8J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAErG,OAAA,CAACxB,WAAW;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb7F,UAAU,CAAC,IAAI,CAAC;gBAChBhB,aAAa,CAACuD,kBAAkB,CAAC1C,UAAU,EAAE,OAAO,CAAC,CAClDsM,IAAI,CAAC9F,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP1C,SAAS,EAAE+E,IAAI,CAACpE;kBAClB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC/B,KAAK,CAAC,mCAAmC,EAAE8B,GAAG,CAAC;gBACzD,CAAC,CAAC,CACDqK,OAAO,CAAC,MAAM;kBACbpM,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAoF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ/D,OAAA,CAACjE,KAAK;QAAC2J,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACzB7F,OAAA,CAAClE,UAAU;UAACoK,OAAO,EAAC,IAAI;UAAC2F,YAAY;UAAAhG,QAAA,EAAC;QAAe;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAClE/D,OAAA,CAAClE,UAAU;UAACoK,OAAO,EAAC,OAAO;UAAClC,KAAK,EAAC,gBAAgB;UAAC0B,EAAE,EAAE;YAAEO,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAElE;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb/D,OAAA,CAAChE,IAAI;UAAC+K,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAnB,QAAA,gBAEzB7F,OAAA,CAAChE,IAAI;YAACiL,IAAI;YAACC,EAAE,EAAE,EAAG;YAACgC,EAAE,EAAE,CAAE;YAAArD,QAAA,eACvB7F,OAAA,CAAC/D,IAAI;cAAA4J,QAAA,gBACH7F,OAAA,CAAC9D,WAAW;gBAAA2J,QAAA,gBACV7F,OAAA,CAAClE,UAAU;kBAACoK,OAAO,EAAC,IAAI;kBAAAL,QAAA,EAAC;gBAAuB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7D/D,OAAA,CAAClE,UAAU;kBAACoK,OAAO,EAAC,OAAO;kBAAClC,KAAK,EAAC,gBAAgB;kBAAC0B,EAAE,EAAE;oBAAEO,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAElE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACd/D,OAAA,CAAC7D,WAAW;gBAAA0J,QAAA,eACV7F,OAAA,CAAC5D,MAAM;kBACL6P,SAAS;kBACT/F,OAAO,EAAC,UAAU;kBAClBlC,KAAK,EAAC,MAAM;kBACZsC,OAAO,EAAEA,CAAA,KAAM;oBACbnF,aAAa,CAAC,kBAAkB,CAAC;oBACjCF,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBAAA4E,QAAA,EACH;gBAED;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGP/D,OAAA,CAAChE,IAAI;YAACiL,IAAI;YAACC,EAAE,EAAE,EAAG;YAACgC,EAAE,EAAE,CAAE;YAAArD,QAAA,eACvB7F,OAAA,CAAC/D,IAAI;cAAA4J,QAAA,gBACH7F,OAAA,CAAC9D,WAAW;gBAAA2J,QAAA,gBACV7F,OAAA,CAAClE,UAAU;kBAACoK,OAAO,EAAC,IAAI;kBAAAL,QAAA,EAAC;gBAAuB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7D/D,OAAA,CAAClE,UAAU;kBAACoK,OAAO,EAAC,OAAO;kBAAClC,KAAK,EAAC,gBAAgB;kBAAC0B,EAAE,EAAE;oBAAEO,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAElE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACd/D,OAAA,CAAC7D,WAAW;gBAAA0J,QAAA,eACV7F,OAAA,CAAC5D,MAAM;kBACL6P,SAAS;kBACT/F,OAAO,EAAC,UAAU;kBAClBlC,KAAK,EAAC,SAAS;kBACfsC,OAAO,EAAEA,CAAA,KAAM;oBACbnF,aAAa,CAAC,cAAc,CAAC;oBAC7B;oBACA,MAAM6D,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;oBACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;oBAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;oBAExC/D,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXG,WAAW,EAAE2D,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;sBAClD9D,SAAS,EAAEwD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC7C,CAAC,CAAC;oBACFrE,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBAAA4E,QAAA,EACH;gBAED;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGPrC,WAAW,CAACM,eAAe,iBAC1BhC,OAAA,CAAC5C,SAAS;QAACsK,eAAe;QAAChC,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAEL,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBAC9C7F,OAAA,CAAC3C,gBAAgB;UAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,GAAG;YAAC6J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAAClB,SAAS;cAAC4G,EAAE,EAAE;gBAAE+B,EAAE,EAAE,CAAC;gBAAEzD,KAAK,EAAE;cAAY;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD/D,OAAA,CAAClE,UAAU;cAACoK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;UAAAuI,QAAA,eACf7F,OAAA,CAACnE,GAAG;YAAAgK,QAAA,gBACF7F,OAAA,CAACnE,GAAG;cAAC6J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC5D,MAAM;gBACLiK,SAAS,eAAErG,OAAA,CAAC5B,YAAY;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,kBAAkB,EAAE,KAAK,CAAE;gBACnEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC5D,MAAM;gBACLiK,SAAS,eAAErG,OAAA,CAAC5B,YAAY;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,kBAAkB,EAAE,OAAO,CAAE;gBACrEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL4C,2BAA2B,CAACjF,WAAW,CAACM,eAAe,CAAC;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACZ,EAEArC,WAAW,CAACO,WAAW,iBACtBjC,OAAA,CAAC5C,SAAS;QAACsK,eAAe;QAAChC,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAEL,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBAC9C7F,OAAA,CAAC3C,gBAAgB;UAACsK,UAAU,eAAE3H,OAAA,CAACd,cAAc;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,GAAG;YAAC6J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAAChC,YAAY;cAAC0H,EAAE,EAAE;gBAAE+B,EAAE,EAAE,CAAC;gBAAEzD,KAAK,EAAE;cAAe;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtD/D,OAAA,CAAClE,UAAU;cAACoK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC1C,gBAAgB;UAAAuI,QAAA,eACf7F,OAAA,CAACnE,GAAG;YAAAgK,QAAA,gBACF7F,OAAA,CAACnE,GAAG;cAAC6J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC5D,MAAM;gBACLiK,SAAS,eAAErG,OAAA,CAAC5B,YAAY;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,cAAc,EAAE,KAAK,CAAE;gBAC/DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC5D,MAAM;gBACLiK,SAAS,eAAErG,OAAA,CAAC5B,YAAY;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAE;gBACjEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL6C,uBAAuB,CAAClF,WAAW,CAACO,WAAW,CAAC;UAAA;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACZ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL+H,YAAY,CAAC,CAAC;EAAA;IAAAlI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC3D,EAAA,CAjtCID,iBAAiB;EAAA,QACJd,WAAW,EACLC,SAAS,EACfC,OAAO;AAAA;AAAAuN,EAAA,GAHpB3M,iBAAiB;AAmtCvB,eAAeA,iBAAiB;AAAC,IAAA2M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}