{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tabs, Tab } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon, Home as HomeIcon, ViewList as ViewListIcon, ViewModule as ViewModuleIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  const {\n    isImpersonating\n  } = useAuth();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'card'\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 20000);\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 20000);\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Torna al menu amministratore (solo per admin che impersonano un utente)\n  const handleBackToAdmin = () => {\n    // Naviga direttamente al menu amministratore\n    navigate('/dashboard/admin');\n  };\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = cavi => {\n    if (cavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Nessun cavo trovato in questa categoria.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this);\n    }\n    if (viewMode === 'table') {\n      return /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          mt: 2,\n          overflowX: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Utility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"N.Cond\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Sezione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicaz.Part.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicaz.Arr.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri T.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => {\n              // Formatta i valori per la visualizzazione come nella CLI\n              const id_cavo = String(cavo.id_cavo).replace('$', '');\n              const utility = cavo.utility || '-';\n              const tipologia = cavo.tipologia || '-';\n\n              // Gestisci n_conduttori come stringa o numero\n              let n_conduttori = '-';\n              try {\n                const n_cond_val = parseInt(cavo.n_conduttori, 10) || 0;\n                n_conduttori = n_cond_val > 0 ? String(n_cond_val) : '-';\n              } catch (e) {\n                n_conduttori = cavo.n_conduttori || '-';\n              }\n\n              // Gestisci sezione come stringa\n              let sezione = '-';\n              const sezione_val = cavo.sezione;\n              if (typeof sezione_val === 'number' && sezione_val === 0) {\n                sezione = '-';\n              } else {\n                sezione = sezione_val ? String(sezione_val) : '-';\n              }\n              const ubicazione_partenza = cavo.ubicazione_partenza || '-';\n              const ubicazione_arrivo = cavo.ubicazione_arrivo || '-';\n              const metri_teorici = cavo.metri_teorici ? `${parseFloat(cavo.metri_teorici).toFixed(2)}` : '-';\n              const stato = cavo.stato_installazione || '-';\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: utility\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: n_conduttori\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: ubicazione_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: ubicazione_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: metri_teorici\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: stato\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this);\n    } else {\n      // Visualizzazione a schede (card)\n      return /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Sistema: \", cavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Tipologia: \", cavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Partenza: \", cavo.ubicazione_partenza || 'N/A', \" - \", cavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Arrivo: \", cavo.ubicazione_arrivo || 'N/A', \" - \", cavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Metratura reale: \", cavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Stato: \", cavo.stato_installazione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this)\n        }, cavo.id_cavo, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this);\n    }\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = mode => {\n    setViewMode(mode);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToCantieri,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          children: \"Visualizza Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this), isImpersonating && /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 24\n        }, this),\n        onClick: handleBackToAdmin,\n        children: \"Torna al Menu Admin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Cantiere: \", cantiereName, \" (ID: \", cantiereId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              border: '1px solid #ddd',\n              borderRadius: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              color: viewMode === 'table' ? 'primary' : 'default',\n              onClick: () => handleViewModeChange('table'),\n              title: \"Vista tabellare\",\n              children: /*#__PURE__*/_jsxDEV(ViewListIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              color: viewMode === 'card' ? 'primary' : 'default',\n              onClick: () => handleViewModeChange('card'),\n              title: \"Vista a schede\",\n              children: /*#__PURE__*/_jsxDEV(ViewModuleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 26\n            }, this),\n            onClick: handleBackToCantieri,\n            children: \"Torna ai Cantieri\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 26\n          }, this),\n          onClick: handleBackToCantieri,\n          children: \"Torna al Cantiere\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"primary\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Cavi Attivi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this), renderCaviTable(caviAttivi)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Cavi Spare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this), renderCaviTable(caviSpare)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 312,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"YaYnEUYMaZHKZ1xgJjnaLMvx5Xs=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Tabs", "Tab", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Home", "HomeIcon", "ViewList", "ViewListIcon", "ViewModule", "ViewModuleIcon", "useNavigate", "useAuth", "caviService", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "isImpersonating", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "viewMode", "setViewMode", "fetchData", "console", "log", "token", "localStorage", "getItem", "selectedCantiereId", "selectedCantiereName", "cantiereIdNum", "parseInt", "isNaN", "timeoutPromise", "Promise", "_", "reject", "setTimeout", "Error", "caviPromise", "get<PERSON><PERSON>", "attivi", "race", "caviError", "message", "status", "data", "stack", "warn", "sparePromise", "spare", "spareError", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "response", "errorMessage", "includes", "detail", "code", "handleBackToCantieri", "handleBackToAdmin", "renderCaviTable", "cavi", "length", "severity", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "sx", "mt", "overflowX", "size", "map", "cavo", "id_cavo", "String", "replace", "utility", "tipologia", "n_conduttori", "n_cond_val", "e", "sezione", "sezione_val", "ubicazione_partenza", "ubicazione_arrivo", "metri_te<PERSON>ci", "parseFloat", "toFixed", "stato", "stato_installazione", "container", "spacing", "item", "xs", "sm", "md", "variant", "color", "sistema", "utenza_partenza", "utenza_arrivo", "metratura_reale", "handleViewModeChange", "mode", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "window", "location", "reload", "ml", "title", "startIcon", "p", "gap", "border", "borderRadius", "flexDirection", "gutterBottom", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Tabs,\n  Tab\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Home as HomeIcon,\n  ViewList as ViewListIcon,\n  ViewModule as ViewModuleIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport caviService from '../../services/caviService';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating } = useAuth();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'card'\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato:', { selectedCantiereId, selectedCantiereName });\n\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 20000);\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 20000);\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Torna al menu amministratore (solo per admin che impersonano un utente)\n  const handleBackToAdmin = () => {\n    // Naviga direttamente al menu amministratore\n    navigate('/dashboard/admin');\n  };\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = (cavi) => {\n    if (cavi.length === 0) {\n      return (\n        <Alert severity=\"info\">Nessun cavo trovato in questa categoria.</Alert>\n      );\n    }\n\n    if (viewMode === 'table') {\n      return (\n        <TableContainer component={Paper} sx={{ mt: 2, overflowX: 'auto' }}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Utility</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>N.Cond</TableCell>\n                <TableCell>Sezione</TableCell>\n                <TableCell>Ubicaz.Part.</TableCell>\n                <TableCell>Ubicaz.Arr.</TableCell>\n                <TableCell>Metri T.</TableCell>\n                <TableCell>Stato</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {cavi.map((cavo) => {\n                // Formatta i valori per la visualizzazione come nella CLI\n                const id_cavo = String(cavo.id_cavo).replace('$', '');\n                const utility = cavo.utility || '-';\n                const tipologia = cavo.tipologia || '-';\n\n                // Gestisci n_conduttori come stringa o numero\n                let n_conduttori = '-';\n                try {\n                  const n_cond_val = parseInt(cavo.n_conduttori, 10) || 0;\n                  n_conduttori = n_cond_val > 0 ? String(n_cond_val) : '-';\n                } catch (e) {\n                  n_conduttori = cavo.n_conduttori || '-';\n                }\n\n                // Gestisci sezione come stringa\n                let sezione = '-';\n                const sezione_val = cavo.sezione;\n                if (typeof sezione_val === 'number' && sezione_val === 0) {\n                  sezione = '-';\n                } else {\n                  sezione = sezione_val ? String(sezione_val) : '-';\n                }\n\n                const ubicazione_partenza = cavo.ubicazione_partenza || '-';\n                const ubicazione_arrivo = cavo.ubicazione_arrivo || '-';\n                const metri_teorici = cavo.metri_teorici ? `${parseFloat(cavo.metri_teorici).toFixed(2)}` : '-';\n                const stato = cavo.stato_installazione || '-';\n\n                return (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>{id_cavo}</TableCell>\n                    <TableCell>{utility}</TableCell>\n                    <TableCell>{tipologia}</TableCell>\n                    <TableCell>{n_conduttori}</TableCell>\n                    <TableCell>{sezione}</TableCell>\n                    <TableCell>{ubicazione_partenza}</TableCell>\n                    <TableCell>{ubicazione_arrivo}</TableCell>\n                    <TableCell>{metri_teorici}</TableCell>\n                    <TableCell>{stato}</TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      );\n    } else {\n      // Visualizzazione a schede (card)\n      return (\n        <Grid container spacing={2}>\n          {cavi.map((cavo) => (\n            <Grid item xs={12} sm={6} md={4} key={cavo.id_cavo}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\" component=\"div\">\n                    {cavo.id_cavo}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Sistema: {cavo.sistema || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Tipologia: {cavo.tipologia || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Partenza: {cavo.ubicazione_partenza || 'N/A'} - {cavo.utenza_partenza || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Arrivo: {cavo.ubicazione_arrivo || 'N/A'} - {cavo.utenza_arrivo || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Metri teorici: {cavo.metri_teorici || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Metratura reale: {cavo.metratura_reale || '0'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Stato: {cavo.stato_installazione || 'N/A'}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      );\n    }\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = (mode) => {\n    setViewMode(mode);\n  };\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Visualizza Cavi\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        {isImpersonating && (\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<HomeIcon />}\n            onClick={handleBackToAdmin}\n          >\n            Torna al Menu Admin\n          </Button>\n        )}\n      </Box>\n\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h6\">\n            Cantiere: {cantiereName} (ID: {cantiereId})\n          </Typography>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n            <Box sx={{ display: 'flex', border: '1px solid #ddd', borderRadius: 1 }}>\n              <IconButton\n                color={viewMode === 'table' ? 'primary' : 'default'}\n                onClick={() => handleViewModeChange('table')}\n                title=\"Vista tabellare\"\n              >\n                <ViewListIcon />\n              </IconButton>\n              <IconButton\n                color={viewMode === 'card' ? 'primary' : 'default'}\n                onClick={() => handleViewModeChange('card')}\n                title=\"Vista a schede\"\n              >\n                <ViewModuleIcon />\n              </IconButton>\n            </Box>\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              startIcon={<ArrowBackIcon />}\n              onClick={handleBackToCantieri}\n            >\n              Torna ai Cantieri\n            </Button>\n          </Box>\n        </Box>\n      </Paper>\n\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <Typography>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              startIcon={<ArrowBackIcon />}\n              onClick={handleBackToCantieri}\n            >\n              Torna al Cantiere\n            </Button>\n            <Button\n              variant=\"outlined\"\n              color=\"primary\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h5\" gutterBottom>\n              Cavi Attivi\n            </Typography>\n            {renderCaviTable(caviAttivi)}\n          </Box>\n\n          <Box sx={{ mt: 4 }}>\n            <Typography variant=\"h5\" gutterBottom>\n              Cavi Spare\n            </Typography>\n            {renderCaviTable(caviSpare)}\n          </Box>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,GAAG,QACE,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAgB,CAAC,GAAGN,OAAO,CAAC,CAAC;EACrC,MAAMO,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgD,KAAK,EAAEC,QAAQ,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAACC,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACVN,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMW,kBAAkB,GAAGF,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACrE,MAAME,oBAAoB,GAAGH,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEzEJ,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;UAAEI,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QAElF,IAAI,CAACD,kBAAkB,EAAE;UACvBT,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMa,aAAa,GAAGC,QAAQ,CAACH,kBAAkB,EAAE,EAAE,CAAC;QACtDL,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEM,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxBX,QAAQ,CAAC,2BAA2BS,kBAAkB,mCAAmC,CAAC;UAC1FX,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAACqB,aAAa,CAAC;QAC5BnB,eAAe,CAACkB,oBAAoB,IAAI,YAAYC,aAAa,EAAE,CAAC;;QAEpE;QACAP,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEM,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC;UAC9F,CAAC,CAAC;;UAEF;UACA,MAAMC,WAAW,GAAGtC,WAAW,CAACuC,OAAO,CAACV,aAAa,EAAE,CAAC,CAAC;UACzD,MAAMW,MAAM,GAAG,MAAMP,OAAO,CAACQ,IAAI,CAAC,CAACH,WAAW,EAAEN,cAAc,CAAC,CAAC;UAEhEV,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEiB,MAAM,CAAC;UAC5C5B,aAAa,CAAC4B,MAAM,IAAI,EAAE,CAAC;QAC7B,CAAC,CAAC,OAAOE,SAAS,EAAE;UAClBpB,OAAO,CAACL,KAAK,CAAC,yCAAyC,EAAEyB,SAAS,CAAC;UACnEpB,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAE;YAC5C0B,OAAO,EAAED,SAAS,CAACC,OAAO;YAC1BC,MAAM,EAAEF,SAAS,CAACE,MAAM;YACxBC,IAAI,EAAEH,SAAS,CAACG,IAAI;YACpBC,KAAK,EAAEJ,SAAS,CAACI;UACnB,CAAC,CAAC;;UAEF;UACAlC,aAAa,CAAC,EAAE,CAAC;UACjBU,OAAO,CAACyB,IAAI,CAAC,sDAAsD,CAAC;QACtE;;QAEA;QACAzB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEM,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC;UAC7F,CAAC,CAAC;;UAEF;UACA,MAAMW,YAAY,GAAGhD,WAAW,CAACuC,OAAO,CAACV,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAMoB,KAAK,GAAG,MAAMhB,OAAO,CAACQ,IAAI,CAAC,CAACO,YAAY,EAAEhB,cAAc,CAAC,CAAC;UAEhEV,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE0B,KAAK,CAAC;UAC1CnC,YAAY,CAACmC,KAAK,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,OAAOC,UAAU,EAAE;UACnB5B,OAAO,CAACL,KAAK,CAAC,wCAAwC,EAAEiC,UAAU,CAAC;UACnE5B,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAE;YAC3C0B,OAAO,EAAEO,UAAU,CAACP,OAAO;YAC3BC,MAAM,EAAEM,UAAU,CAACN,MAAM;YACzBC,IAAI,EAAEK,UAAU,CAACL,IAAI;YACrBC,KAAK,EAAEI,UAAU,CAACJ;UACpB,CAAC,CAAC;;UAEF;UACAhC,YAAY,CAAC,EAAE,CAAC;QAClB;;QAEA;QACAE,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAOmC,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZnC,OAAO,CAACL,KAAK,CAAC,kCAAkC,EAAEkC,GAAG,CAAC;QACtD7B,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAE;UACzC0B,OAAO,EAAEQ,GAAG,CAACR,OAAO;UACpBC,MAAM,EAAEO,GAAG,CAACP,MAAM,MAAAQ,aAAA,GAAID,GAAG,CAACO,QAAQ,cAAAN,aAAA,uBAAZA,aAAA,CAAcR,MAAM;UAC1CC,IAAI,EAAEM,GAAG,CAACN,IAAI,MAAAQ,cAAA,GAAIF,GAAG,CAACO,QAAQ,cAAAL,cAAA,uBAAZA,cAAA,CAAcR,IAAI;UACpCC,KAAK,EAAEK,GAAG,CAACL;QACb,CAAC,CAAC;;QAEF;QACA,IAAIa,YAAY,GAAG,oBAAoB;QAEvC,IAAIR,GAAG,CAACR,OAAO,IAAIQ,GAAG,CAACR,OAAO,CAACiB,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjED,YAAY,GAAGR,GAAG,CAACR,OAAO;QAC5B,CAAC,MAAM,IAAIQ,GAAG,CAACP,MAAM,KAAK,GAAG,IAAIO,GAAG,CAACP,MAAM,KAAK,GAAG,IACzC,EAAAU,cAAA,GAAAH,GAAG,CAACO,QAAQ,cAAAJ,cAAA,uBAAZA,cAAA,CAAcV,MAAM,MAAK,GAAG,IAAI,EAAAW,cAAA,GAAAJ,GAAG,CAACO,QAAQ,cAAAH,cAAA,uBAAZA,cAAA,CAAcX,MAAM,MAAK,GAAG,EAAE;UACtEe,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAH,cAAA,GAAIL,GAAG,CAACO,QAAQ,cAAAF,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcX,IAAI,cAAAY,mBAAA,eAAlBA,mBAAA,CAAoBI,MAAM,EAAE;UACrC;UACAF,YAAY,GAAG,eAAeR,GAAG,CAACO,QAAQ,CAACb,IAAI,CAACgB,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIV,GAAG,CAACW,IAAI,KAAK,aAAa,EAAE;UACrC;UACAH,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIR,GAAG,CAACR,OAAO,EAAE;UACtBgB,YAAY,GAAGR,GAAG,CAACR,OAAO;QAC5B;QAEAzB,QAAQ,CAAC,gCAAgCyC,YAAY,sBAAsB,CAAC;;QAE5E;QACA/C,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDK,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0C,oBAAoB,GAAGA,CAAA,KAAM;IACjCzD,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAED;EACA,MAAM0D,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACA1D,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;;EAED;EACA,MAAM2D,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;MACrB,oBACEjE,OAAA,CAACxB,KAAK;QAAC0F,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAwC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAE3E;IAEA,IAAItD,QAAQ,KAAK,OAAO,EAAE;MACxB,oBACEjB,OAAA,CAACnB,cAAc;QAAC2F,SAAS,EAAErG,KAAM;QAACsG,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAAR,QAAA,eACjEnE,OAAA,CAACtB,KAAK;UAACkG,IAAI,EAAC,OAAO;UAAAT,QAAA,gBACjBnE,OAAA,CAAClB,SAAS;YAAAqF,QAAA,eACRnE,OAAA,CAACjB,QAAQ;cAAAoF,QAAA,gBACPnE,OAAA,CAACpB,SAAS;gBAAAuF,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvE,OAAA,CAACpB,SAAS;gBAAAuF,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvE,OAAA,CAACpB,SAAS;gBAAAuF,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCvE,OAAA,CAACpB,SAAS;gBAAAuF,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BvE,OAAA,CAACpB,SAAS;gBAAAuF,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvE,OAAA,CAACpB,SAAS;gBAAAuF,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnCvE,OAAA,CAACpB,SAAS;gBAAAuF,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCvE,OAAA,CAACpB,SAAS;gBAAAuF,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BvE,OAAA,CAACpB,SAAS;gBAAAuF,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZvE,OAAA,CAACrB,SAAS;YAAAwF,QAAA,EACPH,IAAI,CAACa,GAAG,CAAEC,IAAI,IAAK;cAClB;cACA,MAAMC,OAAO,GAAGC,MAAM,CAACF,IAAI,CAACC,OAAO,CAAC,CAACE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;cACrD,MAAMC,OAAO,GAAGJ,IAAI,CAACI,OAAO,IAAI,GAAG;cACnC,MAAMC,SAAS,GAAGL,IAAI,CAACK,SAAS,IAAI,GAAG;;cAEvC;cACA,IAAIC,YAAY,GAAG,GAAG;cACtB,IAAI;gBACF,MAAMC,UAAU,GAAGzD,QAAQ,CAACkD,IAAI,CAACM,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC;gBACvDA,YAAY,GAAGC,UAAU,GAAG,CAAC,GAAGL,MAAM,CAACK,UAAU,CAAC,GAAG,GAAG;cAC1D,CAAC,CAAC,OAAOC,CAAC,EAAE;gBACVF,YAAY,GAAGN,IAAI,CAACM,YAAY,IAAI,GAAG;cACzC;;cAEA;cACA,IAAIG,OAAO,GAAG,GAAG;cACjB,MAAMC,WAAW,GAAGV,IAAI,CAACS,OAAO;cAChC,IAAI,OAAOC,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,CAAC,EAAE;gBACxDD,OAAO,GAAG,GAAG;cACf,CAAC,MAAM;gBACLA,OAAO,GAAGC,WAAW,GAAGR,MAAM,CAACQ,WAAW,CAAC,GAAG,GAAG;cACnD;cAEA,MAAMC,mBAAmB,GAAGX,IAAI,CAACW,mBAAmB,IAAI,GAAG;cAC3D,MAAMC,iBAAiB,GAAGZ,IAAI,CAACY,iBAAiB,IAAI,GAAG;cACvD,MAAMC,aAAa,GAAGb,IAAI,CAACa,aAAa,GAAG,GAAGC,UAAU,CAACd,IAAI,CAACa,aAAa,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG;cAC/F,MAAMC,KAAK,GAAGhB,IAAI,CAACiB,mBAAmB,IAAI,GAAG;cAE7C,oBACE/F,OAAA,CAACjB,QAAQ;gBAAAoF,QAAA,gBACPnE,OAAA,CAACpB,SAAS;kBAAAuF,QAAA,EAAEY;gBAAO;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCvE,OAAA,CAACpB,SAAS;kBAAAuF,QAAA,EAAEe;gBAAO;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCvE,OAAA,CAACpB,SAAS;kBAAAuF,QAAA,EAAEgB;gBAAS;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClCvE,OAAA,CAACpB,SAAS;kBAAAuF,QAAA,EAAEiB;gBAAY;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCvE,OAAA,CAACpB,SAAS;kBAAAuF,QAAA,EAAEoB;gBAAO;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCvE,OAAA,CAACpB,SAAS;kBAAAuF,QAAA,EAAEsB;gBAAmB;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5CvE,OAAA,CAACpB,SAAS;kBAAAuF,QAAA,EAAEuB;gBAAiB;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1CvE,OAAA,CAACpB,SAAS;kBAAAuF,QAAA,EAAEwB;gBAAa;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtCvE,OAAA,CAACpB,SAAS;kBAAAuF,QAAA,EAAE2B;gBAAK;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA,GATjBO,IAAI,CAACC,OAAO;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUjB,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAErB,CAAC,MAAM;MACL;MACA,oBACEvE,OAAA,CAAC3B,IAAI;QAAC2H,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA9B,QAAA,EACxBH,IAAI,CAACa,GAAG,CAAEC,IAAI,iBACb9E,OAAA,CAAC3B,IAAI;UAAC6H,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlC,QAAA,eAC9BnE,OAAA,CAAC1B,IAAI;YAAA6F,QAAA,eACHnE,OAAA,CAACzB,WAAW;cAAA4F,QAAA,gBACVnE,OAAA,CAAC9B,UAAU;gBAACoI,OAAO,EAAC,IAAI;gBAAC9B,SAAS,EAAC,KAAK;gBAAAL,QAAA,EACrCW,IAAI,CAACC;cAAO;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACbvE,OAAA,CAAC9B,UAAU;gBAACoI,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAApC,QAAA,GAAC,WACxC,EAACW,IAAI,CAAC0B,OAAO,IAAI,KAAK;cAAA;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACbvE,OAAA,CAAC9B,UAAU;gBAACoI,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAApC,QAAA,GAAC,aACtC,EAACW,IAAI,CAACK,SAAS,IAAI,KAAK;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACbvE,OAAA,CAAC9B,UAAU;gBAACoI,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAApC,QAAA,GAAC,YACvC,EAACW,IAAI,CAACW,mBAAmB,IAAI,KAAK,EAAC,KAAG,EAACX,IAAI,CAAC2B,eAAe,IAAI,KAAK;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACbvE,OAAA,CAAC9B,UAAU;gBAACoI,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAApC,QAAA,GAAC,UACzC,EAACW,IAAI,CAACY,iBAAiB,IAAI,KAAK,EAAC,KAAG,EAACZ,IAAI,CAAC4B,aAAa,IAAI,KAAK;cAAA;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACbvE,OAAA,CAAC9B,UAAU;gBAACoI,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAApC,QAAA,GAAC,iBAClC,EAACW,IAAI,CAACa,aAAa,IAAI,KAAK;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACbvE,OAAA,CAAC9B,UAAU;gBAACoI,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAApC,QAAA,GAAC,mBAChC,EAACW,IAAI,CAAC6B,eAAe,IAAI,GAAG;cAAA;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACbvE,OAAA,CAAC9B,UAAU;gBAACoI,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAApC,QAAA,GAAC,SAC1C,EAACW,IAAI,CAACiB,mBAAmB,IAAI,KAAK;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GA5B6BO,IAAI,CAACC,OAAO;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6B5C,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAEX;EACF,CAAC;;EAED;EACA,MAAMqC,oBAAoB,GAAIC,IAAI,IAAK;IACrC3F,WAAW,CAAC2F,IAAI,CAAC;EACnB,CAAC;EAED,oBACE7G,OAAA,CAAC/B,GAAG;IAAAkG,QAAA,gBACFnE,OAAA,CAAC/B,GAAG;MAACwG,EAAE,EAAE;QAAEqC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAA9C,QAAA,gBACzFnE,OAAA,CAAC/B,GAAG;QAACwG,EAAE,EAAE;UAAEsC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAA7C,QAAA,gBACjDnE,OAAA,CAACvB,UAAU;UAACyI,OAAO,EAAErD,oBAAqB;UAACY,EAAE,EAAE;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAhD,QAAA,eACvDnE,OAAA,CAACb,aAAa;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACbvE,OAAA,CAAC9B,UAAU;UAACoI,OAAO,EAAC,IAAI;UAAAnC,QAAA,EAAC;QAEzB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvE,OAAA,CAACvB,UAAU;UACTyI,OAAO,EAAEA,CAAA,KAAME,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxC7C,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UACdhB,KAAK,EAAC,SAAS;UACfiB,KAAK,EAAC,oBAAoB;UAAArD,QAAA,eAE1BnE,OAAA,CAACX,WAAW;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EACLpE,eAAe,iBACdH,OAAA,CAAC5B,MAAM;QACLkI,OAAO,EAAC,WAAW;QACnBC,KAAK,EAAC,SAAS;QACfkB,SAAS,eAAEzH,OAAA,CAACT,QAAQ;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxB2C,OAAO,EAAEpD,iBAAkB;QAAAK,QAAA,EAC5B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENvE,OAAA,CAAC7B,KAAK;MAACsG,EAAE,EAAE;QAAEqC,EAAE,EAAE,CAAC;QAAEY,CAAC,EAAE;MAAE,CAAE;MAAAvD,QAAA,eACzBnE,OAAA,CAAC/B,GAAG;QAACwG,EAAE,EAAE;UAAEsC,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,eAAe;UAAED,UAAU,EAAE;QAAS,CAAE;QAAA7C,QAAA,gBAClFnE,OAAA,CAAC9B,UAAU;UAACoI,OAAO,EAAC,IAAI;UAAAnC,QAAA,GAAC,YACb,EAAC5D,YAAY,EAAC,QAAM,EAACF,UAAU,EAAC,GAC5C;QAAA;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvE,OAAA,CAAC/B,GAAG;UAACwG,EAAE,EAAE;YAAEsC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEW,GAAG,EAAE;UAAE,CAAE;UAAAxD,QAAA,gBACzDnE,OAAA,CAAC/B,GAAG;YAACwG,EAAE,EAAE;cAAEsC,OAAO,EAAE,MAAM;cAAEa,MAAM,EAAE,gBAAgB;cAAEC,YAAY,EAAE;YAAE,CAAE;YAAA1D,QAAA,gBACtEnE,OAAA,CAACvB,UAAU;cACT8H,KAAK,EAAEtF,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,SAAU;cACpDiG,OAAO,EAAEA,CAAA,KAAMN,oBAAoB,CAAC,OAAO,CAAE;cAC7CY,KAAK,EAAC,iBAAiB;cAAArD,QAAA,eAEvBnE,OAAA,CAACP,YAAY;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACbvE,OAAA,CAACvB,UAAU;cACT8H,KAAK,EAAEtF,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG,SAAU;cACnDiG,OAAO,EAAEA,CAAA,KAAMN,oBAAoB,CAAC,MAAM,CAAE;cAC5CY,KAAK,EAAC,gBAAgB;cAAArD,QAAA,eAEtBnE,OAAA,CAACL,cAAc;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNvE,OAAA,CAAC5B,MAAM;YACLkI,OAAO,EAAC,WAAW;YACnBC,KAAK,EAAC,SAAS;YACfkB,SAAS,eAAEzH,OAAA,CAACb,aAAa;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7B2C,OAAO,EAAErD,oBAAqB;YAAAM,QAAA,EAC/B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEP1D,OAAO,gBACNb,OAAA,CAAC/B,GAAG;MAACwG,EAAE,EAAE;QAAEsC,OAAO,EAAE,MAAM;QAAEe,aAAa,EAAE,QAAQ;QAAEd,UAAU,EAAE,QAAQ;QAAEtC,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,gBACjFnE,OAAA,CAAC9B,UAAU;QAAAiG,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC5CvE,OAAA,CAAC5B,MAAM;QACLkI,OAAO,EAAC,UAAU;QAClBC,KAAK,EAAC,SAAS;QACfW,OAAO,EAAEA,CAAA,KAAME,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxC7C,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAP,QAAA,EACf;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJxD,KAAK,gBACPf,OAAA,CAAC/B,GAAG;MAAAkG,QAAA,gBACFnE,OAAA,CAACxB,KAAK;QAAC0F,QAAQ,EAAC,OAAO;QAACO,EAAE,EAAE;UAAEqC,EAAE,EAAE;QAAE,CAAE;QAAA3C,QAAA,EAAEpD;MAAK;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACtDvE,OAAA,CAAC/B,GAAG;QAACwG,EAAE,EAAE;UAAEsC,OAAO,EAAE,MAAM;UAAEY,GAAG,EAAE;QAAE,CAAE;QAAAxD,QAAA,gBACnCnE,OAAA,CAAC5B,MAAM;UACLkI,OAAO,EAAC,WAAW;UACnBmB,SAAS,eAAEzH,OAAA,CAACb,aAAa;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7B2C,OAAO,EAAErD,oBAAqB;UAAAM,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvE,OAAA,CAAC5B,MAAM;UACLkI,OAAO,EAAC,UAAU;UAClBC,KAAK,EAAC,SAAS;UACfW,OAAO,EAAEA,CAAA,KAAME,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAnD,QAAA,EACzC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENvE,OAAA,CAAC/B,GAAG;MAAAkG,QAAA,gBACFnE,OAAA,CAAC/B,GAAG;QAACwG,EAAE,EAAE;UAAEqC,EAAE,EAAE;QAAE,CAAE;QAAA3C,QAAA,gBACjBnE,OAAA,CAAC9B,UAAU;UAACoI,OAAO,EAAC,IAAI;UAACyB,YAAY;UAAA5D,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZR,eAAe,CAACtD,UAAU,CAAC;MAAA;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAENvE,OAAA,CAAC/B,GAAG;QAACwG,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACjBnE,OAAA,CAAC9B,UAAU;UAACoI,OAAO,EAAC,IAAI;UAACyB,YAAY;UAAA5D,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZR,eAAe,CAACpD,SAAS,CAAC;MAAA;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrE,EAAA,CA3YID,kBAAkB;EAAA,QACMJ,OAAO,EAClBD,WAAW;AAAA;AAAAoI,EAAA,GAFxB/H,kBAAkB;AA6YxB,eAAeA,kBAAkB;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}