{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * @name closestIndexTo\n * @category Common Helpers\n * @summary Return an index of the closest date from the array comparing to the given date.\n *\n * @description\n * Return an index of the closest date from the array comparing to the given date.\n *\n * @param dateToCompare - The date to compare with\n * @param dates - The array to search\n *\n * @returns An index of the date closest to the given date or undefined if no valid value is given\n *\n * @example\n * // Which date is closer to 6 September 2015?\n * const dateToCompare = new Date(2015, 8, 6)\n * const datesArray = [\n *   new Date(2015, 0, 1),\n *   new Date(2016, 0, 1),\n *   new Date(2017, 0, 1)\n * ]\n * const result = closestIndexTo(dateToCompare, datesArray)\n * //=> 1\n */\nexport function closestIndexTo(dateToCompare, dates) {\n  // [TODO] It would be better to return -1 here rather than undefined, as this\n  // is how JS behaves, but it would be a breaking change, so we need\n  // to consider it for v4.\n  const timeToCompare = +toDate(dateToCompare);\n  if (isNaN(timeToCompare)) return NaN;\n  let result;\n  let minDistance;\n  dates.forEach((date, index) => {\n    const date_ = toDate(date);\n    if (isNaN(+date_)) {\n      result = NaN;\n      minDistance = NaN;\n      return;\n    }\n    const distance = Math.abs(timeToCompare - +date_);\n    if (result == null || distance < minDistance) {\n      result = index;\n      minDistance = distance;\n    }\n  });\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default closestIndexTo;", "map": {"version": 3, "names": ["toDate", "closestIndexTo", "dateToCompare", "dates", "timeToCompare", "isNaN", "NaN", "result", "minDistance", "for<PERSON>ach", "date", "index", "date_", "distance", "Math", "abs"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/closestIndexTo.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * @name closestIndexTo\n * @category Common Helpers\n * @summary Return an index of the closest date from the array comparing to the given date.\n *\n * @description\n * Return an index of the closest date from the array comparing to the given date.\n *\n * @param dateToCompare - The date to compare with\n * @param dates - The array to search\n *\n * @returns An index of the date closest to the given date or undefined if no valid value is given\n *\n * @example\n * // Which date is closer to 6 September 2015?\n * const dateToCompare = new Date(2015, 8, 6)\n * const datesArray = [\n *   new Date(2015, 0, 1),\n *   new Date(2016, 0, 1),\n *   new Date(2017, 0, 1)\n * ]\n * const result = closestIndexTo(dateToCompare, datesArray)\n * //=> 1\n */\nexport function closestIndexTo(dateToCompare, dates) {\n  // [TODO] It would be better to return -1 here rather than undefined, as this\n  // is how JS behaves, but it would be a breaking change, so we need\n  // to consider it for v4.\n  const timeToCompare = +toDate(dateToCompare);\n\n  if (isNaN(timeToCompare)) return NaN;\n\n  let result;\n  let minDistance;\n  dates.forEach((date, index) => {\n    const date_ = toDate(date);\n\n    if (isNaN(+date_)) {\n      result = NaN;\n      minDistance = NaN;\n      return;\n    }\n\n    const distance = Math.abs(timeToCompare - +date_);\n    if (result == null || distance < minDistance) {\n      result = index;\n      minDistance = distance;\n    }\n  });\n\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default closestIndexTo;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,aAAa,EAAEC,KAAK,EAAE;EACnD;EACA;EACA;EACA,MAAMC,aAAa,GAAG,CAACJ,MAAM,CAACE,aAAa,CAAC;EAE5C,IAAIG,KAAK,CAACD,aAAa,CAAC,EAAE,OAAOE,GAAG;EAEpC,IAAIC,MAAM;EACV,IAAIC,WAAW;EACfL,KAAK,CAACM,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;IAC7B,MAAMC,KAAK,GAAGZ,MAAM,CAACU,IAAI,CAAC;IAE1B,IAAIL,KAAK,CAAC,CAACO,KAAK,CAAC,EAAE;MACjBL,MAAM,GAAGD,GAAG;MACZE,WAAW,GAAGF,GAAG;MACjB;IACF;IAEA,MAAMO,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACX,aAAa,GAAG,CAACQ,KAAK,CAAC;IACjD,IAAIL,MAAM,IAAI,IAAI,IAAIM,QAAQ,GAAGL,WAAW,EAAE;MAC5CD,MAAM,GAAGI,KAAK;MACdH,WAAW,GAAGK,QAAQ;IACxB;EACF,CAAC,CAAC;EAEF,OAAON,MAAM;AACf;;AAEA;AACA,eAAeN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}