{"ast": null, "code": "import { addLeadingZeros } from \"./_lib/addLeadingZeros.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link formatRFC3339} function options.\n */\n\n/**\n * @name formatRFC3339\n * @category Common Helpers\n * @summary Format the date according to the RFC 3339 standard (https://tools.ietf.org/html/rfc3339#section-5.6).\n *\n * @description\n * Return the formatted date string in RFC 3339 format. Options may be passed to control the parts and notations of the date.\n *\n * @param date - The original date\n * @param options - An object with options.\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format:\n * formatRFC3339(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18T19:00:52Z'\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format, 3 digits of second fraction\n * formatRFC3339(new Date(2019, 8, 18, 19, 0, 52, 234), {\n *   fractionDigits: 3\n * })\n * //=> '2019-09-18T19:00:52.234Z'\n */\nexport function formatRFC3339(date, options) {\n  const date_ = toDate(date, options?.in);\n  if (!isValid(date_)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n  const fractionDigits = options?.fractionDigits ?? 0;\n  const day = addLeadingZeros(date_.getDate(), 2);\n  const month = addLeadingZeros(date_.getMonth() + 1, 2);\n  const year = date_.getFullYear();\n  const hour = addLeadingZeros(date_.getHours(), 2);\n  const minute = addLeadingZeros(date_.getMinutes(), 2);\n  const second = addLeadingZeros(date_.getSeconds(), 2);\n  let fractionalSecond = \"\";\n  if (fractionDigits > 0) {\n    const milliseconds = date_.getMilliseconds();\n    const fractionalSeconds = Math.trunc(milliseconds * Math.pow(10, fractionDigits - 3));\n    fractionalSecond = \".\" + addLeadingZeros(fractionalSeconds, fractionDigits);\n  }\n  let offset = \"\";\n  const tzOffset = date_.getTimezoneOffset();\n  if (tzOffset !== 0) {\n    const absoluteOffset = Math.abs(tzOffset);\n    const hourOffset = addLeadingZeros(Math.trunc(absoluteOffset / 60), 2);\n    const minuteOffset = addLeadingZeros(absoluteOffset % 60, 2);\n    // If less than 0, the sign is +, because it is ahead of time.\n    const sign = tzOffset < 0 ? \"+\" : \"-\";\n    offset = `${sign}${hourOffset}:${minuteOffset}`;\n  } else {\n    offset = \"Z\";\n  }\n  return `${year}-${month}-${day}T${hour}:${minute}:${second}${fractionalSecond}${offset}`;\n}\n\n// Fallback for modularized imports:\nexport default formatRFC3339;", "map": {"version": 3, "names": ["addLeadingZeros", "<PERSON><PERSON><PERSON><PERSON>", "toDate", "formatRFC3339", "date", "options", "date_", "in", "RangeError", "fractionDigits", "day", "getDate", "month", "getMonth", "year", "getFullYear", "hour", "getHours", "minute", "getMinutes", "second", "getSeconds", "fractionalSecond", "milliseconds", "getMilliseconds", "fractionalSeconds", "Math", "trunc", "pow", "offset", "tzOffset", "getTimezoneOffset", "absoluteOffset", "abs", "hourOffset", "minuteOffset", "sign"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/formatRFC3339.js"], "sourcesContent": ["import { addLeadingZeros } from \"./_lib/addLeadingZeros.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link formatRFC3339} function options.\n */\n\n/**\n * @name formatRFC3339\n * @category Common Helpers\n * @summary Format the date according to the RFC 3339 standard (https://tools.ietf.org/html/rfc3339#section-5.6).\n *\n * @description\n * Return the formatted date string in RFC 3339 format. Options may be passed to control the parts and notations of the date.\n *\n * @param date - The original date\n * @param options - An object with options.\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format:\n * formatRFC3339(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18T19:00:52Z'\n *\n * @example\n * // Represent 18 September 2019 in RFC 3339 format, 3 digits of second fraction\n * formatRFC3339(new Date(2019, 8, 18, 19, 0, 52, 234), {\n *   fractionDigits: 3\n * })\n * //=> '2019-09-18T19:00:52.234Z'\n */\nexport function formatRFC3339(date, options) {\n  const date_ = toDate(date, options?.in);\n\n  if (!isValid(date_)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  const fractionDigits = options?.fractionDigits ?? 0;\n\n  const day = addLeadingZeros(date_.getDate(), 2);\n  const month = addLeadingZeros(date_.getMonth() + 1, 2);\n  const year = date_.getFullYear();\n\n  const hour = addLeadingZeros(date_.getHours(), 2);\n  const minute = addLeadingZeros(date_.getMinutes(), 2);\n  const second = addLeadingZeros(date_.getSeconds(), 2);\n\n  let fractionalSecond = \"\";\n  if (fractionDigits > 0) {\n    const milliseconds = date_.getMilliseconds();\n    const fractionalSeconds = Math.trunc(\n      milliseconds * Math.pow(10, fractionDigits - 3),\n    );\n    fractionalSecond = \".\" + addLeadingZeros(fractionalSeconds, fractionDigits);\n  }\n\n  let offset = \"\";\n  const tzOffset = date_.getTimezoneOffset();\n\n  if (tzOffset !== 0) {\n    const absoluteOffset = Math.abs(tzOffset);\n    const hourOffset = addLeadingZeros(Math.trunc(absoluteOffset / 60), 2);\n    const minuteOffset = addLeadingZeros(absoluteOffset % 60, 2);\n    // If less than 0, the sign is +, because it is ahead of time.\n    const sign = tzOffset < 0 ? \"+\" : \"-\";\n\n    offset = `${sign}${hourOffset}:${minuteOffset}`;\n  } else {\n    offset = \"Z\";\n  }\n\n  return `${year}-${month}-${day}T${hour}:${minute}:${second}${fractionalSecond}${offset}`;\n}\n\n// Fallback for modularized imports:\nexport default formatRFC3339;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC3C,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EAEvC,IAAI,CAACN,OAAO,CAACK,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIE,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EAEA,MAAMC,cAAc,GAAGJ,OAAO,EAAEI,cAAc,IAAI,CAAC;EAEnD,MAAMC,GAAG,GAAGV,eAAe,CAACM,KAAK,CAACK,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/C,MAAMC,KAAK,GAAGZ,eAAe,CAACM,KAAK,CAACO,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EACtD,MAAMC,IAAI,GAAGR,KAAK,CAACS,WAAW,CAAC,CAAC;EAEhC,MAAMC,IAAI,GAAGhB,eAAe,CAACM,KAAK,CAACW,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACjD,MAAMC,MAAM,GAAGlB,eAAe,CAACM,KAAK,CAACa,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;EACrD,MAAMC,MAAM,GAAGpB,eAAe,CAACM,KAAK,CAACe,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;EAErD,IAAIC,gBAAgB,GAAG,EAAE;EACzB,IAAIb,cAAc,GAAG,CAAC,EAAE;IACtB,MAAMc,YAAY,GAAGjB,KAAK,CAACkB,eAAe,CAAC,CAAC;IAC5C,MAAMC,iBAAiB,GAAGC,IAAI,CAACC,KAAK,CAClCJ,YAAY,GAAGG,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEnB,cAAc,GAAG,CAAC,CAChD,CAAC;IACDa,gBAAgB,GAAG,GAAG,GAAGtB,eAAe,CAACyB,iBAAiB,EAAEhB,cAAc,CAAC;EAC7E;EAEA,IAAIoB,MAAM,GAAG,EAAE;EACf,MAAMC,QAAQ,GAAGxB,KAAK,CAACyB,iBAAiB,CAAC,CAAC;EAE1C,IAAID,QAAQ,KAAK,CAAC,EAAE;IAClB,MAAME,cAAc,GAAGN,IAAI,CAACO,GAAG,CAACH,QAAQ,CAAC;IACzC,MAAMI,UAAU,GAAGlC,eAAe,CAAC0B,IAAI,CAACC,KAAK,CAACK,cAAc,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IACtE,MAAMG,YAAY,GAAGnC,eAAe,CAACgC,cAAc,GAAG,EAAE,EAAE,CAAC,CAAC;IAC5D;IACA,MAAMI,IAAI,GAAGN,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;IAErCD,MAAM,GAAG,GAAGO,IAAI,GAAGF,UAAU,IAAIC,YAAY,EAAE;EACjD,CAAC,MAAM;IACLN,MAAM,GAAG,GAAG;EACd;EAEA,OAAO,GAAGf,IAAI,IAAIF,KAAK,IAAIF,GAAG,IAAIM,IAAI,IAAIE,MAAM,IAAIE,MAAM,GAAGE,gBAAgB,GAAGO,MAAM,EAAE;AAC1F;;AAEA;AACA,eAAe1B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}