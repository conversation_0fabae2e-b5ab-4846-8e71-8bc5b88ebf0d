{"ast": null, "code": "'use client';\n\nexport { default } from './TableCell';\nexport { default as tableCellClasses } from './tableCellClasses';\nexport * from './tableCellClasses';", "map": {"version": 3, "names": ["default", "tableCellClasses"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/material/TableCell/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './TableCell';\nexport { default as tableCellClasses } from './tableCellClasses';\nexport * from './tableCellClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,oBAAoB;AAChE,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}