{"ast": null, "code": "import { getPickersLocalization } from './utils/getPickersLocalization';\n\n// This object is not Partial<PickersLocaleText> because it is the default values\n\nconst enUSPickers = {\n  // Calendar navigation\n  previousMonth: 'Previous month',\n  nextMonth: 'Next month',\n  // View navigation\n  openPreviousView: 'open previous view',\n  openNextView: 'open next view',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'year view is open, switch to calendar view' : 'calendar view is open, switch to year view',\n  // DateRange placeholders\n  start: 'Start',\n  end: 'End',\n  // Action bar\n  cancelButtonLabel: 'Cancel',\n  clearButtonLabel: 'Clear',\n  okButtonLabel: 'OK',\n  todayButtonLabel: 'Today',\n  // Toolbar titles\n  datePickerToolbarTitle: 'Select date',\n  dateTimePickerToolbarTitle: 'Select date & time',\n  timePickerToolbarTitle: 'Select time',\n  dateRangePickerToolbarTitle: 'Select date range',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => `Select ${view}. ${time === null ? 'No time selected' : `Selected time is ${adapter.format(time, 'fullTime')}`}`,\n  hoursClockNumberText: hours => `${hours} hours`,\n  minutesClockNumberText: minutes => `${minutes} minutes`,\n  secondsClockNumberText: seconds => `${seconds} seconds`,\n  // Digital clock labels\n  selectViewText: view => `Select ${view}`,\n  // Calendar labels\n  calendarWeekNumberHeaderLabel: 'Week number',\n  calendarWeekNumberHeaderText: '#',\n  calendarWeekNumberAriaLabelText: weekNumber => `Week ${weekNumber}`,\n  calendarWeekNumberText: weekNumber => `${weekNumber}`,\n  // Open picker labels\n  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Choose date, selected date is ${utils.format(value, 'fullDate')}` : 'Choose date',\n  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Choose time, selected time is ${utils.format(value, 'fullTime')}` : 'Choose time',\n  fieldClearLabel: 'Clear value',\n  // Table labels\n  timeTableLabel: 'pick time',\n  dateTableLabel: 'pick date',\n  // Field section placeholders\n  fieldYearPlaceholder: params => 'Y'.repeat(params.digitAmount),\n  fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'MMMM' : 'MM',\n  fieldDayPlaceholder: () => 'DD',\n  fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',\n  fieldHoursPlaceholder: () => 'hh',\n  fieldMinutesPlaceholder: () => 'mm',\n  fieldSecondsPlaceholder: () => 'ss',\n  fieldMeridiemPlaceholder: () => 'aa'\n};\nexport const DEFAULT_LOCALE = enUSPickers;\nexport const enUS = getPickersLocalization(enUSPickers);", "map": {"version": 3, "names": ["getPickersLocalization", "enUSPickers", "previousMonth", "nextMonth", "openPreviousView", "openNextView", "calendarViewSwitchingButtonAriaLabel", "view", "start", "end", "cancelButtonLabel", "clearButtonLabel", "okButtonLabel", "todayButtonLabel", "datePickerToolbarTitle", "dateTimePickerToolbarTitle", "timePickerToolbarTitle", "dateRangePickerToolbarTitle", "clockLabelText", "time", "adapter", "format", "hoursClockNumberText", "hours", "minutesClockNumberText", "minutes", "secondsClockNumberText", "seconds", "selectViewText", "calendarWeekNumberHeaderLabel", "calendarWeekNumberHeaderText", "calendarWeekNumberAriaLabelText", "weekNumber", "calendarWeekNumberText", "openDatePickerDialogue", "value", "utils", "<PERSON><PERSON><PERSON><PERSON>", "openTimePickerDialogue", "fieldClearLabel", "timeTable<PERSON>abel", "dateTableLabel", "fieldYearPlaceholder", "params", "repeat", "digitAmount", "fieldMonthPlaceholder", "contentType", "fieldDayPlaceholder", "fieldWeekDayPlaceholder", "fieldHoursPlaceholder", "fieldMinutesPlaceholder", "fieldSecondsPlaceholder", "fieldMeridiemPlaceholder", "DEFAULT_LOCALE", "enUS"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/locales/enUS.js"], "sourcesContent": ["import { getPickersLocalization } from './utils/getPickersLocalization';\n\n// This object is not Partial<PickersLocaleText> because it is the default values\n\nconst enUSPickers = {\n  // Calendar navigation\n  previousMonth: 'Previous month',\n  nextMonth: 'Next month',\n  // View navigation\n  openPreviousView: 'open previous view',\n  openNextView: 'open next view',\n  calendarViewSwitchingButtonAriaLabel: view => view === 'year' ? 'year view is open, switch to calendar view' : 'calendar view is open, switch to year view',\n  // DateRange placeholders\n  start: 'Start',\n  end: 'End',\n  // Action bar\n  cancelButtonLabel: 'Cancel',\n  clearButtonLabel: 'Clear',\n  okButtonLabel: 'OK',\n  todayButtonLabel: 'Today',\n  // Toolbar titles\n  datePickerToolbarTitle: 'Select date',\n  dateTimePickerToolbarTitle: 'Select date & time',\n  timePickerToolbarTitle: 'Select time',\n  dateRangePickerToolbarTitle: 'Select date range',\n  // Clock labels\n  clockLabelText: (view, time, adapter) => `Select ${view}. ${time === null ? 'No time selected' : `Selected time is ${adapter.format(time, 'fullTime')}`}`,\n  hoursClockNumberText: hours => `${hours} hours`,\n  minutesClockNumberText: minutes => `${minutes} minutes`,\n  secondsClockNumberText: seconds => `${seconds} seconds`,\n  // Digital clock labels\n  selectViewText: view => `Select ${view}`,\n  // Calendar labels\n  calendarWeekNumberHeaderLabel: 'Week number',\n  calendarWeekNumberHeaderText: '#',\n  calendarWeekNumberAriaLabelText: weekNumber => `Week ${weekNumber}`,\n  calendarWeekNumberText: weekNumber => `${weekNumber}`,\n  // Open picker labels\n  openDatePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Choose date, selected date is ${utils.format(value, 'fullDate')}` : 'Choose date',\n  openTimePickerDialogue: (value, utils) => value !== null && utils.isValid(value) ? `Choose time, selected time is ${utils.format(value, 'fullTime')}` : 'Choose time',\n  fieldClearLabel: 'Clear value',\n  // Table labels\n  timeTableLabel: 'pick time',\n  dateTableLabel: 'pick date',\n  // Field section placeholders\n  fieldYearPlaceholder: params => 'Y'.repeat(params.digitAmount),\n  fieldMonthPlaceholder: params => params.contentType === 'letter' ? 'MMMM' : 'MM',\n  fieldDayPlaceholder: () => 'DD',\n  fieldWeekDayPlaceholder: params => params.contentType === 'letter' ? 'EEEE' : 'EE',\n  fieldHoursPlaceholder: () => 'hh',\n  fieldMinutesPlaceholder: () => 'mm',\n  fieldSecondsPlaceholder: () => 'ss',\n  fieldMeridiemPlaceholder: () => 'aa'\n};\nexport const DEFAULT_LOCALE = enUSPickers;\nexport const enUS = getPickersLocalization(enUSPickers);"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,gCAAgC;;AAEvE;;AAEA,MAAMC,WAAW,GAAG;EAClB;EACAC,aAAa,EAAE,gBAAgB;EAC/BC,SAAS,EAAE,YAAY;EACvB;EACAC,gBAAgB,EAAE,oBAAoB;EACtCC,YAAY,EAAE,gBAAgB;EAC9BC,oCAAoC,EAAEC,IAAI,IAAIA,IAAI,KAAK,MAAM,GAAG,4CAA4C,GAAG,4CAA4C;EAC3J;EACAC,KAAK,EAAE,OAAO;EACdC,GAAG,EAAE,KAAK;EACV;EACAC,iBAAiB,EAAE,QAAQ;EAC3BC,gBAAgB,EAAE,OAAO;EACzBC,aAAa,EAAE,IAAI;EACnBC,gBAAgB,EAAE,OAAO;EACzB;EACAC,sBAAsB,EAAE,aAAa;EACrCC,0BAA0B,EAAE,oBAAoB;EAChDC,sBAAsB,EAAE,aAAa;EACrCC,2BAA2B,EAAE,mBAAmB;EAChD;EACAC,cAAc,EAAEA,CAACX,IAAI,EAAEY,IAAI,EAAEC,OAAO,KAAK,UAAUb,IAAI,KAAKY,IAAI,KAAK,IAAI,GAAG,kBAAkB,GAAG,oBAAoBC,OAAO,CAACC,MAAM,CAACF,IAAI,EAAE,UAAU,CAAC,EAAE,EAAE;EACzJG,oBAAoB,EAAEC,KAAK,IAAI,GAAGA,KAAK,QAAQ;EAC/CC,sBAAsB,EAAEC,OAAO,IAAI,GAAGA,OAAO,UAAU;EACvDC,sBAAsB,EAAEC,OAAO,IAAI,GAAGA,OAAO,UAAU;EACvD;EACAC,cAAc,EAAErB,IAAI,IAAI,UAAUA,IAAI,EAAE;EACxC;EACAsB,6BAA6B,EAAE,aAAa;EAC5CC,4BAA4B,EAAE,GAAG;EACjCC,+BAA+B,EAAEC,UAAU,IAAI,QAAQA,UAAU,EAAE;EACnEC,sBAAsB,EAAED,UAAU,IAAI,GAAGA,UAAU,EAAE;EACrD;EACAE,sBAAsB,EAAEA,CAACC,KAAK,EAAEC,KAAK,KAAKD,KAAK,KAAK,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAG,iCAAiCC,KAAK,CAACf,MAAM,CAACc,KAAK,EAAE,UAAU,CAAC,EAAE,GAAG,aAAa;EACrKG,sBAAsB,EAAEA,CAACH,KAAK,EAAEC,KAAK,KAAKD,KAAK,KAAK,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GAAG,iCAAiCC,KAAK,CAACf,MAAM,CAACc,KAAK,EAAE,UAAU,CAAC,EAAE,GAAG,aAAa;EACrKI,eAAe,EAAE,aAAa;EAC9B;EACAC,cAAc,EAAE,WAAW;EAC3BC,cAAc,EAAE,WAAW;EAC3B;EACAC,oBAAoB,EAAEC,MAAM,IAAI,GAAG,CAACC,MAAM,CAACD,MAAM,CAACE,WAAW,CAAC;EAC9DC,qBAAqB,EAAEH,MAAM,IAAIA,MAAM,CAACI,WAAW,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI;EAChFC,mBAAmB,EAAEA,CAAA,KAAM,IAAI;EAC/BC,uBAAuB,EAAEN,MAAM,IAAIA,MAAM,CAACI,WAAW,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI;EAClFG,qBAAqB,EAAEA,CAAA,KAAM,IAAI;EACjCC,uBAAuB,EAAEA,CAAA,KAAM,IAAI;EACnCC,uBAAuB,EAAEA,CAAA,KAAM,IAAI;EACnCC,wBAAwB,EAAEA,CAAA,KAAM;AAClC,CAAC;AACD,OAAO,MAAMC,cAAc,GAAGrD,WAAW;AACzC,OAAO,MAAMsD,IAAI,GAAGvD,sBAAsB,CAACC,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}