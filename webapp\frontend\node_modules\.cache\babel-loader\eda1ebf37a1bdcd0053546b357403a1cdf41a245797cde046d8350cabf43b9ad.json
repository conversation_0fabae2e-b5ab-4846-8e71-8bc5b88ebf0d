{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeListRivoluzionato.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, CircularProgress, Tooltip, Grid, List, ListItem, ListItemText, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Visibility as ViewIcon, Assignment as AssignIcon, Refresh as RefreshIcon, Person as PersonIcon, Email as EmailIcon, Phone as PhoneIcon, ExpandMore as ExpandMoreIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComandeListRivoluzionato = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Stati comande\n  const [comande, setComande] = useState([]);\n  const [statistiche, setStatistiche] = useState(null);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    if (cantiereId) {\n      loadResponsabili();\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]);\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const data = await comandeService.getComande(cantiereId);\n      setComande(data.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenResponsabileDialog(true);\n  };\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDeleteResponsabile = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n  const getStatoColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'IN_CORSO': 'primary',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600,\n          color: 'primary.main'\n        },\n        children: [\"Gestione Responsabili e Comande - \", cantiereName]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 2,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 24\n          }, this),\n          onClick: () => setOpenCreaConCavi(true),\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: \"Nuova Comanda\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 24\n          }, this),\n          onClick: () => {\n            loadResponsabili();\n            loadComande();\n            loadStatistiche();\n          },\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500\n          },\n          children: \"Aggiorna\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 9\n    }, this), statistiche && /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"info.main\",\n            fontWeight: \"bold\",\n            children: statistiche.responsabili_attivi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Responsabili Attivi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"primary.main\",\n            fontWeight: \"bold\",\n            children: statistiche.totale_comande\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Totale Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"warning.main\",\n            fontWeight: \"bold\",\n            children: statistiche.comande_in_corso\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"In Corso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            textAlign: 'center',\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            color: \"success.main\",\n            fontWeight: \"bold\",\n            children: statistiche.comande_completate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Completate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Responsabili del Cantiere\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 26\n            }, this),\n            onClick: () => handleOpenResponsabileDialog('create'),\n            sx: {\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3,\n              py: 1\n            },\n            children: \"Inserisci Responsabile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), loadingResponsabili ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          py: 4,\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: responsabili.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 0,\n            sx: {\n              p: 6,\n              textAlign: 'center',\n              backgroundColor: 'grey.50',\n              border: '1px dashed',\n              borderColor: 'grey.300'\n            },\n            children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n              sx: {\n                fontSize: 48,\n                color: 'grey.400',\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Nessun responsabile configurato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 3\n              },\n              children: \"Aggiungi il primo responsabile per iniziare a gestire le comande\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 32\n              }, this),\n              onClick: () => handleOpenResponsabileDialog('create'),\n              sx: {\n                textTransform: 'none'\n              },\n              children: \"Inserisci Primo Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 17\n          }, this) : responsabili.map(responsabile => /*#__PURE__*/_jsxDEV(Accordion, {\n            sx: {\n              mb: 2,\n              '&:before': {\n                display: 'none'\n              },\n              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n              border: '1px solid',\n              borderColor: 'grey.200'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n              expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 35\n              }, this),\n              sx: {\n                '&:hover': {\n                  backgroundColor: 'grey.50'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                width: \"100%\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 2,\n                  children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                    color: \"primary\",\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      sx: {\n                        fontWeight: 500\n                      },\n                      children: responsabile.nome_responsabile\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 416,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      gap: 3,\n                      mt: 0.5,\n                      children: [responsabile.email && /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 0.5,\n                        children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n                          fontSize: \"small\",\n                          color: \"action\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 422,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"text.secondary\",\n                          children: responsabile.email\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 423,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 421,\n                        columnNumber: 33\n                      }, this), responsabile.telefono && /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 0.5,\n                        children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                          fontSize: \"small\",\n                          color: \"action\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 430,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"text.secondary\",\n                          children: responsabile.telefono\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 431,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 429,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 419,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    icon: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 35\n                    }, this),\n                    label: `${Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) ? comandePerResponsabile[responsabile.id_responsabile].length : 0} comande`,\n                    size: \"small\",\n                    color: \"primary\",\n                    variant: \"outlined\",\n                    sx: {\n                      fontWeight: 500\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Modifica responsabile\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleOpenResponsabileDialog('edit', responsabile),\n                      sx: {\n                        '&:hover': {\n                          backgroundColor: 'primary.light',\n                          color: 'white'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 460,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 450,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Elimina responsabile\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleDeleteResponsabile(responsabile.id_responsabile),\n                      sx: {\n                        '&:hover': {\n                          backgroundColor: 'error.light',\n                          color: 'white'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 474,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n              sx: {\n                pt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 500,\n                  mb: 2\n                },\n                children: \"Comande Assegnate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 23\n              }, this), !Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) || comandePerResponsabile[responsabile.id_responsabile].length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  textAlign: 'center',\n                  backgroundColor: 'grey.50',\n                  borderRadius: 1,\n                  border: '1px dashed',\n                  borderColor: 'grey.300'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Nessuna comanda assegnata a questo responsabile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 25\n              }, this) : /*#__PURE__*/_jsxDEV(List, {\n                dense: true,\n                children: Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) && comandePerResponsabile[responsabile.id_responsabile].map(comanda => /*#__PURE__*/_jsxDEV(ListItem, {\n                  divider: true,\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        fontWeight: \"bold\",\n                        children: comanda.codice_comanda\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 508,\n                        columnNumber: 37\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: getTipoComandaLabel(comanda.tipo_comanda),\n                        size: \"small\",\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 511,\n                        columnNumber: 37\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: comanda.stato || 'CREATA',\n                        size: \"small\",\n                        color: getStatoColor(comanda.stato)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 516,\n                        columnNumber: 37\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 35\n                    }, this),\n                    secondary: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      children: [comanda.descrizione || 'Nessuna descrizione', comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 524,\n                      columnNumber: 35\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 31\n                  }, this)\n                }, comanda.codice_comanda, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 29\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 21\n            }, this)]\n          }, responsabile.id_responsabile, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openResponsabileDialog,\n      onClose: handleCloseResponsabileDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nome Responsabile\",\n            value: formDataResponsabile.nome_responsabile,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              nome_responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: formDataResponsabile.email,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Email per notifiche (opzionale se inserisci telefono)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Telefono\",\n            value: formDataResponsabile.telefono,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              telefono: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Numero per SMS (opzionale se inserisci email)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseResponsabileDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitResponsabile,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 544,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: () => {\n        loadComande();\n        loadStatistiche();\n        loadResponsabili();\n        setOpenCreaConCavi(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 616,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 247,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeListRivoluzionato, \"RCyY25oyd2KxFP2mXglVnFz9LZc=\");\n_c = ComandeListRivoluzionato;\nexport default ComandeListRivoluzionato;\nvar _c;\n$RefreshReg$(_c, \"ComandeListRivoluzionato\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON><PERSON>", "Grid", "List", "ListItem", "ListItemText", "Accordion", "AccordionSummary", "AccordionDetails", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Visibility", "ViewIcon", "Assignment", "AssignIcon", "Refresh", "RefreshIcon", "Person", "PersonIcon", "Email", "EmailIcon", "Phone", "PhoneIcon", "ExpandMore", "ExpandMoreIcon", "comandeService", "responsabiliService", "CreaComandaConCavi", "jsxDEV", "_jsxDEV", "ComandeListRivoluzionato", "cantiereId", "cantiereName", "_s", "loading", "setLoading", "error", "setError", "comande", "setComande", "statistiche", "setStatistiche", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "loadResponsabili", "loadComande", "loadStatistiche", "data", "getComande", "err", "console", "stats", "getStatisticheComande", "getResponsabiliCantiere", "loadComandePerResponsabili", "_err$response", "_err$response$data", "errorMessage", "response", "detail", "message", "responsabiliList", "comandeMap", "responsabile", "getComandeByResponsabile", "Array", "isArray", "id_responsabile", "handleOpenResponsabileDialog", "mode", "handleCloseResponsabileDialog", "handleSubmitResponsabile", "trim", "createResponsabile", "updateResponsabile", "handleDeleteResponsabile", "idResponsabile", "window", "confirm", "deleteResponsabile", "getTipoComandaLabel", "tipo", "labels", "getStatoColor", "stato", "colors", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "variant", "fontWeight", "color", "gap", "startIcon", "onClick", "textTransform", "px", "severity", "container", "spacing", "item", "xs", "sm", "md", "textAlign", "responsabili_attivi", "totale_comande", "comande_in_corso", "comande_completate", "py", "length", "elevation", "backgroundColor", "border", "borderColor", "fontSize", "gutterBottom", "map", "boxShadow", "expandIcon", "width", "mt", "e", "stopPropagation", "icon", "label", "size", "title", "pt", "borderRadius", "dense", "comanda", "divider", "primary", "codice_comanda", "tipo_comanda", "secondary", "descrizione", "data_creazione", "Date", "toLocaleDateString", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "pb", "value", "onChange", "target", "margin", "required", "type", "helperText", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeListRivoluzionato.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Alert,\n  CircularProgress,\n  Tooltip,\n  Grid,\n  List,\n  ListItem,\n  ListItemText,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Visibility as ViewIcon,\n  Assignment as AssignIcon,\n  Refresh as RefreshIcon,\n  Person as PersonIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  ExpandMore as ExpandMoreIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\n\nconst ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Stati comande\n  const [comande, setComande] = useState([]);\n  const [statistiche, setStatistiche] = useState(null);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    if (cantiereId) {\n      loadResponsabili();\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]);\n\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const data = await comandeService.getComande(cantiereId);\n      setComande(data.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    \n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    \n    setOpenResponsabileDialog(true);\n  };\n\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      \n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      \n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDeleteResponsabile = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  const getStatoColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'IN_CORSO': 'primary',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n          Gestione Responsabili e Comande - {cantiereName}\n        </Typography>\n        <Box display=\"flex\" gap={2}>\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={() => setOpenCreaConCavi(true)}\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            Nuova Comanda\n          </Button>\n          <Button\n            variant=\"outlined\"\n            startIcon={<RefreshIcon />}\n            onClick={() => {\n              loadResponsabili();\n              loadComande();\n              loadStatistiche();\n            }}\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500\n            }}\n          >\n            Aggiorna\n          </Button>\n        </Box>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Statistiche */}\n      {statistiche && (\n        <Grid container spacing={3} mb={4}>\n          <Grid item xs={12} sm={6} md={3}>\n            <Card sx={{ textAlign: 'center', p: 2 }}>\n              <Typography variant=\"h4\" color=\"info.main\" fontWeight=\"bold\">\n                {statistiche.responsabili_attivi}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Responsabili Attivi\n              </Typography>\n            </Card>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <Card sx={{ textAlign: 'center', p: 2 }}>\n              <Typography variant=\"h4\" color=\"primary.main\" fontWeight=\"bold\">\n                {statistiche.totale_comande}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Totale Comande\n              </Typography>\n            </Card>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <Card sx={{ textAlign: 'center', p: 2 }}>\n              <Typography variant=\"h4\" color=\"warning.main\" fontWeight=\"bold\">\n                {statistiche.comande_in_corso}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                In Corso\n              </Typography>\n            </Card>\n          </Grid>\n          <Grid item xs={12} sm={6} md={3}>\n            <Card sx={{ textAlign: 'center', p: 2 }}>\n              <Typography variant=\"h4\" color=\"success.main\" fontWeight=\"bold\">\n                {statistiche.comande_completate}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Completate\n              </Typography>\n            </Card>\n          </Grid>\n        </Grid>\n      )}\n\n      {/* Sezione Responsabili - Elemento Principale */}\n      <Box>\n        <Box>\n          {/* Toolbar Responsabili */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Responsabili del Cantiere\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={() => handleOpenResponsabileDialog('create')}\n              sx={{\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1\n              }}\n            >\n              Inserisci Responsabile\n            </Button>\n          </Box>\n\n          {loadingResponsabili ? (\n            <Box display=\"flex\" justifyContent=\"center\" py={4}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              {responsabili.length === 0 ? (\n                <Paper\n                  elevation={0}\n                  sx={{\n                    p: 6,\n                    textAlign: 'center',\n                    backgroundColor: 'grey.50',\n                    border: '1px dashed',\n                    borderColor: 'grey.300'\n                  }}\n                >\n                  <PersonIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n                  <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                    Nessun responsabile configurato\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                    Aggiungi il primo responsabile per iniziare a gestire le comande\n                  </Typography>\n                  <Button\n                    variant=\"contained\"\n                    startIcon={<AddIcon />}\n                    onClick={() => handleOpenResponsabileDialog('create')}\n                    sx={{ textTransform: 'none' }}\n                  >\n                    Inserisci Primo Responsabile\n                  </Button>\n                </Paper>\n              ) : (\n                responsabili.map((responsabile) => (\n                  <Accordion\n                    key={responsabile.id_responsabile}\n                    sx={{\n                      mb: 2,\n                      '&:before': { display: 'none' },\n                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                      border: '1px solid',\n                      borderColor: 'grey.200'\n                    }}\n                  >\n                    <AccordionSummary\n                      expandIcon={<ExpandMoreIcon />}\n                      sx={{\n                        '&:hover': {\n                          backgroundColor: 'grey.50'\n                        }\n                      }}\n                    >\n                      <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" width=\"100%\">\n                        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                          <PersonIcon color=\"primary\" sx={{ fontSize: 28 }} />\n                          <Box>\n                            <Typography variant=\"h6\" sx={{ fontWeight: 500 }}>\n                              {responsabile.nome_responsabile}\n                            </Typography>\n                            <Box display=\"flex\" gap={3} mt={0.5}>\n                              {responsabile.email && (\n                                <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n                                  <EmailIcon fontSize=\"small\" color=\"action\" />\n                                  <Typography variant=\"body2\" color=\"text.secondary\">\n                                    {responsabile.email}\n                                  </Typography>\n                                </Box>\n                              )}\n                              {responsabile.telefono && (\n                                <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n                                  <PhoneIcon fontSize=\"small\" color=\"action\" />\n                                  <Typography variant=\"body2\" color=\"text.secondary\">\n                                    {responsabile.telefono}\n                                  </Typography>\n                                </Box>\n                              )}\n                            </Box>\n                          </Box>\n                        </Box>\n\n                        <Box display=\"flex\" alignItems=\"center\" gap={1} onClick={(e) => e.stopPropagation()}>\n                          <Chip\n                            icon={<AssignIcon />}\n                            label={`${Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) ? comandePerResponsabile[responsabile.id_responsabile].length : 0} comande`}\n                            size=\"small\"\n                            color=\"primary\"\n                            variant=\"outlined\"\n                            sx={{ fontWeight: 500 }}\n                          />\n                          <Tooltip title=\"Modifica responsabile\">\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleOpenResponsabileDialog('edit', responsabile)}\n                              sx={{\n                                '&:hover': {\n                                  backgroundColor: 'primary.light',\n                                  color: 'white'\n                                }\n                              }}\n                            >\n                              <EditIcon fontSize=\"small\" />\n                            </IconButton>\n                          </Tooltip>\n                          <Tooltip title=\"Elimina responsabile\">\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleDeleteResponsabile(responsabile.id_responsabile)}\n                              sx={{\n                                '&:hover': {\n                                  backgroundColor: 'error.light',\n                                  color: 'white'\n                                }\n                              }}\n                            >\n                              <DeleteIcon fontSize=\"small\" />\n                            </IconButton>\n                          </Tooltip>\n                        </Box>\n                      </Box>\n                    </AccordionSummary>\n\n                    <AccordionDetails sx={{ pt: 2 }}>\n                      <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>\n                        Comande Assegnate\n                      </Typography>\n\n                      {(!Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) || comandePerResponsabile[responsabile.id_responsabile].length === 0) ? (\n                        <Box\n                          sx={{\n                            p: 3,\n                            textAlign: 'center',\n                            backgroundColor: 'grey.50',\n                            borderRadius: 1,\n                            border: '1px dashed',\n                            borderColor: 'grey.300'\n                          }}\n                        >\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Nessuna comanda assegnata a questo responsabile\n                          </Typography>\n                        </Box>\n                      ) : (\n                        <List dense>\n                          {Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) && comandePerResponsabile[responsabile.id_responsabile].map((comanda) => (\n                            <ListItem key={comanda.codice_comanda} divider>\n                              <ListItemText\n                                primary={\n                                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                                    <Typography variant=\"body2\" fontWeight=\"bold\">\n                                      {comanda.codice_comanda}\n                                    </Typography>\n                                    <Chip\n                                      label={getTipoComandaLabel(comanda.tipo_comanda)}\n                                      size=\"small\"\n                                      variant=\"outlined\"\n                                    />\n                                    <Chip\n                                      label={comanda.stato || 'CREATA'}\n                                      size=\"small\"\n                                      color={getStatoColor(comanda.stato)}\n                                    />\n                                  </Box>\n                                }\n                                secondary={\n                                  <Typography variant=\"body2\" color=\"textSecondary\">\n                                    {comanda.descrizione || 'Nessuna descrizione'}\n                                    {comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`}\n                                  </Typography>\n                                }\n                              />\n                            </ListItem>\n                          ))}\n                        </List>\n                      )}\n                    </AccordionDetails>\n                  </Accordion>\n                ))\n              )}\n            </Box>\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per creazione/modifica responsabile */}\n      <Dialog\n        open={openResponsabileDialog}\n        onClose={handleCloseResponsabileDialog}\n        maxWidth=\"sm\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nome Responsabile\"\n              value={formDataResponsabile.nome_responsabile}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              variant=\"outlined\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={formDataResponsabile.email}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Email per notifiche (opzionale se inserisci telefono)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Telefono\"\n              value={formDataResponsabile.telefono}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Numero per SMS (opzionale se inserisci email)\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseResponsabileDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitResponsabile}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog CreaComandaConCavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={() => {\n          loadComande();\n          loadStatistiche();\n          loadResponsabili();\n          setOpenCreaConCavi(false);\n        }}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeListRivoluzionato;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,QACX,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,QAAQ,EACtBC,UAAU,IAAIC,UAAU,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjE;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8D,KAAK,EAAEC,QAAQ,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAACgE,OAAO,EAAEC,UAAU,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkE,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoE,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACsE,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC0E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAAC4E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC8E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG/E,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAACgF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACkF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnF,QAAQ,CAAC;IAC/DoF,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACArF,SAAS,CAAC,MAAM;IACd,IAAIwD,UAAU,EAAE;MACd8B,gBAAgB,CAAC,CAAC;MAClBC,WAAW,CAAC,CAAC;MACbC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAChC,UAAU,CAAC,CAAC;EAEhB,MAAM+B,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF3B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM6B,IAAI,GAAG,MAAMvC,cAAc,CAACwC,UAAU,CAAClC,UAAU,CAAC;MACxDQ,UAAU,CAACyB,IAAI,CAAC1B,OAAO,IAAI,EAAE,CAAC;MAC9BD,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO6B,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,uCAAuC,EAAE8B,GAAG,CAAC;MAC3D7B,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMK,KAAK,GAAG,MAAM3C,cAAc,CAAC4C,qBAAqB,CAACtC,UAAU,CAAC;MACpEU,cAAc,CAAC2B,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,2CAA2C,EAAE8B,GAAG,CAAC;IACjE;EACF,CAAC;EAED,MAAML,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFd,sBAAsB,CAAC,IAAI,CAAC;MAC5BV,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM2B,IAAI,GAAG,MAAMtC,mBAAmB,CAAC4C,uBAAuB,CAACvC,UAAU,CAAC;MAC1Ec,eAAe,CAACmB,IAAI,IAAI,EAAE,CAAC;MAC3B,MAAMO,0BAA0B,CAACP,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOE,GAAG,EAAE;MAAA,IAAAM,aAAA,EAAAC,kBAAA;MACZN,OAAO,CAAC/B,KAAK,CAAC,0CAA0C,EAAE8B,GAAG,CAAC;MAC9D,MAAMQ,YAAY,GAAG,EAAAF,aAAA,GAAAN,GAAG,CAACS,QAAQ,cAAAH,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcR,IAAI,cAAAS,kBAAA,uBAAlBA,kBAAA,CAAoBG,MAAM,KAAIV,GAAG,CAACW,OAAO,IAAI,yCAAyC;MAC3GxC,QAAQ,CAAC,4CAA4CqC,YAAY,EAAE,CAAC;IACtE,CAAC,SAAS;MACR3B,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMwB,0BAA0B,GAAG,MAAOO,gBAAgB,IAAK;IAC7D,IAAI;MACF,MAAMC,UAAU,GAAG,CAAC,CAAC;MACrB,KAAK,MAAMC,YAAY,IAAIF,gBAAgB,EAAE;QAC3C,IAAI;UACF,MAAMH,QAAQ,GAAG,MAAMlD,cAAc,CAACwD,wBAAwB,CAAClD,UAAU,EAAEiD,YAAY,CAACtB,iBAAiB,CAAC;UAC1G;UACA,IAAIpB,OAAO,GAAG,EAAE;UAChB,IAAIqC,QAAQ,IAAIO,KAAK,CAACC,OAAO,CAACR,QAAQ,CAAC,EAAE;YACvCrC,OAAO,GAAGqC,QAAQ;UACpB,CAAC,MAAM,IAAIA,QAAQ,IAAIA,QAAQ,CAACrC,OAAO,IAAI4C,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACrC,OAAO,CAAC,EAAE;YAC1EA,OAAO,GAAGqC,QAAQ,CAACrC,OAAO;UAC5B,CAAC,MAAM,IAAIqC,QAAQ,IAAIA,QAAQ,CAACX,IAAI,IAAIkB,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACX,IAAI,CAAC,EAAE;YACpE1B,OAAO,GAAGqC,QAAQ,CAACX,IAAI;UACzB;UACAe,UAAU,CAACC,YAAY,CAACI,eAAe,CAAC,GAAG9C,OAAO;QACpD,CAAC,CAAC,OAAO4B,GAAG,EAAE;UACZC,OAAO,CAAC/B,KAAK,CAAC,sCAAsC4C,YAAY,CAACtB,iBAAiB,GAAG,EAAEQ,GAAG,CAAC;UAC3Fa,UAAU,CAACC,YAAY,CAACI,eAAe,CAAC,GAAG,EAAE;QAC/C;MACF;MACAnC,yBAAyB,CAAC8B,UAAU,CAAC;IACvC,CAAC,CAAC,OAAOb,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,uCAAuC,EAAE8B,GAAG,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMmB,4BAA4B,GAAGA,CAACC,IAAI,EAAEN,YAAY,GAAG,IAAI,KAAK;IAClE3B,yBAAyB,CAACiC,IAAI,CAAC;IAC/B/B,uBAAuB,CAACyB,YAAY,CAAC;IAErC,IAAIM,IAAI,KAAK,MAAM,IAAIN,YAAY,EAAE;MACnCvB,uBAAuB,CAAC;QACtBC,iBAAiB,EAAEsB,YAAY,CAACtB,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAEqB,YAAY,CAACrB,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAEoB,YAAY,CAACpB,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMoC,6BAA6B,GAAGA,CAAA,KAAM;IAC1CpC,yBAAyB,CAAC,KAAK,CAAC;IAChCI,uBAAuB,CAAC,IAAI,CAAC;IAC7BlB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAMmD,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFnD,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,CAACmB,oBAAoB,CAACE,iBAAiB,CAAC+B,IAAI,CAAC,CAAC,EAAE;QAClDpD,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACmB,oBAAoB,CAACG,KAAK,IAAI,CAACH,oBAAoB,CAACI,QAAQ,EAAE;QACjEvB,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAIe,sBAAsB,KAAK,QAAQ,EAAE;QACvC,MAAM1B,mBAAmB,CAACgE,kBAAkB,CAAC3D,UAAU,EAAEyB,oBAAoB,CAAC;MAChF,CAAC,MAAM,IAAIJ,sBAAsB,KAAK,MAAM,EAAE;QAC5C,MAAM1B,mBAAmB,CAACiE,kBAAkB,CAACrC,oBAAoB,CAAC8B,eAAe,EAAE5B,oBAAoB,CAAC;MAC1G;MAEA+B,6BAA6B,CAAC,CAAC;MAC/B,MAAM1B,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOK,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,yBAAyB,EAAE8B,GAAG,CAAC;MAC7C7B,QAAQ,CAAC6B,GAAG,CAACU,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAMgB,wBAAwB,GAAG,MAAOC,cAAc,IAAK;IACzD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAMrE,mBAAmB,CAACsE,kBAAkB,CAACH,cAAc,CAAC;MAC5D,MAAMhC,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOK,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,EAAE8B,GAAG,CAAC;MAChD7B,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;EAED,MAAM4D,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE;IACpB,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAME,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAED,IAAInE,OAAO,EAAE;IACX,oBACEL,OAAA,CAACrD,GAAG;MAAC+H,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/E9E,OAAA,CAACjC,gBAAgB;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACElF,OAAA,CAACrD,GAAG;IAACwI,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAEhB9E,OAAA,CAACrD,GAAG;MAAC+H,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACS,EAAE,EAAE,CAAE;MAAAP,QAAA,gBAC3E9E,OAAA,CAAClD,UAAU;QAACwI,OAAO,EAAC,IAAI;QAACH,EAAE,EAAE;UAAEI,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAV,QAAA,GAAC,oCACrC,EAAC3E,YAAY;MAAA;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACblF,OAAA,CAACrD,GAAG;QAAC+H,OAAO,EAAC,MAAM;QAACe,GAAG,EAAE,CAAE;QAAAX,QAAA,gBACzB9E,OAAA,CAACjD,MAAM;UACLuI,OAAO,EAAC,WAAW;UACnBI,SAAS,eAAE1F,OAAA,CAACvB,OAAO;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBS,OAAO,EAAEA,CAAA,KAAM7E,kBAAkB,CAAC,IAAI,CAAE;UACxCqE,EAAE,EAAE;YACFS,aAAa,EAAE,MAAM;YACrBL,UAAU,EAAE,GAAG;YACfM,EAAE,EAAE;UACN,CAAE;UAAAf,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlF,OAAA,CAACjD,MAAM;UACLuI,OAAO,EAAC,UAAU;UAClBI,SAAS,eAAE1F,OAAA,CAACb,WAAW;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BS,OAAO,EAAEA,CAAA,KAAM;YACb3D,gBAAgB,CAAC,CAAC;YAClBC,WAAW,CAAC,CAAC;YACbC,eAAe,CAAC,CAAC;UACnB,CAAE;UACFiD,EAAE,EAAE;YACFS,aAAa,EAAE,MAAM;YACrBL,UAAU,EAAE;UACd,CAAE;UAAAT,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL3E,KAAK,iBACJP,OAAA,CAAClC,KAAK;MAACgI,QAAQ,EAAC,OAAO;MAACX,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACnCvE;IAAK;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGAvE,WAAW,iBACVX,OAAA,CAAC/B,IAAI;MAAC8H,SAAS;MAACC,OAAO,EAAE,CAAE;MAACX,EAAE,EAAE,CAAE;MAAAP,QAAA,gBAChC9E,OAAA,CAAC/B,IAAI;QAACgI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eAC9B9E,OAAA,CAACpD,IAAI;UAACuI,EAAE,EAAE;YAAEkB,SAAS,EAAE,QAAQ;YAAEjB,CAAC,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACtC9E,OAAA,CAAClD,UAAU;YAACwI,OAAO,EAAC,IAAI;YAACE,KAAK,EAAC,WAAW;YAACD,UAAU,EAAC,MAAM;YAAAT,QAAA,EACzDnE,WAAW,CAAC2F;UAAmB;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACblF,OAAA,CAAClD,UAAU;YAACwI,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAAAV,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPlF,OAAA,CAAC/B,IAAI;QAACgI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eAC9B9E,OAAA,CAACpD,IAAI;UAACuI,EAAE,EAAE;YAAEkB,SAAS,EAAE,QAAQ;YAAEjB,CAAC,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACtC9E,OAAA,CAAClD,UAAU;YAACwI,OAAO,EAAC,IAAI;YAACE,KAAK,EAAC,cAAc;YAACD,UAAU,EAAC,MAAM;YAAAT,QAAA,EAC5DnE,WAAW,CAAC4F;UAAc;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACblF,OAAA,CAAClD,UAAU;YAACwI,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAAAV,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPlF,OAAA,CAAC/B,IAAI;QAACgI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eAC9B9E,OAAA,CAACpD,IAAI;UAACuI,EAAE,EAAE;YAAEkB,SAAS,EAAE,QAAQ;YAAEjB,CAAC,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACtC9E,OAAA,CAAClD,UAAU;YAACwI,OAAO,EAAC,IAAI;YAACE,KAAK,EAAC,cAAc;YAACD,UAAU,EAAC,MAAM;YAAAT,QAAA,EAC5DnE,WAAW,CAAC6F;UAAgB;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACblF,OAAA,CAAClD,UAAU;YAACwI,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAAAV,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPlF,OAAA,CAAC/B,IAAI;QAACgI,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eAC9B9E,OAAA,CAACpD,IAAI;UAACuI,EAAE,EAAE;YAAEkB,SAAS,EAAE,QAAQ;YAAEjB,CAAC,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACtC9E,OAAA,CAAClD,UAAU;YAACwI,OAAO,EAAC,IAAI;YAACE,KAAK,EAAC,cAAc;YAACD,UAAU,EAAC,MAAM;YAAAT,QAAA,EAC5DnE,WAAW,CAAC8F;UAAkB;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACblF,OAAA,CAAClD,UAAU;YAACwI,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAAAV,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACP,eAGDlF,OAAA,CAACrD,GAAG;MAAAmI,QAAA,eACF9E,OAAA,CAACrD,GAAG;QAAAmI,QAAA,gBAEF9E,OAAA,CAACrD,GAAG;UAAC+H,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAACS,EAAE,EAAE,CAAE;UAAAP,QAAA,gBAC3E9E,OAAA,CAAClD,UAAU;YAACwI,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAe,CAAE;YAAAV,QAAA,EAAC;UAEzE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblF,OAAA,CAACjD,MAAM;YACLuI,OAAO,EAAC,WAAW;YACnBI,SAAS,eAAE1F,OAAA,CAACvB,OAAO;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBS,OAAO,EAAEA,CAAA,KAAMnC,4BAA4B,CAAC,QAAQ,CAAE;YACtD2B,EAAE,EAAE;cACFS,aAAa,EAAE,MAAM;cACrBL,UAAU,EAAE,GAAG;cACfM,EAAE,EAAE,CAAC;cACLa,EAAE,EAAE;YACN,CAAE;YAAA5B,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELjE,mBAAmB,gBAClBjB,OAAA,CAACrD,GAAG;UAAC+H,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,QAAQ;UAAC+B,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAChD9E,OAAA,CAACjC,gBAAgB;YAAAgH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAENlF,OAAA,CAACrD,GAAG;UAAAmI,QAAA,EACD/D,YAAY,CAAC4F,MAAM,KAAK,CAAC,gBACxB3G,OAAA,CAAC1C,KAAK;YACJsJ,SAAS,EAAE,CAAE;YACbzB,EAAE,EAAE;cACFC,CAAC,EAAE,CAAC;cACJiB,SAAS,EAAE,QAAQ;cACnBQ,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,YAAY;cACpBC,WAAW,EAAE;YACf,CAAE;YAAAjC,QAAA,gBAEF9E,OAAA,CAACX,UAAU;cAAC8F,EAAE,EAAE;gBAAE6B,QAAQ,EAAE,EAAE;gBAAExB,KAAK,EAAE,UAAU;gBAAEH,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DlF,OAAA,CAAClD,UAAU;cAACwI,OAAO,EAAC,IAAI;cAACE,KAAK,EAAC,gBAAgB;cAACyB,YAAY;cAAAnC,QAAA,EAAC;YAE7D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblF,OAAA,CAAClD,UAAU;cAACwI,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAACL,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblF,OAAA,CAACjD,MAAM;cACLuI,OAAO,EAAC,WAAW;cACnBI,SAAS,eAAE1F,OAAA,CAACvB,OAAO;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBS,OAAO,EAAEA,CAAA,KAAMnC,4BAA4B,CAAC,QAAQ,CAAE;cACtD2B,EAAE,EAAE;gBAAES,aAAa,EAAE;cAAO,CAAE;cAAAd,QAAA,EAC/B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,GAERnE,YAAY,CAACmG,GAAG,CAAE/D,YAAY,iBAC5BnD,OAAA,CAAC3B,SAAS;YAER8G,EAAE,EAAE;cACFE,EAAE,EAAE,CAAC;cACL,UAAU,EAAE;gBAAEX,OAAO,EAAE;cAAO,CAAC;cAC/ByC,SAAS,EAAE,2BAA2B;cACtCL,MAAM,EAAE,WAAW;cACnBC,WAAW,EAAE;YACf,CAAE;YAAAjC,QAAA,gBAEF9E,OAAA,CAAC1B,gBAAgB;cACf8I,UAAU,eAAEpH,OAAA,CAACL,cAAc;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC/BC,EAAE,EAAE;gBACF,SAAS,EAAE;kBACT0B,eAAe,EAAE;gBACnB;cACF,CAAE;cAAA/B,QAAA,eAEF9E,OAAA,CAACrD,GAAG;gBAAC+H,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC,QAAQ;gBAACD,cAAc,EAAC,eAAe;gBAAC0C,KAAK,EAAC,MAAM;gBAAAvC,QAAA,gBACjF9E,OAAA,CAACrD,GAAG;kBAAC+H,OAAO,EAAC,MAAM;kBAACE,UAAU,EAAC,QAAQ;kBAACa,GAAG,EAAE,CAAE;kBAAAX,QAAA,gBAC7C9E,OAAA,CAACX,UAAU;oBAACmG,KAAK,EAAC,SAAS;oBAACL,EAAE,EAAE;sBAAE6B,QAAQ,EAAE;oBAAG;kBAAE;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpDlF,OAAA,CAACrD,GAAG;oBAAAmI,QAAA,gBACF9E,OAAA,CAAClD,UAAU;sBAACwI,OAAO,EAAC,IAAI;sBAACH,EAAE,EAAE;wBAAEI,UAAU,EAAE;sBAAI,CAAE;sBAAAT,QAAA,EAC9C3B,YAAY,CAACtB;oBAAiB;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACblF,OAAA,CAACrD,GAAG;sBAAC+H,OAAO,EAAC,MAAM;sBAACe,GAAG,EAAE,CAAE;sBAAC6B,EAAE,EAAE,GAAI;sBAAAxC,QAAA,GACjC3B,YAAY,CAACrB,KAAK,iBACjB9B,OAAA,CAACrD,GAAG;wBAAC+H,OAAO,EAAC,MAAM;wBAACE,UAAU,EAAC,QAAQ;wBAACa,GAAG,EAAE,GAAI;wBAAAX,QAAA,gBAC/C9E,OAAA,CAACT,SAAS;0BAACyH,QAAQ,EAAC,OAAO;0BAACxB,KAAK,EAAC;wBAAQ;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7ClF,OAAA,CAAClD,UAAU;0BAACwI,OAAO,EAAC,OAAO;0BAACE,KAAK,EAAC,gBAAgB;0BAAAV,QAAA,EAC/C3B,YAAY,CAACrB;wBAAK;0BAAAiD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACT,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CACN,EACA/B,YAAY,CAACpB,QAAQ,iBACpB/B,OAAA,CAACrD,GAAG;wBAAC+H,OAAO,EAAC,MAAM;wBAACE,UAAU,EAAC,QAAQ;wBAACa,GAAG,EAAE,GAAI;wBAAAX,QAAA,gBAC/C9E,OAAA,CAACP,SAAS;0BAACuH,QAAQ,EAAC,OAAO;0BAACxB,KAAK,EAAC;wBAAQ;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7ClF,OAAA,CAAClD,UAAU;0BAACwI,OAAO,EAAC,OAAO;0BAACE,KAAK,EAAC,gBAAgB;0BAAAV,QAAA,EAC/C3B,YAAY,CAACpB;wBAAQ;0BAAAgD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlF,OAAA,CAACrD,GAAG;kBAAC+H,OAAO,EAAC,MAAM;kBAACE,UAAU,EAAC,QAAQ;kBAACa,GAAG,EAAE,CAAE;kBAACE,OAAO,EAAG4B,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;kBAAA1C,QAAA,gBAClF9E,OAAA,CAACzC,IAAI;oBACHkK,IAAI,eAAEzH,OAAA,CAACf,UAAU;sBAAA8F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACrBwC,KAAK,EAAE,GAAGrE,KAAK,CAACC,OAAO,CAACnC,sBAAsB,CAACgC,YAAY,CAACI,eAAe,CAAC,CAAC,GAAGpC,sBAAsB,CAACgC,YAAY,CAACI,eAAe,CAAC,CAACoD,MAAM,GAAG,CAAC,UAAW;oBAC1JgB,IAAI,EAAC,OAAO;oBACZnC,KAAK,EAAC,SAAS;oBACfF,OAAO,EAAC,UAAU;oBAClBH,EAAE,EAAE;sBAAEI,UAAU,EAAE;oBAAI;kBAAE;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACFlF,OAAA,CAAChC,OAAO;oBAAC4J,KAAK,EAAC,uBAAuB;oBAAA9C,QAAA,eACpC9E,OAAA,CAACxC,UAAU;sBACTmK,IAAI,EAAC,OAAO;sBACZhC,OAAO,EAAEA,CAAA,KAAMnC,4BAA4B,CAAC,MAAM,EAAEL,YAAY,CAAE;sBAClEgC,EAAE,EAAE;wBACF,SAAS,EAAE;0BACT0B,eAAe,EAAE,eAAe;0BAChCrB,KAAK,EAAE;wBACT;sBACF,CAAE;sBAAAV,QAAA,eAEF9E,OAAA,CAACrB,QAAQ;wBAACqI,QAAQ,EAAC;sBAAO;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVlF,OAAA,CAAChC,OAAO;oBAAC4J,KAAK,EAAC,sBAAsB;oBAAA9C,QAAA,eACnC9E,OAAA,CAACxC,UAAU;sBACTmK,IAAI,EAAC,OAAO;sBACZhC,OAAO,EAAEA,CAAA,KAAM5B,wBAAwB,CAACZ,YAAY,CAACI,eAAe,CAAE;sBACtE4B,EAAE,EAAE;wBACF,SAAS,EAAE;0BACT0B,eAAe,EAAE,aAAa;0BAC9BrB,KAAK,EAAE;wBACT;sBACF,CAAE;sBAAAV,QAAA,eAEF9E,OAAA,CAACnB,UAAU;wBAACmI,QAAQ,EAAC;sBAAO;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC,eAEnBlF,OAAA,CAACzB,gBAAgB;cAAC4G,EAAE,EAAE;gBAAE0C,EAAE,EAAE;cAAE,CAAE;cAAA/C,QAAA,gBAC9B9E,OAAA,CAAClD,UAAU;gBAACwI,OAAO,EAAC,WAAW;gBAAC2B,YAAY;gBAAC9B,EAAE,EAAE;kBAAEI,UAAU,EAAE,GAAG;kBAAEF,EAAE,EAAE;gBAAE,CAAE;gBAAAP,QAAA,EAAC;cAE7E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAEX,CAAC7B,KAAK,CAACC,OAAO,CAACnC,sBAAsB,CAACgC,YAAY,CAACI,eAAe,CAAC,CAAC,IAAIpC,sBAAsB,CAACgC,YAAY,CAACI,eAAe,CAAC,CAACoD,MAAM,KAAK,CAAC,gBACzI3G,OAAA,CAACrD,GAAG;gBACFwI,EAAE,EAAE;kBACFC,CAAC,EAAE,CAAC;kBACJiB,SAAS,EAAE,QAAQ;kBACnBQ,eAAe,EAAE,SAAS;kBAC1BiB,YAAY,EAAE,CAAC;kBACfhB,MAAM,EAAE,YAAY;kBACpBC,WAAW,EAAE;gBACf,CAAE;gBAAAjC,QAAA,eAEF9E,OAAA,CAAClD,UAAU;kBAACwI,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,gBAAgB;kBAAAV,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,gBAENlF,OAAA,CAAC9B,IAAI;gBAAC6J,KAAK;gBAAAjD,QAAA,EACRzB,KAAK,CAACC,OAAO,CAACnC,sBAAsB,CAACgC,YAAY,CAACI,eAAe,CAAC,CAAC,IAAIpC,sBAAsB,CAACgC,YAAY,CAACI,eAAe,CAAC,CAAC2D,GAAG,CAAEc,OAAO,iBACvIhI,OAAA,CAAC7B,QAAQ;kBAA8B8J,OAAO;kBAAAnD,QAAA,eAC5C9E,OAAA,CAAC5B,YAAY;oBACX8J,OAAO,eACLlI,OAAA,CAACrD,GAAG;sBAAC+H,OAAO,EAAC,MAAM;sBAACE,UAAU,EAAC,QAAQ;sBAACa,GAAG,EAAE,CAAE;sBAAAX,QAAA,gBAC7C9E,OAAA,CAAClD,UAAU;wBAACwI,OAAO,EAAC,OAAO;wBAACC,UAAU,EAAC,MAAM;wBAAAT,QAAA,EAC1CkD,OAAO,CAACG;sBAAc;wBAAApD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CAAC,eACblF,OAAA,CAACzC,IAAI;wBACHmK,KAAK,EAAEtD,mBAAmB,CAAC4D,OAAO,CAACI,YAAY,CAAE;wBACjDT,IAAI,EAAC,OAAO;wBACZrC,OAAO,EAAC;sBAAU;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACFlF,OAAA,CAACzC,IAAI;wBACHmK,KAAK,EAAEM,OAAO,CAACxD,KAAK,IAAI,QAAS;wBACjCmD,IAAI,EAAC,OAAO;wBACZnC,KAAK,EAAEjB,aAAa,CAACyD,OAAO,CAACxD,KAAK;sBAAE;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;oBACDmD,SAAS,eACPrI,OAAA,CAAClD,UAAU;sBAACwI,OAAO,EAAC,OAAO;sBAACE,KAAK,EAAC,eAAe;sBAAAV,QAAA,GAC9CkD,OAAO,CAACM,WAAW,IAAI,qBAAqB,EAC5CN,OAAO,CAACO,cAAc,IAAI,cAAc,IAAIC,IAAI,CAACR,OAAO,CAACO,cAAc,CAAC,CAACE,kBAAkB,CAAC,CAAC,EAAE;oBAAA;sBAAA1D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF;kBACb;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC,GAzBW8C,OAAO,CAACG,cAAc;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0B3B,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC;UAAA,GA3Id/B,YAAY,CAACI,eAAe;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4IxB,CACZ;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlF,OAAA,CAACvC,MAAM;MACLiL,IAAI,EAAErH,sBAAuB;MAC7BsH,OAAO,EAAEjF,6BAA8B;MACvCkF,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV3D,EAAE,EAAE;UAAE2C,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAhD,QAAA,gBAEF9E,OAAA,CAACtC,WAAW;QAACyH,EAAE,EAAE;UAAE4D,EAAE,EAAE;QAAE,CAAE;QAAAjE,QAAA,eACzB9E,OAAA,CAAClD,UAAU;UAACwI,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9CvD,sBAAsB,KAAK,QAAQ,GAAG,wBAAwB,GAAG;QAAuB;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdlF,OAAA,CAACrC,aAAa;QAAAmH,QAAA,eACZ9E,OAAA,CAACrD,GAAG;UAACwI,EAAE,EAAE;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBACjB9E,OAAA,CAACnC,SAAS;YACRgL,SAAS;YACTnB,KAAK,EAAC,mBAAmB;YACzBsB,KAAK,EAAErH,oBAAoB,CAACE,iBAAkB;YAC9CoH,QAAQ,EAAG1B,CAAC,IAAK3F,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEE,iBAAiB,EAAE0F,CAAC,CAAC2B,MAAM,CAACF;YAAM,CAAC,CAAE;YACzGG,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACR9D,OAAO,EAAC,UAAU;YAClBH,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFlF,OAAA,CAACnC,SAAS;YACRgL,SAAS;YACTnB,KAAK,EAAC,OAAO;YACb2B,IAAI,EAAC,OAAO;YACZL,KAAK,EAAErH,oBAAoB,CAACG,KAAM;YAClCmH,QAAQ,EAAG1B,CAAC,IAAK3F,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEG,KAAK,EAAEyF,CAAC,CAAC2B,MAAM,CAACF;YAAM,CAAC,CAAE;YAC7FG,MAAM,EAAC,QAAQ;YACf7D,OAAO,EAAC,UAAU;YAClBgE,UAAU,EAAC,uDAAuD;YAClEnE,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFlF,OAAA,CAACnC,SAAS;YACRgL,SAAS;YACTnB,KAAK,EAAC,UAAU;YAChBsB,KAAK,EAAErH,oBAAoB,CAACI,QAAS;YACrCkH,QAAQ,EAAG1B,CAAC,IAAK3F,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEI,QAAQ,EAAEwF,CAAC,CAAC2B,MAAM,CAACF;YAAM,CAAC,CAAE;YAChGG,MAAM,EAAC,QAAQ;YACf7D,OAAO,EAAC,UAAU;YAClBgE,UAAU,EAAC;UAA+C;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBlF,OAAA,CAACpC,aAAa;QAACuH,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEyC,EAAE,EAAE;QAAE,CAAE;QAAA/C,QAAA,gBACjC9E,OAAA,CAACjD,MAAM;UACL4I,OAAO,EAAEjC,6BAA8B;UACvCyB,EAAE,EAAE;YAAES,aAAa,EAAE;UAAO,CAAE;UAAAd,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlF,OAAA,CAACjD,MAAM;UACL4I,OAAO,EAAEhC,wBAAyB;UAClC2B,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACFS,aAAa,EAAE,MAAM;YACrBL,UAAU,EAAE,GAAG;YACfM,EAAE,EAAE;UACN,CAAE;UAAAf,QAAA,EAEDvD,sBAAsB,KAAK,QAAQ,GAAG,MAAM,GAAG;QAAO;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTlF,OAAA,CAACF,kBAAkB;MACjBI,UAAU,EAAEA,UAAW;MACvBwI,IAAI,EAAE7H,eAAgB;MACtB8H,OAAO,EAAEA,CAAA,KAAM7H,kBAAkB,CAAC,KAAK,CAAE;MACzCyI,SAAS,EAAEA,CAAA,KAAM;QACftH,WAAW,CAAC,CAAC;QACbC,eAAe,CAAC,CAAC;QACjBF,gBAAgB,CAAC,CAAC;QAClBlB,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IAAE;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC9E,EAAA,CApkBIH,wBAAwB;AAAAuJ,EAAA,GAAxBvJ,wBAAwB;AAskB9B,eAAeA,wBAAwB;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}