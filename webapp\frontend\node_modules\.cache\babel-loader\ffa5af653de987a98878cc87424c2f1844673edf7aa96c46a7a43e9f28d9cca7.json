{"ast": null, "code": "import { monthsInQuarter } from \"./constants.mjs\";\n\n/**\n * @name monthsToQuarters\n * @category Conversion Helpers\n * @summary Convert number of months to quarters.\n *\n * @description\n * Convert a number of months to a full number of quarters.\n *\n * @param months - The number of months to be converted.\n *\n * @returns The number of months converted in quarters\n *\n * @example\n * // Convert 6 months to quarters:\n * const result = monthsToQuarters(6)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = monthsToQuarters(7)\n * //=> 2\n */\nexport function monthsToQuarters(months) {\n  const quarters = months / monthsInQuarter;\n  return Math.trunc(quarters);\n}\n\n// Fallback for modularized imports:\nexport default monthsToQuarters;", "map": {"version": 3, "names": ["monthsInQuarter", "monthsToQuarters", "months", "quarters", "Math", "trunc"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/monthsToQuarters.mjs"], "sourcesContent": ["import { monthsInQuarter } from \"./constants.mjs\";\n\n/**\n * @name monthsToQuarters\n * @category Conversion Helpers\n * @summary Convert number of months to quarters.\n *\n * @description\n * Convert a number of months to a full number of quarters.\n *\n * @param months - The number of months to be converted.\n *\n * @returns The number of months converted in quarters\n *\n * @example\n * // Convert 6 months to quarters:\n * const result = monthsToQuarters(6)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = monthsToQuarters(7)\n * //=> 2\n */\nexport function monthsToQuarters(months) {\n  const quarters = months / monthsInQuarter;\n  return Math.trunc(quarters);\n}\n\n// Fallback for modularized imports:\nexport default monthsToQuarters;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,iBAAiB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EACvC,MAAMC,QAAQ,GAAGD,MAAM,GAAGF,eAAe;EACzC,OAAOI,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC;AAC7B;;AAEA;AACA,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}