{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Box, Paper, Typography, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip, Dialog, DialogTitle, DialogContent, DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, List, ListItem, ListItemText, ListItemSecondaryAction } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Cancel as CancelIcon, Warning as WarningIcon, Info as InfoIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null);\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response || loadError.code === 'ECONNABORTED' || loadError.message && loadError.message.includes('Network Error')) {\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(`${API_URL}/cavi/${cantiereId}`, {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 30000 // 30 secondi\n            });\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Se c'è un cavo selezionato, carica le bobine compatibili usando l'endpoint dedicato\n      if (selectedCavo && selectedCavo.tipologia !== undefined && selectedCavo.n_conduttori !== undefined && selectedCavo.sezione !== undefined) {\n        // Log dettagliato per debug\n        console.log('Valori del cavo selezionato:', {\n          tipologia: selectedCavo.tipologia,\n          n_conduttori: selectedCavo.n_conduttori,\n          sezione: selectedCavo.sezione,\n          tipi: {\n            tipologia: typeof selectedCavo.tipologia,\n            n_conduttori: typeof selectedCavo.n_conduttori,\n            sezione: typeof selectedCavo.sezione\n          }\n        });\n        console.log('Caricamento bobine compatibili per il cavo selezionato...');\n        try {\n          // Usa il nuovo metodo per ottenere le bobine compatibili\n          const bobineCompatibili = await parcoCaviService.getBobineCompatibili(cantiereId, selectedCavo.tipologia, selectedCavo.n_conduttori, selectedCavo.sezione);\n          if (bobineCompatibili && bobineCompatibili.length > 0) {\n            console.log(`Trovate ${bobineCompatibili.length} bobine compatibili`);\n            // Ordina le bobine per metri residui (decrescente)\n            bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n            setBobine(bobineCompatibili);\n            return;\n          } else {\n            console.log('Nessuna bobina compatibile trovata, carico tutte le bobine disponibili');\n          }\n        } catch (compatError) {\n          console.error('Errore nel caricamento delle bobine compatibili:', compatError);\n          // In caso di errore, continua con il metodo standard\n        }\n      }\n\n      // Metodo standard: carica tutte le bobine e filtra manualmente\n      console.log('Caricamento di tutte le bobine disponibili...');\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina => (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') && bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata');\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia !== undefined && selectedCavo.n_conduttori !== undefined && selectedCavo.sezione !== undefined) {\n          // Log dettagliato per debug del filtro manuale\n          console.log('Filtro manuale - Valori del cavo selezionato:', {\n            tipologia: selectedCavo.tipologia,\n            n_conduttori: selectedCavo.n_conduttori,\n            sezione: selectedCavo.sezione,\n            tipi: {\n              tipologia: typeof selectedCavo.tipologia,\n              n_conduttori: typeof selectedCavo.n_conduttori,\n              sezione: typeof selectedCavo.sezione\n            }\n          });\n          // Aggiungi log per debug\n          console.log('Filtraggio bobine compatibili per cavo:', {\n            tipologia: selectedCavo.tipologia,\n            n_conduttori: selectedCavo.n_conduttori,\n            sezione: selectedCavo.sezione\n          });\n\n          // Gestisci valori null o vuoti in modo più robusto\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina => {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoConduttori = String(selectedCavo.n_conduttori || '0');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n            const bobinaTipologia = String(bobina.tipologia || '');\n            const bobinaConduttori = String(bobina.n_conduttori || '0');\n            const bobinaSezione = String(bobina.sezione || '0');\n\n            // Log per debug\n            console.log(`Confronto bobina ${bobina.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              n_conduttori: `${bobinaConduttori} === ${cavoConduttori}`,\n              sezione: `${bobinaSezione} === ${cavoSezione}`\n            });\n            return bobinaTipologia === cavoTipologia && bobinaConduttori === cavoConduttori && bobinaSezione === cavoSezione;\n          });\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n            console.log(`Filtrate ${bobineCompatibili.length} bobine compatibili`);\n          } else {\n            console.log('Nessuna bobina compatibile trovata, mostro tutte le bobine disponibili');\n            console.log('Cavo selezionato:', selectedCavo.tipologia, String(selectedCavo.n_conduttori), String(selectedCavo.sezione));\n            console.log('Bobine disponibili:', bobineUtilizzabili.map(b => [b.id_bobina, b.tipologia, String(b.n_conduttori), String(b.sezione)]));\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo => cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase()));\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo => cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase());\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || exactMatch.metratura_reale && exactMatch.metratura_reale > 0) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      forceOver = false;\n\n      // Se si usa BOBINA_VUOTA, imposta sempre forceOver a true\n      if (idBobina === 'BOBINA_VUOTA') {\n        forceOver = true;\n        console.log('Forzando operazione per BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        // Anche in questo caso forziamo l'operazione\n        forceOver = true;\n        console.log(`Forzando operazione per metri posati (${metriPosati}) > metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n              await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, forceOver);\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n              if (error.response) {\n                var _error$response$data;\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message;\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, forceOver);\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response) {\n        var _error$response$data2;\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.detail) || error.message;\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 961,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Cerca cavo per ID\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 967,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 9,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavo\",\n              variant: \"outlined\",\n              value: cavoIdInput,\n              onChange: e => setCavoIdInput(e.target.value),\n              placeholder: \"Inserisci l'ID del cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 972,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 971,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: handleSearchCavoById,\n              disabled: caviLoading || !cavoIdInput.trim(),\n              startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 988,\n                columnNumber: 42\n              }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 988,\n                columnNumber: 75\n              }, this),\n              children: \"Cerca\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 982,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 981,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 970,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 966,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 998,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1004,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1003,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Non ci sono cavi disponibili da installare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1007,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '400px',\n            overflow: 'auto'\n          },\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1018,\n                    columnNumber: 27\n                  }, this), isCableSpare(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"SPARE\",\n                    color: \"error\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1020,\n                    columnNumber: 29\n                  }, this) : isCableInstalled(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"Installato\",\n                    color: \"success\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1027,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: getCableStateColor(cavo.stato_installazione),\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1034,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1017,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [cavo.tipologia || 'N/A', \" - \", cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1045,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1048,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', \" - A: \", cavo.ubicazione_arrivo || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1049,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1052,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A', \" - Metri posati: \", cavo.metratura_reale || '0']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1053,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  edge: \"end\",\n                  onClick: e => {\n                    e.stopPropagation(); // Prevent triggering the ListItem click\n                    setSelectedCavo(cavo);\n                    setShowCavoDetailsDialog(true);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1065,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1060,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1059,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1069,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1013,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1011,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 997,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 960,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1085,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n        cavo: selectedCavo,\n        compact: true,\n        title: \"Dettagli del cavo selezionato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1089,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold'\n          },\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1096,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                bgcolor: '#f5f5f5',\n                borderRadius: 1,\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold',\n                  color: 'primary.main'\n                },\n                children: \"Informazioni cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1104,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 1,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium'\n                    },\n                    children: \"Metri teorici:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1109,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1108,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1112,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1111,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium'\n                    },\n                    children: \"Stato attuale:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1115,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1114,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedCavo.stato_installazione || 'N/D',\n                    size: \"small\",\n                    color: getCableStateColor(selectedCavo.stato_installazione),\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1118,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1117,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1107,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                bgcolor: '#f5f5f5',\n                borderRadius: 1,\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold',\n                  color: 'secondary.main'\n                },\n                children: \"Informazioni bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1131,\n                columnNumber: 17\n              }, this), formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                return bobina ? /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"ID Bobina:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1139,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1138,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: getBobinaNumber(bobina.id_bobina)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1142,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1141,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1145,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1144,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1148,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1147,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1151,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1150,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: bobina.stato_bobina || 'N/D',\n                      size: \"small\",\n                      color: getReelStateColor(bobina.stato_bobina),\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1154,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1153,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1137,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Bobina non trovata\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1163,\n                  columnNumber: 21\n                }, this);\n              })() : /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: formData.id_bobina === 'BOBINA_VUOTA' ? \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" : \"Nessuna bobina selezionata\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1166,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1130,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            fullWidth: true,\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1176,\n          columnNumber: 11\n        }, this), formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: formWarnings.metri_posati\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1199,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1095,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1084,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = e => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo) {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoConduttori = String(selectedCavo.n_conduttori || '0');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n            const bobinaTipologia = String(bobinaEsistente.tipologia || '');\n            const bobinaConduttori = String(bobinaEsistente.n_conduttori || '0');\n            const bobinaSezione = String(bobinaEsistente.sezione || '0');\n\n            // Log per debug\n            console.log(`Verifica compatibilità bobina ${bobinaEsistente.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              n_conduttori: `${bobinaConduttori} === ${cavoConduttori}`,\n              sezione: `${bobinaSezione} === ${cavoSezione}`\n            });\n            if (bobinaTipologia !== cavoTipologia || bobinaConduttori !== cavoConduttori || bobinaSezione !== cavoSezione) {\n              // Mostra il dialogo per bobine incompatibili\n              setIncompatibleReel(bobinaEsistente);\n              setShowIncompatibleReelDialog(true);\n              return;\n            }\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Associa bobina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          paragraph: true,\n          children: \"Seleziona una bobina da associare al cavo. \\xC8 necessario associare sempre una bobina, anche utilizzando l'opzione \\\"BOBINA VUOTA\\\" se non si desidera associare una bobina specifica.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1335,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1341,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1340,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"Inserimento diretto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1348,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                size: \"small\",\n                fullWidth: true,\n                label: \"Numero bobina\",\n                variant: \"outlined\",\n                placeholder: \"Solo il numero (Y)\",\n                helperText: formErrors.id_bobina_input || \"Inserisci solo il numero della bobina\",\n                error: !!formErrors.id_bobina_input,\n                onBlur: handleBobinaNumberInput,\n                sx: {\n                  mb: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1351,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [\"ID Bobina: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: formData.id_bobina || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1363,\n                  columnNumber: 32\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1362,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1347,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"Selezione dalla lista\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1369,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  sx: {\n                    color: 'primary.main',\n                    fontWeight: 'bold',\n                    mb: 1\n                  },\n                  children: selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1373,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  size: \"small\",\n                  error: !!formErrors.id_bobina,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    id: \"bobina-select-label\",\n                    children: \"Seleziona bobina\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1378,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    labelId: \"bobina-select-label\",\n                    id: \"bobina-select\",\n                    name: \"id_bobina\",\n                    value: formData.id_bobina,\n                    label: \"Seleziona bobina\",\n                    onChange: handleFormChange,\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"BOBINA_VUOTA\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"BOBINA VUOTA\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1388,\n                        columnNumber: 27\n                      }, this), \" (nessuna bobina associata)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1387,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1390,\n                      columnNumber: 25\n                    }, this), bobine.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                      component: \"li\",\n                      sx: {\n                        p: 1,\n                        bgcolor: 'background.paper'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          color: 'success.main'\n                        },\n                        children: [bobine.length, \" bobine compatibili trovate\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1393,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1392,\n                      columnNumber: 27\n                    }, this) : null, bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: bobina.id_bobina,\n                      disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                      sx: {\n                        '&.Mui-selected': {\n                          bgcolor: 'success.light'\n                        },\n                        '&.Mui-selected:hover': {\n                          bgcolor: 'success.light'\n                        },\n                        bgcolor: selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione) ? 'rgba(76, 175, 80, 0.08)' : 'inherit'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          flexDirection: 'column',\n                          width: '100%'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center',\n                            width: '100%'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            sx: {\n                              fontWeight: 'bold'\n                            },\n                            children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A']\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1415,\n                            columnNumber: 33\n                          }, this), selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione) && /*#__PURE__*/_jsxDEV(Chip, {\n                            size: \"small\",\n                            label: \"Compatibile\",\n                            color: \"success\",\n                            variant: \"outlined\",\n                            sx: {\n                              height: 20,\n                              fontSize: '0.6rem'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1422,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1414,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            width: '100%'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            children: [bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1432,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            sx: {\n                              fontWeight: 'bold',\n                              color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                            },\n                            children: [bobina.metri_residui || 0, \" m disponibili\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1435,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1431,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1413,\n                        columnNumber: 29\n                      }, this)\n                    }, bobina.id_bobina, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1399,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1379,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                    children: formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1443,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1377,\n                  columnNumber: 21\n                }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n                  severity: \"warning\",\n                  sx: {\n                    mt: 2,\n                    fontSize: '0.8rem'\n                  },\n                  children: \"Nessuna bobina compatibile trovata. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1449,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1372,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1368,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1345,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Nota\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1459,\n                columnNumber: 19\n              }, this), \": Se selezioni \\\"BOBINA VUOTA\\\", potrai associare una bobina specifica in un secondo momento.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1458,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1457,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1344,\n          columnNumber: 13\n        }, this), !bobineLoading && formData.id_bobina && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: 'background.paper',\n            borderRadius: 1,\n            border: '1px solid #e0e0e0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Dettagli bobina selezionata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1468,\n            columnNumber: 15\n          }, this), (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            if (bobina) {\n              return /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Numero:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1478,\n                      columnNumber: 27\n                    }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1477,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tipologia:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1481,\n                      columnNumber: 27\n                    }, this), \" \", bobina.tipologia || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1480,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Conduttori:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1484,\n                      columnNumber: 27\n                    }, this), \" \", bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1483,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1476,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri totali:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1489,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_totali || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1488,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1492,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1491,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1495,\n                      columnNumber: 27\n                    }, this), \" \", bobina.stato_bobina || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1494,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1487,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1475,\n                columnNumber: 21\n              }, this);\n            }\n            return /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"error\",\n              children: \"Bobina non trovata nel database\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1502,\n              columnNumber: 19\n            }, this);\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1467,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1511,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1334,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1329,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1540,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1545,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo,\n          compact: true,\n          title: \"Dettagli del cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1550,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Informazioni sull'operazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1558,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1564,\n                  columnNumber: 19\n                }, this), \" \", formData.metri_posati, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1563,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato Installazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1567,\n                  columnNumber: 19\n                }, this), \" \", statoInstallazione]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1566,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1562,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina Associata:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1572,\n                  columnNumber: 19\n                }, this), \" \", numeroBobina]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1571,\n                columnNumber: 17\n              }, this), bobinaInfo && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Residui Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1576,\n                  columnNumber: 21\n                }, this), \" \", bobinaInfo.metri_residui, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1575,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1570,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1561,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1557,\n          columnNumber: 11\n        }, this), bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1585,\n            columnNumber: 15\n          }, this), \" I metri posati (\", formData.metri_posati, \"m) superano i metri residui della bobina (\", bobinaInfo.metri_residui, \"m). Questo porter\\xE0 la bobina in stato OVER.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1584,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1590,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1544,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1539,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      // Seleziona Cavo\n      case 1:\n        return renderStep3();\n      // Associa Bobina\n      case 2:\n        return renderStep2();\n      // Inserisci Metri\n      case 3:\n        return renderStep4();\n      // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReel(null);\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    if (!selectedCavo || !incompatibleReel) return;\n    try {\n      setLoading(true);\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoToMatchReel(cantiereId, selectedCavo.id_cavo, incompatibleReel);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, selectedCavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: incompatibleReel.id_bobina\n      });\n      onSuccess(`Caratteristiche del cavo ${selectedCavo.id_cavo} aggiornate per corrispondere alla bobina ${incompatibleReel.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Cerca cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1687,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 9,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Cavo\",\n            variant: \"outlined\",\n            value: cavoIdInput,\n            onChange: e => setCavoIdInput(e.target.value),\n            placeholder: \"Inserisci l'ID del cavo o parte di esso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1692,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1691,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSearchCavoById,\n            disabled: caviLoading || !cavoIdInput.trim(),\n            startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1708,\n              columnNumber: 40\n            }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1708,\n              columnNumber: 73\n            }, this),\n            children: \"Cerca\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1702,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1701,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1690,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1686,\n      columnNumber: 7\n    }, this), showSearchResults && searchResults.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Risultati della ricerca\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1719,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                bgcolor: '#f5f5f5'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1726,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1727,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Conduttori\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1728,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1729,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1730,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1731,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1732,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1725,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1724,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: searchResults.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1738,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1739,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1740,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1741,\n                  columnNumber: 71\n                }, this), \"A: \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1741,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1742,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: cavo.stato_installazione || 'N/D',\n                  size: \"small\",\n                  color: getCableStateColor(cavo.stato_installazione),\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1744,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1743,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  variant: \"contained\",\n                  color: \"primary\",\n                  onClick: () => handleCavoSelect(cavo),\n                  disabled: isCableInstalled(cavo),\n                  children: \"Seleziona\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1752,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1751,\n                columnNumber: 21\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1737,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1735,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1723,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1722,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1718,\n      columnNumber: 9\n    }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserimento metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1773,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          bgcolor: '#f5f5f5',\n          borderRadius: 1,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold',\n            color: 'primary.main'\n          },\n          children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1779,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1784,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1784,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Conduttori:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1785,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.n_conduttori || 'N/A', \" x \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1785,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1786,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1786,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1783,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione partenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1789,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1789,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione arrivo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1790,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1790,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1792,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: selectedCavo.stato_installazione || 'N/D',\n                size: \"small\",\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                variant: \"outlined\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1793,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1791,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1788,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1782,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1778,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1808,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            fullWidth: true,\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1811,\n            columnNumber: 15\n          }, this), formWarnings.metri_posati && !formErrors.metri_posati && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: formWarnings.metri_posati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1828,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1807,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Associa bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1836,\n            columnNumber: 15\n          }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'center',\n              my: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1841,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1840,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              sx: {\n                color: 'primary.main',\n                fontWeight: 'bold',\n                mb: 1\n              },\n              children: selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1845,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              error: !!formErrors.id_bobina,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"bobina-select-label\",\n                children: \"Seleziona bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1850,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"bobina-select-label\",\n                id: \"bobina-select\",\n                name: \"id_bobina\",\n                value: formData.id_bobina,\n                label: \"Seleziona bobina\",\n                onChange: handleFormChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"BOBINA_VUOTA\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"BOBINA VUOTA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1860,\n                    columnNumber: 25\n                  }, this), \" (nessuna bobina associata)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1859,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1862,\n                  columnNumber: 23\n                }, this), bobine.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"li\",\n                  sx: {\n                    p: 1,\n                    bgcolor: 'background.paper'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      color: 'success.main'\n                    },\n                    children: [bobine.length, \" bobine compatibili trovate\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1865,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1864,\n                  columnNumber: 25\n                }, this) : null, bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: bobina.id_bobina,\n                  disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                  sx: {\n                    '&.Mui-selected': {\n                      bgcolor: 'success.light'\n                    },\n                    '&.Mui-selected:hover': {\n                      bgcolor: 'success.light'\n                    },\n                    bgcolor: selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione) ? 'rgba(76, 175, 80, 0.08)' : 'inherit'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      flexDirection: 'column',\n                      width: '100%'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        width: '100%'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontWeight: 'bold'\n                        },\n                        children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1887,\n                        columnNumber: 31\n                      }, this), selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) && String(bobina.sezione) === String(selectedCavo.sezione) && /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: \"Compatibile\",\n                        color: \"success\",\n                        variant: \"outlined\",\n                        sx: {\n                          height: 20,\n                          fontSize: '0.6rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1894,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1886,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        width: '100%'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        children: [bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1904,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                        },\n                        children: [bobina.metri_residui || 0, \" m disponibili\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1907,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1903,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1885,\n                    columnNumber: 27\n                  }, this)\n                }, bobina.id_bobina, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1871,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1851,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1915,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1849,\n              columnNumber: 19\n            }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mt: 2,\n                fontSize: '0.8rem'\n              },\n              children: \"Nessuna bobina compatibile trovata. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1921,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1844,\n            columnNumber: 17\n          }, this), !bobineLoading && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            if (bobina) {\n              return /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 2,\n                  p: 2,\n                  bgcolor: '#f5f5f5',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Bobina:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1934,\n                    columnNumber: 51\n                  }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1934,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Metri residui:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1935,\n                    columnNumber: 51\n                  }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1935,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Stato:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1937,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: bobina.stato_bobina || 'N/D',\n                    size: \"small\",\n                    color: getReelStateColor(bobina.stato_bobina),\n                    variant: \"outlined\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1938,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1936,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1933,\n                columnNumber: 21\n              }, this);\n            }\n            return null;\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1835,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1805,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          onClick: () => {\n            setSelectedCavo(null);\n            setFormData({\n              id_cavo: '',\n              metri_posati: '',\n              id_bobina: ''\n            });\n          },\n          startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1966,\n            columnNumber: 26\n          }, this),\n          disabled: loading,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1955,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSubmit,\n          endIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1976,\n            columnNumber: 24\n          }, this),\n          disabled: loading || !formData.metri_posati || !formData.id_bobina,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1979,\n            columnNumber: 26\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1972,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1954,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1772,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showConfirmDialog,\n      onClose: () => setShowConfirmDialog(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1989,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: confirmDialogProps.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1990,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1988,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1987,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mt: 2\n          },\n          children: confirmDialogProps.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1994,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1993,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowConfirmDialog(false),\n          color: \"secondary\",\n          variant: \"outlined\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1999,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setShowConfirmDialog(false);\n            confirmDialogProps.onConfirm();\n          },\n          color: \"primary\",\n          variant: \"contained\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2002,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1998,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1986,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2020,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavo gi\\xE0 posato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2021,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2019,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2018,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: alreadyLaidCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: alreadyLaidCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2028,\n              columnNumber: 25\n            }, this), \" risulta gi\\xE0 posato (\", alreadyLaidCavo.metratura_reale || 0, \"m).\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2027,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: \"Puoi scegliere di:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2030,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"ul\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata al cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2034,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2035,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2036,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2033,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2026,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2024,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2042,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2046,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2049,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2045,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2041,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2017,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: handleCloseIncompatibleReelDialog,\n      cavo: selectedCavo,\n      bobina: incompatibleReel,\n      onUpdateCavo: handleUpdateCavoToMatchReel,\n      onSelectAnotherReel: handleSelectAnotherReel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2057,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2075,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Dettagli Cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2076,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2074,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2073,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2080,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2079,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          color: \"primary\",\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2083,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2082,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2067,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1684,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"hsuyd/WWnSfyS7dylwwvyUS2Ql4=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Search", "SearchIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "useNavigate", "caviService", "axiosInstance", "IncompatibleReelDialog", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchResults", "setSearchResults", "showSearchResults", "setShowSearchResults", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "activeStep", "setActiveStep", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReel", "setIncompatibleReel", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "alreadyLaidCavo", "setAlreadyLaidCavo", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "loadBobine", "getBobinaNumber", "idBobina", "includes", "split", "loadCavi", "console", "log", "caviData", "get<PERSON><PERSON>", "length", "loadError", "error", "isNetworkError", "response", "code", "message", "Promise", "resolve", "setTimeout", "token", "localStorage", "getItem", "API_URL", "defaults", "baseURL", "retryResponse", "get", "headers", "timeout", "data", "retryError", "errorMessage", "detail", "tipologia", "undefined", "n_conduttori", "sezione", "tipi", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "getBobineCompatibili", "sort", "a", "b", "metri_residui", "compatError", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "bobina", "stato_bobina", "cavoTipologia", "String", "cavoConduttori", "cavoSezione", "bobinaTipologia", "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bobinaSezione", "map", "handleSearchCavoById", "trim", "filteredCavi", "cavo", "toLowerCase", "exactMatch", "find", "stato_installazione", "metratura_reale", "modificato_manualmente", "handleCavoSelect", "status", "window", "confirm", "reactivateSpare", "then", "updatedCavo", "catch", "cavoId", "handleFormChange", "e", "name", "value", "target", "validateField", "warning", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "prev", "notificationShown", "setNotificationShown", "showConfirmDialog", "setShowConfirmDialog", "confirmDialogProps", "setConfirmDialogProps", "title", "onConfirm", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "handleNext", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "metriTeorici", "handleSubmit", "statoInstallazione", "forceOver", "confirmMessage", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "success", "_error$response$data", "request", "_error$response$data2", "renderStep1", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "container", "spacing", "alignItems", "item", "xs", "fullWidth", "label", "onChange", "placeholder", "color", "onClick", "disabled", "startIcon", "size", "display", "justifyContent", "my", "severity", "maxHeight", "overflow", "button", "primary", "ml", "secondary", "component", "ubicazione_partenza", "ubicazione_arrivo", "edge", "stopPropagation", "renderStep2", "compact", "fontWeight", "md", "bgcolor", "borderRadius", "height", "mt", "type", "helperText", "FormHelperTextProps", "renderStep3", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "handleBobinaNumberInput", "id_bobina_input", "idBobinaCompleto", "<PERSON><PERSON><PERSON>", "paragraph", "onBlur", "id", "labelId", "flexDirection", "width", "fontSize", "border", "metri_totali", "renderStep4", "bobinaInfo", "getStepContent", "step", "handleCloseAlreadyLaidDialog", "handleModifyReel", "handleSelectAnotherCable", "handleCloseIncompatibleReelDialog", "handleUpdateCavoToMatchReel", "updateCavoToMatchReel", "getCavoById", "handleSelectAnotherReel", "endIcon", "open", "onClose", "max<PERSON><PERSON><PERSON>", "gap", "autoFocus", "mr", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null);\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response ||\n            loadError.code === 'ECONNABORTED' ||\n            (loadError.message && loadError.message.includes('Network Error'))) {\n\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(\n              `${API_URL}/cavi/${cantiereId}`,\n              {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 30000 // 30 secondi\n              }\n            );\n\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Se c'è un cavo selezionato, carica le bobine compatibili usando l'endpoint dedicato\n      if (selectedCavo && selectedCavo.tipologia !== undefined && selectedCavo.n_conduttori !== undefined && selectedCavo.sezione !== undefined) {\n        // Log dettagliato per debug\n        console.log('Valori del cavo selezionato:', {\n          tipologia: selectedCavo.tipologia,\n          n_conduttori: selectedCavo.n_conduttori,\n          sezione: selectedCavo.sezione,\n          tipi: {\n            tipologia: typeof selectedCavo.tipologia,\n            n_conduttori: typeof selectedCavo.n_conduttori,\n            sezione: typeof selectedCavo.sezione\n          }\n        });\n        console.log('Caricamento bobine compatibili per il cavo selezionato...');\n        try {\n          // Usa il nuovo metodo per ottenere le bobine compatibili\n          const bobineCompatibili = await parcoCaviService.getBobineCompatibili(\n            cantiereId,\n            selectedCavo.tipologia,\n            selectedCavo.n_conduttori,\n            selectedCavo.sezione\n          );\n\n          if (bobineCompatibili && bobineCompatibili.length > 0) {\n            console.log(`Trovate ${bobineCompatibili.length} bobine compatibili`);\n            // Ordina le bobine per metri residui (decrescente)\n            bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n            setBobine(bobineCompatibili);\n            return;\n          } else {\n            console.log('Nessuna bobina compatibile trovata, carico tutte le bobine disponibili');\n          }\n        } catch (compatError) {\n          console.error('Errore nel caricamento delle bobine compatibili:', compatError);\n          // In caso di errore, continua con il metodo standard\n        }\n      }\n\n      // Metodo standard: carica tutte le bobine e filtra manualmente\n      console.log('Caricamento di tutte le bobine disponibili...');\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina =>\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') &&\n        bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata'\n      );\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia !== undefined && selectedCavo.n_conduttori !== undefined && selectedCavo.sezione !== undefined) {\n          // Log dettagliato per debug del filtro manuale\n          console.log('Filtro manuale - Valori del cavo selezionato:', {\n            tipologia: selectedCavo.tipologia,\n            n_conduttori: selectedCavo.n_conduttori,\n            sezione: selectedCavo.sezione,\n            tipi: {\n              tipologia: typeof selectedCavo.tipologia,\n              n_conduttori: typeof selectedCavo.n_conduttori,\n              sezione: typeof selectedCavo.sezione\n            }\n          });\n          // Aggiungi log per debug\n          console.log('Filtraggio bobine compatibili per cavo:', {\n            tipologia: selectedCavo.tipologia,\n            n_conduttori: selectedCavo.n_conduttori,\n            sezione: selectedCavo.sezione\n          });\n\n          // Gestisci valori null o vuoti in modo più robusto\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina => {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoConduttori = String(selectedCavo.n_conduttori || '0');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n\n            const bobinaTipologia = String(bobina.tipologia || '');\n            const bobinaConduttori = String(bobina.n_conduttori || '0');\n            const bobinaSezione = String(bobina.sezione || '0');\n\n            // Log per debug\n            console.log(`Confronto bobina ${bobina.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              n_conduttori: `${bobinaConduttori} === ${cavoConduttori}`,\n              sezione: `${bobinaSezione} === ${cavoSezione}`\n            });\n\n            return bobinaTipologia === cavoTipologia &&\n                   bobinaConduttori === cavoConduttori &&\n                   bobinaSezione === cavoSezione;\n          });\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n            console.log(`Filtrate ${bobineCompatibili.length} bobine compatibili`);\n          } else {\n            console.log('Nessuna bobina compatibile trovata, mostro tutte le bobine disponibili');\n            console.log('Cavo selezionato:', selectedCavo.tipologia, String(selectedCavo.n_conduttori), String(selectedCavo.sezione));\n            console.log('Bobine disponibili:', bobineUtilizzabili.map(b => [b.id_bobina, b.tipologia, String(b.n_conduttori), String(b.sezione)]));\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase())\n      );\n\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo =>\n        cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase()\n      );\n\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || (exactMatch.metratura_reale && exactMatch.metratura_reale > 0)) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      forceOver = false;\n\n      // Se si usa BOBINA_VUOTA, imposta sempre forceOver a true\n      if (idBobina === 'BOBINA_VUOTA') {\n        forceOver = true;\n        console.log('Forzando operazione per BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        // Anche in questo caso forziamo l'operazione\n        forceOver = true;\n        console.log(`Forzando operazione per metri posati (${metriPosati}) > metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n\n              await caviService.updateMetriPosati(\n                cantiereId,\n                formData.id_cavo,\n                metriPosati,\n                idBobina,\n                forceOver\n              );\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n              if (error.response) {\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = error.response.data?.detail || error.message;\n\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n      if (error.response) {\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = error.response.data?.detail || error.message;\n\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID */}\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Cerca cavo per ID\n          </Typography>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={9}>\n              <TextField\n                fullWidth\n                label=\"ID Cavo\"\n                variant=\"outlined\"\n                value={cavoIdInput}\n                onChange={(e) => setCavoIdInput(e.target.value)}\n                placeholder=\"Inserisci l'ID del cavo\"\n              />\n            </Grid>\n            <Grid item xs={3}>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={handleSearchCavoById}\n                disabled={caviLoading || !cavoIdInput.trim()}\n                startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n              >\n                Cerca\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Lista cavi */}\n        <Paper sx={{ p: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\">\n              Non ci sono cavi disponibili da installare.\n            </Alert>\n          ) : (\n            <List sx={{ maxHeight: '400px', overflow: 'auto' }}>\n              {cavi.map((cavo) => (\n                <React.Fragment key={cavo.id_cavo}>\n                  <ListItem button onClick={() => handleCavoSelect(cavo)}>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle1\">{cavo.id_cavo}</Typography>\n                          {isCableSpare(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"SPARE\"\n                              color=\"error\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : isCableInstalled(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"Installato\"\n                              color=\"success\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : (\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione}\n                              color={getCableStateColor(cavo.stato_installazione)}\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <>\n                          <Typography variant=\"body2\" component=\"span\">\n                            {cavo.tipologia || 'N/A'} - {cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Da: {cavo.ubicazione_partenza || 'N/A'} - A: {cavo.ubicazione_arrivo || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Metri teorici: {cavo.metri_teorici || 'N/A'} - Metri posati: {cavo.metratura_reale || '0'}\n                          </Typography>\n                        </>\n                      }\n                    />\n                    <ListItemSecondaryAction>\n                      <IconButton edge=\"end\" onClick={(e) => {\n                        e.stopPropagation(); // Prevent triggering the ListItem click\n                        setSelectedCavo(cavo);\n                        setShowCavoDetailsDialog(true);\n                      }}>\n                        <InfoIcon />\n                      </IconButton>\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                  <Divider />\n                </React.Fragment>\n              ))}\n            </List>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserisci metri posati\n        </Typography>\n\n        <CavoDetailsView\n          cavo={selectedCavo}\n          compact={true}\n          title=\"Dettagli del cavo selezionato\"\n        />\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom sx={{ fontWeight: 'bold' }}>\n            Inserisci i metri posati\n          </Typography>\n\n          {/* Informazioni sul cavo e sulla bobina in una griglia */}\n          <Grid container spacing={2} sx={{ mb: 3 }}>\n            <Grid item xs={12} md={6}>\n              <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, height: '100%' }}>\n                <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>\n                  Informazioni cavo\n                </Typography>\n                <Grid container spacing={1}>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Metri teorici:</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\">{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Stato attuale:</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Chip\n                      label={selectedCavo.stato_installazione || 'N/D'}\n                      size=\"small\"\n                      color={getCableStateColor(selectedCavo.stato_installazione)}\n                      variant=\"outlined\"\n                    />\n                  </Grid>\n                </Grid>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, height: '100%' }}>\n                <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'secondary.main' }}>\n                  Informazioni bobina\n                </Typography>\n                {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n                  const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                  return bobina ? (\n                    <Grid container spacing={1}>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>ID Bobina:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\">{getBobinaNumber(bobina.id_bobina)}</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Metri residui:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\">{bobina.metri_residui || 0} m</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Stato:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Chip\n                          label={bobina.stato_bobina || 'N/D'}\n                          size=\"small\"\n                          color={getReelStateColor(bobina.stato_bobina)}\n                          variant=\"outlined\"\n                        />\n                      </Grid>\n                    </Grid>\n                  ) : (\n                    <Typography variant=\"body2\">Bobina non trovata</Typography>\n                  );\n                })() : (\n                  <Typography variant=\"body2\">\n                    {formData.id_bobina === 'BOBINA_VUOTA' ?\n                      \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" :\n                      \"Nessuna bobina selezionata\"}\n                  </Typography>\n                )}\n              </Box>\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, mb: 3 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Metratura posata\n            </Typography>\n            <TextField\n              size=\"small\"\n              fullWidth\n              label=\"Metri posati\"\n              variant=\"outlined\"\n              name=\"metri_posati\"\n              type=\"number\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              sx={{ mb: 1 }}\n            />\n          </Box>\n\n          {formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              {formWarnings.metri_posati}\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\">\n              Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n            </Typography>\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = (e) => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo) {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoConduttori = String(selectedCavo.n_conduttori || '0');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n\n            const bobinaTipologia = String(bobinaEsistente.tipologia || '');\n            const bobinaConduttori = String(bobinaEsistente.n_conduttori || '0');\n            const bobinaSezione = String(bobinaEsistente.sezione || '0');\n\n            // Log per debug\n            console.log(`Verifica compatibilità bobina ${bobinaEsistente.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              n_conduttori: `${bobinaConduttori} === ${cavoConduttori}`,\n              sezione: `${bobinaSezione} === ${cavoSezione}`\n            });\n\n            if (bobinaTipologia !== cavoTipologia ||\n                bobinaConduttori !== cavoConduttori ||\n                bobinaSezione !== cavoSezione) {\n              // Mostra il dialogo per bobine incompatibili\n              setIncompatibleReel(bobinaEsistente);\n              setShowIncompatibleReelDialog(true);\n              return;\n            }\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Associa bobina\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"body1\" paragraph>\n            Seleziona una bobina da associare al cavo. È necessario associare sempre una bobina, anche utilizzando l'opzione \"BOBINA VUOTA\" se non si desidera associare una bobina specifica.\n          </Typography>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              <Grid container spacing={3}>\n                {/* Colonna sinistra: Input diretto */}\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                    Inserimento diretto\n                  </Typography>\n                  <TextField\n                    size=\"small\"\n                    fullWidth\n                    label=\"Numero bobina\"\n                    variant=\"outlined\"\n                    placeholder=\"Solo il numero (Y)\"\n                    helperText={formErrors.id_bobina_input || \"Inserisci solo il numero della bobina\"}\n                    error={!!formErrors.id_bobina_input}\n                    onBlur={handleBobinaNumberInput}\n                    sx={{ mb: 1 }}\n                  />\n                  <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                    ID Bobina: <strong>{formData.id_bobina || '-'}</strong>\n                  </Typography>\n                </Grid>\n\n                {/* Colonna destra: Selezione dalla lista */}\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                    Selezione dalla lista\n                  </Typography>\n                  <Box>\n                    <Typography variant=\"subtitle2\" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold', mb: 1 }}>\n                      {selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'}\n                    </Typography>\n\n                    <FormControl fullWidth size=\"small\" error={!!formErrors.id_bobina}>\n                      <InputLabel id=\"bobina-select-label\">Seleziona bobina</InputLabel>\n                      <Select\n                        labelId=\"bobina-select-label\"\n                        id=\"bobina-select\"\n                        name=\"id_bobina\"\n                        value={formData.id_bobina}\n                        label=\"Seleziona bobina\"\n                        onChange={handleFormChange}\n                      >\n                        <MenuItem value=\"BOBINA_VUOTA\">\n                          <strong>BOBINA VUOTA</strong> (nessuna bobina associata)\n                        </MenuItem>\n                        <Divider />\n                        {bobine.length > 0 ? (\n                          <Box component=\"li\" sx={{ p: 1, bgcolor: 'background.paper' }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: 'success.main' }}>\n                              {bobine.length} bobine compatibili trovate\n                            </Typography>\n                          </Box>\n                        ) : null}\n                        {bobine.map((bobina) => (\n                          <MenuItem\n                            key={bobina.id_bobina}\n                            value={bobina.id_bobina}\n                            disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                            sx={{\n                              '&.Mui-selected': { bgcolor: 'success.light' },\n                              '&.Mui-selected:hover': { bgcolor: 'success.light' },\n                              bgcolor: selectedCavo &&\n                                     bobina.tipologia === selectedCavo.tipologia &&\n                                     String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                                     String(bobina.sezione) === String(selectedCavo.sezione) ?\n                                     'rgba(76, 175, 80, 0.08)' : 'inherit'\n                            }}\n                          >\n                            <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>\n                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>\n                                <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                                  {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'}\n                                </Typography>\n                                {selectedCavo &&\n                                 bobina.tipologia === selectedCavo.tipologia &&\n                                 String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                                 String(bobina.sezione) === String(selectedCavo.sezione) && (\n                                  <Chip\n                                    size=\"small\"\n                                    label=\"Compatibile\"\n                                    color=\"success\"\n                                    variant=\"outlined\"\n                                    sx={{ height: 20, fontSize: '0.6rem' }}\n                                  />\n                                )}\n                              </Box>\n                              <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>\n                                <Typography variant=\"caption\">\n                                  {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                                </Typography>\n                                <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                  {bobina.metri_residui || 0} m disponibili\n                                </Typography>\n                              </Box>\n                            </Box>\n                          </MenuItem>\n                        ))}\n                      </Select>\n                      <FormHelperText>\n                        {formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'}\n                      </FormHelperText>\n                    </FormControl>\n\n                    {bobine.length === 0 && !bobineLoading && (\n                      <Alert severity=\"warning\" sx={{ mt: 2, fontSize: '0.8rem' }}>\n                        Nessuna bobina compatibile trovata. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\n                      </Alert>\n                    )}\n                  </Box>\n                </Grid>\n              </Grid>\n\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                <Typography variant=\"body2\">\n                  <strong>Nota</strong>: Se selezioni \"BOBINA VUOTA\", potrai associare una bobina specifica in un secondo momento.\n                </Typography>\n              </Alert>\n            </Box>\n          )}\n\n          {/* Mostra dettagli della bobina selezionata */}\n          {!bobineLoading && formData.id_bobina && (\n            <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n              <Typography variant=\"subtitle2\" gutterBottom>\n                Dettagli bobina selezionata\n              </Typography>\n              {(() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                if (bobina) {\n                  return (\n                    <Grid container spacing={2}>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Numero:</strong> {getBobinaNumber(bobina.id_bobina)}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Tipologia:</strong> {bobina.tipologia || 'N/A'}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Conduttori:</strong> {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Metri totali:</strong> {bobina.metri_totali || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Metri residui:</strong> {bobina.metri_residui || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Stato:</strong> {bobina.stato_bobina || 'N/A'}\n                        </Typography>\n                      </Grid>\n                    </Grid>\n                  );\n                }\n                return (\n                  <Typography variant=\"body2\" color=\"error\">\n                    Bobina non trovata nel database\n                  </Typography>\n                );\n              })()}\n            </Box>\n          )}\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          {/* Dettagli del cavo */}\n          <CavoDetailsView\n            cavo={selectedCavo}\n            compact={true}\n            title=\"Dettagli del cavo\"\n          />\n\n          {/* Informazioni sull'operazione */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Informazioni sull'operazione:\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Metri Posati:</strong> {formData.metri_posati} m\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato Installazione:</strong> {statoInstallazione}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Bobina Associata:</strong> {numeroBobina}\n                </Typography>\n                {bobinaInfo && (\n                  <Typography variant=\"body2\">\n                    <strong>Metri Residui Bobina:</strong> {bobinaInfo.metri_residui} m\n                  </Typography>\n                )}\n              </Grid>\n            </Grid>\n          </Box>\n\n          {bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mt: 3 }}>\n              <strong>Attenzione:</strong> I metri posati ({formData.metri_posati}m) superano i metri residui della bobina ({bobinaInfo.metri_residui}m).\n              Questo porterà la bobina in stato OVER.\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1(); // Seleziona Cavo\n      case 1:\n        return renderStep3(); // Associa Bobina\n      case 2:\n        return renderStep2(); // Inserisci Metri\n      case 3:\n        return renderStep4(); // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReel(null);\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    if (!selectedCavo || !incompatibleReel) return;\n\n    try {\n      setLoading(true);\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoToMatchReel(cantiereId, selectedCavo.id_cavo, incompatibleReel);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, selectedCavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: incompatibleReel.id_bobina\n      });\n\n      onSuccess(`Caratteristiche del cavo ${selectedCavo.id_cavo} aggiornate per corrispondere alla bobina ${incompatibleReel.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n\n  return (\n    <Box>\n      {/* Sezione di ricerca */}\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Cerca cavo\n        </Typography>\n        <Grid container spacing={2} alignItems=\"center\">\n          <Grid item xs={9}>\n            <TextField\n              fullWidth\n              label=\"ID Cavo\"\n              variant=\"outlined\"\n              value={cavoIdInput}\n              onChange={(e) => setCavoIdInput(e.target.value)}\n              placeholder=\"Inserisci l'ID del cavo o parte di esso\"\n            />\n          </Grid>\n          <Grid item xs={3}>\n            <Button\n              fullWidth\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSearchCavoById}\n              disabled={caviLoading || !cavoIdInput.trim()}\n              startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n            >\n              Cerca\n            </Button>\n          </Grid>\n        </Grid>\n      </Paper>\n\n      {/* Risultati della ricerca */}\n      {showSearchResults && searchResults.length > 0 && (\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Risultati della ricerca\n          </Typography>\n          <TableContainer>\n            <Table size=\"small\">\n              <TableHead>\n                <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Tipologia</TableCell>\n                  <TableCell>Conduttori</TableCell>\n                  <TableCell>Ubicazione</TableCell>\n                  <TableCell>Metri Teorici</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell>Azioni</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {searchResults.map((cavo) => (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>{cavo.id_cavo}</TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}</TableCell>\n                    <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={cavo.stato_installazione || 'N/D'}\n                        size=\"small\"\n                        color={getCableStateColor(cavo.stato_installazione)}\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Button\n                        size=\"small\"\n                        variant=\"contained\"\n                        color=\"primary\"\n                        onClick={() => handleCavoSelect(cavo)}\n                        disabled={isCableInstalled(cavo)}\n                      >\n                        Seleziona\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Paper>\n      )}\n\n      {/* Form per inserimento metri e selezione bobina */}\n      {selectedCavo && (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Inserimento metri posati\n          </Typography>\n\n          {/* Dettagli del cavo selezionato */}\n          <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, mb: 3 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>\n              Cavo selezionato: {selectedCavo.id_cavo}\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'} x {selectedCavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metri teorici:</strong> {selectedCavo.metri_teorici || 'N/A'} m</Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Ubicazione partenza:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ubicazione arrivo:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato:</strong>\n                  <Chip\n                    label={selectedCavo.stato_installazione || 'N/D'}\n                    size=\"small\"\n                    color={getCableStateColor(selectedCavo.stato_installazione)}\n                    variant=\"outlined\"\n                    sx={{ ml: 1 }}\n                  />\n                </Typography>\n              </Grid>\n            </Grid>\n          </Box>\n\n          <Grid container spacing={3}>\n            {/* Colonna sinistra: Metri posati */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                Metratura posata\n              </Typography>\n              <TextField\n                size=\"small\"\n                fullWidth\n                label=\"Metri posati\"\n                variant=\"outlined\"\n                name=\"metri_posati\"\n                type=\"number\"\n                value={formData.metri_posati}\n                onChange={handleFormChange}\n                error={!!formErrors.metri_posati}\n                helperText={formErrors.metri_posati || formWarnings.metri_posati}\n                FormHelperTextProps={{\n                  sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n                }}\n                sx={{ mb: 1 }}\n              />\n              {formWarnings.metri_posati && !formErrors.metri_posati && (\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  {formWarnings.metri_posati}\n                </Alert>\n              )}\n            </Grid>\n\n            {/* Colonna destra: Selezione bobina */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                Associa bobina\n              </Typography>\n              {bobineLoading ? (\n                <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n                  <CircularProgress />\n                </Box>\n              ) : (\n                <Box>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold', mb: 1 }}>\n                    {selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'}\n                  </Typography>\n\n                  <FormControl fullWidth size=\"small\" error={!!formErrors.id_bobina}>\n                    <InputLabel id=\"bobina-select-label\">Seleziona bobina</InputLabel>\n                    <Select\n                      labelId=\"bobina-select-label\"\n                      id=\"bobina-select\"\n                      name=\"id_bobina\"\n                      value={formData.id_bobina}\n                      label=\"Seleziona bobina\"\n                      onChange={handleFormChange}\n                    >\n                      <MenuItem value=\"BOBINA_VUOTA\">\n                        <strong>BOBINA VUOTA</strong> (nessuna bobina associata)\n                      </MenuItem>\n                      <Divider />\n                      {bobine.length > 0 ? (\n                        <Box component=\"li\" sx={{ p: 1, bgcolor: 'background.paper' }}>\n                          <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: 'success.main' }}>\n                            {bobine.length} bobine compatibili trovate\n                          </Typography>\n                        </Box>\n                      ) : null}\n                      {bobine.map((bobina) => (\n                        <MenuItem\n                          key={bobina.id_bobina}\n                          value={bobina.id_bobina}\n                          disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                          sx={{\n                            '&.Mui-selected': { bgcolor: 'success.light' },\n                            '&.Mui-selected:hover': { bgcolor: 'success.light' },\n                            bgcolor: selectedCavo &&\n                                   bobina.tipologia === selectedCavo.tipologia &&\n                                   String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                                   String(bobina.sezione) === String(selectedCavo.sezione) ?\n                                   'rgba(76, 175, 80, 0.08)' : 'inherit'\n                          }}\n                        >\n                          <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>\n                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                                {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'}\n                              </Typography>\n                              {selectedCavo &&\n                               bobina.tipologia === selectedCavo.tipologia &&\n                               String(bobina.n_conduttori) === String(selectedCavo.n_conduttori) &&\n                               String(bobina.sezione) === String(selectedCavo.sezione) && (\n                                <Chip\n                                  size=\"small\"\n                                  label=\"Compatibile\"\n                                  color=\"success\"\n                                  variant=\"outlined\"\n                                  sx={{ height: 20, fontSize: '0.6rem' }}\n                                />\n                              )}\n                            </Box>\n                            <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>\n                              <Typography variant=\"caption\">\n                                {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                              </Typography>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                {bobina.metri_residui || 0} m disponibili\n                              </Typography>\n                            </Box>\n                          </Box>\n                        </MenuItem>\n                      ))}\n                    </Select>\n                    <FormHelperText>\n                      {formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'}\n                    </FormHelperText>\n                  </FormControl>\n\n                  {bobine.length === 0 && !bobineLoading && (\n                    <Alert severity=\"warning\" sx={{ mt: 2, fontSize: '0.8rem' }}>\n                      Nessuna bobina compatibile trovata. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\n                    </Alert>\n                  )}\n                </Box>\n              )}\n\n              {/* Mostra dettagli della bobina selezionata */}\n              {!bobineLoading && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                if (bobina) {\n                  return (\n                    <Box sx={{ mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n                      <Typography variant=\"body2\"><strong>Bobina:</strong> {getBobinaNumber(bobina.id_bobina)}</Typography>\n                      <Typography variant=\"body2\"><strong>Metri residui:</strong> {bobina.metri_residui || 0} m</Typography>\n                      <Typography variant=\"body2\">\n                        <strong>Stato:</strong>\n                        <Chip\n                          label={bobina.stato_bobina || 'N/D'}\n                          size=\"small\"\n                          color={getReelStateColor(bobina.stato_bobina)}\n                          variant=\"outlined\"\n                          sx={{ ml: 1 }}\n                        />\n                      </Typography>\n                    </Box>\n                  );\n                }\n                return null;\n              })()}\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>\n            <Button\n              variant=\"outlined\"\n              color=\"secondary\"\n              onClick={() => {\n                setSelectedCavo(null);\n                setFormData({\n                  id_cavo: '',\n                  metri_posati: '',\n                  id_bobina: ''\n                });\n              }}\n              startIcon={<CancelIcon />}\n              disabled={loading}\n            >\n              Annulla\n            </Button>\n\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSubmit}\n              endIcon={<SaveIcon />}\n              disabled={loading || !formData.metri_posati || !formData.id_bobina}\n            >\n              {loading ? <CircularProgress size={24} /> : 'Salva'}\n            </Button>\n          </Box>\n        </Paper>\n      )}\n\n      {/* Dialogo di conferma generico */}\n      <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">{confirmDialogProps.title}</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <Typography variant=\"body1\" sx={{ mt: 2 }}>\n            {confirmDialogProps.message}\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowConfirmDialog(false)} color=\"secondary\" variant=\"outlined\">\n            Annulla\n          </Button>\n          <Button\n            onClick={() => {\n              setShowConfirmDialog(false);\n              confirmDialogProps.onConfirm();\n            }}\n            color=\"primary\"\n            variant=\"contained\"\n            autoFocus\n          >\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">Cavo già posato</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          {alreadyLaidCavo && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body1\" paragraph>\n                Il cavo <strong>{alreadyLaidCavo.id_cavo}</strong> risulta già posato ({alreadyLaidCavo.metratura_reale || 0}m).\n              </Typography>\n              <Typography variant=\"body1\" paragraph>\n                Puoi scegliere di:\n              </Typography>\n              <Typography variant=\"body2\" component=\"ul\">\n                <li>Modificare la bobina associata al cavo</li>\n                <li>Selezionare un altro cavo</li>\n                <li>Annullare l'operazione</li>\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={handleCloseIncompatibleReelDialog}\n        cavo={selectedCavo}\n        bobina={incompatibleReel}\n        onUpdateCavo={handleUpdateCavoToMatchReel}\n        onSelectAnotherReel={handleSelectAnotherReel}\n      />\n\n      {/* Dialogo per visualizzare i dettagli del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <InfoIcon color=\"primary\" />\n            <Typography variant=\"h6\">Dettagli Cavo</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <CavoDetailsView cavo={selectedCavo} />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)} color=\"primary\">\n            Chiudi\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,QAClB,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuE,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC+E,IAAI,EAAEC,OAAO,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiF,MAAM,EAAEC,SAAS,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmF,YAAY,EAAEC,eAAe,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqF,WAAW,EAAEC,cAAc,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACuF,UAAU,EAAEC,aAAa,CAAC,GAAGxF,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM,CAACyF,QAAQ,EAAEC,WAAW,CAAC,GAAG1F,QAAQ,CAAC;IACvC2F,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACgG,YAAY,EAAEC,eAAe,CAAC,GAAGjG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAACkG,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACoG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACsG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACwG,eAAe,EAAEC,kBAAkB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC0G,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACd2G,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC5C,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAM6C,eAAe,GAAIC,QAAQ,IAAK;IACpC;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOF,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMG,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFzC,cAAc,CAAC,IAAI,CAAC;MACpB0C,OAAO,CAACC,GAAG,CAAC,oCAAoCnD,UAAU,KAAK,CAAC;;MAEhE;MACA,IAAI;QACF,MAAMoD,QAAQ,GAAG,MAAMxE,WAAW,CAACyE,OAAO,CAACrD,UAAU,CAAC;QACtDkD,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACE,MAAM,OAAO,CAAC;;QAE/C;QACA;QACAtC,OAAO,CAACoC,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOG,SAAS,EAAE;QAClBL,OAAO,CAACM,KAAK,CAAC,qDAAqD,EAAED,SAAS,CAAC;;QAE/E;QACA,IAAIA,SAAS,CAACE,cAAc,IAAI,CAACF,SAAS,CAACG,QAAQ,IAC/CH,SAAS,CAACI,IAAI,KAAK,cAAc,IAChCJ,SAAS,CAACK,OAAO,IAAIL,SAAS,CAACK,OAAO,CAACb,QAAQ,CAAC,eAAe,CAAE,EAAE;UAEtEG,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtE,MAAM,IAAIU,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvD,IAAI;YACF;YACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC3C,MAAMC,OAAO,GAAGtF,aAAa,CAACuF,QAAQ,CAACC,OAAO;;YAE9C;YACA,MAAMC,aAAa,GAAG,MAAMpI,KAAK,CAACqI,GAAG,CACnC,GAAGJ,OAAO,SAASnE,UAAU,EAAE,EAC/B;cACEwE,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUR,KAAK;cAClC,CAAC;cACDS,OAAO,EAAE,KAAK,CAAC;YACjB,CACF,CAAC;YAEDvB,OAAO,CAACC,GAAG,CAAC,2CAA2CmB,aAAa,CAACI,IAAI,CAACpB,MAAM,OAAO,CAAC;YACxFtC,OAAO,CAACsD,aAAa,CAACI,IAAI,CAAC;UAC7B,CAAC,CAAC,OAAOC,UAAU,EAAE;YACnBzB,OAAO,CAACM,KAAK,CAAC,uCAAuC,EAAEmB,UAAU,CAAC;YAClE,MAAMA,UAAU;UAClB;QACF,CAAC,MAAM;UACL,MAAMpB,SAAS;QACjB;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIoB,YAAY,GAAG,iCAAiC;MAEpD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEA1D,OAAO,CAAC0E,YAAY,CAAC;IACvB,CAAC,SAAS;MACRpE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMoC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFlC,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,IAAIS,YAAY,IAAIA,YAAY,CAAC2D,SAAS,KAAKC,SAAS,IAAI5D,YAAY,CAAC6D,YAAY,KAAKD,SAAS,IAAI5D,YAAY,CAAC8D,OAAO,KAAKF,SAAS,EAAE;QACzI;QACA7B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;UAC1C2B,SAAS,EAAE3D,YAAY,CAAC2D,SAAS;UACjCE,YAAY,EAAE7D,YAAY,CAAC6D,YAAY;UACvCC,OAAO,EAAE9D,YAAY,CAAC8D,OAAO;UAC7BC,IAAI,EAAE;YACJJ,SAAS,EAAE,OAAO3D,YAAY,CAAC2D,SAAS;YACxCE,YAAY,EAAE,OAAO7D,YAAY,CAAC6D,YAAY;YAC9CC,OAAO,EAAE,OAAO9D,YAAY,CAAC8D;UAC/B;QACF,CAAC,CAAC;QACF/B,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;QACxE,IAAI;UACF;UACA,MAAMgC,iBAAiB,GAAG,MAAM1F,gBAAgB,CAAC2F,oBAAoB,CACnEpF,UAAU,EACVmB,YAAY,CAAC2D,SAAS,EACtB3D,YAAY,CAAC6D,YAAY,EACzB7D,YAAY,CAAC8D,OACf,CAAC;UAED,IAAIE,iBAAiB,IAAIA,iBAAiB,CAAC7B,MAAM,GAAG,CAAC,EAAE;YACrDJ,OAAO,CAACC,GAAG,CAAC,WAAWgC,iBAAiB,CAAC7B,MAAM,qBAAqB,CAAC;YACrE;YACA6B,iBAAiB,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;YACnEtE,SAAS,CAACiE,iBAAiB,CAAC;YAC5B;UACF,CAAC,MAAM;YACLjC,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;UACvF;QACF,CAAC,CAAC,OAAOsC,WAAW,EAAE;UACpBvC,OAAO,CAACM,KAAK,CAAC,kDAAkD,EAAEiC,WAAW,CAAC;UAC9E;QACF;MACF;;MAEA;MACAvC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC5D,MAAMuC,UAAU,GAAG,MAAMjG,gBAAgB,CAACkG,SAAS,CAAC3F,UAAU,CAAC;;MAE/D;MACA,IAAI4F,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM,IAC/C,CAACA,MAAM,CAACC,YAAY,KAAK,aAAa,IAAID,MAAM,CAACC,YAAY,KAAK,QAAQ,KAC1ED,MAAM,CAACC,YAAY,KAAK,MAAM,IAAID,MAAM,CAACC,YAAY,KAAK,WAC5D,CAAC;;MAED;MACA,IAAI5E,YAAY,EAAE;QAChB;QACA,IAAIA,YAAY,CAAC2D,SAAS,KAAKC,SAAS,IAAI5D,YAAY,CAAC6D,YAAY,KAAKD,SAAS,IAAI5D,YAAY,CAAC8D,OAAO,KAAKF,SAAS,EAAE;UACzH;UACA7B,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE;YAC3D2B,SAAS,EAAE3D,YAAY,CAAC2D,SAAS;YACjCE,YAAY,EAAE7D,YAAY,CAAC6D,YAAY;YACvCC,OAAO,EAAE9D,YAAY,CAAC8D,OAAO;YAC7BC,IAAI,EAAE;cACJJ,SAAS,EAAE,OAAO3D,YAAY,CAAC2D,SAAS;cACxCE,YAAY,EAAE,OAAO7D,YAAY,CAAC6D,YAAY;cAC9CC,OAAO,EAAE,OAAO9D,YAAY,CAAC8D;YAC/B;UACF,CAAC,CAAC;UACF;UACA/B,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;YACrD2B,SAAS,EAAE3D,YAAY,CAAC2D,SAAS;YACjCE,YAAY,EAAE7D,YAAY,CAAC6D,YAAY;YACvCC,OAAO,EAAE9D,YAAY,CAAC8D;UACxB,CAAC,CAAC;;UAEF;UACA,MAAME,iBAAiB,GAAGS,kBAAkB,CAACC,MAAM,CAACC,MAAM,IAAI;YAC5D;YACA,MAAME,aAAa,GAAGC,MAAM,CAAC9E,YAAY,CAAC2D,SAAS,IAAI,EAAE,CAAC;YAC1D,MAAMoB,cAAc,GAAGD,MAAM,CAAC9E,YAAY,CAAC6D,YAAY,IAAI,GAAG,CAAC;YAC/D,MAAMmB,WAAW,GAAGF,MAAM,CAAC9E,YAAY,CAAC8D,OAAO,IAAI,GAAG,CAAC;YAEvD,MAAMmB,eAAe,GAAGH,MAAM,CAACH,MAAM,CAAChB,SAAS,IAAI,EAAE,CAAC;YACtD,MAAMuB,gBAAgB,GAAGJ,MAAM,CAACH,MAAM,CAACd,YAAY,IAAI,GAAG,CAAC;YAC3D,MAAMsB,aAAa,GAAGL,MAAM,CAACH,MAAM,CAACb,OAAO,IAAI,GAAG,CAAC;;YAEnD;YACA/B,OAAO,CAACC,GAAG,CAAC,oBAAoB2C,MAAM,CAACjE,SAAS,GAAG,EAAE;cACnDiD,SAAS,EAAE,GAAGsB,eAAe,QAAQJ,aAAa,EAAE;cACpDhB,YAAY,EAAE,GAAGqB,gBAAgB,QAAQH,cAAc,EAAE;cACzDjB,OAAO,EAAE,GAAGqB,aAAa,QAAQH,WAAW;YAC9C,CAAC,CAAC;YAEF,OAAOC,eAAe,KAAKJ,aAAa,IACjCK,gBAAgB,KAAKH,cAAc,IACnCI,aAAa,KAAKH,WAAW;UACtC,CAAC,CAAC;;UAEF;UACA,IAAIhB,iBAAiB,CAAC7B,MAAM,GAAG,CAAC,EAAE;YAChCsC,kBAAkB,GAAGT,iBAAiB;YACtCjC,OAAO,CAACC,GAAG,CAAC,YAAYgC,iBAAiB,CAAC7B,MAAM,qBAAqB,CAAC;UACxE,CAAC,MAAM;YACLJ,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;YACrFD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEhC,YAAY,CAAC2D,SAAS,EAAEmB,MAAM,CAAC9E,YAAY,CAAC6D,YAAY,CAAC,EAAEiB,MAAM,CAAC9E,YAAY,CAAC8D,OAAO,CAAC,CAAC;YACzH/B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEyC,kBAAkB,CAACW,GAAG,CAAChB,CAAC,IAAI,CAACA,CAAC,CAAC1D,SAAS,EAAE0D,CAAC,CAACT,SAAS,EAAEmB,MAAM,CAACV,CAAC,CAACP,YAAY,CAAC,EAAEiB,MAAM,CAACV,CAAC,CAACN,OAAO,CAAC,CAAC,CAAC,CAAC;UACxI;QACF;;QAEA;QACAW,kBAAkB,CAACP,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;MACtE;MAEAtE,SAAS,CAAC0E,kBAAkB,CAAC;IAC/B,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DtD,OAAO,CAAC,uCAAuC,IAAIsD,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRlD,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM8F,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACnF,WAAW,CAACoF,IAAI,CAAC,CAAC,EAAE;MACvBvG,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFM,cAAc,CAAC,IAAI,CAAC;MACpB0C,OAAO,CAACC,GAAG,CAAC,6BAA6B9B,WAAW,CAACoF,IAAI,CAAC,CAAC,iBAAiBzG,UAAU,EAAE,CAAC;;MAEzF;MACA,MAAMoD,QAAQ,GAAG,MAAMxE,WAAW,CAACyE,OAAO,CAACrD,UAAU,CAAC;;MAEtD;MACA,MAAM0G,YAAY,GAAGtD,QAAQ,CAACyC,MAAM,CAACc,IAAI,IACvCA,IAAI,CAAChF,OAAO,CAACiF,WAAW,CAAC,CAAC,CAAC7D,QAAQ,CAAC1B,WAAW,CAACoF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CACtE,CAAC;MAED1D,OAAO,CAACC,GAAG,CAAC,WAAWuD,YAAY,CAACpD,MAAM,iCAAiC,CAAC;;MAE5E;MACA,MAAMuD,UAAU,GAAGH,YAAY,CAACI,IAAI,CAACH,IAAI,IACvCA,IAAI,CAAChF,OAAO,CAACiF,WAAW,CAAC,CAAC,KAAKvF,WAAW,CAACoF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAChE,CAAC;MAED,IAAIC,UAAU,EAAE;QACd3D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE0D,UAAU,CAAC;;QAEzD;QACA,IAAIA,UAAU,CAACE,mBAAmB,KAAK,YAAY,IAAKF,UAAU,CAACG,eAAe,IAAIH,UAAU,CAACG,eAAe,GAAG,CAAE,EAAE;UACrH9D,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE0D,UAAU,CAAC;UAC/DpE,kBAAkB,CAACoE,UAAU,CAAC;UAC9BtE,wBAAwB,CAAC,IAAI,CAAC;UAC9B/B,cAAc,CAAC,KAAK,CAAC;UACrB;QACF;;QAEA;QACA,IAAIqG,UAAU,CAACI,sBAAsB,KAAK,CAAC,EAAE;UAC3C/D,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE0D,UAAU,CAAC;UAC9C;QACF;;QAEA;QACAK,gBAAgB,CAACL,UAAU,CAAC;MAC9B,CAAC,MAAM,IAAIH,YAAY,CAACpD,MAAM,GAAG,CAAC,EAAE;QAClC;QACA1C,gBAAgB,CAAC8F,YAAY,CAAC;QAC9B5F,oBAAoB,CAAC,IAAI,CAAC;MAC5B,CAAC,MAAM;QACL;QACAZ,OAAO,CAAC,oCAAoCmB,WAAW,CAACoF,IAAI,CAAC,CAAC,kBAAkBzG,UAAU,EAAE,CAAC;MAC/F;IACF,CAAC,CAAC,OAAOwD,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;MAEtD;MACA,IAAIoB,YAAY,GAAG,+BAA+B;MAElD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAAC2D,MAAM,KAAK,GAAG,EAAE;QAC/BvC,YAAY,GAAG,gBAAgBvD,WAAW,CAACoF,IAAI,CAAC,CAAC,8BAA8BzG,UAAU,EAAE;MAC7F,CAAC,MAAM,IAAIwD,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEA1D,OAAO,CAAC0E,YAAY,CAAC;IACvB,CAAC,SAAS;MACRpE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM0G,gBAAgB,GAAIP,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACI,mBAAmB,KAAK,YAAY,IAAKJ,IAAI,CAACK,eAAe,IAAIL,IAAI,CAACK,eAAe,GAAG,CAAE,EAAE;MACnG;MACAvE,kBAAkB,CAACkE,IAAI,CAAC;MACxBpE,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IACA;IAAA,KACK,IAAIoE,IAAI,CAACM,sBAAsB,KAAK,CAAC,EAAE;MAC1C;MACA,IAAIG,MAAM,CAACC,OAAO,CAAC,WAAWV,IAAI,CAAChF,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACA2F,eAAe,CAACX,IAAI,CAAChF,OAAO,CAAC,CAAC4F,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAGb,IAAI;YAAEM,sBAAsB,EAAE;UAAE,CAAC;UAC1D7F,eAAe,CAACoG,WAAW,CAAC;UAC5B9F,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAE6F,WAAW,CAAC7F,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAd,oBAAoB,CAAC,KAAK,CAAC;;UAE3B;UACA8B,UAAU,CAAC,CAAC;QACd,CAAC,CAAC,CAAC6E,KAAK,CAACjE,KAAK,IAAI;UAChBN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvEtD,OAAO,CAAC,kDAAkD,IAAIsD,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACAxC,eAAe,CAACuF,IAAI,CAAC;MACrBjF,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEgF,IAAI,CAAChF,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAd,oBAAoB,CAAC,KAAK,CAAC;;MAE3B;MACA,IAAI6F,IAAI,CAAC7B,SAAS,IAAI6B,IAAI,CAAC3B,YAAY,IAAI2B,IAAI,CAAC1B,OAAO,EAAE;QACvD/B,OAAO,CAACC,GAAG,CAAC,8CAA8CwD,IAAI,CAAChF,OAAO,KAAK,CAAC;QAC5EiB,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC;;EAED;EACA,MAAM0E,eAAe,GAAG,MAAOI,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAM9I,WAAW,CAAC0I,eAAe,CAACtH,UAAU,EAAE0H,MAAM,CAAC;MACrDzH,SAAS,CAAC,QAAQyH,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOlE,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvEtD,OAAO,CAAC,kDAAkD,IAAIsD,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMJ,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMmE,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCrG,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACoG,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACAE,aAAa,CAACH,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAACH,IAAI,EAAEC,KAAK,KAAK;IACrC,IAAItE,KAAK,GAAG,IAAI;IAChB,IAAIyE,OAAO,GAAG,IAAI;IAElB,IAAIJ,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACrB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCjD,KAAK,GAAG,uCAAuC;QAC/C,OAAO,KAAK;MACd;;MAEA;MACA,IAAI0E,KAAK,CAACC,UAAU,CAACL,KAAK,CAAC,CAAC,IAAIK,UAAU,CAACL,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDtE,KAAK,GAAG,sCAAsC;QAC9C,OAAO,KAAK;MACd;MAEA,MAAM4E,WAAW,GAAGD,UAAU,CAACL,KAAK,CAAC;;MAErC;MACA,IAAI3G,YAAY,IAAIA,YAAY,CAACkH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAChH,YAAY,CAACkH,aAAa,CAAC,EAAE;QACtGJ,OAAO,GAAG,mBAAmBG,WAAW,yCAAyCjH,YAAY,CAACkH,aAAa,IAAI;MACjH;;MAEA;MACA,IAAI5G,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC/D,MAAMiE,MAAM,GAAG7E,MAAM,CAAC6F,IAAI,CAACvB,CAAC,IAAIA,CAAC,CAAC1D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIiE,MAAM,IAAIsC,WAAW,GAAGD,UAAU,CAACrC,MAAM,CAACN,aAAa,CAAC,EAAE;UAC5DyC,OAAO,GAAG,mBAAmBG,WAAW,6CAA6CtC,MAAM,CAACN,aAAa,oCAAoC;QAC/I;MACF;IACF,CAAC,MAAM,IAAIqC,IAAI,KAAK,WAAW,EAAE;MAC/B;MACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACrB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCjD,KAAK,GAAG,qCAAqC;QAC7C,OAAO,KAAK;MACd;IACF;;IAEA;IACAzB,aAAa,CAACuG,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACT,IAAI,GAAGrE;IACV,CAAC,CAAC,CAAC;;IAEH;IACAvB,eAAe,CAACqG,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACT,IAAI,GAAGI;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAACzE,KAAK;EACf,CAAC;;EAED;EACA,MAAM,CAAC+E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxM,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACyM,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1M,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2M,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5M,QAAQ,CAAC;IAC3D6M,KAAK,EAAE,EAAE;IACTjF,OAAO,EAAE,EAAE;IACXkF,SAAS,EAAEA,CAAA,KAAM,CAAC;EACpB,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;;IAEnB;IACAV,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACA,IAAI,CAAC/G,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAAC6E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEwC,MAAM,CAACrH,YAAY,GAAG,uCAAuC;MAC7DoH,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAId,KAAK,CAACC,UAAU,CAAC1G,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAIuG,UAAU,CAAC1G,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7FqH,MAAM,CAACrH,YAAY,GAAG,sCAAsC;MAC5DoH,OAAO,GAAG,KAAK;IACjB;;IAEA;IACA,IAAI,CAACvH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,CAAC4E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC3DwC,MAAM,CAACpH,SAAS,GAAG,qCAAqC;MACxDmH,OAAO,GAAG,KAAK;IACjB;IAEA,IAAIA,OAAO,EAAE;MACX,MAAMZ,WAAW,GAAGD,UAAU,CAAC1G,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAIT,YAAY,IAAIA,YAAY,CAACkH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAChH,YAAY,CAACkH,aAAa,CAAC,EAAE;QACtGa,QAAQ,CAACtH,YAAY,GAAG,mBAAmBwG,WAAW,yCAAyCjH,YAAY,CAACkH,aAAa,IAAI;QAC7HG,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5B;QACA;MACF;;MAEA;MACA,IAAIQ,OAAO,IAAIvH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC1E,MAAMiE,MAAM,GAAG7E,MAAM,CAAC6F,IAAI,CAACvB,CAAC,IAAIA,CAAC,CAAC1D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIiE,MAAM,IAAIsC,WAAW,GAAGD,UAAU,CAACrC,MAAM,CAACN,aAAa,CAAC,EAAE;UAC5D0D,QAAQ,CAACtH,YAAY,GAAG,mBAAmBwG,WAAW,6CAA6CtC,MAAM,CAACN,aAAa,oCAAoC;UAC3JgD,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;;UAE5B;UACAI,qBAAqB,CAAC;YACpBC,KAAK,EAAE,kCAAkC;YACzCjF,OAAO,EAAE,mBAAmBwE,WAAW,6CAA6CtC,MAAM,CAACN,aAAa,8DAA8D;YACtKsD,SAAS,EAAEA,CAAA,KAAM;cACf;cACAK,UAAU,CAAC,CAAC;YACd;UACF,CAAC,CAAC;UACFT,oBAAoB,CAAC,IAAI,CAAC;UAC1B,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;IACF;IAEA3G,aAAa,CAACkH,MAAM,CAAC;IACrBhH,eAAe,CAACiH,QAAQ,CAAC;IACzB,OAAOF,OAAO;EAChB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI5H,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAACwH,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF,CAAC,MAAM,IAAIxH,UAAU,KAAK,CAAC,EAAE;MAC3B;MACAqB,UAAU,CAAC,CAAC;IACd;IAEApB,aAAa,CAAE4H,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB7H,aAAa,CAAE4H,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxB9H,aAAa,CAAC,CAAC,CAAC;IAChBJ,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBI,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA;EACA,MAAMsH,2BAA2B,GAAGA,CAACnB,WAAW,EAAEoB,YAAY,KAAK;IACjE,OAAOtK,mBAAmB,CAACkJ,WAAW,EAAEoB,YAAY,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B;IACA;IACA,IAAI3G,QAAQ;IACZ,IAAI4G,kBAAkB;IACtB,IAAItB,WAAW;IACf,IAAIuB,SAAS,GAAG,KAAK;IAErB,IAAI;MACFrJ,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAACyI,YAAY,CAAC,CAAC,EAAE;QACnBzI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA8H,WAAW,GAAGD,UAAU,CAAC1G,QAAQ,CAACG,YAAY,CAAC;;MAE/C;MACAkB,QAAQ,GAAGrB,QAAQ,CAACI,SAAS;;MAE7B;MACA,IAAI,CAACiB,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;QAChC;QACAf,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbD,SAAS,EAAE;QACb,CAAC,CAAC;QACFvB,UAAU,CAAC,KAAK,CAAC;QACjB;MACF,CAAC,MAAM,IAAIwC,QAAQ,KAAK,cAAc,EAAE;QACtC;QACAI,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAL,QAAQ,GAAG,cAAc;MAC3B,CAAC,MAAM;QACL;QACAI,OAAO,CAACC,GAAG,CAAC,wBAAwBL,QAAQ,EAAE,CAAC;MACjD;;MAEA;MACA4G,kBAAkB,GAAGH,2BAA2B,CAACnB,WAAW,EAAEjH,YAAY,CAACkH,aAAa,CAAC;;MAEzF;MACAsB,SAAS,GAAG,KAAK;;MAEjB;MACA,IAAI7G,QAAQ,KAAK,cAAc,EAAE;QAC/B6G,SAAS,GAAG,IAAI;QAChBzG,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACrD;MACA;MAAA,KACK,IAAIL,QAAQ,IAAIA,QAAQ,KAAK,cAAc,EAAE;QAChD,MAAMgD,MAAM,GAAG7E,MAAM,CAAC6F,IAAI,CAACvB,CAAC,IAAIA,CAAC,CAAC1D,SAAS,KAAKiB,QAAQ,CAAC;QACzD,IAAIgD,MAAM,IAAIsC,WAAW,GAAGD,UAAU,CAACrC,MAAM,CAACN,aAAa,CAAC,EAAE;UAC5DmE,SAAS,GAAG,IAAI;UAChBzG,OAAO,CAACC,GAAG,CAAC,qCAAqCL,QAAQ,iCAAiC,CAAC;QAC7F;MACF;;MAEA;MACA,IAAI3B,YAAY,IAAIA,YAAY,CAACkH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAChH,YAAY,CAACkH,aAAa,CAAC,EAAE;QACtG;QACAsB,SAAS,GAAG,IAAI;QAChBzG,OAAO,CAACC,GAAG,CAAC,yCAAyCiF,WAAW,sBAAsBjH,YAAY,CAACkH,aAAa,GAAG,CAAC;MACtH;;MAEA;MACAnF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBnD,UAAU;QACV0H,MAAM,EAAEjG,QAAQ,CAACE,OAAO;QACxByG,WAAW;QACXtF,QAAQ;QACR6G,SAAS;QACTD;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACnB,iBAAiB,EAAE;QACtB,MAAMqB,cAAc,GAAG,qCAAqCnI,QAAQ,CAACE,OAAO,QAAQyG,WAAW,WAAW;;QAE1G;QACAQ,qBAAqB,CAAC;UACpBC,KAAK,EAAE,wBAAwB;UAC/BjF,OAAO,EAAEgG,cAAc;UACvBd,SAAS,EAAE,MAAAA,CAAA,KAAY;YACrB;YACA,IAAI;cACFxI,UAAU,CAAC,IAAI,CAAC;cAEhB,MAAM1B,WAAW,CAACiL,iBAAiB,CACjC7J,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChByG,WAAW,EACXtF,QAAQ,EACR6G,SACF,CAAC;;cAED;cACA,IAAIG,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;cAC9F,IAAI5G,QAAQ,KAAK,cAAc,EAAE;gBAC/BgH,cAAc,IAAI,iCAAiC;cACrD,CAAC,MAAM,IAAIhH,QAAQ,EAAE;gBACnB,MAAMgD,MAAM,GAAG7E,MAAM,CAAC6F,IAAI,CAACvB,CAAC,IAAIA,CAAC,CAAC1D,SAAS,KAAKiB,QAAQ,CAAC;gBACzD,IAAIgD,MAAM,EAAE;kBACVgE,cAAc,IAAI,gCAAgChH,QAAQ,EAAE;gBAC9D;cACF;;cAEA;cACA7C,SAAS,CAAC6J,cAAc,CAAC;;cAEzB;cACAR,WAAW,CAAC,CAAC;;cAEb;cACArG,QAAQ,CAAC,CAAC;YACZ,CAAC,CAAC,OAAOO,KAAK,EAAE;cACdN,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;cAEzE;cACA,IAAIV,QAAQ,KAAK,cAAc,IAAIU,KAAK,CAACuG,OAAO,EAAE;gBAChD;gBACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;gBAC7HzJ,SAAS,CAAC6J,cAAc,CAAC;;gBAEzB;gBACAR,WAAW,CAAC,CAAC;;gBAEb;gBACArG,QAAQ,CAAC,CAAC;gBACV;cACF;;cAEA;cACA,IAAI2B,YAAY,GAAG,kDAAkD;cAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;gBAAA,IAAAsG,oBAAA;gBAClB;gBACA,MAAM7C,MAAM,GAAG3D,KAAK,CAACE,QAAQ,CAACyD,MAAM;gBACpC,MAAMtC,MAAM,GAAG,EAAAmF,oBAAA,GAAAxG,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAsF,oBAAA,uBAAnBA,oBAAA,CAAqBnF,MAAM,KAAIrB,KAAK,CAACI,OAAO;gBAE3D,IAAIuD,MAAM,KAAK,GAAG,EAAE;kBAClB;kBACA,IAAItC,MAAM,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;oBACpC6B,YAAY,GAAG,uGAAuG;kBACxH,CAAC,MAAM,IAAIC,MAAM,CAAC9B,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACxC6B,YAAY,GAAG,4EAA4E;kBAC7F,CAAC,MAAM;oBACLA,YAAY,GAAGC,MAAM;kBACvB;gBACF,CAAC,MAAM,IAAIsC,MAAM,KAAK,GAAG,EAAE;kBACzB;kBACA,IAAIrE,QAAQ,KAAK,cAAc,IAAI+B,MAAM,CAAC9B,QAAQ,CAAC,aAAa,CAAC,EAAE;oBACjE,IAAI+G,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;oBAC7HzJ,SAAS,CAAC6J,cAAc,CAAC;;oBAEzB;oBACAR,WAAW,CAAC,CAAC;;oBAEb;oBACArG,QAAQ,CAAC,CAAC;oBACV;kBACF,CAAC,MAAM;oBACL2B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;kBACvD;gBACF,CAAC,MAAM;kBACLD,YAAY,GAAG,sBAAsBuC,MAAM,MAAMtC,MAAM,EAAE;gBAC3D;cACF,CAAC,MAAM,IAAIrB,KAAK,CAACyG,OAAO,EAAE;gBACxB;gBACArF,YAAY,GAAG,+DAA+D;cAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAI/B,QAAQ,KAAK,cAAc,EAAE;gBACtD;gBACA8B,YAAY,GAAGpB,KAAK,CAACqB,MAAM;gBAC3B,IAAIrB,KAAK,CAAC2D,MAAM,KAAK,GAAG,IAAI3D,KAAK,CAACuG,OAAO,EAAE;kBACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;kBAC7HzJ,SAAS,CAAC6J,cAAc,CAAC;;kBAEzB;kBACAR,WAAW,CAAC,CAAC;;kBAEb;kBACArG,QAAQ,CAAC,CAAC;kBACV;gBACF;cACF,CAAC,MAAM;gBACL;gBACA2B,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;cACtE;cAEA3E,OAAO,CAAC0E,YAAY,CAAC;YACvB,CAAC,SAAS;cACRtE,UAAU,CAAC,KAAK,CAAC;YACnB;UACF;QACF,CAAC,CAAC;QACFoI,oBAAoB,CAAC,IAAI,CAAC;QAC1BpI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA4C,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEnD,UAAU,CAAC;MACxCkD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE1B,QAAQ,CAACE,OAAO,CAAC;MAC3CuB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEiF,WAAW,CAAC;MAC3ClF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEL,QAAQ,EAAE,OAAOA,QAAQ,CAAC;MACtDI,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEwG,SAAS,CAAC;MAEtC,MAAM/K,WAAW,CAACiL,iBAAiB,CACjC7J,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChByG,WAAW,EACXtF,QAAQ,EACR6G,SACF,CAAC;;MAED;MACA,IAAIG,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;MAC9F,IAAI5G,QAAQ,KAAK,cAAc,EAAE;QAC/BgH,cAAc,IAAI,iCAAiC;MACrD,CAAC,MAAM,IAAIhH,QAAQ,EAAE;QACnB,MAAMgD,MAAM,GAAG7E,MAAM,CAAC6F,IAAI,CAACvB,CAAC,IAAIA,CAAC,CAAC1D,SAAS,KAAKiB,QAAQ,CAAC;QACzD,IAAIgD,MAAM,EAAE;UACVgE,cAAc,IAAI,gCAAgChH,QAAQ,EAAE;QAC9D;MACF;;MAEA;MACA7C,SAAS,CAAC6J,cAAc,CAAC;;MAEzB;MACAR,WAAW,CAAC,CAAC;;MAEb;MACArG,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAIV,QAAQ,KAAK,cAAc,IAAIU,KAAK,CAACuG,OAAO,EAAE;QAChD;QACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;QAC7HzJ,SAAS,CAAC6J,cAAc,CAAC;;QAEzB;QACAR,WAAW,CAAC,CAAC;;QAEb;QACArG,QAAQ,CAAC,CAAC;QACV;MACF;;MAEA;MACA,IAAI2B,YAAY,GAAG,kDAAkD;MAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;QAAA,IAAAwG,qBAAA;QAClB;QACA,MAAM/C,MAAM,GAAG3D,KAAK,CAACE,QAAQ,CAACyD,MAAM;QACpC,MAAMtC,MAAM,GAAG,EAAAqF,qBAAA,GAAA1G,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAwF,qBAAA,uBAAnBA,qBAAA,CAAqBrF,MAAM,KAAIrB,KAAK,CAACI,OAAO;QAE3D,IAAIuD,MAAM,KAAK,GAAG,EAAE;UAClB;UACA,IAAItC,MAAM,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;YACpC6B,YAAY,GAAG,uGAAuG;UACxH,CAAC,MAAM,IAAIC,MAAM,CAAC9B,QAAQ,CAAC,YAAY,CAAC,EAAE;YACxC6B,YAAY,GAAG,4EAA4E;UAC7F,CAAC,MAAM;YACLA,YAAY,GAAGC,MAAM;UACvB;QACF,CAAC,MAAM,IAAIsC,MAAM,KAAK,GAAG,EAAE;UACzB;UACA,IAAIrE,QAAQ,KAAK,cAAc,IAAI+B,MAAM,CAAC9B,QAAQ,CAAC,aAAa,CAAC,EAAE;YACjE,IAAI+G,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;YAC7HzJ,SAAS,CAAC6J,cAAc,CAAC;;YAEzB;YACAR,WAAW,CAAC,CAAC;;YAEb;YACArG,QAAQ,CAAC,CAAC;YACV;UACF,CAAC,MAAM;YACL2B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;UACvD;QACF,CAAC,MAAM;UACLD,YAAY,GAAG,sBAAsBuC,MAAM,MAAMtC,MAAM,EAAE;QAC3D;MACF,CAAC,MAAM,IAAIrB,KAAK,CAACyG,OAAO,EAAE;QACxB;QACArF,YAAY,GAAG,+DAA+D;MAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAI/B,QAAQ,KAAK,cAAc,EAAE;QACtD;QACA8B,YAAY,GAAGpB,KAAK,CAACqB,MAAM;QAC3B,IAAIrB,KAAK,CAAC2D,MAAM,KAAK,GAAG,IAAI3D,KAAK,CAACuG,OAAO,EAAE;UACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;UAC7HzJ,SAAS,CAAC6J,cAAc,CAAC;;UAEzB;UACAR,WAAW,CAAC,CAAC;;UAEb;UACArG,QAAQ,CAAC,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL;QACA2B,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;MACtE;MAEA3E,OAAO,CAAC0E,YAAY,CAAC;IACvB,CAAC,SAAS;MACRtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6J,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACEvK,OAAA,CAACzD,GAAG;MAAAiO,QAAA,gBACFxK,OAAA,CAACvD,UAAU;QAACgO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb9K,OAAA,CAACxD,KAAK;QAACuO,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzBxK,OAAA,CAACvD,UAAU;UAACgO,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb9K,OAAA,CAACpD,IAAI;UAACsO,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAZ,QAAA,gBAC7CxK,OAAA,CAACpD,IAAI;YAACyO,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACfxK,OAAA,CAACtD,SAAS;cACR6O,SAAS;cACTC,KAAK,EAAC,SAAS;cACff,OAAO,EAAC,UAAU;cAClBvC,KAAK,EAAEzG,WAAY;cACnBgK,QAAQ,EAAGzD,CAAC,IAAKtG,cAAc,CAACsG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAChDwD,WAAW,EAAC;YAAyB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP9K,OAAA,CAACpD,IAAI;YAACyO,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACfxK,OAAA,CAACrD,MAAM;cACL4O,SAAS;cACTd,OAAO,EAAC,WAAW;cACnBkB,KAAK,EAAC,SAAS;cACfC,OAAO,EAAEhF,oBAAqB;cAC9BiF,QAAQ,EAAElL,WAAW,IAAI,CAACc,WAAW,CAACoF,IAAI,CAAC,CAAE;cAC7CiF,SAAS,EAAEnL,WAAW,gBAAGX,OAAA,CAAC7C,gBAAgB;gBAAC4O,IAAI,EAAE;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG9K,OAAA,CAAC1B,UAAU;gBAAAqM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAN,QAAA,EAC1E;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGR9K,OAAA,CAACxD,KAAK;QAACuO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBxK,OAAA,CAACvD,UAAU;UAACgO,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZnK,WAAW,gBACVX,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DxK,OAAA,CAAC7C,gBAAgB;YAAAwN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJ3J,IAAI,CAACuC,MAAM,KAAK,CAAC,gBACnB1D,OAAA,CAAC9C,KAAK;UAACiP,QAAQ,EAAC,MAAM;UAAA3B,QAAA,EAAC;QAEvB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAER9K,OAAA,CAAC/B,IAAI;UAAC8M,EAAE,EAAE;YAAEqB,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA7B,QAAA,EAChDrJ,IAAI,CAACwF,GAAG,CAAEI,IAAI,iBACb/G,OAAA,CAAC7D,KAAK,CAAC8D,QAAQ;YAAAuK,QAAA,gBACbxK,OAAA,CAAC9B,QAAQ;cAACoO,MAAM;cAACV,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAACP,IAAI,CAAE;cAAAyD,QAAA,gBACrDxK,OAAA,CAAC7B,YAAY;gBACXoO,OAAO,eACLvM,OAAA,CAACzD,GAAG;kBAACwO,EAAE,EAAE;oBAAEiB,OAAO,EAAE,MAAM;oBAAEZ,UAAU,EAAE;kBAAS,CAAE;kBAAAZ,QAAA,gBACjDxK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,WAAW;oBAAAD,QAAA,EAAEzD,IAAI,CAAChF;kBAAO;oBAAA4I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,EAC1DrL,YAAY,CAACsH,IAAI,CAAC,gBACjB/G,OAAA,CAAC1C,IAAI;oBACHyO,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,OAAO;oBACbG,KAAK,EAAC,OAAO;oBACbZ,EAAE,EAAE;sBAAEyB,EAAE,EAAE;oBAAE;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,GACApL,gBAAgB,CAACqH,IAAI,CAAC,gBACxB/G,OAAA,CAAC1C,IAAI;oBACHyO,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,YAAY;oBAClBG,KAAK,EAAC,SAAS;oBACfZ,EAAE,EAAE;sBAAEyB,EAAE,EAAE;oBAAE;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,gBAEF9K,OAAA,CAAC1C,IAAI;oBACHyO,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAEzE,IAAI,CAACI,mBAAoB;oBAChCwE,KAAK,EAAEhM,kBAAkB,CAACoH,IAAI,CAACI,mBAAmB,CAAE;oBACpD4D,EAAE,EAAE;sBAAEyB,EAAE,EAAE;oBAAE;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACD2B,SAAS,eACPzM,OAAA,CAAAE,SAAA;kBAAAsK,QAAA,gBACExK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAACiC,SAAS,EAAC,MAAM;oBAAAlC,QAAA,GACzCzD,IAAI,CAAC7B,SAAS,IAAI,KAAK,EAAC,KAAG,EAAC6B,IAAI,CAAC3B,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC2B,IAAI,CAAC1B,OAAO,IAAI,KAAK;kBAAA;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACb9K,OAAA;oBAAA2K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN9K,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAACiC,SAAS,EAAC,MAAM;oBAAAlC,QAAA,GAAC,MACvC,EAACzD,IAAI,CAAC4F,mBAAmB,IAAI,KAAK,EAAC,QAAM,EAAC5F,IAAI,CAAC6F,iBAAiB,IAAI,KAAK;kBAAA;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACb9K,OAAA;oBAAA2K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN9K,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAACiC,SAAS,EAAC,MAAM;oBAAAlC,QAAA,GAAC,iBAC5B,EAACzD,IAAI,CAAC0B,aAAa,IAAI,KAAK,EAAC,mBAAiB,EAAC1B,IAAI,CAACK,eAAe,IAAI,GAAG;kBAAA;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC;gBAAA,eACb;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF9K,OAAA,CAAC5B,uBAAuB;gBAAAoM,QAAA,eACtBxK,OAAA,CAAC3C,UAAU;kBAACwP,IAAI,EAAC,KAAK;kBAACjB,OAAO,EAAG5D,CAAC,IAAK;oBACrCA,CAAC,CAAC8E,eAAe,CAAC,CAAC,CAAC,CAAC;oBACrBtL,eAAe,CAACuF,IAAI,CAAC;oBACrBhE,wBAAwB,CAAC,IAAI,CAAC;kBAChC,CAAE;kBAAAyH,QAAA,eACAxK,OAAA,CAAClB,QAAQ;oBAAA6L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACX9K,OAAA,CAAC/C,OAAO;cAAA0N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAxDQ/D,IAAI,CAAChF,OAAO;YAAA4I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyDjB,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMiC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACxL,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEvB,OAAA,CAACzD,GAAG;MAAAiO,QAAA,gBACFxK,OAAA,CAACvD,UAAU;QAACgO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb9K,OAAA,CAACb,eAAe;QACd4H,IAAI,EAAExF,YAAa;QACnByL,OAAO,EAAE,IAAK;QACd/D,KAAK,EAAC;MAA+B;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAEF9K,OAAA,CAACxD,KAAK;QAACuO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBxK,OAAA,CAACvD,UAAU;UAACgO,OAAO,EAAC,WAAW;UAACC,YAAY;UAACK,EAAE,EAAE;YAAEkC,UAAU,EAAE;UAAO,CAAE;UAAAzC,QAAA,EAAC;QAEzE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGb9K,OAAA,CAACpD,IAAI;UAACsO,SAAS;UAACC,OAAO,EAAE,CAAE;UAACJ,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACxCxK,OAAA,CAACpD,IAAI;YAACyO,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC4B,EAAE,EAAE,CAAE;YAAA1C,QAAA,eACvBxK,OAAA,CAACzD,GAAG;cAACwO,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEmC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAO,CAAE;cAAA7C,QAAA,gBACrExK,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEkC,UAAU,EAAE,MAAM;kBAAEtB,KAAK,EAAE;gBAAe,CAAE;gBAAAnB,QAAA,EAAC;cAEhG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9K,OAAA,CAACpD,IAAI;gBAACsO,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAX,QAAA,gBACzBxK,OAAA,CAACpD,IAAI;kBAACyO,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAACM,EAAE,EAAE;sBAAEkC,UAAU,EAAE;oBAAS,CAAE;oBAAAzC,QAAA,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACP9K,OAAA,CAACpD,IAAI;kBAACyO,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAEjJ,YAAY,CAACkH,aAAa,IAAI,KAAK,EAAC,IAAE;kBAAA;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eACP9K,OAAA,CAACpD,IAAI;kBAACyO,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAACM,EAAE,EAAE;sBAAEkC,UAAU,EAAE;oBAAS,CAAE;oBAAAzC,QAAA,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACP9K,OAAA,CAACpD,IAAI;kBAACyO,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfxK,OAAA,CAAC1C,IAAI;oBACHkO,KAAK,EAAEjK,YAAY,CAAC4F,mBAAmB,IAAI,KAAM;oBACjD4E,IAAI,EAAC,OAAO;oBACZJ,KAAK,EAAEhM,kBAAkB,CAAC4B,YAAY,CAAC4F,mBAAmB,CAAE;oBAC5DsD,OAAO,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEP9K,OAAA,CAACpD,IAAI;YAACyO,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC4B,EAAE,EAAE,CAAE;YAAA1C,QAAA,eACvBxK,OAAA,CAACzD,GAAG;cAACwO,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEmC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAO,CAAE;cAAA7C,QAAA,gBACrExK,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEkC,UAAU,EAAE,MAAM;kBAAEtB,KAAK,EAAE;gBAAiB,CAAE;gBAAAnB,QAAA,EAAC;cAElG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZjJ,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,CAAC,MAAM;gBACpE,MAAMiE,MAAM,GAAG7E,MAAM,CAAC6F,IAAI,CAACvB,CAAC,IAAIA,CAAC,CAAC1D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;gBACnE,OAAOiE,MAAM,gBACXlG,OAAA,CAACpD,IAAI;kBAACsO,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAX,QAAA,gBACzBxK,OAAA,CAACpD,IAAI;oBAACyO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;sBAACgO,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEkC,UAAU,EAAE;sBAAS,CAAE;sBAAAzC,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC,eACP9K,OAAA,CAACpD,IAAI;oBAACyO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;sBAACgO,OAAO,EAAC,OAAO;sBAAAD,QAAA,EAAEvH,eAAe,CAACiD,MAAM,CAACjE,SAAS;oBAAC;sBAAA0I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC,eACP9K,OAAA,CAACpD,IAAI;oBAACyO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;sBAACgO,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEkC,UAAU,EAAE;sBAAS,CAAE;sBAAAzC,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC,eACP9K,OAAA,CAACpD,IAAI;oBAACyO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;sBAACgO,OAAO,EAAC,OAAO;sBAAAD,QAAA,GAAEtE,MAAM,CAACN,aAAa,IAAI,CAAC,EAAC,IAAE;oBAAA;sBAAA+E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACP9K,OAAA,CAACpD,IAAI;oBAACyO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfxK,OAAA,CAACvD,UAAU;sBAACgO,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEkC,UAAU,EAAE;sBAAS,CAAE;sBAAAzC,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eACP9K,OAAA,CAACpD,IAAI;oBAACyO,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfxK,OAAA,CAAC1C,IAAI;sBACHkO,KAAK,EAAEtF,MAAM,CAACC,YAAY,IAAI,KAAM;sBACpC4F,IAAI,EAAC,OAAO;sBACZJ,KAAK,EAAE/L,iBAAiB,CAACsG,MAAM,CAACC,YAAY,CAAE;sBAC9CsE,OAAO,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEP9K,OAAA,CAACvD,UAAU;kBAACgO,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAC3D;cACH,CAAC,EAAE,CAAC,gBACF9K,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,OAAO;gBAAAD,QAAA,EACxB3I,QAAQ,CAACI,SAAS,KAAK,cAAc,GACpC,kDAAkD,GAClD;cAA4B;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEP9K,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEuC,EAAE,EAAE,CAAC;YAAErC,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACxBxK,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEkC,UAAU,EAAE;YAAO,CAAE;YAAAzC,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9K,OAAA,CAACtD,SAAS;YACRqP,IAAI,EAAC,OAAO;YACZR,SAAS;YACTC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClBxC,IAAI,EAAC,cAAc;YACnBsF,IAAI,EAAC,QAAQ;YACbrF,KAAK,EAAErG,QAAQ,CAACG,YAAa;YAC7ByJ,QAAQ,EAAE1D,gBAAiB;YAC3BnE,KAAK,EAAE,CAAC,CAAC1B,UAAU,CAACF,YAAa;YACjCwL,UAAU,EAAEtL,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjEyL,mBAAmB,EAAE;cACnB1C,EAAE,EAAE;gBAAEY,KAAK,EAAEvJ,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACF+I,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL1I,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,IAAI,CAAC2G,iBAAiB,iBAC1E3I,OAAA,CAAC9C,KAAK;UAACiP,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EACrCpI,YAAY,CAACJ;QAAY;UAAA2I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACR,eAED9K,OAAA,CAAC9C,KAAK;UAACiP,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,eACnCxK,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAC;UAE5B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM4C,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,MAAMC,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAIxN,UAAU,KAAKwN,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAI3H,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAACrE,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAOuG,UAAU,CAACrC,MAAM,CAACN,aAAa,CAAC,IAAI2C,UAAU,CAAC1G,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAM8L,uBAAuB,GAAI9F,CAAC,IAAK;MACrC,MAAM4F,YAAY,GAAG5F,CAAC,CAACG,MAAM,CAACD,KAAK,CAACrB,IAAI,CAAC,CAAC;;MAE1C;MACA,IAAI+G,YAAY,CAAC5G,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtClF,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb6L,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIH,YAAY,EAAE;QAChB;QACA,MAAMI,gBAAgB,GAAGL,iBAAiB,CAACC,YAAY,CAAC;;QAExD;QACA,MAAMK,eAAe,GAAG5M,MAAM,CAAC6F,IAAI,CAACvB,CAAC,IAAIA,CAAC,CAAC1D,SAAS,KAAK+L,gBAAgB,CAAC;QAE1E,IAAIC,eAAe,EAAE;UACnB;UACA,IAAIA,eAAe,CAAC9H,YAAY,KAAK,MAAM,IAAI8H,eAAe,CAAC9H,YAAY,KAAK,WAAW,EAAE;YAC3FhE,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb6L,eAAe,EAAE,aAAaH,YAAY,eAAeK,eAAe,CAAC9H,YAAY;YACvF,CAAC,CAAC;YACF;UACF;;UAEA;UACA,IAAI5E,YAAY,EAAE;YAChB;YACA,MAAM6E,aAAa,GAAGC,MAAM,CAAC9E,YAAY,CAAC2D,SAAS,IAAI,EAAE,CAAC;YAC1D,MAAMoB,cAAc,GAAGD,MAAM,CAAC9E,YAAY,CAAC6D,YAAY,IAAI,GAAG,CAAC;YAC/D,MAAMmB,WAAW,GAAGF,MAAM,CAAC9E,YAAY,CAAC8D,OAAO,IAAI,GAAG,CAAC;YAEvD,MAAMmB,eAAe,GAAGH,MAAM,CAAC4H,eAAe,CAAC/I,SAAS,IAAI,EAAE,CAAC;YAC/D,MAAMuB,gBAAgB,GAAGJ,MAAM,CAAC4H,eAAe,CAAC7I,YAAY,IAAI,GAAG,CAAC;YACpE,MAAMsB,aAAa,GAAGL,MAAM,CAAC4H,eAAe,CAAC5I,OAAO,IAAI,GAAG,CAAC;;YAE5D;YACA/B,OAAO,CAACC,GAAG,CAAC,iCAAiC0K,eAAe,CAAChM,SAAS,GAAG,EAAE;cACzEiD,SAAS,EAAE,GAAGsB,eAAe,QAAQJ,aAAa,EAAE;cACpDhB,YAAY,EAAE,GAAGqB,gBAAgB,QAAQH,cAAc,EAAE;cACzDjB,OAAO,EAAE,GAAGqB,aAAa,QAAQH,WAAW;YAC9C,CAAC,CAAC;YAEF,IAAIC,eAAe,KAAKJ,aAAa,IACjCK,gBAAgB,KAAKH,cAAc,IACnCI,aAAa,KAAKH,WAAW,EAAE;cACjC;cACA9D,mBAAmB,CAACwL,eAAe,CAAC;cACpC1L,6BAA6B,CAAC,IAAI,CAAC;cACnC;YACF;UACF;;UAEA;UACA,IAAIsL,mBAAmB,CAACI,eAAe,CAAC,EAAE;YACxC;YACAnM,WAAW,CAAC;cACV,GAAGD,QAAQ;cACXI,SAAS,EAAE+L;YACb,CAAC,CAAC;YACF7L,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb6L,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACA5L,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb6L,eAAe,EAAE,aAAaH,YAAY,sCAAsCK,eAAe,CAACrI,aAAa;YAC/G,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACAzD,aAAa,CAAC;YACZ,GAAGD,UAAU;YACb6L,eAAe,EAAE,UAAUH,YAAY;UACzC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACA9L,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb6L,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,oBACE/N,OAAA,CAACzD,GAAG;MAAAiO,QAAA,gBACFxK,OAAA,CAACvD,UAAU;QAACgO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb9K,OAAA,CAACxD,KAAK;QAACuO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBxK,OAAA,CAACvD,UAAU;UAACgO,OAAO,EAAC,OAAO;UAACyD,SAAS;UAAA1D,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZjK,aAAa,gBACZb,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DxK,OAAA,CAAC7C,gBAAgB;YAAAwN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAEN9K,OAAA,CAACzD,GAAG;UAAAiO,QAAA,gBACFxK,OAAA,CAACpD,IAAI;YAACsO,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBAEzBxK,OAAA,CAACpD,IAAI;cAACyO,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC4B,EAAE,EAAE,CAAE;cAAA1C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEkC,UAAU,EAAE;gBAAO,CAAE;gBAAAzC,QAAA,EAAC;cAEzE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9K,OAAA,CAACtD,SAAS;gBACRqP,IAAI,EAAC,OAAO;gBACZR,SAAS;gBACTC,KAAK,EAAC,eAAe;gBACrBf,OAAO,EAAC,UAAU;gBAClBiB,WAAW,EAAC,oBAAoB;gBAChC8B,UAAU,EAAEtL,UAAU,CAAC6L,eAAe,IAAI,uCAAwC;gBAClFnK,KAAK,EAAE,CAAC,CAAC1B,UAAU,CAAC6L,eAAgB;gBACpCI,MAAM,EAAEL,uBAAwB;gBAChC/C,EAAE,EAAE;kBAAEE,EAAE,EAAE;gBAAE;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACF9K,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,OAAO;gBAACM,EAAE,EAAE;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAT,QAAA,GAAC,aAC9B,eAAAxK,OAAA;kBAAAwK,QAAA,EAAS3I,QAAQ,CAACI,SAAS,IAAI;gBAAG;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAGP9K,OAAA,CAACpD,IAAI;cAACyO,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC4B,EAAE,EAAE,CAAE;cAAA1C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEkC,UAAU,EAAE;gBAAO,CAAE;gBAAAzC,QAAA,EAAC;cAEzE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9K,OAAA,CAACzD,GAAG;gBAAAiO,QAAA,gBACFxK,OAAA,CAACvD,UAAU;kBAACgO,OAAO,EAAC,WAAW;kBAACC,YAAY;kBAACK,EAAE,EAAE;oBAAEY,KAAK,EAAE,cAAc;oBAAEsB,UAAU,EAAE,MAAM;oBAAEhC,EAAE,EAAE;kBAAE,CAAE;kBAAAT,QAAA,EACnGjJ,YAAY,GAAG,4CAA4C,GAAG;gBAAsB;kBAAAoJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CAAC,eAEb9K,OAAA,CAACnD,WAAW;kBAAC0O,SAAS;kBAACQ,IAAI,EAAC,OAAO;kBAACnI,KAAK,EAAE,CAAC,CAAC1B,UAAU,CAACD,SAAU;kBAAAuI,QAAA,gBAChExK,OAAA,CAAClD,UAAU;oBAACsR,EAAE,EAAC,qBAAqB;oBAAA5D,QAAA,EAAC;kBAAgB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClE9K,OAAA,CAACjD,MAAM;oBACLsR,OAAO,EAAC,qBAAqB;oBAC7BD,EAAE,EAAC,eAAe;oBAClBnG,IAAI,EAAC,WAAW;oBAChBC,KAAK,EAAErG,QAAQ,CAACI,SAAU;oBAC1BuJ,KAAK,EAAC,kBAAkB;oBACxBC,QAAQ,EAAE1D,gBAAiB;oBAAAyC,QAAA,gBAE3BxK,OAAA,CAAChD,QAAQ;sBAACkL,KAAK,EAAC,cAAc;sBAAAsC,QAAA,gBAC5BxK,OAAA;wBAAAwK,QAAA,EAAQ;sBAAY;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,+BAC/B;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eACX9K,OAAA,CAAC/C,OAAO;sBAAA0N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACVzJ,MAAM,CAACqC,MAAM,GAAG,CAAC,gBAChB1D,OAAA,CAACzD,GAAG;sBAACmQ,SAAS,EAAC,IAAI;sBAAC3B,EAAE,EAAE;wBAAEC,CAAC,EAAE,CAAC;wBAAEmC,OAAO,EAAE;sBAAmB,CAAE;sBAAA3C,QAAA,eAC5DxK,OAAA,CAACvD,UAAU;wBAACgO,OAAO,EAAC,SAAS;wBAACM,EAAE,EAAE;0BAAEkC,UAAU,EAAE,MAAM;0BAAEtB,KAAK,EAAE;wBAAe,CAAE;wBAAAnB,QAAA,GAC7EnJ,MAAM,CAACqC,MAAM,EAAC,6BACjB;sBAAA;wBAAAiH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,GACJ,IAAI,EACPzJ,MAAM,CAACsF,GAAG,CAAET,MAAM,iBACjBlG,OAAA,CAAChD,QAAQ;sBAEPkL,KAAK,EAAEhC,MAAM,CAACjE,SAAU;sBACxB4J,QAAQ,EAAE3F,MAAM,CAACN,aAAa,GAAG2C,UAAU,CAAC1G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;sBACxE+I,EAAE,EAAE;wBACF,gBAAgB,EAAE;0BAAEoC,OAAO,EAAE;wBAAgB,CAAC;wBAC9C,sBAAsB,EAAE;0BAAEA,OAAO,EAAE;wBAAgB,CAAC;wBACpDA,OAAO,EAAE5L,YAAY,IACd2E,MAAM,CAAChB,SAAS,KAAK3D,YAAY,CAAC2D,SAAS,IAC3CmB,MAAM,CAACH,MAAM,CAACd,YAAY,CAAC,KAAKiB,MAAM,CAAC9E,YAAY,CAAC6D,YAAY,CAAC,IACjEiB,MAAM,CAACH,MAAM,CAACb,OAAO,CAAC,KAAKgB,MAAM,CAAC9E,YAAY,CAAC8D,OAAO,CAAC,GACvD,yBAAyB,GAAG;sBACrC,CAAE;sBAAAmF,QAAA,eAEFxK,OAAA,CAACzD,GAAG;wBAACwO,EAAE,EAAE;0BAAEiB,OAAO,EAAE,MAAM;0BAAEsC,aAAa,EAAE,QAAQ;0BAAEC,KAAK,EAAE;wBAAO,CAAE;wBAAA/D,QAAA,gBACnExK,OAAA,CAACzD,GAAG;0BAACwO,EAAE,EAAE;4BAAEiB,OAAO,EAAE,MAAM;4BAAEC,cAAc,EAAE,eAAe;4BAAEb,UAAU,EAAE,QAAQ;4BAAEmD,KAAK,EAAE;0BAAO,CAAE;0BAAA/D,QAAA,gBACjGxK,OAAA,CAACvD,UAAU;4BAACgO,OAAO,EAAC,OAAO;4BAACM,EAAE,EAAE;8BAAEkC,UAAU,EAAE;4BAAO,CAAE;4BAAAzC,QAAA,GACpDvH,eAAe,CAACiD,MAAM,CAACjE,SAAS,CAAC,EAAC,KAAG,EAACiE,MAAM,CAAChB,SAAS,IAAI,KAAK;0BAAA;4BAAAyF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtD,CAAC,EACZvJ,YAAY,IACZ2E,MAAM,CAAChB,SAAS,KAAK3D,YAAY,CAAC2D,SAAS,IAC3CmB,MAAM,CAACH,MAAM,CAACd,YAAY,CAAC,KAAKiB,MAAM,CAAC9E,YAAY,CAAC6D,YAAY,CAAC,IACjEiB,MAAM,CAACH,MAAM,CAACb,OAAO,CAAC,KAAKgB,MAAM,CAAC9E,YAAY,CAAC8D,OAAO,CAAC,iBACtDrF,OAAA,CAAC1C,IAAI;4BACHyO,IAAI,EAAC,OAAO;4BACZP,KAAK,EAAC,aAAa;4BACnBG,KAAK,EAAC,SAAS;4BACflB,OAAO,EAAC,UAAU;4BAClBM,EAAE,EAAE;8BAAEsC,MAAM,EAAE,EAAE;8BAAEmB,QAAQ,EAAE;4BAAS;0BAAE;4BAAA7D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxC,CACF;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACN9K,OAAA,CAACzD,GAAG;0BAACwO,EAAE,EAAE;4BAAEiB,OAAO,EAAE,MAAM;4BAAEC,cAAc,EAAE,eAAe;4BAAEsC,KAAK,EAAE;0BAAO,CAAE;0BAAA/D,QAAA,gBAC3ExK,OAAA,CAACvD,UAAU;4BAACgO,OAAO,EAAC,SAAS;4BAAAD,QAAA,GAC1BtE,MAAM,CAACd,YAAY,IAAI,KAAK,EAAC,KAAG,EAACc,MAAM,CAACb,OAAO,IAAI,KAAK;0BAAA;4BAAAsF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/C,CAAC,eACb9K,OAAA,CAACvD,UAAU;4BAACgO,OAAO,EAAC,SAAS;4BAACM,EAAE,EAAE;8BAAEkC,UAAU,EAAE,MAAM;8BAAEtB,KAAK,EAAEzF,MAAM,CAACN,aAAa,GAAG2C,UAAU,CAAC1G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;4BAAe,CAAE;4BAAAwI,QAAA,GAC5JtE,MAAM,CAACN,aAAa,IAAI,CAAC,EAAC,gBAC7B;0BAAA;4BAAA+E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAvCD5E,MAAM,CAACjE,SAAS;sBAAA0I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAwCb,CACX,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eACT9K,OAAA,CAAC5C,cAAc;oBAAAoN,QAAA,EACZtI,UAAU,CAACD,SAAS,IAAI;kBAAsD;oBAAA0I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAEbzJ,MAAM,CAACqC,MAAM,KAAK,CAAC,IAAI,CAAC7C,aAAa,iBACpCb,OAAA,CAAC9C,KAAK;kBAACiP,QAAQ,EAAC,SAAS;kBAACpB,EAAE,EAAE;oBAAEuC,EAAE,EAAE,CAAC;oBAAEkB,QAAQ,EAAE;kBAAS,CAAE;kBAAAhE,QAAA,EAAC;gBAE7D;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP9K,OAAA,CAAC9C,KAAK;YAACiP,QAAQ,EAAC,MAAM;YAACpB,EAAE,EAAE;cAAEuC,EAAE,EAAE;YAAE,CAAE;YAAA9C,QAAA,eACnCxK,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,iGACvB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,EAGA,CAACjK,aAAa,IAAIgB,QAAQ,CAACI,SAAS,iBACnCjC,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEuC,EAAE,EAAE,CAAC;YAAEtC,CAAC,EAAE,CAAC;YAAEmC,OAAO,EAAE,kBAAkB;YAAEC,YAAY,EAAE,CAAC;YAAEqB,MAAM,EAAE;UAAoB,CAAE;UAAAjE,QAAA,gBAClGxK,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAF,QAAA,EAAC;UAE7C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ,CAAC,MAAM;YACN,MAAM5E,MAAM,GAAG7E,MAAM,CAAC6F,IAAI,CAACvB,CAAC,IAAIA,CAAC,CAAC1D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,IAAIiE,MAAM,EAAE;cACV,oBACElG,OAAA,CAACpD,IAAI;gBAACsO,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAX,QAAA,gBACzBxK,OAAA,CAACpD,IAAI;kBAACyO,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAAC4B,EAAE,EAAE,CAAE;kBAAA1C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBxK,OAAA;sBAAAwK,QAAA,EAAQ;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC7H,eAAe,CAACiD,MAAM,CAACjE,SAAS,CAAC;kBAAA;oBAAA0I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACb9K,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBxK,OAAA;sBAAAwK,QAAA,EAAQ;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5E,MAAM,CAAChB,SAAS,IAAI,KAAK;kBAAA;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACb9K,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBxK,OAAA;sBAAAwK,QAAA,EAAQ;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5E,MAAM,CAACd,YAAY,IAAI,KAAK,EAAC,KAAG,EAACc,MAAM,CAACb,OAAO,IAAI,KAAK;kBAAA;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACP9K,OAAA,CAACpD,IAAI;kBAACyO,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAAC4B,EAAE,EAAE,CAAE;kBAAA1C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBxK,OAAA;sBAAAwK,QAAA,EAAQ;oBAAa;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5E,MAAM,CAACwI,YAAY,IAAI,CAAC,EAAC,IAC3D;kBAAA;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb9K,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBxK,OAAA;sBAAAwK,QAAA,EAAQ;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5E,MAAM,CAACN,aAAa,IAAI,CAAC,EAAC,IAC7D;kBAAA;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb9K,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBxK,OAAA;sBAAAwK,QAAA,EAAQ;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC5E,MAAM,CAACC,YAAY,IAAI,KAAK;kBAAA;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAEX;YACA,oBACE9K,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAACkB,KAAK,EAAC,OAAO;cAAAnB,QAAA,EAAC;YAE1C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAEjB,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEAzJ,MAAM,CAACqC,MAAM,KAAK,CAAC,IAAI,CAAC7C,aAAa,iBACpCb,OAAA,CAAC9C,KAAK;UAACiP,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,EAAC;QAEzC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM6D,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,IAAIf,YAAY,GAAG,SAAS;IAC5B,IAAIgB,UAAU,GAAG,IAAI;IAErB,IAAI/M,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzC2L,YAAY,GAAG,cAAc;IAC/B,CAAC,MAAM,IAAI/L,QAAQ,CAACI,SAAS,EAAE;MAC7B2L,YAAY,GAAG3K,eAAe,CAACpB,QAAQ,CAACI,SAAS,CAAC;MAClD;MACA2M,UAAU,GAAGvN,MAAM,CAAC6F,IAAI,CAACvB,CAAC,IAAIA,CAAC,CAAC1D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IACnE;;IAEA;IACA,MAAM6H,kBAAkB,GAAGH,2BAA2B,CAACpB,UAAU,CAAC1G,QAAQ,CAACG,YAAY,CAAC,EAAET,YAAY,CAACkH,aAAa,CAAC;IAErH,oBACEzI,OAAA,CAACzD,GAAG;MAAAiO,QAAA,gBACFxK,OAAA,CAACvD,UAAU;QAACgO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb9K,OAAA,CAACxD,KAAK;QAACuO,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBxK,OAAA,CAACvD,UAAU;UAACgO,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGb9K,OAAA,CAACb,eAAe;UACd4H,IAAI,EAAExF,YAAa;UACnByL,OAAO,EAAE,IAAK;UACd/D,KAAK,EAAC;QAAmB;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGF9K,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEuC,EAAE,EAAE,CAAC;YAAEtC,CAAC,EAAE,CAAC;YAAEmC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBAC5DxK,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEkC,UAAU,EAAE;YAAO,CAAE;YAAAzC,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9K,OAAA,CAACpD,IAAI;YAACsO,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBACzBxK,OAAA,CAACpD,IAAI;cAACyO,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC4B,EAAE,EAAE,CAAE;cAAA1C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBxK,OAAA;kBAAAwK,QAAA,EAAQ;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjJ,QAAQ,CAACG,YAAY,EAAC,IACxD;cAAA;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9K,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBxK,OAAA;kBAAAwK,QAAA,EAAQ;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChB,kBAAkB;cAAA;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACP9K,OAAA,CAACpD,IAAI;cAACyO,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC4B,EAAE,EAAE,CAAE;cAAA1C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBxK,OAAA;kBAAAwK,QAAA,EAAQ;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC8C,YAAY;cAAA;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACZ8D,UAAU,iBACT5O,OAAA,CAACvD,UAAU;gBAACgO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBxK,OAAA;kBAAAwK,QAAA,EAAQ;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC8D,UAAU,CAAChJ,aAAa,EAAC,IACnE;cAAA;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAEL8D,UAAU,IAAIrG,UAAU,CAAC1G,QAAQ,CAACG,YAAY,CAAC,GAAGuG,UAAU,CAACqG,UAAU,CAAChJ,aAAa,CAAC,IAAI,CAAC+C,iBAAiB,iBAC3G3I,OAAA,CAAC9C,KAAK;UAACiP,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,gBACtCxK,OAAA;YAAAwK,QAAA,EAAQ;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qBAAiB,EAACjJ,QAAQ,CAACG,YAAY,EAAC,4CAA0C,EAAC4M,UAAU,CAAChJ,aAAa,EAAC,gDAE1I;QAAA;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAED9K,OAAA,CAAC9C,KAAK;UAACiP,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,GAAC,8EAEpC,EAAC3I,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,gFAAgF;QAAA;UAAA0I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM+D,cAAc,GAAIC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAOvE,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOmD,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOX,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAO4B,WAAW,CAAC,CAAC;MAAE;MACxB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;;EAED;EACA,MAAMI,4BAA4B,GAAGA,CAAA,KAAM;IACzCpM,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMmM,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIpM,eAAe,EAAE;MACnBpC,QAAQ,CAAC,mCAAmCJ,UAAU,IAAIwC,eAAe,CAACb,OAAO,EAAE,CAAC;IACtF;IACAgN,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,4BAA4B,CAAC,CAAC;IAC9B;IACAvN,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBR,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMgO,iCAAiC,GAAGA,CAAA,KAAM;IAC9C3M,6BAA6B,CAAC,KAAK,CAAC;IACpCE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM0M,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI,CAAC5N,YAAY,IAAI,CAACiB,gBAAgB,EAAE;IAExC,IAAI;MACF9B,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAM1B,WAAW,CAACoQ,qBAAqB,CAAChP,UAAU,EAAEmB,YAAY,CAACQ,OAAO,EAAES,gBAAgB,CAAC;;MAE3F;MACA,MAAMoF,WAAW,GAAG,MAAM5I,WAAW,CAACqQ,WAAW,CAACjP,UAAU,EAAEmB,YAAY,CAACQ,OAAO,CAAC;MACnFP,eAAe,CAACoG,WAAW,CAAC;;MAE5B;MACA9F,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXI,SAAS,EAAEO,gBAAgB,CAACP;MAC9B,CAAC,CAAC;MAEF5B,SAAS,CAAC,4BAA4BkB,YAAY,CAACQ,OAAO,6CAA6CS,gBAAgB,CAACP,SAAS,EAAE,CAAC;MACpIiN,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOtL,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iEAAiE,EAAEA,KAAK,CAAC;MACvFtD,OAAO,CAAC,kEAAkE,IAAIsD,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACvH,CAAC,SAAS;MACRtD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4O,uBAAuB,GAAGA,CAAA,KAAM;IACpCJ,iCAAiC,CAAC,CAAC;IACnC;IACApN,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,oBACEjC,OAAA,CAACzD,GAAG;IAAAiO,QAAA,gBAEFxK,OAAA,CAACxD,KAAK;MAACuO,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACzBxK,OAAA,CAACvD,UAAU;QAACgO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb9K,OAAA,CAACpD,IAAI;QAACsO,SAAS;QAACC,OAAO,EAAE,CAAE;QAACC,UAAU,EAAC,QAAQ;QAAAZ,QAAA,gBAC7CxK,OAAA,CAACpD,IAAI;UAACyO,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAd,QAAA,eACfxK,OAAA,CAACtD,SAAS;YACR6O,SAAS;YACTC,KAAK,EAAC,SAAS;YACff,OAAO,EAAC,UAAU;YAClBvC,KAAK,EAAEzG,WAAY;YACnBgK,QAAQ,EAAGzD,CAAC,IAAKtG,cAAc,CAACsG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;YAChDwD,WAAW,EAAC;UAAyC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP9K,OAAA,CAACpD,IAAI;UAACyO,IAAI;UAACC,EAAE,EAAE,CAAE;UAAAd,QAAA,eACfxK,OAAA,CAACrD,MAAM;YACL4O,SAAS;YACTd,OAAO,EAAC,WAAW;YACnBkB,KAAK,EAAC,SAAS;YACfC,OAAO,EAAEhF,oBAAqB;YAC9BiF,QAAQ,EAAElL,WAAW,IAAI,CAACc,WAAW,CAACoF,IAAI,CAAC,CAAE;YAC7CiF,SAAS,EAAEnL,WAAW,gBAAGX,OAAA,CAAC7C,gBAAgB;cAAC4O,IAAI,EAAE;YAAG;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG9K,OAAA,CAAC1B,UAAU;cAAAqM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAN,QAAA,EAC1E;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGP7J,iBAAiB,IAAIF,aAAa,CAAC2C,MAAM,GAAG,CAAC,iBAC5C1D,OAAA,CAACxD,KAAK;MAACuO,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,gBACzBxK,OAAA,CAACvD,UAAU;QAACgO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb9K,OAAA,CAAClC,cAAc;QAAA0M,QAAA,eACbxK,OAAA,CAACrC,KAAK;UAACoO,IAAI,EAAC,OAAO;UAAAvB,QAAA,gBACjBxK,OAAA,CAACjC,SAAS;YAAAyM,QAAA,eACRxK,OAAA,CAAChC,QAAQ;cAAC+M,EAAE,EAAE;gBAAEoC,OAAO,EAAE;cAAU,CAAE;cAAA3C,QAAA,gBACnCxK,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpC9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ9K,OAAA,CAACpC,SAAS;YAAA4M,QAAA,EACPzJ,aAAa,CAAC4F,GAAG,CAAEI,IAAI,iBACtB/G,OAAA,CAAChC,QAAQ;cAAAwM,QAAA,gBACPxK,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAEzD,IAAI,CAAChF;cAAO;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrC9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,EAAEzD,IAAI,CAAC7B,SAAS,IAAI;cAAK;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChD9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,GAAEzD,IAAI,CAAC3B,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC2B,IAAI,CAAC1B,OAAO,IAAI,KAAK;cAAA;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7E9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,GAAC,MAAI,EAACzD,IAAI,CAAC4F,mBAAmB,IAAI,KAAK,eAAC3M,OAAA;kBAAA2K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,OAAG,EAAC/D,IAAI,CAAC6F,iBAAiB,IAAI,KAAK;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvG9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,GAAEzD,IAAI,CAAC0B,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACtD9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,eACRxK,OAAA,CAAC1C,IAAI;kBACHkO,KAAK,EAAEzE,IAAI,CAACI,mBAAmB,IAAI,KAAM;kBACzC4E,IAAI,EAAC,OAAO;kBACZJ,KAAK,EAAEhM,kBAAkB,CAACoH,IAAI,CAACI,mBAAmB,CAAE;kBACpDsD,OAAO,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ9K,OAAA,CAACnC,SAAS;gBAAA2M,QAAA,eACRxK,OAAA,CAACrD,MAAM;kBACLoP,IAAI,EAAC,OAAO;kBACZtB,OAAO,EAAC,WAAW;kBACnBkB,KAAK,EAAC,SAAS;kBACfC,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAACP,IAAI,CAAE;kBACtC8E,QAAQ,EAAEnM,gBAAgB,CAACqH,IAAI,CAAE;kBAAAyD,QAAA,EAClC;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAxBC/D,IAAI,CAAChF,OAAO;cAAA4I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACR,EAGAvJ,YAAY,iBACXvB,OAAA,CAACxD,KAAK;MAACuO,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,gBAClBxK,OAAA,CAACvD,UAAU;QAACgO,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb9K,OAAA,CAACzD,GAAG;QAACwO,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEmC,OAAO,EAAE,SAAS;UAAEC,YAAY,EAAE,CAAC;UAAEnC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBAC5DxK,OAAA,CAACvD,UAAU;UAACgO,OAAO,EAAC,WAAW;UAACC,YAAY;UAACK,EAAE,EAAE;YAAEkC,UAAU,EAAE,MAAM;YAAEtB,KAAK,EAAE;UAAe,CAAE;UAAAnB,QAAA,GAAC,oBAC5E,EAACjJ,YAAY,CAACQ,OAAO;QAAA;UAAA4I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACb9K,OAAA,CAACpD,IAAI;UAACsO,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAX,QAAA,gBACzBxK,OAAA,CAACpD,IAAI;YAACyO,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC4B,EAAE,EAAE,CAAE;YAAA1C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvJ,YAAY,CAAC2D,SAAS,IAAI,KAAK;YAAA;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACtG9K,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvJ,YAAY,CAAC6D,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC7D,YAAY,CAAC8D,OAAO,IAAI,KAAK;YAAA;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC5I9K,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvJ,YAAY,CAACkH,aAAa,IAAI,KAAK,EAAC,IAAE;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5G,CAAC,eACP9K,OAAA,CAACpD,IAAI;YAACyO,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC4B,EAAE,EAAE,CAAE;YAAA1C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvJ,YAAY,CAACoL,mBAAmB,IAAI,KAAK;YAAA;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC1H9K,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAACxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACvJ,YAAY,CAACqL,iBAAiB,IAAI,KAAK;YAAA;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACtH9K,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvB9K,OAAA,CAAC1C,IAAI;gBACHkO,KAAK,EAAEjK,YAAY,CAAC4F,mBAAmB,IAAI,KAAM;gBACjD4E,IAAI,EAAC,OAAO;gBACZJ,KAAK,EAAEhM,kBAAkB,CAAC4B,YAAY,CAAC4F,mBAAmB,CAAE;gBAC5DsD,OAAO,EAAC,UAAU;gBAClBM,EAAE,EAAE;kBAAEyB,EAAE,EAAE;gBAAE;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN9K,OAAA,CAACpD,IAAI;QAACsO,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAX,QAAA,gBAEzBxK,OAAA,CAACpD,IAAI;UAACyO,IAAI;UAACC,EAAE,EAAE,EAAG;UAAC4B,EAAE,EAAE,CAAE;UAAA1C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEkC,UAAU,EAAE;YAAO,CAAE;YAAAzC,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9K,OAAA,CAACtD,SAAS;YACRqP,IAAI,EAAC,OAAO;YACZR,SAAS;YACTC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClBxC,IAAI,EAAC,cAAc;YACnBsF,IAAI,EAAC,QAAQ;YACbrF,KAAK,EAAErG,QAAQ,CAACG,YAAa;YAC7ByJ,QAAQ,EAAE1D,gBAAiB;YAC3BnE,KAAK,EAAE,CAAC,CAAC1B,UAAU,CAACF,YAAa;YACjCwL,UAAU,EAAEtL,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjEyL,mBAAmB,EAAE;cACnB1C,EAAE,EAAE;gBAAEY,KAAK,EAAEvJ,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACF+I,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EACD1I,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,iBACpDhC,OAAA,CAAC9C,KAAK;YAACiP,QAAQ,EAAC,SAAS;YAACpB,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,EACrCpI,YAAY,CAACJ;UAAY;YAAA2I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGP9K,OAAA,CAACpD,IAAI;UAACyO,IAAI;UAACC,EAAE,EAAE,EAAG;UAAC4B,EAAE,EAAE,CAAE;UAAA1C,QAAA,gBACvBxK,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEkC,UAAU,EAAE;YAAO,CAAE;YAAAzC,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZjK,aAAa,gBACZb,OAAA,CAACzD,GAAG;YAACwO,EAAE,EAAE;cAAEiB,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA1B,QAAA,eAC5DxK,OAAA,CAAC7C,gBAAgB;cAAAwN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,gBAEN9K,OAAA,CAACzD,GAAG;YAAAiO,QAAA,gBACFxK,OAAA,CAACvD,UAAU;cAACgO,OAAO,EAAC,WAAW;cAACC,YAAY;cAACK,EAAE,EAAE;gBAAEY,KAAK,EAAE,cAAc;gBAAEsB,UAAU,EAAE,MAAM;gBAAEhC,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,EACnGjJ,YAAY,GAAG,4CAA4C,GAAG;YAAsB;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eAEb9K,OAAA,CAACnD,WAAW;cAAC0O,SAAS;cAACQ,IAAI,EAAC,OAAO;cAACnI,KAAK,EAAE,CAAC,CAAC1B,UAAU,CAACD,SAAU;cAAAuI,QAAA,gBAChExK,OAAA,CAAClD,UAAU;gBAACsR,EAAE,EAAC,qBAAqB;gBAAA5D,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClE9K,OAAA,CAACjD,MAAM;gBACLsR,OAAO,EAAC,qBAAqB;gBAC7BD,EAAE,EAAC,eAAe;gBAClBnG,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAErG,QAAQ,CAACI,SAAU;gBAC1BuJ,KAAK,EAAC,kBAAkB;gBACxBC,QAAQ,EAAE1D,gBAAiB;gBAAAyC,QAAA,gBAE3BxK,OAAA,CAAChD,QAAQ;kBAACkL,KAAK,EAAC,cAAc;kBAAAsC,QAAA,gBAC5BxK,OAAA;oBAAAwK,QAAA,EAAQ;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,+BAC/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACX9K,OAAA,CAAC/C,OAAO;kBAAA0N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACVzJ,MAAM,CAACqC,MAAM,GAAG,CAAC,gBAChB1D,OAAA,CAACzD,GAAG;kBAACmQ,SAAS,EAAC,IAAI;kBAAC3B,EAAE,EAAE;oBAAEC,CAAC,EAAE,CAAC;oBAAEmC,OAAO,EAAE;kBAAmB,CAAE;kBAAA3C,QAAA,eAC5DxK,OAAA,CAACvD,UAAU;oBAACgO,OAAO,EAAC,SAAS;oBAACM,EAAE,EAAE;sBAAEkC,UAAU,EAAE,MAAM;sBAAEtB,KAAK,EAAE;oBAAe,CAAE;oBAAAnB,QAAA,GAC7EnJ,MAAM,CAACqC,MAAM,EAAC,6BACjB;kBAAA;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,GACJ,IAAI,EACPzJ,MAAM,CAACsF,GAAG,CAAET,MAAM,iBACjBlG,OAAA,CAAChD,QAAQ;kBAEPkL,KAAK,EAAEhC,MAAM,CAACjE,SAAU;kBACxB4J,QAAQ,EAAE3F,MAAM,CAACN,aAAa,GAAG2C,UAAU,CAAC1G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;kBACxE+I,EAAE,EAAE;oBACF,gBAAgB,EAAE;sBAAEoC,OAAO,EAAE;oBAAgB,CAAC;oBAC9C,sBAAsB,EAAE;sBAAEA,OAAO,EAAE;oBAAgB,CAAC;oBACpDA,OAAO,EAAE5L,YAAY,IACd2E,MAAM,CAAChB,SAAS,KAAK3D,YAAY,CAAC2D,SAAS,IAC3CmB,MAAM,CAACH,MAAM,CAACd,YAAY,CAAC,KAAKiB,MAAM,CAAC9E,YAAY,CAAC6D,YAAY,CAAC,IACjEiB,MAAM,CAACH,MAAM,CAACb,OAAO,CAAC,KAAKgB,MAAM,CAAC9E,YAAY,CAAC8D,OAAO,CAAC,GACvD,yBAAyB,GAAG;kBACrC,CAAE;kBAAAmF,QAAA,eAEFxK,OAAA,CAACzD,GAAG;oBAACwO,EAAE,EAAE;sBAAEiB,OAAO,EAAE,MAAM;sBAAEsC,aAAa,EAAE,QAAQ;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBAAA/D,QAAA,gBACnExK,OAAA,CAACzD,GAAG;sBAACwO,EAAE,EAAE;wBAAEiB,OAAO,EAAE,MAAM;wBAAEC,cAAc,EAAE,eAAe;wBAAEb,UAAU,EAAE,QAAQ;wBAAEmD,KAAK,EAAE;sBAAO,CAAE;sBAAA/D,QAAA,gBACjGxK,OAAA,CAACvD,UAAU;wBAACgO,OAAO,EAAC,OAAO;wBAACM,EAAE,EAAE;0BAAEkC,UAAU,EAAE;wBAAO,CAAE;wBAAAzC,QAAA,GACpDvH,eAAe,CAACiD,MAAM,CAACjE,SAAS,CAAC,EAAC,KAAG,EAACiE,MAAM,CAAChB,SAAS,IAAI,KAAK;sBAAA;wBAAAyF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,EACZvJ,YAAY,IACZ2E,MAAM,CAAChB,SAAS,KAAK3D,YAAY,CAAC2D,SAAS,IAC3CmB,MAAM,CAACH,MAAM,CAACd,YAAY,CAAC,KAAKiB,MAAM,CAAC9E,YAAY,CAAC6D,YAAY,CAAC,IACjEiB,MAAM,CAACH,MAAM,CAACb,OAAO,CAAC,KAAKgB,MAAM,CAAC9E,YAAY,CAAC8D,OAAO,CAAC,iBACtDrF,OAAA,CAAC1C,IAAI;wBACHyO,IAAI,EAAC,OAAO;wBACZP,KAAK,EAAC,aAAa;wBACnBG,KAAK,EAAC,SAAS;wBACflB,OAAO,EAAC,UAAU;wBAClBM,EAAE,EAAE;0BAAEsC,MAAM,EAAE,EAAE;0BAAEmB,QAAQ,EAAE;wBAAS;sBAAE;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CACF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACN9K,OAAA,CAACzD,GAAG;sBAACwO,EAAE,EAAE;wBAAEiB,OAAO,EAAE,MAAM;wBAAEC,cAAc,EAAE,eAAe;wBAAEsC,KAAK,EAAE;sBAAO,CAAE;sBAAA/D,QAAA,gBAC3ExK,OAAA,CAACvD,UAAU;wBAACgO,OAAO,EAAC,SAAS;wBAAAD,QAAA,GAC1BtE,MAAM,CAACd,YAAY,IAAI,KAAK,EAAC,KAAG,EAACc,MAAM,CAACb,OAAO,IAAI,KAAK;sBAAA;wBAAAsF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,eACb9K,OAAA,CAACvD,UAAU;wBAACgO,OAAO,EAAC,SAAS;wBAACM,EAAE,EAAE;0BAAEkC,UAAU,EAAE,MAAM;0BAAEtB,KAAK,EAAEzF,MAAM,CAACN,aAAa,GAAG2C,UAAU,CAAC1G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;wBAAe,CAAE;wBAAAwI,QAAA,GAC5JtE,MAAM,CAACN,aAAa,IAAI,CAAC,EAAC,gBAC7B;sBAAA;wBAAA+E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAvCD5E,MAAM,CAACjE,SAAS;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwCb,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACT9K,OAAA,CAAC5C,cAAc;gBAAAoN,QAAA,EACZtI,UAAU,CAACD,SAAS,IAAI;cAAsD;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAEbzJ,MAAM,CAACqC,MAAM,KAAK,CAAC,IAAI,CAAC7C,aAAa,iBACpCb,OAAA,CAAC9C,KAAK;cAACiP,QAAQ,EAAC,SAAS;cAACpB,EAAE,EAAE;gBAAEuC,EAAE,EAAE,CAAC;gBAAEkB,QAAQ,EAAE;cAAS,CAAE;cAAAhE,QAAA,EAAC;YAE7D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGA,CAACjK,aAAa,IAAIgB,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,CAAC,MAAM;YACvF,MAAMiE,MAAM,GAAG7E,MAAM,CAAC6F,IAAI,CAACvB,CAAC,IAAIA,CAAC,CAAC1D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,IAAIiE,MAAM,EAAE;cACV,oBACElG,OAAA,CAACzD,GAAG;gBAACwO,EAAE,EAAE;kBAAEuC,EAAE,EAAE,CAAC;kBAAEtC,CAAC,EAAE,CAAC;kBAAEmC,OAAO,EAAE,SAAS;kBAAEC,YAAY,EAAE;gBAAE,CAAE;gBAAA5C,QAAA,gBAC5DxK,OAAA,CAACvD,UAAU;kBAACgO,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBAACxK,OAAA;oBAAAwK,QAAA,EAAQ;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC7H,eAAe,CAACiD,MAAM,CAACjE,SAAS,CAAC;gBAAA;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACrG9K,OAAA,CAACvD,UAAU;kBAACgO,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBAACxK,OAAA;oBAAAwK,QAAA,EAAQ;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC5E,MAAM,CAACN,aAAa,IAAI,CAAC,EAAC,IAAE;gBAAA;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtG9K,OAAA,CAACvD,UAAU;kBAACgO,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBACzBxK,OAAA;oBAAAwK,QAAA,EAAQ;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvB9K,OAAA,CAAC1C,IAAI;oBACHkO,KAAK,EAAEtF,MAAM,CAACC,YAAY,IAAI,KAAM;oBACpC4F,IAAI,EAAC,OAAO;oBACZJ,KAAK,EAAE/L,iBAAiB,CAACsG,MAAM,CAACC,YAAY,CAAE;oBAC9CsE,OAAO,EAAC,UAAU;oBAClBM,EAAE,EAAE;sBAAEyB,EAAE,EAAE;oBAAE;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAEV;YACA,OAAO,IAAI;UACb,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP9K,OAAA,CAACzD,GAAG;QAACwO,EAAE,EAAE;UAAEuC,EAAE,EAAE,CAAC;UAAEtB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAzB,QAAA,gBACnExK,OAAA,CAACrD,MAAM;UACL8N,OAAO,EAAC,UAAU;UAClBkB,KAAK,EAAC,WAAW;UACjBC,OAAO,EAAEA,CAAA,KAAM;YACbpK,eAAe,CAAC,IAAI,CAAC;YACrBM,WAAW,CAAC;cACVC,OAAO,EAAE,EAAE;cACXC,YAAY,EAAE,EAAE;cAChBC,SAAS,EAAE;YACb,CAAC,CAAC;UACJ,CAAE;UACF6J,SAAS,eAAE9L,OAAA,CAACtB,UAAU;YAAAiM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Be,QAAQ,EAAEpL,OAAQ;UAAA+J,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET9K,OAAA,CAACrD,MAAM;UACL8N,OAAO,EAAC,WAAW;UACnBkB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAE/B,YAAa;UACtB0F,OAAO,eAAEvP,OAAA,CAACxB,QAAQ;YAAAmM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBe,QAAQ,EAAEpL,OAAO,IAAI,CAACoB,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,SAAU;UAAAuI,QAAA,EAElE/J,OAAO,gBAAGT,OAAA,CAAC7C,gBAAgB;YAAC4O,IAAI,EAAE;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGD9K,OAAA,CAACzC,MAAM;MAACiS,IAAI,EAAE3G,iBAAkB;MAAC4G,OAAO,EAAEA,CAAA,KAAM3G,oBAAoB,CAAC,KAAK,CAAE;MAAC4G,QAAQ,EAAC,IAAI;MAACnE,SAAS;MAAAf,QAAA,gBAClGxK,OAAA,CAACxC,WAAW;QAACuN,EAAE,EAAE;UAAEoC,OAAO,EAAE;QAAgB,CAAE;QAAA3C,QAAA,eAC5CxK,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEuE,GAAG,EAAE;UAAE,CAAE;UAAAnF,QAAA,gBACzDxK,OAAA,CAACpB,WAAW;YAAC+M,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B9K,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAEzB,kBAAkB,CAACE;UAAK;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd9K,OAAA,CAACvC,aAAa;QAAA+M,QAAA,eACZxK,OAAA,CAACvD,UAAU;UAACgO,OAAO,EAAC,OAAO;UAACM,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,EACvCzB,kBAAkB,CAAC/E;QAAO;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChB9K,OAAA,CAACtC,aAAa;QAAA8M,QAAA,gBACZxK,OAAA,CAACrD,MAAM;UAACiP,OAAO,EAAEA,CAAA,KAAM9C,oBAAoB,CAAC,KAAK,CAAE;UAAC6C,KAAK,EAAC,WAAW;UAAClB,OAAO,EAAC,UAAU;UAAAD,QAAA,EAAC;QAEzF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9K,OAAA,CAACrD,MAAM;UACLiP,OAAO,EAAEA,CAAA,KAAM;YACb9C,oBAAoB,CAAC,KAAK,CAAC;YAC3BC,kBAAkB,CAACG,SAAS,CAAC,CAAC;UAChC,CAAE;UACFyC,KAAK,EAAC,SAAS;UACflB,OAAO,EAAC,WAAW;UACnBmF,SAAS;UAAApF,QAAA,EACV;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT9K,OAAA,CAACzC,MAAM;MAACiS,IAAI,EAAE9M,qBAAsB;MAAC+M,OAAO,EAAEV,4BAA6B;MAACW,QAAQ,EAAC,IAAI;MAACnE,SAAS;MAAAf,QAAA,gBACjGxK,OAAA,CAACxC,WAAW;QAACuN,EAAE,EAAE;UAAEoC,OAAO,EAAE;QAAgB,CAAE;QAAA3C,QAAA,eAC5CxK,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEuE,GAAG,EAAE;UAAE,CAAE;UAAAnF,QAAA,gBACzDxK,OAAA,CAACpB,WAAW;YAAC+M,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B9K,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd9K,OAAA,CAACvC,aAAa;QAAA+M,QAAA,EACX5H,eAAe,iBACd5C,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,gBACjBxK,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,OAAO;YAACyD,SAAS;YAAA1D,QAAA,GAAC,UAC5B,eAAAxK,OAAA;cAAAwK,QAAA,EAAS5H,eAAe,CAACb;YAAO;cAAA4I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,4BAAqB,EAAClI,eAAe,CAACwE,eAAe,IAAI,CAAC,EAAC,KAC/G;UAAA;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9K,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,OAAO;YAACyD,SAAS;YAAA1D,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9K,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,OAAO;YAACiC,SAAS,EAAC,IAAI;YAAAlC,QAAA,gBACxCxK,OAAA;cAAAwK,QAAA,EAAI;YAAsC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C9K,OAAA;cAAAwK,QAAA,EAAI;YAAyB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC9K,OAAA;cAAAwK,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChB9K,OAAA,CAACtC,aAAa;QAACqN,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEiB,cAAc,EAAE;QAAgB,CAAE;QAAAzB,QAAA,gBAC3DxK,OAAA,CAACrD,MAAM;UAACiP,OAAO,EAAEmD,4BAA6B;UAACpD,KAAK,EAAC,WAAW;UAAAnB,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9K,OAAA,CAACzD,GAAG;UAAAiO,QAAA,gBACFxK,OAAA,CAACrD,MAAM;YAACiP,OAAO,EAAEqD,wBAAyB;YAACtD,KAAK,EAAC,SAAS;YAACZ,EAAE,EAAE;cAAE8E,EAAE,EAAE;YAAE,CAAE;YAAArF,QAAA,EAAC;UAE1E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9K,OAAA,CAACrD,MAAM;YAACiP,OAAO,EAAEoD,gBAAiB;YAACvE,OAAO,EAAC,WAAW;YAACkB,KAAK,EAAC,SAAS;YAAAnB,QAAA,EAAC;UAEvE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT9K,OAAA,CAACd,sBAAsB;MACrBsQ,IAAI,EAAElN,0BAA2B;MACjCmN,OAAO,EAAEP,iCAAkC;MAC3CnI,IAAI,EAAExF,YAAa;MACnB2E,MAAM,EAAE1D,gBAAiB;MACzBsN,YAAY,EAAEX,2BAA4B;MAC1CY,mBAAmB,EAAET;IAAwB;MAAA3E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAGF9K,OAAA,CAACzC,MAAM;MACLiS,IAAI,EAAE1M,qBAAsB;MAC5B2M,OAAO,EAAEA,CAAA,KAAM1M,wBAAwB,CAAC,KAAK,CAAE;MAC/C2M,QAAQ,EAAC,IAAI;MACbnE,SAAS;MAAAf,QAAA,gBAETxK,OAAA,CAACxC,WAAW;QAAAgN,QAAA,eACVxK,OAAA,CAACzD,GAAG;UAACwO,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEuE,GAAG,EAAE;UAAE,CAAE;UAAAnF,QAAA,gBACzDxK,OAAA,CAAClB,QAAQ;YAAC6M,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5B9K,OAAA,CAACvD,UAAU;YAACgO,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd9K,OAAA,CAACvC,aAAa;QAAA+M,QAAA,eACZxK,OAAA,CAACb,eAAe;UAAC4H,IAAI,EAAExF;QAAa;UAAAoJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAChB9K,OAAA,CAACtC,aAAa;QAAA8M,QAAA,eACZxK,OAAA,CAACrD,MAAM;UAACiP,OAAO,EAAEA,CAAA,KAAM7I,wBAAwB,CAAC,KAAK,CAAE;UAAC4I,KAAK,EAAC,SAAS;UAAAnB,QAAA,EAAC;QAExE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACvK,EAAA,CAp+DIJ,kBAAkB;EAAA,QACLpB,WAAW;AAAA;AAAAiR,EAAA,GADxB7P,kBAAkB;AAs+DxB,eAAeA,kBAAkB;AAAC,IAAA6P,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}