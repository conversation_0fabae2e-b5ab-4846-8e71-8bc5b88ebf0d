{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['ie.', 'isz.'],\n  abbreviated: ['i. e.', 'i. sz.'],\n  wide: ['<PERSON><PERSON><PERSON>', 'id<PERSON>számításunk szerint']\n};\nvar quarterValues = {\n  narrow: ['1.', '2.', '3.', '4.'],\n  abbreviated: ['1. n.év', '2. n.év', '3. n.év', '4. n.év'],\n  wide: ['1. negyedév', '2. negyedév', '3. negyedév', '4. negyedév']\n};\nvar formattingQuarterValues = {\n  narrow: ['I.', 'II.', 'III.', 'IV.'],\n  abbreviated: ['I. n.év', 'II. n.év', 'III. n.év', 'IV. n.év'],\n  wide: ['I. negyedév', 'II. negyedév', 'III. negyedév', 'IV. negyedév']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'Á', 'M', 'J', 'J', 'A', 'Sz', 'O', 'N', 'D'],\n  abbreviated: ['jan.', 'febr.', 'm<PERSON>rc.', 'ápr.', 'm<PERSON>j.', 'jún.', 'júl.', 'aug.', 'szept.', 'okt.', 'nov.', 'dec.'],\n  wide: ['január', 'febru<PERSON>r', 'm<PERSON>rcius', 'április', 'május', 'június', 'július', 'augusztus', 'szeptember', 'október', 'november', 'december']\n};\nvar dayValues = {\n  narrow: ['V', 'H', 'K', 'Sz', 'Cs', 'P', 'Sz'],\n  short: ['V', 'H', 'K', 'Sze', 'Cs', 'P', 'Szo'],\n  abbreviated: ['V', 'H', 'K', 'Sze', 'Cs', 'P', 'Szo'],\n  wide: ['vasárnap', 'hétfő', 'kedd', 'szerda', 'csütörtök', 'péntek', 'szombat']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'de.',\n    pm: 'du.',\n    midnight: 'éjfél',\n    noon: 'dél',\n    morning: 'reggel',\n    afternoon: 'du.',\n    evening: 'este',\n    night: 'éjjel'\n  },\n  abbreviated: {\n    am: 'de.',\n    pm: 'du.',\n    midnight: 'éjfél',\n    noon: 'dél',\n    morning: 'reggel',\n    afternoon: 'du.',\n    evening: 'este',\n    night: 'éjjel'\n  },\n  wide: {\n    am: 'de.',\n    pm: 'du.',\n    midnight: 'éjfél',\n    noon: 'dél',\n    morning: 'reggel',\n    afternoon: 'délután',\n    evening: 'este',\n    night: 'éjjel'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    },\n    formattingValues: formattingQuarterValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "formattingQuarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "formattingValues", "defaultFormattingWidth", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/hu/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['ie.', 'isz.'],\n  abbreviated: ['i. e.', 'i. sz.'],\n  wide: ['<PERSON><PERSON><PERSON>', 'id<PERSON>számításunk szerint']\n};\nvar quarterValues = {\n  narrow: ['1.', '2.', '3.', '4.'],\n  abbreviated: ['1. n.év', '2. n.év', '3. n.év', '4. n.év'],\n  wide: ['1. negyedév', '2. negyedév', '3. negyedév', '4. negyedév']\n};\nvar formattingQuarterValues = {\n  narrow: ['I.', 'II.', 'III.', 'IV.'],\n  abbreviated: ['I. n.év', 'II. n.év', 'III. n.év', 'IV. n.év'],\n  wide: ['I. negyedév', 'II. negyedév', 'III. negyedév', 'IV. negyedév']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'Á', 'M', 'J', 'J', 'A', 'Sz', 'O', 'N', 'D'],\n  abbreviated: ['jan.', 'febr.', 'm<PERSON>rc.', 'ápr.', 'm<PERSON>j.', 'jún.', 'júl.', 'aug.', 'szept.', 'okt.', 'nov.', 'dec.'],\n  wide: ['január', 'febru<PERSON>r', 'm<PERSON>rcius', 'április', 'május', 'június', 'július', 'augusztus', 'szeptember', 'október', 'november', 'december']\n};\nvar dayValues = {\n  narrow: ['V', 'H', 'K', 'Sz', 'Cs', 'P', 'Sz'],\n  short: ['V', 'H', 'K', 'Sze', 'Cs', 'P', 'Szo'],\n  abbreviated: ['V', 'H', 'K', 'Sze', 'Cs', 'P', 'Szo'],\n  wide: ['vasárnap', 'hétfő', 'kedd', 'szerda', 'csütörtök', 'péntek', 'szombat']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'de.',\n    pm: 'du.',\n    midnight: 'éjfél',\n    noon: 'dél',\n    morning: 'reggel',\n    afternoon: 'du.',\n    evening: 'este',\n    night: 'éjjel'\n  },\n  abbreviated: {\n    am: 'de.',\n    pm: 'du.',\n    midnight: 'éjfél',\n    noon: 'dél',\n    morning: 'reggel',\n    afternoon: 'du.',\n    evening: 'este',\n    night: 'éjjel'\n  },\n  wide: {\n    am: 'de.',\n    pm: 'du.',\n    midnight: 'éjfél',\n    noon: 'dél',\n    morning: 'reggel',\n    afternoon: 'délután',\n    evening: 'este',\n    night: 'éjjel'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    },\n    formattingValues: formattingQuarterValues,\n    defaultFormattingWidth: 'wide'\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;EACvBC,WAAW,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;EAChCC,IAAI,EAAE,CAAC,gBAAgB,EAAE,wBAAwB;AACnD,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAChCC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EACzDC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AACD,IAAIE,uBAAuB,GAAG;EAC5BJ,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;EACpCC,WAAW,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC;EAC7DC,IAAI,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,eAAe,EAAE,cAAc;AACvE,CAAC;AACD,IAAIG,WAAW,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACrEC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EACjHC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU;AAC7I,CAAC;AACD,IAAII,SAAS,GAAG;EACdN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC;EAC9CO,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC;EAC/CN,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC;EACrDC,IAAI,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS;AAChF,CAAC;AACD,IAAIM,eAAe,GAAG;EACpBR,MAAM,EAAE;IACNS,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,KAAK;IACTC,EAAE,EAAE,KAAK;IACTC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,OAAOE,MAAM,GAAG,GAAG;AACrB,CAAC;AACD,IAAIE,QAAQ,GAAG;EACbL,aAAa,EAAEA,aAAa;EAC5BM,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB,CAAC;IACDE,gBAAgB,EAAExB,uBAAuB;IACzCyB,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EACFC,KAAK,EAAEhC,eAAe,CAAC;IACrB0B,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFM,GAAG,EAAEjC,eAAe,CAAC;IACnB0B,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFO,SAAS,EAAElC,eAAe,CAAC;IACzB0B,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}