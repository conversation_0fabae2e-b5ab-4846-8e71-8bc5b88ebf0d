{"ast": null, "code": "import { formatDistance } from \"./it/_lib/formatDistance.js\";\nimport { formatRelative } from \"./it/_lib/formatRelative.js\";\nimport { localize } from \"./it/_lib/localize.js\";\nimport { match } from \"./it/_lib/match.js\";\nimport { formatLong } from \"./it-CH/_lib/formatLong.js\";\n\n/**\n * @category Locales\n * @summary Italian locale (Switzerland).\n * @language Italian\n * @iso-639-2 ita\n * <AUTHOR> [@maic66](https://github.com/maic66)\n */\nexport const itCH = {\n  code: \"it-CH\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default itCH;", "map": {"version": 3, "names": ["formatDistance", "formatRelative", "localize", "match", "formatLong", "itCH", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/it-CH.js"], "sourcesContent": ["import { formatDistance } from \"./it/_lib/formatDistance.js\";\nimport { formatRelative } from \"./it/_lib/formatRelative.js\";\nimport { localize } from \"./it/_lib/localize.js\";\nimport { match } from \"./it/_lib/match.js\";\nimport { formatLong } from \"./it-CH/_lib/formatLong.js\";\n\n/**\n * @category Locales\n * @summary Italian locale (Switzerland).\n * @language Italian\n * @iso-639-2 ita\n * <AUTHOR> [@maic66](https://github.com/maic66)\n */\nexport const itCH = {\n  code: \"it-CH\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default itCH;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,UAAU,QAAQ,4BAA4B;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BI,UAAU,EAAEA,UAAU;EACtBH,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZI,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}