{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nconst API_URL = config.API_URL;\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst cantieriService = {\n  // Ottiene la lista di tutti i cantieri dell'utente corrente\n  getMyCantieri: async () => {\n    try {\n      const response = await axiosInstance.get('/cantieri');\n      return response.data;\n    } catch (error) {\n      console.error('Get cantieri error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea un nuovo cantiere\n  createCantiere: async cantiereData => {\n    try {\n      const response = await axiosInstance.post('/cantieri', cantiereData);\n      return response.data;\n    } catch (error) {\n      console.error('Create cantiere error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un cantiere\n  deleteCantiere: async cantiereId => {\n    try {\n      const response = await axiosInstance.delete(`/cantieri/${cantiereId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cantiere error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i dettagli di un cantiere specifico\n  getCantiere: async cantiereId => {\n    try {\n      const response = await axiosInstance.get(`/cantieri/${cantiereId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cantiere error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i cantieri di un utente specifico (per amministratori che impersonano utenti)\n  getUserCantieri: async userId => {\n    try {\n      const response = await axiosInstance.get(`/cantieri/user/${userId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get user cantieri error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default cantieriService;", "map": {"version": 3, "names": ["axios", "config", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "cantieriService", "getMyCantieri", "response", "get", "data", "console", "createCantiere", "cantiereData", "post", "deleteCantiere", "cantiereId", "delete", "getCantiere", "getUserCantieri", "userId"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/cantieriService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\n// Crea un'istanza di axios con configurazione personalizzata\r\nconst axiosInstance = axios.create({\r\n  baseURL: API_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json'\r\n  }\r\n});\r\n\r\n// Configura axios per includere il token in tutte le richieste\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem('token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nconst cantieriService = {\r\n  // Ottiene la lista di tutti i cantieri dell'utente corrente\r\n  getMyCantieri: async () => {\r\n    try {\r\n      const response = await axiosInstance.get('/cantieri');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get cantieri error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea un nuovo cantiere\r\n  createCantiere: async (cantiereData) => {\r\n    try {\r\n      const response = await axiosInstance.post('/cantieri', cantiereData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Create cantiere error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Elimina un cantiere\r\n  deleteCantiere: async (cantiereId) => {\r\n    try {\r\n      const response = await axiosInstance.delete(`/cantieri/${cantiereId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Delete cantiere error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene i dettagli di un cantiere specifico\r\n  getCantiere: async (cantiereId) => {\r\n    try {\r\n      const response = await axiosInstance.get(`/cantieri/${cantiereId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get cantiere error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene i cantieri di un utente specifico (per amministratori che impersonano utenti)\r\n  getUserCantieri: async (userId) => {\r\n    try {\r\n      const response = await axiosInstance.get(`/cantieri/user/${userId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get user cantieri error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default cantieriService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAE9B,MAAMC,OAAO,GAAGD,MAAM,CAACC,OAAO;;AAE9B;AACA,MAAMC,aAAa,GAAGH,KAAK,CAACI,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCR,MAAM,IAAK;EACV,MAAMS,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTT,MAAM,CAACK,OAAO,CAACO,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOT,MAAM;AACf,CAAC,EACAa,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,eAAe,GAAG;EACtB;EACAC,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMhB,aAAa,CAACiB,GAAG,CAAC,WAAW,CAAC;MACrD,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,GAAGP,KAAK;IACpD;EACF,CAAC;EAED;EACAS,cAAc,EAAE,MAAOC,YAAY,IAAK;IACtC,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMhB,aAAa,CAACsB,IAAI,CAAC,WAAW,EAAED,YAAY,CAAC;MACpE,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,GAAGP,KAAK;IACpD;EACF,CAAC;EAED;EACAY,cAAc,EAAE,MAAOC,UAAU,IAAK;IACpC,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMhB,aAAa,CAACyB,MAAM,CAAC,aAAaD,UAAU,EAAE,CAAC;MACtE,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,GAAGP,KAAK;IACpD;EACF,CAAC;EAED;EACAe,WAAW,EAAE,MAAOF,UAAU,IAAK;IACjC,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMhB,aAAa,CAACiB,GAAG,CAAC,aAAaO,UAAU,EAAE,CAAC;MACnE,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,GAAGP,KAAK;IACpD;EACF,CAAC;EAED;EACAgB,eAAe,EAAE,MAAOC,MAAM,IAAK;IACjC,IAAI;MACF,MAAMZ,QAAQ,GAAG,MAAMhB,aAAa,CAACiB,GAAG,CAAC,kBAAkBW,MAAM,EAAE,CAAC;MACpE,OAAOZ,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,GAAGP,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}