{"ast": null, "code": "var baseExtremum = require('./_baseExtremum'),\n  baseGt = require('./_baseGt'),\n  baseIteratee = require('./_baseIteratee');\n\n/**\n * This method is like `_.max` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * the value is ranked. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {*} Returns the maximum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * _.maxBy(objects, function(o) { return o.n; });\n * // => { 'n': 2 }\n *\n * // The `_.property` iteratee shorthand.\n * _.maxBy(objects, 'n');\n * // => { 'n': 2 }\n */\nfunction maxBy(array, iteratee) {\n  return array && array.length ? baseExtremum(array, baseIteratee(iteratee, 2), baseGt) : undefined;\n}\nmodule.exports = maxBy;", "map": {"version": 3, "names": ["baseExtremum", "require", "baseGt", "baseIteratee", "maxBy", "array", "iteratee", "length", "undefined", "module", "exports"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/lodash/maxBy.js"], "sourcesContent": ["var baseExtremum = require('./_baseExtremum'),\n    baseGt = require('./_baseGt'),\n    baseIteratee = require('./_baseIteratee');\n\n/**\n * This method is like `_.max` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * the value is ranked. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {*} Returns the maximum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * _.maxBy(objects, function(o) { return o.n; });\n * // => { 'n': 2 }\n *\n * // The `_.property` iteratee shorthand.\n * _.maxBy(objects, 'n');\n * // => { 'n': 2 }\n */\nfunction maxBy(array, iteratee) {\n  return (array && array.length)\n    ? baseExtremum(array, baseIteratee(iteratee, 2), baseGt)\n    : undefined;\n}\n\nmodule.exports = maxBy;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;EACzCC,MAAM,GAAGD,OAAO,CAAC,WAAW,CAAC;EAC7BE,YAAY,GAAGF,OAAO,CAAC,iBAAiB,CAAC;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,KAAKA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC9B,OAAQD,KAAK,IAAIA,KAAK,CAACE,MAAM,GACzBP,YAAY,CAACK,KAAK,EAAEF,YAAY,CAACG,QAAQ,EAAE,CAAC,CAAC,EAAEJ,MAAM,CAAC,GACtDM,SAAS;AACf;AAEAC,MAAM,CAACC,OAAO,GAAGN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}