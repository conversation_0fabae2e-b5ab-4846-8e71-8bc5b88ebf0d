{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\parco\\\\CreaBobinaPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Paper, Button, IconButton, Alert, Snackbar, TextField, Grid, CircularProgress } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon, Save as SaveIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport parcoCaviService from '../../../services/parcoCaviService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreaBobinaPage = () => {\n  _s();\n  const {\n    isImpersonating\n  } = useAuth();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    tipo_cavo: '',\n    costruttore: '',\n    lotto: '',\n    anno_costruzione: '',\n    lunghezza_totale: '',\n    lunghezza_residua: '',\n    note: ''\n  });\n  const [errors, setErrors] = useState({});\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Gestisce il cambio dei valori del form\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Rimuovi l'errore quando l'utente inizia a digitare\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: null\n      });\n    }\n  };\n\n  // Valida il form\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.numero_bobina) {\n      newErrors.numero_bobina = 'Il numero bobina è obbligatorio';\n    }\n    if (!formData.tipo_cavo) {\n      newErrors.tipo_cavo = 'Il tipo di cavo è obbligatorio';\n    }\n    if (!formData.lunghezza_totale) {\n      newErrors.lunghezza_totale = 'La lunghezza totale è obbligatoria';\n    } else if (isNaN(formData.lunghezza_totale)) {\n      newErrors.lunghezza_totale = 'La lunghezza deve essere un numero';\n    }\n    if (!formData.lunghezza_residua) {\n      newErrors.lunghezza_residua = 'La lunghezza residua è obbligatoria';\n    } else if (isNaN(formData.lunghezza_residua)) {\n      newErrors.lunghezza_residua = 'La lunghezza deve essere un numero';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Salva la nuova bobina\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n    setLoading(true);\n    try {\n      // Converti i valori numerici\n      const bobinaData = {\n        ...formData,\n        lunghezza_totale: parseFloat(formData.lunghezza_totale),\n        lunghezza_residua: parseFloat(formData.lunghezza_residua),\n        anno_costruzione: formData.anno_costruzione ? parseInt(formData.anno_costruzione, 10) : null\n      };\n      await parcoCaviService.createBobina(cantiereId, bobinaData);\n      handleSuccess('Bobina creata con successo');\n\n      // Resetta il form\n      setFormData({\n        numero_bobina: '',\n        tipo_cavo: '',\n        costruttore: '',\n        lotto: '',\n        anno_costruzione: '',\n        lunghezza_totale: '',\n        lunghezza_residua: '',\n        note: ''\n      });\n    } catch (error) {\n      handleError(error.message || 'Errore nella creazione della bobina');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Torna al menu principale di Parco Cavi\n  const handleBackToParco = () => {\n    navigate('/dashboard/cavi/parco');\n  };\n\n  // Gestisce le notifiche di successo\n  const handleSuccess = message => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce le notifiche di errore\n  const handleError = message => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToParco,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          children: \"Crea Nuova Bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Cantiere: \", cantiereName, \" (ID: \", cantiereId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 24\n          }, this),\n          onClick: handleBackToParco,\n          children: \"Torna a Parco Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserisci i dati della nuova bobina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Numero Bobina *\",\n            name: \"numero_bobina\",\n            value: formData.numero_bobina,\n            onChange: handleChange,\n            error: !!errors.numero_bobina,\n            helperText: errors.numero_bobina,\n            variant: \"outlined\",\n            margin: \"normal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Tipo Cavo *\",\n            name: \"tipo_cavo\",\n            value: formData.tipo_cavo,\n            onChange: handleChange,\n            error: !!errors.tipo_cavo,\n            helperText: errors.tipo_cavo,\n            variant: \"outlined\",\n            margin: \"normal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Costruttore\",\n            name: \"costruttore\",\n            value: formData.costruttore,\n            onChange: handleChange,\n            variant: \"outlined\",\n            margin: \"normal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Lotto\",\n            name: \"lotto\",\n            value: formData.lotto,\n            onChange: handleChange,\n            variant: \"outlined\",\n            margin: \"normal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 4,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Anno Costruzione\",\n            name: \"anno_costruzione\",\n            value: formData.anno_costruzione,\n            onChange: handleChange,\n            variant: \"outlined\",\n            margin: \"normal\",\n            type: \"number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 4,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Lunghezza Totale (m) *\",\n            name: \"lunghezza_totale\",\n            value: formData.lunghezza_totale,\n            onChange: handleChange,\n            error: !!errors.lunghezza_totale,\n            helperText: errors.lunghezza_totale,\n            variant: \"outlined\",\n            margin: \"normal\",\n            type: \"number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 4,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Lunghezza Residua (m) *\",\n            name: \"lunghezza_residua\",\n            value: formData.lunghezza_residua,\n            onChange: handleChange,\n            error: !!errors.lunghezza_residua,\n            helperText: errors.lunghezza_residua,\n            variant: \"outlined\",\n            margin: \"normal\",\n            type: \"number\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Note\",\n            name: \"note\",\n            value: formData.note,\n            onChange: handleChange,\n            variant: \"outlined\",\n            margin: \"normal\",\n            multiline: true,\n            rows: 3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'flex-end'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 34\n          }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 67\n          }, this),\n          onClick: handleSave,\n          disabled: loading,\n          children: loading ? 'Salvataggio...' : 'Salva Bobina'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openSnackbar,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: alertSeverity,\n        sx: {\n          width: '100%'\n        },\n        children: alertMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n};\n_s(CreaBobinaPage, \"3Tk8h58vSLHOqm8Gt61ZraSqvaQ=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = CreaBobinaPage;\nexport default CreaBobinaPage;\nvar _c;\n$RefreshReg$(_c, \"CreaBobinaPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "Snackbar", "TextField", "Grid", "CircularProgress", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Save", "SaveIcon", "useNavigate", "useAuth", "AdminHomeButton", "parcoCaviService", "jsxDEV", "_jsxDEV", "CreaBobinaPage", "_s", "isImpersonating", "navigate", "loading", "setLoading", "alertMessage", "setAlertMessage", "alertSeverity", "setAlertSeverity", "openSnackbar", "setOpenSnackbar", "formData", "setFormData", "numero_bobina", "tipo_cavo", "costru<PERSON><PERSON>", "lotto", "anno_costruzione", "lunghezza_totale", "lunghezza_residua", "note", "errors", "setErrors", "cantiereId", "parseInt", "localStorage", "getItem", "cantiereName", "handleChange", "e", "name", "value", "target", "validateForm", "newErrors", "isNaN", "Object", "keys", "length", "handleSave", "bobina<PERSON><PERSON>", "parseFloat", "createBobina", "handleSuccess", "error", "handleError", "message", "handleBackToCantieri", "handleBackToParco", "handleCloseSnackbar", "children", "sx", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "window", "location", "reload", "ml", "color", "title", "p", "startIcon", "gutterBottom", "container", "spacing", "mt", "item", "xs", "sm", "fullWidth", "label", "onChange", "helperText", "margin", "type", "multiline", "rows", "size", "disabled", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/parco/CreaBobinaPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert,\n  Snackbar,\n  TextField,\n  Grid,\n  CircularProgress\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Save as SaveIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport parcoCaviService from '../../../services/parcoCaviService';\n\nconst CreaBobinaPage = () => {\n  const { isImpersonating } = useAuth();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    tipo_cavo: '',\n    costruttore: '',\n    lotto: '',\n    anno_costruzione: '',\n    lunghezza_totale: '',\n    lunghezza_residua: '',\n    note: ''\n  });\n  const [errors, setErrors] = useState({});\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Gestisce il cambio dei valori del form\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Rimuovi l'errore quando l'utente inizia a digitare\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: null\n      });\n    }\n  };\n\n  // Valida il form\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.numero_bobina) {\n      newErrors.numero_bobina = 'Il numero bobina è obbligatorio';\n    }\n\n    if (!formData.tipo_cavo) {\n      newErrors.tipo_cavo = 'Il tipo di cavo è obbligatorio';\n    }\n\n    if (!formData.lunghezza_totale) {\n      newErrors.lunghezza_totale = 'La lunghezza totale è obbligatoria';\n    } else if (isNaN(formData.lunghezza_totale)) {\n      newErrors.lunghezza_totale = 'La lunghezza deve essere un numero';\n    }\n\n    if (!formData.lunghezza_residua) {\n      newErrors.lunghezza_residua = 'La lunghezza residua è obbligatoria';\n    } else if (isNaN(formData.lunghezza_residua)) {\n      newErrors.lunghezza_residua = 'La lunghezza deve essere un numero';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Salva la nuova bobina\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Converti i valori numerici\n      const bobinaData = {\n        ...formData,\n        lunghezza_totale: parseFloat(formData.lunghezza_totale),\n        lunghezza_residua: parseFloat(formData.lunghezza_residua),\n        anno_costruzione: formData.anno_costruzione ? parseInt(formData.anno_costruzione, 10) : null\n      };\n\n      await parcoCaviService.createBobina(cantiereId, bobinaData);\n      handleSuccess('Bobina creata con successo');\n\n      // Resetta il form\n      setFormData({\n        numero_bobina: '',\n        tipo_cavo: '',\n        costruttore: '',\n        lotto: '',\n        anno_costruzione: '',\n        lunghezza_totale: '',\n        lunghezza_residua: '',\n        note: ''\n      });\n    } catch (error) {\n      handleError(error.message || 'Errore nella creazione della bobina');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Torna al menu principale di Parco Cavi\n  const handleBackToParco = () => {\n    navigate('/dashboard/cavi/parco');\n  };\n\n\n\n  // Gestisce le notifiche di successo\n  const handleSuccess = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce le notifiche di errore\n  const handleError = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToParco} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Crea Nuova Bobina\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h6\">\n            Cantiere: {cantiereName} (ID: {cantiereId})\n          </Typography>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<ArrowBackIcon />}\n            onClick={handleBackToParco}\n          >\n            Torna a Parco Cavi\n          </Button>\n        </Box>\n      </Paper>\n\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserisci i dati della nuova bobina\n        </Typography>\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12} sm={6}>\n            <TextField\n              fullWidth\n              label=\"Numero Bobina *\"\n              name=\"numero_bobina\"\n              value={formData.numero_bobina}\n              onChange={handleChange}\n              error={!!errors.numero_bobina}\n              helperText={errors.numero_bobina}\n              variant=\"outlined\"\n              margin=\"normal\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={6}>\n            <TextField\n              fullWidth\n              label=\"Tipo Cavo *\"\n              name=\"tipo_cavo\"\n              value={formData.tipo_cavo}\n              onChange={handleChange}\n              error={!!errors.tipo_cavo}\n              helperText={errors.tipo_cavo}\n              variant=\"outlined\"\n              margin=\"normal\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={6}>\n            <TextField\n              fullWidth\n              label=\"Costruttore\"\n              name=\"costruttore\"\n              value={formData.costruttore}\n              onChange={handleChange}\n              variant=\"outlined\"\n              margin=\"normal\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={6}>\n            <TextField\n              fullWidth\n              label=\"Lotto\"\n              name=\"lotto\"\n              value={formData.lotto}\n              onChange={handleChange}\n              variant=\"outlined\"\n              margin=\"normal\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={4}>\n            <TextField\n              fullWidth\n              label=\"Anno Costruzione\"\n              name=\"anno_costruzione\"\n              value={formData.anno_costruzione}\n              onChange={handleChange}\n              variant=\"outlined\"\n              margin=\"normal\"\n              type=\"number\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={4}>\n            <TextField\n              fullWidth\n              label=\"Lunghezza Totale (m) *\"\n              name=\"lunghezza_totale\"\n              value={formData.lunghezza_totale}\n              onChange={handleChange}\n              error={!!errors.lunghezza_totale}\n              helperText={errors.lunghezza_totale}\n              variant=\"outlined\"\n              margin=\"normal\"\n              type=\"number\"\n            />\n          </Grid>\n          <Grid item xs={12} sm={4}>\n            <TextField\n              fullWidth\n              label=\"Lunghezza Residua (m) *\"\n              name=\"lunghezza_residua\"\n              value={formData.lunghezza_residua}\n              onChange={handleChange}\n              error={!!errors.lunghezza_residua}\n              helperText={errors.lunghezza_residua}\n              variant=\"outlined\"\n              margin=\"normal\"\n              type=\"number\"\n            />\n          </Grid>\n          <Grid item xs={12}>\n            <TextField\n              fullWidth\n              label=\"Note\"\n              name=\"note\"\n              value={formData.note}\n              onChange={handleChange}\n              variant=\"outlined\"\n              margin=\"normal\"\n              multiline\n              rows={3}\n            />\n          </Grid>\n        </Grid>\n\n        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={loading ? <CircularProgress size={24} /> : <SaveIcon />}\n            onClick={handleSave}\n            disabled={loading}\n          >\n            {loading ? 'Salvataggio...' : 'Salva Bobina'}\n          </Button>\n        </Box>\n      </Paper>\n\n      <Snackbar\n        open={openSnackbar}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>\n          {alertMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default CreaBobinaPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,QAAQ,EACRC,SAAS,EACTC,IAAI,EACJC,gBAAgB,QACX,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,gBAAgB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAgB,CAAC,GAAGP,OAAO,CAAC,CAAC;EACrC,MAAMQ,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC;IACvCqC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,gBAAgB,EAAE,EAAE;IACpBC,gBAAgB,EAAE,EAAE;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAM+C,UAAU,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;EAC3E,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAAI,YAAYH,UAAU,EAAE;;EAE7F;EACA,MAAMK,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCpB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACmB,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,IAAIV,MAAM,CAACS,IAAI,CAAC,EAAE;MAChBR,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACS,IAAI,GAAG;MACV,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMG,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACvB,QAAQ,CAACE,aAAa,EAAE;MAC3BqB,SAAS,CAACrB,aAAa,GAAG,iCAAiC;IAC7D;IAEA,IAAI,CAACF,QAAQ,CAACG,SAAS,EAAE;MACvBoB,SAAS,CAACpB,SAAS,GAAG,gCAAgC;IACxD;IAEA,IAAI,CAACH,QAAQ,CAACO,gBAAgB,EAAE;MAC9BgB,SAAS,CAAChB,gBAAgB,GAAG,oCAAoC;IACnE,CAAC,MAAM,IAAIiB,KAAK,CAACxB,QAAQ,CAACO,gBAAgB,CAAC,EAAE;MAC3CgB,SAAS,CAAChB,gBAAgB,GAAG,oCAAoC;IACnE;IAEA,IAAI,CAACP,QAAQ,CAACQ,iBAAiB,EAAE;MAC/Be,SAAS,CAACf,iBAAiB,GAAG,qCAAqC;IACrE,CAAC,MAAM,IAAIgB,KAAK,CAACxB,QAAQ,CAACQ,iBAAiB,CAAC,EAAE;MAC5Ce,SAAS,CAACf,iBAAiB,GAAG,oCAAoC;IACpE;IAEAG,SAAS,CAACY,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA7B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAMoC,UAAU,GAAG;QACjB,GAAG7B,QAAQ;QACXO,gBAAgB,EAAEuB,UAAU,CAAC9B,QAAQ,CAACO,gBAAgB,CAAC;QACvDC,iBAAiB,EAAEsB,UAAU,CAAC9B,QAAQ,CAACQ,iBAAiB,CAAC;QACzDF,gBAAgB,EAAEN,QAAQ,CAACM,gBAAgB,GAAGO,QAAQ,CAACb,QAAQ,CAACM,gBAAgB,EAAE,EAAE,CAAC,GAAG;MAC1F,CAAC;MAED,MAAMrB,gBAAgB,CAAC8C,YAAY,CAACnB,UAAU,EAAEiB,UAAU,CAAC;MAC3DG,aAAa,CAAC,4BAA4B,CAAC;;MAE3C;MACA/B,WAAW,CAAC;QACVC,aAAa,EAAE,EAAE;QACjBC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,EAAE;QACTC,gBAAgB,EAAE,EAAE;QACpBC,gBAAgB,EAAE,EAAE;QACpBC,iBAAiB,EAAE,EAAE;QACrBC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdC,WAAW,CAACD,KAAK,CAACE,OAAO,IAAI,qCAAqC,CAAC;IACrE,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2C,oBAAoB,GAAGA,CAAA,KAAM;IACjC7C,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAED;EACA,MAAM8C,iBAAiB,GAAGA,CAAA,KAAM;IAC9B9C,QAAQ,CAAC,uBAAuB,CAAC;EACnC,CAAC;;EAID;EACA,MAAMyC,aAAa,GAAIG,OAAO,IAAK;IACjCxC,eAAe,CAACwC,OAAO,CAAC;IACxBtC,gBAAgB,CAAC,SAAS,CAAC;IAC3BE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMmC,WAAW,GAAIC,OAAO,IAAK;IAC/BxC,eAAe,CAACwC,OAAO,CAAC;IACxBtC,gBAAgB,CAAC,OAAO,CAAC;IACzBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuC,mBAAmB,GAAGA,CAAA,KAAM;IAChCvC,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,oBACEZ,OAAA,CAACrB,GAAG;IAAAyE,QAAA,gBACFpD,OAAA,CAACrB,GAAG;MAAC0E,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAL,QAAA,gBACzFpD,OAAA,CAACrB,GAAG;QAAC0E,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBACjDpD,OAAA,CAACjB,UAAU;UAAC2E,OAAO,EAAER,iBAAkB;UAACG,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,eACpDpD,OAAA,CAACV,aAAa;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACb/D,OAAA,CAACpB,UAAU;UAACoF,OAAO,EAAC,IAAI;UAAAZ,QAAA,EAAC;QAEzB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/D,OAAA,CAACjB,UAAU;UACT2E,OAAO,EAAEA,CAAA,KAAMO,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCd,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE,CAAE;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAC,oBAAoB;UAAAlB,QAAA,eAE1BpD,OAAA,CAACR,WAAW;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN/D,OAAA,CAACH,eAAe;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAEN/D,OAAA,CAACnB,KAAK;MAACwE,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEiB,CAAC,EAAE;MAAE,CAAE;MAAAnB,QAAA,eACzBpD,OAAA,CAACrB,GAAG;QAAC0E,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,eAAe;UAAED,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBAClFpD,OAAA,CAACpB,UAAU;UAACoF,OAAO,EAAC,IAAI;UAAAZ,QAAA,GAAC,YACb,EAACvB,YAAY,EAAC,QAAM,EAACJ,UAAU,EAAC,GAC5C;QAAA;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/D,OAAA,CAAClB,MAAM;UACLkF,OAAO,EAAC,WAAW;UACnBK,KAAK,EAAC,SAAS;UACfG,SAAS,eAAExE,OAAA,CAACV,aAAa;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BL,OAAO,EAAER,iBAAkB;UAAAE,QAAA,EAC5B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAER/D,OAAA,CAACnB,KAAK;MAACwE,EAAE,EAAE;QAAEkB,CAAC,EAAE;MAAE,CAAE;MAAAnB,QAAA,gBAClBpD,OAAA,CAACpB,UAAU;QAACoF,OAAO,EAAC,IAAI;QAACS,YAAY;QAAArB,QAAA,EAAC;MAEtC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb/D,OAAA,CAACb,IAAI;QAACuF,SAAS;QAACC,OAAO,EAAE,CAAE;QAACtB,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAE,CAAE;QAAAxB,QAAA,gBACxCpD,OAAA,CAACb,IAAI;UAAC0F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBpD,OAAA,CAACd,SAAS;YACR8F,SAAS;YACTC,KAAK,EAAC,iBAAiB;YACvBjD,IAAI,EAAC,eAAe;YACpBC,KAAK,EAAEpB,QAAQ,CAACE,aAAc;YAC9BmE,QAAQ,EAAEpD,YAAa;YACvBgB,KAAK,EAAE,CAAC,CAACvB,MAAM,CAACR,aAAc;YAC9BoE,UAAU,EAAE5D,MAAM,CAACR,aAAc;YACjCiD,OAAO,EAAC,UAAU;YAClBoB,MAAM,EAAC;UAAQ;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/D,OAAA,CAACb,IAAI;UAAC0F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBpD,OAAA,CAACd,SAAS;YACR8F,SAAS;YACTC,KAAK,EAAC,aAAa;YACnBjD,IAAI,EAAC,WAAW;YAChBC,KAAK,EAAEpB,QAAQ,CAACG,SAAU;YAC1BkE,QAAQ,EAAEpD,YAAa;YACvBgB,KAAK,EAAE,CAAC,CAACvB,MAAM,CAACP,SAAU;YAC1BmE,UAAU,EAAE5D,MAAM,CAACP,SAAU;YAC7BgD,OAAO,EAAC,UAAU;YAClBoB,MAAM,EAAC;UAAQ;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/D,OAAA,CAACb,IAAI;UAAC0F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBpD,OAAA,CAACd,SAAS;YACR8F,SAAS;YACTC,KAAK,EAAC,aAAa;YACnBjD,IAAI,EAAC,aAAa;YAClBC,KAAK,EAAEpB,QAAQ,CAACI,WAAY;YAC5BiE,QAAQ,EAAEpD,YAAa;YACvBkC,OAAO,EAAC,UAAU;YAClBoB,MAAM,EAAC;UAAQ;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/D,OAAA,CAACb,IAAI;UAAC0F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBpD,OAAA,CAACd,SAAS;YACR8F,SAAS;YACTC,KAAK,EAAC,OAAO;YACbjD,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEpB,QAAQ,CAACK,KAAM;YACtBgE,QAAQ,EAAEpD,YAAa;YACvBkC,OAAO,EAAC,UAAU;YAClBoB,MAAM,EAAC;UAAQ;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/D,OAAA,CAACb,IAAI;UAAC0F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBpD,OAAA,CAACd,SAAS;YACR8F,SAAS;YACTC,KAAK,EAAC,kBAAkB;YACxBjD,IAAI,EAAC,kBAAkB;YACvBC,KAAK,EAAEpB,QAAQ,CAACM,gBAAiB;YACjC+D,QAAQ,EAAEpD,YAAa;YACvBkC,OAAO,EAAC,UAAU;YAClBoB,MAAM,EAAC,QAAQ;YACfC,IAAI,EAAC;UAAQ;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/D,OAAA,CAACb,IAAI;UAAC0F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBpD,OAAA,CAACd,SAAS;YACR8F,SAAS;YACTC,KAAK,EAAC,wBAAwB;YAC9BjD,IAAI,EAAC,kBAAkB;YACvBC,KAAK,EAAEpB,QAAQ,CAACO,gBAAiB;YACjC8D,QAAQ,EAAEpD,YAAa;YACvBgB,KAAK,EAAE,CAAC,CAACvB,MAAM,CAACH,gBAAiB;YACjC+D,UAAU,EAAE5D,MAAM,CAACH,gBAAiB;YACpC4C,OAAO,EAAC,UAAU;YAClBoB,MAAM,EAAC,QAAQ;YACfC,IAAI,EAAC;UAAQ;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/D,OAAA,CAACb,IAAI;UAAC0F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvBpD,OAAA,CAACd,SAAS;YACR8F,SAAS;YACTC,KAAK,EAAC,yBAAyB;YAC/BjD,IAAI,EAAC,mBAAmB;YACxBC,KAAK,EAAEpB,QAAQ,CAACQ,iBAAkB;YAClC6D,QAAQ,EAAEpD,YAAa;YACvBgB,KAAK,EAAE,CAAC,CAACvB,MAAM,CAACF,iBAAkB;YAClC8D,UAAU,EAAE5D,MAAM,CAACF,iBAAkB;YACrC2C,OAAO,EAAC,UAAU;YAClBoB,MAAM,EAAC,QAAQ;YACfC,IAAI,EAAC;UAAQ;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP/D,OAAA,CAACb,IAAI;UAAC0F,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA1B,QAAA,eAChBpD,OAAA,CAACd,SAAS;YACR8F,SAAS;YACTC,KAAK,EAAC,MAAM;YACZjD,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEpB,QAAQ,CAACS,IAAK;YACrB4D,QAAQ,EAAEpD,YAAa;YACvBkC,OAAO,EAAC,UAAU;YAClBoB,MAAM,EAAC,QAAQ;YACfE,SAAS;YACTC,IAAI,EAAE;UAAE;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP/D,OAAA,CAACrB,GAAG;QAAC0E,EAAE,EAAE;UAAEuB,EAAE,EAAE,CAAC;UAAErB,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE;QAAW,CAAE;QAAAL,QAAA,eAC9DpD,OAAA,CAAClB,MAAM;UACLkF,OAAO,EAAC,WAAW;UACnBK,KAAK,EAAC,SAAS;UACfG,SAAS,EAAEnE,OAAO,gBAAGL,OAAA,CAACZ,gBAAgB;YAACoG,IAAI,EAAE;UAAG;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG/D,OAAA,CAACN,QAAQ;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnEL,OAAO,EAAEjB,UAAW;UACpBgD,QAAQ,EAAEpF,OAAQ;UAAA+C,QAAA,EAEjB/C,OAAO,GAAG,gBAAgB,GAAG;QAAc;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAER/D,OAAA,CAACf,QAAQ;MACPyG,IAAI,EAAE/E,YAAa;MACnBgF,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEzC,mBAAoB;MAC7B0C,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA3C,QAAA,eAE3DpD,OAAA,CAAChB,KAAK;QAAC4G,OAAO,EAAEzC,mBAAoB;QAAC6C,QAAQ,EAAEvF,aAAc;QAAC4C,EAAE,EAAE;UAAE4C,KAAK,EAAE;QAAO,CAAE;QAAA7C,QAAA,EACjF7C;MAAY;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC7D,EAAA,CApTID,cAAc;EAAA,QACUL,OAAO,EAClBD,WAAW;AAAA;AAAAuG,EAAA,GAFxBjG,cAAc;AAsTpB,eAAeA,cAAc;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}