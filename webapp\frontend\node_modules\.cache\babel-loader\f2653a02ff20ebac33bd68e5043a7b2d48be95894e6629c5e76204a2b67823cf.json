{"ast": null, "code": "var root = require('./_root');\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function () {\n  return root.Date.now();\n};\nmodule.exports = now;", "map": {"version": 3, "names": ["root", "require", "now", "Date", "module", "exports"], "sources": ["C:/CMS/webapp/frontend/node_modules/lodash/now.js"], "sourcesContent": ["var root = require('./_root');\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\nmodule.exports = now;\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO,CAAC,SAAS,CAAC;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,GAAG,GAAG,SAAAA,CAAA,EAAW;EACnB,OAAOF,IAAI,CAACG,IAAI,CAACD,GAAG,CAAC,CAAC;AACxB,CAAC;AAEDE,MAAM,CAACC,OAAO,GAAGH,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}