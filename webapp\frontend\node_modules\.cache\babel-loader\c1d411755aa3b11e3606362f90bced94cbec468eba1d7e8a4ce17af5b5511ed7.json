{"ast": null, "code": "import React,{useState}from'react';import{useNavigate}from'react-router-dom';import{Container,Box,Typography,TextField,Button,Paper,Tabs,Tab,Alert,CircularProgress}from'@mui/material';import{useAuth}from'../context/AuthContext';// Componente per il pannello di login\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function TabPanel(props){const{children,value,index,...other}=props;return/*#__PURE__*/_jsx(\"div\",{role:\"tabpanel\",hidden:value!==index,id:`login-tabpanel-${index}`,\"aria-labelledby\":`login-tab-${index}`,...other,children:value===index&&/*#__PURE__*/_jsx(Box,{sx:{p:3},children:children})});}const LoginPage=()=>{const[tabValue,setTabValue]=useState(0);const[loading,setLoading]=useState(false);const[error,setError]=useState('');// Form per login standard\nconst[standardCredentials,setStandardCredentials]=useState({username:'',password:''});// Form per login cantiere\nconst[cantiereCredentials,setCantiereCredentials]=useState({codice_univoco:'',password:''});const{login}=useAuth();const navigate=useNavigate();// Gestione del cambio di tab\nconst handleTabChange=(event,newValue)=>{setTabValue(newValue);setError('');};// Gestione dell'input per il login standard\nconst handleStandardInputChange=e=>{const{name,value}=e.target;setStandardCredentials({...standardCredentials,[name]:value});};// Gestione dell'input per il login cantiere\nconst handleCantiereInputChange=e=>{const{name,value}=e.target;setCantiereCredentials({...cantiereCredentials,[name]:value});};// Gestione del login standard\nconst handleStandardLogin=async e=>{e.preventDefault();setLoading(true);setError('');try{await login(standardCredentials,'standard');navigate('/dashboard');}catch(err){setError(err.detail||'Errore durante il login. Verifica le credenziali.');}finally{setLoading(false);}};// Gestione del login cantiere\nconst handleCantiereLogin=async e=>{e.preventDefault();setLoading(true);setError('');try{await login(cantiereCredentials,'cantiere');navigate('/dashboard');}catch(err){setError(err.detail||'Errore durante il login. Verifica le credenziali.');}finally{setLoading(false);}};return/*#__PURE__*/_jsx(Container,{component:\"main\",maxWidth:\"sm\",children:/*#__PURE__*/_jsxs(Box,{sx:{marginTop:8,display:'flex',flexDirection:'column',alignItems:'center'},children:[/*#__PURE__*/_jsx(Typography,{component:\"h1\",variant:\"h4\",sx:{mb:4},children:\"Sistema di Gestione Cantieri\"}),/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{width:'100%'},children:[/*#__PURE__*/_jsxs(Tabs,{value:tabValue,onChange:handleTabChange,variant:\"fullWidth\",indicatorColor:\"primary\",textColor:\"primary\",children:[/*#__PURE__*/_jsx(Tab,{label:\"Admin / Utente\"}),/*#__PURE__*/_jsx(Tab,{label:\"Cantiere\"})]}),error&&/*#__PURE__*/_jsx(Box,{sx:{p:2},children:/*#__PURE__*/_jsx(Alert,{severity:\"error\",children:error})}),/*#__PURE__*/_jsx(TabPanel,{value:tabValue,index:0,children:/*#__PURE__*/_jsxs(Box,{component:\"form\",onSubmit:handleStandardLogin,noValidate:true,children:[/*#__PURE__*/_jsx(TextField,{margin:\"normal\",required:true,fullWidth:true,id:\"username\",label:\"Username\",name:\"username\",autoComplete:\"username\",autoFocus:true,value:standardCredentials.username,onChange:handleStandardInputChange}),/*#__PURE__*/_jsx(TextField,{margin:\"normal\",required:true,fullWidth:true,name:\"password\",label:\"Password\",type:\"password\",id:\"password\",autoComplete:\"current-password\",value:standardCredentials.password,onChange:handleStandardInputChange}),/*#__PURE__*/_jsx(Button,{type:\"submit\",fullWidth:true,variant:\"contained\",sx:{mt:3,mb:2},disabled:loading,children:loading?/*#__PURE__*/_jsx(CircularProgress,{size:24}):'Accedi'})]})}),/*#__PURE__*/_jsx(TabPanel,{value:tabValue,index:1,children:/*#__PURE__*/_jsxs(Box,{component:\"form\",onSubmit:handleCantiereLogin,noValidate:true,children:[/*#__PURE__*/_jsx(TextField,{margin:\"normal\",required:true,fullWidth:true,id:\"codice_univoco\",label:\"Codice Univoco Cantiere\",name:\"codice_univoco\",autoComplete:\"off\",autoFocus:true,value:cantiereCredentials.codice_univoco,onChange:handleCantiereInputChange}),/*#__PURE__*/_jsx(TextField,{margin:\"normal\",required:true,fullWidth:true,name:\"password\",label:\"Password\",type:\"password\",id:\"cantiere-password\",autoComplete:\"current-password\",value:cantiereCredentials.password,onChange:handleCantiereInputChange}),/*#__PURE__*/_jsx(Button,{type:\"submit\",fullWidth:true,variant:\"contained\",sx:{mt:3,mb:2},disabled:loading,children:loading?/*#__PURE__*/_jsx(CircularProgress,{size:24}):'Accedi'})]})})]})]})});};export default LoginPage;", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Container", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Paper", "Tabs", "Tab", "<PERSON><PERSON>", "CircularProgress", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "TabPanel", "props", "children", "value", "index", "other", "role", "hidden", "id", "sx", "p", "LoginPage", "tabValue", "setTabValue", "loading", "setLoading", "error", "setError", "standardCredentials", "setStandardCredentials", "username", "password", "cantiereCredentials", "setCantiereCredentials", "codice_univoco", "login", "navigate", "handleTabChange", "event", "newValue", "handleStandardInputChange", "e", "name", "target", "handleCantiereInputChange", "handleStandardLogin", "preventDefault", "err", "detail", "handleCantiereLogin", "component", "max<PERSON><PERSON><PERSON>", "marginTop", "display", "flexDirection", "alignItems", "variant", "mb", "elevation", "width", "onChange", "indicatorColor", "textColor", "label", "severity", "onSubmit", "noValidate", "margin", "required", "fullWidth", "autoComplete", "autoFocus", "type", "mt", "disabled", "size"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/LoginPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Container,\n  Box,\n  Typography,\n  TextField,\n  Button,\n  Paper,\n  Tabs,\n  Tab,\n  Alert,\n  CircularProgress\n} from '@mui/material';\nimport { useAuth } from '../context/AuthContext';\n\n// Componente per il pannello di login\nfunction TabPanel(props) {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`login-tabpanel-${index}`}\n      aria-labelledby={`login-tab-${index}`}\n      {...other}\n    >\n      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}\n    </div>\n  );\n}\n\nconst LoginPage = () => {\n  const [tabValue, setTabValue] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  \n  // Form per login standard\n  const [standardCredentials, setStandardCredentials] = useState({\n    username: '',\n    password: ''\n  });\n  \n  // Form per login cantiere\n  const [cantiereCredentials, setCantiereCredentials] = useState({\n    codice_univoco: '',\n    password: ''\n  });\n  \n  const { login } = useAuth();\n  const navigate = useNavigate();\n\n  // Gestione del cambio di tab\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n    setError('');\n  };\n\n  // Gestione dell'input per il login standard\n  const handleStandardInputChange = (e) => {\n    const { name, value } = e.target;\n    setStandardCredentials({\n      ...standardCredentials,\n      [name]: value\n    });\n  };\n\n  // Gestione dell'input per il login cantiere\n  const handleCantiereInputChange = (e) => {\n    const { name, value } = e.target;\n    setCantiereCredentials({\n      ...cantiereCredentials,\n      [name]: value\n    });\n  };\n\n  // Gestione del login standard\n  const handleStandardLogin = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    \n    try {\n      await login(standardCredentials, 'standard');\n      navigate('/dashboard');\n    } catch (err) {\n      setError(err.detail || 'Errore durante il login. Verifica le credenziali.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestione del login cantiere\n  const handleCantiereLogin = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    \n    try {\n      await login(cantiereCredentials, 'cantiere');\n      navigate('/dashboard');\n    } catch (err) {\n      setError(err.detail || 'Errore durante il login. Verifica le credenziali.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container component=\"main\" maxWidth=\"sm\">\n      <Box\n        sx={{\n          marginTop: 8,\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n        }}\n      >\n        <Typography component=\"h1\" variant=\"h4\" sx={{ mb: 4 }}>\n          Sistema di Gestione Cantieri\n        </Typography>\n        \n        <Paper elevation={3} sx={{ width: '100%' }}>\n          <Tabs\n            value={tabValue}\n            onChange={handleTabChange}\n            variant=\"fullWidth\"\n            indicatorColor=\"primary\"\n            textColor=\"primary\"\n          >\n            <Tab label=\"Admin / Utente\" />\n            <Tab label=\"Cantiere\" />\n          </Tabs>\n          \n          {error && (\n            <Box sx={{ p: 2 }}>\n              <Alert severity=\"error\">{error}</Alert>\n            </Box>\n          )}\n          \n          {/* Tab per login standard */}\n          <TabPanel value={tabValue} index={0}>\n            <Box component=\"form\" onSubmit={handleStandardLogin} noValidate>\n              <TextField\n                margin=\"normal\"\n                required\n                fullWidth\n                id=\"username\"\n                label=\"Username\"\n                name=\"username\"\n                autoComplete=\"username\"\n                autoFocus\n                value={standardCredentials.username}\n                onChange={handleStandardInputChange}\n              />\n              <TextField\n                margin=\"normal\"\n                required\n                fullWidth\n                name=\"password\"\n                label=\"Password\"\n                type=\"password\"\n                id=\"password\"\n                autoComplete=\"current-password\"\n                value={standardCredentials.password}\n                onChange={handleStandardInputChange}\n              />\n              <Button\n                type=\"submit\"\n                fullWidth\n                variant=\"contained\"\n                sx={{ mt: 3, mb: 2 }}\n                disabled={loading}\n              >\n                {loading ? <CircularProgress size={24} /> : 'Accedi'}\n              </Button>\n            </Box>\n          </TabPanel>\n          \n          {/* Tab per login cantiere */}\n          <TabPanel value={tabValue} index={1}>\n            <Box component=\"form\" onSubmit={handleCantiereLogin} noValidate>\n              <TextField\n                margin=\"normal\"\n                required\n                fullWidth\n                id=\"codice_univoco\"\n                label=\"Codice Univoco Cantiere\"\n                name=\"codice_univoco\"\n                autoComplete=\"off\"\n                autoFocus\n                value={cantiereCredentials.codice_univoco}\n                onChange={handleCantiereInputChange}\n              />\n              <TextField\n                margin=\"normal\"\n                required\n                fullWidth\n                name=\"password\"\n                label=\"Password\"\n                type=\"password\"\n                id=\"cantiere-password\"\n                autoComplete=\"current-password\"\n                value={cantiereCredentials.password}\n                onChange={handleCantiereInputChange}\n              />\n              <Button\n                type=\"submit\"\n                fullWidth\n                variant=\"contained\"\n                sx={{ mt: 3, mb: 2 }}\n                disabled={loading}\n              >\n                {loading ? <CircularProgress size={24} /> : 'Accedi'}\n              </Button>\n            </Box>\n          </TabPanel>\n        </Paper>\n      </Box>\n    </Container>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,SAAS,CACTC,GAAG,CACHC,UAAU,CACVC,SAAS,CACTC,MAAM,CACNC,KAAK,CACLC,IAAI,CACJC,GAAG,CACHC,KAAK,CACLC,gBAAgB,KACX,eAAe,CACtB,OAASC,OAAO,KAAQ,wBAAwB,CAEhD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,QAAS,CAAAC,QAAQA,CAACC,KAAK,CAAE,CACvB,KAAM,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAE,GAAGC,KAAM,CAAC,CAAGJ,KAAK,CAElD,mBACEJ,IAAA,QACES,IAAI,CAAC,UAAU,CACfC,MAAM,CAAEJ,KAAK,GAAKC,KAAM,CACxBI,EAAE,CAAE,kBAAkBJ,KAAK,EAAG,CAC9B,kBAAiB,aAAaA,KAAK,EAAG,IAClCC,KAAK,CAAAH,QAAA,CAERC,KAAK,GAAKC,KAAK,eAAIP,IAAA,CAACX,GAAG,EAACuB,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAR,QAAA,CAAEA,QAAQ,CAAM,CAAC,CACpD,CAAC,CAEV,CAEA,KAAM,CAAAS,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAG9B,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAAC+B,OAAO,CAAEC,UAAU,CAAC,CAAGhC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACiC,KAAK,CAAEC,QAAQ,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CAEtC;AACA,KAAM,CAACmC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGpC,QAAQ,CAAC,CAC7DqC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,EACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGxC,QAAQ,CAAC,CAC7DyC,cAAc,CAAE,EAAE,CAClBH,QAAQ,CAAE,EACZ,CAAC,CAAC,CAEF,KAAM,CAAEI,KAAM,CAAC,CAAG9B,OAAO,CAAC,CAAC,CAC3B,KAAM,CAAA+B,QAAQ,CAAG1C,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAAA2C,eAAe,CAAGA,CAACC,KAAK,CAAEC,QAAQ,GAAK,CAC3ChB,WAAW,CAACgB,QAAQ,CAAC,CACrBZ,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAED;AACA,KAAM,CAAAa,yBAAyB,CAAIC,CAAC,EAAK,CACvC,KAAM,CAAEC,IAAI,CAAE7B,KAAM,CAAC,CAAG4B,CAAC,CAACE,MAAM,CAChCd,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,CAACc,IAAI,EAAG7B,KACV,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA+B,yBAAyB,CAAIH,CAAC,EAAK,CACvC,KAAM,CAAEC,IAAI,CAAE7B,KAAM,CAAC,CAAG4B,CAAC,CAACE,MAAM,CAChCV,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,CAACU,IAAI,EAAG7B,KACV,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAgC,mBAAmB,CAAG,KAAO,CAAAJ,CAAC,EAAK,CACvCA,CAAC,CAACK,cAAc,CAAC,CAAC,CAClBrB,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CACF,KAAM,CAAAQ,KAAK,CAACP,mBAAmB,CAAE,UAAU,CAAC,CAC5CQ,QAAQ,CAAC,YAAY,CAAC,CACxB,CAAE,MAAOW,GAAG,CAAE,CACZpB,QAAQ,CAACoB,GAAG,CAACC,MAAM,EAAI,mDAAmD,CAAC,CAC7E,CAAC,OAAS,CACRvB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAwB,mBAAmB,CAAG,KAAO,CAAAR,CAAC,EAAK,CACvCA,CAAC,CAACK,cAAc,CAAC,CAAC,CAClBrB,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CACF,KAAM,CAAAQ,KAAK,CAACH,mBAAmB,CAAE,UAAU,CAAC,CAC5CI,QAAQ,CAAC,YAAY,CAAC,CACxB,CAAE,MAAOW,GAAG,CAAE,CACZpB,QAAQ,CAACoB,GAAG,CAACC,MAAM,EAAI,mDAAmD,CAAC,CAC7E,CAAC,OAAS,CACRvB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACElB,IAAA,CAACZ,SAAS,EAACuD,SAAS,CAAC,MAAM,CAACC,QAAQ,CAAC,IAAI,CAAAvC,QAAA,cACvCH,KAAA,CAACb,GAAG,EACFuB,EAAE,CAAE,CACFiC,SAAS,CAAE,CAAC,CACZC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QACd,CAAE,CAAA3C,QAAA,eAEFL,IAAA,CAACV,UAAU,EAACqD,SAAS,CAAC,IAAI,CAACM,OAAO,CAAC,IAAI,CAACrC,EAAE,CAAE,CAAEsC,EAAE,CAAE,CAAE,CAAE,CAAA7C,QAAA,CAAC,8BAEvD,CAAY,CAAC,cAEbH,KAAA,CAACT,KAAK,EAAC0D,SAAS,CAAE,CAAE,CAACvC,EAAE,CAAE,CAAEwC,KAAK,CAAE,MAAO,CAAE,CAAA/C,QAAA,eACzCH,KAAA,CAACR,IAAI,EACHY,KAAK,CAAES,QAAS,CAChBsC,QAAQ,CAAEvB,eAAgB,CAC1BmB,OAAO,CAAC,WAAW,CACnBK,cAAc,CAAC,SAAS,CACxBC,SAAS,CAAC,SAAS,CAAAlD,QAAA,eAEnBL,IAAA,CAACL,GAAG,EAAC6D,KAAK,CAAC,gBAAgB,CAAE,CAAC,cAC9BxD,IAAA,CAACL,GAAG,EAAC6D,KAAK,CAAC,UAAU,CAAE,CAAC,EACpB,CAAC,CAENrC,KAAK,eACJnB,IAAA,CAACX,GAAG,EAACuB,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAR,QAAA,cAChBL,IAAA,CAACJ,KAAK,EAAC6D,QAAQ,CAAC,OAAO,CAAApD,QAAA,CAAEc,KAAK,CAAQ,CAAC,CACpC,CACN,cAGDnB,IAAA,CAACG,QAAQ,EAACG,KAAK,CAAES,QAAS,CAACR,KAAK,CAAE,CAAE,CAAAF,QAAA,cAClCH,KAAA,CAACb,GAAG,EAACsD,SAAS,CAAC,MAAM,CAACe,QAAQ,CAAEpB,mBAAoB,CAACqB,UAAU,MAAAtD,QAAA,eAC7DL,IAAA,CAACT,SAAS,EACRqE,MAAM,CAAC,QAAQ,CACfC,QAAQ,MACRC,SAAS,MACTnD,EAAE,CAAC,UAAU,CACb6C,KAAK,CAAC,UAAU,CAChBrB,IAAI,CAAC,UAAU,CACf4B,YAAY,CAAC,UAAU,CACvBC,SAAS,MACT1D,KAAK,CAAEe,mBAAmB,CAACE,QAAS,CACpC8B,QAAQ,CAAEpB,yBAA0B,CACrC,CAAC,cACFjC,IAAA,CAACT,SAAS,EACRqE,MAAM,CAAC,QAAQ,CACfC,QAAQ,MACRC,SAAS,MACT3B,IAAI,CAAC,UAAU,CACfqB,KAAK,CAAC,UAAU,CAChBS,IAAI,CAAC,UAAU,CACftD,EAAE,CAAC,UAAU,CACboD,YAAY,CAAC,kBAAkB,CAC/BzD,KAAK,CAAEe,mBAAmB,CAACG,QAAS,CACpC6B,QAAQ,CAAEpB,yBAA0B,CACrC,CAAC,cACFjC,IAAA,CAACR,MAAM,EACLyE,IAAI,CAAC,QAAQ,CACbH,SAAS,MACTb,OAAO,CAAC,WAAW,CACnBrC,EAAE,CAAE,CAAEsD,EAAE,CAAE,CAAC,CAAEhB,EAAE,CAAE,CAAE,CAAE,CACrBiB,QAAQ,CAAElD,OAAQ,CAAAZ,QAAA,CAEjBY,OAAO,cAAGjB,IAAA,CAACH,gBAAgB,EAACuE,IAAI,CAAE,EAAG,CAAE,CAAC,CAAG,QAAQ,CAC9C,CAAC,EACN,CAAC,CACE,CAAC,cAGXpE,IAAA,CAACG,QAAQ,EAACG,KAAK,CAAES,QAAS,CAACR,KAAK,CAAE,CAAE,CAAAF,QAAA,cAClCH,KAAA,CAACb,GAAG,EAACsD,SAAS,CAAC,MAAM,CAACe,QAAQ,CAAEhB,mBAAoB,CAACiB,UAAU,MAAAtD,QAAA,eAC7DL,IAAA,CAACT,SAAS,EACRqE,MAAM,CAAC,QAAQ,CACfC,QAAQ,MACRC,SAAS,MACTnD,EAAE,CAAC,gBAAgB,CACnB6C,KAAK,CAAC,yBAAyB,CAC/BrB,IAAI,CAAC,gBAAgB,CACrB4B,YAAY,CAAC,KAAK,CAClBC,SAAS,MACT1D,KAAK,CAAEmB,mBAAmB,CAACE,cAAe,CAC1C0B,QAAQ,CAAEhB,yBAA0B,CACrC,CAAC,cACFrC,IAAA,CAACT,SAAS,EACRqE,MAAM,CAAC,QAAQ,CACfC,QAAQ,MACRC,SAAS,MACT3B,IAAI,CAAC,UAAU,CACfqB,KAAK,CAAC,UAAU,CAChBS,IAAI,CAAC,UAAU,CACftD,EAAE,CAAC,mBAAmB,CACtBoD,YAAY,CAAC,kBAAkB,CAC/BzD,KAAK,CAAEmB,mBAAmB,CAACD,QAAS,CACpC6B,QAAQ,CAAEhB,yBAA0B,CACrC,CAAC,cACFrC,IAAA,CAACR,MAAM,EACLyE,IAAI,CAAC,QAAQ,CACbH,SAAS,MACTb,OAAO,CAAC,WAAW,CACnBrC,EAAE,CAAE,CAAEsD,EAAE,CAAE,CAAC,CAAEhB,EAAE,CAAE,CAAE,CAAE,CACrBiB,QAAQ,CAAElD,OAAQ,CAAAZ,QAAA,CAEjBY,OAAO,cAAGjB,IAAA,CAACH,gBAAgB,EAACuE,IAAI,CAAE,EAAG,CAAE,CAAC,CAAG,QAAQ,CAC9C,CAAC,EACN,CAAC,CACE,CAAC,EACN,CAAC,EACL,CAAC,CACG,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAtD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}