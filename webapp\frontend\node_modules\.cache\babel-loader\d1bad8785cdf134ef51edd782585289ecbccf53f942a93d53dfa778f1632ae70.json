{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriDialogCompleto.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Typography, Box, Grid, Paper, Alert, CircularProgress, Chip, InputAdornment, IconButton, List, ListItem, ListItemButton, ListItemText } from '@mui/material';\nimport { Search as SearchIcon, Cancel as CancelIcon, CheckCircle as CheckCircleIcon, Warning as WarningIcon, Close as CloseIcon } from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport { getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Dialog completo per inserire i metri posati di un cavo preselezionato\n * Basato su InserisciMetriForm ma adattato per dialog con cavo preselezionato\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Se il dialogo è aperto\n * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude\n * @param {Object} props.cavo - Cavo preselezionato\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.loading - Indica se il salvataggio è in corso\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriDialogCompleto = ({\n  open = false,\n  onClose = () => {},\n  cavo = null,\n  cantiereId,\n  onSuccess = () => {},\n  onError = () => {},\n  loading = false\n}) => {\n  _s();\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [saving, setSaving] = useState(false);\n\n  // Stati per bobine\n  const [bobine, setBobine] = useState([]);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per la ricerca delle bobine\n  const [searchText, setSearchText] = useState('');\n\n  // Stati per dialoghi\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    if (!idBobina || idBobina === 'BOBINA_VUOTA') return 'VUOTA';\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    // Cerca nella lista bobine per ottenere il numero_bobina\n    const bobina = bobine.find(b => b.id_bobina === idBobina);\n    return bobina ? bobina.numero_bobina || idBobina : idBobina;\n  };\n\n  // Gestisce la selezione di una bobina compatibile\n  const handleSelectBobinaCompatibile = bobina => {\n    console.log('Bobina compatibile selezionata:', bobina);\n    setFormData(prev => ({\n      ...prev,\n      id_bobina: bobina.id_bobina\n    }));\n\n    // Reset degli errori\n    setFormErrors(prev => ({\n      ...prev,\n      id_bobina: ''\n    }));\n  };\n\n  // Gestisce la selezione di una bobina incompatibile\n  const handleSelectBobinaIncompatibile = bobina => {\n    console.log('Bobina incompatibile selezionata:', bobina);\n    setIncompatibleReel(bobina);\n    setShowIncompatibleReelDialog(true);\n  };\n\n  // Gestisce l'uso di una bobina incompatibile\n  const handleUseIncompatibleReel = async (bobina, cavo) => {\n    try {\n      console.log(`Utilizzo bobina incompatibile ${bobina.id_bobina} con cavo ${cavo.id_cavo} senza aggiornare le caratteristiche`);\n\n      // Seleziona la bobina incompatibile\n      setFormData(prev => ({\n        ...prev,\n        id_bobina: bobina.id_bobina\n      }));\n\n      // Reset degli errori\n      setFormErrors(prev => ({\n        ...prev,\n        id_bobina: ''\n      }));\n      setShowIncompatibleReelDialog(false);\n    } catch (error) {\n      console.error('Errore durante la selezione della bobina incompatibile:', error);\n    }\n  };\n\n  // Gestisce la chiusura del dialog bobina incompatibile\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReel(null);\n  };\n\n  // Gestisce la selezione di BOBINA_VUOTA\n  const handleSelectBobinaVuota = () => {\n    console.log('BOBINA_VUOTA selezionata');\n    setFormData(prev => ({\n      ...prev,\n      id_bobina: 'BOBINA_VUOTA'\n    }));\n\n    // Reset degli errori\n    setFormErrors(prev => ({\n      ...prev,\n      id_bobina: ''\n    }));\n  };\n\n  // Gestisce il cambio del testo di ricerca\n  const handleSearchTextChange = event => {\n    setSearchText(event.target.value);\n  };\n\n  // Carica le bobine disponibili\n  const loadBobine = useCallback(async () => {\n    if (!cantiereId || !cavo) return;\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine iniziato...');\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter(bobina => bobina.stato_bobina !== 'Terminata' && bobina.stato_bobina !== 'Over' && bobina.metri_residui > 0);\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte\n      if (cavo && cavo.tipologia && cavo.sezione) {\n        console.log('Cavo selezionato, evidenziando bobine compatibili...');\n        console.log('Dati cavo:', {\n          id_cavo: cavo.id_cavo,\n          tipologia: cavo.tipologia,\n          sezione: cavo.sezione\n        });\n\n        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui\n        const cavoTipologia = String(cavo.tipologia || '').trim().toLowerCase();\n        const cavoSezione = String(cavo.sezione || '0').trim();\n\n        // Identifica le bobine compatibili\n        const bobineCompatibili = [];\n        const bobineNonCompatibili = [];\n\n        // Dividi le bobine in compatibili e non compatibili\n        bobineUtilizzabili.forEach(bobina => {\n          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();\n          const bobinaSezione = String(bobina.sezione || '0').trim();\n\n          // Verifica compatibilità\n          const tipologiaMatch = bobinaTipologia === cavoTipologia;\n          const sezioneMatch = bobinaSezione === cavoSezione;\n          const isCompatible = tipologiaMatch && sezioneMatch;\n          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {\n            'Tipologia bobina': `\"${bobina.tipologia}\"`,\n            'Tipologia cavo': `\"${cavo.tipologia}\"`,\n            'Tipologie uguali?': tipologiaMatch,\n            'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n            'Sezione cavo': `\"${String(cavo.sezione)}\"`,\n            'Sezioni uguali?': sezioneMatch,\n            'Stato bobina': bobina.stato_bobina,\n            'Metri residui': bobina.metri_residui,\n            'Compatibile?': isCompatible\n          });\n          if (isCompatible) {\n            bobineCompatibili.push(bobina);\n          } else {\n            bobineNonCompatibili.push(bobina);\n          }\n        });\n        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);\n        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);\n\n        // Log dettagliato delle bobine compatibili\n        if (bobineCompatibili.length > 0) {\n          console.log('Dettaglio bobine compatibili:');\n          bobineCompatibili.forEach(bobina => {\n            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);\n          });\n        } else {\n          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n        }\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];\n\n        // Imposta le bobine nel componente\n        setBobine(bobineOrdinate);\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n        setBobine(bobineUtilizzabili);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      setBobine([]);\n    } finally {\n      setBobineLoading(false);\n    }\n  }, [cantiereId, cavo]);\n\n  // Reset quando si apre il dialogo\n  useEffect(() => {\n    if (open && cavo) {\n      var _cavo$metratura_reale;\n      setFormData({\n        metri_posati: ((_cavo$metratura_reale = cavo.metratura_reale) === null || _cavo$metratura_reale === void 0 ? void 0 : _cavo$metratura_reale.toString()) || '',\n        id_bobina: ''\n      });\n      setFormErrors({});\n      setFormWarnings({});\n      setSaving(false);\n\n      // Carica le bobine disponibili\n      loadBobine();\n    }\n  }, [open, cavo, cantiereId, loadBobine]);\n\n  // Gestisce i cambiamenti del form\n  const handleFormChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Rimuovi errori quando l'utente inizia a digitare\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Validazione in tempo reale per metri posati\n    if (name === 'metri_posati' && value && cavo) {\n      const metri = parseFloat(value);\n      if (!isNaN(metri) && metri > cavo.metri_teorici) {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: `I metri posati (${metri}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n        }));\n      } else {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: ''\n        }));\n      }\n    }\n  };\n\n  // Validazione del form\n  const validateForm = () => {\n    const errors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'I metri posati sono obbligatori';\n    } else {\n      const metri = parseFloat(formData.metri_posati);\n      if (isNaN(metri) || metri < 0) {\n        errors.metri_posati = 'Inserire un valore numerico valido maggiore o uguale a 0';\n      }\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina || formData.id_bobina === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina o utilizzare BOBINA VUOTA';\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Gestisce il salvataggio\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    // Dichiara le variabili fuori dal try-catch per poterle usare nel catch\n    const metriPosati = parseFloat(formData.metri_posati);\n    let idBobina = formData.id_bobina;\n    try {\n      setSaving(true);\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', cavo.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina);\n\n      // Chiamata API con la funzione originale completa\n      await caviService.updateMetriPosati(cantiereId, cavo.id_cavo, metriPosati, idBobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Successo\n      const successMessage = `Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metriPosati}m`;\n      onSuccess(successMessage);\n\n      // Chiudi il dialog\n      handleClose();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        const successMessage = `Metri posati aggiornati con successo. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n        handleClose();\n        return;\n      }\n\n      // Gestione errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        errorMessage = error.response.data.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setSaving(false);\n    }\n  };\n  const handleClose = () => {\n    if (!saving && !loading) {\n      setFormErrors({});\n      setFormWarnings({});\n      setFormData({\n        metri_posati: '',\n        id_bobina: ''\n      });\n      onClose();\n    }\n  };\n  const handleKeyPress = event => {\n    if (event.key === 'Enter' && !saving && !loading && formData.metri_posati.trim() && formData.id_bobina) {\n      handleSave();\n    }\n  };\n  if (!cavo) return null;\n\n  // Filtra le bobine compatibili\n  const getBobineCompatibili = () => {\n    if (!cavo) return [];\n    return bobine.filter(bobina => {\n      const isCompatible = bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione;\n      const matchesSearch = searchText === '' || bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) || bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase()) || bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase());\n      return isCompatible && matchesSearch && bobina.metri_residui > 0;\n    });\n  };\n\n  // Filtra le bobine incompatibili\n  const getBobineIncompatibili = () => {\n    if (!cavo) return [];\n    return bobine.filter(bobina => {\n      const isIncompatible = bobina.tipologia !== cavo.tipologia || bobina.sezione !== cavo.sezione;\n      const matchesSearch = searchText === '' || bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) || bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase()) || bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase());\n      return isIncompatible && matchesSearch && bobina.metri_residui > 0;\n    });\n  };\n  const bobineCompatibili = getBobineCompatibili();\n  const bobineIncompatibili = getBobineIncompatibili();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Dialog, {\n      open: open,\n      onClose: handleClose,\n      maxWidth: \"xl\",\n      fullWidth: true,\n      disableEscapeKeyDown: saving || loading,\n      PaperProps: {\n        sx: {\n          minHeight: '700px',\n          maxHeight: '90vh'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          component: \"div\",\n          children: [\"Inserisci Metri Posati - \", cavo.id_cavo]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          sx: {\n            mb: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 2.5,\n                height: '100%',\n                bgcolor: '#f8f9fa',\n                borderRadius: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                sx: {\n                  fontWeight: 'bold',\n                  mb: 2,\n                  color: '#1976d2'\n                },\n                children: \"Informazioni Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tipologia:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 479,\n                      columnNumber: 23\n                    }, this), \" \", cavo.tipologia || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Sezione:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 482,\n                      columnNumber: 23\n                    }, this), \" \", cavo.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri teorici:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 485,\n                      columnNumber: 23\n                    }, this), \" \", cavo.metri_teorici || 'N/A', \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Da:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 23\n                    }, this), \" \", cavo.ubicazione_partenza || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"A:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 493,\n                      columnNumber: 23\n                    }, this), \" \", cavo.ubicazione_arrivo || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Gi\\xE0 posati:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 496,\n                      columnNumber: 23\n                    }, this), \" \", cavo.metratura_reale || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              sx: {\n                p: 2.5,\n                height: '100%',\n                borderRadius: 2,\n                border: '2px solid #e3f2fd'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                sx: {\n                  fontWeight: 'bold',\n                  mb: 2,\n                  color: '#1976d2'\n                },\n                children: \"Metri da Installare\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                autoFocus: true,\n                fullWidth: true,\n                label: \"Metri Posati\",\n                name: \"metri_posati\",\n                type: \"number\",\n                value: formData.metri_posati,\n                onChange: handleFormChange,\n                onKeyPress: handleKeyPress,\n                error: Boolean(formErrors.metri_posati),\n                helperText: formErrors.metri_posati || formWarnings.metri_posati,\n                FormHelperTextProps: {\n                  sx: {\n                    color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n                  }\n                },\n                disabled: saving || loading,\n                size: \"medium\",\n                inputProps: {\n                  max: 999999,\n                  step: 0.1,\n                  style: {\n                    fontSize: '1.1rem',\n                    fontWeight: 'bold',\n                    textAlign: 'center'\n                  }\n                },\n                InputProps: {\n                  endAdornment: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    color: \"primary\",\n                    sx: {\n                      fontWeight: 'bold'\n                    },\n                    children: \"m\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 35\n                  }, this)\n                },\n                sx: {\n                  '& .MuiOutlinedInput-root': {\n                    '& fieldset': {\n                      borderColor: '#1976d2',\n                      borderWidth: 2\n                    },\n                    '&:hover fieldset': {\n                      borderColor: '#1565c0'\n                    },\n                    '&.Mui-focused fieldset': {\n                      borderColor: '#0d47a1'\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 2,\n              color: '#1976d2'\n            },\n            children: \"Selezione Bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              mb: 3,\n              bgcolor: '#fafafa',\n              borderRadius: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 5,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  size: \"small\",\n                  label: \"Cerca bobina\",\n                  variant: \"outlined\",\n                  value: searchText,\n                  onChange: handleSearchTextChange,\n                  placeholder: \"ID, tipologia, formazione...\",\n                  fullWidth: true,\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                      position: \"start\",\n                      children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 574,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 573,\n                      columnNumber: 25\n                    }, this),\n                    endAdornment: searchText ? /*#__PURE__*/_jsxDEV(InputAdornment, {\n                      position: \"end\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        \"aria-label\": \"clear search\",\n                        onClick: () => setSearchText(''),\n                        edge: \"end\",\n                        children: /*#__PURE__*/_jsxDEV(CancelIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 585,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 579,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 578,\n                      columnNumber: 25\n                    }, this) : null\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 7,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: formData.id_bobina === 'BOBINA_VUOTA' ? 'contained' : 'outlined',\n                  size: \"medium\",\n                  onClick: handleSelectBobinaVuota,\n                  fullWidth: true,\n                  color: formData.id_bobina === 'BOBINA_VUOTA' ? 'success' : 'primary',\n                  sx: {\n                    height: '40px',\n                    fontWeight: 'bold',\n                    textTransform: 'none'\n                  },\n                  children: \"BOBINA VUOTA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 595,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 13\n          }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'center',\n              my: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                gutterBottom: true,\n                sx: {\n                  color: 'success.main',\n                  fontWeight: 'medium'\n                },\n                children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                  sx: {\n                    mr: 1,\n                    verticalAlign: 'middle'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 21\n                }, this), \"Bobine Compatibili (\", bobineCompatibili.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  maxHeight: 300,\n                  overflow: 'auto',\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1\n                },\n                children: bobineCompatibili.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    p: 2,\n                    textAlign: 'center',\n                    color: 'text.secondary'\n                  },\n                  children: \"Nessuna bobina compatibile trovata\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(List, {\n                  dense: true,\n                  children: bobineCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n                    disablePadding: true,\n                    children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                      selected: formData.id_bobina === bobina.id_bobina,\n                      onClick: () => handleSelectBobinaCompatibile(bobina),\n                      sx: {\n                        '&.Mui-selected': {\n                          backgroundColor: 'rgba(76, 175, 80, 0.1)',\n                          '&:hover': {\n                            backgroundColor: 'rgba(76, 175, 80, 0.2)'\n                          }\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                        primary: /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            fontWeight: \"medium\",\n                            children: bobina.numero_bobina || bobina.id_bobina\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 653,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                            size: \"small\",\n                            label: `${bobina.metri_residui}m`,\n                            color: \"success\",\n                            variant: \"outlined\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 656,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 652,\n                          columnNumber: 35\n                        }, this),\n                        secondary: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"text.secondary\",\n                          children: [bobina.tipologia, \" - \", bobina.sezione]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 665,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 650,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 638,\n                      columnNumber: 29\n                    }, this)\n                  }, bobina.id_bobina, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 634,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                gutterBottom: true,\n                sx: {\n                  color: 'warning.main',\n                  fontWeight: 'medium'\n                },\n                children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                  sx: {\n                    mr: 1,\n                    verticalAlign: 'middle'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 21\n                }, this), \"Bobine Incompatibili (\", bobineIncompatibili.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  maxHeight: 300,\n                  overflow: 'auto',\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1\n                },\n                children: bobineIncompatibili.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    p: 2,\n                    textAlign: 'center',\n                    color: 'text.secondary'\n                  },\n                  children: \"Nessuna bobina incompatibile trovata\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(List, {\n                  dense: true,\n                  children: bobineIncompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n                    disablePadding: true,\n                    children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                      onClick: () => handleSelectBobinaIncompatibile(bobina),\n                      sx: {\n                        '&:hover': {\n                          backgroundColor: 'rgba(255, 152, 0, 0.1)'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                        primary: /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            fontWeight: \"medium\",\n                            children: bobina.numero_bobina || bobina.id_bobina\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 708,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                            size: \"small\",\n                            label: `${bobina.metri_residui}m`,\n                            color: \"warning\",\n                            variant: \"outlined\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 711,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 707,\n                          columnNumber: 35\n                        }, this),\n                        secondary: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"text.secondary\",\n                          children: [bobina.tipologia, \" - \", bobina.sezione]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 720,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 705,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 697,\n                      columnNumber: 29\n                    }, this)\n                  }, bobina.id_bobina, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 693,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 691,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 15\n          }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mt: 2\n            },\n            children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 15\n          }, this), formErrors.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"error\",\n            sx: {\n              mt: 1,\n              display: 'block'\n            },\n            children: formErrors.id_bobina\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleClose,\n          disabled: saving || loading,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSave,\n          variant: \"contained\",\n          disabled: saving || loading || !formData.metri_posati || !formData.id_bobina,\n          startIcon: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 33\n          }, this) : null,\n          children: saving ? 'Salvando...' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 756,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 749,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(InserisciMetriDialogCompleto, \"cMUClNdKTiswIATi+jMugZVESq8=\");\n_c = InserisciMetriDialogCompleto;\nexport default InserisciMetriDialogCompleto;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriDialogCompleto\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Typography", "Box", "Grid", "Paper", "<PERSON><PERSON>", "CircularProgress", "Chip", "InputAdornment", "IconButton", "List", "ListItem", "ListItemButton", "ListItemText", "Search", "SearchIcon", "Cancel", "CancelIcon", "CheckCircle", "CheckCircleIcon", "Warning", "WarningIcon", "Close", "CloseIcon", "caviService", "parcoCaviService", "IncompatibleReelDialog", "getReelStateColor", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriDialogCompleto", "open", "onClose", "cavo", "cantiereId", "onSuccess", "onError", "loading", "_s", "formData", "setFormData", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "saving", "setSaving", "bobine", "set<PERSON>ob<PERSON>", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchText", "setSearchText", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReel", "setIncompatibleReel", "getBobinaNumber", "idBobina", "includes", "split", "bobina", "find", "b", "numero_bobina", "handleSelectBobinaCompatibile", "console", "log", "prev", "handleSelectBobinaIncompatibile", "handleUseIncompatibleReel", "id_cavo", "error", "handleCloseIncompatibleReelDialog", "handleSelectBobinaVuota", "handleSearchTextChange", "event", "target", "value", "loadBobine", "bobine<PERSON><PERSON>", "getBobine", "length", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "stato_bobina", "metri_residui", "tipologia", "sezione", "cavoTipologia", "String", "trim", "toLowerCase", "cavoSezione", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "bobineNonCompatibili", "for<PERSON>ach", "bobinaTipologia", "bobinaSezione", "tipologiaMatch", "sezioneMatch", "isCompatible", "push", "sort", "a", "bobineOrdinate", "_cavo$metratura_reale", "metratura_reale", "toString", "handleFormChange", "name", "metri", "parseFloat", "isNaN", "metri_te<PERSON>ci", "validateForm", "errors", "Object", "keys", "handleSave", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "handleClose", "_error$response", "_error$response$data", "success", "errorMessage", "response", "data", "detail", "message", "handleKeyPress", "key", "getBobineCompatibili", "matchesSearch", "getBobineIncompatibili", "isIncompatible", "bobineIncompatibili", "children", "max<PERSON><PERSON><PERSON>", "fullWidth", "disableEscapeKeyDown", "PaperProps", "sx", "minHeight", "maxHeight", "pb", "variant", "component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dividers", "p", "container", "spacing", "mb", "item", "xs", "md", "height", "bgcolor", "borderRadius", "fontWeight", "color", "ubicazione_partenza", "ubicazione_arrivo", "border", "autoFocus", "label", "type", "onChange", "onKeyPress", "Boolean", "helperText", "FormHelperTextProps", "disabled", "size", "inputProps", "max", "step", "style", "fontSize", "textAlign", "InputProps", "endAdornment", "borderColor", "borderWidth", "alignItems", "sm", "placeholder", "startAdornment", "position", "onClick", "edge", "textTransform", "display", "justifyContent", "my", "gutterBottom", "mr", "verticalAlign", "overflow", "dense", "map", "disablePadding", "selected", "backgroundColor", "primary", "secondary", "severity", "mt", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/InserisciMetriDialogCompleto.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  TextField,\n  Typography,\n  Box,\n  Grid,\n  Paper,\n  Alert,\n  CircularProgress,\n  Chip,\n  InputAdornment,\n  IconButton,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Cancel as CancelIcon,\n  CheckCircle as CheckCircleIcon,\n  Warning as WarningIcon,\n  Close as CloseIcon\n} from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport { getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Dialog completo per inserire i metri posati di un cavo preselezionato\n * Basato su InserisciMetriForm ma adattato per dialog con cavo preselezionato\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Se il dialogo è aperto\n * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude\n * @param {Object} props.cavo - Cavo preselezionato\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.loading - Indica se il salvataggio è in corso\n */\nconst InserisciMetriDialogCompleto = ({\n  open = false,\n  onClose = () => {},\n  cavo = null,\n  cantiereId,\n  onSuccess = () => {},\n  onError = () => {},\n  loading = false\n}) => {\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [saving, setSaving] = useState(false);\n  \n  // Stati per bobine\n  const [bobine, setBobine] = useState([]);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per la ricerca delle bobine\n  const [searchText, setSearchText] = useState('');\n\n  // Stati per dialoghi\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    if (!idBobina || idBobina === 'BOBINA_VUOTA') return 'VUOTA';\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    // Cerca nella lista bobine per ottenere il numero_bobina\n    const bobina = bobine.find(b => b.id_bobina === idBobina);\n    return bobina ? bobina.numero_bobina || idBobina : idBobina;\n  };\n\n  // Gestisce la selezione di una bobina compatibile\n  const handleSelectBobinaCompatibile = (bobina) => {\n    console.log('Bobina compatibile selezionata:', bobina);\n    setFormData(prev => ({\n      ...prev,\n      id_bobina: bobina.id_bobina\n    }));\n\n    // Reset degli errori\n    setFormErrors(prev => ({\n      ...prev,\n      id_bobina: ''\n    }));\n  };\n\n  // Gestisce la selezione di una bobina incompatibile\n  const handleSelectBobinaIncompatibile = (bobina) => {\n    console.log('Bobina incompatibile selezionata:', bobina);\n    setIncompatibleReel(bobina);\n    setShowIncompatibleReelDialog(true);\n  };\n\n  // Gestisce l'uso di una bobina incompatibile\n  const handleUseIncompatibleReel = async (bobina, cavo) => {\n    try {\n      console.log(`Utilizzo bobina incompatibile ${bobina.id_bobina} con cavo ${cavo.id_cavo} senza aggiornare le caratteristiche`);\n\n      // Seleziona la bobina incompatibile\n      setFormData(prev => ({\n        ...prev,\n        id_bobina: bobina.id_bobina\n      }));\n\n      // Reset degli errori\n      setFormErrors(prev => ({\n        ...prev,\n        id_bobina: ''\n      }));\n\n      setShowIncompatibleReelDialog(false);\n    } catch (error) {\n      console.error('Errore durante la selezione della bobina incompatibile:', error);\n    }\n  };\n\n  // Gestisce la chiusura del dialog bobina incompatibile\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReel(null);\n  };\n\n  // Gestisce la selezione di BOBINA_VUOTA\n  const handleSelectBobinaVuota = () => {\n    console.log('BOBINA_VUOTA selezionata');\n    setFormData(prev => ({\n      ...prev,\n      id_bobina: 'BOBINA_VUOTA'\n    }));\n\n    // Reset degli errori\n    setFormErrors(prev => ({\n      ...prev,\n      id_bobina: ''\n    }));\n  };\n\n  // Gestisce il cambio del testo di ricerca\n  const handleSearchTextChange = (event) => {\n    setSearchText(event.target.value);\n  };\n\n  // Carica le bobine disponibili\n  const loadBobine = useCallback(async () => {\n    if (!cantiereId || !cavo) return;\n\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine iniziato...');\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter(bobina =>\n        bobina.stato_bobina !== 'Terminata' &&\n        bobina.stato_bobina !== 'Over' &&\n        bobina.metri_residui > 0\n      );\n\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte\n      if (cavo && cavo.tipologia && cavo.sezione) {\n        console.log('Cavo selezionato, evidenziando bobine compatibili...');\n        console.log('Dati cavo:', {\n          id_cavo: cavo.id_cavo,\n          tipologia: cavo.tipologia,\n          sezione: cavo.sezione\n        });\n\n        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui\n        const cavoTipologia = String(cavo.tipologia || '').trim().toLowerCase();\n        const cavoSezione = String(cavo.sezione || '0').trim();\n\n        // Identifica le bobine compatibili\n        const bobineCompatibili = [];\n        const bobineNonCompatibili = [];\n\n        // Dividi le bobine in compatibili e non compatibili\n        bobineUtilizzabili.forEach(bobina => {\n          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();\n          const bobinaSezione = String(bobina.sezione || '0').trim();\n\n          // Verifica compatibilità\n          const tipologiaMatch = bobinaTipologia === cavoTipologia;\n          const sezioneMatch = bobinaSezione === cavoSezione;\n          const isCompatible = tipologiaMatch && sezioneMatch;\n\n          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {\n            'Tipologia bobina': `\"${bobina.tipologia}\"`,\n            'Tipologia cavo': `\"${cavo.tipologia}\"`,\n            'Tipologie uguali?': tipologiaMatch,\n            'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n            'Sezione cavo': `\"${String(cavo.sezione)}\"`,\n            'Sezioni uguali?': sezioneMatch,\n            'Stato bobina': bobina.stato_bobina,\n            'Metri residui': bobina.metri_residui,\n            'Compatibile?': isCompatible\n          });\n\n          if (isCompatible) {\n            bobineCompatibili.push(bobina);\n          } else {\n            bobineNonCompatibili.push(bobina);\n          }\n        });\n\n        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);\n        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);\n\n        // Log dettagliato delle bobine compatibili\n        if (bobineCompatibili.length > 0) {\n          console.log('Dettaglio bobine compatibili:');\n          bobineCompatibili.forEach(bobina => {\n            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);\n          });\n        } else {\n          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n        }\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];\n\n        // Imposta le bobine nel componente\n        setBobine(bobineOrdinate);\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n        setBobine(bobineUtilizzabili);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      setBobine([]);\n    } finally {\n      setBobineLoading(false);\n    }\n  }, [cantiereId, cavo]);\n\n  // Reset quando si apre il dialogo\n  useEffect(() => {\n    if (open && cavo) {\n      setFormData({\n        metri_posati: cavo.metratura_reale?.toString() || '',\n        id_bobina: ''\n      });\n      setFormErrors({});\n      setFormWarnings({});\n      setSaving(false);\n\n      // Carica le bobine disponibili\n      loadBobine();\n    }\n  }, [open, cavo, cantiereId, loadBobine]);\n\n  // Gestisce i cambiamenti del form\n  const handleFormChange = (event) => {\n    const { name, value } = event.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Rimuovi errori quando l'utente inizia a digitare\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n    \n    // Validazione in tempo reale per metri posati\n    if (name === 'metri_posati' && value && cavo) {\n      const metri = parseFloat(value);\n      if (!isNaN(metri) && metri > cavo.metri_teorici) {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: `I metri posati (${metri}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n        }));\n      } else {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: ''\n        }));\n      }\n    }\n  };\n\n  // Validazione del form\n  const validateForm = () => {\n    const errors = {};\n    \n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'I metri posati sono obbligatori';\n    } else {\n      const metri = parseFloat(formData.metri_posati);\n      if (isNaN(metri) || metri < 0) {\n        errors.metri_posati = 'Inserire un valore numerico valido maggiore o uguale a 0';\n      }\n    }\n    \n    // Validazione bobina\n    if (!formData.id_bobina || formData.id_bobina === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina o utilizzare BOBINA VUOTA';\n    }\n    \n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Gestisce il salvataggio\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    // Dichiara le variabili fuori dal try-catch per poterle usare nel catch\n    const metriPosati = parseFloat(formData.metri_posati);\n    let idBobina = formData.id_bobina;\n\n    try {\n      setSaving(true);\n      \n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', cavo.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina);\n      \n      // Chiamata API con la funzione originale completa\n      await caviService.updateMetriPosati(\n        cantiereId,\n        cavo.id_cavo,\n        metriPosati,\n        idBobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n      \n      // Successo\n      const successMessage = `Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metriPosati}m`;\n      onSuccess(successMessage);\n      \n      // Chiudi il dialog\n      handleClose();\n      \n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      \n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        const successMessage = `Metri posati aggiornati con successo. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n        handleClose();\n        return;\n      }\n      \n      // Gestione errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response?.data?.detail) {\n        errorMessage = error.response.data.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      \n      onError(errorMessage);\n      \n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!saving && !loading) {\n      setFormErrors({});\n      setFormWarnings({});\n      setFormData({ metri_posati: '', id_bobina: '' });\n      onClose();\n    }\n  };\n\n  const handleKeyPress = (event) => {\n    if (event.key === 'Enter' && !saving && !loading && formData.metri_posati.trim() && formData.id_bobina) {\n      handleSave();\n    }\n  };\n\n  if (!cavo) return null;\n\n  // Filtra le bobine compatibili\n  const getBobineCompatibili = () => {\n    if (!cavo) return [];\n\n    return bobine.filter(bobina => {\n      const isCompatible = bobina.tipologia === cavo.tipologia &&\n                          bobina.sezione === cavo.sezione;\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()));\n      return isCompatible && matchesSearch && bobina.metri_residui > 0;\n    });\n  };\n\n  // Filtra le bobine incompatibili\n  const getBobineIncompatibili = () => {\n    if (!cavo) return [];\n\n    return bobine.filter(bobina => {\n      const isIncompatible = bobina.tipologia !== cavo.tipologia ||\n                            bobina.sezione !== cavo.sezione;\n      const matchesSearch = searchText === '' ||\n                           bobina.id_bobina.toLowerCase().includes(searchText.toLowerCase()) ||\n                           (bobina.tipologia && bobina.tipologia.toLowerCase().includes(searchText.toLowerCase())) ||\n                           (bobina.numero_bobina && bobina.numero_bobina.toLowerCase().includes(searchText.toLowerCase()));\n      return isIncompatible && matchesSearch && bobina.metri_residui > 0;\n    });\n  };\n\n  const bobineCompatibili = getBobineCompatibili();\n  const bobineIncompatibili = getBobineIncompatibili();\n\n  return (\n    <>\n      <Dialog\n        open={open}\n        onClose={handleClose}\n        maxWidth=\"xl\"\n        fullWidth\n        disableEscapeKeyDown={saving || loading}\n        PaperProps={{\n          sx: {\n            minHeight: '700px',\n            maxHeight: '90vh'\n          }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" component=\"div\">\n            Inserisci Metri Posati - {cavo.id_cavo}\n          </Typography>\n        </DialogTitle>\n\n        <DialogContent dividers sx={{ p: 3 }}>\n          {/* Sezione informazioni cavo e metri posati */}\n          <Grid container spacing={3} sx={{ mb: 4 }}>\n            {/* Informazioni cavo */}\n            <Grid item xs={12} md={8}>\n              <Paper sx={{ p: 2.5, height: '100%', bgcolor: '#f8f9fa', borderRadius: 2 }}>\n                <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 2, color: '#1976d2' }}>\n                  Informazioni Cavo\n                </Typography>\n                <Grid container spacing={2}>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                      <strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                      <strong>Sezione:</strong> {cavo.sezione || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      <strong>Metri teorici:</strong> {cavo.metri_teorici || 'N/A'} m\n                    </Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                      <strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                      <strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      <strong>Già posati:</strong> {cavo.metratura_reale || 0} m\n                    </Typography>\n                  </Grid>\n                </Grid>\n              </Paper>\n            </Grid>\n\n            {/* Campo metri posati */}\n            <Grid item xs={12} md={4}>\n              <Paper sx={{ p: 2.5, height: '100%', borderRadius: 2, border: '2px solid #e3f2fd' }}>\n                <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 2, color: '#1976d2' }}>\n                  Metri da Installare\n                </Typography>\n                <TextField\n                  autoFocus\n                  fullWidth\n                  label=\"Metri Posati\"\n                  name=\"metri_posati\"\n                  type=\"number\"\n                  value={formData.metri_posati}\n                  onChange={handleFormChange}\n                  onKeyPress={handleKeyPress}\n                  error={Boolean(formErrors.metri_posati)}\n                  helperText={formErrors.metri_posati || formWarnings.metri_posati}\n                  FormHelperTextProps={{\n                    sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n                  }}\n                  disabled={saving || loading}\n                  size=\"medium\"\n                  inputProps={{\n                    max: 999999,\n                    step: 0.1,\n                    style: { fontSize: '1.1rem', fontWeight: 'bold', textAlign: 'center' }\n                  }}\n                  InputProps={{\n                    endAdornment: <Typography variant=\"h6\" color=\"primary\" sx={{ fontWeight: 'bold' }}>m</Typography>\n                  }}\n                  sx={{\n                    '& .MuiOutlinedInput-root': {\n                      '& fieldset': {\n                        borderColor: '#1976d2',\n                        borderWidth: 2\n                      },\n                      '&:hover fieldset': {\n                        borderColor: '#1565c0',\n                      },\n                      '&.Mui-focused fieldset': {\n                        borderColor: '#0d47a1',\n                      },\n                    },\n                  }}\n                />\n              </Paper>\n            </Grid>\n          </Grid>\n\n          {/* Selezione bobina con doppia lista */}\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold', mb: 2, color: '#1976d2' }}>\n              Selezione Bobina\n            </Typography>\n\n            {/* Controlli di ricerca compatti */}\n            <Paper sx={{ p: 2, mb: 3, bgcolor: '#fafafa', borderRadius: 2 }}>\n              <Grid container spacing={2} alignItems=\"center\">\n                {/* Campo di ricerca */}\n                <Grid item xs={12} sm={5}>\n                  <TextField\n                    size=\"small\"\n                    label=\"Cerca bobina\"\n                    variant=\"outlined\"\n                    value={searchText}\n                    onChange={handleSearchTextChange}\n                    placeholder=\"ID, tipologia, formazione...\"\n                    fullWidth\n                    InputProps={{\n                      startAdornment: (\n                        <InputAdornment position=\"start\">\n                          <SearchIcon fontSize=\"small\" />\n                        </InputAdornment>\n                      ),\n                      endAdornment: searchText ? (\n                        <InputAdornment position=\"end\">\n                          <IconButton\n                            size=\"small\"\n                            aria-label=\"clear search\"\n                            onClick={() => setSearchText('')}\n                            edge=\"end\"\n                          >\n                            <CancelIcon fontSize=\"small\" />\n                          </IconButton>\n                        </InputAdornment>\n                      ) : null\n                    }}\n                  />\n                </Grid>\n\n                {/* Pulsante BOBINA VUOTA */}\n                <Grid item xs={12} sm={7}>\n                  <Button\n                    variant={formData.id_bobina === 'BOBINA_VUOTA' ? 'contained' : 'outlined'}\n                    size=\"medium\"\n                    onClick={handleSelectBobinaVuota}\n                    fullWidth\n                    color={formData.id_bobina === 'BOBINA_VUOTA' ? 'success' : 'primary'}\n                    sx={{\n                      height: '40px',\n                      fontWeight: 'bold',\n                      textTransform: 'none'\n                    }}\n                  >\n                    BOBINA VUOTA\n                  </Button>\n                </Grid>\n              </Grid>\n            </Paper>\n\n            {bobineLoading ? (\n              <Box sx={{ display: 'flex', justifyContent: 'center', my: 1 }}>\n                <CircularProgress size={24} />\n              </Box>\n            ) : (\n              <Grid container spacing={3}>\n                {/* Bobine compatibili */}\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"subtitle1\" gutterBottom sx={{ color: 'success.main', fontWeight: 'medium' }}>\n                    <CheckCircleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />\n                    Bobine Compatibili ({bobineCompatibili.length})\n                  </Typography>\n\n                  <Box sx={{ maxHeight: 300, overflow: 'auto', border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                    {bobineCompatibili.length === 0 ? (\n                      <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>\n                        Nessuna bobina compatibile trovata\n                      </Box>\n                    ) : (\n                      <List dense>\n                        {bobineCompatibili.map((bobina) => (\n                          <ListItem\n                            key={bobina.id_bobina}\n                            disablePadding\n                          >\n                            <ListItemButton\n                              selected={formData.id_bobina === bobina.id_bobina}\n                              onClick={() => handleSelectBobinaCompatibile(bobina)}\n                              sx={{\n                                '&.Mui-selected': {\n                                  backgroundColor: 'rgba(76, 175, 80, 0.1)',\n                                  '&:hover': {\n                                    backgroundColor: 'rgba(76, 175, 80, 0.2)',\n                                  },\n                                },\n                              }}\n                            >\n                              <ListItemText\n                                primary={\n                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                                    <Typography variant=\"body2\" fontWeight=\"medium\">\n                                      {bobina.numero_bobina || bobina.id_bobina}\n                                    </Typography>\n                                    <Chip\n                                      size=\"small\"\n                                      label={`${bobina.metri_residui}m`}\n                                      color=\"success\"\n                                      variant=\"outlined\"\n                                    />\n                                  </Box>\n                                }\n                                secondary={\n                                  <Typography variant=\"caption\" color=\"text.secondary\">\n                                    {bobina.tipologia} - {bobina.sezione}\n                                  </Typography>\n                                }\n                              />\n                            </ListItemButton>\n                          </ListItem>\n                        ))}\n                      </List>\n                    )}\n                  </Box>\n                </Grid>\n\n                {/* Bobine incompatibili */}\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"subtitle1\" gutterBottom sx={{ color: 'warning.main', fontWeight: 'medium' }}>\n                    <WarningIcon sx={{ mr: 1, verticalAlign: 'middle' }} />\n                    Bobine Incompatibili ({bobineIncompatibili.length})\n                  </Typography>\n\n                  <Box sx={{ maxHeight: 300, overflow: 'auto', border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                    {bobineIncompatibili.length === 0 ? (\n                      <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>\n                        Nessuna bobina incompatibile trovata\n                      </Box>\n                    ) : (\n                      <List dense>\n                        {bobineIncompatibili.map((bobina) => (\n                          <ListItem\n                            key={bobina.id_bobina}\n                            disablePadding\n                          >\n                            <ListItemButton\n                              onClick={() => handleSelectBobinaIncompatibile(bobina)}\n                              sx={{\n                                '&:hover': {\n                                  backgroundColor: 'rgba(255, 152, 0, 0.1)',\n                                },\n                              }}\n                            >\n                              <ListItemText\n                                primary={\n                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                                    <Typography variant=\"body2\" fontWeight=\"medium\">\n                                      {bobina.numero_bobina || bobina.id_bobina}\n                                    </Typography>\n                                    <Chip\n                                      size=\"small\"\n                                      label={`${bobina.metri_residui}m`}\n                                      color=\"warning\"\n                                      variant=\"outlined\"\n                                    />\n                                  </Box>\n                                }\n                                secondary={\n                                  <Typography variant=\"caption\" color=\"text.secondary\">\n                                    {bobina.tipologia} - {bobina.sezione}\n                                  </Typography>\n                                }\n                              />\n                            </ListItemButton>\n                          </ListItem>\n                        ))}\n                      </List>\n                    )}\n                  </Box>\n                </Grid>\n              </Grid>\n            )}\n\n            {bobine.length === 0 && !bobineLoading && (\n              <Alert severity=\"warning\" sx={{ mt: 2 }}>\n                Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n              </Alert>\n            )}\n\n            {formErrors.id_bobina && (\n              <Typography variant=\"caption\" color=\"error\" sx={{ mt: 1, display: 'block' }}>\n                {formErrors.id_bobina}\n              </Typography>\n            )}\n          </Box>\n        </DialogContent>\n\n        <DialogActions>\n          <Button\n            onClick={handleClose}\n            disabled={saving || loading}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSave}\n            variant=\"contained\"\n            disabled={saving || loading || !formData.metri_posati || !formData.id_bobina}\n            startIcon={saving ? <CircularProgress size={20} /> : null}\n          >\n            {saving ? 'Salvando...' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </>\n  );\n};\n\nexport default InserisciMetriDialogCompleto;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,QACP,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,SAASC,iBAAiB,QAAQ,wBAAwB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAaA,MAAMC,4BAA4B,GAAGA,CAAC;EACpCC,IAAI,GAAG,KAAK;EACZC,OAAO,GAAGA,CAAA,KAAM,CAAC,CAAC;EAClBC,IAAI,GAAG,IAAI;EACXC,UAAU;EACVC,SAAS,GAAGA,CAAA,KAAM,CAAC,CAAC;EACpBC,OAAO,GAAGA,CAAA,KAAM,CAAC,CAAC;EAClBC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC;IACvCmD,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACyD,MAAM,EAAEC,SAAS,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAAC2D,MAAM,EAAEC,SAAS,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6D,aAAa,EAAEC,gBAAgB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAACiE,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACmE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAMqE,eAAe,GAAIC,QAAQ,IAAK;IACpC,IAAI,CAACA,QAAQ,IAAIA,QAAQ,KAAK,cAAc,EAAE,OAAO,OAAO;IAC5D;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA;IACA,MAAMC,MAAM,GAAGd,MAAM,CAACe,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvB,SAAS,KAAKkB,QAAQ,CAAC;IACzD,OAAOG,MAAM,GAAGA,MAAM,CAACG,aAAa,IAAIN,QAAQ,GAAGA,QAAQ;EAC7D,CAAC;;EAED;EACA,MAAMO,6BAA6B,GAAIJ,MAAM,IAAK;IAChDK,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEN,MAAM,CAAC;IACtDvB,WAAW,CAAC8B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP5B,SAAS,EAAEqB,MAAM,CAACrB;IACpB,CAAC,CAAC,CAAC;;IAEH;IACAE,aAAa,CAAC0B,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP5B,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM6B,+BAA+B,GAAIR,MAAM,IAAK;IAClDK,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEN,MAAM,CAAC;IACxDL,mBAAmB,CAACK,MAAM,CAAC;IAC3BP,6BAA6B,CAAC,IAAI,CAAC;EACrC,CAAC;;EAED;EACA,MAAMgB,yBAAyB,GAAG,MAAAA,CAAOT,MAAM,EAAE9B,IAAI,KAAK;IACxD,IAAI;MACFmC,OAAO,CAACC,GAAG,CAAC,iCAAiCN,MAAM,CAACrB,SAAS,aAAaT,IAAI,CAACwC,OAAO,sCAAsC,CAAC;;MAE7H;MACAjC,WAAW,CAAC8B,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP5B,SAAS,EAAEqB,MAAM,CAACrB;MACpB,CAAC,CAAC,CAAC;;MAEH;MACAE,aAAa,CAAC0B,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP5B,SAAS,EAAE;MACb,CAAC,CAAC,CAAC;MAEHc,6BAA6B,CAAC,KAAK,CAAC;IACtC,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;IACjF;EACF,CAAC;;EAED;EACA,MAAMC,iCAAiC,GAAGA,CAAA,KAAM;IAC9CnB,6BAA6B,CAAC,KAAK,CAAC;IACpCE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMkB,uBAAuB,GAAGA,CAAA,KAAM;IACpCR,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvC7B,WAAW,CAAC8B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP5B,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;;IAEH;IACAE,aAAa,CAAC0B,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP5B,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMmC,sBAAsB,GAAIC,KAAK,IAAK;IACxCxB,aAAa,CAACwB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGzF,WAAW,CAAC,YAAY;IACzC,IAAI,CAAC0C,UAAU,IAAI,CAACD,IAAI,EAAE;IAE1B,IAAI;MACFmB,gBAAgB,CAAC,IAAI,CAAC;MACtBgB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMa,UAAU,GAAG,MAAM3D,gBAAgB,CAAC4D,SAAS,CAACjD,UAAU,CAAC;MAC/DkC,OAAO,CAACC,GAAG,CAAC,oBAAoBa,UAAU,CAACE,MAAM,EAAE,CAAC;;MAEpD;MACA,MAAMC,kBAAkB,GAAGH,UAAU,CAACI,MAAM,CAACvB,MAAM,IACjDA,MAAM,CAACwB,YAAY,KAAK,WAAW,IACnCxB,MAAM,CAACwB,YAAY,KAAK,MAAM,IAC9BxB,MAAM,CAACyB,aAAa,GAAG,CACzB,CAAC;MAEDpB,OAAO,CAACC,GAAG,CAAC,wBAAwBgB,kBAAkB,CAACD,MAAM,EAAE,CAAC;;MAEhE;MACA,IAAInD,IAAI,IAAIA,IAAI,CAACwD,SAAS,IAAIxD,IAAI,CAACyD,OAAO,EAAE;QAC1CtB,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnED,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;UACxBI,OAAO,EAAExC,IAAI,CAACwC,OAAO;UACrBgB,SAAS,EAAExD,IAAI,CAACwD,SAAS;UACzBC,OAAO,EAAEzD,IAAI,CAACyD;QAChB,CAAC,CAAC;;QAEF;QACA,MAAMC,aAAa,GAAGC,MAAM,CAAC3D,IAAI,CAACwD,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACvE,MAAMC,WAAW,GAAGH,MAAM,CAAC3D,IAAI,CAACyD,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC;;QAEtD;QACA,MAAMG,iBAAiB,GAAG,EAAE;QAC5B,MAAMC,oBAAoB,GAAG,EAAE;;QAE/B;QACAZ,kBAAkB,CAACa,OAAO,CAACnC,MAAM,IAAI;UACnC,MAAMoC,eAAe,GAAGP,MAAM,CAAC7B,MAAM,CAAC0B,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAC3E,MAAMM,aAAa,GAAGR,MAAM,CAAC7B,MAAM,CAAC2B,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC;;UAE1D;UACA,MAAMQ,cAAc,GAAGF,eAAe,KAAKR,aAAa;UACxD,MAAMW,YAAY,GAAGF,aAAa,KAAKL,WAAW;UAClD,MAAMQ,YAAY,GAAGF,cAAc,IAAIC,YAAY;UAEnDlC,OAAO,CAACC,GAAG,CAAC,iCAAiCN,MAAM,CAACrB,SAAS,GAAG,EAAE;YAChE,kBAAkB,EAAE,IAAIqB,MAAM,CAAC0B,SAAS,GAAG;YAC3C,gBAAgB,EAAE,IAAIxD,IAAI,CAACwD,SAAS,GAAG;YACvC,mBAAmB,EAAEY,cAAc;YACnC,gBAAgB,EAAE,IAAIT,MAAM,CAAC7B,MAAM,CAAC2B,OAAO,CAAC,GAAG;YAC/C,cAAc,EAAE,IAAIE,MAAM,CAAC3D,IAAI,CAACyD,OAAO,CAAC,GAAG;YAC3C,iBAAiB,EAAEY,YAAY;YAC/B,cAAc,EAAEvC,MAAM,CAACwB,YAAY;YACnC,eAAe,EAAExB,MAAM,CAACyB,aAAa;YACrC,cAAc,EAAEe;UAClB,CAAC,CAAC;UAEF,IAAIA,YAAY,EAAE;YAChBP,iBAAiB,CAACQ,IAAI,CAACzC,MAAM,CAAC;UAChC,CAAC,MAAM;YACLkC,oBAAoB,CAACO,IAAI,CAACzC,MAAM,CAAC;UACnC;QACF,CAAC,CAAC;QAEFK,OAAO,CAACC,GAAG,CAAC,+BAA+B2B,iBAAiB,CAACZ,MAAM,EAAE,CAAC;QACtEhB,OAAO,CAACC,GAAG,CAAC,2BAA2B4B,oBAAoB,CAACb,MAAM,EAAE,CAAC;;QAErE;QACA,IAAIY,iBAAiB,CAACZ,MAAM,GAAG,CAAC,EAAE;UAChChB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;UAC5C2B,iBAAiB,CAACE,OAAO,CAACnC,MAAM,IAAI;YAClCK,OAAO,CAACC,GAAG,CAAC,KAAKN,MAAM,CAACrB,SAAS,KAAKqB,MAAM,CAAC0B,SAAS,MAAM1B,MAAM,CAAC2B,OAAO,KAAK3B,MAAM,CAACyB,aAAa,IAAI,CAAC;UAC1G,CAAC,CAAC;QACJ,CAAC,MAAM;UACLpB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAChE;;QAEA;QACA2B,iBAAiB,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEzC,CAAC,KAAKA,CAAC,CAACuB,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;QACnES,oBAAoB,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEzC,CAAC,KAAKA,CAAC,CAACuB,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;;QAEtE;QACA,MAAMmB,cAAc,GAAG,CAAC,GAAGX,iBAAiB,EAAE,GAAGC,oBAAoB,CAAC;;QAEtE;QACA/C,SAAS,CAACyD,cAAc,CAAC;MAC3B,CAAC,MAAM;QACL;QACAvC,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpEgB,kBAAkB,CAACoB,IAAI,CAAC,CAACC,CAAC,EAAEzC,CAAC,KAAKA,CAAC,CAACuB,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;QACpEtC,SAAS,CAACmC,kBAAkB,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DxB,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC,EAAE,CAAClB,UAAU,EAAED,IAAI,CAAC,CAAC;;EAEtB;EACA1C,SAAS,CAAC,MAAM;IACd,IAAIwC,IAAI,IAAIE,IAAI,EAAE;MAAA,IAAA2E,qBAAA;MAChBpE,WAAW,CAAC;QACVC,YAAY,EAAE,EAAAmE,qBAAA,GAAA3E,IAAI,CAAC4E,eAAe,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,QAAQ,CAAC,CAAC,KAAI,EAAE;QACpDpE,SAAS,EAAE;MACb,CAAC,CAAC;MACFE,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;MACnBE,SAAS,CAAC,KAAK,CAAC;;MAEhB;MACAiC,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAClD,IAAI,EAAEE,IAAI,EAAEC,UAAU,EAAE+C,UAAU,CAAC,CAAC;;EAExC;EACA,MAAM8B,gBAAgB,GAAIjC,KAAK,IAAK;IAClC,MAAM;MAAEkC,IAAI;MAAEhC;IAAM,CAAC,GAAGF,KAAK,CAACC,MAAM;IACpCvC,WAAW,CAAC8B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAAC0C,IAAI,GAAGhC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIrC,UAAU,CAACqE,IAAI,CAAC,EAAE;MACpBpE,aAAa,CAAC0B,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAAC0C,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAIA,IAAI,KAAK,cAAc,IAAIhC,KAAK,IAAI/C,IAAI,EAAE;MAC5C,MAAMgF,KAAK,GAAGC,UAAU,CAAClC,KAAK,CAAC;MAC/B,IAAI,CAACmC,KAAK,CAACF,KAAK,CAAC,IAAIA,KAAK,GAAGhF,IAAI,CAACmF,aAAa,EAAE;QAC/CtE,eAAe,CAACwB,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP7B,YAAY,EAAE,mBAAmBwE,KAAK,yCAAyChF,IAAI,CAACmF,aAAa;QACnG,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLtE,eAAe,CAACwB,IAAI,KAAK;UACvB,GAAGA,IAAI;UACP7B,YAAY,EAAE;QAChB,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC;;EAED;EACA,MAAM4E,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAC/E,QAAQ,CAACE,YAAY,IAAIF,QAAQ,CAACE,YAAY,CAACoD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEyB,MAAM,CAAC7E,YAAY,GAAG,iCAAiC;IACzD,CAAC,MAAM;MACL,MAAMwE,KAAK,GAAGC,UAAU,CAAC3E,QAAQ,CAACE,YAAY,CAAC;MAC/C,IAAI0E,KAAK,CAACF,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;QAC7BK,MAAM,CAAC7E,YAAY,GAAG,0DAA0D;MAClF;IACF;;IAEA;IACA,IAAI,CAACF,QAAQ,CAACG,SAAS,IAAIH,QAAQ,CAACG,SAAS,KAAK,EAAE,EAAE;MACpD4E,MAAM,CAAC5E,SAAS,GAAG,+DAA+D;IACpF;IAEAE,aAAa,CAAC0E,MAAM,CAAC;IACrB,OAAOC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAAClC,MAAM,KAAK,CAAC;EACzC,CAAC;;EAED;EACA,MAAMqC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACJ,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;;IAEA;IACA,MAAMK,WAAW,GAAGR,UAAU,CAAC3E,QAAQ,CAACE,YAAY,CAAC;IACrD,IAAImB,QAAQ,GAAGrB,QAAQ,CAACG,SAAS;IAEjC,IAAI;MACFM,SAAS,CAAC,IAAI,CAAC;MAEfoB,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEnC,UAAU,CAAC;MACxCkC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEpC,IAAI,CAACwC,OAAO,CAAC;MACvCL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEqD,WAAW,CAAC;MAC3CtD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAET,QAAQ,CAAC;;MAErC;MACA,MAAMtC,WAAW,CAACqG,iBAAiB,CACjCzF,UAAU,EACVD,IAAI,CAACwC,OAAO,EACZiD,WAAW,EACX9D,QAAQ,EACR,IAAI,CAAC;MACP,CAAC;;MAED;MACA,MAAMgE,cAAc,GAAG,oDAAoD3F,IAAI,CAACwC,OAAO,KAAKiD,WAAW,GAAG;MAC1GvF,SAAS,CAACyF,cAAc,CAAC;;MAEzB;MACAC,WAAW,CAAC,CAAC;IAEf,CAAC,CAAC,OAAOnD,KAAK,EAAE;MAAA,IAAAoD,eAAA,EAAAC,oBAAA;MACd3D,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAId,QAAQ,KAAK,cAAc,IAAIc,KAAK,CAACsD,OAAO,EAAE;QAChD,MAAMJ,cAAc,GAAG,qEAAqE;QAC5FzF,SAAS,CAACyF,cAAc,CAAC;QACzBC,WAAW,CAAC,CAAC;QACb;MACF;;MAEA;MACA,IAAII,YAAY,GAAG,kDAAkD;MACrE,KAAAH,eAAA,GAAIpD,KAAK,CAACwD,QAAQ,cAAAJ,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgBK,IAAI,cAAAJ,oBAAA,eAApBA,oBAAA,CAAsBK,MAAM,EAAE;QAChCH,YAAY,GAAGvD,KAAK,CAACwD,QAAQ,CAACC,IAAI,CAACC,MAAM;MAC3C,CAAC,MAAM,IAAI1D,KAAK,CAAC2D,OAAO,EAAE;QACxBJ,YAAY,GAAGvD,KAAK,CAAC2D,OAAO;MAC9B;MAEAjG,OAAO,CAAC6F,YAAY,CAAC;IAEvB,CAAC,SAAS;MACRjF,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAM6E,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAC9E,MAAM,IAAI,CAACV,OAAO,EAAE;MACvBO,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;MACnBN,WAAW,CAAC;QAAEC,YAAY,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAC,CAAC;MAChDV,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAMsG,cAAc,GAAIxD,KAAK,IAAK;IAChC,IAAIA,KAAK,CAACyD,GAAG,KAAK,OAAO,IAAI,CAACxF,MAAM,IAAI,CAACV,OAAO,IAAIE,QAAQ,CAACE,YAAY,CAACoD,IAAI,CAAC,CAAC,IAAItD,QAAQ,CAACG,SAAS,EAAE;MACtG+E,UAAU,CAAC,CAAC;IACd;EACF,CAAC;EAED,IAAI,CAACxF,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA,MAAMuG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAACvG,IAAI,EAAE,OAAO,EAAE;IAEpB,OAAOgB,MAAM,CAACqC,MAAM,CAACvB,MAAM,IAAI;MAC7B,MAAMwC,YAAY,GAAGxC,MAAM,CAAC0B,SAAS,KAAKxD,IAAI,CAACwD,SAAS,IACpC1B,MAAM,CAAC2B,OAAO,KAAKzD,IAAI,CAACyD,OAAO;MACnD,MAAM+C,aAAa,GAAGpF,UAAU,KAAK,EAAE,IAClBU,MAAM,CAACrB,SAAS,CAACoD,WAAW,CAAC,CAAC,CAACjC,QAAQ,CAACR,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAC,IAChE/B,MAAM,CAAC0B,SAAS,IAAI1B,MAAM,CAAC0B,SAAS,CAACK,WAAW,CAAC,CAAC,CAACjC,QAAQ,CAACR,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAE,IACtF/B,MAAM,CAACG,aAAa,IAAIH,MAAM,CAACG,aAAa,CAAC4B,WAAW,CAAC,CAAC,CAACjC,QAAQ,CAACR,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAE;MACpH,OAAOS,YAAY,IAAIkC,aAAa,IAAI1E,MAAM,CAACyB,aAAa,GAAG,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMkD,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAACzG,IAAI,EAAE,OAAO,EAAE;IAEpB,OAAOgB,MAAM,CAACqC,MAAM,CAACvB,MAAM,IAAI;MAC7B,MAAM4E,cAAc,GAAG5E,MAAM,CAAC0B,SAAS,KAAKxD,IAAI,CAACwD,SAAS,IACpC1B,MAAM,CAAC2B,OAAO,KAAKzD,IAAI,CAACyD,OAAO;MACrD,MAAM+C,aAAa,GAAGpF,UAAU,KAAK,EAAE,IAClBU,MAAM,CAACrB,SAAS,CAACoD,WAAW,CAAC,CAAC,CAACjC,QAAQ,CAACR,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAC,IAChE/B,MAAM,CAAC0B,SAAS,IAAI1B,MAAM,CAAC0B,SAAS,CAACK,WAAW,CAAC,CAAC,CAACjC,QAAQ,CAACR,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAE,IACtF/B,MAAM,CAACG,aAAa,IAAIH,MAAM,CAACG,aAAa,CAAC4B,WAAW,CAAC,CAAC,CAACjC,QAAQ,CAACR,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAE;MACpH,OAAO6C,cAAc,IAAIF,aAAa,IAAI1E,MAAM,CAACyB,aAAa,GAAG,CAAC;IACpE,CAAC,CAAC;EACJ,CAAC;EAED,MAAMQ,iBAAiB,GAAGwC,oBAAoB,CAAC,CAAC;EAChD,MAAMI,mBAAmB,GAAGF,sBAAsB,CAAC,CAAC;EAEpD,oBACE/G,OAAA,CAAAE,SAAA;IAAAgH,QAAA,eACElH,OAAA,CAAClC,MAAM;MACLsC,IAAI,EAAEA,IAAK;MACXC,OAAO,EAAE6F,WAAY;MACrBiB,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,oBAAoB,EAAEjG,MAAM,IAAIV,OAAQ;MACxC4G,UAAU,EAAE;QACVC,EAAE,EAAE;UACFC,SAAS,EAAE,OAAO;UAClBC,SAAS,EAAE;QACb;MACF,CAAE;MAAAP,QAAA,gBAEFlH,OAAA,CAACjC,WAAW;QAACwJ,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,eACzBlH,OAAA,CAAC5B,UAAU;UAACuJ,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,KAAK;UAAAV,QAAA,GAAC,2BACd,EAAC5G,IAAI,CAACwC,OAAO;QAAA;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEdhI,OAAA,CAAChC,aAAa;QAACiK,QAAQ;QAACV,EAAE,EAAE;UAAEW,CAAC,EAAE;QAAE,CAAE;QAAAhB,QAAA,gBAEnClH,OAAA,CAAC1B,IAAI;UAAC6J,SAAS;UAACC,OAAO,EAAE,CAAE;UAACb,EAAE,EAAE;YAAEc,EAAE,EAAE;UAAE,CAAE;UAAAnB,QAAA,gBAExClH,OAAA,CAAC1B,IAAI;YAACgK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlH,OAAA,CAACzB,KAAK;cAACgJ,EAAE,EAAE;gBAAEW,CAAC,EAAE,GAAG;gBAAEO,MAAM,EAAE,MAAM;gBAAEC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAzB,QAAA,gBACzElH,OAAA,CAAC5B,UAAU;gBAACuJ,OAAO,EAAC,WAAW;gBAACJ,EAAE,EAAE;kBAAEqB,UAAU,EAAE,MAAM;kBAAEP,EAAE,EAAE,CAAC;kBAAEQ,KAAK,EAAE;gBAAU,CAAE;gBAAA3B,QAAA,EAAC;cAErF;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbhI,OAAA,CAAC1B,IAAI;gBAAC6J,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAlB,QAAA,gBACzBlH,OAAA,CAAC1B,IAAI;kBAACgK,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAArB,QAAA,gBACflH,OAAA,CAAC5B,UAAU;oBAACuJ,OAAO,EAAC,OAAO;oBAACJ,EAAE,EAAE;sBAAEc,EAAE,EAAE;oBAAE,CAAE;oBAAAnB,QAAA,gBACxClH,OAAA;sBAAAkH,QAAA,EAAQ;oBAAU;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1H,IAAI,CAACwD,SAAS,IAAI,KAAK;kBAAA;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACbhI,OAAA,CAAC5B,UAAU;oBAACuJ,OAAO,EAAC,OAAO;oBAACJ,EAAE,EAAE;sBAAEc,EAAE,EAAE;oBAAE,CAAE;oBAAAnB,QAAA,gBACxClH,OAAA;sBAAAkH,QAAA,EAAQ;oBAAQ;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1H,IAAI,CAACyD,OAAO,IAAI,KAAK;kBAAA;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACbhI,OAAA,CAAC5B,UAAU;oBAACuJ,OAAO,EAAC,OAAO;oBAAAT,QAAA,gBACzBlH,OAAA;sBAAAkH,QAAA,EAAQ;oBAAc;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1H,IAAI,CAACmF,aAAa,IAAI,KAAK,EAAC,IAC/D;kBAAA;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACPhI,OAAA,CAAC1B,IAAI;kBAACgK,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAArB,QAAA,gBACflH,OAAA,CAAC5B,UAAU;oBAACuJ,OAAO,EAAC,OAAO;oBAACJ,EAAE,EAAE;sBAAEc,EAAE,EAAE;oBAAE,CAAE;oBAAAnB,QAAA,gBACxClH,OAAA;sBAAAkH,QAAA,EAAQ;oBAAG;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1H,IAAI,CAACwI,mBAAmB,IAAI,KAAK;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACbhI,OAAA,CAAC5B,UAAU;oBAACuJ,OAAO,EAAC,OAAO;oBAACJ,EAAE,EAAE;sBAAEc,EAAE,EAAE;oBAAE,CAAE;oBAAAnB,QAAA,gBACxClH,OAAA;sBAAAkH,QAAA,EAAQ;oBAAE;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1H,IAAI,CAACyI,iBAAiB,IAAI,KAAK;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACbhI,OAAA,CAAC5B,UAAU;oBAACuJ,OAAO,EAAC,OAAO;oBAAAT,QAAA,gBACzBlH,OAAA;sBAAAkH,QAAA,EAAQ;oBAAW;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC1H,IAAI,CAAC4E,eAAe,IAAI,CAAC,EAAC,IAC1D;kBAAA;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGPhI,OAAA,CAAC1B,IAAI;YAACgK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlH,OAAA,CAACzB,KAAK;cAACgJ,EAAE,EAAE;gBAAEW,CAAC,EAAE,GAAG;gBAAEO,MAAM,EAAE,MAAM;gBAAEE,YAAY,EAAE,CAAC;gBAAEK,MAAM,EAAE;cAAoB,CAAE;cAAA9B,QAAA,gBAClFlH,OAAA,CAAC5B,UAAU;gBAACuJ,OAAO,EAAC,WAAW;gBAACJ,EAAE,EAAE;kBAAEqB,UAAU,EAAE,MAAM;kBAAEP,EAAE,EAAE,CAAC;kBAAEQ,KAAK,EAAE;gBAAU,CAAE;gBAAA3B,QAAA,EAAC;cAErF;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbhI,OAAA,CAAC7B,SAAS;gBACR8K,SAAS;gBACT7B,SAAS;gBACT8B,KAAK,EAAC,cAAc;gBACpB7D,IAAI,EAAC,cAAc;gBACnB8D,IAAI,EAAC,QAAQ;gBACb9F,KAAK,EAAEzC,QAAQ,CAACE,YAAa;gBAC7BsI,QAAQ,EAAEhE,gBAAiB;gBAC3BiE,UAAU,EAAE1C,cAAe;gBAC3B5D,KAAK,EAAEuG,OAAO,CAACtI,UAAU,CAACF,YAAY,CAAE;gBACxCyI,UAAU,EAAEvI,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;gBACjE0I,mBAAmB,EAAE;kBACnBjC,EAAE,EAAE;oBAAEsB,KAAK,EAAE3H,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;kBAAa;gBACrG,CAAE;gBACF2I,QAAQ,EAAErI,MAAM,IAAIV,OAAQ;gBAC5BgJ,IAAI,EAAC,QAAQ;gBACbC,UAAU,EAAE;kBACVC,GAAG,EAAE,MAAM;kBACXC,IAAI,EAAE,GAAG;kBACTC,KAAK,EAAE;oBAAEC,QAAQ,EAAE,QAAQ;oBAAEnB,UAAU,EAAE,MAAM;oBAAEoB,SAAS,EAAE;kBAAS;gBACvE,CAAE;gBACFC,UAAU,EAAE;kBACVC,YAAY,eAAElK,OAAA,CAAC5B,UAAU;oBAACuJ,OAAO,EAAC,IAAI;oBAACkB,KAAK,EAAC,SAAS;oBAACtB,EAAE,EAAE;sBAAEqB,UAAU,EAAE;oBAAO,CAAE;oBAAA1B,QAAA,EAAC;kBAAC;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAClG,CAAE;gBACFT,EAAE,EAAE;kBACF,0BAA0B,EAAE;oBAC1B,YAAY,EAAE;sBACZ4C,WAAW,EAAE,SAAS;sBACtBC,WAAW,EAAE;oBACf,CAAC;oBACD,kBAAkB,EAAE;sBAClBD,WAAW,EAAE;oBACf,CAAC;oBACD,wBAAwB,EAAE;sBACxBA,WAAW,EAAE;oBACf;kBACF;gBACF;cAAE;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPhI,OAAA,CAAC3B,GAAG;UAACkJ,EAAE,EAAE;YAAEc,EAAE,EAAE;UAAE,CAAE;UAAAnB,QAAA,gBACjBlH,OAAA,CAAC5B,UAAU;YAACuJ,OAAO,EAAC,WAAW;YAACJ,EAAE,EAAE;cAAEqB,UAAU,EAAE,MAAM;cAAEP,EAAE,EAAE,CAAC;cAAEQ,KAAK,EAAE;YAAU,CAAE;YAAA3B,QAAA,EAAC;UAErF;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAGbhI,OAAA,CAACzB,KAAK;YAACgJ,EAAE,EAAE;cAAEW,CAAC,EAAE,CAAC;cAAEG,EAAE,EAAE,CAAC;cAAEK,OAAO,EAAE,SAAS;cAAEC,YAAY,EAAE;YAAE,CAAE;YAAAzB,QAAA,eAC9DlH,OAAA,CAAC1B,IAAI;cAAC6J,SAAS;cAACC,OAAO,EAAE,CAAE;cAACiC,UAAU,EAAC,QAAQ;cAAAnD,QAAA,gBAE7ClH,OAAA,CAAC1B,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAC+B,EAAE,EAAE,CAAE;gBAAApD,QAAA,eACvBlH,OAAA,CAAC7B,SAAS;kBACRuL,IAAI,EAAC,OAAO;kBACZR,KAAK,EAAC,cAAc;kBACpBvB,OAAO,EAAC,UAAU;kBAClBtE,KAAK,EAAE3B,UAAW;kBAClB0H,QAAQ,EAAElG,sBAAuB;kBACjCqH,WAAW,EAAC,8BAA8B;kBAC1CnD,SAAS;kBACT6C,UAAU,EAAE;oBACVO,cAAc,eACZxK,OAAA,CAACrB,cAAc;sBAAC8L,QAAQ,EAAC,OAAO;sBAAAvD,QAAA,eAC9BlH,OAAA,CAACd,UAAU;wBAAC6K,QAAQ,EAAC;sBAAO;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CACjB;oBACDkC,YAAY,EAAExI,UAAU,gBACtB1B,OAAA,CAACrB,cAAc;sBAAC8L,QAAQ,EAAC,KAAK;sBAAAvD,QAAA,eAC5BlH,OAAA,CAACpB,UAAU;wBACT8K,IAAI,EAAC,OAAO;wBACZ,cAAW,cAAc;wBACzBgB,OAAO,EAAEA,CAAA,KAAM/I,aAAa,CAAC,EAAE,CAAE;wBACjCgJ,IAAI,EAAC,KAAK;wBAAAzD,QAAA,eAEVlH,OAAA,CAACZ,UAAU;0BAAC2K,QAAQ,EAAC;wBAAO;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,GACf;kBACN;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGPhI,OAAA,CAAC1B,IAAI;gBAACgK,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAC+B,EAAE,EAAE,CAAE;gBAAApD,QAAA,eACvBlH,OAAA,CAAC9B,MAAM;kBACLyJ,OAAO,EAAE/G,QAAQ,CAACG,SAAS,KAAK,cAAc,GAAG,WAAW,GAAG,UAAW;kBAC1E2I,IAAI,EAAC,QAAQ;kBACbgB,OAAO,EAAEzH,uBAAwB;kBACjCmE,SAAS;kBACTyB,KAAK,EAAEjI,QAAQ,CAACG,SAAS,KAAK,cAAc,GAAG,SAAS,GAAG,SAAU;kBACrEwG,EAAE,EAAE;oBACFkB,MAAM,EAAE,MAAM;oBACdG,UAAU,EAAE,MAAM;oBAClBgC,aAAa,EAAE;kBACjB,CAAE;kBAAA1D,QAAA,EACH;gBAED;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EAEPxG,aAAa,gBACZxB,OAAA,CAAC3B,GAAG;YAACkJ,EAAE,EAAE;cAAEsD,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA7D,QAAA,eAC5DlH,OAAA,CAACvB,gBAAgB;cAACiL,IAAI,EAAE;YAAG;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,gBAENhI,OAAA,CAAC1B,IAAI;YAAC6J,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAlB,QAAA,gBAEzBlH,OAAA,CAAC1B,IAAI;cAACgK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtB,QAAA,gBACvBlH,OAAA,CAAC5B,UAAU;gBAACuJ,OAAO,EAAC,WAAW;gBAACqD,YAAY;gBAACzD,EAAE,EAAE;kBAAEsB,KAAK,EAAE,cAAc;kBAAED,UAAU,EAAE;gBAAS,CAAE;gBAAA1B,QAAA,gBAC/FlH,OAAA,CAACV,eAAe;kBAACiI,EAAE,EAAE;oBAAE0D,EAAE,EAAE,CAAC;oBAAEC,aAAa,EAAE;kBAAS;gBAAE;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wBACvC,EAAC3D,iBAAiB,CAACZ,MAAM,EAAC,GAChD;cAAA;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEbhI,OAAA,CAAC3B,GAAG;gBAACkJ,EAAE,EAAE;kBAAEE,SAAS,EAAE,GAAG;kBAAE0D,QAAQ,EAAE,MAAM;kBAAEnC,MAAM,EAAE,mBAAmB;kBAAEL,YAAY,EAAE;gBAAE,CAAE;gBAAAzB,QAAA,EACzF7C,iBAAiB,CAACZ,MAAM,KAAK,CAAC,gBAC7BzD,OAAA,CAAC3B,GAAG;kBAACkJ,EAAE,EAAE;oBAAEW,CAAC,EAAE,CAAC;oBAAE8B,SAAS,EAAE,QAAQ;oBAAEnB,KAAK,EAAE;kBAAiB,CAAE;kBAAA3B,QAAA,EAAC;gBAEjE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,gBAENhI,OAAA,CAACnB,IAAI;kBAACuM,KAAK;kBAAAlE,QAAA,EACR7C,iBAAiB,CAACgH,GAAG,CAAEjJ,MAAM,iBAC5BpC,OAAA,CAAClB,QAAQ;oBAEPwM,cAAc;oBAAApE,QAAA,eAEdlH,OAAA,CAACjB,cAAc;sBACbwM,QAAQ,EAAE3K,QAAQ,CAACG,SAAS,KAAKqB,MAAM,CAACrB,SAAU;sBAClD2J,OAAO,EAAEA,CAAA,KAAMlI,6BAA6B,CAACJ,MAAM,CAAE;sBACrDmF,EAAE,EAAE;wBACF,gBAAgB,EAAE;0BAChBiE,eAAe,EAAE,wBAAwB;0BACzC,SAAS,EAAE;4BACTA,eAAe,EAAE;0BACnB;wBACF;sBACF,CAAE;sBAAAtE,QAAA,eAEFlH,OAAA,CAAChB,YAAY;wBACXyM,OAAO,eACLzL,OAAA,CAAC3B,GAAG;0BAACkJ,EAAE,EAAE;4BAAEsD,OAAO,EAAE,MAAM;4BAAEC,cAAc,EAAE,eAAe;4BAAET,UAAU,EAAE;0BAAS,CAAE;0BAAAnD,QAAA,gBAClFlH,OAAA,CAAC5B,UAAU;4BAACuJ,OAAO,EAAC,OAAO;4BAACiB,UAAU,EAAC,QAAQ;4BAAA1B,QAAA,EAC5C9E,MAAM,CAACG,aAAa,IAAIH,MAAM,CAACrB;0BAAS;4BAAA8G,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/B,CAAC,eACbhI,OAAA,CAACtB,IAAI;4BACHgL,IAAI,EAAC,OAAO;4BACZR,KAAK,EAAE,GAAG9G,MAAM,CAACyB,aAAa,GAAI;4BAClCgF,KAAK,EAAC,SAAS;4BACflB,OAAO,EAAC;0BAAU;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CACN;wBACD0D,SAAS,eACP1L,OAAA,CAAC5B,UAAU;0BAACuJ,OAAO,EAAC,SAAS;0BAACkB,KAAK,EAAC,gBAAgB;0BAAA3B,QAAA,GACjD9E,MAAM,CAAC0B,SAAS,EAAC,KAAG,EAAC1B,MAAM,CAAC2B,OAAO;wBAAA;0BAAA8D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1B;sBACb;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACY;kBAAC,GAnCZ5F,MAAM,CAACrB,SAAS;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAoCb,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACP;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGPhI,OAAA,CAAC1B,IAAI;cAACgK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtB,QAAA,gBACvBlH,OAAA,CAAC5B,UAAU;gBAACuJ,OAAO,EAAC,WAAW;gBAACqD,YAAY;gBAACzD,EAAE,EAAE;kBAAEsB,KAAK,EAAE,cAAc;kBAAED,UAAU,EAAE;gBAAS,CAAE;gBAAA1B,QAAA,gBAC/FlH,OAAA,CAACR,WAAW;kBAAC+H,EAAE,EAAE;oBAAE0D,EAAE,EAAE,CAAC;oBAAEC,aAAa,EAAE;kBAAS;gBAAE;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,0BACjC,EAACf,mBAAmB,CAACxD,MAAM,EAAC,GACpD;cAAA;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEbhI,OAAA,CAAC3B,GAAG;gBAACkJ,EAAE,EAAE;kBAAEE,SAAS,EAAE,GAAG;kBAAE0D,QAAQ,EAAE,MAAM;kBAAEnC,MAAM,EAAE,mBAAmB;kBAAEL,YAAY,EAAE;gBAAE,CAAE;gBAAAzB,QAAA,EACzFD,mBAAmB,CAACxD,MAAM,KAAK,CAAC,gBAC/BzD,OAAA,CAAC3B,GAAG;kBAACkJ,EAAE,EAAE;oBAAEW,CAAC,EAAE,CAAC;oBAAE8B,SAAS,EAAE,QAAQ;oBAAEnB,KAAK,EAAE;kBAAiB,CAAE;kBAAA3B,QAAA,EAAC;gBAEjE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,gBAENhI,OAAA,CAACnB,IAAI;kBAACuM,KAAK;kBAAAlE,QAAA,EACRD,mBAAmB,CAACoE,GAAG,CAAEjJ,MAAM,iBAC9BpC,OAAA,CAAClB,QAAQ;oBAEPwM,cAAc;oBAAApE,QAAA,eAEdlH,OAAA,CAACjB,cAAc;sBACb2L,OAAO,EAAEA,CAAA,KAAM9H,+BAA+B,CAACR,MAAM,CAAE;sBACvDmF,EAAE,EAAE;wBACF,SAAS,EAAE;0BACTiE,eAAe,EAAE;wBACnB;sBACF,CAAE;sBAAAtE,QAAA,eAEFlH,OAAA,CAAChB,YAAY;wBACXyM,OAAO,eACLzL,OAAA,CAAC3B,GAAG;0BAACkJ,EAAE,EAAE;4BAAEsD,OAAO,EAAE,MAAM;4BAAEC,cAAc,EAAE,eAAe;4BAAET,UAAU,EAAE;0BAAS,CAAE;0BAAAnD,QAAA,gBAClFlH,OAAA,CAAC5B,UAAU;4BAACuJ,OAAO,EAAC,OAAO;4BAACiB,UAAU,EAAC,QAAQ;4BAAA1B,QAAA,EAC5C9E,MAAM,CAACG,aAAa,IAAIH,MAAM,CAACrB;0BAAS;4BAAA8G,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/B,CAAC,eACbhI,OAAA,CAACtB,IAAI;4BACHgL,IAAI,EAAC,OAAO;4BACZR,KAAK,EAAE,GAAG9G,MAAM,CAACyB,aAAa,GAAI;4BAClCgF,KAAK,EAAC,SAAS;4BACflB,OAAO,EAAC;0BAAU;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CACN;wBACD0D,SAAS,eACP1L,OAAA,CAAC5B,UAAU;0BAACuJ,OAAO,EAAC,SAAS;0BAACkB,KAAK,EAAC,gBAAgB;0BAAA3B,QAAA,GACjD9E,MAAM,CAAC0B,SAAS,EAAC,KAAG,EAAC1B,MAAM,CAAC2B,OAAO;wBAAA;0BAAA8D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1B;sBACb;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACY;kBAAC,GA/BZ5F,MAAM,CAACrB,SAAS;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAgCb,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACP;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACP,EAEA1G,MAAM,CAACmC,MAAM,KAAK,CAAC,IAAI,CAACjC,aAAa,iBACpCxB,OAAA,CAACxB,KAAK;YAACmN,QAAQ,EAAC,SAAS;YAACpE,EAAE,EAAE;cAAEqE,EAAE,EAAE;YAAE,CAAE;YAAA1E,QAAA,EAAC;UAEzC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR,EAEAhH,UAAU,CAACD,SAAS,iBACnBf,OAAA,CAAC5B,UAAU;YAACuJ,OAAO,EAAC,SAAS;YAACkB,KAAK,EAAC,OAAO;YAACtB,EAAE,EAAE;cAAEqE,EAAE,EAAE,CAAC;cAAEf,OAAO,EAAE;YAAQ,CAAE;YAAA3D,QAAA,EACzElG,UAAU,CAACD;UAAS;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEhBhI,OAAA,CAAC/B,aAAa;QAAAiJ,QAAA,gBACZlH,OAAA,CAAC9B,MAAM;UACLwM,OAAO,EAAExE,WAAY;UACrBuD,QAAQ,EAAErI,MAAM,IAAIV,OAAQ;UAAAwG,QAAA,EAC7B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThI,OAAA,CAAC9B,MAAM;UACLwM,OAAO,EAAE5E,UAAW;UACpB6B,OAAO,EAAC,WAAW;UACnB8B,QAAQ,EAAErI,MAAM,IAAIV,OAAO,IAAI,CAACE,QAAQ,CAACE,YAAY,IAAI,CAACF,QAAQ,CAACG,SAAU;UAC7E8K,SAAS,EAAEzK,MAAM,gBAAGpB,OAAA,CAACvB,gBAAgB;YAACiL,IAAI,EAAE;UAAG;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG,IAAK;UAAAd,QAAA,EAEzD9F,MAAM,GAAG,aAAa,GAAG;QAAO;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC,gBACT,CAAC;AAEP,CAAC;AAACrH,EAAA,CAhtBIR,4BAA4B;AAAA2L,EAAA,GAA5B3L,4BAA4B;AAktBlC,eAAeA,4BAA4B;AAAC,IAAA2L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}