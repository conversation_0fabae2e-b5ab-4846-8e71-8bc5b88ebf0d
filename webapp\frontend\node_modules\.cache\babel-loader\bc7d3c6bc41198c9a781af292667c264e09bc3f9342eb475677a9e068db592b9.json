{"ast": null, "code": "var accusativeWeekdays = ['ned<PERSON><PERSON>', 'pondě<PERSON><PERSON>', 'úter<PERSON>', 'středu', 'čtvrtek', 'pátek', 'sobotu'];\nvar formatRelativeLocale = {\n  lastWeek: \"'posledn<PERSON>' eeee 've' p\",\n  yesterday: \"'včera v' p\",\n  today: \"'dnes v' p\",\n  tomorrow: \"'zítra v' p\",\n  nextWeek: function nextWeek(date) {\n    var day = date.getUTCDay();\n    return \"'v \" + accusativeWeekdays[day] + \" o' p\";\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["accusativeWeekdays", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "date", "day", "getUTCDay", "other", "formatRelative", "token", "format"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/cs/_lib/formatRelative/index.js"], "sourcesContent": ["var accusativeWeekdays = ['ned<PERSON><PERSON>', 'pondě<PERSON><PERSON>', 'úter<PERSON>', 'středu', 'čtvrtek', 'pátek', 'sobotu'];\nvar formatRelativeLocale = {\n  lastWeek: \"'posledn<PERSON>' eeee 've' p\",\n  yesterday: \"'včera v' p\",\n  today: \"'dnes v' p\",\n  tomorrow: \"'zítra v' p\",\n  nextWeek: function nextWeek(date) {\n    var day = date.getUTCDay();\n    return \"'v \" + accusativeWeekdays[day] + \" o' p\";\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,kBAAkB,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC;AAC/F,IAAIC,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,wBAAwB;EAClCC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAE;IAChC,IAAIC,GAAG,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;IAC1B,OAAO,KAAK,GAAGT,kBAAkB,CAACQ,GAAG,CAAC,GAAG,OAAO;EAClD,CAAC;EACDE,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEL,IAAI,EAAE;EACxD,IAAIM,MAAM,GAAGZ,oBAAoB,CAACW,KAAK,CAAC;EACxC,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACN,IAAI,CAAC;EACrB;EACA,OAAOM,MAAM;AACf,CAAC;AACD,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}