{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"nas lugha na diog\",\n    other: \"nas lugha na {{count}} diogan\"\n  },\n  xSeconds: {\n    one: \"1 diog\",\n    two: \"2 dhiog\",\n    twenty: \"20 diog\",\n    other: \"{{count}} diogan\"\n  },\n  halfAMinute: \"leth mhionaid\",\n  lessThanXMinutes: {\n    one: \"nas lugha na mionaid\",\n    other: \"nas lugha na {{count}} mionaidean\"\n  },\n  xMinutes: {\n    one: \"1 mionaid\",\n    two: \"2 mhionaid\",\n    twenty: \"20 mionaid\",\n    other: \"{{count}} mionaidean\"\n  },\n  aboutXHours: {\n    one: \"mu uair de thìde\",\n    other: \"mu {{count}} uairean de thìde\"\n  },\n  xHours: {\n    one: \"1 uair de thìde\",\n    two: \"2 uair de thìde\",\n    twenty: \"20 uair de thìde\",\n    other: \"{{count}} uairean de thìde\"\n  },\n  xDays: {\n    one: \"1 là\",\n    other: \"{{count}} là\"\n  },\n  aboutXWeeks: {\n    one: \"mu 1 seachdain\",\n    other: \"mu {{count}} seachdainean\"\n  },\n  xWeeks: {\n    one: \"1 seachdain\",\n    other: \"{{count}} seachdainean\"\n  },\n  aboutXMonths: {\n    one: \"mu mhìos\",\n    other: \"mu {{count}} mìosan\"\n  },\n  xMonths: {\n    one: \"1 mìos\",\n    other: \"{{count}} mìosan\"\n  },\n  aboutXYears: {\n    one: \"mu bhliadhna\",\n    other: \"mu {{count}} bliadhnaichean\"\n  },\n  xYears: {\n    one: \"1 bhliadhna\",\n    other: \"{{count}} bliadhna\"\n  },\n  overXYears: {\n    one: \"còrr is bliadhna\",\n    other: \"còrr is {{count}} bliadhnaichean\"\n  },\n  almostXYears: {\n    one: \"cha mhòr bliadhna\",\n    other: \"cha mhòr {{count}} bliadhnaichean\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2 && !!tokenValue.two) {\n    result = tokenValue.two;\n  } else if (count === 20 && !!tokenValue.twenty) {\n    result = tokenValue.twenty;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"ann an \" + result;\n    } else {\n      return \"o chionn \" + result;\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "two", "twenty", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/gd/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"nas lugha na diog\",\n    other: \"nas lugha na {{count}} diogan\",\n  },\n\n  xSeconds: {\n    one: \"1 diog\",\n    two: \"2 dhiog\",\n    twenty: \"20 diog\",\n    other: \"{{count}} diogan\",\n  },\n\n  halfAMinute: \"leth mhionaid\",\n\n  lessThanXMinutes: {\n    one: \"nas lugha na mionaid\",\n    other: \"nas lugha na {{count}} mionaidean\",\n  },\n\n  xMinutes: {\n    one: \"1 mionaid\",\n    two: \"2 mhionaid\",\n    twenty: \"20 mionaid\",\n    other: \"{{count}} mionaidean\",\n  },\n\n  aboutXHours: {\n    one: \"mu uair de thìde\",\n    other: \"mu {{count}} uairean de thìde\",\n  },\n\n  xHours: {\n    one: \"1 uair de thìde\",\n    two: \"2 uair de thìde\",\n    twenty: \"20 uair de thìde\",\n    other: \"{{count}} uairean de thìde\",\n  },\n\n  xDays: {\n    one: \"1 là\",\n    other: \"{{count}} là\",\n  },\n\n  aboutXWeeks: {\n    one: \"mu 1 seachdain\",\n    other: \"mu {{count}} seachdainean\",\n  },\n\n  xWeeks: {\n    one: \"1 seachdain\",\n    other: \"{{count}} seachdainean\",\n  },\n\n  aboutXMonths: {\n    one: \"mu mhìos\",\n    other: \"mu {{count}} mìosan\",\n  },\n\n  xMonths: {\n    one: \"1 mìos\",\n    other: \"{{count}} mìosan\",\n  },\n\n  aboutXYears: {\n    one: \"mu bhliadhna\",\n    other: \"mu {{count}} bliadhnaichean\",\n  },\n\n  xYears: {\n    one: \"1 bhliadhna\",\n    other: \"{{count}} bliadhna\",\n  },\n\n  overXYears: {\n    one: \"còrr is bliadhna\",\n    other: \"còrr is {{count}} bliadhnaichean\",\n  },\n\n  almostXYears: {\n    one: \"cha mhòr bliadhna\",\n    other: \"cha mhòr {{count}} bliadhnaichean\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2 && !!tokenValue.two) {\n    result = tokenValue.two;\n  } else if (count === 20 && !!tokenValue.twenty) {\n    result = tokenValue.twenty;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"ann an \" + result;\n    } else {\n      return \"o chionn \" + result;\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,QAAQ;IACbG,GAAG,EAAE,SAAS;IACdC,MAAM,EAAE,SAAS;IACjBH,KAAK,EAAE;EACT,CAAC;EAEDI,WAAW,EAAE,eAAe;EAE5BC,gBAAgB,EAAE;IAChBN,GAAG,EAAE,sBAAsB;IAC3BC,KAAK,EAAE;EACT,CAAC;EAEDM,QAAQ,EAAE;IACRP,GAAG,EAAE,WAAW;IAChBG,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,YAAY;IACpBH,KAAK,EAAE;EACT,CAAC;EAEDO,WAAW,EAAE;IACXR,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDQ,MAAM,EAAE;IACNT,GAAG,EAAE,iBAAiB;IACtBG,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,kBAAkB;IAC1BH,KAAK,EAAE;EACT,CAAC;EAEDS,KAAK,EAAE;IACLV,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDU,WAAW,EAAE;IACXX,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDW,MAAM,EAAE;IACNZ,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDY,YAAY,EAAE;IACZb,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDa,OAAO,EAAE;IACPd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDc,WAAW,EAAE;IACXf,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDe,MAAM,EAAE;IACNhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDgB,UAAU,EAAE;IACVjB,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDiB,YAAY,EAAE;IACZlB,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMkB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAG1B,oBAAoB,CAACsB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACxB,GAAG;EACzB,CAAC,MAAM,IAAIqB,KAAK,KAAK,CAAC,IAAI,CAAC,CAACG,UAAU,CAACrB,GAAG,EAAE;IAC1CoB,MAAM,GAAGC,UAAU,CAACrB,GAAG;EACzB,CAAC,MAAM,IAAIkB,KAAK,KAAK,EAAE,IAAI,CAAC,CAACG,UAAU,CAACpB,MAAM,EAAE;IAC9CmB,MAAM,GAAGC,UAAU,CAACpB,MAAM;EAC5B,CAAC,MAAM;IACLmB,MAAM,GAAGC,UAAU,CAACvB,KAAK,CAACwB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,SAAS,GAAGL,MAAM;IAC3B,CAAC,MAAM;MACL,OAAO,WAAW,GAAGA,MAAM;IAC7B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}