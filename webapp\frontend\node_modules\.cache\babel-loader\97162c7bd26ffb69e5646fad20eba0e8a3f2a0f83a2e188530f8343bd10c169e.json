{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nexport const PickerContext = /*#__PURE__*/React.createContext(null);\n\n/**\n * Returns the context passed by the Picker wrapping the current component.\n */\nexport const usePickerContext = () => {\n  const value = React.useContext(PickerContext);\n  if (value == null) {\n    throw new Error('MUI X: The `usePickerContext` hook can only be called inside the context of a Picker component');\n  }\n  return value;\n};", "map": {"version": 3, "names": ["React", "<PERSON>er<PERSON>ontext", "createContext", "usePickerContext", "value", "useContext", "Error"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/hooks/usePickerContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nexport const PickerContext = /*#__PURE__*/React.createContext(null);\n\n/**\n * Returns the context passed by the Picker wrapping the current component.\n */\nexport const usePickerContext = () => {\n  const value = React.useContext(PickerContext);\n  if (value == null) {\n    throw new Error('MUI X: The `usePickerContext` hook can only be called inside the context of a Picker component');\n  }\n  return value;\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,aAAa,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;;AAEnE;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EACpC,MAAMC,KAAK,GAAGJ,KAAK,CAACK,UAAU,CAACJ,aAAa,CAAC;EAC7C,IAAIG,KAAK,IAAI,IAAI,EAAE;IACjB,MAAM,IAAIE,KAAK,CAAC,gGAAgG,CAAC;EACnH;EACA,OAAOF,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}