{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst numberValues = {\n  locale: {\n    1: \"१\",\n    2: \"२\",\n    3: \"३\",\n    4: \"४\",\n    5: \"५\",\n    6: \"६\",\n    7: \"७\",\n    8: \"८\",\n    9: \"९\",\n    0: \"०\"\n  },\n  number: {\n    \"१\": \"1\",\n    \"२\": \"2\",\n    \"३\": \"3\",\n    \"४\": \"4\",\n    \"५\": \"5\",\n    \"६\": \"6\",\n    \"७\": \"7\",\n    \"८\": \"8\",\n    \"९\": \"9\",\n    \"०\": \"0\"\n  }\n};\n\n// CLDR #1585 - #1592\nconst eraValues = {\n  narrow: [\"ईसा-पूर्व\", \"ईस्वी\"],\n  abbreviated: [\"ईसा-पूर्व\", \"ईस्वी\"],\n  wide: [\"ईसा-पूर्व\", \"ईसवी सन\"]\n};\n\n// CLDR #1593 - #1616\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"ति1\", \"ति2\", \"ति3\", \"ति4\"],\n  wide: [\"पहली तिमाही\", \"दूसरी तिमाही\", \"तीसरी तिमाही\", \"चौथी तिमाही\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n// https://www.unicode.org/cldr/charts/32/summary/hi.html\n// CLDR #1617 - #1688\nconst monthValues = {\n  narrow: [\"ज\", \"फ़\", \"मा\", \"अ\", \"मई\", \"जू\", \"जु\", \"अग\", \"सि\", \"अक्टू\", \"न\", \"दि\"],\n  abbreviated: [\"जन\", \"फ़र\", \"मार्च\", \"अप्रैल\", \"मई\", \"जून\", \"जुल\", \"अग\", \"सित\", \"अक्टू\", \"नव\", \"दिस\"],\n  wide: [\"जनवरी\", \"फ़रवरी\", \"मार्च\", \"अप्रैल\", \"मई\", \"जून\", \"जुलाई\", \"अगस्त\", \"सितंबर\", \"अक्टूबर\", \"नवंबर\", \"दिसंबर\"]\n};\n\n// CLDR #1689 - #1744\nconst dayValues = {\n  narrow: [\"र\", \"सो\", \"मं\", \"बु\", \"गु\", \"शु\", \"श\"],\n  short: [\"र\", \"सो\", \"मं\", \"बु\", \"गु\", \"शु\", \"श\"],\n  abbreviated: [\"रवि\", \"सोम\", \"मंगल\", \"बुध\", \"गुरु\", \"शुक्र\", \"शनि\"],\n  wide: [\"रविवार\", \"सोमवार\", \"मंगलवार\", \"बुधवार\", \"गुरुवार\", \"शुक्रवार\", \"शनिवार\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\"\n  },\n  abbreviated: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\"\n  },\n  wide: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\"\n  },\n  abbreviated: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\"\n  },\n  wide: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return numberToLocale(number);\n};\nexport function localeToNumber(locale) {\n  const enNumber = locale.toString().replace(/[१२३४५६७८९०]/g, function (match) {\n    return numberValues.number[match];\n  });\n  return Number(enNumber);\n}\nexport function numberToLocale(enNumber) {\n  return enNumber.toString().replace(/\\d/g, function (match) {\n    return numberValues.locale[match];\n  });\n}\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "numberValues", "locale", "number", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "Number", "numberToLocale", "localeToNumber", "enNumber", "toString", "replace", "match", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/hi/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst numberValues = {\n  locale: {\n    1: \"१\",\n    2: \"२\",\n    3: \"३\",\n    4: \"४\",\n    5: \"५\",\n    6: \"६\",\n    7: \"७\",\n    8: \"८\",\n    9: \"९\",\n    0: \"०\",\n  },\n  number: {\n    \"१\": \"1\",\n    \"२\": \"2\",\n    \"३\": \"3\",\n    \"४\": \"4\",\n    \"५\": \"5\",\n    \"६\": \"6\",\n    \"७\": \"7\",\n    \"८\": \"8\",\n    \"९\": \"9\",\n    \"०\": \"0\",\n  },\n};\n\n// CLDR #1585 - #1592\nconst eraValues = {\n  narrow: [\"ईसा-पूर्व\", \"ईस्वी\"],\n  abbreviated: [\"ईसा-पूर्व\", \"ईस्वी\"],\n  wide: [\"ईसा-पूर्व\", \"ईसवी सन\"],\n};\n\n// CLDR #1593 - #1616\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"ति1\", \"ति2\", \"ति3\", \"ति4\"],\n  wide: [\"पहली तिमाही\", \"दूसरी तिमाही\", \"तीसरी तिमाही\", \"चौथी तिमाही\"],\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\n// https://www.unicode.org/cldr/charts/32/summary/hi.html\n// CLDR #1617 - #1688\nconst monthValues = {\n  narrow: [\n    \"ज\",\n    \"फ़\",\n    \"मा\",\n    \"अ\",\n    \"मई\",\n    \"जू\",\n    \"जु\",\n    \"अग\",\n    \"सि\",\n    \"अक्टू\",\n    \"न\",\n    \"दि\",\n  ],\n\n  abbreviated: [\n    \"जन\",\n    \"फ़र\",\n    \"मार्च\",\n    \"अप्रैल\",\n    \"मई\",\n    \"जून\",\n    \"जुल\",\n    \"अग\",\n    \"सित\",\n    \"अक्टू\",\n    \"नव\",\n    \"दिस\",\n  ],\n\n  wide: [\n    \"जनवरी\",\n    \"फ़रवरी\",\n    \"मार्च\",\n    \"अप्रैल\",\n    \"मई\",\n    \"जून\",\n    \"जुलाई\",\n    \"अगस्त\",\n    \"सितंबर\",\n    \"अक्टूबर\",\n    \"नवंबर\",\n    \"दिसंबर\",\n  ],\n};\n\n// CLDR #1689 - #1744\nconst dayValues = {\n  narrow: [\"र\", \"सो\", \"मं\", \"बु\", \"गु\", \"शु\", \"श\"],\n  short: [\"र\", \"सो\", \"मं\", \"बु\", \"गु\", \"शु\", \"श\"],\n  abbreviated: [\"रवि\", \"सोम\", \"मंगल\", \"बुध\", \"गुरु\", \"शुक्र\", \"शनि\"],\n  wide: [\n    \"रविवार\",\n    \"सोमवार\",\n    \"मंगलवार\",\n    \"बुधवार\",\n    \"गुरुवार\",\n    \"शुक्रवार\",\n    \"शनिवार\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\",\n  },\n  abbreviated: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\",\n  },\n  wide: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\",\n  },\n  abbreviated: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\",\n  },\n  wide: {\n    am: \"पूर्वाह्न\",\n    pm: \"अपराह्न\",\n    midnight: \"मध्यरात्रि\",\n    noon: \"दोपहर\",\n    morning: \"सुबह\",\n    afternoon: \"दोपहर\",\n    evening: \"शाम\",\n    night: \"रात\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return numberToLocale(number);\n};\n\nexport function localeToNumber(locale) {\n  const enNumber = locale.toString().replace(/[१२३४५६७८९०]/g, function (match) {\n    return numberValues.number[match];\n  });\n  return Number(enNumber);\n}\n\nexport function numberToLocale(enNumber) {\n  return enNumber.toString().replace(/\\d/g, function (match) {\n    return numberValues.locale[match];\n  });\n}\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,YAAY,GAAG;EACnBC,MAAM,EAAE;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE,GAAG;IACN,CAAC,EAAE;EACL,CAAC;EACDC,MAAM,EAAE;IACN,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE;EACP;AACF,CAAC;;AAED;AACA,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC;EAC9BC,WAAW,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC;EACnCC,IAAI,EAAE,CAAC,WAAW,EAAE,SAAS;AAC/B,CAAC;;AAED;AACA,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACzCC,IAAI,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa;AACrE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CACN,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,GAAG,EACH,IAAI,CACL;EAEDC,WAAW,EAAE,CACX,IAAI,EACJ,KAAK,EACL,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,OAAO,EACP,IAAI,EACJ,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,OAAO,EACP,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,OAAO,EACP,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,QAAQ;AAEZ,CAAC;;AAED;AACA,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EAChDM,KAAK,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EAC/CL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC;EAClEC,IAAI,EAAE,CACJ,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,UAAU,EACV,QAAQ;AAEZ,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,MAAMrB,MAAM,GAAGsB,MAAM,CAACF,WAAW,CAAC;EAClC,OAAOG,cAAc,CAACvB,MAAM,CAAC;AAC/B,CAAC;AAED,OAAO,SAASwB,cAAcA,CAACzB,MAAM,EAAE;EACrC,MAAM0B,QAAQ,GAAG1B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,eAAe,EAAE,UAAUC,KAAK,EAAE;IAC3E,OAAO9B,YAAY,CAACE,MAAM,CAAC4B,KAAK,CAAC;EACnC,CAAC,CAAC;EACF,OAAON,MAAM,CAACG,QAAQ,CAAC;AACzB;AAEA,OAAO,SAASF,cAAcA,CAACE,QAAQ,EAAE;EACvC,OAAOA,QAAQ,CAACC,QAAQ,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,UAAUC,KAAK,EAAE;IACzD,OAAO9B,YAAY,CAACC,MAAM,CAAC6B,KAAK,CAAC;EACnC,CAAC,CAAC;AACJ;AAEA,OAAO,MAAMC,QAAQ,GAAG;EACtBV,aAAa;EAEbW,GAAG,EAAEjC,eAAe,CAAC;IACnBkC,MAAM,EAAE9B,SAAS;IACjB+B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAEpC,eAAe,CAAC;IACvBkC,MAAM,EAAE1B,aAAa;IACrB2B,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAEtC,eAAe,CAAC;IACrBkC,MAAM,EAAEzB,WAAW;IACnB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAEvC,eAAe,CAAC;IACnBkC,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAExC,eAAe,CAAC;IACzBkC,MAAM,EAAEtB,eAAe;IACvBuB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEpB,yBAAyB;IAC3CqB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}