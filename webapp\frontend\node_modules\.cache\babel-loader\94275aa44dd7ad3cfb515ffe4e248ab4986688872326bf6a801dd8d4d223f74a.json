{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Box, Typography, Paper, Grid, Button, Chip, Alert, CircularProgress, Divider, IconButton, FormControl, InputLabel, Select, MenuItem, TextField, Switch, FormControlLabel, Drawer, List, ListItemButton, ListItemIcon, ListItemText, AppBar, Toolbar, CssBaseline, Stack // Added for easier spacing\n} from '@mui/material';\nimport { Assessment as AssessmentIcon, BarChart as BarChartIcon, List as ListIcon, Download as DownloadIcon, Refresh as RefreshIcon,\n// Kept for retry logic\nArrowBack as ArrowBackIcon, Cable as CableIcon, Inventory as InventoryIcon, Timeline as TimelineIcon,\n// Added for consistency\nShow<PERSON>hart as ShowChartIcon, Menu as MenuIcon // For potential mobile drawer toggle\n} from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext'; // Assuming path is correct\nimport AdminHomeButton from '../../components/common/AdminHomeButton'; // Assuming path is correct\nimport reportService from '../../services/reportService'; // Assuming path is correct\nimport FilterableTable from '../../components/common/FilterableTable'; // Assuming path is correct\n\n// Import dei componenti grafici (assuming paths are correct)\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DRAWER_WIDTH = 280;\nconst ReportCaviPageProfessional = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    cantiereId\n  } = useParams();\n  // const { user } = useAuth(); // user not directly used in this refactoring, but good to have if needed for permissions\n\n  const [loadingGlobal, setLoadingGlobal] = useState(false); // For initial bulk load\n  const [loadingReport, setLoadingReport] = useState(false); // For specific report generation\n  const [error, setError] = useState(null);\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    // Will be populated on demand\n    posaPeriodo: null // Will be populated on demand\n  });\n  const [formData, setFormData] = useState({\n    id_bobina: '',\n    data_inizio: '',\n    data_fine: ''\n  });\n  const [showCharts, setShowCharts] = useState(true);\n  const [drawerOpen, setDrawerOpen] = useState(true); // For permanent drawer, can be adapted for mobile\n\n  const reportTypes = [{\n    id: 'progress',\n    title: 'Report Avanzamento',\n    icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 58\n    }, this),\n    color: 'primary'\n  }, {\n    id: 'boq',\n    title: 'Bill of Quantities',\n    icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 53\n    }, this),\n    color: 'secondary'\n  }, {\n    id: 'bobine',\n    title: 'Report Utilizzo Bobine',\n    icon: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 60\n    }, this),\n    color: 'success'\n  }, {\n    id: 'cavi-stato',\n    title: 'Report Cavi per Stato',\n    icon: /*#__PURE__*/_jsxDEV(BarChartIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 63\n    }, this),\n    color: 'error'\n  }, {\n    id: 'bobina-specifica',\n    title: 'Report Bobina Specifica',\n    icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 71\n    }, this),\n    color: 'info'\n  }, {\n    id: 'posa-periodo',\n    title: 'Report Posa per Periodo',\n    icon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 67\n    }, this),\n    color: 'warning'\n  }];\n  const loadInitialReports = useCallback(async () => {\n    if (!cantiereId) return;\n    setLoadingGlobal(true);\n    setError(null);\n    try {\n      const reportPromises = {\n        progress: reportService.getProgressReport(cantiereId, 'video').catch(err => ({\n          error: true,\n          type: 'progress',\n          err\n        })),\n        boq: reportService.getBillOfQuantities(cantiereId, 'video').catch(err => ({\n          error: true,\n          type: 'boq',\n          err\n        })),\n        bobine: reportService.getBobineReport(cantiereId, 'video').catch(err => ({\n          error: true,\n          type: 'bobine',\n          err\n        })),\n        caviStato: reportService.getCaviStatoReport(cantiereId, 'video').catch(err => ({\n          error: true,\n          type: 'cavi-stato',\n          err\n        }))\n      };\n      const results = await Promise.all(Object.values(reportPromises));\n      const newReportsData = {\n        ...reportsData\n      };\n      let hasError = false;\n      let successfulLoads = 0;\n      results.forEach(res => {\n        if (res && !res.error && res.content) {\n          const type = Object.keys(reportPromises).find(key => {\n            var _res$content;\n            return reportPromises[key].toString().includes(((_res$content = res.content) === null || _res$content === void 0 ? void 0 : _res$content.nome_cantiere) || '###');\n          }); // Heuristic to find type\n          // A more robust way would be to ensure service calls return type or map promises better\n          if (res.content && res.content.nome_cantiere) {\n            // Basic check for valid report content\n            if (results[0] === res) newReportsData.progress = res.content;\n            if (results[1] === res) newReportsData.boq = res.content;\n            if (results[2] === res) newReportsData.bobine = res.content;\n            if (results[3] === res) newReportsData.caviStato = res.content;\n            successfulLoads++;\n          }\n        } else if (res && res.error) {\n          console.error(`Error loading ${res.type} report:`, res.err);\n          hasError = true;\n        }\n      });\n      setReportsData(newReportsData);\n      if (hasError && successfulLoads === 0) {\n        setError('Errore nel caricamento di alcuni report iniziali. Alcuni dati potrebbero non essere disponibili.');\n      }\n    } catch (err) {\n      console.error('Unexpected error loading initial reports:', err);\n      setError('Errore generale nel caricamento dei report. Riprova più tardi.');\n    } finally {\n      setLoadingGlobal(false);\n    }\n  }, [cantiereId]); // reportsData removed from dependency array to prevent loop\n\n  useEffect(() => {\n    loadInitialReports();\n  }, [loadInitialReports]);\n  const handleInputChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const generateReportWithFormat = async (reportId, format, params = {}) => {\n    setLoadingReport(true);\n    setError(null);\n    try {\n      let response;\n      const currentIdBobina = params.id_bobina || formData.id_bobina;\n      const currentDataInizio = params.data_inizio || formData.data_inizio;\n      const currentDataFine = params.data_fine || formData.data_fine;\n      switch (reportId) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!currentIdBobina) {\n            setError('ID Bobina è richiesto.');\n            setLoadingReport(false);\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, currentIdBobina, format);\n          break;\n        case 'posa-periodo':\n          if (!currentDataInizio || !currentDataFine) {\n            setError('Date di inizio e fine sono richieste.');\n            setLoadingReport(false);\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, currentDataInizio, currentDataFine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        setReportsData(prev => ({\n          ...prev,\n          [reportId]: response.content\n        }));\n      } else if (response.file_url) {\n        window.open(response.file_url, '_blank');\n      } else {\n        setError('URL del file non ricevuto per il download.');\n      }\n    } catch (err) {\n      console.error(`Errore nella generazione del report ${reportId}:`, err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n      // Clear stale data for on-demand reports if generation fails\n      if (reportId === 'bobinaSpecifica' || reportId === 'posaPeriodo') {\n        setReportsData(prev => ({\n          ...prev,\n          [reportId]: null\n        }));\n      }\n    } finally {\n      setLoadingReport(false);\n    }\n  };\n  const renderReportHeader = (title, reportIdForExport) => /*#__PURE__*/_jsxDEV(Stack, {\n    direction: \"row\",\n    justifyContent: \"space-between\",\n    alignItems: \"center\",\n    spacing: 2,\n    sx: {\n      mb: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      component: \"h2\",\n      sx: {\n        fontWeight: 600\n      },\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      spacing: 1,\n      alignItems: \"center\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 22\n        }, this),\n        onClick: () => generateReportWithFormat(reportIdForExport, 'pdf'),\n        variant: \"outlined\",\n        size: \"small\",\n        disabled: loadingReport,\n        children: \"PDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 22\n        }, this),\n        onClick: () => generateReportWithFormat(reportIdForExport, 'excel'),\n        variant: \"outlined\",\n        size: \"small\",\n        color: \"success\",\n        disabled: loadingReport,\n        children: \"Excel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this),\n        labelPlacement: \"start\",\n        label: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            fontSize: \"small\",\n            sx: {\n              mr: 0.5\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this), \" Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 196,\n    columnNumber: 5\n  }, this);\n  const renderKpiCard = (title, value, caption, icon, color = \"text.secondary\") => /*#__PURE__*/_jsxDEV(Grid, {\n    item: true,\n    xs: 12,\n    sm: 6,\n    md: 3,\n    children: /*#__PURE__*/_jsxDEV(Paper, {\n      variant: \"outlined\",\n      sx: {\n        p: 2,\n        height: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          justifyContent: \"space-between\",\n          alignItems: \"flex-start\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            color: \"text.secondary\",\n            sx: {\n              fontWeight: 500\n            },\n            children: title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 23\n          }, this), icon && /*#__PURE__*/React.cloneElement(icon, {\n            sx: {\n              color: color\n            }\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 19\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          sx: {\n            fontWeight: 'bold',\n            color: color\n          },\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 19\n        }, this), caption && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: caption\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 31\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 15\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 11\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 240,\n    columnNumber: 7\n  }, this);\n\n  // --- Individual Report View Components ---\n\n  const ProgressReportView = () => {\n    const data = reportsData.progress;\n    if (loadingGlobal && !data) return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 77\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 40\n    }, this);\n    if (!data) return /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      children: \"Dati del Report Avanzamento non disponibili.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 23\n    }, this);\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      elevation: 0,\n      children: [renderReportHeader(`Report Avanzamento - ${data.nome_cantiere || 'Cantiere'}`, 'progress'), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mb: 3\n        },\n        children: [renderKpiCard(\"Metri Totali\", `${data.metri_totali || 0}m`, null, /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 78\n        }, this), 'primary.main'), renderKpiCard(\"Metri Posati\", `${data.metri_posati || 0}m`, `(${data.percentuale_avanzamento || 0}%)`, /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 115\n        }, this), 'success.main'), renderKpiCard(\"Metri Rimanenti\", `${data.metri_da_posare || 0}m`, `(${(100 - (data.percentuale_avanzamento || 0)).toFixed(2)}%)`, /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 142\n        }, this), 'warning.main'), renderKpiCard(\"Media/Giorno\", `${data.media_giornaliera || 0}m`, data.giorni_stimati ? `(${data.giorni_stimati} giorni rimasti)` : null, /*#__PURE__*/_jsxDEV(ShowChartIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 149\n        }, this), 'info.main')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), showCharts && data && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3,\n          p: 2,\n          border: '1px solid #eee',\n          borderRadius: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Grafico Avanzamento\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ProgressChart, {\n          data: data\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            variant: \"outlined\",\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2\n              },\n              children: \"Dettagli Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: \"Totale Cavi:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 71\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: data.totale_cavi\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 108\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: \"Cavi Posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 71\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'success.main'\n                  },\n                  children: [data.cavi_posati, \" (\", data.percentuale_cavi, \"%)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 108\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: \"Cavi Rimanenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 71\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    fontWeight: 600,\n                    color: 'warning.main'\n                  },\n                  children: [data.cavi_rimanenti, \" (\", (100 - data.percentuale_cavi).toFixed(2), \"%)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 111\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            variant: \"outlined\",\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2\n              },\n              children: \"Performance e Previsioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                justifyContent: \"space-between\",\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: \"Media Giornaliera:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 71\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: [data.media_giornaliera, \"m/giorno\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 114\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), data.giorni_stimati && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  justifyContent: \"space-between\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    children: \"Giorni Stimati:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 75\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      fontWeight: 600\n                    },\n                    children: [data.giorni_stimati, \" giorni\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 115\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  justifyContent: \"space-between\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    children: \"Data Completamento:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 75\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      fontWeight: 600\n                    },\n                    children: data.data_completamento\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 302,\n                    columnNumber: 119\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 16\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            variant: \"outlined\",\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2\n              },\n              children: \"Attivit\\xE0 Recente\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n              data: data.posa_recente.map(posa => ({\n                data: posa.data,\n                metri: `${posa.metri}m`\n              })),\n              columns: [{\n                field: 'data',\n                headerName: 'Data',\n                width: 200\n              }, {\n                field: 'metri',\n                headerName: 'Metri Posati',\n                width: 150,\n                align: 'right'\n              }],\n              pagination: data.posa_recente.length > 6,\n              pageSize: 6\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this);\n  };\n  const BoqReportView = () => {\n    const data = reportsData.boq;\n    if (loadingGlobal && !data) return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 77\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 40\n    }, this);\n    if (!data) return /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      children: \"Dati Bill of Quantities non disponibili.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 23\n    }, this);\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      elevation: 0,\n      children: [renderReportHeader(`Bill of Quantities - ${data.nome_cantiere || 'Cantiere'}`, 'boq'), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), showCharts && data && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3,\n          p: 2,\n          border: '1px solid #eee',\n          borderRadius: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Grafico BOQ\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 14\n        }, this), /*#__PURE__*/_jsxDEV(BoqChart, {\n          data: data\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        variant: \"outlined\",\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2\n          },\n          children: \"Cavi per Tipologia\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n          data: data.cavi_per_tipo || [],\n          columns: [/* Same as your original */\n          {\n            field: 'tipologia',\n            headerName: 'Tipologia',\n            width: 150\n          }, {\n            field: 'sezione',\n            headerName: 'Sezione',\n            width: 100\n          }, {\n            field: 'num_cavi',\n            headerName: '#Cavi',\n            width: 80,\n            align: 'right',\n            dataType: 'number'\n          }, {\n            field: 'metri_teorici',\n            headerName: 'Teorici',\n            width: 120,\n            align: 'right',\n            renderCell: row => `${row.metri_teorici}m`\n          }, {\n            field: 'metri_reali',\n            headerName: 'Reali',\n            width: 120,\n            align: 'right',\n            renderCell: row => `${row.metri_reali}m`\n          }, {\n            field: 'metri_da_posare',\n            headerName: 'Da Posare',\n            width: 120,\n            align: 'right',\n            renderCell: row => `${row.metri_da_posare}m`\n          }],\n          pageSize: 10\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        variant: \"outlined\",\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2\n          },\n          children: \"Bobine Disponibili\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n          data: data.bobine_per_tipo || [],\n          columns: [/* Same as your original */\n          {\n            field: 'tipologia',\n            headerName: 'Tipologia',\n            width: 150\n          }, {\n            field: 'sezione',\n            headerName: 'Sezione',\n            width: 100\n          }, {\n            field: 'num_bobine',\n            headerName: '#Bobine',\n            width: 100,\n            align: 'right',\n            dataType: 'number'\n          }, {\n            field: 'metri_disponibili',\n            headerName: 'Metri Disp.',\n            width: 150,\n            align: 'right',\n            renderCell: row => `${row.metri_disponibili}m`\n          }],\n          pageSize: 10\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 7\n    }, this);\n  };\n  const BobineReportView = () => {\n    const data = reportsData.bobine;\n    if (loadingGlobal && !data) return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 77\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 40\n    }, this);\n    if (!data) return /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      children: \"Dati Utilizzo Bobine non disponibili.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 23\n    }, this);\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      elevation: 0,\n      children: [renderReportHeader(`Report Utilizzo Bobine (${data.totale_bobine || 0} totali) - ${data.nome_cantiere || 'Cantiere'}`, 'bobine'), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), showCharts && data && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3,\n          p: 2,\n          border: '1px solid #eee',\n          borderRadius: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Grafico Utilizzo Bobine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(BobineChart, {\n          data: data\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        variant: \"outlined\",\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2\n          },\n          children: \"Dettaglio Bobine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n          data: data.bobine || [],\n          columns: [/* Same as your original */\n          {\n            field: 'id_bobina',\n            headerName: 'ID Bobina',\n            width: 120\n          }, {\n            field: 'tipologia',\n            headerName: 'Tipologia',\n            width: 150\n          }, {\n            field: 'sezione',\n            headerName: 'Sezione',\n            width: 100\n          }, {\n            field: 'stato',\n            headerName: 'Stato',\n            width: 120,\n            renderCell: row => /*#__PURE__*/_jsxDEV(Chip, {\n              label: row.stato,\n              color: row.stato === 'DISPONIBILE' ? 'success' : 'warning',\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 89\n            }, this)\n          }, {\n            field: 'metri_totali',\n            headerName: 'Totali',\n            width: 120,\n            align: 'right',\n            renderCell: row => `${row.metri_totali}m`\n          }, {\n            field: 'metri_residui',\n            headerName: 'Residui',\n            width: 120,\n            align: 'right',\n            renderCell: row => `${row.metri_residui}m`\n          }, {\n            field: 'metri_utilizzati',\n            headerName: 'Utilizzati',\n            width: 140,\n            align: 'right',\n            renderCell: row => `${row.metri_utilizzati}m`\n          }, {\n            field: 'percentuale_utilizzo',\n            headerName: '% Uso',\n            width: 100,\n            align: 'right',\n            renderCell: row => `${row.percentuale_utilizzo}%`\n          }],\n          pageSize: 10\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this);\n  };\n  const CaviStatoReportView = () => {\n    const data = reportsData.caviStato;\n    if (loadingGlobal && !data) return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 77\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 40\n    }, this);\n    if (!data) return /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      children: \"Dati Cavi per Stato non disponibili.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 23\n    }, this);\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      elevation: 0,\n      children: [renderReportHeader(`Report Cavi per Stato - ${data.nome_cantiere || 'Cantiere'}`, 'cavi-stato'), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this), showCharts && data && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3,\n          p: 2,\n          border: '1px solid #eee',\n          borderRadius: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Grafico Cavi per Stato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CaviStatoChart, {\n          data: data\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        variant: \"outlined\",\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2\n          },\n          children: \"Distribuzione Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n          data: data.cavi_per_stato || [],\n          columns: [/* Same as your original */\n          {\n            field: 'stato',\n            headerName: 'Stato',\n            width: 150,\n            renderCell: row => /*#__PURE__*/_jsxDEV(Chip, {\n              label: row.stato,\n              color: row.stato === 'Installato' ? 'success' : row.stato === 'Da Posare' ? 'default' : 'warning',\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 89\n            }, this)\n          }, {\n            field: 'num_cavi',\n            headerName: '#Cavi',\n            width: 120,\n            align: 'right',\n            dataType: 'number'\n          }, {\n            field: 'metri_teorici',\n            headerName: 'Metri Teorici',\n            width: 150,\n            align: 'right',\n            renderCell: row => `${row.metri_teorici}m`\n          }, {\n            field: 'metri_reali',\n            headerName: 'Metri Reali',\n            width: 150,\n            align: 'right',\n            renderCell: row => `${row.metri_reali}m`\n          }],\n          pagination: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 7\n    }, this);\n  };\n  const BobinaSpecificaReportView = () => {\n    var _data$bobina;\n    const data = reportsData.bobinaSpecifica;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      elevation: 0,\n      children: [renderReportHeader(\"Report Bobina Specifica\", 'bobina-specifica'), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        component: \"form\",\n        onSubmit: e => {\n          e.preventDefault();\n          generateReportWithFormat('bobina-specifica', 'video');\n        },\n        spacing: 2,\n        direction: {\n          xs: 'column',\n          sm: 'row'\n        },\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          name: \"id_bobina\",\n          label: \"ID Bobina\",\n          value: formData.id_bobina,\n          onChange: handleInputChange,\n          size: \"small\",\n          helperText: \"Es: 1 per C1_B1\",\n          sx: {\n            flexGrow: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          disabled: loadingReport || !formData.id_bobina,\n          startIcon: loadingReport ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 128\n          }, this) : /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 161\n          }, this),\n          children: \"Visualizza Dati Bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this), error && selectedReportType === 'bobina-specifica' && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 64\n      }, this), loadingReport && selectedReportType === 'bobina-specifica' && !data && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center',\n          p: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 118\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 81\n      }, this), data && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mt: 2,\n            mb: 1,\n            fontWeight: 500\n          },\n          children: [\"Dati Bobina: \", (_data$bobina = data.bobina) === null || _data$bobina === void 0 ? void 0 : _data$bobina.id_bobina]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              variant: \"outlined\",\n              sx: {\n                p: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                sx: {\n                  mb: 1,\n                  fontWeight: 'medium'\n                },\n                children: \"Dettagli\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 19\n              }, this), data.bobina && /*#__PURE__*/_jsxDEV(Stack, {\n                spacing: 0.5,\n                children: [/*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  justifyContent: \"space-between\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: \"ID:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 75\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 500\n                    },\n                    children: data.bobina.id_bobina\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 119\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  justifyContent: \"space-between\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: \"Tipologia:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 75\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 500\n                    },\n                    children: data.bobina.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 126\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  justifyContent: \"space-between\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: \"Sezione:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 75\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 500\n                    },\n                    children: data.bobina.sezione\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 124\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  justifyContent: \"space-between\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: \"Stato:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 75\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: data.bobina.stato,\n                    color: data.bobina.stato === 'DISPONIBILE' ? 'success' : 'warning',\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 122\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 35\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              variant: \"outlined\",\n              sx: {\n                p: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                sx: {\n                  mb: 1,\n                  fontWeight: 'medium'\n                },\n                children: \"Metriche Utilizzo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this), data.bobina && /*#__PURE__*/_jsxDEV(Stack, {\n                spacing: 0.5,\n                children: [/*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  justifyContent: \"space-between\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: \"Metri Totali:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 76\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 500\n                    },\n                    children: [data.bobina.metri_totali, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 130\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  justifyContent: \"space-between\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: \"Metri Utilizzati:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 76\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 500,\n                      color: 'success.dark'\n                    },\n                    children: [data.bobina.metri_utilizzati, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 134\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  justifyContent: \"space-between\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: \"Metri Residui:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 76\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 500,\n                      color: 'warning.dark'\n                    },\n                    children: [data.bobina.metri_residui, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 131\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  justifyContent: \"space-between\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: \"% Utilizzo:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 76\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 500\n                    },\n                    children: [data.bobina.percentuale_utilizzo, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 128\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 22\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 35\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              variant: \"outlined\",\n              sx: {\n                p: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                sx: {\n                  mb: 1,\n                  fontWeight: 'medium'\n                },\n                children: [\"Cavi Associati (\", data.totale_cavi || 0, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n                data: data.cavi_associati || [],\n                columns: [/* Same as your original */\n                {\n                  field: 'id_cavo',\n                  headerName: 'ID Cavo',\n                  width: 120\n                }, {\n                  field: 'sistema',\n                  headerName: 'Sistema',\n                  width: 120\n                }, {\n                  field: 'utility',\n                  headerName: 'Utility',\n                  width: 120\n                }, {\n                  field: 'tipologia',\n                  headerName: 'Tipologia',\n                  width: 150\n                }, {\n                  field: 'metri_teorici',\n                  headerName: 'Teorici',\n                  width: 120,\n                  align: 'right',\n                  renderCell: row => `${row.metri_teorici}m`\n                }, {\n                  field: 'metri_reali',\n                  headerName: 'Reali',\n                  width: 120,\n                  align: 'right',\n                  renderCell: row => `${row.metri_reali}m`\n                }, {\n                  field: 'stato',\n                  headerName: 'Stato',\n                  width: 120,\n                  renderCell: row => /*#__PURE__*/_jsxDEV(Chip, {\n                    label: row.stato,\n                    color: row.stato === 'POSATO' || row.stato === 'Installato' ? 'success' : 'default',\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 97\n                  }, this)\n                }],\n                pageSize: 5\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 7\n    }, this);\n  };\n  const PosaPeriodoReportView = () => {\n    const data = reportsData.posaPeriodo;\n    const today = new Date().toISOString().split('T')[0];\n    const lastMonthDate = new Date();\n    lastMonthDate.setMonth(lastMonthDate.getMonth() - 1);\n    const lastMonth = lastMonthDate.toISOString().split('T')[0];\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      elevation: 0,\n      children: [renderReportHeader(\"Report Posa per Periodo\", 'posa-periodo'), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        component: \"form\",\n        onSubmit: e => {\n          e.preventDefault();\n          generateReportWithFormat('posa-periodo', 'video');\n        },\n        spacing: 2,\n        direction: {\n          xs: 'column',\n          sm: 'row'\n        },\n        sx: {\n          mb: 2\n        },\n        alignItems: \"flex-start\",\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          name: \"data_inizio\",\n          label: \"Data Inizio\",\n          type: \"date\",\n          value: formData.data_inizio || lastMonth,\n          onChange: handleInputChange,\n          InputLabelProps: {\n            shrink: true\n          },\n          size: \"small\",\n          sx: {\n            flexGrow: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          name: \"data_fine\",\n          label: \"Data Fine\",\n          type: \"date\",\n          value: formData.data_fine || today,\n          onChange: handleInputChange,\n          InputLabelProps: {\n            shrink: true\n          },\n          size: \"small\",\n          sx: {\n            flexGrow: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          disabled: loadingReport || !formData.data_inizio || !formData.data_fine,\n          startIcon: loadingReport ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 153\n          }, this) : /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 186\n          }, this),\n          children: \"Visualizza Dati Periodo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 9\n      }, this), error && selectedReportType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 60\n      }, this), loadingReport && selectedReportType === 'posa-periodo' && !data && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center',\n          p: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 114\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 77\n      }, this), data && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mt: 2,\n            mb: 1,\n            fontWeight: 500\n          },\n          children: [\"Dati per il periodo: \", data.data_inizio, \" - \", data.data_fine]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mb: 3\n          },\n          children: [renderKpiCard(\"Metri Totali Periodo\", `${data.totale_metri_periodo || 0}m`, null, /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 100\n          }, this), 'warning.main'), renderKpiCard(\"Giorni Attivi\", data.giorni_attivi || 0, null, /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 80\n          }, this), 'info.main'), renderKpiCard(\"Media/Giorno\", `${data.media_giornaliera || 0}m`, null, /*#__PURE__*/_jsxDEV(ShowChartIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 89\n          }, this), 'success.main'), renderKpiCard(\"Media/Settimana (Stimata)\", `${data.giorni_attivi ? Math.round((data.totale_metri_periodo || 0) / data.giorni_attivi * 7) : 0}m`, null, /*#__PURE__*/_jsxDEV(BarChartIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 169\n          }, this), 'primary.main')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 13\n        }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            p: 2,\n            border: '1px solid #eee',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Grafico Posa nel Periodo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 18\n          }, this), /*#__PURE__*/_jsxDEV(TimelineChart, {\n            data: data\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Paper, {\n          variant: \"outlined\",\n          sx: {\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2\n            },\n            children: \"Dettaglio Posa Giornaliera\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.posa_giornaliera || [],\n            columns: [/* Same as your original */\n            {\n              field: 'data',\n              headerName: 'Data',\n              width: 200\n            }, {\n              field: 'metri',\n              headerName: 'Metri Posati',\n              width: 150,\n              align: 'right',\n              renderCell: row => `${row.metri}m`\n            }],\n            pageSize: 10\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 7\n    }, this);\n  };\n\n  // --- Main Render ---\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      minHeight: '100vh',\n      bgcolor: 'grey.100'\n    },\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 595,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      sx: {\n        zIndex: theme => theme.zIndex.drawer + 1,\n        backgroundColor: 'primary.dark'\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"inherit\",\n          \"aria-label\": \"open drawer\",\n          edge: \"start\",\n          onClick: () => setDrawerOpen(!drawerOpen),\n          sx: {\n            mr: 2,\n            display: {\n              sm: 'none'\n            }\n          } // Example for mobile toggle\n          ,\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          noWrap: true,\n          component: \"div\",\n          sx: {\n            flexGrow: 1\n          },\n          children: [\"Reportistica Cantiere \", cantiereId && `- ID ${cantiereId}`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: () => navigate(-1),\n            startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 77\n            }, this),\n            children: \"Indietro\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {\n            color: \"inherit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 596,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n      variant: \"permanent\" // \"temporary\" for mobile with drawerOpen state\n      ,\n      open: drawerOpen,\n      sx: {\n        width: DRAWER_WIDTH,\n        flexShrink: 0,\n        [`& .MuiDrawer-paper`]: {\n          width: DRAWER_WIDTH,\n          boxSizing: 'border-box',\n          top: '64px',\n          height: 'calc(100% - 64px)'\n        } // Adjust top for AppBar\n      },\n      children: /*#__PURE__*/_jsxDEV(List, {\n        sx: {\n          pt: 2\n        },\n        children: [\" \", reportTypes.map(report => /*#__PURE__*/_jsxDEV(ListItemButton, {\n          selected: selectedReportType === report.id,\n          onClick: () => {\n            setSelectedReportType(report.id);\n            setError(null); // Clear previous errors when changing report type\n            // Reset form data for on-demand reports if switching to them\n            if (report.id === 'bobina-specifica' || report.id === 'posa-periodo') {\n              // Only reset if not already viewing that report type, or data may clear during input\n              if (selectedReportType !== report.id) {\n                setFormData({\n                  id_bobina: '',\n                  data_inizio: '',\n                  data_fine: ''\n                });\n                // Clear previous data for these reports to force re-fetch with new params\n                setReportsData(prev => ({\n                  ...prev,\n                  bobinaSpecifica: null,\n                  posaPeriodo: null\n                }));\n              }\n            } else if (!reportsData[report.id] && !loadingGlobal) {\n              // If data not preloaded and not currently loading all\n              generateReportWithFormat(report.id, 'video');\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            sx: {\n              color: `${report.color}.main`\n            },\n            children: report.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: report.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 15\n          }, this)]\n        }, report.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 617,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"main\",\n      sx: {\n        flexGrow: 1,\n        p: 3,\n        mt: '64px'\n      },\n      children: [\" \", loadingGlobal && !Object.values(reportsData).some(d => d) &&\n      /*#__PURE__*/\n      // Show global loader only if no data at all is loaded yet\n      _jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '50vh'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            mt: 2\n          },\n          children: \"Caricamento report iniziali...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 13\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 19\n      }, this), !loadingGlobal && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [selectedReportType === 'progress' && /*#__PURE__*/_jsxDEV(ProgressReportView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 51\n        }, this), selectedReportType === 'boq' && /*#__PURE__*/_jsxDEV(BoqReportView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 46\n        }, this), selectedReportType === 'bobine' && /*#__PURE__*/_jsxDEV(BobineReportView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 49\n        }, this), selectedReportType === 'cavi-stato' && /*#__PURE__*/_jsxDEV(CaviStatoReportView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 53\n        }, this), selectedReportType === 'bobina-specifica' && /*#__PURE__*/_jsxDEV(BobinaSpecificaReportView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 669,\n          columnNumber: 59\n        }, this), selectedReportType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(PosaPeriodoReportView, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 55\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 594,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageProfessional, \"VlrBV4FTrJ4W8Rxo30ZZWpsS54A=\", false, function () {\n  return [useNavigate, useParams];\n});\n_c = ReportCaviPageProfessional;\nexport default ReportCaviPageProfessional;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageProfessional\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Box", "Typography", "Paper", "Grid", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "IconButton", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Switch", "FormControlLabel", "Drawer", "List", "ListItemButton", "ListItemIcon", "ListItemText", "AppBar", "<PERSON><PERSON><PERSON>", "CssBaseline", "<PERSON><PERSON>", "Assessment", "AssessmentIcon", "<PERSON><PERSON><PERSON>", "BarChartIcon", "ListIcon", "Download", "DownloadIcon", "Refresh", "RefreshIcon", "ArrowBack", "ArrowBackIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "Timeline", "TimelineIcon", "ShowChart", "ShowChartIcon", "<PERSON><PERSON>", "MenuIcon", "useNavigate", "useParams", "useAuth", "AdminHomeButton", "reportService", "FilterableTable", "ProgressChart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TimelineChart", "Cavi<PERSON>tato<PERSON>hart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DRAWER_WIDTH", "ReportCaviPageProfessional", "_s", "navigate", "cantiereId", "loadingGlobal", "setLoadingGlobal", "loadingReport", "setLoadingReport", "error", "setError", "selectedReportType", "setSelectedReportType", "reportsData", "setReportsData", "progress", "boq", "bobine", "caviStato", "bobinaSpecifica", "posaPeriodo", "formData", "setFormData", "id_bobina", "data_inizio", "data_fine", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "drawerOpen", "setDrawerOpen", "reportTypes", "id", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "loadInitialReports", "reportPromises", "getProgressReport", "catch", "err", "type", "getBillOfQuantities", "getBobineReport", "getCaviStatoReport", "results", "Promise", "all", "Object", "values", "newReportsData", "<PERSON><PERSON><PERSON><PERSON>", "successfulLoads", "for<PERSON>ach", "res", "content", "keys", "find", "key", "_res$content", "toString", "includes", "nome_cantiere", "console", "handleInputChange", "e", "target", "name", "value", "generateReportWithFormat", "reportId", "format", "params", "response", "currentIdBobina", "currentDataInizio", "currentDataFine", "getBobinaReport", "getPosaPerPeriodoReport", "Error", "prev", "file_url", "window", "open", "detail", "message", "renderReportHeader", "reportIdForExport", "direction", "justifyContent", "alignItems", "spacing", "sx", "mb", "children", "variant", "component", "fontWeight", "startIcon", "onClick", "size", "disabled", "control", "checked", "onChange", "labelPlacement", "label", "display", "fontSize", "mr", "renderKpiCard", "caption", "item", "xs", "sm", "md", "p", "height", "cloneElement", "ProgressReportView", "data", "textAlign", "severity", "elevation", "my", "container", "metri_totali", "metri_posati", "percentuale_avanzamento", "metri_da_posare", "toFixed", "media_giornaliera", "giorni_stimati", "border", "borderRadius", "gutterBottom", "totale_cavi", "cavi_posati", "percentuale_cavi", "cavi_rimanenti", "data_completamento", "posa_recente", "length", "map", "posa", "metri", "columns", "field", "headerName", "width", "align", "pagination", "pageSize", "BoqReportView", "cavi_per_tipo", "dataType", "renderCell", "row", "metri_te<PERSON>ci", "metri_reali", "bobine_per_tipo", "metri_disponibili", "BobineReportView", "totale_bobine", "stato", "metri_residui", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "percentuale_utilizzo", "CaviStatoReportView", "cavi_per_stato", "BobinaSpecificaReportView", "_data$bobina", "onSubmit", "preventDefault", "helperText", "flexGrow", "mt", "bobina", "tipologia", "sezione", "cavi_associati", "PosaPeriodoReportView", "today", "Date", "toISOString", "split", "lastMonthDate", "setMonth", "getMonth", "lastM<PERSON>h", "InputLabelProps", "shrink", "totale_metri_periodo", "giorni_attivi", "Math", "round", "posa_giornal<PERSON>", "minHeight", "bgcolor", "position", "zIndex", "theme", "drawer", "backgroundColor", "edge", "noWrap", "flexShrink", "boxSizing", "top", "pt", "report", "selected", "primary", "some", "d", "flexDirection", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n  IconButton,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Switch,\n  FormControlLabel,\n  Drawer,\n  List,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  AppBar,\n  Toolbar,\n  CssBaseline,\n  Stack, // Added for easier spacing\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n  BarChart as BarChartIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Refresh as RefreshIcon, // Kept for retry logic\n  ArrowBack as ArrowBackIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  Timeline as TimelineIcon, // Added for consistency\n  ShowChart as ShowChartIcon,\n  Menu as MenuIcon, // For potential mobile drawer toggle\n} from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext'; // Assuming path is correct\nimport AdminHomeButton from '../../components/common/AdminHomeButton'; // Assuming path is correct\nimport reportService from '../../services/reportService'; // Assuming path is correct\nimport FilterableTable from '../../components/common/FilterableTable'; // Assuming path is correct\n\n// Import dei componenti grafici (assuming paths are correct)\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\n\nconst DRAWER_WIDTH = 280;\n\nconst ReportCaviPageProfessional = () => {\n  const navigate = useNavigate();\n  const { cantiereId } = useParams();\n  // const { user } = useAuth(); // user not directly used in this refactoring, but good to have if needed for permissions\n\n  const [loadingGlobal, setLoadingGlobal] = useState(false); // For initial bulk load\n  const [loadingReport, setLoadingReport] = useState(false); // For specific report generation\n  const [error, setError] = useState(null);\n\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null, // Will be populated on demand\n    posaPeriodo: null,    // Will be populated on demand\n  });\n\n  const [formData, setFormData] = useState({\n    id_bobina: '',\n    data_inizio: '',\n    data_fine: '',\n  });\n\n  const [showCharts, setShowCharts] = useState(true);\n  const [drawerOpen, setDrawerOpen] = useState(true); // For permanent drawer, can be adapted for mobile\n\n  const reportTypes = [\n    { id: 'progress', title: 'Report Avanzamento', icon: <AssessmentIcon />, color: 'primary' },\n    { id: 'boq', title: 'Bill of Quantities', icon: <ListIcon />, color: 'secondary' },\n    { id: 'bobine', title: 'Report Utilizzo Bobine', icon: <InventoryIcon />, color: 'success' },\n    { id: 'cavi-stato', title: 'Report Cavi per Stato', icon: <BarChartIcon />, color: 'error' },\n    { id: 'bobina-specifica', title: 'Report Bobina Specifica', icon: <CableIcon />, color: 'info' },\n    { id: 'posa-periodo', title: 'Report Posa per Periodo', icon: <TimelineIcon />, color: 'warning' },\n  ];\n\n  const loadInitialReports = useCallback(async () => {\n    if (!cantiereId) return;\n    setLoadingGlobal(true);\n    setError(null);\n    try {\n      const reportPromises = {\n        progress: reportService.getProgressReport(cantiereId, 'video').catch(err => ({ error: true, type: 'progress', err })),\n        boq: reportService.getBillOfQuantities(cantiereId, 'video').catch(err => ({ error: true, type: 'boq', err })),\n        bobine: reportService.getBobineReport(cantiereId, 'video').catch(err => ({ error: true, type: 'bobine', err })),\n        caviStato: reportService.getCaviStatoReport(cantiereId, 'video').catch(err => ({ error: true, type: 'cavi-stato', err })),\n      };\n\n      const results = await Promise.all(Object.values(reportPromises));\n\n      const newReportsData = { ...reportsData };\n      let hasError = false;\n      let successfulLoads = 0;\n\n      results.forEach(res => {\n        if (res && !res.error && res.content) {\n          const type = Object.keys(reportPromises).find(key => reportPromises[key].toString().includes(res.content?.nome_cantiere || '###')); // Heuristic to find type\n          // A more robust way would be to ensure service calls return type or map promises better\n          if (res.content && res.content.nome_cantiere) { // Basic check for valid report content\n             if(results[0] === res) newReportsData.progress = res.content;\n             if(results[1] === res) newReportsData.boq = res.content;\n             if(results[2] === res) newReportsData.bobine = res.content;\n             if(results[3] === res) newReportsData.caviStato = res.content;\n             successfulLoads++;\n          }\n        } else if (res && res.error) {\n          console.error(`Error loading ${res.type} report:`, res.err);\n          hasError = true;\n        }\n      });\n\n      setReportsData(newReportsData);\n      if (hasError && successfulLoads === 0) {\n        setError('Errore nel caricamento di alcuni report iniziali. Alcuni dati potrebbero non essere disponibili.');\n      }\n\n    } catch (err) {\n      console.error('Unexpected error loading initial reports:', err);\n      setError('Errore generale nel caricamento dei report. Riprova più tardi.');\n    } finally {\n      setLoadingGlobal(false);\n    }\n  }, [cantiereId]); // reportsData removed from dependency array to prevent loop\n\n  useEffect(() => {\n    loadInitialReports();\n  }, [loadInitialReports]);\n\n  const handleInputChange = (e) => {\n    setFormData({ ...formData, [e.target.name]: e.target.value });\n  };\n\n  const generateReportWithFormat = async (reportId, format, params = {}) => {\n    setLoadingReport(true);\n    setError(null);\n    try {\n      let response;\n      const currentIdBobina = params.id_bobina || formData.id_bobina;\n      const currentDataInizio = params.data_inizio || formData.data_inizio;\n      const currentDataFine = params.data_fine || formData.data_fine;\n\n      switch (reportId) {\n        case 'progress': response = await reportService.getProgressReport(cantiereId, format); break;\n        case 'boq': response = await reportService.getBillOfQuantities(cantiereId, format); break;\n        case 'bobine': response = await reportService.getBobineReport(cantiereId, format); break;\n        case 'cavi-stato': response = await reportService.getCaviStatoReport(cantiereId, format); break;\n        case 'bobina-specifica':\n          if (!currentIdBobina) { setError('ID Bobina è richiesto.'); setLoadingReport(false); return; }\n          response = await reportService.getBobinaReport(cantiereId, currentIdBobina, format);\n          break;\n        case 'posa-periodo':\n          if (!currentDataInizio || !currentDataFine) { setError('Date di inizio e fine sono richieste.'); setLoadingReport(false); return; }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, currentDataInizio, currentDataFine, format);\n          break;\n        default: throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        setReportsData(prev => ({ ...prev, [reportId]: response.content }));\n      } else if (response.file_url) {\n        window.open(response.file_url, '_blank');\n      } else {\n        setError('URL del file non ricevuto per il download.');\n      }\n    } catch (err) {\n      console.error(`Errore nella generazione del report ${reportId}:`, err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n      // Clear stale data for on-demand reports if generation fails\n      if (reportId === 'bobinaSpecifica' || reportId === 'posaPeriodo') {\n        setReportsData(prev => ({ ...prev, [reportId]: null }));\n      }\n    } finally {\n      setLoadingReport(false);\n    }\n  };\n\n  const renderReportHeader = (title, reportIdForExport) => (\n    <Stack direction=\"row\" justifyContent=\"space-between\" alignItems=\"center\" spacing={2} sx={{ mb: 2 }}>\n      <Typography variant=\"h5\" component=\"h2\" sx={{ fontWeight: 600 }}>\n        {title}\n      </Typography>\n      <Stack direction=\"row\" spacing={1} alignItems=\"center\">\n        <Button\n          startIcon={<DownloadIcon />}\n          onClick={() => generateReportWithFormat(reportIdForExport, 'pdf')}\n          variant=\"outlined\"\n          size=\"small\"\n          disabled={loadingReport}\n        >\n          PDF\n        </Button>\n        <Button\n          startIcon={<DownloadIcon />}\n          onClick={() => generateReportWithFormat(reportIdForExport, 'excel')}\n          variant=\"outlined\"\n          size=\"small\"\n          color=\"success\"\n          disabled={loadingReport}\n        >\n          Excel\n        </Button>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          labelPlacement=\"start\"\n          label={\n            <Typography variant=\"caption\" sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon fontSize=\"small\" sx={{ mr: 0.5 }} /> Grafici\n            </Typography>\n          }\n        />\n      </Stack>\n    </Stack>\n  );\n\n  const renderKpiCard = (title, value, caption, icon, color = \"text.secondary\") => (\n      <Grid item xs={12} sm={6} md={3}>\n          <Paper variant=\"outlined\" sx={{ p: 2, height: '100%' }}>\n              <Stack spacing={1}>\n                  <Stack direction=\"row\" justifyContent=\"space-between\" alignItems=\"flex-start\">\n                      <Typography variant=\"subtitle1\" color=\"text.secondary\" sx={{ fontWeight: 500 }}>\n                          {title}\n                      </Typography>\n                      {icon && React.cloneElement(icon, { sx: { color: color }})}\n                  </Stack>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: color }}>\n                      {value}\n                  </Typography>\n                  {caption && <Typography variant=\"caption\" color=\"text.secondary\">{caption}</Typography>}\n              </Stack>\n          </Paper>\n      </Grid>\n  );\n\n  // --- Individual Report View Components ---\n\n  const ProgressReportView = () => {\n    const data = reportsData.progress;\n    if (loadingGlobal && !data) return <Box sx={{textAlign: 'center', p:3}}><CircularProgress /></Box>;\n    if (!data) return <Alert severity=\"info\">Dati del Report Avanzamento non disponibili.</Alert>;\n\n    return (\n      <Paper sx={{ p: 3 }} elevation={0}>\n        {renderReportHeader(`Report Avanzamento - ${data.nome_cantiere || 'Cantiere'}`, 'progress')}\n        <Divider sx={{ my: 2 }} />\n        <Grid container spacing={2} sx={{ mb: 3 }}>\n          {renderKpiCard(\"Metri Totali\", `${data.metri_totali || 0}m`, null, <CableIcon />, 'primary.main')}\n          {renderKpiCard(\"Metri Posati\", `${data.metri_posati || 0}m`, `(${data.percentuale_avanzamento || 0}%)`, <TimelineIcon />, 'success.main')}\n          {renderKpiCard(\"Metri Rimanenti\", `${data.metri_da_posare || 0}m`, `(${(100 - (data.percentuale_avanzamento || 0)).toFixed(2)}%)`, <AssessmentIcon />, 'warning.main')}\n          {renderKpiCard(\"Media/Giorno\", `${data.media_giornaliera || 0}m`, data.giorni_stimati ? `(${data.giorni_stimati} giorni rimasti)` : null, <ShowChartIcon />, 'info.main')}\n        </Grid>\n\n        {showCharts && data && (\n          <Box sx={{ mb: 3, p:2, border: '1px solid #eee', borderRadius:1 }}>\n            <Typography variant=\"h6\" gutterBottom>Grafico Avanzamento</Typography>\n            <ProgressChart data={data} />\n          </Box>\n        )}\n\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={6}>\n            <Paper variant=\"outlined\" sx={{ p: 2 }}>\n              <Typography variant=\"h6\" sx={{ mb: 2 }}>Dettagli Cavi</Typography>\n              <Stack spacing={1}>\n                <Stack direction=\"row\" justifyContent=\"space-between\"><Typography>Totale Cavi:</Typography><Typography sx={{ fontWeight: 600 }}>{data.totale_cavi}</Typography></Stack>\n                <Stack direction=\"row\" justifyContent=\"space-between\"><Typography>Cavi Posati:</Typography><Typography sx={{ fontWeight: 600, color: 'success.main' }}>{data.cavi_posati} ({data.percentuale_cavi}%)</Typography></Stack>\n                <Stack direction=\"row\" justifyContent=\"space-between\"><Typography>Cavi Rimanenti:</Typography><Typography sx={{ fontWeight: 600, color: 'warning.main' }}>{data.cavi_rimanenti} ({(100 - data.percentuale_cavi).toFixed(2)}%)</Typography></Stack>\n              </Stack>\n            </Paper>\n          </Grid>\n          <Grid item xs={12} md={6}>\n            <Paper variant=\"outlined\" sx={{ p: 2 }}>\n              <Typography variant=\"h6\" sx={{ mb: 2 }}>Performance e Previsioni</Typography>\n               <Stack spacing={1}>\n                <Stack direction=\"row\" justifyContent=\"space-between\"><Typography>Media Giornaliera:</Typography><Typography sx={{ fontWeight: 600 }}>{data.media_giornaliera}m/giorno</Typography></Stack>\n                {data.giorni_stimati && (\n                  <>\n                    <Stack direction=\"row\" justifyContent=\"space-between\"><Typography>Giorni Stimati:</Typography><Typography sx={{ fontWeight: 600 }}>{data.giorni_stimati} giorni</Typography></Stack>\n                    <Stack direction=\"row\" justifyContent=\"space-between\"><Typography>Data Completamento:</Typography><Typography sx={{ fontWeight: 600 }}>{data.data_completamento}</Typography></Stack>\n                  </>\n                )}\n              </Stack>\n            </Paper>\n          </Grid>\n          {data.posa_recente && data.posa_recente.length > 0 && (\n            <Grid item xs={12}>\n              <Paper variant=\"outlined\" sx={{ p: 2 }}>\n                <Typography variant=\"h6\" sx={{ mb: 2 }}>Attività Recente</Typography>\n                <FilterableTable\n                  data={data.posa_recente.map(posa => ({ data: posa.data, metri: `${posa.metri}m` }))}\n                  columns={[\n                    { field: 'data', headerName: 'Data', width: 200 },\n                    { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }\n                  ]}\n                  pagination={data.posa_recente.length > 6}\n                  pageSize={6}\n                />\n              </Paper>\n            </Grid>\n          )}\n        </Grid>\n      </Paper>\n    );\n  };\n\n  const BoqReportView = () => {\n    const data = reportsData.boq;\n    if (loadingGlobal && !data) return <Box sx={{textAlign: 'center', p:3}}><CircularProgress /></Box>;\n    if (!data) return <Alert severity=\"info\">Dati Bill of Quantities non disponibili.</Alert>;\n\n    return (\n      <Paper sx={{ p: 3 }} elevation={0}>\n        {renderReportHeader(`Bill of Quantities - ${data.nome_cantiere || 'Cantiere'}`, 'boq')}\n        <Divider sx={{ my: 2 }} />\n        {showCharts && data && (\n          <Box sx={{ mb: 3, p:2, border: '1px solid #eee', borderRadius:1 }}>\n             <Typography variant=\"h6\" gutterBottom>Grafico BOQ</Typography>\n            <BoqChart data={data} />\n          </Box>\n        )}\n        <Paper variant=\"outlined\" sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"h6\" sx={{ mb: 2 }}>Cavi per Tipologia</Typography>\n          <FilterableTable\n            data={data.cavi_per_tipo || []}\n            columns={[ /* Same as your original */\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'sezione', headerName: 'Sezione', width: 100 },\n                { field: 'num_cavi', headerName: '#Cavi', width: 80, align: 'right', dataType: 'number' },\n                { field: 'metri_teorici', headerName: 'Teorici', width: 120, align: 'right', renderCell: (row) => `${row.metri_teorici}m` },\n                { field: 'metri_reali', headerName: 'Reali', width: 120, align: 'right', renderCell: (row) => `${row.metri_reali}m` },\n                { field: 'metri_da_posare', headerName: 'Da Posare', width: 120, align: 'right', renderCell: (row) => `${row.metri_da_posare}m` }\n            ]}\n            pageSize={10}\n          />\n        </Paper>\n        <Paper variant=\"outlined\" sx={{ p: 2 }}>\n          <Typography variant=\"h6\" sx={{ mb: 2 }}>Bobine Disponibili</Typography>\n          <FilterableTable\n            data={data.bobine_per_tipo || []}\n            columns={[ /* Same as your original */\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'sezione', headerName: 'Sezione', width: 100 },\n                { field: 'num_bobine', headerName: '#Bobine', width: 100, align: 'right', dataType: 'number' },\n                { field: 'metri_disponibili', headerName: 'Metri Disp.', width: 150, align: 'right', renderCell: (row) => `${row.metri_disponibili}m` }\n            ]}\n            pageSize={10}\n          />\n        </Paper>\n      </Paper>\n    );\n  };\n\n  const BobineReportView = () => {\n    const data = reportsData.bobine;\n    if (loadingGlobal && !data) return <Box sx={{textAlign: 'center', p:3}}><CircularProgress /></Box>;\n    if (!data) return <Alert severity=\"info\">Dati Utilizzo Bobine non disponibili.</Alert>;\n\n    return (\n      <Paper sx={{ p: 3 }} elevation={0}>\n        {renderReportHeader(`Report Utilizzo Bobine (${data.totale_bobine || 0} totali) - ${data.nome_cantiere || 'Cantiere'}`, 'bobine')}\n        <Divider sx={{ my: 2 }} />\n        {showCharts && data && (\n          <Box sx={{ mb: 3, p:2, border: '1px solid #eee', borderRadius:1 }}>\n            <Typography variant=\"h6\" gutterBottom>Grafico Utilizzo Bobine</Typography>\n            <BobineChart data={data} />\n          </Box>\n        )}\n        <Paper variant=\"outlined\" sx={{ p: 2 }}>\n          <Typography variant=\"h6\" sx={{ mb: 2 }}>Dettaglio Bobine</Typography>\n          <FilterableTable\n            data={data.bobine || []}\n            columns={[ /* Same as your original */\n                { field: 'id_bobina', headerName: 'ID Bobina', width: 120 },\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'sezione', headerName: 'Sezione', width: 100 },\n                { field: 'stato', headerName: 'Stato', width: 120, renderCell: (row) => <Chip label={row.stato} color={row.stato === 'DISPONIBILE' ? 'success' : 'warning'} size=\"small\"/> },\n                { field: 'metri_totali', headerName: 'Totali', width: 120, align: 'right', renderCell: (row) => `${row.metri_totali}m` },\n                { field: 'metri_residui', headerName: 'Residui', width: 120, align: 'right', renderCell: (row) => `${row.metri_residui}m` },\n                { field: 'metri_utilizzati', headerName: 'Utilizzati', width: 140, align: 'right', renderCell: (row) => `${row.metri_utilizzati}m` },\n                { field: 'percentuale_utilizzo', headerName: '% Uso', width: 100, align: 'right', renderCell: (row) => `${row.percentuale_utilizzo}%` }\n            ]}\n            pageSize={10}\n          />\n        </Paper>\n      </Paper>\n    );\n  };\n\n  const CaviStatoReportView = () => {\n    const data = reportsData.caviStato;\n    if (loadingGlobal && !data) return <Box sx={{textAlign: 'center', p:3}}><CircularProgress /></Box>;\n    if (!data) return <Alert severity=\"info\">Dati Cavi per Stato non disponibili.</Alert>;\n\n    return (\n      <Paper sx={{ p: 3 }} elevation={0}>\n        {renderReportHeader(`Report Cavi per Stato - ${data.nome_cantiere || 'Cantiere'}`, 'cavi-stato')}\n        <Divider sx={{ my: 2 }} />\n        {showCharts && data && (\n          <Box sx={{ mb: 3, p:2, border: '1px solid #eee', borderRadius:1 }}>\n            <Typography variant=\"h6\" gutterBottom>Grafico Cavi per Stato</Typography>\n            <CaviStatoChart data={data} />\n          </Box>\n        )}\n        <Paper variant=\"outlined\" sx={{ p: 2 }}>\n          <Typography variant=\"h6\" sx={{ mb: 2 }}>Distribuzione Cavi</Typography>\n          <FilterableTable\n            data={data.cavi_per_stato || []}\n            columns={[ /* Same as your original */\n                { field: 'stato', headerName: 'Stato', width: 150, renderCell: (row) => <Chip label={row.stato} color={row.stato === 'Installato' ? 'success' : (row.stato === 'Da Posare' ? 'default' : 'warning')} size=\"small\"/>},\n                { field: 'num_cavi', headerName: '#Cavi', width: 120, align: 'right', dataType: 'number' },\n                { field: 'metri_teorici', headerName: 'Metri Teorici', width: 150, align: 'right', renderCell: (row) => `${row.metri_teorici}m` },\n                { field: 'metri_reali', headerName: 'Metri Reali', width: 150, align: 'right', renderCell: (row) => `${row.metri_reali}m` }\n            ]}\n            pagination={false}\n          />\n        </Paper>\n      </Paper>\n    );\n  };\n\n  const BobinaSpecificaReportView = () => {\n    const data = reportsData.bobinaSpecifica;\n\n    return (\n      <Paper sx={{ p: 3 }} elevation={0}>\n        {renderReportHeader(\"Report Bobina Specifica\", 'bobina-specifica')}\n        <Divider sx={{ my: 2 }} />\n        <Stack component=\"form\" onSubmit={(e) => { e.preventDefault(); generateReportWithFormat('bobina-specifica', 'video'); }} spacing={2} direction={{ xs: 'column', sm: 'row' }} sx={{ mb: 2 }}>\n          <TextField\n            name=\"id_bobina\"\n            label=\"ID Bobina\"\n            value={formData.id_bobina}\n            onChange={handleInputChange}\n            size=\"small\"\n            helperText=\"Es: 1 per C1_B1\"\n            sx={{ flexGrow: 1 }}\n          />\n          <Button type=\"submit\" variant=\"contained\" disabled={loadingReport || !formData.id_bobina} startIcon={loadingReport ? <CircularProgress size={20} /> : <AssessmentIcon />}>\n            Visualizza Dati Bobina\n          </Button>\n        </Stack>\n        {error && selectedReportType === 'bobina-specifica' && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\n\n        {loadingReport && selectedReportType === 'bobina-specifica' && !data && <Box sx={{textAlign: 'center', p:3}}><CircularProgress /></Box>}\n\n        {data && (\n          <>\n            <Typography variant=\"h6\" sx={{ mt: 2, mb:1, fontWeight: 500 }}>Dati Bobina: {data.bobina?.id_bobina}</Typography>\n            <Grid container spacing={3}>\n              <Grid item xs={12} md={6}>\n                <Paper variant=\"outlined\" sx={{ p: 2 }}>\n                  <Typography variant=\"subtitle1\" sx={{ mb: 1, fontWeight: 'medium' }}>Dettagli</Typography>\n                  {data.bobina && <Stack spacing={0.5}>\n                    <Stack direction=\"row\" justifyContent=\"space-between\"><Typography variant=\"body2\">ID:</Typography><Typography variant=\"body2\" sx={{ fontWeight: 500 }}>{data.bobina.id_bobina}</Typography></Stack>\n                    <Stack direction=\"row\" justifyContent=\"space-between\"><Typography variant=\"body2\">Tipologia:</Typography><Typography variant=\"body2\" sx={{ fontWeight: 500 }}>{data.bobina.tipologia}</Typography></Stack>\n                    <Stack direction=\"row\" justifyContent=\"space-between\"><Typography variant=\"body2\">Sezione:</Typography><Typography variant=\"body2\" sx={{ fontWeight: 500 }}>{data.bobina.sezione}</Typography></Stack>\n                    <Stack direction=\"row\" justifyContent=\"space-between\"><Typography variant=\"body2\">Stato:</Typography><Chip label={data.bobina.stato} color={data.bobina.stato === 'DISPONIBILE' ? 'success' : 'warning'} size=\"small\"/></Stack>\n                  </Stack>}\n                </Paper>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Paper variant=\"outlined\" sx={{ p: 2 }}>\n                  <Typography variant=\"subtitle1\" sx={{ mb: 1, fontWeight: 'medium' }}>Metriche Utilizzo</Typography>\n                  {data.bobina && <Stack spacing={0.5}>\n                     <Stack direction=\"row\" justifyContent=\"space-between\"><Typography variant=\"body2\">Metri Totali:</Typography><Typography variant=\"body2\" sx={{ fontWeight: 500 }}>{data.bobina.metri_totali}m</Typography></Stack>\n                     <Stack direction=\"row\" justifyContent=\"space-between\"><Typography variant=\"body2\">Metri Utilizzati:</Typography><Typography variant=\"body2\" sx={{ fontWeight: 500, color: 'success.dark' }}>{data.bobina.metri_utilizzati}m</Typography></Stack>\n                     <Stack direction=\"row\" justifyContent=\"space-between\"><Typography variant=\"body2\">Metri Residui:</Typography><Typography variant=\"body2\" sx={{ fontWeight: 500, color: 'warning.dark' }}>{data.bobina.metri_residui}m</Typography></Stack>\n                     <Stack direction=\"row\" justifyContent=\"space-between\"><Typography variant=\"body2\">% Utilizzo:</Typography><Typography variant=\"body2\" sx={{ fontWeight: 500 }}>{data.bobina.percentuale_utilizzo}%</Typography></Stack>\n                  </Stack>}\n                </Paper>\n              </Grid>\n              <Grid item xs={12}>\n                <Paper variant=\"outlined\" sx={{ p: 2 }}>\n                  <Typography variant=\"subtitle1\" sx={{ mb: 1, fontWeight: 'medium' }}>Cavi Associati ({data.totale_cavi || 0})</Typography>\n                  <FilterableTable\n                    data={data.cavi_associati || []}\n                    columns={[ /* Same as your original */\n                        { field: 'id_cavo', headerName: 'ID Cavo', width: 120 },\n                        { field: 'sistema', headerName: 'Sistema', width: 120 },\n                        { field: 'utility', headerName: 'Utility', width: 120 },\n                        { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                        { field: 'metri_teorici', headerName: 'Teorici', width: 120, align: 'right', renderCell: (row) => `${row.metri_teorici}m` },\n                        { field: 'metri_reali', headerName: 'Reali', width: 120, align: 'right', renderCell: (row) => `${row.metri_reali}m` },\n                        { field: 'stato', headerName: 'Stato', width: 120, renderCell: (row) => <Chip label={row.stato} color={row.stato === 'POSATO' || row.stato === 'Installato' ? 'success' : 'default'} size=\"small\"/> }\n                    ]}\n                    pageSize={5}\n                  />\n                </Paper>\n              </Grid>\n            </Grid>\n          </>\n        )}\n      </Paper>\n    );\n  };\n\n  const PosaPeriodoReportView = () => {\n    const data = reportsData.posaPeriodo;\n    const today = new Date().toISOString().split('T')[0];\n    const lastMonthDate = new Date();\n    lastMonthDate.setMonth(lastMonthDate.getMonth() - 1);\n    const lastMonth = lastMonthDate.toISOString().split('T')[0];\n\n    return (\n      <Paper sx={{ p: 3 }} elevation={0}>\n        {renderReportHeader(\"Report Posa per Periodo\", 'posa-periodo')}\n        <Divider sx={{ my: 2 }} />\n        <Stack component=\"form\" onSubmit={(e) => { e.preventDefault(); generateReportWithFormat('posa-periodo', 'video'); }} spacing={2} direction={{ xs: 'column', sm: 'row' }} sx={{ mb: 2 }} alignItems=\"flex-start\">\n          <TextField\n            name=\"data_inizio\"\n            label=\"Data Inizio\"\n            type=\"date\"\n            value={formData.data_inizio || lastMonth}\n            onChange={handleInputChange}\n            InputLabelProps={{ shrink: true }}\n            size=\"small\"\n            sx={{ flexGrow: 1 }}\n          />\n          <TextField\n            name=\"data_fine\"\n            label=\"Data Fine\"\n            type=\"date\"\n            value={formData.data_fine || today}\n            onChange={handleInputChange}\n            InputLabelProps={{ shrink: true }}\n            size=\"small\"\n            sx={{ flexGrow: 1 }}\n          />\n          <Button type=\"submit\" variant=\"contained\" disabled={loadingReport || !formData.data_inizio || !formData.data_fine} startIcon={loadingReport ? <CircularProgress size={20} /> : <AssessmentIcon />}>\n            Visualizza Dati Periodo\n          </Button>\n        </Stack>\n        {error && selectedReportType === 'posa-periodo' && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\n\n        {loadingReport && selectedReportType === 'posa-periodo' && !data && <Box sx={{textAlign: 'center', p:3}}><CircularProgress /></Box>}\n\n        {data && (\n          <>\n            <Typography variant=\"h6\" sx={{ mt:2, mb:1, fontWeight:500 }}>Dati per il periodo: {data.data_inizio} - {data.data_fine}</Typography>\n            <Grid container spacing={2} sx={{ mb: 3 }}>\n                {renderKpiCard(\"Metri Totali Periodo\", `${data.totale_metri_periodo || 0}m`, null, <AssessmentIcon />, 'warning.main')}\n                {renderKpiCard(\"Giorni Attivi\", data.giorni_attivi || 0, null, <TimelineIcon />, 'info.main')}\n                {renderKpiCard(\"Media/Giorno\", `${data.media_giornaliera || 0}m`, null, <ShowChartIcon />, 'success.main')}\n                {renderKpiCard(\"Media/Settimana (Stimata)\", `${data.giorni_attivi ? Math.round((data.totale_metri_periodo || 0) / data.giorni_attivi * 7) : 0}m`, null, <BarChartIcon />, 'primary.main')}\n            </Grid>\n            {showCharts && (\n              <Box sx={{ mb: 3, p:2, border: '1px solid #eee', borderRadius:1 }}>\n                 <Typography variant=\"h6\" gutterBottom>Grafico Posa nel Periodo</Typography>\n                <TimelineChart data={data} />\n              </Box>\n            )}\n            <Paper variant=\"outlined\" sx={{ p: 2 }}>\n              <Typography variant=\"h6\" sx={{ mb: 2 }}>Dettaglio Posa Giornaliera</Typography>\n              <FilterableTable\n                data={data.posa_giornaliera || []}\n                columns={[ /* Same as your original */\n                    { field: 'data', headerName: 'Data', width: 200 },\n                    { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', renderCell: (row) => `${row.metri}m` }\n                ]}\n                pageSize={10}\n              />\n            </Paper>\n          </>\n        )}\n      </Paper>\n    );\n  };\n\n  // --- Main Render ---\n  return (\n    <Box sx={{ display: 'flex', minHeight: '100vh', bgcolor: 'grey.100' }}>\n      <CssBaseline />\n      <AppBar position=\"fixed\" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1, backgroundColor: 'primary.dark' }}>\n        <Toolbar>\n          <IconButton\n            color=\"inherit\"\n            aria-label=\"open drawer\"\n            edge=\"start\"\n            onClick={() => setDrawerOpen(!drawerOpen)}\n            sx={{ mr: 2, display: { sm: 'none' } }} // Example for mobile toggle\n          >\n            <MenuIcon />\n          </IconButton>\n          <Typography variant=\"h6\" noWrap component=\"div\" sx={{ flexGrow: 1 }}>\n            Reportistica Cantiere {cantiereId && `- ID ${cantiereId}`}\n          </Typography>\n          <Stack direction=\"row\" spacing={1}>\n            <Button color=\"inherit\" onClick={() => navigate(-1)} startIcon={<ArrowBackIcon />}>Indietro</Button>\n            <AdminHomeButton color=\"inherit\"/>\n          </Stack>\n        </Toolbar>\n      </AppBar>\n\n      <Drawer\n        variant=\"permanent\" // \"temporary\" for mobile with drawerOpen state\n        open={drawerOpen}\n        sx={{\n          width: DRAWER_WIDTH,\n          flexShrink: 0,\n          [`& .MuiDrawer-paper`]: { width: DRAWER_WIDTH, boxSizing: 'border-box', top: '64px', height: 'calc(100% - 64px)' }, // Adjust top for AppBar\n        }}\n      >\n        <List sx={{ pt: 2 }}> {/* Add padding top if Toolbar is not used inside Drawer */}\n          {reportTypes.map((report) => (\n            <ListItemButton\n              key={report.id}\n              selected={selectedReportType === report.id}\n              onClick={() => {\n                  setSelectedReportType(report.id);\n                  setError(null); // Clear previous errors when changing report type\n                  // Reset form data for on-demand reports if switching to them\n                  if (report.id === 'bobina-specifica' || report.id === 'posa-periodo') {\n                    // Only reset if not already viewing that report type, or data may clear during input\n                    if(selectedReportType !== report.id) {\n                        setFormData({ id_bobina: '', data_inizio: '', data_fine: ''});\n                        // Clear previous data for these reports to force re-fetch with new params\n                        setReportsData(prev => ({...prev, bobinaSpecifica: null, posaPeriodo: null}));\n                    }\n                  } else if (!reportsData[report.id] && !loadingGlobal) { // If data not preloaded and not currently loading all\n                      generateReportWithFormat(report.id, 'video');\n                  }\n              }}\n            >\n              <ListItemIcon sx={{color: `${report.color}.main`}}>{report.icon}</ListItemIcon>\n              <ListItemText primary={report.title} />\n            </ListItemButton>\n          ))}\n        </List>\n      </Drawer>\n\n      <Box component=\"main\" sx={{ flexGrow: 1, p: 3, mt: '64px' }}> {/* mt to offset AppBar */}\n        {loadingGlobal && !Object.values(reportsData).some(d => d) && ( // Show global loader only if no data at all is loaded yet\n            <Box sx={{ display: 'flex', flexDirection:'column', alignItems: 'center', justifyContent: 'center', height: '50vh' }}>\n                <CircularProgress />\n                <Typography sx={{mt:2}}>Caricamento report iniziali...</Typography>\n            </Box>\n        )}\n        {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\n\n        {!loadingGlobal && (\n            <>\n            {selectedReportType === 'progress' && <ProgressReportView />}\n            {selectedReportType === 'boq' && <BoqReportView />}\n            {selectedReportType === 'bobine' && <BobineReportView />}\n            {selectedReportType === 'cavi-stato' && <CaviStatoReportView />}\n            {selectedReportType === 'bobina-specifica' && <BobinaSpecificaReportView />}\n            {selectedReportType === 'posa-periodo' && <PosaPeriodoReportView />}\n            </>\n        )}\n      </Box>\n    </Box>\n  );\n};\n\nexport default ReportCaviPageProfessional;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,IAAI,EACJC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,MAAM,EACNC,OAAO,EACPC,WAAW,EACXC,KAAK,CAAE;AAAA,OACF,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBX,IAAI,IAAIY,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW;AAAE;AACxBC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,QAAQ,IAAIC,YAAY;AAAE;AAC1BC,SAAS,IAAIC,aAAa,EAC1BC,IAAI,IAAIC,QAAQ,CAAE;AAAA,OACb,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,OAAO,QAAQ,2BAA2B,CAAC,CAAC;AACrD,OAAOC,eAAe,MAAM,yCAAyC,CAAC,CAAC;AACvE,OAAOC,aAAa,MAAM,8BAA8B,CAAC,CAAC;AAC1D,OAAOC,eAAe,MAAM,yCAAyC,CAAC,CAAC;;AAEvE;AACA,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,cAAc,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpE,MAAMC,YAAY,GAAG,GAAG;AAExB,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmB;EAAW,CAAC,GAAGlB,SAAS,CAAC,CAAC;EAClC;;EAEA,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACwE,aAAa,EAAEC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EAC3D,MAAM,CAAC0E,KAAK,EAAEC,QAAQ,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM,CAAC4E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7E,QAAQ,CAAC,UAAU,CAAC;EACxE,MAAM,CAAC8E,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAC;IAC7CgF,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,IAAI;IAAE;IACvBC,WAAW,EAAE,IAAI,CAAK;EACxB,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvF,QAAQ,CAAC;IACvCwF,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC6F,UAAU,EAAEC,aAAa,CAAC,GAAG9F,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEpD,MAAM+F,WAAW,GAAG,CAClB;IAAEC,EAAE,EAAE,UAAU;IAAEC,KAAK,EAAE,oBAAoB;IAAEC,IAAI,eAAEpC,OAAA,CAAChC,cAAc;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC3F;IAAEP,EAAE,EAAE,KAAK;IAAEC,KAAK,EAAE,oBAAoB;IAAEC,IAAI,eAAEpC,OAAA,CAAC7B,QAAQ;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAY,CAAC,EAClF;IAAEP,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,wBAAwB;IAAEC,IAAI,eAAEpC,OAAA,CAACnB,aAAa;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC5F;IAAEP,EAAE,EAAE,YAAY;IAAEC,KAAK,EAAE,uBAAuB;IAAEC,IAAI,eAAEpC,OAAA,CAAC9B,YAAY;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAC5F;IAAEP,EAAE,EAAE,kBAAkB;IAAEC,KAAK,EAAE,yBAAyB;IAAEC,IAAI,eAAEpC,OAAA,CAACrB,SAAS;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAO,CAAC,EAChG;IAAEP,EAAE,EAAE,cAAc;IAAEC,KAAK,EAAE,yBAAyB;IAAEC,IAAI,eAAEpC,OAAA,CAACjB,YAAY;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAU,CAAC,CACnG;EAED,MAAMC,kBAAkB,GAAGtG,WAAW,CAAC,YAAY;IACjD,IAAI,CAACmE,UAAU,EAAE;IACjBE,gBAAgB,CAAC,IAAI,CAAC;IACtBI,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF,MAAM8B,cAAc,GAAG;QACrBzB,QAAQ,EAAE1B,aAAa,CAACoD,iBAAiB,CAACrC,UAAU,EAAE,OAAO,CAAC,CAACsC,KAAK,CAACC,GAAG,KAAK;UAAElC,KAAK,EAAE,IAAI;UAAEmC,IAAI,EAAE,UAAU;UAAED;QAAI,CAAC,CAAC,CAAC;QACrH3B,GAAG,EAAE3B,aAAa,CAACwD,mBAAmB,CAACzC,UAAU,EAAE,OAAO,CAAC,CAACsC,KAAK,CAACC,GAAG,KAAK;UAAElC,KAAK,EAAE,IAAI;UAAEmC,IAAI,EAAE,KAAK;UAAED;QAAI,CAAC,CAAC,CAAC;QAC7G1B,MAAM,EAAE5B,aAAa,CAACyD,eAAe,CAAC1C,UAAU,EAAE,OAAO,CAAC,CAACsC,KAAK,CAACC,GAAG,KAAK;UAAElC,KAAK,EAAE,IAAI;UAAEmC,IAAI,EAAE,QAAQ;UAAED;QAAI,CAAC,CAAC,CAAC;QAC/GzB,SAAS,EAAE7B,aAAa,CAAC0D,kBAAkB,CAAC3C,UAAU,EAAE,OAAO,CAAC,CAACsC,KAAK,CAACC,GAAG,KAAK;UAAElC,KAAK,EAAE,IAAI;UAAEmC,IAAI,EAAE,YAAY;UAAED;QAAI,CAAC,CAAC;MAC1H,CAAC;MAED,MAAMK,OAAO,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACC,MAAM,CAACC,MAAM,CAACZ,cAAc,CAAC,CAAC;MAEhE,MAAMa,cAAc,GAAG;QAAE,GAAGxC;MAAY,CAAC;MACzC,IAAIyC,QAAQ,GAAG,KAAK;MACpB,IAAIC,eAAe,GAAG,CAAC;MAEvBP,OAAO,CAACQ,OAAO,CAACC,GAAG,IAAI;QACrB,IAAIA,GAAG,IAAI,CAACA,GAAG,CAAChD,KAAK,IAAIgD,GAAG,CAACC,OAAO,EAAE;UACpC,MAAMd,IAAI,GAAGO,MAAM,CAACQ,IAAI,CAACnB,cAAc,CAAC,CAACoB,IAAI,CAACC,GAAG;YAAA,IAAAC,YAAA;YAAA,OAAItB,cAAc,CAACqB,GAAG,CAAC,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAAF,YAAA,GAAAL,GAAG,CAACC,OAAO,cAAAI,YAAA,uBAAXA,YAAA,CAAaG,aAAa,KAAI,KAAK,CAAC;UAAA,EAAC,CAAC,CAAC;UACpI;UACA,IAAIR,GAAG,CAACC,OAAO,IAAID,GAAG,CAACC,OAAO,CAACO,aAAa,EAAE;YAAE;YAC7C,IAAGjB,OAAO,CAAC,CAAC,CAAC,KAAKS,GAAG,EAAEJ,cAAc,CAACtC,QAAQ,GAAG0C,GAAG,CAACC,OAAO;YAC5D,IAAGV,OAAO,CAAC,CAAC,CAAC,KAAKS,GAAG,EAAEJ,cAAc,CAACrC,GAAG,GAAGyC,GAAG,CAACC,OAAO;YACvD,IAAGV,OAAO,CAAC,CAAC,CAAC,KAAKS,GAAG,EAAEJ,cAAc,CAACpC,MAAM,GAAGwC,GAAG,CAACC,OAAO;YAC1D,IAAGV,OAAO,CAAC,CAAC,CAAC,KAAKS,GAAG,EAAEJ,cAAc,CAACnC,SAAS,GAAGuC,GAAG,CAACC,OAAO;YAC7DH,eAAe,EAAE;UACpB;QACF,CAAC,MAAM,IAAIE,GAAG,IAAIA,GAAG,CAAChD,KAAK,EAAE;UAC3ByD,OAAO,CAACzD,KAAK,CAAC,iBAAiBgD,GAAG,CAACb,IAAI,UAAU,EAAEa,GAAG,CAACd,GAAG,CAAC;UAC3DW,QAAQ,GAAG,IAAI;QACjB;MACF,CAAC,CAAC;MAEFxC,cAAc,CAACuC,cAAc,CAAC;MAC9B,IAAIC,QAAQ,IAAIC,eAAe,KAAK,CAAC,EAAE;QACrC7C,QAAQ,CAAC,kGAAkG,CAAC;MAC9G;IAEF,CAAC,CAAC,OAAOiC,GAAG,EAAE;MACZuB,OAAO,CAACzD,KAAK,CAAC,2CAA2C,EAAEkC,GAAG,CAAC;MAC/DjC,QAAQ,CAAC,gEAAgE,CAAC;IAC5E,CAAC,SAAS;MACRJ,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC,EAAE,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElBpE,SAAS,CAAC,MAAM;IACduG,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAExB,MAAM4B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B9C,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAAC+C,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAAM,CAAC,CAAC;EAC/D,CAAC;EAED,MAAMC,wBAAwB,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,GAAG,CAAC,CAAC,KAAK;IACxEnE,gBAAgB,CAAC,IAAI,CAAC;IACtBE,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF,IAAIkE,QAAQ;MACZ,MAAMC,eAAe,GAAGF,MAAM,CAACpD,SAAS,IAAIF,QAAQ,CAACE,SAAS;MAC9D,MAAMuD,iBAAiB,GAAGH,MAAM,CAACnD,WAAW,IAAIH,QAAQ,CAACG,WAAW;MACpE,MAAMuD,eAAe,GAAGJ,MAAM,CAAClD,SAAS,IAAIJ,QAAQ,CAACI,SAAS;MAE9D,QAAQgD,QAAQ;QACd,KAAK,UAAU;UAAEG,QAAQ,GAAG,MAAMvF,aAAa,CAACoD,iBAAiB,CAACrC,UAAU,EAAEsE,MAAM,CAAC;UAAE;QACvF,KAAK,KAAK;UAAEE,QAAQ,GAAG,MAAMvF,aAAa,CAACwD,mBAAmB,CAACzC,UAAU,EAAEsE,MAAM,CAAC;UAAE;QACpF,KAAK,QAAQ;UAAEE,QAAQ,GAAG,MAAMvF,aAAa,CAACyD,eAAe,CAAC1C,UAAU,EAAEsE,MAAM,CAAC;UAAE;QACnF,KAAK,YAAY;UAAEE,QAAQ,GAAG,MAAMvF,aAAa,CAAC0D,kBAAkB,CAAC3C,UAAU,EAAEsE,MAAM,CAAC;UAAE;QAC1F,KAAK,kBAAkB;UACrB,IAAI,CAACG,eAAe,EAAE;YAAEnE,QAAQ,CAAC,wBAAwB,CAAC;YAAEF,gBAAgB,CAAC,KAAK,CAAC;YAAE;UAAQ;UAC7FoE,QAAQ,GAAG,MAAMvF,aAAa,CAAC2F,eAAe,CAAC5E,UAAU,EAAEyE,eAAe,EAAEH,MAAM,CAAC;UACnF;QACF,KAAK,cAAc;UACjB,IAAI,CAACI,iBAAiB,IAAI,CAACC,eAAe,EAAE;YAAErE,QAAQ,CAAC,uCAAuC,CAAC;YAAEF,gBAAgB,CAAC,KAAK,CAAC;YAAE;UAAQ;UAClIoE,QAAQ,GAAG,MAAMvF,aAAa,CAAC4F,uBAAuB,CAAC7E,UAAU,EAAE0E,iBAAiB,EAAEC,eAAe,EAAEL,MAAM,CAAC;UAC9G;QACF;UAAS,MAAM,IAAIQ,KAAK,CAAC,iCAAiC,CAAC;MAC7D;MAEA,IAAIR,MAAM,KAAK,OAAO,EAAE;QACtB5D,cAAc,CAACqE,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACV,QAAQ,GAAGG,QAAQ,CAAClB;QAAQ,CAAC,CAAC,CAAC;MACrE,CAAC,MAAM,IAAIkB,QAAQ,CAACQ,QAAQ,EAAE;QAC5BC,MAAM,CAACC,IAAI,CAACV,QAAQ,CAACQ,QAAQ,EAAE,QAAQ,CAAC;MAC1C,CAAC,MAAM;QACL1E,QAAQ,CAAC,4CAA4C,CAAC;MACxD;IACF,CAAC,CAAC,OAAOiC,GAAG,EAAE;MACZuB,OAAO,CAACzD,KAAK,CAAC,uCAAuCgE,QAAQ,GAAG,EAAE9B,GAAG,CAAC;MACtEjC,QAAQ,CAACiC,GAAG,CAAC4C,MAAM,IAAI5C,GAAG,CAAC6C,OAAO,IAAI,0CAA0C,CAAC;MACjF;MACA,IAAIf,QAAQ,KAAK,iBAAiB,IAAIA,QAAQ,KAAK,aAAa,EAAE;QAChE3D,cAAc,CAACqE,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACV,QAAQ,GAAG;QAAK,CAAC,CAAC,CAAC;MACzD;IACF,CAAC,SAAS;MACRjE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMiF,kBAAkB,GAAGA,CAACzD,KAAK,EAAE0D,iBAAiB,kBAClD7F,OAAA,CAAClC,KAAK;IAACgI,SAAS,EAAC,KAAK;IAACC,cAAc,EAAC,eAAe;IAACC,UAAU,EAAC,QAAQ;IAACC,OAAO,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAClGpG,OAAA,CAAC1D,UAAU;MAAC+J,OAAO,EAAC,IAAI;MAACC,SAAS,EAAC,IAAI;MAACJ,EAAE,EAAE;QAAEK,UAAU,EAAE;MAAI,CAAE;MAAAH,QAAA,EAC7DjE;IAAK;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACbxC,OAAA,CAAClC,KAAK;MAACgI,SAAS,EAAC,KAAK;MAACG,OAAO,EAAE,CAAE;MAACD,UAAU,EAAC,QAAQ;MAAAI,QAAA,gBACpDpG,OAAA,CAACvD,MAAM;QACL+J,SAAS,eAAExG,OAAA,CAAC3B,YAAY;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC5BiE,OAAO,EAAEA,CAAA,KAAM9B,wBAAwB,CAACkB,iBAAiB,EAAE,KAAK,CAAE;QAClEQ,OAAO,EAAC,UAAU;QAClBK,IAAI,EAAC,OAAO;QACZC,QAAQ,EAAEjG,aAAc;QAAA0F,QAAA,EACzB;MAED;QAAA/D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxC,OAAA,CAACvD,MAAM;QACL+J,SAAS,eAAExG,OAAA,CAAC3B,YAAY;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC5BiE,OAAO,EAAEA,CAAA,KAAM9B,wBAAwB,CAACkB,iBAAiB,EAAE,OAAO,CAAE;QACpEQ,OAAO,EAAC,UAAU;QAClBK,IAAI,EAAC,OAAO;QACZjE,KAAK,EAAC,SAAS;QACfkE,QAAQ,EAAEjG,aAAc;QAAA0F,QAAA,EACzB;MAED;QAAA/D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxC,OAAA,CAAC3C,gBAAgB;QACfuJ,OAAO,eACL5G,OAAA,CAAC5C,MAAM;UACLyJ,OAAO,EAAEhF,UAAW;UACpBiF,QAAQ,EAAGvC,CAAC,IAAKzC,aAAa,CAACyC,CAAC,CAACC,MAAM,CAACqC,OAAO,CAAE;UACjDpE,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDuE,cAAc,EAAC,OAAO;QACtBC,KAAK,eACHhH,OAAA,CAAC1D,UAAU;UAAC+J,OAAO,EAAC,SAAS;UAACH,EAAE,EAAE;YAAEe,OAAO,EAAE,MAAM;YAAEjB,UAAU,EAAE;UAAS,CAAE;UAAAI,QAAA,gBAC1EpG,OAAA,CAACf,aAAa;YAACiI,QAAQ,EAAC,OAAO;YAAChB,EAAE,EAAE;cAAEiB,EAAE,EAAE;YAAI;UAAE;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YACrD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACR;EAED,MAAM4E,aAAa,GAAGA,CAACjF,KAAK,EAAEuC,KAAK,EAAE2C,OAAO,EAAEjF,IAAI,EAAEK,KAAK,GAAG,gBAAgB,kBACxEzC,OAAA,CAACxD,IAAI;IAAC8K,IAAI;IAACC,EAAE,EAAE,EAAG;IAACC,EAAE,EAAE,CAAE;IAACC,EAAE,EAAE,CAAE;IAAArB,QAAA,eAC5BpG,OAAA,CAACzD,KAAK;MAAC8J,OAAO,EAAC,UAAU;MAACH,EAAE,EAAE;QAAEwB,CAAC,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAvB,QAAA,eACnDpG,OAAA,CAAClC,KAAK;QAACmI,OAAO,EAAE,CAAE;QAAAG,QAAA,gBACdpG,OAAA,CAAClC,KAAK;UAACgI,SAAS,EAAC,KAAK;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,YAAY;UAAAI,QAAA,gBACzEpG,OAAA,CAAC1D,UAAU;YAAC+J,OAAO,EAAC,WAAW;YAAC5D,KAAK,EAAC,gBAAgB;YAACyD,EAAE,EAAE;cAAEK,UAAU,EAAE;YAAI,CAAE;YAAAH,QAAA,EAC1EjE;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EACZJ,IAAI,iBAAInG,KAAK,CAAC2L,YAAY,CAACxF,IAAI,EAAE;YAAE8D,EAAE,EAAE;cAAEzD,KAAK,EAAEA;YAAM;UAAC,CAAC,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACRxC,OAAA,CAAC1D,UAAU;UAAC+J,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEK,UAAU,EAAE,MAAM;YAAE9D,KAAK,EAAEA;UAAM,CAAE;UAAA2D,QAAA,EAC7D1B;QAAK;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EACZ6E,OAAO,iBAAIrH,OAAA,CAAC1D,UAAU;UAAC+J,OAAO,EAAC,SAAS;UAAC5D,KAAK,EAAC,gBAAgB;UAAA2D,QAAA,EAAEiB;QAAO;UAAAhF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CACT;;EAED;;EAEA,MAAMqF,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,IAAI,GAAG9G,WAAW,CAACE,QAAQ;IACjC,IAAIV,aAAa,IAAI,CAACsH,IAAI,EAAE,oBAAO9H,OAAA,CAAC3D,GAAG;MAAC6J,EAAE,EAAE;QAAC6B,SAAS,EAAE,QAAQ;QAAEL,CAAC,EAAC;MAAC,CAAE;MAAAtB,QAAA,eAACpG,OAAA,CAACpD,gBAAgB;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;IAClG,IAAI,CAACsF,IAAI,EAAE,oBAAO9H,OAAA,CAACrD,KAAK;MAACqL,QAAQ,EAAC,MAAM;MAAA5B,QAAA,EAAC;IAA4C;MAAA/D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;IAE7F,oBACExC,OAAA,CAACzD,KAAK;MAAC2J,EAAE,EAAE;QAAEwB,CAAC,EAAE;MAAE,CAAE;MAACO,SAAS,EAAE,CAAE;MAAA7B,QAAA,GAC/BR,kBAAkB,CAAC,wBAAwBkC,IAAI,CAAC1D,aAAa,IAAI,UAAU,EAAE,EAAE,UAAU,CAAC,eAC3FpE,OAAA,CAACnD,OAAO;QAACqJ,EAAE,EAAE;UAAEgC,EAAE,EAAE;QAAE;MAAE;QAAA7F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1BxC,OAAA,CAACxD,IAAI;QAAC2L,SAAS;QAAClC,OAAO,EAAE,CAAE;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,GACvCgB,aAAa,CAAC,cAAc,EAAE,GAAGU,IAAI,CAACM,YAAY,IAAI,CAAC,GAAG,EAAE,IAAI,eAAEpI,OAAA,CAACrB,SAAS;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAAE,cAAc,CAAC,EAChG4E,aAAa,CAAC,cAAc,EAAE,GAAGU,IAAI,CAACO,YAAY,IAAI,CAAC,GAAG,EAAE,IAAIP,IAAI,CAACQ,uBAAuB,IAAI,CAAC,IAAI,eAAEtI,OAAA,CAACjB,YAAY;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAAE,cAAc,CAAC,EACxI4E,aAAa,CAAC,iBAAiB,EAAE,GAAGU,IAAI,CAACS,eAAe,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAIT,IAAI,CAACQ,uBAAuB,IAAI,CAAC,CAAC,EAAEE,OAAO,CAAC,CAAC,CAAC,IAAI,eAAExI,OAAA,CAAChC,cAAc;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAAE,cAAc,CAAC,EACrK4E,aAAa,CAAC,cAAc,EAAE,GAAGU,IAAI,CAACW,iBAAiB,IAAI,CAAC,GAAG,EAAEX,IAAI,CAACY,cAAc,GAAG,IAAIZ,IAAI,CAACY,cAAc,kBAAkB,GAAG,IAAI,eAAE1I,OAAA,CAACf,aAAa;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAAE,WAAW,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrK,CAAC,EAENX,UAAU,IAAIiG,IAAI,iBACjB9H,OAAA,CAAC3D,GAAG;QAAC6J,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEuB,CAAC,EAAC,CAAC;UAAEiB,MAAM,EAAE,gBAAgB;UAAEC,YAAY,EAAC;QAAE,CAAE;QAAAxC,QAAA,gBAChEpG,OAAA,CAAC1D,UAAU;UAAC+J,OAAO,EAAC,IAAI;UAACwC,YAAY;UAAAzC,QAAA,EAAC;QAAmB;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACtExC,OAAA,CAACN,aAAa;UAACoI,IAAI,EAAEA;QAAK;UAAAzF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CACN,eAEDxC,OAAA,CAACxD,IAAI;QAAC2L,SAAS;QAAClC,OAAO,EAAE,CAAE;QAAAG,QAAA,gBACzBpG,OAAA,CAACxD,IAAI;UAAC8K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAArB,QAAA,eACvBpG,OAAA,CAACzD,KAAK;YAAC8J,OAAO,EAAC,UAAU;YAACH,EAAE,EAAE;cAAEwB,CAAC,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBACrCpG,OAAA,CAAC1D,UAAU;cAAC+J,OAAO,EAAC,IAAI;cAACH,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAC,QAAA,EAAC;YAAa;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClExC,OAAA,CAAClC,KAAK;cAACmI,OAAO,EAAE,CAAE;cAAAG,QAAA,gBAChBpG,OAAA,CAAClC,KAAK;gBAACgI,SAAS,EAAC,KAAK;gBAACC,cAAc,EAAC,eAAe;gBAAAK,QAAA,gBAACpG,OAAA,CAAC1D,UAAU;kBAAA8J,QAAA,EAAC;gBAAY;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAAAxC,OAAA,CAAC1D,UAAU;kBAAC4J,EAAE,EAAE;oBAAEK,UAAU,EAAE;kBAAI,CAAE;kBAAAH,QAAA,EAAE0B,IAAI,CAACgB;gBAAW;kBAAAzG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvKxC,OAAA,CAAClC,KAAK;gBAACgI,SAAS,EAAC,KAAK;gBAACC,cAAc,EAAC,eAAe;gBAAAK,QAAA,gBAACpG,OAAA,CAAC1D,UAAU;kBAAA8J,QAAA,EAAC;gBAAY;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAAAxC,OAAA,CAAC1D,UAAU;kBAAC4J,EAAE,EAAE;oBAAEK,UAAU,EAAE,GAAG;oBAAE9D,KAAK,EAAE;kBAAe,CAAE;kBAAA2D,QAAA,GAAE0B,IAAI,CAACiB,WAAW,EAAC,IAAE,EAACjB,IAAI,CAACkB,gBAAgB,EAAC,IAAE;gBAAA;kBAAA3G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzNxC,OAAA,CAAClC,KAAK;gBAACgI,SAAS,EAAC,KAAK;gBAACC,cAAc,EAAC,eAAe;gBAAAK,QAAA,gBAACpG,OAAA,CAAC1D,UAAU;kBAAA8J,QAAA,EAAC;gBAAe;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAAAxC,OAAA,CAAC1D,UAAU;kBAAC4J,EAAE,EAAE;oBAAEK,UAAU,EAAE,GAAG;oBAAE9D,KAAK,EAAE;kBAAe,CAAE;kBAAA2D,QAAA,GAAE0B,IAAI,CAACmB,cAAc,EAAC,IAAE,EAAC,CAAC,GAAG,GAAGnB,IAAI,CAACkB,gBAAgB,EAAER,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;gBAAA;kBAAAnG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7O,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACPxC,OAAA,CAACxD,IAAI;UAAC8K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAArB,QAAA,eACvBpG,OAAA,CAACzD,KAAK;YAAC8J,OAAO,EAAC,UAAU;YAACH,EAAE,EAAE;cAAEwB,CAAC,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBACrCpG,OAAA,CAAC1D,UAAU;cAAC+J,OAAO,EAAC,IAAI;cAACH,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAC,QAAA,EAAC;YAAwB;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5ExC,OAAA,CAAClC,KAAK;cAACmI,OAAO,EAAE,CAAE;cAAAG,QAAA,gBACjBpG,OAAA,CAAClC,KAAK;gBAACgI,SAAS,EAAC,KAAK;gBAACC,cAAc,EAAC,eAAe;gBAAAK,QAAA,gBAACpG,OAAA,CAAC1D,UAAU;kBAAA8J,QAAA,EAAC;gBAAkB;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAAAxC,OAAA,CAAC1D,UAAU;kBAAC4J,EAAE,EAAE;oBAAEK,UAAU,EAAE;kBAAI,CAAE;kBAAAH,QAAA,GAAE0B,IAAI,CAACW,iBAAiB,EAAC,UAAQ;gBAAA;kBAAApG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC1LsF,IAAI,CAACY,cAAc,iBAClB1I,OAAA,CAAAE,SAAA;gBAAAkG,QAAA,gBACEpG,OAAA,CAAClC,KAAK;kBAACgI,SAAS,EAAC,KAAK;kBAACC,cAAc,EAAC,eAAe;kBAAAK,QAAA,gBAACpG,OAAA,CAAC1D,UAAU;oBAAA8J,QAAA,EAAC;kBAAe;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAAAxC,OAAA,CAAC1D,UAAU;oBAAC4J,EAAE,EAAE;sBAAEK,UAAU,EAAE;oBAAI,CAAE;oBAAAH,QAAA,GAAE0B,IAAI,CAACY,cAAc,EAAC,SAAO;kBAAA;oBAAArG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpLxC,OAAA,CAAClC,KAAK;kBAACgI,SAAS,EAAC,KAAK;kBAACC,cAAc,EAAC,eAAe;kBAAAK,QAAA,gBAACpG,OAAA,CAAC1D,UAAU;oBAAA8J,QAAA,EAAC;kBAAmB;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAAAxC,OAAA,CAAC1D,UAAU;oBAAC4J,EAAE,EAAE;sBAAEK,UAAU,EAAE;oBAAI,CAAE;oBAAAH,QAAA,EAAE0B,IAAI,CAACoB;kBAAkB;oBAAA7G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,eACrL,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EACNsF,IAAI,CAACqB,YAAY,IAAIrB,IAAI,CAACqB,YAAY,CAACC,MAAM,GAAG,CAAC,iBAChDpJ,OAAA,CAACxD,IAAI;UAAC8K,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAnB,QAAA,eAChBpG,OAAA,CAACzD,KAAK;YAAC8J,OAAO,EAAC,UAAU;YAACH,EAAE,EAAE;cAAEwB,CAAC,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBACrCpG,OAAA,CAAC1D,UAAU;cAAC+J,OAAO,EAAC,IAAI;cAACH,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAC,QAAA,EAAC;YAAgB;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrExC,OAAA,CAACP,eAAe;cACdqI,IAAI,EAAEA,IAAI,CAACqB,YAAY,CAACE,GAAG,CAACC,IAAI,KAAK;gBAAExB,IAAI,EAAEwB,IAAI,CAACxB,IAAI;gBAAEyB,KAAK,EAAE,GAAGD,IAAI,CAACC,KAAK;cAAI,CAAC,CAAC,CAAE;cACpFC,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,UAAU,EAAE,MAAM;gBAAEC,KAAK,EAAE;cAAI,CAAC,EACjD;gBAAEF,KAAK,EAAE,OAAO;gBAAEC,UAAU,EAAE,cAAc;gBAAEC,KAAK,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAQ,CAAC,CAC1E;cACFC,UAAU,EAAE/B,IAAI,CAACqB,YAAY,CAACC,MAAM,GAAG,CAAE;cACzCU,QAAQ,EAAE;YAAE;cAAAzH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,MAAMuH,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMjC,IAAI,GAAG9G,WAAW,CAACG,GAAG;IAC5B,IAAIX,aAAa,IAAI,CAACsH,IAAI,EAAE,oBAAO9H,OAAA,CAAC3D,GAAG;MAAC6J,EAAE,EAAE;QAAC6B,SAAS,EAAE,QAAQ;QAAEL,CAAC,EAAC;MAAC,CAAE;MAAAtB,QAAA,eAACpG,OAAA,CAACpD,gBAAgB;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;IAClG,IAAI,CAACsF,IAAI,EAAE,oBAAO9H,OAAA,CAACrD,KAAK;MAACqL,QAAQ,EAAC,MAAM;MAAA5B,QAAA,EAAC;IAAwC;MAAA/D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;IAEzF,oBACExC,OAAA,CAACzD,KAAK;MAAC2J,EAAE,EAAE;QAAEwB,CAAC,EAAE;MAAE,CAAE;MAACO,SAAS,EAAE,CAAE;MAAA7B,QAAA,GAC/BR,kBAAkB,CAAC,wBAAwBkC,IAAI,CAAC1D,aAAa,IAAI,UAAU,EAAE,EAAE,KAAK,CAAC,eACtFpE,OAAA,CAACnD,OAAO;QAACqJ,EAAE,EAAE;UAAEgC,EAAE,EAAE;QAAE;MAAE;QAAA7F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACzBX,UAAU,IAAIiG,IAAI,iBACjB9H,OAAA,CAAC3D,GAAG;QAAC6J,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEuB,CAAC,EAAC,CAAC;UAAEiB,MAAM,EAAE,gBAAgB;UAAEC,YAAY,EAAC;QAAE,CAAE;QAAAxC,QAAA,gBAC/DpG,OAAA,CAAC1D,UAAU;UAAC+J,OAAO,EAAC,IAAI;UAACwC,YAAY;UAAAzC,QAAA,EAAC;QAAW;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC/DxC,OAAA,CAACJ,QAAQ;UAACkI,IAAI,EAAEA;QAAK;UAAAzF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CACN,eACDxC,OAAA,CAACzD,KAAK;QAAC8J,OAAO,EAAC,UAAU;QAACH,EAAE,EAAE;UAAEwB,CAAC,EAAE,CAAC;UAAEvB,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBAC5CpG,OAAA,CAAC1D,UAAU;UAAC+J,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAC,QAAA,EAAC;QAAkB;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACvExC,OAAA,CAACP,eAAe;UACdqI,IAAI,EAAEA,IAAI,CAACkC,aAAa,IAAI,EAAG;UAC/BR,OAAO,EAAE,CAAE;UACP;YAAEC,KAAK,EAAE,WAAW;YAAEC,UAAU,EAAE,WAAW;YAAEC,KAAK,EAAE;UAAI,CAAC,EAC3D;YAAEF,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE,SAAS;YAAEC,KAAK,EAAE;UAAI,CAAC,EACvD;YAAEF,KAAK,EAAE,UAAU;YAAEC,UAAU,EAAE,OAAO;YAAEC,KAAK,EAAE,EAAE;YAAEC,KAAK,EAAE,OAAO;YAAEK,QAAQ,EAAE;UAAS,CAAC,EACzF;YAAER,KAAK,EAAE,eAAe;YAAEC,UAAU,EAAE,SAAS;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE,OAAO;YAAEM,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;UAAI,CAAC,EAC3H;YAAEX,KAAK,EAAE,aAAa;YAAEC,UAAU,EAAE,OAAO;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE,OAAO;YAAEM,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;UAAI,CAAC,EACrH;YAAEZ,KAAK,EAAE,iBAAiB;YAAEC,UAAU,EAAE,WAAW;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE,OAAO;YAAEM,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAAC5B,eAAe;UAAI,CAAC,CACnI;UACFuB,QAAQ,EAAE;QAAG;UAAAzH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACRxC,OAAA,CAACzD,KAAK;QAAC8J,OAAO,EAAC,UAAU;QAACH,EAAE,EAAE;UAAEwB,CAAC,EAAE;QAAE,CAAE;QAAAtB,QAAA,gBACrCpG,OAAA,CAAC1D,UAAU;UAAC+J,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAC,QAAA,EAAC;QAAkB;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACvExC,OAAA,CAACP,eAAe;UACdqI,IAAI,EAAEA,IAAI,CAACwC,eAAe,IAAI,EAAG;UACjCd,OAAO,EAAE,CAAE;UACP;YAAEC,KAAK,EAAE,WAAW;YAAEC,UAAU,EAAE,WAAW;YAAEC,KAAK,EAAE;UAAI,CAAC,EAC3D;YAAEF,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE,SAAS;YAAEC,KAAK,EAAE;UAAI,CAAC,EACvD;YAAEF,KAAK,EAAE,YAAY;YAAEC,UAAU,EAAE,SAAS;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE,OAAO;YAAEK,QAAQ,EAAE;UAAS,CAAC,EAC9F;YAAER,KAAK,EAAE,mBAAmB;YAAEC,UAAU,EAAE,aAAa;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE,OAAO;YAAEM,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACI,iBAAiB;UAAI,CAAC,CACzI;UACFT,QAAQ,EAAE;QAAG;UAAAzH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEZ,CAAC;EAED,MAAMgI,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAM1C,IAAI,GAAG9G,WAAW,CAACI,MAAM;IAC/B,IAAIZ,aAAa,IAAI,CAACsH,IAAI,EAAE,oBAAO9H,OAAA,CAAC3D,GAAG;MAAC6J,EAAE,EAAE;QAAC6B,SAAS,EAAE,QAAQ;QAAEL,CAAC,EAAC;MAAC,CAAE;MAAAtB,QAAA,eAACpG,OAAA,CAACpD,gBAAgB;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;IAClG,IAAI,CAACsF,IAAI,EAAE,oBAAO9H,OAAA,CAACrD,KAAK;MAACqL,QAAQ,EAAC,MAAM;MAAA5B,QAAA,EAAC;IAAqC;MAAA/D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;IAEtF,oBACExC,OAAA,CAACzD,KAAK;MAAC2J,EAAE,EAAE;QAAEwB,CAAC,EAAE;MAAE,CAAE;MAACO,SAAS,EAAE,CAAE;MAAA7B,QAAA,GAC/BR,kBAAkB,CAAC,2BAA2BkC,IAAI,CAAC2C,aAAa,IAAI,CAAC,cAAc3C,IAAI,CAAC1D,aAAa,IAAI,UAAU,EAAE,EAAE,QAAQ,CAAC,eACjIpE,OAAA,CAACnD,OAAO;QAACqJ,EAAE,EAAE;UAAEgC,EAAE,EAAE;QAAE;MAAE;QAAA7F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACzBX,UAAU,IAAIiG,IAAI,iBACjB9H,OAAA,CAAC3D,GAAG;QAAC6J,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEuB,CAAC,EAAC,CAAC;UAAEiB,MAAM,EAAE,gBAAgB;UAAEC,YAAY,EAAC;QAAE,CAAE;QAAAxC,QAAA,gBAChEpG,OAAA,CAAC1D,UAAU;UAAC+J,OAAO,EAAC,IAAI;UAACwC,YAAY;UAAAzC,QAAA,EAAC;QAAuB;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC1ExC,OAAA,CAACL,WAAW;UAACmI,IAAI,EAAEA;QAAK;UAAAzF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CACN,eACDxC,OAAA,CAACzD,KAAK;QAAC8J,OAAO,EAAC,UAAU;QAACH,EAAE,EAAE;UAAEwB,CAAC,EAAE;QAAE,CAAE;QAAAtB,QAAA,gBACrCpG,OAAA,CAAC1D,UAAU;UAAC+J,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAC,QAAA,EAAC;QAAgB;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACrExC,OAAA,CAACP,eAAe;UACdqI,IAAI,EAAEA,IAAI,CAAC1G,MAAM,IAAI,EAAG;UACxBoI,OAAO,EAAE,CAAE;UACP;YAAEC,KAAK,EAAE,WAAW;YAAEC,UAAU,EAAE,WAAW;YAAEC,KAAK,EAAE;UAAI,CAAC,EAC3D;YAAEF,KAAK,EAAE,WAAW;YAAEC,UAAU,EAAE,WAAW;YAAEC,KAAK,EAAE;UAAI,CAAC,EAC3D;YAAEF,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE,SAAS;YAAEC,KAAK,EAAE;UAAI,CAAC,EACvD;YAAEF,KAAK,EAAE,OAAO;YAAEC,UAAU,EAAE,OAAO;YAAEC,KAAK,EAAE,GAAG;YAAEO,UAAU,EAAGC,GAAG,iBAAKnK,OAAA,CAACtD,IAAI;cAACsK,KAAK,EAAEmD,GAAG,CAACO,KAAM;cAACjI,KAAK,EAAE0H,GAAG,CAACO,KAAK,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;cAAChE,IAAI,EAAC;YAAO;cAAArE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAE,CAAC,EAC5K;YAAEiH,KAAK,EAAE,cAAc;YAAEC,UAAU,EAAE,QAAQ;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE,OAAO;YAAEM,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAAC/B,YAAY;UAAI,CAAC,EACxH;YAAEqB,KAAK,EAAE,eAAe;YAAEC,UAAU,EAAE,SAAS;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE,OAAO;YAAEM,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACQ,aAAa;UAAI,CAAC,EAC3H;YAAElB,KAAK,EAAE,kBAAkB;YAAEC,UAAU,EAAE,YAAY;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE,OAAO;YAAEM,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACS,gBAAgB;UAAI,CAAC,EACpI;YAAEnB,KAAK,EAAE,sBAAsB;YAAEC,UAAU,EAAE,OAAO;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE,OAAO;YAAEM,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACU,oBAAoB;UAAI,CAAC,CACzI;UACFf,QAAQ,EAAE;QAAG;UAAAzH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEZ,CAAC;EAED,MAAMsI,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMhD,IAAI,GAAG9G,WAAW,CAACK,SAAS;IAClC,IAAIb,aAAa,IAAI,CAACsH,IAAI,EAAE,oBAAO9H,OAAA,CAAC3D,GAAG;MAAC6J,EAAE,EAAE;QAAC6B,SAAS,EAAE,QAAQ;QAAEL,CAAC,EAAC;MAAC,CAAE;MAAAtB,QAAA,eAACpG,OAAA,CAACpD,gBAAgB;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;IAClG,IAAI,CAACsF,IAAI,EAAE,oBAAO9H,OAAA,CAACrD,KAAK;MAACqL,QAAQ,EAAC,MAAM;MAAA5B,QAAA,EAAC;IAAoC;MAAA/D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;IAErF,oBACExC,OAAA,CAACzD,KAAK;MAAC2J,EAAE,EAAE;QAAEwB,CAAC,EAAE;MAAE,CAAE;MAACO,SAAS,EAAE,CAAE;MAAA7B,QAAA,GAC/BR,kBAAkB,CAAC,2BAA2BkC,IAAI,CAAC1D,aAAa,IAAI,UAAU,EAAE,EAAE,YAAY,CAAC,eAChGpE,OAAA,CAACnD,OAAO;QAACqJ,EAAE,EAAE;UAAEgC,EAAE,EAAE;QAAE;MAAE;QAAA7F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACzBX,UAAU,IAAIiG,IAAI,iBACjB9H,OAAA,CAAC3D,GAAG;QAAC6J,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEuB,CAAC,EAAC,CAAC;UAAEiB,MAAM,EAAE,gBAAgB;UAAEC,YAAY,EAAC;QAAE,CAAE;QAAAxC,QAAA,gBAChEpG,OAAA,CAAC1D,UAAU;UAAC+J,OAAO,EAAC,IAAI;UAACwC,YAAY;UAAAzC,QAAA,EAAC;QAAsB;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACzExC,OAAA,CAACF,cAAc;UAACgI,IAAI,EAAEA;QAAK;UAAAzF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CACN,eACDxC,OAAA,CAACzD,KAAK;QAAC8J,OAAO,EAAC,UAAU;QAACH,EAAE,EAAE;UAAEwB,CAAC,EAAE;QAAE,CAAE;QAAAtB,QAAA,gBACrCpG,OAAA,CAAC1D,UAAU;UAAC+J,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAC,QAAA,EAAC;QAAkB;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACvExC,OAAA,CAACP,eAAe;UACdqI,IAAI,EAAEA,IAAI,CAACiD,cAAc,IAAI,EAAG;UAChCvB,OAAO,EAAE,CAAE;UACP;YAAEC,KAAK,EAAE,OAAO;YAAEC,UAAU,EAAE,OAAO;YAAEC,KAAK,EAAE,GAAG;YAAEO,UAAU,EAAGC,GAAG,iBAAKnK,OAAA,CAACtD,IAAI;cAACsK,KAAK,EAAEmD,GAAG,CAACO,KAAM;cAACjI,KAAK,EAAE0H,GAAG,CAACO,KAAK,KAAK,YAAY,GAAG,SAAS,GAAIP,GAAG,CAACO,KAAK,KAAK,WAAW,GAAG,SAAS,GAAG,SAAW;cAAChE,IAAI,EAAC;YAAO;cAAArE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC,CAAC,EACpN;YAAEiH,KAAK,EAAE,UAAU;YAAEC,UAAU,EAAE,OAAO;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE,OAAO;YAAEK,QAAQ,EAAE;UAAS,CAAC,EAC1F;YAAER,KAAK,EAAE,eAAe;YAAEC,UAAU,EAAE,eAAe;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE,OAAO;YAAEM,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;UAAI,CAAC,EACjI;YAAEX,KAAK,EAAE,aAAa;YAAEC,UAAU,EAAE,aAAa;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE,OAAO;YAAEM,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;UAAI,CAAC,CAC7H;UACFR,UAAU,EAAE;QAAM;UAAAxH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEZ,CAAC;EAED,MAAMwI,yBAAyB,GAAGA,CAAA,KAAM;IAAA,IAAAC,YAAA;IACtC,MAAMnD,IAAI,GAAG9G,WAAW,CAACM,eAAe;IAExC,oBACEtB,OAAA,CAACzD,KAAK;MAAC2J,EAAE,EAAE;QAAEwB,CAAC,EAAE;MAAE,CAAE;MAACO,SAAS,EAAE,CAAE;MAAA7B,QAAA,GAC/BR,kBAAkB,CAAC,yBAAyB,EAAE,kBAAkB,CAAC,eAClE5F,OAAA,CAACnD,OAAO;QAACqJ,EAAE,EAAE;UAAEgC,EAAE,EAAE;QAAE;MAAE;QAAA7F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1BxC,OAAA,CAAClC,KAAK;QAACwI,SAAS,EAAC,MAAM;QAAC4E,QAAQ,EAAG3G,CAAC,IAAK;UAAEA,CAAC,CAAC4G,cAAc,CAAC,CAAC;UAAExG,wBAAwB,CAAC,kBAAkB,EAAE,OAAO,CAAC;QAAE,CAAE;QAACsB,OAAO,EAAE,CAAE;QAACH,SAAS,EAAE;UAAEyB,EAAE,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAM,CAAE;QAACtB,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACzLpG,OAAA,CAAC7C,SAAS;UACRsH,IAAI,EAAC,WAAW;UAChBuC,KAAK,EAAC,WAAW;UACjBtC,KAAK,EAAElD,QAAQ,CAACE,SAAU;UAC1BoF,QAAQ,EAAExC,iBAAkB;UAC5BoC,IAAI,EAAC,OAAO;UACZ0E,UAAU,EAAC,iBAAiB;UAC5BlF,EAAE,EAAE;YAAEmF,QAAQ,EAAE;UAAE;QAAE;UAAAhJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACFxC,OAAA,CAACvD,MAAM;UAACsG,IAAI,EAAC,QAAQ;UAACsD,OAAO,EAAC,WAAW;UAACM,QAAQ,EAAEjG,aAAa,IAAI,CAACc,QAAQ,CAACE,SAAU;UAAC8E,SAAS,EAAE9F,aAAa,gBAAGV,OAAA,CAACpD,gBAAgB;YAAC8J,IAAI,EAAE;UAAG;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGxC,OAAA,CAAChC,cAAc;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA4D,QAAA,EAAC;QAE1K;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EACP5B,KAAK,IAAIE,kBAAkB,KAAK,kBAAkB,iBAAId,OAAA,CAACrD,KAAK;QAACqL,QAAQ,EAAC,OAAO;QAAC9B,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAAExF;MAAK;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAE5G9B,aAAa,IAAII,kBAAkB,KAAK,kBAAkB,IAAI,CAACgH,IAAI,iBAAI9H,OAAA,CAAC3D,GAAG;QAAC6J,EAAE,EAAE;UAAC6B,SAAS,EAAE,QAAQ;UAAEL,CAAC,EAAC;QAAC,CAAE;QAAAtB,QAAA,eAACpG,OAAA,CAACpD,gBAAgB;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EAEtIsF,IAAI,iBACH9H,OAAA,CAAAE,SAAA;QAAAkG,QAAA,gBACEpG,OAAA,CAAC1D,UAAU;UAAC+J,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEoF,EAAE,EAAE,CAAC;YAAEnF,EAAE,EAAC,CAAC;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAH,QAAA,GAAC,eAAa,GAAA6E,YAAA,GAACnD,IAAI,CAACyD,MAAM,cAAAN,YAAA,uBAAXA,YAAA,CAAavJ,SAAS;QAAA;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACjHxC,OAAA,CAACxD,IAAI;UAAC2L,SAAS;UAAClC,OAAO,EAAE,CAAE;UAAAG,QAAA,gBACzBpG,OAAA,CAACxD,IAAI;YAAC8K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAArB,QAAA,eACvBpG,OAAA,CAACzD,KAAK;cAAC8J,OAAO,EAAC,UAAU;cAACH,EAAE,EAAE;gBAAEwB,CAAC,EAAE;cAAE,CAAE;cAAAtB,QAAA,gBACrCpG,OAAA,CAAC1D,UAAU;gBAAC+J,OAAO,EAAC,WAAW;gBAACH,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAEI,UAAU,EAAE;gBAAS,CAAE;gBAAAH,QAAA,EAAC;cAAQ;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACzFsF,IAAI,CAACyD,MAAM,iBAAIvL,OAAA,CAAClC,KAAK;gBAACmI,OAAO,EAAE,GAAI;gBAAAG,QAAA,gBAClCpG,OAAA,CAAClC,KAAK;kBAACgI,SAAS,EAAC,KAAK;kBAACC,cAAc,EAAC,eAAe;kBAAAK,QAAA,gBAACpG,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAC;kBAAG;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAAAxC,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAACH,EAAE,EAAE;sBAAEK,UAAU,EAAE;oBAAI,CAAE;oBAAAH,QAAA,EAAE0B,IAAI,CAACyD,MAAM,CAAC7J;kBAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnMxC,OAAA,CAAClC,KAAK;kBAACgI,SAAS,EAAC,KAAK;kBAACC,cAAc,EAAC,eAAe;kBAAAK,QAAA,gBAACpG,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAC;kBAAU;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAAAxC,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAACH,EAAE,EAAE;sBAAEK,UAAU,EAAE;oBAAI,CAAE;oBAAAH,QAAA,EAAE0B,IAAI,CAACyD,MAAM,CAACC;kBAAS;oBAAAnJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1MxC,OAAA,CAAClC,KAAK;kBAACgI,SAAS,EAAC,KAAK;kBAACC,cAAc,EAAC,eAAe;kBAAAK,QAAA,gBAACpG,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAC;kBAAQ;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAAAxC,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAACH,EAAE,EAAE;sBAAEK,UAAU,EAAE;oBAAI,CAAE;oBAAAH,QAAA,EAAE0B,IAAI,CAACyD,MAAM,CAACE;kBAAO;oBAAApJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtMxC,OAAA,CAAClC,KAAK;kBAACgI,SAAS,EAAC,KAAK;kBAACC,cAAc,EAAC,eAAe;kBAAAK,QAAA,gBAACpG,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAC;kBAAM;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAAAxC,OAAA,CAACtD,IAAI;oBAACsK,KAAK,EAAEc,IAAI,CAACyD,MAAM,CAACb,KAAM;oBAACjI,KAAK,EAAEqF,IAAI,CAACyD,MAAM,CAACb,KAAK,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;oBAAChE,IAAI,EAAC;kBAAO;oBAAArE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1N,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACPxC,OAAA,CAACxD,IAAI;YAAC8K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAArB,QAAA,eACvBpG,OAAA,CAACzD,KAAK;cAAC8J,OAAO,EAAC,UAAU;cAACH,EAAE,EAAE;gBAAEwB,CAAC,EAAE;cAAE,CAAE;cAAAtB,QAAA,gBACrCpG,OAAA,CAAC1D,UAAU;gBAAC+J,OAAO,EAAC,WAAW;gBAACH,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAEI,UAAU,EAAE;gBAAS,CAAE;gBAAAH,QAAA,EAAC;cAAiB;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAClGsF,IAAI,CAACyD,MAAM,iBAAIvL,OAAA,CAAClC,KAAK;gBAACmI,OAAO,EAAE,GAAI;gBAAAG,QAAA,gBACjCpG,OAAA,CAAClC,KAAK;kBAACgI,SAAS,EAAC,KAAK;kBAACC,cAAc,EAAC,eAAe;kBAAAK,QAAA,gBAACpG,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAC;kBAAa;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAAAxC,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAACH,EAAE,EAAE;sBAAEK,UAAU,EAAE;oBAAI,CAAE;oBAAAH,QAAA,GAAE0B,IAAI,CAACyD,MAAM,CAACnD,YAAY,EAAC,GAAC;kBAAA;oBAAA/F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjNxC,OAAA,CAAClC,KAAK;kBAACgI,SAAS,EAAC,KAAK;kBAACC,cAAc,EAAC,eAAe;kBAAAK,QAAA,gBAACpG,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAC;kBAAiB;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAAAxC,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAACH,EAAE,EAAE;sBAAEK,UAAU,EAAE,GAAG;sBAAE9D,KAAK,EAAE;oBAAe,CAAE;oBAAA2D,QAAA,GAAE0B,IAAI,CAACyD,MAAM,CAACX,gBAAgB,EAAC,GAAC;kBAAA;oBAAAvI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChPxC,OAAA,CAAClC,KAAK;kBAACgI,SAAS,EAAC,KAAK;kBAACC,cAAc,EAAC,eAAe;kBAAAK,QAAA,gBAACpG,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAC;kBAAc;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAAAxC,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAACH,EAAE,EAAE;sBAAEK,UAAU,EAAE,GAAG;sBAAE9D,KAAK,EAAE;oBAAe,CAAE;oBAAA2D,QAAA,GAAE0B,IAAI,CAACyD,MAAM,CAACZ,aAAa,EAAC,GAAC;kBAAA;oBAAAtI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1OxC,OAAA,CAAClC,KAAK;kBAACgI,SAAS,EAAC,KAAK;kBAACC,cAAc,EAAC,eAAe;kBAAAK,QAAA,gBAACpG,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAC;kBAAW;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAAAxC,OAAA,CAAC1D,UAAU;oBAAC+J,OAAO,EAAC,OAAO;oBAACH,EAAE,EAAE;sBAAEK,UAAU,EAAE;oBAAI,CAAE;oBAAAH,QAAA,GAAE0B,IAAI,CAACyD,MAAM,CAACV,oBAAoB,EAAC,GAAC;kBAAA;oBAAAxI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACPxC,OAAA,CAACxD,IAAI;YAAC8K,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAnB,QAAA,eAChBpG,OAAA,CAACzD,KAAK;cAAC8J,OAAO,EAAC,UAAU;cAACH,EAAE,EAAE;gBAAEwB,CAAC,EAAE;cAAE,CAAE;cAAAtB,QAAA,gBACrCpG,OAAA,CAAC1D,UAAU;gBAAC+J,OAAO,EAAC,WAAW;gBAACH,EAAE,EAAE;kBAAEC,EAAE,EAAE,CAAC;kBAAEI,UAAU,EAAE;gBAAS,CAAE;gBAAAH,QAAA,GAAC,kBAAgB,EAAC0B,IAAI,CAACgB,WAAW,IAAI,CAAC,EAAC,GAAC;cAAA;gBAAAzG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1HxC,OAAA,CAACP,eAAe;gBACdqI,IAAI,EAAEA,IAAI,CAAC4D,cAAc,IAAI,EAAG;gBAChClC,OAAO,EAAE,CAAE;gBACP;kBAAEC,KAAK,EAAE,SAAS;kBAAEC,UAAU,EAAE,SAAS;kBAAEC,KAAK,EAAE;gBAAI,CAAC,EACvD;kBAAEF,KAAK,EAAE,SAAS;kBAAEC,UAAU,EAAE,SAAS;kBAAEC,KAAK,EAAE;gBAAI,CAAC,EACvD;kBAAEF,KAAK,EAAE,SAAS;kBAAEC,UAAU,EAAE,SAAS;kBAAEC,KAAK,EAAE;gBAAI,CAAC,EACvD;kBAAEF,KAAK,EAAE,WAAW;kBAAEC,UAAU,EAAE,WAAW;kBAAEC,KAAK,EAAE;gBAAI,CAAC,EAC3D;kBAAEF,KAAK,EAAE,eAAe;kBAAEC,UAAU,EAAE,SAAS;kBAAEC,KAAK,EAAE,GAAG;kBAAEC,KAAK,EAAE,OAAO;kBAAEM,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;gBAAI,CAAC,EAC3H;kBAAEX,KAAK,EAAE,aAAa;kBAAEC,UAAU,EAAE,OAAO;kBAAEC,KAAK,EAAE,GAAG;kBAAEC,KAAK,EAAE,OAAO;kBAAEM,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;gBAAI,CAAC,EACrH;kBAAEZ,KAAK,EAAE,OAAO;kBAAEC,UAAU,EAAE,OAAO;kBAAEC,KAAK,EAAE,GAAG;kBAAEO,UAAU,EAAGC,GAAG,iBAAKnK,OAAA,CAACtD,IAAI;oBAACsK,KAAK,EAAEmD,GAAG,CAACO,KAAM;oBAACjI,KAAK,EAAE0H,GAAG,CAACO,KAAK,KAAK,QAAQ,IAAIP,GAAG,CAACO,KAAK,KAAK,YAAY,GAAG,SAAS,GAAG,SAAU;oBAAChE,IAAI,EAAC;kBAAO;oBAAArE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAE,CAAC,CACvM;gBACFsH,QAAQ,EAAE;cAAE;gBAAAzH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACP,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEZ,CAAC;EAED,MAAMmJ,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAM7D,IAAI,GAAG9G,WAAW,CAACO,WAAW;IACpC,MAAMqK,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,MAAMC,aAAa,GAAG,IAAIH,IAAI,CAAC,CAAC;IAChCG,aAAa,CAACC,QAAQ,CAACD,aAAa,CAACE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;IACpD,MAAMC,SAAS,GAAGH,aAAa,CAACF,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAE3D,oBACE/L,OAAA,CAACzD,KAAK;MAAC2J,EAAE,EAAE;QAAEwB,CAAC,EAAE;MAAE,CAAE;MAACO,SAAS,EAAE,CAAE;MAAA7B,QAAA,GAC/BR,kBAAkB,CAAC,yBAAyB,EAAE,cAAc,CAAC,eAC9D5F,OAAA,CAACnD,OAAO;QAACqJ,EAAE,EAAE;UAAEgC,EAAE,EAAE;QAAE;MAAE;QAAA7F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1BxC,OAAA,CAAClC,KAAK;QAACwI,SAAS,EAAC,MAAM;QAAC4E,QAAQ,EAAG3G,CAAC,IAAK;UAAEA,CAAC,CAAC4G,cAAc,CAAC,CAAC;UAAExG,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAC;QAAE,CAAE;QAACsB,OAAO,EAAE,CAAE;QAACH,SAAS,EAAE;UAAEyB,EAAE,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAM,CAAE;QAACtB,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAACH,UAAU,EAAC,YAAY;QAAAI,QAAA,gBAC7MpG,OAAA,CAAC7C,SAAS;UACRsH,IAAI,EAAC,aAAa;UAClBuC,KAAK,EAAC,aAAa;UACnBjE,IAAI,EAAC,MAAM;UACX2B,KAAK,EAAElD,QAAQ,CAACG,WAAW,IAAIwK,SAAU;UACzCrF,QAAQ,EAAExC,iBAAkB;UAC5B8H,eAAe,EAAE;YAAEC,MAAM,EAAE;UAAK,CAAE;UAClC3F,IAAI,EAAC,OAAO;UACZR,EAAE,EAAE;YAAEmF,QAAQ,EAAE;UAAE;QAAE;UAAAhJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACFxC,OAAA,CAAC7C,SAAS;UACRsH,IAAI,EAAC,WAAW;UAChBuC,KAAK,EAAC,WAAW;UACjBjE,IAAI,EAAC,MAAM;UACX2B,KAAK,EAAElD,QAAQ,CAACI,SAAS,IAAIgK,KAAM;UACnC9E,QAAQ,EAAExC,iBAAkB;UAC5B8H,eAAe,EAAE;YAAEC,MAAM,EAAE;UAAK,CAAE;UAClC3F,IAAI,EAAC,OAAO;UACZR,EAAE,EAAE;YAAEmF,QAAQ,EAAE;UAAE;QAAE;UAAAhJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACFxC,OAAA,CAACvD,MAAM;UAACsG,IAAI,EAAC,QAAQ;UAACsD,OAAO,EAAC,WAAW;UAACM,QAAQ,EAAEjG,aAAa,IAAI,CAACc,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAU;UAAC4E,SAAS,EAAE9F,aAAa,gBAAGV,OAAA,CAACpD,gBAAgB;YAAC8J,IAAI,EAAE;UAAG;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGxC,OAAA,CAAChC,cAAc;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA4D,QAAA,EAAC;QAEnM;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EACP5B,KAAK,IAAIE,kBAAkB,KAAK,cAAc,iBAAId,OAAA,CAACrD,KAAK;QAACqL,QAAQ,EAAC,OAAO;QAAC9B,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAAExF;MAAK;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAExG9B,aAAa,IAAII,kBAAkB,KAAK,cAAc,IAAI,CAACgH,IAAI,iBAAI9H,OAAA,CAAC3D,GAAG;QAAC6J,EAAE,EAAE;UAAC6B,SAAS,EAAE,QAAQ;UAAEL,CAAC,EAAC;QAAC,CAAE;QAAAtB,QAAA,eAACpG,OAAA,CAACpD,gBAAgB;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EAElIsF,IAAI,iBACH9H,OAAA,CAAAE,SAAA;QAAAkG,QAAA,gBACEpG,OAAA,CAAC1D,UAAU;UAAC+J,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEoF,EAAE,EAAC,CAAC;YAAEnF,EAAE,EAAC,CAAC;YAAEI,UAAU,EAAC;UAAI,CAAE;UAAAH,QAAA,GAAC,uBAAqB,EAAC0B,IAAI,CAACnG,WAAW,EAAC,KAAG,EAACmG,IAAI,CAAClG,SAAS;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACpIxC,OAAA,CAACxD,IAAI;UAAC2L,SAAS;UAAClC,OAAO,EAAE,CAAE;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAC,QAAA,GACrCgB,aAAa,CAAC,sBAAsB,EAAE,GAAGU,IAAI,CAACwE,oBAAoB,IAAI,CAAC,GAAG,EAAE,IAAI,eAAEtM,OAAA,CAAChC,cAAc;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAAE,cAAc,CAAC,EACrH4E,aAAa,CAAC,eAAe,EAAEU,IAAI,CAACyE,aAAa,IAAI,CAAC,EAAE,IAAI,eAAEvM,OAAA,CAACjB,YAAY;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAAE,WAAW,CAAC,EAC5F4E,aAAa,CAAC,cAAc,EAAE,GAAGU,IAAI,CAACW,iBAAiB,IAAI,CAAC,GAAG,EAAE,IAAI,eAAEzI,OAAA,CAACf,aAAa;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAAE,cAAc,CAAC,EACzG4E,aAAa,CAAC,2BAA2B,EAAE,GAAGU,IAAI,CAACyE,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC3E,IAAI,CAACwE,oBAAoB,IAAI,CAAC,IAAIxE,IAAI,CAACyE,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,eAAEvM,OAAA,CAAC9B,YAAY;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAAE,cAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvL,CAAC,EACNX,UAAU,iBACT7B,OAAA,CAAC3D,GAAG;UAAC6J,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEuB,CAAC,EAAC,CAAC;YAAEiB,MAAM,EAAE,gBAAgB;YAAEC,YAAY,EAAC;UAAE,CAAE;UAAAxC,QAAA,gBAC/DpG,OAAA,CAAC1D,UAAU;YAAC+J,OAAO,EAAC,IAAI;YAACwC,YAAY;YAAAzC,QAAA,EAAC;UAAwB;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5ExC,OAAA,CAACH,aAAa;YAACiI,IAAI,EAAEA;UAAK;YAAAzF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CACN,eACDxC,OAAA,CAACzD,KAAK;UAAC8J,OAAO,EAAC,UAAU;UAACH,EAAE,EAAE;YAAEwB,CAAC,EAAE;UAAE,CAAE;UAAAtB,QAAA,gBACrCpG,OAAA,CAAC1D,UAAU;YAAC+J,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAC,QAAA,EAAC;UAA0B;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/ExC,OAAA,CAACP,eAAe;YACdqI,IAAI,EAAEA,IAAI,CAAC4E,gBAAgB,IAAI,EAAG;YAClClD,OAAO,EAAE,CAAE;YACP;cAAEC,KAAK,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAI,CAAC,EACjD;cAAEF,KAAK,EAAE,OAAO;cAAEC,UAAU,EAAE,cAAc;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACZ,KAAK;YAAI,CAAC,CAClH;YACFO,QAAQ,EAAE;UAAG;YAAAzH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA,eACR,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEZ,CAAC;;EAED;EACA,oBACExC,OAAA,CAAC3D,GAAG;IAAC6J,EAAE,EAAE;MAAEe,OAAO,EAAE,MAAM;MAAE0F,SAAS,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAW,CAAE;IAAAxG,QAAA,gBACpEpG,OAAA,CAACnC,WAAW;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfxC,OAAA,CAACrC,MAAM;MAACkP,QAAQ,EAAC,OAAO;MAAC3G,EAAE,EAAE;QAAE4G,MAAM,EAAGC,KAAK,IAAKA,KAAK,CAACD,MAAM,CAACE,MAAM,GAAG,CAAC;QAAEC,eAAe,EAAE;MAAe,CAAE;MAAA7G,QAAA,eAC3GpG,OAAA,CAACpC,OAAO;QAAAwI,QAAA,gBACNpG,OAAA,CAAClD,UAAU;UACT2F,KAAK,EAAC,SAAS;UACf,cAAW,aAAa;UACxByK,IAAI,EAAC,OAAO;UACZzG,OAAO,EAAEA,CAAA,KAAMzE,aAAa,CAAC,CAACD,UAAU,CAAE;UAC1CmE,EAAE,EAAE;YAAEiB,EAAE,EAAE,CAAC;YAAEF,OAAO,EAAE;cAAEO,EAAE,EAAE;YAAO;UAAE,CAAE,CAAC;UAAA;UAAApB,QAAA,eAExCpG,OAAA,CAACb,QAAQ;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACbxC,OAAA,CAAC1D,UAAU;UAAC+J,OAAO,EAAC,IAAI;UAAC8G,MAAM;UAAC7G,SAAS,EAAC,KAAK;UAACJ,EAAE,EAAE;YAAEmF,QAAQ,EAAE;UAAE,CAAE;UAAAjF,QAAA,GAAC,wBAC7C,EAAC7F,UAAU,IAAI,QAAQA,UAAU,EAAE;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACbxC,OAAA,CAAClC,KAAK;UAACgI,SAAS,EAAC,KAAK;UAACG,OAAO,EAAE,CAAE;UAAAG,QAAA,gBAChCpG,OAAA,CAACvD,MAAM;YAACgG,KAAK,EAAC,SAAS;YAACgE,OAAO,EAAEA,CAAA,KAAMnG,QAAQ,CAAC,CAAC,CAAC,CAAE;YAACkG,SAAS,eAAExG,OAAA,CAACvB,aAAa;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAA4D,QAAA,EAAC;UAAQ;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpGxC,OAAA,CAACT,eAAe;YAACkD,KAAK,EAAC;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAETxC,OAAA,CAAC1C,MAAM;MACL+I,OAAO,EAAC,WAAW,CAAC;MAAA;MACpBZ,IAAI,EAAE1D,UAAW;MACjBmE,EAAE,EAAE;QACFyD,KAAK,EAAExJ,YAAY;QACnBiN,UAAU,EAAE,CAAC;QACb,CAAC,oBAAoB,GAAG;UAAEzD,KAAK,EAAExJ,YAAY;UAAEkN,SAAS,EAAE,YAAY;UAAEC,GAAG,EAAE,MAAM;UAAE3F,MAAM,EAAE;QAAoB,CAAC,CAAE;MACtH,CAAE;MAAAvB,QAAA,eAEFpG,OAAA,CAACzC,IAAI;QAAC2I,EAAE,EAAE;UAAEqH,EAAE,EAAE;QAAE,CAAE;QAAAnH,QAAA,GAAC,GAAC,EACnBnE,WAAW,CAACoH,GAAG,CAAEmE,MAAM,iBACtBxN,OAAA,CAACxC,cAAc;UAEbiQ,QAAQ,EAAE3M,kBAAkB,KAAK0M,MAAM,CAACtL,EAAG;UAC3CuE,OAAO,EAAEA,CAAA,KAAM;YACX1F,qBAAqB,CAACyM,MAAM,CAACtL,EAAE,CAAC;YAChCrB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;YAChB;YACA,IAAI2M,MAAM,CAACtL,EAAE,KAAK,kBAAkB,IAAIsL,MAAM,CAACtL,EAAE,KAAK,cAAc,EAAE;cACpE;cACA,IAAGpB,kBAAkB,KAAK0M,MAAM,CAACtL,EAAE,EAAE;gBACjCT,WAAW,CAAC;kBAAEC,SAAS,EAAE,EAAE;kBAAEC,WAAW,EAAE,EAAE;kBAAEC,SAAS,EAAE;gBAAE,CAAC,CAAC;gBAC7D;gBACAX,cAAc,CAACqE,IAAI,KAAK;kBAAC,GAAGA,IAAI;kBAAEhE,eAAe,EAAE,IAAI;kBAAEC,WAAW,EAAE;gBAAI,CAAC,CAAC,CAAC;cACjF;YACF,CAAC,MAAM,IAAI,CAACP,WAAW,CAACwM,MAAM,CAACtL,EAAE,CAAC,IAAI,CAAC1B,aAAa,EAAE;cAAE;cACpDmE,wBAAwB,CAAC6I,MAAM,CAACtL,EAAE,EAAE,OAAO,CAAC;YAChD;UACJ,CAAE;UAAAkE,QAAA,gBAEFpG,OAAA,CAACvC,YAAY;YAACyI,EAAE,EAAE;cAACzD,KAAK,EAAE,GAAG+K,MAAM,CAAC/K,KAAK;YAAO,CAAE;YAAA2D,QAAA,EAAEoH,MAAM,CAACpL;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAC/ExC,OAAA,CAACtC,YAAY;YAACgQ,OAAO,EAAEF,MAAM,CAACrL;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA,GAnBlCgL,MAAM,CAACtL,EAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoBA,CACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAETxC,OAAA,CAAC3D,GAAG;MAACiK,SAAS,EAAC,MAAM;MAACJ,EAAE,EAAE;QAAEmF,QAAQ,EAAE,CAAC;QAAE3D,CAAC,EAAE,CAAC;QAAE4D,EAAE,EAAE;MAAO,CAAE;MAAAlF,QAAA,GAAC,GAAC,EAC3D5F,aAAa,IAAI,CAAC8C,MAAM,CAACC,MAAM,CAACvC,WAAW,CAAC,CAAC2M,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC;MAAA;MAAM;MAC5D5N,OAAA,CAAC3D,GAAG;QAAC6J,EAAE,EAAE;UAAEe,OAAO,EAAE,MAAM;UAAE4G,aAAa,EAAC,QAAQ;UAAE7H,UAAU,EAAE,QAAQ;UAAED,cAAc,EAAE,QAAQ;UAAE4B,MAAM,EAAE;QAAO,CAAE;QAAAvB,QAAA,gBACjHpG,OAAA,CAACpD,gBAAgB;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpBxC,OAAA,CAAC1D,UAAU;UAAC4J,EAAE,EAAE;YAACoF,EAAE,EAAC;UAAC,CAAE;UAAAlF,QAAA,EAAC;QAA8B;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CACR,EACA5B,KAAK,iBAAIZ,OAAA,CAACrD,KAAK;QAACqL,QAAQ,EAAC,OAAO;QAAC9B,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAAExF;MAAK;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAE/D,CAAChC,aAAa,iBACXR,OAAA,CAAAE,SAAA;QAAAkG,QAAA,GACCtF,kBAAkB,KAAK,UAAU,iBAAId,OAAA,CAAC6H,kBAAkB;UAAAxF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC3D1B,kBAAkB,KAAK,KAAK,iBAAId,OAAA,CAAC+J,aAAa;UAAA1H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACjD1B,kBAAkB,KAAK,QAAQ,iBAAId,OAAA,CAACwK,gBAAgB;UAAAnI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACvD1B,kBAAkB,KAAK,YAAY,iBAAId,OAAA,CAAC8K,mBAAmB;UAAAzI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC9D1B,kBAAkB,KAAK,kBAAkB,iBAAId,OAAA,CAACgL,yBAAyB;UAAA3I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC1E1B,kBAAkB,KAAK,cAAc,iBAAId,OAAA,CAAC2L,qBAAqB;UAAAtJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,eACjE,CACL;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnC,EAAA,CA1mBID,0BAA0B;EAAA,QACbhB,WAAW,EACLC,SAAS;AAAA;AAAAyO,EAAA,GAF5B1N,0BAA0B;AA4mBhC,eAAeA,0BAA0B;AAAC,IAAA0N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}