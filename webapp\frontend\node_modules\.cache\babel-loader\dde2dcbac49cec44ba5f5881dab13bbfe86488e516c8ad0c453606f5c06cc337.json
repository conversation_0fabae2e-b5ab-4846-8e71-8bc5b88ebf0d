{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst parcoCaviService = {\n  // Ottiene la lista delle bobine di un cantiere\n  getBobine: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea una nuova bobina\n  createBobina: async (cantiereId, bobinaData) => {\n    try {\n      const response = await axiosInstance.post(`/parco-cavi/${cantiereId}`, bobinaData);\n      return response.data;\n    } catch (error) {\n      console.error('Create bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna una bobina esistente\n  updateBobina: async (cantiereId, numeroBobina, bobinaData) => {\n    try {\n      const response = await axiosInstance.put(`/parco-cavi/${cantiereId}/${numeroBobina}`, bobinaData);\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina una bobina\n  deleteBobina: async (cantiereId, numeroBobina) => {\n    try {\n      const response = await axiosInstance.delete(`/parco-cavi/${cantiereId}/${numeroBobina}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene lo storico utilizzo delle bobine\n  getStoricoUtilizzo: async cantiereId => {\n    try {\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereId}/storico`);\n      return response.data;\n    } catch (error) {\n      console.error('Get storico utilizzo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default parcoCaviService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "parcoCaviService", "getBobine", "cantiereId", "cantiereIdNum", "parseInt", "isNaN", "Error", "response", "get", "data", "console", "createBobina", "bobina<PERSON><PERSON>", "post", "updateBobina", "numeroBobina", "put", "deleteBobina", "delete", "getStoricoUtilizzo"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/parcoCaviService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\nconst parcoCaviService = {\n  // Ottiene la lista delle bobine di un cantiere\n  getBobine: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea una nuova bobina\n  createBobina: async (cantiereId, bobinaData) => {\n    try {\n      const response = await axiosInstance.post(`/parco-cavi/${cantiereId}`, bobinaData);\n      return response.data;\n    } catch (error) {\n      console.error('Create bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna una bobina esistente\n  updateBobina: async (cantiereId, numeroBobina, bobinaData) => {\n    try {\n      const response = await axiosInstance.put(`/parco-cavi/${cantiereId}/${numeroBobina}`, bobinaData);\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina una bobina\n  deleteBobina: async (cantiereId, numeroBobina) => {\n    try {\n      const response = await axiosInstance.delete(`/parco-cavi/${cantiereId}/${numeroBobina}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene lo storico utilizzo delle bobine\n  getStoricoUtilizzo: async (cantiereId) => {\n    try {\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereId}/storico`);\n      return response.data;\n    } catch (error) {\n      console.error('Get storico utilizzo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default parcoCaviService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,gBAAgB,GAAG;EACvB;EACAC,SAAS,EAAE,MAAOC,UAAU,IAAK;IAC/B,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMK,QAAQ,GAAG,MAAMtB,aAAa,CAACuB,GAAG,CAAC,eAAeL,aAAa,EAAE,CAAC;MACxE,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF,CAAC;EAED;EACAc,YAAY,EAAE,MAAAA,CAAOT,UAAU,EAAEU,UAAU,KAAK;IAC9C,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMtB,aAAa,CAAC4B,IAAI,CAAC,eAAeX,UAAU,EAAE,EAAEU,UAAU,CAAC;MAClF,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF,CAAC;EAED;EACAiB,YAAY,EAAE,MAAAA,CAAOZ,UAAU,EAAEa,YAAY,EAAEH,UAAU,KAAK;IAC5D,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMtB,aAAa,CAAC+B,GAAG,CAAC,eAAed,UAAU,IAAIa,YAAY,EAAE,EAAEH,UAAU,CAAC;MACjG,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF,CAAC;EAED;EACAoB,YAAY,EAAE,MAAAA,CAAOf,UAAU,EAAEa,YAAY,KAAK;IAChD,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMtB,aAAa,CAACiC,MAAM,CAAC,eAAehB,UAAU,IAAIa,YAAY,EAAE,CAAC;MACxF,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF,CAAC;EAED;EACAsB,kBAAkB,EAAE,MAAOjB,UAAU,IAAK;IACxC,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMtB,aAAa,CAACuB,GAAG,CAAC,eAAeN,UAAU,UAAU,CAAC;MAC7E,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}