{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Box, Paper, Typography, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip, InputAdornment, Dialog, DialogTitle, DialogContent, DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, List, ListItem, ListItemButton, ListItemText, ListItemSecondaryAction } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Cancel as CancelIcon, Warning as WarningIcon, Info as InfoIcon, AddCircleOutline as AddCircleOutlineIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null); // Mantenuto per compatibilità\n  const [incompatibleReelData, setIncompatibleReelData] = useState({\n    cavo: null,\n    bobina: null\n  });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n\n  // Stati per i filtri delle bobine\n  const [searchText, setSearchText] = useState('');\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = idBobina => {\n    console.log('Bobina selezionata:', idBobina);\n    setFormData({\n      ...formData,\n      id_bobina: idBobina\n    });\n\n    // Reset degli errori\n    setFormErrors(prev => ({\n      ...prev,\n      id_bobina: null,\n      id_bobina_input: null\n    }));\n\n    // Forza il re-render per mostrare le informazioni della bobina selezionata\n    const selectedBobina = bobine.find(b => b.id_bobina === idBobina);\n    if (selectedBobina) {\n      console.log('Dettagli bobina selezionata:', selectedBobina);\n    }\n  };\n\n  // Gestisce il cambio del testo di ricerca\n  const handleSearchTextChange = event => {\n    setSearchText(event.target.value);\n  };\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response || loadError.code === 'ECONNABORTED' || loadError.message && loadError.message.includes('Network Error')) {\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(`${API_URL}/cavi/${cantiereId}`, {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 30000 // 30 secondi\n            });\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine iniziato...');\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter(bobina => bobina.stato_bobina !== 'Terminata' && bobina.stato_bobina !== 'Over' && bobina.metri_residui > 0);\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte\n      if (selectedCavo && selectedCavo.tipologia && selectedCavo.sezione) {\n        console.log('Cavo selezionato, evidenziando bobine compatibili...');\n        console.log('Dati cavo:', {\n          id_cavo: selectedCavo.id_cavo,\n          tipologia: selectedCavo.tipologia,\n          sezione: selectedCavo.sezione\n        });\n\n        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui\n        const cavoTipologia = String(selectedCavo.tipologia || '').trim().toLowerCase();\n        const cavoSezione = String(selectedCavo.sezione || '0').trim();\n\n        // Identifica le bobine compatibili\n        const bobineCompatibili = [];\n        const bobineNonCompatibili = [];\n\n        // Dividi le bobine in compatibili e non compatibili\n        bobineUtilizzabili.forEach(bobina => {\n          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();\n          const bobinaSezione = String(bobina.sezione || '0').trim();\n\n          // Verifica compatibilità\n          const tipologiaMatch = bobinaTipologia === cavoTipologia;\n          const sezioneMatch = bobinaSezione === cavoSezione;\n          const isCompatible = tipologiaMatch && sezioneMatch;\n          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {\n            'Tipologia bobina': `\"${bobina.tipologia}\"`,\n            'Tipologia cavo': `\"${selectedCavo.tipologia}\"`,\n            'Tipologie uguali?': tipologiaMatch,\n            'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n            'Sezione cavo': `\"${String(selectedCavo.sezione)}\"`,\n            'Sezioni uguali?': sezioneMatch,\n            'Stato bobina': bobina.stato_bobina,\n            'Metri residui': bobina.metri_residui,\n            'Compatibile?': isCompatible\n          });\n          if (isCompatible) {\n            bobineCompatibili.push(bobina);\n          } else {\n            bobineNonCompatibili.push(bobina);\n          }\n        });\n        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);\n        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);\n\n        // Log dettagliato delle bobine compatibili\n        if (bobineCompatibili.length > 0) {\n          console.log('Dettaglio bobine compatibili:');\n          bobineCompatibili.forEach(bobina => {\n            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);\n          });\n        } else {\n          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n        }\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];\n\n        // Imposta le bobine nel componente\n        setBobine(bobineOrdinate);\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n        setBobine(bobineUtilizzabili);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo => cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase()));\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo => cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase());\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || exactMatch.metratura_reale && exactMatch.metratura_reale > 0) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Gestione speciale per il cambio di bobina\n    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {\n      // Verifica compatibilità tra cavo e bobina\n      const bobina = bobine.find(b => b.id_bobina === value);\n      if (bobina && selectedCavo) {\n        // Nella nuova configurazione, controlliamo solo tipologia e formazione (sezione)\n        // Utilizziamo una comparazione più robusta con trim() e gestione di null/undefined\n        const isCompatible = String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim();\n        if (!isCompatible) {\n          console.log('Bobina incompatibile selezionata:', bobina);\n          console.log('Cavo corrente:', selectedCavo);\n          console.log('Confronto formazione:', {\n            cavoFormazione: String(selectedCavo.sezione || '0'),\n            bobinaFormazione: String(bobina.sezione || '0')\n          });\n\n          // Mostra il dialogo di incompatibilità\n          setIncompatibleReelData({\n            cavo: selectedCavo,\n            bobina: bobina\n          });\n          setShowIncompatibleReelDialog(true);\n\n          // Non aggiornare il valore del form finché l'utente non conferma\n          return;\n        }\n      }\n    }\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      forceOver = true;\n      console.log('Impostando forceOver a true per garantire il completamento dell\\'operazione');\n\n      // Log delle condizioni che richiederebbero forceOver\n      if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Operazione con BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          console.log(`La bobina ${idBobina} andrà in stato OVER (metri posati ${metriPosati} > metri residui ${bobina.metri_residui})`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        console.log(`I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n\n              // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n              console.log('Impostando forceOver a true nel dialogo di conferma per garantire il completamento dell\\'operazione');\n              await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, true // Forza sempre a true per evitare blocchi\n              );\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n              if (error.response) {\n                var _error$response$data;\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message;\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      console.log('Impostando forceOver a true nella chiamata diretta per garantire il completamento dell\\'operazione');\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response) {\n        var _error$response$data2;\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.detail) || error.message;\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        sx: {\n          mb: 1,\n          fontWeight: 'bold'\n        },\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1012,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1.5,\n          mb: 2,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              mr: 1,\n              minWidth: '80px'\n            },\n            children: \"Cerca cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1019,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            label: \"ID Cavo\",\n            variant: \"outlined\",\n            value: cavoIdInput,\n            onChange: e => setCavoIdInput(e.target.value),\n            placeholder: \"Inserisci l'ID del cavo\",\n            sx: {\n              flexGrow: 0,\n              width: '200px',\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1022,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSearchCavoById,\n            disabled: caviLoading || !cavoIdInput.trim(),\n            startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 40\n            }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 73\n            }, this),\n            size: \"small\",\n            sx: {\n              minWidth: '80px',\n              height: '36px',\n              mr: 2\n            },\n            children: \"CERCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1031,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              flexGrow: 1,\n              flexWrap: 'nowrap',\n              overflow: 'hidden',\n              ml: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mr: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                sx: {\n                  fontWeight: 'bold',\n                  whiteSpace: 'nowrap',\n                  mr: 1,\n                  fontSize: '0.95rem'\n                },\n                children: [\"Cavo: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: '#1976d2'\n                  },\n                  children: selectedCavo.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1048,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                orientation: \"vertical\",\n                flexItem: true,\n                sx: {\n                  mx: 1.5\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1050,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 3,\n                  flexWrap: 'nowrap',\n                  overflow: 'hidden'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    whiteSpace: 'nowrap'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium',\n                      fontSize: '0.9rem',\n                      mr: 0.5\n                    },\n                    children: \"Tipo:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1053,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontSize: '0.9rem'\n                    },\n                    children: selectedCavo.tipologia || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1054,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1052,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    whiteSpace: 'nowrap'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium',\n                      fontSize: '0.9rem',\n                      mr: 0.5\n                    },\n                    children: \"Form:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1057,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontSize: '0.9rem'\n                    },\n                    children: selectedCavo.sezione || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1058,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1056,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    whiteSpace: 'nowrap'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium',\n                      fontSize: '0.9rem',\n                      mr: 0.5\n                    },\n                    children: \"Metri:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1061,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontSize: '0.9rem'\n                    },\n                    children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1062,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1060,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    whiteSpace: 'nowrap'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium',\n                      fontSize: '0.9rem',\n                      mr: 0.5\n                    },\n                    children: \"Stato:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1065,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: selectedCavo.stato_installazione || 'N/D',\n                    color: getCableStateColor(selectedCavo.stato_installazione),\n                    sx: {\n                      height: '22px',\n                      '& .MuiChip-label': {\n                        px: 1,\n                        py: 0,\n                        fontSize: '0.85rem'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1066,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1064,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1051,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1046,\n              columnNumber: 17\n            }, this), formData.id_bobina && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Divider, {\n                orientation: \"vertical\",\n                flexItem: true,\n                sx: {\n                  mx: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1078,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    fontWeight: 'bold',\n                    whiteSpace: 'nowrap',\n                    mr: 1,\n                    fontSize: '0.95rem',\n                    color: '#2e7d32'\n                  },\n                  children: [\"Bobina: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: formData.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(formData.id_bobina)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1081,\n                    columnNumber: 33\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1080,\n                  columnNumber: 23\n                }, this), (() => {\n                  if (formData.id_bobina === 'BOBINA_VUOTA') {\n                    return /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        whiteSpace: 'nowrap'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontSize: '0.9rem',\n                          color: 'text.secondary',\n                          fontStyle: 'italic'\n                        },\n                        children: \"(Cavo posato senza bobina specifica)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1087,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1086,\n                      columnNumber: 29\n                    }, this);\n                  }\n                  const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                  return bobina ? /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 3,\n                      flexWrap: 'nowrap',\n                      overflow: 'hidden'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        whiteSpace: 'nowrap'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontWeight: 'medium',\n                          fontSize: '0.9rem',\n                          mr: 0.5\n                        },\n                        children: \"Residui:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1098,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontSize: '0.9rem',\n                          color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main',\n                          fontWeight: 'bold'\n                        },\n                        children: [bobina.metri_residui || 0, \" m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1099,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1097,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        whiteSpace: 'nowrap'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontWeight: 'medium',\n                          fontSize: '0.9rem',\n                          mr: 0.5\n                        },\n                        children: \"Stato:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1104,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: bobina.stato_bobina || 'N/D',\n                        color: getReelStateColor(bobina.stato_bobina),\n                        sx: {\n                          height: '22px',\n                          '& .MuiChip-label': {\n                            px: 1,\n                            py: 0,\n                            fontSize: '0.85rem'\n                          }\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1105,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1103,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1096,\n                    columnNumber: 27\n                  }, this) : null;\n                })()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1079,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1045,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1018,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1017,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1.5,\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          sx: {\n            mb: 1\n          },\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1127,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1133,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1132,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            py: 0.5\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: \"Non ci sono cavi disponibili da installare.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1137,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1136,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          variant: \"outlined\",\n          sx: {\n            maxHeight: '300px',\n            overflow: 'auto',\n            width: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            size: \"small\",\n            stickyHeader: true,\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  '& th': {\n                    fontWeight: 'bold',\n                    py: 1,\n                    bgcolor: '#f5f5f5'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"ID Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1144,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Tipologia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1145,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Formazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1146,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1147,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1148,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  sx: {\n                    width: '40px'\n                  },\n                  children: \"Info\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1149,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1143,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1142,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n                hover: true,\n                onClick: () => handleCavoSelect(cavo),\n                sx: {\n                  cursor: 'pointer',\n                  '&:hover': {\n                    bgcolor: '#f1f8e9'\n                  },\n                  '& td': {\n                    py: 0.5\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 'medium'\n                  },\n                  children: cavo.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1164,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1165,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.sezione || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1166,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.metri_teorici || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1167,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: isCableSpare(cavo) ? 'SPARE' : isCableInstalled(cavo) ? 'Installato' : cavo.stato_installazione,\n                    color: isCableSpare(cavo) ? 'error' : isCableInstalled(cavo) ? 'success' : getCableStateColor(cavo.stato_installazione),\n                    sx: {\n                      height: '20px',\n                      '& .MuiChip-label': {\n                        px: 1,\n                        py: 0,\n                        fontSize: '0.7rem'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1169,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1168,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: e => {\n                      e.stopPropagation();\n                      setSelectedCavo(cavo);\n                      setShowCavoDetailsDialog(true);\n                    },\n                    children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1185,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1177,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1176,\n                  columnNumber: 23\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1154,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1152,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1141,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1140,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1011,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        sx: {\n          mb: 1,\n          fontWeight: 'bold'\n        },\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold'\n          },\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2,\n            mb: 2,\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1,\n              width: '200px'\n            },\n            inputProps: {\n              max: 999999,\n              // Limite a 6 cifre\n              step: 0.1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1220,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1216,\n          columnNumber: 11\n        }, this), formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: formWarnings.metri_posati\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1242,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1248,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1209,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1204,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Filtra le bobine in base al testo di ricerca\n    const bobineFiltrate = bobine.filter(bobina => {\n      // Filtro per testo di ricerca\n      const searchLower = searchText.toLowerCase();\n      const matchesSearch = !searchText || getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower) || String(bobina.tipologia || '').toLowerCase().includes(searchLower) || String(bobina.sezione || '').toLowerCase().includes(searchLower);\n      return matchesSearch;\n    });\n\n    // Separa le bobine compatibili e non compatibili\n    const bobineCompatibili = selectedCavo ? bobineFiltrate.filter(bobina => String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim()) : bobineFiltrate;\n    const bobineNonCompatibili = selectedCavo ? bobineFiltrate.filter(bobina => String(bobina.tipologia || '').trim() !== String(selectedCavo.tipologia || '').trim() || String(bobina.sezione || '0').trim() !== String(selectedCavo.sezione || '0').trim()) : [];\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = e => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo) {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n            const bobinaTipologia = String(bobinaEsistente.tipologia || '');\n            const bobinaSezione = String(bobinaEsistente.sezione || '0');\n\n            // Log per debug\n            console.log(`Verifica compatibilità bobina ${bobinaEsistente.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              formazione: `${bobinaSezione} === ${cavoSezione}`\n            });\n            if (bobinaTipologia !== cavoTipologia || bobinaSezione !== cavoSezione) {\n              // Mostra il dialogo per bobine incompatibili\n              setIncompatibleReelData({\n                cavo: selectedCavo,\n                bobina: bobinaEsistente\n              });\n              setShowIncompatibleReelDialog(true);\n              return;\n            }\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 'bold',\n            display: 'inline',\n            fontSize: '1.1rem'\n          },\n          children: \"Associa bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            display: 'inline',\n            ml: 1,\n            color: 'text.secondary'\n          },\n          children: \"(Seleziona una bobina da associare al cavo o usa \\\"BOBINA VUOTA\\\" se non desideri associare una bobina specifica)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1401,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1397,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1409,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              width: '200px'\n            },\n            inputProps: {\n              max: 999999,\n              // Limite a 6 cifre\n              step: 0.1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1412,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1408,\n          columnNumber: 11\n        }, this), formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: formWarnings.metri_posati\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1434,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1439,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2,\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              size: \"small\",\n              label: \"Cerca\",\n              variant: \"outlined\",\n              value: searchText,\n              onChange: handleSearchTextChange,\n              placeholder: \"ID, tipologia...\",\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1455,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1454,\n                  columnNumber: 21\n                }, this),\n                endAdornment: searchText ? /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    \"aria-label\": \"clear search\",\n                    onClick: () => setSearchText(''),\n                    edge: \"end\",\n                    children: /*#__PURE__*/_jsxDEV(CancelIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1466,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1460,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1459,\n                  columnNumber: 21\n                }, this) : null\n              },\n              sx: {\n                width: '200px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1445,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              onClick: () => handleSelectBobina('BOBINA_VUOTA'),\n              sx: {\n                height: '40px'\n              },\n              children: \"BOBINA VUOTA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1474,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1443,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1441,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1487,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1486,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 'bold',\n                display: 'block',\n                mb: 1\n              },\n              children: \"Inserimento diretto\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1493,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'flex-start',\n                gap: 1,\n                maxWidth: '300px'\n              },\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                size: \"small\",\n                label: \"Numero bobina\",\n                variant: \"outlined\",\n                placeholder: \"Solo Y\",\n                error: !!formErrors.id_bobina_input,\n                onBlur: handleBobinaNumberInput,\n                sx: {\n                  flex: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1497,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1496,\n              columnNumber: 17\n            }, this), formErrors.id_bobina_input && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"error\",\n              sx: {\n                display: 'block',\n                mt: 0.5\n              },\n              children: formErrors.id_bobina_input\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1508,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1492,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Paper, {\n                variant: \"outlined\",\n                sx: {\n                  p: 2,\n                  height: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    fontWeight: 'bold',\n                    mb: 1\n                  },\n                  children: \"ELENCO BOBINE COMPATIBILI\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1520,\n                  columnNumber: 21\n                }, this), bobineCompatibili.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      width: '100%',\n                      py: 0.8,\n                      px: 1.8,\n                      bgcolor: '#f5f5f5',\n                      borderRadius: 1,\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '60px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"ID\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1528,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1527,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '120px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Tipo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1531,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1530,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '100px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Form.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1534,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1533,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '100px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Residui\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1537,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1536,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        flexGrow: 0\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Stato\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1540,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1539,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1526,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(List, {\n                    sx: {\n                      maxHeight: '300px',\n                      overflow: 'auto',\n                      bgcolor: 'background.paper'\n                    },\n                    children: bobineCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n                      disablePadding: true,\n                      secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                        edge: \"end\",\n                        size: \"small\",\n                        onClick: () => handleSelectBobina(bobina.id_bobina),\n                        disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                        children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                          color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'disabled' : 'primary'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1555,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1549,\n                        columnNumber: 31\n                      }, this),\n                      sx: {\n                        bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                        borderRadius: '4px',\n                        mb: 0.5,\n                        border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                        dense: true,\n                        onClick: () => handleSelectBobina(bobina.id_bobina),\n                        disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                        children: /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            width: '100%',\n                            py: 0.8\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '60px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontWeight: 'bold',\n                                fontSize: '0.9rem'\n                              },\n                              children: getBobinaNumber(bobina.id_bobina)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1572,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1571,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '120px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontSize: '0.85rem'\n                              },\n                              children: bobina.tipologia || 'N/A'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1577,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1576,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '100px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontSize: '0.85rem'\n                              },\n                              children: bobina.sezione || 'N/A'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1582,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1581,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '100px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontWeight: 'bold',\n                                fontSize: '0.85rem',\n                                color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                              },\n                              children: [bobina.metri_residui || 0, \" m\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1587,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1586,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              flexGrow: 0\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Chip, {\n                              size: \"small\",\n                              label: bobina.stato_bobina || 'N/D',\n                              color: getReelStateColor(bobina.stato_bobina),\n                              variant: \"outlined\",\n                              sx: {\n                                height: 22,\n                                fontSize: '0.8rem',\n                                '& .MuiChip-label': {\n                                  px: 1,\n                                  py: 0\n                                }\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1592,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1591,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1570,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1565,\n                        columnNumber: 29\n                      }, this)\n                    }, bobina.id_bobina, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1545,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1543,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(Alert, {\n                  severity: \"info\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: \"Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1607,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1519,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1518,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Paper, {\n                variant: \"outlined\",\n                sx: {\n                  p: 2,\n                  height: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    fontWeight: 'bold',\n                    mb: 1\n                  },\n                  children: \"ELENCO BOBINE NON COMPATIBILI\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1617,\n                  columnNumber: 21\n                }, this), bobineNonCompatibili.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      width: '100%',\n                      py: 0.8,\n                      px: 1.8,\n                      bgcolor: '#f5f5f5',\n                      borderRadius: 1,\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '60px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"ID\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1625,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1624,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '120px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Tipo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1628,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1627,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '100px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Form.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1631,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1630,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '100px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Residui\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1634,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1633,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        flexGrow: 0\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Stato\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1637,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1636,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1623,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(List, {\n                    sx: {\n                      maxHeight: '300px',\n                      overflow: 'auto',\n                      bgcolor: 'background.paper'\n                    },\n                    children: bobineNonCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n                      disablePadding: true,\n                      secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                        edge: \"end\",\n                        size: \"small\",\n                        onClick: () => handleSelectBobina(bobina.id_bobina),\n                        disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                        children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                          color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'disabled' : 'primary'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1652,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1646,\n                        columnNumber: 31\n                      }, this),\n                      sx: {\n                        bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                        borderRadius: '4px',\n                        mb: 0.5,\n                        border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                        dense: true,\n                        onClick: () => handleSelectBobina(bobina.id_bobina),\n                        disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                        children: /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            width: '100%',\n                            py: 0.8\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '60px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontWeight: 'bold',\n                                fontSize: '0.9rem'\n                              },\n                              children: getBobinaNumber(bobina.id_bobina)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1669,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1668,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '120px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontSize: '0.85rem'\n                              },\n                              children: bobina.tipologia || 'N/A'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1674,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1673,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '100px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontSize: '0.85rem'\n                              },\n                              children: bobina.sezione || 'N/A'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1679,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1678,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '100px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontWeight: 'bold',\n                                fontSize: '0.85rem',\n                                color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                              },\n                              children: [bobina.metri_residui || 0, \" m\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1684,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1683,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              display: 'flex',\n                              gap: 1\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Chip, {\n                              size: \"small\",\n                              label: bobina.stato_bobina || 'N/D',\n                              color: getReelStateColor(bobina.stato_bobina),\n                              variant: \"outlined\",\n                              sx: {\n                                height: 22,\n                                fontSize: '0.8rem',\n                                '& .MuiChip-label': {\n                                  px: 1,\n                                  py: 0\n                                }\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1689,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                              size: \"small\",\n                              label: \"Non comp.\",\n                              color: \"warning\",\n                              variant: \"outlined\",\n                              sx: {\n                                height: 22,\n                                fontSize: '0.8rem',\n                                '& .MuiChip-label': {\n                                  px: 1,\n                                  py: 0\n                                }\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1696,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1688,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1667,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1662,\n                        columnNumber: 29\n                      }, this)\n                    }, bobina.id_bobina, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1642,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1640,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(Alert, {\n                  severity: \"info\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: \"Nessuna bobina non compatibile disponibile con i filtri attuali.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1711,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1616,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1615,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1516,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1490,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1724,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'flex-end',\n            mt: 3,\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"secondary\",\n            onClick: handleReset,\n            disabled: loading,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1731,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSubmit,\n            disabled: loading || !selectedCavo || !formData.metri_posati || !formData.id_bobina,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1744,\n              columnNumber: 36\n            }, this) : null,\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1739,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1730,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1406,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1396,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1774,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1779,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo,\n          compact: true,\n          title: \"Dettagli del cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1784,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Informazioni sull'operazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1792,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1798,\n                  columnNumber: 19\n                }, this), \" \", formData.metri_posati, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1797,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato Installazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1801,\n                  columnNumber: 19\n                }, this), \" \", statoInstallazione]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1800,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1796,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina Associata:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1806,\n                  columnNumber: 19\n                }, this), \" \", numeroBobina]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1805,\n                columnNumber: 17\n              }, this), bobinaInfo && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Residui Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1810,\n                  columnNumber: 21\n                }, this), \" \", bobinaInfo.metri_residui, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1809,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1804,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1795,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1791,\n          columnNumber: 11\n        }, this), bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1819,\n            columnNumber: 15\n          }, this), \" I metri posati (\", formData.metri_posati, \"m) superano i metri residui della bobina (\", bobinaInfo.metri_residui, \"m). Questo porter\\xE0 la bobina in stato OVER.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1818,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1824,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1778,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1773,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      // Seleziona Cavo\n      case 1:\n        return renderStep3();\n      // Associa Bobina\n      case 2:\n        return renderStep2();\n      // Inserisci Metri\n      case 3:\n        return renderStep4();\n      // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({\n      cavo: null,\n      bobina: null\n    });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    const {\n      cavo,\n      bobina\n    } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per aggiornare il cavo:', {\n        cavo,\n        bobina\n      });\n      onError('Dati mancanti per aggiornare il cavo');\n      return;\n    }\n    try {\n      setLoading(true);\n      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);\n      console.log('Dati cavo prima dell\\'aggiornamento:', cavo);\n      console.log('Dati bobina:', bobina);\n\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      console.log('Dati cavo dopo l\\'aggiornamento:', updatedCavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: bobina.id_bobina\n      });\n\n      // Ricarica le bobine per aggiornare l'interfaccia\n      await loadBobine();\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per corrispondere alla bobina ${bobina.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1.5,\n          mb: 2,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              mr: 1,\n              minWidth: '80px'\n            },\n            children: \"Cerca cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1938,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            label: \"ID Cavo\",\n            variant: \"outlined\",\n            value: cavoIdInput,\n            onChange: e => setCavoIdInput(e.target.value),\n            placeholder: \"Inserisci l'ID del cavo\",\n            sx: {\n              flexGrow: 0,\n              width: '200px',\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1941,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSearchCavoById,\n            disabled: caviLoading || !cavoIdInput.trim(),\n            startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1955,\n              columnNumber: 40\n            }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1955,\n              columnNumber: 73\n            }, this),\n            size: \"small\",\n            sx: {\n              minWidth: '80px',\n              height: '36px',\n              mr: 2\n            },\n            children: \"CERCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1950,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              flexGrow: 1,\n              flexWrap: 'nowrap',\n              overflow: 'hidden',\n              ml: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 'bold',\n                whiteSpace: 'nowrap',\n                mr: 1,\n                fontSize: '0.9rem'\n              },\n              children: [\"Cavo: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#1976d2'\n                },\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1966,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1965,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"vertical\",\n              flexItem: true,\n              sx: {\n                mx: 1.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1968,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 3,\n                flexWrap: 'nowrap',\n                overflow: 'hidden'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.85rem',\n                    mr: 0.5\n                  },\n                  children: \"Tipo:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1971,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.85rem'\n                  },\n                  children: selectedCavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1972,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1970,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.85rem',\n                    mr: 0.5\n                  },\n                  children: \"Form:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1975,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.85rem'\n                  },\n                  children: selectedCavo.sezione || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1976,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1974,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.85rem',\n                    mr: 0.5\n                  },\n                  children: \"Metri:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1979,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.85rem'\n                  },\n                  children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1980,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1978,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.85rem',\n                    mr: 0.5\n                  },\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1983,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: selectedCavo.stato_installazione || 'N/D',\n                  color: getCableStateColor(selectedCavo.stato_installazione),\n                  sx: {\n                    height: '20px',\n                    '& .MuiChip-label': {\n                      px: 1,\n                      py: 0,\n                      fontSize: '0.8rem'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1984,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1982,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1969,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1964,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1937,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1936,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1997,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1935,\n      columnNumber: 7\n    }, this), showSearchResults && searchResults.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Risultati della ricerca\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2003,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                bgcolor: '#f5f5f5'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2010,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2011,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2012,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2013,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2014,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2015,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2016,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2009,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2008,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: searchResults.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2022,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2023,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.sezione || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2024,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2025,\n                  columnNumber: 71\n                }, this), \"A: \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2025,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2026,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: cavo.stato_installazione || 'N/D',\n                  size: \"small\",\n                  color: getCableStateColor(cavo.stato_installazione),\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2028,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2027,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  variant: \"contained\",\n                  color: \"primary\",\n                  onClick: () => handleCavoSelect(cavo),\n                  disabled: isCableInstalled(cavo),\n                  children: \"Seleziona\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2036,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2035,\n                columnNumber: 21\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2021,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2019,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2007,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2006,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2002,\n      columnNumber: 9\n    }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Box, {\n      children: renderStep3()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2055,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showConfirmDialog,\n      onClose: () => setShowConfirmDialog(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2064,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: confirmDialogProps.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2065,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2063,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2062,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mt: 2\n          },\n          children: confirmDialogProps.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2069,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2068,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowConfirmDialog(false),\n          color: \"secondary\",\n          variant: \"outlined\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2074,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setShowConfirmDialog(false);\n            confirmDialogProps.onConfirm();\n          },\n          color: \"primary\",\n          variant: \"contained\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2077,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2073,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2061,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2095,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavo gi\\xE0 posato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2096,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2094,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2093,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: alreadyLaidCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: alreadyLaidCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2103,\n              columnNumber: 25\n            }, this), \" risulta gi\\xE0 posato (\", alreadyLaidCavo.metratura_reale || 0, \"m).\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2102,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: \"Puoi scegliere di:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2105,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"ul\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata al cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2109,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2110,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2111,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2108,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2101,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2099,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2121,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2092,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: handleCloseIncompatibleReelDialog,\n      cavo: incompatibleReelData.cavo,\n      bobina: incompatibleReelData.bobina,\n      onUpdateCavo: handleUpdateCavoToMatchReel,\n      onSelectAnotherReel: handleSelectAnotherReel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2132,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Dettagli Cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2151,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2149,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2155,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          color: \"primary\",\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2158,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2142,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1933,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"3yQvF7cITe0apgtUg91Zz7kARz8=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "InputAdornment", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "List", "ListItem", "ListItemButton", "ListItemText", "ListItemSecondaryAction", "Search", "SearchIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "AddCircleOutline", "AddCircleOutlineIcon", "useNavigate", "caviService", "axiosInstance", "IncompatibleReelDialog", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchResults", "setSearchResults", "showSearchResults", "setShowSearchResults", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "activeStep", "setActiveStep", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReel", "setIncompatibleReel", "incompatibleReelData", "setIncompatibleReelData", "cavo", "bobina", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "searchText", "setSearchText", "alreadyLaidCavo", "setAlreadyLaidCavo", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "handleSelectBobina", "idBobina", "console", "log", "prev", "id_bobina_input", "<PERSON><PERSON><PERSON><PERSON>", "find", "b", "handleSearchTextChange", "event", "target", "value", "loadBobine", "getBobinaNumber", "includes", "split", "loadCavi", "caviData", "get<PERSON><PERSON>", "length", "loadError", "error", "isNetworkError", "response", "code", "message", "Promise", "resolve", "setTimeout", "token", "localStorage", "getItem", "API_URL", "defaults", "baseURL", "retryResponse", "get", "headers", "timeout", "data", "retryError", "errorMessage", "detail", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "stato_bobina", "metri_residui", "tipologia", "sezione", "cavoTipologia", "String", "trim", "toLowerCase", "cavoSezione", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "bobineNonCompatibili", "for<PERSON>ach", "bobinaTipologia", "bobinaSezione", "tipologiaMatch", "sezioneMatch", "isCompatible", "push", "sort", "a", "bobineOrdinate", "handleSearchCavoById", "filteredCavi", "exactMatch", "stato_installazione", "metratura_reale", "modificato_manualmente", "handleCavoSelect", "status", "window", "confirm", "reactivateSpare", "then", "updatedCavo", "catch", "n_conduttori", "cavoId", "handleFormChange", "e", "name", "cavoFormazione", "bobinaFormazione", "validateField", "warning", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "notificationShown", "setNotificationShown", "showConfirmDialog", "setShowConfirmDialog", "confirmDialogProps", "setConfirmDialogProps", "title", "onConfirm", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "handleNext", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "metriTeorici", "handleSubmit", "statoInstallazione", "forceOver", "confirmMessage", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "success", "_error$response$data", "request", "_error$response$data2", "renderStep1", "children", "variant", "sx", "mb", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "width", "display", "alignItems", "mr", "min<PERSON><PERSON><PERSON>", "size", "label", "onChange", "placeholder", "flexGrow", "color", "onClick", "disabled", "startIcon", "fontSize", "height", "flexWrap", "overflow", "ml", "whiteSpace", "style", "orientation", "flexItem", "mx", "gap", "px", "py", "fontStyle", "justifyContent", "my", "severity", "component", "maxHeight", "<PERSON><PERSON><PERSON><PERSON>", "bgcolor", "align", "map", "hover", "cursor", "stopPropagation", "renderStep2", "gutterBottom", "mt", "type", "helperText", "FormHelperTextProps", "inputProps", "max", "step", "renderStep3", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "bobineFiltrate", "searchLower", "matchesSearch", "handleBobinaNumberInput", "idBobinaCompleto", "<PERSON><PERSON><PERSON>", "formazione", "InputProps", "startAdornment", "position", "endAdornment", "edge", "max<PERSON><PERSON><PERSON>", "onBlur", "flex", "container", "spacing", "item", "xs", "md", "borderRadius", "disablePadding", "secondaryAction", "border", "dense", "renderStep4", "bobinaInfo", "compact", "getStepContent", "handleCloseAlreadyLaidDialog", "handleModifyReel", "handleSelectAnotherCable", "handleCloseIncompatibleReelDialog", "handleUpdateCavoToMatchReel", "updateCavoForCompatibility", "getCavoById", "handleSelectAnotherReel", "ubicazione_partenza", "ubicazione_arrivo", "open", "onClose", "fullWidth", "autoFocus", "paragraph", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip,\n  InputAdornment,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  ListItemSecondaryAction\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon,\n  AddCircleOutline as AddCircleOutlineIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null); // Mantenuto per compatibilità\n  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n\n  // Stati per i filtri delle bobine\n  const [searchText, setSearchText] = useState('');\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = (idBobina) => {\n    console.log('Bobina selezionata:', idBobina);\n    setFormData({\n      ...formData,\n      id_bobina: idBobina\n    });\n\n    // Reset degli errori\n    setFormErrors(prev => ({\n      ...prev,\n      id_bobina: null,\n      id_bobina_input: null\n    }));\n\n    // Forza il re-render per mostrare le informazioni della bobina selezionata\n    const selectedBobina = bobine.find(b => b.id_bobina === idBobina);\n    if (selectedBobina) {\n      console.log('Dettagli bobina selezionata:', selectedBobina);\n    }\n  };\n\n\n  // Gestisce il cambio del testo di ricerca\n  const handleSearchTextChange = (event) => {\n    setSearchText(event.target.value);\n  };\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response ||\n            loadError.code === 'ECONNABORTED' ||\n            (loadError.message && loadError.message.includes('Network Error'))) {\n\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(\n              `${API_URL}/cavi/${cantiereId}`,\n              {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 30000 // 30 secondi\n              }\n            );\n\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine iniziato...');\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter(bobina =>\n        bobina.stato_bobina !== 'Terminata' &&\n        bobina.stato_bobina !== 'Over' &&\n        bobina.metri_residui > 0\n      );\n\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte\n      if (selectedCavo && selectedCavo.tipologia && selectedCavo.sezione) {\n        console.log('Cavo selezionato, evidenziando bobine compatibili...');\n        console.log('Dati cavo:', {\n          id_cavo: selectedCavo.id_cavo,\n          tipologia: selectedCavo.tipologia,\n          sezione: selectedCavo.sezione\n        });\n\n        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui\n        const cavoTipologia = String(selectedCavo.tipologia || '').trim().toLowerCase();\n        const cavoSezione = String(selectedCavo.sezione || '0').trim();\n\n        // Identifica le bobine compatibili\n        const bobineCompatibili = [];\n        const bobineNonCompatibili = [];\n\n        // Dividi le bobine in compatibili e non compatibili\n        bobineUtilizzabili.forEach(bobina => {\n          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();\n          const bobinaSezione = String(bobina.sezione || '0').trim();\n\n          // Verifica compatibilità\n          const tipologiaMatch = bobinaTipologia === cavoTipologia;\n          const sezioneMatch = bobinaSezione === cavoSezione;\n          const isCompatible = tipologiaMatch && sezioneMatch;\n\n          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {\n            'Tipologia bobina': `\"${bobina.tipologia}\"`,\n            'Tipologia cavo': `\"${selectedCavo.tipologia}\"`,\n            'Tipologie uguali?': tipologiaMatch,\n            'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n            'Sezione cavo': `\"${String(selectedCavo.sezione)}\"`,\n            'Sezioni uguali?': sezioneMatch,\n            'Stato bobina': bobina.stato_bobina,\n            'Metri residui': bobina.metri_residui,\n            'Compatibile?': isCompatible\n          });\n\n          if (isCompatible) {\n            bobineCompatibili.push(bobina);\n          } else {\n            bobineNonCompatibili.push(bobina);\n          }\n        });\n\n        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);\n        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);\n\n        // Log dettagliato delle bobine compatibili\n        if (bobineCompatibili.length > 0) {\n          console.log('Dettaglio bobine compatibili:');\n          bobineCompatibili.forEach(bobina => {\n            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);\n          });\n        } else {\n          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n        }\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];\n\n        // Imposta le bobine nel componente\n        setBobine(bobineOrdinate);\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n        setBobine(bobineUtilizzabili);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase())\n      );\n\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo =>\n        cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase()\n      );\n\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || (exactMatch.metratura_reale && exactMatch.metratura_reale > 0)) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Gestione speciale per il cambio di bobina\n    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {\n      // Verifica compatibilità tra cavo e bobina\n      const bobina = bobine.find(b => b.id_bobina === value);\n\n      if (bobina && selectedCavo) {\n        // Nella nuova configurazione, controlliamo solo tipologia e formazione (sezione)\n        // Utilizziamo una comparazione più robusta con trim() e gestione di null/undefined\n        const isCompatible =\n          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n          String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim();\n\n        if (!isCompatible) {\n          console.log('Bobina incompatibile selezionata:', bobina);\n          console.log('Cavo corrente:', selectedCavo);\n          console.log('Confronto formazione:', {\n            cavoFormazione: String(selectedCavo.sezione || '0'),\n            bobinaFormazione: String(bobina.sezione || '0')\n          });\n\n          // Mostra il dialogo di incompatibilità\n          setIncompatibleReelData({\n            cavo: selectedCavo,\n            bobina: bobina\n          });\n          setShowIncompatibleReelDialog(true);\n\n          // Non aggiornare il valore del form finché l'utente non conferma\n          return;\n        }\n      }\n    }\n\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      forceOver = true;\n      console.log('Impostando forceOver a true per garantire il completamento dell\\'operazione');\n\n      // Log delle condizioni che richiederebbero forceOver\n      if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Operazione con BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          console.log(`La bobina ${idBobina} andrà in stato OVER (metri posati ${metriPosati} > metri residui ${bobina.metri_residui})`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        console.log(`I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n\n              // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n              console.log('Impostando forceOver a true nel dialogo di conferma per garantire il completamento dell\\'operazione');\n              await caviService.updateMetriPosati(\n                cantiereId,\n                formData.id_cavo,\n                metriPosati,\n                idBobina,\n                true // Forza sempre a true per evitare blocchi\n              );\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n              if (error.response) {\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = error.response.data?.detail || error.message;\n\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      console.log('Impostando forceOver a true nella chiamata diretta per garantire il completamento dell\\'operazione');\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n      if (error.response) {\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = error.response.data?.detail || error.message;\n\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 'bold' }}>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID - Versione compatta con dettagli cavo selezionato */}\n        <Paper sx={{ p: 1.5, mb: 2, width: '100%' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\n            <Typography variant=\"subtitle2\" sx={{ mr: 1, minWidth: '80px' }}>\n              Cerca cavo\n            </Typography>\n            <TextField\n              size=\"small\"\n              label=\"ID Cavo\"\n              variant=\"outlined\"\n              value={cavoIdInput}\n              onChange={(e) => setCavoIdInput(e.target.value)}\n              placeholder=\"Inserisci l'ID del cavo\"\n              sx={{ flexGrow: 0, width: '200px', mr: 1 }}\n            />\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSearchCavoById}\n              disabled={caviLoading || !cavoIdInput.trim()}\n              startIcon={caviLoading ? <CircularProgress size={16} /> : <SearchIcon fontSize=\"small\" />}\n              size=\"small\"\n              sx={{ minWidth: '80px', height: '36px', mr: 2 }}\n            >\n              CERCA\n            </Button>\n\n            {/* Dettagli cavo e bobina selezionati in riga singola */}\n            {selectedCavo && (\n              <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, flexWrap: 'nowrap', overflow: 'hidden', ml: 4 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem' }}>\n                    Cavo: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>\n                  </Typography>\n                  <Divider orientation=\"vertical\" flexItem sx={{ mx: 1.5 }} />\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Tipo:</Typography>\n                      <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Form:</Typography>\n                      <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Metri:</Typography>\n                      <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedCavo.stato_installazione || 'N/D'}\n                        color={getCableStateColor(selectedCavo.stato_installazione)}\n                        sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}\n                      />\n                    </Box>\n                  </Box>\n                </Box>\n\n                {formData.id_bobina && (\n                  <>\n                    <Divider orientation=\"vertical\" flexItem sx={{ mx: 2 }} />\n                    <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                      <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem', color: '#2e7d32' }}>\n                        Bobina: <span>{formData.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(formData.id_bobina)}</span>\n                      </Typography>\n                      {(() => {\n                        if (formData.id_bobina === 'BOBINA_VUOTA') {\n                          return (\n                            <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                              <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: 'text.secondary', fontStyle: 'italic' }}>\n                                (Cavo posato senza bobina specifica)\n                              </Typography>\n                            </Box>\n                          );\n                        }\n\n                        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                        return bobina ? (\n                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>\n                            <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Residui:</Typography>\n                              <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main', fontWeight: 'bold' }}>\n                                {bobina.metri_residui || 0} m\n                              </Typography>\n                            </Box>\n                            <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>\n                              <Chip\n                                size=\"small\"\n                                label={bobina.stato_bobina || 'N/D'}\n                                color={getReelStateColor(bobina.stato_bobina)}\n                                sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}\n                              />\n                            </Box>\n                          </Box>\n                        ) : null;\n                      })()}\n                    </Box>\n                  </>\n                )}\n\n\n              </Box>\n            )}\n          </Box>\n        </Paper>\n\n        {/* Lista cavi - versione compatta */}\n        <Paper sx={{ p: 1.5, width: '100%' }}>\n          <Typography variant=\"subtitle2\" sx={{ mb: 1 }}>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n              <CircularProgress size={24} />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\" sx={{ py: 0.5 }}>\n              <Typography variant=\"caption\">Non ci sono cavi disponibili da installare.</Typography>\n            </Alert>\n          ) : (\n            <TableContainer component={Paper} variant=\"outlined\" sx={{ maxHeight: '300px', overflow: 'auto', width: '100%' }}>\n              <Table size=\"small\" stickyHeader>\n                <TableHead>\n                  <TableRow sx={{ '& th': { fontWeight: 'bold', py: 1, bgcolor: '#f5f5f5' } }}>\n                    <TableCell>ID Cavo</TableCell>\n                    <TableCell>Tipologia</TableCell>\n                    <TableCell>Formazione</TableCell>\n                    <TableCell>Metri</TableCell>\n                    <TableCell>Stato</TableCell>\n                    <TableCell align=\"center\" sx={{ width: '40px' }}>Info</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {cavi.map((cavo) => (\n                    <TableRow\n                      key={cavo.id_cavo}\n                      hover\n                      onClick={() => handleCavoSelect(cavo)}\n                      sx={{\n                        cursor: 'pointer',\n                        '&:hover': { bgcolor: '#f1f8e9' },\n                        '& td': { py: 0.5 }\n                      }}\n                    >\n                      <TableCell sx={{ fontWeight: 'medium' }}>{cavo.id_cavo}</TableCell>\n                      <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                      <TableCell>{cavo.sezione || 'N/A'}</TableCell>\n                      <TableCell>{cavo.metri_teorici || 'N/A'}</TableCell>\n                      <TableCell>\n                        <Chip\n                          size=\"small\"\n                          label={isCableSpare(cavo) ? 'SPARE' : isCableInstalled(cavo) ? 'Installato' : cavo.stato_installazione}\n                          color={isCableSpare(cavo) ? 'error' : isCableInstalled(cavo) ? 'success' : getCableStateColor(cavo.stato_installazione)}\n                          sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.7rem' } }}\n                        />\n                      </TableCell>\n                      <TableCell align=\"center\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            setSelectedCavo(cavo);\n                            setShowCavoDetailsDialog(true);\n                          }}\n                        >\n                          <InfoIcon fontSize=\"small\" />\n                        </IconButton>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 'bold' }}>\n          Inserisci metri posati\n        </Typography>\n\n        <Paper sx={{ p: 2, width: '100%' }}>\n          <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n            Inserisci i metri posati\n          </Typography>\n\n\n\n          <Box sx={{ mt: 2, mb: 2, width: '100%' }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Metratura posata\n            </Typography>\n            <TextField\n              size=\"small\"\n              label=\"Metri posati\"\n              variant=\"outlined\"\n              name=\"metri_posati\"\n              type=\"number\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              sx={{ mb: 1, width: '200px' }}\n              inputProps={{\n                max: 999999, // Limite a 6 cifre\n                step: 0.1\n              }}\n            />\n          </Box>\n\n          {formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              {formWarnings.metri_posati}\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\">\n              Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n            </Typography>\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Filtra le bobine in base al testo di ricerca\n    const bobineFiltrate = bobine.filter(bobina => {\n      // Filtro per testo di ricerca\n      const searchLower = searchText.toLowerCase();\n      const matchesSearch = !searchText ||\n        getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower) ||\n        String(bobina.tipologia || '').toLowerCase().includes(searchLower) ||\n        String(bobina.sezione || '').toLowerCase().includes(searchLower);\n\n      return matchesSearch;\n    });\n\n    // Separa le bobine compatibili e non compatibili\n    const bobineCompatibili = selectedCavo\n      ? bobineFiltrate.filter(bobina =>\n          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n          String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim())\n      : bobineFiltrate;\n\n    const bobineNonCompatibili = selectedCavo\n      ? bobineFiltrate.filter(bobina =>\n          String(bobina.tipologia || '').trim() !== String(selectedCavo.tipologia || '').trim() ||\n          String(bobina.sezione || '0').trim() !== String(selectedCavo.sezione || '0').trim())\n      : [];\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = (e) => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo) {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n\n            const bobinaTipologia = String(bobinaEsistente.tipologia || '');\n            const bobinaSezione = String(bobinaEsistente.sezione || '0');\n\n            // Log per debug\n            console.log(`Verifica compatibilità bobina ${bobinaEsistente.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              formazione: `${bobinaSezione} === ${cavoSezione}`\n            });\n\n            if (bobinaTipologia !== cavoTipologia ||\n                bobinaSezione !== cavoSezione) {\n              // Mostra il dialogo per bobine incompatibili\n              setIncompatibleReelData({\n                cavo: selectedCavo,\n                bobina: bobinaEsistente\n              });\n              setShowIncompatibleReelDialog(true);\n              return;\n            }\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n\n    return (\n      <Box>\n        <Box sx={{ mb: 1 }}>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold', display: 'inline', fontSize: '1.1rem' }}>\n            Associa bobina\n          </Typography>\n          <Typography variant=\"body2\" sx={{ display: 'inline', ml: 1, color: 'text.secondary' }}>\n            (Seleziona una bobina da associare al cavo o usa \"BOBINA VUOTA\" se non desideri associare una bobina specifica)\n          </Typography>\n        </Box>\n\n        <Paper sx={{ p: 2, width: '100%' }}>\n          {/* Campo per l'inserimento dei metri posati */}\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Metratura posata\n            </Typography>\n            <TextField\n              size=\"small\"\n              label=\"Metri posati\"\n              variant=\"outlined\"\n              name=\"metri_posati\"\n              type=\"number\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              sx={{ width: '200px' }}\n              inputProps={{\n                max: 999999, // Limite a 6 cifre\n                step: 0.1\n              }}\n            />\n          </Box>\n\n          {formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              {formWarnings.metri_posati}\n            </Alert>\n          )}\n\n          <Divider sx={{ my: 2 }} />\n\n          <Box sx={{ mb: 2 }}>\n\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>\n              {/* Campo di ricerca - versione compatta */}\n              <TextField\n                size=\"small\"\n                label=\"Cerca\"\n                variant=\"outlined\"\n                value={searchText}\n                onChange={handleSearchTextChange}\n                placeholder=\"ID, tipologia...\"\n                InputProps={{\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <SearchIcon fontSize=\"small\" />\n                    </InputAdornment>\n                  ),\n                  endAdornment: searchText ? (\n                    <InputAdornment position=\"end\">\n                      <IconButton\n                        size=\"small\"\n                        aria-label=\"clear search\"\n                        onClick={() => setSearchText('')}\n                        edge=\"end\"\n                      >\n                        <CancelIcon fontSize=\"small\" />\n                      </IconButton>\n                    </InputAdornment>\n                  ) : null\n                }}\n                sx={{ width: '200px' }}\n              />\n\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => handleSelectBobina('BOBINA_VUOTA')}\n                sx={{ height: '40px' }}\n              >\n                BOBINA VUOTA\n              </Button>\n            </Box>\n          </Box>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 1 }}>\n              <CircularProgress size={24} />\n            </Box>\n          ) : (\n            <Box>\n              {/* Input diretto - versione compatta */}\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', display: 'block', mb: 1 }}>\n                  Inserimento diretto\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, maxWidth: '300px' }}>\n                  <TextField\n                    size=\"small\"\n                    label=\"Numero bobina\"\n                    variant=\"outlined\"\n                    placeholder=\"Solo Y\"\n                    error={!!formErrors.id_bobina_input}\n                    onBlur={handleBobinaNumberInput}\n                    sx={{ flex: 1 }}\n                  />\n                </Box>\n                {formErrors.id_bobina_input && (\n                  <Typography variant=\"caption\" color=\"error\" sx={{ display: 'block', mt: 0.5 }}>\n                    {formErrors.id_bobina_input}\n                  </Typography>\n                )}\n\n              </Box>\n\n              {/* Griglia per le due liste di bobine */}\n              <Grid container spacing={2}>\n                {/* Colonna sinistra: Bobine compatibili */}\n                <Grid item xs={12} md={6}>\n                  <Paper variant=\"outlined\" sx={{ p: 2, height: '100%' }}>\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                      ELENCO BOBINE COMPATIBILI\n                    </Typography>\n\n                    {bobineCompatibili.length > 0 ? (\n                      <>\n                        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>\n                          <Box sx={{ width: '60px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>\n                          </Box>\n                          <Box sx={{ width: '120px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>\n                          </Box>\n                          <Box sx={{ width: '100px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>\n                          </Box>\n                          <Box sx={{ width: '100px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>\n                          </Box>\n                          <Box sx={{ flexGrow: 0 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>\n                          </Box>\n                        </Box>\n                      <List sx={{ maxHeight: '300px', overflow: 'auto', bgcolor: 'background.paper' }}>\n                        {bobineCompatibili.map((bobina) => (\n                          <ListItem\n                            key={bobina.id_bobina}\n                            disablePadding\n                            secondaryAction={\n                              <IconButton\n                                edge=\"end\"\n                                size=\"small\"\n                                onClick={() => handleSelectBobina(bobina.id_bobina)}\n                                disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                              >\n                                <AddCircleOutlineIcon color={bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'disabled' : 'primary'} />\n                              </IconButton>\n                            }\n                            sx={{\n                              bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                              borderRadius: '4px',\n                              mb: 0.5,\n                              border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none',\n                            }}\n                          >\n                            <ListItemButton\n                              dense\n                              onClick={() => handleSelectBobina(bobina.id_bobina)}\n                              disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                            >\n                              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>\n                                <Box sx={{ width: '60px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>\n                                    {getBobinaNumber(bobina.id_bobina)}\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ width: '120px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                    {bobina.tipologia || 'N/A'}\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ width: '100px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                    {bobina.sezione || 'N/A'}\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ width: '100px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                    {bobina.metri_residui || 0} m\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ flexGrow: 0 }}>\n                                  <Chip\n                                    size=\"small\"\n                                    label={bobina.stato_bobina || 'N/D'}\n                                    color={getReelStateColor(bobina.stato_bobina)}\n                                    variant=\"outlined\"\n                                    sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                                  />\n                                </Box>\n                              </Box>\n                            </ListItemButton>\n                          </ListItem>\n                        ))}\n                      </List>\n                      </>\n                    ) : (\n                      <Alert severity=\"info\" sx={{ mt: 1 }}>\n                        Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\n                      </Alert>\n                    )}\n                  </Paper>\n                </Grid>\n\n                {/* Colonna destra: Bobine non compatibili */}\n                <Grid item xs={12} md={6}>\n                  <Paper variant=\"outlined\" sx={{ p: 2, height: '100%' }}>\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                      ELENCO BOBINE NON COMPATIBILI\n                    </Typography>\n\n                    {bobineNonCompatibili.length > 0 ? (\n                      <>\n                        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>\n                          <Box sx={{ width: '60px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>\n                          </Box>\n                          <Box sx={{ width: '120px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>\n                          </Box>\n                          <Box sx={{ width: '100px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>\n                          </Box>\n                          <Box sx={{ width: '100px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>\n                          </Box>\n                          <Box sx={{ flexGrow: 0 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>\n                          </Box>\n                        </Box>\n                      <List sx={{ maxHeight: '300px', overflow: 'auto', bgcolor: 'background.paper' }}>\n                        {bobineNonCompatibili.map((bobina) => (\n                          <ListItem\n                            key={bobina.id_bobina}\n                            disablePadding\n                            secondaryAction={\n                              <IconButton\n                                edge=\"end\"\n                                size=\"small\"\n                                onClick={() => handleSelectBobina(bobina.id_bobina)}\n                                disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                              >\n                                <AddCircleOutlineIcon color={bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'disabled' : 'primary'} />\n                              </IconButton>\n                            }\n                            sx={{\n                              bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                              borderRadius: '4px',\n                              mb: 0.5,\n                              border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none',\n                            }}\n                          >\n                            <ListItemButton\n                              dense\n                              onClick={() => handleSelectBobina(bobina.id_bobina)}\n                              disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                            >\n                              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>\n                                <Box sx={{ width: '60px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>\n                                    {getBobinaNumber(bobina.id_bobina)}\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ width: '120px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                    {bobina.tipologia || 'N/A'}\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ width: '100px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                    {bobina.sezione || 'N/A'}\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ width: '100px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                    {bobina.metri_residui || 0} m\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ display: 'flex', gap: 1 }}>\n                                  <Chip\n                                    size=\"small\"\n                                    label={bobina.stato_bobina || 'N/D'}\n                                    color={getReelStateColor(bobina.stato_bobina)}\n                                    variant=\"outlined\"\n                                    sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                                  />\n                                  <Chip\n                                    size=\"small\"\n                                    label=\"Non comp.\"\n                                    color=\"warning\"\n                                    variant=\"outlined\"\n                                    sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                                  />\n                                </Box>\n                              </Box>\n                            </ListItemButton>\n                          </ListItem>\n                        ))}\n                      </List>\n                      </>\n                    ) : (\n                      <Alert severity=\"info\" sx={{ mt: 1 }}>\n                        Nessuna bobina non compatibile disponibile con i filtri attuali.\n                      </Alert>\n                    )}\n                  </Paper>\n                </Grid>\n              </Grid>\n            </Box>\n          )}\n\n\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n\n          {/* Pulsanti Salva e Annulla */}\n          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3, gap: 2 }}>\n            <Button\n              variant=\"outlined\"\n              color=\"secondary\"\n              onClick={handleReset}\n              disabled={loading}\n            >\n              Annulla\n            </Button>\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSubmit}\n              disabled={loading || !selectedCavo || !formData.metri_posati || !formData.id_bobina}\n              startIcon={loading ? <CircularProgress size={20} /> : null}\n            >\n              Salva\n            </Button>\n          </Box>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          {/* Dettagli del cavo */}\n          <CavoDetailsView\n            cavo={selectedCavo}\n            compact={true}\n            title=\"Dettagli del cavo\"\n          />\n\n          {/* Informazioni sull'operazione */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Informazioni sull'operazione:\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Metri Posati:</strong> {formData.metri_posati} m\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato Installazione:</strong> {statoInstallazione}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Bobina Associata:</strong> {numeroBobina}\n                </Typography>\n                {bobinaInfo && (\n                  <Typography variant=\"body2\">\n                    <strong>Metri Residui Bobina:</strong> {bobinaInfo.metri_residui} m\n                  </Typography>\n                )}\n              </Grid>\n            </Grid>\n          </Box>\n\n          {bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mt: 3 }}>\n              <strong>Attenzione:</strong> I metri posati ({formData.metri_posati}m) superano i metri residui della bobina ({bobinaInfo.metri_residui}m).\n              Questo porterà la bobina in stato OVER.\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1(); // Seleziona Cavo\n      case 1:\n        return renderStep3(); // Associa Bobina\n      case 2:\n        return renderStep2(); // Inserisci Metri\n      case 3:\n        return renderStep4(); // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({ cavo: null, bobina: null });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    const { cavo, bobina } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per aggiornare il cavo:', { cavo, bobina });\n      onError('Dati mancanti per aggiornare il cavo');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);\n      console.log('Dati cavo prima dell\\'aggiornamento:', cavo);\n      console.log('Dati bobina:', bobina);\n\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      console.log('Dati cavo dopo l\\'aggiornamento:', updatedCavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: bobina.id_bobina\n      });\n\n      // Ricarica le bobine per aggiornare l'interfaccia\n      await loadBobine();\n\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per corrispondere alla bobina ${bobina.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n\n  return (\n    <Box>\n      {/* Sezione di ricerca con dettagli cavo selezionato in riga singola */}\n      <Box sx={{ mb: 2 }}>\n        <Paper sx={{ p: 1.5, mb: 2, width: '100%' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\n            <Typography variant=\"subtitle2\" sx={{ mr: 1, minWidth: '80px' }}>\n              Cerca cavo\n            </Typography>\n            <TextField\n              size=\"small\"\n              label=\"ID Cavo\"\n              variant=\"outlined\"\n              value={cavoIdInput}\n              onChange={(e) => setCavoIdInput(e.target.value)}\n              placeholder=\"Inserisci l'ID del cavo\"\n              sx={{ flexGrow: 0, width: '200px', mr: 1 }}\n            />\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSearchCavoById}\n              disabled={caviLoading || !cavoIdInput.trim()}\n              startIcon={caviLoading ? <CircularProgress size={16} /> : <SearchIcon fontSize=\"small\" />}\n              size=\"small\"\n              sx={{ minWidth: '80px', height: '36px', mr: 2 }}\n            >\n              CERCA\n            </Button>\n\n            {/* Dettagli cavo selezionato in riga singola */}\n            {selectedCavo && (\n              <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, flexWrap: 'nowrap', overflow: 'hidden', ml: 4 }}>\n                <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.9rem' }}>\n                  Cavo: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>\n                </Typography>\n                <Divider orientation=\"vertical\" flexItem sx={{ mx: 1.5 }} />\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Tipo:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Form:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Metri:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Stato:</Typography>\n                    <Chip\n                      size=\"small\"\n                      label={selectedCavo.stato_installazione || 'N/D'}\n                      color={getCableStateColor(selectedCavo.stato_installazione)}\n                      sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.8rem' } }}\n                    />\n                  </Box>\n                </Box>\n              </Box>\n            )}\n          </Box>\n        </Paper>\n\n        <Divider sx={{ my: 2 }} />\n      </Box>\n\n      {/* Risultati della ricerca */}\n      {showSearchResults && searchResults.length > 0 && (\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Risultati della ricerca\n          </Typography>\n          <TableContainer>\n            <Table size=\"small\">\n              <TableHead>\n                <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Tipologia</TableCell>\n                  <TableCell>Formazione</TableCell>\n                  <TableCell>Ubicazione</TableCell>\n                  <TableCell>Metri Teorici</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell>Azioni</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {searchResults.map((cavo) => (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>{cavo.id_cavo}</TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.sezione || 'N/A'}</TableCell>\n                    <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={cavo.stato_installazione || 'N/D'}\n                        size=\"small\"\n                        color={getCableStateColor(cavo.stato_installazione)}\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Button\n                        size=\"small\"\n                        variant=\"contained\"\n                        color=\"primary\"\n                        onClick={() => handleCavoSelect(cavo)}\n                        disabled={isCableInstalled(cavo)}\n                      >\n                        Seleziona\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Paper>\n      )}\n      {/* Sezione di selezione bobina */}\n      {selectedCavo && (\n        <Box>\n          {renderStep3()}\n        </Box>\n      )}\n\n      {/* Dialogo di conferma generico */}\n      <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">{confirmDialogProps.title}</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <Typography variant=\"body1\" sx={{ mt: 2 }}>\n            {confirmDialogProps.message}\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowConfirmDialog(false)} color=\"secondary\" variant=\"outlined\">\n            Annulla\n          </Button>\n          <Button\n            onClick={() => {\n              setShowConfirmDialog(false);\n              confirmDialogProps.onConfirm();\n            }}\n            color=\"primary\"\n            variant=\"contained\"\n            autoFocus\n          >\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">Cavo già posato</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          {alreadyLaidCavo && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body1\" paragraph>\n                Il cavo <strong>{alreadyLaidCavo.id_cavo}</strong> risulta già posato ({alreadyLaidCavo.metratura_reale || 0}m).\n              </Typography>\n              <Typography variant=\"body1\" paragraph>\n                Puoi scegliere di:\n              </Typography>\n              <Typography variant=\"body2\" component=\"ul\">\n                <li>Modificare la bobina associata al cavo</li>\n                <li>Selezionare un altro cavo</li>\n                <li>Annullare l'operazione</li>\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={handleCloseIncompatibleReelDialog}\n        cavo={incompatibleReelData.cavo}\n        bobina={incompatibleReelData.bobina}\n        onUpdateCavo={handleUpdateCavoToMatchReel}\n        onSelectAnotherReel={handleSelectAnotherReel}\n      />\n\n      {/* Dialogo per visualizzare i dettagli del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <InfoIcon color=\"primary\" />\n            <Typography variant=\"h6\">Dettagli Cavo</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <CavoDetailsView cavo={selectedCavo} />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)} color=\"primary\">\n            Chiudi\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,uBAAuB,QAClB,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,gBAAgB,IAAIC,oBAAoB,QACnC,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACmF,IAAI,EAAEC,OAAO,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACqF,MAAM,EAAEC,SAAS,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuF,YAAY,EAAEC,eAAe,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyF,WAAW,EAAEC,cAAc,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAAC2F,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM,CAAC6F,QAAQ,EAAEC,WAAW,CAAC,GAAG9F,QAAQ,CAAC;IACvC+F,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACoG,YAAY,EAAEC,eAAe,CAAC,GAAGrG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAACsG,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACwG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC0G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3G,QAAQ,CAAC;IAAE4G,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC;EAC9F,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACA,MAAM,CAACgH,UAAU,EAAEC,aAAa,CAAC,GAAGjH,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkH,eAAe,EAAEC,kBAAkB,CAAC,GAAGnH,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoH,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrH,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACA,MAAMsH,kBAAkB,GAAIC,QAAQ,IAAK;IACvCC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,QAAQ,CAAC;IAC5CzB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAEsB;IACb,CAAC,CAAC;;IAEF;IACApB,aAAa,CAACuB,IAAI,KAAK;MACrB,GAAGA,IAAI;MACPzB,SAAS,EAAE,IAAI;MACf0B,eAAe,EAAE;IACnB,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMC,cAAc,GAAGvC,MAAM,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,SAAS,KAAKsB,QAAQ,CAAC;IACjE,IAAIK,cAAc,EAAE;MAClBJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEG,cAAc,CAAC;IAC7D;EACF,CAAC;;EAGD;EACA,MAAMG,sBAAsB,GAAIC,KAAK,IAAK;IACxCf,aAAa,CAACe,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;;EAED;EACAjI,SAAS,CAAC,MAAM;IACdkI,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC/D,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMgE,eAAe,GAAIb,QAAQ,IAAK;IACpC;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACc,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOd,QAAQ,CAACe,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOf,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMgB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF3D,cAAc,CAAC,IAAI,CAAC;MACpB4C,OAAO,CAACC,GAAG,CAAC,oCAAoCrD,UAAU,KAAK,CAAC;;MAEhE;MACA,IAAI;QACF,MAAMoE,QAAQ,GAAG,MAAMxF,WAAW,CAACyF,OAAO,CAACrE,UAAU,CAAC;QACtDoD,OAAO,CAACC,GAAG,CAAC,YAAYe,QAAQ,CAACE,MAAM,OAAO,CAAC;;QAE/C;QACA;QACAtD,OAAO,CAACoD,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOG,SAAS,EAAE;QAClBnB,OAAO,CAACoB,KAAK,CAAC,qDAAqD,EAAED,SAAS,CAAC;;QAE/E;QACA,IAAIA,SAAS,CAACE,cAAc,IAAI,CAACF,SAAS,CAACG,QAAQ,IAC/CH,SAAS,CAACI,IAAI,KAAK,cAAc,IAChCJ,SAAS,CAACK,OAAO,IAAIL,SAAS,CAACK,OAAO,CAACX,QAAQ,CAAC,eAAe,CAAE,EAAE;UAEtEb,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtE,MAAM,IAAIwB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvD,IAAI;YACF;YACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC3C,MAAMC,OAAO,GAAGtG,aAAa,CAACuG,QAAQ,CAACC,OAAO;;YAE9C;YACA,MAAMC,aAAa,GAAG,MAAMxJ,KAAK,CAACyJ,GAAG,CACnC,GAAGJ,OAAO,SAASnF,UAAU,EAAE,EAC/B;cACEwF,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUR,KAAK;cAClC,CAAC;cACDS,OAAO,EAAE,KAAK,CAAC;YACjB,CACF,CAAC;YAEDrC,OAAO,CAACC,GAAG,CAAC,2CAA2CiC,aAAa,CAACI,IAAI,CAACpB,MAAM,OAAO,CAAC;YACxFtD,OAAO,CAACsE,aAAa,CAACI,IAAI,CAAC;UAC7B,CAAC,CAAC,OAAOC,UAAU,EAAE;YACnBvC,OAAO,CAACoB,KAAK,CAAC,uCAAuC,EAAEmB,UAAU,CAAC;YAClE,MAAMA,UAAU;UAClB;QACF,CAAC,MAAM;UACL,MAAMpB,SAAS;QACjB;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIoB,YAAY,GAAG,iCAAiC;MAEpD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEA1E,OAAO,CAAC0F,YAAY,CAAC;IACvB,CAAC,SAAS;MACRpF,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMuD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFrD,gBAAgB,CAAC,IAAI,CAAC;MACtB0C,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMyC,UAAU,GAAG,MAAMrG,gBAAgB,CAACsG,SAAS,CAAC/F,UAAU,CAAC;MAC/DoD,OAAO,CAACC,GAAG,CAAC,oBAAoByC,UAAU,CAACxB,MAAM,EAAE,CAAC;;MAEpD;MACA,MAAM0B,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAACxD,MAAM,IACjDA,MAAM,CAACyD,YAAY,KAAK,WAAW,IACnCzD,MAAM,CAACyD,YAAY,KAAK,MAAM,IAC9BzD,MAAM,CAAC0D,aAAa,GAAG,CACzB,CAAC;MAED/C,OAAO,CAACC,GAAG,CAAC,wBAAwB2C,kBAAkB,CAAC1B,MAAM,EAAE,CAAC;;MAEhE;MACA,IAAInD,YAAY,IAAIA,YAAY,CAACiF,SAAS,IAAIjF,YAAY,CAACkF,OAAO,EAAE;QAClEjD,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnED,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;UACxB1B,OAAO,EAAER,YAAY,CAACQ,OAAO;UAC7ByE,SAAS,EAAEjF,YAAY,CAACiF,SAAS;UACjCC,OAAO,EAAElF,YAAY,CAACkF;QACxB,CAAC,CAAC;;QAEF;QACA,MAAMC,aAAa,GAAGC,MAAM,CAACpF,YAAY,CAACiF,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC/E,MAAMC,WAAW,GAAGH,MAAM,CAACpF,YAAY,CAACkF,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC;;QAE9D;QACA,MAAMG,iBAAiB,GAAG,EAAE;QAC5B,MAAMC,oBAAoB,GAAG,EAAE;;QAE/B;QACAZ,kBAAkB,CAACa,OAAO,CAACpE,MAAM,IAAI;UACnC,MAAMqE,eAAe,GAAGP,MAAM,CAAC9D,MAAM,CAAC2D,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAC3E,MAAMM,aAAa,GAAGR,MAAM,CAAC9D,MAAM,CAAC4D,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC;;UAE1D;UACA,MAAMQ,cAAc,GAAGF,eAAe,KAAKR,aAAa;UACxD,MAAMW,YAAY,GAAGF,aAAa,KAAKL,WAAW;UAClD,MAAMQ,YAAY,GAAGF,cAAc,IAAIC,YAAY;UAEnD7D,OAAO,CAACC,GAAG,CAAC,iCAAiCZ,MAAM,CAACZ,SAAS,GAAG,EAAE;YAChE,kBAAkB,EAAE,IAAIY,MAAM,CAAC2D,SAAS,GAAG;YAC3C,gBAAgB,EAAE,IAAIjF,YAAY,CAACiF,SAAS,GAAG;YAC/C,mBAAmB,EAAEY,cAAc;YACnC,gBAAgB,EAAE,IAAIT,MAAM,CAAC9D,MAAM,CAAC4D,OAAO,CAAC,GAAG;YAC/C,cAAc,EAAE,IAAIE,MAAM,CAACpF,YAAY,CAACkF,OAAO,CAAC,GAAG;YACnD,iBAAiB,EAAEY,YAAY;YAC/B,cAAc,EAAExE,MAAM,CAACyD,YAAY;YACnC,eAAe,EAAEzD,MAAM,CAAC0D,aAAa;YACrC,cAAc,EAAEe;UAClB,CAAC,CAAC;UAEF,IAAIA,YAAY,EAAE;YAChBP,iBAAiB,CAACQ,IAAI,CAAC1E,MAAM,CAAC;UAChC,CAAC,MAAM;YACLmE,oBAAoB,CAACO,IAAI,CAAC1E,MAAM,CAAC;UACnC;QACF,CAAC,CAAC;QAEFW,OAAO,CAACC,GAAG,CAAC,+BAA+BsD,iBAAiB,CAACrC,MAAM,EAAE,CAAC;QACtElB,OAAO,CAACC,GAAG,CAAC,2BAA2BuD,oBAAoB,CAACtC,MAAM,EAAE,CAAC;;QAErE;QACA,IAAIqC,iBAAiB,CAACrC,MAAM,GAAG,CAAC,EAAE;UAChClB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;UAC5CsD,iBAAiB,CAACE,OAAO,CAACpE,MAAM,IAAI;YAClCW,OAAO,CAACC,GAAG,CAAC,KAAKZ,MAAM,CAACZ,SAAS,KAAKY,MAAM,CAAC2D,SAAS,MAAM3D,MAAM,CAAC4D,OAAO,KAAK5D,MAAM,CAAC0D,aAAa,IAAI,CAAC;UAC1G,CAAC,CAAC;QACJ,CAAC,MAAM;UACL/C,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAChE;;QAEA;QACAsD,iBAAiB,CAACS,IAAI,CAAC,CAACC,CAAC,EAAE3D,CAAC,KAAKA,CAAC,CAACyC,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;QACnES,oBAAoB,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAE3D,CAAC,KAAKA,CAAC,CAACyC,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;;QAEtE;QACA,MAAMmB,cAAc,GAAG,CAAC,GAAGX,iBAAiB,EAAE,GAAGC,oBAAoB,CAAC;;QAEtE;QACA1F,SAAS,CAACoG,cAAc,CAAC;MAC3B,CAAC,MAAM;QACL;QACAlE,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE2C,kBAAkB,CAACoB,IAAI,CAAC,CAACC,CAAC,EAAE3D,CAAC,KAAKA,CAAC,CAACyC,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;QACpEjF,SAAS,CAAC8E,kBAAkB,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DtE,OAAO,CAAC,uCAAuC,IAAIsE,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRlE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM6G,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAAClG,WAAW,CAACmF,IAAI,CAAC,CAAC,EAAE;MACvBtG,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFM,cAAc,CAAC,IAAI,CAAC;MACpB4C,OAAO,CAACC,GAAG,CAAC,6BAA6BhC,WAAW,CAACmF,IAAI,CAAC,CAAC,iBAAiBxG,UAAU,EAAE,CAAC;;MAEzF;MACA,MAAMoE,QAAQ,GAAG,MAAMxF,WAAW,CAACyF,OAAO,CAACrE,UAAU,CAAC;;MAEtD;MACA,MAAMwH,YAAY,GAAGpD,QAAQ,CAAC6B,MAAM,CAACzD,IAAI,IACvCA,IAAI,CAACb,OAAO,CAAC8E,WAAW,CAAC,CAAC,CAACxC,QAAQ,CAAC5C,WAAW,CAACmF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACtE,CAAC;MAEDrD,OAAO,CAACC,GAAG,CAAC,WAAWmE,YAAY,CAAClD,MAAM,iCAAiC,CAAC;;MAE5E;MACA,MAAMmD,UAAU,GAAGD,YAAY,CAAC/D,IAAI,CAACjB,IAAI,IACvCA,IAAI,CAACb,OAAO,CAAC8E,WAAW,CAAC,CAAC,KAAKpF,WAAW,CAACmF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAChE,CAAC;MAED,IAAIgB,UAAU,EAAE;QACdrE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEoE,UAAU,CAAC;;QAEzD;QACA,IAAIA,UAAU,CAACC,mBAAmB,KAAK,YAAY,IAAKD,UAAU,CAACE,eAAe,IAAIF,UAAU,CAACE,eAAe,GAAG,CAAE,EAAE;UACrHvE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEoE,UAAU,CAAC;UAC/D1E,kBAAkB,CAAC0E,UAAU,CAAC;UAC9B9E,wBAAwB,CAAC,IAAI,CAAC;UAC9BnC,cAAc,CAAC,KAAK,CAAC;UACrB;QACF;;QAEA;QACA,IAAIiH,UAAU,CAACG,sBAAsB,KAAK,CAAC,EAAE;UAC3CxE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoE,UAAU,CAAC;UAC9C;QACF;;QAEA;QACAI,gBAAgB,CAACJ,UAAU,CAAC;MAC9B,CAAC,MAAM,IAAID,YAAY,CAAClD,MAAM,GAAG,CAAC,EAAE;QAClC;QACA1D,gBAAgB,CAAC4G,YAAY,CAAC;QAC9B1G,oBAAoB,CAAC,IAAI,CAAC;MAC5B,CAAC,MAAM;QACL;QACAZ,OAAO,CAAC,oCAAoCmB,WAAW,CAACmF,IAAI,CAAC,CAAC,kBAAkBxG,UAAU,EAAE,CAAC;MAC/F;IACF,CAAC,CAAC,OAAOwE,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;MAEtD;MACA,IAAIoB,YAAY,GAAG,+BAA+B;MAElD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAACsD,MAAM,KAAK,GAAG,EAAE;QAC/BlC,YAAY,GAAG,gBAAgBvE,WAAW,CAACmF,IAAI,CAAC,CAAC,8BAA8BxG,UAAU,EAAE;MAC7F,CAAC,MAAM,IAAIwE,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEA1E,OAAO,CAAC0F,YAAY,CAAC;IACvB,CAAC,SAAS;MACRpF,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMqH,gBAAgB,GAAIrF,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACkF,mBAAmB,KAAK,YAAY,IAAKlF,IAAI,CAACmF,eAAe,IAAInF,IAAI,CAACmF,eAAe,GAAG,CAAE,EAAE;MACnG;MACA5E,kBAAkB,CAACP,IAAI,CAAC;MACxBG,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IACA;IAAA,KACK,IAAIH,IAAI,CAACoF,sBAAsB,KAAK,CAAC,EAAE;MAC1C;MACA,IAAIG,MAAM,CAACC,OAAO,CAAC,WAAWxF,IAAI,CAACb,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACAsG,eAAe,CAACzF,IAAI,CAACb,OAAO,CAAC,CAACuG,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAG3F,IAAI;YAAEoF,sBAAsB,EAAE;UAAE,CAAC;UAC1DxG,eAAe,CAAC+G,WAAW,CAAC;UAC5BzG,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAEwG,WAAW,CAACxG,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAd,oBAAoB,CAAC,KAAK,CAAC;;UAE3B;UACAiD,UAAU,CAAC,CAAC;QACd,CAAC,CAAC,CAACqE,KAAK,CAAC5D,KAAK,IAAI;UAChBpB,OAAO,CAACoB,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvEtE,OAAO,CAAC,kDAAkD,IAAIsE,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACAxD,eAAe,CAACoB,IAAI,CAAC;MACrBd,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEa,IAAI,CAACb,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAd,oBAAoB,CAAC,KAAK,CAAC;;MAE3B;MACA,IAAI0B,IAAI,CAAC4D,SAAS,IAAI5D,IAAI,CAAC6F,YAAY,IAAI7F,IAAI,CAAC6D,OAAO,EAAE;QACvDjD,OAAO,CAACC,GAAG,CAAC,8CAA8Cb,IAAI,CAACb,OAAO,KAAK,CAAC;QAC5EoC,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC;;EAED;EACA,MAAMkE,eAAe,GAAG,MAAOK,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAM1J,WAAW,CAACqJ,eAAe,CAACjI,UAAU,EAAEsI,MAAM,CAAC;MACrDrI,SAAS,CAAC,QAAQqI,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvEtE,OAAO,CAAC,kDAAkD,IAAIsE,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMJ,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM+D,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAE3E;IAAM,CAAC,GAAG0E,CAAC,CAAC3E,MAAM;;IAEhC;IACA,IAAI4E,IAAI,KAAK,WAAW,IAAI3E,KAAK,IAAIA,KAAK,KAAK,cAAc,EAAE;MAC7D;MACA,MAAMrB,MAAM,GAAGxB,MAAM,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,SAAS,KAAKiC,KAAK,CAAC;MAEtD,IAAIrB,MAAM,IAAItB,YAAY,EAAE;QAC1B;QACA;QACA,MAAM+F,YAAY,GAChBX,MAAM,CAAC9D,MAAM,CAAC2D,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpF,YAAY,CAACiF,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFD,MAAM,CAAC9D,MAAM,CAAC4D,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpF,YAAY,CAACkF,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC;QAErF,IAAI,CAACU,YAAY,EAAE;UACjB9D,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEZ,MAAM,CAAC;UACxDW,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAElC,YAAY,CAAC;UAC3CiC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;YACnCqF,cAAc,EAAEnC,MAAM,CAACpF,YAAY,CAACkF,OAAO,IAAI,GAAG,CAAC;YACnDsC,gBAAgB,EAAEpC,MAAM,CAAC9D,MAAM,CAAC4D,OAAO,IAAI,GAAG;UAChD,CAAC,CAAC;;UAEF;UACA9D,uBAAuB,CAAC;YACtBC,IAAI,EAAErB,YAAY;YAClBsB,MAAM,EAAEA;UACV,CAAC,CAAC;UACFN,6BAA6B,CAAC,IAAI,CAAC;;UAEnC;UACA;QACF;MACF;IACF;IAEAT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACgH,IAAI,GAAG3E;IACV,CAAC,CAAC;;IAEF;IACA8E,aAAa,CAACH,IAAI,EAAE3E,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM8E,aAAa,GAAGA,CAACH,IAAI,EAAE3E,KAAK,KAAK;IACrC,IAAIU,KAAK,GAAG,IAAI;IAChB,IAAIqE,OAAO,GAAG,IAAI;IAElB,IAAIJ,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAI,CAAC3E,KAAK,IAAIA,KAAK,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjChC,KAAK,GAAG,uCAAuC;QAC/C,OAAO,KAAK;MACd;;MAEA;MACA,IAAIsE,KAAK,CAACC,UAAU,CAACjF,KAAK,CAAC,CAAC,IAAIiF,UAAU,CAACjF,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDU,KAAK,GAAG,sCAAsC;QAC9C,OAAO,KAAK;MACd;MAEA,MAAMwE,WAAW,GAAGD,UAAU,CAACjF,KAAK,CAAC;;MAErC;MACA,IAAI3C,YAAY,IAAIA,YAAY,CAAC8H,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAC5H,YAAY,CAAC8H,aAAa,CAAC,EAAE;QACtGJ,OAAO,GAAG,mBAAmBG,WAAW,yCAAyC7H,YAAY,CAAC8H,aAAa,IAAI;MACjH;;MAEA;MACA,IAAIxH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC/D,MAAMY,MAAM,GAAGxB,MAAM,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIY,MAAM,IAAIuG,WAAW,GAAGD,UAAU,CAACtG,MAAM,CAAC0D,aAAa,CAAC,EAAE;UAC5D0C,OAAO,GAAG,mBAAmBG,WAAW,6CAA6CvG,MAAM,CAAC0D,aAAa,oCAAoC;QAC/I;MACF;IACF,CAAC,MAAM,IAAIsC,IAAI,KAAK,WAAW,EAAE;MAC/B;MACA,IAAI,CAAC3E,KAAK,IAAIA,KAAK,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjChC,KAAK,GAAG,qCAAqC;QAC7C,OAAO,KAAK;MACd;IACF;;IAEA;IACAzC,aAAa,CAACuB,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACmF,IAAI,GAAGjE;IACV,CAAC,CAAC,CAAC;;IAEH;IACAvC,eAAe,CAACqB,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACmF,IAAI,GAAGI;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAACrE,KAAK;EACf,CAAC;;EAED;EACA,MAAM,CAAC0E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvN,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACwN,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzN,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC0N,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3N,QAAQ,CAAC;IAC3D4N,KAAK,EAAE,EAAE;IACT5E,OAAO,EAAE,EAAE;IACX6E,SAAS,EAAEA,CAAA,KAAM,CAAC;EACpB,CAAC,CAAC;;EAEF;;EAEA;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;;IAEnB;IACAV,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACA,IAAI,CAAC1H,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAAC4E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEoD,MAAM,CAAChI,YAAY,GAAG,uCAAuC;MAC7D+H,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAIb,KAAK,CAACC,UAAU,CAACtH,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAImH,UAAU,CAACtH,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7FgI,MAAM,CAAChI,YAAY,GAAG,sCAAsC;MAC5D+H,OAAO,GAAG,KAAK;IACjB;;IAEA;IACA,IAAI,CAAClI,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,CAAC2E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC3DoD,MAAM,CAAC/H,SAAS,GAAG,qCAAqC;MACxD8H,OAAO,GAAG,KAAK;IACjB;IAEA,IAAIA,OAAO,EAAE;MACX,MAAMX,WAAW,GAAGD,UAAU,CAACtH,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAIT,YAAY,IAAIA,YAAY,CAAC8H,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAC5H,YAAY,CAAC8H,aAAa,CAAC,EAAE;QACtGY,QAAQ,CAACjI,YAAY,GAAG,mBAAmBoH,WAAW,yCAAyC7H,YAAY,CAAC8H,aAAa,IAAI;QAC7HE,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5B;QACA;MACF;;MAEA;MACA,IAAIQ,OAAO,IAAIlI,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC1E,MAAMY,MAAM,GAAGxB,MAAM,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIY,MAAM,IAAIuG,WAAW,GAAGD,UAAU,CAACtG,MAAM,CAAC0D,aAAa,CAAC,EAAE;UAC5D0D,QAAQ,CAACjI,YAAY,GAAG,mBAAmBoH,WAAW,6CAA6CvG,MAAM,CAAC0D,aAAa,oCAAoC;UAC3JgD,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;;UAE5B;UACAI,qBAAqB,CAAC;YACpBC,KAAK,EAAE,kCAAkC;YACzC5E,OAAO,EAAE,mBAAmBoE,WAAW,6CAA6CvG,MAAM,CAAC0D,aAAa,8DAA8D;YACtKsD,SAAS,EAAEA,CAAA,KAAM;cACf;cACAK,UAAU,CAAC,CAAC;YACd;UACF,CAAC,CAAC;UACFT,oBAAoB,CAAC,IAAI,CAAC;UAC1B,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;IACF;IAEAtH,aAAa,CAAC6H,MAAM,CAAC;IACrB3H,eAAe,CAAC4H,QAAQ,CAAC;IACzB,OAAOF,OAAO;EAChB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIvI,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAACmI,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF,CAAC,MAAM,IAAInI,UAAU,KAAK,CAAC,EAAE;MAC3B;MACAwC,UAAU,CAAC,CAAC;IACd;IAEAvC,aAAa,CAAEuI,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBxI,aAAa,CAAEuI,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxBzI,aAAa,CAAC,CAAC,CAAC;IAChBJ,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBI,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA;EACA,MAAMiI,2BAA2B,GAAGA,CAAClB,WAAW,EAAEmB,YAAY,KAAK;IACjE,OAAOjL,mBAAmB,CAAC8J,WAAW,EAAEmB,YAAY,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B;IACA;IACA,IAAIjH,QAAQ;IACZ,IAAIkH,kBAAkB;IACtB,IAAIrB,WAAW;IACf,IAAIsB,SAAS,GAAG,KAAK;IAErB,IAAI;MACFhK,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAACoJ,YAAY,CAAC,CAAC,EAAE;QACnBpJ,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA0I,WAAW,GAAGD,UAAU,CAACtH,QAAQ,CAACG,YAAY,CAAC;;MAE/C;MACAuB,QAAQ,GAAG1B,QAAQ,CAACI,SAAS;;MAE7B;MACA,IAAI,CAACsB,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;QAChC;QACApB,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbD,SAAS,EAAE;QACb,CAAC,CAAC;QACFvB,UAAU,CAAC,KAAK,CAAC;QACjB;MACF,CAAC,MAAM,IAAI6C,QAAQ,KAAK,cAAc,EAAE;QACtC;QACAC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAF,QAAQ,GAAG,cAAc;MAC3B,CAAC,MAAM;QACL;QACAC,OAAO,CAACC,GAAG,CAAC,wBAAwBF,QAAQ,EAAE,CAAC;MACjD;;MAEA;MACAkH,kBAAkB,GAAGH,2BAA2B,CAAClB,WAAW,EAAE7H,YAAY,CAAC8H,aAAa,CAAC;;MAEzF;MACAqB,SAAS,GAAG,IAAI;MAChBlH,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;MAE1F;MACA,IAAIF,QAAQ,KAAK,cAAc,EAAE;QAC/BC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC5C;MACA;MAAA,KACK,IAAIF,QAAQ,IAAIA,QAAQ,KAAK,cAAc,EAAE;QAChD,MAAMV,MAAM,GAAGxB,MAAM,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,SAAS,KAAKsB,QAAQ,CAAC;QACzD,IAAIV,MAAM,IAAIuG,WAAW,GAAGD,UAAU,CAACtG,MAAM,CAAC0D,aAAa,CAAC,EAAE;UAC5D/C,OAAO,CAACC,GAAG,CAAC,aAAaF,QAAQ,sCAAsC6F,WAAW,oBAAoBvG,MAAM,CAAC0D,aAAa,GAAG,CAAC;QAChI;MACF;;MAEA;MACA,IAAIhF,YAAY,IAAIA,YAAY,CAAC8H,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAC5H,YAAY,CAAC8H,aAAa,CAAC,EAAE;QACtG7F,OAAO,CAACC,GAAG,CAAC,mBAAmB2F,WAAW,+BAA+B7H,YAAY,CAAC8H,aAAa,GAAG,CAAC;MACzG;;MAEA;MACA7F,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBrD,UAAU;QACVsI,MAAM,EAAE7G,QAAQ,CAACE,OAAO;QACxBqH,WAAW;QACX7F,QAAQ;QACRmH,SAAS;QACTD;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACnB,iBAAiB,EAAE;QACtB,MAAMqB,cAAc,GAAG,qCAAqC9I,QAAQ,CAACE,OAAO,QAAQqH,WAAW,WAAW;;QAE1G;QACAO,qBAAqB,CAAC;UACpBC,KAAK,EAAE,wBAAwB;UAC/B5E,OAAO,EAAE2F,cAAc;UACvBd,SAAS,EAAE,MAAAA,CAAA,KAAY;YACrB;YACA,IAAI;cACFnJ,UAAU,CAAC,IAAI,CAAC;;cAEhB;cACA8C,OAAO,CAACC,GAAG,CAAC,qGAAqG,CAAC;cAClH,MAAMzE,WAAW,CAAC4L,iBAAiB,CACjCxK,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChBqH,WAAW,EACX7F,QAAQ,EACR,IAAI,CAAC;cACP,CAAC;;cAED;cACA,IAAIsH,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;cAC9F,IAAIlH,QAAQ,KAAK,cAAc,EAAE;gBAC/BsH,cAAc,IAAI,iCAAiC;cACrD,CAAC,MAAM,IAAItH,QAAQ,EAAE;gBACnB,MAAMV,MAAM,GAAGxB,MAAM,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,SAAS,KAAKsB,QAAQ,CAAC;gBACzD,IAAIV,MAAM,EAAE;kBACVgI,cAAc,IAAI,gCAAgCtH,QAAQ,EAAE;gBAC9D;cACF;;cAEA;cACAlD,SAAS,CAACwK,cAAc,CAAC;;cAEzB;cACAR,WAAW,CAAC,CAAC;;cAEb;cACA9F,QAAQ,CAAC,CAAC;YACZ,CAAC,CAAC,OAAOK,KAAK,EAAE;cACdpB,OAAO,CAACoB,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;cAEzE;cACA,IAAIrB,QAAQ,KAAK,cAAc,IAAIqB,KAAK,CAACkG,OAAO,EAAE;gBAChD;gBACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;gBAC7HpK,SAAS,CAACwK,cAAc,CAAC;;gBAEzB;gBACAR,WAAW,CAAC,CAAC;;gBAEb;gBACA9F,QAAQ,CAAC,CAAC;gBACV;cACF;;cAEA;cACA,IAAIyB,YAAY,GAAG,kDAAkD;cAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;gBAAA,IAAAiG,oBAAA;gBAClB;gBACA,MAAM7C,MAAM,GAAGtD,KAAK,CAACE,QAAQ,CAACoD,MAAM;gBACpC,MAAMjC,MAAM,GAAG,EAAA8E,oBAAA,GAAAnG,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAiF,oBAAA,uBAAnBA,oBAAA,CAAqB9E,MAAM,KAAIrB,KAAK,CAACI,OAAO;gBAE3D,IAAIkD,MAAM,KAAK,GAAG,EAAE;kBAClB;kBACA,IAAIjC,MAAM,CAAC5B,QAAQ,CAAC,eAAe,CAAC,EAAE;oBACpC2B,YAAY,GAAG,uGAAuG;kBACxH,CAAC,MAAM,IAAIC,MAAM,CAAC5B,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACxC2B,YAAY,GAAG,4EAA4E;kBAC7F,CAAC,MAAM;oBACLA,YAAY,GAAGC,MAAM;kBACvB;gBACF,CAAC,MAAM,IAAIiC,MAAM,KAAK,GAAG,EAAE;kBACzB;kBACA,IAAI3E,QAAQ,KAAK,cAAc,IAAI0C,MAAM,CAAC5B,QAAQ,CAAC,aAAa,CAAC,EAAE;oBACjE,IAAIwG,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;oBAC7HpK,SAAS,CAACwK,cAAc,CAAC;;oBAEzB;oBACAR,WAAW,CAAC,CAAC;;oBAEb;oBACA9F,QAAQ,CAAC,CAAC;oBACV;kBACF,CAAC,MAAM;oBACLyB,YAAY,GAAG,8BAA8BC,MAAM,EAAE;kBACvD;gBACF,CAAC,MAAM;kBACLD,YAAY,GAAG,sBAAsBkC,MAAM,MAAMjC,MAAM,EAAE;gBAC3D;cACF,CAAC,MAAM,IAAIrB,KAAK,CAACoG,OAAO,EAAE;gBACxB;gBACAhF,YAAY,GAAG,+DAA+D;cAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAI1C,QAAQ,KAAK,cAAc,EAAE;gBACtD;gBACAyC,YAAY,GAAGpB,KAAK,CAACqB,MAAM;gBAC3B,IAAIrB,KAAK,CAACsD,MAAM,KAAK,GAAG,IAAItD,KAAK,CAACkG,OAAO,EAAE;kBACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;kBAC7HpK,SAAS,CAACwK,cAAc,CAAC;;kBAEzB;kBACAR,WAAW,CAAC,CAAC;;kBAEb;kBACA9F,QAAQ,CAAC,CAAC;kBACV;gBACF;cACF,CAAC,MAAM;gBACL;gBACAyB,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;cACtE;cAEA3F,OAAO,CAAC0F,YAAY,CAAC;YACvB,CAAC,SAAS;cACRtF,UAAU,CAAC,KAAK,CAAC;YACnB;UACF;QACF,CAAC,CAAC;QACF+I,oBAAoB,CAAC,IAAI,CAAC;QAC1B/I,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA8C,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAErD,UAAU,CAAC;MACxCoD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE5B,QAAQ,CAACE,OAAO,CAAC;MAC3CyB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE2F,WAAW,CAAC;MAC3C5F,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,QAAQ,EAAE,OAAOA,QAAQ,CAAC;MACtDC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEiH,SAAS,CAAC;;MAEtC;MACAlH,OAAO,CAACC,GAAG,CAAC,oGAAoG,CAAC;MACjH,MAAMzE,WAAW,CAAC4L,iBAAiB,CACjCxK,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChBqH,WAAW,EACX7F,QAAQ,EACR,IAAI,CAAC;MACP,CAAC;;MAED;MACA,IAAIsH,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;MAC9F,IAAIlH,QAAQ,KAAK,cAAc,EAAE;QAC/BsH,cAAc,IAAI,iCAAiC;MACrD,CAAC,MAAM,IAAItH,QAAQ,EAAE;QACnB,MAAMV,MAAM,GAAGxB,MAAM,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,SAAS,KAAKsB,QAAQ,CAAC;QACzD,IAAIV,MAAM,EAAE;UACVgI,cAAc,IAAI,gCAAgCtH,QAAQ,EAAE;QAC9D;MACF;;MAEA;MACAlD,SAAS,CAACwK,cAAc,CAAC;;MAEzB;MACAR,WAAW,CAAC,CAAC;;MAEb;MACA9F,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAIrB,QAAQ,KAAK,cAAc,IAAIqB,KAAK,CAACkG,OAAO,EAAE;QAChD;QACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;QAC7HpK,SAAS,CAACwK,cAAc,CAAC;;QAEzB;QACAR,WAAW,CAAC,CAAC;;QAEb;QACA9F,QAAQ,CAAC,CAAC;QACV;MACF;;MAEA;MACA,IAAIyB,YAAY,GAAG,kDAAkD;MAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;QAAA,IAAAmG,qBAAA;QAClB;QACA,MAAM/C,MAAM,GAAGtD,KAAK,CAACE,QAAQ,CAACoD,MAAM;QACpC,MAAMjC,MAAM,GAAG,EAAAgF,qBAAA,GAAArG,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAmF,qBAAA,uBAAnBA,qBAAA,CAAqBhF,MAAM,KAAIrB,KAAK,CAACI,OAAO;QAE3D,IAAIkD,MAAM,KAAK,GAAG,EAAE;UAClB;UACA,IAAIjC,MAAM,CAAC5B,QAAQ,CAAC,eAAe,CAAC,EAAE;YACpC2B,YAAY,GAAG,uGAAuG;UACxH,CAAC,MAAM,IAAIC,MAAM,CAAC5B,QAAQ,CAAC,YAAY,CAAC,EAAE;YACxC2B,YAAY,GAAG,4EAA4E;UAC7F,CAAC,MAAM;YACLA,YAAY,GAAGC,MAAM;UACvB;QACF,CAAC,MAAM,IAAIiC,MAAM,KAAK,GAAG,EAAE;UACzB;UACA,IAAI3E,QAAQ,KAAK,cAAc,IAAI0C,MAAM,CAAC5B,QAAQ,CAAC,aAAa,CAAC,EAAE;YACjE,IAAIwG,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;YAC7HpK,SAAS,CAACwK,cAAc,CAAC;;YAEzB;YACAR,WAAW,CAAC,CAAC;;YAEb;YACA9F,QAAQ,CAAC,CAAC;YACV;UACF,CAAC,MAAM;YACLyB,YAAY,GAAG,8BAA8BC,MAAM,EAAE;UACvD;QACF,CAAC,MAAM;UACLD,YAAY,GAAG,sBAAsBkC,MAAM,MAAMjC,MAAM,EAAE;QAC3D;MACF,CAAC,MAAM,IAAIrB,KAAK,CAACoG,OAAO,EAAE;QACxB;QACAhF,YAAY,GAAG,+DAA+D;MAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAI1C,QAAQ,KAAK,cAAc,EAAE;QACtD;QACAyC,YAAY,GAAGpB,KAAK,CAACqB,MAAM;QAC3B,IAAIrB,KAAK,CAACsD,MAAM,KAAK,GAAG,IAAItD,KAAK,CAACkG,OAAO,EAAE;UACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;UAC7HpK,SAAS,CAACwK,cAAc,CAAC;;UAEzB;UACAR,WAAW,CAAC,CAAC;;UAEb;UACA9F,QAAQ,CAAC,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL;QACAyB,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;MACtE;MAEA3F,OAAO,CAAC0F,YAAY,CAAC;IACvB,CAAC,SAAS;MACRtF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwK,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACElL,OAAA,CAAC7D,GAAG;MAAAgP,QAAA,gBACFnL,OAAA,CAAC3D,UAAU;QAAC+O,OAAO,EAAC,WAAW;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAEnE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb3L,OAAA,CAAC5D,KAAK;QAACiP,EAAE,EAAE;UAAEO,CAAC,EAAE,GAAG;UAAEN,EAAE,EAAE,CAAC;UAAEO,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,eAC1CnL,OAAA,CAAC7D,GAAG;UAACkP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEF,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,gBAChEnL,OAAA,CAAC3D,UAAU;YAAC+O,OAAO,EAAC,WAAW;YAACC,EAAE,EAAE;cAAEW,EAAE,EAAE,CAAC;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAEjE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3L,OAAA,CAAC1D,SAAS;YACR4P,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,SAAS;YACff,OAAO,EAAC,UAAU;YAClBlH,KAAK,EAAEzC,WAAY;YACnB2K,QAAQ,EAAGxD,CAAC,IAAKlH,cAAc,CAACkH,CAAC,CAAC3E,MAAM,CAACC,KAAK,CAAE;YAChDmI,WAAW,EAAC,yBAAyB;YACrChB,EAAE,EAAE;cAAEiB,QAAQ,EAAE,CAAC;cAAET,KAAK,EAAE,OAAO;cAAEG,EAAE,EAAE;YAAE;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACF3L,OAAA,CAACzD,MAAM;YACL6O,OAAO,EAAC,WAAW;YACnBmB,KAAK,EAAC,SAAS;YACfC,OAAO,EAAE7E,oBAAqB;YAC9B8E,QAAQ,EAAE9L,WAAW,IAAI,CAACc,WAAW,CAACmF,IAAI,CAAC,CAAE;YAC7C8F,SAAS,EAAE/L,WAAW,gBAAGX,OAAA,CAACjD,gBAAgB;cAACmP,IAAI,EAAE;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG3L,OAAA,CAAC5B,UAAU;cAACuO,QAAQ,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1FO,IAAI,EAAC,OAAO;YACZb,EAAE,EAAE;cAAEY,QAAQ,EAAE,MAAM;cAAEW,MAAM,EAAE,MAAM;cAAEZ,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,EACjD;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAGRpK,YAAY,iBACXvB,OAAA,CAAC7D,GAAG;YAACkP,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEO,QAAQ,EAAE,CAAC;cAAEO,QAAQ,EAAE,QAAQ;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,gBAC7GnL,OAAA,CAAC7D,GAAG;cAACkP,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAb,QAAA,gBACxDnL,OAAA,CAAC3D,UAAU;gBAAC+O,OAAO,EAAC,WAAW;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEyB,UAAU,EAAE,QAAQ;kBAAEhB,EAAE,EAAE,CAAC;kBAAEW,QAAQ,EAAE;gBAAU,CAAE;gBAAAxB,QAAA,GAAC,QACtG,eAAAnL,OAAA;kBAAMiN,KAAK,EAAE;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAApB,QAAA,EAAE5J,YAAY,CAACQ;gBAAO;kBAAAyJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACb3L,OAAA,CAACnD,OAAO;gBAACqQ,WAAW,EAAC,UAAU;gBAACC,QAAQ;gBAAC9B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAI;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5D3L,OAAA,CAAC7D,GAAG;gBAACkP,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEsB,GAAG,EAAE,CAAC;kBAAER,QAAQ,EAAE,QAAQ;kBAAEC,QAAQ,EAAE;gBAAS,CAAE;gBAAA3B,QAAA,gBACjGnL,OAAA,CAAC7D,GAAG;kBAACkP,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEiB,UAAU,EAAE;kBAAS,CAAE;kBAAA7B,QAAA,gBACvEnL,OAAA,CAAC3D,UAAU;oBAAC+O,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoB,QAAQ,EAAE,QAAQ;sBAAEX,EAAE,EAAE;oBAAI,CAAE;oBAAAb,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzG3L,OAAA,CAAC3D,UAAU;oBAAC+O,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEsB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,EAAE5J,YAAY,CAACiF,SAAS,IAAI;kBAAK;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,eACN3L,OAAA,CAAC7D,GAAG;kBAACkP,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEiB,UAAU,EAAE;kBAAS,CAAE;kBAAA7B,QAAA,gBACvEnL,OAAA,CAAC3D,UAAU;oBAAC+O,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoB,QAAQ,EAAE,QAAQ;sBAAEX,EAAE,EAAE;oBAAI,CAAE;oBAAAb,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzG3L,OAAA,CAAC3D,UAAU;oBAAC+O,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEsB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,EAAE5J,YAAY,CAACkF,OAAO,IAAI;kBAAK;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjG,CAAC,eACN3L,OAAA,CAAC7D,GAAG;kBAACkP,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEiB,UAAU,EAAE;kBAAS,CAAE;kBAAA7B,QAAA,gBACvEnL,OAAA,CAAC3D,UAAU;oBAAC+O,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoB,QAAQ,EAAE,QAAQ;sBAAEX,EAAE,EAAE;oBAAI,CAAE;oBAAAb,QAAA,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC1G3L,OAAA,CAAC3D,UAAU;oBAAC+O,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEsB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,GAAE5J,YAAY,CAAC8H,aAAa,IAAI,KAAK,EAAC,IAAE;kBAAA;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzG,CAAC,eACN3L,OAAA,CAAC7D,GAAG;kBAACkP,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEiB,UAAU,EAAE;kBAAS,CAAE;kBAAA7B,QAAA,gBACvEnL,OAAA,CAAC3D,UAAU;oBAAC+O,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoB,QAAQ,EAAE,QAAQ;sBAAEX,EAAE,EAAE;oBAAI,CAAE;oBAAAb,QAAA,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC1G3L,OAAA,CAAC9C,IAAI;oBACHgP,IAAI,EAAC,OAAO;oBACZC,KAAK,EAAE5K,YAAY,CAACuG,mBAAmB,IAAI,KAAM;oBACjDyE,KAAK,EAAE5M,kBAAkB,CAAC4B,YAAY,CAACuG,mBAAmB,CAAE;oBAC5DuD,EAAE,EAAE;sBAAEuB,MAAM,EAAE,MAAM;sBAAE,kBAAkB,EAAE;wBAAEU,EAAE,EAAE,CAAC;wBAAEC,EAAE,EAAE,CAAC;wBAAEZ,QAAQ,EAAE;sBAAU;oBAAE;kBAAE;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEL9J,QAAQ,CAACI,SAAS,iBACjBjC,OAAA,CAAAE,SAAA;cAAAiL,QAAA,gBACEnL,OAAA,CAACnD,OAAO;gBAACqQ,WAAW,EAAC,UAAU;gBAACC,QAAQ;gBAAC9B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1D3L,OAAA,CAAC7D,GAAG;gBAACkP,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAAZ,QAAA,gBACjDnL,OAAA,CAAC3D,UAAU;kBAAC+O,OAAO,EAAC,WAAW;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,MAAM;oBAAEyB,UAAU,EAAE,QAAQ;oBAAEhB,EAAE,EAAE,CAAC;oBAAEW,QAAQ,EAAE,SAAS;oBAAEJ,KAAK,EAAE;kBAAU,CAAE;kBAAApB,QAAA,GAAC,UACtH,eAAAnL,OAAA;oBAAAmL,QAAA,EAAOtJ,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,OAAO,GAAGmC,eAAe,CAACvC,QAAQ,CAACI,SAAS;kBAAC;oBAAAuJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClG,CAAC,EACZ,CAAC,MAAM;kBACN,IAAI9J,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;oBACzC,oBACEjC,OAAA,CAAC7D,GAAG;sBAACkP,EAAE,EAAE;wBAAES,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEiB,UAAU,EAAE;sBAAS,CAAE;sBAAA7B,QAAA,eACvEnL,OAAA,CAAC3D,UAAU;wBAAC+O,OAAO,EAAC,OAAO;wBAACC,EAAE,EAAE;0BAAEsB,QAAQ,EAAE,QAAQ;0BAAEJ,KAAK,EAAE,gBAAgB;0BAAEiB,SAAS,EAAE;wBAAS,CAAE;wBAAArC,QAAA,EAAC;sBAEtG;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAEV;kBAEA,MAAM9I,MAAM,GAAGxB,MAAM,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;kBACnE,OAAOY,MAAM,gBACX7C,OAAA,CAAC7D,GAAG;oBAACkP,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEsB,GAAG,EAAE,CAAC;sBAAER,QAAQ,EAAE,QAAQ;sBAAEC,QAAQ,EAAE;oBAAS,CAAE;oBAAA3B,QAAA,gBACjGnL,OAAA,CAAC7D,GAAG;sBAACkP,EAAE,EAAE;wBAAES,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEiB,UAAU,EAAE;sBAAS,CAAE;sBAAA7B,QAAA,gBACvEnL,OAAA,CAAC3D,UAAU;wBAAC+O,OAAO,EAAC,OAAO;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,QAAQ;0BAAEoB,QAAQ,EAAE,QAAQ;0BAAEX,EAAE,EAAE;wBAAI,CAAE;wBAAAb,QAAA,EAAC;sBAAQ;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC5G3L,OAAA,CAAC3D,UAAU;wBAAC+O,OAAO,EAAC,OAAO;wBAACC,EAAE,EAAE;0BAAEsB,QAAQ,EAAE,QAAQ;0BAAEJ,KAAK,EAAE1J,MAAM,CAAC0D,aAAa,GAAG4C,UAAU,CAACtH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG,cAAc;0BAAEuJ,UAAU,EAAE;wBAAO,CAAE;wBAAAJ,QAAA,GAC9KtI,MAAM,CAAC0D,aAAa,IAAI,CAAC,EAAC,IAC7B;sBAAA;wBAAAiF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACN3L,OAAA,CAAC7D,GAAG;sBAACkP,EAAE,EAAE;wBAAES,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEiB,UAAU,EAAE;sBAAS,CAAE;sBAAA7B,QAAA,gBACvEnL,OAAA,CAAC3D,UAAU;wBAAC+O,OAAO,EAAC,OAAO;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,QAAQ;0BAAEoB,QAAQ,EAAE,QAAQ;0BAAEX,EAAE,EAAE;wBAAI,CAAE;wBAAAb,QAAA,EAAC;sBAAM;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC1G3L,OAAA,CAAC9C,IAAI;wBACHgP,IAAI,EAAC,OAAO;wBACZC,KAAK,EAAEtJ,MAAM,CAACyD,YAAY,IAAI,KAAM;wBACpCiG,KAAK,EAAE3M,iBAAiB,CAACiD,MAAM,CAACyD,YAAY,CAAE;wBAC9C+E,EAAE,EAAE;0BAAEuB,MAAM,EAAE,MAAM;0BAAE,kBAAkB,EAAE;4BAAEU,EAAE,EAAE,CAAC;4BAAEC,EAAE,EAAE,CAAC;4BAAEZ,QAAQ,EAAE;0BAAU;wBAAE;sBAAE;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,GACJ,IAAI;gBACV,CAAC,EAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA,eACN,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGR3L,OAAA,CAAC5D,KAAK;QAACiP,EAAE,EAAE;UAAEO,CAAC,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBACnCnL,OAAA,CAAC3D,UAAU;UAAC+O,OAAO,EAAC,WAAW;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EAAC;QAE/C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZhL,WAAW,gBACVX,OAAA,CAAC7D,GAAG;UAACkP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAE2B,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAvC,QAAA,eAC5DnL,OAAA,CAACjD,gBAAgB;YAACmP,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,GACJxK,IAAI,CAACuD,MAAM,KAAK,CAAC,gBACnB1E,OAAA,CAAClD,KAAK;UAAC6Q,QAAQ,EAAC,MAAM;UAACtC,EAAE,EAAE;YAAEkC,EAAE,EAAE;UAAI,CAAE;UAAApC,QAAA,eACrCnL,OAAA,CAAC3D,UAAU;YAAC+O,OAAO,EAAC,SAAS;YAAAD,QAAA,EAAC;UAA2C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,gBAER3L,OAAA,CAACrC,cAAc;UAACiQ,SAAS,EAAExR,KAAM;UAACgP,OAAO,EAAC,UAAU;UAACC,EAAE,EAAE;YAAEwC,SAAS,EAAE,OAAO;YAAEf,QAAQ,EAAE,MAAM;YAAEjB,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,eAC/GnL,OAAA,CAACxC,KAAK;YAAC0O,IAAI,EAAC,OAAO;YAAC4B,YAAY;YAAA3C,QAAA,gBAC9BnL,OAAA,CAACpC,SAAS;cAAAuN,QAAA,eACRnL,OAAA,CAACnC,QAAQ;gBAACwN,EAAE,EAAE;kBAAE,MAAM,EAAE;oBAAEE,UAAU,EAAE,MAAM;oBAAEgC,EAAE,EAAE,CAAC;oBAAEQ,OAAO,EAAE;kBAAU;gBAAE,CAAE;gBAAA5C,QAAA,gBAC1EnL,OAAA,CAACtC,SAAS;kBAAAyN,QAAA,EAAC;gBAAO;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9B3L,OAAA,CAACtC,SAAS;kBAAAyN,QAAA,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChC3L,OAAA,CAACtC,SAAS;kBAAAyN,QAAA,EAAC;gBAAU;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjC3L,OAAA,CAACtC,SAAS;kBAAAyN,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5B3L,OAAA,CAACtC,SAAS;kBAAAyN,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5B3L,OAAA,CAACtC,SAAS;kBAACsQ,KAAK,EAAC,QAAQ;kBAAC3C,EAAE,EAAE;oBAAEQ,KAAK,EAAE;kBAAO,CAAE;kBAAAV,QAAA,EAAC;gBAAI;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZ3L,OAAA,CAACvC,SAAS;cAAA0N,QAAA,EACPhK,IAAI,CAAC8M,GAAG,CAAErL,IAAI,iBACb5C,OAAA,CAACnC,QAAQ;gBAEPqQ,KAAK;gBACL1B,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAACrF,IAAI,CAAE;gBACtCyI,EAAE,EAAE;kBACF8C,MAAM,EAAE,SAAS;kBACjB,SAAS,EAAE;oBAAEJ,OAAO,EAAE;kBAAU,CAAC;kBACjC,MAAM,EAAE;oBAAER,EAAE,EAAE;kBAAI;gBACpB,CAAE;gBAAApC,QAAA,gBAEFnL,OAAA,CAACtC,SAAS;kBAAC2N,EAAE,EAAE;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAJ,QAAA,EAAEvI,IAAI,CAACb;gBAAO;kBAAAyJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnE3L,OAAA,CAACtC,SAAS;kBAAAyN,QAAA,EAAEvI,IAAI,CAAC4D,SAAS,IAAI;gBAAK;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChD3L,OAAA,CAACtC,SAAS;kBAAAyN,QAAA,EAAEvI,IAAI,CAAC6D,OAAO,IAAI;gBAAK;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9C3L,OAAA,CAACtC,SAAS;kBAAAyN,QAAA,EAAEvI,IAAI,CAACyG,aAAa,IAAI;gBAAK;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpD3L,OAAA,CAACtC,SAAS;kBAAAyN,QAAA,eACRnL,OAAA,CAAC9C,IAAI;oBACHgP,IAAI,EAAC,OAAO;oBACZC,KAAK,EAAE1M,YAAY,CAACmD,IAAI,CAAC,GAAG,OAAO,GAAGlD,gBAAgB,CAACkD,IAAI,CAAC,GAAG,YAAY,GAAGA,IAAI,CAACkF,mBAAoB;oBACvGyE,KAAK,EAAE9M,YAAY,CAACmD,IAAI,CAAC,GAAG,OAAO,GAAGlD,gBAAgB,CAACkD,IAAI,CAAC,GAAG,SAAS,GAAGjD,kBAAkB,CAACiD,IAAI,CAACkF,mBAAmB,CAAE;oBACxHuD,EAAE,EAAE;sBAAEuB,MAAM,EAAE,MAAM;sBAAE,kBAAkB,EAAE;wBAAEU,EAAE,EAAE,CAAC;wBAAEC,EAAE,EAAE,CAAC;wBAAEZ,QAAQ,EAAE;sBAAS;oBAAE;kBAAE;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ3L,OAAA,CAACtC,SAAS;kBAACsQ,KAAK,EAAC,QAAQ;kBAAA7C,QAAA,eACvBnL,OAAA,CAAC/C,UAAU;oBACTiP,IAAI,EAAC,OAAO;oBACZM,OAAO,EAAG5D,CAAC,IAAK;sBACdA,CAAC,CAACwF,eAAe,CAAC,CAAC;sBACnB5M,eAAe,CAACoB,IAAI,CAAC;sBACrBS,wBAAwB,CAAC,IAAI,CAAC;oBAChC,CAAE;oBAAA8H,QAAA,eAEFnL,OAAA,CAACpB,QAAQ;sBAAC+N,QAAQ,EAAC;oBAAO;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAhCP/I,IAAI,CAACb,OAAO;gBAAAyJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiCT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM0C,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAC9M,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEvB,OAAA,CAAC7D,GAAG;MAAAgP,QAAA,gBACFnL,OAAA,CAAC3D,UAAU;QAAC+O,OAAO,EAAC,WAAW;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAEnE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb3L,OAAA,CAAC5D,KAAK;QAACiP,EAAE,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBACjCnL,OAAA,CAAC3D,UAAU;UAAC+O,OAAO,EAAC,WAAW;UAACkD,YAAY;UAACjD,EAAE,EAAE;YAAEE,UAAU,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAEzE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAIb3L,OAAA,CAAC7D,GAAG;UAACkP,EAAE,EAAE;YAAEkD,EAAE,EAAE,CAAC;YAAEjD,EAAE,EAAE,CAAC;YAAEO,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,gBACvCnL,OAAA,CAAC3D,UAAU;YAAC+O,OAAO,EAAC,WAAW;YAACkD,YAAY;YAACjD,EAAE,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEzE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3L,OAAA,CAAC1D,SAAS;YACR4P,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClBvC,IAAI,EAAC,cAAc;YACnB2F,IAAI,EAAC,QAAQ;YACbtK,KAAK,EAAErC,QAAQ,CAACG,YAAa;YAC7BoK,QAAQ,EAAEzD,gBAAiB;YAC3B/D,KAAK,EAAE,CAAC,CAAC1C,UAAU,CAACF,YAAa;YACjCyM,UAAU,EAAEvM,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjE0M,mBAAmB,EAAE;cACnBrD,EAAE,EAAE;gBAAEkB,KAAK,EAAEnK,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACFqJ,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAQ,CAAE;YAC9B8C,UAAU,EAAE;cACVC,GAAG,EAAE,MAAM;cAAE;cACbC,IAAI,EAAE;YACR;UAAE;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELvJ,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,IAAI,CAACsH,iBAAiB,iBAC1EtJ,OAAA,CAAClD,KAAK;UAAC6Q,QAAQ,EAAC,SAAS;UAACtC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EACrC/I,YAAY,CAACJ;QAAY;UAAAwJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACR,eAED3L,OAAA,CAAClD,KAAK;UAAC6Q,QAAQ,EAAC,MAAM;UAACtC,EAAE,EAAE;YAAEkD,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,eACnCnL,OAAA,CAAC3D,UAAU;YAAC+O,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAC;UAE5B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMmD,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,MAAMC,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAI5O,UAAU,KAAK4O,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAIpM,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAAChB,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAOmH,UAAU,CAACtG,MAAM,CAAC0D,aAAa,CAAC,IAAI4C,UAAU,CAACtH,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAMkN,cAAc,GAAG7N,MAAM,CAACgF,MAAM,CAACxD,MAAM,IAAI;MAC7C;MACA,MAAMsM,WAAW,GAAGnM,UAAU,CAAC6D,WAAW,CAAC,CAAC;MAC5C,MAAMuI,aAAa,GAAG,CAACpM,UAAU,IAC/BoB,eAAe,CAACvB,MAAM,CAACZ,SAAS,CAAC,CAAC4E,WAAW,CAAC,CAAC,CAACxC,QAAQ,CAAC8K,WAAW,CAAC,IACrExI,MAAM,CAAC9D,MAAM,CAAC2D,SAAS,IAAI,EAAE,CAAC,CAACK,WAAW,CAAC,CAAC,CAACxC,QAAQ,CAAC8K,WAAW,CAAC,IAClExI,MAAM,CAAC9D,MAAM,CAAC4D,OAAO,IAAI,EAAE,CAAC,CAACI,WAAW,CAAC,CAAC,CAACxC,QAAQ,CAAC8K,WAAW,CAAC;MAElE,OAAOC,aAAa;IACtB,CAAC,CAAC;;IAEF;IACA,MAAMrI,iBAAiB,GAAGxF,YAAY,GAClC2N,cAAc,CAAC7I,MAAM,CAACxD,MAAM,IAC1B8D,MAAM,CAAC9D,MAAM,CAAC2D,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpF,YAAY,CAACiF,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFD,MAAM,CAAC9D,MAAM,CAAC4D,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpF,YAAY,CAACkF,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,GACtFsI,cAAc;IAElB,MAAMlI,oBAAoB,GAAGzF,YAAY,GACrC2N,cAAc,CAAC7I,MAAM,CAACxD,MAAM,IAC1B8D,MAAM,CAAC9D,MAAM,CAAC2D,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpF,YAAY,CAACiF,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFD,MAAM,CAAC9D,MAAM,CAAC4D,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpF,YAAY,CAACkF,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,GACtF,EAAE;;IAEN;IACA,MAAMyI,uBAAuB,GAAIzG,CAAC,IAAK;MACrC,MAAMoG,YAAY,GAAGpG,CAAC,CAAC3E,MAAM,CAACC,KAAK,CAAC0C,IAAI,CAAC,CAAC;;MAE1C;MACA,IAAIoI,YAAY,CAACnI,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtC/E,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbyB,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIqL,YAAY,EAAE;QAChB;QACA,MAAMM,gBAAgB,GAAGP,iBAAiB,CAACC,YAAY,CAAC;;QAExD;QACA,MAAMO,eAAe,GAAGlO,MAAM,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,SAAS,KAAKqN,gBAAgB,CAAC;QAE1E,IAAIC,eAAe,EAAE;UACnB;UACA,IAAIA,eAAe,CAACjJ,YAAY,KAAK,MAAM,IAAIiJ,eAAe,CAACjJ,YAAY,KAAK,WAAW,EAAE;YAC3FnE,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbyB,eAAe,EAAE,aAAaqL,YAAY,eAAeO,eAAe,CAACjJ,YAAY;YACvF,CAAC,CAAC;YACF;UACF;;UAEA;UACA,IAAI/E,YAAY,EAAE;YAChB;YACA,MAAMmF,aAAa,GAAGC,MAAM,CAACpF,YAAY,CAACiF,SAAS,IAAI,EAAE,CAAC;YAC1D,MAAMM,WAAW,GAAGH,MAAM,CAACpF,YAAY,CAACkF,OAAO,IAAI,GAAG,CAAC;YAEvD,MAAMS,eAAe,GAAGP,MAAM,CAAC4I,eAAe,CAAC/I,SAAS,IAAI,EAAE,CAAC;YAC/D,MAAMW,aAAa,GAAGR,MAAM,CAAC4I,eAAe,CAAC9I,OAAO,IAAI,GAAG,CAAC;;YAE5D;YACAjD,OAAO,CAACC,GAAG,CAAC,iCAAiC8L,eAAe,CAACtN,SAAS,GAAG,EAAE;cACzEuE,SAAS,EAAE,GAAGU,eAAe,QAAQR,aAAa,EAAE;cACpD8I,UAAU,EAAE,GAAGrI,aAAa,QAAQL,WAAW;YACjD,CAAC,CAAC;YAEF,IAAII,eAAe,KAAKR,aAAa,IACjCS,aAAa,KAAKL,WAAW,EAAE;cACjC;cACAnE,uBAAuB,CAAC;gBACtBC,IAAI,EAAErB,YAAY;gBAClBsB,MAAM,EAAE0M;cACV,CAAC,CAAC;cACFhN,6BAA6B,CAAC,IAAI,CAAC;cACnC;YACF;UACF;;UAEA;UACA,IAAI0M,mBAAmB,CAACM,eAAe,CAAC,EAAE;YACxC;YACAzN,WAAW,CAAC;cACV,GAAGD,QAAQ;cACXI,SAAS,EAAEqN;YACb,CAAC,CAAC;YACFnN,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbyB,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACAxB,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbyB,eAAe,EAAE,aAAaqL,YAAY,sCAAsCO,eAAe,CAAChJ,aAAa;YAC/G,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACApE,aAAa,CAAC;YACZ,GAAGD,UAAU;YACbyB,eAAe,EAAE,UAAUqL,YAAY;UACzC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACAlN,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbyB,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,oBACE3D,OAAA,CAAC7D,GAAG;MAAAgP,QAAA,gBACFnL,OAAA,CAAC7D,GAAG;QAACkP,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBACjBnL,OAAA,CAAC3D,UAAU;UAAC+O,OAAO,EAAC,WAAW;UAACC,EAAE,EAAE;YAAEE,UAAU,EAAE,MAAM;YAAEO,OAAO,EAAE,QAAQ;YAAEa,QAAQ,EAAE;UAAS,CAAE;UAAAxB,QAAA,EAAC;QAEnG;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3L,OAAA,CAAC3D,UAAU;UAAC+O,OAAO,EAAC,OAAO;UAACC,EAAE,EAAE;YAAES,OAAO,EAAE,QAAQ;YAAEiB,EAAE,EAAE,CAAC;YAAER,KAAK,EAAE;UAAiB,CAAE;UAAApB,QAAA,EAAC;QAEvF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEN3L,OAAA,CAAC5D,KAAK;QAACiP,EAAE,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBAEjCnL,OAAA,CAAC7D,GAAG;UAACkP,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,gBACjBnL,OAAA,CAAC3D,UAAU;YAAC+O,OAAO,EAAC,WAAW;YAACkD,YAAY;YAACjD,EAAE,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEzE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3L,OAAA,CAAC1D,SAAS;YACR4P,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClBvC,IAAI,EAAC,cAAc;YACnB2F,IAAI,EAAC,QAAQ;YACbtK,KAAK,EAAErC,QAAQ,CAACG,YAAa;YAC7BoK,QAAQ,EAAEzD,gBAAiB;YAC3B/D,KAAK,EAAE,CAAC,CAAC1C,UAAU,CAACF,YAAa;YACjCyM,UAAU,EAAEvM,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjE0M,mBAAmB,EAAE;cACnBrD,EAAE,EAAE;gBAAEkB,KAAK,EAAEnK,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACFqJ,EAAE,EAAE;cAAEQ,KAAK,EAAE;YAAQ,CAAE;YACvB8C,UAAU,EAAE;cACVC,GAAG,EAAE,MAAM;cAAE;cACbC,IAAI,EAAE;YACR;UAAE;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELvJ,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,IAAI,CAACsH,iBAAiB,iBAC1EtJ,OAAA,CAAClD,KAAK;UAAC6Q,QAAQ,EAAC,SAAS;UAACtC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EACrC/I,YAAY,CAACJ;QAAY;UAAAwJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACR,eAED3L,OAAA,CAACnD,OAAO;UAACwO,EAAE,EAAE;YAAEqC,EAAE,EAAE;UAAE;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1B3L,OAAA,CAAC7D,GAAG;UAACkP,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,eAEjBnL,OAAA,CAAC7D,GAAG;YAACkP,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEsB,GAAG,EAAE,CAAC;cAAE/B,EAAE,EAAE;YAAE,CAAE;YAAAH,QAAA,gBAEhEnL,OAAA,CAAC1D,SAAS;cACR4P,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,OAAO;cACbf,OAAO,EAAC,UAAU;cAClBlH,KAAK,EAAElB,UAAW;cAClBoJ,QAAQ,EAAErI,sBAAuB;cACjCsI,WAAW,EAAC,kBAAkB;cAC9BoD,UAAU,EAAE;gBACVC,cAAc,eACZ1P,OAAA,CAAC7C,cAAc;kBAACwS,QAAQ,EAAC,OAAO;kBAAAxE,QAAA,eAC9BnL,OAAA,CAAC5B,UAAU;oBAACuO,QAAQ,EAAC;kBAAO;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CACjB;gBACDiE,YAAY,EAAE5M,UAAU,gBACtBhD,OAAA,CAAC7C,cAAc;kBAACwS,QAAQ,EAAC,KAAK;kBAAAxE,QAAA,eAC5BnL,OAAA,CAAC/C,UAAU;oBACTiP,IAAI,EAAC,OAAO;oBACZ,cAAW,cAAc;oBACzBM,OAAO,EAAEA,CAAA,KAAMvJ,aAAa,CAAC,EAAE,CAAE;oBACjC4M,IAAI,EAAC,KAAK;oBAAA1E,QAAA,eAEVnL,OAAA,CAACxB,UAAU;sBAACmO,QAAQ,EAAC;oBAAO;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,GACf;cACN,CAAE;cACFN,EAAE,EAAE;gBAAEQ,KAAK,EAAE;cAAQ;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eAEF3L,OAAA,CAACzD,MAAM;cACL6O,OAAO,EAAC,UAAU;cAClBc,IAAI,EAAC,OAAO;cACZM,OAAO,EAAEA,CAAA,KAAMlJ,kBAAkB,CAAC,cAAc,CAAE;cAClD+H,EAAE,EAAE;gBAAEuB,MAAM,EAAE;cAAO,CAAE;cAAAzB,QAAA,EACxB;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL9K,aAAa,gBACZb,OAAA,CAAC7D,GAAG;UAACkP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAE2B,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAvC,QAAA,eAC5DnL,OAAA,CAACjD,gBAAgB;YAACmP,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,gBAEN3L,OAAA,CAAC7D,GAAG;UAAAgP,QAAA,gBAEFnL,OAAA,CAAC7D,GAAG;YAACkP,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAH,QAAA,gBACjBnL,OAAA,CAAC3D,UAAU;cAAC+O,OAAO,EAAC,WAAW;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAEO,OAAO,EAAE,OAAO;gBAAER,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,EAAC;YAErF;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3L,OAAA,CAAC7D,GAAG;cAACkP,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,YAAY;gBAAEsB,GAAG,EAAE,CAAC;gBAAEyC,QAAQ,EAAE;cAAQ,CAAE;cAAA3E,QAAA,eAChFnL,OAAA,CAAC1D,SAAS;gBACR4P,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAC,eAAe;gBACrBf,OAAO,EAAC,UAAU;gBAClBiB,WAAW,EAAC,QAAQ;gBACpBzH,KAAK,EAAE,CAAC,CAAC1C,UAAU,CAACyB,eAAgB;gBACpCoM,MAAM,EAAEV,uBAAwB;gBAChChE,EAAE,EAAE;kBAAE2E,IAAI,EAAE;gBAAE;cAAE;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EACLzJ,UAAU,CAACyB,eAAe,iBACzB3D,OAAA,CAAC3D,UAAU;cAAC+O,OAAO,EAAC,SAAS;cAACmB,KAAK,EAAC,OAAO;cAAClB,EAAE,EAAE;gBAAES,OAAO,EAAE,OAAO;gBAAEyC,EAAE,EAAE;cAAI,CAAE;cAAApD,QAAA,EAC3EjJ,UAAU,CAACyB;YAAe;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEE,CAAC,eAGN3L,OAAA,CAACxD,IAAI;YAACyT,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA/E,QAAA,gBAEzBnL,OAAA,CAACxD,IAAI;cAAC2T,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlF,QAAA,eACvBnL,OAAA,CAAC5D,KAAK;gBAACgP,OAAO,EAAC,UAAU;gBAACC,EAAE,EAAE;kBAAEO,CAAC,EAAE,CAAC;kBAAEgB,MAAM,EAAE;gBAAO,CAAE;gBAAAzB,QAAA,gBACrDnL,OAAA,CAAC3D,UAAU;kBAAC+O,OAAO,EAAC,WAAW;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,MAAM;oBAAED,EAAE,EAAE;kBAAE,CAAE;kBAAAH,QAAA,EAAC;gBAEnE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EAEZ5E,iBAAiB,CAACrC,MAAM,GAAG,CAAC,gBAC3B1E,OAAA,CAAAE,SAAA;kBAAAiL,QAAA,gBACEnL,OAAA,CAAC7D,GAAG;oBAACkP,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEF,KAAK,EAAE,MAAM;sBAAE0B,EAAE,EAAE,GAAG;sBAAED,EAAE,EAAE,GAAG;sBAAES,OAAO,EAAE,SAAS;sBAAEuC,YAAY,EAAE,CAAC;sBAAEhF,EAAE,EAAE;oBAAE,CAAE;oBAAAH,QAAA,gBAC9HnL,OAAA,CAAC7D,GAAG;sBAACkP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,MAAM;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eAChCnL,OAAA,CAAC3D,UAAU;wBAAC+O,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,eACN3L,OAAA,CAAC7D,GAAG;sBAACkP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,OAAO;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eACjCnL,OAAA,CAAC3D,UAAU;wBAAC+O,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAI;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7F,CAAC,eACN3L,OAAA,CAAC7D,GAAG;sBAACkP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,OAAO;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eACjCnL,OAAA,CAAC3D,UAAU;wBAAC+O,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAK;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9F,CAAC,eACN3L,OAAA,CAAC7D,GAAG;sBAACkP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,OAAO;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eACjCnL,OAAA,CAAC3D,UAAU;wBAAC+O,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAO;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChG,CAAC,eACN3L,OAAA,CAAC7D,GAAG;sBAACkP,EAAE,EAAE;wBAAEiB,QAAQ,EAAE;sBAAE,CAAE;sBAAAnB,QAAA,eACvBnL,OAAA,CAAC3D,UAAU;wBAAC+O,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAK;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACR3L,OAAA,CAAClC,IAAI;oBAACuN,EAAE,EAAE;sBAAEwC,SAAS,EAAE,OAAO;sBAAEf,QAAQ,EAAE,MAAM;sBAAEiB,OAAO,EAAE;oBAAmB,CAAE;oBAAA5C,QAAA,EAC7EpE,iBAAiB,CAACkH,GAAG,CAAEpL,MAAM,iBAC5B7C,OAAA,CAACjC,QAAQ;sBAEPwS,cAAc;sBACdC,eAAe,eACbxQ,OAAA,CAAC/C,UAAU;wBACT4S,IAAI,EAAC,KAAK;wBACV3D,IAAI,EAAC,OAAO;wBACZM,OAAO,EAAEA,CAAA,KAAMlJ,kBAAkB,CAACT,MAAM,CAACZ,SAAS,CAAE;wBACpDwK,QAAQ,EAAE5J,MAAM,CAAC0D,aAAa,GAAG4C,UAAU,CAACtH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;wBAAAmJ,QAAA,eAExEnL,OAAA,CAAClB,oBAAoB;0BAACyN,KAAK,EAAE1J,MAAM,CAAC0D,aAAa,GAAG4C,UAAU,CAACtH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,UAAU,GAAG;wBAAU;0BAAAwJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7G,CACb;sBACDN,EAAE,EAAE;wBACF0C,OAAO,EAAElM,QAAQ,CAACI,SAAS,KAAKY,MAAM,CAACZ,SAAS,GAAG,yBAAyB,GAAG,SAAS;wBACxFqO,YAAY,EAAE,KAAK;wBACnBhF,EAAE,EAAE,GAAG;wBACPmF,MAAM,EAAE5O,QAAQ,CAACI,SAAS,KAAKY,MAAM,CAACZ,SAAS,GAAG,mBAAmB,GAAG;sBAC1E,CAAE;sBAAAkJ,QAAA,eAEFnL,OAAA,CAAChC,cAAc;wBACb0S,KAAK;wBACLlE,OAAO,EAAEA,CAAA,KAAMlJ,kBAAkB,CAACT,MAAM,CAACZ,SAAS,CAAE;wBACpDwK,QAAQ,EAAE5J,MAAM,CAAC0D,aAAa,GAAG4C,UAAU,CAACtH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;wBAAAmJ,QAAA,eAExEnL,OAAA,CAAC7D,GAAG;0BAACkP,EAAE,EAAE;4BAAES,OAAO,EAAE,MAAM;4BAAEC,UAAU,EAAE,QAAQ;4BAAEF,KAAK,EAAE,MAAM;4BAAE0B,EAAE,EAAE;0BAAI,CAAE;0BAAApC,QAAA,gBACzEnL,OAAA,CAAC7D,GAAG;4BAACkP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,MAAM;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eAChCnL,OAAA,CAAC3D,UAAU;8BAAC+O,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEE,UAAU,EAAE,MAAM;gCAAEoB,QAAQ,EAAE;8BAAS,CAAE;8BAAAxB,QAAA,EACxE/G,eAAe,CAACvB,MAAM,CAACZ,SAAS;4BAAC;8BAAAuJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACxB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN3L,OAAA,CAAC7D,GAAG;4BAACkP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,OAAO;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eACjCnL,OAAA,CAAC3D,UAAU;8BAAC+O,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEsB,QAAQ,EAAE;8BAAU,CAAE;8BAAAxB,QAAA,EACrDtI,MAAM,CAAC2D,SAAS,IAAI;4BAAK;8BAAAgF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN3L,OAAA,CAAC7D,GAAG;4BAACkP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,OAAO;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eACjCnL,OAAA,CAAC3D,UAAU;8BAAC+O,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEsB,QAAQ,EAAE;8BAAU,CAAE;8BAAAxB,QAAA,EACrDtI,MAAM,CAAC4D,OAAO,IAAI;4BAAK;8BAAA+E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACd;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN3L,OAAA,CAAC7D,GAAG;4BAACkP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,OAAO;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eACjCnL,OAAA,CAAC3D,UAAU;8BAAC+O,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEE,UAAU,EAAE,MAAM;gCAAEoB,QAAQ,EAAE,SAAS;gCAAEJ,KAAK,EAAE1J,MAAM,CAAC0D,aAAa,GAAG4C,UAAU,CAACtH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;8BAAe,CAAE;8BAAAmJ,QAAA,GAC/KtI,MAAM,CAAC0D,aAAa,IAAI,CAAC,EAAC,IAC7B;4BAAA;8BAAAiF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAY;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN3L,OAAA,CAAC7D,GAAG;4BAACkP,EAAE,EAAE;8BAAEiB,QAAQ,EAAE;4BAAE,CAAE;4BAAAnB,QAAA,eACvBnL,OAAA,CAAC9C,IAAI;8BACHgP,IAAI,EAAC,OAAO;8BACZC,KAAK,EAAEtJ,MAAM,CAACyD,YAAY,IAAI,KAAM;8BACpCiG,KAAK,EAAE3M,iBAAiB,CAACiD,MAAM,CAACyD,YAAY,CAAE;8BAC9C8E,OAAO,EAAC,UAAU;8BAClBC,EAAE,EAAE;gCAAEuB,MAAM,EAAE,EAAE;gCAAED,QAAQ,EAAE,QAAQ;gCAAE,kBAAkB,EAAE;kCAAEW,EAAE,EAAE,CAAC;kCAAEC,EAAE,EAAE;gCAAE;8BAAE;4BAAE;8BAAA/B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9E;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ;oBAAC,GAvDZ9I,MAAM,CAACZ,SAAS;sBAAAuJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAwDb,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,eACL,CAAC,gBAEH3L,OAAA,CAAClD,KAAK;kBAAC6Q,QAAQ,EAAC,MAAM;kBAACtC,EAAE,EAAE;oBAAEkD,EAAE,EAAE;kBAAE,CAAE;kBAAApD,QAAA,EAAC;gBAEtC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGP3L,OAAA,CAACxD,IAAI;cAAC2T,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlF,QAAA,eACvBnL,OAAA,CAAC5D,KAAK;gBAACgP,OAAO,EAAC,UAAU;gBAACC,EAAE,EAAE;kBAAEO,CAAC,EAAE,CAAC;kBAAEgB,MAAM,EAAE;gBAAO,CAAE;gBAAAzB,QAAA,gBACrDnL,OAAA,CAAC3D,UAAU;kBAAC+O,OAAO,EAAC,WAAW;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,MAAM;oBAAED,EAAE,EAAE;kBAAE,CAAE;kBAAAH,QAAA,EAAC;gBAEnE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EAEZ3E,oBAAoB,CAACtC,MAAM,GAAG,CAAC,gBAC9B1E,OAAA,CAAAE,SAAA;kBAAAiL,QAAA,gBACEnL,OAAA,CAAC7D,GAAG;oBAACkP,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEF,KAAK,EAAE,MAAM;sBAAE0B,EAAE,EAAE,GAAG;sBAAED,EAAE,EAAE,GAAG;sBAAES,OAAO,EAAE,SAAS;sBAAEuC,YAAY,EAAE,CAAC;sBAAEhF,EAAE,EAAE;oBAAE,CAAE;oBAAAH,QAAA,gBAC9HnL,OAAA,CAAC7D,GAAG;sBAACkP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,MAAM;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eAChCnL,OAAA,CAAC3D,UAAU;wBAAC+O,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,eACN3L,OAAA,CAAC7D,GAAG;sBAACkP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,OAAO;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eACjCnL,OAAA,CAAC3D,UAAU;wBAAC+O,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAI;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7F,CAAC,eACN3L,OAAA,CAAC7D,GAAG;sBAACkP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,OAAO;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eACjCnL,OAAA,CAAC3D,UAAU;wBAAC+O,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAK;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9F,CAAC,eACN3L,OAAA,CAAC7D,GAAG;sBAACkP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,OAAO;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eACjCnL,OAAA,CAAC3D,UAAU;wBAAC+O,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAO;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChG,CAAC,eACN3L,OAAA,CAAC7D,GAAG;sBAACkP,EAAE,EAAE;wBAAEiB,QAAQ,EAAE;sBAAE,CAAE;sBAAAnB,QAAA,eACvBnL,OAAA,CAAC3D,UAAU;wBAAC+O,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAK;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACR3L,OAAA,CAAClC,IAAI;oBAACuN,EAAE,EAAE;sBAAEwC,SAAS,EAAE,OAAO;sBAAEf,QAAQ,EAAE,MAAM;sBAAEiB,OAAO,EAAE;oBAAmB,CAAE;oBAAA5C,QAAA,EAC7EnE,oBAAoB,CAACiH,GAAG,CAAEpL,MAAM,iBAC/B7C,OAAA,CAACjC,QAAQ;sBAEPwS,cAAc;sBACdC,eAAe,eACbxQ,OAAA,CAAC/C,UAAU;wBACT4S,IAAI,EAAC,KAAK;wBACV3D,IAAI,EAAC,OAAO;wBACZM,OAAO,EAAEA,CAAA,KAAMlJ,kBAAkB,CAACT,MAAM,CAACZ,SAAS,CAAE;wBACpDwK,QAAQ,EAAE5J,MAAM,CAAC0D,aAAa,GAAG4C,UAAU,CAACtH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;wBAAAmJ,QAAA,eAExEnL,OAAA,CAAClB,oBAAoB;0BAACyN,KAAK,EAAE1J,MAAM,CAAC0D,aAAa,GAAG4C,UAAU,CAACtH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,UAAU,GAAG;wBAAU;0BAAAwJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7G,CACb;sBACDN,EAAE,EAAE;wBACF0C,OAAO,EAAElM,QAAQ,CAACI,SAAS,KAAKY,MAAM,CAACZ,SAAS,GAAG,yBAAyB,GAAG,SAAS;wBACxFqO,YAAY,EAAE,KAAK;wBACnBhF,EAAE,EAAE,GAAG;wBACPmF,MAAM,EAAE5O,QAAQ,CAACI,SAAS,KAAKY,MAAM,CAACZ,SAAS,GAAG,mBAAmB,GAAG;sBAC1E,CAAE;sBAAAkJ,QAAA,eAEFnL,OAAA,CAAChC,cAAc;wBACb0S,KAAK;wBACLlE,OAAO,EAAEA,CAAA,KAAMlJ,kBAAkB,CAACT,MAAM,CAACZ,SAAS,CAAE;wBACpDwK,QAAQ,EAAE5J,MAAM,CAAC0D,aAAa,GAAG4C,UAAU,CAACtH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;wBAAAmJ,QAAA,eAExEnL,OAAA,CAAC7D,GAAG;0BAACkP,EAAE,EAAE;4BAAES,OAAO,EAAE,MAAM;4BAAEC,UAAU,EAAE,QAAQ;4BAAEF,KAAK,EAAE,MAAM;4BAAE0B,EAAE,EAAE;0BAAI,CAAE;0BAAApC,QAAA,gBACzEnL,OAAA,CAAC7D,GAAG;4BAACkP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,MAAM;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eAChCnL,OAAA,CAAC3D,UAAU;8BAAC+O,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEE,UAAU,EAAE,MAAM;gCAAEoB,QAAQ,EAAE;8BAAS,CAAE;8BAAAxB,QAAA,EACxE/G,eAAe,CAACvB,MAAM,CAACZ,SAAS;4BAAC;8BAAAuJ,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACxB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN3L,OAAA,CAAC7D,GAAG;4BAACkP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,OAAO;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eACjCnL,OAAA,CAAC3D,UAAU;8BAAC+O,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEsB,QAAQ,EAAE;8BAAU,CAAE;8BAAAxB,QAAA,EACrDtI,MAAM,CAAC2D,SAAS,IAAI;4BAAK;8BAAAgF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN3L,OAAA,CAAC7D,GAAG;4BAACkP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,OAAO;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eACjCnL,OAAA,CAAC3D,UAAU;8BAAC+O,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEsB,QAAQ,EAAE;8BAAU,CAAE;8BAAAxB,QAAA,EACrDtI,MAAM,CAAC4D,OAAO,IAAI;4BAAK;8BAAA+E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACd;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN3L,OAAA,CAAC7D,GAAG;4BAACkP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,OAAO;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eACjCnL,OAAA,CAAC3D,UAAU;8BAAC+O,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEE,UAAU,EAAE,MAAM;gCAAEoB,QAAQ,EAAE,SAAS;gCAAEJ,KAAK,EAAE1J,MAAM,CAAC0D,aAAa,GAAG4C,UAAU,CAACtH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;8BAAe,CAAE;8BAAAmJ,QAAA,GAC/KtI,MAAM,CAAC0D,aAAa,IAAI,CAAC,EAAC,IAC7B;4BAAA;8BAAAiF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAY;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN3L,OAAA,CAAC7D,GAAG;4BAACkP,EAAE,EAAE;8BAAES,OAAO,EAAE,MAAM;8BAAEuB,GAAG,EAAE;4BAAE,CAAE;4BAAAlC,QAAA,gBACnCnL,OAAA,CAAC9C,IAAI;8BACHgP,IAAI,EAAC,OAAO;8BACZC,KAAK,EAAEtJ,MAAM,CAACyD,YAAY,IAAI,KAAM;8BACpCiG,KAAK,EAAE3M,iBAAiB,CAACiD,MAAM,CAACyD,YAAY,CAAE;8BAC9C8E,OAAO,EAAC,UAAU;8BAClBC,EAAE,EAAE;gCAAEuB,MAAM,EAAE,EAAE;gCAAED,QAAQ,EAAE,QAAQ;gCAAE,kBAAkB,EAAE;kCAAEW,EAAE,EAAE,CAAC;kCAAEC,EAAE,EAAE;gCAAE;8BAAE;4BAAE;8BAAA/B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9E,CAAC,eACF3L,OAAA,CAAC9C,IAAI;8BACHgP,IAAI,EAAC,OAAO;8BACZC,KAAK,EAAC,WAAW;8BACjBI,KAAK,EAAC,SAAS;8BACfnB,OAAO,EAAC,UAAU;8BAClBC,EAAE,EAAE;gCAAEuB,MAAM,EAAE,EAAE;gCAAED,QAAQ,EAAE,QAAQ;gCAAE,kBAAkB,EAAE;kCAAEW,EAAE,EAAE,CAAC;kCAAEC,EAAE,EAAE;gCAAE;8BAAE;4BAAE;8BAAA/B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9E,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ;oBAAC,GA9DZ9I,MAAM,CAACZ,SAAS;sBAAAuJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+Db,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,eACL,CAAC,gBAEH3L,OAAA,CAAClD,KAAK;kBAAC6Q,QAAQ,EAAC,MAAM;kBAACtC,EAAE,EAAE;oBAAEkD,EAAE,EAAE;kBAAE,CAAE;kBAAApD,QAAA,EAAC;gBAEtC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAIAtK,MAAM,CAACqD,MAAM,KAAK,CAAC,IAAI,CAAC7D,aAAa,iBACpCb,OAAA,CAAClD,KAAK;UAAC6Q,QAAQ,EAAC,SAAS;UAACtC,EAAE,EAAE;YAAEkD,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,EAAC;QAEzC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAGD3L,OAAA,CAAC7D,GAAG;UAACkP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAE2B,cAAc,EAAE,UAAU;YAAEc,EAAE,EAAE,CAAC;YAAElB,GAAG,EAAE;UAAE,CAAE;UAAAlC,QAAA,gBACtEnL,OAAA,CAACzD,MAAM;YACL6O,OAAO,EAAC,UAAU;YAClBmB,KAAK,EAAC,WAAW;YACjBC,OAAO,EAAEnC,WAAY;YACrBoC,QAAQ,EAAEhM,OAAQ;YAAA0K,QAAA,EACnB;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3L,OAAA,CAACzD,MAAM;YACL6O,OAAO,EAAC,WAAW;YACnBmB,KAAK,EAAC,SAAS;YACfC,OAAO,EAAEhC,YAAa;YACtBiC,QAAQ,EAAEhM,OAAO,IAAI,CAACc,YAAY,IAAI,CAACM,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,SAAU;YACpFyK,SAAS,EAAEjM,OAAO,gBAAGT,OAAA,CAACjD,gBAAgB;cAACmP,IAAI,EAAE;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG,IAAK;YAAAR,QAAA,EAC5D;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMgF,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,IAAI3B,YAAY,GAAG,SAAS;IAC5B,IAAI4B,UAAU,GAAG,IAAI;IAErB,IAAI/O,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzC+M,YAAY,GAAG,cAAc;IAC/B,CAAC,MAAM,IAAInN,QAAQ,CAACI,SAAS,EAAE;MAC7B+M,YAAY,GAAG5K,eAAe,CAACvC,QAAQ,CAACI,SAAS,CAAC;MAClD;MACA2O,UAAU,GAAGvP,MAAM,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7B,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IACnE;;IAEA;IACA,MAAMwI,kBAAkB,GAAGH,2BAA2B,CAACnB,UAAU,CAACtH,QAAQ,CAACG,YAAY,CAAC,EAAET,YAAY,CAAC8H,aAAa,CAAC;IAErH,oBACErJ,OAAA,CAAC7D,GAAG;MAAAgP,QAAA,gBACFnL,OAAA,CAAC3D,UAAU;QAAC+O,OAAO,EAAC,IAAI;QAACkD,YAAY;QAAAnD,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb3L,OAAA,CAAC5D,KAAK;QAACiP,EAAE,EAAE;UAAEO,CAAC,EAAE;QAAE,CAAE;QAAAT,QAAA,gBAClBnL,OAAA,CAAC3D,UAAU;UAAC+O,OAAO,EAAC,WAAW;UAACkD,YAAY;UAAAnD,QAAA,EAAC;QAE7C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGb3L,OAAA,CAACb,eAAe;UACdyD,IAAI,EAAErB,YAAa;UACnBsP,OAAO,EAAE,IAAK;UACdjH,KAAK,EAAC;QAAmB;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGF3L,OAAA,CAAC7D,GAAG;UAACkP,EAAE,EAAE;YAAEkD,EAAE,EAAE,CAAC;YAAE3C,CAAC,EAAE,CAAC;YAAEmC,OAAO,EAAE,SAAS;YAAEuC,YAAY,EAAE;UAAE,CAAE;UAAAnF,QAAA,gBAC5DnL,OAAA,CAAC3D,UAAU;YAAC+O,OAAO,EAAC,WAAW;YAACkD,YAAY;YAACjD,EAAE,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEzE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3L,OAAA,CAACxD,IAAI;YAACyT,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA/E,QAAA,gBACzBnL,OAAA,CAACxD,IAAI;cAAC2T,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlF,QAAA,gBACvBnL,OAAA,CAAC3D,UAAU;gBAAC+O,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBnL,OAAA;kBAAAmL,QAAA,EAAQ;gBAAa;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9J,QAAQ,CAACG,YAAY,EAAC,IACxD;cAAA;gBAAAwJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb3L,OAAA,CAAC3D,UAAU;gBAAC+O,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBnL,OAAA;kBAAAmL,QAAA,EAAQ;gBAAoB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClB,kBAAkB;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACP3L,OAAA,CAACxD,IAAI;cAAC2T,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlF,QAAA,gBACvBnL,OAAA,CAAC3D,UAAU;gBAAC+O,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBnL,OAAA;kBAAAmL,QAAA,EAAQ;gBAAiB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACqD,YAAY;cAAA;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACZiF,UAAU,iBACT5Q,OAAA,CAAC3D,UAAU;gBAAC+O,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBnL,OAAA;kBAAAmL,QAAA,EAAQ;gBAAqB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACiF,UAAU,CAACrK,aAAa,EAAC,IACnE;cAAA;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAELiF,UAAU,IAAIzH,UAAU,CAACtH,QAAQ,CAACG,YAAY,CAAC,GAAGmH,UAAU,CAACyH,UAAU,CAACrK,aAAa,CAAC,IAAI,CAAC+C,iBAAiB,iBAC3GtJ,OAAA,CAAClD,KAAK;UAAC6Q,QAAQ,EAAC,SAAS;UAACtC,EAAE,EAAE;YAAEkD,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,gBACtCnL,OAAA;YAAAmL,QAAA,EAAQ;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qBAAiB,EAAC9J,QAAQ,CAACG,YAAY,EAAC,4CAA0C,EAAC4O,UAAU,CAACrK,aAAa,EAAC,gDAE1I;QAAA;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAED3L,OAAA,CAAClD,KAAK;UAAC6Q,QAAQ,EAAC,MAAM;UAACtC,EAAE,EAAE;YAAEkD,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,GAAC,8EAEpC,EAACtJ,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,gFAAgF;QAAA;UAAAuJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMmF,cAAc,GAAIjC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAO3D,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAO4D,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOT,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOsC,WAAW,CAAC,CAAC;MAAE;MACxB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;;EAED;EACA,MAAMI,4BAA4B,GAAGA,CAAA,KAAM;IACzChO,wBAAwB,CAAC,KAAK,CAAC;IAC/BI,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAID;EACA,MAAM6N,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI9N,eAAe,EAAE;MACnB1C,QAAQ,CAAC,mCAAmCJ,UAAU,IAAI8C,eAAe,CAACnB,OAAO,EAAE,CAAC;IACtF;IACAgP,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,4BAA4B,CAAC,CAAC;IAC9B;IACAvP,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBR,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMgQ,iCAAiC,GAAGA,CAAA,KAAM;IAC9C3O,6BAA6B,CAAC,KAAK,CAAC;IACpCI,uBAAuB,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMsO,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,MAAM;MAAEvO,IAAI;MAAEC;IAAO,CAAC,GAAGH,oBAAoB;IAC7C,IAAI,CAACE,IAAI,IAAI,CAACC,MAAM,EAAE;MACpBW,OAAO,CAACoB,KAAK,CAAC,uCAAuC,EAAE;QAAEhC,IAAI;QAAEC;MAAO,CAAC,CAAC;MACxEvC,OAAO,CAAC,sCAAsC,CAAC;MAC/C;IACF;IAEA,IAAI;MACFI,UAAU,CAAC,IAAI,CAAC;MAChB8C,OAAO,CAACC,GAAG,CAAC,0CAA0Cb,IAAI,CAACb,OAAO,iCAAiCc,MAAM,CAACZ,SAAS,EAAE,CAAC;MACtHuB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEb,IAAI,CAAC;MACzDY,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEZ,MAAM,CAAC;;MAEnC;MACA,MAAM7D,WAAW,CAACoS,0BAA0B,CAAChR,UAAU,EAAEwC,IAAI,CAACb,OAAO,EAAEc,MAAM,CAACZ,SAAS,CAAC;;MAExF;MACA,MAAMsG,WAAW,GAAG,MAAMvJ,WAAW,CAACqS,WAAW,CAACjR,UAAU,EAAEwC,IAAI,CAACb,OAAO,CAAC;MAC3EyB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE8E,WAAW,CAAC;MAC5D/G,eAAe,CAAC+G,WAAW,CAAC;;MAE5B;MACAzG,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXI,SAAS,EAAEY,MAAM,CAACZ;MACpB,CAAC,CAAC;;MAEF;MACA,MAAMkC,UAAU,CAAC,CAAC;MAElB9D,SAAS,CAAC,4BAA4BuC,IAAI,CAACb,OAAO,6CAA6Cc,MAAM,CAACZ,SAAS,EAAE,CAAC;MAClHiP,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOtM,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,iEAAiE,EAAEA,KAAK,CAAC;MACvFtE,OAAO,CAAC,kEAAkE,IAAIsE,KAAK,CAACqB,MAAM,IAAIrB,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACvI,CAAC,SAAS;MACRtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4Q,uBAAuB,GAAGA,CAAA,KAAM;IACpCJ,iCAAiC,CAAC,CAAC;IACnC;IACApP,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,oBACEjC,OAAA,CAAC7D,GAAG;IAAAgP,QAAA,gBAEFnL,OAAA,CAAC7D,GAAG;MAACkP,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACjBnL,OAAA,CAAC5D,KAAK;QAACiP,EAAE,EAAE;UAAEO,CAAC,EAAE,GAAG;UAAEN,EAAE,EAAE,CAAC;UAAEO,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,eAC1CnL,OAAA,CAAC7D,GAAG;UAACkP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEF,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,gBAChEnL,OAAA,CAAC3D,UAAU;YAAC+O,OAAO,EAAC,WAAW;YAACC,EAAE,EAAE;cAAEW,EAAE,EAAE,CAAC;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAEjE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3L,OAAA,CAAC1D,SAAS;YACR4P,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,SAAS;YACff,OAAO,EAAC,UAAU;YAClBlH,KAAK,EAAEzC,WAAY;YACnB2K,QAAQ,EAAGxD,CAAC,IAAKlH,cAAc,CAACkH,CAAC,CAAC3E,MAAM,CAACC,KAAK,CAAE;YAChDmI,WAAW,EAAC,yBAAyB;YACrChB,EAAE,EAAE;cAAEiB,QAAQ,EAAE,CAAC;cAAET,KAAK,EAAE,OAAO;cAAEG,EAAE,EAAE;YAAE;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACF3L,OAAA,CAACzD,MAAM;YACL6O,OAAO,EAAC,WAAW;YACnBmB,KAAK,EAAC,SAAS;YACfC,OAAO,EAAE7E,oBAAqB;YAC9B8E,QAAQ,EAAE9L,WAAW,IAAI,CAACc,WAAW,CAACmF,IAAI,CAAC,CAAE;YAC7C8F,SAAS,EAAE/L,WAAW,gBAAGX,OAAA,CAACjD,gBAAgB;cAACmP,IAAI,EAAE;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG3L,OAAA,CAAC5B,UAAU;cAACuO,QAAQ,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1FO,IAAI,EAAC,OAAO;YACZb,EAAE,EAAE;cAAEY,QAAQ,EAAE,MAAM;cAAEW,MAAM,EAAE,MAAM;cAAEZ,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,EACjD;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAGRpK,YAAY,iBACXvB,OAAA,CAAC7D,GAAG;YAACkP,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEO,QAAQ,EAAE,CAAC;cAAEO,QAAQ,EAAE,QAAQ;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,gBAC7GnL,OAAA,CAAC3D,UAAU;cAAC+O,OAAO,EAAC,WAAW;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAEyB,UAAU,EAAE,QAAQ;gBAAEhB,EAAE,EAAE,CAAC;gBAAEW,QAAQ,EAAE;cAAS,CAAE;cAAAxB,QAAA,GAAC,QACrG,eAAAnL,OAAA;gBAAMiN,KAAK,EAAE;kBAAEV,KAAK,EAAE;gBAAU,CAAE;gBAAApB,QAAA,EAAE5J,YAAY,CAACQ;cAAO;gBAAAyJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACb3L,OAAA,CAACnD,OAAO;cAACqQ,WAAW,EAAC,UAAU;cAACC,QAAQ;cAAC9B,EAAE,EAAE;gBAAE+B,EAAE,EAAE;cAAI;YAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5D3L,OAAA,CAAC7D,GAAG;cAACkP,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEsB,GAAG,EAAE,CAAC;gBAAER,QAAQ,EAAE,QAAQ;gBAAEC,QAAQ,EAAE;cAAS,CAAE;cAAA3B,QAAA,gBACjGnL,OAAA,CAAC7D,GAAG;gBAACkP,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEiB,UAAU,EAAE;gBAAS,CAAE;gBAAA7B,QAAA,gBACvEnL,OAAA,CAAC3D,UAAU;kBAAC+O,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEoB,QAAQ,EAAE,SAAS;oBAAEX,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1G3L,OAAA,CAAC3D,UAAU;kBAAC+O,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEsB,QAAQ,EAAE;kBAAU,CAAE;kBAAAxB,QAAA,EAAE5J,YAAY,CAACiF,SAAS,IAAI;gBAAK;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpG,CAAC,eACN3L,OAAA,CAAC7D,GAAG;gBAACkP,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEiB,UAAU,EAAE;gBAAS,CAAE;gBAAA7B,QAAA,gBACvEnL,OAAA,CAAC3D,UAAU;kBAAC+O,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEoB,QAAQ,EAAE,SAAS;oBAAEX,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1G3L,OAAA,CAAC3D,UAAU;kBAAC+O,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEsB,QAAQ,EAAE;kBAAU,CAAE;kBAAAxB,QAAA,EAAE5J,YAAY,CAACkF,OAAO,IAAI;gBAAK;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClG,CAAC,eACN3L,OAAA,CAAC7D,GAAG;gBAACkP,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEiB,UAAU,EAAE;gBAAS,CAAE;gBAAA7B,QAAA,gBACvEnL,OAAA,CAAC3D,UAAU;kBAAC+O,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEoB,QAAQ,EAAE,SAAS;oBAAEX,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3G3L,OAAA,CAAC3D,UAAU;kBAAC+O,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEsB,QAAQ,EAAE;kBAAU,CAAE;kBAAAxB,QAAA,GAAE5J,YAAY,CAAC8H,aAAa,IAAI,KAAK,EAAC,IAAE;gBAAA;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC,eACN3L,OAAA,CAAC7D,GAAG;gBAACkP,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEiB,UAAU,EAAE;gBAAS,CAAE;gBAAA7B,QAAA,gBACvEnL,OAAA,CAAC3D,UAAU;kBAAC+O,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEoB,QAAQ,EAAE,SAAS;oBAAEX,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3G3L,OAAA,CAAC9C,IAAI;kBACHgP,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAE5K,YAAY,CAACuG,mBAAmB,IAAI,KAAM;kBACjDyE,KAAK,EAAE5M,kBAAkB,CAAC4B,YAAY,CAACuG,mBAAmB,CAAE;kBAC5DuD,EAAE,EAAE;oBAAEuB,MAAM,EAAE,MAAM;oBAAE,kBAAkB,EAAE;sBAAEU,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE,CAAC;sBAAEZ,QAAQ,EAAE;oBAAS;kBAAE;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAER3L,OAAA,CAACnD,OAAO;QAACwO,EAAE,EAAE;UAAEqC,EAAE,EAAE;QAAE;MAAE;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC,EAGL1K,iBAAiB,IAAIF,aAAa,CAAC2D,MAAM,GAAG,CAAC,iBAC5C1E,OAAA,CAAC5D,KAAK;MAACiP,EAAE,EAAE;QAAEO,CAAC,EAAE,CAAC;QAAEN,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACzBnL,OAAA,CAAC3D,UAAU;QAAC+O,OAAO,EAAC,IAAI;QAACkD,YAAY;QAAAnD,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb3L,OAAA,CAACrC,cAAc;QAAAwN,QAAA,eACbnL,OAAA,CAACxC,KAAK;UAAC0O,IAAI,EAAC,OAAO;UAAAf,QAAA,gBACjBnL,OAAA,CAACpC,SAAS;YAAAuN,QAAA,eACRnL,OAAA,CAACnC,QAAQ;cAACwN,EAAE,EAAE;gBAAE0C,OAAO,EAAE;cAAU,CAAE;cAAA5C,QAAA,gBACnCnL,OAAA,CAACtC,SAAS;gBAAAyN,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B3L,OAAA,CAACtC,SAAS;gBAAAyN,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC3L,OAAA,CAACtC,SAAS;gBAAAyN,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC3L,OAAA,CAACtC,SAAS;gBAAAyN,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC3L,OAAA,CAACtC,SAAS;gBAAAyN,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpC3L,OAAA,CAACtC,SAAS;gBAAAyN,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B3L,OAAA,CAACtC,SAAS;gBAAAyN,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ3L,OAAA,CAACvC,SAAS;YAAA0N,QAAA,EACPpK,aAAa,CAACkN,GAAG,CAAErL,IAAI,iBACtB5C,OAAA,CAACnC,QAAQ;cAAAsN,QAAA,gBACPnL,OAAA,CAACtC,SAAS;gBAAAyN,QAAA,EAAEvI,IAAI,CAACb;cAAO;gBAAAyJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrC3L,OAAA,CAACtC,SAAS;gBAAAyN,QAAA,EAAEvI,IAAI,CAAC4D,SAAS,IAAI;cAAK;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChD3L,OAAA,CAACtC,SAAS;gBAAAyN,QAAA,EAAEvI,IAAI,CAAC6D,OAAO,IAAI;cAAK;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9C3L,OAAA,CAACtC,SAAS;gBAAAyN,QAAA,GAAC,MAAI,EAACvI,IAAI,CAAC2O,mBAAmB,IAAI,KAAK,eAACvR,OAAA;kBAAAwL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,OAAG,EAAC/I,IAAI,CAAC4O,iBAAiB,IAAI,KAAK;cAAA;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvG3L,OAAA,CAACtC,SAAS;gBAAAyN,QAAA,GAAEvI,IAAI,CAACyG,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACtD3L,OAAA,CAACtC,SAAS;gBAAAyN,QAAA,eACRnL,OAAA,CAAC9C,IAAI;kBACHiP,KAAK,EAAEvJ,IAAI,CAACkF,mBAAmB,IAAI,KAAM;kBACzCoE,IAAI,EAAC,OAAO;kBACZK,KAAK,EAAE5M,kBAAkB,CAACiD,IAAI,CAACkF,mBAAmB,CAAE;kBACpDsD,OAAO,EAAC;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ3L,OAAA,CAACtC,SAAS;gBAAAyN,QAAA,eACRnL,OAAA,CAACzD,MAAM;kBACL2P,IAAI,EAAC,OAAO;kBACZd,OAAO,EAAC,WAAW;kBACnBmB,KAAK,EAAC,SAAS;kBACfC,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAACrF,IAAI,CAAE;kBACtC6J,QAAQ,EAAE/M,gBAAgB,CAACkD,IAAI,CAAE;kBAAAuI,QAAA,EAClC;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAxBC/I,IAAI,CAACb,OAAO;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACR,EAEApK,YAAY,iBACXvB,OAAA,CAAC7D,GAAG;MAAAgP,QAAA,EACD2D,WAAW,CAAC;IAAC;MAAAtD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CACN,eAGD3L,OAAA,CAAC5C,MAAM;MAACqU,IAAI,EAAEjI,iBAAkB;MAACkI,OAAO,EAAEA,CAAA,KAAMjI,oBAAoB,CAAC,KAAK,CAAE;MAACqG,QAAQ,EAAC,IAAI;MAAC6B,SAAS;MAAAxG,QAAA,gBAClGnL,OAAA,CAAC3C,WAAW;QAACgO,EAAE,EAAE;UAAE0C,OAAO,EAAE;QAAgB,CAAE;QAAA5C,QAAA,eAC5CnL,OAAA,CAAC7D,GAAG;UAACkP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEsB,GAAG,EAAE;UAAE,CAAE;UAAAlC,QAAA,gBACzDnL,OAAA,CAACtB,WAAW;YAAC6N,KAAK,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B3L,OAAA,CAAC3D,UAAU;YAAC+O,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAEzB,kBAAkB,CAACE;UAAK;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd3L,OAAA,CAAC1C,aAAa;QAAA6N,QAAA,eACZnL,OAAA,CAAC3D,UAAU;UAAC+O,OAAO,EAAC,OAAO;UAACC,EAAE,EAAE;YAAEkD,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,EACvCzB,kBAAkB,CAAC1E;QAAO;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChB3L,OAAA,CAACzC,aAAa;QAAA4N,QAAA,gBACZnL,OAAA,CAACzD,MAAM;UAACiQ,OAAO,EAAEA,CAAA,KAAM/C,oBAAoB,CAAC,KAAK,CAAE;UAAC8C,KAAK,EAAC,WAAW;UAACnB,OAAO,EAAC,UAAU;UAAAD,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3L,OAAA,CAACzD,MAAM;UACLiQ,OAAO,EAAEA,CAAA,KAAM;YACb/C,oBAAoB,CAAC,KAAK,CAAC;YAC3BC,kBAAkB,CAACG,SAAS,CAAC,CAAC;UAChC,CAAE;UACF0C,KAAK,EAAC,SAAS;UACfnB,OAAO,EAAC,WAAW;UACnBwG,SAAS;UAAAzG,QAAA,EACV;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT3L,OAAA,CAAC5C,MAAM;MAACqU,IAAI,EAAE3O,qBAAsB;MAAC4O,OAAO,EAAEX,4BAA6B;MAACjB,QAAQ,EAAC,IAAI;MAAC6B,SAAS;MAAAxG,QAAA,gBACjGnL,OAAA,CAAC3C,WAAW;QAACgO,EAAE,EAAE;UAAE0C,OAAO,EAAE;QAAgB,CAAE;QAAA5C,QAAA,eAC5CnL,OAAA,CAAC7D,GAAG;UAACkP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEsB,GAAG,EAAE;UAAE,CAAE;UAAAlC,QAAA,gBACzDnL,OAAA,CAACtB,WAAW;YAAC6N,KAAK,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B3L,OAAA,CAAC3D,UAAU;YAAC+O,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAe;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd3L,OAAA,CAAC1C,aAAa;QAAA6N,QAAA,EACXjI,eAAe,iBACdlD,OAAA,CAAC7D,GAAG;UAACkP,EAAE,EAAE;YAAEkD,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,gBACjBnL,OAAA,CAAC3D,UAAU;YAAC+O,OAAO,EAAC,OAAO;YAACyG,SAAS;YAAA1G,QAAA,GAAC,UAC5B,eAAAnL,OAAA;cAAAmL,QAAA,EAASjI,eAAe,CAACnB;YAAO;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,4BAAqB,EAACzI,eAAe,CAAC6E,eAAe,IAAI,CAAC,EAAC,KAC/G;UAAA;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3L,OAAA,CAAC3D,UAAU;YAAC+O,OAAO,EAAC,OAAO;YAACyG,SAAS;YAAA1G,QAAA,EAAC;UAEtC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3L,OAAA,CAAC3D,UAAU;YAAC+O,OAAO,EAAC,OAAO;YAACwC,SAAS,EAAC,IAAI;YAAAzC,QAAA,gBACxCnL,OAAA;cAAAmL,QAAA,EAAI;YAAsC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C3L,OAAA;cAAAmL,QAAA,EAAI;YAAyB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC3L,OAAA;cAAAmL,QAAA,EAAI;YAAsB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChB3L,OAAA,CAACzC,aAAa;QAAC8N,EAAE,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAE6B,cAAc,EAAE;QAAgB,CAAE;QAAAtC,QAAA,gBAC3DnL,OAAA,CAACzD,MAAM;UAACiQ,OAAO,EAAEuE,4BAA6B;UAACxE,KAAK,EAAC,WAAW;UAAApB,QAAA,EAAC;QAEjE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3L,OAAA,CAAC7D,GAAG;UAAAgP,QAAA,gBACFnL,OAAA,CAACzD,MAAM;YAACiQ,OAAO,EAAEyE,wBAAyB;YAAC1E,KAAK,EAAC,SAAS;YAAClB,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAE1E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3L,OAAA,CAACzD,MAAM;YAACiQ,OAAO,EAAEwE,gBAAiB;YAAC5F,OAAO,EAAC,WAAW;YAACmB,KAAK,EAAC,SAAS;YAAApB,QAAA,EAAC;UAEvE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT3L,OAAA,CAACd,sBAAsB;MACrBuS,IAAI,EAAEnP,0BAA2B;MACjCoP,OAAO,EAAER,iCAAkC;MAC3CtO,IAAI,EAAEF,oBAAoB,CAACE,IAAK;MAChCC,MAAM,EAAEH,oBAAoB,CAACG,MAAO;MACpCiP,YAAY,EAAEX,2BAA4B;MAC1CY,mBAAmB,EAAET;IAAwB;MAAA9F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAGF3L,OAAA,CAAC5C,MAAM;MACLqU,IAAI,EAAErO,qBAAsB;MAC5BsO,OAAO,EAAEA,CAAA,KAAMrO,wBAAwB,CAAC,KAAK,CAAE;MAC/CyM,QAAQ,EAAC,IAAI;MACb6B,SAAS;MAAAxG,QAAA,gBAETnL,OAAA,CAAC3C,WAAW;QAAA8N,QAAA,eACVnL,OAAA,CAAC7D,GAAG;UAACkP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEsB,GAAG,EAAE;UAAE,CAAE;UAAAlC,QAAA,gBACzDnL,OAAA,CAACpB,QAAQ;YAAC2N,KAAK,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5B3L,OAAA,CAAC3D,UAAU;YAAC+O,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAa;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd3L,OAAA,CAAC1C,aAAa;QAAA6N,QAAA,eACZnL,OAAA,CAACb,eAAe;UAACyD,IAAI,EAAErB;QAAa;UAAAiK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAChB3L,OAAA,CAACzC,aAAa;QAAA4N,QAAA,eACZnL,OAAA,CAACzD,MAAM;UAACiQ,OAAO,EAAEA,CAAA,KAAMnJ,wBAAwB,CAAC,KAAK,CAAE;UAACkJ,KAAK,EAAC,SAAS;UAAApB,QAAA,EAAC;QAExE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACpL,EAAA,CA5iEIJ,kBAAkB;EAAA,QACLpB,WAAW;AAAA;AAAAiT,EAAA,GADxB7R,kBAAkB;AA8iExB,eAAeA,kBAAkB;AAAC,IAAA6R,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}