{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ParcoCavi.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, CardActions, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Divider, Alert, CircularProgress, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, FormHelperText, IconButton } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon, Save as SaveIcon, ViewList as ViewListIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport ConfigurazioneDialog from './ConfigurazioneDialog';\nimport { validateBobinaData, validateBobinaField, validateBobinaId, isEmpty } from '../../utils/bobinaValidationUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ParcoCavi = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  initialOption = null\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(initialOption);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'Disponibile',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n  const [openConfigDialog, setOpenConfigDialog] = useState(false);\n  const [isFirstInsertion, setIsFirstInsertion] = useState(false);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle bobine');\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica lo storico utilizzo bobine\n  const loadStoricoUtilizzo = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getStoricoUtilizzo(cantiereId);\n      setStoricoUtilizzo(data);\n    } catch (error) {\n      onError('Errore nel caricamento dello storico utilizzo');\n      console.error('Errore nel caricamento dello storico utilizzo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente e gestisce l'opzione iniziale\n  // Utilizziamo una ref per tenere traccia se l'effetto è già stato eseguito\n  const initialLoadDone = React.useRef(false);\n  useEffect(() => {\n    // Esegui solo una volta all'avvio del componente\n    if (!initialLoadDone.current) {\n      console.log('Primo caricamento del componente, initialOption:', initialOption);\n      initialLoadDone.current = true;\n      if (initialOption === 'creaBobina') {\n        console.log('Avvio processo creazione bobina');\n        // IMPORTANTE: Per il primo caricamento con creaBobina, forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE ALL\\'AVVIO');\n        setIsFirstInsertion(true);\n        setOpenConfigDialog(true);\n      } else if (initialOption) {\n        console.log('Eseguendo handleOptionSelect con:', initialOption);\n        handleOptionSelect(initialOption);\n      } else {\n        console.log('Caricando bobine');\n        loadBobine();\n      }\n    }\n  }, []); // Dipendenze vuote per eseguire solo al mount\n\n  // Verifica se è il primo inserimento di una bobina per un cantiere\n  const checkIfFirstInsertion = async () => {\n    // Variabile per memorizzare la configurazione\n    let configurazione = 's'; // Valore predefinito\n\n    try {\n      // Previene chiamate multiple\n      if (loading) {\n        console.log('Operazione già in corso, uscita');\n        return;\n      }\n\n      // Assicuriamoci che nessun dialog sia aperto\n      setOpenDialog(false);\n      setOpenConfigDialog(false); // Chiudi anche il dialog di configurazione se aperto\n\n      setLoading(true);\n      console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n\n      // Gestione caso in cui cantiereId non sia valido\n      if (!cantiereId || isNaN(parseInt(cantiereId))) {\n        onError('ID cantiere non valido');\n        console.error('ID cantiere non valido:', cantiereId);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API reale\n      let isFirst = false;\n      try {\n        console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n        const response = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\n        isFirst = response.is_first_insertion;\n\n        // Controlla se c'è una configurazione salvata in localStorage\n        const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`);\n\n        // Usa la configurazione salvata in localStorage se disponibile, altrimenti usa quella dal server\n        configurazione = savedConfig || response.configurazione || 's';\n        console.log('Configurazione da localStorage:', savedConfig);\n        console.log('Configurazione dal server:', response.configurazione);\n        console.log('Configurazione finale utilizzata:', configurazione);\n        setIsFirstInsertion(isFirst);\n        console.log('È il primo inserimento di una bobina?', isFirst);\n        console.log('Configurazione esistente:', configurazione);\n      } catch (error) {\n        console.error('Errore durante la verifica del primo inserimento:', error);\n        // In caso di errore, assumiamo che non sia il primo inserimento\n        setIsFirstInsertion(false);\n        onError('Errore durante la verifica del primo inserimento. Procedendo con inserimento standard.');\n      }\n      if (isFirst) {\n        // Se è il primo inserimento, mostra il dialog di configurazione\n        console.log('Mostrando il dialog di configurazione');\n        // Assicuriamoci che il dialog di creazione sia chiuso\n        setOpenDialog(false);\n\n        // IMPORTANTE: Forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE');\n        setOpenConfigDialog(true);\n      } else {\n        // Non è il primo inserimento, procedi con il form normale\n        console.log('Non è il primo inserimento, mostrando il form normale');\n\n        // Ottieni il prossimo numero di bobina solo se la configurazione è automatica\n        let nextBobinaNumber = '1';\n        if (configurazione === 's') {\n          try {\n            // Ottieni l'ultimo numero di bobina dal backend\n            const bobine = await parcoCaviService.getBobine(cantiereId);\n            if (bobine && bobine.length > 0) {\n              // Filtra solo le bobine con numero_bobina numerico\n              const numericBobine = bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina));\n\n              // Log per debug\n              console.log('Bobine totali:', bobine.length);\n              console.log('Bobine con numero numerico:', numericBobine.length);\n              console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));\n              if (numericBobine.length > 0) {\n                // Trova il numero massimo tra le bobine esistenti\n                const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));\n                console.log('Numero massimo trovato:', maxNumber);\n                nextBobinaNumber = String(maxNumber + 1);\n              }\n            }\n            console.log('Prossimo numero bobina:', nextBobinaNumber);\n          } catch (error) {\n            console.error('Errore nel recupero del prossimo numero bobina:', error);\n            // In caso di errore, usa 1 come default\n            nextBobinaNumber = '1';\n          }\n        }\n        setDialogType('creaBobina');\n        setFormData({\n          // In modalità automatica, imposta il numero progressivo\n          // In modalità manuale, lascia vuoto per far inserire all'utente\n          numero_bobina: configurazione === 's' ? nextBobinaNumber : '',\n          utility: '',\n          tipologia: '',\n          n_conduttori: '',\n          sezione: '',\n          metri_totali: '',\n          metri_residui: '',\n          stato_bobina: 'Disponibile',\n          ubicazione_bobina: '',\n          fornitore: '',\n          n_DDT: '',\n          data_DDT: '',\n          configurazione: configurazione // Usa la configurazione esistente\n        });\n        setOpenDialog(true);\n      }\n    } catch (error) {\n      // Gestione dettagliata dell'errore\n      let errorMessage = 'Errore nel controllo dell\\'inserimento della prima bobina';\n      if (error.response) {\n        var _error$response$data;\n        // Errore di risposta dal server\n        errorMessage += `: ${error.response.status} - ${((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Errore server'}`;\n        console.error('Dettagli errore API:', error.response);\n      } else if (error.request) {\n        // Errore di rete (nessuna risposta ricevuta)\n        errorMessage += ': Errore di connessione al server';\n      } else {\n        // Errore generico\n        errorMessage += `: ${error.message || 'Errore sconosciuto'}`;\n      }\n      onError(errorMessage);\n      console.error('Errore completo:', error);\n\n      // In caso di errore, mantieni la configurazione esistente o usa il default\n      // Non forzare il reset a 's' per evitare di perdere la configurazione manuale\n      if (!configurazione) {\n        configurazione = 's'; // Fallback al valore di default solo se non è già impostato\n      }\n      setDialogType('creaBobina');\n      setFormData({\n        numero_bobina: configurazione === 's' ? '1' : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configurazione // Usa la configurazione esistente o il default\n      });\n      setOpenDialog(true);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la conferma della configurazione\n  const handleConfigConfirm = async configValue => {\n    console.log('Configurazione selezionata:', configValue);\n    // Salva la configurazione selezionata in localStorage per persistenza\n    localStorage.setItem(`cantiere_${cantiereId}_config`, configValue);\n    setLoading(true);\n    try {\n      // Ottieni il prossimo numero di bobina se la configurazione è automatica\n      let nextBobinaNumber = '1';\n      if (configValue === 's') {\n        try {\n          // Ottieni l'ultimo numero di bobina dal backend\n          const bobine = await parcoCaviService.getBobine(cantiereId);\n          if (bobine && bobine.length > 0) {\n            // Filtra solo le bobine con numero_bobina numerico\n            const numericBobine = bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina));\n\n            // Log per debug\n            console.log('Bobine totali:', bobine.length);\n            console.log('Bobine con numero numerico:', numericBobine.length);\n            console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));\n            if (numericBobine.length > 0) {\n              // Trova il numero massimo tra le bobine esistenti\n              const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));\n              console.log('Numero massimo trovato:', maxNumber);\n              nextBobinaNumber = String(maxNumber + 1);\n            }\n          }\n          console.log('Prossimo numero bobina:', nextBobinaNumber);\n        } catch (error) {\n          console.error('Errore nel recupero del prossimo numero bobina:', error);\n          // In caso di errore, usa 1 come default\n          nextBobinaNumber = '1';\n        }\n      }\n\n      // Imposta i valori di default per la bobina\n      const defaultFormData = {\n        // In modalità automatica, imposta il numero progressivo\n        // In modalità manuale, lascia vuoto per far inserire all'utente\n        numero_bobina: configValue === 's' ? nextBobinaNumber : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configValue // Imposta la configurazione scelta\n      };\n      console.log('Impostando i dati del form con configurazione:', configValue, 'numero_bobina:', defaultFormData.numero_bobina);\n\n      // Importante: prima prepara il form, poi chiudi il dialog di configurazione\n      // e solo dopo apri il dialog di creazione\n      setFormData(defaultFormData);\n      setDialogType('creaBobina');\n\n      // Chiudi il dialog di configurazione\n      setOpenConfigDialog(false);\n\n      // Piccolo timeout per assicurarsi che il dialog di configurazione sia chiuso\n      // prima di aprire il dialog di creazione\n      setTimeout(() => {\n        setOpenDialog(true);\n        console.log('Dialog di creazione bobina aperto con numero_bobina:', defaultFormData.numero_bobina);\n      }, 300);\n    } catch (error) {\n      console.error('Errore durante la preparazione del form:', error);\n      onError('Errore durante la preparazione del form: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      checkIfFirstInsertion();\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n\n    // Recupera la configurazione salvata per questo cantiere\n    const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`) || 's';\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: savedConfig // Mantieni la configurazione salvata\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = bobina => {\n    console.log('Bobina selezionata:', bobina);\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n\n      // Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe\n      setFormData({\n        numero_bobina: bobina.numero_bobina || '',\n        utility: String(bobina.utility || ''),\n        tipologia: String(bobina.tipologia || ''),\n        n_conduttori: bobina.n_conduttori !== null && bobina.n_conduttori !== undefined ? Number(bobina.n_conduttori) : '',\n        sezione: bobina.sezione !== null && bobina.sezione !== undefined ? Number(bobina.sezione) : '',\n        metri_totali: bobina.metri_totali !== null && bobina.metri_totali !== undefined ? Number(bobina.metri_totali) : '',\n        metri_residui: bobina.metri_residui !== null && bobina.metri_residui !== undefined ? Number(bobina.metri_residui) : '',\n        stato_bobina: String(bobina.stato_bobina || 'Disponibile'),\n        ubicazione_bobina: String(bobina.ubicazione_bobina || ''),\n        fornitore: String(bobina.fornitore || ''),\n        n_DDT: String(bobina.n_DDT || ''),\n        data_DDT: bobina.data_DDT || '',\n        configurazione: String(bobina.configurazione || 's')\n      });\n      console.log('Form data impostati per la modifica:', {\n        numero_bobina: bobina.numero_bobina,\n        utility: bobina.utility,\n        tipologia: bobina.tipologia,\n        n_conduttori: bobina.n_conduttori,\n        sezione: bobina.sezione,\n        metri_totali: bobina.metri_totali,\n        metri_residui: bobina.metri_residui\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      // Validazione speciale per numero_bobina quando configurazione è 'n'\n      if (name === 'numero_bobina' && formData.configurazione === 'n') {\n        const idResult = validateBobinaId(value);\n        if (!idResult.valid) {\n          setFormErrors(prev => ({\n            ...prev,\n            [name]: idResult.message\n          }));\n        } else {\n          setFormErrors(prev => {\n            const newErrors = {\n              ...prev\n            };\n            delete newErrors[name];\n            return newErrors;\n          });\n        }\n        return;\n      }\n      const result = validateBobinaField(name, value);\n\n      // Aggiorna gli errori\n      if (!result.valid) {\n        setFormErrors(prev => ({\n          ...prev,\n          [name]: result.message\n        }));\n      } else {\n        setFormErrors(prev => {\n          const newErrors = {\n            ...prev\n          };\n          delete newErrors[name];\n          return newErrors;\n        });\n\n        // Aggiorna gli avvisi\n        if (result.warning) {\n          setFormWarnings(prev => ({\n            ...prev,\n            [name]: result.message\n          }));\n        } else {\n          setFormWarnings(prev => {\n            const newWarnings = {\n              ...prev\n            };\n            delete newWarnings[name];\n            return newWarnings;\n          });\n        }\n      }\n    }\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      // Validazione completa dei dati prima del salvataggio\n      if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n        const validation = validateBobinaData(formData);\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          onError('Correggi gli errori nel form prima di salvare');\n          return;\n        }\n\n        // Verifica aggiuntiva per il campo numero_bobina quando la configurazione è manuale\n        if (formData.configurazione === 'n' && (!formData.numero_bobina || formData.numero_bobina.trim() === '')) {\n          setFormErrors({\n            ...formErrors,\n            numero_bobina: 'L\\'ID della bobina è obbligatorio'\n          });\n          onError('L\\'ID della bobina è obbligatorio');\n          return;\n        }\n      }\n      setLoading(true);\n      console.log('Salvataggio dati bobina in corso...');\n      if (dialogType === 'creaBobina') {\n        // Prepara i dati per la creazione della bobina\n        const bobinaData = {\n          ...formData,\n          // Assicurati che tutti i campi siano nel formato corretto\n          numero_bobina: String(formData.numero_bobina || ''),\n          utility: String(formData.utility || ''),\n          tipologia: String(formData.tipologia || ''),\n          n_conduttori: String(formData.n_conduttori || ''),\n          sezione: String(formData.sezione || ''),\n          metri_totali: parseFloat(formData.metri_totali) || 0,\n          ubicazione_bobina: String(formData.ubicazione_bobina || 'TBD'),\n          fornitore: String(formData.fornitore || 'TBD'),\n          n_DDT: String(formData.n_DDT || 'TBD'),\n          data_DDT: formData.data_DDT || null,\n          // Assicurati che la configurazione sia impostata correttamente\n          configurazione: String(formData.configurazione || 's')\n        };\n        console.log('Dati bobina da inviare:', bobinaData);\n        try {\n          // Log dei dati che stiamo per inviare\n          console.log('Invio dati bobina al backend:', JSON.stringify(bobinaData, null, 2));\n\n          // Invia i dati al backend\n          await parcoCaviService.createBobina(cantiereId, bobinaData);\n          onSuccess('Bobina creata con successo');\n\n          // Chiudi il dialog\n          handleCloseDialog();\n\n          // Imposta l'opzione selezionata a visualizzaBobine e carica le bobine\n          setSelectedOption('visualizzaBobine');\n          loadBobine();\n\n          // Notifica al componente padre che dovrebbe reindirizzare alla pagina di visualizzazione bobine\n          if (typeof window !== 'undefined') {\n            // Usa un evento personalizzato per comunicare con il componente padre\n            const event = new CustomEvent('redirectToVisualizzaBobine', {\n              detail: {\n                cantiereId\n              }\n            });\n            window.dispatchEvent(event);\n          }\n        } catch (error) {\n          console.error('Errore durante la creazione della bobina:', error);\n\n          // Gestione dettagliata dell'errore\n          let errorMessage = 'Errore durante la creazione della bobina';\n          if (error.detail) {\n            errorMessage = error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n\n          // Log dettagliato dell'errore\n          console.error('Dettagli errore:', errorMessage);\n          console.error('Dati inviati:', JSON.stringify(bobinaData, null, 2));\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'modificaBobina') {\n        // Per la modifica, usa l'ID bobina completo o il numero bobina\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina || formData.numero_bobina;\n        console.log('Modifica bobina con ID:', bobinaId);\n        console.log('Dati da inviare:', formData);\n        try {\n          const response = await parcoCaviService.updateBobina(cantiereId, bobinaId, formData);\n          console.log('Risposta modifica bobina:', response);\n          onSuccess('Bobina modificata con successo');\n        } catch (error) {\n          console.error('Errore durante la modifica della bobina:', error);\n          onError(error.detail || 'Errore durante la modifica della bobina');\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'eliminaBobina') {\n        // Per l'eliminazione, usa l'ID bobina completo\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina;\n        const response = await parcoCaviService.deleteBobina(cantiereId, bobinaId);\n\n        // Verifica se è stata eliminata l'ultima bobina\n        if (response.is_last_bobina) {\n          console.log(`Eliminata l'ultima bobina del cantiere ${cantiereId}. Il parco cavi è ora vuoto.`);\n          onSuccess('Bobina eliminata con successo. Il parco cavi è ora vuoto.');\n\n          // Forza il refresh della pagina per reinnescare il sistema del primo inserimento\n          setTimeout(() => {\n            // Imposta l'opzione selezionata a creaBobina per forzare il dialog di configurazione\n            setSelectedOption('creaBobina');\n            // Forza l'apertura del dialog di configurazione\n            setIsFirstInsertion(true);\n            setOpenConfigDialog(true);\n          }, 1000);\n        } else {\n          onSuccess('Bobina eliminata con successo');\n        }\n      }\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le bobine in formato lista\n  const renderBobineCards = () => {\n    if (bobine.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Nessuna bobina disponibile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      sx: {\n        mt: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        size: \"small\",\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              bgcolor: '#f5f5f5'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"ID Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 679,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Utility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"N\\xB0 Cond.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Sezione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri Tot.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri Res.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(TableRow, {\n            hover: true,\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.numero_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.utility || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.tipologia || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.n_conduttori || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.sezione || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [bobina.metri_totali || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [bobina.metri_residui || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                component: \"span\",\n                sx: {\n                  px: 1,\n                  py: 0.5,\n                  borderRadius: 1,\n                  bgcolor: bobina.stato_bobina === 'Disponibile' ? 'success.light' : bobina.stato_bobina === 'In Uso' ? 'warning.light' : 'error.light',\n                  color: 'white',\n                  fontSize: '0.75rem',\n                  fontWeight: 'bold'\n                },\n                children: bobina.stato_bobina || 'DISPONIBILE'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.ubicazione_bobina || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  color: \"primary\",\n                  onClick: () => {\n                    setDialogType('selezionaBobina');\n                    handleBobinaSelect(bobina);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  color: \"error\",\n                  onClick: () => {\n                    setDialogType('eliminaBobina');\n                    setSelectedBobina(bobina);\n                    setOpenDialog(true);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 740,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 17\n            }, this)]\n          }, bobina.numero_bobina, true, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 676,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 675,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 757,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [dialogType === 'modificaBobina' && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mb: 2,\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              fontWeight: \"bold\",\n              children: \"Condizioni per la modifica:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"La bobina deve essere nello stato \\\"Disponibile\\\"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"La bobina non deve essere associata a nessun cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato attuale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 21\n                }, this), \" \", (selectedBobina === null || selectedBobina === void 0 ? void 0 : selectedBobina.stato_bobina) || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 762,\n            columnNumber: 15\n          }, this), Object.keys(formWarnings).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2,\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              children: \"Attenzione:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                margin: 0,\n                paddingLeft: '20px'\n              },\n              children: Object.values(formWarnings).map((warning, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: warning\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 782,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"numero_bobina\",\n                label: \"ID Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.numero_bobina,\n                onChange: handleFormChange,\n                disabled: dialogType === 'modificaBobina' || dialogType === 'creaBobina' && formData.configurazione === 's',\n                required: true,\n                error: !!formErrors.numero_bobina,\n                InputProps: {\n                  sx: {\n                    bgcolor: formData.configurazione === 's' ? '#f5f5f5' : 'transparent',\n                    fontWeight: 'bold'\n                  }\n                },\n                helperText: formErrors.numero_bobina || (dialogType === 'creaBobina' && formData.configurazione === 's' ? `ID completo: C${cantiereId}_B${formData.numero_bobina || ''} (generato automaticamente)` : dialogType === 'creaBobina' && formData.configurazione === 'n' ? `Inserisci l'ID della bobina (es. A123). ID completo sarà: C${cantiereId}_B{tuo input}` : ''),\n                type: formData.configurazione === 's' ? \"text\" : \"text\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utility\",\n                label: \"Utility\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utility,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.utility,\n                helperText: formErrors.utility || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"tipologia\",\n                label: \"Tipologia\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.tipologia,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.tipologia,\n                helperText: formErrors.tipologia || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 833,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 832,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_conduttori\",\n                label: \"N\\xB0 Conduttori\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_conduttori,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.n_conduttori,\n                helperText: formErrors.n_conduttori || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sezione\",\n                label: \"Sezione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sezione,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.sezione,\n                helperText: formErrors.sezione || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_totali\",\n                label: \"Metri Totali\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_totali,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.metri_totali,\n                helperText: formErrors.metri_totali || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 15\n            }, this), dialogType === 'modificaBobina' && /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_residui\",\n                label: \"Metri Residui\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_residui,\n                onChange: handleFormChange,\n                required: true,\n                disabled: true,\n                helperText: \"I metri residui non possono essere modificati direttamente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"stato-bobina-label\",\n                  children: \"Stato Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 903,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: \"stato-bobina-label\",\n                  name: \"stato_bobina\",\n                  value: formData.stato_bobina,\n                  label: \"Stato Bobina\",\n                  onChange: handleFormChange,\n                  disabled: dialogType === 'creaBobina',\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Disponibile\",\n                    children: \"Disponibile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 912,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"In uso\",\n                    children: \"In uso\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 913,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Terminata\",\n                    children: \"Terminata\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Danneggiata\",\n                    children: \"Danneggiata\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 915,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Over\",\n                    children: \"Over\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 916,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 904,\n                  columnNumber: 19\n                }, this), dialogType === 'creaBobina' && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: \"Per una nuova bobina, lo stato \\xE8 sempre \\\"Disponibile\\\"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 919,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 902,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_bobina\",\n                label: \"Ubicazione Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_bobina,\n                onChange: handleFormChange,\n                error: !!formErrors.ubicazione_bobina,\n                helperText: formErrors.ubicazione_bobina || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 924,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 923,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"fornitore\",\n                label: \"Fornitore\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.fornitore,\n                onChange: handleFormChange,\n                error: !!formErrors.fornitore,\n                helperText: formErrors.fornitore || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 935,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_DDT\",\n                label: \"Numero DDT\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_DDT,\n                onChange: handleFormChange,\n                error: !!formErrors.n_DDT,\n                helperText: formErrors.n_DDT || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 948,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 947,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"data_DDT\",\n                label: \"Data DDT (YYYY-MM-DD)\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.data_DDT,\n                onChange: handleFormChange,\n                placeholder: \"YYYY-MM-DD\",\n                error: !!formErrors.data_DDT,\n                helperText: formErrors.data_DDT || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 960,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"configurazione\",\n                label: \"Modalit\\xE0 Numerazione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.configurazione === 's' ? 'Automatica' : 'Manuale',\n                InputProps: {\n                  readOnly: true,\n                  sx: {\n                    bgcolor: '#f5f5f5'\n                  }\n                },\n                helperText: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    fontWeight: 'medium',\n                    color: formData.configurazione === 's' ? 'success.main' : 'info.main'\n                  },\n                  children: formData.configurazione === 's' ? 'Numerazione progressiva automatica (1, 2, 3, ...)' : 'Inserimento manuale dell\\'ID bobina (es. A123, SPEC01, ...)'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 986,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 973,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 972,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 760,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 997,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || Object.keys(formErrors).length > 0,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 69\n            }, this),\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 998,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 996,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 756,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Seleziona Bobina da Modificare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1013,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1016,\n            columnNumber: 15\n          }, this) : bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna bobina disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1018,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleBobinaSelect(bobina),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Bobina: ${bobina.numero_bobina}`,\n                secondary: `Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1027,\n                columnNumber: 21\n              }, this)\n            }, bobina.numero_bobina, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1022,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1020,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1014,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1037,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1036,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1012,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'eliminaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Elimina Bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1044,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: !selectedBobina ? loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1048,\n            columnNumber: 17\n          }, this) : bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna bobina disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1050,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => setSelectedBobina(bobina),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Bobina: ${bobina.numero_bobina}`,\n                secondary: `Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1059,\n                columnNumber: 23\n              }, this)\n            }, bobina.numero_bobina, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1054,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1052,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mb: 2\n              },\n              children: [\"Sei sicuro di voler eliminare la bobina \", selectedBobina.numero_bobina, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1069,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 2\n              },\n              children: \"Questa operazione non pu\\xF2 essere annullata.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1072,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                fontWeight: \"bold\",\n                children: \"Condizioni per l'eliminazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1076,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"La bobina deve essere completamente integra (metri residui = metri totali)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1080,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"La bobina deve essere nello stato \\\"Disponibile\\\"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1081,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"La bobina non deve essere associata a nessun cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1079,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1075,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 1,\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato attuale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1087,\n                  columnNumber: 21\n                }, this), \" \", selectedBobina.stato_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1086,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri totali:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1090,\n                  columnNumber: 21\n                }, this), \" \", selectedBobina.metri_totali || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1089,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri residui:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1093,\n                  columnNumber: 21\n                }, this), \" \", selectedBobina.metri_residui || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1092,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1085,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1068,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1045,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1100,\n            columnNumber: 13\n          }, this), selectedBobina && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1106,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1106,\n              columnNumber: 71\n            }, this),\n            children: \"Elimina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1102,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1099,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1043,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'visualizzaStorico') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"lg\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Storico Utilizzo Bobine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1120,\n            columnNumber: 15\n          }, this) : storicoUtilizzo.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun dato storico disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1122,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Bobina\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1128,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Utility\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1129,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Tipologia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1130,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"N\\xB0 Conduttori\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1131,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Sezione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1132,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1133,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Residui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1134,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Cavi Associati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1135,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1127,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: storicoUtilizzo.map((record, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.numero_bobina\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1141,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.utility\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1142,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1143,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.n_conduttori\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1144,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.sezione\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1145,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.metri_totali\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1146,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.metri_residui\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1147,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.cavi.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1148,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1140,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1138,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1125,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1124,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Chiudi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1157,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1156,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1116,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [selectedOption === 'visualizzaBobine' && !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"inherit\",\n            startIcon: /*#__PURE__*/_jsxDEV(HistoryIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1175,\n              columnNumber: 28\n            }, this),\n            onClick: () => {\n              loadStoricoUtilizzo();\n              setDialogType('visualizzaStorico');\n              setOpenDialog(true);\n            },\n            sx: {\n              fontWeight: 'medium',\n              borderRadius: 0\n            },\n            children: \"Storico Utilizzo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1172,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"inherit\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1191,\n              columnNumber: 28\n            }, this),\n            onClick: () => checkIfFirstInsertion(),\n            sx: {\n              fontWeight: 'medium',\n              borderRadius: 0\n            },\n            children: \"Crea Nuova Bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1188,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1171,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1170,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1204,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1203,\n        columnNumber: 13\n      }, this) : renderBobineCards()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1169,\n      columnNumber: 9\n    }, this) : !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        minHeight: '300px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: !selectedOption ? /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: \"Seleziona un'opzione dal menu principale per iniziare.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1213,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [selectedOption === 'creaBobina' && 'Crea Nuova Bobina', selectedOption === 'modificaBobina' && 'Modifica Bobina', selectedOption === 'eliminaBobina' && 'Elimina Bobina', selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1218,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n          sx: {\n            mt: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1224,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1217,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1211,\n      columnNumber: 9\n    }, this) : null, renderDialog(), /*#__PURE__*/_jsxDEV(ConfigurazioneDialog, {\n      open: openConfigDialog,\n      onClose: () => setOpenConfigDialog(false),\n      onConfirm: handleConfigConfirm\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1233,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1167,\n    columnNumber: 5\n  }, this);\n};\n_s(ParcoCavi, \"aPVvt0JmyP4/WtJb67LlF7JuBzs=\");\n_c = ParcoCavi;\nexport default ParcoCavi;\nvar _c;\n$RefreshReg$(_c, \"ParcoCavi\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Divider", "<PERSON><PERSON>", "CircularProgress", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "FormHelperText", "IconButton", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "Save", "SaveIcon", "ViewList", "ViewListIcon", "Warning", "WarningIcon", "parcoCaviService", "ConfigurazioneDialog", "validateBobinaData", "validateBob<PERSON>F<PERSON>", "validateBobinaId", "isEmpty", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cantiereId", "onSuccess", "onError", "initialOption", "_s", "loading", "setLoading", "bobine", "set<PERSON>ob<PERSON>", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedBobina", "formData", "setFormData", "numero_bobina", "utility", "tipologia", "n_conduttori", "sezione", "metri_totali", "metri_residui", "stato_bobina", "ubicazione_bobina", "fornitore", "n_DDT", "data_DDT", "configurazione", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "storico<PERSON><PERSON><PERSON><PERSON>", "setStoricoUtilizzo", "openConfigDialog", "setOpenConfigDialog", "isFirstInsertion", "setIsFirstInsertion", "loadBobine", "data", "getBobine", "error", "console", "loadStoricoUtilizzo", "getStoricoUtilizzo", "initialLoadDone", "useRef", "current", "log", "handleOptionSelect", "checkIfFirstInsertion", "isNaN", "parseInt", "<PERSON><PERSON><PERSON><PERSON>", "response", "isFirstBobinaInsertion", "is_first_insertion", "savedConfig", "localStorage", "getItem", "nextBobinaNumber", "length", "numericBobine", "filter", "b", "test", "map", "maxNumber", "Math", "max", "String", "errorMessage", "_error$response$data", "status", "detail", "request", "message", "handleConfigConfirm", "config<PERSON><PERSON><PERSON>", "setItem", "defaultFormData", "setTimeout", "option", "handleCloseDialog", "handleBobinaSelect", "bobina", "undefined", "Number", "handleFormChange", "e", "name", "value", "target", "idResult", "valid", "prev", "newErrors", "result", "warning", "newWarnings", "handleSave", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "trim", "bobina<PERSON><PERSON>", "parseFloat", "JSON", "stringify", "createBobina", "window", "event", "CustomEvent", "dispatchEvent", "bobina<PERSON>d", "id_bobina", "updateBobina", "deleteBobina", "is_last_bobina", "renderBobineCards", "severity", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "sx", "mt", "size", "bgcolor", "hover", "px", "py", "borderRadius", "color", "fontSize", "fontWeight", "display", "gap", "onClick", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "mb", "variant", "Object", "keys", "style", "margin", "paddingLeft", "values", "index", "container", "spacing", "item", "xs", "sm", "label", "onChange", "disabled", "required", "InputProps", "helperText", "type", "id", "labelId", "placeholder", "readOnly", "startIcon", "button", "primary", "secondary", "flexDirection", "record", "cavi", "p", "justifyContent", "alignItems", "my", "minHeight", "textAlign", "gutterBottom", "onConfirm", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ParcoCavi.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Divider,\n  Alert,\n  CircularProgress,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  FormHelperText,\n  IconButton\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  History as HistoryIcon,\n  Save as SaveIcon,\n  ViewList as ViewListIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport ConfigurazioneDialog from './ConfigurazioneDialog';\nimport { validateBobinaData, validateBobinaField, validateBobinaId, isEmpty } from '../../utils/bobinaValidationUtils';\n\nconst ParcoCavi = ({ cantiereId, onSuccess, onError, initialOption = null }) => {\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(initialOption);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'Disponibile',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n  const [openConfigDialog, setOpenConfigDialog] = useState(false);\n  const [isFirstInsertion, setIsFirstInsertion] = useState(false);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle bobine');\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica lo storico utilizzo bobine\n  const loadStoricoUtilizzo = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getStoricoUtilizzo(cantiereId);\n      setStoricoUtilizzo(data);\n    } catch (error) {\n      onError('Errore nel caricamento dello storico utilizzo');\n      console.error('Errore nel caricamento dello storico utilizzo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente e gestisce l'opzione iniziale\n  // Utilizziamo una ref per tenere traccia se l'effetto è già stato eseguito\n  const initialLoadDone = React.useRef(false);\n\n  useEffect(() => {\n    // Esegui solo una volta all'avvio del componente\n    if (!initialLoadDone.current) {\n      console.log('Primo caricamento del componente, initialOption:', initialOption);\n      initialLoadDone.current = true;\n\n      if (initialOption === 'creaBobina') {\n        console.log('Avvio processo creazione bobina');\n        // IMPORTANTE: Per il primo caricamento con creaBobina, forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE ALL\\'AVVIO');\n        setIsFirstInsertion(true);\n        setOpenConfigDialog(true);\n      } else if (initialOption) {\n        console.log('Eseguendo handleOptionSelect con:', initialOption);\n        handleOptionSelect(initialOption);\n      } else {\n        console.log('Caricando bobine');\n        loadBobine();\n      }\n    }\n  }, []);  // Dipendenze vuote per eseguire solo al mount\n\n  // Verifica se è il primo inserimento di una bobina per un cantiere\n  const checkIfFirstInsertion = async () => {\n    // Variabile per memorizzare la configurazione\n    let configurazione = 's'; // Valore predefinito\n\n    try {\n      // Previene chiamate multiple\n      if (loading) {\n        console.log('Operazione già in corso, uscita');\n        return;\n      }\n\n      // Assicuriamoci che nessun dialog sia aperto\n      setOpenDialog(false);\n      setOpenConfigDialog(false); // Chiudi anche il dialog di configurazione se aperto\n\n      setLoading(true);\n      console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n\n      // Gestione caso in cui cantiereId non sia valido\n      if (!cantiereId || isNaN(parseInt(cantiereId))) {\n        onError('ID cantiere non valido');\n        console.error('ID cantiere non valido:', cantiereId);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API reale\n      let isFirst = false;\n\n      try {\n        console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n        const response = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\n        isFirst = response.is_first_insertion;\n\n        // Controlla se c'è una configurazione salvata in localStorage\n        const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`);\n\n        // Usa la configurazione salvata in localStorage se disponibile, altrimenti usa quella dal server\n        configurazione = savedConfig || response.configurazione || 's';\n\n        console.log('Configurazione da localStorage:', savedConfig);\n        console.log('Configurazione dal server:', response.configurazione);\n        console.log('Configurazione finale utilizzata:', configurazione);\n\n        setIsFirstInsertion(isFirst);\n        console.log('È il primo inserimento di una bobina?', isFirst);\n        console.log('Configurazione esistente:', configurazione);\n      } catch (error) {\n        console.error('Errore durante la verifica del primo inserimento:', error);\n        // In caso di errore, assumiamo che non sia il primo inserimento\n        setIsFirstInsertion(false);\n        onError('Errore durante la verifica del primo inserimento. Procedendo con inserimento standard.');\n      }\n\n      if (isFirst) {\n        // Se è il primo inserimento, mostra il dialog di configurazione\n        console.log('Mostrando il dialog di configurazione');\n        // Assicuriamoci che il dialog di creazione sia chiuso\n        setOpenDialog(false);\n\n        // IMPORTANTE: Forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE');\n        setOpenConfigDialog(true);\n      } else {\n        // Non è il primo inserimento, procedi con il form normale\n        console.log('Non è il primo inserimento, mostrando il form normale');\n\n        // Ottieni il prossimo numero di bobina solo se la configurazione è automatica\n        let nextBobinaNumber = '1';\n        if (configurazione === 's') {\n          try {\n            // Ottieni l'ultimo numero di bobina dal backend\n            const bobine = await parcoCaviService.getBobine(cantiereId);\n            if (bobine && bobine.length > 0) {\n              // Filtra solo le bobine con numero_bobina numerico\n              const numericBobine = bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina));\n\n              // Log per debug\n              console.log('Bobine totali:', bobine.length);\n              console.log('Bobine con numero numerico:', numericBobine.length);\n              console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));\n\n              if (numericBobine.length > 0) {\n                // Trova il numero massimo tra le bobine esistenti\n                const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));\n                console.log('Numero massimo trovato:', maxNumber);\n                nextBobinaNumber = String(maxNumber + 1);\n              }\n            }\n            console.log('Prossimo numero bobina:', nextBobinaNumber);\n          } catch (error) {\n            console.error('Errore nel recupero del prossimo numero bobina:', error);\n            // In caso di errore, usa 1 come default\n            nextBobinaNumber = '1';\n          }\n        }\n\n        setDialogType('creaBobina');\n        setFormData({\n          // In modalità automatica, imposta il numero progressivo\n          // In modalità manuale, lascia vuoto per far inserire all'utente\n          numero_bobina: configurazione === 's' ? nextBobinaNumber : '',\n          utility: '',\n          tipologia: '',\n          n_conduttori: '',\n          sezione: '',\n          metri_totali: '',\n          metri_residui: '',\n          stato_bobina: 'Disponibile',\n          ubicazione_bobina: '',\n          fornitore: '',\n          n_DDT: '',\n          data_DDT: '',\n          configurazione: configurazione  // Usa la configurazione esistente\n        });\n        setOpenDialog(true);\n      }\n    } catch (error) {\n      // Gestione dettagliata dell'errore\n      let errorMessage = 'Errore nel controllo dell\\'inserimento della prima bobina';\n\n      if (error.response) {\n        // Errore di risposta dal server\n        errorMessage += `: ${error.response.status} - ${error.response.data?.detail || 'Errore server'}`;\n        console.error('Dettagli errore API:', error.response);\n      } else if (error.request) {\n        // Errore di rete (nessuna risposta ricevuta)\n        errorMessage += ': Errore di connessione al server';\n      } else {\n        // Errore generico\n        errorMessage += `: ${error.message || 'Errore sconosciuto'}`;\n      }\n\n      onError(errorMessage);\n      console.error('Errore completo:', error);\n\n      // In caso di errore, mantieni la configurazione esistente o usa il default\n      // Non forzare il reset a 's' per evitare di perdere la configurazione manuale\n      if (!configurazione) {\n        configurazione = 's'; // Fallback al valore di default solo se non è già impostato\n      }\n\n      setDialogType('creaBobina');\n      setFormData({\n        numero_bobina: configurazione === 's' ? '1' : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configurazione  // Usa la configurazione esistente o il default\n      });\n      setOpenDialog(true);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la conferma della configurazione\n  const handleConfigConfirm = async (configValue) => {\n    console.log('Configurazione selezionata:', configValue);\n    // Salva la configurazione selezionata in localStorage per persistenza\n    localStorage.setItem(`cantiere_${cantiereId}_config`, configValue);\n    setLoading(true);\n\n    try {\n      // Ottieni il prossimo numero di bobina se la configurazione è automatica\n      let nextBobinaNumber = '1';\n      if (configValue === 's') {\n        try {\n          // Ottieni l'ultimo numero di bobina dal backend\n          const bobine = await parcoCaviService.getBobine(cantiereId);\n          if (bobine && bobine.length > 0) {\n            // Filtra solo le bobine con numero_bobina numerico\n            const numericBobine = bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina));\n\n            // Log per debug\n            console.log('Bobine totali:', bobine.length);\n            console.log('Bobine con numero numerico:', numericBobine.length);\n            console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));\n\n            if (numericBobine.length > 0) {\n              // Trova il numero massimo tra le bobine esistenti\n              const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));\n              console.log('Numero massimo trovato:', maxNumber);\n              nextBobinaNumber = String(maxNumber + 1);\n            }\n          }\n          console.log('Prossimo numero bobina:', nextBobinaNumber);\n        } catch (error) {\n          console.error('Errore nel recupero del prossimo numero bobina:', error);\n          // In caso di errore, usa 1 come default\n          nextBobinaNumber = '1';\n        }\n      }\n\n      // Imposta i valori di default per la bobina\n      const defaultFormData = {\n        // In modalità automatica, imposta il numero progressivo\n        // In modalità manuale, lascia vuoto per far inserire all'utente\n        numero_bobina: configValue === 's' ? nextBobinaNumber : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configValue // Imposta la configurazione scelta\n      };\n\n      console.log('Impostando i dati del form con configurazione:', configValue, 'numero_bobina:', defaultFormData.numero_bobina);\n\n      // Importante: prima prepara il form, poi chiudi il dialog di configurazione\n      // e solo dopo apri il dialog di creazione\n      setFormData(defaultFormData);\n      setDialogType('creaBobina');\n\n      // Chiudi il dialog di configurazione\n      setOpenConfigDialog(false);\n\n      // Piccolo timeout per assicurarsi che il dialog di configurazione sia chiuso\n      // prima di aprire il dialog di creazione\n      setTimeout(() => {\n        setOpenDialog(true);\n        console.log('Dialog di creazione bobina aperto con numero_bobina:', defaultFormData.numero_bobina);\n      }, 300);\n    } catch (error) {\n      console.error('Errore durante la preparazione del form:', error);\n      onError('Errore durante la preparazione del form: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      checkIfFirstInsertion();\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n\n    // Recupera la configurazione salvata per questo cantiere\n    const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`) || 's';\n\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: savedConfig // Mantieni la configurazione salvata\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = (bobina) => {\n    console.log('Bobina selezionata:', bobina);\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n\n      // Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe\n      setFormData({\n        numero_bobina: bobina.numero_bobina || '',\n        utility: String(bobina.utility || ''),\n        tipologia: String(bobina.tipologia || ''),\n        n_conduttori: bobina.n_conduttori !== null && bobina.n_conduttori !== undefined ? Number(bobina.n_conduttori) : '',\n        sezione: bobina.sezione !== null && bobina.sezione !== undefined ? Number(bobina.sezione) : '',\n        metri_totali: bobina.metri_totali !== null && bobina.metri_totali !== undefined ? Number(bobina.metri_totali) : '',\n        metri_residui: bobina.metri_residui !== null && bobina.metri_residui !== undefined ? Number(bobina.metri_residui) : '',\n        stato_bobina: String(bobina.stato_bobina || 'Disponibile'),\n        ubicazione_bobina: String(bobina.ubicazione_bobina || ''),\n        fornitore: String(bobina.fornitore || ''),\n        n_DDT: String(bobina.n_DDT || ''),\n        data_DDT: bobina.data_DDT || '',\n        configurazione: String(bobina.configurazione || 's')\n      });\n\n      console.log('Form data impostati per la modifica:', {\n        numero_bobina: bobina.numero_bobina,\n        utility: bobina.utility,\n        tipologia: bobina.tipologia,\n        n_conduttori: bobina.n_conduttori,\n        sezione: bobina.sezione,\n        metri_totali: bobina.metri_totali,\n        metri_residui: bobina.metri_residui\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      // Validazione speciale per numero_bobina quando configurazione è 'n'\n      if (name === 'numero_bobina' && formData.configurazione === 'n') {\n        const idResult = validateBobinaId(value);\n        if (!idResult.valid) {\n          setFormErrors(prev => ({\n            ...prev,\n            [name]: idResult.message\n          }));\n        } else {\n          setFormErrors(prev => {\n            const newErrors = { ...prev };\n            delete newErrors[name];\n            return newErrors;\n          });\n        }\n        return;\n      }\n\n      const result = validateBobinaField(name, value);\n\n      // Aggiorna gli errori\n      if (!result.valid) {\n        setFormErrors(prev => ({\n          ...prev,\n          [name]: result.message\n        }));\n      } else {\n        setFormErrors(prev => {\n          const newErrors = { ...prev };\n          delete newErrors[name];\n          return newErrors;\n        });\n\n        // Aggiorna gli avvisi\n        if (result.warning) {\n          setFormWarnings(prev => ({\n            ...prev,\n            [name]: result.message\n          }));\n        } else {\n          setFormWarnings(prev => {\n            const newWarnings = { ...prev };\n            delete newWarnings[name];\n            return newWarnings;\n          });\n        }\n      }\n    }\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      // Validazione completa dei dati prima del salvataggio\n      if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n        const validation = validateBobinaData(formData);\n\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          onError('Correggi gli errori nel form prima di salvare');\n          return;\n        }\n\n        // Verifica aggiuntiva per il campo numero_bobina quando la configurazione è manuale\n        if (formData.configurazione === 'n' && (!formData.numero_bobina || formData.numero_bobina.trim() === '')) {\n          setFormErrors({\n            ...formErrors,\n            numero_bobina: 'L\\'ID della bobina è obbligatorio'\n          });\n          onError('L\\'ID della bobina è obbligatorio');\n          return;\n        }\n      }\n\n      setLoading(true);\n      console.log('Salvataggio dati bobina in corso...');\n\n      if (dialogType === 'creaBobina') {\n        // Prepara i dati per la creazione della bobina\n        const bobinaData = {\n          ...formData,\n          // Assicurati che tutti i campi siano nel formato corretto\n          numero_bobina: String(formData.numero_bobina || ''),\n          utility: String(formData.utility || ''),\n          tipologia: String(formData.tipologia || ''),\n          n_conduttori: String(formData.n_conduttori || ''),\n          sezione: String(formData.sezione || ''),\n          metri_totali: parseFloat(formData.metri_totali) || 0,\n          ubicazione_bobina: String(formData.ubicazione_bobina || 'TBD'),\n          fornitore: String(formData.fornitore || 'TBD'),\n          n_DDT: String(formData.n_DDT || 'TBD'),\n          data_DDT: formData.data_DDT || null,\n          // Assicurati che la configurazione sia impostata correttamente\n          configurazione: String(formData.configurazione || 's')\n        };\n\n        console.log('Dati bobina da inviare:', bobinaData);\n\n        try {\n          // Log dei dati che stiamo per inviare\n          console.log('Invio dati bobina al backend:', JSON.stringify(bobinaData, null, 2));\n\n          // Invia i dati al backend\n          await parcoCaviService.createBobina(cantiereId, bobinaData);\n          onSuccess('Bobina creata con successo');\n\n          // Chiudi il dialog\n          handleCloseDialog();\n\n          // Imposta l'opzione selezionata a visualizzaBobine e carica le bobine\n          setSelectedOption('visualizzaBobine');\n          loadBobine();\n\n          // Notifica al componente padre che dovrebbe reindirizzare alla pagina di visualizzazione bobine\n          if (typeof window !== 'undefined') {\n            // Usa un evento personalizzato per comunicare con il componente padre\n            const event = new CustomEvent('redirectToVisualizzaBobine', { detail: { cantiereId } });\n            window.dispatchEvent(event);\n          }\n        } catch (error) {\n          console.error('Errore durante la creazione della bobina:', error);\n\n          // Gestione dettagliata dell'errore\n          let errorMessage = 'Errore durante la creazione della bobina';\n\n          if (error.detail) {\n            errorMessage = error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n\n          // Log dettagliato dell'errore\n          console.error('Dettagli errore:', errorMessage);\n          console.error('Dati inviati:', JSON.stringify(bobinaData, null, 2));\n\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'modificaBobina') {\n        // Per la modifica, usa l'ID bobina completo o il numero bobina\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina || formData.numero_bobina;\n\n        console.log('Modifica bobina con ID:', bobinaId);\n        console.log('Dati da inviare:', formData);\n\n        try {\n          const response = await parcoCaviService.updateBobina(cantiereId, bobinaId, formData);\n          console.log('Risposta modifica bobina:', response);\n          onSuccess('Bobina modificata con successo');\n        } catch (error) {\n          console.error('Errore durante la modifica della bobina:', error);\n          onError(error.detail || 'Errore durante la modifica della bobina');\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'eliminaBobina') {\n        // Per l'eliminazione, usa l'ID bobina completo\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina;\n        const response = await parcoCaviService.deleteBobina(cantiereId, bobinaId);\n\n        // Verifica se è stata eliminata l'ultima bobina\n        if (response.is_last_bobina) {\n          console.log(`Eliminata l'ultima bobina del cantiere ${cantiereId}. Il parco cavi è ora vuoto.`);\n          onSuccess('Bobina eliminata con successo. Il parco cavi è ora vuoto.');\n\n          // Forza il refresh della pagina per reinnescare il sistema del primo inserimento\n          setTimeout(() => {\n            // Imposta l'opzione selezionata a creaBobina per forzare il dialog di configurazione\n            setSelectedOption('creaBobina');\n            // Forza l'apertura del dialog di configurazione\n            setIsFirstInsertion(true);\n            setOpenConfigDialog(true);\n          }, 1000);\n        } else {\n          onSuccess('Bobina eliminata con successo');\n        }\n      }\n\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le bobine in formato lista\n  const renderBobineCards = () => {\n    if (bobine.length === 0) {\n      return (\n        <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n      );\n    }\n\n    return (\n      <TableContainer component={Paper} sx={{ mt: 2 }}>\n        <Table size=\"small\">\n          <TableHead>\n            <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n              <TableCell><strong>ID Bobina</strong></TableCell>\n              <TableCell><strong>Utility</strong></TableCell>\n              <TableCell><strong>Tipologia</strong></TableCell>\n              <TableCell><strong>N° Cond.</strong></TableCell>\n              <TableCell><strong>Sezione</strong></TableCell>\n              <TableCell><strong>Metri Tot.</strong></TableCell>\n              <TableCell><strong>Metri Res.</strong></TableCell>\n              <TableCell><strong>Stato</strong></TableCell>\n              <TableCell><strong>Ubicazione</strong></TableCell>\n              <TableCell><strong>Azioni</strong></TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {bobine.map((bobina) => (\n              <TableRow key={bobina.numero_bobina} hover>\n                <TableCell>{bobina.numero_bobina}</TableCell>\n                <TableCell>{bobina.utility || 'N/A'}</TableCell>\n                <TableCell>{bobina.tipologia || 'N/A'}</TableCell>\n                <TableCell>{bobina.n_conduttori || 'N/A'}</TableCell>\n                <TableCell>{bobina.sezione || 'N/A'}</TableCell>\n                <TableCell>{bobina.metri_totali || 'N/A'} m</TableCell>\n                <TableCell>{bobina.metri_residui || 'N/A'} m</TableCell>\n                <TableCell>\n                  <Box\n                    component=\"span\"\n                    sx={{\n                      px: 1,\n                      py: 0.5,\n                      borderRadius: 1,\n                      bgcolor: bobina.stato_bobina === 'Disponibile' ? 'success.light' :\n                              bobina.stato_bobina === 'In Uso' ? 'warning.light' : 'error.light',\n                      color: 'white',\n                      fontSize: '0.75rem',\n                      fontWeight: 'bold'\n                    }}\n                  >\n                    {bobina.stato_bobina || 'DISPONIBILE'}\n                  </Box>\n                </TableCell>\n                <TableCell>{bobina.ubicazione_bobina || 'N/A'}</TableCell>\n                <TableCell>\n                  <Box sx={{ display: 'flex', gap: 1 }}>\n                    <IconButton\n                      size=\"small\"\n                      color=\"primary\"\n                      onClick={() => {\n                        setDialogType('selezionaBobina');\n                        handleBobinaSelect(bobina);\n                      }}\n                    >\n                      <EditIcon fontSize=\"small\" />\n                    </IconButton>\n                    <IconButton\n                      size=\"small\"\n                      color=\"error\"\n                      onClick={() => {\n                        setDialogType('eliminaBobina');\n                        setSelectedBobina(bobina);\n                        setOpenDialog(true);\n                      }}\n                    >\n                      <DeleteIcon fontSize=\"small\" />\n                    </IconButton>\n                  </Box>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n    );\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'}\n          </DialogTitle>\n          <DialogContent>\n            {dialogType === 'modificaBobina' && (\n              <Alert severity=\"info\" sx={{ mb: 2, mt: 1 }}>\n                <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                  Condizioni per la modifica:\n                </Typography>\n                <ul>\n                  <li>La bobina deve essere nello stato \"Disponibile\"</li>\n                  <li>La bobina non deve essere associata a nessun cavo</li>\n                  <li>Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente</li>\n                </ul>\n                <Box sx={{ mt: 1 }}>\n                  <Typography variant=\"body2\">\n                    <strong>Stato attuale:</strong> {selectedBobina?.stato_bobina || 'N/A'}\n                  </Typography>\n                </Box>\n              </Alert>\n            )}\n\n            {Object.keys(formWarnings).length > 0 && (\n              <Alert severity=\"warning\" sx={{ mb: 2, mt: 1 }}>\n                <Typography variant=\"subtitle2\">Attenzione:</Typography>\n                <ul style={{ margin: 0, paddingLeft: '20px' }}>\n                  {Object.values(formWarnings).map((warning, index) => (\n                    <li key={index}>{warning}</li>\n                  ))}\n                </ul>\n              </Alert>\n            )}\n\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"numero_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.numero_bobina}\n                  onChange={handleFormChange}\n                  disabled={dialogType === 'modificaBobina' || (dialogType === 'creaBobina' && formData.configurazione === 's')}\n                  required\n                  error={!!formErrors.numero_bobina}\n                  InputProps={{\n                    sx: {\n                      bgcolor: formData.configurazione === 's' ? '#f5f5f5' : 'transparent',\n                      fontWeight: 'bold',\n                    }\n                  }}\n                  helperText={\n                    formErrors.numero_bobina ||\n                    (dialogType === 'creaBobina' && formData.configurazione === 's'\n                      ? `ID completo: C${cantiereId}_B${formData.numero_bobina || ''} (generato automaticamente)`\n                      : dialogType === 'creaBobina' && formData.configurazione === 'n'\n                        ? `Inserisci l'ID della bobina (es. A123). ID completo sarà: C${cantiereId}_B{tuo input}`\n                        : '')\n                  }\n                  type={formData.configurazione === 's' ? \"text\" : \"text\"}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"N° Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.n_conduttori}\n                  helperText={formErrors.n_conduttori || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_totali\"\n                  label=\"Metri Totali\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_totali}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_totali}\n                  helperText={formErrors.metri_totali || ''}\n                />\n              </Grid>\n              {dialogType === 'modificaBobina' && (\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"metri_residui\"\n                    label=\"Metri Residui\"\n                    type=\"number\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.metri_residui}\n                    onChange={handleFormChange}\n                    required\n                    disabled={true}\n                    helperText=\"I metri residui non possono essere modificati direttamente\"\n                  />\n                </Grid>\n              )}\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel id=\"stato-bobina-label\">Stato Bobina</InputLabel>\n                  <Select\n                    labelId=\"stato-bobina-label\"\n                    name=\"stato_bobina\"\n                    value={formData.stato_bobina}\n                    label=\"Stato Bobina\"\n                    onChange={handleFormChange}\n                    disabled={dialogType === 'creaBobina'}\n                  >\n                    <MenuItem value=\"Disponibile\">Disponibile</MenuItem>\n                    <MenuItem value=\"In uso\">In uso</MenuItem>\n                    <MenuItem value=\"Terminata\">Terminata</MenuItem>\n                    <MenuItem value=\"Danneggiata\">Danneggiata</MenuItem>\n                    <MenuItem value=\"Over\">Over</MenuItem>\n                  </Select>\n                  {dialogType === 'creaBobina' && (\n                    <FormHelperText>Per una nuova bobina, lo stato è sempre \"Disponibile\"</FormHelperText>\n                  )}\n                </FormControl>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_bobina\"\n                  label=\"Ubicazione Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_bobina}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_bobina}\n                  helperText={formErrors.ubicazione_bobina || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"fornitore\"\n                  label=\"Fornitore\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.fornitore}\n                  onChange={handleFormChange}\n                  error={!!formErrors.fornitore}\n                  helperText={formErrors.fornitore || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_DDT\"\n                  label=\"Numero DDT\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_DDT}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_DDT}\n                  helperText={formErrors.n_DDT || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"data_DDT\"\n                  label=\"Data DDT (YYYY-MM-DD)\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.data_DDT}\n                  onChange={handleFormChange}\n                  placeholder=\"YYYY-MM-DD\"\n                  error={!!formErrors.data_DDT}\n                  helperText={formErrors.data_DDT || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"configurazione\"\n                  label=\"Modalità Numerazione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.configurazione === 's' ? 'Automatica' : 'Manuale'}\n                  InputProps={{\n                    readOnly: true,\n                    sx: {\n                      bgcolor: '#f5f5f5',\n                    }\n                  }}\n                  helperText={\n                    <Box sx={{ fontWeight: 'medium', color: formData.configurazione === 's' ? 'success.main' : 'info.main' }}>\n                      {formData.configurazione === 's'\n                        ? 'Numerazione progressiva automatica (1, 2, 3, ...)'\n                        : 'Inserimento manuale dell\\'ID bobina (es. A123, SPEC01, ...)'}\n                    </Box>\n                  }\n                />\n              </Grid>\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading || Object.keys(formErrors).length > 0}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              variant=\"contained\"\n              color=\"primary\"\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Bobina da Modificare</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : bobine.length === 0 ? (\n              <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n            ) : (\n              <List>\n                {bobine.map((bobina) => (\n                  <ListItem\n                    button\n                    key={bobina.numero_bobina}\n                    onClick={() => handleBobinaSelect(bobina)}\n                  >\n                    <ListItemText\n                      primary={`Bobina: ${bobina.numero_bobina}`}\n                      secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Elimina Bobina</DialogTitle>\n          <DialogContent>\n            {!selectedBobina ? (\n              loading ? (\n                <CircularProgress />\n              ) : bobine.length === 0 ? (\n                <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n              ) : (\n                <List>\n                  {bobine.map((bobina) => (\n                    <ListItem\n                      button\n                      key={bobina.numero_bobina}\n                      onClick={() => setSelectedBobina(bobina)}\n                    >\n                      <ListItemText\n                        primary={`Bobina: ${bobina.numero_bobina}`}\n                        secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              )\n            ) : (\n              <Box>\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  Sei sicuro di voler eliminare la bobina {selectedBobina.numero_bobina}?\n                </Alert>\n                <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                  Questa operazione non può essere annullata.\n                </Typography>\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                    Condizioni per l'eliminazione:\n                  </Typography>\n                  <ul>\n                    <li>La bobina deve essere completamente integra (metri residui = metri totali)</li>\n                    <li>La bobina deve essere nello stato \"Disponibile\"</li>\n                    <li>La bobina non deve essere associata a nessun cavo</li>\n                  </ul>\n                </Alert>\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 2 }}>\n                  <Typography variant=\"body2\">\n                    <strong>Stato attuale:</strong> {selectedBobina.stato_bobina || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\">\n                    <strong>Metri totali:</strong> {selectedBobina.metri_totali || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\">\n                    <strong>Metri residui:</strong> {selectedBobina.metri_residui || 'N/A'}\n                  </Typography>\n                </Box>\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedBobina && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color=\"error\"\n                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}\n              >\n                Elimina\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'visualizzaStorico') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"lg\" fullWidth>\n          <DialogTitle>Storico Utilizzo Bobine</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : storicoUtilizzo.length === 0 ? (\n              <Alert severity=\"info\">Nessun dato storico disponibile</Alert>\n            ) : (\n              <TableContainer component={Paper} sx={{ mt: 2 }}>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Bobina</TableCell>\n                      <TableCell>Utility</TableCell>\n                      <TableCell>Tipologia</TableCell>\n                      <TableCell>N° Conduttori</TableCell>\n                      <TableCell>Sezione</TableCell>\n                      <TableCell>Metri Totali</TableCell>\n                      <TableCell>Metri Residui</TableCell>\n                      <TableCell>Cavi Associati</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {storicoUtilizzo.map((record, index) => (\n                      <TableRow key={index}>\n                        <TableCell>{record.numero_bobina}</TableCell>\n                        <TableCell>{record.utility}</TableCell>\n                        <TableCell>{record.tipologia}</TableCell>\n                        <TableCell>{record.n_conduttori}</TableCell>\n                        <TableCell>{record.sezione}</TableCell>\n                        <TableCell>{record.metri_totali}</TableCell>\n                        <TableCell>{record.metri_residui}</TableCell>\n                        <TableCell>{record.cavi.length}</TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Chiudi</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box>\n      {selectedOption === 'visualizzaBobine' && !openDialog ? (\n        <Paper sx={{ p: 3 }}>\n          <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n            <Box sx={{ display: 'flex', gap: 2 }}>\n              <Button\n                variant=\"outlined\"\n                color=\"inherit\"\n                startIcon={<HistoryIcon />}\n                onClick={() => {\n                  loadStoricoUtilizzo();\n                  setDialogType('visualizzaStorico');\n                  setOpenDialog(true);\n                }}\n                sx={{\n                  fontWeight: 'medium',\n                  borderRadius: 0\n                }}\n              >\n                Storico Utilizzo\n              </Button>\n              <Button\n                variant=\"contained\"\n                color=\"inherit\"\n                startIcon={<AddIcon />}\n                onClick={() => checkIfFirstInsertion()}\n                sx={{\n                  fontWeight: 'medium',\n                  borderRadius: 0\n                }}\n              >\n                Crea Nuova Bobina\n              </Button>\n            </Box>\n          </Box>\n          {loading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            renderBobineCards()\n          )}\n        </Paper>\n      ) : !openDialog ? (\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption ? (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu principale per iniziare.\n            </Typography>\n          ) : (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'creaBobina' && 'Crea Nuova Bobina'}\n                {selectedOption === 'modificaBobina' && 'Modifica Bobina'}\n                {selectedOption === 'eliminaBobina' && 'Elimina Bobina'}\n                {selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo'}\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      ) : null}\n\n      {renderDialog()}\n\n      {/* Dialog di configurazione per il primo inserimento */}\n      <ConfigurazioneDialog\n        open={openConfigDialog}\n        onClose={() => setOpenConfigDialog(false)}\n        onConfirm={handleConfigConfirm}\n      />\n    </Box>\n  );\n};\n\nexport default ParcoCavi;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,cAAc,EACdC,UAAU,QACL,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,SAASC,kBAAkB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,OAAO,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvH,MAAMC,SAAS,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC9E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiE,MAAM,EAAEC,SAAS,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmE,cAAc,EAAEC,iBAAiB,CAAC,GAAGpE,QAAQ,CAAC6D,aAAa,CAAC;EACnE,MAAM,CAACQ,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuE,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyE,cAAc,EAAEC,iBAAiB,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC;IACvC6E,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,aAAa;IAC3BC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC4F,YAAY,EAAEC,eAAe,CAAC,GAAG7F,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC8F,eAAe,EAAEC,kBAAkB,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACgG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACkG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAMoG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFpC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqC,IAAI,GAAG,MAAMpD,gBAAgB,CAACqD,SAAS,CAAC5C,UAAU,CAAC;MACzDQ,SAAS,CAACmC,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd3C,OAAO,CAAC,qCAAqC,CAAC;MAC9C4C,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFzC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqC,IAAI,GAAG,MAAMpD,gBAAgB,CAACyD,kBAAkB,CAAChD,UAAU,CAAC;MAClEqC,kBAAkB,CAACM,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd3C,OAAO,CAAC,+CAA+C,CAAC;MACxD4C,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;IACxE,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA;EACA,MAAM2C,eAAe,GAAG5G,KAAK,CAAC6G,MAAM,CAAC,KAAK,CAAC;EAE3C3G,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAAC0G,eAAe,CAACE,OAAO,EAAE;MAC5BL,OAAO,CAACM,GAAG,CAAC,kDAAkD,EAAEjD,aAAa,CAAC;MAC9E8C,eAAe,CAACE,OAAO,GAAG,IAAI;MAE9B,IAAIhD,aAAa,KAAK,YAAY,EAAE;QAClC2C,OAAO,CAACM,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAN,OAAO,CAACM,GAAG,CAAC,oDAAoD,CAAC;QACjEX,mBAAmB,CAAC,IAAI,CAAC;QACzBF,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM,IAAIpC,aAAa,EAAE;QACxB2C,OAAO,CAACM,GAAG,CAAC,mCAAmC,EAAEjD,aAAa,CAAC;QAC/DkD,kBAAkB,CAAClD,aAAa,CAAC;MACnC,CAAC,MAAM;QACL2C,OAAO,CAACM,GAAG,CAAC,kBAAkB,CAAC;QAC/BV,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAE;;EAET;EACA,MAAMY,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC;IACA,IAAIvB,cAAc,GAAG,GAAG,CAAC,CAAC;;IAE1B,IAAI;MACF;MACA,IAAI1B,OAAO,EAAE;QACXyC,OAAO,CAACM,GAAG,CAAC,iCAAiC,CAAC;QAC9C;MACF;;MAEA;MACAxC,aAAa,CAAC,KAAK,CAAC;MACpB2B,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;;MAE5BjC,UAAU,CAAC,IAAI,CAAC;MAChBwC,OAAO,CAACM,GAAG,CAAC,wDAAwD,EAAEpD,UAAU,CAAC;;MAEjF;MACA,IAAI,CAACA,UAAU,IAAIuD,KAAK,CAACC,QAAQ,CAACxD,UAAU,CAAC,CAAC,EAAE;QAC9CE,OAAO,CAAC,wBAAwB,CAAC;QACjC4C,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAE7C,UAAU,CAAC;QACpDM,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAImD,OAAO,GAAG,KAAK;MAEnB,IAAI;QACFX,OAAO,CAACM,GAAG,CAAC,wDAAwD,EAAEpD,UAAU,CAAC;QACjF,MAAM0D,QAAQ,GAAG,MAAMnE,gBAAgB,CAACoE,sBAAsB,CAAC3D,UAAU,CAAC;QAC1EyD,OAAO,GAAGC,QAAQ,CAACE,kBAAkB;;QAErC;QACA,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY/D,UAAU,SAAS,CAAC;;QAEzE;QACA+B,cAAc,GAAG8B,WAAW,IAAIH,QAAQ,CAAC3B,cAAc,IAAI,GAAG;QAE9De,OAAO,CAACM,GAAG,CAAC,iCAAiC,EAAES,WAAW,CAAC;QAC3Df,OAAO,CAACM,GAAG,CAAC,4BAA4B,EAAEM,QAAQ,CAAC3B,cAAc,CAAC;QAClEe,OAAO,CAACM,GAAG,CAAC,mCAAmC,EAAErB,cAAc,CAAC;QAEhEU,mBAAmB,CAACgB,OAAO,CAAC;QAC5BX,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAEK,OAAO,CAAC;QAC7DX,OAAO,CAACM,GAAG,CAAC,2BAA2B,EAAErB,cAAc,CAAC;MAC1D,CAAC,CAAC,OAAOc,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;QACzE;QACAJ,mBAAmB,CAAC,KAAK,CAAC;QAC1BvC,OAAO,CAAC,wFAAwF,CAAC;MACnG;MAEA,IAAIuD,OAAO,EAAE;QACX;QACAX,OAAO,CAACM,GAAG,CAAC,uCAAuC,CAAC;QACpD;QACAxC,aAAa,CAAC,KAAK,CAAC;;QAEpB;QACAkC,OAAO,CAACM,GAAG,CAAC,yCAAyC,CAAC;QACtDb,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL;QACAO,OAAO,CAACM,GAAG,CAAC,uDAAuD,CAAC;;QAEpE;QACA,IAAIY,gBAAgB,GAAG,GAAG;QAC1B,IAAIjC,cAAc,KAAK,GAAG,EAAE;UAC1B,IAAI;YACF;YACA,MAAMxB,MAAM,GAAG,MAAMhB,gBAAgB,CAACqD,SAAS,CAAC5C,UAAU,CAAC;YAC3D,IAAIO,MAAM,IAAIA,MAAM,CAAC0D,MAAM,GAAG,CAAC,EAAE;cAC/B;cACA,MAAMC,aAAa,GAAG3D,MAAM,CAAC4D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjD,aAAa,IAAI,OAAO,CAACkD,IAAI,CAACD,CAAC,CAACjD,aAAa,CAAC,CAAC;;cAE1F;cACA2B,OAAO,CAACM,GAAG,CAAC,gBAAgB,EAAE7C,MAAM,CAAC0D,MAAM,CAAC;cAC5CnB,OAAO,CAACM,GAAG,CAAC,6BAA6B,EAAEc,aAAa,CAACD,MAAM,CAAC;cAChEnB,OAAO,CAACM,GAAG,CAAC,mBAAmB,EAAEc,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIA,CAAC,CAACjD,aAAa,CAAC,CAAC;cAEzE,IAAI+C,aAAa,CAACD,MAAM,GAAG,CAAC,EAAE;gBAC5B;gBACA,MAAMM,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGP,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIZ,QAAQ,CAACY,CAAC,CAACjD,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;gBACpF2B,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEmB,SAAS,CAAC;gBACjDP,gBAAgB,GAAGU,MAAM,CAACH,SAAS,GAAG,CAAC,CAAC;cAC1C;YACF;YACAzB,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEY,gBAAgB,CAAC;UAC1D,CAAC,CAAC,OAAOnB,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;YACvE;YACAmB,gBAAgB,GAAG,GAAG;UACxB;QACF;QAEAlD,aAAa,CAAC,YAAY,CAAC;QAC3BI,WAAW,CAAC;UACV;UACA;UACAC,aAAa,EAAEY,cAAc,KAAK,GAAG,GAAGiC,gBAAgB,GAAG,EAAE;UAC7D5C,OAAO,EAAE,EAAE;UACXC,SAAS,EAAE,EAAE;UACbC,YAAY,EAAE,EAAE;UAChBC,OAAO,EAAE,EAAE;UACXC,YAAY,EAAE,EAAE;UAChBC,aAAa,EAAE,EAAE;UACjBC,YAAY,EAAE,aAAa;UAC3BC,iBAAiB,EAAE,EAAE;UACrBC,SAAS,EAAE,EAAE;UACbC,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,cAAc,EAAEA,cAAc,CAAE;QAClC,CAAC,CAAC;QACFnB,aAAa,CAAC,IAAI,CAAC;MACrB;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACd;MACA,IAAI8B,YAAY,GAAG,2DAA2D;MAE9E,IAAI9B,KAAK,CAACa,QAAQ,EAAE;QAAA,IAAAkB,oBAAA;QAClB;QACAD,YAAY,IAAI,KAAK9B,KAAK,CAACa,QAAQ,CAACmB,MAAM,MAAM,EAAAD,oBAAA,GAAA/B,KAAK,CAACa,QAAQ,CAACf,IAAI,cAAAiC,oBAAA,uBAAnBA,oBAAA,CAAqBE,MAAM,KAAI,eAAe,EAAE;QAChGhC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAACa,QAAQ,CAAC;MACvD,CAAC,MAAM,IAAIb,KAAK,CAACkC,OAAO,EAAE;QACxB;QACAJ,YAAY,IAAI,mCAAmC;MACrD,CAAC,MAAM;QACL;QACAA,YAAY,IAAI,KAAK9B,KAAK,CAACmC,OAAO,IAAI,oBAAoB,EAAE;MAC9D;MAEA9E,OAAO,CAACyE,YAAY,CAAC;MACrB7B,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACA;MACA,IAAI,CAACd,cAAc,EAAE;QACnBA,cAAc,GAAG,GAAG,CAAC,CAAC;MACxB;MAEAjB,aAAa,CAAC,YAAY,CAAC;MAC3BI,WAAW,CAAC;QACVC,aAAa,EAAEY,cAAc,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE;QAChDX,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,aAAa;QAC3BC,iBAAiB,EAAE,EAAE;QACrBC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAEA,cAAc,CAAE;MAClC,CAAC,CAAC;MACFnB,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2E,mBAAmB,GAAG,MAAOC,WAAW,IAAK;IACjDpC,OAAO,CAACM,GAAG,CAAC,6BAA6B,EAAE8B,WAAW,CAAC;IACvD;IACApB,YAAY,CAACqB,OAAO,CAAC,YAAYnF,UAAU,SAAS,EAAEkF,WAAW,CAAC;IAClE5E,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,IAAI0D,gBAAgB,GAAG,GAAG;MAC1B,IAAIkB,WAAW,KAAK,GAAG,EAAE;QACvB,IAAI;UACF;UACA,MAAM3E,MAAM,GAAG,MAAMhB,gBAAgB,CAACqD,SAAS,CAAC5C,UAAU,CAAC;UAC3D,IAAIO,MAAM,IAAIA,MAAM,CAAC0D,MAAM,GAAG,CAAC,EAAE;YAC/B;YACA,MAAMC,aAAa,GAAG3D,MAAM,CAAC4D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjD,aAAa,IAAI,OAAO,CAACkD,IAAI,CAACD,CAAC,CAACjD,aAAa,CAAC,CAAC;;YAE1F;YACA2B,OAAO,CAACM,GAAG,CAAC,gBAAgB,EAAE7C,MAAM,CAAC0D,MAAM,CAAC;YAC5CnB,OAAO,CAACM,GAAG,CAAC,6BAA6B,EAAEc,aAAa,CAACD,MAAM,CAAC;YAChEnB,OAAO,CAACM,GAAG,CAAC,mBAAmB,EAAEc,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIA,CAAC,CAACjD,aAAa,CAAC,CAAC;YAEzE,IAAI+C,aAAa,CAACD,MAAM,GAAG,CAAC,EAAE;cAC5B;cACA,MAAMM,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGP,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIZ,QAAQ,CAACY,CAAC,CAACjD,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;cACpF2B,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEmB,SAAS,CAAC;cACjDP,gBAAgB,GAAGU,MAAM,CAACH,SAAS,GAAG,CAAC,CAAC;YAC1C;UACF;UACAzB,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEY,gBAAgB,CAAC;QAC1D,CAAC,CAAC,OAAOnB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvE;UACAmB,gBAAgB,GAAG,GAAG;QACxB;MACF;;MAEA;MACA,MAAMoB,eAAe,GAAG;QACtB;QACA;QACAjE,aAAa,EAAE+D,WAAW,KAAK,GAAG,GAAGlB,gBAAgB,GAAG,EAAE;QAC1D5C,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,aAAa;QAC3BC,iBAAiB,EAAE,EAAE;QACrBC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAEmD,WAAW,CAAC;MAC9B,CAAC;MAEDpC,OAAO,CAACM,GAAG,CAAC,gDAAgD,EAAE8B,WAAW,EAAE,gBAAgB,EAAEE,eAAe,CAACjE,aAAa,CAAC;;MAE3H;MACA;MACAD,WAAW,CAACkE,eAAe,CAAC;MAC5BtE,aAAa,CAAC,YAAY,CAAC;;MAE3B;MACAyB,mBAAmB,CAAC,KAAK,CAAC;;MAE1B;MACA;MACA8C,UAAU,CAAC,MAAM;QACfzE,aAAa,CAAC,IAAI,CAAC;QACnBkC,OAAO,CAACM,GAAG,CAAC,sDAAsD,EAAEgC,eAAe,CAACjE,aAAa,CAAC;MACpG,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE3C,OAAO,CAAC,2CAA2C,IAAI2C,KAAK,CAACmC,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAChG,CAAC,SAAS;MACR1E,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+C,kBAAkB,GAAIiC,MAAM,IAAK;IACrC5E,iBAAiB,CAAC4E,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,kBAAkB,EAAE;MACjC5C,UAAU,CAAC,CAAC;IACd,CAAC,MAAM,IAAI4C,MAAM,KAAK,YAAY,EAAE;MAClChC,qBAAqB,CAAC,CAAC;IACzB,CAAC,MAAM,IAAIgC,MAAM,KAAK,gBAAgB,EAAE;MACtC5C,UAAU,CAAC,CAAC;MACZ5B,aAAa,CAAC,iBAAiB,CAAC;MAChCF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI0E,MAAM,KAAK,eAAe,EAAE;MACrC5C,UAAU,CAAC,CAAC;MACZ5B,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI0E,MAAM,KAAK,mBAAmB,EAAE;MACzCvC,mBAAmB,CAAC,CAAC;MACrBjC,aAAa,CAAC,mBAAmB,CAAC;MAClCF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM2E,iBAAiB,GAAGA,CAAA,KAAM;IAC9B3E,aAAa,CAAC,KAAK,CAAC;IACpBI,iBAAiB,CAAC,IAAI,CAAC;;IAEvB;IACA,MAAM6C,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY/D,UAAU,SAAS,CAAC,IAAI,GAAG;IAEhFkB,WAAW,CAAC;MACVC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,aAAa;MAC3BC,iBAAiB,EAAE,EAAE;MACrBC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE8B,WAAW,CAAC;IAC9B,CAAC,CAAC;IACF5B,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMqD,kBAAkB,GAAIC,MAAM,IAAK;IACrC3C,OAAO,CAACM,GAAG,CAAC,qBAAqB,EAAEqC,MAAM,CAAC;IAC1CzE,iBAAiB,CAACyE,MAAM,CAAC;IACzB,IAAI5E,UAAU,KAAK,iBAAiB,EAAE;MACpCC,aAAa,CAAC,gBAAgB,CAAC;;MAE/B;MACAI,WAAW,CAAC;QACVC,aAAa,EAAEsE,MAAM,CAACtE,aAAa,IAAI,EAAE;QACzCC,OAAO,EAAEsD,MAAM,CAACe,MAAM,CAACrE,OAAO,IAAI,EAAE,CAAC;QACrCC,SAAS,EAAEqD,MAAM,CAACe,MAAM,CAACpE,SAAS,IAAI,EAAE,CAAC;QACzCC,YAAY,EAAEmE,MAAM,CAACnE,YAAY,KAAK,IAAI,IAAImE,MAAM,CAACnE,YAAY,KAAKoE,SAAS,GAAGC,MAAM,CAACF,MAAM,CAACnE,YAAY,CAAC,GAAG,EAAE;QAClHC,OAAO,EAAEkE,MAAM,CAAClE,OAAO,KAAK,IAAI,IAAIkE,MAAM,CAAClE,OAAO,KAAKmE,SAAS,GAAGC,MAAM,CAACF,MAAM,CAAClE,OAAO,CAAC,GAAG,EAAE;QAC9FC,YAAY,EAAEiE,MAAM,CAACjE,YAAY,KAAK,IAAI,IAAIiE,MAAM,CAACjE,YAAY,KAAKkE,SAAS,GAAGC,MAAM,CAACF,MAAM,CAACjE,YAAY,CAAC,GAAG,EAAE;QAClHC,aAAa,EAAEgE,MAAM,CAAChE,aAAa,KAAK,IAAI,IAAIgE,MAAM,CAAChE,aAAa,KAAKiE,SAAS,GAAGC,MAAM,CAACF,MAAM,CAAChE,aAAa,CAAC,GAAG,EAAE;QACtHC,YAAY,EAAEgD,MAAM,CAACe,MAAM,CAAC/D,YAAY,IAAI,aAAa,CAAC;QAC1DC,iBAAiB,EAAE+C,MAAM,CAACe,MAAM,CAAC9D,iBAAiB,IAAI,EAAE,CAAC;QACzDC,SAAS,EAAE8C,MAAM,CAACe,MAAM,CAAC7D,SAAS,IAAI,EAAE,CAAC;QACzCC,KAAK,EAAE6C,MAAM,CAACe,MAAM,CAAC5D,KAAK,IAAI,EAAE,CAAC;QACjCC,QAAQ,EAAE2D,MAAM,CAAC3D,QAAQ,IAAI,EAAE;QAC/BC,cAAc,EAAE2C,MAAM,CAACe,MAAM,CAAC1D,cAAc,IAAI,GAAG;MACrD,CAAC,CAAC;MAEFe,OAAO,CAACM,GAAG,CAAC,sCAAsC,EAAE;QAClDjC,aAAa,EAAEsE,MAAM,CAACtE,aAAa;QACnCC,OAAO,EAAEqE,MAAM,CAACrE,OAAO;QACvBC,SAAS,EAAEoE,MAAM,CAACpE,SAAS;QAC3BC,YAAY,EAAEmE,MAAM,CAACnE,YAAY;QACjCC,OAAO,EAAEkE,MAAM,CAAClE,OAAO;QACvBC,YAAY,EAAEiE,MAAM,CAACjE,YAAY;QACjCC,aAAa,EAAEgE,MAAM,CAAChE;MACxB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMmE,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA9E,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC6E,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,IAAIlF,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;MAClE;MACA,IAAIiF,IAAI,KAAK,eAAe,IAAI7E,QAAQ,CAACc,cAAc,KAAK,GAAG,EAAE;QAC/D,MAAMkE,QAAQ,GAAGtG,gBAAgB,CAACoG,KAAK,CAAC;QACxC,IAAI,CAACE,QAAQ,CAACC,KAAK,EAAE;UACnBjE,aAAa,CAACkE,IAAI,KAAK;YACrB,GAAGA,IAAI;YACP,CAACL,IAAI,GAAGG,QAAQ,CAACjB;UACnB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACL/C,aAAa,CAACkE,IAAI,IAAI;YACpB,MAAMC,SAAS,GAAG;cAAE,GAAGD;YAAK,CAAC;YAC7B,OAAOC,SAAS,CAACN,IAAI,CAAC;YACtB,OAAOM,SAAS;UAClB,CAAC,CAAC;QACJ;QACA;MACF;MAEA,MAAMC,MAAM,GAAG3G,mBAAmB,CAACoG,IAAI,EAAEC,KAAK,CAAC;;MAE/C;MACA,IAAI,CAACM,MAAM,CAACH,KAAK,EAAE;QACjBjE,aAAa,CAACkE,IAAI,KAAK;UACrB,GAAGA,IAAI;UACP,CAACL,IAAI,GAAGO,MAAM,CAACrB;QACjB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL/C,aAAa,CAACkE,IAAI,IAAI;UACpB,MAAMC,SAAS,GAAG;YAAE,GAAGD;UAAK,CAAC;UAC7B,OAAOC,SAAS,CAACN,IAAI,CAAC;UACtB,OAAOM,SAAS;QAClB,CAAC,CAAC;;QAEF;QACA,IAAIC,MAAM,CAACC,OAAO,EAAE;UAClBnE,eAAe,CAACgE,IAAI,KAAK;YACvB,GAAGA,IAAI;YACP,CAACL,IAAI,GAAGO,MAAM,CAACrB;UACjB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACL7C,eAAe,CAACgE,IAAI,IAAI;YACtB,MAAMI,WAAW,GAAG;cAAE,GAAGJ;YAAK,CAAC;YAC/B,OAAOI,WAAW,CAACT,IAAI,CAAC;YACxB,OAAOS,WAAW;UACpB,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,IAAI3F,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;QAClE,MAAM4F,UAAU,GAAGhH,kBAAkB,CAACwB,QAAQ,CAAC;QAE/C,IAAI,CAACwF,UAAU,CAACC,OAAO,EAAE;UACvBzE,aAAa,CAACwE,UAAU,CAACE,MAAM,CAAC;UAChCxE,eAAe,CAACsE,UAAU,CAACG,QAAQ,CAAC;UACpC1G,OAAO,CAAC,+CAA+C,CAAC;UACxD;QACF;;QAEA;QACA,IAAIe,QAAQ,CAACc,cAAc,KAAK,GAAG,KAAK,CAACd,QAAQ,CAACE,aAAa,IAAIF,QAAQ,CAACE,aAAa,CAAC0F,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE;UACxG5E,aAAa,CAAC;YACZ,GAAGD,UAAU;YACbb,aAAa,EAAE;UACjB,CAAC,CAAC;UACFjB,OAAO,CAAC,mCAAmC,CAAC;UAC5C;QACF;MACF;MAEAI,UAAU,CAAC,IAAI,CAAC;MAChBwC,OAAO,CAACM,GAAG,CAAC,qCAAqC,CAAC;MAElD,IAAIvC,UAAU,KAAK,YAAY,EAAE;QAC/B;QACA,MAAMiG,UAAU,GAAG;UACjB,GAAG7F,QAAQ;UACX;UACAE,aAAa,EAAEuD,MAAM,CAACzD,QAAQ,CAACE,aAAa,IAAI,EAAE,CAAC;UACnDC,OAAO,EAAEsD,MAAM,CAACzD,QAAQ,CAACG,OAAO,IAAI,EAAE,CAAC;UACvCC,SAAS,EAAEqD,MAAM,CAACzD,QAAQ,CAACI,SAAS,IAAI,EAAE,CAAC;UAC3CC,YAAY,EAAEoD,MAAM,CAACzD,QAAQ,CAACK,YAAY,IAAI,EAAE,CAAC;UACjDC,OAAO,EAAEmD,MAAM,CAACzD,QAAQ,CAACM,OAAO,IAAI,EAAE,CAAC;UACvCC,YAAY,EAAEuF,UAAU,CAAC9F,QAAQ,CAACO,YAAY,CAAC,IAAI,CAAC;UACpDG,iBAAiB,EAAE+C,MAAM,CAACzD,QAAQ,CAACU,iBAAiB,IAAI,KAAK,CAAC;UAC9DC,SAAS,EAAE8C,MAAM,CAACzD,QAAQ,CAACW,SAAS,IAAI,KAAK,CAAC;UAC9CC,KAAK,EAAE6C,MAAM,CAACzD,QAAQ,CAACY,KAAK,IAAI,KAAK,CAAC;UACtCC,QAAQ,EAAEb,QAAQ,CAACa,QAAQ,IAAI,IAAI;UACnC;UACAC,cAAc,EAAE2C,MAAM,CAACzD,QAAQ,CAACc,cAAc,IAAI,GAAG;QACvD,CAAC;QAEDe,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAE0D,UAAU,CAAC;QAElD,IAAI;UACF;UACAhE,OAAO,CAACM,GAAG,CAAC,+BAA+B,EAAE4D,IAAI,CAACC,SAAS,CAACH,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;UAEjF;UACA,MAAMvH,gBAAgB,CAAC2H,YAAY,CAAClH,UAAU,EAAE8G,UAAU,CAAC;UAC3D7G,SAAS,CAAC,4BAA4B,CAAC;;UAEvC;UACAsF,iBAAiB,CAAC,CAAC;;UAEnB;UACA7E,iBAAiB,CAAC,kBAAkB,CAAC;UACrCgC,UAAU,CAAC,CAAC;;UAEZ;UACA,IAAI,OAAOyE,MAAM,KAAK,WAAW,EAAE;YACjC;YACA,MAAMC,KAAK,GAAG,IAAIC,WAAW,CAAC,4BAA4B,EAAE;cAAEvC,MAAM,EAAE;gBAAE9E;cAAW;YAAE,CAAC,CAAC;YACvFmH,MAAM,CAACG,aAAa,CAACF,KAAK,CAAC;UAC7B;QACF,CAAC,CAAC,OAAOvE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;;UAEjE;UACA,IAAI8B,YAAY,GAAG,0CAA0C;UAE7D,IAAI9B,KAAK,CAACiC,MAAM,EAAE;YAChBH,YAAY,GAAG9B,KAAK,CAACiC,MAAM;UAC7B,CAAC,MAAM,IAAIjC,KAAK,CAACmC,OAAO,EAAE;YACxBL,YAAY,GAAG9B,KAAK,CAACmC,OAAO;UAC9B;;UAEA;UACAlC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAE8B,YAAY,CAAC;UAC/C7B,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEmE,IAAI,CAACC,SAAS,CAACH,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEnE5G,OAAO,CAACyE,YAAY,CAAC;UACrBrE,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,MAAM,IAAIO,UAAU,KAAK,gBAAgB,EAAE;QAC1C;QACA,MAAM0G,QAAQ,GAAGxG,cAAc,CAACyG,SAAS,IAAIzG,cAAc,CAACI,aAAa,IAAIF,QAAQ,CAACE,aAAa;QAEnG2B,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEmE,QAAQ,CAAC;QAChDzE,OAAO,CAACM,GAAG,CAAC,kBAAkB,EAAEnC,QAAQ,CAAC;QAEzC,IAAI;UACF,MAAMyC,QAAQ,GAAG,MAAMnE,gBAAgB,CAACkI,YAAY,CAACzH,UAAU,EAAEuH,QAAQ,EAAEtG,QAAQ,CAAC;UACpF6B,OAAO,CAACM,GAAG,CAAC,2BAA2B,EAAEM,QAAQ,CAAC;UAClDzD,SAAS,CAAC,gCAAgC,CAAC;QAC7C,CAAC,CAAC,OAAO4C,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;UAChE3C,OAAO,CAAC2C,KAAK,CAACiC,MAAM,IAAI,yCAAyC,CAAC;UAClExE,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,MAAM,IAAIO,UAAU,KAAK,eAAe,EAAE;QACzC;QACA,MAAM0G,QAAQ,GAAGxG,cAAc,CAACyG,SAAS,IAAIzG,cAAc,CAACI,aAAa;QACzE,MAAMuC,QAAQ,GAAG,MAAMnE,gBAAgB,CAACmI,YAAY,CAAC1H,UAAU,EAAEuH,QAAQ,CAAC;;QAE1E;QACA,IAAI7D,QAAQ,CAACiE,cAAc,EAAE;UAC3B7E,OAAO,CAACM,GAAG,CAAC,0CAA0CpD,UAAU,8BAA8B,CAAC;UAC/FC,SAAS,CAAC,2DAA2D,CAAC;;UAEtE;UACAoF,UAAU,CAAC,MAAM;YACf;YACA3E,iBAAiB,CAAC,YAAY,CAAC;YAC/B;YACA+B,mBAAmB,CAAC,IAAI,CAAC;YACzBF,mBAAmB,CAAC,IAAI,CAAC;UAC3B,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACLtC,SAAS,CAAC,+BAA+B,CAAC;QAC5C;MACF;MAEAsF,iBAAiB,CAAC,CAAC;MACnB7C,UAAU,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd3C,OAAO,CAAC,gCAAgC,IAAI2C,KAAK,CAACiC,MAAM,IAAIjC,KAAK,CAACmC,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACnGlC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsH,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIrH,MAAM,CAAC0D,MAAM,KAAK,CAAC,EAAE;MACvB,oBACEnE,OAAA,CAAC/B,KAAK;QAAC8J,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAE7D;IAEA,oBACEpI,OAAA,CAAC1B,cAAc;MAAC+J,SAAS,EAAExL,KAAM;MAACyL,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,eAC9ChI,OAAA,CAAC7B,KAAK;QAACqK,IAAI,EAAC,OAAO;QAAAR,QAAA,gBACjBhI,OAAA,CAACzB,SAAS;UAAAyJ,QAAA,eACRhI,OAAA,CAACxB,QAAQ;YAAC8J,EAAE,EAAE;cAAEG,OAAO,EAAE;YAAU,CAAE;YAAAT,QAAA,gBACnChI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,eAAChI,OAAA;gBAAAgI,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACjDpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,eAAChI,OAAA;gBAAAgI,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/CpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,eAAChI,OAAA;gBAAAgI,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACjDpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,eAAChI,OAAA;gBAAAgI,QAAA,EAAQ;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChDpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,eAAChI,OAAA;gBAAAgI,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/CpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,eAAChI,OAAA;gBAAAgI,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClDpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,eAAChI,OAAA;gBAAAgI,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClDpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,eAAChI,OAAA;gBAAAgI,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7CpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,eAAChI,OAAA;gBAAAgI,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClDpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,eAAChI,OAAA;gBAAAgI,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZpI,OAAA,CAAC5B,SAAS;UAAA4J,QAAA,EACPvH,MAAM,CAAC+D,GAAG,CAAEmB,MAAM,iBACjB3F,OAAA,CAACxB,QAAQ;YAA4BkK,KAAK;YAAAV,QAAA,gBACxChI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,EAAErC,MAAM,CAACtE;YAAa;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7CpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,EAAErC,MAAM,CAACrE,OAAO,IAAI;YAAK;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChDpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,EAAErC,MAAM,CAACpE,SAAS,IAAI;YAAK;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClDpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,EAAErC,MAAM,CAACnE,YAAY,IAAI;YAAK;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrDpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,EAAErC,MAAM,CAAClE,OAAO,IAAI;YAAK;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChDpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,GAAErC,MAAM,CAACjE,YAAY,IAAI,KAAK,EAAC,IAAE;YAAA;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACvDpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,GAAErC,MAAM,CAAChE,aAAa,IAAI,KAAK,EAAC,IAAE;YAAA;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACxDpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,eACRhI,OAAA,CAACtD,GAAG;gBACF2L,SAAS,EAAC,MAAM;gBAChBC,EAAE,EAAE;kBACFK,EAAE,EAAE,CAAC;kBACLC,EAAE,EAAE,GAAG;kBACPC,YAAY,EAAE,CAAC;kBACfJ,OAAO,EAAE9C,MAAM,CAAC/D,YAAY,KAAK,aAAa,GAAG,eAAe,GACxD+D,MAAM,CAAC/D,YAAY,KAAK,QAAQ,GAAG,eAAe,GAAG,aAAa;kBAC1EkH,KAAK,EAAE,OAAO;kBACdC,QAAQ,EAAE,SAAS;kBACnBC,UAAU,EAAE;gBACd,CAAE;gBAAAhB,QAAA,EAEDrC,MAAM,CAAC/D,YAAY,IAAI;cAAa;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACZpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,EAAErC,MAAM,CAAC9D,iBAAiB,IAAI;YAAK;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1DpI,OAAA,CAAC3B,SAAS;cAAA2J,QAAA,eACRhI,OAAA,CAACtD,GAAG;gBAAC4L,EAAE,EAAE;kBAAEW,OAAO,EAAE,MAAM;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAAlB,QAAA,gBACnChI,OAAA,CAACtB,UAAU;kBACT8J,IAAI,EAAC,OAAO;kBACZM,KAAK,EAAC,SAAS;kBACfK,OAAO,EAAEA,CAAA,KAAM;oBACbnI,aAAa,CAAC,iBAAiB,CAAC;oBAChC0E,kBAAkB,CAACC,MAAM,CAAC;kBAC5B,CAAE;kBAAAqC,QAAA,eAEFhI,OAAA,CAAClB,QAAQ;oBAACiK,QAAQ,EAAC;kBAAO;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACbpI,OAAA,CAACtB,UAAU;kBACT8J,IAAI,EAAC,OAAO;kBACZM,KAAK,EAAC,OAAO;kBACbK,OAAO,EAAEA,CAAA,KAAM;oBACbnI,aAAa,CAAC,eAAe,CAAC;oBAC9BE,iBAAiB,CAACyE,MAAM,CAAC;oBACzB7E,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBAAAkH,QAAA,eAEFhI,OAAA,CAAChB,UAAU;oBAAC+J,QAAQ,EAAC;kBAAO;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA,GAlDCzC,MAAM,CAACtE,aAAa;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmDzB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAErB,CAAC;;EAED;EACA,MAAMgB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIrI,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;MAClE,oBACEf,OAAA,CAAC9C,MAAM;QAACmM,IAAI,EAAExI,UAAW;QAACyI,OAAO,EAAE7D,iBAAkB;QAAC8D,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAxB,QAAA,gBAC3EhI,OAAA,CAAC7C,WAAW;UAAA6K,QAAA,EACTjH,UAAU,KAAK,YAAY,GAAG,mBAAmB,GAAG;QAAiB;UAAAkH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACdpI,OAAA,CAAC5C,aAAa;UAAA4K,QAAA,GACXjH,UAAU,KAAK,gBAAgB,iBAC9Bf,OAAA,CAAC/B,KAAK;YAAC8J,QAAQ,EAAC,MAAM;YAACO,EAAE,EAAE;cAAEmB,EAAE,EAAE,CAAC;cAAElB,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBAC1ChI,OAAA,CAACrD,UAAU;cAAC+M,OAAO,EAAC,WAAW;cAACV,UAAU,EAAC,MAAM;cAAAhB,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpI,OAAA;cAAAgI,QAAA,gBACEhI,OAAA;gBAAAgI,QAAA,EAAI;cAA+C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDpI,OAAA;gBAAAgI,QAAA,EAAI;cAAiD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1DpI,OAAA;gBAAAgI,QAAA,EAAI;cAAgF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eACLpI,OAAA,CAACtD,GAAG;cAAC4L,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,eACjBhI,OAAA,CAACrD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAA1B,QAAA,gBACzBhI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,CAAAnH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEW,YAAY,KAAI,KAAK;cAAA;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EAEAuB,MAAM,CAACC,IAAI,CAACxH,YAAY,CAAC,CAAC+B,MAAM,GAAG,CAAC,iBACnCnE,OAAA,CAAC/B,KAAK;YAAC8J,QAAQ,EAAC,SAAS;YAACO,EAAE,EAAE;cAAEmB,EAAE,EAAE,CAAC;cAAElB,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBAC7ChI,OAAA,CAACrD,UAAU;cAAC+M,OAAO,EAAC,WAAW;cAAA1B,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxDpI,OAAA;cAAI6J,KAAK,EAAE;gBAAEC,MAAM,EAAE,CAAC;gBAAEC,WAAW,EAAE;cAAO,CAAE;cAAA/B,QAAA,EAC3C2B,MAAM,CAACK,MAAM,CAAC5H,YAAY,CAAC,CAACoC,GAAG,CAAC,CAACgC,OAAO,EAAEyD,KAAK,kBAC9CjK,OAAA;gBAAAgI,QAAA,EAAiBxB;cAAO,GAAfyD,KAAK;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACR,eAEDpI,OAAA,CAAClD,IAAI;YAACoN,SAAS;YAACC,OAAO,EAAE,CAAE;YAAC7B,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBACxChI,OAAA,CAAClD,IAAI;cAACsN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtC,QAAA,eACvBhI,OAAA,CAAC1C,SAAS;gBACR0I,IAAI,EAAC,eAAe;gBACpBuE,KAAK,EAAC,WAAW;gBACjBf,SAAS;gBACTE,OAAO,EAAC,UAAU;gBAClBzD,KAAK,EAAE9E,QAAQ,CAACE,aAAc;gBAC9BmJ,QAAQ,EAAE1E,gBAAiB;gBAC3B2E,QAAQ,EAAE1J,UAAU,KAAK,gBAAgB,IAAKA,UAAU,KAAK,YAAY,IAAII,QAAQ,CAACc,cAAc,KAAK,GAAK;gBAC9GyI,QAAQ;gBACR3H,KAAK,EAAE,CAAC,CAACb,UAAU,CAACb,aAAc;gBAClCsJ,UAAU,EAAE;kBACVrC,EAAE,EAAE;oBACFG,OAAO,EAAEtH,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,SAAS,GAAG,aAAa;oBACpE+G,UAAU,EAAE;kBACd;gBACF,CAAE;gBACF4B,UAAU,EACR1I,UAAU,CAACb,aAAa,KACvBN,UAAU,KAAK,YAAY,IAAII,QAAQ,CAACc,cAAc,KAAK,GAAG,GAC3D,iBAAiB/B,UAAU,KAAKiB,QAAQ,CAACE,aAAa,IAAI,EAAE,6BAA6B,GACzFN,UAAU,KAAK,YAAY,IAAII,QAAQ,CAACc,cAAc,KAAK,GAAG,GAC5D,8DAA8D/B,UAAU,eAAe,GACvF,EAAE,CACT;gBACD2K,IAAI,EAAE1J,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,MAAM,GAAG;cAAO;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPpI,OAAA,CAAClD,IAAI;cAACsN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtC,QAAA,eACvBhI,OAAA,CAAC1C,SAAS;gBACR0I,IAAI,EAAC,SAAS;gBACduE,KAAK,EAAC,SAAS;gBACff,SAAS;gBACTE,OAAO,EAAC,UAAU;gBAClBzD,KAAK,EAAE9E,QAAQ,CAACG,OAAQ;gBACxBkJ,QAAQ,EAAE1E,gBAAiB;gBAC3B4E,QAAQ;gBACR3H,KAAK,EAAE,CAAC,CAACb,UAAU,CAACZ,OAAQ;gBAC5BsJ,UAAU,EAAE1I,UAAU,CAACZ,OAAO,IAAI;cAAG;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPpI,OAAA,CAAClD,IAAI;cAACsN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtC,QAAA,eACvBhI,OAAA,CAAC1C,SAAS;gBACR0I,IAAI,EAAC,WAAW;gBAChBuE,KAAK,EAAC,WAAW;gBACjBf,SAAS;gBACTE,OAAO,EAAC,UAAU;gBAClBzD,KAAK,EAAE9E,QAAQ,CAACI,SAAU;gBAC1BiJ,QAAQ,EAAE1E,gBAAiB;gBAC3B4E,QAAQ;gBACR3H,KAAK,EAAE,CAAC,CAACb,UAAU,CAACX,SAAU;gBAC9BqJ,UAAU,EAAE1I,UAAU,CAACX,SAAS,IAAI;cAAG;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPpI,OAAA,CAAClD,IAAI;cAACsN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtC,QAAA,eACvBhI,OAAA,CAAC1C,SAAS;gBACR0I,IAAI,EAAC,cAAc;gBACnBuE,KAAK,EAAC,kBAAe;gBACrBf,SAAS;gBACTE,OAAO,EAAC,UAAU;gBAClBzD,KAAK,EAAE9E,QAAQ,CAACK,YAAa;gBAC7BgJ,QAAQ,EAAE1E,gBAAiB;gBAC3B4E,QAAQ;gBACR3H,KAAK,EAAE,CAAC,CAACb,UAAU,CAACV,YAAa;gBACjCoJ,UAAU,EAAE1I,UAAU,CAACV,YAAY,IAAI;cAAG;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPpI,OAAA,CAAClD,IAAI;cAACsN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtC,QAAA,eACvBhI,OAAA,CAAC1C,SAAS;gBACR0I,IAAI,EAAC,SAAS;gBACduE,KAAK,EAAC,SAAS;gBACff,SAAS;gBACTE,OAAO,EAAC,UAAU;gBAClBzD,KAAK,EAAE9E,QAAQ,CAACM,OAAQ;gBACxB+I,QAAQ,EAAE1E,gBAAiB;gBAC3B4E,QAAQ;gBACR3H,KAAK,EAAE,CAAC,CAACb,UAAU,CAACT,OAAQ;gBAC5BmJ,UAAU,EAAE1I,UAAU,CAACT,OAAO,IAAI;cAAG;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPpI,OAAA,CAAClD,IAAI;cAACsN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtC,QAAA,eACvBhI,OAAA,CAAC1C,SAAS;gBACR0I,IAAI,EAAC,cAAc;gBACnBuE,KAAK,EAAC,cAAc;gBACpBM,IAAI,EAAC,QAAQ;gBACbrB,SAAS;gBACTE,OAAO,EAAC,UAAU;gBAClBzD,KAAK,EAAE9E,QAAQ,CAACO,YAAa;gBAC7B8I,QAAQ,EAAE1E,gBAAiB;gBAC3B4E,QAAQ;gBACR3H,KAAK,EAAE,CAAC,CAACb,UAAU,CAACR,YAAa;gBACjCkJ,UAAU,EAAE1I,UAAU,CAACR,YAAY,IAAI;cAAG;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACNrH,UAAU,KAAK,gBAAgB,iBAC9Bf,OAAA,CAAClD,IAAI;cAACsN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtC,QAAA,eACvBhI,OAAA,CAAC1C,SAAS;gBACR0I,IAAI,EAAC,eAAe;gBACpBuE,KAAK,EAAC,eAAe;gBACrBM,IAAI,EAAC,QAAQ;gBACbrB,SAAS;gBACTE,OAAO,EAAC,UAAU;gBAClBzD,KAAK,EAAE9E,QAAQ,CAACQ,aAAc;gBAC9B6I,QAAQ,EAAE1E,gBAAiB;gBAC3B4E,QAAQ;gBACRD,QAAQ,EAAE,IAAK;gBACfG,UAAU,EAAC;cAA4D;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACP,eACDpI,OAAA,CAAClD,IAAI;cAACsN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtC,QAAA,eACvBhI,OAAA,CAACzC,WAAW;gBAACiM,SAAS;gBAAAxB,QAAA,gBACpBhI,OAAA,CAACxC,UAAU;kBAACsN,EAAE,EAAC,oBAAoB;kBAAA9C,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7DpI,OAAA,CAACvC,MAAM;kBACLsN,OAAO,EAAC,oBAAoB;kBAC5B/E,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAE9E,QAAQ,CAACS,YAAa;kBAC7B2I,KAAK,EAAC,cAAc;kBACpBC,QAAQ,EAAE1E,gBAAiB;kBAC3B2E,QAAQ,EAAE1J,UAAU,KAAK,YAAa;kBAAAiH,QAAA,gBAEtChI,OAAA,CAACtC,QAAQ;oBAACuI,KAAK,EAAC,aAAa;oBAAA+B,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpDpI,OAAA,CAACtC,QAAQ;oBAACuI,KAAK,EAAC,QAAQ;oBAAA+B,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1CpI,OAAA,CAACtC,QAAQ;oBAACuI,KAAK,EAAC,WAAW;oBAAA+B,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChDpI,OAAA,CAACtC,QAAQ;oBAACuI,KAAK,EAAC,aAAa;oBAAA+B,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpDpI,OAAA,CAACtC,QAAQ;oBAACuI,KAAK,EAAC,MAAM;oBAAA+B,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,EACRrH,UAAU,KAAK,YAAY,iBAC1Bf,OAAA,CAACvB,cAAc;kBAAAuJ,QAAA,EAAC;gBAAqD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgB,CACtF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACPpI,OAAA,CAAClD,IAAI;cAACsN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtC,QAAA,eACvBhI,OAAA,CAAC1C,SAAS;gBACR0I,IAAI,EAAC,mBAAmB;gBACxBuE,KAAK,EAAC,mBAAmB;gBACzBf,SAAS;gBACTE,OAAO,EAAC,UAAU;gBAClBzD,KAAK,EAAE9E,QAAQ,CAACU,iBAAkB;gBAClC2I,QAAQ,EAAE1E,gBAAiB;gBAC3B/C,KAAK,EAAE,CAAC,CAACb,UAAU,CAACL,iBAAkB;gBACtC+I,UAAU,EAAE1I,UAAU,CAACL,iBAAiB,IAAI;cAAG;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPpI,OAAA,CAAClD,IAAI;cAACsN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtC,QAAA,eACvBhI,OAAA,CAAC1C,SAAS;gBACR0I,IAAI,EAAC,WAAW;gBAChBuE,KAAK,EAAC,WAAW;gBACjBf,SAAS;gBACTE,OAAO,EAAC,UAAU;gBAClBzD,KAAK,EAAE9E,QAAQ,CAACW,SAAU;gBAC1B0I,QAAQ,EAAE1E,gBAAiB;gBAC3B/C,KAAK,EAAE,CAAC,CAACb,UAAU,CAACJ,SAAU;gBAC9B8I,UAAU,EAAE1I,UAAU,CAACJ,SAAS,IAAI;cAAG;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPpI,OAAA,CAAClD,IAAI;cAACsN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtC,QAAA,eACvBhI,OAAA,CAAC1C,SAAS;gBACR0I,IAAI,EAAC,OAAO;gBACZuE,KAAK,EAAC,YAAY;gBAClBf,SAAS;gBACTE,OAAO,EAAC,UAAU;gBAClBzD,KAAK,EAAE9E,QAAQ,CAACY,KAAM;gBACtByI,QAAQ,EAAE1E,gBAAiB;gBAC3B/C,KAAK,EAAE,CAAC,CAACb,UAAU,CAACH,KAAM;gBAC1B6I,UAAU,EAAE1I,UAAU,CAACH,KAAK,IAAI;cAAG;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPpI,OAAA,CAAClD,IAAI;cAACsN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtC,QAAA,eACvBhI,OAAA,CAAC1C,SAAS;gBACR0I,IAAI,EAAC,UAAU;gBACfuE,KAAK,EAAC,uBAAuB;gBAC7Bf,SAAS;gBACTE,OAAO,EAAC,UAAU;gBAClBzD,KAAK,EAAE9E,QAAQ,CAACa,QAAS;gBACzBwI,QAAQ,EAAE1E,gBAAiB;gBAC3BkF,WAAW,EAAC,YAAY;gBACxBjI,KAAK,EAAE,CAAC,CAACb,UAAU,CAACF,QAAS;gBAC7B4I,UAAU,EAAE1I,UAAU,CAACF,QAAQ,IAAI;cAAG;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPpI,OAAA,CAAClD,IAAI;cAACsN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAtC,QAAA,eACvBhI,OAAA,CAAC1C,SAAS;gBACR0I,IAAI,EAAC,gBAAgB;gBACrBuE,KAAK,EAAC,yBAAsB;gBAC5Bf,SAAS;gBACTE,OAAO,EAAC,UAAU;gBAClBzD,KAAK,EAAE9E,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,YAAY,GAAG,SAAU;gBAClE0I,UAAU,EAAE;kBACVM,QAAQ,EAAE,IAAI;kBACd3C,EAAE,EAAE;oBACFG,OAAO,EAAE;kBACX;gBACF,CAAE;gBACFmC,UAAU,eACR5K,OAAA,CAACtD,GAAG;kBAAC4L,EAAE,EAAE;oBAAEU,UAAU,EAAE,QAAQ;oBAAEF,KAAK,EAAE3H,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,cAAc,GAAG;kBAAY,CAAE;kBAAA+F,QAAA,EACtG7G,QAAQ,CAACc,cAAc,KAAK,GAAG,GAC5B,mDAAmD,GACnD;gBAA6D;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAChBpI,OAAA,CAAC3C,aAAa;UAAA2K,QAAA,gBACZhI,OAAA,CAACpD,MAAM;YAACuM,OAAO,EAAE1D,iBAAkB;YAAAuC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDpI,OAAA,CAACpD,MAAM;YACLuM,OAAO,EAAEzC,UAAW;YACpB+D,QAAQ,EAAElK,OAAO,IAAIoJ,MAAM,CAACC,IAAI,CAAC1H,UAAU,CAAC,CAACiC,MAAM,GAAG,CAAE;YACxD+G,SAAS,EAAE3K,OAAO,gBAAGP,OAAA,CAAC9B,gBAAgB;cAACsK,IAAI,EAAE;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpI,OAAA,CAACZ,QAAQ;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnEsB,OAAO,EAAC,WAAW;YACnBZ,KAAK,EAAC,SAAS;YAAAd,QAAA,EAChB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIrH,UAAU,KAAK,iBAAiB,EAAE;MAC3C,oBACEf,OAAA,CAAC9C,MAAM;QAACmM,IAAI,EAAExI,UAAW;QAACyI,OAAO,EAAE7D,iBAAkB;QAAC8D,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAxB,QAAA,gBAC3EhI,OAAA,CAAC7C,WAAW;UAAA6K,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzDpI,OAAA,CAAC5C,aAAa;UAAA4K,QAAA,EACXzH,OAAO,gBACNP,OAAA,CAAC9B,gBAAgB;YAAA+J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB3H,MAAM,CAAC0D,MAAM,KAAK,CAAC,gBACrBnE,OAAA,CAAC/B,KAAK;YAAC8J,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEzDpI,OAAA,CAACrC,IAAI;YAAAqK,QAAA,EACFvH,MAAM,CAAC+D,GAAG,CAAEmB,MAAM,iBACjB3F,OAAA,CAACpC,QAAQ;cACPuN,MAAM;cAENhC,OAAO,EAAEA,CAAA,KAAMzD,kBAAkB,CAACC,MAAM,CAAE;cAAAqC,QAAA,eAE1ChI,OAAA,CAACnC,YAAY;gBACXuN,OAAO,EAAE,WAAWzF,MAAM,CAACtE,aAAa,EAAG;gBAC3CgK,SAAS,EAAE,cAAc1F,MAAM,CAACpE,SAAS,IAAI,KAAK,eAAeoE,MAAM,CAACrE,OAAO,IAAI,KAAK,eAAeqE,MAAM,CAAChE,aAAa,IAAI,KAAK;cAAK;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC,GANGzC,MAAM,CAACtE,aAAa;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBpI,OAAA,CAAC3C,aAAa;UAAA2K,QAAA,eACZhI,OAAA,CAACpD,MAAM;YAACuM,OAAO,EAAE1D,iBAAkB;YAAAuC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIrH,UAAU,KAAK,eAAe,EAAE;MACzC,oBACEf,OAAA,CAAC9C,MAAM;QAACmM,IAAI,EAAExI,UAAW;QAACyI,OAAO,EAAE7D,iBAAkB;QAAC8D,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAxB,QAAA,gBAC3EhI,OAAA,CAAC7C,WAAW;UAAA6K,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzCpI,OAAA,CAAC5C,aAAa;UAAA4K,QAAA,EACX,CAAC/G,cAAc,GACdV,OAAO,gBACLP,OAAA,CAAC9B,gBAAgB;YAAA+J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB3H,MAAM,CAAC0D,MAAM,KAAK,CAAC,gBACrBnE,OAAA,CAAC/B,KAAK;YAAC8J,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEzDpI,OAAA,CAACrC,IAAI;YAAAqK,QAAA,EACFvH,MAAM,CAAC+D,GAAG,CAAEmB,MAAM,iBACjB3F,OAAA,CAACpC,QAAQ;cACPuN,MAAM;cAENhC,OAAO,EAAEA,CAAA,KAAMjI,iBAAiB,CAACyE,MAAM,CAAE;cAAAqC,QAAA,eAEzChI,OAAA,CAACnC,YAAY;gBACXuN,OAAO,EAAE,WAAWzF,MAAM,CAACtE,aAAa,EAAG;gBAC3CgK,SAAS,EAAE,cAAc1F,MAAM,CAACpE,SAAS,IAAI,KAAK,eAAeoE,MAAM,CAACrE,OAAO,IAAI,KAAK,eAAeqE,MAAM,CAAChE,aAAa,IAAI,KAAK;cAAK;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC,GANGzC,MAAM,CAACtE,aAAa;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACP,gBAEDpI,OAAA,CAACtD,GAAG;YAAAsL,QAAA,gBACFhI,OAAA,CAAC/B,KAAK;cAAC8J,QAAQ,EAAC,SAAS;cAACO,EAAE,EAAE;gBAAEmB,EAAE,EAAE;cAAE,CAAE;cAAAzB,QAAA,GAAC,0CACC,EAAC/G,cAAc,CAACI,aAAa,EAAC,GACxE;YAAA;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRpI,OAAA,CAACrD,UAAU;cAAC+M,OAAO,EAAC,OAAO;cAACpB,EAAE,EAAE;gBAAEmB,EAAE,EAAE;cAAE,CAAE;cAAAzB,QAAA,EAAC;YAE3C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpI,OAAA,CAAC/B,KAAK;cAAC8J,QAAQ,EAAC,MAAM;cAACO,EAAE,EAAE;gBAAEmB,EAAE,EAAE;cAAE,CAAE;cAAAzB,QAAA,gBACnChI,OAAA,CAACrD,UAAU;gBAAC+M,OAAO,EAAC,WAAW;gBAACV,UAAU,EAAC,MAAM;gBAAAhB,QAAA,EAAC;cAElD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbpI,OAAA;gBAAAgI,QAAA,gBACEhI,OAAA;kBAAAgI,QAAA,EAAI;gBAA0E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnFpI,OAAA;kBAAAgI,QAAA,EAAI;gBAA+C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxDpI,OAAA;kBAAAgI,QAAA,EAAI;gBAAiD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRpI,OAAA,CAACtD,GAAG;cAAC4L,EAAE,EAAE;gBAAEW,OAAO,EAAE,MAAM;gBAAEqC,aAAa,EAAE,QAAQ;gBAAEpC,GAAG,EAAE,CAAC;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAzB,QAAA,gBACnEhI,OAAA,CAACrD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAA1B,QAAA,gBACzBhI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnH,cAAc,CAACW,YAAY,IAAI,KAAK;cAAA;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACbpI,OAAA,CAACrD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAA1B,QAAA,gBACzBhI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnH,cAAc,CAACS,YAAY,IAAI,KAAK;cAAA;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACbpI,OAAA,CAACrD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAA1B,QAAA,gBACzBhI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnH,cAAc,CAACU,aAAa,IAAI,KAAK;cAAA;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBpI,OAAA,CAAC3C,aAAa;UAAA2K,QAAA,gBACZhI,OAAA,CAACpD,MAAM;YAACuM,OAAO,EAAE1D,iBAAkB;YAAAuC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDnH,cAAc,iBACbjB,OAAA,CAACpD,MAAM;YACLuM,OAAO,EAAEzC,UAAW;YACpB+D,QAAQ,EAAElK,OAAQ;YAClBuI,KAAK,EAAC,OAAO;YACboC,SAAS,EAAE3K,OAAO,gBAAGP,OAAA,CAAC9B,gBAAgB;cAACsK,IAAI,EAAE;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpI,OAAA,CAAChB,UAAU;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACtE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIrH,UAAU,KAAK,mBAAmB,EAAE;MAC7C,oBACEf,OAAA,CAAC9C,MAAM;QAACmM,IAAI,EAAExI,UAAW;QAACyI,OAAO,EAAE7D,iBAAkB;QAAC8D,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAxB,QAAA,gBAC3EhI,OAAA,CAAC7C,WAAW;UAAA6K,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAClDpI,OAAA,CAAC5C,aAAa;UAAA4K,QAAA,EACXzH,OAAO,gBACNP,OAAA,CAAC9B,gBAAgB;YAAA+J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB9F,eAAe,CAAC6B,MAAM,KAAK,CAAC,gBAC9BnE,OAAA,CAAC/B,KAAK;YAAC8J,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE9DpI,OAAA,CAAC1B,cAAc;YAAC+J,SAAS,EAAExL,KAAM;YAACyL,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,eAC9ChI,OAAA,CAAC7B,KAAK;cAACqK,IAAI,EAAC,OAAO;cAAAR,QAAA,gBACjBhI,OAAA,CAACzB,SAAS;gBAAAyJ,QAAA,eACRhI,OAAA,CAACxB,QAAQ;kBAAAwJ,QAAA,gBACPhI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7BpI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BpI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAChCpI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpCpI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BpI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnCpI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpCpI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZpI,OAAA,CAAC5B,SAAS;gBAAA4J,QAAA,EACP1F,eAAe,CAACkC,GAAG,CAAC,CAAC+G,MAAM,EAAEtB,KAAK,kBACjCjK,OAAA,CAACxB,QAAQ;kBAAAwJ,QAAA,gBACPhI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAEuD,MAAM,CAAClK;kBAAa;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7CpI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAEuD,MAAM,CAACjK;kBAAO;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvCpI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAEuD,MAAM,CAAChK;kBAAS;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzCpI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAEuD,MAAM,CAAC/J;kBAAY;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5CpI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAEuD,MAAM,CAAC9J;kBAAO;oBAAAwG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvCpI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAEuD,MAAM,CAAC7J;kBAAY;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5CpI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAEuD,MAAM,CAAC5J;kBAAa;oBAAAsG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7CpI,OAAA,CAAC3B,SAAS;oBAAA2J,QAAA,EAAEuD,MAAM,CAACC,IAAI,CAACrH;kBAAM;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA,GAR9B6B,KAAK;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASV,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBpI,OAAA,CAAC3C,aAAa;UAAA2K,QAAA,eACZhI,OAAA,CAACpD,MAAM;YAACuM,OAAO,EAAE1D,iBAAkB;YAAAuC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACEpI,OAAA,CAACtD,GAAG;IAAAsL,QAAA,GACDrH,cAAc,KAAK,kBAAkB,IAAI,CAACE,UAAU,gBACnDb,OAAA,CAACnD,KAAK;MAACyL,EAAE,EAAE;QAAEmD,CAAC,EAAE;MAAE,CAAE;MAAAzD,QAAA,gBAClBhI,OAAA,CAACtD,GAAG;QAAC4L,EAAE,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAEyC,cAAc,EAAE,UAAU;UAAEC,UAAU,EAAE,QAAQ;UAAElC,EAAE,EAAE;QAAE,CAAE;QAAAzB,QAAA,eACpFhI,OAAA,CAACtD,GAAG;UAAC4L,EAAE,EAAE;YAAEW,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAlB,QAAA,gBACnChI,OAAA,CAACpD,MAAM;YACL8M,OAAO,EAAC,UAAU;YAClBZ,KAAK,EAAC,SAAS;YACfoC,SAAS,eAAElL,OAAA,CAACd,WAAW;cAAA+I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3Be,OAAO,EAAEA,CAAA,KAAM;cACblG,mBAAmB,CAAC,CAAC;cACrBjC,aAAa,CAAC,mBAAmB,CAAC;cAClCF,aAAa,CAAC,IAAI,CAAC;YACrB,CAAE;YACFwH,EAAE,EAAE;cACFU,UAAU,EAAE,QAAQ;cACpBH,YAAY,EAAE;YAChB,CAAE;YAAAb,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpI,OAAA,CAACpD,MAAM;YACL8M,OAAO,EAAC,WAAW;YACnBZ,KAAK,EAAC,SAAS;YACfoC,SAAS,eAAElL,OAAA,CAACpB,OAAO;cAAAqJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBe,OAAO,EAAEA,CAAA,KAAM3F,qBAAqB,CAAC,CAAE;YACvC8E,EAAE,EAAE;cACFU,UAAU,EAAE,QAAQ;cACpBH,YAAY,EAAE;YAChB,CAAE;YAAAb,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACL7H,OAAO,gBACNP,OAAA,CAACtD,GAAG;QAAC4L,EAAE,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAEyC,cAAc,EAAE,QAAQ;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAA5D,QAAA,eAC5DhI,OAAA,CAAC9B,gBAAgB;UAAA+J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GAENN,iBAAiB,CAAC,CACnB;IAAA;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,CAACvH,UAAU,gBACbb,OAAA,CAACnD,KAAK;MAACyL,EAAE,EAAE;QAAEmD,CAAC,EAAE,CAAC;QAAEI,SAAS,EAAE,OAAO;QAAE5C,OAAO,EAAE,MAAM;QAAE0C,UAAU,EAAE,QAAQ;QAAED,cAAc,EAAE;MAAS,CAAE;MAAA1D,QAAA,EACtG,CAACrH,cAAc,gBACdX,OAAA,CAACrD,UAAU;QAAC+M,OAAO,EAAC,OAAO;QAAA1B,QAAA,EAAC;MAE5B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,gBAEbpI,OAAA,CAACtD,GAAG;QAAC4L,EAAE,EAAE;UAAEwD,SAAS,EAAE;QAAS,CAAE;QAAA9D,QAAA,gBAC/BhI,OAAA,CAACrD,UAAU;UAAC+M,OAAO,EAAC,IAAI;UAACqC,YAAY;UAAA/D,QAAA,GAClCrH,cAAc,KAAK,YAAY,IAAI,mBAAmB,EACtDA,cAAc,KAAK,gBAAgB,IAAI,iBAAiB,EACxDA,cAAc,KAAK,eAAe,IAAI,gBAAgB,EACtDA,cAAc,KAAK,mBAAmB,IAAI,6BAA6B;QAAA;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACbpI,OAAA,CAAC9B,gBAAgB;UAACoK,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,IAAI,EAEPgB,YAAY,CAAC,CAAC,eAGfpJ,OAAA,CAACN,oBAAoB;MACnB2J,IAAI,EAAE7G,gBAAiB;MACvB8G,OAAO,EAAEA,CAAA,KAAM7G,mBAAmB,CAAC,KAAK,CAAE;MAC1CuJ,SAAS,EAAE7G;IAAoB;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC9H,EAAA,CAtqCIL,SAAS;AAAAgM,EAAA,GAAThM,SAAS;AAwqCf,eAAeA,SAAS;AAAC,IAAAgM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}