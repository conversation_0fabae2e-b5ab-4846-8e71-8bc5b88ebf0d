{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'kurang dari 1 detik',\n    other: 'kurang dari {{count}} detik'\n  },\n  xSeconds: {\n    one: '1 detik',\n    other: '{{count}} detik'\n  },\n  halfAMinute: 'setengah menit',\n  lessThanXMinutes: {\n    one: 'kurang dari 1 menit',\n    other: 'kurang dari {{count}} menit'\n  },\n  xMinutes: {\n    one: '1 menit',\n    other: '{{count}} menit'\n  },\n  aboutXHours: {\n    one: 'sekitar 1 jam',\n    other: 'sekitar {{count}} jam'\n  },\n  xHours: {\n    one: '1 jam',\n    other: '{{count}} jam'\n  },\n  xDays: {\n    one: '1 hari',\n    other: '{{count}} hari'\n  },\n  aboutXWeeks: {\n    one: 'sekitar 1 minggu',\n    other: 'sekitar {{count}} minggu'\n  },\n  xWeeks: {\n    one: '1 minggu',\n    other: '{{count}} minggu'\n  },\n  aboutXMonths: {\n    one: 'sekitar 1 bulan',\n    other: 'sekitar {{count}} bulan'\n  },\n  xMonths: {\n    one: '1 bulan',\n    other: '{{count}} bulan'\n  },\n  aboutXYears: {\n    one: 'sekitar 1 tahun',\n    other: 'sekitar {{count}} tahun'\n  },\n  xYears: {\n    one: '1 tahun',\n    other: '{{count}} tahun'\n  },\n  overXYears: {\n    one: 'lebih dari 1 tahun',\n    other: 'lebih dari {{count}} tahun'\n  },\n  almostXYears: {\n    one: 'hampir 1 tahun',\n    other: 'hampir {{count}} tahun'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'dalam waktu ' + result;\n    } else {\n      return result + ' yang lalu';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "toString", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/id/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'kurang dari 1 detik',\n    other: 'kurang dari {{count}} detik'\n  },\n  xSeconds: {\n    one: '1 detik',\n    other: '{{count}} detik'\n  },\n  halfAMinute: 'setengah menit',\n  lessThanXMinutes: {\n    one: 'kurang dari 1 menit',\n    other: 'kurang dari {{count}} menit'\n  },\n  xMinutes: {\n    one: '1 menit',\n    other: '{{count}} menit'\n  },\n  aboutXHours: {\n    one: 'sekitar 1 jam',\n    other: 'sekitar {{count}} jam'\n  },\n  xHours: {\n    one: '1 jam',\n    other: '{{count}} jam'\n  },\n  xDays: {\n    one: '1 hari',\n    other: '{{count}} hari'\n  },\n  aboutXWeeks: {\n    one: 'sekitar 1 minggu',\n    other: 'sekitar {{count}} minggu'\n  },\n  xWeeks: {\n    one: '1 minggu',\n    other: '{{count}} minggu'\n  },\n  aboutXMonths: {\n    one: 'sekitar 1 bulan',\n    other: 'sekitar {{count}} bulan'\n  },\n  xMonths: {\n    one: '1 bulan',\n    other: '{{count}} bulan'\n  },\n  aboutXYears: {\n    one: 'sekitar 1 tahun',\n    other: 'sekitar {{count}} tahun'\n  },\n  xYears: {\n    one: '1 tahun',\n    other: '{{count}} tahun'\n  },\n  overXYears: {\n    one: 'lebih dari 1 tahun',\n    other: 'lebih dari {{count}} tahun'\n  },\n  almostXYears: {\n    one: 'hampir 1 tahun',\n    other: 'hampir {{count}} tahun'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'dalam waktu ' + result;\n    } else {\n      return result + ' yang lalu';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,gBAAgB;EAC7BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEJ,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC;EAClE;EACA,IAAIJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,cAAc,GAAGL,MAAM;IAChC,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,YAAY;IAC9B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}