{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\admin\\\\TipologieCaviManager.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Tabs, Tab, Typography, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Chip, IconButton, Alert, CircularProgress, Grid, Card, CardContent, CardActions, Accordion, AccordionSummary, AccordionDetails, FormControlLabel, Switch } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, ExpandMore as ExpandMoreIcon, Category as CategoryIcon, Business as BusinessIcon, Assignment as AssignmentIcon, Cable as CableIcon, Search as SearchIcon } from '@mui/icons-material';\nimport tipologieCaviService from '../../services/tipologieCaviService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TipologieCaviManager = () => {\n  _s();\n  const [tabValue, setTabValue] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Stati per categorie\n  const [categorie, setCategorie] = useState([]);\n  const [categoriaDialog, setCategoriaDialog] = useState(false);\n  const [categoriaForm, setCategoriaForm] = useState({\n    nome_categoria: '',\n    descrizione: '',\n    id_categoria_padre: null,\n    livello: 1,\n    ordine_visualizzazione: 0,\n    attiva: true\n  });\n  const [editingCategoria, setEditingCategoria] = useState(null);\n\n  // Stati per produttori\n  const [produttori, setProduttori] = useState([]);\n  const [produttoreDialog, setProduttoreDialog] = useState(false);\n  const [produttoreForm, setProduttoreForm] = useState({\n    nome_produttore: '',\n    paese: '',\n    sito_web: '',\n    email_contatto: '',\n    telefono: '',\n    note: '',\n    attivo: true\n  });\n  const [editingProduttore, setEditingProduttore] = useState(null);\n\n  // Stati per standard\n  const [standard, setStandard] = useState([]);\n  const [standardDialog, setStandardDialog] = useState(false);\n  const [standardForm, setStandardForm] = useState({\n    nome_standard: '',\n    ente_normativo: '',\n    descrizione: '',\n    anno_pubblicazione: null,\n    versione: '',\n    url_documento: '',\n    attivo: true\n  });\n  const [editingStandard, setEditingStandard] = useState(null);\n\n  // Stati per tipologie\n  const [tipologie, setTipologie] = useState([]);\n  const [tipologieTotal, setTipologieTotal] = useState(0);\n  const [tipologiePage, setTipologiePage] = useState(1);\n  const [tipologiePageSize] = useState(20);\n  const [tipologiaDialog, setTipologiaDialog] = useState(false);\n  const [tipologiaForm, setTipologiaForm] = useState({\n    codice_prodotto: '',\n    nome_commerciale: '',\n    id_produttore: null,\n    id_categoria: null,\n    id_standard_principale: null,\n    descrizione_breve: '',\n    descrizione_completa: '',\n    materiale_guaina_esterna: '',\n    diametro_esterno_mm: null,\n    peso_kg_per_km: null,\n    temperatura_min_celsius: null,\n    temperatura_max_celsius: null,\n    raggio_curvatura_min_mm: null,\n    resistente_uv: false,\n    resistente_olio: false,\n    resistente_fiamma: false,\n    per_esterno: false,\n    per_interrato: false,\n    scheda_tecnica_url: '',\n    immagine_url: '',\n    prezzo_indicativo_euro_per_metro: null,\n    disponibile: true,\n    note: ''\n  });\n  const [editingTipologia, setEditingTipologia] = useState(null);\n\n  // Filtri per tipologie\n  const [filtriTipologie, setFiltriTipologie] = useState({\n    categoria_id: null,\n    produttore_id: null,\n    disponibile: null,\n    search_text: ''\n  });\n  useEffect(() => {\n    loadData();\n  }, [tabValue]);\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      switch (tabValue) {\n        case 0:\n          await loadCategorie();\n          break;\n        case 1:\n          await loadProduttori();\n          break;\n        case 2:\n          await loadStandard();\n          break;\n        case 3:\n          await loadTipologie();\n          break;\n      }\n    } catch (error) {\n      setError('Errore nel caricamento dei dati: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadCategorie = async () => {\n    const data = await tipologieCaviService.getCategorie();\n    setCategorie(data);\n  };\n  const loadProduttori = async () => {\n    const data = await tipologieCaviService.getProduttori();\n    setProduttori(data);\n  };\n  const loadStandard = async () => {\n    const data = await tipologieCaviService.getStandard();\n    setStandard(data);\n  };\n  const loadTipologie = async () => {\n    const params = {\n      page: tipologiePage,\n      page_size: tipologiePageSize,\n      ...filtriTipologie\n    };\n    const data = await tipologieCaviService.getTipologie(params);\n    setTipologie(data.tipologie);\n    setTipologieTotal(data.total_count);\n  };\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n    setError('');\n    setSuccess('');\n  };\n\n  // Funzioni per categorie\n  const handleCreateCategoria = () => {\n    setCategoriaForm({\n      nome_categoria: '',\n      descrizione: '',\n      id_categoria_padre: null,\n      livello: 1,\n      ordine_visualizzazione: 0,\n      attiva: true\n    });\n    setEditingCategoria(null);\n    setCategoriaDialog(true);\n  };\n  const handleEditCategoria = categoria => {\n    setCategoriaForm(categoria);\n    setEditingCategoria(categoria.id_categoria);\n    setCategoriaDialog(true);\n  };\n  const handleSaveCategoria = async () => {\n    try {\n      if (editingCategoria) {\n        await tipologieCaviService.updateCategoria(editingCategoria, categoriaForm);\n        setSuccess('Categoria aggiornata con successo');\n      } else {\n        await tipologieCaviService.createCategoria(categoriaForm);\n        setSuccess('Categoria creata con successo');\n      }\n      setCategoriaDialog(false);\n      await loadCategorie();\n    } catch (error) {\n      setError('Errore nel salvataggio: ' + error.message);\n    }\n  };\n  const handleDeleteCategoria = async id => {\n    if (window.confirm('Sei sicuro di voler eliminare questa categoria?')) {\n      try {\n        await tipologieCaviService.deleteCategoria(id);\n        setSuccess('Categoria eliminata con successo');\n        await loadCategorie();\n      } catch (error) {\n        setError('Errore nell\\'eliminazione: ' + error.message);\n      }\n    }\n  };\n\n  // Funzioni per produttori\n  const handleCreateProduttore = () => {\n    setProduttoreForm({\n      nome_produttore: '',\n      paese: '',\n      sito_web: '',\n      email_contatto: '',\n      telefono: '',\n      note: '',\n      attivo: true\n    });\n    setEditingProduttore(null);\n    setProduttoreDialog(true);\n  };\n  const handleEditProduttore = produttore => {\n    setProduttoreForm(produttore);\n    setEditingProduttore(produttore.id_produttore);\n    setProduttoreDialog(true);\n  };\n  const handleSaveProduttore = async () => {\n    try {\n      if (editingProduttore) {\n        await tipologieCaviService.updateProduttore(editingProduttore, produttoreForm);\n        setSuccess('Produttore aggiornato con successo');\n      } else {\n        await tipologieCaviService.createProduttore(produttoreForm);\n        setSuccess('Produttore creato con successo');\n      }\n      setProduttoreDialog(false);\n      await loadProduttori();\n    } catch (error) {\n      setError('Errore nel salvataggio: ' + error.message);\n    }\n  };\n  const handleDeleteProduttore = async id => {\n    if (window.confirm('Sei sicuro di voler eliminare questo produttore?')) {\n      try {\n        await tipologieCaviService.deleteProduttore(id);\n        setSuccess('Produttore eliminato con successo');\n        await loadProduttori();\n      } catch (error) {\n        setError('Errore nell\\'eliminazione: ' + error.message);\n      }\n    }\n  };\n\n  // Funzioni per standard\n  const handleCreateStandard = () => {\n    setStandardForm({\n      nome_standard: '',\n      ente_normativo: '',\n      descrizione: '',\n      anno_pubblicazione: null,\n      versione: '',\n      url_documento: '',\n      attivo: true\n    });\n    setEditingStandard(null);\n    setStandardDialog(true);\n  };\n  const handleEditStandard = std => {\n    setStandardForm(std);\n    setEditingStandard(std.id_standard);\n    setStandardDialog(true);\n  };\n  const handleSaveStandard = async () => {\n    try {\n      if (editingStandard) {\n        await tipologieCaviService.updateStandard(editingStandard, standardForm);\n        setSuccess('Standard aggiornato con successo');\n      } else {\n        await tipologieCaviService.createStandard(standardForm);\n        setSuccess('Standard creato con successo');\n      }\n      setStandardDialog(false);\n      await loadStandard();\n    } catch (error) {\n      setError('Errore nel salvataggio: ' + error.message);\n    }\n  };\n  const handleDeleteStandard = async id => {\n    if (window.confirm('Sei sicuro di voler eliminare questo standard?')) {\n      try {\n        await tipologieCaviService.deleteStandard(id);\n        setSuccess('Standard eliminato con successo');\n        await loadStandard();\n      } catch (error) {\n        setError('Errore nell\\'eliminazione: ' + error.message);\n      }\n    }\n  };\n  const renderCategorieTab = () => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Gestione Categorie Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 22\n        }, this),\n        onClick: handleCreateCategoria,\n        children: \"Nuova Categoria\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Nome\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Descrizione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Livello\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Categoria Padre\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Attiva\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: categorie.map(categoria => {\n            var _categoria$categoria_;\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: categoria.nome_categoria\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: categoria.descrizione\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: categoria.livello\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: ((_categoria$categoria_ = categoria.categoria_padre) === null || _categoria$categoria_ === void 0 ? void 0 : _categoria$categoria_.nome_categoria) || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: categoria.attiva ? 'Sì' : 'No',\n                  color: categoria.attiva ? 'success' : 'default',\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => handleEditCategoria(categoria),\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => handleDeleteCategoria(categoria.id_categoria),\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this)]\n            }, categoria.id_categoria, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 346,\n    columnNumber: 5\n  }, this);\n  const renderProduttoriTab = () => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Gestione Produttori Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 22\n        }, this),\n        onClick: handleCreateProduttore,\n        children: \"Nuovo Produttore\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Nome\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Paese\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Telefono\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Attivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: produttori.map(produttore => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: produttore.nome_produttore\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: produttore.paese\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: produttore.email_contatto\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: produttore.telefono\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: produttore.attivo ? 'Sì' : 'No',\n                color: produttore.attivo ? 'success' : 'default',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleEditProduttore(produttore),\n                children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleDeleteProduttore(produttore.id_produttore),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 19\n            }, this)]\n          }, produttore.id_produttore, true, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 407,\n    columnNumber: 5\n  }, this);\n  const renderStandardTab = () => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Gestione Standard Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 22\n        }, this),\n        onClick: handleCreateStandard,\n        children: \"Nuovo Standard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Nome\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Ente Normativo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Anno\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Versione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Attivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: standard.map(std => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: std.nome_standard\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: std.ente_normativo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: std.anno_pubblicazione\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: std.versione\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: std.attivo ? 'Sì' : 'No',\n                color: std.attivo ? 'success' : 'default',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleEditStandard(std),\n                children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => handleDeleteStandard(std.id_standard),\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 19\n            }, this)]\n          }, std.id_standard, true, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 466,\n    columnNumber: 5\n  }, this);\n  const renderTipologieTab = () => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Gestione Tipologie Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 22\n        }, this),\n        onClick: () => setTipologiaDialog(true),\n        children: \"Nuova Tipologia\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Ricerca\",\n            value: filtriTipologie.search_text,\n            onChange: e => setFiltriTipologie({\n              ...filtriTipologie,\n              search_text: e.target.value\n            }),\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 33\n              }, this)\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Categoria\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: filtriTipologie.categoria_id || '',\n              onChange: e => setFiltriTipologie({\n                ...filtriTipologie,\n                categoria_id: e.target.value || null\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"Tutte\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 17\n              }, this), categorie.map(cat => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: cat.id_categoria,\n                children: cat.nome_categoria\n              }, cat.id_categoria, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Produttore\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: filtriTipologie.produttore_id || '',\n              onChange: e => setFiltriTipologie({\n                ...filtriTipologie,\n                produttore_id: e.target.value || null\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"Tutti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 17\n              }, this), produttori.map(prod => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: prod.id_produttore,\n                children: prod.nome_produttore\n              }, prod.id_produttore, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            fullWidth: true,\n            onClick: loadTipologie,\n            sx: {\n              height: '56px'\n            },\n            children: \"Applica Filtri\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 597,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Codice Prodotto\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Nome Commerciale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Produttore\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Categoria\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Disponibile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: tipologie.map(tipologia => {\n            var _tipologia$produttore, _tipologia$categoria;\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: tipologia.codice_prodotto\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: tipologia.nome_commerciale\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: ((_tipologia$produttore = tipologia.produttore) === null || _tipologia$produttore === void 0 ? void 0 : _tipologia$produttore.nome_produttore) || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: ((_tipologia$categoria = tipologia.categoria) === null || _tipologia$categoria === void 0 ? void 0 : _tipologia$categoria.nome_categoria) || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: tipologia.disponibile ? 'Sì' : 'No',\n                  color: tipologia.disponibile ? 'success' : 'default',\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => handleEditTipologia(tipologia),\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => handleDeleteTipologia(tipologia.id_tipologia),\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 19\n              }, this)]\n            }, tipologia.id_tipologia, true, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 600,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 599,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 525,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%'\n    },\n    children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      onClose: () => setError(''),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 645,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"success\",\n      sx: {\n        mb: 2\n      },\n      onClose: () => setSuccess(''),\n      children: success\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 651,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        width: '100%',\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: tabValue,\n        onChange: handleTabChange,\n        indicatorColor: \"primary\",\n        textColor: \"primary\",\n        variant: \"scrollable\",\n        scrollButtons: \"auto\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(CategoryIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 22\n          }, this),\n          label: \"Categorie\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 22\n          }, this),\n          label: \"Produttori\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 22\n          }, this),\n          label: \"Standard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 22\n          }, this),\n          label: \"Tipologie\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 657,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [tabValue === 0 && renderCategorieTab(), tabValue === 1 && renderProduttoriTab(), tabValue === 2 && renderStandardTab(), tabValue === 3 && renderTipologieTab()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 672,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: categoriaDialog,\n      onClose: () => setCategoriaDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editingCategoria ? 'Modifica Categoria' : 'Nuova Categoria'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 681,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Nome Categoria\",\n              value: categoriaForm.nome_categoria,\n              onChange: e => setCategoriaForm({\n                ...categoriaForm,\n                nome_categoria: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Categoria Padre\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: categoriaForm.id_categoria_padre || '',\n                onChange: e => setCategoriaForm({\n                  ...categoriaForm,\n                  id_categoria_padre: e.target.value || null\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Nessuna (Categoria principale)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 19\n                }, this), categorie.filter(c => c.livello < 3).map(cat => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: cat.id_categoria,\n                  children: cat.nome_categoria\n                }, cat.id_categoria, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 695,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione\",\n              value: categoriaForm.descrizione,\n              onChange: e => setCategoriaForm({\n                ...categoriaForm,\n                descrizione: e.target.value\n              }),\n              multiline: true,\n              rows: 3\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Ordine Visualizzazione\",\n              type: \"number\",\n              value: categoriaForm.ordine_visualizzazione,\n              onChange: e => setCategoriaForm({\n                ...categoriaForm,\n                ordine_visualizzazione: parseInt(e.target.value) || 0\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: categoriaForm.attiva,\n                onChange: e => setCategoriaForm({\n                  ...categoriaForm,\n                  attiva: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 19\n              }, this),\n              label: \"Categoria Attiva\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 684,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setCategoriaDialog(false),\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSaveCategoria,\n          variant: \"contained\",\n          children: editingCategoria ? 'Aggiorna' : 'Crea'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 743,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: produttoreDialog,\n      onClose: () => setProduttoreDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editingProduttore ? 'Modifica Produttore' : 'Nuovo Produttore'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Nome Produttore\",\n              value: produttoreForm.nome_produttore,\n              onChange: e => setProduttoreForm({\n                ...produttoreForm,\n                nome_produttore: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 758,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Paese\",\n              value: produttoreForm.paese,\n              onChange: e => setProduttoreForm({\n                ...produttoreForm,\n                paese: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Email Contatto\",\n              type: \"email\",\n              value: produttoreForm.email_contatto,\n              onChange: e => setProduttoreForm({\n                ...produttoreForm,\n                email_contatto: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 775,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Telefono\",\n              value: produttoreForm.telefono,\n              onChange: e => setProduttoreForm({\n                ...produttoreForm,\n                telefono: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 784,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Sito Web\",\n              value: produttoreForm.sito_web,\n              onChange: e => setProduttoreForm({\n                ...produttoreForm,\n                sito_web: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note\",\n              value: produttoreForm.note,\n              onChange: e => setProduttoreForm({\n                ...produttoreForm,\n                note: e.target.value\n              }),\n              multiline: true,\n              rows: 3\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 800,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: produttoreForm.attivo,\n                onChange: e => setProduttoreForm({\n                  ...produttoreForm,\n                  attivo: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 19\n              }, this),\n              label: \"Produttore Attivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 810,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 757,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 756,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setProduttoreDialog(false),\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 824,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSaveProduttore,\n          variant: \"contained\",\n          children: editingProduttore ? 'Aggiorna' : 'Crea'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 823,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: standardDialog,\n      onClose: () => setStandardDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: editingStandard ? 'Modifica Standard' : 'Nuovo Standard'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 833,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Nome Standard\",\n              value: standardForm.nome_standard,\n              onChange: e => setStandardForm({\n                ...standardForm,\n                nome_standard: e.target.value\n              }),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Ente Normativo\",\n              value: standardForm.ente_normativo,\n              onChange: e => setStandardForm({\n                ...standardForm,\n                ente_normativo: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 848,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione\",\n              value: standardForm.descrizione,\n              onChange: e => setStandardForm({\n                ...standardForm,\n                descrizione: e.target.value\n              }),\n              multiline: true,\n              rows: 3\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Anno Pubblicazione\",\n              type: \"number\",\n              value: standardForm.anno_pubblicazione || '',\n              onChange: e => setStandardForm({\n                ...standardForm,\n                anno_pubblicazione: parseInt(e.target.value) || null\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Versione\",\n              value: standardForm.versione,\n              onChange: e => setStandardForm({\n                ...standardForm,\n                versione: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 875,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 874,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              control: /*#__PURE__*/_jsxDEV(Switch, {\n                checked: standardForm.attivo,\n                onChange: e => setStandardForm({\n                  ...standardForm,\n                  attivo: e.target.checked\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 19\n              }, this),\n              label: \"Standard Attivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 882,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"URL Documento\",\n              value: standardForm.url_documento,\n              onChange: e => setStandardForm({\n                ...standardForm,\n                url_documento: e.target.value\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 893,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 837,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 836,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setStandardDialog(false),\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 904,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSaveStandard,\n          variant: \"contained\",\n          children: editingStandard ? 'Aggiorna' : 'Crea'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 905,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 903,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 832,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 643,\n    columnNumber: 5\n  }, this);\n};\n_s(TipologieCaviManager, \"iDQG0UQ86/1tKvQ+BcBsV3pOgHw=\");\n_c = TipologieCaviManager;\nexport default TipologieCaviManager;\nvar _c;\n$RefreshReg$(_c, \"TipologieCaviManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Tabs", "Tab", "Typography", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Chip", "IconButton", "<PERSON><PERSON>", "CircularProgress", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Accordion", "AccordionSummary", "AccordionDetails", "FormControlLabel", "Switch", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "ExpandMore", "ExpandMoreIcon", "Category", "CategoryIcon", "Business", "BusinessIcon", "Assignment", "AssignmentIcon", "Cable", "CableIcon", "Search", "SearchIcon", "tipologieCaviService", "jsxDEV", "_jsxDEV", "TipologieCaviManager", "_s", "tabValue", "setTabValue", "loading", "setLoading", "error", "setError", "success", "setSuccess", "categorie", "setCategorie", "categoriaDialog", "setCategoriaDialog", "categoriaForm", "setCategoriaForm", "nome_categoria", "descrizione", "id_categoria_padre", "livello", "ordine_visualizzazione", "attiva", "editingCategoria", "setEditingCategoria", "produttori", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "produttoreDialog", "setProduttoreDialog", "produttoreForm", "setProduttoreForm", "nome_produttore", "paese", "sito_web", "email_contatto", "telefono", "note", "attivo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setEditingProduttore", "standard", "setStandard", "standardDialog", "setStandardDialog", "standardForm", "setStandardForm", "nome_standard", "ente_normativo", "anno_pubblicazione", "versione", "url_documento", "editingStandard", "setEditingStandard", "tipologie", "setTipologie", "tipologieTotal", "setTipologieTotal", "tipologiePage", "setTipologiePage", "tipologiePageSize", "tipologiaDialog", "setTipologiaDialog", "tipologiaForm", "setTipologiaForm", "codice_prodotto", "nome_commerciale", "id_produttore", "id_categoria", "id_standard_principale", "descrizione_breve", "descrizione_completa", "materiale_guaina_esterna", "diametro_esterno_mm", "peso_kg_per_km", "temperatura_min_celsius", "temperatura_max_celsius", "raggio_curvatura_min_mm", "resistente_uv", "resistente_olio", "resistente_fiamma", "per_esterno", "per_interrato", "scheda_tecnica_url", "immagine_url", "prezzo_indicativo_euro_per_metro", "disponibile", "editingTipologia", "setEditingTipologia", "filtriTipologie", "setFiltriTipologie", "categoria_id", "produttore_id", "search_text", "loadData", "loadCategorie", "loadProduttori", "loadStandard", "loadTipologie", "message", "data", "getCategorie", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getStandard", "params", "page", "page_size", "getTipologie", "total_count", "handleTabChange", "event", "newValue", "handleCreateCategoria", "handleEditCategoria", "categoria", "handleSaveCategoria", "updateCategoria", "createCategoria", "handleDeleteCategoria", "id", "window", "confirm", "deleteCategoria", "handleCreateProduttore", "handleEditProduttore", "produttore", "handleSaveProduttore", "updateProduttore", "createProduttore", "handleDeleteProduttore", "deleteProduttore", "handleCreateStandard", "handleEditStandard", "std", "id_standard", "handleSaveStandard", "updateStandard", "createStandard", "handleDeleteStandard", "deleteStandard", "renderCategorieTab", "children", "sx", "display", "justifyContent", "mb", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "component", "map", "_categoria$categoria_", "categoria_padre", "label", "color", "size", "renderProduttoriTab", "renderStandardTab", "renderTipologieTab", "p", "container", "spacing", "item", "xs", "md", "fullWidth", "value", "onChange", "e", "target", "InputProps", "startAdornment", "cat", "prod", "height", "tipologia", "_tipologia$produttore", "_tipologia$categoria", "handleEditTipologia", "handleDeleteTipologia", "id_tipologia", "width", "severity", "onClose", "indicatorColor", "textColor", "scrollButtons", "icon", "mt", "open", "max<PERSON><PERSON><PERSON>", "required", "filter", "c", "multiline", "rows", "type", "parseInt", "control", "checked", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/admin/TipologieCaviManager.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Tabs,\n  Tab,\n  Typography,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Chip,\n  IconButton,\n  Alert,\n  CircularProgress,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  FormControlLabel,\n  Switch\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  ExpandMore as ExpandMoreIcon,\n  Category as CategoryIcon,\n  Business as BusinessIcon,\n  Assignment as AssignmentIcon,\n  Cable as CableIcon,\n  Search as SearchIcon\n} from '@mui/icons-material';\nimport tipologieCaviService from '../../services/tipologieCaviService';\n\nconst TipologieCaviManager = () => {\n  const [tabValue, setTabValue] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Stati per categorie\n  const [categorie, setCategorie] = useState([]);\n  const [categoriaDialog, setCategoriaDialog] = useState(false);\n  const [categoriaForm, setCategoriaForm] = useState({\n    nome_categoria: '',\n    descrizione: '',\n    id_categoria_padre: null,\n    livello: 1,\n    ordine_visualizzazione: 0,\n    attiva: true\n  });\n  const [editingCategoria, setEditingCategoria] = useState(null);\n\n  // Stati per produttori\n  const [produttori, setProduttori] = useState([]);\n  const [produttoreDialog, setProduttoreDialog] = useState(false);\n  const [produttoreForm, setProduttoreForm] = useState({\n    nome_produttore: '',\n    paese: '',\n    sito_web: '',\n    email_contatto: '',\n    telefono: '',\n    note: '',\n    attivo: true\n  });\n  const [editingProduttore, setEditingProduttore] = useState(null);\n\n  // Stati per standard\n  const [standard, setStandard] = useState([]);\n  const [standardDialog, setStandardDialog] = useState(false);\n  const [standardForm, setStandardForm] = useState({\n    nome_standard: '',\n    ente_normativo: '',\n    descrizione: '',\n    anno_pubblicazione: null,\n    versione: '',\n    url_documento: '',\n    attivo: true\n  });\n  const [editingStandard, setEditingStandard] = useState(null);\n\n  // Stati per tipologie\n  const [tipologie, setTipologie] = useState([]);\n  const [tipologieTotal, setTipologieTotal] = useState(0);\n  const [tipologiePage, setTipologiePage] = useState(1);\n  const [tipologiePageSize] = useState(20);\n  const [tipologiaDialog, setTipologiaDialog] = useState(false);\n  const [tipologiaForm, setTipologiaForm] = useState({\n    codice_prodotto: '',\n    nome_commerciale: '',\n    id_produttore: null,\n    id_categoria: null,\n    id_standard_principale: null,\n    descrizione_breve: '',\n    descrizione_completa: '',\n    materiale_guaina_esterna: '',\n    diametro_esterno_mm: null,\n    peso_kg_per_km: null,\n    temperatura_min_celsius: null,\n    temperatura_max_celsius: null,\n    raggio_curvatura_min_mm: null,\n    resistente_uv: false,\n    resistente_olio: false,\n    resistente_fiamma: false,\n    per_esterno: false,\n    per_interrato: false,\n    scheda_tecnica_url: '',\n    immagine_url: '',\n    prezzo_indicativo_euro_per_metro: null,\n    disponibile: true,\n    note: ''\n  });\n  const [editingTipologia, setEditingTipologia] = useState(null);\n\n  // Filtri per tipologie\n  const [filtriTipologie, setFiltriTipologie] = useState({\n    categoria_id: null,\n    produttore_id: null,\n    disponibile: null,\n    search_text: ''\n  });\n\n  useEffect(() => {\n    loadData();\n  }, [tabValue]);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      switch (tabValue) {\n        case 0:\n          await loadCategorie();\n          break;\n        case 1:\n          await loadProduttori();\n          break;\n        case 2:\n          await loadStandard();\n          break;\n        case 3:\n          await loadTipologie();\n          break;\n      }\n    } catch (error) {\n      setError('Errore nel caricamento dei dati: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadCategorie = async () => {\n    const data = await tipologieCaviService.getCategorie();\n    setCategorie(data);\n  };\n\n  const loadProduttori = async () => {\n    const data = await tipologieCaviService.getProduttori();\n    setProduttori(data);\n  };\n\n  const loadStandard = async () => {\n    const data = await tipologieCaviService.getStandard();\n    setStandard(data);\n  };\n\n  const loadTipologie = async () => {\n    const params = {\n      page: tipologiePage,\n      page_size: tipologiePageSize,\n      ...filtriTipologie\n    };\n    const data = await tipologieCaviService.getTipologie(params);\n    setTipologie(data.tipologie);\n    setTipologieTotal(data.total_count);\n  };\n\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n    setError('');\n    setSuccess('');\n  };\n\n  // Funzioni per categorie\n  const handleCreateCategoria = () => {\n    setCategoriaForm({\n      nome_categoria: '',\n      descrizione: '',\n      id_categoria_padre: null,\n      livello: 1,\n      ordine_visualizzazione: 0,\n      attiva: true\n    });\n    setEditingCategoria(null);\n    setCategoriaDialog(true);\n  };\n\n  const handleEditCategoria = (categoria) => {\n    setCategoriaForm(categoria);\n    setEditingCategoria(categoria.id_categoria);\n    setCategoriaDialog(true);\n  };\n\n  const handleSaveCategoria = async () => {\n    try {\n      if (editingCategoria) {\n        await tipologieCaviService.updateCategoria(editingCategoria, categoriaForm);\n        setSuccess('Categoria aggiornata con successo');\n      } else {\n        await tipologieCaviService.createCategoria(categoriaForm);\n        setSuccess('Categoria creata con successo');\n      }\n      setCategoriaDialog(false);\n      await loadCategorie();\n    } catch (error) {\n      setError('Errore nel salvataggio: ' + error.message);\n    }\n  };\n\n  const handleDeleteCategoria = async (id) => {\n    if (window.confirm('Sei sicuro di voler eliminare questa categoria?')) {\n      try {\n        await tipologieCaviService.deleteCategoria(id);\n        setSuccess('Categoria eliminata con successo');\n        await loadCategorie();\n      } catch (error) {\n        setError('Errore nell\\'eliminazione: ' + error.message);\n      }\n    }\n  };\n\n  // Funzioni per produttori\n  const handleCreateProduttore = () => {\n    setProduttoreForm({\n      nome_produttore: '',\n      paese: '',\n      sito_web: '',\n      email_contatto: '',\n      telefono: '',\n      note: '',\n      attivo: true\n    });\n    setEditingProduttore(null);\n    setProduttoreDialog(true);\n  };\n\n  const handleEditProduttore = (produttore) => {\n    setProduttoreForm(produttore);\n    setEditingProduttore(produttore.id_produttore);\n    setProduttoreDialog(true);\n  };\n\n  const handleSaveProduttore = async () => {\n    try {\n      if (editingProduttore) {\n        await tipologieCaviService.updateProduttore(editingProduttore, produttoreForm);\n        setSuccess('Produttore aggiornato con successo');\n      } else {\n        await tipologieCaviService.createProduttore(produttoreForm);\n        setSuccess('Produttore creato con successo');\n      }\n      setProduttoreDialog(false);\n      await loadProduttori();\n    } catch (error) {\n      setError('Errore nel salvataggio: ' + error.message);\n    }\n  };\n\n  const handleDeleteProduttore = async (id) => {\n    if (window.confirm('Sei sicuro di voler eliminare questo produttore?')) {\n      try {\n        await tipologieCaviService.deleteProduttore(id);\n        setSuccess('Produttore eliminato con successo');\n        await loadProduttori();\n      } catch (error) {\n        setError('Errore nell\\'eliminazione: ' + error.message);\n      }\n    }\n  };\n\n  // Funzioni per standard\n  const handleCreateStandard = () => {\n    setStandardForm({\n      nome_standard: '',\n      ente_normativo: '',\n      descrizione: '',\n      anno_pubblicazione: null,\n      versione: '',\n      url_documento: '',\n      attivo: true\n    });\n    setEditingStandard(null);\n    setStandardDialog(true);\n  };\n\n  const handleEditStandard = (std) => {\n    setStandardForm(std);\n    setEditingStandard(std.id_standard);\n    setStandardDialog(true);\n  };\n\n  const handleSaveStandard = async () => {\n    try {\n      if (editingStandard) {\n        await tipologieCaviService.updateStandard(editingStandard, standardForm);\n        setSuccess('Standard aggiornato con successo');\n      } else {\n        await tipologieCaviService.createStandard(standardForm);\n        setSuccess('Standard creato con successo');\n      }\n      setStandardDialog(false);\n      await loadStandard();\n    } catch (error) {\n      setError('Errore nel salvataggio: ' + error.message);\n    }\n  };\n\n  const handleDeleteStandard = async (id) => {\n    if (window.confirm('Sei sicuro di voler eliminare questo standard?')) {\n      try {\n        await tipologieCaviService.deleteStandard(id);\n        setSuccess('Standard eliminato con successo');\n        await loadStandard();\n      } catch (error) {\n        setError('Errore nell\\'eliminazione: ' + error.message);\n      }\n    }\n  };\n\n  const renderCategorieTab = () => (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>\n        <Typography variant=\"h6\">Gestione Categorie Cavi</Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={handleCreateCategoria}\n        >\n          Nuova Categoria\n        </Button>\n      </Box>\n\n      {loading ? (\n        <CircularProgress />\n      ) : (\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Nome</TableCell>\n                <TableCell>Descrizione</TableCell>\n                <TableCell>Livello</TableCell>\n                <TableCell>Categoria Padre</TableCell>\n                <TableCell>Attiva</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {categorie.map((categoria) => (\n                <TableRow key={categoria.id_categoria}>\n                  <TableCell>{categoria.nome_categoria}</TableCell>\n                  <TableCell>{categoria.descrizione}</TableCell>\n                  <TableCell>{categoria.livello}</TableCell>\n                  <TableCell>\n                    {categoria.categoria_padre?.nome_categoria || '-'}\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      label={categoria.attiva ? 'Sì' : 'No'}\n                      color={categoria.attiva ? 'success' : 'default'}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <IconButton onClick={() => handleEditCategoria(categoria)}>\n                      <EditIcon />\n                    </IconButton>\n                    <IconButton onClick={() => handleDeleteCategoria(categoria.id_categoria)}>\n                      <DeleteIcon />\n                    </IconButton>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      )}\n    </Box>\n  );\n\n  const renderProduttoriTab = () => (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>\n        <Typography variant=\"h6\">Gestione Produttori Cavi</Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={handleCreateProduttore}\n        >\n          Nuovo Produttore\n        </Button>\n      </Box>\n\n      {loading ? (\n        <CircularProgress />\n      ) : (\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Nome</TableCell>\n                <TableCell>Paese</TableCell>\n                <TableCell>Email</TableCell>\n                <TableCell>Telefono</TableCell>\n                <TableCell>Attivo</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {produttori.map((produttore) => (\n                <TableRow key={produttore.id_produttore}>\n                  <TableCell>{produttore.nome_produttore}</TableCell>\n                  <TableCell>{produttore.paese}</TableCell>\n                  <TableCell>{produttore.email_contatto}</TableCell>\n                  <TableCell>{produttore.telefono}</TableCell>\n                  <TableCell>\n                    <Chip\n                      label={produttore.attivo ? 'Sì' : 'No'}\n                      color={produttore.attivo ? 'success' : 'default'}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <IconButton onClick={() => handleEditProduttore(produttore)}>\n                      <EditIcon />\n                    </IconButton>\n                    <IconButton onClick={() => handleDeleteProduttore(produttore.id_produttore)}>\n                      <DeleteIcon />\n                    </IconButton>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      )}\n    </Box>\n  );\n\n  const renderStandardTab = () => (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>\n        <Typography variant=\"h6\">Gestione Standard Cavi</Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={handleCreateStandard}\n        >\n          Nuovo Standard\n        </Button>\n      </Box>\n\n      {loading ? (\n        <CircularProgress />\n      ) : (\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Nome</TableCell>\n                <TableCell>Ente Normativo</TableCell>\n                <TableCell>Anno</TableCell>\n                <TableCell>Versione</TableCell>\n                <TableCell>Attivo</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {standard.map((std) => (\n                <TableRow key={std.id_standard}>\n                  <TableCell>{std.nome_standard}</TableCell>\n                  <TableCell>{std.ente_normativo}</TableCell>\n                  <TableCell>{std.anno_pubblicazione}</TableCell>\n                  <TableCell>{std.versione}</TableCell>\n                  <TableCell>\n                    <Chip\n                      label={std.attivo ? 'Sì' : 'No'}\n                      color={std.attivo ? 'success' : 'default'}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <IconButton onClick={() => handleEditStandard(std)}>\n                      <EditIcon />\n                    </IconButton>\n                    <IconButton onClick={() => handleDeleteStandard(std.id_standard)}>\n                      <DeleteIcon />\n                    </IconButton>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      )}\n    </Box>\n  );\n\n  const renderTipologieTab = () => (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>\n        <Typography variant=\"h6\">Gestione Tipologie Cavi</Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          onClick={() => setTipologiaDialog(true)}\n        >\n          Nuova Tipologia\n        </Button>\n      </Box>\n\n      {/* Filtri */}\n      <Paper sx={{ p: 2, mb: 2 }}>\n        <Grid container spacing={2}>\n          <Grid item xs={12} md={3}>\n            <TextField\n              fullWidth\n              label=\"Ricerca\"\n              value={filtriTipologie.search_text}\n              onChange={(e) => setFiltriTipologie({...filtriTipologie, search_text: e.target.value})}\n              InputProps={{\n                startAdornment: <SearchIcon />\n              }}\n            />\n          </Grid>\n          <Grid item xs={12} md={3}>\n            <FormControl fullWidth>\n              <InputLabel>Categoria</InputLabel>\n              <Select\n                value={filtriTipologie.categoria_id || ''}\n                onChange={(e) => setFiltriTipologie({...filtriTipologie, categoria_id: e.target.value || null})}\n              >\n                <MenuItem value=\"\">Tutte</MenuItem>\n                {categorie.map((cat) => (\n                  <MenuItem key={cat.id_categoria} value={cat.id_categoria}>\n                    {cat.nome_categoria}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid item xs={12} md={3}>\n            <FormControl fullWidth>\n              <InputLabel>Produttore</InputLabel>\n              <Select\n                value={filtriTipologie.produttore_id || ''}\n                onChange={(e) => setFiltriTipologie({...filtriTipologie, produttore_id: e.target.value || null})}\n              >\n                <MenuItem value=\"\">Tutti</MenuItem>\n                {produttori.map((prod) => (\n                  <MenuItem key={prod.id_produttore} value={prod.id_produttore}>\n                    {prod.nome_produttore}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n          <Grid item xs={12} md={3}>\n            <Button\n              variant=\"outlined\"\n              fullWidth\n              onClick={loadTipologie}\n              sx={{ height: '56px' }}\n            >\n              Applica Filtri\n            </Button>\n          </Grid>\n        </Grid>\n      </Paper>\n\n      {loading ? (\n        <CircularProgress />\n      ) : (\n        <TableContainer component={Paper}>\n          <Table>\n            <TableHead>\n              <TableRow>\n                <TableCell>Codice Prodotto</TableCell>\n                <TableCell>Nome Commerciale</TableCell>\n                <TableCell>Produttore</TableCell>\n                <TableCell>Categoria</TableCell>\n                <TableCell>Disponibile</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {tipologie.map((tipologia) => (\n                <TableRow key={tipologia.id_tipologia}>\n                  <TableCell>{tipologia.codice_prodotto}</TableCell>\n                  <TableCell>{tipologia.nome_commerciale}</TableCell>\n                  <TableCell>{tipologia.produttore?.nome_produttore || '-'}</TableCell>\n                  <TableCell>{tipologia.categoria?.nome_categoria || '-'}</TableCell>\n                  <TableCell>\n                    <Chip\n                      label={tipologia.disponibile ? 'Sì' : 'No'}\n                      color={tipologia.disponibile ? 'success' : 'default'}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <IconButton onClick={() => handleEditTipologia(tipologia)}>\n                      <EditIcon />\n                    </IconButton>\n                    <IconButton onClick={() => handleDeleteTipologia(tipologia.id_tipologia)}>\n                      <DeleteIcon />\n                    </IconButton>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      )}\n    </Box>\n  );\n\n  return (\n    <Box sx={{ width: '100%' }}>\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }} onClose={() => setError('')}>\n          {error}\n        </Alert>\n      )}\n\n      {success && (\n        <Alert severity=\"success\" sx={{ mb: 2 }} onClose={() => setSuccess('')}>\n          {success}\n        </Alert>\n      )}\n\n      <Paper sx={{ width: '100%', mb: 2 }}>\n        <Tabs\n          value={tabValue}\n          onChange={handleTabChange}\n          indicatorColor=\"primary\"\n          textColor=\"primary\"\n          variant=\"scrollable\"\n          scrollButtons=\"auto\"\n        >\n          <Tab icon={<CategoryIcon />} label=\"Categorie\" />\n          <Tab icon={<BusinessIcon />} label=\"Produttori\" />\n          <Tab icon={<AssignmentIcon />} label=\"Standard\" />\n          <Tab icon={<CableIcon />} label=\"Tipologie\" />\n        </Tabs>\n      </Paper>\n\n      <Box sx={{ mt: 2 }}>\n        {tabValue === 0 && renderCategorieTab()}\n        {tabValue === 1 && renderProduttoriTab()}\n        {tabValue === 2 && renderStandardTab()}\n        {tabValue === 3 && renderTipologieTab()}\n      </Box>\n\n      {/* Dialog per Categoria */}\n      <Dialog open={categoriaDialog} onClose={() => setCategoriaDialog(false)} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {editingCategoria ? 'Modifica Categoria' : 'Nuova Categoria'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Nome Categoria\"\n                value={categoriaForm.nome_categoria}\n                onChange={(e) => setCategoriaForm({...categoriaForm, nome_categoria: e.target.value})}\n                required\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth>\n                <InputLabel>Categoria Padre</InputLabel>\n                <Select\n                  value={categoriaForm.id_categoria_padre || ''}\n                  onChange={(e) => setCategoriaForm({...categoriaForm, id_categoria_padre: e.target.value || null})}\n                >\n                  <MenuItem value=\"\">Nessuna (Categoria principale)</MenuItem>\n                  {categorie.filter(c => c.livello < 3).map((cat) => (\n                    <MenuItem key={cat.id_categoria} value={cat.id_categoria}>\n                      {cat.nome_categoria}\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Descrizione\"\n                value={categoriaForm.descrizione}\n                onChange={(e) => setCategoriaForm({...categoriaForm, descrizione: e.target.value})}\n                multiline\n                rows={3}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Ordine Visualizzazione\"\n                type=\"number\"\n                value={categoriaForm.ordine_visualizzazione}\n                onChange={(e) => setCategoriaForm({...categoriaForm, ordine_visualizzazione: parseInt(e.target.value) || 0})}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={categoriaForm.attiva}\n                    onChange={(e) => setCategoriaForm({...categoriaForm, attiva: e.target.checked})}\n                  />\n                }\n                label=\"Categoria Attiva\"\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setCategoriaDialog(false)}>Annulla</Button>\n          <Button onClick={handleSaveCategoria} variant=\"contained\">\n            {editingCategoria ? 'Aggiorna' : 'Crea'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per Produttore */}\n      <Dialog open={produttoreDialog} onClose={() => setProduttoreDialog(false)} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {editingProduttore ? 'Modifica Produttore' : 'Nuovo Produttore'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Nome Produttore\"\n                value={produttoreForm.nome_produttore}\n                onChange={(e) => setProduttoreForm({...produttoreForm, nome_produttore: e.target.value})}\n                required\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Paese\"\n                value={produttoreForm.paese}\n                onChange={(e) => setProduttoreForm({...produttoreForm, paese: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Email Contatto\"\n                type=\"email\"\n                value={produttoreForm.email_contatto}\n                onChange={(e) => setProduttoreForm({...produttoreForm, email_contatto: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Telefono\"\n                value={produttoreForm.telefono}\n                onChange={(e) => setProduttoreForm({...produttoreForm, telefono: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Sito Web\"\n                value={produttoreForm.sito_web}\n                onChange={(e) => setProduttoreForm({...produttoreForm, sito_web: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Note\"\n                value={produttoreForm.note}\n                onChange={(e) => setProduttoreForm({...produttoreForm, note: e.target.value})}\n                multiline\n                rows={3}\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={produttoreForm.attivo}\n                    onChange={(e) => setProduttoreForm({...produttoreForm, attivo: e.target.checked})}\n                  />\n                }\n                label=\"Produttore Attivo\"\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setProduttoreDialog(false)}>Annulla</Button>\n          <Button onClick={handleSaveProduttore} variant=\"contained\">\n            {editingProduttore ? 'Aggiorna' : 'Crea'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per Standard */}\n      <Dialog open={standardDialog} onClose={() => setStandardDialog(false)} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {editingStandard ? 'Modifica Standard' : 'Nuovo Standard'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Nome Standard\"\n                value={standardForm.nome_standard}\n                onChange={(e) => setStandardForm({...standardForm, nome_standard: e.target.value})}\n                required\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Ente Normativo\"\n                value={standardForm.ente_normativo}\n                onChange={(e) => setStandardForm({...standardForm, ente_normativo: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Descrizione\"\n                value={standardForm.descrizione}\n                onChange={(e) => setStandardForm({...standardForm, descrizione: e.target.value})}\n                multiline\n                rows={3}\n              />\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Anno Pubblicazione\"\n                type=\"number\"\n                value={standardForm.anno_pubblicazione || ''}\n                onChange={(e) => setStandardForm({...standardForm, anno_pubblicazione: parseInt(e.target.value) || null})}\n              />\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Versione\"\n                value={standardForm.versione}\n                onChange={(e) => setStandardForm({...standardForm, versione: e.target.value})}\n              />\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <FormControlLabel\n                control={\n                  <Switch\n                    checked={standardForm.attivo}\n                    onChange={(e) => setStandardForm({...standardForm, attivo: e.target.checked})}\n                  />\n                }\n                label=\"Standard Attivo\"\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"URL Documento\"\n                value={standardForm.url_documento}\n                onChange={(e) => setStandardForm({...standardForm, url_documento: e.target.value})}\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setStandardDialog(false)}>Annulla</Button>\n          <Button onClick={handleSaveStandard} variant=\"contained\">\n            {editingStandard ? 'Aggiorna' : 'Crea'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default TipologieCaviManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,gBAAgB,EAChBC,MAAM,QACD,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,OAAOC,oBAAoB,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5D,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+D,KAAK,EAAEC,QAAQ,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiE,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAACmE,SAAS,EAAEC,YAAY,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqE,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuE,aAAa,EAAEC,gBAAgB,CAAC,GAAGxE,QAAQ,CAAC;IACjDyE,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE,EAAE;IACfC,kBAAkB,EAAE,IAAI;IACxBC,OAAO,EAAE,CAAC;IACVC,sBAAsB,EAAE,CAAC;IACzBC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACiF,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqF,cAAc,EAAEC,iBAAiB,CAAC,GAAGtF,QAAQ,CAAC;IACnDuF,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE,EAAE;IAClBC,QAAQ,EAAE,EAAE;IACZC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/F,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAM,CAACgG,QAAQ,EAAEC,WAAW,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkG,cAAc,EAAEC,iBAAiB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoG,YAAY,EAAEC,eAAe,CAAC,GAAGrG,QAAQ,CAAC;IAC/CsG,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE,EAAE;IAClB7B,WAAW,EAAE,EAAE;IACf8B,kBAAkB,EAAE,IAAI;IACxBC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBb,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACc,eAAe,EAAEC,kBAAkB,CAAC,GAAG5G,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAAC6G,SAAS,EAAEC,YAAY,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+G,cAAc,EAAEC,iBAAiB,CAAC,GAAGhH,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACiH,aAAa,EAAEC,gBAAgB,CAAC,GAAGlH,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACmH,iBAAiB,CAAC,GAAGnH,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACoH,eAAe,EAAEC,kBAAkB,CAAC,GAAGrH,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsH,aAAa,EAAEC,gBAAgB,CAAC,GAAGvH,QAAQ,CAAC;IACjDwH,eAAe,EAAE,EAAE;IACnBC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE,IAAI;IAClBC,sBAAsB,EAAE,IAAI;IAC5BC,iBAAiB,EAAE,EAAE;IACrBC,oBAAoB,EAAE,EAAE;IACxBC,wBAAwB,EAAE,EAAE;IAC5BC,mBAAmB,EAAE,IAAI;IACzBC,cAAc,EAAE,IAAI;IACpBC,uBAAuB,EAAE,IAAI;IAC7BC,uBAAuB,EAAE,IAAI;IAC7BC,uBAAuB,EAAE,IAAI;IAC7BC,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,KAAK;IACtBC,iBAAiB,EAAE,KAAK;IACxBC,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE,KAAK;IACpBC,kBAAkB,EAAE,EAAE;IACtBC,YAAY,EAAE,EAAE;IAChBC,gCAAgC,EAAE,IAAI;IACtCC,WAAW,EAAE,IAAI;IACjBjD,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM,CAACkD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/I,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACgJ,eAAe,EAAEC,kBAAkB,CAAC,GAAGjJ,QAAQ,CAAC;IACrDkJ,YAAY,EAAE,IAAI;IAClBC,aAAa,EAAE,IAAI;IACnBN,WAAW,EAAE,IAAI;IACjBO,WAAW,EAAE;EACf,CAAC,CAAC;EAEFnJ,SAAS,CAAC,MAAM;IACdoJ,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC1F,QAAQ,CAAC,CAAC;EAEd,MAAM0F,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3BvF,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,QAAQH,QAAQ;QACd,KAAK,CAAC;UACJ,MAAM2F,aAAa,CAAC,CAAC;UACrB;QACF,KAAK,CAAC;UACJ,MAAMC,cAAc,CAAC,CAAC;UACtB;QACF,KAAK,CAAC;UACJ,MAAMC,YAAY,CAAC,CAAC;UACpB;QACF,KAAK,CAAC;UACJ,MAAMC,aAAa,CAAC,CAAC;UACrB;MACJ;IACF,CAAC,CAAC,OAAO1F,KAAK,EAAE;MACdC,QAAQ,CAAC,mCAAmC,GAAGD,KAAK,CAAC2F,OAAO,CAAC;IAC/D,CAAC,SAAS;MACR5F,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAMK,IAAI,GAAG,MAAMrG,oBAAoB,CAACsG,YAAY,CAAC,CAAC;IACtDxF,YAAY,CAACuF,IAAI,CAAC;EACpB,CAAC;EAED,MAAMJ,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,MAAMI,IAAI,GAAG,MAAMrG,oBAAoB,CAACuG,aAAa,CAAC,CAAC;IACvD3E,aAAa,CAACyE,IAAI,CAAC;EACrB,CAAC;EAED,MAAMH,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMG,IAAI,GAAG,MAAMrG,oBAAoB,CAACwG,WAAW,CAAC,CAAC;IACrD7D,WAAW,CAAC0D,IAAI,CAAC;EACnB,CAAC;EAED,MAAMF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,MAAMM,MAAM,GAAG;MACbC,IAAI,EAAE/C,aAAa;MACnBgD,SAAS,EAAE9C,iBAAiB;MAC5B,GAAG6B;IACL,CAAC;IACD,MAAMW,IAAI,GAAG,MAAMrG,oBAAoB,CAAC4G,YAAY,CAACH,MAAM,CAAC;IAC5DjD,YAAY,CAAC6C,IAAI,CAAC9C,SAAS,CAAC;IAC5BG,iBAAiB,CAAC2C,IAAI,CAACQ,WAAW,CAAC;EACrC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3C1G,WAAW,CAAC0G,QAAQ,CAAC;IACrBtG,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;EAChB,CAAC;;EAED;EACA,MAAMqG,qBAAqB,GAAGA,CAAA,KAAM;IAClC/F,gBAAgB,CAAC;MACfC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,EAAE;MACfC,kBAAkB,EAAE,IAAI;MACxBC,OAAO,EAAE,CAAC;MACVC,sBAAsB,EAAE,CAAC;MACzBC,MAAM,EAAE;IACV,CAAC,CAAC;IACFE,mBAAmB,CAAC,IAAI,CAAC;IACzBV,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMkG,mBAAmB,GAAIC,SAAS,IAAK;IACzCjG,gBAAgB,CAACiG,SAAS,CAAC;IAC3BzF,mBAAmB,CAACyF,SAAS,CAAC9C,YAAY,CAAC;IAC3CrD,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMoG,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,IAAI3F,gBAAgB,EAAE;QACpB,MAAMzB,oBAAoB,CAACqH,eAAe,CAAC5F,gBAAgB,EAAER,aAAa,CAAC;QAC3EL,UAAU,CAAC,mCAAmC,CAAC;MACjD,CAAC,MAAM;QACL,MAAMZ,oBAAoB,CAACsH,eAAe,CAACrG,aAAa,CAAC;QACzDL,UAAU,CAAC,+BAA+B,CAAC;MAC7C;MACAI,kBAAkB,CAAC,KAAK,CAAC;MACzB,MAAMgF,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOvF,KAAK,EAAE;MACdC,QAAQ,CAAC,0BAA0B,GAAGD,KAAK,CAAC2F,OAAO,CAAC;IACtD;EACF,CAAC;EAED,MAAMmB,qBAAqB,GAAG,MAAOC,EAAE,IAAK;IAC1C,IAAIC,MAAM,CAACC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MACrE,IAAI;QACF,MAAM1H,oBAAoB,CAAC2H,eAAe,CAACH,EAAE,CAAC;QAC9C5G,UAAU,CAAC,kCAAkC,CAAC;QAC9C,MAAMoF,aAAa,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOvF,KAAK,EAAE;QACdC,QAAQ,CAAC,6BAA6B,GAAGD,KAAK,CAAC2F,OAAO,CAAC;MACzD;IACF;EACF,CAAC;;EAED;EACA,MAAMwB,sBAAsB,GAAGA,CAAA,KAAM;IACnC5F,iBAAiB,CAAC;MAChBC,eAAe,EAAE,EAAE;MACnBC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE,EAAE;MAClBC,QAAQ,EAAE,EAAE;MACZC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE;IACV,CAAC,CAAC;IACFE,oBAAoB,CAAC,IAAI,CAAC;IAC1BX,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM+F,oBAAoB,GAAIC,UAAU,IAAK;IAC3C9F,iBAAiB,CAAC8F,UAAU,CAAC;IAC7BrF,oBAAoB,CAACqF,UAAU,CAAC1D,aAAa,CAAC;IAC9CtC,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMiG,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,IAAIvF,iBAAiB,EAAE;QACrB,MAAMxC,oBAAoB,CAACgI,gBAAgB,CAACxF,iBAAiB,EAAET,cAAc,CAAC;QAC9EnB,UAAU,CAAC,oCAAoC,CAAC;MAClD,CAAC,MAAM;QACL,MAAMZ,oBAAoB,CAACiI,gBAAgB,CAAClG,cAAc,CAAC;QAC3DnB,UAAU,CAAC,gCAAgC,CAAC;MAC9C;MACAkB,mBAAmB,CAAC,KAAK,CAAC;MAC1B,MAAMmE,cAAc,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOxF,KAAK,EAAE;MACdC,QAAQ,CAAC,0BAA0B,GAAGD,KAAK,CAAC2F,OAAO,CAAC;IACtD;EACF,CAAC;EAED,MAAM8B,sBAAsB,GAAG,MAAOV,EAAE,IAAK;IAC3C,IAAIC,MAAM,CAACC,OAAO,CAAC,kDAAkD,CAAC,EAAE;MACtE,IAAI;QACF,MAAM1H,oBAAoB,CAACmI,gBAAgB,CAACX,EAAE,CAAC;QAC/C5G,UAAU,CAAC,mCAAmC,CAAC;QAC/C,MAAMqF,cAAc,CAAC,CAAC;MACxB,CAAC,CAAC,OAAOxF,KAAK,EAAE;QACdC,QAAQ,CAAC,6BAA6B,GAAGD,KAAK,CAAC2F,OAAO,CAAC;MACzD;IACF;EACF,CAAC;;EAED;EACA,MAAMgC,oBAAoB,GAAGA,CAAA,KAAM;IACjCrF,eAAe,CAAC;MACdC,aAAa,EAAE,EAAE;MACjBC,cAAc,EAAE,EAAE;MAClB7B,WAAW,EAAE,EAAE;MACf8B,kBAAkB,EAAE,IAAI;MACxBC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBb,MAAM,EAAE;IACV,CAAC,CAAC;IACFe,kBAAkB,CAAC,IAAI,CAAC;IACxBT,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMwF,kBAAkB,GAAIC,GAAG,IAAK;IAClCvF,eAAe,CAACuF,GAAG,CAAC;IACpBhF,kBAAkB,CAACgF,GAAG,CAACC,WAAW,CAAC;IACnC1F,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAM2F,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,IAAInF,eAAe,EAAE;QACnB,MAAMrD,oBAAoB,CAACyI,cAAc,CAACpF,eAAe,EAAEP,YAAY,CAAC;QACxElC,UAAU,CAAC,kCAAkC,CAAC;MAChD,CAAC,MAAM;QACL,MAAMZ,oBAAoB,CAAC0I,cAAc,CAAC5F,YAAY,CAAC;QACvDlC,UAAU,CAAC,8BAA8B,CAAC;MAC5C;MACAiC,iBAAiB,CAAC,KAAK,CAAC;MACxB,MAAMqD,YAAY,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOzF,KAAK,EAAE;MACdC,QAAQ,CAAC,0BAA0B,GAAGD,KAAK,CAAC2F,OAAO,CAAC;IACtD;EACF,CAAC;EAED,MAAMuC,oBAAoB,GAAG,MAAOnB,EAAE,IAAK;IACzC,IAAIC,MAAM,CAACC,OAAO,CAAC,gDAAgD,CAAC,EAAE;MACpE,IAAI;QACF,MAAM1H,oBAAoB,CAAC4I,cAAc,CAACpB,EAAE,CAAC;QAC7C5G,UAAU,CAAC,iCAAiC,CAAC;QAC7C,MAAMsF,YAAY,CAAC,CAAC;MACtB,CAAC,CAAC,OAAOzF,KAAK,EAAE;QACdC,QAAQ,CAAC,6BAA6B,GAAGD,KAAK,CAAC2F,OAAO,CAAC;MACzD;IACF;EACF,CAAC;EAED,MAAMyC,kBAAkB,GAAGA,CAAA,kBACzB3I,OAAA,CAACtD,GAAG;IAAAkM,QAAA,gBACF5I,OAAA,CAACtD,GAAG;MAACmM,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACnE5I,OAAA,CAAClD,UAAU;QAACmM,OAAO,EAAC,IAAI;QAAAL,QAAA,EAAC;MAAuB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7DrJ,OAAA,CAACjD,MAAM;QACLkM,OAAO,EAAC,WAAW;QACnBK,SAAS,eAAEtJ,OAAA,CAACnB,OAAO;UAAAqK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBE,OAAO,EAAExC,qBAAsB;QAAA6B,QAAA,EAChC;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELhJ,OAAO,gBACNL,OAAA,CAAC9B,gBAAgB;MAAAgL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEpBrJ,OAAA,CAAC7C,cAAc;MAACqM,SAAS,EAAE7M,KAAM;MAAAiM,QAAA,eAC/B5I,OAAA,CAAChD,KAAK;QAAA4L,QAAA,gBACJ5I,OAAA,CAAC5C,SAAS;UAAAwL,QAAA,eACR5I,OAAA,CAAC3C,QAAQ;YAAAuL,QAAA,gBACP5I,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClCrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9BrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAe;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtCrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7BrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZrJ,OAAA,CAAC/C,SAAS;UAAA2L,QAAA,EACPjI,SAAS,CAAC8I,GAAG,CAAExC,SAAS;YAAA,IAAAyC,qBAAA;YAAA,oBACvB1J,OAAA,CAAC3C,QAAQ;cAAAuL,QAAA,gBACP5I,OAAA,CAAC9C,SAAS;gBAAA0L,QAAA,EAAE3B,SAAS,CAAChG;cAAc;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjDrJ,OAAA,CAAC9C,SAAS;gBAAA0L,QAAA,EAAE3B,SAAS,CAAC/F;cAAW;gBAAAgI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9CrJ,OAAA,CAAC9C,SAAS;gBAAA0L,QAAA,EAAE3B,SAAS,CAAC7F;cAAO;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1CrJ,OAAA,CAAC9C,SAAS;gBAAA0L,QAAA,EACP,EAAAc,qBAAA,GAAAzC,SAAS,CAAC0C,eAAe,cAAAD,qBAAA,uBAAzBA,qBAAA,CAA2BzI,cAAc,KAAI;cAAG;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACZrJ,OAAA,CAAC9C,SAAS;gBAAA0L,QAAA,eACR5I,OAAA,CAACjC,IAAI;kBACH6L,KAAK,EAAE3C,SAAS,CAAC3F,MAAM,GAAG,IAAI,GAAG,IAAK;kBACtCuI,KAAK,EAAE5C,SAAS,CAAC3F,MAAM,GAAG,SAAS,GAAG,SAAU;kBAChDwI,IAAI,EAAC;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZrJ,OAAA,CAAC9C,SAAS;gBAAA0L,QAAA,gBACR5I,OAAA,CAAChC,UAAU;kBAACuL,OAAO,EAAEA,CAAA,KAAMvC,mBAAmB,CAACC,SAAS,CAAE;kBAAA2B,QAAA,eACxD5I,OAAA,CAACjB,QAAQ;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACbrJ,OAAA,CAAChC,UAAU;kBAACuL,OAAO,EAAEA,CAAA,KAAMlC,qBAAqB,CAACJ,SAAS,CAAC9C,YAAY,CAAE;kBAAAyE,QAAA,eACvE5I,OAAA,CAACf,UAAU;oBAAAiK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GArBCpC,SAAS,CAAC9C,YAAY;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsB3B,CAAC;UAAA,CACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACjB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMU,mBAAmB,GAAGA,CAAA,kBAC1B/J,OAAA,CAACtD,GAAG;IAAAkM,QAAA,gBACF5I,OAAA,CAACtD,GAAG;MAACmM,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACnE5I,OAAA,CAAClD,UAAU;QAACmM,OAAO,EAAC,IAAI;QAAAL,QAAA,EAAC;MAAwB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC9DrJ,OAAA,CAACjD,MAAM;QACLkM,OAAO,EAAC,WAAW;QACnBK,SAAS,eAAEtJ,OAAA,CAACnB,OAAO;UAAAqK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBE,OAAO,EAAE7B,sBAAuB;QAAAkB,QAAA,EACjC;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELhJ,OAAO,gBACNL,OAAA,CAAC9B,gBAAgB;MAAAgL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEpBrJ,OAAA,CAAC7C,cAAc;MAACqM,SAAS,EAAE7M,KAAM;MAAAiM,QAAA,eAC/B5I,OAAA,CAAChD,KAAK;QAAA4L,QAAA,gBACJ5I,OAAA,CAAC5C,SAAS;UAAAwL,QAAA,eACR5I,OAAA,CAAC3C,QAAQ;YAAAuL,QAAA,gBACP5I,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5BrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5BrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/BrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7BrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZrJ,OAAA,CAAC/C,SAAS;UAAA2L,QAAA,EACPnH,UAAU,CAACgI,GAAG,CAAE7B,UAAU,iBACzB5H,OAAA,CAAC3C,QAAQ;YAAAuL,QAAA,gBACP5I,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAEhB,UAAU,CAAC7F;YAAe;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnDrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAEhB,UAAU,CAAC5F;YAAK;cAAAkH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACzCrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAEhB,UAAU,CAAC1F;YAAc;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClDrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAEhB,UAAU,CAACzF;YAAQ;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5CrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,eACR5I,OAAA,CAACjC,IAAI;gBACH6L,KAAK,EAAEhC,UAAU,CAACvF,MAAM,GAAG,IAAI,GAAG,IAAK;gBACvCwH,KAAK,EAAEjC,UAAU,CAACvF,MAAM,GAAG,SAAS,GAAG,SAAU;gBACjDyH,IAAI,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,gBACR5I,OAAA,CAAChC,UAAU;gBAACuL,OAAO,EAAEA,CAAA,KAAM5B,oBAAoB,CAACC,UAAU,CAAE;gBAAAgB,QAAA,eAC1D5I,OAAA,CAACjB,QAAQ;kBAAAmK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACbrJ,OAAA,CAAChC,UAAU;gBAACuL,OAAO,EAAEA,CAAA,KAAMvB,sBAAsB,CAACJ,UAAU,CAAC1D,aAAa,CAAE;gBAAA0E,QAAA,eAC1E5I,OAAA,CAACf,UAAU;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAnBCzB,UAAU,CAAC1D,aAAa;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoB7B,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACjB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMW,iBAAiB,GAAGA,CAAA,kBACxBhK,OAAA,CAACtD,GAAG;IAAAkM,QAAA,gBACF5I,OAAA,CAACtD,GAAG;MAACmM,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACnE5I,OAAA,CAAClD,UAAU;QAACmM,OAAO,EAAC,IAAI;QAAAL,QAAA,EAAC;MAAsB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC5DrJ,OAAA,CAACjD,MAAM;QACLkM,OAAO,EAAC,WAAW;QACnBK,SAAS,eAAEtJ,OAAA,CAACnB,OAAO;UAAAqK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBE,OAAO,EAAErB,oBAAqB;QAAAU,QAAA,EAC/B;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELhJ,OAAO,gBACNL,OAAA,CAAC9B,gBAAgB;MAAAgL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEpBrJ,OAAA,CAAC7C,cAAc;MAACqM,SAAS,EAAE7M,KAAM;MAAAiM,QAAA,eAC/B5I,OAAA,CAAChD,KAAK;QAAA4L,QAAA,gBACJ5I,OAAA,CAAC5C,SAAS;UAAAwL,QAAA,eACR5I,OAAA,CAAC3C,QAAQ;YAAAuL,QAAA,gBACP5I,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAc;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrCrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/BrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7BrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZrJ,OAAA,CAAC/C,SAAS;UAAA2L,QAAA,EACPpG,QAAQ,CAACiH,GAAG,CAAErB,GAAG,iBAChBpI,OAAA,CAAC3C,QAAQ;YAAAuL,QAAA,gBACP5I,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAER,GAAG,CAACtF;YAAa;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1CrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAER,GAAG,CAACrF;YAAc;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3CrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAER,GAAG,CAACpF;YAAkB;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/CrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAER,GAAG,CAACnF;YAAQ;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrCrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,eACR5I,OAAA,CAACjC,IAAI;gBACH6L,KAAK,EAAExB,GAAG,CAAC/F,MAAM,GAAG,IAAI,GAAG,IAAK;gBAChCwH,KAAK,EAAEzB,GAAG,CAAC/F,MAAM,GAAG,SAAS,GAAG,SAAU;gBAC1CyH,IAAI,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,gBACR5I,OAAA,CAAChC,UAAU;gBAACuL,OAAO,EAAEA,CAAA,KAAMpB,kBAAkB,CAACC,GAAG,CAAE;gBAAAQ,QAAA,eACjD5I,OAAA,CAACjB,QAAQ;kBAAAmK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACbrJ,OAAA,CAAChC,UAAU;gBAACuL,OAAO,EAAEA,CAAA,KAAMd,oBAAoB,CAACL,GAAG,CAACC,WAAW,CAAE;gBAAAO,QAAA,eAC/D5I,OAAA,CAACf,UAAU;kBAAAiK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA,GAnBCjB,GAAG,CAACC,WAAW;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBpB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACjB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMY,kBAAkB,GAAGA,CAAA,kBACzBjK,OAAA,CAACtD,GAAG;IAAAkM,QAAA,gBACF5I,OAAA,CAACtD,GAAG;MAACmM,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACnE5I,OAAA,CAAClD,UAAU;QAACmM,OAAO,EAAC,IAAI;QAAAL,QAAA,EAAC;MAAuB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7DrJ,OAAA,CAACjD,MAAM;QACLkM,OAAO,EAAC,WAAW;QACnBK,SAAS,eAAEtJ,OAAA,CAACnB,OAAO;UAAAqK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBE,OAAO,EAAEA,CAAA,KAAM1F,kBAAkB,CAAC,IAAI,CAAE;QAAA+E,QAAA,EACzC;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNrJ,OAAA,CAACrD,KAAK;MAACkM,EAAE,EAAE;QAAEqB,CAAC,EAAE,CAAC;QAAElB,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACzB5I,OAAA,CAAC7B,IAAI;QAACgM,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAxB,QAAA,gBACzB5I,OAAA,CAAC7B,IAAI;UAACkM,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvB5I,OAAA,CAACtC,SAAS;YACR8M,SAAS;YACTZ,KAAK,EAAC,SAAS;YACfa,KAAK,EAAEjF,eAAe,CAACI,WAAY;YACnC8E,QAAQ,EAAGC,CAAC,IAAKlF,kBAAkB,CAAC;cAAC,GAAGD,eAAe;cAAEI,WAAW,EAAE+E,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YACvFI,UAAU,EAAE;cACVC,cAAc,eAAE9K,OAAA,CAACH,UAAU;gBAAAqJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC/B;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;UAACkM,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvB5I,OAAA,CAACrC,WAAW;YAAC6M,SAAS;YAAA5B,QAAA,gBACpB5I,OAAA,CAACpC,UAAU;cAAAgL,QAAA,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClCrJ,OAAA,CAACnC,MAAM;cACL4M,KAAK,EAAEjF,eAAe,CAACE,YAAY,IAAI,EAAG;cAC1CgF,QAAQ,EAAGC,CAAC,IAAKlF,kBAAkB,CAAC;gBAAC,GAAGD,eAAe;gBAAEE,YAAY,EAAEiF,CAAC,CAACC,MAAM,CAACH,KAAK,IAAI;cAAI,CAAC,CAAE;cAAA7B,QAAA,gBAEhG5I,OAAA,CAAClC,QAAQ;gBAAC2M,KAAK,EAAC,EAAE;gBAAA7B,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EAClC1I,SAAS,CAAC8I,GAAG,CAAEsB,GAAG,iBACjB/K,OAAA,CAAClC,QAAQ;gBAAwB2M,KAAK,EAAEM,GAAG,CAAC5G,YAAa;gBAAAyE,QAAA,EACtDmC,GAAG,CAAC9J;cAAc,GADN8J,GAAG,CAAC5G,YAAY;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAErB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;UAACkM,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvB5I,OAAA,CAACrC,WAAW;YAAC6M,SAAS;YAAA5B,QAAA,gBACpB5I,OAAA,CAACpC,UAAU;cAAAgL,QAAA,EAAC;YAAU;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnCrJ,OAAA,CAACnC,MAAM;cACL4M,KAAK,EAAEjF,eAAe,CAACG,aAAa,IAAI,EAAG;cAC3C+E,QAAQ,EAAGC,CAAC,IAAKlF,kBAAkB,CAAC;gBAAC,GAAGD,eAAe;gBAAEG,aAAa,EAAEgF,CAAC,CAACC,MAAM,CAACH,KAAK,IAAI;cAAI,CAAC,CAAE;cAAA7B,QAAA,gBAEjG5I,OAAA,CAAClC,QAAQ;gBAAC2M,KAAK,EAAC,EAAE;gBAAA7B,QAAA,EAAC;cAAK;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EAClC5H,UAAU,CAACgI,GAAG,CAAEuB,IAAI,iBACnBhL,OAAA,CAAClC,QAAQ;gBAA0B2M,KAAK,EAAEO,IAAI,CAAC9G,aAAc;gBAAA0E,QAAA,EAC1DoC,IAAI,CAACjJ;cAAe,GADRiJ,IAAI,CAAC9G,aAAa;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEvB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;UAACkM,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA3B,QAAA,eACvB5I,OAAA,CAACjD,MAAM;YACLkM,OAAO,EAAC,UAAU;YAClBuB,SAAS;YACTjB,OAAO,EAAEtD,aAAc;YACvB4C,EAAE,EAAE;cAAEoC,MAAM,EAAE;YAAO,CAAE;YAAArC,QAAA,EACxB;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEPhJ,OAAO,gBACNL,OAAA,CAAC9B,gBAAgB;MAAAgL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAEpBrJ,OAAA,CAAC7C,cAAc;MAACqM,SAAS,EAAE7M,KAAM;MAAAiM,QAAA,eAC/B5I,OAAA,CAAChD,KAAK;QAAA4L,QAAA,gBACJ5I,OAAA,CAAC5C,SAAS;UAAAwL,QAAA,eACR5I,OAAA,CAAC3C,QAAQ;YAAAuL,QAAA,gBACP5I,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAe;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtCrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAgB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACvCrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAU;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACjCrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChCrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClCrJ,OAAA,CAAC9C,SAAS;cAAA0L,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZrJ,OAAA,CAAC/C,SAAS;UAAA2L,QAAA,EACPvF,SAAS,CAACoG,GAAG,CAAEyB,SAAS;YAAA,IAAAC,qBAAA,EAAAC,oBAAA;YAAA,oBACvBpL,OAAA,CAAC3C,QAAQ;cAAAuL,QAAA,gBACP5I,OAAA,CAAC9C,SAAS;gBAAA0L,QAAA,EAAEsC,SAAS,CAAClH;cAAe;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClDrJ,OAAA,CAAC9C,SAAS;gBAAA0L,QAAA,EAAEsC,SAAS,CAACjH;cAAgB;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnDrJ,OAAA,CAAC9C,SAAS;gBAAA0L,QAAA,EAAE,EAAAuC,qBAAA,GAAAD,SAAS,CAACtD,UAAU,cAAAuD,qBAAA,uBAApBA,qBAAA,CAAsBpJ,eAAe,KAAI;cAAG;gBAAAmH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrErJ,OAAA,CAAC9C,SAAS;gBAAA0L,QAAA,EAAE,EAAAwC,oBAAA,GAAAF,SAAS,CAACjE,SAAS,cAAAmE,oBAAA,uBAAnBA,oBAAA,CAAqBnK,cAAc,KAAI;cAAG;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnErJ,OAAA,CAAC9C,SAAS;gBAAA0L,QAAA,eACR5I,OAAA,CAACjC,IAAI;kBACH6L,KAAK,EAAEsB,SAAS,CAAC7F,WAAW,GAAG,IAAI,GAAG,IAAK;kBAC3CwE,KAAK,EAAEqB,SAAS,CAAC7F,WAAW,GAAG,SAAS,GAAG,SAAU;kBACrDyE,IAAI,EAAC;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZrJ,OAAA,CAAC9C,SAAS;gBAAA0L,QAAA,gBACR5I,OAAA,CAAChC,UAAU;kBAACuL,OAAO,EAAEA,CAAA,KAAM8B,mBAAmB,CAACH,SAAS,CAAE;kBAAAtC,QAAA,eACxD5I,OAAA,CAACjB,QAAQ;oBAAAmK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACbrJ,OAAA,CAAChC,UAAU;kBAACuL,OAAO,EAAEA,CAAA,KAAM+B,qBAAqB,CAACJ,SAAS,CAACK,YAAY,CAAE;kBAAA3C,QAAA,eACvE5I,OAAA,CAACf,UAAU;oBAAAiK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAnBC6B,SAAS,CAACK,YAAY;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoB3B,CAAC;UAAA,CACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACjB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,oBACErJ,OAAA,CAACtD,GAAG;IAACmM,EAAE,EAAE;MAAE2C,KAAK,EAAE;IAAO,CAAE;IAAA5C,QAAA,GACxBrI,KAAK,iBACJP,OAAA,CAAC/B,KAAK;MAACwN,QAAQ,EAAC,OAAO;MAAC5C,EAAE,EAAE;QAAEG,EAAE,EAAE;MAAE,CAAE;MAAC0C,OAAO,EAAEA,CAAA,KAAMlL,QAAQ,CAAC,EAAE,CAAE;MAAAoI,QAAA,EAChErI;IAAK;MAAA2I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA5I,OAAO,iBACNT,OAAA,CAAC/B,KAAK;MAACwN,QAAQ,EAAC,SAAS;MAAC5C,EAAE,EAAE;QAAEG,EAAE,EAAE;MAAE,CAAE;MAAC0C,OAAO,EAAEA,CAAA,KAAMhL,UAAU,CAAC,EAAE,CAAE;MAAAkI,QAAA,EACpEnI;IAAO;MAAAyI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAEDrJ,OAAA,CAACrD,KAAK;MAACkM,EAAE,EAAE;QAAE2C,KAAK,EAAE,MAAM;QAAExC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAClC5I,OAAA,CAACpD,IAAI;QACH6N,KAAK,EAAEtK,QAAS;QAChBuK,QAAQ,EAAE9D,eAAgB;QAC1B+E,cAAc,EAAC,SAAS;QACxBC,SAAS,EAAC,SAAS;QACnB3C,OAAO,EAAC,YAAY;QACpB4C,aAAa,EAAC,MAAM;QAAAjD,QAAA,gBAEpB5I,OAAA,CAACnD,GAAG;UAACiP,IAAI,eAAE9L,OAAA,CAACX,YAAY;YAAA6J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACO,KAAK,EAAC;QAAW;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDrJ,OAAA,CAACnD,GAAG;UAACiP,IAAI,eAAE9L,OAAA,CAACT,YAAY;YAAA2J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACO,KAAK,EAAC;QAAY;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClDrJ,OAAA,CAACnD,GAAG;UAACiP,IAAI,eAAE9L,OAAA,CAACP,cAAc;YAAAyJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACO,KAAK,EAAC;QAAU;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClDrJ,OAAA,CAACnD,GAAG;UAACiP,IAAI,eAAE9L,OAAA,CAACL,SAAS;YAAAuJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACO,KAAK,EAAC;QAAW;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAERrJ,OAAA,CAACtD,GAAG;MAACmM,EAAE,EAAE;QAAEkD,EAAE,EAAE;MAAE,CAAE;MAAAnD,QAAA,GAChBzI,QAAQ,KAAK,CAAC,IAAIwI,kBAAkB,CAAC,CAAC,EACtCxI,QAAQ,KAAK,CAAC,IAAI4J,mBAAmB,CAAC,CAAC,EACvC5J,QAAQ,KAAK,CAAC,IAAI6J,iBAAiB,CAAC,CAAC,EACrC7J,QAAQ,KAAK,CAAC,IAAI8J,kBAAkB,CAAC,CAAC;IAAA;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eAGNrJ,OAAA,CAAC1C,MAAM;MAAC0O,IAAI,EAAEnL,eAAgB;MAAC6K,OAAO,EAAEA,CAAA,KAAM5K,kBAAkB,CAAC,KAAK,CAAE;MAACmL,QAAQ,EAAC,IAAI;MAACzB,SAAS;MAAA5B,QAAA,gBAC9F5I,OAAA,CAACzC,WAAW;QAAAqL,QAAA,EACTrH,gBAAgB,GAAG,oBAAoB,GAAG;MAAiB;QAAA2H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eACdrJ,OAAA,CAACxC,aAAa;QAAAoL,QAAA,eACZ5I,OAAA,CAAC7B,IAAI;UAACgM,SAAS;UAACC,OAAO,EAAE,CAAE;UAACvB,EAAE,EAAE;YAAEkD,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,gBACxC5I,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,gBAAgB;cACtBa,KAAK,EAAE1J,aAAa,CAACE,cAAe;cACpCyJ,QAAQ,EAAGC,CAAC,IAAK3J,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEE,cAAc,EAAE0J,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACtFyB,QAAQ;YAAA;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5I,OAAA,CAACrC,WAAW;cAAC6M,SAAS;cAAA5B,QAAA,gBACpB5I,OAAA,CAACpC,UAAU;gBAAAgL,QAAA,EAAC;cAAe;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxCrJ,OAAA,CAACnC,MAAM;gBACL4M,KAAK,EAAE1J,aAAa,CAACI,kBAAkB,IAAI,EAAG;gBAC9CuJ,QAAQ,EAAGC,CAAC,IAAK3J,gBAAgB,CAAC;kBAAC,GAAGD,aAAa;kBAAEI,kBAAkB,EAAEwJ,CAAC,CAACC,MAAM,CAACH,KAAK,IAAI;gBAAI,CAAC,CAAE;gBAAA7B,QAAA,gBAElG5I,OAAA,CAAClC,QAAQ;kBAAC2M,KAAK,EAAC,EAAE;kBAAA7B,QAAA,EAAC;gBAA8B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAC3D1I,SAAS,CAACwL,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChL,OAAO,GAAG,CAAC,CAAC,CAACqI,GAAG,CAAEsB,GAAG,iBAC5C/K,OAAA,CAAClC,QAAQ;kBAAwB2M,KAAK,EAAEM,GAAG,CAAC5G,YAAa;kBAAAyE,QAAA,EACtDmC,GAAG,CAAC9J;gBAAc,GADN8J,GAAG,CAAC5G,YAAY;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAErB,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,aAAa;cACnBa,KAAK,EAAE1J,aAAa,CAACG,WAAY;cACjCwJ,QAAQ,EAAGC,CAAC,IAAK3J,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEG,WAAW,EAAEyJ,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACnF4B,SAAS;cACTC,IAAI,EAAE;YAAE;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,wBAAwB;cAC9B2C,IAAI,EAAC,QAAQ;cACb9B,KAAK,EAAE1J,aAAa,CAACM,sBAAuB;cAC5CqJ,QAAQ,EAAGC,CAAC,IAAK3J,gBAAgB,CAAC;gBAAC,GAAGD,aAAa;gBAAEM,sBAAsB,EAAEmL,QAAQ,CAAC7B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI;cAAC,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5I,OAAA,CAACtB,gBAAgB;cACf+N,OAAO,eACLzM,OAAA,CAACrB,MAAM;gBACL+N,OAAO,EAAE3L,aAAa,CAACO,MAAO;gBAC9BoJ,QAAQ,EAAGC,CAAC,IAAK3J,gBAAgB,CAAC;kBAAC,GAAGD,aAAa;kBAAEO,MAAM,EAAEqJ,CAAC,CAACC,MAAM,CAAC8B;gBAAO,CAAC;cAAE;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CACF;cACDO,KAAK,EAAC;YAAkB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBrJ,OAAA,CAACvC,aAAa;QAAAmL,QAAA,gBACZ5I,OAAA,CAACjD,MAAM;UAACwM,OAAO,EAAEA,CAAA,KAAMzI,kBAAkB,CAAC,KAAK,CAAE;UAAA8H,QAAA,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClErJ,OAAA,CAACjD,MAAM;UAACwM,OAAO,EAAErC,mBAAoB;UAAC+B,OAAO,EAAC,WAAW;UAAAL,QAAA,EACtDrH,gBAAgB,GAAG,UAAU,GAAG;QAAM;UAAA2H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTrJ,OAAA,CAAC1C,MAAM;MAAC0O,IAAI,EAAErK,gBAAiB;MAAC+J,OAAO,EAAEA,CAAA,KAAM9J,mBAAmB,CAAC,KAAK,CAAE;MAACqK,QAAQ,EAAC,IAAI;MAACzB,SAAS;MAAA5B,QAAA,gBAChG5I,OAAA,CAACzC,WAAW;QAAAqL,QAAA,EACTtG,iBAAiB,GAAG,qBAAqB,GAAG;MAAkB;QAAA4G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACdrJ,OAAA,CAACxC,aAAa;QAAAoL,QAAA,eACZ5I,OAAA,CAAC7B,IAAI;UAACgM,SAAS;UAACC,OAAO,EAAE,CAAE;UAACvB,EAAE,EAAE;YAAEkD,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,gBACxC5I,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,iBAAiB;cACvBa,KAAK,EAAE5I,cAAc,CAACE,eAAgB;cACtC2I,QAAQ,EAAGC,CAAC,IAAK7I,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEE,eAAe,EAAE4I,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACzFyB,QAAQ;YAAA;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,OAAO;cACba,KAAK,EAAE5I,cAAc,CAACG,KAAM;cAC5B0I,QAAQ,EAAGC,CAAC,IAAK7I,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEG,KAAK,EAAE2I,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,gBAAgB;cACtB2C,IAAI,EAAC,OAAO;cACZ9B,KAAK,EAAE5I,cAAc,CAACK,cAAe;cACrCwI,QAAQ,EAAGC,CAAC,IAAK7I,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEK,cAAc,EAAEyI,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,UAAU;cAChBa,KAAK,EAAE5I,cAAc,CAACM,QAAS;cAC/BuI,QAAQ,EAAGC,CAAC,IAAK7I,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEM,QAAQ,EAAEwI,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,UAAU;cAChBa,KAAK,EAAE5I,cAAc,CAACI,QAAS;cAC/ByI,QAAQ,EAAGC,CAAC,IAAK7I,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEI,QAAQ,EAAE0I,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,MAAM;cACZa,KAAK,EAAE5I,cAAc,CAACO,IAAK;cAC3BsI,QAAQ,EAAGC,CAAC,IAAK7I,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEO,IAAI,EAAEuI,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAC9E4B,SAAS;cACTC,IAAI,EAAE;YAAE;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChB5I,OAAA,CAACtB,gBAAgB;cACf+N,OAAO,eACLzM,OAAA,CAACrB,MAAM;gBACL+N,OAAO,EAAE7K,cAAc,CAACQ,MAAO;gBAC/BqI,QAAQ,EAAGC,CAAC,IAAK7I,iBAAiB,CAAC;kBAAC,GAAGD,cAAc;kBAAEQ,MAAM,EAAEsI,CAAC,CAACC,MAAM,CAAC8B;gBAAO,CAAC;cAAE;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CACF;cACDO,KAAK,EAAC;YAAmB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBrJ,OAAA,CAACvC,aAAa;QAAAmL,QAAA,gBACZ5I,OAAA,CAACjD,MAAM;UAACwM,OAAO,EAAEA,CAAA,KAAM3H,mBAAmB,CAAC,KAAK,CAAE;UAAAgH,QAAA,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACnErJ,OAAA,CAACjD,MAAM;UAACwM,OAAO,EAAE1B,oBAAqB;UAACoB,OAAO,EAAC,WAAW;UAAAL,QAAA,EACvDtG,iBAAiB,GAAG,UAAU,GAAG;QAAM;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTrJ,OAAA,CAAC1C,MAAM;MAAC0O,IAAI,EAAEtJ,cAAe;MAACgJ,OAAO,EAAEA,CAAA,KAAM/I,iBAAiB,CAAC,KAAK,CAAE;MAACsJ,QAAQ,EAAC,IAAI;MAACzB,SAAS;MAAA5B,QAAA,gBAC5F5I,OAAA,CAACzC,WAAW;QAAAqL,QAAA,EACTzF,eAAe,GAAG,mBAAmB,GAAG;MAAgB;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACdrJ,OAAA,CAACxC,aAAa;QAAAoL,QAAA,eACZ5I,OAAA,CAAC7B,IAAI;UAACgM,SAAS;UAACC,OAAO,EAAE,CAAE;UAACvB,EAAE,EAAE;YAAEkD,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,gBACxC5I,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,eAAe;cACrBa,KAAK,EAAE7H,YAAY,CAACE,aAAc;cAClC4H,QAAQ,EAAGC,CAAC,IAAK9H,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEE,aAAa,EAAE6H,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACnFyB,QAAQ;YAAA;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,gBAAgB;cACtBa,KAAK,EAAE7H,YAAY,CAACG,cAAe;cACnC2H,QAAQ,EAAGC,CAAC,IAAK9H,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEG,cAAc,EAAE4H,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,aAAa;cACnBa,KAAK,EAAE7H,YAAY,CAAC1B,WAAY;cAChCwJ,QAAQ,EAAGC,CAAC,IAAK9H,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAE1B,WAAW,EAAEyJ,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACjF4B,SAAS;cACTC,IAAI,EAAE;YAAE;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,oBAAoB;cAC1B2C,IAAI,EAAC,QAAQ;cACb9B,KAAK,EAAE7H,YAAY,CAACI,kBAAkB,IAAI,EAAG;cAC7C0H,QAAQ,EAAGC,CAAC,IAAK9H,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEI,kBAAkB,EAAEwJ,QAAQ,CAAC7B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI;cAAI,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,UAAU;cAChBa,KAAK,EAAE7H,YAAY,CAACK,QAAS;cAC7ByH,QAAQ,EAAGC,CAAC,IAAK9H,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEK,QAAQ,EAAE0H,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACvB5I,OAAA,CAACtB,gBAAgB;cACf+N,OAAO,eACLzM,OAAA,CAACrB,MAAM;gBACL+N,OAAO,EAAE9J,YAAY,CAACP,MAAO;gBAC7BqI,QAAQ,EAAGC,CAAC,IAAK9H,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEP,MAAM,EAAEsI,CAAC,CAACC,MAAM,CAAC8B;gBAAO,CAAC;cAAE;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CACF;cACDO,KAAK,EAAC;YAAiB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrJ,OAAA,CAAC7B,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA1B,QAAA,eAChB5I,OAAA,CAACtC,SAAS;cACR8M,SAAS;cACTZ,KAAK,EAAC,eAAe;cACrBa,KAAK,EAAE7H,YAAY,CAACM,aAAc;cAClCwH,QAAQ,EAAGC,CAAC,IAAK9H,eAAe,CAAC;gBAAC,GAAGD,YAAY;gBAAEM,aAAa,EAAEyH,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC;YAAE;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBrJ,OAAA,CAACvC,aAAa;QAAAmL,QAAA,gBACZ5I,OAAA,CAACjD,MAAM;UAACwM,OAAO,EAAEA,CAAA,KAAM5G,iBAAiB,CAAC,KAAK,CAAE;UAAAiG,QAAA,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACjErJ,OAAA,CAACjD,MAAM;UAACwM,OAAO,EAAEjB,kBAAmB;UAACW,OAAO,EAAC,WAAW;UAAAL,QAAA,EACrDzF,eAAe,GAAG,UAAU,GAAG;QAAM;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACnJ,EAAA,CA71BID,oBAAoB;AAAA0M,EAAA,GAApB1M,oBAAoB;AA+1B1B,eAAeA,oBAAoB;AAAC,IAAA0M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}