{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\";\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Box, CssBaseline } from '@mui/material';\nimport { useAuth } from '../context/AuthContext';\nimport TopNavbar from '../components/TopNavbar';\nimport HomePage from './HomePage';\nimport AdminPage from './AdminPage';\nimport UserPage from './UserPage';\nimport CaviPage from './CaviPage';\nimport UserExpirationChecker from '../components/admin/UserExpirationChecker';\n\n// Importa le nuove pagine per i cavi\nimport VisualizzaCaviPage from './cavi/VisualizzaCaviPage';\nimport ParcoCaviPage from './cavi/ParcoCaviPage';\nimport ReportCaviPageNew from './cavi/ReportCaviPageNew';\nimport CertificazioneCaviPage from './cavi/CertificazioneCaviPage';\nimport CertificazioniPageDebug from './CertificazioniPageDebug';\nimport GestioneComandeePage from './cavi/GestioneComandeePage';\nimport TestCaviPage from './cavi/TestCaviPage';\nimport TestBobinePage from './TestBobinePage';\n\n// Importa le pagine per Parco Cavi\nimport VisualizzaBobinePage from './cavi/parco/VisualizzaBobinePage';\nimport CreaBobinaPage from './cavi/parco/CreaBobinaPage';\nimport ParcoCaviModificaBobinaPage from './cavi/parco/ModificaBobinaPage';\nimport EliminaBobinaPage from './cavi/parco/EliminaBobinaPage';\nimport StoricoUtilizzoPage from './cavi/parco/StoricoUtilizzoPage';\n\n// Importa le pagine per Posa e Collegamenti\nimport InserisciMetriPage from './cavi/posa/InserisciMetriPage';\nimport MetriPosatiSemplificatoPage from './cavi/posa/MetriPosatiSemplificatoPage';\nimport ModificaCavoPage from './cavi/posa/ModificaCavoPage';\nimport PosaCaviModificaBobinaPage from './cavi/posa/ModificaBobinaPage';\nimport CollegamentiPage from './cavi/posa/CollegamentiPage';\n\n// Importa la pagina per il cantiere specifico\nimport CantierePage from './cantieri/CantierePage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(UserExpirationChecker, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TopNavbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"main\",\n      sx: {\n        flexGrow: 1,\n        p: 3,\n        width: '100%',\n        backgroundColor: '#f5f5f5',\n        // Sfondo grigio chiaro per l'area principale\n        minHeight: 'calc(100vh - 40px)',\n        // Altezza minima per coprire l'intera viewport meno l'altezza della navbar\n        overflowX: 'hidden' // Previene scrollbar orizzontale\n      },\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(AdminPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri\",\n          element: /*#__PURE__*/_jsxDEV(UserPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri/:cantiereId\",\n          element: /*#__PURE__*/_jsxDEV(CantierePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 56\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri/:cantiereId/certificazioni\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioniPageDebug, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 71\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi\",\n          element: /*#__PURE__*/_jsxDEV(CaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/visualizza\",\n          element: /*#__PURE__*/_jsxDEV(VisualizzaCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 51\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard/cavi/visualizza\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco\",\n          element: /*#__PURE__*/_jsxDEV(ParcoCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/excel\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard/cavi/visualizza\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/report\",\n          element: /*#__PURE__*/_jsxDEV(ReportCaviPageNew, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/:cantiereId/report\",\n          element: /*#__PURE__*/_jsxDEV(ReportCaviPageNew, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 59\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 55\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/visualizza\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 66\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/filtra\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 62\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/crea\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 60\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/modifica\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 64\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/dettagli\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 64\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/pdf\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 59\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/elimina\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 63\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/strumenti\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 65\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/comande\",\n          element: /*#__PURE__*/_jsxDEV(GestioneComandeePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 48\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/test\",\n          element: /*#__PURE__*/_jsxDEV(TestCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/test-bobine/:cantiereId\",\n          element: /*#__PURE__*/_jsxDEV(TestBobinePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 64\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/visualizza\",\n          element: /*#__PURE__*/_jsxDEV(VisualizzaBobinePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 57\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/crea\",\n          element: /*#__PURE__*/_jsxDEV(CreaBobinaPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 51\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/modifica\",\n          element: /*#__PURE__*/_jsxDEV(ParcoCaviModificaBobinaPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 55\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/elimina\",\n          element: /*#__PURE__*/_jsxDEV(EliminaBobinaPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 54\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/storico\",\n          element: /*#__PURE__*/_jsxDEV(StoricoUtilizzoPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 54\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/inserisci-metri\",\n          element: /*#__PURE__*/_jsxDEV(InserisciMetriPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 61\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/metri-posati-semplificato\",\n          element: /*#__PURE__*/_jsxDEV(MetriPosatiSemplificatoPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 71\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/modifica-cavo\",\n          element: /*#__PURE__*/_jsxDEV(ModificaCavoPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 59\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri/:cantiereId/cavi/posa/modifica-cavo\",\n          element: /*#__PURE__*/_jsxDEV(ModificaCavoPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 80\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/modifica-bobina\",\n          element: /*#__PURE__*/_jsxDEV(PosaCaviModificaBobinaPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 61\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri/:cantiereId/cavi/posa/modifica-bobina/:cavoId?\",\n          element: /*#__PURE__*/_jsxDEV(PosaCaviModificaBobinaPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 91\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/collegamenti\",\n          element: /*#__PURE__*/_jsxDEV(CollegamentiPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 58\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri/:cantiereId/cavi/posa/collegamenti\",\n          element: /*#__PURE__*/_jsxDEV(CollegamentiPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 79\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "Box", "CssBaseline", "useAuth", "TopNavbar", "HomePage", "AdminPage", "UserPage", "CaviPage", "UserExpirationChecker", "VisualizzaCaviPage", "ParcoCaviPage", "ReportCaviPageNew", "CertificazioneCaviPage", "CertificazioniPageDebug", "GestioneComandeePage", "TestCaviPage", "TestBobinePage", "VisualizzaBobinePage", "CreaBobinaPage", "ParcoCaviModificaBobinaPage", "EliminaBobinaPage", "StoricoUtilizzoPage", "InserisciMetriPage", "MetriPosatiSemplificatoPage", "ModificaCavoPage", "PosaCaviModificaBobinaPage", "CollegamentiPage", "CantierePage", "jsxDEV", "_jsxDEV", "Dashboard", "sx", "display", "flexDirection", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "flexGrow", "p", "width", "backgroundColor", "minHeight", "overflowX", "path", "element", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Box, CssBaseline } from '@mui/material';\n\nimport { useAuth } from '../context/AuthContext';\nimport TopNavbar from '../components/TopNavbar';\nimport HomePage from './HomePage';\nimport AdminPage from './AdminPage';\nimport UserPage from './UserPage';\nimport CaviPage from './CaviPage';\nimport UserExpirationChecker from '../components/admin/UserExpirationChecker';\n\n// Importa le nuove pagine per i cavi\nimport VisualizzaCaviPage from './cavi/VisualizzaCaviPage';\nimport ParcoCaviPage from './cavi/ParcoCaviPage';\n\nimport ReportCaviPageNew from './cavi/ReportCaviPageNew';\nimport CertificazioneCaviPage from './cavi/CertificazioneCaviPage';\nimport CertificazioniPageDebug from './CertificazioniPageDebug';\nimport GestioneComandeePage from './cavi/GestioneComandeePage';\nimport TestCaviPage from './cavi/TestCaviPage';\nimport TestBobinePage from './TestBobinePage';\n\n// Importa le pagine per Parco Cavi\nimport VisualizzaBobinePage from './cavi/parco/VisualizzaBobinePage';\nimport CreaBobinaPage from './cavi/parco/CreaBobinaPage';\nimport ParcoCaviModificaBobinaPage from './cavi/parco/ModificaBobinaPage';\nimport EliminaBobinaPage from './cavi/parco/EliminaBobinaPage';\nimport StoricoUtilizzoPage from './cavi/parco/StoricoUtilizzoPage';\n\n// Importa le pagine per Posa e Collegamenti\nimport InserisciMetriPage from './cavi/posa/InserisciMetriPage';\nimport MetriPosatiSemplificatoPage from './cavi/posa/MetriPosatiSemplificatoPage';\nimport ModificaCavoPage from './cavi/posa/ModificaCavoPage';\n\nimport PosaCaviModificaBobinaPage from './cavi/posa/ModificaBobinaPage';\nimport CollegamentiPage from './cavi/posa/CollegamentiPage';\n\n\n// Importa la pagina per il cantiere specifico\nimport CantierePage from './cantieri/CantierePage';\n\nconst Dashboard = () => {\n\n  return (\n    <Box sx={{ display: 'flex', flexDirection: 'column' }}>\n      {/* Componente invisibile che verifica gli utenti scaduti */}\n      <UserExpirationChecker />\n      <CssBaseline />\n      <TopNavbar />\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: 3,\n          width: '100%',\n          backgroundColor: '#f5f5f5', // Sfondo grigio chiaro per l'area principale\n          minHeight: 'calc(100vh - 40px)', // Altezza minima per coprire l'intera viewport meno l'altezza della navbar\n          overflowX: 'hidden' // Previene scrollbar orizzontale\n        }}\n      >\n        <Routes>\n          <Route path=\"/\" element={<HomePage />} />\n          <Route path=\"/admin\" element={<AdminPage />} />\n          <Route path=\"/cantieri\" element={<UserPage />} />\n          <Route path=\"/cantieri/:cantiereId\" element={<CantierePage />} />\n          <Route path=\"/cantieri/:cantiereId/certificazioni\" element={<CertificazioniPageDebug />} />\n\n          {/* Route per la gestione cavi */}\n          <Route path=\"/cavi\" element={<CaviPage />} />\n          <Route path=\"/cavi/visualizza\" element={<VisualizzaCaviPage />} />\n          <Route path=\"/cavi/posa\" element={<Navigate to=\"/dashboard/cavi/visualizza\" replace />} />\n          <Route path=\"/cavi/parco\" element={<ParcoCaviPage />} />\n          <Route path=\"/cavi/excel\" element={<Navigate to=\"/dashboard/cavi/visualizza\" replace />} />\n          <Route path=\"/cavi/report\" element={<ReportCaviPageNew />} />\n          <Route path=\"/cavi/:cantiereId/report\" element={<ReportCaviPageNew />} />\n\n          <Route path=\"/cavi/certificazione\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/visualizza\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/filtra\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/crea\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/modifica\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/dettagli\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/pdf\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/elimina\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/strumenti\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/comande\" element={<GestioneComandeePage />} />\n          <Route path=\"/cavi/test\" element={<TestCaviPage />} />\n\n          {/* Route per la pagina di test delle bobine */}\n          <Route path=\"/cavi/test-bobine/:cantiereId\" element={<TestBobinePage />} />\n\n          {/* Route per Parco Cavi */}\n          <Route path=\"/cavi/parco/visualizza\" element={<VisualizzaBobinePage />} />\n          <Route path=\"/cavi/parco/crea\" element={<CreaBobinaPage />} />\n          <Route path=\"/cavi/parco/modifica\" element={<ParcoCaviModificaBobinaPage />} />\n          <Route path=\"/cavi/parco/elimina\" element={<EliminaBobinaPage />} />\n          <Route path=\"/cavi/parco/storico\" element={<StoricoUtilizzoPage />} />\n\n          {/* Route per Posa e Collegamenti */}\n          <Route path=\"/cavi/posa/inserisci-metri\" element={<InserisciMetriPage />} />\n          <Route path=\"/cavi/posa/metri-posati-semplificato\" element={<MetriPosatiSemplificatoPage />} />\n          {/* Modifica cavo ora ha una pagina dedicata */}\n          <Route path=\"/cavi/posa/modifica-cavo\" element={<ModificaCavoPage />} />\n          <Route path=\"/cantieri/:cantiereId/cavi/posa/modifica-cavo\" element={<ModificaCavoPage />} />\n\n          <Route path=\"/cavi/posa/modifica-bobina\" element={<PosaCaviModificaBobinaPage />} />\n          <Route path=\"/cantieri/:cantiereId/cavi/posa/modifica-bobina/:cavoId?\" element={<PosaCaviModificaBobinaPage />} />\n          <Route path=\"/cavi/posa/collegamenti\" element={<CollegamentiPage />} />\n          <Route path=\"/cantieri/:cantiereId/cavi/posa/collegamenti\" element={<CollegamentiPage />} />\n\n          {/* Altre route verranno aggiunte man mano che vengono implementate */}\n        </Routes>\n      </Box>\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,GAAG,EAAEC,WAAW,QAAQ,eAAe;AAEhD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,qBAAqB,MAAM,2CAA2C;;AAE7E;AACA,OAAOC,kBAAkB,MAAM,2BAA2B;AAC1D,OAAOC,aAAa,MAAM,sBAAsB;AAEhD,OAAOC,iBAAiB,MAAM,0BAA0B;AACxD,OAAOC,sBAAsB,MAAM,+BAA+B;AAClE,OAAOC,uBAAuB,MAAM,2BAA2B;AAC/D,OAAOC,oBAAoB,MAAM,6BAA6B;AAC9D,OAAOC,YAAY,MAAM,qBAAqB;AAC9C,OAAOC,cAAc,MAAM,kBAAkB;;AAE7C;AACA,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,2BAA2B,MAAM,iCAAiC;AACzE,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,mBAAmB,MAAM,kCAAkC;;AAElE;AACA,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,2BAA2B,MAAM,yCAAyC;AACjF,OAAOC,gBAAgB,MAAM,8BAA8B;AAE3D,OAAOC,0BAA0B,MAAM,gCAAgC;AACvE,OAAOC,gBAAgB,MAAM,8BAA8B;;AAG3D;AACA,OAAOC,YAAY,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAEtB,oBACED,OAAA,CAAC7B,GAAG;IAAC+B,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEpDL,OAAA,CAACrB,qBAAqB;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzBT,OAAA,CAAC5B,WAAW;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfT,OAAA,CAAC1B,SAAS;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACbT,OAAA,CAAC7B,GAAG;MACFuC,SAAS,EAAC,MAAM;MAChBR,EAAE,EAAE;QACFS,QAAQ,EAAE,CAAC;QACXC,CAAC,EAAE,CAAC;QACJC,KAAK,EAAE,MAAM;QACbC,eAAe,EAAE,SAAS;QAAE;QAC5BC,SAAS,EAAE,oBAAoB;QAAE;QACjCC,SAAS,EAAE,QAAQ,CAAC;MACtB,CAAE;MAAAX,QAAA,eAEFL,OAAA,CAAChC,MAAM;QAAAqC,QAAA,gBACLL,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,GAAG;UAACC,OAAO,eAAElB,OAAA,CAACzB,QAAQ;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAElB,OAAA,CAACxB,SAAS;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,WAAW;UAACC,OAAO,eAAElB,OAAA,CAACvB,QAAQ;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,uBAAuB;UAACC,OAAO,eAAElB,OAAA,CAACF,YAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,sCAAsC;UAACC,OAAO,eAAElB,OAAA,CAAChB,uBAAuB;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG3FT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,OAAO;UAACC,OAAO,eAAElB,OAAA,CAACtB,QAAQ;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,kBAAkB;UAACC,OAAO,eAAElB,OAAA,CAACpB,kBAAkB;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,YAAY;UAACC,OAAO,eAAElB,OAAA,CAAC9B,QAAQ;YAACiD,EAAE,EAAC,4BAA4B;YAACC,OAAO;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1FT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,aAAa;UAACC,OAAO,eAAElB,OAAA,CAACnB,aAAa;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,aAAa;UAACC,OAAO,eAAElB,OAAA,CAAC9B,QAAQ;YAACiD,EAAE,EAAC,4BAA4B;YAACC,OAAO;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3FT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,cAAc;UAACC,OAAO,eAAElB,OAAA,CAAClB,iBAAiB;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,0BAA0B;UAACC,OAAO,eAAElB,OAAA,CAAClB,iBAAiB;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEzET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,sBAAsB;UAACC,OAAO,eAAElB,OAAA,CAACjB,sBAAsB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1ET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,iCAAiC;UAACC,OAAO,eAAElB,OAAA,CAACjB,sBAAsB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrFT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,6BAA6B;UAACC,OAAO,eAAElB,OAAA,CAACjB,sBAAsB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjFT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,2BAA2B;UAACC,OAAO,eAAElB,OAAA,CAACjB,sBAAsB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/ET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,+BAA+B;UAACC,OAAO,eAAElB,OAAA,CAACjB,sBAAsB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnFT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,+BAA+B;UAACC,OAAO,eAAElB,OAAA,CAACjB,sBAAsB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnFT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,0BAA0B;UAACC,OAAO,eAAElB,OAAA,CAACjB,sBAAsB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9ET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,8BAA8B;UAACC,OAAO,eAAElB,OAAA,CAACjB,sBAAsB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClFT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,gCAAgC;UAACC,OAAO,eAAElB,OAAA,CAACjB,sBAAsB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpFT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,eAAe;UAACC,OAAO,eAAElB,OAAA,CAACf,oBAAoB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,YAAY;UAACC,OAAO,eAAElB,OAAA,CAACd,YAAY;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGtDT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,+BAA+B;UAACC,OAAO,eAAElB,OAAA,CAACb,cAAc;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG3ET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,wBAAwB;UAACC,OAAO,eAAElB,OAAA,CAACZ,oBAAoB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1ET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,kBAAkB;UAACC,OAAO,eAAElB,OAAA,CAACX,cAAc;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,sBAAsB;UAACC,OAAO,eAAElB,OAAA,CAACV,2BAA2B;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/ET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,qBAAqB;UAACC,OAAO,eAAElB,OAAA,CAACT,iBAAiB;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,qBAAqB;UAACC,OAAO,eAAElB,OAAA,CAACR,mBAAmB;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGtET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,4BAA4B;UAACC,OAAO,eAAElB,OAAA,CAACP,kBAAkB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5ET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,sCAAsC;UAACC,OAAO,eAAElB,OAAA,CAACN,2BAA2B;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE/FT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,0BAA0B;UAACC,OAAO,eAAElB,OAAA,CAACL,gBAAgB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,+CAA+C;UAACC,OAAO,eAAElB,OAAA,CAACL,gBAAgB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE7FT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,4BAA4B;UAACC,OAAO,eAAElB,OAAA,CAACJ,0BAA0B;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpFT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,0DAA0D;UAACC,OAAO,eAAElB,OAAA,CAACJ,0BAA0B;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClHT,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,yBAAyB;UAACC,OAAO,eAAElB,OAAA,CAACH,gBAAgB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvET,OAAA,CAAC/B,KAAK;UAACgD,IAAI,EAAC,8CAA8C;UAACC,OAAO,eAAElB,OAAA,CAACH,gBAAgB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGtF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACY,EAAA,GA1EIpB,SAAS;AA4Ef,eAAeA,SAAS;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}