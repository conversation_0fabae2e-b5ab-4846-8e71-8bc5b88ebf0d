{"ast": null, "code": "import { createIsAfterIgnoreDatePart } from \"../internals/utils/time-utils.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\n\n/**\n * Validation props used by the Time Picker, Time Field and Clock components.\n */\n\n/**\n * Validation props as received by the validateTime method.\n */\n\n/**\n * Name of the props that should be defaulted before being passed to the validateTime method.\n */\n\nexport const validateTime = ({\n  adapter,\n  value,\n  timezone,\n  props\n}) => {\n  if (value === null) {\n    return null;\n  }\n  const {\n    minTime,\n    maxTime,\n    minutesStep,\n    shouldDisableTime,\n    disableIgnoringDatePartForTimeValidation = false,\n    disablePast,\n    disableFuture\n  } = props;\n  const now = adapter.utils.date(undefined, timezone);\n  const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, adapter.utils);\n  switch (true) {\n    case !adapter.utils.isValid(value):\n      return 'invalidDate';\n    case Boolean(minTime && isAfter(minTime, value)):\n      return 'minTime';\n    case Boolean(maxTime && isAfter(value, maxTime)):\n      return 'maxTime';\n    case Boolean(disableFuture && adapter.utils.isAfter(value, now)):\n      return 'disableFuture';\n    case Boolean(disablePast && adapter.utils.isBefore(value, now)):\n      return 'disablePast';\n    case Boolean(shouldDisableTime && shouldDisableTime(value, 'hours')):\n      return 'shouldDisableTime-hours';\n    case Boolean(shouldDisableTime && shouldDisableTime(value, 'minutes')):\n      return 'shouldDisableTime-minutes';\n    case Boolean(shouldDisableTime && shouldDisableTime(value, 'seconds')):\n      return 'shouldDisableTime-seconds';\n    case Boolean(minutesStep && adapter.utils.getMinutes(value) % minutesStep !== 0):\n      return 'minutesStep';\n    default:\n      return null;\n  }\n};\nvalidateTime.valueManager = singleItemValueManager;", "map": {"version": 3, "names": ["createIsAfterIgnoreDatePart", "singleItemValueManager", "validateTime", "adapter", "value", "timezone", "props", "minTime", "maxTime", "minutesStep", "shouldDisableTime", "disableIgnoringDatePartForTimeValidation", "disablePast", "disableFuture", "now", "utils", "date", "undefined", "isAfter", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "isBefore", "getMinutes", "valueManager"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/validation/validateTime.js"], "sourcesContent": ["import { createIsAfterIgnoreDatePart } from \"../internals/utils/time-utils.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\n\n/**\n * Validation props used by the Time Picker, Time Field and Clock components.\n */\n\n/**\n * Validation props as received by the validateTime method.\n */\n\n/**\n * Name of the props that should be defaulted before being passed to the validateTime method.\n */\n\nexport const validateTime = ({\n  adapter,\n  value,\n  timezone,\n  props\n}) => {\n  if (value === null) {\n    return null;\n  }\n  const {\n    minTime,\n    maxTime,\n    minutesStep,\n    shouldDisableTime,\n    disableIgnoringDatePartForTimeValidation = false,\n    disablePast,\n    disableFuture\n  } = props;\n  const now = adapter.utils.date(undefined, timezone);\n  const isAfter = createIsAfterIgnoreDatePart(disableIgnoringDatePartForTimeValidation, adapter.utils);\n  switch (true) {\n    case !adapter.utils.isValid(value):\n      return 'invalidDate';\n    case Boolean(minTime && isAfter(minTime, value)):\n      return 'minTime';\n    case Boolean(maxTime && isAfter(value, maxTime)):\n      return 'maxTime';\n    case Boolean(disableFuture && adapter.utils.isAfter(value, now)):\n      return 'disableFuture';\n    case Boolean(disablePast && adapter.utils.isBefore(value, now)):\n      return 'disablePast';\n    case Boolean(shouldDisableTime && shouldDisableTime(value, 'hours')):\n      return 'shouldDisableTime-hours';\n    case Boolean(shouldDisableTime && shouldDisableTime(value, 'minutes')):\n      return 'shouldDisableTime-minutes';\n    case Boolean(shouldDisableTime && shouldDisableTime(value, 'seconds')):\n      return 'shouldDisableTime-seconds';\n    case Boolean(minutesStep && adapter.utils.getMinutes(value) % minutesStep !== 0):\n      return 'minutesStep';\n    default:\n      return null;\n  }\n};\nvalidateTime.valueManager = singleItemValueManager;"], "mappings": "AAAA,SAASA,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,sBAAsB,QAAQ,qCAAqC;;AAE5E;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,OAAO,MAAMC,YAAY,GAAGA,CAAC;EAC3BC,OAAO;EACPC,KAAK;EACLC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,IAAIF,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO,IAAI;EACb;EACA,MAAM;IACJG,OAAO;IACPC,OAAO;IACPC,WAAW;IACXC,iBAAiB;IACjBC,wCAAwC,GAAG,KAAK;IAChDC,WAAW;IACXC;EACF,CAAC,GAAGP,KAAK;EACT,MAAMQ,GAAG,GAAGX,OAAO,CAACY,KAAK,CAACC,IAAI,CAACC,SAAS,EAAEZ,QAAQ,CAAC;EACnD,MAAMa,OAAO,GAAGlB,2BAA2B,CAACW,wCAAwC,EAAER,OAAO,CAACY,KAAK,CAAC;EACpG,QAAQ,IAAI;IACV,KAAK,CAACZ,OAAO,CAACY,KAAK,CAACI,OAAO,CAACf,KAAK,CAAC;MAChC,OAAO,aAAa;IACtB,KAAKgB,OAAO,CAACb,OAAO,IAAIW,OAAO,CAACX,OAAO,EAAEH,KAAK,CAAC,CAAC;MAC9C,OAAO,SAAS;IAClB,KAAKgB,OAAO,CAACZ,OAAO,IAAIU,OAAO,CAACd,KAAK,EAAEI,OAAO,CAAC,CAAC;MAC9C,OAAO,SAAS;IAClB,KAAKY,OAAO,CAACP,aAAa,IAAIV,OAAO,CAACY,KAAK,CAACG,OAAO,CAACd,KAAK,EAAEU,GAAG,CAAC,CAAC;MAC9D,OAAO,eAAe;IACxB,KAAKM,OAAO,CAACR,WAAW,IAAIT,OAAO,CAACY,KAAK,CAACM,QAAQ,CAACjB,KAAK,EAAEU,GAAG,CAAC,CAAC;MAC7D,OAAO,aAAa;IACtB,KAAKM,OAAO,CAACV,iBAAiB,IAAIA,iBAAiB,CAACN,KAAK,EAAE,OAAO,CAAC,CAAC;MAClE,OAAO,yBAAyB;IAClC,KAAKgB,OAAO,CAACV,iBAAiB,IAAIA,iBAAiB,CAACN,KAAK,EAAE,SAAS,CAAC,CAAC;MACpE,OAAO,2BAA2B;IACpC,KAAKgB,OAAO,CAACV,iBAAiB,IAAIA,iBAAiB,CAACN,KAAK,EAAE,SAAS,CAAC,CAAC;MACpE,OAAO,2BAA2B;IACpC,KAAKgB,OAAO,CAACX,WAAW,IAAIN,OAAO,CAACY,KAAK,CAACO,UAAU,CAAClB,KAAK,CAAC,GAAGK,WAAW,KAAK,CAAC,CAAC;MAC9E,OAAO,aAAa;IACtB;MACE,OAAO,IAAI;EACf;AACF,CAAC;AACDP,YAAY,CAACqB,YAAY,GAAGtB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}