{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"1초 미만\",\n    other: \"{{count}}초 미만\"\n  },\n  xSeconds: {\n    one: \"1초\",\n    other: \"{{count}}초\"\n  },\n  halfAMinute: \"30초\",\n  lessThanXMinutes: {\n    one: \"1분 미만\",\n    other: \"{{count}}분 미만\"\n  },\n  xMinutes: {\n    one: \"1분\",\n    other: \"{{count}}분\"\n  },\n  aboutXHours: {\n    one: \"약 1시간\",\n    other: \"약 {{count}}시간\"\n  },\n  xHours: {\n    one: \"1시간\",\n    other: \"{{count}}시간\"\n  },\n  xDays: {\n    one: \"1일\",\n    other: \"{{count}}일\"\n  },\n  aboutXWeeks: {\n    one: \"약 1주\",\n    other: \"약 {{count}}주\"\n  },\n  xWeeks: {\n    one: \"1주\",\n    other: \"{{count}}주\"\n  },\n  aboutXMonths: {\n    one: \"약 1개월\",\n    other: \"약 {{count}}개월\"\n  },\n  xMonths: {\n    one: \"1개월\",\n    other: \"{{count}}개월\"\n  },\n  aboutXYears: {\n    one: \"약 1년\",\n    other: \"약 {{count}}년\"\n  },\n  xYears: {\n    one: \"1년\",\n    other: \"{{count}}년\"\n  },\n  overXYears: {\n    one: \"1년 이상\",\n    other: \"{{count}}년 이상\"\n  },\n  almostXYears: {\n    one: \"거의 1년\",\n    other: \"거의 {{count}}년\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count.toString());\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" 후\";\n    } else {\n      return result + \" 전\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "toString", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/ko/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"1초 미만\",\n    other: \"{{count}}초 미만\",\n  },\n\n  xSeconds: {\n    one: \"1초\",\n    other: \"{{count}}초\",\n  },\n\n  halfAMinute: \"30초\",\n\n  lessThanXMinutes: {\n    one: \"1분 미만\",\n    other: \"{{count}}분 미만\",\n  },\n\n  xMinutes: {\n    one: \"1분\",\n    other: \"{{count}}분\",\n  },\n\n  aboutXHours: {\n    one: \"약 1시간\",\n    other: \"약 {{count}}시간\",\n  },\n\n  xHours: {\n    one: \"1시간\",\n    other: \"{{count}}시간\",\n  },\n\n  xDays: {\n    one: \"1일\",\n    other: \"{{count}}일\",\n  },\n\n  aboutXWeeks: {\n    one: \"약 1주\",\n    other: \"약 {{count}}주\",\n  },\n\n  xWeeks: {\n    one: \"1주\",\n    other: \"{{count}}주\",\n  },\n\n  aboutXMonths: {\n    one: \"약 1개월\",\n    other: \"약 {{count}}개월\",\n  },\n\n  xMonths: {\n    one: \"1개월\",\n    other: \"{{count}}개월\",\n  },\n\n  aboutXYears: {\n    one: \"약 1년\",\n    other: \"약 {{count}}년\",\n  },\n\n  xYears: {\n    one: \"1년\",\n    other: \"{{count}}년\",\n  },\n\n  overXYears: {\n    one: \"1년 이상\",\n    other: \"{{count}}년 이상\",\n  },\n\n  almostXYears: {\n    one: \"거의 1년\",\n    other: \"거의 {{count}}년\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count.toString());\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" 후\";\n    } else {\n      return result + \" 전\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,KAAK;EAElBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEJ,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC;EAClE;EAEA,IAAIJ,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,IAAI;IACtB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,IAAI;IACtB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}