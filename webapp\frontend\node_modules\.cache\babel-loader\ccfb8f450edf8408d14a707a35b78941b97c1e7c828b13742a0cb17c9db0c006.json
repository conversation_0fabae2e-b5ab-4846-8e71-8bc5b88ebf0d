{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\nconst eraValues = {\n  narrow: [\"НТӨ\", \"НТ\"],\n  abbreviated: [\"НТӨ\", \"НТ\"],\n  wide: [\"нийтийн тооллын өмнөх\", \"нийтийн тооллын\"]\n};\nconst quarterValues = {\n  narrow: [\"I\", \"II\", \"III\", \"IV\"],\n  abbreviated: [\"I улирал\", \"II улирал\", \"III улирал\", \"IV улирал\"],\n  wide: [\"1-р улирал\", \"2-р улирал\", \"3-р улирал\", \"4-р улирал\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"I\", \"II\", \"III\", \"IV\", \"V\", \"VI\", \"VII\", \"VIII\", \"IX\", \"X\", \"XI\", \"XII\"],\n  abbreviated: [\"1-р сар\", \"2-р сар\", \"3-р сар\", \"4-р сар\", \"5-р сар\", \"6-р сар\", \"7-р сар\", \"8-р сар\", \"9-р сар\", \"10-р сар\", \"11-р сар\", \"12-р сар\"],\n  wide: [\"Нэгдүгээр сар\", \"Хоёрдугаар сар\", \"Гуравдугаар сар\", \"Дөрөвдүгээр сар\", \"Тавдугаар сар\", \"Зургаадугаар сар\", \"Долоодугаар сар\", \"Наймдугаар сар\", \"Есдүгээр сар\", \"Аравдугаар сар\", \"Арваннэгдүгээр сар\", \"Арван хоёрдугаар сар\"]\n};\nconst formattingMonthValues = {\n  narrow: [\"I\", \"II\", \"III\", \"IV\", \"V\", \"VI\", \"VII\", \"VIII\", \"IX\", \"X\", \"XI\", \"XII\"],\n  abbreviated: [\"1-р сар\", \"2-р сар\", \"3-р сар\", \"4-р сар\", \"5-р сар\", \"6-р сар\", \"7-р сар\", \"8-р сар\", \"9-р сар\", \"10-р сар\", \"11-р сар\", \"12-р сар\"],\n  wide: [\"нэгдүгээр сар\", \"хоёрдугаар сар\", \"гуравдугаар сар\", \"дөрөвдүгээр сар\", \"тавдугаар сар\", \"зургаадугаар сар\", \"долоодугаар сар\", \"наймдугаар сар\", \"есдүгээр сар\", \"аравдугаар сар\", \"арваннэгдүгээр сар\", \"арван хоёрдугаар сар\"]\n};\nconst dayValues = {\n  narrow: [\"Н\", \"Д\", \"М\", \"Л\", \"П\", \"Б\", \"Б\"],\n  short: [\"Ня\", \"Да\", \"Мя\", \"Лх\", \"Пү\", \"Ба\", \"Бя\"],\n  abbreviated: [\"Ням\", \"Дав\", \"Мяг\", \"Лха\", \"Пүр\", \"Баа\", \"Бям\"],\n  wide: [\"Ням\", \"Даваа\", \"Мягмар\", \"Лхагва\", \"Пүрэв\", \"Баасан\", \"Бямба\"]\n};\nconst formattingDayValues = {\n  narrow: [\"Н\", \"Д\", \"М\", \"Л\", \"П\", \"Б\", \"Б\"],\n  short: [\"Ня\", \"Да\", \"Мя\", \"Лх\", \"Пү\", \"Ба\", \"Бя\"],\n  abbreviated: [\"Ням\", \"Дав\", \"Мяг\", \"Лха\", \"Пүр\", \"Баа\", \"Бям\"],\n  wide: [\"ням\", \"даваа\", \"мягмар\", \"лхагва\", \"пүрэв\", \"баасан\", \"бямба\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ү.ө.\",\n    pm: \"ү.х.\",\n    midnight: \"шөнө дунд\",\n    noon: \"үд дунд\",\n    morning: \"өглөө\",\n    afternoon: \"өдөр\",\n    evening: \"орой\",\n    night: \"шөнө\"\n  },\n  abbreviated: {\n    am: \"ү.ө.\",\n    pm: \"ү.х.\",\n    midnight: \"шөнө дунд\",\n    noon: \"үд дунд\",\n    morning: \"өглөө\",\n    afternoon: \"өдөр\",\n    evening: \"орой\",\n    night: \"шөнө\"\n  },\n  wide: {\n    am: \"ү.ө.\",\n    pm: \"ү.х.\",\n    midnight: \"шөнө дунд\",\n    noon: \"үд дунд\",\n    morning: \"өглөө\",\n    afternoon: \"өдөр\",\n    evening: \"орой\",\n    night: \"шөнө\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "formattingDayValues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/mn/_lib/localize.mjs"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\nconst eraValues = {\n  narrow: [\"НТӨ\", \"НТ\"],\n  abbreviated: [\"НТӨ\", \"НТ\"],\n  wide: [\"нийтийн тооллын өмнөх\", \"нийтийн тооллын\"],\n};\n\nconst quarterValues = {\n  narrow: [\"I\", \"II\", \"III\", \"IV\"],\n  abbreviated: [\"I улирал\", \"II улирал\", \"III улирал\", \"IV улирал\"],\n  wide: [\"1-р улирал\", \"2-р улирал\", \"3-р улирал\", \"4-р улирал\"],\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\n    \"I\",\n    \"II\",\n    \"III\",\n    \"IV\",\n    \"V\",\n    \"VI\",\n    \"VII\",\n    \"VIII\",\n    \"IX\",\n    \"X\",\n    \"XI\",\n    \"XII\",\n  ],\n\n  abbreviated: [\n    \"1-р сар\",\n    \"2-р сар\",\n    \"3-р сар\",\n    \"4-р сар\",\n    \"5-р сар\",\n    \"6-р сар\",\n    \"7-р сар\",\n    \"8-р сар\",\n    \"9-р сар\",\n    \"10-р сар\",\n    \"11-р сар\",\n    \"12-р сар\",\n  ],\n\n  wide: [\n    \"Нэгдүгээр сар\",\n    \"Хоёрдугаар сар\",\n    \"Гуравдугаар сар\",\n    \"Дөрөвдүгээр сар\",\n    \"Тавдугаар сар\",\n    \"Зургаадугаар сар\",\n    \"Долоодугаар сар\",\n    \"Наймдугаар сар\",\n    \"Есдүгээр сар\",\n    \"Аравдугаар сар\",\n    \"Арваннэгдүгээр сар\",\n    \"Арван хоёрдугаар сар\",\n  ],\n};\n\nconst formattingMonthValues = {\n  narrow: [\n    \"I\",\n    \"II\",\n    \"III\",\n    \"IV\",\n    \"V\",\n    \"VI\",\n    \"VII\",\n    \"VIII\",\n    \"IX\",\n    \"X\",\n    \"XI\",\n    \"XII\",\n  ],\n\n  abbreviated: [\n    \"1-р сар\",\n    \"2-р сар\",\n    \"3-р сар\",\n    \"4-р сар\",\n    \"5-р сар\",\n    \"6-р сар\",\n    \"7-р сар\",\n    \"8-р сар\",\n    \"9-р сар\",\n    \"10-р сар\",\n    \"11-р сар\",\n    \"12-р сар\",\n  ],\n\n  wide: [\n    \"нэгдүгээр сар\",\n    \"хоёрдугаар сар\",\n    \"гуравдугаар сар\",\n    \"дөрөвдүгээр сар\",\n    \"тавдугаар сар\",\n    \"зургаадугаар сар\",\n    \"долоодугаар сар\",\n    \"наймдугаар сар\",\n    \"есдүгээр сар\",\n    \"аравдугаар сар\",\n    \"арваннэгдүгээр сар\",\n    \"арван хоёрдугаар сар\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"Н\", \"Д\", \"М\", \"Л\", \"П\", \"Б\", \"Б\"],\n  short: [\"Ня\", \"Да\", \"Мя\", \"Лх\", \"Пү\", \"Ба\", \"Бя\"],\n  abbreviated: [\"Ням\", \"Дав\", \"Мяг\", \"Лха\", \"Пүр\", \"Баа\", \"Бям\"],\n  wide: [\"Ням\", \"Даваа\", \"Мягмар\", \"Лхагва\", \"Пүрэв\", \"Баасан\", \"Бямба\"],\n};\n\nconst formattingDayValues = {\n  narrow: [\"Н\", \"Д\", \"М\", \"Л\", \"П\", \"Б\", \"Б\"],\n  short: [\"Ня\", \"Да\", \"Мя\", \"Лх\", \"Пү\", \"Ба\", \"Бя\"],\n  abbreviated: [\"Ням\", \"Дав\", \"Мяг\", \"Лха\", \"Пүр\", \"Баа\", \"Бям\"],\n  wide: [\"ням\", \"даваа\", \"мягмар\", \"лхагва\", \"пүрэв\", \"баасан\", \"бямба\"],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ү.ө.\",\n    pm: \"ү.х.\",\n    midnight: \"шөнө дунд\",\n    noon: \"үд дунд\",\n    morning: \"өглөө\",\n    afternoon: \"өдөр\",\n    evening: \"орой\",\n    night: \"шөнө\",\n  },\n  abbreviated: {\n    am: \"ү.ө.\",\n    pm: \"ү.х.\",\n    midnight: \"шөнө дунд\",\n    noon: \"үд дунд\",\n    morning: \"өглөө\",\n    afternoon: \"өдөр\",\n    evening: \"орой\",\n    night: \"шөнө\",\n  },\n  wide: {\n    am: \"ү.ө.\",\n    pm: \"ү.х.\",\n    midnight: \"шөнө дунд\",\n    noon: \"үд дунд\",\n    morning: \"өглөө\",\n    afternoon: \"өдөр\",\n    evening: \"орой\",\n    night: \"шөнө\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAEhE,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;EACrBC,WAAW,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;EAC1BC,IAAI,EAAE,CAAC,uBAAuB,EAAE,iBAAiB;AACnD,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;EAChCC,WAAW,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;EACjEC,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY;AAC/D,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CACN,GAAG,EACH,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,KAAK,EACL,MAAM,EACN,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,KAAK,CACN;EAEDC,WAAW,EAAE,CACX,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,UAAU,EACV,UAAU,EACV,UAAU,CACX;EAEDC,IAAI,EAAE,CACJ,eAAe,EACf,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EACjB,eAAe,EACf,kBAAkB,EAClB,iBAAiB,EACjB,gBAAgB,EAChB,cAAc,EACd,gBAAgB,EAChB,oBAAoB,EACpB,sBAAsB;AAE1B,CAAC;AAED,MAAMG,qBAAqB,GAAG;EAC5BL,MAAM,EAAE,CACN,GAAG,EACH,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,KAAK,EACL,MAAM,EACN,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,KAAK,CACN;EAEDC,WAAW,EAAE,CACX,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,UAAU,EACV,UAAU,EACV,UAAU,CACX;EAEDC,IAAI,EAAE,CACJ,eAAe,EACf,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EACjB,eAAe,EACf,kBAAkB,EAClB,iBAAiB,EACjB,gBAAgB,EAChB,cAAc,EACd,gBAAgB,EAChB,oBAAoB,EACpB,sBAAsB;AAE1B,CAAC;AAED,MAAMI,SAAS,GAAG;EAChBN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO;AACvE,CAAC;AAED,MAAMM,mBAAmB,GAAG;EAC1BR,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO;AACvE,CAAC;AAED,MAAMO,eAAe,GAAG;EACtBT,MAAM,EAAE;IACNU,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDhB,WAAW,EAAE;IACXS,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,IAAI,EAAE;IACJQ,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMG,QAAQ,GAAG;EACtBJ,aAAa;EAEbK,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAExB,qBAAqB;IACvCyB,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EAEFC,GAAG,EAAEjC,eAAe,CAAC;IACnB0B,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAErB,mBAAmB;IACrCsB,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EAEFE,SAAS,EAAElC,eAAe,CAAC;IACzB0B,MAAM,EAAEf,eAAe;IACvBgB,YAAY,EAAE;EAChB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}