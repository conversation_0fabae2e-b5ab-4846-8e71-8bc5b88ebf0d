{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\common\\\\SelectedCantiereDisplay.js\";\nimport React from 'react';\nimport { Box, Typography, Chip } from '@mui/material';\nimport { Construction as ConstructionIcon } from '@mui/icons-material';\n\n/**\n * Componente che mostra il cantiere selezionato\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SelectedCantiereDisplay = () => {\n  // Recupera l'ID e il nome del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Se non c'è un cantiere selezionato, non mostrare nulla\n  if (!selectedCantiereId) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      alignItems: 'center',\n      mx: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      color: \"textSecondary\",\n      sx: {\n        mr: 1\n      },\n      children: \"Cantiere attivo:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n      icon: /*#__PURE__*/_jsxDEV(ConstructionIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 15\n      }, this),\n      label: selectedCantiereName || selectedCantiereId,\n      color: \"secondary\",\n      variant: \"outlined\",\n      size: \"small\",\n      sx: {\n        fontWeight: 'bold'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_c = SelectedCantiereDisplay;\nexport default SelectedCantiereDisplay;\nvar _c;\n$RefreshReg$(_c, \"SelectedCantiereDisplay\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Chip", "Construction", "ConstructionIcon", "jsxDEV", "_jsxDEV", "SelectedCantiereDisplay", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "sx", "display", "alignItems", "mx", "children", "variant", "color", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "fontSize", "label", "size", "fontWeight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/common/SelectedCantiereDisplay.js"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, Chip } from '@mui/material';\nimport { Construction as ConstructionIcon } from '@mui/icons-material';\n\n/**\n * Componente che mostra il cantiere selezionato\n */\nconst SelectedCantiereDisplay = () => {\n  // Recupera l'ID e il nome del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Se non c'è un cantiere selezionato, non mostrare nulla\n  if (!selectedCantiereId) {\n    return null;\n  }\n\n  return (\n    <Box sx={{ display: 'flex', alignItems: 'center', mx: 2 }}>\n      <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mr: 1 }}>\n        Cantiere attivo:\n      </Typography>\n      <Chip\n        icon={<ConstructionIcon fontSize=\"small\" />}\n        label={selectedCantiereName || selectedCantiereId}\n        color=\"secondary\"\n        variant=\"outlined\"\n        size=\"small\"\n        sx={{ fontWeight: 'bold' }}\n      />\n    </Box>\n  );\n};\n\nexport default SelectedCantiereDisplay;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,QAAQ,eAAe;AACrD,SAASC,YAAY,IAAIC,gBAAgB,QAAQ,qBAAqB;;AAEtE;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA;AAGA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EACpC;EACA,MAAMC,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEzE;EACA,IAAI,CAACF,kBAAkB,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,oBACEF,OAAA,CAACN,GAAG;IAACY,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE,QAAQ;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACxDV,OAAA,CAACL,UAAU;MAACgB,OAAO,EAAC,OAAO;MAACC,KAAK,EAAC,eAAe;MAACN,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EAAC;IAEjE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbjB,OAAA,CAACJ,IAAI;MACHsB,IAAI,eAAElB,OAAA,CAACF,gBAAgB;QAACqB,QAAQ,EAAC;MAAO;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC5CG,KAAK,EAAEf,oBAAoB,IAAIH,kBAAmB;MAClDU,KAAK,EAAC,WAAW;MACjBD,OAAO,EAAC,UAAU;MAClBU,IAAI,EAAC,OAAO;MACZf,EAAE,EAAE;QAAEgB,UAAU,EAAE;MAAO;IAAE;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACM,EAAA,GAzBItB,uBAAuB;AA2B7B,eAAeA,uBAAuB;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}