{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name setDayOfYear\n * @category Day Helpers\n * @summary Set the day of the year to the given date.\n *\n * @description\n * Set the day of the year to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param dayOfYear - The day of the year of the new date\n *\n * @returns The new date with the day of the year set\n *\n * @example\n * // Set the 2nd day of the year to 2 July 2014:\n * const result = setDayOfYear(new Date(2014, 6, 2), 2)\n * //=> Thu Jan 02 2014 00:00:00\n */\nexport function setDayOfYear(date, dayOfYear) {\n  const _date = toDate(date);\n  _date.setMonth(0);\n  _date.setDate(dayOfYear);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setDayOfYear;", "map": {"version": 3, "names": ["toDate", "setDayOfYear", "date", "dayOfYear", "_date", "setMonth", "setDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/setDayOfYear.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name setDayOfYear\n * @category Day Helpers\n * @summary Set the day of the year to the given date.\n *\n * @description\n * Set the day of the year to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param dayOfYear - The day of the year of the new date\n *\n * @returns The new date with the day of the year set\n *\n * @example\n * // Set the 2nd day of the year to 2 July 2014:\n * const result = setDayOfYear(new Date(2014, 6, 2), 2)\n * //=> Thu Jan 02 2014 00:00:00\n */\nexport function setDayOfYear(date, dayOfYear) {\n  const _date = toDate(date);\n  _date.setMonth(0);\n  _date.setDate(dayOfYear);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setDayOfYear;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,SAAS,EAAE;EAC5C,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,CAAC;EAC1BE,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC;EACjBD,KAAK,CAACE,OAAO,CAACH,SAAS,CAAC;EACxB,OAAOC,KAAK;AACd;;AAEA;AACA,eAAeH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}