{"ast": null, "code": "import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport function getClockPointerUtilityClass(slot) {\n  return generateUtilityClass('MuiClockPointer', slot);\n}\nexport const clockPointerClasses = generateUtilityClasses('<PERSON><PERSON><PERSON>lockPointer', ['root', 'thumb']);", "map": {"version": 3, "names": ["unstable_generateUtilityClass", "generateUtilityClass", "unstable_generateUtilityClasses", "generateUtilityClasses", "getClockPointerUtilityClass", "slot", "clockPointerClasses"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/TimeClock/clockPointerClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport function getClockPointerUtilityClass(slot) {\n  return generateUtilityClass('MuiClockPointer', slot);\n}\nexport const clockPointerClasses = generateUtilityClasses('<PERSON><PERSON><PERSON>lockPointer', ['root', 'thumb']);"], "mappings": "AAAA,SAASA,6BAA6B,IAAIC,oBAAoB,EAAEC,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AAC7I,OAAO,SAASC,2BAA2BA,CAACC,IAAI,EAAE;EAChD,OAAOJ,oBAAoB,CAAC,iBAAiB,EAAEI,IAAI,CAAC;AACtD;AACA,OAAO,MAAMC,mBAAmB,GAAGH,sBAAsB,CAAC,iBAAiB,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}