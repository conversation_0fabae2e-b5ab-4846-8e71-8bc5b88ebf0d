{"ast": null, "code": "function declensionGroup(scheme, count) {\n  if (count === 1 && scheme.one) {\n    return scheme.one;\n  }\n  if (count >= 2 && count <= 4 && scheme.twoFour) {\n    return scheme.twoFour;\n  }\n\n  // if count === null || count === 0 || count >= 5\n  return scheme.other;\n}\nfunction declension(scheme, count, time) {\n  var group = declensionGroup(scheme, count);\n  var finalText = group[time];\n  return finalText.replace('{{count}}', String(count));\n}\nfunction extractPreposition(token) {\n  var result = ['lessThan', 'about', 'over', 'almost'].filter(function (preposition) {\n    return !!token.match(new RegExp('^' + preposition));\n  });\n  return result[0];\n}\nfunction prefixPreposition(preposition) {\n  var translation = '';\n  if (preposition === 'almost') {\n    translation = 'takmer';\n  }\n  if (preposition === 'about') {\n    translation = 'približne';\n  }\n  return translation.length > 0 ? translation + ' ' : '';\n}\nfunction suffixPreposition(preposition) {\n  var translation = '';\n  if (preposition === 'lessThan') {\n    translation = 'menej než';\n  }\n  if (preposition === 'over') {\n    translation = 'viac než';\n  }\n  return translation.length > 0 ? translation + ' ' : '';\n}\nfunction lowercaseFirstLetter(string) {\n  return string.charAt(0).toLowerCase() + string.slice(1);\n}\nvar formatDistanceLocale = {\n  xSeconds: {\n    one: {\n      present: 'sekunda',\n      past: 'sekundou',\n      future: 'sekundu'\n    },\n    twoFour: {\n      present: '{{count}} sekundy',\n      past: '{{count}} sekundami',\n      future: '{{count}} sekundy'\n    },\n    other: {\n      present: '{{count}} sekúnd',\n      past: '{{count}} sekundami',\n      future: '{{count}} sekúnd'\n    }\n  },\n  halfAMinute: {\n    other: {\n      present: 'pol minúty',\n      past: 'pol minútou',\n      future: 'pol minúty'\n    }\n  },\n  xMinutes: {\n    one: {\n      present: 'minúta',\n      past: 'minútou',\n      future: 'minútu'\n    },\n    twoFour: {\n      present: '{{count}} minúty',\n      past: '{{count}} minútami',\n      future: '{{count}} minúty'\n    },\n    other: {\n      present: '{{count}} minút',\n      past: '{{count}} minútami',\n      future: '{{count}} minút'\n    }\n  },\n  xHours: {\n    one: {\n      present: 'hodina',\n      past: 'hodinou',\n      future: 'hodinu'\n    },\n    twoFour: {\n      present: '{{count}} hodiny',\n      past: '{{count}} hodinami',\n      future: '{{count}} hodiny'\n    },\n    other: {\n      present: '{{count}} hodín',\n      past: '{{count}} hodinami',\n      future: '{{count}} hodín'\n    }\n  },\n  xDays: {\n    one: {\n      present: 'deň',\n      past: 'dňom',\n      future: 'deň'\n    },\n    twoFour: {\n      present: '{{count}} dni',\n      past: '{{count}} dňami',\n      future: '{{count}} dni'\n    },\n    other: {\n      present: '{{count}} dní',\n      past: '{{count}} dňami',\n      future: '{{count}} dní'\n    }\n  },\n  xWeeks: {\n    one: {\n      present: 'týždeň',\n      past: 'týždňom',\n      future: 'týždeň'\n    },\n    twoFour: {\n      present: '{{count}} týždne',\n      past: '{{count}} týždňami',\n      future: '{{count}} týždne'\n    },\n    other: {\n      present: '{{count}} týždňov',\n      past: '{{count}} týždňami',\n      future: '{{count}} týždňov'\n    }\n  },\n  xMonths: {\n    one: {\n      present: 'mesiac',\n      past: 'mesiacom',\n      future: 'mesiac'\n    },\n    twoFour: {\n      present: '{{count}} mesiace',\n      past: '{{count}} mesiacmi',\n      future: '{{count}} mesiace'\n    },\n    other: {\n      present: '{{count}} mesiacov',\n      past: '{{count}} mesiacmi',\n      future: '{{count}} mesiacov'\n    }\n  },\n  xYears: {\n    one: {\n      present: 'rok',\n      past: 'rokom',\n      future: 'rok'\n    },\n    twoFour: {\n      present: '{{count}} roky',\n      past: '{{count}} rokmi',\n      future: '{{count}} roky'\n    },\n    other: {\n      present: '{{count}} rokov',\n      past: '{{count}} rokmi',\n      future: '{{count}} rokov'\n    }\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var preposition = extractPreposition(token) || '';\n  var key = lowercaseFirstLetter(token.substring(preposition.length));\n  var scheme = formatDistanceLocale[key];\n  if (!(options !== null && options !== void 0 && options.addSuffix)) {\n    return prefixPreposition(preposition) + suffixPreposition(preposition) + declension(scheme, count, 'present');\n  }\n  if (options.comparison && options.comparison > 0) {\n    return prefixPreposition(preposition) + 'o ' + suffixPreposition(preposition) + declension(scheme, count, 'future');\n  } else {\n    return prefixPreposition(preposition) + 'pred ' + suffixPreposition(preposition) + declension(scheme, count, 'past');\n  }\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["declensionGroup", "scheme", "count", "one", "twoFour", "other", "declension", "time", "group", "finalText", "replace", "String", "extractPreposition", "token", "result", "filter", "preposition", "match", "RegExp", "prefixPreposition", "translation", "length", "suffixPreposition", "lowercaseFirstLetter", "string", "char<PERSON>t", "toLowerCase", "slice", "formatDistanceLocale", "xSeconds", "present", "past", "future", "halfAMinute", "xMinutes", "xHours", "xDays", "xWeeks", "xMonths", "xYears", "formatDistance", "options", "key", "substring", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/sk/_lib/formatDistance/index.js"], "sourcesContent": ["function declensionGroup(scheme, count) {\n  if (count === 1 && scheme.one) {\n    return scheme.one;\n  }\n  if (count >= 2 && count <= 4 && scheme.twoFour) {\n    return scheme.twoFour;\n  }\n\n  // if count === null || count === 0 || count >= 5\n  return scheme.other;\n}\nfunction declension(scheme, count, time) {\n  var group = declensionGroup(scheme, count);\n  var finalText = group[time];\n  return finalText.replace('{{count}}', String(count));\n}\nfunction extractPreposition(token) {\n  var result = ['lessThan', 'about', 'over', 'almost'].filter(function (preposition) {\n    return !!token.match(new RegExp('^' + preposition));\n  });\n  return result[0];\n}\nfunction prefixPreposition(preposition) {\n  var translation = '';\n  if (preposition === 'almost') {\n    translation = 'takmer';\n  }\n  if (preposition === 'about') {\n    translation = 'približne';\n  }\n  return translation.length > 0 ? translation + ' ' : '';\n}\nfunction suffixPreposition(preposition) {\n  var translation = '';\n  if (preposition === 'lessThan') {\n    translation = 'menej než';\n  }\n  if (preposition === 'over') {\n    translation = 'viac než';\n  }\n  return translation.length > 0 ? translation + ' ' : '';\n}\nfunction lowercaseFirstLetter(string) {\n  return string.charAt(0).toLowerCase() + string.slice(1);\n}\nvar formatDistanceLocale = {\n  xSeconds: {\n    one: {\n      present: 'sekunda',\n      past: 'sekundou',\n      future: 'sekundu'\n    },\n    twoFour: {\n      present: '{{count}} sekundy',\n      past: '{{count}} sekundami',\n      future: '{{count}} sekundy'\n    },\n    other: {\n      present: '{{count}} sekúnd',\n      past: '{{count}} sekundami',\n      future: '{{count}} sekúnd'\n    }\n  },\n  halfAMinute: {\n    other: {\n      present: 'pol minúty',\n      past: 'pol minútou',\n      future: 'pol minúty'\n    }\n  },\n  xMinutes: {\n    one: {\n      present: 'minúta',\n      past: 'minútou',\n      future: 'minútu'\n    },\n    twoFour: {\n      present: '{{count}} minúty',\n      past: '{{count}} minútami',\n      future: '{{count}} minúty'\n    },\n    other: {\n      present: '{{count}} minút',\n      past: '{{count}} minútami',\n      future: '{{count}} minút'\n    }\n  },\n  xHours: {\n    one: {\n      present: 'hodina',\n      past: 'hodinou',\n      future: 'hodinu'\n    },\n    twoFour: {\n      present: '{{count}} hodiny',\n      past: '{{count}} hodinami',\n      future: '{{count}} hodiny'\n    },\n    other: {\n      present: '{{count}} hodín',\n      past: '{{count}} hodinami',\n      future: '{{count}} hodín'\n    }\n  },\n  xDays: {\n    one: {\n      present: 'deň',\n      past: 'dňom',\n      future: 'deň'\n    },\n    twoFour: {\n      present: '{{count}} dni',\n      past: '{{count}} dňami',\n      future: '{{count}} dni'\n    },\n    other: {\n      present: '{{count}} dní',\n      past: '{{count}} dňami',\n      future: '{{count}} dní'\n    }\n  },\n  xWeeks: {\n    one: {\n      present: 'týždeň',\n      past: 'týždňom',\n      future: 'týždeň'\n    },\n    twoFour: {\n      present: '{{count}} týždne',\n      past: '{{count}} týždňami',\n      future: '{{count}} týždne'\n    },\n    other: {\n      present: '{{count}} týždňov',\n      past: '{{count}} týždňami',\n      future: '{{count}} týždňov'\n    }\n  },\n  xMonths: {\n    one: {\n      present: 'mesiac',\n      past: 'mesiacom',\n      future: 'mesiac'\n    },\n    twoFour: {\n      present: '{{count}} mesiace',\n      past: '{{count}} mesiacmi',\n      future: '{{count}} mesiace'\n    },\n    other: {\n      present: '{{count}} mesiacov',\n      past: '{{count}} mesiacmi',\n      future: '{{count}} mesiacov'\n    }\n  },\n  xYears: {\n    one: {\n      present: 'rok',\n      past: 'rokom',\n      future: 'rok'\n    },\n    twoFour: {\n      present: '{{count}} roky',\n      past: '{{count}} rokmi',\n      future: '{{count}} roky'\n    },\n    other: {\n      present: '{{count}} rokov',\n      past: '{{count}} rokmi',\n      future: '{{count}} rokov'\n    }\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var preposition = extractPreposition(token) || '';\n  var key = lowercaseFirstLetter(token.substring(preposition.length));\n  var scheme = formatDistanceLocale[key];\n  if (!(options !== null && options !== void 0 && options.addSuffix)) {\n    return prefixPreposition(preposition) + suffixPreposition(preposition) + declension(scheme, count, 'present');\n  }\n  if (options.comparison && options.comparison > 0) {\n    return prefixPreposition(preposition) + 'o ' + suffixPreposition(preposition) + declension(scheme, count, 'future');\n  } else {\n    return prefixPreposition(preposition) + 'pred ' + suffixPreposition(preposition) + declension(scheme, count, 'past');\n  }\n};\nexport default formatDistance;"], "mappings": "AAAA,SAASA,eAAeA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACtC,IAAIA,KAAK,KAAK,CAAC,IAAID,MAAM,CAACE,GAAG,EAAE;IAC7B,OAAOF,MAAM,CAACE,GAAG;EACnB;EACA,IAAID,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,CAAC,IAAID,MAAM,CAACG,OAAO,EAAE;IAC9C,OAAOH,MAAM,CAACG,OAAO;EACvB;;EAEA;EACA,OAAOH,MAAM,CAACI,KAAK;AACrB;AACA,SAASC,UAAUA,CAACL,MAAM,EAAEC,KAAK,EAAEK,IAAI,EAAE;EACvC,IAAIC,KAAK,GAAGR,eAAe,CAACC,MAAM,EAAEC,KAAK,CAAC;EAC1C,IAAIO,SAAS,GAAGD,KAAK,CAACD,IAAI,CAAC;EAC3B,OAAOE,SAAS,CAACC,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACT,KAAK,CAAC,CAAC;AACtD;AACA,SAASU,kBAAkBA,CAACC,KAAK,EAAE;EACjC,IAAIC,MAAM,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAACC,MAAM,CAAC,UAAUC,WAAW,EAAE;IACjF,OAAO,CAAC,CAACH,KAAK,CAACI,KAAK,CAAC,IAAIC,MAAM,CAAC,GAAG,GAAGF,WAAW,CAAC,CAAC;EACrD,CAAC,CAAC;EACF,OAAOF,MAAM,CAAC,CAAC,CAAC;AAClB;AACA,SAASK,iBAAiBA,CAACH,WAAW,EAAE;EACtC,IAAII,WAAW,GAAG,EAAE;EACpB,IAAIJ,WAAW,KAAK,QAAQ,EAAE;IAC5BI,WAAW,GAAG,QAAQ;EACxB;EACA,IAAIJ,WAAW,KAAK,OAAO,EAAE;IAC3BI,WAAW,GAAG,WAAW;EAC3B;EACA,OAAOA,WAAW,CAACC,MAAM,GAAG,CAAC,GAAGD,WAAW,GAAG,GAAG,GAAG,EAAE;AACxD;AACA,SAASE,iBAAiBA,CAACN,WAAW,EAAE;EACtC,IAAII,WAAW,GAAG,EAAE;EACpB,IAAIJ,WAAW,KAAK,UAAU,EAAE;IAC9BI,WAAW,GAAG,WAAW;EAC3B;EACA,IAAIJ,WAAW,KAAK,MAAM,EAAE;IAC1BI,WAAW,GAAG,UAAU;EAC1B;EACA,OAAOA,WAAW,CAACC,MAAM,GAAG,CAAC,GAAGD,WAAW,GAAG,GAAG,GAAG,EAAE;AACxD;AACA,SAASG,oBAAoBA,CAACC,MAAM,EAAE;EACpC,OAAOA,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC;AACzD;AACA,IAAIC,oBAAoB,GAAG;EACzBC,QAAQ,EAAE;IACR1B,GAAG,EAAE;MACH2B,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE;IACV,CAAC;IACD5B,OAAO,EAAE;MACP0B,OAAO,EAAE,mBAAmB;MAC5BC,IAAI,EAAE,qBAAqB;MAC3BC,MAAM,EAAE;IACV,CAAC;IACD3B,KAAK,EAAE;MACLyB,OAAO,EAAE,kBAAkB;MAC3BC,IAAI,EAAE,qBAAqB;MAC3BC,MAAM,EAAE;IACV;EACF,CAAC;EACDC,WAAW,EAAE;IACX5B,KAAK,EAAE;MACLyB,OAAO,EAAE,YAAY;MACrBC,IAAI,EAAE,aAAa;MACnBC,MAAM,EAAE;IACV;EACF,CAAC;EACDE,QAAQ,EAAE;IACR/B,GAAG,EAAE;MACH2B,OAAO,EAAE,QAAQ;MACjBC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC;IACD5B,OAAO,EAAE;MACP0B,OAAO,EAAE,kBAAkB;MAC3BC,IAAI,EAAE,oBAAoB;MAC1BC,MAAM,EAAE;IACV,CAAC;IACD3B,KAAK,EAAE;MACLyB,OAAO,EAAE,iBAAiB;MAC1BC,IAAI,EAAE,oBAAoB;MAC1BC,MAAM,EAAE;IACV;EACF,CAAC;EACDG,MAAM,EAAE;IACNhC,GAAG,EAAE;MACH2B,OAAO,EAAE,QAAQ;MACjBC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC;IACD5B,OAAO,EAAE;MACP0B,OAAO,EAAE,kBAAkB;MAC3BC,IAAI,EAAE,oBAAoB;MAC1BC,MAAM,EAAE;IACV,CAAC;IACD3B,KAAK,EAAE;MACLyB,OAAO,EAAE,iBAAiB;MAC1BC,IAAI,EAAE,oBAAoB;MAC1BC,MAAM,EAAE;IACV;EACF,CAAC;EACDI,KAAK,EAAE;IACLjC,GAAG,EAAE;MACH2B,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE;IACV,CAAC;IACD5B,OAAO,EAAE;MACP0B,OAAO,EAAE,eAAe;MACxBC,IAAI,EAAE,iBAAiB;MACvBC,MAAM,EAAE;IACV,CAAC;IACD3B,KAAK,EAAE;MACLyB,OAAO,EAAE,eAAe;MACxBC,IAAI,EAAE,iBAAiB;MACvBC,MAAM,EAAE;IACV;EACF,CAAC;EACDK,MAAM,EAAE;IACNlC,GAAG,EAAE;MACH2B,OAAO,EAAE,QAAQ;MACjBC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC;IACD5B,OAAO,EAAE;MACP0B,OAAO,EAAE,kBAAkB;MAC3BC,IAAI,EAAE,oBAAoB;MAC1BC,MAAM,EAAE;IACV,CAAC;IACD3B,KAAK,EAAE;MACLyB,OAAO,EAAE,mBAAmB;MAC5BC,IAAI,EAAE,oBAAoB;MAC1BC,MAAM,EAAE;IACV;EACF,CAAC;EACDM,OAAO,EAAE;IACPnC,GAAG,EAAE;MACH2B,OAAO,EAAE,QAAQ;MACjBC,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE;IACV,CAAC;IACD5B,OAAO,EAAE;MACP0B,OAAO,EAAE,mBAAmB;MAC5BC,IAAI,EAAE,oBAAoB;MAC1BC,MAAM,EAAE;IACV,CAAC;IACD3B,KAAK,EAAE;MACLyB,OAAO,EAAE,oBAAoB;MAC7BC,IAAI,EAAE,oBAAoB;MAC1BC,MAAM,EAAE;IACV;EACF,CAAC;EACDO,MAAM,EAAE;IACNpC,GAAG,EAAE;MACH2B,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAE;IACV,CAAC;IACD5B,OAAO,EAAE;MACP0B,OAAO,EAAE,gBAAgB;MACzBC,IAAI,EAAE,iBAAiB;MACvBC,MAAM,EAAE;IACV,CAAC;IACD3B,KAAK,EAAE;MACLyB,OAAO,EAAE,iBAAiB;MAC1BC,IAAI,EAAE,iBAAiB;MACvBC,MAAM,EAAE;IACV;EACF;AACF,CAAC;AACD,IAAIQ,cAAc,GAAG,SAASA,cAAcA,CAAC3B,KAAK,EAAEX,KAAK,EAAEuC,OAAO,EAAE;EAClE,IAAIzB,WAAW,GAAGJ,kBAAkB,CAACC,KAAK,CAAC,IAAI,EAAE;EACjD,IAAI6B,GAAG,GAAGnB,oBAAoB,CAACV,KAAK,CAAC8B,SAAS,CAAC3B,WAAW,CAACK,MAAM,CAAC,CAAC;EACnE,IAAIpB,MAAM,GAAG2B,oBAAoB,CAACc,GAAG,CAAC;EACtC,IAAI,EAAED,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACG,SAAS,CAAC,EAAE;IAClE,OAAOzB,iBAAiB,CAACH,WAAW,CAAC,GAAGM,iBAAiB,CAACN,WAAW,CAAC,GAAGV,UAAU,CAACL,MAAM,EAAEC,KAAK,EAAE,SAAS,CAAC;EAC/G;EACA,IAAIuC,OAAO,CAACI,UAAU,IAAIJ,OAAO,CAACI,UAAU,GAAG,CAAC,EAAE;IAChD,OAAO1B,iBAAiB,CAACH,WAAW,CAAC,GAAG,IAAI,GAAGM,iBAAiB,CAACN,WAAW,CAAC,GAAGV,UAAU,CAACL,MAAM,EAAEC,KAAK,EAAE,QAAQ,CAAC;EACrH,CAAC,MAAM;IACL,OAAOiB,iBAAiB,CAACH,WAAW,CAAC,GAAG,OAAO,GAAGM,iBAAiB,CAACN,WAAW,CAAC,GAAGV,UAAU,CAACL,MAAM,EAAEC,KAAK,EAAE,MAAM,CAAC;EACtH;AACF,CAAC;AACD,eAAesC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}