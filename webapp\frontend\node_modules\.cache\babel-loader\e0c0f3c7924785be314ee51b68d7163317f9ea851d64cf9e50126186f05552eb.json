{"ast": null, "code": "import React from'react';import{Box,TableCell,TableSortLabel,Typography}from'@mui/material';import ExcelLikeFilter from'./ExcelLikeFilter';/**\n * Componente per l'intestazione di una tabella con filtri in stile Excel\n * \n * @param {Object} props - Proprietà del componente\n * @param {string} props.columnName - Nome della colonna\n * @param {string} props.label - Etichetta da visualizzare\n * @param {Array} props.data - Dati della tabella\n * @param {Function} props.onFilterChange - Funzione chiamata quando il filtro cambia\n * @param {string} props.dataType - Tipo di dati ('text', 'number', 'date')\n * @param {string} props.sortDirection - Direzione di ordinamento ('asc', 'desc', null)\n * @param {Function} props.onSortChange - Funzione chiamata quando l'ordinamento cambia\n * @param {boolean} props.disableFilter - <PERSON>sabilita il filtro\n * @param {boolean} props.disableSort - Disabilita l'ordinamento\n */import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FilterableTableHeader=_ref=>{let{columnName,label,data,onFilterChange,dataType='text',sortDirection=null,onSortChange=null,disableFilter=false,disableSort=false,...cellProps}=_ref;const handleSortClick=()=>{if(onSortChange&&!disableSort){const newDirection=sortDirection==='asc'?'desc':'asc';onSortChange(columnName,newDirection);}};return/*#__PURE__*/_jsx(TableCell,{...cellProps,children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between'},children:[!disableSort&&onSortChange?/*#__PURE__*/_jsx(TableSortLabel,{active:Boolean(sortDirection),direction:sortDirection||'asc',onClick:handleSortClick,children:/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",component:\"span\",children:label})}):/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",component:\"span\",children:label}),!disableFilter&&/*#__PURE__*/_jsx(ExcelLikeFilter,{data:data,columnName:columnName,onFilterChange:onFilterChange,dataType:dataType})]})});};export default FilterableTableHeader;", "map": {"version": 3, "names": ["React", "Box", "TableCell", "TableSortLabel", "Typography", "ExcelLikeFilter", "jsx", "_jsx", "jsxs", "_jsxs", "FilterableTableHeader", "_ref", "columnName", "label", "data", "onFilterChange", "dataType", "sortDirection", "onSortChange", "disableFilter", "disableSort", "cellProps", "handleSortClick", "newDirection", "children", "sx", "display", "alignItems", "justifyContent", "active", "Boolean", "direction", "onClick", "variant", "component"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/common/FilterableTableHeader.js"], "sourcesContent": ["import React from 'react';\nimport { Box, TableCell, TableSortLabel, Typography } from '@mui/material';\nimport ExcelLikeFilter from './ExcelLikeFilter';\n\n/**\n * Componente per l'intestazione di una tabella con filtri in stile Excel\n * \n * @param {Object} props - Proprietà del componente\n * @param {string} props.columnName - Nome della colonna\n * @param {string} props.label - Etichetta da visualizzare\n * @param {Array} props.data - Dati della tabella\n * @param {Function} props.onFilterChange - Funzione chiamata quando il filtro cambia\n * @param {string} props.dataType - Tipo di dati ('text', 'number', 'date')\n * @param {string} props.sortDirection - Direzione di ordinamento ('asc', 'desc', null)\n * @param {Function} props.onSortChange - Funzione chiamata quando l'ordinamento cambia\n * @param {boolean} props.disableFilter - Disabilita il filtro\n * @param {boolean} props.disableSort - Disabilita l'ordinamento\n */\nconst FilterableTableHeader = ({\n  columnName,\n  label,\n  data,\n  onFilterChange,\n  dataType = 'text',\n  sortDirection = null,\n  onSortChange = null,\n  disableFilter = false,\n  disableSort = false,\n  ...cellProps\n}) => {\n  const handleSortClick = () => {\n    if (onSortChange && !disableSort) {\n      const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';\n      onSortChange(columnName, newDirection);\n    }\n  };\n\n  return (\n    <TableCell {...cellProps}>\n      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        {!disableSort && onSortChange ? (\n          <TableSortLabel\n            active={Boolean(sortDirection)}\n            direction={sortDirection || 'asc'}\n            onClick={handleSortClick}\n          >\n            <Typography variant=\"subtitle2\" component=\"span\">\n              {label}\n            </Typography>\n          </TableSortLabel>\n        ) : (\n          <Typography variant=\"subtitle2\" component=\"span\">\n            {label}\n          </Typography>\n        )}\n        \n        {!disableFilter && (\n          <ExcelLikeFilter\n            data={data}\n            columnName={columnName}\n            onFilterChange={onFilterChange}\n            dataType={dataType}\n          />\n        )}\n      </Box>\n    </TableCell>\n  );\n};\n\nexport default FilterableTableHeader;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,GAAG,CAAEC,SAAS,CAAEC,cAAc,CAAEC,UAAU,KAAQ,eAAe,CAC1E,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAbA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAcA,KAAM,CAAAC,qBAAqB,CAAGC,IAAA,EAWxB,IAXyB,CAC7BC,UAAU,CACVC,KAAK,CACLC,IAAI,CACJC,cAAc,CACdC,QAAQ,CAAG,MAAM,CACjBC,aAAa,CAAG,IAAI,CACpBC,YAAY,CAAG,IAAI,CACnBC,aAAa,CAAG,KAAK,CACrBC,WAAW,CAAG,KAAK,CACnB,GAAGC,SACL,CAAC,CAAAV,IAAA,CACC,KAAM,CAAAW,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAIJ,YAAY,EAAI,CAACE,WAAW,CAAE,CAChC,KAAM,CAAAG,YAAY,CAAGN,aAAa,GAAK,KAAK,CAAG,MAAM,CAAG,KAAK,CAC7DC,YAAY,CAACN,UAAU,CAAEW,YAAY,CAAC,CACxC,CACF,CAAC,CAED,mBACEhB,IAAA,CAACL,SAAS,KAAKmB,SAAS,CAAAG,QAAA,cACtBf,KAAA,CAACR,GAAG,EAACwB,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,eAAgB,CAAE,CAAAJ,QAAA,EACjF,CAACJ,WAAW,EAAIF,YAAY,cAC3BX,IAAA,CAACJ,cAAc,EACb0B,MAAM,CAAEC,OAAO,CAACb,aAAa,CAAE,CAC/Bc,SAAS,CAAEd,aAAa,EAAI,KAAM,CAClCe,OAAO,CAAEV,eAAgB,CAAAE,QAAA,cAEzBjB,IAAA,CAACH,UAAU,EAAC6B,OAAO,CAAC,WAAW,CAACC,SAAS,CAAC,MAAM,CAAAV,QAAA,CAC7CX,KAAK,CACI,CAAC,CACC,CAAC,cAEjBN,IAAA,CAACH,UAAU,EAAC6B,OAAO,CAAC,WAAW,CAACC,SAAS,CAAC,MAAM,CAAAV,QAAA,CAC7CX,KAAK,CACI,CACb,CAEA,CAACM,aAAa,eACbZ,IAAA,CAACF,eAAe,EACdS,IAAI,CAAEA,IAAK,CACXF,UAAU,CAAEA,UAAW,CACvBG,cAAc,CAAEA,cAAe,CAC/BC,QAAQ,CAAEA,QAAS,CACpB,CACF,EACE,CAAC,CACG,CAAC,CAEhB,CAAC,CAED,cAAe,CAAAN,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}