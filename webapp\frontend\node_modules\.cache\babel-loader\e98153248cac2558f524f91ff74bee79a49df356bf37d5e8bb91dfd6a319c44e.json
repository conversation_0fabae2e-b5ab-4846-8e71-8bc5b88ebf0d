{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Chip, CircularProgress, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport AggiungiCavoForm from '../../components/cavi/AggiungiCavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport './CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  var _caviAttivi$, _caviAttivi$2, _caviAttivi$3;\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const {\n    openEliminaCavoDialog,\n    setOpenEliminaCavoDialog,\n    openModificaCavoDialog,\n    setOpenModificaCavoDialog,\n    openAggiungiCavoDialog,\n    setOpenAggiungiCavoDialog\n  } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState(null);\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Rimosso stato per il debug\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  const fetchCavi = async () => {\n    try {\n      setLoading(true);\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      const attivi = await caviService.getCavi(cantiereId, 0, filters);\n      console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        const spare = await caviService.getCaviSpare(cantiereId);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n        setCaviSpare(spare || []);\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        console.log('Tentativo con metodo standard...');\n        const spare = await caviService.getCavi(cantiereId, 3);\n        console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        setCaviSpare(spare || []);\n      }\n\n      // Carica le statistiche\n      console.log('Caricamento statistiche...');\n      const statsData = await caviService.getCaviStats(cantiereId);\n      console.log('Statistiche caricate:', statsData);\n      setStats(statsData);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = cavo => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: handleCloseDetails,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Cavo: \", selectedCavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sistema:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utility:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utility || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Colore:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.colore_cavo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"N. Conduttori:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.n_conduttori || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sezione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"SH:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sh || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metratura Reale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 45\n                }, this), \" \", normalizeInstallationStatus(selectedCavo.stato_installazione)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Collegamenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.collegamenti || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.id_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ultimo Aggiornamento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 45\n                }, this), \" \", new Date(selectedCavo.timestamp).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDetails,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    if (!stats) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Statistiche\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this), loadingStats ? /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Cavi Attivi: \", stats.totali.cavi_attivi]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Cavi Spare: \", stats.totali.cavi_spare]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Totale Cavi: \", stats.totali.cavi_totali]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Metrature\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Metri Teorici: \", stats.metrature.metri_teorici_totali.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Metri Posati: \", stats.metrature.metri_reali_totali.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: '100%',\n                mr: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"determinate\",\n                value: stats.metrature.percentuale_completamento,\n                sx: {\n                  height: 10,\n                  borderRadius: 5\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                minWidth: 35\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: `${stats.metrature.percentuale_completamento.toFixed(1)}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Stati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: stats.stati.map((stato, index) => /*#__PURE__*/_jsxDEV(Chip, {\n              label: `${stato.stato}: ${stato.count}`,\n              size: \"small\",\n              onClick: () => {\n                setFilters(prev => ({\n                  ...prev,\n                  stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                }));\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 553,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [renderStatsPanel(), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Attivi \", caviAttivi.length > 0 ? `(${caviAttivi.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: () => window.location.reload(),\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 71\n            }, this),\n            disabled: loading,\n            children: \"Aggiorna\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 11\n      }, this), caviAttivi.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2,\n            p: 1,\n            bgcolor: '#f0f0f0',\n            borderRadius: 1,\n            fontSize: '0.8rem',\n            fontFamily: 'monospace',\n            display: 'none'\n          },\n          children: Object.keys(caviAttivi[0]).map(key => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [key, \": \", JSON.stringify(caviAttivi[0][key])]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 21\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 613,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviAttivi,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi attivi filtrati:', filteredData.length),\n          revisioneCorrente: ((_caviAttivi$ = caviAttivi[0]) === null || _caviAttivi$ === void 0 ? void 0 : _caviAttivi$.revisione_ufficiale) || ((_caviAttivi$2 = caviAttivi[0]) === null || _caviAttivi$2 === void 0 ? void 0 : _caviAttivi$2.revisione) || ((_caviAttivi$3 = caviAttivi[0]) === null || _caviAttivi$3 === void 0 ? void 0 : _caviAttivi$3.rev)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 610,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 2\n        },\n        children: \"Nessun cavo attivo trovato. I cavi attivi appariranno qui.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 627,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Spare \", caviSpare.length > 0 ? `(${caviSpare.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: () => window.location.reload(),\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 71\n            }, this),\n            disabled: loading,\n            children: \"Aggiorna\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 13\n        }, this), caviSpare.length > 0 ? /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviSpare,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi spare filtrati:', filteredData.length)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: \"Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 655,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 633,\n        columnNumber: 11\n      }, this), renderDetailsDialog(), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openEliminaCavoDialog,\n        onClose: () => setOpenEliminaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"md\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio all'utente\n              alert(message);\n              // Ricarica i dati dopo un ritardo più lungo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                console.log('Ricaricamento dati dopo operazione...');\n                // Forza un ricaricamento completo della pagina\n                window.location.reload();\n              }, 2000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante l\\'eliminazione del cavo:', message);\n            // Mostra un alert all'utente\n            alert(`Errore: ${message}`);\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            fetchCavi();\n          },\n          initialOption: \"eliminaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 667,\n        columnNumber: 11\n      }, this), openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openModificaCavoDialog,\n        onClose: () => setOpenModificaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"md\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenModificaCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio all'utente\n              alert(message);\n              // Ricarica i dati dopo un ritardo più lungo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                console.log('Ricaricamento dati dopo operazione...');\n                try {\n                  // Ricarica i dati invece di ricaricare la pagina\n                  fetchCavi();\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina dopo un ulteriore ritardo\n                  setTimeout(() => {\n                    console.log('Tentativo di ricaricamento della pagina...');\n                    window.location.reload();\n                  }, 1000);\n                }\n              }, 2000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante la modifica del cavo:', message);\n            // Mostra un alert all'utente\n            alert(`Errore: ${message}`);\n            // Chiudi il dialogo\n            setOpenModificaCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            setTimeout(() => {\n              console.log('Ricaricamento dati dopo errore...');\n              try {\n                fetchCavi();\n              } catch (reloadError) {\n                console.error('Errore durante il ricaricamento dei dati dopo errore:', reloadError);\n                // Se fallisce, prova a ricaricare la pagina dopo un ulteriore ritardo\n                setTimeout(() => {\n                  console.log('Tentativo di ricaricamento della pagina dopo errore...');\n                  window.location.reload();\n                }, 1000);\n              }\n            }, 1000);\n          },\n          initialOption: \"modificaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 714,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openAggiungiCavoDialog,\n        onClose: () => setOpenAggiungiCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"md\",\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Aggiungi Nuovo Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(AggiungiCavoForm, {\n              cantiereId: cantiereId,\n              onSuccess: message => {\n                // Chiudi il dialogo\n                setOpenAggiungiCavoDialog(false);\n                // Mostra un messaggio di successo\n                alert(message);\n                // Ricarica i dati dopo un ritardo più lungo per dare tempo al database di aggiornarsi\n                setTimeout(() => {\n                  console.log('Ricaricamento dati dopo operazione...');\n                  try {\n                    // Ricarica i dati invece di ricaricare la pagina\n                    fetchCavi();\n                  } catch (error) {\n                    console.error('Errore durante il ricaricamento dei dati:', error);\n                    // Se fallisce, prova a ricaricare la pagina dopo un ulteriore ritardo\n                    setTimeout(() => {\n                      console.log('Tentativo di ricaricamento della pagina...');\n                      window.location.reload();\n                    }, 1000);\n                  }\n                }, 2000);\n              },\n              onError: message => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'aggiunta del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n              },\n              isDialog: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 786,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setOpenAggiungiCavoDialog(false),\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 822,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 821,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 779,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 548,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"tvZzodNHDIdbAtiErryuJ2G6WY8=\", false, function () {\n  return [useAuth, useGlobalContext, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Chip", "CircularProgress", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "InfoIcon", "RefreshIcon", "useNavigate", "useAuth", "useGlobalContext", "PosaCaviCollegamenti", "caviService", "AggiungiCavoForm", "normalizeInstallationStatus", "CaviFilterableTable", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "_caviAttivi$", "_caviAttivi$2", "_caviAttivi$3", "isImpersonating", "user", "openEliminaCavoDialog", "setOpenEliminaCavoDialog", "openModificaCavoDialog", "setOpenModificaCavoDialog", "openAggiungiCavoDialog", "setOpenAggiungiCavoDialog", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "detailsDialogOpen", "setDetailsDialogOpen", "stats", "setStats", "loadingStats", "setLoadingStats", "loadStatiInstallazione", "setStatiInstallazione", "filters", "setFilters", "stato_installazione", "tipologia", "sort_by", "sort_order", "statiInstallazione", "tipologieCavi", "setTipologieCavi", "<PERSON><PERSON><PERSON>", "console", "log", "attivi", "get<PERSON><PERSON>", "length", "caviSpareTra<PERSON>ttivi", "filter", "cavo", "modificato_manualmente", "spare", "getCaviSpare", "spareError", "statsData", "getCaviStats", "message", "fetchData", "token", "localStorage", "getItem", "selectedCantiereId", "selectedCantiereName", "i", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "stati", "item", "stato", "tipologie", "tipo", "statsError", "timeoutPromise", "Promise", "_", "reject", "setTimeout", "Error", "caviPromise", "race", "caviError", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "includes", "detail", "handleOpenDetails", "handleCloseDetails", "renderDetailsDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "id_cavo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dividers", "container", "spacing", "xs", "md", "variant", "gutterBottom", "sx", "mb", "sistema", "utility", "colore_cavo", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "responsabile_partenza", "comanda_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "responsabile_arrivo", "comanda_arrivo", "metri_te<PERSON>ci", "metratura_reale", "colle<PERSON>nti", "id_bobina", "responsabile_posa", "comanda_posa", "Date", "timestamp", "toLocaleString", "onClick", "renderStatsPanel", "p", "totali", "cavi_attivi", "cavi_spare", "cavi_totali", "metrature", "metri_teorici_totali", "toFixed", "metri_reali_totali", "display", "alignItems", "mt", "width", "mr", "value", "percentuale_completamento", "height", "borderRadius", "min<PERSON><PERSON><PERSON>", "color", "flexWrap", "gap", "index", "label", "count", "size", "prev", "className", "flexDirection", "window", "location", "reload", "severity", "justifyContent", "startIcon", "disabled", "process", "env", "NODE_ENV", "bgcolor", "fontSize", "fontFamily", "Object", "keys", "stringify", "cavi", "onFilteredDataChange", "filteredData", "revisioneCorrente", "revisione_ufficiale", "revisione", "rev", "onSuccess", "alert", "onError", "initialOption", "reloadError", "isDialog", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Chip,\n  CircularProgress,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport AggiungiCavoForm from '../../components/cavi/AggiungiCavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog, openAggiungiCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState(null);\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Rimosso stato per il debug\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  const fetchCavi = async () => {\n    try {\n      setLoading(true);\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      const attivi = await caviService.getCavi(cantiereId, 0, filters);\n      console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        const spare = await caviService.getCaviSpare(cantiereId);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n        setCaviSpare(spare || []);\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        console.log('Tentativo con metodo standard...');\n        const spare = await caviService.getCavi(cantiereId, 3);\n        console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        setCaviSpare(spare || []);\n      }\n\n      // Carica le statistiche\n      console.log('Caricamento statistiche...');\n      const statsData = await caviService.getCaviStats(cantiereId);\n      console.log('Statistiche caricate:', statsData);\n      setStats(statsData);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = (cavo) => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Cavo: {selectedCavo.id_cavo}\n        </DialogTitle>\n        <DialogContent dividers>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Informazioni Generali</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>N. Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Sezione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>SH:</strong> {selectedCavo.sh || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Partenza</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Arrivo</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Installazione</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>\n                <Typography variant=\"body2\"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDetails}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    if (!stats) return null;\n\n    return (\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Typography variant=\"h6\" gutterBottom>Statistiche</Typography>\n        {loadingStats ? (\n          <LinearProgress />\n        ) : (\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Totali</Typography>\n              <Typography variant=\"body2\">Cavi Attivi: {stats.totali.cavi_attivi}</Typography>\n              <Typography variant=\"body2\">Cavi Spare: {stats.totali.cavi_spare}</Typography>\n              <Typography variant=\"body2\">Totale Cavi: {stats.totali.cavi_totali}</Typography>\n              {/* Rimossa visualizzazione della revisione da qui, spostata nel titolo delle statistiche della tabella */}\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Metrature</Typography>\n              <Typography variant=\"body2\">Metri Teorici: {stats.metrature.metri_teorici_totali.toFixed(2)}</Typography>\n              <Typography variant=\"body2\">Metri Posati: {stats.metrature.metri_reali_totali.toFixed(2)}</Typography>\n              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                <Box sx={{ width: '100%', mr: 1 }}>\n                  <LinearProgress\n                    variant=\"determinate\"\n                    value={stats.metrature.percentuale_completamento}\n                    sx={{ height: 10, borderRadius: 5 }}\n                  />\n                </Box>\n                <Box sx={{ minWidth: 35 }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">{`${stats.metrature.percentuale_completamento.toFixed(1)}%`}</Typography>\n                </Box>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Stati</Typography>\n              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                {stats.stati.map((stato, index) => (\n                  <Chip\n                    key={index}\n                    label={`${stato.stato}: ${stato.count}`}\n                    size=\"small\"\n                    onClick={() => {\n                      setFilters(prev => ({\n                        ...prev,\n                        stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                      }));\n                    }}\n                  />\n                ))}\n              </Box>\n            </Grid>\n          </Grid>\n        )}\n      </Paper>\n    );\n  };\n\n  return (\n    <Box className=\"cavi-page\">\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <CircularProgress size={40} />\n          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          {/* Rimosso il pulsante di refresh, gli utenti possono usare il refresh del browser */}\n\n          {/* Pannello delle statistiche */}\n          {renderStatsPanel()}\n\n          {/* Sezione Cavi Attivi */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Attivi {caviAttivi.length > 0 ? `(${caviAttivi.length})` : ''}\n              </Typography>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => window.location.reload()}\n                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}\n                disabled={loading}\n              >\n                Aggiorna\n              </Button>\n            </Box>\n          </Box>\n\n          {caviAttivi.length > 0 ? (\n            <Box sx={{ mb: 2 }}>\n              {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}\n              {process.env.NODE_ENV === 'development' && (\n                <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>\n                  {Object.keys(caviAttivi[0]).map(key => (\n                    <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>\n                  ))}\n                </Box>\n              )}\n              <CaviFilterableTable\n                cavi={caviAttivi}\n                loading={loading}\n                onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}\n                revisioneCorrente={caviAttivi[0]?.revisione_ufficiale || caviAttivi[0]?.revisione || caviAttivi[0]?.rev}\n              />\n            </Box>\n          ) : (\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Nessun cavo attivo trovato. I cavi attivi appariranno qui.\n            </Alert>\n          )}\n\n          {/* Sezione Cavi Spare */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}\n              </Typography>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => window.location.reload()}\n                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}\n                disabled={loading}\n              >\n                Aggiorna\n              </Button>\n            </Box>\n            {caviSpare.length > 0 ? (\n              <CaviFilterableTable\n                cavi={caviSpare}\n                loading={loading}\n                onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}\n              />\n            ) : (\n              <Alert severity=\"info\" sx={{ mb: 2 }}>\n                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Rimossa sezione Debug */}\n\n          {/* Dialogo dei dettagli del cavo */}\n          {renderDetailsDialog()}\n\n          {/* Dialogo per l'eliminazione dei cavi */}\n          <Dialog\n            open={openEliminaCavoDialog}\n            onClose={() => setOpenEliminaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio all'utente\n                  alert(message);\n                  // Ricarica i dati dopo un ritardo più lungo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    console.log('Ricaricamento dati dopo operazione...');\n                    // Forza un ricaricamento completo della pagina\n                    window.location.reload();\n                  }, 2000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'eliminazione del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                fetchCavi();\n              }}\n              initialOption=\"eliminaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per la modifica dei cavi */}\n          {/* Log del cantiereId prima di aprire il dialog */}\n          {openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId)}\n\n          <Dialog\n            open={openModificaCavoDialog}\n            onClose={() => setOpenModificaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio all'utente\n                  alert(message);\n                  // Ricarica i dati dopo un ritardo più lungo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    console.log('Ricaricamento dati dopo operazione...');\n                    try {\n                      // Ricarica i dati invece di ricaricare la pagina\n                      fetchCavi();\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina dopo un ulteriore ritardo\n                      setTimeout(() => {\n                        console.log('Tentativo di ricaricamento della pagina...');\n                        window.location.reload();\n                      }, 1000);\n                    }\n                  }, 2000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante la modifica del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                setTimeout(() => {\n                  console.log('Ricaricamento dati dopo errore...');\n                  try {\n                    fetchCavi();\n                  } catch (reloadError) {\n                    console.error('Errore durante il ricaricamento dei dati dopo errore:', reloadError);\n                    // Se fallisce, prova a ricaricare la pagina dopo un ulteriore ritardo\n                    setTimeout(() => {\n                      console.log('Tentativo di ricaricamento della pagina dopo errore...');\n                      window.location.reload();\n                    }, 1000);\n                  }\n                }, 1000);\n              }}\n              initialOption=\"modificaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per l'aggiunta di un nuovo cavo */}\n          <Dialog\n            open={openAggiungiCavoDialog}\n            onClose={() => setOpenAggiungiCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <DialogTitle>Aggiungi Nuovo Cavo</DialogTitle>\n            <DialogContent>\n              <Box sx={{ mt: 1 }}>\n                <AggiungiCavoForm\n                  cantiereId={cantiereId}\n                  onSuccess={(message) => {\n                    // Chiudi il dialogo\n                    setOpenAggiungiCavoDialog(false);\n                    // Mostra un messaggio di successo\n                    alert(message);\n                    // Ricarica i dati dopo un ritardo più lungo per dare tempo al database di aggiornarsi\n                    setTimeout(() => {\n                      console.log('Ricaricamento dati dopo operazione...');\n                      try {\n                        // Ricarica i dati invece di ricaricare la pagina\n                        fetchCavi();\n                      } catch (error) {\n                        console.error('Errore durante il ricaricamento dei dati:', error);\n                        // Se fallisce, prova a ricaricare la pagina dopo un ulteriore ritardo\n                        setTimeout(() => {\n                          console.log('Tentativo di ricaricamento della pagina...');\n                          window.location.reload();\n                        }, 1000);\n                      }\n                    }, 2000);\n                  }}\n                  onError={(message) => {\n                    // Mostra un messaggio di errore\n                    console.error('Errore durante l\\'aggiunta del cavo:', message);\n                    // Mostra un alert all'utente\n                    alert(`Errore: ${message}`);\n                  }}\n                  isDialog={true}\n                />\n              </Box>\n            </DialogContent>\n            <DialogActions>\n              <Button onClick={() => setOpenAggiungiCavoDialog(false)}>Annulla</Button>\n            </DialogActions>\n          </Dialog>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,gBAAgB,EAChBC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,WAAW,MAAM,6BAA6B;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,SAASC,2BAA2B,QAAQ,6BAA6B;AACzE,OAAOC,mBAAmB,MAAM,2CAA2C;AAC3E,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEgB,qBAAqB;IAAEC,wBAAwB;IAAEC,sBAAsB;IAAEC,yBAAyB;IAAEC,sBAAsB;IAAEC;EAA0B,CAAC,GAAGpB,gBAAgB,CAAC,CAAC;EACpL,MAAMqB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkD,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsD,KAAK,EAAEC,QAAQ,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EACxC;;EAEA;EACA,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC4D,KAAK,EAAEC,QAAQ,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;;EAEvD;;EAEA;EACA,MAAMgE,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAC,qBAAqB,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnE,QAAQ,CAAC;IACrCoE,mBAAmB,EAAE,EAAE;IACvBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEP,qBAAqB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;;EAEtD;;EAEA;EACA,MAAM2E,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAChBuB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEjC,UAAU,CAAC;;MAEzD;MACAgC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,MAAMC,MAAM,GAAG,MAAMtD,WAAW,CAACuD,OAAO,CAACnC,UAAU,EAAE,CAAC,EAAEsB,OAAO,CAAC;MAChEU,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,MAAM,GAAGA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC;;MAEhE;MACA,IAAIF,MAAM,IAAIA,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;QAC/B,MAAMC,kBAAkB,GAAGH,MAAM,CAACI,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,sBAAsB,KAAK,CAAC,CAAC;QACnF,IAAIH,kBAAkB,CAACD,MAAM,GAAG,CAAC,EAAE;UACjCJ,OAAO,CAACtB,KAAK,CAAC,wEAAwE,EAAE2B,kBAAkB,CAAC;QAC7G;MACF;MAEAhC,aAAa,CAAC6B,MAAM,IAAI,EAAE,CAAC;;MAE3B;MACA,IAAI;QACFF,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9D,MAAMQ,KAAK,GAAG,MAAM7D,WAAW,CAAC8D,YAAY,CAAC1C,UAAU,CAAC;QACxDgC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEQ,KAAK,GAAGA,KAAK,CAACL,MAAM,GAAG,CAAC,CAAC;QACnF,IAAIK,KAAK,IAAIA,KAAK,CAACL,MAAM,GAAG,CAAC,EAAE;UAC7BJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEQ,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5C;QACAlC,YAAY,CAACkC,KAAK,IAAI,EAAE,CAAC;MAC3B,CAAC,CAAC,OAAOE,UAAU,EAAE;QACnBX,OAAO,CAACtB,KAAK,CAAC,8DAA8D,EAAEiC,UAAU,CAAC;QACzF;QACAX,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QAC/C,MAAMQ,KAAK,GAAG,MAAM7D,WAAW,CAACuD,OAAO,CAACnC,UAAU,EAAE,CAAC,CAAC;QACtDgC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEQ,KAAK,GAAGA,KAAK,CAACL,MAAM,GAAG,CAAC,CAAC;QACjF7B,YAAY,CAACkC,KAAK,IAAI,EAAE,CAAC;MAC3B;;MAEA;MACAT,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzC,MAAMW,SAAS,GAAG,MAAMhE,WAAW,CAACiE,YAAY,CAAC7C,UAAU,CAAC;MAC5DgC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEW,SAAS,CAAC;MAC/C3B,QAAQ,CAAC2B,SAAS,CAAC;IACrB,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdsB,OAAO,CAACtB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDC,QAAQ,CAAC,oCAAoCD,KAAK,CAACoC,OAAO,IAAI,oBAAoB,EAAE,CAAC;IACvF,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACApD,SAAS,CAAC,MAAM;IACd;IACA+D,sBAAsB,CAAC,CAAC;IAExB,MAAM2B,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFf,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMe,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3ClB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAACe,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACVrC,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAI0C,kBAAkB,GAAGF,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAIE,oBAAoB,GAAGH,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvElB,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAEkB,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnGpB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEzC,IAAI,CAAC;;QAEjC;QACAwC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,CAACb,MAAM,EAAEiB,CAAC,EAAE,EAAE;UAC5C,MAAMC,GAAG,GAAGL,YAAY,CAACK,GAAG,CAACD,CAAC,CAAC;UAC/BrB,OAAO,CAACC,GAAG,CAAC,GAAGqB,GAAG,KAAKL,YAAY,CAACC,OAAO,CAACI,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAA9D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D,IAAI,MAAK,eAAe,EAAE;UAClCvB,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAIzC,IAAI,CAACgE,WAAW,EAAE;YACpBxB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEzC,IAAI,CAACgE,WAAW,CAAC;YACrEL,kBAAkB,GAAG3D,IAAI,CAACgE,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDL,oBAAoB,GAAG5D,IAAI,CAACkE,aAAa,IAAI,YAAYlE,IAAI,CAACgE,WAAW,EAAE;;YAE3E;YACAP,YAAY,CAACU,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;YAC9DF,YAAY,CAACU,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;YAClEpB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEkB,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACFnB,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAMe,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAIF,KAAK,EAAE;gBACT;gBACA,MAAMY,SAAS,GAAGZ,KAAK,CAACa,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvChC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEuC,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvBxB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEuC,OAAO,CAAChB,WAAW,CAAC;kBACtEL,kBAAkB,GAAGqB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAL,oBAAoB,GAAG,YAAYoB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACAP,YAAY,CAACU,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;kBAC9DF,YAAY,CAACU,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;kBAClEpB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEkB,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOwB,CAAC,EAAE;cACV3C,OAAO,CAACtB,KAAK,CAAC,6CAA6C,EAAEiE,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACxB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9FnB,OAAO,CAAC4C,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACAzB,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACAH,YAAY,CAACU,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;UAC9DF,YAAY,CAACU,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;UAClEpB,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEkB,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvBxC,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMoE,aAAa,GAAGC,QAAQ,CAAC3B,kBAAkB,EAAE,EAAE,CAAC;QACtDnB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE4C,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxBlE,QAAQ,CAAC,2BAA2BwC,kBAAkB,mCAAmC,CAAC;UAC1F1C,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAAC4E,aAAa,CAAC;QAC5B1E,eAAe,CAACiD,oBAAoB,IAAI,YAAYyB,aAAa,EAAE,CAAC;;QAEpE;QACA,IAAI;UACF1D,eAAe,CAAC,IAAI,CAAC;UACrBa,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE4C,aAAa,CAAC;UACxE,MAAMjC,SAAS,GAAG,MAAMhE,WAAW,CAACiE,YAAY,CAACgC,aAAa,CAAC;UAC/D7C,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEW,SAAS,CAAC;UACpD3B,QAAQ,CAAC2B,SAAS,CAAC;;UAEnB;UACA,IAAIA,SAAS,IAAIA,SAAS,CAACoC,KAAK,EAAE;YAChC,MAAMA,KAAK,GAAGpC,SAAS,CAACoC,KAAK,CAACb,GAAG,CAACc,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,CAAC5C,MAAM,CAAC4C,KAAK,IAAIA,KAAK,KAAK,iBAAiB,CAAC;YAClG7D,qBAAqB,CAAC2D,KAAK,CAAC;UAC9B;UAEA,IAAIpC,SAAS,IAAIA,SAAS,CAACuC,SAAS,EAAE;YACpC,MAAMA,SAAS,GAAGvC,SAAS,CAACuC,SAAS,CAAChB,GAAG,CAACc,IAAI,IAAIA,IAAI,CAACxD,SAAS,CAAC,CAACa,MAAM,CAAC8C,IAAI,IAAIA,IAAI,KAAK,iBAAiB,CAAC;YAC5GtD,gBAAgB,CAACqD,SAAS,CAAC;UAC7B;UAEAhE,eAAe,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC,OAAOkE,UAAU,EAAE;UACnBrD,OAAO,CAACtB,KAAK,CAAC,2CAA2C,EAAE2E,UAAU,CAAC;UACtElE,eAAe,CAAC,KAAK,CAAC;UACtB;QACF;;QAEA;QACAa,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE4C,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMS,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACA3D,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEX,OAAO,CAAC;UAC1E,MAAMsE,WAAW,GAAGhH,WAAW,CAACuD,OAAO,CAAC0C,aAAa,EAAE,CAAC,EAAEvD,OAAO,CAAC;UAClE,MAAMY,MAAM,GAAG,MAAMqD,OAAO,CAACM,IAAI,CAAC,CAACD,WAAW,EAAEN,cAAc,CAAC,CAAC;UAEhEtD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,MAAM,CAAC;UAC5CF,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEC,MAAM,GAAGA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC;UACzE,IAAIF,MAAM,IAAIA,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;YAC/BJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,MAAM;YACLF,OAAO,CAAC4C,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;UACAxE,aAAa,CAAC6B,MAAM,IAAI,EAAE,CAAC;QAC7B,CAAC,CAAC,OAAO4D,SAAS,EAAE;UAClB9D,OAAO,CAACtB,KAAK,CAAC,yCAAyC,EAAEoF,SAAS,CAAC;UACnE9D,OAAO,CAACtB,KAAK,CAAC,8BAA8B,EAAE;YAC5CoC,OAAO,EAAEgD,SAAS,CAAChD,OAAO;YAC1BiD,MAAM,EAAED,SAAS,CAACC,MAAM;YACxBC,IAAI,EAAEF,SAAS,CAACE,IAAI;YACpBC,KAAK,EAAEH,SAAS,CAACG,KAAK;YACtBC,IAAI,EAAEJ,SAAS,CAACI,IAAI;YACpBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,QAAQ,EAAEN,SAAS,CAACM,QAAQ,GAAG;cAC7BL,MAAM,EAAED,SAAS,CAACM,QAAQ,CAACL,MAAM;cACjCM,UAAU,EAAEP,SAAS,CAACM,QAAQ,CAACC,UAAU;cACzCL,IAAI,EAAEF,SAAS,CAACM,QAAQ,CAACJ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA3F,aAAa,CAAC,EAAE,CAAC;UACjB2B,OAAO,CAAC4C,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACAjE,QAAQ,CAAC,2CAA2CmF,SAAS,CAAChD,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACAd,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE4C,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMS,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACA3D,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD;UACA,MAAMqE,YAAY,GAAG1H,WAAW,CAACuD,OAAO,CAAC0C,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAMpC,KAAK,GAAG,MAAM8C,OAAO,CAACM,IAAI,CAAC,CAACS,YAAY,EAAEhB,cAAc,CAAC,CAAC;UAEhEtD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEQ,KAAK,CAAC;UAC1CT,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEQ,KAAK,GAAGA,KAAK,CAACL,MAAM,GAAG,CAAC,CAAC;UACtE,IAAIK,KAAK,IAAIA,KAAK,CAACL,MAAM,GAAG,CAAC,EAAE;YAC7BJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEQ,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACLT,OAAO,CAAC4C,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACAtE,YAAY,CAACkC,KAAK,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,OAAOE,UAAU,EAAE;UACnBX,OAAO,CAACtB,KAAK,CAAC,wCAAwC,EAAEiC,UAAU,CAAC;UACnEX,OAAO,CAACtB,KAAK,CAAC,6BAA6B,EAAE;YAC3CoC,OAAO,EAAEH,UAAU,CAACG,OAAO;YAC3BiD,MAAM,EAAEpD,UAAU,CAACoD,MAAM;YACzBC,IAAI,EAAErD,UAAU,CAACqD,IAAI;YACrBC,KAAK,EAAEtD,UAAU,CAACsD,KAAK;YACvBC,IAAI,EAAEvD,UAAU,CAACuD,IAAI;YACrBC,IAAI,EAAExD,UAAU,CAACwD,IAAI;YACrBC,QAAQ,EAAEzD,UAAU,CAACyD,QAAQ,GAAG;cAC9BL,MAAM,EAAEpD,UAAU,CAACyD,QAAQ,CAACL,MAAM;cAClCM,UAAU,EAAE1D,UAAU,CAACyD,QAAQ,CAACC,UAAU;cAC1CL,IAAI,EAAErD,UAAU,CAACyD,QAAQ,CAACJ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACAzF,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0CgC,UAAU,CAACG,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACArC,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAO8F,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZ7E,OAAO,CAACtB,KAAK,CAAC,kCAAkC,EAAE6F,GAAG,CAAC;QACtDvE,OAAO,CAACtB,KAAK,CAAC,2BAA2B,EAAE;UACzCoC,OAAO,EAAEyD,GAAG,CAACzD,OAAO;UACpBiD,MAAM,EAAEQ,GAAG,CAACR,MAAM,MAAAS,aAAA,GAAID,GAAG,CAACH,QAAQ,cAAAI,aAAA,uBAAZA,aAAA,CAAcT,MAAM;UAC1CC,IAAI,EAAEO,GAAG,CAACP,IAAI,MAAAS,cAAA,GAAIF,GAAG,CAACH,QAAQ,cAAAK,cAAA,uBAAZA,cAAA,CAAcT,IAAI;UACpCC,KAAK,EAAEM,GAAG,CAACN;QACb,CAAC,CAAC;;QAEF;QACA,IAAIa,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAACzD,OAAO,IAAIyD,GAAG,CAACzD,OAAO,CAACiE,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjED,YAAY,GAAGP,GAAG,CAACzD,OAAO;QAC5B,CAAC,MAAM,IAAIyD,GAAG,CAACR,MAAM,KAAK,GAAG,IAAIQ,GAAG,CAACR,MAAM,KAAK,GAAG,IACzC,EAAAW,cAAA,GAAAH,GAAG,CAACH,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcX,MAAM,MAAK,GAAG,IAAI,EAAAY,cAAA,GAAAJ,GAAG,CAACH,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcZ,MAAM,MAAK,GAAG,EAAE;UACtEe,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAACH,QAAQ,cAAAQ,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,eAAlBA,mBAAA,CAAoBG,MAAM,EAAE;UACrC;UACAF,YAAY,GAAG,eAAeP,GAAG,CAACH,QAAQ,CAACJ,IAAI,CAACgB,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIT,GAAG,CAACL,IAAI,KAAK,aAAa,EAAE;UACrC;UACAY,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAACzD,OAAO,EAAE;UACtBgE,YAAY,GAAGP,GAAG,CAACzD,OAAO;QAC5B;QAEAnC,QAAQ,CAAC,gCAAgCmG,YAAY,sBAAsB,CAAC;;QAE5E;QACAzG,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDsC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACzB,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf;;EAEA;EACA,MAAM2F,iBAAiB,GAAI1E,IAAI,IAAK;IAClC1B,eAAe,CAAC0B,IAAI,CAAC;IACrBxB,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMmG,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnG,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;;EAEA;;EAEA;;EAEA;EACA,MAAMsG,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACvG,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACE3B,OAAA,CAACf,MAAM;MAACkJ,IAAI,EAAEtG,iBAAkB;MAACuG,OAAO,EAAEH,kBAAmB;MAACI,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAC,QAAA,gBACnFvI,OAAA,CAACd,WAAW;QAAAqJ,QAAA,GAAC,iBACI,EAAC5G,YAAY,CAAC6G,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACd5I,OAAA,CAACb,aAAa;QAAC0J,QAAQ;QAAAN,QAAA,eACrBvI,OAAA,CAACvB,IAAI;UAACqK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAR,QAAA,gBACzBvI,OAAA,CAACvB,IAAI;YAACuH,IAAI;YAACgD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvBvI,OAAA,CAAC1B,UAAU;cAAC4K,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/E5I,OAAA,CAAC3B,GAAG;cAAC+K,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBvI,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAAC2H,OAAO,IAAI,KAAK;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClG5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAAC4H,OAAO,IAAI,KAAK;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClG5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAACa,SAAS,IAAI,KAAK;cAAA;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtG5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAAC6H,WAAW,IAAI,KAAK;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrG5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAAC8H,YAAY,IAAI,KAAK;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC7G5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAAC+H,OAAO,IAAI,KAAK;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClG5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAACgI,EAAE,IAAI,KAAK;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eAEN5I,OAAA,CAAC1B,UAAU;cAAC4K,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClE5I,OAAA,CAAC3B,GAAG;cAAC+K,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBvI,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAACiI,mBAAmB,IAAI,KAAK;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjH5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAACkI,eAAe,IAAI,KAAK;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzG5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAACmI,2BAA2B,IAAI,KAAK;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1H5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAACoI,qBAAqB,IAAI,KAAK;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrH5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAACqI,gBAAgB,IAAI,KAAK;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEP5I,OAAA,CAACvB,IAAI;YAACuH,IAAI;YAACgD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvBvI,OAAA,CAAC1B,UAAU;cAAC4K,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChE5I,OAAA,CAAC3B,GAAG;cAAC+K,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBvI,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAACsI,iBAAiB,IAAI,KAAK;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/G5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAACuI,aAAa,IAAI,KAAK;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvG5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAACwI,yBAAyB,IAAI,KAAK;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxH5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAACyI,mBAAmB,IAAI,KAAK;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnH5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAAC0I,cAAc,IAAI,KAAK;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CAAC,eAEN5I,OAAA,CAAC1B,UAAU;cAAC4K,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvE5I,OAAA,CAAC3B,GAAG;cAAC+K,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBvI,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAAC2I,aAAa,IAAI,KAAK;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9G5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAAC4I,eAAe,IAAI,GAAG;cAAA;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChH5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC/I,2BAA2B,CAAC8B,YAAY,CAACY,mBAAmB,CAAC;cAAA;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChI5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAAC6I,YAAY,IAAI,GAAG;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1G5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAAC8I,SAAS,IAAI,KAAK;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnG5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAAC+I,iBAAiB,IAAI,KAAK;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtH5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjH,YAAY,CAACgJ,YAAY,IAAI,KAAK;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC5G5I,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACvI,OAAA;kBAAAuI,QAAA,EAAQ;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIgC,IAAI,CAACjJ,YAAY,CAACkJ,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB5I,OAAA,CAACZ,aAAa;QAAAmJ,QAAA,eACZvI,OAAA,CAACxB,MAAM;UAACuM,OAAO,EAAE9C,kBAAmB;UAAAM,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;;EAEA;EACA,MAAMoC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACjJ,KAAK,EAAE,OAAO,IAAI;IAEvB,oBACE/B,OAAA,CAACzB,KAAK;MAAC6K,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAE4B,CAAC,EAAE;MAAE,CAAE;MAAA1C,QAAA,gBACzBvI,OAAA,CAAC1B,UAAU;QAAC4K,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAZ,QAAA,EAAC;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAC7D3G,YAAY,gBACXjC,OAAA,CAAChB,cAAc;QAAAyJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAElB5I,OAAA,CAACvB,IAAI;QAACqK,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAR,QAAA,gBACzBvI,OAAA,CAACvB,IAAI;UAACuH,IAAI;UAACgD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBvI,OAAA,CAAC1B,UAAU;YAAC4K,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChE5I,OAAA,CAAC1B,UAAU;YAAC4K,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,eAAa,EAACxG,KAAK,CAACmJ,MAAM,CAACC,WAAW;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAChF5I,OAAA,CAAC1B,UAAU;YAAC4K,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,cAAY,EAACxG,KAAK,CAACmJ,MAAM,CAACE,UAAU;UAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC9E5I,OAAA,CAAC1B,UAAU;YAAC4K,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,eAAa,EAACxG,KAAK,CAACmJ,MAAM,CAACG,WAAW;UAAA;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE5E,CAAC,eAEP5I,OAAA,CAACvB,IAAI;UAACuH,IAAI;UAACgD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBvI,OAAA,CAAC1B,UAAU;YAAC4K,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnE5I,OAAA,CAAC1B,UAAU;YAAC4K,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,iBAAe,EAACxG,KAAK,CAACuJ,SAAS,CAACC,oBAAoB,CAACC,OAAO,CAAC,CAAC,CAAC;UAAA;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzG5I,OAAA,CAAC1B,UAAU;YAAC4K,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,gBAAc,EAACxG,KAAK,CAACuJ,SAAS,CAACG,kBAAkB,CAACD,OAAO,CAAC,CAAC,CAAC;UAAA;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACtG5I,OAAA,CAAC3B,GAAG;YAAC+K,EAAE,EAAE;cAAEsC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAArD,QAAA,gBACxDvI,OAAA,CAAC3B,GAAG;cAAC+K,EAAE,EAAE;gBAAEyC,KAAK,EAAE,MAAM;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAvD,QAAA,eAChCvI,OAAA,CAAChB,cAAc;gBACbkK,OAAO,EAAC,aAAa;gBACrB6C,KAAK,EAAEhK,KAAK,CAACuJ,SAAS,CAACU,yBAA0B;gBACjD5C,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEC,YAAY,EAAE;gBAAE;cAAE;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5I,OAAA,CAAC3B,GAAG;cAAC+K,EAAE,EAAE;gBAAE+C,QAAQ,EAAE;cAAG,CAAE;cAAA5D,QAAA,eACxBvI,OAAA,CAAC1B,UAAU;gBAAC4K,OAAO,EAAC,OAAO;gBAACkD,KAAK,EAAC,gBAAgB;gBAAA7D,QAAA,EAAE,GAAGxG,KAAK,CAACuJ,SAAS,CAACU,yBAAyB,CAACR,OAAO,CAAC,CAAC,CAAC;cAAG;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP5I,OAAA,CAACvB,IAAI;UAACuH,IAAI;UAACgD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBvI,OAAA,CAAC1B,UAAU;YAAC4K,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/D5I,OAAA,CAAC3B,GAAG;YAAC+K,EAAE,EAAE;cAAEsC,OAAO,EAAE,MAAM;cAAEW,QAAQ,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAA/D,QAAA,EACpDxG,KAAK,CAACgE,KAAK,CAACb,GAAG,CAAC,CAACe,KAAK,EAAEsG,KAAK,kBAC5BvM,OAAA,CAAClB,IAAI;cAEH0N,KAAK,EAAE,GAAGvG,KAAK,CAACA,KAAK,KAAKA,KAAK,CAACwG,KAAK,EAAG;cACxCC,IAAI,EAAC,OAAO;cACZ3B,OAAO,EAAEA,CAAA,KAAM;gBACbzI,UAAU,CAACqK,IAAI,KAAK;kBAClB,GAAGA,IAAI;kBACPpK,mBAAmB,EAAE0D,KAAK,CAACA,KAAK,KAAK,iBAAiB,GAAG,EAAE,GAAGA,KAAK,CAACA;gBACtE,CAAC,CAAC,CAAC;cACL;YAAE,GARGsG,KAAK;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEZ,CAAC;EAED,oBACE5I,OAAA,CAAC3B,GAAG;IAACuO,SAAS,EAAC,WAAW;IAAArE,QAAA,EACvBhH,OAAO,gBACNvB,OAAA,CAAC3B,GAAG;MAAC+K,EAAE,EAAE;QAAEsC,OAAO,EAAE,MAAM;QAAEmB,aAAa,EAAE,QAAQ;QAAElB,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAArD,QAAA,gBACjFvI,OAAA,CAACjB,gBAAgB;QAAC2N,IAAI,EAAE;MAAG;QAAAjE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9B5I,OAAA,CAAC1B,UAAU;QAAC8K,EAAE,EAAE;UAAEwC,EAAE,EAAE;QAAE,CAAE;QAAArD,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3D5I,OAAA,CAACxB,MAAM;QACL0K,OAAO,EAAC,UAAU;QAClBkD,KAAK,EAAC,SAAS;QACfrB,OAAO,EAAEA,CAAA,KAAM+B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxC5D,EAAE,EAAE;UAAEwC,EAAE,EAAE;QAAE,CAAE;QAAArD,QAAA,EACf;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJnH,KAAK,gBACPzB,OAAA,CAAC3B,GAAG;MAAAkK,QAAA,gBACFvI,OAAA,CAACpB,KAAK;QAACqO,QAAQ,EAAC,OAAO;QAAC7D,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,GACnC9G,KAAK,EACLA,KAAK,CAACqG,QAAQ,CAAC,eAAe,CAAC,iBAC9B9H,OAAA,CAAC1B,UAAU;UAAC4K,OAAO,EAAC,OAAO;UAACE,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAArD,QAAA,gBACxCvI,OAAA;YAAAuI,QAAA,EAAQ;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAA5I,OAAA;YAAAyI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAA5I,OAAA;YAAAuI,QAAA,EAAM;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACR5I,OAAA,CAAC3B,GAAG;QAAC+K,EAAE,EAAE;UAAEsC,OAAO,EAAE,MAAM;UAAEY,GAAG,EAAE;QAAE,CAAE;QAAA/D,QAAA,eACnCvI,OAAA,CAACxB,MAAM;UACL0K,OAAO,EAAC,WAAW;UACnB0D,SAAS,EAAC,gBAAgB;UAC1B7B,OAAO,EAAEA,CAAA,KAAM+B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAzE,QAAA,EACzC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAEN5I,OAAA,CAAC3B,GAAG;MAAAkK,QAAA,GAIDyC,gBAAgB,CAAC,CAAC,eAGnBhL,OAAA,CAAC3B,GAAG;QAAC+K,EAAE,EAAE;UAAEwC,EAAE,EAAE;QAAE,CAAE;QAAArD,QAAA,eACjBvI,OAAA,CAAC3B,GAAG;UAAC+K,EAAE,EAAE;YAAEsC,OAAO,EAAE,MAAM;YAAEwB,cAAc,EAAE,eAAe;YAAEvB,UAAU,EAAE,QAAQ;YAAEtC,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACzFvI,OAAA,CAAC1B,UAAU;YAAC4K,OAAO,EAAC,IAAI;YAAAX,QAAA,GAAC,cACX,EAACpH,UAAU,CAACgC,MAAM,GAAG,CAAC,GAAG,IAAIhC,UAAU,CAACgC,MAAM,GAAG,GAAG,EAAE;UAAA;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACb5I,OAAA,CAACxB,MAAM;YACL0K,OAAO,EAAC,UAAU;YAClBwD,IAAI,EAAC,OAAO;YACZ3B,OAAO,EAAEA,CAAA,KAAM+B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxCG,SAAS,EAAE5L,OAAO,gBAAGvB,OAAA,CAACjB,gBAAgB;cAAC2N,IAAI,EAAE;YAAG;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5I,OAAA,CAACV,WAAW;cAAAmJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtEwE,QAAQ,EAAE7L,OAAQ;YAAAgH,QAAA,EACnB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELzH,UAAU,CAACgC,MAAM,GAAG,CAAC,gBACpBnD,OAAA,CAAC3B,GAAG;QAAC+K,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,GAEhB8E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCvN,OAAA,CAAC3B,GAAG;UAAC+K,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAE4B,CAAC,EAAE,CAAC;YAAEuC,OAAO,EAAE,SAAS;YAAEtB,YAAY,EAAE,CAAC;YAAEuB,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE,WAAW;YAAEhC,OAAO,EAAE;UAAO,CAAE;UAAAnD,QAAA,EACzHoF,MAAM,CAACC,IAAI,CAACzM,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC+D,GAAG,CAACb,GAAG,iBACjCrE,OAAA;YAAAuI,QAAA,GAAgBlE,GAAG,EAAC,IAAE,EAACmB,IAAI,CAACqI,SAAS,CAAC1M,UAAU,CAAC,CAAC,CAAC,CAACkD,GAAG,CAAC,CAAC;UAAA,GAA/CA,GAAG;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkD,CAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eACD5I,OAAA,CAACF,mBAAmB;UAClBgO,IAAI,EAAE3M,UAAW;UACjBI,OAAO,EAAEA,OAAQ;UACjBwM,oBAAoB,EAAGC,YAAY,IAAKjL,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgL,YAAY,CAAC7K,MAAM,CAAE;UAClG8K,iBAAiB,EAAE,EAAA9N,YAAA,GAAAgB,UAAU,CAAC,CAAC,CAAC,cAAAhB,YAAA,uBAAbA,YAAA,CAAe+N,mBAAmB,OAAA9N,aAAA,GAAIe,UAAU,CAAC,CAAC,CAAC,cAAAf,aAAA,uBAAbA,aAAA,CAAe+N,SAAS,OAAA9N,aAAA,GAAIc,UAAU,CAAC,CAAC,CAAC,cAAAd,aAAA,uBAAbA,aAAA,CAAe+N,GAAG;QAAC;UAAA3F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAEN5I,OAAA,CAACpB,KAAK;QAACqO,QAAQ,EAAC,MAAM;QAAC7D,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,EAAC;MAEtC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,eAGD5I,OAAA,CAAC3B,GAAG;QAAC+K,EAAE,EAAE;UAAEwC,EAAE,EAAE;QAAE,CAAE;QAAArD,QAAA,gBACjBvI,OAAA,CAAC3B,GAAG;UAAC+K,EAAE,EAAE;YAAEsC,OAAO,EAAE,MAAM;YAAEwB,cAAc,EAAE,eAAe;YAAEvB,UAAU,EAAE,QAAQ;YAAEtC,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACzFvI,OAAA,CAAC1B,UAAU;YAAC4K,OAAO,EAAC,IAAI;YAAAX,QAAA,GAAC,aACZ,EAAClH,SAAS,CAAC8B,MAAM,GAAG,CAAC,GAAG,IAAI9B,SAAS,CAAC8B,MAAM,GAAG,GAAG,EAAE;UAAA;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACb5I,OAAA,CAACxB,MAAM;YACL0K,OAAO,EAAC,UAAU;YAClBwD,IAAI,EAAC,OAAO;YACZ3B,OAAO,EAAEA,CAAA,KAAM+B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxCG,SAAS,EAAE5L,OAAO,gBAAGvB,OAAA,CAACjB,gBAAgB;cAAC2N,IAAI,EAAE;YAAG;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5I,OAAA,CAACV,WAAW;cAAAmJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtEwE,QAAQ,EAAE7L,OAAQ;YAAAgH,QAAA,EACnB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACLvH,SAAS,CAAC8B,MAAM,GAAG,CAAC,gBACnBnD,OAAA,CAACF,mBAAmB;UAClBgO,IAAI,EAAEzM,SAAU;UAChBE,OAAO,EAAEA,OAAQ;UACjBwM,oBAAoB,EAAGC,YAAY,IAAKjL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgL,YAAY,CAAC7K,MAAM;QAAE;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC,gBAEF5I,OAAA,CAACpB,KAAK;UAACqO,QAAQ,EAAC,MAAM;UAAC7D,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,EAAC;QAEtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAKLV,mBAAmB,CAAC,CAAC,eAGtBlI,OAAA,CAACf,MAAM;QACLkJ,IAAI,EAAE3H,qBAAsB;QAC5B4H,OAAO,EAAEA,CAAA,KAAM3H,wBAAwB,CAAC,KAAK,CAAE;QAC/C6H,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAE,QAAA,eAEbvI,OAAA,CAACN,oBAAoB;UACnBqB,UAAU,EAAEA,UAAW;UACvBsN,SAAS,EAAGxK,OAAO,IAAK;YACtB;YACApD,wBAAwB,CAAC,KAAK,CAAC;;YAE/B;YACA,IAAIoD,OAAO,EAAE;cACX;cACAd,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEa,OAAO,CAAC;cAC9C;cACAyK,KAAK,CAACzK,OAAO,CAAC;cACd;cACA4C,UAAU,CAAC,MAAM;gBACf1D,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD;gBACA8J,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;cAC1B,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACAjK,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFuL,OAAO,EAAG1K,OAAO,IAAK;YACpB;YACAd,OAAO,CAACtB,KAAK,CAAC,0CAA0C,EAAEoC,OAAO,CAAC;YAClE;YACAyK,KAAK,CAAC,WAAWzK,OAAO,EAAE,CAAC;YAC3B;YACApD,wBAAwB,CAAC,KAAK,CAAC;YAC/B;YACAqC,SAAS,CAAC,CAAC;UACb,CAAE;UACF0L,aAAa,EAAC;QAAa;UAAA/F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAIRlI,sBAAsB,IAAIqC,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEjC,UAAU,CAAC,eAEhHf,OAAA,CAACf,MAAM;QACLkJ,IAAI,EAAEzH,sBAAuB;QAC7B0H,OAAO,EAAEA,CAAA,KAAMzH,yBAAyB,CAAC,KAAK,CAAE;QAChD2H,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAE,QAAA,eAEbvI,OAAA,CAACN,oBAAoB;UACnBqB,UAAU,EAAEA,UAAW;UACvBsN,SAAS,EAAGxK,OAAO,IAAK;YACtB;YACAlD,yBAAyB,CAAC,KAAK,CAAC;;YAEhC;YACA,IAAIkD,OAAO,EAAE;cACX;cACAd,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEa,OAAO,CAAC;cAC9C;cACAyK,KAAK,CAACzK,OAAO,CAAC;cACd;cACA4C,UAAU,CAAC,MAAM;gBACf1D,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD,IAAI;kBACF;kBACAF,SAAS,CAAC,CAAC;gBACb,CAAC,CAAC,OAAOrB,KAAK,EAAE;kBACdsB,OAAO,CAACtB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACAgF,UAAU,CAAC,MAAM;oBACf1D,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;oBACzD8J,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;kBAC1B,CAAC,EAAE,IAAI,CAAC;gBACV;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACAjK,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFuL,OAAO,EAAG1K,OAAO,IAAK;YACpB;YACAd,OAAO,CAACtB,KAAK,CAAC,sCAAsC,EAAEoC,OAAO,CAAC;YAC9D;YACAyK,KAAK,CAAC,WAAWzK,OAAO,EAAE,CAAC;YAC3B;YACAlD,yBAAyB,CAAC,KAAK,CAAC;YAChC;YACA8F,UAAU,CAAC,MAAM;cACf1D,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;cAChD,IAAI;gBACFF,SAAS,CAAC,CAAC;cACb,CAAC,CAAC,OAAO2L,WAAW,EAAE;gBACpB1L,OAAO,CAACtB,KAAK,CAAC,uDAAuD,EAAEgN,WAAW,CAAC;gBACnF;gBACAhI,UAAU,CAAC,MAAM;kBACf1D,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;kBACrE8J,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B,CAAC,EAAE,IAAI,CAAC;cACV;YACF,CAAC,EAAE,IAAI,CAAC;UACV,CAAE;UACFwB,aAAa,EAAC;QAAc;UAAA/F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGT5I,OAAA,CAACf,MAAM;QACLkJ,IAAI,EAAEvH,sBAAuB;QAC7BwH,OAAO,EAAEA,CAAA,KAAMvH,yBAAyB,CAAC,KAAK,CAAE;QAChDyH,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAE,QAAA,gBAEbvI,OAAA,CAACd,WAAW;UAAAqJ,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC9C5I,OAAA,CAACb,aAAa;UAAAoJ,QAAA,eACZvI,OAAA,CAAC3B,GAAG;YAAC+K,EAAE,EAAE;cAAEwC,EAAE,EAAE;YAAE,CAAE;YAAArD,QAAA,eACjBvI,OAAA,CAACJ,gBAAgB;cACfmB,UAAU,EAAEA,UAAW;cACvBsN,SAAS,EAAGxK,OAAO,IAAK;gBACtB;gBACAhD,yBAAyB,CAAC,KAAK,CAAC;gBAChC;gBACAyN,KAAK,CAACzK,OAAO,CAAC;gBACd;gBACA4C,UAAU,CAAC,MAAM;kBACf1D,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;kBACpD,IAAI;oBACF;oBACAF,SAAS,CAAC,CAAC;kBACb,CAAC,CAAC,OAAOrB,KAAK,EAAE;oBACdsB,OAAO,CAACtB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;oBACjE;oBACAgF,UAAU,CAAC,MAAM;sBACf1D,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;sBACzD8J,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;oBAC1B,CAAC,EAAE,IAAI,CAAC;kBACV;gBACF,CAAC,EAAE,IAAI,CAAC;cACV,CAAE;cACFuB,OAAO,EAAG1K,OAAO,IAAK;gBACpB;gBACAd,OAAO,CAACtB,KAAK,CAAC,sCAAsC,EAAEoC,OAAO,CAAC;gBAC9D;gBACAyK,KAAK,CAAC,WAAWzK,OAAO,EAAE,CAAC;cAC7B,CAAE;cACF6K,QAAQ,EAAE;YAAK;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChB5I,OAAA,CAACZ,aAAa;UAAAmJ,QAAA,eACZvI,OAAA,CAACxB,MAAM;YAACuM,OAAO,EAAEA,CAAA,KAAMlK,yBAAyB,CAAC,KAAK,CAAE;YAAA0H,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1I,EAAA,CA7xBID,kBAAkB;EAAA,QACYT,OAAO,EACyHC,gBAAgB,EACjKF,WAAW;AAAA;AAAAoP,EAAA,GAHxB1O,kBAAkB;AA+xBxB,eAAeA,kBAAkB;AAAC,IAAA0O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}