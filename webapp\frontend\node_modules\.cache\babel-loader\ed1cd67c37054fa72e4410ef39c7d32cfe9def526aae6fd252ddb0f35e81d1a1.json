{"ast": null, "code": "import { formatDistance } from \"./it/_lib/formatDistance.mjs\";\nimport { formatRelative } from \"./it/_lib/formatRelative.mjs\";\nimport { localize } from \"./it/_lib/localize.mjs\";\nimport { match } from \"./it/_lib/match.mjs\";\nimport { formatLong } from \"./it-CH/_lib/formatLong.mjs\";\n\n/**\n * @category Locales\n * @summary Italian locale (Switzerland).\n * @language Italian\n * @iso-639-2 ita\n * <AUTHOR> [@maic66](https://github.com/maic66)\n */\nexport const itCH = {\n  code: \"it-CH\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default itCH;", "map": {"version": 3, "names": ["formatDistance", "formatRelative", "localize", "match", "formatLong", "itCH", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/it-CH.mjs"], "sourcesContent": ["import { formatDistance } from \"./it/_lib/formatDistance.mjs\";\nimport { formatRelative } from \"./it/_lib/formatRelative.mjs\";\nimport { localize } from \"./it/_lib/localize.mjs\";\nimport { match } from \"./it/_lib/match.mjs\";\nimport { formatLong } from \"./it-CH/_lib/formatLong.mjs\";\n\n/**\n * @category Locales\n * @summary Italian locale (Switzerland).\n * @language Italian\n * @iso-639-2 ita\n * <AUTHOR> [@maic66](https://github.com/maic66)\n */\nexport const itCH = {\n  code: \"it-CH\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default itCH;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,UAAU,QAAQ,6BAA6B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BI,UAAU,EAAEA,UAAU;EACtBH,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZI,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}