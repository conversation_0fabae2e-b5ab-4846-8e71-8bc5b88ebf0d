{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12.5 3.5c0-1.1.9-2 2-2s2 .9 2 2-.9 2-2 2-2-.9-2-2m7.5 8.41c0-.49-.36-.9-.84-.98-1.53-.25-2.79-1.16-3.47-2.35l-1-1.58c-.4-.6-1-1-1.7-1-.68 0-1.28.28-4.77 1.76C7.49 8.07 7 8.8 7 9.6V12c0 .55.45 1 1 1s1-.45 1-1V9.58l1.79-.7L9.2 17l-2.88 2.03-1.14-1.47L4 18.5l2.38 3.04c.51.65 1.16 1.15 1.88 1.41.28.1.53.04.72-.11.3-.23.42-.7.12-1.07-.08-.1-.2-.17-.31-.22-.43-.18-.82-.45-1.14-.83l-.08-.1 2.85-2.01c.38-.27.65-.66.77-1.1l.7-2.53 2.11 2v4.52h-2V23h3.87c.82 0 1.61-.21 2.26-.61.26-.16.37-.39.37-.64 0-.38-.3-.75-.77-.75-.13 0-.26.04-.37.1-.4.23-.87.37-1.36.4v-5.16c0-.55-.23-1.07-.62-1.45l-1.49-1.41.6-3c1.07 1.24 2.63 2.15 4.37 2.43.6.1 1.14-.39 1.14-1\"\n}), 'SnowshoeingRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/icons-material/esm/SnowshoeingRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M12.5 3.5c0-1.1.9-2 2-2s2 .9 2 2-.9 2-2 2-2-.9-2-2m7.5 8.41c0-.49-.36-.9-.84-.98-1.53-.25-2.79-1.16-3.47-2.35l-1-1.58c-.4-.6-1-1-1.7-1-.68 0-1.28.28-4.77 1.76C7.49 8.07 7 8.8 7 9.6V12c0 .55.45 1 1 1s1-.45 1-1V9.58l1.79-.7L9.2 17l-2.88 2.03-1.14-1.47L4 18.5l2.38 3.04c.51.65 1.16 1.15 1.88 1.41.28.1.53.04.72-.11.3-.23.42-.7.12-1.07-.08-.1-.2-.17-.31-.22-.43-.18-.82-.45-1.14-.83l-.08-.1 2.85-2.01c.38-.27.65-.66.77-1.1l.7-2.53 2.11 2v4.52h-2V23h3.87c.82 0 1.61-.21 2.26-.61.26-.16.37-.39.37-.64 0-.38-.3-.75-.77-.75-.13 0-.26.04-.37.1-.4.23-.87.37-1.36.4v-5.16c0-.55-.23-1.07-.62-1.45l-1.49-1.41.6-3c1.07 1.24 2.63 2.15 4.37 2.43.6.1 1.14-.39 1.14-1\"\n}), 'SnowshoeingRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}