{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Typography,Paper,Button,Accordion,AccordionSummary,AccordionDetails,Table,TableBody,TableCell,TableContainer,TableHead,TableRow}from'@mui/material';import{ExpandMore as ExpandMoreIcon,Refresh as RefreshIcon}from'@mui/icons-material';import userService from'../../services/userService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DatabaseView=()=>{const[dbData,setDbData]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState('');// Carica i dati del database\nconst loadDbData=async()=>{setLoading(true);try{const data=await userService.getDbRaw();setDbData(data);setError('');}catch(err){setError(err.detail||'Errore durante il caricamento dei dati del database');}finally{setLoading(false);}};// Carica i dati all'avvio del componente\nuseEffect(()=>{loadDbData();},[]);return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Visualizzazione Database Raw\"}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(RefreshIcon,{}),onClick:loadDbData,disabled:loading,children:\"Aggiorna\"})]}),error&&/*#__PURE__*/_jsx(Typography,{color:\"error\",sx:{mb:2},children:error}),loading?/*#__PURE__*/_jsx(Typography,{children:\"Caricamento dati...\"}):!dbData?/*#__PURE__*/_jsx(Typography,{children:\"Nessun dato disponibile\"}):/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",children:\"Tabella Utenti\"})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsx(TableContainer,{component:Paper,children:/*#__PURE__*/_jsxs(Table,{size:\"small\",children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"ID\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Username\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Password\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Ruolo\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Data Scadenza\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Abilitato\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Creato Da\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:dbData.users.map(user=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:user.id_utente}),/*#__PURE__*/_jsx(TableCell,{children:user.username}),/*#__PURE__*/_jsx(TableCell,{children:user.password}),/*#__PURE__*/_jsx(TableCell,{children:user.ruolo}),/*#__PURE__*/_jsx(TableCell,{children:user.data_scadenza}),/*#__PURE__*/_jsx(TableCell,{children:user.abilitato?'Sì':'No'}),/*#__PURE__*/_jsx(TableCell,{children:user.created_by})]},user.id_utente))})]})})})]})})]});};export default DatabaseView;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Accordion", "AccordionSummary", "AccordionDetails", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "ExpandMore", "ExpandMoreIcon", "Refresh", "RefreshIcon", "userService", "jsx", "_jsx", "jsxs", "_jsxs", "DatabaseView", "dbData", "setDbData", "loading", "setLoading", "error", "setError", "loadDbData", "data", "getDbRaw", "err", "detail", "children", "sx", "display", "justifyContent", "alignItems", "mb", "variant", "startIcon", "onClick", "disabled", "color", "defaultExpanded", "expandIcon", "component", "size", "users", "map", "user", "id_utente", "username", "password", "ruolo", "data_scadenza", "abilitato", "created_by"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/admin/DatabaseView.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow\n} from '@mui/material';\nimport {\n  ExpandMore as ExpandMoreIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport userService from '../../services/userService';\n\nconst DatabaseView = () => {\n  const [dbData, setDbData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n\n  // Carica i dati del database\n  const loadDbData = async () => {\n    setLoading(true);\n    try {\n      const data = await userService.getDbRaw();\n      setDbData(data);\n      setError('');\n    } catch (err) {\n      setError(err.detail || 'Errore durante il caricamento dei dati del database');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente\n  useEffect(() => {\n    loadDbData();\n  }, []);\n\n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n        <Typography variant=\"h6\">Visualizzazione Database Raw</Typography>\n        <Button\n          variant=\"outlined\"\n          startIcon={<RefreshIcon />}\n          onClick={loadDbData}\n          disabled={loading}\n        >\n          Aggiorna\n        </Button>\n      </Box>\n      \n      {error && (\n        <Typography color=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Typography>\n      )}\n      \n      {loading ? (\n        <Typography>Caricamento dati...</Typography>\n      ) : !dbData ? (\n        <Typography>Nessun dato disponibile</Typography>\n      ) : (\n        <Box>\n          {/* Tabella Utenti */}\n          <Accordion defaultExpanded>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Typography variant=\"subtitle1\">Tabella Utenti</Typography>\n            </AccordionSummary>\n            <AccordionDetails>\n              <TableContainer component={Paper}>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>ID</TableCell>\n                      <TableCell>Username</TableCell>\n                      <TableCell>Password</TableCell>\n                      <TableCell>Ruolo</TableCell>\n                      <TableCell>Data Scadenza</TableCell>\n                      <TableCell>Abilitato</TableCell>\n                      <TableCell>Creato Da</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {dbData.users.map((user) => (\n                      <TableRow key={user.id_utente}>\n                        <TableCell>{user.id_utente}</TableCell>\n                        <TableCell>{user.username}</TableCell>\n                        <TableCell>{user.password}</TableCell>\n                        <TableCell>{user.ruolo}</TableCell>\n                        <TableCell>{user.data_scadenza}</TableCell>\n                        <TableCell>{user.abilitato ? 'Sì' : 'No'}</TableCell>\n                        <TableCell>{user.created_by}</TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            </AccordionDetails>\n          </Accordion>\n          \n          {/* Altre tabelle possono essere aggiunte qui in futuro */}\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default DatabaseView;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,SAAS,CACTC,gBAAgB,CAChBC,gBAAgB,CAChBC,KAAK,CACLC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,SAAS,CACTC,QAAQ,KACH,eAAe,CACtB,OACEC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,OAAO,GAAI,CAAAC,WAAW,KACjB,qBAAqB,CAC5B,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErD,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAAC2B,OAAO,CAAEC,UAAU,CAAC,CAAG5B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC6B,KAAK,CAAEC,QAAQ,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CAEtC;AACA,KAAM,CAAA+B,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7BH,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAI,IAAI,CAAG,KAAM,CAAAb,WAAW,CAACc,QAAQ,CAAC,CAAC,CACzCP,SAAS,CAACM,IAAI,CAAC,CACfF,QAAQ,CAAC,EAAE,CAAC,CACd,CAAE,MAAOI,GAAG,CAAE,CACZJ,QAAQ,CAACI,GAAG,CAACC,MAAM,EAAI,qDAAqD,CAAC,CAC/E,CAAC,OAAS,CACRP,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA3B,SAAS,CAAC,IAAM,CACd8B,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,mBACER,KAAA,CAACrB,GAAG,EAAAkC,QAAA,eACFb,KAAA,CAACrB,GAAG,EAACmC,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eACzFf,IAAA,CAAClB,UAAU,EAACuC,OAAO,CAAC,IAAI,CAAAN,QAAA,CAAC,8BAA4B,CAAY,CAAC,cAClEf,IAAA,CAAChB,MAAM,EACLqC,OAAO,CAAC,UAAU,CAClBC,SAAS,cAAEtB,IAAA,CAACH,WAAW,GAAE,CAAE,CAC3B0B,OAAO,CAAEb,UAAW,CACpBc,QAAQ,CAAElB,OAAQ,CAAAS,QAAA,CACnB,UAED,CAAQ,CAAC,EACN,CAAC,CAELP,KAAK,eACJR,IAAA,CAAClB,UAAU,EAAC2C,KAAK,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEI,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,CACrCP,KAAK,CACI,CACb,CAEAF,OAAO,cACNN,IAAA,CAAClB,UAAU,EAAAiC,QAAA,CAAC,qBAAmB,CAAY,CAAC,CAC1C,CAACX,MAAM,cACTJ,IAAA,CAAClB,UAAU,EAAAiC,QAAA,CAAC,yBAAuB,CAAY,CAAC,cAEhDf,IAAA,CAACnB,GAAG,EAAAkC,QAAA,cAEFb,KAAA,CAACjB,SAAS,EAACyC,eAAe,MAAAX,QAAA,eACxBf,IAAA,CAACd,gBAAgB,EAACyC,UAAU,cAAE3B,IAAA,CAACL,cAAc,GAAE,CAAE,CAAAoB,QAAA,cAC/Cf,IAAA,CAAClB,UAAU,EAACuC,OAAO,CAAC,WAAW,CAAAN,QAAA,CAAC,gBAAc,CAAY,CAAC,CAC3C,CAAC,cACnBf,IAAA,CAACb,gBAAgB,EAAA4B,QAAA,cACff,IAAA,CAACT,cAAc,EAACqC,SAAS,CAAE7C,KAAM,CAAAgC,QAAA,cAC/Bb,KAAA,CAACd,KAAK,EAACyC,IAAI,CAAC,OAAO,CAAAd,QAAA,eACjBf,IAAA,CAACR,SAAS,EAAAuB,QAAA,cACRb,KAAA,CAACT,QAAQ,EAAAsB,QAAA,eACPf,IAAA,CAACV,SAAS,EAAAyB,QAAA,CAAC,IAAE,CAAW,CAAC,cACzBf,IAAA,CAACV,SAAS,EAAAyB,QAAA,CAAC,UAAQ,CAAW,CAAC,cAC/Bf,IAAA,CAACV,SAAS,EAAAyB,QAAA,CAAC,UAAQ,CAAW,CAAC,cAC/Bf,IAAA,CAACV,SAAS,EAAAyB,QAAA,CAAC,OAAK,CAAW,CAAC,cAC5Bf,IAAA,CAACV,SAAS,EAAAyB,QAAA,CAAC,eAAa,CAAW,CAAC,cACpCf,IAAA,CAACV,SAAS,EAAAyB,QAAA,CAAC,WAAS,CAAW,CAAC,cAChCf,IAAA,CAACV,SAAS,EAAAyB,QAAA,CAAC,WAAS,CAAW,CAAC,EACxB,CAAC,CACF,CAAC,cACZf,IAAA,CAACX,SAAS,EAAA0B,QAAA,CACPX,MAAM,CAAC0B,KAAK,CAACC,GAAG,CAAEC,IAAI,eACrB9B,KAAA,CAACT,QAAQ,EAAAsB,QAAA,eACPf,IAAA,CAACV,SAAS,EAAAyB,QAAA,CAAEiB,IAAI,CAACC,SAAS,CAAY,CAAC,cACvCjC,IAAA,CAACV,SAAS,EAAAyB,QAAA,CAAEiB,IAAI,CAACE,QAAQ,CAAY,CAAC,cACtClC,IAAA,CAACV,SAAS,EAAAyB,QAAA,CAAEiB,IAAI,CAACG,QAAQ,CAAY,CAAC,cACtCnC,IAAA,CAACV,SAAS,EAAAyB,QAAA,CAAEiB,IAAI,CAACI,KAAK,CAAY,CAAC,cACnCpC,IAAA,CAACV,SAAS,EAAAyB,QAAA,CAAEiB,IAAI,CAACK,aAAa,CAAY,CAAC,cAC3CrC,IAAA,CAACV,SAAS,EAAAyB,QAAA,CAAEiB,IAAI,CAACM,SAAS,CAAG,IAAI,CAAG,IAAI,CAAY,CAAC,cACrDtC,IAAA,CAACV,SAAS,EAAAyB,QAAA,CAAEiB,IAAI,CAACO,UAAU,CAAY,CAAC,GAP3BP,IAAI,CAACC,SAQV,CACX,CAAC,CACO,CAAC,EACP,CAAC,CACM,CAAC,CACD,CAAC,EACV,CAAC,CAGT,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA9B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}