{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"eeee 'passat a' p\",\n  yesterday: \"'ièr a' p\",\n  today: \"'uèi a' p\",\n  tomorrow: \"'deman a' p\",\n  nextWeek: \"eeee 'a' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/oc/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"eeee 'passat a' p\",\n  yesterday: \"'ièr a' p\",\n  today: \"'uèi a' p\",\n  tomorrow: \"'deman a' p\",\n  nextWeek: \"eeee 'a' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,mBAAmB;EAC7BC,SAAS,EAAE,WAAW;EACtBC,KAAK,EAAE,WAAW;EAClBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAE,YAAY;EACtBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}