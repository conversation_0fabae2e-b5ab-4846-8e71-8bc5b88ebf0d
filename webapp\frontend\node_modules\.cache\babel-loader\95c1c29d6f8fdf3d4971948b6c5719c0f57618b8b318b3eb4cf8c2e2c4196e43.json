{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\nconst API_URL = config.API_URL;\nconst comandeService = {\n  // Ottiene la lista delle comande di un cantiere\n  getComande: async (cantiereId, params = {}) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}`, {\n        params\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Get comande error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea una nuova comanda\n  createComanda: async (cantiereId, comandaData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/comande/${cantiereIdNum}`, comandaData);\n      return response.data;\n    } catch (error) {\n      console.error('Create comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna una comanda esistente\n  updateComanda: async (cantiereId, idComanda, comandaData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.put(`/comande/${cantiereIdNum}/${idComanda}`, comandaData);\n      return response.data;\n    } catch (error) {\n      console.error('Update comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina una comanda\n  deleteComanda: async (cantiereId, idComanda) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.delete(`/comande/${cantiereIdNum}/${idComanda}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Assegna una comanda a un cavo\n  assignComandaToCavo: async (cantiereId, idComanda, idCavo) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/comande/${cantiereIdNum}/${idComanda}/assign`, {\n        id_cavo: idCavo\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Assign comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Genera PDF di una comanda\n  printComanda: async (cantiereId, idComanda) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/comande/${cantiereIdNum}/${idComanda}/pdf`);\n      return response.data;\n    } catch (error) {\n      console.error('Print comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default comandeService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "comandeService", "getComande", "cantiereId", "params", "cantiereIdNum", "parseInt", "isNaN", "Error", "response", "get", "data", "error", "console", "createComanda", "comandaData", "post", "updateComanda", "idComanda", "put", "deleteComanda", "delete", "assignComandaToCavo", "idCavo", "id_cavo", "printComanda"], "sources": ["C:/CMS/webapp/frontend/src/services/comandeService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\nconst comandeService = {\r\n  // Ottiene la lista delle comande di un cantiere\r\n  getComande: async (cantiereId, params = {}) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/comande/cantiere/${cantiereIdNum}`, { params });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get comande error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea una nuova comanda\r\n  createComanda: async (cantiereId, comandaData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.post(`/comande/${cantiereIdNum}`, comandaData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Create comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna una comanda esistente\r\n  updateComanda: async (cantiereId, idComanda, comandaData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.put(`/comande/${cantiereIdNum}/${idComanda}`, comandaData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Update comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Elimina una comanda\r\n  deleteComanda: async (cantiereId, idComanda) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.delete(`/comande/${cantiereIdNum}/${idComanda}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Delete comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Assegna una comanda a un cavo\r\n  assignComandaToCavo: async (cantiereId, idComanda, idCavo) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.post(`/comande/${cantiereIdNum}/${idComanda}/assign`, {\r\n        id_cavo: idCavo\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Assign comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Genera PDF di una comanda\r\n  printComanda: async (cantiereId, idComanda) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/comande/${cantiereIdNum}/${idComanda}/pdf`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Print comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default comandeService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;AAEzC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;AAE9B,MAAMC,cAAc,GAAG;EACrB;EACAC,UAAU,EAAE,MAAAA,CAAOC,UAAU,EAAEC,MAAM,GAAG,CAAC,CAAC,KAAK;IAC7C,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,qBAAqBL,aAAa,EAAE,EAAE;QAAED;MAAO,CAAC,CAAC;MAC1F,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAE,aAAa,EAAE,MAAAA,CAAOX,UAAU,EAAEY,WAAW,KAAK;IAChD,IAAI;MACF;MACA,MAAMV,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,YAAYX,aAAa,EAAE,EAAEU,WAAW,CAAC;MACnF,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAK,aAAa,EAAE,MAAAA,CAAOd,UAAU,EAAEe,SAAS,EAAEH,WAAW,KAAK;IAC3D,IAAI;MACF;MACA,MAAMV,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACoB,GAAG,CAAC,YAAYd,aAAa,IAAIa,SAAS,EAAE,EAAEH,WAAW,CAAC;MAC/F,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAQ,aAAa,EAAE,MAAAA,CAAOjB,UAAU,EAAEe,SAAS,KAAK;IAC9C,IAAI;MACF;MACA,MAAMb,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACsB,MAAM,CAAC,YAAYhB,aAAa,IAAIa,SAAS,EAAE,CAAC;MACrF,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAU,mBAAmB,EAAE,MAAAA,CAAOnB,UAAU,EAAEe,SAAS,EAAEK,MAAM,KAAK;IAC5D,IAAI;MACF;MACA,MAAMlB,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACiB,IAAI,CAAC,YAAYX,aAAa,IAAIa,SAAS,SAAS,EAAE;QACzFM,OAAO,EAAED;MACX,CAAC,CAAC;MACF,OAAOd,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAa,YAAY,EAAE,MAAAA,CAAOtB,UAAU,EAAEe,SAAS,KAAK;IAC7C,IAAI;MACF;MACA,MAAMb,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,YAAYL,aAAa,IAAIa,SAAS,MAAM,CAAC;MACtF,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeX,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}