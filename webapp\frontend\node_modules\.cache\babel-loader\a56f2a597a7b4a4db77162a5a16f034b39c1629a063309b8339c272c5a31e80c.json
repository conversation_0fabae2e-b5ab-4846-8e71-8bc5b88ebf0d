{"ast": null, "code": "import { formatDistance } from \"./en-US/_lib/formatDistance.mjs\";\nimport { formatRelative } from \"./en-US/_lib/formatRelative.mjs\";\nimport { localize } from \"./en-US/_lib/localize.mjs\";\nimport { match } from \"./en-US/_lib/match.mjs\";\nimport { formatLong } from \"./en-GB/_lib/formatLong.mjs\";\n\n/**\n * @category Locales\n * @summary English locale (Ireland).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@tan75](https://github.com/tan75)\n */\nexport const enIE = {\n  code: \"en-IE\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default enIE;", "map": {"version": 3, "names": ["formatDistance", "formatRelative", "localize", "match", "formatLong", "enIE", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/en-IE.mjs"], "sourcesContent": ["import { formatDistance } from \"./en-US/_lib/formatDistance.mjs\";\nimport { formatRelative } from \"./en-US/_lib/formatRelative.mjs\";\nimport { localize } from \"./en-US/_lib/localize.mjs\";\nimport { match } from \"./en-US/_lib/match.mjs\";\nimport { formatLong } from \"./en-GB/_lib/formatLong.mjs\";\n\n/**\n * @category Locales\n * @summary English locale (Ireland).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@tan75](https://github.com/tan75)\n */\nexport const enIE = {\n  code: \"en-IE\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default enIE;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,iCAAiC;AAChE,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,SAASC,UAAU,QAAQ,6BAA6B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BI,UAAU,EAAEA,UAAU;EACtBH,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZI,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}