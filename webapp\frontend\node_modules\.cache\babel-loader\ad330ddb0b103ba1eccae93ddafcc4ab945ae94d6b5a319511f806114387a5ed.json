{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\PosaCaviCollegamenti.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Paper, Divider, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Grid, Alert, CircularProgress, FormHelperText, Radio, RadioGroup, FormControlLabel, FormLabel } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Cable as CableIcon, Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PosaCaviCollegamenti = ({\n  cantiereId: propCantiereId,\n  onSuccess,\n  onError,\n  initialOption = null\n}) => {\n  _s();\n  // Log del cantiereId all'avvio\n  console.log('PosaCaviCollegamenti - cantiereId da props:', propCantiereId);\n\n  // Se cantiereId non è definito nelle props, prova a recuperarlo dal localStorage\n  const cantiereId = propCantiereId || parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  console.log('PosaCaviCollegamenti - cantiereId effettivo:', cantiereId);\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n\n  // Inizializza il componente con l'opzione iniziale se specificata\n  React.useEffect(() => {\n    if (initialOption) {\n      // Imposta direttamente le opzioni invece di chiamare handleOptionSelect\n      // per evitare dipendenze circolari\n      setSelectedOption(initialOption);\n      if (initialOption === 'eliminaCavo') {\n        loadCavi('eliminaCavo');\n        setDialogType('eliminaCavo');\n        setOpenDialog(true);\n      } else if (initialOption === 'modificaCavo') {\n        loadCavi('modificaCavo');\n        setDialogType('selezionaCavo');\n        setOpenDialog(true);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [initialOption]);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async operationType => {\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n\n      // Filtra i cavi in base al tipo di operazione\n      if (operationType === 'modificaCavo') {\n        // Per modifica cavo, mostra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\n        const caviNonPosati = caviData.filter(cavo => parseFloat(cavo.metratura_reale) === 0 && cavo.stato_installazione !== 'Installato');\n        setCavi(caviNonPosati);\n      } else {\n        // Per altre operazioni, mostra tutti i cavi\n        setCavi(caviData);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi(option);\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      // Reindirizza alla pagina di aggiunta cavo\n      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/posa/aggiungi-cavo`;\n    } else if (option === 'modificaCavo') {\n      loadCavi('modificaCavo');\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi('eliminaCavo');\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    } else if (option === 'collegamentoCavo') {\n      // Reindirizza alla pagina di gestione collegamenti\n      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/collegamenti`;\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    // Reset dello stato del componente\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setCavoIdInput('');\n    setFormErrors({});\n    setFormWarnings({});\n    setDeleteMode('spare'); // Reset alla modalità predefinita\n\n    // Se è stato specificato un initialOption, notifica il genitore che il dialogo è stato chiuso\n    // ma senza messaggio di errore\n    if (initialOption && onSuccess) {\n      // Chiama onSuccess ma senza messaggio per evitare l'alert\n      onSuccess(null);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n      console.log('Ricerca cavo con ID:', cavoIdInput, 'per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      const cavo = caviData.find(c => c.id_cavo === cavoIdInput.trim());\n      if (!cavo) {\n        onError(`Cavo con ID ${cavoIdInput} non trovato`);\n        return;\n      }\n\n      // Verifica se stiamo cercando un cavo per modificarlo\n      if (dialogType === 'selezionaCavo') {\n        // Verifica che il cavo non sia già posato\n        if (parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato') {\n          onError(`Il cavo ${cavo.id_cavo} risulta già posato. Utilizza l'opzione \"Modifica bobina cavo posato\" per modificarlo.`);\n          return;\n        }\n      }\n\n      // Seleziona il cavo trovato\n      handleCavoSelect(cavo);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce il cambio dell'input dell'ID cavo\n  const handleCavoIdInputChange = e => {\n    setCavoIdInput(e.target.value);\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({\n            metri_posati: 'Inserire un valore numerico valido'\n          });\n          setLoading(false);\n          return;\n        }\n        await caviService.updateMetriPosati(cantiereId, formData.id_cavo, parseFloat(formData.metri_posati));\n        onSuccess('Metri posati aggiornati con successo');\n      } else if (dialogType === 'modificaBobina') {\n        await caviService.updateBobina(cantiereId, formData.id_cavo, formData.id_bobina);\n        onSuccess('Bobina aggiornata con successo');\n      } else if (dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n\n        // Rimuovi i campi di sistema che non devono essere modificati\n        const dataToSend = {\n          ...validatedData\n        };\n        delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n        delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n        delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n        delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n        delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n        // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n        dataToSend.modificato_manualmente = 1;\n        console.log('Dati inviati al server:', dataToSend);\n        try {\n          console.log('Invio dati al server per aggiornamento cavo:', dataToSend);\n          const result = await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n          console.log('Risposta dal server dopo aggiornamento cavo:', result);\n          onSuccess('Cavo modificato con successo');\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento del cavo:', error);\n\n          // Gestione più dettagliata dell'errore\n          let errorMessage = 'Errore durante l\\'aggiornamento del cavo';\n          if (error.response) {\n            // Errore dal server con risposta\n            errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n            if (error.response.data && error.response.data.detail) {\n              errorMessage += ` - ${error.response.data.detail}`;\n            }\n          } else if (error.request) {\n            // Errore di rete senza risposta dal server\n            errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n          } else if (error.message) {\n            // Errore con messaggio\n            errorMessage += `: ${error.message}`;\n          }\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        // Verifica se il cavo è installato\n        const isInstalled = selectedCavo.stato_installazione === 'Installato' || selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0;\n        if (isInstalled) {\n          // Se è installato, marca solo come SPARE\n          console.log('Marcando cavo installato come SPARE:', selectedCavo.id_cavo);\n          try {\n            // Prima prova con markCavoAsSpare\n            const result = await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo);\n            console.log('Risultato marcatura SPARE:', result);\n            console.log('Nuovo valore modificato_manualmente:', result.modificato_manualmente);\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n          } catch (markError) {\n            console.error('Errore con markCavoAsSpare, tentativo con deleteCavo mode=spare:', markError);\n            // Se fallisce, prova con deleteCavo mode=spare\n            const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, 'spare');\n            console.log('Risultato marcatura SPARE con deleteCavo:', result);\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n          }\n        } else {\n          // Se non è installato, usa la modalità selezionata (SPARE o DELETE)\n          console.log('Eliminando cavo non installato con modalità:', deleteMode);\n          const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n          console.log('Risultato eliminazione/marcatura:', result);\n          onSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n        }\n      }\n\n      // Non chiamare handleCloseDialog() qui, perché il dialog verrà chiuso dal genitore\n      // quando viene chiamato onSuccess()\n    } catch (error) {\n      console.error('Errore durante l\\'operazione:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore sconosciuto';\n      if (error.detail) {\n        // Errore dal backend con dettaglio\n        errorMessage = error.detail;\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage = error.message;\n      } else if (typeof error === 'string') {\n        // Errore come stringa\n        errorMessage = error;\n      }\n      onError('Errore durante l\\'operazione: ' + errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'inserisciMetri') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Inserisci Metri Posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Seleziona un cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                button: true,\n                onClick: () => handleCavoSelect(cavo),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: cavo.id_cavo,\n                  secondary: `${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 23\n                }, this)\n              }, cavo.id_cavo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metri teorici: \", selectedCavo.metri_teorici || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metratura attuale: \", selectedCavo.metratura_reale || '0']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"dense\",\n              name: \"metri_posati\",\n              label: \"Metri posati da aggiungere\",\n              type: \"number\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_posati,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_posati,\n              helperText: formErrors.metri_posati,\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || !formData.metri_posati,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 71\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'modificaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Bobina Cavo Posato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Seleziona un cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                button: true,\n                onClick: () => handleCavoSelect(cavo),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: cavo.id_cavo,\n                  secondary: `Bobina attuale: ${cavo.id_bobina || 'Non assegnata'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 23\n                }, this)\n              }, cavo.id_cavo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Bobina attuale: \", selectedCavo.id_bobina || 'Non assegnata']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"dense\",\n              name: \"id_bobina\",\n              label: \"ID Bobina\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_bobina,\n              onChange: handleFormChange,\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 71\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mb: 2\n            },\n            children: \"Puoi modificare solo i cavi non ancora posati (metratura = 0 e stato diverso da \\\"Installato\\\"). Per modificare cavi gi\\xE0 posati, utilizzare l'opzione \\\"Modifica bobina cavo posato\\\".\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 13\n          }, this), caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Inserisci l'ID del cavo da modificare:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"ID Cavo\",\n                variant: \"outlined\",\n                value: cavoIdInput,\n                onChange: handleCavoIdInputChange,\n                placeholder: \"Inserisci l'ID del cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: handleSearchCavoById,\n                disabled: caviLoading || !cavoIdInput.trim(),\n                sx: {\n                  ml: 2,\n                  minWidth: '120px'\n                },\n                children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 36\n                }, this) : \"Cerca\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 15\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'eliminaCavo') {\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = selectedCavo && (selectedCavo.stato_installazione === 'Installato' || selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0);\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: !selectedCavo ? 'Elimina Cavo' : isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Inserisci l'ID del cavo da eliminare:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"ID Cavo\",\n                variant: \"outlined\",\n                value: cavoIdInput,\n                onChange: handleCavoIdInputChange,\n                placeholder: \"Inserisci l'ID del cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: handleSearchCavoById,\n                disabled: caviLoading || !cavoIdInput.trim(),\n                sx: {\n                  ml: 2,\n                  minWidth: '120px'\n                },\n                children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 36\n                }, this) : \"Cerca\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 15\n          }, this) : dialogType === 'eliminaCavo' && isInstalled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n              children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 27\n              }, this), \" risulta installato o parzialmente posato.\", selectedCavo.metratura_reale > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [\" Metri posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [selectedCavo.metratura_reale, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 676,\n                  columnNumber: 38\n                }, this), \".\"]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DialogContentText, {\n              sx: {\n                mt: 2\n              },\n              children: \"Non \\xE8 possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : dialogType === 'eliminaCavo' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n              children: [\"Stai per eliminare il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 46\n              }, this), \".\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              component: \"fieldset\",\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n                component: \"legend\",\n                children: \"Scegli l'operazione da eseguire:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n                value: deleteMode,\n                onChange: e => setDeleteMode(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  value: \"spare\",\n                  control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 32\n                  }, this),\n                  label: \"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  value: \"delete\",\n                  control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 32\n                  }, this),\n                  label: \"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 13\n          }, this), dialogType === 'eliminaCavo' && selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: isInstalled ? \"warning\" : \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 38\n            }, this) : isInstalled ? /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 85\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 103\n            }, this),\n            children: isInstalled ? \"Marca come SPARE\" : deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 713,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 710,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'modificaCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"id_cavo\",\n                label: \"ID Cavo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.id_cavo,\n                disabled: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"revisione_ufficiale\",\n                label: \"Revisione Ufficiale\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.revisione_ufficiale,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sistema\",\n                label: \"Sistema\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sistema,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utility\",\n                label: \"Utility\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utility,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"colore_cavo\",\n                label: \"Colore Cavo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.colore_cavo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"tipologia\",\n                label: \"Tipologia\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.tipologia,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_conduttori\",\n                label: \"Numero Conduttori\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_conduttori,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sezione\",\n                label: \"Sezione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sezione,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 802,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sh\",\n                label: \"SH\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sh || formData.SH,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_partenza\",\n                label: \"Ubicazione Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utenza_partenza\",\n                label: \"Utenza Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utenza_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"descrizione_utenza_partenza\",\n                label: \"Descrizione Utenza Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.descrizione_utenza_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_arrivo\",\n                label: \"Ubicazione Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utenza_arrivo\",\n                label: \"Utenza Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utenza_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"descrizione_utenza_arrivo\",\n                label: \"Descrizione Utenza Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.descrizione_utenza_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_teorici\",\n                label: \"Metri Teorici\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_teorici,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 881,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 729,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 897,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 69\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 898,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 896,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 727,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '280px',\n        mr: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Posa Cavi e Collegamenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 918,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 921,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          dense: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('inserisciMetri'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 925,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 924,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"1. Inserisci metri posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 923,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('modificaBobina'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 932,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 931,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"2. Modifica bobina cavo posato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 934,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('aggiungiCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 939,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 938,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"3. Aggiungi nuovo cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 941,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('modificaCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 946,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"4. Modifica cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 944,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('eliminaCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 953,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"5. Elimina cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 951,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('collegamentoCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 960,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"6. Collegamento cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 958,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 922,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 917,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 916,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flexGrow: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          minHeight: '300px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: [!selectedOption && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          children: \"Seleziona un'opzione dal menu a sinistra per iniziare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 972,\n          columnNumber: 13\n        }, this), selectedOption && !openDialog && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: [selectedOption === 'inserisciMetri' && 'Inserisci metri posati', selectedOption === 'modificaCavo' && 'Modifica cavo', selectedOption === 'aggiungiCavo' && 'Aggiungi nuovo cavo', selectedOption === 'eliminaCavo' && 'Elimina cavo', selectedOption === 'modificaBobina' && 'Modifica bobina cavo posato', selectedOption === 'collegamentoCavo' && 'Collegamento cavo']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 978,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Caricamento in corso...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 986,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n            sx: {\n              mt: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 989,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 977,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 970,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 969,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 914,\n    columnNumber: 5\n  }, this);\n};\n_s(PosaCaviCollegamenti, \"rJgpKvklhCAha+37suBEac3Umps=\");\n_c = PosaCaviCollegamenti;\nexport default PosaCaviCollegamenti;\nvar _c;\n$RefreshReg$(_c, \"PosaCaviCollegamenti\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Divider", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Grid", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "Radio", "RadioGroup", "FormControlLabel", "FormLabel", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Cable", "CableIcon", "Save", "SaveIcon", "Warning", "WarningIcon", "caviService", "validateCavoData", "validateField", "isEmpty", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PosaCaviCollegamenti", "cantiereId", "propCantiereId", "onSuccess", "onError", "initialOption", "_s", "console", "log", "parseInt", "localStorage", "getItem", "loading", "setLoading", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "cavoIdInput", "setCavoIdInput", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "cavi", "<PERSON><PERSON><PERSON>", "caviLoading", "setCaviLoading", "deleteMode", "setDeleteMode", "useEffect", "loadCavi", "operationType", "Error", "caviData", "get<PERSON><PERSON>", "cavi<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "cavo", "parseFloat", "metratura_reale", "stato_installazione", "error", "errorMessage", "response", "status", "statusText", "data", "detail", "request", "message", "handleOptionSelect", "option", "window", "location", "href", "handleCloseDialog", "handleCavoSelect", "metri_te<PERSON>ci", "handleSearchCavoById", "trim", "find", "c", "handleCavoIdInputChange", "e", "target", "value", "handleFormChange", "name", "additionalParams", "metriTeorici", "result", "prev", "valid", "warning", "handleSave", "isNaN", "updateMetri<PERSON><PERSON><PERSON>", "updateBobina", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "validatedData", "dataToSend", "modificato_manualmente", "timestamp", "updateCavo", "Object", "keys", "length", "warningMessages", "values", "join", "warn", "isInstalled", "markCavoAsSpare", "<PERSON><PERSON><PERSON><PERSON>", "deleteCavo", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "variant", "gutterBottom", "map", "button", "onClick", "primary", "secondary", "tipologia", "ubicazione_partenza", "ubicazione_arrivo", "sx", "mt", "margin", "label", "type", "onChange", "required", "helperText", "disabled", "startIcon", "size", "mb", "p", "display", "alignItems", "placeholder", "color", "ml", "min<PERSON><PERSON><PERSON>", "component", "control", "container", "spacing", "item", "xs", "sm", "revisione_ufficiale", "sistema", "utility", "colore_cavo", "n_conduttori", "sezione", "sh", "SH", "utenza_partenza", "descrizione_utenza_partenza", "utenza_arrivo", "descrizione_utenza_arrivo", "width", "mr", "dense", "flexGrow", "minHeight", "justifyContent", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/PosaCaviCollegamenti.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogContentText,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Grid,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  FormLabel\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Cable as CableIcon,\n  Save as SaveIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\n\nconst PosaCaviCollegamenti = ({ cantiereId: propCantiereId, onSuccess, onError, initialOption = null }) => {\n  // Log del cantiereId all'avvio\n  console.log('PosaCaviCollegamenti - cantiereId da props:', propCantiereId);\n\n  // Se cantiereId non è definito nelle props, prova a recuperarlo dal localStorage\n  const cantiereId = propCantiereId || parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  console.log('PosaCaviCollegamenti - cantiereId effettivo:', cantiereId);\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n\n  // Inizializza il componente con l'opzione iniziale se specificata\n  React.useEffect(() => {\n    if (initialOption) {\n      // Imposta direttamente le opzioni invece di chiamare handleOptionSelect\n      // per evitare dipendenze circolari\n      setSelectedOption(initialOption);\n\n      if (initialOption === 'eliminaCavo') {\n        loadCavi('eliminaCavo');\n        setDialogType('eliminaCavo');\n        setOpenDialog(true);\n      } else if (initialOption === 'modificaCavo') {\n        loadCavi('modificaCavo');\n        setDialogType('selezionaCavo');\n        setOpenDialog(true);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [initialOption]);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async (operationType) => {\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n\n      // Filtra i cavi in base al tipo di operazione\n      if (operationType === 'modificaCavo') {\n        // Per modifica cavo, mostra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\n        const caviNonPosati = caviData.filter(cavo =>\n          parseFloat(cavo.metratura_reale) === 0 &&\n          cavo.stato_installazione !== 'Installato'\n        );\n        setCavi(caviNonPosati);\n      } else {\n        // Per altre operazioni, mostra tutti i cavi\n        setCavi(caviData);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi(option);\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      // Reindirizza alla pagina di aggiunta cavo\n      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/posa/aggiungi-cavo`;\n    } else if (option === 'modificaCavo') {\n      loadCavi('modificaCavo');\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi('eliminaCavo');\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    } else if (option === 'collegamentoCavo') {\n      // Reindirizza alla pagina di gestione collegamenti\n      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/collegamenti`;\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    // Reset dello stato del componente\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setCavoIdInput('');\n    setFormErrors({});\n    setFormWarnings({});\n    setDeleteMode('spare'); // Reset alla modalità predefinita\n\n    // Se è stato specificato un initialOption, notifica il genitore che il dialogo è stato chiuso\n    // ma senza messaggio di errore\n    if (initialOption && onSuccess) {\n      // Chiama onSuccess ma senza messaggio per evitare l'alert\n      onSuccess(null);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n\n      console.log('Ricerca cavo con ID:', cavoIdInput, 'per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      const cavo = caviData.find(c => c.id_cavo === cavoIdInput.trim());\n\n      if (!cavo) {\n        onError(`Cavo con ID ${cavoIdInput} non trovato`);\n        return;\n      }\n\n      // Verifica se stiamo cercando un cavo per modificarlo\n      if (dialogType === 'selezionaCavo') {\n        // Verifica che il cavo non sia già posato\n        if (parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato') {\n          onError(`Il cavo ${cavo.id_cavo} risulta già posato. Utilizza l'opzione \"Modifica bobina cavo posato\" per modificarlo.`);\n          return;\n        }\n      }\n\n      // Seleziona il cavo trovato\n      handleCavoSelect(cavo);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce il cambio dell'input dell'ID cavo\n  const handleCavoIdInputChange = (e) => {\n    setCavoIdInput(e.target.value);\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({ metri_posati: 'Inserire un valore numerico valido' });\n          setLoading(false);\n          return;\n        }\n\n        await caviService.updateMetriPosati(\n          cantiereId,\n          formData.id_cavo,\n          parseFloat(formData.metri_posati)\n        );\n        onSuccess('Metri posati aggiornati con successo');\n      } else if (dialogType === 'modificaBobina') {\n        await caviService.updateBobina(\n          cantiereId,\n          formData.id_cavo,\n          formData.id_bobina\n        );\n        onSuccess('Bobina aggiornata con successo');\n      } else if (dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n\n        // Rimuovi i campi di sistema che non devono essere modificati\n        const dataToSend = { ...validatedData };\n        delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n        delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n        delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n        delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n        delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n        // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n        dataToSend.modificato_manualmente = 1;\n\n        console.log('Dati inviati al server:', dataToSend);\n\n        try {\n          console.log('Invio dati al server per aggiornamento cavo:', dataToSend);\n          const result = await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n          console.log('Risposta dal server dopo aggiornamento cavo:', result);\n          onSuccess('Cavo modificato con successo');\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento del cavo:', error);\n\n          // Gestione più dettagliata dell'errore\n          let errorMessage = 'Errore durante l\\'aggiornamento del cavo';\n\n          if (error.response) {\n            // Errore dal server con risposta\n            errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n            if (error.response.data && error.response.data.detail) {\n              errorMessage += ` - ${error.response.data.detail}`;\n            }\n          } else if (error.request) {\n            // Errore di rete senza risposta dal server\n            errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n          } else if (error.message) {\n            // Errore con messaggio\n            errorMessage += `: ${error.message}`;\n          }\n\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        // Verifica se il cavo è installato\n        const isInstalled = selectedCavo.stato_installazione === 'Installato' || (selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0);\n\n        if (isInstalled) {\n          // Se è installato, marca solo come SPARE\n          console.log('Marcando cavo installato come SPARE:', selectedCavo.id_cavo);\n          try {\n            // Prima prova con markCavoAsSpare\n            const result = await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo);\n            console.log('Risultato marcatura SPARE:', result);\n            console.log('Nuovo valore modificato_manualmente:', result.modificato_manualmente);\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n          } catch (markError) {\n            console.error('Errore con markCavoAsSpare, tentativo con deleteCavo mode=spare:', markError);\n            // Se fallisce, prova con deleteCavo mode=spare\n            const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, 'spare');\n            console.log('Risultato marcatura SPARE con deleteCavo:', result);\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n          }\n        } else {\n          // Se non è installato, usa la modalità selezionata (SPARE o DELETE)\n          console.log('Eliminando cavo non installato con modalità:', deleteMode);\n          const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n          console.log('Risultato eliminazione/marcatura:', result);\n          onSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n        }\n      }\n\n      // Non chiamare handleCloseDialog() qui, perché il dialog verrà chiuso dal genitore\n      // quando viene chiamato onSuccess()\n    } catch (error) {\n      console.error('Errore durante l\\'operazione:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore sconosciuto';\n\n      if (error.detail) {\n        // Errore dal backend con dettaglio\n        errorMessage = error.detail;\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage = error.message;\n      } else if (typeof error === 'string') {\n        // Errore come stringa\n        errorMessage = error;\n      }\n\n      onError('Errore durante l\\'operazione: ' + errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'inserisciMetri') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Inserisci Metri Posati</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metri teorici: {selectedCavo.metri_teorici || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metratura attuale: {selectedCavo.metratura_reale || '0'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"metri_posati\"\n                  label=\"Metri posati da aggiungere\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_posati}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_posati}\n                  helperText={formErrors.metri_posati}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading || !formData.metri_posati}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Bobina Cavo Posato</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`Bobina attuale: ${cavo.id_bobina || 'Non assegnata'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Bobina attuale: {selectedCavo.id_bobina || 'Non assegnata'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"id_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_bobina}\n                  onChange={handleFormChange}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Cavo</DialogTitle>\n          <DialogContent>\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Puoi modificare solo i cavi non ancora posati (metratura = 0 e stato diverso da \"Installato\").\n              Per modificare cavi già posati, utilizzare l'opzione \"Modifica bobina cavo posato\".\n            </Alert>\n\n            {caviLoading ? (\n              <CircularProgress />\n            ) : !selectedCavo ? (\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Inserisci l'ID del cavo da modificare:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>\n                  <TextField\n                    fullWidth\n                    label=\"ID Cavo\"\n                    variant=\"outlined\"\n                    value={cavoIdInput}\n                    onChange={handleCavoIdInputChange}\n                    placeholder=\"Inserisci l'ID del cavo\"\n                  />\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    onClick={handleSearchCavoById}\n                    disabled={caviLoading || !cavoIdInput.trim()}\n                    sx={{ ml: 2, minWidth: '120px' }}\n                  >\n                    {caviLoading ? <CircularProgress size={24} /> : \"Cerca\"}\n                  </Button>\n                </Box>\n              </Box>\n            ) : null}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaCavo') {\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = selectedCavo && (selectedCavo.stato_installazione === 'Installato' || (selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0));\n\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {!selectedCavo ? 'Elimina Cavo' :\n             isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'}\n          </DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : !selectedCavo ? (\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Inserisci l'ID del cavo da eliminare:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>\n                  <TextField\n                    fullWidth\n                    label=\"ID Cavo\"\n                    variant=\"outlined\"\n                    value={cavoIdInput}\n                    onChange={handleCavoIdInputChange}\n                    placeholder=\"Inserisci l'ID del cavo\"\n                  />\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    onClick={handleSearchCavoById}\n                    disabled={caviLoading || !cavoIdInput.trim()}\n                    sx={{ ml: 2, minWidth: '120px' }}\n                  >\n                    {caviLoading ? <CircularProgress size={24} /> : \"Cerca\"}\n                  </Button>\n                </Box>\n              </Box>\n            ) : dialogType === 'eliminaCavo' && isInstalled ? (\n              <>\n                <DialogContentText>\n                  Il cavo <strong>{selectedCavo.id_cavo}</strong> risulta installato o parzialmente posato.\n                  {selectedCavo.metratura_reale > 0 && (\n                    <> Metri posati: <strong>{selectedCavo.metratura_reale} m</strong>.</>\n                  )}\n                </DialogContentText>\n                <DialogContentText sx={{ mt: 2 }}>\n                  Non è possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\n                </DialogContentText>\n              </>\n            ) : dialogType === 'eliminaCavo' ? (\n              <>\n                <DialogContentText>\n                  Stai per eliminare il cavo <strong>{selectedCavo.id_cavo}</strong>.\n                </DialogContentText>\n\n                <FormControl component=\"fieldset\" sx={{ mt: 2 }}>\n                  <FormLabel component=\"legend\">Scegli l'operazione da eseguire:</FormLabel>\n                  <RadioGroup\n                    value={deleteMode}\n                    onChange={(e) => setDeleteMode(e.target.value)}\n                  >\n                    <FormControlLabel\n                      value=\"spare\"\n                      control={<Radio />}\n                      label=\"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                    />\n                    <FormControlLabel\n                      value=\"delete\"\n                      control={<Radio />}\n                      label=\"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                    />\n                  </RadioGroup>\n                </FormControl>\n              </>\n            ) : null}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {dialogType === 'eliminaCavo' && selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color={isInstalled ? \"warning\" : \"error\"}\n                startIcon={loading ? <CircularProgress size={20} /> : isInstalled ? <WarningIcon /> : <DeleteIcon />}\n              >\n                {isInstalled ? \"Marca come SPARE\" : (deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\")}\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Cavo</DialogTitle>\n          <DialogContent>\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  disabled\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"revisione_ufficiale\"\n                  label=\"Revisione Ufficiale\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.revisione_ufficiale}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sistema\"\n                  label=\"Sistema\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sistema}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"colore_cavo\"\n                  label=\"Colore Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.colore_cavo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"Numero Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sh\"\n                  label=\"SH\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sh || formData.SH}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_partenza\"\n                  label=\"Ubicazione Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utenza_partenza\"\n                  label=\"Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"descrizione_utenza_partenza\"\n                  label=\"Descrizione Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_arrivo\"\n                  label=\"Ubicazione Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utenza_arrivo\"\n                  label=\"Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"descrizione_utenza_arrivo\"\n                  label=\"Descrizione Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_teorici\"\n                  label=\"Metri Teorici\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_teorici}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              {/* Campo ID Bobina rimosso perché è un campo di sistema */}\n              {/* Campo Stato Installazione rimosso perché è un campo di sistema */}\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      {/* Menu a cascata nella sidebar */}\n      <Box sx={{ width: '280px', mr: 3 }}>\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Posa Cavi e Collegamenti\n          </Typography>\n          <Divider sx={{ mb: 2 }} />\n          <List component=\"nav\" dense>\n            <ListItemButton onClick={() => handleOptionSelect('inserisciMetri')}>\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"1. Inserisci metri posati\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaBobina')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"2. Modifica bobina cavo posato\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('aggiungiCavo')}>\n              <ListItemIcon>\n                <AddIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"3. Aggiungi nuovo cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaCavo')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"4. Modifica cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('eliminaCavo')}>\n              <ListItemIcon>\n                <DeleteIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"5. Elimina cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('collegamentoCavo')}>\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"6. Collegamento cavo\" />\n            </ListItemButton>\n          </List>\n        </Paper>\n      </Box>\n\n      {/* Area principale per il contenuto */}\n      <Box sx={{ flexGrow: 1 }}>\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption && (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu a sinistra per iniziare.\n            </Typography>\n          )}\n          {selectedOption && !openDialog && (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'inserisciMetri' && 'Inserisci metri posati'}\n                {selectedOption === 'modificaCavo' && 'Modifica cavo'}\n                {selectedOption === 'aggiungiCavo' && 'Aggiungi nuovo cavo'}\n                {selectedOption === 'eliminaCavo' && 'Elimina cavo'}\n                {selectedOption === 'modificaBobina' && 'Modifica bobina cavo posato'}\n                {selectedOption === 'collegamentoCavo' && 'Collegamento cavo'}\n              </Typography>\n              <Typography variant=\"body1\">\n                Caricamento in corso...\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      </Box>\n\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default PosaCaviCollegamenti;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,SAAS,QACJ,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvF,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,UAAU,EAAEC,cAAc;EAAEC,SAAS;EAAEC,OAAO;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EACzG;EACAC,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEN,cAAc,CAAC;;EAE1E;EACA,MAAMD,UAAU,GAAGC,cAAc,IAAIO,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;EAC7FJ,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEP,UAAU,CAAC;EACvE,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+D,cAAc,EAAEC,iBAAiB,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmE,UAAU,EAAEC,aAAa,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqE,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuE,QAAQ,EAAEC,WAAW,CAAC,GAAGxE,QAAQ,CAAC;IACvCyE,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8E,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACgF,YAAY,EAAEC,eAAe,CAAC,GAAGjF,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACkF,IAAI,EAAEC,OAAO,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsF,UAAU,EAAEC,aAAa,CAAC,GAAGvF,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEvD;EACAD,KAAK,CAACyF,SAAS,CAAC,MAAM;IACpB,IAAIlC,aAAa,EAAE;MACjB;MACA;MACAU,iBAAiB,CAACV,aAAa,CAAC;MAEhC,IAAIA,aAAa,KAAK,aAAa,EAAE;QACnCmC,QAAQ,CAAC,aAAa,CAAC;QACvBrB,aAAa,CAAC,aAAa,CAAC;QAC5BF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM,IAAIZ,aAAa,KAAK,cAAc,EAAE;QAC3CmC,QAAQ,CAAC,cAAc,CAAC;QACxBrB,aAAa,CAAC,eAAe,CAAC;QAC9BF,aAAa,CAAC,IAAI,CAAC;MACrB;IACF;IACA;EACF,CAAC,EAAE,CAACZ,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMmC,QAAQ,GAAG,MAAOC,aAAa,IAAK;IACxC,IAAI;MACFL,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,IAAI,CAACnC,UAAU,EAAE;QACf,MAAM,IAAIyC,KAAK,CAAC,mCAAmC,CAAC;MACtD;MAEAnC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEP,UAAU,CAAC;MACzD,MAAM0C,QAAQ,GAAG,MAAMnD,WAAW,CAACoD,OAAO,CAAC3C,UAAU,EAAE,CAAC,CAAC;;MAEzD;MACA,IAAIwC,aAAa,KAAK,cAAc,EAAE;QACpC;QACA,MAAMI,aAAa,GAAGF,QAAQ,CAACG,MAAM,CAACC,IAAI,IACxCC,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,KAAK,CAAC,IACtCF,IAAI,CAACG,mBAAmB,KAAK,YAC/B,CAAC;QACDhB,OAAO,CAACW,aAAa,CAAC;MACxB,CAAC,MAAM;QACL;QACAX,OAAO,CAACS,QAAQ,CAAC;MACnB;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIC,YAAY,GAAG,iCAAiC;MAEpD,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClB;QACAD,YAAY,IAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE;QACzE,IAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,IAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;UACrDL,YAAY,IAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;QACpD;MACF,CAAC,MAAM,IAAIN,KAAK,CAACO,OAAO,EAAE;QACxB;QACAN,YAAY,IAAI,iEAAiE;MACnF,CAAC,MAAM,IAAID,KAAK,CAACQ,OAAO,EAAE;QACxB;QACAP,YAAY,IAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE;MACtC;MAEAvD,OAAO,CAACgD,YAAY,CAAC;IACvB,CAAC,SAAS;MACRhB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMwB,kBAAkB,GAAIC,MAAM,IAAK;IACrC9C,iBAAiB,CAAC8C,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,gBAAgB,IAAIA,MAAM,KAAK,gBAAgB,EAAE;MAC9DrB,QAAQ,CAACqB,MAAM,CAAC;MAChB1C,aAAa,CAAC0C,MAAM,CAAC;MACrB5C,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI4C,MAAM,KAAK,cAAc,EAAE;MACpC;MACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuB/D,UAAU,0BAA0B;IACpF,CAAC,MAAM,IAAI4D,MAAM,KAAK,cAAc,EAAE;MACpCrB,QAAQ,CAAC,cAAc,CAAC;MACxBrB,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI4C,MAAM,KAAK,aAAa,EAAE;MACnCrB,QAAQ,CAAC,aAAa,CAAC;MACvBrB,aAAa,CAAC,aAAa,CAAC;MAC5BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI4C,MAAM,KAAK,kBAAkB,EAAE;MACxC;MACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuB/D,UAAU,oBAAoB;IAC9E;EACF,CAAC;;EAED;EACA,MAAMgE,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACAhD,aAAa,CAAC,KAAK,CAAC;IACpBI,eAAe,CAAC,IAAI,CAAC;IACrBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,cAAc,CAAC,EAAE,CAAC;IAClBE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;IACnBM,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;;IAExB;IACA;IACA,IAAIjC,aAAa,IAAIF,SAAS,EAAE;MAC9B;MACAA,SAAS,CAAC,IAAI,CAAC;IACjB;EACF,CAAC;;EAED;EACA,MAAM+D,gBAAgB,GAAInB,IAAI,IAAK;IACjC1B,eAAe,CAAC0B,IAAI,CAAC;IACrB,IAAI7B,UAAU,KAAK,gBAAgB,EAAE;MACnCK,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEuB,IAAI,CAACvB,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIP,UAAU,KAAK,gBAAgB,EAAE;MAC1CK,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEuB,IAAI,CAACvB,OAAO;QACrBE,SAAS,EAAEqB,IAAI,CAACrB,SAAS,IAAI;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIR,UAAU,KAAK,eAAe,EAAE;MACzCC,aAAa,CAAC,cAAc,CAAC;MAC7BI,WAAW,CAAC;QACV,GAAGwB,IAAI;QACPoB,aAAa,EAAEpB,IAAI,CAACoB,aAAa,IAAI,EAAE;QACvClB,eAAe,EAAEF,IAAI,CAACE,eAAe,IAAI;MAC3C,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMmB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACzC,WAAW,CAAC0C,IAAI,CAAC,CAAC,EAAE;MACvBjE,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFgC,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,IAAI,CAACnC,UAAU,EAAE;QACf,MAAM,IAAIyC,KAAK,CAAC,mCAAmC,CAAC;MACtD;MAEAnC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEmB,WAAW,EAAE,eAAe,EAAE1B,UAAU,CAAC;MAC7E,MAAM0C,QAAQ,GAAG,MAAMnD,WAAW,CAACoD,OAAO,CAAC3C,UAAU,EAAE,CAAC,CAAC;MACzD,MAAM8C,IAAI,GAAGJ,QAAQ,CAAC2B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/C,OAAO,KAAKG,WAAW,CAAC0C,IAAI,CAAC,CAAC,CAAC;MAEjE,IAAI,CAACtB,IAAI,EAAE;QACT3C,OAAO,CAAC,eAAeuB,WAAW,cAAc,CAAC;QACjD;MACF;;MAEA;MACA,IAAIT,UAAU,KAAK,eAAe,EAAE;QAClC;QACA,IAAI8B,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,GAAG,CAAC,IAAIF,IAAI,CAACG,mBAAmB,KAAK,YAAY,EAAE;UACrF9C,OAAO,CAAC,WAAW2C,IAAI,CAACvB,OAAO,wFAAwF,CAAC;UACxH;QACF;MACF;;MAEA;MACA0C,gBAAgB,CAACnB,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIC,YAAY,GAAG,iCAAiC;MAEpD,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClB;QACAD,YAAY,IAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE;QACzE,IAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,IAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;UACrDL,YAAY,IAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;QACpD;MACF,CAAC,MAAM,IAAIN,KAAK,CAACO,OAAO,EAAE;QACxB;QACAN,YAAY,IAAI,iEAAiE;MACnF,CAAC,MAAM,IAAID,KAAK,CAACQ,OAAO,EAAE;QACxB;QACAP,YAAY,IAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE;MACtC;MAEAvD,OAAO,CAACgD,YAAY,CAAC;IACvB,CAAC,SAAS;MACRhB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMoC,uBAAuB,GAAIC,CAAC,IAAK;IACrC7C,cAAc,CAAC6C,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAChC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIH,CAAC,IAAK;IAC9B,MAAM;MAAEI,IAAI;MAAEF;IAAM,CAAC,GAAGF,CAAC,CAACC,MAAM;;IAEhC;IACAnD,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACuD,IAAI,GAAGF;IACV,CAAC,CAAC;;IAEF;IACA,IAAIzD,UAAU,KAAK,cAAc,EAAE;MACjC,MAAM4D,gBAAgB,GAAG,CAAC,CAAC;MAC3B,IAAID,IAAI,KAAK,iBAAiB,EAAE;QAC9BC,gBAAgB,CAACC,YAAY,GAAG/B,UAAU,CAAC1B,QAAQ,CAAC6C,aAAa,IAAI,CAAC,CAAC;MACzE;MAEA,MAAMa,MAAM,GAAGtF,aAAa,CAACmF,IAAI,EAAEF,KAAK,EAAEG,gBAAgB,CAAC;;MAE3D;MACAhD,aAAa,CAACmD,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACJ,IAAI,GAAG,CAACG,MAAM,CAACE,KAAK,GAAGF,MAAM,CAACrB,OAAO,GAAG;MAC3C,CAAC,CAAC,CAAC;;MAEH;MACA3B,eAAe,CAACiD,IAAI,KAAK;QACvB,GAAGA,IAAI;QACP,CAACJ,IAAI,GAAGG,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACrB,OAAO,GAAG;MAC5C,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMyB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFvE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIK,UAAU,KAAK,gBAAgB,EAAE;QACnC;QACA,IAAIvB,OAAO,CAAC2B,QAAQ,CAACG,YAAY,CAAC,IAAI4D,KAAK,CAACrC,UAAU,CAAC1B,QAAQ,CAACG,YAAY,CAAC,CAAC,EAAE;UAC9EK,aAAa,CAAC;YAAEL,YAAY,EAAE;UAAqC,CAAC,CAAC;UACrEZ,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEA,MAAMrB,WAAW,CAAC8F,iBAAiB,CACjCrF,UAAU,EACVqB,QAAQ,CAACE,OAAO,EAChBwB,UAAU,CAAC1B,QAAQ,CAACG,YAAY,CAClC,CAAC;QACDtB,SAAS,CAAC,sCAAsC,CAAC;MACnD,CAAC,MAAM,IAAIe,UAAU,KAAK,gBAAgB,EAAE;QAC1C,MAAM1B,WAAW,CAAC+F,YAAY,CAC5BtF,UAAU,EACVqB,QAAQ,CAACE,OAAO,EAChBF,QAAQ,CAACI,SACX,CAAC;QACDvB,SAAS,CAAC,gCAAgC,CAAC;MAC7C,CAAC,MAAM,IAAIe,UAAU,KAAK,cAAc,EAAE;QACxC;QACA,MAAMsE,UAAU,GAAG/F,gBAAgB,CAAC6B,QAAQ,CAAC;QAE7C,IAAI,CAACkE,UAAU,CAACC,OAAO,EAAE;UACvB3D,aAAa,CAAC0D,UAAU,CAACE,MAAM,CAAC;UAChC1D,eAAe,CAACwD,UAAU,CAACG,QAAQ,CAAC;UACpC9E,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAM+E,aAAa,GAAGJ,UAAU,CAACI,aAAa;;QAE9C;QACA,MAAMC,UAAU,GAAG;UAAE,GAAGD;QAAc,CAAC;QACvC,OAAOC,UAAU,CAACnE,SAAS,CAAC,CAAC;QAC7B,OAAOmE,UAAU,CAAC5C,eAAe,CAAC,CAAC;QACnC,OAAO4C,UAAU,CAACC,sBAAsB,CAAC,CAAC;QAC1C,OAAOD,UAAU,CAACE,SAAS,CAAC,CAAC;QAC7B,OAAOF,UAAU,CAAC3C,mBAAmB,CAAC,CAAC;;QAEvC;QACA2C,UAAU,CAACC,sBAAsB,GAAG,CAAC;QAErCvF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEqF,UAAU,CAAC;QAElD,IAAI;UACFtF,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEqF,UAAU,CAAC;UACvE,MAAMb,MAAM,GAAG,MAAMxF,WAAW,CAACwG,UAAU,CAAC/F,UAAU,EAAE4F,UAAU,CAACrE,OAAO,EAAEqE,UAAU,CAAC;UACvFtF,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEwE,MAAM,CAAC;UACnE7E,SAAS,CAAC,8BAA8B,CAAC;QAC3C,CAAC,CAAC,OAAOgD,KAAK,EAAE;UACd5C,OAAO,CAAC4C,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;;UAEjE;UACA,IAAIC,YAAY,GAAG,0CAA0C;UAE7D,IAAID,KAAK,CAACE,QAAQ,EAAE;YAClB;YACAD,YAAY,IAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE;YACzE,IAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,IAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;cACrDL,YAAY,IAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;YACpD;UACF,CAAC,MAAM,IAAIN,KAAK,CAACO,OAAO,EAAE;YACxB;YACAN,YAAY,IAAI,iEAAiE;UACnF,CAAC,MAAM,IAAID,KAAK,CAACQ,OAAO,EAAE;YACxB;YACAP,YAAY,IAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE;UACtC;UAEAvD,OAAO,CAACgD,YAAY,CAAC;UACrBvC,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAIoF,MAAM,CAACC,IAAI,CAACV,UAAU,CAACG,QAAQ,CAAC,CAACQ,MAAM,GAAG,CAAC,EAAE;UAC/C,MAAMC,eAAe,GAAGH,MAAM,CAACI,MAAM,CAACb,UAAU,CAACG,QAAQ,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC;UACrE/F,OAAO,CAACgG,IAAI,CAAC,gCAAgC,EAAEH,eAAe,CAAC;QACjE;MACF,CAAC,MAAM,IAAIlF,UAAU,KAAK,aAAa,EAAE;QACvC;QACA,MAAMsF,WAAW,GAAGpF,YAAY,CAAC8B,mBAAmB,KAAK,YAAY,IAAK9B,YAAY,CAAC6B,eAAe,IAAI7B,YAAY,CAAC6B,eAAe,GAAG,CAAE;QAE3I,IAAIuD,WAAW,EAAE;UACf;UACAjG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEY,YAAY,CAACI,OAAO,CAAC;UACzE,IAAI;YACF;YACA,MAAMwD,MAAM,GAAG,MAAMxF,WAAW,CAACiH,eAAe,CAACxG,UAAU,EAAEmB,YAAY,CAACI,OAAO,CAAC;YAClFjB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEwE,MAAM,CAAC;YACjDzE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEwE,MAAM,CAACc,sBAAsB,CAAC;YAClF3F,SAAS,CAAC,QAAQiB,YAAY,CAACI,OAAO,kCAAkC,CAAC;UAC3E,CAAC,CAAC,OAAOkF,SAAS,EAAE;YAClBnG,OAAO,CAAC4C,KAAK,CAAC,kEAAkE,EAAEuD,SAAS,CAAC;YAC5F;YACA,MAAM1B,MAAM,GAAG,MAAMxF,WAAW,CAACmH,UAAU,CAAC1G,UAAU,EAAEmB,YAAY,CAACI,OAAO,EAAE,OAAO,CAAC;YACtFjB,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEwE,MAAM,CAAC;YAChE7E,SAAS,CAAC,QAAQiB,YAAY,CAACI,OAAO,kCAAkC,CAAC;UAC3E;QACF,CAAC,MAAM;UACL;UACAjB,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE6B,UAAU,CAAC;UACvE,MAAM2C,MAAM,GAAG,MAAMxF,WAAW,CAACmH,UAAU,CAAC1G,UAAU,EAAEmB,YAAY,CAACI,OAAO,EAAEa,UAAU,CAAC;UACzF9B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEwE,MAAM,CAAC;UACxD7E,SAAS,CAAC,QAAQiB,YAAY,CAACI,OAAO,IAAIa,UAAU,KAAK,OAAO,GAAG,oBAAoB,GAAG,WAAW,eAAe,CAAC;QACvH;MACF;;MAEA;MACA;IACF,CAAC,CAAC,OAAOc,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;MAErD;MACA,IAAIC,YAAY,GAAG,oBAAoB;MAEvC,IAAID,KAAK,CAACM,MAAM,EAAE;QAChB;QACAL,YAAY,GAAGD,KAAK,CAACM,MAAM;MAC7B,CAAC,MAAM,IAAIN,KAAK,CAACQ,OAAO,EAAE;QACxB;QACAP,YAAY,GAAGD,KAAK,CAACQ,OAAO;MAC9B,CAAC,MAAM,IAAI,OAAOR,KAAK,KAAK,QAAQ,EAAE;QACpC;QACAC,YAAY,GAAGD,KAAK;MACtB;MAEA/C,OAAO,CAAC,gCAAgC,GAAGgD,YAAY,CAAC;IAC1D,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+F,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI1F,UAAU,KAAK,gBAAgB,EAAE;MACnC,oBACErB,OAAA,CAACnC,MAAM;QAACmJ,IAAI,EAAE7F,UAAW;QAAC8F,OAAO,EAAE7C,iBAAkB;QAAC8C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EpH,OAAA,CAAClC,WAAW;UAAAsJ,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACjDxH,OAAA,CAACjC,aAAa;UAAAqJ,QAAA,EACX9E,WAAW,gBACVtC,OAAA,CAACvB,gBAAgB;YAAA4I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBpF,IAAI,CAACkE,MAAM,KAAK,CAAC,gBACnBtG,OAAA,CAACxB,KAAK;YAACiJ,QAAQ,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAACjG,YAAY,gBACfvB,OAAA,CAAC7C,GAAG;YAAAiK,QAAA,gBACFpH,OAAA,CAAC5C,UAAU;cAACsK,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxH,OAAA,CAACxC,IAAI;cAAA4J,QAAA,EACFhF,IAAI,CAACwF,GAAG,CAAE1E,IAAI,iBACblD,OAAA,CAACvC,QAAQ;gBACPoK,MAAM;gBAENC,OAAO,EAAEA,CAAA,KAAMzD,gBAAgB,CAACnB,IAAI,CAAE;gBAAAkE,QAAA,eAEtCpH,OAAA,CAACtC,YAAY;kBACXqK,OAAO,EAAE7E,IAAI,CAACvB,OAAQ;kBACtBqG,SAAS,EAAE,GAAG9E,IAAI,CAAC+E,SAAS,IAAI,KAAK,UAAU/E,IAAI,CAACgF,mBAAmB,IAAI,KAAK,OAAOhF,IAAI,CAACiF,iBAAiB,IAAI,KAAK;gBAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC,GANGtE,IAAI,CAACvB,OAAO;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAENxH,OAAA,CAAC7C,GAAG;YAACiL,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBACjBpH,OAAA,CAAC5C,UAAU;cAACsK,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,GAAC,oBACzB,EAAC7F,YAAY,CAACI,OAAO;YAAA;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACbxH,OAAA,CAAC5C,UAAU;cAACsK,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAP,QAAA,GAAC,iBACxB,EAAC7F,YAAY,CAAC+C,aAAa,IAAI,KAAK;YAAA;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACbxH,OAAA,CAAC5C,UAAU;cAACsK,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAP,QAAA,GAAC,qBACpB,EAAC7F,YAAY,CAAC6B,eAAe,IAAI,GAAG;YAAA;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACbxH,OAAA,CAAC9B,SAAS;cACRoK,MAAM,EAAC,OAAO;cACdtD,IAAI,EAAC,cAAc;cACnBuD,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,QAAQ;cACbrB,SAAS;cACTO,OAAO,EAAC,UAAU;cAClB5C,KAAK,EAAErD,QAAQ,CAACG,YAAa;cAC7B6G,QAAQ,EAAE1D,gBAAiB;cAC3B2D,QAAQ;cACRpF,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACJ,YAAa;cACjC+G,UAAU,EAAE3G,UAAU,CAACJ,YAAa;cACpCwG,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBxH,OAAA,CAAC/B,aAAa;UAAAmJ,QAAA,gBACZpH,OAAA,CAAC3C,MAAM;YAACyK,OAAO,EAAE1D,iBAAkB;YAAAgD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDjG,YAAY,iBACXvB,OAAA,CAAC3C,MAAM;YACLyK,OAAO,EAAEvC,UAAW;YACpBqD,QAAQ,EAAE7H,OAAO,IAAI,CAACU,QAAQ,CAACG,YAAa;YAC5CiH,SAAS,EAAE9H,OAAO,gBAAGf,OAAA,CAACvB,gBAAgB;cAACqK,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGxH,OAAA,CAACR,QAAQ;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInG,UAAU,KAAK,gBAAgB,EAAE;MAC1C,oBACErB,OAAA,CAACnC,MAAM;QAACmJ,IAAI,EAAE7F,UAAW;QAAC8F,OAAO,EAAE7C,iBAAkB;QAAC8C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EpH,OAAA,CAAClC,WAAW;UAAAsJ,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACtDxH,OAAA,CAACjC,aAAa;UAAAqJ,QAAA,EACX9E,WAAW,gBACVtC,OAAA,CAACvB,gBAAgB;YAAA4I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBpF,IAAI,CAACkE,MAAM,KAAK,CAAC,gBACnBtG,OAAA,CAACxB,KAAK;YAACiJ,QAAQ,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAACjG,YAAY,gBACfvB,OAAA,CAAC7C,GAAG;YAAAiK,QAAA,gBACFpH,OAAA,CAAC5C,UAAU;cAACsK,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxH,OAAA,CAACxC,IAAI;cAAA4J,QAAA,EACFhF,IAAI,CAACwF,GAAG,CAAE1E,IAAI,iBACblD,OAAA,CAACvC,QAAQ;gBACPoK,MAAM;gBAENC,OAAO,EAAEA,CAAA,KAAMzD,gBAAgB,CAACnB,IAAI,CAAE;gBAAAkE,QAAA,eAEtCpH,OAAA,CAACtC,YAAY;kBACXqK,OAAO,EAAE7E,IAAI,CAACvB,OAAQ;kBACtBqG,SAAS,EAAE,mBAAmB9E,IAAI,CAACrB,SAAS,IAAI,eAAe;gBAAG;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC,GANGtE,IAAI,CAACvB,OAAO;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAENxH,OAAA,CAAC7C,GAAG;YAACiL,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBACjBpH,OAAA,CAAC5C,UAAU;cAACsK,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,GAAC,oBACzB,EAAC7F,YAAY,CAACI,OAAO;YAAA;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACbxH,OAAA,CAAC5C,UAAU;cAACsK,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAP,QAAA,GAAC,kBACvB,EAAC7F,YAAY,CAACM,SAAS,IAAI,eAAe;YAAA;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACbxH,OAAA,CAAC9B,SAAS;cACRoK,MAAM,EAAC,OAAO;cACdtD,IAAI,EAAC,WAAW;cAChBuD,KAAK,EAAC,WAAW;cACjBpB,SAAS;cACTO,OAAO,EAAC,UAAU;cAClB5C,KAAK,EAAErD,QAAQ,CAACI,SAAU;cAC1B4G,QAAQ,EAAE1D,gBAAiB;cAC3BqD,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBxH,OAAA,CAAC/B,aAAa;UAAAmJ,QAAA,gBACZpH,OAAA,CAAC3C,MAAM;YAACyK,OAAO,EAAE1D,iBAAkB;YAAAgD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDjG,YAAY,iBACXvB,OAAA,CAAC3C,MAAM;YACLyK,OAAO,EAAEvC,UAAW;YACpBqD,QAAQ,EAAE7H,OAAQ;YAClB8H,SAAS,EAAE9H,OAAO,gBAAGf,OAAA,CAACvB,gBAAgB;cAACqK,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGxH,OAAA,CAACR,QAAQ;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInG,UAAU,KAAK,eAAe,EAAE;MACzC,oBACErB,OAAA,CAACnC,MAAM;QAACmJ,IAAI,EAAE7F,UAAW;QAAC8F,OAAO,EAAE7C,iBAAkB;QAAC8C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EpH,OAAA,CAAClC,WAAW;UAAAsJ,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxCxH,OAAA,CAACjC,aAAa;UAAAqJ,QAAA,gBACZpH,OAAA,CAACxB,KAAK;YAACiJ,QAAQ,EAAC,MAAM;YAACW,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YAAA3B,QAAA,EAAC;UAGtC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAEPlF,WAAW,gBACVtC,OAAA,CAACvB,gBAAgB;YAAA4I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB,CAACjG,YAAY,gBACfvB,OAAA,CAAC7C,GAAG;YAACiL,EAAE,EAAE;cAAEY,CAAC,EAAE;YAAE,CAAE;YAAA5B,QAAA,gBAChBpH,OAAA,CAAC5C,UAAU;cAACsK,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxH,OAAA,CAAC7C,GAAG;cAACiL,EAAE,EAAE;gBAAEa,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEb,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBACxDpH,OAAA,CAAC9B,SAAS;gBACRiJ,SAAS;gBACToB,KAAK,EAAC,SAAS;gBACfb,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAEhD,WAAY;gBACnB2G,QAAQ,EAAE9D,uBAAwB;gBAClCwE,WAAW,EAAC;cAAyB;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACFxH,OAAA,CAAC3C,MAAM;gBACLqK,OAAO,EAAC,WAAW;gBACnB0B,KAAK,EAAC,SAAS;gBACftB,OAAO,EAAEvD,oBAAqB;gBAC9BqE,QAAQ,EAAEtG,WAAW,IAAI,CAACR,WAAW,CAAC0C,IAAI,CAAC,CAAE;gBAC7C4D,EAAE,EAAE;kBAAEiB,EAAE,EAAE,CAAC;kBAAEC,QAAQ,EAAE;gBAAQ,CAAE;gBAAAlC,QAAA,EAEhC9E,WAAW,gBAAGtC,OAAA,CAACvB,gBAAgB;kBAACqK,IAAI,EAAE;gBAAG;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG;cAAO;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAChBxH,OAAA,CAAC/B,aAAa;UAAAmJ,QAAA,eACZpH,OAAA,CAAC3C,MAAM;YAACyK,OAAO,EAAE1D,iBAAkB;YAAAgD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInG,UAAU,KAAK,aAAa,EAAE;MACvC;MACA,MAAMsF,WAAW,GAAGpF,YAAY,KAAKA,YAAY,CAAC8B,mBAAmB,KAAK,YAAY,IAAK9B,YAAY,CAAC6B,eAAe,IAAI7B,YAAY,CAAC6B,eAAe,GAAG,CAAE,CAAC;MAE7J,oBACEpD,OAAA,CAACnC,MAAM;QAACmJ,IAAI,EAAE7F,UAAW;QAAC8F,OAAO,EAAE7C,iBAAkB;QAAC8C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EpH,OAAA,CAAClC,WAAW;UAAAsJ,QAAA,EACT,CAAC7F,YAAY,GAAG,cAAc,GAC9BoF,WAAW,GAAG,uBAAuB,GAAG;QAAc;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACdxH,OAAA,CAACjC,aAAa;UAAAqJ,QAAA,EACX9E,WAAW,gBACVtC,OAAA,CAACvB,gBAAgB;YAAA4I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB,CAACjG,YAAY,gBACfvB,OAAA,CAAC7C,GAAG;YAACiL,EAAE,EAAE;cAAEY,CAAC,EAAE;YAAE,CAAE;YAAA5B,QAAA,gBAChBpH,OAAA,CAAC5C,UAAU;cAACsK,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxH,OAAA,CAAC7C,GAAG;cAACiL,EAAE,EAAE;gBAAEa,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEb,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBACxDpH,OAAA,CAAC9B,SAAS;gBACRiJ,SAAS;gBACToB,KAAK,EAAC,SAAS;gBACfb,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAEhD,WAAY;gBACnB2G,QAAQ,EAAE9D,uBAAwB;gBAClCwE,WAAW,EAAC;cAAyB;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACFxH,OAAA,CAAC3C,MAAM;gBACLqK,OAAO,EAAC,WAAW;gBACnB0B,KAAK,EAAC,SAAS;gBACftB,OAAO,EAAEvD,oBAAqB;gBAC9BqE,QAAQ,EAAEtG,WAAW,IAAI,CAACR,WAAW,CAAC0C,IAAI,CAAC,CAAE;gBAC7C4D,EAAE,EAAE;kBAAEiB,EAAE,EAAE,CAAC;kBAAEC,QAAQ,EAAE;gBAAQ,CAAE;gBAAAlC,QAAA,EAEhC9E,WAAW,gBAAGtC,OAAA,CAACvB,gBAAgB;kBAACqK,IAAI,EAAE;gBAAG;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG;cAAO;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJnG,UAAU,KAAK,aAAa,IAAIsF,WAAW,gBAC7C3G,OAAA,CAAAE,SAAA;YAAAkH,QAAA,gBACEpH,OAAA,CAAChC,iBAAiB;cAAAoJ,QAAA,GAAC,UACT,eAAApH,OAAA;gBAAAoH,QAAA,EAAS7F,YAAY,CAACI;cAAO;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,8CAC/C,EAACjG,YAAY,CAAC6B,eAAe,GAAG,CAAC,iBAC/BpD,OAAA,CAAAE,SAAA;gBAAAkH,QAAA,GAAE,iBAAe,eAAApH,OAAA;kBAAAoH,QAAA,GAAS7F,YAAY,CAAC6B,eAAe,EAAC,IAAE;gBAAA;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC;cAAA,eAAE,CACtE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACgB,CAAC,eACpBxH,OAAA,CAAChC,iBAAiB;cAACoK,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC;UAAA,eACpB,CAAC,GACDnG,UAAU,KAAK,aAAa,gBAC9BrB,OAAA,CAAAE,SAAA;YAAAkH,QAAA,gBACEpH,OAAA,CAAChC,iBAAiB;cAAAoJ,QAAA,GAAC,6BACU,eAAApH,OAAA;gBAAAoH,QAAA,EAAS7F,YAAY,CAACI;cAAO;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KACpE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAEpBxH,OAAA,CAAC7B,WAAW;cAACoL,SAAS,EAAC,UAAU;cAACnB,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBAC9CpH,OAAA,CAAClB,SAAS;gBAACyK,SAAS,EAAC,QAAQ;gBAAAnC,QAAA,EAAC;cAAgC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1ExH,OAAA,CAACpB,UAAU;gBACTkG,KAAK,EAAEtC,UAAW;gBAClBiG,QAAQ,EAAG7D,CAAC,IAAKnC,aAAa,CAACmC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAAAsC,QAAA,gBAE/CpH,OAAA,CAACnB,gBAAgB;kBACfiG,KAAK,EAAC,OAAO;kBACb0E,OAAO,eAAExJ,OAAA,CAACrB,KAAK;oBAAA0I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBe,KAAK,EAAC;gBAAqF;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC,eACFxH,OAAA,CAACnB,gBAAgB;kBACfiG,KAAK,EAAC,QAAQ;kBACd0E,OAAO,eAAExJ,OAAA,CAACrB,KAAK;oBAAA0I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBe,KAAK,EAAC;gBAAsE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA,eACd,CAAC,GACD;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAChBxH,OAAA,CAAC/B,aAAa;UAAAmJ,QAAA,gBACZpH,OAAA,CAAC3C,MAAM;YAACyK,OAAO,EAAE1D,iBAAkB;YAAAgD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDnG,UAAU,KAAK,aAAa,IAAIE,YAAY,iBAC3CvB,OAAA,CAAC3C,MAAM;YACLyK,OAAO,EAAEvC,UAAW;YACpBqD,QAAQ,EAAE7H,OAAQ;YAClBqI,KAAK,EAAEzC,WAAW,GAAG,SAAS,GAAG,OAAQ;YACzCkC,SAAS,EAAE9H,OAAO,gBAAGf,OAAA,CAACvB,gBAAgB;cAACqK,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAGb,WAAW,gBAAG3G,OAAA,CAACN,WAAW;cAAA2H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGxH,OAAA,CAACZ,UAAU;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAEpGT,WAAW,GAAG,kBAAkB,GAAInE,UAAU,KAAK,OAAO,GAAG,kBAAkB,GAAG;UAA0B;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAInG,UAAU,KAAK,cAAc,EAAE;MACxC,oBACErB,OAAA,CAACnC,MAAM;QAACmJ,IAAI,EAAE7F,UAAW;QAAC8F,OAAO,EAAE7C,iBAAkB;QAAC8C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EpH,OAAA,CAAClC,WAAW;UAAAsJ,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxCxH,OAAA,CAACjC,aAAa;UAAAqJ,QAAA,eACZpH,OAAA,CAACzB,IAAI;YAACkL,SAAS;YAACC,OAAO,EAAE,CAAE;YAACtB,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBACxCpH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,SAAS;gBACduD,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAACE,OAAQ;gBACxBiH,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,qBAAqB;gBAC1BuD,KAAK,EAAC,qBAAqB;gBAC3BpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAACqI,mBAAoB;gBACpCrB,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,SAAS;gBACduD,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAACsI,OAAQ;gBACxBtB,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,SAAS;gBACduD,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAACuI,OAAQ;gBACxBvB,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,aAAa;gBAClBuD,KAAK,EAAC,aAAa;gBACnBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAACwI,WAAY;gBAC5BxB,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,WAAW;gBAChBuD,KAAK,EAAC,WAAW;gBACjBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAACwG,SAAU;gBAC1BQ,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,cAAc;gBACnBuD,KAAK,EAAC,mBAAmB;gBACzBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAACyI,YAAa;gBAC7BzB,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,SAAS;gBACduD,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAAC0I,OAAQ;gBACxB1B,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,IAAI;gBACTuD,KAAK,EAAC,IAAI;gBACVpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAAC2I,EAAE,IAAI3I,QAAQ,CAAC4I,EAAG;gBAClC5B,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,qBAAqB;gBAC1BuD,KAAK,EAAC,qBAAqB;gBAC3BpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAACyG,mBAAoB;gBACpCO,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,iBAAiB;gBACtBuD,KAAK,EAAC,iBAAiB;gBACvBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAAC6I,eAAgB;gBAChC7B,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,6BAA6B;gBAClCuD,KAAK,EAAC,6BAA6B;gBACnCpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAAC8I,2BAA4B;gBAC5C9B,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,mBAAmB;gBACxBuD,KAAK,EAAC,mBAAmB;gBACzBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAAC0G,iBAAkB;gBAClCM,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,eAAe;gBACpBuD,KAAK,EAAC,eAAe;gBACrBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAAC+I,aAAc;gBAC9B/B,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,2BAA2B;gBAChCuD,KAAK,EAAC,2BAA2B;gBACjCpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAACgJ,yBAA0B;gBAC1ChC,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPxH,OAAA,CAACzB,IAAI;cAACoL,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBpH,OAAA,CAAC9B,SAAS;gBACR8G,IAAI,EAAC,eAAe;gBACpBuD,KAAK,EAAC,eAAe;gBACrBC,IAAI,EAAC,QAAQ;gBACbrB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB5C,KAAK,EAAErD,QAAQ,CAAC6C,aAAc;gBAC9BmE,QAAQ,EAAE1D;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAChBxH,OAAA,CAAC/B,aAAa;UAAAmJ,QAAA,gBACZpH,OAAA,CAAC3C,MAAM;YAACyK,OAAO,EAAE1D,iBAAkB;YAAAgD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDxH,OAAA,CAAC3C,MAAM;YACLyK,OAAO,EAAEvC,UAAW;YACpBqD,QAAQ,EAAE7H,OAAQ;YAClB8H,SAAS,EAAE9H,OAAO,gBAAGf,OAAA,CAACvB,gBAAgB;cAACqK,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGxH,OAAA,CAACR,QAAQ;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACExH,OAAA,CAAC7C,GAAG;IAACiL,EAAE,EAAE;MAAEa,OAAO,EAAE;IAAO,CAAE;IAAA7B,QAAA,gBAE3BpH,OAAA,CAAC7C,GAAG;MAACiL,EAAE,EAAE;QAAEsC,KAAK,EAAE,OAAO;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAvD,QAAA,eACjCpH,OAAA,CAAC1C,KAAK;QAAC8K,EAAE,EAAE;UAAEY,CAAC,EAAE,CAAC;UAAED,EAAE,EAAE;QAAE,CAAE;QAAA3B,QAAA,gBACzBpH,OAAA,CAAC5C,UAAU;UAACsK,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAP,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxH,OAAA,CAACzC,OAAO;UAAC6K,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE;QAAE;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BxH,OAAA,CAACxC,IAAI;UAAC+L,SAAS,EAAC,KAAK;UAACqB,KAAK;UAAAxD,QAAA,gBACzBpH,OAAA,CAACpC,cAAc;YAACkK,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,gBAAgB,CAAE;YAAAqD,QAAA,gBAClEpH,OAAA,CAACrC,YAAY;cAAAyJ,QAAA,eACXpH,OAAA,CAACV,SAAS;gBAAA+H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACfxH,OAAA,CAACtC,YAAY;cAACqK,OAAO,EAAC;YAA2B;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAEjBxH,OAAA,CAACpC,cAAc;YAACkK,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,gBAAgB,CAAE;YAAAqD,QAAA,gBAClEpH,OAAA,CAACrC,YAAY;cAAAyJ,QAAA,eACXpH,OAAA,CAACd,QAAQ;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACfxH,OAAA,CAACtC,YAAY;cAACqK,OAAO,EAAC;YAAgC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEjBxH,OAAA,CAACpC,cAAc;YAACkK,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,cAAc,CAAE;YAAAqD,QAAA,gBAChEpH,OAAA,CAACrC,YAAY;cAAAyJ,QAAA,eACXpH,OAAA,CAAChB,OAAO;gBAAAqI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACfxH,OAAA,CAACtC,YAAY;cAACqK,OAAO,EAAC;YAAwB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAEjBxH,OAAA,CAACpC,cAAc;YAACkK,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,cAAc,CAAE;YAAAqD,QAAA,gBAChEpH,OAAA,CAACrC,YAAY;cAAAyJ,QAAA,eACXpH,OAAA,CAACd,QAAQ;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACfxH,OAAA,CAACtC,YAAY;cAACqK,OAAO,EAAC;YAAkB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAEjBxH,OAAA,CAACpC,cAAc;YAACkK,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,aAAa,CAAE;YAAAqD,QAAA,gBAC/DpH,OAAA,CAACrC,YAAY;cAAAyJ,QAAA,eACXpH,OAAA,CAACZ,UAAU;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACfxH,OAAA,CAACtC,YAAY;cAACqK,OAAO,EAAC;YAAiB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eAEjBxH,OAAA,CAACpC,cAAc;YAACkK,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,kBAAkB,CAAE;YAAAqD,QAAA,gBACpEpH,OAAA,CAACrC,YAAY;cAAAyJ,QAAA,eACXpH,OAAA,CAACV,SAAS;gBAAA+H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACfxH,OAAA,CAACtC,YAAY;cAACqK,OAAO,EAAC;YAAsB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNxH,OAAA,CAAC7C,GAAG;MAACiL,EAAE,EAAE;QAAEyC,QAAQ,EAAE;MAAE,CAAE;MAAAzD,QAAA,eACvBpH,OAAA,CAAC1C,KAAK;QAAC8K,EAAE,EAAE;UAAEY,CAAC,EAAE,CAAC;UAAE8B,SAAS,EAAE,OAAO;UAAE7B,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAE6B,cAAc,EAAE;QAAS,CAAE;QAAA3D,QAAA,GACtG,CAACnG,cAAc,iBACdjB,OAAA,CAAC5C,UAAU;UAACsK,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAE5B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb,EACAvG,cAAc,IAAI,CAACE,UAAU,iBAC5BnB,OAAA,CAAC7C,GAAG;UAACiL,EAAE,EAAE;YAAE4C,SAAS,EAAE;UAAS,CAAE;UAAA5D,QAAA,gBAC/BpH,OAAA,CAAC5C,UAAU;YAACsK,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAP,QAAA,GAClCnG,cAAc,KAAK,gBAAgB,IAAI,wBAAwB,EAC/DA,cAAc,KAAK,cAAc,IAAI,eAAe,EACpDA,cAAc,KAAK,cAAc,IAAI,qBAAqB,EAC1DA,cAAc,KAAK,aAAa,IAAI,cAAc,EAClDA,cAAc,KAAK,gBAAgB,IAAI,6BAA6B,EACpEA,cAAc,KAAK,kBAAkB,IAAI,mBAAmB;UAAA;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACbxH,OAAA,CAAC5C,UAAU;YAACsK,OAAO,EAAC,OAAO;YAAAN,QAAA,EAAC;UAE5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxH,OAAA,CAACvB,gBAAgB;YAAC2J,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELT,YAAY,CAAC,CAAC;EAAA;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC/G,EAAA,CA37BIN,oBAAoB;AAAA8K,EAAA,GAApB9K,oBAAoB;AA67B1B,eAAeA,oBAAoB;AAAC,IAAA8K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}