'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useAuth } from '@/contexts/AuthContext'
import { certificazioniApi, strumentiApi, rapportiGeneraliApi, nonConformitaApi } from '@/lib/api'
import { CertificazioneCavo, StrumentoCertificato, RapportoGeneraleCollaudo, NonConformita } from '@/types/certificazioni'
import { 
  FileText, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Eye,
  Download,
  Upload,
  Loader2,
  <PERSON>,
  Settings,
  BarChart3,
  Alert<PERSON>rian<PERSON>
} from 'lucide-react'
import CertificazioneForm from './CertificazioneForm'
import StrumentiManager from './StrumentiManager'
import RapportiGeneraliManager from './RapportiGeneraliManager'
import CertificazioniStatistics from './CertificazioniStatistics'
import NonConformitaManager from './NonConformitaManager'

interface CertificazioniManagerProps {
  cantiereId: number
}

export default function CertificazioniManager({ cantiereId }: CertificazioniManagerProps) {
  const [activeTab, setActiveTab] = useState('certificazioni')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [certificazioni, setCertificazioni] = useState<CertificazioneCavo[]>([])
  const [strumenti, setStrumenti] = useState<StrumentoCertificato[]>([])
  const [rapporti, setRapporti] = useState<RapportoGeneraleCollaudo[]>([])
  const [nonConformita, setNonConformita] = useState<NonConformita[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [selectedCertificazione, setSelectedCertificazione] = useState<CertificazioneCavo | null>(null)

  const { user, cantiere } = useAuth()

  useEffect(() => {
    if (cantiereId) {
      loadData()
    }
  }, [cantiereId])

  const loadData = async () => {
    try {
      setIsLoading(true)
      setError('')
      
      const [
        certificazioniData,
        strumentiData,
        rapportiData,
        nonConformitaData
      ] = await Promise.all([
        certificazioniApi.getCertificazioni(cantiereId),
        strumentiApi.getStrumenti(cantiereId),
        rapportiGeneraliApi.getRapporti(cantiereId),
        nonConformitaApi.getNonConformita(cantiereId)
      ])

      setCertificazioni(certificazioniData)
      setStrumenti(strumentiData)
      setRapporti(rapportiData)
      setNonConformita(nonConformitaData)
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante il caricamento dei dati')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateCertificazione = () => {
    setSelectedCertificazione(null)
    setShowForm(true)
  }

  const handleEditCertificazione = (cert: CertificazioneCavo) => {
    setSelectedCertificazione(cert)
    setShowForm(true)
  }

  const handleDeleteCertificazione = async (id: number) => {
    if (!confirm('Sei sicuro di voler eliminare questa certificazione?')) return

    try {
      await certificazioniApi.deleteCertificazione(cantiereId, id)
      loadData()
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante l\'eliminazione')
    }
  }

  const getStatusBadge = (stato: string) => {
    switch (stato) {
      case 'CONFORME':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Conforme</Badge>
      case 'NON_CONFORME':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Non Conforme</Badge>
      case 'BOZZA':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Bozza</Badge>
      case 'IN_REVISIONE':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">In Revisione</Badge>
      default:
        return <Badge variant="outline">{stato}</Badge>
    }
  }

  const filteredCertificazioni = certificazioni.filter(cert => {
    const matchesSearch = !searchTerm || 
      cert.id_cavo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cert.responsabile_certificazione?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = selectedStatus === 'all' || cert.stato_certificato === selectedStatus
    
    return matchesSearch && matchesStatus
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          Caricamento certificazioni...
        </div>
      </div>
    )
  }

  if (showForm) {
    return (
      <CertificazioneForm
        cantiereId={cantiereId}
        certificazione={selectedCertificazione}
        strumenti={strumenti}
        onSuccess={() => {
          setShowForm(false)
          loadData()
        }}
        onCancel={() => setShowForm(false)}
        onStrumentiUpdate={loadData}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
            <Award className="h-8 w-8 text-blue-600" />
            Sistema Certificazioni CEI 64-8
          </h1>
          <p className="text-slate-600 mt-1">
            Gestione completa certificazioni, strumenti e rapporti di collaudo
          </p>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="certificazioni" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Certificazioni
          </TabsTrigger>
          <TabsTrigger value="strumenti" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Strumenti
          </TabsTrigger>
          <TabsTrigger value="rapporti" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Rapporti Generali
          </TabsTrigger>
          <TabsTrigger value="non-conformita" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Non Conformità
          </TabsTrigger>
          <TabsTrigger value="statistiche" className="flex items-center gap-2">
            <Award className="h-4 w-4" />
            Statistiche
          </TabsTrigger>
        </TabsList>

        {/* Certificazioni Tab */}
        <TabsContent value="certificazioni" className="space-y-6">
          {/* Controls */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  placeholder="Cerca per ID cavo o responsabile..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-slate-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">Tutti gli stati</option>
                <option value="CONFORME">Conforme</option>
                <option value="NON_CONFORME">Non Conforme</option>
                <option value="BOZZA">Bozza</option>
                <option value="IN_REVISIONE">In Revisione</option>
              </select>
            </div>
            
            <Button onClick={handleCreateCertificazione}>
              <Plus className="h-4 w-4 mr-2" />
              Nuova Certificazione
            </Button>
          </div>

          {/* Certificazioni List */}
          <Card>
            <CardHeader>
              <CardTitle>Certificazioni Cavi</CardTitle>
              <CardDescription>
                {filteredCertificazioni.length} certificazioni trovate
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>ID Cavo</TableHead>
                      <TableHead>Data Certificazione</TableHead>
                      <TableHead>Responsabile</TableHead>
                      <TableHead>Stato</TableHead>
                      <TableHead>Isolamento (MΩ)</TableHead>
                      <TableHead>Esito</TableHead>
                      <TableHead>Azioni</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredCertificazioni.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8 text-slate-500">
                          Nessuna certificazione trovata
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredCertificazioni.map((cert) => (
                        <TableRow key={cert.id_certificazione}>
                          <TableCell className="font-medium">{cert.id_cavo}</TableCell>
                          <TableCell>
                            {cert.data_certificazione ? 
                              new Date(cert.data_certificazione).toLocaleDateString('it-IT') : 
                              '-'
                            }
                          </TableCell>
                          <TableCell>{cert.responsabile_certificazione || '-'}</TableCell>
                          <TableCell>{getStatusBadge(cert.stato_certificato || 'BOZZA')}</TableCell>
                          <TableCell>{cert.valore_isolamento || '-'}</TableCell>
                          <TableCell>
                            {cert.esito_complessivo === 'CONFORME' ? (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : cert.esito_complessivo === 'NON_CONFORME' ? (
                              <AlertCircle className="h-4 w-4 text-red-600" />
                            ) : (
                              <Clock className="h-4 w-4 text-yellow-600" />
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditCertificazione(cert)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteCertificazione(cert.id_certificazione)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Altri Tab */}
        <TabsContent value="strumenti">
          <StrumentiManager
            cantiereId={cantiereId}
            strumenti={strumenti}
            onUpdate={loadData}
          />
        </TabsContent>

        <TabsContent value="rapporti">
          <RapportiGeneraliManager
            cantiereId={cantiereId}
            rapporti={rapporti}
            onUpdate={loadData}
          />
        </TabsContent>

        <TabsContent value="non-conformita">
          <NonConformitaManager
            cantiereId={cantiereId}
            nonConformita={nonConformita}
            onUpdate={loadData}
          />
        </TabsContent>

        <TabsContent value="statistiche">
          <CertificazioniStatistics
            cantiereId={cantiereId}
            certificazioni={certificazioni}
            strumenti={strumenti}
            nonConformita={nonConformita}
          />
        </TabsContent>
      </Tabs>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}
    </div>
  )
}
