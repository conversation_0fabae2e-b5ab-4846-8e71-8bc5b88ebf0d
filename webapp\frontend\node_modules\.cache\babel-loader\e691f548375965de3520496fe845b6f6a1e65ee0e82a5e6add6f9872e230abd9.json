{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { monthsInQuarter } from \"../constants/index.js\";\n/**\n * @name quartersToMonths\n * @category Conversion Helpers\n * @summary Convert number of quarters to months.\n *\n * @description\n * Convert a number of quarters to a full number of months.\n *\n * @param {number} quarters - number of quarters to be converted\n *\n * @returns {number} the number of quarters converted in months\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 quarters to months\n * const result = quartersToMonths(2)\n * //=> 6\n */\nexport default function quartersToMonths(quarters) {\n  requiredArgs(1, arguments);\n  return Math.floor(quarters * monthsInQuarter);\n}", "map": {"version": 3, "names": ["requiredArgs", "monthsInQuarter", "quartersToMonths", "quarters", "arguments", "Math", "floor"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/quartersToMonths/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { monthsInQuarter } from \"../constants/index.js\";\n/**\n * @name quartersToMonths\n * @category Conversion Helpers\n * @summary Convert number of quarters to months.\n *\n * @description\n * Convert a number of quarters to a full number of months.\n *\n * @param {number} quarters - number of quarters to be converted\n *\n * @returns {number} the number of quarters converted in months\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 quarters to months\n * const result = quartersToMonths(2)\n * //=> 6\n */\nexport default function quartersToMonths(quarters) {\n  requiredArgs(1, arguments);\n  return Math.floor(quarters * monthsInQuarter);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,eAAe,QAAQ,uBAAuB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EACjDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOC,IAAI,CAACC,KAAK,CAACH,QAAQ,GAAGF,eAAe,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}