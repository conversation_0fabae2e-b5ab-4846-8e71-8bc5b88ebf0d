{"ast": null, "code": "import { endOfDay } from \"./endOfDay.mjs\";\n\n/**\n * @name endOfToday\n * @category Day Helpers\n * @summary Return the end of today.\n * @pure false\n *\n * @description\n * Return the end of today.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @returns The end of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfToday()\n * //=> Mon Oct 6 2014 23:59:59.999\n */\nexport function endOfToday() {\n  return endOfDay(Date.now());\n}\n\n// Fallback for modularized imports:\nexport default endOfToday;", "map": {"version": 3, "names": ["endOfDay", "endOfToday", "Date", "now"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/endOfToday.mjs"], "sourcesContent": ["import { endOfDay } from \"./endOfDay.mjs\";\n\n/**\n * @name endOfToday\n * @category Day Helpers\n * @summary Return the end of today.\n * @pure false\n *\n * @description\n * Return the end of today.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @returns The end of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfToday()\n * //=> Mon Oct 6 2014 23:59:59.999\n */\nexport function endOfToday() {\n  return endOfDay(Date.now());\n}\n\n// Fallback for modularized imports:\nexport default endOfToday;\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAAA,EAAG;EAC3B,OAAOD,QAAQ,CAACE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;AAC7B;;AAEA;AACA,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}