{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cantieri\\\\CantierePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, Typography, Paper, Button, IconButton, Alert, CircularProgress, Grid, Card, CardContent, Divider, Tabs, Tab, List, ListItem, ListItemIcon, ListItemText, ListItemButton } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Home as HomeIcon, Refresh as RefreshIcon, Cable as CableIcon, ViewList as ViewListIcon, Engineering as EngineeringIcon, Inventory as InventoryIcon, TableChart as TableChartIcon, Assessment as AssessmentIcon, VerifiedUser as VerifiedUserIcon, ShoppingCart as ShoppingCartIcon } from '@mui/icons-material';\nimport { useAuth } from '../../context/AuthContext';\nimport cantieriService from '../../services/cantieriService';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CantierePage = () => {\n  _s();\n  const {\n    cantiereId\n  } = useParams();\n  const {\n    isImpersonating\n  } = useAuth();\n  const navigate = useNavigate();\n  const [cantiere, setCantiere] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loadingCavi, setLoadingCavi] = useState(false);\n  const [errorCavi, setErrorCavi] = useState(null);\n  const [tabValue, setTabValue] = useState(0);\n\n  // Carica i dettagli del cantiere\n  useEffect(() => {\n    const fetchCantiere = async () => {\n      try {\n        setLoading(true);\n        const data = await cantieriService.getCantiere(cantiereId);\n        setCantiere(data);\n\n        // Salva l'ID e il nome del cantiere nel localStorage per compatibilità con le pagine esistenti\n        localStorage.setItem('selectedCantiereId', data.id_cantiere.toString());\n        localStorage.setItem('selectedCantiereName', data.nome);\n\n        // Dopo aver caricato il cantiere, carica i cavi\n        await loadCavi(data.id_cantiere);\n      } catch (err) {\n        console.error('Errore nel caricamento del cantiere:', err);\n        setError('Impossibile caricare i dettagli del cantiere. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // Funzione per caricare i cavi del cantiere\n    const loadCavi = async idCantiere => {\n      try {\n        setLoadingCavi(true);\n        setErrorCavi(null);\n\n        // Carica i cavi attivi\n        console.log('Caricamento cavi attivi per cantiere:', idCantiere);\n        try {\n          const attivi = await caviService.getCavi(idCantiere, 0);\n          console.log('Cavi attivi caricati:', attivi);\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          setCaviAttivi([]);\n        }\n\n        // Carica i cavi spare\n        try {\n          const spare = await caviService.getCavi(idCantiere, 3);\n          console.log('Cavi spare caricati:', spare);\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          setCaviSpare([]);\n        }\n      } catch (error) {\n        console.error('Errore generale nel caricamento dei cavi:', error);\n        setErrorCavi('Impossibile caricare i cavi. Riprova più tardi.');\n      } finally {\n        setLoadingCavi(false);\n      }\n    };\n    if (cantiereId) {\n      fetchCantiere();\n    }\n  }, [cantiereId]);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Torna al menu amministratore (solo per admin che impersonano un utente)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Naviga alle diverse sezioni di gestione cavi\n  const navigateTo = path => {\n    navigate(path);\n  };\n\n  // Gestisce il cambio di tab\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n  };\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = cavi => {\n    if (cavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Nessun cavo trovato in questa categoria.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"div\",\n              children: cavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Sistema: \", cavo.sistema || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Tipologia: \", cavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Partenza: \", cavo.ubicazione_partenza || 'N/A', \" - \", cavo.utenza_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Arrivo: \", cavo.ubicazione_arrivo || 'N/A', \" - \", cavo.utenza_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Stato: \", cavo.stato_installazione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this)\n      }, cavo.id_cavo, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        mt: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: handleBackToCantieri,\n        sx: {\n          mt: 2\n        },\n        children: \"Torna alla lista cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this);\n  }\n  if (!cantiere) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"Cantiere non trovato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: handleBackToCantieri,\n        sx: {\n          mt: 2\n        },\n        children: \"Torna alla lista cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToCantieri,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          children: \"Dettagli Cantiere\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), isImpersonating && /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 24\n        }, this),\n        onClick: handleBackToAdmin,\n        children: \"Torna al Menu Admin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        gutterBottom: true,\n        children: cantiere.nome\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          mb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"ID:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), \" \", cantiere.id_cantiere]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          mb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Codice Univoco:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), \" \", cantiere.codice_univoco]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          mb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Data Creazione:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), \" \", new Date(cantiere.data_creazione).toLocaleString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), cantiere.descrizione && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          mb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"Descrizione:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this), \" \", cantiere.descrizione]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      color: \"primary\",\n      startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 20\n      }, this),\n      onClick: handleBackToCantieri,\n      sx: {\n        mb: 2\n      },\n      children: \"Torna alla Lista Cantieri\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: tabValue,\n          onChange: handleTabChange,\n          \"aria-label\": \"cavi tabs\",\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Cavi Attivi\",\n            icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 44\n            }, this),\n            iconPosition: \"start\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Cavi Spare\",\n            icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 43\n            }, this),\n            iconPosition: \"start\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 3\n        },\n        children: [tabValue === 0 && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Cavi Attivi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), loadingCavi ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'center',\n              my: 4\n            },\n            children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 17\n          }, this) : errorCavi ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 2\n            },\n            children: errorCavi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 17\n          }, this) : renderCaviTable(caviAttivi)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this), tabValue === 1 && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Cavi Spare\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this), loadingCavi ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'center',\n              my: 4\n            },\n            children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 17\n          }, this) : errorCavi ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 2\n            },\n            children: errorCavi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 17\n          }, this) : renderCaviTable(caviSpare)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 214,\n    columnNumber: 5\n  }, this);\n};\n_s(CantierePage, \"1cQ4N6Vpk71ToRZImzXFg6uyZyg=\", false, function () {\n  return [useParams, useAuth, useNavigate];\n});\n_c = CantierePage;\nexport default CantierePage;\nvar _c;\n$RefreshReg$(_c, \"CantierePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "CircularProgress", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "Tabs", "Tab", "List", "ListItem", "ListItemIcon", "ListItemText", "ListItemButton", "ArrowBack", "ArrowBackIcon", "Home", "HomeIcon", "Refresh", "RefreshIcon", "Cable", "CableIcon", "ViewList", "ViewListIcon", "Engineering", "EngineeringIcon", "Inventory", "InventoryIcon", "Table<PERSON>hart", "TableChartIcon", "Assessment", "AssessmentIcon", "VerifiedUser", "VerifiedUserIcon", "ShoppingCart", "ShoppingCartIcon", "useAuth", "cantieriService", "caviService", "jsxDEV", "_jsxDEV", "CantierePage", "_s", "cantiereId", "isImpersonating", "navigate", "cantiere", "setCantiere", "loading", "setLoading", "error", "setError", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loadingCavi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabValue", "setTabValue", "fetchCantiere", "data", "getCantiere", "localStorage", "setItem", "id_cantiere", "toString", "nome", "loadCavi", "err", "console", "idCantiere", "log", "attivi", "get<PERSON><PERSON>", "caviError", "spare", "spareError", "handleBackToCantieri", "handleBackToAdmin", "navigateTo", "path", "handleTabChange", "event", "newValue", "renderCaviTable", "cavi", "length", "severity", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "map", "cavo", "item", "xs", "sm", "md", "variant", "component", "id_cavo", "color", "sistema", "tipologia", "ubicazione_partenza", "utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "stato_installazione", "sx", "display", "justifyContent", "mt", "onClick", "mb", "alignItems", "mr", "window", "location", "reload", "ml", "title", "startIcon", "p", "gutterBottom", "codice_univoco", "Date", "data_creazione", "toLocaleString", "descrizione", "borderBottom", "borderColor", "value", "onChange", "label", "icon", "iconPosition", "my", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cantieri/CantierePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert,\n  CircularProgress,\n  Grid,\n  Card,\n  CardContent,\n  Divider,\n  Tabs,\n  Tab,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  ListItemButton\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Home as HomeIcon,\n  Refresh as RefreshIcon,\n  Cable as CableIcon,\n  ViewList as ViewListIcon,\n  Engineering as EngineeringIcon,\n  Inventory as InventoryIcon,\n  TableChart as TableChartIcon,\n  Assessment as AssessmentIcon,\n  VerifiedUser as VerifiedUserIcon,\n  ShoppingCart as ShoppingCartIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../../context/AuthContext';\nimport cantieriService from '../../services/cantieriService';\nimport caviService from '../../services/caviService';\n\nconst CantierePage = () => {\n  const { cantiereId } = useParams();\n  const { isImpersonating } = useAuth();\n  const navigate = useNavigate();\n\n  const [cantiere, setCantiere] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loadingCavi, setLoadingCavi] = useState(false);\n  const [errorCavi, setErrorCavi] = useState(null);\n  const [tabValue, setTabValue] = useState(0);\n\n  // Carica i dettagli del cantiere\n  useEffect(() => {\n    const fetchCantiere = async () => {\n      try {\n        setLoading(true);\n        const data = await cantieriService.getCantiere(cantiereId);\n        setCantiere(data);\n\n        // Salva l'ID e il nome del cantiere nel localStorage per compatibilità con le pagine esistenti\n        localStorage.setItem('selectedCantiereId', data.id_cantiere.toString());\n        localStorage.setItem('selectedCantiereName', data.nome);\n\n        // Dopo aver caricato il cantiere, carica i cavi\n        await loadCavi(data.id_cantiere);\n      } catch (err) {\n        console.error('Errore nel caricamento del cantiere:', err);\n        setError('Impossibile caricare i dettagli del cantiere. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // Funzione per caricare i cavi del cantiere\n    const loadCavi = async (idCantiere) => {\n      try {\n        setLoadingCavi(true);\n        setErrorCavi(null);\n\n        // Carica i cavi attivi\n        console.log('Caricamento cavi attivi per cantiere:', idCantiere);\n        try {\n          const attivi = await caviService.getCavi(idCantiere, 0);\n          console.log('Cavi attivi caricati:', attivi);\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          setCaviAttivi([]);\n        }\n\n        // Carica i cavi spare\n        try {\n          const spare = await caviService.getCavi(idCantiere, 3);\n          console.log('Cavi spare caricati:', spare);\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          setCaviSpare([]);\n        }\n      } catch (error) {\n        console.error('Errore generale nel caricamento dei cavi:', error);\n        setErrorCavi('Impossibile caricare i cavi. Riprova più tardi.');\n      } finally {\n        setLoadingCavi(false);\n      }\n    };\n\n    if (cantiereId) {\n      fetchCantiere();\n    }\n  }, [cantiereId]);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Torna al menu amministratore (solo per admin che impersonano un utente)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Naviga alle diverse sezioni di gestione cavi\n  const navigateTo = (path) => {\n    navigate(path);\n  };\n\n  // Gestisce il cambio di tab\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n  };\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = (cavi) => {\n    if (cavi.length === 0) {\n      return (\n        <Alert severity=\"info\">Nessun cavo trovato in questa categoria.</Alert>\n      );\n    }\n\n    return (\n      <Grid container spacing={2}>\n        {cavi.map((cavo) => (\n          <Grid item xs={12} sm={6} md={4} key={cavo.id_cavo}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" component=\"div\">\n                  {cavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Sistema: {cavo.sistema || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Tipologia: {cavo.tipologia || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Partenza: {cavo.ubicazione_partenza || 'N/A'} - {cavo.utenza_partenza || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Arrivo: {cavo.ubicazione_arrivo || 'N/A'} - {cavo.utenza_arrivo || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Stato: {cavo.stato_installazione || 'N/A'}\n                </Typography>\n              </CardContent>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n    );\n  };\n\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box sx={{ mt: 2 }}>\n        <Alert severity=\"error\">{error}</Alert>\n        <Button\n          variant=\"contained\"\n          onClick={handleBackToCantieri}\n          sx={{ mt: 2 }}\n        >\n          Torna alla lista cantieri\n        </Button>\n      </Box>\n    );\n  }\n\n  if (!cantiere) {\n    return (\n      <Box sx={{ mt: 2 }}>\n        <Alert severity=\"warning\">Cantiere non trovato</Alert>\n        <Button\n          variant=\"contained\"\n          onClick={handleBackToCantieri}\n          sx={{ mt: 2 }}\n        >\n          Torna alla lista cantieri\n        </Button>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Dettagli Cantiere\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        {isImpersonating && (\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<HomeIcon />}\n            onClick={handleBackToAdmin}\n          >\n            Torna al Menu Admin\n          </Button>\n        )}\n      </Box>\n\n      {/* Dettagli cantiere */}\n      <Paper sx={{ mb: 3, p: 3 }}>\n        <Typography variant=\"h5\" gutterBottom>\n          {cantiere.nome}\n        </Typography>\n        <Typography variant=\"body1\" sx={{ mb: 1 }}>\n          <strong>ID:</strong> {cantiere.id_cantiere}\n        </Typography>\n        <Typography variant=\"body1\" sx={{ mb: 1 }}>\n          <strong>Codice Univoco:</strong> {cantiere.codice_univoco}\n        </Typography>\n        <Typography variant=\"body1\" sx={{ mb: 1 }}>\n          <strong>Data Creazione:</strong> {new Date(cantiere.data_creazione).toLocaleString()}\n        </Typography>\n        {cantiere.descrizione && (\n          <Typography variant=\"body1\" sx={{ mb: 1 }}>\n            <strong>Descrizione:</strong> {cantiere.descrizione}\n          </Typography>\n        )}\n      </Paper>\n\n      {/* Tabs per visualizzare i cavi */}\n      <Button\n        variant=\"contained\"\n        color=\"primary\"\n        startIcon={<ArrowBackIcon />}\n        onClick={handleBackToCantieri}\n        sx={{ mb: 2 }}\n      >\n        Torna alla Lista Cantieri\n      </Button>\n\n      <Paper sx={{ mb: 3 }}>\n        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\n          <Tabs value={tabValue} onChange={handleTabChange} aria-label=\"cavi tabs\">\n            <Tab label=\"Cavi Attivi\" icon={<CableIcon />} iconPosition=\"start\" />\n            <Tab label=\"Cavi Spare\" icon={<CableIcon />} iconPosition=\"start\" />\n          </Tabs>\n        </Box>\n        <Box sx={{ p: 3 }}>\n          {tabValue === 0 && (\n            <Box>\n              <Typography variant=\"h6\" gutterBottom>\n                Cavi Attivi\n              </Typography>\n              {loadingCavi ? (\n                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n                  <CircularProgress />\n                </Box>\n              ) : errorCavi ? (\n                <Alert severity=\"error\" sx={{ mb: 2 }}>{errorCavi}</Alert>\n              ) : (\n                renderCaviTable(caviAttivi)\n              )}\n            </Box>\n          )}\n          {tabValue === 1 && (\n            <Box>\n              <Typography variant=\"h6\" gutterBottom>\n                Cavi Spare\n              </Typography>\n              {loadingCavi ? (\n                <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n                  <CircularProgress />\n                </Box>\n              ) : errorCavi ? (\n                <Alert severity=\"error\" sx={{ mb: 2 }}>{errorCavi}</Alert>\n              ) : (\n                renderCaviTable(caviSpare)\n              )}\n            </Box>\n          )}\n        </Box>\n      </Paper>\n    </Box>\n  );\n};\n\nexport default CantierePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,QACT,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,eAAe,EAC9BC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,YAAY,IAAIC,gBAAgB,EAChCC,YAAY,IAAIC,gBAAgB,QAC3B,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,gCAAgC;AAC5D,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAW,CAAC,GAAGjD,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEkD;EAAgB,CAAC,GAAGR,OAAO,CAAC,CAAC;EACrC,MAAMS,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACmD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACwD,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0D,KAAK,EAAEC,QAAQ,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8D,SAAS,EAAEC,YAAY,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkE,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoE,QAAQ,EAAEC,WAAW,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC;;EAE3C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMqE,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFb,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMc,IAAI,GAAG,MAAM1B,eAAe,CAAC2B,WAAW,CAACrB,UAAU,CAAC;QAC1DI,WAAW,CAACgB,IAAI,CAAC;;QAEjB;QACAE,YAAY,CAACC,OAAO,CAAC,oBAAoB,EAAEH,IAAI,CAACI,WAAW,CAACC,QAAQ,CAAC,CAAC,CAAC;QACvEH,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAEH,IAAI,CAACM,IAAI,CAAC;;QAEvD;QACA,MAAMC,QAAQ,CAACP,IAAI,CAACI,WAAW,CAAC;MAClC,CAAC,CAAC,OAAOI,GAAG,EAAE;QACZC,OAAO,CAACtB,KAAK,CAAC,sCAAsC,EAAEqB,GAAG,CAAC;QAC1DpB,QAAQ,CAAC,kEAAkE,CAAC;MAC9E,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;;IAED;IACA,MAAMqB,QAAQ,GAAG,MAAOG,UAAU,IAAK;MACrC,IAAI;QACFhB,cAAc,CAAC,IAAI,CAAC;QACpBE,YAAY,CAAC,IAAI,CAAC;;QAElB;QACAa,OAAO,CAACE,GAAG,CAAC,uCAAuC,EAAED,UAAU,CAAC;QAChE,IAAI;UACF,MAAME,MAAM,GAAG,MAAMrC,WAAW,CAACsC,OAAO,CAACH,UAAU,EAAE,CAAC,CAAC;UACvDD,OAAO,CAACE,GAAG,CAAC,uBAAuB,EAAEC,MAAM,CAAC;UAC5CtB,aAAa,CAACsB,MAAM,IAAI,EAAE,CAAC;QAC7B,CAAC,CAAC,OAAOE,SAAS,EAAE;UAClBL,OAAO,CAACtB,KAAK,CAAC,yCAAyC,EAAE2B,SAAS,CAAC;UACnExB,aAAa,CAAC,EAAE,CAAC;QACnB;;QAEA;QACA,IAAI;UACF,MAAMyB,KAAK,GAAG,MAAMxC,WAAW,CAACsC,OAAO,CAACH,UAAU,EAAE,CAAC,CAAC;UACtDD,OAAO,CAACE,GAAG,CAAC,sBAAsB,EAAEI,KAAK,CAAC;UAC1CvB,YAAY,CAACuB,KAAK,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,OAAOC,UAAU,EAAE;UACnBP,OAAO,CAACtB,KAAK,CAAC,wCAAwC,EAAE6B,UAAU,CAAC;UACnExB,YAAY,CAAC,EAAE,CAAC;QAClB;MACF,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdsB,OAAO,CAACtB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjES,YAAY,CAAC,iDAAiD,CAAC;MACjE,CAAC,SAAS;QACRF,cAAc,CAAC,KAAK,CAAC;MACvB;IACF,CAAC;IAED,IAAId,UAAU,EAAE;MACdmB,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACnB,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMqC,oBAAoB,GAAGA,CAAA,KAAM;IACjCnC,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAED;EACA,MAAMoC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpC,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMqC,UAAU,GAAIC,IAAI,IAAK;IAC3BtC,QAAQ,CAACsC,IAAI,CAAC;EAChB,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CzB,WAAW,CAACyB,QAAQ,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;MACrB,oBACEjD,OAAA,CAACvC,KAAK;QAACyF,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAwC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAE3E;IAEA,oBACEvD,OAAA,CAACrC,IAAI;MAAC6F,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAN,QAAA,EACxBH,IAAI,CAACU,GAAG,CAAEC,IAAI,iBACb3D,OAAA,CAACrC,IAAI;QAACiG,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eAC9BnD,OAAA,CAACpC,IAAI;UAAAuF,QAAA,eACHnD,OAAA,CAACnC,WAAW;YAAAsF,QAAA,gBACVnD,OAAA,CAAC3C,UAAU;cAAC2G,OAAO,EAAC,IAAI;cAACC,SAAS,EAAC,KAAK;cAAAd,QAAA,EACrCQ,IAAI,CAACO;YAAO;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACbvD,OAAA,CAAC3C,UAAU;cAAC2G,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAhB,QAAA,GAAC,WACxC,EAACQ,IAAI,CAACS,OAAO,IAAI,KAAK;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACbvD,OAAA,CAAC3C,UAAU;cAAC2G,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAhB,QAAA,GAAC,aACtC,EAACQ,IAAI,CAACU,SAAS,IAAI,KAAK;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACbvD,OAAA,CAAC3C,UAAU;cAAC2G,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAhB,QAAA,GAAC,YACvC,EAACQ,IAAI,CAACW,mBAAmB,IAAI,KAAK,EAAC,KAAG,EAACX,IAAI,CAACY,eAAe,IAAI,KAAK;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACbvD,OAAA,CAAC3C,UAAU;cAAC2G,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAhB,QAAA,GAAC,UACzC,EAACQ,IAAI,CAACa,iBAAiB,IAAI,KAAK,EAAC,KAAG,EAACb,IAAI,CAACc,aAAa,IAAI,KAAK;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACbvD,OAAA,CAAC3C,UAAU;cAAC2G,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAhB,QAAA,GAAC,SAC1C,EAACQ,IAAI,CAACe,mBAAmB,IAAI,KAAK;YAAA;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GAtB6BI,IAAI,CAACO,OAAO;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuB5C,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX,CAAC;EAED,IAAI/C,OAAO,EAAE;IACX,oBACER,OAAA,CAAC5C,GAAG;MAACuH,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA3B,QAAA,eAC5DnD,OAAA,CAACtC,gBAAgB;QAAA0F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,IAAI7C,KAAK,EAAE;IACT,oBACEV,OAAA,CAAC5C,GAAG;MAACuH,EAAE,EAAE;QAAEG,EAAE,EAAE;MAAE,CAAE;MAAA3B,QAAA,gBACjBnD,OAAA,CAACvC,KAAK;QAACyF,QAAQ,EAAC,OAAO;QAAAC,QAAA,EAAEzC;MAAK;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACvCvD,OAAA,CAACzC,MAAM;QACLyG,OAAO,EAAC,WAAW;QACnBe,OAAO,EAAEvC,oBAAqB;QAC9BmC,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAA3B,QAAA,EACf;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,IAAI,CAACjD,QAAQ,EAAE;IACb,oBACEN,OAAA,CAAC5C,GAAG;MAACuH,EAAE,EAAE;QAAEG,EAAE,EAAE;MAAE,CAAE;MAAA3B,QAAA,gBACjBnD,OAAA,CAACvC,KAAK;QAACyF,QAAQ,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACtDvD,OAAA,CAACzC,MAAM;QACLyG,OAAO,EAAC,WAAW;QACnBe,OAAO,EAAEvC,oBAAqB;QAC9BmC,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAA3B,QAAA,EACf;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEvD,OAAA,CAAC5C,GAAG;IAAA+F,QAAA,gBACFnD,OAAA,CAAC5C,GAAG;MAACuH,EAAE,EAAE;QAAEK,EAAE,EAAE,CAAC;QAAEJ,OAAO,EAAE,MAAM;QAAEK,UAAU,EAAE,QAAQ;QAAEJ,cAAc,EAAE;MAAgB,CAAE;MAAA1B,QAAA,gBACzFnD,OAAA,CAAC5C,GAAG;QAACuH,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEK,UAAU,EAAE;QAAS,CAAE;QAAA9B,QAAA,gBACjDnD,OAAA,CAACxC,UAAU;UAACuH,OAAO,EAAEvC,oBAAqB;UAACmC,EAAE,EAAE;YAAEO,EAAE,EAAE;UAAE,CAAE;UAAA/B,QAAA,eACvDnD,OAAA,CAACzB,aAAa;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACbvD,OAAA,CAAC3C,UAAU;UAAC2G,OAAO,EAAC,IAAI;UAAAb,QAAA,EAAC;QAEzB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvD,OAAA,CAACxC,UAAU;UACTuH,OAAO,EAAEA,CAAA,KAAMI,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCV,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UACdnB,KAAK,EAAC,SAAS;UACfoB,KAAK,EAAC,oBAAoB;UAAApC,QAAA,eAE1BnD,OAAA,CAACrB,WAAW;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EACLnD,eAAe,iBACdJ,OAAA,CAACzC,MAAM;QACLyG,OAAO,EAAC,WAAW;QACnBG,KAAK,EAAC,SAAS;QACfqB,SAAS,eAAExF,OAAA,CAACvB,QAAQ;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBwB,OAAO,EAAEtC,iBAAkB;QAAAU,QAAA,EAC5B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNvD,OAAA,CAAC1C,KAAK;MAACqH,EAAE,EAAE;QAAEK,EAAE,EAAE,CAAC;QAAES,CAAC,EAAE;MAAE,CAAE;MAAAtC,QAAA,gBACzBnD,OAAA,CAAC3C,UAAU;QAAC2G,OAAO,EAAC,IAAI;QAAC0B,YAAY;QAAAvC,QAAA,EAClC7C,QAAQ,CAACuB;MAAI;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACbvD,OAAA,CAAC3C,UAAU;QAAC2G,OAAO,EAAC,OAAO;QAACW,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBACxCnD,OAAA;UAAAmD,QAAA,EAAQ;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACjD,QAAQ,CAACqB,WAAW;MAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACbvD,OAAA,CAAC3C,UAAU;QAAC2G,OAAO,EAAC,OAAO;QAACW,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBACxCnD,OAAA;UAAAmD,QAAA,EAAQ;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACjD,QAAQ,CAACqF,cAAc;MAAA;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACbvD,OAAA,CAAC3C,UAAU;QAAC2G,OAAO,EAAC,OAAO;QAACW,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBACxCnD,OAAA;UAAAmD,QAAA,EAAQ;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAAC,IAAIqC,IAAI,CAACtF,QAAQ,CAACuF,cAAc,CAAC,CAACC,cAAc,CAAC,CAAC;MAAA;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,EACZjD,QAAQ,CAACyF,WAAW,iBACnB/F,OAAA,CAAC3C,UAAU;QAAC2G,OAAO,EAAC,OAAO;QAACW,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBACxCnD,OAAA;UAAAmD,QAAA,EAAQ;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KAAC,EAACjD,QAAQ,CAACyF,WAAW;MAAA;QAAA3C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGRvD,OAAA,CAACzC,MAAM;MACLyG,OAAO,EAAC,WAAW;MACnBG,KAAK,EAAC,SAAS;MACfqB,SAAS,eAAExF,OAAA,CAACzB,aAAa;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC7BwB,OAAO,EAAEvC,oBAAqB;MAC9BmC,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAA7B,QAAA,EACf;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAETvD,OAAA,CAAC1C,KAAK;MAACqH,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAA7B,QAAA,gBACnBnD,OAAA,CAAC5C,GAAG;QAACuH,EAAE,EAAE;UAAEqB,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAU,CAAE;QAAA9C,QAAA,eACnDnD,OAAA,CAACjC,IAAI;UAACmI,KAAK,EAAE9E,QAAS;UAAC+E,QAAQ,EAAEvD,eAAgB;UAAC,cAAW,WAAW;UAAAO,QAAA,gBACtEnD,OAAA,CAAChC,GAAG;YAACoI,KAAK,EAAC,aAAa;YAACC,IAAI,eAAErG,OAAA,CAACnB,SAAS;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAC+C,YAAY,EAAC;UAAO;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrEvD,OAAA,CAAChC,GAAG;YAACoI,KAAK,EAAC,YAAY;YAACC,IAAI,eAAErG,OAAA,CAACnB,SAAS;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAC+C,YAAY,EAAC;UAAO;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNvD,OAAA,CAAC5C,GAAG;QAACuH,EAAE,EAAE;UAAEc,CAAC,EAAE;QAAE,CAAE;QAAAtC,QAAA,GACf/B,QAAQ,KAAK,CAAC,iBACbpB,OAAA,CAAC5C,GAAG;UAAA+F,QAAA,gBACFnD,OAAA,CAAC3C,UAAU;YAAC2G,OAAO,EAAC,IAAI;YAAC0B,YAAY;YAAAvC,QAAA,EAAC;UAEtC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZvC,WAAW,gBACVhB,OAAA,CAAC5C,GAAG;YAACuH,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,QAAQ;cAAE0B,EAAE,EAAE;YAAE,CAAE;YAAApD,QAAA,eAC5DnD,OAAA,CAACtC,gBAAgB;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,GACJrC,SAAS,gBACXlB,OAAA,CAACvC,KAAK;YAACyF,QAAQ,EAAC,OAAO;YAACyB,EAAE,EAAE;cAAEK,EAAE,EAAE;YAAE,CAAE;YAAA7B,QAAA,EAAEjC;UAAS;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,GAE1DR,eAAe,CAACnC,UAAU,CAC3B;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EACAnC,QAAQ,KAAK,CAAC,iBACbpB,OAAA,CAAC5C,GAAG;UAAA+F,QAAA,gBACFnD,OAAA,CAAC3C,UAAU;YAAC2G,OAAO,EAAC,IAAI;YAAC0B,YAAY;YAAAvC,QAAA,EAAC;UAEtC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZvC,WAAW,gBACVhB,OAAA,CAAC5C,GAAG;YAACuH,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,QAAQ;cAAE0B,EAAE,EAAE;YAAE,CAAE;YAAApD,QAAA,eAC5DnD,OAAA,CAACtC,gBAAgB;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,GACJrC,SAAS,gBACXlB,OAAA,CAACvC,KAAK;YAACyF,QAAQ,EAAC,OAAO;YAACyB,EAAE,EAAE;cAAEK,EAAE,EAAE;YAAE,CAAE;YAAA7B,QAAA,EAAEjC;UAAS;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,GAE1DR,eAAe,CAACjC,SAAS,CAC1B;QAAA;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACrD,EAAA,CAxRID,YAAY;EAAA,QACO/C,SAAS,EACJ0C,OAAO,EAClBzC,WAAW;AAAA;AAAAqJ,EAAA,GAHxBvG,YAAY;AA0RlB,eAAeA,YAAY;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}