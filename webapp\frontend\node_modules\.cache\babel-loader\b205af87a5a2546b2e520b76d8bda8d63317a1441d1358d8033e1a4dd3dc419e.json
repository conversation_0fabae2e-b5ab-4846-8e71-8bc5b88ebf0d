{"ast": null, "code": "import { numberToLocale } from \"./localize.js\";\n\n// Source: https://www.unicode.org/cldr/charts/32/summary/hi.html\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"१ सेकंड से कम\",\n    // CLDR #1310\n    other: \"{{count}} सेकंड से कम\"\n  },\n  xSeconds: {\n    one: \"१ सेकंड\",\n    other: \"{{count}} सेकंड\"\n  },\n  halfAMinute: \"आधा मिनट\",\n  lessThanXMinutes: {\n    one: \"१ मिनट से कम\",\n    other: \"{{count}} मिनट से कम\"\n  },\n  xMinutes: {\n    one: \"१ मिनट\",\n    // CLDR #1307\n    other: \"{{count}} मिनट\"\n  },\n  aboutXHours: {\n    one: \"लगभग १ घंटा\",\n    other: \"लगभग {{count}} घंटे\"\n  },\n  xHours: {\n    one: \"१ घंटा\",\n    // CLDR #1304\n    other: \"{{count}} घंटे\" // CLDR #4467\n  },\n  xDays: {\n    one: \"१ दिन\",\n    // CLDR #1286\n    other: \"{{count}} दिन\"\n  },\n  aboutXWeeks: {\n    one: \"लगभग १ सप्ताह\",\n    other: \"लगभग {{count}} सप्ताह\"\n  },\n  xWeeks: {\n    one: \"१ सप्ताह\",\n    other: \"{{count}} सप्ताह\"\n  },\n  aboutXMonths: {\n    one: \"लगभग १ महीना\",\n    other: \"लगभग {{count}} महीने\"\n  },\n  xMonths: {\n    one: \"१ महीना\",\n    other: \"{{count}} महीने\"\n  },\n  aboutXYears: {\n    one: \"लगभग १ वर्ष\",\n    other: \"लगभग {{count}} वर्ष\" // CLDR #4823\n  },\n  xYears: {\n    one: \"१ वर्ष\",\n    other: \"{{count}} वर्ष\"\n  },\n  overXYears: {\n    one: \"१ वर्ष से अधिक\",\n    other: \"{{count}} वर्ष से अधिक\"\n  },\n  almostXYears: {\n    one: \"लगभग १ वर्ष\",\n    other: \"लगभग {{count}} वर्ष\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", numberToLocale(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"मे \";\n    } else {\n      return result + \" पहले\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["numberToLocale", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/hi/_lib/formatDistance.js"], "sourcesContent": ["import { numberToLocale } from \"./localize.js\";\n\n// Source: https://www.unicode.org/cldr/charts/32/summary/hi.html\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"१ सेकंड से कम\", // CLDR #1310\n    other: \"{{count}} सेकंड से कम\",\n  },\n\n  xSeconds: {\n    one: \"१ सेकंड\",\n    other: \"{{count}} सेकंड\",\n  },\n\n  halfAMinute: \"आधा मिनट\",\n\n  lessThanXMinutes: {\n    one: \"१ मिनट से कम\",\n    other: \"{{count}} मिनट से कम\",\n  },\n\n  xMinutes: {\n    one: \"१ मिनट\", // CLDR #1307\n    other: \"{{count}} मिनट\",\n  },\n\n  aboutXHours: {\n    one: \"लगभग १ घंटा\",\n    other: \"लगभग {{count}} घंटे\",\n  },\n\n  xHours: {\n    one: \"१ घंटा\", // CLDR #1304\n    other: \"{{count}} घंटे\", // CLDR #4467\n  },\n\n  xDays: {\n    one: \"१ दिन\", // CLDR #1286\n    other: \"{{count}} दिन\",\n  },\n\n  aboutXWeeks: {\n    one: \"लगभग १ सप्ताह\",\n    other: \"लगभग {{count}} सप्ताह\",\n  },\n\n  xWeeks: {\n    one: \"१ सप्ताह\",\n    other: \"{{count}} सप्ताह\",\n  },\n\n  aboutXMonths: {\n    one: \"लगभग १ महीना\",\n    other: \"लगभग {{count}} महीने\",\n  },\n\n  xMonths: {\n    one: \"१ महीना\",\n    other: \"{{count}} महीने\",\n  },\n\n  aboutXYears: {\n    one: \"लगभग १ वर्ष\",\n    other: \"लगभग {{count}} वर्ष\", // CLDR #4823\n  },\n\n  xYears: {\n    one: \"१ वर्ष\",\n    other: \"{{count}} वर्ष\",\n  },\n\n  overXYears: {\n    one: \"१ वर्ष से अधिक\",\n    other: \"{{count}} वर्ष से अधिक\",\n  },\n\n  almostXYears: {\n    one: \"लगभग १ वर्ष\",\n    other: \"लगभग {{count}} वर्ष\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", numberToLocale(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \"मे \";\n    } else {\n      return result + \" पहले\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,eAAe;;AAE9C;;AAEA,MAAMC,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,eAAe;IAAE;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,UAAU;EAEvBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,QAAQ;IAAE;IACfC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IAAE;IACfC,KAAK,EAAE,gBAAgB,CAAE;EAC3B,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IAAE;IACdC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,qBAAqB,CAAE;EAChC,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAE1B,cAAc,CAACsB,KAAK,CAAC,CAAC;EACvE;EAEA,IAAIC,OAAO,EAAEI,SAAS,EAAE;IACtB,IAAIJ,OAAO,CAACK,UAAU,IAAIL,OAAO,CAACK,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOJ,MAAM,GAAG,KAAK;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,OAAO;IACzB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}