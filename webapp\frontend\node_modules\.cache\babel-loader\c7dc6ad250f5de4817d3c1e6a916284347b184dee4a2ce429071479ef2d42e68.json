{"ast": null, "code": "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\nconst dateFormats = {\n  full: \"EEEE, do MMMM, y\",\n  // CLDR #1787\n  long: \"do MMMM, y\",\n  // CLDR #1788\n  medium: \"d MMM, y\",\n  // CLDR #1789\n  short: \"dd/MM/yyyy\" // CLDR #1790\n};\nconst timeFormats = {\n  full: \"h:mm:ss a zzzz\",\n  // CLDR #1791\n  long: \"h:mm:ss a z\",\n  // CLDR #1792\n  medium: \"h:mm:ss a\",\n  // CLDR #1793\n  short: \"h:mm a\" // CLDR #1794\n};\nconst dateTimeFormats = {\n  full: \"{{date}} 'को' {{time}}\",\n  // CLDR #1795\n  long: \"{{date}} 'को' {{time}}\",\n  // CLDR #1796\n  medium: \"{{date}}, {{time}}\",\n  // CLDR #1797\n  short: \"{{date}}, {{time}}\" // CLDR #1798\n};\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};", "map": {"version": 3, "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/hi/_lib/formatLong.js"], "sourcesContent": ["import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\nconst dateFormats = {\n  full: \"EEEE, do MMMM, y\", // CLDR #1787\n  long: \"do MMMM, y\", // CLDR #1788\n  medium: \"d MMM, y\", // CLDR #1789\n  short: \"dd/MM/yyyy\", // CLDR #1790\n};\n\nconst timeFormats = {\n  full: \"h:mm:ss a zzzz\", // CLDR #1791\n  long: \"h:mm:ss a z\", // CLDR #1792\n  medium: \"h:mm:ss a\", // CLDR #1793\n  short: \"h:mm a\", // CLDR #1794\n};\n\nconst dateTimeFormats = {\n  full: \"{{date}} 'को' {{time}}\", // CLDR #1795\n  long: \"{{date}} 'को' {{time}}\", // CLDR #1796\n  medium: \"{{date}}, {{time}}\", // CLDR #1797\n  short: \"{{date}}, {{time}}\", // CLDR #1798\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,iCAAiC;AAEnE,MAAMC,WAAW,GAAG;EAClBC,IAAI,EAAE,kBAAkB;EAAE;EAC1BC,IAAI,EAAE,YAAY;EAAE;EACpBC,MAAM,EAAE,UAAU;EAAE;EACpBC,KAAK,EAAE,YAAY,CAAE;AACvB,CAAC;AAED,MAAMC,WAAW,GAAG;EAClBJ,IAAI,EAAE,gBAAgB;EAAE;EACxBC,IAAI,EAAE,aAAa;EAAE;EACrBC,MAAM,EAAE,WAAW;EAAE;EACrBC,KAAK,EAAE,QAAQ,CAAE;AACnB,CAAC;AAED,MAAME,eAAe,GAAG;EACtBL,IAAI,EAAE,wBAAwB;EAAE;EAChCC,IAAI,EAAE,wBAAwB;EAAE;EAChCC,MAAM,EAAE,oBAAoB;EAAE;EAC9BC,KAAK,EAAE,oBAAoB,CAAE;AAC/B,CAAC;AAED,OAAO,MAAMG,UAAU,GAAG;EACxBC,IAAI,EAAET,iBAAiB,CAAC;IACtBU,OAAO,EAAET,WAAW;IACpBU,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,IAAI,EAAEZ,iBAAiB,CAAC;IACtBU,OAAO,EAAEJ,WAAW;IACpBK,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFE,QAAQ,EAAEb,iBAAiB,CAAC;IAC1BU,OAAO,EAAEH,eAAe;IACxBI,YAAY,EAAE;EAChB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}