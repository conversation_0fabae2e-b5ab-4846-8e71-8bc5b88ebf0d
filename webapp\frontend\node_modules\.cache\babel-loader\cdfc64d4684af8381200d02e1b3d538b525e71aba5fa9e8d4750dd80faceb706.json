{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\SmartCaviFilter.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, InputAdornment, IconButton, FormControl, InputLabel, Select, MenuItem, Typography, Chip, Button } from '@mui/material';\nimport { Search as SearchIcon, Clear as ClearIcon, Cancel as CancelIcon } from '@mui/icons-material';\n\n/**\n * Componente per il filtraggio intelligente dei cavi\n * Implementa un sistema di ricerca simile a quello delle bobine con supporto per termini multipli separati da virgola\n * \n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista completa dei cavi\n * @param {Function} props.onFilteredDataChange - Callback chiamata quando i dati filtrati cambiano\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SmartCaviFilter = ({\n  cavi = [],\n  onFilteredDataChange = null,\n  loading = false\n}) => {\n  _s();\n  const [searchText, setSearchText] = useState('');\n  const [searchType, setSearchType] = useState('contains'); // 'contains' o 'equals'\n  const [filteredCavi, setFilteredCavi] = useState(cavi);\n\n  // Aggiorna i dati filtrati quando cambiano i cavi di input\n  useEffect(() => {\n    setFilteredCavi(cavi);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(cavi);\n    }\n  }, [cavi, onFilteredDataChange]);\n\n  // Applica il filtro quando cambiano i parametri di ricerca\n  useEffect(() => {\n    applyFilter();\n  }, [searchText, searchType, cavi]);\n\n  /**\n   * Normalizza una stringa per la ricerca (lowercase, trim)\n   */\n  const normalizeString = str => {\n    return String(str || '').toLowerCase().trim();\n  };\n\n  /**\n   * Verifica se un termine numerico corrisponde a un valore\n   */\n  const matchesNumericTerm = (term, value) => {\n    const numericTerm = parseFloat(term);\n    const numericValue = parseFloat(String(value || '0'));\n    if (isNaN(numericTerm) || isNaN(numericValue)) {\n      return false;\n    }\n    return numericValue === numericTerm;\n  };\n\n  /**\n   * Estrae il numero dall'ID del cavo (es. \"CANT_001_C001\" -> \"001\")\n   */\n  const getCavoNumber = idCavo => {\n    if (!idCavo) return '';\n    const match = idCavo.match(/_C(\\d+)$/);\n    return match ? match[1] : idCavo;\n  };\n\n  /**\n   * Verifica se un cavo corrisponde a un termine di ricerca\n   */\n  const cavoMatchesTerm = (cavo, term, isExactMatch = false) => {\n    const termStr = normalizeString(term);\n    const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));\n\n    // Campi da cercare\n    const cavoId = normalizeString(cavo.id_cavo);\n    const cavoNumber = normalizeString(getCavoNumber(cavo.id_cavo));\n    const tipologia = normalizeString(cavo.tipologia);\n    const sezione = normalizeString(cavo.sezione);\n    const utility = normalizeString(cavo.utility);\n    const sistema = normalizeString(cavo.sistema);\n    const ubicazionePartenza = normalizeString(cavo.ubicazione_partenza);\n    const ubicazioneArrivo = normalizeString(cavo.ubicazione_arrivo);\n    const utenzaPartenza = normalizeString(cavo.utenza_partenza);\n    const utenzaArrivo = normalizeString(cavo.utenza_arrivo);\n    if (isExactMatch) {\n      // Ricerca esatta\n      if (isNumericTerm) {\n        // Per termini numerici, verifica corrispondenza esatta con sezione e numero cavo\n        return matchesNumericTerm(termStr, cavo.sezione) || cavoNumber === termStr || cavoId === termStr;\n      } else {\n        // Per termini testuali, verifica corrispondenza esatta\n        return cavoId === termStr || cavoNumber === termStr || tipologia === termStr || sezione === termStr || utility === termStr || sistema === termStr || ubicazionePartenza === termStr || ubicazioneArrivo === termStr || utenzaPartenza === termStr || utenzaArrivo === termStr;\n      }\n    } else {\n      // Ricerca con \"contiene\"\n      if (isNumericTerm) {\n        // Per termini numerici, verifica corrispondenza esatta con sezione\n        if (matchesNumericTerm(termStr, cavo.sezione)) {\n          return true;\n        }\n        // Verifica se il numero è contenuto nell'ID\n        if (cavoId.includes(termStr) || cavoNumber.includes(termStr)) {\n          return true;\n        }\n      }\n\n      // Ricerca standard con includes\n      return cavoId.includes(termStr) || cavoNumber.includes(termStr) || tipologia.includes(termStr) || sezione.includes(termStr) || utility.includes(termStr) || sistema.includes(termStr) || ubicazionePartenza.includes(termStr) || ubicazioneArrivo.includes(termStr) || utenzaPartenza.includes(termStr) || utenzaArrivo.includes(termStr);\n    }\n  };\n\n  /**\n   * Applica il filtro ai cavi\n   */\n  const applyFilter = () => {\n    if (!searchText.trim()) {\n      // Se non c'è testo di ricerca, mostra tutti i cavi\n      setFilteredCavi(cavi);\n      if (onFilteredDataChange) {\n        onFilteredDataChange(cavi);\n      }\n      return;\n    }\n\n    // Dividi il testo di ricerca in termini separati da virgola\n    const searchTerms = searchText.split(',').map(term => term.trim()).filter(term => term.length > 0);\n    let filtered = [];\n    if (searchType === 'equals') {\n      // Per la ricerca esatta con termini multipli, tutti i termini devono corrispondere (AND)\n      if (searchTerms.length === 1) {\n        // Singolo termine: ricerca esatta\n        filtered = cavi.filter(cavo => cavoMatchesTerm(cavo, searchTerms[0], true));\n      } else {\n        // Termini multipli: tutti devono corrispondere\n        filtered = cavi.filter(cavo => searchTerms.every(term => cavoMatchesTerm(cavo, term, true)));\n      }\n    } else {\n      // Per la ricerca con 'contains', almeno un termine deve corrispondere (OR)\n      filtered = cavi.filter(cavo => searchTerms.some(term => cavoMatchesTerm(cavo, term, false)));\n    }\n    setFilteredCavi(filtered);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(filtered);\n    }\n  };\n\n  /**\n   * Gestisce il cambio del testo di ricerca\n   */\n  const handleSearchTextChange = event => {\n    setSearchText(event.target.value);\n  };\n\n  /**\n   * Pulisce il filtro\n   */\n  const clearFilter = () => {\n    setSearchText('');\n    setSearchType('contains');\n  };\n\n  /**\n   * Conta i termini di ricerca\n   */\n  const getSearchTermsCount = () => {\n    if (!searchText.trim()) return 0;\n    return searchText.split(',').map(term => term.trim()).filter(term => term.length > 0).length;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: 2,\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(TextField, {\n        size: \"small\",\n        label: \"Ricerca intelligente cavi\",\n        variant: \"outlined\",\n        value: searchText,\n        onChange: handleSearchTextChange,\n        placeholder: \"ID, tipologia, formazione, utility, sistema, ubicazioni... (usa virgole per termini multipli)\",\n        disabled: loading,\n        sx: {\n          flexGrow: 1,\n          minWidth: '300px'\n        },\n        InputProps: {\n          startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n            position: \"start\",\n            children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this),\n          endAdornment: searchText ? /*#__PURE__*/_jsxDEV(InputAdornment, {\n            position: \"end\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              \"aria-label\": \"clear search\",\n              onClick: clearFilter,\n              edge: \"end\",\n              children: /*#__PURE__*/_jsxDEV(CancelIcon, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this) : null\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n        size: \"small\",\n        sx: {\n          minWidth: '140px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n          id: \"search-type-label\",\n          children: \"Tipo ricerca\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          labelId: \"search-type-label\",\n          value: searchType,\n          label: \"Tipo ricerca\",\n          onChange: e => setSearchType(e.target.value),\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"contains\",\n            children: \"Contiene\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: \"equals\",\n            children: \"Uguale a\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), searchText && /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        size: \"small\",\n        startIcon: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 24\n        }, this),\n        onClick: clearFilter,\n        disabled: loading,\n        children: \"Pulisci\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: 2,\n        flexWrap: 'wrap'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: loading ? 'Caricamento...' : `${filteredCavi.length} di ${cavi.length} cavi`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), searchText && /*#__PURE__*/_jsxDEV(Chip, {\n        size: \"small\",\n        label: `${getSearchTermsCount()} termine${getSearchTermsCount() > 1 ? 'i' : ''} di ricerca`,\n        color: \"primary\",\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 11\n      }, this), searchText && /*#__PURE__*/_jsxDEV(Chip, {\n        size: \"small\",\n        label: searchType === 'contains' ? 'Ricerca per contenuto' : 'Ricerca esatta',\n        color: \"secondary\",\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this), !searchText && /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"caption\",\n      color: \"text.secondary\",\n      sx: {\n        mt: 1,\n        display: 'block'\n      },\n      children: \"\\uD83D\\uDCA1 Suggerimenti: Usa virgole per cercare pi\\xF9 termini (es: \\\"240,MT,PRYSMIAN\\\"), numeri per sezioni (es: \\\"240\\\"), testo per tipologie/utility/ubicazioni\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 218,\n    columnNumber: 5\n  }, this);\n};\n_s(SmartCaviFilter, \"jMCHDK2P57fyguVoAS5xikjCafU=\");\n_c = SmartCaviFilter;\nexport default SmartCaviFilter;\nvar _c;\n$RefreshReg$(_c, \"SmartCaviFilter\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "InputAdornment", "IconButton", "FormControl", "InputLabel", "Select", "MenuItem", "Typography", "Chip", "<PERSON><PERSON>", "Search", "SearchIcon", "Clear", "ClearIcon", "Cancel", "CancelIcon", "jsxDEV", "_jsxDEV", "SmartCaviFilter", "cavi", "onFilteredDataChange", "loading", "_s", "searchText", "setSearchText", "searchType", "setSearchType", "filteredCavi", "setFilteredCavi", "applyFilter", "normalizeString", "str", "String", "toLowerCase", "trim", "matchesNumericTerm", "term", "value", "numericTerm", "parseFloat", "numericValue", "isNaN", "getCavoNumber", "idCavo", "match", "cavoMatchesTerm", "cavo", "isExactMatch", "termStr", "isNumericTerm", "cavoId", "id_cavo", "cavoNumber", "tipologia", "sezione", "utility", "sistema", "ubicazionePartenza", "ubicazione_partenza", "ubicazioneArrivo", "ubicazione_arrivo", "utenzaPartenza", "utenza_partenza", "utenzaArrivo", "utenza_arrivo", "includes", "searchTerms", "split", "map", "filter", "length", "filtered", "every", "some", "handleSearchTextChange", "event", "target", "clearFilter", "getSearchTermsCount", "sx", "mb", "children", "display", "alignItems", "gap", "size", "label", "variant", "onChange", "placeholder", "disabled", "flexGrow", "min<PERSON><PERSON><PERSON>", "InputProps", "startAdornment", "position", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "endAdornment", "onClick", "edge", "id", "labelId", "e", "startIcon", "flexWrap", "color", "mt", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/SmartCaviFilter.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  InputAdornment,\n  IconButton,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Typography,\n  Chip,\n  Button\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Clear as ClearIcon,\n  Cancel as CancelIcon\n} from '@mui/icons-material';\n\n/**\n * Componente per il filtraggio intelligente dei cavi\n * Implementa un sistema di ricerca simile a quello delle bobine con supporto per termini multipli separati da virgola\n * \n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista completa dei cavi\n * @param {Function} props.onFilteredDataChange - Callback chiamata quando i dati filtrati cambiano\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n */\nconst SmartCaviFilter = ({ \n  cavi = [], \n  onFilteredDataChange = null, \n  loading = false \n}) => {\n  const [searchText, setSearchText] = useState('');\n  const [searchType, setSearchType] = useState('contains'); // 'contains' o 'equals'\n  const [filteredCavi, setFilteredCavi] = useState(cavi);\n\n  // Aggiorna i dati filtrati quando cambiano i cavi di input\n  useEffect(() => {\n    setFilteredCavi(cavi);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(cavi);\n    }\n  }, [cavi, onFilteredDataChange]);\n\n  // Applica il filtro quando cambiano i parametri di ricerca\n  useEffect(() => {\n    applyFilter();\n  }, [searchText, searchType, cavi]);\n\n  /**\n   * Normalizza una stringa per la ricerca (lowercase, trim)\n   */\n  const normalizeString = (str) => {\n    return String(str || '').toLowerCase().trim();\n  };\n\n  /**\n   * Verifica se un termine numerico corrisponde a un valore\n   */\n  const matchesNumericTerm = (term, value) => {\n    const numericTerm = parseFloat(term);\n    const numericValue = parseFloat(String(value || '0'));\n    \n    if (isNaN(numericTerm) || isNaN(numericValue)) {\n      return false;\n    }\n    \n    return numericValue === numericTerm;\n  };\n\n  /**\n   * Estrae il numero dall'ID del cavo (es. \"CANT_001_C001\" -> \"001\")\n   */\n  const getCavoNumber = (idCavo) => {\n    if (!idCavo) return '';\n    const match = idCavo.match(/_C(\\d+)$/);\n    return match ? match[1] : idCavo;\n  };\n\n  /**\n   * Verifica se un cavo corrisponde a un termine di ricerca\n   */\n  const cavoMatchesTerm = (cavo, term, isExactMatch = false) => {\n    const termStr = normalizeString(term);\n    const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));\n\n    // Campi da cercare\n    const cavoId = normalizeString(cavo.id_cavo);\n    const cavoNumber = normalizeString(getCavoNumber(cavo.id_cavo));\n    const tipologia = normalizeString(cavo.tipologia);\n    const sezione = normalizeString(cavo.sezione);\n    const utility = normalizeString(cavo.utility);\n    const sistema = normalizeString(cavo.sistema);\n    const ubicazionePartenza = normalizeString(cavo.ubicazione_partenza);\n    const ubicazioneArrivo = normalizeString(cavo.ubicazione_arrivo);\n    const utenzaPartenza = normalizeString(cavo.utenza_partenza);\n    const utenzaArrivo = normalizeString(cavo.utenza_arrivo);\n\n    if (isExactMatch) {\n      // Ricerca esatta\n      if (isNumericTerm) {\n        // Per termini numerici, verifica corrispondenza esatta con sezione e numero cavo\n        return matchesNumericTerm(termStr, cavo.sezione) ||\n               cavoNumber === termStr ||\n               cavoId === termStr;\n      } else {\n        // Per termini testuali, verifica corrispondenza esatta\n        return cavoId === termStr ||\n               cavoNumber === termStr ||\n               tipologia === termStr ||\n               sezione === termStr ||\n               utility === termStr ||\n               sistema === termStr ||\n               ubicazionePartenza === termStr ||\n               ubicazioneArrivo === termStr ||\n               utenzaPartenza === termStr ||\n               utenzaArrivo === termStr;\n      }\n    } else {\n      // Ricerca con \"contiene\"\n      if (isNumericTerm) {\n        // Per termini numerici, verifica corrispondenza esatta con sezione\n        if (matchesNumericTerm(termStr, cavo.sezione)) {\n          return true;\n        }\n        // Verifica se il numero è contenuto nell'ID\n        if (cavoId.includes(termStr) || cavoNumber.includes(termStr)) {\n          return true;\n        }\n      }\n\n      // Ricerca standard con includes\n      return cavoId.includes(termStr) ||\n             cavoNumber.includes(termStr) ||\n             tipologia.includes(termStr) ||\n             sezione.includes(termStr) ||\n             utility.includes(termStr) ||\n             sistema.includes(termStr) ||\n             ubicazionePartenza.includes(termStr) ||\n             ubicazioneArrivo.includes(termStr) ||\n             utenzaPartenza.includes(termStr) ||\n             utenzaArrivo.includes(termStr);\n    }\n  };\n\n  /**\n   * Applica il filtro ai cavi\n   */\n  const applyFilter = () => {\n    if (!searchText.trim()) {\n      // Se non c'è testo di ricerca, mostra tutti i cavi\n      setFilteredCavi(cavi);\n      if (onFilteredDataChange) {\n        onFilteredDataChange(cavi);\n      }\n      return;\n    }\n\n    // Dividi il testo di ricerca in termini separati da virgola\n    const searchTerms = searchText.split(',')\n      .map(term => term.trim())\n      .filter(term => term.length > 0);\n\n    let filtered = [];\n\n    if (searchType === 'equals') {\n      // Per la ricerca esatta con termini multipli, tutti i termini devono corrispondere (AND)\n      if (searchTerms.length === 1) {\n        // Singolo termine: ricerca esatta\n        filtered = cavi.filter(cavo => \n          cavoMatchesTerm(cavo, searchTerms[0], true)\n        );\n      } else {\n        // Termini multipli: tutti devono corrispondere\n        filtered = cavi.filter(cavo => \n          searchTerms.every(term => cavoMatchesTerm(cavo, term, true))\n        );\n      }\n    } else {\n      // Per la ricerca con 'contains', almeno un termine deve corrispondere (OR)\n      filtered = cavi.filter(cavo => \n        searchTerms.some(term => cavoMatchesTerm(cavo, term, false))\n      );\n    }\n\n    setFilteredCavi(filtered);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(filtered);\n    }\n  };\n\n  /**\n   * Gestisce il cambio del testo di ricerca\n   */\n  const handleSearchTextChange = (event) => {\n    setSearchText(event.target.value);\n  };\n\n  /**\n   * Pulisce il filtro\n   */\n  const clearFilter = () => {\n    setSearchText('');\n    setSearchType('contains');\n  };\n\n  /**\n   * Conta i termini di ricerca\n   */\n  const getSearchTermsCount = () => {\n    if (!searchText.trim()) return 0;\n    return searchText.split(',').map(term => term.trim()).filter(term => term.length > 0).length;\n  };\n\n  return (\n    <Box sx={{ mb: 3 }}>\n      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>\n        {/* Campo di ricerca principale */}\n        <TextField\n          size=\"small\"\n          label=\"Ricerca intelligente cavi\"\n          variant=\"outlined\"\n          value={searchText}\n          onChange={handleSearchTextChange}\n          placeholder=\"ID, tipologia, formazione, utility, sistema, ubicazioni... (usa virgole per termini multipli)\"\n          disabled={loading}\n          sx={{ flexGrow: 1, minWidth: '300px' }}\n          InputProps={{\n            startAdornment: (\n              <InputAdornment position=\"start\">\n                <SearchIcon fontSize=\"small\" />\n              </InputAdornment>\n            ),\n            endAdornment: searchText ? (\n              <InputAdornment position=\"end\">\n                <IconButton\n                  size=\"small\"\n                  aria-label=\"clear search\"\n                  onClick={clearFilter}\n                  edge=\"end\"\n                >\n                  <CancelIcon fontSize=\"small\" />\n                </IconButton>\n              </InputAdornment>\n            ) : null\n          }}\n        />\n\n        {/* Dropdown per il tipo di ricerca */}\n        <FormControl size=\"small\" sx={{ minWidth: '140px' }}>\n          <InputLabel id=\"search-type-label\">Tipo ricerca</InputLabel>\n          <Select\n            labelId=\"search-type-label\"\n            value={searchType}\n            label=\"Tipo ricerca\"\n            onChange={(e) => setSearchType(e.target.value)}\n            disabled={loading}\n          >\n            <MenuItem value=\"contains\">Contiene</MenuItem>\n            <MenuItem value=\"equals\">Uguale a</MenuItem>\n          </Select>\n        </FormControl>\n\n        {/* Pulsante per pulire tutti i filtri */}\n        {searchText && (\n          <Button\n            variant=\"outlined\"\n            size=\"small\"\n            startIcon={<ClearIcon />}\n            onClick={clearFilter}\n            disabled={loading}\n          >\n            Pulisci\n          </Button>\n        )}\n      </Box>\n\n      {/* Informazioni sui risultati */}\n      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>\n        {/* Statistiche di ricerca */}\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          {loading ? 'Caricamento...' : `${filteredCavi.length} di ${cavi.length} cavi`}\n        </Typography>\n\n        {/* Chip con informazioni sui termini di ricerca */}\n        {searchText && (\n          <Chip\n            size=\"small\"\n            label={`${getSearchTermsCount()} termine${getSearchTermsCount() > 1 ? 'i' : ''} di ricerca`}\n            color=\"primary\"\n            variant=\"outlined\"\n          />\n        )}\n\n        {/* Chip con tipo di ricerca attivo */}\n        {searchText && (\n          <Chip\n            size=\"small\"\n            label={searchType === 'contains' ? 'Ricerca per contenuto' : 'Ricerca esatta'}\n            color=\"secondary\"\n            variant=\"outlined\"\n          />\n        )}\n      </Box>\n\n      {/* Suggerimenti per l'uso */}\n      {!searchText && (\n        <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1, display: 'block' }}>\n          💡 Suggerimenti: Usa virgole per cercare più termini (es: \"240,MT,PRYSMIAN\"), \n          numeri per sezioni (es: \"240\"), testo per tipologie/utility/ubicazioni\n        </Typography>\n      )}\n    </Box>\n  );\n};\n\nexport default SmartCaviFilter;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,cAAc,EACdC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,IAAI,EACJC,MAAM,QACD,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA;AASA,MAAMC,eAAe,GAAGA,CAAC;EACvBC,IAAI,GAAG,EAAE;EACTC,oBAAoB,GAAG,IAAI;EAC3BC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAACsB,IAAI,CAAC;;EAEtD;EACArB,SAAS,CAAC,MAAM;IACd8B,eAAe,CAACT,IAAI,CAAC;IACrB,IAAIC,oBAAoB,EAAE;MACxBA,oBAAoB,CAACD,IAAI,CAAC;IAC5B;EACF,CAAC,EAAE,CAACA,IAAI,EAAEC,oBAAoB,CAAC,CAAC;;EAEhC;EACAtB,SAAS,CAAC,MAAM;IACd+B,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACN,UAAU,EAAEE,UAAU,EAAEN,IAAI,CAAC,CAAC;;EAElC;AACF;AACA;EACE,MAAMW,eAAe,GAAIC,GAAG,IAAK;IAC/B,OAAOC,MAAM,CAACD,GAAG,IAAI,EAAE,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;EAC/C,CAAC;;EAED;AACF;AACA;EACE,MAAMC,kBAAkB,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IAC1C,MAAMC,WAAW,GAAGC,UAAU,CAACH,IAAI,CAAC;IACpC,MAAMI,YAAY,GAAGD,UAAU,CAACP,MAAM,CAACK,KAAK,IAAI,GAAG,CAAC,CAAC;IAErD,IAAII,KAAK,CAACH,WAAW,CAAC,IAAIG,KAAK,CAACD,YAAY,CAAC,EAAE;MAC7C,OAAO,KAAK;IACd;IAEA,OAAOA,YAAY,KAAKF,WAAW;EACrC,CAAC;;EAED;AACF;AACA;EACE,MAAMI,aAAa,GAAIC,MAAM,IAAK;IAChC,IAAI,CAACA,MAAM,EAAE,OAAO,EAAE;IACtB,MAAMC,KAAK,GAAGD,MAAM,CAACC,KAAK,CAAC,UAAU,CAAC;IACtC,OAAOA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGD,MAAM;EAClC,CAAC;;EAED;AACF;AACA;EACE,MAAME,eAAe,GAAGA,CAACC,IAAI,EAAEV,IAAI,EAAEW,YAAY,GAAG,KAAK,KAAK;IAC5D,MAAMC,OAAO,GAAGlB,eAAe,CAACM,IAAI,CAAC;IACrC,MAAMa,aAAa,GAAG,CAACR,KAAK,CAACO,OAAO,CAAC,IAAI,CAACP,KAAK,CAACF,UAAU,CAACS,OAAO,CAAC,CAAC;;IAEpE;IACA,MAAME,MAAM,GAAGpB,eAAe,CAACgB,IAAI,CAACK,OAAO,CAAC;IAC5C,MAAMC,UAAU,GAAGtB,eAAe,CAACY,aAAa,CAACI,IAAI,CAACK,OAAO,CAAC,CAAC;IAC/D,MAAME,SAAS,GAAGvB,eAAe,CAACgB,IAAI,CAACO,SAAS,CAAC;IACjD,MAAMC,OAAO,GAAGxB,eAAe,CAACgB,IAAI,CAACQ,OAAO,CAAC;IAC7C,MAAMC,OAAO,GAAGzB,eAAe,CAACgB,IAAI,CAACS,OAAO,CAAC;IAC7C,MAAMC,OAAO,GAAG1B,eAAe,CAACgB,IAAI,CAACU,OAAO,CAAC;IAC7C,MAAMC,kBAAkB,GAAG3B,eAAe,CAACgB,IAAI,CAACY,mBAAmB,CAAC;IACpE,MAAMC,gBAAgB,GAAG7B,eAAe,CAACgB,IAAI,CAACc,iBAAiB,CAAC;IAChE,MAAMC,cAAc,GAAG/B,eAAe,CAACgB,IAAI,CAACgB,eAAe,CAAC;IAC5D,MAAMC,YAAY,GAAGjC,eAAe,CAACgB,IAAI,CAACkB,aAAa,CAAC;IAExD,IAAIjB,YAAY,EAAE;MAChB;MACA,IAAIE,aAAa,EAAE;QACjB;QACA,OAAOd,kBAAkB,CAACa,OAAO,EAAEF,IAAI,CAACQ,OAAO,CAAC,IACzCF,UAAU,KAAKJ,OAAO,IACtBE,MAAM,KAAKF,OAAO;MAC3B,CAAC,MAAM;QACL;QACA,OAAOE,MAAM,KAAKF,OAAO,IAClBI,UAAU,KAAKJ,OAAO,IACtBK,SAAS,KAAKL,OAAO,IACrBM,OAAO,KAAKN,OAAO,IACnBO,OAAO,KAAKP,OAAO,IACnBQ,OAAO,KAAKR,OAAO,IACnBS,kBAAkB,KAAKT,OAAO,IAC9BW,gBAAgB,KAAKX,OAAO,IAC5Ba,cAAc,KAAKb,OAAO,IAC1Be,YAAY,KAAKf,OAAO;MACjC;IACF,CAAC,MAAM;MACL;MACA,IAAIC,aAAa,EAAE;QACjB;QACA,IAAId,kBAAkB,CAACa,OAAO,EAAEF,IAAI,CAACQ,OAAO,CAAC,EAAE;UAC7C,OAAO,IAAI;QACb;QACA;QACA,IAAIJ,MAAM,CAACe,QAAQ,CAACjB,OAAO,CAAC,IAAII,UAAU,CAACa,QAAQ,CAACjB,OAAO,CAAC,EAAE;UAC5D,OAAO,IAAI;QACb;MACF;;MAEA;MACA,OAAOE,MAAM,CAACe,QAAQ,CAACjB,OAAO,CAAC,IACxBI,UAAU,CAACa,QAAQ,CAACjB,OAAO,CAAC,IAC5BK,SAAS,CAACY,QAAQ,CAACjB,OAAO,CAAC,IAC3BM,OAAO,CAACW,QAAQ,CAACjB,OAAO,CAAC,IACzBO,OAAO,CAACU,QAAQ,CAACjB,OAAO,CAAC,IACzBQ,OAAO,CAACS,QAAQ,CAACjB,OAAO,CAAC,IACzBS,kBAAkB,CAACQ,QAAQ,CAACjB,OAAO,CAAC,IACpCW,gBAAgB,CAACM,QAAQ,CAACjB,OAAO,CAAC,IAClCa,cAAc,CAACI,QAAQ,CAACjB,OAAO,CAAC,IAChCe,YAAY,CAACE,QAAQ,CAACjB,OAAO,CAAC;IACvC;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMnB,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACN,UAAU,CAACW,IAAI,CAAC,CAAC,EAAE;MACtB;MACAN,eAAe,CAACT,IAAI,CAAC;MACrB,IAAIC,oBAAoB,EAAE;QACxBA,oBAAoB,CAACD,IAAI,CAAC;MAC5B;MACA;IACF;;IAEA;IACA,MAAM+C,WAAW,GAAG3C,UAAU,CAAC4C,KAAK,CAAC,GAAG,CAAC,CACtCC,GAAG,CAAChC,IAAI,IAAIA,IAAI,CAACF,IAAI,CAAC,CAAC,CAAC,CACxBmC,MAAM,CAACjC,IAAI,IAAIA,IAAI,CAACkC,MAAM,GAAG,CAAC,CAAC;IAElC,IAAIC,QAAQ,GAAG,EAAE;IAEjB,IAAI9C,UAAU,KAAK,QAAQ,EAAE;MAC3B;MACA,IAAIyC,WAAW,CAACI,MAAM,KAAK,CAAC,EAAE;QAC5B;QACAC,QAAQ,GAAGpD,IAAI,CAACkD,MAAM,CAACvB,IAAI,IACzBD,eAAe,CAACC,IAAI,EAAEoB,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAC5C,CAAC;MACH,CAAC,MAAM;QACL;QACAK,QAAQ,GAAGpD,IAAI,CAACkD,MAAM,CAACvB,IAAI,IACzBoB,WAAW,CAACM,KAAK,CAACpC,IAAI,IAAIS,eAAe,CAACC,IAAI,EAAEV,IAAI,EAAE,IAAI,CAAC,CAC7D,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACAmC,QAAQ,GAAGpD,IAAI,CAACkD,MAAM,CAACvB,IAAI,IACzBoB,WAAW,CAACO,IAAI,CAACrC,IAAI,IAAIS,eAAe,CAACC,IAAI,EAAEV,IAAI,EAAE,KAAK,CAAC,CAC7D,CAAC;IACH;IAEAR,eAAe,CAAC2C,QAAQ,CAAC;IACzB,IAAInD,oBAAoB,EAAE;MACxBA,oBAAoB,CAACmD,QAAQ,CAAC;IAChC;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMG,sBAAsB,GAAIC,KAAK,IAAK;IACxCnD,aAAa,CAACmD,KAAK,CAACC,MAAM,CAACvC,KAAK,CAAC;EACnC,CAAC;;EAED;AACF;AACA;EACE,MAAMwC,WAAW,GAAGA,CAAA,KAAM;IACxBrD,aAAa,CAAC,EAAE,CAAC;IACjBE,aAAa,CAAC,UAAU,CAAC;EAC3B,CAAC;;EAED;AACF;AACA;EACE,MAAMoD,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACvD,UAAU,CAACW,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC;IAChC,OAAOX,UAAU,CAAC4C,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAChC,IAAI,IAAIA,IAAI,CAACF,IAAI,CAAC,CAAC,CAAC,CAACmC,MAAM,CAACjC,IAAI,IAAIA,IAAI,CAACkC,MAAM,GAAG,CAAC,CAAC,CAACA,MAAM;EAC9F,CAAC;EAED,oBACErD,OAAA,CAAClB,GAAG;IAACgF,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACjBhE,OAAA,CAAClB,GAAG;MAACgF,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE,CAAC;QAAEJ,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAEhEhE,OAAA,CAACjB,SAAS;QACRqF,IAAI,EAAC,OAAO;QACZC,KAAK,EAAC,2BAA2B;QACjCC,OAAO,EAAC,UAAU;QAClBlD,KAAK,EAAEd,UAAW;QAClBiE,QAAQ,EAAEd,sBAAuB;QACjCe,WAAW,EAAC,+FAA+F;QAC3GC,QAAQ,EAAErE,OAAQ;QAClB0D,EAAE,EAAE;UAAEY,QAAQ,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAQ,CAAE;QACvCC,UAAU,EAAE;UACVC,cAAc,eACZ7E,OAAA,CAAChB,cAAc;YAAC8F,QAAQ,EAAC,OAAO;YAAAd,QAAA,eAC9BhE,OAAA,CAACN,UAAU;cAACqF,QAAQ,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CACjB;UACDC,YAAY,EAAE9E,UAAU,gBACtBN,OAAA,CAAChB,cAAc;YAAC8F,QAAQ,EAAC,KAAK;YAAAd,QAAA,eAC5BhE,OAAA,CAACf,UAAU;cACTmF,IAAI,EAAC,OAAO;cACZ,cAAW,cAAc;cACzBiB,OAAO,EAAEzB,WAAY;cACrB0B,IAAI,EAAC,KAAK;cAAAtB,QAAA,eAEVhE,OAAA,CAACF,UAAU;gBAACiF,QAAQ,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,GACf;QACN;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGFnF,OAAA,CAACd,WAAW;QAACkF,IAAI,EAAC,OAAO;QAACN,EAAE,EAAE;UAAEa,QAAQ,EAAE;QAAQ,CAAE;QAAAX,QAAA,gBAClDhE,OAAA,CAACb,UAAU;UAACoG,EAAE,EAAC,mBAAmB;UAAAvB,QAAA,EAAC;QAAY;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC5DnF,OAAA,CAACZ,MAAM;UACLoG,OAAO,EAAC,mBAAmB;UAC3BpE,KAAK,EAAEZ,UAAW;UAClB6D,KAAK,EAAC,cAAc;UACpBE,QAAQ,EAAGkB,CAAC,IAAKhF,aAAa,CAACgF,CAAC,CAAC9B,MAAM,CAACvC,KAAK,CAAE;UAC/CqD,QAAQ,EAAErE,OAAQ;UAAA4D,QAAA,gBAElBhE,OAAA,CAACX,QAAQ;YAAC+B,KAAK,EAAC,UAAU;YAAA4C,QAAA,EAAC;UAAQ;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC9CnF,OAAA,CAACX,QAAQ;YAAC+B,KAAK,EAAC,QAAQ;YAAA4C,QAAA,EAAC;UAAQ;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGb7E,UAAU,iBACTN,OAAA,CAACR,MAAM;QACL8E,OAAO,EAAC,UAAU;QAClBF,IAAI,EAAC,OAAO;QACZsB,SAAS,eAAE1F,OAAA,CAACJ,SAAS;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBE,OAAO,EAAEzB,WAAY;QACrBa,QAAQ,EAAErE,OAAQ;QAAA4D,QAAA,EACnB;MAED;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNnF,OAAA,CAAClB,GAAG;MAACgF,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE,CAAC;QAAEwB,QAAQ,EAAE;MAAO,CAAE;MAAA3B,QAAA,gBAE3EhE,OAAA,CAACV,UAAU;QAACgF,OAAO,EAAC,OAAO;QAACsB,KAAK,EAAC,gBAAgB;QAAA5B,QAAA,EAC/C5D,OAAO,GAAG,gBAAgB,GAAG,GAAGM,YAAY,CAAC2C,MAAM,OAAOnD,IAAI,CAACmD,MAAM;MAAO;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,EAGZ7E,UAAU,iBACTN,OAAA,CAACT,IAAI;QACH6E,IAAI,EAAC,OAAO;QACZC,KAAK,EAAE,GAAGR,mBAAmB,CAAC,CAAC,WAAWA,mBAAmB,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,aAAc;QAC5F+B,KAAK,EAAC,SAAS;QACftB,OAAO,EAAC;MAAU;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACF,EAGA7E,UAAU,iBACTN,OAAA,CAACT,IAAI;QACH6E,IAAI,EAAC,OAAO;QACZC,KAAK,EAAE7D,UAAU,KAAK,UAAU,GAAG,uBAAuB,GAAG,gBAAiB;QAC9EoF,KAAK,EAAC,WAAW;QACjBtB,OAAO,EAAC;MAAU;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL,CAAC7E,UAAU,iBACVN,OAAA,CAACV,UAAU;MAACgF,OAAO,EAAC,SAAS;MAACsB,KAAK,EAAC,gBAAgB;MAAC9B,EAAE,EAAE;QAAE+B,EAAE,EAAE,CAAC;QAAE5B,OAAO,EAAE;MAAQ,CAAE;MAAAD,QAAA,EAAC;IAGtF;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9E,EAAA,CA/RIJ,eAAe;AAAA6F,EAAA,GAAf7F,eAAe;AAiSrB,eAAeA,eAAe;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}