{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link eachDayOfInterval} function options.\n */\n\n/**\n * @name eachDayOfInterval\n * @category Interval Helpers\n * @summary Return the array of dates within the specified time interval.\n *\n * @description\n * Return the array of dates within the specified time interval.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of days from the day of the interval start to the day of the interval end\n *\n * @example\n * // Each day between 6 October 2014 and 10 October 2014:\n * const result = eachDayOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 9, 10)\n * })\n * //=> [\n * //   Mon Oct 06 2014 00:00:00,\n * //   Tue Oct 07 2014 00:00:00,\n * //   Wed Oct 08 2014 00:00:00,\n * //   Thu Oct 09 2014 00:00:00,\n * //   Fri Oct 10 2014 00:00:00\n * // ]\n */\nexport function eachDayOfInterval(interval, options) {\n  const startDate = toDate(interval.start);\n  const endDate = toDate(interval.end);\n  let reversed = +startDate > +endDate;\n  const endTime = reversed ? +startDate : +endDate;\n  const currentDate = reversed ? endDate : startDate;\n  currentDate.setHours(0, 0, 0, 0);\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n  const dates = [];\n  while (+currentDate <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate.setDate(currentDate.getDate() + step);\n    currentDate.setHours(0, 0, 0, 0);\n  }\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachDayOfInterval;", "map": {"version": 3, "names": ["toDate", "eachDayOfInterval", "interval", "options", "startDate", "start", "endDate", "end", "reversed", "endTime", "currentDate", "setHours", "step", "dates", "push", "setDate", "getDate", "reverse"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/eachDayOfInterval.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link eachDayOfInterval} function options.\n */\n\n/**\n * @name eachDayOfInterval\n * @category Interval Helpers\n * @summary Return the array of dates within the specified time interval.\n *\n * @description\n * Return the array of dates within the specified time interval.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of days from the day of the interval start to the day of the interval end\n *\n * @example\n * // Each day between 6 October 2014 and 10 October 2014:\n * const result = eachDayOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 9, 10)\n * })\n * //=> [\n * //   Mon Oct 06 2014 00:00:00,\n * //   Tue Oct 07 2014 00:00:00,\n * //   Wed Oct 08 2014 00:00:00,\n * //   Thu Oct 09 2014 00:00:00,\n * //   Fri Oct 10 2014 00:00:00\n * // ]\n */\nexport function eachDayOfInterval(interval, options) {\n  const startDate = toDate(interval.start);\n  const endDate = toDate(interval.end);\n\n  let reversed = +startDate > +endDate;\n  const endTime = reversed ? +startDate : +endDate;\n  const currentDate = reversed ? endDate : startDate;\n  currentDate.setHours(0, 0, 0, 0);\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+currentDate <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate.setDate(currentDate.getDate() + step);\n    currentDate.setHours(0, 0, 0, 0);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachDayOfInterval;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACnD,MAAMC,SAAS,GAAGJ,MAAM,CAACE,QAAQ,CAACG,KAAK,CAAC;EACxC,MAAMC,OAAO,GAAGN,MAAM,CAACE,QAAQ,CAACK,GAAG,CAAC;EAEpC,IAAIC,QAAQ,GAAG,CAACJ,SAAS,GAAG,CAACE,OAAO;EACpC,MAAMG,OAAO,GAAGD,QAAQ,GAAG,CAACJ,SAAS,GAAG,CAACE,OAAO;EAChD,MAAMI,WAAW,GAAGF,QAAQ,GAAGF,OAAO,GAAGF,SAAS;EAClDM,WAAW,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAEhC,IAAIC,IAAI,GAAGT,OAAO,EAAES,IAAI,IAAI,CAAC;EAC7B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,IAAIA,IAAI,GAAG,CAAC,EAAE;IACZA,IAAI,GAAG,CAACA,IAAI;IACZJ,QAAQ,GAAG,CAACA,QAAQ;EACtB;EAEA,MAAMK,KAAK,GAAG,EAAE;EAEhB,OAAO,CAACH,WAAW,IAAID,OAAO,EAAE;IAC9BI,KAAK,CAACC,IAAI,CAACd,MAAM,CAACU,WAAW,CAAC,CAAC;IAC/BA,WAAW,CAACK,OAAO,CAACL,WAAW,CAACM,OAAO,CAAC,CAAC,GAAGJ,IAAI,CAAC;IACjDF,WAAW,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAClC;EAEA,OAAOH,QAAQ,GAAGK,KAAK,CAACI,OAAO,CAAC,CAAC,GAAGJ,KAAK;AAC3C;;AAEA;AACA,eAAeZ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}