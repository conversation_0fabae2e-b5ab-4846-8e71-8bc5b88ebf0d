{"ast": null, "code": "import React,{useState}from'react';import{Box,Typography,Paper,Button,IconButton,Alert,Snackbar}from'@mui/material';import{ArrowBack as ArrowBackIcon,Refresh as RefreshIcon,Home as HomeIcon}from'@mui/icons-material';import{useNavigate}from'react-router-dom';import{useAuth}from'../../../context/AuthContext';import AdminHomeButton from'../../../components/common/AdminHomeButton';import CollegamentiCavo from'../../../components/cavi/CollegamentiCavo';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CollegamentiPage=()=>{const navigate=useNavigate();const{isImpersonating}=useAuth();const[alertMessage,setAlertMessage]=useState(null);const[alertSeverity,setAlertSeverity]=useState('success');const[openSnackbar,setOpenSnackbar]=useState(false);// Recupera l'ID del cantiere selezionato dal localStorage\nconst cantiereId=localStorage.getItem('selectedCantiereId');const cantiereName=localStorage.getItem('selectedCantiereName');// Gestisce il ritorno alla pagina dei cantieri\nconst handleBackToCantieri=()=>{navigate('/dashboard/cantieri');};// Gestisce il ritorno al menu admin (per admin che impersonano utenti)\nconst handleBackToAdmin=()=>{navigate('/dashboard/admin');};// Gestisce il ritorno alla pagina principale di posa cavi\nconst handleBackToPosa=()=>{navigate('/dashboard/cavi/posa');};// Gestisce il successo di un'operazione\nconst handleSuccess=message=>{setAlertMessage(message);setAlertSeverity('success');setOpenSnackbar(true);};// Gestisce l'errore di un'operazione\nconst handleError=message=>{setAlertMessage(message);setAlertSeverity('error');setOpenSnackbar(true);};// Chiude lo snackbar\nconst handleCloseSnackbar=()=>{setOpenSnackbar(false);};// Se non c'è un cantiere selezionato, reindirizza alla pagina dei cantieri\nif(!cantiereId){navigate('/dashboard/cantieri');return null;}return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:3,display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(IconButton,{onClick:handleBackToPosa,sx:{mr:1},children:/*#__PURE__*/_jsx(ArrowBackIcon,{})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Gestisci Collegamenti Cavo\"}),/*#__PURE__*/_jsx(IconButton,{onClick:()=>window.location.reload(),sx:{ml:2},color:\"primary\",title:\"Ricarica la pagina\",children:/*#__PURE__*/_jsx(RefreshIcon,{})})]}),/*#__PURE__*/_jsx(AdminHomeButton,{})]}),/*#__PURE__*/_jsx(CollegamentiCavo,{cantiereId:cantiereId,onSuccess:handleSuccess,onError:handleError}),/*#__PURE__*/_jsx(Snackbar,{open:openSnackbar,autoHideDuration:6000,onClose:handleCloseSnackbar,anchorOrigin:{vertical:'bottom',horizontal:'center'},children:/*#__PURE__*/_jsx(Alert,{onClose:handleCloseSnackbar,severity:alertSeverity,sx:{width:'100%'},children:alertMessage})})]});};export default CollegamentiPage;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "Snackbar", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Home", "HomeIcon", "useNavigate", "useAuth", "AdminHomeButton", "CollegamentiCavo", "jsx", "_jsx", "jsxs", "_jsxs", "CollegamentiPage", "navigate", "isImpersonating", "alertMessage", "setAlertMessage", "alertSeverity", "setAlertSeverity", "openSnackbar", "setOpenSnackbar", "cantiereId", "localStorage", "getItem", "cantiereName", "handleBackToCantieri", "handleBackToAdmin", "handleBackToPosa", "handleSuccess", "message", "handleError", "handleCloseSnackbar", "children", "sx", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "variant", "window", "location", "reload", "ml", "color", "title", "onSuccess", "onError", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "width"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/posa/CollegamentiPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert,\n  Snackbar\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Home as HomeIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport CollegamentiCavo from '../../../components/cavi/CollegamentiCavo';\n\nconst CollegamentiPage = () => {\n  const navigate = useNavigate();\n  const { isImpersonating } = useAuth();\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  const cantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Gestisce il ritorno alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce il ritorno al menu admin (per admin che impersonano utenti)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce il ritorno alla pagina principale di posa cavi\n  const handleBackToPosa = () => {\n    navigate('/dashboard/cavi/posa');\n  };\n\n  // Gestisce il successo di un'operazione\n  const handleSuccess = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce l'errore di un'operazione\n  const handleError = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  // Se non c'è un cantiere selezionato, reindirizza alla pagina dei cantieri\n  if (!cantiereId) {\n    navigate('/dashboard/cantieri');\n    return null;\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToPosa} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h6\">\n            Gestisci Collegamenti Cavo\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Componente per la gestione dei collegamenti */}\n      <CollegamentiCavo\n        cantiereId={cantiereId}\n        onSuccess={handleSuccess}\n        onError={handleError}\n      />\n\n      <Snackbar\n        open={openSnackbar}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>\n          {alertMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default CollegamentiPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,UAAU,CACVC,KAAK,CACLC,QAAQ,KACH,eAAe,CACtB,OACEC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,OAAO,GAAI,CAAAC,WAAW,CACtBC,IAAI,GAAI,CAAAC,QAAQ,KACX,qBAAqB,CAC5B,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,OAAO,KAAQ,8BAA8B,CACtD,MAAO,CAAAC,eAAe,KAAM,4CAA4C,CACxE,MAAO,CAAAC,gBAAgB,KAAM,2CAA2C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzE,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAC,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEU,eAAgB,CAAC,CAAGT,OAAO,CAAC,CAAC,CACrC,KAAM,CAACU,YAAY,CAAEC,eAAe,CAAC,CAAG1B,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAC2B,aAAa,CAAEC,gBAAgB,CAAC,CAAG5B,QAAQ,CAAC,SAAS,CAAC,CAC7D,KAAM,CAAC6B,YAAY,CAAEC,eAAe,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CAEvD;AACA,KAAM,CAAA+B,UAAU,CAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAC7D,KAAM,CAAAC,YAAY,CAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAEjE;AACA,KAAM,CAAAE,oBAAoB,CAAGA,CAAA,GAAM,CACjCZ,QAAQ,CAAC,qBAAqB,CAAC,CACjC,CAAC,CAED;AACA,KAAM,CAAAa,iBAAiB,CAAGA,CAAA,GAAM,CAC9Bb,QAAQ,CAAC,kBAAkB,CAAC,CAC9B,CAAC,CAED;AACA,KAAM,CAAAc,gBAAgB,CAAGA,CAAA,GAAM,CAC7Bd,QAAQ,CAAC,sBAAsB,CAAC,CAClC,CAAC,CAED;AACA,KAAM,CAAAe,aAAa,CAAIC,OAAO,EAAK,CACjCb,eAAe,CAACa,OAAO,CAAC,CACxBX,gBAAgB,CAAC,SAAS,CAAC,CAC3BE,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAU,WAAW,CAAID,OAAO,EAAK,CAC/Bb,eAAe,CAACa,OAAO,CAAC,CACxBX,gBAAgB,CAAC,OAAO,CAAC,CACzBE,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAW,mBAAmB,CAAGA,CAAA,GAAM,CAChCX,eAAe,CAAC,KAAK,CAAC,CACxB,CAAC,CAED;AACA,GAAI,CAACC,UAAU,CAAE,CACfR,QAAQ,CAAC,qBAAqB,CAAC,CAC/B,MAAO,KAAI,CACb,CAEA,mBACEF,KAAA,CAACpB,GAAG,EAAAyC,QAAA,eACFrB,KAAA,CAACpB,GAAG,EAAC0C,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,eAAgB,CAAE,CAAAL,QAAA,eACzFrB,KAAA,CAACpB,GAAG,EAAC0C,EAAE,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAJ,QAAA,eACjDvB,IAAA,CAACd,UAAU,EAAC2C,OAAO,CAAEX,gBAAiB,CAACM,EAAE,CAAE,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,cACnDvB,IAAA,CAACV,aAAa,GAAE,CAAC,CACP,CAAC,cACbU,IAAA,CAACjB,UAAU,EAACgD,OAAO,CAAC,IAAI,CAAAR,QAAA,CAAC,4BAEzB,CAAY,CAAC,cACbvB,IAAA,CAACd,UAAU,EACT2C,OAAO,CAAEA,CAAA,GAAMG,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE,CACxCV,EAAE,CAAE,CAAEW,EAAE,CAAE,CAAE,CAAE,CACdC,KAAK,CAAC,SAAS,CACfC,KAAK,CAAC,oBAAoB,CAAAd,QAAA,cAE1BvB,IAAA,CAACR,WAAW,GAAE,CAAC,CACL,CAAC,EACV,CAAC,cACNQ,IAAA,CAACH,eAAe,GAAE,CAAC,EAChB,CAAC,cAGNG,IAAA,CAACF,gBAAgB,EACfc,UAAU,CAAEA,UAAW,CACvB0B,SAAS,CAAEnB,aAAc,CACzBoB,OAAO,CAAElB,WAAY,CACtB,CAAC,cAEFrB,IAAA,CAACZ,QAAQ,EACPoD,IAAI,CAAE9B,YAAa,CACnB+B,gBAAgB,CAAE,IAAK,CACvBC,OAAO,CAAEpB,mBAAoB,CAC7BqB,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAtB,QAAA,cAE3DvB,IAAA,CAACb,KAAK,EAACuD,OAAO,CAAEpB,mBAAoB,CAACwB,QAAQ,CAAEtC,aAAc,CAACgB,EAAE,CAAE,CAAEuB,KAAK,CAAE,MAAO,CAAE,CAAAxB,QAAA,CACjFjB,YAAY,CACR,CAAC,CACA,CAAC,EACR,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}