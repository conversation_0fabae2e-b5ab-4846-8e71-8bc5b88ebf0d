{"ast": null, "code": "import { addDays } from \"./addDays.js\";\n\n/**\n * The {@link addWeeks} function options.\n */\n\n/**\n * @name addWeeks\n * @category Week Helpers\n * @summary Add the specified number of weeks to the given date.\n *\n * @description\n * Add the specified number of weeks to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of weeks to be added.\n * @param options - An object with options\n *\n * @returns The new date with the weeks added\n *\n * @example\n * // Add 4 weeks to 1 September 2014:\n * const result = addWeeks(new Date(2014, 8, 1), 4)\n * //=> Mon Sep 29 2014 00:00:00\n */\nexport function addWeeks(date, amount, options) {\n  return addDays(date, amount * 7, options);\n}\n\n// Fallback for modularized imports:\nexport default addWeeks;", "map": {"version": 3, "names": ["addDays", "addWeeks", "date", "amount", "options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/addWeeks.js"], "sourcesContent": ["import { addDays } from \"./addDays.js\";\n\n/**\n * The {@link addWeeks} function options.\n */\n\n/**\n * @name addWeeks\n * @category Week Helpers\n * @summary Add the specified number of weeks to the given date.\n *\n * @description\n * Add the specified number of weeks to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of weeks to be added.\n * @param options - An object with options\n *\n * @returns The new date with the weeks added\n *\n * @example\n * // Add 4 weeks to 1 September 2014:\n * const result = addWeeks(new Date(2014, 8, 1), 4)\n * //=> Mon Sep 29 2014 00:00:00\n */\nexport function addWeeks(date, amount, options) {\n  return addDays(date, amount * 7, options);\n}\n\n// Fallback for modularized imports:\nexport default addWeeks;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;;AAEtC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC9C,OAAOJ,OAAO,CAACE,IAAI,EAAEC,MAAM,GAAG,CAAC,EAAEC,OAAO,CAAC;AAC3C;;AAEA;AACA,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}