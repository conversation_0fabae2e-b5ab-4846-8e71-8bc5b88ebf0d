{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M7.1 7.01C5.74 8.32 5 10.1 5 12c0 3.86 3.14 7 7 7 1.87 0 3.63-.73 4.95-2.05C18.27 15.63 19 13.87 19 12V5h-7c-1.84 0-3.58.71-4.9 2.01m6.78.11c.14.14.16.36.04.52l-2.44 3.33 4.05.4c.44.04.63.59.3.89l-5.16 4.63c-.16.15-.41.14-.56-.01-.14-.14-.16-.36-.04-.52l2.44-3.33-4.05-.4c-.44-.04-.63-.59-.3-.89l5.16-4.63c.16-.15.41-.14.56.01\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 3c-4.8 0-9 3.86-9 9 0 2.12.74 4.07 1.97 5.61L3 19.59 4.41 21l1.97-1.97C7.93 20.26 9.88 21 12 21c2.3 0 4.61-.88 6.36-2.64C20.12 16.61 21 14.3 21 12V3zm7 9c0 1.87-.73 3.63-2.05 4.95C15.63 18.27 13.87 19 12 19c-3.86 0-7-3.14-7-7 0-1.9.74-3.68 2.1-4.99C8.42 5.71 10.16 5 12 5h7z\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"m8.46 12.63 4.05.4-2.44 3.33c-.11.16-.1.38.04.52.15.15.4.16.56.01l5.16-4.63c.33-.3.15-.85-.3-.89l-4.05-.4 2.44-3.33c.11-.16.1-.38-.04-.52-.15-.15-.4-.16-.56-.01l-5.16 4.63c-.32.3-.14.85.3.89\"\n}, \"2\")], 'EnergySavingsLeafTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/icons-material/esm/EnergySavingsLeafTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M7.1 7.01C5.74 8.32 5 10.1 5 12c0 3.86 3.14 7 7 7 1.87 0 3.63-.73 4.95-2.05C18.27 15.63 19 13.87 19 12V5h-7c-1.84 0-3.58.71-4.9 2.01m6.78.11c.14.14.16.36.04.52l-2.44 3.33 4.05.4c.44.04.63.59.3.89l-5.16 4.63c-.16.15-.41.14-.56-.01-.14-.14-.16-.36-.04-.52l2.44-3.33-4.05-.4c-.44-.04-.63-.59-.3-.89l5.16-4.63c.16-.15.41-.14.56.01\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 3c-4.8 0-9 3.86-9 9 0 2.12.74 4.07 1.97 5.61L3 19.59 4.41 21l1.97-1.97C7.93 20.26 9.88 21 12 21c2.3 0 4.61-.88 6.36-2.64C20.12 16.61 21 14.3 21 12V3zm7 9c0 1.87-.73 3.63-2.05 4.95C15.63 18.27 13.87 19 12 19c-3.86 0-7-3.14-7-7 0-1.9.74-3.68 2.1-4.99C8.42 5.71 10.16 5 12 5h7z\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"m8.46 12.63 4.05.4-2.44 3.33c-.11.16-.1.38.04.52.15.15.4.16.56.01l5.16-4.63c.33-.3.15-.85-.3-.89l-4.05-.4 2.44-3.33c.11-.16.1-.38-.04-.52-.15-.15-.4-.16-.56-.01l-5.16 4.63c-.32.3-.14.85.3.89\"\n}, \"2\")], 'EnergySavingsLeafTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE,wUAAwU;EAC3UC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}