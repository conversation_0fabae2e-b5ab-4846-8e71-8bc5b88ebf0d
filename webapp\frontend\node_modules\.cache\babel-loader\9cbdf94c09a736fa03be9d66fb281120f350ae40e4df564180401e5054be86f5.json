{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isWithinInterval\n * @category Interval Helpers\n * @summary Is the given date within the interval?\n *\n * @description\n * Is the given date within the interval? (Including start and end.)\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n * @param interval - The interval to check\n *\n * @returns The date is within the interval\n *\n * @example\n * // For the date within the interval:\n * isWithinInterval(new Date(2014, 0, 3), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * //=> true\n *\n * @example\n * // For the date outside of the interval:\n * isWithinInterval(new Date(2014, 0, 10), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * //=> false\n *\n * @example\n * // For date equal to interval start:\n * isWithinInterval(date, { start, end: date })\n * // => true\n *\n * @example\n * // For date equal to interval end:\n * isWithinInterval(date, { start: date, end })\n * // => true\n */\nexport function isWithinInterval(date, interval) {\n  const time = +toDate(date);\n  const [startTime, endTime] = [+toDate(interval.start), +toDate(interval.end)].sort((a, b) => a - b);\n  return time >= startTime && time <= endTime;\n}\n\n// Fallback for modularized imports:\nexport default isWithinInterval;", "map": {"version": 3, "names": ["toDate", "isWithinInterval", "date", "interval", "time", "startTime", "endTime", "start", "end", "sort", "a", "b"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/isWithinInterval.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isWithinInterval\n * @category Interval Helpers\n * @summary Is the given date within the interval?\n *\n * @description\n * Is the given date within the interval? (Including start and end.)\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n * @param interval - The interval to check\n *\n * @returns The date is within the interval\n *\n * @example\n * // For the date within the interval:\n * isWithinInterval(new Date(2014, 0, 3), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * //=> true\n *\n * @example\n * // For the date outside of the interval:\n * isWithinInterval(new Date(2014, 0, 10), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * //=> false\n *\n * @example\n * // For date equal to interval start:\n * isWithinInterval(date, { start, end: date })\n * // => true\n *\n * @example\n * // For date equal to interval end:\n * isWithinInterval(date, { start: date, end })\n * // => true\n */\nexport function isWithinInterval(date, interval) {\n  const time = +toDate(date);\n  const [startTime, endTime] = [\n    +toDate(interval.start),\n    +toDate(interval.end),\n  ].sort((a, b) => a - b);\n\n  return time >= startTime && time <= endTime;\n}\n\n// Fallback for modularized imports:\nexport default isWithinInterval;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAC/C,MAAMC,IAAI,GAAG,CAACJ,MAAM,CAACE,IAAI,CAAC;EAC1B,MAAM,CAACG,SAAS,EAAEC,OAAO,CAAC,GAAG,CAC3B,CAACN,MAAM,CAACG,QAAQ,CAACI,KAAK,CAAC,EACvB,CAACP,MAAM,CAACG,QAAQ,CAACK,GAAG,CAAC,CACtB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;EAEvB,OAAOP,IAAI,IAAIC,SAAS,IAAID,IAAI,IAAIE,OAAO;AAC7C;;AAEA;AACA,eAAeL,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}