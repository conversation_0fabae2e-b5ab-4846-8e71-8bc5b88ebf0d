{"ast": null, "code": "import React,{useState}from'react';import{<PERSON>,<PERSON>po<PERSON>,<PERSON>ton,Text<PERSON>ield,Alert,Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,CircularProgress}from'@mui/material';import{Warning as WarningIcon,Delete as DeleteIcon}from'@mui/icons-material';import adminService from'../../services/adminService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ResetDatabase=()=>{const[confirmText,setConfirmText]=useState('');const[dialogOpen,setDialogOpen]=useState(false);const[loading,setLoading]=useState(false);const[error,setError]=useState('');const[success,setSuccess]=useState('');// Apre il dialog di conferma\nconst handleOpenDialog=()=>{setDialogOpen(true);setConfirmText('');setError('');};// Chiude il dialog di conferma\nconst handleCloseDialog=()=>{setDialogOpen(false);};// Gestisce il cambio del testo di conferma\nconst handleConfirmTextChange=event=>{setConfirmText(event.target.value);};// Gestisce il reset del database\nconst handleResetDatabase=async()=>{if(confirmText!=='RESET'){setError('Per confermare, digita esattamente \"RESET\"');return;}setLoading(true);try{await adminService.resetDatabase();setSuccess('Database resettato con successo. Il sistema verrà riavviato.');// Chiudi il dialog\nsetDialogOpen(false);// Logout e reindirizza alla pagina di login dopo 3 secondi\nsetTimeout(()=>{localStorage.removeItem('token');window.location.href='/login';},3000);}catch(err){setError(err.detail||'Errore durante il reset del database');}finally{setLoading(false);}};return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Reset Database\"}),/*#__PURE__*/_jsxs(Alert,{severity:\"warning\",sx:{mb:2,width:'100%'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:\"\\u26A0\\uFE0F ATTENZIONE: Questa operazione \\xE8 irreversibile!\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Tutti i dati verranno eliminati permanentemente.\"})]}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"error\",startIcon:/*#__PURE__*/_jsx(DeleteIcon,{}),onClick:handleOpenDialog,sx:{mt:2},children:\"Reset Database\"})]}),success&&/*#__PURE__*/_jsx(Alert,{severity:\"success\",sx:{mt:2},children:success}),/*#__PURE__*/_jsxs(Dialog,{open:dialogOpen,onClose:handleCloseDialog,children:[/*#__PURE__*/_jsx(DialogTitle,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(WarningIcon,{color:\"error\",sx:{mr:1}}),\"Conferma Reset Database\"]})}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsxs(DialogContentText,{children:[\"\\u26A0\\uFE0F ATTENZIONE: Questa operazione \\xE8 irreversibile!\",/*#__PURE__*/_jsx(\"br\",{}),\"Tutti i dati verranno eliminati permanentemente.\",/*#__PURE__*/_jsx(\"br\",{}),/*#__PURE__*/_jsx(\"br\",{}),\"Per confermare, digita \\\"RESET\\\" nel campo sottostante.\"]}),/*#__PURE__*/_jsx(TextField,{autoFocus:true,margin:\"dense\",id:\"confirm\",label:\"Digita RESET per confermare\",type:\"text\",fullWidth:true,variant:\"outlined\",value:confirmText,onChange:handleConfirmTextChange,error:!!error,helperText:error})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,disabled:loading,children:\"Annulla\"}),/*#__PURE__*/_jsx(Button,{onClick:handleResetDatabase,color:\"error\",disabled:confirmText!=='RESET'||loading,children:loading?/*#__PURE__*/_jsx(CircularProgress,{size:24}):'Conferma Reset'})]})]})]});};export default ResetDatabase;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "TextField", "<PERSON><PERSON>", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "CircularProgress", "Warning", "WarningIcon", "Delete", "DeleteIcon", "adminService", "jsx", "_jsx", "jsxs", "_jsxs", "ResetDatabase", "confirmText", "setConfirmText", "dialogOpen", "setDialogOpen", "loading", "setLoading", "error", "setError", "success", "setSuccess", "handleOpenDialog", "handleCloseDialog", "handleConfirmTextChange", "event", "target", "value", "handleResetDatabase", "resetDatabase", "setTimeout", "localStorage", "removeItem", "window", "location", "href", "err", "detail", "children", "sx", "display", "flexDirection", "alignItems", "mb", "variant", "gutterBottom", "severity", "width", "color", "startIcon", "onClick", "mt", "open", "onClose", "mr", "autoFocus", "margin", "id", "label", "type", "fullWidth", "onChange", "helperText", "disabled", "size"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/admin/ResetDatabase.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>ton,\n  TextField,\n  Alert,\n  Dialog,\n  DialogActions,\n  DialogContent,\n  DialogContentText,\n  DialogTitle,\n  CircularProgress\n} from '@mui/material';\nimport {\n  Warning as WarningIcon,\n  Delete as DeleteIcon\n} from '@mui/icons-material';\nimport adminService from '../../services/adminService';\n\nconst ResetDatabase = () => {\n  const [confirmText, setConfirmText] = useState('');\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Apre il dialog di conferma\n  const handleOpenDialog = () => {\n    setDialogOpen(true);\n    setConfirmText('');\n    setError('');\n  };\n\n  // Chiude il dialog di conferma\n  const handleCloseDialog = () => {\n    setDialogOpen(false);\n  };\n\n  // Gestisce il cambio del testo di conferma\n  const handleConfirmTextChange = (event) => {\n    setConfirmText(event.target.value);\n  };\n\n  // Gestisce il reset del database\n  const handleResetDatabase = async () => {\n    if (confirmText !== 'RESET') {\n      setError('Per confermare, digita esattamente \"RESET\"');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      await adminService.resetDatabase();\n      setSuccess('Database resettato con successo. Il sistema verrà riavviato.');\n      \n      // Chiudi il dialog\n      setDialogOpen(false);\n      \n      // Logout e reindirizza alla pagina di login dopo 3 secondi\n      setTimeout(() => {\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n      }, 3000);\n    } catch (err) {\n      setError(err.detail || 'Errore durante il reset del database');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Box>\n      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 2 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Reset Database\n        </Typography>\n        \n        <Alert severity=\"warning\" sx={{ mb: 2, width: '100%' }}>\n          <Typography variant=\"body1\">\n            ⚠️ ATTENZIONE: Questa operazione è irreversibile!\n          </Typography>\n          <Typography variant=\"body2\">\n            Tutti i dati verranno eliminati permanentemente.\n          </Typography>\n        </Alert>\n        \n        <Button\n          variant=\"contained\"\n          color=\"error\"\n          startIcon={<DeleteIcon />}\n          onClick={handleOpenDialog}\n          sx={{ mt: 2 }}\n        >\n          Reset Database\n        </Button>\n      </Box>\n      \n      {success && (\n        <Alert severity=\"success\" sx={{ mt: 2 }}>\n          {success}\n        </Alert>\n      )}\n      \n      {/* Dialog di conferma */}\n      <Dialog open={dialogOpen} onClose={handleCloseDialog}>\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <WarningIcon color=\"error\" sx={{ mr: 1 }} />\n            Conferma Reset Database\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            ⚠️ ATTENZIONE: Questa operazione è irreversibile!\n            <br />\n            Tutti i dati verranno eliminati permanentemente.\n            <br /><br />\n            Per confermare, digita \"RESET\" nel campo sottostante.\n          </DialogContentText>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"confirm\"\n            label=\"Digita RESET per confermare\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={confirmText}\n            onChange={handleConfirmTextChange}\n            error={!!error}\n            helperText={error}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog} disabled={loading}>\n            Annulla\n          </Button>\n          <Button \n            onClick={handleResetDatabase} \n            color=\"error\" \n            disabled={confirmText !== 'RESET' || loading}\n          >\n            {loading ? <CircularProgress size={24} /> : 'Conferma Reset'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ResetDatabase;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,GAAG,CACHC,UAAU,CACVC,MAAM,CACNC,SAAS,CACTC,KAAK,CACLC,MAAM,CACNC,aAAa,CACbC,aAAa,CACbC,iBAAiB,CACjBC,WAAW,CACXC,gBAAgB,KACX,eAAe,CACtB,OACEC,OAAO,GAAI,CAAAC,WAAW,CACtBC,MAAM,GAAI,CAAAC,UAAU,KACf,qBAAqB,CAC5B,MAAO,CAAAC,YAAY,KAAM,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvD,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGvB,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACwB,UAAU,CAAEC,aAAa,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC0B,OAAO,CAAEC,UAAU,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC4B,KAAK,CAAEC,QAAQ,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC8B,OAAO,CAAEC,UAAU,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CAE1C;AACA,KAAM,CAAAgC,gBAAgB,CAAGA,CAAA,GAAM,CAC7BP,aAAa,CAAC,IAAI,CAAC,CACnBF,cAAc,CAAC,EAAE,CAAC,CAClBM,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAED;AACA,KAAM,CAAAI,iBAAiB,CAAGA,CAAA,GAAM,CAC9BR,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAS,uBAAuB,CAAIC,KAAK,EAAK,CACzCZ,cAAc,CAACY,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CACpC,CAAC,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAIhB,WAAW,GAAK,OAAO,CAAE,CAC3BO,QAAQ,CAAC,4CAA4C,CAAC,CACtD,OACF,CAEAF,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAX,YAAY,CAACuB,aAAa,CAAC,CAAC,CAClCR,UAAU,CAAC,8DAA8D,CAAC,CAE1E;AACAN,aAAa,CAAC,KAAK,CAAC,CAEpB;AACAe,UAAU,CAAC,IAAM,CACfC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC,CAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,QAAQ,CACjC,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAOC,GAAG,CAAE,CACZjB,QAAQ,CAACiB,GAAG,CAACC,MAAM,EAAI,sCAAsC,CAAC,CAChE,CAAC,OAAS,CACRpB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACEP,KAAA,CAACnB,GAAG,EAAA+C,QAAA,eACF5B,KAAA,CAACnB,GAAG,EAACgD,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eACjF9B,IAAA,CAAChB,UAAU,EAACoD,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAP,QAAA,CAAC,gBAEtC,CAAY,CAAC,cAEb5B,KAAA,CAACf,KAAK,EAACmD,QAAQ,CAAC,SAAS,CAACP,EAAE,CAAE,CAAEI,EAAE,CAAE,CAAC,CAAEI,KAAK,CAAE,MAAO,CAAE,CAAAT,QAAA,eACrD9B,IAAA,CAAChB,UAAU,EAACoD,OAAO,CAAC,OAAO,CAAAN,QAAA,CAAC,gEAE5B,CAAY,CAAC,cACb9B,IAAA,CAAChB,UAAU,EAACoD,OAAO,CAAC,OAAO,CAAAN,QAAA,CAAC,kDAE5B,CAAY,CAAC,EACR,CAAC,cAER9B,IAAA,CAACf,MAAM,EACLmD,OAAO,CAAC,WAAW,CACnBI,KAAK,CAAC,OAAO,CACbC,SAAS,cAAEzC,IAAA,CAACH,UAAU,GAAE,CAAE,CAC1B6C,OAAO,CAAE5B,gBAAiB,CAC1BiB,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,CACf,gBAED,CAAQ,CAAC,EACN,CAAC,CAELlB,OAAO,eACNZ,IAAA,CAACb,KAAK,EAACmD,QAAQ,CAAC,SAAS,CAACP,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,CACrClB,OAAO,CACH,CACR,cAGDV,KAAA,CAACd,MAAM,EAACwD,IAAI,CAAEtC,UAAW,CAACuC,OAAO,CAAE9B,iBAAkB,CAAAe,QAAA,eACnD9B,IAAA,CAACR,WAAW,EAAAsC,QAAA,cACV5B,KAAA,CAACnB,GAAG,EAACgD,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAJ,QAAA,eACjD9B,IAAA,CAACL,WAAW,EAAC6C,KAAK,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,0BAE9C,EAAK,CAAC,CACK,CAAC,cACd5C,KAAA,CAACZ,aAAa,EAAAwC,QAAA,eACZ5B,KAAA,CAACX,iBAAiB,EAAAuC,QAAA,EAAC,gEAEjB,cAAA9B,IAAA,QAAK,CAAC,mDAEN,cAAAA,IAAA,QAAK,CAAC,cAAAA,IAAA,QAAK,CAAC,0DAEd,EAAmB,CAAC,cACpBA,IAAA,CAACd,SAAS,EACR6D,SAAS,MACTC,MAAM,CAAC,OAAO,CACdC,EAAE,CAAC,SAAS,CACZC,KAAK,CAAC,6BAA6B,CACnCC,IAAI,CAAC,MAAM,CACXC,SAAS,MACThB,OAAO,CAAC,UAAU,CAClBjB,KAAK,CAAEf,WAAY,CACnBiD,QAAQ,CAAErC,uBAAwB,CAClCN,KAAK,CAAE,CAAC,CAACA,KAAM,CACf4C,UAAU,CAAE5C,KAAM,CACnB,CAAC,EACW,CAAC,cAChBR,KAAA,CAACb,aAAa,EAAAyC,QAAA,eACZ9B,IAAA,CAACf,MAAM,EAACyD,OAAO,CAAE3B,iBAAkB,CAACwC,QAAQ,CAAE/C,OAAQ,CAAAsB,QAAA,CAAC,SAEvD,CAAQ,CAAC,cACT9B,IAAA,CAACf,MAAM,EACLyD,OAAO,CAAEtB,mBAAoB,CAC7BoB,KAAK,CAAC,OAAO,CACbe,QAAQ,CAAEnD,WAAW,GAAK,OAAO,EAAII,OAAQ,CAAAsB,QAAA,CAE5CtB,OAAO,cAAGR,IAAA,CAACP,gBAAgB,EAAC+D,IAAI,CAAE,EAAG,CAAE,CAAC,CAAG,gBAAgB,CACtD,CAAC,EACI,CAAC,EACV,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAArD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}