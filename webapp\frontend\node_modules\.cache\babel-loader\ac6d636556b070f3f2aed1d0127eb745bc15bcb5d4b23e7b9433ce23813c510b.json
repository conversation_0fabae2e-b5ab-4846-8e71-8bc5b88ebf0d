{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\parco\\\\StoricoUtilizzoPage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, Typography, Paper, Button, IconButton, Alert, Snackbar } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport ParcoCavi from '../../../components/cavi/ParcoCavi';\nimport { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StoricoUtilizzoPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    selectedCantiere\n  } = useAuth();\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [openSuccess, setOpenSuccess] = useState(false);\n  const [openError, setOpenError] = useState(false);\n\n  // Recupera l'ID del cantiere dal localStorage se non è disponibile nel contesto\n  const cantiereId = selectedCantiere ? selectedCantiere.id_cantiere : parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = selectedCantiere ? selectedCantiere.nome : localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Verifica se un cantiere è selezionato\n  if (!cantiereId || isNaN(cantiereId)) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"Nessun cantiere selezionato. Seleziona un cantiere per gestire il parco cavi.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: () => navigate('/dashboard/cantieri'),\n        sx: {\n          mt: 2\n        },\n        children: \"Vai alla lista cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Gestisce il ritorno alla pagina del parco cavi\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cavi/parco');\n  };\n\n  // Gestisce i messaggi di successo\n  const handleSuccess = message => {\n    setSuccessMessage(message);\n    setOpenSuccess(true);\n  };\n\n  // Gestisce i messaggi di errore\n  const handleError = message => {\n    setErrorMessage(message);\n    setOpenError(true);\n  };\n\n  // Chiude i messaggi\n  const handleCloseSuccess = () => {\n    setOpenSuccess(false);\n  };\n  const handleCloseError = () => {\n    setOpenError(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToCantieri,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          children: \"Storico Utilizzo Bobine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), cantiereId && /*#__PURE__*/_jsxDEV(ParcoCavi, {\n      cantiereId: cantiereId,\n      onSuccess: handleSuccess,\n      onError: handleError,\n      initialOption: \"visualizzaStorico\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openSuccess,\n      autoHideDuration: 6000,\n      onClose: handleCloseSuccess,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSuccess,\n        severity: \"success\",\n        sx: {\n          width: '100%'\n        },\n        children: successMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openError,\n      autoHideDuration: 6000,\n      onClose: handleCloseError,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseError,\n        severity: \"error\",\n        sx: {\n          width: '100%'\n        },\n        children: errorMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n_s(StoricoUtilizzoPage, \"alPhJDWny/l2XY6014qd2C0Vy3U=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = StoricoUtilizzoPage;\nexport default StoricoUtilizzoPage;\nvar _c;\n$RefreshReg$(_c, \"StoricoUtilizzoPage\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "Snackbar", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "useNavigate", "useAuth", "AdminHomeButton", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useState", "jsxDEV", "_jsxDEV", "StoricoUtilizzoPage", "_s", "navigate", "selected<PERSON><PERSON><PERSON>", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "openSuccess", "setOpenSuccess", "openError", "set<PERSON>pen<PERSON>rror", "cantiereId", "id_cantiere", "parseInt", "localStorage", "getItem", "cantiereName", "nome", "isNaN", "sx", "p", "children", "severity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "color", "onClick", "mt", "handleBackToCantieri", "handleSuccess", "message", "handleError", "handleCloseSuccess", "handleCloseError", "mb", "display", "alignItems", "justifyContent", "mr", "window", "location", "reload", "ml", "title", "onSuccess", "onError", "initialOption", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/parco/StoricoUtilizzoPage.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert,\n  Snackbar\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport ParcoCavi from '../../../components/cavi/ParcoCavi';\nimport { useState } from 'react';\n\nconst StoricoUtilizzoPage = () => {\n  const navigate = useNavigate();\n  const { selectedCantiere } = useAuth();\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [openSuccess, setOpenSuccess] = useState(false);\n  const [openError, setOpenError] = useState(false);\n\n  // Recupera l'ID del cantiere dal localStorage se non è disponibile nel contesto\n  const cantiereId = selectedCantiere ? selectedCantiere.id_cantiere : parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = selectedCantiere ? selectedCantiere.nome : localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Verifica se un cantiere è selezionato\n  if (!cantiereId || isNaN(cantiereId)) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Alert severity=\"warning\">\n          Nessun cantiere selezionato. Seleziona un cantiere per gestire il parco cavi.\n        </Alert>\n        <Button\n          variant=\"contained\"\n          color=\"primary\"\n          onClick={() => navigate('/dashboard/cantieri')}\n          sx={{ mt: 2 }}\n        >\n          Vai alla lista cantieri\n        </Button>\n      </Box>\n    );\n  }\n\n  // Gestisce il ritorno alla pagina del parco cavi\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cavi/parco');\n  };\n\n  // Gestisce i messaggi di successo\n  const handleSuccess = (message) => {\n    setSuccessMessage(message);\n    setOpenSuccess(true);\n  };\n\n  // Gestisce i messaggi di errore\n  const handleError = (message) => {\n    setErrorMessage(message);\n    setOpenError(true);\n  };\n\n  // Chiude i messaggi\n  const handleCloseSuccess = () => {\n    setOpenSuccess(false);\n  };\n\n  const handleCloseError = () => {\n    setOpenError(false);\n  };\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Storico Utilizzo Bobine\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {cantiereId && (\n        <ParcoCavi\n          cantiereId={cantiereId}\n          onSuccess={handleSuccess}\n          onError={handleError}\n          initialOption=\"visualizzaStorico\"\n        />\n      )}\n\n      {/* Snackbar per i messaggi di successo */}\n      <Snackbar\n        open={openSuccess}\n        autoHideDuration={6000}\n        onClose={handleCloseSuccess}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSuccess} severity=\"success\" sx={{ width: '100%' }}>\n          {successMessage}\n        </Alert>\n      </Snackbar>\n\n      {/* Snackbar per i messaggi di errore */}\n      <Snackbar\n        open={openError}\n        autoHideDuration={6000}\n        onClose={handleCloseError}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseError} severity=\"error\" sx={{ width: '100%' }}>\n          {errorMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default StoricoUtilizzoPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,QAAQ,QACH,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,SAASC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU;EAAiB,CAAC,GAAGT,OAAO,CAAC,CAAC;EACtC,MAAM,CAACU,cAAc,EAAEC,iBAAiB,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAMe,UAAU,GAAGT,gBAAgB,GAAGA,gBAAgB,CAACU,WAAW,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;EAC7H,MAAMC,YAAY,GAAGd,gBAAgB,GAAGA,gBAAgB,CAACe,IAAI,GAAGH,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAAI,YAAYJ,UAAU,EAAE;;EAExI;EACA,IAAI,CAACA,UAAU,IAAIO,KAAK,CAACP,UAAU,CAAC,EAAE;IACpC,oBACEb,OAAA,CAACjB,GAAG;MAACsC,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAChBvB,OAAA,CAACZ,KAAK;QAACoC,QAAQ,EAAC,SAAS;QAAAD,QAAA,EAAC;MAE1B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR5B,OAAA,CAACd,MAAM;QACL2C,OAAO,EAAC,WAAW;QACnBC,KAAK,EAAC,SAAS;QACfC,OAAO,EAAEA,CAAA,KAAM5B,QAAQ,CAAC,qBAAqB,CAAE;QAC/CkB,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,EACf;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;;EAEA;EACA,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IACjC9B,QAAQ,CAAC,uBAAuB,CAAC;EACnC,CAAC;;EAED;EACA,MAAM+B,aAAa,GAAIC,OAAO,IAAK;IACjC7B,iBAAiB,CAAC6B,OAAO,CAAC;IAC1BzB,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAM0B,WAAW,GAAID,OAAO,IAAK;IAC/B3B,eAAe,CAAC2B,OAAO,CAAC;IACxBvB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMyB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B3B,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAM4B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B1B,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,oBACEZ,OAAA,CAACjB,GAAG;IAAAwC,QAAA,gBACFvB,OAAA,CAACjB,GAAG;MAACsC,EAAE,EAAE;QAAEkB,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAnB,QAAA,gBACzFvB,OAAA,CAACjB,GAAG;QAACsC,EAAE,EAAE;UAAEmB,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAlB,QAAA,gBACjDvB,OAAA,CAACb,UAAU;UAAC4C,OAAO,EAAEE,oBAAqB;UAACZ,EAAE,EAAE;YAAEsB,EAAE,EAAE;UAAE,CAAE;UAAApB,QAAA,eACvDvB,OAAA,CAACT,aAAa;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACb5B,OAAA,CAAChB,UAAU;UAAC6C,OAAO,EAAC,IAAI;UAAAN,QAAA,EAAC;QAEzB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5B,OAAA,CAACb,UAAU;UACT4C,OAAO,EAAEA,CAAA,KAAMa,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCzB,EAAE,EAAE;YAAE0B,EAAE,EAAE;UAAE,CAAE;UACdjB,KAAK,EAAC,SAAS;UACfkB,KAAK,EAAC,oBAAoB;UAAAzB,QAAA,eAE1BvB,OAAA,CAACP,WAAW;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN5B,OAAA,CAACJ,eAAe;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAELf,UAAU,iBACTb,OAAA,CAACH,SAAS;MACRgB,UAAU,EAAEA,UAAW;MACvBoC,SAAS,EAAEf,aAAc;MACzBgB,OAAO,EAAEd,WAAY;MACrBe,aAAa,EAAC;IAAmB;MAAA1B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CACF,eAGD5B,OAAA,CAACX,QAAQ;MACP+D,IAAI,EAAE3C,WAAY;MAClB4C,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEjB,kBAAmB;MAC5BkB,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAlC,QAAA,eAE3DvB,OAAA,CAACZ,KAAK;QAACkE,OAAO,EAAEjB,kBAAmB;QAACb,QAAQ,EAAC,SAAS;QAACH,EAAE,EAAE;UAAEqC,KAAK,EAAE;QAAO,CAAE;QAAAnC,QAAA,EAC1ElB;MAAc;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGX5B,OAAA,CAACX,QAAQ;MACP+D,IAAI,EAAEzC,SAAU;MAChB0C,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEhB,gBAAiB;MAC1BiB,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAlC,QAAA,eAE3DvB,OAAA,CAACZ,KAAK;QAACkE,OAAO,EAAEhB,gBAAiB;QAACd,QAAQ,EAAC,OAAO;QAACH,EAAE,EAAE;UAAEqC,KAAK,EAAE;QAAO,CAAE;QAAAnC,QAAA,EACtEhB;MAAY;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC1B,EAAA,CAjHID,mBAAmB;EAAA,QACNP,WAAW,EACCC,OAAO;AAAA;AAAAgE,EAAA,GAFhC1D,mBAAmB;AAmHzB,eAAeA,mBAAmB;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}