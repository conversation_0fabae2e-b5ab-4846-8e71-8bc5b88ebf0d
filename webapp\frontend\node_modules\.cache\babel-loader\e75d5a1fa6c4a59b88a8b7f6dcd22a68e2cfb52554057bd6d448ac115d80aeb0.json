{"ast": null, "code": "import useMediaQuery from '@mui/material/useMediaQuery';\nconst PREFERS_REDUCED_MOTION = '@media (prefers-reduced-motion: reduce)';\n\n// detect if user agent has Android version < 10 or iOS version < 13\nconst mobileVersionMatches = typeof navigator !== 'undefined' && navigator.userAgent.match(/android\\s(\\d+)|OS\\s(\\d+)/i);\nconst androidVersion = mobileVersionMatches && mobileVersionMatches[1] ? parseInt(mobileVersionMatches[1], 10) : null;\nconst iOSVersion = mobileVersionMatches && mobileVersionMatches[2] ? parseInt(mobileVersionMatches[2], 10) : null;\nexport const slowAnimationDevices = androidVersion && androidVersion < 10 || iOSVersion && iOSVersion < 13 || false;\nexport function useReduceAnimations(customReduceAnimations) {\n  const prefersReduced = useMediaQuery(PREFERS_REDUCED_MOTION, {\n    defaultMatches: false\n  });\n  if (customReduceAnimations != null) {\n    return customReduceAnimations;\n  }\n  return prefersReduced || slowAnimationDevices;\n}", "map": {"version": 3, "names": ["useMediaQuery", "PREFERS_REDUCED_MOTION", "mobileVersionMatches", "navigator", "userAgent", "match", "androidVersion", "parseInt", "iOSVersion", "slowAnimationDevices", "useReduceAnimations", "customReduceAnimations", "prefersReduced", "defaultMatches"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/hooks/useReduceAnimations.js"], "sourcesContent": ["import useMediaQuery from '@mui/material/useMediaQuery';\nconst PREFERS_REDUCED_MOTION = '@media (prefers-reduced-motion: reduce)';\n\n// detect if user agent has Android version < 10 or iOS version < 13\nconst mobileVersionMatches = typeof navigator !== 'undefined' && navigator.userAgent.match(/android\\s(\\d+)|OS\\s(\\d+)/i);\nconst androidVersion = mobileVersionMatches && mobileVersionMatches[1] ? parseInt(mobileVersionMatches[1], 10) : null;\nconst iOSVersion = mobileVersionMatches && mobileVersionMatches[2] ? parseInt(mobileVersionMatches[2], 10) : null;\nexport const slowAnimationDevices = androidVersion && androidVersion < 10 || iOSVersion && iOSVersion < 13 || false;\nexport function useReduceAnimations(customReduceAnimations) {\n  const prefersReduced = useMediaQuery(PREFERS_REDUCED_MOTION, {\n    defaultMatches: false\n  });\n  if (customReduceAnimations != null) {\n    return customReduceAnimations;\n  }\n  return prefersReduced || slowAnimationDevices;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,6BAA6B;AACvD,MAAMC,sBAAsB,GAAG,yCAAyC;;AAExE;AACA,MAAMC,oBAAoB,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,CAACC,KAAK,CAAC,2BAA2B,CAAC;AACvH,MAAMC,cAAc,GAAGJ,oBAAoB,IAAIA,oBAAoB,CAAC,CAAC,CAAC,GAAGK,QAAQ,CAACL,oBAAoB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;AACrH,MAAMM,UAAU,GAAGN,oBAAoB,IAAIA,oBAAoB,CAAC,CAAC,CAAC,GAAGK,QAAQ,CAACL,oBAAoB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;AACjH,OAAO,MAAMO,oBAAoB,GAAGH,cAAc,IAAIA,cAAc,GAAG,EAAE,IAAIE,UAAU,IAAIA,UAAU,GAAG,EAAE,IAAI,KAAK;AACnH,OAAO,SAASE,mBAAmBA,CAACC,sBAAsB,EAAE;EAC1D,MAAMC,cAAc,GAAGZ,aAAa,CAACC,sBAAsB,EAAE;IAC3DY,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,IAAIF,sBAAsB,IAAI,IAAI,EAAE;IAClC,OAAOA,sBAAsB;EAC/B;EACA,OAAOC,cAAc,IAAIH,oBAAoB;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}