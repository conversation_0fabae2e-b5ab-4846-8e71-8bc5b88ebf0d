{"ast": null, "code": "import { formatDistance } from \"./id/_lib/formatDistance.js\";\nimport { formatLong } from \"./id/_lib/formatLong.js\";\nimport { formatRelative } from \"./id/_lib/formatRelative.js\";\nimport { localize } from \"./id/_lib/localize.js\";\nimport { match } from \"./id/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Indonesian locale.\n * @language Indonesian\n * @iso-639-2 ind\n * <AUTHOR> [@rbudiharso](https://github.com/rbudiharso)\n * <AUTHOR> Nata [@bentinata](https://github.com/bentinata)\n * <AUTHOR> [@deerawan](https://github.com/deerawan)\n * <AUTHOR> Ajitiono [@imballinst](https://github.com/imballinst)\n */\nexport const id = {\n  code: \"id\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default id;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "id", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/id.js"], "sourcesContent": ["import { formatDistance } from \"./id/_lib/formatDistance.js\";\nimport { formatLong } from \"./id/_lib/formatLong.js\";\nimport { formatRelative } from \"./id/_lib/formatRelative.js\";\nimport { localize } from \"./id/_lib/localize.js\";\nimport { match } from \"./id/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Indonesian locale.\n * @language Indonesian\n * @iso-639-2 ind\n * <AUTHOR> [@rbudiharso](https://github.com/rbudiharso)\n * <AUTHOR> Nata [@bentinata](https://github.com/bentinata)\n * <AUTHOR> [@deerawan](https://github.com/deerawan)\n * <AUTHOR> Ajitiono [@imballinst](https://github.com/imballinst)\n */\nexport const id = {\n  code: \"id\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default id;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}