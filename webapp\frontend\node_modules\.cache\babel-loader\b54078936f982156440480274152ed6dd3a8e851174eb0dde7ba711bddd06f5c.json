{"ast": null, "code": "import React from'react';import{<PERSON><PERSON><PERSON>,<PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>}from'recharts';import{Box,Typography,Grid,Paper,Chip}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const COLORS={DISPONIBILE:'#2e7d32',IN_USO:'#ed6c02',TERMINATA:'#d32f2f',OVER:'#9c27b0'};const STATUS_COLORS={primary:'#1976d2',secondary:'#dc004e',success:'#2e7d32',warning:'#ed6c02',info:'#0288d1',error:'#d32f2f'};const Bobine<PERSON>hart=_ref=>{let{data}=_ref;if(!data||!data.bobine)return null;// Raggruppa bobine per stato\nconst bobinePerStato=data.bobine.reduce((acc,bobina)=>{const stato=bobina.stato||'SCONOSCIUTO';if(!acc[stato]){acc[stato]={count:0,metri_totali:0,metri_residui:0};}acc[stato].count++;acc[stato].metri_totali+=bobina.metri_totali||0;acc[stato].metri_residui+=bobina.metri_residui||0;return acc;},{});const statoData=Object.entries(bobinePerStato).map(_ref2=>{let[stato,info]=_ref2;return{stato,count:info.count,metri_totali:info.metri_totali,metri_residui:info.metri_residui,metri_utilizzati:info.metri_totali-info.metri_residui,percentuale_utilizzo:info.metri_totali>0?(info.metri_totali-info.metri_residui)/info.metri_totali*100:0};});// Raggruppa bobine per tipologia\nconst bobinePerTipologia=data.bobine.reduce((acc,bobina)=>{const tipologia=bobina.tipologia||'SCONOSCIUTA';if(!acc[tipologia]){acc[tipologia]={count:0,metri_totali:0,metri_residui:0};}acc[tipologia].count++;acc[tipologia].metri_totali+=bobina.metri_totali||0;acc[tipologia].metri_residui+=bobina.metri_residui||0;return acc;},{});const tipologiaData=Object.entries(bobinePerTipologia).map(_ref3=>{let[tipologia,info]=_ref3;return{tipologia:tipologia.length>10?tipologia.substring(0,10)+'...':tipologia,tipologia_full:tipologia,count:info.count,metri_totali:info.metri_totali,metri_residui:info.metri_residui,metri_utilizzati:info.metri_totali-info.metri_residui,percentuale_utilizzo:info.metri_totali>0?(info.metri_totali-info.metri_residui)/info.metri_totali*100:0};});// Dati per scatter plot efficienza\nconst efficienzaData=data.bobine.map(bobina=>({id:bobina.id_bobina,metri_totali:bobina.metri_totali||0,percentuale_utilizzo:bobina.percentuale_utilizzo||0,stato:bobina.stato,tipologia:bobina.tipologia}));const CustomTooltip=_ref4=>{let{active,payload,label}=_ref4;if(active&&payload&&payload.length){return/*#__PURE__*/_jsxs(Paper,{sx:{p:1,border:'1px solid #ccc'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:`${label}`}),payload.map((entry,index)=>/*#__PURE__*/_jsx(Typography,{variant:\"body2\",style:{color:entry.color},children:`${entry.name}: ${entry.value}`},index))]});}return null;};const ScatterTooltip=_ref5=>{let{active,payload}=_ref5;if(active&&payload&&payload.length){const data=payload[0].payload;return/*#__PURE__*/_jsxs(Paper,{sx:{p:1,border:'1px solid #ccc'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:`ID: ${data.id}`}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:`Tipologia: ${data.tipologia}`}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:`Metri Totali: ${data.metri_totali}m`}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:`Utilizzo: ${data.percentuale_utilizzo.toFixed(1)}%`}),/*#__PURE__*/_jsx(Chip,{label:data.stato,size:\"small\"})]});}return null;};const renderCustomizedLabel=_ref6=>{let{cx,cy,midAngle,innerRadius,outerRadius,percent}=_ref6;if(percent<0.05)return null;const RADIAN=Math.PI/180;const radius=innerRadius+(outerRadius-innerRadius)*0.5;const x=cx+radius*Math.cos(-midAngle*RADIAN);const y=cy+radius*Math.sin(-midAngle*RADIAN);return/*#__PURE__*/_jsx(\"text\",{x:x,y:y,fill:\"white\",textAnchor:x>cx?'start':'end',dominantBaseline:\"central\",fontSize:\"12\",fontWeight:\"bold\",children:`${(percent*100).toFixed(0)}%`});};return/*#__PURE__*/_jsxs(Box,{sx:{mt:3},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,children:[\"Analisi Grafiche Bobine (\",data.totale_bobine,\" totali)\"]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:350},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,align:\"center\",children:\"Distribuzione per Stato\"}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:280,children:/*#__PURE__*/_jsxs(PieChart,{children:[/*#__PURE__*/_jsx(Pie,{data:statoData,cx:\"50%\",cy:\"50%\",labelLine:false,label:renderCustomizedLabel,outerRadius:80,fill:\"#8884d8\",dataKey:\"count\",children:statoData.map((entry,index)=>/*#__PURE__*/_jsx(Cell,{fill:COLORS[entry.stato]||STATUS_COLORS.info},`cell-${index}`))}),/*#__PURE__*/_jsx(Tooltip,{content:/*#__PURE__*/_jsx(CustomTooltip,{})}),/*#__PURE__*/_jsx(Legend,{})]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:350},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,align:\"center\",children:\"Utilizzo per Tipologia\"}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:280,children:/*#__PURE__*/_jsxs(BarChart,{data:tipologiaData,margin:{top:20,right:30,left:20,bottom:5},children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"tipologia\",angle:-45,textAnchor:\"end\",height:80}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{content:/*#__PURE__*/_jsx(CustomTooltip,{})}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Bar,{dataKey:\"metri_totali\",fill:STATUS_COLORS.primary,name:\"Metri Totali\"}),/*#__PURE__*/_jsx(Bar,{dataKey:\"metri_utilizzati\",fill:STATUS_COLORS.success,name:\"Metri Utilizzati\"}),/*#__PURE__*/_jsx(Bar,{dataKey:\"metri_residui\",fill:STATUS_COLORS.warning,name:\"Metri Residui\"})]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:350},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,align:\"center\",children:\"Numero Bobine per Stato\"}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:280,children:/*#__PURE__*/_jsxs(BarChart,{data:statoData,margin:{top:20,right:30,left:20,bottom:5},children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"stato\"}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{content:/*#__PURE__*/_jsx(CustomTooltip,{})}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Bar,{dataKey:\"count\",fill:STATUS_COLORS.secondary,name:\"Numero Bobine\"})]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:350},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,align:\"center\",children:\"Efficienza Utilizzo (Metri vs Percentuale)\"}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:280,children:/*#__PURE__*/_jsxs(ScatterChart,{margin:{top:20,right:20,bottom:20,left:20},children:[/*#__PURE__*/_jsx(CartesianGrid,{}),/*#__PURE__*/_jsx(XAxis,{type:\"number\",dataKey:\"metri_totali\",name:\"Metri Totali\",label:{value:'Metri Totali',position:'insideBottom',offset:-10}}),/*#__PURE__*/_jsx(YAxis,{type:\"number\",dataKey:\"percentuale_utilizzo\",name:\"Utilizzo %\",label:{value:'Utilizzo %',angle:-90,position:'insideLeft'}}),/*#__PURE__*/_jsx(Tooltip,{content:/*#__PURE__*/_jsx(ScatterTooltip,{})}),/*#__PURE__*/_jsx(Scatter,{name:\"Bobine\",data:efficienzaData,fill:STATUS_COLORS.info})]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Statistiche Riassuntive\"}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:2,children:statoData.map((stato,index)=>/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',p:1,border:'1px solid #e0e0e0',borderRadius:1},children:[/*#__PURE__*/_jsx(Chip,{label:stato.stato,color:stato.stato==='DISPONIBILE'?'success':stato.stato==='TERMINATA'?'error':'warning',sx:{mb:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:stato.count}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"bobine\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[stato.metri_residui.toFixed(0),\"m residui\"]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[stato.percentuale_utilizzo.toFixed(1),\"% utilizzo\"]})]})},index))})]})})]})]});};export default BobineChart;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Box", "Typography", "Grid", "Paper", "Chip", "jsx", "_jsx", "jsxs", "_jsxs", "COLORS", "DISPONIBILE", "IN_USO", "TERMINATA", "OVER", "STATUS_COLORS", "primary", "secondary", "success", "warning", "info", "error", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "data", "bobine", "bobine<PERSON>er<PERSON><PERSON><PERSON>", "reduce", "acc", "bobina", "stato", "count", "metri_totali", "metri_residui", "statoData", "Object", "entries", "map", "_ref2", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "percentuale_utilizzo", "bobinePerTipologia", "tipologia", "tipologiaData", "_ref3", "length", "substring", "tipologia_full", "efficienzaData", "id", "id_bobina", "CustomTooltip", "_ref4", "active", "payload", "label", "sx", "p", "border", "children", "variant", "entry", "index", "style", "color", "name", "value", "ScatterTooltip", "_ref5", "toFixed", "size", "renderCustomizedLabel", "_ref6", "cx", "cy", "midAngle", "innerRadius", "outerRadius", "percent", "RADIAN", "Math", "PI", "radius", "x", "cos", "y", "sin", "fill", "textAnchor", "dominantBaseline", "fontSize", "fontWeight", "mt", "gutterBottom", "totale_bobine", "container", "spacing", "item", "xs", "md", "height", "align", "width", "labelLine", "dataKey", "content", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angle", "type", "position", "offset", "sm", "textAlign", "borderRadius", "mb"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/charts/BobineChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>ianG<PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  ResponsiveC<PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>\n} from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\n\nconst COLORS = {\n  DISPONIBILE: '#2e7d32',\n  IN_USO: '#ed6c02',\n  TERMINATA: '#d32f2f',\n  OVER: '#9c27b0'\n};\n\nconst STATUS_COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f'\n};\n\nconst BobineChart = ({ data }) => {\n  if (!data || !data.bobine) return null;\n\n  // Raggruppa bobine per stato\n  const bobinePerStato = data.bobine.reduce((acc, bobina) => {\n    const stato = bobina.stato || 'SCONOSCIUTO';\n    if (!acc[stato]) {\n      acc[stato] = { count: 0, metri_totali: 0, metri_residui: 0 };\n    }\n    acc[stato].count++;\n    acc[stato].metri_totali += bobina.metri_totali || 0;\n    acc[stato].metri_residui += bobina.metri_residui || 0;\n    return acc;\n  }, {});\n\n  const statoData = Object.entries(bobinePerStato).map(([stato, info]) => ({\n    stato,\n    count: info.count,\n    metri_totali: info.metri_totali,\n    metri_residui: info.metri_residui,\n    metri_utilizzati: info.metri_totali - info.metri_residui,\n    percentuale_utilizzo: info.metri_totali > 0 ? ((info.metri_totali - info.metri_residui) / info.metri_totali * 100) : 0\n  }));\n\n  // Raggruppa bobine per tipologia\n  const bobinePerTipologia = data.bobine.reduce((acc, bobina) => {\n    const tipologia = bobina.tipologia || 'SCONOSCIUTA';\n    if (!acc[tipologia]) {\n      acc[tipologia] = { count: 0, metri_totali: 0, metri_residui: 0 };\n    }\n    acc[tipologia].count++;\n    acc[tipologia].metri_totali += bobina.metri_totali || 0;\n    acc[tipologia].metri_residui += bobina.metri_residui || 0;\n    return acc;\n  }, {});\n\n  const tipologiaData = Object.entries(bobinePerTipologia).map(([tipologia, info]) => ({\n    tipologia: tipologia.length > 10 ? tipologia.substring(0, 10) + '...' : tipologia,\n    tipologia_full: tipologia,\n    count: info.count,\n    metri_totali: info.metri_totali,\n    metri_residui: info.metri_residui,\n    metri_utilizzati: info.metri_totali - info.metri_residui,\n    percentuale_utilizzo: info.metri_totali > 0 ? ((info.metri_totali - info.metri_residui) / info.metri_totali * 100) : 0\n  }));\n\n  // Dati per scatter plot efficienza\n  const efficienzaData = data.bobine.map(bobina => ({\n    id: bobina.id_bobina,\n    metri_totali: bobina.metri_totali || 0,\n    percentuale_utilizzo: bobina.percentuale_utilizzo || 0,\n    stato: bobina.stato,\n    tipologia: bobina.tipologia\n  }));\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${entry.value}`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const ScatterTooltip = ({ active, payload }) => {\n    if (active && payload && payload.length) {\n      const data = payload[0].payload;\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`ID: ${data.id}`}</Typography>\n          <Typography variant=\"body2\">{`Tipologia: ${data.tipologia}`}</Typography>\n          <Typography variant=\"body2\">{`Metri Totali: ${data.metri_totali}m`}</Typography>\n          <Typography variant=\"body2\">{`Utilizzo: ${data.percentuale_utilizzo.toFixed(1)}%`}</Typography>\n          <Chip label={data.stato} size=\"small\" />\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {\n    if (percent < 0.05) return null;\n    \n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n\n    return (\n      <text \n        x={x} \n        y={y} \n        fill=\"white\" \n        textAnchor={x > cx ? 'start' : 'end'} \n        dominantBaseline=\"central\"\n        fontSize=\"12\"\n        fontWeight=\"bold\"\n      >\n        {`${(percent * 100).toFixed(0)}%`}\n      </text>\n    );\n  };\n\n  return (\n    <Box sx={{ mt: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        Analisi Grafiche Bobine ({data.totale_bobine} totali)\n      </Typography>\n      \n      <Grid container spacing={3}>\n        {/* Grafico a torta - Bobine per Stato */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Distribuzione per Stato\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <PieChart>\n                <Pie\n                  data={statoData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={renderCustomizedLabel}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"count\"\n                >\n                  {statoData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={COLORS[entry.stato] || STATUS_COLORS.info} />\n                  ))}\n                </Pie>\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n              </PieChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico a barre - Utilizzo per Tipologia */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Utilizzo per Tipologia\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <BarChart data={tipologiaData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"tipologia\" angle={-45} textAnchor=\"end\" height={80} />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"metri_totali\" fill={STATUS_COLORS.primary} name=\"Metri Totali\" />\n                <Bar dataKey=\"metri_utilizzati\" fill={STATUS_COLORS.success} name=\"Metri Utilizzati\" />\n                <Bar dataKey=\"metri_residui\" fill={STATUS_COLORS.warning} name=\"Metri Residui\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico a barre - Conteggio per Stato */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Numero Bobine per Stato\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <BarChart data={statoData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"stato\" />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"count\" fill={STATUS_COLORS.secondary} name=\"Numero Bobine\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Scatter Plot - Efficienza Bobine */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Efficienza Utilizzo (Metri vs Percentuale)\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>\n                <CartesianGrid />\n                <XAxis \n                  type=\"number\" \n                  dataKey=\"metri_totali\" \n                  name=\"Metri Totali\"\n                  label={{ value: 'Metri Totali', position: 'insideBottom', offset: -10 }}\n                />\n                <YAxis \n                  type=\"number\" \n                  dataKey=\"percentuale_utilizzo\" \n                  name=\"Utilizzo %\"\n                  label={{ value: 'Utilizzo %', angle: -90, position: 'insideLeft' }}\n                />\n                <Tooltip content={<ScatterTooltip />} />\n                <Scatter \n                  name=\"Bobine\" \n                  data={efficienzaData} \n                  fill={STATUS_COLORS.info}\n                />\n              </ScatterChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Statistiche Riassuntive */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Statistiche Riassuntive\n            </Typography>\n            <Grid container spacing={2}>\n              {statoData.map((stato, index) => (\n                <Grid item xs={12} sm={6} md={3} key={index}>\n                  <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                    <Chip \n                      label={stato.stato} \n                      color={stato.stato === 'DISPONIBILE' ? 'success' : stato.stato === 'TERMINATA' ? 'error' : 'warning'}\n                      sx={{ mb: 1 }}\n                    />\n                    <Typography variant=\"h6\">{stato.count}</Typography>\n                    <Typography variant=\"body2\">bobine</Typography>\n                    <Typography variant=\"body2\">\n                      {stato.metri_residui.toFixed(0)}m residui\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      {stato.percentuale_utilizzo.toFixed(1)}% utilizzo\n                    </Typography>\n                  </Box>\n                </Grid>\n              ))}\n            </Grid>\n          </Paper>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default BobineChart;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,QAAQ,CACRC,GAAG,CACHC,KAAK,CACLC,KAAK,CACLC,aAAa,CACbC,OAAO,CACPC,MAAM,CACNC,mBAAmB,CACnBC,QAAQ,CACRC,GAAG,CACHC,IAAI,CACJC,YAAY,CACZC,OAAO,KACF,UAAU,CACjB,OAASC,GAAG,CAAEC,UAAU,CAAEC,IAAI,CAAEC,KAAK,CAAEC,IAAI,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,KAAM,CAAAC,MAAM,CAAG,CACbC,WAAW,CAAE,SAAS,CACtBC,MAAM,CAAE,SAAS,CACjBC,SAAS,CAAE,SAAS,CACpBC,IAAI,CAAE,SACR,CAAC,CAED,KAAM,CAAAC,aAAa,CAAG,CACpBC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,SAAS,CACpBC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SACT,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAc,IAAb,CAAEC,IAAK,CAAC,CAAAD,IAAA,CAC3B,GAAI,CAACC,IAAI,EAAI,CAACA,IAAI,CAACC,MAAM,CAAE,MAAO,KAAI,CAEtC;AACA,KAAM,CAAAC,cAAc,CAAGF,IAAI,CAACC,MAAM,CAACE,MAAM,CAAC,CAACC,GAAG,CAAEC,MAAM,GAAK,CACzD,KAAM,CAAAC,KAAK,CAAGD,MAAM,CAACC,KAAK,EAAI,aAAa,CAC3C,GAAI,CAACF,GAAG,CAACE,KAAK,CAAC,CAAE,CACfF,GAAG,CAACE,KAAK,CAAC,CAAG,CAAEC,KAAK,CAAE,CAAC,CAAEC,YAAY,CAAE,CAAC,CAAEC,aAAa,CAAE,CAAE,CAAC,CAC9D,CACAL,GAAG,CAACE,KAAK,CAAC,CAACC,KAAK,EAAE,CAClBH,GAAG,CAACE,KAAK,CAAC,CAACE,YAAY,EAAIH,MAAM,CAACG,YAAY,EAAI,CAAC,CACnDJ,GAAG,CAACE,KAAK,CAAC,CAACG,aAAa,EAAIJ,MAAM,CAACI,aAAa,EAAI,CAAC,CACrD,MAAO,CAAAL,GAAG,CACZ,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN,KAAM,CAAAM,SAAS,CAAGC,MAAM,CAACC,OAAO,CAACV,cAAc,CAAC,CAACW,GAAG,CAACC,KAAA,MAAC,CAACR,KAAK,CAAEV,IAAI,CAAC,CAAAkB,KAAA,OAAM,CACvER,KAAK,CACLC,KAAK,CAAEX,IAAI,CAACW,KAAK,CACjBC,YAAY,CAAEZ,IAAI,CAACY,YAAY,CAC/BC,aAAa,CAAEb,IAAI,CAACa,aAAa,CACjCM,gBAAgB,CAAEnB,IAAI,CAACY,YAAY,CAAGZ,IAAI,CAACa,aAAa,CACxDO,oBAAoB,CAAEpB,IAAI,CAACY,YAAY,CAAG,CAAC,CAAI,CAACZ,IAAI,CAACY,YAAY,CAAGZ,IAAI,CAACa,aAAa,EAAIb,IAAI,CAACY,YAAY,CAAG,GAAG,CAAI,CACvH,CAAC,EAAC,CAAC,CAEH;AACA,KAAM,CAAAS,kBAAkB,CAAGjB,IAAI,CAACC,MAAM,CAACE,MAAM,CAAC,CAACC,GAAG,CAAEC,MAAM,GAAK,CAC7D,KAAM,CAAAa,SAAS,CAAGb,MAAM,CAACa,SAAS,EAAI,aAAa,CACnD,GAAI,CAACd,GAAG,CAACc,SAAS,CAAC,CAAE,CACnBd,GAAG,CAACc,SAAS,CAAC,CAAG,CAAEX,KAAK,CAAE,CAAC,CAAEC,YAAY,CAAE,CAAC,CAAEC,aAAa,CAAE,CAAE,CAAC,CAClE,CACAL,GAAG,CAACc,SAAS,CAAC,CAACX,KAAK,EAAE,CACtBH,GAAG,CAACc,SAAS,CAAC,CAACV,YAAY,EAAIH,MAAM,CAACG,YAAY,EAAI,CAAC,CACvDJ,GAAG,CAACc,SAAS,CAAC,CAACT,aAAa,EAAIJ,MAAM,CAACI,aAAa,EAAI,CAAC,CACzD,MAAO,CAAAL,GAAG,CACZ,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN,KAAM,CAAAe,aAAa,CAAGR,MAAM,CAACC,OAAO,CAACK,kBAAkB,CAAC,CAACJ,GAAG,CAACO,KAAA,MAAC,CAACF,SAAS,CAAEtB,IAAI,CAAC,CAAAwB,KAAA,OAAM,CACnFF,SAAS,CAAEA,SAAS,CAACG,MAAM,CAAG,EAAE,CAAGH,SAAS,CAACI,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,KAAK,CAAGJ,SAAS,CACjFK,cAAc,CAAEL,SAAS,CACzBX,KAAK,CAAEX,IAAI,CAACW,KAAK,CACjBC,YAAY,CAAEZ,IAAI,CAACY,YAAY,CAC/BC,aAAa,CAAEb,IAAI,CAACa,aAAa,CACjCM,gBAAgB,CAAEnB,IAAI,CAACY,YAAY,CAAGZ,IAAI,CAACa,aAAa,CACxDO,oBAAoB,CAAEpB,IAAI,CAACY,YAAY,CAAG,CAAC,CAAI,CAACZ,IAAI,CAACY,YAAY,CAAGZ,IAAI,CAACa,aAAa,EAAIb,IAAI,CAACY,YAAY,CAAG,GAAG,CAAI,CACvH,CAAC,EAAC,CAAC,CAEH;AACA,KAAM,CAAAgB,cAAc,CAAGxB,IAAI,CAACC,MAAM,CAACY,GAAG,CAACR,MAAM,GAAK,CAChDoB,EAAE,CAAEpB,MAAM,CAACqB,SAAS,CACpBlB,YAAY,CAAEH,MAAM,CAACG,YAAY,EAAI,CAAC,CACtCQ,oBAAoB,CAAEX,MAAM,CAACW,oBAAoB,EAAI,CAAC,CACtDV,KAAK,CAAED,MAAM,CAACC,KAAK,CACnBY,SAAS,CAAEb,MAAM,CAACa,SACpB,CAAC,CAAC,CAAC,CAEH,KAAM,CAAAS,aAAa,CAAGC,KAAA,EAAgC,IAA/B,CAAEC,MAAM,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAAH,KAAA,CAC/C,GAAIC,MAAM,EAAIC,OAAO,EAAIA,OAAO,CAACT,MAAM,CAAE,CACvC,mBACEpC,KAAA,CAACL,KAAK,EAACoD,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,gBAAiB,CAAE,CAAAC,QAAA,eAC5CpD,IAAA,CAACL,UAAU,EAAC0D,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAE,GAAGJ,KAAK,EAAE,CAAa,CAAC,CACpDD,OAAO,CAACjB,GAAG,CAAC,CAACwB,KAAK,CAAEC,KAAK,gBACxBvD,IAAA,CAACL,UAAU,EAAa0D,OAAO,CAAC,OAAO,CAACG,KAAK,CAAE,CAAEC,KAAK,CAAEH,KAAK,CAACG,KAAM,CAAE,CAAAL,QAAA,CACnE,GAAGE,KAAK,CAACI,IAAI,KAAKJ,KAAK,CAACK,KAAK,EAAE,EADjBJ,KAEL,CACb,CAAC,EACG,CAAC,CAEZ,CACA,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAAK,cAAc,CAAGC,KAAA,EAAyB,IAAxB,CAAEf,MAAM,CAAEC,OAAQ,CAAC,CAAAc,KAAA,CACzC,GAAIf,MAAM,EAAIC,OAAO,EAAIA,OAAO,CAACT,MAAM,CAAE,CACvC,KAAM,CAAArB,IAAI,CAAG8B,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO,CAC/B,mBACE7C,KAAA,CAACL,KAAK,EAACoD,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,gBAAiB,CAAE,CAAAC,QAAA,eAC5CpD,IAAA,CAACL,UAAU,EAAC0D,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAE,OAAOnC,IAAI,CAACyB,EAAE,EAAE,CAAa,CAAC,cAC3D1C,IAAA,CAACL,UAAU,EAAC0D,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAE,cAAcnC,IAAI,CAACkB,SAAS,EAAE,CAAa,CAAC,cACzEnC,IAAA,CAACL,UAAU,EAAC0D,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAE,iBAAiBnC,IAAI,CAACQ,YAAY,GAAG,CAAa,CAAC,cAChFzB,IAAA,CAACL,UAAU,EAAC0D,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAE,aAAanC,IAAI,CAACgB,oBAAoB,CAAC6B,OAAO,CAAC,CAAC,CAAC,GAAG,CAAa,CAAC,cAC/F9D,IAAA,CAACF,IAAI,EAACkD,KAAK,CAAE/B,IAAI,CAACM,KAAM,CAACwC,IAAI,CAAC,OAAO,CAAE,CAAC,EACnC,CAAC,CAEZ,CACA,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAAC,qBAAqB,CAAGC,KAAA,EAA6D,IAA5D,CAAEC,EAAE,CAAEC,EAAE,CAAEC,QAAQ,CAAEC,WAAW,CAAEC,WAAW,CAAEC,OAAQ,CAAC,CAAAN,KAAA,CACpF,GAAIM,OAAO,CAAG,IAAI,CAAE,MAAO,KAAI,CAE/B,KAAM,CAAAC,MAAM,CAAGC,IAAI,CAACC,EAAE,CAAG,GAAG,CAC5B,KAAM,CAAAC,MAAM,CAAGN,WAAW,CAAG,CAACC,WAAW,CAAGD,WAAW,EAAI,GAAG,CAC9D,KAAM,CAAAO,CAAC,CAAGV,EAAE,CAAGS,MAAM,CAAGF,IAAI,CAACI,GAAG,CAAC,CAACT,QAAQ,CAAGI,MAAM,CAAC,CACpD,KAAM,CAAAM,CAAC,CAAGX,EAAE,CAAGQ,MAAM,CAAGF,IAAI,CAACM,GAAG,CAAC,CAACX,QAAQ,CAAGI,MAAM,CAAC,CAEpD,mBACExE,IAAA,SACE4E,CAAC,CAAEA,CAAE,CACLE,CAAC,CAAEA,CAAE,CACLE,IAAI,CAAC,OAAO,CACZC,UAAU,CAAEL,CAAC,CAAGV,EAAE,CAAG,OAAO,CAAG,KAAM,CACrCgB,gBAAgB,CAAC,SAAS,CAC1BC,QAAQ,CAAC,IAAI,CACbC,UAAU,CAAC,MAAM,CAAAhC,QAAA,CAEhB,GAAG,CAACmB,OAAO,CAAG,GAAG,EAAET,OAAO,CAAC,CAAC,CAAC,GAAG,CAC7B,CAAC,CAEX,CAAC,CAED,mBACE5D,KAAA,CAACR,GAAG,EAACuD,EAAE,CAAE,CAAEoC,EAAE,CAAE,CAAE,CAAE,CAAAjC,QAAA,eACjBlD,KAAA,CAACP,UAAU,EAAC0D,OAAO,CAAC,IAAI,CAACiC,YAAY,MAAAlC,QAAA,EAAC,2BACX,CAACnC,IAAI,CAACsE,aAAa,CAAC,UAC/C,EAAY,CAAC,cAEbrF,KAAA,CAACN,IAAI,EAAC4F,SAAS,MAACC,OAAO,CAAE,CAAE,CAAArC,QAAA,eAEzBpD,IAAA,CAACJ,IAAI,EAAC8F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAxC,QAAA,cACvBlD,KAAA,CAACL,KAAK,EAACoD,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAE2C,MAAM,CAAE,GAAI,CAAE,CAAAzC,QAAA,eAC/BpD,IAAA,CAACL,UAAU,EAAC0D,OAAO,CAAC,WAAW,CAACiC,YAAY,MAACQ,KAAK,CAAC,QAAQ,CAAA1C,QAAA,CAAC,yBAE5D,CAAY,CAAC,cACbpD,IAAA,CAACZ,mBAAmB,EAAC2G,KAAK,CAAC,MAAM,CAACF,MAAM,CAAE,GAAI,CAAAzC,QAAA,cAC5ClD,KAAA,CAACb,QAAQ,EAAA+D,QAAA,eACPpD,IAAA,CAACV,GAAG,EACF2B,IAAI,CAAEU,SAAU,CAChBuC,EAAE,CAAC,KAAK,CACRC,EAAE,CAAC,KAAK,CACR6B,SAAS,CAAE,KAAM,CACjBhD,KAAK,CAAEgB,qBAAsB,CAC7BM,WAAW,CAAE,EAAG,CAChBU,IAAI,CAAC,SAAS,CACdiB,OAAO,CAAC,OAAO,CAAA7C,QAAA,CAEdzB,SAAS,CAACG,GAAG,CAAC,CAACwB,KAAK,CAAEC,KAAK,gBAC1BvD,IAAA,CAACT,IAAI,EAAuByF,IAAI,CAAE7E,MAAM,CAACmD,KAAK,CAAC/B,KAAK,CAAC,EAAIf,aAAa,CAACK,IAAK,EAAjE,QAAQ0C,KAAK,EAAsD,CAC/E,CAAC,CACC,CAAC,cACNvD,IAAA,CAACd,OAAO,EAACgH,OAAO,cAAElG,IAAA,CAAC4C,aAAa,GAAE,CAAE,CAAE,CAAC,cACvC5C,IAAA,CAACb,MAAM,GAAE,CAAC,EACF,CAAC,CACQ,CAAC,EACjB,CAAC,CACJ,CAAC,cAGPa,IAAA,CAACJ,IAAI,EAAC8F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAxC,QAAA,cACvBlD,KAAA,CAACL,KAAK,EAACoD,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAE2C,MAAM,CAAE,GAAI,CAAE,CAAAzC,QAAA,eAC/BpD,IAAA,CAACL,UAAU,EAAC0D,OAAO,CAAC,WAAW,CAACiC,YAAY,MAACQ,KAAK,CAAC,QAAQ,CAAA1C,QAAA,CAAC,wBAE5D,CAAY,CAAC,cACbpD,IAAA,CAACZ,mBAAmB,EAAC2G,KAAK,CAAC,MAAM,CAACF,MAAM,CAAE,GAAI,CAAAzC,QAAA,cAC5ClD,KAAA,CAACrB,QAAQ,EAACoC,IAAI,CAAEmB,aAAc,CAAC+D,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,MAAM,CAAE,CAAE,CAAE,CAAAnD,QAAA,eACjFpD,IAAA,CAACf,aAAa,EAACuH,eAAe,CAAC,KAAK,CAAE,CAAC,cACvCxG,IAAA,CAACjB,KAAK,EAACkH,OAAO,CAAC,WAAW,CAACQ,KAAK,CAAE,CAAC,EAAG,CAACxB,UAAU,CAAC,KAAK,CAACY,MAAM,CAAE,EAAG,CAAE,CAAC,cACtE7F,IAAA,CAAChB,KAAK,GAAE,CAAC,cACTgB,IAAA,CAACd,OAAO,EAACgH,OAAO,cAAElG,IAAA,CAAC4C,aAAa,GAAE,CAAE,CAAE,CAAC,cACvC5C,IAAA,CAACb,MAAM,GAAE,CAAC,cACVa,IAAA,CAAClB,GAAG,EAACmH,OAAO,CAAC,cAAc,CAACjB,IAAI,CAAExE,aAAa,CAACC,OAAQ,CAACiD,IAAI,CAAC,cAAc,CAAE,CAAC,cAC/E1D,IAAA,CAAClB,GAAG,EAACmH,OAAO,CAAC,kBAAkB,CAACjB,IAAI,CAAExE,aAAa,CAACG,OAAQ,CAAC+C,IAAI,CAAC,kBAAkB,CAAE,CAAC,cACvF1D,IAAA,CAAClB,GAAG,EAACmH,OAAO,CAAC,eAAe,CAACjB,IAAI,CAAExE,aAAa,CAACI,OAAQ,CAAC8C,IAAI,CAAC,eAAe,CAAE,CAAC,EACzE,CAAC,CACQ,CAAC,EACjB,CAAC,CACJ,CAAC,cAGP1D,IAAA,CAACJ,IAAI,EAAC8F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAxC,QAAA,cACvBlD,KAAA,CAACL,KAAK,EAACoD,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAE2C,MAAM,CAAE,GAAI,CAAE,CAAAzC,QAAA,eAC/BpD,IAAA,CAACL,UAAU,EAAC0D,OAAO,CAAC,WAAW,CAACiC,YAAY,MAACQ,KAAK,CAAC,QAAQ,CAAA1C,QAAA,CAAC,yBAE5D,CAAY,CAAC,cACbpD,IAAA,CAACZ,mBAAmB,EAAC2G,KAAK,CAAC,MAAM,CAACF,MAAM,CAAE,GAAI,CAAAzC,QAAA,cAC5ClD,KAAA,CAACrB,QAAQ,EAACoC,IAAI,CAAEU,SAAU,CAACwE,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,MAAM,CAAE,CAAE,CAAE,CAAAnD,QAAA,eAC7EpD,IAAA,CAACf,aAAa,EAACuH,eAAe,CAAC,KAAK,CAAE,CAAC,cACvCxG,IAAA,CAACjB,KAAK,EAACkH,OAAO,CAAC,OAAO,CAAE,CAAC,cACzBjG,IAAA,CAAChB,KAAK,GAAE,CAAC,cACTgB,IAAA,CAACd,OAAO,EAACgH,OAAO,cAAElG,IAAA,CAAC4C,aAAa,GAAE,CAAE,CAAE,CAAC,cACvC5C,IAAA,CAACb,MAAM,GAAE,CAAC,cACVa,IAAA,CAAClB,GAAG,EAACmH,OAAO,CAAC,OAAO,CAACjB,IAAI,CAAExE,aAAa,CAACE,SAAU,CAACgD,IAAI,CAAC,eAAe,CAAE,CAAC,EACnE,CAAC,CACQ,CAAC,EACjB,CAAC,CACJ,CAAC,cAGP1D,IAAA,CAACJ,IAAI,EAAC8F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAxC,QAAA,cACvBlD,KAAA,CAACL,KAAK,EAACoD,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAE2C,MAAM,CAAE,GAAI,CAAE,CAAAzC,QAAA,eAC/BpD,IAAA,CAACL,UAAU,EAAC0D,OAAO,CAAC,WAAW,CAACiC,YAAY,MAACQ,KAAK,CAAC,QAAQ,CAAA1C,QAAA,CAAC,4CAE5D,CAAY,CAAC,cACbpD,IAAA,CAACZ,mBAAmB,EAAC2G,KAAK,CAAC,MAAM,CAACF,MAAM,CAAE,GAAI,CAAAzC,QAAA,cAC5ClD,KAAA,CAACV,YAAY,EAAC2G,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEE,MAAM,CAAE,EAAE,CAAED,IAAI,CAAE,EAAG,CAAE,CAAAlD,QAAA,eACjEpD,IAAA,CAACf,aAAa,GAAE,CAAC,cACjBe,IAAA,CAACjB,KAAK,EACJ2H,IAAI,CAAC,QAAQ,CACbT,OAAO,CAAC,cAAc,CACtBvC,IAAI,CAAC,cAAc,CACnBV,KAAK,CAAE,CAAEW,KAAK,CAAE,cAAc,CAAEgD,QAAQ,CAAE,cAAc,CAAEC,MAAM,CAAE,CAAC,EAAG,CAAE,CACzE,CAAC,cACF5G,IAAA,CAAChB,KAAK,EACJ0H,IAAI,CAAC,QAAQ,CACbT,OAAO,CAAC,sBAAsB,CAC9BvC,IAAI,CAAC,YAAY,CACjBV,KAAK,CAAE,CAAEW,KAAK,CAAE,YAAY,CAAE8C,KAAK,CAAE,CAAC,EAAE,CAAEE,QAAQ,CAAE,YAAa,CAAE,CACpE,CAAC,cACF3G,IAAA,CAACd,OAAO,EAACgH,OAAO,cAAElG,IAAA,CAAC4D,cAAc,GAAE,CAAE,CAAE,CAAC,cACxC5D,IAAA,CAACP,OAAO,EACNiE,IAAI,CAAC,QAAQ,CACbzC,IAAI,CAAEwB,cAAe,CACrBuC,IAAI,CAAExE,aAAa,CAACK,IAAK,CAC1B,CAAC,EACU,CAAC,CACI,CAAC,EACjB,CAAC,CACJ,CAAC,cAGPb,IAAA,CAACJ,IAAI,EAAC8F,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAvC,QAAA,cAChBlD,KAAA,CAACL,KAAK,EAACoD,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAE,QAAA,eAClBpD,IAAA,CAACL,UAAU,EAAC0D,OAAO,CAAC,WAAW,CAACiC,YAAY,MAAAlC,QAAA,CAAC,yBAE7C,CAAY,CAAC,cACbpD,IAAA,CAACJ,IAAI,EAAC4F,SAAS,MAACC,OAAO,CAAE,CAAE,CAAArC,QAAA,CACxBzB,SAAS,CAACG,GAAG,CAAC,CAACP,KAAK,CAAEgC,KAAK,gBAC1BvD,IAAA,CAACJ,IAAI,EAAC8F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACkB,EAAE,CAAE,CAAE,CAACjB,EAAE,CAAE,CAAE,CAAAxC,QAAA,cAC9BlD,KAAA,CAACR,GAAG,EAACuD,EAAE,CAAE,CAAE6D,SAAS,CAAE,QAAQ,CAAE5D,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,mBAAmB,CAAE4D,YAAY,CAAE,CAAE,CAAE,CAAA3D,QAAA,eACnFpD,IAAA,CAACF,IAAI,EACHkD,KAAK,CAAEzB,KAAK,CAACA,KAAM,CACnBkC,KAAK,CAAElC,KAAK,CAACA,KAAK,GAAK,aAAa,CAAG,SAAS,CAAGA,KAAK,CAACA,KAAK,GAAK,WAAW,CAAG,OAAO,CAAG,SAAU,CACrG0B,EAAE,CAAE,CAAE+D,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACFhH,IAAA,CAACL,UAAU,EAAC0D,OAAO,CAAC,IAAI,CAAAD,QAAA,CAAE7B,KAAK,CAACC,KAAK,CAAa,CAAC,cACnDxB,IAAA,CAACL,UAAU,EAAC0D,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAC,QAAM,CAAY,CAAC,cAC/ClD,KAAA,CAACP,UAAU,EAAC0D,OAAO,CAAC,OAAO,CAAAD,QAAA,EACxB7B,KAAK,CAACG,aAAa,CAACoC,OAAO,CAAC,CAAC,CAAC,CAAC,WAClC,EAAY,CAAC,cACb5D,KAAA,CAACP,UAAU,EAAC0D,OAAO,CAAC,OAAO,CAAAD,QAAA,EACxB7B,KAAK,CAACU,oBAAoB,CAAC6B,OAAO,CAAC,CAAC,CAAC,CAAC,YACzC,EAAY,CAAC,EACV,CAAC,EAf8BP,KAgBhC,CACP,CAAC,CACE,CAAC,EACF,CAAC,CACJ,CAAC,EACH,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}