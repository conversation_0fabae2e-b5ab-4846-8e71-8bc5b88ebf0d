{"ast": null, "code": "import { getDay } from \"./getDay.mjs\";\nimport { subDays } from \"./subDays.mjs\";\n\n/**\n * @name previousDay\n * @category Weekday Helpers\n * @summary When is the previous day of the week?\n *\n * @description\n * When is the previous day of the week? 0-6 the day of the week, 0 represents Sunday.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n * @param day - The day of the week\n *\n * @returns The date is the previous day of week\n *\n * @example\n * // When is the previous Monday before Mar, 20, 2020?\n * const result = previousDay(new Date(2020, 2, 20), 1)\n * //=> Mon Mar 16 2020 00:00:00\n *\n * @example\n * // When is the previous Tuesday before Mar, 21, 2020?\n * const result = previousDay(new Date(2020, 2, 21), 2)\n * //=> Tue Mar 17 2020 00:00:00\n */\nexport function previousDay(date, day) {\n  let delta = getDay(date) - day;\n  if (delta <= 0) delta += 7;\n  return subDays(date, delta);\n}\n\n// Fallback for modularized imports:\nexport default previousDay;", "map": {"version": 3, "names": ["getDay", "subDays", "previousDay", "date", "day", "delta"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/previousDay.mjs"], "sourcesContent": ["import { getDay } from \"./getDay.mjs\";\nimport { subDays } from \"./subDays.mjs\";\n\n/**\n * @name previousDay\n * @category Weekday Helpers\n * @summary When is the previous day of the week?\n *\n * @description\n * When is the previous day of the week? 0-6 the day of the week, 0 represents Sunday.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n * @param day - The day of the week\n *\n * @returns The date is the previous day of week\n *\n * @example\n * // When is the previous Monday before Mar, 20, 2020?\n * const result = previousDay(new Date(2020, 2, 20), 1)\n * //=> Mon Mar 16 2020 00:00:00\n *\n * @example\n * // When is the previous Tuesday before Mar, 21, 2020?\n * const result = previousDay(new Date(2020, 2, 21), 2)\n * //=> Tue Mar 17 2020 00:00:00\n */\nexport function previousDay(date, day) {\n  let delta = getDay(date) - day;\n  if (delta <= 0) delta += 7;\n\n  return subDays(date, delta);\n}\n\n// Fallback for modularized imports:\nexport default previousDay;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;AACrC,SAASC,OAAO,QAAQ,eAAe;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,GAAG,EAAE;EACrC,IAAIC,KAAK,GAAGL,MAAM,CAACG,IAAI,CAAC,GAAGC,GAAG;EAC9B,IAAIC,KAAK,IAAI,CAAC,EAAEA,KAAK,IAAI,CAAC;EAE1B,OAAOJ,OAAO,CAACE,IAAI,EAAEE,KAAK,CAAC;AAC7B;;AAEA;AACA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}