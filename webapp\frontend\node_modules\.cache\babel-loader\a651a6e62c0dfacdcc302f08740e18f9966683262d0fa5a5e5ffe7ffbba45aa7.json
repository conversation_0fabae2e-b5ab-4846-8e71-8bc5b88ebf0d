{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\TestComande.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Button, Typography, Alert, Card, CardContent, TextField, Grid } from '@mui/material';\nimport comandeService from '../../services/comandeService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestComande = () => {\n  _s();\n  const [result, setResult] = useState(null);\n  const [error, setError] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [cantiereId, setCantiereId] = useState('1');\n  const testCreateComanda = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const comandaData = {\n        id_cantiere: parseInt(cantiereId),\n        tipo_comanda: 'POSA',\n        descrizione: 'Test comanda creata dal frontend',\n        responsabile: 'Test User'\n      };\n      const response = await comandeService.createComanda(comandaData);\n      setResult(response);\n    } catch (err) {\n      setError(err.message || 'Errore nella creazione della comanda');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const testGetComande = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await comandeService.getComande(cantiereId);\n      setResult(response);\n    } catch (err) {\n      setError(err.message || 'Errore nel recupero delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const testGetStatistiche = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await comandeService.getStatisticheComande(cantiereId);\n      setResult(response);\n    } catch (err) {\n      setError(err.message || 'Errore nel recupero delle statistiche');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Test Modulo Comande\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Configurazione Test\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          label: \"ID Cantiere\",\n          value: cantiereId,\n          onChange: e => setCantiereId(e.target.value),\n          type: \"number\",\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 4,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: testCreateComanda,\n          disabled: loading,\n          fullWidth: true,\n          children: \"Test Crea Comanda\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 4,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: testGetComande,\n          disabled: loading,\n          fullWidth: true,\n          children: \"Test Get Comande\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 4,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: testGetStatistiche,\n          disabled: loading,\n          fullWidth: true,\n          children: \"Test Statistiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 9\n    }, this), result && /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Risultato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n          style: {\n            backgroundColor: '#f5f5f5',\n            padding: '16px',\n            borderRadius: '4px',\n            overflow: 'auto',\n            fontSize: '12px'\n          },\n          children: JSON.stringify(result, null, 2)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(TestComande, \"Tbo4rOeo9wIkvGUwfGPokpyS6BQ=\");\n_c = TestComande;\nexport default TestComande;\nvar _c;\n$RefreshReg$(_c, \"TestComande\");", "map": {"version": 3, "names": ["React", "useState", "Box", "<PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "Grid", "comandeService", "jsxDEV", "_jsxDEV", "TestComande", "_s", "result", "setResult", "error", "setError", "loading", "setLoading", "cantiereId", "setCantiereId", "testCreateComanda", "comandaData", "id_cantiere", "parseInt", "tipo_comanda", "descrizione", "responsabile", "response", "createComanda", "err", "message", "testGetComande", "getComande", "testGetStatistiche", "getStatisticheComande", "sx", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "label", "value", "onChange", "e", "target", "type", "container", "spacing", "item", "xs", "sm", "onClick", "disabled", "fullWidth", "severity", "style", "backgroundColor", "padding", "borderRadius", "overflow", "fontSize", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/TestComande.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON>,\n  <PERSON>ton,\n  Ty<PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON>,\n  CardContent,\n  TextField,\n  Grid\n} from '@mui/material';\nimport comandeService from '../../services/comandeService';\n\nconst TestComande = () => {\n  const [result, setResult] = useState(null);\n  const [error, setError] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [cantiereId, setCantiereId] = useState('1');\n\n  const testCreateComanda = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const comandaData = {\n        id_cantiere: parseInt(cantiereId),\n        tipo_comanda: 'POSA',\n        descrizione: 'Test comanda creata dal frontend',\n        responsabile: 'Test User'\n      };\n      \n      const response = await comandeService.createComanda(comandaData);\n      setResult(response);\n    } catch (err) {\n      setError(err.message || 'Errore nella creazione della comanda');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testGetComande = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const response = await comandeService.getComande(cantiereId);\n      setResult(response);\n    } catch (err) {\n      setError(err.message || 'Errore nel recupero delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testGetStatistiche = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const response = await comandeService.getStatisticheComande(cantiereId);\n      setResult(response);\n    } catch (err) {\n      setError(err.message || 'Errore nel recupero delle statistiche');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Test Modulo Comande\n      </Typography>\n      \n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            Configurazione Test\n          </Typography>\n          <TextField\n            label=\"ID Cantiere\"\n            value={cantiereId}\n            onChange={(e) => setCantiereId(e.target.value)}\n            type=\"number\"\n            sx={{ mb: 2 }}\n          />\n        </CardContent>\n      </Card>\n\n      <Grid container spacing={2} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={4}>\n          <Button\n            variant=\"contained\"\n            onClick={testCreateComanda}\n            disabled={loading}\n            fullWidth\n          >\n            Test Crea Comanda\n          </Button>\n        </Grid>\n        <Grid item xs={12} sm={4}>\n          <Button\n            variant=\"contained\"\n            onClick={testGetComande}\n            disabled={loading}\n            fullWidth\n          >\n            Test Get Comande\n          </Button>\n        </Grid>\n        <Grid item xs={12} sm={4}>\n          <Button\n            variant=\"contained\"\n            onClick={testGetStatistiche}\n            disabled={loading}\n            fullWidth\n          >\n            Test Statistiche\n          </Button>\n        </Grid>\n      </Grid>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {result && (\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Risultato\n            </Typography>\n            <pre style={{ \n              backgroundColor: '#f5f5f5', \n              padding: '16px', \n              borderRadius: '4px',\n              overflow: 'auto',\n              fontSize: '12px'\n            }}>\n              {JSON.stringify(result, null, 2)}\n            </pre>\n          </CardContent>\n        </Card>\n      )}\n    </Box>\n  );\n};\n\nexport default TestComande;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,IAAI,QACC,eAAe;AACtB,OAAOC,cAAc,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,GAAG,CAAC;EAEjD,MAAMsB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBF,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMM,WAAW,GAAG;QAClBC,WAAW,EAAEC,QAAQ,CAACL,UAAU,CAAC;QACjCM,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,kCAAkC;QAC/CC,YAAY,EAAE;MAChB,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMpB,cAAc,CAACqB,aAAa,CAACP,WAAW,CAAC;MAChER,SAAS,CAACc,QAAQ,CAAC;IACrB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZd,QAAQ,CAACc,GAAG,CAACC,OAAO,IAAI,sCAAsC,CAAC;IACjE,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFd,UAAU,CAAC,IAAI,CAAC;MAChBF,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMY,QAAQ,GAAG,MAAMpB,cAAc,CAACyB,UAAU,CAACd,UAAU,CAAC;MAC5DL,SAAS,CAACc,QAAQ,CAAC;IACrB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZd,QAAQ,CAACc,GAAG,CAACC,OAAO,IAAI,mCAAmC,CAAC;IAC9D,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChBF,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMY,QAAQ,GAAG,MAAMpB,cAAc,CAAC2B,qBAAqB,CAAChB,UAAU,CAAC;MACvEL,SAAS,CAACc,QAAQ,CAAC;IACrB,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZd,QAAQ,CAACc,GAAG,CAACC,OAAO,IAAI,uCAAuC,CAAC;IAClE,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACER,OAAA,CAACV,GAAG;IAACoC,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAChB5B,OAAA,CAACR,UAAU;MAACqC,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEblC,OAAA,CAACN,IAAI;MAACgC,EAAE,EAAE;QAAES,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,eAClB5B,OAAA,CAACL,WAAW;QAAAiC,QAAA,gBACV5B,OAAA,CAACR,UAAU;UAACqC,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAF,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblC,OAAA,CAACJ,SAAS;UACRwC,KAAK,EAAC,aAAa;UACnBC,KAAK,EAAE5B,UAAW;UAClB6B,QAAQ,EAAGC,CAAC,IAAK7B,aAAa,CAAC6B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC/CI,IAAI,EAAC,QAAQ;UACbf,EAAE,EAAE;YAAES,EAAE,EAAE;UAAE;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEPlC,OAAA,CAACH,IAAI;MAAC6C,SAAS;MAACC,OAAO,EAAE,CAAE;MAACjB,EAAE,EAAE;QAAES,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,gBACxC5B,OAAA,CAACH,IAAI;QAAC+C,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACvB5B,OAAA,CAACT,MAAM;UACLsC,OAAO,EAAC,WAAW;UACnBkB,OAAO,EAAEpC,iBAAkB;UAC3BqC,QAAQ,EAAEzC,OAAQ;UAClB0C,SAAS;UAAArB,QAAA,EACV;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACPlC,OAAA,CAACH,IAAI;QAAC+C,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACvB5B,OAAA,CAACT,MAAM;UACLsC,OAAO,EAAC,WAAW;UACnBkB,OAAO,EAAEzB,cAAe;UACxB0B,QAAQ,EAAEzC,OAAQ;UAClB0C,SAAS;UAAArB,QAAA,EACV;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACPlC,OAAA,CAACH,IAAI;QAAC+C,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACvB5B,OAAA,CAACT,MAAM;UACLsC,OAAO,EAAC,WAAW;UACnBkB,OAAO,EAAEvB,kBAAmB;UAC5BwB,QAAQ,EAAEzC,OAAQ;UAClB0C,SAAS;UAAArB,QAAA,EACV;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEN7B,KAAK,iBACJL,OAAA,CAACP,KAAK;MAACyD,QAAQ,EAAC,OAAO;MAACxB,EAAE,EAAE;QAAES,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACnCvB;IAAK;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA/B,MAAM,iBACLH,OAAA,CAACN,IAAI;MAAAkC,QAAA,eACH5B,OAAA,CAACL,WAAW;QAAAiC,QAAA,gBACV5B,OAAA,CAACR,UAAU;UAACqC,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAF,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblC,OAAA;UAAKmD,KAAK,EAAE;YACVC,eAAe,EAAE,SAAS;YAC1BC,OAAO,EAAE,MAAM;YACfC,YAAY,EAAE,KAAK;YACnBC,QAAQ,EAAE,MAAM;YAChBC,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,EACC6B,IAAI,CAACC,SAAS,CAACvD,MAAM,EAAE,IAAI,EAAE,CAAC;QAAC;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChC,EAAA,CAvIID,WAAW;AAAA0D,EAAA,GAAX1D,WAAW;AAyIjB,eAAeA,WAAW;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}