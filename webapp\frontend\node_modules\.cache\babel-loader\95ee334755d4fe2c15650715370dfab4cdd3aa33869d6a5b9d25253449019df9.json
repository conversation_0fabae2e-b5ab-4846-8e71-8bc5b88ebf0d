{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\AggiungiCavoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, FormHelperText, Alert, CircularProgress, Typography, Paper } from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport config from '../../config';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AggiungiCavoForm = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  isDialog = false\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    revisione_ufficiale: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Carica la revisione corrente all'avvio\n  useEffect(() => {\n    const loadRevisioneCorrente = async () => {\n      try {\n        setLoadingRevisione(true);\n        const revisione = await caviService.getRevisioneCorrente(cantiereId);\n        setFormData(prev => ({\n          ...prev,\n          revisione_ufficiale: revisione\n        }));\n      } catch (error) {\n        console.error('Errore nel caricamento della revisione corrente:', error);\n        onError('Errore nel caricamento della revisione corrente');\n      } finally {\n        setLoadingRevisione(false);\n      }\n    };\n    loadRevisioneCorrente();\n  }, [cantiereId, onError]);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, non fare nulla (il dialog ha il suo pulsante di annullamento)\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      // Validazione completa dei dati del cavo\n      console.log('Dati del form prima della validazione:', formData);\n      const validation = validateCavoData(formData);\n      console.log('Risultato validazione:', validation);\n      if (!validation.isValid) {\n        console.error('Errori di validazione:', validation.errors);\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        onError('Ci sono errori nel form. Controlla i campi evidenziati in rosso.');\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Assicurati che i campi obbligatori siano presenti (basati sulla definizione della tabella)\n      // Campi obbligatori: id_cavo, id_cantiere, utility, tipologia, n_conduttori, sezione, metri_teorici,\n      // ubicazione_partenza, ubicazione_arrivo, stato_installazione\n\n      // Verifica che i campi obbligatori siano presenti\n      const requiredFields = ['id_cavo', 'utility', 'tipologia', 'n_conduttori', 'sezione', 'metri_teorici', 'ubicazione_partenza', 'ubicazione_arrivo', 'stato_installazione'];\n      const missingFields = requiredFields.filter(field => !validatedData[field]);\n      if (missingFields.length > 0) {\n        throw new Error(`Campi obbligatori mancanti: ${missingFields.join(', ')}`);\n      }\n\n      // Prepara i dati da inviare\n      const dataToSend = {\n        ...validatedData,\n        // Assicurati che i campi obbligatori siano presenti\n        id_cavo: validatedData.id_cavo.toUpperCase(),\n        utility: validatedData.utility,\n        tipologia: validatedData.tipologia,\n        n_conduttori: validatedData.n_conduttori ? validatedData.n_conduttori.toString() : \"0\",\n        // Invia come stringa\n        sezione: validatedData.sezione ? validatedData.sezione.toString() : \"0\",\n        // Invia come stringa\n        metri_teorici: parseFloat(validatedData.metri_teorici) || 0,\n        ubicazione_partenza: validatedData.ubicazione_partenza,\n        ubicazione_arrivo: validatedData.ubicazione_arrivo,\n        stato_installazione: validatedData.stato_installazione || 'Da installare',\n        // Campi opzionali\n        metratura_reale: validatedData.metratura_reale ? parseFloat(validatedData.metratura_reale) : null,\n        id_bobina: validatedData.id_bobina || null,\n        // Altri campi che potrebbero essere utili\n        sistema: validatedData.sistema || null,\n        colore_cavo: validatedData.colore_cavo || null,\n        utenza_partenza: validatedData.utenza_partenza || null,\n        utenza_arrivo: validatedData.utenza_arrivo || null,\n        descrizione_utenza_partenza: validatedData.descrizione_utenza_partenza || null,\n        descrizione_utenza_arrivo: validatedData.descrizione_utenza_arrivo || null,\n        sh: validatedData.sh || 'N',\n        responsabile_posa: validatedData.responsabile_posa || null,\n        note: validatedData.note || null\n      };\n      console.log('Dati da inviare al server dopo la validazione:', dataToSend);\n      try {\n        // Invia i dati al server\n        console.log('Tentativo di invio dati al server...');\n\n        // Verifica che cantiereId sia valido\n        if (!cantiereId) {\n          throw new Error('ID cantiere non valido o mancante');\n        }\n\n        // Usa direttamente axios per avere più controllo\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('Token di autenticazione mancante. Effettua nuovamente il login.');\n        }\n        console.log(`Invio richiesta POST a /cavi/${cantiereId}`);\n        console.log('Dati inviati:', JSON.stringify(dataToSend, null, 2));\n        const response = await axios.post(`${config.API_URL}/cavi/${cantiereId}`, dataToSend, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          },\n          timeout: 60000,\n          // 60 secondi (aumentato per risolvere problemi di timeout)\n          validateStatus: function (status) {\n            // Considera validi tutti gli status code per poter vedere l'errore\n            return true;\n          }\n        });\n\n        // Log dettagliato della risposta\n        console.log('Status code:', response.status);\n        console.log('Headers:', response.headers);\n        console.log('Risposta completa:', response.data);\n\n        // Se lo status code non è di successo, lancia un errore\n        if (response.status >= 400) {\n          let errorDetail = response.data.detail || `Errore ${response.status}: ${response.statusText}`;\n          console.error('Errore dal server:', errorDetail);\n\n          // Gestione specifica per errori comuni\n          if (response.status === 422) {\n            // Errore di validazione\n            console.error('Errore di validazione:', response.data);\n\n            // Verifica se l'errore è relativo a un ID duplicato\n            if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('duplicate')) {\n              errorDetail = `Esiste già un cavo con ID ${dataToSend.id_cavo}. Usa un ID diverso.`;\n            } else if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('unique')) {\n              errorDetail = `Esiste già un cavo con ID ${dataToSend.id_cavo}. Usa un ID diverso.`;\n            } else if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('already exists')) {\n              errorDetail = `Esiste già un cavo con ID ${dataToSend.id_cavo}. Usa un ID diverso.`;\n            } else if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('constraint')) {\n              errorDetail = `Errore di vincolo nel database. Potrebbe esserci un cavo con lo stesso ID o un altro problema di vincolo.`;\n            }\n\n            // Verifica se ci sono errori di validazione specifici nei campi\n            if (response.data && response.data.detail && Array.isArray(response.data.detail)) {\n              const validationErrors = response.data.detail;\n              const errorMessages = validationErrors.map(err => {\n                const field = err.loc && err.loc.length > 1 ? err.loc[1] : 'campo sconosciuto';\n                return `${field}: ${err.msg}`;\n              }).join('\\n');\n              errorDetail = `Errori di validazione:\\n${errorMessages}`;\n            }\n          }\n          throw new Error(errorDetail);\n        }\n        console.log('Risposta dal server:', response.data);\n        onSuccess('Cavo aggiunto con successo');\n\n        // Reindirizza alla visualizzazione dei cavi solo se non è in un dialog\n        if (!isDialog) {\n          console.log('Reindirizzamento a visualizza cavi...');\n          // Usa setTimeout per dare tempo al browser di mostrare il messaggio di successo\n          setTimeout(() => {\n            try {\n              window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n            } catch (navError) {\n              console.error('Errore durante il reindirizzamento:', navError);\n              // Fallback: ricarica la pagina\n              window.location.reload();\n            }\n          }, 1000);\n        }\n      } catch (error) {\n        console.error('Errore durante l\\'invio dei dati al server:', error);\n        let errorMessage = 'Errore durante l\\'aggiunta del cavo';\n\n        // Gestione specifica per errori di rete\n        if (error.message && (error.message.includes('Network Error') || error.message.includes('Failed to fetch'))) {\n          errorMessage = 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.';\n          console.error('Errore di connessione al server:', error);\n\n          // Prova a verificare se il server è raggiungibile\n          try {\n            // Usa l'URL del backend dalla configurazione\n            const pingUrl = `${config.API_URL}/health`;\n            console.log('Tentativo di ping al server:', pingUrl);\n            fetch(pingUrl, {\n              mode: 'cors'\n            }).then(response => {\n              console.log('Ping al server:', response.status);\n              if (!response.ok) {\n                console.error('Il server non risponde correttamente');\n              }\n            }).catch(pingError => {\n              console.error('Il server non è raggiungibile:', pingError);\n            });\n          } catch (pingError) {\n            console.error('Errore durante il ping al server:', pingError);\n          }\n        }\n        // Estrai il messaggio di errore per altri tipi di errori\n        else if (error && typeof error === 'object') {\n          if (error.response && error.response.data) {\n            // Errore dal server con risposta\n            const responseData = error.response.data;\n            if (responseData.detail) {\n              errorMessage = responseData.detail;\n            } else if (typeof responseData === 'string') {\n              errorMessage = responseData;\n            } else {\n              errorMessage = JSON.stringify(responseData);\n            }\n          } else if (error.detail) {\n            errorMessage = error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          } else if (error.error) {\n            errorMessage = error.error;\n          }\n        } else if (typeof error === 'string') {\n          errorMessage = error;\n        }\n\n        // Mostra l'errore all'utente\n        onError(errorMessage);\n        setLoading(false);\n      }\n\n      // Resetta il form\n      setFormData({\n        id_cavo: '',\n        revisione_ufficiale: formData.revisione_ufficiale,\n        // Mantieni la revisione\n        sistema: '',\n        utility: '',\n        colore_cavo: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        sh: '',\n        ubicazione_partenza: '',\n        utenza_partenza: '',\n        descrizione_utenza_partenza: '',\n        ubicazione_arrivo: '',\n        utenza_arrivo: '',\n        descrizione_utenza_arrivo: '',\n        metri_teorici: '',\n        metratura_reale: '0',\n        responsabile_posa: '',\n        id_bobina: '',\n        stato_installazione: 'Da installare'\n      });\n      setFormErrors({});\n      setFormWarnings({});\n    } catch (error) {\n      console.error('Errore durante l\\'aggiunta del cavo:', error);\n      onError(error.detail || 'Errore durante l\\'aggiunta del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleSubmit,\n    noValidate: true,\n    children: loadingRevisione ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [hasWarnings && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 21\n        }, this),\n        sx: {\n          mb: isDialog ? 2 : 3,\n          py: isDialog ? 0.5 : 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          sx: {\n            fontSize: isDialog ? '0.8rem' : '0.875rem'\n          },\n          children: \"Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Informazioni Generali\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"id_cavo\",\n              label: \"ID Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_cavo,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.id_cavo,\n              helperText: formErrors.id_cavo,\n              inputProps: {\n                style: {\n                  textTransform: 'uppercase'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"revisione_ufficiale\",\n              label: \"Revisione Ufficiale\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.revisione_ufficiale,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.revisione_ufficiale,\n              helperText: formErrors.revisione_ufficiale\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sistema\",\n              label: \"Sistema\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sistema,\n              onChange: handleFormChange,\n              error: !!formErrors.sistema,\n              helperText: formErrors.sistema\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utility\",\n              label: \"Utility\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utility,\n              onChange: handleFormChange,\n              error: !!formErrors.utility,\n              helperText: formErrors.utility\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Caratteristiche Tecniche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"colore_cavo\",\n              label: \"Colore Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.colore_cavo,\n              onChange: handleFormChange,\n              error: !!formErrors.colore_cavo,\n              helperText: formErrors.colore_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"tipologia\",\n              label: \"Tipologia\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.tipologia,\n              onChange: handleFormChange,\n              error: !!formErrors.tipologia,\n              helperText: formErrors.tipologia\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"n_conduttori\",\n              label: \"Numero Conduttori\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.n_conduttori,\n              onChange: handleFormChange,\n              error: !!formErrors.n_conduttori,\n              helperText: formErrors.n_conduttori || formWarnings.n_conduttori,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.n_conduttori ? 'orange' : undefined\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sezione\",\n              label: \"Sezione\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sezione,\n              onChange: handleFormChange,\n              error: !!formErrors.sezione,\n              helperText: formErrors.sezione || formWarnings.sezione,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.sezione ? 'orange' : undefined\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              error: !!formErrors.sh,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"sh-label\",\n                children: \"Schermato (S/N)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"sh-label\",\n                name: \"sh\",\n                value: formData.sh,\n                label: \"Schermato (S/N)\",\n                onChange: handleFormChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"S\",\n                  children: \"S\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"N\",\n                  children: \"N\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this), formErrors.sh && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: formErrors.sh\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Ubicazione Partenza\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_partenza\",\n              label: \"Ubicazione Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_partenza,\n              helperText: formErrors.ubicazione_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_partenza\",\n              label: \"Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_partenza,\n              helperText: formErrors.utenza_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_partenza\",\n              label: \"Descrizione Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_partenza,\n              helperText: formErrors.descrizione_utenza_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Ubicazione Arrivo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_arrivo\",\n              label: \"Ubicazione Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_arrivo,\n              helperText: formErrors.ubicazione_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_arrivo\",\n              label: \"Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_arrivo,\n              helperText: formErrors.utenza_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_arrivo\",\n              label: \"Descrizione Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_arrivo,\n              helperText: formErrors.descrizione_utenza_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Metratura\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"metri_teorici\",\n              label: \"Metri Teorici\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_teorici,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_teorici,\n              helperText: formErrors.metri_teorici\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 11\n      }, this), !isDialog && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          size: \"large\",\n          onClick: handleCancel,\n          disabled: loading,\n          sx: {\n            minWidth: 150\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"large\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 28\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 150\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 28\n          }, this) : 'Salva Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 647,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 636,\n        columnNumber: 13\n      }, this), isDialog && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"medium\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 28\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 120\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 28\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 369,\n    columnNumber: 5\n  }, this);\n};\n_s(AggiungiCavoForm, \"Muxm1Wb8jDN/87yWsjtE8U4F0S8=\", false, function () {\n  return [useNavigate];\n});\n_c = AggiungiCavoForm;\nexport default AggiungiCavoForm;\nvar _c;\n$RefreshReg$(_c, \"AggiungiCavoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "FormHelperText", "<PERSON><PERSON>", "CircularProgress", "Typography", "Paper", "Save", "SaveIcon", "Warning", "WarningIcon", "useNavigate", "axios", "config", "caviService", "validateCavoData", "validateField", "isEmpty", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AggiungiCavoForm", "cantiereId", "onSuccess", "onError", "isDialog", "_s", "navigate", "loading", "setLoading", "loadingRevisione", "setLoadingRevisione", "formData", "setFormData", "id_cavo", "revisione_ufficiale", "sistema", "utility", "colore_cavo", "tipologia", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "responsabile_posa", "id_bobina", "stato_installazione", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "loadRevisioneCorrente", "revisione", "getRevisioneCorrente", "prev", "error", "console", "handleFormChange", "e", "name", "value", "target", "additionalParams", "metriTeorici", "parseFloat", "result", "valid", "message", "warning", "handleCancel", "handleSubmit", "preventDefault", "log", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "validatedData", "toUpperCase", "requiredFields", "missingFields", "filter", "field", "length", "Error", "join", "dataToSend", "toString", "note", "token", "localStorage", "getItem", "JSON", "stringify", "response", "post", "API_URL", "headers", "timeout", "validateStatus", "status", "data", "errorDetail", "detail", "statusText", "toLowerCase", "includes", "Array", "isArray", "validationErrors", "errorMessages", "map", "err", "loc", "msg", "setTimeout", "window", "location", "href", "navError", "reload", "errorMessage", "pingUrl", "fetch", "mode", "then", "ok", "catch", "pingError", "responseData", "hasWarnings", "Object", "keys", "component", "onSubmit", "noValidate", "children", "sx", "display", "justifyContent", "my", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "icon", "mb", "py", "variant", "fontSize", "p", "boxShadow", "gutterBottom", "container", "spacing", "item", "xs", "sm", "label", "fullWidth", "onChange", "required", "helperText", "inputProps", "style", "textTransform", "FormHelperTextProps", "color", "undefined", "id", "labelId", "mt", "gap", "size", "onClick", "disabled", "min<PERSON><PERSON><PERSON>", "type", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/AggiungiCavoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormHelperText,\n  Alert,\n  CircularProgress,\n  Typography,\n  Paper\n} from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport config from '../../config';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\nconst AggiungiCavoForm = ({ cantiereId, onSuccess, onError, isDialog = false }) => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    revisione_ufficiale: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Carica la revisione corrente all'avvio\n  useEffect(() => {\n    const loadRevisioneCorrente = async () => {\n      try {\n        setLoadingRevisione(true);\n        const revisione = await caviService.getRevisioneCorrente(cantiereId);\n        setFormData(prev => ({ ...prev, revisione_ufficiale: revisione }));\n      } catch (error) {\n        console.error('Errore nel caricamento della revisione corrente:', error);\n        onError('Errore nel caricamento della revisione corrente');\n      } finally {\n        setLoadingRevisione(false);\n      }\n    };\n\n    loadRevisioneCorrente();\n  }, [cantiereId, onError]);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, non fare nulla (il dialog ha il suo pulsante di annullamento)\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      // Validazione completa dei dati del cavo\n      console.log('Dati del form prima della validazione:', formData);\n      const validation = validateCavoData(formData);\n      console.log('Risultato validazione:', validation);\n\n      if (!validation.isValid) {\n        console.error('Errori di validazione:', validation.errors);\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        onError('Ci sono errori nel form. Controlla i campi evidenziati in rosso.');\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Assicurati che i campi obbligatori siano presenti (basati sulla definizione della tabella)\n      // Campi obbligatori: id_cavo, id_cantiere, utility, tipologia, n_conduttori, sezione, metri_teorici,\n      // ubicazione_partenza, ubicazione_arrivo, stato_installazione\n\n      // Verifica che i campi obbligatori siano presenti\n      const requiredFields = [\n        'id_cavo', 'utility', 'tipologia', 'n_conduttori', 'sezione', 'metri_teorici',\n        'ubicazione_partenza', 'ubicazione_arrivo', 'stato_installazione'\n      ];\n\n      const missingFields = requiredFields.filter(field => !validatedData[field]);\n      if (missingFields.length > 0) {\n        throw new Error(`Campi obbligatori mancanti: ${missingFields.join(', ')}`);\n      }\n\n      // Prepara i dati da inviare\n      const dataToSend = {\n        ...validatedData,\n        // Assicurati che i campi obbligatori siano presenti\n        id_cavo: validatedData.id_cavo.toUpperCase(),\n        utility: validatedData.utility,\n        tipologia: validatedData.tipologia,\n        n_conduttori: validatedData.n_conduttori ? validatedData.n_conduttori.toString() : \"0\", // Invia come stringa\n        sezione: validatedData.sezione ? validatedData.sezione.toString() : \"0\", // Invia come stringa\n        metri_teorici: parseFloat(validatedData.metri_teorici) || 0,\n        ubicazione_partenza: validatedData.ubicazione_partenza,\n        ubicazione_arrivo: validatedData.ubicazione_arrivo,\n        stato_installazione: validatedData.stato_installazione || 'Da installare',\n\n        // Campi opzionali\n        metratura_reale: validatedData.metratura_reale ? parseFloat(validatedData.metratura_reale) : null,\n        id_bobina: validatedData.id_bobina || null,\n\n        // Altri campi che potrebbero essere utili\n        sistema: validatedData.sistema || null,\n        colore_cavo: validatedData.colore_cavo || null,\n        utenza_partenza: validatedData.utenza_partenza || null,\n        utenza_arrivo: validatedData.utenza_arrivo || null,\n        descrizione_utenza_partenza: validatedData.descrizione_utenza_partenza || null,\n        descrizione_utenza_arrivo: validatedData.descrizione_utenza_arrivo || null,\n        sh: validatedData.sh || 'N',\n        responsabile_posa: validatedData.responsabile_posa || null,\n        note: validatedData.note || null\n      };\n\n      console.log('Dati da inviare al server dopo la validazione:', dataToSend);\n\n      try {\n        // Invia i dati al server\n        console.log('Tentativo di invio dati al server...');\n\n        // Verifica che cantiereId sia valido\n        if (!cantiereId) {\n          throw new Error('ID cantiere non valido o mancante');\n        }\n\n        // Usa direttamente axios per avere più controllo\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('Token di autenticazione mancante. Effettua nuovamente il login.');\n        }\n\n        console.log(`Invio richiesta POST a /cavi/${cantiereId}`);\n        console.log('Dati inviati:', JSON.stringify(dataToSend, null, 2));\n\n        const response = await axios.post(`${config.API_URL}/cavi/${cantiereId}`, dataToSend, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          },\n          timeout: 60000, // 60 secondi (aumentato per risolvere problemi di timeout)\n          validateStatus: function (status) {\n            // Considera validi tutti gli status code per poter vedere l'errore\n            return true;\n          }\n        });\n\n        // Log dettagliato della risposta\n        console.log('Status code:', response.status);\n        console.log('Headers:', response.headers);\n        console.log('Risposta completa:', response.data);\n\n        // Se lo status code non è di successo, lancia un errore\n        if (response.status >= 400) {\n          let errorDetail = response.data.detail || `Errore ${response.status}: ${response.statusText}`;\n          console.error('Errore dal server:', errorDetail);\n\n          // Gestione specifica per errori comuni\n          if (response.status === 422) {\n            // Errore di validazione\n            console.error('Errore di validazione:', response.data);\n\n            // Verifica se l'errore è relativo a un ID duplicato\n            if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('duplicate')) {\n              errorDetail = `Esiste già un cavo con ID ${dataToSend.id_cavo}. Usa un ID diverso.`;\n            } else if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('unique')) {\n              errorDetail = `Esiste già un cavo con ID ${dataToSend.id_cavo}. Usa un ID diverso.`;\n            } else if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('already exists')) {\n              errorDetail = `Esiste già un cavo con ID ${dataToSend.id_cavo}. Usa un ID diverso.`;\n            } else if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('constraint')) {\n              errorDetail = `Errore di vincolo nel database. Potrebbe esserci un cavo con lo stesso ID o un altro problema di vincolo.`;\n            }\n\n            // Verifica se ci sono errori di validazione specifici nei campi\n            if (response.data && response.data.detail && Array.isArray(response.data.detail)) {\n              const validationErrors = response.data.detail;\n              const errorMessages = validationErrors.map(err => {\n                const field = err.loc && err.loc.length > 1 ? err.loc[1] : 'campo sconosciuto';\n                return `${field}: ${err.msg}`;\n              }).join('\\n');\n\n              errorDetail = `Errori di validazione:\\n${errorMessages}`;\n            }\n          }\n\n          throw new Error(errorDetail);\n        }\n\n        console.log('Risposta dal server:', response.data);\n        onSuccess('Cavo aggiunto con successo');\n\n        // Reindirizza alla visualizzazione dei cavi solo se non è in un dialog\n        if (!isDialog) {\n          console.log('Reindirizzamento a visualizza cavi...');\n          // Usa setTimeout per dare tempo al browser di mostrare il messaggio di successo\n          setTimeout(() => {\n            try {\n              window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n            } catch (navError) {\n              console.error('Errore durante il reindirizzamento:', navError);\n              // Fallback: ricarica la pagina\n              window.location.reload();\n            }\n          }, 1000);\n        }\n      } catch (error) {\n        console.error('Errore durante l\\'invio dei dati al server:', error);\n\n        let errorMessage = 'Errore durante l\\'aggiunta del cavo';\n\n        // Gestione specifica per errori di rete\n        if (error.message && (error.message.includes('Network Error') || error.message.includes('Failed to fetch'))) {\n          errorMessage = 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.';\n          console.error('Errore di connessione al server:', error);\n\n          // Prova a verificare se il server è raggiungibile\n          try {\n            // Usa l'URL del backend dalla configurazione\n            const pingUrl = `${config.API_URL}/health`;\n            console.log('Tentativo di ping al server:', pingUrl);\n\n            fetch(pingUrl, { mode: 'cors' })\n              .then(response => {\n                console.log('Ping al server:', response.status);\n                if (!response.ok) {\n                  console.error('Il server non risponde correttamente');\n                }\n              })\n              .catch(pingError => {\n                console.error('Il server non è raggiungibile:', pingError);\n              });\n          } catch (pingError) {\n            console.error('Errore durante il ping al server:', pingError);\n          }\n        }\n        // Estrai il messaggio di errore per altri tipi di errori\n        else if (error && typeof error === 'object') {\n          if (error.response && error.response.data) {\n            // Errore dal server con risposta\n            const responseData = error.response.data;\n            if (responseData.detail) {\n              errorMessage = responseData.detail;\n            } else if (typeof responseData === 'string') {\n              errorMessage = responseData;\n            } else {\n              errorMessage = JSON.stringify(responseData);\n            }\n          } else if (error.detail) {\n            errorMessage = error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          } else if (error.error) {\n            errorMessage = error.error;\n          }\n        } else if (typeof error === 'string') {\n          errorMessage = error;\n        }\n\n        // Mostra l'errore all'utente\n        onError(errorMessage);\n        setLoading(false);\n      }\n\n      // Resetta il form\n      setFormData({\n        id_cavo: '',\n        revisione_ufficiale: formData.revisione_ufficiale, // Mantieni la revisione\n        sistema: '',\n        utility: '',\n        colore_cavo: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        sh: '',\n        ubicazione_partenza: '',\n        utenza_partenza: '',\n        descrizione_utenza_partenza: '',\n        ubicazione_arrivo: '',\n        utenza_arrivo: '',\n        descrizione_utenza_arrivo: '',\n        metri_teorici: '',\n        metratura_reale: '0',\n        responsabile_posa: '',\n        id_bobina: '',\n        stato_installazione: 'Da installare'\n      });\n      setFormErrors({});\n      setFormWarnings({});\n    } catch (error) {\n      console.error('Errore durante l\\'aggiunta del cavo:', error);\n      onError(error.detail || 'Errore durante l\\'aggiunta del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n\n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n      {loadingRevisione ? (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      ) : (\n        <>\n          {hasWarnings && (\n            <Alert\n              severity=\"warning\"\n              icon={<WarningIcon />}\n              sx={{ mb: isDialog ? 2 : 3, py: isDialog ? 0.5 : 1 }}\n            >\n              <Typography variant=\"subtitle2\" sx={{ fontSize: isDialog ? '0.8rem' : '0.875rem' }}>\n                Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\n              </Typography>\n            </Alert>\n          )}\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Informazioni Generali\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.id_cavo}\n                  helperText={formErrors.id_cavo}\n                  inputProps={{ style: { textTransform: 'uppercase' } }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"revisione_ufficiale\"\n                  label=\"Revisione Ufficiale\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.revisione_ufficiale}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.revisione_ufficiale}\n                  helperText={formErrors.revisione_ufficiale}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sistema\"\n                  label=\"Sistema\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sistema}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sistema}\n                  helperText={formErrors.sistema}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: 3, mb: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Caratteristiche Tecniche\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"colore_cavo\"\n                  label=\"Colore Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.colore_cavo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.colore_cavo}\n                  helperText={formErrors.colore_cavo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"Numero Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_conduttori}\n                  helperText={formErrors.n_conduttori || formWarnings.n_conduttori}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.n_conduttori ? 'orange' : undefined }\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione || formWarnings.sezione}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.sezione ? 'orange' : undefined }\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <FormControl fullWidth error={!!formErrors.sh}>\n                  <InputLabel id=\"sh-label\">Schermato (S/N)</InputLabel>\n                  <Select\n                    labelId=\"sh-label\"\n                    name=\"sh\"\n                    value={formData.sh}\n                    label=\"Schermato (S/N)\"\n                    onChange={handleFormChange}\n                  >\n                    <MenuItem value=\"S\">S</MenuItem>\n                    <MenuItem value=\"N\">N</MenuItem>\n                  </Select>\n                  {formErrors.sh && <FormHelperText>{formErrors.sh}</FormHelperText>}\n                </FormControl>\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Ubicazione Partenza\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_partenza\"\n                  label=\"Ubicazione Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_partenza}\n                  helperText={formErrors.ubicazione_partenza}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_partenza\"\n                  label=\"Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_partenza}\n                  helperText={formErrors.utenza_partenza}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_partenza\"\n                  label=\"Descrizione Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_partenza}\n                  helperText={formErrors.descrizione_utenza_partenza}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Ubicazione Arrivo\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_arrivo\"\n                  label=\"Ubicazione Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_arrivo}\n                  helperText={formErrors.ubicazione_arrivo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_arrivo\"\n                  label=\"Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_arrivo}\n                  helperText={formErrors.utenza_arrivo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_arrivo\"\n                  label=\"Descrizione Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_arrivo}\n                  helperText={formErrors.descrizione_utenza_arrivo}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Metratura\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_teorici\"\n                  label=\"Metri Teorici\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_teorici}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_teorici}\n                  helperText={formErrors.metri_teorici}\n                />\n              </Grid>\n\n            </Grid>\n          </Paper>\n\n          {!isDialog && (\n            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 2 }}>\n              <Button\n                variant=\"outlined\"\n                color=\"secondary\"\n                size=\"large\"\n                onClick={handleCancel}\n                disabled={loading}\n                sx={{ minWidth: 150 }}\n              >\n                Annulla\n              </Button>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                color=\"primary\"\n                size=\"large\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n                sx={{ minWidth: 150 }}\n              >\n                {loading ? <CircularProgress size={24} /> : 'Salva Cavo'}\n              </Button>\n            </Box>\n          )}\n          {isDialog && (\n            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                color=\"primary\"\n                size=\"medium\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n                sx={{ minWidth: 120 }}\n              >\n                {loading ? <CircularProgress size={20} /> : 'Salva'}\n              </Button>\n            </Box>\n          )}\n        </>\n      )}\n    </Box>\n  );\n};\n\nexport default AggiungiCavoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SAASC,IAAI,IAAIC,QAAQ,EAAEC,OAAO,IAAIC,WAAW,QAAQ,qBAAqB;AAC9E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AACtF,SAASC,wBAAwB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvE,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,QAAQ,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACjF,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC;IACvC4C,OAAO,EAAE,EAAE;IACXC,mBAAmB,EAAE,EAAE;IACvBC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,EAAE,EAAE,EAAE;IACNC,mBAAmB,EAAE,EAAE;IACvBC,eAAe,EAAE,EAAE;IACnBC,2BAA2B,EAAE,EAAE;IAC/BC,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE,EAAE;IACjBC,yBAAyB,EAAE,EAAE;IAC7BC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,GAAG;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,mBAAmB,EAAE;EACvB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmE,qBAAqB,GAAG,MAAAA,CAAA,KAAY;MACxC,IAAI;QACF3B,mBAAmB,CAAC,IAAI,CAAC;QACzB,MAAM4B,SAAS,GAAG,MAAM/C,WAAW,CAACgD,oBAAoB,CAACtC,UAAU,CAAC;QACpEW,WAAW,CAAC4B,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE1B,mBAAmB,EAAEwB;QAAU,CAAC,CAAC,CAAC;MACpE,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;QACxEtC,OAAO,CAAC,iDAAiD,CAAC;MAC5D,CAAC,SAAS;QACRO,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAED2B,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,CAACpC,UAAU,EAAEE,OAAO,CAAC,CAAC;;EAEzB;EACA,MAAMwC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACAnC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACkC,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,MAAME,gBAAgB,GAAG,CAAC,CAAC;IAC3B,IAAIH,IAAI,KAAK,iBAAiB,EAAE;MAC9BG,gBAAgB,CAACC,YAAY,GAAGC,UAAU,CAACvC,QAAQ,CAACiB,aAAa,IAAI,CAAC,CAAC;IACzE;IAEA,MAAMuB,MAAM,GAAG1D,aAAa,CAACoD,IAAI,EAAEC,KAAK,EAAEE,gBAAgB,CAAC;;IAE3D;IACAd,aAAa,CAACM,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACK,IAAI,GAAG,CAACM,MAAM,CAACC,KAAK,GAAGD,MAAM,CAACE,OAAO,GAAG;IAC3C,CAAC,CAAC,CAAC;;IAEH;IACAjB,eAAe,CAACI,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACK,IAAI,GAAGM,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACE,OAAO,GAAG;IAC5C,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInD,QAAQ,EAAE;MACZ;MACA;IACF;IACA;IACAT,wBAAwB,CAACW,QAAQ,CAAC;EACpC,CAAC;;EAED;EACA,MAAMkD,YAAY,GAAG,MAAOZ,CAAC,IAAK;IAChCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClBjD,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACAkC,OAAO,CAACgB,GAAG,CAAC,wCAAwC,EAAE/C,QAAQ,CAAC;MAC/D,MAAMgD,UAAU,GAAGnE,gBAAgB,CAACmB,QAAQ,CAAC;MAC7C+B,OAAO,CAACgB,GAAG,CAAC,wBAAwB,EAAEC,UAAU,CAAC;MAEjD,IAAI,CAACA,UAAU,CAACC,OAAO,EAAE;QACvBlB,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEkB,UAAU,CAACE,MAAM,CAAC;QAC1D3B,aAAa,CAACyB,UAAU,CAACE,MAAM,CAAC;QAChCzB,eAAe,CAACuB,UAAU,CAACG,QAAQ,CAAC;QACpCtD,UAAU,CAAC,KAAK,CAAC;QACjBL,OAAO,CAAC,kEAAkE,CAAC;QAC3E;MACF;;MAEA;MACA,MAAM4D,aAAa,GAAGJ,UAAU,CAACI,aAAa;;MAE9C;MACAA,aAAa,CAAClD,OAAO,GAAGkD,aAAa,CAAClD,OAAO,CAACmD,WAAW,CAAC,CAAC;;MAE3D;MACA;MACA;;MAEA;MACA,MAAMC,cAAc,GAAG,CACrB,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,EAC7E,qBAAqB,EAAE,mBAAmB,EAAE,qBAAqB,CAClE;MAED,MAAMC,aAAa,GAAGD,cAAc,CAACE,MAAM,CAACC,KAAK,IAAI,CAACL,aAAa,CAACK,KAAK,CAAC,CAAC;MAC3E,IAAIF,aAAa,CAACG,MAAM,GAAG,CAAC,EAAE;QAC5B,MAAM,IAAIC,KAAK,CAAC,+BAA+BJ,aAAa,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAC5E;;MAEA;MACA,MAAMC,UAAU,GAAG;QACjB,GAAGT,aAAa;QAChB;QACAlD,OAAO,EAAEkD,aAAa,CAAClD,OAAO,CAACmD,WAAW,CAAC,CAAC;QAC5ChD,OAAO,EAAE+C,aAAa,CAAC/C,OAAO;QAC9BE,SAAS,EAAE6C,aAAa,CAAC7C,SAAS;QAClCC,YAAY,EAAE4C,aAAa,CAAC5C,YAAY,GAAG4C,aAAa,CAAC5C,YAAY,CAACsD,QAAQ,CAAC,CAAC,GAAG,GAAG;QAAE;QACxFrD,OAAO,EAAE2C,aAAa,CAAC3C,OAAO,GAAG2C,aAAa,CAAC3C,OAAO,CAACqD,QAAQ,CAAC,CAAC,GAAG,GAAG;QAAE;QACzE7C,aAAa,EAAEsB,UAAU,CAACa,aAAa,CAACnC,aAAa,CAAC,IAAI,CAAC;QAC3DN,mBAAmB,EAAEyC,aAAa,CAACzC,mBAAmB;QACtDG,iBAAiB,EAAEsC,aAAa,CAACtC,iBAAiB;QAClDO,mBAAmB,EAAE+B,aAAa,CAAC/B,mBAAmB,IAAI,eAAe;QAEzE;QACAH,eAAe,EAAEkC,aAAa,CAAClC,eAAe,GAAGqB,UAAU,CAACa,aAAa,CAAClC,eAAe,CAAC,GAAG,IAAI;QACjGE,SAAS,EAAEgC,aAAa,CAAChC,SAAS,IAAI,IAAI;QAE1C;QACAhB,OAAO,EAAEgD,aAAa,CAAChD,OAAO,IAAI,IAAI;QACtCE,WAAW,EAAE8C,aAAa,CAAC9C,WAAW,IAAI,IAAI;QAC9CM,eAAe,EAAEwC,aAAa,CAACxC,eAAe,IAAI,IAAI;QACtDG,aAAa,EAAEqC,aAAa,CAACrC,aAAa,IAAI,IAAI;QAClDF,2BAA2B,EAAEuC,aAAa,CAACvC,2BAA2B,IAAI,IAAI;QAC9EG,yBAAyB,EAAEoC,aAAa,CAACpC,yBAAyB,IAAI,IAAI;QAC1EN,EAAE,EAAE0C,aAAa,CAAC1C,EAAE,IAAI,GAAG;QAC3BS,iBAAiB,EAAEiC,aAAa,CAACjC,iBAAiB,IAAI,IAAI;QAC1D4C,IAAI,EAAEX,aAAa,CAACW,IAAI,IAAI;MAC9B,CAAC;MAEDhC,OAAO,CAACgB,GAAG,CAAC,gDAAgD,EAAEc,UAAU,CAAC;MAEzE,IAAI;QACF;QACA9B,OAAO,CAACgB,GAAG,CAAC,sCAAsC,CAAC;;QAEnD;QACA,IAAI,CAACzD,UAAU,EAAE;UACf,MAAM,IAAIqE,KAAK,CAAC,mCAAmC,CAAC;QACtD;;QAEA;QACA,MAAMK,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI,CAACF,KAAK,EAAE;UACV,MAAM,IAAIL,KAAK,CAAC,iEAAiE,CAAC;QACpF;QAEA5B,OAAO,CAACgB,GAAG,CAAC,gCAAgCzD,UAAU,EAAE,CAAC;QACzDyC,OAAO,CAACgB,GAAG,CAAC,eAAe,EAAEoB,IAAI,CAACC,SAAS,CAACP,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAEjE,MAAMQ,QAAQ,GAAG,MAAM3F,KAAK,CAAC4F,IAAI,CAAC,GAAG3F,MAAM,CAAC4F,OAAO,SAASjF,UAAU,EAAE,EAAEuE,UAAU,EAAE;UACpFW,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUR,KAAK;UAClC,CAAC;UACDS,OAAO,EAAE,KAAK;UAAE;UAChBC,cAAc,EAAE,SAAAA,CAAUC,MAAM,EAAE;YAChC;YACA,OAAO,IAAI;UACb;QACF,CAAC,CAAC;;QAEF;QACA5C,OAAO,CAACgB,GAAG,CAAC,cAAc,EAAEsB,QAAQ,CAACM,MAAM,CAAC;QAC5C5C,OAAO,CAACgB,GAAG,CAAC,UAAU,EAAEsB,QAAQ,CAACG,OAAO,CAAC;QACzCzC,OAAO,CAACgB,GAAG,CAAC,oBAAoB,EAAEsB,QAAQ,CAACO,IAAI,CAAC;;QAEhD;QACA,IAAIP,QAAQ,CAACM,MAAM,IAAI,GAAG,EAAE;UAC1B,IAAIE,WAAW,GAAGR,QAAQ,CAACO,IAAI,CAACE,MAAM,IAAI,UAAUT,QAAQ,CAACM,MAAM,KAAKN,QAAQ,CAACU,UAAU,EAAE;UAC7FhD,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAE+C,WAAW,CAAC;;UAEhD;UACA,IAAIR,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE;YAC3B;YACA5C,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEuC,QAAQ,CAACO,IAAI,CAAC;;YAEtD;YACA,IAAIC,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAIA,WAAW,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;cACrGJ,WAAW,GAAG,6BAA6BhB,UAAU,CAAC3D,OAAO,sBAAsB;YACrF,CAAC,MAAM,IAAI2E,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAIA,WAAW,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;cACzGJ,WAAW,GAAG,6BAA6BhB,UAAU,CAAC3D,OAAO,sBAAsB;YACrF,CAAC,MAAM,IAAI2E,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAIA,WAAW,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;cACjHJ,WAAW,GAAG,6BAA6BhB,UAAU,CAAC3D,OAAO,sBAAsB;YACrF,CAAC,MAAM,IAAI2E,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAIA,WAAW,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;cAC7GJ,WAAW,GAAG,2GAA2G;YAC3H;;YAEA;YACA,IAAIR,QAAQ,CAACO,IAAI,IAAIP,QAAQ,CAACO,IAAI,CAACE,MAAM,IAAII,KAAK,CAACC,OAAO,CAACd,QAAQ,CAACO,IAAI,CAACE,MAAM,CAAC,EAAE;cAChF,MAAMM,gBAAgB,GAAGf,QAAQ,CAACO,IAAI,CAACE,MAAM;cAC7C,MAAMO,aAAa,GAAGD,gBAAgB,CAACE,GAAG,CAACC,GAAG,IAAI;gBAChD,MAAM9B,KAAK,GAAG8B,GAAG,CAACC,GAAG,IAAID,GAAG,CAACC,GAAG,CAAC9B,MAAM,GAAG,CAAC,GAAG6B,GAAG,CAACC,GAAG,CAAC,CAAC,CAAC,GAAG,mBAAmB;gBAC9E,OAAO,GAAG/B,KAAK,KAAK8B,GAAG,CAACE,GAAG,EAAE;cAC/B,CAAC,CAAC,CAAC7B,IAAI,CAAC,IAAI,CAAC;cAEbiB,WAAW,GAAG,2BAA2BQ,aAAa,EAAE;YAC1D;UACF;UAEA,MAAM,IAAI1B,KAAK,CAACkB,WAAW,CAAC;QAC9B;QAEA9C,OAAO,CAACgB,GAAG,CAAC,sBAAsB,EAAEsB,QAAQ,CAACO,IAAI,CAAC;QAClDrF,SAAS,CAAC,4BAA4B,CAAC;;QAEvC;QACA,IAAI,CAACE,QAAQ,EAAE;UACbsC,OAAO,CAACgB,GAAG,CAAC,uCAAuC,CAAC;UACpD;UACA2C,UAAU,CAAC,MAAM;YACf,IAAI;cACFC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuBvG,UAAU,kBAAkB;YAC5E,CAAC,CAAC,OAAOwG,QAAQ,EAAE;cACjB/D,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEgE,QAAQ,CAAC;cAC9D;cACAH,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC;YAC1B;UACF,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,CAAC,OAAOjE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;QAEnE,IAAIkE,YAAY,GAAG,qCAAqC;;QAExD;QACA,IAAIlE,KAAK,CAACY,OAAO,KAAKZ,KAAK,CAACY,OAAO,CAACuC,QAAQ,CAAC,eAAe,CAAC,IAAInD,KAAK,CAACY,OAAO,CAACuC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,EAAE;UAC3Ge,YAAY,GAAG,+EAA+E;UAC9FjE,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;UAExD;UACA,IAAI;YACF;YACA,MAAMmE,OAAO,GAAG,GAAGtH,MAAM,CAAC4F,OAAO,SAAS;YAC1CxC,OAAO,CAACgB,GAAG,CAAC,8BAA8B,EAAEkD,OAAO,CAAC;YAEpDC,KAAK,CAACD,OAAO,EAAE;cAAEE,IAAI,EAAE;YAAO,CAAC,CAAC,CAC7BC,IAAI,CAAC/B,QAAQ,IAAI;cAChBtC,OAAO,CAACgB,GAAG,CAAC,iBAAiB,EAAEsB,QAAQ,CAACM,MAAM,CAAC;cAC/C,IAAI,CAACN,QAAQ,CAACgC,EAAE,EAAE;gBAChBtE,OAAO,CAACD,KAAK,CAAC,sCAAsC,CAAC;cACvD;YACF,CAAC,CAAC,CACDwE,KAAK,CAACC,SAAS,IAAI;cAClBxE,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEyE,SAAS,CAAC;YAC5D,CAAC,CAAC;UACN,CAAC,CAAC,OAAOA,SAAS,EAAE;YAClBxE,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEyE,SAAS,CAAC;UAC/D;QACF;QACA;QAAA,KACK,IAAIzE,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC3C,IAAIA,KAAK,CAACuC,QAAQ,IAAIvC,KAAK,CAACuC,QAAQ,CAACO,IAAI,EAAE;YACzC;YACA,MAAM4B,YAAY,GAAG1E,KAAK,CAACuC,QAAQ,CAACO,IAAI;YACxC,IAAI4B,YAAY,CAAC1B,MAAM,EAAE;cACvBkB,YAAY,GAAGQ,YAAY,CAAC1B,MAAM;YACpC,CAAC,MAAM,IAAI,OAAO0B,YAAY,KAAK,QAAQ,EAAE;cAC3CR,YAAY,GAAGQ,YAAY;YAC7B,CAAC,MAAM;cACLR,YAAY,GAAG7B,IAAI,CAACC,SAAS,CAACoC,YAAY,CAAC;YAC7C;UACF,CAAC,MAAM,IAAI1E,KAAK,CAACgD,MAAM,EAAE;YACvBkB,YAAY,GAAGlE,KAAK,CAACgD,MAAM;UAC7B,CAAC,MAAM,IAAIhD,KAAK,CAACY,OAAO,EAAE;YACxBsD,YAAY,GAAGlE,KAAK,CAACY,OAAO;UAC9B,CAAC,MAAM,IAAIZ,KAAK,CAACA,KAAK,EAAE;YACtBkE,YAAY,GAAGlE,KAAK,CAACA,KAAK;UAC5B;QACF,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UACpCkE,YAAY,GAAGlE,KAAK;QACtB;;QAEA;QACAtC,OAAO,CAACwG,YAAY,CAAC;QACrBnG,UAAU,CAAC,KAAK,CAAC;MACnB;;MAEA;MACAI,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,mBAAmB,EAAEH,QAAQ,CAACG,mBAAmB;QAAE;QACnDC,OAAO,EAAE,EAAE;QACXC,OAAO,EAAE,EAAE;QACXC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,EAAE;QACXC,EAAE,EAAE,EAAE;QACNC,mBAAmB,EAAE,EAAE;QACvBC,eAAe,EAAE,EAAE;QACnBC,2BAA2B,EAAE,EAAE;QAC/BC,iBAAiB,EAAE,EAAE;QACrBC,aAAa,EAAE,EAAE;QACjBC,yBAAyB,EAAE,EAAE;QAC7BC,aAAa,EAAE,EAAE;QACjBC,eAAe,EAAE,GAAG;QACpBC,iBAAiB,EAAE,EAAE;QACrBC,SAAS,EAAE,EAAE;QACbC,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACFE,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DtC,OAAO,CAACsC,KAAK,CAACgD,MAAM,IAAI,qCAAqC,CAAC;IAChE,CAAC,SAAS;MACRjF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4G,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACnF,YAAY,CAAC,CAACkC,MAAM,GAAG,CAAC;EAExD,oBACExE,OAAA,CAAC1B,GAAG;IAACoJ,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAEhE,YAAa;IAACiE,UAAU;IAAAC,QAAA,EACrDjH,gBAAgB,gBACfZ,OAAA,CAAC1B,GAAG;MAACwJ,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAC5D7H,OAAA,CAAChB,gBAAgB;QAAAkJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,gBAENrI,OAAA,CAAAE,SAAA;MAAA2H,QAAA,GACGN,WAAW,iBACVvH,OAAA,CAACjB,KAAK;QACJuJ,QAAQ,EAAC,SAAS;QAClBC,IAAI,eAAEvI,OAAA,CAACV,WAAW;UAAA4I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBP,EAAE,EAAE;UAAEU,EAAE,EAAEjI,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEkI,EAAE,EAAElI,QAAQ,GAAG,GAAG,GAAG;QAAE,CAAE;QAAAsH,QAAA,eAErD7H,OAAA,CAACf,UAAU;UAACyJ,OAAO,EAAC,WAAW;UAACZ,EAAE,EAAE;YAAEa,QAAQ,EAAEpI,QAAQ,GAAG,QAAQ,GAAG;UAAW,CAAE;UAAAsH,QAAA,EAAC;QAEpF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR,eAEDrI,OAAA,CAACd,KAAK;QAAC4I,EAAE,EAAE;UAAEc,CAAC,EAAErI,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEiI,EAAE,EAAEjI,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEsI,SAAS,EAAEtI,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAsH,QAAA,gBACpF7H,OAAA,CAACf,UAAU;UAACyJ,OAAO,EAAC,IAAI;UAACI,YAAY;UAAChB,EAAE,EAAE;YAAEa,QAAQ,EAAEpI,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAsH,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrI,OAAA,CAACvB,IAAI;UAACsK,SAAS;UAACC,OAAO,EAAEzI,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAsH,QAAA,gBACxC7H,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACdoG,KAAK,EAAC,SAAS;cACfC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACE,OAAQ;cACxBsI,QAAQ,EAAExG,gBAAiB;cAC3ByG,QAAQ;cACR3G,KAAK,EAAE,CAAC,CAACR,UAAU,CAACpB,OAAQ;cAC5BwI,UAAU,EAAEpH,UAAU,CAACpB,OAAQ;cAC/ByI,UAAU,EAAE;gBAAEC,KAAK,EAAE;kBAAEC,aAAa,EAAE;gBAAY;cAAE;YAAE;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrI,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,qBAAqB;cAC1BoG,KAAK,EAAC,qBAAqB;cAC3BC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACG,mBAAoB;cACpCqI,QAAQ,EAAExG,gBAAiB;cAC3ByG,QAAQ;cACR3G,KAAK,EAAE,CAAC,CAACR,UAAU,CAACnB,mBAAoB;cACxCuI,UAAU,EAAEpH,UAAU,CAACnB;YAAoB;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrI,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACdoG,KAAK,EAAC,SAAS;cACfC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACI,OAAQ;cACxBoI,QAAQ,EAAExG,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAAClB,OAAQ;cAC5BsI,UAAU,EAAEpH,UAAU,CAAClB;YAAQ;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrI,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACdoG,KAAK,EAAC,SAAS;cACfC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACK,OAAQ;cACxBmI,QAAQ,EAAExG,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACjB,OAAQ;cAC5BqI,UAAU,EAAEpH,UAAU,CAACjB;YAAQ;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERrI,OAAA,CAACd,KAAK;QAAC4I,EAAE,EAAE;UAAEc,CAAC,EAAE,CAAC;UAAEJ,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,gBACzB7H,OAAA,CAACf,UAAU;UAACyJ,OAAO,EAAC,IAAI;UAACI,YAAY;UAAAjB,QAAA,EAAC;QAEtC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrI,OAAA,CAACvB,IAAI;UAACsK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAnB,QAAA,gBACzB7H,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,aAAa;cAClBoG,KAAK,EAAC,aAAa;cACnBC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACM,WAAY;cAC5BkI,QAAQ,EAAExG,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAAChB,WAAY;cAChCoI,UAAU,EAAEpH,UAAU,CAAChB;YAAY;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrI,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,WAAW;cAChBoG,KAAK,EAAC,WAAW;cACjBC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACO,SAAU;cAC1BiI,QAAQ,EAAExG,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACf,SAAU;cAC9BmI,UAAU,EAAEpH,UAAU,CAACf;YAAU;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrI,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,cAAc;cACnBoG,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACQ,YAAa;cAC7BgI,QAAQ,EAAExG,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACd,YAAa;cACjCkI,UAAU,EAAEpH,UAAU,CAACd,YAAY,IAAIgB,YAAY,CAAChB,YAAa;cACjEsI,mBAAmB,EAAE;gBACnBF,KAAK,EAAE;kBAAEG,KAAK,EAAEvH,YAAY,CAAChB,YAAY,GAAG,QAAQ,GAAGwI;gBAAU;cACnE;YAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrI,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACdoG,KAAK,EAAC,SAAS;cACfC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACS,OAAQ;cACxB+H,QAAQ,EAAExG,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACb,OAAQ;cAC5BiI,UAAU,EAAEpH,UAAU,CAACb,OAAO,IAAIe,YAAY,CAACf,OAAQ;cACvDqI,mBAAmB,EAAE;gBACnBF,KAAK,EAAE;kBAAEG,KAAK,EAAEvH,YAAY,CAACf,OAAO,GAAG,QAAQ,GAAGuI;gBAAU;cAC9D;YAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrI,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACtB,WAAW;cAAC2K,SAAS;cAACzG,KAAK,EAAE,CAAC,CAACR,UAAU,CAACZ,EAAG;cAAAqG,QAAA,gBAC5C7H,OAAA,CAACrB,UAAU;gBAACoL,EAAE,EAAC,UAAU;gBAAAlC,QAAA,EAAC;cAAe;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtDrI,OAAA,CAACpB,MAAM;gBACLoL,OAAO,EAAC,UAAU;gBAClBhH,IAAI,EAAC,IAAI;gBACTC,KAAK,EAAEnC,QAAQ,CAACU,EAAG;gBACnB4H,KAAK,EAAC,iBAAiB;gBACvBE,QAAQ,EAAExG,gBAAiB;gBAAA+E,QAAA,gBAE3B7H,OAAA,CAACnB,QAAQ;kBAACoE,KAAK,EAAC,GAAG;kBAAA4E,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAChCrI,OAAA,CAACnB,QAAQ;kBAACoE,KAAK,EAAC,GAAG;kBAAA4E,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,EACRjG,UAAU,CAACZ,EAAE,iBAAIxB,OAAA,CAAClB,cAAc;gBAAA+I,QAAA,EAAEzF,UAAU,CAACZ;cAAE;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERrI,OAAA,CAACd,KAAK;QAAC4I,EAAE,EAAE;UAAEc,CAAC,EAAErI,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEiI,EAAE,EAAEjI,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEsI,SAAS,EAAEtI,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAsH,QAAA,gBACpF7H,OAAA,CAACf,UAAU;UAACyJ,OAAO,EAAC,IAAI;UAACI,YAAY;UAAChB,EAAE,EAAE;YAAEa,QAAQ,EAAEpI,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAsH,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrI,OAAA,CAACvB,IAAI;UAACsK,SAAS;UAACC,OAAO,EAAEzI,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAsH,QAAA,gBACxC7H,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,qBAAqB;cAC1BoG,KAAK,EAAC,qBAAqB;cAC3BC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACW,mBAAoB;cACpC6H,QAAQ,EAAExG,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACX,mBAAoB;cACxC+H,UAAU,EAAEpH,UAAU,CAACX;YAAoB;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrI,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,iBAAiB;cACtBoG,KAAK,EAAC,iBAAiB;cACvBC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACY,eAAgB;cAChC4H,QAAQ,EAAExG,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACV,eAAgB;cACpC8H,UAAU,EAAEpH,UAAU,CAACV;YAAgB;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrI,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,6BAA6B;cAClCoG,KAAK,EAAC,6BAA6B;cACnCC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACa,2BAA4B;cAC5C2H,QAAQ,EAAExG,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACT,2BAA4B;cAChD6H,UAAU,EAAEpH,UAAU,CAACT;YAA4B;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERrI,OAAA,CAACd,KAAK;QAAC4I,EAAE,EAAE;UAAEc,CAAC,EAAErI,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEiI,EAAE,EAAEjI,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEsI,SAAS,EAAEtI,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAsH,QAAA,gBACpF7H,OAAA,CAACf,UAAU;UAACyJ,OAAO,EAAC,IAAI;UAACI,YAAY;UAAChB,EAAE,EAAE;YAAEa,QAAQ,EAAEpI,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAsH,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrI,OAAA,CAACvB,IAAI;UAACsK,SAAS;UAACC,OAAO,EAAEzI,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAsH,QAAA,gBACxC7H,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,mBAAmB;cACxBoG,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACc,iBAAkB;cAClC0H,QAAQ,EAAExG,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACR,iBAAkB;cACtC4H,UAAU,EAAEpH,UAAU,CAACR;YAAkB;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrI,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,eAAe;cACpBoG,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACe,aAAc;cAC9ByH,QAAQ,EAAExG,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACP,aAAc;cAClC2H,UAAU,EAAEpH,UAAU,CAACP;YAAc;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrI,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,2BAA2B;cAChCoG,KAAK,EAAC,2BAA2B;cACjCC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACgB,yBAA0B;cAC1CwH,QAAQ,EAAExG,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACN,yBAA0B;cAC9C0H,UAAU,EAAEpH,UAAU,CAACN;YAA0B;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERrI,OAAA,CAACd,KAAK;QAAC4I,EAAE,EAAE;UAAEc,CAAC,EAAErI,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEiI,EAAE,EAAEjI,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEsI,SAAS,EAAEtI,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAsH,QAAA,gBACpF7H,OAAA,CAACf,UAAU;UAACyJ,OAAO,EAAC,IAAI;UAACI,YAAY;UAAChB,EAAE,EAAE;YAAEa,QAAQ,EAAEpI,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAsH,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrI,OAAA,CAACvB,IAAI;UAACsK,SAAS;UAACC,OAAO,EAAEzI,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAsH,QAAA,eACxC7H,OAAA,CAACvB,IAAI;YAACwK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvB7H,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,eAAe;cACpBoG,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTX,OAAO,EAAC,UAAU;cAClBzF,KAAK,EAAEnC,QAAQ,CAACiB,aAAc;cAC9BuH,QAAQ,EAAExG,gBAAiB;cAC3ByG,QAAQ;cACR3G,KAAK,EAAE,CAAC,CAACR,UAAU,CAACL,aAAc;cAClCyH,UAAU,EAAEpH,UAAU,CAACL;YAAc;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAEP,CAAC9H,QAAQ,iBACRP,OAAA,CAAC1B,GAAG;QAACwJ,EAAE,EAAE;UAAEmC,EAAE,EAAE,CAAC;UAAElC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEkC,GAAG,EAAE;QAAE,CAAE;QAAArC,QAAA,gBACpE7H,OAAA,CAACxB,MAAM;UACLkK,OAAO,EAAC,UAAU;UAClBmB,KAAK,EAAC,WAAW;UACjBM,IAAI,EAAC,OAAO;UACZC,OAAO,EAAE1G,YAAa;UACtB2G,QAAQ,EAAE3J,OAAQ;UAClBoH,EAAE,EAAE;YAAEwC,QAAQ,EAAE;UAAI,CAAE;UAAAzC,QAAA,EACvB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrI,OAAA,CAACxB,MAAM;UACL+L,IAAI,EAAC,QAAQ;UACb7B,OAAO,EAAC,WAAW;UACnBmB,KAAK,EAAC,SAAS;UACfM,IAAI,EAAC,OAAO;UACZK,SAAS,eAAExK,OAAA,CAACZ,QAAQ;YAAA8I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBgC,QAAQ,EAAE3J,OAAQ;UAClBoH,EAAE,EAAE;YAAEwC,QAAQ,EAAE;UAAI,CAAE;UAAAzC,QAAA,EAErBnH,OAAO,gBAAGV,OAAA,CAAChB,gBAAgB;YAACmL,IAAI,EAAE;UAAG;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAY;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EACA9H,QAAQ,iBACPP,OAAA,CAAC1B,GAAG;QAACwJ,EAAE,EAAE;UAAEmC,EAAE,EAAE,CAAC;UAAElC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEkC,GAAG,EAAE;QAAE,CAAE;QAAArC,QAAA,eACtE7H,OAAA,CAACxB,MAAM;UACL+L,IAAI,EAAC,QAAQ;UACb7B,OAAO,EAAC,WAAW;UACnBmB,KAAK,EAAC,SAAS;UACfM,IAAI,EAAC,QAAQ;UACbK,SAAS,eAAExK,OAAA,CAACZ,QAAQ;YAAA8I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBgC,QAAQ,EAAE3J,OAAQ;UAClBoH,EAAE,EAAE;YAAEwC,QAAQ,EAAE;UAAI,CAAE;UAAAzC,QAAA,EAErBnH,OAAO,gBAAGV,OAAA,CAAChB,gBAAgB;YAACmL,IAAI,EAAE;UAAG;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA,eACD;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC7H,EAAA,CA9oBIL,gBAAgB;EAAA,QACHZ,WAAW;AAAA;AAAAkL,EAAA,GADxBtK,gBAAgB;AAgpBtB,eAAeA,gBAAgB;AAAC,IAAAsK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}