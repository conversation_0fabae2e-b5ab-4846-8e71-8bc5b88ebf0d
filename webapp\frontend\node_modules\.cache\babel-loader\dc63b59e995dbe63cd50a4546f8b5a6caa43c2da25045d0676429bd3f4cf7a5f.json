{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Typography,Chip,TableRow,TableCell,IconButton,Tooltip}from'@mui/material';import{Edit as EditIcon,Delete as DeleteIcon,History as HistoryIcon,Add as AddIcon}from'@mui/icons-material';import FilterableTable from'../common/FilterableTable';import{REEL_STATES,getReelStateColor}from'../../utils/stateUtils';/**\n * Componente per visualizzare la lista delle bobine con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.bobine - Lista delle bobine da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {Function} props.onEdit - Funzione chiamata quando si vuole modificare una bobina\n * @param {Function} props.onDelete - Funzione chiamata quando si vuole eliminare una bobina\n * @param {Function} props.onViewHistory - Funzione chiamata quando si vuole visualizzare lo storico di una bobina\n * @param {Function} props.onQuickAdd - Funzione chiamata quando si vuole aggiungere rapidamente cavi a una bobina\n */import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const BobineFilterableTable=_ref=>{let{bobine=[],loading=false,onFilteredDataChange=null,onEdit=null,onDelete=null,onViewHistory=null,onQuickAdd=null}=_ref;const[filteredBobine,setFilteredBobine]=useState(bobine);// Aggiorna i dati filtrati quando cambiano le bobine\nuseEffect(()=>{setFilteredBobine(bobine);},[bobine]);// Notifica il componente padre quando cambiano i dati filtrati\nconst handleFilteredDataChange=data=>{setFilteredBobine(data);if(onFilteredDataChange){onFilteredDataChange(data);}};// Definizione delle colonne\nconst columns=[{field:'numero_bobina',headerName:'Numero',dataType:'text',headerStyle:{fontWeight:'bold'}},{field:'utility',headerName:'Utility',dataType:'text'},{field:'tipologia',headerName:'Tipologia',dataType:'text'},// n_conduttori field is now a spare field (kept in DB but hidden in UI)\n{field:'sezione',headerName:'Formazione',dataType:'text',align:'right',cellStyle:{textAlign:'right'}},{field:'metri_totali',headerName:'Metri Totali',dataType:'number',align:'right',cellStyle:{textAlign:'right'},renderCell:row=>row.metri_totali?row.metri_totali.toFixed(1):'0'},{field:'metri_residui',headerName:'Metri Residui',dataType:'number',align:'right',cellStyle:{textAlign:'right'},renderCell:row=>row.metri_residui?row.metri_residui.toFixed(1):'0'},{field:'stato_bobina',headerName:'Stato',dataType:'text',renderCell:row=>{return/*#__PURE__*/_jsx(Chip,{label:row.stato_bobina||'N/D',size:\"small\",color:getReelStateColor(row.stato_bobina),variant:\"outlined\"});}},{field:'ubicazione_bobina',headerName:'Ubicazione',dataType:'text'},{field:'fornitore',headerName:'Fornitore',dataType:'text'},{field:'n_DDT',headerName:'N° DDT',dataType:'text'},{field:'data_DDT',headerName:'Data DDT',dataType:'text'},{field:'actions',headerName:'Azioni',disableFilter:true,disableSort:true,align:'center',renderCell:row=>/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'center'},children:[onEdit&&/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>onEdit(row),title:\"Modifica bobina\",color:\"primary\",children:/*#__PURE__*/_jsx(EditIcon,{fontSize:\"small\"})}),onDelete&&/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>onDelete(row),title:\"Elimina bobina\",color:\"error\",disabled:row.stato_bobina!=='Disponibile'||row.metri_residui!==row.metri_totali,children:/*#__PURE__*/_jsx(DeleteIcon,{fontSize:\"small\"})}),onViewHistory&&/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>onViewHistory(row),title:\"Visualizza storico utilizzo\",color:\"info\",children:/*#__PURE__*/_jsx(HistoryIcon,{fontSize:\"small\"})}),onQuickAdd&&row.stato_bobina!=='Terminata'&&row.stato_bobina!=='Over'&&/*#__PURE__*/_jsx(Tooltip,{title:\"Aggiungi cavi a questa bobina\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>onQuickAdd(row),color:\"success\",children:/*#__PURE__*/_jsx(AddIcon,{fontSize:\"small\"})})})]})}];// Renderizza una riga personalizzata\nconst renderRow=(row,index)=>{// Determina il colore di sfondo in base allo stato\nlet bgColor='inherit';if(row.stato_bobina===REEL_STATES.DISPONIBILE)bgColor='rgba(76, 175, 80, 0.1)';else if(row.stato_bobina===REEL_STATES.IN_USO)bgColor='rgba(255, 152, 0, 0.1)';else if(row.stato_bobina===REEL_STATES.TERMINATA)bgColor='rgba(255, 152, 0, 0.2)';else if(row.stato_bobina===REEL_STATES.OVER)bgColor='rgba(244, 67, 54, 0.1)';return/*#__PURE__*/_jsx(TableRow,{sx:{backgroundColor:bgColor,'&:hover':{backgroundColor:'rgba(0, 0, 0, 0.04)'}},children:columns.map(column=>/*#__PURE__*/_jsx(TableCell,{align:column.align||'left',sx:column.cellStyle,children:column.renderCell?column.renderCell(row):row[column.field]},column.field))},index);};// Calcola le statistiche\nconst calculateStats=()=>{if(!filteredBobine.length)return null;const totalBobine=filteredBobine.length;const disponibili=filteredBobine.filter(b=>b.stato_bobina===REEL_STATES.DISPONIBILE).length;const inUso=filteredBobine.filter(b=>b.stato_bobina===REEL_STATES.IN_USO).length;const terminate=filteredBobine.filter(b=>b.stato_bobina===REEL_STATES.TERMINATA).length;const over=filteredBobine.filter(b=>b.stato_bobina===REEL_STATES.OVER).length;const metriTotali=filteredBobine.reduce((sum,b)=>sum+(b.metri_totali||0),0);const metriResidui=filteredBobine.reduce((sum,b)=>sum+(b.metri_residui||0),0);const metriUtilizzati=metriTotali-metriResidui;const percentualeUtilizzo=metriTotali?Math.round(metriUtilizzati/metriTotali*100):0;return{totalBobine,disponibili,inUso,terminate,over,metriTotali,metriResidui,metriUtilizzati,percentualeUtilizzo};};const stats=calculateStats();return/*#__PURE__*/_jsxs(Box,{children:[stats&&/*#__PURE__*/_jsxs(Box,{sx:{mb:3,p:2,bgcolor:'background.paper',borderRadius:1,boxShadow:1},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,children:[\"Statistiche (\",filteredBobine.length,\" bobine visualizzate)\"]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexWrap:'wrap',gap:3},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Utilizzo\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[stats.percentualeUtilizzo,\"%\"]})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Disponibili\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"success.main\",children:stats.disponibili})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"In uso\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"warning.main\",children:stats.inUso})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Terminate\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"warning.main\",children:stats.terminate})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Over\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"error.main\",children:stats.over})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Metri totali\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[stats.metriTotali.toFixed(1),\" m\"]})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Metri residui\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[stats.metriResidui.toFixed(1),\" m\"]})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Metri utilizzati\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[stats.metriUtilizzati.toFixed(1),\" m\"]})]})]})]}),/*#__PURE__*/_jsx(FilterableTable,{data:bobine,columns:columns,onFilteredDataChange:handleFilteredDataChange,loading:loading,emptyMessage:\"Nessuna bobina disponibile\",renderRow:renderRow})]});};export default BobineFilterableTable;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Chip", "TableRow", "TableCell", "IconButton", "<PERSON><PERSON><PERSON>", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "Add", "AddIcon", "FilterableTable", "REEL_STATES", "getReelStateColor", "jsx", "_jsx", "jsxs", "_jsxs", "BobineFilterableTable", "_ref", "bobine", "loading", "onFilteredDataChange", "onEdit", "onDelete", "onViewHistory", "onQuickAdd", "filteredBobine", "setFilteredBobine", "handleFilteredDataChange", "data", "columns", "field", "headerName", "dataType", "headerStyle", "fontWeight", "align", "cellStyle", "textAlign", "renderCell", "row", "metri_totali", "toFixed", "metri_residui", "label", "stato_bobina", "size", "color", "variant", "disableFilter", "disableSort", "sx", "display", "justifyContent", "children", "onClick", "title", "fontSize", "disabled", "renderRow", "index", "bgColor", "DISPONIBILE", "IN_USO", "TERMINATA", "OVER", "backgroundColor", "map", "column", "calculateStats", "length", "totalBobine", "disponibili", "filter", "b", "inUso", "terminate", "over", "metriTotali", "reduce", "sum", "metriResidui", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "percentuale<PERSON><PERSON><PERSON><PERSON>", "Math", "round", "stats", "mb", "p", "bgcolor", "borderRadius", "boxShadow", "gutterBottom", "flexWrap", "gap", "emptyMessage"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/BobineFilterableTable.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, IconButton, Tooltip } from '@mui/material';\nimport { Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon, Add as AddIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport { REEL_STATES, getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Componente per visualizzare la lista delle bobine con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.bobine - Lista delle bobine da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {Function} props.onEdit - Funzione chiamata quando si vuole modificare una bobina\n * @param {Function} props.onDelete - Funzione chiamata quando si vuole eliminare una bobina\n * @param {Function} props.onViewHistory - Funzione chiamata quando si vuole visualizzare lo storico di una bobina\n * @param {Function} props.onQuickAdd - Funzione chiamata quando si vuole aggiungere rapidamente cavi a una bobina\n */\nconst BobineFilterableTable = ({\n  bobine = [],\n  loading = false,\n  onFilteredDataChange = null,\n  onEdit = null,\n  onDelete = null,\n  onViewHistory = null,\n  onQuickAdd = null\n}) => {\n  const [filteredBobine, setFilteredBobine] = useState(bobine);\n\n  // Aggiorna i dati filtrati quando cambiano le bobine\n  useEffect(() => {\n    setFilteredBobine(bobine);\n  }, [bobine]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = (data) => {\n    setFilteredBobine(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Definizione delle colonne\n  const columns = [\n    {\n      field: 'numero_bobina',\n      headerName: 'Numero',\n      dataType: 'text',\n      headerStyle: { fontWeight: 'bold' }\n    },\n    {\n      field: 'utility',\n      headerName: 'Utility',\n      dataType: 'text'\n    },\n    {\n      field: 'tipologia',\n      headerName: 'Tipologia',\n      dataType: 'text'\n    },\n    // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n    {\n      field: 'sezione',\n      headerName: 'Formazione',\n      dataType: 'text',\n      align: 'right',\n      cellStyle: { textAlign: 'right' }\n    },\n    {\n      field: 'metri_totali',\n      headerName: 'Metri Totali',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metri_totali ? row.metri_totali.toFixed(1) : '0'\n    },\n    {\n      field: 'metri_residui',\n      headerName: 'Metri Residui',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metri_residui ? row.metri_residui.toFixed(1) : '0'\n    },\n    {\n      field: 'stato_bobina',\n      headerName: 'Stato',\n      dataType: 'text',\n      renderCell: (row) => {\n        return (\n          <Chip\n            label={row.stato_bobina || 'N/D'}\n            size=\"small\"\n            color={getReelStateColor(row.stato_bobina)}\n            variant=\"outlined\"\n          />\n        );\n      }\n    },\n    {\n      field: 'ubicazione_bobina',\n      headerName: 'Ubicazione',\n      dataType: 'text'\n    },\n    {\n      field: 'fornitore',\n      headerName: 'Fornitore',\n      dataType: 'text'\n    },\n    {\n      field: 'n_DDT',\n      headerName: 'N° DDT',\n      dataType: 'text'\n    },\n    {\n      field: 'data_DDT',\n      headerName: 'Data DDT',\n      dataType: 'text'\n    },\n    {\n      field: 'actions',\n      headerName: 'Azioni',\n      disableFilter: true,\n      disableSort: true,\n      align: 'center',\n      renderCell: (row) => (\n        <Box sx={{ display: 'flex', justifyContent: 'center' }}>\n          {onEdit && (\n            <IconButton\n              size=\"small\"\n              onClick={() => onEdit(row)}\n              title=\"Modifica bobina\"\n              color=\"primary\"\n            >\n              <EditIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n          {onDelete && (\n            <IconButton\n              size=\"small\"\n              onClick={() => onDelete(row)}\n              title=\"Elimina bobina\"\n              color=\"error\"\n              disabled={row.stato_bobina !== 'Disponibile' || row.metri_residui !== row.metri_totali}\n            >\n              <DeleteIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n          {onViewHistory && (\n            <IconButton\n              size=\"small\"\n              onClick={() => onViewHistory(row)}\n              title=\"Visualizza storico utilizzo\"\n              color=\"info\"\n            >\n              <HistoryIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n          {onQuickAdd && row.stato_bobina !== 'Terminata' && row.stato_bobina !== 'Over' && (\n            <Tooltip title=\"Aggiungi cavi a questa bobina\">\n              <IconButton\n                size=\"small\"\n                onClick={() => onQuickAdd(row)}\n                color=\"success\"\n              >\n                <AddIcon fontSize=\"small\" />\n              </IconButton>\n            </Tooltip>\n          )}\n        </Box>\n      )\n    }\n  ];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    if (row.stato_bobina === REEL_STATES.DISPONIBILE) bgColor = 'rgba(76, 175, 80, 0.1)';\n    else if (row.stato_bobina === REEL_STATES.IN_USO) bgColor = 'rgba(255, 152, 0, 0.1)';\n    else if (row.stato_bobina === REEL_STATES.TERMINATA) bgColor = 'rgba(255, 152, 0, 0.2)';\n    else if (row.stato_bobina === REEL_STATES.OVER) bgColor = 'rgba(244, 67, 54, 0.1)';\n\n    return (\n      <TableRow\n        key={index}\n        sx={{\n          backgroundColor: bgColor,\n          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            align={column.align || 'left'}\n            sx={column.cellStyle}\n          >\n            {column.renderCell ? column.renderCell(row) : row[column.field]}\n          </TableCell>\n        ))}\n      </TableRow>\n    );\n  };\n\n  // Calcola le statistiche\n  const calculateStats = () => {\n    if (!filteredBobine.length) return null;\n\n    const totalBobine = filteredBobine.length;\n    const disponibili = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.DISPONIBILE).length;\n    const inUso = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.IN_USO).length;\n    const terminate = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.TERMINATA).length;\n    const over = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.OVER).length;\n\n    const metriTotali = filteredBobine.reduce((sum, b) => sum + (b.metri_totali || 0), 0);\n    const metriResidui = filteredBobine.reduce((sum, b) => sum + (b.metri_residui || 0), 0);\n    const metriUtilizzati = metriTotali - metriResidui;\n\n    const percentualeUtilizzo = metriTotali ? Math.round((metriUtilizzati / metriTotali) * 100) : 0;\n\n    return {\n      totalBobine,\n      disponibili,\n      inUso,\n      terminate,\n      over,\n      metriTotali,\n      metriResidui,\n      metriUtilizzati,\n      percentualeUtilizzo\n    };\n  };\n\n  const stats = calculateStats();\n\n  return (\n    <Box>\n      {stats && (\n        <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Statistiche ({filteredBobine.length} bobine visualizzate)\n          </Typography>\n          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Utilizzo</Typography>\n              <Typography variant=\"h6\">{stats.percentualeUtilizzo}%</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Disponibili</Typography>\n              <Typography variant=\"h6\" color=\"success.main\">{stats.disponibili}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">In uso</Typography>\n              <Typography variant=\"h6\" color=\"warning.main\">{stats.inUso}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Terminate</Typography>\n              <Typography variant=\"h6\" color=\"warning.main\">{stats.terminate}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Over</Typography>\n              <Typography variant=\"h6\" color=\"error.main\">{stats.over}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri totali</Typography>\n              <Typography variant=\"h6\">{stats.metriTotali.toFixed(1)} m</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri residui</Typography>\n              <Typography variant=\"h6\">{stats.metriResidui.toFixed(1)} m</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri utilizzati</Typography>\n              <Typography variant=\"h6\">{stats.metriUtilizzati.toFixed(1)} m</Typography>\n            </Box>\n          </Box>\n        </Box>\n      )}\n\n      <FilterableTable\n        data={bobine}\n        columns={columns}\n        onFilteredDataChange={handleFilteredDataChange}\n        loading={loading}\n        emptyMessage=\"Nessuna bobina disponibile\"\n        renderRow={renderRow}\n      />\n    </Box>\n  );\n};\n\nexport default BobineFilterableTable;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,GAAG,CAAEC,UAAU,CAAEC,IAAI,CAAEC,QAAQ,CAAEC,SAAS,CAAEC,UAAU,CAAEC,OAAO,KAAQ,eAAe,CAC/F,OAASC,IAAI,GAAI,CAAAC,QAAQ,CAAEC,MAAM,GAAI,CAAAC,UAAU,CAAEC,OAAO,GAAI,CAAAC,WAAW,CAAEC,GAAG,GAAI,CAAAC,OAAO,KAAQ,qBAAqB,CACpH,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,OAASC,WAAW,CAAEC,iBAAiB,KAAQ,wBAAwB,CAEvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAXA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAYA,KAAM,CAAAC,qBAAqB,CAAGC,IAAA,EAQxB,IARyB,CAC7BC,MAAM,CAAG,EAAE,CACXC,OAAO,CAAG,KAAK,CACfC,oBAAoB,CAAG,IAAI,CAC3BC,MAAM,CAAG,IAAI,CACbC,QAAQ,CAAG,IAAI,CACfC,aAAa,CAAG,IAAI,CACpBC,UAAU,CAAG,IACf,CAAC,CAAAP,IAAA,CACC,KAAM,CAACQ,cAAc,CAAEC,iBAAiB,CAAC,CAAGlC,QAAQ,CAAC0B,MAAM,CAAC,CAE5D;AACAzB,SAAS,CAAC,IAAM,CACdiC,iBAAiB,CAACR,MAAM,CAAC,CAC3B,CAAC,CAAE,CAACA,MAAM,CAAC,CAAC,CAEZ;AACA,KAAM,CAAAS,wBAAwB,CAAIC,IAAI,EAAK,CACzCF,iBAAiB,CAACE,IAAI,CAAC,CACvB,GAAIR,oBAAoB,CAAE,CACxBA,oBAAoB,CAACQ,IAAI,CAAC,CAC5B,CACF,CAAC,CAED;AACA,KAAM,CAAAC,OAAO,CAAG,CACd,CACEC,KAAK,CAAE,eAAe,CACtBC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,MAAM,CAChBC,WAAW,CAAE,CAAEC,UAAU,CAAE,MAAO,CACpC,CAAC,CACD,CACEJ,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,SAAS,CACrBC,QAAQ,CAAE,MACZ,CAAC,CACD,CACEF,KAAK,CAAE,WAAW,CAClBC,UAAU,CAAE,WAAW,CACvBC,QAAQ,CAAE,MACZ,CAAC,CACD;AACA,CACEF,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,YAAY,CACxBC,QAAQ,CAAE,MAAM,CAChBG,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAClC,CAAC,CACD,CACEP,KAAK,CAAE,cAAc,CACrBC,UAAU,CAAE,cAAc,CAC1BC,QAAQ,CAAE,QAAQ,CAClBG,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAAC,CACjCC,UAAU,CAAGC,GAAG,EAAKA,GAAG,CAACC,YAAY,CAAGD,GAAG,CAACC,YAAY,CAACC,OAAO,CAAC,CAAC,CAAC,CAAG,GACxE,CAAC,CACD,CACEX,KAAK,CAAE,eAAe,CACtBC,UAAU,CAAE,eAAe,CAC3BC,QAAQ,CAAE,QAAQ,CAClBG,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAAC,CACjCC,UAAU,CAAGC,GAAG,EAAKA,GAAG,CAACG,aAAa,CAAGH,GAAG,CAACG,aAAa,CAACD,OAAO,CAAC,CAAC,CAAC,CAAG,GAC1E,CAAC,CACD,CACEX,KAAK,CAAE,cAAc,CACrBC,UAAU,CAAE,OAAO,CACnBC,QAAQ,CAAE,MAAM,CAChBM,UAAU,CAAGC,GAAG,EAAK,CACnB,mBACE1B,IAAA,CAACjB,IAAI,EACH+C,KAAK,CAAEJ,GAAG,CAACK,YAAY,EAAI,KAAM,CACjCC,IAAI,CAAC,OAAO,CACZC,KAAK,CAAEnC,iBAAiB,CAAC4B,GAAG,CAACK,YAAY,CAAE,CAC3CG,OAAO,CAAC,UAAU,CACnB,CAAC,CAEN,CACF,CAAC,CACD,CACEjB,KAAK,CAAE,mBAAmB,CAC1BC,UAAU,CAAE,YAAY,CACxBC,QAAQ,CAAE,MACZ,CAAC,CACD,CACEF,KAAK,CAAE,WAAW,CAClBC,UAAU,CAAE,WAAW,CACvBC,QAAQ,CAAE,MACZ,CAAC,CACD,CACEF,KAAK,CAAE,OAAO,CACdC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,MACZ,CAAC,CACD,CACEF,KAAK,CAAE,UAAU,CACjBC,UAAU,CAAE,UAAU,CACtBC,QAAQ,CAAE,MACZ,CAAC,CACD,CACEF,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,QAAQ,CACpBiB,aAAa,CAAE,IAAI,CACnBC,WAAW,CAAE,IAAI,CACjBd,KAAK,CAAE,QAAQ,CACfG,UAAU,CAAGC,GAAG,eACdxB,KAAA,CAACrB,GAAG,EAACwD,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAC,QAAA,EACpDhC,MAAM,eACLR,IAAA,CAACd,UAAU,EACT8C,IAAI,CAAC,OAAO,CACZS,OAAO,CAAEA,CAAA,GAAMjC,MAAM,CAACkB,GAAG,CAAE,CAC3BgB,KAAK,CAAC,iBAAiB,CACvBT,KAAK,CAAC,SAAS,CAAAO,QAAA,cAEfxC,IAAA,CAACX,QAAQ,EAACsD,QAAQ,CAAC,OAAO,CAAE,CAAC,CACnB,CACb,CACAlC,QAAQ,eACPT,IAAA,CAACd,UAAU,EACT8C,IAAI,CAAC,OAAO,CACZS,OAAO,CAAEA,CAAA,GAAMhC,QAAQ,CAACiB,GAAG,CAAE,CAC7BgB,KAAK,CAAC,gBAAgB,CACtBT,KAAK,CAAC,OAAO,CACbW,QAAQ,CAAElB,GAAG,CAACK,YAAY,GAAK,aAAa,EAAIL,GAAG,CAACG,aAAa,GAAKH,GAAG,CAACC,YAAa,CAAAa,QAAA,cAEvFxC,IAAA,CAACT,UAAU,EAACoD,QAAQ,CAAC,OAAO,CAAE,CAAC,CACrB,CACb,CACAjC,aAAa,eACZV,IAAA,CAACd,UAAU,EACT8C,IAAI,CAAC,OAAO,CACZS,OAAO,CAAEA,CAAA,GAAM/B,aAAa,CAACgB,GAAG,CAAE,CAClCgB,KAAK,CAAC,6BAA6B,CACnCT,KAAK,CAAC,MAAM,CAAAO,QAAA,cAEZxC,IAAA,CAACP,WAAW,EAACkD,QAAQ,CAAC,OAAO,CAAE,CAAC,CACtB,CACb,CACAhC,UAAU,EAAIe,GAAG,CAACK,YAAY,GAAK,WAAW,EAAIL,GAAG,CAACK,YAAY,GAAK,MAAM,eAC5E/B,IAAA,CAACb,OAAO,EAACuD,KAAK,CAAC,+BAA+B,CAAAF,QAAA,cAC5CxC,IAAA,CAACd,UAAU,EACT8C,IAAI,CAAC,OAAO,CACZS,OAAO,CAAEA,CAAA,GAAM9B,UAAU,CAACe,GAAG,CAAE,CAC/BO,KAAK,CAAC,SAAS,CAAAO,QAAA,cAEfxC,IAAA,CAACL,OAAO,EAACgD,QAAQ,CAAC,OAAO,CAAE,CAAC,CAClB,CAAC,CACN,CACV,EACE,CAET,CAAC,CACF,CAED;AACA,KAAM,CAAAE,SAAS,CAAGA,CAACnB,GAAG,CAAEoB,KAAK,GAAK,CAChC;AACA,GAAI,CAAAC,OAAO,CAAG,SAAS,CACvB,GAAIrB,GAAG,CAACK,YAAY,GAAKlC,WAAW,CAACmD,WAAW,CAAED,OAAO,CAAG,wBAAwB,CAAC,IAChF,IAAIrB,GAAG,CAACK,YAAY,GAAKlC,WAAW,CAACoD,MAAM,CAAEF,OAAO,CAAG,wBAAwB,CAAC,IAChF,IAAIrB,GAAG,CAACK,YAAY,GAAKlC,WAAW,CAACqD,SAAS,CAAEH,OAAO,CAAG,wBAAwB,CAAC,IACnF,IAAIrB,GAAG,CAACK,YAAY,GAAKlC,WAAW,CAACsD,IAAI,CAAEJ,OAAO,CAAG,wBAAwB,CAElF,mBACE/C,IAAA,CAAChB,QAAQ,EAEPqD,EAAE,CAAE,CACFe,eAAe,CAAEL,OAAO,CACxB,SAAS,CAAE,CAAEK,eAAe,CAAE,qBAAsB,CACtD,CAAE,CAAAZ,QAAA,CAEDxB,OAAO,CAACqC,GAAG,CAAEC,MAAM,eAClBtD,IAAA,CAACf,SAAS,EAERqC,KAAK,CAAEgC,MAAM,CAAChC,KAAK,EAAI,MAAO,CAC9Be,EAAE,CAAEiB,MAAM,CAAC/B,SAAU,CAAAiB,QAAA,CAEpBc,MAAM,CAAC7B,UAAU,CAAG6B,MAAM,CAAC7B,UAAU,CAACC,GAAG,CAAC,CAAGA,GAAG,CAAC4B,MAAM,CAACrC,KAAK,CAAC,EAJ1DqC,MAAM,CAACrC,KAKH,CACZ,CAAC,EAdG6B,KAeG,CAAC,CAEf,CAAC,CAED;AACA,KAAM,CAAAS,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAI,CAAC3C,cAAc,CAAC4C,MAAM,CAAE,MAAO,KAAI,CAEvC,KAAM,CAAAC,WAAW,CAAG7C,cAAc,CAAC4C,MAAM,CACzC,KAAM,CAAAE,WAAW,CAAG9C,cAAc,CAAC+C,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC7B,YAAY,GAAKlC,WAAW,CAACmD,WAAW,CAAC,CAACQ,MAAM,CACjG,KAAM,CAAAK,KAAK,CAAGjD,cAAc,CAAC+C,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC7B,YAAY,GAAKlC,WAAW,CAACoD,MAAM,CAAC,CAACO,MAAM,CACtF,KAAM,CAAAM,SAAS,CAAGlD,cAAc,CAAC+C,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC7B,YAAY,GAAKlC,WAAW,CAACqD,SAAS,CAAC,CAACM,MAAM,CAC7F,KAAM,CAAAO,IAAI,CAAGnD,cAAc,CAAC+C,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC7B,YAAY,GAAKlC,WAAW,CAACsD,IAAI,CAAC,CAACK,MAAM,CAEnF,KAAM,CAAAQ,WAAW,CAAGpD,cAAc,CAACqD,MAAM,CAAC,CAACC,GAAG,CAAEN,CAAC,GAAKM,GAAG,EAAIN,CAAC,CAACjC,YAAY,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,CACrF,KAAM,CAAAwC,YAAY,CAAGvD,cAAc,CAACqD,MAAM,CAAC,CAACC,GAAG,CAAEN,CAAC,GAAKM,GAAG,EAAIN,CAAC,CAAC/B,aAAa,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,CACvF,KAAM,CAAAuC,eAAe,CAAGJ,WAAW,CAAGG,YAAY,CAElD,KAAM,CAAAE,mBAAmB,CAAGL,WAAW,CAAGM,IAAI,CAACC,KAAK,CAAEH,eAAe,CAAGJ,WAAW,CAAI,GAAG,CAAC,CAAG,CAAC,CAE/F,MAAO,CACLP,WAAW,CACXC,WAAW,CACXG,KAAK,CACLC,SAAS,CACTC,IAAI,CACJC,WAAW,CACXG,YAAY,CACZC,eAAe,CACfC,mBACF,CAAC,CACH,CAAC,CAED,KAAM,CAAAG,KAAK,CAAGjB,cAAc,CAAC,CAAC,CAE9B,mBACErD,KAAA,CAACrB,GAAG,EAAA2D,QAAA,EACDgC,KAAK,eACJtE,KAAA,CAACrB,GAAG,EAACwD,EAAE,CAAE,CAAEoC,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,OAAO,CAAE,kBAAkB,CAAEC,YAAY,CAAE,CAAC,CAAEC,SAAS,CAAE,CAAE,CAAE,CAAArC,QAAA,eACnFtC,KAAA,CAACpB,UAAU,EAACoD,OAAO,CAAC,WAAW,CAAC4C,YAAY,MAAAtC,QAAA,EAAC,eAC9B,CAAC5B,cAAc,CAAC4C,MAAM,CAAC,uBACtC,EAAY,CAAC,cACbtD,KAAA,CAACrB,GAAG,EAACwD,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEyC,QAAQ,CAAE,MAAM,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAxC,QAAA,eACrDtC,KAAA,CAACrB,GAAG,EAAA2D,QAAA,eACFxC,IAAA,CAAClB,UAAU,EAACoD,OAAO,CAAC,OAAO,CAACD,KAAK,CAAC,gBAAgB,CAAAO,QAAA,CAAC,UAAQ,CAAY,CAAC,cACxEtC,KAAA,CAACpB,UAAU,EAACoD,OAAO,CAAC,IAAI,CAAAM,QAAA,EAAEgC,KAAK,CAACH,mBAAmB,CAAC,GAAC,EAAY,CAAC,EAC/D,CAAC,cACNnE,KAAA,CAACrB,GAAG,EAAA2D,QAAA,eACFxC,IAAA,CAAClB,UAAU,EAACoD,OAAO,CAAC,OAAO,CAACD,KAAK,CAAC,gBAAgB,CAAAO,QAAA,CAAC,aAAW,CAAY,CAAC,cAC3ExC,IAAA,CAAClB,UAAU,EAACoD,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,cAAc,CAAAO,QAAA,CAAEgC,KAAK,CAACd,WAAW,CAAa,CAAC,EAC3E,CAAC,cACNxD,KAAA,CAACrB,GAAG,EAAA2D,QAAA,eACFxC,IAAA,CAAClB,UAAU,EAACoD,OAAO,CAAC,OAAO,CAACD,KAAK,CAAC,gBAAgB,CAAAO,QAAA,CAAC,QAAM,CAAY,CAAC,cACtExC,IAAA,CAAClB,UAAU,EAACoD,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,cAAc,CAAAO,QAAA,CAAEgC,KAAK,CAACX,KAAK,CAAa,CAAC,EACrE,CAAC,cACN3D,KAAA,CAACrB,GAAG,EAAA2D,QAAA,eACFxC,IAAA,CAAClB,UAAU,EAACoD,OAAO,CAAC,OAAO,CAACD,KAAK,CAAC,gBAAgB,CAAAO,QAAA,CAAC,WAAS,CAAY,CAAC,cACzExC,IAAA,CAAClB,UAAU,EAACoD,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,cAAc,CAAAO,QAAA,CAAEgC,KAAK,CAACV,SAAS,CAAa,CAAC,EACzE,CAAC,cACN5D,KAAA,CAACrB,GAAG,EAAA2D,QAAA,eACFxC,IAAA,CAAClB,UAAU,EAACoD,OAAO,CAAC,OAAO,CAACD,KAAK,CAAC,gBAAgB,CAAAO,QAAA,CAAC,MAAI,CAAY,CAAC,cACpExC,IAAA,CAAClB,UAAU,EAACoD,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,YAAY,CAAAO,QAAA,CAAEgC,KAAK,CAACT,IAAI,CAAa,CAAC,EAClE,CAAC,cACN7D,KAAA,CAACrB,GAAG,EAAA2D,QAAA,eACFxC,IAAA,CAAClB,UAAU,EAACoD,OAAO,CAAC,OAAO,CAACD,KAAK,CAAC,gBAAgB,CAAAO,QAAA,CAAC,cAAY,CAAY,CAAC,cAC5EtC,KAAA,CAACpB,UAAU,EAACoD,OAAO,CAAC,IAAI,CAAAM,QAAA,EAAEgC,KAAK,CAACR,WAAW,CAACpC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAE,EAAY,CAAC,EACnE,CAAC,cACN1B,KAAA,CAACrB,GAAG,EAAA2D,QAAA,eACFxC,IAAA,CAAClB,UAAU,EAACoD,OAAO,CAAC,OAAO,CAACD,KAAK,CAAC,gBAAgB,CAAAO,QAAA,CAAC,eAAa,CAAY,CAAC,cAC7EtC,KAAA,CAACpB,UAAU,EAACoD,OAAO,CAAC,IAAI,CAAAM,QAAA,EAAEgC,KAAK,CAACL,YAAY,CAACvC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAE,EAAY,CAAC,EACpE,CAAC,cACN1B,KAAA,CAACrB,GAAG,EAAA2D,QAAA,eACFxC,IAAA,CAAClB,UAAU,EAACoD,OAAO,CAAC,OAAO,CAACD,KAAK,CAAC,gBAAgB,CAAAO,QAAA,CAAC,kBAAgB,CAAY,CAAC,cAChFtC,KAAA,CAACpB,UAAU,EAACoD,OAAO,CAAC,IAAI,CAAAM,QAAA,EAAEgC,KAAK,CAACJ,eAAe,CAACxC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAE,EAAY,CAAC,EACvE,CAAC,EACH,CAAC,EACH,CACN,cAED5B,IAAA,CAACJ,eAAe,EACdmB,IAAI,CAAEV,MAAO,CACbW,OAAO,CAAEA,OAAQ,CACjBT,oBAAoB,CAAEO,wBAAyB,CAC/CR,OAAO,CAAEA,OAAQ,CACjB2E,YAAY,CAAC,4BAA4B,CACzCpC,SAAS,CAAEA,SAAU,CACtB,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAA1C,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}