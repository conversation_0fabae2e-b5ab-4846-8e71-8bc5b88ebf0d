{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport Tab from '@mui/material/Tab';\nimport Tabs, { tabsClasses } from '@mui/material/Tabs';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { TimeIcon, DateRangeIcon } from \"../icons/index.js\";\nimport { usePickerTranslations } from \"../hooks/usePickerTranslations.js\";\nimport { getDateTimePickerTabsUtilityClass } from \"./dateTimePickerTabsClasses.js\";\nimport { isDatePickerView } from \"../internals/utils/date-utils.js\";\nimport { usePickerPrivateContext } from \"../internals/hooks/usePickerPrivateContext.js\";\nimport { usePickerContext } from \"../hooks/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst viewToTab = view => {\n  if (isDatePickerView(view)) {\n    return 'date';\n  }\n  return 'time';\n};\nconst tabToView = tab => {\n  if (tab === 'date') {\n    return 'day';\n  }\n  return 'hours';\n};\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getDateTimePickerTabsUtilityClass, classes);\n};\nconst DateTimePickerTabsRoot = styled(Tabs, {\n  name: 'MuiDateTimePickerTabs',\n  slot: 'Root'\n})(({\n  theme\n}) => ({\n  boxShadow: `0 -1px 0 0 inset ${(theme.vars || theme).palette.divider}`,\n  '&:last-child': {\n    boxShadow: `0 1px 0 0 inset ${(theme.vars || theme).palette.divider}`,\n    [`& .${tabsClasses.indicator}`]: {\n      bottom: 'auto',\n      top: 0\n    }\n  }\n}));\n\n/**\n * Demos:\n *\n * - [DateTimePicker](https://mui.com/x/react-date-pickers/date-time-picker/)\n * - [Custom slots and subcomponents](https://mui.com/x/react-date-pickers/custom-components/)\n *\n * API:\n *\n * - [DateTimePickerTabs API](https://mui.com/x/api/date-pickers/date-time-picker-tabs/)\n */\nconst DateTimePickerTabs = function DateTimePickerTabs(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiDateTimePickerTabs'\n  });\n  const {\n    dateIcon = /*#__PURE__*/_jsx(DateRangeIcon, {}),\n    timeIcon = /*#__PURE__*/_jsx(TimeIcon, {}),\n    hidden = typeof window === 'undefined' || window.innerHeight < 667,\n    className,\n    classes: classesProp,\n    sx\n  } = props;\n  const translations = usePickerTranslations();\n  const {\n    ownerState\n  } = usePickerPrivateContext();\n  const {\n    view,\n    setView\n  } = usePickerContext();\n  const classes = useUtilityClasses(classesProp);\n  const handleChange = (event, value) => {\n    setView(tabToView(value));\n  };\n  if (hidden) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(DateTimePickerTabsRoot, {\n    ownerState: ownerState,\n    variant: \"fullWidth\",\n    value: viewToTab(view),\n    onChange: handleChange,\n    className: clsx(className, classes.root),\n    sx: sx,\n    children: [/*#__PURE__*/_jsx(Tab, {\n      value: \"date\",\n      \"aria-label\": translations.dateTableLabel,\n      icon: /*#__PURE__*/_jsx(React.Fragment, {\n        children: dateIcon\n      })\n    }), /*#__PURE__*/_jsx(Tab, {\n      value: \"time\",\n      \"aria-label\": translations.timeTableLabel,\n      icon: /*#__PURE__*/_jsx(React.Fragment, {\n        children: timeIcon\n      })\n    })]\n  });\n};\nprocess.env.NODE_ENV !== \"production\" ? DateTimePickerTabs.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * Date tab icon.\n   * @default DateRange\n   */\n  dateIcon: PropTypes.node,\n  /**\n   * Toggles visibility of the tabs allowing view switching.\n   * @default `window.innerHeight < 667` for `DesktopDateTimePicker` and `MobileDateTimePicker`, `displayStaticWrapperAs === 'desktop'` for `StaticDateTimePicker`\n   */\n  hidden: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Time tab icon.\n   * @default Time\n   */\n  timeIcon: PropTypes.node\n} : void 0;\nexport { DateTimePickerTabs };", "map": {"version": 3, "names": ["React", "clsx", "PropTypes", "Tab", "Tabs", "tabsClasses", "styled", "useThemeProps", "composeClasses", "TimeIcon", "DateRangeIcon", "usePickerTranslations", "getDateTimePickerTabsUtilityClass", "isDatePickerView", "usePickerPrivateContext", "usePickerContext", "jsx", "_jsx", "jsxs", "_jsxs", "viewToTab", "view", "tabToView", "tab", "useUtilityClasses", "classes", "slots", "root", "DateTimePickerTabsRoot", "name", "slot", "theme", "boxShadow", "vars", "palette", "divider", "indicator", "bottom", "top", "DateTimePickerTabs", "inProps", "props", "dateIcon", "timeIcon", "hidden", "window", "innerHeight", "className", "classesProp", "sx", "translations", "ownerState", "<PERSON><PERSON><PERSON><PERSON>", "handleChange", "event", "value", "variant", "onChange", "children", "dateTableLabel", "icon", "Fragment", "timeTable<PERSON>abel", "process", "env", "NODE_ENV", "propTypes", "object", "string", "node", "bool", "oneOfType", "arrayOf", "func"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/DateTimePicker/DateTimePickerTabs.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport Tab from '@mui/material/Tab';\nimport Tabs, { tabsClasses } from '@mui/material/Tabs';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { TimeIcon, DateRangeIcon } from \"../icons/index.js\";\nimport { usePickerTranslations } from \"../hooks/usePickerTranslations.js\";\nimport { getDateTimePickerTabsUtilityClass } from \"./dateTimePickerTabsClasses.js\";\nimport { isDatePickerView } from \"../internals/utils/date-utils.js\";\nimport { usePickerPrivateContext } from \"../internals/hooks/usePickerPrivateContext.js\";\nimport { usePickerContext } from \"../hooks/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst viewToTab = view => {\n  if (isDatePickerView(view)) {\n    return 'date';\n  }\n  return 'time';\n};\nconst tabToView = tab => {\n  if (tab === 'date') {\n    return 'day';\n  }\n  return 'hours';\n};\nconst useUtilityClasses = classes => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getDateTimePickerTabsUtilityClass, classes);\n};\nconst DateTimePickerTabsRoot = styled(Tabs, {\n  name: 'MuiDateTimePickerTabs',\n  slot: 'Root'\n})(({\n  theme\n}) => ({\n  boxShadow: `0 -1px 0 0 inset ${(theme.vars || theme).palette.divider}`,\n  '&:last-child': {\n    boxShadow: `0 1px 0 0 inset ${(theme.vars || theme).palette.divider}`,\n    [`& .${tabsClasses.indicator}`]: {\n      bottom: 'auto',\n      top: 0\n    }\n  }\n}));\n\n/**\n * Demos:\n *\n * - [DateTimePicker](https://mui.com/x/react-date-pickers/date-time-picker/)\n * - [Custom slots and subcomponents](https://mui.com/x/react-date-pickers/custom-components/)\n *\n * API:\n *\n * - [DateTimePickerTabs API](https://mui.com/x/api/date-pickers/date-time-picker-tabs/)\n */\nconst DateTimePickerTabs = function DateTimePickerTabs(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiDateTimePickerTabs'\n  });\n  const {\n    dateIcon = /*#__PURE__*/_jsx(DateRangeIcon, {}),\n    timeIcon = /*#__PURE__*/_jsx(TimeIcon, {}),\n    hidden = typeof window === 'undefined' || window.innerHeight < 667,\n    className,\n    classes: classesProp,\n    sx\n  } = props;\n  const translations = usePickerTranslations();\n  const {\n    ownerState\n  } = usePickerPrivateContext();\n  const {\n    view,\n    setView\n  } = usePickerContext();\n  const classes = useUtilityClasses(classesProp);\n  const handleChange = (event, value) => {\n    setView(tabToView(value));\n  };\n  if (hidden) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxs(DateTimePickerTabsRoot, {\n    ownerState: ownerState,\n    variant: \"fullWidth\",\n    value: viewToTab(view),\n    onChange: handleChange,\n    className: clsx(className, classes.root),\n    sx: sx,\n    children: [/*#__PURE__*/_jsx(Tab, {\n      value: \"date\",\n      \"aria-label\": translations.dateTableLabel,\n      icon: /*#__PURE__*/_jsx(React.Fragment, {\n        children: dateIcon\n      })\n    }), /*#__PURE__*/_jsx(Tab, {\n      value: \"time\",\n      \"aria-label\": translations.timeTableLabel,\n      icon: /*#__PURE__*/_jsx(React.Fragment, {\n        children: timeIcon\n      })\n    })]\n  });\n};\nprocess.env.NODE_ENV !== \"production\" ? DateTimePickerTabs.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * Date tab icon.\n   * @default DateRange\n   */\n  dateIcon: PropTypes.node,\n  /**\n   * Toggles visibility of the tabs allowing view switching.\n   * @default `window.innerHeight < 667` for `DesktopDateTimePicker` and `MobileDateTimePicker`, `displayStaticWrapperAs === 'desktop'` for `StaticDateTimePicker`\n   */\n  hidden: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Time tab icon.\n   * @default Time\n   */\n  timeIcon: PropTypes.node\n} : void 0;\nexport { DateTimePickerTabs };"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,IAAI,IAAIC,WAAW,QAAQ,oBAAoB;AACtD,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,QAAQ,EAAEC,aAAa,QAAQ,mBAAmB;AAC3D,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,SAASC,iCAAiC,QAAQ,gCAAgC;AAClF,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,gBAAgB,QAAQ,mBAAmB;AACpD,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,SAAS,GAAGC,IAAI,IAAI;EACxB,IAAIR,gBAAgB,CAACQ,IAAI,CAAC,EAAE;IAC1B,OAAO,MAAM;EACf;EACA,OAAO,MAAM;AACf,CAAC;AACD,MAAMC,SAAS,GAAGC,GAAG,IAAI;EACvB,IAAIA,GAAG,KAAK,MAAM,EAAE;IAClB,OAAO,KAAK;EACd;EACA,OAAO,OAAO;AAChB,CAAC;AACD,MAAMC,iBAAiB,GAAGC,OAAO,IAAI;EACnC,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOnB,cAAc,CAACkB,KAAK,EAAEd,iCAAiC,EAAEa,OAAO,CAAC;AAC1E,CAAC;AACD,MAAMG,sBAAsB,GAAGtB,MAAM,CAACF,IAAI,EAAE;EAC1CyB,IAAI,EAAE,uBAAuB;EAC7BC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC,CAAC;EACFC;AACF,CAAC,MAAM;EACLC,SAAS,EAAE,oBAAoB,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEG,OAAO,CAACC,OAAO,EAAE;EACtE,cAAc,EAAE;IACdH,SAAS,EAAE,mBAAmB,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEG,OAAO,CAACC,OAAO,EAAE;IACrE,CAAC,MAAM9B,WAAW,CAAC+B,SAAS,EAAE,GAAG;MAC/BC,MAAM,EAAE,MAAM;MACdC,GAAG,EAAE;IACP;EACF;AACF,CAAC,CAAC,CAAC;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,OAAO,EAAE;EAC9D,MAAMC,KAAK,GAAGlC,aAAa,CAAC;IAC1BkC,KAAK,EAAED,OAAO;IACdX,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJa,QAAQ,GAAG,aAAazB,IAAI,CAACP,aAAa,EAAE,CAAC,CAAC,CAAC;IAC/CiC,QAAQ,GAAG,aAAa1B,IAAI,CAACR,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC1CmC,MAAM,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,WAAW,GAAG,GAAG;IAClEC,SAAS;IACTtB,OAAO,EAAEuB,WAAW;IACpBC;EACF,CAAC,GAAGR,KAAK;EACT,MAAMS,YAAY,GAAGvC,qBAAqB,CAAC,CAAC;EAC5C,MAAM;IACJwC;EACF,CAAC,GAAGrC,uBAAuB,CAAC,CAAC;EAC7B,MAAM;IACJO,IAAI;IACJ+B;EACF,CAAC,GAAGrC,gBAAgB,CAAC,CAAC;EACtB,MAAMU,OAAO,GAAGD,iBAAiB,CAACwB,WAAW,CAAC;EAC9C,MAAMK,YAAY,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACrCH,OAAO,CAAC9B,SAAS,CAACiC,KAAK,CAAC,CAAC;EAC3B,CAAC;EACD,IAAIX,MAAM,EAAE;IACV,OAAO,IAAI;EACb;EACA,OAAO,aAAazB,KAAK,CAACS,sBAAsB,EAAE;IAChDuB,UAAU,EAAEA,UAAU;IACtBK,OAAO,EAAE,WAAW;IACpBD,KAAK,EAAEnC,SAAS,CAACC,IAAI,CAAC;IACtBoC,QAAQ,EAAEJ,YAAY;IACtBN,SAAS,EAAE9C,IAAI,CAAC8C,SAAS,EAAEtB,OAAO,CAACE,IAAI,CAAC;IACxCsB,EAAE,EAAEA,EAAE;IACNS,QAAQ,EAAE,CAAC,aAAazC,IAAI,CAACd,GAAG,EAAE;MAChCoD,KAAK,EAAE,MAAM;MACb,YAAY,EAAEL,YAAY,CAACS,cAAc;MACzCC,IAAI,EAAE,aAAa3C,IAAI,CAACjB,KAAK,CAAC6D,QAAQ,EAAE;QACtCH,QAAQ,EAAEhB;MACZ,CAAC;IACH,CAAC,CAAC,EAAE,aAAazB,IAAI,CAACd,GAAG,EAAE;MACzBoD,KAAK,EAAE,MAAM;MACb,YAAY,EAAEL,YAAY,CAACY,cAAc;MACzCF,IAAI,EAAE,aAAa3C,IAAI,CAACjB,KAAK,CAAC6D,QAAQ,EAAE;QACtCH,QAAQ,EAAEf;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACDoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1B,kBAAkB,CAAC2B,SAAS,GAAG;EACrE;EACA;EACA;EACA;EACA;AACF;AACA;EACEzC,OAAO,EAAEvB,SAAS,CAACiE,MAAM;EACzBpB,SAAS,EAAE7C,SAAS,CAACkE,MAAM;EAC3B;AACF;AACA;AACA;EACE1B,QAAQ,EAAExC,SAAS,CAACmE,IAAI;EACxB;AACF;AACA;AACA;EACEzB,MAAM,EAAE1C,SAAS,CAACoE,IAAI;EACtB;AACF;AACA;EACErB,EAAE,EAAE/C,SAAS,CAACqE,SAAS,CAAC,CAACrE,SAAS,CAACsE,OAAO,CAACtE,SAAS,CAACqE,SAAS,CAAC,CAACrE,SAAS,CAACuE,IAAI,EAAEvE,SAAS,CAACiE,MAAM,EAAEjE,SAAS,CAACoE,IAAI,CAAC,CAAC,CAAC,EAAEpE,SAAS,CAACuE,IAAI,EAAEvE,SAAS,CAACiE,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACExB,QAAQ,EAAEzC,SAAS,CAACmE;AACtB,CAAC,GAAG,KAAK,CAAC;AACV,SAAS9B,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}