{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'li għadda' 'fil-'p\",\n  yesterday: \"'Il-bieraħ fil-'p\",\n  today: \"'<PERSON>lum fil-'p\",\n  tomorrow: \"'G<PERSON><PERSON> fil-'p\",\n  nextWeek: \"eeee 'fil-'p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/mt/_lib/formatRelative.mjs"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"eeee 'li għadda' 'fil-'p\",\n  yesterday: \"'Il-bieraħ fil-'p\",\n  today: \"'Illum fil-'p\",\n  tomorrow: \"'G<PERSON><PERSON> fil-'p\",\n  nextWeek: \"eeee 'fil-'p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,0BAA0B;EACpCC,SAAS,EAAE,mBAAmB;EAC9BC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE,cAAc;EACxBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}