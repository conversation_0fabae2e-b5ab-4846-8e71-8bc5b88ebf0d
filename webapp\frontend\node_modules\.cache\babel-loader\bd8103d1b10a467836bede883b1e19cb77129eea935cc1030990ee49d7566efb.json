{"ast": null, "code": "import axios from'axios';import config from'../config';import axiosInstance from'./axiosConfig';const API_URL=config.API_URL;// Gestione degli errori di autenticazione\naxiosInstance.interceptors.response.use(response=>response,error=>{console.error('Errore nella risposta API:',error);if(error.response&&error.response.status===401){// Se la risposta è 401 Unauthorized, effettua il logout\nconsole.log('Errore 401 rilevato, rimozione token');localStorage.removeItem('token');// Pulisci eventuali selezioni di cantiere precedenti\nlocalStorage.removeItem('selectedCantiereId');localStorage.removeItem('selectedCantiereName');console.log('Rimossi dati cantiere precedenti dal localStorage');// Non reindirizzare automaticamente, lascia che sia il componente React a gestire il reindirizzamento\n// Questo evita loop di reindirizzamento\n}return Promise.reject(error);});const authService={// Login standard (admin o utente standard)\nlogin:async(credentials,loginType)=>{try{console.log(`Tentativo di login ${loginType} con API_URL: ${API_URL}`);if(loginType==='standard'){// Converti le credenziali nel formato richiesto da OAuth2\nconst formData=new FormData();formData.append('username',credentials.username);formData.append('password',credentials.password);console.log(`Invio richiesta POST a ${API_URL}/auth/login`);// Usa axios direttamente per il login perché richiede FormData\nconst response=await axios.post(`${API_URL}/auth/login`,formData,{headers:{'Content-Type':'multipart/form-data'}});console.log('Risposta ricevuta:',response);return response.data;}else if(loginType==='cantiere'){// Login cantiere\nconsole.log(`Invio richiesta POST a ${API_URL}/auth/login/cantiere`);const response=await axiosInstance.post('/auth/login/cantiere',{codice_univoco:credentials.codice_univoco,password:credentials.password});console.log('Risposta ricevuta:',response);// Salva l'ID e il nome del cantiere nel localStorage\nif(response.data.cantiere_id){console.log('Salvando ID cantiere nel localStorage:',response.data.cantiere_id);localStorage.setItem('selectedCantiereId',response.data.cantiere_id.toString());localStorage.setItem('selectedCantiereName',response.data.cantiere_name||`Cantiere ${response.data.cantiere_id}`);}else{console.warn('Risposta login cantiere non contiene cantiere_id:',response.data);}return response.data;}else{throw new Error('Tipo di login non valido');}}catch(error){console.error('Login error:',error);if(error.response){console.error('Dettagli errore:',error.response.status,error.response.data);// Miglioramento della gestione degli errori per mostrare messaggi più chiari\nif(error.response.status===401){throw{detail:error.response.data.detail||'Credenziali non valide. Verifica username e password.'};}else if(error.response.status===403){throw{detail:error.response.data.detail||'Accesso negato. Non hai i permessi necessari.'};}else{throw error.response.data;}}else if(error.request){console.error('Nessuna risposta ricevuta:',error.request);throw{detail:'Errore di connessione al server. Verifica che il backend sia in esecuzione.'};}else{console.error('Errore durante la configurazione della richiesta:',error.message);throw{detail:error.message};}}},// Verifica la validità del token\ncheckToken:async()=>{try{console.log('Verifica token in corso...');const response=await axiosInstance.post('/auth/test-token');console.log('Risposta verifica token:',response.data);// Controlla se l'utente è impersonato da un admin\n// Questo valore viene ora impostato dal backend nel token JWT\nconst isImpersonated=response.data.is_impersonated===true;// Se l'utente è impersonato, salva lo stato nel localStorage\nif(isImpersonated){console.log('Utente impersonato da admin, salvataggio stato nel localStorage');localStorage.setItem('isImpersonating','true');}else{// Altrimenti, assicurati che non ci sia uno stato di impersonificazione salvato\nlocalStorage.removeItem('isImpersonating');}// Costruisci l'oggetto utente con i dati dal token\nconst userData={id:response.data.user_id,username:response.data.username,role:response.data.role,isImpersonated:isImpersonated};// Se l'utente è un utente cantiere, aggiungi i dati del cantiere\nif(response.data.role==='cantieri_user'&&response.data.cantiere_id){userData.cantiere_id=response.data.cantiere_id;userData.cantiere_name=response.data.cantiere_name||`Cantiere ${response.data.cantiere_id}`;// Salva l'ID e il nome del cantiere nel localStorage\nlocalStorage.setItem('selectedCantiereId',response.data.cantiere_id.toString());localStorage.setItem('selectedCantiereName',userData.cantiere_name);console.log('Salvati dati cantiere nel localStorage durante checkToken:',{cantiere_id:response.data.cantiere_id,cantiere_name:userData.cantiere_name});}// Se l'utente è impersonato, aggiungi i dati dell'utente impersonato\nif(isImpersonated&&response.data.impersonated_id){// Salva i dati dell'utente impersonato nel localStorage\nconst impersonatedUserData={id:response.data.impersonated_id,username:response.data.impersonated_username,role:response.data.impersonated_role};localStorage.setItem('impersonatedUser',JSON.stringify(impersonatedUserData));}return userData;}catch(error){console.error('Check token error:',error);// Pulisci il localStorage per evitare loop\nlocalStorage.removeItem('token');localStorage.removeItem('isImpersonating');// Pulisci eventuali selezioni di cantiere precedenti\nlocalStorage.removeItem('selectedCantiereId');localStorage.removeItem('selectedCantiereName');console.log('Rimossi dati cantiere precedenti dal localStorage');throw error.response?error.response.data:error;}},// Impersona un altro utente (solo per admin)\nimpersonateUser:async userId=>{try{const response=await axiosInstance.post('/auth/impersonate',{user_id:userId});return response.data;}catch(error){console.error('Impersonate user error:',error);throw error.response?error.response.data:error;}}};export default authService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "interceptors", "response", "use", "error", "console", "status", "log", "localStorage", "removeItem", "Promise", "reject", "authService", "login", "credentials", "loginType", "formData", "FormData", "append", "username", "password", "post", "headers", "data", "codice_univoco", "cantiere_id", "setItem", "toString", "cantiere_name", "warn", "Error", "detail", "request", "message", "checkToken", "isImpersonated", "is_impersonated", "userData", "id", "user_id", "role", "impersonated_id", "impersonated<PERSON><PERSON><PERSON><PERSON>", "impersonated_username", "impersonated_role", "JSON", "stringify", "impersonate<PERSON><PERSON>", "userId"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/authService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\n// Gestione degli errori di autenticazione\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    console.error('Errore nella risposta API:', error);\r\n    if (error.response && error.response.status === 401) {\r\n      // Se la risposta è 401 Unauthorized, effettua il logout\r\n      console.log('Errore 401 rilevato, rimozione token');\r\n      localStorage.removeItem('token');\r\n\r\n      // Pulisci eventuali selezioni di cantiere precedenti\r\n      localStorage.removeItem('selectedCantiereId');\r\n      localStorage.removeItem('selectedCantiereName');\r\n      console.log('Rimossi dati cantiere precedenti dal localStorage');\r\n\r\n      // Non reindirizzare automaticamente, lascia che sia il componente React a gestire il reindirizzamento\r\n      // Questo evita loop di reindirizzamento\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nconst authService = {\r\n  // Login standard (admin o utente standard)\r\n  login: async (credentials, loginType) => {\r\n    try {\r\n      console.log(`Tentativo di login ${loginType} con API_URL: ${API_URL}`);\r\n\r\n      if (loginType === 'standard') {\r\n        // Converti le credenziali nel formato richiesto da OAuth2\r\n        const formData = new FormData();\r\n        formData.append('username', credentials.username);\r\n        formData.append('password', credentials.password);\r\n\r\n        console.log(`Invio richiesta POST a ${API_URL}/auth/login`);\r\n        // Usa axios direttamente per il login perché richiede FormData\r\n        const response = await axios.post(`${API_URL}/auth/login`, formData, {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data',\r\n          },\r\n        });\r\n        console.log('Risposta ricevuta:', response);\r\n        return response.data;\r\n      } else if (loginType === 'cantiere') {\r\n        // Login cantiere\r\n        console.log(`Invio richiesta POST a ${API_URL}/auth/login/cantiere`);\r\n        const response = await axiosInstance.post('/auth/login/cantiere', {\r\n          codice_univoco: credentials.codice_univoco,\r\n          password: credentials.password\r\n        });\r\n        console.log('Risposta ricevuta:', response);\r\n\r\n        // Salva l'ID e il nome del cantiere nel localStorage\r\n        if (response.data.cantiere_id) {\r\n          console.log('Salvando ID cantiere nel localStorage:', response.data.cantiere_id);\r\n          localStorage.setItem('selectedCantiereId', response.data.cantiere_id.toString());\r\n          localStorage.setItem('selectedCantiereName', response.data.cantiere_name || `Cantiere ${response.data.cantiere_id}`);\r\n        } else {\r\n          console.warn('Risposta login cantiere non contiene cantiere_id:', response.data);\r\n        }\r\n\r\n        return response.data;\r\n      } else {\r\n        throw new Error('Tipo di login non valido');\r\n      }\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      if (error.response) {\r\n        console.error('Dettagli errore:', error.response.status, error.response.data);\r\n        // Miglioramento della gestione degli errori per mostrare messaggi più chiari\r\n        if (error.response.status === 401) {\r\n          throw { detail: error.response.data.detail || 'Credenziali non valide. Verifica username e password.' };\r\n        } else if (error.response.status === 403) {\r\n          throw { detail: error.response.data.detail || 'Accesso negato. Non hai i permessi necessari.' };\r\n        } else {\r\n          throw error.response.data;\r\n        }\r\n      } else if (error.request) {\r\n        console.error('Nessuna risposta ricevuta:', error.request);\r\n        throw { detail: 'Errore di connessione al server. Verifica che il backend sia in esecuzione.' };\r\n      } else {\r\n        console.error('Errore durante la configurazione della richiesta:', error.message);\r\n        throw { detail: error.message };\r\n      }\r\n    }\r\n  },\r\n\r\n\r\n\r\n  // Verifica la validità del token\r\n  checkToken: async () => {\r\n    try {\r\n      console.log('Verifica token in corso...');\r\n      const response = await axiosInstance.post('/auth/test-token');\r\n      console.log('Risposta verifica token:', response.data);\r\n\r\n      // Controlla se l'utente è impersonato da un admin\r\n      // Questo valore viene ora impostato dal backend nel token JWT\r\n      const isImpersonated = response.data.is_impersonated === true;\r\n\r\n      // Se l'utente è impersonato, salva lo stato nel localStorage\r\n      if (isImpersonated) {\r\n        console.log('Utente impersonato da admin, salvataggio stato nel localStorage');\r\n        localStorage.setItem('isImpersonating', 'true');\r\n      } else {\r\n        // Altrimenti, assicurati che non ci sia uno stato di impersonificazione salvato\r\n        localStorage.removeItem('isImpersonating');\r\n      }\r\n\r\n      // Costruisci l'oggetto utente con i dati dal token\r\n      const userData = {\r\n        id: response.data.user_id,\r\n        username: response.data.username,\r\n        role: response.data.role,\r\n        isImpersonated: isImpersonated\r\n      };\r\n\r\n      // Se l'utente è un utente cantiere, aggiungi i dati del cantiere\r\n      if (response.data.role === 'cantieri_user' && response.data.cantiere_id) {\r\n        userData.cantiere_id = response.data.cantiere_id;\r\n        userData.cantiere_name = response.data.cantiere_name || `Cantiere ${response.data.cantiere_id}`;\r\n\r\n        // Salva l'ID e il nome del cantiere nel localStorage\r\n        localStorage.setItem('selectedCantiereId', response.data.cantiere_id.toString());\r\n        localStorage.setItem('selectedCantiereName', userData.cantiere_name);\r\n        console.log('Salvati dati cantiere nel localStorage durante checkToken:', {\r\n          cantiere_id: response.data.cantiere_id,\r\n          cantiere_name: userData.cantiere_name\r\n        });\r\n      }\r\n\r\n      // Se l'utente è impersonato, aggiungi i dati dell'utente impersonato\r\n      if (isImpersonated && response.data.impersonated_id) {\r\n        // Salva i dati dell'utente impersonato nel localStorage\r\n        const impersonatedUserData = {\r\n          id: response.data.impersonated_id,\r\n          username: response.data.impersonated_username,\r\n          role: response.data.impersonated_role\r\n        };\r\n\r\n        localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData));\r\n      }\r\n\r\n      return userData;\r\n    } catch (error) {\r\n      console.error('Check token error:', error);\r\n      // Pulisci il localStorage per evitare loop\r\n      localStorage.removeItem('token');\r\n      localStorage.removeItem('isImpersonating');\r\n\r\n      // Pulisci eventuali selezioni di cantiere precedenti\r\n      localStorage.removeItem('selectedCantiereId');\r\n      localStorage.removeItem('selectedCantiereName');\r\n      console.log('Rimossi dati cantiere precedenti dal localStorage');\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Impersona un altro utente (solo per admin)\r\n  impersonateUser: async (userId) => {\r\n    try {\r\n      const response = await axiosInstance.post('/auth/impersonate', {\r\n        user_id: userId\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Impersonate user error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default authService;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,WAAW,CAC9B,MAAO,CAAAC,aAAa,KAAM,eAAe,CAEzC,KAAM,CAAAC,OAAO,CAAGF,MAAM,CAACE,OAAO,CAE9B;AACAD,aAAa,CAACE,YAAY,CAACC,QAAQ,CAACC,GAAG,CACpCD,QAAQ,EAAKA,QAAQ,CACrBE,KAAK,EAAK,CACTC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,GAAIA,KAAK,CAACF,QAAQ,EAAIE,KAAK,CAACF,QAAQ,CAACI,MAAM,GAAK,GAAG,CAAE,CACnD;AACAD,OAAO,CAACE,GAAG,CAAC,sCAAsC,CAAC,CACnDC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC,CAEhC;AACAD,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC,CAC7CD,YAAY,CAACC,UAAU,CAAC,sBAAsB,CAAC,CAC/CJ,OAAO,CAACE,GAAG,CAAC,mDAAmD,CAAC,CAEhE;AACA;AACF,CACA,MAAO,CAAAG,OAAO,CAACC,MAAM,CAACP,KAAK,CAAC,CAC9B,CACF,CAAC,CAED,KAAM,CAAAQ,WAAW,CAAG,CAClB;AACAC,KAAK,CAAE,KAAAA,CAAOC,WAAW,CAAEC,SAAS,GAAK,CACvC,GAAI,CACFV,OAAO,CAACE,GAAG,CAAC,sBAAsBQ,SAAS,iBAAiBf,OAAO,EAAE,CAAC,CAEtE,GAAIe,SAAS,GAAK,UAAU,CAAE,CAC5B;AACA,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,CAAEJ,WAAW,CAACK,QAAQ,CAAC,CACjDH,QAAQ,CAACE,MAAM,CAAC,UAAU,CAAEJ,WAAW,CAACM,QAAQ,CAAC,CAEjDf,OAAO,CAACE,GAAG,CAAC,0BAA0BP,OAAO,aAAa,CAAC,CAC3D;AACA,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAAL,KAAK,CAACwB,IAAI,CAAC,GAAGrB,OAAO,aAAa,CAAEgB,QAAQ,CAAE,CACnEM,OAAO,CAAE,CACP,cAAc,CAAE,qBAClB,CACF,CAAC,CAAC,CACFjB,OAAO,CAACE,GAAG,CAAC,oBAAoB,CAAEL,QAAQ,CAAC,CAC3C,MAAO,CAAAA,QAAQ,CAACqB,IAAI,CACtB,CAAC,IAAM,IAAIR,SAAS,GAAK,UAAU,CAAE,CACnC;AACAV,OAAO,CAACE,GAAG,CAAC,0BAA0BP,OAAO,sBAAsB,CAAC,CACpE,KAAM,CAAAE,QAAQ,CAAG,KAAM,CAAAH,aAAa,CAACsB,IAAI,CAAC,sBAAsB,CAAE,CAChEG,cAAc,CAAEV,WAAW,CAACU,cAAc,CAC1CJ,QAAQ,CAAEN,WAAW,CAACM,QACxB,CAAC,CAAC,CACFf,OAAO,CAACE,GAAG,CAAC,oBAAoB,CAAEL,QAAQ,CAAC,CAE3C;AACA,GAAIA,QAAQ,CAACqB,IAAI,CAACE,WAAW,CAAE,CAC7BpB,OAAO,CAACE,GAAG,CAAC,wCAAwC,CAAEL,QAAQ,CAACqB,IAAI,CAACE,WAAW,CAAC,CAChFjB,YAAY,CAACkB,OAAO,CAAC,oBAAoB,CAAExB,QAAQ,CAACqB,IAAI,CAACE,WAAW,CAACE,QAAQ,CAAC,CAAC,CAAC,CAChFnB,YAAY,CAACkB,OAAO,CAAC,sBAAsB,CAAExB,QAAQ,CAACqB,IAAI,CAACK,aAAa,EAAI,YAAY1B,QAAQ,CAACqB,IAAI,CAACE,WAAW,EAAE,CAAC,CACtH,CAAC,IAAM,CACLpB,OAAO,CAACwB,IAAI,CAAC,mDAAmD,CAAE3B,QAAQ,CAACqB,IAAI,CAAC,CAClF,CAEA,MAAO,CAAArB,QAAQ,CAACqB,IAAI,CACtB,CAAC,IAAM,CACL,KAAM,IAAI,CAAAO,KAAK,CAAC,0BAA0B,CAAC,CAC7C,CACF,CAAE,MAAO1B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACpC,GAAIA,KAAK,CAACF,QAAQ,CAAE,CAClBG,OAAO,CAACD,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAACF,QAAQ,CAACI,MAAM,CAAEF,KAAK,CAACF,QAAQ,CAACqB,IAAI,CAAC,CAC7E;AACA,GAAInB,KAAK,CAACF,QAAQ,CAACI,MAAM,GAAK,GAAG,CAAE,CACjC,KAAM,CAAEyB,MAAM,CAAE3B,KAAK,CAACF,QAAQ,CAACqB,IAAI,CAACQ,MAAM,EAAI,uDAAwD,CAAC,CACzG,CAAC,IAAM,IAAI3B,KAAK,CAACF,QAAQ,CAACI,MAAM,GAAK,GAAG,CAAE,CACxC,KAAM,CAAEyB,MAAM,CAAE3B,KAAK,CAACF,QAAQ,CAACqB,IAAI,CAACQ,MAAM,EAAI,+CAAgD,CAAC,CACjG,CAAC,IAAM,CACL,KAAM,CAAA3B,KAAK,CAACF,QAAQ,CAACqB,IAAI,CAC3B,CACF,CAAC,IAAM,IAAInB,KAAK,CAAC4B,OAAO,CAAE,CACxB3B,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC4B,OAAO,CAAC,CAC1D,KAAM,CAAED,MAAM,CAAE,6EAA8E,CAAC,CACjG,CAAC,IAAM,CACL1B,OAAO,CAACD,KAAK,CAAC,mDAAmD,CAAEA,KAAK,CAAC6B,OAAO,CAAC,CACjF,KAAM,CAAEF,MAAM,CAAE3B,KAAK,CAAC6B,OAAQ,CAAC,CACjC,CACF,CACF,CAAC,CAID;AACAC,UAAU,CAAE,KAAAA,CAAA,GAAY,CACtB,GAAI,CACF7B,OAAO,CAACE,GAAG,CAAC,4BAA4B,CAAC,CACzC,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAAH,aAAa,CAACsB,IAAI,CAAC,kBAAkB,CAAC,CAC7DhB,OAAO,CAACE,GAAG,CAAC,0BAA0B,CAAEL,QAAQ,CAACqB,IAAI,CAAC,CAEtD;AACA;AACA,KAAM,CAAAY,cAAc,CAAGjC,QAAQ,CAACqB,IAAI,CAACa,eAAe,GAAK,IAAI,CAE7D;AACA,GAAID,cAAc,CAAE,CAClB9B,OAAO,CAACE,GAAG,CAAC,iEAAiE,CAAC,CAC9EC,YAAY,CAACkB,OAAO,CAAC,iBAAiB,CAAE,MAAM,CAAC,CACjD,CAAC,IAAM,CACL;AACAlB,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC,CAC5C,CAEA;AACA,KAAM,CAAA4B,QAAQ,CAAG,CACfC,EAAE,CAAEpC,QAAQ,CAACqB,IAAI,CAACgB,OAAO,CACzBpB,QAAQ,CAAEjB,QAAQ,CAACqB,IAAI,CAACJ,QAAQ,CAChCqB,IAAI,CAAEtC,QAAQ,CAACqB,IAAI,CAACiB,IAAI,CACxBL,cAAc,CAAEA,cAClB,CAAC,CAED;AACA,GAAIjC,QAAQ,CAACqB,IAAI,CAACiB,IAAI,GAAK,eAAe,EAAItC,QAAQ,CAACqB,IAAI,CAACE,WAAW,CAAE,CACvEY,QAAQ,CAACZ,WAAW,CAAGvB,QAAQ,CAACqB,IAAI,CAACE,WAAW,CAChDY,QAAQ,CAACT,aAAa,CAAG1B,QAAQ,CAACqB,IAAI,CAACK,aAAa,EAAI,YAAY1B,QAAQ,CAACqB,IAAI,CAACE,WAAW,EAAE,CAE/F;AACAjB,YAAY,CAACkB,OAAO,CAAC,oBAAoB,CAAExB,QAAQ,CAACqB,IAAI,CAACE,WAAW,CAACE,QAAQ,CAAC,CAAC,CAAC,CAChFnB,YAAY,CAACkB,OAAO,CAAC,sBAAsB,CAAEW,QAAQ,CAACT,aAAa,CAAC,CACpEvB,OAAO,CAACE,GAAG,CAAC,4DAA4D,CAAE,CACxEkB,WAAW,CAAEvB,QAAQ,CAACqB,IAAI,CAACE,WAAW,CACtCG,aAAa,CAAES,QAAQ,CAACT,aAC1B,CAAC,CAAC,CACJ,CAEA;AACA,GAAIO,cAAc,EAAIjC,QAAQ,CAACqB,IAAI,CAACkB,eAAe,CAAE,CACnD;AACA,KAAM,CAAAC,oBAAoB,CAAG,CAC3BJ,EAAE,CAAEpC,QAAQ,CAACqB,IAAI,CAACkB,eAAe,CACjCtB,QAAQ,CAAEjB,QAAQ,CAACqB,IAAI,CAACoB,qBAAqB,CAC7CH,IAAI,CAAEtC,QAAQ,CAACqB,IAAI,CAACqB,iBACtB,CAAC,CAEDpC,YAAY,CAACkB,OAAO,CAAC,kBAAkB,CAAEmB,IAAI,CAACC,SAAS,CAACJ,oBAAoB,CAAC,CAAC,CAChF,CAEA,MAAO,CAAAL,QAAQ,CACjB,CAAE,MAAOjC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C;AACAI,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC,CAChCD,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC,CAE1C;AACAD,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC,CAC7CD,YAAY,CAACC,UAAU,CAAC,sBAAsB,CAAC,CAC/CJ,OAAO,CAACE,GAAG,CAAC,mDAAmD,CAAC,CAChE,KAAM,CAAAH,KAAK,CAACF,QAAQ,CAAGE,KAAK,CAACF,QAAQ,CAACqB,IAAI,CAAGnB,KAAK,CACpD,CACF,CAAC,CAED;AACA2C,eAAe,CAAE,KAAO,CAAAC,MAAM,EAAK,CACjC,GAAI,CACF,KAAM,CAAA9C,QAAQ,CAAG,KAAM,CAAAH,aAAa,CAACsB,IAAI,CAAC,mBAAmB,CAAE,CAC7DkB,OAAO,CAAES,MACX,CAAC,CAAC,CACF,MAAO,CAAA9C,QAAQ,CAACqB,IAAI,CACtB,CAAE,MAAOnB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,KAAM,CAAAA,KAAK,CAACF,QAAQ,CAAGE,KAAK,CAACF,QAAQ,CAACqB,IAAI,CAAGnB,KAAK,CACpD,CACF,CACF,CAAC,CAED,cAAe,CAAAQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}