{"ast": null, "code": "import React,{createContext,useState,useContext}from'react';// Crea il contesto globale\nimport{jsx as _jsx}from\"react/jsx-runtime\";const GlobalContext=/*#__PURE__*/createContext(null);// Hook personalizzato per utilizzare il contesto globale\nexport const useGlobalContext=()=>useContext(GlobalContext);export const GlobalProvider=_ref=>{let{children}=_ref;// Stato per il dialogo di eliminazione cavi\nconst[openEliminaCavoDialog,setOpenEliminaCavoDialog]=useState(false);// Stato per il dialogo di modifica cavi\nconst[openModificaCavoDialog,setOpenModificaCavoDialog]=useState(false);// Stato per il dialogo di aggiunta cavi\nconst[openAggiungiCavoDialog,setOpenAggiungiCavoDialog]=useState(false);// Valore del contesto\nconst value={openEliminaCavoDialog,setOpenEliminaCavoDialog,openModificaCavoDialog,setOpenModificaCavoDialog,openAggiungiCavoDialog,setOpenAggiungiCavoDialog};return/*#__PURE__*/_jsx(GlobalContext.Provider,{value:value,children:children});};", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "jsx", "_jsx", "GlobalContext", "useGlobalContext", "GlobalProvider", "_ref", "children", "openEliminaCavoDialog", "setOpenEliminaCavoDialog", "openModificaCavoDialog", "setOpenModificaCavoDialog", "openAggiungiCavoDialog", "setOpenAggiungiCavoDialog", "value", "Provider"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/context/GlobalContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext } from 'react';\n\n// Crea il contesto globale\nconst GlobalContext = createContext(null);\n\n// Hook personalizzato per utilizzare il contesto globale\nexport const useGlobalContext = () => useContext(GlobalContext);\n\nexport const GlobalProvider = ({ children }) => {\n  // Stato per il dialogo di eliminazione cavi\n  const [openEliminaCavoDialog, setOpenEliminaCavoDialog] = useState(false);\n\n  // Stato per il dialogo di modifica cavi\n  const [openModificaCavoDialog, setOpenModificaCavoDialog] = useState(false);\n\n  // Stato per il dialogo di aggiunta cavi\n  const [openAggiungiCavoDialog, setOpenAggiungiCavoDialog] = useState(false);\n\n  // Valore del contesto\n  const value = {\n    openEliminaCavoDialog,\n    setOpenEliminaCavoDialog,\n    openModificaCavoDialog,\n    setOpenModificaCavoDialog,\n    openAggiungiCavoDialog,\n    setOpenAggiungiCavoDialog\n  };\n\n  return <GlobalContext.Provider value={value}>{children}</GlobalContext.Provider>;\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,QAAQ,CAAEC,UAAU,KAAQ,OAAO,CAElE;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBACA,KAAM,CAAAC,aAAa,cAAGL,aAAa,CAAC,IAAI,CAAC,CAEzC;AACA,MAAO,MAAM,CAAAM,gBAAgB,CAAGA,CAAA,GAAMJ,UAAU,CAACG,aAAa,CAAC,CAE/D,MAAO,MAAM,CAAAE,cAAc,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACzC;AACA,KAAM,CAACE,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGV,QAAQ,CAAC,KAAK,CAAC,CAEzE;AACA,KAAM,CAACW,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGZ,QAAQ,CAAC,KAAK,CAAC,CAE3E;AACA,KAAM,CAACa,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGd,QAAQ,CAAC,KAAK,CAAC,CAE3E;AACA,KAAM,CAAAe,KAAK,CAAG,CACZN,qBAAqB,CACrBC,wBAAwB,CACxBC,sBAAsB,CACtBC,yBAAyB,CACzBC,sBAAsB,CACtBC,yBACF,CAAC,CAED,mBAAOX,IAAA,CAACC,aAAa,CAACY,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAAP,QAAA,CAAEA,QAAQ,CAAyB,CAAC,CAClF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}