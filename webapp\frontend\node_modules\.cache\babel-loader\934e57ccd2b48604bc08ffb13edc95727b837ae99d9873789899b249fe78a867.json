{"ast": null, "code": "import { getRoundingMethod } from \"./_lib/getRoundingMethod.mjs\";\nimport { differenceInMonths } from \"./differenceInMonths.mjs\";\n\n/**\n * The {@link differenceInQuarters} function options.\n */\n\n/**\n * @name differenceInQuarters\n * @category Quarter Helpers\n * @summary Get the number of quarters between the given dates.\n *\n * @description\n * Get the number of quarters between the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The later date\n * @param dateRight - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of full quarters\n *\n * @example\n * // How many full quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInQuarters(new Date(2014, 6, 2), new Date(2013, 11, 31))\n * //=> 2\n */\nexport function differenceInQuarters(dateLeft, dateRight, options) {\n  const diff = differenceInMonths(dateLeft, dateRight) / 3;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInQuarters;", "map": {"version": 3, "names": ["getRoundingMethod", "differenceInMonths", "differenceInQuarters", "dateLeft", "dateRight", "options", "diff", "roundingMethod"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/differenceInQuarters.mjs"], "sourcesContent": ["import { getRoundingMethod } from \"./_lib/getRoundingMethod.mjs\";\nimport { differenceInMonths } from \"./differenceInMonths.mjs\";\n\n/**\n * The {@link differenceInQuarters} function options.\n */\n\n/**\n * @name differenceInQuarters\n * @category Quarter Helpers\n * @summary Get the number of quarters between the given dates.\n *\n * @description\n * Get the number of quarters between the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The later date\n * @param dateRight - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of full quarters\n *\n * @example\n * // How many full quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInQuarters(new Date(2014, 6, 2), new Date(2013, 11, 31))\n * //=> 2\n */\nexport function differenceInQuarters(dateLeft, dateRight, options) {\n  const diff = differenceInMonths(dateLeft, dateRight) / 3;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInQuarters;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,8BAA8B;AAChE,SAASC,kBAAkB,QAAQ,0BAA0B;;AAE7D;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACjE,MAAMC,IAAI,GAAGL,kBAAkB,CAACE,QAAQ,EAAEC,SAAS,CAAC,GAAG,CAAC;EACxD,OAAOJ,iBAAiB,CAACK,OAAO,EAAEE,cAAc,CAAC,CAACD,IAAI,CAAC;AACzD;;AAEA;AACA,eAAeJ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}