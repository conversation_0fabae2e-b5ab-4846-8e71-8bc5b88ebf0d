{"ast": null, "code": "'use client';\n\nexport { default } from './Snackbar';\nexport { default as snackbarClasses } from './snackbarClasses';\nexport * from './snackbarClasses';", "map": {"version": 3, "names": ["default", "snackbarClasses"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/material/Snackbar/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Snackbar';\nexport { default as snackbarClasses } from './snackbarClasses';\nexport * from './snackbarClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASA,OAAO,IAAIC,eAAe,QAAQ,mBAAmB;AAC9D,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}