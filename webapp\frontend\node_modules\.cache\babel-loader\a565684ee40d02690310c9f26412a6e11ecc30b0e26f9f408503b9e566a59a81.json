{"ast": null, "code": "import { addMinutes } from \"./addMinutes.mjs\";\nimport { startOfMinute } from \"./startOfMinute.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link eachMinuteOfInterval} function options.\n */\n\n/**\n * @name eachMinuteOfInterval\n * @category Interval Helpers\n * @summary Return the array of minutes within the specified time interval.\n *\n * @description\n * Returns the array of minutes within the specified time interval.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of minutes from the minute of the interval start to the minute of the interval end\n *\n * @example\n * // Each minute between 14 October 2020, 13:00 and 14 October 2020, 13:03\n * const result = eachMinuteOfInterval({\n *   start: new Date(2014, 9, 14, 13),\n *   end: new Date(2014, 9, 14, 13, 3)\n * })\n * //=> [\n * //   Wed Oct 14 2014 13:00:00,\n * //   Wed Oct 14 2014 13:01:00,\n * //   Wed Oct 14 2014 13:02:00,\n * //   Wed Oct 14 2014 13:03:00\n * // ]\n */\nexport function eachMinuteOfInterval(interval, options) {\n  const startDate = startOfMinute(toDate(interval.start));\n  const endDate = toDate(interval.end);\n  let reversed = +startDate > +endDate;\n  const endTime = reversed ? +startDate : +endDate;\n  let currentDate = reversed ? endDate : startDate;\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n  const dates = [];\n  while (+currentDate <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate = addMinutes(currentDate, step);\n  }\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachMinuteOfInterval;", "map": {"version": 3, "names": ["addMinutes", "startOfMinute", "toDate", "eachMinuteOfInterval", "interval", "options", "startDate", "start", "endDate", "end", "reversed", "endTime", "currentDate", "step", "dates", "push", "reverse"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/eachMinuteOfInterval.mjs"], "sourcesContent": ["import { addMinutes } from \"./addMinutes.mjs\";\nimport { startOfMinute } from \"./startOfMinute.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link eachMinuteOfInterval} function options.\n */\n\n/**\n * @name eachMinuteOfInterval\n * @category Interval Helpers\n * @summary Return the array of minutes within the specified time interval.\n *\n * @description\n * Returns the array of minutes within the specified time interval.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of minutes from the minute of the interval start to the minute of the interval end\n *\n * @example\n * // Each minute between 14 October 2020, 13:00 and 14 October 2020, 13:03\n * const result = eachMinuteOfInterval({\n *   start: new Date(2014, 9, 14, 13),\n *   end: new Date(2014, 9, 14, 13, 3)\n * })\n * //=> [\n * //   Wed Oct 14 2014 13:00:00,\n * //   Wed Oct 14 2014 13:01:00,\n * //   Wed Oct 14 2014 13:02:00,\n * //   Wed Oct 14 2014 13:03:00\n * // ]\n */\nexport function eachMinuteOfInterval(interval, options) {\n  const startDate = startOfMinute(toDate(interval.start));\n  const endDate = toDate(interval.end);\n\n  let reversed = +startDate > +endDate;\n  const endTime = reversed ? +startDate : +endDate;\n  let currentDate = reversed ? endDate : startDate;\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+currentDate <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate = addMinutes(currentDate, step);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachMinuteOfInterval;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACtD,MAAMC,SAAS,GAAGL,aAAa,CAACC,MAAM,CAACE,QAAQ,CAACG,KAAK,CAAC,CAAC;EACvD,MAAMC,OAAO,GAAGN,MAAM,CAACE,QAAQ,CAACK,GAAG,CAAC;EAEpC,IAAIC,QAAQ,GAAG,CAACJ,SAAS,GAAG,CAACE,OAAO;EACpC,MAAMG,OAAO,GAAGD,QAAQ,GAAG,CAACJ,SAAS,GAAG,CAACE,OAAO;EAChD,IAAII,WAAW,GAAGF,QAAQ,GAAGF,OAAO,GAAGF,SAAS;EAEhD,IAAIO,IAAI,GAAGR,OAAO,EAAEQ,IAAI,IAAI,CAAC;EAC7B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,IAAIA,IAAI,GAAG,CAAC,EAAE;IACZA,IAAI,GAAG,CAACA,IAAI;IACZH,QAAQ,GAAG,CAACA,QAAQ;EACtB;EAEA,MAAMI,KAAK,GAAG,EAAE;EAEhB,OAAO,CAACF,WAAW,IAAID,OAAO,EAAE;IAC9BG,KAAK,CAACC,IAAI,CAACb,MAAM,CAACU,WAAW,CAAC,CAAC;IAC/BA,WAAW,GAAGZ,UAAU,CAACY,WAAW,EAAEC,IAAI,CAAC;EAC7C;EAEA,OAAOH,QAAQ,GAAGI,KAAK,CAACE,OAAO,CAAC,CAAC,GAAGF,KAAK;AAC3C;;AAEA;AACA,eAAeX,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}