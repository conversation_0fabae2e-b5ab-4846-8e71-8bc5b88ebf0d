{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ModificaBobinaForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Typography, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Divider, Alert, CircularProgress, Dialog, DialogTitle, DialogContent, DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Radio, RadioGroup, FormControlLabel, IconButton, InputAdornment, List, ListItem, ListItemText, ListItemSecondaryAction, Chip } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Cancel as CancelIcon, Warning as WarningIcon, Info as InfoIcon, AddCircleOutline as AddCircleOutlineIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Componente per la modifica della bobina di un cavo già posato\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ModificaBobinaForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [selectedOption, setSelectedOption] = useState('');\n  const [selectedBobinaId, setSelectedBobinaId] = useState('');\n  const [bobinaSearchText, setBobinaSearchText] = useState('');\n  const [bobinaNumericInput, setBobinaNumericInput] = useState('');\n\n  // Stati per i dialoghi\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');\n  const [confirmDialogAction, setConfirmDialogAction] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica i cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica le bobine quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadBobine();\n    }\n  }, [selectedCavo]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica solo i cavi installati (con metratura_reale > 0)\n      const caviData = await caviService.getCavi(cantiereId, null, 'Installato');\n      console.log(`Caricati ${caviData.length} cavi installati`);\n\n      // Filtra i cavi che hanno metratura_reale > 0\n      const caviInstallati = caviData.filter(cavo => parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato');\n      setCavi(caviInstallati);\n      console.log(`Filtrati ${caviInstallati.length} cavi effettivamente installati`);\n    } catch (error) {\n      console.error('Errore durante il caricamento dei cavi:', error);\n      onError('Errore durante il caricamento dei cavi: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    if (!selectedCavo) return;\n    try {\n      setBobineLoading(true);\n      console.log(`Caricamento bobine per il cantiere ${cantiereId}...`);\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Caricati ${bobineData.length} bobine`);\n\n      // Filtra le bobine compatibili\n      const compatibleBobineData = bobineData.filter(bobina => bobina.tipologia === selectedCavo.tipologia && bobina.sezione === selectedCavo.sezione && (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'));\n      setBobine(bobineData);\n      setCompatibleBobine(compatibleBobineData);\n      console.log(`Filtrate ${compatibleBobineData.length} bobine compatibili`);\n    } catch (error) {\n      console.error('Errore durante il caricamento delle bobine:', error);\n      onError('Errore durante il caricamento delle bobine: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setLoading(true);\n      const cavo = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica che il cavo sia installato\n      if (parseFloat(cavo.metratura_reale) <= 0 && cavo.stato_installazione !== 'Installato') {\n        onError('Il cavo selezionato non risulta installato');\n        setLoading(false);\n        return;\n      }\n      setSelectedCavo(cavo);\n      setSelectedOption(''); // Reset dell'opzione selezionata\n      setSelectedBobinaId(''); // Reset della bobina selezionata\n      setShowSearchResults(false);\n    } catch (error) {\n      console.error('Errore durante la ricerca del cavo:', error);\n      onError('Errore durante la ricerca del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo dalla lista\n  const handleSelectCavo = cavo => {\n    setSelectedCavo(cavo);\n    setSelectedOption(''); // Reset dell'opzione selezionata\n    setSelectedBobinaId(''); // Reset della bobina selezionata\n    setShowSearchResults(false);\n  };\n\n  // Gestisce il cambio dell'opzione selezionata\n  const handleOptionChange = event => {\n    setSelectedOption(event.target.value);\n    setSelectedBobinaId(''); // Reset della bobina selezionata quando cambia l'opzione\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = bobinaId => {\n    setSelectedBobinaId(bobinaId);\n    // Aggiorna anche l'input numerico con la parte numerica\n    setBobinaNumericInput(getBobinaNumber(bobinaId));\n  };\n\n  // Gestisce la selezione di una bobina tramite input numerico\n  const handleSelectBobinaByNumber = () => {\n    if (!bobinaNumericInput.trim()) {\n      onError('Inserisci un numero di bobina valido');\n      return;\n    }\n\n    // Costruisci l'ID completo della bobina\n    const fullBobinaId = `C${cantiereId}_B${bobinaNumericInput.trim()}`;\n\n    // Verifica che la bobina esista\n    const bobina = bobine.find(b => b.id_bobina === fullBobinaId);\n    if (!bobina) {\n      onError(`Bobina ${bobinaNumericInput} non trovata nel cantiere`);\n      return;\n    }\n\n    // Verifica compatibilità\n    const isCompatible = selectedCavo && String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim();\n    if (!isCompatible) {\n      onError(`Bobina ${bobinaNumericInput} non è compatibile con il cavo selezionato`);\n      return;\n    }\n\n    // Seleziona la bobina\n    setSelectedBobinaId(fullBobinaId);\n  };\n\n  // Gestisce il salvataggio delle modifiche\n  const handleSave = () => {\n    if (!selectedCavo) {\n      onError('Seleziona un cavo prima di procedere');\n      return;\n    }\n    if (!selectedOption) {\n      onError('Seleziona un\\'opzione prima di procedere');\n      return;\n    }\n\n    // Verifica che sia stata selezionata una bobina se l'opzione è \"assegnaNuova\"\n    if (selectedOption === 'assegnaNuova' && !selectedBobinaId) {\n      onError('Seleziona una bobina prima di procedere');\n      return;\n    }\n\n    // Prepara il messaggio di conferma in base all'opzione selezionata\n    let message = '';\n    let action = null;\n    if (selectedOption === 'assegnaNuova') {\n      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);\n      message = `Sei sicuro di voler assegnare la bobina ${getBobinaNumber(selectedBobinaId)} al cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina(selectedBobinaId);\n    } else if (selectedOption === 'rimuoviBobina') {\n      message = `Sei sicuro di voler rimuovere la bobina attuale dal cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina('BOBINA_VUOTA');\n    } else if (selectedOption === 'annullaInstallazione') {\n      message = `ATTENZIONE: Questa operazione annullerà l'installazione del cavo ${selectedCavo.id_cavo}. Tutti i metri posati saranno restituiti alla bobina originale. Sei sicuro di voler procedere?`;\n      action = () => annullaInstallazione();\n    }\n    setConfirmDialogMessage(message);\n    setConfirmDialogAction(() => action);\n    setShowConfirmDialog(true);\n  };\n\n  // Funzione per aggiornare la bobina di un cavo\n  const updateBobina = async bobinaId => {\n    try {\n      setLoading(true);\n      await caviService.updateBobina(cantiereId, selectedCavo.id_cavo, bobinaId);\n      onSuccess(`Bobina ${bobinaId === 'BOBINA_VUOTA' ? 'vuota assegnata' : 'assegnata'} con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento della bobina:', error);\n      onError('Errore durante l\\'aggiornamento della bobina: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per annullare l'installazione di un cavo\n  const annullaInstallazione = async () => {\n    try {\n      setLoading(true);\n\n      // Chiamata all'API per annullare l'installazione\n      await caviService.cancelInstallation(cantiereId, selectedCavo.id_cavo);\n      onSuccess(`Installazione del cavo ${selectedCavo.id_cavo} annullata con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'annullamento dell\\'installazione:', error);\n      onError('Errore durante l\\'annullamento dell\\'installazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per chiudere il form e resettare tutto\n  const handleCloseForm = () => {\n    // Reset di tutti gli stati\n    setSelectedCavo(null);\n    setSelectedOption('');\n    setSelectedBobinaId('');\n    setCavoIdInput('');\n    setShowSearchResults(false);\n\n    // Messaggio di conferma\n    onSuccess('Operazione annullata');\n  };\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    if (idBobina === 'BOBINA_VUOTA') return 'BOBINA VUOTA';\n\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Renderizza il form per la selezione del cavo\n  const renderCavoSelectionForm = () => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"subtitle2\",\n      sx: {\n        mb: 1,\n        fontWeight: 'bold'\n      },\n      children: \"Seleziona un cavo posato\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 1.5,\n        mb: 2,\n        width: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          sx: {\n            mr: 1,\n            minWidth: '80px'\n          },\n          children: \"Cerca cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          size: \"small\",\n          label: \"ID Cavo\",\n          variant: \"outlined\",\n          value: cavoIdInput,\n          onChange: e => setCavoIdInput(e.target.value),\n          placeholder: \"Inserisci l'ID del cavo\",\n          sx: {\n            flexGrow: 0,\n            width: '200px',\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSearchCavoById,\n          disabled: caviLoading || !cavoIdInput.trim(),\n          startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 38\n          }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 71\n          }, this),\n          size: \"small\",\n          sx: {\n            minWidth: '80px',\n            height: '36px',\n            mr: 2\n          },\n          children: \"CERCA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1,\n            flexWrap: 'nowrap',\n            overflow: 'hidden',\n            ml: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mr: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 'bold',\n                whiteSpace: 'nowrap',\n                mr: 1,\n                fontSize: '0.95rem'\n              },\n              children: [\"Cavo: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#1976d2'\n                },\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"vertical\",\n              flexItem: true,\n              sx: {\n                mx: 1.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 3,\n                flexWrap: 'nowrap',\n                overflow: 'hidden'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.9rem',\n                    mr: 0.5\n                  },\n                  children: \"Tipo:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.9rem'\n                  },\n                  children: selectedCavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.9rem',\n                    mr: 0.5\n                  },\n                  children: \"Form:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.9rem'\n                  },\n                  children: selectedCavo.sezione || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.9rem',\n                    mr: 0.5\n                  },\n                  children: \"Metri:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.9rem'\n                  },\n                  children: [selectedCavo.metratura_reale || 'N/A', \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.9rem',\n                    mr: 0.5\n                  },\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: selectedCavo.stato_installazione || 'N/D',\n                  color: \"success\",\n                  sx: {\n                    height: '22px',\n                    '& .MuiChip-label': {\n                      px: 1,\n                      py: 0,\n                      fontSize: '0.85rem'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 15\n          }, this), selectedCavo.id_bobina && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"vertical\",\n              flexItem: true,\n              sx: {\n                mx: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                sx: {\n                  fontWeight: 'bold',\n                  whiteSpace: 'nowrap',\n                  mr: 1,\n                  fontSize: '0.95rem',\n                  color: '#2e7d32'\n                },\n                children: [\"Bobina: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: selectedCavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(selectedCavo.id_bobina)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 31\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 21\n              }, this), (() => {\n                if (selectedCavo.id_bobina === 'BOBINA_VUOTA') {\n                  return /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontSize: '0.9rem',\n                        color: 'text.secondary',\n                        fontStyle: 'italic'\n                      },\n                      children: \"(Cavo posato senza bobina specifica)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 437,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 27\n                  }, this);\n                }\n                const bobina = bobine.find(b => b.id_bobina === selectedCavo.id_bobina);\n                return bobina ? /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 3,\n                    flexWrap: 'nowrap',\n                    overflow: 'hidden'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium',\n                        fontSize: '0.9rem',\n                        mr: 0.5\n                      },\n                      children: \"Residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 448,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontSize: '0.9rem',\n                        color: 'success.main',\n                        fontWeight: 'bold'\n                      },\n                      children: [bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 449,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium',\n                        fontSize: '0.9rem',\n                        mr: 0.5\n                      },\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: bobina.stato_bobina || 'N/D',\n                      color: bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning',\n                      sx: {\n                        height: '22px',\n                        '& .MuiChip-label': {\n                          px: 1,\n                          py: 0,\n                          fontSize: '0.85rem'\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 25\n                }, this) : null;\n              })()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 1.5,\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        sx: {\n          mb: 1\n        },\n        children: \"Seleziona dalla lista\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 9\n      }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 11\n      }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          py: 0.5\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          children: \"Non ci sono cavi posati disponibili.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        variant: \"outlined\",\n        sx: {\n          maxHeight: '300px',\n          overflow: 'auto',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          stickyHeader: true,\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                '& th': {\n                  fontWeight: 'bold',\n                  py: 1,\n                  bgcolor: '#f5f5f5'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                sx: {\n                  width: '40px'\n                },\n                children: \"Info\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n              hover: true,\n              onClick: () => handleSelectCavo(cavo),\n              sx: {\n                cursor: 'pointer',\n                '&:hover': {\n                  bgcolor: '#f1f8e9'\n                },\n                '& td': {\n                  py: 0.5\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 'medium'\n                },\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.sezione || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.metratura_reale || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.id_bobina ? cavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(cavo.id_bobina) : 'VUOTA'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: \"Installato\",\n                  color: \"success\",\n                  sx: {\n                    height: '20px',\n                    '& .MuiChip-label': {\n                      px: 1,\n                      py: 0,\n                      fontSize: '0.7rem'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: e => {\n                    e.stopPropagation();\n                    setSelectedCavo(cavo);\n                    setShowCavoDetailsDialog(true);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 21\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 474,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 360,\n    columnNumber: 5\n  }, this);\n\n  // Renderizza le opzioni di modifica\n  const renderModificaOptions = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Opzioni di modifica\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n        value: selectedOption,\n        onChange: handleOptionChange,\n        children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"assegnaNuova\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 22\n          }, this),\n          label: \"Assegna nuova bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"rimuoviBobina\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 22\n          }, this),\n          label: \"Rimuovi bobina attuale\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"annullaInstallazione\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 22\n          }, this),\n          label: \"Annulla installazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 9\n      }, this), selectedOption === 'assegnaNuova' && renderBobineSelection(), selectedOption === 'rimuoviBobina' && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: \"Questa operazione rimuover\\xE0 l'associazione con la bobina attuale, assegnando una \\\"BOBINA_VUOTA\\\" al cavo. Il cavo rimarr\\xE0 nello stato posato e i metri posati rimarranno invariati. La bobina attuale (se presente) riavr\\xE0 i suoi metri restituiti.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 11\n      }, this), selectedOption === 'annullaInstallazione' && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"bold\",\n          children: \"ATTENZIONE: Questa operazione annuller\\xE0 completamente l'installazione del cavo.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [\"- Il cavo torner\\xE0 allo stato \\\"Da installare\\\"\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 59\n          }, this), \"- La metratura reale sar\\xE0 azzerata\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 49\n          }, this), \"- L'associazione con la bobina sar\\xE0 rimossa\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 58\n          }, this), \"- I metri posati saranno restituiti alla bobina originale (se presente)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 24\n          }, this),\n          onClick: handleCloseForm,\n          disabled: loading,\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 24\n          }, this),\n          onClick: handleSave,\n          disabled: loading || !selectedOption || selectedOption === 'assegnaNuova' && !selectedBobinaId,\n          children: \"Salva modifiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza la selezione delle bobine\n  const renderBobineSelection = () => {\n    if (bobineLoading) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Filtra le bobine in base al testo di ricerca\n    const bobineFiltrate = bobine.filter(bobina => {\n      const searchLower = bobinaSearchText.toLowerCase();\n      return !bobinaSearchText || getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower) || String(bobina.tipologia || '').toLowerCase().includes(searchLower) || String(bobina.sezione || '').toLowerCase().includes(searchLower);\n    });\n\n    // Separa le bobine compatibili e non compatibili\n    const bobineCompatibili = selectedCavo ? bobineFiltrate.filter(bobina => String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim() && (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso')) : [];\n    const bobineNonCompatibili = selectedCavo ? bobineFiltrate.filter(bobina => !(String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim()) && (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso')) : [];\n\n    // Ordina per metri residui (decrescente)\n    bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n    bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Ricerca bobina (ID, tipologia, formazione)\",\n          value: bobinaSearchText,\n          onChange: e => setBobinaSearchText(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 17\n            }, this)\n          },\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 673,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 672,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2,\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Inserisci numero bobina (es. 3)\",\n          value: bobinaNumericInput,\n          onChange: e => setBobinaNumericInput(e.target.value),\n          type: \"number\",\n          sx: {\n            width: '250px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          onClick: handleSelectBobinaByNumber,\n          disabled: !bobinaNumericInput.trim(),\n          children: \"Seleziona\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 690,\n        columnNumber: 9\n      }, this), selectedBobinaId && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        sx: {\n          mb: 2\n        },\n        children: [\"Bobina selezionata: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: getBobinaNumber(selectedBobinaId)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 710,\n          columnNumber: 33\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 709,\n        columnNumber: 11\n      }, this), bobineCompatibili.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 2,\n            bgcolor: '#e8f5e8'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              color: '#2e7d32',\n              fontWeight: 'bold'\n            },\n            children: [\"Bobine compatibili (\", bobineCompatibili.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Metri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 717,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '300px',\n            overflow: 'auto',\n            bgcolor: 'background.paper'\n          },\n          children: bobineCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n            disablePadding: true,\n            secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n              edge: \"end\",\n              size: \"small\",\n              onClick: () => handleSelectBobina(bobina.id_bobina),\n              color: selectedBobinaId === bobina.id_bobina ? 'success' : 'primary',\n              children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 21\n            }, this),\n            sx: {\n              bgcolor: selectedBobinaId === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n              borderRadius: '4px',\n              mb: 0.5,\n              '&:hover': {\n                bgcolor: selectedBobinaId === bobina.id_bobina ? 'rgba(76, 175, 80, 0.2)' : 'rgba(0, 0, 0, 0.04)'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: selectedBobinaId === bobina.id_bobina ? 'bold' : 'normal'\n                    },\n                    children: getBobinaNumber(bobina.id_bobina)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: bobina.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 772,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 771,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: bobina.sezione\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 775,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [bobina.metri_residui, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 778,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 777,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: bobina.stato_bobina,\n                    size: \"small\",\n                    color: bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 23\n              }, this),\n              sx: {\n                bgcolor: selectedBobinaId === bobina.id_bobina ? '#e8f5e8' : 'transparent',\n                borderRadius: 1,\n                px: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 19\n            }, this)\n          }, bobina.id_bobina, false, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 739,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 716,\n        columnNumber: 11\n      }, this), bobineNonCompatibili.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 2,\n            bgcolor: '#fff3e0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              color: '#f57c00',\n              fontWeight: 'bold'\n            },\n            children: [\"Bobine non compatibili (\", bobineNonCompatibili.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 805,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Queste bobine non corrispondono alle caratteristiche del cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 813,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Metri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  fontSize: '0.85rem'\n                },\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 811,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '300px',\n            overflow: 'auto',\n            bgcolor: 'background.paper'\n          },\n          children: bobineNonCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n            disablePadding: true,\n            secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n              edge: \"end\",\n              size: \"small\",\n              onClick: () => {\n                onError(`Bobina ${getBobinaNumber(bobina.id_bobina)} non compatibile. Tipologia: ${bobina.tipologia}, Formazione: ${bobina.sezione}`);\n              },\n              color: \"warning\",\n              children: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 843,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 21\n            }, this),\n            sx: {\n              borderRadius: '4px',\n              mb: 0.5,\n              '&:hover': {\n                bgcolor: 'rgba(255, 152, 0, 0.08)'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: getBobinaNumber(bobina.id_bobina)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 858,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 857,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: bobina.tipologia !== (selectedCavo === null || selectedCavo === void 0 ? void 0 : selectedCavo.tipologia) ? 'error' : 'text.primary',\n                    children: bobina.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: String(bobina.sezione) !== String(selectedCavo === null || selectedCavo === void 0 ? void 0 : selectedCavo.sezione) ? 'error' : 'text.primary',\n                    children: bobina.sezione\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 865,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [bobina.metri_residui, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 871,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 870,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flex: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: bobina.stato_bobina,\n                    size: \"small\",\n                    color: bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 874,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 873,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 19\n            }, this)\n          }, bobina.id_bobina, false, {\n            fileName: _jsxFileName,\n            lineNumber: 831,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 829,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 11\n      }, this), bobineCompatibili.length === 0 && bobineNonCompatibili.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: bobinaSearchText ? 'Nessuna bobina trovata con i criteri di ricerca specificati.' : 'Non ci sono bobine disponibili.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 891,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 670,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [renderCavoSelectionForm(), renderModificaOptions(), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Dettagli completi del cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 914,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: selectedCavo && /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 916,\n          columnNumber: 28\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 915,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 919,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 918,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 908,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showConfirmDialog,\n      onClose: () => setShowConfirmDialog(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Conferma operazione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 928,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: confirmDialogMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 930,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 929,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowConfirmDialog(false),\n          color: \"primary\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setShowConfirmDialog(false);\n            if (confirmDialogAction) confirmDialogAction();\n          },\n          color: \"primary\",\n          variant: \"contained\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 939,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 932,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 924,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 900,\n    columnNumber: 5\n  }, this);\n};\n_s(ModificaBobinaForm, \"PZ4fkXn+INwY5icGbHeyKjTAiNs=\", false, function () {\n  return [useNavigate];\n});\n_c = ModificaBobinaForm;\nexport default ModificaBobinaForm;\nvar _c;\n$RefreshReg$(_c, \"ModificaBobinaForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Radio", "RadioGroup", "FormControlLabel", "IconButton", "InputAdornment", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Chip", "Search", "SearchIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "AddCircleOutline", "AddCircleOutlineIcon", "useNavigate", "caviService", "parcoCaviService", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ModificaBobinaForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchResults", "setSearchResults", "showSearchResults", "setShowSearchResults", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "compatibleBobine", "setCompatibleBobine", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "selectedOption", "setSelectedOption", "selectedBobinaId", "setSelectedBobinaId", "bobinaSearchText", "setBobinaSearchText", "bobinaNumericInput", "setBobinaNumericInput", "showConfirmDialog", "setShowConfirmDialog", "confirmDialogMessage", "setConfirmDialogMessage", "confirmDialogAction", "setConfirmDialogAction", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "loadCavi", "loadBobine", "console", "log", "caviData", "get<PERSON><PERSON>", "length", "caviInstallati", "filter", "cavo", "parseFloat", "metratura_reale", "stato_installazione", "error", "detail", "message", "bobine<PERSON><PERSON>", "getBobine", "compatibleBobineData", "bobina", "tipologia", "sezione", "stato_bobina", "handleSearchCavoById", "trim", "getCavoById", "handleSelectCavo", "handleOptionChange", "event", "target", "value", "handleSelectBobina", "bobina<PERSON>d", "getBobinaNumber", "handleSelectBobinaByNumber", "fullBobinaId", "find", "b", "id_bobina", "isCompatible", "String", "handleSave", "action", "id_cavo", "updateBobina", "annullaInstallazione", "cancelInstallation", "handleCloseForm", "idBobina", "includes", "split", "renderCavoSelectionForm", "children", "variant", "sx", "mb", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "width", "display", "alignItems", "mr", "min<PERSON><PERSON><PERSON>", "size", "label", "onChange", "e", "placeholder", "flexGrow", "color", "onClick", "disabled", "startIcon", "fontSize", "height", "flexWrap", "overflow", "ml", "whiteSpace", "style", "orientation", "flexItem", "mx", "gap", "px", "py", "fontStyle", "metri_residui", "justifyContent", "my", "severity", "component", "maxHeight", "<PERSON><PERSON><PERSON><PERSON>", "bgcolor", "align", "map", "hover", "cursor", "stopPropagation", "renderModificaOptions", "gutterBottom", "control", "renderBobineSelection", "mt", "bobineFiltrate", "searchLower", "toLowerCase", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "bobineNonCompatibili", "sort", "a", "fullWidth", "InputProps", "startAdornment", "position", "type", "flex", "disablePadding", "secondaryAction", "edge", "borderRadius", "primary", "open", "onClose", "max<PERSON><PERSON><PERSON>", "autoFocus", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ModificaBobinaForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  IconButton,\n  InputAdornment,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Chip\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon,\n  AddCircleOutline as AddCircleOutlineIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\n\n/**\n * Componente per la modifica della bobina di un cavo già posato\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst ModificaBobinaForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [selectedOption, setSelectedOption] = useState('');\n  const [selectedBobinaId, setSelectedBobinaId] = useState('');\n  const [bobinaSearchText, setBobinaSearchText] = useState('');\n  const [bobinaNumericInput, setBobinaNumericInput] = useState('');\n\n  // Stati per i dialoghi\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');\n  const [confirmDialogAction, setConfirmDialogAction] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica i cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica le bobine quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadBobine();\n    }\n  }, [selectedCavo]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica solo i cavi installati (con metratura_reale > 0)\n      const caviData = await caviService.getCavi(cantiereId, null, 'Installato');\n      console.log(`Caricati ${caviData.length} cavi installati`);\n\n      // Filtra i cavi che hanno metratura_reale > 0\n      const caviInstallati = caviData.filter(cavo =>\n        parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato'\n      );\n\n      setCavi(caviInstallati);\n      console.log(`Filtrati ${caviInstallati.length} cavi effettivamente installati`);\n    } catch (error) {\n      console.error('Errore durante il caricamento dei cavi:', error);\n      onError('Errore durante il caricamento dei cavi: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    if (!selectedCavo) return;\n\n    try {\n      setBobineLoading(true);\n      console.log(`Caricamento bobine per il cantiere ${cantiereId}...`);\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Caricati ${bobineData.length} bobine`);\n\n      // Filtra le bobine compatibili\n      const compatibleBobineData = bobineData.filter(bobina =>\n        bobina.tipologia === selectedCavo.tipologia &&\n        bobina.sezione === selectedCavo.sezione &&\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso')\n      );\n\n      setBobine(bobineData);\n      setCompatibleBobine(compatibleBobineData);\n      console.log(`Filtrate ${compatibleBobineData.length} bobine compatibili`);\n    } catch (error) {\n      console.error('Errore durante il caricamento delle bobine:', error);\n      onError('Errore durante il caricamento delle bobine: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      const cavo = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica che il cavo sia installato\n      if (parseFloat(cavo.metratura_reale) <= 0 && cavo.stato_installazione !== 'Installato') {\n        onError('Il cavo selezionato non risulta installato');\n        setLoading(false);\n        return;\n      }\n\n      setSelectedCavo(cavo);\n      setSelectedOption(''); // Reset dell'opzione selezionata\n      setSelectedBobinaId(''); // Reset della bobina selezionata\n      setShowSearchResults(false);\n    } catch (error) {\n      console.error('Errore durante la ricerca del cavo:', error);\n      onError('Errore durante la ricerca del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo dalla lista\n  const handleSelectCavo = (cavo) => {\n    setSelectedCavo(cavo);\n    setSelectedOption(''); // Reset dell'opzione selezionata\n    setSelectedBobinaId(''); // Reset della bobina selezionata\n    setShowSearchResults(false);\n  };\n\n  // Gestisce il cambio dell'opzione selezionata\n  const handleOptionChange = (event) => {\n    setSelectedOption(event.target.value);\n    setSelectedBobinaId(''); // Reset della bobina selezionata quando cambia l'opzione\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = (bobinaId) => {\n    setSelectedBobinaId(bobinaId);\n    // Aggiorna anche l'input numerico con la parte numerica\n    setBobinaNumericInput(getBobinaNumber(bobinaId));\n  };\n\n  // Gestisce la selezione di una bobina tramite input numerico\n  const handleSelectBobinaByNumber = () => {\n    if (!bobinaNumericInput.trim()) {\n      onError('Inserisci un numero di bobina valido');\n      return;\n    }\n\n    // Costruisci l'ID completo della bobina\n    const fullBobinaId = `C${cantiereId}_B${bobinaNumericInput.trim()}`;\n\n    // Verifica che la bobina esista\n    const bobina = bobine.find(b => b.id_bobina === fullBobinaId);\n    if (!bobina) {\n      onError(`Bobina ${bobinaNumericInput} non trovata nel cantiere`);\n      return;\n    }\n\n    // Verifica compatibilità\n    const isCompatible = selectedCavo &&\n      String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n      String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim();\n\n    if (!isCompatible) {\n      onError(`Bobina ${bobinaNumericInput} non è compatibile con il cavo selezionato`);\n      return;\n    }\n\n    // Seleziona la bobina\n    setSelectedBobinaId(fullBobinaId);\n  };\n\n  // Gestisce il salvataggio delle modifiche\n  const handleSave = () => {\n    if (!selectedCavo) {\n      onError('Seleziona un cavo prima di procedere');\n      return;\n    }\n\n    if (!selectedOption) {\n      onError('Seleziona un\\'opzione prima di procedere');\n      return;\n    }\n\n    // Verifica che sia stata selezionata una bobina se l'opzione è \"assegnaNuova\"\n    if (selectedOption === 'assegnaNuova' && !selectedBobinaId) {\n      onError('Seleziona una bobina prima di procedere');\n      return;\n    }\n\n    // Prepara il messaggio di conferma in base all'opzione selezionata\n    let message = '';\n    let action = null;\n\n    if (selectedOption === 'assegnaNuova') {\n      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);\n      message = `Sei sicuro di voler assegnare la bobina ${getBobinaNumber(selectedBobinaId)} al cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina(selectedBobinaId);\n    } else if (selectedOption === 'rimuoviBobina') {\n      message = `Sei sicuro di voler rimuovere la bobina attuale dal cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina('BOBINA_VUOTA');\n    } else if (selectedOption === 'annullaInstallazione') {\n      message = `ATTENZIONE: Questa operazione annullerà l'installazione del cavo ${selectedCavo.id_cavo}. Tutti i metri posati saranno restituiti alla bobina originale. Sei sicuro di voler procedere?`;\n      action = () => annullaInstallazione();\n    }\n\n    setConfirmDialogMessage(message);\n    setConfirmDialogAction(() => action);\n    setShowConfirmDialog(true);\n  };\n\n  // Funzione per aggiornare la bobina di un cavo\n  const updateBobina = async (bobinaId) => {\n    try {\n      setLoading(true);\n      await caviService.updateBobina(cantiereId, selectedCavo.id_cavo, bobinaId);\n\n      onSuccess(`Bobina ${bobinaId === 'BOBINA_VUOTA' ? 'vuota assegnata' : 'assegnata'} con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento della bobina:', error);\n      onError('Errore durante l\\'aggiornamento della bobina: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per annullare l'installazione di un cavo\n  const annullaInstallazione = async () => {\n    try {\n      setLoading(true);\n\n      // Chiamata all'API per annullare l'installazione\n      await caviService.cancelInstallation(cantiereId, selectedCavo.id_cavo);\n\n      onSuccess(`Installazione del cavo ${selectedCavo.id_cavo} annullata con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'annullamento dell\\'installazione:', error);\n      onError('Errore durante l\\'annullamento dell\\'installazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per chiudere il form e resettare tutto\n  const handleCloseForm = () => {\n    // Reset di tutti gli stati\n    setSelectedCavo(null);\n    setSelectedOption('');\n    setSelectedBobinaId('');\n    setCavoIdInput('');\n    setShowSearchResults(false);\n\n    // Messaggio di conferma\n    onSuccess('Operazione annullata');\n  };\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    if (idBobina === 'BOBINA_VUOTA') return 'BOBINA VUOTA';\n\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Renderizza il form per la selezione del cavo\n  const renderCavoSelectionForm = () => (\n    <Box>\n      <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 'bold' }}>\n        Seleziona un cavo posato\n      </Typography>\n\n      {/* Ricerca per ID - Versione compatta con dettagli cavo selezionato */}\n      <Paper sx={{ p: 1.5, mb: 2, width: '100%' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\n          <Typography variant=\"subtitle2\" sx={{ mr: 1, minWidth: '80px' }}>\n            Cerca cavo\n          </Typography>\n          <TextField\n            size=\"small\"\n            label=\"ID Cavo\"\n            variant=\"outlined\"\n            value={cavoIdInput}\n            onChange={(e) => setCavoIdInput(e.target.value)}\n            placeholder=\"Inserisci l'ID del cavo\"\n            sx={{ flexGrow: 0, width: '200px', mr: 1 }}\n          />\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={handleSearchCavoById}\n            disabled={caviLoading || !cavoIdInput.trim()}\n            startIcon={caviLoading ? <CircularProgress size={16} /> : <SearchIcon fontSize=\"small\" />}\n            size=\"small\"\n            sx={{ minWidth: '80px', height: '36px', mr: 2 }}\n          >\n            CERCA\n          </Button>\n\n          {/* Dettagli cavo selezionato in riga singola */}\n          {selectedCavo && (\n            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, flexWrap: 'nowrap', overflow: 'hidden', ml: 4 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>\n                <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem' }}>\n                  Cavo: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>\n                </Typography>\n                <Divider orientation=\"vertical\" flexItem sx={{ mx: 1.5 }} />\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Tipo:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Form:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Metri:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.metratura_reale || 'N/A'} m</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>\n                    <Chip\n                      size=\"small\"\n                      label={selectedCavo.stato_installazione || 'N/D'}\n                      color=\"success\"\n                      sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}\n                    />\n                  </Box>\n                </Box>\n              </Box>\n\n              {/* Dettagli bobina attuale */}\n              {selectedCavo.id_bobina && (\n                <>\n                  <Divider orientation=\"vertical\" flexItem sx={{ mx: 2 }} />\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem', color: '#2e7d32' }}>\n                      Bobina: <span>{selectedCavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(selectedCavo.id_bobina)}</span>\n                    </Typography>\n                    {(() => {\n                      if (selectedCavo.id_bobina === 'BOBINA_VUOTA') {\n                        return (\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: 'text.secondary', fontStyle: 'italic' }}>\n                              (Cavo posato senza bobina specifica)\n                            </Typography>\n                          </Box>\n                        );\n                      }\n\n                      const bobina = bobine.find(b => b.id_bobina === selectedCavo.id_bobina);\n                      return bobina ? (\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Residui:</Typography>\n                            <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: 'success.main', fontWeight: 'bold' }}>\n                              {bobina.metri_residui || 0} m\n                            </Typography>\n                          </Box>\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>\n                            <Chip\n                              size=\"small\"\n                              label={bobina.stato_bobina || 'N/D'}\n                              color={bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'}\n                              sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}\n                            />\n                          </Box>\n                        </Box>\n                      ) : null;\n                    })()}\n                  </Box>\n                </>\n              )}\n            </Box>\n          )}\n        </Box>\n      </Paper>\n\n      {/* Lista cavi - versione compatta */}\n      <Paper sx={{ p: 1.5, width: '100%' }}>\n        <Typography variant=\"subtitle2\" sx={{ mb: 1 }}>\n          Seleziona dalla lista\n        </Typography>\n\n        {caviLoading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n            <CircularProgress size={24} />\n          </Box>\n        ) : cavi.length === 0 ? (\n          <Alert severity=\"info\" sx={{ py: 0.5 }}>\n            <Typography variant=\"caption\">Non ci sono cavi posati disponibili.</Typography>\n          </Alert>\n        ) : (\n          <TableContainer component={Paper} variant=\"outlined\" sx={{ maxHeight: '300px', overflow: 'auto', width: '100%' }}>\n            <Table size=\"small\" stickyHeader>\n              <TableHead>\n                <TableRow sx={{ '& th': { fontWeight: 'bold', py: 1, bgcolor: '#f5f5f5' } }}>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Tipologia</TableCell>\n                  <TableCell>Formazione</TableCell>\n                  <TableCell>Metri</TableCell>\n                  <TableCell>Bobina</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell align=\"center\" sx={{ width: '40px' }}>Info</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {cavi.map((cavo) => (\n                  <TableRow\n                    key={cavo.id_cavo}\n                    hover\n                    onClick={() => handleSelectCavo(cavo)}\n                    sx={{\n                      cursor: 'pointer',\n                      '&:hover': { bgcolor: '#f1f8e9' },\n                      '& td': { py: 0.5 }\n                    }}\n                  >\n                    <TableCell sx={{ fontWeight: 'medium' }}>{cavo.id_cavo}</TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.sezione || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metratura_reale || 'N/A'} m</TableCell>\n                    <TableCell>\n                      {cavo.id_bobina ? (cavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(cavo.id_bobina)) : 'VUOTA'}\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        size=\"small\"\n                        label=\"Installato\"\n                        color=\"success\"\n                        sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.7rem' } }}\n                      />\n                    </TableCell>\n                    <TableCell align=\"center\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          setSelectedCavo(cavo);\n                          setShowCavoDetailsDialog(true);\n                        }}\n                      >\n                        <InfoIcon fontSize=\"small\" />\n                      </IconButton>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        )}\n      </Paper>\n    </Box>\n  );\n\n\n\n  // Renderizza le opzioni di modifica\n  const renderModificaOptions = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Opzioni di modifica\n        </Typography>\n\n        <RadioGroup\n          value={selectedOption}\n          onChange={handleOptionChange}\n        >\n          <FormControlLabel\n            value=\"assegnaNuova\"\n            control={<Radio />}\n            label=\"Assegna nuova bobina\"\n          />\n          <FormControlLabel\n            value=\"rimuoviBobina\"\n            control={<Radio />}\n            label=\"Rimuovi bobina attuale\"\n          />\n          <FormControlLabel\n            value=\"annullaInstallazione\"\n            control={<Radio />}\n            label=\"Annulla installazione\"\n          />\n        </RadioGroup>\n\n        {selectedOption === 'assegnaNuova' && renderBobineSelection()}\n\n        {selectedOption === 'rimuoviBobina' && (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Questa operazione rimuoverà l'associazione con la bobina attuale, assegnando una \"BOBINA_VUOTA\" al cavo.\n            Il cavo rimarrà nello stato posato e i metri posati rimarranno invariati.\n            La bobina attuale (se presente) riavrà i suoi metri restituiti.\n          </Alert>\n        )}\n\n        {selectedOption === 'annullaInstallazione' && (\n          <Alert severity=\"warning\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" fontWeight=\"bold\">\n              ATTENZIONE: Questa operazione annullerà completamente l'installazione del cavo.\n            </Typography>\n            <Typography variant=\"body2\">\n              - Il cavo tornerà allo stato \"Da installare\"<br />\n              - La metratura reale sarà azzerata<br />\n              - L'associazione con la bobina sarà rimossa<br />\n              - I metri posati saranno restituiti alla bobina originale (se presente)\n            </Typography>\n          </Alert>\n        )}\n\n        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n          <Button\n            variant=\"outlined\"\n            color=\"secondary\"\n            startIcon={<CancelIcon />}\n            onClick={handleCloseForm}\n            disabled={loading}\n          >\n            Annulla operazione\n          </Button>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<SaveIcon />}\n            onClick={handleSave}\n            disabled={loading || !selectedOption || (selectedOption === 'assegnaNuova' && !selectedBobinaId)}\n          >\n            Salva modifiche\n          </Button>\n        </Box>\n      </Paper>\n    );\n  };\n\n  // Renderizza la selezione delle bobine\n  const renderBobineSelection = () => {\n    if (bobineLoading) {\n      return (\n        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n          <CircularProgress />\n        </Box>\n      );\n    }\n\n    // Filtra le bobine in base al testo di ricerca\n    const bobineFiltrate = bobine.filter(bobina => {\n      const searchLower = bobinaSearchText.toLowerCase();\n      return !bobinaSearchText ||\n        getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower) ||\n        String(bobina.tipologia || '').toLowerCase().includes(searchLower) ||\n        String(bobina.sezione || '').toLowerCase().includes(searchLower);\n    });\n\n    // Separa le bobine compatibili e non compatibili\n    const bobineCompatibili = selectedCavo\n      ? bobineFiltrate.filter(bobina =>\n          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n          String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim() &&\n          (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'))\n      : [];\n\n    const bobineNonCompatibili = selectedCavo\n      ? bobineFiltrate.filter(bobina =>\n          !(String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n            String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim()) &&\n          (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'))\n      : [];\n\n    // Ordina per metri residui (decrescente)\n    bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n    bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n    return (\n      <Box sx={{ mt: 2 }}>\n        {/* Campo di ricerca rapida */}\n        <Box sx={{ mb: 2 }}>\n          <TextField\n            fullWidth\n            label=\"Ricerca bobina (ID, tipologia, formazione)\"\n            value={bobinaSearchText}\n            onChange={(e) => setBobinaSearchText(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon />\n                </InputAdornment>\n              )\n            }}\n            sx={{ mb: 2 }}\n          />\n        </Box>\n\n        {/* Input numerico per selezione diretta */}\n        <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>\n          <TextField\n            label=\"Inserisci numero bobina (es. 3)\"\n            value={bobinaNumericInput}\n            onChange={(e) => setBobinaNumericInput(e.target.value)}\n            type=\"number\"\n            sx={{ width: '250px' }}\n          />\n          <Button\n            variant=\"outlined\"\n            onClick={handleSelectBobinaByNumber}\n            disabled={!bobinaNumericInput.trim()}\n          >\n            Seleziona\n          </Button>\n        </Box>\n\n        {/* Bobina selezionata */}\n        {selectedBobinaId && (\n          <Alert severity=\"success\" sx={{ mb: 2 }}>\n            Bobina selezionata: <strong>{getBobinaNumber(selectedBobinaId)}</strong>\n          </Alert>\n        )}\n\n        {/* Lista bobine compatibili */}\n        {bobineCompatibili.length > 0 && (\n          <Paper sx={{ mb: 2 }}>\n            <Box sx={{ p: 2, bgcolor: '#e8f5e8' }}>\n              <Typography variant=\"h6\" sx={{ color: '#2e7d32', fontWeight: 'bold' }}>\n                Bobine compatibili ({bobineCompatibili.length})\n              </Typography>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipologia</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Formazione</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Metri</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>\n                </Box>\n              </Box>\n            </Box>\n            <List sx={{ maxHeight: '300px', overflow: 'auto', bgcolor: 'background.paper' }}>\n              {bobineCompatibili.map((bobina) => (\n                <ListItem\n                  key={bobina.id_bobina}\n                  disablePadding\n                  secondaryAction={\n                    <IconButton\n                      edge=\"end\"\n                      size=\"small\"\n                      onClick={() => handleSelectBobina(bobina.id_bobina)}\n                      color={selectedBobinaId === bobina.id_bobina ? 'success' : 'primary'}\n                    >\n                      <AddCircleOutlineIcon />\n                    </IconButton>\n                  }\n                  sx={{\n                    bgcolor: selectedBobinaId === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                    borderRadius: '4px',\n                    mb: 0.5,\n                    '&:hover': {\n                      bgcolor: selectedBobinaId === bobina.id_bobina ? 'rgba(76, 175, 80, 0.2)' : 'rgba(0, 0, 0, 0.04)'\n                    }\n                  }}\n                >\n                  <ListItemText\n                    primary={\n                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\" sx={{ fontWeight: selectedBobinaId === bobina.id_bobina ? 'bold' : 'normal' }}>\n                            {getBobinaNumber(bobina.id_bobina)}\n                          </Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\">{bobina.tipologia}</Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\">{bobina.sezione}</Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\">{bobina.metri_residui}m</Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Chip\n                            label={bobina.stato_bobina}\n                            size=\"small\"\n                            color={bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'}\n                          />\n                        </Box>\n                      </Box>\n                    }\n                    sx={{\n                      bgcolor: selectedBobinaId === bobina.id_bobina ? '#e8f5e8' : 'transparent',\n                      borderRadius: 1,\n                      px: 1\n                    }}\n                  />\n                </ListItem>\n              ))}\n            </List>\n          </Paper>\n        )}\n\n        {/* Lista bobine non compatibili */}\n        {bobineNonCompatibili.length > 0 && (\n          <Paper sx={{ mb: 2 }}>\n            <Box sx={{ p: 2, bgcolor: '#fff3e0' }}>\n              <Typography variant=\"h6\" sx={{ color: '#f57c00', fontWeight: 'bold' }}>\n                Bobine non compatibili ({bobineNonCompatibili.length})\n              </Typography>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                Queste bobine non corrispondono alle caratteristiche del cavo\n              </Typography>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipologia</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Formazione</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Metri</Typography>\n                </Box>\n                <Box sx={{ flex: 1 }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>\n                </Box>\n              </Box>\n            </Box>\n            <List sx={{ maxHeight: '300px', overflow: 'auto', bgcolor: 'background.paper' }}>\n              {bobineNonCompatibili.map((bobina) => (\n                <ListItem\n                  key={bobina.id_bobina}\n                  disablePadding\n                  secondaryAction={\n                    <IconButton\n                      edge=\"end\"\n                      size=\"small\"\n                      onClick={() => {\n                        onError(`Bobina ${getBobinaNumber(bobina.id_bobina)} non compatibile. Tipologia: ${bobina.tipologia}, Formazione: ${bobina.sezione}`);\n                      }}\n                      color=\"warning\"\n                    >\n                      <WarningIcon />\n                    </IconButton>\n                  }\n                  sx={{\n                    borderRadius: '4px',\n                    mb: 0.5,\n                    '&:hover': {\n                      bgcolor: 'rgba(255, 152, 0, 0.08)'\n                    }\n                  }}\n                >\n                  <ListItemText\n                    primary={\n                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\">{getBobinaNumber(bobina.id_bobina)}</Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\" color={bobina.tipologia !== selectedCavo?.tipologia ? 'error' : 'text.primary'}>\n                            {bobina.tipologia}\n                          </Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\" color={String(bobina.sezione) !== String(selectedCavo?.sezione) ? 'error' : 'text.primary'}>\n                            {bobina.sezione}\n                          </Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Typography variant=\"body2\">{bobina.metri_residui}m</Typography>\n                        </Box>\n                        <Box sx={{ flex: 1 }}>\n                          <Chip\n                            label={bobina.stato_bobina}\n                            size=\"small\"\n                            color={bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'}\n                          />\n                        </Box>\n                      </Box>\n                    }\n                  />\n                </ListItem>\n              ))}\n            </List>\n          </Paper>\n        )}\n\n        {/* Messaggio se non ci sono bobine */}\n        {bobineCompatibili.length === 0 && bobineNonCompatibili.length === 0 && (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            {bobinaSearchText ? 'Nessuna bobina trovata con i criteri di ricerca specificati.' : 'Non ci sono bobine disponibili.'}\n          </Alert>\n        )}\n      </Box>\n    );\n  };\n\n  return (\n    <Box>\n      {/* Form per la selezione del cavo */}\n      {renderCavoSelectionForm()}\n\n      {/* Opzioni di modifica */}\n      {renderModificaOptions()}\n\n      {/* Dialog per la visualizzazione dei dettagli completi del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>Dettagli completi del cavo</DialogTitle>\n        <DialogContent>\n          {selectedCavo && <CavoDetailsView cavo={selectedCavo} />}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog di conferma */}\n      <Dialog\n        open={showConfirmDialog}\n        onClose={() => setShowConfirmDialog(false)}\n      >\n        <DialogTitle>Conferma operazione</DialogTitle>\n        <DialogContent>\n          <Typography>{confirmDialogMessage}</Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button\n            onClick={() => setShowConfirmDialog(false)}\n            color=\"primary\"\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={() => {\n              setShowConfirmDialog(false);\n              if (confirmDialogAction) confirmDialogAction();\n            }}\n            color=\"primary\"\n            variant=\"contained\"\n            autoFocus\n          >\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ModificaBobinaForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,UAAU,EACVC,cAAc,EACdC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,IAAI,QACC,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,gBAAgB,IAAIC,oBAAoB,QACnC,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwE,WAAW,EAAEC,cAAc,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0E,aAAa,EAAEC,gBAAgB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4E,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC8E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACgF,IAAI,EAAEC,OAAO,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACkF,MAAM,EAAEC,SAAS,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACoF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACsF,YAAY,EAAEC,eAAe,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACwF,WAAW,EAAEC,cAAc,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0F,cAAc,EAAEC,iBAAiB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC8F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACgG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM,CAACkG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACoG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACsG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvG,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACwG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzG,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACdyG,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACzC,UAAU,CAAC,CAAC;;EAEhB;EACAhE,SAAS,CAAC,MAAM;IACd,IAAIqF,YAAY,EAAE;MAChBqB,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACrB,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMoB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFjC,cAAc,CAAC,IAAI,CAAC;MACpBmC,OAAO,CAACC,GAAG,CAAC,oCAAoC5C,UAAU,KAAK,CAAC;;MAEhE;MACA,MAAM6C,QAAQ,GAAG,MAAM9D,WAAW,CAAC+D,OAAO,CAAC9C,UAAU,EAAE,IAAI,EAAE,YAAY,CAAC;MAC1E2C,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACE,MAAM,kBAAkB,CAAC;;MAE1D;MACA,MAAMC,cAAc,GAAGH,QAAQ,CAACI,MAAM,CAACC,IAAI,IACzCC,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,GAAG,CAAC,IAAIF,IAAI,CAACG,mBAAmB,KAAK,YACvE,CAAC;MAEDrC,OAAO,CAACgC,cAAc,CAAC;MACvBL,OAAO,CAACC,GAAG,CAAC,YAAYI,cAAc,CAACD,MAAM,iCAAiC,CAAC;IACjF,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/DpD,OAAO,CAAC,0CAA0C,IAAIoD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC/G,CAAC,SAAS;MACRhD,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMkC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACrB,YAAY,EAAE;IAEnB,IAAI;MACFX,gBAAgB,CAAC,IAAI,CAAC;MACtBiC,OAAO,CAACC,GAAG,CAAC,sCAAsC5C,UAAU,KAAK,CAAC;;MAElE;MACA,MAAMyD,UAAU,GAAG,MAAMzE,gBAAgB,CAAC0E,SAAS,CAAC1D,UAAU,CAAC;MAC/D2C,OAAO,CAACC,GAAG,CAAC,YAAYa,UAAU,CAACV,MAAM,SAAS,CAAC;;MAEnD;MACA,MAAMY,oBAAoB,GAAGF,UAAU,CAACR,MAAM,CAACW,MAAM,IACnDA,MAAM,CAACC,SAAS,KAAKxC,YAAY,CAACwC,SAAS,IAC3CD,MAAM,CAACE,OAAO,KAAKzC,YAAY,CAACyC,OAAO,KACtCF,MAAM,CAACG,YAAY,KAAK,aAAa,IAAIH,MAAM,CAACG,YAAY,KAAK,QAAQ,CAC5E,CAAC;MAED7C,SAAS,CAACuC,UAAU,CAAC;MACrBrC,mBAAmB,CAACuC,oBAAoB,CAAC;MACzChB,OAAO,CAACC,GAAG,CAAC,YAAYe,oBAAoB,CAACZ,MAAM,qBAAqB,CAAC;IAC3E,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnEpD,OAAO,CAAC,8CAA8C,IAAIoD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACnH,CAAC,SAAS;MACR9C,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMsD,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACzC,WAAW,CAAC0C,IAAI,CAAC,CAAC,EAAE;MACvB/D,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFI,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM4C,IAAI,GAAG,MAAMnE,WAAW,CAACmF,WAAW,CAAClE,UAAU,EAAEuB,WAAW,CAAC0C,IAAI,CAAC,CAAC,CAAC;;MAE1E;MACA,IAAId,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACG,mBAAmB,KAAK,YAAY,EAAE;QACtFnD,OAAO,CAAC,4CAA4C,CAAC;QACrDI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEAgB,eAAe,CAAC4B,IAAI,CAAC;MACrBxB,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;MACvBE,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;MACzBd,oBAAoB,CAAC,KAAK,CAAC;IAC7B,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DpD,OAAO,CAAC,sCAAsC,IAAIoD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC3G,CAAC,SAAS;MACRlD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6D,gBAAgB,GAAIjB,IAAI,IAAK;IACjC5B,eAAe,CAAC4B,IAAI,CAAC;IACrBxB,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;IACvBE,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;IACzBd,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMsD,kBAAkB,GAAIC,KAAK,IAAK;IACpC3C,iBAAiB,CAAC2C,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;IACrC3C,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM4C,kBAAkB,GAAIC,QAAQ,IAAK;IACvC7C,mBAAmB,CAAC6C,QAAQ,CAAC;IAC7B;IACAzC,qBAAqB,CAAC0C,eAAe,CAACD,QAAQ,CAAC,CAAC;EAClD,CAAC;;EAED;EACA,MAAME,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAI,CAAC5C,kBAAkB,CAACkC,IAAI,CAAC,CAAC,EAAE;MAC9B/D,OAAO,CAAC,sCAAsC,CAAC;MAC/C;IACF;;IAEA;IACA,MAAM0E,YAAY,GAAG,IAAI5E,UAAU,KAAK+B,kBAAkB,CAACkC,IAAI,CAAC,CAAC,EAAE;;IAEnE;IACA,MAAML,MAAM,GAAG3C,MAAM,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKH,YAAY,CAAC;IAC7D,IAAI,CAAChB,MAAM,EAAE;MACX1D,OAAO,CAAC,UAAU6B,kBAAkB,2BAA2B,CAAC;MAChE;IACF;;IAEA;IACA,MAAMiD,YAAY,GAAG3D,YAAY,IAC/B4D,MAAM,CAACrB,MAAM,CAACC,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKgB,MAAM,CAAC5D,YAAY,CAACwC,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFgB,MAAM,CAACrB,MAAM,CAACE,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKgB,MAAM,CAAC5D,YAAY,CAACyC,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC;IAEnF,IAAI,CAACe,YAAY,EAAE;MACjB9E,OAAO,CAAC,UAAU6B,kBAAkB,4CAA4C,CAAC;MACjF;IACF;;IAEA;IACAH,mBAAmB,CAACgD,YAAY,CAAC;EACnC,CAAC;;EAED;EACA,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAAC7D,YAAY,EAAE;MACjBnB,OAAO,CAAC,sCAAsC,CAAC;MAC/C;IACF;IAEA,IAAI,CAACuB,cAAc,EAAE;MACnBvB,OAAO,CAAC,0CAA0C,CAAC;MACnD;IACF;;IAEA;IACA,IAAIuB,cAAc,KAAK,cAAc,IAAI,CAACE,gBAAgB,EAAE;MAC1DzB,OAAO,CAAC,yCAAyC,CAAC;MAClD;IACF;;IAEA;IACA,IAAIsD,OAAO,GAAG,EAAE;IAChB,IAAI2B,MAAM,GAAG,IAAI;IAEjB,IAAI1D,cAAc,KAAK,cAAc,EAAE;MACrC,MAAMmC,MAAM,GAAG3C,MAAM,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKpD,gBAAgB,CAAC;MACjE6B,OAAO,GAAG,2CAA2CkB,eAAe,CAAC/C,gBAAgB,CAAC,YAAYN,YAAY,CAAC+D,OAAO,GAAG;MACzHD,MAAM,GAAGA,CAAA,KAAME,YAAY,CAAC1D,gBAAgB,CAAC;IAC/C,CAAC,MAAM,IAAIF,cAAc,KAAK,eAAe,EAAE;MAC7C+B,OAAO,GAAG,4DAA4DnC,YAAY,CAAC+D,OAAO,GAAG;MAC7FD,MAAM,GAAGA,CAAA,KAAME,YAAY,CAAC,cAAc,CAAC;IAC7C,CAAC,MAAM,IAAI5D,cAAc,KAAK,sBAAsB,EAAE;MACpD+B,OAAO,GAAG,oEAAoEnC,YAAY,CAAC+D,OAAO,iGAAiG;MACnMD,MAAM,GAAGA,CAAA,KAAMG,oBAAoB,CAAC,CAAC;IACvC;IAEAlD,uBAAuB,CAACoB,OAAO,CAAC;IAChClB,sBAAsB,CAAC,MAAM6C,MAAM,CAAC;IACpCjD,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMmD,YAAY,GAAG,MAAOZ,QAAQ,IAAK;IACvC,IAAI;MACFnE,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMvB,WAAW,CAACsG,YAAY,CAACrF,UAAU,EAAEqB,YAAY,CAAC+D,OAAO,EAAEX,QAAQ,CAAC;MAE1ExE,SAAS,CAAC,UAAUwE,QAAQ,KAAK,cAAc,GAAG,iBAAiB,GAAG,WAAW,eAAe,CAAC;;MAEjG;MACAnD,eAAe,CAAC,IAAI,CAAC;MACrBI,iBAAiB,CAAC,EAAE,CAAC;MACrBE,mBAAmB,CAAC,EAAE,CAAC;MACvBJ,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAiB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrEpD,OAAO,CAAC,gDAAgD,IAAIoD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACrH,CAAC,SAAS;MACRlD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgF,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFhF,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMvB,WAAW,CAACwG,kBAAkB,CAACvF,UAAU,EAAEqB,YAAY,CAAC+D,OAAO,CAAC;MAEtEnF,SAAS,CAAC,0BAA0BoB,YAAY,CAAC+D,OAAO,yBAAyB,CAAC;;MAElF;MACA9D,eAAe,CAAC,IAAI,CAAC;MACrBI,iBAAiB,CAAC,EAAE,CAAC;MACrBE,mBAAmB,CAAC,EAAE,CAAC;MACvBJ,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAiB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;MAC3EpD,OAAO,CAAC,sDAAsD,IAAIoD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC3H,CAAC,SAAS;MACRlD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkF,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACAlE,eAAe,CAAC,IAAI,CAAC;IACrBI,iBAAiB,CAAC,EAAE,CAAC;IACrBE,mBAAmB,CAAC,EAAE,CAAC;IACvBJ,cAAc,CAAC,EAAE,CAAC;IAClBV,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACAb,SAAS,CAAC,sBAAsB,CAAC;EACnC,CAAC;;EAED;EACA,MAAMyE,eAAe,GAAIe,QAAQ,IAAK;IACpC,IAAIA,QAAQ,KAAK,cAAc,EAAE,OAAO,cAAc;;IAEtD;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOF,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMG,uBAAuB,GAAGA,CAAA,kBAC9BhG,OAAA,CAAC3D,GAAG;IAAA4J,QAAA,gBACFjG,OAAA,CAACzD,UAAU;MAAC2J,OAAO,EAAC,WAAW;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAAJ,QAAA,EAAC;IAEnE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbzG,OAAA,CAAC1D,KAAK;MAAC6J,EAAE,EAAE;QAAEO,CAAC,EAAE,GAAG;QAAEN,EAAE,EAAE,CAAC;QAAEO,KAAK,EAAE;MAAO,CAAE;MAAAV,QAAA,eAC1CjG,OAAA,CAAC3D,GAAG;QAAC8J,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEF,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBAChEjG,OAAA,CAACzD,UAAU;UAAC2J,OAAO,EAAC,WAAW;UAACC,EAAE,EAAE;YAAEW,EAAE,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAd,QAAA,EAAC;QAEjE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzG,OAAA,CAACxD,SAAS;UACRwK,IAAI,EAAC,OAAO;UACZC,KAAK,EAAC,SAAS;UACff,OAAO,EAAC,UAAU;UAClBvB,KAAK,EAAEhD,WAAY;UACnBuF,QAAQ,EAAGC,CAAC,IAAKvF,cAAc,CAACuF,CAAC,CAACzC,MAAM,CAACC,KAAK,CAAE;UAChDyC,WAAW,EAAC,yBAAyB;UACrCjB,EAAE,EAAE;YAAEkB,QAAQ,EAAE,CAAC;YAAEV,KAAK,EAAE,OAAO;YAAEG,EAAE,EAAE;UAAE;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACFzG,OAAA,CAACvD,MAAM;UACLyJ,OAAO,EAAC,WAAW;UACnBoB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEnD,oBAAqB;UAC9BoD,QAAQ,EAAE7G,WAAW,IAAI,CAACgB,WAAW,CAAC0C,IAAI,CAAC,CAAE;UAC7CoD,SAAS,EAAE9G,WAAW,gBAAGX,OAAA,CAAC/C,gBAAgB;YAAC+J,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGzG,OAAA,CAACzB,UAAU;YAACmJ,QAAQ,EAAC;UAAO;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1FO,IAAI,EAAC,OAAO;UACZb,EAAE,EAAE;YAAEY,QAAQ,EAAE,MAAM;YAAEY,MAAM,EAAE,MAAM;YAAEb,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,EACjD;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAGRhF,YAAY,iBACXzB,OAAA,CAAC3D,GAAG;UAAC8J,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEQ,QAAQ,EAAE,CAAC;YAAEO,QAAQ,EAAE,QAAQ;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA7B,QAAA,gBAC7GjG,OAAA,CAAC3D,GAAG;YAAC8J,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,gBACxDjG,OAAA,CAACzD,UAAU;cAAC2J,OAAO,EAAC,WAAW;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAE0B,UAAU,EAAE,QAAQ;gBAAEjB,EAAE,EAAE,CAAC;gBAAEY,QAAQ,EAAE;cAAU,CAAE;cAAAzB,QAAA,GAAC,QACtG,eAAAjG,OAAA;gBAAMgI,KAAK,EAAE;kBAAEV,KAAK,EAAE;gBAAU,CAAE;gBAAArB,QAAA,EAAExE,YAAY,CAAC+D;cAAO;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACbzG,OAAA,CAACjD,OAAO;cAACkL,WAAW,EAAC,UAAU;cAACC,QAAQ;cAAC/B,EAAE,EAAE;gBAAEgC,EAAE,EAAE;cAAI;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DzG,OAAA,CAAC3D,GAAG;cAAC8J,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEuB,GAAG,EAAE,CAAC;gBAAER,QAAQ,EAAE,QAAQ;gBAAEC,QAAQ,EAAE;cAAS,CAAE;cAAA5B,QAAA,gBACjGjG,OAAA,CAAC3D,GAAG;gBAAC8J,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEkB,UAAU,EAAE;gBAAS,CAAE;gBAAA9B,QAAA,gBACvEjG,OAAA,CAACzD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,QAAQ,EAAE,QAAQ;oBAAEZ,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzGzG,OAAA,CAACzD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEuB,QAAQ,EAAE;kBAAS,CAAE;kBAAAzB,QAAA,EAAExE,YAAY,CAACwC,SAAS,IAAI;gBAAK;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnG,CAAC,eACNzG,OAAA,CAAC3D,GAAG;gBAAC8J,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEkB,UAAU,EAAE;gBAAS,CAAE;gBAAA9B,QAAA,gBACvEjG,OAAA,CAACzD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,QAAQ,EAAE,QAAQ;oBAAEZ,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzGzG,OAAA,CAACzD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEuB,QAAQ,EAAE;kBAAS,CAAE;kBAAAzB,QAAA,EAAExE,YAAY,CAACyC,OAAO,IAAI;gBAAK;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjG,CAAC,eACNzG,OAAA,CAAC3D,GAAG;gBAAC8J,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEkB,UAAU,EAAE;gBAAS,CAAE;gBAAA9B,QAAA,gBACvEjG,OAAA,CAACzD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,QAAQ,EAAE,QAAQ;oBAAEZ,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1GzG,OAAA,CAACzD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEuB,QAAQ,EAAE;kBAAS,CAAE;kBAAAzB,QAAA,GAAExE,YAAY,CAAC+B,eAAe,IAAI,KAAK,EAAC,IAAE;gBAAA;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3G,CAAC,eACNzG,OAAA,CAAC3D,GAAG;gBAAC8J,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEkB,UAAU,EAAE;gBAAS,CAAE;gBAAA9B,QAAA,gBACvEjG,OAAA,CAACzD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,QAAQ,EAAE,QAAQ;oBAAEZ,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1GzG,OAAA,CAAC3B,IAAI;kBACH2I,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAExF,YAAY,CAACgC,mBAAmB,IAAI,KAAM;kBACjD6D,KAAK,EAAC,SAAS;kBACfnB,EAAE,EAAE;oBAAEwB,MAAM,EAAE,MAAM;oBAAE,kBAAkB,EAAE;sBAAEU,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE,CAAC;sBAAEZ,QAAQ,EAAE;oBAAU;kBAAE;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLhF,YAAY,CAAC0D,SAAS,iBACrBnF,OAAA,CAAAE,SAAA;YAAA+F,QAAA,gBACEjG,OAAA,CAACjD,OAAO;cAACkL,WAAW,EAAC,UAAU;cAACC,QAAQ;cAAC/B,EAAE,EAAE;gBAAEgC,EAAE,EAAE;cAAE;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DzG,OAAA,CAAC3D,GAAG;cAAC8J,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAZ,QAAA,gBACjDjG,OAAA,CAACzD,UAAU;gBAAC2J,OAAO,EAAC,WAAW;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAE0B,UAAU,EAAE,QAAQ;kBAAEjB,EAAE,EAAE,CAAC;kBAAEY,QAAQ,EAAE,SAAS;kBAAEJ,KAAK,EAAE;gBAAU,CAAE;gBAAArB,QAAA,GAAC,UACtH,eAAAjG,OAAA;kBAAAiG,QAAA,EAAOxE,YAAY,CAAC0D,SAAS,KAAK,cAAc,GAAG,OAAO,GAAGL,eAAe,CAACrD,YAAY,CAAC0D,SAAS;gBAAC;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC,EACZ,CAAC,MAAM;gBACN,IAAIhF,YAAY,CAAC0D,SAAS,KAAK,cAAc,EAAE;kBAC7C,oBACEnF,OAAA,CAAC3D,GAAG;oBAAC8J,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,eACvEjG,OAAA,CAACzD,UAAU;sBAAC2J,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEuB,QAAQ,EAAE,QAAQ;wBAAEJ,KAAK,EAAE,gBAAgB;wBAAEiB,SAAS,EAAE;sBAAS,CAAE;sBAAAtC,QAAA,EAAC;oBAEtG;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAEV;gBAEA,MAAMzC,MAAM,GAAG3C,MAAM,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAK1D,YAAY,CAAC0D,SAAS,CAAC;gBACvE,OAAOnB,MAAM,gBACXhE,OAAA,CAAC3D,GAAG;kBAAC8J,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEuB,GAAG,EAAE,CAAC;oBAAER,QAAQ,EAAE,QAAQ;oBAAEC,QAAQ,EAAE;kBAAS,CAAE;kBAAA5B,QAAA,gBACjGjG,OAAA,CAAC3D,GAAG;oBAAC8J,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,gBACvEjG,OAAA,CAACzD,UAAU;sBAAC2J,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,QAAQ;wBAAEqB,QAAQ,EAAE,QAAQ;wBAAEZ,EAAE,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EAAC;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC5GzG,OAAA,CAACzD,UAAU;sBAAC2J,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEuB,QAAQ,EAAE,QAAQ;wBAAEJ,KAAK,EAAE,cAAc;wBAAEjB,UAAU,EAAE;sBAAO,CAAE;sBAAAJ,QAAA,GAC/FjC,MAAM,CAACwE,aAAa,IAAI,CAAC,EAAC,IAC7B;oBAAA;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNzG,OAAA,CAAC3D,GAAG;oBAAC8J,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,gBACvEjG,OAAA,CAACzD,UAAU;sBAAC2J,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,QAAQ;wBAAEqB,QAAQ,EAAE,QAAQ;wBAAEZ,EAAE,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC1GzG,OAAA,CAAC3B,IAAI;sBACH2I,IAAI,EAAC,OAAO;sBACZC,KAAK,EAAEjD,MAAM,CAACG,YAAY,IAAI,KAAM;sBACpCmD,KAAK,EAAEtD,MAAM,CAACG,YAAY,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;sBACrEgC,EAAE,EAAE;wBAAEwB,MAAM,EAAE,MAAM;wBAAE,kBAAkB,EAAE;0BAAEU,EAAE,EAAE,CAAC;0BAAEC,EAAE,EAAE,CAAC;0BAAEZ,QAAQ,EAAE;wBAAU;sBAAE;oBAAE;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACJ,IAAI;cACV,CAAC,EAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,eACN,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRzG,OAAA,CAAC1D,KAAK;MAAC6J,EAAE,EAAE;QAAEO,CAAC,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAAV,QAAA,gBACnCjG,OAAA,CAACzD,UAAU;QAAC2J,OAAO,EAAC,WAAW;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAE/C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZ9F,WAAW,gBACVX,OAAA,CAAC3D,GAAG;QAAC8J,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAE6B,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAzC,QAAA,eAC5DjG,OAAA,CAAC/C,gBAAgB;UAAC+J,IAAI,EAAE;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,GACJtF,IAAI,CAACgC,MAAM,KAAK,CAAC,gBACnBnD,OAAA,CAAChD,KAAK;QAAC2L,QAAQ,EAAC,MAAM;QAACxC,EAAE,EAAE;UAAEmC,EAAE,EAAE;QAAI,CAAE;QAAArC,QAAA,eACrCjG,OAAA,CAACzD,UAAU;UAAC2J,OAAO,EAAC,SAAS;UAAAD,QAAA,EAAC;QAAoC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,gBAERzG,OAAA,CAACvC,cAAc;QAACmL,SAAS,EAAEtM,KAAM;QAAC4J,OAAO,EAAC,UAAU;QAACC,EAAE,EAAE;UAAE0C,SAAS,EAAE,OAAO;UAAEhB,QAAQ,EAAE,MAAM;UAAElB,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,eAC/GjG,OAAA,CAAC1C,KAAK;UAAC0J,IAAI,EAAC,OAAO;UAAC8B,YAAY;UAAA7C,QAAA,gBAC9BjG,OAAA,CAACtC,SAAS;YAAAuI,QAAA,eACRjG,OAAA,CAACrC,QAAQ;cAACwI,EAAE,EAAE;gBAAE,MAAM,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEiC,EAAE,EAAE,CAAC;kBAAES,OAAO,EAAE;gBAAU;cAAE,CAAE;cAAA9C,QAAA,gBAC1EjG,OAAA,CAACxC,SAAS;gBAAAyI,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BzG,OAAA,CAACxC,SAAS;gBAAAyI,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCzG,OAAA,CAACxC,SAAS;gBAAAyI,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjCzG,OAAA,CAACxC,SAAS;gBAAAyI,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BzG,OAAA,CAACxC,SAAS;gBAAAyI,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BzG,OAAA,CAACxC,SAAS;gBAAAyI,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BzG,OAAA,CAACxC,SAAS;gBAACwL,KAAK,EAAC,QAAQ;gBAAC7C,EAAE,EAAE;kBAAEQ,KAAK,EAAE;gBAAO,CAAE;gBAAAV,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZzG,OAAA,CAACzC,SAAS;YAAA0I,QAAA,EACP9E,IAAI,CAAC8H,GAAG,CAAE3F,IAAI,iBACbtD,OAAA,CAACrC,QAAQ;cAEPuL,KAAK;cACL3B,OAAO,EAAEA,CAAA,KAAMhD,gBAAgB,CAACjB,IAAI,CAAE;cACtC6C,EAAE,EAAE;gBACFgD,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE;kBAAEJ,OAAO,EAAE;gBAAU,CAAC;gBACjC,MAAM,EAAE;kBAAET,EAAE,EAAE;gBAAI;cACpB,CAAE;cAAArC,QAAA,gBAEFjG,OAAA,CAACxC,SAAS;gBAAC2I,EAAE,EAAE;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,EAAE3C,IAAI,CAACkC;cAAO;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnEzG,OAAA,CAACxC,SAAS;gBAAAyI,QAAA,EAAE3C,IAAI,CAACW,SAAS,IAAI;cAAK;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChDzG,OAAA,CAACxC,SAAS;gBAAAyI,QAAA,EAAE3C,IAAI,CAACY,OAAO,IAAI;cAAK;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9CzG,OAAA,CAACxC,SAAS;gBAAAyI,QAAA,GAAE3C,IAAI,CAACE,eAAe,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACxDzG,OAAA,CAACxC,SAAS;gBAAAyI,QAAA,EACP3C,IAAI,CAAC6B,SAAS,GAAI7B,IAAI,CAAC6B,SAAS,KAAK,cAAc,GAAG,OAAO,GAAGL,eAAe,CAACxB,IAAI,CAAC6B,SAAS,CAAC,GAAI;cAAO;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClG,CAAC,eACZzG,OAAA,CAACxC,SAAS;gBAAAyI,QAAA,eACRjG,OAAA,CAAC3B,IAAI;kBACH2I,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAC,YAAY;kBAClBK,KAAK,EAAC,SAAS;kBACfnB,EAAE,EAAE;oBAAEwB,MAAM,EAAE,MAAM;oBAAE,kBAAkB,EAAE;sBAAEU,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE,CAAC;sBAAEZ,QAAQ,EAAE;oBAAS;kBAAE;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZzG,OAAA,CAACxC,SAAS;gBAACwL,KAAK,EAAC,QAAQ;gBAAA/C,QAAA,eACvBjG,OAAA,CAACjC,UAAU;kBACTiJ,IAAI,EAAC,OAAO;kBACZO,OAAO,EAAGJ,CAAC,IAAK;oBACdA,CAAC,CAACiC,eAAe,CAAC,CAAC;oBACnB1H,eAAe,CAAC4B,IAAI,CAAC;oBACrBV,wBAAwB,CAAC,IAAI,CAAC;kBAChC,CAAE;kBAAAqD,QAAA,eAEFjG,OAAA,CAACjB,QAAQ;oBAAC2I,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAnCPnD,IAAI,CAACkC,OAAO;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoCT,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CACjB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;;EAID;EACA,MAAM4C,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAC5H,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEzB,OAAA,CAAC1D,KAAK;MAAC6J,EAAE,EAAE;QAAEO,CAAC,EAAE,CAAC;QAAEN,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACzBjG,OAAA,CAACzD,UAAU;QAAC2J,OAAO,EAAC,IAAI;QAACoD,YAAY;QAAArD,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbzG,OAAA,CAACnC,UAAU;QACT8G,KAAK,EAAE9C,cAAe;QACtBqF,QAAQ,EAAE1C,kBAAmB;QAAAyB,QAAA,gBAE7BjG,OAAA,CAAClC,gBAAgB;UACf6G,KAAK,EAAC,cAAc;UACpB4E,OAAO,eAAEvJ,OAAA,CAACpC,KAAK;YAAA0I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBQ,KAAK,EAAC;QAAsB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACFzG,OAAA,CAAClC,gBAAgB;UACf6G,KAAK,EAAC,eAAe;UACrB4E,OAAO,eAAEvJ,OAAA,CAACpC,KAAK;YAAA0I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBQ,KAAK,EAAC;QAAwB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACFzG,OAAA,CAAClC,gBAAgB;UACf6G,KAAK,EAAC,sBAAsB;UAC5B4E,OAAO,eAAEvJ,OAAA,CAACpC,KAAK;YAAA0I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBQ,KAAK,EAAC;QAAuB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,EAEZ5E,cAAc,KAAK,cAAc,IAAI2H,qBAAqB,CAAC,CAAC,EAE5D3H,cAAc,KAAK,eAAe,iBACjC7B,OAAA,CAAChD,KAAK;QAAC2L,QAAQ,EAAC,MAAM;QAACxC,EAAE,EAAE;UAAEsD,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,EAAC;MAItC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,EAEA5E,cAAc,KAAK,sBAAsB,iBACxC7B,OAAA,CAAChD,KAAK;QAAC2L,QAAQ,EAAC,SAAS;QAACxC,EAAE,EAAE;UAAEsD,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,gBACtCjG,OAAA,CAACzD,UAAU;UAAC2J,OAAO,EAAC,OAAO;UAACG,UAAU,EAAC,MAAM;UAAAJ,QAAA,EAAC;QAE9C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzG,OAAA,CAACzD,UAAU;UAAC2J,OAAO,EAAC,OAAO;UAAAD,QAAA,GAAC,mDACkB,eAAAjG,OAAA;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,yCAChB,eAAAzG,OAAA;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,kDACG,eAAAzG,OAAA;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,2EAEnD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR,eAEDzG,OAAA,CAAC3D,GAAG;QAAC8J,EAAE,EAAE;UAAEsD,EAAE,EAAE,CAAC;UAAE7C,OAAO,EAAE,MAAM;UAAE6B,cAAc,EAAE,UAAU;UAAEL,GAAG,EAAE;QAAE,CAAE;QAAAnC,QAAA,gBACtEjG,OAAA,CAACvD,MAAM;UACLyJ,OAAO,EAAC,UAAU;UAClBoB,KAAK,EAAC,WAAW;UACjBG,SAAS,eAAEzH,OAAA,CAACrB,UAAU;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Bc,OAAO,EAAE3B,eAAgB;UACzB4B,QAAQ,EAAE/G,OAAQ;UAAAwF,QAAA,EACnB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzG,OAAA,CAACvD,MAAM;UACLyJ,OAAO,EAAC,WAAW;UACnBoB,KAAK,EAAC,SAAS;UACfG,SAAS,eAAEzH,OAAA,CAACvB,QAAQ;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBc,OAAO,EAAEjC,UAAW;UACpBkC,QAAQ,EAAE/G,OAAO,IAAI,CAACoB,cAAc,IAAKA,cAAc,KAAK,cAAc,IAAI,CAACE,gBAAkB;UAAAkE,QAAA,EAClG;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEZ,CAAC;;EAED;EACA,MAAM+C,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI3I,aAAa,EAAE;MACjB,oBACEb,OAAA,CAAC3D,GAAG;QAAC8J,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAE6B,cAAc,EAAE,QAAQ;UAAEgB,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,eAC5DjG,OAAA,CAAC/C,gBAAgB;UAAAqJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAEV;;IAEA;IACA,MAAMiD,cAAc,GAAGrI,MAAM,CAACgC,MAAM,CAACW,MAAM,IAAI;MAC7C,MAAM2F,WAAW,GAAG1H,gBAAgB,CAAC2H,WAAW,CAAC,CAAC;MAClD,OAAO,CAAC3H,gBAAgB,IACtB6C,eAAe,CAACd,MAAM,CAACmB,SAAS,CAAC,CAACyE,WAAW,CAAC,CAAC,CAAC9D,QAAQ,CAAC6D,WAAW,CAAC,IACrEtE,MAAM,CAACrB,MAAM,CAACC,SAAS,IAAI,EAAE,CAAC,CAAC2F,WAAW,CAAC,CAAC,CAAC9D,QAAQ,CAAC6D,WAAW,CAAC,IAClEtE,MAAM,CAACrB,MAAM,CAACE,OAAO,IAAI,EAAE,CAAC,CAAC0F,WAAW,CAAC,CAAC,CAAC9D,QAAQ,CAAC6D,WAAW,CAAC;IACpE,CAAC,CAAC;;IAEF;IACA,MAAME,iBAAiB,GAAGpI,YAAY,GAClCiI,cAAc,CAACrG,MAAM,CAACW,MAAM,IAC1BqB,MAAM,CAACrB,MAAM,CAACC,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKgB,MAAM,CAAC5D,YAAY,CAACwC,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFgB,MAAM,CAACrB,MAAM,CAACE,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKgB,MAAM,CAAC5D,YAAY,CAACyC,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAChFL,MAAM,CAACG,YAAY,KAAK,aAAa,IAAIH,MAAM,CAACG,YAAY,KAAK,QAAQ,CAAC,CAAC,GAC9E,EAAE;IAEN,MAAM2F,oBAAoB,GAAGrI,YAAY,GACrCiI,cAAc,CAACrG,MAAM,CAACW,MAAM,IAC1B,EAAEqB,MAAM,CAACrB,MAAM,CAACC,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKgB,MAAM,CAAC5D,YAAY,CAACwC,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFgB,MAAM,CAACrB,MAAM,CAACE,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKgB,MAAM,CAAC5D,YAAY,CAACyC,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,KACnFL,MAAM,CAACG,YAAY,KAAK,aAAa,IAAIH,MAAM,CAACG,YAAY,KAAK,QAAQ,CAAC,CAAC,GAC9E,EAAE;;IAEN;IACA0F,iBAAiB,CAACE,IAAI,CAAC,CAACC,CAAC,EAAE9E,CAAC,KAAKA,CAAC,CAACsD,aAAa,GAAGwB,CAAC,CAACxB,aAAa,CAAC;IACnEsB,oBAAoB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAE9E,CAAC,KAAKA,CAAC,CAACsD,aAAa,GAAGwB,CAAC,CAACxB,aAAa,CAAC;IAEtE,oBACExI,OAAA,CAAC3D,GAAG;MAAC8J,EAAE,EAAE;QAAEsD,EAAE,EAAE;MAAE,CAAE;MAAAxD,QAAA,gBAEjBjG,OAAA,CAAC3D,GAAG;QAAC8J,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,eACjBjG,OAAA,CAACxD,SAAS;UACRyN,SAAS;UACThD,KAAK,EAAC,4CAA4C;UAClDtC,KAAK,EAAE1C,gBAAiB;UACxBiF,QAAQ,EAAGC,CAAC,IAAKjF,mBAAmB,CAACiF,CAAC,CAACzC,MAAM,CAACC,KAAK,CAAE;UACrDuF,UAAU,EAAE;YACVC,cAAc,eACZnK,OAAA,CAAChC,cAAc;cAACoM,QAAQ,EAAC,OAAO;cAAAnE,QAAA,eAC9BjG,OAAA,CAACzB,UAAU;gBAAA+H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAEpB,CAAE;UACFN,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNzG,OAAA,CAAC3D,GAAG;QAAC8J,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEuB,GAAG,EAAE;QAAE,CAAE;QAAAnC,QAAA,gBAChEjG,OAAA,CAACxD,SAAS;UACRyK,KAAK,EAAC,iCAAiC;UACvCtC,KAAK,EAAExC,kBAAmB;UAC1B+E,QAAQ,EAAGC,CAAC,IAAK/E,qBAAqB,CAAC+E,CAAC,CAACzC,MAAM,CAACC,KAAK,CAAE;UACvD0F,IAAI,EAAC,QAAQ;UACblE,EAAE,EAAE;YAAEQ,KAAK,EAAE;UAAQ;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACFzG,OAAA,CAACvD,MAAM;UACLyJ,OAAO,EAAC,UAAU;UAClBqB,OAAO,EAAExC,0BAA2B;UACpCyC,QAAQ,EAAE,CAACrF,kBAAkB,CAACkC,IAAI,CAAC,CAAE;UAAA4B,QAAA,EACtC;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGL1E,gBAAgB,iBACf/B,OAAA,CAAChD,KAAK;QAAC2L,QAAQ,EAAC,SAAS;QAACxC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,GAAC,sBACnB,eAAAjG,OAAA;UAAAiG,QAAA,EAASnB,eAAe,CAAC/C,gBAAgB;QAAC;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CACR,EAGAoD,iBAAiB,CAAC1G,MAAM,GAAG,CAAC,iBAC3BnD,OAAA,CAAC1D,KAAK;QAAC6J,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBACnBjG,OAAA,CAAC3D,GAAG;UAAC8J,EAAE,EAAE;YAAEO,CAAC,EAAE,CAAC;YAAEqC,OAAO,EAAE;UAAU,CAAE;UAAA9C,QAAA,gBACpCjG,OAAA,CAACzD,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACC,EAAE,EAAE;cAAEmB,KAAK,EAAE,SAAS;cAAEjB,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,GAAC,sBACjD,EAAC4D,iBAAiB,CAAC1G,MAAM,EAAC,GAChD;UAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzG,OAAA,CAAC3D,GAAG;YAAC8J,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAE6B,cAAc,EAAE,eAAe;cAAEgB,EAAE,EAAE;YAAE,CAAE;YAAAxD,QAAA,gBACnEjG,OAAA,CAAC3D,GAAG;cAAC8J,EAAE,EAAE;gBAAEmE,IAAI,EAAE;cAAE,CAAE;cAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;gBAAC2J,OAAO,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEqB,QAAQ,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,EAAC;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eACNzG,OAAA,CAAC3D,GAAG;cAAC8J,EAAE,EAAE;gBAAEmE,IAAI,EAAE;cAAE,CAAE;cAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;gBAAC2J,OAAO,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEqB,QAAQ,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG,CAAC,eACNzG,OAAA,CAAC3D,GAAG;cAAC8J,EAAE,EAAE;gBAAEmE,IAAI,EAAE;cAAE,CAAE;cAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;gBAAC2J,OAAO,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEqB,QAAQ,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnG,CAAC,eACNzG,OAAA,CAAC3D,GAAG;cAAC8J,EAAE,EAAE;gBAAEmE,IAAI,EAAE;cAAE,CAAE;cAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;gBAAC2J,OAAO,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEqB,QAAQ,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC,eACNzG,OAAA,CAAC3D,GAAG;cAAC8J,EAAE,EAAE;gBAAEmE,IAAI,EAAE;cAAE,CAAE;cAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;gBAAC2J,OAAO,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEqB,QAAQ,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNzG,OAAA,CAAC/B,IAAI;UAACkI,EAAE,EAAE;YAAE0C,SAAS,EAAE,OAAO;YAAEhB,QAAQ,EAAE,MAAM;YAAEkB,OAAO,EAAE;UAAmB,CAAE;UAAA9C,QAAA,EAC7E4D,iBAAiB,CAACZ,GAAG,CAAEjF,MAAM,iBAC5BhE,OAAA,CAAC9B,QAAQ;YAEPqM,cAAc;YACdC,eAAe,eACbxK,OAAA,CAACjC,UAAU;cACT0M,IAAI,EAAC,KAAK;cACVzD,IAAI,EAAC,OAAO;cACZO,OAAO,EAAEA,CAAA,KAAM3C,kBAAkB,CAACZ,MAAM,CAACmB,SAAS,CAAE;cACpDmC,KAAK,EAAEvF,gBAAgB,KAAKiC,MAAM,CAACmB,SAAS,GAAG,SAAS,GAAG,SAAU;cAAAc,QAAA,eAErEjG,OAAA,CAACf,oBAAoB;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CACb;YACDN,EAAE,EAAE;cACF4C,OAAO,EAAEhH,gBAAgB,KAAKiC,MAAM,CAACmB,SAAS,GAAG,yBAAyB,GAAG,SAAS;cACtFuF,YAAY,EAAE,KAAK;cACnBtE,EAAE,EAAE,GAAG;cACP,SAAS,EAAE;gBACT2C,OAAO,EAAEhH,gBAAgB,KAAKiC,MAAM,CAACmB,SAAS,GAAG,wBAAwB,GAAG;cAC9E;YACF,CAAE;YAAAc,QAAA,eAEFjG,OAAA,CAAC7B,YAAY;cACXwM,OAAO,eACL3K,OAAA,CAAC3D,GAAG;gBAAC8J,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAE6B,cAAc,EAAE,eAAe;kBAAE5B,UAAU,EAAE;gBAAS,CAAE;gBAAAZ,QAAA,gBAClFjG,OAAA,CAAC3D,GAAG;kBAAC8J,EAAE,EAAE;oBAAEmE,IAAI,EAAE;kBAAE,CAAE;kBAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAEtE,gBAAgB,KAAKiC,MAAM,CAACmB,SAAS,GAAG,MAAM,GAAG;oBAAS,CAAE;oBAAAc,QAAA,EACvGnB,eAAe,CAACd,MAAM,CAACmB,SAAS;kBAAC;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNzG,OAAA,CAAC3D,GAAG;kBAAC8J,EAAE,EAAE;oBAAEmE,IAAI,EAAE;kBAAE,CAAE;kBAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAEjC,MAAM,CAACC;kBAAS;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACNzG,OAAA,CAAC3D,GAAG;kBAAC8J,EAAE,EAAE;oBAAEmE,IAAI,EAAE;kBAAE,CAAE;kBAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAEjC,MAAM,CAACE;kBAAO;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACNzG,OAAA,CAAC3D,GAAG;kBAAC8J,EAAE,EAAE;oBAAEmE,IAAI,EAAE;kBAAE,CAAE;kBAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAEjC,MAAM,CAACwE,aAAa,EAAC,GAAC;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACNzG,OAAA,CAAC3D,GAAG;kBAAC8J,EAAE,EAAE;oBAAEmE,IAAI,EAAE;kBAAE,CAAE;kBAAArE,QAAA,eACnBjG,OAAA,CAAC3B,IAAI;oBACH4I,KAAK,EAAEjD,MAAM,CAACG,YAAa;oBAC3B6C,IAAI,EAAC,OAAO;oBACZM,KAAK,EAAEtD,MAAM,CAACG,YAAY,KAAK,aAAa,GAAG,SAAS,GAAG;kBAAU;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;cACDN,EAAE,EAAE;gBACF4C,OAAO,EAAEhH,gBAAgB,KAAKiC,MAAM,CAACmB,SAAS,GAAG,SAAS,GAAG,aAAa;gBAC1EuF,YAAY,EAAE,CAAC;gBACfrC,EAAE,EAAE;cACN;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GApDGzC,MAAM,CAACmB,SAAS;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAqDb,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACR,EAGAqD,oBAAoB,CAAC3G,MAAM,GAAG,CAAC,iBAC9BnD,OAAA,CAAC1D,KAAK;QAAC6J,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBACnBjG,OAAA,CAAC3D,GAAG;UAAC8J,EAAE,EAAE;YAAEO,CAAC,EAAE,CAAC;YAAEqC,OAAO,EAAE;UAAU,CAAE;UAAA9C,QAAA,gBACpCjG,OAAA,CAACzD,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACC,EAAE,EAAE;cAAEmB,KAAK,EAAE,SAAS;cAAEjB,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,GAAC,0BAC7C,EAAC6D,oBAAoB,CAAC3G,MAAM,EAAC,GACvD;UAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzG,OAAA,CAACzD,UAAU;YAAC2J,OAAO,EAAC,SAAS;YAACoB,KAAK,EAAC,gBAAgB;YAAArB,QAAA,EAAC;UAErD;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzG,OAAA,CAAC3D,GAAG;YAAC8J,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAE6B,cAAc,EAAE,eAAe;cAAEgB,EAAE,EAAE;YAAE,CAAE;YAAAxD,QAAA,gBACnEjG,OAAA,CAAC3D,GAAG;cAAC8J,EAAE,EAAE;gBAAEmE,IAAI,EAAE;cAAE,CAAE;cAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;gBAAC2J,OAAO,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEqB,QAAQ,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,EAAC;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eACNzG,OAAA,CAAC3D,GAAG;cAAC8J,EAAE,EAAE;gBAAEmE,IAAI,EAAE;cAAE,CAAE;cAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;gBAAC2J,OAAO,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEqB,QAAQ,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG,CAAC,eACNzG,OAAA,CAAC3D,GAAG;cAAC8J,EAAE,EAAE;gBAAEmE,IAAI,EAAE;cAAE,CAAE;cAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;gBAAC2J,OAAO,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEqB,QAAQ,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnG,CAAC,eACNzG,OAAA,CAAC3D,GAAG;cAAC8J,EAAE,EAAE;gBAAEmE,IAAI,EAAE;cAAE,CAAE;cAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;gBAAC2J,OAAO,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEqB,QAAQ,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC,eACNzG,OAAA,CAAC3D,GAAG;cAAC8J,EAAE,EAAE;gBAAEmE,IAAI,EAAE;cAAE,CAAE;cAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;gBAAC2J,OAAO,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEqB,QAAQ,EAAE;gBAAU,CAAE;gBAAAzB,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNzG,OAAA,CAAC/B,IAAI;UAACkI,EAAE,EAAE;YAAE0C,SAAS,EAAE,OAAO;YAAEhB,QAAQ,EAAE,MAAM;YAAEkB,OAAO,EAAE;UAAmB,CAAE;UAAA9C,QAAA,EAC7E6D,oBAAoB,CAACb,GAAG,CAAEjF,MAAM,iBAC/BhE,OAAA,CAAC9B,QAAQ;YAEPqM,cAAc;YACdC,eAAe,eACbxK,OAAA,CAACjC,UAAU;cACT0M,IAAI,EAAC,KAAK;cACVzD,IAAI,EAAC,OAAO;cACZO,OAAO,EAAEA,CAAA,KAAM;gBACbjH,OAAO,CAAC,UAAUwE,eAAe,CAACd,MAAM,CAACmB,SAAS,CAAC,gCAAgCnB,MAAM,CAACC,SAAS,iBAAiBD,MAAM,CAACE,OAAO,EAAE,CAAC;cACvI,CAAE;cACFoD,KAAK,EAAC,SAAS;cAAArB,QAAA,eAEfjG,OAAA,CAACnB,WAAW;gBAAAyH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACb;YACDN,EAAE,EAAE;cACFuE,YAAY,EAAE,KAAK;cACnBtE,EAAE,EAAE,GAAG;cACP,SAAS,EAAE;gBACT2C,OAAO,EAAE;cACX;YACF,CAAE;YAAA9C,QAAA,eAEFjG,OAAA,CAAC7B,YAAY;cACXwM,OAAO,eACL3K,OAAA,CAAC3D,GAAG;gBAAC8J,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAE6B,cAAc,EAAE,eAAe;kBAAE5B,UAAU,EAAE;gBAAS,CAAE;gBAAAZ,QAAA,gBAClFjG,OAAA,CAAC3D,GAAG;kBAAC8J,EAAE,EAAE;oBAAEmE,IAAI,EAAE;kBAAE,CAAE;kBAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAEnB,eAAe,CAACd,MAAM,CAACmB,SAAS;kBAAC;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC,eACNzG,OAAA,CAAC3D,GAAG;kBAAC8J,EAAE,EAAE;oBAAEmE,IAAI,EAAE;kBAAE,CAAE;kBAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACoB,KAAK,EAAEtD,MAAM,CAACC,SAAS,MAAKxC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEwC,SAAS,IAAG,OAAO,GAAG,cAAe;oBAAAgC,QAAA,EACxGjC,MAAM,CAACC;kBAAS;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNzG,OAAA,CAAC3D,GAAG;kBAAC8J,EAAE,EAAE;oBAAEmE,IAAI,EAAE;kBAAE,CAAE;kBAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACoB,KAAK,EAAEjC,MAAM,CAACrB,MAAM,CAACE,OAAO,CAAC,KAAKmB,MAAM,CAAC5D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEyC,OAAO,CAAC,GAAG,OAAO,GAAG,cAAe;oBAAA+B,QAAA,EACpHjC,MAAM,CAACE;kBAAO;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNzG,OAAA,CAAC3D,GAAG;kBAAC8J,EAAE,EAAE;oBAAEmE,IAAI,EAAE;kBAAE,CAAE;kBAAArE,QAAA,eACnBjG,OAAA,CAACzD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAEjC,MAAM,CAACwE,aAAa,EAAC,GAAC;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACNzG,OAAA,CAAC3D,GAAG;kBAAC8J,EAAE,EAAE;oBAAEmE,IAAI,EAAE;kBAAE,CAAE;kBAAArE,QAAA,eACnBjG,OAAA,CAAC3B,IAAI;oBACH4I,KAAK,EAAEjD,MAAM,CAACG,YAAa;oBAC3B6C,IAAI,EAAC,OAAO;oBACZM,KAAK,EAAEtD,MAAM,CAACG,YAAY,KAAK,aAAa,GAAG,SAAS,GAAG;kBAAU;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC,GAlDGzC,MAAM,CAACmB,SAAS;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmDb,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACR,EAGAoD,iBAAiB,CAAC1G,MAAM,KAAK,CAAC,IAAI2G,oBAAoB,CAAC3G,MAAM,KAAK,CAAC,iBAClEnD,OAAA,CAAChD,KAAK;QAAC2L,QAAQ,EAAC,MAAM;QAACxC,EAAE,EAAE;UAAEsD,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,EAClChE,gBAAgB,GAAG,8DAA8D,GAAG;MAAiC;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjH,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,oBACEzG,OAAA,CAAC3D,GAAG;IAAA4J,QAAA,GAEDD,uBAAuB,CAAC,CAAC,EAGzBqD,qBAAqB,CAAC,CAAC,eAGxBrJ,OAAA,CAAC9C,MAAM;MACL0N,IAAI,EAAEjI,qBAAsB;MAC5BkI,OAAO,EAAEA,CAAA,KAAMjI,wBAAwB,CAAC,KAAK,CAAE;MAC/CkI,QAAQ,EAAC,IAAI;MACbb,SAAS;MAAAhE,QAAA,gBAETjG,OAAA,CAAC7C,WAAW;QAAA8I,QAAA,EAAC;MAA0B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACrDzG,OAAA,CAAC5C,aAAa;QAAA6I,QAAA,EACXxE,YAAY,iBAAIzB,OAAA,CAACX,eAAe;UAACiE,IAAI,EAAE7B;QAAa;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAChBzG,OAAA,CAAC3C,aAAa;QAAA4I,QAAA,eACZjG,OAAA,CAACvD,MAAM;UAAC8K,OAAO,EAAEA,CAAA,KAAM3E,wBAAwB,CAAC,KAAK,CAAE;UAAAqD,QAAA,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTzG,OAAA,CAAC9C,MAAM;MACL0N,IAAI,EAAEvI,iBAAkB;MACxBwI,OAAO,EAAEA,CAAA,KAAMvI,oBAAoB,CAAC,KAAK,CAAE;MAAA2D,QAAA,gBAE3CjG,OAAA,CAAC7C,WAAW;QAAA8I,QAAA,EAAC;MAAmB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9CzG,OAAA,CAAC5C,aAAa;QAAA6I,QAAA,eACZjG,OAAA,CAACzD,UAAU;UAAA0J,QAAA,EAAE1D;QAAoB;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAChBzG,OAAA,CAAC3C,aAAa;QAAA4I,QAAA,gBACZjG,OAAA,CAACvD,MAAM;UACL8K,OAAO,EAAEA,CAAA,KAAMjF,oBAAoB,CAAC,KAAK,CAAE;UAC3CgF,KAAK,EAAC,SAAS;UAAArB,QAAA,EAChB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzG,OAAA,CAACvD,MAAM;UACL8K,OAAO,EAAEA,CAAA,KAAM;YACbjF,oBAAoB,CAAC,KAAK,CAAC;YAC3B,IAAIG,mBAAmB,EAAEA,mBAAmB,CAAC,CAAC;UAChD,CAAE;UACF6E,KAAK,EAAC,SAAS;UACfpB,OAAO,EAAC,WAAW;UACnB6E,SAAS;UAAA9E,QAAA,EACV;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAClG,EAAA,CAr3BIJ,kBAAkB;EAAA,QACLjB,WAAW;AAAA;AAAA8L,EAAA,GADxB7K,kBAAkB;AAu3BxB,eAAeA,kBAAkB;AAAC,IAAA6K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}