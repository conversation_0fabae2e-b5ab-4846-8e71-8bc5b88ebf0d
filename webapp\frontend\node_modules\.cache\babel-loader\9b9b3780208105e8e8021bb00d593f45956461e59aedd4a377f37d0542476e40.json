{"ast": null, "code": "import { constructFrom } from \"./constructFrom.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name closestTo\n * @category Common Helpers\n * @summary Return a date from the array closest to the given date.\n *\n * @description\n * Return a date from the array closest to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateToCompare - The date to compare with\n * @param dates - The array to search\n *\n * @returns The date from the array closest to the given date or undefined if no valid value is given\n *\n * @example\n * // Which date is closer to 6 September 2015: 1 January 2000 or 1 January 2030?\n * const dateToCompare = new Date(2015, 8, 6)\n * const result = closestTo(dateToCompare, [\n *   new Date(2000, 0, 1),\n *   new Date(2030, 0, 1)\n * ])\n * //=> Tue Jan 01 2030 00:00:00\n */\nexport function closestTo(dateToCompare, dates) {\n  const date = toDate(dateToCompare);\n  if (isNaN(Number(date))) return constructFrom(dateToCompare, NaN);\n  const timeToCompare = date.getTime();\n  let result;\n  let minDistance;\n  dates.forEach(dirtyDate => {\n    const currentDate = toDate(dirtyDate);\n    if (isNaN(Number(currentDate))) {\n      result = constructFrom(dateToCompare, NaN);\n      minDistance = NaN;\n      return;\n    }\n    const distance = Math.abs(timeToCompare - currentDate.getTime());\n    if (result == null || distance < minDistance) {\n      result = currentDate;\n      minDistance = distance;\n    }\n  });\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default closestTo;", "map": {"version": 3, "names": ["constructFrom", "toDate", "closestTo", "dateToCompare", "dates", "date", "isNaN", "Number", "NaN", "timeToCompare", "getTime", "result", "minDistance", "for<PERSON>ach", "dirtyDate", "currentDate", "distance", "Math", "abs"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/closestTo.mjs"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name closestTo\n * @category Common Helpers\n * @summary Return a date from the array closest to the given date.\n *\n * @description\n * Return a date from the array closest to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateToCompare - The date to compare with\n * @param dates - The array to search\n *\n * @returns The date from the array closest to the given date or undefined if no valid value is given\n *\n * @example\n * // Which date is closer to 6 September 2015: 1 January 2000 or 1 January 2030?\n * const dateToCompare = new Date(2015, 8, 6)\n * const result = closestTo(dateToCompare, [\n *   new Date(2000, 0, 1),\n *   new Date(2030, 0, 1)\n * ])\n * //=> Tue Jan 01 2030 00:00:00\n */\nexport function closestTo(dateToCompare, dates) {\n  const date = toDate(dateToCompare);\n\n  if (isNaN(Number(date))) return constructFrom(dateToCompare, NaN);\n\n  const timeToCompare = date.getTime();\n\n  let result;\n  let minDistance;\n  dates.forEach((dirtyDate) => {\n    const currentDate = toDate(dirtyDate);\n\n    if (isNaN(Number(currentDate))) {\n      result = constructFrom(dateToCompare, NaN);\n      minDistance = NaN;\n      return;\n    }\n\n    const distance = Math.abs(timeToCompare - currentDate.getTime());\n    if (result == null || distance < minDistance) {\n      result = currentDate;\n      minDistance = distance;\n    }\n  });\n\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default closestTo;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;AACnD,SAASC,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,aAAa,EAAEC,KAAK,EAAE;EAC9C,MAAMC,IAAI,GAAGJ,MAAM,CAACE,aAAa,CAAC;EAElC,IAAIG,KAAK,CAACC,MAAM,CAACF,IAAI,CAAC,CAAC,EAAE,OAAOL,aAAa,CAACG,aAAa,EAAEK,GAAG,CAAC;EAEjE,MAAMC,aAAa,GAAGJ,IAAI,CAACK,OAAO,CAAC,CAAC;EAEpC,IAAIC,MAAM;EACV,IAAIC,WAAW;EACfR,KAAK,CAACS,OAAO,CAAEC,SAAS,IAAK;IAC3B,MAAMC,WAAW,GAAGd,MAAM,CAACa,SAAS,CAAC;IAErC,IAAIR,KAAK,CAACC,MAAM,CAACQ,WAAW,CAAC,CAAC,EAAE;MAC9BJ,MAAM,GAAGX,aAAa,CAACG,aAAa,EAAEK,GAAG,CAAC;MAC1CI,WAAW,GAAGJ,GAAG;MACjB;IACF;IAEA,MAAMQ,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACT,aAAa,GAAGM,WAAW,CAACL,OAAO,CAAC,CAAC,CAAC;IAChE,IAAIC,MAAM,IAAI,IAAI,IAAIK,QAAQ,GAAGJ,WAAW,EAAE;MAC5CD,MAAM,GAAGI,WAAW;MACpBH,WAAW,GAAGI,QAAQ;IACxB;EACF,CAAC,CAAC;EAEF,OAAOL,MAAM;AACf;;AAEA;AACA,eAAeT,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}