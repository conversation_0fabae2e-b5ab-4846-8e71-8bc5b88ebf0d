{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CavoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, Button, Stack, MenuItem, Alert, CircularProgress, Typography, Divider, Paper, IconButton, Tooltip, Grid } from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon, Cancel as CancelIcon, KeyboardArrowDown as KeyboardArrowDownIcon, KeyboardArrowUp as KeyboardArrowUpIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { validateCavoData, validateField } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SECTION_CONFIG = [{\n  title: 'Informazioni Generali',\n  collapsible: false,\n  fields: [{\n    name: 'id_cavo',\n    label: 'ID Cavo',\n    required: true,\n    inputProps: {\n      style: {\n        textTransform: 'uppercase'\n      }\n    }\n  }, {\n    name: 'utility',\n    label: 'Utility',\n    required: true\n  }, {\n    name: 'sistema',\n    label: 'Sistema'\n  }]\n}, {\n  title: 'Caratteristiche Tecniche',\n  collapsible: false,\n  fields: [{\n    name: 'colore_cavo',\n    label: 'Colore Cavo'\n  }, {\n    name: 'tipologia',\n    label: 'Tipologia'\n  },\n  // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n  {\n    name: 'sezione',\n    label: 'Formazione'\n  }\n  // sh field is now a spare field (kept in DB but hidden in UI)\n  ]\n}, {\n  title: 'Partenza',\n  collapsible: true,\n  fields: [{\n    name: 'ubicazione_partenza',\n    label: 'Ubicazione Partenza',\n    gridSize: {\n      xs: 12,\n      sm: 6,\n      md: 4\n    }\n  }, {\n    name: 'utenza_partenza',\n    label: 'Utenza Partenza',\n    gridSize: {\n      xs: 12,\n      sm: 6,\n      md: 4\n    }\n  }, {\n    name: 'descrizione_utenza_partenza',\n    label: 'Descrizione Utenza Partenza',\n    gridSize: {\n      xs: 12,\n      sm: 6,\n      md: 4\n    }\n  }]\n}, {\n  title: 'Arrivo',\n  collapsible: true,\n  fields: [{\n    name: 'ubicazione_arrivo',\n    label: 'Ubicazione Arrivo',\n    gridSize: {\n      xs: 12,\n      sm: 6,\n      md: 4\n    }\n  }, {\n    name: 'utenza_arrivo',\n    label: 'Utenza Arrivo',\n    gridSize: {\n      xs: 12,\n      sm: 6,\n      md: 4\n    }\n  }, {\n    name: 'descrizione_utenza_arrivo',\n    label: 'Descrizione Utenza Arrivo',\n    gridSize: {\n      xs: 12,\n      sm: 6,\n      md: 4\n    }\n  }]\n}, {\n  title: 'Metratura',\n  collapsible: false,\n  fields: [{\n    name: 'metri_teorici',\n    label: 'Metri Teorici',\n    required: true\n  }]\n}];\nconst defaultData = {\n  id_cavo: '',\n  utility: '',\n  sistema: '',\n  colore_cavo: '',\n  tipologia: '',\n  n_conduttori: '',\n  sezione: '',\n  sh: 'N',\n  ubicazione_partenza: '',\n  utenza_partenza: '',\n  descrizione_utenza_partenza: '',\n  ubicazione_arrivo: '',\n  utenza_arrivo: '',\n  descrizione_utenza_arrivo: '',\n  metri_teorici: '',\n  metratura_reale: '0'\n};\nconst CavoForm = ({\n  mode = 'add',\n  initialData = {},\n  onSubmit,\n  onSuccess,\n  onError,\n  onCancel\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Funzione per pulire i dati iniziali da valori null/undefined\n  const cleanInitialData = data => {\n    const cleaned = {};\n    Object.keys(defaultData).forEach(key => {\n      // Se il valore è null o undefined, usa il valore di default\n      cleaned[key] = data[key] !== null && data[key] !== undefined ? data[key] : defaultData[key];\n    });\n    return cleaned;\n  };\n  const [formData, setFormData] = useState(cleanInitialData(initialData));\n  const [formErrors, setFormErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [expanded, setExpanded] = useState({});\n  const [showWarnings, setShowWarnings] = useState(false);\n\n  // Inizializza lo stato di espansione delle sezioni collassabili\n  useEffect(() => {\n    const state = {};\n    SECTION_CONFIG.forEach(s => {\n      if (s.collapsible) state[s.title] = true;\n    });\n    setExpanded(state);\n  }, []);\n\n  // Gestisce l'espansione/collasso delle sezioni\n  const toggleExpand = title => {\n    setExpanded(prev => ({\n      ...prev,\n      [title]: !prev[title]\n    }));\n  };\n\n  // Gestisce i cambiamenti nei campi del form\n  const handleChange = e => {\n    var _result$message;\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Validazione in tempo reale\n    const extra = name === 'metratura_reale' ? {\n      metriTeorici: parseFloat(formData.metri_teorici || 0)\n    } : {};\n    const result = validateField(name, value, extra);\n\n    // Assicurati che gli errori siano sempre stringhe o null\n    const errorMessage = result.valid ? null : typeof result.message === 'object' ? ((_result$message = result.message) === null || _result$message === void 0 ? void 0 : _result$message.message) || JSON.stringify(result.message) : result.message;\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: errorMessage\n    }));\n    setWarnings(prev => {\n      var _result$message2;\n      // Assicurati che i warning siano sempre stringhe o null\n      const warningMessage = result.warning ? typeof result.message === 'object' ? ((_result$message2 = result.message) === null || _result$message2 === void 0 ? void 0 : _result$message2.message) || JSON.stringify(result.message) : result.message : null;\n      const newWarnings = {\n        ...prev,\n        [name]: warningMessage\n      };\n      // Mostra il banner di avviso se ci sono warning\n      setShowWarnings(Object.values(newWarnings).some(w => w));\n      return newWarnings;\n    });\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    const validation = validateCavoData(formData);\n    if (!validation.isValid) {\n      // Assicurati che gli errori siano sempre stringhe o null\n      const processedErrors = {};\n      Object.entries(validation.errors).forEach(([key, value]) => {\n        processedErrors[key] = typeof value === 'object' ? (value === null || value === void 0 ? void 0 : value.message) || JSON.stringify(value) : value;\n      });\n\n      // Assicurati che i warning siano sempre stringhe o null\n      const processedWarnings = {};\n      Object.entries(validation.warnings).forEach(([key, value]) => {\n        processedWarnings[key] = typeof value === 'object' ? (value === null || value === void 0 ? void 0 : value.message) || JSON.stringify(value) : value;\n      });\n      setFormErrors(processedErrors);\n      setWarnings(processedWarnings);\n      setShowWarnings(Object.values(processedWarnings).some(w => w));\n      setLoading(false);\n      onError('Ci sono errori nel form.');\n      return;\n    }\n    try {\n      const finalData = {\n        ...validation.validatedData,\n        id_cavo: validation.validatedData.id_cavo.toUpperCase(),\n        metratura_reale: '0' // Set default value\n      };\n      await onSubmit(finalData);\n      setLoading(false);\n      onSuccess(`Cavo ${mode === 'add' ? 'aggiunto' : 'modificato'} con successo.`);\n      redirectToVisualizzaCavi(navigate);\n    } catch (err) {\n      setLoading(false);\n      onError(err.message);\n    }\n  };\n\n  // Gestisce l'annullamento del form\n  const handleCancel = () => {\n    if (onCancel) {\n      onCancel();\n    } else {\n      redirectToVisualizzaCavi(navigate);\n    }\n  };\n\n  // Renderizza un singolo campo del form\n  const renderField = field => {\n    var _formData$field$name, _field$options;\n    // Gestisce i valori null/undefined per evitare la visualizzazione di \"null\"\n    const fieldValue = (_formData$field$name = formData[field.name]) !== null && _formData$field$name !== void 0 ? _formData$field$name : '';\n\n    // Gestisce i messaggi di errore e warning in modo più pulito\n    const errorMessage = formErrors[field.name];\n    const warningMessage = warnings[field.name];\n    const helperText = errorMessage || warningMessage || '';\n    return /*#__PURE__*/_jsxDEV(TextField, {\n      select: field.type === 'select',\n      fullWidth: true,\n      size: \"small\",\n      margin: \"none\",\n      label: field.label,\n      name: field.name,\n      value: fieldValue,\n      onChange: handleChange,\n      error: !!errorMessage,\n      helperText: helperText,\n      required: field.required,\n      variant: \"outlined\",\n      InputLabelProps: {\n        shrink: true,\n        sx: {\n          fontWeight: field.required ? 600 : 400,\n          color: field.required ? 'primary.main' : 'text.secondary'\n        }\n      },\n      sx: {\n        '& .MuiOutlinedInput-root': {\n          '&:hover fieldset': {\n            borderColor: 'primary.main'\n          },\n          '&.Mui-focused fieldset': {\n            borderWidth: 2\n          }\n        },\n        '& .MuiFormHelperText-root': {\n          fontSize: '0.7rem',\n          marginTop: '2px',\n          lineHeight: 1.2,\n          color: errorMessage ? 'error.main' : warningMessage ? 'warning.main' : 'text.secondary'\n        }\n      },\n      ...(field.inputProps || {}),\n      children: (_field$options = field.options) === null || _field$options === void 0 ? void 0 : _field$options.map(opt => /*#__PURE__*/_jsxDEV(MenuItem, {\n        value: opt,\n        children: opt\n      }, opt, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 2,\n    sx: {\n      border: '1px solid #e0e0e0',\n      borderRadius: 3,\n      overflow: 'hidden',\n      background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',\n      boxShadow: '0 4px 20px rgba(0,0,0,0.08)'\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      component: \"form\",\n      onSubmit: handleSubmit,\n      noValidate: true,\n      children: [showWarnings && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 19\n        }, this),\n        sx: {\n          borderRadius: 0,\n          py: 0.5,\n          backgroundColor: '#fff3cd',\n          borderBottom: '1px solid #ffeaa7',\n          '& .MuiAlert-message': {\n            fontWeight: 500,\n            fontSize: '0.85rem'\n          }\n        },\n        children: \"Alcuni campi potrebbero necessitare revisione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this), SECTION_CONFIG.map((section, index) => {\n        const isExpanded = section.collapsible ? expanded[section.title] : true;\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            borderBottom: index < SECTION_CONFIG.length - 1 ? '1px solid #e8eaed' : 'none'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              px: 2.5,\n              py: 1,\n              background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n              borderBottom: isExpanded ? '1px solid #e8eaed' : 'none',\n              cursor: section.collapsible ? 'pointer' : 'default',\n              transition: 'all 0.2s ease-in-out',\n              '&:hover': section.collapsible ? {\n                background: 'linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%)'\n              } : {}\n            },\n            onClick: () => section.collapsible && toggleExpand(section.title),\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 700,\n                color: '#495057',\n                fontSize: '0.9rem',\n                letterSpacing: '0.3px',\n                textTransform: 'uppercase'\n              },\n              children: section.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this), section.collapsible && /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              sx: {\n                ml: 1\n              },\n              onClick: e => {\n                e.stopPropagation();\n                toggleExpand(section.title);\n              },\n              children: isExpanded ? /*#__PURE__*/_jsxDEV(KeyboardArrowUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 35\n              }, this) : /*#__PURE__*/_jsxDEV(KeyboardArrowDownIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 61\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this), isExpanded && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2,\n              backgroundColor: '#ffffff',\n              borderBottom: index < SECTION_CONFIG.length - 1 ? '1px solid #f1f3f4' : 'none'\n            },\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 1.5,\n              children: section.fields.map(field => {\n                var _field$gridSize, _field$gridSize2, _field$gridSize3;\n                return /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: ((_field$gridSize = field.gridSize) === null || _field$gridSize === void 0 ? void 0 : _field$gridSize.xs) || 12,\n                  sm: ((_field$gridSize2 = field.gridSize) === null || _field$gridSize2 === void 0 ? void 0 : _field$gridSize2.sm) || 6,\n                  md: ((_field$gridSize3 = field.gridSize) === null || _field$gridSize3 === void 0 ? void 0 : _field$gridSize3.md) || 4,\n                  children: renderField(field)\n                }, field.name, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 17\n          }, this)]\n        }, section.title, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: 1.5,\n          p: 2,\n          background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n          borderTop: '1px solid #e8eaed'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Annulla e torna indietro\",\n          arrow: true,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"inherit\",\n            onClick: handleCancel,\n            disabled: loading,\n            startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 26\n            }, this),\n            size: \"medium\",\n            sx: {\n              borderColor: '#6c757d',\n              color: '#6c757d',\n              fontWeight: 600,\n              px: 2.5,\n              py: 0.8,\n              '&:hover': {\n                borderColor: '#495057',\n                backgroundColor: '#f8f9fa',\n                color: '#495057'\n              }\n            },\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: mode === 'add' ? 'Salva nuovo cavo' : 'Salva modifiche al cavo',\n          arrow: true,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"contained\",\n            color: \"primary\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 18,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 85\n            }, this),\n            disabled: loading,\n            size: \"medium\",\n            sx: {\n              fontWeight: 600,\n              px: 3,\n              py: 0.8,\n              background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',\n              boxShadow: '0 4px 12px rgba(0,123,255,0.3)',\n              '&:hover': {\n                background: 'linear-gradient(135deg, #0056b3 0%, #004085 100%)',\n                boxShadow: '0 6px 16px rgba(0,123,255,0.4)',\n                transform: 'translateY(-1px)'\n              },\n              '&:disabled': {\n                background: '#6c757d',\n                boxShadow: 'none'\n              },\n              transition: 'all 0.2s ease-in-out'\n            },\n            children: loading ? 'Salvataggio...' : mode === 'add' ? 'Salva Cavo' : 'Aggiorna Cavo'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 271,\n    columnNumber: 5\n  }, this);\n};\n_s(CavoForm, \"0aAxsgP6ioN6maEYvXolywNYVLI=\", false, function () {\n  return [useNavigate];\n});\n_c = CavoForm;\nexport default CavoForm;\nvar _c;\n$RefreshReg$(_c, \"CavoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "Typography", "Divider", "Paper", "IconButton", "<PERSON><PERSON><PERSON>", "Grid", "Save", "SaveIcon", "Warning", "WarningIcon", "Cancel", "CancelIcon", "KeyboardArrowDown", "KeyboardArrowDownIcon", "KeyboardArrowUp", "KeyboardArrowUpIcon", "useNavigate", "validateCavoData", "validateField", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "SECTION_CONFIG", "title", "collapsible", "fields", "name", "label", "required", "inputProps", "style", "textTransform", "gridSize", "xs", "sm", "md", "defaultData", "id_cavo", "utility", "sistema", "colore_cavo", "tipologia", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "CavoForm", "mode", "initialData", "onSubmit", "onSuccess", "onError", "onCancel", "_s", "navigate", "cleanInitialData", "data", "cleaned", "Object", "keys", "for<PERSON>ach", "key", "undefined", "formData", "setFormData", "formErrors", "setFormErrors", "warnings", "setWarnings", "loading", "setLoading", "expanded", "setExpanded", "showWarnings", "setShowWarnings", "state", "s", "toggleExpand", "prev", "handleChange", "e", "_result$message", "value", "target", "extra", "metriTeorici", "parseFloat", "result", "errorMessage", "valid", "message", "JSON", "stringify", "_result$message2", "warningMessage", "warning", "newWarnings", "values", "some", "w", "handleSubmit", "preventDefault", "validation", "<PERSON><PERSON><PERSON><PERSON>", "processedErrors", "entries", "errors", "processedWarnings", "finalData", "validatedData", "toUpperCase", "err", "handleCancel", "renderField", "field", "_formData$field$name", "_field$options", "fieldValue", "helperText", "select", "type", "fullWidth", "size", "margin", "onChange", "error", "variant", "InputLabelProps", "shrink", "sx", "fontWeight", "color", "borderColor", "borderWidth", "fontSize", "marginTop", "lineHeight", "children", "options", "map", "opt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "elevation", "border", "borderRadius", "overflow", "background", "boxShadow", "component", "noValidate", "severity", "icon", "py", "backgroundColor", "borderBottom", "section", "index", "isExpanded", "length", "display", "justifyContent", "alignItems", "px", "cursor", "transition", "onClick", "letterSpacing", "ml", "stopPropagation", "p", "container", "spacing", "_field$gridSize", "_field$gridSize2", "_field$gridSize3", "item", "gap", "borderTop", "arrow", "disabled", "startIcon", "transform", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CavoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Stack,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  Typography,\n  Divider,\n  Paper,\n  IconButton,\n  Tooltip,\n  Grid\n} from '@mui/material';\nimport {\n  Save as SaveIcon,\n  Warning as WarningIcon,\n  Cancel as CancelIcon,\n  KeyboardArrowDown as KeyboardArrowDownIcon,\n  KeyboardArrowUp as KeyboardArrowUpIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { validateCavoData, validateField } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\nconst SECTION_CONFIG = [\n  {\n    title: 'Informazioni Generali',\n    collapsible: false,\n    fields: [\n      { name: 'id_cavo', label: 'ID Cavo', required: true, inputProps: { style: { textTransform: 'uppercase' } } },\n      { name: 'utility', label: 'Utility', required: true },\n      { name: 'sistema', label: '<PERSON>ste<PERSON>' }\n    ]\n  },\n  {\n    title: 'Caratteristiche Tecniche',\n    collapsible: false,\n    fields: [\n      { name: 'colore_cavo', label: 'Colore Cavo' },\n      { name: 'tipologia', label: 'Tipologia' },\n      // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n      { name: 'sezione', label: 'Formazione' },\n      // sh field is now a spare field (kept in DB but hidden in UI)\n    ]\n  },\n  {\n    title: 'Partenza',\n    collapsible: true,\n    fields: [\n      { name: 'ubicazione_partenza', label: 'Ubicazione Partenza', gridSize: { xs: 12, sm: 6, md: 4 } },\n      { name: 'utenza_partenza', label: 'Utenza Partenza', gridSize: { xs: 12, sm: 6, md: 4 } },\n      { name: 'descrizione_utenza_partenza', label: 'Descrizione Utenza Partenza', gridSize: { xs: 12, sm: 6, md: 4 } }\n    ]\n  },\n  {\n    title: 'Arrivo',\n    collapsible: true,\n    fields: [\n      { name: 'ubicazione_arrivo', label: 'Ubicazione Arrivo', gridSize: { xs: 12, sm: 6, md: 4 } },\n      { name: 'utenza_arrivo', label: 'Utenza Arrivo', gridSize: { xs: 12, sm: 6, md: 4 } },\n      { name: 'descrizione_utenza_arrivo', label: 'Descrizione Utenza Arrivo', gridSize: { xs: 12, sm: 6, md: 4 } }\n    ]\n  },\n  {\n    title: 'Metratura',\n    collapsible: false,\n    fields: [\n      { name: 'metri_teorici', label: 'Metri Teorici', required: true }\n    ]\n  }\n];\n\nconst defaultData = {\n  id_cavo: '',\n  utility: '',\n  sistema: '',\n  colore_cavo: '',\n  tipologia: '',\n  n_conduttori: '',\n  sezione: '',\n  sh: 'N',\n  ubicazione_partenza: '',\n  utenza_partenza: '',\n  descrizione_utenza_partenza: '',\n  ubicazione_arrivo: '',\n  utenza_arrivo: '',\n  descrizione_utenza_arrivo: '',\n  metri_teorici: '',\n  metratura_reale: '0'\n};\n\nconst CavoForm = ({ mode = 'add', initialData = {}, onSubmit, onSuccess, onError, onCancel }) => {\n  const navigate = useNavigate();\n\n  // Funzione per pulire i dati iniziali da valori null/undefined\n  const cleanInitialData = (data) => {\n    const cleaned = {};\n    Object.keys(defaultData).forEach(key => {\n      // Se il valore è null o undefined, usa il valore di default\n      cleaned[key] = (data[key] !== null && data[key] !== undefined) ? data[key] : defaultData[key];\n    });\n    return cleaned;\n  };\n\n  const [formData, setFormData] = useState(cleanInitialData(initialData));\n  const [formErrors, setFormErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [expanded, setExpanded] = useState({});\n  const [showWarnings, setShowWarnings] = useState(false);\n\n  // Inizializza lo stato di espansione delle sezioni collassabili\n  useEffect(() => {\n    const state = {};\n    SECTION_CONFIG.forEach(s => { if (s.collapsible) state[s.title] = true; });\n    setExpanded(state);\n  }, []);\n\n  // Gestisce l'espansione/collasso delle sezioni\n  const toggleExpand = (title) => {\n    setExpanded(prev => ({ ...prev, [title]: !prev[title] }));\n  };\n\n  // Gestisce i cambiamenti nei campi del form\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Validazione in tempo reale\n    const extra = name === 'metratura_reale' ? { metriTeorici: parseFloat(formData.metri_teorici || 0) } : {};\n    const result = validateField(name, value, extra);\n\n    // Assicurati che gli errori siano sempre stringhe o null\n    const errorMessage = result.valid ? null :\n      (typeof result.message === 'object' ?\n        (result.message?.message || JSON.stringify(result.message)) :\n        result.message);\n\n    setFormErrors(prev => ({ ...prev, [name]: errorMessage }));\n\n    setWarnings(prev => {\n      // Assicurati che i warning siano sempre stringhe o null\n      const warningMessage = result.warning ?\n        (typeof result.message === 'object' ?\n          (result.message?.message || JSON.stringify(result.message)) :\n          result.message) :\n        null;\n\n      const newWarnings = { ...prev, [name]: warningMessage };\n      // Mostra il banner di avviso se ci sono warning\n      setShowWarnings(Object.values(newWarnings).some(w => w));\n      return newWarnings;\n    });\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    const validation = validateCavoData(formData);\n\n    if (!validation.isValid) {\n      // Assicurati che gli errori siano sempre stringhe o null\n      const processedErrors = {};\n      Object.entries(validation.errors).forEach(([key, value]) => {\n        processedErrors[key] = typeof value === 'object' ?\n          (value?.message || JSON.stringify(value)) : value;\n      });\n\n      // Assicurati che i warning siano sempre stringhe o null\n      const processedWarnings = {};\n      Object.entries(validation.warnings).forEach(([key, value]) => {\n        processedWarnings[key] = typeof value === 'object' ?\n          (value?.message || JSON.stringify(value)) : value;\n      });\n\n      setFormErrors(processedErrors);\n      setWarnings(processedWarnings);\n      setShowWarnings(Object.values(processedWarnings).some(w => w));\n      setLoading(false);\n      onError('Ci sono errori nel form.');\n      return;\n    }\n\n    try {\n      const finalData = { \n        ...validation.validatedData, \n        id_cavo: validation.validatedData.id_cavo.toUpperCase(),\n        metratura_reale: '0' // Set default value\n      };\n      await onSubmit(finalData);\n      setLoading(false);\n      onSuccess(`Cavo ${mode === 'add' ? 'aggiunto' : 'modificato'} con successo.`);\n      redirectToVisualizzaCavi(navigate);\n    } catch (err) {\n      setLoading(false);\n      onError(err.message);\n    }\n  };\n\n  // Gestisce l'annullamento del form\n  const handleCancel = () => {\n    if (onCancel) {\n      onCancel();\n    } else {\n      redirectToVisualizzaCavi(navigate);\n    }\n  };\n\n  // Renderizza un singolo campo del form\n  const renderField = (field) => {\n    // Gestisce i valori null/undefined per evitare la visualizzazione di \"null\"\n    const fieldValue = formData[field.name] ?? '';\n\n    // Gestisce i messaggi di errore e warning in modo più pulito\n    const errorMessage = formErrors[field.name];\n    const warningMessage = warnings[field.name];\n\n    const helperText = errorMessage || warningMessage || '';\n\n    return (\n      <TextField\n        select={field.type === 'select'}\n        fullWidth\n        size=\"small\"\n        margin=\"none\"\n        label={field.label}\n        name={field.name}\n        value={fieldValue}\n        onChange={handleChange}\n        error={!!errorMessage}\n        helperText={helperText}\n        required={field.required}\n        variant=\"outlined\"\n        InputLabelProps={{\n          shrink: true,\n          sx: {\n            fontWeight: field.required ? 600 : 400,\n            color: field.required ? 'primary.main' : 'text.secondary'\n          }\n        }}\n        sx={{\n          '& .MuiOutlinedInput-root': {\n            '&:hover fieldset': {\n              borderColor: 'primary.main',\n            },\n            '&.Mui-focused fieldset': {\n              borderWidth: 2,\n            },\n          },\n          '& .MuiFormHelperText-root': {\n            fontSize: '0.7rem',\n            marginTop: '2px',\n            lineHeight: 1.2,\n            color: errorMessage ? 'error.main' : warningMessage ? 'warning.main' : 'text.secondary'\n          }\n        }}\n        {...(field.inputProps || {})}\n      >\n        {field.options?.map(opt => (\n          <MenuItem key={opt} value={opt}>{opt}</MenuItem>\n        ))}\n      </TextField>\n    );\n  };\n\n  return (\n    <Paper\n      elevation={2}\n      sx={{\n        border: '1px solid #e0e0e0',\n        borderRadius: 3,\n        overflow: 'hidden',\n        background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',\n        boxShadow: '0 4px 20px rgba(0,0,0,0.08)'\n      }}\n    >\n      <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n        {/* Avviso di validazione */}\n        {showWarnings && (\n          <Alert\n            severity=\"warning\"\n            icon={<WarningIcon />}\n            sx={{\n              borderRadius: 0,\n              py: 0.5,\n              backgroundColor: '#fff3cd',\n              borderBottom: '1px solid #ffeaa7',\n              '& .MuiAlert-message': {\n                fontWeight: 500,\n                fontSize: '0.85rem'\n              }\n            }}\n          >\n            Alcuni campi potrebbero necessitare revisione\n          </Alert>\n        )}\n\n        {/* Sezioni del form */}\n        {SECTION_CONFIG.map((section, index) => {\n          const isExpanded = section.collapsible ? expanded[section.title] : true;\n\n          return (\n            <Box key={section.title} sx={{ borderBottom: index < SECTION_CONFIG.length - 1 ? '1px solid #e8eaed' : 'none' }}>\n              {/* Intestazione della sezione */}\n              <Box\n                sx={{\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  px: 2.5,\n                  py: 1,\n                  background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n                  borderBottom: isExpanded ? '1px solid #e8eaed' : 'none',\n                  cursor: section.collapsible ? 'pointer' : 'default',\n                  transition: 'all 0.2s ease-in-out',\n                  '&:hover': section.collapsible ? {\n                    background: 'linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%)',\n                  } : {}\n                }}\n                onClick={() => section.collapsible && toggleExpand(section.title)}\n              >\n                <Typography\n                  variant=\"subtitle1\"\n                  sx={{\n                    fontWeight: 700,\n                    color: '#495057',\n                    fontSize: '0.9rem',\n                    letterSpacing: '0.3px',\n                    textTransform: 'uppercase'\n                  }}\n                >\n                  {section.title}\n                </Typography>\n\n                {section.collapsible && (\n                  <IconButton\n                    size=\"small\"\n                    sx={{ ml: 1 }}\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      toggleExpand(section.title);\n                    }}\n                  >\n                    {isExpanded ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}\n                  </IconButton>\n                )}\n              </Box>\n\n              {/* Contenuto della sezione */}\n              {isExpanded && (\n                <Box sx={{\n                  p: 2,\n                  backgroundColor: '#ffffff',\n                  borderBottom: index < SECTION_CONFIG.length - 1 ? '1px solid #f1f3f4' : 'none'\n                }}>\n                  <Grid container spacing={1.5}>\n                    {section.fields.map(field => (\n                      <Grid\n                        item\n                        xs={field.gridSize?.xs || 12}\n                        sm={field.gridSize?.sm || 6}\n                        md={field.gridSize?.md || 4}\n                        key={field.name}\n                      >\n                        {renderField(field)}\n                      </Grid>\n                    ))}\n                  </Grid>\n                </Box>\n              )}\n            </Box>\n          );\n        })}\n\n        {/* Pulsanti di azione */}\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'flex-end',\n            gap: 1.5,\n            p: 2,\n            background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n            borderTop: '1px solid #e8eaed'\n          }}\n        >\n          <Tooltip title=\"Annulla e torna indietro\" arrow>\n            <Button\n              variant=\"outlined\"\n              color=\"inherit\"\n              onClick={handleCancel}\n              disabled={loading}\n              startIcon={<CancelIcon />}\n              size=\"medium\"\n              sx={{\n                borderColor: '#6c757d',\n                color: '#6c757d',\n                fontWeight: 600,\n                px: 2.5,\n                py: 0.8,\n                '&:hover': {\n                  borderColor: '#495057',\n                  backgroundColor: '#f8f9fa',\n                  color: '#495057'\n                }\n              }}\n            >\n              Annulla\n            </Button>\n          </Tooltip>\n\n          <Tooltip title={mode === 'add' ? 'Salva nuovo cavo' : 'Salva modifiche al cavo'} arrow>\n            <Button\n              type=\"submit\"\n              variant=\"contained\"\n              color=\"primary\"\n              startIcon={loading ? <CircularProgress size={18} color=\"inherit\" /> : <SaveIcon />}\n              disabled={loading}\n              size=\"medium\"\n              sx={{\n                fontWeight: 600,\n                px: 3,\n                py: 0.8,\n                background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',\n                boxShadow: '0 4px 12px rgba(0,123,255,0.3)',\n                '&:hover': {\n                  background: 'linear-gradient(135deg, #0056b3 0%, #004085 100%)',\n                  boxShadow: '0 6px 16px rgba(0,123,255,0.4)',\n                  transform: 'translateY(-1px)'\n                },\n                '&:disabled': {\n                  background: '#6c757d',\n                  boxShadow: 'none'\n                },\n                transition: 'all 0.2s ease-in-out'\n              }}\n            >\n              {loading ? 'Salvataggio...' : mode === 'add' ? 'Salva Cavo' : 'Aggiorna Cavo'}\n            </Button>\n          </Tooltip>\n        </Box>\n      </Box>\n    </Paper>\n  );\n};\n\nexport default CavoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,IAAI,QACC,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,iBAAiB,IAAIC,qBAAqB,EAC1CC,eAAe,IAAIC,mBAAmB,QACjC,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,gBAAgB,EAAEC,aAAa,QAAQ,6BAA6B;AAC7E,SAASC,wBAAwB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,cAAc,GAAG,CACrB;EACEC,KAAK,EAAE,uBAAuB;EAC9BC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE;MAAEC,KAAK,EAAE;QAAEC,aAAa,EAAE;MAAY;IAAE;EAAE,CAAC,EAC5G;IAAEL,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACrD;IAAEF,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC;AAEzC,CAAC,EACD;EACEJ,KAAK,EAAE,0BAA0B;EACjCC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC7C;IAAED,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC;EACzC;EACA;IAAED,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAa;EACvC;EAAA;AAEJ,CAAC,EACD;EACEJ,KAAK,EAAE,UAAU;EACjBC,WAAW,EAAE,IAAI;EACjBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,qBAAqB;IAAEC,KAAK,EAAE,qBAAqB;IAAEK,QAAQ,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE;EAAE,CAAC,EACjG;IAAET,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE,iBAAiB;IAAEK,QAAQ,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE;EAAE,CAAC,EACzF;IAAET,IAAI,EAAE,6BAA6B;IAAEC,KAAK,EAAE,6BAA6B;IAAEK,QAAQ,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE;EAAE,CAAC;AAErH,CAAC,EACD;EACEZ,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE,IAAI;EACjBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,KAAK,EAAE,mBAAmB;IAAEK,QAAQ,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE;EAAE,CAAC,EAC7F;IAAET,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEK,QAAQ,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE;EAAE,CAAC,EACrF;IAAET,IAAI,EAAE,2BAA2B;IAAEC,KAAK,EAAE,2BAA2B;IAAEK,QAAQ,EAAE;MAAEC,EAAE,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE;EAAE,CAAC;AAEjH,CAAC,EACD;EACEZ,KAAK,EAAE,WAAW;EAClBC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,QAAQ,EAAE;EAAK,CAAC;AAErE,CAAC,CACF;AAED,MAAMQ,WAAW,GAAG;EAClBC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,EAAE;EACXC,WAAW,EAAE,EAAE;EACfC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,EAAE;EAChBC,OAAO,EAAE,EAAE;EACXC,EAAE,EAAE,GAAG;EACPC,mBAAmB,EAAE,EAAE;EACvBC,eAAe,EAAE,EAAE;EACnBC,2BAA2B,EAAE,EAAE;EAC/BC,iBAAiB,EAAE,EAAE;EACrBC,aAAa,EAAE,EAAE;EACjBC,yBAAyB,EAAE,EAAE;EAC7BC,aAAa,EAAE,EAAE;EACjBC,eAAe,EAAE;AACnB,CAAC;AAED,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,IAAI,GAAG,KAAK;EAAEC,WAAW,GAAG,CAAC,CAAC;EAAEC,QAAQ;EAAEC,SAAS;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/F,MAAMC,QAAQ,GAAG7C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM8C,gBAAgB,GAAIC,IAAI,IAAK;IACjC,MAAMC,OAAO,GAAG,CAAC,CAAC;IAClBC,MAAM,CAACC,IAAI,CAAC9B,WAAW,CAAC,CAAC+B,OAAO,CAACC,GAAG,IAAI;MACtC;MACAJ,OAAO,CAACI,GAAG,CAAC,GAAIL,IAAI,CAACK,GAAG,CAAC,KAAK,IAAI,IAAIL,IAAI,CAACK,GAAG,CAAC,KAAKC,SAAS,GAAIN,IAAI,CAACK,GAAG,CAAC,GAAGhC,WAAW,CAACgC,GAAG,CAAC;IAC/F,CAAC,CAAC;IACF,OAAOJ,OAAO;EAChB,CAAC;EAED,MAAM,CAACM,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAACuE,gBAAgB,CAACP,WAAW,CAAC,CAAC;EACvE,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACmF,QAAQ,EAAEC,WAAW,CAAC,GAAGpF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACqF,OAAO,EAAEC,UAAU,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuF,QAAQ,EAAEC,WAAW,CAAC,GAAGxF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACyF,YAAY,EAAEC,eAAe,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM0F,KAAK,GAAG,CAAC,CAAC;IAChB5D,cAAc,CAAC6C,OAAO,CAACgB,CAAC,IAAI;MAAE,IAAIA,CAAC,CAAC3D,WAAW,EAAE0D,KAAK,CAACC,CAAC,CAAC5D,KAAK,CAAC,GAAG,IAAI;IAAE,CAAC,CAAC;IAC1EwD,WAAW,CAACG,KAAK,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,YAAY,GAAI7D,KAAK,IAAK;IAC9BwD,WAAW,CAACM,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC9D,KAAK,GAAG,CAAC8D,IAAI,CAAC9D,KAAK;IAAE,CAAC,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,MAAM+D,YAAY,GAAIC,CAAC,IAAK;IAAA,IAAAC,eAAA;IAC1B,MAAM;MAAE9D,IAAI;MAAE+D;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCnB,WAAW,CAACc,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC3D,IAAI,GAAG+D;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACA,MAAME,KAAK,GAAGjE,IAAI,KAAK,iBAAiB,GAAG;MAAEkE,YAAY,EAAEC,UAAU,CAACvB,QAAQ,CAACnB,aAAa,IAAI,CAAC;IAAE,CAAC,GAAG,CAAC,CAAC;IACzG,MAAM2C,MAAM,GAAG5E,aAAa,CAACQ,IAAI,EAAE+D,KAAK,EAAEE,KAAK,CAAC;;IAEhD;IACA,MAAMI,YAAY,GAAGD,MAAM,CAACE,KAAK,GAAG,IAAI,GACrC,OAAOF,MAAM,CAACG,OAAO,KAAK,QAAQ,GAChC,EAAAT,eAAA,GAAAM,MAAM,CAACG,OAAO,cAAAT,eAAA,uBAAdA,eAAA,CAAgBS,OAAO,KAAIC,IAAI,CAACC,SAAS,CAACL,MAAM,CAACG,OAAO,CAAC,GAC1DH,MAAM,CAACG,OAAQ;IAEnBxB,aAAa,CAACY,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC3D,IAAI,GAAGqE;IAAa,CAAC,CAAC,CAAC;IAE1DpB,WAAW,CAACU,IAAI,IAAI;MAAA,IAAAe,gBAAA;MAClB;MACA,MAAMC,cAAc,GAAGP,MAAM,CAACQ,OAAO,GAClC,OAAOR,MAAM,CAACG,OAAO,KAAK,QAAQ,GAChC,EAAAG,gBAAA,GAAAN,MAAM,CAACG,OAAO,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBH,OAAO,KAAIC,IAAI,CAACC,SAAS,CAACL,MAAM,CAACG,OAAO,CAAC,GAC1DH,MAAM,CAACG,OAAO,GAChB,IAAI;MAEN,MAAMM,WAAW,GAAG;QAAE,GAAGlB,IAAI;QAAE,CAAC3D,IAAI,GAAG2E;MAAe,CAAC;MACvD;MACApB,eAAe,CAAChB,MAAM,CAACuC,MAAM,CAACD,WAAW,CAAC,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC;MACxD,OAAOH,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMI,YAAY,GAAG,MAAOpB,CAAC,IAAK;IAChCA,CAAC,CAACqB,cAAc,CAAC,CAAC;IAClB/B,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMgC,UAAU,GAAG5F,gBAAgB,CAACqD,QAAQ,CAAC;IAE7C,IAAI,CAACuC,UAAU,CAACC,OAAO,EAAE;MACvB;MACA,MAAMC,eAAe,GAAG,CAAC,CAAC;MAC1B9C,MAAM,CAAC+C,OAAO,CAACH,UAAU,CAACI,MAAM,CAAC,CAAC9C,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEqB,KAAK,CAAC,KAAK;QAC1DsB,eAAe,CAAC3C,GAAG,CAAC,GAAG,OAAOqB,KAAK,KAAK,QAAQ,GAC7C,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,OAAO,KAAIC,IAAI,CAACC,SAAS,CAACV,KAAK,CAAC,GAAIA,KAAK;MACrD,CAAC,CAAC;;MAEF;MACA,MAAMyB,iBAAiB,GAAG,CAAC,CAAC;MAC5BjD,MAAM,CAAC+C,OAAO,CAACH,UAAU,CAACnC,QAAQ,CAAC,CAACP,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEqB,KAAK,CAAC,KAAK;QAC5DyB,iBAAiB,CAAC9C,GAAG,CAAC,GAAG,OAAOqB,KAAK,KAAK,QAAQ,GAC/C,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEQ,OAAO,KAAIC,IAAI,CAACC,SAAS,CAACV,KAAK,CAAC,GAAIA,KAAK;MACrD,CAAC,CAAC;MAEFhB,aAAa,CAACsC,eAAe,CAAC;MAC9BpC,WAAW,CAACuC,iBAAiB,CAAC;MAC9BjC,eAAe,CAAChB,MAAM,CAACuC,MAAM,CAACU,iBAAiB,CAAC,CAACT,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC;MAC9D7B,UAAU,CAAC,KAAK,CAAC;MACjBnB,OAAO,CAAC,0BAA0B,CAAC;MACnC;IACF;IAEA,IAAI;MACF,MAAMyD,SAAS,GAAG;QAChB,GAAGN,UAAU,CAACO,aAAa;QAC3B/E,OAAO,EAAEwE,UAAU,CAACO,aAAa,CAAC/E,OAAO,CAACgF,WAAW,CAAC,CAAC;QACvDjE,eAAe,EAAE,GAAG,CAAC;MACvB,CAAC;MACD,MAAMI,QAAQ,CAAC2D,SAAS,CAAC;MACzBtC,UAAU,CAAC,KAAK,CAAC;MACjBpB,SAAS,CAAC,QAAQH,IAAI,KAAK,KAAK,GAAG,UAAU,GAAG,YAAY,gBAAgB,CAAC;MAC7EnC,wBAAwB,CAAC0C,QAAQ,CAAC;IACpC,CAAC,CAAC,OAAOyD,GAAG,EAAE;MACZzC,UAAU,CAAC,KAAK,CAAC;MACjBnB,OAAO,CAAC4D,GAAG,CAACrB,OAAO,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMsB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI5D,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACLxC,wBAAwB,CAAC0C,QAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAM2D,WAAW,GAAIC,KAAK,IAAK;IAAA,IAAAC,oBAAA,EAAAC,cAAA;IAC7B;IACA,MAAMC,UAAU,IAAAF,oBAAA,GAAGpD,QAAQ,CAACmD,KAAK,CAAC/F,IAAI,CAAC,cAAAgG,oBAAA,cAAAA,oBAAA,GAAI,EAAE;;IAE7C;IACA,MAAM3B,YAAY,GAAGvB,UAAU,CAACiD,KAAK,CAAC/F,IAAI,CAAC;IAC3C,MAAM2E,cAAc,GAAG3B,QAAQ,CAAC+C,KAAK,CAAC/F,IAAI,CAAC;IAE3C,MAAMmG,UAAU,GAAG9B,YAAY,IAAIM,cAAc,IAAI,EAAE;IAEvD,oBACEhF,OAAA,CAAC3B,SAAS;MACRoI,MAAM,EAAEL,KAAK,CAACM,IAAI,KAAK,QAAS;MAChCC,SAAS;MACTC,IAAI,EAAC,OAAO;MACZC,MAAM,EAAC,MAAM;MACbvG,KAAK,EAAE8F,KAAK,CAAC9F,KAAM;MACnBD,IAAI,EAAE+F,KAAK,CAAC/F,IAAK;MACjB+D,KAAK,EAAEmC,UAAW;MAClBO,QAAQ,EAAE7C,YAAa;MACvB8C,KAAK,EAAE,CAAC,CAACrC,YAAa;MACtB8B,UAAU,EAAEA,UAAW;MACvBjG,QAAQ,EAAE6F,KAAK,CAAC7F,QAAS;MACzByG,OAAO,EAAC,UAAU;MAClBC,eAAe,EAAE;QACfC,MAAM,EAAE,IAAI;QACZC,EAAE,EAAE;UACFC,UAAU,EAAEhB,KAAK,CAAC7F,QAAQ,GAAG,GAAG,GAAG,GAAG;UACtC8G,KAAK,EAAEjB,KAAK,CAAC7F,QAAQ,GAAG,cAAc,GAAG;QAC3C;MACF,CAAE;MACF4G,EAAE,EAAE;QACF,0BAA0B,EAAE;UAC1B,kBAAkB,EAAE;YAClBG,WAAW,EAAE;UACf,CAAC;UACD,wBAAwB,EAAE;YACxBC,WAAW,EAAE;UACf;QACF,CAAC;QACD,2BAA2B,EAAE;UAC3BC,QAAQ,EAAE,QAAQ;UAClBC,SAAS,EAAE,KAAK;UAChBC,UAAU,EAAE,GAAG;UACfL,KAAK,EAAE3C,YAAY,GAAG,YAAY,GAAGM,cAAc,GAAG,cAAc,GAAG;QACzE;MACF,CAAE;MAAA,IACGoB,KAAK,CAAC5F,UAAU,IAAI,CAAC,CAAC;MAAAmH,QAAA,GAAArB,cAAA,GAE1BF,KAAK,CAACwB,OAAO,cAAAtB,cAAA,uBAAbA,cAAA,CAAeuB,GAAG,CAACC,GAAG,iBACrB9H,OAAA,CAACxB,QAAQ;QAAW4F,KAAK,EAAE0D,GAAI;QAAAH,QAAA,EAAEG;MAAG,GAArBA,GAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA6B,CAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAEhB,CAAC;EAED,oBACElI,OAAA,CAACnB,KAAK;IACJsJ,SAAS,EAAE,CAAE;IACbhB,EAAE,EAAE;MACFiB,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,mDAAmD;MAC/DC,SAAS,EAAE;IACb,CAAE;IAAAb,QAAA,eAEF3H,OAAA,CAAC5B,GAAG;MAACqK,SAAS,EAAC,MAAM;MAACtG,QAAQ,EAAEmD,YAAa;MAACoD,UAAU;MAAAf,QAAA,GAErDhE,YAAY,iBACX3D,OAAA,CAACvB,KAAK;QACJkK,QAAQ,EAAC,SAAS;QAClBC,IAAI,eAAE5I,OAAA,CAACZ,WAAW;UAAA2I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBf,EAAE,EAAE;UACFkB,YAAY,EAAE,CAAC;UACfQ,EAAE,EAAE,GAAG;UACPC,eAAe,EAAE,SAAS;UAC1BC,YAAY,EAAE,mBAAmB;UACjC,qBAAqB,EAAE;YACrB3B,UAAU,EAAE,GAAG;YACfI,QAAQ,EAAE;UACZ;QACF,CAAE;QAAAG,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,EAGAjI,cAAc,CAAC4H,GAAG,CAAC,CAACmB,OAAO,EAAEC,KAAK,KAAK;QACtC,MAAMC,UAAU,GAAGF,OAAO,CAAC7I,WAAW,GAAGsD,QAAQ,CAACuF,OAAO,CAAC9I,KAAK,CAAC,GAAG,IAAI;QAEvE,oBACEF,OAAA,CAAC5B,GAAG;UAAqB+I,EAAE,EAAE;YAAE4B,YAAY,EAAEE,KAAK,GAAGhJ,cAAc,CAACkJ,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG;UAAO,CAAE;UAAAxB,QAAA,gBAE9G3H,OAAA,CAAC5B,GAAG;YACF+I,EAAE,EAAE;cACFiC,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/BC,UAAU,EAAE,QAAQ;cACpBC,EAAE,EAAE,GAAG;cACPV,EAAE,EAAE,CAAC;cACLN,UAAU,EAAE,mDAAmD;cAC/DQ,YAAY,EAAEG,UAAU,GAAG,mBAAmB,GAAG,MAAM;cACvDM,MAAM,EAAER,OAAO,CAAC7I,WAAW,GAAG,SAAS,GAAG,SAAS;cACnDsJ,UAAU,EAAE,sBAAsB;cAClC,SAAS,EAAET,OAAO,CAAC7I,WAAW,GAAG;gBAC/BoI,UAAU,EAAE;cACd,CAAC,GAAG,CAAC;YACP,CAAE;YACFmB,OAAO,EAAEA,CAAA,KAAMV,OAAO,CAAC7I,WAAW,IAAI4D,YAAY,CAACiF,OAAO,CAAC9I,KAAK,CAAE;YAAAyH,QAAA,gBAElE3H,OAAA,CAACrB,UAAU;cACTqI,OAAO,EAAC,WAAW;cACnBG,EAAE,EAAE;gBACFC,UAAU,EAAE,GAAG;gBACfC,KAAK,EAAE,SAAS;gBAChBG,QAAQ,EAAE,QAAQ;gBAClBmC,aAAa,EAAE,OAAO;gBACtBjJ,aAAa,EAAE;cACjB,CAAE;cAAAiH,QAAA,EAEDqB,OAAO,CAAC9I;YAAK;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAEZc,OAAO,CAAC7I,WAAW,iBAClBH,OAAA,CAAClB,UAAU;cACT8H,IAAI,EAAC,OAAO;cACZO,EAAE,EAAE;gBAAEyC,EAAE,EAAE;cAAE,CAAE;cACdF,OAAO,EAAGxF,CAAC,IAAK;gBACdA,CAAC,CAAC2F,eAAe,CAAC,CAAC;gBACnB9F,YAAY,CAACiF,OAAO,CAAC9I,KAAK,CAAC;cAC7B,CAAE;cAAAyH,QAAA,EAEDuB,UAAU,gBAAGlJ,OAAA,CAACN,mBAAmB;gBAAAqI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGlI,OAAA,CAACR,qBAAqB;gBAAAuI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLgB,UAAU,iBACTlJ,OAAA,CAAC5B,GAAG;YAAC+I,EAAE,EAAE;cACP2C,CAAC,EAAE,CAAC;cACJhB,eAAe,EAAE,SAAS;cAC1BC,YAAY,EAAEE,KAAK,GAAGhJ,cAAc,CAACkJ,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG;YAC1E,CAAE;YAAAxB,QAAA,eACA3H,OAAA,CAAChB,IAAI;cAAC+K,SAAS;cAACC,OAAO,EAAE,GAAI;cAAArC,QAAA,EAC1BqB,OAAO,CAAC5I,MAAM,CAACyH,GAAG,CAACzB,KAAK;gBAAA,IAAA6D,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;gBAAA,oBACvBnK,OAAA,CAAChB,IAAI;kBACHoL,IAAI;kBACJxJ,EAAE,EAAE,EAAAqJ,eAAA,GAAA7D,KAAK,CAACzF,QAAQ,cAAAsJ,eAAA,uBAAdA,eAAA,CAAgBrJ,EAAE,KAAI,EAAG;kBAC7BC,EAAE,EAAE,EAAAqJ,gBAAA,GAAA9D,KAAK,CAACzF,QAAQ,cAAAuJ,gBAAA,uBAAdA,gBAAA,CAAgBrJ,EAAE,KAAI,CAAE;kBAC5BC,EAAE,EAAE,EAAAqJ,gBAAA,GAAA/D,KAAK,CAACzF,QAAQ,cAAAwJ,gBAAA,uBAAdA,gBAAA,CAAgBrJ,EAAE,KAAI,CAAE;kBAAA6G,QAAA,EAG3BxB,WAAW,CAACC,KAAK;gBAAC,GAFdA,KAAK,CAAC/F,IAAI;kBAAA0H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGX,CAAC;cAAA,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA,GAnEOc,OAAO,CAAC9I,KAAK;UAAA6H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoElB,CAAC;MAEV,CAAC,CAAC,eAGFlI,OAAA,CAAC5B,GAAG;QACF+I,EAAE,EAAE;UACFiC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,UAAU;UAC1BgB,GAAG,EAAE,GAAG;UACRP,CAAC,EAAE,CAAC;UACJvB,UAAU,EAAE,mDAAmD;UAC/D+B,SAAS,EAAE;QACb,CAAE;QAAA3C,QAAA,gBAEF3H,OAAA,CAACjB,OAAO;UAACmB,KAAK,EAAC,0BAA0B;UAACqK,KAAK;UAAA5C,QAAA,eAC7C3H,OAAA,CAAC1B,MAAM;YACL0I,OAAO,EAAC,UAAU;YAClBK,KAAK,EAAC,SAAS;YACfqC,OAAO,EAAExD,YAAa;YACtBsE,QAAQ,EAAEjH,OAAQ;YAClBkH,SAAS,eAAEzK,OAAA,CAACV,UAAU;cAAAyI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BtB,IAAI,EAAC,QAAQ;YACbO,EAAE,EAAE;cACFG,WAAW,EAAE,SAAS;cACtBD,KAAK,EAAE,SAAS;cAChBD,UAAU,EAAE,GAAG;cACfmC,EAAE,EAAE,GAAG;cACPV,EAAE,EAAE,GAAG;cACP,SAAS,EAAE;gBACTvB,WAAW,EAAE,SAAS;gBACtBwB,eAAe,EAAE,SAAS;gBAC1BzB,KAAK,EAAE;cACT;YACF,CAAE;YAAAM,QAAA,EACH;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEVlI,OAAA,CAACjB,OAAO;UAACmB,KAAK,EAAE+B,IAAI,KAAK,KAAK,GAAG,kBAAkB,GAAG,yBAA0B;UAACsI,KAAK;UAAA5C,QAAA,eACpF3H,OAAA,CAAC1B,MAAM;YACLoI,IAAI,EAAC,QAAQ;YACbM,OAAO,EAAC,WAAW;YACnBK,KAAK,EAAC,SAAS;YACfoD,SAAS,EAAElH,OAAO,gBAAGvD,OAAA,CAACtB,gBAAgB;cAACkI,IAAI,EAAE,EAAG;cAACS,KAAK,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlI,OAAA,CAACd,QAAQ;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnFsC,QAAQ,EAAEjH,OAAQ;YAClBqD,IAAI,EAAC,QAAQ;YACbO,EAAE,EAAE;cACFC,UAAU,EAAE,GAAG;cACfmC,EAAE,EAAE,CAAC;cACLV,EAAE,EAAE,GAAG;cACPN,UAAU,EAAE,mDAAmD;cAC/DC,SAAS,EAAE,gCAAgC;cAC3C,SAAS,EAAE;gBACTD,UAAU,EAAE,mDAAmD;gBAC/DC,SAAS,EAAE,gCAAgC;gBAC3CkC,SAAS,EAAE;cACb,CAAC;cACD,YAAY,EAAE;gBACZnC,UAAU,EAAE,SAAS;gBACrBC,SAAS,EAAE;cACb,CAAC;cACDiB,UAAU,EAAE;YACd,CAAE;YAAA9B,QAAA,EAEDpE,OAAO,GAAG,gBAAgB,GAAGtB,IAAI,KAAK,KAAK,GAAG,YAAY,GAAG;UAAe;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAAC3F,EAAA,CAjWIP,QAAQ;EAAA,QACKrC,WAAW;AAAA;AAAAgL,EAAA,GADxB3I,QAAQ;AAmWd,eAAeA,QAAQ;AAAC,IAAA2I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}