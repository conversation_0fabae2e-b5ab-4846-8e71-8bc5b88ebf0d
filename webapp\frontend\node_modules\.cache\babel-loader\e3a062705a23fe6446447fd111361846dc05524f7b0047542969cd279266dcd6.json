{"ast": null, "code": "import { formatDistance } from \"./lb/_lib/formatDistance.js\";\nimport { formatLong } from \"./lb/_lib/formatLong.js\";\nimport { formatRelative } from \"./lb/_lib/formatRelative.js\";\nimport { localize } from \"./lb/_lib/localize.js\";\nimport { match } from \"./lb/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Luxembourgish locale.\n * @language Luxembourgish\n * @iso-639-2 ltz\n * <AUTHOR> [@dwaxweiler](https://github.com/dwaxweiler)\n */\nexport const lb = {\n  code: \"lb\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default lb;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "lb", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/lb.js"], "sourcesContent": ["import { formatDistance } from \"./lb/_lib/formatDistance.js\";\nimport { formatLong } from \"./lb/_lib/formatLong.js\";\nimport { formatRelative } from \"./lb/_lib/formatRelative.js\";\nimport { localize } from \"./lb/_lib/localize.js\";\nimport { match } from \"./lb/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Luxembourgish locale.\n * @language Luxembourgish\n * @iso-639-2 ltz\n * <AUTHOR> [@dwaxweiler](https://github.com/dwaxweiler)\n */\nexport const lb = {\n  code: \"lb\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default lb;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}