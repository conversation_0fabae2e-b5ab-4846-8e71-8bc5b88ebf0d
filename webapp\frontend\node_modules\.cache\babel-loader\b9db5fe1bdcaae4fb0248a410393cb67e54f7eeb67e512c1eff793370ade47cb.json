{"ast": null, "code": "// Inspired by https://github.com/material-components/material-components-ios/blob/bca36107405594d5b7b16265a5b0ed698f85a5ee/components/Elevation/src/UIColor%2BMaterialElevation.m#L61\nconst getOverlayAlpha = elevation => {\n  let alphaValue;\n  if (elevation < 1) {\n    alphaValue = 5.11916 * elevation ** 2;\n  } else {\n    alphaValue = 4.5 * Math.log(elevation + 1) + 2;\n  }\n  return (alphaValue / 100).toFixed(2);\n};\nexport default getOverlayAlpha;", "map": {"version": 3, "names": ["getOverlayAlpha", "elevation", "alphaValue", "Math", "log", "toFixed"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/material/styles/getOverlayAlpha.js"], "sourcesContent": ["// Inspired by https://github.com/material-components/material-components-ios/blob/bca36107405594d5b7b16265a5b0ed698f85a5ee/components/Elevation/src/UIColor%2BMaterialElevation.m#L61\nconst getOverlayAlpha = elevation => {\n  let alphaValue;\n  if (elevation < 1) {\n    alphaValue = 5.11916 * elevation ** 2;\n  } else {\n    alphaValue = 4.5 * Math.log(elevation + 1) + 2;\n  }\n  return (alphaValue / 100).toFixed(2);\n};\nexport default getOverlayAlpha;"], "mappings": "AAAA;AACA,MAAMA,eAAe,GAAGC,SAAS,IAAI;EACnC,IAAIC,UAAU;EACd,IAAID,SAAS,GAAG,CAAC,EAAE;IACjBC,UAAU,GAAG,OAAO,GAAGD,SAAS,IAAI,CAAC;EACvC,CAAC,MAAM;IACLC,UAAU,GAAG,GAAG,GAAGC,IAAI,CAACC,GAAG,CAACH,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC;EAChD;EACA,OAAO,CAACC,UAAU,GAAG,GAAG,EAAEG,OAAO,CAAC,CAAC,CAAC;AACtC,CAAC;AACD,eAAeL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}