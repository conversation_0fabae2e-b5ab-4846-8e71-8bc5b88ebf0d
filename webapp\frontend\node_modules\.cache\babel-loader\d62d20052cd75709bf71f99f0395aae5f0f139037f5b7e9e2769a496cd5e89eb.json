{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\PosaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, Typography, Paper, Button, IconButton, Alert } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon, Home as HomeIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PosaCaviPage = () => {\n  _s();\n  const {\n    isImpersonating\n  } = useAuth();\n  const navigate = useNavigate();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Torna alla pagina del cantiere\n  const handleBackToCantiere = () => {\n    if (cantiereId) {\n      navigate(`/dashboard/cantieri/${cantiereId}`);\n    } else {\n      navigate('/dashboard/cantieri');\n    }\n  };\n\n  // Torna al menu amministratore (solo per admin che impersonano un utente)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce le notifiche\n  const handleSuccess = message => {\n    // Qui puoi implementare una notifica di successo se necessario\n    console.log('Successo:', message);\n  };\n  const handleError = message => {\n    // Qui puoi implementare una notifica di errore se necessario\n    console.error('Errore:', message);\n  };\n  if (!cantiereId || isNaN(cantiereId)) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: \"Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 22\n        }, this),\n        onClick: handleBackToCantiere,\n        children: \"Torna ai Cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToCantiere,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          children: \"Posa Cavi e Collegamenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), isImpersonating && /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 24\n        }, this),\n        onClick: handleBackToAdmin,\n        children: \"Torna al Menu Admin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Cantiere: \", cantiereName, \" (ID: \", cantiereId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 24\n          }, this),\n          onClick: handleBackToCantiere,\n          children: \"Torna al Cantiere\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n      cantiereId: cantiereId,\n      onSuccess: handleSuccess,\n      onError: handleError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_s(PosaCaviPage, \"2LrHI1hw5pqPbj9zv+06AB4ZF78=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = PosaCaviPage;\nexport default PosaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"PosaCaviPage\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Home", "HomeIcon", "useNavigate", "useAuth", "PosaCaviCollegamenti", "jsxDEV", "_jsxDEV", "PosaCaviPage", "_s", "isImpersonating", "navigate", "cantiereId", "parseInt", "localStorage", "getItem", "cantiereName", "handleBackToCantiere", "handleBackToAdmin", "handleSuccess", "message", "console", "log", "handleError", "error", "isNaN", "children", "severity", "sx", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "startIcon", "onClick", "display", "alignItems", "justifyContent", "mr", "window", "location", "reload", "ml", "color", "title", "p", "onSuccess", "onError", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/PosaCaviPage.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Home as HomeIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\n\nconst PosaCaviPage = () => {\n  const { isImpersonating } = useAuth();\n  const navigate = useNavigate();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Torna alla pagina del cantiere\n  const handleBackToCantiere = () => {\n    if (cantiereId) {\n      navigate(`/dashboard/cantieri/${cantiereId}`);\n    } else {\n      navigate('/dashboard/cantieri');\n    }\n  };\n\n  // Torna al menu amministratore (solo per admin che impersonano un utente)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce le notifiche\n  const handleSuccess = (message) => {\n    // Qui puoi implementare una notifica di successo se necessario\n    console.log('Successo:', message);\n  };\n\n  const handleError = (message) => {\n    // Qui puoi implementare una notifica di errore se necessario\n    console.error('Errore:', message);\n  };\n\n  if (!cantiereId || isNaN(cantiereId)) {\n    return (\n      <Box>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\n        </Alert>\n        <Button\n          variant=\"contained\"\n          startIcon={<ArrowBackIcon />}\n          onClick={handleBackToCantiere}\n        >\n          Torna ai Cantieri\n        </Button>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantiere} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Posa Cavi e Collegamenti\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        {isImpersonating && (\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<HomeIcon />}\n            onClick={handleBackToAdmin}\n          >\n            Torna al Menu Admin\n          </Button>\n        )}\n      </Box>\n\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h6\">\n            Cantiere: {cantiereName} (ID: {cantiereId})\n          </Typography>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<ArrowBackIcon />}\n            onClick={handleBackToCantiere}\n          >\n            Torna al Cantiere\n          </Button>\n        </Box>\n      </Paper>\n\n      <PosaCaviCollegamenti\n        cantiereId={cantiereId}\n        onSuccess={handleSuccess}\n        onError={handleError}\n      />\n    </Box>\n  );\n};\n\nexport default PosaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,oBAAoB,MAAM,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAgB,CAAC,GAAGN,OAAO,CAAC,CAAC;EACrC,MAAMO,QAAQ,GAAGR,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMS,UAAU,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;EAC3E,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAAI,YAAYH,UAAU,EAAE;;EAE7F;EACA,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIL,UAAU,EAAE;MACdD,QAAQ,CAAC,uBAAuBC,UAAU,EAAE,CAAC;IAC/C,CAAC,MAAM;MACLD,QAAQ,CAAC,qBAAqB,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMO,iBAAiB,GAAGA,CAAA,KAAM;IAC9BP,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMQ,aAAa,GAAIC,OAAO,IAAK;IACjC;IACAC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEF,OAAO,CAAC;EACnC,CAAC;EAED,MAAMG,WAAW,GAAIH,OAAO,IAAK;IAC/B;IACAC,OAAO,CAACG,KAAK,CAAC,SAAS,EAAEJ,OAAO,CAAC;EACnC,CAAC;EAED,IAAI,CAACR,UAAU,IAAIa,KAAK,CAACb,UAAU,CAAC,EAAE;IACpC,oBACEL,OAAA,CAAChB,GAAG;MAAAmC,QAAA,gBACFnB,OAAA,CAACX,KAAK;QAAC+B,QAAQ,EAAC,OAAO;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAEvC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR1B,OAAA,CAACb,MAAM;QACLwC,OAAO,EAAC,WAAW;QACnBC,SAAS,eAAE5B,OAAA,CAACT,aAAa;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BG,OAAO,EAAEnB,oBAAqB;QAAAS,QAAA,EAC/B;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE1B,OAAA,CAAChB,GAAG;IAAAmC,QAAA,gBACFnB,OAAA,CAAChB,GAAG;MAACqC,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEQ,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAb,QAAA,gBACzFnB,OAAA,CAAChB,GAAG;QAACqC,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAZ,QAAA,gBACjDnB,OAAA,CAACZ,UAAU;UAACyC,OAAO,EAAEnB,oBAAqB;UAACW,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,eACvDnB,OAAA,CAACT,aAAa;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACb1B,OAAA,CAACf,UAAU;UAAC0C,OAAO,EAAC,IAAI;UAAAR,QAAA,EAAC;QAEzB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb1B,OAAA,CAACZ,UAAU;UACTyC,OAAO,EAAEA,CAAA,KAAMK,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCf,EAAE,EAAE;YAAEgB,EAAE,EAAE;UAAE,CAAE;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAC,oBAAoB;UAAApB,QAAA,eAE1BnB,OAAA,CAACP,WAAW;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EACLvB,eAAe,iBACdH,OAAA,CAACb,MAAM;QACLwC,OAAO,EAAC,WAAW;QACnBW,KAAK,EAAC,SAAS;QACfV,SAAS,eAAE5B,OAAA,CAACL,QAAQ;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBG,OAAO,EAAElB,iBAAkB;QAAAQ,QAAA,EAC5B;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN1B,OAAA,CAACd,KAAK;MAACmC,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEkB,CAAC,EAAE;MAAE,CAAE;MAAArB,QAAA,eACzBnB,OAAA,CAAChB,GAAG;QAACqC,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,eAAe;UAAED,UAAU,EAAE;QAAS,CAAE;QAAAZ,QAAA,gBAClFnB,OAAA,CAACf,UAAU;UAAC0C,OAAO,EAAC,IAAI;UAAAR,QAAA,GAAC,YACb,EAACV,YAAY,EAAC,QAAM,EAACJ,UAAU,EAAC,GAC5C;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb1B,OAAA,CAACb,MAAM;UACLwC,OAAO,EAAC,WAAW;UACnBW,KAAK,EAAC,SAAS;UACfV,SAAS,eAAE5B,OAAA,CAACT,aAAa;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BG,OAAO,EAAEnB,oBAAqB;UAAAS,QAAA,EAC/B;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAER1B,OAAA,CAACF,oBAAoB;MACnBO,UAAU,EAAEA,UAAW;MACvBoC,SAAS,EAAE7B,aAAc;MACzB8B,OAAO,EAAE1B;IAAY;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxB,EAAA,CAxGID,YAAY;EAAA,QACYJ,OAAO,EAClBD,WAAW;AAAA;AAAA+C,EAAA,GAFxB1C,YAAY;AA0GlB,eAAeA,YAAY;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}