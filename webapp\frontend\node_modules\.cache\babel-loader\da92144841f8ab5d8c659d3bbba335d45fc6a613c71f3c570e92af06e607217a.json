{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\charts\\\\BoqChart.js\";\nimport React from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell, ComposedChart, Line, LineChart } from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f',\n  purple: '#9c27b0',\n  teal: '#00695c'\n};\nconst BoqChart = ({\n  data\n}) => {\n  var _data$distinta_materi, _data$distinta_materi2, _data$riepilogo, _data$riepilogo$total, _data$riepilogo2, _data$riepilogo2$tota, _data$riepilogo3, _data$riepilogo3$tota, _data$riepilogo4, _data$riepilogo4$perc;\n  if (!data) return null;\n\n  // Prepara dati per grafici dalla nuova struttura distinta_materiali\n  const caviData = ((_data$distinta_materi = data.distinta_materiali) === null || _data$distinta_materi === void 0 ? void 0 : _data$distinta_materi.map((cavo, index) => {\n    var _cavo$tipologia;\n    return {\n      ...cavo,\n      // Mappa i nuovi campi ai vecchi per compatibilità\n      metri_teorici: cavo.metri_teorici_totali,\n      metri_reali: cavo.metri_reali_posati,\n      tipologia_short: ((_cavo$tipologia = cavo.tipologia) === null || _cavo$tipologia === void 0 ? void 0 : _cavo$tipologia.length) > 8 ? cavo.tipologia.substring(0, 8) + '...' : cavo.tipologia,\n      color: Object.values(COLORS)[index % Object.values(COLORS).length],\n      deficit: Math.max(0, cavo.metri_da_posare - (cavo.metri_reali_posati || 0)),\n      surplus: Math.max(0, (cavo.metri_reali_posati || 0) - cavo.metri_da_posare)\n    };\n  })) || [];\n\n  // Prepara dati per grafici bobine (ora inclusi nella distinta_materiali)\n  const bobineData = ((_data$distinta_materi2 = data.distinta_materiali) === null || _data$distinta_materi2 === void 0 ? void 0 : _data$distinta_materi2.filter(item => item.num_bobine > 0).map((bobina, index) => {\n    var _bobina$tipologia;\n    return {\n      tipologia: bobina.tipologia,\n      sezione: bobina.sezione,\n      num_bobine: bobina.num_bobine,\n      metri_disponibili: bobina.metri_disponibili,\n      tipologia_short: ((_bobina$tipologia = bobina.tipologia) === null || _bobina$tipologia === void 0 ? void 0 : _bobina$tipologia.length) > 8 ? bobina.tipologia.substring(0, 8) + '...' : bobina.tipologia,\n      color: Object.values(COLORS)[index % Object.values(COLORS).length]\n    };\n  })) || [];\n\n  // Calcola totali per grafici a torta\n  const totaliCavi = caviData.reduce((acc, cavo) => {\n    acc.teorici += cavo.metri_teorici || 0;\n    acc.reali += cavo.metri_reali || 0;\n    acc.da_posare += cavo.metri_da_posare || 0;\n    return acc;\n  }, {\n    teorici: 0,\n    reali: 0,\n    da_posare: 0\n  });\n  const totaliData = [{\n    name: 'Metri Teorici',\n    value: totaliCavi.teorici,\n    color: COLORS.primary\n  }, {\n    name: 'Metri Reali',\n    value: totaliCavi.reali,\n    color: COLORS.success\n  }, {\n    name: 'Metri da Posare',\n    value: totaliCavi.da_posare,\n    color: COLORS.warning\n  }];\n\n  // Analisi deficit/surplus\n  const analisiData = caviData.map(cavo => ({\n    tipologia: cavo.tipologia_short,\n    tipologia_full: cavo.tipologia,\n    deficit: cavo.deficit,\n    surplus: cavo.surplus,\n    necessita_acquisto: cavo.deficit > 0\n  }));\n  const CustomTooltip = ({\n    active,\n    payload,\n    label\n  }) => {\n    if (active && payload && payload.length) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1,\n          border: '1px solid #ccc'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `${label}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), payload.map((entry, index) => /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          style: {\n            color: entry.color\n          },\n          children: `${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderCustomizedLabel = ({\n    cx,\n    cy,\n    midAngle,\n    innerRadius,\n    outerRadius,\n    percent\n  }) => {\n    if (percent < 0.05) return null;\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n    return /*#__PURE__*/_jsxDEV(\"text\", {\n      x: x,\n      y: y,\n      fill: \"white\",\n      textAnchor: x > cx ? 'start' : 'end',\n      dominantBaseline: \"central\",\n      fontSize: \"12\",\n      fontWeight: \"bold\",\n      children: `${(percent * 100).toFixed(0)}%`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mt: 3\n    },\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 0,\n            border: '1px solid #e0e0e0',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: '#f8f9fa',\n              p: 2,\n              borderBottom: '1px solid #e0e0e0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600,\n                color: '#2c3e50'\n              },\n              children: \"\\uD83D\\uDCCB Bill of Quantities - Distinta Materiali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: '#666',\n                mt: 0.5\n              },\n              children: \"Riepilogo completo dei materiali per tipologia di cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              overflow: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              style: {\n                width: '100%',\n                borderCollapse: 'collapse'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  style: {\n                    backgroundColor: '#f8f9fa'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'left',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Tipologia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 141,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"N\\xB0 Cavi\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Teorici\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Posati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Rimanenti\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"% Completamento\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0'\n                    },\n                    children: \"Stato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: caviData.map((cavo, index) => {\n                  const percentuale = cavo.metri_teorici > 0 ? (cavo.metri_reali || 0) / cavo.metri_teorici * 100 : 0;\n                  const isCompleto = percentuale >= 100;\n                  const isInCorso = percentuale > 0 && percentuale < 100;\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    style: {\n                      backgroundColor: index % 2 === 0 ? '#ffffff' : '#fafafa'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        fontSize: '13px',\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0',\n                        fontWeight: 500\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            width: '4px',\n                            height: '20px',\n                            backgroundColor: cavo.color,\n                            borderRadius: '2px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 224,\n                          columnNumber: 29\n                        }, this), cavo.tipologia]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 223,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'center',\n                        fontSize: '13px',\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: cavo.num_cavi\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_teorici || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 240,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: COLORS.success,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_reali || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: COLORS.warning,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_da_posare || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: isCompleto ? COLORS.success : isInCorso ? COLORS.warning : COLORS.secondary,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [percentuale.toFixed(1), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'center',\n                        fontSize: '13px',\n                        borderBottom: '1px solid #f0f0f0'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'inline-flex',\n                          alignItems: 'center',\n                          gap: 0.5,\n                          px: 1,\n                          py: 0.5,\n                          borderRadius: '12px',\n                          fontSize: '11px',\n                          fontWeight: 600,\n                          backgroundColor: isCompleto ? '#e8f5e8' : isInCorso ? '#fff3cd' : '#f8f9fa',\n                          color: isCompleto ? '#2e7d32' : isInCorso ? '#856404' : '#6c757d'\n                        },\n                        children: isCompleto ? '✅ Completato' : isInCorso ? '🔄 In Corso' : '⏳ Da Iniziare'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 281,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 25\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: '#f8f9fa',\n              p: 2,\n              borderTop: '1px solid #e0e0e0'\n            },\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      color: COLORS.primary,\n                      fontWeight: 600\n                    },\n                    children: [((_data$riepilogo = data.riepilogo) === null || _data$riepilogo === void 0 ? void 0 : (_data$riepilogo$total = _data$riepilogo.totale_metri_teorici) === null || _data$riepilogo$total === void 0 ? void 0 : _data$riepilogo$total.toFixed(1)) || totaliCavi.teorici.toFixed(1), \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Metri Teorici Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      color: COLORS.success,\n                      fontWeight: 600\n                    },\n                    children: [((_data$riepilogo2 = data.riepilogo) === null || _data$riepilogo2 === void 0 ? void 0 : (_data$riepilogo2$tota = _data$riepilogo2.totale_metri_posati) === null || _data$riepilogo2$tota === void 0 ? void 0 : _data$riepilogo2$tota.toFixed(1)) || totaliCavi.reali.toFixed(1), \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Metri Posati Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      color: COLORS.warning,\n                      fontWeight: 600\n                    },\n                    children: [((_data$riepilogo3 = data.riepilogo) === null || _data$riepilogo3 === void 0 ? void 0 : (_data$riepilogo3$tota = _data$riepilogo3.totale_metri_da_posare) === null || _data$riepilogo3$tota === void 0 ? void 0 : _data$riepilogo3$tota.toFixed(1)) || totaliCavi.da_posare.toFixed(1), \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Metri Rimanenti\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      color: COLORS.primary,\n                      fontWeight: 600\n                    },\n                    children: [((_data$riepilogo4 = data.riepilogo) === null || _data$riepilogo4 === void 0 ? void 0 : (_data$riepilogo4$perc = _data$riepilogo4.percentuale_completamento) === null || _data$riepilogo4$perc === void 0 ? void 0 : _data$riepilogo4$perc.toFixed(1)) || (totaliCavi.teorici > 0 ? (totaliCavi.reali / totaliCavi.teorici * 100).toFixed(1) : 0), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Completamento Totale\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_c = BoqChart;\nexport default BoqChart;\nvar _c;\n$RefreshReg$(_c, \"BoqChart\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "ComposedChart", "Line", "Line<PERSON>hart", "Box", "Typography", "Grid", "Paper", "Chip", "jsxDEV", "_jsxDEV", "COLORS", "primary", "secondary", "success", "warning", "info", "error", "purple", "teal", "<PERSON><PERSON><PERSON><PERSON>", "data", "_data$distinta_materi", "_data$distinta_materi2", "_data$riepilogo", "_data$riepilogo$total", "_data$riepilogo2", "_data$riepilogo2$tota", "_data$riepilogo3", "_data$riepilogo3$tota", "_data$riepilogo4", "_data$riepilogo4$perc", "caviData", "distinta_materiali", "map", "cavo", "index", "_cavo$tipologia", "metri_te<PERSON>ci", "metri_teorici_totali", "metri_reali", "metri_reali_posati", "tipologia_short", "tipologia", "length", "substring", "color", "Object", "values", "deficit", "Math", "max", "metri_da_posare", "surplus", "bobine<PERSON><PERSON>", "filter", "item", "num_bobine", "bobina", "_bobina$tipologia", "sezione", "metri_disponibili", "totaliCavi", "reduce", "acc", "<PERSON><PERSON><PERSON>", "reali", "da_posare", "totaliData", "name", "value", "analisiData", "tipologia_full", "necessita_acquisto", "CustomTooltip", "active", "payload", "label", "sx", "p", "border", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entry", "style", "toFixed", "renderCustomizedLabel", "cx", "cy", "midAngle", "innerRadius", "outerRadius", "percent", "RADIAN", "PI", "radius", "x", "cos", "y", "sin", "fill", "textAnchor", "dominantBaseline", "fontSize", "fontWeight", "mt", "container", "spacing", "xs", "borderRadius", "bgcolor", "borderBottom", "overflow", "width", "borderCollapse", "backgroundColor", "padding", "textAlign", "borderRight", "percentuale", "isCompleto", "isInCorso", "display", "alignItems", "gap", "height", "num_cavi", "px", "py", "borderTop", "sm", "riepilogo", "totale_metri_teorici", "totale_metri_posati", "totale_metri_da_posare", "percentuale_completamento", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/charts/BoqChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Bar,\n  XAxis,\n  <PERSON>A<PERSON><PERSON>,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer,\n  <PERSON><PERSON>hart,\n  Pie,\n  Cell,\n  ComposedChart,\n  Line,\n  LineChart\n} from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\n\nconst COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f',\n  purple: '#9c27b0',\n  teal: '#00695c'\n};\n\nconst BoqChart = ({ data }) => {\n  if (!data) return null;\n\n  // Prepara dati per grafici dalla nuova struttura distinta_materiali\n  const caviData = data.distinta_materiali?.map((cavo, index) => ({\n    ...cavo,\n    // Mappa i nuovi campi ai vecchi per compatibilità\n    metri_teorici: cavo.metri_teorici_totali,\n    metri_reali: cavo.metri_reali_posati,\n    tipologia_short: cavo.tipologia?.length > 8 ? cavo.tipologia.substring(0, 8) + '...' : cavo.tipologia,\n    color: Object.values(COLORS)[index % Object.values(COLORS).length],\n    deficit: Math.max(0, cavo.metri_da_posare - (cavo.metri_reali_posati || 0)),\n    surplus: Math.max(0, (cavo.metri_reali_posati || 0) - cavo.metri_da_posare)\n  })) || [];\n\n  // Prepara dati per grafici bobine (ora inclusi nella distinta_materiali)\n  const bobineData = data.distinta_materiali?.filter(item => item.num_bobine > 0).map((bobina, index) => ({\n    tipologia: bobina.tipologia,\n    sezione: bobina.sezione,\n    num_bobine: bobina.num_bobine,\n    metri_disponibili: bobina.metri_disponibili,\n    tipologia_short: bobina.tipologia?.length > 8 ? bobina.tipologia.substring(0, 8) + '...' : bobina.tipologia,\n    color: Object.values(COLORS)[index % Object.values(COLORS).length]\n  })) || [];\n\n  // Calcola totali per grafici a torta\n  const totaliCavi = caviData.reduce((acc, cavo) => {\n    acc.teorici += cavo.metri_teorici || 0;\n    acc.reali += cavo.metri_reali || 0;\n    acc.da_posare += cavo.metri_da_posare || 0;\n    return acc;\n  }, { teorici: 0, reali: 0, da_posare: 0 });\n\n  const totaliData = [\n    { name: 'Metri Teorici', value: totaliCavi.teorici, color: COLORS.primary },\n    { name: 'Metri Reali', value: totaliCavi.reali, color: COLORS.success },\n    { name: 'Metri da Posare', value: totaliCavi.da_posare, color: COLORS.warning }\n  ];\n\n  // Analisi deficit/surplus\n  const analisiData = caviData.map(cavo => ({\n    tipologia: cavo.tipologia_short,\n    tipologia_full: cavo.tipologia,\n    deficit: cavo.deficit,\n    surplus: cavo.surplus,\n    necessita_acquisto: cavo.deficit > 0\n  }));\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {\n    if (percent < 0.05) return null;\n\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n\n    return (\n      <text\n        x={x}\n        y={y}\n        fill=\"white\"\n        textAnchor={x > cx ? 'start' : 'end'}\n        dominantBaseline=\"central\"\n        fontSize=\"12\"\n        fontWeight=\"bold\"\n      >\n        {`${(percent * 100).toFixed(0)}%`}\n      </text>\n    );\n  };\n\n  return (\n    <Box sx={{ mt: 3 }}>\n      {/* Tabella Bill of Quantities Unificata */}\n      <Grid container spacing={2}>\n        <Grid item xs={12}>\n          <Paper sx={{ p: 0, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n            <Box sx={{\n              bgcolor: '#f8f9fa',\n              p: 2,\n              borderBottom: '1px solid #e0e0e0'\n            }}>\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                📋 Bill of Quantities - Distinta Materiali\n              </Typography>\n              <Typography variant=\"body2\" sx={{ color: '#666', mt: 0.5 }}>\n                Riepilogo completo dei materiali per tipologia di cavo\n              </Typography>\n            </Box>\n\n            <Box sx={{ overflow: 'auto' }}>\n              <table style={{ width: '100%', borderCollapse: 'collapse' }}>\n                <thead>\n                  <tr style={{ backgroundColor: '#f8f9fa' }}>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'left',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Tipologia</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>N° Cavi</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri Teorici</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri Posati</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri Rimanenti</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>% Completamento</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0'\n                    }}>Stato</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {caviData.map((cavo, index) => {\n                    const percentuale = cavo.metri_teorici > 0 ?\n                      ((cavo.metri_reali || 0) / cavo.metri_teorici * 100) : 0;\n                    const isCompleto = percentuale >= 100;\n                    const isInCorso = percentuale > 0 && percentuale < 100;\n\n                    return (\n                      <tr key={index} style={{\n                        backgroundColor: index % 2 === 0 ? '#ffffff' : '#fafafa'\n                      }}>\n                        <td style={{\n                          padding: '12px 16px',\n                          fontSize: '13px',\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0',\n                          fontWeight: 500\n                        }}>\n                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                            <Box sx={{\n                              width: '4px',\n                              height: '20px',\n                              backgroundColor: cavo.color,\n                              borderRadius: '2px'\n                            }} />\n                            {cavo.tipologia}\n                          </Box>\n                        </td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'center',\n                          fontSize: '13px',\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{cavo.num_cavi}</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_teorici || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: COLORS.success,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_reali || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: COLORS.warning,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_da_posare || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: isCompleto ? COLORS.success : isInCorso ? COLORS.warning : COLORS.secondary,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{percentuale.toFixed(1)}%</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'center',\n                          fontSize: '13px',\n                          borderBottom: '1px solid #f0f0f0'\n                        }}>\n                          <Box sx={{\n                            display: 'inline-flex',\n                            alignItems: 'center',\n                            gap: 0.5,\n                            px: 1,\n                            py: 0.5,\n                            borderRadius: '12px',\n                            fontSize: '11px',\n                            fontWeight: 600,\n                            backgroundColor: isCompleto ? '#e8f5e8' : isInCorso ? '#fff3cd' : '#f8f9fa',\n                            color: isCompleto ? '#2e7d32' : isInCorso ? '#856404' : '#6c757d'\n                          }}>\n                            {isCompleto ? '✅ Completato' : isInCorso ? '🔄 In Corso' : '⏳ Da Iniziare'}\n                          </Box>\n                        </td>\n                      </tr>\n                    );\n                  })}\n                </tbody>\n              </table>\n            </Box>\n\n            {/* Totali in fondo */}\n            <Box sx={{\n              bgcolor: '#f8f9fa',\n              p: 2,\n              borderTop: '1px solid #e0e0e0'\n            }}>\n              <Grid container spacing={3}>\n                <Grid item xs={12} sm={3}>\n                  <Box sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"h6\" sx={{ color: COLORS.primary, fontWeight: 600 }}>\n                      {data.riepilogo?.totale_metri_teorici?.toFixed(1) || totaliCavi.teorici.toFixed(1)}m\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                      Metri Teorici Totali\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={12} sm={3}>\n                  <Box sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"h6\" sx={{ color: COLORS.success, fontWeight: 600 }}>\n                      {data.riepilogo?.totale_metri_posati?.toFixed(1) || totaliCavi.reali.toFixed(1)}m\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                      Metri Posati Totali\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={12} sm={3}>\n                  <Box sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"h6\" sx={{ color: COLORS.warning, fontWeight: 600 }}>\n                      {data.riepilogo?.totale_metri_da_posare?.toFixed(1) || totaliCavi.da_posare.toFixed(1)}m\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                      Metri Rimanenti\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={12} sm={3}>\n                  <Box sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"h6\" sx={{ color: COLORS.primary, fontWeight: 600 }}>\n                      {data.riepilogo?.percentuale_completamento?.toFixed(1) || (totaliCavi.teorici > 0 ? ((totaliCavi.reali / totaliCavi.teorici) * 100).toFixed(1) : 0)}%\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                      Completamento Totale\n                    </Typography>\n                  </Box>\n                </Grid>\n              </Grid>\n            </Box>\n          </Paper>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default BoqChart;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,aAAa,EACbC,IAAI,EACJC,SAAS,QACJ,UAAU;AACjB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,MAAM,GAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE,SAAS;EACjBC,IAAI,EAAE;AACR,CAAC;AAED,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;EAC7B,IAAI,CAACV,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA,MAAMW,QAAQ,GAAG,EAAAV,qBAAA,GAAAD,IAAI,CAACY,kBAAkB,cAAAX,qBAAA,uBAAvBA,qBAAA,CAAyBY,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK;IAAA,IAAAC,eAAA;IAAA,OAAM;MAC9D,GAAGF,IAAI;MACP;MACAG,aAAa,EAAEH,IAAI,CAACI,oBAAoB;MACxCC,WAAW,EAAEL,IAAI,CAACM,kBAAkB;MACpCC,eAAe,EAAE,EAAAL,eAAA,GAAAF,IAAI,CAACQ,SAAS,cAAAN,eAAA,uBAAdA,eAAA,CAAgBO,MAAM,IAAG,CAAC,GAAGT,IAAI,CAACQ,SAAS,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGV,IAAI,CAACQ,SAAS;MACrGG,KAAK,EAAEC,MAAM,CAACC,MAAM,CAACrC,MAAM,CAAC,CAACyB,KAAK,GAAGW,MAAM,CAACC,MAAM,CAACrC,MAAM,CAAC,CAACiC,MAAM,CAAC;MAClEK,OAAO,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEhB,IAAI,CAACiB,eAAe,IAAIjB,IAAI,CAACM,kBAAkB,IAAI,CAAC,CAAC,CAAC;MAC3EY,OAAO,EAAEH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAChB,IAAI,CAACM,kBAAkB,IAAI,CAAC,IAAIN,IAAI,CAACiB,eAAe;IAC5E,CAAC;EAAA,CAAC,CAAC,KAAI,EAAE;;EAET;EACA,MAAME,UAAU,GAAG,EAAA/B,sBAAA,GAAAF,IAAI,CAACY,kBAAkB,cAAAV,sBAAA,uBAAvBA,sBAAA,CAAyBgC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC,CAACvB,GAAG,CAAC,CAACwB,MAAM,EAAEtB,KAAK;IAAA,IAAAuB,iBAAA;IAAA,OAAM;MACtGhB,SAAS,EAAEe,MAAM,CAACf,SAAS;MAC3BiB,OAAO,EAAEF,MAAM,CAACE,OAAO;MACvBH,UAAU,EAAEC,MAAM,CAACD,UAAU;MAC7BI,iBAAiB,EAAEH,MAAM,CAACG,iBAAiB;MAC3CnB,eAAe,EAAE,EAAAiB,iBAAA,GAAAD,MAAM,CAACf,SAAS,cAAAgB,iBAAA,uBAAhBA,iBAAA,CAAkBf,MAAM,IAAG,CAAC,GAAGc,MAAM,CAACf,SAAS,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGa,MAAM,CAACf,SAAS;MAC3GG,KAAK,EAAEC,MAAM,CAACC,MAAM,CAACrC,MAAM,CAAC,CAACyB,KAAK,GAAGW,MAAM,CAACC,MAAM,CAACrC,MAAM,CAAC,CAACiC,MAAM;IACnE,CAAC;EAAA,CAAC,CAAC,KAAI,EAAE;;EAET;EACA,MAAMkB,UAAU,GAAG9B,QAAQ,CAAC+B,MAAM,CAAC,CAACC,GAAG,EAAE7B,IAAI,KAAK;IAChD6B,GAAG,CAACC,OAAO,IAAI9B,IAAI,CAACG,aAAa,IAAI,CAAC;IACtC0B,GAAG,CAACE,KAAK,IAAI/B,IAAI,CAACK,WAAW,IAAI,CAAC;IAClCwB,GAAG,CAACG,SAAS,IAAIhC,IAAI,CAACiB,eAAe,IAAI,CAAC;IAC1C,OAAOY,GAAG;EACZ,CAAC,EAAE;IAAEC,OAAO,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAE,CAAC,CAAC;EAE1C,MAAMC,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAER,UAAU,CAACG,OAAO;IAAEnB,KAAK,EAAEnC,MAAM,CAACC;EAAQ,CAAC,EAC3E;IAAEyD,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAER,UAAU,CAACI,KAAK;IAAEpB,KAAK,EAAEnC,MAAM,CAACG;EAAQ,CAAC,EACvE;IAAEuD,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAER,UAAU,CAACK,SAAS;IAAErB,KAAK,EAAEnC,MAAM,CAACI;EAAQ,CAAC,CAChF;;EAED;EACA,MAAMwD,WAAW,GAAGvC,QAAQ,CAACE,GAAG,CAACC,IAAI,KAAK;IACxCQ,SAAS,EAAER,IAAI,CAACO,eAAe;IAC/B8B,cAAc,EAAErC,IAAI,CAACQ,SAAS;IAC9BM,OAAO,EAAEd,IAAI,CAACc,OAAO;IACrBI,OAAO,EAAElB,IAAI,CAACkB,OAAO;IACrBoB,kBAAkB,EAAEtC,IAAI,CAACc,OAAO,GAAG;EACrC,CAAC,CAAC,CAAC;EAEH,MAAMyB,aAAa,GAAGA,CAAC;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAM,CAAC,KAAK;IACpD,IAAIF,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAAChC,MAAM,EAAE;MACvC,oBACElC,OAAA,CAACH,KAAK;QAACuE,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAiB,CAAE;QAAAC,QAAA,gBAC5CvE,OAAA,CAACL,UAAU;UAAC6E,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,GAAGJ,KAAK;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,EACpDV,OAAO,CAAC1C,GAAG,CAAC,CAACqD,KAAK,EAAEnD,KAAK,kBACxB1B,OAAA,CAACL,UAAU;UAAa6E,OAAO,EAAC,OAAO;UAACM,KAAK,EAAE;YAAE1C,KAAK,EAAEyC,KAAK,CAACzC;UAAM,CAAE;UAAAmC,QAAA,EACnE,GAAGM,KAAK,CAAClB,IAAI,KAAK,OAAOkB,KAAK,CAACjB,KAAK,KAAK,QAAQ,GAAGiB,KAAK,CAACjB,KAAK,CAACmB,OAAO,CAAC,CAAC,CAAC,GAAGF,KAAK,CAACjB,KAAK;QAAE,GAD5ElC,KAAK;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMI,qBAAqB,GAAGA,CAAC;IAAEC,EAAE;IAAEC,EAAE;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,WAAW;IAAEC;EAAQ,CAAC,KAAK;IACzF,IAAIA,OAAO,GAAG,IAAI,EAAE,OAAO,IAAI;IAE/B,MAAMC,MAAM,GAAG/C,IAAI,CAACgD,EAAE,GAAG,GAAG;IAC5B,MAAMC,MAAM,GAAGL,WAAW,GAAG,CAACC,WAAW,GAAGD,WAAW,IAAI,GAAG;IAC9D,MAAMM,CAAC,GAAGT,EAAE,GAAGQ,MAAM,GAAGjD,IAAI,CAACmD,GAAG,CAAC,CAACR,QAAQ,GAAGI,MAAM,CAAC;IACpD,MAAMK,CAAC,GAAGV,EAAE,GAAGO,MAAM,GAAGjD,IAAI,CAACqD,GAAG,CAAC,CAACV,QAAQ,GAAGI,MAAM,CAAC;IAEpD,oBACEvF,OAAA;MACE0F,CAAC,EAAEA,CAAE;MACLE,CAAC,EAAEA,CAAE;MACLE,IAAI,EAAC,OAAO;MACZC,UAAU,EAAEL,CAAC,GAAGT,EAAE,GAAG,OAAO,GAAG,KAAM;MACrCe,gBAAgB,EAAC,SAAS;MAC1BC,QAAQ,EAAC,IAAI;MACbC,UAAU,EAAC,MAAM;MAAA3B,QAAA,EAEhB,GAAG,CAACe,OAAO,GAAG,GAAG,EAAEP,OAAO,CAAC,CAAC,CAAC;IAAG;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEX,CAAC;EAED,oBACE5E,OAAA,CAACN,GAAG;IAAC0E,EAAE,EAAE;MAAE+B,EAAE,EAAE;IAAE,CAAE;IAAA5B,QAAA,eAEjBvE,OAAA,CAACJ,IAAI;MAACwG,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA9B,QAAA,eACzBvE,OAAA,CAACJ,IAAI;QAACkD,IAAI;QAACwD,EAAE,EAAE,EAAG;QAAA/B,QAAA,eAChBvE,OAAA,CAACH,KAAK;UAACuE,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEC,MAAM,EAAE,mBAAmB;YAAEiC,YAAY,EAAE;UAAE,CAAE;UAAAhC,QAAA,gBAChEvE,OAAA,CAACN,GAAG;YAAC0E,EAAE,EAAE;cACPoC,OAAO,EAAE,SAAS;cAClBnC,CAAC,EAAE,CAAC;cACJoC,YAAY,EAAE;YAChB,CAAE;YAAAlC,QAAA,gBACAvE,OAAA,CAACL,UAAU;cAAC6E,OAAO,EAAC,IAAI;cAACJ,EAAE,EAAE;gBAAE8B,UAAU,EAAE,GAAG;gBAAE9D,KAAK,EAAE;cAAU,CAAE;cAAAmC,QAAA,EAAC;YAEpE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5E,OAAA,CAACL,UAAU;cAAC6E,OAAO,EAAC,OAAO;cAACJ,EAAE,EAAE;gBAAEhC,KAAK,EAAE,MAAM;gBAAE+D,EAAE,EAAE;cAAI,CAAE;cAAA5B,QAAA,EAAC;YAE5D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEN5E,OAAA,CAACN,GAAG;YAAC0E,EAAE,EAAE;cAAEsC,QAAQ,EAAE;YAAO,CAAE;YAAAnC,QAAA,eAC5BvE,OAAA;cAAO8E,KAAK,EAAE;gBAAE6B,KAAK,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAW,CAAE;cAAArC,QAAA,gBAC1DvE,OAAA;gBAAAuE,QAAA,eACEvE,OAAA;kBAAI8E,KAAK,EAAE;oBAAE+B,eAAe,EAAE;kBAAU,CAAE;kBAAAtC,QAAA,gBACxCvE,OAAA;oBAAI8E,KAAK,EAAE;sBACTgC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,MAAM;sBACjBd,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChBqE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAzC,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjB5E,OAAA;oBAAI8E,KAAK,EAAE;sBACTgC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,QAAQ;sBACnBd,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChBqE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAzC,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf5E,OAAA;oBAAI8E,KAAK,EAAE;sBACTgC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBd,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChBqE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAzC,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrB5E,OAAA;oBAAI8E,KAAK,EAAE;sBACTgC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBd,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChBqE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAzC,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpB5E,OAAA;oBAAI8E,KAAK,EAAE;sBACTgC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBd,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChBqE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAzC,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvB5E,OAAA;oBAAI8E,KAAK,EAAE;sBACTgC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBd,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChBqE,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAzC,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvB5E,OAAA;oBAAI8E,KAAK,EAAE;sBACTgC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,QAAQ;sBACnBd,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChBqE,YAAY,EAAE;oBAChB,CAAE;oBAAAlC,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR5E,OAAA;gBAAAuE,QAAA,EACGjD,QAAQ,CAACE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;kBAC7B,MAAMuF,WAAW,GAAGxF,IAAI,CAACG,aAAa,GAAG,CAAC,GACvC,CAACH,IAAI,CAACK,WAAW,IAAI,CAAC,IAAIL,IAAI,CAACG,aAAa,GAAG,GAAG,GAAI,CAAC;kBAC1D,MAAMsF,UAAU,GAAGD,WAAW,IAAI,GAAG;kBACrC,MAAME,SAAS,GAAGF,WAAW,GAAG,CAAC,IAAIA,WAAW,GAAG,GAAG;kBAEtD,oBACEjH,OAAA;oBAAgB8E,KAAK,EAAE;sBACrB+B,eAAe,EAAEnF,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG;oBACjD,CAAE;oBAAA6C,QAAA,gBACAvE,OAAA;sBAAI8E,KAAK,EAAE;wBACTgC,OAAO,EAAE,WAAW;wBACpBb,QAAQ,EAAE,MAAM;wBAChBQ,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE,mBAAmB;wBAChCd,UAAU,EAAE;sBACd,CAAE;sBAAA3B,QAAA,eACAvE,OAAA,CAACN,GAAG;wBAAC0E,EAAE,EAAE;0BAAEgD,OAAO,EAAE,MAAM;0BAAEC,UAAU,EAAE,QAAQ;0BAAEC,GAAG,EAAE;wBAAE,CAAE;wBAAA/C,QAAA,gBACzDvE,OAAA,CAACN,GAAG;0BAAC0E,EAAE,EAAE;4BACPuC,KAAK,EAAE,KAAK;4BACZY,MAAM,EAAE,MAAM;4BACdV,eAAe,EAAEpF,IAAI,CAACW,KAAK;4BAC3BmE,YAAY,EAAE;0BAChB;wBAAE;0BAAA9B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACJnD,IAAI,CAACQ,SAAS;sBAAA;wBAAAwC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACL5E,OAAA;sBAAI8E,KAAK,EAAE;wBACTgC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,QAAQ;wBACnBd,QAAQ,EAAE,MAAM;wBAChBQ,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAzC,QAAA,EAAE9C,IAAI,CAAC+F;oBAAQ;sBAAA/C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvB5E,OAAA;sBAAI8E,KAAK,EAAE;wBACTgC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBd,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACfO,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAzC,QAAA,GAAE,CAAC9C,IAAI,CAACG,aAAa,IAAI,CAAC,EAAEmD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/C5E,OAAA;sBAAI8E,KAAK,EAAE;wBACTgC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBd,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAEnC,MAAM,CAACG,OAAO;wBACrBqG,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAzC,QAAA,GAAE,CAAC9C,IAAI,CAACK,WAAW,IAAI,CAAC,EAAEiD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7C5E,OAAA;sBAAI8E,KAAK,EAAE;wBACTgC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBd,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAEnC,MAAM,CAACI,OAAO;wBACrBoG,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAzC,QAAA,GAAE,CAAC9C,IAAI,CAACiB,eAAe,IAAI,CAAC,EAAEqC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjD5E,OAAA;sBAAI8E,KAAK,EAAE;wBACTgC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBd,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAE8E,UAAU,GAAGjH,MAAM,CAACG,OAAO,GAAG+G,SAAS,GAAGlH,MAAM,CAACI,OAAO,GAAGJ,MAAM,CAACE,SAAS;wBAClFsG,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAzC,QAAA,GAAE0C,WAAW,CAAClC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjC5E,OAAA;sBAAI8E,KAAK,EAAE;wBACTgC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,QAAQ;wBACnBd,QAAQ,EAAE,MAAM;wBAChBQ,YAAY,EAAE;sBAChB,CAAE;sBAAAlC,QAAA,eACAvE,OAAA,CAACN,GAAG;wBAAC0E,EAAE,EAAE;0BACPgD,OAAO,EAAE,aAAa;0BACtBC,UAAU,EAAE,QAAQ;0BACpBC,GAAG,EAAE,GAAG;0BACRG,EAAE,EAAE,CAAC;0BACLC,EAAE,EAAE,GAAG;0BACPnB,YAAY,EAAE,MAAM;0BACpBN,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE,GAAG;0BACfW,eAAe,EAAEK,UAAU,GAAG,SAAS,GAAGC,SAAS,GAAG,SAAS,GAAG,SAAS;0BAC3E/E,KAAK,EAAE8E,UAAU,GAAG,SAAS,GAAGC,SAAS,GAAG,SAAS,GAAG;wBAC1D,CAAE;wBAAA5C,QAAA,EACC2C,UAAU,GAAG,cAAc,GAAGC,SAAS,GAAG,aAAa,GAAG;sBAAe;wBAAA1C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA,GAlFElD,KAAK;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmFV,CAAC;gBAET,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGN5E,OAAA,CAACN,GAAG;YAAC0E,EAAE,EAAE;cACPoC,OAAO,EAAE,SAAS;cAClBnC,CAAC,EAAE,CAAC;cACJsD,SAAS,EAAE;YACb,CAAE;YAAApD,QAAA,eACAvE,OAAA,CAACJ,IAAI;cAACwG,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA9B,QAAA,gBACzBvE,OAAA,CAACJ,IAAI;gBAACkD,IAAI;gBAACwD,EAAE,EAAE,EAAG;gBAACsB,EAAE,EAAE,CAAE;gBAAArD,QAAA,eACvBvE,OAAA,CAACN,GAAG;kBAAC0E,EAAE,EAAE;oBAAE2C,SAAS,EAAE;kBAAS,CAAE;kBAAAxC,QAAA,gBAC/BvE,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,IAAI;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAEnC,MAAM,CAACC,OAAO;sBAAEgG,UAAU,EAAE;oBAAI,CAAE;oBAAA3B,QAAA,GACrE,EAAAzD,eAAA,GAAAH,IAAI,CAACkH,SAAS,cAAA/G,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBgH,oBAAoB,cAAA/G,qBAAA,uBAApCA,qBAAA,CAAsCgE,OAAO,CAAC,CAAC,CAAC,KAAI3B,UAAU,CAACG,OAAO,CAACwB,OAAO,CAAC,CAAC,CAAC,EAAC,GACrF;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb5E,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,SAAS;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE;oBAAO,CAAE;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACP5E,OAAA,CAACJ,IAAI;gBAACkD,IAAI;gBAACwD,EAAE,EAAE,EAAG;gBAACsB,EAAE,EAAE,CAAE;gBAAArD,QAAA,eACvBvE,OAAA,CAACN,GAAG;kBAAC0E,EAAE,EAAE;oBAAE2C,SAAS,EAAE;kBAAS,CAAE;kBAAAxC,QAAA,gBAC/BvE,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,IAAI;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAEnC,MAAM,CAACG,OAAO;sBAAE8F,UAAU,EAAE;oBAAI,CAAE;oBAAA3B,QAAA,GACrE,EAAAvD,gBAAA,GAAAL,IAAI,CAACkH,SAAS,cAAA7G,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB+G,mBAAmB,cAAA9G,qBAAA,uBAAnCA,qBAAA,CAAqC8D,OAAO,CAAC,CAAC,CAAC,KAAI3B,UAAU,CAACI,KAAK,CAACuB,OAAO,CAAC,CAAC,CAAC,EAAC,GAClF;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb5E,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,SAAS;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE;oBAAO,CAAE;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACP5E,OAAA,CAACJ,IAAI;gBAACkD,IAAI;gBAACwD,EAAE,EAAE,EAAG;gBAACsB,EAAE,EAAE,CAAE;gBAAArD,QAAA,eACvBvE,OAAA,CAACN,GAAG;kBAAC0E,EAAE,EAAE;oBAAE2C,SAAS,EAAE;kBAAS,CAAE;kBAAAxC,QAAA,gBAC/BvE,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,IAAI;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAEnC,MAAM,CAACI,OAAO;sBAAE6F,UAAU,EAAE;oBAAI,CAAE;oBAAA3B,QAAA,GACrE,EAAArD,gBAAA,GAAAP,IAAI,CAACkH,SAAS,cAAA3G,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB8G,sBAAsB,cAAA7G,qBAAA,uBAAtCA,qBAAA,CAAwC4D,OAAO,CAAC,CAAC,CAAC,KAAI3B,UAAU,CAACK,SAAS,CAACsB,OAAO,CAAC,CAAC,CAAC,EAAC,GACzF;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb5E,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,SAAS;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE;oBAAO,CAAE;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACP5E,OAAA,CAACJ,IAAI;gBAACkD,IAAI;gBAACwD,EAAE,EAAE,EAAG;gBAACsB,EAAE,EAAE,CAAE;gBAAArD,QAAA,eACvBvE,OAAA,CAACN,GAAG;kBAAC0E,EAAE,EAAE;oBAAE2C,SAAS,EAAE;kBAAS,CAAE;kBAAAxC,QAAA,gBAC/BvE,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,IAAI;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAEnC,MAAM,CAACC,OAAO;sBAAEgG,UAAU,EAAE;oBAAI,CAAE;oBAAA3B,QAAA,GACrE,EAAAnD,gBAAA,GAAAT,IAAI,CAACkH,SAAS,cAAAzG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB6G,yBAAyB,cAAA5G,qBAAA,uBAAzCA,qBAAA,CAA2C0D,OAAO,CAAC,CAAC,CAAC,MAAK3B,UAAU,CAACG,OAAO,GAAG,CAAC,GAAG,CAAEH,UAAU,CAACI,KAAK,GAAGJ,UAAU,CAACG,OAAO,GAAI,GAAG,EAAEwB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAC,GACtJ;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb5E,OAAA,CAACL,UAAU;oBAAC6E,OAAO,EAAC,SAAS;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE;oBAAO,CAAE;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACsD,EAAA,GAtUIxH,QAAQ;AAwUd,eAAeA,QAAQ;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}