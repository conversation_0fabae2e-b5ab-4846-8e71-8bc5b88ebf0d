{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isSunday\n * @category Weekday Helpers\n * @summary Is the given date Sunday?\n *\n * @description\n * Is the given date Sunday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is Sunday\n *\n * @example\n * // Is 21 September 2014 Sunday?\n * const result = isSunday(new Date(2014, 8, 21))\n * //=> true\n */\nexport function isSunday(date) {\n  return toDate(date).getDay() === 0;\n}\n\n// Fallback for modularized imports:\nexport default isSunday;", "map": {"version": 3, "names": ["toDate", "is<PERSON><PERSON><PERSON>", "date", "getDay"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/isSunday.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isSunday\n * @category Weekday Helpers\n * @summary Is the given date Sunday?\n *\n * @description\n * Is the given date Sunday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is Sunday\n *\n * @example\n * // Is 21 September 2014 Sunday?\n * const result = isSunday(new Date(2014, 8, 21))\n * //=> true\n */\nexport function isSunday(date) {\n  return toDate(date).getDay() === 0;\n}\n\n// Fallback for modularized imports:\nexport default isSunday;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAE;EAC7B,OAAOF,MAAM,CAACE,IAAI,CAAC,CAACC,MAAM,CAAC,CAAC,KAAK,CAAC;AACpC;;AAEA;AACA,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}