{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { startOfQuarter } from \"./startOfQuarter.js\";\n\n/**\n * The {@link isSameQuarter} function options.\n */\n\n/**\n * @name isSameQuarter\n * @category Quarter Helpers\n * @summary Are the given dates in the same quarter (and year)?\n *\n * @description\n * Are the given dates in the same quarter (and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same quarter (and year)\n *\n * @example\n * // Are 1 January 2014 and 8 March 2014 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2014, 2, 8))\n * //=> true\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */\nexport function isSameQuarter(laterDate, earlierDate, options) {\n  const [dateLeft_, dateRight_] = normalizeDates(options?.in, laterDate, earlierDate);\n  return +startOfQuarter(dateLeft_) === +startOfQuarter(dateRight_);\n}\n\n// Fallback for modularized imports:\nexport default isSameQuarter;", "map": {"version": 3, "names": ["normalizeDates", "startOfQuarter", "isSameQuarter", "laterDate", "earlierDate", "options", "dateLeft_", "dateRight_", "in"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/isSameQuarter.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { startOfQuarter } from \"./startOfQuarter.js\";\n\n/**\n * The {@link isSameQuarter} function options.\n */\n\n/**\n * @name isSameQuarter\n * @category Quarter Helpers\n * @summary Are the given dates in the same quarter (and year)?\n *\n * @description\n * Are the given dates in the same quarter (and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same quarter (and year)\n *\n * @example\n * // Are 1 January 2014 and 8 March 2014 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2014, 2, 8))\n * //=> true\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */\nexport function isSameQuarter(laterDate, earlierDate, options) {\n  const [dateLeft_, dateRight_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return +startOfQuarter(dateLeft_) === +startOfQuarter(dateRight_);\n}\n\n// Fallback for modularized imports:\nexport default isSameQuarter;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SAASC,cAAc,QAAQ,qBAAqB;;AAEpD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAC7D,MAAM,CAACC,SAAS,EAAEC,UAAU,CAAC,GAAGP,cAAc,CAC5CK,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EACD,OAAO,CAACH,cAAc,CAACK,SAAS,CAAC,KAAK,CAACL,cAAc,CAACM,UAAU,CAAC;AACnE;;AAEA;AACA,eAAeL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}