{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\TestBobineComponent.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, CircularProgress, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Alert } from '@mui/material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport caviService from '../../services/caviService';\n\n/**\n * Componente di test per visualizzare tutte le bobine e i cavi disponibili\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestBobineComponent = ({\n  cantiereId\n}) => {\n  _s();\n  const [bobine, setBobine] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Carica bobine e cavi all'avvio\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true);\n\n        // Carica bobine\n        console.log('Caricamento bobine per cantiere:', cantiereId);\n        const bobineData = await parcoCaviService.getBobine(cantiereId);\n        console.log('Bobine caricate:', bobineData);\n        setBobine(bobineData);\n\n        // Carica cavi\n        console.log('Caricamento cavi per cantiere:', cantiereId);\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log('Cavi caricati:', caviData);\n        setCavi(caviData);\n      } catch (error) {\n        console.error('Errore nel caricamento dei dati:', error);\n        setError('Errore nel caricamento dei dati: ' + (error.message || 'Errore sconosciuto'));\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadData();\n  }, [cantiereId]);\n\n  // Mostra loading\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Mostra errore\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        my: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      gutterBottom: true,\n      children: \"Test Visualizzazione Bobine e Cavi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      sx: {\n        mt: 3\n      },\n      children: [\"Bobine Disponibili (\", bobine.length, \")\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        my: 2\n      },\n      children: \"Nessuna bobina disponibile per questo cantiere.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        size: \"small\",\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              bgcolor: '#e3f2fd'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"ID Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Tipologia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Sezione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Metri Residui\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: bobina.id_bobina\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 30\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.tipologia || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.sezione || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [bobina.metri_residui || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.stato_bobina || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 19\n            }, this)]\n          }, bobina.id_bobina, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      sx: {\n        mt: 3\n      },\n      children: [\"Cavi Disponibili (\", cavi.length, \")\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        my: 2\n      },\n      children: \"Nessun cavo disponibile per questo cantiere.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        size: \"small\",\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              bgcolor: '#e3f2fd'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"ID Cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Tipologia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Sezione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Metri Teorici\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 30\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: cavo.tipologia || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: cavo.sezione || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [cavo.metri_teorici || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: cavo.stato_installazione || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      sx: {\n        mt: 3\n      },\n      children: \"Debug - Verifica Compatibilit\\xE0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        size: \"small\",\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              bgcolor: '#e3f2fd'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Tipologia Match\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Sezione Match\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Compatibile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: cavi.filter(cavo => cavo.stato_installazione === 'Da installare').map(cavo => bobine.filter(bobina => bobina.stato_bobina !== 'Terminata').map(bobina => {\n            const tipologiaMatch = String(cavo.tipologia || '').trim().toLowerCase() === String(bobina.tipologia || '').trim().toLowerCase();\n            const sezioneMatch = String(cavo.sezione || '').trim() === String(bobina.sezione || '').trim();\n            const isCompatible = tipologiaMatch && sezioneMatch;\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                bgcolor: isCompatible ? '#f1f8e9' : 'inherit'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.id_cavo, \" (\", cavo.tipologia, \"/\", cavo.sezione, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [bobina.id_bobina, \" (\", bobina.tipologia, \"/\", bobina.sezione, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: tipologiaMatch ? '✅' : '❌'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: sezioneMatch ? '✅' : '❌'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: isCompatible ? '✅ Compatibile' : '❌ Non compatibile'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 21\n              }, this)]\n            }, `${cavo.id_cavo}-${bobina.id_bobina}`, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 19\n            }, this);\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(TestBobineComponent, \"zY/6m8UsojomwAe1HaDlVp4R0/Q=\");\n_c = TestBobineComponent;\nexport default TestBobineComponent;\nvar _c;\n$RefreshReg$(_c, \"TestBobineComponent\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "CircularProgress", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "<PERSON><PERSON>", "parcoCaviService", "caviService", "jsxDEV", "_jsxDEV", "TestBobineComponent", "cantiereId", "_s", "bobine", "set<PERSON>ob<PERSON>", "cavi", "<PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "loadData", "console", "log", "bobine<PERSON><PERSON>", "getBobine", "caviData", "get<PERSON><PERSON>", "message", "sx", "display", "justifyContent", "my", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "p", "variant", "gutterBottom", "mt", "length", "component", "mb", "size", "bgcolor", "map", "bobina", "id_bobina", "tipologia", "sezione", "metri_residui", "stato_bobina", "cavo", "id_cavo", "metri_te<PERSON>ci", "stato_installazione", "filter", "tipologiaMatch", "String", "trim", "toLowerCase", "sezioneMatch", "isCompatible", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/TestBobineComponent.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  CircularProgress,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Alert\n} from '@mui/material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport caviService from '../../services/caviService';\n\n/**\n * Componente di test per visualizzare tutte le bobine e i cavi disponibili\n */\nconst TestBobineComponent = ({ cantiereId }) => {\n  const [bobine, setBobine] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Carica bobine e cavi all'avvio\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        setLoading(true);\n        \n        // Carica bobine\n        console.log('Caricamento bobine per cantiere:', cantiereId);\n        const bobineData = await parcoCaviService.getBobine(cantiereId);\n        console.log('Bobine caricate:', bobineData);\n        setBobine(bobineData);\n        \n        // Carica cavi\n        console.log('Caricamento cavi per cantiere:', cantiereId);\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log('Cavi caricati:', caviData);\n        setCavi(caviData);\n      } catch (error) {\n        console.error('Errore nel caricamento dei dati:', error);\n        setError('Errore nel caricamento dei dati: ' + (error.message || 'Errore sconosciuto'));\n      } finally {\n        setLoading(false);\n      }\n    };\n    \n    loadData();\n  }, [cantiereId]);\n\n  // Mostra loading\n  if (loading) {\n    return (\n      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  // Mostra errore\n  if (error) {\n    return (\n      <Alert severity=\"error\" sx={{ my: 2 }}>\n        {error}\n      </Alert>\n    );\n  }\n\n  return (\n    <Paper sx={{ p: 3 }}>\n      <Typography variant=\"h5\" gutterBottom>\n        Test Visualizzazione Bobine e Cavi\n      </Typography>\n      \n      {/* Tabella Bobine */}\n      <Typography variant=\"h6\" gutterBottom sx={{ mt: 3 }}>\n        Bobine Disponibili ({bobine.length})\n      </Typography>\n      \n      {bobine.length === 0 ? (\n        <Alert severity=\"info\" sx={{ my: 2 }}>\n          Nessuna bobina disponibile per questo cantiere.\n        </Alert>\n      ) : (\n        <TableContainer component={Paper} sx={{ mb: 3 }}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow sx={{ bgcolor: '#e3f2fd' }}>\n                <TableCell>ID Bobina</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Sezione</TableCell>\n                <TableCell>Metri Residui</TableCell>\n                <TableCell>Stato</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {bobine.map((bobina) => (\n                <TableRow key={bobina.id_bobina}>\n                  <TableCell><strong>{bobina.id_bobina}</strong></TableCell>\n                  <TableCell>{bobina.tipologia || 'N/A'}</TableCell>\n                  <TableCell>{bobina.sezione || 'N/A'}</TableCell>\n                  <TableCell>{bobina.metri_residui || 'N/A'} m</TableCell>\n                  <TableCell>{bobina.stato_bobina || 'N/A'}</TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      )}\n      \n      {/* Tabella Cavi */}\n      <Typography variant=\"h6\" gutterBottom sx={{ mt: 3 }}>\n        Cavi Disponibili ({cavi.length})\n      </Typography>\n      \n      {cavi.length === 0 ? (\n        <Alert severity=\"info\" sx={{ my: 2 }}>\n          Nessun cavo disponibile per questo cantiere.\n        </Alert>\n      ) : (\n        <TableContainer component={Paper} sx={{ mb: 3 }}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow sx={{ bgcolor: '#e3f2fd' }}>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Sezione</TableCell>\n                <TableCell>Metri Teorici</TableCell>\n                <TableCell>Stato</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {cavi.map((cavo) => (\n                <TableRow key={cavo.id_cavo}>\n                  <TableCell><strong>{cavo.id_cavo}</strong></TableCell>\n                  <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                  <TableCell>{cavo.sezione || 'N/A'}</TableCell>\n                  <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                  <TableCell>{cavo.stato_installazione || 'N/A'}</TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      )}\n      \n      {/* Sezione di debug */}\n      <Typography variant=\"h6\" gutterBottom sx={{ mt: 3 }}>\n        Debug - Verifica Compatibilità\n      </Typography>\n      \n      <TableContainer component={Paper} sx={{ mb: 3 }}>\n        <Table size=\"small\">\n          <TableHead>\n            <TableRow sx={{ bgcolor: '#e3f2fd' }}>\n              <TableCell>Cavo</TableCell>\n              <TableCell>Bobina</TableCell>\n              <TableCell>Tipologia Match</TableCell>\n              <TableCell>Sezione Match</TableCell>\n              <TableCell>Compatibile</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {cavi.filter(cavo => cavo.stato_installazione === 'Da installare').map((cavo) => (\n              bobine.filter(bobina => bobina.stato_bobina !== 'Terminata').map((bobina) => {\n                const tipologiaMatch = String(cavo.tipologia || '').trim().toLowerCase() === String(bobina.tipologia || '').trim().toLowerCase();\n                const sezioneMatch = String(cavo.sezione || '').trim() === String(bobina.sezione || '').trim();\n                const isCompatible = tipologiaMatch && sezioneMatch;\n                \n                return (\n                  <TableRow key={`${cavo.id_cavo}-${bobina.id_bobina}`} sx={{ bgcolor: isCompatible ? '#f1f8e9' : 'inherit' }}>\n                    <TableCell>{cavo.id_cavo} ({cavo.tipologia}/{cavo.sezione})</TableCell>\n                    <TableCell>{bobina.id_bobina} ({bobina.tipologia}/{bobina.sezione})</TableCell>\n                    <TableCell>{tipologiaMatch ? '✅' : '❌'}</TableCell>\n                    <TableCell>{sezioneMatch ? '✅' : '❌'}</TableCell>\n                    <TableCell>{isCompatible ? '✅ Compatibile' : '❌ Non compatibile'}</TableCell>\n                  </TableRow>\n                );\n              })\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n    </Paper>\n  );\n};\n\nexport default TestBobineComponent;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,WAAW,MAAM,4BAA4B;;AAEpD;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA;AAGA,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACsB,IAAI,EAAEC,OAAO,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd,MAAM2B,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACAI,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEZ,UAAU,CAAC;QAC3D,MAAMa,UAAU,GAAG,MAAMlB,gBAAgB,CAACmB,SAAS,CAACd,UAAU,CAAC;QAC/DW,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,UAAU,CAAC;QAC3CV,SAAS,CAACU,UAAU,CAAC;;QAErB;QACAF,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEZ,UAAU,CAAC;QACzD,MAAMe,QAAQ,GAAG,MAAMnB,WAAW,CAACoB,OAAO,CAAChB,UAAU,CAAC;QACtDW,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEG,QAAQ,CAAC;QACvCV,OAAO,CAACU,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdG,OAAO,CAACH,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxDC,QAAQ,CAAC,mCAAmC,IAAID,KAAK,CAACS,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACzF,CAAC,SAAS;QACRV,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACV,UAAU,CAAC,CAAC;;EAEhB;EACA,IAAIM,OAAO,EAAE;IACX,oBACER,OAAA,CAACd,GAAG;MAACkC,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC5DxB,OAAA,CAACX,gBAAgB;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;;EAEA;EACA,IAAIlB,KAAK,EAAE;IACT,oBACEV,OAAA,CAACJ,KAAK;MAACiC,QAAQ,EAAC,OAAO;MAACT,EAAE,EAAE;QAAEG,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,EACnCd;IAAK;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEZ;EAEA,oBACE5B,OAAA,CAACZ,KAAK;IAACgC,EAAE,EAAE;MAAEU,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAClBxB,OAAA,CAACb,UAAU;MAAC4C,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAR,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGb5B,OAAA,CAACb,UAAU;MAAC4C,OAAO,EAAC,IAAI;MAACC,YAAY;MAACZ,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,GAAC,sBAC/B,EAACpB,MAAM,CAAC8B,MAAM,EAAC,GACrC;IAAA;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZxB,MAAM,CAAC8B,MAAM,KAAK,CAAC,gBAClBlC,OAAA,CAACJ,KAAK;MAACiC,QAAQ,EAAC,MAAM;MAACT,EAAE,EAAE;QAAEG,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,gBAER5B,OAAA,CAACP,cAAc;MAAC0C,SAAS,EAAE/C,KAAM;MAACgC,EAAE,EAAE;QAAEgB,EAAE,EAAE;MAAE,CAAE;MAAAZ,QAAA,eAC9CxB,OAAA,CAACV,KAAK;QAAC+C,IAAI,EAAC,OAAO;QAAAb,QAAA,gBACjBxB,OAAA,CAACN,SAAS;UAAA8B,QAAA,eACRxB,OAAA,CAACL,QAAQ;YAACyB,EAAE,EAAE;cAAEkB,OAAO,EAAE;YAAU,CAAE;YAAAd,QAAA,gBACnCxB,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChC5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChC5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9B5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpC5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZ5B,OAAA,CAACT,SAAS;UAAAiC,QAAA,EACPpB,MAAM,CAACmC,GAAG,CAAEC,MAAM,iBACjBxC,OAAA,CAACL,QAAQ;YAAA6B,QAAA,gBACPxB,OAAA,CAACR,SAAS;cAAAgC,QAAA,eAACxB,OAAA;gBAAAwB,QAAA,EAASgB,MAAM,CAACC;cAAS;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC1D5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAEgB,MAAM,CAACE,SAAS,IAAI;YAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClD5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAEgB,MAAM,CAACG,OAAO,IAAI;YAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChD5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,GAAEgB,MAAM,CAACI,aAAa,IAAI,KAAK,EAAC,IAAE;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACxD5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAEgB,MAAM,CAACK,YAAY,IAAI;YAAK;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA,GALxCY,MAAM,CAACC,SAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMrB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACjB,eAGD5B,OAAA,CAACb,UAAU;MAAC4C,OAAO,EAAC,IAAI;MAACC,YAAY;MAACZ,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,GAAC,oBACjC,EAAClB,IAAI,CAAC4B,MAAM,EAAC,GACjC;IAAA;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZtB,IAAI,CAAC4B,MAAM,KAAK,CAAC,gBAChBlC,OAAA,CAACJ,KAAK;MAACiC,QAAQ,EAAC,MAAM;MAACT,EAAE,EAAE;QAAEG,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,gBAER5B,OAAA,CAACP,cAAc;MAAC0C,SAAS,EAAE/C,KAAM;MAACgC,EAAE,EAAE;QAAEgB,EAAE,EAAE;MAAE,CAAE;MAAAZ,QAAA,eAC9CxB,OAAA,CAACV,KAAK;QAAC+C,IAAI,EAAC,OAAO;QAAAb,QAAA,gBACjBxB,OAAA,CAACN,SAAS;UAAA8B,QAAA,eACRxB,OAAA,CAACL,QAAQ;YAACyB,EAAE,EAAE;cAAEkB,OAAO,EAAE;YAAU,CAAE;YAAAd,QAAA,gBACnCxB,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9B5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChC5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9B5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpC5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZ5B,OAAA,CAACT,SAAS;UAAAiC,QAAA,EACPlB,IAAI,CAACiC,GAAG,CAAEO,IAAI,iBACb9C,OAAA,CAACL,QAAQ;YAAA6B,QAAA,gBACPxB,OAAA,CAACR,SAAS;cAAAgC,QAAA,eAACxB,OAAA;gBAAAwB,QAAA,EAASsB,IAAI,CAACC;cAAO;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtD5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAEsB,IAAI,CAACJ,SAAS,IAAI;YAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChD5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAEsB,IAAI,CAACH,OAAO,IAAI;YAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9C5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,GAAEsB,IAAI,CAACE,aAAa,IAAI,KAAK,EAAC,IAAE;YAAA;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtD5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAEsB,IAAI,CAACG,mBAAmB,IAAI;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA,GAL7CkB,IAAI,CAACC,OAAO;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMjB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACjB,eAGD5B,OAAA,CAACb,UAAU;MAAC4C,OAAO,EAAC,IAAI;MAACC,YAAY;MAACZ,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EAAC;IAErD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb5B,OAAA,CAACP,cAAc;MAAC0C,SAAS,EAAE/C,KAAM;MAACgC,EAAE,EAAE;QAAEgB,EAAE,EAAE;MAAE,CAAE;MAAAZ,QAAA,eAC9CxB,OAAA,CAACV,KAAK;QAAC+C,IAAI,EAAC,OAAO;QAAAb,QAAA,gBACjBxB,OAAA,CAACN,SAAS;UAAA8B,QAAA,eACRxB,OAAA,CAACL,QAAQ;YAACyB,EAAE,EAAE;cAAEkB,OAAO,EAAE;YAAU,CAAE;YAAAd,QAAA,gBACnCxB,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3B5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7B5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtC5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpC5B,OAAA,CAACR,SAAS;cAAAgC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZ5B,OAAA,CAACT,SAAS;UAAAiC,QAAA,EACPlB,IAAI,CAAC4C,MAAM,CAACJ,IAAI,IAAIA,IAAI,CAACG,mBAAmB,KAAK,eAAe,CAAC,CAACV,GAAG,CAAEO,IAAI,IAC1E1C,MAAM,CAAC8C,MAAM,CAACV,MAAM,IAAIA,MAAM,CAACK,YAAY,KAAK,WAAW,CAAC,CAACN,GAAG,CAAEC,MAAM,IAAK;YAC3E,MAAMW,cAAc,GAAGC,MAAM,CAACN,IAAI,CAACJ,SAAS,IAAI,EAAE,CAAC,CAACW,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAKF,MAAM,CAACZ,MAAM,CAACE,SAAS,IAAI,EAAE,CAAC,CAACW,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YAChI,MAAMC,YAAY,GAAGH,MAAM,CAACN,IAAI,CAACH,OAAO,IAAI,EAAE,CAAC,CAACU,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACZ,MAAM,CAACG,OAAO,IAAI,EAAE,CAAC,CAACU,IAAI,CAAC,CAAC;YAC9F,MAAMG,YAAY,GAAGL,cAAc,IAAII,YAAY;YAEnD,oBACEvD,OAAA,CAACL,QAAQ;cAA6CyB,EAAE,EAAE;gBAAEkB,OAAO,EAAEkB,YAAY,GAAG,SAAS,GAAG;cAAU,CAAE;cAAAhC,QAAA,gBAC1GxB,OAAA,CAACR,SAAS;gBAAAgC,QAAA,GAAEsB,IAAI,CAACC,OAAO,EAAC,IAAE,EAACD,IAAI,CAACJ,SAAS,EAAC,GAAC,EAACI,IAAI,CAACH,OAAO,EAAC,GAAC;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACvE5B,OAAA,CAACR,SAAS;gBAAAgC,QAAA,GAAEgB,MAAM,CAACC,SAAS,EAAC,IAAE,EAACD,MAAM,CAACE,SAAS,EAAC,GAAC,EAACF,MAAM,CAACG,OAAO,EAAC,GAAC;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/E5B,OAAA,CAACR,SAAS;gBAAAgC,QAAA,EAAE2B,cAAc,GAAG,GAAG,GAAG;cAAG;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnD5B,OAAA,CAACR,SAAS;gBAAAgC,QAAA,EAAE+B,YAAY,GAAG,GAAG,GAAG;cAAG;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjD5B,OAAA,CAACR,SAAS;gBAAAgC,QAAA,EAAEgC,YAAY,GAAG,eAAe,GAAG;cAAmB;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA,GALhE,GAAGkB,IAAI,CAACC,OAAO,IAAIP,MAAM,CAACC,SAAS,EAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAM1C,CAAC;UAEf,CAAC,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEZ,CAAC;AAACzB,EAAA,CAzKIF,mBAAmB;AAAAwD,EAAA,GAAnBxD,mBAAmB;AA2KzB,eAAeA,mBAAmB;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}