{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'dernier à' p\",\n  yesterday: \"'hier à' p\",\n  today: \"'aujourd’hui à' p\",\n  tomorrow: \"'demain à' p'\",\n  nextWeek: \"eeee 'prochain à' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/fr/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"eeee 'dernier à' p\",\n  yesterday: \"'hier à' p\",\n  today: \"'aujourd’hui à' p\",\n  tomorrow: \"'demain à' p'\",\n  nextWeek: \"eeee 'prochain à' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,oBAAoB;EAC9BC,SAAS,EAAE,YAAY;EACvBC,KAAK,EAAE,mBAAmB;EAC1BC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE,qBAAqB;EAC/BC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}