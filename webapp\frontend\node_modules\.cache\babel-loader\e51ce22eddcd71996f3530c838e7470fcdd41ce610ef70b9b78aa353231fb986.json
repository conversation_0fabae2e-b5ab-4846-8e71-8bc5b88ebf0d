{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\charts\\\\ProgressChart.js\";\nimport React from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line } from 'recharts';\nimport { Box, Typography, Grid, Paper } from '@mui/material';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst COLORS = {\n  primary: '#2c3e50',\n  secondary: '#34495e',\n  success: '#27ae60',\n  warning: '#f39c12',\n  info: '#3498db',\n  error: '#e74c3c',\n  light: '#ecf0f1',\n  dark: '#2c3e50',\n  accent: '#9b59b6'\n};\nconst ProgressChart = ({\n  data\n}) => {\n  var _data$posa_recente;\n  if (!data) return null;\n\n  // Dati per il grafico a torta dell'avanzamento\n  const progressData = [{\n    name: 'Metri Posati',\n    value: data.metri_posati,\n    color: COLORS.success\n  }, {\n    name: 'Metri Rimanenti',\n    value: data.metri_da_posare,\n    color: COLORS.warning\n  }];\n\n  // Dati per il grafico a torta dei cavi\n  const caviData = [{\n    name: 'Cavi Posati',\n    value: data.cavi_posati,\n    color: COLORS.success\n  }, {\n    name: 'Cavi Rimanenti',\n    value: data.cavi_rimanenti,\n    color: COLORS.warning\n  }];\n\n  // Dati per il grafico a barre delle metriche principali\n  const metricsData = [{\n    name: 'Metri',\n    Totali: data.metri_totali,\n    Posati: data.metri_posati,\n    Rimanenti: data.metri_da_posare\n  }, {\n    name: 'Cavi',\n    Totali: data.totale_cavi,\n    Posati: data.cavi_posati,\n    Rimanenti: data.cavi_rimanenti\n  }];\n\n  // Dati per il grafico temporale della posa recente\n  const posaTrendData = ((_data$posa_recente = data.posa_recente) === null || _data$posa_recente === void 0 ? void 0 : _data$posa_recente.map(posa => ({\n    data: posa.data,\n    metri: posa.metri\n  }))) || [];\n  const CustomTooltip = ({\n    active,\n    payload,\n    label\n  }) => {\n    if (active && payload && payload.length) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1,\n          border: '1px solid #ccc'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `${label}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), payload.map((entry, index) => /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          style: {\n            color: entry.color\n          },\n          children: `${entry.name}: ${entry.value}`\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderCustomizedLabel = ({\n    cx,\n    cy,\n    midAngle,\n    innerRadius,\n    outerRadius,\n    percent\n  }) => {\n    if (percent < 0.05) return null; // Non mostrare etichette per fette troppo piccole\n\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n    return /*#__PURE__*/_jsxDEV(\"text\", {\n      x: x,\n      y: y,\n      fill: \"white\",\n      textAnchor: x > cx ? 'start' : 'end',\n      dominantBaseline: \"central\",\n      fontSize: \"12\",\n      fontWeight: \"bold\",\n      children: `${(percent * 100).toFixed(0)}%`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      border: '1px solid #e0e0e0',\n      borderRadius: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"subtitle1\",\n      sx: {\n        fontWeight: 500,\n        mb: 2,\n        color: '#2c3e50'\n      },\n      children: \"Analisi Avanzamento\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            border: '1px solid #e0e0e0',\n            borderRadius: 1,\n            overflow: 'hidden'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: '#f8f9fa',\n              p: 1.5,\n              borderBottom: '1px solid #e0e0e0'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 600,\n                color: '#2c3e50'\n              },\n              children: \"Metriche di Avanzamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              style: {\n                width: '100%',\n                borderCollapse: 'collapse'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  style: {\n                    backgroundColor: '#f8f9fa'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'left',\n                      fontSize: '12px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0'\n                    },\n                    children: \"Metrica\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '12px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0'\n                    },\n                    children: \"Valore\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '12px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0'\n                    },\n                    children: \"%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'center',\n                      fontSize: '12px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0'\n                    },\n                    children: \"Progresso\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      fontSize: '13px',\n                      borderBottom: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      borderBottom: '1px solid #f0f0f0'\n                    },\n                    children: [data.metri_totali, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      borderBottom: '1px solid #f0f0f0'\n                    },\n                    children: \"100%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'center',\n                      borderBottom: '1px solid #f0f0f0'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '60px',\n                        height: '6px',\n                        bgcolor: '#e0e0e0',\n                        borderRadius: '3px',\n                        position: 'relative',\n                        margin: '0 auto'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '100%',\n                          height: '100%',\n                          bgcolor: COLORS.primary,\n                          borderRadius: '3px'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 209,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      fontSize: '13px',\n                      borderBottom: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Posati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: COLORS.success,\n                      borderBottom: '1px solid #f0f0f0'\n                    },\n                    children: [data.metri_posati, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      color: COLORS.success,\n                      borderBottom: '1px solid #f0f0f0'\n                    },\n                    children: [data.percentuale_avanzamento, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'center',\n                      borderBottom: '1px solid #f0f0f0'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '60px',\n                        height: '6px',\n                        bgcolor: '#e0e0e0',\n                        borderRadius: '3px',\n                        position: 'relative',\n                        margin: '0 auto'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: `${data.percentuale_avanzamento}%`,\n                          height: '100%',\n                          bgcolor: COLORS.success,\n                          borderRadius: '3px'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 260,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      fontSize: '13px',\n                      borderBottom: '1px solid #f0f0f0'\n                    },\n                    children: \"Cavi Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      borderBottom: '1px solid #f0f0f0'\n                    },\n                    children: data.totale_cavi\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      borderBottom: '1px solid #f0f0f0'\n                    },\n                    children: \"100%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'center',\n                      borderBottom: '1px solid #f0f0f0'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '60px',\n                        height: '6px',\n                        bgcolor: '#e0e0e0',\n                        borderRadius: '3px',\n                        position: 'relative',\n                        margin: '0 auto'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '100%',\n                          height: '100%',\n                          bgcolor: COLORS.primary,\n                          borderRadius: '3px'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 301,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      fontSize: '13px'\n                    },\n                    children: \"Cavi Posati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: COLORS.success\n                    },\n                    children: data.cavi_posati\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      color: COLORS.success\n                    },\n                    children: [data.percentuale_cavi, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: '8px 12px',\n                      textAlign: 'center'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '60px',\n                        height: '6px',\n                        bgcolor: '#e0e0e0',\n                        borderRadius: '3px',\n                        position: 'relative',\n                        margin: '0 auto'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: `${data.percentuale_cavi}%`,\n                          height: '100%',\n                          bgcolor: COLORS.success,\n                          borderRadius: '3px'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 340,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            border: '1px solid #e0e0e0',\n            borderRadius: 1,\n            overflow: 'hidden',\n            height: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: '#f8f9fa',\n              p: 1.5,\n              borderBottom: '1px solid #e0e0e0'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 600,\n                color: '#2c3e50'\n              },\n              children: \"Distribuzione Avanzamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2,\n              height: 'calc(100% - 50px)',\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: 180,\n              children: /*#__PURE__*/_jsxDEV(PieChart, {\n                children: [/*#__PURE__*/_jsxDEV(Pie, {\n                  data: progressData,\n                  cx: \"50%\",\n                  cy: \"50%\",\n                  innerRadius: 35,\n                  outerRadius: 65,\n                  paddingAngle: 2,\n                  dataKey: \"value\",\n                  stroke: \"none\",\n                  children: progressData.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                    fill: entry.color\n                  }, `cell-${index}`, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  content: ({\n                    active,\n                    payload\n                  }) => {\n                    if (active && payload && payload.length) {\n                      return /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          bgcolor: 'white',\n                          p: 1,\n                          border: '1px solid #e0e0e0',\n                          borderRadius: 1,\n                          fontSize: '12px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            fontWeight: 600\n                          },\n                          children: [payload[0].name, \": \", payload[0].value, \"m\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 400,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 393,\n                        columnNumber: 27\n                      }, this);\n                    }\n                    return null;\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this), data.media_giornaliera && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            border: '1px solid #e0e0e0',\n            borderRadius: 1,\n            overflow: 'hidden'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: '#f8f9fa',\n              p: 1.5,\n              borderBottom: '1px solid #e0e0e0'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 600,\n                color: '#2c3e50'\n              },\n              children: \"Performance e Previsioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                md: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      fontWeight: 600,\n                      color: COLORS.primary\n                    },\n                    children: [data.media_giornaliera, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Media Giornaliera\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), data.giorni_stimati && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 3,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      sx: {\n                        fontWeight: 600,\n                        color: COLORS.warning\n                      },\n                      children: data.giorni_stimati\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 448,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        color: '#666'\n                      },\n                      children: \"Giorni Stimati\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 600,\n                        color: COLORS.info\n                      },\n                      children: data.data_completamento\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        color: '#666'\n                      },\n                      children: \"Data Completamento Prevista\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n};\n_c = ProgressChart;\nexport default ProgressChart;\nvar _c;\n$RefreshReg$(_c, \"ProgressChart\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "Line<PERSON>hart", "Line", "Box", "Typography", "Grid", "Paper", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "COLORS", "primary", "secondary", "success", "warning", "info", "error", "light", "dark", "accent", "ProgressChart", "data", "_data$posa_recente", "progressData", "name", "value", "metri_posati", "color", "metri_da_posare", "caviData", "cavi_posati", "cavi_rimanenti", "metricsData", "Totali", "metri_totali", "Posati", "<PERSON><PERSON><PERSON><PERSON>", "totale_cavi", "posaTrendData", "posa_recente", "map", "posa", "metri", "CustomTooltip", "active", "payload", "label", "length", "sx", "p", "border", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entry", "index", "style", "renderCustomizedLabel", "cx", "cy", "midAngle", "innerRadius", "outerRadius", "percent", "RADIAN", "Math", "PI", "radius", "x", "cos", "y", "sin", "fill", "textAnchor", "dominantBaseline", "fontSize", "fontWeight", "toFixed", "borderRadius", "mb", "container", "spacing", "item", "xs", "md", "overflow", "bgcolor", "borderBottom", "width", "borderCollapse", "backgroundColor", "padding", "textAlign", "height", "position", "margin", "percentuale_avanzamento", "percentuale_cavi", "display", "alignItems", "paddingAngle", "dataKey", "stroke", "content", "media_giornaliera", "giorni_stimati", "data_completamento", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/charts/ProgressChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Pie,\n  Cell,\n  BarChart,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  <PERSON>lt<PERSON>,\n  Legend,\n  ResponsiveContainer,\n  LineChart,\n  Line\n} from 'recharts';\nimport { Box, Typography, Grid, Paper } from '@mui/material';\n\nconst COLORS = {\n  primary: '#2c3e50',\n  secondary: '#34495e',\n  success: '#27ae60',\n  warning: '#f39c12',\n  info: '#3498db',\n  error: '#e74c3c',\n  light: '#ecf0f1',\n  dark: '#2c3e50',\n  accent: '#9b59b6'\n};\n\nconst ProgressChart = ({ data }) => {\n  if (!data) return null;\n\n  // Dati per il grafico a torta dell'avanzamento\n  const progressData = [\n    {\n      name: 'Metri Posati',\n      value: data.metri_posati,\n      color: COLORS.success\n    },\n    {\n      name: 'Metri Rimanenti',\n      value: data.metri_da_posare,\n      color: COLORS.warning\n    }\n  ];\n\n  // Dati per il grafico a torta dei cavi\n  const caviData = [\n    {\n      name: '<PERSON><PERSON>',\n      value: data.cavi_posati,\n      color: COLORS.success\n    },\n    {\n      name: '<PERSON><PERSON>',\n      value: data.cavi_rimanenti,\n      color: COLORS.warning\n    }\n  ];\n\n  // Dati per il grafico a barre delle metriche principali\n  const metricsData = [\n    {\n      name: 'Metri',\n      Totali: data.metri_totali,\n      Posati: data.metri_posati,\n      Rimanenti: data.metri_da_posare\n    },\n    {\n      name: 'Cavi',\n      Totali: data.totale_cavi,\n      Posati: data.cavi_posati,\n      Rimanenti: data.cavi_rimanenti\n    }\n  ];\n\n  // Dati per il grafico temporale della posa recente\n  const posaTrendData = data.posa_recente?.map(posa => ({\n    data: posa.data,\n    metri: posa.metri\n  })) || [];\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${entry.value}`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {\n    if (percent < 0.05) return null; // Non mostrare etichette per fette troppo piccole\n\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n\n    return (\n      <text\n        x={x}\n        y={y}\n        fill=\"white\"\n        textAnchor={x > cx ? 'start' : 'end'}\n        dominantBaseline=\"central\"\n        fontSize=\"12\"\n        fontWeight=\"bold\"\n      >\n        {`${(percent * 100).toFixed(0)}%`}\n      </text>\n    );\n  };\n\n  return (\n    <Paper sx={{ p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n      <Typography variant=\"subtitle1\" sx={{ fontWeight: 500, mb: 2, color: '#2c3e50' }}>\n        Analisi Avanzamento\n      </Typography>\n\n      <Grid container spacing={2}>\n        {/* Tabella Metriche Principali */}\n        <Grid item xs={12} md={8}>\n          <Box sx={{\n            border: '1px solid #e0e0e0',\n            borderRadius: 1,\n            overflow: 'hidden'\n          }}>\n            <Box sx={{\n              bgcolor: '#f8f9fa',\n              p: 1.5,\n              borderBottom: '1px solid #e0e0e0'\n            }}>\n              <Typography variant=\"subtitle2\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                Metriche di Avanzamento\n              </Typography>\n            </Box>\n            <Box sx={{ p: 0 }}>\n              <table style={{ width: '100%', borderCollapse: 'collapse' }}>\n                <thead>\n                  <tr style={{ backgroundColor: '#f8f9fa' }}>\n                    <th style={{\n                      padding: '8px 12px',\n                      textAlign: 'left',\n                      fontSize: '12px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0'\n                    }}>Metrica</th>\n                    <th style={{\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '12px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0'\n                    }}>Valore</th>\n                    <th style={{\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '12px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0'\n                    }}>%</th>\n                    <th style={{\n                      padding: '8px 12px',\n                      textAlign: 'center',\n                      fontSize: '12px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0'\n                    }}>Progresso</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr>\n                    <td style={{\n                      padding: '8px 12px',\n                      fontSize: '13px',\n                      borderBottom: '1px solid #f0f0f0'\n                    }}>Metri Totali</td>\n                    <td style={{\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      borderBottom: '1px solid #f0f0f0'\n                    }}>{data.metri_totali}m</td>\n                    <td style={{\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      borderBottom: '1px solid #f0f0f0'\n                    }}>100%</td>\n                    <td style={{\n                      padding: '8px 12px',\n                      textAlign: 'center',\n                      borderBottom: '1px solid #f0f0f0'\n                    }}>\n                      <Box sx={{\n                        width: '60px',\n                        height: '6px',\n                        bgcolor: '#e0e0e0',\n                        borderRadius: '3px',\n                        position: 'relative',\n                        margin: '0 auto'\n                      }}>\n                        <Box sx={{\n                          width: '100%',\n                          height: '100%',\n                          bgcolor: COLORS.primary,\n                          borderRadius: '3px'\n                        }} />\n                      </Box>\n                    </td>\n                  </tr>\n                  <tr>\n                    <td style={{\n                      padding: '8px 12px',\n                      fontSize: '13px',\n                      borderBottom: '1px solid #f0f0f0'\n                    }}>Metri Posati</td>\n                    <td style={{\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: COLORS.success,\n                      borderBottom: '1px solid #f0f0f0'\n                    }}>{data.metri_posati}m</td>\n                    <td style={{\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      color: COLORS.success,\n                      borderBottom: '1px solid #f0f0f0'\n                    }}>{data.percentuale_avanzamento}%</td>\n                    <td style={{\n                      padding: '8px 12px',\n                      textAlign: 'center',\n                      borderBottom: '1px solid #f0f0f0'\n                    }}>\n                      <Box sx={{\n                        width: '60px',\n                        height: '6px',\n                        bgcolor: '#e0e0e0',\n                        borderRadius: '3px',\n                        position: 'relative',\n                        margin: '0 auto'\n                      }}>\n                        <Box sx={{\n                          width: `${data.percentuale_avanzamento}%`,\n                          height: '100%',\n                          bgcolor: COLORS.success,\n                          borderRadius: '3px'\n                        }} />\n                      </Box>\n                    </td>\n                  </tr>\n                  <tr>\n                    <td style={{\n                      padding: '8px 12px',\n                      fontSize: '13px',\n                      borderBottom: '1px solid #f0f0f0'\n                    }}>Cavi Totali</td>\n                    <td style={{\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      borderBottom: '1px solid #f0f0f0'\n                    }}>{data.totale_cavi}</td>\n                    <td style={{\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      borderBottom: '1px solid #f0f0f0'\n                    }}>100%</td>\n                    <td style={{\n                      padding: '8px 12px',\n                      textAlign: 'center',\n                      borderBottom: '1px solid #f0f0f0'\n                    }}>\n                      <Box sx={{\n                        width: '60px',\n                        height: '6px',\n                        bgcolor: '#e0e0e0',\n                        borderRadius: '3px',\n                        position: 'relative',\n                        margin: '0 auto'\n                      }}>\n                        <Box sx={{\n                          width: '100%',\n                          height: '100%',\n                          bgcolor: COLORS.primary,\n                          borderRadius: '3px'\n                        }} />\n                      </Box>\n                    </td>\n                  </tr>\n                  <tr>\n                    <td style={{\n                      padding: '8px 12px',\n                      fontSize: '13px'\n                    }}>Cavi Posati</td>\n                    <td style={{\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: COLORS.success\n                    }}>{data.cavi_posati}</td>\n                    <td style={{\n                      padding: '8px 12px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      color: COLORS.success\n                    }}>{data.percentuale_cavi}%</td>\n                    <td style={{\n                      padding: '8px 12px',\n                      textAlign: 'center'\n                    }}>\n                      <Box sx={{\n                        width: '60px',\n                        height: '6px',\n                        bgcolor: '#e0e0e0',\n                        borderRadius: '3px',\n                        position: 'relative',\n                        margin: '0 auto'\n                      }}>\n                        <Box sx={{\n                          width: `${data.percentuale_cavi}%`,\n                          height: '100%',\n                          bgcolor: COLORS.success,\n                          borderRadius: '3px'\n                        }} />\n                      </Box>\n                    </td>\n                  </tr>\n                </tbody>\n              </table>\n            </Box>\n          </Box>\n        </Grid>\n\n        {/* Grafico Compatto */}\n        <Grid item xs={12} md={4}>\n          <Box sx={{\n            border: '1px solid #e0e0e0',\n            borderRadius: 1,\n            overflow: 'hidden',\n            height: '100%'\n          }}>\n            <Box sx={{\n              bgcolor: '#f8f9fa',\n              p: 1.5,\n              borderBottom: '1px solid #e0e0e0'\n            }}>\n              <Typography variant=\"subtitle2\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                Distribuzione Avanzamento\n              </Typography>\n            </Box>\n            <Box sx={{ p: 2, height: 'calc(100% - 50px)', display: 'flex', alignItems: 'center' }}>\n              <ResponsiveContainer width=\"100%\" height={180}>\n                <PieChart>\n                  <Pie\n                    data={progressData}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    innerRadius={35}\n                    outerRadius={65}\n                    paddingAngle={2}\n                    dataKey=\"value\"\n                    stroke=\"none\"\n                  >\n                    {progressData.map((entry, index) => (\n                      <Cell key={`cell-${index}`} fill={entry.color} />\n                    ))}\n                  </Pie>\n                  <Tooltip\n                    content={({ active, payload }) => {\n                      if (active && payload && payload.length) {\n                        return (\n                          <Box sx={{\n                            bgcolor: 'white',\n                            p: 1,\n                            border: '1px solid #e0e0e0',\n                            borderRadius: 1,\n                            fontSize: '12px'\n                          }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 600 }}>\n                              {payload[0].name}: {payload[0].value}m\n                            </Typography>\n                          </Box>\n                        );\n                      }\n                      return null;\n                    }}\n                  />\n                </PieChart>\n              </ResponsiveContainer>\n            </Box>\n          </Box>\n        </Grid>\n\n        {/* Performance e Trend (solo se ci sono dati) */}\n        {data.media_giornaliera && (\n          <Grid item xs={12}>\n            <Box sx={{\n              border: '1px solid #e0e0e0',\n              borderRadius: 1,\n              overflow: 'hidden'\n            }}>\n              <Box sx={{\n                bgcolor: '#f8f9fa',\n                p: 1.5,\n                borderBottom: '1px solid #e0e0e0'\n              }}>\n                <Typography variant=\"subtitle2\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Performance e Previsioni\n                </Typography>\n              </Box>\n              <Box sx={{ p: 2 }}>\n                <Grid container spacing={3}>\n                  <Grid item xs={12} md={3}>\n                    <Box sx={{ textAlign: 'center' }}>\n                      <Typography variant=\"h6\" sx={{ fontWeight: 600, color: COLORS.primary }}>\n                        {data.media_giornaliera}m\n                      </Typography>\n                      <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                        Media Giornaliera\n                      </Typography>\n                    </Box>\n                  </Grid>\n                  {data.giorni_stimati && (\n                    <>\n                      <Grid item xs={12} md={3}>\n                        <Box sx={{ textAlign: 'center' }}>\n                          <Typography variant=\"h6\" sx={{ fontWeight: 600, color: COLORS.warning }}>\n                            {data.giorni_stimati}\n                          </Typography>\n                          <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                            Giorni Stimati\n                          </Typography>\n                        </Box>\n                      </Grid>\n                      <Grid item xs={12} md={6}>\n                        <Box sx={{ textAlign: 'center' }}>\n                          <Typography variant=\"body2\" sx={{ fontWeight: 600, color: COLORS.info }}>\n                            {data.data_completamento}\n                          </Typography>\n                          <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                            Data Completamento Prevista\n                          </Typography>\n                        </Box>\n                      </Grid>\n                    </>\n                  )}\n                </Grid>\n              </Box>\n            </Box>\n          </Grid>\n        )}\n      </Grid>\n    </Paper>\n  );\n};\n\nexport default ProgressChart;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,SAAS,EACTC,IAAI,QACC,UAAU;AACjB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7D,MAAMC,MAAM,GAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAA,IAAAC,kBAAA;EAClC,IAAI,CAACD,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA,MAAME,YAAY,GAAG,CACnB;IACEC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAEJ,IAAI,CAACK,YAAY;IACxBC,KAAK,EAAEjB,MAAM,CAACG;EAChB,CAAC,EACD;IACEW,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAEJ,IAAI,CAACO,eAAe;IAC3BD,KAAK,EAAEjB,MAAM,CAACI;EAChB,CAAC,CACF;;EAED;EACA,MAAMe,QAAQ,GAAG,CACf;IACEL,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAEJ,IAAI,CAACS,WAAW;IACvBH,KAAK,EAAEjB,MAAM,CAACG;EAChB,CAAC,EACD;IACEW,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAEJ,IAAI,CAACU,cAAc;IAC1BJ,KAAK,EAAEjB,MAAM,CAACI;EAChB,CAAC,CACF;;EAED;EACA,MAAMkB,WAAW,GAAG,CAClB;IACER,IAAI,EAAE,OAAO;IACbS,MAAM,EAAEZ,IAAI,CAACa,YAAY;IACzBC,MAAM,EAAEd,IAAI,CAACK,YAAY;IACzBU,SAAS,EAAEf,IAAI,CAACO;EAClB,CAAC,EACD;IACEJ,IAAI,EAAE,MAAM;IACZS,MAAM,EAAEZ,IAAI,CAACgB,WAAW;IACxBF,MAAM,EAAEd,IAAI,CAACS,WAAW;IACxBM,SAAS,EAAEf,IAAI,CAACU;EAClB,CAAC,CACF;;EAED;EACA,MAAMO,aAAa,GAAG,EAAAhB,kBAAA,GAAAD,IAAI,CAACkB,YAAY,cAAAjB,kBAAA,uBAAjBA,kBAAA,CAAmBkB,GAAG,CAACC,IAAI,KAAK;IACpDpB,IAAI,EAAEoB,IAAI,CAACpB,IAAI;IACfqB,KAAK,EAAED,IAAI,CAACC;EACd,CAAC,CAAC,CAAC,KAAI,EAAE;EAET,MAAMC,aAAa,GAAGA,CAAC;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAM,CAAC,KAAK;IACpD,IAAIF,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACE,MAAM,EAAE;MACvC,oBACExC,OAAA,CAACF,KAAK;QAAC2C,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAiB,CAAE;QAAAC,QAAA,gBAC5C5C,OAAA,CAACJ,UAAU;UAACiD,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,GAAGL,KAAK;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,EACpDX,OAAO,CAACL,GAAG,CAAC,CAACiB,KAAK,EAAEC,KAAK,kBACxBnD,OAAA,CAACJ,UAAU;UAAaiD,OAAO,EAAC,OAAO;UAACO,KAAK,EAAE;YAAEhC,KAAK,EAAE8B,KAAK,CAAC9B;UAAM,CAAE;UAAAwB,QAAA,EACnE,GAAGM,KAAK,CAACjC,IAAI,KAAKiC,KAAK,CAAChC,KAAK;QAAE,GADjBiC,KAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMI,qBAAqB,GAAGA,CAAC;IAAEC,EAAE;IAAEC,EAAE;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,WAAW;IAAEC;EAAQ,CAAC,KAAK;IACzF,IAAIA,OAAO,GAAG,IAAI,EAAE,OAAO,IAAI,CAAC,CAAC;;IAEjC,MAAMC,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IAC5B,MAAMC,MAAM,GAAGN,WAAW,GAAG,CAACC,WAAW,GAAGD,WAAW,IAAI,GAAG;IAC9D,MAAMO,CAAC,GAAGV,EAAE,GAAGS,MAAM,GAAGF,IAAI,CAACI,GAAG,CAAC,CAACT,QAAQ,GAAGI,MAAM,CAAC;IACpD,MAAMM,CAAC,GAAGX,EAAE,GAAGQ,MAAM,GAAGF,IAAI,CAACM,GAAG,CAAC,CAACX,QAAQ,GAAGI,MAAM,CAAC;IAEpD,oBACE5D,OAAA;MACEgE,CAAC,EAAEA,CAAE;MACLE,CAAC,EAAEA,CAAE;MACLE,IAAI,EAAC,OAAO;MACZC,UAAU,EAAEL,CAAC,GAAGV,EAAE,GAAG,OAAO,GAAG,KAAM;MACrCgB,gBAAgB,EAAC,SAAS;MAC1BC,QAAQ,EAAC,IAAI;MACbC,UAAU,EAAC,MAAM;MAAA5B,QAAA,EAEhB,GAAG,CAACe,OAAO,GAAG,GAAG,EAAEc,OAAO,CAAC,CAAC,CAAC;IAAG;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEX,CAAC;EAED,oBACEjD,OAAA,CAACF,KAAK;IAAC2C,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,MAAM,EAAE,mBAAmB;MAAE+B,YAAY,EAAE;IAAE,CAAE;IAAA9B,QAAA,gBAChE5C,OAAA,CAACJ,UAAU;MAACiD,OAAO,EAAC,WAAW;MAACJ,EAAE,EAAE;QAAE+B,UAAU,EAAE,GAAG;QAAEG,EAAE,EAAE,CAAC;QAAEvD,KAAK,EAAE;MAAU,CAAE;MAAAwB,QAAA,EAAC;IAElF;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbjD,OAAA,CAACH,IAAI;MAAC+E,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAjC,QAAA,gBAEzB5C,OAAA,CAACH,IAAI;QAACiF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eACvB5C,OAAA,CAACL,GAAG;UAAC8C,EAAE,EAAE;YACPE,MAAM,EAAE,mBAAmB;YAC3B+B,YAAY,EAAE,CAAC;YACfO,QAAQ,EAAE;UACZ,CAAE;UAAArC,QAAA,gBACA5C,OAAA,CAACL,GAAG;YAAC8C,EAAE,EAAE;cACPyC,OAAO,EAAE,SAAS;cAClBxC,CAAC,EAAE,GAAG;cACNyC,YAAY,EAAE;YAChB,CAAE;YAAAvC,QAAA,eACA5C,OAAA,CAACJ,UAAU;cAACiD,OAAO,EAAC,WAAW;cAACJ,EAAE,EAAE;gBAAE+B,UAAU,EAAE,GAAG;gBAAEpD,KAAK,EAAE;cAAU,CAAE;cAAAwB,QAAA,EAAC;YAE3E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjD,OAAA,CAACL,GAAG;YAAC8C,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,eAChB5C,OAAA;cAAOoD,KAAK,EAAE;gBAAEgC,KAAK,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAW,CAAE;cAAAzC,QAAA,gBAC1D5C,OAAA;gBAAA4C,QAAA,eACE5C,OAAA;kBAAIoD,KAAK,EAAE;oBAAEkC,eAAe,EAAE;kBAAU,CAAE;kBAAA1C,QAAA,gBACxC5C,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,MAAM;sBACjBjB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACfpD,KAAK,EAAE,SAAS;sBAChB+D,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,OAAO;sBAClBjB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACfpD,KAAK,EAAE,SAAS;sBAChB+D,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACdjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,OAAO;sBAClBjB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACfpD,KAAK,EAAE,SAAS;sBAChB+D,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,EAAC;kBAAC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACTjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,QAAQ;sBACnBjB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACfpD,KAAK,EAAE,SAAS;sBAChB+D,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRjD,OAAA;gBAAA4C,QAAA,gBACE5C,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBhB,QAAQ,EAAE,MAAM;sBAChBY,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpBjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,OAAO;sBAClBjB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACfW,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,GAAE9B,IAAI,CAACa,YAAY,EAAC,GAAC;kBAAA;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5BjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,OAAO;sBAClBjB,QAAQ,EAAE,MAAM;sBAChBY,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,EAAC;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACZjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,QAAQ;sBACnBL,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,eACA5C,OAAA,CAACL,GAAG;sBAAC8C,EAAE,EAAE;wBACP2C,KAAK,EAAE,MAAM;wBACbK,MAAM,EAAE,KAAK;wBACbP,OAAO,EAAE,SAAS;wBAClBR,YAAY,EAAE,KAAK;wBACnBgB,QAAQ,EAAE,UAAU;wBACpBC,MAAM,EAAE;sBACV,CAAE;sBAAA/C,QAAA,eACA5C,OAAA,CAACL,GAAG;wBAAC8C,EAAE,EAAE;0BACP2C,KAAK,EAAE,MAAM;0BACbK,MAAM,EAAE,MAAM;0BACdP,OAAO,EAAE/E,MAAM,CAACC,OAAO;0BACvBsE,YAAY,EAAE;wBAChB;sBAAE;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACLjD,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBhB,QAAQ,EAAE,MAAM;sBAChBY,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpBjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,OAAO;sBAClBjB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACfpD,KAAK,EAAEjB,MAAM,CAACG,OAAO;sBACrB6E,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,GAAE9B,IAAI,CAACK,YAAY,EAAC,GAAC;kBAAA;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5BjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,OAAO;sBAClBjB,QAAQ,EAAE,MAAM;sBAChBnD,KAAK,EAAEjB,MAAM,CAACG,OAAO;sBACrB6E,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,GAAE9B,IAAI,CAAC8E,uBAAuB,EAAC,GAAC;kBAAA;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvCjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,QAAQ;sBACnBL,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,eACA5C,OAAA,CAACL,GAAG;sBAAC8C,EAAE,EAAE;wBACP2C,KAAK,EAAE,MAAM;wBACbK,MAAM,EAAE,KAAK;wBACbP,OAAO,EAAE,SAAS;wBAClBR,YAAY,EAAE,KAAK;wBACnBgB,QAAQ,EAAE,UAAU;wBACpBC,MAAM,EAAE;sBACV,CAAE;sBAAA/C,QAAA,eACA5C,OAAA,CAACL,GAAG;wBAAC8C,EAAE,EAAE;0BACP2C,KAAK,EAAE,GAAGtE,IAAI,CAAC8E,uBAAuB,GAAG;0BACzCH,MAAM,EAAE,MAAM;0BACdP,OAAO,EAAE/E,MAAM,CAACG,OAAO;0BACvBoE,YAAY,EAAE;wBAChB;sBAAE;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACLjD,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBhB,QAAQ,EAAE,MAAM;sBAChBY,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnBjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,OAAO;sBAClBjB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACfW,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,EAAE9B,IAAI,CAACgB;kBAAW;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1BjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,OAAO;sBAClBjB,QAAQ,EAAE,MAAM;sBAChBY,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,EAAC;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACZjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,QAAQ;sBACnBL,YAAY,EAAE;oBAChB,CAAE;oBAAAvC,QAAA,eACA5C,OAAA,CAACL,GAAG;sBAAC8C,EAAE,EAAE;wBACP2C,KAAK,EAAE,MAAM;wBACbK,MAAM,EAAE,KAAK;wBACbP,OAAO,EAAE,SAAS;wBAClBR,YAAY,EAAE,KAAK;wBACnBgB,QAAQ,EAAE,UAAU;wBACpBC,MAAM,EAAE;sBACV,CAAE;sBAAA/C,QAAA,eACA5C,OAAA,CAACL,GAAG;wBAAC8C,EAAE,EAAE;0BACP2C,KAAK,EAAE,MAAM;0BACbK,MAAM,EAAE,MAAM;0BACdP,OAAO,EAAE/E,MAAM,CAACC,OAAO;0BACvBsE,YAAY,EAAE;wBAChB;sBAAE;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACLjD,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBhB,QAAQ,EAAE;oBACZ,CAAE;oBAAA3B,QAAA,EAAC;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnBjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,OAAO;sBAClBjB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACfpD,KAAK,EAAEjB,MAAM,CAACG;oBAChB,CAAE;oBAAAsC,QAAA,EAAE9B,IAAI,CAACS;kBAAW;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1BjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE,OAAO;sBAClBjB,QAAQ,EAAE,MAAM;sBAChBnD,KAAK,EAAEjB,MAAM,CAACG;oBAChB,CAAE;oBAAAsC,QAAA,GAAE9B,IAAI,CAAC+E,gBAAgB,EAAC,GAAC;kBAAA;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChCjD,OAAA;oBAAIoD,KAAK,EAAE;sBACTmC,OAAO,EAAE,UAAU;sBACnBC,SAAS,EAAE;oBACb,CAAE;oBAAA5C,QAAA,eACA5C,OAAA,CAACL,GAAG;sBAAC8C,EAAE,EAAE;wBACP2C,KAAK,EAAE,MAAM;wBACbK,MAAM,EAAE,KAAK;wBACbP,OAAO,EAAE,SAAS;wBAClBR,YAAY,EAAE,KAAK;wBACnBgB,QAAQ,EAAE,UAAU;wBACpBC,MAAM,EAAE;sBACV,CAAE;sBAAA/C,QAAA,eACA5C,OAAA,CAACL,GAAG;wBAAC8C,EAAE,EAAE;0BACP2C,KAAK,EAAE,GAAGtE,IAAI,CAAC+E,gBAAgB,GAAG;0BAClCJ,MAAM,EAAE,MAAM;0BACdP,OAAO,EAAE/E,MAAM,CAACG,OAAO;0BACvBoE,YAAY,EAAE;wBAChB;sBAAE;wBAAA5B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGPjD,OAAA,CAACH,IAAI;QAACiF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApC,QAAA,eACvB5C,OAAA,CAACL,GAAG;UAAC8C,EAAE,EAAE;YACPE,MAAM,EAAE,mBAAmB;YAC3B+B,YAAY,EAAE,CAAC;YACfO,QAAQ,EAAE,QAAQ;YAClBQ,MAAM,EAAE;UACV,CAAE;UAAA7C,QAAA,gBACA5C,OAAA,CAACL,GAAG;YAAC8C,EAAE,EAAE;cACPyC,OAAO,EAAE,SAAS;cAClBxC,CAAC,EAAE,GAAG;cACNyC,YAAY,EAAE;YAChB,CAAE;YAAAvC,QAAA,eACA5C,OAAA,CAACJ,UAAU;cAACiD,OAAO,EAAC,WAAW;cAACJ,EAAE,EAAE;gBAAE+B,UAAU,EAAE,GAAG;gBAAEpD,KAAK,EAAE;cAAU,CAAE;cAAAwB,QAAA,EAAC;YAE3E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjD,OAAA,CAACL,GAAG;YAAC8C,EAAE,EAAE;cAAEC,CAAC,EAAE,CAAC;cAAE+C,MAAM,EAAE,mBAAmB;cAAEK,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAnD,QAAA,eACpF5C,OAAA,CAACR,mBAAmB;cAAC4F,KAAK,EAAC,MAAM;cAACK,MAAM,EAAE,GAAI;cAAA7C,QAAA,eAC5C5C,OAAA,CAAClB,QAAQ;gBAAA8D,QAAA,gBACP5C,OAAA,CAACjB,GAAG;kBACF+B,IAAI,EAAEE,YAAa;kBACnBsC,EAAE,EAAC,KAAK;kBACRC,EAAE,EAAC,KAAK;kBACRE,WAAW,EAAE,EAAG;kBAChBC,WAAW,EAAE,EAAG;kBAChBsC,YAAY,EAAE,CAAE;kBAChBC,OAAO,EAAC,OAAO;kBACfC,MAAM,EAAC,MAAM;kBAAAtD,QAAA,EAEZ5B,YAAY,CAACiB,GAAG,CAAC,CAACiB,KAAK,EAAEC,KAAK,kBAC7BnD,OAAA,CAAChB,IAAI;oBAAuBoF,IAAI,EAAElB,KAAK,CAAC9B;kBAAM,GAAnC,QAAQ+B,KAAK,EAAE;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAsB,CACjD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNjD,OAAA,CAACV,OAAO;kBACN6G,OAAO,EAAEA,CAAC;oBAAE9D,MAAM;oBAAEC;kBAAQ,CAAC,KAAK;oBAChC,IAAID,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACE,MAAM,EAAE;sBACvC,oBACExC,OAAA,CAACL,GAAG;wBAAC8C,EAAE,EAAE;0BACPyC,OAAO,EAAE,OAAO;0BAChBxC,CAAC,EAAE,CAAC;0BACJC,MAAM,EAAE,mBAAmB;0BAC3B+B,YAAY,EAAE,CAAC;0BACfH,QAAQ,EAAE;wBACZ,CAAE;wBAAA3B,QAAA,eACA5C,OAAA,CAACJ,UAAU;0BAACiD,OAAO,EAAC,SAAS;0BAACJ,EAAE,EAAE;4BAAE+B,UAAU,EAAE;0BAAI,CAAE;0BAAA5B,QAAA,GACnDN,OAAO,CAAC,CAAC,CAAC,CAACrB,IAAI,EAAC,IAAE,EAACqB,OAAO,CAAC,CAAC,CAAC,CAACpB,KAAK,EAAC,GACvC;wBAAA;0BAAA4B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAEV;oBACA,OAAO,IAAI;kBACb;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGNnC,IAAI,CAACsF,iBAAiB,iBACrBpG,OAAA,CAACH,IAAI;QAACiF,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAnC,QAAA,eAChB5C,OAAA,CAACL,GAAG;UAAC8C,EAAE,EAAE;YACPE,MAAM,EAAE,mBAAmB;YAC3B+B,YAAY,EAAE,CAAC;YACfO,QAAQ,EAAE;UACZ,CAAE;UAAArC,QAAA,gBACA5C,OAAA,CAACL,GAAG;YAAC8C,EAAE,EAAE;cACPyC,OAAO,EAAE,SAAS;cAClBxC,CAAC,EAAE,GAAG;cACNyC,YAAY,EAAE;YAChB,CAAE;YAAAvC,QAAA,eACA5C,OAAA,CAACJ,UAAU;cAACiD,OAAO,EAAC,WAAW;cAACJ,EAAE,EAAE;gBAAE+B,UAAU,EAAE,GAAG;gBAAEpD,KAAK,EAAE;cAAU,CAAE;cAAAwB,QAAA,EAAC;YAE3E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjD,OAAA,CAACL,GAAG;YAAC8C,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,eAChB5C,OAAA,CAACH,IAAI;cAAC+E,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAjC,QAAA,gBACzB5C,OAAA,CAACH,IAAI;gBAACiF,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAApC,QAAA,eACvB5C,OAAA,CAACL,GAAG;kBAAC8C,EAAE,EAAE;oBAAE+C,SAAS,EAAE;kBAAS,CAAE;kBAAA5C,QAAA,gBAC/B5C,OAAA,CAACJ,UAAU;oBAACiD,OAAO,EAAC,IAAI;oBAACJ,EAAE,EAAE;sBAAE+B,UAAU,EAAE,GAAG;sBAAEpD,KAAK,EAAEjB,MAAM,CAACC;oBAAQ,CAAE;oBAAAwC,QAAA,GACrE9B,IAAI,CAACsF,iBAAiB,EAAC,GAC1B;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbjD,OAAA,CAACJ,UAAU;oBAACiD,OAAO,EAAC,SAAS;oBAACJ,EAAE,EAAE;sBAAErB,KAAK,EAAE;oBAAO,CAAE;oBAAAwB,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EACNnC,IAAI,CAACuF,cAAc,iBAClBrG,OAAA,CAAAE,SAAA;gBAAA0C,QAAA,gBACE5C,OAAA,CAACH,IAAI;kBAACiF,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAAApC,QAAA,eACvB5C,OAAA,CAACL,GAAG;oBAAC8C,EAAE,EAAE;sBAAE+C,SAAS,EAAE;oBAAS,CAAE;oBAAA5C,QAAA,gBAC/B5C,OAAA,CAACJ,UAAU;sBAACiD,OAAO,EAAC,IAAI;sBAACJ,EAAE,EAAE;wBAAE+B,UAAU,EAAE,GAAG;wBAAEpD,KAAK,EAAEjB,MAAM,CAACI;sBAAQ,CAAE;sBAAAqC,QAAA,EACrE9B,IAAI,CAACuF;oBAAc;sBAAAvD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACbjD,OAAA,CAACJ,UAAU;sBAACiD,OAAO,EAAC,SAAS;sBAACJ,EAAE,EAAE;wBAAErB,KAAK,EAAE;sBAAO,CAAE;sBAAAwB,QAAA,EAAC;oBAErD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACPjD,OAAA,CAACH,IAAI;kBAACiF,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAAApC,QAAA,eACvB5C,OAAA,CAACL,GAAG;oBAAC8C,EAAE,EAAE;sBAAE+C,SAAS,EAAE;oBAAS,CAAE;oBAAA5C,QAAA,gBAC/B5C,OAAA,CAACJ,UAAU;sBAACiD,OAAO,EAAC,OAAO;sBAACJ,EAAE,EAAE;wBAAE+B,UAAU,EAAE,GAAG;wBAAEpD,KAAK,EAAEjB,MAAM,CAACK;sBAAK,CAAE;sBAAAoC,QAAA,EACrE9B,IAAI,CAACwF;oBAAkB;sBAAAxD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC,eACbjD,OAAA,CAACJ,UAAU;sBAACiD,OAAO,EAAC,SAAS;sBAACJ,EAAE,EAAE;wBAAErB,KAAK,EAAE;sBAAO,CAAE;sBAAAwB,QAAA,EAAC;oBAErD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA,eACP,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ,CAAC;AAACsD,EAAA,GA7bI1F,aAAa;AA+bnB,eAAeA,aAAa;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}