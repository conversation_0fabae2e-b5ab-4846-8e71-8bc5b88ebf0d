{"ast": null, "code": "var nativeCreate = require('./_nativeCreate');\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction hashGet(key) {\n  var data = this.__data__;\n  if (nativeCreate) {\n    var result = data[key];\n    return result === HASH_UNDEFINED ? undefined : result;\n  }\n  return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\nmodule.exports = hashGet;", "map": {"version": 3, "names": ["nativeCreate", "require", "HASH_UNDEFINED", "objectProto", "Object", "prototype", "hasOwnProperty", "hashGet", "key", "data", "__data__", "result", "undefined", "call", "module", "exports"], "sources": ["C:/CMS/webapp/frontend/node_modules/lodash/_hashGet.js"], "sourcesContent": ["var nativeCreate = require('./_nativeCreate');\n\n/** Used to stand-in for `undefined` hash values. */\nvar HASH_UNDEFINED = '__lodash_hash_undefined__';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Gets the hash value for `key`.\n *\n * @private\n * @name get\n * @memberOf Hash\n * @param {string} key The key of the value to get.\n * @returns {*} Returns the entry value.\n */\nfunction hashGet(key) {\n  var data = this.__data__;\n  if (nativeCreate) {\n    var result = data[key];\n    return result === HASH_UNDEFINED ? undefined : result;\n  }\n  return hasOwnProperty.call(data, key) ? data[key] : undefined;\n}\n\nmodule.exports = hashGet;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;;AAE7C;AACA,IAAIC,cAAc,GAAG,2BAA2B;;AAEhD;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,GAAG,EAAE;EACpB,IAAIC,IAAI,GAAG,IAAI,CAACC,QAAQ;EACxB,IAAIV,YAAY,EAAE;IAChB,IAAIW,MAAM,GAAGF,IAAI,CAACD,GAAG,CAAC;IACtB,OAAOG,MAAM,KAAKT,cAAc,GAAGU,SAAS,GAAGD,MAAM;EACvD;EACA,OAAOL,cAAc,CAACO,IAAI,CAACJ,IAAI,EAAED,GAAG,CAAC,GAAGC,IAAI,CAACD,GAAG,CAAC,GAAGI,SAAS;AAC/D;AAEAE,MAAM,CAACC,OAAO,GAAGR,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}