{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport '../../styles/reports.css';\nimport { Box, Typography, Paper, Grid, Card, CardContent, Button, Chip, Alert, CircularProgress, Divider, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails, Switch, FormControlLabel } from '@mui/material';\nimport { Assessment as AssessmentIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon, DateRange as DateRangeIcon, Cable as CableIcon, Inventory as InventoryIcon, ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon, ShowChart as ShowChartIcon } from '@mui/icons-material';\nimport { useParams } from 'react-router-dom';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\nimport EmptyState from '../../components/common/EmptyState';\nimport MetricCard from '../../components/common/MetricCard';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s2();\n  var _s = $RefreshSig$();\n  const {\n    cantiereId\n  } = useParams();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobinaSpecifica: null,\n    storicoBobine: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // State per storico bobine accordion\n  const [expandedBobine, setExpandedBobine] = useState(new Set());\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Import certificazione service\n        const certificazioneService = await import('../../services/certificazioneService');\n\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading progress report:', err);\n          return {\n            content: null\n          };\n        });\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video').catch(err => {\n          console.error('Error loading BOQ report:', err);\n          return {\n            content: null\n          };\n        });\n\n        // Carica statistiche certificazioni\n        const certificazioniPromise = certificazioneService.default.getCertificazioni(cantiereId).catch(err => {\n          console.error('Error loading certificazioni:', err);\n          return [];\n        });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, certificazioniData] = await Promise.all([progressPromise, boqPromise, certificazioniPromise]);\n\n        // Aggiungi statistiche certificazioni ai dati del progress report\n        if (progressData.content && certificazioniData) {\n          const totaleCavi = progressData.content.totale_cavi || 0;\n          const caviCertificati = certificazioniData.length;\n          const percentualeCertificazione = totaleCavi > 0 ? Math.round(caviCertificati / totaleCavi * 100) : 0;\n\n          // Calcola certificazioni di oggi\n          const oggi = new Date().toDateString();\n          const certificazioniOggi = certificazioniData.filter(cert => new Date(cert.data_certificazione).toDateString() === oggi).length;\n          progressData.content.certificazioni = {\n            totale: caviCertificati,\n            percentuale: percentualeCertificazione,\n            oggi: certificazioniOggi,\n            rimanenti: totaleCavi - caviCertificati\n          };\n        }\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobinaSpecifica: null,\n          storicoBobine: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Load storico bobine when selected\n  useEffect(() => {\n    if (cantiereId && selectedReportType === 'storico-bobine') {\n      loadStoricoBobine();\n    }\n  }, [cantiereId, selectedReportType]);\n\n  // Function to load storico bobine data\n  const loadStoricoBobine = async () => {\n    try {\n      setLoading(true);\n      // Simuliamo i dati per ora - in futuro sarà una chiamata API\n      const mockData = {\n        bobine: [{\n          id: 1,\n          codice: 'BOB001',\n          tipologia: 'FG7OR 4x16',\n          formazione: '4x16',\n          metri_totali: 500,\n          metri_utilizzati: 350,\n          metri_residui: 150,\n          stato: 'In Uso',\n          data_arrivo: '2024-01-15',\n          fornitore: 'Prysmian',\n          cavi_associati: [{\n            id: 101,\n            nomenclatura: 'CAV001',\n            metri_utilizzati: 120,\n            data_posa: '2024-01-20'\n          }, {\n            id: 102,\n            nomenclatura: 'CAV002',\n            metri_utilizzati: 85,\n            data_posa: '2024-01-22'\n          }, {\n            id: 103,\n            nomenclatura: 'CAV003',\n            metri_utilizzati: 145,\n            data_posa: '2024-01-25'\n          }]\n        }, {\n          id: 2,\n          codice: 'BOB002',\n          tipologia: 'FG7OR 2x10',\n          formazione: '2x10',\n          metri_totali: 300,\n          metri_utilizzati: 300,\n          metri_residui: 0,\n          stato: 'Esaurita',\n          data_arrivo: '2024-01-10',\n          fornitore: 'Nexans',\n          cavi_associati: [{\n            id: 201,\n            nomenclatura: 'CAV004',\n            metri_utilizzati: 150,\n            data_posa: '2024-01-18'\n          }, {\n            id: 202,\n            nomenclatura: 'CAV005',\n            metri_utilizzati: 150,\n            data_posa: '2024-01-19'\n          }]\n        }, {\n          id: 3,\n          codice: 'BOB003',\n          tipologia: 'FG7OR 6x25',\n          formazione: '6x25',\n          metri_totali: 800,\n          metri_utilizzati: 200,\n          metri_residui: 600,\n          stato: 'Disponibile',\n          data_arrivo: '2024-02-01',\n          fornitore: 'Prysmian',\n          cavi_associati: [{\n            id: 301,\n            nomenclatura: 'CAV006',\n            metri_utilizzati: 200,\n            data_posa: '2024-02-05'\n          }]\n        }]\n      };\n      setReportsData(prev => ({\n        ...prev,\n        storicoBobine: mockData\n      }));\n    } catch (err) {\n      console.error('Error loading storico bobine:', err);\n      setError('Errore nel caricamento dello storico bobine');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderProgressReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 1,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: '#2c3e50'\n        },\n        children: \"\\uD83D\\uDCCA Report Avanzamento Lavori\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Totali\",\n          value: data.metri_totali,\n          unit: \"m\",\n          subtitle: \"Lunghezza complessiva del progetto\",\n          gradient: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Posati\",\n          value: data.metri_posati,\n          unit: \"m\",\n          subtitle: `${data.percentuale_avanzamento}% completato`,\n          gradient: \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n          progress: data.percentuale_avanzamento,\n          trend: data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down',\n          trendValue: `${data.percentuale_avanzamento}%`,\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Rimanenti\",\n          value: data.metri_da_posare,\n          unit: \"m\",\n          subtitle: `${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`,\n          gradient: \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Media/Giorno\",\n          value: data.media_giornaliera || 0,\n          unit: \"m\",\n          subtitle: data.giorni_stimati ? `${data.giorni_stimati} giorni lavorativi rimasti` : data.media_giornaliera > 0 ? 'Calcolo in corso' : 'Nessuna posa recente',\n          gradient: \"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\",\n          size: \"large\",\n          tooltip: data.giorni_lavorativi_effettivi ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.` : 'Media giornaliera basata sui giorni di lavoro effettivo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(ProgressChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      sx: {\n        mb: 5\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            border: '1px solid #e0e0e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n                sx: {\n                  color: '#3498db',\n                  mr: 1,\n                  fontSize: 28\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#2c3e50'\n                },\n                children: \"Stato Cavi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    bgcolor: '#f8f9fa',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: '#2c3e50',\n                      mb: 1\n                    },\n                    children: data.totale_cavi\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Cavi Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    bgcolor: '#e8f5e8',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: '#27ae60',\n                      mb: 1\n                    },\n                    children: data.cavi_posati\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: [\"Cavi Posati (\", data.percentuale_cavi, \"%)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Progresso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: [data.percentuale_cavi, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: `${data.percentuale_cavi}%`,\n                    height: '100%',\n                    bgcolor: '#27ae60',\n                    transition: 'width 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            border: '1px solid #e0e0e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  bgcolor: '#9c27b0',\n                  borderRadius: '50%',\n                  p: 1,\n                  mr: 2,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    color: 'white',\n                    fontWeight: 'bold'\n                  },\n                  children: \"\\uD83D\\uDD12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#2c3e50'\n                },\n                children: \"Stato Certificazioni Cavi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this), data.certificazioni ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                sx: {\n                  mb: 3\n                },\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center',\n                      p: 2,\n                      bgcolor: '#e8f5e8',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      sx: {\n                        fontWeight: 700,\n                        color: '#27ae60',\n                        mb: 1\n                      },\n                      children: data.certificazioni.totale\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 512,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        color: '#666'\n                      },\n                      children: \"Certificati\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 515,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      textAlign: 'center',\n                      p: 2,\n                      bgcolor: '#fff3cd',\n                      borderRadius: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h4\",\n                      sx: {\n                        fontWeight: 700,\n                        color: '#856404',\n                        mb: 1\n                      },\n                      children: data.certificazioni.rimanenti\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 523,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        color: '#666'\n                      },\n                      children: \"Da Certificare\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 526,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  mb: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 700,\n                    color: '#9c27b0',\n                    mb: 1\n                  },\n                  children: [data.certificazioni.percentuale, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    mb: 1\n                  },\n                  children: \"Completamento Certificazioni\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: '#999',\n                    fontSize: '0.75rem'\n                  },\n                  children: [data.certificazioni.oggi, \" certificazioni completate oggi\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: `${data.certificazioni.percentuale}%`,\n                    height: '100%',\n                    bgcolor: '#9c27b0',\n                    transition: 'width 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                p: 2,\n                bgcolor: '#f8f9fa',\n                borderRadius: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: \"Nessuna certificazione disponibile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 7\n    }, this), data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        border: '1px solid #e0e0e0'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(DateRangeIcon, {\n            sx: {\n              color: '#9b59b6',\n              mr: 1,\n              fontSize: 28\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600,\n              color: '#2c3e50'\n            },\n            children: \"\\uD83D\\uDCC8 Attivit\\xE0 Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: data.posa_recente.slice(0, 5).map((posa, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                border: '1px solid #e0e0e0',\n                borderRadius: 2,\n                bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',\n                transition: 'all 0.2s',\n                '&:hover': {\n                  bgcolor: '#f5f5f5',\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666',\n                  mb: 1\n                },\n                children: posa.data\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#2c3e50'\n                },\n                children: [posa.metri, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 21\n              }, this), index === 0 && /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Pi\\xF9 recente\",\n                size: \"small\",\n                sx: {\n                  mt: 1,\n                  bgcolor: '#3498db',\n                  color: 'white',\n                  fontSize: '0.7rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 13\n        }, this), data.posa_recente.length > 5 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Accordion, {\n            children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n              expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 49\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#3498db'\n                },\n                children: [\"Mostra tutti i \", data.posa_recente.length, \" record\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n              children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n                data: data.posa_recente.map(posa => ({\n                  data: posa.data,\n                  metri: `${posa.metri}m`\n                })),\n                columns: [{\n                  field: 'data',\n                  headerName: 'Data',\n                  width: 200\n                }, {\n                  field: 'metri',\n                  headerName: 'Metri Posati',\n                  width: 150,\n                  align: 'right'\n                }],\n                pagination: true,\n                pageSize: 10\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 577,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 326,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 1,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n        sx: {\n          color: '#8e44ad',\n          mr: 1,\n          fontSize: 28\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 669,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: '#2c3e50'\n        },\n        children: \"\\uD83D\\uDCCB Bill of Quantities - Distinta Materiali\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 660,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(BoqChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 677,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 658,\n    columnNumber: 5\n  }, this);\n  const renderStoricoBobineReport = data => {\n    _s();\n    const [expandedBobine, setExpandedBobine] = useState(new Set());\n    const toggleBobina = bobinaId => {\n      const newExpanded = new Set(expandedBobine);\n      if (newExpanded.has(bobinaId)) {\n        newExpanded.delete(bobinaId);\n      } else {\n        newExpanded.add(bobinaId);\n      }\n      setExpandedBobine(newExpanded);\n    };\n    const getStatoColor = stato => {\n      switch (stato) {\n        case 'Disponibile':\n          return '#27ae60';\n        case 'In Uso':\n          return '#f39c12';\n        case 'Esaurita':\n          return '#e74c3c';\n        default:\n          return '#95a5a6';\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 3,\n          p: 2,\n          bgcolor: '#f8f9fa',\n          borderRadius: 1,\n          border: '1px solid #e0e0e0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n          sx: {\n            color: '#9b59b6',\n            mr: 1,\n            fontSize: 28\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          sx: {\n            fontWeight: 600,\n            color: '#2c3e50'\n          },\n          children: \"\\uD83D\\uDCE6 Storico Bobine - Cavi Associati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 710,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: data.bobine.map(bobina => /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            mb: 2,\n            border: '1px solid #e0e0e0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 3,\n              cursor: 'pointer',\n              bgcolor: expandedBobine.has(bobina.id) ? '#f8f9fa' : 'white',\n              borderBottom: expandedBobine.has(bobina.id) ? '1px solid #e0e0e0' : 'none',\n              '&:hover': {\n                bgcolor: '#f5f5f5'\n              }\n            },\n            onClick: () => toggleBobina(bobina.id),\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: 8,\n                      height: 8,\n                      borderRadius: '50%',\n                      bgcolor: getStatoColor(bobina.stato),\n                      mr: 2\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 745,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      sx: {\n                        fontWeight: 600,\n                        color: '#2c3e50'\n                      },\n                      children: bobina.codice\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 753,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        color: '#666'\n                      },\n                      children: bobina.tipologia\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 756,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 752,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    mb: 0.5\n                  },\n                  children: \"Formazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 500\n                  },\n                  children: bobina.formazione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 767,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 763,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    mb: 0.5\n                  },\n                  children: \"Metri Totali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 500\n                  },\n                  children: [bobina.metri_totali, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    mb: 0.5\n                  },\n                  children: \"Utilizzati\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 500,\n                    color: '#e74c3c'\n                  },\n                  children: [bobina.metri_utilizzati, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 785,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    mb: 0.5\n                  },\n                  children: \"Residui\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 500,\n                    color: '#27ae60'\n                  },\n                  children: [bobina.metri_residui, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 794,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 790,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 1,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'center'\n                  },\n                  children: expandedBobine.has(bobina.id) ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {\n                    sx: {\n                      color: '#666'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 802,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {\n                    sx: {\n                      color: '#666'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 803,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 15\n          }, this), expandedBobine.has(bobina.id) && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 3,\n              bgcolor: '#fafafa'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2,\n                fontWeight: 600,\n                color: '#2c3e50'\n              },\n              children: [\"Cavi Associati (\", bobina.cavi_associati.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 19\n            }, this), bobina.cavi_associati.length > 0 ? /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: bobina.cavi_associati.map(cavo => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 4,\n                children: /*#__PURE__*/_jsxDEV(Paper, {\n                  sx: {\n                    p: 2,\n                    border: '1px solid #e0e0e0',\n                    bgcolor: 'white'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    sx: {\n                      fontWeight: 600,\n                      mb: 1\n                    },\n                    children: cavo.nomenclatura\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 822,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666',\n                      mb: 0.5\n                    },\n                    children: [\"Metri utilizzati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [cavo.metri_utilizzati, \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 826,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 825,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: [\"Data posa: \", new Date(cavo.data_posa).toLocaleDateString('it-IT')]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 828,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 27\n                }, this)\n              }, cavo.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                p: 3,\n                bgcolor: 'white',\n                borderRadius: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: \"Nessun cavo associato a questa bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 837,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 3,\n                p: 2,\n                bgcolor: 'white',\n                borderRadius: 1,\n                border: '1px solid #e0e0e0'\n              },\n              children: /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 4,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666',\n                      mb: 0.5\n                    },\n                    children: \"Fornitore\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 847,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    sx: {\n                      fontWeight: 500\n                    },\n                    children: bobina.fornitore\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 850,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 846,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 4,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666',\n                      mb: 0.5\n                    },\n                    children: \"Data Arrivo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 855,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    sx: {\n                      fontWeight: 500\n                    },\n                    children: new Date(bobina.data_arrivo).toLocaleDateString('it-IT')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 858,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 854,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  sm: 4,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666',\n                      mb: 0.5\n                    },\n                    children: \"Stato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 863,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        bgcolor: getStatoColor(bobina.stato),\n                        mr: 1\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 867,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      sx: {\n                        fontWeight: 500\n                      },\n                      children: bobina.stato\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 874,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 862,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 17\n          }, this)]\n        }, bobina.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          bgcolor: '#f8f9fa',\n          border: '1px solid #e0e0e0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2,\n            fontWeight: 600,\n            color: '#2c3e50'\n          },\n          children: \"\\uD83D\\uDCCA Riepilogo Generale\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#2c3e50',\n                  mb: 1\n                },\n                children: data.bobine.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: \"Bobine Totali\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 898,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 893,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#2c3e50',\n                  mb: 1\n                },\n                children: data.bobine.reduce((sum, b) => sum + b.cavi_associati.length, 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 905,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: \"Cavi Associati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 908,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 903,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#2c3e50',\n                  mb: 1\n                },\n                children: [data.bobine.reduce((sum, b) => sum + b.metri_utilizzati, 0), \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 915,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: \"Metri Utilizzati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 918,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 914,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 913,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 3,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#2c3e50',\n                  mb: 1\n                },\n                children: [data.bobine.reduce((sum, b) => sum + b.metri_residui, 0), \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 925,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: \"Metri Residui\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 928,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 924,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 923,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 892,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 888,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 708,\n      columnNumber: 7\n    }, this);\n  };\n  _s(renderStoricoBobineReport, \"5Fph/b+BmS+FESDTjsTaNz1A0bc=\");\n  const renderPosaPeriodoReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: 'warning.main'\n        },\n        children: \"Report Posa per Periodo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 947,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 952,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 960,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 959,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 950,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 946,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      sx: {\n        mb: 5\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'warning.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.totale_metri_periodo, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 971,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Metri Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 974,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: [data.data_inizio, \" - \", data.data_fine]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 975,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 970,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 969,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'info.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: data.giorni_attivi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 980,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Giorni Attivi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 983,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 979,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 978,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'success.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.media_giornaliera, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 988,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Giorno\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 991,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 987,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 986,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'primary.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [Math.round(data.totale_metri_periodo / data.giorni_attivi * 7), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 996,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Settimana\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 999,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 995,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 994,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 968,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 5,\n        width: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(TimelineChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1007,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1006,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2,\n          fontWeight: 600\n        },\n        children: \"Dettaglio Posa Giornaliera\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1013,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.posa_giornaliera || [],\n        columns: [{\n          field: 'data',\n          headerName: 'Data',\n          width: 200\n        }, {\n          field: 'metri',\n          headerName: 'Metri Posati',\n          width: 150,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri}m`\n        }],\n        pageSize: 10\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1016,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1012,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 944,\n    columnNumber: 5\n  }, this);\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: dialogType === 'posa-periodo' ? 'Report Posa per Periodo' : 'Genera Report'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1033,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1038,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1046,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1052,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1053,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1054,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1045,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1044,\n          columnNumber: 11\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1064,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1063,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1074,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1073,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1043,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1036,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1088,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1093,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1093,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1089,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1087,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1032,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"report-main-container report-fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1105,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1104,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1111,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1110,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600,\n            color: '#2c3e50',\n            mb: 2,\n            textAlign: 'center'\n          },\n          children: \"\\uD83C\\uDFAF Seleziona il tipo di report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: `report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`,\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('progress'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#3498db',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1137,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Avanzamento\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1138,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Panoramica lavori\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1141,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1136,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1125,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('boq'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#8e44ad',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1161,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Bill of Quantities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1162,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Distinta materiali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1165,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1160,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1150,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'storico-bobine' ? '2px solid #9b59b6' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'storico-bobine' ? '#f8f4ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('storico-bobine'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#9b59b6',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Storico Bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1188,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Cavi per bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1191,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1186,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1176,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minHeight: '400px',\n          width: '100%'\n        },\n        children: [selectedReportType === 'progress' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            borderRadius: 2\n          },\n          children: reportsData.progress ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1209,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1208,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1219,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1218,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1207,\n              columnNumber: 19\n            }, this), renderProgressReport(reportsData.progress)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1206,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"progress\",\n            title: \"Caricamento Report Avanzamento...\",\n            description: \"Stiamo elaborando i dati dell'avanzamento dei lavori\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1231,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"progress\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare il report di avanzamento. Verifica la connessione e riprova.\",\n            onRetry: () => {\n              setLoading(true);\n              reportService.getProgressReport(cantiereId, 'video').then(data => {\n                setReportsData(prev => ({\n                  ...prev,\n                  progress: data.content\n                }));\n              }).catch(err => {\n                console.error('Error retrying progress report:', err);\n              }).finally(() => {\n                setLoading(false);\n              });\n            },\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1238,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1204,\n          columnNumber: 13\n        }, this), selectedReportType === 'boq' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            borderRadius: 2\n          },\n          children: reportsData.boq ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1272,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1271,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1282,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1281,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1270,\n              columnNumber: 19\n            }, this), renderBoqReport(reportsData.boq)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1269,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"boq\",\n            title: \"Caricamento Bill of Quantities...\",\n            description: \"Stiamo elaborando la distinta materiali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1294,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"boq\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare la distinta materiali. Verifica la connessione e riprova.\",\n            onRetry: () => {\n              setLoading(true);\n              reportService.getBillOfQuantities(cantiereId, 'video').then(data => {\n                setReportsData(prev => ({\n                  ...prev,\n                  boq: data.content\n                }));\n              }).catch(err => {\n                console.error('Error retrying BOQ report:', err);\n              }).finally(() => {\n                setLoading(false);\n              });\n            },\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1301,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1267,\n          columnNumber: 13\n        }, this), selectedReportType === 'storico-bobine' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            borderRadius: 2\n          },\n          children: reportsData.storicoBobine ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1335,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('storico-bobine', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1334,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1345,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('storico-bobine', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1344,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1333,\n              columnNumber: 19\n            }, this), renderStoricoBobineReport(reportsData.storicoBobine)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1332,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"storico-bobine\",\n            title: \"Caricamento Storico Bobine...\",\n            description: \"Stiamo elaborando i dati delle bobine e dei cavi associati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1357,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"storico-bobine\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare lo storico bobine. Verifica la connessione e riprova.\",\n            onRetry: () => loadStoricoBobine(),\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1364,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1330,\n          columnNumber: 13\n        }, this), selectedReportType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.posaPeriodo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1383,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1382,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1393,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1392,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1381,\n              columnNumber: 19\n            }, this), renderPosaPeriodoReport(reportsData.posaPeriodo)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1380,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"action-required\",\n            reportType: \"posa-periodo\",\n            title: \"Seleziona un Periodo\",\n            description: \"Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttivit\\xE0 del team.\",\n            actionLabel: \"Seleziona Periodo\",\n            onAction: () => {\n              setDialogType('posa-periodo');\n              // Set default date range (last month to today)\n              const today = new Date();\n              const lastMonth = new Date();\n              lastMonth.setMonth(today.getMonth() - 1);\n              setFormData({\n                ...formData,\n                data_inizio: lastMonth.toISOString().split('T')[0],\n                data_fine: today.toISOString().split('T')[0]\n              });\n              setOpenDialog(true);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1405,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1378,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1201,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1116,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1102,\n    columnNumber: 5\n  }, this);\n};\n_s2(ReportCaviPageNew, \"eQ0UGmsvcrJBK75aVpbKNwjrHh4=\", false, function () {\n  return [useParams];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Switch", "FormControlLabel", "Assessment", "AssessmentIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "ExpandLess", "ExpandLessIcon", "ShowChart", "ShowChartIcon", "useParams", "AdminHomeButton", "reportService", "FilterableTable", "EmptyState", "MetricCard", "ProgressChart", "<PERSON><PERSON><PERSON><PERSON>", "TimelineChart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s2", "_s", "$RefreshSig$", "cantiereId", "loading", "setLoading", "error", "setError", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedReportType", "setSelectedReportType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportsData", "setReportsData", "progress", "boq", "bobinaSpecifica", "storicoBobine", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "expandedBobine", "setExpandedBobine", "Set", "loadAllReports", "certificazioneService", "progressPromise", "getProgressReport", "catch", "err", "console", "content", "boq<PERSON><PERSON><PERSON>", "getBillOfQuantities", "certificazioniPromise", "default", "getCertificazioni", "progressData", "boqData", "certificazioniData", "Promise", "all", "totaleCavi", "totale_cavi", "caviCertificati", "length", "percentualeCertificazione", "Math", "round", "oggi", "Date", "toDateString", "certificazioniOggi", "filter", "cert", "data_certificazione", "certificazioni", "totale", "percentuale", "<PERSON><PERSON><PERSON>", "loadStoricoBobine", "mockData", "bobine", "id", "codice", "tipologia", "formazione", "metri_totali", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "metri_residui", "stato", "data_arrivo", "fornitore", "cavi_associati", "nomenclatura", "data_posa", "prev", "generateReportWithFormat", "reportType", "format", "response", "getPosaPerPeriodoReport", "Error", "file_url", "window", "open", "detail", "message", "handleGenerateReport", "handleCloseDialog", "renderProgressReport", "data", "children", "sx", "display", "justifyContent", "alignItems", "mb", "p", "bgcolor", "borderRadius", "border", "variant", "fontWeight", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "control", "checked", "onChange", "e", "target", "label", "mr", "container", "spacing", "item", "xs", "sm", "md", "title", "value", "unit", "subtitle", "gradient", "size", "metri_posati", "percentuale_avanzamento", "trend", "trendValue", "metri_da_posare", "toFixed", "media_giornaliera", "giorni_stimati", "tooltip", "giorni_lavorativi_effettivi", "height", "fontSize", "textAlign", "cavi_posati", "percentuale_cavi", "mt", "width", "overflow", "transition", "posa_recente", "slice", "map", "posa", "index", "transform", "boxShadow", "metri", "expandIcon", "columns", "field", "headerName", "align", "pagination", "pageSize", "renderBoqReport", "renderStoricoBobineReport", "to<PERSON><PERSON><PERSON><PERSON>", "bobina<PERSON>d", "newExpanded", "has", "delete", "add", "getStatoColor", "bobina", "cursor", "borderBottom", "onClick", "cavo", "toLocaleDateString", "reduce", "sum", "b", "renderPosaPeriodoReport", "totale_metri_periodo", "giorni_attivi", "posa_giornal<PERSON>", "dataType", "renderCell", "row", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "type", "InputLabelProps", "shrink", "disabled", "startIcon", "className", "my", "flexDirection", "minHeight", "description", "onRetry", "then", "finally", "posaPeriodo", "actionLabel", "onAction", "today", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport '../../styles/reports.css';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon,\n  ExpandLess as ExpandLessIcon,\n  ShowChart as ShowChartIcon\n} from '@mui/icons-material';\nimport { useParams } from 'react-router-dom';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\nimport EmptyState from '../../components/common/EmptyState';\nimport MetricCard from '../../components/common/MetricCard';\n\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\n\nconst ReportCaviPageNew = () => {\n  const { cantiereId } = useParams();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobinaSpecifica: null,\n    storicoBobine: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // State per storico bobine accordion\n  const [expandedBobine, setExpandedBobine] = useState(new Set());\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Import certificazione service\n        const certificazioneService = await import('../../services/certificazioneService');\n\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading progress report:', err);\n            return { content: null };\n          });\n\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading BOQ report:', err);\n            return { content: null };\n          });\n\n        // Carica statistiche certificazioni\n        const certificazioniPromise = certificazioneService.default.getCertificazioni(cantiereId)\n          .catch(err => {\n            console.error('Error loading certificazioni:', err);\n            return [];\n          });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, certificazioniData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          certificazioniPromise\n        ]);\n\n        // Aggiungi statistiche certificazioni ai dati del progress report\n        if (progressData.content && certificazioniData) {\n          const totaleCavi = progressData.content.totale_cavi || 0;\n          const caviCertificati = certificazioniData.length;\n          const percentualeCertificazione = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;\n\n          // Calcola certificazioni di oggi\n          const oggi = new Date().toDateString();\n          const certificazioniOggi = certificazioniData.filter(cert =>\n            new Date(cert.data_certificazione).toDateString() === oggi\n          ).length;\n\n          progressData.content.certificazioni = {\n            totale: caviCertificati,\n            percentuale: percentualeCertificazione,\n            oggi: certificazioniOggi,\n            rimanenti: totaleCavi - caviCertificati\n          };\n        }\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobinaSpecifica: null,\n          storicoBobine: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Load storico bobine when selected\n  useEffect(() => {\n    if (cantiereId && selectedReportType === 'storico-bobine') {\n      loadStoricoBobine();\n    }\n  }, [cantiereId, selectedReportType]);\n\n  // Function to load storico bobine data\n  const loadStoricoBobine = async () => {\n    try {\n      setLoading(true);\n      // Simuliamo i dati per ora - in futuro sarà una chiamata API\n      const mockData = {\n        bobine: [\n          {\n            id: 1,\n            codice: 'BOB001',\n            tipologia: 'FG7OR 4x16',\n            formazione: '4x16',\n            metri_totali: 500,\n            metri_utilizzati: 350,\n            metri_residui: 150,\n            stato: 'In Uso',\n            data_arrivo: '2024-01-15',\n            fornitore: 'Prysmian',\n            cavi_associati: [\n              { id: 101, nomenclatura: 'CAV001', metri_utilizzati: 120, data_posa: '2024-01-20' },\n              { id: 102, nomenclatura: 'CAV002', metri_utilizzati: 85, data_posa: '2024-01-22' },\n              { id: 103, nomenclatura: 'CAV003', metri_utilizzati: 145, data_posa: '2024-01-25' }\n            ]\n          },\n          {\n            id: 2,\n            codice: 'BOB002',\n            tipologia: 'FG7OR 2x10',\n            formazione: '2x10',\n            metri_totali: 300,\n            metri_utilizzati: 300,\n            metri_residui: 0,\n            stato: 'Esaurita',\n            data_arrivo: '2024-01-10',\n            fornitore: 'Nexans',\n            cavi_associati: [\n              { id: 201, nomenclatura: 'CAV004', metri_utilizzati: 150, data_posa: '2024-01-18' },\n              { id: 202, nomenclatura: 'CAV005', metri_utilizzati: 150, data_posa: '2024-01-19' }\n            ]\n          },\n          {\n            id: 3,\n            codice: 'BOB003',\n            tipologia: 'FG7OR 6x25',\n            formazione: '6x25',\n            metri_totali: 800,\n            metri_utilizzati: 200,\n            metri_residui: 600,\n            stato: 'Disponibile',\n            data_arrivo: '2024-02-01',\n            fornitore: 'Prysmian',\n            cavi_associati: [\n              { id: 301, nomenclatura: 'CAV006', metri_utilizzati: 200, data_posa: '2024-02-05' }\n            ]\n          }\n        ]\n      };\n\n      setReportsData(prev => ({\n        ...prev,\n        storicoBobine: mockData\n      }));\n    } catch (err) {\n      console.error('Error loading storico bobine:', err);\n      setError('Errore nel caricamento dello storico bobine');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n\n\n  const renderProgressReport = (data) => (\n    <Box>\n      {/* Header con controlli migliorato */}\n      <Box sx={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 1,\n        border: '1px solid #e0e0e0'\n      }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n          📊 Report Avanzamento Lavori\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Metriche Principali - Cards Moderne con MetricCard */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Totali\"\n            value={data.metri_totali}\n            unit=\"m\"\n            subtitle=\"Lunghezza complessiva del progetto\"\n            gradient=\"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\"\n            size=\"large\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Posati\"\n            value={data.metri_posati}\n            unit=\"m\"\n            subtitle={`${data.percentuale_avanzamento}% completato`}\n            gradient=\"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\"\n            progress={data.percentuale_avanzamento}\n            trend={data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down'}\n            trendValue={`${data.percentuale_avanzamento}%`}\n            size=\"large\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Rimanenti\"\n            value={data.metri_da_posare}\n            unit=\"m\"\n            subtitle={`${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`}\n            gradient=\"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\"\n            size=\"large\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Media/Giorno\"\n            value={data.media_giornaliera || 0}\n            unit=\"m\"\n            subtitle={\n              data.giorni_stimati\n                ? `${data.giorni_stimati} giorni lavorativi rimasti`\n                : (data.media_giornaliera > 0\n                    ? 'Calcolo in corso'\n                    : 'Nessuna posa recente')\n            }\n            gradient=\"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\"\n            size=\"large\"\n            tooltip={\n              data.giorni_lavorativi_effettivi\n                ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.`\n                : 'Media giornaliera basata sui giorni di lavoro effettivo'\n            }\n          />\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <ProgressChart data={data} />\n        </Box>\n      )}\n\n      {/* Dettagli Performance - Cards Informative */}\n      <Grid container spacing={4} sx={{ mb: 5 }}>\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <CableIcon sx={{ color: '#3498db', mr: 1, fontSize: 28 }} />\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Stato Cavi\n                </Typography>\n              </Box>\n              <Grid container spacing={2}>\n                <Grid item xs={6}>\n                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                      {data.totale_cavi}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      Cavi Totali\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={6}>\n                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>\n                      {data.cavi_posati}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      Cavi Posati ({data.percentuale_cavi}%)\n                    </Typography>\n                  </Box>\n                </Grid>\n              </Grid>\n              <Box sx={{ mt: 2 }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body2\">Progresso</Typography>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                    {data.percentuale_cavi}%\n                  </Typography>\n                </Box>\n                <Box sx={{\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                }}>\n                  <Box sx={{\n                    width: `${data.percentuale_cavi}%`,\n                    height: '100%',\n                    bgcolor: '#27ae60',\n                    transition: 'width 0.3s ease'\n                  }} />\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                <Box sx={{\n                  bgcolor: '#9c27b0',\n                  borderRadius: '50%',\n                  p: 1,\n                  mr: 2,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}>\n                  <Typography variant=\"h6\" sx={{ color: 'white', fontWeight: 'bold' }}>\n                    🔒\n                  </Typography>\n                </Box>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Stato Certificazioni Cavi\n                </Typography>\n              </Box>\n\n              {data.certificazioni ? (\n                <>\n                  <Grid container spacing={2} sx={{ mb: 3 }}>\n                    <Grid item xs={6}>\n                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>\n                        <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>\n                          {data.certificazioni.totale}\n                        </Typography>\n                        <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                          Certificati\n                        </Typography>\n                      </Box>\n                    </Grid>\n\n                    <Grid item xs={6}>\n                      <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#fff3cd', borderRadius: 1 }}>\n                        <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#856404', mb: 1 }}>\n                          {data.certificazioni.rimanenti}\n                        </Typography>\n                        <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                          Da Certificare\n                        </Typography>\n                      </Box>\n                    </Grid>\n                  </Grid>\n\n                  <Box sx={{ textAlign: 'center', mb: 2 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#9c27b0', mb: 1 }}>\n                      {data.certificazioni.percentuale}%\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 1 }}>\n                      Completamento Certificazioni\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#999', fontSize: '0.75rem' }}>\n                      {data.certificazioni.oggi} certificazioni completate oggi\n                    </Typography>\n                  </Box>\n\n                  {/* Progress bar certificazioni */}\n                  <Box sx={{\n                    width: '100%',\n                    height: 8,\n                    bgcolor: '#e0e0e0',\n                    borderRadius: 4,\n                    overflow: 'hidden'\n                  }}>\n                    <Box sx={{\n                      width: `${data.certificazioni.percentuale}%`,\n                      height: '100%',\n                      bgcolor: '#9c27b0',\n                      transition: 'width 0.3s ease'\n                    }} />\n                  </Box>\n                </>\n              ) : (\n                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>\n                  <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                    Nessuna certificazione disponibile\n                  </Typography>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n\n\n      {/* Attività Recente - Design Migliorato */}\n      {data.posa_recente && data.posa_recente.length > 0 && (\n        <Card sx={{ border: '1px solid #e0e0e0' }}>\n          <CardContent sx={{ p: 3 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n              <DateRangeIcon sx={{ color: '#9b59b6', mr: 1, fontSize: 28 }} />\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                📈 Attività Recente\n              </Typography>\n            </Box>\n\n            {/* Mostra solo gli ultimi 5 record in formato card per mobile-friendly */}\n            <Grid container spacing={2}>\n              {data.posa_recente.slice(0, 5).map((posa, index) => (\n                <Grid item xs={12} sm={6} md={4} key={index}>\n                  <Box sx={{\n                    p: 2,\n                    border: '1px solid #e0e0e0',\n                    borderRadius: 2,\n                    bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',\n                    transition: 'all 0.2s',\n                    '&:hover': {\n                      bgcolor: '#f5f5f5',\n                      transform: 'translateY(-2px)',\n                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                    }\n                  }}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 1 }}>\n                      {posa.data}\n                    </Typography>\n                    <Typography variant=\"h5\" sx={{ fontWeight: 700, color: '#2c3e50' }}>\n                      {posa.metri}m\n                    </Typography>\n                    {index === 0 && (\n                      <Chip\n                        label=\"Più recente\"\n                        size=\"small\"\n                        sx={{\n                          mt: 1,\n                          bgcolor: '#3498db',\n                          color: 'white',\n                          fontSize: '0.7rem'\n                        }}\n                      />\n                    )}\n                  </Box>\n                </Grid>\n              ))}\n            </Grid>\n\n            {/* Link per vedere tutti i dati se ce ne sono di più */}\n            {data.posa_recente.length > 5 && (\n              <Box sx={{ mt: 3, textAlign: 'center' }}>\n                <Accordion>\n                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                    <Typography variant=\"body2\" sx={{ color: '#3498db' }}>\n                      Mostra tutti i {data.posa_recente.length} record\n                    </Typography>\n                  </AccordionSummary>\n                  <AccordionDetails>\n                    <FilterableTable\n                      data={data.posa_recente.map(posa => ({\n                        data: posa.data,\n                        metri: `${posa.metri}m`\n                      }))}\n                      columns={[\n                        { field: 'data', headerName: 'Data', width: 200 },\n                        { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }\n                      ]}\n                      pagination={true}\n                      pageSize={10}\n                    />\n                  </AccordionDetails>\n                </Accordion>\n              </Box>\n            )}\n          </CardContent>\n        </Card>\n      )}\n    </Box>\n  );\n\n  const renderBoqReport = (data) => (\n    <Box>\n      {/* Header migliorato */}\n      <Box sx={{\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 1,\n        border: '1px solid #e0e0e0'\n      }}>\n        <ListIcon sx={{ color: '#8e44ad', mr: 1, fontSize: 28 }} />\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n          📋 Bill of Quantities - Distinta Materiali\n        </Typography>\n      </Box>\n\n      {/* Grafici BOQ se disponibili */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <BoqChart data={data} />\n        </Box>\n      )}\n\n\n    </Box>\n  );\n  const renderStoricoBobineReport = (data) => {\n    const [expandedBobine, setExpandedBobine] = useState(new Set());\n\n    const toggleBobina = (bobinaId) => {\n      const newExpanded = new Set(expandedBobine);\n      if (newExpanded.has(bobinaId)) {\n        newExpanded.delete(bobinaId);\n      } else {\n        newExpanded.add(bobinaId);\n      }\n      setExpandedBobine(newExpanded);\n    };\n\n    const getStatoColor = (stato) => {\n      switch (stato) {\n        case 'Disponibile': return '#27ae60';\n        case 'In Uso': return '#f39c12';\n        case 'Esaurita': return '#e74c3c';\n        default: return '#95a5a6';\n      }\n    };\n\n    return (\n      <Box>\n        {/* Header */}\n        <Box sx={{\n          display: 'flex',\n          alignItems: 'center',\n          mb: 3,\n          p: 2,\n          bgcolor: '#f8f9fa',\n          borderRadius: 1,\n          border: '1px solid #e0e0e0'\n        }}>\n          <TimelineIcon sx={{ color: '#9b59b6', mr: 1, fontSize: 28 }} />\n          <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n            📦 Storico Bobine - Cavi Associati\n          </Typography>\n        </Box>\n\n        {/* Lista Bobine con Accordion */}\n        <Box sx={{ mb: 3 }}>\n          {data.bobine.map((bobina) => (\n            <Paper key={bobina.id} sx={{ mb: 2, border: '1px solid #e0e0e0' }}>\n              {/* Header Bobina */}\n              <Box\n                sx={{\n                  p: 3,\n                  cursor: 'pointer',\n                  bgcolor: expandedBobine.has(bobina.id) ? '#f8f9fa' : 'white',\n                  borderBottom: expandedBobine.has(bobina.id) ? '1px solid #e0e0e0' : 'none',\n                  '&:hover': {\n                    bgcolor: '#f5f5f5'\n                  }\n                }}\n                onClick={() => toggleBobina(bobina.id)}\n              >\n                <Grid container spacing={2} alignItems=\"center\">\n                  <Grid item xs={12} sm={3}>\n                    <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                      <Box sx={{\n                        width: 8,\n                        height: 8,\n                        borderRadius: '50%',\n                        bgcolor: getStatoColor(bobina.stato),\n                        mr: 2\n                      }} />\n                      <Box>\n                        <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                          {bobina.codice}\n                        </Typography>\n                        <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                          {bobina.tipologia}\n                        </Typography>\n                      </Box>\n                    </Box>\n                  </Grid>\n\n                  <Grid item xs={12} sm={2}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                      Formazione\n                    </Typography>\n                    <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                      {bobina.formazione}\n                    </Typography>\n                  </Grid>\n\n                  <Grid item xs={12} sm={2}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                      Metri Totali\n                    </Typography>\n                    <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                      {bobina.metri_totali}m\n                    </Typography>\n                  </Grid>\n\n                  <Grid item xs={12} sm={2}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                      Utilizzati\n                    </Typography>\n                    <Typography variant=\"body1\" sx={{ fontWeight: 500, color: '#e74c3c' }}>\n                      {bobina.metri_utilizzati}m\n                    </Typography>\n                  </Grid>\n\n                  <Grid item xs={12} sm={2}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                      Residui\n                    </Typography>\n                    <Typography variant=\"body1\" sx={{ fontWeight: 500, color: '#27ae60' }}>\n                      {bobina.metri_residui}m\n                    </Typography>\n                  </Grid>\n\n                  <Grid item xs={12} sm={1}>\n                    <Box sx={{ display: 'flex', justifyContent: 'center' }}>\n                      {expandedBobine.has(bobina.id) ?\n                        <ExpandLessIcon sx={{ color: '#666' }} /> :\n                        <ExpandMoreIcon sx={{ color: '#666' }} />\n                      }\n                    </Box>\n                  </Grid>\n                </Grid>\n              </Box>\n\n              {/* Dettagli Cavi Associati */}\n              {expandedBobine.has(bobina.id) && (\n                <Box sx={{ p: 3, bgcolor: '#fafafa' }}>\n                  <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: '#2c3e50' }}>\n                    Cavi Associati ({bobina.cavi_associati.length})\n                  </Typography>\n\n                  {bobina.cavi_associati.length > 0 ? (\n                    <Grid container spacing={2}>\n                      {bobina.cavi_associati.map((cavo) => (\n                        <Grid item xs={12} sm={6} md={4} key={cavo.id}>\n                          <Paper sx={{ p: 2, border: '1px solid #e0e0e0', bgcolor: 'white' }}>\n                            <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 1 }}>\n                              {cavo.nomenclatura}\n                            </Typography>\n                            <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                              Metri utilizzati: <strong>{cavo.metri_utilizzati}m</strong>\n                            </Typography>\n                            <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                              Data posa: {new Date(cavo.data_posa).toLocaleDateString('it-IT')}\n                            </Typography>\n                          </Paper>\n                        </Grid>\n                      ))}\n                    </Grid>\n                  ) : (\n                    <Box sx={{ textAlign: 'center', p: 3, bgcolor: 'white', borderRadius: 1 }}>\n                      <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                        Nessun cavo associato a questa bobina\n                      </Typography>\n                    </Box>\n                  )}\n\n                  {/* Info aggiuntive bobina */}\n                  <Box sx={{ mt: 3, p: 2, bgcolor: 'white', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n                    <Grid container spacing={2}>\n                      <Grid item xs={12} sm={4}>\n                        <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                          Fornitore\n                        </Typography>\n                        <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                          {bobina.fornitore}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={12} sm={4}>\n                        <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                          Data Arrivo\n                        </Typography>\n                        <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                          {new Date(bobina.data_arrivo).toLocaleDateString('it-IT')}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={12} sm={4}>\n                        <Typography variant=\"body2\" sx={{ color: '#666', mb: 0.5 }}>\n                          Stato\n                        </Typography>\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Box sx={{\n                            width: 8,\n                            height: 8,\n                            borderRadius: '50%',\n                            bgcolor: getStatoColor(bobina.stato),\n                            mr: 1\n                          }} />\n                          <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                            {bobina.stato}\n                          </Typography>\n                        </Box>\n                      </Grid>\n                    </Grid>\n                  </Box>\n                </Box>\n              )}\n            </Paper>\n          ))}\n        </Box>\n\n        {/* Riepilogo */}\n        <Paper sx={{ p: 3, bgcolor: '#f8f9fa', border: '1px solid #e0e0e0' }}>\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600, color: '#2c3e50' }}>\n            📊 Riepilogo Generale\n          </Typography>\n          <Grid container spacing={3}>\n            <Grid item xs={12} sm={3}>\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                  {data.bobine.length}\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                  Bobine Totali\n                </Typography>\n              </Box>\n            </Grid>\n            <Grid item xs={12} sm={3}>\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                  {data.bobine.reduce((sum, b) => sum + b.cavi_associati.length, 0)}\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                  Cavi Associati\n                </Typography>\n              </Box>\n            </Grid>\n            <Grid item xs={12} sm={3}>\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                  {data.bobine.reduce((sum, b) => sum + b.metri_utilizzati, 0)}m\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                  Metri Utilizzati\n                </Typography>\n              </Box>\n            </Grid>\n            <Grid item xs={12} sm={3}>\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                  {data.bobine.reduce((sum, b) => sum + b.metri_residui, 0)}m\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                  Metri Residui\n                </Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </Paper>\n      </Box>\n    );\n  };\n\n\n\n\n\n  const renderPosaPeriodoReport = (data) => (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: 'warning.main' }}>\n          Report Posa per Periodo\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Statistiche Periodo - Layout Orizzontale */}\n      <Grid container spacing={4} sx={{ mb: 5 }}>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'warning.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.totale_metri_periodo}m\n            </Typography>\n            <Typography variant=\"body1\">Metri Totali</Typography>\n            <Typography variant=\"caption\">{data.data_inizio} - {data.data_fine}</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'info.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.giorni_attivi}\n            </Typography>\n            <Typography variant=\"body1\">Giorni Attivi</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'success.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.media_giornaliera}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Giorno</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {Math.round(data.totale_metri_periodo / data.giorni_attivi * 7)}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Settimana</Typography>\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 5, width: '100%' }}>\n          <TimelineChart data={data} />\n        </Box>\n      )}\n\n      {/* Posa Giornaliera */}\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n          Dettaglio Posa Giornaliera\n        </Typography>\n        <FilterableTable\n          data={data.posa_giornaliera || []}\n          columns={[\n            { field: 'data', headerName: 'Data', width: 200 },\n            { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri}m` }\n          ]}\n          pageSize={10}\n        />\n      </Paper>\n    </Box>\n  );\n\n\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {dialogType === 'posa-periodo' ? 'Report Posa per Periodo' : 'Genera Report'}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box className=\"report-main-container report-fade-in\">\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Loading indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Reports Navigation */}\n      <Box sx={{ mt: 3 }}>\n        {/* Report Navigation - Design Compatto */}\n        <Box sx={{ mb: 3 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50', mb: 2, textAlign: 'center' }}>\n            🎯 Seleziona il tipo di report\n          </Typography>\n          <Grid container spacing={2}>\n            {/* Report Avanzamento */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                className={`report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`}\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('progress')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <AssessmentIcon sx={{ fontSize: 32, color: '#3498db', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Avanzamento\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Panoramica lavori\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {/* Bill of Quantities */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('boq')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <ListIcon sx={{ fontSize: 32, color: '#8e44ad', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Bill of Quantities\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Distinta materiali\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n\n\n            {/* Storico Bobine */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'storico-bobine' ? '2px solid #9b59b6' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'storico-bobine' ? '#f8f4ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('storico-bobine')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <TimelineIcon sx={{ fontSize: 32, color: '#9b59b6', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Storico Bobine\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Cavi per bobina\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </Box>\n\n        {/* Report Content */}\n        <Box sx={{ minHeight: '400px', width: '100%' }}>\n          {/* Progress Report */}\n          {selectedReportType === 'progress' && (\n            <Paper sx={{ p: 2, borderRadius: 2 }}>\n              {reportsData.progress ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderProgressReport(reportsData.progress)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"progress\"\n                  title=\"Caricamento Report Avanzamento...\"\n                  description=\"Stiamo elaborando i dati dell'avanzamento dei lavori\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"progress\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare il report di avanzamento. Verifica la connessione e riprova.\"\n                  onRetry={() => {\n                    setLoading(true);\n                    reportService.getProgressReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          progress: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying progress report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n          {/* Bill of Quantities */}\n          {selectedReportType === 'boq' && (\n            <Paper sx={{ p: 2, borderRadius: 2 }}>\n              {reportsData.boq ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderBoqReport(reportsData.boq)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"boq\"\n                  title=\"Caricamento Bill of Quantities...\"\n                  description=\"Stiamo elaborando la distinta materiali\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"boq\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare la distinta materiali. Verifica la connessione e riprova.\"\n                  onRetry={() => {\n                    setLoading(true);\n                    reportService.getBillOfQuantities(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          boq: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying BOQ report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n          {/* Storico Bobine Report */}\n          {selectedReportType === 'storico-bobine' && (\n            <Paper sx={{ p: 2, borderRadius: 2 }}>\n              {reportsData.storicoBobine ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('storico-bobine', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('storico-bobine', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderStoricoBobineReport(reportsData.storicoBobine)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"storico-bobine\"\n                  title=\"Caricamento Storico Bobine...\"\n                  description=\"Stiamo elaborando i dati delle bobine e dei cavi associati\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"storico-bobine\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare lo storico bobine. Verifica la connessione e riprova.\"\n                  onRetry={() => loadStoricoBobine()}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n          {/* Posa per Periodo Report */}\n          {selectedReportType === 'posa-periodo' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.posaPeriodo ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderPosaPeriodoReport(reportsData.posaPeriodo)}\n                </Box>\n              ) : (\n                <EmptyState\n                  type=\"action-required\"\n                  reportType=\"posa-periodo\"\n                  title=\"Seleziona un Periodo\"\n                  description=\"Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttività del team.\"\n                  actionLabel=\"Seleziona Periodo\"\n                  onAction={() => {\n                    setDialogType('posa-periodo');\n                    // Set default date range (last month to today)\n                    const today = new Date();\n                    const lastMonth = new Date();\n                    lastMonth.setMonth(today.getMonth() - 1);\n\n                    setFormData({\n                      ...formData,\n                      data_inizio: lastMonth.toISOString().split('T')[0],\n                      data_fine: today.toISOString().split('T')[0]\n                    });\n                    setOpenDialog(true);\n                  }}\n                />\n              )}\n            </Paper>\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,0BAA0B;AACjC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EAEXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EAEPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAE5BC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EAEtBC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,UAAU,MAAM,oCAAoC;;AAG3D;AACA,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAOC,aAAa,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAAA,IAAAC,EAAA,GAAAC,YAAA;EAC9B,MAAM;IAAEC;EAAW,CAAC,GAAGjB,SAAS,CAAC,CAAC;EAElC,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuE,KAAK,EAAEC,QAAQ,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM,CAACyE,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2E,UAAU,EAAEC,aAAa,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9E,QAAQ,CAAC,UAAU,CAAC;EACxE,MAAM,CAAC+E,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAAC;IACvCiF,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtF,QAAQ,CAAC;IAC7CuF,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,eAAe,EAAE,IAAI;IACrBC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAAC6F,cAAc,EAAEC,iBAAiB,CAAC,GAAG9F,QAAQ,CAAC,IAAI+F,GAAG,CAAC,CAAC,CAAC;;EAE/D;EACA9F,SAAS,CAAC,MAAM;IACd,MAAM+F,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC1B,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAM2B,qBAAqB,GAAG,MAAM,MAAM,CAAC,sCAAsC,CAAC;;QAElF;QACA,MAAMC,eAAe,GAAG7C,aAAa,CAAC8C,iBAAiB,CAAC/B,UAAU,EAAE,OAAO,CAAC,CACzEgC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,gCAAgC,EAAE8B,GAAG,CAAC;UACpD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMC,UAAU,GAAGnD,aAAa,CAACoD,mBAAmB,CAACrC,UAAU,EAAE,OAAO,CAAC,CACtEgC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,2BAA2B,EAAE8B,GAAG,CAAC;UAC/C,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;;QAEJ;QACA,MAAMG,qBAAqB,GAAGT,qBAAqB,CAACU,OAAO,CAACC,iBAAiB,CAACxC,UAAU,CAAC,CACtFgC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,+BAA+B,EAAE8B,GAAG,CAAC;UACnD,OAAO,EAAE;QACX,CAAC,CAAC;;QAEJ;QACA,MAAM,CAACQ,YAAY,EAAEC,OAAO,EAAEC,kBAAkB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpEf,eAAe,EACfM,UAAU,EACVE,qBAAqB,CACtB,CAAC;;QAEF;QACA,IAAIG,YAAY,CAACN,OAAO,IAAIQ,kBAAkB,EAAE;UAC9C,MAAMG,UAAU,GAAGL,YAAY,CAACN,OAAO,CAACY,WAAW,IAAI,CAAC;UACxD,MAAMC,eAAe,GAAGL,kBAAkB,CAACM,MAAM;UACjD,MAAMC,yBAAyB,GAAGJ,UAAU,GAAG,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAEJ,eAAe,GAAGF,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;UAEvG;UACA,MAAMO,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;UACtC,MAAMC,kBAAkB,GAAGb,kBAAkB,CAACc,MAAM,CAACC,IAAI,IACvD,IAAIJ,IAAI,CAACI,IAAI,CAACC,mBAAmB,CAAC,CAACJ,YAAY,CAAC,CAAC,KAAKF,IACxD,CAAC,CAACJ,MAAM;UAERR,YAAY,CAACN,OAAO,CAACyB,cAAc,GAAG;YACpCC,MAAM,EAAEb,eAAe;YACvBc,WAAW,EAAEZ,yBAAyB;YACtCG,IAAI,EAAEG,kBAAkB;YACxBO,SAAS,EAAEjB,UAAU,GAAGE;UAC1B,CAAC;QACH;;QAEA;QACA9B,cAAc,CAAC;UACbC,QAAQ,EAAEsB,YAAY,CAACN,OAAO;UAC9Bf,GAAG,EAAEsB,OAAO,CAACP,OAAO;UACpBd,eAAe,EAAE,IAAI;UACrBC,aAAa,EAAE;QACjB,CAAC,CAAC;;QAEF;QACA,IAAImB,YAAY,CAACN,OAAO,IAAIO,OAAO,CAACP,OAAO,EAAE;UAC3C/B,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,MAAM;UACLA,QAAQ,CAAC,uDAAuD,CAAC;QACnE;MACF,CAAC,CAAC,OAAO6B,GAAG,EAAE;QACZ;QACAC,OAAO,CAAC/B,KAAK,CAAC,mCAAmC,EAAE8B,GAAG,CAAC;QACvD7B,QAAQ,CAAC,uDAAuD,CAAC;MACnE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIF,UAAU,EAAE;MACd4B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC5B,UAAU,CAAC,CAAC;;EAEhB;EACAnE,SAAS,CAAC,MAAM;IACd,IAAImE,UAAU,IAAIS,kBAAkB,KAAK,gBAAgB,EAAE;MACzDuD,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAAChE,UAAU,EAAES,kBAAkB,CAAC,CAAC;;EAEpC;EACA,MAAMuD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF9D,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAM+D,QAAQ,GAAG;QACfC,MAAM,EAAE,CACN;UACEC,EAAE,EAAE,CAAC;UACLC,MAAM,EAAE,QAAQ;UAChBC,SAAS,EAAE,YAAY;UACvBC,UAAU,EAAE,MAAM;UAClBC,YAAY,EAAE,GAAG;UACjBC,gBAAgB,EAAE,GAAG;UACrBC,aAAa,EAAE,GAAG;UAClBC,KAAK,EAAE,QAAQ;UACfC,WAAW,EAAE,YAAY;UACzBC,SAAS,EAAE,UAAU;UACrBC,cAAc,EAAE,CACd;YAAEV,EAAE,EAAE,GAAG;YAAEW,YAAY,EAAE,QAAQ;YAAEN,gBAAgB,EAAE,GAAG;YAAEO,SAAS,EAAE;UAAa,CAAC,EACnF;YAAEZ,EAAE,EAAE,GAAG;YAAEW,YAAY,EAAE,QAAQ;YAAEN,gBAAgB,EAAE,EAAE;YAAEO,SAAS,EAAE;UAAa,CAAC,EAClF;YAAEZ,EAAE,EAAE,GAAG;YAAEW,YAAY,EAAE,QAAQ;YAAEN,gBAAgB,EAAE,GAAG;YAAEO,SAAS,EAAE;UAAa,CAAC;QAEvF,CAAC,EACD;UACEZ,EAAE,EAAE,CAAC;UACLC,MAAM,EAAE,QAAQ;UAChBC,SAAS,EAAE,YAAY;UACvBC,UAAU,EAAE,MAAM;UAClBC,YAAY,EAAE,GAAG;UACjBC,gBAAgB,EAAE,GAAG;UACrBC,aAAa,EAAE,CAAC;UAChBC,KAAK,EAAE,UAAU;UACjBC,WAAW,EAAE,YAAY;UACzBC,SAAS,EAAE,QAAQ;UACnBC,cAAc,EAAE,CACd;YAAEV,EAAE,EAAE,GAAG;YAAEW,YAAY,EAAE,QAAQ;YAAEN,gBAAgB,EAAE,GAAG;YAAEO,SAAS,EAAE;UAAa,CAAC,EACnF;YAAEZ,EAAE,EAAE,GAAG;YAAEW,YAAY,EAAE,QAAQ;YAAEN,gBAAgB,EAAE,GAAG;YAAEO,SAAS,EAAE;UAAa,CAAC;QAEvF,CAAC,EACD;UACEZ,EAAE,EAAE,CAAC;UACLC,MAAM,EAAE,QAAQ;UAChBC,SAAS,EAAE,YAAY;UACvBC,UAAU,EAAE,MAAM;UAClBC,YAAY,EAAE,GAAG;UACjBC,gBAAgB,EAAE,GAAG;UACrBC,aAAa,EAAE,GAAG;UAClBC,KAAK,EAAE,aAAa;UACpBC,WAAW,EAAE,YAAY;UACzBC,SAAS,EAAE,UAAU;UACrBC,cAAc,EAAE,CACd;YAAEV,EAAE,EAAE,GAAG;YAAEW,YAAY,EAAE,QAAQ;YAAEN,gBAAgB,EAAE,GAAG;YAAEO,SAAS,EAAE;UAAa,CAAC;QAEvF,CAAC;MAEL,CAAC;MAED7D,cAAc,CAAC8D,IAAI,KAAK;QACtB,GAAGA,IAAI;QACP1D,aAAa,EAAE2C;MACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOhC,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,+BAA+B,EAAE8B,GAAG,CAAC;MACnD7B,QAAQ,CAAC,6CAA6C,CAAC;IACzD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+E,wBAAwB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,MAAM,KAAK;IAC7D,IAAI;MACFjF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAIgF,QAAQ;MAEZ,QAAQF,UAAU;QAChB,KAAK,UAAU;UACbE,QAAQ,GAAG,MAAMnG,aAAa,CAAC8C,iBAAiB,CAAC/B,UAAU,EAAEmF,MAAM,CAAC;UACpE;QACF,KAAK,KAAK;UACRC,QAAQ,GAAG,MAAMnG,aAAa,CAACoD,mBAAmB,CAACrC,UAAU,EAAEmF,MAAM,CAAC;UACtE;QAEF,KAAK,cAAc;UACjB,IAAI,CAACxE,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDX,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACAgF,QAAQ,GAAG,MAAMnG,aAAa,CAACoG,uBAAuB,CACpDrF,UAAU,EACVW,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClBoE,MACF,CAAC;UACD;QACF;UACE,MAAM,IAAIG,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIH,MAAM,KAAK,OAAO,EAAE;QACtB;QACA,IAAID,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,cAAc,EAAE;UACtEhE,cAAc,CAAC8D,IAAI,KAAK;YACtB,GAAGA,IAAI;YACP,CAACE,UAAU,KAAK,kBAAkB,GAAG,iBAAiB,GAAG,aAAa,GAAGE,QAAQ,CAACjD;UACpF,CAAC,CAAC,CAAC;QACL;MACF,CAAC,MAAM;QACL;QACA,IAAIiD,QAAQ,CAACG,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACG,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;IACF,CAAC,CAAC,OAAOtD,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,sCAAsC,EAAE8B,GAAG,CAAC;MAC1D7B,QAAQ,CAAC6B,GAAG,CAACyD,MAAM,IAAIzD,GAAG,CAAC0D,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACRzF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAID,MAAM0F,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMX,wBAAwB,CAAC1E,UAAU,EAAEI,QAAQ,CAACE,OAAO,CAAC;IAC5DP,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMuF,iBAAiB,GAAGA,CAAA,KAAM;IAC9BvF,aAAa,CAAC,KAAK,CAAC;IACpBF,QAAQ,CAAC,IAAI,CAAC;IACdQ,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAID,MAAM8E,oBAAoB,GAAIC,IAAI,iBAChCtG,OAAA,CAAC3D,GAAG;IAAAkK,QAAA,gBAEFvG,OAAA,CAAC3D,GAAG;MAACmK,EAAE,EAAE;QACPC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE;MACV,CAAE;MAAAT,QAAA,gBACAvG,OAAA,CAAC1D,UAAU;QAAC2K,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEU,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAZ,QAAA,EAAC;MAEpE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvH,OAAA,CAACnC,gBAAgB;QACf2J,OAAO,eACLxH,OAAA,CAACpC,MAAM;UACL6J,OAAO,EAAE3F,UAAW;UACpB4F,QAAQ,EAAGC,CAAC,IAAK5F,aAAa,CAAC4F,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDN,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDM,KAAK,eACH7H,OAAA,CAAC3D,GAAG;UAACmK,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAJ,QAAA,gBACjDvG,OAAA,CAACX,aAAa;YAACmH,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNvH,OAAA,CAACxD,IAAI;MAACuL,SAAS;MAACC,OAAO,EAAE,CAAE;MAACxB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxCvG,OAAA,CAACxD,IAAI;QAACyL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9BvG,OAAA,CAACL,UAAU;UACT0I,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEhC,IAAI,CAACxB,YAAa;UACzByD,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAC,oCAAoC;UAC7CC,QAAQ,EAAC,mDAAmD;UAC5DC,IAAI,EAAC;QAAO;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPvH,OAAA,CAACxD,IAAI;QAACyL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9BvG,OAAA,CAACL,UAAU;UACT0I,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEhC,IAAI,CAACqC,YAAa;UACzBJ,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAE,GAAGlC,IAAI,CAACsC,uBAAuB,cAAe;UACxDH,QAAQ,EAAC,mDAAmD;UAC5D/G,QAAQ,EAAE4E,IAAI,CAACsC,uBAAwB;UACvCC,KAAK,EAAEvC,IAAI,CAACsC,uBAAuB,GAAG,EAAE,GAAG,IAAI,GAAGtC,IAAI,CAACsC,uBAAuB,GAAG,EAAE,GAAG,MAAM,GAAG,MAAO;UACtGE,UAAU,EAAE,GAAGxC,IAAI,CAACsC,uBAAuB,GAAI;UAC/CF,IAAI,EAAC;QAAO;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPvH,OAAA,CAACxD,IAAI;QAACyL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9BvG,OAAA,CAACL,UAAU;UACT0I,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAEhC,IAAI,CAACyC,eAAgB;UAC5BR,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAE,GAAG,CAAC,GAAG,GAAGlC,IAAI,CAACsC,uBAAuB,EAAEI,OAAO,CAAC,CAAC,CAAC,iBAAkB;UAC9EP,QAAQ,EAAC,mDAAmD;UAC5DC,IAAI,EAAC;QAAO;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPvH,OAAA,CAACxD,IAAI;QAACyL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA7B,QAAA,eAC9BvG,OAAA,CAACL,UAAU;UACT0I,KAAK,EAAC,cAAc;UACpBC,KAAK,EAAEhC,IAAI,CAAC2C,iBAAiB,IAAI,CAAE;UACnCV,IAAI,EAAC,GAAG;UACRC,QAAQ,EACNlC,IAAI,CAAC4C,cAAc,GACf,GAAG5C,IAAI,CAAC4C,cAAc,4BAA4B,GACjD5C,IAAI,CAAC2C,iBAAiB,GAAG,CAAC,GACvB,kBAAkB,GAClB,sBACT;UACDR,QAAQ,EAAC,mDAAmD;UAC5DC,IAAI,EAAC,OAAO;UACZS,OAAO,EACL7C,IAAI,CAAC8C,2BAA2B,GAC5B,gBAAgB9C,IAAI,CAAC8C,2BAA2B,oFAAoF,GACpI;QACL;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGNzF,UAAU,iBACT9B,OAAA,CAAC3D,GAAG;MAACmK,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACjBvG,OAAA,CAACJ,aAAa;QAAC0G,IAAI,EAAEA;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDvH,OAAA,CAACxD,IAAI;MAACuL,SAAS;MAACC,OAAO,EAAE,CAAE;MAACxB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxCvG,OAAA,CAACxD,IAAI;QAACyL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBvG,OAAA,CAACvD,IAAI;UAAC+J,EAAE,EAAE;YAAE6C,MAAM,EAAE,MAAM;YAAErC,MAAM,EAAE;UAAoB,CAAE;UAAAT,QAAA,eACxDvG,OAAA,CAACtD,WAAW;YAAC8J,EAAE,EAAE;cAAEK,CAAC,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACxBvG,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBACxDvG,OAAA,CAACnB,SAAS;gBAAC2H,EAAE,EAAE;kBAAEW,KAAK,EAAE,SAAS;kBAAEW,EAAE,EAAE,CAAC;kBAAEwB,QAAQ,EAAE;gBAAG;cAAE;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5DvH,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,EAAC;cAEpE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNvH,OAAA,CAACxD,IAAI;cAACuL,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAzB,QAAA,gBACzBvG,OAAA,CAACxD,IAAI;gBAACyL,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAA3B,QAAA,eACfvG,OAAA,CAAC3D,GAAG;kBAACmK,EAAE,EAAE;oBAAE+C,SAAS,EAAE,QAAQ;oBAAE1C,CAAC,EAAE,CAAC;oBAAEC,OAAO,EAAE,SAAS;oBAAEC,YAAY,EAAE;kBAAE,CAAE;kBAAAR,QAAA,gBAC1EvG,OAAA,CAAC1D,UAAU;oBAAC2K,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEU,UAAU,EAAE,GAAG;sBAAEC,KAAK,EAAE,SAAS;sBAAEP,EAAE,EAAE;oBAAE,CAAE;oBAAAL,QAAA,EACvED,IAAI,CAAChD;kBAAW;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACbvH,OAAA,CAAC1D,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE;oBAAO,CAAE;oBAAAZ,QAAA,EAAC;kBAEnD;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACPvH,OAAA,CAACxD,IAAI;gBAACyL,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAA3B,QAAA,eACfvG,OAAA,CAAC3D,GAAG;kBAACmK,EAAE,EAAE;oBAAE+C,SAAS,EAAE,QAAQ;oBAAE1C,CAAC,EAAE,CAAC;oBAAEC,OAAO,EAAE,SAAS;oBAAEC,YAAY,EAAE;kBAAE,CAAE;kBAAAR,QAAA,gBAC1EvG,OAAA,CAAC1D,UAAU;oBAAC2K,OAAO,EAAC,IAAI;oBAACT,EAAE,EAAE;sBAAEU,UAAU,EAAE,GAAG;sBAAEC,KAAK,EAAE,SAAS;sBAAEP,EAAE,EAAE;oBAAE,CAAE;oBAAAL,QAAA,EACvED,IAAI,CAACkD;kBAAW;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACbvH,OAAA,CAAC1D,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE;oBAAO,CAAE;oBAAAZ,QAAA,GAAC,eACpC,EAACD,IAAI,CAACmD,gBAAgB,EAAC,IACtC;kBAAA;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACPvH,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBAAEkD,EAAE,EAAE;cAAE,CAAE;cAAAnD,QAAA,gBACjBvG,OAAA,CAAC3D,GAAG;gBAACmK,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACnEvG,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,OAAO;kBAAAV,QAAA,EAAC;gBAAS;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClDvH,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE;kBAAI,CAAE;kBAAAX,QAAA,GACjDD,IAAI,CAACmD,gBAAgB,EAAC,GACzB;gBAAA;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvH,OAAA,CAAC3D,GAAG;gBAACmK,EAAE,EAAE;kBACPmD,KAAK,EAAE,MAAM;kBACbN,MAAM,EAAE,CAAC;kBACTvC,OAAO,EAAE,SAAS;kBAClBC,YAAY,EAAE,CAAC;kBACf6C,QAAQ,EAAE;gBACZ,CAAE;gBAAArD,QAAA,eACAvG,OAAA,CAAC3D,GAAG;kBAACmK,EAAE,EAAE;oBACPmD,KAAK,EAAE,GAAGrD,IAAI,CAACmD,gBAAgB,GAAG;oBAClCJ,MAAM,EAAE,MAAM;oBACdvC,OAAO,EAAE,SAAS;oBAClB+C,UAAU,EAAE;kBACd;gBAAE;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPvH,OAAA,CAACxD,IAAI;QAACyL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBvG,OAAA,CAACvD,IAAI;UAAC+J,EAAE,EAAE;YAAE6C,MAAM,EAAE,MAAM;YAAErC,MAAM,EAAE;UAAoB,CAAE;UAAAT,QAAA,eACxDvG,OAAA,CAACtD,WAAW;YAAC8J,EAAE,EAAE;cAAEK,CAAC,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACxBvG,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBACxDvG,OAAA,CAAC3D,GAAG;gBAACmK,EAAE,EAAE;kBACPM,OAAO,EAAE,SAAS;kBAClBC,YAAY,EAAE,KAAK;kBACnBF,CAAC,EAAE,CAAC;kBACJiB,EAAE,EAAE,CAAC;kBACLrB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE;gBAClB,CAAE;gBAAAH,QAAA,eACAvG,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,IAAI;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,OAAO;oBAAED,UAAU,EAAE;kBAAO,CAAE;kBAAAX,QAAA,EAAC;gBAErE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNvH,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,EAAC;cAEpE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAELjB,IAAI,CAACnC,cAAc,gBAClBnE,OAAA,CAAAE,SAAA;cAAAqG,QAAA,gBACEvG,OAAA,CAACxD,IAAI;gBAACuL,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAACxB,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACxCvG,OAAA,CAACxD,IAAI;kBAACyL,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAA3B,QAAA,eACfvG,OAAA,CAAC3D,GAAG;oBAACmK,EAAE,EAAE;sBAAE+C,SAAS,EAAE,QAAQ;sBAAE1C,CAAC,EAAE,CAAC;sBAAEC,OAAO,EAAE,SAAS;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAAAR,QAAA,gBAC1EvG,OAAA,CAAC1D,UAAU;sBAAC2K,OAAO,EAAC,IAAI;sBAACT,EAAE,EAAE;wBAAEU,UAAU,EAAE,GAAG;wBAAEC,KAAK,EAAE,SAAS;wBAAEP,EAAE,EAAE;sBAAE,CAAE;sBAAAL,QAAA,EACvED,IAAI,CAACnC,cAAc,CAACC;oBAAM;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,eACbvH,OAAA,CAAC1D,UAAU;sBAAC2K,OAAO,EAAC,OAAO;sBAACT,EAAE,EAAE;wBAAEW,KAAK,EAAE;sBAAO,CAAE;sBAAAZ,QAAA,EAAC;oBAEnD;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAEPvH,OAAA,CAACxD,IAAI;kBAACyL,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAA3B,QAAA,eACfvG,OAAA,CAAC3D,GAAG;oBAACmK,EAAE,EAAE;sBAAE+C,SAAS,EAAE,QAAQ;sBAAE1C,CAAC,EAAE,CAAC;sBAAEC,OAAO,EAAE,SAAS;sBAAEC,YAAY,EAAE;oBAAE,CAAE;oBAAAR,QAAA,gBAC1EvG,OAAA,CAAC1D,UAAU;sBAAC2K,OAAO,EAAC,IAAI;sBAACT,EAAE,EAAE;wBAAEU,UAAU,EAAE,GAAG;wBAAEC,KAAK,EAAE,SAAS;wBAAEP,EAAE,EAAE;sBAAE,CAAE;sBAAAL,QAAA,EACvED,IAAI,CAACnC,cAAc,CAACG;oBAAS;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC,eACbvH,OAAA,CAAC1D,UAAU;sBAAC2K,OAAO,EAAC,OAAO;sBAACT,EAAE,EAAE;wBAAEW,KAAK,EAAE;sBAAO,CAAE;sBAAAZ,QAAA,EAAC;oBAEnD;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEPvH,OAAA,CAAC3D,GAAG;gBAACmK,EAAE,EAAE;kBAAE+C,SAAS,EAAE,QAAQ;kBAAE3C,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,gBACtCvG,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,IAAI;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE,CAAE;kBAAAL,QAAA,GACvED,IAAI,CAACnC,cAAc,CAACE,WAAW,EAAC,GACnC;gBAAA;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEP,EAAE,EAAE;kBAAE,CAAE;kBAAAL,QAAA,EAAC;gBAE1D;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,SAAS;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEmC,QAAQ,EAAE;kBAAU,CAAE;kBAAA/C,QAAA,GACtED,IAAI,CAACnC,cAAc,CAACP,IAAI,EAAC,iCAC5B;gBAAA;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNvH,OAAA,CAAC3D,GAAG;gBAACmK,EAAE,EAAE;kBACPmD,KAAK,EAAE,MAAM;kBACbN,MAAM,EAAE,CAAC;kBACTvC,OAAO,EAAE,SAAS;kBAClBC,YAAY,EAAE,CAAC;kBACf6C,QAAQ,EAAE;gBACZ,CAAE;gBAAArD,QAAA,eACAvG,OAAA,CAAC3D,GAAG;kBAACmK,EAAE,EAAE;oBACPmD,KAAK,EAAE,GAAGrD,IAAI,CAACnC,cAAc,CAACE,WAAW,GAAG;oBAC5CgF,MAAM,EAAE,MAAM;oBACdvC,OAAO,EAAE,SAAS;oBAClB+C,UAAU,EAAE;kBACd;gBAAE;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA,eACN,CAAC,gBAEHvH,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBAAE+C,SAAS,EAAE,QAAQ;gBAAE1C,CAAC,EAAE,CAAC;gBAAEC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAR,QAAA,eAC1EvG,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEnD;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAKNjB,IAAI,CAACwD,YAAY,IAAIxD,IAAI,CAACwD,YAAY,CAACtG,MAAM,GAAG,CAAC,iBAChDxD,OAAA,CAACvD,IAAI;MAAC+J,EAAE,EAAE;QAAEQ,MAAM,EAAE;MAAoB,CAAE;MAAAT,QAAA,eACxCvG,OAAA,CAACtD,WAAW;QAAC8J,EAAE,EAAE;UAAEK,CAAC,EAAE;QAAE,CAAE;QAAAN,QAAA,gBACxBvG,OAAA,CAAC3D,GAAG;UAACmK,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAL,QAAA,gBACxDvG,OAAA,CAACrB,aAAa;YAAC6H,EAAE,EAAE;cAAEW,KAAK,EAAE,SAAS;cAAEW,EAAE,EAAE,CAAC;cAAEwB,QAAQ,EAAE;YAAG;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChEvH,OAAA,CAAC1D,UAAU;YAAC2K,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAZ,QAAA,EAAC;UAEpE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNvH,OAAA,CAACxD,IAAI;UAACuL,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzB,QAAA,EACxBD,IAAI,CAACwD,YAAY,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7ClK,OAAA,CAACxD,IAAI;YAACyL,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC9BvG,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBACPK,CAAC,EAAE,CAAC;gBACJG,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,CAAC;gBACfD,OAAO,EAAEoD,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;gBAC5CL,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE;kBACT/C,OAAO,EAAE,SAAS;kBAClBqD,SAAS,EAAE,kBAAkB;kBAC7BC,SAAS,EAAE;gBACb;cACF,CAAE;cAAA7D,QAAA,gBACAvG,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE,MAAM;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,EACtD0D,IAAI,CAAC3D;cAAI;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACbvH,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,GAChE0D,IAAI,CAACI,KAAK,EAAC,GACd;cAAA;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZ2C,KAAK,KAAK,CAAC,iBACVlK,OAAA,CAACpD,IAAI;gBACHiL,KAAK,EAAC,gBAAa;gBACnBa,IAAI,EAAC,OAAO;gBACZlC,EAAE,EAAE;kBACFkD,EAAE,EAAE,CAAC;kBACL5C,OAAO,EAAE,SAAS;kBAClBK,KAAK,EAAE,OAAO;kBACdmC,QAAQ,EAAE;gBACZ;cAAE;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GA/B8B2C,KAAK;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGNjB,IAAI,CAACwD,YAAY,CAACtG,MAAM,GAAG,CAAC,iBAC3BxD,OAAA,CAAC3D,GAAG;UAACmK,EAAE,EAAE;YAAEkD,EAAE,EAAE,CAAC;YAAEH,SAAS,EAAE;UAAS,CAAE;UAAAhD,QAAA,eACtCvG,OAAA,CAACvC,SAAS;YAAA8I,QAAA,gBACRvG,OAAA,CAACtC,gBAAgB;cAAC4M,UAAU,eAAEtK,OAAA,CAACf,cAAc;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAhB,QAAA,eAC/CvG,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,GAAC,iBACrC,EAACD,IAAI,CAACwD,YAAY,CAACtG,MAAM,EAAC,SAC3C;cAAA;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACnBvH,OAAA,CAACrC,gBAAgB;cAAA4I,QAAA,eACfvG,OAAA,CAACP,eAAe;gBACd6G,IAAI,EAAEA,IAAI,CAACwD,YAAY,CAACE,GAAG,CAACC,IAAI,KAAK;kBACnC3D,IAAI,EAAE2D,IAAI,CAAC3D,IAAI;kBACf+D,KAAK,EAAE,GAAGJ,IAAI,CAACI,KAAK;gBACtB,CAAC,CAAC,CAAE;gBACJE,OAAO,EAAE,CACP;kBAAEC,KAAK,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEd,KAAK,EAAE;gBAAI,CAAC,EACjD;kBAAEa,KAAK,EAAE,OAAO;kBAAEC,UAAU,EAAE,cAAc;kBAAEd,KAAK,EAAE,GAAG;kBAAEe,KAAK,EAAE;gBAAQ,CAAC,CAC1E;gBACFC,UAAU,EAAE,IAAK;gBACjBC,QAAQ,EAAE;cAAG;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMsD,eAAe,GAAIvE,IAAI,iBAC3BtG,OAAA,CAAC3D,GAAG;IAAAkK,QAAA,gBAEFvG,OAAA,CAAC3D,GAAG;MAACmK,EAAE,EAAE;QACPC,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJC,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE;MACV,CAAE;MAAAT,QAAA,gBACAvG,OAAA,CAAC7B,QAAQ;QAACqI,EAAE,EAAE;UAAEW,KAAK,EAAE,SAAS;UAAEW,EAAE,EAAE,CAAC;UAAEwB,QAAQ,EAAE;QAAG;MAAE;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DvH,OAAA,CAAC1D,UAAU;QAAC2K,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEU,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAZ,QAAA,EAAC;MAEpE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGLzF,UAAU,iBACT9B,OAAA,CAAC3D,GAAG;MAACmK,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACjBvG,OAAA,CAACH,QAAQ;QAACyG,IAAI,EAAEA;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGE,CACN;EACD,MAAMuD,yBAAyB,GAAIxE,IAAI,IAAK;IAAAjG,EAAA;IAC1C,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9F,QAAQ,CAAC,IAAI+F,GAAG,CAAC,CAAC,CAAC;IAE/D,MAAM6I,YAAY,GAAIC,QAAQ,IAAK;MACjC,MAAMC,WAAW,GAAG,IAAI/I,GAAG,CAACF,cAAc,CAAC;MAC3C,IAAIiJ,WAAW,CAACC,GAAG,CAACF,QAAQ,CAAC,EAAE;QAC7BC,WAAW,CAACE,MAAM,CAACH,QAAQ,CAAC;MAC9B,CAAC,MAAM;QACLC,WAAW,CAACG,GAAG,CAACJ,QAAQ,CAAC;MAC3B;MACA/I,iBAAiB,CAACgJ,WAAW,CAAC;IAChC,CAAC;IAED,MAAMI,aAAa,GAAIpG,KAAK,IAAK;MAC/B,QAAQA,KAAK;QACX,KAAK,aAAa;UAAE,OAAO,SAAS;QACpC,KAAK,QAAQ;UAAE,OAAO,SAAS;QAC/B,KAAK,UAAU;UAAE,OAAO,SAAS;QACjC;UAAS,OAAO,SAAS;MAC3B;IACF,CAAC;IAED,oBACEjF,OAAA,CAAC3D,GAAG;MAAAkK,QAAA,gBAEFvG,OAAA,CAAC3D,GAAG;QAACmK,EAAE,EAAE;UACPC,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBC,EAAE,EAAE,CAAC;UACLC,CAAC,EAAE,CAAC;UACJC,OAAO,EAAE,SAAS;UAClBC,YAAY,EAAE,CAAC;UACfC,MAAM,EAAE;QACV,CAAE;QAAAT,QAAA,gBACAvG,OAAA,CAAC/B,YAAY;UAACuI,EAAE,EAAE;YAAEW,KAAK,EAAE,SAAS;YAAEW,EAAE,EAAE,CAAC;YAAEwB,QAAQ,EAAE;UAAG;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/DvH,OAAA,CAAC1D,UAAU;UAAC2K,OAAO,EAAC,IAAI;UAACT,EAAE,EAAE;YAAEU,UAAU,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAZ,QAAA,EAAC;QAEpE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNvH,OAAA,CAAC3D,GAAG;QAACmK,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,EAChBD,IAAI,CAAC7B,MAAM,CAACuF,GAAG,CAAEsB,MAAM,iBACtBtL,OAAA,CAACzD,KAAK;UAAiBiK,EAAE,EAAE;YAAEI,EAAE,EAAE,CAAC;YAAEI,MAAM,EAAE;UAAoB,CAAE;UAAAT,QAAA,gBAEhEvG,OAAA,CAAC3D,GAAG;YACFmK,EAAE,EAAE;cACFK,CAAC,EAAE,CAAC;cACJ0E,MAAM,EAAE,SAAS;cACjBzE,OAAO,EAAE9E,cAAc,CAACkJ,GAAG,CAACI,MAAM,CAAC5G,EAAE,CAAC,GAAG,SAAS,GAAG,OAAO;cAC5D8G,YAAY,EAAExJ,cAAc,CAACkJ,GAAG,CAACI,MAAM,CAAC5G,EAAE,CAAC,GAAG,mBAAmB,GAAG,MAAM;cAC1E,SAAS,EAAE;gBACToC,OAAO,EAAE;cACX;YACF,CAAE;YACF2E,OAAO,EAAEA,CAAA,KAAMV,YAAY,CAACO,MAAM,CAAC5G,EAAE,CAAE;YAAA6B,QAAA,eAEvCvG,OAAA,CAACxD,IAAI;cAACuL,SAAS;cAACC,OAAO,EAAE,CAAE;cAACrB,UAAU,EAAC,QAAQ;cAAAJ,QAAA,gBAC7CvG,OAAA,CAACxD,IAAI;gBAACyL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,eACvBvG,OAAA,CAAC3D,GAAG;kBAACmK,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAJ,QAAA,gBACjDvG,OAAA,CAAC3D,GAAG;oBAACmK,EAAE,EAAE;sBACPmD,KAAK,EAAE,CAAC;sBACRN,MAAM,EAAE,CAAC;sBACTtC,YAAY,EAAE,KAAK;sBACnBD,OAAO,EAAEuE,aAAa,CAACC,MAAM,CAACrG,KAAK,CAAC;sBACpC6C,EAAE,EAAE;oBACN;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACLvH,OAAA,CAAC3D,GAAG;oBAAAkK,QAAA,gBACFvG,OAAA,CAAC1D,UAAU;sBAAC2K,OAAO,EAAC,IAAI;sBAACT,EAAE,EAAE;wBAAEU,UAAU,EAAE,GAAG;wBAAEC,KAAK,EAAE;sBAAU,CAAE;sBAAAZ,QAAA,EAChE+E,MAAM,CAAC3G;oBAAM;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACbvH,OAAA,CAAC1D,UAAU;sBAAC2K,OAAO,EAAC,OAAO;sBAACT,EAAE,EAAE;wBAAEW,KAAK,EAAE;sBAAO,CAAE;sBAAAZ,QAAA,EAC/C+E,MAAM,CAAC1G;oBAAS;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAEPvH,OAAA,CAACxD,IAAI;gBAACyL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,gBACvBvG,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEP,EAAE,EAAE;kBAAI,CAAE;kBAAAL,QAAA,EAAC;gBAE5D;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE;kBAAI,CAAE;kBAAAX,QAAA,EACjD+E,MAAM,CAACzG;gBAAU;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAEPvH,OAAA,CAACxD,IAAI;gBAACyL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,gBACvBvG,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEP,EAAE,EAAE;kBAAI,CAAE;kBAAAL,QAAA,EAAC;gBAE5D;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE;kBAAI,CAAE;kBAAAX,QAAA,GACjD+E,MAAM,CAACxG,YAAY,EAAC,GACvB;gBAAA;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAEPvH,OAAA,CAACxD,IAAI;gBAACyL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,gBACvBvG,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEP,EAAE,EAAE;kBAAI,CAAE;kBAAAL,QAAA,EAAC;gBAE5D;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAZ,QAAA,GACnE+E,MAAM,CAACvG,gBAAgB,EAAC,GAC3B;gBAAA;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAEPvH,OAAA,CAACxD,IAAI;gBAACyL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,gBACvBvG,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEP,EAAE,EAAE;kBAAI,CAAE;kBAAAL,QAAA,EAAC;gBAE5D;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAZ,QAAA,GACnE+E,MAAM,CAACtG,aAAa,EAAC,GACxB;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAEPvH,OAAA,CAACxD,IAAI;gBAACyL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA5B,QAAA,eACvBvG,OAAA,CAAC3D,GAAG;kBAACmK,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE;kBAAS,CAAE;kBAAAH,QAAA,EACpDvE,cAAc,CAACkJ,GAAG,CAACI,MAAM,CAAC5G,EAAE,CAAC,gBAC5B1E,OAAA,CAACb,cAAc;oBAACqH,EAAE,EAAE;sBAAEW,KAAK,EAAE;oBAAO;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBACzCvH,OAAA,CAACf,cAAc;oBAACuH,EAAE,EAAE;sBAAEW,KAAK,EAAE;oBAAO;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAExC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAGLvF,cAAc,CAACkJ,GAAG,CAACI,MAAM,CAAC5G,EAAE,CAAC,iBAC5B1E,OAAA,CAAC3D,GAAG;YAACmK,EAAE,EAAE;cAAEK,CAAC,EAAE,CAAC;cAAEC,OAAO,EAAE;YAAU,CAAE;YAAAP,QAAA,gBACpCvG,OAAA,CAAC1D,UAAU;cAAC2K,OAAO,EAAC,IAAI;cAACT,EAAE,EAAE;gBAAEI,EAAE,EAAE,CAAC;gBAAEM,UAAU,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAU,CAAE;cAAAZ,QAAA,GAAC,kBACzD,EAAC+E,MAAM,CAAClG,cAAc,CAAC5B,MAAM,EAAC,GAChD;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEZ+D,MAAM,CAAClG,cAAc,CAAC5B,MAAM,GAAG,CAAC,gBAC/BxD,OAAA,CAACxD,IAAI;cAACuL,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAzB,QAAA,EACxB+E,MAAM,CAAClG,cAAc,CAAC4E,GAAG,CAAE0B,IAAI,iBAC9B1L,OAAA,CAACxD,IAAI;gBAACyL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAACC,EAAE,EAAE,CAAE;gBAAA7B,QAAA,eAC9BvG,OAAA,CAACzD,KAAK;kBAACiK,EAAE,EAAE;oBAAEK,CAAC,EAAE,CAAC;oBAAEG,MAAM,EAAE,mBAAmB;oBAAEF,OAAO,EAAE;kBAAQ,CAAE;kBAAAP,QAAA,gBACjEvG,OAAA,CAAC1D,UAAU;oBAAC2K,OAAO,EAAC,WAAW;oBAACT,EAAE,EAAE;sBAAEU,UAAU,EAAE,GAAG;sBAAEN,EAAE,EAAE;oBAAE,CAAE;oBAAAL,QAAA,EAC5DmF,IAAI,CAACrG;kBAAY;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACbvH,OAAA,CAAC1D,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE,MAAM;sBAAEP,EAAE,EAAE;oBAAI,CAAE;oBAAAL,QAAA,GAAC,oBACxC,eAAAvG,OAAA;sBAAAuG,QAAA,GAASmF,IAAI,CAAC3G,gBAAgB,EAAC,GAAC;oBAAA;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACbvH,OAAA,CAAC1D,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE;oBAAO,CAAE;oBAAAZ,QAAA,GAAC,aACtC,EAAC,IAAI1C,IAAI,CAAC6H,IAAI,CAACpG,SAAS,CAAC,CAACqG,kBAAkB,CAAC,OAAO,CAAC;kBAAA;oBAAAvE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC,GAX4BmE,IAAI,CAAChH,EAAE;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYvC,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAEPvH,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBAAE+C,SAAS,EAAE,QAAQ;gBAAE1C,CAAC,EAAE,CAAC;gBAAEC,OAAO,EAAE,OAAO;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAR,QAAA,eACxEvG,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEnD;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN,eAGDvH,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBAAEkD,EAAE,EAAE,CAAC;gBAAE7C,CAAC,EAAE,CAAC;gBAAEC,OAAO,EAAE,OAAO;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAoB,CAAE;cAAAT,QAAA,eACvFvG,OAAA,CAACxD,IAAI;gBAACuL,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAzB,QAAA,gBACzBvG,OAAA,CAACxD,IAAI;kBAACyL,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAAA5B,QAAA,gBACvBvG,OAAA,CAAC1D,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE,MAAM;sBAAEP,EAAE,EAAE;oBAAI,CAAE;oBAAAL,QAAA,EAAC;kBAE5D;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEU,UAAU,EAAE;oBAAI,CAAE;oBAAAX,QAAA,EACjD+E,MAAM,CAACnG;kBAAS;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACPvH,OAAA,CAACxD,IAAI;kBAACyL,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAAA5B,QAAA,gBACvBvG,OAAA,CAAC1D,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE,MAAM;sBAAEP,EAAE,EAAE;oBAAI,CAAE;oBAAAL,QAAA,EAAC;kBAE5D;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEU,UAAU,EAAE;oBAAI,CAAE;oBAAAX,QAAA,EACjD,IAAI1C,IAAI,CAACyH,MAAM,CAACpG,WAAW,CAAC,CAACyG,kBAAkB,CAAC,OAAO;kBAAC;oBAAAvE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACPvH,OAAA,CAACxD,IAAI;kBAACyL,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAAA5B,QAAA,gBACvBvG,OAAA,CAAC1D,UAAU;oBAAC2K,OAAO,EAAC,OAAO;oBAACT,EAAE,EAAE;sBAAEW,KAAK,EAAE,MAAM;sBAAEP,EAAE,EAAE;oBAAI,CAAE;oBAAAL,QAAA,EAAC;kBAE5D;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbvH,OAAA,CAAC3D,GAAG;oBAACmK,EAAE,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE;oBAAS,CAAE;oBAAAJ,QAAA,gBACjDvG,OAAA,CAAC3D,GAAG;sBAACmK,EAAE,EAAE;wBACPmD,KAAK,EAAE,CAAC;wBACRN,MAAM,EAAE,CAAC;wBACTtC,YAAY,EAAE,KAAK;wBACnBD,OAAO,EAAEuE,aAAa,CAACC,MAAM,CAACrG,KAAK,CAAC;wBACpC6C,EAAE,EAAE;sBACN;oBAAE;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACLvH,OAAA,CAAC1D,UAAU;sBAAC2K,OAAO,EAAC,OAAO;sBAACT,EAAE,EAAE;wBAAEU,UAAU,EAAE;sBAAI,CAAE;sBAAAX,QAAA,EACjD+E,MAAM,CAACrG;oBAAK;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA,GA1JS+D,MAAM,CAAC5G,EAAE;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2Jd,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNvH,OAAA,CAACzD,KAAK;QAACiK,EAAE,EAAE;UAAEK,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE,SAAS;UAAEE,MAAM,EAAE;QAAoB,CAAE;QAAAT,QAAA,gBACnEvG,OAAA,CAAC1D,UAAU;UAAC2K,OAAO,EAAC,IAAI;UAACT,EAAE,EAAE;YAAEI,EAAE,EAAE,CAAC;YAAEM,UAAU,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAZ,QAAA,EAAC;QAE3E;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvH,OAAA,CAACxD,IAAI;UAACuL,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzB,QAAA,gBACzBvG,OAAA,CAACxD,IAAI;YAACyL,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eACvBvG,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBAAE+C,SAAS,EAAE;cAAS,CAAE;cAAAhD,QAAA,gBAC/BvG,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE,SAAS;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,EACvED,IAAI,CAAC7B,MAAM,CAACjB;cAAM;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACbvH,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEnD;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPvH,OAAA,CAACxD,IAAI;YAACyL,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eACvBvG,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBAAE+C,SAAS,EAAE;cAAS,CAAE;cAAAhD,QAAA,gBAC/BvG,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE,SAAS;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,EACvED,IAAI,CAAC7B,MAAM,CAACmH,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAAC1G,cAAc,CAAC5B,MAAM,EAAE,CAAC;cAAC;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACbvH,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEnD;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPvH,OAAA,CAACxD,IAAI;YAACyL,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eACvBvG,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBAAE+C,SAAS,EAAE;cAAS,CAAE;cAAAhD,QAAA,gBAC/BvG,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE,SAAS;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,GACvED,IAAI,CAAC7B,MAAM,CAACmH,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAAC/G,gBAAgB,EAAE,CAAC,CAAC,EAAC,GAC/D;cAAA;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEnD;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACPvH,OAAA,CAACxD,IAAI;YAACyL,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eACvBvG,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBAAE+C,SAAS,EAAE;cAAS,CAAE;cAAAhD,QAAA,gBAC/BvG,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,IAAI;gBAACT,EAAE,EAAE;kBAAEU,UAAU,EAAE,GAAG;kBAAEC,KAAK,EAAE,SAAS;kBAAEP,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,GACvED,IAAI,CAAC7B,MAAM,CAACmH,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAAC9G,aAAa,EAAE,CAAC,CAAC,EAAC,GAC5D;cAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;gBAAC2K,OAAO,EAAC,OAAO;gBAACT,EAAE,EAAE;kBAAEW,KAAK,EAAE;gBAAO,CAAE;gBAAAZ,QAAA,EAAC;cAEnD;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAAClH,EAAA,CA5PIyK,yBAAyB;EAkQ/B,MAAMiB,uBAAuB,GAAIzF,IAAI,iBACnCtG,OAAA,CAAC3D,GAAG;IAAAkK,QAAA,gBAEFvG,OAAA,CAAC3D,GAAG;MAACmK,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACzFvG,OAAA,CAAC1D,UAAU;QAAC2K,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEU,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAZ,QAAA,EAAC;MAEzE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvH,OAAA,CAACnC,gBAAgB;QACf2J,OAAO,eACLxH,OAAA,CAACpC,MAAM;UACL6J,OAAO,EAAE3F,UAAW;UACpB4F,QAAQ,EAAGC,CAAC,IAAK5F,aAAa,CAAC4F,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDN,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDM,KAAK,eACH7H,OAAA,CAAC3D,GAAG;UAACmK,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAJ,QAAA,gBACjDvG,OAAA,CAACX,aAAa;YAACmH,EAAE,EAAE;cAAEsB,EAAE,EAAE;YAAE;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNvH,OAAA,CAACxD,IAAI;MAACuL,SAAS;MAACC,OAAO,EAAE,CAAE;MAACxB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxCvG,OAAA,CAACxD,IAAI;QAACyL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBvG,OAAA,CAACzD,KAAK;UAACiK,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE0C,SAAS,EAAE,QAAQ;YAAEzC,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAChFvG,OAAA,CAAC1D,UAAU;YAAC2K,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,GACxDD,IAAI,CAAC0F,oBAAoB,EAAC,GAC7B;UAAA;YAAA5E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;YAAC2K,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAY;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrDvH,OAAA,CAAC1D,UAAU;YAAC2K,OAAO,EAAC,SAAS;YAAAV,QAAA,GAAED,IAAI,CAACjF,WAAW,EAAC,KAAG,EAACiF,IAAI,CAAChF,SAAS;UAAA;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPvH,OAAA,CAACxD,IAAI;QAACyL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBvG,OAAA,CAACzD,KAAK;UAACiK,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE0C,SAAS,EAAE,QAAQ;YAAEzC,OAAO,EAAE,WAAW;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAC7EvG,OAAA,CAAC1D,UAAU;YAAC2K,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,EACxDD,IAAI,CAAC2F;UAAa;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACbvH,OAAA,CAAC1D,UAAU;YAAC2K,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAa;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPvH,OAAA,CAACxD,IAAI;QAACyL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBvG,OAAA,CAACzD,KAAK;UAACiK,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE0C,SAAS,EAAE,QAAQ;YAAEzC,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAChFvG,OAAA,CAAC1D,UAAU;YAAC2K,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,GACxDD,IAAI,CAAC2C,iBAAiB,EAAC,GAC1B;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;YAAC2K,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAY;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPvH,OAAA,CAACxD,IAAI;QAACyL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA7B,QAAA,eACvBvG,OAAA,CAACzD,KAAK;UAACiK,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAE0C,SAAS,EAAE,QAAQ;YAAEzC,OAAO,EAAE,cAAc;YAAEK,KAAK,EAAE;UAAQ,CAAE;UAAAZ,QAAA,gBAChFvG,OAAA,CAAC1D,UAAU;YAAC2K,OAAO,EAAC,IAAI;YAACT,EAAE,EAAE;cAAEU,UAAU,EAAE,MAAM;cAAEN,EAAE,EAAE;YAAE,CAAE;YAAAL,QAAA,GACxD7C,IAAI,CAACC,KAAK,CAAC2C,IAAI,CAAC0F,oBAAoB,GAAG1F,IAAI,CAAC2F,aAAa,GAAG,CAAC,CAAC,EAAC,GAClE;UAAA;YAAA7E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;YAAC2K,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAAe;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGNzF,UAAU,iBACT9B,OAAA,CAAC3D,GAAG;MAACmK,EAAE,EAAE;QAAEI,EAAE,EAAE,CAAC;QAAE+C,KAAK,EAAE;MAAO,CAAE;MAAApD,QAAA,eAChCvG,OAAA,CAACF,aAAa;QAACwG,IAAI,EAAEA;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDvH,OAAA,CAACzD,KAAK;MAACiK,EAAE,EAAE;QAAEK,CAAC,EAAE;MAAE,CAAE;MAAAN,QAAA,gBAClBvG,OAAA,CAAC1D,UAAU;QAAC2K,OAAO,EAAC,IAAI;QAACT,EAAE,EAAE;UAAEI,EAAE,EAAE,CAAC;UAAEM,UAAU,EAAE;QAAI,CAAE;QAAAX,QAAA,EAAC;MAEzD;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvH,OAAA,CAACP,eAAe;QACd6G,IAAI,EAAEA,IAAI,CAAC4F,gBAAgB,IAAI,EAAG;QAClC3B,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,MAAM;UAAEC,UAAU,EAAE,MAAM;UAAEd,KAAK,EAAE;QAAI,CAAC,EACjD;UAAEa,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE,cAAc;UAAEd,KAAK,EAAE,GAAG;UAAEe,KAAK,EAAE,OAAO;UAAEyB,QAAQ,EAAE,QAAQ;UAC1FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAAChC,KAAK;QAAI,CAAC,CACxC;QACFO,QAAQ,EAAE;MAAG;QAAAxD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;EAID,MAAM+E,YAAY,GAAGA,CAAA,kBACnBtM,OAAA,CAAChD,MAAM;IAACgJ,IAAI,EAAEpF,UAAW;IAAC2L,OAAO,EAAEnG,iBAAkB;IAACoG,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAlG,QAAA,gBAC3EvG,OAAA,CAAC/C,WAAW;MAAAsJ,QAAA,EACTzF,UAAU,KAAK,cAAc,GAAG,yBAAyB,GAAG;IAAe;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eACdvH,OAAA,CAAC9C,aAAa;MAAAqJ,QAAA,GACX7F,KAAK,iBACJV,OAAA,CAACnD,KAAK;QAAC6P,QAAQ,EAAC,OAAO;QAAClG,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,EACnC7F;MAAK;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDvH,OAAA,CAACxD,IAAI;QAACuL,SAAS;QAACC,OAAO,EAAE,CAAE;QAACxB,EAAE,EAAE;UAAEkD,EAAE,EAAE;QAAE,CAAE;QAAAnD,QAAA,gBACxCvG,OAAA,CAACxD,IAAI;UAACyL,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA3B,QAAA,eAChBvG,OAAA,CAAC5C,WAAW;YAACqP,SAAS;YAAAlG,QAAA,gBACpBvG,OAAA,CAAC3C,UAAU;cAAAkJ,QAAA,EAAC;YAAO;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChCvH,OAAA,CAAC1C,MAAM;cACLgL,KAAK,EAAEpH,QAAQ,CAACE,OAAQ;cACxByG,KAAK,EAAC,SAAS;cACfH,QAAQ,EAAGC,CAAC,IAAKxG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAEuG,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cAAA/B,QAAA,gBAEvEvG,OAAA,CAACzC,QAAQ;gBAAC+K,KAAK,EAAC,OAAO;gBAAA/B,QAAA,EAAC;cAAoB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvDvH,OAAA,CAACzC,QAAQ;gBAAC+K,KAAK,EAAC,KAAK;gBAAA/B,QAAA,EAAC;cAAY;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7CvH,OAAA,CAACzC,QAAQ;gBAAC+K,KAAK,EAAC,OAAO;gBAAA/B,QAAA,EAAC;cAAc;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAINzG,UAAU,KAAK,cAAc,iBAC5Bd,OAAA,CAAAE,SAAA;UAAAqG,QAAA,gBACEvG,OAAA,CAACxD,IAAI;YAACyL,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACfvG,OAAA,CAACxC,SAAS;cACRiP,SAAS;cACTE,IAAI,EAAC,MAAM;cACX9E,KAAK,EAAC,aAAa;cACnBS,KAAK,EAAEpH,QAAQ,CAACG,WAAY;cAC5BqG,QAAQ,EAAGC,CAAC,IAAKxG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAEsG,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cAC3EsE,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAzF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPvH,OAAA,CAACxD,IAAI;YAACyL,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA3B,QAAA,eACfvG,OAAA,CAACxC,SAAS;cACRiP,SAAS;cACTE,IAAI,EAAC,MAAM;cACX9E,KAAK,EAAC,WAAW;cACjBS,KAAK,EAAEpH,QAAQ,CAACI,SAAU;cAC1BoG,QAAQ,EAAGC,CAAC,IAAKxG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAEqG,CAAC,CAACC,MAAM,CAACU;cAAM,CAAC,CAAE;cACzEsE,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAzF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChBvH,OAAA,CAAC7C,aAAa;MAAAoJ,QAAA,gBACZvG,OAAA,CAACrD,MAAM;QAAC8O,OAAO,EAAErF,iBAAkB;QAAAG,QAAA,EAAC;MAAO;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpDvH,OAAA,CAACrD,MAAM;QACL8O,OAAO,EAAEtF,oBAAqB;QAC9Bc,OAAO,EAAC,WAAW;QACnB6F,QAAQ,EAAEtM,OAAQ;QAClBuM,SAAS,EAAEvM,OAAO,gBAAGR,OAAA,CAAClD,gBAAgB;UAAC4L,IAAI,EAAE;QAAG;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGvH,OAAA,CAACzB,cAAc;UAAA6I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAhB,QAAA,EAExE/F,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAA4G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACEvH,OAAA,CAAC3D,GAAG;IAAC2Q,SAAS,EAAC,sCAAsC;IAAAzG,QAAA,gBAEnDvG,OAAA,CAAC3D,GAAG;MAACmK,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,UAAU;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,eACpFvG,OAAA,CAACT,eAAe;QAAA6H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGL/G,OAAO,iBACNR,OAAA,CAAC3D,GAAG;MAACmK,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEuG,EAAE,EAAE;MAAE,CAAE;MAAA1G,QAAA,eAC5DvG,OAAA,CAAClD,gBAAgB;QAAAsK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGDvH,OAAA,CAAC3D,GAAG;MAACmK,EAAE,EAAE;QAAEkD,EAAE,EAAE;MAAE,CAAE;MAAAnD,QAAA,gBAEjBvG,OAAA,CAAC3D,GAAG;QAACmK,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,gBACjBvG,OAAA,CAAC1D,UAAU;UAAC2K,OAAO,EAAC,IAAI;UAACT,EAAE,EAAE;YAAEU,UAAU,EAAE,GAAG;YAAEC,KAAK,EAAE,SAAS;YAAEP,EAAE,EAAE,CAAC;YAAE2C,SAAS,EAAE;UAAS,CAAE;UAAAhD,QAAA,EAAC;QAEhG;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvH,OAAA,CAACxD,IAAI;UAACuL,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzB,QAAA,gBAEzBvG,OAAA,CAACxD,IAAI;YAACyL,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC7BvG,OAAA,CAACvD,IAAI;cACHuQ,SAAS,EAAE,eAAehM,kBAAkB,KAAK,UAAU,GAAG,sBAAsB,GAAG,EAAE,EAAG;cAC5FwF,EAAE,EAAE;gBACF6C,MAAM,EAAE,OAAO;gBACfkC,MAAM,EAAE,SAAS;gBACjBvE,MAAM,EAAEhG,kBAAkB,KAAK,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;gBACrF8F,OAAO,EAAE9F,kBAAkB,KAAK,UAAU,GAAG,SAAS,GAAG,OAAO;gBAChE6I,UAAU,EAAE;cACd,CAAE;cACF4B,OAAO,EAAEA,CAAA,KAAMxK,qBAAqB,CAAC,UAAU,CAAE;cAAAsF,QAAA,eAEjDvG,OAAA,CAACtD,WAAW;gBAAC8J,EAAE,EAAE;kBAAEK,CAAC,EAAE,CAAC;kBAAE0C,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE5C,OAAO,EAAE,MAAM;kBAAEyG,aAAa,EAAE,QAAQ;kBAAExG,cAAc,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjIvG,OAAA,CAACjC,cAAc;kBAACyI,EAAE,EAAE;oBAAE8C,QAAQ,EAAE,EAAE;oBAAEnC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjEvH,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,WAAW;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE0C,QAAQ,EAAE;kBAAS,CAAE;kBAAA/C,QAAA,EAAC;gBAEtF;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEmC,QAAQ,EAAE;kBAAS,CAAE;kBAAA/C,QAAA,EAAC;gBAEvE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPvH,OAAA,CAACxD,IAAI;YAACyL,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC7BvG,OAAA,CAACvD,IAAI;cACH+J,EAAE,EAAE;gBACF6C,MAAM,EAAE,OAAO;gBACfkC,MAAM,EAAE,SAAS;gBACjBvE,MAAM,EAAEhG,kBAAkB,KAAK,KAAK,GAAG,mBAAmB,GAAG,mBAAmB;gBAChF8F,OAAO,EAAE9F,kBAAkB,KAAK,KAAK,GAAG,SAAS,GAAG,OAAO;gBAC3D6I,UAAU,EAAE;cACd,CAAE;cACF4B,OAAO,EAAEA,CAAA,KAAMxK,qBAAqB,CAAC,KAAK,CAAE;cAAAsF,QAAA,eAE5CvG,OAAA,CAACtD,WAAW;gBAAC8J,EAAE,EAAE;kBAAEK,CAAC,EAAE,CAAC;kBAAE0C,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE5C,OAAO,EAAE,MAAM;kBAAEyG,aAAa,EAAE,QAAQ;kBAAExG,cAAc,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjIvG,OAAA,CAAC7B,QAAQ;kBAACqI,EAAE,EAAE;oBAAE8C,QAAQ,EAAE,EAAE;oBAAEnC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3DvH,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,WAAW;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE0C,QAAQ,EAAE;kBAAS,CAAE;kBAAA/C,QAAA,EAAC;gBAEtF;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEmC,QAAQ,EAAE;kBAAS,CAAE;kBAAA/C,QAAA,EAAC;gBAEvE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAKPvH,OAAA,CAACxD,IAAI;YAACyL,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eAC7BvG,OAAA,CAACvD,IAAI;cACH+J,EAAE,EAAE;gBACF6C,MAAM,EAAE,OAAO;gBACfkC,MAAM,EAAE,SAAS;gBACjBvE,MAAM,EAAEhG,kBAAkB,KAAK,gBAAgB,GAAG,mBAAmB,GAAG,mBAAmB;gBAC3F8F,OAAO,EAAE9F,kBAAkB,KAAK,gBAAgB,GAAG,SAAS,GAAG,OAAO;gBACtE6I,UAAU,EAAE;cACd,CAAE;cACF4B,OAAO,EAAEA,CAAA,KAAMxK,qBAAqB,CAAC,gBAAgB,CAAE;cAAAsF,QAAA,eAEvDvG,OAAA,CAACtD,WAAW;gBAAC8J,EAAE,EAAE;kBAAEK,CAAC,EAAE,CAAC;kBAAE0C,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE5C,OAAO,EAAE,MAAM;kBAAEyG,aAAa,EAAE,QAAQ;kBAAExG,cAAc,EAAE;gBAAS,CAAE;gBAAAH,QAAA,gBACjIvG,OAAA,CAAC/B,YAAY;kBAACuI,EAAE,EAAE;oBAAE8C,QAAQ,EAAE,EAAE;oBAAEnC,KAAK,EAAE,SAAS;oBAAEP,EAAE,EAAE;kBAAE;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/DvH,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,WAAW;kBAACT,EAAE,EAAE;oBAAEU,UAAU,EAAE,GAAG;oBAAEN,EAAE,EAAE,GAAG;oBAAE0C,QAAQ,EAAE;kBAAS,CAAE;kBAAA/C,QAAA,EAAC;gBAEtF;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbvH,OAAA,CAAC1D,UAAU;kBAAC2K,OAAO,EAAC,OAAO;kBAACT,EAAE,EAAE;oBAAEW,KAAK,EAAE,MAAM;oBAAEmC,QAAQ,EAAE;kBAAS,CAAE;kBAAA/C,QAAA,EAAC;gBAEvE;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNvH,OAAA,CAAC3D,GAAG;QAACmK,EAAE,EAAE;UAAE2G,SAAS,EAAE,OAAO;UAAExD,KAAK,EAAE;QAAO,CAAE;QAAApD,QAAA,GAE5CvF,kBAAkB,KAAK,UAAU,iBAChChB,OAAA,CAACzD,KAAK;UAACiK,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAEE,YAAY,EAAE;UAAE,CAAE;UAAAR,QAAA,EAClC/E,WAAW,CAACE,QAAQ,gBACnB1B,OAAA,CAAC3D,GAAG;YAAAkK,QAAA,gBACFvG,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC9DvG,OAAA,CAACrD,MAAM;gBACLoQ,SAAS,eAAE/M,OAAA,CAAC3B,YAAY;kBAAA+I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BkE,OAAO,EAAEA,CAAA,KAAMjG,wBAAwB,CAAC,UAAU,EAAE,KAAK,CAAE;gBAC3DyB,OAAO,EAAC,UAAU;gBAClByB,IAAI,EAAC,OAAO;gBACZvB,KAAK,EAAC,SAAS;gBACfX,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EACf;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvH,OAAA,CAACrD,MAAM;gBACLoQ,SAAS,eAAE/M,OAAA,CAAC3B,YAAY;kBAAA+I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BkE,OAAO,EAAEA,CAAA,KAAMjG,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAE;gBAC7DyB,OAAO,EAAC,UAAU;gBAClByB,IAAI,EAAC,OAAO;gBACZvB,KAAK,EAAC,SAAS;gBAAAZ,QAAA,EAChB;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLlB,oBAAoB,CAAC7E,WAAW,CAACE,QAAQ,CAAC;UAAA;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,GACJ/G,OAAO,gBACTR,OAAA,CAACN,UAAU;YACTiN,IAAI,EAAC,SAAS;YACdlH,UAAU,EAAC,UAAU;YACrB4C,KAAK,EAAC,mCAAmC;YACzC+E,WAAW,EAAC;UAAsD;YAAAhG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,gBAEFvH,OAAA,CAACN,UAAU;YACTiN,IAAI,EAAC,OAAO;YACZlH,UAAU,EAAC,UAAU;YACrB4C,KAAK,EAAC,wBAAwB;YAC9B+E,WAAW,EAAC,mFAAmF;YAC/FC,OAAO,EAAEA,CAAA,KAAM;cACb5M,UAAU,CAAC,IAAI,CAAC;cAChBjB,aAAa,CAAC8C,iBAAiB,CAAC/B,UAAU,EAAE,OAAO,CAAC,CACjD+M,IAAI,CAAChH,IAAI,IAAI;gBACZ7E,cAAc,CAAC8D,IAAI,KAAK;kBACtB,GAAGA,IAAI;kBACP7D,QAAQ,EAAE4E,IAAI,CAAC5D;gBACjB,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;gBACZC,OAAO,CAAC/B,KAAK,CAAC,iCAAiC,EAAE8B,GAAG,CAAC;cACvD,CAAC,CAAC,CACD+K,OAAO,CAAC,MAAM;gBACb9M,UAAU,CAAC,KAAK,CAAC;cACnB,CAAC,CAAC;YACN,CAAE;YACFD,OAAO,EAAEA;UAAQ;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGAvG,kBAAkB,KAAK,KAAK,iBAC3BhB,OAAA,CAACzD,KAAK;UAACiK,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAEE,YAAY,EAAE;UAAE,CAAE;UAAAR,QAAA,EAClC/E,WAAW,CAACG,GAAG,gBACd3B,OAAA,CAAC3D,GAAG;YAAAkK,QAAA,gBACFvG,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC9DvG,OAAA,CAACrD,MAAM;gBACLoQ,SAAS,eAAE/M,OAAA,CAAC3B,YAAY;kBAAA+I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BkE,OAAO,EAAEA,CAAA,KAAMjG,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAE;gBACtDyB,OAAO,EAAC,UAAU;gBAClByB,IAAI,EAAC,OAAO;gBACZvB,KAAK,EAAC,SAAS;gBACfX,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EACf;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvH,OAAA,CAACrD,MAAM;gBACLoQ,SAAS,eAAE/M,OAAA,CAAC3B,YAAY;kBAAA+I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BkE,OAAO,EAAEA,CAAA,KAAMjG,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAE;gBACxDyB,OAAO,EAAC,UAAU;gBAClByB,IAAI,EAAC,OAAO;gBACZvB,KAAK,EAAC,SAAS;gBAAAZ,QAAA,EAChB;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLsD,eAAe,CAACrJ,WAAW,CAACG,GAAG,CAAC;UAAA;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,GACJ/G,OAAO,gBACTR,OAAA,CAACN,UAAU;YACTiN,IAAI,EAAC,SAAS;YACdlH,UAAU,EAAC,KAAK;YAChB4C,KAAK,EAAC,mCAAmC;YACzC+E,WAAW,EAAC;UAAyC;YAAAhG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,gBAEFvH,OAAA,CAACN,UAAU;YACTiN,IAAI,EAAC,OAAO;YACZlH,UAAU,EAAC,KAAK;YAChB4C,KAAK,EAAC,wBAAwB;YAC9B+E,WAAW,EAAC,gFAAgF;YAC5FC,OAAO,EAAEA,CAAA,KAAM;cACb5M,UAAU,CAAC,IAAI,CAAC;cAChBjB,aAAa,CAACoD,mBAAmB,CAACrC,UAAU,EAAE,OAAO,CAAC,CACnD+M,IAAI,CAAChH,IAAI,IAAI;gBACZ7E,cAAc,CAAC8D,IAAI,KAAK;kBACtB,GAAGA,IAAI;kBACP5D,GAAG,EAAE2E,IAAI,CAAC5D;gBACZ,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;gBACZC,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,EAAE8B,GAAG,CAAC;cAClD,CAAC,CAAC,CACD+K,OAAO,CAAC,MAAM;gBACb9M,UAAU,CAAC,KAAK,CAAC;cACnB,CAAC,CAAC;YACN,CAAE;YACFD,OAAO,EAAEA;UAAQ;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGAvG,kBAAkB,KAAK,gBAAgB,iBACtChB,OAAA,CAACzD,KAAK;UAACiK,EAAE,EAAE;YAAEK,CAAC,EAAE,CAAC;YAAEE,YAAY,EAAE;UAAE,CAAE;UAAAR,QAAA,EAClC/E,WAAW,CAACK,aAAa,gBACxB7B,OAAA,CAAC3D,GAAG;YAAAkK,QAAA,gBACFvG,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC9DvG,OAAA,CAACrD,MAAM;gBACLoQ,SAAS,eAAE/M,OAAA,CAAC3B,YAAY;kBAAA+I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BkE,OAAO,EAAEA,CAAA,KAAMjG,wBAAwB,CAAC,gBAAgB,EAAE,KAAK,CAAE;gBACjEyB,OAAO,EAAC,UAAU;gBAClByB,IAAI,EAAC,OAAO;gBACZvB,KAAK,EAAC,SAAS;gBACfX,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EACf;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvH,OAAA,CAACrD,MAAM;gBACLoQ,SAAS,eAAE/M,OAAA,CAAC3B,YAAY;kBAAA+I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BkE,OAAO,EAAEA,CAAA,KAAMjG,wBAAwB,CAAC,gBAAgB,EAAE,OAAO,CAAE;gBACnEyB,OAAO,EAAC,UAAU;gBAClByB,IAAI,EAAC,OAAO;gBACZvB,KAAK,EAAC,SAAS;gBAAAZ,QAAA,EAChB;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLuD,yBAAyB,CAACtJ,WAAW,CAACK,aAAa,CAAC;UAAA;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,GACJ/G,OAAO,gBACTR,OAAA,CAACN,UAAU;YACTiN,IAAI,EAAC,SAAS;YACdlH,UAAU,EAAC,gBAAgB;YAC3B4C,KAAK,EAAC,+BAA+B;YACrC+E,WAAW,EAAC;UAA4D;YAAAhG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,gBAEFvH,OAAA,CAACN,UAAU;YACTiN,IAAI,EAAC,OAAO;YACZlH,UAAU,EAAC,gBAAgB;YAC3B4C,KAAK,EAAC,wBAAwB;YAC9B+E,WAAW,EAAC,4EAA4E;YACxFC,OAAO,EAAEA,CAAA,KAAM9I,iBAAiB,CAAC,CAAE;YACnC/D,OAAO,EAAEA;UAAQ;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGAvG,kBAAkB,KAAK,cAAc,iBACpChB,OAAA,CAACzD,KAAK;UAACiK,EAAE,EAAE;YAAEK,CAAC,EAAE;UAAE,CAAE;UAAAN,QAAA,EACjB/E,WAAW,CAACgM,WAAW,gBACtBxN,OAAA,CAAC3D,GAAG;YAAAkK,QAAA,gBACFvG,OAAA,CAAC3D,GAAG;cAACmK,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,gBAC9DvG,OAAA,CAACrD,MAAM;gBACLoQ,SAAS,eAAE/M,OAAA,CAAC3B,YAAY;kBAAA+I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BkE,OAAO,EAAEA,CAAA,KAAMjG,wBAAwB,CAAC,cAAc,EAAE,KAAK,CAAE;gBAC/DyB,OAAO,EAAC,UAAU;gBAClByB,IAAI,EAAC,OAAO;gBACZvB,KAAK,EAAC,SAAS;gBACfX,EAAE,EAAE;kBAAEsB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,EACf;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvH,OAAA,CAACrD,MAAM;gBACLoQ,SAAS,eAAE/M,OAAA,CAAC3B,YAAY;kBAAA+I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BkE,OAAO,EAAEA,CAAA,KAAMjG,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAE;gBACjEyB,OAAO,EAAC,UAAU;gBAClByB,IAAI,EAAC,OAAO;gBACZvB,KAAK,EAAC,SAAS;gBAAAZ,QAAA,EAChB;cAED;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLwE,uBAAuB,CAACvK,WAAW,CAACgM,WAAW,CAAC;UAAA;YAAApG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,gBAENvH,OAAA,CAACN,UAAU;YACTiN,IAAI,EAAC,iBAAiB;YACtBlH,UAAU,EAAC,cAAc;YACzB4C,KAAK,EAAC,sBAAsB;YAC5B+E,WAAW,EAAC,8GAA2G;YACvHK,WAAW,EAAC,mBAAmB;YAC/BC,QAAQ,EAAEA,CAAA,KAAM;cACd3M,aAAa,CAAC,cAAc,CAAC;cAC7B;cACA,MAAM4M,KAAK,GAAG,IAAI9J,IAAI,CAAC,CAAC;cACxB,MAAM+J,SAAS,GAAG,IAAI/J,IAAI,CAAC,CAAC;cAC5B+J,SAAS,CAACC,QAAQ,CAACF,KAAK,CAACG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;cAExC3M,WAAW,CAAC;gBACV,GAAGD,QAAQ;gBACXG,WAAW,EAAEuM,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAClD1M,SAAS,EAAEqM,KAAK,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC;cACFnN,aAAa,CAAC,IAAI,CAAC;YACrB;UAAE;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL+E,YAAY,CAAC,CAAC;EAAA;IAAAlF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAACnH,GAAA,CA/1CID,iBAAiB;EAAA,QACEb,SAAS;AAAA;AAAA2O,EAAA,GAD5B9N,iBAAiB;AAi2CvB,eAAeA,iBAAiB;AAAC,IAAA8N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}