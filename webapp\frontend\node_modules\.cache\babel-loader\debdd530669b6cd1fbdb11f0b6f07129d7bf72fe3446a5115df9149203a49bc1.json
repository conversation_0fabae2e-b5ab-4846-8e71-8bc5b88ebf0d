{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\nconst API_URL = config.API_URL;\nconst excelService = {\n  // Importa cavi da Excel\n  importCavi: async (cantiereId, formData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Modifica la configurazione per l'upload di file\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      };\n      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-cavi`, formData, config);\n      return response.data;\n    } catch (error) {\n      console.error('Import cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Importa parco bobine da Excel\n  importParcoBobine: async (cantiereId, formData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Modifica la configurazione per l'upload di file\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      };\n      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-parco-bobine`, formData, config);\n      return response.data;\n    } catch (error) {\n      console.error('Import parco bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea template Excel per cavi\n  createCaviTemplate: async () => {\n    try {\n      // Usa axiosInstance per fare la richiesta con il token di autenticazione\n      const response = await axiosInstance.get('/excel/template-cavi', {\n        responseType: 'blob' // Importante per scaricare file binari\n      });\n\n      // Crea un URL per il blob e avvia il download\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', 'template_cavi.xlsx');\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      return {\n        success: true\n      };\n    } catch (error) {\n      console.error('Create cavi template error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea template Excel per parco bobine\n  createParcoBobineTemplate: async () => {\n    try {\n      // Usa window.open per avviare direttamente il download\n      window.open(`${API_URL}/excel/template-parco-bobine`, '_blank');\n      return {\n        success: true\n      };\n    } catch (error) {\n      console.error('Create parco bobine template error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Esporta cavi in Excel\n  exportCavi: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Usa axiosInstance per fare la richiesta con il token di autenticazione\n      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-cavi`, {\n        responseType: 'blob' // Importante per scaricare file binari\n      });\n\n      // Crea un URL per il blob e avvia il download\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', `export_cavi_${cantiereIdNum}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      return {\n        success: true\n      };\n    } catch (error) {\n      console.error('Export cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Esporta parco bobine in Excel\n  exportParcoBobine: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Usa axiosInstance per fare la richiesta con il token di autenticazione\n      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-parco-bobine`, {\n        responseType: 'blob' // Importante per scaricare file binari\n      });\n\n      // Crea un URL per il blob e avvia il download\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', `export_parco_bobine_${cantiereIdNum}.xlsx`);\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      return {\n        success: true\n      };\n    } catch (error) {\n      console.error('Export parco bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default excelService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "excelService", "importCavi", "cantiereId", "formData", "cantiereIdNum", "parseInt", "isNaN", "Error", "headers", "localStorage", "getItem", "response", "post", "data", "error", "console", "importParcoBobine", "createCaviTemplate", "get", "responseType", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "success", "createParcoBobineTemplate", "open", "exportCavi", "exportParcoBobine"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/excelService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\nconst excelService = {\r\n  // Importa cavi da Excel\r\n  importCavi: async (cantiereId, formData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Modifica la configurazione per l'upload di file\r\n      const config = {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        }\r\n      };\r\n\r\n      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-cavi`, formData, config);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Import cavi error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Importa parco bobine da Excel\r\n  importParcoBobine: async (cantiereId, formData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Modifica la configurazione per l'upload di file\r\n      const config = {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        }\r\n      };\r\n\r\n      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-parco-bobine`, formData, config);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Import parco bobine error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea template Excel per cavi\r\n  createCaviTemplate: async () => {\r\n    try {\r\n      // Usa axiosInstance per fare la richiesta con il token di autenticazione\r\n      const response = await axiosInstance.get('/excel/template-cavi', {\r\n        responseType: 'blob' // Importante per scaricare file binari\r\n      });\r\n\r\n      // Crea un URL per il blob e avvia il download\r\n      const url = window.URL.createObjectURL(new Blob([response.data]));\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.setAttribute('download', 'template_cavi.xlsx');\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      link.remove();\r\n\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error('Create cavi template error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea template Excel per parco bobine\r\n  createParcoBobineTemplate: async () => {\r\n    try {\r\n      // Usa window.open per avviare direttamente il download\r\n      window.open(`${API_URL}/excel/template-parco-bobine`, '_blank');\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error('Create parco bobine template error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n\r\n  // Esporta cavi in Excel\r\n  exportCavi: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Usa axiosInstance per fare la richiesta con il token di autenticazione\r\n      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-cavi`, {\r\n        responseType: 'blob' // Importante per scaricare file binari\r\n      });\r\n\r\n      // Crea un URL per il blob e avvia il download\r\n      const url = window.URL.createObjectURL(new Blob([response.data]));\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.setAttribute('download', `export_cavi_${cantiereIdNum}.xlsx`);\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      link.remove();\r\n\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error('Export cavi error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Esporta parco bobine in Excel\r\n  exportParcoBobine: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Usa axiosInstance per fare la richiesta con il token di autenticazione\r\n      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-parco-bobine`, {\r\n        responseType: 'blob' // Importante per scaricare file binari\r\n      });\r\n\r\n      // Crea un URL per il blob e avvia il download\r\n      const url = window.URL.createObjectURL(new Blob([response.data]));\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.setAttribute('download', `export_parco_bobine_${cantiereIdNum}.xlsx`);\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      link.remove();\r\n\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error('Export parco bobine error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default excelService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;AAEzC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;AAE9B,MAAMC,YAAY,GAAG;EACnB;EACAC,UAAU,EAAE,MAAAA,CAAOC,UAAU,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA,MAAML,MAAM,GAAG;QACbW,OAAO,EAAE;UACP,cAAc,EAAE,qBAAqB;UACrC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMf,KAAK,CAACgB,IAAI,CAAC,GAAGb,OAAO,UAAUK,aAAa,cAAc,EAAED,QAAQ,EAAEN,MAAM,CAAC;MACpG,OAAOc,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAE,iBAAiB,EAAE,MAAAA,CAAOd,UAAU,EAAEC,QAAQ,KAAK;IACjD,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA,MAAML,MAAM,GAAG;QACbW,OAAO,EAAE;UACP,cAAc,EAAE,qBAAqB;UACrC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMf,KAAK,CAACgB,IAAI,CAAC,GAAGb,OAAO,UAAUK,aAAa,sBAAsB,EAAED,QAAQ,EAAEN,MAAM,CAAC;MAC5G,OAAOc,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAG,kBAAkB,EAAE,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF;MACA,MAAMN,QAAQ,GAAG,MAAMb,aAAa,CAACoB,GAAG,CAAC,sBAAsB,EAAE;QAC/DC,YAAY,EAAE,MAAM,CAAC;MACvB,CAAC,CAAC;;MAEF;MACA,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAACb,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;MACjE,MAAMY,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGR,GAAG;MACfK,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,oBAAoB,CAAC;MACnDH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZP,IAAI,CAACQ,MAAM,CAAC,CAAC;MAEb,OAAO;QAAEC,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAqB,yBAAyB,EAAE,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF;MACAd,MAAM,CAACe,IAAI,CAAC,GAAGrC,OAAO,8BAA8B,EAAE,QAAQ,CAAC;MAC/D,OAAO;QAAEmC,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAGD;EACAuB,UAAU,EAAE,MAAOnC,UAAU,IAAK;IAChC,IAAI;MACF;MACA,MAAME,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA,MAAMS,QAAQ,GAAG,MAAMb,aAAa,CAACoB,GAAG,CAAC,UAAUd,aAAa,cAAc,EAAE;QAC9Ee,YAAY,EAAE,MAAM,CAAC;MACvB,CAAC,CAAC;;MAEF;MACA,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAACb,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;MACjE,MAAMY,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGR,GAAG;MACfK,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,eAAezB,aAAa,OAAO,CAAC;MAClEsB,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZP,IAAI,CAACQ,MAAM,CAAC,CAAC;MAEb,OAAO;QAAEC,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAwB,iBAAiB,EAAE,MAAOpC,UAAU,IAAK;IACvC,IAAI;MACF;MACA,MAAME,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA,MAAMS,QAAQ,GAAG,MAAMb,aAAa,CAACoB,GAAG,CAAC,UAAUd,aAAa,sBAAsB,EAAE;QACtFe,YAAY,EAAE,MAAM,CAAC;MACvB,CAAC,CAAC;;MAEF;MACA,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAACb,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;MACjE,MAAMY,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGR,GAAG;MACfK,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,uBAAuBzB,aAAa,OAAO,CAAC;MAC1EsB,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZP,IAAI,CAACQ,MAAM,CAAC,CAAC;MAEb,OAAO;QAAEC,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAed,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}