{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { Box, CssBaseline, Drawer, AppBar, Toolbar, Typography, Divider, List, IconButton } from '@mui/material';\nimport { Menu as MenuIcon, Logout as LogoutIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport MainMenu from '../components/MainMenu';\nimport HomePage from './HomePage';\nimport AdminPage from './AdminPage';\nimport UserPage from './UserPage';\nimport CaviPage from './CaviPage';\nimport UserExpirationChecker from '../components/admin/UserExpirationChecker';\n\n// Importa le nuove pagine per i cavi\nimport VisualizzaCaviPage from './cavi/VisualizzaCaviPage';\nimport PosaCaviPage from './cavi/PosaCaviPage';\nimport ParcoCaviPage from './cavi/ParcoCaviPage';\nimport GestioneExcelPage from './cavi/GestioneExcelPage';\nimport ReportCaviPage from './cavi/ReportCaviPage';\nimport CertificazioneCaviPage from './cavi/CertificazioneCaviPage';\nimport GestioneComandeePage from './cavi/GestioneComandeePage';\nimport TestCaviPage from './cavi/TestCaviPage';\n\n// Importa le pagine per Posa e Collegamenti\nimport InserisciMetriPage from './cavi/posa/InserisciMetriPage';\nimport ModificaCavoPage from './cavi/posa/ModificaCavoPage';\nimport AggiungiCavoPage from './cavi/posa/AggiungiCavoPage';\nimport EliminaCavoPage from './cavi/posa/EliminaCavoPage';\nimport ModificaBobinaPage from './cavi/posa/ModificaBobinaPage';\nimport CollegamentiPage from './cavi/posa/CollegamentiPage';\n\n// Importa la pagina per il cantiere specifico\nimport CantierePage from './cantieri/CantierePage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst drawerWidth = 280; // Aumentato per ospitare il menu a cascata\n\nconst Dashboard = () => {\n  _s();\n  const {\n    user,\n    logout,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n  const [mobileOpen, setMobileOpen] = React.useState(false);\n  const handleDrawerToggle = () => {\n    setMobileOpen(!mobileOpen);\n  };\n  const handleLogout = () => {\n    logout();\n  };\n  const drawer = /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Toolbar, {\n      sx: {\n        backgroundColor: '#1976d2',\n        color: 'white'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        noWrap: true,\n        component: \"div\",\n        children: \"CMS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        overflowY: 'auto',\n        maxHeight: 'calc(100vh - 64px)'\n      },\n      children: /*#__PURE__*/_jsxDEV(MainMenu, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex'\n    },\n    children: [/*#__PURE__*/_jsxDEV(UserExpirationChecker, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      sx: {\n        width: {\n          sm: `calc(100% - ${drawerWidth}px)`\n        },\n        ml: {\n          sm: `${drawerWidth}px`\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"inherit\",\n          \"aria-label\": \"open drawer\",\n          edge: \"start\",\n          onClick: handleDrawerToggle,\n          sx: {\n            mr: 2,\n            display: {\n              sm: 'none'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          noWrap: true,\n          component: \"div\",\n          sx: {\n            flexGrow: 1\n          },\n          children: \"Sistema di Gestione Cantieri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'right',\n              mr: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: user === null || user === void 0 ? void 0 : user.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), isImpersonating && impersonatedUser && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'orange.light',\n                display: 'block'\n              },\n              children: [\"Accesso come: \", impersonatedUser.username]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            color: \"inherit\",\n            onClick: handleLogout,\n            children: /*#__PURE__*/_jsxDEV(LogoutIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"nav\",\n      sx: {\n        width: {\n          sm: drawerWidth\n        },\n        flexShrink: {\n          sm: 0\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Drawer, {\n        variant: \"temporary\",\n        open: mobileOpen,\n        onClose: handleDrawerToggle,\n        ModalProps: {\n          keepMounted: true // Better open performance on mobile\n        },\n        sx: {\n          display: {\n            xs: 'block',\n            sm: 'none'\n          },\n          '& .MuiDrawer-paper': {\n            boxSizing: 'border-box',\n            width: drawerWidth\n          }\n        },\n        children: drawer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Drawer, {\n        variant: \"permanent\",\n        sx: {\n          display: {\n            xs: 'none',\n            sm: 'block'\n          },\n          '& .MuiDrawer-paper': {\n            boxSizing: 'border-box',\n            width: drawerWidth\n          }\n        },\n        open: true,\n        children: drawer\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"main\",\n      sx: {\n        flexGrow: 1,\n        p: 3,\n        width: {\n          sm: `calc(100% - ${drawerWidth}px)`\n        },\n        backgroundColor: '#f5f5f5',\n        // Sfondo grigio chiaro per l'area principale\n        minHeight: '100vh' // Altezza minima per coprire l'intera viewport\n      },\n      children: [/*#__PURE__*/_jsxDEV(Toolbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(AdminPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri\",\n          element: /*#__PURE__*/_jsxDEV(UserPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri/:cantiereId\",\n          element: /*#__PURE__*/_jsxDEV(CantierePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 56\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi\",\n          element: /*#__PURE__*/_jsxDEV(CaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/visualizza\",\n          element: /*#__PURE__*/_jsxDEV(VisualizzaCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 51\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa\",\n          element: /*#__PURE__*/_jsxDEV(PosaCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco\",\n          element: /*#__PURE__*/_jsxDEV(ParcoCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/excel\",\n          element: /*#__PURE__*/_jsxDEV(GestioneExcelPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/report\",\n          element: /*#__PURE__*/_jsxDEV(ReportCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 55\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/comande\",\n          element: /*#__PURE__*/_jsxDEV(GestioneComandeePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 48\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/test\",\n          element: /*#__PURE__*/_jsxDEV(TestCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/inserisci-metri\",\n          element: /*#__PURE__*/_jsxDEV(InserisciMetriPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 61\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/modifica-cavo\",\n          element: /*#__PURE__*/_jsxDEV(ModificaCavoPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 59\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/aggiungi-cavo\",\n          element: /*#__PURE__*/_jsxDEV(AggiungiCavoPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 59\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/elimina-cavo\",\n          element: /*#__PURE__*/_jsxDEV(EliminaCavoPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 58\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/modifica-bobina\",\n          element: /*#__PURE__*/_jsxDEV(ModificaBobinaPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 61\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/collegamenti\",\n          element: /*#__PURE__*/_jsxDEV(CollegamentiPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 58\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"/isIPUUu4nPLhTkyH82s3Gk0KeI=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Box", "CssBaseline", "Drawer", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "Divider", "List", "IconButton", "<PERSON><PERSON>", "MenuIcon", "Logout", "LogoutIcon", "useAuth", "MainMenu", "HomePage", "AdminPage", "UserPage", "CaviPage", "UserExpirationChecker", "VisualizzaCaviPage", "PosaCaviPage", "ParcoCaviPage", "GestioneExcelPage", "ReportCaviPage", "CertificazioneCaviPage", "GestioneComandeePage", "TestCaviPage", "InserisciMetriPage", "ModificaCavoPage", "AggiungiCavoPage", "EliminaCavoPage", "ModificaBobinaPage", "CollegamentiPage", "CantierePage", "jsxDEV", "_jsxDEV", "drawerWidth", "Dashboard", "_s", "user", "logout", "isImpersonating", "impersonated<PERSON><PERSON>", "mobileOpen", "setMobileOpen", "useState", "handleDrawerToggle", "handleLogout", "drawer", "children", "sx", "backgroundColor", "color", "variant", "noWrap", "component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "overflowY", "maxHeight", "display", "position", "width", "sm", "ml", "edge", "onClick", "mr", "flexGrow", "alignItems", "textAlign", "username", "flexShrink", "open", "onClose", "ModalProps", "keepMounted", "xs", "boxSizing", "p", "minHeight", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport { Box, CssBaseline, Drawer, AppBar, Toolbar, Typography, Divider, List, IconButton } from '@mui/material';\nimport { Menu as MenuIcon, Logout as LogoutIcon } from '@mui/icons-material';\n\nimport { useAuth } from '../context/AuthContext';\nimport MainMenu from '../components/MainMenu';\nimport HomePage from './HomePage';\nimport AdminPage from './AdminPage';\nimport UserPage from './UserPage';\nimport CaviPage from './CaviPage';\nimport UserExpirationChecker from '../components/admin/UserExpirationChecker';\n\n// Importa le nuove pagine per i cavi\nimport VisualizzaCaviPage from './cavi/VisualizzaCaviPage';\nimport PosaCaviPage from './cavi/PosaCaviPage';\nimport ParcoCaviPage from './cavi/ParcoCaviPage';\nimport GestioneExcelPage from './cavi/GestioneExcelPage';\nimport ReportCaviPage from './cavi/ReportCaviPage';\nimport CertificazioneCaviPage from './cavi/CertificazioneCaviPage';\nimport GestioneComandeePage from './cavi/GestioneComandeePage';\nimport TestCaviPage from './cavi/TestCaviPage';\n\n// Importa le pagine per Posa e Collegamenti\nimport InserisciMetriPage from './cavi/posa/InserisciMetriPage';\nimport ModificaCavoPage from './cavi/posa/ModificaCavoPage';\nimport AggiungiCavoPage from './cavi/posa/AggiungiCavoPage';\nimport EliminaCavoPage from './cavi/posa/EliminaCavoPage';\nimport ModificaBobinaPage from './cavi/posa/ModificaBobinaPage';\nimport CollegamentiPage from './cavi/posa/CollegamentiPage';\n\n// Importa la pagina per il cantiere specifico\nimport CantierePage from './cantieri/CantierePage';\n\nconst drawerWidth = 280; // Aumentato per ospitare il menu a cascata\n\nconst Dashboard = () => {\n  const { user, logout, isImpersonating, impersonatedUser } = useAuth();\n  const [mobileOpen, setMobileOpen] = React.useState(false);\n\n  const handleDrawerToggle = () => {\n    setMobileOpen(!mobileOpen);\n  };\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  const drawer = (\n    <div>\n      <Toolbar sx={{ backgroundColor: '#1976d2', color: 'white' }}>\n        <Typography variant=\"h6\" noWrap component=\"div\">\n          CMS\n        </Typography>\n      </Toolbar>\n      <Divider />\n      <Box sx={{ overflowY: 'auto', maxHeight: 'calc(100vh - 64px)' }}>\n        <MainMenu />\n      </Box>\n    </div>\n  );\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      {/* Componente invisibile che verifica gli utenti scaduti */}\n      <UserExpirationChecker />\n      <CssBaseline />\n      <AppBar\n        position=\"fixed\"\n        sx={{\n          width: { sm: `calc(100% - ${drawerWidth}px)` },\n          ml: { sm: `${drawerWidth}px` },\n        }}\n      >\n        <Toolbar>\n          <IconButton\n            color=\"inherit\"\n            aria-label=\"open drawer\"\n            edge=\"start\"\n            onClick={handleDrawerToggle}\n            sx={{ mr: 2, display: { sm: 'none' } }}\n          >\n            <MenuIcon />\n          </IconButton>\n          <Typography variant=\"h6\" noWrap component=\"div\" sx={{ flexGrow: 1 }}>\n            Sistema di Gestione Cantieri\n          </Typography>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <Box sx={{ textAlign: 'right', mr: 2 }}>\n              <Typography variant=\"body1\">\n                {user?.username}\n              </Typography>\n              {isImpersonating && impersonatedUser && (\n                <Typography variant=\"caption\" sx={{ color: 'orange.light', display: 'block' }}>\n                  Accesso come: {impersonatedUser.username}\n                </Typography>\n              )}\n            </Box>\n            <IconButton color=\"inherit\" onClick={handleLogout}>\n              <LogoutIcon />\n            </IconButton>\n          </Box>\n        </Toolbar>\n      </AppBar>\n      <Box\n        component=\"nav\"\n        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}\n      >\n        <Drawer\n          variant=\"temporary\"\n          open={mobileOpen}\n          onClose={handleDrawerToggle}\n          ModalProps={{\n            keepMounted: true, // Better open performance on mobile\n          }}\n          sx={{\n            display: { xs: 'block', sm: 'none' },\n            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },\n          }}\n        >\n          {drawer}\n        </Drawer>\n        <Drawer\n          variant=\"permanent\"\n          sx={{\n            display: { xs: 'none', sm: 'block' },\n            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },\n          }}\n          open\n        >\n          {drawer}\n        </Drawer>\n      </Box>\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: 3,\n          width: { sm: `calc(100% - ${drawerWidth}px)` },\n          backgroundColor: '#f5f5f5', // Sfondo grigio chiaro per l'area principale\n          minHeight: '100vh' // Altezza minima per coprire l'intera viewport\n        }}\n      >\n        <Toolbar />\n        <Routes>\n          <Route path=\"/\" element={<HomePage />} />\n          <Route path=\"/admin\" element={<AdminPage />} />\n          <Route path=\"/cantieri\" element={<UserPage />} />\n          <Route path=\"/cantieri/:cantiereId\" element={<CantierePage />} />\n\n          {/* Route per la gestione cavi */}\n          <Route path=\"/cavi\" element={<CaviPage />} />\n          <Route path=\"/cavi/visualizza\" element={<VisualizzaCaviPage />} />\n          <Route path=\"/cavi/posa\" element={<PosaCaviPage />} />\n          <Route path=\"/cavi/parco\" element={<ParcoCaviPage />} />\n          <Route path=\"/cavi/excel\" element={<GestioneExcelPage />} />\n          <Route path=\"/cavi/report\" element={<ReportCaviPage />} />\n          <Route path=\"/cavi/certificazione\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/comande\" element={<GestioneComandeePage />} />\n          <Route path=\"/cavi/test\" element={<TestCaviPage />} />\n\n          {/* Route per Posa e Collegamenti */}\n          <Route path=\"/cavi/posa/inserisci-metri\" element={<InserisciMetriPage />} />\n          <Route path=\"/cavi/posa/modifica-cavo\" element={<ModificaCavoPage />} />\n          <Route path=\"/cavi/posa/aggiungi-cavo\" element={<AggiungiCavoPage />} />\n          <Route path=\"/cavi/posa/elimina-cavo\" element={<EliminaCavoPage />} />\n          <Route path=\"/cavi/posa/modifica-bobina\" element={<ModificaBobinaPage />} />\n          <Route path=\"/cavi/posa/collegamenti\" element={<CollegamentiPage />} />\n\n          {/* Altre route verranno aggiunte man mano che vengono implementate */}\n        </Routes>\n      </Box>\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SAASC,GAAG,EAAEC,WAAW,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAO,EAAEC,IAAI,EAAEC,UAAU,QAAQ,eAAe;AAChH,SAASC,IAAI,IAAIC,QAAQ,EAAEC,MAAM,IAAIC,UAAU,QAAQ,qBAAqB;AAE5E,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,qBAAqB,MAAM,2CAA2C;;AAE7E;AACA,OAAOC,kBAAkB,MAAM,2BAA2B;AAC1D,OAAOC,YAAY,MAAM,qBAAqB;AAC9C,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,iBAAiB,MAAM,0BAA0B;AACxD,OAAOC,cAAc,MAAM,uBAAuB;AAClD,OAAOC,sBAAsB,MAAM,+BAA+B;AAClE,OAAOC,oBAAoB,MAAM,6BAA6B;AAC9D,OAAOC,YAAY,MAAM,qBAAqB;;AAE9C;AACA,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,gBAAgB,MAAM,8BAA8B;;AAE3D;AACA,OAAOC,YAAY,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC,IAAI;IAAEC,MAAM;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAG9B,OAAO,CAAC,CAAC;EACrE,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhD,KAAK,CAACiD,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BF,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzBP,MAAM,CAAC,CAAC;EACV,CAAC;EAED,MAAMQ,MAAM,gBACVb,OAAA;IAAAc,QAAA,gBACEd,OAAA,CAAChC,OAAO;MAAC+C,EAAE,EAAE;QAAEC,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAQ,CAAE;MAAAH,QAAA,eAC1Dd,OAAA,CAAC/B,UAAU;QAACiD,OAAO,EAAC,IAAI;QAACC,MAAM;QAACC,SAAS,EAAC,KAAK;QAAAN,QAAA,EAAC;MAEhD;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACVxB,OAAA,CAAC9B,OAAO;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXxB,OAAA,CAACpC,GAAG;MAACmD,EAAE,EAAE;QAAEU,SAAS,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAqB,CAAE;MAAAZ,QAAA,eAC9Dd,OAAA,CAACtB,QAAQ;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACExB,OAAA,CAACpC,GAAG;IAACmD,EAAE,EAAE;MAAEY,OAAO,EAAE;IAAO,CAAE;IAAAb,QAAA,gBAE3Bd,OAAA,CAACjB,qBAAqB;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzBxB,OAAA,CAACnC,WAAW;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfxB,OAAA,CAACjC,MAAM;MACL6D,QAAQ,EAAC,OAAO;MAChBb,EAAE,EAAE;QACFc,KAAK,EAAE;UAAEC,EAAE,EAAE,eAAe7B,WAAW;QAAM,CAAC;QAC9C8B,EAAE,EAAE;UAAED,EAAE,EAAE,GAAG7B,WAAW;QAAK;MAC/B,CAAE;MAAAa,QAAA,eAEFd,OAAA,CAAChC,OAAO;QAAA8C,QAAA,gBACNd,OAAA,CAAC5B,UAAU;UACT6C,KAAK,EAAC,SAAS;UACf,cAAW,aAAa;UACxBe,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEtB,kBAAmB;UAC5BI,EAAE,EAAE;YAAEmB,EAAE,EAAE,CAAC;YAAEP,OAAO,EAAE;cAAEG,EAAE,EAAE;YAAO;UAAE,CAAE;UAAAhB,QAAA,eAEvCd,OAAA,CAAC1B,QAAQ;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACbxB,OAAA,CAAC/B,UAAU;UAACiD,OAAO,EAAC,IAAI;UAACC,MAAM;UAACC,SAAS,EAAC,KAAK;UAACL,EAAE,EAAE;YAAEoB,QAAQ,EAAE;UAAE,CAAE;UAAArB,QAAA,EAAC;QAErE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxB,OAAA,CAACpC,GAAG;UAACmD,EAAE,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAES,UAAU,EAAE;UAAS,CAAE;UAAAtB,QAAA,gBACjDd,OAAA,CAACpC,GAAG;YAACmD,EAAE,EAAE;cAAEsB,SAAS,EAAE,OAAO;cAAEH,EAAE,EAAE;YAAE,CAAE;YAAApB,QAAA,gBACrCd,OAAA,CAAC/B,UAAU;cAACiD,OAAO,EAAC,OAAO;cAAAJ,QAAA,EACxBV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkC;YAAQ;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EACZlB,eAAe,IAAIC,gBAAgB,iBAClCP,OAAA,CAAC/B,UAAU;cAACiD,OAAO,EAAC,SAAS;cAACH,EAAE,EAAE;gBAAEE,KAAK,EAAE,cAAc;gBAAEU,OAAO,EAAE;cAAQ,CAAE;cAAAb,QAAA,GAAC,gBAC/D,EAACP,gBAAgB,CAAC+B,QAAQ;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNxB,OAAA,CAAC5B,UAAU;YAAC6C,KAAK,EAAC,SAAS;YAACgB,OAAO,EAAErB,YAAa;YAAAE,QAAA,eAChDd,OAAA,CAACxB,UAAU;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACTxB,OAAA,CAACpC,GAAG;MACFwD,SAAS,EAAC,KAAK;MACfL,EAAE,EAAE;QAAEc,KAAK,EAAE;UAAEC,EAAE,EAAE7B;QAAY,CAAC;QAAEsC,UAAU,EAAE;UAAET,EAAE,EAAE;QAAE;MAAE,CAAE;MAAAhB,QAAA,gBAE1Dd,OAAA,CAAClC,MAAM;QACLoD,OAAO,EAAC,WAAW;QACnBsB,IAAI,EAAEhC,UAAW;QACjBiC,OAAO,EAAE9B,kBAAmB;QAC5B+B,UAAU,EAAE;UACVC,WAAW,EAAE,IAAI,CAAE;QACrB,CAAE;QACF5B,EAAE,EAAE;UACFY,OAAO,EAAE;YAAEiB,EAAE,EAAE,OAAO;YAAEd,EAAE,EAAE;UAAO,CAAC;UACpC,oBAAoB,EAAE;YAAEe,SAAS,EAAE,YAAY;YAAEhB,KAAK,EAAE5B;UAAY;QACtE,CAAE;QAAAa,QAAA,EAEDD;MAAM;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACTxB,OAAA,CAAClC,MAAM;QACLoD,OAAO,EAAC,WAAW;QACnBH,EAAE,EAAE;UACFY,OAAO,EAAE;YAAEiB,EAAE,EAAE,MAAM;YAAEd,EAAE,EAAE;UAAQ,CAAC;UACpC,oBAAoB,EAAE;YAAEe,SAAS,EAAE,YAAY;YAAEhB,KAAK,EAAE5B;UAAY;QACtE,CAAE;QACFuC,IAAI;QAAA1B,QAAA,EAEHD;MAAM;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACNxB,OAAA,CAACpC,GAAG;MACFwD,SAAS,EAAC,MAAM;MAChBL,EAAE,EAAE;QACFoB,QAAQ,EAAE,CAAC;QACXW,CAAC,EAAE,CAAC;QACJjB,KAAK,EAAE;UAAEC,EAAE,EAAE,eAAe7B,WAAW;QAAM,CAAC;QAC9Ce,eAAe,EAAE,SAAS;QAAE;QAC5B+B,SAAS,EAAE,OAAO,CAAC;MACrB,CAAE;MAAAjC,QAAA,gBAEFd,OAAA,CAAChC,OAAO;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXxB,OAAA,CAACtC,MAAM;QAAAoD,QAAA,gBACLd,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEjD,OAAA,CAACrB,QAAQ;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCxB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEjD,OAAA,CAACpB,SAAS;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CxB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEjD,OAAA,CAACnB,QAAQ;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDxB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,uBAAuB;UAACC,OAAO,eAAEjD,OAAA,CAACF,YAAY;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGjExB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEjD,OAAA,CAAClB,QAAQ;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CxB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,kBAAkB;UAACC,OAAO,eAAEjD,OAAA,CAAChB,kBAAkB;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClExB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEjD,OAAA,CAACf,YAAY;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDxB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,aAAa;UAACC,OAAO,eAAEjD,OAAA,CAACd,aAAa;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDxB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,aAAa;UAACC,OAAO,eAAEjD,OAAA,CAACb,iBAAiB;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DxB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,cAAc;UAACC,OAAO,eAAEjD,OAAA,CAACZ,cAAc;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DxB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,sBAAsB;UAACC,OAAO,eAAEjD,OAAA,CAACX,sBAAsB;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1ExB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,eAAe;UAACC,OAAO,eAAEjD,OAAA,CAACV,oBAAoB;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjExB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEjD,OAAA,CAACT,YAAY;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGtDxB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,4BAA4B;UAACC,OAAO,eAAEjD,OAAA,CAACR,kBAAkB;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5ExB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,0BAA0B;UAACC,OAAO,eAAEjD,OAAA,CAACP,gBAAgB;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxExB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,0BAA0B;UAACC,OAAO,eAAEjD,OAAA,CAACN,gBAAgB;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxExB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,yBAAyB;UAACC,OAAO,eAAEjD,OAAA,CAACL,eAAe;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtExB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,4BAA4B;UAACC,OAAO,eAAEjD,OAAA,CAACJ,kBAAkB;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5ExB,OAAA,CAACrC,KAAK;UAACqF,IAAI,EAAC,yBAAyB;UAACC,OAAO,eAAEjD,OAAA,CAACH,gBAAgB;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGjE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrB,EAAA,CA1IID,SAAS;EAAA,QAC+CzB,OAAO;AAAA;AAAAyE,EAAA,GAD/DhD,SAAS;AA4If,eAAeA,SAAS;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}