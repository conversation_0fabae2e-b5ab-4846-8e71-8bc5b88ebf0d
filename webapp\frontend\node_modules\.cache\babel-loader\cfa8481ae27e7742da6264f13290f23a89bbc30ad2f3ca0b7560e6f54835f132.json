{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { useAuth } from './context/AuthContext';\nimport LoginPage from './pages/LoginPageNew';\nimport Dashboard from './pages/Dashboard';\nimport ProtectedRoute from './components/ProtectedRoute';\n\n// Tema personalizzato\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2'\n    },\n    secondary: {\n      main: '#dc004e'\n    },\n    info: {\n      main: '#0288d1'\n    },\n    success: {\n      main: '#2e7d32'\n    },\n    warning: {\n      main: '#ed6c02'\n    },\n    error: {\n      main: '#d32f2f'\n    }\n  }\n});\nfunction App() {\n  _s();\n  const {\n    isAuthenticated,\n    loading\n  } = useAuth();\n  console.log('App - Stato autenticazione:', {\n    isAuthenticated,\n    loading\n  });\n\n  // Se l'applicazione è in caricamento, mostra un indicatore di caricamento\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '100vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Caricamento...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: isAuthenticated ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 68\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard/*\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: isAuthenticated ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/login\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"F3aPsg481KjBH7Z7iYl6LJifZz0=\", false, function () {\n  return [useAuth];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "ThemeProvider", "createTheme", "CssBaseline", "useAuth", "LoginPage", "Dashboard", "ProtectedRoute", "jsxDEV", "_jsxDEV", "theme", "palette", "primary", "main", "secondary", "info", "success", "warning", "error", "App", "_s", "isAuthenticated", "loading", "console", "log", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "justifyContent", "alignItems", "height", "textAlign", "path", "element", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\n\nimport { useAuth } from './context/AuthContext';\nimport LoginPage from './pages/LoginPageNew';\nimport Dashboard from './pages/Dashboard';\nimport ProtectedRoute from './components/ProtectedRoute';\n\n// Tema personalizzato\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n    info: {\n      main: '#0288d1',\n    },\n    success: {\n      main: '#2e7d32',\n    },\n    warning: {\n      main: '#ed6c02',\n    },\n    error: {\n      main: '#d32f2f',\n    },\n  },\n});\n\nfunction App() {\n  const { isAuthenticated, loading } = useAuth();\n\n  console.log('App - Stato autenticazione:', { isAuthenticated, loading });\n\n  // Se l'applicazione è in caricamento, mostra un indicatore di caricamento\n  if (loading) {\n    return (\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\n          <div style={{ textAlign: 'center' }}>\n            <div>Caricamento...</div>\n          </div>\n        </div>\n      </ThemeProvider>\n    );\n  }\n\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Routes>\n        <Route path=\"/login\" element={\n          isAuthenticated ? <Navigate to=\"/dashboard\" replace /> : <LoginPage />\n        } />\n        <Route\n          path=\"/dashboard/*\"\n          element={\n            <ProtectedRoute>\n              <Dashboard />\n            </ProtectedRoute>\n          }\n        />\n        <Route\n          path=\"/\"\n          element={\n            isAuthenticated ? (\n              <Navigate to=\"/dashboard\" replace />\n            ) : (\n              <Navigate to=\"/login\" replace />\n            )\n          }\n        />\n      </Routes>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AAEnD,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,cAAc,MAAM,6BAA6B;;AAExD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,KAAK,GAAGR,WAAW,CAAC;EACxBS,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTD,IAAI,EAAE;IACR,CAAC;IACDE,IAAI,EAAE;MACJF,IAAI,EAAE;IACR,CAAC;IACDG,OAAO,EAAE;MACPH,IAAI,EAAE;IACR,CAAC;IACDI,OAAO,EAAE;MACPJ,IAAI,EAAE;IACR,CAAC;IACDK,KAAK,EAAE;MACLL,IAAI,EAAE;IACR;EACF;AACF,CAAC,CAAC;AAEF,SAASM,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAE9CmB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;IAAEH,eAAe;IAAEC;EAAQ,CAAC,CAAC;;EAExE;EACA,IAAIA,OAAO,EAAE;IACX,oBACEb,OAAA,CAACR,aAAa;MAACS,KAAK,EAAEA,KAAM;MAAAe,QAAA,gBAC1BhB,OAAA,CAACN,WAAW;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfpB,OAAA;QAAKqB,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,UAAU,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAT,QAAA,eAC/FhB,OAAA;UAAKqB,KAAK,EAAE;YAAEK,SAAS,EAAE;UAAS,CAAE;UAAAV,QAAA,eAClChB,OAAA;YAAAgB,QAAA,EAAK;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAEpB;EAEA,oBACEpB,OAAA,CAACR,aAAa;IAACS,KAAK,EAAEA,KAAM;IAAAe,QAAA,gBAC1BhB,OAAA,CAACN,WAAW;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfpB,OAAA,CAACX,MAAM;MAAA2B,QAAA,gBACLhB,OAAA,CAACV,KAAK;QAACqC,IAAI,EAAC,QAAQ;QAACC,OAAO,EAC1BhB,eAAe,gBAAGZ,OAAA,CAACT,QAAQ;UAACsC,EAAE,EAAC,YAAY;UAACC,OAAO;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGpB,OAAA,CAACJ,SAAS;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MACtE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACJpB,OAAA,CAACV,KAAK;QACJqC,IAAI,EAAC,cAAc;QACnBC,OAAO,eACL5B,OAAA,CAACF,cAAc;UAAAkB,QAAA,eACbhB,OAAA,CAACH,SAAS;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFpB,OAAA,CAACV,KAAK;QACJqC,IAAI,EAAC,GAAG;QACRC,OAAO,EACLhB,eAAe,gBACbZ,OAAA,CAACT,QAAQ;UAACsC,EAAE,EAAC,YAAY;UAACC,OAAO;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEpCpB,OAAA,CAACT,QAAQ;UAACsC,EAAE,EAAC,QAAQ;UAACC,OAAO;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAElC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACT,EAAA,CA/CQD,GAAG;EAAA,QAC2Bf,OAAO;AAAA;AAAAoC,EAAA,GADrCrB,GAAG;AAiDZ,eAAeA,GAAG;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}