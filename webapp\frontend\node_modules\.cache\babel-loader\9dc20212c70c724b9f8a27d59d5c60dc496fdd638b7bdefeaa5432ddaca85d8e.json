{"ast": null, "code": "import { formatDistance } from \"./gd/_lib/formatDistance.js\";\nimport { formatLong } from \"./gd/_lib/formatLong.js\";\nimport { formatRelative } from \"./gd/_lib/formatRelative.js\";\nimport { localize } from \"./gd/_lib/localize.js\";\nimport { match } from \"./gd/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Scottish Gaelic.\n * @language Scottish Gaelic\n * @iso-639-2 gla\n * <AUTHOR> [@leedriscoll](https://github.com/leedriscoll)\n */\nexport const gd = {\n  code: \"gd\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default gd;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "gd", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/gd.js"], "sourcesContent": ["import { formatDistance } from \"./gd/_lib/formatDistance.js\";\nimport { formatLong } from \"./gd/_lib/formatLong.js\";\nimport { formatRelative } from \"./gd/_lib/formatRelative.js\";\nimport { localize } from \"./gd/_lib/localize.js\";\nimport { match } from \"./gd/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Scottish Gaelic.\n * @language Scottish Gaelic\n * @iso-639-2 gla\n * <AUTHOR> [@leedriscoll](https://github.com/leedriscoll)\n */\nexport const gd = {\n  code: \"gd\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default gd;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}