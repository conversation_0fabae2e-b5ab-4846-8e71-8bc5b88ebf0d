{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\AggiungiCavoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, FormHelperText, Alert, CircularProgress, Typography, Paper } from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AggiungiCavoForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    revisione_ufficiale: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Carica la revisione corrente all'avvio\n  useEffect(() => {\n    const loadRevisioneCorrente = async () => {\n      try {\n        setLoadingRevisione(true);\n        const revisione = await caviService.getRevisioneCorrente(cantiereId);\n        setFormData(prev => ({\n          ...prev,\n          revisione_ufficiale: revisione\n        }));\n      } catch (error) {\n        console.error('Errore nel caricamento della revisione corrente:', error);\n        onError('Errore nel caricamento della revisione corrente');\n      } finally {\n        setLoadingRevisione(false);\n      }\n    };\n    loadRevisioneCorrente();\n  }, [cantiereId, onError]);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      // Validazione completa dei dati del cavo\n      const validation = validateCavoData(formData);\n      if (!validation.isValid) {\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Invia i dati al server\n      await caviService.createCavo(cantiereId, validatedData);\n      onSuccess('Cavo aggiunto con successo');\n\n      // Resetta il form\n      setFormData({\n        id_cavo: '',\n        revisione_ufficiale: validatedData.revisione_ufficiale,\n        // Mantieni la revisione\n        sistema: '',\n        utility: '',\n        colore_cavo: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        sh: '',\n        ubicazione_partenza: '',\n        utenza_partenza: '',\n        descrizione_utenza_partenza: '',\n        ubicazione_arrivo: '',\n        utenza_arrivo: '',\n        descrizione_utenza_arrivo: '',\n        metri_teorici: '',\n        metratura_reale: '0',\n        responsabile_posa: '',\n        id_bobina: '',\n        stato_installazione: 'Da installare'\n      });\n      setFormErrors({});\n      setFormWarnings({});\n    } catch (error) {\n      console.error('Errore durante l\\'aggiunta del cavo:', error);\n      onError(error.detail || 'Errore durante l\\'aggiunta del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleSubmit,\n    noValidate: true,\n    children: loadingRevisione ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [hasWarnings && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 21\n        }, this),\n        sx: {\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          children: \"Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Informazioni Generali\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"id_cavo\",\n              label: \"ID Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_cavo,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.id_cavo,\n              helperText: formErrors.id_cavo,\n              inputProps: {\n                style: {\n                  textTransform: 'uppercase'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"revisione_ufficiale\",\n              label: \"Revisione Ufficiale\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.revisione_ufficiale,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.revisione_ufficiale,\n              helperText: formErrors.revisione_ufficiale\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sistema\",\n              label: \"Sistema\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sistema,\n              onChange: handleFormChange,\n              error: !!formErrors.sistema,\n              helperText: formErrors.sistema\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utility\",\n              label: \"Utility\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utility,\n              onChange: handleFormChange,\n              error: !!formErrors.utility,\n              helperText: formErrors.utility\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Caratteristiche Tecniche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"colore_cavo\",\n              label: \"Colore Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.colore_cavo,\n              onChange: handleFormChange,\n              error: !!formErrors.colore_cavo,\n              helperText: formErrors.colore_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"tipologia\",\n              label: \"Tipologia\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.tipologia,\n              onChange: handleFormChange,\n              error: !!formErrors.tipologia,\n              helperText: formErrors.tipologia\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"n_conduttori\",\n              label: \"Numero Conduttori\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.n_conduttori,\n              onChange: handleFormChange,\n              error: !!formErrors.n_conduttori,\n              helperText: formErrors.n_conduttori || formWarnings.n_conduttori,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.n_conduttori ? 'orange' : undefined\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sezione\",\n              label: \"Sezione\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sezione,\n              onChange: handleFormChange,\n              error: !!formErrors.sezione,\n              helperText: formErrors.sezione || formWarnings.sezione,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.sezione ? 'orange' : undefined\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              error: !!formErrors.sh,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"sh-label\",\n                children: \"Schermato (S/N)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"sh-label\",\n                name: \"sh\",\n                value: formData.sh,\n                label: \"Schermato (S/N)\",\n                onChange: handleFormChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"S\",\n                  children: \"S\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"N\",\n                  children: \"N\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this), formErrors.sh && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: formErrors.sh\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Ubicazione Partenza\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_partenza\",\n              label: \"Ubicazione Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_partenza,\n              helperText: formErrors.ubicazione_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_partenza\",\n              label: \"Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_partenza,\n              helperText: formErrors.utenza_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_partenza\",\n              label: \"Descrizione Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_partenza,\n              helperText: formErrors.descrizione_utenza_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Ubicazione Arrivo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_arrivo\",\n              label: \"Ubicazione Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_arrivo,\n              helperText: formErrors.ubicazione_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_arrivo\",\n              label: \"Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_arrivo,\n              helperText: formErrors.utenza_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_arrivo\",\n              label: \"Descrizione Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_arrivo,\n              helperText: formErrors.descrizione_utenza_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Metratura e Stato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"metri_teorici\",\n              label: \"Metri Teorici\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_teorici,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_teorici,\n              helperText: formErrors.metri_teorici\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              error: !!formErrors.stato_installazione,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"stato-installazione-label\",\n                children: \"Stato Installazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"stato-installazione-label\",\n                name: \"stato_installazione\",\n                value: formData.stato_installazione,\n                label: \"Stato Installazione\",\n                onChange: handleFormChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Da installare\",\n                  children: \"Da installare\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"In corso\",\n                  children: \"In corso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Installato\",\n                  children: \"Installato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 19\n              }, this), formErrors.stato_installazione && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: formErrors.stato_installazione\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 54\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"large\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 26\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 200\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 26\n          }, this) : 'Salva Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n_s(AggiungiCavoForm, \"u/tK8CAJ93wPC9+jOaZKGeBCBkg=\");\n_c = AggiungiCavoForm;\nexport default AggiungiCavoForm;\nvar _c;\n$RefreshReg$(_c, \"AggiungiCavoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "FormHelperText", "<PERSON><PERSON>", "CircularProgress", "Typography", "Paper", "Save", "SaveIcon", "Warning", "WarningIcon", "useNavigate", "caviService", "validateCavoData", "validateField", "isEmpty", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AggiungiCavoForm", "cantiereId", "onSuccess", "onError", "_s", "loading", "setLoading", "loadingRevisione", "setLoadingRevisione", "formData", "setFormData", "id_cavo", "revisione_ufficiale", "sistema", "utility", "colore_cavo", "tipologia", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "responsabile_posa", "id_bobina", "stato_installazione", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "loadRevisioneCorrente", "revisione", "getRevisioneCorrente", "prev", "error", "console", "handleFormChange", "e", "name", "value", "target", "additionalParams", "metriTeorici", "parseFloat", "result", "valid", "message", "warning", "handleSubmit", "preventDefault", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "validatedData", "toUpperCase", "createCavo", "detail", "hasWarnings", "Object", "keys", "length", "component", "onSubmit", "noValidate", "children", "sx", "display", "justifyContent", "my", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "icon", "mb", "variant", "p", "gutterBottom", "container", "spacing", "item", "xs", "sm", "label", "fullWidth", "onChange", "required", "helperText", "inputProps", "style", "textTransform", "FormHelperTextProps", "color", "undefined", "id", "labelId", "mt", "type", "size", "startIcon", "disabled", "min<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/AggiungiCavoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormHelperText,\n  Alert,\n  CircularProgress,\n  Typography,\n  Paper\n} from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\n\nconst AggiungiCavoForm = ({ cantiereId, onSuccess, onError }) => {\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    revisione_ufficiale: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Carica la revisione corrente all'avvio\n  useEffect(() => {\n    const loadRevisioneCorrente = async () => {\n      try {\n        setLoadingRevisione(true);\n        const revisione = await caviService.getRevisioneCorrente(cantiereId);\n        setFormData(prev => ({ ...prev, revisione_ufficiale: revisione }));\n      } catch (error) {\n        console.error('Errore nel caricamento della revisione corrente:', error);\n        onError('Errore nel caricamento della revisione corrente');\n      } finally {\n        setLoadingRevisione(false);\n      }\n    };\n\n    loadRevisioneCorrente();\n  }, [cantiereId, onError]);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      // Validazione completa dei dati del cavo\n      const validation = validateCavoData(formData);\n\n      if (!validation.isValid) {\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Invia i dati al server\n      await caviService.createCavo(cantiereId, validatedData);\n      onSuccess('Cavo aggiunto con successo');\n\n      // Resetta il form\n      setFormData({\n        id_cavo: '',\n        revisione_ufficiale: validatedData.revisione_ufficiale, // Mantieni la revisione\n        sistema: '',\n        utility: '',\n        colore_cavo: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        sh: '',\n        ubicazione_partenza: '',\n        utenza_partenza: '',\n        descrizione_utenza_partenza: '',\n        ubicazione_arrivo: '',\n        utenza_arrivo: '',\n        descrizione_utenza_arrivo: '',\n        metri_teorici: '',\n        metratura_reale: '0',\n        responsabile_posa: '',\n        id_bobina: '',\n        stato_installazione: 'Da installare'\n      });\n      setFormErrors({});\n      setFormWarnings({});\n    } catch (error) {\n      console.error('Errore durante l\\'aggiunta del cavo:', error);\n      onError(error.detail || 'Errore durante l\\'aggiunta del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n\n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n      {loadingRevisione ? (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      ) : (\n        <>\n          {hasWarnings && (\n            <Alert\n              severity=\"warning\"\n              icon={<WarningIcon />}\n              sx={{ mb: 3 }}\n            >\n              <Typography variant=\"subtitle2\">\n                Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\n              </Typography>\n            </Alert>\n          )}\n\n          <Paper sx={{ p: 3, mb: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Informazioni Generali\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.id_cavo}\n                  helperText={formErrors.id_cavo}\n                  inputProps={{ style: { textTransform: 'uppercase' } }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"revisione_ufficiale\"\n                  label=\"Revisione Ufficiale\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.revisione_ufficiale}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.revisione_ufficiale}\n                  helperText={formErrors.revisione_ufficiale}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sistema\"\n                  label=\"Sistema\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sistema}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sistema}\n                  helperText={formErrors.sistema}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: 3, mb: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Caratteristiche Tecniche\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"colore_cavo\"\n                  label=\"Colore Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.colore_cavo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.colore_cavo}\n                  helperText={formErrors.colore_cavo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"Numero Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_conduttori}\n                  helperText={formErrors.n_conduttori || formWarnings.n_conduttori}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.n_conduttori ? 'orange' : undefined }\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione || formWarnings.sezione}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.sezione ? 'orange' : undefined }\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <FormControl fullWidth error={!!formErrors.sh}>\n                  <InputLabel id=\"sh-label\">Schermato (S/N)</InputLabel>\n                  <Select\n                    labelId=\"sh-label\"\n                    name=\"sh\"\n                    value={formData.sh}\n                    label=\"Schermato (S/N)\"\n                    onChange={handleFormChange}\n                  >\n                    <MenuItem value=\"S\">S</MenuItem>\n                    <MenuItem value=\"N\">N</MenuItem>\n                  </Select>\n                  {formErrors.sh && <FormHelperText>{formErrors.sh}</FormHelperText>}\n                </FormControl>\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: 3, mb: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Ubicazione Partenza\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_partenza\"\n                  label=\"Ubicazione Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_partenza}\n                  helperText={formErrors.ubicazione_partenza}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_partenza\"\n                  label=\"Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_partenza}\n                  helperText={formErrors.utenza_partenza}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_partenza\"\n                  label=\"Descrizione Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_partenza}\n                  helperText={formErrors.descrizione_utenza_partenza}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: 3, mb: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Ubicazione Arrivo\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_arrivo\"\n                  label=\"Ubicazione Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_arrivo}\n                  helperText={formErrors.ubicazione_arrivo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_arrivo\"\n                  label=\"Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_arrivo}\n                  helperText={formErrors.utenza_arrivo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_arrivo\"\n                  label=\"Descrizione Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_arrivo}\n                  helperText={formErrors.descrizione_utenza_arrivo}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: 3, mb: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Metratura e Stato\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_teorici\"\n                  label=\"Metri Teorici\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_teorici}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_teorici}\n                  helperText={formErrors.metri_teorici}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth error={!!formErrors.stato_installazione}>\n                  <InputLabel id=\"stato-installazione-label\">Stato Installazione</InputLabel>\n                  <Select\n                    labelId=\"stato-installazione-label\"\n                    name=\"stato_installazione\"\n                    value={formData.stato_installazione}\n                    label=\"Stato Installazione\"\n                    onChange={handleFormChange}\n                  >\n                    <MenuItem value=\"Da installare\">Da installare</MenuItem>\n                    <MenuItem value=\"In corso\">In corso</MenuItem>\n                    <MenuItem value=\"Installato\">Installato</MenuItem>\n                  </Select>\n                  {formErrors.stato_installazione && <FormHelperText>{formErrors.stato_installazione}</FormHelperText>}\n                </FormControl>\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>\n            <Button\n              type=\"submit\"\n              variant=\"contained\"\n              color=\"primary\"\n              size=\"large\"\n              startIcon={<SaveIcon />}\n              disabled={loading}\n              sx={{ minWidth: 200 }}\n            >\n              {loading ? <CircularProgress size={24} /> : 'Salva Cavo'}\n            </Button>\n          </Box>\n        </>\n      )}\n    </Box>\n  );\n};\n\nexport default AggiungiCavoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SAASC,IAAI,IAAIC,QAAQ,EAAEC,OAAO,IAAIC,WAAW,QAAQ,qBAAqB;AAC9E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvF,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC/D,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACqC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC;IACvCuC,OAAO,EAAE,EAAE;IACXC,mBAAmB,EAAE,EAAE;IACvBC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,EAAE,EAAE,EAAE;IACNC,mBAAmB,EAAE,EAAE;IACvBC,eAAe,EAAE,EAAE;IACnBC,2BAA2B,EAAE,EAAE;IAC/BC,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE,EAAE;IACjBC,yBAAyB,EAAE,EAAE;IAC7BC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,GAAG;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,mBAAmB,EAAE;EACvB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC6D,YAAY,EAAEC,eAAe,CAAC,GAAG9D,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM8D,qBAAqB,GAAG,MAAAA,CAAA,KAAY;MACxC,IAAI;QACF3B,mBAAmB,CAAC,IAAI,CAAC;QACzB,MAAM4B,SAAS,GAAG,MAAM5C,WAAW,CAAC6C,oBAAoB,CAACpC,UAAU,CAAC;QACpES,WAAW,CAAC4B,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE1B,mBAAmB,EAAEwB;QAAU,CAAC,CAAC,CAAC;MACpE,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;QACxEpC,OAAO,CAAC,iDAAiD,CAAC;MAC5D,CAAC,SAAS;QACRK,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAED2B,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,CAAClC,UAAU,EAAEE,OAAO,CAAC,CAAC;;EAEzB;EACA,MAAMsC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACAnC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACkC,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,MAAME,gBAAgB,GAAG,CAAC,CAAC;IAC3B,IAAIH,IAAI,KAAK,iBAAiB,EAAE;MAC9BG,gBAAgB,CAACC,YAAY,GAAGC,UAAU,CAACvC,QAAQ,CAACiB,aAAa,IAAI,CAAC,CAAC;IACzE;IAEA,MAAMuB,MAAM,GAAGvD,aAAa,CAACiD,IAAI,EAAEC,KAAK,EAAEE,gBAAgB,CAAC;;IAE3D;IACAd,aAAa,CAACM,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACK,IAAI,GAAG,CAACM,MAAM,CAACC,KAAK,GAAGD,MAAM,CAACE,OAAO,GAAG;IAC3C,CAAC,CAAC,CAAC;;IAEH;IACAjB,eAAe,CAACI,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACK,IAAI,GAAGM,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACE,OAAO,GAAG;IAC5C,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,YAAY,GAAG,MAAOX,CAAC,IAAK;IAChCA,CAAC,CAACY,cAAc,CAAC,CAAC;IAClBhD,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAMiD,UAAU,GAAG9D,gBAAgB,CAACgB,QAAQ,CAAC;MAE7C,IAAI,CAAC8C,UAAU,CAACC,OAAO,EAAE;QACvBxB,aAAa,CAACuB,UAAU,CAACE,MAAM,CAAC;QAChCvB,eAAe,CAACqB,UAAU,CAACG,QAAQ,CAAC;QACpCpD,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAMqD,aAAa,GAAGJ,UAAU,CAACI,aAAa;;MAE9C;MACAA,aAAa,CAAChD,OAAO,GAAGgD,aAAa,CAAChD,OAAO,CAACiD,WAAW,CAAC,CAAC;;MAE3D;MACA,MAAMpE,WAAW,CAACqE,UAAU,CAAC5D,UAAU,EAAE0D,aAAa,CAAC;MACvDzD,SAAS,CAAC,4BAA4B,CAAC;;MAEvC;MACAQ,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,mBAAmB,EAAE+C,aAAa,CAAC/C,mBAAmB;QAAE;QACxDC,OAAO,EAAE,EAAE;QACXC,OAAO,EAAE,EAAE;QACXC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,EAAE;QACXC,EAAE,EAAE,EAAE;QACNC,mBAAmB,EAAE,EAAE;QACvBC,eAAe,EAAE,EAAE;QACnBC,2BAA2B,EAAE,EAAE;QAC/BC,iBAAiB,EAAE,EAAE;QACrBC,aAAa,EAAE,EAAE;QACjBC,yBAAyB,EAAE,EAAE;QAC7BC,aAAa,EAAE,EAAE;QACjBC,eAAe,EAAE,GAAG;QACpBC,iBAAiB,EAAE,EAAE;QACrBC,SAAS,EAAE,EAAE;QACbC,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACFE,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DpC,OAAO,CAACoC,KAAK,CAACuB,MAAM,IAAI,qCAAqC,CAAC;IAChE,CAAC,SAAS;MACRxD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyD,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAChC,YAAY,CAAC,CAACiC,MAAM,GAAG,CAAC;EAExD,oBACErE,OAAA,CAACvB,GAAG;IAAC6F,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAEf,YAAa;IAACgB,UAAU;IAAAC,QAAA,EACrD/D,gBAAgB,gBACfV,OAAA,CAACvB,GAAG;MAACiG,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAC5DzE,OAAA,CAACb,gBAAgB;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,gBAENjF,OAAA,CAAAE,SAAA;MAAAuE,QAAA,GACGP,WAAW,iBACVlE,OAAA,CAACd,KAAK;QACJgG,QAAQ,EAAC,SAAS;QAClBC,IAAI,eAAEnF,OAAA,CAACP,WAAW;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBP,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,eAEdzE,OAAA,CAACZ,UAAU;UAACiG,OAAO,EAAC,WAAW;UAAAZ,QAAA,EAAC;QAEhC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR,eAEDjF,OAAA,CAACX,KAAK;QAACqF,EAAE,EAAE;UAAEY,CAAC,EAAE,CAAC;UAAEF,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,gBACzBzE,OAAA,CAACZ,UAAU;UAACiG,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAd,QAAA,EAAC;QAEtC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjF,OAAA,CAACpB,IAAI;UAAC4G,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAhB,QAAA,gBACzBzE,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,SAAS;cACd+C,KAAK,EAAC,SAAS;cACfC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACE,OAAQ;cACxBiF,QAAQ,EAAEnD,gBAAiB;cAC3BoD,QAAQ;cACRtD,KAAK,EAAE,CAAC,CAACR,UAAU,CAACpB,OAAQ;cAC5BmF,UAAU,EAAE/D,UAAU,CAACpB,OAAQ;cAC/BoF,UAAU,EAAE;gBAAEC,KAAK,EAAE;kBAAEC,aAAa,EAAE;gBAAY;cAAE;YAAE;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,qBAAqB;cAC1B+C,KAAK,EAAC,qBAAqB;cAC3BC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACG,mBAAoB;cACpCgF,QAAQ,EAAEnD,gBAAiB;cAC3BoD,QAAQ;cACRtD,KAAK,EAAE,CAAC,CAACR,UAAU,CAACnB,mBAAoB;cACxCkF,UAAU,EAAE/D,UAAU,CAACnB;YAAoB;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,SAAS;cACd+C,KAAK,EAAC,SAAS;cACfC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACI,OAAQ;cACxB+E,QAAQ,EAAEnD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAAClB,OAAQ;cAC5BiF,UAAU,EAAE/D,UAAU,CAAClB;YAAQ;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,SAAS;cACd+C,KAAK,EAAC,SAAS;cACfC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACK,OAAQ;cACxB8E,QAAQ,EAAEnD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACjB,OAAQ;cAC5BgF,UAAU,EAAE/D,UAAU,CAACjB;YAAQ;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERjF,OAAA,CAACX,KAAK;QAACqF,EAAE,EAAE;UAAEY,CAAC,EAAE,CAAC;UAAEF,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,gBACzBzE,OAAA,CAACZ,UAAU;UAACiG,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAd,QAAA,EAAC;QAEtC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjF,OAAA,CAACpB,IAAI;UAAC4G,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAhB,QAAA,gBACzBzE,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,aAAa;cAClB+C,KAAK,EAAC,aAAa;cACnBC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACM,WAAY;cAC5B6E,QAAQ,EAAEnD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAAChB,WAAY;cAChC+E,UAAU,EAAE/D,UAAU,CAAChB;YAAY;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,WAAW;cAChB+C,KAAK,EAAC,WAAW;cACjBC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACO,SAAU;cAC1B4E,QAAQ,EAAEnD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACf,SAAU;cAC9B8E,UAAU,EAAE/D,UAAU,CAACf;YAAU;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,cAAc;cACnB+C,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACQ,YAAa;cAC7B2E,QAAQ,EAAEnD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACd,YAAa;cACjC6E,UAAU,EAAE/D,UAAU,CAACd,YAAY,IAAIgB,YAAY,CAAChB,YAAa;cACjEiF,mBAAmB,EAAE;gBACnBF,KAAK,EAAE;kBAAEG,KAAK,EAAElE,YAAY,CAAChB,YAAY,GAAG,QAAQ,GAAGmF;gBAAU;cACnE;YAAE;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,SAAS;cACd+C,KAAK,EAAC,SAAS;cACfC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACS,OAAQ;cACxB0E,QAAQ,EAAEnD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACb,OAAQ;cAC5B4E,UAAU,EAAE/D,UAAU,CAACb,OAAO,IAAIe,YAAY,CAACf,OAAQ;cACvDgF,mBAAmB,EAAE;gBACnBF,KAAK,EAAE;kBAAEG,KAAK,EAAElE,YAAY,CAACf,OAAO,GAAG,QAAQ,GAAGkF;gBAAU;cAC9D;YAAE;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACnB,WAAW;cAACiH,SAAS;cAACpD,KAAK,EAAE,CAAC,CAACR,UAAU,CAACZ,EAAG;cAAAmD,QAAA,gBAC5CzE,OAAA,CAAClB,UAAU;gBAAC0H,EAAE,EAAC,UAAU;gBAAA/B,QAAA,EAAC;cAAe;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtDjF,OAAA,CAACjB,MAAM;gBACL0H,OAAO,EAAC,UAAU;gBAClB3D,IAAI,EAAC,IAAI;gBACTC,KAAK,EAAEnC,QAAQ,CAACU,EAAG;gBACnBuE,KAAK,EAAC,iBAAiB;gBACvBE,QAAQ,EAAEnD,gBAAiB;gBAAA6B,QAAA,gBAE3BzE,OAAA,CAAChB,QAAQ;kBAAC+D,KAAK,EAAC,GAAG;kBAAA0B,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAChCjF,OAAA,CAAChB,QAAQ;kBAAC+D,KAAK,EAAC,GAAG;kBAAA0B,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,EACR/C,UAAU,CAACZ,EAAE,iBAAItB,OAAA,CAACf,cAAc;gBAAAwF,QAAA,EAAEvC,UAAU,CAACZ;cAAE;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERjF,OAAA,CAACX,KAAK;QAACqF,EAAE,EAAE;UAAEY,CAAC,EAAE,CAAC;UAAEF,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,gBACzBzE,OAAA,CAACZ,UAAU;UAACiG,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAd,QAAA,EAAC;QAEtC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjF,OAAA,CAACpB,IAAI;UAAC4G,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAhB,QAAA,gBACzBzE,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,qBAAqB;cAC1B+C,KAAK,EAAC,qBAAqB;cAC3BC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACW,mBAAoB;cACpCwE,QAAQ,EAAEnD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACX,mBAAoB;cACxC0E,UAAU,EAAE/D,UAAU,CAACX;YAAoB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,iBAAiB;cACtB+C,KAAK,EAAC,iBAAiB;cACvBC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACY,eAAgB;cAChCuE,QAAQ,EAAEnD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACV,eAAgB;cACpCyE,UAAU,EAAE/D,UAAU,CAACV;YAAgB;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,6BAA6B;cAClC+C,KAAK,EAAC,6BAA6B;cACnCC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACa,2BAA4B;cAC5CsE,QAAQ,EAAEnD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACT,2BAA4B;cAChDwE,UAAU,EAAE/D,UAAU,CAACT;YAA4B;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERjF,OAAA,CAACX,KAAK;QAACqF,EAAE,EAAE;UAAEY,CAAC,EAAE,CAAC;UAAEF,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,gBACzBzE,OAAA,CAACZ,UAAU;UAACiG,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAd,QAAA,EAAC;QAEtC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjF,OAAA,CAACpB,IAAI;UAAC4G,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAhB,QAAA,gBACzBzE,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,mBAAmB;cACxB+C,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACc,iBAAkB;cAClCqE,QAAQ,EAAEnD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACR,iBAAkB;cACtCuE,UAAU,EAAE/D,UAAU,CAACR;YAAkB;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,eAAe;cACpB+C,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACe,aAAc;cAC9BoE,QAAQ,EAAEnD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACP,aAAc;cAClCsE,UAAU,EAAE/D,UAAU,CAACP;YAAc;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,2BAA2B;cAChC+C,KAAK,EAAC,2BAA2B;cACjCC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACgB,yBAA0B;cAC1CmE,QAAQ,EAAEnD,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACN,yBAA0B;cAC9CqE,UAAU,EAAE/D,UAAU,CAACN;YAA0B;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERjF,OAAA,CAACX,KAAK;QAACqF,EAAE,EAAE;UAAEY,CAAC,EAAE,CAAC;UAAEF,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,gBACzBzE,OAAA,CAACZ,UAAU;UAACiG,OAAO,EAAC,IAAI;UAACE,YAAY;UAAAd,QAAA,EAAC;QAEtC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjF,OAAA,CAACpB,IAAI;UAAC4G,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAhB,QAAA,gBACzBzE,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACtB,SAAS;cACRoE,IAAI,EAAC,eAAe;cACpB+C,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTT,OAAO,EAAC,UAAU;cAClBtC,KAAK,EAAEnC,QAAQ,CAACiB,aAAc;cAC9BkE,QAAQ,EAAEnD,gBAAiB;cAC3BoD,QAAQ;cACRtD,KAAK,EAAE,CAAC,CAACR,UAAU,CAACL,aAAc;cAClCoE,UAAU,EAAE/D,UAAU,CAACL;YAAc;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjF,OAAA,CAACpB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACvBzE,OAAA,CAACnB,WAAW;cAACiH,SAAS;cAACpD,KAAK,EAAE,CAAC,CAACR,UAAU,CAACD,mBAAoB;cAAAwC,QAAA,gBAC7DzE,OAAA,CAAClB,UAAU;gBAAC0H,EAAE,EAAC,2BAA2B;gBAAA/B,QAAA,EAAC;cAAmB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC3EjF,OAAA,CAACjB,MAAM;gBACL0H,OAAO,EAAC,2BAA2B;gBACnC3D,IAAI,EAAC,qBAAqB;gBAC1BC,KAAK,EAAEnC,QAAQ,CAACqB,mBAAoB;gBACpC4D,KAAK,EAAC,qBAAqB;gBAC3BE,QAAQ,EAAEnD,gBAAiB;gBAAA6B,QAAA,gBAE3BzE,OAAA,CAAChB,QAAQ;kBAAC+D,KAAK,EAAC,eAAe;kBAAA0B,QAAA,EAAC;gBAAa;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACxDjF,OAAA,CAAChB,QAAQ;kBAAC+D,KAAK,EAAC,UAAU;kBAAA0B,QAAA,EAAC;gBAAQ;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC9CjF,OAAA,CAAChB,QAAQ;kBAAC+D,KAAK,EAAC,YAAY;kBAAA0B,QAAA,EAAC;gBAAU;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,EACR/C,UAAU,CAACD,mBAAmB,iBAAIjC,OAAA,CAACf,cAAc;gBAAAwF,QAAA,EAAEvC,UAAU,CAACD;cAAmB;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERjF,OAAA,CAACvB,GAAG;QAACiG,EAAE,EAAE;UAAEgC,EAAE,EAAE,CAAC;UAAE/B,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAS,CAAE;QAAAH,QAAA,eAC5DzE,OAAA,CAACrB,MAAM;UACLgI,IAAI,EAAC,QAAQ;UACbtB,OAAO,EAAC,WAAW;UACnBiB,KAAK,EAAC,SAAS;UACfM,IAAI,EAAC,OAAO;UACZC,SAAS,eAAE7G,OAAA,CAACT,QAAQ;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxB6B,QAAQ,EAAEtG,OAAQ;UAClBkE,EAAE,EAAE;YAAEqC,QAAQ,EAAE;UAAI,CAAE;UAAAtC,QAAA,EAErBjE,OAAO,gBAAGR,OAAA,CAACb,gBAAgB;YAACyH,IAAI,EAAE;UAAG;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAY;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,eACN;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1E,EAAA,CAvbIJ,gBAAgB;AAAA6G,EAAA,GAAhB7G,gBAAgB;AAybtB,eAAeA,gBAAgB;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}