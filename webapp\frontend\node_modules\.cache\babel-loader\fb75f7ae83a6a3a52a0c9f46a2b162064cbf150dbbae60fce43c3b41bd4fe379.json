{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeListRivoluzionato.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, CircularProgress, Tooltip, List, ListItem, ListItemText, Accordion, AccordionSummary, AccordionDetails, Stack } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Assignment as AssignIcon, Person as PersonIcon, Email as EmailIcon, Phone as PhoneIcon, ExpandMore as ExpandMoreIcon, CheckCircle as CheckCircleIcon, Verified as VerifiedIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComandeListRivoluzionato = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      // Non serve più salvare le comande in uno stato separato\n      // Le comande vengono caricate per responsabile\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    if (cantiereId) {\n      loadResponsabili();\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]);\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenResponsabileDialog(true);\n  };\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDeleteResponsabile = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n  const getStatoColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'IN_CORSO': 'primary',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600,\n          color: 'primary.main'\n        },\n        children: cantiereName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 9\n    }, this), statistiche && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        bgcolor: 'grey.50'\n      },\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 4,\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n            color: \"primary\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.responsabili_attivi || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            color: \"info\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.totale_comande || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Totale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            color: \"warning\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_in_corso || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"In Corso\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(VerifiedIcon, {\n            color: \"success\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_completate || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Completate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 32,\n              height: 32,\n              borderRadius: '50%',\n              bgcolor: statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.8 ? 'success.main' : statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.5 ? 'warning.main' : 'error.main',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              fontWeight: \"bold\",\n              color: \"white\",\n              children: [statistiche.totale_comande > 0 ? Math.round(statistiche.comande_completate / statistiche.totale_comande * 100) : 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"medium\",\n              sx: {\n                lineHeight: 1\n              },\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [statistiche.comande_create || 0, \" create\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Responsabili del Cantiere\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 26\n            }, this),\n            onClick: () => handleOpenResponsabileDialog('create'),\n            sx: {\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3,\n              py: 1\n            },\n            children: \"Inserisci Responsabile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this), loadingResponsabili ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          py: 4,\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: responsabili.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 0,\n            sx: {\n              p: 6,\n              textAlign: 'center',\n              backgroundColor: 'grey.50',\n              border: '1px dashed',\n              borderColor: 'grey.300'\n            },\n            children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n              sx: {\n                fontSize: 48,\n                color: 'grey.400',\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Nessun responsabile configurato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 3\n              },\n              children: \"Aggiungi il primo responsabile per iniziare a gestire le comande\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 32\n              }, this),\n              onClick: () => handleOpenResponsabileDialog('create'),\n              sx: {\n                textTransform: 'none'\n              },\n              children: \"Inserisci Primo Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 17\n          }, this) : responsabili.map(responsabile => /*#__PURE__*/_jsxDEV(Accordion, {\n            sx: {\n              mb: 2,\n              '&:before': {\n                display: 'none'\n              },\n              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n              border: '1px solid',\n              borderColor: 'grey.200'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n              expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 35\n              }, this),\n              sx: {\n                '&:hover': {\n                  backgroundColor: 'grey.50'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                width: \"100%\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 2,\n                  children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                    color: \"primary\",\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      sx: {\n                        fontWeight: 500\n                      },\n                      children: responsabile.nome_responsabile\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      gap: 3,\n                      mt: 0.5,\n                      children: [responsabile.email && /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 0.5,\n                        children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n                          fontSize: \"small\",\n                          color: \"action\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 423,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"text.secondary\",\n                          children: responsabile.email\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 424,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 422,\n                        columnNumber: 33\n                      }, this), responsabile.telefono && /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 0.5,\n                        children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                          fontSize: \"small\",\n                          color: \"action\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 431,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"text.secondary\",\n                          children: responsabile.telefono\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 432,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 430,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"contained\",\n                    size: \"small\",\n                    startIcon: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 445,\n                      columnNumber: 40\n                    }, this),\n                    onClick: () => {\n                      // Pre-seleziona il responsabile nel dialog di creazione comanda\n                      setOpenCreaConCavi(true);\n                    },\n                    sx: {\n                      textTransform: 'none',\n                      fontWeight: 500,\n                      px: 2,\n                      py: 0.5,\n                      minWidth: 'auto'\n                    },\n                    children: Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) ? comandePerResponsabile[responsabile.id_responsabile].length : 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Modifica responsabile\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleOpenResponsabileDialog('edit', responsabile),\n                      sx: {\n                        '&:hover': {\n                          backgroundColor: 'primary.light',\n                          color: 'white'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 471,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Elimina responsabile\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleDeleteResponsabile(responsabile.id_responsabile),\n                      sx: {\n                        '&:hover': {\n                          backgroundColor: 'error.light',\n                          color: 'white'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 485,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n              sx: {\n                pt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 500,\n                  mb: 2\n                },\n                children: \"Comande Assegnate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 23\n              }, this), !Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) || comandePerResponsabile[responsabile.id_responsabile].length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  textAlign: 'center',\n                  backgroundColor: 'grey.50',\n                  borderRadius: 1,\n                  border: '1px dashed',\n                  borderColor: 'grey.300'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Nessuna comanda assegnata a questo responsabile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 25\n              }, this) : /*#__PURE__*/_jsxDEV(List, {\n                dense: true,\n                children: Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) && comandePerResponsabile[responsabile.id_responsabile].map(comanda => /*#__PURE__*/_jsxDEV(ListItem, {\n                  divider: true,\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        fontWeight: \"bold\",\n                        children: comanda.codice_comanda\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 519,\n                        columnNumber: 37\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: getTipoComandaLabel(comanda.tipo_comanda),\n                        size: \"small\",\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 522,\n                        columnNumber: 37\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: comanda.stato || 'CREATA',\n                        size: \"small\",\n                        color: getStatoColor(comanda.stato)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 527,\n                        columnNumber: 37\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 518,\n                      columnNumber: 35\n                    }, this),\n                    secondary: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      children: [comanda.descrizione || 'Nessuna descrizione', comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 535,\n                      columnNumber: 35\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 31\n                  }, this)\n                }, comanda.codice_comanda, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 29\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 21\n            }, this)]\n          }, responsabile.id_responsabile, true, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openResponsabileDialog,\n      onClose: handleCloseResponsabileDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nome Responsabile\",\n            value: formDataResponsabile.nome_responsabile,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              nome_responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: formDataResponsabile.email,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Email per notifiche (opzionale se inserisci telefono)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Telefono\",\n            value: formDataResponsabile.telefono,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              telefono: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Numero per SMS (opzionale se inserisci email)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseResponsabileDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 606,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitResponsabile,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 605,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: () => {\n        loadComande();\n        loadStatistiche();\n        loadResponsabili();\n        setOpenCreaConCavi(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 627,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 238,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeListRivoluzionato, \"XFGec8M35ALWmJ1j18U8X4nzJHk=\");\n_c = ComandeListRivoluzionato;\nexport default ComandeListRivoluzionato;\nvar _c;\n$RefreshReg$(_c, \"ComandeListRivoluzionato\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON><PERSON>", "List", "ListItem", "ListItemText", "Accordion", "AccordionSummary", "AccordionDetails", "<PERSON><PERSON>", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Assignment", "AssignIcon", "Person", "PersonIcon", "Email", "EmailIcon", "Phone", "PhoneIcon", "ExpandMore", "ExpandMoreIcon", "CheckCircle", "CheckCircleIcon", "Verified", "VerifiedIcon", "comandeService", "responsabiliService", "CreaComandaConCavi", "jsxDEV", "_jsxDEV", "ComandeListRivoluzionato", "cantiereId", "cantiereName", "_s", "loading", "setLoading", "error", "setError", "statistiche", "setStatistiche", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "loadComande", "err", "console", "loadStatistiche", "stats", "getStatisticheComande", "loadResponsabili", "data", "getResponsabiliCantiere", "loadComandePerResponsabili", "_err$response", "_err$response$data", "errorMessage", "response", "detail", "message", "responsabiliList", "comandeMap", "responsabile", "getComandeByResponsabile", "comande", "Array", "isArray", "id_responsabile", "handleOpenResponsabileDialog", "mode", "handleCloseResponsabileDialog", "handleSubmitResponsabile", "trim", "createResponsabile", "updateResponsabile", "handleDeleteResponsabile", "idResponsabile", "window", "confirm", "deleteResponsabile", "getTipoComandaLabel", "tipo", "labels", "getStatoColor", "stato", "colors", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "variant", "fontWeight", "color", "severity", "bgcolor", "direction", "spacing", "flexWrap", "fontSize", "lineHeight", "responsabili_attivi", "totale_comande", "comande_in_corso", "comande_completate", "width", "height", "borderRadius", "Math", "round", "comande_create", "startIcon", "onClick", "textTransform", "px", "py", "length", "elevation", "textAlign", "backgroundColor", "border", "borderColor", "gutterBottom", "map", "boxShadow", "expandIcon", "gap", "mt", "e", "stopPropagation", "size", "min<PERSON><PERSON><PERSON>", "title", "pt", "dense", "comanda", "divider", "primary", "codice_comanda", "label", "tipo_comanda", "secondary", "descrizione", "data_creazione", "Date", "toLocaleDateString", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "pb", "value", "onChange", "target", "margin", "required", "type", "helperText", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeListRivoluzionato.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Alert,\n  CircularProgress,\n  Tooltip,\n  List,\n  ListItem,\n  ListItemText,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Stack\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Assignment as AssignIcon,\n  Person as PersonIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  ExpandMore as ExpandMoreIcon,\n  CheckCircle as CheckCircleIcon,\n  Verified as VerifiedIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\n\nconst ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      // Non serve più salvare le comande in uno stato separato\n      // Le comande vengono caricate per responsabile\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    if (cantiereId) {\n      loadResponsabili();\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]);\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    \n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    \n    setOpenResponsabileDialog(true);\n  };\n\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      \n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      \n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDeleteResponsabile = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  const getStatoColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'IN_CORSO': 'primary',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n          {cantiereName}\n        </Typography>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Statistiche in stile Visualizza Cavi */}\n      {statistiche && (\n        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n          <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n            {/* Responsabili */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <PersonIcon color=\"primary\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.responsabili_attivi || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Responsabili\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Totale Comande */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <AssignIcon color=\"info\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.totale_comande || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Totale\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* In Corso */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <CheckCircleIcon color=\"warning\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_in_corso || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  In Corso\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Completate */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <VerifiedIcon color=\"success\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_completate || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Completate\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Percentuale completamento */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <Box sx={{\n                width: 32,\n                height: 32,\n                borderRadius: '50%',\n                bgcolor: (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.8 ? 'success.main' :\n                         (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.5 ? 'warning.main' : 'error.main',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}>\n                <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n                  {statistiche.totale_comande > 0 ? Math.round((statistiche.comande_completate / statistiche.totale_comande) * 100) : 0}%\n                </Typography>\n              </Box>\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n                  Completamento\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {statistiche.comande_create || 0} create\n                </Typography>\n              </Box>\n            </Stack>\n          </Stack>\n        </Paper>\n      )}\n\n      {/* Sezione Responsabili - Elemento Principale */}\n      <Box>\n        <Box>\n          {/* Toolbar Responsabili */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Responsabili del Cantiere\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={() => handleOpenResponsabileDialog('create')}\n              sx={{\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1\n              }}\n            >\n              Inserisci Responsabile\n            </Button>\n          </Box>\n\n          {loadingResponsabili ? (\n            <Box display=\"flex\" justifyContent=\"center\" py={4}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              {responsabili.length === 0 ? (\n                <Paper\n                  elevation={0}\n                  sx={{\n                    p: 6,\n                    textAlign: 'center',\n                    backgroundColor: 'grey.50',\n                    border: '1px dashed',\n                    borderColor: 'grey.300'\n                  }}\n                >\n                  <PersonIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n                  <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                    Nessun responsabile configurato\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                    Aggiungi il primo responsabile per iniziare a gestire le comande\n                  </Typography>\n                  <Button\n                    variant=\"contained\"\n                    startIcon={<AddIcon />}\n                    onClick={() => handleOpenResponsabileDialog('create')}\n                    sx={{ textTransform: 'none' }}\n                  >\n                    Inserisci Primo Responsabile\n                  </Button>\n                </Paper>\n              ) : (\n                responsabili.map((responsabile) => (\n                  <Accordion\n                    key={responsabile.id_responsabile}\n                    sx={{\n                      mb: 2,\n                      '&:before': { display: 'none' },\n                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                      border: '1px solid',\n                      borderColor: 'grey.200'\n                    }}\n                  >\n                    <AccordionSummary\n                      expandIcon={<ExpandMoreIcon />}\n                      sx={{\n                        '&:hover': {\n                          backgroundColor: 'grey.50'\n                        }\n                      }}\n                    >\n                      <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" width=\"100%\">\n                        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                          <PersonIcon color=\"primary\" sx={{ fontSize: 28 }} />\n                          <Box>\n                            <Typography variant=\"h6\" sx={{ fontWeight: 500 }}>\n                              {responsabile.nome_responsabile}\n                            </Typography>\n                            <Box display=\"flex\" gap={3} mt={0.5}>\n                              {responsabile.email && (\n                                <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n                                  <EmailIcon fontSize=\"small\" color=\"action\" />\n                                  <Typography variant=\"body2\" color=\"text.secondary\">\n                                    {responsabile.email}\n                                  </Typography>\n                                </Box>\n                              )}\n                              {responsabile.telefono && (\n                                <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n                                  <PhoneIcon fontSize=\"small\" color=\"action\" />\n                                  <Typography variant=\"body2\" color=\"text.secondary\">\n                                    {responsabile.telefono}\n                                  </Typography>\n                                </Box>\n                              )}\n                            </Box>\n                          </Box>\n                        </Box>\n\n                        <Box display=\"flex\" alignItems=\"center\" gap={1} onClick={(e) => e.stopPropagation()}>\n                          <Button\n                            variant=\"contained\"\n                            size=\"small\"\n                            startIcon={<AssignIcon />}\n                            onClick={() => {\n                              // Pre-seleziona il responsabile nel dialog di creazione comanda\n                              setOpenCreaConCavi(true);\n                            }}\n                            sx={{\n                              textTransform: 'none',\n                              fontWeight: 500,\n                              px: 2,\n                              py: 0.5,\n                              minWidth: 'auto'\n                            }}\n                          >\n                            {Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) ? comandePerResponsabile[responsabile.id_responsabile].length : 0}\n                          </Button>\n                          <Tooltip title=\"Modifica responsabile\">\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleOpenResponsabileDialog('edit', responsabile)}\n                              sx={{\n                                '&:hover': {\n                                  backgroundColor: 'primary.light',\n                                  color: 'white'\n                                }\n                              }}\n                            >\n                              <EditIcon fontSize=\"small\" />\n                            </IconButton>\n                          </Tooltip>\n                          <Tooltip title=\"Elimina responsabile\">\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleDeleteResponsabile(responsabile.id_responsabile)}\n                              sx={{\n                                '&:hover': {\n                                  backgroundColor: 'error.light',\n                                  color: 'white'\n                                }\n                              }}\n                            >\n                              <DeleteIcon fontSize=\"small\" />\n                            </IconButton>\n                          </Tooltip>\n                        </Box>\n                      </Box>\n                    </AccordionSummary>\n\n                    <AccordionDetails sx={{ pt: 2 }}>\n                      <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>\n                        Comande Assegnate\n                      </Typography>\n\n                      {(!Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) || comandePerResponsabile[responsabile.id_responsabile].length === 0) ? (\n                        <Box\n                          sx={{\n                            p: 3,\n                            textAlign: 'center',\n                            backgroundColor: 'grey.50',\n                            borderRadius: 1,\n                            border: '1px dashed',\n                            borderColor: 'grey.300'\n                          }}\n                        >\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Nessuna comanda assegnata a questo responsabile\n                          </Typography>\n                        </Box>\n                      ) : (\n                        <List dense>\n                          {Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) && comandePerResponsabile[responsabile.id_responsabile].map((comanda) => (\n                            <ListItem key={comanda.codice_comanda} divider>\n                              <ListItemText\n                                primary={\n                                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                                    <Typography variant=\"body2\" fontWeight=\"bold\">\n                                      {comanda.codice_comanda}\n                                    </Typography>\n                                    <Chip\n                                      label={getTipoComandaLabel(comanda.tipo_comanda)}\n                                      size=\"small\"\n                                      variant=\"outlined\"\n                                    />\n                                    <Chip\n                                      label={comanda.stato || 'CREATA'}\n                                      size=\"small\"\n                                      color={getStatoColor(comanda.stato)}\n                                    />\n                                  </Box>\n                                }\n                                secondary={\n                                  <Typography variant=\"body2\" color=\"textSecondary\">\n                                    {comanda.descrizione || 'Nessuna descrizione'}\n                                    {comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`}\n                                  </Typography>\n                                }\n                              />\n                            </ListItem>\n                          ))}\n                        </List>\n                      )}\n                    </AccordionDetails>\n                  </Accordion>\n                ))\n              )}\n            </Box>\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per creazione/modifica responsabile */}\n      <Dialog\n        open={openResponsabileDialog}\n        onClose={handleCloseResponsabileDialog}\n        maxWidth=\"sm\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nome Responsabile\"\n              value={formDataResponsabile.nome_responsabile}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              variant=\"outlined\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={formDataResponsabile.email}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Email per notifiche (opzionale se inserisci telefono)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Telefono\"\n              value={formDataResponsabile.telefono}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Numero per SMS (opzionale se inserisci email)\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseResponsabileDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitResponsabile}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog CreaComandaConCavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={() => {\n          loadComande();\n          loadStatistiche();\n          loadResponsabili();\n          setOpenCreaConCavi(false);\n        }}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeListRivoluzionato;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,KAAK,QACA,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,UAAU,EACxBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,EAC5BC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjE;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsD,KAAK,EAAEC,QAAQ,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0D,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACgE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACkE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACoE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrE,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAACsE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACwE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzE,QAAQ,CAAC;IAC/D0E,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChB;MACA;MACAE,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOuB,GAAG,EAAE;MACZC,OAAO,CAACzB,KAAK,CAAC,uCAAuC,EAAEwB,GAAG,CAAC;MAC3DvB,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,KAAK,GAAG,MAAMtC,cAAc,CAACuC,qBAAqB,CAACjC,UAAU,CAAC;MACpEQ,cAAc,CAACwB,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOH,GAAG,EAAE;MACZC,OAAO,CAACzB,KAAK,CAAC,2CAA2C,EAAEwB,GAAG,CAAC;IACjE;EACF,CAAC;EAED,MAAMK,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFpB,sBAAsB,CAAC,IAAI,CAAC;MAC5BR,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM6B,IAAI,GAAG,MAAMxC,mBAAmB,CAACyC,uBAAuB,CAACpC,UAAU,CAAC;MAC1EY,eAAe,CAACuB,IAAI,IAAI,EAAE,CAAC;MAC3B,MAAME,0BAA0B,CAACF,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAON,GAAG,EAAE;MAAA,IAAAS,aAAA,EAAAC,kBAAA;MACZT,OAAO,CAACzB,KAAK,CAAC,0CAA0C,EAAEwB,GAAG,CAAC;MAC9D,MAAMW,YAAY,GAAG,EAAAF,aAAA,GAAAT,GAAG,CAACY,QAAQ,cAAAH,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcH,IAAI,cAAAI,kBAAA,uBAAlBA,kBAAA,CAAoBG,MAAM,KAAIb,GAAG,CAACc,OAAO,IAAI,yCAAyC;MAC3GrC,QAAQ,CAAC,4CAA4CkC,YAAY,EAAE,CAAC;IACtE,CAAC,SAAS;MACR1B,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA9D,SAAS,CAAC,MAAM;IACd,IAAIgD,UAAU,EAAE;MACdkC,gBAAgB,CAAC,CAAC;MAClBN,WAAW,CAAC,CAAC;MACbG,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC/B,UAAU,CAAC,CAAC;EAEhB,MAAMqC,0BAA0B,GAAG,MAAOO,gBAAgB,IAAK;IAC7D,IAAI;MACF,MAAMC,UAAU,GAAG,CAAC,CAAC;MACrB,KAAK,MAAMC,YAAY,IAAIF,gBAAgB,EAAE;QAC3C,IAAI;UACF,MAAMH,QAAQ,GAAG,MAAM/C,cAAc,CAACqD,wBAAwB,CAAC/C,UAAU,EAAE8C,YAAY,CAACrB,iBAAiB,CAAC;UAC1G;UACA,IAAIuB,OAAO,GAAG,EAAE;UAChB,IAAIP,QAAQ,IAAIQ,KAAK,CAACC,OAAO,CAACT,QAAQ,CAAC,EAAE;YACvCO,OAAO,GAAGP,QAAQ;UACpB,CAAC,MAAM,IAAIA,QAAQ,IAAIA,QAAQ,CAACO,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACT,QAAQ,CAACO,OAAO,CAAC,EAAE;YAC1EA,OAAO,GAAGP,QAAQ,CAACO,OAAO;UAC5B,CAAC,MAAM,IAAIP,QAAQ,IAAIA,QAAQ,CAACN,IAAI,IAAIc,KAAK,CAACC,OAAO,CAACT,QAAQ,CAACN,IAAI,CAAC,EAAE;YACpEa,OAAO,GAAGP,QAAQ,CAACN,IAAI;UACzB;UACAU,UAAU,CAACC,YAAY,CAACK,eAAe,CAAC,GAAGH,OAAO;QACpD,CAAC,CAAC,OAAOnB,GAAG,EAAE;UACZC,OAAO,CAACzB,KAAK,CAAC,sCAAsCyC,YAAY,CAACrB,iBAAiB,GAAG,EAAEI,GAAG,CAAC;UAC3FgB,UAAU,CAACC,YAAY,CAACK,eAAe,CAAC,GAAG,EAAE;QAC/C;MACF;MACAnC,yBAAyB,CAAC6B,UAAU,CAAC;IACvC,CAAC,CAAC,OAAOhB,GAAG,EAAE;MACZC,OAAO,CAACzB,KAAK,CAAC,uCAAuC,EAAEwB,GAAG,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMuB,4BAA4B,GAAGA,CAACC,IAAI,EAAEP,YAAY,GAAG,IAAI,KAAK;IAClE1B,yBAAyB,CAACiC,IAAI,CAAC;IAC/B/B,uBAAuB,CAACwB,YAAY,CAAC;IAErC,IAAIO,IAAI,KAAK,MAAM,IAAIP,YAAY,EAAE;MACnCtB,uBAAuB,CAAC;QACtBC,iBAAiB,EAAEqB,YAAY,CAACrB,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAEoB,YAAY,CAACpB,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAEmB,YAAY,CAACnB,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMoC,6BAA6B,GAAGA,CAAA,KAAM;IAC1CpC,yBAAyB,CAAC,KAAK,CAAC;IAChCI,uBAAuB,CAAC,IAAI,CAAC;IAC7BhB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAMiD,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFjD,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,CAACiB,oBAAoB,CAACE,iBAAiB,CAAC+B,IAAI,CAAC,CAAC,EAAE;QAClDlD,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACiB,oBAAoB,CAACG,KAAK,IAAI,CAACH,oBAAoB,CAACI,QAAQ,EAAE;QACjErB,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAIa,sBAAsB,KAAK,QAAQ,EAAE;QACvC,MAAMxB,mBAAmB,CAAC8D,kBAAkB,CAACzD,UAAU,EAAEuB,oBAAoB,CAAC;MAChF,CAAC,MAAM,IAAIJ,sBAAsB,KAAK,MAAM,EAAE;QAC5C,MAAMxB,mBAAmB,CAAC+D,kBAAkB,CAACrC,oBAAoB,CAAC8B,eAAe,EAAE5B,oBAAoB,CAAC;MAC1G;MAEA+B,6BAA6B,CAAC,CAAC;MAC/B,MAAMpB,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOL,GAAG,EAAE;MACZC,OAAO,CAACzB,KAAK,CAAC,yBAAyB,EAAEwB,GAAG,CAAC;MAC7CvB,QAAQ,CAACuB,GAAG,CAACa,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAMiB,wBAAwB,GAAG,MAAOC,cAAc,IAAK;IACzD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAMnE,mBAAmB,CAACoE,kBAAkB,CAACH,cAAc,CAAC;MAC5D,MAAM1B,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOL,GAAG,EAAE;MACZC,OAAO,CAACzB,KAAK,CAAC,4BAA4B,EAAEwB,GAAG,CAAC;MAChDvB,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;EAED,MAAM0D,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE;IACpB,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAME,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAED,IAAIjE,OAAO,EAAE;IACX,oBACEL,OAAA,CAAC7C,GAAG;MAACqH,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/E5E,OAAA,CAACjC,gBAAgB;QAAA8G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACEhF,OAAA,CAAC7C,GAAG;IAAC8H,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAEhB5E,OAAA,CAAC7C,GAAG;MAACgI,EAAE,EAAE,CAAE;MAAAP,QAAA,eACT5E,OAAA,CAAC5C,UAAU;QAACgI,OAAO,EAAC,IAAI;QAACH,EAAE,EAAE;UAAEI,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAV,QAAA,EACrEzE;MAAY;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAELzE,KAAK,iBACJP,OAAA,CAAClC,KAAK;MAACyH,QAAQ,EAAC,OAAO;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACnCrE;IAAK;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGAvE,WAAW,iBACVT,OAAA,CAAC1C,KAAK;MAAC2H,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEK,OAAO,EAAE;MAAU,CAAE;MAAAZ,QAAA,eAC7C5E,OAAA,CAACzB,KAAK;QAACkH,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAAChB,UAAU,EAAC,QAAQ;QAACD,cAAc,EAAC,eAAe;QAACkB,QAAQ,EAAC,MAAM;QAAAf,QAAA,gBAEnG5E,OAAA,CAACzB,KAAK;UAACkH,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD5E,OAAA,CAACf,UAAU;YAACqG,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/ChF,OAAA,CAAC7C,GAAG;YAAAyH,QAAA,gBACF5E,OAAA,CAAC5C,UAAU;cAACgI,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DnE,WAAW,CAACqF,mBAAmB,IAAI;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACbhF,OAAA,CAAC5C,UAAU;cAACgI,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRhF,OAAA,CAACzB,KAAK;UAACkH,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD5E,OAAA,CAACjB,UAAU;YAACuG,KAAK,EAAC,MAAM;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5ChF,OAAA,CAAC7C,GAAG;YAAAyH,QAAA,gBACF5E,OAAA,CAAC5C,UAAU;cAACgI,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DnE,WAAW,CAACsF,cAAc,IAAI;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACbhF,OAAA,CAAC5C,UAAU;cAACgI,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRhF,OAAA,CAACzB,KAAK;UAACkH,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD5E,OAAA,CAACP,eAAe;YAAC6F,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDhF,OAAA,CAAC7C,GAAG;YAAAyH,QAAA,gBACF5E,OAAA,CAAC5C,UAAU;cAACgI,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DnE,WAAW,CAACuF,gBAAgB,IAAI;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACbhF,OAAA,CAAC5C,UAAU;cAACgI,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRhF,OAAA,CAACzB,KAAK;UAACkH,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD5E,OAAA,CAACL,YAAY;YAAC2F,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDhF,OAAA,CAAC7C,GAAG;YAAAyH,QAAA,gBACF5E,OAAA,CAAC5C,UAAU;cAACgI,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DnE,WAAW,CAACwF,kBAAkB,IAAI;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACbhF,OAAA,CAAC5C,UAAU;cAACgI,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRhF,OAAA,CAACzB,KAAK;UAACkH,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD5E,OAAA,CAAC7C,GAAG;YAAC8H,EAAE,EAAE;cACPiB,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,YAAY,EAAE,KAAK;cACnBZ,OAAO,EAAG/E,WAAW,CAACwF,kBAAkB,IAAIxF,WAAW,CAACsF,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAC3FtF,WAAW,CAACwF,kBAAkB,IAAIxF,WAAW,CAACsF,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAAG,YAAY;cACpHvB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,eACA5E,OAAA,CAAC5C,UAAU;cAACgI,OAAO,EAAC,SAAS;cAACC,UAAU,EAAC,MAAM;cAACC,KAAK,EAAC,OAAO;cAAAV,QAAA,GAC1DnE,WAAW,CAACsF,cAAc,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAAE7F,WAAW,CAACwF,kBAAkB,GAAGxF,WAAW,CAACsF,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GACxH;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNhF,OAAA,CAAC7C,GAAG;YAAAyH,QAAA,gBACF5E,OAAA,CAAC5C,UAAU;cAACgI,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,QAAQ;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhF,OAAA,CAAC5C,UAAU;cAACgI,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,GACjDnE,WAAW,CAAC8F,cAAc,IAAI,CAAC,EAAC,SACnC;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAGDhF,OAAA,CAAC7C,GAAG;MAAAyH,QAAA,eACF5E,OAAA,CAAC7C,GAAG;QAAAyH,QAAA,gBAEF5E,OAAA,CAAC7C,GAAG;UAACqH,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAACS,EAAE,EAAE,CAAE;UAAAP,QAAA,gBAC3E5E,OAAA,CAAC5C,UAAU;YAACgI,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAe,CAAE;YAAAV,QAAA,EAAC;UAEzE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhF,OAAA,CAAC3C,MAAM;YACL+H,OAAO,EAAC,WAAW;YACnBoB,SAAS,eAAExG,OAAA,CAACvB,OAAO;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvByB,OAAO,EAAEA,CAAA,KAAMnD,4BAA4B,CAAC,QAAQ,CAAE;YACtD2B,EAAE,EAAE;cACFyB,aAAa,EAAE,MAAM;cACrBrB,UAAU,EAAE,GAAG;cACfsB,EAAE,EAAE,CAAC;cACLC,EAAE,EAAE;YACN,CAAE;YAAAhC,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELjE,mBAAmB,gBAClBf,OAAA,CAAC7C,GAAG;UAACqH,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,QAAQ;UAACmC,EAAE,EAAE,CAAE;UAAAhC,QAAA,eAChD5E,OAAA,CAACjC,gBAAgB;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAENhF,OAAA,CAAC7C,GAAG;UAAAyH,QAAA,EACD/D,YAAY,CAACgG,MAAM,KAAK,CAAC,gBACxB7G,OAAA,CAAC1C,KAAK;YACJwJ,SAAS,EAAE,CAAE;YACb7B,EAAE,EAAE;cACFC,CAAC,EAAE,CAAC;cACJ6B,SAAS,EAAE,QAAQ;cACnBC,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,YAAY;cACpBC,WAAW,EAAE;YACf,CAAE;YAAAtC,QAAA,gBAEF5E,OAAA,CAACf,UAAU;cAACgG,EAAE,EAAE;gBAAEW,QAAQ,EAAE,EAAE;gBAAEN,KAAK,EAAE,UAAU;gBAAEH,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DhF,OAAA,CAAC5C,UAAU;cAACgI,OAAO,EAAC,IAAI;cAACE,KAAK,EAAC,gBAAgB;cAAC6B,YAAY;cAAAvC,QAAA,EAAC;YAE7D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhF,OAAA,CAAC5C,UAAU;cAACgI,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAACL,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhF,OAAA,CAAC3C,MAAM;cACL+H,OAAO,EAAC,WAAW;cACnBoB,SAAS,eAAExG,OAAA,CAACvB,OAAO;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvByB,OAAO,EAAEA,CAAA,KAAMnD,4BAA4B,CAAC,QAAQ,CAAE;cACtD2B,EAAE,EAAE;gBAAEyB,aAAa,EAAE;cAAO,CAAE;cAAA9B,QAAA,EAC/B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,GAERnE,YAAY,CAACuG,GAAG,CAAEpE,YAAY,iBAC5BhD,OAAA,CAAC5B,SAAS;YAER6G,EAAE,EAAE;cACFE,EAAE,EAAE,CAAC;cACL,UAAU,EAAE;gBAAEX,OAAO,EAAE;cAAO,CAAC;cAC/B6C,SAAS,EAAE,2BAA2B;cACtCJ,MAAM,EAAE,WAAW;cACnBC,WAAW,EAAE;YACf,CAAE;YAAAtC,QAAA,gBAEF5E,OAAA,CAAC3B,gBAAgB;cACfiJ,UAAU,eAAEtH,OAAA,CAACT,cAAc;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC/BC,EAAE,EAAE;gBACF,SAAS,EAAE;kBACT+B,eAAe,EAAE;gBACnB;cACF,CAAE;cAAApC,QAAA,eAEF5E,OAAA,CAAC7C,GAAG;gBAACqH,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC,QAAQ;gBAACD,cAAc,EAAC,eAAe;gBAACyB,KAAK,EAAC,MAAM;gBAAAtB,QAAA,gBACjF5E,OAAA,CAAC7C,GAAG;kBAACqH,OAAO,EAAC,MAAM;kBAACE,UAAU,EAAC,QAAQ;kBAAC6C,GAAG,EAAE,CAAE;kBAAA3C,QAAA,gBAC7C5E,OAAA,CAACf,UAAU;oBAACqG,KAAK,EAAC,SAAS;oBAACL,EAAE,EAAE;sBAAEW,QAAQ,EAAE;oBAAG;kBAAE;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpDhF,OAAA,CAAC7C,GAAG;oBAAAyH,QAAA,gBACF5E,OAAA,CAAC5C,UAAU;sBAACgI,OAAO,EAAC,IAAI;sBAACH,EAAE,EAAE;wBAAEI,UAAU,EAAE;sBAAI,CAAE;sBAAAT,QAAA,EAC9C5B,YAAY,CAACrB;oBAAiB;sBAAAkD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACbhF,OAAA,CAAC7C,GAAG;sBAACqH,OAAO,EAAC,MAAM;sBAAC+C,GAAG,EAAE,CAAE;sBAACC,EAAE,EAAE,GAAI;sBAAA5C,QAAA,GACjC5B,YAAY,CAACpB,KAAK,iBACjB5B,OAAA,CAAC7C,GAAG;wBAACqH,OAAO,EAAC,MAAM;wBAACE,UAAU,EAAC,QAAQ;wBAAC6C,GAAG,EAAE,GAAI;wBAAA3C,QAAA,gBAC/C5E,OAAA,CAACb,SAAS;0BAACyG,QAAQ,EAAC,OAAO;0BAACN,KAAK,EAAC;wBAAQ;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7ChF,OAAA,CAAC5C,UAAU;0BAACgI,OAAO,EAAC,OAAO;0BAACE,KAAK,EAAC,gBAAgB;0BAAAV,QAAA,EAC/C5B,YAAY,CAACpB;wBAAK;0BAAAiD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACT,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CACN,EACAhC,YAAY,CAACnB,QAAQ,iBACpB7B,OAAA,CAAC7C,GAAG;wBAACqH,OAAO,EAAC,MAAM;wBAACE,UAAU,EAAC,QAAQ;wBAAC6C,GAAG,EAAE,GAAI;wBAAA3C,QAAA,gBAC/C5E,OAAA,CAACX,SAAS;0BAACuG,QAAQ,EAAC,OAAO;0BAACN,KAAK,EAAC;wBAAQ;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7ChF,OAAA,CAAC5C,UAAU;0BAACgI,OAAO,EAAC,OAAO;0BAACE,KAAK,EAAC,gBAAgB;0BAAAV,QAAA,EAC/C5B,YAAY,CAACnB;wBAAQ;0BAAAgD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENhF,OAAA,CAAC7C,GAAG;kBAACqH,OAAO,EAAC,MAAM;kBAACE,UAAU,EAAC,QAAQ;kBAAC6C,GAAG,EAAE,CAAE;kBAACd,OAAO,EAAGgB,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;kBAAA9C,QAAA,gBAClF5E,OAAA,CAAC3C,MAAM;oBACL+H,OAAO,EAAC,WAAW;oBACnBuC,IAAI,EAAC,OAAO;oBACZnB,SAAS,eAAExG,OAAA,CAACjB,UAAU;sBAAA8F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC1ByB,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACA7F,kBAAkB,CAAC,IAAI,CAAC;oBAC1B,CAAE;oBACFqE,EAAE,EAAE;sBACFyB,aAAa,EAAE,MAAM;sBACrBrB,UAAU,EAAE,GAAG;sBACfsB,EAAE,EAAE,CAAC;sBACLC,EAAE,EAAE,GAAG;sBACPgB,QAAQ,EAAE;oBACZ,CAAE;oBAAAhD,QAAA,EAEDzB,KAAK,CAACC,OAAO,CAACnC,sBAAsB,CAAC+B,YAAY,CAACK,eAAe,CAAC,CAAC,GAAGpC,sBAAsB,CAAC+B,YAAY,CAACK,eAAe,CAAC,CAACwD,MAAM,GAAG;kBAAC;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChI,CAAC,eACThF,OAAA,CAAChC,OAAO;oBAAC6J,KAAK,EAAC,uBAAuB;oBAAAjD,QAAA,eACpC5E,OAAA,CAACxC,UAAU;sBACTmK,IAAI,EAAC,OAAO;sBACZlB,OAAO,EAAEA,CAAA,KAAMnD,4BAA4B,CAAC,MAAM,EAAEN,YAAY,CAAE;sBAClEiC,EAAE,EAAE;wBACF,SAAS,EAAE;0BACT+B,eAAe,EAAE,eAAe;0BAChC1B,KAAK,EAAE;wBACT;sBACF,CAAE;sBAAAV,QAAA,eAEF5E,OAAA,CAACrB,QAAQ;wBAACiH,QAAQ,EAAC;sBAAO;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVhF,OAAA,CAAChC,OAAO;oBAAC6J,KAAK,EAAC,sBAAsB;oBAAAjD,QAAA,eACnC5E,OAAA,CAACxC,UAAU;sBACTmK,IAAI,EAAC,OAAO;sBACZlB,OAAO,EAAEA,CAAA,KAAM5C,wBAAwB,CAACb,YAAY,CAACK,eAAe,CAAE;sBACtE4B,EAAE,EAAE;wBACF,SAAS,EAAE;0BACT+B,eAAe,EAAE,aAAa;0BAC9B1B,KAAK,EAAE;wBACT;sBACF,CAAE;sBAAAV,QAAA,eAEF5E,OAAA,CAACnB,UAAU;wBAAC+G,QAAQ,EAAC;sBAAO;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC,eAEnBhF,OAAA,CAAC1B,gBAAgB;cAAC2G,EAAE,EAAE;gBAAE6C,EAAE,EAAE;cAAE,CAAE;cAAAlD,QAAA,gBAC9B5E,OAAA,CAAC5C,UAAU;gBAACgI,OAAO,EAAC,WAAW;gBAAC+B,YAAY;gBAAClC,EAAE,EAAE;kBAAEI,UAAU,EAAE,GAAG;kBAAEF,EAAE,EAAE;gBAAE,CAAE;gBAAAP,QAAA,EAAC;cAE7E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAEX,CAAC7B,KAAK,CAACC,OAAO,CAACnC,sBAAsB,CAAC+B,YAAY,CAACK,eAAe,CAAC,CAAC,IAAIpC,sBAAsB,CAAC+B,YAAY,CAACK,eAAe,CAAC,CAACwD,MAAM,KAAK,CAAC,gBACzI7G,OAAA,CAAC7C,GAAG;gBACF8H,EAAE,EAAE;kBACFC,CAAC,EAAE,CAAC;kBACJ6B,SAAS,EAAE,QAAQ;kBACnBC,eAAe,EAAE,SAAS;kBAC1BZ,YAAY,EAAE,CAAC;kBACfa,MAAM,EAAE,YAAY;kBACpBC,WAAW,EAAE;gBACf,CAAE;gBAAAtC,QAAA,eAEF5E,OAAA,CAAC5C,UAAU;kBAACgI,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,gBAAgB;kBAAAV,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,gBAENhF,OAAA,CAAC/B,IAAI;gBAAC8J,KAAK;gBAAAnD,QAAA,EACRzB,KAAK,CAACC,OAAO,CAACnC,sBAAsB,CAAC+B,YAAY,CAACK,eAAe,CAAC,CAAC,IAAIpC,sBAAsB,CAAC+B,YAAY,CAACK,eAAe,CAAC,CAAC+D,GAAG,CAAEY,OAAO,iBACvIhI,OAAA,CAAC9B,QAAQ;kBAA8B+J,OAAO;kBAAArD,QAAA,eAC5C5E,OAAA,CAAC7B,YAAY;oBACX+J,OAAO,eACLlI,OAAA,CAAC7C,GAAG;sBAACqH,OAAO,EAAC,MAAM;sBAACE,UAAU,EAAC,QAAQ;sBAAC6C,GAAG,EAAE,CAAE;sBAAA3C,QAAA,gBAC7C5E,OAAA,CAAC5C,UAAU;wBAACgI,OAAO,EAAC,OAAO;wBAACC,UAAU,EAAC,MAAM;wBAAAT,QAAA,EAC1CoD,OAAO,CAACG;sBAAc;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CAAC,eACbhF,OAAA,CAACzC,IAAI;wBACH6K,KAAK,EAAElE,mBAAmB,CAAC8D,OAAO,CAACK,YAAY,CAAE;wBACjDV,IAAI,EAAC,OAAO;wBACZvC,OAAO,EAAC;sBAAU;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACFhF,OAAA,CAACzC,IAAI;wBACH6K,KAAK,EAAEJ,OAAO,CAAC1D,KAAK,IAAI,QAAS;wBACjCqD,IAAI,EAAC,OAAO;wBACZrC,KAAK,EAAEjB,aAAa,CAAC2D,OAAO,CAAC1D,KAAK;sBAAE;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;oBACDsD,SAAS,eACPtI,OAAA,CAAC5C,UAAU;sBAACgI,OAAO,EAAC,OAAO;sBAACE,KAAK,EAAC,eAAe;sBAAAV,QAAA,GAC9CoD,OAAO,CAACO,WAAW,IAAI,qBAAqB,EAC5CP,OAAO,CAACQ,cAAc,IAAI,cAAc,IAAIC,IAAI,CAACT,OAAO,CAACQ,cAAc,CAAC,CAACE,kBAAkB,CAAC,CAAC,EAAE;oBAAA;sBAAA7D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF;kBACb;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC,GAzBWgD,OAAO,CAACG,cAAc;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0B3B,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC;UAAA,GArJdhC,YAAY,CAACK,eAAe;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsJxB,CACZ;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhF,OAAA,CAACvC,MAAM;MACLkL,IAAI,EAAExH,sBAAuB;MAC7ByH,OAAO,EAAEpF,6BAA8B;MACvCqF,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV9D,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEF5E,OAAA,CAACtC,WAAW;QAACuH,EAAE,EAAE;UAAE+D,EAAE,EAAE;QAAE,CAAE;QAAApE,QAAA,eACzB5E,OAAA,CAAC5C,UAAU;UAACgI,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9CvD,sBAAsB,KAAK,QAAQ,GAAG,wBAAwB,GAAG;QAAuB;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdhF,OAAA,CAACrC,aAAa;QAAAiH,QAAA,eACZ5E,OAAA,CAAC7C,GAAG;UAAC8H,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACjB5E,OAAA,CAACnC,SAAS;YACRiL,SAAS;YACTV,KAAK,EAAC,mBAAmB;YACzBa,KAAK,EAAExH,oBAAoB,CAACE,iBAAkB;YAC9CuH,QAAQ,EAAGzB,CAAC,IAAK/F,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEE,iBAAiB,EAAE8F,CAAC,CAAC0B,MAAM,CAACF;YAAM,CAAC,CAAE;YACzGG,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRjE,OAAO,EAAC,UAAU;YAClBH,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFhF,OAAA,CAACnC,SAAS;YACRiL,SAAS;YACTV,KAAK,EAAC,OAAO;YACbkB,IAAI,EAAC,OAAO;YACZL,KAAK,EAAExH,oBAAoB,CAACG,KAAM;YAClCsH,QAAQ,EAAGzB,CAAC,IAAK/F,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEG,KAAK,EAAE6F,CAAC,CAAC0B,MAAM,CAACF;YAAM,CAAC,CAAE;YAC7FG,MAAM,EAAC,QAAQ;YACfhE,OAAO,EAAC,UAAU;YAClBmE,UAAU,EAAC,uDAAuD;YAClEtE,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFhF,OAAA,CAACnC,SAAS;YACRiL,SAAS;YACTV,KAAK,EAAC,UAAU;YAChBa,KAAK,EAAExH,oBAAoB,CAACI,QAAS;YACrCqH,QAAQ,EAAGzB,CAAC,IAAK/F,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEI,QAAQ,EAAE4F,CAAC,CAAC0B,MAAM,CAACF;YAAM,CAAC,CAAE;YAChGG,MAAM,EAAC,QAAQ;YACfhE,OAAO,EAAC,UAAU;YAClBmE,UAAU,EAAC;UAA+C;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBhF,OAAA,CAACpC,aAAa;QAACqH,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAlD,QAAA,gBACjC5E,OAAA,CAAC3C,MAAM;UACLoJ,OAAO,EAAEjD,6BAA8B;UACvCyB,EAAE,EAAE;YAAEyB,aAAa,EAAE;UAAO,CAAE;UAAA9B,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThF,OAAA,CAAC3C,MAAM;UACLoJ,OAAO,EAAEhD,wBAAyB;UAClC2B,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACFyB,aAAa,EAAE,MAAM;YACrBrB,UAAU,EAAE,GAAG;YACfsB,EAAE,EAAE;UACN,CAAE;UAAA/B,QAAA,EAEDvD,sBAAsB,KAAK,QAAQ,GAAG,MAAM,GAAG;QAAO;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGThF,OAAA,CAACF,kBAAkB;MACjBI,UAAU,EAAEA,UAAW;MACvByI,IAAI,EAAEhI,eAAgB;MACtBiI,OAAO,EAAEA,CAAA,KAAMhI,kBAAkB,CAAC,KAAK,CAAE;MACzC4I,SAAS,EAAEA,CAAA,KAAM;QACf1H,WAAW,CAAC,CAAC;QACbG,eAAe,CAAC,CAAC;QACjBG,gBAAgB,CAAC,CAAC;QAClBxB,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IAAE;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAvlBIH,wBAAwB;AAAAwJ,EAAA,GAAxBxJ,wBAAwB;AAylB9B,eAAeA,wBAAwB;AAAC,IAAAwJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}