{"ast": null, "code": "import { formatDistance } from \"./az/_lib/formatDistance.js\";\nimport { formatLong } from \"./az/_lib/formatLong.js\";\nimport { formatRelative } from \"./az/_lib/formatRelative.js\";\nimport { localize } from \"./az/_lib/localize.js\";\nimport { match } from \"./az/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Azerbaijani locale.\n * @language Azerbaijani\n * @iso-639-2 aze\n */\n\nexport const az = {\n  code: \"az\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default az;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "az", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/az.js"], "sourcesContent": ["import { formatDistance } from \"./az/_lib/formatDistance.js\";\nimport { formatLong } from \"./az/_lib/formatLong.js\";\nimport { formatRelative } from \"./az/_lib/formatRelative.js\";\nimport { localize } from \"./az/_lib/localize.js\";\nimport { match } from \"./az/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Azerbaijani locale.\n * @language Azerbaijani\n * @iso-639-2 aze\n */\n\nexport const az = {\n  code: \"az\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default az;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC;IACfC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}