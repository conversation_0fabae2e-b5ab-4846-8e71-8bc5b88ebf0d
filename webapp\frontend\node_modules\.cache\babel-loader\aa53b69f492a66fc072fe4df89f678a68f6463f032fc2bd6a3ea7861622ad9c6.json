{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M22 19H2c-.55 0-1 .45-1 1s.45 1 1 1h20c.55 0 1-.45 1-1s-.45-1-1-1M4 18h16c1.1 0 1.99-.9 1.99-2L22 5c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2m4.59-9.95c1.28-1.87 3.86-2.05 5.38-.52 1.18 1.18 1.34 3 .47 4.36L16 13.44c.29.29.29.77 0 1.06-.29.29-.77.29-1.06 0l-1.55-1.55c-1.57 1-3.76.64-4.87-1.11-.73-1.14-.69-2.67.07-3.79\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"11.5\",\n  cy: \"10\",\n  r: \"2\"\n}, \"1\")], 'ScreenSearchDesktopRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "cx", "cy", "r"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/icons-material/esm/ScreenSearchDesktopRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M22 19H2c-.55 0-1 .45-1 1s.45 1 1 1h20c.55 0 1-.45 1-1s-.45-1-1-1M4 18h16c1.1 0 1.99-.9 1.99-2L22 5c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2m4.59-9.95c1.28-1.87 3.86-2.05 5.38-.52 1.18 1.18 1.34 3 .47 4.36L16 13.44c.29.29.29.77 0 1.06-.29.29-.77.29-1.06 0l-1.55-1.55c-1.57 1-3.76.64-4.87-1.11-.73-1.14-.69-2.67.07-3.79\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"11.5\",\n  cy: \"10\",\n  r: \"2\"\n}, \"1\")], 'ScreenSearchDesktopRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,QAAQ,EAAE;EACnCE,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,4BAA4B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}