{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameYear } from \"./isSameYear.js\";\n\n/**\n * The {@link isThisYear} function options.\n */\n\n/**\n * @name isThisYear\n * @category Year Helpers\n * @summary Is the given date in the same year as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same year as the current date?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is in this year\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this year?\n * const result = isThisYear(new Date(2014, 6, 2))\n * //=> true\n */\nexport function isThisYear(date, options) {\n  return isSameYear(constructFrom(options?.in || date, date), constructNow(options?.in || date));\n}\n\n// Fallback for modularized imports:\nexport default isThisYear;", "map": {"version": 3, "names": ["constructFrom", "constructNow", "isSameYear", "isThisYear", "date", "options", "in"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/isThisYear.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameYear } from \"./isSameYear.js\";\n\n/**\n * The {@link isThisYear} function options.\n */\n\n/**\n * @name isThisYear\n * @category Year Helpers\n * @summary Is the given date in the same year as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same year as the current date?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is in this year\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this year?\n * const result = isThisYear(new Date(2014, 6, 2))\n * //=> true\n */\nexport function isThisYear(date, options) {\n  return isSameYear(\n    constructFrom(options?.in || date, date),\n    constructNow(options?.in || date),\n  );\n}\n\n// Fallback for modularized imports:\nexport default isThisYear;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,UAAU,QAAQ,iBAAiB;;AAE5C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACxC,OAAOH,UAAU,CACfF,aAAa,CAACK,OAAO,EAAEC,EAAE,IAAIF,IAAI,EAAEA,IAAI,CAAC,EACxCH,YAAY,CAACI,OAAO,EAAEC,EAAE,IAAIF,IAAI,CAClC,CAAC;AACH;;AAEA;AACA,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}