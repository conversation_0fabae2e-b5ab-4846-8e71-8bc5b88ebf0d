{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ParcoCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Alert, Card, CardContent, CardActions, Grid, Divider } from '@mui/material';\nimport { Home as HomeIcon, ViewList as ViewListIcon, Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport ParcoCavi from '../../components/cavi/ParcoCavi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ParcoCaviPage = () => {\n  _s();\n  const {\n    isImpersonating\n  } = useAuth();\n  const navigate = useNavigate();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Reindirizza automaticamente alla pagina di visualizzazione bobine\n  useEffect(() => {\n    navigate('/dashboard/cavi/parco/visualizza');\n  }, [navigate]);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cavi');\n  };\n\n  // Gestisce le notifiche\n  const handleSuccess = message => {\n    // Qui puoi implementare una notifica di successo se necessario\n    console.log('Successo:', message);\n  };\n  const handleError = message => {\n    // Qui puoi implementare una notifica di errore se necessario\n    console.error('Errore:', message);\n  };\n  if (!cantiereId || isNaN(cantiereId)) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: \"Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: handleBackToCantieri,\n        children: \"Torna ai Cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'flex-end'\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: [\"Cantiere: \", cantiereName, \" (ID: \", cantiereId, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Parco Cavi - Gestione Bobine\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: \"Seleziona un'opzione dal menu principale nella barra di navigazione in alto per gestire le bobine del parco cavi.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          children: \"Opzioni disponibili:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Visualizza Bobine Disponibili\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Crea Nuova Bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Modifica Bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Elimina Bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Visualizza Storico Utilizzo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n_s(ParcoCaviPage, \"OxxvM3/RCcA+TPqcucQgbM4tCcQ=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = ParcoCaviPage;\nexport default ParcoCaviPage;\nvar _c;\n$RefreshReg$(_c, \"ParcoCaviPage\");", "map": {"version": 3, "names": ["React", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Grid", "Divider", "Home", "HomeIcon", "ViewList", "ViewListIcon", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "useNavigate", "useAuth", "AdminHomeButton", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ParcoCaviPage", "_s", "isImpersonating", "navigate", "cantiereId", "parseInt", "localStorage", "getItem", "cantiereName", "handleBackToCantieri", "handleSuccess", "message", "console", "log", "handleError", "error", "isNaN", "children", "severity", "sx", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "display", "alignItems", "justifyContent", "p", "gutterBottom", "mt", "color", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/ParcoCaviPage.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Alert,\n  Card,\n  CardContent,\n  CardActions,\n  Grid,\n  Divider\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  ViewList as ViewListIcon,\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  History as HistoryIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport ParcoCavi from '../../components/cavi/ParcoCavi';\n\nconst ParcoCaviPage = () => {\n  const { isImpersonating } = useAuth();\n  const navigate = useNavigate();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Reindirizza automaticamente alla pagina di visualizzazione bobine\n  useEffect(() => {\n    navigate('/dashboard/cavi/parco/visualizza');\n  }, [navigate]);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cavi');\n  };\n\n\n\n  // Gestisce le notifiche\n  const handleSuccess = (message) => {\n    // Qui puoi implementare una notifica di successo se necessario\n    console.log('Successo:', message);\n  };\n\n  const handleError = (message) => {\n    // Qui puoi implementare una notifica di errore se necessario\n    console.error('Errore:', message);\n  };\n\n  if (!cantiereId || isNaN(cantiereId)) {\n    return (\n      <Box>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\n        </Alert>\n        <Button\n          variant=\"contained\"\n          onClick={handleBackToCantieri}\n        >\n          Torna ai Cantieri\n        </Button>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>\n        <AdminHomeButton />\n      </Box>\n\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Typography variant=\"h6\">\n          Cantiere: {cantiereName} (ID: {cantiereId})\n        </Typography>\n      </Paper>\n\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Parco Cavi - Gestione Bobine\n        </Typography>\n        <Typography variant=\"body1\">\n          Seleziona un'opzione dal menu principale nella barra di navigazione in alto per gestire le bobine del parco cavi.\n        </Typography>\n        <Box sx={{ mt: 2 }}>\n          <Typography variant=\"body2\" color=\"textSecondary\">\n            Opzioni disponibili:\n          </Typography>\n          <ul>\n            <li>Visualizza Bobine Disponibili</li>\n            <li>Crea Nuova Bobina</li>\n            <li>Modifica Bobina</li>\n            <li>Elimina Bobina</li>\n            <li>Visualizza Storico Utilizzo</li>\n          </ul>\n        </Box>\n      </Paper>\n    </Box>\n  );\n};\n\nexport default ParcoCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,SAAS,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC;EAAgB,CAAC,GAAGP,OAAO,CAAC,CAAC;EACrC,MAAMQ,QAAQ,GAAGT,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMU,UAAU,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;EAC3E,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAAI,YAAYH,UAAU,EAAE;;EAE7F;EACAjC,SAAS,CAAC,MAAM;IACdgC,QAAQ,CAAC,kCAAkC,CAAC;EAC9C,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMM,oBAAoB,GAAGA,CAAA,KAAM;IACjCN,QAAQ,CAAC,iBAAiB,CAAC;EAC7B,CAAC;;EAID;EACA,MAAMO,aAAa,GAAIC,OAAO,IAAK;IACjC;IACAC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEF,OAAO,CAAC;EACnC,CAAC;EAED,MAAMG,WAAW,GAAIH,OAAO,IAAK;IAC/B;IACAC,OAAO,CAACG,KAAK,CAAC,SAAS,EAAEJ,OAAO,CAAC;EACnC,CAAC;EAED,IAAI,CAACP,UAAU,IAAIY,KAAK,CAACZ,UAAU,CAAC,EAAE;IACpC,oBACEL,OAAA,CAAC3B,GAAG;MAAA6C,QAAA,gBACFlB,OAAA,CAACvB,KAAK;QAAC0C,QAAQ,EAAC,OAAO;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAEvC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRzB,OAAA,CAACxB,MAAM;QACLkD,OAAO,EAAC,WAAW;QACnBC,OAAO,EAAEjB,oBAAqB;QAAAQ,QAAA,EAC/B;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEzB,OAAA,CAAC3B,GAAG;IAAA6C,QAAA,gBACFlB,OAAA,CAAC3B,GAAG;MAAC+C,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEO,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAW,CAAE;MAAAZ,QAAA,eACpFlB,OAAA,CAACH,eAAe;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAENzB,OAAA,CAACzB,KAAK;MAAC6C,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEU,CAAC,EAAE;MAAE,CAAE;MAAAb,QAAA,eACzBlB,OAAA,CAAC1B,UAAU;QAACoD,OAAO,EAAC,IAAI;QAAAR,QAAA,GAAC,YACb,EAACT,YAAY,EAAC,QAAM,EAACJ,UAAU,EAAC,GAC5C;MAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAERzB,OAAA,CAACzB,KAAK;MAAC6C,EAAE,EAAE;QAAEW,CAAC,EAAE,CAAC;QAAEV,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACzBlB,OAAA,CAAC1B,UAAU;QAACoD,OAAO,EAAC,IAAI;QAACM,YAAY;QAAAd,QAAA,EAAC;MAEtC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbzB,OAAA,CAAC1B,UAAU;QAACoD,OAAO,EAAC,OAAO;QAAAR,QAAA,EAAC;MAE5B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbzB,OAAA,CAAC3B,GAAG;QAAC+C,EAAE,EAAE;UAAEa,EAAE,EAAE;QAAE,CAAE;QAAAf,QAAA,gBACjBlB,OAAA,CAAC1B,UAAU;UAACoD,OAAO,EAAC,OAAO;UAACQ,KAAK,EAAC,eAAe;UAAAhB,QAAA,EAAC;QAElD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzB,OAAA;UAAAkB,QAAA,gBACElB,OAAA;YAAAkB,QAAA,EAAI;UAA6B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtCzB,OAAA;YAAAkB,QAAA,EAAI;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BzB,OAAA;YAAAkB,QAAA,EAAI;UAAe;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBzB,OAAA;YAAAkB,QAAA,EAAI;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBzB,OAAA;YAAAkB,QAAA,EAAI;UAA2B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACvB,EAAA,CAjFID,aAAa;EAAA,QACWL,OAAO,EAClBD,WAAW;AAAA;AAAAwC,EAAA,GAFxBlC,aAAa;AAmFnB,eAAeA,aAAa;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}