{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isEqual\n * @category Common Helpers\n * @summary Are the given dates equal?\n *\n * @description\n * Are the given dates equal?\n *\n * @param dateLeft - The first date to compare\n * @param dateRight - The second date to compare\n *\n * @returns The dates are equal\n *\n * @example\n * // Are 2 July 2014 06:30:45.000 and 2 July 2014 06:30:45.500 equal?\n * const result = isEqual(\n *   new Date(2014, 6, 2, 6, 30, 45, 0),\n *   new Date(2014, 6, 2, 6, 30, 45, 500)\n * )\n * //=> false\n */\nexport function isEqual(leftDate, rightDate) {\n  return +toDate(leftDate) === +toDate(rightDate);\n}\n\n// Fallback for modularized imports:\nexport default isEqual;", "map": {"version": 3, "names": ["toDate", "isEqual", "leftDate", "rightDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/isEqual.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * @name isEqual\n * @category Common Helpers\n * @summary Are the given dates equal?\n *\n * @description\n * Are the given dates equal?\n *\n * @param dateLeft - The first date to compare\n * @param dateRight - The second date to compare\n *\n * @returns The dates are equal\n *\n * @example\n * // Are 2 July 2014 06:30:45.000 and 2 July 2014 06:30:45.500 equal?\n * const result = isEqual(\n *   new Date(2014, 6, 2, 6, 30, 45, 0),\n *   new Date(2014, 6, 2, 6, 30, 45, 500)\n * )\n * //=> false\n */\nexport function isEqual(leftDate, rightDate) {\n  return +toDate(leftDate) === +toDate(rightDate);\n}\n\n// Fallback for modularized imports:\nexport default isEqual;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EAC3C,OAAO,CAACH,MAAM,CAACE,QAAQ,CAAC,KAAK,CAACF,MAAM,CAACG,SAAS,CAAC;AACjD;;AAEA;AACA,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}