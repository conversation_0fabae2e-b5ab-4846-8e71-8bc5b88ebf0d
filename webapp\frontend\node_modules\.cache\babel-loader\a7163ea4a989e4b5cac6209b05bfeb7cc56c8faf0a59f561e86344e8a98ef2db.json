{"ast": null, "code": "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n    options = _ref.options,\n    name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n    checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n    _options$altAxis = options.altAxis,\n    checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n    boundary = options.boundary,\n    rootBoundary = options.rootBoundary,\n    altBoundary = options.altBoundary,\n    padding = options.padding,\n    _options$tether = options.tether,\n    tether = _options$tether === void 0 ? true : _options$tether,\n    _options$tetherOffset = options.tetherOffset,\n    tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n  if (!popperOffsets) {\n    return;\n  }\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n    var _mainSide = mainAxis === 'x' ? top : left;\n    var _altSide = mainAxis === 'x' ? bottom : right;\n    var _offset = popperOffsets[altAxis];\n    var _len = altAxis === 'y' ? 'height' : 'width';\n    var _min = _offset + overflow[_mainSide];\n    var _max = _offset - overflow[_altSide];\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "map": {"version": 3, "names": ["top", "left", "right", "bottom", "start", "getBasePlacement", "getMainAxisFromPlacement", "getAltAxis", "within", "withinMaxClamp", "getLayoutRect", "getOffsetParent", "detectOverflow", "getVariation", "getFreshSideObject", "min", "mathMin", "max", "mathMax", "preventOverflow", "_ref", "state", "options", "name", "_options$mainAxis", "mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "boundary", "rootBoundary", "altBoundary", "padding", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "overflow", "basePlacement", "placement", "variation", "isBasePlacement", "popperOffsets", "modifiersData", "referenceRect", "rects", "reference", "popperRect", "popper", "tetherOffsetValue", "Object", "assign", "normalizedTetherOffsetValue", "offsetModifierState", "offset", "data", "x", "y", "_offsetModifierState$", "mainSide", "altSide", "len", "additive", "minLen", "maxLen", "arrowElement", "elements", "arrow", "arrowRect", "width", "height", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "arrowOffsetParent", "clientOffset", "clientTop", "clientLeft", "offsetModifierValue", "tetherMin", "tetherMax", "preventedOffset", "_offsetModifierState$2", "_mainSide", "_altSide", "_offset", "_len", "_min", "_max", "isOriginSide", "indexOf", "_offsetModifierValue", "_tetherMin", "_tetherMax", "_preventedOffset", "enabled", "phase", "fn", "requiresIfExists"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@popperjs/core/lib/modifiers/preventOverflow.js"], "sourcesContent": ["import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,QAAQ,aAAa;AAC7D,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,wBAAwB,MAAM,sCAAsC;AAC3E,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,SAASC,MAAM,EAAEC,cAAc,QAAQ,oBAAoB;AAC3D,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,SAASC,GAAG,IAAIC,OAAO,EAAEC,GAAG,IAAIC,OAAO,QAAQ,kBAAkB;AAEjE,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC7B,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,IAAI,GAAGH,IAAI,CAACG,IAAI;EACpB,IAAIC,iBAAiB,GAAGF,OAAO,CAACG,QAAQ;IACpCC,aAAa,GAAGF,iBAAiB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,iBAAiB;IACvEG,gBAAgB,GAAGL,OAAO,CAACM,OAAO;IAClCC,YAAY,GAAGF,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IACrEG,QAAQ,GAAGR,OAAO,CAACQ,QAAQ;IAC3BC,YAAY,GAAGT,OAAO,CAACS,YAAY;IACnCC,WAAW,GAAGV,OAAO,CAACU,WAAW;IACjCC,OAAO,GAAGX,OAAO,CAACW,OAAO;IACzBC,eAAe,GAAGZ,OAAO,CAACa,MAAM;IAChCA,MAAM,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAC5DE,qBAAqB,GAAGd,OAAO,CAACe,YAAY;IAC5CA,YAAY,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;EAC/E,IAAIE,QAAQ,GAAG1B,cAAc,CAACS,KAAK,EAAE;IACnCS,QAAQ,EAAEA,QAAQ;IAClBC,YAAY,EAAEA,YAAY;IAC1BE,OAAO,EAAEA,OAAO;IAChBD,WAAW,EAAEA;EACf,CAAC,CAAC;EACF,IAAIO,aAAa,GAAGlC,gBAAgB,CAACgB,KAAK,CAACmB,SAAS,CAAC;EACrD,IAAIC,SAAS,GAAG5B,YAAY,CAACQ,KAAK,CAACmB,SAAS,CAAC;EAC7C,IAAIE,eAAe,GAAG,CAACD,SAAS;EAChC,IAAIhB,QAAQ,GAAGnB,wBAAwB,CAACiC,aAAa,CAAC;EACtD,IAAIX,OAAO,GAAGrB,UAAU,CAACkB,QAAQ,CAAC;EAClC,IAAIkB,aAAa,GAAGtB,KAAK,CAACuB,aAAa,CAACD,aAAa;EACrD,IAAIE,aAAa,GAAGxB,KAAK,CAACyB,KAAK,CAACC,SAAS;EACzC,IAAIC,UAAU,GAAG3B,KAAK,CAACyB,KAAK,CAACG,MAAM;EACnC,IAAIC,iBAAiB,GAAG,OAAOb,YAAY,KAAK,UAAU,GAAGA,YAAY,CAACc,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE/B,KAAK,CAACyB,KAAK,EAAE;IACvGN,SAAS,EAAEnB,KAAK,CAACmB;EACnB,CAAC,CAAC,CAAC,GAAGH,YAAY;EAClB,IAAIgB,2BAA2B,GAAG,OAAOH,iBAAiB,KAAK,QAAQ,GAAG;IACxEzB,QAAQ,EAAEyB,iBAAiB;IAC3BtB,OAAO,EAAEsB;EACX,CAAC,GAAGC,MAAM,CAACC,MAAM,CAAC;IAChB3B,QAAQ,EAAE,CAAC;IACXG,OAAO,EAAE;EACX,CAAC,EAAEsB,iBAAiB,CAAC;EACrB,IAAII,mBAAmB,GAAGjC,KAAK,CAACuB,aAAa,CAACW,MAAM,GAAGlC,KAAK,CAACuB,aAAa,CAACW,MAAM,CAAClC,KAAK,CAACmB,SAAS,CAAC,GAAG,IAAI;EACzG,IAAIgB,IAAI,GAAG;IACTC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACL,CAAC;EAED,IAAI,CAACf,aAAa,EAAE;IAClB;EACF;EAEA,IAAIjB,aAAa,EAAE;IACjB,IAAIiC,qBAAqB;IAEzB,IAAIC,QAAQ,GAAGnC,QAAQ,KAAK,GAAG,GAAGzB,GAAG,GAAGC,IAAI;IAC5C,IAAI4D,OAAO,GAAGpC,QAAQ,KAAK,GAAG,GAAGtB,MAAM,GAAGD,KAAK;IAC/C,IAAI4D,GAAG,GAAGrC,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;IAC/C,IAAI8B,MAAM,GAAGZ,aAAa,CAAClB,QAAQ,CAAC;IACpC,IAAIV,GAAG,GAAGwC,MAAM,GAAGjB,QAAQ,CAACsB,QAAQ,CAAC;IACrC,IAAI3C,GAAG,GAAGsC,MAAM,GAAGjB,QAAQ,CAACuB,OAAO,CAAC;IACpC,IAAIE,QAAQ,GAAG5B,MAAM,GAAG,CAACa,UAAU,CAACc,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IAChD,IAAIE,MAAM,GAAGvB,SAAS,KAAKrC,KAAK,GAAGyC,aAAa,CAACiB,GAAG,CAAC,GAAGd,UAAU,CAACc,GAAG,CAAC;IACvE,IAAIG,MAAM,GAAGxB,SAAS,KAAKrC,KAAK,GAAG,CAAC4C,UAAU,CAACc,GAAG,CAAC,GAAG,CAACjB,aAAa,CAACiB,GAAG,CAAC,CAAC,CAAC;IAC3E;;IAEA,IAAII,YAAY,GAAG7C,KAAK,CAAC8C,QAAQ,CAACC,KAAK;IACvC,IAAIC,SAAS,GAAGlC,MAAM,IAAI+B,YAAY,GAAGxD,aAAa,CAACwD,YAAY,CAAC,GAAG;MACrEI,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;IACD,IAAIC,kBAAkB,GAAGnD,KAAK,CAACuB,aAAa,CAAC,kBAAkB,CAAC,GAAGvB,KAAK,CAACuB,aAAa,CAAC,kBAAkB,CAAC,CAACX,OAAO,GAAGnB,kBAAkB,CAAC,CAAC;IACzI,IAAI2D,eAAe,GAAGD,kBAAkB,CAACZ,QAAQ,CAAC;IAClD,IAAIc,eAAe,GAAGF,kBAAkB,CAACX,OAAO,CAAC,CAAC,CAAC;IACnD;IACA;IACA;IACA;;IAEA,IAAIc,QAAQ,GAAGnE,MAAM,CAAC,CAAC,EAAEqC,aAAa,CAACiB,GAAG,CAAC,EAAEO,SAAS,CAACP,GAAG,CAAC,CAAC;IAC5D,IAAIc,SAAS,GAAGlC,eAAe,GAAGG,aAAa,CAACiB,GAAG,CAAC,GAAG,CAAC,GAAGC,QAAQ,GAAGY,QAAQ,GAAGF,eAAe,GAAGpB,2BAA2B,CAAC5B,QAAQ,GAAGuC,MAAM,GAAGW,QAAQ,GAAGF,eAAe,GAAGpB,2BAA2B,CAAC5B,QAAQ;IACpN,IAAIoD,SAAS,GAAGnC,eAAe,GAAG,CAACG,aAAa,CAACiB,GAAG,CAAC,GAAG,CAAC,GAAGC,QAAQ,GAAGY,QAAQ,GAAGD,eAAe,GAAGrB,2BAA2B,CAAC5B,QAAQ,GAAGwC,MAAM,GAAGU,QAAQ,GAAGD,eAAe,GAAGrB,2BAA2B,CAAC5B,QAAQ;IACrN,IAAIqD,iBAAiB,GAAGzD,KAAK,CAAC8C,QAAQ,CAACC,KAAK,IAAIzD,eAAe,CAACU,KAAK,CAAC8C,QAAQ,CAACC,KAAK,CAAC;IACrF,IAAIW,YAAY,GAAGD,iBAAiB,GAAGrD,QAAQ,KAAK,GAAG,GAAGqD,iBAAiB,CAACE,SAAS,IAAI,CAAC,GAAGF,iBAAiB,CAACG,UAAU,IAAI,CAAC,GAAG,CAAC;IAClI,IAAIC,mBAAmB,GAAG,CAACvB,qBAAqB,GAAGL,mBAAmB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAAC7B,QAAQ,CAAC,KAAK,IAAI,GAAGkC,qBAAqB,GAAG,CAAC;IAC5J,IAAIwB,SAAS,GAAG5B,MAAM,GAAGqB,SAAS,GAAGM,mBAAmB,GAAGH,YAAY;IACvE,IAAIK,SAAS,GAAG7B,MAAM,GAAGsB,SAAS,GAAGK,mBAAmB;IACxD,IAAIG,eAAe,GAAG7E,MAAM,CAAC2B,MAAM,GAAGnB,OAAO,CAACD,GAAG,EAAEoE,SAAS,CAAC,GAAGpE,GAAG,EAAEwC,MAAM,EAAEpB,MAAM,GAAGjB,OAAO,CAACD,GAAG,EAAEmE,SAAS,CAAC,GAAGnE,GAAG,CAAC;IACpH0B,aAAa,CAAClB,QAAQ,CAAC,GAAG4D,eAAe;IACzC7B,IAAI,CAAC/B,QAAQ,CAAC,GAAG4D,eAAe,GAAG9B,MAAM;EAC3C;EAEA,IAAI1B,YAAY,EAAE;IAChB,IAAIyD,sBAAsB;IAE1B,IAAIC,SAAS,GAAG9D,QAAQ,KAAK,GAAG,GAAGzB,GAAG,GAAGC,IAAI;IAE7C,IAAIuF,QAAQ,GAAG/D,QAAQ,KAAK,GAAG,GAAGtB,MAAM,GAAGD,KAAK;IAEhD,IAAIuF,OAAO,GAAG9C,aAAa,CAACf,OAAO,CAAC;IAEpC,IAAI8D,IAAI,GAAG9D,OAAO,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;IAE/C,IAAI+D,IAAI,GAAGF,OAAO,GAAGnD,QAAQ,CAACiD,SAAS,CAAC;IAExC,IAAIK,IAAI,GAAGH,OAAO,GAAGnD,QAAQ,CAACkD,QAAQ,CAAC;IAEvC,IAAIK,YAAY,GAAG,CAAC7F,GAAG,EAAEC,IAAI,CAAC,CAAC6F,OAAO,CAACvD,aAAa,CAAC,KAAK,CAAC,CAAC;IAE5D,IAAIwD,oBAAoB,GAAG,CAACT,sBAAsB,GAAGhC,mBAAmB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAAC1B,OAAO,CAAC,KAAK,IAAI,GAAG0D,sBAAsB,GAAG,CAAC;IAE9J,IAAIU,UAAU,GAAGH,YAAY,GAAGF,IAAI,GAAGF,OAAO,GAAG5C,aAAa,CAAC6C,IAAI,CAAC,GAAG1C,UAAU,CAAC0C,IAAI,CAAC,GAAGK,oBAAoB,GAAG1C,2BAA2B,CAACzB,OAAO;IAEpJ,IAAIqE,UAAU,GAAGJ,YAAY,GAAGJ,OAAO,GAAG5C,aAAa,CAAC6C,IAAI,CAAC,GAAG1C,UAAU,CAAC0C,IAAI,CAAC,GAAGK,oBAAoB,GAAG1C,2BAA2B,CAACzB,OAAO,GAAGgE,IAAI;IAEpJ,IAAIM,gBAAgB,GAAG/D,MAAM,IAAI0D,YAAY,GAAGpF,cAAc,CAACuF,UAAU,EAAEP,OAAO,EAAEQ,UAAU,CAAC,GAAGzF,MAAM,CAAC2B,MAAM,GAAG6D,UAAU,GAAGL,IAAI,EAAEF,OAAO,EAAEtD,MAAM,GAAG8D,UAAU,GAAGL,IAAI,CAAC;IAEzKjD,aAAa,CAACf,OAAO,CAAC,GAAGsE,gBAAgB;IACzC1C,IAAI,CAAC5B,OAAO,CAAC,GAAGsE,gBAAgB,GAAGT,OAAO;EAC5C;EAEApE,KAAK,CAACuB,aAAa,CAACrB,IAAI,CAAC,GAAGiC,IAAI;AAClC,CAAC,CAAC;;AAGF,eAAe;EACbjC,IAAI,EAAE,iBAAiB;EACvB4E,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,MAAM;EACbC,EAAE,EAAElF,eAAe;EACnBmF,gBAAgB,EAAE,CAAC,QAAQ;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}