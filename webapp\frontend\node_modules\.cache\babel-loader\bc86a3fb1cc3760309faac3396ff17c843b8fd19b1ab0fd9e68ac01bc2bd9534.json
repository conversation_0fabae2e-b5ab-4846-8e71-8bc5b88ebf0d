{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\nimport userService from '../../services/userService';\nimport { useAuth } from '../../context/AuthContext';\n\n/**\n * Componente invisibile che verifica periodicamente gli utenti scaduti.\n * Viene eseguito solo quando un amministratore è loggato.\n */\nconst UserExpirationChecker = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [lastCheck, setLastCheck] = useState(null);\n\n  // Verifica gli utenti scaduti all'avvio e ogni ora\n  useEffect(() => {\n    // Esegui solo se l'utente è un amministratore\n    if (user && user.role === 'owner') {\n      // Funzione per verificare gli utenti scaduti\n      const checkExpiredUsers = async () => {\n        try {\n          const result = await userService.checkExpiredUsers();\n          console.log('Verifica utenti scaduti:', result);\n          setLastCheck(new Date());\n        } catch (error) {\n          console.error('Errore durante la verifica degli utenti scaduti:', error);\n          // Non fare nulla in caso di errore, per evitare di bloccare l'applicazione\n          // L'errore potrebbe essere dovuto a un problema di autorizzazione\n          // quando si impersona un utente non amministratore\n        }\n      };\n\n      // Esegui subito la verifica\n      checkExpiredUsers();\n\n      // Imposta un intervallo per verificare ogni ora\n      const interval = setInterval(checkExpiredUsers, 60 * 60 * 1000);\n\n      // Pulisci l'intervallo quando il componente viene smontato\n      return () => clearInterval(interval);\n    }\n  }, [user]);\n\n  // Questo componente non renderizza nulla\n  return null;\n};\n_s(UserExpirationChecker, \"NSw//K7Dzxw1vOtA0JNDG0QMXO8=\", false, function () {\n  return [useAuth];\n});\n_c = UserExpirationChecker;\nexport default UserExpirationChecker;\nvar _c;\n$RefreshReg$(_c, \"UserExpirationChecker\");", "map": {"version": 3, "names": ["useEffect", "useState", "userService", "useAuth", "UserExpirationChecker", "_s", "user", "<PERSON><PERSON><PERSON><PERSON>", "setLastCheck", "role", "checkExpiredUsers", "result", "console", "log", "Date", "error", "interval", "setInterval", "clearInterval", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/admin/UserExpirationChecker.js"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport userService from '../../services/userService';\nimport { useAuth } from '../../context/AuthContext';\n\n/**\n * Componente invisibile che verifica periodicamente gli utenti scaduti.\n * Viene eseguito solo quando un amministratore è loggato.\n */\nconst UserExpirationChecker = () => {\n  const { user } = useAuth();\n  const [lastCheck, setLastCheck] = useState(null);\n\n  // Verifica gli utenti scaduti all'avvio e ogni ora\n  useEffect(() => {\n    // Esegui solo se l'utente è un amministratore\n    if (user && user.role === 'owner') {\n      // Funzione per verificare gli utenti scaduti\n      const checkExpiredUsers = async () => {\n        try {\n          const result = await userService.checkExpiredUsers();\n          console.log('Verifica utenti scaduti:', result);\n          setLastCheck(new Date());\n        } catch (error) {\n          console.error('Errore durante la verifica degli utenti scaduti:', error);\n          // Non fare nulla in caso di errore, per evitare di bloccare l'applicazione\n          // L'errore potrebbe essere dovuto a un problema di autorizzazione\n          // quando si impersona un utente non amministratore\n        }\n      };\n\n      // Esegui subito la verifica\n      checkExpiredUsers();\n\n      // Imposta un intervallo per verificare ogni ora\n      const interval = setInterval(checkExpiredUsers, 60 * 60 * 1000);\n\n      // Pulisci l'intervallo quando il componente viene smontato\n      return () => clearInterval(interval);\n    }\n  }, [user]);\n\n  // Questo componente non renderizza nulla\n  return null;\n};\n\nexport default UserExpirationChecker;\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,OAAO,QAAQ,2BAA2B;;AAEnD;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAK,CAAC,GAAGH,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAGP,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACAD,SAAS,CAAC,MAAM;IACd;IACA,IAAIM,IAAI,IAAIA,IAAI,CAACG,IAAI,KAAK,OAAO,EAAE;MACjC;MACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpC,IAAI;UACF,MAAMC,MAAM,GAAG,MAAMT,WAAW,CAACQ,iBAAiB,CAAC,CAAC;UACpDE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,MAAM,CAAC;UAC/CH,YAAY,CAAC,IAAIM,IAAI,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdH,OAAO,CAACG,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;UACxE;UACA;UACA;QACF;MACF,CAAC;;MAED;MACAL,iBAAiB,CAAC,CAAC;;MAEnB;MACA,MAAMM,QAAQ,GAAGC,WAAW,CAACP,iBAAiB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;;MAE/D;MACA,OAAO,MAAMQ,aAAa,CAACF,QAAQ,CAAC;IACtC;EACF,CAAC,EAAE,CAACV,IAAI,CAAC,CAAC;;EAEV;EACA,OAAO,IAAI;AACb,CAAC;AAACD,EAAA,CAnCID,qBAAqB;EAAA,QACRD,OAAO;AAAA;AAAAgB,EAAA,GADpBf,qBAAqB;AAqC3B,eAAeA,qBAAqB;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}