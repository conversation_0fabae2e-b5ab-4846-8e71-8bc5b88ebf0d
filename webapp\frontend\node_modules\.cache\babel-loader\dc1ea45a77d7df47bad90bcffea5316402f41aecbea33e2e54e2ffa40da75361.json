{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\posa\\\\EliminaCavoPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Paper, Button, IconButton, Alert, Snackbar, Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions, CircularProgress, TextField, Radio, RadioGroup, FormControlLabel, FormControl, FormLabel } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon, Delete as DeleteIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport caviService from '../../../services/caviService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EliminaCavoPage = () => {\n  _s();\n  var _selectedCavo, _selectedCavo2, _selectedCavo3;\n  const navigate = useNavigate();\n  const {\n    isImpersonating\n  } = useAuth();\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n\n  // Stati per la gestione dei cavi\n  const [loading, setLoading] = useState(false);\n  const [cavoId, setCavoId] = useState('');\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState(''); // 'confirm', 'spare' o 'error'\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n  const [cavoInfo, setCavoInfo] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  const cantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Gestisce il ritorno alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce il ritorno al menu admin (per admin che impersonano utenti)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce il ritorno alla pagina principale di posa cavi\n  const handleBackToPosa = () => {\n    navigate('/dashboard/cavi/posa');\n  };\n\n  // Gestisce il successo di un'operazione\n  const handleSuccess = message => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce l'errore di un'operazione\n  const handleError = message => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  // Chiude il dialogo\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setDialogType('');\n    setCavoInfo(null);\n  };\n\n  // Verifica se il cavo esiste e determina il tipo di dialogo\n  const verificaCavo = async () => {\n    if (!cavoId.trim()) {\n      handleError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setLoading(true);\n\n      // Ottieni i dettagli del cavo\n      const cavi = await caviService.getCavi(cantiereId);\n      const cavo = cavi.find(c => c.id_cavo === cavoId.trim());\n      if (!cavo) {\n        setDialogType('error');\n        setOpenDialog(true);\n        return;\n      }\n      setCavoInfo(cavo);\n\n      // Determina il tipo di dialogo in base allo stato del cavo\n      const isInstalled = cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0;\n      setDialogType(isInstalled ? 'spare' : 'confirm');\n      setOpenDialog(true);\n    } catch (error) {\n      handleError(`Errore durante la verifica del cavo: ${error.message || 'Errore sconosciuto'}`);\n      console.error('Errore durante la verifica del cavo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'eliminazione o la marcatura come SPARE di un cavo\n  const handleDeleteCavo = async () => {\n    if (!cavoInfo) return;\n    try {\n      setLoading(true);\n      if (dialogType === 'spare') {\n        // Marca il cavo come SPARE\n        await caviService.markCavoAsSpare(cantiereId, cavoInfo.id_cavo);\n        handleSuccess(`Cavo ${cavoInfo.id_cavo} marcato come SPARE con successo`);\n      } else {\n        // Elimina il cavo o marcalo come SPARE in base alla modalità selezionata\n        await caviService.deleteCavo(cantiereId, cavoInfo.id_cavo, deleteMode);\n        handleSuccess(`Cavo ${cavoInfo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n      }\n      handleCloseDialog();\n      setCavoId(''); // Resetta il campo di input\n    } catch (error) {\n      handleError(`Errore durante l'operazione: ${error.message || 'Errore sconosciuto'}`);\n      console.error('Errore durante l\\'eliminazione/marcatura del cavo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce il cambio dell'ID cavo\n  const handleCavoIdChange = event => {\n    setCavoId(event.target.value);\n  };\n\n  // Se non c'è un cantiere selezionato, reindirizza alla pagina dei cantieri\n  if (!cantiereId) {\n    navigate('/dashboard/cantieri');\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToPosa,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          children: \"Elimina Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Cantiere: \", cantiereName, \" (ID: \", cantiereId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 24\n          }, this),\n          onClick: handleBackToPosa,\n          children: \"Torna a Posa e Collegamenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Elimina Cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        paragraph: true,\n        children: \"Questa funzionalit\\xE0 consente di eliminare un cavo dal cantiere o marcarlo come SPARE.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"textSecondary\",\n        paragraph: true,\n        children: \"Seleziona un cavo dalla lista. Per i cavi gi\\xE0 posati, verr\\xE0 offerta solo l'opzione di marcarli come SPARE invece di eliminarli completamente.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3,\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Cerca cavo\",\n          variant: \"outlined\",\n          fullWidth: true,\n          value: searchTerm,\n          onChange: handleSearchChange,\n          placeholder: \"Cerca per ID, tipologia, ubicazione...\",\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(SearchIcon, {\n              color: \"action\",\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 31\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: loadCavi,\n          sx: {\n            ml: 2,\n            minWidth: '120px'\n          },\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 24\n          }, this) : \"Aggiorna\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Cavi disponibili\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          p: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this) : filteredCavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: searchTerm ? \"Nessun cavo corrisponde ai criteri di ricerca\" : \"Nessun cavo disponibile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(List, {\n        children: filteredCavi.map(cavo => {\n          const isInstalled = cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0;\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 27\n                  }, this), isInstalled && /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"Installato\",\n                    color: \"primary\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [cavo.tipologia || 'N/A', \" - \", cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', \" A: \", cavo.ubicazione_arrivo || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 27\n                  }, this), cavo.metratura_reale > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      component: \"span\",\n                      children: [\"Metri posati: \", cavo.metratura_reale, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  edge: \"end\",\n                  onClick: () => handleCavoSelect(cavo),\n                  color: \"error\",\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog && dialogType === 'spare',\n      onClose: handleCloseDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\",\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), \"Marca cavo come SPARE\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: (_selectedCavo = selectedCavo) === null || _selectedCavo === void 0 ? void 0 : _selectedCavo.id_cavo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 21\n          }, this), \" risulta installato o parzialmente posato.\", ((_selectedCavo2 = selectedCavo) === null || _selectedCavo2 === void 0 ? void 0 : _selectedCavo2.metratura_reale) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [\" Metri posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [selectedCavo.metratura_reale, \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 32\n            }, this), \".\"]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContentText, {\n          sx: {\n            mt: 2\n          },\n          children: \"Non \\xE8 possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          control: /*#__PURE__*/_jsxDEV(Checkbox, {\n            checked: forceSpare,\n            onChange: e => setForceSpare(e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 15\n          }, this),\n          label: \"Forza marcatura come SPARE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteCavo,\n          color: \"warning\",\n          variant: \"contained\",\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 24\n          }, this) : \"Marca come SPARE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog && dialogType === 'confirm',\n      onClose: handleCloseDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(DeleteIcon, {\n            color: \"error\",\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this), \"Elimina cavo\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"Stai per eliminare il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: (_selectedCavo3 = selectedCavo) === null || _selectedCavo3 === void 0 ? void 0 : _selectedCavo3.id_cavo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 40\n          }, this), \".\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          component: \"fieldset\",\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n            component: \"legend\",\n            children: \"Scegli l'operazione da eseguire:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n            value: deleteMode,\n            onChange: e => setDeleteMode(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n              value: \"spare\",\n              control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 26\n              }, this),\n              label: \"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              value: \"delete\",\n              control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 26\n              }, this),\n              label: \"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteCavo,\n          color: \"error\",\n          variant: \"contained\",\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 24\n          }, this) : deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openSnackbar,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: alertSeverity,\n        sx: {\n          width: '100%'\n        },\n        children: alertMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 5\n  }, this);\n};\n_s(EliminaCavoPage, \"ML9GFPoOhfYiP5uvyTlS7EP6ed4=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = EliminaCavoPage;\nexport default EliminaCavoPage;\nvar _c;\n$RefreshReg$(_c, \"EliminaCavoPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "Snackbar", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogActions", "CircularProgress", "TextField", "Radio", "RadioGroup", "FormControlLabel", "FormControl", "FormLabel", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Delete", "DeleteIcon", "Warning", "WarningIcon", "useNavigate", "useAuth", "AdminHomeButton", "caviService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EliminaCavoPage", "_s", "_selected<PERSON>avo", "_selectedCavo2", "_selectedCavo3", "navigate", "isImpersonating", "alertMessage", "setAlertMessage", "alertSeverity", "setAlertSeverity", "openSnackbar", "setOpenSnackbar", "loading", "setLoading", "cavoId", "setCavoId", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "deleteMode", "setDeleteMode", "cavoInfo", "setCavoInfo", "cantiereId", "localStorage", "getItem", "cantiereName", "handleBackToCantieri", "handleBackToAdmin", "handleBackToPosa", "handleSuccess", "message", "handleError", "handleCloseSnackbar", "handleCloseDialog", "verificaCavo", "trim", "cavi", "get<PERSON><PERSON>", "cavo", "find", "c", "id_cavo", "isInstalled", "stato_installazione", "metratura_reale", "error", "console", "handleDeleteCavo", "markCavoAsSpare", "deleteCavo", "handleCavoIdChange", "event", "target", "value", "children", "sx", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "window", "location", "reload", "ml", "color", "title", "p", "startIcon", "gutterBottom", "paragraph", "label", "fullWidth", "searchTerm", "onChange", "handleSearchChange", "placeholder", "InputProps", "startAdornment", "SearchIcon", "loadCavi", "min<PERSON><PERSON><PERSON>", "disabled", "size", "filteredCavi", "length", "severity", "mt", "List", "map", "ListItem", "button", "handleCavoSelect", "ListItemText", "primary", "Chip", "secondary", "component", "tipologia", "n_conduttori", "sezione", "ubicazione_partenza", "ubicazione_arrivo", "ListItemSecondaryAction", "edge", "Divider", "open", "onClose", "selected<PERSON><PERSON><PERSON>", "control", "Checkbox", "checked", "forceSpare", "e", "setForceSpare", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/posa/EliminaCavoPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert,\n  Snackbar,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogContentText,\n  DialogActions,\n  CircularProgress,\n  TextField,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  FormControl,\n  FormLabel\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Delete as DeleteIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport caviService from '../../../services/caviService';\n\nconst EliminaCavoPage = () => {\n  const navigate = useNavigate();\n  const { isImpersonating } = useAuth();\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n\n  // Stati per la gestione dei cavi\n  const [loading, setLoading] = useState(false);\n  const [cavoId, setCavoId] = useState('');\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState(''); // 'confirm', 'spare' o 'error'\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n  const [cavoInfo, setCavoInfo] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  const cantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Gestisce il ritorno alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce il ritorno al menu admin (per admin che impersonano utenti)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce il ritorno alla pagina principale di posa cavi\n  const handleBackToPosa = () => {\n    navigate('/dashboard/cavi/posa');\n  };\n\n  // Gestisce il successo di un'operazione\n  const handleSuccess = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce l'errore di un'operazione\n  const handleError = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  // Chiude il dialogo\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setDialogType('');\n    setCavoInfo(null);\n  };\n\n  // Verifica se il cavo esiste e determina il tipo di dialogo\n  const verificaCavo = async () => {\n    if (!cavoId.trim()) {\n      handleError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      // Ottieni i dettagli del cavo\n      const cavi = await caviService.getCavi(cantiereId);\n      const cavo = cavi.find(c => c.id_cavo === cavoId.trim());\n\n      if (!cavo) {\n        setDialogType('error');\n        setOpenDialog(true);\n        return;\n      }\n\n      setCavoInfo(cavo);\n\n      // Determina il tipo di dialogo in base allo stato del cavo\n      const isInstalled = cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0);\n      setDialogType(isInstalled ? 'spare' : 'confirm');\n      setOpenDialog(true);\n    } catch (error) {\n      handleError(`Errore durante la verifica del cavo: ${error.message || 'Errore sconosciuto'}`);\n      console.error('Errore durante la verifica del cavo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'eliminazione o la marcatura come SPARE di un cavo\n  const handleDeleteCavo = async () => {\n    if (!cavoInfo) return;\n\n    try {\n      setLoading(true);\n\n      if (dialogType === 'spare') {\n        // Marca il cavo come SPARE\n        await caviService.markCavoAsSpare(cantiereId, cavoInfo.id_cavo);\n        handleSuccess(`Cavo ${cavoInfo.id_cavo} marcato come SPARE con successo`);\n      } else {\n        // Elimina il cavo o marcalo come SPARE in base alla modalità selezionata\n        await caviService.deleteCavo(cantiereId, cavoInfo.id_cavo, deleteMode);\n        handleSuccess(`Cavo ${cavoInfo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n      }\n\n      handleCloseDialog();\n      setCavoId(''); // Resetta il campo di input\n    } catch (error) {\n      handleError(`Errore durante l'operazione: ${error.message || 'Errore sconosciuto'}`);\n      console.error('Errore durante l\\'eliminazione/marcatura del cavo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce il cambio dell'ID cavo\n  const handleCavoIdChange = (event) => {\n    setCavoId(event.target.value);\n  };\n\n  // Se non c'è un cantiere selezionato, reindirizza alla pagina dei cantieri\n  if (!cantiereId) {\n    navigate('/dashboard/cantieri');\n    return null;\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToPosa} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Elimina Cavo\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h6\">\n            Cantiere: {cantiereName} (ID: {cantiereId})\n          </Typography>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<ArrowBackIcon />}\n            onClick={handleBackToPosa}\n          >\n            Torna a Posa e Collegamenti\n          </Button>\n        </Box>\n      </Paper>\n\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Elimina Cavo\n        </Typography>\n        <Typography variant=\"body1\" paragraph>\n          Questa funzionalità consente di eliminare un cavo dal cantiere o marcarlo come SPARE.\n        </Typography>\n        <Typography variant=\"body2\" color=\"textSecondary\" paragraph>\n          Seleziona un cavo dalla lista. Per i cavi già posati, verrà offerta solo l'opzione di marcarli come SPARE invece di eliminarli completamente.\n        </Typography>\n\n        {/* Campo di ricerca */}\n        <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>\n          <TextField\n            label=\"Cerca cavo\"\n            variant=\"outlined\"\n            fullWidth\n            value={searchTerm}\n            onChange={handleSearchChange}\n            placeholder=\"Cerca per ID, tipologia, ubicazione...\"\n            InputProps={{\n              startAdornment: <SearchIcon color=\"action\" sx={{ mr: 1 }} />\n            }}\n          />\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={loadCavi}\n            sx={{ ml: 2, minWidth: '120px' }}\n            disabled={loading}\n          >\n            {loading ? <CircularProgress size={24} /> : \"Aggiorna\"}\n          </Button>\n        </Box>\n      </Paper>\n\n      {/* Lista dei cavi */}\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Cavi disponibili\n        </Typography>\n\n        {loading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>\n            <CircularProgress />\n          </Box>\n        ) : filteredCavi.length === 0 ? (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            {searchTerm ? \"Nessun cavo corrisponde ai criteri di ricerca\" : \"Nessun cavo disponibile\"}\n          </Alert>\n        ) : (\n          <List>\n            {filteredCavi.map((cavo) => {\n              const isInstalled = cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0);\n              return (\n                <React.Fragment key={cavo.id_cavo}>\n                  <ListItem button onClick={() => handleCavoSelect(cavo)}>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle1\">{cavo.id_cavo}</Typography>\n                          {isInstalled && (\n                            <Chip\n                              size=\"small\"\n                              label=\"Installato\"\n                              color=\"primary\"\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <>\n                          <Typography variant=\"body2\" component=\"span\">\n                            {cavo.tipologia || 'N/A'} - {cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Da: {cavo.ubicazione_partenza || 'N/A'} A: {cavo.ubicazione_arrivo || 'N/A'}\n                          </Typography>\n                          {cavo.metratura_reale > 0 && (\n                            <>\n                              <br />\n                              <Typography variant=\"body2\" component=\"span\">\n                                Metri posati: {cavo.metratura_reale} m\n                              </Typography>\n                            </>\n                          )}\n                        </>\n                      }\n                    />\n                    <ListItemSecondaryAction>\n                      <IconButton edge=\"end\" onClick={() => handleCavoSelect(cavo)} color=\"error\">\n                        <DeleteIcon />\n                      </IconButton>\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                  <Divider />\n                </React.Fragment>\n              );\n            })}\n          </List>\n        )}\n      </Paper>\n\n      {/* Dialogo di conferma per cavi installati (solo SPARE) */}\n      <Dialog open={openDialog && dialogType === 'spare'} onClose={handleCloseDialog}>\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <WarningIcon color=\"warning\" sx={{ mr: 1 }} />\n            Marca cavo come SPARE\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Il cavo <strong>{selectedCavo?.id_cavo}</strong> risulta installato o parzialmente posato.\n            {selectedCavo?.metratura_reale > 0 && (\n              <> Metri posati: <strong>{selectedCavo.metratura_reale} m</strong>.</>\n            )}\n          </DialogContentText>\n          <DialogContentText sx={{ mt: 2 }}>\n            Non è possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\n          </DialogContentText>\n          <FormControlLabel\n            control={\n              <Checkbox\n                checked={forceSpare}\n                onChange={(e) => setForceSpare(e.target.checked)}\n              />\n            }\n            label=\"Forza marcatura come SPARE\"\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>Annulla</Button>\n          <Button\n            onClick={handleDeleteCavo}\n            color=\"warning\"\n            variant=\"contained\"\n            disabled={loading}\n          >\n            {loading ? <CircularProgress size={24} /> : \"Marca come SPARE\"}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo di conferma per cavi non installati (scelta tra SPARE e DELETE) */}\n      <Dialog open={openDialog && dialogType === 'confirm'} onClose={handleCloseDialog}>\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <DeleteIcon color=\"error\" sx={{ mr: 1 }} />\n            Elimina cavo\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Stai per eliminare il cavo <strong>{selectedCavo?.id_cavo}</strong>.\n          </DialogContentText>\n\n          <FormControl component=\"fieldset\" sx={{ mt: 2 }}>\n            <FormLabel component=\"legend\">Scegli l'operazione da eseguire:</FormLabel>\n            <RadioGroup\n              value={deleteMode}\n              onChange={(e) => setDeleteMode(e.target.value)}\n            >\n              <FormControlLabel\n                value=\"spare\"\n                control={<Radio />}\n                label=\"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n              />\n              <FormControlLabel\n                value=\"delete\"\n                control={<Radio />}\n                label=\"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n              />\n            </RadioGroup>\n          </FormControl>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>Annulla</Button>\n          <Button\n            onClick={handleDeleteCavo}\n            color=\"error\"\n            variant=\"contained\"\n            disabled={loading}\n          >\n            {loading ? <CircularProgress size={24} /> : (deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\")}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      <Snackbar\n        open={openSnackbar}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>\n          {alertMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default EliminaCavoPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,WAAW,EACXC,SAAS,QACJ,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,WAAW,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA;EAC5B,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc;EAAgB,CAAC,GAAGb,OAAO,CAAC,CAAC;EACrC,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6C,aAAa,EAAEC,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmD,MAAM,EAAEC,SAAS,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAClD,MAAM,CAACyD,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC2D,QAAQ,EAAEC,WAAW,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAM6D,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EAC7D,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEjE;EACA,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjCxB,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAED;EACA,MAAMyB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BzB,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;;EAED;EACA,MAAM0B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B1B,QAAQ,CAAC,sBAAsB,CAAC;EAClC,CAAC;;EAED;EACA,MAAM2B,aAAa,GAAIC,OAAO,IAAK;IACjCzB,eAAe,CAACyB,OAAO,CAAC;IACxBvB,gBAAgB,CAAC,SAAS,CAAC;IAC3BE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsB,WAAW,GAAID,OAAO,IAAK;IAC/BzB,eAAe,CAACyB,OAAO,CAAC;IACxBvB,gBAAgB,CAAC,OAAO,CAAC;IACzBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuB,mBAAmB,GAAGA,CAAA,KAAM;IAChCvB,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAMwB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BlB,aAAa,CAAC,KAAK,CAAC;IACpBE,aAAa,CAAC,EAAE,CAAC;IACjBI,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,MAAMa,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACtB,MAAM,CAACuB,IAAI,CAAC,CAAC,EAAE;MAClBJ,WAAW,CAAC,6BAA6B,CAAC;MAC1C;IACF;IAEA,IAAI;MACFpB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMyB,IAAI,GAAG,MAAM5C,WAAW,CAAC6C,OAAO,CAACf,UAAU,CAAC;MAClD,MAAMgB,IAAI,GAAGF,IAAI,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAK7B,MAAM,CAACuB,IAAI,CAAC,CAAC,CAAC;MAExD,IAAI,CAACG,IAAI,EAAE;QACTrB,aAAa,CAAC,OAAO,CAAC;QACtBF,aAAa,CAAC,IAAI,CAAC;QACnB;MACF;MAEAM,WAAW,CAACiB,IAAI,CAAC;;MAEjB;MACA,MAAMI,WAAW,GAAGJ,IAAI,CAACK,mBAAmB,KAAK,YAAY,IAAKL,IAAI,CAACM,eAAe,IAAIN,IAAI,CAACM,eAAe,GAAG,CAAE;MACnH3B,aAAa,CAACyB,WAAW,GAAG,OAAO,GAAG,SAAS,CAAC;MAChD3B,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACdd,WAAW,CAAC,wCAAwCc,KAAK,CAACf,OAAO,IAAI,oBAAoB,EAAE,CAAC;MAC5FgB,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAC3B,QAAQ,EAAE;IAEf,IAAI;MACFT,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIK,UAAU,KAAK,OAAO,EAAE;QAC1B;QACA,MAAMxB,WAAW,CAACwD,eAAe,CAAC1B,UAAU,EAAEF,QAAQ,CAACqB,OAAO,CAAC;QAC/DZ,aAAa,CAAC,QAAQT,QAAQ,CAACqB,OAAO,kCAAkC,CAAC;MAC3E,CAAC,MAAM;QACL;QACA,MAAMjD,WAAW,CAACyD,UAAU,CAAC3B,UAAU,EAAEF,QAAQ,CAACqB,OAAO,EAAEvB,UAAU,CAAC;QACtEW,aAAa,CAAC,QAAQT,QAAQ,CAACqB,OAAO,IAAIvB,UAAU,KAAK,OAAO,GAAG,oBAAoB,GAAG,WAAW,eAAe,CAAC;MACvH;MAEAe,iBAAiB,CAAC,CAAC;MACnBpB,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdd,WAAW,CAAC,gCAAgCc,KAAK,CAACf,OAAO,IAAI,oBAAoB,EAAE,CAAC;MACpFgB,OAAO,CAACD,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;IAC5E,CAAC,SAAS;MACRlC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuC,kBAAkB,GAAIC,KAAK,IAAK;IACpCtC,SAAS,CAACsC,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EAC/B,CAAC;;EAED;EACA,IAAI,CAAC/B,UAAU,EAAE;IACfpB,QAAQ,CAAC,qBAAqB,CAAC;IAC/B,OAAO,IAAI;EACb;EAEA,oBACER,OAAA,CAAChC,GAAG;IAAA4F,QAAA,gBACF5D,OAAA,CAAChC,GAAG;MAAC6F,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAL,QAAA,gBACzF5D,OAAA,CAAChC,GAAG;QAAC6F,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBACjD5D,OAAA,CAAC5B,UAAU;UAAC8F,OAAO,EAAEhC,gBAAiB;UAAC2B,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,eACnD5D,OAAA,CAACZ,aAAa;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACbvE,OAAA,CAAC/B,UAAU;UAACuG,OAAO,EAAC,IAAI;UAAAZ,QAAA,EAAC;QAEzB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvE,OAAA,CAAC5B,UAAU;UACT8F,OAAO,EAAEA,CAAA,KAAMO,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCd,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE,CAAE;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAC,oBAAoB;UAAAlB,QAAA,eAE1B5D,OAAA,CAACV,WAAW;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNvE,OAAA,CAACH,eAAe;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAENvE,OAAA,CAAC9B,KAAK;MAAC2F,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEiB,CAAC,EAAE;MAAE,CAAE;MAAAnB,QAAA,eACzB5D,OAAA,CAAChC,GAAG;QAAC6F,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,eAAe;UAAED,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBAClF5D,OAAA,CAAC/B,UAAU;UAACuG,OAAO,EAAC,IAAI;UAAAZ,QAAA,GAAC,YACb,EAAC7B,YAAY,EAAC,QAAM,EAACH,UAAU,EAAC,GAC5C;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvE,OAAA,CAAC7B,MAAM;UACLqG,OAAO,EAAC,WAAW;UACnBK,KAAK,EAAC,SAAS;UACfG,SAAS,eAAEhF,OAAA,CAACZ,aAAa;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BL,OAAO,EAAEhC,gBAAiB;UAAA0B,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAERvE,OAAA,CAAC9B,KAAK;MAAC2F,EAAE,EAAE;QAAEkB,CAAC,EAAE,CAAC;QAAEjB,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBACzB5D,OAAA,CAAC/B,UAAU;QAACuG,OAAO,EAAC,IAAI;QAACS,YAAY;QAAArB,QAAA,EAAC;MAEtC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvE,OAAA,CAAC/B,UAAU;QAACuG,OAAO,EAAC,OAAO;QAACU,SAAS;QAAAtB,QAAA,EAAC;MAEtC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvE,OAAA,CAAC/B,UAAU;QAACuG,OAAO,EAAC,OAAO;QAACK,KAAK,EAAC,eAAe;QAACK,SAAS;QAAAtB,QAAA,EAAC;MAE5D;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbvE,OAAA,CAAChC,GAAG;QAAC6F,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBACxD5D,OAAA,CAACnB,SAAS;UACRsG,KAAK,EAAC,YAAY;UAClBX,OAAO,EAAC,UAAU;UAClBY,SAAS;UACTzB,KAAK,EAAE0B,UAAW;UAClBC,QAAQ,EAAEC,kBAAmB;UAC7BC,WAAW,EAAC,wCAAwC;UACpDC,UAAU,EAAE;YACVC,cAAc,eAAE1F,OAAA,CAAC2F,UAAU;cAACd,KAAK,EAAC,QAAQ;cAAChB,EAAE,EAAE;gBAAEM,EAAE,EAAE;cAAE;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAC7D;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFvE,OAAA,CAAC7B,MAAM;UACLqG,OAAO,EAAC,WAAW;UACnBK,KAAK,EAAC,SAAS;UACfX,OAAO,EAAE0B,QAAS;UAClB/B,EAAE,EAAE;YAAEe,EAAE,EAAE,CAAC;YAAEiB,QAAQ,EAAE;UAAQ,CAAE;UACjCC,QAAQ,EAAE9E,OAAQ;UAAA4C,QAAA,EAEjB5C,OAAO,gBAAGhB,OAAA,CAACpB,gBAAgB;YAACmH,IAAI,EAAE;UAAG;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAU;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRvE,OAAA,CAAC9B,KAAK;MAAC2F,EAAE,EAAE;QAAEkB,CAAC,EAAE;MAAE,CAAE;MAAAnB,QAAA,gBAClB5D,OAAA,CAAC/B,UAAU;QAACuG,OAAO,EAAC,IAAI;QAACS,YAAY;QAAArB,QAAA,EAAC;MAEtC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZvD,OAAO,gBACNhB,OAAA,CAAChC,GAAG;QAAC6F,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,QAAQ;UAAEc,CAAC,EAAE;QAAE,CAAE;QAAAnB,QAAA,eAC3D5D,OAAA,CAACpB,gBAAgB;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GACJyB,YAAY,CAACC,MAAM,KAAK,CAAC,gBAC3BjG,OAAA,CAAC3B,KAAK;QAAC6H,QAAQ,EAAC,MAAM;QAACrC,EAAE,EAAE;UAAEsC,EAAE,EAAE;QAAE,CAAE;QAAAvC,QAAA,EAClCyB,UAAU,GAAG,+CAA+C,GAAG;MAAyB;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC,gBAERvE,OAAA,CAACoG,IAAI;QAAAxC,QAAA,EACFoC,YAAY,CAACK,GAAG,CAAEzD,IAAI,IAAK;UAC1B,MAAMI,WAAW,GAAGJ,IAAI,CAACK,mBAAmB,KAAK,YAAY,IAAKL,IAAI,CAACM,eAAe,IAAIN,IAAI,CAACM,eAAe,GAAG,CAAE;UACnH,oBACElD,OAAA,CAAClC,KAAK,CAACmC,QAAQ;YAAA2D,QAAA,gBACb5D,OAAA,CAACsG,QAAQ;cAACC,MAAM;cAACrC,OAAO,EAAEA,CAAA,KAAMsC,gBAAgB,CAAC5D,IAAI,CAAE;cAAAgB,QAAA,gBACrD5D,OAAA,CAACyG,YAAY;gBACXC,OAAO,eACL1G,OAAA,CAAChC,GAAG;kBAAC6F,EAAE,EAAE;oBAAEE,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAAJ,QAAA,gBACjD5D,OAAA,CAAC/B,UAAU;oBAACuG,OAAO,EAAC,WAAW;oBAAAZ,QAAA,EAAEhB,IAAI,CAACG;kBAAO;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,EAC1DvB,WAAW,iBACVhD,OAAA,CAAC2G,IAAI;oBACHZ,IAAI,EAAC,OAAO;oBACZZ,KAAK,EAAC,YAAY;oBAClBN,KAAK,EAAC,SAAS;oBACfhB,EAAE,EAAE;sBAAEe,EAAE,EAAE;oBAAE;kBAAE;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACDqC,SAAS,eACP5G,OAAA,CAAAE,SAAA;kBAAA0D,QAAA,gBACE5D,OAAA,CAAC/B,UAAU;oBAACuG,OAAO,EAAC,OAAO;oBAACqC,SAAS,EAAC,MAAM;oBAAAjD,QAAA,GACzChB,IAAI,CAACkE,SAAS,IAAI,KAAK,EAAC,KAAG,EAAClE,IAAI,CAACmE,YAAY,IAAI,KAAK,EAAC,KAAG,EAACnE,IAAI,CAACoE,OAAO,IAAI,KAAK;kBAAA;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACbvE,OAAA;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNvE,OAAA,CAAC/B,UAAU;oBAACuG,OAAO,EAAC,OAAO;oBAACqC,SAAS,EAAC,MAAM;oBAAAjD,QAAA,GAAC,MACvC,EAAChB,IAAI,CAACqE,mBAAmB,IAAI,KAAK,EAAC,MAAI,EAACrE,IAAI,CAACsE,iBAAiB,IAAI,KAAK;kBAAA;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,EACZ3B,IAAI,CAACM,eAAe,GAAG,CAAC,iBACvBlD,OAAA,CAAAE,SAAA;oBAAA0D,QAAA,gBACE5D,OAAA;sBAAAoE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNvE,OAAA,CAAC/B,UAAU;sBAACuG,OAAO,EAAC,OAAO;sBAACqC,SAAS,EAAC,MAAM;sBAAAjD,QAAA,GAAC,gBAC7B,EAAChB,IAAI,CAACM,eAAe,EAAC,IACtC;oBAAA;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA,eACb,CACH;gBAAA,eACD;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFvE,OAAA,CAACmH,uBAAuB;gBAAAvD,QAAA,eACtB5D,OAAA,CAAC5B,UAAU;kBAACgJ,IAAI,EAAC,KAAK;kBAAClD,OAAO,EAAEA,CAAA,KAAMsC,gBAAgB,CAAC5D,IAAI,CAAE;kBAACiC,KAAK,EAAC,OAAO;kBAAAjB,QAAA,eACzE5D,OAAA,CAACR,UAAU;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACXvE,OAAA,CAACqH,OAAO;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GA1CQ3B,IAAI,CAACG,OAAO;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2CjB,CAAC;QAErB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGRvE,OAAA,CAACzB,MAAM;MAAC+I,IAAI,EAAElG,UAAU,IAAIE,UAAU,KAAK,OAAQ;MAACiG,OAAO,EAAEhF,iBAAkB;MAAAqB,QAAA,gBAC7E5D,OAAA,CAACxB,WAAW;QAAAoF,QAAA,eACV5D,OAAA,CAAChC,GAAG;UAAC6F,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAJ,QAAA,gBACjD5D,OAAA,CAACN,WAAW;YAACmF,KAAK,EAAC,SAAS;YAAChB,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,yBAEhD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdvE,OAAA,CAACvB,aAAa;QAAAmF,QAAA,gBACZ5D,OAAA,CAACtB,iBAAiB;UAAAkF,QAAA,GAAC,UACT,eAAA5D,OAAA;YAAA4D,QAAA,GAAAvD,aAAA,GAASmH,YAAY,cAAAnH,aAAA,uBAAZA,aAAA,CAAc0C;UAAO;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,8CAChD,EAAC,EAAAjE,cAAA,GAAAkH,YAAY,cAAAlH,cAAA,uBAAZA,cAAA,CAAc4C,eAAe,IAAG,CAAC,iBAChClD,OAAA,CAAAE,SAAA;YAAA0D,QAAA,GAAE,iBAAe,eAAA5D,OAAA;cAAA4D,QAAA,GAAS4D,YAAY,CAACtE,eAAe,EAAC,IAAE;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC;UAAA,eAAE,CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACgB,CAAC,eACpBvE,OAAA,CAACtB,iBAAiB;UAACmF,EAAE,EAAE;YAAEsC,EAAE,EAAE;UAAE,CAAE;UAAAvC,QAAA,EAAC;QAElC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBvE,OAAA,CAAChB,gBAAgB;UACfyI,OAAO,eACLzH,OAAA,CAAC0H,QAAQ;YACPC,OAAO,EAAEC,UAAW;YACpBtC,QAAQ,EAAGuC,CAAC,IAAKC,aAAa,CAACD,CAAC,CAACnE,MAAM,CAACiE,OAAO;UAAE;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CACF;UACDY,KAAK,EAAC;QAA4B;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBvE,OAAA,CAACrB,aAAa;QAAAiF,QAAA,gBACZ5D,OAAA,CAAC7B,MAAM;UAAC+F,OAAO,EAAE3B,iBAAkB;UAAAqB,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpDvE,OAAA,CAAC7B,MAAM;UACL+F,OAAO,EAAEb,gBAAiB;UAC1BwB,KAAK,EAAC,SAAS;UACfL,OAAO,EAAC,WAAW;UACnBsB,QAAQ,EAAE9E,OAAQ;UAAA4C,QAAA,EAEjB5C,OAAO,gBAAGhB,OAAA,CAACpB,gBAAgB;YAACmH,IAAI,EAAE;UAAG;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAkB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTvE,OAAA,CAACzB,MAAM;MAAC+I,IAAI,EAAElG,UAAU,IAAIE,UAAU,KAAK,SAAU;MAACiG,OAAO,EAAEhF,iBAAkB;MAAAqB,QAAA,gBAC/E5D,OAAA,CAACxB,WAAW;QAAAoF,QAAA,eACV5D,OAAA,CAAChC,GAAG;UAAC6F,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAJ,QAAA,gBACjD5D,OAAA,CAACR,UAAU;YAACqF,KAAK,EAAC,OAAO;YAAChB,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdvE,OAAA,CAACvB,aAAa;QAAAmF,QAAA,gBACZ5D,OAAA,CAACtB,iBAAiB;UAAAkF,QAAA,GAAC,6BACU,eAAA5D,OAAA;YAAA4D,QAAA,GAAArD,cAAA,GAASiH,YAAY,cAAAjH,cAAA,uBAAZA,cAAA,CAAcwC;UAAO;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,KACrE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eAEpBvE,OAAA,CAACf,WAAW;UAAC4H,SAAS,EAAC,UAAU;UAAChD,EAAE,EAAE;YAAEsC,EAAE,EAAE;UAAE,CAAE;UAAAvC,QAAA,gBAC9C5D,OAAA,CAACd,SAAS;YAAC2H,SAAS,EAAC,QAAQ;YAAAjD,QAAA,EAAC;UAAgC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAC1EvE,OAAA,CAACjB,UAAU;YACT4E,KAAK,EAAEnC,UAAW;YAClB8D,QAAQ,EAAGuC,CAAC,IAAKpG,aAAa,CAACoG,CAAC,CAACnE,MAAM,CAACC,KAAK,CAAE;YAAAC,QAAA,gBAE/C5D,OAAA,CAAChB,gBAAgB;cACf2E,KAAK,EAAC,OAAO;cACb8D,OAAO,eAAEzH,OAAA,CAAClB,KAAK;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBY,KAAK,EAAC;YAAqF;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC,eACFvE,OAAA,CAAChB,gBAAgB;cACf2E,KAAK,EAAC,QAAQ;cACd8D,OAAO,eAAEzH,OAAA,CAAClB,KAAK;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBY,KAAK,EAAC;YAAsE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAChBvE,OAAA,CAACrB,aAAa;QAAAiF,QAAA,gBACZ5D,OAAA,CAAC7B,MAAM;UAAC+F,OAAO,EAAE3B,iBAAkB;UAAAqB,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpDvE,OAAA,CAAC7B,MAAM;UACL+F,OAAO,EAAEb,gBAAiB;UAC1BwB,KAAK,EAAC,OAAO;UACbL,OAAO,EAAC,WAAW;UACnBsB,QAAQ,EAAE9E,OAAQ;UAAA4C,QAAA,EAEjB5C,OAAO,gBAAGhB,OAAA,CAACpB,gBAAgB;YAACmH,IAAI,EAAE;UAAG;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAI/C,UAAU,KAAK,OAAO,GAAG,kBAAkB,GAAG;QAA0B;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAETvE,OAAA,CAAC1B,QAAQ;MACPgJ,IAAI,EAAExG,YAAa;MACnBiH,gBAAgB,EAAE,IAAK;MACvBR,OAAO,EAAEjF,mBAAoB;MAC7B0F,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAtE,QAAA,eAE3D5D,OAAA,CAAC3B,KAAK;QAACkJ,OAAO,EAAEjF,mBAAoB;QAAC4D,QAAQ,EAAEtF,aAAc;QAACiD,EAAE,EAAE;UAAEsE,KAAK,EAAE;QAAO,CAAE;QAAAvE,QAAA,EACjFlD;MAAY;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACnE,EAAA,CArXID,eAAe;EAAA,QACFR,WAAW,EACAC,OAAO;AAAA;AAAAwI,EAAA,GAF/BjI,eAAe;AAuXrB,eAAeA,eAAe;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}