{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeListRivoluzionato.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { Box, Typography, Button, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, CircularProgress, Tooltip, List, ListItem, ListItemText, Accordion, AccordionSummary, AccordionDetails, Stack, MenuItem, Divider } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Assignment as AssignIcon, Person as PersonIcon, Email as EmailIcon, Phone as PhoneIcon, ExpandMore as ExpandMoreIcon, CheckCircle as CheckCircleIcon, Verified as VerifiedIcon, Visibility as ViewIcon, People as PeopleIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport ResponsabiliListPopup from './ResponsabiliListPopup';\nimport ComandeListTable from './ComandeListTable';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ComandeListRivoluzionato = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [allComande, setAllComande] = useState([]);\n  const [loadingComande, setLoadingComande] = useState(false);\n\n  // Stati per popup responsabili\n  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [dialogModeComanda, setDialogModeComanda] = useState('view'); // 'view', 'edit'\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n  const loadComande = async () => {\n    try {\n      setLoadingComande(true);\n      const comandeData = await comandeService.getComande(cantiereId);\n      setAllComande(comandeData || []);\n    } catch (err) {\n      console.error('Errore nel caricamento comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoadingComande(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      console.log('📊 Statistiche ricevute:', stats);\n      setStatistiche(stats);\n    } catch (err) {\n      var _err$response;\n      console.error('❌ Errore nel caricamento delle statistiche:', err);\n      console.error('❌ Dettagli errore:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    if (cantiereId) {\n      loadResponsabili();\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n        handleOpenComandaDialog('view', comandaTrovata);\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenResponsabileDialog(true);\n  };\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDeleteResponsabile = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (mode, comanda = null) => {\n    setDialogModeComanda(mode);\n    setSelectedComanda(comanda);\n    if (mode === 'edit' && comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        priorita: comanda.priorita || 'NORMALE',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n    setOpenComandaDialog(true);\n  };\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n  };\n  const handleSubmitComanda = async () => {\n    try {\n      if (dialogModeComanda === 'edit') {\n        await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n        handleCloseComandaDialog();\n        await loadResponsabili(); // Ricarica per aggiornare le comande\n        await loadComande();\n        await loadStatistiche();\n      }\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n  const handleDeleteComanda = async codiceComanda => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing/Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n  const getStatoColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n  const getPrioritaColor = priorita => {\n    const colors = {\n      'BASSA': 'default',\n      'NORMALE': 'primary',\n      'ALTA': 'warning',\n      'URGENTE': 'error'\n    };\n    return colors[priorita] || 'default';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600,\n          color: 'primary.main'\n        },\n        children: cantiereName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 9\n    }, this), searchingComanda && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: [\"\\uD83D\\uDD0D Ricerca comanda \", searchingComanda, \" in corso...\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 9\n    }, this), statistiche && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        bgcolor: 'grey.50'\n      },\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 4,\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n            color: \"primary\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.responsabili_attivi || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            color: \"info\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.totale_comande || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Totale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            color: \"warning\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_in_corso || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"In Corso\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(VerifiedIcon, {\n            color: \"success\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_completate || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Completate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 32,\n              height: 32,\n              borderRadius: '50%',\n              bgcolor: statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.8 ? 'success.main' : statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.5 ? 'warning.main' : 'error.main',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              fontWeight: \"bold\",\n              color: \"white\",\n              children: [statistiche.totale_comande > 0 ? Math.round(statistiche.comande_completate / statistiche.totale_comande * 100) : 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"medium\",\n              sx: {\n                lineHeight: 1\n              },\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [statistiche.comande_create || 0, \" create\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Gestione Responsabili e Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            gap: 2,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 28\n              }, this),\n              onClick: () => setOpenResponsabiliPopup(true),\n              sx: {\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#f5f7fa',\n                color: '#2196f3',\n                border: '1px solid #2196f3',\n                '&:hover': {\n                  backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                  borderColor: '#1976d2'\n                }\n              },\n              children: \"Lista Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 28\n              }, this),\n              onClick: () => handleOpenResponsabileDialog('create'),\n              sx: {\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#2196f3',\n                color: 'white',\n                '&:hover': {\n                  backgroundColor: '#1976d2'\n                }\n              },\n              children: \"Crea Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 11\n        }, this), loadingComande ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          py: 4,\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 13\n        }, this) : allComande.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 0,\n          sx: {\n            p: 6,\n            textAlign: 'center',\n            backgroundColor: 'grey.50',\n            border: '1px dashed',\n            borderColor: 'grey.300'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            sx: {\n              fontSize: 48,\n              color: 'grey.400',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: \"Nessuna comanda disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"Crea la prima comanda per iniziare a gestire i lavori\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 28\n            }, this),\n            onClick: () => setOpenCreaConCavi(true),\n            sx: {\n              textTransform: 'none',\n              backgroundColor: '#2196f3',\n              color: 'white',\n              '&:hover': {\n                backgroundColor: '#1976d2'\n              }\n            },\n            children: \"Crea Prima Comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ComandeListTable, {\n          comande: allComande,\n          onViewComanda: handleOpenComandaDialog.bind(null, 'view'),\n          onEditComanda: handleOpenComandaDialog.bind(null, 'edit'),\n          onDeleteComanda: handleDeleteComanda,\n          loading: loadingComande\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openResponsabileDialog,\n      onClose: handleCloseResponsabileDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nome Responsabile\",\n            value: formDataResponsabile.nome_responsabile,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              nome_responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: formDataResponsabile.email,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Email per notifiche (opzionale se inserisci telefono)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Telefono\",\n            value: formDataResponsabile.telefono,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              telefono: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Numero per SMS (opzionale se inserisci email)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 644,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseResponsabileDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 680,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitResponsabile,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 679,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 629,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openComandaDialog,\n      onClose: handleCloseComandaDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeComanda === 'view' ? 'Dettagli Comanda' : 'Modifica Comanda'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 711,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 710,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: dialogModeComanda === 'view' && selectedComanda ? /*#__PURE__*/_jsxDEV(List, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Codice Comanda\",\n                secondary: selectedComanda.codice_comanda\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Tipo\",\n                secondary: getTipoComandaLabel(selectedComanda.tipo_comanda)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Stato\",\n                secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: selectedComanda.stato,\n                  color: getStatoColor(selectedComanda.stato),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Descrizione\",\n                secondary: selectedComanda.descrizione || 'Nessuna descrizione'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 747,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Priorit\\xE0\",\n                secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: selectedComanda.priorita || 'NORMALE',\n                  color: getPrioritaColor(selectedComanda.priorita || 'NORMALE'),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Responsabile\",\n                secondary: selectedComanda.responsabile || 'Non assegnato'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 17\n            }, this), selectedComanda.note_capo_cantiere && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Note Capo Cantiere\",\n                  secondary: selectedComanda.note_capo_cantiere\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Data Creazione\",\n                secondary: new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 785,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 17\n            }, this), selectedComanda.data_scadenza && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Data Scadenza\",\n                  secondary: new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 794,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Cavi Assegnati\",\n                secondary: selectedComanda.numero_cavi_assegnati || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 803,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Completamento\",\n                secondary: `${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 809,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 718,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Tipo Comanda\",\n              value: formDataComanda.tipo_comanda,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                tipo_comanda: e.target.value\n              }),\n              margin: \"normal\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"POSA\",\n                children: \"Posa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_PARTENZA\",\n                children: \"Collegamento Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_ARRIVO\",\n                children: \"Collegamento Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"CERTIFICAZIONE\",\n                children: \"Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 830,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"TESTING\",\n                children: \"Testing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Priorit\\xE0\",\n              value: formDataComanda.priorita,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                priorita: e.target.value\n              }),\n              margin: \"normal\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BASSA\",\n                children: \"Bassa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 843,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NORMALE\",\n                children: \"Normale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"ALTA\",\n                children: \"Alta\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"URGENTE\",\n                children: \"Urgente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione\",\n              value: formDataComanda.descrizione,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                descrizione: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 3,\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 849,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Responsabile\",\n              value: formDataComanda.responsabile,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                responsabile: e.target.value\n              }),\n              margin: \"normal\",\n              required: true,\n              helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 860,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note Capo Cantiere\",\n              value: formDataComanda.note_capo_cantiere,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                note_capo_cantiere: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 2,\n              helperText: \"Istruzioni specifiche per il responsabile\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Data Scadenza\",\n              type: \"date\",\n              value: formDataComanda.data_scadenza,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                data_scadenza: e.target.value\n              }),\n              margin: \"normal\",\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 716,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 715,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseComandaDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: dialogModeComanda === 'view' ? 'Chiudi' : 'Annulla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 899,\n          columnNumber: 11\n        }, this), dialogModeComanda === 'edit' && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitComanda,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: \"Salva Modifiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 906,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 898,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 701,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: (response, successMessage) => {\n        console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n        // Mostra messaggio di successo se fornito\n        if (successMessage) {\n          // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n          console.log('📢 Successo:', successMessage);\n        }\n\n        // Ricarica tutti i dati per aggiornare l'interfaccia\n        loadComande();\n        loadStatistiche();\n        loadResponsabili();\n        setOpenCreaConCavi(false);\n        console.log('✅ Interfaccia aggiornata');\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 922,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ResponsabiliListPopup, {\n      open: openResponsabiliPopup,\n      onClose: () => setOpenResponsabiliPopup(false),\n      responsabili: responsabili,\n      comandePerResponsabile: comandePerResponsabile,\n      onEditResponsabile: responsabile => {\n        setOpenResponsabiliPopup(false);\n        handleOpenResponsabileDialog('edit', responsabile);\n      },\n      onDeleteResponsabile: async idResponsabile => {\n        await handleDeleteResponsabile(idResponsabile);\n        setOpenResponsabiliPopup(false);\n      },\n      loading: loadingResponsabili,\n      error: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 946,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 422,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeListRivoluzionato, \"tUlyvVMsLWpyWDOJ/hsEr/Aq26w=\", false, function () {\n  return [useSearchParams];\n});\n_c = ComandeListRivoluzionato;\nexport default ComandeListRivoluzionato;\nvar _c;\n$RefreshReg$(_c, \"ComandeListRivoluzionato\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON><PERSON>", "List", "ListItem", "ListItemText", "Accordion", "AccordionSummary", "AccordionDetails", "<PERSON><PERSON>", "MenuItem", "Divider", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Assignment", "AssignIcon", "Person", "PersonIcon", "Email", "EmailIcon", "Phone", "PhoneIcon", "ExpandMore", "ExpandMoreIcon", "CheckCircle", "CheckCircleIcon", "Verified", "VerifiedIcon", "Visibility", "ViewIcon", "People", "PeopleIcon", "comandeService", "responsabiliService", "CreaComandaConCavi", "ResponsabiliListPopup", "ComandeListTable", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ComandeListRivoluzionato", "cantiereId", "cantiereName", "_s", "searchParams", "setSearchParams", "loading", "setLoading", "error", "setError", "searchingComanda", "setSearchingComanda", "statistiche", "setStatistiche", "allComande", "setAllComande", "loadingComande", "setLoadingComande", "openResponsabiliPopup", "setOpenResponsabiliPopup", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "openComandaDialog", "setOpenComandaDialog", "dialogModeComanda", "setDialogModeComanda", "selectedComanda", "setSelectedComanda", "formDataComanda", "setFormDataComanda", "tipo_comanda", "descrizione", "responsabile", "data_scadenza", "priorita", "note_capo_cantiere", "loadComande", "comandeData", "getComande", "err", "console", "loadStatistiche", "log", "stats", "getStatisticheComande", "_err$response", "response", "data", "message", "loadResponsabili", "getResponsabiliCantiere", "loadComandePerResponsabili", "_err$response2", "_err$response2$data", "errorMessage", "detail", "comandaParam", "get", "length", "Object", "keys", "comandaTrovata", "comandeResp", "id_responsabile", "find", "c", "codice_comanda", "handleOpenComandaDialog", "setTimeout", "prev", "newParams", "URLSearchParams", "delete", "warn", "for<PERSON>ach", "resp", "comande", "cmd", "responsabiliList", "comandeMap", "getComandeByResponsabile", "Array", "isArray", "handleOpenResponsabileDialog", "mode", "handleCloseResponsabileDialog", "handleSubmitResponsabile", "trim", "createResponsabile", "updateResponsabile", "handleDeleteResponsabile", "idResponsabile", "window", "confirm", "deleteResponsabile", "comanda", "handleCloseComandaDialog", "handleSubmitComanda", "updateComanda", "handleDeleteComanda", "codiceComanda", "deleteComanda", "getTipoComandaLabel", "tipo", "labels", "getStatoColor", "stato", "colors", "getPrioritaColor", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "variant", "fontWeight", "color", "severity", "bgcolor", "direction", "spacing", "flexWrap", "fontSize", "lineHeight", "responsabili_attivi", "totale_comande", "comande_in_corso", "comande_completate", "width", "height", "borderRadius", "Math", "round", "comande_create", "gap", "startIcon", "onClick", "textTransform", "px", "py", "backgroundColor", "border", "borderColor", "elevation", "textAlign", "gutterBottom", "onViewComanda", "bind", "onEditComanda", "onDeleteComanda", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "pb", "pt", "label", "value", "onChange", "e", "target", "margin", "required", "type", "helperText", "primary", "secondary", "size", "Date", "data_creazione", "toLocaleDateString", "numero_cavi_assegnati", "percentuale_completamento", "toFixed", "select", "multiline", "rows", "InputLabelProps", "shrink", "onSuccess", "successMessage", "onEditResponsabile", "onDeleteResponsabile", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeListRivoluzionato.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Alert,\n  CircularProgress,\n  Tooltip,\n  List,\n  ListItem,\n  ListItemText,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Stack,\n  MenuItem,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Assignment as AssignIcon,\n  Person as PersonIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  ExpandMore as ExpandMoreIcon,\n  CheckCircle as CheckCircleIcon,\n  Verified as VerifiedIcon,\n  Visibility as ViewIcon,\n  People as PeopleIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport ResponsabiliListPopup from './ResponsabiliListPopup';\nimport ComandeListTable from './ComandeListTable';\n\nconst ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [allComande, setAllComande] = useState([]);\n  const [loadingComande, setLoadingComande] = useState(false);\n\n  // Stati per popup responsabili\n  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [dialogModeComanda, setDialogModeComanda] = useState('view'); // 'view', 'edit'\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n\n  const loadComande = async () => {\n    try {\n      setLoadingComande(true);\n      const comandeData = await comandeService.getComande(cantiereId);\n      setAllComande(comandeData || []);\n    } catch (err) {\n      console.error('Errore nel caricamento comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoadingComande(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      console.log('📊 Statistiche ricevute:', stats);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento delle statistiche:', err);\n      console.error('❌ Dettagli errore:', err.response?.data || err.message);\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    if (cantiereId) {\n      loadResponsabili();\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n        handleOpenComandaDialog('view', comandaTrovata);\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    \n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    \n    setOpenResponsabileDialog(true);\n  };\n\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      \n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      \n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDeleteResponsabile = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (mode, comanda = null) => {\n    setDialogModeComanda(mode);\n    setSelectedComanda(comanda);\n\n    if (mode === 'edit' && comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        priorita: comanda.priorita || 'NORMALE',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n\n    setOpenComandaDialog(true);\n  };\n\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n  };\n\n  const handleSubmitComanda = async () => {\n    try {\n      if (dialogModeComanda === 'edit') {\n        await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n        handleCloseComandaDialog();\n        await loadResponsabili(); // Ricarica per aggiornare le comande\n        await loadComande();\n        await loadStatistiche();\n      }\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n\n  const handleDeleteComanda = async (codiceComanda) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing/Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  const getStatoColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n  const getPrioritaColor = (priorita) => {\n    const colors = {\n      'BASSA': 'default',\n      'NORMALE': 'primary',\n      'ALTA': 'warning',\n      'URGENTE': 'error'\n    };\n    return colors[priorita] || 'default';\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n          {cantiereName}\n        </Typography>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {searchingComanda && (\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          🔍 Ricerca comanda {searchingComanda} in corso...\n        </Alert>\n      )}\n\n      {/* Statistiche in stile Visualizza Cavi */}\n      {statistiche && (\n        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n          <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n            {/* Responsabili */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <PersonIcon color=\"primary\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.responsabili_attivi || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Responsabili\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Totale Comande */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <AssignIcon color=\"info\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.totale_comande || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Totale\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* In Corso */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <CheckCircleIcon color=\"warning\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_in_corso || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  In Corso\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Completate */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <VerifiedIcon color=\"success\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_completate || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Completate\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Percentuale completamento */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <Box sx={{\n                width: 32,\n                height: 32,\n                borderRadius: '50%',\n                bgcolor: (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.8 ? 'success.main' :\n                         (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.5 ? 'warning.main' : 'error.main',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}>\n                <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n                  {statistiche.totale_comande > 0 ? Math.round((statistiche.comande_completate / statistiche.totale_comande) * 100) : 0}%\n                </Typography>\n              </Box>\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n                  Completamento\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {statistiche.comande_create || 0} create\n                </Typography>\n              </Box>\n            </Stack>\n          </Stack>\n        </Paper>\n      )}\n\n      {/* Sezione Comande - Elemento Principale */}\n      <Box>\n        <Box>\n          {/* Toolbar Comande */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Gestione Responsabili e Comande\n            </Typography>\n            <Box display=\"flex\" gap={2}>\n              <Button\n                variant=\"outlined\"\n                startIcon={<PeopleIcon />}\n                onClick={() => setOpenResponsabiliPopup(true)}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#f5f7fa',\n                  color: '#2196f3',\n                  border: '1px solid #2196f3',\n                  '&:hover': {\n                    backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                    borderColor: '#1976d2'\n                  }\n                }}\n              >\n                Lista Responsabili\n              </Button>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => handleOpenResponsabileDialog('create')}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#2196f3',\n                  color: 'white',\n                  '&:hover': {\n                    backgroundColor: '#1976d2'\n                  }\n                }}\n              >\n                Crea Responsabile\n              </Button>\n            </Box>\n          </Box>\n\n          {/* Lista Comande in stile tabella */}\n          {loadingComande ? (\n            <Box display=\"flex\" justifyContent=\"center\" py={4}>\n              <CircularProgress />\n            </Box>\n          ) : allComande.length === 0 ? (\n            <Paper\n              elevation={0}\n              sx={{\n                p: 6,\n                textAlign: 'center',\n                backgroundColor: 'grey.50',\n                border: '1px dashed',\n                borderColor: 'grey.300'\n              }}\n            >\n              <AssignIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n              <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                Nessuna comanda disponibile\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                Crea la prima comanda per iniziare a gestire i lavori\n              </Typography>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => setOpenCreaConCavi(true)}\n                sx={{\n                  textTransform: 'none',\n                  backgroundColor: '#2196f3',\n                  color: 'white',\n                  '&:hover': {\n                    backgroundColor: '#1976d2'\n                  }\n                }}\n              >\n                Crea Prima Comanda\n              </Button>\n            </Paper>\n          ) : (\n            <ComandeListTable\n              comande={allComande}\n              onViewComanda={handleOpenComandaDialog.bind(null, 'view')}\n              onEditComanda={handleOpenComandaDialog.bind(null, 'edit')}\n              onDeleteComanda={handleDeleteComanda}\n              loading={loadingComande}\n            />\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per creazione/modifica responsabile */}\n      <Dialog\n        open={openResponsabileDialog}\n        onClose={handleCloseResponsabileDialog}\n        maxWidth=\"sm\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nome Responsabile\"\n              value={formDataResponsabile.nome_responsabile}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              variant=\"outlined\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={formDataResponsabile.email}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Email per notifiche (opzionale se inserisci telefono)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Telefono\"\n              value={formDataResponsabile.telefono}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Numero per SMS (opzionale se inserisci email)\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseResponsabileDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitResponsabile}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per visualizzazione/modifica comanda */}\n      <Dialog\n        open={openComandaDialog}\n        onClose={handleCloseComandaDialog}\n        maxWidth=\"md\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeComanda === 'view' ? 'Dettagli Comanda' : 'Modifica Comanda'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            {dialogModeComanda === 'view' && selectedComanda ? (\n              <List>\n                <ListItem>\n                  <ListItemText\n                    primary=\"Codice Comanda\"\n                    secondary={selectedComanda.codice_comanda}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Tipo\"\n                    secondary={getTipoComandaLabel(selectedComanda.tipo_comanda)}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Stato\"\n                    secondary={\n                      <Chip\n                        label={selectedComanda.stato}\n                        color={getStatoColor(selectedComanda.stato)}\n                        size=\"small\"\n                      />\n                    }\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Descrizione\"\n                    secondary={selectedComanda.descrizione || 'Nessuna descrizione'}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Priorità\"\n                    secondary={\n                      <Chip\n                        label={selectedComanda.priorita || 'NORMALE'}\n                        color={getPrioritaColor(selectedComanda.priorita || 'NORMALE')}\n                        size=\"small\"\n                      />\n                    }\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Responsabile\"\n                    secondary={selectedComanda.responsabile || 'Non assegnato'}\n                  />\n                </ListItem>\n                {selectedComanda.note_capo_cantiere && (\n                  <>\n                    <Divider />\n                    <ListItem>\n                      <ListItemText\n                        primary=\"Note Capo Cantiere\"\n                        secondary={selectedComanda.note_capo_cantiere}\n                      />\n                    </ListItem>\n                  </>\n                )}\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Data Creazione\"\n                    secondary={new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')}\n                  />\n                </ListItem>\n                {selectedComanda.data_scadenza && (\n                  <>\n                    <Divider />\n                    <ListItem>\n                      <ListItemText\n                        primary=\"Data Scadenza\"\n                        secondary={new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')}\n                      />\n                    </ListItem>\n                  </>\n                )}\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Cavi Assegnati\"\n                    secondary={selectedComanda.numero_cavi_assegnati || 0}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Completamento\"\n                    secondary={`${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`}\n                  />\n                </ListItem>\n              </List>\n            ) : (\n              <>\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Tipo Comanda\"\n                  value={formDataComanda.tipo_comanda}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, tipo_comanda: e.target.value })}\n                  margin=\"normal\"\n                  sx={{ mb: 2 }}\n                >\n                  <MenuItem value=\"POSA\">Posa</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n                  <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n                  <MenuItem value=\"TESTING\">Testing</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Priorità\"\n                  value={formDataComanda.priorita}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, priorita: e.target.value })}\n                  margin=\"normal\"\n                  sx={{ mb: 2 }}\n                >\n                  <MenuItem value=\"BASSA\">Bassa</MenuItem>\n                  <MenuItem value=\"NORMALE\">Normale</MenuItem>\n                  <MenuItem value=\"ALTA\">Alta</MenuItem>\n                  <MenuItem value=\"URGENTE\">Urgente</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  label=\"Descrizione\"\n                  value={formDataComanda.descrizione}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, descrizione: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={3}\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Responsabile\"\n                  value={formDataComanda.responsabile}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, responsabile: e.target.value })}\n                  margin=\"normal\"\n                  required\n                  helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Note Capo Cantiere\"\n                  value={formDataComanda.note_capo_cantiere}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, note_capo_cantiere: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={2}\n                  helperText=\"Istruzioni specifiche per il responsabile\"\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Data Scadenza\"\n                  type=\"date\"\n                  value={formDataComanda.data_scadenza}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, data_scadenza: e.target.value })}\n                  margin=\"normal\"\n                  InputLabelProps={{\n                    shrink: true,\n                  }}\n                />\n              </>\n            )}\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseComandaDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            {dialogModeComanda === 'view' ? 'Chiudi' : 'Annulla'}\n          </Button>\n          {dialogModeComanda === 'edit' && (\n            <Button\n              onClick={handleSubmitComanda}\n              variant=\"contained\"\n              sx={{\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3\n              }}\n            >\n              Salva Modifiche\n            </Button>\n          )}\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog CreaComandaConCavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={(response, successMessage) => {\n          console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n          // Mostra messaggio di successo se fornito\n          if (successMessage) {\n            // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n            console.log('📢 Successo:', successMessage);\n          }\n\n          // Ricarica tutti i dati per aggiornare l'interfaccia\n          loadComande();\n          loadStatistiche();\n          loadResponsabili();\n          setOpenCreaConCavi(false);\n\n          console.log('✅ Interfaccia aggiornata');\n        }}\n      />\n\n      {/* Popup Lista Responsabili */}\n      <ResponsabiliListPopup\n        open={openResponsabiliPopup}\n        onClose={() => setOpenResponsabiliPopup(false)}\n        responsabili={responsabili}\n        comandePerResponsabile={comandePerResponsabile}\n        onEditResponsabile={(responsabile) => {\n          setOpenResponsabiliPopup(false);\n          handleOpenResponsabileDialog('edit', responsabile);\n        }}\n        onDeleteResponsabile={async (idResponsabile) => {\n          await handleDeleteResponsabile(idResponsabile);\n          setOpenResponsabiliPopup(false);\n        }}\n        loading={loadingResponsabili}\n        error={error}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeListRivoluzionato;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,KAAK,EACLC,QAAQ,EACRC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,UAAU,EACxBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,EAC5BC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,QAAQ,EACtBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjE;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9D,eAAe,CAAC,CAAC;;EAEzD;EACA,MAAM,CAAC+D,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmE,KAAK,EAAEC,QAAQ,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACuE,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyE,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2E,cAAc,EAAEC,iBAAiB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC6E,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC+E,eAAe,EAAEC,kBAAkB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACiF,YAAY,EAAEC,eAAe,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACmF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACqF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGtF,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACuF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACyF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG1F,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAAC2F,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5F,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC6F,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9F,QAAQ,CAAC;IAC/D+F,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACoG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACpE,MAAM,CAACsG,eAAe,EAAEC,kBAAkB,CAAC,GAAGvG,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwG,eAAe,EAAEC,kBAAkB,CAAC,GAAGzG,QAAQ,CAAC;IACrD0G,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,SAAS;IACnBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFpC,iBAAiB,CAAC,IAAI,CAAC;MACvB,MAAMqC,WAAW,GAAG,MAAM/D,cAAc,CAACgE,UAAU,CAACtD,UAAU,CAAC;MAC/Dc,aAAa,CAACuC,WAAW,IAAI,EAAE,CAAC;IAClC,CAAC,CAAC,OAAOE,GAAG,EAAE;MACZC,OAAO,CAACjD,KAAK,CAAC,iCAAiC,EAAEgD,GAAG,CAAC;MACrD/C,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRQ,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMyC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFD,OAAO,CAACE,GAAG,CAAC,0CAA0C,EAAE1D,UAAU,CAAC;MACnE,MAAM2D,KAAK,GAAG,MAAMrE,cAAc,CAACsE,qBAAqB,CAAC5D,UAAU,CAAC;MACpEwD,OAAO,CAACE,GAAG,CAAC,0BAA0B,EAAEC,KAAK,CAAC;MAC9C/C,cAAc,CAAC+C,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOJ,GAAG,EAAE;MAAA,IAAAM,aAAA;MACZL,OAAO,CAACjD,KAAK,CAAC,6CAA6C,EAAEgD,GAAG,CAAC;MACjEC,OAAO,CAACjD,KAAK,CAAC,oBAAoB,EAAE,EAAAsD,aAAA,GAAAN,GAAG,CAACO,QAAQ,cAAAD,aAAA,uBAAZA,aAAA,CAAcE,IAAI,KAAIR,GAAG,CAACS,OAAO,CAAC;IACxE;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFzC,sBAAsB,CAAC,IAAI,CAAC;MAC5BhB,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMuD,IAAI,GAAG,MAAMxE,mBAAmB,CAAC2E,uBAAuB,CAAClE,UAAU,CAAC;MAC1EsB,eAAe,CAACyC,IAAI,IAAI,EAAE,CAAC;MAC3B,MAAMI,0BAA0B,CAACJ,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOR,GAAG,EAAE;MAAA,IAAAa,cAAA,EAAAC,mBAAA;MACZb,OAAO,CAACjD,KAAK,CAAC,0CAA0C,EAAEgD,GAAG,CAAC;MAC9D,MAAMe,YAAY,GAAG,EAAAF,cAAA,GAAAb,GAAG,CAACO,QAAQ,cAAAM,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcL,IAAI,cAAAM,mBAAA,uBAAlBA,mBAAA,CAAoBE,MAAM,KAAIhB,GAAG,CAACS,OAAO,IAAI,yCAAyC;MAC3GxD,QAAQ,CAAC,4CAA4C8D,YAAY,EAAE,CAAC;IACtE,CAAC,SAAS;MACR9C,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAID;EACAnF,SAAS,CAAC,MAAM;IACd,IAAI2D,UAAU,EAAE;MACdiE,gBAAgB,CAAC,CAAC;MAClBb,WAAW,CAAC,CAAC;MACbK,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACzD,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB;EACA3D,SAAS,CAAC,MAAM;IACd,MAAMmI,YAAY,GAAGrE,YAAY,CAACsE,GAAG,CAAC,SAAS,CAAC;IAChDjB,OAAO,CAACE,GAAG,CAAC,qCAAqC,EAAEc,YAAY,CAAC;IAChEhB,OAAO,CAACE,GAAG,CAAC,gBAAgB,EAAE;MAC5BrC,YAAY,EAAEA,YAAY,CAACqD,MAAM;MACjCjD,sBAAsB,EAAEkD,MAAM,CAACC,IAAI,CAACnD,sBAAsB,CAAC,CAACiD,MAAM;MAClErE,OAAO;MACPkB;IACF,CAAC,CAAC;;IAEF;IACA,IAAIiD,YAAY,IAAIA,YAAY,KAAK/D,gBAAgB,EAAE;MACrDC,mBAAmB,CAAC8D,YAAY,CAAC;IACnC;IAEA,IAAIA,YAAY,IAAInD,YAAY,CAACqD,MAAM,GAAG,CAAC,IAAIC,MAAM,CAACC,IAAI,CAACnD,sBAAsB,CAAC,CAACiD,MAAM,GAAG,CAAC,EAAE;MAC7FlB,OAAO,CAACE,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,IAAImB,cAAc,GAAG,IAAI;MAEzB,KAAK,MAAM7B,YAAY,IAAI3B,YAAY,EAAE;QACvC,MAAMyD,WAAW,GAAGrD,sBAAsB,CAACuB,YAAY,CAAC+B,eAAe,CAAC,IAAI,EAAE;QAC9EvB,OAAO,CAACE,GAAG,CAAC,mBAAmBV,YAAY,CAACb,iBAAiB,KAAK2C,WAAW,CAACJ,MAAM,UAAU,CAAC;QAC/FG,cAAc,GAAGC,WAAW,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAKV,YAAY,CAAC;QACzE,IAAIK,cAAc,EAAE;UAClBrB,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAEmB,cAAc,CAAC;UACjD;QACF;MACF;MAEA,IAAIA,cAAc,EAAE;QAClBrB,OAAO,CAACE,GAAG,CAAC,wCAAwC,EAAEc,YAAY,CAAC;QACnE9D,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3ByE,uBAAuB,CAAC,MAAM,EAAEN,cAAc,CAAC;QAC/C;QACAO,UAAU,CAAC,MAAM;UACfhF,eAAe,CAACiF,IAAI,IAAI;YACtB,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACF,IAAI,CAAC;YAC3CC,SAAS,CAACE,MAAM,CAAC,SAAS,CAAC;YAC3B,OAAOF,SAAS;UAClB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACL9B,OAAO,CAACiC,IAAI,CAAC,yBAAyB,EAAEjB,YAAY,CAAC;QACrDhB,OAAO,CAACE,GAAG,CAAC,yBAAyB,CAAC;QACtCrC,YAAY,CAACqE,OAAO,CAACC,IAAI,IAAI;UAC3B,MAAMC,OAAO,GAAGnE,sBAAsB,CAACkE,IAAI,CAACZ,eAAe,CAAC,IAAI,EAAE;UAClEa,OAAO,CAACF,OAAO,CAACG,GAAG,IAAI;YACrBrC,OAAO,CAACE,GAAG,CAAC,OAAOmC,GAAG,CAACX,cAAc,KAAKS,IAAI,CAACxD,iBAAiB,GAAG,CAAC;UACtE,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA;QACA,IAAI,CAAC9B,OAAO,IAAI,CAACkB,mBAAmB,EAAE;UACpCiC,OAAO,CAACE,GAAG,CAAC,uCAAuC,CAAC;UACpD0B,UAAU,CAAC,MAAM;YACfnB,gBAAgB,CAAC,CAAC;UACpB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF,CAAC,MAAM,IAAIO,YAAY,EAAE;MACvBhB,OAAO,CAACE,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,IAAI,CAACrD,OAAO,IAAI,CAACkB,mBAAmB,IAAIF,YAAY,CAACqD,MAAM,KAAK,CAAC,EAAE;QACjElB,OAAO,CAACE,GAAG,CAAC,0CAA0C,CAAC;QACvDO,gBAAgB,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE,CAAC9D,YAAY,EAAEkB,YAAY,EAAEI,sBAAsB,EAAEpB,OAAO,EAAEkB,mBAAmB,CAAC,CAAC,CAAC,CAAC;;EAExF,MAAM4C,0BAA0B,GAAG,MAAO2B,gBAAgB,IAAK;IAC7D,IAAI;MACF,MAAMC,UAAU,GAAG,CAAC,CAAC;MACrB,KAAK,MAAM/C,YAAY,IAAI8C,gBAAgB,EAAE;QAC3C,IAAI;UACF,MAAMhC,QAAQ,GAAG,MAAMxE,cAAc,CAAC0G,wBAAwB,CAAChG,UAAU,EAAEgD,YAAY,CAACb,iBAAiB,CAAC;UAC1G;UACA,IAAIyD,OAAO,GAAG,EAAE;UAChB,IAAI9B,QAAQ,IAAImC,KAAK,CAACC,OAAO,CAACpC,QAAQ,CAAC,EAAE;YACvC8B,OAAO,GAAG9B,QAAQ;UACpB,CAAC,MAAM,IAAIA,QAAQ,IAAIA,QAAQ,CAAC8B,OAAO,IAAIK,KAAK,CAACC,OAAO,CAACpC,QAAQ,CAAC8B,OAAO,CAAC,EAAE;YAC1EA,OAAO,GAAG9B,QAAQ,CAAC8B,OAAO;UAC5B,CAAC,MAAM,IAAI9B,QAAQ,IAAIA,QAAQ,CAACC,IAAI,IAAIkC,KAAK,CAACC,OAAO,CAACpC,QAAQ,CAACC,IAAI,CAAC,EAAE;YACpE6B,OAAO,GAAG9B,QAAQ,CAACC,IAAI;UACzB;UACAgC,UAAU,CAAC/C,YAAY,CAAC+B,eAAe,CAAC,GAAGa,OAAO;QACpD,CAAC,CAAC,OAAOrC,GAAG,EAAE;UACZC,OAAO,CAACjD,KAAK,CAAC,sCAAsCyC,YAAY,CAACb,iBAAiB,GAAG,EAAEoB,GAAG,CAAC;UAC3FwC,UAAU,CAAC/C,YAAY,CAAC+B,eAAe,CAAC,GAAG,EAAE;QAC/C;MACF;MACArD,yBAAyB,CAACqE,UAAU,CAAC;IACvC,CAAC,CAAC,OAAOxC,GAAG,EAAE;MACZC,OAAO,CAACjD,KAAK,CAAC,uCAAuC,EAAEgD,GAAG,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAM4C,4BAA4B,GAAGA,CAACC,IAAI,EAAEpD,YAAY,GAAG,IAAI,KAAK;IAClElB,yBAAyB,CAACsE,IAAI,CAAC;IAC/BpE,uBAAuB,CAACgB,YAAY,CAAC;IAErC,IAAIoD,IAAI,KAAK,MAAM,IAAIpD,YAAY,EAAE;MACnCd,uBAAuB,CAAC;QACtBC,iBAAiB,EAAEa,YAAY,CAACb,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAEY,YAAY,CAACZ,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAEW,YAAY,CAACX,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMyE,6BAA6B,GAAGA,CAAA,KAAM;IAC1CzE,yBAAyB,CAAC,KAAK,CAAC;IAChCI,uBAAuB,CAAC,IAAI,CAAC;IAC7BxB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAM8F,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF9F,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,CAACyB,oBAAoB,CAACE,iBAAiB,CAACoE,IAAI,CAAC,CAAC,EAAE;QAClD/F,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACyB,oBAAoB,CAACG,KAAK,IAAI,CAACH,oBAAoB,CAACI,QAAQ,EAAE;QACjE7B,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAIqB,sBAAsB,KAAK,QAAQ,EAAE;QACvC,MAAMtC,mBAAmB,CAACiH,kBAAkB,CAACxG,UAAU,EAAEiC,oBAAoB,CAAC;MAChF,CAAC,MAAM,IAAIJ,sBAAsB,KAAK,MAAM,EAAE;QAC5C,MAAMtC,mBAAmB,CAACkH,kBAAkB,CAAC1E,oBAAoB,CAACgD,eAAe,EAAE9C,oBAAoB,CAAC;MAC1G;MAEAoE,6BAA6B,CAAC,CAAC;MAC/B,MAAMpC,gBAAgB,CAAC,CAAC;MACxB,MAAMb,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAACjD,KAAK,CAAC,yBAAyB,EAAEgD,GAAG,CAAC;MAC7C/C,QAAQ,CAAC+C,GAAG,CAACgB,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAMmC,wBAAwB,GAAG,MAAOC,cAAc,IAAK;IACzD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAMtH,mBAAmB,CAACuH,kBAAkB,CAACH,cAAc,CAAC;MAC5D,MAAM1C,gBAAgB,CAAC,CAAC;MACxB,MAAMb,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAACjD,KAAK,CAAC,4BAA4B,EAAEgD,GAAG,CAAC;MAChD/C,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAM2E,uBAAuB,GAAGA,CAACiB,IAAI,EAAEW,OAAO,GAAG,IAAI,KAAK;IACxDtE,oBAAoB,CAAC2D,IAAI,CAAC;IAC1BzD,kBAAkB,CAACoE,OAAO,CAAC;IAE3B,IAAIX,IAAI,KAAK,MAAM,IAAIW,OAAO,EAAE;MAC9BlE,kBAAkB,CAAC;QACjBC,YAAY,EAAEiE,OAAO,CAACjE,YAAY;QAClCC,WAAW,EAAEgE,OAAO,CAAChE,WAAW,IAAI,EAAE;QACtCC,YAAY,EAAE+D,OAAO,CAAC/D,YAAY,IAAI,EAAE;QACxCC,aAAa,EAAE8D,OAAO,CAAC9D,aAAa,IAAI,EAAE;QAC1CC,QAAQ,EAAE6D,OAAO,CAAC7D,QAAQ,IAAI,SAAS;QACvCC,kBAAkB,EAAE4D,OAAO,CAAC5D,kBAAkB,IAAI;MACpD,CAAC,CAAC;IACJ;IAEAZ,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMyE,wBAAwB,GAAGA,CAAA,KAAM;IACrCzE,oBAAoB,CAAC,KAAK,CAAC;IAC3BI,kBAAkB,CAAC,IAAI,CAAC;IACxBE,kBAAkB,CAAC;MACjBC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,SAAS;MACnBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM8D,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,IAAIzE,iBAAiB,KAAK,MAAM,EAAE;QAChC,MAAMlD,cAAc,CAAC4H,aAAa,CAACxE,eAAe,CAACwC,cAAc,EAAEtC,eAAe,CAAC;QACnFoE,wBAAwB,CAAC,CAAC;QAC1B,MAAM/C,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAMb,WAAW,CAAC,CAAC;QACnB,MAAMK,eAAe,CAAC,CAAC;MACzB;IACF,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAACjD,KAAK,CAAC,yBAAyB,EAAEgD,GAAG,CAAC;MAC7C/C,QAAQ,CAAC,sCAAsC,CAAC;IAClD;EACF,CAAC;EAED,MAAM2G,mBAAmB,GAAG,MAAOC,aAAa,IAAK;IACnD,IAAI,CAACR,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACpE;IACF;IAEA,IAAI;MACF,MAAMvH,cAAc,CAAC+H,aAAa,CAACD,aAAa,CAAC;MACjD,MAAMnD,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC1B,MAAMb,WAAW,CAAC,CAAC;MACnB,MAAMK,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAACjD,KAAK,CAAC,4BAA4B,EAAEgD,GAAG,CAAC;MAChD/C,QAAQ,CAAC,yCAAyC,CAAC;IACrD;EACF,CAAC;EAED,MAAM8G,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE,gBAAgB;MAClC,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAME,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAED,MAAME,gBAAgB,GAAI1E,QAAQ,IAAK;IACrC,MAAMyE,MAAM,GAAG;MACb,OAAO,EAAE,SAAS;MAClB,SAAS,EAAE,SAAS;MACpB,MAAM,EAAE,SAAS;MACjB,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,MAAM,CAACzE,QAAQ,CAAC,IAAI,SAAS;EACtC,CAAC;EAED,IAAI7C,OAAO,EAAE;IACX,oBACET,OAAA,CAACrD,GAAG;MAACsL,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/ErI,OAAA,CAACzC,gBAAgB;QAAA+K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACEzI,OAAA,CAACrD,GAAG;IAAC+L,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAEhBrI,OAAA,CAACrD,GAAG;MAACiM,EAAE,EAAE,CAAE;MAAAP,QAAA,eACTrI,OAAA,CAACpD,UAAU;QAACiM,OAAO,EAAC,IAAI;QAACH,EAAE,EAAE;UAAEI,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAV,QAAA,EACrEhI;MAAY;QAAAiI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAEL9H,KAAK,iBACJX,OAAA,CAAC1C,KAAK;MAAC0L,QAAQ,EAAC,OAAO;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACnC1H;IAAK;MAAA2H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA5H,gBAAgB,iBACfb,OAAA,CAAC1C,KAAK;MAAC0L,QAAQ,EAAC,MAAM;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,GAAC,+BACjB,EAACxH,gBAAgB,EAAC,cACvC;IAAA;MAAAyH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAGA1H,WAAW,iBACVf,OAAA,CAAClD,KAAK;MAAC4L,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEK,OAAO,EAAE;MAAU,CAAE;MAAAZ,QAAA,eAC7CrI,OAAA,CAACjC,KAAK;QAACmL,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAAChB,UAAU,EAAC,QAAQ;QAACD,cAAc,EAAC,eAAe;QAACkB,QAAQ,EAAC,MAAM;QAAAf,QAAA,gBAEnGrI,OAAA,CAACjC,KAAK;UAACmL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDrI,OAAA,CAACrB,UAAU;YAACoK,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CzI,OAAA,CAACrD,GAAG;YAAA0L,QAAA,gBACFrI,OAAA,CAACpD,UAAU;cAACiM,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DtH,WAAW,CAACwI,mBAAmB,IAAI;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACbzI,OAAA,CAACpD,UAAU;cAACiM,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRzI,OAAA,CAACjC,KAAK;UAACmL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDrI,OAAA,CAACvB,UAAU;YAACsK,KAAK,EAAC,MAAM;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CzI,OAAA,CAACrD,GAAG;YAAA0L,QAAA,gBACFrI,OAAA,CAACpD,UAAU;cAACiM,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DtH,WAAW,CAACyI,cAAc,IAAI;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACbzI,OAAA,CAACpD,UAAU;cAACiM,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRzI,OAAA,CAACjC,KAAK;UAACmL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDrI,OAAA,CAACb,eAAe;YAAC4J,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDzI,OAAA,CAACrD,GAAG;YAAA0L,QAAA,gBACFrI,OAAA,CAACpD,UAAU;cAACiM,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DtH,WAAW,CAAC0I,gBAAgB,IAAI;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACbzI,OAAA,CAACpD,UAAU;cAACiM,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRzI,OAAA,CAACjC,KAAK;UAACmL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDrI,OAAA,CAACX,YAAY;YAAC0J,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDzI,OAAA,CAACrD,GAAG;YAAA0L,QAAA,gBACFrI,OAAA,CAACpD,UAAU;cAACiM,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9DtH,WAAW,CAAC2I,kBAAkB,IAAI;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACbzI,OAAA,CAACpD,UAAU;cAACiM,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRzI,OAAA,CAACjC,KAAK;UAACmL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDrI,OAAA,CAACrD,GAAG;YAAC+L,EAAE,EAAE;cACPiB,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,YAAY,EAAE,KAAK;cACnBZ,OAAO,EAAGlI,WAAW,CAAC2I,kBAAkB,IAAI3I,WAAW,CAACyI,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAC3FzI,WAAW,CAAC2I,kBAAkB,IAAI3I,WAAW,CAACyI,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAAG,YAAY;cACpHvB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,eACArI,OAAA,CAACpD,UAAU;cAACiM,OAAO,EAAC,SAAS;cAACC,UAAU,EAAC,MAAM;cAACC,KAAK,EAAC,OAAO;cAAAV,QAAA,GAC1DtH,WAAW,CAACyI,cAAc,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAAEhJ,WAAW,CAAC2I,kBAAkB,GAAG3I,WAAW,CAACyI,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GACxH;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNzI,OAAA,CAACrD,GAAG;YAAA0L,QAAA,gBACFrI,OAAA,CAACpD,UAAU;cAACiM,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,QAAQ;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzI,OAAA,CAACpD,UAAU;cAACiM,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,GACjDtH,WAAW,CAACiJ,cAAc,IAAI,CAAC,EAAC,SACnC;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAGDzI,OAAA,CAACrD,GAAG;MAAA0L,QAAA,eACFrI,OAAA,CAACrD,GAAG;QAAA0L,QAAA,gBAEFrI,OAAA,CAACrD,GAAG;UAACsL,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAACS,EAAE,EAAE,CAAE;UAAAP,QAAA,gBAC3ErI,OAAA,CAACpD,UAAU;YAACiM,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAe,CAAE;YAAAV,QAAA,EAAC;UAEzE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzI,OAAA,CAACrD,GAAG;YAACsL,OAAO,EAAC,MAAM;YAACgC,GAAG,EAAE,CAAE;YAAA5B,QAAA,gBACzBrI,OAAA,CAACnD,MAAM;cACLgM,OAAO,EAAC,UAAU;cAClBqB,SAAS,eAAElK,OAAA,CAACP,UAAU;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1B0B,OAAO,EAAEA,CAAA,KAAM7I,wBAAwB,CAAC,IAAI,CAAE;cAC9CoH,EAAE,EAAE;gBACF0B,aAAa,EAAE,MAAM;gBACrBtB,UAAU,EAAE,GAAG;gBACfuB,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLC,eAAe,EAAE,SAAS;gBAC1BxB,KAAK,EAAE,SAAS;gBAChByB,MAAM,EAAE,mBAAmB;gBAC3B,SAAS,EAAE;kBACTD,eAAe,EAAE,yBAAyB;kBAC1CE,WAAW,EAAE;gBACf;cACF,CAAE;cAAApC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTzI,OAAA,CAACnD,MAAM;cACLgM,OAAO,EAAC,WAAW;cACnBqB,SAAS,eAAElK,OAAA,CAAC7B,OAAO;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvB0B,OAAO,EAAEA,CAAA,KAAM5D,4BAA4B,CAAC,QAAQ,CAAE;cACtDmC,EAAE,EAAE;gBACF0B,aAAa,EAAE,MAAM;gBACrBtB,UAAU,EAAE,GAAG;gBACfuB,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLC,eAAe,EAAE,SAAS;gBAC1BxB,KAAK,EAAE,OAAO;gBACd,SAAS,EAAE;kBACTwB,eAAe,EAAE;gBACnB;cACF,CAAE;cAAAlC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLtH,cAAc,gBACbnB,OAAA,CAACrD,GAAG;UAACsL,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,QAAQ;UAACoC,EAAE,EAAE,CAAE;UAAAjC,QAAA,eAChDrI,OAAA,CAACzC,gBAAgB;YAAA+K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJxH,UAAU,CAAC6D,MAAM,KAAK,CAAC,gBACzB9E,OAAA,CAAClD,KAAK;UACJ4N,SAAS,EAAE,CAAE;UACbhC,EAAE,EAAE;YACFC,CAAC,EAAE,CAAC;YACJgC,SAAS,EAAE,QAAQ;YACnBJ,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,YAAY;YACpBC,WAAW,EAAE;UACf,CAAE;UAAApC,QAAA,gBAEFrI,OAAA,CAACvB,UAAU;YAACiK,EAAE,EAAE;cAAEW,QAAQ,EAAE,EAAE;cAAEN,KAAK,EAAE,UAAU;cAAEH,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DzI,OAAA,CAACpD,UAAU;YAACiM,OAAO,EAAC,IAAI;YAACE,KAAK,EAAC,gBAAgB;YAAC6B,YAAY;YAAAvC,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzI,OAAA,CAACpD,UAAU;YAACiM,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAACL,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzI,OAAA,CAACnD,MAAM;YACLgM,OAAO,EAAC,WAAW;YACnBqB,SAAS,eAAElK,OAAA,CAAC7B,OAAO;cAAAmK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB0B,OAAO,EAAEA,CAAA,KAAM3I,kBAAkB,CAAC,IAAI,CAAE;YACxCkH,EAAE,EAAE;cACF0B,aAAa,EAAE,MAAM;cACrBG,eAAe,EAAE,SAAS;cAC1BxB,KAAK,EAAE,OAAO;cACd,SAAS,EAAE;gBACTwB,eAAe,EAAE;cACnB;YACF,CAAE;YAAAlC,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAERzI,OAAA,CAACF,gBAAgB;UACfkG,OAAO,EAAE/E,UAAW;UACpB4J,aAAa,EAAEtF,uBAAuB,CAACuF,IAAI,CAAC,IAAI,EAAE,MAAM,CAAE;UAC1DC,aAAa,EAAExF,uBAAuB,CAACuF,IAAI,CAAC,IAAI,EAAE,MAAM,CAAE;UAC1DE,eAAe,EAAEzD,mBAAoB;UACrC9G,OAAO,EAAEU;QAAe;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzI,OAAA,CAAC/C,MAAM;MACLgO,IAAI,EAAElJ,sBAAuB;MAC7BmJ,OAAO,EAAEzE,6BAA8B;MACvC0E,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV3C,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEFrI,OAAA,CAAC9C,WAAW;QAACwL,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAjD,QAAA,eACzBrI,OAAA,CAACpD,UAAU;UAACiM,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9CpG,sBAAsB,KAAK,QAAQ,GAAG,wBAAwB,GAAG;QAAuB;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdzI,OAAA,CAAC7C,aAAa;QAAAkL,QAAA,eACZrI,OAAA,CAACrD,GAAG;UAAC+L,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACjBrI,OAAA,CAAC3C,SAAS;YACR+N,SAAS;YACTI,KAAK,EAAC,mBAAmB;YACzBC,KAAK,EAAEpJ,oBAAoB,CAACE,iBAAkB;YAC9CmJ,QAAQ,EAAGC,CAAC,IAAKrJ,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEE,iBAAiB,EAAEoJ,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzGI,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRjD,OAAO,EAAC,UAAU;YAClBH,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFzI,OAAA,CAAC3C,SAAS;YACR+N,SAAS;YACTI,KAAK,EAAC,OAAO;YACbO,IAAI,EAAC,OAAO;YACZN,KAAK,EAAEpJ,oBAAoB,CAACG,KAAM;YAClCkJ,QAAQ,EAAGC,CAAC,IAAKrJ,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEG,KAAK,EAAEmJ,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC7FI,MAAM,EAAC,QAAQ;YACfhD,OAAO,EAAC,UAAU;YAClBmD,UAAU,EAAC,uDAAuD;YAClEtD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFzI,OAAA,CAAC3C,SAAS;YACR+N,SAAS;YACTI,KAAK,EAAC,UAAU;YAChBC,KAAK,EAAEpJ,oBAAoB,CAACI,QAAS;YACrCiJ,QAAQ,EAAGC,CAAC,IAAKrJ,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEI,QAAQ,EAAEkJ,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAChGI,MAAM,EAAC,QAAQ;YACfhD,OAAO,EAAC,UAAU;YAClBmD,UAAU,EAAC;UAA+C;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBzI,OAAA,CAAC5C,aAAa;QAACsL,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAlD,QAAA,gBACjCrI,OAAA,CAACnD,MAAM;UACLsN,OAAO,EAAE1D,6BAA8B;UACvCiC,EAAE,EAAE;YAAE0B,aAAa,EAAE;UAAO,CAAE;UAAA/B,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzI,OAAA,CAACnD,MAAM;UACLsN,OAAO,EAAEzD,wBAAyB;UAClCmC,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACF0B,aAAa,EAAE,MAAM;YACrBtB,UAAU,EAAE,GAAG;YACfuB,EAAE,EAAE;UACN,CAAE;UAAAhC,QAAA,EAEDpG,sBAAsB,KAAK,QAAQ,GAAG,MAAM,GAAG;QAAO;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTzI,OAAA,CAAC/C,MAAM;MACLgO,IAAI,EAAEvI,iBAAkB;MACxBwI,OAAO,EAAE9D,wBAAyB;MAClC+D,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV3C,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEFrI,OAAA,CAAC9C,WAAW;QAACwL,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAjD,QAAA,eACzBrI,OAAA,CAACpD,UAAU;UAACiM,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9CzF,iBAAiB,KAAK,MAAM,GAAG,kBAAkB,GAAG;QAAkB;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdzI,OAAA,CAAC7C,aAAa;QAAAkL,QAAA,eACZrI,OAAA,CAACrD,GAAG;UAAC+L,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,EAChBzF,iBAAiB,KAAK,MAAM,IAAIE,eAAe,gBAC9C9C,OAAA,CAACvC,IAAI;YAAA4K,QAAA,gBACHrI,OAAA,CAACtC,QAAQ;cAAA2K,QAAA,eACPrI,OAAA,CAACrC,YAAY;gBACXsO,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAEpJ,eAAe,CAACwC;cAAe;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXzI,OAAA,CAAC/B,OAAO;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzI,OAAA,CAACtC,QAAQ;cAAA2K,QAAA,eACPrI,OAAA,CAACrC,YAAY;gBACXsO,OAAO,EAAC,MAAM;gBACdC,SAAS,EAAExE,mBAAmB,CAAC5E,eAAe,CAACI,YAAY;cAAE;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXzI,OAAA,CAAC/B,OAAO;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzI,OAAA,CAACtC,QAAQ;cAAA2K,QAAA,eACPrI,OAAA,CAACrC,YAAY;gBACXsO,OAAO,EAAC,OAAO;gBACfC,SAAS,eACPlM,OAAA,CAACjD,IAAI;kBACHyO,KAAK,EAAE1I,eAAe,CAACgF,KAAM;kBAC7BiB,KAAK,EAAElB,aAAa,CAAC/E,eAAe,CAACgF,KAAK,CAAE;kBAC5CqE,IAAI,EAAC;gBAAO;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXzI,OAAA,CAAC/B,OAAO;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzI,OAAA,CAACtC,QAAQ;cAAA2K,QAAA,eACPrI,OAAA,CAACrC,YAAY;gBACXsO,OAAO,EAAC,aAAa;gBACrBC,SAAS,EAAEpJ,eAAe,CAACK,WAAW,IAAI;cAAsB;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXzI,OAAA,CAAC/B,OAAO;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzI,OAAA,CAACtC,QAAQ;cAAA2K,QAAA,eACPrI,OAAA,CAACrC,YAAY;gBACXsO,OAAO,EAAC,aAAU;gBAClBC,SAAS,eACPlM,OAAA,CAACjD,IAAI;kBACHyO,KAAK,EAAE1I,eAAe,CAACQ,QAAQ,IAAI,SAAU;kBAC7CyF,KAAK,EAAEf,gBAAgB,CAAClF,eAAe,CAACQ,QAAQ,IAAI,SAAS,CAAE;kBAC/D6I,IAAI,EAAC;gBAAO;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXzI,OAAA,CAAC/B,OAAO;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzI,OAAA,CAACtC,QAAQ;cAAA2K,QAAA,eACPrI,OAAA,CAACrC,YAAY;gBACXsO,OAAO,EAAC,cAAc;gBACtBC,SAAS,EAAEpJ,eAAe,CAACM,YAAY,IAAI;cAAgB;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACV3F,eAAe,CAACS,kBAAkB,iBACjCvD,OAAA,CAAAE,SAAA;cAAAmI,QAAA,gBACErI,OAAA,CAAC/B,OAAO;gBAAAqK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXzI,OAAA,CAACtC,QAAQ;gBAAA2K,QAAA,eACPrI,OAAA,CAACrC,YAAY;kBACXsO,OAAO,EAAC,oBAAoB;kBAC5BC,SAAS,EAAEpJ,eAAe,CAACS;gBAAmB;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA,eACX,CACH,eACDzI,OAAA,CAAC/B,OAAO;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzI,OAAA,CAACtC,QAAQ;cAAA2K,QAAA,eACPrI,OAAA,CAACrC,YAAY;gBACXsO,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAE,IAAIE,IAAI,CAACtJ,eAAe,CAACuJ,cAAc,CAAC,CAACC,kBAAkB,CAAC,OAAO;cAAE;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACV3F,eAAe,CAACO,aAAa,iBAC5BrD,OAAA,CAAAE,SAAA;cAAAmI,QAAA,gBACErI,OAAA,CAAC/B,OAAO;gBAAAqK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXzI,OAAA,CAACtC,QAAQ;gBAAA2K,QAAA,eACPrI,OAAA,CAACrC,YAAY;kBACXsO,OAAO,EAAC,eAAe;kBACvBC,SAAS,EAAE,IAAIE,IAAI,CAACtJ,eAAe,CAACO,aAAa,CAAC,CAACiJ,kBAAkB,CAAC,OAAO;gBAAE;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA,eACX,CACH,eACDzI,OAAA,CAAC/B,OAAO;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzI,OAAA,CAACtC,QAAQ;cAAA2K,QAAA,eACPrI,OAAA,CAACrC,YAAY;gBACXsO,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAEpJ,eAAe,CAACyJ,qBAAqB,IAAI;cAAE;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXzI,OAAA,CAAC/B,OAAO;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzI,OAAA,CAACtC,QAAQ;cAAA2K,QAAA,eACPrI,OAAA,CAACrC,YAAY;gBACXsO,OAAO,EAAC,eAAe;gBACvBC,SAAS,EAAE,GAAG,CAACpJ,eAAe,CAAC0J,yBAAyB,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;cAAI;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,gBAEPzI,OAAA,CAAAE,SAAA;YAAAmI,QAAA,gBACErI,OAAA,CAAC3C,SAAS;cACR+N,SAAS;cACTsB,MAAM;cACNlB,KAAK,EAAC,cAAc;cACpBC,KAAK,EAAEzI,eAAe,CAACE,YAAa;cACpCwI,QAAQ,EAAGC,CAAC,IAAK1I,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEE,YAAY,EAAEyI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC1FI,MAAM,EAAC,QAAQ;cACfnD,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBAEdrI,OAAA,CAAChC,QAAQ;gBAACyN,KAAK,EAAC,MAAM;gBAAApD,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCzI,OAAA,CAAChC,QAAQ;gBAACyN,KAAK,EAAC,uBAAuB;gBAAApD,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxEzI,OAAA,CAAChC,QAAQ;gBAACyN,KAAK,EAAC,qBAAqB;gBAAApD,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpEzI,OAAA,CAAChC,QAAQ;gBAACyN,KAAK,EAAC,gBAAgB;gBAAApD,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1DzI,OAAA,CAAChC,QAAQ;gBAACyN,KAAK,EAAC,SAAS;gBAAApD,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEZzI,OAAA,CAAC3C,SAAS;cACR+N,SAAS;cACTsB,MAAM;cACNlB,KAAK,EAAC,aAAU;cAChBC,KAAK,EAAEzI,eAAe,CAACM,QAAS;cAChCoI,QAAQ,EAAGC,CAAC,IAAK1I,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEM,QAAQ,EAAEqI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACtFI,MAAM,EAAC,QAAQ;cACfnD,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBAEdrI,OAAA,CAAChC,QAAQ;gBAACyN,KAAK,EAAC,OAAO;gBAAApD,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxCzI,OAAA,CAAChC,QAAQ;gBAACyN,KAAK,EAAC,SAAS;gBAAApD,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5CzI,OAAA,CAAChC,QAAQ;gBAACyN,KAAK,EAAC,MAAM;gBAAApD,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCzI,OAAA,CAAChC,QAAQ;gBAACyN,KAAK,EAAC,SAAS;gBAAApD,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEZzI,OAAA,CAAC3C,SAAS;cACR+N,SAAS;cACTI,KAAK,EAAC,aAAa;cACnBC,KAAK,EAAEzI,eAAe,CAACG,WAAY;cACnCuI,QAAQ,EAAGC,CAAC,IAAK1I,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEG,WAAW,EAAEwI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACzFI,MAAM,EAAC,QAAQ;cACfc,SAAS;cACTC,IAAI,EAAE,CAAE;cACRlE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFzI,OAAA,CAAC3C,SAAS;cACR+N,SAAS;cACTI,KAAK,EAAC,cAAc;cACpBC,KAAK,EAAEzI,eAAe,CAACI,YAAa;cACpCsI,QAAQ,EAAGC,CAAC,IAAK1I,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEI,YAAY,EAAEuI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC1FI,MAAM,EAAC,QAAQ;cACfC,QAAQ;cACRE,UAAU,EAAC,0CAAuC;cAClDtD,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFzI,OAAA,CAAC3C,SAAS;cACR+N,SAAS;cACTI,KAAK,EAAC,oBAAoB;cAC1BC,KAAK,EAAEzI,eAAe,CAACO,kBAAmB;cAC1CmI,QAAQ,EAAGC,CAAC,IAAK1I,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEO,kBAAkB,EAAEoI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAChGI,MAAM,EAAC,QAAQ;cACfc,SAAS;cACTC,IAAI,EAAE,CAAE;cACRZ,UAAU,EAAC,2CAA2C;cACtDtD,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFzI,OAAA,CAAC3C,SAAS;cACR+N,SAAS;cACTI,KAAK,EAAC,eAAe;cACrBO,IAAI,EAAC,MAAM;cACXN,KAAK,EAAEzI,eAAe,CAACK,aAAc;cACrCqI,QAAQ,EAAGC,CAAC,IAAK1I,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEK,aAAa,EAAEsI,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC3FI,MAAM,EAAC,QAAQ;cACfgB,eAAe,EAAE;gBACfC,MAAM,EAAE;cACV;YAAE;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACF;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBzI,OAAA,CAAC5C,aAAa;QAACsL,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAlD,QAAA,gBACjCrI,OAAA,CAACnD,MAAM;UACLsN,OAAO,EAAE/C,wBAAyB;UAClCsB,EAAE,EAAE;YAAE0B,aAAa,EAAE;UAAO,CAAE;UAAA/B,QAAA,EAE7BzF,iBAAiB,KAAK,MAAM,GAAG,QAAQ,GAAG;QAAS;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,EACR7F,iBAAiB,KAAK,MAAM,iBAC3B5C,OAAA,CAACnD,MAAM;UACLsN,OAAO,EAAE9C,mBAAoB;UAC7BwB,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACF0B,aAAa,EAAE,MAAM;YACrBtB,UAAU,EAAE,GAAG;YACfuB,EAAE,EAAE;UACN,CAAE;UAAAhC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTzI,OAAA,CAACJ,kBAAkB;MACjBQ,UAAU,EAAEA,UAAW;MACvB6K,IAAI,EAAE1J,eAAgB;MACtB2J,OAAO,EAAEA,CAAA,KAAM1J,kBAAkB,CAAC,KAAK,CAAE;MACzCuL,SAAS,EAAEA,CAAC7I,QAAQ,EAAE8I,cAAc,KAAK;QACvCpJ,OAAO,CAACE,GAAG,CAAC,iDAAiD,CAAC;;QAE9D;QACA,IAAIkJ,cAAc,EAAE;UAClB;UACApJ,OAAO,CAACE,GAAG,CAAC,cAAc,EAAEkJ,cAAc,CAAC;QAC7C;;QAEA;QACAxJ,WAAW,CAAC,CAAC;QACbK,eAAe,CAAC,CAAC;QACjBQ,gBAAgB,CAAC,CAAC;QAClB7C,kBAAkB,CAAC,KAAK,CAAC;QAEzBoC,OAAO,CAACE,GAAG,CAAC,0BAA0B,CAAC;MACzC;IAAE;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFzI,OAAA,CAACH,qBAAqB;MACpBoL,IAAI,EAAE5J,qBAAsB;MAC5B6J,OAAO,EAAEA,CAAA,KAAM5J,wBAAwB,CAAC,KAAK,CAAE;MAC/CG,YAAY,EAAEA,YAAa;MAC3BI,sBAAsB,EAAEA,sBAAuB;MAC/CoL,kBAAkB,EAAG7J,YAAY,IAAK;QACpC9B,wBAAwB,CAAC,KAAK,CAAC;QAC/BiF,4BAA4B,CAAC,MAAM,EAAEnD,YAAY,CAAC;MACpD,CAAE;MACF8J,oBAAoB,EAAE,MAAOnG,cAAc,IAAK;QAC9C,MAAMD,wBAAwB,CAACC,cAAc,CAAC;QAC9CzF,wBAAwB,CAAC,KAAK,CAAC;MACjC,CAAE;MACFb,OAAO,EAAEkB,mBAAoB;MAC7BhB,KAAK,EAAEA;IAAM;MAAA2H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACnI,EAAA,CAp5BIH,wBAAwB;EAAA,QAEYzD,eAAe;AAAA;AAAAyQ,EAAA,GAFnDhN,wBAAwB;AAs5B9B,eAAeA,wBAAwB;AAAC,IAAAgN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}