{"ast": null, "code": "import { differenceInCalendarDays } from \"./differenceInCalendarDays.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name differenceInDays\n * @category Day Helpers\n * @summary Get the number of full days between the given dates.\n *\n * @description\n * Get the number of full day periods between two dates. Fractional days are\n * truncated towards zero.\n *\n * One \"full day\" is the distance between a local time in one day to the same\n * local time on the next or previous day. A full day can sometimes be less than\n * or more than 24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 24-hour periods, use this instead:\n * `Math.trunc(differenceInHours(dateLeft, dateRight)/24)|0`.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The later date\n * @param dateRight - The earlier date\n *\n * @returns The number of full days according to the local timezone\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 365\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 0\n *\n * @example\n * // How many full days are between\n * // 1 March 2020 0:00 and 1 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 92 days, even in\n * // time zones where DST starts and the\n * // period has only 92*24-1 hours.\n * const result = differenceInDays(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 1)\n * )\n * //=> 92\n */\nexport function differenceInDays(dateLeft, dateRight) {\n  const _dateLeft = toDate(dateLeft);\n  const _dateRight = toDate(dateRight);\n  const sign = compareLocalAsc(_dateLeft, _dateRight);\n  const difference = Math.abs(differenceInCalendarDays(_dateLeft, _dateRight));\n  _dateLeft.setDate(_dateLeft.getDate() - sign * difference);\n\n  // Math.abs(diff in full days - diff in calendar days) === 1 if last calendar day is not full\n  // If so, result must be decreased by 1 in absolute value\n  const isLastDayNotFull = Number(compareLocalAsc(_dateLeft, _dateRight) === -sign);\n  const result = sign * (difference - isLastDayNotFull);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Like `compareAsc` but uses local time not UTC, which is needed\n// for accurate equality comparisons of UTC timestamps that end up\n// having the same representation in local time, e.g. one hour before\n// DST ends vs. the instant that DST ends.\nfunction compareLocalAsc(dateLeft, dateRight) {\n  const diff = dateLeft.getFullYear() - dateRight.getFullYear() || dateLeft.getMonth() - dateRight.getMonth() || dateLeft.getDate() - dateRight.getDate() || dateLeft.getHours() - dateRight.getHours() || dateLeft.getMinutes() - dateRight.getMinutes() || dateLeft.getSeconds() - dateRight.getSeconds() || dateLeft.getMilliseconds() - dateRight.getMilliseconds();\n  if (diff < 0) {\n    return -1;\n  } else if (diff > 0) {\n    return 1;\n    // Return 0 if diff is 0; return NaN if diff is NaN\n  } else {\n    return diff;\n  }\n}\n\n// Fallback for modularized imports:\nexport default differenceInDays;", "map": {"version": 3, "names": ["differenceInCalendarDays", "toDate", "differenceInDays", "dateLeft", "dateRight", "_dateLeft", "_dateRight", "sign", "compareLocalAsc", "difference", "Math", "abs", "setDate", "getDate", "isLastDayNotFull", "Number", "result", "diff", "getFullYear", "getMonth", "getHours", "getMinutes", "getSeconds", "getMilliseconds"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/differenceInDays.mjs"], "sourcesContent": ["import { differenceInCalendarDays } from \"./differenceInCalendarDays.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name differenceInDays\n * @category Day Helpers\n * @summary Get the number of full days between the given dates.\n *\n * @description\n * Get the number of full day periods between two dates. Fractional days are\n * truncated towards zero.\n *\n * One \"full day\" is the distance between a local time in one day to the same\n * local time on the next or previous day. A full day can sometimes be less than\n * or more than 24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 24-hour periods, use this instead:\n * `Math.trunc(differenceInHours(dateLeft, dateRight)/24)|0`.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The later date\n * @param dateRight - The earlier date\n *\n * @returns The number of full days according to the local timezone\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 365\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 0\n *\n * @example\n * // How many full days are between\n * // 1 March 2020 0:00 and 1 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 92 days, even in\n * // time zones where DST starts and the\n * // period has only 92*24-1 hours.\n * const result = differenceInDays(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 1)\n * )\n * //=> 92\n */\nexport function differenceInDays(dateLeft, dateRight) {\n  const _dateLeft = toDate(dateLeft);\n  const _dateRight = toDate(dateRight);\n\n  const sign = compareLocalAsc(_dateLeft, _dateRight);\n  const difference = Math.abs(differenceInCalendarDays(_dateLeft, _dateRight));\n\n  _dateLeft.setDate(_dateLeft.getDate() - sign * difference);\n\n  // Math.abs(diff in full days - diff in calendar days) === 1 if last calendar day is not full\n  // If so, result must be decreased by 1 in absolute value\n  const isLastDayNotFull = Number(\n    compareLocalAsc(_dateLeft, _dateRight) === -sign,\n  );\n  const result = sign * (difference - isLastDayNotFull);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Like `compareAsc` but uses local time not UTC, which is needed\n// for accurate equality comparisons of UTC timestamps that end up\n// having the same representation in local time, e.g. one hour before\n// DST ends vs. the instant that DST ends.\nfunction compareLocalAsc(dateLeft, dateRight) {\n  const diff =\n    dateLeft.getFullYear() - dateRight.getFullYear() ||\n    dateLeft.getMonth() - dateRight.getMonth() ||\n    dateLeft.getDate() - dateRight.getDate() ||\n    dateLeft.getHours() - dateRight.getHours() ||\n    dateLeft.getMinutes() - dateRight.getMinutes() ||\n    dateLeft.getSeconds() - dateRight.getSeconds() ||\n    dateLeft.getMilliseconds() - dateRight.getMilliseconds();\n\n  if (diff < 0) {\n    return -1;\n  } else if (diff > 0) {\n    return 1;\n    // Return 0 if diff is 0; return NaN if diff is NaN\n  } else {\n    return diff;\n  }\n}\n\n// Fallback for modularized imports:\nexport default differenceInDays;\n"], "mappings": "AAAA,SAASA,wBAAwB,QAAQ,gCAAgC;AACzE,SAASC,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EACpD,MAAMC,SAAS,GAAGJ,MAAM,CAACE,QAAQ,CAAC;EAClC,MAAMG,UAAU,GAAGL,MAAM,CAACG,SAAS,CAAC;EAEpC,MAAMG,IAAI,GAAGC,eAAe,CAACH,SAAS,EAAEC,UAAU,CAAC;EACnD,MAAMG,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACX,wBAAwB,CAACK,SAAS,EAAEC,UAAU,CAAC,CAAC;EAE5ED,SAAS,CAACO,OAAO,CAACP,SAAS,CAACQ,OAAO,CAAC,CAAC,GAAGN,IAAI,GAAGE,UAAU,CAAC;;EAE1D;EACA;EACA,MAAMK,gBAAgB,GAAGC,MAAM,CAC7BP,eAAe,CAACH,SAAS,EAAEC,UAAU,CAAC,KAAK,CAACC,IAC9C,CAAC;EACD,MAAMS,MAAM,GAAGT,IAAI,IAAIE,UAAU,GAAGK,gBAAgB,CAAC;EACrD;EACA,OAAOE,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;AAClC;;AAEA;AACA;AACA;AACA;AACA,SAASR,eAAeA,CAACL,QAAQ,EAAEC,SAAS,EAAE;EAC5C,MAAMa,IAAI,GACRd,QAAQ,CAACe,WAAW,CAAC,CAAC,GAAGd,SAAS,CAACc,WAAW,CAAC,CAAC,IAChDf,QAAQ,CAACgB,QAAQ,CAAC,CAAC,GAAGf,SAAS,CAACe,QAAQ,CAAC,CAAC,IAC1ChB,QAAQ,CAACU,OAAO,CAAC,CAAC,GAAGT,SAAS,CAACS,OAAO,CAAC,CAAC,IACxCV,QAAQ,CAACiB,QAAQ,CAAC,CAAC,GAAGhB,SAAS,CAACgB,QAAQ,CAAC,CAAC,IAC1CjB,QAAQ,CAACkB,UAAU,CAAC,CAAC,GAAGjB,SAAS,CAACiB,UAAU,CAAC,CAAC,IAC9ClB,QAAQ,CAACmB,UAAU,CAAC,CAAC,GAAGlB,SAAS,CAACkB,UAAU,CAAC,CAAC,IAC9CnB,QAAQ,CAACoB,eAAe,CAAC,CAAC,GAAGnB,SAAS,CAACmB,eAAe,CAAC,CAAC;EAE1D,IAAIN,IAAI,GAAG,CAAC,EAAE;IACZ,OAAO,CAAC,CAAC;EACX,CAAC,MAAM,IAAIA,IAAI,GAAG,CAAC,EAAE;IACnB,OAAO,CAAC;IACR;EACF,CAAC,MAAM;IACL,OAAOA,IAAI;EACb;AACF;;AAEA;AACA,eAAef,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}