{"ast": null, "code": "import * as React from 'react';\n/**\n * Provides information about the current step in Stepper.\n */\nconst StepContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  StepContext.displayName = 'StepContext';\n}\n\n/**\n * Returns the current StepContext or an empty object if no StepContext\n * has been defined in the component tree.\n */\nexport function useStepContext() {\n  return React.useContext(StepContext);\n}\nexport default StepContext;", "map": {"version": 3, "names": ["React", "StepContext", "createContext", "process", "env", "NODE_ENV", "displayName", "useStepContext", "useContext"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/Step/StepContext.js"], "sourcesContent": ["import * as React from 'react';\n/**\n * Provides information about the current step in Stepper.\n */\nconst StepContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  StepContext.displayName = 'StepContext';\n}\n\n/**\n * Returns the current StepContext or an empty object if no StepContext\n * has been defined in the component tree.\n */\nexport function useStepContext() {\n  return React.useContext(StepContext);\n}\nexport default StepContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA,MAAMC,WAAW,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AACxD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,WAAW,CAACK,WAAW,GAAG,aAAa;AACzC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAAA,EAAG;EAC/B,OAAOP,KAAK,CAACQ,UAAU,CAACP,WAAW,CAAC;AACtC;AACA,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}