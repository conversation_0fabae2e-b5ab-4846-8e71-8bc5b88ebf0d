{"ast": null, "code": "import { formatDistance } from \"./ar-DZ/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./ar-DZ/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./ar-DZ/_lib/formatRelative.mjs\";\nimport { localize } from \"./ar-DZ/_lib/localize.mjs\";\nimport { match } from \"./ar-DZ/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Arabic locale (Algerian Arabic).\n * @language Algerian Arabic\n * @iso-639-2 ara\n * <AUTHOR> [@badre429](https://github.com/badre429)\n * <AUTHOR> [@elshahat](https://github.com/elshahat)\n */\nexport const arDZ = {\n  code: \"ar-DZ\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default arDZ;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "arDZ", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ar-DZ.mjs"], "sourcesContent": ["import { formatDistance } from \"./ar-DZ/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./ar-DZ/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./ar-DZ/_lib/formatRelative.mjs\";\nimport { localize } from \"./ar-DZ/_lib/localize.mjs\";\nimport { match } from \"./ar-DZ/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Arabic locale (Algerian Arabic).\n * @language Algerian Arabic\n * @iso-639-2 ara\n * <AUTHOR> [@badre429](https://github.com/badre429)\n * <AUTHOR> [@elshahat](https://github.com/elshahat)\n */\nexport const arDZ = {\n  code: \"ar-DZ\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default arDZ;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,iCAAiC;AAChE,SAASC,UAAU,QAAQ,6BAA6B;AACxD,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,KAAK,QAAQ,wBAAwB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}