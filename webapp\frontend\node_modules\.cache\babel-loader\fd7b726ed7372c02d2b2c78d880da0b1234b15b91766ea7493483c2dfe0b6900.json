{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['e.ə', 'b.e'],\n  abbreviated: ['e.ə', 'b.e'],\n  wide: ['eramızdan əvvəl', 'bizim era']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['K1', 'K2', 'K3', 'K4'],\n  wide: ['1ci kvartal', '2ci kvartal', '3cü kvartal', '4cü kvartal']\n};\nvar monthValues = {\n  narrow: ['Y', 'F', 'M', 'A', 'M', 'İ', 'İ', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['Yan', 'Fev', 'Mar', 'Apr', 'May', 'İyun', 'İyul', 'Avq', 'Sen', 'Okt', 'Noy', 'Dek'],\n  wide: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', 'May', '<PERSON><PERSON>', '<PERSON>yu<PERSON>', 'Avq<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ya<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>']\n};\nvar dayValues = {\n  narrow: ['B.', 'B.e', 'Ç.a', 'Ç.', 'C.a', 'C.', 'Ş.'],\n  short: ['B.', 'B.e', 'Ç.a', 'Ç.', 'C.a', 'C.', 'Ş.'],\n  abbreviated: ['Baz', 'Baz.e', 'Çər.a', 'Çər', 'Cüm.a', 'Cüm', 'Şə'],\n  wide: ['Bazar', 'Bazar ertəsi', 'Çərşənbə axşamı', 'Çərşənbə', 'Cümə axşamı', 'Cümə', 'Şənbə']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'gecəyarı',\n    noon: 'gün',\n    morning: 'səhər',\n    afternoon: 'gündüz',\n    evening: 'axşam',\n    night: 'gecə'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'gecəyarı',\n    noon: 'gün',\n    morning: 'səhər',\n    afternoon: 'gündüz',\n    evening: 'axşam',\n    night: 'gecə'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'gecəyarı',\n    noon: 'gün',\n    morning: 'səhər',\n    afternoon: 'gündüz',\n    evening: 'axşam',\n    night: 'gecə'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'gecəyarı',\n    noon: 'gün',\n    morning: 'səhər',\n    afternoon: 'gündüz',\n    evening: 'axşam',\n    night: 'gecə'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'gecəyarı',\n    noon: 'gün',\n    morning: 'səhər',\n    afternoon: 'gündüz',\n    evening: 'axşam',\n    night: 'gecə'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'gecəyarı',\n    noon: 'gün',\n    morning: 'səhər',\n    afternoon: 'gündüz',\n    evening: 'axşam',\n    night: 'gecə'\n  }\n};\nvar suffixes = {\n  1: '-inci',\n  5: '-inci',\n  8: '-inci',\n  70: '-inci',\n  80: '-inci',\n  2: '-nci',\n  7: '-nci',\n  20: '-nci',\n  50: '-nci',\n  3: '-üncü',\n  4: '-üncü',\n  100: '-üncü',\n  6: '-ncı',\n  9: '-uncu',\n  10: '-uncu',\n  30: '-uncu',\n  60: '-ıncı',\n  90: '-ıncı'\n};\nvar getSuffix = function getSuffix(number) {\n  if (number === 0) {\n    // special case for zero\n    return number + '-ıncı';\n  }\n  var a = number % 10;\n  var b = number % 100 - a;\n  var c = number >= 100 ? 100 : null;\n  if (suffixes[a]) {\n    return suffixes[a];\n  } else if (suffixes[b]) {\n    return suffixes[b];\n  } else if (c !== null) {\n    return suffixes[c];\n  }\n  return '';\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  var suffix = getSuffix(number);\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "suffixes", "getSuffix", "number", "a", "b", "c", "ordinalNumber", "dirtyNumber", "_options", "Number", "suffix", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/az/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['e.ə', 'b.e'],\n  abbreviated: ['e.ə', 'b.e'],\n  wide: ['eramızdan əvvəl', 'bizim era']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['K1', 'K2', 'K3', 'K4'],\n  wide: ['1ci kvartal', '2ci kvartal', '3cü kvartal', '4cü kvartal']\n};\nvar monthValues = {\n  narrow: ['Y', 'F', 'M', 'A', 'M', 'İ', 'İ', 'A', 'S', 'O', 'N', 'D'],\n  abbreviated: ['Yan', 'Fev', 'Mar', 'Apr', 'May', 'İyun', 'İyul', 'Avq', 'Sen', 'Okt', 'Noy', 'Dek'],\n  wide: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', 'May', '<PERSON><PERSON>', '<PERSON>yu<PERSON>', 'Avq<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ya<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>']\n};\nvar dayValues = {\n  narrow: ['B.', 'B.e', 'Ç.a', 'Ç.', 'C.a', 'C.', 'Ş.'],\n  short: ['B.', 'B.e', 'Ç.a', 'Ç.', 'C.a', 'C.', 'Ş.'],\n  abbreviated: ['Baz', 'Baz.e', 'Çər.a', 'Çər', 'Cüm.a', 'Cüm', 'Şə'],\n  wide: ['Bazar', 'Bazar ertəsi', 'Çərşənbə axşamı', 'Çərşənbə', 'Cümə axşamı', 'Cümə', 'Şənbə']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'am',\n    pm: 'pm',\n    midnight: 'gecəyarı',\n    noon: 'gün',\n    morning: 'səhər',\n    afternoon: 'gündüz',\n    evening: 'axşam',\n    night: 'gecə'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'gecəyarı',\n    noon: 'gün',\n    morning: 'səhər',\n    afternoon: 'gündüz',\n    evening: 'axşam',\n    night: 'gecə'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'gecəyarı',\n    noon: 'gün',\n    morning: 'səhər',\n    afternoon: 'gündüz',\n    evening: 'axşam',\n    night: 'gecə'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'a',\n    pm: 'p',\n    midnight: 'gecəyarı',\n    noon: 'gün',\n    morning: 'səhər',\n    afternoon: 'gündüz',\n    evening: 'axşam',\n    night: 'gecə'\n  },\n  abbreviated: {\n    am: 'AM',\n    pm: 'PM',\n    midnight: 'gecəyarı',\n    noon: 'gün',\n    morning: 'səhər',\n    afternoon: 'gündüz',\n    evening: 'axşam',\n    night: 'gecə'\n  },\n  wide: {\n    am: 'a.m.',\n    pm: 'p.m.',\n    midnight: 'gecəyarı',\n    noon: 'gün',\n    morning: 'səhər',\n    afternoon: 'gündüz',\n    evening: 'axşam',\n    night: 'gecə'\n  }\n};\nvar suffixes = {\n  1: '-inci',\n  5: '-inci',\n  8: '-inci',\n  70: '-inci',\n  80: '-inci',\n  2: '-nci',\n  7: '-nci',\n  20: '-nci',\n  50: '-nci',\n  3: '-üncü',\n  4: '-üncü',\n  100: '-üncü',\n  6: '-ncı',\n  9: '-uncu',\n  10: '-uncu',\n  30: '-uncu',\n  60: '-ıncı',\n  90: '-ıncı'\n};\nvar getSuffix = function getSuffix(number) {\n  if (number === 0) {\n    // special case for zero\n    return number + '-ıncı';\n  }\n  var a = number % 10;\n  var b = number % 100 - a;\n  var c = number >= 100 ? 100 : null;\n  if (suffixes[a]) {\n    return suffixes[a];\n  } else if (suffixes[b]) {\n    return suffixes[b];\n  } else if (c !== null) {\n    return suffixes[c];\n  }\n  return '';\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  var suffix = getSuffix(number);\n  return number + suffix;\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EACtBC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAC3BC,IAAI,EAAE,CAAC,iBAAiB,EAAE,WAAW;AACvC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACnGC,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ;AACxH,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;EACrDM,KAAK,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;EACpDL,WAAW,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC;EACnEC,IAAI,EAAE,CAAC,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO;AAC/F,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,QAAQ,GAAG;EACb,CAAC,EAAE,OAAO;EACV,CAAC,EAAE,OAAO;EACV,CAAC,EAAE,OAAO;EACV,EAAE,EAAE,OAAO;EACX,EAAE,EAAE,OAAO;EACX,CAAC,EAAE,MAAM;EACT,CAAC,EAAE,MAAM;EACT,EAAE,EAAE,MAAM;EACV,EAAE,EAAE,MAAM;EACV,CAAC,EAAE,OAAO;EACV,CAAC,EAAE,OAAO;EACV,GAAG,EAAE,OAAO;EACZ,CAAC,EAAE,MAAM;EACT,CAAC,EAAE,OAAO;EACV,EAAE,EAAE,OAAO;EACX,EAAE,EAAE,OAAO;EACX,EAAE,EAAE,OAAO;EACX,EAAE,EAAE;AACN,CAAC;AACD,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,MAAM,EAAE;EACzC,IAAIA,MAAM,KAAK,CAAC,EAAE;IAChB;IACA,OAAOA,MAAM,GAAG,OAAO;EACzB;EACA,IAAIC,CAAC,GAAGD,MAAM,GAAG,EAAE;EACnB,IAAIE,CAAC,GAAGF,MAAM,GAAG,GAAG,GAAGC,CAAC;EACxB,IAAIE,CAAC,GAAGH,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI;EAClC,IAAIF,QAAQ,CAACG,CAAC,CAAC,EAAE;IACf,OAAOH,QAAQ,CAACG,CAAC,CAAC;EACpB,CAAC,MAAM,IAAIH,QAAQ,CAACI,CAAC,CAAC,EAAE;IACtB,OAAOJ,QAAQ,CAACI,CAAC,CAAC;EACpB,CAAC,MAAM,IAAIC,CAAC,KAAK,IAAI,EAAE;IACrB,OAAOL,QAAQ,CAACK,CAAC,CAAC;EACpB;EACA,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,IAAIN,MAAM,GAAGO,MAAM,CAACF,WAAW,CAAC;EAChC,IAAIG,MAAM,GAAGT,SAAS,CAACC,MAAM,CAAC;EAC9B,OAAOA,MAAM,GAAGQ,MAAM;AACxB,CAAC;AACD,IAAIC,QAAQ,GAAG;EACbL,aAAa,EAAEA,aAAa;EAC5BM,GAAG,EAAE/B,eAAe,CAAC;IACnBgC,MAAM,EAAE/B,SAAS;IACjBgC,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAElC,eAAe,CAAC;IACvBgC,MAAM,EAAE3B,aAAa;IACrB4B,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAEpC,eAAe,CAAC;IACrBgC,MAAM,EAAE1B,WAAW;IACnB2B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAErC,eAAe,CAAC;IACnBgC,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAEtC,eAAe,CAAC;IACzBgC,MAAM,EAAEvB,eAAe;IACvBwB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAErB,yBAAyB;IAC3CsB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}