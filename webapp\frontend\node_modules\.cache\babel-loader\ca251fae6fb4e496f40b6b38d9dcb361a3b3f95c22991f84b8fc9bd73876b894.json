{"ast": null, "code": "import { formatDistance } from \"./uz/_lib/formatDistance.js\";\nimport { formatLong } from \"./uz/_lib/formatLong.js\";\nimport { formatRelative } from \"./uz/_lib/formatRelative.js\";\nimport { localize } from \"./uz/_lib/localize.js\";\nimport { match } from \"./uz/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Uzbek locale.\n * @language Uzbek\n * @iso-639-2 uzb\n * <AUTHOR> [@mukhammadali](https://github.com/Mukhammadali)\n */\nexport const uz = {\n  code: \"uz\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default uz;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "uz", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/uz.js"], "sourcesContent": ["import { formatDistance } from \"./uz/_lib/formatDistance.js\";\nimport { formatLong } from \"./uz/_lib/formatLong.js\";\nimport { formatRelative } from \"./uz/_lib/formatRelative.js\";\nimport { localize } from \"./uz/_lib/localize.js\";\nimport { match } from \"./uz/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Uzbek locale.\n * @language Uzbek\n * @iso-639-2 uzb\n * <AUTHOR> [@mukhammadali](https://github.com/Mukhammadali)\n */\nexport const uz = {\n  code: \"uz\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default uz;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}