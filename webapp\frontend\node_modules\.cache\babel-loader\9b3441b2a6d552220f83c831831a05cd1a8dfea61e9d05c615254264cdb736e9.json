{"ast": null, "code": "function futureSeconds(text) {\n  return text.replace(/sekuntia?/, 'sekunnin');\n}\nfunction futureMinutes(text) {\n  return text.replace(/minuuttia?/, 'minuutin');\n}\nfunction futureHours(text) {\n  return text.replace(/tuntia?/, 'tunnin');\n}\nfunction futureDays(text) {\n  return text.replace(/päivää?/, 'päivän');\n}\nfunction futureWeeks(text) {\n  return text.replace(/(viikko|viikkoa)/, 'viikon');\n}\nfunction futureMonths(text) {\n  return text.replace(/(kuukausi|kuukautta)/, 'kuukauden');\n}\nfunction futureYears(text) {\n  return text.replace(/(vuosi|vuotta)/, 'vuoden');\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'alle sekunti',\n    other: 'alle {{count}} sekuntia',\n    futureTense: futureSeconds\n  },\n  xSeconds: {\n    one: 'sekunti',\n    other: '{{count}} sekuntia',\n    futureTense: futureSeconds\n  },\n  halfAMinute: {\n    one: 'puoli minuuttia',\n    other: 'puoli minuuttia',\n    futureTense: function futureTense(_text) {\n      return 'puolen minuutin';\n    }\n  },\n  lessThanXMinutes: {\n    one: 'alle minuutti',\n    other: 'alle {{count}} minuuttia',\n    futureTense: futureMinutes\n  },\n  xMinutes: {\n    one: 'minuutti',\n    other: '{{count}} minuuttia',\n    futureTense: futureMinutes\n  },\n  aboutXHours: {\n    one: 'noin tunti',\n    other: 'noin {{count}} tuntia',\n    futureTense: futureHours\n  },\n  xHours: {\n    one: 'tunti',\n    other: '{{count}} tuntia',\n    futureTense: futureHours\n  },\n  xDays: {\n    one: 'päivä',\n    other: '{{count}} päivää',\n    futureTense: futureDays\n  },\n  aboutXWeeks: {\n    one: 'noin viikko',\n    other: 'noin {{count}} viikkoa',\n    futureTense: futureWeeks\n  },\n  xWeeks: {\n    one: 'viikko',\n    other: '{{count}} viikkoa',\n    futureTense: futureWeeks\n  },\n  aboutXMonths: {\n    one: 'noin kuukausi',\n    other: 'noin {{count}} kuukautta',\n    futureTense: futureMonths\n  },\n  xMonths: {\n    one: 'kuukausi',\n    other: '{{count}} kuukautta',\n    futureTense: futureMonths\n  },\n  aboutXYears: {\n    one: 'noin vuosi',\n    other: 'noin {{count}} vuotta',\n    futureTense: futureYears\n  },\n  xYears: {\n    one: 'vuosi',\n    other: '{{count}} vuotta',\n    futureTense: futureYears\n  },\n  overXYears: {\n    one: 'yli vuosi',\n    other: 'yli {{count}} vuotta',\n    futureTense: futureYears\n  },\n  almostXYears: {\n    one: 'lähes vuosi',\n    other: 'lähes {{count}} vuotta',\n    futureTense: futureYears\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var tokenValue = formatDistanceLocale[token];\n  var result = count === 1 ? tokenValue.one : tokenValue.other.replace('{{count}}', String(count));\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return tokenValue.futureTense(result) + ' kuluttua';\n    } else {\n      return result + ' sitten';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["futureSeconds", "text", "replace", "futureMinutes", "futureHours", "futureDays", "futureWeeks", "futureMonths", "future<PERSON><PERSON>s", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "futureTense", "xSeconds", "halfAMinute", "_text", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "tokenValue", "result", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/fi/_lib/formatDistance/index.js"], "sourcesContent": ["function futureSeconds(text) {\n  return text.replace(/sekuntia?/, 'sekunnin');\n}\nfunction futureMinutes(text) {\n  return text.replace(/minuuttia?/, 'minuutin');\n}\nfunction futureHours(text) {\n  return text.replace(/tuntia?/, 'tunnin');\n}\nfunction futureDays(text) {\n  return text.replace(/päivää?/, 'päivän');\n}\nfunction futureWeeks(text) {\n  return text.replace(/(viikko|viikkoa)/, 'viikon');\n}\nfunction futureMonths(text) {\n  return text.replace(/(kuukausi|kuukautta)/, 'kuukauden');\n}\nfunction futureYears(text) {\n  return text.replace(/(vuosi|vuotta)/, 'vuoden');\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'alle sekunti',\n    other: 'alle {{count}} sekuntia',\n    futureTense: futureSeconds\n  },\n  xSeconds: {\n    one: 'sekunti',\n    other: '{{count}} sekuntia',\n    futureTense: futureSeconds\n  },\n  halfAMinute: {\n    one: 'puoli minuuttia',\n    other: 'puoli minuuttia',\n    futureTense: function futureTense(_text) {\n      return 'puolen minuutin';\n    }\n  },\n  lessThanXMinutes: {\n    one: 'alle minuutti',\n    other: 'alle {{count}} minuuttia',\n    futureTense: futureMinutes\n  },\n  xMinutes: {\n    one: 'minuutti',\n    other: '{{count}} minuuttia',\n    futureTense: futureMinutes\n  },\n  aboutXHours: {\n    one: 'noin tunti',\n    other: 'noin {{count}} tuntia',\n    futureTense: futureHours\n  },\n  xHours: {\n    one: 'tunti',\n    other: '{{count}} tuntia',\n    futureTense: futureHours\n  },\n  xDays: {\n    one: 'päivä',\n    other: '{{count}} päivää',\n    futureTense: futureDays\n  },\n  aboutXWeeks: {\n    one: 'noin viikko',\n    other: 'noin {{count}} viikkoa',\n    futureTense: futureWeeks\n  },\n  xWeeks: {\n    one: 'viikko',\n    other: '{{count}} viikkoa',\n    futureTense: futureWeeks\n  },\n  aboutXMonths: {\n    one: 'noin kuukausi',\n    other: 'noin {{count}} kuukautta',\n    futureTense: futureMonths\n  },\n  xMonths: {\n    one: 'kuukausi',\n    other: '{{count}} kuukautta',\n    futureTense: futureMonths\n  },\n  aboutXYears: {\n    one: 'noin vuosi',\n    other: 'noin {{count}} vuotta',\n    futureTense: futureYears\n  },\n  xYears: {\n    one: 'vuosi',\n    other: '{{count}} vuotta',\n    futureTense: futureYears\n  },\n  overXYears: {\n    one: 'yli vuosi',\n    other: 'yli {{count}} vuotta',\n    futureTense: futureYears\n  },\n  almostXYears: {\n    one: 'lähes vuosi',\n    other: 'lähes {{count}} vuotta',\n    futureTense: futureYears\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var tokenValue = formatDistanceLocale[token];\n  var result = count === 1 ? tokenValue.one : tokenValue.other.replace('{{count}}', String(count));\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return tokenValue.futureTense(result) + ' kuluttua';\n    } else {\n      return result + ' sitten';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,SAASA,aAAaA,CAACC,IAAI,EAAE;EAC3B,OAAOA,IAAI,CAACC,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC;AAC9C;AACA,SAASC,aAAaA,CAACF,IAAI,EAAE;EAC3B,OAAOA,IAAI,CAACC,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC;AAC/C;AACA,SAASE,WAAWA,CAACH,IAAI,EAAE;EACzB,OAAOA,IAAI,CAACC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC;AAC1C;AACA,SAASG,UAAUA,CAACJ,IAAI,EAAE;EACxB,OAAOA,IAAI,CAACC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC;AAC1C;AACA,SAASI,WAAWA,CAACL,IAAI,EAAE;EACzB,OAAOA,IAAI,CAACC,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC;AACnD;AACA,SAASK,YAAYA,CAACN,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACC,OAAO,CAAC,sBAAsB,EAAE,WAAW,CAAC;AAC1D;AACA,SAASM,WAAWA,CAACP,IAAI,EAAE;EACzB,OAAOA,IAAI,CAACC,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC;AACjD;AACA,IAAIO,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAEb;EACf,CAAC;EACDc,QAAQ,EAAE;IACRH,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAEb;EACf,CAAC;EACDe,WAAW,EAAE;IACXJ,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,SAASA,WAAWA,CAACG,KAAK,EAAE;MACvC,OAAO,iBAAiB;IAC1B;EACF,CAAC;EACDC,gBAAgB,EAAE;IAChBN,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EAAEV;EACf,CAAC;EACDe,QAAQ,EAAE;IACRP,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAEV;EACf,CAAC;EACDgB,WAAW,EAAE;IACXR,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAET;EACf,CAAC;EACDgB,MAAM,EAAE;IACNT,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAET;EACf,CAAC;EACDiB,KAAK,EAAE;IACLV,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAER;EACf,CAAC;EACDiB,WAAW,EAAE;IACXX,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAEP;EACf,CAAC;EACDiB,MAAM,EAAE;IACNZ,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAEP;EACf,CAAC;EACDkB,YAAY,EAAE;IACZb,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EAAEN;EACf,CAAC;EACDkB,OAAO,EAAE;IACPd,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAEN;EACf,CAAC;EACDmB,WAAW,EAAE;IACXf,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAEL;EACf,CAAC;EACDmB,MAAM,EAAE;IACNhB,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAEL;EACf,CAAC;EACDoB,UAAU,EAAE;IACVjB,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAEL;EACf,CAAC;EACDqB,YAAY,EAAE;IACZlB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAEL;EACf;AACF,CAAC;AACD,IAAIsB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,UAAU,GAAGzB,oBAAoB,CAACsB,KAAK,CAAC;EAC5C,IAAII,MAAM,GAAGH,KAAK,KAAK,CAAC,GAAGE,UAAU,CAACvB,GAAG,GAAGuB,UAAU,CAACtB,KAAK,CAACV,OAAO,CAAC,WAAW,EAAEkC,MAAM,CAACJ,KAAK,CAAC,CAAC;EAChG,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACI,SAAS,EAAE;IAC/D,IAAIJ,OAAO,CAACK,UAAU,IAAIL,OAAO,CAACK,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOJ,UAAU,CAACrB,WAAW,CAACsB,MAAM,CAAC,GAAG,WAAW;IACrD,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,SAAS;IAC3B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}