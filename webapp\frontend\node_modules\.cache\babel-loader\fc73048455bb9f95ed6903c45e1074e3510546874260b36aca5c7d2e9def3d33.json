{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\TestBobinePage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Container, Typography, Box } from '@mui/material';\nimport TestBobineComponent from '../components/cavi/TestBobineComponent';\nimport { useParams } from 'react-router-dom';\n\n/**\n * Pagina di test per visualizzare tutte le bobine e i cavi disponibili\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestBobinePage = () => {\n  _s();\n  const {\n    cantiereId\n  } = useParams();\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        my: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        children: [\"Test Bobine e Cavi - Cantiere \", cantiereId]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TestBobineComponent, {\n        cantiereId: cantiereId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n};\n_s(TestBobinePage, \"4XKgwRYPqHWoFV3FBTJhQhu1m2w=\", false, function () {\n  return [useParams];\n});\n_c = TestBobinePage;\nexport default TestBobinePage;\nvar _c;\n$RefreshReg$(_c, \"TestBobinePage\");", "map": {"version": 3, "names": ["React", "Container", "Typography", "Box", "TestBobineComponent", "useParams", "jsxDEV", "_jsxDEV", "TestBobinePage", "_s", "cantiereId", "max<PERSON><PERSON><PERSON>", "children", "sx", "my", "variant", "component", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/TestBobinePage.js"], "sourcesContent": ["import React from 'react';\nimport { Container, Typography, Box } from '@mui/material';\nimport TestBobineComponent from '../components/cavi/TestBobineComponent';\nimport { useParams } from 'react-router-dom';\n\n/**\n * Pagina di test per visualizzare tutte le bobine e i cavi disponibili\n */\nconst TestBobinePage = () => {\n  const { cantiereId } = useParams();\n  \n  return (\n    <Container maxWidth=\"lg\">\n      <Box sx={{ my: 4 }}>\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n          Test Bobine e Cavi - Cantiere {cantiereId}\n        </Typography>\n        \n        <TestBobineComponent cantiereId={cantiereId} />\n      </Box>\n    </Container>\n  );\n};\n\nexport default TestBobinePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,UAAU,EAAEC,GAAG,QAAQ,eAAe;AAC1D,OAAOC,mBAAmB,MAAM,wCAAwC;AACxE,SAASC,SAAS,QAAQ,kBAAkB;;AAE5C;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA;AAGA,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC;EAAW,CAAC,GAAGL,SAAS,CAAC,CAAC;EAElC,oBACEE,OAAA,CAACN,SAAS;IAACU,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACtBL,OAAA,CAACJ,GAAG;MAACU,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBACjBL,OAAA,CAACL,UAAU;QAACa,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAAAL,QAAA,GAAC,gCACrB,EAACF,UAAU;MAAA;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eAEbd,OAAA,CAACH,mBAAmB;QAACM,UAAU,EAAEA;MAAW;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACZ,EAAA,CAdID,cAAc;EAAA,QACKH,SAAS;AAAA;AAAAiB,EAAA,GAD5Bd,cAAc;AAgBpB,eAAeA,cAAc;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}