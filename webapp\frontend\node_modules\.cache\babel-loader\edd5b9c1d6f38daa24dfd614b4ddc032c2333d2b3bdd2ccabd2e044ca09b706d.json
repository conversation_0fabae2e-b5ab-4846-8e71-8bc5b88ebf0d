{"ast": null, "code": "/**\n * Utility per la validazione dei campi delle bobine\n * Implementa le stesse regole di validazione della CLI\n */\n\n// Costanti\nexport const TBD = \"TBD\";\n\n/**\n * Verifica se un valore è vuoto\n * @param {string|number|null} value - Valore da verificare\n * @returns {boolean} - True se il valore è vuoto, false altrimenti\n */\nexport const isEmpty = value => {\n  return value === null || value === undefined || typeof value === 'string' && value.trim() === '' || typeof value === 'number' && isNaN(value);\n};\n\n/**\n * Converte un valore in float\n * @param {string|number|null} value - Valore da convertire\n * @returns {number} - Valore convertito in float\n */\nexport const convertToFloat = value => {\n  if (isEmpty(value)) return 0;\n  if (typeof value === 'number') return value;\n  if (typeof value === 'string') {\n    const normalized = value.trim().replace(',', '.');\n    try {\n      return parseFloat(normalized);\n    } catch (e) {\n      console.warn(`Errore conversione float: '${value}' non è un numero valido.`);\n      return 0;\n    }\n  }\n  console.warn(`Tipo di valore non supportato: ${typeof value}`);\n  return 0;\n};\n\n/**\n * Valida un campo numerico\n * @param {string|number} value - Valore da validare\n * @param {string} fieldName - Nome del campo\n * @returns {Object} - Risultato della validazione\n */\nexport const validateNumber = (value, fieldName) => {\n  try {\n    // Gestione campi vuoti\n    if (isEmpty(value)) {\n      if (fieldName === \"Numero conduttori\" || fieldName === \"Sezione\") {\n        return {\n          valid: false,\n          message: `${fieldName} è obbligatorio`,\n          value: null\n        };\n      }\n      return {\n        valid: false,\n        message: `${fieldName} non può essere vuoto`,\n        value: null\n      };\n    }\n\n    // Normalizzazione input se è stringa\n    let normalizedValue = value;\n    if (typeof value === 'string') {\n      normalizedValue = value.trim().replace(',', '.');\n      if (normalizedValue === '.') {\n        return {\n          valid: false,\n          message: `${fieldName} non valido`,\n          value: null\n        };\n      }\n    }\n\n    // Conversione\n    const numero = parseFloat(normalizedValue);\n\n    // Validazione numero negativo\n    if (numero < 0) {\n      return {\n        valid: false,\n        message: `${fieldName} non può essere negativo`,\n        value: null\n      };\n    }\n\n    // Validazione limiti specifici\n    if (fieldName === \"Numero conduttori\" && numero > 24) {\n      return {\n        valid: true,\n        message: `ATTENZIONE: Il numero di conduttori (${numero}) supera il limite standard di 24`,\n        value: numero.toString(),\n        warning: true\n      };\n    }\n    if (fieldName === \"Sezione\" && numero > 1000) {\n      return {\n        valid: true,\n        message: `ATTENZIONE: La sezione (${numero}) supera il limite standard di 1000`,\n        value: numero.toString(),\n        warning: true\n      };\n    }\n    return {\n      valid: true,\n      message: \"\",\n      value: numero.toString()\n    };\n  } catch (e) {\n    return {\n      valid: false,\n      message: `Il valore inserito per ${fieldName} non è un numero valido`,\n      value: null\n    };\n  }\n};\n\n/**\n * Valida i metri totali\n * @param {string|number} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateMetriTotali = value => {\n  try {\n    if (isEmpty(value)) {\n      return {\n        valid: false,\n        message: \"I metri totali sono obbligatori\",\n        value: null\n      };\n    }\n    const val = convertToFloat(value);\n    if (val <= 0) {\n      return {\n        valid: false,\n        message: \"I metri totali devono essere maggiori di zero\",\n        value: null\n      };\n    }\n    if (val > 100000) {\n      // 100km come limite ragionevole\n      return {\n        valid: false,\n        message: \"I metri totali non possono superare 100.000\",\n        value: null\n      };\n    }\n    return {\n      valid: true,\n      message: \"Valore valido\",\n      value: val\n    };\n  } catch (e) {\n    return {\n      valid: false,\n      message: \"Il valore deve essere un numero valido\",\n      value: null\n    };\n  }\n};\n\n/**\n * Valida un campo di testo base\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBaseField = value => {\n  if (isEmpty(value)) {\n    return {\n      valid: true,\n      message: \"Campo vuoto\",\n      value: TBD\n    };\n  }\n  return {\n    valid: true,\n    message: \"Campo valido\",\n    value: value.trim()\n  };\n};\n\n/**\n * Valida un campo di testo obbligatorio\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateRequiredTextField = value => {\n  if (isEmpty(value)) {\n    return {\n      valid: false,\n      message: \"Il campo non può essere vuoto\",\n      value: null\n    };\n  }\n  return {\n    valid: true,\n    message: \"Campo valido\",\n    value: value.trim()\n  };\n};\n\n/**\n * Valida un campo in base al suo tipo\n * @param {string} fieldName - Nome del campo\n * @param {string|number} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBobinaField = (fieldName, value) => {\n  // Campi che richiedono validazione speciale\n  const specialValidations = {\n    'metri_totali': () => validateMetriTotali(value),\n    'n_conduttori': () => validateNumber(value, \"Numero conduttori\"),\n    'sezione': () => validateNumber(value, \"Sezione\")\n  };\n\n  // Campi obbligatori\n  const requiredFields = ['utility', 'tipologia', 'n_conduttori', 'sezione', 'metri_totali'];\n\n  // Campi che possono avere \"TBD\" come valore predefinito quando vuoti\n  const tbdFields = ['ubicazione_bobina', 'fornitore', 'n_DDT'];\n\n  // Se il campo richiede validazione speciale, usala\n  if (fieldName in specialValidations) {\n    return specialValidations[fieldName]();\n  }\n\n  // Se il campo è obbligatorio\n  if (requiredFields.includes(fieldName)) {\n    return validateRequiredTextField(value);\n  }\n\n  // Se il campo può avere TBD come valore predefinito\n  if (tbdFields.includes(fieldName)) {\n    return validateBaseField(value);\n  }\n\n  // Per tutti gli altri campi, usa la validazione base\n  return validateBaseField(value);\n};\n\n/**\n * Valida l'ID della bobina\n * @param {string} value - ID della bobina\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBobinaId = value => {\n  // Gestione dei valori nulli o vuoti\n  if (isEmpty(value)) {\n    return {\n      valid: false,\n      message: \"L'ID della bobina è obbligatorio\",\n      value: null\n    };\n  }\n\n  // Converti sempre in stringa e rimuovi spazi iniziali e finali\n  const stringValue = String(value).trim();\n\n  // Verifica che l'ID non sia vuoto dopo il trim\n  if (stringValue === '') {\n    return {\n      valid: false,\n      message: \"L'ID della bobina è obbligatorio\",\n      value: null\n    };\n  }\n\n  // Verifica che l'ID non contenga caratteri speciali non consentiti\n  const invalidChars = /[\\s\\\\/:*?\"<>|]/;\n  if (invalidChars.test(stringValue)) {\n    return {\n      valid: false,\n      message: \"L'ID della bobina non può contenere spazi o caratteri speciali come \\\\ / : * ? \\\" < > |\",\n      value: null\n    };\n  }\n\n  // Verifica che l'ID non sia troppo lungo\n  if (stringValue.length > 50) {\n    return {\n      valid: false,\n      message: \"L'ID della bobina non può superare i 50 caratteri\",\n      value: null\n    };\n  }\n  return {\n    valid: true,\n    message: \"\",\n    value: stringValue\n  };\n};\n\n/**\n * Valida tutti i campi di una bobina\n * @param {Object} bobinaData - Dati della bobina\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBobinaData = bobinaData => {\n  const errors = {};\n  const warnings = {};\n  const validatedData = {\n    ...bobinaData\n  };\n\n  // Campi da validare\n  const fieldsToValidate = ['utility', 'tipologia', 'n_conduttori', 'sezione', 'metri_totali', 'ubicazione_bobina', 'fornitore', 'n_DDT'];\n\n  // Validazione speciale per numero_bobina (parte dell'ID_BOBINA)\n  if (bobinaData.configurazione === 'n') {\n    // In modalità manuale, verifica che il campo numero_bobina sia presente e non vuoto\n    if (!bobinaData.numero_bobina || bobinaData.numero_bobina.trim() === '') {\n      errors.numero_bobina = \"L'ID della bobina è obbligatorio\";\n    } else {\n      const idResult = validateBobinaId(bobinaData.numero_bobina);\n      if (!idResult.valid) {\n        errors.numero_bobina = idResult.message;\n      } else {\n        validatedData.numero_bobina = idResult.value;\n      }\n    }\n  } else if (bobinaData.configurazione === 's') {\n    // In modalità automatica, il numero_bobina dovrebbe essere già impostato\n    if (!bobinaData.numero_bobina || bobinaData.numero_bobina.trim() === '') {\n      // Se per qualche motivo non è impostato, imposta un valore di default\n      validatedData.numero_bobina = '1';\n    }\n  }\n\n  // Validazione degli altri campi\n  for (const field of fieldsToValidate) {\n    const result = validateBobinaField(field, bobinaData[field]);\n    if (!result.valid) {\n      errors[field] = result.message;\n    } else {\n      validatedData[field] = result.value;\n      if (result.warning) {\n        warnings[field] = result.message;\n      }\n    }\n  }\n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors,\n    warnings,\n    validatedData\n  };\n};", "map": {"version": 3, "names": ["TBD", "isEmpty", "value", "undefined", "trim", "isNaN", "convertToFloat", "normalized", "replace", "parseFloat", "e", "console", "warn", "validateNumber", "fieldName", "valid", "message", "normalizedValue", "numero", "toString", "warning", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "val", "validateBaseField", "validateRequiredTextField", "validateBob<PERSON>F<PERSON>", "specialValidations", "metri_totali", "n_conduttori", "sezione", "requiredFields", "tbdFields", "includes", "validateBobinaId", "stringValue", "String", "invalid<PERSON>hars", "test", "length", "validateBobinaData", "bobina<PERSON><PERSON>", "errors", "warnings", "validatedData", "fieldsToValidate", "configurazione", "numero_bobina", "idResult", "field", "result", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/utils/bobinaValidationUtils.js"], "sourcesContent": ["/**\n * Utility per la validazione dei campi delle bobine\n * Implementa le stesse regole di validazione della CLI\n */\n\n// Costanti\nexport const TBD = \"TBD\";\n\n/**\n * Verifica se un valore è vuoto\n * @param {string|number|null} value - Valore da verificare\n * @returns {boolean} - True se il valore è vuoto, false altrimenti\n */\nexport const isEmpty = (value) => {\n  return value === null || value === undefined ||\n         (typeof value === 'string' && value.trim() === '') ||\n         (typeof value === 'number' && isNaN(value));\n};\n\n/**\n * Converte un valore in float\n * @param {string|number|null} value - Valore da convertire\n * @returns {number} - Valore convertito in float\n */\nexport const convertToFloat = (value) => {\n  if (isEmpty(value)) return 0;\n\n  if (typeof value === 'number') return value;\n\n  if (typeof value === 'string') {\n    const normalized = value.trim().replace(',', '.');\n    try {\n      return parseFloat(normalized);\n    } catch (e) {\n      console.warn(`Errore conversione float: '${value}' non è un numero valido.`);\n      return 0;\n    }\n  }\n\n  console.warn(`Tipo di valore non supportato: ${typeof value}`);\n  return 0;\n};\n\n/**\n * Valida un campo numerico\n * @param {string|number} value - Valore da validare\n * @param {string} fieldName - Nome del campo\n * @returns {Object} - Risultato della validazione\n */\nexport const validateNumber = (value, fieldName) => {\n  try {\n    // Gestione campi vuoti\n    if (isEmpty(value)) {\n      if (fieldName === \"Numero conduttori\" || fieldName === \"Sezione\") {\n        return { valid: false, message: `${fieldName} è obbligatorio`, value: null };\n      }\n      return { valid: false, message: `${fieldName} non può essere vuoto`, value: null };\n    }\n\n    // Normalizzazione input se è stringa\n    let normalizedValue = value;\n    if (typeof value === 'string') {\n      normalizedValue = value.trim().replace(',', '.');\n      if (normalizedValue === '.') {\n        return { valid: false, message: `${fieldName} non valido`, value: null };\n      }\n    }\n\n    // Conversione\n    const numero = parseFloat(normalizedValue);\n\n    // Validazione numero negativo\n    if (numero < 0) {\n      return { valid: false, message: `${fieldName} non può essere negativo`, value: null };\n    }\n\n    // Validazione limiti specifici\n    if (fieldName === \"Numero conduttori\" && numero > 24) {\n      return {\n        valid: true,\n        message: `ATTENZIONE: Il numero di conduttori (${numero}) supera il limite standard di 24`,\n        value: numero.toString(),\n        warning: true\n      };\n    }\n\n    if (fieldName === \"Sezione\" && numero > 1000) {\n      return {\n        valid: true,\n        message: `ATTENZIONE: La sezione (${numero}) supera il limite standard di 1000`,\n        value: numero.toString(),\n        warning: true\n      };\n    }\n\n    return { valid: true, message: \"\", value: numero.toString() };\n  } catch (e) {\n    return { valid: false, message: `Il valore inserito per ${fieldName} non è un numero valido`, value: null };\n  }\n};\n\n/**\n * Valida i metri totali\n * @param {string|number} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateMetriTotali = (value) => {\n  try {\n    if (isEmpty(value)) {\n      return { valid: false, message: \"I metri totali sono obbligatori\", value: null };\n    }\n\n    const val = convertToFloat(value);\n\n    if (val <= 0) {\n      return { valid: false, message: \"I metri totali devono essere maggiori di zero\", value: null };\n    }\n\n    if (val > 100000) {  // 100km come limite ragionevole\n      return { valid: false, message: \"I metri totali non possono superare 100.000\", value: null };\n    }\n\n    return { valid: true, message: \"Valore valido\", value: val };\n  } catch (e) {\n    return { valid: false, message: \"Il valore deve essere un numero valido\", value: null };\n  }\n};\n\n/**\n * Valida un campo di testo base\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBaseField = (value) => {\n  if (isEmpty(value)) {\n    return { valid: true, message: \"Campo vuoto\", value: TBD };\n  }\n  return { valid: true, message: \"Campo valido\", value: value.trim() };\n};\n\n/**\n * Valida un campo di testo obbligatorio\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateRequiredTextField = (value) => {\n  if (isEmpty(value)) {\n    return { valid: false, message: \"Il campo non può essere vuoto\", value: null };\n  }\n  return { valid: true, message: \"Campo valido\", value: value.trim() };\n};\n\n/**\n * Valida un campo in base al suo tipo\n * @param {string} fieldName - Nome del campo\n * @param {string|number} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBobinaField = (fieldName, value) => {\n  // Campi che richiedono validazione speciale\n  const specialValidations = {\n    'metri_totali': () => validateMetriTotali(value),\n    'n_conduttori': () => validateNumber(value, \"Numero conduttori\"),\n    'sezione': () => validateNumber(value, \"Sezione\"),\n  };\n\n  // Campi obbligatori\n  const requiredFields = [\n    'utility', 'tipologia', 'n_conduttori', 'sezione', 'metri_totali'\n  ];\n\n  // Campi che possono avere \"TBD\" come valore predefinito quando vuoti\n  const tbdFields = [\n    'ubicazione_bobina', 'fornitore', 'n_DDT'\n  ];\n\n  // Se il campo richiede validazione speciale, usala\n  if (fieldName in specialValidations) {\n    return specialValidations[fieldName]();\n  }\n\n  // Se il campo è obbligatorio\n  if (requiredFields.includes(fieldName)) {\n    return validateRequiredTextField(value);\n  }\n\n  // Se il campo può avere TBD come valore predefinito\n  if (tbdFields.includes(fieldName)) {\n    return validateBaseField(value);\n  }\n\n  // Per tutti gli altri campi, usa la validazione base\n  return validateBaseField(value);\n};\n\n/**\n * Valida l'ID della bobina\n * @param {string} value - ID della bobina\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBobinaId = (value) => {\n  // Gestione dei valori nulli o vuoti\n  if (isEmpty(value)) {\n    return { valid: false, message: \"L'ID della bobina è obbligatorio\", value: null };\n  }\n\n  // Converti sempre in stringa e rimuovi spazi iniziali e finali\n  const stringValue = String(value).trim();\n\n  // Verifica che l'ID non sia vuoto dopo il trim\n  if (stringValue === '') {\n    return { valid: false, message: \"L'ID della bobina è obbligatorio\", value: null };\n  }\n\n  // Verifica che l'ID non contenga caratteri speciali non consentiti\n  const invalidChars = /[\\s\\\\/:*?\"<>|]/;\n  if (invalidChars.test(stringValue)) {\n    return {\n      valid: false,\n      message: \"L'ID della bobina non può contenere spazi o caratteri speciali come \\\\ / : * ? \\\" < > |\",\n      value: null\n    };\n  }\n\n  // Verifica che l'ID non sia troppo lungo\n  if (stringValue.length > 50) {\n    return {\n      valid: false,\n      message: \"L'ID della bobina non può superare i 50 caratteri\",\n      value: null\n    };\n  }\n\n  return { valid: true, message: \"\", value: stringValue };\n};\n\n/**\n * Valida tutti i campi di una bobina\n * @param {Object} bobinaData - Dati della bobina\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBobinaData = (bobinaData) => {\n  const errors = {};\n  const warnings = {};\n  const validatedData = { ...bobinaData };\n\n  // Campi da validare\n  const fieldsToValidate = [\n    'utility', 'tipologia', 'n_conduttori',\n    'sezione', 'metri_totali', 'ubicazione_bobina', 'fornitore', 'n_DDT'\n  ];\n\n  // Validazione speciale per numero_bobina (parte dell'ID_BOBINA)\n  if (bobinaData.configurazione === 'n') {\n    // In modalità manuale, verifica che il campo numero_bobina sia presente e non vuoto\n    if (!bobinaData.numero_bobina || bobinaData.numero_bobina.trim() === '') {\n      errors.numero_bobina = \"L'ID della bobina è obbligatorio\";\n    } else {\n      const idResult = validateBobinaId(bobinaData.numero_bobina);\n      if (!idResult.valid) {\n        errors.numero_bobina = idResult.message;\n      } else {\n        validatedData.numero_bobina = idResult.value;\n      }\n    }\n  } else if (bobinaData.configurazione === 's') {\n    // In modalità automatica, il numero_bobina dovrebbe essere già impostato\n    if (!bobinaData.numero_bobina || bobinaData.numero_bobina.trim() === '') {\n      // Se per qualche motivo non è impostato, imposta un valore di default\n      validatedData.numero_bobina = '1';\n    }\n  }\n\n  // Validazione degli altri campi\n  for (const field of fieldsToValidate) {\n    const result = validateBobinaField(field, bobinaData[field]);\n\n    if (!result.valid) {\n      errors[field] = result.message;\n    } else {\n      validatedData[field] = result.value;\n      if (result.warning) {\n        warnings[field] = result.message;\n      }\n    }\n  }\n\n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors,\n    warnings,\n    validatedData\n  };\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,GAAG,GAAG,KAAK;;AAExB;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,OAAO,GAAIC,KAAK,IAAK;EAChC,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IACpC,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,IAAI,CAAC,CAAC,KAAK,EAAG,IACjD,OAAOF,KAAK,KAAK,QAAQ,IAAIG,KAAK,CAACH,KAAK,CAAE;AACpD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,cAAc,GAAIJ,KAAK,IAAK;EACvC,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE,OAAO,CAAC;EAE5B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;EAE3C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAMK,UAAU,GAAGL,KAAK,CAACE,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IACjD,IAAI;MACF,OAAOC,UAAU,CAACF,UAAU,CAAC;IAC/B,CAAC,CAAC,OAAOG,CAAC,EAAE;MACVC,OAAO,CAACC,IAAI,CAAC,8BAA8BV,KAAK,2BAA2B,CAAC;MAC5E,OAAO,CAAC;IACV;EACF;EAEAS,OAAO,CAACC,IAAI,CAAC,kCAAkC,OAAOV,KAAK,EAAE,CAAC;EAC9D,OAAO,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,cAAc,GAAGA,CAACX,KAAK,EAAEY,SAAS,KAAK;EAClD,IAAI;IACF;IACA,IAAIb,OAAO,CAACC,KAAK,CAAC,EAAE;MAClB,IAAIY,SAAS,KAAK,mBAAmB,IAAIA,SAAS,KAAK,SAAS,EAAE;QAChE,OAAO;UAAEC,KAAK,EAAE,KAAK;UAAEC,OAAO,EAAE,GAAGF,SAAS,iBAAiB;UAAEZ,KAAK,EAAE;QAAK,CAAC;MAC9E;MACA,OAAO;QAAEa,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,GAAGF,SAAS,uBAAuB;QAAEZ,KAAK,EAAE;MAAK,CAAC;IACpF;;IAEA;IACA,IAAIe,eAAe,GAAGf,KAAK;IAC3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7Be,eAAe,GAAGf,KAAK,CAACE,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;MAChD,IAAIS,eAAe,KAAK,GAAG,EAAE;QAC3B,OAAO;UAAEF,KAAK,EAAE,KAAK;UAAEC,OAAO,EAAE,GAAGF,SAAS,aAAa;UAAEZ,KAAK,EAAE;QAAK,CAAC;MAC1E;IACF;;IAEA;IACA,MAAMgB,MAAM,GAAGT,UAAU,CAACQ,eAAe,CAAC;;IAE1C;IACA,IAAIC,MAAM,GAAG,CAAC,EAAE;MACd,OAAO;QAAEH,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,GAAGF,SAAS,0BAA0B;QAAEZ,KAAK,EAAE;MAAK,CAAC;IACvF;;IAEA;IACA,IAAIY,SAAS,KAAK,mBAAmB,IAAII,MAAM,GAAG,EAAE,EAAE;MACpD,OAAO;QACLH,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,wCAAwCE,MAAM,mCAAmC;QAC1FhB,KAAK,EAAEgB,MAAM,CAACC,QAAQ,CAAC,CAAC;QACxBC,OAAO,EAAE;MACX,CAAC;IACH;IAEA,IAAIN,SAAS,KAAK,SAAS,IAAII,MAAM,GAAG,IAAI,EAAE;MAC5C,OAAO;QACLH,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,2BAA2BE,MAAM,qCAAqC;QAC/EhB,KAAK,EAAEgB,MAAM,CAACC,QAAQ,CAAC,CAAC;QACxBC,OAAO,EAAE;MACX,CAAC;IACH;IAEA,OAAO;MAAEL,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEd,KAAK,EAAEgB,MAAM,CAACC,QAAQ,CAAC;IAAE,CAAC;EAC/D,CAAC,CAAC,OAAOT,CAAC,EAAE;IACV,OAAO;MAAEK,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,0BAA0BF,SAAS,yBAAyB;MAAEZ,KAAK,EAAE;IAAK,CAAC;EAC7G;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMmB,mBAAmB,GAAInB,KAAK,IAAK;EAC5C,IAAI;IACF,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE;MAClB,OAAO;QAAEa,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,iCAAiC;QAAEd,KAAK,EAAE;MAAK,CAAC;IAClF;IAEA,MAAMoB,GAAG,GAAGhB,cAAc,CAACJ,KAAK,CAAC;IAEjC,IAAIoB,GAAG,IAAI,CAAC,EAAE;MACZ,OAAO;QAAEP,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,+CAA+C;QAAEd,KAAK,EAAE;MAAK,CAAC;IAChG;IAEA,IAAIoB,GAAG,GAAG,MAAM,EAAE;MAAG;MACnB,OAAO;QAAEP,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,6CAA6C;QAAEd,KAAK,EAAE;MAAK,CAAC;IAC9F;IAEA,OAAO;MAAEa,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,eAAe;MAAEd,KAAK,EAAEoB;IAAI,CAAC;EAC9D,CAAC,CAAC,OAAOZ,CAAC,EAAE;IACV,OAAO;MAAEK,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,wCAAwC;MAAEd,KAAK,EAAE;IAAK,CAAC;EACzF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMqB,iBAAiB,GAAIrB,KAAK,IAAK;EAC1C,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE;IAClB,OAAO;MAAEa,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,aAAa;MAAEd,KAAK,EAAEF;IAAI,CAAC;EAC5D;EACA,OAAO;IAAEe,KAAK,EAAE,IAAI;IAAEC,OAAO,EAAE,cAAc;IAAEd,KAAK,EAAEA,KAAK,CAACE,IAAI,CAAC;EAAE,CAAC;AACtE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMoB,yBAAyB,GAAItB,KAAK,IAAK;EAClD,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE;IAClB,OAAO;MAAEa,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,+BAA+B;MAAEd,KAAK,EAAE;IAAK,CAAC;EAChF;EACA,OAAO;IAAEa,KAAK,EAAE,IAAI;IAAEC,OAAO,EAAE,cAAc;IAAEd,KAAK,EAAEA,KAAK,CAACE,IAAI,CAAC;EAAE,CAAC;AACtE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMqB,mBAAmB,GAAGA,CAACX,SAAS,EAAEZ,KAAK,KAAK;EACvD;EACA,MAAMwB,kBAAkB,GAAG;IACzB,cAAc,EAAEC,CAAA,KAAMN,mBAAmB,CAACnB,KAAK,CAAC;IAChD,cAAc,EAAE0B,CAAA,KAAMf,cAAc,CAACX,KAAK,EAAE,mBAAmB,CAAC;IAChE,SAAS,EAAE2B,CAAA,KAAMhB,cAAc,CAACX,KAAK,EAAE,SAAS;EAClD,CAAC;;EAED;EACA,MAAM4B,cAAc,GAAG,CACrB,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,cAAc,CAClE;;EAED;EACA,MAAMC,SAAS,GAAG,CAChB,mBAAmB,EAAE,WAAW,EAAE,OAAO,CAC1C;;EAED;EACA,IAAIjB,SAAS,IAAIY,kBAAkB,EAAE;IACnC,OAAOA,kBAAkB,CAACZ,SAAS,CAAC,CAAC,CAAC;EACxC;;EAEA;EACA,IAAIgB,cAAc,CAACE,QAAQ,CAAClB,SAAS,CAAC,EAAE;IACtC,OAAOU,yBAAyB,CAACtB,KAAK,CAAC;EACzC;;EAEA;EACA,IAAI6B,SAAS,CAACC,QAAQ,CAAClB,SAAS,CAAC,EAAE;IACjC,OAAOS,iBAAiB,CAACrB,KAAK,CAAC;EACjC;;EAEA;EACA,OAAOqB,iBAAiB,CAACrB,KAAK,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM+B,gBAAgB,GAAI/B,KAAK,IAAK;EACzC;EACA,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE;IAClB,OAAO;MAAEa,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,kCAAkC;MAAEd,KAAK,EAAE;IAAK,CAAC;EACnF;;EAEA;EACA,MAAMgC,WAAW,GAAGC,MAAM,CAACjC,KAAK,CAAC,CAACE,IAAI,CAAC,CAAC;;EAExC;EACA,IAAI8B,WAAW,KAAK,EAAE,EAAE;IACtB,OAAO;MAAEnB,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,kCAAkC;MAAEd,KAAK,EAAE;IAAK,CAAC;EACnF;;EAEA;EACA,MAAMkC,YAAY,GAAG,gBAAgB;EACrC,IAAIA,YAAY,CAACC,IAAI,CAACH,WAAW,CAAC,EAAE;IAClC,OAAO;MACLnB,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,yFAAyF;MAClGd,KAAK,EAAE;IACT,CAAC;EACH;;EAEA;EACA,IAAIgC,WAAW,CAACI,MAAM,GAAG,EAAE,EAAE;IAC3B,OAAO;MACLvB,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,mDAAmD;MAC5Dd,KAAK,EAAE;IACT,CAAC;EACH;EAEA,OAAO;IAAEa,KAAK,EAAE,IAAI;IAAEC,OAAO,EAAE,EAAE;IAAEd,KAAK,EAAEgC;EAAY,CAAC;AACzD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,kBAAkB,GAAIC,UAAU,IAAK;EAChD,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;EACnB,MAAMC,aAAa,GAAG;IAAE,GAAGH;EAAW,CAAC;;EAEvC;EACA,MAAMI,gBAAgB,GAAG,CACvB,SAAS,EAAE,WAAW,EAAE,cAAc,EACtC,SAAS,EAAE,cAAc,EAAE,mBAAmB,EAAE,WAAW,EAAE,OAAO,CACrE;;EAED;EACA,IAAIJ,UAAU,CAACK,cAAc,KAAK,GAAG,EAAE;IACrC;IACA,IAAI,CAACL,UAAU,CAACM,aAAa,IAAIN,UAAU,CAACM,aAAa,CAAC1C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACvEqC,MAAM,CAACK,aAAa,GAAG,kCAAkC;IAC3D,CAAC,MAAM;MACL,MAAMC,QAAQ,GAAGd,gBAAgB,CAACO,UAAU,CAACM,aAAa,CAAC;MAC3D,IAAI,CAACC,QAAQ,CAAChC,KAAK,EAAE;QACnB0B,MAAM,CAACK,aAAa,GAAGC,QAAQ,CAAC/B,OAAO;MACzC,CAAC,MAAM;QACL2B,aAAa,CAACG,aAAa,GAAGC,QAAQ,CAAC7C,KAAK;MAC9C;IACF;EACF,CAAC,MAAM,IAAIsC,UAAU,CAACK,cAAc,KAAK,GAAG,EAAE;IAC5C;IACA,IAAI,CAACL,UAAU,CAACM,aAAa,IAAIN,UAAU,CAACM,aAAa,CAAC1C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACvE;MACAuC,aAAa,CAACG,aAAa,GAAG,GAAG;IACnC;EACF;;EAEA;EACA,KAAK,MAAME,KAAK,IAAIJ,gBAAgB,EAAE;IACpC,MAAMK,MAAM,GAAGxB,mBAAmB,CAACuB,KAAK,EAAER,UAAU,CAACQ,KAAK,CAAC,CAAC;IAE5D,IAAI,CAACC,MAAM,CAAClC,KAAK,EAAE;MACjB0B,MAAM,CAACO,KAAK,CAAC,GAAGC,MAAM,CAACjC,OAAO;IAChC,CAAC,MAAM;MACL2B,aAAa,CAACK,KAAK,CAAC,GAAGC,MAAM,CAAC/C,KAAK;MACnC,IAAI+C,MAAM,CAAC7B,OAAO,EAAE;QAClBsB,QAAQ,CAACM,KAAK,CAAC,GAAGC,MAAM,CAACjC,OAAO;MAClC;IACF;EACF;EAEA,OAAO;IACLkC,OAAO,EAAEC,MAAM,CAACC,IAAI,CAACX,MAAM,CAAC,CAACH,MAAM,KAAK,CAAC;IACzCG,MAAM;IACNC,QAAQ;IACRC;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}