{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isSameYear\n * @category Year Helpers\n * @summary Are the given dates in the same year?\n *\n * @description\n * Are the given dates in the same year?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The first date to check\n * @param dateRight - The second date to check\n *\n * @returns The dates are in the same year\n *\n * @example\n * // Are 2 September 2014 and 25 September 2014 in the same year?\n * const result = isSameYear(new Date(2014, 8, 2), new Date(2014, 8, 25))\n * //=> true\n */\nexport function isSameYear(dateLeft, dateRight) {\n  const _dateLeft = toDate(dateLeft);\n  const _dateRight = toDate(dateRight);\n  return _dateLeft.getFullYear() === _dateRight.getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default isSameYear;", "map": {"version": 3, "names": ["toDate", "isSameYear", "dateLeft", "dateRight", "_dateLeft", "_dateRight", "getFullYear"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/isSameYear.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isSameYear\n * @category Year Helpers\n * @summary Are the given dates in the same year?\n *\n * @description\n * Are the given dates in the same year?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The first date to check\n * @param dateRight - The second date to check\n *\n * @returns The dates are in the same year\n *\n * @example\n * // Are 2 September 2014 and 25 September 2014 in the same year?\n * const result = isSameYear(new Date(2014, 8, 2), new Date(2014, 8, 25))\n * //=> true\n */\nexport function isSameYear(dateLeft, dateRight) {\n  const _dateLeft = toDate(dateLeft);\n  const _dateRight = toDate(dateRight);\n  return _dateLeft.getFullYear() === _dateRight.getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default isSameYear;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EAC9C,MAAMC,SAAS,GAAGJ,MAAM,CAACE,QAAQ,CAAC;EAClC,MAAMG,UAAU,GAAGL,MAAM,CAACG,SAAS,CAAC;EACpC,OAAOC,SAAS,CAACE,WAAW,CAAC,CAAC,KAAKD,UAAU,CAACC,WAAW,CAAC,CAAC;AAC7D;;AAEA;AACA,eAAeL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}