{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"circle\", {\n  cx: \"4\",\n  cy: \"14\",\n  r: \"2\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M1.22 17.58C.48 17.9 0 18.62 0 19.43V21h4.5v-1.61c0-.83.23-1.61.63-2.29-.37-.06-.74-.1-1.13-.1-.99 0-1.93.21-2.78.58\"\n}, \"1\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"20\",\n  cy: \"14\",\n  r: \"2\"\n}, \"2\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M22.78 17.58c-.85-.37-1.79-.58-2.78-.58-.39 0-.76.04-1.13.1.4.68.63 1.46.63 2.29V21H24v-1.57c0-.81-.48-1.53-1.22-1.85m-6.54-.93c-1.17-.52-2.61-.9-4.24-.9-1.63 0-3.07.39-4.24.9C6.68 17.13 6 18.21 6 19.39V21h12v-1.61c0-1.18-.68-2.26-1.76-2.74M8.07 19c.09-.23.27-.42.49-.52 1.1-.49 2.26-.73 3.43-.73 1.18 0 2.33.25 3.43.73.23.1.4.29.49.52zM9 12c0 1.66 1.34 3 3 3s3-1.34 3-3-1.34-3-3-3-3 1.34-3 3m4 0c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1\"\n}, \"3\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M2.48 10.86C2.17 10.1 2 9.36 2 8.6 2 6.02 4.02 4 6.6 4c2.68 0 3.82 1.74 5.4 3.59C13.57 5.76 14.7 4 17.4 4 19.98 4 22 6.02 22 8.6c0 .76-.17 1.5-.48 2.26.65.31 1.18.82 1.53 1.44.6-1.2.95-2.42.95-3.7C24 4.9 21.1 2 17.4 2c-2.09 0-4.09.97-5.4 2.51C10.69 2.97 8.69 2 6.6 2 2.9 2 0 4.9 0 8.6c0 1.28.35 2.5.96 3.7.35-.62.88-1.13 1.52-1.44\"\n}, \"4\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M17.4 4c-2.68 0-3.82 1.74-5.4 3.59C10.44 5.75 9.3 4 6.6 4 4.02 4 2 6.02 2 8.6c0 .76.17 1.5.48 2.26.46-.22.97-.36 1.52-.36 1.93 0 3.5 1.57 3.5 3.5 0 .45-.09.87-.24 1.26.97-.42 1.96-.69 2.98-.84C9.49 13.87 9 13 9 12c0-1.66 1.34-3 3-3s3 1.34 3 3c0 1-.49 1.87-1.24 2.42 1.02.15 2.02.43 2.98.85-.15-.39-.24-.82-.24-1.26 0-1.93 1.57-3.5 3.5-3.5.55 0 1.06.14 1.52.36.31-.77.48-1.51.48-2.27C22 6.02 19.98 4 17.4 4M8.07 19.02h7.85c-.09-.23-.27-.42-.49-.52-1.1-.49-2.26-.73-3.43-.73-1.18 0-2.33.25-3.43.73-.23.1-.4.29-.5.52\",\n  opacity: \".3\"\n}, \"5\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"1\",\n  opacity: \".3\"\n}, \"6\")], 'Diversity1TwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "cx", "cy", "r", "d", "opacity"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/icons-material/esm/Diversity1TwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"circle\", {\n  cx: \"4\",\n  cy: \"14\",\n  r: \"2\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M1.22 17.58C.48 17.9 0 18.62 0 19.43V21h4.5v-1.61c0-.83.23-1.61.63-2.29-.37-.06-.74-.1-1.13-.1-.99 0-1.93.21-2.78.58\"\n}, \"1\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"20\",\n  cy: \"14\",\n  r: \"2\"\n}, \"2\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M22.78 17.58c-.85-.37-1.79-.58-2.78-.58-.39 0-.76.04-1.13.1.4.68.63 1.46.63 2.29V21H24v-1.57c0-.81-.48-1.53-1.22-1.85m-6.54-.93c-1.17-.52-2.61-.9-4.24-.9-1.63 0-3.07.39-4.24.9C6.68 17.13 6 18.21 6 19.39V21h12v-1.61c0-1.18-.68-2.26-1.76-2.74M8.07 19c.09-.23.27-.42.49-.52 1.1-.49 2.26-.73 3.43-.73 1.18 0 2.33.25 3.43.73.23.1.4.29.49.52zM9 12c0 1.66 1.34 3 3 3s3-1.34 3-3-1.34-3-3-3-3 1.34-3 3m4 0c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1\"\n}, \"3\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M2.48 10.86C2.17 10.1 2 9.36 2 8.6 2 6.02 4.02 4 6.6 4c2.68 0 3.82 1.74 5.4 3.59C13.57 5.76 14.7 4 17.4 4 19.98 4 22 6.02 22 8.6c0 .76-.17 1.5-.48 2.26.65.31 1.18.82 1.53 1.44.6-1.2.95-2.42.95-3.7C24 4.9 21.1 2 17.4 2c-2.09 0-4.09.97-5.4 2.51C10.69 2.97 8.69 2 6.6 2 2.9 2 0 4.9 0 8.6c0 1.28.35 2.5.96 3.7.35-.62.88-1.13 1.52-1.44\"\n}, \"4\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M17.4 4c-2.68 0-3.82 1.74-5.4 3.59C10.44 5.75 9.3 4 6.6 4 4.02 4 2 6.02 2 8.6c0 .76.17 1.5.48 2.26.46-.22.97-.36 1.52-.36 1.93 0 3.5 1.57 3.5 3.5 0 .45-.09.87-.24 1.26.97-.42 1.96-.69 2.98-.84C9.49 13.87 9 13 9 12c0-1.66 1.34-3 3-3s3 1.34 3 3c0 1-.49 1.87-1.24 2.42 1.02.15 2.02.43 2.98.85-.15-.39-.24-.82-.24-1.26 0-1.93 1.57-3.5 3.5-3.5.55 0 1.06.14 1.52.36.31-.77.48-1.51.48-2.27C22 6.02 19.98 4 17.4 4M8.07 19.02h7.85c-.09-.23-.27-.42-.49-.52-1.1-.49-2.26-.73-3.43-.73-1.18 0-2.33.25-3.43.73-.23.1-.4.29-.5.52\",\n  opacity: \".3\"\n}, \"5\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"1\",\n  opacity: \".3\"\n}, \"6\")], 'Diversity1TwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,QAAQ,EAAE;EACxDC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaH,IAAI,CAAC,MAAM,EAAE;EACjCI,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaJ,IAAI,CAAC,QAAQ,EAAE;EACnCC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaH,IAAI,CAAC,MAAM,EAAE;EACjCI,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaJ,IAAI,CAAC,MAAM,EAAE;EACjCI,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaJ,IAAI,CAAC,MAAM,EAAE;EACjCI,CAAC,EAAE,mgBAAmgB;EACtgBC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaL,IAAI,CAAC,QAAQ,EAAE;EACnCC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE,GAAG;EACNE,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}