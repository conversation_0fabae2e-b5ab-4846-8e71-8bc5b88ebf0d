{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name min\n * @category Common Helpers\n * @summary Returns the earliest of the given dates.\n *\n * @description\n * Returns the earliest of the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dates - The dates to compare\n *\n * @returns The earliest of the dates\n *\n * @example\n * // Which of these dates is the earliest?\n * const result = min([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Wed Feb 11 1987 00:00:00\n */\nexport function min(dates) {\n  let result;\n  dates.forEach(dirtyDate => {\n    const date = toDate(dirtyDate);\n    if (!result || result > date || isNaN(+date)) {\n      result = date;\n    }\n  });\n  return result || new Date(NaN);\n}\n\n// Fallback for modularized imports:\nexport default min;", "map": {"version": 3, "names": ["toDate", "min", "dates", "result", "for<PERSON>ach", "dirtyDate", "date", "isNaN", "Date", "NaN"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/min.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name min\n * @category Common Helpers\n * @summary Returns the earliest of the given dates.\n *\n * @description\n * Returns the earliest of the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dates - The dates to compare\n *\n * @returns The earliest of the dates\n *\n * @example\n * // Which of these dates is the earliest?\n * const result = min([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Wed Feb 11 1987 00:00:00\n */\nexport function min(dates) {\n  let result;\n\n  dates.forEach((dirtyDate) => {\n    const date = toDate(dirtyDate);\n    if (!result || result > date || isNaN(+date)) {\n      result = date;\n    }\n  });\n\n  return result || new Date(NaN);\n}\n\n// Fallback for modularized imports:\nexport default min;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,GAAGA,CAACC,KAAK,EAAE;EACzB,IAAIC,MAAM;EAEVD,KAAK,CAACE,OAAO,CAAEC,SAAS,IAAK;IAC3B,MAAMC,IAAI,GAAGN,MAAM,CAACK,SAAS,CAAC;IAC9B,IAAI,CAACF,MAAM,IAAIA,MAAM,GAAGG,IAAI,IAAIC,KAAK,CAAC,CAACD,IAAI,CAAC,EAAE;MAC5CH,MAAM,GAAGG,IAAI;IACf;EACF,CAAC,CAAC;EAEF,OAAOH,MAAM,IAAI,IAAIK,IAAI,CAACC,GAAG,CAAC;AAChC;;AAEA;AACA,eAAeR,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}