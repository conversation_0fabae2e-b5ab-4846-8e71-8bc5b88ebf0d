{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useThemeProps } from '@mui/material/styles';\nimport { useDefaultDates, useUtils } from '../internals/hooks/useUtils';\nimport { applyDefaultViewProps } from '../internals/utils/views';\nimport { applyDefaultDate } from '../internals/utils/date-utils';\nimport { DatePickerToolbar } from './DatePickerToolbar';\nimport { uncapitalizeObjectKeys } from '../internals/utils/slots-migration';\nexport function useDatePickerDefaultizedProps(props, name) {\n  var _themeProps$slots, _themeProps$disableFu, _themeProps$disablePa, _themeProps$slotProps;\n  const utils = useUtils();\n  const defaultDates = useDefaultDates();\n  const themeProps = useThemeProps({\n    props,\n    name\n  });\n  const localeText = React.useMemo(() => {\n    var _themeProps$localeTex;\n    if (((_themeProps$localeTex = themeProps.localeText) == null ? void 0 : _themeProps$localeTex.toolbarTitle) == null) {\n      return themeProps.localeText;\n    }\n    return _extends({}, themeProps.localeText, {\n      datePickerToolbarTitle: themeProps.localeText.toolbarTitle\n    });\n  }, [themeProps.localeText]);\n  const slots = (_themeProps$slots = themeProps.slots) != null ? _themeProps$slots : uncapitalizeObjectKeys(themeProps.components);\n  return _extends({}, themeProps, {\n    localeText\n  }, applyDefaultViewProps({\n    views: themeProps.views,\n    openTo: themeProps.openTo,\n    defaultViews: ['year', 'day'],\n    defaultOpenTo: 'day'\n  }), {\n    disableFuture: (_themeProps$disableFu = themeProps.disableFuture) != null ? _themeProps$disableFu : false,\n    disablePast: (_themeProps$disablePa = themeProps.disablePast) != null ? _themeProps$disablePa : false,\n    minDate: applyDefaultDate(utils, themeProps.minDate, defaultDates.minDate),\n    maxDate: applyDefaultDate(utils, themeProps.maxDate, defaultDates.maxDate),\n    slots: _extends({\n      toolbar: DatePickerToolbar\n    }, slots),\n    slotProps: (_themeProps$slotProps = themeProps.slotProps) != null ? _themeProps$slotProps : themeProps.componentsProps\n  });\n}", "map": {"version": 3, "names": ["_extends", "React", "useThemeProps", "useDefaultDates", "useUtils", "applyDefaultViewProps", "applyDefaultDate", "DatePickerToolbar", "uncapitalizeObjectKeys", "useDatePickerDefaultizedProps", "props", "name", "_themeProps$slots", "_themeProps$disableFu", "_themeProps$disablePa", "_themeProps$slotProps", "utils", "defaultDates", "themeProps", "localeText", "useMemo", "_themeProps$localeTex", "toolbarTitle", "datePickerToolbarTitle", "slots", "components", "views", "openTo", "defaultViews", "defaultOpenTo", "disableFuture", "disablePast", "minDate", "maxDate", "toolbar", "slotProps", "componentsProps"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/DatePicker/shared.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useThemeProps } from '@mui/material/styles';\nimport { useDefaultDates, useUtils } from '../internals/hooks/useUtils';\nimport { applyDefaultViewProps } from '../internals/utils/views';\nimport { applyDefaultDate } from '../internals/utils/date-utils';\nimport { DatePickerToolbar } from './DatePickerToolbar';\nimport { uncapitalizeObjectKeys } from '../internals/utils/slots-migration';\nexport function useDatePickerDefaultizedProps(props, name) {\n  var _themeProps$slots, _themeProps$disableFu, _themeProps$disablePa, _themeProps$slotProps;\n  const utils = useUtils();\n  const defaultDates = useDefaultDates();\n  const themeProps = useThemeProps({\n    props,\n    name\n  });\n  const localeText = React.useMemo(() => {\n    var _themeProps$localeTex;\n    if (((_themeProps$localeTex = themeProps.localeText) == null ? void 0 : _themeProps$localeTex.toolbarTitle) == null) {\n      return themeProps.localeText;\n    }\n    return _extends({}, themeProps.localeText, {\n      datePickerToolbarTitle: themeProps.localeText.toolbarTitle\n    });\n  }, [themeProps.localeText]);\n  const slots = (_themeProps$slots = themeProps.slots) != null ? _themeProps$slots : uncapitalizeObjectKeys(themeProps.components);\n  return _extends({}, themeProps, {\n    localeText\n  }, applyDefaultViewProps({\n    views: themeProps.views,\n    openTo: themeProps.openTo,\n    defaultViews: ['year', 'day'],\n    defaultOpenTo: 'day'\n  }), {\n    disableFuture: (_themeProps$disableFu = themeProps.disableFuture) != null ? _themeProps$disableFu : false,\n    disablePast: (_themeProps$disablePa = themeProps.disablePast) != null ? _themeProps$disablePa : false,\n    minDate: applyDefaultDate(utils, themeProps.minDate, defaultDates.minDate),\n    maxDate: applyDefaultDate(utils, themeProps.maxDate, defaultDates.maxDate),\n    slots: _extends({\n      toolbar: DatePickerToolbar\n    }, slots),\n    slotProps: (_themeProps$slotProps = themeProps.slotProps) != null ? _themeProps$slotProps : themeProps.componentsProps\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,eAAe,EAAEC,QAAQ,QAAQ,6BAA6B;AACvE,SAASC,qBAAqB,QAAQ,0BAA0B;AAChE,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,OAAO,SAASC,6BAA6BA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACzD,IAAIC,iBAAiB,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,qBAAqB;EAC1F,MAAMC,KAAK,GAAGZ,QAAQ,CAAC,CAAC;EACxB,MAAMa,YAAY,GAAGd,eAAe,CAAC,CAAC;EACtC,MAAMe,UAAU,GAAGhB,aAAa,CAAC;IAC/BQ,KAAK;IACLC;EACF,CAAC,CAAC;EACF,MAAMQ,UAAU,GAAGlB,KAAK,CAACmB,OAAO,CAAC,MAAM;IACrC,IAAIC,qBAAqB;IACzB,IAAI,CAAC,CAACA,qBAAqB,GAAGH,UAAU,CAACC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,qBAAqB,CAACC,YAAY,KAAK,IAAI,EAAE;MACnH,OAAOJ,UAAU,CAACC,UAAU;IAC9B;IACA,OAAOnB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,UAAU,CAACC,UAAU,EAAE;MACzCI,sBAAsB,EAAEL,UAAU,CAACC,UAAU,CAACG;IAChD,CAAC,CAAC;EACJ,CAAC,EAAE,CAACJ,UAAU,CAACC,UAAU,CAAC,CAAC;EAC3B,MAAMK,KAAK,GAAG,CAACZ,iBAAiB,GAAGM,UAAU,CAACM,KAAK,KAAK,IAAI,GAAGZ,iBAAiB,GAAGJ,sBAAsB,CAACU,UAAU,CAACO,UAAU,CAAC;EAChI,OAAOzB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,UAAU,EAAE;IAC9BC;EACF,CAAC,EAAEd,qBAAqB,CAAC;IACvBqB,KAAK,EAAER,UAAU,CAACQ,KAAK;IACvBC,MAAM,EAAET,UAAU,CAACS,MAAM;IACzBC,YAAY,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;IAC7BC,aAAa,EAAE;EACjB,CAAC,CAAC,EAAE;IACFC,aAAa,EAAE,CAACjB,qBAAqB,GAAGK,UAAU,CAACY,aAAa,KAAK,IAAI,GAAGjB,qBAAqB,GAAG,KAAK;IACzGkB,WAAW,EAAE,CAACjB,qBAAqB,GAAGI,UAAU,CAACa,WAAW,KAAK,IAAI,GAAGjB,qBAAqB,GAAG,KAAK;IACrGkB,OAAO,EAAE1B,gBAAgB,CAACU,KAAK,EAAEE,UAAU,CAACc,OAAO,EAAEf,YAAY,CAACe,OAAO,CAAC;IAC1EC,OAAO,EAAE3B,gBAAgB,CAACU,KAAK,EAAEE,UAAU,CAACe,OAAO,EAAEhB,YAAY,CAACgB,OAAO,CAAC;IAC1ET,KAAK,EAAExB,QAAQ,CAAC;MACdkC,OAAO,EAAE3B;IACX,CAAC,EAAEiB,KAAK,CAAC;IACTW,SAAS,EAAE,CAACpB,qBAAqB,GAAGG,UAAU,CAACiB,SAAS,KAAK,IAAI,GAAGpB,qBAAqB,GAAGG,UAAU,CAACkB;EACzG,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}