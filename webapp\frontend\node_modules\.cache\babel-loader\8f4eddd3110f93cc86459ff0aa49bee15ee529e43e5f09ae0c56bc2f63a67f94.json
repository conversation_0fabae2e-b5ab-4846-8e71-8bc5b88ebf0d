{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'أقل من ثانية',\n    two: 'أقل من ثانيتين',\n    threeToTen: 'أقل من {{count}} ثواني',\n    other: 'أقل من {{count}} ثانية'\n  },\n  xSeconds: {\n    one: 'ثانية',\n    two: 'ثانيتين',\n    threeToTen: '{{count}} ثواني',\n    other: '{{count}} ثانية'\n  },\n  halfAMinute: 'نص دقيقة',\n  lessThanXMinutes: {\n    one: 'أقل من دقيقة',\n    two: 'أقل من دقيقتين',\n    threeToTen: 'أقل من {{count}} دقايق',\n    other: 'أقل من {{count}} دقيقة'\n  },\n  xMinutes: {\n    one: 'دقيقة',\n    two: 'دقيقتين',\n    threeToTen: '{{count}} دقايق',\n    other: '{{count}} دقيقة'\n  },\n  aboutXHours: {\n    one: 'حوالي ساعة',\n    two: 'حوالي ساعتين',\n    threeToTen: 'حوالي {{count}} ساعات',\n    other: 'حوالي {{count}} ساعة'\n  },\n  xHours: {\n    one: 'ساعة',\n    two: 'ساعتين',\n    threeToTen: '{{count}} ساعات',\n    other: '{{count}} ساعة'\n  },\n  xDays: {\n    one: 'يوم',\n    two: 'يومين',\n    threeToTen: '{{count}} أيام',\n    other: '{{count}} يوم'\n  },\n  aboutXWeeks: {\n    one: 'حوالي أسبوع',\n    two: 'حوالي أسبوعين',\n    threeToTen: 'حوالي {{count}} أسابيع',\n    other: 'حوالي {{count}} أسبوع'\n  },\n  xWeeks: {\n    one: 'أسبوع',\n    two: 'أسبوعين',\n    threeToTen: '{{count}} أسابيع',\n    other: '{{count}} أسبوع'\n  },\n  aboutXMonths: {\n    one: 'حوالي شهر',\n    two: 'حوالي شهرين',\n    threeToTen: 'حوالي {{count}} أشهر',\n    other: 'حوالي {{count}} شهر'\n  },\n  xMonths: {\n    one: 'شهر',\n    two: 'شهرين',\n    threeToTen: '{{count}} أشهر',\n    other: '{{count}} شهر'\n  },\n  aboutXYears: {\n    one: 'حوالي سنة',\n    two: 'حوالي سنتين',\n    threeToTen: 'حوالي {{count}} سنين',\n    other: 'حوالي {{count}} سنة'\n  },\n  xYears: {\n    one: 'عام',\n    two: 'عامين',\n    threeToTen: '{{count}} أعوام',\n    other: '{{count}} عام'\n  },\n  overXYears: {\n    one: 'أكثر من سنة',\n    two: 'أكثر من سنتين',\n    threeToTen: 'أكثر من {{count}} سنين',\n    other: 'أكثر من {{count}} سنة'\n  },\n  almostXYears: {\n    one: 'عام تقريبًا',\n    two: 'عامين تقريبًا',\n    threeToTen: '{{count}} أعوام تقريبًا',\n    other: '{{count}} عام تقريبًا'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2) {\n    result = tokenValue.two;\n  } else if (count <= 10) {\n    result = tokenValue.threeToTen.replace('{{count}}', String(count));\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"\\u0641\\u064A \\u062E\\u0644\\u0627\\u0644 \".concat(result);\n    } else {\n      return \"\\u0645\\u0646\\u0630 \".concat(result);\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "two", "threeToTen", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison", "concat"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/ar-EG/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'أقل من ثانية',\n    two: 'أقل من ثانيتين',\n    threeToTen: 'أقل من {{count}} ثواني',\n    other: 'أقل من {{count}} ثانية'\n  },\n  xSeconds: {\n    one: 'ثانية',\n    two: 'ثانيتين',\n    threeToTen: '{{count}} ثواني',\n    other: '{{count}} ثانية'\n  },\n  halfAMinute: 'نص دقيقة',\n  lessThanXMinutes: {\n    one: 'أقل من دقيقة',\n    two: 'أقل من دقيقتين',\n    threeToTen: 'أقل من {{count}} دقايق',\n    other: 'أقل من {{count}} دقيقة'\n  },\n  xMinutes: {\n    one: 'دقيقة',\n    two: 'دقيقتين',\n    threeToTen: '{{count}} دقايق',\n    other: '{{count}} دقيقة'\n  },\n  aboutXHours: {\n    one: 'حوالي ساعة',\n    two: 'حوالي ساعتين',\n    threeToTen: 'حوالي {{count}} ساعات',\n    other: 'حوالي {{count}} ساعة'\n  },\n  xHours: {\n    one: 'ساعة',\n    two: 'ساعتين',\n    threeToTen: '{{count}} ساعات',\n    other: '{{count}} ساعة'\n  },\n  xDays: {\n    one: 'يوم',\n    two: 'يومين',\n    threeToTen: '{{count}} أيام',\n    other: '{{count}} يوم'\n  },\n  aboutXWeeks: {\n    one: 'حوالي أسبوع',\n    two: 'حوالي أسبوعين',\n    threeToTen: 'حوالي {{count}} أسابيع',\n    other: 'حوالي {{count}} أسبوع'\n  },\n  xWeeks: {\n    one: 'أسبوع',\n    two: 'أسبوعين',\n    threeToTen: '{{count}} أسابيع',\n    other: '{{count}} أسبوع'\n  },\n  aboutXMonths: {\n    one: 'حوالي شهر',\n    two: 'حوالي شهرين',\n    threeToTen: 'حوالي {{count}} أشهر',\n    other: 'حوالي {{count}} شهر'\n  },\n  xMonths: {\n    one: 'شهر',\n    two: 'شهرين',\n    threeToTen: '{{count}} أشهر',\n    other: '{{count}} شهر'\n  },\n  aboutXYears: {\n    one: 'حوالي سنة',\n    two: 'حوالي سنتين',\n    threeToTen: 'حوالي {{count}} سنين',\n    other: 'حوالي {{count}} سنة'\n  },\n  xYears: {\n    one: 'عام',\n    two: 'عامين',\n    threeToTen: '{{count}} أعوام',\n    other: '{{count}} عام'\n  },\n  overXYears: {\n    one: 'أكثر من سنة',\n    two: 'أكثر من سنتين',\n    threeToTen: 'أكثر من {{count}} سنين',\n    other: 'أكثر من {{count}} سنة'\n  },\n  almostXYears: {\n    one: 'عام تقريبًا',\n    two: 'عامين تقريبًا',\n    threeToTen: '{{count}} أعوام تقريبًا',\n    other: '{{count}} عام تقريبًا'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2) {\n    result = tokenValue.two;\n  } else if (count <= 10) {\n    result = tokenValue.threeToTen.replace('{{count}}', String(count));\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"\\u0641\\u064A \\u062E\\u0644\\u0627\\u0644 \".concat(result);\n    } else {\n      return \"\\u0645\\u0646\\u0630 \".concat(result);\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,cAAc;IACnBC,GAAG,EAAE,gBAAgB;IACrBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRJ,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,SAAS;IACdC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,UAAU;EACvBC,gBAAgB,EAAE;IAChBN,GAAG,EAAE,cAAc;IACnBC,GAAG,EAAE,gBAAgB;IACrBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRP,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,SAAS;IACdC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXR,GAAG,EAAE,YAAY;IACjBC,GAAG,EAAE,cAAc;IACnBC,UAAU,EAAE,uBAAuB;IACnCC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNT,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,QAAQ;IACbC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLV,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,gBAAgB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXX,GAAG,EAAE,aAAa;IAClBC,GAAG,EAAE,eAAe;IACpBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNZ,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,SAAS;IACdC,UAAU,EAAE,kBAAkB;IAC9BC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZb,GAAG,EAAE,WAAW;IAChBC,GAAG,EAAE,aAAa;IAClBC,UAAU,EAAE,sBAAsB;IAClCC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPd,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,gBAAgB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXf,GAAG,EAAE,WAAW;IAChBC,GAAG,EAAE,aAAa;IAClBC,UAAU,EAAE,sBAAsB;IAClCC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNhB,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVjB,GAAG,EAAE,aAAa;IAClBC,GAAG,EAAE,eAAe;IACpBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZlB,GAAG,EAAE,aAAa;IAClBC,GAAG,EAAE,eAAe;IACpBC,UAAU,EAAE,yBAAyB;IACrCC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAG1B,oBAAoB,CAACsB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACxB,GAAG;EACzB,CAAC,MAAM,IAAIqB,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM,IAAIoB,KAAK,IAAI,EAAE,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,UAAU,CAACuB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EACpE,CAAC,MAAM;IACLE,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,wCAAwC,CAACC,MAAM,CAACN,MAAM,CAAC;IAChE,CAAC,MAAM;MACL,OAAO,qBAAqB,CAACM,MAAM,CAACN,MAAM,CAAC;IAC7C;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}