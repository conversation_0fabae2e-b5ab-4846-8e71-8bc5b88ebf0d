{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setYear\n * @category Year Helpers\n * @summary Set the year to the given date.\n *\n * @description\n * Set the year to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} year - the year of the new date\n * @returns {Date} the new date with the year set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set year 2013 to 1 September 2014:\n * const result = setYear(new Date(2014, 8, 1), 2013)\n * //=> Sun Sep 01 2013 00:00:00\n */\nexport default function setYear(dirtyDate, dirtyYear) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var year = toInteger(dirtyYear);\n\n  // Check if date is Invalid Date because Date.prototype.setFullYear ignores the value of Invalid Date\n  if (isNaN(date.getTime())) {\n    return new Date(NaN);\n  }\n  date.setFullYear(year);\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "requiredArgs", "setYear", "dirtyDate", "dirtyYear", "arguments", "date", "year", "isNaN", "getTime", "Date", "NaN", "setFullYear"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/setYear/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setYear\n * @category Year Helpers\n * @summary Set the year to the given date.\n *\n * @description\n * Set the year to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} year - the year of the new date\n * @returns {Date} the new date with the year set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set year 2013 to 1 September 2014:\n * const result = setYear(new Date(2014, 8, 1), 2013)\n * //=> Sun Sep 01 2013 00:00:00\n */\nexport default function setYear(dirtyDate, dirtyYear) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var year = toInteger(dirtyYear);\n\n  // Check if date is Invalid Date because Date.prototype.setFullYear ignores the value of Invalid Date\n  if (isNaN(date.getTime())) {\n    return new Date(NaN);\n  }\n  date.setFullYear(year);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,OAAOA,CAACC,SAAS,EAAEC,SAAS,EAAE;EACpDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGN,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAII,IAAI,GAAGR,SAAS,CAACK,SAAS,CAAC;;EAE/B;EACA,IAAII,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;IACzB,OAAO,IAAIC,IAAI,CAACC,GAAG,CAAC;EACtB;EACAL,IAAI,CAACM,WAAW,CAACL,IAAI,CAAC;EACtB,OAAOD,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}