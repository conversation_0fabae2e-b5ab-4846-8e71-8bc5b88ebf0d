{"ast": null, "code": "import { formatDistance } from \"./zh-CN/_lib/formatDistance.js\";\nimport { formatLong } from \"./zh-CN/_lib/formatLong.js\";\nimport { formatRelative } from \"./zh-CN/_lib/formatRelative.js\";\nimport { localize } from \"./zh-CN/_lib/localize.js\";\nimport { match } from \"./zh-CN/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Chinese Simplified locale.\n * @language Chinese Simplified\n * @iso-639-2 zho\n * <AUTHOR> [@KingMario](https://github.com/KingMario)\n * <AUTHOR> [@fnlctrl](https://github.com/fnlctrl)\n * <AUTHOR> [@sabrinamiao](https://github.com/sabrinamiao)\n * <AUTHOR> [@cubicwork](https://github.com/cubicwork)\n * <AUTHOR> [@skyuplam](https://github.com/skyuplam)\n */\nexport const zhCN = {\n  code: \"zh-CN\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default zhCN;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "zhCN", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/zh-CN.js"], "sourcesContent": ["import { formatDistance } from \"./zh-CN/_lib/formatDistance.js\";\nimport { formatLong } from \"./zh-CN/_lib/formatLong.js\";\nimport { formatRelative } from \"./zh-CN/_lib/formatRelative.js\";\nimport { localize } from \"./zh-CN/_lib/localize.js\";\nimport { match } from \"./zh-CN/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Chinese Simplified locale.\n * @language Chinese Simplified\n * @iso-639-2 zho\n * <AUTHOR> [@KingMario](https://github.com/KingMario)\n * <AUTHOR> [@fnlctrl](https://github.com/fnlctrl)\n * <AUTHOR> [@sabrinamiao](https://github.com/sabrinamiao)\n * <AUTHOR> [@cubicwork](https://github.com/cubicwork)\n * <AUTHOR> [@skyuplam](https://github.com/skyuplam)\n */\nexport const zhCN = {\n  code: \"zh-CN\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default zhCN;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,KAAK,QAAQ,uBAAuB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}