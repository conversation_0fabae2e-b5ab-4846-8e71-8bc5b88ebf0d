{"ast": null, "code": "import { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.mjs\";\nimport { millisecondsInDay } from \"./constants.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name getOverlappingDaysInIntervals\n * @category Interval Helpers\n * @summary Get the number of days that overlap in two time intervals\n *\n * @description\n * Get the number of days that overlap in two time intervals. It uses the time\n * between dates to calculate the number of days, rounding it up to include\n * partial days.\n *\n * Two equal 0-length intervals will result in 0. Two equal 1ms intervals will\n * result in 1.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param intervalLeft - The first interval to compare.\n * @param intervalRight - The second interval to compare.\n *\n * @returns The number of days that overlap in two time intervals\n *\n * @example\n * // For overlapping time intervals adds 1 for each started overlapping day:\n * getOverlappingDaysInIntervals(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n * )\n * //=> 3\n *\n * @example\n * // For non-overlapping time intervals returns 0:\n * getOverlappingDaysInIntervals(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 21), end: new Date(2014, 0, 22) }\n * )\n * //=> 0\n */\n\nexport function getOverlappingDaysInIntervals(intervalLeft, intervalRight) {\n  const [leftStart, leftEnd] = [+toDate(intervalLeft.start), +toDate(intervalLeft.end)].sort((a, b) => a - b);\n  const [rightStart, rightEnd] = [+toDate(intervalRight.start), +toDate(intervalRight.end)].sort((a, b) => a - b);\n\n  // Prevent NaN result if intervals don't overlap at all.\n  const isOverlapping = leftStart < rightEnd && rightStart < leftEnd;\n  if (!isOverlapping) return 0;\n\n  // Remove the timezone offset to negate the DST effect on calculations.\n  const overlapLeft = rightStart < leftStart ? leftStart : rightStart;\n  const left = overlapLeft - getTimezoneOffsetInMilliseconds(overlapLeft);\n  const overlapRight = rightEnd > leftEnd ? leftEnd : rightEnd;\n  const right = overlapRight - getTimezoneOffsetInMilliseconds(overlapRight);\n\n  // Ceil the number to include partial days too.\n  return Math.ceil((right - left) / millisecondsInDay);\n}\n\n// Fallback for modularized imports:\nexport default getOverlappingDaysInIntervals;", "map": {"version": 3, "names": ["getTimezoneOffsetInMilliseconds", "millisecondsInDay", "toDate", "getOverlappingDaysInIntervals", "intervalLeft", "intervalRight", "leftStart", "leftEnd", "start", "end", "sort", "a", "b", "rightStart", "rightEnd", "isOverlapping", "overlapLeft", "left", "overlapRight", "right", "Math", "ceil"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/getOverlappingDaysInIntervals.mjs"], "sourcesContent": ["import { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.mjs\";\nimport { millisecondsInDay } from \"./constants.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name getOverlappingDaysInIntervals\n * @category Interval Helpers\n * @summary Get the number of days that overlap in two time intervals\n *\n * @description\n * Get the number of days that overlap in two time intervals. It uses the time\n * between dates to calculate the number of days, rounding it up to include\n * partial days.\n *\n * Two equal 0-length intervals will result in 0. Two equal 1ms intervals will\n * result in 1.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param intervalLeft - The first interval to compare.\n * @param intervalRight - The second interval to compare.\n *\n * @returns The number of days that overlap in two time intervals\n *\n * @example\n * // For overlapping time intervals adds 1 for each started overlapping day:\n * getOverlappingDaysInIntervals(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n * )\n * //=> 3\n *\n * @example\n * // For non-overlapping time intervals returns 0:\n * getOverlappingDaysInIntervals(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 21), end: new Date(2014, 0, 22) }\n * )\n * //=> 0\n */\n\nexport function getOverlappingDaysInIntervals(intervalLeft, intervalRight) {\n  const [leftStart, leftEnd] = [\n    +toDate(intervalLeft.start),\n    +toDate(intervalLeft.end),\n  ].sort((a, b) => a - b);\n  const [rightStart, rightEnd] = [\n    +toDate(intervalRight.start),\n    +toDate(intervalRight.end),\n  ].sort((a, b) => a - b);\n\n  // Prevent NaN result if intervals don't overlap at all.\n  const isOverlapping = leftStart < rightEnd && rightStart < leftEnd;\n  if (!isOverlapping) return 0;\n\n  // Remove the timezone offset to negate the DST effect on calculations.\n  const overlapLeft = rightStart < leftStart ? leftStart : rightStart;\n  const left = overlapLeft - getTimezoneOffsetInMilliseconds(overlapLeft);\n  const overlapRight = rightEnd > leftEnd ? leftEnd : rightEnd;\n  const right = overlapRight - getTimezoneOffsetInMilliseconds(overlapRight);\n\n  // Ceil the number to include partial days too.\n  return Math.ceil((right - left) / millisecondsInDay);\n}\n\n// Fallback for modularized imports:\nexport default getOverlappingDaysInIntervals;\n"], "mappings": "AAAA,SAASA,+BAA+B,QAAQ,4CAA4C;AAC5F,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,SAASC,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,6BAA6BA,CAACC,YAAY,EAAEC,aAAa,EAAE;EACzE,MAAM,CAACC,SAAS,EAAEC,OAAO,CAAC,GAAG,CAC3B,CAACL,MAAM,CAACE,YAAY,CAACI,KAAK,CAAC,EAC3B,CAACN,MAAM,CAACE,YAAY,CAACK,GAAG,CAAC,CAC1B,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;EACvB,MAAM,CAACC,UAAU,EAAEC,QAAQ,CAAC,GAAG,CAC7B,CAACZ,MAAM,CAACG,aAAa,CAACG,KAAK,CAAC,EAC5B,CAACN,MAAM,CAACG,aAAa,CAACI,GAAG,CAAC,CAC3B,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;;EAEvB;EACA,MAAMG,aAAa,GAAGT,SAAS,GAAGQ,QAAQ,IAAID,UAAU,GAAGN,OAAO;EAClE,IAAI,CAACQ,aAAa,EAAE,OAAO,CAAC;;EAE5B;EACA,MAAMC,WAAW,GAAGH,UAAU,GAAGP,SAAS,GAAGA,SAAS,GAAGO,UAAU;EACnE,MAAMI,IAAI,GAAGD,WAAW,GAAGhB,+BAA+B,CAACgB,WAAW,CAAC;EACvE,MAAME,YAAY,GAAGJ,QAAQ,GAAGP,OAAO,GAAGA,OAAO,GAAGO,QAAQ;EAC5D,MAAMK,KAAK,GAAGD,YAAY,GAAGlB,+BAA+B,CAACkB,YAAY,CAAC;;EAE1E;EACA,OAAOE,IAAI,CAACC,IAAI,CAAC,CAACF,KAAK,GAAGF,IAAI,IAAIhB,iBAAiB,CAAC;AACtD;;AAEA;AACA,eAAeE,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}