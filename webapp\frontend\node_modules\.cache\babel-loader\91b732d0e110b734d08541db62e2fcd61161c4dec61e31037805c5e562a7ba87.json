{"ast": null, "code": "import { default as value } from \"./value.js\";\nexport default function piecewise(interpolate, values) {\n  if (values === undefined) values = interpolate, interpolate = value;\n  var i = 0,\n    n = values.length - 1,\n    v = values[0],\n    I = new Array(n < 0 ? 0 : n);\n  while (i < n) I[i] = interpolate(v, v = values[++i]);\n  return function (t) {\n    var i = Math.max(0, Math.min(n - 1, Math.floor(t *= n)));\n    return I[i](t - i);\n  };\n}", "map": {"version": 3, "names": ["default", "value", "piecewise", "interpolate", "values", "undefined", "i", "n", "length", "v", "I", "Array", "t", "Math", "max", "min", "floor"], "sources": ["C:/CMS/webapp/frontend/node_modules/d3-interpolate/src/piecewise.js"], "sourcesContent": ["import {default as value} from \"./value.js\";\n\nexport default function piecewise(interpolate, values) {\n  if (values === undefined) values = interpolate, interpolate = value;\n  var i = 0, n = values.length - 1, v = values[0], I = new Array(n < 0 ? 0 : n);\n  while (i < n) I[i] = interpolate(v, v = values[++i]);\n  return function(t) {\n    var i = Math.max(0, Math.min(n - 1, Math.floor(t *= n)));\n    return I[i](t - i);\n  };\n}\n"], "mappings": "AAAA,SAAQA,OAAO,IAAIC,KAAK,QAAO,YAAY;AAE3C,eAAe,SAASC,SAASA,CAACC,WAAW,EAAEC,MAAM,EAAE;EACrD,IAAIA,MAAM,KAAKC,SAAS,EAAED,MAAM,GAAGD,WAAW,EAAEA,WAAW,GAAGF,KAAK;EACnE,IAAIK,CAAC,GAAG,CAAC;IAAEC,CAAC,GAAGH,MAAM,CAACI,MAAM,GAAG,CAAC;IAAEC,CAAC,GAAGL,MAAM,CAAC,CAAC,CAAC;IAAEM,CAAC,GAAG,IAAIC,KAAK,CAACJ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC;EAC7E,OAAOD,CAAC,GAAGC,CAAC,EAAEG,CAAC,CAACJ,CAAC,CAAC,GAAGH,WAAW,CAACM,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAAC,EAAEE,CAAC,CAAC,CAAC;EACpD,OAAO,UAASM,CAAC,EAAE;IACjB,IAAIN,CAAC,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACR,CAAC,GAAG,CAAC,EAAEM,IAAI,CAACG,KAAK,CAACJ,CAAC,IAAIL,CAAC,CAAC,CAAC,CAAC;IACxD,OAAOG,CAAC,CAACJ,CAAC,CAAC,CAACM,CAAC,GAAGN,CAAC,CAAC;EACpB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}