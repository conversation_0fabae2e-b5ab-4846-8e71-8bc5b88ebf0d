{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\PosaCaviCollegamenti.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Paper, Divider, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Grid, Alert, CircularProgress, FormHelperText, Radio, RadioGroup, FormControlLabel, FormLabel } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Cable as CableIcon, Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport CavoForm from './CavoForm';\nimport SelezionaCavoForm from './SelezionaCavoForm';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PosaCaviCollegamenti = ({\n  cantiereId: propCantiereId,\n  onSuccess,\n  onError,\n  initialOption = null,\n  preselectedCavo = null,\n  dialogOnly = false\n}) => {\n  _s();\n  // Log del cantiereId all'avvio\n  console.log('PosaCaviCollegamenti - cantiereId da props:', propCantiereId);\n\n  // Aggiungi navigate per la navigazione programmatica\n  const navigate = useNavigate();\n\n  // Se cantiereId non è definito nelle props, prova a recuperarlo dal localStorage\n  const cantiereId = propCantiereId || parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  console.log('PosaCaviCollegamenti - cantiereId effettivo:', cantiereId);\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n\n  // Inizializza il componente con l'opzione iniziale se specificata\n  React.useEffect(() => {\n    if (initialOption) {\n      // Imposta direttamente le opzioni invece di chiamare handleOptionSelect\n      // per evitare dipendenze circolari\n      setSelectedOption(initialOption);\n      if (initialOption === 'eliminaCavo') {\n        if (preselectedCavo) {\n          // Se c'è un cavo preselezionato, usalo direttamente\n          setSelectedCavo(preselectedCavo);\n          setDialogType('eliminaCavo');\n          setOpenDialog(true);\n        } else {\n          // Altrimenti carica la lista dei cavi\n          loadCavi('eliminaCavo');\n          setDialogType('eliminaCavo');\n          setOpenDialog(true);\n        }\n      } else if (initialOption === 'modificaCavo') {\n        if (preselectedCavo) {\n          // Se c'è un cavo preselezionato, usalo direttamente per la modifica\n          setSelectedCavo(preselectedCavo);\n          setDialogType('modificaCavo');\n          setFormData({\n            ...preselectedCavo,\n            metri_teorici: preselectedCavo.metri_teorici || '',\n            metratura_reale: preselectedCavo.metratura_reale || '0'\n          });\n          setOpenDialog(true);\n        } else {\n          // Altrimenti carica la lista dei cavi\n          loadCavi('modificaCavo');\n          setDialogType('selezionaCavo');\n          setOpenDialog(true);\n        }\n      } else if (initialOption === 'aggiungiCavo') {\n        setDialogType('aggiungiCavo');\n        setOpenDialog(true);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [initialOption, preselectedCavo]);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async operationType => {\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n\n      // Filtra i cavi in base al tipo di operazione\n      if (operationType === 'modificaCavo') {\n        // Per modifica cavo, mostra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\n        const caviNonPosati = caviData.filter(cavo => parseFloat(cavo.metratura_reale) === 0 && cavo.stato_installazione !== 'Installato');\n        setCavi(caviNonPosati);\n      } else {\n        // Per altre operazioni, mostra tutti i cavi\n        setCavi(caviData);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi(option);\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      // Apri il dialog per aggiungere un nuovo cavo\n      setDialogType('aggiungiCavo');\n      setOpenDialog(true);\n    } else if (option === 'modificaCavo') {\n      loadCavi('modificaCavo');\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi('eliminaCavo');\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    console.log('Chiusura dialog...');\n    // Reset dello stato del componente\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setCavoIdInput('');\n    setFormErrors({});\n    setFormWarnings({});\n    setDeleteMode('spare'); // Reset alla modalità predefinita\n    setLoading(false); // Assicurati che loading sia false quando chiudi il dialog\n\n    // Se è stato specificato un initialOption, notifica il genitore che il dialogo è stato chiuso\n    // ma senza messaggio di errore\n    if (initialOption && onSuccess) {\n      // Chiama onSuccess ma senza messaggio per evitare l'alert\n      onSuccess(null);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setSelectedCavo(cavo);\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n      console.log('Ricerca cavo con ID:', cavoIdInput, 'per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      const cavo = caviData.find(c => c.id_cavo === cavoIdInput.trim());\n      if (!cavo) {\n        onError(`Cavo con ID ${cavoIdInput} non trovato`);\n        return;\n      }\n\n      // Verifica se stiamo cercando un cavo per modificarlo\n      if (dialogType === 'selezionaCavo') {\n        // Verifica che il cavo non sia già posato\n        if (parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato') {\n          onError(`Il cavo ${cavo.id_cavo} risulta già posato. Utilizza l'opzione \"Modifica bobina cavo posato\" per modificarlo.`);\n          return;\n        }\n      }\n\n      // Seleziona il cavo trovato\n      handleCavoSelect(cavo);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce il cambio dell'input dell'ID cavo\n  const handleCavoIdInputChange = e => {\n    setCavoIdInput(e.target.value);\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({\n            metri_posati: 'Inserire un valore numerico valido'\n          });\n          setLoading(false);\n          return;\n        }\n        try {\n          await caviService.updateMetriPosati(cantiereId, formData.id_cavo, parseFloat(formData.metri_posati));\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Metri posati aggiornati con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n          onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n          setLoading(false);\n        }\n      } else if (dialogType === 'modificaBobina') {\n        try {\n          await caviService.updateBobina(cantiereId, formData.id_cavo, formData.id_bobina);\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Bobina aggiornata con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento della bobina:', error);\n          onError('Errore durante l\\'aggiornamento della bobina: ' + (error.message || 'Errore sconosciuto'));\n          setLoading(false);\n        }\n      } else if (dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n\n        // Rimuovi i campi di sistema che non devono essere modificati\n        const dataToSend = {\n          ...validatedData\n        };\n        delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n        delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n        delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n        delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n        delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n        // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n        dataToSend.modificato_manualmente = 1;\n        console.log('Dati inviati al server:', dataToSend);\n        try {\n          console.log('Invio dati al server per aggiornamento cavo:', dataToSend);\n          const result = await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n          console.log('Risposta dal server dopo aggiornamento cavo:', result);\n\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Cavo modificato con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n          return;\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento del cavo:', error);\n\n          // Gestione più dettagliata dell'errore\n          let errorMessage = 'Errore durante l\\'aggiornamento del cavo';\n          if (error.response) {\n            // Errore dal server con risposta\n            errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n            if (error.response.data && error.response.data.detail) {\n              errorMessage += ` - ${error.response.data.detail}`;\n            }\n          } else if (error.request) {\n            // Errore di rete senza risposta dal server\n            errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n\n            // Anche se c'è un errore di rete, la modifica potrebbe essere stata salvata\n            // Quindi consideriamo l'operazione come riuscita\n            console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore di rete');\n            // Prima chiama onSuccess, poi chiudi il dialog\n            onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');\n            // Chiudi il dialog\n            handleCloseDialog();\n            // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n            setTimeout(() => {\n              redirectToVisualizzaCavi(navigate, 1000);\n            }, 500);\n            return;\n          } else if (error.message) {\n            // Errore con messaggio\n            errorMessage += `: ${error.message}`;\n\n            // Se il messaggio indica che la modifica potrebbe essere stata salvata comunque\n            if (error.message.includes('La modifica potrebbe essere stata salvata')) {\n              console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore');\n              handleCloseDialog();\n              onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');\n              // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n              redirectToVisualizzaCavi(navigate, 1000);\n              return;\n            }\n          }\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        // Verifica se il cavo è installato\n        const isInstalled = selectedCavo.stato_installazione === 'Installato' || selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0;\n        if (isInstalled) {\n          // Se è installato, marca solo come SPARE\n          console.log('Marcando cavo installato come SPARE:', selectedCavo.id_cavo);\n          try {\n            // Prima prova con markCavoAsSpare\n            const result = await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo);\n            console.log('Risultato marcatura SPARE:', result);\n            console.log('Nuovo valore modificato_manualmente:', result.modificato_manualmente);\n            // Chiudi il dialog prima di chiamare onSuccess\n            handleCloseDialog();\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n            // Reindirizza alla pagina di visualizzazione cavi con un ritardo\n            redirectToVisualizzaCavi(navigate, 500);\n          } catch (markError) {\n            console.error('Errore con markCavoAsSpare, tentativo con deleteCavo mode=spare:', markError);\n            // Se fallisce, prova con deleteCavo mode=spare\n            const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, 'spare');\n            console.log('Risultato marcatura SPARE con deleteCavo:', result);\n            // Chiudi il dialog prima di chiamare onSuccess\n            handleCloseDialog();\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n          }\n        } else {\n          // Se non è installato, usa la modalità selezionata (SPARE o DELETE)\n          console.log('Eliminando cavo non installato con modalità:', deleteMode);\n          const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n          console.log('Risultato eliminazione/marcatura:', result);\n          // Chiudi il dialog prima di chiamare onSuccess\n          handleCloseDialog();\n          onSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n        }\n      }\n\n      // Non chiamare handleCloseDialog() qui, perché il dialog verrà chiuso dal genitore\n      // quando viene chiamato onSuccess()\n    } catch (error) {\n      console.error('Errore durante l\\'operazione:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore sconosciuto';\n      if (error.detail) {\n        // Errore dal backend con dettaglio\n        errorMessage = error.detail;\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage = error.message;\n      } else if (typeof error === 'string') {\n        // Errore come stringa\n        errorMessage = error;\n      }\n      onError('Errore durante l\\'operazione: ' + errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'aggiungiCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        PaperProps: {\n          sx: {\n            width: '80%',\n            // Allargato di 2cm (rispetto a sm che è circa 60%)\n            minHeight: '80vh',\n            // Allungato di 1cm (aggiungendo altezza minima)\n            maxHeight: '90vh',\n            overflow: 'auto'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          sx: {\n            pb: 1\n          },\n          children: \"Aggiungi Nuovo Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          sx: {\n            pt: 0,\n            pb: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(CavoForm, {\n              mode: \"add\",\n              cantiereId: cantiereId,\n              onSubmit: async validatedData => {\n                try {\n                  await caviService.createCavo(cantiereId, validatedData);\n                  return true;\n                } catch (error) {\n                  throw error;\n                }\n              },\n              onSuccess: message => {\n                onSuccess(message);\n                handleCloseDialog();\n              },\n              onError: onError,\n              isDialog: true,\n              onCancel: handleCloseDialog\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'inserisciMetri') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Inserisci Metri Posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Seleziona un cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                button: true,\n                onClick: () => handleCavoSelect(cavo),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: cavo.id_cavo,\n                  secondary: `${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 23\n                }, this)\n              }, cavo.id_cavo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metri teorici: \", selectedCavo.metri_teorici || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metratura attuale: \", selectedCavo.metratura_reale || '0']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"dense\",\n              name: \"metri_posati\",\n              label: \"Metri posati da aggiungere\",\n              type: \"number\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_posati,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_posati,\n              helperText: formErrors.metri_posati,\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || !formData.metri_posati,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 71\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'modificaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Bobina Cavo Posato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 669,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Seleziona un cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                button: true,\n                onClick: () => handleCavoSelect(cavo),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: cavo.id_cavo,\n                  secondary: `Bobina attuale: ${cavo.id_bobina ? cavo.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : cavo.id_bobina : 'BOBINA VUOTA'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 23\n                }, this)\n              }, cavo.id_cavo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Bobina attuale: \", selectedCavo.id_bobina ? selectedCavo.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : selectedCavo.id_bobina : 'BOBINA VUOTA']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"dense\",\n              name: \"id_bobina\",\n              label: \"ID Bobina\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_bobina,\n              onChange: handleFormChange,\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 717,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 71\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 716,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 668,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 733,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mb: 2\n            },\n            children: \"Puoi modificare solo i cavi non ancora posati (metratura = 0 e stato diverso da \\\"Installato\\\"). Per modificare cavi gi\\xE0 posati, utilizzare l'opzione \\\"Modifica bobina cavo posato\\\".\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 13\n          }, this), caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Inserisci l'ID del cavo da modificare:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"ID Cavo\",\n                variant: \"outlined\",\n                value: cavoIdInput,\n                onChange: handleCavoIdInputChange,\n                placeholder: \"Inserisci l'ID del cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: handleSearchCavoById,\n                disabled: caviLoading || !cavoIdInput.trim(),\n                sx: {\n                  ml: 2,\n                  minWidth: '120px'\n                },\n                children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 36\n                }, this) : \"Cerca\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 15\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 770,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 769,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 732,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'eliminaCavo') {\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = selectedCavo && (selectedCavo.stato_installazione === 'Installato' || selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0);\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: !selectedCavo ? 'Elimina Cavo' : isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 780,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 786,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Inserisci l'ID del cavo da eliminare:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"ID Cavo\",\n                variant: \"outlined\",\n                value: cavoIdInput,\n                onChange: handleCavoIdInputChange,\n                placeholder: \"Inserisci l'ID del cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: handleSearchCavoById,\n                disabled: caviLoading || !cavoIdInput.trim(),\n                sx: {\n                  ml: 2,\n                  minWidth: '120px'\n                },\n                children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 36\n                }, this) : \"Cerca\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 15\n          }, this) : dialogType === 'eliminaCavo' && isInstalled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n              children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 815,\n                columnNumber: 27\n              }, this), \" risulta installato o parzialmente posato.\", selectedCavo.metratura_reale > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [\" Metri posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [selectedCavo.metratura_reale, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 817,\n                  columnNumber: 38\n                }, this), \".\"]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DialogContentText, {\n              sx: {\n                mt: 2\n              },\n              children: \"Non \\xE8 possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : dialogType === 'eliminaCavo' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n              children: [\"Stai per eliminare il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 46\n              }, this), \".\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              component: \"fieldset\",\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n                component: \"legend\",\n                children: \"Scegli l'operazione da eseguire:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n                value: deleteMode,\n                onChange: e => setDeleteMode(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  value: \"spare\",\n                  control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 838,\n                    columnNumber: 32\n                  }, this),\n                  label: \"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  value: \"delete\",\n                  control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 843,\n                    columnNumber: 32\n                  }, this),\n                  label: \"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 841,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 852,\n            columnNumber: 13\n          }, this), dialogType === 'eliminaCavo' && selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: isInstalled ? \"warning\" : \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 38\n            }, this) : isInstalled ? /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 85\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 103\n            }, this),\n            children: isInstalled ? \"Marca come SPARE\" : deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 854,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 851,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 779,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'modificaCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        PaperProps: {\n          sx: {\n            width: '80%',\n            // Allargato di 2cm (rispetto a sm che è circa 60%)\n            minHeight: '80vh',\n            // Allungato di 1cm (aggiungendo altezza minima)\n            maxHeight: '90vh',\n            overflow: 'auto'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          sx: {\n            pb: 1\n          },\n          children: \"Modifica Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 882,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          sx: {\n            pt: 0,\n            pb: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 0\n            },\n            children: selectedCavo ? /*#__PURE__*/_jsxDEV(CavoForm, {\n              mode: \"edit\",\n              initialData: formData,\n              cantiereId: cantiereId,\n              onSubmit: async validatedData => {\n                try {\n                  // Rimuovi i campi di sistema che non devono essere modificati\n                  const dataToSend = {\n                    ...validatedData\n                  };\n                  delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n                  delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n                  delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n                  delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n                  delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n                  // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n                  dataToSend.modificato_manualmente = 1;\n                  await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n                  return true;\n                } catch (error) {\n                  throw error;\n                }\n              },\n              onSuccess: message => {\n                onSuccess(message);\n                handleCloseDialog();\n              },\n              onError: onError,\n              isDialog: true,\n              onCancel: handleCloseDialog\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(SelezionaCavoForm, {\n              cantiereId: cantiereId,\n              onSuccess: message => {\n                onSuccess(message);\n                handleCloseDialog();\n              },\n              onError: onError,\n              isDialog: true,\n              onCancel: handleCloseDialog\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 918,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 883,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 868,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '280px',\n        mr: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Posa Cavi e Collegamenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 944,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 947,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          dense: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('inserisciMetri'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 951,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 950,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"1. Inserisci metri posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 953,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 949,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('modificaBobina'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"2. Modifica bobina cavo posato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 956,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('aggiungiCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 965,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"3. Aggiungi nuovo cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 967,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 963,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('modificaCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 972,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 971,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"4. Modifica cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 974,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 970,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('eliminaCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 979,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 978,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"5. Elimina cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 981,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 977,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 948,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 943,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 942,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flexGrow: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          minHeight: '300px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: [!selectedOption && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          children: \"Seleziona un'opzione dal menu a sinistra per iniziare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 991,\n          columnNumber: 13\n        }, this), selectedOption && !openDialog && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: [selectedOption === 'inserisciMetri' && 'Inserisci metri posati', selectedOption === 'modificaCavo' && 'Modifica cavo', selectedOption === 'aggiungiCavo' && 'Aggiungi nuovo cavo', selectedOption === 'eliminaCavo' && 'Elimina cavo', selectedOption === 'modificaBobina' && 'Modifica bobina cavo posato']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 997,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Caricamento in corso...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1004,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n            sx: {\n              mt: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1007,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 996,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 989,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 988,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 940,\n    columnNumber: 5\n  }, this);\n};\n_s(PosaCaviCollegamenti, \"iT4/SZ0cNA98E3RS5ZcI0FqlIJU=\", false, function () {\n  return [useNavigate];\n});\n_c = PosaCaviCollegamenti;\nexport default PosaCaviCollegamenti;\nvar _c;\n$RefreshReg$(_c, \"PosaCaviCollegamenti\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Divider", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Grid", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "Radio", "RadioGroup", "FormControlLabel", "FormLabel", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Cable", "CableIcon", "Save", "SaveIcon", "Warning", "WarningIcon", "useNavigate", "caviService", "validateCavoData", "validateField", "isEmpty", "redirectToVisualizzaCavi", "CavoForm", "SelezionaCavoForm", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PosaCaviCollegamenti", "cantiereId", "propCantiereId", "onSuccess", "onError", "initialOption", "preselectedCavo", "dialogOnly", "_s", "console", "log", "navigate", "parseInt", "localStorage", "getItem", "loading", "setLoading", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "cavoIdInput", "setCavoIdInput", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "cavi", "<PERSON><PERSON><PERSON>", "caviLoading", "setCaviLoading", "deleteMode", "setDeleteMode", "useEffect", "loadCavi", "metri_te<PERSON>ci", "metratura_reale", "operationType", "Error", "caviData", "get<PERSON><PERSON>", "cavi<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "cavo", "parseFloat", "stato_installazione", "error", "errorMessage", "response", "status", "statusText", "data", "detail", "request", "message", "handleOptionSelect", "option", "handleCloseDialog", "handleCavoSelect", "handleSearchCavoById", "trim", "find", "c", "handleCavoIdInputChange", "e", "target", "value", "handleFormChange", "name", "additionalParams", "metriTeorici", "result", "prev", "valid", "warning", "handleSave", "isNaN", "updateMetri<PERSON><PERSON><PERSON>", "setTimeout", "updateBobina", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "validatedData", "dataToSend", "modificato_manualmente", "timestamp", "updateCavo", "includes", "Object", "keys", "length", "warningMessages", "values", "join", "warn", "isInstalled", "markCavoAsSpare", "<PERSON><PERSON><PERSON><PERSON>", "deleteCavo", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "sx", "width", "minHeight", "maxHeight", "overflow", "children", "pb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pt", "mt", "mode", "onSubmit", "createCavo", "isDialog", "onCancel", "severity", "variant", "gutterBottom", "map", "button", "onClick", "primary", "secondary", "tipologia", "ubicazione_partenza", "ubicazione_arrivo", "margin", "label", "type", "onChange", "required", "helperText", "disabled", "startIcon", "size", "mb", "p", "display", "alignItems", "placeholder", "color", "ml", "min<PERSON><PERSON><PERSON>", "component", "control", "initialData", "mr", "dense", "flexGrow", "justifyContent", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/PosaCaviCollegamenti.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogContentText,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Grid,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  FormLabel\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Cable as CableIcon,\n  Save as SaveIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport CavoForm from './CavoForm';\nimport SelezionaCavoForm from './SelezionaCavoForm';\n\nconst PosaCaviCollegamenti = ({ cantiereId: propCantiereId, onSuccess, onError, initialOption = null, preselectedCavo = null, dialogOnly = false }) => {\n  // Log del cantiereId all'avvio\n  console.log('PosaCaviCollegamenti - cantiereId da props:', propCantiereId);\n\n  // Aggiungi navigate per la navigazione programmatica\n  const navigate = useNavigate();\n\n  // Se cantiereId non è definito nelle props, prova a recuperarlo dal localStorage\n  const cantiereId = propCantiereId || parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  console.log('PosaCaviCollegamenti - cantiereId effettivo:', cantiereId);\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n\n  // Inizializza il componente con l'opzione iniziale se specificata\n  React.useEffect(() => {\n    if (initialOption) {\n      // Imposta direttamente le opzioni invece di chiamare handleOptionSelect\n      // per evitare dipendenze circolari\n      setSelectedOption(initialOption);\n\n      if (initialOption === 'eliminaCavo') {\n        if (preselectedCavo) {\n          // Se c'è un cavo preselezionato, usalo direttamente\n          setSelectedCavo(preselectedCavo);\n          setDialogType('eliminaCavo');\n          setOpenDialog(true);\n        } else {\n          // Altrimenti carica la lista dei cavi\n          loadCavi('eliminaCavo');\n          setDialogType('eliminaCavo');\n          setOpenDialog(true);\n        }\n      } else if (initialOption === 'modificaCavo') {\n        if (preselectedCavo) {\n          // Se c'è un cavo preselezionato, usalo direttamente per la modifica\n          setSelectedCavo(preselectedCavo);\n          setDialogType('modificaCavo');\n          setFormData({\n            ...preselectedCavo,\n            metri_teorici: preselectedCavo.metri_teorici || '',\n            metratura_reale: preselectedCavo.metratura_reale || '0'\n          });\n          setOpenDialog(true);\n        } else {\n          // Altrimenti carica la lista dei cavi\n          loadCavi('modificaCavo');\n          setDialogType('selezionaCavo');\n          setOpenDialog(true);\n        }\n      } else if (initialOption === 'aggiungiCavo') {\n        setDialogType('aggiungiCavo');\n        setOpenDialog(true);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [initialOption, preselectedCavo]);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async (operationType) => {\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n\n      // Filtra i cavi in base al tipo di operazione\n      if (operationType === 'modificaCavo') {\n        // Per modifica cavo, mostra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\n        const caviNonPosati = caviData.filter(cavo =>\n          parseFloat(cavo.metratura_reale) === 0 &&\n          cavo.stato_installazione !== 'Installato'\n        );\n        setCavi(caviNonPosati);\n      } else {\n        // Per altre operazioni, mostra tutti i cavi\n        setCavi(caviData);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi(option);\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      // Apri il dialog per aggiungere un nuovo cavo\n      setDialogType('aggiungiCavo');\n      setOpenDialog(true);\n    } else if (option === 'modificaCavo') {\n      loadCavi('modificaCavo');\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi('eliminaCavo');\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    console.log('Chiusura dialog...');\n    // Reset dello stato del componente\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setCavoIdInput('');\n    setFormErrors({});\n    setFormWarnings({});\n    setDeleteMode('spare'); // Reset alla modalità predefinita\n    setLoading(false); // Assicurati che loading sia false quando chiudi il dialog\n\n    // Se è stato specificato un initialOption, notifica il genitore che il dialogo è stato chiuso\n    // ma senza messaggio di errore\n    if (initialOption && onSuccess) {\n      // Chiama onSuccess ma senza messaggio per evitare l'alert\n      onSuccess(null);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setSelectedCavo(cavo);\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n\n      console.log('Ricerca cavo con ID:', cavoIdInput, 'per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      const cavo = caviData.find(c => c.id_cavo === cavoIdInput.trim());\n\n      if (!cavo) {\n        onError(`Cavo con ID ${cavoIdInput} non trovato`);\n        return;\n      }\n\n      // Verifica se stiamo cercando un cavo per modificarlo\n      if (dialogType === 'selezionaCavo') {\n        // Verifica che il cavo non sia già posato\n        if (parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato') {\n          onError(`Il cavo ${cavo.id_cavo} risulta già posato. Utilizza l'opzione \"Modifica bobina cavo posato\" per modificarlo.`);\n          return;\n        }\n      }\n\n      // Seleziona il cavo trovato\n      handleCavoSelect(cavo);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce il cambio dell'input dell'ID cavo\n  const handleCavoIdInputChange = (e) => {\n    setCavoIdInput(e.target.value);\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({ metri_posati: 'Inserire un valore numerico valido' });\n          setLoading(false);\n          return;\n        }\n\n        try {\n          await caviService.updateMetriPosati(\n            cantiereId,\n            formData.id_cavo,\n            parseFloat(formData.metri_posati)\n          );\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Metri posati aggiornati con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n          onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n          setLoading(false);\n        }\n      } else if (dialogType === 'modificaBobina') {\n        try {\n          await caviService.updateBobina(\n            cantiereId,\n            formData.id_cavo,\n            formData.id_bobina\n          );\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Bobina aggiornata con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento della bobina:', error);\n          onError('Errore durante l\\'aggiornamento della bobina: ' + (error.message || 'Errore sconosciuto'));\n          setLoading(false);\n        }\n      } else if (dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n\n        // Rimuovi i campi di sistema che non devono essere modificati\n        const dataToSend = { ...validatedData };\n        delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n        delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n        delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n        delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n        delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n        // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n        dataToSend.modificato_manualmente = 1;\n\n        console.log('Dati inviati al server:', dataToSend);\n\n        try {\n          console.log('Invio dati al server per aggiornamento cavo:', dataToSend);\n          const result = await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n          console.log('Risposta dal server dopo aggiornamento cavo:', result);\n\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Cavo modificato con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n          return;\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento del cavo:', error);\n\n          // Gestione più dettagliata dell'errore\n          let errorMessage = 'Errore durante l\\'aggiornamento del cavo';\n\n          if (error.response) {\n            // Errore dal server con risposta\n            errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n            if (error.response.data && error.response.data.detail) {\n              errorMessage += ` - ${error.response.data.detail}`;\n            }\n          } else if (error.request) {\n            // Errore di rete senza risposta dal server\n            errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n\n            // Anche se c'è un errore di rete, la modifica potrebbe essere stata salvata\n            // Quindi consideriamo l'operazione come riuscita\n            console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore di rete');\n            // Prima chiama onSuccess, poi chiudi il dialog\n            onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');\n            // Chiudi il dialog\n            handleCloseDialog();\n            // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n            setTimeout(() => {\n              redirectToVisualizzaCavi(navigate, 1000);\n            }, 500);\n            return;\n          } else if (error.message) {\n            // Errore con messaggio\n            errorMessage += `: ${error.message}`;\n\n            // Se il messaggio indica che la modifica potrebbe essere stata salvata comunque\n            if (error.message.includes('La modifica potrebbe essere stata salvata')) {\n              console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore');\n              handleCloseDialog();\n              onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');\n              // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n              redirectToVisualizzaCavi(navigate, 1000);\n              return;\n            }\n          }\n\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        // Verifica se il cavo è installato\n        const isInstalled = selectedCavo.stato_installazione === 'Installato' || (selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0);\n\n        if (isInstalled) {\n          // Se è installato, marca solo come SPARE\n          console.log('Marcando cavo installato come SPARE:', selectedCavo.id_cavo);\n          try {\n            // Prima prova con markCavoAsSpare\n            const result = await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo);\n            console.log('Risultato marcatura SPARE:', result);\n            console.log('Nuovo valore modificato_manualmente:', result.modificato_manualmente);\n            // Chiudi il dialog prima di chiamare onSuccess\n            handleCloseDialog();\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n            // Reindirizza alla pagina di visualizzazione cavi con un ritardo\n            redirectToVisualizzaCavi(navigate, 500);\n          } catch (markError) {\n            console.error('Errore con markCavoAsSpare, tentativo con deleteCavo mode=spare:', markError);\n            // Se fallisce, prova con deleteCavo mode=spare\n            const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, 'spare');\n            console.log('Risultato marcatura SPARE con deleteCavo:', result);\n            // Chiudi il dialog prima di chiamare onSuccess\n            handleCloseDialog();\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n          }\n        } else {\n          // Se non è installato, usa la modalità selezionata (SPARE o DELETE)\n          console.log('Eliminando cavo non installato con modalità:', deleteMode);\n          const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n          console.log('Risultato eliminazione/marcatura:', result);\n          // Chiudi il dialog prima di chiamare onSuccess\n          handleCloseDialog();\n          onSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n        }\n      }\n\n      // Non chiamare handleCloseDialog() qui, perché il dialog verrà chiuso dal genitore\n      // quando viene chiamato onSuccess()\n    } catch (error) {\n      console.error('Errore durante l\\'operazione:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore sconosciuto';\n\n      if (error.detail) {\n        // Errore dal backend con dettaglio\n        errorMessage = error.detail;\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage = error.message;\n      } else if (typeof error === 'string') {\n        // Errore come stringa\n        errorMessage = error;\n      }\n\n      onError('Errore durante l\\'operazione: ' + errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'aggiungiCavo') {\n      return (\n        <Dialog\n          open={openDialog}\n          onClose={handleCloseDialog}\n          maxWidth=\"md\"\n          fullWidth\n          PaperProps={{\n            sx: {\n              width: '80%',  // Allargato di 2cm (rispetto a sm che è circa 60%)\n              minHeight: '80vh', // Allungato di 1cm (aggiungendo altezza minima)\n              maxHeight: '90vh',\n              overflow: 'auto'\n            }\n          }}\n        >\n          <DialogTitle sx={{ pb: 1 }}>Aggiungi Nuovo Cavo</DialogTitle>\n          <DialogContent sx={{ pt: 0, pb: 1 }}>\n            <Box sx={{ mt: 0 }}>\n              <CavoForm\n                mode=\"add\"\n                cantiereId={cantiereId}\n                onSubmit={async (validatedData) => {\n                  try {\n                    await caviService.createCavo(cantiereId, validatedData);\n                    return true;\n                  } catch (error) {\n                    throw error;\n                  }\n                }}\n                onSuccess={(message) => {\n                  onSuccess(message);\n                  handleCloseDialog();\n                }}\n                onError={onError}\n                isDialog={true}\n                onCancel={handleCloseDialog}\n              />\n            </Box>\n          </DialogContent>\n          {/* No DialogActions needed here as CavoForm has its own buttons */}\n        </Dialog>\n      );\n    } else if (dialogType === 'inserisciMetri') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Inserisci Metri Posati</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metri teorici: {selectedCavo.metri_teorici || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metratura attuale: {selectedCavo.metratura_reale || '0'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"metri_posati\"\n                  label=\"Metri posati da aggiungere\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_posati}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_posati}\n                  helperText={formErrors.metri_posati}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading || !formData.metri_posati}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Bobina Cavo Posato</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`Bobina attuale: ${cavo.id_bobina ? (cavo.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : cavo.id_bobina) : 'BOBINA VUOTA'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Bobina attuale: {selectedCavo.id_bobina ? (selectedCavo.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : selectedCavo.id_bobina) : 'BOBINA VUOTA'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"id_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_bobina}\n                  onChange={handleFormChange}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Modifica Cavo</DialogTitle>\n          <DialogContent>\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Puoi modificare solo i cavi non ancora posati (metratura = 0 e stato diverso da \"Installato\").\n              Per modificare cavi già posati, utilizzare l'opzione \"Modifica bobina cavo posato\".\n            </Alert>\n\n            {caviLoading ? (\n              <CircularProgress />\n            ) : !selectedCavo ? (\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Inserisci l'ID del cavo da modificare:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>\n                  <TextField\n                    fullWidth\n                    label=\"ID Cavo\"\n                    variant=\"outlined\"\n                    value={cavoIdInput}\n                    onChange={handleCavoIdInputChange}\n                    placeholder=\"Inserisci l'ID del cavo\"\n                  />\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    onClick={handleSearchCavoById}\n                    disabled={caviLoading || !cavoIdInput.trim()}\n                    sx={{ ml: 2, minWidth: '120px' }}\n                  >\n                    {caviLoading ? <CircularProgress size={24} /> : \"Cerca\"}\n                  </Button>\n                </Box>\n              </Box>\n            ) : null}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaCavo') {\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = selectedCavo && (selectedCavo.stato_installazione === 'Installato' || (selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0));\n\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>\n            {!selectedCavo ? 'Elimina Cavo' :\n             isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'}\n          </DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : !selectedCavo ? (\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Inserisci l'ID del cavo da eliminare:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>\n                  <TextField\n                    fullWidth\n                    label=\"ID Cavo\"\n                    variant=\"outlined\"\n                    value={cavoIdInput}\n                    onChange={handleCavoIdInputChange}\n                    placeholder=\"Inserisci l'ID del cavo\"\n                  />\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    onClick={handleSearchCavoById}\n                    disabled={caviLoading || !cavoIdInput.trim()}\n                    sx={{ ml: 2, minWidth: '120px' }}\n                  >\n                    {caviLoading ? <CircularProgress size={24} /> : \"Cerca\"}\n                  </Button>\n                </Box>\n              </Box>\n            ) : dialogType === 'eliminaCavo' && isInstalled ? (\n              <>\n                <DialogContentText>\n                  Il cavo <strong>{selectedCavo.id_cavo}</strong> risulta installato o parzialmente posato.\n                  {selectedCavo.metratura_reale > 0 && (\n                    <> Metri posati: <strong>{selectedCavo.metratura_reale} m</strong>.</>\n                  )}\n                </DialogContentText>\n                <DialogContentText sx={{ mt: 2 }}>\n                  Non è possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\n                </DialogContentText>\n              </>\n            ) : dialogType === 'eliminaCavo' ? (\n              <>\n                <DialogContentText>\n                  Stai per eliminare il cavo <strong>{selectedCavo.id_cavo}</strong>.\n                </DialogContentText>\n\n                <FormControl component=\"fieldset\" sx={{ mt: 2 }}>\n                  <FormLabel component=\"legend\">Scegli l'operazione da eseguire:</FormLabel>\n                  <RadioGroup\n                    value={deleteMode}\n                    onChange={(e) => setDeleteMode(e.target.value)}\n                  >\n                    <FormControlLabel\n                      value=\"spare\"\n                      control={<Radio />}\n                      label=\"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                    />\n                    <FormControlLabel\n                      value=\"delete\"\n                      control={<Radio />}\n                      label=\"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                    />\n                  </RadioGroup>\n                </FormControl>\n              </>\n            ) : null}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {dialogType === 'eliminaCavo' && selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color={isInstalled ? \"warning\" : \"error\"}\n                startIcon={loading ? <CircularProgress size={20} /> : isInstalled ? <WarningIcon /> : <DeleteIcon />}\n              >\n                {isInstalled ? \"Marca come SPARE\" : (deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\")}\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaCavo') {\n      return (\n        <Dialog\n          open={openDialog}\n          onClose={handleCloseDialog}\n          maxWidth=\"md\"\n          fullWidth\n          PaperProps={{\n            sx: {\n              width: '80%',  // Allargato di 2cm (rispetto a sm che è circa 60%)\n              minHeight: '80vh', // Allungato di 1cm (aggiungendo altezza minima)\n              maxHeight: '90vh',\n              overflow: 'auto'\n            }\n          }}\n        >\n          <DialogTitle sx={{ pb: 1 }}>Modifica Cavo</DialogTitle>\n          <DialogContent sx={{ pt: 0, pb: 1 }}>\n            <Box sx={{ mt: 0 }}>\n              {selectedCavo ? (\n                <CavoForm\n                  mode=\"edit\"\n                  initialData={formData}\n                  cantiereId={cantiereId}\n                  onSubmit={async (validatedData) => {\n                    try {\n                      // Rimuovi i campi di sistema che non devono essere modificati\n                      const dataToSend = { ...validatedData };\n                      delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n                      delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n                      delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n                      delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n                      delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n                      // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n                      dataToSend.modificato_manualmente = 1;\n\n                      await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n                      return true;\n                    } catch (error) {\n                      throw error;\n                    }\n                  }}\n                  onSuccess={(message) => {\n                    onSuccess(message);\n                    handleCloseDialog();\n                  }}\n                  onError={onError}\n                  isDialog={true}\n                  onCancel={handleCloseDialog}\n                />\n              ) : (\n                <SelezionaCavoForm\n                  cantiereId={cantiereId}\n                  onSuccess={(message) => {\n                    onSuccess(message);\n                    handleCloseDialog();\n                  }}\n                  onError={onError}\n                  isDialog={true}\n                  onCancel={handleCloseDialog}\n                />\n              )}\n            </Box>\n          </DialogContent>\n          {/* No DialogActions needed here as CavoForm has its own buttons */}\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      {/* Menu a cascata nella sidebar */}\n      <Box sx={{ width: '280px', mr: 3 }}>\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Posa Cavi e Collegamenti\n          </Typography>\n          <Divider sx={{ mb: 2 }} />\n          <List component=\"nav\" dense>\n            <ListItemButton onClick={() => handleOptionSelect('inserisciMetri')}>\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"1. Inserisci metri posati\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaBobina')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"2. Modifica bobina cavo posato\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('aggiungiCavo')}>\n              <ListItemIcon>\n                <AddIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"3. Aggiungi nuovo cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaCavo')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"4. Modifica cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('eliminaCavo')}>\n              <ListItemIcon>\n                <DeleteIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"5. Elimina cavo\" />\n            </ListItemButton>\n          </List>\n        </Paper>\n      </Box>\n\n      {/* Area principale per il contenuto */}\n      <Box sx={{ flexGrow: 1 }}>\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption && (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu a sinistra per iniziare.\n            </Typography>\n          )}\n          {selectedOption && !openDialog && (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'inserisciMetri' && 'Inserisci metri posati'}\n                {selectedOption === 'modificaCavo' && 'Modifica cavo'}\n                {selectedOption === 'aggiungiCavo' && 'Aggiungi nuovo cavo'}\n                {selectedOption === 'eliminaCavo' && 'Elimina cavo'}\n                {selectedOption === 'modificaBobina' && 'Modifica bobina cavo posato'}\n              </Typography>\n              <Typography variant=\"body1\">\n                Caricamento in corso...\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      </Box>\n\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default PosaCaviCollegamenti;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,SAAS,QACJ,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AACtF,SAASC,wBAAwB,QAAQ,6BAA6B;AACtE,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,UAAU,EAAEC,cAAc;EAAEC,SAAS;EAAEC,OAAO;EAAEC,aAAa,GAAG,IAAI;EAAEC,eAAe,GAAG,IAAI;EAAEC,UAAU,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACrJ;EACAC,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAER,cAAc,CAAC;;EAE1E;EACA,MAAMS,QAAQ,GAAGvB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMa,UAAU,GAAGC,cAAc,IAAIU,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;EAC7FL,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAET,UAAU,CAAC;EACvE,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsE,cAAc,EAAEC,iBAAiB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACwE,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0E,UAAU,EAAEC,aAAa,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4E,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8E,QAAQ,EAAEC,WAAW,CAAC,GAAG/E,QAAQ,CAAC;IACvCgF,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqF,UAAU,EAAEC,aAAa,CAAC,GAAGtF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACuF,YAAY,EAAEC,eAAe,CAAC,GAAGxF,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACyF,IAAI,EAAEC,OAAO,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC2F,WAAW,EAAEC,cAAc,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6F,UAAU,EAAEC,aAAa,CAAC,GAAG9F,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEvD;EACAD,KAAK,CAACgG,SAAS,CAAC,MAAM;IACpB,IAAIrC,aAAa,EAAE;MACjB;MACA;MACAa,iBAAiB,CAACb,aAAa,CAAC;MAEhC,IAAIA,aAAa,KAAK,aAAa,EAAE;QACnC,IAAIC,eAAe,EAAE;UACnB;UACAkB,eAAe,CAAClB,eAAe,CAAC;UAChCgB,aAAa,CAAC,aAAa,CAAC;UAC5BF,aAAa,CAAC,IAAI,CAAC;QACrB,CAAC,MAAM;UACL;UACAuB,QAAQ,CAAC,aAAa,CAAC;UACvBrB,aAAa,CAAC,aAAa,CAAC;UAC5BF,aAAa,CAAC,IAAI,CAAC;QACrB;MACF,CAAC,MAAM,IAAIf,aAAa,KAAK,cAAc,EAAE;QAC3C,IAAIC,eAAe,EAAE;UACnB;UACAkB,eAAe,CAAClB,eAAe,CAAC;UAChCgB,aAAa,CAAC,cAAc,CAAC;UAC7BI,WAAW,CAAC;YACV,GAAGpB,eAAe;YAClBsC,aAAa,EAAEtC,eAAe,CAACsC,aAAa,IAAI,EAAE;YAClDC,eAAe,EAAEvC,eAAe,CAACuC,eAAe,IAAI;UACtD,CAAC,CAAC;UACFzB,aAAa,CAAC,IAAI,CAAC;QACrB,CAAC,MAAM;UACL;UACAuB,QAAQ,CAAC,cAAc,CAAC;UACxBrB,aAAa,CAAC,eAAe,CAAC;UAC9BF,aAAa,CAAC,IAAI,CAAC;QACrB;MACF,CAAC,MAAM,IAAIf,aAAa,KAAK,cAAc,EAAE;QAC3CiB,aAAa,CAAC,cAAc,CAAC;QAC7BF,aAAa,CAAC,IAAI,CAAC;MACrB;IACF;IACA;EACF,CAAC,EAAE,CAACf,aAAa,EAAEC,eAAe,CAAC,CAAC;;EAEpC;EACA,MAAMqC,QAAQ,GAAG,MAAOG,aAAa,IAAK;IACxC,IAAI;MACFP,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,IAAI,CAACtC,UAAU,EAAE;QACf,MAAM,IAAI8C,KAAK,CAAC,mCAAmC,CAAC;MACtD;MAEAtC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAET,UAAU,CAAC;MACzD,MAAM+C,QAAQ,GAAG,MAAM3D,WAAW,CAAC4D,OAAO,CAAChD,UAAU,EAAE,CAAC,CAAC;;MAEzD;MACA,IAAI6C,aAAa,KAAK,cAAc,EAAE;QACpC;QACA,MAAMI,aAAa,GAAGF,QAAQ,CAACG,MAAM,CAACC,IAAI,IACxCC,UAAU,CAACD,IAAI,CAACP,eAAe,CAAC,KAAK,CAAC,IACtCO,IAAI,CAACE,mBAAmB,KAAK,YAC/B,CAAC;QACDjB,OAAO,CAACa,aAAa,CAAC;MACxB,CAAC,MAAM;QACL;QACAb,OAAO,CAACW,QAAQ,CAAC;MACnB;IACF,CAAC,CAAC,OAAOO,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIC,YAAY,GAAG,iCAAiC;MAEpD,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClB;QACAD,YAAY,IAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE;QACzE,IAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,IAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;UACrDL,YAAY,IAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;QACpD;MACF,CAAC,MAAM,IAAIN,KAAK,CAACO,OAAO,EAAE;QACxB;QACAN,YAAY,IAAI,iEAAiE;MACnF,CAAC,MAAM,IAAID,KAAK,CAACQ,OAAO,EAAE;QACxB;QACAP,YAAY,IAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE;MACtC;MAEA3D,OAAO,CAACoD,YAAY,CAAC;IACvB,CAAC,SAAS;MACRjB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMyB,kBAAkB,GAAIC,MAAM,IAAK;IACrC/C,iBAAiB,CAAC+C,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,gBAAgB,IAAIA,MAAM,KAAK,gBAAgB,EAAE;MAC9DtB,QAAQ,CAACsB,MAAM,CAAC;MAChB3C,aAAa,CAAC2C,MAAM,CAAC;MACrB7C,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI6C,MAAM,KAAK,cAAc,EAAE;MACpC;MACA3C,aAAa,CAAC,cAAc,CAAC;MAC7BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI6C,MAAM,KAAK,cAAc,EAAE;MACpCtB,QAAQ,CAAC,cAAc,CAAC;MACxBrB,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI6C,MAAM,KAAK,aAAa,EAAE;MACnCtB,QAAQ,CAAC,aAAa,CAAC;MACvBrB,aAAa,CAAC,aAAa,CAAC;MAC5BF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM8C,iBAAiB,GAAGA,CAAA,KAAM;IAC9BzD,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC;IACAU,aAAa,CAAC,KAAK,CAAC;IACpBI,eAAe,CAAC,IAAI,CAAC;IACrBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,cAAc,CAAC,EAAE,CAAC;IAClBE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;IACnBM,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;IACxBzB,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;;IAEnB;IACA;IACA,IAAIX,aAAa,IAAIF,SAAS,EAAE;MAC9B;MACAA,SAAS,CAAC,IAAI,CAAC;IACjB;EACF,CAAC;;EAED;EACA,MAAMgE,gBAAgB,GAAIf,IAAI,IAAK;IACjC5B,eAAe,CAAC4B,IAAI,CAAC;IACrB,IAAI/B,UAAU,KAAK,gBAAgB,EAAE;MACnCK,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEyB,IAAI,CAACzB,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIP,UAAU,KAAK,gBAAgB,EAAE;MAC1CK,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEyB,IAAI,CAACzB,OAAO;QACrBE,SAAS,EAAEuB,IAAI,CAACvB,SAAS,IAAI;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIR,UAAU,KAAK,eAAe,EAAE;MACzCC,aAAa,CAAC,cAAc,CAAC;MAC7BE,eAAe,CAAC4B,IAAI,CAAC;MACrB1B,WAAW,CAAC;QACV,GAAG0B,IAAI;QACPR,aAAa,EAAEQ,IAAI,CAACR,aAAa,IAAI,EAAE;QACvCC,eAAe,EAAEO,IAAI,CAACP,eAAe,IAAI;MAC3C,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMuB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACtC,WAAW,CAACuC,IAAI,CAAC,CAAC,EAAE;MACvBjE,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFmC,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,IAAI,CAACtC,UAAU,EAAE;QACf,MAAM,IAAI8C,KAAK,CAAC,mCAAmC,CAAC;MACtD;MAEAtC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEoB,WAAW,EAAE,eAAe,EAAE7B,UAAU,CAAC;MAC7E,MAAM+C,QAAQ,GAAG,MAAM3D,WAAW,CAAC4D,OAAO,CAAChD,UAAU,EAAE,CAAC,CAAC;MACzD,MAAMmD,IAAI,GAAGJ,QAAQ,CAACsB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5C,OAAO,KAAKG,WAAW,CAACuC,IAAI,CAAC,CAAC,CAAC;MAEjE,IAAI,CAACjB,IAAI,EAAE;QACThD,OAAO,CAAC,eAAe0B,WAAW,cAAc,CAAC;QACjD;MACF;;MAEA;MACA,IAAIT,UAAU,KAAK,eAAe,EAAE;QAClC;QACA,IAAIgC,UAAU,CAACD,IAAI,CAACP,eAAe,CAAC,GAAG,CAAC,IAAIO,IAAI,CAACE,mBAAmB,KAAK,YAAY,EAAE;UACrFlD,OAAO,CAAC,WAAWgD,IAAI,CAACzB,OAAO,wFAAwF,CAAC;UACxH;QACF;MACF;;MAEA;MACAwC,gBAAgB,CAACf,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIC,YAAY,GAAG,iCAAiC;MAEpD,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClB;QACAD,YAAY,IAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE;QACzE,IAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,IAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;UACrDL,YAAY,IAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;QACpD;MACF,CAAC,MAAM,IAAIN,KAAK,CAACO,OAAO,EAAE;QACxB;QACAN,YAAY,IAAI,iEAAiE;MACnF,CAAC,MAAM,IAAID,KAAK,CAACQ,OAAO,EAAE;QACxB;QACAP,YAAY,IAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE;MACtC;MAEA3D,OAAO,CAACoD,YAAY,CAAC;IACvB,CAAC,SAAS;MACRjB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMiC,uBAAuB,GAAIC,CAAC,IAAK;IACrC1C,cAAc,CAAC0C,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAChC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIH,CAAC,IAAK;IAC9B,MAAM;MAAEI,IAAI;MAAEF;IAAM,CAAC,GAAGF,CAAC,CAACC,MAAM;;IAEhC;IACAhD,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACoD,IAAI,GAAGF;IACV,CAAC,CAAC;;IAEF;IACA,IAAItD,UAAU,KAAK,cAAc,EAAE;MACjC,MAAMyD,gBAAgB,GAAG,CAAC,CAAC;MAC3B,IAAID,IAAI,KAAK,iBAAiB,EAAE;QAC9BC,gBAAgB,CAACC,YAAY,GAAG1B,UAAU,CAAC5B,QAAQ,CAACmB,aAAa,IAAI,CAAC,CAAC;MACzE;MAEA,MAAMoC,MAAM,GAAGzF,aAAa,CAACsF,IAAI,EAAEF,KAAK,EAAEG,gBAAgB,CAAC;;MAE3D;MACA7C,aAAa,CAACgD,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACJ,IAAI,GAAG,CAACG,MAAM,CAACE,KAAK,GAAGF,MAAM,CAACjB,OAAO,GAAG;MAC3C,CAAC,CAAC,CAAC;;MAEH;MACA5B,eAAe,CAAC8C,IAAI,KAAK;QACvB,GAAGA,IAAI;QACP,CAACJ,IAAI,GAAGG,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACjB,OAAO,GAAG;MAC5C,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMqB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFpE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIK,UAAU,KAAK,gBAAgB,EAAE;QACnC;QACA,IAAI7B,OAAO,CAACiC,QAAQ,CAACG,YAAY,CAAC,IAAIyD,KAAK,CAAChC,UAAU,CAAC5B,QAAQ,CAACG,YAAY,CAAC,CAAC,EAAE;UAC9EK,aAAa,CAAC;YAAEL,YAAY,EAAE;UAAqC,CAAC,CAAC;UACrEZ,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEA,IAAI;UACF,MAAM3B,WAAW,CAACiG,iBAAiB,CACjCrF,UAAU,EACVwB,QAAQ,CAACE,OAAO,EAChB0B,UAAU,CAAC5B,QAAQ,CAACG,YAAY,CAClC,CAAC;UACD;UACAzB,SAAS,CAAC,sCAAsC,CAAC;UACjD;UACA+D,iBAAiB,CAAC,CAAC;UACnB;UACAqB,UAAU,CAAC,MAAM;YACf9F,wBAAwB,CAACkB,QAAQ,EAAE,IAAI,CAAC;UAC1C,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,CAAC,OAAO4C,KAAK,EAAE;UACd9C,OAAO,CAAC8C,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;UACzEnD,OAAO,CAAC,oDAAoD,IAAImD,KAAK,CAACQ,OAAO,IAAI,oBAAoB,CAAC,CAAC;UACvG/C,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,MAAM,IAAIK,UAAU,KAAK,gBAAgB,EAAE;QAC1C,IAAI;UACF,MAAMhC,WAAW,CAACmG,YAAY,CAC5BvF,UAAU,EACVwB,QAAQ,CAACE,OAAO,EAChBF,QAAQ,CAACI,SACX,CAAC;UACD;UACA1B,SAAS,CAAC,gCAAgC,CAAC;UAC3C;UACA+D,iBAAiB,CAAC,CAAC;UACnB;UACAqB,UAAU,CAAC,MAAM;YACf9F,wBAAwB,CAACkB,QAAQ,EAAE,IAAI,CAAC;UAC1C,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,CAAC,OAAO4C,KAAK,EAAE;UACd9C,OAAO,CAAC8C,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;UACrEnD,OAAO,CAAC,gDAAgD,IAAImD,KAAK,CAACQ,OAAO,IAAI,oBAAoB,CAAC,CAAC;UACnG/C,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,MAAM,IAAIK,UAAU,KAAK,cAAc,EAAE;QACxC;QACA,MAAMoE,UAAU,GAAGnG,gBAAgB,CAACmC,QAAQ,CAAC;QAE7C,IAAI,CAACgE,UAAU,CAACC,OAAO,EAAE;UACvBzD,aAAa,CAACwD,UAAU,CAACE,MAAM,CAAC;UAChCxD,eAAe,CAACsD,UAAU,CAACG,QAAQ,CAAC;UACpC5E,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAM6E,aAAa,GAAGJ,UAAU,CAACI,aAAa;;QAE9C;QACA,MAAMC,UAAU,GAAG;UAAE,GAAGD;QAAc,CAAC;QACvC,OAAOC,UAAU,CAACjE,SAAS,CAAC,CAAC;QAC7B,OAAOiE,UAAU,CAACjD,eAAe,CAAC,CAAC;QACnC,OAAOiD,UAAU,CAACC,sBAAsB,CAAC,CAAC;QAC1C,OAAOD,UAAU,CAACE,SAAS,CAAC,CAAC;QAC7B,OAAOF,UAAU,CAACxC,mBAAmB,CAAC,CAAC;;QAEvC;QACAwC,UAAU,CAACC,sBAAsB,GAAG,CAAC;QAErCtF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEoF,UAAU,CAAC;QAElD,IAAI;UACFrF,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEoF,UAAU,CAAC;UACvE,MAAMd,MAAM,GAAG,MAAM3F,WAAW,CAAC4G,UAAU,CAAChG,UAAU,EAAE6F,UAAU,CAACnE,OAAO,EAAEmE,UAAU,CAAC;UACvFrF,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEsE,MAAM,CAAC;;UAEnE;UACA7E,SAAS,CAAC,8BAA8B,CAAC;UACzC;UACA+D,iBAAiB,CAAC,CAAC;UACnB;UACAqB,UAAU,CAAC,MAAM;YACf9F,wBAAwB,CAACkB,QAAQ,EAAE,IAAI,CAAC;UAC1C,CAAC,EAAE,GAAG,CAAC;UACP;QACF,CAAC,CAAC,OAAO4C,KAAK,EAAE;UACd9C,OAAO,CAAC8C,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;;UAEjE;UACA,IAAIC,YAAY,GAAG,0CAA0C;UAE7D,IAAID,KAAK,CAACE,QAAQ,EAAE;YAClB;YACAD,YAAY,IAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE;YACzE,IAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,IAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;cACrDL,YAAY,IAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;YACpD;UACF,CAAC,MAAM,IAAIN,KAAK,CAACO,OAAO,EAAE;YACxB;YACAN,YAAY,IAAI,iEAAiE;;YAEjF;YACA;YACA/C,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC;YACpF;YACAP,SAAS,CAAC,yEAAyE,CAAC;YACpF;YACA+D,iBAAiB,CAAC,CAAC;YACnB;YACAqB,UAAU,CAAC,MAAM;cACf9F,wBAAwB,CAACkB,QAAQ,EAAE,IAAI,CAAC;YAC1C,CAAC,EAAE,GAAG,CAAC;YACP;UACF,CAAC,MAAM,IAAI4C,KAAK,CAACQ,OAAO,EAAE;YACxB;YACAP,YAAY,IAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE;;YAEpC;YACA,IAAIR,KAAK,CAACQ,OAAO,CAACmC,QAAQ,CAAC,2CAA2C,CAAC,EAAE;cACvEzF,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;cAC5EwD,iBAAiB,CAAC,CAAC;cACnB/D,SAAS,CAAC,yEAAyE,CAAC;cACpF;cACAV,wBAAwB,CAACkB,QAAQ,EAAE,IAAI,CAAC;cACxC;YACF;UACF;UAEAP,OAAO,CAACoD,YAAY,CAAC;UACrBxC,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAImF,MAAM,CAACC,IAAI,CAACX,UAAU,CAACG,QAAQ,CAAC,CAACS,MAAM,GAAG,CAAC,EAAE;UAC/C,MAAMC,eAAe,GAAGH,MAAM,CAACI,MAAM,CAACd,UAAU,CAACG,QAAQ,CAAC,CAACY,IAAI,CAAC,IAAI,CAAC;UACrE/F,OAAO,CAACgG,IAAI,CAAC,gCAAgC,EAAEH,eAAe,CAAC;QACjE;MACF,CAAC,MAAM,IAAIjF,UAAU,KAAK,aAAa,EAAE;QACvC;QACA,MAAMqF,WAAW,GAAGnF,YAAY,CAAC+B,mBAAmB,KAAK,YAAY,IAAK/B,YAAY,CAACsB,eAAe,IAAItB,YAAY,CAACsB,eAAe,GAAG,CAAE;QAE3I,IAAI6D,WAAW,EAAE;UACf;UACAjG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEa,YAAY,CAACI,OAAO,CAAC;UACzE,IAAI;YACF;YACA,MAAMqD,MAAM,GAAG,MAAM3F,WAAW,CAACsH,eAAe,CAAC1G,UAAU,EAAEsB,YAAY,CAACI,OAAO,CAAC;YAClFlB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEsE,MAAM,CAAC;YACjDvE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEsE,MAAM,CAACe,sBAAsB,CAAC;YAClF;YACA7B,iBAAiB,CAAC,CAAC;YACnB/D,SAAS,CAAC,QAAQoB,YAAY,CAACI,OAAO,kCAAkC,CAAC;YACzE;YACAlC,wBAAwB,CAACkB,QAAQ,EAAE,GAAG,CAAC;UACzC,CAAC,CAAC,OAAOiG,SAAS,EAAE;YAClBnG,OAAO,CAAC8C,KAAK,CAAC,kEAAkE,EAAEqD,SAAS,CAAC;YAC5F;YACA,MAAM5B,MAAM,GAAG,MAAM3F,WAAW,CAACwH,UAAU,CAAC5G,UAAU,EAAEsB,YAAY,CAACI,OAAO,EAAE,OAAO,CAAC;YACtFlB,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEsE,MAAM,CAAC;YAChE;YACAd,iBAAiB,CAAC,CAAC;YACnB/D,SAAS,CAAC,QAAQoB,YAAY,CAACI,OAAO,kCAAkC,CAAC;UAC3E;QACF,CAAC,MAAM;UACL;UACAlB,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE8B,UAAU,CAAC;UACvE,MAAMwC,MAAM,GAAG,MAAM3F,WAAW,CAACwH,UAAU,CAAC5G,UAAU,EAAEsB,YAAY,CAACI,OAAO,EAAEa,UAAU,CAAC;UACzF/B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEsE,MAAM,CAAC;UACxD;UACAd,iBAAiB,CAAC,CAAC;UACnB/D,SAAS,CAAC,QAAQoB,YAAY,CAACI,OAAO,IAAIa,UAAU,KAAK,OAAO,GAAG,oBAAoB,GAAG,WAAW,eAAe,CAAC;QACvH;MACF;;MAEA;MACA;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;MAErD;MACA,IAAIC,YAAY,GAAG,oBAAoB;MAEvC,IAAID,KAAK,CAACM,MAAM,EAAE;QAChB;QACAL,YAAY,GAAGD,KAAK,CAACM,MAAM;MAC7B,CAAC,MAAM,IAAIN,KAAK,CAACQ,OAAO,EAAE;QACxB;QACAP,YAAY,GAAGD,KAAK,CAACQ,OAAO;MAC9B,CAAC,MAAM,IAAI,OAAOR,KAAK,KAAK,QAAQ,EAAE;QACpC;QACAC,YAAY,GAAGD,KAAK;MACtB;MAEAnD,OAAO,CAAC,gCAAgC,GAAGoD,YAAY,CAAC;IAC1D,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8F,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIzF,UAAU,KAAK,cAAc,EAAE;MACjC,oBACExB,OAAA,CAACvC,MAAM;QACLyJ,IAAI,EAAE5F,UAAW;QACjB6F,OAAO,EAAE9C,iBAAkB;QAC3B+C,QAAQ,EAAC,IAAI;QACbC,SAAS;QACTC,UAAU,EAAE;UACVC,EAAE,EAAE;YACFC,KAAK,EAAE,KAAK;YAAG;YACfC,SAAS,EAAE,MAAM;YAAE;YACnBC,SAAS,EAAE,MAAM;YACjBC,QAAQ,EAAE;UACZ;QACF,CAAE;QAAAC,QAAA,gBAEF5H,OAAA,CAACtC,WAAW;UAAC6J,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC7DjI,OAAA,CAACrC,aAAa;UAAC4J,EAAE,EAAE;YAAEW,EAAE,EAAE,CAAC;YAAEL,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,eAClC5H,OAAA,CAACjD,GAAG;YAACwK,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,eACjB5H,OAAA,CAACH,QAAQ;cACPuI,IAAI,EAAC,KAAK;cACVhI,UAAU,EAAEA,UAAW;cACvBiI,QAAQ,EAAE,MAAOrC,aAAa,IAAK;gBACjC,IAAI;kBACF,MAAMxG,WAAW,CAAC8I,UAAU,CAAClI,UAAU,EAAE4F,aAAa,CAAC;kBACvD,OAAO,IAAI;gBACb,CAAC,CAAC,OAAOtC,KAAK,EAAE;kBACd,MAAMA,KAAK;gBACb;cACF,CAAE;cACFpD,SAAS,EAAG4D,OAAO,IAAK;gBACtB5D,SAAS,CAAC4D,OAAO,CAAC;gBAClBG,iBAAiB,CAAC,CAAC;cACrB,CAAE;cACF9D,OAAO,EAAEA,OAAQ;cACjBgI,QAAQ,EAAE,IAAK;cACfC,QAAQ,EAAEnE;YAAkB;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CAAC;IAEb,CAAC,MAAM,IAAIzG,UAAU,KAAK,gBAAgB,EAAE;MAC1C,oBACExB,OAAA,CAACvC,MAAM;QAACyJ,IAAI,EAAE5F,UAAW;QAAC6F,OAAO,EAAE9C,iBAAkB;QAAC+C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAO,QAAA,gBAC3E5H,OAAA,CAACtC,WAAW;UAAAkK,QAAA,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACjDjI,OAAA,CAACrC,aAAa;UAAAiK,QAAA,EACXnF,WAAW,gBACVzC,OAAA,CAAC3B,gBAAgB;YAAAyJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB1F,IAAI,CAACiE,MAAM,KAAK,CAAC,gBACnBxG,OAAA,CAAC5B,KAAK;YAACqK,QAAQ,EAAC,MAAM;YAAAb,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAACvG,YAAY,gBACf1B,OAAA,CAACjD,GAAG;YAAA6K,QAAA,gBACF5H,OAAA,CAAChD,UAAU;cAAC0L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAf,QAAA,EAAC;YAE7C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjI,OAAA,CAAC5C,IAAI;cAAAwK,QAAA,EACFrF,IAAI,CAACqG,GAAG,CAAErF,IAAI,iBACbvD,OAAA,CAAC3C,QAAQ;gBACPwL,MAAM;gBAENC,OAAO,EAAEA,CAAA,KAAMxE,gBAAgB,CAACf,IAAI,CAAE;gBAAAqE,QAAA,eAEtC5H,OAAA,CAAC1C,YAAY;kBACXyL,OAAO,EAAExF,IAAI,CAACzB,OAAQ;kBACtBkH,SAAS,EAAE,GAAGzF,IAAI,CAAC0F,SAAS,IAAI,KAAK,UAAU1F,IAAI,CAAC2F,mBAAmB,IAAI,KAAK,OAAO3F,IAAI,CAAC4F,iBAAiB,IAAI,KAAK;gBAAG;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC,GANG1E,IAAI,CAACzB,OAAO;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAENjI,OAAA,CAACjD,GAAG;YAACwK,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBACjB5H,OAAA,CAAChD,UAAU;cAAC0L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAf,QAAA,GAAC,oBACzB,EAAClG,YAAY,CAACI,OAAO;YAAA;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACbjI,OAAA,CAAChD,UAAU;cAAC0L,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAf,QAAA,GAAC,iBACxB,EAAClG,YAAY,CAACqB,aAAa,IAAI,KAAK;YAAA;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACbjI,OAAA,CAAChD,UAAU;cAAC0L,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAf,QAAA,GAAC,qBACpB,EAAClG,YAAY,CAACsB,eAAe,IAAI,GAAG;YAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACbjI,OAAA,CAAClC,SAAS;cACRsL,MAAM,EAAC,OAAO;cACdpE,IAAI,EAAC,cAAc;cACnBqE,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,QAAQ;cACbjC,SAAS;cACTqB,OAAO,EAAC,UAAU;cAClB5D,KAAK,EAAElD,QAAQ,CAACG,YAAa;cAC7BwH,QAAQ,EAAExE,gBAAiB;cAC3ByE,QAAQ;cACR9F,KAAK,EAAE,CAAC,CAACvB,UAAU,CAACJ,YAAa;cACjC0H,UAAU,EAAEtH,UAAU,CAACJ,YAAa;cACpCwF,EAAE,EAAE;gBAAEY,EAAE,EAAE;cAAE;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBjI,OAAA,CAACnC,aAAa;UAAA+J,QAAA,gBACZ5H,OAAA,CAAC/C,MAAM;YAAC6L,OAAO,EAAEzE,iBAAkB;YAAAuD,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDvG,YAAY,iBACX1B,OAAA,CAAC/C,MAAM;YACL6L,OAAO,EAAEvD,UAAW;YACpBmE,QAAQ,EAAExI,OAAO,IAAI,CAACU,QAAQ,CAACG,YAAa;YAC5C4H,SAAS,EAAEzI,OAAO,gBAAGlB,OAAA,CAAC3B,gBAAgB;cAACuL,IAAI,EAAE;YAAG;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGjI,OAAA,CAACZ,QAAQ;cAAA0I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAL,QAAA,EACpE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIzG,UAAU,KAAK,gBAAgB,EAAE;MAC1C,oBACExB,OAAA,CAACvC,MAAM;QAACyJ,IAAI,EAAE5F,UAAW;QAAC6F,OAAO,EAAE9C,iBAAkB;QAAC+C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAO,QAAA,gBAC3E5H,OAAA,CAACtC,WAAW;UAAAkK,QAAA,EAAC;QAA2B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACtDjI,OAAA,CAACrC,aAAa;UAAAiK,QAAA,EACXnF,WAAW,gBACVzC,OAAA,CAAC3B,gBAAgB;YAAAyJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB1F,IAAI,CAACiE,MAAM,KAAK,CAAC,gBACnBxG,OAAA,CAAC5B,KAAK;YAACqK,QAAQ,EAAC,MAAM;YAAAb,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAACvG,YAAY,gBACf1B,OAAA,CAACjD,GAAG;YAAA6K,QAAA,gBACF5H,OAAA,CAAChD,UAAU;cAAC0L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAf,QAAA,EAAC;YAE7C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjI,OAAA,CAAC5C,IAAI;cAAAwK,QAAA,EACFrF,IAAI,CAACqG,GAAG,CAAErF,IAAI,iBACbvD,OAAA,CAAC3C,QAAQ;gBACPwL,MAAM;gBAENC,OAAO,EAAEA,CAAA,KAAMxE,gBAAgB,CAACf,IAAI,CAAE;gBAAAqE,QAAA,eAEtC5H,OAAA,CAAC1C,YAAY;kBACXyL,OAAO,EAAExF,IAAI,CAACzB,OAAQ;kBACtBkH,SAAS,EAAE,mBAAmBzF,IAAI,CAACvB,SAAS,GAAIuB,IAAI,CAACvB,SAAS,KAAK,cAAc,GAAG,cAAc,GAAGuB,IAAI,CAACvB,SAAS,GAAI,cAAc;gBAAG;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzI;cAAC,GANG1E,IAAI,CAACzB,OAAO;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAENjI,OAAA,CAACjD,GAAG;YAACwK,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBACjB5H,OAAA,CAAChD,UAAU;cAAC0L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAf,QAAA,GAAC,oBACzB,EAAClG,YAAY,CAACI,OAAO;YAAA;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACbjI,OAAA,CAAChD,UAAU;cAAC0L,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAf,QAAA,GAAC,kBACvB,EAAClG,YAAY,CAACM,SAAS,GAAIN,YAAY,CAACM,SAAS,KAAK,cAAc,GAAG,cAAc,GAAGN,YAAY,CAACM,SAAS,GAAI,cAAc;YAAA;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtI,CAAC,eACbjI,OAAA,CAAClC,SAAS;cACRsL,MAAM,EAAC,OAAO;cACdpE,IAAI,EAAC,WAAW;cAChBqE,KAAK,EAAC,WAAW;cACjBhC,SAAS;cACTqB,OAAO,EAAC,UAAU;cAClB5D,KAAK,EAAElD,QAAQ,CAACI,SAAU;cAC1BuH,QAAQ,EAAExE,gBAAiB;cAC3BwC,EAAE,EAAE;gBAAEY,EAAE,EAAE;cAAE;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBjI,OAAA,CAACnC,aAAa;UAAA+J,QAAA,gBACZ5H,OAAA,CAAC/C,MAAM;YAAC6L,OAAO,EAAEzE,iBAAkB;YAAAuD,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDvG,YAAY,iBACX1B,OAAA,CAAC/C,MAAM;YACL6L,OAAO,EAAEvD,UAAW;YACpBmE,QAAQ,EAAExI,OAAQ;YAClByI,SAAS,EAAEzI,OAAO,gBAAGlB,OAAA,CAAC3B,gBAAgB;cAACuL,IAAI,EAAE;YAAG;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGjI,OAAA,CAACZ,QAAQ;cAAA0I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAL,QAAA,EACpE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIzG,UAAU,KAAK,eAAe,EAAE;MACzC,oBACExB,OAAA,CAACvC,MAAM;QAACyJ,IAAI,EAAE5F,UAAW;QAAC6F,OAAO,EAAE9C,iBAAkB;QAAC+C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAO,QAAA,gBAC3E5H,OAAA,CAACtC,WAAW;UAAAkK,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxCjI,OAAA,CAACrC,aAAa;UAAAiK,QAAA,gBACZ5H,OAAA,CAAC5B,KAAK;YAACqK,QAAQ,EAAC,MAAM;YAAClB,EAAE,EAAE;cAAEsC,EAAE,EAAE;YAAE,CAAE;YAAAjC,QAAA,EAAC;UAGtC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAEPxF,WAAW,gBACVzC,OAAA,CAAC3B,gBAAgB;YAAAyJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB,CAACvG,YAAY,gBACf1B,OAAA,CAACjD,GAAG;YAACwK,EAAE,EAAE;cAAEuC,CAAC,EAAE;YAAE,CAAE;YAAAlC,QAAA,gBAChB5H,OAAA,CAAChD,UAAU;cAAC0L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAf,QAAA,EAAC;YAE7C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjI,OAAA,CAACjD,GAAG;cAACwK,EAAE,EAAE;gBAAEwC,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAE7B,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBACxD5H,OAAA,CAAClC,SAAS;gBACRuJ,SAAS;gBACTgC,KAAK,EAAC,SAAS;gBACfX,OAAO,EAAC,UAAU;gBAClB5D,KAAK,EAAE7C,WAAY;gBACnBsH,QAAQ,EAAE5E,uBAAwB;gBAClCsF,WAAW,EAAC;cAAyB;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACFjI,OAAA,CAAC/C,MAAM;gBACLyL,OAAO,EAAC,WAAW;gBACnBwB,KAAK,EAAC,SAAS;gBACfpB,OAAO,EAAEvE,oBAAqB;gBAC9BmF,QAAQ,EAAEjH,WAAW,IAAI,CAACR,WAAW,CAACuC,IAAI,CAAC,CAAE;gBAC7C+C,EAAE,EAAE;kBAAE4C,EAAE,EAAE,CAAC;kBAAEC,QAAQ,EAAE;gBAAQ,CAAE;gBAAAxC,QAAA,EAEhCnF,WAAW,gBAAGzC,OAAA,CAAC3B,gBAAgB;kBAACuL,IAAI,EAAE;gBAAG;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG;cAAO;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAChBjI,OAAA,CAACnC,aAAa;UAAA+J,QAAA,eACZ5H,OAAA,CAAC/C,MAAM;YAAC6L,OAAO,EAAEzE,iBAAkB;YAAAuD,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIzG,UAAU,KAAK,aAAa,EAAE;MACvC;MACA,MAAMqF,WAAW,GAAGnF,YAAY,KAAKA,YAAY,CAAC+B,mBAAmB,KAAK,YAAY,IAAK/B,YAAY,CAACsB,eAAe,IAAItB,YAAY,CAACsB,eAAe,GAAG,CAAE,CAAC;MAE7J,oBACEhD,OAAA,CAACvC,MAAM;QAACyJ,IAAI,EAAE5F,UAAW;QAAC6F,OAAO,EAAE9C,iBAAkB;QAAC+C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAO,QAAA,gBAC3E5H,OAAA,CAACtC,WAAW;UAAAkK,QAAA,EACT,CAAClG,YAAY,GAAG,cAAc,GAC9BmF,WAAW,GAAG,uBAAuB,GAAG;QAAc;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACdjI,OAAA,CAACrC,aAAa;UAAAiK,QAAA,EACXnF,WAAW,gBACVzC,OAAA,CAAC3B,gBAAgB;YAAAyJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB,CAACvG,YAAY,gBACf1B,OAAA,CAACjD,GAAG;YAACwK,EAAE,EAAE;cAAEuC,CAAC,EAAE;YAAE,CAAE;YAAAlC,QAAA,gBAChB5H,OAAA,CAAChD,UAAU;cAAC0L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAf,QAAA,EAAC;YAE7C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjI,OAAA,CAACjD,GAAG;cAACwK,EAAE,EAAE;gBAAEwC,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAE7B,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBACxD5H,OAAA,CAAClC,SAAS;gBACRuJ,SAAS;gBACTgC,KAAK,EAAC,SAAS;gBACfX,OAAO,EAAC,UAAU;gBAClB5D,KAAK,EAAE7C,WAAY;gBACnBsH,QAAQ,EAAE5E,uBAAwB;gBAClCsF,WAAW,EAAC;cAAyB;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACFjI,OAAA,CAAC/C,MAAM;gBACLyL,OAAO,EAAC,WAAW;gBACnBwB,KAAK,EAAC,SAAS;gBACfpB,OAAO,EAAEvE,oBAAqB;gBAC9BmF,QAAQ,EAAEjH,WAAW,IAAI,CAACR,WAAW,CAACuC,IAAI,CAAC,CAAE;gBAC7C+C,EAAE,EAAE;kBAAE4C,EAAE,EAAE,CAAC;kBAAEC,QAAQ,EAAE;gBAAQ,CAAE;gBAAAxC,QAAA,EAEhCnF,WAAW,gBAAGzC,OAAA,CAAC3B,gBAAgB;kBAACuL,IAAI,EAAE;gBAAG;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG;cAAO;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJzG,UAAU,KAAK,aAAa,IAAIqF,WAAW,gBAC7C7G,OAAA,CAAAE,SAAA;YAAA0H,QAAA,gBACE5H,OAAA,CAACpC,iBAAiB;cAAAgK,QAAA,GAAC,UACT,eAAA5H,OAAA;gBAAA4H,QAAA,EAASlG,YAAY,CAACI;cAAO;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,8CAC/C,EAACvG,YAAY,CAACsB,eAAe,GAAG,CAAC,iBAC/BhD,OAAA,CAAAE,SAAA;gBAAA0H,QAAA,GAAE,iBAAe,eAAA5H,OAAA;kBAAA4H,QAAA,GAASlG,YAAY,CAACsB,eAAe,EAAC,IAAE;gBAAA;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC;cAAA,eAAE,CACtE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACgB,CAAC,eACpBjI,OAAA,CAACpC,iBAAiB;cAAC2J,EAAE,EAAE;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,EAAC;YAElC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC;UAAA,eACpB,CAAC,GACDzG,UAAU,KAAK,aAAa,gBAC9BxB,OAAA,CAAAE,SAAA;YAAA0H,QAAA,gBACE5H,OAAA,CAACpC,iBAAiB;cAAAgK,QAAA,GAAC,6BACU,eAAA5H,OAAA;gBAAA4H,QAAA,EAASlG,YAAY,CAACI;cAAO;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KACpE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAEpBjI,OAAA,CAACjC,WAAW;cAACsM,SAAS,EAAC,UAAU;cAAC9C,EAAE,EAAE;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBAC9C5H,OAAA,CAACtB,SAAS;gBAAC2L,SAAS,EAAC,QAAQ;gBAAAzC,QAAA,EAAC;cAAgC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1EjI,OAAA,CAACxB,UAAU;gBACTsG,KAAK,EAAEnC,UAAW;gBAClB4G,QAAQ,EAAG3E,CAAC,IAAKhC,aAAa,CAACgC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAAA8C,QAAA,gBAE/C5H,OAAA,CAACvB,gBAAgB;kBACfqG,KAAK,EAAC,OAAO;kBACbwF,OAAO,eAAEtK,OAAA,CAACzB,KAAK;oBAAAuJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBoB,KAAK,EAAC;gBAAqF;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC,eACFjI,OAAA,CAACvB,gBAAgB;kBACfqG,KAAK,EAAC,QAAQ;kBACdwF,OAAO,eAAEtK,OAAA,CAACzB,KAAK;oBAAAuJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBoB,KAAK,EAAC;gBAAsE;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA,eACd,CAAC,GACD;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAChBjI,OAAA,CAACnC,aAAa;UAAA+J,QAAA,gBACZ5H,OAAA,CAAC/C,MAAM;YAAC6L,OAAO,EAAEzE,iBAAkB;YAAAuD,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDzG,UAAU,KAAK,aAAa,IAAIE,YAAY,iBAC3C1B,OAAA,CAAC/C,MAAM;YACL6L,OAAO,EAAEvD,UAAW;YACpBmE,QAAQ,EAAExI,OAAQ;YAClBgJ,KAAK,EAAErD,WAAW,GAAG,SAAS,GAAG,OAAQ;YACzC8C,SAAS,EAAEzI,OAAO,gBAAGlB,OAAA,CAAC3B,gBAAgB;cAACuL,IAAI,EAAE;YAAG;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAGpB,WAAW,gBAAG7G,OAAA,CAACV,WAAW;cAAAwI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGjI,OAAA,CAAChB,UAAU;cAAA8I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAL,QAAA,EAEpGf,WAAW,GAAG,kBAAkB,GAAIlE,UAAU,KAAK,OAAO,GAAG,kBAAkB,GAAG;UAA0B;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIzG,UAAU,KAAK,cAAc,EAAE;MACxC,oBACExB,OAAA,CAACvC,MAAM;QACLyJ,IAAI,EAAE5F,UAAW;QACjB6F,OAAO,EAAE9C,iBAAkB;QAC3B+C,QAAQ,EAAC,IAAI;QACbC,SAAS;QACTC,UAAU,EAAE;UACVC,EAAE,EAAE;YACFC,KAAK,EAAE,KAAK;YAAG;YACfC,SAAS,EAAE,MAAM;YAAE;YACnBC,SAAS,EAAE,MAAM;YACjBC,QAAQ,EAAE;UACZ;QACF,CAAE;QAAAC,QAAA,gBAEF5H,OAAA,CAACtC,WAAW;UAAC6J,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACvDjI,OAAA,CAACrC,aAAa;UAAC4J,EAAE,EAAE;YAAEW,EAAE,EAAE,CAAC;YAAEL,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,eAClC5H,OAAA,CAACjD,GAAG;YAACwK,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAChBlG,YAAY,gBACX1B,OAAA,CAACH,QAAQ;cACPuI,IAAI,EAAC,MAAM;cACXmC,WAAW,EAAE3I,QAAS;cACtBxB,UAAU,EAAEA,UAAW;cACvBiI,QAAQ,EAAE,MAAOrC,aAAa,IAAK;gBACjC,IAAI;kBACF;kBACA,MAAMC,UAAU,GAAG;oBAAE,GAAGD;kBAAc,CAAC;kBACvC,OAAOC,UAAU,CAACjE,SAAS,CAAC,CAAC;kBAC7B,OAAOiE,UAAU,CAACjD,eAAe,CAAC,CAAC;kBACnC,OAAOiD,UAAU,CAACC,sBAAsB,CAAC,CAAC;kBAC1C,OAAOD,UAAU,CAACE,SAAS,CAAC,CAAC;kBAC7B,OAAOF,UAAU,CAACxC,mBAAmB,CAAC,CAAC;;kBAEvC;kBACAwC,UAAU,CAACC,sBAAsB,GAAG,CAAC;kBAErC,MAAM1G,WAAW,CAAC4G,UAAU,CAAChG,UAAU,EAAE6F,UAAU,CAACnE,OAAO,EAAEmE,UAAU,CAAC;kBACxE,OAAO,IAAI;gBACb,CAAC,CAAC,OAAOvC,KAAK,EAAE;kBACd,MAAMA,KAAK;gBACb;cACF,CAAE;cACFpD,SAAS,EAAG4D,OAAO,IAAK;gBACtB5D,SAAS,CAAC4D,OAAO,CAAC;gBAClBG,iBAAiB,CAAC,CAAC;cACrB,CAAE;cACF9D,OAAO,EAAEA,OAAQ;cACjBgI,QAAQ,EAAE,IAAK;cACfC,QAAQ,EAAEnE;YAAkB;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,gBAEFjI,OAAA,CAACF,iBAAiB;cAChBM,UAAU,EAAEA,UAAW;cACvBE,SAAS,EAAG4D,OAAO,IAAK;gBACtB5D,SAAS,CAAC4D,OAAO,CAAC;gBAClBG,iBAAiB,CAAC,CAAC;cACrB,CAAE;cACF9D,OAAO,EAAEA,OAAQ;cACjBgI,QAAQ,EAAE,IAAK;cACfC,QAAQ,EAAEnE;YAAkB;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACEjI,OAAA,CAACjD,GAAG;IAACwK,EAAE,EAAE;MAAEwC,OAAO,EAAE;IAAO,CAAE;IAAAnC,QAAA,gBAE3B5H,OAAA,CAACjD,GAAG;MAACwK,EAAE,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEgD,EAAE,EAAE;MAAE,CAAE;MAAA5C,QAAA,eACjC5H,OAAA,CAAC9C,KAAK;QAACqK,EAAE,EAAE;UAAEuC,CAAC,EAAE,CAAC;UAAED,EAAE,EAAE;QAAE,CAAE;QAAAjC,QAAA,gBACzB5H,OAAA,CAAChD,UAAU;UAAC0L,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAf,QAAA,EAAC;QAEtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjI,OAAA,CAAC7C,OAAO;UAACoK,EAAE,EAAE;YAAEsC,EAAE,EAAE;UAAE;QAAE;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BjI,OAAA,CAAC5C,IAAI;UAACiN,SAAS,EAAC,KAAK;UAACI,KAAK;UAAA7C,QAAA,gBACzB5H,OAAA,CAACxC,cAAc;YAACsL,OAAO,EAAEA,CAAA,KAAM3E,kBAAkB,CAAC,gBAAgB,CAAE;YAAAyD,QAAA,gBAClE5H,OAAA,CAACzC,YAAY;cAAAqK,QAAA,eACX5H,OAAA,CAACd,SAAS;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACfjI,OAAA,CAAC1C,YAAY;cAACyL,OAAO,EAAC;YAA2B;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAEjBjI,OAAA,CAACxC,cAAc;YAACsL,OAAO,EAAEA,CAAA,KAAM3E,kBAAkB,CAAC,gBAAgB,CAAE;YAAAyD,QAAA,gBAClE5H,OAAA,CAACzC,YAAY;cAAAqK,QAAA,eACX5H,OAAA,CAAClB,QAAQ;gBAAAgJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACfjI,OAAA,CAAC1C,YAAY;cAACyL,OAAO,EAAC;YAAgC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEjBjI,OAAA,CAACxC,cAAc;YAACsL,OAAO,EAAEA,CAAA,KAAM3E,kBAAkB,CAAC,cAAc,CAAE;YAAAyD,QAAA,gBAChE5H,OAAA,CAACzC,YAAY;cAAAqK,QAAA,eACX5H,OAAA,CAACpB,OAAO;gBAAAkJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACfjI,OAAA,CAAC1C,YAAY;cAACyL,OAAO,EAAC;YAAwB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAEjBjI,OAAA,CAACxC,cAAc;YAACsL,OAAO,EAAEA,CAAA,KAAM3E,kBAAkB,CAAC,cAAc,CAAE;YAAAyD,QAAA,gBAChE5H,OAAA,CAACzC,YAAY;cAAAqK,QAAA,eACX5H,OAAA,CAAClB,QAAQ;gBAAAgJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACfjI,OAAA,CAAC1C,YAAY;cAACyL,OAAO,EAAC;YAAkB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAEjBjI,OAAA,CAACxC,cAAc;YAACsL,OAAO,EAAEA,CAAA,KAAM3E,kBAAkB,CAAC,aAAa,CAAE;YAAAyD,QAAA,gBAC/D5H,OAAA,CAACzC,YAAY;cAAAqK,QAAA,eACX5H,OAAA,CAAChB,UAAU;gBAAA8I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACfjI,OAAA,CAAC1C,YAAY;cAACyL,OAAO,EAAC;YAAiB;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNjI,OAAA,CAACjD,GAAG;MAACwK,EAAE,EAAE;QAAEmD,QAAQ,EAAE;MAAE,CAAE;MAAA9C,QAAA,eACvB5H,OAAA,CAAC9C,KAAK;QAACqK,EAAE,EAAE;UAAEuC,CAAC,EAAE,CAAC;UAAErC,SAAS,EAAE,OAAO;UAAEsC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEW,cAAc,EAAE;QAAS,CAAE;QAAA/C,QAAA,GACtG,CAACxG,cAAc,iBACdpB,OAAA,CAAChD,UAAU;UAAC0L,OAAO,EAAC,OAAO;UAAAd,QAAA,EAAC;QAE5B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb,EACA7G,cAAc,IAAI,CAACE,UAAU,iBAC5BtB,OAAA,CAACjD,GAAG;UAACwK,EAAE,EAAE;YAAEqD,SAAS,EAAE;UAAS,CAAE;UAAAhD,QAAA,gBAC/B5H,OAAA,CAAChD,UAAU;YAAC0L,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAf,QAAA,GAClCxG,cAAc,KAAK,gBAAgB,IAAI,wBAAwB,EAC/DA,cAAc,KAAK,cAAc,IAAI,eAAe,EACpDA,cAAc,KAAK,cAAc,IAAI,qBAAqB,EAC1DA,cAAc,KAAK,aAAa,IAAI,cAAc,EAClDA,cAAc,KAAK,gBAAgB,IAAI,6BAA6B;UAAA;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACbjI,OAAA,CAAChD,UAAU;YAAC0L,OAAO,EAAC,OAAO;YAAAd,QAAA,EAAC;UAE5B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjI,OAAA,CAAC3B,gBAAgB;YAACkJ,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELhB,YAAY,CAAC,CAAC;EAAA;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAACtH,EAAA,CAz8BIR,oBAAoB;EAAA,QAKPZ,WAAW;AAAA;AAAAsL,EAAA,GALxB1K,oBAAoB;AA28B1B,eAAeA,oBAAoB;AAAC,IAAA0K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}