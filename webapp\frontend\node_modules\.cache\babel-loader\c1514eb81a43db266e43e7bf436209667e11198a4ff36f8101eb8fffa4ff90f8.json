{"ast": null, "code": "import { startOfDay } from \"./startOfDay.mjs\";\n\n/**\n * @name startOfToday\n * @category Day Helpers\n * @summary Return the start of today.\n * @pure false\n *\n * @description\n * Return the start of today.\n *\n * @returns The start of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfToday()\n * //=> Mon Oct 6 2014 00:00:00\n */\nexport function startOfToday() {\n  return startOfDay(Date.now());\n}\n\n// Fallback for modularized imports:\nexport default startOfToday;", "map": {"version": 3, "names": ["startOfDay", "startOfToday", "Date", "now"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/startOfToday.mjs"], "sourcesContent": ["import { startOfDay } from \"./startOfDay.mjs\";\n\n/**\n * @name startOfToday\n * @category Day Helpers\n * @summary Return the start of today.\n * @pure false\n *\n * @description\n * Return the start of today.\n *\n * @returns The start of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfToday()\n * //=> Mon Oct 6 2014 00:00:00\n */\nexport function startOfToday() {\n  return startOfDay(Date.now());\n}\n\n// Fallback for modularized imports:\nexport default startOfToday;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAAA,EAAG;EAC7B,OAAOD,UAAU,CAACE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;AAC/B;;AAEA;AACA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}