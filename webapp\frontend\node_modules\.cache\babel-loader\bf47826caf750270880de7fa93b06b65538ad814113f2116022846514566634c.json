{"ast": null, "code": "import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useControlled from '@mui/utils/useControlled';\nimport { useUtils } from \"./useUtils.js\";\n/**\n * Hooks controlling the value while making sure that:\n * - The value returned by `onChange` always have the timezone of `props.value` or `props.defaultValue` if defined\n * - The value rendered is always the one from `props.timezone` if defined\n */\nexport const useControlledValue = ({\n  name,\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  referenceDate,\n  onChange: onChangeProp,\n  valueManager\n}) => {\n  const utils = useUtils();\n  const [valueWithInputTimezone, setValue] = useControlled({\n    name,\n    state: 'value',\n    controlled: valueProp,\n    default: defaultValue ?? valueManager.emptyValue\n  });\n  const inputTimezone = React.useMemo(() => valueManager.getTimezone(utils, valueWithInputTimezone), [utils, valueManager, valueWithInputTimezone]);\n  const setInputTimezone = useEventCallback(newValue => {\n    if (inputTimezone == null) {\n      return newValue;\n    }\n    return valueManager.setTimezone(utils, inputTimezone, newValue);\n  });\n  const timezoneToRender = React.useMemo(() => {\n    if (timezoneProp) {\n      return timezoneProp;\n    }\n    if (inputTimezone) {\n      return inputTimezone;\n    }\n    if (referenceDate) {\n      return utils.getTimezone(referenceDate);\n    }\n    return 'default';\n  }, [timezoneProp, inputTimezone, referenceDate, utils]);\n  const valueWithTimezoneToRender = React.useMemo(() => valueManager.setTimezone(utils, timezoneToRender, valueWithInputTimezone), [valueManager, utils, timezoneToRender, valueWithInputTimezone]);\n  const handleValueChange = useEventCallback((newValue, ...otherParams) => {\n    const newValueWithInputTimezone = setInputTimezone(newValue);\n    setValue(newValueWithInputTimezone);\n    onChangeProp?.(newValueWithInputTimezone, ...otherParams);\n  });\n  return {\n    value: valueWithTimezoneToRender,\n    handleValueChange,\n    timezone: timezoneToRender\n  };\n};", "map": {"version": 3, "names": ["React", "useEventCallback", "useControlled", "useUtils", "useControlledValue", "name", "timezone", "timezoneProp", "value", "valueProp", "defaultValue", "referenceDate", "onChange", "onChangeProp", "valueManager", "utils", "valueWithInputTimezone", "setValue", "state", "controlled", "default", "emptyValue", "inputTimezone", "useMemo", "getTimezone", "setInputTimezone", "newValue", "setTimezone", "timezoneToRender", "valueWithTimezoneToRender", "handleValueChange", "otherParams", "newValueWithInputTimezone"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/hooks/useControlledValue.js"], "sourcesContent": ["import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useControlled from '@mui/utils/useControlled';\nimport { useUtils } from \"./useUtils.js\";\n/**\n * Hooks controlling the value while making sure that:\n * - The value returned by `onChange` always have the timezone of `props.value` or `props.defaultValue` if defined\n * - The value rendered is always the one from `props.timezone` if defined\n */\nexport const useControlledValue = ({\n  name,\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  referenceDate,\n  onChange: onChangeProp,\n  valueManager\n}) => {\n  const utils = useUtils();\n  const [valueWithInputTimezone, setValue] = useControlled({\n    name,\n    state: 'value',\n    controlled: valueProp,\n    default: defaultValue ?? valueManager.emptyValue\n  });\n  const inputTimezone = React.useMemo(() => valueManager.getTimezone(utils, valueWithInputTimezone), [utils, valueManager, valueWithInputTimezone]);\n  const setInputTimezone = useEventCallback(newValue => {\n    if (inputTimezone == null) {\n      return newValue;\n    }\n    return valueManager.setTimezone(utils, inputTimezone, newValue);\n  });\n  const timezoneToRender = React.useMemo(() => {\n    if (timezoneProp) {\n      return timezoneProp;\n    }\n    if (inputTimezone) {\n      return inputTimezone;\n    }\n    if (referenceDate) {\n      return utils.getTimezone(referenceDate);\n    }\n    return 'default';\n  }, [timezoneProp, inputTimezone, referenceDate, utils]);\n  const valueWithTimezoneToRender = React.useMemo(() => valueManager.setTimezone(utils, timezoneToRender, valueWithInputTimezone), [valueManager, utils, timezoneToRender, valueWithInputTimezone]);\n  const handleValueChange = useEventCallback((newValue, ...otherParams) => {\n    const newValueWithInputTimezone = setInputTimezone(newValue);\n    setValue(newValueWithInputTimezone);\n    onChangeProp?.(newValueWithInputTimezone, ...otherParams);\n  });\n  return {\n    value: valueWithTimezoneToRender,\n    handleValueChange,\n    timezone: timezoneToRender\n  };\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,QAAQ,QAAQ,eAAe;AACxC;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,kBAAkB,GAAGA,CAAC;EACjCC,IAAI;EACJC,QAAQ,EAAEC,YAAY;EACtBC,KAAK,EAAEC,SAAS;EAChBC,YAAY;EACZC,aAAa;EACbC,QAAQ,EAAEC,YAAY;EACtBC;AACF,CAAC,KAAK;EACJ,MAAMC,KAAK,GAAGZ,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACa,sBAAsB,EAAEC,QAAQ,CAAC,GAAGf,aAAa,CAAC;IACvDG,IAAI;IACJa,KAAK,EAAE,OAAO;IACdC,UAAU,EAAEV,SAAS;IACrBW,OAAO,EAAEV,YAAY,IAAII,YAAY,CAACO;EACxC,CAAC,CAAC;EACF,MAAMC,aAAa,GAAGtB,KAAK,CAACuB,OAAO,CAAC,MAAMT,YAAY,CAACU,WAAW,CAACT,KAAK,EAAEC,sBAAsB,CAAC,EAAE,CAACD,KAAK,EAAED,YAAY,EAAEE,sBAAsB,CAAC,CAAC;EACjJ,MAAMS,gBAAgB,GAAGxB,gBAAgB,CAACyB,QAAQ,IAAI;IACpD,IAAIJ,aAAa,IAAI,IAAI,EAAE;MACzB,OAAOI,QAAQ;IACjB;IACA,OAAOZ,YAAY,CAACa,WAAW,CAACZ,KAAK,EAAEO,aAAa,EAAEI,QAAQ,CAAC;EACjE,CAAC,CAAC;EACF,MAAME,gBAAgB,GAAG5B,KAAK,CAACuB,OAAO,CAAC,MAAM;IAC3C,IAAIhB,YAAY,EAAE;MAChB,OAAOA,YAAY;IACrB;IACA,IAAIe,aAAa,EAAE;MACjB,OAAOA,aAAa;IACtB;IACA,IAAIX,aAAa,EAAE;MACjB,OAAOI,KAAK,CAACS,WAAW,CAACb,aAAa,CAAC;IACzC;IACA,OAAO,SAAS;EAClB,CAAC,EAAE,CAACJ,YAAY,EAAEe,aAAa,EAAEX,aAAa,EAAEI,KAAK,CAAC,CAAC;EACvD,MAAMc,yBAAyB,GAAG7B,KAAK,CAACuB,OAAO,CAAC,MAAMT,YAAY,CAACa,WAAW,CAACZ,KAAK,EAAEa,gBAAgB,EAAEZ,sBAAsB,CAAC,EAAE,CAACF,YAAY,EAAEC,KAAK,EAAEa,gBAAgB,EAAEZ,sBAAsB,CAAC,CAAC;EACjM,MAAMc,iBAAiB,GAAG7B,gBAAgB,CAAC,CAACyB,QAAQ,EAAE,GAAGK,WAAW,KAAK;IACvE,MAAMC,yBAAyB,GAAGP,gBAAgB,CAACC,QAAQ,CAAC;IAC5DT,QAAQ,CAACe,yBAAyB,CAAC;IACnCnB,YAAY,GAAGmB,yBAAyB,EAAE,GAAGD,WAAW,CAAC;EAC3D,CAAC,CAAC;EACF,OAAO;IACLvB,KAAK,EAAEqB,yBAAyB;IAChCC,iBAAiB;IACjBxB,QAAQ,EAAEsB;EACZ,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}