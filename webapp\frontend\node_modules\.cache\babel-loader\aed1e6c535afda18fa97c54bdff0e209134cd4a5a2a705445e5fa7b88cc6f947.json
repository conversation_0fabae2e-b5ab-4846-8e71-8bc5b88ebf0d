{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Box, Paper, Typography, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip, Dialog, DialogTitle, DialogContent, DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Cancel as CancelIcon, Warning as WarningIcon, Info as InfoIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null);\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response || loadError.code === 'ECONNABORTED' || loadError.message && loadError.message.includes('Network Error')) {\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(`${API_URL}/cavi/${cantiereId}`, {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 30000 // 30 secondi\n            });\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Carica solo le bobine disponibili o in uso\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina => (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') && bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata');\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina => bobina.tipologia === selectedCavo.tipologia && bobina.n_conduttori === selectedCavo.n_conduttori && bobina.sezione === selectedCavo.sezione);\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n          } else {\n            console.log('Nessuna bobina compatibile trovata, mostro tutte le bobine disponibili');\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo => cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase()));\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo => cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase());\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || exactMatch.metratura_reale && exactMatch.metratura_reale > 0) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    } else {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n      // Se non è selezionata alcuna bobina, imposta esplicitamente a null\n      if (!idBobina || idBobina === '') {\n        idBobina = null;\n        console.log('Nessuna bobina selezionata, impostando a null');\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      forceOver = false;\n\n      // Se si usa BOBINA_VUOTA, imposta sempre forceOver a true\n      if (idBobina === 'BOBINA_VUOTA') {\n        forceOver = true;\n        console.log('Forzando operazione per BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        // Anche in questo caso forziamo l'operazione\n        forceOver = true;\n        console.log(`Forzando operazione per metri posati (${metriPosati}) > metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n              await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, forceOver);\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n              if (error.response) {\n                var _error$response$data;\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message;\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, forceOver);\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response) {\n        var _error$response$data2;\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.detail) || error.message;\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 845,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Cerca cavo per ID\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 851,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 9,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavo\",\n              variant: \"outlined\",\n              value: cavoIdInput,\n              onChange: e => setCavoIdInput(e.target.value),\n              placeholder: \"Inserisci l'ID del cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 855,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: handleSearchCavoById,\n              disabled: caviLoading || !cavoIdInput.trim(),\n              startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 42\n              }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 75\n              }, this),\n              children: \"Cerca\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 854,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 850,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 882,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 888,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 887,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Non ci sono cavi disponibili da installare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 891,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '400px',\n            overflow: 'auto'\n          },\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 902,\n                    columnNumber: 27\n                  }, this), isCableSpare(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"SPARE\",\n                    color: \"error\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 904,\n                    columnNumber: 29\n                  }, this) : isCableInstalled(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"Installato\",\n                    color: \"success\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 911,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: getCableStateColor(cavo.stato_installazione),\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 918,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 901,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [cavo.tipologia || 'N/A', \" - \", cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 929,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 932,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', \" - A: \", cavo.ubicazione_arrivo || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 933,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 936,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A', \" - Metri posati: \", cavo.metratura_reale || '0']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 937,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 899,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  edge: \"end\",\n                  onClick: e => {\n                    e.stopPropagation(); // Prevent triggering the ListItem click\n                    setSelectedCavo(cavo);\n                    setShowCavoDetailsDialog(true);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 949,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 944,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 943,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 898,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 953,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 897,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 895,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 881,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 844,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 969,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n        cavo: selectedCavo,\n        compact: true,\n        title: \"Dettagli del cavo selezionato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 973,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold'\n          },\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 980,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                bgcolor: '#f5f5f5',\n                borderRadius: 1,\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold',\n                  color: 'primary.main'\n                },\n                children: \"Informazioni cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 988,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 1,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium'\n                    },\n                    children: \"Metri teorici:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 993,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 992,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 996,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 995,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium'\n                    },\n                    children: \"Stato attuale:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 999,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 998,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedCavo.stato_installazione || 'N/D',\n                    size: \"small\",\n                    color: getCableStateColor(selectedCavo.stato_installazione),\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1002,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1001,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 991,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 987,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 986,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                bgcolor: '#f5f5f5',\n                borderRadius: 1,\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold',\n                  color: 'secondary.main'\n                },\n                children: \"Informazioni bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 17\n              }, this), formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                return bobina ? /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"ID Bobina:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1023,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1022,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: getBobinaNumber(bobina.id_bobina)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1026,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1025,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1029,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1028,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1032,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1031,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1035,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1034,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: bobina.stato_bobina || 'N/D',\n                      size: \"small\",\n                      color: getReelStateColor(bobina.stato_bobina),\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1038,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1037,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1021,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Bobina non trovata\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1047,\n                  columnNumber: 21\n                }, this);\n              })() : /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: formData.id_bobina === 'BOBINA_VUOTA' ? \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" : \"Nessuna bobina selezionata\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1050,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1013,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 985,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1061,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            fullWidth: true,\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1064,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1060,\n          columnNumber: 11\n        }, this), formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: formWarnings.metri_posati\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1083,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1089,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1088,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 979,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 968,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = e => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo && (bobinaEsistente.tipologia !== selectedCavo.tipologia || String(bobinaEsistente.n_conduttori) !== String(selectedCavo.n_conduttori) || String(bobinaEsistente.sezione) !== String(selectedCavo.sezione))) {\n            // Mostra il dialogo per bobine incompatibili\n            setIncompatibleReel(bobinaEsistente);\n            setShowIncompatibleReelDialog(true);\n            return;\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Associa bobina (opzionale)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          paragraph: true,\n          children: \"Puoi associare una bobina al cavo selezionato oppure utilizzare l'opzione \\\"BOBINA VUOTA\\\" per registrare i metri posati senza associare una bobina specifica.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1203,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1209,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1208,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"Inserimento diretto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1216,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                size: \"small\",\n                fullWidth: true,\n                label: \"Numero bobina\",\n                variant: \"outlined\",\n                placeholder: \"Solo il numero (Y)\",\n                helperText: formErrors.id_bobina_input || \"Inserisci solo il numero della bobina\",\n                error: !!formErrors.id_bobina_input,\n                onBlur: handleBobinaNumberInput,\n                sx: {\n                  mb: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1219,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [\"ID Bobina: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: formData.id_bobina || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1231,\n                  columnNumber: 32\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1230,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1215,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"Selezione dalla lista\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1237,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                size: \"small\",\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"bobina-select-label\",\n                  children: \"Seleziona bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1241,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: \"bobina-select-label\",\n                  id: \"bobina-select\",\n                  name: \"id_bobina\",\n                  value: formData.id_bobina,\n                  label: \"Seleziona bobina\",\n                  onChange: handleFormChange,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"BOBINA_VUOTA\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"BOBINA VUOTA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1251,\n                      columnNumber: 25\n                    }, this), \" (nessuna bobina associata)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1250,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1253,\n                    columnNumber: 23\n                  }, this), bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: bobina.id_bobina,\n                    disabled: bobina.metri_residui < parseFloat(formData.metri_posati),\n                    children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A', \" - \", bobina.metri_residui || 0, \" m\"]\n                  }, bobina.id_bobina, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1255,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1242,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: \"Seleziona una bobina o \\\"BOBINA VUOTA\\\"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1264,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1240,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1236,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1213,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Nota\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1273,\n                columnNumber: 19\n              }, this), \": Se selezioni \\\"BOBINA VUOTA\\\", potrai associare una bobina specifica in un secondo momento.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1272,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1271,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1212,\n          columnNumber: 13\n        }, this), !bobineLoading && formData.id_bobina && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: 'background.paper',\n            borderRadius: 1,\n            border: '1px solid #e0e0e0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Dettagli bobina selezionata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1282,\n            columnNumber: 15\n          }, this), (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            if (bobina) {\n              return /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Numero:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1292,\n                      columnNumber: 27\n                    }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1291,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tipologia:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1295,\n                      columnNumber: 27\n                    }, this), \" \", bobina.tipologia || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1294,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Conduttori:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1298,\n                      columnNumber: 27\n                    }, this), \" \", bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1297,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1290,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri totali:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1303,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_totali || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1302,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1306,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1305,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1309,\n                      columnNumber: 27\n                    }, this), \" \", bobina.stato_bobina || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1308,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1301,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1289,\n                columnNumber: 21\n              }, this);\n            }\n            return /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"error\",\n              children: \"Bobina non trovata nel database\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1316,\n              columnNumber: 19\n            }, this);\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1281,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1325,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1202,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1197,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1359,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo,\n          compact: true,\n          title: \"Dettagli del cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1364,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Informazioni sull'operazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1378,\n                  columnNumber: 19\n                }, this), \" \", formData.metri_posati, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1377,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato Installazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1381,\n                  columnNumber: 19\n                }, this), \" \", statoInstallazione]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1380,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1376,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina Associata:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1386,\n                  columnNumber: 19\n                }, this), \" \", numeroBobina]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1385,\n                columnNumber: 17\n              }, this), bobinaInfo && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Residui Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1390,\n                  columnNumber: 21\n                }, this), \" \", bobinaInfo.metri_residui, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1389,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1384,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1375,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1371,\n          columnNumber: 11\n        }, this), bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1399,\n            columnNumber: 15\n          }, this), \" I metri posati (\", formData.metri_posati, \"m) superano i metri residui della bobina (\", bobinaInfo.metri_residui, \"m). Questo porter\\xE0 la bobina in stato OVER.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1398,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1404,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1358,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1353,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      // Seleziona Cavo\n      case 1:\n        return renderStep3();\n      // Associa Bobina\n      case 2:\n        return renderStep2();\n      // Inserisci Metri\n      case 3:\n        return renderStep4();\n      // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReel(null);\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    if (!selectedCavo || !incompatibleReel) return;\n    try {\n      setLoading(true);\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoToMatchReel(cantiereId, selectedCavo.id_cavo, incompatibleReel);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, selectedCavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: incompatibleReel.id_bobina\n      });\n      onSuccess(`Caratteristiche del cavo ${selectedCavo.id_cavo} aggiornate per corrispondere alla bobina ${incompatibleReel.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n\n  // Renderizza il form compresso in un'unica vista\n  const renderCompressedForm = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Cerca cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1503,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 9,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavo\",\n              variant: \"outlined\",\n              value: cavoIdInput,\n              onChange: e => setCavoIdInput(e.target.value),\n              placeholder: \"Inserisci l'ID del cavo o parte di esso\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1508,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1507,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: handleSearchCavoById,\n              disabled: caviLoading || !cavoIdInput.trim(),\n              startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1524,\n                columnNumber: 42\n              }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1524,\n                columnNumber: 75\n              }, this),\n              children: \"Cerca\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1518,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1517,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1506,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1502,\n        columnNumber: 9\n      }, this), showSearchResults && searchResults.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Risultati della ricerca\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1535,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  bgcolor: '#f5f5f5'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"ID Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1542,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Tipologia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1543,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Conduttori\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1544,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Ubicazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1545,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri Teorici\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1546,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1547,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Azioni\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1548,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1541,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1540,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: searchResults.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1554,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1555,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1556,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1557,\n                    columnNumber: 73\n                  }, this), \"A: \", cavo.ubicazione_arrivo || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1557,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [cavo.metri_teorici || 'N/A', \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1558,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: cavo.stato_installazione || 'N/D',\n                    size: \"small\",\n                    color: getCableStateColor(cavo.stato_installazione),\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1560,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1559,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    variant: \"contained\",\n                    color: \"primary\",\n                    onClick: () => handleCavoSelect(cavo),\n                    disabled: isCableInstalled(cavo),\n                    children: \"Seleziona\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1568,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1567,\n                  columnNumber: 23\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1553,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1551,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1539,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1538,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1534,\n        columnNumber: 11\n      }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Inserimento metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1589,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1,\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold',\n              color: 'primary.main'\n            },\n            children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1595,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1600,\n                  columnNumber: 47\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1600,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Conduttori:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1601,\n                  columnNumber: 47\n                }, this), \" \", selectedCavo.n_conduttori || 'N/A', \" x \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1601,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1602,\n                  columnNumber: 47\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1602,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1599,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione partenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1605,\n                  columnNumber: 47\n                }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1605,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione arrivo:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1606,\n                  columnNumber: 47\n                }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1606,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1608,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: selectedCavo.stato_installazione || 'N/D',\n                  size: \"small\",\n                  color: getCableStateColor(selectedCavo.stato_installazione),\n                  variant: \"outlined\",\n                  sx: {\n                    ml: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1609,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1607,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1604,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1598,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1594,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"Metratura posata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1624,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              size: \"small\",\n              fullWidth: true,\n              label: \"Metri posati\",\n              variant: \"outlined\",\n              name: \"metri_posati\",\n              type: \"number\",\n              value: formData.metri_posati,\n              onChange: handleFormChange,\n              error: !!formErrors.metri_posati,\n              helperText: formErrors.metri_posati || formWarnings.metri_posati,\n              FormHelperTextProps: {\n                sx: {\n                  color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n                }\n              },\n              sx: {\n                mb: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1627,\n              columnNumber: 17\n            }, this), formWarnings.metri_posati && !formErrors.metri_posati && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mb: 2\n              },\n              children: formWarnings.metri_posati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1644,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1623,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"Associa bobina (opzionale)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1652,\n              columnNumber: 17\n            }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                my: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1657,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1656,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"bobina-select-label\",\n                children: \"Seleziona bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1661,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"bobina-select-label\",\n                id: \"bobina-select\",\n                name: \"id_bobina\",\n                value: formData.id_bobina,\n                label: \"Seleziona bobina\",\n                onChange: handleFormChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"BOBINA_VUOTA\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"BOBINA VUOTA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1671,\n                    columnNumber: 25\n                  }, this), \" (nessuna bobina associata)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1670,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1673,\n                  columnNumber: 23\n                }, this), bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: bobina.id_bobina,\n                  disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                  children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A', \" - \", bobina.metri_residui || 0, \" m\"]\n                }, bobina.id_bobina, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1675,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1662,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: \"Seleziona una bobina o \\\"BOBINA VUOTA\\\"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1684,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1660,\n              columnNumber: 19\n            }, this), !bobineLoading && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n              const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n              if (bobina) {\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mt: 2,\n                    p: 2,\n                    bgcolor: '#f5f5f5',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Bobina:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1696,\n                      columnNumber: 53\n                    }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1696,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1697,\n                      columnNumber: 53\n                    }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1697,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1699,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: bobina.stato_bobina || 'N/D',\n                      size: \"small\",\n                      color: getReelStateColor(bobina.stato_bobina),\n                      variant: \"outlined\",\n                      sx: {\n                        ml: 1\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1700,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1698,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1695,\n                  columnNumber: 23\n                }, this);\n              }\n              return null;\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1651,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1621,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            display: 'flex',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"secondary\",\n            onClick: () => {\n              setSelectedCavo(null);\n              setFormData({\n                id_cavo: '',\n                metri_posati: '',\n                id_bobina: ''\n              });\n            },\n            startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1728,\n              columnNumber: 28\n            }, this),\n            disabled: loading,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1717,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSubmit,\n            endIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1738,\n              columnNumber: 26\n            }, this),\n            disabled: loading || !formData.metri_posati,\n            children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1741,\n              columnNumber: 28\n            }, this) : 'Salva'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1734,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1716,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1588,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1500,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [renderCompressedForm(), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showConfirmDialog,\n      onClose: () => setShowConfirmDialog(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1758,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: confirmDialogProps.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1759,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1757,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1756,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mt: 2\n          },\n          children: confirmDialogProps.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1763,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1762,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowConfirmDialog(false),\n          color: \"secondary\",\n          variant: \"outlined\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1768,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setShowConfirmDialog(false);\n            confirmDialogProps.onConfirm();\n          },\n          color: \"primary\",\n          variant: \"contained\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1771,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1767,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1755,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1789,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavo gi\\xE0 posato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1790,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1788,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1787,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: alreadyLaidCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: alreadyLaidCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1797,\n              columnNumber: 25\n            }, this), \" risulta gi\\xE0 posato (\", alreadyLaidCavo.metratura_reale || 0, \"m).\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1796,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: \"Puoi scegliere di:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1799,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"ul\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata al cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1803,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1804,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1805,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1802,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1795,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1793,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1811,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1815,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1818,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1814,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1810,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1786,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: handleCloseIncompatibleReelDialog,\n      cavo: selectedCavo,\n      bobina: incompatibleReel,\n      onUpdateCavo: handleUpdateCavoToMatchReel,\n      onSelectAnotherReel: handleSelectAnotherReel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1826,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1844,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Dettagli Cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1845,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1843,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1842,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1849,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1848,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          color: \"primary\",\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1852,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1851,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1836,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1751,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"MnqmcIZ4wBxcUDgu5+IipbS27NU=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Search", "SearchIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "useNavigate", "caviService", "axiosInstance", "IncompatibleReelDialog", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchResults", "setSearchResults", "showSearchResults", "setShowSearchResults", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReel", "setIncompatibleReel", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "alreadyLaidCavo", "setAlreadyLaidCavo", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "loadBobine", "getBobinaNumber", "idBobina", "includes", "split", "loadCavi", "console", "log", "caviData", "get<PERSON><PERSON>", "length", "<PERSON><PERSON><PERSON>", "loadError", "error", "isNetworkError", "response", "code", "message", "Promise", "resolve", "setTimeout", "token", "localStorage", "getItem", "API_URL", "defaults", "baseURL", "retryResponse", "get", "headers", "timeout", "data", "retryError", "errorMessage", "detail", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "bobina", "stato_bobina", "tipologia", "n_conduttori", "sezione", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "sort", "a", "b", "metri_residui", "handleSearchCavoById", "trim", "filteredCavi", "cavo", "toLowerCase", "exactMatch", "find", "stato_installazione", "metratura_reale", "modificato_manualmente", "handleCavoSelect", "status", "window", "confirm", "reactivateSpare", "then", "updatedCavo", "catch", "cavoId", "handleFormChange", "e", "name", "value", "target", "validateField", "warning", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "prev", "notificationShown", "setNotificationShown", "showConfirmDialog", "setShowConfirmDialog", "confirmDialogProps", "setConfirmDialogProps", "title", "onConfirm", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "handleNext", "activeStep", "setActiveStep", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "metriTeorici", "handleSubmit", "statoInstallazione", "forceOver", "confirmMessage", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "success", "_error$response$data", "request", "_error$response$data2", "renderStep1", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "container", "spacing", "alignItems", "item", "xs", "fullWidth", "label", "onChange", "placeholder", "color", "onClick", "disabled", "startIcon", "size", "display", "justifyContent", "my", "cavi", "severity", "List", "maxHeight", "overflow", "map", "ListItem", "button", "ListItemText", "primary", "ml", "secondary", "component", "ubicazione_partenza", "ubicazione_arrivo", "ListItemSecondaryAction", "edge", "stopPropagation", "renderStep2", "compact", "fontWeight", "md", "bgcolor", "borderRadius", "height", "mt", "type", "helperText", "FormHelperTextProps", "renderStep3", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "handleBobinaNumberInput", "id_bobina_input", "idBobinaCompleto", "<PERSON><PERSON><PERSON>", "String", "paragraph", "onBlur", "id", "labelId", "border", "metri_totali", "renderStep4", "bobinaInfo", "getStepContent", "step", "handleCloseAlreadyLaidDialog", "handleModifyReel", "handleSelectAnotherCable", "handleCloseIncompatibleReelDialog", "handleUpdateCavoToMatchReel", "updateCavoToMatchReel", "getCavoById", "handleSelectAnotherReel", "renderCompressedForm", "endIcon", "open", "onClose", "max<PERSON><PERSON><PERSON>", "gap", "autoFocus", "mr", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null);\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response ||\n            loadError.code === 'ECONNABORTED' ||\n            (loadError.message && loadError.message.includes('Network Error'))) {\n\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(\n              `${API_URL}/cavi/${cantiereId}`,\n              {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 30000 // 30 secondi\n              }\n            );\n\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Carica solo le bobine disponibili o in uso\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina =>\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') &&\n        bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata'\n      );\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina =>\n            bobina.tipologia === selectedCavo.tipologia &&\n            bobina.n_conduttori === selectedCavo.n_conduttori &&\n            bobina.sezione === selectedCavo.sezione\n          );\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n          } else {\n            console.log('Nessuna bobina compatibile trovata, mostro tutte le bobine disponibili');\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase())\n      );\n\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo =>\n        cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase()\n      );\n\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || (exactMatch.metratura_reale && exactMatch.metratura_reale > 0)) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    } else {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n      // Se non è selezionata alcuna bobina, imposta esplicitamente a null\n      if (!idBobina || idBobina === '') {\n        idBobina = null;\n        console.log('Nessuna bobina selezionata, impostando a null');\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      forceOver = false;\n\n      // Se si usa BOBINA_VUOTA, imposta sempre forceOver a true\n      if (idBobina === 'BOBINA_VUOTA') {\n        forceOver = true;\n        console.log('Forzando operazione per BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        // Anche in questo caso forziamo l'operazione\n        forceOver = true;\n        console.log(`Forzando operazione per metri posati (${metriPosati}) > metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n\n              await caviService.updateMetriPosati(\n                cantiereId,\n                formData.id_cavo,\n                metriPosati,\n                idBobina,\n                forceOver\n              );\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n              if (error.response) {\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = error.response.data?.detail || error.message;\n\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n      if (error.response) {\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = error.response.data?.detail || error.message;\n\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID */}\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Cerca cavo per ID\n          </Typography>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={9}>\n              <TextField\n                fullWidth\n                label=\"ID Cavo\"\n                variant=\"outlined\"\n                value={cavoIdInput}\n                onChange={(e) => setCavoIdInput(e.target.value)}\n                placeholder=\"Inserisci l'ID del cavo\"\n              />\n            </Grid>\n            <Grid item xs={3}>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={handleSearchCavoById}\n                disabled={caviLoading || !cavoIdInput.trim()}\n                startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n              >\n                Cerca\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Lista cavi */}\n        <Paper sx={{ p: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\">\n              Non ci sono cavi disponibili da installare.\n            </Alert>\n          ) : (\n            <List sx={{ maxHeight: '400px', overflow: 'auto' }}>\n              {cavi.map((cavo) => (\n                <React.Fragment key={cavo.id_cavo}>\n                  <ListItem button onClick={() => handleCavoSelect(cavo)}>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle1\">{cavo.id_cavo}</Typography>\n                          {isCableSpare(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"SPARE\"\n                              color=\"error\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : isCableInstalled(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"Installato\"\n                              color=\"success\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : (\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione}\n                              color={getCableStateColor(cavo.stato_installazione)}\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <>\n                          <Typography variant=\"body2\" component=\"span\">\n                            {cavo.tipologia || 'N/A'} - {cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Da: {cavo.ubicazione_partenza || 'N/A'} - A: {cavo.ubicazione_arrivo || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Metri teorici: {cavo.metri_teorici || 'N/A'} - Metri posati: {cavo.metratura_reale || '0'}\n                          </Typography>\n                        </>\n                      }\n                    />\n                    <ListItemSecondaryAction>\n                      <IconButton edge=\"end\" onClick={(e) => {\n                        e.stopPropagation(); // Prevent triggering the ListItem click\n                        setSelectedCavo(cavo);\n                        setShowCavoDetailsDialog(true);\n                      }}>\n                        <InfoIcon />\n                      </IconButton>\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                  <Divider />\n                </React.Fragment>\n              ))}\n            </List>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserisci metri posati\n        </Typography>\n\n        <CavoDetailsView\n          cavo={selectedCavo}\n          compact={true}\n          title=\"Dettagli del cavo selezionato\"\n        />\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom sx={{ fontWeight: 'bold' }}>\n            Inserisci i metri posati\n          </Typography>\n\n          {/* Informazioni sul cavo e sulla bobina in una griglia */}\n          <Grid container spacing={2} sx={{ mb: 3 }}>\n            <Grid item xs={12} md={6}>\n              <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, height: '100%' }}>\n                <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>\n                  Informazioni cavo\n                </Typography>\n                <Grid container spacing={1}>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Metri teorici:</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\">{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Stato attuale:</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Chip\n                      label={selectedCavo.stato_installazione || 'N/D'}\n                      size=\"small\"\n                      color={getCableStateColor(selectedCavo.stato_installazione)}\n                      variant=\"outlined\"\n                    />\n                  </Grid>\n                </Grid>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, height: '100%' }}>\n                <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'secondary.main' }}>\n                  Informazioni bobina\n                </Typography>\n                {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n                  const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                  return bobina ? (\n                    <Grid container spacing={1}>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>ID Bobina:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\">{getBobinaNumber(bobina.id_bobina)}</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Metri residui:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\">{bobina.metri_residui || 0} m</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Stato:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Chip\n                          label={bobina.stato_bobina || 'N/D'}\n                          size=\"small\"\n                          color={getReelStateColor(bobina.stato_bobina)}\n                          variant=\"outlined\"\n                        />\n                      </Grid>\n                    </Grid>\n                  ) : (\n                    <Typography variant=\"body2\">Bobina non trovata</Typography>\n                  );\n                })() : (\n                  <Typography variant=\"body2\">\n                    {formData.id_bobina === 'BOBINA_VUOTA' ?\n                      \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" :\n                      \"Nessuna bobina selezionata\"}\n                  </Typography>\n                )}\n              </Box>\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, mb: 3 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Metratura posata\n            </Typography>\n            <TextField\n              size=\"small\"\n              fullWidth\n              label=\"Metri posati\"\n              variant=\"outlined\"\n              name=\"metri_posati\"\n              type=\"number\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              sx={{ mb: 1 }}\n            />\n          </Box>\n\n          {formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              {formWarnings.metri_posati}\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\">\n              Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n            </Typography>\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = (e) => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo && (\n              bobinaEsistente.tipologia !== selectedCavo.tipologia ||\n              String(bobinaEsistente.n_conduttori) !== String(selectedCavo.n_conduttori) ||\n              String(bobinaEsistente.sezione) !== String(selectedCavo.sezione)\n          )) {\n            // Mostra il dialogo per bobine incompatibili\n            setIncompatibleReel(bobinaEsistente);\n            setShowIncompatibleReelDialog(true);\n            return;\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Associa bobina (opzionale)\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"body1\" paragraph>\n            Puoi associare una bobina al cavo selezionato oppure utilizzare l'opzione \"BOBINA VUOTA\" per registrare i metri posati senza associare una bobina specifica.\n          </Typography>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              <Grid container spacing={3}>\n                {/* Colonna sinistra: Input diretto */}\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                    Inserimento diretto\n                  </Typography>\n                  <TextField\n                    size=\"small\"\n                    fullWidth\n                    label=\"Numero bobina\"\n                    variant=\"outlined\"\n                    placeholder=\"Solo il numero (Y)\"\n                    helperText={formErrors.id_bobina_input || \"Inserisci solo il numero della bobina\"}\n                    error={!!formErrors.id_bobina_input}\n                    onBlur={handleBobinaNumberInput}\n                    sx={{ mb: 1 }}\n                  />\n                  <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                    ID Bobina: <strong>{formData.id_bobina || '-'}</strong>\n                  </Typography>\n                </Grid>\n\n                {/* Colonna destra: Selezione dalla lista */}\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                    Selezione dalla lista\n                  </Typography>\n                  <FormControl fullWidth size=\"small\">\n                    <InputLabel id=\"bobina-select-label\">Seleziona bobina</InputLabel>\n                    <Select\n                      labelId=\"bobina-select-label\"\n                      id=\"bobina-select\"\n                      name=\"id_bobina\"\n                      value={formData.id_bobina}\n                      label=\"Seleziona bobina\"\n                      onChange={handleFormChange}\n                    >\n                      <MenuItem value=\"BOBINA_VUOTA\">\n                        <strong>BOBINA VUOTA</strong> (nessuna bobina associata)\n                      </MenuItem>\n                      <Divider />\n                      {bobine.map((bobina) => (\n                        <MenuItem\n                          key={bobina.id_bobina}\n                          value={bobina.id_bobina}\n                          disabled={bobina.metri_residui < parseFloat(formData.metri_posati)}\n                        >\n                          {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'} - {bobina.metri_residui || 0} m\n                        </MenuItem>\n                      ))}\n                    </Select>\n                    <FormHelperText>\n                      Seleziona una bobina o \"BOBINA VUOTA\"\n                    </FormHelperText>\n                  </FormControl>\n                </Grid>\n              </Grid>\n\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                <Typography variant=\"body2\">\n                  <strong>Nota</strong>: Se selezioni \"BOBINA VUOTA\", potrai associare una bobina specifica in un secondo momento.\n                </Typography>\n              </Alert>\n            </Box>\n          )}\n\n          {/* Mostra dettagli della bobina selezionata */}\n          {!bobineLoading && formData.id_bobina && (\n            <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n              <Typography variant=\"subtitle2\" gutterBottom>\n                Dettagli bobina selezionata\n              </Typography>\n              {(() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                if (bobina) {\n                  return (\n                    <Grid container spacing={2}>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Numero:</strong> {getBobinaNumber(bobina.id_bobina)}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Tipologia:</strong> {bobina.tipologia || 'N/A'}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Conduttori:</strong> {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Metri totali:</strong> {bobina.metri_totali || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Metri residui:</strong> {bobina.metri_residui || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Stato:</strong> {bobina.stato_bobina || 'N/A'}\n                        </Typography>\n                      </Grid>\n                    </Grid>\n                  );\n                }\n                return (\n                  <Typography variant=\"body2\" color=\"error\">\n                    Bobina non trovata nel database\n                  </Typography>\n                );\n              })()}\n            </Box>\n          )}\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          {/* Dettagli del cavo */}\n          <CavoDetailsView\n            cavo={selectedCavo}\n            compact={true}\n            title=\"Dettagli del cavo\"\n          />\n\n          {/* Informazioni sull'operazione */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Informazioni sull'operazione:\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Metri Posati:</strong> {formData.metri_posati} m\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato Installazione:</strong> {statoInstallazione}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Bobina Associata:</strong> {numeroBobina}\n                </Typography>\n                {bobinaInfo && (\n                  <Typography variant=\"body2\">\n                    <strong>Metri Residui Bobina:</strong> {bobinaInfo.metri_residui} m\n                  </Typography>\n                )}\n              </Grid>\n            </Grid>\n          </Box>\n\n          {bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mt: 3 }}>\n              <strong>Attenzione:</strong> I metri posati ({formData.metri_posati}m) superano i metri residui della bobina ({bobinaInfo.metri_residui}m).\n              Questo porterà la bobina in stato OVER.\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1(); // Seleziona Cavo\n      case 1:\n        return renderStep3(); // Associa Bobina\n      case 2:\n        return renderStep2(); // Inserisci Metri\n      case 3:\n        return renderStep4(); // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReel(null);\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    if (!selectedCavo || !incompatibleReel) return;\n\n    try {\n      setLoading(true);\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoToMatchReel(cantiereId, selectedCavo.id_cavo, incompatibleReel);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, selectedCavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: incompatibleReel.id_bobina\n      });\n\n      onSuccess(`Caratteristiche del cavo ${selectedCavo.id_cavo} aggiornate per corrispondere alla bobina ${incompatibleReel.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n\n  // Renderizza il form compresso in un'unica vista\n  const renderCompressedForm = () => {\n    return (\n      <Box>\n        {/* Sezione di ricerca */}\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Cerca cavo\n          </Typography>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={9}>\n              <TextField\n                fullWidth\n                label=\"ID Cavo\"\n                variant=\"outlined\"\n                value={cavoIdInput}\n                onChange={(e) => setCavoIdInput(e.target.value)}\n                placeholder=\"Inserisci l'ID del cavo o parte di esso\"\n              />\n            </Grid>\n            <Grid item xs={3}>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={handleSearchCavoById}\n                disabled={caviLoading || !cavoIdInput.trim()}\n                startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n              >\n                Cerca\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Risultati della ricerca */}\n        {showSearchResults && searchResults.length > 0 && (\n          <Paper sx={{ p: 3, mb: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Risultati della ricerca\n            </Typography>\n            <TableContainer>\n              <Table size=\"small\">\n                <TableHead>\n                  <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                    <TableCell>ID Cavo</TableCell>\n                    <TableCell>Tipologia</TableCell>\n                    <TableCell>Conduttori</TableCell>\n                    <TableCell>Ubicazione</TableCell>\n                    <TableCell>Metri Teorici</TableCell>\n                    <TableCell>Stato</TableCell>\n                    <TableCell>Azioni</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {searchResults.map((cavo) => (\n                    <TableRow key={cavo.id_cavo}>\n                      <TableCell>{cavo.id_cavo}</TableCell>\n                      <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                      <TableCell>{cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}</TableCell>\n                      <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>\n                      <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                      <TableCell>\n                        <Chip\n                          label={cavo.stato_installazione || 'N/D'}\n                          size=\"small\"\n                          color={getCableStateColor(cavo.stato_installazione)}\n                          variant=\"outlined\"\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Button\n                          size=\"small\"\n                          variant=\"contained\"\n                          color=\"primary\"\n                          onClick={() => handleCavoSelect(cavo)}\n                          disabled={isCableInstalled(cavo)}\n                        >\n                          Seleziona\n                        </Button>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </Paper>\n        )}\n\n        {/* Form per inserimento metri e selezione bobina */}\n        {selectedCavo && (\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Inserimento metri posati\n            </Typography>\n\n            {/* Dettagli del cavo selezionato */}\n            <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, mb: 3 }}>\n              <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>\n                Cavo selezionato: {selectedCavo.id_cavo}\n              </Typography>\n              <Grid container spacing={2}>\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                  <Typography variant=\"body2\"><strong>Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'} x {selectedCavo.sezione || 'N/A'}</Typography>\n                  <Typography variant=\"body2\"><strong>Metri teorici:</strong> {selectedCavo.metri_teorici || 'N/A'} m</Typography>\n                </Grid>\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"body2\"><strong>Ubicazione partenza:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                  <Typography variant=\"body2\"><strong>Ubicazione arrivo:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                  <Typography variant=\"body2\">\n                    <strong>Stato:</strong>\n                    <Chip\n                      label={selectedCavo.stato_installazione || 'N/D'}\n                      size=\"small\"\n                      color={getCableStateColor(selectedCavo.stato_installazione)}\n                      variant=\"outlined\"\n                      sx={{ ml: 1 }}\n                    />\n                  </Typography>\n                </Grid>\n              </Grid>\n            </Box>\n\n            <Grid container spacing={3}>\n              {/* Colonna sinistra: Metri posati */}\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                  Metratura posata\n                </Typography>\n                <TextField\n                  size=\"small\"\n                  fullWidth\n                  label=\"Metri posati\"\n                  variant=\"outlined\"\n                  name=\"metri_posati\"\n                  type=\"number\"\n                  value={formData.metri_posati}\n                  onChange={handleFormChange}\n                  error={!!formErrors.metri_posati}\n                  helperText={formErrors.metri_posati || formWarnings.metri_posati}\n                  FormHelperTextProps={{\n                    sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n                  }}\n                  sx={{ mb: 1 }}\n                />\n                {formWarnings.metri_posati && !formErrors.metri_posati && (\n                  <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                    {formWarnings.metri_posati}\n                  </Alert>\n                )}\n              </Grid>\n\n              {/* Colonna destra: Selezione bobina */}\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                  Associa bobina (opzionale)\n                </Typography>\n                {bobineLoading ? (\n                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n                    <CircularProgress />\n                  </Box>\n                ) : (\n                  <FormControl fullWidth size=\"small\">\n                    <InputLabel id=\"bobina-select-label\">Seleziona bobina</InputLabel>\n                    <Select\n                      labelId=\"bobina-select-label\"\n                      id=\"bobina-select\"\n                      name=\"id_bobina\"\n                      value={formData.id_bobina}\n                      label=\"Seleziona bobina\"\n                      onChange={handleFormChange}\n                    >\n                      <MenuItem value=\"BOBINA_VUOTA\">\n                        <strong>BOBINA VUOTA</strong> (nessuna bobina associata)\n                      </MenuItem>\n                      <Divider />\n                      {bobine.map((bobina) => (\n                        <MenuItem\n                          key={bobina.id_bobina}\n                          value={bobina.id_bobina}\n                          disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                        >\n                          {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'} - {bobina.metri_residui || 0} m\n                        </MenuItem>\n                      ))}\n                    </Select>\n                    <FormHelperText>\n                      Seleziona una bobina o \"BOBINA VUOTA\"\n                    </FormHelperText>\n                  </FormControl>\n                )}\n\n                {/* Mostra dettagli della bobina selezionata */}\n                {!bobineLoading && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n                  const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                  if (bobina) {\n                    return (\n                      <Box sx={{ mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n                        <Typography variant=\"body2\"><strong>Bobina:</strong> {getBobinaNumber(bobina.id_bobina)}</Typography>\n                        <Typography variant=\"body2\"><strong>Metri residui:</strong> {bobina.metri_residui || 0} m</Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Stato:</strong>\n                          <Chip\n                            label={bobina.stato_bobina || 'N/D'}\n                            size=\"small\"\n                            color={getReelStateColor(bobina.stato_bobina)}\n                            variant=\"outlined\"\n                            sx={{ ml: 1 }}\n                          />\n                        </Typography>\n                      </Box>\n                    );\n                  }\n                  return null;\n                })()}\n              </Grid>\n            </Grid>\n\n            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>\n              <Button\n                variant=\"outlined\"\n                color=\"secondary\"\n                onClick={() => {\n                  setSelectedCavo(null);\n                  setFormData({\n                    id_cavo: '',\n                    metri_posati: '',\n                    id_bobina: ''\n                  });\n                }}\n                startIcon={<CancelIcon />}\n                disabled={loading}\n              >\n                Annulla\n              </Button>\n\n              <Button\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={handleSubmit}\n                endIcon={<SaveIcon />}\n                disabled={loading || !formData.metri_posati}\n              >\n                {loading ? <CircularProgress size={24} /> : 'Salva'}\n              </Button>\n            </Box>\n          </Paper>\n        )}\n      </Box>\n    );\n  };\n\n  return (\n    <Box>\n      {renderCompressedForm()}\n\n      {/* Dialogo di conferma generico */}\n      <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">{confirmDialogProps.title}</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <Typography variant=\"body1\" sx={{ mt: 2 }}>\n            {confirmDialogProps.message}\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowConfirmDialog(false)} color=\"secondary\" variant=\"outlined\">\n            Annulla\n          </Button>\n          <Button\n            onClick={() => {\n              setShowConfirmDialog(false);\n              confirmDialogProps.onConfirm();\n            }}\n            color=\"primary\"\n            variant=\"contained\"\n            autoFocus\n          >\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">Cavo già posato</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          {alreadyLaidCavo && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body1\" paragraph>\n                Il cavo <strong>{alreadyLaidCavo.id_cavo}</strong> risulta già posato ({alreadyLaidCavo.metratura_reale || 0}m).\n              </Typography>\n              <Typography variant=\"body1\" paragraph>\n                Puoi scegliere di:\n              </Typography>\n              <Typography variant=\"body2\" component=\"ul\">\n                <li>Modificare la bobina associata al cavo</li>\n                <li>Selezionare un altro cavo</li>\n                <li>Annullare l'operazione</li>\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={handleCloseIncompatibleReelDialog}\n        cavo={selectedCavo}\n        bobina={incompatibleReel}\n        onUpdateCavo={handleUpdateCavoToMatchReel}\n        onSelectAnotherReel={handleSelectAnotherReel}\n      />\n\n      {/* Dialogo per visualizzare i dettagli del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <InfoIcon color=\"primary\" />\n            <Typography variant=\"h6\">Dettagli Cavo</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <CavoDetailsView cavo={selectedCavo} />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)} color=\"primary\">\n            Chiudi\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,QACH,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmE,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqE,aAAa,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuE,aAAa,EAAEC,gBAAgB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC2E,MAAM,EAAEC,SAAS,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6E,YAAY,EAAEC,eAAe,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+E,WAAW,EAAEC,cAAc,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACiF,QAAQ,EAAEC,WAAW,CAAC,GAAGlF,QAAQ,CAAC;IACvCmF,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACwF,YAAY,EAAEC,eAAe,CAAC,GAAGzF,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAAC0F,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAAC4F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7F,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC8F,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACgG,eAAe,EAAEC,kBAAkB,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACdmG,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACxC,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMyC,eAAe,GAAIC,QAAQ,IAAK;IACpC;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOF,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMG,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFrC,cAAc,CAAC,IAAI,CAAC;MACpBsC,OAAO,CAACC,GAAG,CAAC,oCAAoC/C,UAAU,KAAK,CAAC;;MAEhE;MACA,IAAI;QACF,MAAMgD,QAAQ,GAAG,MAAMpE,WAAW,CAACqE,OAAO,CAACjD,UAAU,CAAC;QACtD8C,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACE,MAAM,OAAO,CAAC;;QAE/C;QACA;QACAC,OAAO,CAACH,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOI,SAAS,EAAE;QAClBN,OAAO,CAACO,KAAK,CAAC,qDAAqD,EAAED,SAAS,CAAC;;QAE/E;QACA,IAAIA,SAAS,CAACE,cAAc,IAAI,CAACF,SAAS,CAACG,QAAQ,IAC/CH,SAAS,CAACI,IAAI,KAAK,cAAc,IAChCJ,SAAS,CAACK,OAAO,IAAIL,SAAS,CAACK,OAAO,CAACd,QAAQ,CAAC,eAAe,CAAE,EAAE;UAEtEG,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtE,MAAM,IAAIW,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvD,IAAI;YACF;YACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC3C,MAAMC,OAAO,GAAGnF,aAAa,CAACoF,QAAQ,CAACC,OAAO;;YAE9C;YACA,MAAMC,aAAa,GAAG,MAAM7H,KAAK,CAAC8H,GAAG,CACnC,GAAGJ,OAAO,SAAShE,UAAU,EAAE,EAC/B;cACEqE,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUR,KAAK;cAClC,CAAC;cACDS,OAAO,EAAE,KAAK,CAAC;YACjB,CACF,CAAC;YAEDxB,OAAO,CAACC,GAAG,CAAC,2CAA2CoB,aAAa,CAACI,IAAI,CAACrB,MAAM,OAAO,CAAC;YACxFC,OAAO,CAACgB,aAAa,CAACI,IAAI,CAAC;UAC7B,CAAC,CAAC,OAAOC,UAAU,EAAE;YACnB1B,OAAO,CAACO,KAAK,CAAC,uCAAuC,EAAEmB,UAAU,CAAC;YAClE,MAAMA,UAAU;UAClB;QACF,CAAC,MAAM;UACL,MAAMpB,SAAS;QACjB;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIoB,YAAY,GAAG,iCAAiC;MAEpD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEAvD,OAAO,CAACuE,YAAY,CAAC;IACvB,CAAC,SAAS;MACRjE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMgC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF9B,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAMiE,UAAU,GAAG,MAAMlF,gBAAgB,CAACmF,SAAS,CAAC5E,UAAU,CAAC;;MAE/D;MACA,IAAI6E,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM,IAC/C,CAACA,MAAM,CAACC,YAAY,KAAK,aAAa,IAAID,MAAM,CAACC,YAAY,KAAK,QAAQ,KAC1ED,MAAM,CAACC,YAAY,KAAK,MAAM,IAAID,MAAM,CAACC,YAAY,KAAK,WAC5D,CAAC;;MAED;MACA,IAAI/D,YAAY,EAAE;QAChB;QACA,IAAIA,YAAY,CAACgE,SAAS,IAAIhE,YAAY,CAACiE,YAAY,IAAIjE,YAAY,CAACkE,OAAO,EAAE;UAC/E,MAAMC,iBAAiB,GAAGP,kBAAkB,CAACC,MAAM,CAACC,MAAM,IACxDA,MAAM,CAACE,SAAS,KAAKhE,YAAY,CAACgE,SAAS,IAC3CF,MAAM,CAACG,YAAY,KAAKjE,YAAY,CAACiE,YAAY,IACjDH,MAAM,CAACI,OAAO,KAAKlE,YAAY,CAACkE,OAClC,CAAC;;UAED;UACA,IAAIC,iBAAiB,CAAClC,MAAM,GAAG,CAAC,EAAE;YAChC2B,kBAAkB,GAAGO,iBAAiB;UACxC,CAAC,MAAM;YACLtC,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;UACvF;QACF;;QAEA;QACA8B,kBAAkB,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;MACtE;MAEAxE,SAAS,CAAC6D,kBAAkB,CAAC;IAC/B,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DnD,OAAO,CAAC,uCAAuC,IAAImD,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACR/C,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM+E,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACtE,WAAW,CAACuE,IAAI,CAAC,CAAC,EAAE;MACvBxF,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFM,cAAc,CAAC,IAAI,CAAC;MACpBsC,OAAO,CAACC,GAAG,CAAC,6BAA6B5B,WAAW,CAACuE,IAAI,CAAC,CAAC,iBAAiB1F,UAAU,EAAE,CAAC;;MAEzF;MACA,MAAMgD,QAAQ,GAAG,MAAMpE,WAAW,CAACqE,OAAO,CAACjD,UAAU,CAAC;;MAEtD;MACA,MAAM2F,YAAY,GAAG3C,QAAQ,CAAC8B,MAAM,CAACc,IAAI,IACvCA,IAAI,CAACrE,OAAO,CAACsE,WAAW,CAAC,CAAC,CAAClD,QAAQ,CAACxB,WAAW,CAACuE,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CACtE,CAAC;MAED/C,OAAO,CAACC,GAAG,CAAC,WAAW4C,YAAY,CAACzC,MAAM,iCAAiC,CAAC;;MAE5E;MACA,MAAM4C,UAAU,GAAGH,YAAY,CAACI,IAAI,CAACH,IAAI,IACvCA,IAAI,CAACrE,OAAO,CAACsE,WAAW,CAAC,CAAC,KAAK1E,WAAW,CAACuE,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAChE,CAAC;MAED,IAAIC,UAAU,EAAE;QACdhD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE+C,UAAU,CAAC;;QAEzD;QACA,IAAIA,UAAU,CAACE,mBAAmB,KAAK,YAAY,IAAKF,UAAU,CAACG,eAAe,IAAIH,UAAU,CAACG,eAAe,GAAG,CAAE,EAAE;UACrHnD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE+C,UAAU,CAAC;UAC/DzD,kBAAkB,CAACyD,UAAU,CAAC;UAC9B3D,wBAAwB,CAAC,IAAI,CAAC;UAC9B3B,cAAc,CAAC,KAAK,CAAC;UACrB;QACF;;QAEA;QACA,IAAIsF,UAAU,CAACI,sBAAsB,KAAK,CAAC,EAAE;UAC3CpD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE+C,UAAU,CAAC;UAC9C;QACF;;QAEA;QACAK,gBAAgB,CAACL,UAAU,CAAC;MAC9B,CAAC,MAAM,IAAIH,YAAY,CAACzC,MAAM,GAAG,CAAC,EAAE;QAClC;QACAtC,gBAAgB,CAAC+E,YAAY,CAAC;QAC9B7E,oBAAoB,CAAC,IAAI,CAAC;MAC5B,CAAC,MAAM;QACL;QACAZ,OAAO,CAAC,oCAAoCiB,WAAW,CAACuE,IAAI,CAAC,CAAC,kBAAkB1F,UAAU,EAAE,CAAC;MAC/F;IACF,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;MAEtD;MACA,IAAIoB,YAAY,GAAG,+BAA+B;MAElD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAAC+C,MAAM,KAAK,GAAG,EAAE;QAC/B3B,YAAY,GAAG,gBAAgBtD,WAAW,CAACuE,IAAI,CAAC,CAAC,8BAA8B1F,UAAU,EAAE;MAC7F,CAAC,MAAM,IAAIqD,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEAvD,OAAO,CAACuE,YAAY,CAAC;IACvB,CAAC,SAAS;MACRjE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM2F,gBAAgB,GAAIP,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACI,mBAAmB,KAAK,YAAY,IAAKJ,IAAI,CAACK,eAAe,IAAIL,IAAI,CAACK,eAAe,GAAG,CAAE,EAAE;MACnG;MACA5D,kBAAkB,CAACuD,IAAI,CAAC;MACxBzD,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IACA;IAAA,KACK,IAAIyD,IAAI,CAACM,sBAAsB,KAAK,CAAC,EAAE;MAC1C;MACA,IAAIG,MAAM,CAACC,OAAO,CAAC,WAAWV,IAAI,CAACrE,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACAgF,eAAe,CAACX,IAAI,CAACrE,OAAO,CAAC,CAACiF,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAGb,IAAI;YAAEM,sBAAsB,EAAE;UAAE,CAAC;UAC1DhF,eAAe,CAACuF,WAAW,CAAC;UAC5BnF,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAEkF,WAAW,CAAClF,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAV,oBAAoB,CAAC,KAAK,CAAC;QAC7B,CAAC,CAAC,CAAC4F,KAAK,CAACrD,KAAK,IAAI;UAChBP,OAAO,CAACO,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvEnD,OAAO,CAAC,kDAAkD,IAAImD,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACAvC,eAAe,CAAC0E,IAAI,CAAC;MACrBtE,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEqE,IAAI,CAACrE,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAV,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMyF,eAAe,GAAG,MAAOI,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAM/H,WAAW,CAAC2H,eAAe,CAACvG,UAAU,EAAE2G,MAAM,CAAC;MACrD1G,SAAS,CAAC,QAAQ0G,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvEnD,OAAO,CAAC,kDAAkD,IAAImD,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMJ,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMuD,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC1F,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACyF,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACAE,aAAa,CAACH,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAACH,IAAI,EAAEC,KAAK,KAAK;IACrC,IAAI1D,KAAK,GAAG,IAAI;IAChB,IAAI6D,OAAO,GAAG,IAAI;IAElB,IAAIJ,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACrB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCrC,KAAK,GAAG,uCAAuC;QAC/C,OAAO,KAAK;MACd;;MAEA;MACA,IAAI8D,KAAK,CAACC,UAAU,CAACL,KAAK,CAAC,CAAC,IAAIK,UAAU,CAACL,KAAK,CAAC,IAAI,CAAC,EAAE;QACtD1D,KAAK,GAAG,sCAAsC;QAC9C,OAAO,KAAK;MACd;MAEA,MAAMgE,WAAW,GAAGD,UAAU,CAACL,KAAK,CAAC;;MAErC;MACA,IAAI9F,YAAY,IAAIA,YAAY,CAACqG,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACnG,YAAY,CAACqG,aAAa,CAAC,EAAE;QACtGJ,OAAO,GAAG,mBAAmBG,WAAW,yCAAyCpG,YAAY,CAACqG,aAAa,IAAI;MACjH;;MAEA;MACA,IAAIjG,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC/D,MAAMsD,MAAM,GAAGhE,MAAM,CAACgF,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC9D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIsD,MAAM,IAAIsC,WAAW,GAAGD,UAAU,CAACrC,MAAM,CAACS,aAAa,CAAC,EAAE;UAC5D0B,OAAO,GAAG,mBAAmBG,WAAW,6CAA6CtC,MAAM,CAACS,aAAa,oCAAoC;QAC/I;MACF;IACF;;IAEA;IACA7D,aAAa,CAAC4F,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACT,IAAI,GAAGzD;IACV,CAAC,CAAC,CAAC;;IAEH;IACAxB,eAAe,CAAC0F,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACT,IAAI,GAAGI;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC7D,KAAK;EACf,CAAC;;EAED;EACA,MAAM,CAACmE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrL,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACsL,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvL,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACwL,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzL,QAAQ,CAAC;IAC3D0L,KAAK,EAAE,EAAE;IACTrE,OAAO,EAAE,EAAE;IACXsE,SAAS,EAAEA,CAAA,KAAM,CAAC;EACpB,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;;IAEnB;IACAV,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACA,IAAI,CAACpG,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAACkE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEwC,MAAM,CAAC1G,YAAY,GAAG,uCAAuC;MAC7DyG,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAId,KAAK,CAACC,UAAU,CAAC/F,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAI4F,UAAU,CAAC/F,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7F0G,MAAM,CAAC1G,YAAY,GAAG,sCAAsC;MAC5DyG,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM;MACL,MAAMZ,WAAW,GAAGD,UAAU,CAAC/F,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAIP,YAAY,IAAIA,YAAY,CAACqG,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACnG,YAAY,CAACqG,aAAa,CAAC,EAAE;QACtGa,QAAQ,CAAC3G,YAAY,GAAG,mBAAmB6F,WAAW,yCAAyCpG,YAAY,CAACqG,aAAa,IAAI;QAC7HG,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5B;QACA;MACF;;MAEA;MACA,IAAIQ,OAAO,IAAI5G,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC1E,MAAMsD,MAAM,GAAGhE,MAAM,CAACgF,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC9D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIsD,MAAM,IAAIsC,WAAW,GAAGD,UAAU,CAACrC,MAAM,CAACS,aAAa,CAAC,EAAE;UAC5D2C,QAAQ,CAAC3G,YAAY,GAAG,mBAAmB6F,WAAW,6CAA6CtC,MAAM,CAACS,aAAa,oCAAoC;UAC3JiC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;;UAE5B;UACAI,qBAAqB,CAAC;YACpBC,KAAK,EAAE,kCAAkC;YACzCrE,OAAO,EAAE,mBAAmB4D,WAAW,6CAA6CtC,MAAM,CAACS,aAAa,8DAA8D;YACtKuC,SAAS,EAAEA,CAAA,KAAM;cACf;cACAK,UAAU,CAAC,CAAC;YACd;UACF,CAAC,CAAC;UACFT,oBAAoB,CAAC,IAAI,CAAC;UAC1B,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;IACF;IAEAhG,aAAa,CAACuG,MAAM,CAAC;IACrBrG,eAAe,CAACsG,QAAQ,CAAC;IACzB,OAAOF,OAAO;EAChB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIC,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAACL,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF,CAAC,MAAM,IAAIK,UAAU,KAAK,CAAC,EAAE;MAC3B;MACA7F,UAAU,CAAC,CAAC;IACd;IAEA8F,aAAa,CAAEC,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBF,aAAa,CAAEC,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxBH,aAAa,CAAC,CAAC,CAAC;IAChBpH,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA;EACA,MAAM6G,2BAA2B,GAAGA,CAACrB,WAAW,EAAEsB,YAAY,KAAK;IACjE,OAAOzJ,mBAAmB,CAACmI,WAAW,EAAEsB,YAAY,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B;IACA;IACA,IAAIlG,QAAQ;IACZ,IAAImG,kBAAkB;IACtB,IAAIxB,WAAW;IACf,IAAIyB,SAAS,GAAG,KAAK;IAErB,IAAI;MACFxI,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAAC0H,YAAY,CAAC,CAAC,EAAE;QACnB1H,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA+G,WAAW,GAAGD,UAAU,CAAC/F,QAAQ,CAACG,YAAY,CAAC;;MAE/C;MACAkB,QAAQ,GAAGrB,QAAQ,CAACI,SAAS;MAC7B;MACA,IAAI,CAACiB,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;QAChCA,QAAQ,GAAG,IAAI;QACfI,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC9D,CAAC,MAAM,IAAIL,QAAQ,KAAK,cAAc,EAAE;QACtCI,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAL,QAAQ,GAAG,cAAc;MAC3B,CAAC,MAAM;QACLI,OAAO,CAACC,GAAG,CAAC,wBAAwBL,QAAQ,EAAE,CAAC;MACjD;;MAEA;MACAmG,kBAAkB,GAAGH,2BAA2B,CAACrB,WAAW,EAAEpG,YAAY,CAACqG,aAAa,CAAC;;MAEzF;MACAwB,SAAS,GAAG,KAAK;;MAEjB;MACA,IAAIpG,QAAQ,KAAK,cAAc,EAAE;QAC/BoG,SAAS,GAAG,IAAI;QAChBhG,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACrD;MACA;MAAA,KACK,IAAIL,QAAQ,IAAIA,QAAQ,KAAK,cAAc,EAAE;QAChD,MAAMqC,MAAM,GAAGhE,MAAM,CAACgF,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC9D,SAAS,KAAKiB,QAAQ,CAAC;QACzD,IAAIqC,MAAM,IAAIsC,WAAW,GAAGD,UAAU,CAACrC,MAAM,CAACS,aAAa,CAAC,EAAE;UAC5DsD,SAAS,GAAG,IAAI;UAChBhG,OAAO,CAACC,GAAG,CAAC,qCAAqCL,QAAQ,iCAAiC,CAAC;QAC7F;MACF;;MAEA;MACA,IAAIzB,YAAY,IAAIA,YAAY,CAACqG,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACnG,YAAY,CAACqG,aAAa,CAAC,EAAE;QACtG;QACAwB,SAAS,GAAG,IAAI;QAChBhG,OAAO,CAACC,GAAG,CAAC,yCAAyCsE,WAAW,sBAAsBpG,YAAY,CAACqG,aAAa,GAAG,CAAC;MACtH;;MAEA;MACAxE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzB/C,UAAU;QACV2G,MAAM,EAAEtF,QAAQ,CAACE,OAAO;QACxB8F,WAAW;QACX3E,QAAQ;QACRoG,SAAS;QACTD;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACrB,iBAAiB,EAAE;QACtB,MAAMuB,cAAc,GAAG,qCAAqC1H,QAAQ,CAACE,OAAO,QAAQ8F,WAAW,WAAW;;QAE1G;QACAQ,qBAAqB,CAAC;UACpBC,KAAK,EAAE,wBAAwB;UAC/BrE,OAAO,EAAEsF,cAAc;UACvBhB,SAAS,EAAE,MAAAA,CAAA,KAAY;YACrB;YACA,IAAI;cACFzH,UAAU,CAAC,IAAI,CAAC;cAEhB,MAAM1B,WAAW,CAACoK,iBAAiB,CACjChJ,UAAU,EACVqB,QAAQ,CAACE,OAAO,EAChB8F,WAAW,EACX3E,QAAQ,EACRoG,SACF,CAAC;;cAED;cACA,IAAIG,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;cAC9F,IAAInG,QAAQ,KAAK,cAAc,EAAE;gBAC/BuG,cAAc,IAAI,iCAAiC;cACrD,CAAC,MAAM,IAAIvG,QAAQ,EAAE;gBACnB,MAAMqC,MAAM,GAAGhE,MAAM,CAACgF,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC9D,SAAS,KAAKiB,QAAQ,CAAC;gBACzD,IAAIqC,MAAM,EAAE;kBACVkE,cAAc,IAAI,gCAAgCvG,QAAQ,EAAE;gBAC9D;cACF;;cAEA;cACAzC,SAAS,CAACgJ,cAAc,CAAC;;cAEzB;cACAR,WAAW,CAAC,CAAC;;cAEb;cACA5F,QAAQ,CAAC,CAAC;YACZ,CAAC,CAAC,OAAOQ,KAAK,EAAE;cACdP,OAAO,CAACO,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;cAEzE;cACA,IAAIX,QAAQ,KAAK,cAAc,IAAIW,KAAK,CAAC6F,OAAO,EAAE;gBAChD;gBACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;gBAC7H5I,SAAS,CAACgJ,cAAc,CAAC;;gBAEzB;gBACAR,WAAW,CAAC,CAAC;;gBAEb;gBACA5F,QAAQ,CAAC,CAAC;gBACV;cACF;;cAEA;cACA,IAAI4B,YAAY,GAAG,kDAAkD;cAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;gBAAA,IAAA4F,oBAAA;gBAClB;gBACA,MAAM/C,MAAM,GAAG/C,KAAK,CAACE,QAAQ,CAAC6C,MAAM;gBACpC,MAAM1B,MAAM,GAAG,EAAAyE,oBAAA,GAAA9F,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAA4E,oBAAA,uBAAnBA,oBAAA,CAAqBzE,MAAM,KAAIrB,KAAK,CAACI,OAAO;gBAE3D,IAAI2C,MAAM,KAAK,GAAG,EAAE;kBAClB;kBACA,IAAI1B,MAAM,CAAC/B,QAAQ,CAAC,eAAe,CAAC,EAAE;oBACpC8B,YAAY,GAAG,uGAAuG;kBACxH,CAAC,MAAM,IAAIC,MAAM,CAAC/B,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACxC8B,YAAY,GAAG,4EAA4E;kBAC7F,CAAC,MAAM;oBACLA,YAAY,GAAGC,MAAM;kBACvB;gBACF,CAAC,MAAM,IAAI0B,MAAM,KAAK,GAAG,EAAE;kBACzB;kBACA,IAAI1D,QAAQ,KAAK,cAAc,IAAIgC,MAAM,CAAC/B,QAAQ,CAAC,aAAa,CAAC,EAAE;oBACjE,IAAIsG,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;oBAC7H5I,SAAS,CAACgJ,cAAc,CAAC;;oBAEzB;oBACAR,WAAW,CAAC,CAAC;;oBAEb;oBACA5F,QAAQ,CAAC,CAAC;oBACV;kBACF,CAAC,MAAM;oBACL4B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;kBACvD;gBACF,CAAC,MAAM;kBACLD,YAAY,GAAG,sBAAsB2B,MAAM,MAAM1B,MAAM,EAAE;gBAC3D;cACF,CAAC,MAAM,IAAIrB,KAAK,CAAC+F,OAAO,EAAE;gBACxB;gBACA3E,YAAY,GAAG,+DAA+D;cAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAIhC,QAAQ,KAAK,cAAc,EAAE;gBACtD;gBACA+B,YAAY,GAAGpB,KAAK,CAACqB,MAAM;gBAC3B,IAAIrB,KAAK,CAAC+C,MAAM,KAAK,GAAG,IAAI/C,KAAK,CAAC6F,OAAO,EAAE;kBACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;kBAC7H5I,SAAS,CAACgJ,cAAc,CAAC;;kBAEzB;kBACAR,WAAW,CAAC,CAAC;;kBAEb;kBACA5F,QAAQ,CAAC,CAAC;kBACV;gBACF;cACF,CAAC,MAAM;gBACL;gBACA4B,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;cACtE;cAEAxE,OAAO,CAACuE,YAAY,CAAC;YACvB,CAAC,SAAS;cACRnE,UAAU,CAAC,KAAK,CAAC;YACnB;UACF;QACF,CAAC,CAAC;QACFqH,oBAAoB,CAAC,IAAI,CAAC;QAC1BrH,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACAwC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE/C,UAAU,CAAC;MACxC8C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE1B,QAAQ,CAACE,OAAO,CAAC;MAC3CuB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEsE,WAAW,CAAC;MAC3CvE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEL,QAAQ,EAAE,OAAOA,QAAQ,CAAC;MACtDI,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE+F,SAAS,CAAC;MAEtC,MAAMlK,WAAW,CAACoK,iBAAiB,CACjChJ,UAAU,EACVqB,QAAQ,CAACE,OAAO,EAChB8F,WAAW,EACX3E,QAAQ,EACRoG,SACF,CAAC;;MAED;MACA,IAAIG,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;MAC9F,IAAInG,QAAQ,KAAK,cAAc,EAAE;QAC/BuG,cAAc,IAAI,iCAAiC;MACrD,CAAC,MAAM,IAAIvG,QAAQ,EAAE;QACnB,MAAMqC,MAAM,GAAGhE,MAAM,CAACgF,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC9D,SAAS,KAAKiB,QAAQ,CAAC;QACzD,IAAIqC,MAAM,EAAE;UACVkE,cAAc,IAAI,gCAAgCvG,QAAQ,EAAE;QAC9D;MACF;;MAEA;MACAzC,SAAS,CAACgJ,cAAc,CAAC;;MAEzB;MACAR,WAAW,CAAC,CAAC;;MAEb;MACA5F,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAIX,QAAQ,KAAK,cAAc,IAAIW,KAAK,CAAC6F,OAAO,EAAE;QAChD;QACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;QAC7H5I,SAAS,CAACgJ,cAAc,CAAC;;QAEzB;QACAR,WAAW,CAAC,CAAC;;QAEb;QACA5F,QAAQ,CAAC,CAAC;QACV;MACF;;MAEA;MACA,IAAI4B,YAAY,GAAG,kDAAkD;MAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;QAAA,IAAA8F,qBAAA;QAClB;QACA,MAAMjD,MAAM,GAAG/C,KAAK,CAACE,QAAQ,CAAC6C,MAAM;QACpC,MAAM1B,MAAM,GAAG,EAAA2E,qBAAA,GAAAhG,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAA8E,qBAAA,uBAAnBA,qBAAA,CAAqB3E,MAAM,KAAIrB,KAAK,CAACI,OAAO;QAE3D,IAAI2C,MAAM,KAAK,GAAG,EAAE;UAClB;UACA,IAAI1B,MAAM,CAAC/B,QAAQ,CAAC,eAAe,CAAC,EAAE;YACpC8B,YAAY,GAAG,uGAAuG;UACxH,CAAC,MAAM,IAAIC,MAAM,CAAC/B,QAAQ,CAAC,YAAY,CAAC,EAAE;YACxC8B,YAAY,GAAG,4EAA4E;UAC7F,CAAC,MAAM;YACLA,YAAY,GAAGC,MAAM;UACvB;QACF,CAAC,MAAM,IAAI0B,MAAM,KAAK,GAAG,EAAE;UACzB;UACA,IAAI1D,QAAQ,KAAK,cAAc,IAAIgC,MAAM,CAAC/B,QAAQ,CAAC,aAAa,CAAC,EAAE;YACjE,IAAIsG,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;YAC7H5I,SAAS,CAACgJ,cAAc,CAAC;;YAEzB;YACAR,WAAW,CAAC,CAAC;;YAEb;YACA5F,QAAQ,CAAC,CAAC;YACV;UACF,CAAC,MAAM;YACL4B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;UACvD;QACF,CAAC,MAAM;UACLD,YAAY,GAAG,sBAAsB2B,MAAM,MAAM1B,MAAM,EAAE;QAC3D;MACF,CAAC,MAAM,IAAIrB,KAAK,CAAC+F,OAAO,EAAE;QACxB;QACA3E,YAAY,GAAG,+DAA+D;MAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAIhC,QAAQ,KAAK,cAAc,EAAE;QACtD;QACA+B,YAAY,GAAGpB,KAAK,CAACqB,MAAM;QAC3B,IAAIrB,KAAK,CAAC+C,MAAM,KAAK,GAAG,IAAI/C,KAAK,CAAC6F,OAAO,EAAE;UACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;UAC7H5I,SAAS,CAACgJ,cAAc,CAAC;;UAEzB;UACAR,WAAW,CAAC,CAAC;;UAEb;UACA5F,QAAQ,CAAC,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL;QACA4B,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;MACtE;MAEAxE,OAAO,CAACuE,YAAY,CAAC;IACvB,CAAC,SAAS;MACRnE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgJ,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACE1J,OAAA,CAACrD,GAAG;MAAAgN,QAAA,gBACF3J,OAAA,CAACnD,UAAU;QAAC+M,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbjK,OAAA,CAACpD,KAAK;QAACsN,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzB3J,OAAA,CAACnD,UAAU;UAAC+M,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjK,OAAA,CAAChD,IAAI;UAACqN,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAZ,QAAA,gBAC7C3J,OAAA,CAAChD,IAAI;YAACwN,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACf3J,OAAA,CAAClD,SAAS;cACR4N,SAAS;cACTC,KAAK,EAAC,SAAS;cACff,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAE5F,WAAY;cACnBqJ,QAAQ,EAAG3D,CAAC,IAAKzF,cAAc,CAACyF,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAChD0D,WAAW,EAAC;YAAyB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjK,OAAA,CAAChD,IAAI;YAACwN,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACf3J,OAAA,CAACjD,MAAM;cACL2N,SAAS;cACTd,OAAO,EAAC,WAAW;cACnBkB,KAAK,EAAC,SAAS;cACfC,OAAO,EAAElF,oBAAqB;cAC9BmF,QAAQ,EAAErK,WAAW,IAAI,CAACY,WAAW,CAACuE,IAAI,CAAC,CAAE;cAC7CmF,SAAS,EAAEtK,WAAW,gBAAGX,OAAA,CAACzC,gBAAgB;gBAAC2N,IAAI,EAAE;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGjK,OAAA,CAAC1B,UAAU;gBAAAwL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAN,QAAA,EAC1E;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGRjK,OAAA,CAACpD,KAAK;QAACsN,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClB3J,OAAA,CAACnD,UAAU;UAAC+M,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZtJ,WAAW,gBACVX,OAAA,CAACrD,GAAG;UAACuN,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5D3J,OAAA,CAACzC,gBAAgB;YAAAuM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJqB,IAAI,CAAChI,MAAM,KAAK,CAAC,gBACnBtD,OAAA,CAAC1C,KAAK;UAACiO,QAAQ,EAAC,MAAM;UAAA5B,QAAA,EAAC;QAEvB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAERjK,OAAA,CAACwL,IAAI;UAACtB,EAAE,EAAE;YAAEuB,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA/B,QAAA,EAChD2B,IAAI,CAACK,GAAG,CAAE3F,IAAI,iBACbhG,OAAA,CAACzD,KAAK,CAAC0D,QAAQ;YAAA0J,QAAA,gBACb3J,OAAA,CAAC4L,QAAQ;cAACC,MAAM;cAACd,OAAO,EAAEA,CAAA,KAAMxE,gBAAgB,CAACP,IAAI,CAAE;cAAA2D,QAAA,gBACrD3J,OAAA,CAAC8L,YAAY;gBACXC,OAAO,eACL/L,OAAA,CAACrD,GAAG;kBAACuN,EAAE,EAAE;oBAAEiB,OAAO,EAAE,MAAM;oBAAEZ,UAAU,EAAE;kBAAS,CAAE;kBAAAZ,QAAA,gBACjD3J,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,WAAW;oBAAAD,QAAA,EAAE3D,IAAI,CAACrE;kBAAO;oBAAAmI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,EAC1DxK,YAAY,CAACuG,IAAI,CAAC,gBACjBhG,OAAA,CAACtC,IAAI;oBACHwN,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,OAAO;oBACbG,KAAK,EAAC,OAAO;oBACbZ,EAAE,EAAE;sBAAE8B,EAAE,EAAE;oBAAE;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,GACAvK,gBAAgB,CAACsG,IAAI,CAAC,gBACxBhG,OAAA,CAACtC,IAAI;oBACHwN,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,YAAY;oBAClBG,KAAK,EAAC,SAAS;oBACfZ,EAAE,EAAE;sBAAE8B,EAAE,EAAE;oBAAE;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,gBAEFjK,OAAA,CAACtC,IAAI;oBACHwN,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAE3E,IAAI,CAACI,mBAAoB;oBAChC0E,KAAK,EAAEnL,kBAAkB,CAACqG,IAAI,CAACI,mBAAmB,CAAE;oBACpD8D,EAAE,EAAE;sBAAE8B,EAAE,EAAE;oBAAE;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACDgC,SAAS,eACPjM,OAAA,CAAAE,SAAA;kBAAAyJ,QAAA,gBACE3J,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAACsC,SAAS,EAAC,MAAM;oBAAAvC,QAAA,GACzC3D,IAAI,CAACX,SAAS,IAAI,KAAK,EAAC,KAAG,EAACW,IAAI,CAACV,YAAY,IAAI,KAAK,EAAC,KAAG,EAACU,IAAI,CAACT,OAAO,IAAI,KAAK;kBAAA;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACbjK,OAAA;oBAAA8J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNjK,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAACsC,SAAS,EAAC,MAAM;oBAAAvC,QAAA,GAAC,MACvC,EAAC3D,IAAI,CAACmG,mBAAmB,IAAI,KAAK,EAAC,QAAM,EAACnG,IAAI,CAACoG,iBAAiB,IAAI,KAAK;kBAAA;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACbjK,OAAA;oBAAA8J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNjK,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAACsC,SAAS,EAAC,MAAM;oBAAAvC,QAAA,GAAC,iBAC5B,EAAC3D,IAAI,CAAC0B,aAAa,IAAI,KAAK,EAAC,mBAAiB,EAAC1B,IAAI,CAACK,eAAe,IAAI,GAAG;kBAAA;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC;gBAAA,eACb;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFjK,OAAA,CAACqM,uBAAuB;gBAAA1C,QAAA,eACtB3J,OAAA,CAACvC,UAAU;kBAAC6O,IAAI,EAAC,KAAK;kBAACvB,OAAO,EAAG9D,CAAC,IAAK;oBACrCA,CAAC,CAACsF,eAAe,CAAC,CAAC,CAAC,CAAC;oBACrBjL,eAAe,CAAC0E,IAAI,CAAC;oBACrBrD,wBAAwB,CAAC,IAAI,CAAC;kBAChC,CAAE;kBAAAgH,QAAA,eACA3J,OAAA,CAAClB,QAAQ;oBAAAgL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACXjK,OAAA,CAAC3C,OAAO;cAAAyM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAxDQjE,IAAI,CAACrE,OAAO;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyDjB,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMuC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACnL,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACErB,OAAA,CAACrD,GAAG;MAAAgN,QAAA,gBACF3J,OAAA,CAACnD,UAAU;QAAC+M,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbjK,OAAA,CAACb,eAAe;QACd6G,IAAI,EAAE3E,YAAa;QACnBoL,OAAO,EAAE,IAAK;QACdvE,KAAK,EAAC;MAA+B;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAEFjK,OAAA,CAACpD,KAAK;QAACsN,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClB3J,OAAA,CAACnD,UAAU;UAAC+M,OAAO,EAAC,WAAW;UAACC,YAAY;UAACK,EAAE,EAAE;YAAEwC,UAAU,EAAE;UAAO,CAAE;UAAA/C,QAAA,EAAC;QAEzE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGbjK,OAAA,CAAChD,IAAI;UAACqN,SAAS;UAACC,OAAO,EAAE,CAAE;UAACJ,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACxC3J,OAAA,CAAChD,IAAI;YAACwN,IAAI;YAACC,EAAE,EAAE,EAAG;YAACkC,EAAE,EAAE,CAAE;YAAAhD,QAAA,eACvB3J,OAAA,CAACrD,GAAG;cAACuN,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEyC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAO,CAAE;cAAAnD,QAAA,gBACrE3J,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEwC,UAAU,EAAE,MAAM;kBAAE5B,KAAK,EAAE;gBAAe,CAAE;gBAAAnB,QAAA,EAAC;cAEhG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjK,OAAA,CAAChD,IAAI;gBAACqN,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAX,QAAA,gBACzB3J,OAAA,CAAChD,IAAI;kBAACwN,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACf3J,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAACM,EAAE,EAAE;sBAAEwC,UAAU,EAAE;oBAAS,CAAE;oBAAA/C,QAAA,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACPjK,OAAA,CAAChD,IAAI;kBAACwN,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACf3J,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAEtI,YAAY,CAACqG,aAAa,IAAI,KAAK,EAAC,IAAE;kBAAA;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eACPjK,OAAA,CAAChD,IAAI;kBAACwN,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACf3J,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAACM,EAAE,EAAE;sBAAEwC,UAAU,EAAE;oBAAS,CAAE;oBAAA/C,QAAA,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACPjK,OAAA,CAAChD,IAAI;kBAACwN,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACf3J,OAAA,CAACtC,IAAI;oBACHiN,KAAK,EAAEtJ,YAAY,CAAC+E,mBAAmB,IAAI,KAAM;oBACjD8E,IAAI,EAAC,OAAO;oBACZJ,KAAK,EAAEnL,kBAAkB,CAAC0B,YAAY,CAAC+E,mBAAmB,CAAE;oBAC5DwD,OAAO,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPjK,OAAA,CAAChD,IAAI;YAACwN,IAAI;YAACC,EAAE,EAAE,EAAG;YAACkC,EAAE,EAAE,CAAE;YAAAhD,QAAA,eACvB3J,OAAA,CAACrD,GAAG;cAACuN,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEyC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAO,CAAE;cAAAnD,QAAA,gBACrE3J,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEwC,UAAU,EAAE,MAAM;kBAAE5B,KAAK,EAAE;gBAAiB,CAAE;gBAAAnB,QAAA,EAAC;cAElG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZxI,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,CAAC,MAAM;gBACpE,MAAMsD,MAAM,GAAGhE,MAAM,CAACgF,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC9D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;gBACnE,OAAOsD,MAAM,gBACXnF,OAAA,CAAChD,IAAI;kBAACqN,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAX,QAAA,gBACzB3J,OAAA,CAAChD,IAAI;oBAACwN,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACf3J,OAAA,CAACnD,UAAU;sBAAC+M,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEwC,UAAU,EAAE;sBAAS,CAAE;sBAAA/C,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC,eACPjK,OAAA,CAAChD,IAAI;oBAACwN,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACf3J,OAAA,CAACnD,UAAU;sBAAC+M,OAAO,EAAC,OAAO;sBAAAD,QAAA,EAAE9G,eAAe,CAACsC,MAAM,CAACtD,SAAS;oBAAC;sBAAAiI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC,eACPjK,OAAA,CAAChD,IAAI;oBAACwN,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACf3J,OAAA,CAACnD,UAAU;sBAAC+M,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEwC,UAAU,EAAE;sBAAS,CAAE;sBAAA/C,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC,eACPjK,OAAA,CAAChD,IAAI;oBAACwN,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACf3J,OAAA,CAACnD,UAAU;sBAAC+M,OAAO,EAAC,OAAO;sBAAAD,QAAA,GAAExE,MAAM,CAACS,aAAa,IAAI,CAAC,EAAC,IAAE;oBAAA;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACPjK,OAAA,CAAChD,IAAI;oBAACwN,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACf3J,OAAA,CAACnD,UAAU;sBAAC+M,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEwC,UAAU,EAAE;sBAAS,CAAE;sBAAA/C,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eACPjK,OAAA,CAAChD,IAAI;oBAACwN,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACf3J,OAAA,CAACtC,IAAI;sBACHiN,KAAK,EAAExF,MAAM,CAACC,YAAY,IAAI,KAAM;sBACpC8F,IAAI,EAAC,OAAO;sBACZJ,KAAK,EAAElL,iBAAiB,CAACuF,MAAM,CAACC,YAAY,CAAE;sBAC9CwE,OAAO,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEPjK,OAAA,CAACnD,UAAU;kBAAC+M,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAC3D;cACH,CAAC,EAAE,CAAC,gBACFjK,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAAD,QAAA,EACxBlI,QAAQ,CAACI,SAAS,KAAK,cAAc,GACpC,kDAAkD,GAClD;cAA4B;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPjK,OAAA,CAACrD,GAAG;UAACuN,EAAE,EAAE;YAAE6C,EAAE,EAAE,CAAC;YAAE3C,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACxB3J,OAAA,CAACnD,UAAU;YAAC+M,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEwC,UAAU,EAAE;YAAO,CAAE;YAAA/C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjK,OAAA,CAAClD,SAAS;YACRoO,IAAI,EAAC,OAAO;YACZR,SAAS;YACTC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClB1C,IAAI,EAAC,cAAc;YACnB8F,IAAI,EAAC,QAAQ;YACb7F,KAAK,EAAE1F,QAAQ,CAACG,YAAa;YAC7BgJ,QAAQ,EAAE5D,gBAAiB;YAC3BvD,KAAK,EAAE,CAAC,CAAC3B,UAAU,CAACF,YAAa;YACjCqL,UAAU,EAAEnL,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjEsL,mBAAmB,EAAE;cACnBhD,EAAE,EAAE;gBAAEY,KAAK,EAAE9I,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACFsI,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELjI,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,IAAI,CAACgG,iBAAiB,iBAC1E5H,OAAA,CAAC1C,KAAK;UAACiO,QAAQ,EAAC,SAAS;UAACrB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EACrC3H,YAAY,CAACJ;QAAY;UAAAkI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACR,eAEDjK,OAAA,CAAC1C,KAAK;UAACiO,QAAQ,EAAC,MAAM;UAACrB,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,eACnC3J,OAAA,CAACnD,UAAU;YAAC+M,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAC;UAE5B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMkD,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,MAAMC,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAIjN,UAAU,KAAKiN,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAInI,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAAC1D,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAO4F,UAAU,CAACrC,MAAM,CAACS,aAAa,CAAC,IAAI4B,UAAU,CAAC/F,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAM2L,uBAAuB,GAAItG,CAAC,IAAK;MACrC,MAAMoG,YAAY,GAAGpG,CAAC,CAACG,MAAM,CAACD,KAAK,CAACrB,IAAI,CAAC,CAAC;;MAE1C;MACA,IAAIuH,YAAY,CAACpH,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtCvE,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb0L,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIH,YAAY,EAAE;QAChB;QACA,MAAMI,gBAAgB,GAAGL,iBAAiB,CAACC,YAAY,CAAC;;QAExD;QACA,MAAMK,eAAe,GAAGvM,MAAM,CAACgF,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC9D,SAAS,KAAK4L,gBAAgB,CAAC;QAE1E,IAAIC,eAAe,EAAE;UACnB;UACA,IAAIA,eAAe,CAACtI,YAAY,KAAK,MAAM,IAAIsI,eAAe,CAACtI,YAAY,KAAK,WAAW,EAAE;YAC3FrD,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb0L,eAAe,EAAE,aAAaH,YAAY,eAAeK,eAAe,CAACtI,YAAY;YACvF,CAAC,CAAC;YACF;UACF;;UAEA;UACA,IAAI/D,YAAY,KACZqM,eAAe,CAACrI,SAAS,KAAKhE,YAAY,CAACgE,SAAS,IACpDsI,MAAM,CAACD,eAAe,CAACpI,YAAY,CAAC,KAAKqI,MAAM,CAACtM,YAAY,CAACiE,YAAY,CAAC,IAC1EqI,MAAM,CAACD,eAAe,CAACnI,OAAO,CAAC,KAAKoI,MAAM,CAACtM,YAAY,CAACkE,OAAO,CAAC,CACnE,EAAE;YACD;YACAlD,mBAAmB,CAACqL,eAAe,CAAC;YACpCvL,6BAA6B,CAAC,IAAI,CAAC;YACnC;UACF;;UAEA;UACA,IAAImL,mBAAmB,CAACI,eAAe,CAAC,EAAE;YACxC;YACAhM,WAAW,CAAC;cACV,GAAGD,QAAQ;cACXI,SAAS,EAAE4L;YACb,CAAC,CAAC;YACF1L,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb0L,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACAzL,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb0L,eAAe,EAAE,aAAaH,YAAY,sCAAsCK,eAAe,CAAC9H,aAAa;YAC/G,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACA7D,aAAa,CAAC;YACZ,GAAGD,UAAU;YACb0L,eAAe,EAAE,UAAUH,YAAY;UACzC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACA3L,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb0L,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,oBACExN,OAAA,CAACrD,GAAG;MAAAgN,QAAA,gBACF3J,OAAA,CAACnD,UAAU;QAAC+M,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbjK,OAAA,CAACpD,KAAK;QAACsN,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClB3J,OAAA,CAACnD,UAAU;UAAC+M,OAAO,EAAC,OAAO;UAACgE,SAAS;UAAAjE,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZpJ,aAAa,gBACZb,OAAA,CAACrD,GAAG;UAACuN,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5D3J,OAAA,CAACzC,gBAAgB;YAAAuM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAENjK,OAAA,CAACrD,GAAG;UAAAgN,QAAA,gBACF3J,OAAA,CAAChD,IAAI;YAACqN,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBAEzB3J,OAAA,CAAChD,IAAI;cAACwN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkC,EAAE,EAAE,CAAE;cAAAhD,QAAA,gBACvB3J,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEwC,UAAU,EAAE;gBAAO,CAAE;gBAAA/C,QAAA,EAAC;cAEzE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjK,OAAA,CAAClD,SAAS;gBACRoO,IAAI,EAAC,OAAO;gBACZR,SAAS;gBACTC,KAAK,EAAC,eAAe;gBACrBf,OAAO,EAAC,UAAU;gBAClBiB,WAAW,EAAC,oBAAoB;gBAChCoC,UAAU,EAAEnL,UAAU,CAAC0L,eAAe,IAAI,uCAAwC;gBAClF/J,KAAK,EAAE,CAAC,CAAC3B,UAAU,CAAC0L,eAAgB;gBACpCK,MAAM,EAAEN,uBAAwB;gBAChCrD,EAAE,EAAE;kBAAEE,EAAE,EAAE;gBAAE;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACFjK,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAACM,EAAE,EAAE;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAT,QAAA,GAAC,aAC9B,eAAA3J,OAAA;kBAAA2J,QAAA,EAASlI,QAAQ,CAACI,SAAS,IAAI;gBAAG;kBAAAiI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAGPjK,OAAA,CAAChD,IAAI;cAACwN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkC,EAAE,EAAE,CAAE;cAAAhD,QAAA,gBACvB3J,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEwC,UAAU,EAAE;gBAAO,CAAE;gBAAA/C,QAAA,EAAC;cAEzE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjK,OAAA,CAAC/C,WAAW;gBAACyN,SAAS;gBAACQ,IAAI,EAAC,OAAO;gBAAAvB,QAAA,gBACjC3J,OAAA,CAAC9C,UAAU;kBAAC4Q,EAAE,EAAC,qBAAqB;kBAAAnE,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClEjK,OAAA,CAAC7C,MAAM;kBACL4Q,OAAO,EAAC,qBAAqB;kBAC7BD,EAAE,EAAC,eAAe;kBAClB5G,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAE1F,QAAQ,CAACI,SAAU;kBAC1B8I,KAAK,EAAC,kBAAkB;kBACxBC,QAAQ,EAAE5D,gBAAiB;kBAAA2C,QAAA,gBAE3B3J,OAAA,CAAC5C,QAAQ;oBAAC+J,KAAK,EAAC,cAAc;oBAAAwC,QAAA,gBAC5B3J,OAAA;sBAAA2J,QAAA,EAAQ;oBAAY;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,+BAC/B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACXjK,OAAA,CAAC3C,OAAO;oBAAAyM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACV9I,MAAM,CAACwK,GAAG,CAAExG,MAAM,iBACjBnF,OAAA,CAAC5C,QAAQ;oBAEP+J,KAAK,EAAEhC,MAAM,CAACtD,SAAU;oBACxBmJ,QAAQ,EAAE7F,MAAM,CAACS,aAAa,GAAG4B,UAAU,CAAC/F,QAAQ,CAACG,YAAY,CAAE;oBAAA+H,QAAA,GAElE9G,eAAe,CAACsC,MAAM,CAACtD,SAAS,CAAC,EAAC,KAAG,EAACsD,MAAM,CAACE,SAAS,IAAI,KAAK,EAAC,KAAG,EAACF,MAAM,CAACS,aAAa,IAAI,CAAC,EAAC,IACjG;kBAAA,GALOT,MAAM,CAACtD,SAAS;oBAAAiI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAKb,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACTjK,OAAA,CAACxC,cAAc;kBAAAmM,QAAA,EAAC;gBAEhB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPjK,OAAA,CAAC1C,KAAK;YAACiO,QAAQ,EAAC,MAAM;YAACrB,EAAE,EAAE;cAAE6C,EAAE,EAAE;YAAE,CAAE;YAAApD,QAAA,eACnC3J,OAAA,CAACnD,UAAU;cAAC+M,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB3J,OAAA;gBAAA2J,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,iGACvB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,EAGA,CAACpJ,aAAa,IAAIY,QAAQ,CAACI,SAAS,iBACnC7B,OAAA,CAACrD,GAAG;UAACuN,EAAE,EAAE;YAAE6C,EAAE,EAAE,CAAC;YAAE5C,CAAC,EAAE,CAAC;YAAEyC,OAAO,EAAE,kBAAkB;YAAEC,YAAY,EAAE,CAAC;YAAEmB,MAAM,EAAE;UAAoB,CAAE;UAAArE,QAAA,gBAClG3J,OAAA,CAACnD,UAAU;YAAC+M,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAF,QAAA,EAAC;UAE7C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ,CAAC,MAAM;YACN,MAAM9E,MAAM,GAAGhE,MAAM,CAACgF,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC9D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,IAAIsD,MAAM,EAAE;cACV,oBACEnF,OAAA,CAAChD,IAAI;gBAACqN,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAX,QAAA,gBACzB3J,OAAA,CAAChD,IAAI;kBAACwN,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACkC,EAAE,EAAE,CAAE;kBAAAhD,QAAA,gBACvB3J,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzB3J,OAAA;sBAAA2J,QAAA,EAAQ;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACpH,eAAe,CAACsC,MAAM,CAACtD,SAAS,CAAC;kBAAA;oBAAAiI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACbjK,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzB3J,OAAA;sBAAA2J,QAAA,EAAQ;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC9E,MAAM,CAACE,SAAS,IAAI,KAAK;kBAAA;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACbjK,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzB3J,OAAA;sBAAA2J,QAAA,EAAQ;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC9E,MAAM,CAACG,YAAY,IAAI,KAAK,EAAC,KAAG,EAACH,MAAM,CAACI,OAAO,IAAI,KAAK;kBAAA;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACPjK,OAAA,CAAChD,IAAI;kBAACwN,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACkC,EAAE,EAAE,CAAE;kBAAAhD,QAAA,gBACvB3J,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzB3J,OAAA;sBAAA2J,QAAA,EAAQ;oBAAa;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC9E,MAAM,CAAC8I,YAAY,IAAI,CAAC,EAAC,IAC3D;kBAAA;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbjK,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzB3J,OAAA;sBAAA2J,QAAA,EAAQ;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC9E,MAAM,CAACS,aAAa,IAAI,CAAC,EAAC,IAC7D;kBAAA;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbjK,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzB3J,OAAA;sBAAA2J,QAAA,EAAQ;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC9E,MAAM,CAACC,YAAY,IAAI,KAAK;kBAAA;oBAAA0E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAEX;YACA,oBACEjK,OAAA,CAACnD,UAAU;cAAC+M,OAAO,EAAC,OAAO;cAACkB,KAAK,EAAC,OAAO;cAAAnB,QAAA,EAAC;YAE1C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAEjB,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEA9I,MAAM,CAACmC,MAAM,KAAK,CAAC,IAAI,CAACzC,aAAa,iBACpCb,OAAA,CAAC1C,KAAK;UAACiO,QAAQ,EAAC,SAAS;UAACrB,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,EAAC;QAEzC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMiE,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,IAAIb,YAAY,GAAG,SAAS;IAC5B,IAAIc,UAAU,GAAG,IAAI;IAErB,IAAI1M,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzCwL,YAAY,GAAG,cAAc;IAC/B,CAAC,MAAM,IAAI5L,QAAQ,CAACI,SAAS,EAAE;MAC7BwL,YAAY,GAAGxK,eAAe,CAACpB,QAAQ,CAACI,SAAS,CAAC;MAClD;MACAsM,UAAU,GAAGhN,MAAM,CAACgF,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC9D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IACnE;;IAEA;IACA,MAAMoH,kBAAkB,GAAGH,2BAA2B,CAACtB,UAAU,CAAC/F,QAAQ,CAACG,YAAY,CAAC,EAAEP,YAAY,CAACqG,aAAa,CAAC;IAErH,oBACE1H,OAAA,CAACrD,GAAG;MAAAgN,QAAA,gBACF3J,OAAA,CAACnD,UAAU;QAAC+M,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbjK,OAAA,CAACpD,KAAK;QAACsN,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClB3J,OAAA,CAACnD,UAAU;UAAC+M,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGbjK,OAAA,CAACb,eAAe;UACd6G,IAAI,EAAE3E,YAAa;UACnBoL,OAAO,EAAE,IAAK;UACdvE,KAAK,EAAC;QAAmB;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGFjK,OAAA,CAACrD,GAAG;UAACuN,EAAE,EAAE;YAAE6C,EAAE,EAAE,CAAC;YAAE5C,CAAC,EAAE,CAAC;YAAEyC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBAC5D3J,OAAA,CAACnD,UAAU;YAAC+M,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEwC,UAAU,EAAE;YAAO,CAAE;YAAA/C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjK,OAAA,CAAChD,IAAI;YAACqN,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBACzB3J,OAAA,CAAChD,IAAI;cAACwN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkC,EAAE,EAAE,CAAE;cAAAhD,QAAA,gBACvB3J,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB3J,OAAA;kBAAA2J,QAAA,EAAQ;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxI,QAAQ,CAACG,YAAY,EAAC,IACxD;cAAA;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjK,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB3J,OAAA;kBAAA2J,QAAA,EAAQ;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChB,kBAAkB;cAAA;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACPjK,OAAA,CAAChD,IAAI;cAACwN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkC,EAAE,EAAE,CAAE;cAAAhD,QAAA,gBACvB3J,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB3J,OAAA;kBAAA2J,QAAA,EAAQ;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACoD,YAAY;cAAA;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACZkE,UAAU,iBACTnO,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB3J,OAAA;kBAAA2J,QAAA,EAAQ;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACkE,UAAU,CAACvI,aAAa,EAAC,IACnE;cAAA;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAELkE,UAAU,IAAI3G,UAAU,CAAC/F,QAAQ,CAACG,YAAY,CAAC,GAAG4F,UAAU,CAAC2G,UAAU,CAACvI,aAAa,CAAC,IAAI,CAACgC,iBAAiB,iBAC3G5H,OAAA,CAAC1C,KAAK;UAACiO,QAAQ,EAAC,SAAS;UAACrB,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,gBACtC3J,OAAA;YAAA2J,QAAA,EAAQ;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qBAAiB,EAACxI,QAAQ,CAACG,YAAY,EAAC,4CAA0C,EAACuM,UAAU,CAACvI,aAAa,EAAC,gDAE1I;QAAA;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAEDjK,OAAA,CAAC1C,KAAK;UAACiO,QAAQ,EAAC,MAAM;UAACrB,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,GAAC,8EAEpC,EAAClI,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,gFAAgF;QAAA;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMmE,cAAc,GAAIC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAO3E,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOyD,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOX,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAO0B,WAAW,CAAC,CAAC;MAAE;MACxB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;;EAED;EACA,MAAMI,4BAA4B,GAAGA,CAAA,KAAM;IACzC/L,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM8L,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI/L,eAAe,EAAE;MACnBhC,QAAQ,CAAC,mCAAmCJ,UAAU,IAAIoC,eAAe,CAACb,OAAO,EAAE,CAAC;IACtF;IACA2M,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,4BAA4B,CAAC,CAAC;IAC9B;IACAhN,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBN,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMuN,iCAAiC,GAAGA,CAAA,KAAM;IAC9CtM,6BAA6B,CAAC,KAAK,CAAC;IACpCE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMqM,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI,CAACrN,YAAY,IAAI,CAACe,gBAAgB,EAAE;IAExC,IAAI;MACF1B,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAM1B,WAAW,CAAC2P,qBAAqB,CAACvO,UAAU,EAAEiB,YAAY,CAACM,OAAO,EAAES,gBAAgB,CAAC;;MAE3F;MACA,MAAMyE,WAAW,GAAG,MAAM7H,WAAW,CAAC4P,WAAW,CAACxO,UAAU,EAAEiB,YAAY,CAACM,OAAO,CAAC;MACnFL,eAAe,CAACuF,WAAW,CAAC;;MAE5B;MACAnF,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXI,SAAS,EAAEO,gBAAgB,CAACP;MAC9B,CAAC,CAAC;MAEFxB,SAAS,CAAC,4BAA4BgB,YAAY,CAACM,OAAO,6CAA6CS,gBAAgB,CAACP,SAAS,EAAE,CAAC;MACpI4M,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOhL,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,iEAAiE,EAAEA,KAAK,CAAC;MACvFnD,OAAO,CAAC,kEAAkE,IAAImD,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACvH,CAAC,SAAS;MACRnD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmO,uBAAuB,GAAGA,CAAA,KAAM;IACpCJ,iCAAiC,CAAC,CAAC;IACnC;IACA/M,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMiN,oBAAoB,GAAGA,CAAA,KAAM;IACjC,oBACE9O,OAAA,CAACrD,GAAG;MAAAgN,QAAA,gBAEF3J,OAAA,CAACpD,KAAK;QAACsN,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzB3J,OAAA,CAACnD,UAAU;UAAC+M,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAF,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjK,OAAA,CAAChD,IAAI;UAACqN,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAZ,QAAA,gBAC7C3J,OAAA,CAAChD,IAAI;YAACwN,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACf3J,OAAA,CAAClD,SAAS;cACR4N,SAAS;cACTC,KAAK,EAAC,SAAS;cACff,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAE5F,WAAY;cACnBqJ,QAAQ,EAAG3D,CAAC,IAAKzF,cAAc,CAACyF,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAChD0D,WAAW,EAAC;YAAyC;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjK,OAAA,CAAChD,IAAI;YAACwN,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACf3J,OAAA,CAACjD,MAAM;cACL2N,SAAS;cACTd,OAAO,EAAC,WAAW;cACnBkB,KAAK,EAAC,SAAS;cACfC,OAAO,EAAElF,oBAAqB;cAC9BmF,QAAQ,EAAErK,WAAW,IAAI,CAACY,WAAW,CAACuE,IAAI,CAAC,CAAE;cAC7CmF,SAAS,EAAEtK,WAAW,gBAAGX,OAAA,CAACzC,gBAAgB;gBAAC2N,IAAI,EAAE;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGjK,OAAA,CAAC1B,UAAU;gBAAAwL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAN,QAAA,EAC1E;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGPhJ,iBAAiB,IAAIF,aAAa,CAACuC,MAAM,GAAG,CAAC,iBAC5CtD,OAAA,CAACpD,KAAK;QAACsN,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzB3J,OAAA,CAACnD,UAAU;UAAC+M,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAF,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjK,OAAA,CAAC9B,cAAc;UAAAyL,QAAA,eACb3J,OAAA,CAACjC,KAAK;YAACmN,IAAI,EAAC,OAAO;YAAAvB,QAAA,gBACjB3J,OAAA,CAAC7B,SAAS;cAAAwL,QAAA,eACR3J,OAAA,CAAC5B,QAAQ;gBAAC8L,EAAE,EAAE;kBAAE0C,OAAO,EAAE;gBAAU,CAAE;gBAAAjD,QAAA,gBACnC3J,OAAA,CAAC/B,SAAS;kBAAA0L,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9BjK,OAAA,CAAC/B,SAAS;kBAAA0L,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChCjK,OAAA,CAAC/B,SAAS;kBAAA0L,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjCjK,OAAA,CAAC/B,SAAS;kBAAA0L,QAAA,EAAC;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjCjK,OAAA,CAAC/B,SAAS;kBAAA0L,QAAA,EAAC;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACpCjK,OAAA,CAAC/B,SAAS;kBAAA0L,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5BjK,OAAA,CAAC/B,SAAS;kBAAA0L,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZjK,OAAA,CAAChC,SAAS;cAAA2L,QAAA,EACP5I,aAAa,CAAC4K,GAAG,CAAE3F,IAAI,iBACtBhG,OAAA,CAAC5B,QAAQ;gBAAAuL,QAAA,gBACP3J,OAAA,CAAC/B,SAAS;kBAAA0L,QAAA,EAAE3D,IAAI,CAACrE;gBAAO;kBAAAmI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCjK,OAAA,CAAC/B,SAAS;kBAAA0L,QAAA,EAAE3D,IAAI,CAACX,SAAS,IAAI;gBAAK;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChDjK,OAAA,CAAC/B,SAAS;kBAAA0L,QAAA,GAAE3D,IAAI,CAACV,YAAY,IAAI,KAAK,EAAC,KAAG,EAACU,IAAI,CAACT,OAAO,IAAI,KAAK;gBAAA;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7EjK,OAAA,CAAC/B,SAAS;kBAAA0L,QAAA,GAAC,MAAI,EAAC3D,IAAI,CAACmG,mBAAmB,IAAI,KAAK,eAACnM,OAAA;oBAAA8J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,OAAG,EAACjE,IAAI,CAACoG,iBAAiB,IAAI,KAAK;gBAAA;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvGjK,OAAA,CAAC/B,SAAS;kBAAA0L,QAAA,GAAE3D,IAAI,CAAC0B,aAAa,IAAI,KAAK,EAAC,IAAE;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACtDjK,OAAA,CAAC/B,SAAS;kBAAA0L,QAAA,eACR3J,OAAA,CAACtC,IAAI;oBACHiN,KAAK,EAAE3E,IAAI,CAACI,mBAAmB,IAAI,KAAM;oBACzC8E,IAAI,EAAC,OAAO;oBACZJ,KAAK,EAAEnL,kBAAkB,CAACqG,IAAI,CAACI,mBAAmB,CAAE;oBACpDwD,OAAO,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZjK,OAAA,CAAC/B,SAAS;kBAAA0L,QAAA,eACR3J,OAAA,CAACjD,MAAM;oBACLmO,IAAI,EAAC,OAAO;oBACZtB,OAAO,EAAC,WAAW;oBACnBkB,KAAK,EAAC,SAAS;oBACfC,OAAO,EAAEA,CAAA,KAAMxE,gBAAgB,CAACP,IAAI,CAAE;oBACtCgF,QAAQ,EAAEtL,gBAAgB,CAACsG,IAAI,CAAE;oBAAA2D,QAAA,EAClC;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,GAxBCjE,IAAI,CAACrE,OAAO;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyBjB,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CACR,EAGA5I,YAAY,iBACXrB,OAAA,CAACpD,KAAK;QAACsN,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClB3J,OAAA,CAACnD,UAAU;UAAC+M,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAF,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGbjK,OAAA,CAACrD,GAAG;UAACuN,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEyC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE,CAAC;YAAEzC,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBAC5D3J,OAAA,CAACnD,UAAU;YAAC+M,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEwC,UAAU,EAAE,MAAM;cAAE5B,KAAK,EAAE;YAAe,CAAE;YAAAnB,QAAA,GAAC,oBAC5E,EAACtI,YAAY,CAACM,OAAO;UAAA;YAAAmI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACbjK,OAAA,CAAChD,IAAI;YAACqN,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBACzB3J,OAAA,CAAChD,IAAI;cAACwN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkC,EAAE,EAAE,CAAE;cAAAhD,QAAA,gBACvB3J,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAAC3J,OAAA;kBAAA2J,QAAA,EAAQ;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5I,YAAY,CAACgE,SAAS,IAAI,KAAK;cAAA;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtGjK,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAAC3J,OAAA;kBAAA2J,QAAA,EAAQ;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5I,YAAY,CAACiE,YAAY,IAAI,KAAK,EAAC,KAAG,EAACjE,YAAY,CAACkE,OAAO,IAAI,KAAK;cAAA;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC5IjK,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAAC3J,OAAA;kBAAA2J,QAAA,EAAQ;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5I,YAAY,CAACqG,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5G,CAAC,eACPjK,OAAA,CAAChD,IAAI;cAACwN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkC,EAAE,EAAE,CAAE;cAAAhD,QAAA,gBACvB3J,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAAC3J,OAAA;kBAAA2J,QAAA,EAAQ;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5I,YAAY,CAAC8K,mBAAmB,IAAI,KAAK;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1HjK,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAAC3J,OAAA;kBAAA2J,QAAA,EAAQ;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC5I,YAAY,CAAC+K,iBAAiB,IAAI,KAAK;cAAA;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtHjK,OAAA,CAACnD,UAAU;gBAAC+M,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB3J,OAAA;kBAAA2J,QAAA,EAAQ;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvBjK,OAAA,CAACtC,IAAI;kBACHiN,KAAK,EAAEtJ,YAAY,CAAC+E,mBAAmB,IAAI,KAAM;kBACjD8E,IAAI,EAAC,OAAO;kBACZJ,KAAK,EAAEnL,kBAAkB,CAAC0B,YAAY,CAAC+E,mBAAmB,CAAE;kBAC5DwD,OAAO,EAAC,UAAU;kBAClBM,EAAE,EAAE;oBAAE8B,EAAE,EAAE;kBAAE;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENjK,OAAA,CAAChD,IAAI;UAACqN,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAX,QAAA,gBAEzB3J,OAAA,CAAChD,IAAI;YAACwN,IAAI;YAACC,EAAE,EAAE,EAAG;YAACkC,EAAE,EAAE,CAAE;YAAAhD,QAAA,gBACvB3J,OAAA,CAACnD,UAAU;cAAC+M,OAAO,EAAC,WAAW;cAACC,YAAY;cAACK,EAAE,EAAE;gBAAEwC,UAAU,EAAE;cAAO,CAAE;cAAA/C,QAAA,EAAC;YAEzE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjK,OAAA,CAAClD,SAAS;cACRoO,IAAI,EAAC,OAAO;cACZR,SAAS;cACTC,KAAK,EAAC,cAAc;cACpBf,OAAO,EAAC,UAAU;cAClB1C,IAAI,EAAC,cAAc;cACnB8F,IAAI,EAAC,QAAQ;cACb7F,KAAK,EAAE1F,QAAQ,CAACG,YAAa;cAC7BgJ,QAAQ,EAAE5D,gBAAiB;cAC3BvD,KAAK,EAAE,CAAC,CAAC3B,UAAU,CAACF,YAAa;cACjCqL,UAAU,EAAEnL,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;cACjEsL,mBAAmB,EAAE;gBACnBhD,EAAE,EAAE;kBAAEY,KAAK,EAAE9I,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;gBAAa;cACrG,CAAE;cACFsI,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,EACDjI,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,iBACpD5B,OAAA,CAAC1C,KAAK;cAACiO,QAAQ,EAAC,SAAS;cAACrB,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,EACrC3H,YAAY,CAACJ;YAAY;cAAAkI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAGPjK,OAAA,CAAChD,IAAI;YAACwN,IAAI;YAACC,EAAE,EAAE,EAAG;YAACkC,EAAE,EAAE,CAAE;YAAAhD,QAAA,gBACvB3J,OAAA,CAACnD,UAAU;cAAC+M,OAAO,EAAC,WAAW;cAACC,YAAY;cAACK,EAAE,EAAE;gBAAEwC,UAAU,EAAE;cAAO,CAAE;cAAA/C,QAAA,EAAC;YAEzE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZpJ,aAAa,gBACZb,OAAA,CAACrD,GAAG;cAACuN,EAAE,EAAE;gBAAEiB,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAA1B,QAAA,eAC5D3J,OAAA,CAACzC,gBAAgB;gBAAAuM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,gBAENjK,OAAA,CAAC/C,WAAW;cAACyN,SAAS;cAACQ,IAAI,EAAC,OAAO;cAAAvB,QAAA,gBACjC3J,OAAA,CAAC9C,UAAU;gBAAC4Q,EAAE,EAAC,qBAAqB;gBAAAnE,QAAA,EAAC;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClEjK,OAAA,CAAC7C,MAAM;gBACL4Q,OAAO,EAAC,qBAAqB;gBAC7BD,EAAE,EAAC,eAAe;gBAClB5G,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAE1F,QAAQ,CAACI,SAAU;gBAC1B8I,KAAK,EAAC,kBAAkB;gBACxBC,QAAQ,EAAE5D,gBAAiB;gBAAA2C,QAAA,gBAE3B3J,OAAA,CAAC5C,QAAQ;kBAAC+J,KAAK,EAAC,cAAc;kBAAAwC,QAAA,gBAC5B3J,OAAA;oBAAA2J,QAAA,EAAQ;kBAAY;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,+BAC/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACXjK,OAAA,CAAC3C,OAAO;kBAAAyM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACV9I,MAAM,CAACwK,GAAG,CAAExG,MAAM,iBACjBnF,OAAA,CAAC5C,QAAQ;kBAEP+J,KAAK,EAAEhC,MAAM,CAACtD,SAAU;kBACxBmJ,QAAQ,EAAE7F,MAAM,CAACS,aAAa,GAAG4B,UAAU,CAAC/F,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;kBAAA+H,QAAA,GAEvE9G,eAAe,CAACsC,MAAM,CAACtD,SAAS,CAAC,EAAC,KAAG,EAACsD,MAAM,CAACE,SAAS,IAAI,KAAK,EAAC,KAAG,EAACF,MAAM,CAACS,aAAa,IAAI,CAAC,EAAC,IACjG;gBAAA,GALOT,MAAM,CAACtD,SAAS;kBAAAiI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAKb,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACTjK,OAAA,CAACxC,cAAc;gBAAAmM,QAAA,EAAC;cAEhB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAgB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACd,EAGA,CAACpJ,aAAa,IAAIY,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,CAAC,MAAM;cACvF,MAAMsD,MAAM,GAAGhE,MAAM,CAACgF,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC9D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;cACnE,IAAIsD,MAAM,EAAE;gBACV,oBACEnF,OAAA,CAACrD,GAAG;kBAACuN,EAAE,EAAE;oBAAE6C,EAAE,EAAE,CAAC;oBAAE5C,CAAC,EAAE,CAAC;oBAAEyC,OAAO,EAAE,SAAS;oBAAEC,YAAY,EAAE;kBAAE,CAAE;kBAAAlD,QAAA,gBAC5D3J,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBAAC3J,OAAA;sBAAA2J,QAAA,EAAQ;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACpH,eAAe,CAACsC,MAAM,CAACtD,SAAS,CAAC;kBAAA;oBAAAiI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eACrGjK,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBAAC3J,OAAA;sBAAA2J,QAAA,EAAQ;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC9E,MAAM,CAACS,aAAa,IAAI,CAAC,EAAC,IAAE;kBAAA;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtGjK,OAAA,CAACnD,UAAU;oBAAC+M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzB3J,OAAA;sBAAA2J,QAAA,EAAQ;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACvBjK,OAAA,CAACtC,IAAI;sBACHiN,KAAK,EAAExF,MAAM,CAACC,YAAY,IAAI,KAAM;sBACpC8F,IAAI,EAAC,OAAO;sBACZJ,KAAK,EAAElL,iBAAiB,CAACuF,MAAM,CAACC,YAAY,CAAE;sBAC9CwE,OAAO,EAAC,UAAU;sBAClBM,EAAE,EAAE;wBAAE8B,EAAE,EAAE;sBAAE;oBAAE;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAEV;cACA,OAAO,IAAI;YACb,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPjK,OAAA,CAACrD,GAAG;UAACuN,EAAE,EAAE;YAAE6C,EAAE,EAAE,CAAC;YAAE5B,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAgB,CAAE;UAAAzB,QAAA,gBACnE3J,OAAA,CAACjD,MAAM;YACL6M,OAAO,EAAC,UAAU;YAClBkB,KAAK,EAAC,WAAW;YACjBC,OAAO,EAAEA,CAAA,KAAM;cACbzJ,eAAe,CAAC,IAAI,CAAC;cACrBI,WAAW,CAAC;gBACVC,OAAO,EAAE,EAAE;gBACXC,YAAY,EAAE,EAAE;gBAChBC,SAAS,EAAE;cACb,CAAC,CAAC;YACJ,CAAE;YACFoJ,SAAS,eAAEjL,OAAA,CAACtB,UAAU;cAAAoL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1Be,QAAQ,EAAEvK,OAAQ;YAAAkJ,QAAA,EACnB;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETjK,OAAA,CAACjD,MAAM;YACL6M,OAAO,EAAC,WAAW;YACnBkB,KAAK,EAAC,SAAS;YACfC,OAAO,EAAE/B,YAAa;YACtB+F,OAAO,eAAE/O,OAAA,CAACxB,QAAQ;cAAAsL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtBe,QAAQ,EAAEvK,OAAO,IAAI,CAACgB,QAAQ,CAACG,YAAa;YAAA+H,QAAA,EAE3ClJ,OAAO,gBAAGT,OAAA,CAACzC,gBAAgB;cAAC2N,IAAI,EAAE;YAAG;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,oBACEjK,OAAA,CAACrD,GAAG;IAAAgN,QAAA,GACDmF,oBAAoB,CAAC,CAAC,eAGvB9O,OAAA,CAACrC,MAAM;MAACqR,IAAI,EAAElH,iBAAkB;MAACmH,OAAO,EAAEA,CAAA,KAAMlH,oBAAoB,CAAC,KAAK,CAAE;MAACmH,QAAQ,EAAC,IAAI;MAACxE,SAAS;MAAAf,QAAA,gBAClG3J,OAAA,CAACpC,WAAW;QAACsM,EAAE,EAAE;UAAE0C,OAAO,EAAE;QAAgB,CAAE;QAAAjD,QAAA,eAC5C3J,OAAA,CAACrD,GAAG;UAACuN,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAE4E,GAAG,EAAE;UAAE,CAAE;UAAAxF,QAAA,gBACzD3J,OAAA,CAACpB,WAAW;YAACkM,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/BjK,OAAA,CAACnD,UAAU;YAAC+M,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAE3B,kBAAkB,CAACE;UAAK;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdjK,OAAA,CAACnC,aAAa;QAAA8L,QAAA,eACZ3J,OAAA,CAACnD,UAAU;UAAC+M,OAAO,EAAC,OAAO;UAACM,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,EACvC3B,kBAAkB,CAACnE;QAAO;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChBjK,OAAA,CAAClC,aAAa;QAAA6L,QAAA,gBACZ3J,OAAA,CAACjD,MAAM;UAACgO,OAAO,EAAEA,CAAA,KAAMhD,oBAAoB,CAAC,KAAK,CAAE;UAAC+C,KAAK,EAAC,WAAW;UAAClB,OAAO,EAAC,UAAU;UAAAD,QAAA,EAAC;QAEzF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjK,OAAA,CAACjD,MAAM;UACLgO,OAAO,EAAEA,CAAA,KAAM;YACbhD,oBAAoB,CAAC,KAAK,CAAC;YAC3BC,kBAAkB,CAACG,SAAS,CAAC,CAAC;UAChC,CAAE;UACF2C,KAAK,EAAC,SAAS;UACflB,OAAO,EAAC,WAAW;UACnBwF,SAAS;UAAAzF,QAAA,EACV;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTjK,OAAA,CAACrC,MAAM;MAACqR,IAAI,EAAE1M,qBAAsB;MAAC2M,OAAO,EAAEX,4BAA6B;MAACY,QAAQ,EAAC,IAAI;MAACxE,SAAS;MAAAf,QAAA,gBACjG3J,OAAA,CAACpC,WAAW;QAACsM,EAAE,EAAE;UAAE0C,OAAO,EAAE;QAAgB,CAAE;QAAAjD,QAAA,eAC5C3J,OAAA,CAACrD,GAAG;UAACuN,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAE4E,GAAG,EAAE;UAAE,CAAE;UAAAxF,QAAA,gBACzD3J,OAAA,CAACpB,WAAW;YAACkM,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/BjK,OAAA,CAACnD,UAAU;YAAC+M,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdjK,OAAA,CAACnC,aAAa;QAAA8L,QAAA,EACXnH,eAAe,iBACdxC,OAAA,CAACrD,GAAG;UAACuN,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,gBACjB3J,OAAA,CAACnD,UAAU;YAAC+M,OAAO,EAAC,OAAO;YAACgE,SAAS;YAAAjE,QAAA,GAAC,UAC5B,eAAA3J,OAAA;cAAA2J,QAAA,EAASnH,eAAe,CAACb;YAAO;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,4BAAqB,EAACzH,eAAe,CAAC6D,eAAe,IAAI,CAAC,EAAC,KAC/G;UAAA;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjK,OAAA,CAACnD,UAAU;YAAC+M,OAAO,EAAC,OAAO;YAACgE,SAAS;YAAAjE,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjK,OAAA,CAACnD,UAAU;YAAC+M,OAAO,EAAC,OAAO;YAACsC,SAAS,EAAC,IAAI;YAAAvC,QAAA,gBACxC3J,OAAA;cAAA2J,QAAA,EAAI;YAAsC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/CjK,OAAA;cAAA2J,QAAA,EAAI;YAAyB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCjK,OAAA;cAAA2J,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBjK,OAAA,CAAClC,aAAa;QAACoM,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEiB,cAAc,EAAE;QAAgB,CAAE;QAAAzB,QAAA,gBAC3D3J,OAAA,CAACjD,MAAM;UAACgO,OAAO,EAAEuD,4BAA6B;UAACxD,KAAK,EAAC,WAAW;UAAAnB,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjK,OAAA,CAACrD,GAAG;UAAAgN,QAAA,gBACF3J,OAAA,CAACjD,MAAM;YAACgO,OAAO,EAAEyD,wBAAyB;YAAC1D,KAAK,EAAC,SAAS;YAACZ,EAAE,EAAE;cAAEmF,EAAE,EAAE;YAAE,CAAE;YAAA1F,QAAA,EAAC;UAE1E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjK,OAAA,CAACjD,MAAM;YAACgO,OAAO,EAAEwD,gBAAiB;YAAC3E,OAAO,EAAC,WAAW;YAACkB,KAAK,EAAC,SAAS;YAAAnB,QAAA,EAAC;UAEvE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTjK,OAAA,CAACd,sBAAsB;MACrB8P,IAAI,EAAE9M,0BAA2B;MACjC+M,OAAO,EAAER,iCAAkC;MAC3CzI,IAAI,EAAE3E,YAAa;MACnB8D,MAAM,EAAE/C,gBAAiB;MACzBkN,YAAY,EAAEZ,2BAA4B;MAC1Ca,mBAAmB,EAAEV;IAAwB;MAAA/E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAGFjK,OAAA,CAACrC,MAAM;MACLqR,IAAI,EAAEtM,qBAAsB;MAC5BuM,OAAO,EAAEA,CAAA,KAAMtM,wBAAwB,CAAC,KAAK,CAAE;MAC/CuM,QAAQ,EAAC,IAAI;MACbxE,SAAS;MAAAf,QAAA,gBAET3J,OAAA,CAACpC,WAAW;QAAA+L,QAAA,eACV3J,OAAA,CAACrD,GAAG;UAACuN,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAE4E,GAAG,EAAE;UAAE,CAAE;UAAAxF,QAAA,gBACzD3J,OAAA,CAAClB,QAAQ;YAACgM,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BjK,OAAA,CAACnD,UAAU;YAAC+M,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdjK,OAAA,CAACnC,aAAa;QAAA8L,QAAA,eACZ3J,OAAA,CAACb,eAAe;UAAC6G,IAAI,EAAE3E;QAAa;UAAAyI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAChBjK,OAAA,CAAClC,aAAa;QAAA6L,QAAA,eACZ3J,OAAA,CAACjD,MAAM;UAACgO,OAAO,EAAEA,CAAA,KAAMpI,wBAAwB,CAAC,KAAK,CAAE;UAACmI,KAAK,EAAC,SAAS;UAAAnB,QAAA,EAAC;QAExE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC1J,EAAA,CAjwDIJ,kBAAkB;EAAA,QACLpB,WAAW;AAAA;AAAAyQ,EAAA,GADxBrP,kBAAkB;AAmwDxB,eAAeA,kBAAkB;AAAC,IAAAqP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}