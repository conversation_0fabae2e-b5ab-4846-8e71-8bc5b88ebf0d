{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\n\n// Assicurati che axios sia disponibile globalmente per i tentativi alternativi\nconst API_URL = config.API_URL;\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null, filters = {}) => {\n    try {\n      console.log('getCavi chiamato con:', {\n        cantiereId,\n        tipoCavo,\n        filters\n      });\n      console.log('Tipo di cantiereId:', typeof cantiereId);\n\n      // Verifica che cantiereId sia definito\n      if (cantiereId === undefined || cantiereId === null) {\n        console.error('cantiereId è undefined o null');\n        throw new Error('ID cantiere mancante');\n      }\n\n      // Assicurati che cantiereId sia un numero\n      let cantiereIdNum = cantiereId;\n      if (typeof cantiereId === 'string') {\n        cantiereIdNum = parseInt(cantiereId, 10);\n        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);\n      }\n      if (isNaN(cantiereIdNum)) {\n        console.error('ID cantiere non è un numero valido:', cantiereId);\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log(`Caricamento cavi per cantiere ${cantiereIdNum} con tipo_cavo=${tipoCavo}`);\n\n      // Soluzione alternativa per i cavi SPARE\n      if (tipoCavo === 3) {\n        console.log('Caricamento cavi SPARE con query diretta...');\n        try {\n          // Usa una query SQL diretta per ottenere i cavi SPARE\n          const response = await axios.get(`${API_URL}/cavi/spare/${cantiereIdNum}`, {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          });\n          console.log('Risposta cavi SPARE:', response.data);\n          return response.data;\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi SPARE:', spareError);\n          // Se fallisce, continua con il metodo standard\n        }\n      }\n\n      // Costruisci l'URL con i parametri di query\n      let url = `/cavi/${cantiereIdNum}`;\n      const queryParams = [];\n      if (tipoCavo !== null) {\n        queryParams.push(`tipo_cavo=${tipoCavo}`);\n      }\n\n      // Aggiungi filtri aggiuntivi se presenti\n      if (filters.stato_installazione) {\n        queryParams.push(`stato_installazione=${encodeURIComponent(filters.stato_installazione)}`);\n      }\n      if (filters.tipologia) {\n        queryParams.push(`tipologia=${encodeURIComponent(filters.tipologia)}`);\n      }\n      if (filters.sort_by) {\n        queryParams.push(`sort_by=${encodeURIComponent(filters.sort_by)}`);\n        if (filters.sort_order) {\n          queryParams.push(`sort_order=${encodeURIComponent(filters.sort_order)}`);\n        }\n      }\n\n      // Aggiungi i parametri di query all'URL\n      if (queryParams.length > 0) {\n        url += `?${queryParams.join('&')}`;\n      }\n\n      // Log dettagliato dell'URL e dei parametri\n      console.log('URL API completo:', url);\n      console.log('Parametri di query:', queryParams);\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');\n      console.log('URL completo:', `${API_URL}${url}`);\n      try {\n        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);\n        console.log('Headers della richiesta:', {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        });\n\n        // Aggiungi un timeout più lungo per la richiesta\n        const response = await axiosInstance.get(url, {\n          timeout: 60000\n        });\n        console.log(`Risposta API: ${url}`, response.data);\n        console.log('Status della risposta:', response.status);\n        console.log('Headers della risposta:', response.headers);\n        if (Array.isArray(response.data)) {\n          console.log(`Numero di cavi ricevuti: ${response.data.length}`);\n          if (response.data.length > 0) {\n            console.log('Primo cavo ricevuto:', response.data[0]);\n          } else {\n            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);\n          }\n        } else {\n          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);\n        }\n        return response.data;\n      } catch (apiError) {\n        var _apiError$response, _apiError$response2, _apiError$response3, _apiError$response4;\n        console.error(`Errore nella chiamata API GET ${url}:`, apiError);\n        console.error('Dettagli errore API:', {\n          message: apiError.message,\n          status: (_apiError$response = apiError.response) === null || _apiError$response === void 0 ? void 0 : _apiError$response.status,\n          statusText: (_apiError$response2 = apiError.response) === null || _apiError$response2 === void 0 ? void 0 : _apiError$response2.statusText,\n          data: (_apiError$response3 = apiError.response) === null || _apiError$response3 === void 0 ? void 0 : _apiError$response3.data,\n          headers: (_apiError$response4 = apiError.response) === null || _apiError$response4 === void 0 ? void 0 : _apiError$response4.headers,\n          code: apiError.code,\n          isAxiosError: apiError.isAxiosError,\n          config: apiError.config ? {\n            url: apiError.config.url,\n            method: apiError.config.method,\n            timeout: apiError.config.timeout,\n            headers: apiError.config.headers\n          } : 'No config'\n        });\n\n        // Gestione specifica per errori di rete\n        if (apiError.code === 'ERR_NETWORK') {\n          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');\n          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile\n          try {\n            console.log('Tentativo di test di connessione al backend...');\n            const testResponse = await fetch(API_URL);\n            console.log('Test di connessione al backend:', testResponse.status);\n          } catch (testError) {\n            console.error('Test di connessione al backend fallito:', testError);\n          }\n        }\n        throw apiError;\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$response4, _error$response4$data, _error$response5, _error$response6;\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,\n        stack: error.stack\n      });\n\n      // Verifica se l'errore è dovuto a un problema di connessione\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout') || error.message.includes('Network Error')) {\n        console.error('Errore di connessione o timeout');\n        // Ritorna un array vuoto invece di lanciare un errore\n        console.log('Ritorno array vuoto come fallback');\n        return [];\n      }\n\n      // Crea un errore più informativo\n      const enhancedError = new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || error.message || 'Errore sconosciuto');\n      enhancedError.status = (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status;\n      enhancedError.data = (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data;\n      enhancedError.response = error.response;\n      enhancedError.originalError = error;\n      enhancedError.code = error.code;\n      enhancedError.isAxiosError = error.isAxiosError;\n      throw enhancedError;\n    }\n  },\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Tentativo di creazione cavo per cantiere ${cantiereIdNum}`);\n      console.log('Dati inviati:', JSON.stringify(cavoData, null, 2));\n\n      // Invia la richiesta al server\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}`, cavoData, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta del server:', response.status, response.statusText);\n      console.log('Dati ricevuti:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError || !error.response || error.code === 'ECONNABORTED' || error.message && error.message.includes('Network Error')) {\n        console.log('Errore di rete o timeout, verifica se il cavo è stato creato...');\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste nel database\n          const token = localStorage.getItem('token');\n          // Assicurati che cantiereId sia un numero\n          const cantiereIdNumber = parseInt(cantiereId, 10);\n          const checkResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNumber}/check/${cavoData.id_cavo}`, {\n            headers: {\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 5000\n          });\n          if (checkResponse.data && checkResponse.data.exists) {\n            console.log('Il cavo risulta creato nonostante l\\'errore di comunicazione');\n            // Recupera i dati del cavo\n            const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNumber}/${cavoData.id_cavo}`, {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 5000\n            });\n            if (cavoResponse.data) {\n              console.log('Dati del cavo recuperati:', cavoResponse.data);\n              return cavoResponse.data;\n            }\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o il cavo non esiste\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: 'La richiesta non è andata a buon fine, ma il cavo potrebbe essere stato creato. Verifica nella lista dei cavi.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      if (error.response) {\n        console.error('Dettagli errore:', error.response.data);\n        console.error('Status errore:', error.response.status);\n        console.error('Headers errore:', error.response.headers);\n\n        // Formatta il messaggio di errore in modo più leggibile\n        const errorDetail = error.response.data.detail || 'Errore sconosciuto';\n        throw {\n          detail: errorDetail,\n          status: error.response.status\n        };\n      }\n      // Se è un errore di validazione locale, formatta il messaggio\n      if (error instanceof Error) {\n        throw {\n          detail: error.message,\n          status: 400\n        };\n      }\n      throw error;\n    }\n  },\n  // Ottiene un cavo specifico per ID\n  getCavoById: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Tentativo di ottenere cavo con ID ${cavoId} dal cantiere ${cantiereIdNum}`);\n\n      // Aumenta il timeout per questa richiesta specifica\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/${cavoId}`, {\n        timeout: 30000 // 30 secondi\n      });\n      console.log(`Cavo trovato:`, response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavo by ID error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError || !error.response || error.code === 'ECONNABORTED' || error.message && error.message.includes('Network Error')) {\n        console.log('Errore di rete o timeout, tentativo di recupero alternativo...');\n        try {\n          // Tentativo alternativo con axios standard (non l'istanza configurata)\n          console.log(`Tentativo alternativo di recupero del cavo ${cavoId}...`);\n          const token = localStorage.getItem('token');\n          const API_URL = axiosInstance.defaults.baseURL;\n\n          // Usa l'ID cantiere originale per la richiesta alternativa\n          const altResponse = await axios.get(`${API_URL}/cavi/${cantiereId}/${cavoId}`, {\n            headers: {\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 10000 // 10 secondi\n          });\n          console.log('Recupero alternativo riuscito:', altResponse.data);\n          return altResponse.data;\n        } catch (altError) {\n          console.error('Anche il tentativo alternativo è fallito:', altError);\n          throw {\n            detail: 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n            status: 0,\n            isNetworkError: true,\n            originalError: error.message\n          };\n        }\n      }\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta PUT a /cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Dati inviati:', cavoData);\n\n      // Verifica che il backend sia raggiungibile\n      try {\n        console.log('Verifica connessione al backend...');\n        const pingResponse = await fetch(`${API_URL}/health`, {\n          method: 'GET'\n        });\n        console.log('Ping al backend:', pingResponse.status, pingResponse.statusText);\n        if (!pingResponse.ok) {\n          console.error('Il server non risponde correttamente:', pingResponse.status);\n          throw new Error('Il server non risponde correttamente. Riprova più tardi.');\n        }\n      } catch (pingError) {\n        console.error('Errore durante il ping al backend:', pingError);\n        throw new Error('Impossibile connettersi al server. Verifica la connessione di rete e riprova.');\n      }\n\n      // Imposta un timeout più lungo per la richiesta\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData, {\n        timeout: 90000 // 90 secondi (timeout esteso)\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError || !error.response || error.code === 'ECONNABORTED' || error.message && error.message.includes('Network Error') || error.request) {\n        console.log('Errore di rete o timeout, verifica se il cavo è stato aggiornato...');\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se è stato aggiornato\n          const token = localStorage.getItem('token');\n          // Assicurati che cantiereId sia un numero\n          const cantiereIdNumber = parseInt(cantiereId, 10);\n          const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNumber}/${cavoId}`, {\n            headers: {\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 5000\n          });\n          if (cavoResponse.data) {\n            console.log('Cavo trovato nel database, verifica se è stato aggiornato');\n\n            // Verifica se almeno uno dei campi è stato aggiornato\n            let isUpdated = false;\n            for (const key in cavoData) {\n              if (cavoData[key] !== undefined && JSON.stringify(cavoData[key]) === JSON.stringify(cavoResponse.data[key])) {\n                console.log(`Campo ${key} risulta aggiornato: ${cavoData[key]}`);\n                isUpdated = true;\n                break;\n              }\n            }\n            if (isUpdated) {\n              console.log('Il cavo risulta aggiornato nonostante l\\'errore di comunicazione');\n              return cavoResponse.data;\n            } else {\n              console.log('Il cavo esiste ma non risulta aggiornato');\n            }\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o il cavo non è stato aggiornato\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: 'La modifica potrebbe essere stata salvata nonostante l\\'errore di comunicazione. Controlla lo stato del cavo.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n\n      // Gestione più dettagliata dell'errore\n      if (error.response) {\n        // Il server ha risposto con un codice di stato diverso da 2xx\n        console.error('Errore dal server:', error.response.status, error.response.statusText);\n        console.error('Dati errore:', error.response.data);\n        throw error.response.data;\n      } else {\n        // Si è verificato un errore durante l'impostazione della richiesta\n        console.error('Errore durante l\\'impostazione della richiesta:', error.message);\n        throw {\n          detail: error.message,\n          status: 500\n        };\n      }\n    }\n  },\n  // Ottiene la revisione corrente del cantiere\n  getRevisioneCorrente: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);\n      return response.data.revisione_corrente;\n    } catch (error) {\n      console.error('Get revisione corrente error:', error);\n      return '00'; // Valore di default in caso di errore\n    }\n  },\n  // Marca un cavo come SPARE\n  markCavoAsSpare: async (cantiereId, cavoId, force = false) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di marcare cavo come SPARE:', {\n        cantiereId: cantiereIdNum,\n        cavoId,\n        force\n      });\n\n      // Prova prima con l'endpoint POST specifico\n      console.log('URL API (POST):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`);\n      try {\n        const postResponse = await axios.post(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`, {\n          force: force\n        }, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Risposta markCavoAsSpare (POST):', postResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE...');\n        const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Stato del cavo dopo marcatura:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n        return cavoResponse.data;\n      } catch (postError) {\n        // Se fallisce il POST, prova con DELETE mode=spare\n        console.error('Errore con endpoint POST, tentativo con DELETE mode=spare:', postError);\n        console.log('URL API (DELETE):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}?mode=spare`);\n        const deleteResponse = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000,\n          params: {\n            mode: 'spare'\n          }\n        });\n        console.log('Risposta markCavoAsSpare (DELETE mode=spare):', deleteResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE con DELETE...');\n        const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Stato del cavo dopo marcatura con DELETE:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n        return cavoResponse.data;\n      }\n    } catch (error) {\n      var _error$response7, _error$response8, _error$response9;\n      console.error('Mark cavo as SPARE error:', error);\n      console.error('Dettagli errore:', {\n        message: error.message,\n        status: (_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : _error$response7.status,\n        statusText: (_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : _error$response8.statusText,\n        data: (_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : _error$response9.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un cavo o lo marca come SPARE\n  deleteCavo: async (cantiereId, cavoId, mode = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di eliminare/marcare cavo:', {\n        cantiereId: cantiereIdNum,\n        cavoId,\n        mode\n      });\n\n      // Se è specificata la modalità, aggiungi il parametro alla richiesta\n      const requestConfig = {\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        timeout: 30000,\n        // Timeout aumentato a 30 secondi\n        params: mode ? {\n          mode\n        } : {}\n      };\n      console.log('URL API:', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Config:', requestConfig);\n\n      // Usa axios direttamente invece di axiosInstance per avere più controllo\n      const response = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, requestConfig);\n      console.log('Risposta deleteCavo:', response.data);\n      return response.data;\n    } catch (error) {\n      var _error$response0, _error$response1, _error$response10;\n      console.error('Delete cavo error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: (_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : _error$response0.status,\n        statusText: (_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : _error$response1.statusText,\n        data: (_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : _error$response10.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n\n      // Crea un errore più informativo\n      if (error.response && error.response.data) {\n        throw error.response.data;\n      } else if (error.message) {\n        throw new Error(error.message);\n      } else {\n        throw new Error('Errore durante l\\'eliminazione del cavo');\n      }\n    }\n  },\n  // Aggiorna le caratteristiche di un cavo per renderlo compatibile con una bobina\n  updateCavoForCompatibility: async (cantiereId, cavoId, idBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/update-for-compatibility`);\n      console.log('Dati:', {\n        id_bobina: idBobina\n      });\n\n      // Prepara i dati da inviare\n      const requestData = {\n        id_bobina: idBobina\n      };\n\n      // Imposta un timeout più lungo per questa operazione\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/update-for-compatibility`, requestData, {\n        timeout: 30000 // 30 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo for compatibility error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      if (error.response && error.response.data) {\n        let errorDetail = error.response.data.detail || 'Errore sconosciuto';\n        // Se errorDetail è un oggetto, convertilo in stringa\n        if (typeof errorDetail === 'object') {\n          errorDetail = JSON.stringify(errorDetail);\n        }\n        throw {\n          detail: errorDetail,\n          status: error.response.status\n        };\n      }\n      // Se è un errore di validazione locale, formatta il messaggio\n      if (error instanceof Error) {\n        throw {\n          detail: error.message,\n          status: 400\n        };\n      }\n      throw error;\n    }\n  },\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati, idBobina = null, forceOver = false) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/metri-posati`);\n      console.log('Dati:', {\n        metri_posati: metriPosati,\n        id_bobina: idBobina,\n        force_over: forceOver\n      });\n\n      // Prepara i dati da inviare\n      const requestData = {\n        metri_posati: metriPosati,\n        data_posa: new Date().toISOString() // Data corrente\n      };\n\n      // Gestione speciale per BOBINA_VUOTA e altri valori di id_bobina\n      if (idBobina === 'BOBINA_VUOTA') {\n        // Assicurati che BOBINA_VUOTA venga inviato come stringa\n        requestData.id_bobina = 'BOBINA_VUOTA';\n        console.log('Impostando id_bobina a BOBINA_VUOTA (stringa)');\n\n        // Quando si usa BOBINA_VUOTA, imposta sempre force_over a true\n        // per evitare problemi con la validazione\n        requestData.force_over = true;\n        console.log('Impostando force_over a true per BOBINA_VUOTA');\n      } else if (idBobina !== null && idBobina !== undefined) {\n        // Per altri valori di bobina\n        requestData.id_bobina = idBobina;\n        console.log(`Impostando id_bobina a ${idBobina}`);\n      } else {\n        // Se non è specificata una bobina, imposta esplicitamente a null\n        requestData.id_bobina = null;\n        console.log('Impostando id_bobina a null');\n      }\n\n      // Log completo dei dati che verranno inviati\n      console.log('Dati completi da inviare:', JSON.stringify(requestData, null, 2));\n\n      // Imposta sempre force_over a true per garantire che l'operazione non si blocchi\n      // quando la bobina va in OVER, indipendentemente dal parametro forceOver\n      requestData.force_over = true;\n      console.log('Impostando force_over a true per garantire il completamento dell\\'operazione');\n\n      // Imposta un timeout più lungo per questa operazione\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, requestData, {\n        timeout: 90000 // 90 secondi per dare più tempo all'operazione\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError || !error.response || error.code === 'ECONNABORTED' || error.message && error.message.includes('Network Error')) {\n        console.log('Errore di rete o timeout, verifica se i metri posati sono stati aggiornati...');\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se i metri posati sono stati aggiornati\n          const token = localStorage.getItem('token');\n          // Assicurati che cantiereId sia un numero\n          const cantiereIdNumber = parseInt(cantiereId, 10);\n\n          // Usa axiosInstance invece di axios diretto per mantenere la gestione degli errori\n          const cavoResponse = await axiosInstance.get(`/cavi/${cantiereIdNumber}/${cavoId}`, {\n            timeout: 10000 // 10 secondi\n          });\n          if (cavoResponse.data && cavoResponse.data.metratura_reale === metriPosati) {\n            console.log('I metri posati risultano aggiornati nonostante l\\'errore di comunicazione');\n            return cavoResponse.data;\n          }\n        } catch (verifyError) {\n          // Non mostrare errori di verifica all'utente, solo log in console\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o i metri posati non sono stati aggiornati\n        // Restituisci un errore più user-friendly senza dettagli tecnici\n        throw {\n          detail: 'Impossibile completare l\\'operazione. Verifica lo stato del cavo e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n\n      // Formatta l'errore in modo più user-friendly\n      if (error.response && error.response.data) {\n        // Errore dal server\n        const serverError = error.response.data;\n        // Rimuovi eventuali dettagli tecnici o riferimenti a localhost\n        const cleanDetail = serverError.detail ? serverError.detail.replace(/localhost:\\d+/g, 'server').replace(/http:\\/\\/[^\\s]+/g, 'server') : 'Errore durante l\\'aggiornamento dei metri posati';\n\n        // Gestione speciale per errori con BOBINA_VUOTA\n        if (idBobina === 'BOBINA_VUOTA' && cleanDetail.includes('non trovata')) {\n          console.log('Errore con BOBINA_VUOTA, ma continuiamo con l\\'operazione');\n          throw {\n            detail: 'Cavo associato a BOBINA VUOTA con successo',\n            status: 200,\n            success: true\n          };\n        }\n        throw {\n          detail: cleanDetail,\n          status: error.response.status\n        };\n      } else {\n        // Errore generico\n        // Gestione speciale per errori con BOBINA_VUOTA\n        if (idBobina === 'BOBINA_VUOTA') {\n          console.log('Errore generico con BOBINA_VUOTA, ma continuiamo con l\\'operazione');\n          throw {\n            detail: 'Cavo associato a BOBINA VUOTA con successo',\n            status: 200,\n            success: true\n          };\n        }\n        throw {\n          detail: 'Errore durante l\\'aggiornamento dei metri posati',\n          status: 500\n        };\n      }\n    }\n  },\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina, forceOver = true) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/bobina`);\n      console.log('ID Bobina:', idBobina);\n      console.log('Force Over:', forceOver);\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {\n        id_bobina: idBobina,\n        force_over: forceOver\n      }, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError || !error.response || error.code === 'ECONNABORTED' || error.message && error.message.includes('Network Error')) {\n        console.log('Errore di rete o timeout, verifica se la bobina è stata aggiornata...');\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se la bobina è stata aggiornata\n          const token = localStorage.getItem('token');\n          // Assicurati che cantiereId sia un numero\n          const cantiereIdNumber = parseInt(cantiereId, 10);\n          const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNumber}/${cavoId}`, {\n            headers: {\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 5000\n          });\n          if (cavoResponse.data && cavoResponse.data.id_bobina === idBobina) {\n            console.log('La bobina risulta aggiornata nonostante l\\'errore di comunicazione');\n            return cavoResponse.data;\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o la bobina non è stata aggiornata\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: 'Impossibile verificare se la bobina è stata aggiornata. Controlla lo stato del cavo prima di riprovare.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      throw error.response ? error.response.data : {\n        detail: error.message,\n        status: 500\n      };\n    }\n  },\n  // Aggiorna le caratteristiche di un cavo per farle corrispondere a quelle di una bobina\n  updateCavoToMatchReel: async (cantiereId, cavoId, bobinaData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Aggiornamento caratteristiche del cavo ${cavoId} per farle corrispondere alla bobina ${bobinaData.id_bobina}`);\n\n      // Prepara i dati da inviare\n      // Nota: n_conduttori non è più utilizzato per la compatibilità\n      const updateData = {\n        tipologia: bobinaData.tipologia,\n        sezione: bobinaData.sezione,\n        modificato_manualmente: 1 // Indica che il cavo è stato modificato manualmente\n      };\n\n      // Chiamata API per aggiornare il cavo\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, updateData, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo to match reel error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError || !error.response || error.code === 'ECONNABORTED' || error.message && error.message.includes('Network Error')) {\n        console.log('Errore di rete o timeout, verifica se il cavo è stato aggiornato...');\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se è stato aggiornato\n          // Nota: n_conduttori non è più utilizzato per la compatibilità\n          const verificaCavo = await caviService.getCavoById(cantiereId, cavoId);\n          if (verificaCavo && verificaCavo.tipologia === bobinaData.tipologia && String(verificaCavo.sezione) === String(bobinaData.sezione)) {\n            console.log('Il cavo risulta aggiornato nonostante l\\'errore');\n            return verificaCavo;\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica:', verifyError);\n        }\n      }\n      throw error;\n    }\n  },\n  // Aggiorna le caratteristiche di un cavo per farle corrispondere a quelle di una bobina (endpoint dedicato)\n  updateCavoForCompatibility: async (cantiereId, cavoId, bobinaId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/update-for-compatibility`);\n      console.log('ID Bobina:', bobinaId);\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/update-for-compatibility`, {\n        id_bobina: bobinaId\n      }, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo for compatibility error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError || !error.response || error.code === 'ECONNABORTED' || error.message && error.message.includes('Network Error')) {\n        console.log('Errore di rete o timeout, verifica se il cavo è stato aggiornato...');\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se è stato aggiornato\n          const verificaCavo = await caviService.getCavoById(cantiereId, cavoId);\n          if (verificaCavo) {\n            console.log('Il cavo risulta aggiornato nonostante l\\'errore');\n            return verificaCavo;\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica:', verifyError);\n        }\n      }\n      throw error.response ? error.response.data : {\n        detail: error.message,\n        status: 500\n      };\n    }\n  },\n  // Riattiva un cavo SPARE\n  reactivateSpare: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/reactivate-spare`);\n\n      // Chiamata API per riattivare il cavo SPARE\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/reactivate-spare`, {}, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Reactivate spare error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError || !error.response || error.code === 'ECONNABORTED' || error.message && error.message.includes('Network Error')) {\n        console.log('Errore di rete o timeout, verifica se il cavo è stato riattivato...');\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se è stato riattivato\n          const verificaCavo = await caviService.getCavoById(cantiereId, cavoId);\n          if (verificaCavo && verificaCavo.modificato_manualmente !== 3) {\n            console.log('Il cavo risulta riattivato nonostante l\\'errore');\n            return verificaCavo;\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica:', verifyError);\n        }\n      }\n      throw error;\n    }\n  },\n  // Ottiene la lista dei cavi installati di un cantiere\n  getCaviInstallati: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Caricamento cavi installati per cantiere ${cantiereIdNum}...`);\n      try {\n        // Primo tentativo con axiosInstance\n        console.log(`URL API: ${axiosInstance.defaults.baseURL}/cavi/${cantiereIdNum}/installati`);\n        const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`, {\n          timeout: 30000 // 30 secondi\n        });\n        console.log(`Risposta getCaviInstallati: ${response.data ? response.data.length : 0} cavi installati trovati`);\n        if (response.data && response.data.length > 0) {\n          console.log('Primo cavo installato:', response.data[0]);\n        } else {\n          console.log('Nessun cavo installato trovato');\n        }\n        return response.data;\n      } catch (primaryError) {\n        console.error('Errore con richiesta primaria, tentativo alternativo:', primaryError);\n\n        // Tentativo alternativo con axios standard\n        console.log(`URL API (alternativo): ${API_URL}/cavi/${cantiereIdNum}/installati`);\n        const token = localStorage.getItem('token');\n        const alternativeResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/installati`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          },\n          timeout: 30000\n        });\n        console.log(`Risposta alternativa: ${alternativeResponse.data ? alternativeResponse.data.length : 0} cavi installati trovati`);\n        return alternativeResponse.data;\n      }\n    } catch (error) {\n      var _error$response11, _error$response12, _error$response13;\n      console.error('Get cavi installati error:', error);\n      console.error('Dettagli errore:', {\n        message: error.message,\n        status: (_error$response11 = error.response) === null || _error$response11 === void 0 ? void 0 : _error$response11.status,\n        statusText: (_error$response12 = error.response) === null || _error$response12 === void 0 ? void 0 : _error$response12.statusText,\n        data: (_error$response13 = error.response) === null || _error$response13 === void 0 ? void 0 : _error$response13.data,\n        url: `/cavi/${cantiereId}/installati`\n      });\n\n      // Verifica se l'errore è dovuto a un problema di connessione\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout') || error.message.includes('Network Error')) {\n        console.error('Errore di connessione o timeout');\n        // Ritorna un array vuoto invece di lanciare un errore\n        console.log('Ritorno array vuoto come fallback');\n        return [];\n      }\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene le statistiche dei cavi di un cantiere\n  getCaviStats: async (cantiereId, revisione = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Costruisci l'URL con il parametro revisione se specificato\n      let url = `/cavi/${cantiereIdNum}/stats`;\n      if (revisione) {\n        url += `?revisione=${encodeURIComponent(revisione)}`;\n      }\n      const response = await axiosInstance.get(url);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi stats error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene la revisione corrente di un cantiere\n  getRevisioneCorrente: async cantiereId => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);\n      return response.data;\n    } catch (error) {\n      console.error('Get revisione corrente error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene tutte le revisioni disponibili di un cantiere\n  getRevisioniDisponibili: async cantiereId => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisioni`);\n      return response.data;\n    } catch (error) {\n      console.error('Get revisioni disponibili error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene direttamente i cavi SPARE\n  getCaviSpare: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log('Caricamento cavi SPARE...');\n\n      // Prova prima con l'endpoint standard con tipo_cavo=3\n      console.log('URL API (standard):', `${API_URL}/cavi/${cantiereIdNum}?tipo_cavo=3`);\n      try {\n        const response = await axios.get(`${API_URL}/cavi/${cantiereIdNum}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000,\n          params: {\n            tipo_cavo: 3\n          }\n        });\n        console.log('Risposta getCaviSpare (standard):', response.data ? response.data.length : 0, 'cavi SPARE trovati');\n        if (response.data && response.data.length > 0) {\n          console.log('Primo cavo SPARE:', response.data[0]);\n        }\n        return response.data;\n      } catch (standardError) {\n        console.error('Errore con endpoint standard, tentativo con endpoint dedicato:', standardError);\n\n        // Se fallisce, prova con l'endpoint dedicato\n        console.log('URL API (dedicato):', `${API_URL}/cavi/spare/${cantiereIdNum}`);\n        const dedicatedResponse = await axios.get(`${API_URL}/cavi/spare/${cantiereIdNum}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Risposta getCaviSpare (dedicato):', dedicatedResponse.data ? dedicatedResponse.data.length : 0, 'cavi SPARE trovati');\n        if (dedicatedResponse.data && dedicatedResponse.data.length > 0) {\n          console.log('Primo cavo SPARE (dedicato):', dedicatedResponse.data[0]);\n        }\n        return dedicatedResponse.data;\n      }\n    } catch (error) {\n      console.error('Get cavi SPARE error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i cavi installati di un cantiere per la gestione collegamenti\n  getCaviInstallati: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Caricamento cavi installati per cantiere ${cantiereIdNum}...`);\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta cavi installati:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Collega un lato di un cavo\n  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/collegamento`);\n      console.log('Dati:', {\n        lato,\n        responsabile\n      });\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {\n        lato: lato,\n        responsabile: responsabile || 'cantiere'\n      }, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Collega cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      throw error.response ? error.response.data : {\n        detail: error.message,\n        status: 500\n      };\n    }\n  },\n  // Scollega un lato di un cavo\n  scollegaCavo: async (cantiereId, cavoId, lato) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta DELETE a /cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Scollega cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      throw error.response ? error.response.data : {\n        detail: error.message,\n        status: 500\n      };\n    }\n  },\n  // Annulla l'installazione di un cavo\n  cancelInstallation: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/cancel-installation`);\n      try {\n        // Prima prova con POST su cancel-installation\n        const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/cancel-installation`, {}, {\n          timeout: 30000 // 30 secondi\n        });\n        return response.data;\n      } catch (postError) {\n        console.log('POST su cancel-installation fallito, tentativo con PUT:', postError);\n        try {\n          // Se POST fallisce, prova con PUT\n          const putResponse = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}/cancel-installation`, {}, {\n            timeout: 30000 // 30 secondi\n          });\n          return putResponse.data;\n        } catch (putError) {\n          console.log('PUT su cancel-installation fallito, tentativo con endpoint alternativo:', putError);\n\n          // Se anche PUT fallisce, prova con l'endpoint alternativo\n          const altResponse = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/annulla-installazione`, {}, {\n            timeout: 30000 // 30 secondi\n          });\n          return altResponse.data;\n        }\n      }\n    } catch (error) {\n      console.error('Cancel installation error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError || !error.response || error.code === 'ECONNABORTED' || error.message && error.message.includes('Network Error')) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      throw error.response ? error.response.data : {\n        detail: error.message,\n        status: 500\n      };\n    }\n  },\n  // Verifica lo stato di un cavo specifico (debug)\n  debugCavo: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Ottieni i cavi attivi\n      console.log('Verificando cavo tra i cavi attivi...');\n      const attivi = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=0`, {\n        timeout: 60000 // 60 secondi\n      });\n      const cavoAttivo = attivi.data.find(c => c.id_cavo === cavoId);\n\n      // Ottieni i cavi SPARE\n      console.log('Verificando cavo tra i cavi SPARE...');\n      const spare = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=3`, {\n        timeout: 60000 // 60 secondi\n      });\n      const cavoSpare = spare.data.find(c => c.id_cavo === cavoId);\n      return {\n        trovato_tra_attivi: !!cavoAttivo,\n        trovato_tra_spare: !!cavoSpare,\n        cavo_attivo: cavoAttivo,\n        cavo_spare: cavoSpare\n      };\n    } catch (error) {\n      console.error('Debug cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      throw error.response ? error.response.data : {\n        detail: error.message,\n        status: 500\n      };\n    }\n  }\n};\nexport default caviService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "caviService", "get<PERSON><PERSON>", "cantiereId", "tipoCavo", "filters", "console", "log", "undefined", "error", "Error", "cantiereIdNum", "parseInt", "isNaN", "response", "get", "headers", "localStorage", "getItem", "timeout", "data", "spareError", "url", "queryParams", "push", "stato_installazione", "encodeURIComponent", "tipologia", "sort_by", "sort_order", "length", "join", "status", "Array", "isArray", "warn", "apiError", "_apiError$response", "_apiError$response2", "_apiError$response3", "_apiError$response4", "message", "statusText", "code", "isAxiosError", "method", "testResponse", "fetch", "testError", "_error$response", "_error$response2", "_error$response3", "_error$response4", "_error$response4$data", "_error$response5", "_error$response6", "stack", "includes", "enhancedError", "detail", "originalError", "createCavo", "cavoData", "JSON", "stringify", "post", "isNetworkError", "isTimeoutError", "Promise", "resolve", "setTimeout", "token", "cantiereIdNumber", "checkResponse", "id_cavo", "exists", "cavoResponse", "verifyError", "customMessage", "errorDetail", "getCavoById", "cavoId", "defaults", "baseURL", "altResponse", "altError", "updateCavo", "pingResponse", "ok", "pingError", "put", "request", "isUpdated", "key", "getRevisioneCorrente", "revisione_corrente", "markCavoAsSpare", "force", "postResponse", "modificato_manualmente", "postError", "deleteResponse", "delete", "params", "mode", "_error$response7", "_error$response8", "_error$response9", "deleteCavo", "requestConfig", "_error$response0", "_error$response1", "_error$response10", "updateCavoForCompatibility", "idBobina", "id_bobina", "requestData", "updateMetri<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forceOver", "metri_posati", "force_over", "data_posa", "Date", "toISOString", "metratura_reale", "serverError", "cleanDetail", "replace", "success", "updateBobina", "updateCavoToMatchReel", "bobina<PERSON><PERSON>", "updateData", "sezione", "verificaCavo", "String", "bobina<PERSON>d", "reactivateSpare", "getCaviInstallati", "primaryError", "alternativeResponse", "_error$response11", "_error$response12", "_error$response13", "getCaviStats", "revisione", "getRevisioniDisponibili", "getCaviSpare", "tipo_cavo", "standardError", "dedicatedResponse", "collegaCavo", "lato", "responsabile", "scollegaCavo", "cancelInstallation", "putResponse", "putError", "debugCavo", "attivi", "cavoAttivo", "find", "c", "spare", "cavoSpare", "trovato_tra_attivi", "trovato_tra_spare", "cavo_attivo", "cavo_spare"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/caviService.js"], "sourcesContent": ["import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\n\n// Assicurati che axios sia disponibile globalmente per i tentativi alternativi\nconst API_URL = config.API_URL;\n\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null, filters = {}) => {\n    try {\n      console.log('getCavi chiamato con:', { cantiereId, tipoCavo, filters });\n      console.log('Tipo di cantiereId:', typeof cantiereId);\n\n      // Verifica che cantiereId sia definito\n      if (cantiereId === undefined || cantiereId === null) {\n        console.error('cantiereId è undefined o null');\n        throw new Error('ID cantiere mancante');\n      }\n\n      // Assicurati che cantiereId sia un numero\n      let cantiereIdNum = cantiereId;\n      if (typeof cantiereId === 'string') {\n        cantiereIdNum = parseInt(cantiereId, 10);\n        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);\n      }\n\n      if (isNaN(cantiereIdNum)) {\n        console.error('ID cantiere non è un numero valido:', cantiereId);\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log(`Caricamento cavi per cantiere ${cantiereIdNum} con tipo_cavo=${tipoCavo}`);\n\n      // Soluzione alternativa per i cavi SPARE\n      if (tipoCavo === 3) {\n        console.log('Caricamento cavi SPARE con query diretta...');\n        try {\n          // Usa una query SQL diretta per ottenere i cavi SPARE\n          const response = await axios.get(\n            `${API_URL}/cavi/spare/${cantiereIdNum}`,\n            {\n              headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${localStorage.getItem('token')}`\n              },\n              timeout: 30000\n            }\n          );\n\n          console.log('Risposta cavi SPARE:', response.data);\n          return response.data;\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi SPARE:', spareError);\n          // Se fallisce, continua con il metodo standard\n        }\n      }\n\n      // Costruisci l'URL con i parametri di query\n      let url = `/cavi/${cantiereIdNum}`;\n      const queryParams = [];\n\n      if (tipoCavo !== null) {\n        queryParams.push(`tipo_cavo=${tipoCavo}`);\n      }\n\n      // Aggiungi filtri aggiuntivi se presenti\n      if (filters.stato_installazione) {\n        queryParams.push(`stato_installazione=${encodeURIComponent(filters.stato_installazione)}`);\n      }\n\n      if (filters.tipologia) {\n        queryParams.push(`tipologia=${encodeURIComponent(filters.tipologia)}`);\n      }\n\n      if (filters.sort_by) {\n        queryParams.push(`sort_by=${encodeURIComponent(filters.sort_by)}`);\n        if (filters.sort_order) {\n          queryParams.push(`sort_order=${encodeURIComponent(filters.sort_order)}`);\n        }\n      }\n\n      // Aggiungi i parametri di query all'URL\n      if (queryParams.length > 0) {\n        url += `?${queryParams.join('&')}`;\n      }\n\n      // Log dettagliato dell'URL e dei parametri\n      console.log('URL API completo:', url);\n      console.log('Parametri di query:', queryParams);\n\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');\n      console.log('URL completo:', `${API_URL}${url}`);\n\n      try {\n        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);\n        console.log('Headers della richiesta:', {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        });\n\n        // Aggiungi un timeout più lungo per la richiesta\n        const response = await axiosInstance.get(url, { timeout: 60000 });\n\n        console.log(`Risposta API: ${url}`, response.data);\n        console.log('Status della risposta:', response.status);\n        console.log('Headers della risposta:', response.headers);\n\n        if (Array.isArray(response.data)) {\n          console.log(`Numero di cavi ricevuti: ${response.data.length}`);\n          if (response.data.length > 0) {\n            console.log('Primo cavo ricevuto:', response.data[0]);\n          } else {\n            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);\n          }\n        } else {\n          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);\n        }\n\n        return response.data;\n      } catch (apiError) {\n        console.error(`Errore nella chiamata API GET ${url}:`, apiError);\n        console.error('Dettagli errore API:', {\n          message: apiError.message,\n          status: apiError.response?.status,\n          statusText: apiError.response?.statusText,\n          data: apiError.response?.data,\n          headers: apiError.response?.headers,\n          code: apiError.code,\n          isAxiosError: apiError.isAxiosError,\n          config: apiError.config ? {\n            url: apiError.config.url,\n            method: apiError.config.method,\n            timeout: apiError.config.timeout,\n            headers: apiError.config.headers\n          } : 'No config'\n        });\n\n        // Gestione specifica per errori di rete\n        if (apiError.code === 'ERR_NETWORK') {\n          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');\n          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile\n          try {\n            console.log('Tentativo di test di connessione al backend...');\n            const testResponse = await fetch(API_URL);\n            console.log('Test di connessione al backend:', testResponse.status);\n          } catch (testError) {\n            console.error('Test di connessione al backend fallito:', testError);\n          }\n        }\n\n        throw apiError;\n      }\n    } catch (error) {\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,\n        stack: error.stack\n      });\n\n      // Verifica se l'errore è dovuto a un problema di connessione\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout') || error.message.includes('Network Error')) {\n        console.error('Errore di connessione o timeout');\n        // Ritorna un array vuoto invece di lanciare un errore\n        console.log('Ritorno array vuoto come fallback');\n        return [];\n      }\n\n      // Crea un errore più informativo\n      const enhancedError = new Error(error.response?.data?.detail || error.message || 'Errore sconosciuto');\n      enhancedError.status = error.response?.status;\n      enhancedError.data = error.response?.data;\n      enhancedError.response = error.response;\n      enhancedError.originalError = error;\n      enhancedError.code = error.code;\n      enhancedError.isAxiosError = error.isAxiosError;\n\n      throw enhancedError;\n    }\n  },\n\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Tentativo di creazione cavo per cantiere ${cantiereIdNum}`);\n      console.log('Dati inviati:', JSON.stringify(cavoData, null, 2));\n\n      // Invia la richiesta al server\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}`, cavoData, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta del server:', response.status, response.statusText);\n      console.log('Dati ricevuti:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError ||\n          !error.response || error.code === 'ECONNABORTED' ||\n          (error.message && error.message.includes('Network Error'))) {\n\n        console.log('Errore di rete o timeout, verifica se il cavo è stato creato...');\n\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste nel database\n          const token = localStorage.getItem('token');\n          // Assicurati che cantiereId sia un numero\n          const cantiereIdNumber = parseInt(cantiereId, 10);\n          const checkResponse = await axios.get(\n            `${API_URL}/cavi/${cantiereIdNumber}/check/${cavoData.id_cavo}`,\n            {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 5000\n            }\n          );\n\n          if (checkResponse.data && checkResponse.data.exists) {\n            console.log('Il cavo risulta creato nonostante l\\'errore di comunicazione');\n            // Recupera i dati del cavo\n            const cavoResponse = await axios.get(\n              `${API_URL}/cavi/${cantiereIdNumber}/${cavoData.id_cavo}`,\n              {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 5000\n              }\n            );\n\n            if (cavoResponse.data) {\n              console.log('Dati del cavo recuperati:', cavoResponse.data);\n              return cavoResponse.data;\n            }\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o il cavo non esiste\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: 'La richiesta non è andata a buon fine, ma il cavo potrebbe essere stato creato. Verifica nella lista dei cavi.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n\n      if (error.response) {\n        console.error('Dettagli errore:', error.response.data);\n        console.error('Status errore:', error.response.status);\n        console.error('Headers errore:', error.response.headers);\n\n        // Formatta il messaggio di errore in modo più leggibile\n        const errorDetail = error.response.data.detail || 'Errore sconosciuto';\n        throw { detail: errorDetail, status: error.response.status };\n      }\n      // Se è un errore di validazione locale, formatta il messaggio\n      if (error instanceof Error) {\n        throw { detail: error.message, status: 400 };\n      }\n      throw error;\n    }\n  },\n\n  // Ottiene un cavo specifico per ID\n  getCavoById: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Tentativo di ottenere cavo con ID ${cavoId} dal cantiere ${cantiereIdNum}`);\n\n      // Aumenta il timeout per questa richiesta specifica\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/${cavoId}`, {\n        timeout: 30000 // 30 secondi\n      });\n\n      console.log(`Cavo trovato:`, response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavo by ID error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError ||\n          !error.response || error.code === 'ECONNABORTED' ||\n          (error.message && error.message.includes('Network Error'))) {\n\n        console.log('Errore di rete o timeout, tentativo di recupero alternativo...');\n\n        try {\n          // Tentativo alternativo con axios standard (non l'istanza configurata)\n          console.log(`Tentativo alternativo di recupero del cavo ${cavoId}...`);\n          const token = localStorage.getItem('token');\n          const API_URL = axiosInstance.defaults.baseURL;\n\n          // Usa l'ID cantiere originale per la richiesta alternativa\n          const altResponse = await axios.get(\n            `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n            {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 10000 // 10 secondi\n            }\n          );\n\n          console.log('Recupero alternativo riuscito:', altResponse.data);\n          return altResponse.data;\n        } catch (altError) {\n          console.error('Anche il tentativo alternativo è fallito:', altError);\n          throw {\n            detail: 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n            status: 0,\n            isNetworkError: true,\n            originalError: error.message\n          };\n        }\n      }\n\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta PUT a /cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Dati inviati:', cavoData);\n\n      // Verifica che il backend sia raggiungibile\n      try {\n        console.log('Verifica connessione al backend...');\n        const pingResponse = await fetch(`${API_URL}/health`, { method: 'GET' });\n        console.log('Ping al backend:', pingResponse.status, pingResponse.statusText);\n        if (!pingResponse.ok) {\n          console.error('Il server non risponde correttamente:', pingResponse.status);\n          throw new Error('Il server non risponde correttamente. Riprova più tardi.');\n        }\n      } catch (pingError) {\n        console.error('Errore durante il ping al backend:', pingError);\n        throw new Error('Impossibile connettersi al server. Verifica la connessione di rete e riprova.');\n      }\n\n      // Imposta un timeout più lungo per la richiesta\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData, {\n        timeout: 90000, // 90 secondi (timeout esteso)\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError ||\n          !error.response || error.code === 'ECONNABORTED' ||\n          (error.message && error.message.includes('Network Error')) ||\n          error.request) {\n\n        console.log('Errore di rete o timeout, verifica se il cavo è stato aggiornato...');\n\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se è stato aggiornato\n          const token = localStorage.getItem('token');\n          // Assicurati che cantiereId sia un numero\n          const cantiereIdNumber = parseInt(cantiereId, 10);\n          const cavoResponse = await axios.get(\n            `${API_URL}/cavi/${cantiereIdNumber}/${cavoId}`,\n            {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 5000\n            }\n          );\n\n          if (cavoResponse.data) {\n            console.log('Cavo trovato nel database, verifica se è stato aggiornato');\n\n            // Verifica se almeno uno dei campi è stato aggiornato\n            let isUpdated = false;\n            for (const key in cavoData) {\n              if (cavoData[key] !== undefined &&\n                  JSON.stringify(cavoData[key]) === JSON.stringify(cavoResponse.data[key])) {\n                console.log(`Campo ${key} risulta aggiornato: ${cavoData[key]}`);\n                isUpdated = true;\n                break;\n              }\n            }\n\n            if (isUpdated) {\n              console.log('Il cavo risulta aggiornato nonostante l\\'errore di comunicazione');\n              return cavoResponse.data;\n            } else {\n              console.log('Il cavo esiste ma non risulta aggiornato');\n            }\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o il cavo non è stato aggiornato\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: 'La modifica potrebbe essere stata salvata nonostante l\\'errore di comunicazione. Controlla lo stato del cavo.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n\n      // Gestione più dettagliata dell'errore\n      if (error.response) {\n        // Il server ha risposto con un codice di stato diverso da 2xx\n        console.error('Errore dal server:', error.response.status, error.response.statusText);\n        console.error('Dati errore:', error.response.data);\n        throw error.response.data;\n      } else {\n        // Si è verificato un errore durante l'impostazione della richiesta\n        console.error('Errore durante l\\'impostazione della richiesta:', error.message);\n        throw { detail: error.message, status: 500 };\n      }\n    }\n  },\n\n  // Ottiene la revisione corrente del cantiere\n  getRevisioneCorrente: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);\n      return response.data.revisione_corrente;\n    } catch (error) {\n      console.error('Get revisione corrente error:', error);\n      return '00'; // Valore di default in caso di errore\n    }\n  },\n\n  // Marca un cavo come SPARE\n  markCavoAsSpare: async (cantiereId, cavoId, force = false) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di marcare cavo come SPARE:', { cantiereId: cantiereIdNum, cavoId, force });\n\n      // Prova prima con l'endpoint POST specifico\n      console.log('URL API (POST):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`);\n\n      try {\n        const postResponse = await axios.post(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`,\n          { force: force },\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Risposta markCavoAsSpare (POST):', postResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE...');\n        const cavoResponse = await axios.get(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Stato del cavo dopo marcatura:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n\n        return cavoResponse.data;\n      } catch (postError) {\n        // Se fallisce il POST, prova con DELETE mode=spare\n        console.error('Errore con endpoint POST, tentativo con DELETE mode=spare:', postError);\n        console.log('URL API (DELETE):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}?mode=spare`);\n\n        const deleteResponse = await axios.delete(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000,\n            params: { mode: 'spare' }\n          }\n        );\n\n        console.log('Risposta markCavoAsSpare (DELETE mode=spare):', deleteResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE con DELETE...');\n        const cavoResponse = await axios.get(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Stato del cavo dopo marcatura con DELETE:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n\n        return cavoResponse.data;\n      }\n    } catch (error) {\n      console.error('Mark cavo as SPARE error:', error);\n      console.error('Dettagli errore:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina un cavo o lo marca come SPARE\n  deleteCavo: async (cantiereId, cavoId, mode = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di eliminare/marcare cavo:', { cantiereId: cantiereIdNum, cavoId, mode });\n\n      // Se è specificata la modalità, aggiungi il parametro alla richiesta\n      const requestConfig = {\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        timeout: 30000, // Timeout aumentato a 30 secondi\n        params: mode ? { mode } : {}\n      };\n\n      console.log('URL API:', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Config:', requestConfig);\n\n      // Usa axios direttamente invece di axiosInstance per avere più controllo\n      const response = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, requestConfig);\n      console.log('Risposta deleteCavo:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cavo error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n\n      // Crea un errore più informativo\n      if (error.response && error.response.data) {\n        throw error.response.data;\n      } else if (error.message) {\n        throw new Error(error.message);\n      } else {\n        throw new Error('Errore durante l\\'eliminazione del cavo');\n      }\n    }\n  },\n\n  // Aggiorna le caratteristiche di un cavo per renderlo compatibile con una bobina\n  updateCavoForCompatibility: async (cantiereId, cavoId, idBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/update-for-compatibility`);\n      console.log('Dati:', { id_bobina: idBobina });\n\n      // Prepara i dati da inviare\n      const requestData = {\n        id_bobina: idBobina\n      };\n\n      // Imposta un timeout più lungo per questa operazione\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/update-for-compatibility`, requestData, {\n        timeout: 30000, // 30 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo for compatibility error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\n      }\n\n      if (error.response && error.response.data) {\n        let errorDetail = error.response.data.detail || 'Errore sconosciuto';\n        // Se errorDetail è un oggetto, convertilo in stringa\n        if (typeof errorDetail === 'object') {\n          errorDetail = JSON.stringify(errorDetail);\n        }\n        throw { detail: errorDetail, status: error.response.status };\n      }\n      // Se è un errore di validazione locale, formatta il messaggio\n      if (error instanceof Error) {\n        throw { detail: error.message, status: 400 };\n      }\n      throw error;\n    }\n  },\n\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati, idBobina = null, forceOver = false) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/metri-posati`);\n      console.log('Dati:', { metri_posati: metriPosati, id_bobina: idBobina, force_over: forceOver });\n\n      // Prepara i dati da inviare\n      const requestData = {\n        metri_posati: metriPosati,\n        data_posa: new Date().toISOString() // Data corrente\n      };\n\n      // Gestione speciale per BOBINA_VUOTA e altri valori di id_bobina\n      if (idBobina === 'BOBINA_VUOTA') {\n        // Assicurati che BOBINA_VUOTA venga inviato come stringa\n        requestData.id_bobina = 'BOBINA_VUOTA';\n        console.log('Impostando id_bobina a BOBINA_VUOTA (stringa)');\n\n        // Quando si usa BOBINA_VUOTA, imposta sempre force_over a true\n        // per evitare problemi con la validazione\n        requestData.force_over = true;\n        console.log('Impostando force_over a true per BOBINA_VUOTA');\n      } else if (idBobina !== null && idBobina !== undefined) {\n        // Per altri valori di bobina\n        requestData.id_bobina = idBobina;\n        console.log(`Impostando id_bobina a ${idBobina}`);\n      } else {\n        // Se non è specificata una bobina, imposta esplicitamente a null\n        requestData.id_bobina = null;\n        console.log('Impostando id_bobina a null');\n      }\n\n      // Log completo dei dati che verranno inviati\n      console.log('Dati completi da inviare:', JSON.stringify(requestData, null, 2));\n\n      // Imposta sempre force_over a true per garantire che l'operazione non si blocchi\n      // quando la bobina va in OVER, indipendentemente dal parametro forceOver\n      requestData.force_over = true;\n      console.log('Impostando force_over a true per garantire il completamento dell\\'operazione');\n\n      // Imposta un timeout più lungo per questa operazione\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, requestData, {\n        timeout: 90000, // 90 secondi per dare più tempo all'operazione\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError ||\n          !error.response || error.code === 'ECONNABORTED' ||\n          (error.message && error.message.includes('Network Error'))) {\n\n        console.log('Errore di rete o timeout, verifica se i metri posati sono stati aggiornati...');\n\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se i metri posati sono stati aggiornati\n          const token = localStorage.getItem('token');\n          // Assicurati che cantiereId sia un numero\n          const cantiereIdNumber = parseInt(cantiereId, 10);\n\n          // Usa axiosInstance invece di axios diretto per mantenere la gestione degli errori\n          const cavoResponse = await axiosInstance.get(`/cavi/${cantiereIdNumber}/${cavoId}`, {\n            timeout: 10000 // 10 secondi\n          });\n\n          if (cavoResponse.data && cavoResponse.data.metratura_reale === metriPosati) {\n            console.log('I metri posati risultano aggiornati nonostante l\\'errore di comunicazione');\n            return cavoResponse.data;\n          }\n        } catch (verifyError) {\n          // Non mostrare errori di verifica all'utente, solo log in console\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o i metri posati non sono stati aggiornati\n        // Restituisci un errore più user-friendly senza dettagli tecnici\n        throw {\n          detail: 'Impossibile completare l\\'operazione. Verifica lo stato del cavo e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n\n      // Formatta l'errore in modo più user-friendly\n      if (error.response && error.response.data) {\n        // Errore dal server\n        const serverError = error.response.data;\n        // Rimuovi eventuali dettagli tecnici o riferimenti a localhost\n        const cleanDetail = serverError.detail ?\n          serverError.detail.replace(/localhost:\\d+/g, 'server').replace(/http:\\/\\/[^\\s]+/g, 'server') :\n          'Errore durante l\\'aggiornamento dei metri posati';\n\n        // Gestione speciale per errori con BOBINA_VUOTA\n        if (idBobina === 'BOBINA_VUOTA' && cleanDetail.includes('non trovata')) {\n          console.log('Errore con BOBINA_VUOTA, ma continuiamo con l\\'operazione');\n          throw {\n            detail: 'Cavo associato a BOBINA VUOTA con successo',\n            status: 200,\n            success: true\n          };\n        }\n\n        throw {\n          detail: cleanDetail,\n          status: error.response.status\n        };\n      } else {\n        // Errore generico\n        // Gestione speciale per errori con BOBINA_VUOTA\n        if (idBobina === 'BOBINA_VUOTA') {\n          console.log('Errore generico con BOBINA_VUOTA, ma continuiamo con l\\'operazione');\n          throw {\n            detail: 'Cavo associato a BOBINA VUOTA con successo',\n            status: 200,\n            success: true\n          };\n        }\n\n        throw {\n          detail: 'Errore durante l\\'aggiornamento dei metri posati',\n          status: 500\n        };\n      }\n    }\n  },\n\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina, forceOver = true) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/bobina`);\n      console.log('ID Bobina:', idBobina);\n      console.log('Force Over:', forceOver);\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {\n        id_bobina: idBobina,\n        force_over: forceOver\n      }, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError ||\n          !error.response || error.code === 'ECONNABORTED' ||\n          (error.message && error.message.includes('Network Error'))) {\n\n        console.log('Errore di rete o timeout, verifica se la bobina è stata aggiornata...');\n\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se la bobina è stata aggiornata\n          const token = localStorage.getItem('token');\n          // Assicurati che cantiereId sia un numero\n          const cantiereIdNumber = parseInt(cantiereId, 10);\n          const cavoResponse = await axios.get(\n            `${API_URL}/cavi/${cantiereIdNumber}/${cavoId}`,\n            {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 5000\n            }\n          );\n\n          if (cavoResponse.data && cavoResponse.data.id_bobina === idBobina) {\n            console.log('La bobina risulta aggiornata nonostante l\\'errore di comunicazione');\n            return cavoResponse.data;\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o la bobina non è stata aggiornata\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: 'Impossibile verificare se la bobina è stata aggiornata. Controlla lo stato del cavo prima di riprovare.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n\n      throw error.response ? error.response.data : { detail: error.message, status: 500 };\n    }\n  },\n\n  // Aggiorna le caratteristiche di un cavo per farle corrispondere a quelle di una bobina\n  updateCavoToMatchReel: async (cantiereId, cavoId, bobinaData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Aggiornamento caratteristiche del cavo ${cavoId} per farle corrispondere alla bobina ${bobinaData.id_bobina}`);\n\n      // Prepara i dati da inviare\n      // Nota: n_conduttori non è più utilizzato per la compatibilità\n      const updateData = {\n        tipologia: bobinaData.tipologia,\n        sezione: bobinaData.sezione,\n        modificato_manualmente: 1 // Indica che il cavo è stato modificato manualmente\n      };\n\n      // Chiamata API per aggiornare il cavo\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, updateData, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo to match reel error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError ||\n          !error.response || error.code === 'ECONNABORTED' ||\n          (error.message && error.message.includes('Network Error'))) {\n\n        console.log('Errore di rete o timeout, verifica se il cavo è stato aggiornato...');\n\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se è stato aggiornato\n          // Nota: n_conduttori non è più utilizzato per la compatibilità\n          const verificaCavo = await caviService.getCavoById(cantiereId, cavoId);\n          if (verificaCavo &&\n              verificaCavo.tipologia === bobinaData.tipologia &&\n              String(verificaCavo.sezione) === String(bobinaData.sezione)) {\n            console.log('Il cavo risulta aggiornato nonostante l\\'errore');\n            return verificaCavo;\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica:', verifyError);\n        }\n      }\n\n      throw error;\n    }\n  },\n\n  // Aggiorna le caratteristiche di un cavo per farle corrispondere a quelle di una bobina (endpoint dedicato)\n  updateCavoForCompatibility: async (cantiereId, cavoId, bobinaId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/update-for-compatibility`);\n      console.log('ID Bobina:', bobinaId);\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/update-for-compatibility`, {\n        id_bobina: bobinaId\n      }, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo for compatibility error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError ||\n          !error.response || error.code === 'ECONNABORTED' ||\n          (error.message && error.message.includes('Network Error'))) {\n\n        console.log('Errore di rete o timeout, verifica se il cavo è stato aggiornato...');\n\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se è stato aggiornato\n          const verificaCavo = await caviService.getCavoById(cantiereId, cavoId);\n          if (verificaCavo) {\n            console.log('Il cavo risulta aggiornato nonostante l\\'errore');\n            return verificaCavo;\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica:', verifyError);\n        }\n      }\n\n      throw error.response ? error.response.data : { detail: error.message, status: 500 };\n    }\n  },\n\n  // Riattiva un cavo SPARE\n  reactivateSpare: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/reactivate-spare`);\n\n      // Chiamata API per riattivare il cavo SPARE\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/reactivate-spare`, {}, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Reactivate spare error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError ||\n          !error.response || error.code === 'ECONNABORTED' ||\n          (error.message && error.message.includes('Network Error'))) {\n\n        console.log('Errore di rete o timeout, verifica se il cavo è stato riattivato...');\n\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se è stato riattivato\n          const verificaCavo = await caviService.getCavoById(cantiereId, cavoId);\n          if (verificaCavo && verificaCavo.modificato_manualmente !== 3) {\n            console.log('Il cavo risulta riattivato nonostante l\\'errore');\n            return verificaCavo;\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica:', verifyError);\n        }\n      }\n\n      throw error;\n    }\n  },\n\n  // Ottiene la lista dei cavi installati di un cantiere\n  getCaviInstallati: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Caricamento cavi installati per cantiere ${cantiereIdNum}...`);\n\n      try {\n        // Primo tentativo con axiosInstance\n        console.log(`URL API: ${axiosInstance.defaults.baseURL}/cavi/${cantiereIdNum}/installati`);\n        const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`, {\n          timeout: 30000 // 30 secondi\n        });\n\n        console.log(`Risposta getCaviInstallati: ${response.data ? response.data.length : 0} cavi installati trovati`);\n        if (response.data && response.data.length > 0) {\n          console.log('Primo cavo installato:', response.data[0]);\n        } else {\n          console.log('Nessun cavo installato trovato');\n        }\n\n        return response.data;\n      } catch (primaryError) {\n        console.error('Errore con richiesta primaria, tentativo alternativo:', primaryError);\n\n        // Tentativo alternativo con axios standard\n        console.log(`URL API (alternativo): ${API_URL}/cavi/${cantiereIdNum}/installati`);\n        const token = localStorage.getItem('token');\n\n        const alternativeResponse = await axios.get(\n          `${API_URL}/cavi/${cantiereIdNum}/installati`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log(`Risposta alternativa: ${alternativeResponse.data ? alternativeResponse.data.length : 0} cavi installati trovati`);\n        return alternativeResponse.data;\n      }\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      console.error('Dettagli errore:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `/cavi/${cantiereId}/installati`\n      });\n\n      // Verifica se l'errore è dovuto a un problema di connessione\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout') || error.message.includes('Network Error')) {\n        console.error('Errore di connessione o timeout');\n        // Ritorna un array vuoto invece di lanciare un errore\n        console.log('Ritorno array vuoto come fallback');\n        return [];\n      }\n\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene le statistiche dei cavi di un cantiere\n  getCaviStats: async (cantiereId, revisione = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Costruisci l'URL con il parametro revisione se specificato\n      let url = `/cavi/${cantiereIdNum}/stats`;\n      if (revisione) {\n        url += `?revisione=${encodeURIComponent(revisione)}`;\n      }\n\n      const response = await axiosInstance.get(url);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi stats error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene la revisione corrente di un cantiere\n  getRevisioneCorrente: async (cantiereId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);\n      return response.data;\n    } catch (error) {\n      console.error('Get revisione corrente error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene tutte le revisioni disponibili di un cantiere\n  getRevisioniDisponibili: async (cantiereId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisioni`);\n      return response.data;\n    } catch (error) {\n      console.error('Get revisioni disponibili error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene direttamente i cavi SPARE\n  getCaviSpare: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log('Caricamento cavi SPARE...');\n\n      // Prova prima con l'endpoint standard con tipo_cavo=3\n      console.log('URL API (standard):', `${API_URL}/cavi/${cantiereIdNum}?tipo_cavo=3`);\n\n      try {\n        const response = await axios.get(\n          `${API_URL}/cavi/${cantiereIdNum}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000,\n            params: { tipo_cavo: 3 }\n          }\n        );\n\n        console.log('Risposta getCaviSpare (standard):', response.data ? response.data.length : 0, 'cavi SPARE trovati');\n        if (response.data && response.data.length > 0) {\n          console.log('Primo cavo SPARE:', response.data[0]);\n        }\n\n        return response.data;\n      } catch (standardError) {\n        console.error('Errore con endpoint standard, tentativo con endpoint dedicato:', standardError);\n\n        // Se fallisce, prova con l'endpoint dedicato\n        console.log('URL API (dedicato):', `${API_URL}/cavi/spare/${cantiereIdNum}`);\n\n        const dedicatedResponse = await axios.get(\n          `${API_URL}/cavi/spare/${cantiereIdNum}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Risposta getCaviSpare (dedicato):', dedicatedResponse.data ? dedicatedResponse.data.length : 0, 'cavi SPARE trovati');\n        if (dedicatedResponse.data && dedicatedResponse.data.length > 0) {\n          console.log('Primo cavo SPARE (dedicato):', dedicatedResponse.data[0]);\n        }\n\n        return dedicatedResponse.data;\n      }\n    } catch (error) {\n      console.error('Get cavi SPARE error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene i cavi installati di un cantiere per la gestione collegamenti\n  getCaviInstallati: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Caricamento cavi installati per cantiere ${cantiereIdNum}...`);\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta cavi installati:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Collega un lato di un cavo\n  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/collegamento`);\n      console.log('Dati:', { lato, responsabile });\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {\n        lato: lato,\n        responsabile: responsabile || 'cantiere'\n      }, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Collega cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\n      }\n\n      throw error.response ? error.response.data : { detail: error.message, status: 500 };\n    }\n  },\n\n  // Scollega un lato di un cavo\n  scollegaCavo: async (cantiereId, cavoId, lato) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta DELETE a /cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);\n\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Scollega cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\n      }\n\n      throw error.response ? error.response.data : { detail: error.message, status: 500 };\n    }\n  },\n\n  // Annulla l'installazione di un cavo\n  cancelInstallation: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/cancel-installation`);\n\n      try {\n        // Prima prova con POST su cancel-installation\n        const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/cancel-installation`, {}, {\n          timeout: 30000, // 30 secondi\n        });\n        return response.data;\n      } catch (postError) {\n        console.log('POST su cancel-installation fallito, tentativo con PUT:', postError);\n\n        try {\n          // Se POST fallisce, prova con PUT\n          const putResponse = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}/cancel-installation`, {}, {\n            timeout: 30000, // 30 secondi\n          });\n          return putResponse.data;\n        } catch (putError) {\n          console.log('PUT su cancel-installation fallito, tentativo con endpoint alternativo:', putError);\n\n          // Se anche PUT fallisce, prova con l'endpoint alternativo\n          const altResponse = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/annulla-installazione`, {}, {\n            timeout: 30000, // 30 secondi\n          });\n          return altResponse.data;\n        }\n      }\n\n\n    } catch (error) {\n      console.error('Cancel installation error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError ||\n          !error.response || error.code === 'ECONNABORTED' ||\n          (error.message && error.message.includes('Network Error'))) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\n      }\n\n      throw error.response ? error.response.data : { detail: error.message, status: 500 };\n    }\n  },\n\n  // Verifica lo stato di un cavo specifico (debug)\n  debugCavo: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Ottieni i cavi attivi\n      console.log('Verificando cavo tra i cavi attivi...');\n      const attivi = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=0`, {\n        timeout: 60000, // 60 secondi\n      });\n      const cavoAttivo = attivi.data.find(c => c.id_cavo === cavoId);\n\n      // Ottieni i cavi SPARE\n      console.log('Verificando cavo tra i cavi SPARE...');\n      const spare = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=3`, {\n        timeout: 60000, // 60 secondi\n      });\n      const cavoSpare = spare.data.find(c => c.id_cavo === cavoId);\n\n      return {\n        trovato_tra_attivi: !!cavoAttivo,\n        trovato_tra_spare: !!cavoSpare,\n        cavo_attivo: cavoAttivo,\n        cavo_spare: cavoSpare\n      };\n    } catch (error) {\n      console.error('Debug cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\n      }\n\n      throw error.response ? error.response.data : { detail: error.message, status: 500 };\n    }\n  }\n};\n\nexport default caviService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;;AAEzC;AACA,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;AAE9B,MAAMC,WAAW,GAAG;EAClB;EACAC,OAAO,EAAE,MAAAA,CAAOC,UAAU,EAAEC,QAAQ,GAAG,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC5D,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QAAEJ,UAAU;QAAEC,QAAQ;QAAEC;MAAQ,CAAC,CAAC;MACvEC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,OAAOJ,UAAU,CAAC;;MAErD;MACA,IAAIA,UAAU,KAAKK,SAAS,IAAIL,UAAU,KAAK,IAAI,EAAE;QACnDG,OAAO,CAACG,KAAK,CAAC,+BAA+B,CAAC;QAC9C,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;MACzC;;MAEA;MACA,IAAIC,aAAa,GAAGR,UAAU;MAC9B,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;QAClCQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;QACxCG,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEI,aAAa,CAAC;MAC1E;MAEA,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;QACxBL,OAAO,CAACG,KAAK,CAAC,qCAAqC,EAAEN,UAAU,CAAC;QAChE,MAAM,IAAIO,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,iCAAiCI,aAAa,kBAAkBP,QAAQ,EAAE,CAAC;;MAEvF;MACA,IAAIA,QAAQ,KAAK,CAAC,EAAE;QAClBE,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1D,IAAI;UACF;UACA,MAAMO,QAAQ,GAAG,MAAMjB,KAAK,CAACkB,GAAG,CAC9B,GAAGf,OAAO,eAAeW,aAAa,EAAE,EACxC;YACEK,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC1D,CAAC;YACDC,OAAO,EAAE;UACX,CACF,CAAC;UAEDb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,QAAQ,CAACM,IAAI,CAAC;UAClD,OAAON,QAAQ,CAACM,IAAI;QACtB,CAAC,CAAC,OAAOC,UAAU,EAAE;UACnBf,OAAO,CAACG,KAAK,CAAC,wCAAwC,EAAEY,UAAU,CAAC;UACnE;QACF;MACF;;MAEA;MACA,IAAIC,GAAG,GAAG,SAASX,aAAa,EAAE;MAClC,MAAMY,WAAW,GAAG,EAAE;MAEtB,IAAInB,QAAQ,KAAK,IAAI,EAAE;QACrBmB,WAAW,CAACC,IAAI,CAAC,aAAapB,QAAQ,EAAE,CAAC;MAC3C;;MAEA;MACA,IAAIC,OAAO,CAACoB,mBAAmB,EAAE;QAC/BF,WAAW,CAACC,IAAI,CAAC,uBAAuBE,kBAAkB,CAACrB,OAAO,CAACoB,mBAAmB,CAAC,EAAE,CAAC;MAC5F;MAEA,IAAIpB,OAAO,CAACsB,SAAS,EAAE;QACrBJ,WAAW,CAACC,IAAI,CAAC,aAAaE,kBAAkB,CAACrB,OAAO,CAACsB,SAAS,CAAC,EAAE,CAAC;MACxE;MAEA,IAAItB,OAAO,CAACuB,OAAO,EAAE;QACnBL,WAAW,CAACC,IAAI,CAAC,WAAWE,kBAAkB,CAACrB,OAAO,CAACuB,OAAO,CAAC,EAAE,CAAC;QAClE,IAAIvB,OAAO,CAACwB,UAAU,EAAE;UACtBN,WAAW,CAACC,IAAI,CAAC,cAAcE,kBAAkB,CAACrB,OAAO,CAACwB,UAAU,CAAC,EAAE,CAAC;QAC1E;MACF;;MAEA;MACA,IAAIN,WAAW,CAACO,MAAM,GAAG,CAAC,EAAE;QAC1BR,GAAG,IAAI,IAAIC,WAAW,CAACQ,IAAI,CAAC,GAAG,CAAC,EAAE;MACpC;;MAEA;MACAzB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEe,GAAG,CAAC;MACrChB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEgB,WAAW,CAAC;MAE/CjB,OAAO,CAACC,GAAG,CAAC,qBAAqBe,GAAG,EAAE,CAAC;MACvChB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEU,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,UAAU,CAAC;MAC9EZ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,GAAGP,OAAO,GAAGsB,GAAG,EAAE,CAAC;MAEhD,IAAI;QACFhB,OAAO,CAACC,GAAG,CAAC,kCAAkCe,GAAG,eAAeL,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,UAAU,EAAE,CAAC;QAC1HZ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;UACtC,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUU,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC,CAAC;;QAEF;QACA,MAAMJ,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAACO,GAAG,EAAE;UAAEH,OAAO,EAAE;QAAM,CAAC,CAAC;QAEjEb,OAAO,CAACC,GAAG,CAAC,iBAAiBe,GAAG,EAAE,EAAER,QAAQ,CAACM,IAAI,CAAC;QAClDd,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEO,QAAQ,CAACkB,MAAM,CAAC;QACtD1B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEO,QAAQ,CAACE,OAAO,CAAC;QAExD,IAAIiB,KAAK,CAACC,OAAO,CAACpB,QAAQ,CAACM,IAAI,CAAC,EAAE;UAChCd,OAAO,CAACC,GAAG,CAAC,4BAA4BO,QAAQ,CAACM,IAAI,CAACU,MAAM,EAAE,CAAC;UAC/D,IAAIhB,QAAQ,CAACM,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;YAC5BxB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,QAAQ,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,MAAM;YACLd,OAAO,CAAC6B,IAAI,CAAC,uCAAuCxB,aAAa,aAAaP,QAAQ,EAAE,CAAC;UAC3F;QACF,CAAC,MAAM;UACLE,OAAO,CAAC6B,IAAI,CAAC,4BAA4B,OAAOrB,QAAQ,CAACM,IAAI,EAAE,EAAEN,QAAQ,CAACM,IAAI,CAAC;QACjF;QAEA,OAAON,QAAQ,CAACM,IAAI;MACtB,CAAC,CAAC,OAAOgB,QAAQ,EAAE;QAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;QACjBlC,OAAO,CAACG,KAAK,CAAC,iCAAiCa,GAAG,GAAG,EAAEc,QAAQ,CAAC;QAChE9B,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAE;UACpCgC,OAAO,EAAEL,QAAQ,CAACK,OAAO;UACzBT,MAAM,GAAAK,kBAAA,GAAED,QAAQ,CAACtB,QAAQ,cAAAuB,kBAAA,uBAAjBA,kBAAA,CAAmBL,MAAM;UACjCU,UAAU,GAAAJ,mBAAA,GAAEF,QAAQ,CAACtB,QAAQ,cAAAwB,mBAAA,uBAAjBA,mBAAA,CAAmBI,UAAU;UACzCtB,IAAI,GAAAmB,mBAAA,GAAEH,QAAQ,CAACtB,QAAQ,cAAAyB,mBAAA,uBAAjBA,mBAAA,CAAmBnB,IAAI;UAC7BJ,OAAO,GAAAwB,mBAAA,GAAEJ,QAAQ,CAACtB,QAAQ,cAAA0B,mBAAA,uBAAjBA,mBAAA,CAAmBxB,OAAO;UACnC2B,IAAI,EAAEP,QAAQ,CAACO,IAAI;UACnBC,YAAY,EAAER,QAAQ,CAACQ,YAAY;UACnC9C,MAAM,EAAEsC,QAAQ,CAACtC,MAAM,GAAG;YACxBwB,GAAG,EAAEc,QAAQ,CAACtC,MAAM,CAACwB,GAAG;YACxBuB,MAAM,EAAET,QAAQ,CAACtC,MAAM,CAAC+C,MAAM;YAC9B1B,OAAO,EAAEiB,QAAQ,CAACtC,MAAM,CAACqB,OAAO;YAChCH,OAAO,EAAEoB,QAAQ,CAACtC,MAAM,CAACkB;UAC3B,CAAC,GAAG;QACN,CAAC,CAAC;;QAEF;QACA,IAAIoB,QAAQ,CAACO,IAAI,KAAK,aAAa,EAAE;UACnCrC,OAAO,CAACG,KAAK,CAAC,0EAA0E,CAAC;UACzF;UACA,IAAI;YACFH,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;YAC7D,MAAMuC,YAAY,GAAG,MAAMC,KAAK,CAAC/C,OAAO,CAAC;YACzCM,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEuC,YAAY,CAACd,MAAM,CAAC;UACrE,CAAC,CAAC,OAAOgB,SAAS,EAAE;YAClB1C,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEuC,SAAS,CAAC;UACrE;QACF;QAEA,MAAMZ,QAAQ;MAChB;IACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MAAA,IAAAwC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdjD,OAAO,CAACG,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCH,OAAO,CAACG,KAAK,CAAC,gBAAgB,EAAE;QAC9BgC,OAAO,EAAEhC,KAAK,CAACgC,OAAO;QACtBT,MAAM,GAAAiB,eAAA,GAAExC,KAAK,CAACK,QAAQ,cAAAmC,eAAA,uBAAdA,eAAA,CAAgBjB,MAAM;QAC9BU,UAAU,GAAAQ,gBAAA,GAAEzC,KAAK,CAACK,QAAQ,cAAAoC,gBAAA,uBAAdA,gBAAA,CAAgBR,UAAU;QACtCtB,IAAI,GAAA+B,gBAAA,GAAE1C,KAAK,CAACK,QAAQ,cAAAqC,gBAAA,uBAAdA,gBAAA,CAAgB/B,IAAI;QAC1BE,GAAG,EAAE,SAASnB,UAAU,GAAGC,QAAQ,KAAK,IAAI,GAAG,cAAcA,QAAQ,EAAE,GAAG,EAAE,EAAE;QAC9EoD,KAAK,EAAE/C,KAAK,CAAC+C;MACf,CAAC,CAAC;;MAEF;MACA,IAAI/C,KAAK,CAACkC,IAAI,KAAK,cAAc,IAAIlC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,SAAS,CAAC,IAAIhD,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAC,EAAE;QACjHnD,OAAO,CAACG,KAAK,CAAC,iCAAiC,CAAC;QAChD;QACAH,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,OAAO,EAAE;MACX;;MAEA;MACA,MAAMmD,aAAa,GAAG,IAAIhD,KAAK,CAAC,EAAA0C,gBAAA,GAAA3C,KAAK,CAACK,QAAQ,cAAAsC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsBM,MAAM,KAAIlD,KAAK,CAACgC,OAAO,IAAI,oBAAoB,CAAC;MACtGiB,aAAa,CAAC1B,MAAM,IAAAsB,gBAAA,GAAG7C,KAAK,CAACK,QAAQ,cAAAwC,gBAAA,uBAAdA,gBAAA,CAAgBtB,MAAM;MAC7C0B,aAAa,CAACtC,IAAI,IAAAmC,gBAAA,GAAG9C,KAAK,CAACK,QAAQ,cAAAyC,gBAAA,uBAAdA,gBAAA,CAAgBnC,IAAI;MACzCsC,aAAa,CAAC5C,QAAQ,GAAGL,KAAK,CAACK,QAAQ;MACvC4C,aAAa,CAACE,aAAa,GAAGnD,KAAK;MACnCiD,aAAa,CAACf,IAAI,GAAGlC,KAAK,CAACkC,IAAI;MAC/Be,aAAa,CAACd,YAAY,GAAGnC,KAAK,CAACmC,YAAY;MAE/C,MAAMc,aAAa;IACrB;EACF,CAAC;EAED;EACAG,UAAU,EAAE,MAAAA,CAAO1D,UAAU,EAAE2D,QAAQ,KAAK;IAC1C,IAAI;MACF;MACA,MAAMnD,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,4CAA4CI,aAAa,EAAE,CAAC;MACxEL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEwD,IAAI,CAACC,SAAS,CAACF,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAE/D;MACA,MAAMhD,QAAQ,GAAG,MAAMf,aAAa,CAACkE,IAAI,CAAC,SAAStD,aAAa,EAAE,EAAEmD,QAAQ,EAAE;QAC5E3C,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,QAAQ,CAACkB,MAAM,EAAElB,QAAQ,CAAC4B,UAAU,CAAC;MACzEpC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAC5C,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;;MAE1C;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,IAC5C,CAAC1D,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACkC,IAAI,KAAK,cAAc,IAC/ClC,KAAK,CAACgC,OAAO,IAAIhC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAE,EAAE;QAE9DnD,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;QAE9E,IAAI;UACF;UACA,MAAM,IAAI6D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEvD;UACA,MAAME,KAAK,GAAGtD,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C;UACA,MAAMsD,gBAAgB,GAAG5D,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;UACjD,MAAMsE,aAAa,GAAG,MAAM5E,KAAK,CAACkB,GAAG,CACnC,GAAGf,OAAO,SAASwE,gBAAgB,UAAUV,QAAQ,CAACY,OAAO,EAAE,EAC/D;YACE1D,OAAO,EAAE;cACP,eAAe,EAAE,UAAUuD,KAAK;YAClC,CAAC;YACDpD,OAAO,EAAE;UACX,CACF,CAAC;UAED,IAAIsD,aAAa,CAACrD,IAAI,IAAIqD,aAAa,CAACrD,IAAI,CAACuD,MAAM,EAAE;YACnDrE,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;YAC3E;YACA,MAAMqE,YAAY,GAAG,MAAM/E,KAAK,CAACkB,GAAG,CAClC,GAAGf,OAAO,SAASwE,gBAAgB,IAAIV,QAAQ,CAACY,OAAO,EAAE,EACzD;cACE1D,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUuD,KAAK;cAClC,CAAC;cACDpD,OAAO,EAAE;YACX,CACF,CAAC;YAED,IAAIyD,YAAY,CAACxD,IAAI,EAAE;cACrBd,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEqE,YAAY,CAACxD,IAAI,CAAC;cAC3D,OAAOwD,YAAY,CAACxD,IAAI;YAC1B;UACF;QACF,CAAC,CAAC,OAAOyD,WAAW,EAAE;UACpBvE,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEoE,WAAW,CAAC;QACvE;;QAEA;QACAvE,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACqE,aAAa,IAAIrE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UACJkB,MAAM,EAAE,gHAAgH;UACxH3B,MAAM,EAAE,CAAC;UACTkC,cAAc,EAAE;QAClB,CAAC;MACH;MAEA,IAAIzD,KAAK,CAACK,QAAQ,EAAE;QAClBR,OAAO,CAACG,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAACK,QAAQ,CAACM,IAAI,CAAC;QACtDd,OAAO,CAACG,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACK,QAAQ,CAACkB,MAAM,CAAC;QACtD1B,OAAO,CAACG,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAACK,QAAQ,CAACE,OAAO,CAAC;;QAExD;QACA,MAAM+D,WAAW,GAAGtE,KAAK,CAACK,QAAQ,CAACM,IAAI,CAACuC,MAAM,IAAI,oBAAoB;QACtE,MAAM;UAAEA,MAAM,EAAEoB,WAAW;UAAE/C,MAAM,EAAEvB,KAAK,CAACK,QAAQ,CAACkB;QAAO,CAAC;MAC9D;MACA;MACA,IAAIvB,KAAK,YAAYC,KAAK,EAAE;QAC1B,MAAM;UAAEiD,MAAM,EAAElD,KAAK,CAACgC,OAAO;UAAET,MAAM,EAAE;QAAI,CAAC;MAC9C;MACA,MAAMvB,KAAK;IACb;EACF,CAAC;EAED;EACAuE,WAAW,EAAE,MAAAA,CAAO7E,UAAU,EAAE8E,MAAM,KAAK;IACzC,IAAI;MACF;MACA,MAAMtE,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,qCAAqC0E,MAAM,iBAAiBtE,aAAa,EAAE,CAAC;;MAExF;MACA,MAAMG,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,IAAIsE,MAAM,EAAE,EAAE;QAC3E9D,OAAO,EAAE,KAAK,CAAC;MACjB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAC3C,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;MAE7C;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,IAC5C,CAAC1D,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACkC,IAAI,KAAK,cAAc,IAC/ClC,KAAK,CAACgC,OAAO,IAAIhC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAE,EAAE;QAE9DnD,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;QAE7E,IAAI;UACF;UACAD,OAAO,CAACC,GAAG,CAAC,8CAA8C0E,MAAM,KAAK,CAAC;UACtE,MAAMV,KAAK,GAAGtD,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,MAAMlB,OAAO,GAAGD,aAAa,CAACmF,QAAQ,CAACC,OAAO;;UAE9C;UACA,MAAMC,WAAW,GAAG,MAAMvF,KAAK,CAACkB,GAAG,CACjC,GAAGf,OAAO,SAASG,UAAU,IAAI8E,MAAM,EAAE,EACzC;YACEjE,OAAO,EAAE;cACP,eAAe,EAAE,UAAUuD,KAAK;YAClC,CAAC;YACDpD,OAAO,EAAE,KAAK,CAAC;UACjB,CACF,CAAC;UAEDb,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE6E,WAAW,CAAChE,IAAI,CAAC;UAC/D,OAAOgE,WAAW,CAAChE,IAAI;QACzB,CAAC,CAAC,OAAOiE,QAAQ,EAAE;UACjB/E,OAAO,CAACG,KAAK,CAAC,2CAA2C,EAAE4E,QAAQ,CAAC;UACpE,MAAM;YACJ1B,MAAM,EAAE,+EAA+E;YACvF3B,MAAM,EAAE,CAAC;YACTkC,cAAc,EAAE,IAAI;YACpBN,aAAa,EAAEnD,KAAK,CAACgC;UACvB,CAAC;QACH;MACF;MAEA,MAAMhC,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACA6E,UAAU,EAAE,MAAAA,CAAOnF,UAAU,EAAE8E,MAAM,EAAEnB,QAAQ,KAAK;IAClD,IAAI;MACF;MACA,MAAMnD,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,kCAAkCI,aAAa,IAAIsE,MAAM,EAAE,CAAC;MACxE3E,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEuD,QAAQ,CAAC;;MAEtC;MACA,IAAI;QACFxD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD,MAAMgF,YAAY,GAAG,MAAMxC,KAAK,CAAC,GAAG/C,OAAO,SAAS,EAAE;UAAE6C,MAAM,EAAE;QAAM,CAAC,CAAC;QACxEvC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEgF,YAAY,CAACvD,MAAM,EAAEuD,YAAY,CAAC7C,UAAU,CAAC;QAC7E,IAAI,CAAC6C,YAAY,CAACC,EAAE,EAAE;UACpBlF,OAAO,CAACG,KAAK,CAAC,uCAAuC,EAAE8E,YAAY,CAACvD,MAAM,CAAC;UAC3E,MAAM,IAAItB,KAAK,CAAC,0DAA0D,CAAC;QAC7E;MACF,CAAC,CAAC,OAAO+E,SAAS,EAAE;QAClBnF,OAAO,CAACG,KAAK,CAAC,oCAAoC,EAAEgF,SAAS,CAAC;QAC9D,MAAM,IAAI/E,KAAK,CAAC,+EAA+E,CAAC;MAClG;;MAEA;MACA,MAAMI,QAAQ,GAAG,MAAMf,aAAa,CAAC2F,GAAG,CAAC,SAAS/E,aAAa,IAAIsE,MAAM,EAAE,EAAEnB,QAAQ,EAAE;QACrF3C,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;;MAE1C;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,IAC5C,CAAC1D,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACkC,IAAI,KAAK,cAAc,IAC/ClC,KAAK,CAACgC,OAAO,IAAIhC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAE,IAC1DhD,KAAK,CAACkF,OAAO,EAAE;QAEjBrF,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;QAElF,IAAI;UACF;UACA,MAAM,IAAI6D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEvD;UACA,MAAME,KAAK,GAAGtD,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C;UACA,MAAMsD,gBAAgB,GAAG5D,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;UACjD,MAAMyE,YAAY,GAAG,MAAM/E,KAAK,CAACkB,GAAG,CAClC,GAAGf,OAAO,SAASwE,gBAAgB,IAAIS,MAAM,EAAE,EAC/C;YACEjE,OAAO,EAAE;cACP,eAAe,EAAE,UAAUuD,KAAK;YAClC,CAAC;YACDpD,OAAO,EAAE;UACX,CACF,CAAC;UAED,IAAIyD,YAAY,CAACxD,IAAI,EAAE;YACrBd,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;;YAExE;YACA,IAAIqF,SAAS,GAAG,KAAK;YACrB,KAAK,MAAMC,GAAG,IAAI/B,QAAQ,EAAE;cAC1B,IAAIA,QAAQ,CAAC+B,GAAG,CAAC,KAAKrF,SAAS,IAC3BuD,IAAI,CAACC,SAAS,CAACF,QAAQ,CAAC+B,GAAG,CAAC,CAAC,KAAK9B,IAAI,CAACC,SAAS,CAACY,YAAY,CAACxD,IAAI,CAACyE,GAAG,CAAC,CAAC,EAAE;gBAC5EvF,OAAO,CAACC,GAAG,CAAC,SAASsF,GAAG,wBAAwB/B,QAAQ,CAAC+B,GAAG,CAAC,EAAE,CAAC;gBAChED,SAAS,GAAG,IAAI;gBAChB;cACF;YACF;YAEA,IAAIA,SAAS,EAAE;cACbtF,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;cAC/E,OAAOqE,YAAY,CAACxD,IAAI;YAC1B,CAAC,MAAM;cACLd,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;YACzD;UACF;QACF,CAAC,CAAC,OAAOsE,WAAW,EAAE;UACpBvE,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEoE,WAAW,CAAC;QACvE;;QAEA;QACAvE,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACqE,aAAa,IAAIrE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UACJkB,MAAM,EAAE,+GAA+G;UACvH3B,MAAM,EAAE,CAAC;UACTkC,cAAc,EAAE;QAClB,CAAC;MACH;;MAEA;MACA,IAAIzD,KAAK,CAACK,QAAQ,EAAE;QAClB;QACAR,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAACK,QAAQ,CAACkB,MAAM,EAAEvB,KAAK,CAACK,QAAQ,CAAC4B,UAAU,CAAC;QACrFpC,OAAO,CAACG,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACK,QAAQ,CAACM,IAAI,CAAC;QAClD,MAAMX,KAAK,CAACK,QAAQ,CAACM,IAAI;MAC3B,CAAC,MAAM;QACL;QACAd,OAAO,CAACG,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAACgC,OAAO,CAAC;QAC/E,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACgC,OAAO;UAAET,MAAM,EAAE;QAAI,CAAC;MAC9C;IACF;EACF,CAAC;EAED;EACA8D,oBAAoB,EAAE,MAAO3F,UAAU,IAAK;IAC1C,IAAI;MACF;MACA,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMW,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,qBAAqB,CAAC;MACrF,OAAOG,QAAQ,CAACM,IAAI,CAAC2E,kBAAkB;IACzC,CAAC,CAAC,OAAOtF,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC;EAED;EACAuF,eAAe,EAAE,MAAAA,CAAO7F,UAAU,EAAE8E,MAAM,EAAEgB,KAAK,GAAG,KAAK,KAAK;IAC5D,IAAI;MACF;MACA,MAAMtF,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QAAEJ,UAAU,EAAEQ,aAAa;QAAEsE,MAAM;QAAEgB;MAAM,CAAC,CAAC;;MAElG;MACA3F,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,GAAGP,OAAO,SAASW,aAAa,IAAIsE,MAAM,gBAAgB,CAAC;MAE1F,IAAI;QACF,MAAMiB,YAAY,GAAG,MAAMrG,KAAK,CAACoE,IAAI,CACnC,GAAGjE,OAAO,SAASW,aAAa,IAAIsE,MAAM,gBAAgB,EAC1D;UAAEgB,KAAK,EAAEA;QAAM,CAAC,EAChB;UACEjF,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE;QACX,CACF,CAAC;QAEDb,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE2F,YAAY,CAAC9E,IAAI,CAAC;;QAElE;QACA,MAAM,IAAIgD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;QAEvD;QACA/D,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE,MAAMqE,YAAY,GAAG,MAAM/E,KAAK,CAACkB,GAAG,CAClC,GAAGf,OAAO,SAASW,aAAa,IAAIsE,MAAM,EAAE,EAC5C;UACEjE,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE;QACX,CACF,CAAC;QAEDb,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEqE,YAAY,CAACxD,IAAI,CAAC;;QAEhE;QACA,IAAIwD,YAAY,CAACxD,IAAI,CAAC+E,sBAAsB,KAAK,CAAC,EAAE;UAClD7F,OAAO,CAACG,KAAK,CAAC,8EAA8E,CAAC;UAC7F,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;QAC3D;QAEA,OAAOkE,YAAY,CAACxD,IAAI;MAC1B,CAAC,CAAC,OAAOgF,SAAS,EAAE;QAClB;QACA9F,OAAO,CAACG,KAAK,CAAC,4DAA4D,EAAE2F,SAAS,CAAC;QACtF9F,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,GAAGP,OAAO,SAASW,aAAa,IAAIsE,MAAM,aAAa,CAAC;QAEzF,MAAMoB,cAAc,GAAG,MAAMxG,KAAK,CAACyG,MAAM,CACvC,GAAGtG,OAAO,SAASW,aAAa,IAAIsE,MAAM,EAAE,EAC5C;UACEjE,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE,KAAK;UACdoF,MAAM,EAAE;YAAEC,IAAI,EAAE;UAAQ;QAC1B,CACF,CAAC;QAEDlG,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE8F,cAAc,CAACjF,IAAI,CAAC;;QAEjF;QACA,MAAM,IAAIgD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;QAEvD;QACA/D,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;QAC/E,MAAMqE,YAAY,GAAG,MAAM/E,KAAK,CAACkB,GAAG,CAClC,GAAGf,OAAO,SAASW,aAAa,IAAIsE,MAAM,EAAE,EAC5C;UACEjE,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE;QACX,CACF,CAAC;QAEDb,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEqE,YAAY,CAACxD,IAAI,CAAC;;QAE3E;QACA,IAAIwD,YAAY,CAACxD,IAAI,CAAC+E,sBAAsB,KAAK,CAAC,EAAE;UAClD7F,OAAO,CAACG,KAAK,CAAC,8EAA8E,CAAC;UAC7F,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;QAC3D;QAEA,OAAOkE,YAAY,CAACxD,IAAI;MAC1B;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA,IAAAgG,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdrG,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDH,OAAO,CAACG,KAAK,CAAC,kBAAkB,EAAE;QAChCgC,OAAO,EAAEhC,KAAK,CAACgC,OAAO;QACtBT,MAAM,GAAAyE,gBAAA,GAAEhG,KAAK,CAACK,QAAQ,cAAA2F,gBAAA,uBAAdA,gBAAA,CAAgBzE,MAAM;QAC9BU,UAAU,GAAAgE,gBAAA,GAAEjG,KAAK,CAACK,QAAQ,cAAA4F,gBAAA,uBAAdA,gBAAA,CAAgBhE,UAAU;QACtCtB,IAAI,GAAAuF,gBAAA,GAAElG,KAAK,CAACK,QAAQ,cAAA6F,gBAAA,uBAAdA,gBAAA,CAAgBvF,IAAI;QAC1BE,GAAG,EAAE,GAAGtB,OAAO,SAASG,UAAU,IAAI8E,MAAM,EAAE;QAC9CnF,MAAM,EAAEW,KAAK,CAACX;MAChB,CAAC,CAAC;MACF,MAAMW,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACAmG,UAAU,EAAE,MAAAA,CAAOzG,UAAU,EAAE8E,MAAM,EAAEuB,IAAI,GAAG,IAAI,KAAK;IACrD,IAAI;MACF;MACA,MAAM7F,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAAEJ,UAAU,EAAEQ,aAAa;QAAEsE,MAAM;QAAEuB;MAAK,CAAC,CAAC;;MAEhG;MACA,MAAMK,aAAa,GAAG;QACpB7F,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACDC,OAAO,EAAE,KAAK;QAAE;QAChBoF,MAAM,EAAEC,IAAI,GAAG;UAAEA;QAAK,CAAC,GAAG,CAAC;MAC7B,CAAC;MAEDlG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,GAAGP,OAAO,SAASW,aAAa,IAAIsE,MAAM,EAAE,CAAC;MACrE3E,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEsG,aAAa,CAAC;;MAErC;MACA,MAAM/F,QAAQ,GAAG,MAAMjB,KAAK,CAACyG,MAAM,CAAC,GAAGtG,OAAO,SAASW,aAAa,IAAIsE,MAAM,EAAE,EAAE4B,aAAa,CAAC;MAChGvG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAClD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA,IAAAqG,gBAAA,EAAAC,gBAAA,EAAAC,iBAAA;MACd1G,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CH,OAAO,CAACG,KAAK,CAAC,gBAAgB,EAAE;QAC9BgC,OAAO,EAAEhC,KAAK,CAACgC,OAAO;QACtBT,MAAM,GAAA8E,gBAAA,GAAErG,KAAK,CAACK,QAAQ,cAAAgG,gBAAA,uBAAdA,gBAAA,CAAgB9E,MAAM;QAC9BU,UAAU,GAAAqE,gBAAA,GAAEtG,KAAK,CAACK,QAAQ,cAAAiG,gBAAA,uBAAdA,gBAAA,CAAgBrE,UAAU;QACtCtB,IAAI,GAAA4F,iBAAA,GAAEvG,KAAK,CAACK,QAAQ,cAAAkG,iBAAA,uBAAdA,iBAAA,CAAgB5F,IAAI;QAC1BE,GAAG,EAAE,GAAGtB,OAAO,SAASG,UAAU,IAAI8E,MAAM,EAAE;QAC9CnF,MAAM,EAAEW,KAAK,CAACX;MAChB,CAAC,CAAC;;MAEF;MACA,IAAIW,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACK,QAAQ,CAACM,IAAI,EAAE;QACzC,MAAMX,KAAK,CAACK,QAAQ,CAACM,IAAI;MAC3B,CAAC,MAAM,IAAIX,KAAK,CAACgC,OAAO,EAAE;QACxB,MAAM,IAAI/B,KAAK,CAACD,KAAK,CAACgC,OAAO,CAAC;MAChC,CAAC,MAAM;QACL,MAAM,IAAI/B,KAAK,CAAC,yCAAyC,CAAC;MAC5D;IACF;EACF,CAAC;EAED;EACAuG,0BAA0B,EAAE,MAAAA,CAAO9G,UAAU,EAAE8E,MAAM,EAAEiC,QAAQ,KAAK;IAClE,IAAI;MACF;MACA,MAAMvG,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,mCAAmCI,aAAa,IAAIsE,MAAM,2BAA2B,CAAC;MAClG3E,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;QAAE4G,SAAS,EAAED;MAAS,CAAC,CAAC;;MAE7C;MACA,MAAME,WAAW,GAAG;QAClBD,SAAS,EAAED;MACb,CAAC;;MAED;MACA,MAAMpG,QAAQ,GAAG,MAAMf,aAAa,CAACkE,IAAI,CAAC,SAAStD,aAAa,IAAIsE,MAAM,2BAA2B,EAAEmC,WAAW,EAAE;QAClHjG,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;;MAE5D;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,EAAE;QAChD7D,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACqE,aAAa,IAAIrE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACqE,aAAa,IAAI,+EAA+E;UAAE9C,MAAM,EAAE,CAAC;UAAEkC,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,IAAIzD,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACK,QAAQ,CAACM,IAAI,EAAE;QACzC,IAAI2D,WAAW,GAAGtE,KAAK,CAACK,QAAQ,CAACM,IAAI,CAACuC,MAAM,IAAI,oBAAoB;QACpE;QACA,IAAI,OAAOoB,WAAW,KAAK,QAAQ,EAAE;UACnCA,WAAW,GAAGhB,IAAI,CAACC,SAAS,CAACe,WAAW,CAAC;QAC3C;QACA,MAAM;UAAEpB,MAAM,EAAEoB,WAAW;UAAE/C,MAAM,EAAEvB,KAAK,CAACK,QAAQ,CAACkB;QAAO,CAAC;MAC9D;MACA;MACA,IAAIvB,KAAK,YAAYC,KAAK,EAAE;QAC1B,MAAM;UAAEiD,MAAM,EAAElD,KAAK,CAACgC,OAAO;UAAET,MAAM,EAAE;QAAI,CAAC;MAC9C;MACA,MAAMvB,KAAK;IACb;EACF,CAAC;EAED;EACA4G,iBAAiB,EAAE,MAAAA,CAAOlH,UAAU,EAAE8E,MAAM,EAAEqC,WAAW,EAAEJ,QAAQ,GAAG,IAAI,EAAEK,SAAS,GAAG,KAAK,KAAK;IAChG,IAAI;MACF;MACA,MAAM5G,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,mCAAmCI,aAAa,IAAIsE,MAAM,eAAe,CAAC;MACtF3E,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;QAAEiH,YAAY,EAAEF,WAAW;QAAEH,SAAS,EAAED,QAAQ;QAAEO,UAAU,EAAEF;MAAU,CAAC,CAAC;;MAE/F;MACA,MAAMH,WAAW,GAAG;QAClBI,YAAY,EAAEF,WAAW;QACzBI,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;MACtC,CAAC;;MAED;MACA,IAAIV,QAAQ,KAAK,cAAc,EAAE;QAC/B;QACAE,WAAW,CAACD,SAAS,GAAG,cAAc;QACtC7G,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;;QAE5D;QACA;QACA6G,WAAW,CAACK,UAAU,GAAG,IAAI;QAC7BnH,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC9D,CAAC,MAAM,IAAI2G,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK1G,SAAS,EAAE;QACtD;QACA4G,WAAW,CAACD,SAAS,GAAGD,QAAQ;QAChC5G,OAAO,CAACC,GAAG,CAAC,0BAA0B2G,QAAQ,EAAE,CAAC;MACnD,CAAC,MAAM;QACL;QACAE,WAAW,CAACD,SAAS,GAAG,IAAI;QAC5B7G,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC5C;;MAEA;MACAD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEwD,IAAI,CAACC,SAAS,CAACoD,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAE9E;MACA;MACAA,WAAW,CAACK,UAAU,GAAG,IAAI;MAC7BnH,OAAO,CAACC,GAAG,CAAC,8EAA8E,CAAC;;MAE3F;MACA,MAAMO,QAAQ,GAAG,MAAMf,aAAa,CAACkE,IAAI,CAAC,SAAStD,aAAa,IAAIsE,MAAM,eAAe,EAAEmC,WAAW,EAAE;QACtGjG,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;MAElD;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,IAC5C,CAAC1D,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACkC,IAAI,KAAK,cAAc,IAC/ClC,KAAK,CAACgC,OAAO,IAAIhC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAE,EAAE;QAE9DnD,OAAO,CAACC,GAAG,CAAC,+EAA+E,CAAC;QAE5F,IAAI;UACF;UACA,MAAM,IAAI6D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEvD;UACA,MAAME,KAAK,GAAGtD,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C;UACA,MAAMsD,gBAAgB,GAAG5D,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;;UAEjD;UACA,MAAMyE,YAAY,GAAG,MAAM7E,aAAa,CAACgB,GAAG,CAAC,SAASyD,gBAAgB,IAAIS,MAAM,EAAE,EAAE;YAClF9D,OAAO,EAAE,KAAK,CAAC;UACjB,CAAC,CAAC;UAEF,IAAIyD,YAAY,CAACxD,IAAI,IAAIwD,YAAY,CAACxD,IAAI,CAACyG,eAAe,KAAKP,WAAW,EAAE;YAC1EhH,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;YACxF,OAAOqE,YAAY,CAACxD,IAAI;UAC1B;QACF,CAAC,CAAC,OAAOyD,WAAW,EAAE;UACpB;UACAvE,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEoE,WAAW,CAAC;QACvE;;QAEA;QACA;QACA,MAAM;UACJlB,MAAM,EAAE,6EAA6E;UACrF3B,MAAM,EAAE,CAAC;UACTkC,cAAc,EAAE;QAClB,CAAC;MACH;;MAEA;MACA,IAAIzD,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACK,QAAQ,CAACM,IAAI,EAAE;QACzC;QACA,MAAM0G,WAAW,GAAGrH,KAAK,CAACK,QAAQ,CAACM,IAAI;QACvC;QACA,MAAM2G,WAAW,GAAGD,WAAW,CAACnE,MAAM,GACpCmE,WAAW,CAACnE,MAAM,CAACqE,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC,GAC5F,kDAAkD;;QAEpD;QACA,IAAId,QAAQ,KAAK,cAAc,IAAIa,WAAW,CAACtE,QAAQ,CAAC,aAAa,CAAC,EAAE;UACtEnD,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;UACxE,MAAM;YACJoD,MAAM,EAAE,4CAA4C;YACpD3B,MAAM,EAAE,GAAG;YACXiG,OAAO,EAAE;UACX,CAAC;QACH;QAEA,MAAM;UACJtE,MAAM,EAAEoE,WAAW;UACnB/F,MAAM,EAAEvB,KAAK,CAACK,QAAQ,CAACkB;QACzB,CAAC;MACH,CAAC,MAAM;QACL;QACA;QACA,IAAIkF,QAAQ,KAAK,cAAc,EAAE;UAC/B5G,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;UACjF,MAAM;YACJoD,MAAM,EAAE,4CAA4C;YACpD3B,MAAM,EAAE,GAAG;YACXiG,OAAO,EAAE;UACX,CAAC;QACH;QAEA,MAAM;UACJtE,MAAM,EAAE,kDAAkD;UAC1D3B,MAAM,EAAE;QACV,CAAC;MACH;IACF;EACF,CAAC;EAED;EACAkG,YAAY,EAAE,MAAAA,CAAO/H,UAAU,EAAE8E,MAAM,EAAEiC,QAAQ,EAAEK,SAAS,GAAG,IAAI,KAAK;IACtE,IAAI;MACF;MACA,MAAM5G,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,mCAAmCI,aAAa,IAAIsE,MAAM,SAAS,CAAC;MAChF3E,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE2G,QAAQ,CAAC;MACnC5G,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgH,SAAS,CAAC;MAErC,MAAMzG,QAAQ,GAAG,MAAMf,aAAa,CAACkE,IAAI,CAAC,SAAStD,aAAa,IAAIsE,MAAM,SAAS,EAAE;QACnFkC,SAAS,EAAED,QAAQ;QACnBO,UAAU,EAAEF;MACd,CAAC,EAAE;QACDpG,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;MAE5C;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,IAC5C,CAAC1D,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACkC,IAAI,KAAK,cAAc,IAC/ClC,KAAK,CAACgC,OAAO,IAAIhC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAE,EAAE;QAE9DnD,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC;QAEpF,IAAI;UACF;UACA,MAAM,IAAI6D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEvD;UACA,MAAME,KAAK,GAAGtD,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C;UACA,MAAMsD,gBAAgB,GAAG5D,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;UACjD,MAAMyE,YAAY,GAAG,MAAM/E,KAAK,CAACkB,GAAG,CAClC,GAAGf,OAAO,SAASwE,gBAAgB,IAAIS,MAAM,EAAE,EAC/C;YACEjE,OAAO,EAAE;cACP,eAAe,EAAE,UAAUuD,KAAK;YAClC,CAAC;YACDpD,OAAO,EAAE;UACX,CACF,CAAC;UAED,IAAIyD,YAAY,CAACxD,IAAI,IAAIwD,YAAY,CAACxD,IAAI,CAAC+F,SAAS,KAAKD,QAAQ,EAAE;YACjE5G,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;YACjF,OAAOqE,YAAY,CAACxD,IAAI;UAC1B;QACF,CAAC,CAAC,OAAOyD,WAAW,EAAE;UACpBvE,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEoE,WAAW,CAAC;QACvE;;QAEA;QACAvE,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACqE,aAAa,IAAIrE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UACJkB,MAAM,EAAE,yGAAyG;UACjH3B,MAAM,EAAE,CAAC;UACTkC,cAAc,EAAE;QAClB,CAAC;MACH;MAEA,MAAMzD,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAG;QAAEuC,MAAM,EAAElD,KAAK,CAACgC,OAAO;QAAET,MAAM,EAAE;MAAI,CAAC;IACrF;EACF,CAAC;EAED;EACAmG,qBAAqB,EAAE,MAAAA,CAAOhI,UAAU,EAAE8E,MAAM,EAAEmD,UAAU,KAAK;IAC/D,IAAI;MACF;MACA,MAAMzH,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,0CAA0C0E,MAAM,wCAAwCmD,UAAU,CAACjB,SAAS,EAAE,CAAC;;MAE3H;MACA;MACA,MAAMkB,UAAU,GAAG;QACjB1G,SAAS,EAAEyG,UAAU,CAACzG,SAAS;QAC/B2G,OAAO,EAAEF,UAAU,CAACE,OAAO;QAC3BnC,sBAAsB,EAAE,CAAC,CAAC;MAC5B,CAAC;;MAED;MACA,MAAMrF,QAAQ,GAAG,MAAMf,aAAa,CAAC2F,GAAG,CAAC,SAAS/E,aAAa,IAAIsE,MAAM,EAAE,EAAEoD,UAAU,EAAE;QACvFlH,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,IAC5C,CAAC1D,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACkC,IAAI,KAAK,cAAc,IAC/ClC,KAAK,CAACgC,OAAO,IAAIhC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAE,EAAE;QAE9DnD,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;QAElF,IAAI;UACF;UACA,MAAM,IAAI6D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEvD;UACA;UACA,MAAMkE,YAAY,GAAG,MAAMtI,WAAW,CAAC+E,WAAW,CAAC7E,UAAU,EAAE8E,MAAM,CAAC;UACtE,IAAIsD,YAAY,IACZA,YAAY,CAAC5G,SAAS,KAAKyG,UAAU,CAACzG,SAAS,IAC/C6G,MAAM,CAACD,YAAY,CAACD,OAAO,CAAC,KAAKE,MAAM,CAACJ,UAAU,CAACE,OAAO,CAAC,EAAE;YAC/DhI,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;YAC9D,OAAOgI,YAAY;UACrB;QACF,CAAC,CAAC,OAAO1D,WAAW,EAAE;UACpBvE,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEoE,WAAW,CAAC;QAC3D;MACF;MAEA,MAAMpE,KAAK;IACb;EACF,CAAC;EAED;EACAwG,0BAA0B,EAAE,MAAAA,CAAO9G,UAAU,EAAE8E,MAAM,EAAEwD,QAAQ,KAAK;IAClE,IAAI;MACF;MACA,MAAM9H,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,mCAAmCI,aAAa,IAAIsE,MAAM,2BAA2B,CAAC;MAClG3E,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkI,QAAQ,CAAC;MAEnC,MAAM3H,QAAQ,GAAG,MAAMf,aAAa,CAACkE,IAAI,CAAC,SAAStD,aAAa,IAAIsE,MAAM,2BAA2B,EAAE;QACrGkC,SAAS,EAAEsB;MACb,CAAC,EAAE;QACDtH,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;;MAE5D;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,IAC5C,CAAC1D,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACkC,IAAI,KAAK,cAAc,IAC/ClC,KAAK,CAACgC,OAAO,IAAIhC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAE,EAAE;QAE9DnD,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;QAElF,IAAI;UACF;UACA,MAAM,IAAI6D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEvD;UACA,MAAMkE,YAAY,GAAG,MAAMtI,WAAW,CAAC+E,WAAW,CAAC7E,UAAU,EAAE8E,MAAM,CAAC;UACtE,IAAIsD,YAAY,EAAE;YAChBjI,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;YAC9D,OAAOgI,YAAY;UACrB;QACF,CAAC,CAAC,OAAO1D,WAAW,EAAE;UACpBvE,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEoE,WAAW,CAAC;QAC3D;MACF;MAEA,MAAMpE,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAG;QAAEuC,MAAM,EAAElD,KAAK,CAACgC,OAAO;QAAET,MAAM,EAAE;MAAI,CAAC;IACrF;EACF,CAAC;EAED;EACA0G,eAAe,EAAE,MAAAA,CAAOvI,UAAU,EAAE8E,MAAM,KAAK;IAC7C,IAAI;MACF;MACA,MAAMtE,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,mCAAmCI,aAAa,IAAIsE,MAAM,mBAAmB,CAAC;;MAE1F;MACA,MAAMnE,QAAQ,GAAG,MAAMf,aAAa,CAACkE,IAAI,CAAC,SAAStD,aAAa,IAAIsE,MAAM,mBAAmB,EAAE,CAAC,CAAC,EAAE;QACjG9D,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;;MAE/C;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,IAC5C,CAAC1D,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACkC,IAAI,KAAK,cAAc,IAC/ClC,KAAK,CAACgC,OAAO,IAAIhC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAE,EAAE;QAE9DnD,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;QAElF,IAAI;UACF;UACA,MAAM,IAAI6D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEvD;UACA,MAAMkE,YAAY,GAAG,MAAMtI,WAAW,CAAC+E,WAAW,CAAC7E,UAAU,EAAE8E,MAAM,CAAC;UACtE,IAAIsD,YAAY,IAAIA,YAAY,CAACpC,sBAAsB,KAAK,CAAC,EAAE;YAC7D7F,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;YAC9D,OAAOgI,YAAY;UACrB;QACF,CAAC,CAAC,OAAO1D,WAAW,EAAE;UACpBvE,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEoE,WAAW,CAAC;QAC3D;MACF;MAEA,MAAMpE,KAAK;IACb;EACF,CAAC;EAED;EACAkI,iBAAiB,EAAE,MAAOxI,UAAU,IAAK;IACvC,IAAI;MACF;MACA,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,4CAA4CI,aAAa,KAAK,CAAC;MAE3E,IAAI;QACF;QACAL,OAAO,CAACC,GAAG,CAAC,YAAYR,aAAa,CAACmF,QAAQ,CAACC,OAAO,SAASxE,aAAa,aAAa,CAAC;QAC1F,MAAMG,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,aAAa,EAAE;UAC5EQ,OAAO,EAAE,KAAK,CAAC;QACjB,CAAC,CAAC;QAEFb,OAAO,CAACC,GAAG,CAAC,+BAA+BO,QAAQ,CAACM,IAAI,GAAGN,QAAQ,CAACM,IAAI,CAACU,MAAM,GAAG,CAAC,0BAA0B,CAAC;QAC9G,IAAIhB,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;UAC7CxB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEO,QAAQ,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC,MAAM;UACLd,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC/C;QAEA,OAAOO,QAAQ,CAACM,IAAI;MACtB,CAAC,CAAC,OAAOwH,YAAY,EAAE;QACrBtI,OAAO,CAACG,KAAK,CAAC,uDAAuD,EAAEmI,YAAY,CAAC;;QAEpF;QACAtI,OAAO,CAACC,GAAG,CAAC,0BAA0BP,OAAO,SAASW,aAAa,aAAa,CAAC;QACjF,MAAM4D,KAAK,GAAGtD,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAE3C,MAAM2H,mBAAmB,GAAG,MAAMhJ,KAAK,CAACkB,GAAG,CACzC,GAAGf,OAAO,SAASW,aAAa,aAAa,EAC7C;UACEK,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUuD,KAAK;UAClC,CAAC;UACDpD,OAAO,EAAE;QACX,CACF,CAAC;QAEDb,OAAO,CAACC,GAAG,CAAC,yBAAyBsI,mBAAmB,CAACzH,IAAI,GAAGyH,mBAAmB,CAACzH,IAAI,CAACU,MAAM,GAAG,CAAC,0BAA0B,CAAC;QAC9H,OAAO+G,mBAAmB,CAACzH,IAAI;MACjC;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA,IAAAqI,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;MACd1I,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDH,OAAO,CAACG,KAAK,CAAC,kBAAkB,EAAE;QAChCgC,OAAO,EAAEhC,KAAK,CAACgC,OAAO;QACtBT,MAAM,GAAA8G,iBAAA,GAAErI,KAAK,CAACK,QAAQ,cAAAgI,iBAAA,uBAAdA,iBAAA,CAAgB9G,MAAM;QAC9BU,UAAU,GAAAqG,iBAAA,GAAEtI,KAAK,CAACK,QAAQ,cAAAiI,iBAAA,uBAAdA,iBAAA,CAAgBrG,UAAU;QACtCtB,IAAI,GAAA4H,iBAAA,GAAEvI,KAAK,CAACK,QAAQ,cAAAkI,iBAAA,uBAAdA,iBAAA,CAAgB5H,IAAI;QAC1BE,GAAG,EAAE,SAASnB,UAAU;MAC1B,CAAC,CAAC;;MAEF;MACA,IAAIM,KAAK,CAACkC,IAAI,KAAK,cAAc,IAAIlC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,SAAS,CAAC,IAAIhD,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAC,EAAE;QACjHnD,OAAO,CAACG,KAAK,CAAC,iCAAiC,CAAC;QAChD;QACAH,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,OAAO,EAAE;MACX;MAEA,MAAME,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACAwI,YAAY,EAAE,MAAAA,CAAO9I,UAAU,EAAE+I,SAAS,GAAG,IAAI,KAAK;IACpD,IAAI;MACF;MACA,MAAMvI,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA,IAAImB,GAAG,GAAG,SAASX,aAAa,QAAQ;MACxC,IAAIuI,SAAS,EAAE;QACb5H,GAAG,IAAI,cAAcI,kBAAkB,CAACwH,SAAS,CAAC,EAAE;MACtD;MAEA,MAAMpI,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAACO,GAAG,CAAC;MAC7C,OAAOR,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACAqF,oBAAoB,EAAE,MAAO3F,UAAU,IAAK;IAC1C,IAAI;MACF,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMW,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,qBAAqB,CAAC;MACrF,OAAOG,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACA0I,uBAAuB,EAAE,MAAOhJ,UAAU,IAAK;IAC7C,IAAI;MACF,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMW,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,YAAY,CAAC;MAC5E,OAAOG,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACA2I,YAAY,EAAE,MAAOjJ,UAAU,IAAK;IAClC,IAAI;MACF;MACA,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACAD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,GAAGP,OAAO,SAASW,aAAa,cAAc,CAAC;MAElF,IAAI;QACF,MAAMG,QAAQ,GAAG,MAAMjB,KAAK,CAACkB,GAAG,CAC9B,GAAGf,OAAO,SAASW,aAAa,EAAE,EAClC;UACEK,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE,KAAK;UACdoF,MAAM,EAAE;YAAE8C,SAAS,EAAE;UAAE;QACzB,CACF,CAAC;QAED/I,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEO,QAAQ,CAACM,IAAI,GAAGN,QAAQ,CAACM,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE,oBAAoB,CAAC;QAChH,IAAIhB,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;UAC7CxB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEO,QAAQ,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC;QACpD;QAEA,OAAON,QAAQ,CAACM,IAAI;MACtB,CAAC,CAAC,OAAOkI,aAAa,EAAE;QACtBhJ,OAAO,CAACG,KAAK,CAAC,gEAAgE,EAAE6I,aAAa,CAAC;;QAE9F;QACAhJ,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,GAAGP,OAAO,eAAeW,aAAa,EAAE,CAAC;QAE5E,MAAM4I,iBAAiB,GAAG,MAAM1J,KAAK,CAACkB,GAAG,CACvC,GAAGf,OAAO,eAAeW,aAAa,EAAE,EACxC;UACEK,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE;QACX,CACF,CAAC;QAEDb,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEgJ,iBAAiB,CAACnI,IAAI,GAAGmI,iBAAiB,CAACnI,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE,oBAAoB,CAAC;QAClI,IAAIyH,iBAAiB,CAACnI,IAAI,IAAImI,iBAAiB,CAACnI,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;UAC/DxB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgJ,iBAAiB,CAACnI,IAAI,CAAC,CAAC,CAAC,CAAC;QACxE;QAEA,OAAOmI,iBAAiB,CAACnI,IAAI;MAC/B;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACAkI,iBAAiB,EAAE,MAAOxI,UAAU,IAAK;IACvC,IAAI;MACF;MACA,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,4CAA4CI,aAAa,KAAK,CAAC;MAE3E,MAAMG,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,aAAa,EAAE;QAC5EQ,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEO,QAAQ,CAACM,IAAI,CAAC;MACvD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACA+I,WAAW,EAAE,MAAAA,CAAOrJ,UAAU,EAAE8E,MAAM,EAAEwE,IAAI,EAAEC,YAAY,KAAK;IAC7D,IAAI;MACF;MACA,MAAM/I,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,mCAAmCI,aAAa,IAAIsE,MAAM,eAAe,CAAC;MACtF3E,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;QAAEkJ,IAAI;QAAEC;MAAa,CAAC,CAAC;MAE5C,MAAM5I,QAAQ,GAAG,MAAMf,aAAa,CAACkE,IAAI,CAAC,SAAStD,aAAa,IAAIsE,MAAM,eAAe,EAAE;QACzFwE,IAAI,EAAEA,IAAI;QACVC,YAAY,EAAEA,YAAY,IAAI;MAChC,CAAC,EAAE;QACDvI,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;;MAE3C;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,EAAE;QAChD7D,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACqE,aAAa,IAAIrE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACqE,aAAa,IAAI,+EAA+E;UAAE9C,MAAM,EAAE,CAAC;UAAEkC,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,MAAMzD,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAG;QAAEuC,MAAM,EAAElD,KAAK,CAACgC,OAAO;QAAET,MAAM,EAAE;MAAI,CAAC;IACrF;EACF,CAAC;EAED;EACA2H,YAAY,EAAE,MAAAA,CAAOxJ,UAAU,EAAE8E,MAAM,EAAEwE,IAAI,KAAK;IAChD,IAAI;MACF;MACA,MAAM9I,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,qCAAqCI,aAAa,IAAIsE,MAAM,iBAAiBwE,IAAI,EAAE,CAAC;MAEhG,MAAM3I,QAAQ,GAAG,MAAMf,aAAa,CAACuG,MAAM,CAAC,SAAS3F,aAAa,IAAIsE,MAAM,iBAAiBwE,IAAI,EAAE,EAAE;QACnGtI,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;MAE5C;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,EAAE;QAChD7D,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACqE,aAAa,IAAIrE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACqE,aAAa,IAAI,+EAA+E;UAAE9C,MAAM,EAAE,CAAC;UAAEkC,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,MAAMzD,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAG;QAAEuC,MAAM,EAAElD,KAAK,CAACgC,OAAO;QAAET,MAAM,EAAE;MAAI,CAAC;IACrF;EACF,CAAC;EAED;EACA4H,kBAAkB,EAAE,MAAAA,CAAOzJ,UAAU,EAAE8E,MAAM,KAAK;IAChD,IAAI;MACF;MACA,MAAMtE,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,mCAAmCI,aAAa,IAAIsE,MAAM,sBAAsB,CAAC;MAE7F,IAAI;QACF;QACA,MAAMnE,QAAQ,GAAG,MAAMf,aAAa,CAACkE,IAAI,CAAC,SAAStD,aAAa,IAAIsE,MAAM,sBAAsB,EAAE,CAAC,CAAC,EAAE;UACpG9D,OAAO,EAAE,KAAK,CAAE;QAClB,CAAC,CAAC;QACF,OAAOL,QAAQ,CAACM,IAAI;MACtB,CAAC,CAAC,OAAOgF,SAAS,EAAE;QAClB9F,OAAO,CAACC,GAAG,CAAC,yDAAyD,EAAE6F,SAAS,CAAC;QAEjF,IAAI;UACF;UACA,MAAMyD,WAAW,GAAG,MAAM9J,aAAa,CAAC2F,GAAG,CAAC,SAAS/E,aAAa,IAAIsE,MAAM,sBAAsB,EAAE,CAAC,CAAC,EAAE;YACtG9D,OAAO,EAAE,KAAK,CAAE;UAClB,CAAC,CAAC;UACF,OAAO0I,WAAW,CAACzI,IAAI;QACzB,CAAC,CAAC,OAAO0I,QAAQ,EAAE;UACjBxJ,OAAO,CAACC,GAAG,CAAC,yEAAyE,EAAEuJ,QAAQ,CAAC;;UAEhG;UACA,MAAM1E,WAAW,GAAG,MAAMrF,aAAa,CAACkE,IAAI,CAAC,SAAStD,aAAa,IAAIsE,MAAM,wBAAwB,EAAE,CAAC,CAAC,EAAE;YACzG9D,OAAO,EAAE,KAAK,CAAE;UAClB,CAAC,CAAC;UACF,OAAOiE,WAAW,CAAChE,IAAI;QACzB;MACF;IAGF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;MAElD;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,IAC5C,CAAC1D,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACkC,IAAI,KAAK,cAAc,IAC/ClC,KAAK,CAACgC,OAAO,IAAIhC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAE,EAAE;QAC9DnD,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACqE,aAAa,IAAIrE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACqE,aAAa,IAAI,+EAA+E;UAAE9C,MAAM,EAAE,CAAC;UAAEkC,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,MAAMzD,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAG;QAAEuC,MAAM,EAAElD,KAAK,CAACgC,OAAO;QAAET,MAAM,EAAE;MAAI,CAAC;IACrF;EACF,CAAC;EAED;EACA+H,SAAS,EAAE,MAAAA,CAAO5J,UAAU,EAAE8E,MAAM,KAAK;IACvC,IAAI;MACF;MACA,MAAMtE,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,MAAMyJ,MAAM,GAAG,MAAMjK,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,cAAc,EAAE;QAC3EQ,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MACF,MAAM8I,UAAU,GAAGD,MAAM,CAAC5I,IAAI,CAAC8I,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzF,OAAO,KAAKO,MAAM,CAAC;;MAE9D;MACA3E,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,MAAM6J,KAAK,GAAG,MAAMrK,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,cAAc,EAAE;QAC1EQ,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MACF,MAAMkJ,SAAS,GAAGD,KAAK,CAAChJ,IAAI,CAAC8I,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzF,OAAO,KAAKO,MAAM,CAAC;MAE5D,OAAO;QACLqF,kBAAkB,EAAE,CAAC,CAACL,UAAU;QAChCM,iBAAiB,EAAE,CAAC,CAACF,SAAS;QAC9BG,WAAW,EAAEP,UAAU;QACvBQ,UAAU,EAAEJ;MACd,CAAC;IACH,CAAC,CAAC,OAAO5J,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;;MAEzC;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,EAAE;QAChD7D,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACqE,aAAa,IAAIrE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACqE,aAAa,IAAI,+EAA+E;UAAE9C,MAAM,EAAE,CAAC;UAAEkC,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,MAAMzD,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAG;QAAEuC,MAAM,EAAElD,KAAK,CAACgC,OAAO;QAAET,MAAM,EAAE;MAAI,CAAC;IACrF;EACF;AACF,CAAC;AAED,eAAe/B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}