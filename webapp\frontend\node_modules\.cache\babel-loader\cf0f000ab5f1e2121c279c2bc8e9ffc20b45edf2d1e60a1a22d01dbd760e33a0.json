{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\MainMenu.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { List, ListItem, ListItemIcon, ListItemText, Divider, Box, Typography, Collapse, ListItemButton } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon, ExpandLess, ExpandMore, ViewList as ViewListIcon, Engineering as EngineeringIcon, Inventory as InventoryIcon, TableChart as TableChartIcon, Assessment as AssessmentIcon, VerifiedUser as VerifiedUserIcon, ShoppingCart as ShoppingCartIcon, Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MainMenu = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n\n  // Stati per i menu a cascata\n  const [openCaviMenu, setOpenCaviMenu] = useState(false);\n  const [openCantieriMenu, setOpenCantieriMenu] = useState(false);\n  const [openAdminMenu, setOpenAdminMenu] = useState(false);\n  const [openPosaMenu, setOpenPosaMenu] = useState(false);\n  const [openParcoMenu, setOpenParcoMenu] = useState(false);\n  const [openExcelMenu, setOpenExcelMenu] = useState(false);\n  const [openReportMenu, setOpenReportMenu] = useState(false);\n  const [openCertificazioneMenu, setOpenCertificazioneMenu] = useState(false);\n  const [openComandeMenu, setOpenComandeMenu] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = path => {\n    return location.pathname.startsWith(path);\n  };\n\n  // Gestisce l'apertura/chiusura dei menu a cascata\n  const handleToggleCaviMenu = () => {\n    setOpenCaviMenu(!openCaviMenu);\n  };\n  const handleToggleCantieriMenu = () => {\n    setOpenCantieriMenu(!openCantieriMenu);\n  };\n  const handleToggleAdminMenu = () => {\n    setOpenAdminMenu(!openAdminMenu);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      console.log('Utente impersonato, reindirizzamento al menu amministratore');\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(List, {\n    dense: true,\n    children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n      selected: isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard'),\n      onClick: () => navigateTo('/dashboard'),\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: isImpersonating ? \"Torna al Menu Admin\" : \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), (user === null || user === void 0 ? void 0 : user.role) === 'owner' && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: handleToggleAdminMenu,\n        selected: isPartOfActive('/dashboard/admin'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(AdminIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: \"Amministrazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 13\n        }, this), openAdminMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 30\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 47\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n        in: openAdminMenu,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            selected: isActive('/dashboard/admin'),\n            onClick: () => navigateTo('/dashboard/admin'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AdminIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Pannello Admin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), ((user === null || user === void 0 ? void 0 : user.role) !== 'owner' || (user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this), isImpersonating && impersonatedUser && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          bgcolor: 'rgba(255, 165, 0, 0.1)',\n          borderLeft: '4px solid orange'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          color: \"textSecondary\",\n          children: \"Accesso come utente:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"bold\",\n          children: impersonatedUser.username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: handleToggleCantieriMenu,\n        selected: isPartOfActive('/dashboard/cantieri'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(ConstructionIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this), openCantieriMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 33\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 50\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n        in: openCantieriMenu,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            selected: isActive('/dashboard/cantieri'),\n            onClick: () => navigateTo('/dashboard/cantieri'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(ViewListIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Lista Cantieri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n              sx: {\n                pl: 2\n              },\n              selected: isActive(`/dashboard/cantieri/${selectedCantiereId}`),\n              onClick: () => navigateTo(`/dashboard/cantieri/${selectedCantiereId}`),\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                children: /*#__PURE__*/_jsxDEV(ConstructionIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Cantiere: ${selectedCantiereName || selectedCantiereId}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 19\n            }, this)\n          }, void 0, false)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 11\n      }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: handleToggleCaviMenu,\n        selected: isPartOfActive('/dashboard/cavi'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: `Gestione Cavi (${selectedCantiereName || selectedCantiereId})`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 15\n        }, this), openCaviMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 48\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 13\n      }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(Collapse, {\n        in: openCaviMenu,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            selected: isActive('/dashboard/cavi/visualizza'),\n            onClick: () => navigateTo('/dashboard/cavi/visualizza'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(ViewListIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Visualizza Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            onClick: () => setOpenPosaMenu(!openPosaMenu),\n            selected: isPartOfActive('/dashboard/cavi/posa'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EngineeringIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Posa e Collegamenti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 19\n            }, this), openPosaMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 35\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 52\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openPosaMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/posa/inserisci-metri'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/inserisci-metri'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(CableIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Inserisci metri posati\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/posa/modifica-cavo'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/modifica-cavo'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Modifica cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/posa/aggiungi-cavo'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/aggiungi-cavo'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Aggiungi nuovo cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/posa/elimina-cavo'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/elimina-cavo'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Elimina cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/posa/modifica-bobina'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/modifica-bobina'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Modifica bobina cavo posato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/posa/collegamenti'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/collegamenti'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(CableIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Gestisci collegamenti cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => setOpenParcoMenu(!openParcoMenu),\n            selected: isPartOfActive('/dashboard/cavi/parco'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Parco Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 19\n            }, this), openParcoMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 53\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openParcoMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/parco/visualizza'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/visualizza'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(ViewListIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Visualizza Bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/parco/crea'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/crea'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Crea Nuova Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/parco/modifica'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/modifica'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 348,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Modifica Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/parco/elimina'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/elimina'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Elimina Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/parco/storico'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/storico'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Storico Utilizzo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => setOpenExcelMenu(!openExcelMenu),\n            selected: isPartOfActive('/dashboard/cavi/excel'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(TableChartIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Gestione Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 19\n            }, this), openExcelMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 53\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openExcelMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/importa-cavi'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/importa-cavi'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Importa cavi da Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/importa-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/importa-bobine'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Importa parco bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/template-cavi'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/template-cavi'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(TableChartIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Template Excel per cavi\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/template-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/template-bobine'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(TableChartIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Template Excel per bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/esporta-cavi'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/esporta-cavi'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(TableChartIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Esporta cavi in Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/esporta-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/esporta-bobine'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(TableChartIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Esporta bobine in Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => setOpenReportMenu(!openReportMenu),\n            selected: isPartOfActive('/dashboard/cavi/report'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Report\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 19\n            }, this), openReportMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 37\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 54\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openReportMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/report/avanzamento'),\n                onClick: () => navigateTo('/dashboard/cavi/report/avanzamento'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Report Avanzamento\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/report/boq'),\n                onClick: () => navigateTo('/dashboard/cavi/report/boq'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 492,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Bill of Quantities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/report/utilizzo-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/report/utilizzo-bobine'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Report Utilizzo Bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/report/posa-periodo'),\n                onClick: () => navigateTo('/dashboard/cavi/report/posa-periodo'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Report Posa per Periodo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => setOpenCertificazioneMenu(!openCertificazioneMenu),\n            selected: isPartOfActive('/dashboard/cavi/certificazione'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(VerifiedUserIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Certificazione Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 19\n            }, this), openCertificazioneMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 45\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 62\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openCertificazioneMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/visualizza'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/visualizza'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(ViewListIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Visualizza certificazioni\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/filtra'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/filtra'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(ViewListIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Filtra per cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/crea'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/crea'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Crea certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/dettagli'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/dettagli'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(ViewListIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Dettagli certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/pdf'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/pdf'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Genera PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/elimina'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/elimina'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Elimina certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/strumenti'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/strumenti'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Gestione strumenti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => setOpenComandeMenu(!openComandeMenu),\n            selected: isPartOfActive('/dashboard/cavi/comande'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(ShoppingCartIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Gestione Comande\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 19\n            }, this), openComandeMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 55\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openComandeMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/visualizza'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/visualizza'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(ViewListIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 636,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Visualizza comande\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/crea'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/crea'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Crea nuova comanda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/modifica'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/modifica'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 658,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Modifica comanda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 660,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/elimina'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/elimina'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 669,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Elimina comanda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/stampa'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/stampa'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 680,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Stampa comanda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/assegna'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/assegna'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 691,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Assegna comanda a cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s(MainMenu, \"Udz8U7QCjEP5k56hCgUxMe5hqis=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = MainMenu;\nexport default MainMenu;\nvar _c;\n$RefreshReg$(_c, \"MainMenu\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "Box", "Typography", "Collapse", "ListItemButton", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "ExpandLess", "ExpandMore", "ViewList", "ViewListIcon", "Engineering", "EngineeringIcon", "Inventory", "InventoryIcon", "Table<PERSON>hart", "TableChartIcon", "Assessment", "AssessmentIcon", "VerifiedUser", "VerifiedUserIcon", "ShoppingCart", "ShoppingCartIcon", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MainMenu", "_s", "navigate", "location", "user", "isImpersonating", "impersonated<PERSON><PERSON>", "openCaviMenu", "setOpenCaviMenu", "openCantieriMenu", "setOpenCantieriMenu", "openAdminMenu", "setOpenAdminMenu", "openPosaMenu", "setOpenPosaMenu", "openParcoMenu", "setOpenParcoMenu", "openExcelMenu", "setOpenExcelMenu", "openReportMenu", "setOpenReportMenu", "openCertificazioneMenu", "setOpenCertificazioneMenu", "openComandeMenu", "setOpenComandeMenu", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "isActive", "path", "pathname", "isPartOfActive", "startsWith", "handleToggleCaviMenu", "handleToggleCantieriMenu", "handleToggleAdminMenu", "navigateTo", "console", "log", "dense", "children", "selected", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "primary", "role", "in", "timeout", "unmountOnExit", "component", "disablePadding", "sx", "pl", "p", "bgcolor", "borderLeft", "variant", "color", "fontWeight", "username", "fontSize", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/MainMenu.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Box,\n  Typography,\n  Collapse,\n  ListItemButton\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon,\n  ExpandLess,\n  ExpandMore,\n  ViewList as ViewListIcon,\n  Engineering as EngineeringIcon,\n  Inventory as InventoryIcon,\n  TableChart as TableChartIcon,\n  Assessment as AssessmentIcon,\n  VerifiedUser as VerifiedUserIcon,\n  ShoppingCart as ShoppingCartIcon,\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\n\nconst MainMenu = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, isImpersonating, impersonatedUser } = useAuth();\n\n  // Stati per i menu a cascata\n  const [openCaviMenu, setOpenCaviMenu] = useState(false);\n  const [openCantieriMenu, setOpenCantieriMenu] = useState(false);\n  const [openAdminMenu, setOpenAdminMenu] = useState(false);\n  const [openPosaMenu, setOpenPosaMenu] = useState(false);\n  const [openParcoMenu, setOpenParcoMenu] = useState(false);\n  const [openExcelMenu, setOpenExcelMenu] = useState(false);\n  const [openReportMenu, setOpenReportMenu] = useState(false);\n  const [openCertificazioneMenu, setOpenCertificazioneMenu] = useState(false);\n  const [openComandeMenu, setOpenComandeMenu] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  // Gestisce l'apertura/chiusura dei menu a cascata\n  const handleToggleCaviMenu = () => {\n    setOpenCaviMenu(!openCaviMenu);\n  };\n\n  const handleToggleCantieriMenu = () => {\n    setOpenCantieriMenu(!openCantieriMenu);\n  };\n\n  const handleToggleAdminMenu = () => {\n    setOpenAdminMenu(!openAdminMenu);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      console.log('Utente impersonato, reindirizzamento al menu amministratore');\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n  };\n\n  return (\n    <List dense>\n      {/* Home - Se l'utente è un amministratore che sta impersonando un utente, mostra \"Torna al Menu Admin\" */}\n      <ListItemButton\n        selected={isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard')}\n        onClick={() => navigateTo('/dashboard')}\n      >\n        <ListItemIcon>\n          <HomeIcon />\n        </ListItemIcon>\n        <ListItemText primary={isImpersonating ? \"Torna al Menu Admin\" : \"Home\"} />\n      </ListItemButton>\n\n      {/* Menu Amministratore (solo per admin) */}\n      {user?.role === 'owner' && (\n        <>\n          <Divider />\n          <ListItemButton\n            onClick={handleToggleAdminMenu}\n            selected={isPartOfActive('/dashboard/admin')}\n          >\n            <ListItemIcon>\n              <AdminIcon />\n            </ListItemIcon>\n            <ListItemText primary=\"Amministrazione\" />\n            {openAdminMenu ? <ExpandLess /> : <ExpandMore />}\n          </ListItemButton>\n          <Collapse in={openAdminMenu} timeout=\"auto\" unmountOnExit>\n            <List component=\"div\" disablePadding>\n              <ListItemButton\n                sx={{ pl: 2 }}\n                selected={isActive('/dashboard/admin')}\n                onClick={() => navigateTo('/dashboard/admin')}\n              >\n                <ListItemIcon>\n                  <AdminIcon />\n                </ListItemIcon>\n                <ListItemText primary=\"Pannello Admin\" />\n              </ListItemButton>\n              {/* Altri sottomenu admin possono essere aggiunti qui */}\n            </List>\n          </Collapse>\n        </>\n      )}\n\n      {/* Menu per utenti standard e cantieri */}\n      {/* Mostra per utenti standard/cantiere o per admin che sta impersonando un utente */}\n      {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (\n        <>\n          <Divider />\n          {isImpersonating && impersonatedUser && (\n            <Box sx={{ p: 2, bgcolor: 'rgba(255, 165, 0, 0.1)', borderLeft: '4px solid orange' }}>\n              <Typography variant=\"subtitle2\" color=\"textSecondary\">\n                Accesso come utente:\n              </Typography>\n              <Typography variant=\"body2\" fontWeight=\"bold\">\n                {impersonatedUser.username}\n              </Typography>\n            </Box>\n          )}\n\n          {/* Menu Cantieri con sottomenu */}\n          <ListItemButton\n            onClick={handleToggleCantieriMenu}\n            selected={isPartOfActive('/dashboard/cantieri')}\n          >\n            <ListItemIcon>\n              <ConstructionIcon />\n            </ListItemIcon>\n            <ListItemText primary={isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"} />\n            {openCantieriMenu ? <ExpandLess /> : <ExpandMore />}\n          </ListItemButton>\n          <Collapse in={openCantieriMenu} timeout=\"auto\" unmountOnExit>\n            <List component=\"div\" disablePadding>\n              <ListItemButton\n                sx={{ pl: 4 }}\n                selected={isActive('/dashboard/cantieri')}\n                onClick={() => navigateTo('/dashboard/cantieri')}\n              >\n                <ListItemIcon>\n                  <ViewListIcon />\n                </ListItemIcon>\n                <ListItemText primary=\"Lista Cantieri\" />\n              </ListItemButton>\n\n              {/* Mostra il cantiere selezionato se presente */}\n              {selectedCantiereId && (\n                <>\n                  <ListItemButton\n                    sx={{ pl: 2 }}\n                    selected={isActive(`/dashboard/cantieri/${selectedCantiereId}`)}\n                    onClick={() => navigateTo(`/dashboard/cantieri/${selectedCantiereId}`)}\n                  >\n                    <ListItemIcon>\n                      <ConstructionIcon />\n                    </ListItemIcon>\n                    <ListItemText primary={`Cantiere: ${selectedCantiereName || selectedCantiereId}`} />\n                  </ListItemButton>\n                </>\n              )}\n            </List>\n          </Collapse>\n\n          {/* Menu Cavi con sottomenu - visibile solo se un cantiere è selezionato */}\n          {selectedCantiereId && (\n            <ListItemButton\n              onClick={handleToggleCaviMenu}\n              selected={isPartOfActive('/dashboard/cavi')}\n            >\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary={`Gestione Cavi (${selectedCantiereName || selectedCantiereId})`} />\n              {openCaviMenu ? <ExpandLess /> : <ExpandMore />}\n            </ListItemButton>\n          )}\n\n          {selectedCantiereId && (\n            <Collapse in={openCaviMenu} timeout=\"auto\" unmountOnExit>\n              <List component=\"div\" disablePadding>\n                <ListItemButton\n                  sx={{ pl: 4 }}\n                  selected={isActive('/dashboard/cavi/visualizza')}\n                  onClick={() => navigateTo('/dashboard/cavi/visualizza')}\n                >\n                  <ListItemIcon>\n                    <ViewListIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Visualizza Cavi\" />\n                </ListItemButton>\n\n                {/* Posa e Collegamenti con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 2 }}\n                  onClick={() => setOpenPosaMenu(!openPosaMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/posa')}\n                >\n                  <ListItemIcon>\n                    <EngineeringIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Posa e Collegamenti\" />\n                  {openPosaMenu ? <ExpandLess /> : <ExpandMore />}\n                </ListItemButton>\n\n                <Collapse in={openPosaMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/posa/inserisci-metri')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/inserisci-metri')}\n                    >\n                      <ListItemIcon>\n                        <CableIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Inserisci metri posati\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/posa/modifica-cavo')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/modifica-cavo')}\n                    >\n                      <ListItemIcon>\n                        <EditIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Modifica cavo\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/posa/aggiungi-cavo')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/aggiungi-cavo')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Aggiungi nuovo cavo\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/posa/elimina-cavo')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/elimina-cavo')}\n                    >\n                      <ListItemIcon>\n                        <DeleteIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Elimina cavo\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/posa/modifica-bobina')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/modifica-bobina')}\n                    >\n                      <ListItemIcon>\n                        <EditIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Modifica bobina cavo posato\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/posa/collegamenti')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/collegamenti')}\n                    >\n                      <ListItemIcon>\n                        <CableIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Gestisci collegamenti cavo\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Parco Cavi con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 4 }}\n                  onClick={() => setOpenParcoMenu(!openParcoMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/parco')}\n                >\n                  <ListItemIcon>\n                    <InventoryIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Parco Cavi\" />\n                  {openParcoMenu ? <ExpandLess /> : <ExpandMore />}\n                </ListItemButton>\n\n                <Collapse in={openParcoMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/parco/visualizza')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/visualizza')}\n                    >\n                      <ListItemIcon>\n                        <ViewListIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Visualizza Bobine\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/parco/crea')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/crea')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Crea Nuova Bobina\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/parco/modifica')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/modifica')}\n                    >\n                      <ListItemIcon>\n                        <EditIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Modifica Bobina\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/parco/elimina')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/elimina')}\n                    >\n                      <ListItemIcon>\n                        <DeleteIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Elimina Bobina\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/parco/storico')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/storico')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Storico Utilizzo\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Gestione Excel con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 4 }}\n                  onClick={() => setOpenExcelMenu(!openExcelMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/excel')}\n                >\n                  <ListItemIcon>\n                    <TableChartIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Gestione Excel\" />\n                  {openExcelMenu ? <ExpandLess /> : <ExpandMore />}\n                </ListItemButton>\n\n                <Collapse in={openExcelMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/importa-cavi')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/importa-cavi')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Importa cavi da Excel\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/importa-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/importa-bobine')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Importa parco bobine\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/template-cavi')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/template-cavi')}\n                    >\n                      <ListItemIcon>\n                        <TableChartIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Template Excel per cavi\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/template-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/template-bobine')}\n                    >\n                      <ListItemIcon>\n                        <TableChartIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Template Excel per bobine\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/esporta-cavi')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/esporta-cavi')}\n                    >\n                      <ListItemIcon>\n                        <TableChartIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Esporta cavi in Excel\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/esporta-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/esporta-bobine')}\n                    >\n                      <ListItemIcon>\n                        <TableChartIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Esporta bobine in Excel\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Report con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 4 }}\n                  onClick={() => setOpenReportMenu(!openReportMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/report')}\n                >\n                  <ListItemIcon>\n                    <AssessmentIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Report\" />\n                  {openReportMenu ? <ExpandLess /> : <ExpandMore />}\n                </ListItemButton>\n\n                <Collapse in={openReportMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/report/avanzamento')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/avanzamento')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Report Avanzamento\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/report/boq')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/boq')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Bill of Quantities\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/report/utilizzo-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/utilizzo-bobine')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Report Utilizzo Bobine\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/report/posa-periodo')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/posa-periodo')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Report Posa per Periodo\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Certificazione Cavi con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 4 }}\n                  onClick={() => setOpenCertificazioneMenu(!openCertificazioneMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/certificazione')}\n                >\n                  <ListItemIcon>\n                    <VerifiedUserIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Certificazione Cavi\" />\n                  {openCertificazioneMenu ? <ExpandLess /> : <ExpandMore />}\n                </ListItemButton>\n\n                <Collapse in={openCertificazioneMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/visualizza')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/visualizza')}\n                    >\n                      <ListItemIcon>\n                        <ViewListIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Visualizza certificazioni\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/filtra')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/filtra')}\n                    >\n                      <ListItemIcon>\n                        <ViewListIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Filtra per cavo\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/crea')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/crea')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Crea certificazione\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/dettagli')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/dettagli')}\n                    >\n                      <ListItemIcon>\n                        <ViewListIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Dettagli certificazione\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/pdf')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/pdf')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Genera PDF\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/elimina')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/elimina')}\n                    >\n                      <ListItemIcon>\n                        <DeleteIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Elimina certificazione\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/strumenti')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/strumenti')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Gestione strumenti\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Gestione Comande con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 4 }}\n                  onClick={() => setOpenComandeMenu(!openComandeMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/comande')}\n                >\n                  <ListItemIcon>\n                    <ShoppingCartIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Gestione Comande\" />\n                  {openComandeMenu ? <ExpandLess /> : <ExpandMore />}\n                </ListItemButton>\n\n                <Collapse in={openComandeMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/visualizza')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/visualizza')}\n                    >\n                      <ListItemIcon>\n                        <ViewListIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Visualizza comande\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/crea')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/crea')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Crea nuova comanda\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/modifica')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/modifica')}\n                    >\n                      <ListItemIcon>\n                        <EditIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Modifica comanda\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/elimina')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/elimina')}\n                    >\n                      <ListItemIcon>\n                        <DeleteIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Elimina comanda\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/stampa')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/stampa')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Stampa comanda\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/assegna')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/assegna')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Assegna comanda a cavo\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n              </List>\n            </Collapse>\n          )}\n        </>\n      )}\n    </List>\n  );\n};\n\nexport default MainMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,UAAU,EACVC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,UAAU,EACVC,UAAU,EACVC,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,eAAe,EAC9BC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,YAAY,IAAIC,gBAAgB,EAChCC,YAAY,IAAIC,gBAAgB,EAChCC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAC9B,MAAMmD,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmD,IAAI;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGX,OAAO,CAAC,CAAC;;EAE7D;EACA,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoE,cAAc,EAAEC,iBAAiB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACwE,eAAe,EAAEC,kBAAkB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM0E,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEzE;EACA,MAAME,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAO3B,QAAQ,CAAC4B,QAAQ,KAAKD,IAAI;EACnC,CAAC;;EAED;EACA,MAAME,cAAc,GAAIF,IAAI,IAAK;IAC/B,OAAO3B,QAAQ,CAAC4B,QAAQ,CAACE,UAAU,CAACH,IAAI,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjC1B,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,MAAM4B,wBAAwB,GAAGA,CAAA,KAAM;IACrCzB,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,MAAM2B,qBAAqB,GAAGA,CAAA,KAAM;IAClCxB,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAM0B,UAAU,GAAIP,IAAI,IAAK;IAC3BQ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAET,IAAI,EAAE,kBAAkB,EAAEzB,eAAe,EAAE,OAAO,EAAED,IAAI,CAAC;IACvF;IACA;IACA,IAAI0B,IAAI,KAAK,YAAY,IAAIzB,eAAe,EAAE;MAC5CiC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ErC,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM;MACLA,QAAQ,CAAC4B,IAAI,CAAC;IAChB;EACF,CAAC;EAED,oBACEjC,OAAA,CAAC3C,IAAI;IAACsF,KAAK;IAAAC,QAAA,gBAET5C,OAAA,CAACnC,cAAc;MACbgF,QAAQ,EAAErC,eAAe,GAAGwB,QAAQ,CAAC,kBAAkB,CAAC,GAAGA,QAAQ,CAAC,YAAY,CAAE;MAClFc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,YAAY,CAAE;MAAAI,QAAA,gBAExC5C,OAAA,CAACzC,YAAY;QAAAqF,QAAA,eACX5C,OAAA,CAACjC,QAAQ;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACflD,OAAA,CAACxC,YAAY;QAAC2F,OAAO,EAAE3C,eAAe,GAAG,qBAAqB,GAAG;MAAO;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,EAGhB,CAAA3C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,IAAI,MAAK,OAAO,iBACrBpD,OAAA,CAAAE,SAAA;MAAA0C,QAAA,gBACE5C,OAAA,CAACvC,OAAO;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXlD,OAAA,CAACnC,cAAc;QACbiF,OAAO,EAAEP,qBAAsB;QAC/BM,QAAQ,EAAEV,cAAc,CAAC,kBAAkB,CAAE;QAAAS,QAAA,gBAE7C5C,OAAA,CAACzC,YAAY;UAAAqF,QAAA,eACX5C,OAAA,CAAC/B,SAAS;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACflD,OAAA,CAACxC,YAAY;UAAC2F,OAAO,EAAC;QAAiB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACzCpC,aAAa,gBAAGd,OAAA,CAACxB,UAAU;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACvB,UAAU;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACjBlD,OAAA,CAACpC,QAAQ;QAACyF,EAAE,EAAEvC,aAAc;QAACwC,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAX,QAAA,eACvD5C,OAAA,CAAC3C,IAAI;UAACmG,SAAS,EAAC,KAAK;UAACC,cAAc;UAAAb,QAAA,eAClC5C,OAAA,CAACnC,cAAc;YACb6F,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdd,QAAQ,EAAEb,QAAQ,CAAC,kBAAkB,CAAE;YACvCc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,kBAAkB,CAAE;YAAAI,QAAA,gBAE9C5C,OAAA,CAACzC,YAAY;cAAAqF,QAAA,eACX5C,OAAA,CAAC/B,SAAS;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACflD,OAAA,CAACxC,YAAY;cAAC2F,OAAO,EAAC;YAAgB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA,eACX,CACH,EAIA,CAAC,CAAA3C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,IAAI,MAAK,OAAO,IAAK,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,IAAI,MAAK,OAAO,IAAI5C,eAAe,IAAIC,gBAAiB,kBACzFT,OAAA,CAAAE,SAAA;MAAA0C,QAAA,gBACE5C,OAAA,CAACvC,OAAO;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACV1C,eAAe,IAAIC,gBAAgB,iBAClCT,OAAA,CAACtC,GAAG;QAACgG,EAAE,EAAE;UAAEE,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE,wBAAwB;UAAEC,UAAU,EAAE;QAAmB,CAAE;QAAAlB,QAAA,gBACnF5C,OAAA,CAACrC,UAAU;UAACoG,OAAO,EAAC,WAAW;UAACC,KAAK,EAAC,eAAe;UAAApB,QAAA,EAAC;QAEtD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblD,OAAA,CAACrC,UAAU;UAACoG,OAAO,EAAC,OAAO;UAACE,UAAU,EAAC,MAAM;UAAArB,QAAA,EAC1CnC,gBAAgB,CAACyD;QAAQ;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN,eAGDlD,OAAA,CAACnC,cAAc;QACbiF,OAAO,EAAER,wBAAyB;QAClCO,QAAQ,EAAEV,cAAc,CAAC,qBAAqB,CAAE;QAAAS,QAAA,gBAEhD5C,OAAA,CAACzC,YAAY;UAAAqF,QAAA,eACX5C,OAAA,CAAC7B,gBAAgB;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACflD,OAAA,CAACxC,YAAY;UAAC2F,OAAO,EAAE3C,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAACyD,QAAQ,EAAE,GAAG;QAAkB;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC9HtC,gBAAgB,gBAAGZ,OAAA,CAACxB,UAAU;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACvB,UAAU;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACjBlD,OAAA,CAACpC,QAAQ;QAACyF,EAAE,EAAEzC,gBAAiB;QAAC0C,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAX,QAAA,eAC1D5C,OAAA,CAAC3C,IAAI;UAACmG,SAAS,EAAC,KAAK;UAACC,cAAc;UAAAb,QAAA,gBAClC5C,OAAA,CAACnC,cAAc;YACb6F,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdd,QAAQ,EAAEb,QAAQ,CAAC,qBAAqB,CAAE;YAC1Cc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,qBAAqB,CAAE;YAAAI,QAAA,gBAEjD5C,OAAA,CAACzC,YAAY;cAAAqF,QAAA,eACX5C,OAAA,CAACrB,YAAY;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACflD,OAAA,CAACxC,YAAY;cAAC2F,OAAO,EAAC;YAAgB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,EAGhBtB,kBAAkB,iBACjB5B,OAAA,CAAAE,SAAA;YAAA0C,QAAA,eACE5C,OAAA,CAACnC,cAAc;cACb6F,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cACdd,QAAQ,EAAEb,QAAQ,CAAC,uBAAuBJ,kBAAkB,EAAE,CAAE;cAChEkB,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,uBAAuBZ,kBAAkB,EAAE,CAAE;cAAAgB,QAAA,gBAEvE5C,OAAA,CAACzC,YAAY;gBAAAqF,QAAA,eACX5C,OAAA,CAAC7B,gBAAgB;kBAAA4E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACflD,OAAA,CAACxC,YAAY;gBAAC2F,OAAO,EAAE,aAAapB,oBAAoB,IAAIH,kBAAkB;cAAG;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC,gBACjB,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGVtB,kBAAkB,iBACjB5B,OAAA,CAACnC,cAAc;QACbiF,OAAO,EAAET,oBAAqB;QAC9BQ,QAAQ,EAAEV,cAAc,CAAC,iBAAiB,CAAE;QAAAS,QAAA,gBAE5C5C,OAAA,CAACzC,YAAY;UAAAqF,QAAA,eACX5C,OAAA,CAAC3B,SAAS;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACflD,OAAA,CAACxC,YAAY;UAAC2F,OAAO,EAAE,kBAAkBpB,oBAAoB,IAAIH,kBAAkB;QAAI;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACzFxC,YAAY,gBAAGV,OAAA,CAACxB,UAAU;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACvB,UAAU;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CACjB,EAEAtB,kBAAkB,iBACjB5B,OAAA,CAACpC,QAAQ;QAACyF,EAAE,EAAE3C,YAAa;QAAC4C,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAX,QAAA,eACtD5C,OAAA,CAAC3C,IAAI;UAACmG,SAAS,EAAC,KAAK;UAACC,cAAc;UAAAb,QAAA,gBAClC5C,OAAA,CAACnC,cAAc;YACb6F,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdd,QAAQ,EAAEb,QAAQ,CAAC,4BAA4B,CAAE;YACjDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,4BAA4B,CAAE;YAAAI,QAAA,gBAExD5C,OAAA,CAACzC,YAAY;cAAAqF,QAAA,eACX5C,OAAA,CAACrB,YAAY;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACflD,OAAA,CAACxC,YAAY;cAAC2F,OAAO,EAAC;YAAiB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eAGjBlD,OAAA,CAACnC,cAAc;YACb6F,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdb,OAAO,EAAEA,CAAA,KAAM7B,eAAe,CAAC,CAACD,YAAY,CAAE;YAC9C6B,QAAQ,EAAEV,cAAc,CAAC,sBAAsB,CAAE;YAAAS,QAAA,gBAEjD5C,OAAA,CAACzC,YAAY;cAAAqF,QAAA,eACX5C,OAAA,CAACnB,eAAe;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACflD,OAAA,CAACxC,YAAY;cAAC2F,OAAO,EAAC;YAAqB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC7ClC,YAAY,gBAAGhB,OAAA,CAACxB,UAAU;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACvB,UAAU;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eAEjBlD,OAAA,CAACpC,QAAQ;YAACyF,EAAE,EAAErC,YAAa;YAACsC,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAX,QAAA,eACtD5C,OAAA,CAAC3C,IAAI;cAACmG,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAb,QAAA,gBAClC5C,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,sCAAsC,CAAE;gBAC3Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,sCAAsC,CAAE;gBAAAI,QAAA,gBAElE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAAC3B,SAAS;oBAAC8F,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAwB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,oCAAoC,CAAE;gBACzDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oCAAoC,CAAE;gBAAAI,QAAA,gBAEhE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACL,QAAQ;oBAACwE,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAe;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,oCAAoC,CAAE;gBACzDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oCAAoC,CAAE;gBAAAI,QAAA,gBAEhE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACP,OAAO;oBAAC0E,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAqB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,mCAAmC,CAAE;gBACxDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,mCAAmC,CAAE;gBAAAI,QAAA,gBAE/D5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACH,UAAU;oBAACsE,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAc;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,sCAAsC,CAAE;gBAC3Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,sCAAsC,CAAE;gBAAAI,QAAA,gBAElE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACL,QAAQ;oBAACwE,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAA6B;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,mCAAmC,CAAE;gBACxDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,mCAAmC,CAAE;gBAAAI,QAAA,gBAE/D5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAAC3B,SAAS;oBAAC8F,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAA4B;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXlD,OAAA,CAACnC,cAAc;YACb6F,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdb,OAAO,EAAEA,CAAA,KAAM3B,gBAAgB,CAAC,CAACD,aAAa,CAAE;YAChD2B,QAAQ,EAAEV,cAAc,CAAC,uBAAuB,CAAE;YAAAS,QAAA,gBAElD5C,OAAA,CAACzC,YAAY;cAAAqF,QAAA,eACX5C,OAAA,CAACjB,aAAa;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACflD,OAAA,CAACxC,YAAY;cAAC2F,OAAO,EAAC;YAAY;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACpChC,aAAa,gBAAGlB,OAAA,CAACxB,UAAU;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACvB,UAAU;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eAEjBlD,OAAA,CAACpC,QAAQ;YAACyF,EAAE,EAAEnC,aAAc;YAACoC,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAX,QAAA,eACvD5C,OAAA,CAAC3C,IAAI;cAACmG,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAb,QAAA,gBAClC5C,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,kCAAkC,CAAE;gBACvDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,kCAAkC,CAAE;gBAAAI,QAAA,gBAE9D5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACrB,YAAY;oBAACwF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAmB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,4BAA4B,CAAE;gBACjDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,4BAA4B,CAAE;gBAAAI,QAAA,gBAExD5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACP,OAAO;oBAAC0E,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAmB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,gCAAgC,CAAE;gBACrDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,gCAAgC,CAAE;gBAAAI,QAAA,gBAE5D5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACL,QAAQ;oBAACwE,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAiB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,+BAA+B,CAAE;gBACpDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,+BAA+B,CAAE;gBAAAI,QAAA,gBAE3D5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACH,UAAU;oBAACsE,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAgB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,+BAA+B,CAAE;gBACpDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,+BAA+B,CAAE;gBAAAI,QAAA,gBAE3D5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACb,cAAc;oBAACgF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAkB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXlD,OAAA,CAACnC,cAAc;YACb6F,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdb,OAAO,EAAEA,CAAA,KAAMzB,gBAAgB,CAAC,CAACD,aAAa,CAAE;YAChDyB,QAAQ,EAAEV,cAAc,CAAC,uBAAuB,CAAE;YAAAS,QAAA,gBAElD5C,OAAA,CAACzC,YAAY;cAAAqF,QAAA,eACX5C,OAAA,CAACf,cAAc;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACflD,OAAA,CAACxC,YAAY;cAAC2F,OAAO,EAAC;YAAgB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACxC9B,aAAa,gBAAGpB,OAAA,CAACxB,UAAU;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACvB,UAAU;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eAEjBlD,OAAA,CAACpC,QAAQ;YAACyF,EAAE,EAAEjC,aAAc;YAACkC,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAX,QAAA,eACvD5C,OAAA,CAAC3C,IAAI;cAACmG,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAb,QAAA,gBAClC5C,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,oCAAoC,CAAE;gBACzDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oCAAoC,CAAE;gBAAAI,QAAA,gBAEhE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACP,OAAO;oBAAC0E,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAuB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,sCAAsC,CAAE;gBAC3Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,sCAAsC,CAAE;gBAAAI,QAAA,gBAElE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACP,OAAO;oBAAC0E,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAsB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,qCAAqC,CAAE;gBAC1Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,qCAAqC,CAAE;gBAAAI,QAAA,gBAEjE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACf,cAAc;oBAACkF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAyB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,uCAAuC,CAAE;gBAC5Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,uCAAuC,CAAE;gBAAAI,QAAA,gBAEnE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACf,cAAc;oBAACkF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAA2B;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,oCAAoC,CAAE;gBACzDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oCAAoC,CAAE;gBAAAI,QAAA,gBAEhE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACf,cAAc;oBAACkF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAuB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,sCAAsC,CAAE;gBAC3Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,sCAAsC,CAAE;gBAAAI,QAAA,gBAElE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACf,cAAc;oBAACkF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAyB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXlD,OAAA,CAACnC,cAAc;YACb6F,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdb,OAAO,EAAEA,CAAA,KAAMvB,iBAAiB,CAAC,CAACD,cAAc,CAAE;YAClDuB,QAAQ,EAAEV,cAAc,CAAC,wBAAwB,CAAE;YAAAS,QAAA,gBAEnD5C,OAAA,CAACzC,YAAY;cAAAqF,QAAA,eACX5C,OAAA,CAACb,cAAc;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACflD,OAAA,CAACxC,YAAY;cAAC2F,OAAO,EAAC;YAAQ;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAChC5B,cAAc,gBAAGtB,OAAA,CAACxB,UAAU;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACvB,UAAU;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAEjBlD,OAAA,CAACpC,QAAQ;YAACyF,EAAE,EAAE/B,cAAe;YAACgC,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAX,QAAA,eACxD5C,OAAA,CAAC3C,IAAI;cAACmG,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAb,QAAA,gBAClC5C,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,oCAAoC,CAAE;gBACzDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oCAAoC,CAAE;gBAAAI,QAAA,gBAEhE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACb,cAAc;oBAACgF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAoB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,4BAA4B,CAAE;gBACjDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,4BAA4B,CAAE;gBAAAI,QAAA,gBAExD5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACb,cAAc;oBAACgF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAoB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,wCAAwC,CAAE;gBAC7Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,wCAAwC,CAAE;gBAAAI,QAAA,gBAEpE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACb,cAAc;oBAACgF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAwB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,qCAAqC,CAAE;gBAC1Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,qCAAqC,CAAE;gBAAAI,QAAA,gBAEjE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACb,cAAc;oBAACgF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAyB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXlD,OAAA,CAACnC,cAAc;YACb6F,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdb,OAAO,EAAEA,CAAA,KAAMrB,yBAAyB,CAAC,CAACD,sBAAsB,CAAE;YAClEqB,QAAQ,EAAEV,cAAc,CAAC,gCAAgC,CAAE;YAAAS,QAAA,gBAE3D5C,OAAA,CAACzC,YAAY;cAAAqF,QAAA,eACX5C,OAAA,CAACX,gBAAgB;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACflD,OAAA,CAACxC,YAAY;cAAC2F,OAAO,EAAC;YAAqB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC7C1B,sBAAsB,gBAAGxB,OAAA,CAACxB,UAAU;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACvB,UAAU;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEjBlD,OAAA,CAACpC,QAAQ;YAACyF,EAAE,EAAE7B,sBAAuB;YAAC8B,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAX,QAAA,eAChE5C,OAAA,CAAC3C,IAAI;cAACmG,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAb,QAAA,gBAClC5C,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,2CAA2C,CAAE;gBAChEc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,2CAA2C,CAAE;gBAAAI,QAAA,gBAEvE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACrB,YAAY;oBAACwF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAA2B;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,uCAAuC,CAAE;gBAC5Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,uCAAuC,CAAE;gBAAAI,QAAA,gBAEnE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACrB,YAAY;oBAACwF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAiB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,qCAAqC,CAAE;gBAC1Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,qCAAqC,CAAE;gBAAAI,QAAA,gBAEjE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACP,OAAO;oBAAC0E,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAqB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,yCAAyC,CAAE;gBAC9Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,yCAAyC,CAAE;gBAAAI,QAAA,gBAErE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACrB,YAAY;oBAACwF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAyB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,oCAAoC,CAAE;gBACzDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oCAAoC,CAAE;gBAAAI,QAAA,gBAEhE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACb,cAAc;oBAACgF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAY;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,wCAAwC,CAAE;gBAC7Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,wCAAwC,CAAE;gBAAAI,QAAA,gBAEpE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACH,UAAU;oBAACsE,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAwB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,0CAA0C,CAAE;gBAC/Dc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,0CAA0C,CAAE;gBAAAI,QAAA,gBAEtE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACb,cAAc;oBAACgF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAoB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXlD,OAAA,CAACnC,cAAc;YACb6F,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdb,OAAO,EAAEA,CAAA,KAAMnB,kBAAkB,CAAC,CAACD,eAAe,CAAE;YACpDmB,QAAQ,EAAEV,cAAc,CAAC,yBAAyB,CAAE;YAAAS,QAAA,gBAEpD5C,OAAA,CAACzC,YAAY;cAAAqF,QAAA,eACX5C,OAAA,CAACT,gBAAgB;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACflD,OAAA,CAACxC,YAAY;cAAC2F,OAAO,EAAC;YAAkB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC1CxB,eAAe,gBAAG1B,OAAA,CAACxB,UAAU;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlD,OAAA,CAACvB,UAAU;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eAEjBlD,OAAA,CAACpC,QAAQ;YAACyF,EAAE,EAAE3B,eAAgB;YAAC4B,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAX,QAAA,eACzD5C,OAAA,CAAC3C,IAAI;cAACmG,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAb,QAAA,gBAClC5C,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,oCAAoC,CAAE;gBACzDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oCAAoC,CAAE;gBAAAI,QAAA,gBAEhE5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACrB,YAAY;oBAACwF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAoB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,8BAA8B,CAAE;gBACnDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,8BAA8B,CAAE;gBAAAI,QAAA,gBAE1D5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACP,OAAO;oBAAC0E,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAoB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,kCAAkC,CAAE;gBACvDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,kCAAkC,CAAE;gBAAAI,QAAA,gBAE9D5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACL,QAAQ;oBAACwE,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAkB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,iCAAiC,CAAE;gBACtDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,iCAAiC,CAAE;gBAAAI,QAAA,gBAE7D5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACH,UAAU;oBAACsE,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAiB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,gCAAgC,CAAE;gBACrDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,gCAAgC,CAAE;gBAAAI,QAAA,gBAE5D5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACb,cAAc;oBAACgF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAgB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eAEjBlD,OAAA,CAACnC,cAAc;gBACb6F,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBACdd,QAAQ,EAAEb,QAAQ,CAAC,iCAAiC,CAAE;gBACtDc,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,iCAAiC,CAAE;gBAAAI,QAAA,gBAE7D5C,OAAA,CAACzC,YAAY;kBAAAqF,QAAA,eACX5C,OAAA,CAACb,cAAc;oBAACgF,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACflD,OAAA,CAACxC,YAAY;kBAAC2F,OAAO,EAAC;gBAAwB;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACX;IAAA,eACD,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAAC9C,EAAA,CA7pBID,QAAQ;EAAA,QACKhD,WAAW,EACXC,WAAW,EACwB0C,OAAO;AAAA;AAAAsE,EAAA,GAHvDjE,QAAQ;AA+pBd,eAAeA,QAAQ;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}