{"ast": null, "code": "// Configurazione centralizzata per l'applicazione\nconst config={// URL dell'API del backend\nAPI_URL:'http://localhost:8001/api',// Altre configurazioni globali possono essere aggiunte qui\nDEFAULT_TIMEOUT:60000// 60 secondi (aumentato per risolvere problemi di timeout)\n};export default config;", "map": {"version": 3, "names": ["config", "API_URL", "DEFAULT_TIMEOUT"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/config.js"], "sourcesContent": ["// Configurazione centralizzata per l'applicazione\r\nconst config = {\r\n  // URL dell'API del backend\r\n  API_URL: 'http://localhost:8001/api',\r\n\r\n  // Altre configurazioni globali possono essere aggiunte qui\r\n  DEFAULT_TIMEOUT: 60000, // 60 secondi (aumentato per risolvere problemi di timeout)\r\n};\r\n\r\nexport default config;\r\n"], "mappings": "AAAA;AACA,KAAM,CAAAA,MAAM,CAAG,CACb;AACAC,OAAO,CAAE,2BAA2B,CAEpC;AACAC,eAAe,CAAE,KAAO;AAC1B,CAAC,CAED,cAAe,CAAAF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}