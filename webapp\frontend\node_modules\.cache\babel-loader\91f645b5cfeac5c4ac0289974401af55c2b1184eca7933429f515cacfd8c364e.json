{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Box, Typography, Grid, Card, CardContent, CardActionArea, Avatar, CircularProgress } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport { AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const {\n    user,\n    isImpersonating\n  } = useAuth();\n  const navigate = useNavigate();\n\n  // Reindirizza automaticamente in base al tipo di utente\n  useEffect(() => {\n    // Breve timeout per evitare reindirizzamenti troppo rapidi\n    const redirectTimer = setTimeout(() => {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n        // Reindirizza direttamente alla pagina di gestione cavi\n        navigate('/dashboard/cavi');\n      }\n    }, 300);\n    return () => clearTimeout(redirectTimer);\n  }, [user, isImpersonating, navigate]);\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    navigate(path);\n  };\n\n  // Mostra un indicatore di caricamento durante il reindirizzamento\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minHeight: '50vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Benvenuto nel Sistema di Gestione Cantieri\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      paragraph: true,\n      children: \"Reindirizzamento in corso...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n      sx: {\n        mt: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"/AY/SwGQ07WHXzlP57+ceNb+c+g=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useEffect", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActionArea", "Avatar", "CircularProgress", "useNavigate", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "useAuth", "jsxDEV", "_jsxDEV", "HomePage", "_s", "user", "isImpersonating", "navigate", "redirectTimer", "setTimeout", "role", "clearTimeout", "navigateTo", "path", "sx", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "paragraph", "mt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Box, Typography, Grid, Card, CardContent, CardActionArea, Avatar, CircularProgress } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\n\nconst HomePage = () => {\n  const { user, isImpersonating } = useAuth();\n  const navigate = useNavigate();\n\n  // Reindirizza automaticamente in base al tipo di utente\n  useEffect(() => {\n    // Breve timeout per evitare reindirizzamenti troppo rapidi\n    const redirectTimer = setTimeout(() => {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if (user?.role === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if (user?.role === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if (user?.role === 'cantieri_user') {\n        // Reindirizza direttamente alla pagina di gestione cavi\n        navigate('/dashboard/cavi');\n      }\n    }, 300);\n\n    return () => clearTimeout(redirectTimer);\n  }, [user, isImpersonating, navigate]);\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    navigate(path);\n  };\n\n  // Mostra un indicatore di caricamento durante il reindirizzamento\n  return (\n    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: '50vh' }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Benvenuto nel Sistema di Gestione Cantieri\n      </Typography>\n\n      <Typography variant=\"body1\" paragraph>\n        Reindirizzamento in corso...\n      </Typography>\n\n      <CircularProgress sx={{ mt: 3 }} />\n    </Box>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAEC,cAAc,EAAEC,MAAM,EAAEC,gBAAgB,QAAQ,eAAe;AAClH,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,QACpB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGN,OAAO,CAAC,CAAC;EAC3C,MAAMO,QAAQ,GAAGhB,WAAW,CAAC,CAAC;;EAE9B;EACAT,SAAS,CAAC,MAAM;IACd;IACA,MAAM0B,aAAa,GAAGC,UAAU,CAAC,MAAM;MACrC;MACA,IAAIH,eAAe,EAAE;QACnBC,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,IAAI,MAAK,OAAO,EAAE;QAC/BH,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,IAAI,MAAK,MAAM,EAAE;QAC9BH,QAAQ,CAAC,qBAAqB,CAAC;MACjC;MACA;MAAA,KACK,IAAI,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,IAAI,MAAK,eAAe,EAAE;QACvC;QACAH,QAAQ,CAAC,iBAAiB,CAAC;MAC7B;IACF,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMI,YAAY,CAACH,aAAa,CAAC;EAC1C,CAAC,EAAE,CAACH,IAAI,EAAEC,eAAe,EAAEC,QAAQ,CAAC,CAAC;;EAErC;EACA,MAAMK,UAAU,GAAIC,IAAI,IAAK;IAC3BN,QAAQ,CAACM,IAAI,CAAC;EAChB,CAAC;;EAED;EACA,oBACEX,OAAA,CAACnB,GAAG;IAAC+B,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,UAAU,EAAE,QAAQ;MAAEC,cAAc,EAAE,QAAQ;MAAEC,SAAS,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACvHlB,OAAA,CAAClB,UAAU;MAACqC,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbxB,OAAA,CAAClB,UAAU;MAACqC,OAAO,EAAC,OAAO;MAACM,SAAS;MAAAP,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbxB,OAAA,CAACZ,gBAAgB;MAACwB,EAAE,EAAE;QAAEc,EAAE,EAAE;MAAE;IAAE;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChC,CAAC;AAEV,CAAC;AAACtB,EAAA,CAjDID,QAAQ;EAAA,QACsBH,OAAO,EACxBT,WAAW;AAAA;AAAAsC,EAAA,GAFxB1B,QAAQ;AAmDd,eAAeA,QAAQ;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}