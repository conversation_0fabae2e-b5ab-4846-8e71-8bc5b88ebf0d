{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"mai puțin de o secundă\",\n    other: \"mai puțin de {{count}} secunde\"\n  },\n  xSeconds: {\n    one: \"1 secundă\",\n    other: \"{{count}} secunde\"\n  },\n  halfAMinute: \"jumătate de minut\",\n  lessThanXMinutes: {\n    one: \"mai puțin de un minut\",\n    other: \"mai puțin de {{count}} minute\"\n  },\n  xMinutes: {\n    one: \"1 minut\",\n    other: \"{{count}} minute\"\n  },\n  aboutXHours: {\n    one: \"circa 1 oră\",\n    other: \"circa {{count}} ore\"\n  },\n  xHours: {\n    one: \"1 oră\",\n    other: \"{{count}} ore\"\n  },\n  xDays: {\n    one: \"1 zi\",\n    other: \"{{count}} zile\"\n  },\n  aboutXWeeks: {\n    one: \"circa o săptămână\",\n    other: \"circa {{count}} săptămâni\"\n  },\n  xWeeks: {\n    one: \"1 săptămână\",\n    other: \"{{count}} săptămâni\"\n  },\n  aboutXMonths: {\n    one: \"circa 1 lună\",\n    other: \"circa {{count}} luni\"\n  },\n  xMonths: {\n    one: \"1 lună\",\n    other: \"{{count}} luni\"\n  },\n  aboutXYears: {\n    one: \"circa 1 an\",\n    other: \"circa {{count}} ani\"\n  },\n  xYears: {\n    one: \"1 an\",\n    other: \"{{count}} ani\"\n  },\n  overXYears: {\n    one: \"peste 1 an\",\n    other: \"peste {{count}} ani\"\n  },\n  almostXYears: {\n    one: \"aproape 1 an\",\n    other: \"aproape {{count}} ani\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"în \" + result;\n    } else {\n      return result + \" în urmă\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ro/_lib/formatDistance.mjs"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"mai puțin de o secundă\",\n    other: \"mai puțin de {{count}} secunde\",\n  },\n\n  xSeconds: {\n    one: \"1 secundă\",\n    other: \"{{count}} secunde\",\n  },\n\n  halfAMinute: \"jumătate de minut\",\n\n  lessThanXMinutes: {\n    one: \"mai puțin de un minut\",\n    other: \"mai puțin de {{count}} minute\",\n  },\n\n  xMinutes: {\n    one: \"1 minut\",\n    other: \"{{count}} minute\",\n  },\n\n  aboutXHours: {\n    one: \"circa 1 oră\",\n    other: \"circa {{count}} ore\",\n  },\n\n  xHours: {\n    one: \"1 oră\",\n    other: \"{{count}} ore\",\n  },\n\n  xDays: {\n    one: \"1 zi\",\n    other: \"{{count}} zile\",\n  },\n\n  aboutXWeeks: {\n    one: \"circa o săptămână\",\n    other: \"circa {{count}} săptămâni\",\n  },\n\n  xWeeks: {\n    one: \"1 săptămână\",\n    other: \"{{count}} săptămâni\",\n  },\n\n  aboutXMonths: {\n    one: \"circa 1 lună\",\n    other: \"circa {{count}} luni\",\n  },\n\n  xMonths: {\n    one: \"1 lună\",\n    other: \"{{count}} luni\",\n  },\n\n  aboutXYears: {\n    one: \"circa 1 an\",\n    other: \"circa {{count}} ani\",\n  },\n\n  xYears: {\n    one: \"1 an\",\n    other: \"{{count}} ani\",\n  },\n\n  overXYears: {\n    one: \"peste 1 an\",\n    other: \"peste {{count}} ani\",\n  },\n\n  almostXYears: {\n    one: \"aproape 1 an\",\n    other: \"aproape {{count}} ani\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"în \" + result;\n    } else {\n      return result + \" în urmă\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,wBAAwB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,mBAAmB;EAEhCC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,uBAAuB;IAC5BC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,UAAU;IAC5B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}