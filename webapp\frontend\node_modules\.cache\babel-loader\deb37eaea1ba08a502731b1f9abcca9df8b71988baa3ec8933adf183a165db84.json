{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m-2.85 8.35L16 13.5v2c0 .28-.22.5-.5.5h-2l-1.15 1.15c-.2.2-.51.2-.71 0L10.5 16h-2c-.28 0-.5-.22-.5-.5v-2l-1.15-1.15c-.2-.2-.2-.51 0-.71L8 10.5v-2c0-.28.22-.5.5-.5h2l1.15-1.15c.2-.2.51-.2.71 0L13.5 8h2c.28 0 .5.22.5.5v2l1.15 1.15c.19.19.19.51 0 .7M12 9v6c1.66 0 3-1.34 3-3s-1.34-3-3-3\"\n}), 'SettingsBrightnessRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/icons-material/esm/SettingsBrightnessRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m-2.85 8.35L16 13.5v2c0 .28-.22.5-.5.5h-2l-1.15 1.15c-.2.2-.51.2-.71 0L10.5 16h-2c-.28 0-.5-.22-.5-.5v-2l-1.15-1.15c-.2-.2-.2-.51 0-.71L8 10.5v-2c0-.28.22-.5.5-.5h2l1.15-1.15c.2-.2.51-.2.71 0L13.5 8h2c.28 0 .5.22.5.5v2l1.15 1.15c.19.19.19.51 0 .7M12 9v6c1.66 0 3-1.34 3-3s-1.34-3-3-3\"\n}), 'SettingsBrightnessRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,2BAA2B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}