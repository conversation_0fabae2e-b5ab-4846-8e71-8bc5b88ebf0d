{"ast": null, "code": "import { setWeek } from \"../../../setWeek.js\";\nimport { startOfWeek } from \"../../../startOfWeek.js\";\nimport { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\n// Local week of year\nexport class LocalWeekParser extends Parser {\n  priority = 100;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"w\":\n        return parseNumericPattern(numericPatterns.week, dateString);\n      case \"wo\":\n        return match.ordinalNumber(dateString, {\n          unit: \"week\"\n        });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n  validate(_date, value) {\n    return value >= 1 && value <= 53;\n  }\n  set(date, _flags, value, options) {\n    return startOfWeek(setWeek(date, value, options), options);\n  }\n  incompatibleTokens = [\"y\", \"R\", \"u\", \"q\", \"Q\", \"M\", \"L\", \"I\", \"d\", \"D\", \"i\", \"t\", \"T\"];\n}", "map": {"version": 3, "names": ["setWeek", "startOfWeek", "numericPatterns", "<PERSON><PERSON><PERSON>", "parseNDigits", "parseNumericPattern", "LocalWeekParser", "priority", "parse", "dateString", "token", "match", "week", "ordinalNumber", "unit", "length", "validate", "_date", "value", "set", "date", "_flags", "options", "incompatibleTokens"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/parse/_lib/parsers/LocalWeekParser.js"], "sourcesContent": ["import { setWeek } from \"../../../setWeek.js\";\nimport { startOfWeek } from \"../../../startOfWeek.js\";\nimport { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\n// Local week of year\nexport class LocalWeekParser extends Parser {\n  priority = 100;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"w\":\n        return parseNumericPattern(numericPatterns.week, dateString);\n      case \"wo\":\n        return match.ordinalNumber(dateString, { unit: \"week\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 53;\n  }\n\n  set(date, _flags, value, options) {\n    return startOfWeek(setWeek(date, value, options), options);\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"R\",\n    \"u\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"i\",\n    \"t\",\n    \"T\",\n  ];\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,aAAa;;AAE/D;AACA,OAAO,MAAMC,eAAe,SAASH,MAAM,CAAC;EAC1CI,QAAQ,GAAG,GAAG;EAEdC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC9B,QAAQD,KAAK;MACX,KAAK,GAAG;QACN,OAAOL,mBAAmB,CAACH,eAAe,CAACU,IAAI,EAAEH,UAAU,CAAC;MAC9D,KAAK,IAAI;QACP,OAAOE,KAAK,CAACE,aAAa,CAACJ,UAAU,EAAE;UAAEK,IAAI,EAAE;QAAO,CAAC,CAAC;MAC1D;QACE,OAAOV,YAAY,CAACM,KAAK,CAACK,MAAM,EAAEN,UAAU,CAAC;IACjD;EACF;EAEAO,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACrB,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,EAAE;EAClC;EAEAC,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEH,KAAK,EAAEI,OAAO,EAAE;IAChC,OAAOrB,WAAW,CAACD,OAAO,CAACoB,IAAI,EAAEF,KAAK,EAAEI,OAAO,CAAC,EAAEA,OAAO,CAAC;EAC5D;EAEAC,kBAAkB,GAAG,CACnB,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACJ;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}