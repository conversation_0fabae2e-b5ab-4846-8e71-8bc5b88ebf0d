import { NextRequest, NextResponse } from 'next/server'

interface StrumentoCertificato {
  id_strumento: number
  id_cantiere: number
  nome: string
  marca: string
  modello: string
  numero_serie: string
  data_calibrazione: string
  data_scadenza_calibrazione: string
  note?: string
  timestamp_creazione: string
  timestamp_modifica?: string
  tipo_strumento?: 'MEGGER' | 'MULTIMETRO' | 'OSCILLOSCOPIO' | 'ALTRO'
  ente_certificatore?: string
  numero_certificato_calibrazione?: string
  range_misura?: string
  precisione?: string
  stato_strumento?: 'ATTIVO' | 'SCADUTO' | 'FUORI_SERVIZIO'
}

export async function GET(
  request: NextRequest,
  { params }: { params: { cantiereId: string } }
) {
  try {
    const cantiereId = parseInt(params.cantiereId)
    
    if (isNaN(cantiereId)) {
      return NextResponse.json(
        { error: 'ID cantiere non valido' },
        { status: 400 }
      )
    }

    // Ottieni il token di autenticazione dalla richiesta
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Token di autenticazione richiesto' },
        { status: 401 }
      )
    }

    // Chiama l'API backend Python
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    const response = await fetch(
      `${backendUrl}/cantieri/${cantiereId}/strumenti`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': authHeader,
        },
      }
    )

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))
      return NextResponse.json(
        { error: errorData.detail || 'Errore dal backend' },
        { status: response.status }
      )
    }

    const data = await response.json()

    return NextResponse.json({
      success: true,
      data: data,
      total: data.length
    })

  } catch (error) {
    console.error('Errore nel recupero strumenti:', error)
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { cantiereId: string } }
) {
  try {
    const cantiereId = parseInt(params.cantiereId)
    const body = await request.json()
    
    if (isNaN(cantiereId)) {
      return NextResponse.json(
        { error: 'ID cantiere non valido' },
        { status: 400 }
      )
    }

    // Ottieni il token di autenticazione dalla richiesta
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Token di autenticazione richiesto' },
        { status: 401 }
      )
    }

    // Validazione base
    if (!body.nome || !body.marca || !body.modello || !body.numero_serie) {
      return NextResponse.json(
        { error: 'Campi obbligatori mancanti' },
        { status: 400 }
      )
    }

    // Chiama l'API backend Python
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    const response = await fetch(
      `${backendUrl}/cantieri/${cantiereId}/strumenti`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': authHeader,
        },
        body: JSON.stringify(body),
      }
    )

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))
      return NextResponse.json(
        { error: errorData.detail || 'Errore dal backend' },
        { status: response.status }
      )
    }

    const data = await response.json()

    return NextResponse.json({
      success: true,
      data: data
    }, { status: 201 })

  } catch (error) {
    console.error('Errore nella creazione strumento:', error)
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    )
  }
}
