import { NextRequest, NextResponse } from 'next/server'

interface StrumentoCertificato {
  id_strumento: number
  id_cantiere: number
  nome: string
  marca: string
  modello: string
  numero_serie: string
  data_calibrazione: string
  data_scadenza_calibrazione: string
  note?: string
  timestamp_creazione: string
  timestamp_modifica?: string
  tipo_strumento?: 'MEGGER' | 'MULTIMETRO' | 'OSCILLOSCOPIO' | 'ALTRO'
  ente_certificatore?: string
  numero_certificato_calibrazione?: string
  range_misura?: string
  precisione?: string
  stato_strumento?: 'ATTIVO' | 'SCADUTO' | 'FUORI_SERVIZIO'
}

export async function GET(
  request: NextRequest,
  { params }: { params: { cantiereId: string } }
) {
  try {
    const cantiereId = parseInt(params.cantiereId)
    
    if (isNaN(cantiereId)) {
      return NextResponse.json(
        { error: 'ID cantiere non valido' },
        { status: 400 }
      )
    }

    // Per ora restituiamo dati mock per testare l'interfaccia
    // TODO: Implementare chiamata al backend Python
    const mockStrumenti: StrumentoCertificato[] = [
      {
        id_strumento: 1,
        id_cantiere: cantiereId,
        nome: "Megger MIT1025",
        marca: "Megger",
        modello: "MIT1025",
        numero_serie: "MG2024001",
        data_calibrazione: "2024-01-15",
        data_scadenza_calibrazione: "2025-01-15",
        note: "Strumento principale per test di isolamento",
        timestamp_creazione: "2024-01-15T10:00:00Z",
        tipo_strumento: "MEGGER",
        ente_certificatore: "LAT 123",
        numero_certificato_calibrazione: "CAL-2024-001",
        range_misura: "0.01 MΩ - 10 GΩ",
        precisione: "±2%",
        stato_strumento: "ATTIVO"
      },
      {
        id_strumento: 2,
        id_cantiere: cantiereId,
        nome: "Fluke 87V",
        marca: "Fluke",
        modello: "87V",
        numero_serie: "FL2024002",
        data_calibrazione: "2024-02-01",
        data_scadenza_calibrazione: "2025-02-01",
        note: "Multimetro digitale per misure di continuità",
        timestamp_creazione: "2024-02-01T14:30:00Z",
        tipo_strumento: "MULTIMETRO",
        ente_certificatore: "LAT 456",
        numero_certificato_calibrazione: "CAL-2024-002",
        range_misura: "0.1 Ω - 50 MΩ",
        precisione: "±0.5%",
        stato_strumento: "ATTIVO"
      },
      {
        id_strumento: 3,
        id_cantiere: cantiereId,
        nome: "Keysight DSOX1204G",
        marca: "Keysight",
        modello: "DSOX1204G",
        numero_serie: "KS2024003",
        data_calibrazione: "2024-01-20",
        data_scadenza_calibrazione: "2025-01-20",
        note: "Oscilloscopio per analisi segnali",
        timestamp_creazione: "2024-01-20T09:15:00Z",
        tipo_strumento: "OSCILLOSCOPIO",
        ente_certificatore: "LAT 789",
        numero_certificato_calibrazione: "CAL-2024-003",
        range_misura: "1 mV/div - 5 V/div",
        precisione: "±3%",
        stato_strumento: "ATTIVO"
      }
    ]

    // Filtra solo strumenti attivi
    const strumentiAttivi = mockStrumenti.filter(s => s.stato_strumento === 'ATTIVO')

    return NextResponse.json({
      success: true,
      data: strumentiAttivi,
      total: strumentiAttivi.length
    })

  } catch (error) {
    console.error('Errore nel recupero strumenti:', error)
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { cantiereId: string } }
) {
  try {
    const cantiereId = parseInt(params.cantiereId)
    const body = await request.json()
    
    if (isNaN(cantiereId)) {
      return NextResponse.json(
        { error: 'ID cantiere non valido' },
        { status: 400 }
      )
    }

    // Validazione base
    if (!body.nome || !body.marca || !body.modello || !body.numero_serie) {
      return NextResponse.json(
        { error: 'Campi obbligatori mancanti' },
        { status: 400 }
      )
    }

    // Per ora restituiamo un mock dello strumento creato
    // TODO: Implementare chiamata al backend Python
    const nuovoStrumento: StrumentoCertificato = {
      id_strumento: Math.floor(Math.random() * 1000) + 100,
      id_cantiere: cantiereId,
      nome: body.nome,
      marca: body.marca,
      modello: body.modello,
      numero_serie: body.numero_serie,
      data_calibrazione: body.data_calibrazione,
      data_scadenza_calibrazione: body.data_scadenza_calibrazione,
      note: body.note || null,
      timestamp_creazione: new Date().toISOString(),
      tipo_strumento: body.tipo_strumento || 'ALTRO',
      ente_certificatore: body.ente_certificatore || null,
      numero_certificato_calibrazione: body.numero_certificato_calibrazione || null,
      range_misura: body.range_misura || null,
      precisione: body.precisione || null,
      stato_strumento: 'ATTIVO'
    }

    return NextResponse.json({
      success: true,
      data: nuovoStrumento
    }, { status: 201 })

  } catch (error) {
    console.error('Errore nella creazione strumento:', error)
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    )
  }
}
