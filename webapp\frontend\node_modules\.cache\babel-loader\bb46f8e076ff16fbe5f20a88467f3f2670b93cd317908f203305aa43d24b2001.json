{"ast": null, "code": "import React,{useEffect}from'react';import{useNavigate}from'react-router-dom';import{useAuth}from'../context/AuthContext';const CaviPage=()=>{const navigate=useNavigate();const{user}=useAuth();// Reindirizza alla pagina di visualizzazione cavi\nuseEffect(()=>{console.log('CaviPage - Inizializzazione');// Verifica se l'utente è un utente cantiere\nconst isCantieresUser=(user===null||user===void 0?void 0:user.role)==='cantieri_user';console.log('Utente cantiere:',isCantieresUser);// Verifica se c'è un cantiere selezionato nel localStorage\nconst selectedCantiereId=localStorage.getItem('selectedCantiereId');const selectedCantiereName=localStorage.getItem('selectedCantiereName');console.log('Cantiere selezionato:',{selectedCantiereId,selectedCantiereName});// Se l'utente è un utente cantiere ma non c'è un cantiere selezionato,\n// potrebbe essere necessario recuperare l'ID del cantiere dal token\nif(isCantieresUser&&!selectedCantiereId&&user!==null&&user!==void 0&&user.id){console.log('Utente cantiere senza cantiere selezionato, tentativo di recupero dal token');// Qui potremmo fare una chiamata API per ottenere i dettagli del cantiere\n// Per ora, reindirizza comunque alla pagina di visualizzazione cavi\n}// Recupera il tab index dal localStorage\nconst savedTabIndex=localStorage.getItem('caviTabIndex');const tabIndex=savedTabIndex?parseInt(savedTabIndex,10):0;console.log('Tab index:',tabIndex);// Reindirizza alla pagina appropriata in base al tab index\nswitch(tabIndex){case 0:console.log('Reindirizzamento a /dashboard/cavi/visualizza');navigate('/dashboard/cavi/visualizza');break;case 1:navigate('/dashboard/cavi/posa');break;case 2:navigate('/dashboard/cavi/parco');break;case 3:navigate('/dashboard/cavi/excel');break;case 4:navigate('/dashboard/cavi/report');break;case 5:navigate('/dashboard/cavi/certificazione');break;case 6:navigate('/dashboard/cavi/comande');break;default:console.log('Tab index non valido, reindirizzamento a /dashboard/cavi/visualizza');navigate('/dashboard/cavi/visualizza');break;}},[navigate,user]);// Rendering di un componente vuoto, poiché il reindirizzamento avviene nell'useEffect\nreturn null;};export default CaviPage;", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useAuth", "CaviPage", "navigate", "user", "console", "log", "isCantieresUser", "role", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "id", "savedTabIndex", "tabIndex", "parseInt"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/CaviPage.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\n\nconst CaviPage = () => {\n  const navigate = useNavigate();\n  const { user } = useAuth();\n\n  // Reindirizza alla pagina di visualizzazione cavi\n  useEffect(() => {\n    console.log('CaviPage - Inizializzazione');\n\n    // Verifica se l'utente è un utente cantiere\n    const isCantieresUser = user?.role === 'cantieri_user';\n    console.log('Utente cantiere:', isCantieresUser);\n\n    // Verifica se c'è un cantiere selezionato nel localStorage\n    const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n    const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n    console.log('Cantiere selezionato:', { selectedCantiereId, selectedCantiereName });\n\n    // Se l'utente è un utente cantiere ma non c'è un cantiere selezionato,\n    // potrebbe essere necessario recuperare l'ID del cantiere dal token\n    if (isCantieresUser && !selectedCantiereId && user?.id) {\n      console.log('Utente cantiere senza cantiere selezionato, tentativo di recupero dal token');\n      // Qui potremmo fare una chiamata API per ottenere i dettagli del cantiere\n      // Per ora, reindirizza comunque alla pagina di visualizzazione cavi\n    }\n\n    // Recupera il tab index dal localStorage\n    const savedTabIndex = localStorage.getItem('caviTabIndex');\n    const tabIndex = savedTabIndex ? parseInt(savedTabIndex, 10) : 0;\n    console.log('Tab index:', tabIndex);\n\n    // Reindirizza alla pagina appropriata in base al tab index\n    switch (tabIndex) {\n      case 0:\n        console.log('Reindirizzamento a /dashboard/cavi/visualizza');\n        navigate('/dashboard/cavi/visualizza');\n        break;\n      case 1:\n        navigate('/dashboard/cavi/posa');\n        break;\n      case 2:\n        navigate('/dashboard/cavi/parco');\n        break;\n      case 3:\n        navigate('/dashboard/cavi/excel');\n        break;\n      case 4:\n        navigate('/dashboard/cavi/report');\n        break;\n      case 5:\n        navigate('/dashboard/cavi/certificazione');\n        break;\n      case 6:\n        navigate('/dashboard/cavi/comande');\n        break;\n      default:\n        console.log('Tab index non valido, reindirizzamento a /dashboard/cavi/visualizza');\n        navigate('/dashboard/cavi/visualizza');\n        break;\n    }\n  }, [navigate, user]);\n\n  // Rendering di un componente vuoto, poiché il reindirizzamento avviene nell'useEffect\n  return null;\n};\n\nexport default CaviPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,OAAO,KAAQ,wBAAwB,CAEhD,KAAM,CAAAC,QAAQ,CAAGA,CAAA,GAAM,CACrB,KAAM,CAAAC,QAAQ,CAAGH,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEI,IAAK,CAAC,CAAGH,OAAO,CAAC,CAAC,CAE1B;AACAF,SAAS,CAAC,IAAM,CACdM,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC,CAE1C;AACA,KAAM,CAAAC,eAAe,CAAG,CAAAH,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEI,IAAI,IAAK,eAAe,CACtDH,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEC,eAAe,CAAC,CAEhD;AACA,KAAM,CAAAE,kBAAkB,CAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CACrE,KAAM,CAAAC,oBAAoB,CAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CACzEN,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAE,CAAEG,kBAAkB,CAAEG,oBAAqB,CAAC,CAAC,CAElF;AACA;AACA,GAAIL,eAAe,EAAI,CAACE,kBAAkB,EAAIL,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAES,EAAE,CAAE,CACtDR,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC,CAC1F;AACA;AACF,CAEA;AACA,KAAM,CAAAQ,aAAa,CAAGJ,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CAC1D,KAAM,CAAAI,QAAQ,CAAGD,aAAa,CAAGE,QAAQ,CAACF,aAAa,CAAE,EAAE,CAAC,CAAG,CAAC,CAChET,OAAO,CAACC,GAAG,CAAC,YAAY,CAAES,QAAQ,CAAC,CAEnC;AACA,OAAQA,QAAQ,EACd,IAAK,EAAC,CACJV,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC,CAC5DH,QAAQ,CAAC,4BAA4B,CAAC,CACtC,MACF,IAAK,EAAC,CACJA,QAAQ,CAAC,sBAAsB,CAAC,CAChC,MACF,IAAK,EAAC,CACJA,QAAQ,CAAC,uBAAuB,CAAC,CACjC,MACF,IAAK,EAAC,CACJA,QAAQ,CAAC,uBAAuB,CAAC,CACjC,MACF,IAAK,EAAC,CACJA,QAAQ,CAAC,wBAAwB,CAAC,CAClC,MACF,IAAK,EAAC,CACJA,QAAQ,CAAC,gCAAgC,CAAC,CAC1C,MACF,IAAK,EAAC,CACJA,QAAQ,CAAC,yBAAyB,CAAC,CACnC,MACF,QACEE,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC,CAClFH,QAAQ,CAAC,4BAA4B,CAAC,CACtC,MACJ,CACF,CAAC,CAAE,CAACA,QAAQ,CAAEC,IAAI,CAAC,CAAC,CAEpB;AACA,MAAO,KAAI,CACb,CAAC,CAED,cAAe,CAAAF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}