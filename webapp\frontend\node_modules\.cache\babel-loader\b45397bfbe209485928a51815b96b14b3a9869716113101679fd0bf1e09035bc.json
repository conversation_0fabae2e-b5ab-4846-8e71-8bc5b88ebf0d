{"ast": null, "code": "import React from'react';import{Navigate,useLocation}from'react-router-dom';import{useAuth}from'../context/AuthContext';import{CircularProgress,Box}from'@mui/material';import{jsx as _jsx}from\"react/jsx-runtime\";const ProtectedRoute=_ref=>{let{children,requiredRole}=_ref;const{isAuthenticated,user,loading}=useAuth();const location=useLocation();console.log('ProtectedRoute - Stato autenticazione:',{isAuthenticated,loading,user});console.log('ProtectedRoute - Percorso corrente:',location.pathname);// Mostra un indicatore di caricamento mentre verifichiamo l'autenticazione\nif(loading){console.log('ProtectedRoute - Caricamento in corso...');return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"100vh\",children:/*#__PURE__*/_jsx(CircularProgress,{})});}// Se l'utente non è autenticato, reindirizza alla pagina di login\nif(!isAuthenticated){console.log('ProtectedRoute - Utente non autenticato, reindirizzamento a /login');// Non rimuoviamo il token qui, lo facciamo solo in AuthContext\nreturn/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true,state:{from:location.pathname}});}// Se è richiesto un ruolo specifico, verifica che l'utente abbia quel ruolo\nif(requiredRole&&user.role!==requiredRole){console.log(`ProtectedRoute - Utente non ha il ruolo richiesto: ${requiredRole}, reindirizzamento a /dashboard`);// Reindirizza alla dashboard o a una pagina di accesso negato\nreturn/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true});}// Restrizioni per utenti cantiere: possono accedere solo alla gestione cavi\nif(user.role==='cantieri_user'){// Verifica se il percorso corrente non è relativo alla gestione cavi\nif(!location.pathname.includes('/dashboard/cavi')){console.log('ProtectedRoute - Utente cantiere tenta di accedere a una pagina non autorizzata, reindirizzamento a /dashboard/cavi');return/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard/cavi\",replace:true});}}console.log('ProtectedRoute - Accesso consentito');// Se l'utente è autenticato e ha il ruolo richiesto, mostra il contenuto protetto\nreturn children;};export default ProtectedRoute;", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "CircularProgress", "Box", "jsx", "_jsx", "ProtectedRoute", "_ref", "children", "requiredRole", "isAuthenticated", "user", "loading", "location", "console", "log", "pathname", "display", "justifyContent", "alignItems", "minHeight", "to", "replace", "state", "from", "role", "includes"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport { CircularProgress, Box } from '@mui/material';\n\nconst ProtectedRoute = ({ children, requiredRole }) => {\n  const { isAuthenticated, user, loading } = useAuth();\n  const location = useLocation();\n\n  console.log('ProtectedRoute - Stato autenticazione:', { isAuthenticated, loading, user });\n  console.log('ProtectedRoute - Percorso corrente:', location.pathname);\n\n  // Mostra un indicatore di caricamento mentre verifichiamo l'autenticazione\n  if (loading) {\n    console.log('ProtectedRoute - Caricamento in corso...');\n    return (\n      <Box\n        display=\"flex\"\n        justifyContent=\"center\"\n        alignItems=\"center\"\n        minHeight=\"100vh\"\n      >\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  // Se l'utente non è autenticato, reindirizza alla pagina di login\n  if (!isAuthenticated) {\n    console.log('ProtectedRoute - Utente non autenticato, reindirizzamento a /login');\n    // Non rimuoviamo il token qui, lo facciamo solo in AuthContext\n    return <Navigate to=\"/login\" replace state={{ from: location.pathname }} />;\n  }\n\n  // Se è richiesto un ruolo specifico, verifica che l'utente abbia quel ruolo\n  if (requiredRole && user.role !== requiredRole) {\n    console.log(`ProtectedRoute - Utente non ha il ruolo richiesto: ${requiredRole}, reindirizzamento a /dashboard`);\n    // Reindirizza alla dashboard o a una pagina di accesso negato\n    return <Navigate to=\"/dashboard\" replace />;\n  }\n\n  // Restrizioni per utenti cantiere: possono accedere solo alla gestione cavi\n  if (user.role === 'cantieri_user') {\n    // Verifica se il percorso corrente non è relativo alla gestione cavi\n    if (!location.pathname.includes('/dashboard/cavi')) {\n      console.log('ProtectedRoute - Utente cantiere tenta di accedere a una pagina non autorizzata, reindirizzamento a /dashboard/cavi');\n      return <Navigate to=\"/dashboard/cavi\" replace />;\n    }\n  }\n\n  console.log('ProtectedRoute - Accesso consentito');\n\n  // Se l'utente è autenticato e ha il ruolo richiesto, mostra il contenuto protetto\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,QAAQ,CAAEC,WAAW,KAAQ,kBAAkB,CACxD,OAASC,OAAO,KAAQ,wBAAwB,CAChD,OAASC,gBAAgB,CAAEC,GAAG,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEtD,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAgC,IAA/B,CAAEC,QAAQ,CAAEC,YAAa,CAAC,CAAAF,IAAA,CAChD,KAAM,CAAEG,eAAe,CAAEC,IAAI,CAAEC,OAAQ,CAAC,CAAGX,OAAO,CAAC,CAAC,CACpD,KAAM,CAAAY,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAE9Bc,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAE,CAAEL,eAAe,CAAEE,OAAO,CAAED,IAAK,CAAC,CAAC,CACzFG,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAEF,QAAQ,CAACG,QAAQ,CAAC,CAErE;AACA,GAAIJ,OAAO,CAAE,CACXE,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CACvD,mBACEV,IAAA,CAACF,GAAG,EACFc,OAAO,CAAC,MAAM,CACdC,cAAc,CAAC,QAAQ,CACvBC,UAAU,CAAC,QAAQ,CACnBC,SAAS,CAAC,OAAO,CAAAZ,QAAA,cAEjBH,IAAA,CAACH,gBAAgB,GAAE,CAAC,CACjB,CAAC,CAEV,CAEA;AACA,GAAI,CAACQ,eAAe,CAAE,CACpBI,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC,CACjF;AACA,mBAAOV,IAAA,CAACN,QAAQ,EAACsB,EAAE,CAAC,QAAQ,CAACC,OAAO,MAACC,KAAK,CAAE,CAAEC,IAAI,CAAEX,QAAQ,CAACG,QAAS,CAAE,CAAE,CAAC,CAC7E,CAEA;AACA,GAAIP,YAAY,EAAIE,IAAI,CAACc,IAAI,GAAKhB,YAAY,CAAE,CAC9CK,OAAO,CAACC,GAAG,CAAC,sDAAsDN,YAAY,iCAAiC,CAAC,CAChH;AACA,mBAAOJ,IAAA,CAACN,QAAQ,EAACsB,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAC,CAC7C,CAEA;AACA,GAAIX,IAAI,CAACc,IAAI,GAAK,eAAe,CAAE,CACjC;AACA,GAAI,CAACZ,QAAQ,CAACG,QAAQ,CAACU,QAAQ,CAAC,iBAAiB,CAAC,CAAE,CAClDZ,OAAO,CAACC,GAAG,CAAC,qHAAqH,CAAC,CAClI,mBAAOV,IAAA,CAACN,QAAQ,EAACsB,EAAE,CAAC,iBAAiB,CAACC,OAAO,MAAE,CAAC,CAClD,CACF,CAEAR,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC,CAElD;AACA,MAAO,CAAAP,QAAQ,CACjB,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}