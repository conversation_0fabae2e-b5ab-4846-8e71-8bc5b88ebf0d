{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\TopNavbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { AppBar, Toolbar, Box, Button, Menu, MenuItem, Typography, IconButton, Divider, Avatar, Tooltip, Snackbar, Alert } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon, Logout as LogoutIcon, KeyboardArrowDown as ArrowDownIcon, FileUpload as FileUploadIcon, FileDownload as FileDownloadIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { useGlobalContext } from '../context/GlobalContext';\nimport SelectedCantiereDisplay from './common/SelectedCantiereDisplay';\nimport ExcelPopup from './cavi/ExcelPopup';\nimport Logo from './Logo';\nimport './TopNavbar.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TopNavbar = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    logout,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n  const {\n    setOpenEliminaCavoDialog,\n    setOpenModificaCavoDialog\n  } = useGlobalContext();\n\n  // Stato per il popup Excel\n  const [excelPopupOpen, setExcelPopupOpen] = useState(false);\n  const [excelOperationType, setExcelOperationType] = useState('');\n\n  // Stato per gli snackbar\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  // const [posaAnchorEl, setPosaAnchorEl] = useState(null); // OBSOLETO: Menu Posa rimosso\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = setAnchorEl => {\n    setAnchorEl(null);\n  };\n\n  // Gestisce l'apertura del popup Excel\n  const handleOpenExcelPopup = operationType => {\n    setExcelOperationType(operationType);\n    setExcelPopupOpen(true);\n    handleMenuClose(setExcelAnchorEl);\n  };\n\n  // Gestisce la creazione diretta dei template senza popup\n  const handleCreateTemplateDirect = async templateType => {\n    try {\n      handleMenuClose(setExcelAnchorEl);\n      if (templateType === 'cavi') {\n        const excelService = await import('../services/excelService');\n        await excelService.default.createCaviTemplate();\n        setSnackbar({\n          open: true,\n          message: 'Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.',\n          severity: 'success'\n        });\n      } else if (templateType === 'parco-bobine') {\n        const excelService = await import('../services/excelService');\n        await excelService.default.createParcoBobineTemplate();\n        setSnackbar({\n          open: true,\n          message: 'Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.',\n          severity: 'success'\n        });\n      }\n    } catch (error) {\n      console.error(`Errore nella creazione del template ${templateType}:`, error);\n      setSnackbar({\n        open: true,\n        message: `Errore nella creazione del template ${templateType}: ${error.message || 'Errore sconosciuto'}`,\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la chiusura del popup Excel\n  const handleCloseExcelPopup = () => {\n    setExcelPopupOpen(false);\n  };\n\n  // Gestisce il successo delle operazioni Excel\n  const handleExcelSuccess = message => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'success'\n    });\n  };\n\n  // Gestisce gli errori delle operazioni Excel\n  const handleExcelError = message => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'error'\n    });\n  };\n\n  // Gestisce la chiusura dello snackbar\n  const handleCloseSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    // Gestione speciale per il percorso Home\n    if (path === '/dashboard') {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n        // Reindirizza direttamente alla pagina di visualizzazione cavi\n        navigate('/dashboard/cavi/visualizza');\n      }\n      // Fallback per altri tipi di utenti\n      else {\n        navigate(path);\n      }\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    // handleMenuClose(setPosaAnchorEl); // OBSOLETO: Menu Posa rimosso - variabile eliminata\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n  };\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = path => {\n    return location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/_jsxDEV(AppBar, {\n    position: \"static\",\n    color: \"default\",\n    elevation: 2,\n    sx: {\n      zIndex: 1100,\n      width: '100%',\n      overflowX: 'hidden'\n    },\n    className: \"excel-style-menu\",\n    children: [/*#__PURE__*/_jsxDEV(Toolbar, {\n      sx: {\n        overflowX: 'hidden',\n        height: '60px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          marginRight: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Logo, {\n          width: 32,\n          height: 32\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontWeight: 700,\n            fontSize: '1.2rem',\n            marginLeft: '8px',\n            color: '#1976d2'\n          },\n          children: \"CABLYS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        color: \"inherit\",\n        onClick: () => isImpersonating ? handleLogout() : navigateTo('/dashboard'),\n        startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 22\n        }, this),\n        sx: {\n          mr: 1\n        },\n        className: isActive('/dashboard') ? 'active-button' : '',\n        children: isImpersonating ? \"Logout\" : (user === null || user === void 0 ? void 0 : user.role) === 'owner' ? \"Pannello Admin\" : (user === null || user === void 0 ? void 0 : user.role) === 'user' ? \"Lista Cantieri\" : (user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        orientation: \"vertical\",\n        flexItem: true,\n        sx: {\n          mx: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), ((user === null || user === void 0 ? void 0 : user.role) !== 'owner' || (user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [isImpersonating && /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          onClick: () => navigateTo('/dashboard/cantieri'),\n          sx: {\n            mr: 1\n          },\n          className: isActive('/dashboard/cantieri') ? 'active-button' : '',\n          children: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"Lista Cantieri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 15\n        }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [(user === null || user === void 0 ? void 0 : user.role) !== 'cantieri_user' && /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: () => navigateTo('/dashboard/cavi/visualizza'),\n            sx: {\n              mr: 1\n            },\n            className: isActive('/dashboard/cavi/visualizza') ? 'active-button' : '',\n            children: \"Visualizza Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"parco-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: () => navigateTo('/dashboard/cavi/parco/visualizza'),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/parco') ? 'active-button' : '',\n            children: \"Parco Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"excel-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setExcelAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/excel') ? 'active-button' : '',\n            children: \"Gestione Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: () => navigateTo(`/dashboard/cavi/${selectedCantiereId}/report`),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/report') ? 'active-button' : '',\n            children: \"Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: () => navigateTo('/dashboard/cavi/comande'),\n            sx: {\n              mr: 1\n            },\n            className: isActive('/dashboard/cavi/comande') ? 'active-button' : '',\n            children: \"Gestione Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: () => navigateTo('/dashboard/produttivita'),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/produttivita') ? 'active-button' : '',\n            children: \"\\u26A1 Produttivit\\xE0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"parco-menu\",\n            anchorEl: parcoAnchorEl,\n            keepMounted: true,\n            open: Boolean(parcoAnchorEl),\n            onClose: () => handleMenuClose(setParcoAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"excel-menu\",\n            anchorEl: excelAnchorEl,\n            keepMounted: true,\n            open: Boolean(excelAnchorEl),\n            onClose: () => handleMenuClose(setExcelAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleOpenExcelPopup('importaCavi'),\n              children: [/*#__PURE__*/_jsxDEV(FileUploadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 21\n              }, this), \"Importa Cavi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleOpenExcelPopup('importaParcoBobine'),\n              children: [/*#__PURE__*/_jsxDEV(FileUploadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 21\n              }, this), \"Importa Parco Bobine\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleCreateTemplateDirect('cavi'),\n              children: [/*#__PURE__*/_jsxDEV(FileDownloadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 21\n              }, this), \"Template Cavi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleCreateTemplateDirect('parco-bobine'),\n              children: [/*#__PURE__*/_jsxDEV(FileDownloadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 21\n              }, this), \"Template Parco Bobine\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleOpenExcelPopup('esportaCavi'),\n              children: [/*#__PURE__*/_jsxDEV(FileDownloadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 21\n              }, this), \"Esporta Cavi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleOpenExcelPopup('esportaParcoBobine'),\n              children: [/*#__PURE__*/_jsxDEV(FileDownloadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 21\n              }, this), \"Esporta Parco Bobine\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flexGrow: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          height: '100%',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(SelectedCantiereDisplay, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 0.5\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 500,\n              fontSize: '0.875rem'\n            },\n            children: isImpersonating && impersonatedUser ? impersonatedUser.username : (user === null || user === void 0 ? void 0 : user.username) || ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Logout\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            color: \"inherit\",\n            onClick: handleLogout,\n            edge: \"end\",\n            sx: {\n              '&:hover': {\n                backgroundColor: '#e9ecef'\n              },\n              padding: '6px'\n            },\n            children: /*#__PURE__*/_jsxDEV(LogoutIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ExcelPopup, {\n      open: excelPopupOpen,\n      onClose: handleCloseExcelPopup,\n      operationType: excelOperationType,\n      cantiereId: cantiereId,\n      onSuccess: handleExcelSuccess,\n      onError: handleExcelError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        variant: \"filled\",\n        sx: {\n          width: '100%'\n        },\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 442,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 206,\n    columnNumber: 5\n  }, this);\n};\n_s(TopNavbar, \"yhEqVWwWGuB1+y9RqvAXQUpZTC0=\", false, function () {\n  return [useNavigate, useLocation, useAuth, useGlobalContext];\n});\n_c = TopNavbar;\nexport default TopNavbar;\nvar _c;\n$RefreshReg$(_c, \"TopNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "AppBar", "<PERSON><PERSON><PERSON>", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Typography", "IconButton", "Divider", "Avatar", "<PERSON><PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "Logout", "LogoutIcon", "KeyboardArrowDown", "ArrowDownIcon", "FileUpload", "FileUploadIcon", "FileDownload", "FileDownloadIcon", "useAuth", "useGlobalContext", "SelectedCantiereDisplay", "ExcelPopup", "Logo", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TopNavbar", "_s", "navigate", "location", "user", "logout", "isImpersonating", "impersonated<PERSON><PERSON>", "setOpenEliminaCavoDialog", "setOpenModificaCavoDialog", "excelPopupOpen", "setExcelPopupOpen", "excelOperationType", "setExcelOperationType", "snackbar", "setSnackbar", "open", "message", "severity", "cantiereId", "parseInt", "localStorage", "getItem", "homeAnchorEl", "setHomeAnchorEl", "adminAnchorEl", "setAdminAnchorEl", "cantieriAnchorEl", "setCantieriAnchorEl", "caviAnchorEl", "setCaviAnchorEl", "parcoAnchorEl", "setParcoAnchorEl", "excelAnchorEl", "setExcelAnchorEl", "selectedCantiereId", "selectedCantiereName", "handleMenuOpen", "event", "setAnchorEl", "currentTarget", "handleMenuClose", "handleOpenExcelPopup", "operationType", "handleCreateTemplateDirect", "templateType", "excelService", "default", "createCaviTemplate", "createParcoBobineTemplate", "error", "console", "handleCloseExcelPopup", "handleExcelSuccess", "handleExcelError", "handleCloseSnackbar", "navigateTo", "path", "role", "handleLogout", "isActive", "pathname", "isPartOfActive", "startsWith", "position", "color", "elevation", "sx", "zIndex", "width", "overflowX", "className", "children", "height", "style", "display", "alignItems", "marginRight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontWeight", "fontSize", "marginLeft", "onClick", "startIcon", "mr", "orientation", "flexItem", "mx", "username", "e", "endIcon", "id", "anchorEl", "keepMounted", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "mt", "flexGrow", "gap", "variant", "title", "edge", "backgroundColor", "padding", "onSuccess", "onError", "autoHideDuration", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/TopNavbar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  AppBar,\n  Toolbar,\n  Box,\n  Button,\n  Menu,\n  MenuItem,\n  Typography,\n  IconButton,\n  Divider,\n  Avatar,\n  Tooltip,\n  Snackbar,\n  Alert\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon,\n  Logout as LogoutIcon,\n  KeyboardArrowDown as ArrowDownIcon,\n  FileUpload as FileUploadIcon,\n  FileDownload as FileDownloadIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { useGlobalContext } from '../context/GlobalContext';\nimport SelectedCantiereDisplay from './common/SelectedCantiereDisplay';\nimport ExcelPopup from './cavi/ExcelPopup';\nimport Logo from './Logo';\nimport './TopNavbar.css';\n\nconst TopNavbar = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, logout, isImpersonating, impersonatedUser } = useAuth();\n  const { setOpenEliminaCavoDialog, setOpenModificaCavoDialog } = useGlobalContext();\n\n  // Stato per il popup Excel\n  const [excelPopupOpen, setExcelPopupOpen] = useState(false);\n  const [excelOperationType, setExcelOperationType] = useState('');\n\n  // Stato per gli snackbar\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  // const [posaAnchorEl, setPosaAnchorEl] = useState(null); // OBSOLETO: Menu Posa rimosso\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n\n\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = (setAnchorEl) => {\n    setAnchorEl(null);\n  };\n\n  // Gestisce l'apertura del popup Excel\n  const handleOpenExcelPopup = (operationType) => {\n    setExcelOperationType(operationType);\n    setExcelPopupOpen(true);\n    handleMenuClose(setExcelAnchorEl);\n  };\n\n  // Gestisce la creazione diretta dei template senza popup\n  const handleCreateTemplateDirect = async (templateType) => {\n    try {\n      handleMenuClose(setExcelAnchorEl);\n\n      if (templateType === 'cavi') {\n        const excelService = await import('../services/excelService');\n        await excelService.default.createCaviTemplate();\n        setSnackbar({\n          open: true,\n          message: 'Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.',\n          severity: 'success'\n        });\n      } else if (templateType === 'parco-bobine') {\n        const excelService = await import('../services/excelService');\n        await excelService.default.createParcoBobineTemplate();\n        setSnackbar({\n          open: true,\n          message: 'Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.',\n          severity: 'success'\n        });\n      }\n    } catch (error) {\n      console.error(`Errore nella creazione del template ${templateType}:`, error);\n      setSnackbar({\n        open: true,\n        message: `Errore nella creazione del template ${templateType}: ${error.message || 'Errore sconosciuto'}`,\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la chiusura del popup Excel\n  const handleCloseExcelPopup = () => {\n    setExcelPopupOpen(false);\n  };\n\n  // Gestisce il successo delle operazioni Excel\n  const handleExcelSuccess = (message) => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'success'\n    });\n  };\n\n  // Gestisce gli errori delle operazioni Excel\n  const handleExcelError = (message) => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'error'\n    });\n  };\n\n  // Gestisce la chiusura dello snackbar\n  const handleCloseSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    // Gestione speciale per il percorso Home\n    if (path === '/dashboard') {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if (user?.role === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if (user?.role === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if (user?.role === 'cantieri_user') {\n        // Reindirizza direttamente alla pagina di visualizzazione cavi\n        navigate('/dashboard/cavi/visualizza');\n      }\n      // Fallback per altri tipi di utenti\n      else {\n        navigate(path);\n      }\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    // handleMenuClose(setPosaAnchorEl); // OBSOLETO: Menu Posa rimosso - variabile eliminata\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n\n\n  };\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  return (\n    <AppBar position=\"static\" color=\"default\" elevation={2} sx={{ zIndex: 1100, width: '100%', overflowX: 'hidden' }} className=\"excel-style-menu\">\n      <Toolbar sx={{ overflowX: 'hidden', height: '60px' }}>\n        {/* CABLYS Logo */}\n        <div style={{ display: 'flex', alignItems: 'center', marginRight: '16px' }}>\n          <Logo width={32} height={32} />\n          <Typography sx={{ fontWeight: 700, fontSize: '1.2rem', marginLeft: '8px', color: '#1976d2' }}>CABLYS</Typography>\n        </div>\n\n        {/* Home - Testo personalizzato in base al tipo di utente */}\n        <Button\n          color=\"inherit\"\n          onClick={() => isImpersonating ? handleLogout() : navigateTo('/dashboard')}\n          startIcon={<HomeIcon />}\n          sx={{ mr: 1 }}\n          className={isActive('/dashboard') ? 'active-button' : ''}\n        >\n          {isImpersonating ? \"Logout\" :\n           user?.role === 'owner' ? \"Pannello Admin\" :\n           user?.role === 'user' ? \"Lista Cantieri\" :\n           user?.role === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\"}\n        </Button>\n        <Divider orientation=\"vertical\" flexItem sx={{ mx: 0.5 }} />\n\n        {/* Il menu Amministratore è stato rimosso perché ridondante con il pulsante Home per gli amministratori */}\n\n        {/* Menu per utenti standard e cantieri */}\n        {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (\n          <>\n            {/* Pulsante Lista Cantieri solo per utenti che impersonano */}\n            {isImpersonating && (\n              <Button\n                color=\"inherit\"\n                onClick={() => navigateTo('/dashboard/cantieri')}\n                sx={{ mr: 1 }}\n                className={isActive('/dashboard/cantieri') ? 'active-button' : ''}\n              >\n                {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"Lista Cantieri\"}\n              </Button>\n            )}\n\n            {/* Il cantiere selezionato è stato spostato nella parte destra della barra di navigazione */}\n\n            {/* Menu di gestione cavi - visibile solo se un cantiere è selezionato */}\n            {selectedCantiereId && (\n              <>\n                {/* Visualizza Cavi - nascosto per utenti cantiere perché ridondante con il tasto Home */}\n                {user?.role !== 'cantieri_user' && (\n                  <Button\n                    color=\"inherit\"\n                    onClick={() => navigateTo('/dashboard/cavi/visualizza')}\n                    sx={{ mr: 1 }}\n                    className={isActive('/dashboard/cavi/visualizza') ? 'active-button' : ''}\n                  >\n                    Visualizza Cavi\n                  </Button>\n                )}\n\n                {/* OBSOLETO: Pulsante \"Posa e Collegamenti\" rimosso - Funzionalità integrate in \"Visualizza Cavi\" */}\n\n                {/* Parco Cavi */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"parco-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={() => navigateTo('/dashboard/cavi/parco/visualizza')}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/parco') ? 'active-button' : ''}\n                >\n                  Parco Cavi\n                </Button>\n\n                {/* Gestione Excel */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"excel-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setExcelAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/excel') ? 'active-button' : ''}\n                >\n                  Gestione Excel\n                </Button>\n\n                {/* Report */}\n                <Button\n                  color=\"inherit\"\n                  onClick={() => navigateTo(`/dashboard/cavi/${selectedCantiereId}/report`)}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/report') ? 'active-button' : ''}\n                >\n                  Report\n                </Button>\n\n\n\n                {/* Gestione Comande - semplificato */}\n                <Button\n                  color=\"inherit\"\n                  onClick={() => navigateTo('/dashboard/cavi/comande')}\n                  sx={{ mr: 1 }}\n                  className={isActive('/dashboard/cavi/comande') ? 'active-button' : ''}\n                >\n                  Gestione Comande\n                </Button>\n\n                {/* Sistema Produttività */}\n                <Button\n                  color=\"inherit\"\n                  onClick={() => navigateTo('/dashboard/produttivita')}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/produttivita') ? 'active-button' : ''}\n                >\n                  ⚡ Produttività\n                </Button>\n\n                {/* OBSOLETO: Menu \"Posa e Collegamenti\" rimosso - Funzionalità migrate ai popup nella pagina Visualizza Cavi */}\n\n                {/* Sottomenu Parco Cavi */}\n                <Menu\n                  id=\"parco-menu\"\n                  anchorEl={parcoAnchorEl}\n                  keepMounted\n                  open={Boolean(parcoAnchorEl)}\n                  onClose={() => handleMenuClose(setParcoAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n\n                </Menu>\n\n                {/* Sottomenu Gestione Excel */}\n                <Menu\n                  id=\"excel-menu\"\n                  anchorEl={excelAnchorEl}\n                  keepMounted\n                  open={Boolean(excelAnchorEl)}\n                  onClose={() => handleMenuClose(setExcelAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => handleOpenExcelPopup('importaCavi')}>\n                    <FileUploadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Importa Cavi\n                  </MenuItem>\n                  <MenuItem onClick={() => handleOpenExcelPopup('importaParcoBobine')}>\n                    <FileUploadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Importa Parco Bobine\n                  </MenuItem>\n                  <Divider />\n                  <MenuItem onClick={() => handleCreateTemplateDirect('cavi')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Template Cavi\n                  </MenuItem>\n                  <MenuItem onClick={() => handleCreateTemplateDirect('parco-bobine')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Template Parco Bobine\n                  </MenuItem>\n                  <Divider />\n                  <MenuItem onClick={() => handleOpenExcelPopup('esportaCavi')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Esporta Cavi\n                  </MenuItem>\n                  <MenuItem onClick={() => handleOpenExcelPopup('esportaParcoBobine')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Esporta Parco Bobine\n                  </MenuItem>\n                </Menu>\n\n\n\n\n\n\n              </>\n            )}\n          </>\n        )}\n\n        {/* Spacer */}\n        <Box sx={{ flexGrow: 1 }} />\n\n        {/* Informazioni utente e logout - Versione compatta */}\n        <Box sx={{ display: 'flex', alignItems: 'center', height: '100%', gap: 1 }}>\n          {/* Mostra il cantiere selezionato */}\n          <SelectedCantiereDisplay />\n\n          {/* Informazioni utente compatte */}\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n            <Typography variant=\"body2\" sx={{ fontWeight: 500, fontSize: '0.875rem' }}>\n              {isImpersonating && impersonatedUser ? impersonatedUser.username : (user?.username || '')}\n            </Typography>\n          </Box>\n\n          <Tooltip title=\"Logout\">\n            <IconButton\n              color=\"inherit\"\n              onClick={handleLogout}\n              edge=\"end\"\n              sx={{ '&:hover': { backgroundColor: '#e9ecef' }, padding: '6px' }}\n            >\n              <LogoutIcon fontSize=\"small\" />\n            </IconButton>\n          </Tooltip>\n        </Box>\n      </Toolbar>\n\n      {/* Excel Popup */}\n      <ExcelPopup\n        open={excelPopupOpen}\n        onClose={handleCloseExcelPopup}\n        operationType={excelOperationType}\n        cantiereId={cantiereId}\n        onSuccess={handleExcelSuccess}\n        onError={handleExcelError}\n      />\n\n      {/* Snackbar per messaggi di successo/errore */}\n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert\n          onClose={handleCloseSnackbar}\n          severity={snackbar.severity}\n          variant=\"filled\"\n          sx={{ width: '100%' }}\n        >\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n    </AppBar>\n  );\n};\n\nexport default TopNavbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,MAAM,IAAIC,UAAU,EACpBC,iBAAiB,IAAIC,aAAa,EAClCC,UAAU,IAAIC,cAAc,EAC5BC,YAAY,IAAIC,gBAAgB,QAC3B,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,OAAOC,uBAAuB,MAAM,kCAAkC;AACtE,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM6C,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6C,IAAI;IAAEC,MAAM;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGhB,OAAO,CAAC,CAAC;EACrE,MAAM;IAAEiB,wBAAwB;IAAEC;EAA0B,CAAC,GAAGjB,gBAAgB,CAAC,CAAC;;EAElF;EACA,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC;IACvC2D,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;;EAE3E;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoE,aAAa,EAAEC,gBAAgB,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACsE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EACtD;EACA,MAAM,CAAC0E,aAAa,EAAEC,gBAAgB,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC4E,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;;EAIxD;EACA,MAAM8E,kBAAkB,GAAGd,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACrE,MAAMc,oBAAoB,GAAGf,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEzE;EACA,MAAMe,cAAc,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;IAC7CA,WAAW,CAACD,KAAK,CAACE,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAIF,WAAW,IAAK;IACvCA,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,MAAMG,oBAAoB,GAAIC,aAAa,IAAK;IAC9C9B,qBAAqB,CAAC8B,aAAa,CAAC;IACpChC,iBAAiB,CAAC,IAAI,CAAC;IACvB8B,eAAe,CAACP,gBAAgB,CAAC;EACnC,CAAC;;EAED;EACA,MAAMU,0BAA0B,GAAG,MAAOC,YAAY,IAAK;IACzD,IAAI;MACFJ,eAAe,CAACP,gBAAgB,CAAC;MAEjC,IAAIW,YAAY,KAAK,MAAM,EAAE;QAC3B,MAAMC,YAAY,GAAG,MAAM,MAAM,CAAC,0BAA0B,CAAC;QAC7D,MAAMA,YAAY,CAACC,OAAO,CAACC,kBAAkB,CAAC,CAAC;QAC/CjC,WAAW,CAAC;UACVC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE,oGAAoG;UAC7GC,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI2B,YAAY,KAAK,cAAc,EAAE;QAC1C,MAAMC,YAAY,GAAG,MAAM,MAAM,CAAC,0BAA0B,CAAC;QAC7D,MAAMA,YAAY,CAACC,OAAO,CAACE,yBAAyB,CAAC,CAAC;QACtDlC,WAAW,CAAC;UACVC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE,4GAA4G;UACrHC,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuCL,YAAY,GAAG,EAAEK,KAAK,CAAC;MAC5EnC,WAAW,CAAC;QACVC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,uCAAuC4B,YAAY,KAAKK,KAAK,CAACjC,OAAO,IAAI,oBAAoB,EAAE;QACxGC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMkC,qBAAqB,GAAGA,CAAA,KAAM;IAClCzC,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM0C,kBAAkB,GAAIpC,OAAO,IAAK;IACtCF,WAAW,CAAC;MACVC,IAAI,EAAE,IAAI;MACVC,OAAO;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMoC,gBAAgB,GAAIrC,OAAO,IAAK;IACpCF,WAAW,CAAC;MACVC,IAAI,EAAE,IAAI;MACVC,OAAO;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMqC,mBAAmB,GAAGA,CAAA,KAAM;IAChCxC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMwC,UAAU,GAAIC,IAAI,IAAK;IAC3B;IACA,IAAIA,IAAI,KAAK,YAAY,EAAE;MACzB;MACA,IAAInD,eAAe,EAAE;QACnBJ,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,IAAI,MAAK,OAAO,EAAE;QAC/BxD,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,IAAI,MAAK,MAAM,EAAE;QAC9BxD,QAAQ,CAAC,qBAAqB,CAAC;MACjC;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,IAAI,MAAK,eAAe,EAAE;QACvC;QACAxD,QAAQ,CAAC,4BAA4B,CAAC;MACxC;MACA;MAAA,KACK;QACHA,QAAQ,CAACuD,IAAI,CAAC;MAChB;IACF,CAAC,MAAM;MACLvD,QAAQ,CAACuD,IAAI,CAAC;IAChB;;IAEA;IACAhB,eAAe,CAACjB,eAAe,CAAC;IAChCiB,eAAe,CAACf,gBAAgB,CAAC;IACjCe,eAAe,CAACb,mBAAmB,CAAC;IACpCa,eAAe,CAACX,eAAe,CAAC;IAChC;IACAW,eAAe,CAACT,gBAAgB,CAAC;IACjCS,eAAe,CAACP,gBAAgB,CAAC;EAGnC,CAAC;EAED,MAAMyB,YAAY,GAAGA,CAAA,KAAM;IACzBtD,MAAM,CAAC,CAAC;EACV,CAAC;;EAED;EACA,MAAMuD,QAAQ,GAAIH,IAAI,IAAK;IACzB,OAAOtD,QAAQ,CAAC0D,QAAQ,KAAKJ,IAAI;EACnC,CAAC;;EAED;EACA,MAAMK,cAAc,GAAIL,IAAI,IAAK;IAC/B,OAAOtD,QAAQ,CAAC0D,QAAQ,CAACE,UAAU,CAACN,IAAI,CAAC;EAC3C,CAAC;EAED,oBACE5D,OAAA,CAACrC,MAAM;IAACwG,QAAQ,EAAC,QAAQ;IAACC,KAAK,EAAC,SAAS;IAACC,SAAS,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,MAAM,EAAE,IAAI;MAAEC,KAAK,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAS,CAAE;IAACC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC5I3E,OAAA,CAACpC,OAAO;MAAC0G,EAAE,EAAE;QAAEG,SAAS,EAAE,QAAQ;QAAEG,MAAM,EAAE;MAAO,CAAE;MAAAD,QAAA,gBAEnD3E,OAAA;QAAK6E,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,WAAW,EAAE;QAAO,CAAE;QAAAL,QAAA,gBACzE3E,OAAA,CAACF,IAAI;UAAC0E,KAAK,EAAE,EAAG;UAACI,MAAM,EAAE;QAAG;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/BpF,OAAA,CAAC/B,UAAU;UAACqG,EAAE,EAAE;YAAEe,UAAU,EAAE,GAAG;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE,KAAK;YAAEnB,KAAK,EAAE;UAAU,CAAE;UAAAO,QAAA,EAAC;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9G,CAAC,eAGNpF,OAAA,CAAClC,MAAM;QACLsG,KAAK,EAAC,SAAS;QACfoB,OAAO,EAAEA,CAAA,KAAM/E,eAAe,GAAGqD,YAAY,CAAC,CAAC,GAAGH,UAAU,CAAC,YAAY,CAAE;QAC3E8B,SAAS,eAAEzF,OAAA,CAACvB,QAAQ;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBd,EAAE,EAAE;UAAEoB,EAAE,EAAE;QAAE,CAAE;QACdhB,SAAS,EAAEX,QAAQ,CAAC,YAAY,CAAC,GAAG,eAAe,GAAG,EAAG;QAAAY,QAAA,EAExDlE,eAAe,GAAG,QAAQ,GAC1B,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,IAAI,MAAK,OAAO,GAAG,gBAAgB,GACzC,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,IAAI,MAAK,MAAM,GAAG,gBAAgB,GACxC,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,IAAI,MAAK,eAAe,GAAG,eAAe,GAAG;MAAM;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACTpF,OAAA,CAAC7B,OAAO;QAACwH,WAAW,EAAC,UAAU;QAACC,QAAQ;QAACtB,EAAE,EAAE;UAAEuB,EAAE,EAAE;QAAI;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAK3D,CAAC,CAAA7E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,IAAI,MAAK,OAAO,IAAK,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,IAAI,MAAK,OAAO,IAAIpD,eAAe,IAAIC,gBAAiB,kBACzFV,OAAA,CAAAE,SAAA;QAAAyE,QAAA,GAEGlE,eAAe,iBACdT,OAAA,CAAClC,MAAM;UACLsG,KAAK,EAAC,SAAS;UACfoB,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAAC,qBAAqB,CAAE;UACjDW,EAAE,EAAE;YAAEoB,EAAE,EAAE;UAAE,CAAE;UACdhB,SAAS,EAAEX,QAAQ,CAAC,qBAAqB,CAAC,GAAG,eAAe,GAAG,EAAG;UAAAY,QAAA,EAEjElE,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAACoF,QAAQ,EAAE,GAAG;QAAgB;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CACT,EAKA9C,kBAAkB,iBACjBtC,OAAA,CAAAE,SAAA;UAAAyE,QAAA,GAEG,CAAApE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsD,IAAI,MAAK,eAAe,iBAC7B7D,OAAA,CAAClC,MAAM;YACLsG,KAAK,EAAC,SAAS;YACfoB,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAAC,4BAA4B,CAAE;YACxDW,EAAE,EAAE;cAAEoB,EAAE,EAAE;YAAE,CAAE;YACdhB,SAAS,EAAEX,QAAQ,CAAC,4BAA4B,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAY,QAAA,EAC1E;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eAKDpF,OAAA,CAAClC,MAAM;YACLsG,KAAK,EAAC,SAAS;YACf,iBAAc,YAAY;YAC1B,iBAAc,MAAM;YACpBoB,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAAC,kCAAkC,CAAE;YAC9DW,EAAE,EAAE;cAAEoB,EAAE,EAAE;YAAE,CAAE;YACdhB,SAAS,EAAET,cAAc,CAAC,uBAAuB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC3E;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGTpF,OAAA,CAAClC,MAAM;YACLsG,KAAK,EAAC,SAAS;YACf,iBAAc,YAAY;YAC1B,iBAAc,MAAM;YACpBoB,OAAO,EAAGO,CAAC,IAAKvD,cAAc,CAACuD,CAAC,EAAE1D,gBAAgB,CAAE;YACpD2D,OAAO,eAAEhG,OAAA,CAACX,aAAa;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3Bd,EAAE,EAAE;cAAEoB,EAAE,EAAE;YAAE,CAAE;YACdhB,SAAS,EAAET,cAAc,CAAC,uBAAuB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC3E;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGTpF,OAAA,CAAClC,MAAM;YACLsG,KAAK,EAAC,SAAS;YACfoB,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAAC,mBAAmBrB,kBAAkB,SAAS,CAAE;YAC1EgC,EAAE,EAAE;cAAEoB,EAAE,EAAE;YAAE,CAAE;YACdhB,SAAS,EAAET,cAAc,CAAC,wBAAwB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC5E;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAKTpF,OAAA,CAAClC,MAAM;YACLsG,KAAK,EAAC,SAAS;YACfoB,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAAC,yBAAyB,CAAE;YACrDW,EAAE,EAAE;cAAEoB,EAAE,EAAE;YAAE,CAAE;YACdhB,SAAS,EAAEX,QAAQ,CAAC,yBAAyB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAY,QAAA,EACvE;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGTpF,OAAA,CAAClC,MAAM;YACLsG,KAAK,EAAC,SAAS;YACfoB,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAAC,yBAAyB,CAAE;YACrDW,EAAE,EAAE;cAAEoB,EAAE,EAAE;YAAE,CAAE;YACdhB,SAAS,EAAET,cAAc,CAAC,yBAAyB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC7E;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAKTpF,OAAA,CAACjC,IAAI;YACHkI,EAAE,EAAC,YAAY;YACfC,QAAQ,EAAEhE,aAAc;YACxBiE,WAAW;YACXhF,IAAI,EAAEiF,OAAO,CAAClE,aAAa,CAAE;YAC7BmE,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAACT,gBAAgB,CAAE;YACjDmE,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACF9B,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAEoC,EAAE,EAAE;YAAI;UAAE;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGZ,CAAC,eAGPpF,OAAA,CAACjC,IAAI;YACHkI,EAAE,EAAC,YAAY;YACfC,QAAQ,EAAE9D,aAAc;YACxB+D,WAAW;YACXhF,IAAI,EAAEiF,OAAO,CAAChE,aAAa,CAAE;YAC7BiE,OAAO,EAAEA,CAAA,KAAMzD,eAAe,CAACP,gBAAgB,CAAE;YACjDiE,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACF9B,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAEoC,EAAE,EAAE;YAAI,CAAE;YAAA/B,QAAA,gBAEhB3E,OAAA,CAAChC,QAAQ;cAACwH,OAAO,EAAEA,CAAA,KAAM3C,oBAAoB,CAAC,aAAa,CAAE;cAAA8B,QAAA,gBAC3D3E,OAAA,CAACT,cAAc;gBAAC+F,QAAQ,EAAC,OAAO;gBAAChB,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXpF,OAAA,CAAChC,QAAQ;cAACwH,OAAO,EAAEA,CAAA,KAAM3C,oBAAoB,CAAC,oBAAoB,CAAE;cAAA8B,QAAA,gBAClE3E,OAAA,CAACT,cAAc;gBAAC+F,QAAQ,EAAC,OAAO;gBAAChB,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXpF,OAAA,CAAC7B,OAAO;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXpF,OAAA,CAAChC,QAAQ;cAACwH,OAAO,EAAEA,CAAA,KAAMzC,0BAA0B,CAAC,MAAM,CAAE;cAAA4B,QAAA,gBAC1D3E,OAAA,CAACP,gBAAgB;gBAAC6F,QAAQ,EAAC,OAAO;gBAAChB,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXpF,OAAA,CAAChC,QAAQ;cAACwH,OAAO,EAAEA,CAAA,KAAMzC,0BAA0B,CAAC,cAAc,CAAE;cAAA4B,QAAA,gBAClE3E,OAAA,CAACP,gBAAgB;gBAAC6F,QAAQ,EAAC,OAAO;gBAAChB,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,yBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXpF,OAAA,CAAC7B,OAAO;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXpF,OAAA,CAAChC,QAAQ;cAACwH,OAAO,EAAEA,CAAA,KAAM3C,oBAAoB,CAAC,aAAa,CAAE;cAAA8B,QAAA,gBAC3D3E,OAAA,CAACP,gBAAgB;gBAAC6F,QAAQ,EAAC,OAAO;gBAAChB,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXpF,OAAA,CAAChC,QAAQ;cAACwH,OAAO,EAAEA,CAAA,KAAM3C,oBAAoB,CAAC,oBAAoB,CAAE;cAAA8B,QAAA,gBAClE3E,OAAA,CAACP,gBAAgB;gBAAC6F,QAAQ,EAAC,OAAO;gBAAChB,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE;cAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA,eAOP,CACH;MAAA,eACD,CACH,eAGDpF,OAAA,CAACnC,GAAG;QAACyG,EAAE,EAAE;UAAEqC,QAAQ,EAAE;QAAE;MAAE;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG5BpF,OAAA,CAACnC,GAAG;QAACyG,EAAE,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEH,MAAM,EAAE,MAAM;UAAEgC,GAAG,EAAE;QAAE,CAAE;QAAAjC,QAAA,gBAEzE3E,OAAA,CAACJ,uBAAuB;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG3BpF,OAAA,CAACnC,GAAG;UAACyG,EAAE,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAE6B,GAAG,EAAE;UAAI,CAAE;UAAAjC,QAAA,eAC3D3E,OAAA,CAAC/B,UAAU;YAAC4I,OAAO,EAAC,OAAO;YAACvC,EAAE,EAAE;cAAEe,UAAU,EAAE,GAAG;cAAEC,QAAQ,EAAE;YAAW,CAAE;YAAAX,QAAA,EACvElE,eAAe,IAAIC,gBAAgB,GAAGA,gBAAgB,CAACoF,QAAQ,GAAI,CAAAvF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuF,QAAQ,KAAI;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENpF,OAAA,CAAC3B,OAAO;UAACyI,KAAK,EAAC,QAAQ;UAAAnC,QAAA,eACrB3E,OAAA,CAAC9B,UAAU;YACTkG,KAAK,EAAC,SAAS;YACfoB,OAAO,EAAE1B,YAAa;YACtBiD,IAAI,EAAC,KAAK;YACVzC,EAAE,EAAE;cAAE,SAAS,EAAE;gBAAE0C,eAAe,EAAE;cAAU,CAAC;cAAEC,OAAO,EAAE;YAAM,CAAE;YAAAtC,QAAA,eAElE3E,OAAA,CAACb,UAAU;cAACmG,QAAQ,EAAC;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVpF,OAAA,CAACH,UAAU;MACTsB,IAAI,EAAEN,cAAe;MACrBwF,OAAO,EAAE9C,qBAAsB;MAC/BT,aAAa,EAAE/B,kBAAmB;MAClCO,UAAU,EAAEA,UAAW;MACvB4F,SAAS,EAAE1D,kBAAmB;MAC9B2D,OAAO,EAAE1D;IAAiB;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAGFpF,OAAA,CAAC1B,QAAQ;MACP6C,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpBiG,gBAAgB,EAAE,IAAK;MACvBf,OAAO,EAAE3C,mBAAoB;MAC7B4C,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA7B,QAAA,eAE3D3E,OAAA,CAACzB,KAAK;QACJ8H,OAAO,EAAE3C,mBAAoB;QAC7BrC,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAC5BwF,OAAO,EAAC,QAAQ;QAChBvC,EAAE,EAAE;UAAEE,KAAK,EAAE;QAAO,CAAE;QAAAG,QAAA,EAErB1D,QAAQ,CAACG;MAAO;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEb,CAAC;AAAChF,EAAA,CAvaID,SAAS;EAAA,QACI1C,WAAW,EACXC,WAAW,EACgCgC,OAAO,EACHC,gBAAgB;AAAA;AAAA0H,EAAA,GAJ5ElH,SAAS;AAyaf,eAAeA,SAAS;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}