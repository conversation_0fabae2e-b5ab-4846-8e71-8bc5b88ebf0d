{"ast": null, "code": "import { formatDecimalParts } from \"./formatDecimal.js\";\nexport default function (x) {\n  return x = formatDecimalParts(Math.abs(x)), x ? x[1] : NaN;\n}", "map": {"version": 3, "names": ["formatDecimalParts", "x", "Math", "abs", "NaN"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/d3-format/src/exponent.js"], "sourcesContent": ["import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport default function(x) {\n  return x = formatDecimalParts(Math.abs(x)), x ? x[1] : NaN;\n}\n"], "mappings": "AAAA,SAAQA,kBAAkB,QAAO,oBAAoB;AAErD,eAAe,UAASC,CAAC,EAAE;EACzB,OAAOA,CAAC,GAAGD,kBAAkB,CAACE,IAAI,CAACC,GAAG,CAACF,CAAC,CAAC,CAAC,EAAEA,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGG,GAAG;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}