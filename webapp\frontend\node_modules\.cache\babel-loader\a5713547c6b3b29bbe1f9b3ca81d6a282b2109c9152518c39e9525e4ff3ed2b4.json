{"ast": null, "code": "import { quartersInYear } from \"./constants.js\";\n\n/**\n * @name quartersToYears\n * @category Conversion Helpers\n * @summary Convert number of quarters to years.\n *\n * @description\n * Convert a number of quarters to a full number of years.\n *\n * @param quarters - The number of quarters to be converted\n *\n * @returns The number of quarters converted in years\n *\n * @example\n * // Convert 8 quarters to years\n * const result = quartersToYears(8)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = quartersToYears(11)\n * //=> 2\n */\nexport function quartersToYears(quarters) {\n  const years = quarters / quartersInYear;\n  return Math.trunc(years);\n}\n\n// Fallback for modularized imports:\nexport default quartersToYears;", "map": {"version": 3, "names": ["quartersInYear", "quartersToYears", "quarters", "years", "Math", "trunc"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/quartersToYears.js"], "sourcesContent": ["import { quartersInYear } from \"./constants.js\";\n\n/**\n * @name quartersToYears\n * @category Conversion Helpers\n * @summary Convert number of quarters to years.\n *\n * @description\n * Convert a number of quarters to a full number of years.\n *\n * @param quarters - The number of quarters to be converted\n *\n * @returns The number of quarters converted in years\n *\n * @example\n * // Convert 8 quarters to years\n * const result = quartersToYears(8)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = quartersToYears(11)\n * //=> 2\n */\nexport function quartersToYears(quarters) {\n  const years = quarters / quartersInYear;\n  return Math.trunc(years);\n}\n\n// Fallback for modularized imports:\nexport default quartersToYears;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gBAAgB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,QAAQ,EAAE;EACxC,MAAMC,KAAK,GAAGD,QAAQ,GAAGF,cAAc;EACvC,OAAOI,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC;AAC1B;;AAEA;AACA,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}