{"ast": null, "code": "import { useFieldV7TextField } from \"./useFieldV7TextField.js\";\nimport { useFieldV6TextField } from \"./useFieldV6TextField.js\";\nimport { useNullableFieldPrivateContext } from \"../useNullableFieldPrivateContext.js\";\nexport const useField = parameters => {\n  const fieldPrivateContext = useNullableFieldPrivateContext();\n  const enableAccessibleFieldDOMStructure = parameters.props.enableAccessibleFieldDOMStructure ?? fieldPrivateContext?.enableAccessibleFieldDOMStructure ?? true;\n  const useFieldTextField = enableAccessibleFieldDOMStructure ? useFieldV7TextField : useFieldV6TextField;\n  return useFieldTextField(parameters);\n};", "map": {"version": 3, "names": ["useFieldV7TextField", "useFieldV6TextField", "useNullableFieldPrivateContext", "useField", "parameters", "fieldPrivateContext", "enableAccessibleFieldDOMStructure", "props", "useFieldTextField"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/hooks/useField/useField.js"], "sourcesContent": ["import { useFieldV7TextField } from \"./useFieldV7TextField.js\";\nimport { useFieldV6TextField } from \"./useFieldV6TextField.js\";\nimport { useNullableFieldPrivateContext } from \"../useNullableFieldPrivateContext.js\";\nexport const useField = parameters => {\n  const fieldPrivateContext = useNullableFieldPrivateContext();\n  const enableAccessibleFieldDOMStructure = parameters.props.enableAccessibleFieldDOMStructure ?? fieldPrivateContext?.enableAccessibleFieldDOMStructure ?? true;\n  const useFieldTextField = enableAccessibleFieldDOMStructure ? useFieldV7TextField : useFieldV6TextField;\n  return useFieldTextField(parameters);\n};"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,8BAA8B,QAAQ,sCAAsC;AACrF,OAAO,MAAMC,QAAQ,GAAGC,UAAU,IAAI;EACpC,MAAMC,mBAAmB,GAAGH,8BAA8B,CAAC,CAAC;EAC5D,MAAMI,iCAAiC,GAAGF,UAAU,CAACG,KAAK,CAACD,iCAAiC,IAAID,mBAAmB,EAAEC,iCAAiC,IAAI,IAAI;EAC9J,MAAME,iBAAiB,GAAGF,iCAAiC,GAAGN,mBAAmB,GAAGC,mBAAmB;EACvG,OAAOO,iBAAiB,CAACJ,UAAU,CAAC;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}