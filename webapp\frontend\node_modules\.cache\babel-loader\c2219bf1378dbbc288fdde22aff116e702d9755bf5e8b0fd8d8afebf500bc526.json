{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"BC\", \"AD\"],\n  abbreviated: [\"BC\", \"AD\"],\n  wide: [\"기원전\", \"서기\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1분기\", \"2분기\", \"3분기\", \"4분기\"]\n};\nconst monthValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"],\n  abbreviated: [\"1월\", \"2월\", \"3월\", \"4월\", \"5월\", \"6월\", \"7월\", \"8월\", \"9월\", \"10월\", \"11월\", \"12월\"],\n  wide: [\"1월\", \"2월\", \"3월\", \"4월\", \"5월\", \"6월\", \"7월\", \"8월\", \"9월\", \"10월\", \"11월\", \"12월\"]\n};\nconst dayValues = {\n  narrow: [\"일\", \"월\", \"화\", \"수\", \"목\", \"금\", \"토\"],\n  short: [\"일\", \"월\", \"화\", \"수\", \"목\", \"금\", \"토\"],\n  abbreviated: [\"일\", \"월\", \"화\", \"수\", \"목\", \"금\", \"토\"],\n  wide: [\"일요일\", \"월요일\", \"화요일\", \"수요일\", \"목요일\", \"금요일\", \"토요일\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\"\n  },\n  abbreviated: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\"\n  },\n  wide: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\"\n  },\n  abbreviated: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\"\n  },\n  wide: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = String(options?.unit);\n  switch (unit) {\n    case \"minute\":\n    case \"second\":\n      return String(number);\n    case \"date\":\n      return number + \"일\";\n    default:\n      return number + \"번째\";\n  }\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/ko/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"BC\", \"AD\"],\n  abbreviated: [\"BC\", \"AD\"],\n  wide: [\"기원전\", \"서기\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1분기\", \"2분기\", \"3분기\", \"4분기\"],\n};\n\nconst monthValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"],\n\n  abbreviated: [\n    \"1월\",\n    \"2월\",\n    \"3월\",\n    \"4월\",\n    \"5월\",\n    \"6월\",\n    \"7월\",\n    \"8월\",\n    \"9월\",\n    \"10월\",\n    \"11월\",\n    \"12월\",\n  ],\n\n  wide: [\n    \"1월\",\n    \"2월\",\n    \"3월\",\n    \"4월\",\n    \"5월\",\n    \"6월\",\n    \"7월\",\n    \"8월\",\n    \"9월\",\n    \"10월\",\n    \"11월\",\n    \"12월\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"일\", \"월\", \"화\", \"수\", \"목\", \"금\", \"토\"],\n  short: [\"일\", \"월\", \"화\", \"수\", \"목\", \"금\", \"토\"],\n  abbreviated: [\"일\", \"월\", \"화\", \"수\", \"목\", \"금\", \"토\"],\n  wide: [\"일요일\", \"월요일\", \"화요일\", \"수요일\", \"목요일\", \"금요일\", \"토요일\"],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\",\n  },\n  abbreviated: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\",\n  },\n  wide: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\",\n  },\n  abbreviated: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\",\n  },\n  wide: {\n    am: \"오전\",\n    pm: \"오후\",\n    midnight: \"자정\",\n    noon: \"정오\",\n    morning: \"아침\",\n    afternoon: \"오후\",\n    evening: \"저녁\",\n    night: \"밤\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = String(options?.unit);\n\n  switch (unit) {\n    case \"minute\":\n    case \"second\":\n      return String(number);\n    case \"date\":\n      return number + \"일\";\n    default:\n      return number + \"번째\";\n  }\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACpBC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACzBC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI;AACpB,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AACnC,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEvEC,WAAW,EAAE,CACX,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK;AAET,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1CL,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAChDC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AACxD,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC9C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,MAAMI,IAAI,GAAGC,MAAM,CAACJ,OAAO,EAAEG,IAAI,CAAC;EAElC,QAAQA,IAAI;IACV,KAAK,QAAQ;IACb,KAAK,QAAQ;MACX,OAAOC,MAAM,CAACH,MAAM,CAAC;IACvB,KAAK,MAAM;MACT,OAAOA,MAAM,GAAG,GAAG;IACrB;MACE,OAAOA,MAAM,GAAG,IAAI;EACxB;AACF,CAAC;AAED,OAAO,MAAMI,QAAQ,GAAG;EACtBP,aAAa;EAEbQ,GAAG,EAAE3B,eAAe,CAAC;IACnB4B,MAAM,EAAE3B,SAAS;IACjB4B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE9B,eAAe,CAAC;IACvB4B,MAAM,EAAEvB,aAAa;IACrBwB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAEhC,eAAe,CAAC;IACrB4B,MAAM,EAAEtB,WAAW;IACnBuB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAEjC,eAAe,CAAC;IACnB4B,MAAM,EAAErB,SAAS;IACjBsB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAElC,eAAe,CAAC;IACzB4B,MAAM,EAAEnB,eAAe;IACvBoB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEjB,yBAAyB;IAC3CkB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}