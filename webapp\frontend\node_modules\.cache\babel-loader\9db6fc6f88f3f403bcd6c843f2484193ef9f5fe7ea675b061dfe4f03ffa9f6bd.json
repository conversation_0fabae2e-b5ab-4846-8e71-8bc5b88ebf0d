{"ast": null, "code": "'use client';\n\nimport useLazyRef from \"../useLazyRef/useLazyRef.js\";\nimport useOnMount from \"../useOnMount/useOnMount.js\";\nexport class Timeout {\n  static create() {\n    return new Timeout();\n  }\n  currentId = null;\n\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n  clear = () => {\n    if (this.currentId !== null) {\n      clearTimeout(this.currentId);\n      this.currentId = null;\n    }\n  };\n  disposeEffect = () => {\n    return this.clear;\n  };\n}\nexport default function useTimeout() {\n  const timeout = useLazyRef(Timeout.create).current;\n  useOnMount(timeout.disposeEffect);\n  return timeout;\n}", "map": {"version": 3, "names": ["useLazyRef", "useOnMount", "Timeout", "create", "currentId", "start", "delay", "fn", "clear", "setTimeout", "clearTimeout", "disposeEffect", "useTimeout", "timeout", "current"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/node_modules/@mui/utils/esm/useTimeout/useTimeout.js"], "sourcesContent": ["'use client';\n\nimport useLazyRef from \"../useLazyRef/useLazyRef.js\";\nimport useOnMount from \"../useOnMount/useOnMount.js\";\nexport class Timeout {\n  static create() {\n    return new Timeout();\n  }\n  currentId = null;\n\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n  clear = () => {\n    if (this.currentId !== null) {\n      clearTimeout(this.currentId);\n      this.currentId = null;\n    }\n  };\n  disposeEffect = () => {\n    return this.clear;\n  };\n}\nexport default function useTimeout() {\n  const timeout = useLazyRef(Timeout.create).current;\n  useOnMount(timeout.disposeEffect);\n  return timeout;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,6BAA6B;AACpD,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAO,MAAMC,OAAO,CAAC;EACnB,OAAOC,MAAMA,CAAA,EAAG;IACd,OAAO,IAAID,OAAO,CAAC,CAAC;EACtB;EACAE,SAAS,GAAG,IAAI;;EAEhB;AACF;AACA;EACEC,KAAKA,CAACC,KAAK,EAAEC,EAAE,EAAE;IACf,IAAI,CAACC,KAAK,CAAC,CAAC;IACZ,IAAI,CAACJ,SAAS,GAAGK,UAAU,CAAC,MAAM;MAChC,IAAI,CAACL,SAAS,GAAG,IAAI;MACrBG,EAAE,CAAC,CAAC;IACN,CAAC,EAAED,KAAK,CAAC;EACX;EACAE,KAAK,GAAGA,CAAA,KAAM;IACZ,IAAI,IAAI,CAACJ,SAAS,KAAK,IAAI,EAAE;MAC3BM,YAAY,CAAC,IAAI,CAACN,SAAS,CAAC;MAC5B,IAAI,CAACA,SAAS,GAAG,IAAI;IACvB;EACF,CAAC;EACDO,aAAa,GAAGA,CAAA,KAAM;IACpB,OAAO,IAAI,CAACH,KAAK;EACnB,CAAC;AACH;AACA,eAAe,SAASI,UAAUA,CAAA,EAAG;EACnC,MAAMC,OAAO,GAAGb,UAAU,CAACE,OAAO,CAACC,MAAM,CAAC,CAACW,OAAO;EAClDb,UAAU,CAACY,OAAO,CAACF,aAAa,CAAC;EACjC,OAAOE,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}