{"ast": null, "code": "import { constructNow } from \"./constructNow.mjs\";\nimport { isSameYear } from \"./isSameYear.mjs\";\n\n/**\n * @name isThisYear\n * @category Year Helpers\n * @summary Is the given date in the same year as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same year as the current date?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is in this year\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this year?\n * const result = isThisYear(new Date(2014, 6, 2))\n * //=> true\n */\nexport function isThisYear(date) {\n  return isSameYear(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisYear;", "map": {"version": 3, "names": ["constructNow", "isSameYear", "isThisYear", "date"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/isThisYear.mjs"], "sourcesContent": ["import { constructNow } from \"./constructNow.mjs\";\nimport { isSameYear } from \"./isSameYear.mjs\";\n\n/**\n * @name isThisYear\n * @category Year Helpers\n * @summary Is the given date in the same year as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same year as the current date?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is in this year\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this year?\n * const result = isThisYear(new Date(2014, 6, 2))\n * //=> true\n */\nexport function isThisYear(date) {\n  return isSameYear(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisYear;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,UAAU,QAAQ,kBAAkB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAOF,UAAU,CAACE,IAAI,EAAEH,YAAY,CAACG,IAAI,CAAC,CAAC;AAC7C;;AAEA;AACA,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}