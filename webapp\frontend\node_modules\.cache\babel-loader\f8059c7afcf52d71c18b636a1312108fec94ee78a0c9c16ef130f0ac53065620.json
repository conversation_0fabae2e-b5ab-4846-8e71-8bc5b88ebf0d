{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\nconst API_URL = config.API_URL;\nconst cantieriService = {\n  // Ottiene la lista di tutti i cantieri dell'utente corrente\n  getMyCantieri: async () => {\n    try {\n      const response = await axiosInstance.get('/cantieri');\n      return response.data;\n    } catch (error) {\n      console.error('Get cantieri error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea un nuovo cantiere\n  createCantiere: async cantiereData => {\n    try {\n      const response = await axiosInstance.post('/cantieri', cantiereData);\n      return response.data;\n    } catch (error) {\n      console.error('Create cantiere error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un cantiere\n  deleteCantiere: async cantiereId => {\n    try {\n      const response = await axiosInstance.delete(`/cantieri/${cantiereId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cantiere error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i dettagli di un cantiere specifico\n  getCantiere: async cantiereId => {\n    try {\n      const response = await axiosInstance.get(`/cantieri/${cantiereId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cantiere error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i cantieri di un utente specifico (per amministratori che impersonano utenti)\n  getUserCantieri: async userId => {\n    try {\n      const response = await axiosInstance.get(`/cantieri/user/${userId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get user cantieri error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default cantieriService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "cantieriService", "getMyCantieri", "response", "get", "data", "error", "console", "createCantiere", "cantiereData", "post", "deleteCantiere", "cantiereId", "delete", "getCantiere", "getUserCantieri", "userId"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/cantieriService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\nconst cantieriService = {\r\n  // Ottiene la lista di tutti i cantieri dell'utente corrente\r\n  getMyCantieri: async () => {\r\n    try {\r\n      const response = await axiosInstance.get('/cantieri');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get cantieri error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea un nuovo cantiere\r\n  createCantiere: async (cantiereData) => {\r\n    try {\r\n      const response = await axiosInstance.post('/cantieri', cantiereData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Create cantiere error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Elimina un cantiere\r\n  deleteCantiere: async (cantiereId) => {\r\n    try {\r\n      const response = await axiosInstance.delete(`/cantieri/${cantiereId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Delete cantiere error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene i dettagli di un cantiere specifico\r\n  getCantiere: async (cantiereId) => {\r\n    try {\r\n      const response = await axiosInstance.get(`/cantieri/${cantiereId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get cantiere error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene i cantieri di un utente specifico (per amministratori che impersonano utenti)\r\n  getUserCantieri: async (userId) => {\r\n    try {\r\n      const response = await axiosInstance.get(`/cantieri/user/${userId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get user cantieri error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default cantieriService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;AAEzC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;AAE9B,MAAMC,eAAe,GAAG;EACtB;EACAC,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,GAAG,CAAC,WAAW,CAAC;MACrD,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAE,cAAc,EAAE,MAAOC,YAAY,IAAK;IACtC,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMJ,aAAa,CAACW,IAAI,CAAC,WAAW,EAAED,YAAY,CAAC;MACpE,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAK,cAAc,EAAE,MAAOC,UAAU,IAAK;IACpC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMJ,aAAa,CAACc,MAAM,CAAC,aAAaD,UAAU,EAAE,CAAC;MACtE,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAQ,WAAW,EAAE,MAAOF,UAAU,IAAK;IACjC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMJ,aAAa,CAACK,GAAG,CAAC,aAAaQ,UAAU,EAAE,CAAC;MACnE,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAS,eAAe,EAAE,MAAOC,MAAM,IAAK;IACjC,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMJ,aAAa,CAACK,GAAG,CAAC,kBAAkBY,MAAM,EAAE,CAAC;MACpE,OAAOb,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}