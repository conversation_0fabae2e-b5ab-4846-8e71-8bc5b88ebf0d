{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Gestione degli errori di autenticazione\naxiosInstance.interceptors.response.use(response => response, error => {\n  console.error('Errore nella risposta API:', error);\n  if (error.response && error.response.status === 401) {\n    // Se la risposta è 401 Unauthorized, effettua il logout\n    console.log('Errore 401 rilevato, rimozione token');\n    localStorage.removeItem('token');\n\n    // Non reindirizzare automaticamente, lascia che sia il componente React a gestire il reindirizzamento\n    // Questo evita loop di reindirizzamento\n  }\n  return Promise.reject(error);\n});\nconst authService = {\n  // Login standard (admin o utente standard)\n  login: async (credentials, loginType) => {\n    try {\n      console.log(`Tentativo di login ${loginType} con API_URL: ${API_URL}`);\n      if (loginType === 'standard') {\n        // Converti le credenziali nel formato richiesto da OAuth2\n        const formData = new FormData();\n        formData.append('username', credentials.username);\n        formData.append('password', credentials.password);\n        console.log(`Invio richiesta POST a ${API_URL}/auth/login`);\n        // Usa axios direttamente per il login perché richiede FormData\n        const response = await axios.post(`${API_URL}/auth/login`, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        console.log('Risposta ricevuta:', response);\n        return response.data;\n      } else if (loginType === 'cantiere') {\n        // Login cantiere\n        console.log(`Invio richiesta POST a ${API_URL}/auth/login/cantiere`);\n        const response = await axiosInstance.post('/auth/login/cantiere', {\n          codice_univoco: credentials.codice_univoco,\n          password: credentials.password\n        });\n        console.log('Risposta ricevuta:', response);\n        return response.data;\n      } else {\n        throw new Error('Tipo di login non valido');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      if (error.response) {\n        console.error('Dettagli errore:', error.response.status, error.response.data);\n        throw error.response.data;\n      } else if (error.request) {\n        console.error('Nessuna risposta ricevuta:', error.request);\n        throw {\n          detail: 'Errore di connessione al server. Verifica che il backend sia in esecuzione.'\n        };\n      } else {\n        console.error('Errore durante la configurazione della richiesta:', error.message);\n        throw {\n          detail: error.message\n        };\n      }\n    }\n  },\n  // Verifica la validità del token\n  checkToken: async () => {\n    try {\n      console.log('Verifica token in corso...');\n      const response = await axiosInstance.post('/auth/test-token');\n      console.log('Risposta verifica token:', response.data);\n      // Controlla se l'utente è impersonato da un admin\n      const isImpersonated = localStorage.getItem('isImpersonating') === 'true';\n      return {\n        id: response.data.user_id,\n        username: response.data.username,\n        role: response.data.role,\n        isImpersonated: isImpersonated\n      };\n    } catch (error) {\n      console.error('Check token error:', error);\n      // Pulisci il localStorage per evitare loop\n      localStorage.removeItem('token');\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Impersona un altro utente (solo per admin)\n  impersonateUser: async userId => {\n    try {\n      const response = await axiosInstance.post('/auth/impersonate', {\n        user_id: userId\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Impersonate user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "console", "status", "log", "removeItem", "authService", "login", "credentials", "loginType", "formData", "FormData", "append", "username", "password", "post", "data", "codice_univoco", "Error", "detail", "message", "checkToken", "isImpersonated", "id", "user_id", "role", "impersonate<PERSON><PERSON>", "userId"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/authService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Gestione degli errori di autenticazione\naxiosInstance.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    console.error('Errore nella risposta API:', error);\n    if (error.response && error.response.status === 401) {\n      // Se la risposta è 401 Unauthorized, effettua il logout\n      console.log('Errore 401 rilevato, rimozione token');\n      localStorage.removeItem('token');\n\n      // Non reindirizzare automaticamente, lascia che sia il componente React a gestire il reindirizzamento\n      // Questo evita loop di reindirizzamento\n    }\n    return Promise.reject(error);\n  }\n);\n\nconst authService = {\n  // Login standard (admin o utente standard)\n  login: async (credentials, loginType) => {\n    try {\n      console.log(`Tentativo di login ${loginType} con API_URL: ${API_URL}`);\n\n      if (loginType === 'standard') {\n        // Converti le credenziali nel formato richiesto da OAuth2\n        const formData = new FormData();\n        formData.append('username', credentials.username);\n        formData.append('password', credentials.password);\n\n        console.log(`Invio richiesta POST a ${API_URL}/auth/login`);\n        // Usa axios direttamente per il login perché richiede FormData\n        const response = await axios.post(`${API_URL}/auth/login`, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data',\n          },\n        });\n        console.log('Risposta ricevuta:', response);\n        return response.data;\n      } else if (loginType === 'cantiere') {\n        // Login cantiere\n        console.log(`Invio richiesta POST a ${API_URL}/auth/login/cantiere`);\n        const response = await axiosInstance.post('/auth/login/cantiere', {\n          codice_univoco: credentials.codice_univoco,\n          password: credentials.password\n        });\n        console.log('Risposta ricevuta:', response);\n        return response.data;\n      } else {\n        throw new Error('Tipo di login non valido');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      if (error.response) {\n        console.error('Dettagli errore:', error.response.status, error.response.data);\n        throw error.response.data;\n      } else if (error.request) {\n        console.error('Nessuna risposta ricevuta:', error.request);\n        throw { detail: 'Errore di connessione al server. Verifica che il backend sia in esecuzione.' };\n      } else {\n        console.error('Errore durante la configurazione della richiesta:', error.message);\n        throw { detail: error.message };\n      }\n    }\n  },\n\n\n\n  // Verifica la validità del token\n  checkToken: async () => {\n    try {\n      console.log('Verifica token in corso...');\n      const response = await axiosInstance.post('/auth/test-token');\n      console.log('Risposta verifica token:', response.data);\n      // Controlla se l'utente è impersonato da un admin\n      const isImpersonated = localStorage.getItem('isImpersonating') === 'true';\n\n      return {\n        id: response.data.user_id,\n        username: response.data.username,\n        role: response.data.role,\n        isImpersonated: isImpersonated\n      };\n    } catch (error) {\n      console.error('Check token error:', error);\n      // Pulisci il localStorage per evitare loop\n      localStorage.removeItem('token');\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Impersona un altro utente (solo per admin)\n  impersonateUser: async (userId) => {\n    try {\n      const response = await axiosInstance.post('/auth/impersonate', {\n        user_id: userId\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Impersonate user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default authService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,aAAa,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CACpCS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EACTI,OAAO,CAACJ,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;EAClD,IAAIA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;IACnD;IACAD,OAAO,CAACE,GAAG,CAAC,sCAAsC,CAAC;IACnDT,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;;IAEhC;IACA;EACF;EACA,OAAON,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMQ,WAAW,GAAG;EAClB;EACAC,KAAK,EAAE,MAAAA,CAAOC,WAAW,EAAEC,SAAS,KAAK;IACvC,IAAI;MACFP,OAAO,CAACE,GAAG,CAAC,sBAAsBK,SAAS,iBAAiBxB,OAAO,EAAE,CAAC;MAEtE,IAAIwB,SAAS,KAAK,UAAU,EAAE;QAC5B;QACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,WAAW,CAACK,QAAQ,CAAC;QACjDH,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,WAAW,CAACM,QAAQ,CAAC;QAEjDZ,OAAO,CAACE,GAAG,CAAC,0BAA0BnB,OAAO,aAAa,CAAC;QAC3D;QACA,MAAMgB,QAAQ,GAAG,MAAMjB,KAAK,CAAC+B,IAAI,CAAC,GAAG9B,OAAO,aAAa,EAAEyB,QAAQ,EAAE;UACnErB,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QACFa,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAEH,QAAQ,CAAC;QAC3C,OAAOA,QAAQ,CAACe,IAAI;MACtB,CAAC,MAAM,IAAIP,SAAS,KAAK,UAAU,EAAE;QACnC;QACAP,OAAO,CAACE,GAAG,CAAC,0BAA0BnB,OAAO,sBAAsB,CAAC;QACpE,MAAMgB,QAAQ,GAAG,MAAMf,aAAa,CAAC6B,IAAI,CAAC,sBAAsB,EAAE;UAChEE,cAAc,EAAET,WAAW,CAACS,cAAc;UAC1CH,QAAQ,EAAEN,WAAW,CAACM;QACxB,CAAC,CAAC;QACFZ,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAEH,QAAQ,CAAC;QAC3C,OAAOA,QAAQ,CAACe,IAAI;MACtB,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAAC,0BAA0B,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,IAAIA,KAAK,CAACG,QAAQ,EAAE;QAClBC,OAAO,CAACJ,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAACG,QAAQ,CAACE,MAAM,EAAEL,KAAK,CAACG,QAAQ,CAACe,IAAI,CAAC;QAC7E,MAAMlB,KAAK,CAACG,QAAQ,CAACe,IAAI;MAC3B,CAAC,MAAM,IAAIlB,KAAK,CAACP,OAAO,EAAE;QACxBW,OAAO,CAACJ,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAACP,OAAO,CAAC;QAC1D,MAAM;UAAE4B,MAAM,EAAE;QAA8E,CAAC;MACjG,CAAC,MAAM;QACLjB,OAAO,CAACJ,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAACsB,OAAO,CAAC;QACjF,MAAM;UAAED,MAAM,EAAErB,KAAK,CAACsB;QAAQ,CAAC;MACjC;IACF;EACF,CAAC;EAID;EACAC,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,IAAI;MACFnB,OAAO,CAACE,GAAG,CAAC,4BAA4B,CAAC;MACzC,MAAMH,QAAQ,GAAG,MAAMf,aAAa,CAAC6B,IAAI,CAAC,kBAAkB,CAAC;MAC7Db,OAAO,CAACE,GAAG,CAAC,0BAA0B,EAAEH,QAAQ,CAACe,IAAI,CAAC;MACtD;MACA,MAAMM,cAAc,GAAG3B,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,KAAK,MAAM;MAEzE,OAAO;QACL2B,EAAE,EAAEtB,QAAQ,CAACe,IAAI,CAACQ,OAAO;QACzBX,QAAQ,EAAEZ,QAAQ,CAACe,IAAI,CAACH,QAAQ;QAChCY,IAAI,EAAExB,QAAQ,CAACe,IAAI,CAACS,IAAI;QACxBH,cAAc,EAAEA;MAClB,CAAC;IACH,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C;MACAH,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;MAChC,MAAMP,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACe,IAAI,GAAGlB,KAAK;IACpD;EACF,CAAC;EAED;EACA4B,eAAe,EAAE,MAAOC,MAAM,IAAK;IACjC,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMf,aAAa,CAAC6B,IAAI,CAAC,mBAAmB,EAAE;QAC7DS,OAAO,EAAEG;MACX,CAAC,CAAC;MACF,OAAO1B,QAAQ,CAACe,IAAI;IACtB,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACe,IAAI,GAAGlB,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}