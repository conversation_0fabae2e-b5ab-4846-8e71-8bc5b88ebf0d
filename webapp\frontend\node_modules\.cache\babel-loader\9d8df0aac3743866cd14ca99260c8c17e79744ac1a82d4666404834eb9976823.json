{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Paper,Typography,TextField,Button,Box,Grid,FormControl,InputLabel,Select,MenuItem,Autocomplete,Alert,Divider}from'@mui/material';import{Save as SaveIcon,Cancel as CancelIcon}from'@mui/icons-material';import{apiService}from'../../services/apiService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function CertificazioneForm(_ref){let{cantiereId,certificazione,strumenti,onSuccess,onCancel}=_ref;const[formData,setFormData]=useState({id_cavo:'',id_operatore:'',strumento_utilizzato:'',id_strumento:'',lunghezza_misurata:'',valore_continuita:'OK',valore_isolamento:'500',valore_resistenza:'OK',note:''});const[cavi,setCavi]=useState([]);const[selectedCavo,setSelectedCavo]=useState(null);const[loading,setLoading]=useState(false);const[error,setError]=useState('');useEffect(()=>{loadCavi();if(certificazione){setFormData({id_cavo:certificazione.id_cavo||'',id_operatore:certificazione.id_operatore||'',strumento_utilizzato:certificazione.strumento_utilizzato||'',id_strumento:certificazione.id_strumento||'',lunghezza_misurata:certificazione.lunghezza_misurata||'',valore_continuita:certificazione.valore_continuita||'OK',valore_isolamento:certificazione.valore_isolamento||'500',valore_resistenza:certificazione.valore_resistenza||'OK',note:certificazione.note||''});}},[certificazione,cantiereId]);const loadCavi=async()=>{try{// Carica solo i cavi installati che non hanno già una certificazione\nconst caviData=await apiService.getCavi(cantiereId);const caviInstallati=caviData.filter(cavo=>cavo.stato_installazione==='INSTALLATO');setCavi(caviInstallati);// Se stiamo modificando, trova il cavo selezionato\nif(certificazione){const cavo=caviInstallati.find(c=>c.id_cavo===certificazione.id_cavo);setSelectedCavo(cavo);}}catch(err){console.error('Errore nel caricamento dei cavi:',err);setError('Errore nel caricamento dei cavi');}};const handleInputChange=(field,value)=>{setFormData(prev=>({...prev,[field]:value}));};const handleCavoChange=(event,newValue)=>{setSelectedCavo(newValue);if(newValue){setFormData(prev=>({...prev,id_cavo:newValue.id_cavo,lunghezza_misurata:newValue.metratura_reale||''}));}else{setFormData(prev=>({...prev,id_cavo:'',lunghezza_misurata:''}));}};const handleStrumentoChange=event=>{const strumentoId=event.target.value;handleInputChange('id_strumento',strumentoId);if(strumentoId){const strumento=strumenti.find(s=>s.id_strumento===strumentoId);if(strumento){handleInputChange('strumento_utilizzato',`${strumento.nome} ${strumento.marca} ${strumento.modello}`);}}else{handleInputChange('strumento_utilizzato','');}};const handleSubmit=async event=>{event.preventDefault();if(!formData.id_cavo){setError('Seleziona un cavo da certificare');return;}try{setLoading(true);setError('');const submitData={...formData,id_strumento:formData.id_strumento||null,lunghezza_misurata:formData.lunghezza_misurata?parseFloat(formData.lunghezza_misurata):null};if(certificazione){await apiService.updateCertificazione(cantiereId,certificazione.id_certificazione,submitData);onSuccess('Certificazione aggiornata con successo');}else{await apiService.createCertificazione(cantiereId,submitData);onSuccess('Certificazione creata con successo');}}catch(err){var _err$response,_err$response$data;console.error('Errore nel salvataggio:',err);setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.detail)||'Errore nel salvataggio della certificazione');}finally{setLoading(false);}};return/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:certificazione?'Modifica Certificazione':'Nuova Certificazione'}),error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:error}),/*#__PURE__*/_jsx(\"form\",{onSubmit:handleSubmit,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",gutterBottom:true,children:\"Selezione Cavo\"}),/*#__PURE__*/_jsx(Autocomplete,{value:selectedCavo,onChange:handleCavoChange,options:cavi,getOptionLabel:option=>`${option.id_cavo} - ${option.tipologia||''} ${option.sezione||''}`,renderInput:params=>/*#__PURE__*/_jsx(TextField,{...params,label:\"Cavo da certificare\",required:true,fullWidth:true}),disabled:!!certificazione// Non modificabile se stiamo editando\n}),selectedCavo&&/*#__PURE__*/_jsx(Box,{sx:{mt:1,p:2,bgcolor:'grey.50',borderRadius:1},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Partenza:\"}),\" \",selectedCavo.ubicazione_partenza||'-',\" |\",/*#__PURE__*/_jsx(\"strong\",{children:\" Arrivo:\"}),\" \",selectedCavo.ubicazione_arrivo||'-',\" |\",/*#__PURE__*/_jsx(\"strong\",{children:\" Metri Teorici:\"}),\" \",selectedCavo.metri_teorici||'-',\" |\",/*#__PURE__*/_jsx(\"strong\",{children:\" Metri Reali:\"}),\" \",selectedCavo.metratura_reale||'-']})})]}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Divider,{})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(TextField,{label:\"Operatore\",value:formData.id_operatore,onChange:e=>handleInputChange('id_operatore',e.target.value),fullWidth:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,children:[/*#__PURE__*/_jsx(InputLabel,{children:\"Strumento Certificato\"}),/*#__PURE__*/_jsxs(Select,{value:formData.id_strumento,onChange:handleStrumentoChange,label:\"Strumento Certificato\",children:[/*#__PURE__*/_jsx(MenuItem,{value:\"\",children:\"Nessuno\"}),strumenti.map(strumento=>/*#__PURE__*/_jsxs(MenuItem,{value:strumento.id_strumento,children:[strumento.nome,\" \",strumento.marca,\" \",strumento.modello]},strumento.id_strumento))]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(TextField,{label:\"Descrizione Strumento (alternativa)\",value:formData.strumento_utilizzato,onChange:e=>handleInputChange('strumento_utilizzato',e.target.value),fullWidth:true,helperText:\"Utilizzare solo se lo strumento non \\xE8 presente nell'elenco sopra\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Divider,{})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(TextField,{label:\"Lunghezza Misurata (m)\",type:\"number\",value:formData.lunghezza_misurata,onChange:e=>handleInputChange('lunghezza_misurata',e.target.value),fullWidth:true,inputProps:{step:0.01,min:0}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(TextField,{label:\"Valore Isolamento (M\\u03A9)\",value:formData.valore_isolamento,onChange:e=>handleInputChange('valore_isolamento',e.target.value),fullWidth:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(TextField,{label:\"Valore Continuit\\xE0\",value:formData.valore_continuita,onChange:e=>handleInputChange('valore_continuita',e.target.value),fullWidth:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(TextField,{label:\"Valore Resistenza\",value:formData.valore_resistenza,onChange:e=>handleInputChange('valore_resistenza',e.target.value),fullWidth:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(TextField,{label:\"Note\",value:formData.note,onChange:e=>handleInputChange('note',e.target.value),fullWidth:true,multiline:true,rows:3})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2,justifyContent:'flex-end'},children:[/*#__PURE__*/_jsx(Button,{variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(CancelIcon,{}),onClick:onCancel,disabled:loading,children:\"Annulla\"}),/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"contained\",startIcon:/*#__PURE__*/_jsx(SaveIcon,{}),disabled:loading,children:loading?'Salvataggio...':'Salva Certificazione'})]})})]})})]});}export default CertificazioneForm;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Box", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Autocomplete", "<PERSON><PERSON>", "Divider", "Save", "SaveIcon", "Cancel", "CancelIcon", "apiService", "jsx", "_jsx", "jsxs", "_jsxs", "CertificazioneForm", "_ref", "cantiereId", "certificazione", "strumenti", "onSuccess", "onCancel", "formData", "setFormData", "id_cavo", "id_operatore", "strumento_utilizzato", "id_strumento", "<PERSON><PERSON><PERSON>_misurata", "valore_continuita", "valore_isolamento", "valore_resistenza", "note", "cavi", "<PERSON><PERSON><PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "loading", "setLoading", "error", "setError", "loadCavi", "caviData", "get<PERSON><PERSON>", "caviInstallati", "filter", "cavo", "stato_installazione", "find", "c", "err", "console", "handleInputChange", "field", "value", "prev", "handleCavoChange", "event", "newValue", "metratura_reale", "handleStrumentoChange", "strumentoId", "target", "strumento", "s", "nome", "marca", "modello", "handleSubmit", "preventDefault", "submitData", "parseFloat", "updateCertificazione", "id_certificazione", "createCertificazione", "_err$response", "_err$response$data", "response", "data", "detail", "sx", "p", "children", "variant", "gutterBottom", "severity", "mb", "onSubmit", "container", "spacing", "item", "xs", "color", "onChange", "options", "getOptionLabel", "option", "tipologia", "sezione", "renderInput", "params", "label", "required", "fullWidth", "disabled", "mt", "bgcolor", "borderRadius", "ubicazione_partenza", "ubicazione_arrivo", "metri_te<PERSON>ci", "md", "e", "map", "helperText", "type", "inputProps", "step", "min", "multiline", "rows", "display", "gap", "justifyContent", "startIcon", "onClick"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/certificazioni/CertificazioneForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Box,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Autocomplete,\n  Alert,\n  Divider\n} from '@mui/material';\nimport { Save as SaveIcon, Cancel as CancelIcon } from '@mui/icons-material';\n\nimport { apiService } from '../../services/apiService';\n\nfunction CertificazioneForm({ cantiereId, certificazione, strumenti, onSuccess, onCancel }) {\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    strumento_utilizzato: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '500',\n    valore_resistenza: 'OK',\n    note: ''\n  });\n\n  const [cavi, setCavi] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    loadCavi();\n    if (certificazione) {\n      setFormData({\n        id_cavo: certificazione.id_cavo || '',\n        id_operatore: certificazione.id_operatore || '',\n        strumento_utilizzato: certificazione.strumento_utilizzato || '',\n        id_strumento: certificazione.id_strumento || '',\n        lunghezza_misurata: certificazione.lunghezza_misurata || '',\n        valore_continuita: certificazione.valore_continuita || 'OK',\n        valore_isolamento: certificazione.valore_isolamento || '500',\n        valore_resistenza: certificazione.valore_resistenza || 'OK',\n        note: certificazione.note || ''\n      });\n    }\n  }, [certificazione, cantiereId]);\n\n  const loadCavi = async () => {\n    try {\n      // Carica solo i cavi installati che non hanno già una certificazione\n      const caviData = await apiService.getCavi(cantiereId);\n      const caviInstallati = caviData.filter(cavo => \n        cavo.stato_installazione === 'INSTALLATO'\n      );\n      setCavi(caviInstallati);\n\n      // Se stiamo modificando, trova il cavo selezionato\n      if (certificazione) {\n        const cavo = caviInstallati.find(c => c.id_cavo === certificazione.id_cavo);\n        setSelectedCavo(cavo);\n      }\n    } catch (err) {\n      console.error('Errore nel caricamento dei cavi:', err);\n      setError('Errore nel caricamento dei cavi');\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleCavoChange = (event, newValue) => {\n    setSelectedCavo(newValue);\n    if (newValue) {\n      setFormData(prev => ({\n        ...prev,\n        id_cavo: newValue.id_cavo,\n        lunghezza_misurata: newValue.metratura_reale || ''\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        id_cavo: '',\n        lunghezza_misurata: ''\n      }));\n    }\n  };\n\n  const handleStrumentoChange = (event) => {\n    const strumentoId = event.target.value;\n    handleInputChange('id_strumento', strumentoId);\n    \n    if (strumentoId) {\n      const strumento = strumenti.find(s => s.id_strumento === strumentoId);\n      if (strumento) {\n        handleInputChange('strumento_utilizzato', `${strumento.nome} ${strumento.marca} ${strumento.modello}`);\n      }\n    } else {\n      handleInputChange('strumento_utilizzato', '');\n    }\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n    \n    if (!formData.id_cavo) {\n      setError('Seleziona un cavo da certificare');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError('');\n\n      const submitData = {\n        ...formData,\n        id_strumento: formData.id_strumento || null,\n        lunghezza_misurata: formData.lunghezza_misurata ? parseFloat(formData.lunghezza_misurata) : null\n      };\n\n      if (certificazione) {\n        await apiService.updateCertificazione(cantiereId, certificazione.id_certificazione, submitData);\n        onSuccess('Certificazione aggiornata con successo');\n      } else {\n        await apiService.createCertificazione(cantiereId, submitData);\n        onSuccess('Certificazione creata con successo');\n      }\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.response?.data?.detail || 'Errore nel salvataggio della certificazione');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Paper sx={{ p: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        {certificazione ? 'Modifica Certificazione' : 'Nuova Certificazione'}\n      </Typography>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      <form onSubmit={handleSubmit}>\n        <Grid container spacing={3}>\n          {/* Selezione Cavo */}\n          <Grid item xs={12}>\n            <Typography variant=\"subtitle2\" color=\"text.secondary\" gutterBottom>\n              Selezione Cavo\n            </Typography>\n            <Autocomplete\n              value={selectedCavo}\n              onChange={handleCavoChange}\n              options={cavi}\n              getOptionLabel={(option) => `${option.id_cavo} - ${option.tipologia || ''} ${option.sezione || ''}`}\n              renderInput={(params) => (\n                <TextField\n                  {...params}\n                  label=\"Cavo da certificare\"\n                  required\n                  fullWidth\n                />\n              )}\n              disabled={!!certificazione} // Non modificabile se stiamo editando\n            />\n            {selectedCavo && (\n              <Box sx={{ mt: 1, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>\n                <Typography variant=\"body2\">\n                  <strong>Partenza:</strong> {selectedCavo.ubicazione_partenza || '-'} | \n                  <strong> Arrivo:</strong> {selectedCavo.ubicazione_arrivo || '-'} | \n                  <strong> Metri Teorici:</strong> {selectedCavo.metri_teorici || '-'} | \n                  <strong> Metri Reali:</strong> {selectedCavo.metratura_reale || '-'}\n                </Typography>\n              </Box>\n            )}\n          </Grid>\n\n          <Grid item xs={12}>\n            <Divider />\n          </Grid>\n\n          {/* Informazioni Operatore e Strumento */}\n          <Grid item xs={12} md={6}>\n            <TextField\n              label=\"Operatore\"\n              value={formData.id_operatore}\n              onChange={(e) => handleInputChange('id_operatore', e.target.value)}\n              fullWidth\n            />\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            <FormControl fullWidth>\n              <InputLabel>Strumento Certificato</InputLabel>\n              <Select\n                value={formData.id_strumento}\n                onChange={handleStrumentoChange}\n                label=\"Strumento Certificato\"\n              >\n                <MenuItem value=\"\">Nessuno</MenuItem>\n                {strumenti.map((strumento) => (\n                  <MenuItem key={strumento.id_strumento} value={strumento.id_strumento}>\n                    {strumento.nome} {strumento.marca} {strumento.modello}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n\n          <Grid item xs={12}>\n            <TextField\n              label=\"Descrizione Strumento (alternativa)\"\n              value={formData.strumento_utilizzato}\n              onChange={(e) => handleInputChange('strumento_utilizzato', e.target.value)}\n              fullWidth\n              helperText=\"Utilizzare solo se lo strumento non è presente nell'elenco sopra\"\n            />\n          </Grid>\n\n          <Grid item xs={12}>\n            <Divider />\n          </Grid>\n\n          {/* Misurazioni */}\n          <Grid item xs={12} md={6}>\n            <TextField\n              label=\"Lunghezza Misurata (m)\"\n              type=\"number\"\n              value={formData.lunghezza_misurata}\n              onChange={(e) => handleInputChange('lunghezza_misurata', e.target.value)}\n              fullWidth\n              inputProps={{ step: 0.01, min: 0 }}\n            />\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            <TextField\n              label=\"Valore Isolamento (MΩ)\"\n              value={formData.valore_isolamento}\n              onChange={(e) => handleInputChange('valore_isolamento', e.target.value)}\n              fullWidth\n            />\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            <TextField\n              label=\"Valore Continuità\"\n              value={formData.valore_continuita}\n              onChange={(e) => handleInputChange('valore_continuita', e.target.value)}\n              fullWidth\n            />\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            <TextField\n              label=\"Valore Resistenza\"\n              value={formData.valore_resistenza}\n              onChange={(e) => handleInputChange('valore_resistenza', e.target.value)}\n              fullWidth\n            />\n          </Grid>\n\n          {/* Note */}\n          <Grid item xs={12}>\n            <TextField\n              label=\"Note\"\n              value={formData.note}\n              onChange={(e) => handleInputChange('note', e.target.value)}\n              fullWidth\n              multiline\n              rows={3}\n            />\n          </Grid>\n\n          {/* Pulsanti */}\n          <Grid item xs={12}>\n            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>\n              <Button\n                variant=\"outlined\"\n                startIcon={<CancelIcon />}\n                onClick={onCancel}\n                disabled={loading}\n              >\n                Annulla\n              </Button>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n              >\n                {loading ? 'Salvataggio...' : 'Salva Certificazione'}\n              </Button>\n            </Box>\n          </Grid>\n        </Grid>\n      </form>\n    </Paper>\n  );\n}\n\nexport default CertificazioneForm;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,KAAK,CACLC,UAAU,CACVC,SAAS,CACTC,MAAM,CACNC,GAAG,CACHC,IAAI,CACJC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,CACRC,YAAY,CACZC,KAAK,CACLC,OAAO,KACF,eAAe,CACtB,OAASC,IAAI,GAAI,CAAAC,QAAQ,CAAEC,MAAM,GAAI,CAAAC,UAAU,KAAQ,qBAAqB,CAE5E,OAASC,UAAU,KAAQ,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvD,QAAS,CAAAC,kBAAkBA,CAAAC,IAAA,CAAiE,IAAhE,CAAEC,UAAU,CAAEC,cAAc,CAAEC,SAAS,CAAEC,SAAS,CAAEC,QAAS,CAAC,CAAAL,IAAA,CACxF,KAAM,CAACM,QAAQ,CAAEC,WAAW,CAAC,CAAGhC,QAAQ,CAAC,CACvCiC,OAAO,CAAE,EAAE,CACXC,YAAY,CAAE,EAAE,CAChBC,oBAAoB,CAAE,EAAE,CACxBC,YAAY,CAAE,EAAE,CAChBC,kBAAkB,CAAE,EAAE,CACtBC,iBAAiB,CAAE,IAAI,CACvBC,iBAAiB,CAAE,KAAK,CACxBC,iBAAiB,CAAE,IAAI,CACvBC,IAAI,CAAE,EACR,CAAC,CAAC,CAEF,KAAM,CAACC,IAAI,CAAEC,OAAO,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAC4C,YAAY,CAAEC,eAAe,CAAC,CAAG7C,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAC8C,OAAO,CAAEC,UAAU,CAAC,CAAG/C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACgD,KAAK,CAAEC,QAAQ,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CAEtCC,SAAS,CAAC,IAAM,CACdiD,QAAQ,CAAC,CAAC,CACV,GAAIvB,cAAc,CAAE,CAClBK,WAAW,CAAC,CACVC,OAAO,CAAEN,cAAc,CAACM,OAAO,EAAI,EAAE,CACrCC,YAAY,CAAEP,cAAc,CAACO,YAAY,EAAI,EAAE,CAC/CC,oBAAoB,CAAER,cAAc,CAACQ,oBAAoB,EAAI,EAAE,CAC/DC,YAAY,CAAET,cAAc,CAACS,YAAY,EAAI,EAAE,CAC/CC,kBAAkB,CAAEV,cAAc,CAACU,kBAAkB,EAAI,EAAE,CAC3DC,iBAAiB,CAAEX,cAAc,CAACW,iBAAiB,EAAI,IAAI,CAC3DC,iBAAiB,CAAEZ,cAAc,CAACY,iBAAiB,EAAI,KAAK,CAC5DC,iBAAiB,CAAEb,cAAc,CAACa,iBAAiB,EAAI,IAAI,CAC3DC,IAAI,CAAEd,cAAc,CAACc,IAAI,EAAI,EAC/B,CAAC,CAAC,CACJ,CACF,CAAC,CAAE,CAACd,cAAc,CAAED,UAAU,CAAC,CAAC,CAEhC,KAAM,CAAAwB,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAI,CACF;AACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAhC,UAAU,CAACiC,OAAO,CAAC1B,UAAU,CAAC,CACrD,KAAM,CAAA2B,cAAc,CAAGF,QAAQ,CAACG,MAAM,CAACC,IAAI,EACzCA,IAAI,CAACC,mBAAmB,GAAK,YAC/B,CAAC,CACDb,OAAO,CAACU,cAAc,CAAC,CAEvB;AACA,GAAI1B,cAAc,CAAE,CAClB,KAAM,CAAA4B,IAAI,CAAGF,cAAc,CAACI,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACzB,OAAO,GAAKN,cAAc,CAACM,OAAO,CAAC,CAC3EY,eAAe,CAACU,IAAI,CAAC,CACvB,CACF,CAAE,MAAOI,GAAG,CAAE,CACZC,OAAO,CAACZ,KAAK,CAAC,kCAAkC,CAAEW,GAAG,CAAC,CACtDV,QAAQ,CAAC,iCAAiC,CAAC,CAC7C,CACF,CAAC,CAED,KAAM,CAAAY,iBAAiB,CAAGA,CAACC,KAAK,CAAEC,KAAK,GAAK,CAC1C/B,WAAW,CAACgC,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP,CAACF,KAAK,EAAGC,KACX,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAE,gBAAgB,CAAGA,CAACC,KAAK,CAAEC,QAAQ,GAAK,CAC5CtB,eAAe,CAACsB,QAAQ,CAAC,CACzB,GAAIA,QAAQ,CAAE,CACZnC,WAAW,CAACgC,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP/B,OAAO,CAAEkC,QAAQ,CAAClC,OAAO,CACzBI,kBAAkB,CAAE8B,QAAQ,CAACC,eAAe,EAAI,EAClD,CAAC,CAAC,CAAC,CACL,CAAC,IAAM,CACLpC,WAAW,CAACgC,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP/B,OAAO,CAAE,EAAE,CACXI,kBAAkB,CAAE,EACtB,CAAC,CAAC,CAAC,CACL,CACF,CAAC,CAED,KAAM,CAAAgC,qBAAqB,CAAIH,KAAK,EAAK,CACvC,KAAM,CAAAI,WAAW,CAAGJ,KAAK,CAACK,MAAM,CAACR,KAAK,CACtCF,iBAAiB,CAAC,cAAc,CAAES,WAAW,CAAC,CAE9C,GAAIA,WAAW,CAAE,CACf,KAAM,CAAAE,SAAS,CAAG5C,SAAS,CAAC6B,IAAI,CAACgB,CAAC,EAAIA,CAAC,CAACrC,YAAY,GAAKkC,WAAW,CAAC,CACrE,GAAIE,SAAS,CAAE,CACbX,iBAAiB,CAAC,sBAAsB,CAAE,GAAGW,SAAS,CAACE,IAAI,IAAIF,SAAS,CAACG,KAAK,IAAIH,SAAS,CAACI,OAAO,EAAE,CAAC,CACxG,CACF,CAAC,IAAM,CACLf,iBAAiB,CAAC,sBAAsB,CAAE,EAAE,CAAC,CAC/C,CACF,CAAC,CAED,KAAM,CAAAgB,YAAY,CAAG,KAAO,CAAAX,KAAK,EAAK,CACpCA,KAAK,CAACY,cAAc,CAAC,CAAC,CAEtB,GAAI,CAAC/C,QAAQ,CAACE,OAAO,CAAE,CACrBgB,QAAQ,CAAC,kCAAkC,CAAC,CAC5C,OACF,CAEA,GAAI,CACFF,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CAEZ,KAAM,CAAA8B,UAAU,CAAG,CACjB,GAAGhD,QAAQ,CACXK,YAAY,CAAEL,QAAQ,CAACK,YAAY,EAAI,IAAI,CAC3CC,kBAAkB,CAAEN,QAAQ,CAACM,kBAAkB,CAAG2C,UAAU,CAACjD,QAAQ,CAACM,kBAAkB,CAAC,CAAG,IAC9F,CAAC,CAED,GAAIV,cAAc,CAAE,CAClB,KAAM,CAAAR,UAAU,CAAC8D,oBAAoB,CAACvD,UAAU,CAAEC,cAAc,CAACuD,iBAAiB,CAAEH,UAAU,CAAC,CAC/FlD,SAAS,CAAC,wCAAwC,CAAC,CACrD,CAAC,IAAM,CACL,KAAM,CAAAV,UAAU,CAACgE,oBAAoB,CAACzD,UAAU,CAAEqD,UAAU,CAAC,CAC7DlD,SAAS,CAAC,oCAAoC,CAAC,CACjD,CACF,CAAE,MAAO8B,GAAG,CAAE,KAAAyB,aAAA,CAAAC,kBAAA,CACZzB,OAAO,CAACZ,KAAK,CAAC,yBAAyB,CAAEW,GAAG,CAAC,CAC7CV,QAAQ,CAAC,EAAAmC,aAAA,CAAAzB,GAAG,CAAC2B,QAAQ,UAAAF,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcG,IAAI,UAAAF,kBAAA,iBAAlBA,kBAAA,CAAoBG,MAAM,GAAI,6CAA6C,CAAC,CACvF,CAAC,OAAS,CACRzC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACExB,KAAA,CAACrB,KAAK,EAACuF,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAC,QAAA,eAClBtE,IAAA,CAAClB,UAAU,EAACyF,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAClChE,cAAc,CAAG,yBAAyB,CAAG,sBAAsB,CAC1D,CAAC,CAEZqB,KAAK,eACJ3B,IAAA,CAACR,KAAK,EAACiF,QAAQ,CAAC,OAAO,CAACL,EAAE,CAAE,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CACnC3C,KAAK,CACD,CACR,cAED3B,IAAA,SAAM2E,QAAQ,CAAEnB,YAAa,CAAAc,QAAA,cAC3BpE,KAAA,CAAChB,IAAI,EAAC0F,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAP,QAAA,eAEzBpE,KAAA,CAAChB,IAAI,EAAC4F,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,eAChBtE,IAAA,CAAClB,UAAU,EAACyF,OAAO,CAAC,WAAW,CAACS,KAAK,CAAC,gBAAgB,CAACR,YAAY,MAAAF,QAAA,CAAC,gBAEpE,CAAY,CAAC,cACbtE,IAAA,CAACT,YAAY,EACXmD,KAAK,CAAEnB,YAAa,CACpB0D,QAAQ,CAAErC,gBAAiB,CAC3BsC,OAAO,CAAE7D,IAAK,CACd8D,cAAc,CAAGC,MAAM,EAAK,GAAGA,MAAM,CAACxE,OAAO,MAAMwE,MAAM,CAACC,SAAS,EAAI,EAAE,IAAID,MAAM,CAACE,OAAO,EAAI,EAAE,EAAG,CACpGC,WAAW,CAAGC,MAAM,eAClBxF,IAAA,CAACjB,SAAS,KACJyG,MAAM,CACVC,KAAK,CAAC,qBAAqB,CAC3BC,QAAQ,MACRC,SAAS,MACV,CACD,CACFC,QAAQ,CAAE,CAAC,CAACtF,cAAgB;AAAA,CAC7B,CAAC,CACDiB,YAAY,eACXvB,IAAA,CAACf,GAAG,EAACmF,EAAE,CAAE,CAAEyB,EAAE,CAAE,CAAC,CAAExB,CAAC,CAAE,CAAC,CAAEyB,OAAO,CAAE,SAAS,CAAEC,YAAY,CAAE,CAAE,CAAE,CAAAzB,QAAA,cAC5DpE,KAAA,CAACpB,UAAU,EAACyF,OAAO,CAAC,OAAO,CAAAD,QAAA,eACzBtE,IAAA,WAAAsE,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAAC/C,YAAY,CAACyE,mBAAmB,EAAI,GAAG,CAAC,IACpE,cAAAhG,IAAA,WAAAsE,QAAA,CAAQ,UAAQ,CAAQ,CAAC,IAAC,CAAC/C,YAAY,CAAC0E,iBAAiB,EAAI,GAAG,CAAC,IACjE,cAAAjG,IAAA,WAAAsE,QAAA,CAAQ,iBAAe,CAAQ,CAAC,IAAC,CAAC/C,YAAY,CAAC2E,aAAa,EAAI,GAAG,CAAC,IACpE,cAAAlG,IAAA,WAAAsE,QAAA,CAAQ,eAAa,CAAQ,CAAC,IAAC,CAAC/C,YAAY,CAACwB,eAAe,EAAI,GAAG,EACzD,CAAC,CACV,CACN,EACG,CAAC,cAEP/C,IAAA,CAACd,IAAI,EAAC4F,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,cAChBtE,IAAA,CAACP,OAAO,GAAE,CAAC,CACP,CAAC,cAGPO,IAAA,CAACd,IAAI,EAAC4F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACoB,EAAE,CAAE,CAAE,CAAA7B,QAAA,cACvBtE,IAAA,CAACjB,SAAS,EACR0G,KAAK,CAAC,WAAW,CACjB/C,KAAK,CAAEhC,QAAQ,CAACG,YAAa,CAC7BoE,QAAQ,CAAGmB,CAAC,EAAK5D,iBAAiB,CAAC,cAAc,CAAE4D,CAAC,CAAClD,MAAM,CAACR,KAAK,CAAE,CACnEiD,SAAS,MACV,CAAC,CACE,CAAC,cAEP3F,IAAA,CAACd,IAAI,EAAC4F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACoB,EAAE,CAAE,CAAE,CAAA7B,QAAA,cACvBpE,KAAA,CAACf,WAAW,EAACwG,SAAS,MAAArB,QAAA,eACpBtE,IAAA,CAACZ,UAAU,EAAAkF,QAAA,CAAC,uBAAqB,CAAY,CAAC,cAC9CpE,KAAA,CAACb,MAAM,EACLqD,KAAK,CAAEhC,QAAQ,CAACK,YAAa,CAC7BkE,QAAQ,CAAEjC,qBAAsB,CAChCyC,KAAK,CAAC,uBAAuB,CAAAnB,QAAA,eAE7BtE,IAAA,CAACV,QAAQ,EAACoD,KAAK,CAAC,EAAE,CAAA4B,QAAA,CAAC,SAAO,CAAU,CAAC,CACpC/D,SAAS,CAAC8F,GAAG,CAAElD,SAAS,eACvBjD,KAAA,CAACZ,QAAQ,EAA8BoD,KAAK,CAAES,SAAS,CAACpC,YAAa,CAAAuD,QAAA,EAClEnB,SAAS,CAACE,IAAI,CAAC,GAAC,CAACF,SAAS,CAACG,KAAK,CAAC,GAAC,CAACH,SAAS,CAACI,OAAO,GADxCJ,SAAS,CAACpC,YAEf,CACX,CAAC,EACI,CAAC,EACE,CAAC,CACV,CAAC,cAEPf,IAAA,CAACd,IAAI,EAAC4F,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,cAChBtE,IAAA,CAACjB,SAAS,EACR0G,KAAK,CAAC,qCAAqC,CAC3C/C,KAAK,CAAEhC,QAAQ,CAACI,oBAAqB,CACrCmE,QAAQ,CAAGmB,CAAC,EAAK5D,iBAAiB,CAAC,sBAAsB,CAAE4D,CAAC,CAAClD,MAAM,CAACR,KAAK,CAAE,CAC3EiD,SAAS,MACTW,UAAU,CAAC,qEAAkE,CAC9E,CAAC,CACE,CAAC,cAEPtG,IAAA,CAACd,IAAI,EAAC4F,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,cAChBtE,IAAA,CAACP,OAAO,GAAE,CAAC,CACP,CAAC,cAGPO,IAAA,CAACd,IAAI,EAAC4F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACoB,EAAE,CAAE,CAAE,CAAA7B,QAAA,cACvBtE,IAAA,CAACjB,SAAS,EACR0G,KAAK,CAAC,wBAAwB,CAC9Bc,IAAI,CAAC,QAAQ,CACb7D,KAAK,CAAEhC,QAAQ,CAACM,kBAAmB,CACnCiE,QAAQ,CAAGmB,CAAC,EAAK5D,iBAAiB,CAAC,oBAAoB,CAAE4D,CAAC,CAAClD,MAAM,CAACR,KAAK,CAAE,CACzEiD,SAAS,MACTa,UAAU,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEC,GAAG,CAAE,CAAE,CAAE,CACpC,CAAC,CACE,CAAC,cAEP1G,IAAA,CAACd,IAAI,EAAC4F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACoB,EAAE,CAAE,CAAE,CAAA7B,QAAA,cACvBtE,IAAA,CAACjB,SAAS,EACR0G,KAAK,CAAC,6BAAwB,CAC9B/C,KAAK,CAAEhC,QAAQ,CAACQ,iBAAkB,CAClC+D,QAAQ,CAAGmB,CAAC,EAAK5D,iBAAiB,CAAC,mBAAmB,CAAE4D,CAAC,CAAClD,MAAM,CAACR,KAAK,CAAE,CACxEiD,SAAS,MACV,CAAC,CACE,CAAC,cAEP3F,IAAA,CAACd,IAAI,EAAC4F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACoB,EAAE,CAAE,CAAE,CAAA7B,QAAA,cACvBtE,IAAA,CAACjB,SAAS,EACR0G,KAAK,CAAC,sBAAmB,CACzB/C,KAAK,CAAEhC,QAAQ,CAACO,iBAAkB,CAClCgE,QAAQ,CAAGmB,CAAC,EAAK5D,iBAAiB,CAAC,mBAAmB,CAAE4D,CAAC,CAAClD,MAAM,CAACR,KAAK,CAAE,CACxEiD,SAAS,MACV,CAAC,CACE,CAAC,cAEP3F,IAAA,CAACd,IAAI,EAAC4F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACoB,EAAE,CAAE,CAAE,CAAA7B,QAAA,cACvBtE,IAAA,CAACjB,SAAS,EACR0G,KAAK,CAAC,mBAAmB,CACzB/C,KAAK,CAAEhC,QAAQ,CAACS,iBAAkB,CAClC8D,QAAQ,CAAGmB,CAAC,EAAK5D,iBAAiB,CAAC,mBAAmB,CAAE4D,CAAC,CAAClD,MAAM,CAACR,KAAK,CAAE,CACxEiD,SAAS,MACV,CAAC,CACE,CAAC,cAGP3F,IAAA,CAACd,IAAI,EAAC4F,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,cAChBtE,IAAA,CAACjB,SAAS,EACR0G,KAAK,CAAC,MAAM,CACZ/C,KAAK,CAAEhC,QAAQ,CAACU,IAAK,CACrB6D,QAAQ,CAAGmB,CAAC,EAAK5D,iBAAiB,CAAC,MAAM,CAAE4D,CAAC,CAAClD,MAAM,CAACR,KAAK,CAAE,CAC3DiD,SAAS,MACTgB,SAAS,MACTC,IAAI,CAAE,CAAE,CACT,CAAC,CACE,CAAC,cAGP5G,IAAA,CAACd,IAAI,EAAC4F,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,cAChBpE,KAAA,CAACjB,GAAG,EAACmF,EAAE,CAAE,CAAEyC,OAAO,CAAE,MAAM,CAAEC,GAAG,CAAE,CAAC,CAAEC,cAAc,CAAE,UAAW,CAAE,CAAAzC,QAAA,eAC/DtE,IAAA,CAAChB,MAAM,EACLuF,OAAO,CAAC,UAAU,CAClByC,SAAS,cAAEhH,IAAA,CAACH,UAAU,GAAE,CAAE,CAC1BoH,OAAO,CAAExG,QAAS,CAClBmF,QAAQ,CAAEnE,OAAQ,CAAA6C,QAAA,CACnB,SAED,CAAQ,CAAC,cACTtE,IAAA,CAAChB,MAAM,EACLuH,IAAI,CAAC,QAAQ,CACbhC,OAAO,CAAC,WAAW,CACnByC,SAAS,cAAEhH,IAAA,CAACL,QAAQ,GAAE,CAAE,CACxBiG,QAAQ,CAAEnE,OAAQ,CAAA6C,QAAA,CAEjB7C,OAAO,CAAG,gBAAgB,CAAG,sBAAsB,CAC9C,CAAC,EACN,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,EACF,CAAC,CAEZ,CAEA,cAAe,CAAAtB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}