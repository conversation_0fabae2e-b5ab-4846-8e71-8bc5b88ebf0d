'use client'

import React from 'react'
import { UnifiedModalExample } from '@/components/cavi/examples/UnifiedModalExample'

export default function TestUnifiedModalPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
            Test Interfaccia Unificata Cable/Bobbin
          </h1>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h2 className="font-semibold text-blue-800 mb-2">Istruzioni per il Test</h2>
            <ul className="text-blue-700 text-sm space-y-1">
              <li>• Clicca sui pulsanti per aprire la modale unificata in diverse modalità</li>
              <li>• Testa la validazione dei campi e la selezione delle bobine</li>
              <li>• Verifica che le sezioni dinamiche si mostrino correttamente</li>
              <li>• Controlla l'accessibilità con Tab e Escape</li>
              <li>• Verifica i messaggi di errore e successo</li>
            </ul>
          </div>

          <UnifiedModalExample />
          
          <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-4">
            <h2 className="font-semibold text-green-800 mb-2">Funzionalità Implementate</h2>
            <div className="grid md:grid-cols-2 gap-4 text-sm text-green-700">
              <div>
                <h3 className="font-medium mb-1">Modalità Aggiungi Metri:</h3>
                <ul className="space-y-1">
                  <li>✅ Validazione input metri</li>
                  <li>✅ Selezione bobina compatibile/incompatibile</li>
                  <li>✅ Opzione BOBINA VUOTA</li>
                  <li>✅ Ricerca bobine</li>
                </ul>
              </div>
              <div>
                <h3 className="font-medium mb-1">Modalità Modifica Bobina:</h3>
                <ul className="space-y-1">
                  <li>✅ Radio button per opzioni</li>
                  <li>✅ Selezione condizionale bobina</li>
                  <li>✅ Modifica metri posati</li>
                  <li>✅ Annulla posa</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h2 className="font-semibold text-yellow-800 mb-2">Accessibilità</h2>
            <div className="text-sm text-yellow-700 space-y-1">
              <li>✅ Attributi ARIA per screen reader</li>
              <li>✅ Navigazione da tastiera (Tab, Enter, Escape)</li>
              <li>✅ Focus management e indicatori visivi</li>
              <li>✅ Messaggi di errore con role="alert"</li>
              <li>✅ Etichette descrittive per tutti i controlli</li>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
