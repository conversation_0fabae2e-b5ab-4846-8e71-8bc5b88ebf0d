{"ast": null, "code": "import * as React from 'react';\nimport { useLocalizationContext } from './useUtils';\nexport function useValidation(props, validate, isSameError, defaultErrorState) {\n  const {\n    value,\n    onError\n  } = props;\n  const adapter = useLocalizationContext();\n  const previousValidationErrorRef = React.useRef(defaultErrorState);\n  const validationError = validate({\n    adapter,\n    value,\n    props\n  });\n  React.useEffect(() => {\n    if (onError && !isSameError(validationError, previousValidationErrorRef.current)) {\n      onError(validationError, value);\n    }\n    previousValidationErrorRef.current = validationError;\n  }, [isSameError, onError, previousValidationErrorRef, validationError, value]);\n  return validationError;\n}", "map": {"version": 3, "names": ["React", "useLocalizationContext", "useValidation", "props", "validate", "isSameError", "defaultErrorState", "value", "onError", "adapter", "previousValidationErrorRef", "useRef", "validationError", "useEffect", "current"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useValidation.js"], "sourcesContent": ["import * as React from 'react';\nimport { useLocalizationContext } from './useUtils';\nexport function useValidation(props, validate, isSameError, defaultErrorState) {\n  const {\n    value,\n    onError\n  } = props;\n  const adapter = useLocalizationContext();\n  const previousValidationErrorRef = React.useRef(defaultErrorState);\n  const validationError = validate({\n    adapter,\n    value,\n    props\n  });\n  React.useEffect(() => {\n    if (onError && !isSameError(validationError, previousValidationErrorRef.current)) {\n      onError(validationError, value);\n    }\n    previousValidationErrorRef.current = validationError;\n  }, [isSameError, onError, previousValidationErrorRef, validationError, value]);\n  return validationError;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,QAAQ,YAAY;AACnD,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,iBAAiB,EAAE;EAC7E,MAAM;IACJC,KAAK;IACLC;EACF,CAAC,GAAGL,KAAK;EACT,MAAMM,OAAO,GAAGR,sBAAsB,CAAC,CAAC;EACxC,MAAMS,0BAA0B,GAAGV,KAAK,CAACW,MAAM,CAACL,iBAAiB,CAAC;EAClE,MAAMM,eAAe,GAAGR,QAAQ,CAAC;IAC/BK,OAAO;IACPF,KAAK;IACLJ;EACF,CAAC,CAAC;EACFH,KAAK,CAACa,SAAS,CAAC,MAAM;IACpB,IAAIL,OAAO,IAAI,CAACH,WAAW,CAACO,eAAe,EAAEF,0BAA0B,CAACI,OAAO,CAAC,EAAE;MAChFN,OAAO,CAACI,eAAe,EAAEL,KAAK,CAAC;IACjC;IACAG,0BAA0B,CAACI,OAAO,GAAGF,eAAe;EACtD,CAAC,EAAE,CAACP,WAAW,EAAEG,OAAO,EAAEE,0BAA0B,EAAEE,eAAe,EAAEL,KAAK,CAAC,CAAC;EAC9E,OAAOK,eAAe;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}