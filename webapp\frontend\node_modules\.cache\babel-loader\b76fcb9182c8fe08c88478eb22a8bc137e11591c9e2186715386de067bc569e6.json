{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\BobineFilterableTable.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, IconButton } from '@mui/material';\nimport { Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\n\n/**\n * Componente per visualizzare la lista delle bobine con filtri in stile Excel\n * \n * @param {Object} props - Proprietà del componente\n * @param {Array} props.bobine - Lista delle bobine da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {Function} props.onEdit - Funzione chiamata quando si vuole modificare una bobina\n * @param {Function} props.onDelete - Funzione chiamata quando si vuole eliminare una bobina\n * @param {Function} props.onViewHistory - Funzione chiamata quando si vuole visualizzare lo storico di una bobina\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BobineFilterableTable = ({\n  bobine = [],\n  loading = false,\n  onFilteredDataChange = null,\n  onEdit = null,\n  onDelete = null,\n  onViewHistory = null\n}) => {\n  _s();\n  const [filteredBobine, setFilteredBobine] = useState(bobine);\n\n  // Aggiorna i dati filtrati quando cambiano le bobine\n  useEffect(() => {\n    setFilteredBobine(bobine);\n  }, [bobine]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = data => {\n    setFilteredBobine(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Definizione delle colonne\n  const columns = [{\n    field: 'numero_bobina',\n    headerName: 'Numero',\n    dataType: 'text',\n    headerStyle: {\n      fontWeight: 'bold'\n    }\n  }, {\n    field: 'utility',\n    headerName: 'Utility',\n    dataType: 'text'\n  }, {\n    field: 'tipologia',\n    headerName: 'Tipologia',\n    dataType: 'text'\n  }, {\n    field: 'n_conduttori',\n    headerName: 'N° Cond.',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    }\n  }, {\n    field: 'sezione',\n    headerName: 'Sezione',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    }\n  }, {\n    field: 'metri_totali',\n    headerName: 'Metri Totali',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metri_totali ? row.metri_totali.toFixed(1) : '0'\n  }, {\n    field: 'metri_residui',\n    headerName: 'Metri Residui',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metri_residui ? row.metri_residui.toFixed(1) : '0'\n  }, {\n    field: 'stato_bobina',\n    headerName: 'Stato',\n    dataType: 'text',\n    renderCell: row => {\n      let color = 'default';\n      if (row.stato_bobina === 'Disponibile') color = 'success';else if (row.stato_bobina === 'In Uso') color = 'warning';else if (row.stato_bobina === 'Esaurita') color = 'error';\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: row.stato_bobina || 'N/D',\n        size: \"small\",\n        color: color,\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    field: 'ubicazione_bobina',\n    headerName: 'Ubicazione',\n    dataType: 'text'\n  }, {\n    field: 'fornitore',\n    headerName: 'Fornitore',\n    dataType: 'text'\n  }, {\n    field: 'n_DDT',\n    headerName: 'N° DDT',\n    dataType: 'text'\n  }, {\n    field: 'data_DDT',\n    headerName: 'Data DDT',\n    dataType: 'text'\n  }, {\n    field: 'actions',\n    headerName: 'Azioni',\n    disableFilter: true,\n    disableSort: true,\n    align: 'center',\n    renderCell: row => /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center'\n      },\n      children: [onEdit && /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: () => onEdit(row),\n        title: \"Modifica bobina\",\n        color: \"primary\",\n        children: /*#__PURE__*/_jsxDEV(EditIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 13\n      }, this), onDelete && /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: () => onDelete(row),\n        title: \"Elimina bobina\",\n        color: \"error\",\n        disabled: row.stato_bobina !== 'Disponibile' || row.metri_residui !== row.metri_totali,\n        children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 13\n      }, this), onViewHistory && /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: () => onViewHistory(row),\n        title: \"Visualizza storico utilizzo\",\n        color: \"info\",\n        children: /*#__PURE__*/_jsxDEV(HistoryIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    if (row.stato_bobina === 'Disponibile') bgColor = 'rgba(76, 175, 80, 0.1)';else if (row.stato_bobina === 'In Uso') bgColor = 'rgba(255, 152, 0, 0.1)';else if (row.stato_bobina === 'Esaurita') bgColor = 'rgba(244, 67, 54, 0.1)';\n    return /*#__PURE__*/_jsxDEV(TableRow, {\n      sx: {\n        backgroundColor: bgColor,\n        '&:hover': {\n          backgroundColor: 'rgba(0, 0, 0, 0.04)'\n        }\n      },\n      children: columns.map(column => /*#__PURE__*/_jsxDEV(TableCell, {\n        align: column.align || 'left',\n        sx: column.cellStyle,\n        children: column.renderCell ? column.renderCell(row) : row[column.field]\n      }, column.field, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this))\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Calcola le statistiche\n  const calculateStats = () => {\n    if (!filteredBobine.length) return null;\n    const totalBobine = filteredBobine.length;\n    const disponibili = filteredBobine.filter(b => b.stato_bobina === 'Disponibile').length;\n    const inUso = filteredBobine.filter(b => b.stato_bobina === 'In Uso').length;\n    const esaurite = filteredBobine.filter(b => b.stato_bobina === 'Esaurita').length;\n    const metriTotali = filteredBobine.reduce((sum, b) => sum + (b.metri_totali || 0), 0);\n    const metriResidui = filteredBobine.reduce((sum, b) => sum + (b.metri_residui || 0), 0);\n    const metriUtilizzati = metriTotali - metriResidui;\n    const percentualeUtilizzo = metriTotali ? Math.round(metriUtilizzati / metriTotali * 100) : 0;\n    return {\n      totalBobine,\n      disponibili,\n      inUso,\n      esaurite,\n      metriTotali,\n      metriResidui,\n      metriUtilizzati,\n      percentualeUtilizzo\n    };\n  };\n  const stats = calculateStats();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [stats && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        p: 2,\n        bgcolor: 'background.paper',\n        borderRadius: 1,\n        boxShadow: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: [\"Statistiche (\", filteredBobine.length, \" bobine visualizzate)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Utilizzo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.percentualeUtilizzo, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Disponibili\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"success.main\",\n            children: stats.disponibili\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"In uso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"warning.main\",\n            children: stats.inUso\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Esaurite\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"error.main\",\n            children: stats.esaurite\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Metri totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.metriTotali.toFixed(1), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Metri residui\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.metriResidui.toFixed(1), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Metri utilizzati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.metriUtilizzati.toFixed(1), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n      data: bobine,\n      columns: columns,\n      onFilteredDataChange: handleFilteredDataChange,\n      loading: loading,\n      emptyMessage: \"Nessuna bobina disponibile\",\n      renderRow: renderRow\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 231,\n    columnNumber: 5\n  }, this);\n};\n_s(BobineFilterableTable, \"f3TlITpreTFylm3or0YT9wPW5MI=\");\n_c = BobineFilterableTable;\nexport default BobineFilterableTable;\nvar _c;\n$RefreshReg$(_c, \"BobineFilterableTable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Chip", "TableRow", "TableCell", "IconButton", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "FilterableTable", "jsxDEV", "_jsxDEV", "BobineFilterableTable", "bobine", "loading", "onFilteredDataChange", "onEdit", "onDelete", "onViewHistory", "_s", "filteredBobine", "setFilteredBobine", "handleFilteredDataChange", "data", "columns", "field", "headerName", "dataType", "headerStyle", "fontWeight", "align", "cellStyle", "textAlign", "renderCell", "row", "metri_totali", "toFixed", "metri_residui", "color", "stato_bobina", "label", "size", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disableFilter", "disableSort", "sx", "display", "justifyContent", "children", "onClick", "title", "fontSize", "disabled", "renderRow", "index", "bgColor", "backgroundColor", "map", "column", "calculateStats", "length", "totalBobine", "disponibili", "filter", "b", "inUso", "esaurite", "metriTotali", "reduce", "sum", "metriResidui", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "percentuale<PERSON><PERSON><PERSON><PERSON>", "Math", "round", "stats", "mb", "p", "bgcolor", "borderRadius", "boxShadow", "gutterBottom", "flexWrap", "gap", "emptyMessage", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/BobineFilterableTable.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, IconButton } from '@mui/material';\nimport { Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\n\n/**\n * Componente per visualizzare la lista delle bobine con filtri in stile Excel\n * \n * @param {Object} props - Proprietà del componente\n * @param {Array} props.bobine - Lista delle bobine da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {Function} props.onEdit - Funzione chiamata quando si vuole modificare una bobina\n * @param {Function} props.onDelete - Funzione chiamata quando si vuole eliminare una bobina\n * @param {Function} props.onViewHistory - Funzione chiamata quando si vuole visualizzare lo storico di una bobina\n */\nconst BobineFilterableTable = ({ \n  bobine = [], \n  loading = false, \n  onFilteredDataChange = null,\n  onEdit = null,\n  onDelete = null,\n  onViewHistory = null\n}) => {\n  const [filteredBobine, setFilteredBobine] = useState(bobine);\n  \n  // Aggiorna i dati filtrati quando cambiano le bobine\n  useEffect(() => {\n    setFilteredBobine(bobine);\n  }, [bobine]);\n  \n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = (data) => {\n    setFilteredBobine(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n  \n  // Definizione delle colonne\n  const columns = [\n    { \n      field: 'numero_bobina', \n      headerName: 'Numero', \n      dataType: 'text',\n      headerStyle: { fontWeight: 'bold' }\n    },\n    { \n      field: 'utility', \n      headerName: 'Utility', \n      dataType: 'text' \n    },\n    { \n      field: 'tipologia', \n      headerName: 'Tipologia', \n      dataType: 'text' \n    },\n    { \n      field: 'n_conduttori', \n      headerName: 'N° Cond.', \n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' }\n    },\n    { \n      field: 'sezione', \n      headerName: 'Sezione', \n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' }\n    },\n    { \n      field: 'metri_totali', \n      headerName: 'Metri Totali', \n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metri_totali ? row.metri_totali.toFixed(1) : '0'\n    },\n    { \n      field: 'metri_residui', \n      headerName: 'Metri Residui', \n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metri_residui ? row.metri_residui.toFixed(1) : '0'\n    },\n    { \n      field: 'stato_bobina', \n      headerName: 'Stato', \n      dataType: 'text',\n      renderCell: (row) => {\n        let color = 'default';\n        if (row.stato_bobina === 'Disponibile') color = 'success';\n        else if (row.stato_bobina === 'In Uso') color = 'warning';\n        else if (row.stato_bobina === 'Esaurita') color = 'error';\n        \n        return (\n          <Chip \n            label={row.stato_bobina || 'N/D'} \n            size=\"small\" \n            color={color}\n            variant=\"outlined\"\n          />\n        );\n      }\n    },\n    { \n      field: 'ubicazione_bobina', \n      headerName: 'Ubicazione', \n      dataType: 'text' \n    },\n    { \n      field: 'fornitore', \n      headerName: 'Fornitore', \n      dataType: 'text' \n    },\n    { \n      field: 'n_DDT', \n      headerName: 'N° DDT', \n      dataType: 'text' \n    },\n    { \n      field: 'data_DDT', \n      headerName: 'Data DDT', \n      dataType: 'text' \n    },\n    { \n      field: 'actions', \n      headerName: 'Azioni', \n      disableFilter: true,\n      disableSort: true,\n      align: 'center',\n      renderCell: (row) => (\n        <Box sx={{ display: 'flex', justifyContent: 'center' }}>\n          {onEdit && (\n            <IconButton \n              size=\"small\" \n              onClick={() => onEdit(row)}\n              title=\"Modifica bobina\"\n              color=\"primary\"\n            >\n              <EditIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n          {onDelete && (\n            <IconButton \n              size=\"small\" \n              onClick={() => onDelete(row)}\n              title=\"Elimina bobina\"\n              color=\"error\"\n              disabled={row.stato_bobina !== 'Disponibile' || row.metri_residui !== row.metri_totali}\n            >\n              <DeleteIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n          {onViewHistory && (\n            <IconButton \n              size=\"small\" \n              onClick={() => onViewHistory(row)}\n              title=\"Visualizza storico utilizzo\"\n              color=\"info\"\n            >\n              <HistoryIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n        </Box>\n      )\n    }\n  ];\n  \n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    if (row.stato_bobina === 'Disponibile') bgColor = 'rgba(76, 175, 80, 0.1)';\n    else if (row.stato_bobina === 'In Uso') bgColor = 'rgba(255, 152, 0, 0.1)';\n    else if (row.stato_bobina === 'Esaurita') bgColor = 'rgba(244, 67, 54, 0.1)';\n    \n    return (\n      <TableRow \n        key={index}\n        sx={{ \n          backgroundColor: bgColor,\n          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell \n            key={column.field} \n            align={column.align || 'left'}\n            sx={column.cellStyle}\n          >\n            {column.renderCell ? column.renderCell(row) : row[column.field]}\n          </TableCell>\n        ))}\n      </TableRow>\n    );\n  };\n  \n  // Calcola le statistiche\n  const calculateStats = () => {\n    if (!filteredBobine.length) return null;\n    \n    const totalBobine = filteredBobine.length;\n    const disponibili = filteredBobine.filter(b => b.stato_bobina === 'Disponibile').length;\n    const inUso = filteredBobine.filter(b => b.stato_bobina === 'In Uso').length;\n    const esaurite = filteredBobine.filter(b => b.stato_bobina === 'Esaurita').length;\n    \n    const metriTotali = filteredBobine.reduce((sum, b) => sum + (b.metri_totali || 0), 0);\n    const metriResidui = filteredBobine.reduce((sum, b) => sum + (b.metri_residui || 0), 0);\n    const metriUtilizzati = metriTotali - metriResidui;\n    \n    const percentualeUtilizzo = metriTotali ? Math.round((metriUtilizzati / metriTotali) * 100) : 0;\n    \n    return {\n      totalBobine,\n      disponibili,\n      inUso,\n      esaurite,\n      metriTotali,\n      metriResidui,\n      metriUtilizzati,\n      percentualeUtilizzo\n    };\n  };\n  \n  const stats = calculateStats();\n  \n  return (\n    <Box>\n      {stats && (\n        <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Statistiche ({filteredBobine.length} bobine visualizzate)\n          </Typography>\n          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Utilizzo</Typography>\n              <Typography variant=\"h6\">{stats.percentualeUtilizzo}%</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Disponibili</Typography>\n              <Typography variant=\"h6\" color=\"success.main\">{stats.disponibili}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">In uso</Typography>\n              <Typography variant=\"h6\" color=\"warning.main\">{stats.inUso}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Esaurite</Typography>\n              <Typography variant=\"h6\" color=\"error.main\">{stats.esaurite}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri totali</Typography>\n              <Typography variant=\"h6\">{stats.metriTotali.toFixed(1)} m</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri residui</Typography>\n              <Typography variant=\"h6\">{stats.metriResidui.toFixed(1)} m</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri utilizzati</Typography>\n              <Typography variant=\"h6\">{stats.metriUtilizzati.toFixed(1)} m</Typography>\n            </Box>\n          </Box>\n        </Box>\n      )}\n      \n      <FilterableTable\n        data={bobine}\n        columns={columns}\n        onFilteredDataChange={handleFilteredDataChange}\n        loading={loading}\n        emptyMessage=\"Nessuna bobina disponibile\"\n        renderRow={renderRow}\n      />\n    </Box>\n  );\n};\n\nexport default BobineFilterableTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,eAAe;AACtF,SAASC,IAAI,IAAIC,QAAQ,EAAEC,MAAM,IAAIC,UAAU,EAAEC,OAAO,IAAIC,WAAW,QAAQ,qBAAqB;AACpG,OAAOC,eAAe,MAAM,2BAA2B;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAAAC,MAAA,IAAAC,OAAA;AAWA,MAAMC,qBAAqB,GAAGA,CAAC;EAC7BC,MAAM,GAAG,EAAE;EACXC,OAAO,GAAG,KAAK;EACfC,oBAAoB,GAAG,IAAI;EAC3BC,MAAM,GAAG,IAAI;EACbC,QAAQ,GAAG,IAAI;EACfC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAACkB,MAAM,CAAC;;EAE5D;EACAjB,SAAS,CAAC,MAAM;IACdyB,iBAAiB,CAACR,MAAM,CAAC;EAC3B,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMS,wBAAwB,GAAIC,IAAI,IAAK;IACzCF,iBAAiB,CAACE,IAAI,CAAC;IACvB,IAAIR,oBAAoB,EAAE;MACxBA,oBAAoB,CAACQ,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE;MAAEC,UAAU,EAAE;IAAO;EACpC,CAAC,EACD;IACEJ,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE,QAAQ;IAClBG,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ;EAClC,CAAC,EACD;IACEP,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE,QAAQ;IAClBG,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ;EAClC,CAAC,EACD;IACEP,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,cAAc;IAC1BC,QAAQ,EAAE,QAAQ;IAClBG,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCC,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACC,YAAY,GAAGD,GAAG,CAACC,YAAY,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG;EACxE,CAAC,EACD;IACEX,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,eAAe;IAC3BC,QAAQ,EAAE,QAAQ;IAClBG,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCC,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACG,aAAa,GAAGH,GAAG,CAACG,aAAa,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG;EAC1E,CAAC,EACD;IACEX,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,OAAO;IACnBC,QAAQ,EAAE,MAAM;IAChBM,UAAU,EAAGC,GAAG,IAAK;MACnB,IAAII,KAAK,GAAG,SAAS;MACrB,IAAIJ,GAAG,CAACK,YAAY,KAAK,aAAa,EAAED,KAAK,GAAG,SAAS,CAAC,KACrD,IAAIJ,GAAG,CAACK,YAAY,KAAK,QAAQ,EAAED,KAAK,GAAG,SAAS,CAAC,KACrD,IAAIJ,GAAG,CAACK,YAAY,KAAK,UAAU,EAAED,KAAK,GAAG,OAAO;MAEzD,oBACE3B,OAAA,CAACZ,IAAI;QACHyC,KAAK,EAAEN,GAAG,CAACK,YAAY,IAAI,KAAM;QACjCE,IAAI,EAAC,OAAO;QACZH,KAAK,EAAEA,KAAM;QACbI,OAAO,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAEN;EACF,CAAC,EACD;IACErB,KAAK,EAAE,mBAAmB;IAC1BC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,UAAU;IACjBC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,QAAQ;IACpBqB,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBlB,KAAK,EAAE,QAAQ;IACfG,UAAU,EAAGC,GAAG,iBACdvB,OAAA,CAACd,GAAG;MAACoD,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAC,QAAA,GACpDpC,MAAM,iBACLL,OAAA,CAACT,UAAU;QACTuC,IAAI,EAAC,OAAO;QACZY,OAAO,EAAEA,CAAA,KAAMrC,MAAM,CAACkB,GAAG,CAAE;QAC3BoB,KAAK,EAAC,iBAAiB;QACvBhB,KAAK,EAAC,SAAS;QAAAc,QAAA,eAEfzC,OAAA,CAACP,QAAQ;UAACmD,QAAQ,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACb,EACA7B,QAAQ,iBACPN,OAAA,CAACT,UAAU;QACTuC,IAAI,EAAC,OAAO;QACZY,OAAO,EAAEA,CAAA,KAAMpC,QAAQ,CAACiB,GAAG,CAAE;QAC7BoB,KAAK,EAAC,gBAAgB;QACtBhB,KAAK,EAAC,OAAO;QACbkB,QAAQ,EAAEtB,GAAG,CAACK,YAAY,KAAK,aAAa,IAAIL,GAAG,CAACG,aAAa,KAAKH,GAAG,CAACC,YAAa;QAAAiB,QAAA,eAEvFzC,OAAA,CAACL,UAAU;UAACiD,QAAQ,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CACb,EACA5B,aAAa,iBACZP,OAAA,CAACT,UAAU;QACTuC,IAAI,EAAC,OAAO;QACZY,OAAO,EAAEA,CAAA,KAAMnC,aAAa,CAACgB,GAAG,CAAE;QAClCoB,KAAK,EAAC,6BAA6B;QACnChB,KAAK,EAAC,MAAM;QAAAc,QAAA,eAEZzC,OAAA,CAACH,WAAW;UAAC+C,QAAQ,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAET,CAAC,CACF;;EAED;EACA,MAAMW,SAAS,GAAGA,CAACvB,GAAG,EAAEwB,KAAK,KAAK;IAChC;IACA,IAAIC,OAAO,GAAG,SAAS;IACvB,IAAIzB,GAAG,CAACK,YAAY,KAAK,aAAa,EAAEoB,OAAO,GAAG,wBAAwB,CAAC,KACtE,IAAIzB,GAAG,CAACK,YAAY,KAAK,QAAQ,EAAEoB,OAAO,GAAG,wBAAwB,CAAC,KACtE,IAAIzB,GAAG,CAACK,YAAY,KAAK,UAAU,EAAEoB,OAAO,GAAG,wBAAwB;IAE5E,oBACEhD,OAAA,CAACX,QAAQ;MAEPiD,EAAE,EAAE;QACFW,eAAe,EAAED,OAAO;QACxB,SAAS,EAAE;UAAEC,eAAe,EAAE;QAAsB;MACtD,CAAE;MAAAR,QAAA,EAED5B,OAAO,CAACqC,GAAG,CAAEC,MAAM,iBAClBnD,OAAA,CAACV,SAAS;QAER6B,KAAK,EAAEgC,MAAM,CAAChC,KAAK,IAAI,MAAO;QAC9BmB,EAAE,EAAEa,MAAM,CAAC/B,SAAU;QAAAqB,QAAA,EAEpBU,MAAM,CAAC7B,UAAU,GAAG6B,MAAM,CAAC7B,UAAU,CAACC,GAAG,CAAC,GAAGA,GAAG,CAAC4B,MAAM,CAACrC,KAAK;MAAC,GAJ1DqC,MAAM,CAACrC,KAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKR,CACZ;IAAC,GAdGY,KAAK;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeF,CAAC;EAEf,CAAC;;EAED;EACA,MAAMiB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAAC3C,cAAc,CAAC4C,MAAM,EAAE,OAAO,IAAI;IAEvC,MAAMC,WAAW,GAAG7C,cAAc,CAAC4C,MAAM;IACzC,MAAME,WAAW,GAAG9C,cAAc,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7B,YAAY,KAAK,aAAa,CAAC,CAACyB,MAAM;IACvF,MAAMK,KAAK,GAAGjD,cAAc,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7B,YAAY,KAAK,QAAQ,CAAC,CAACyB,MAAM;IAC5E,MAAMM,QAAQ,GAAGlD,cAAc,CAAC+C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7B,YAAY,KAAK,UAAU,CAAC,CAACyB,MAAM;IAEjF,MAAMO,WAAW,GAAGnD,cAAc,CAACoD,MAAM,CAAC,CAACC,GAAG,EAAEL,CAAC,KAAKK,GAAG,IAAIL,CAAC,CAACjC,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACrF,MAAMuC,YAAY,GAAGtD,cAAc,CAACoD,MAAM,CAAC,CAACC,GAAG,EAAEL,CAAC,KAAKK,GAAG,IAAIL,CAAC,CAAC/B,aAAa,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACvF,MAAMsC,eAAe,GAAGJ,WAAW,GAAGG,YAAY;IAElD,MAAME,mBAAmB,GAAGL,WAAW,GAAGM,IAAI,CAACC,KAAK,CAAEH,eAAe,GAAGJ,WAAW,GAAI,GAAG,CAAC,GAAG,CAAC;IAE/F,OAAO;MACLN,WAAW;MACXC,WAAW;MACXG,KAAK;MACLC,QAAQ;MACRC,WAAW;MACXG,YAAY;MACZC,eAAe;MACfC;IACF,CAAC;EACH,CAAC;EAED,MAAMG,KAAK,GAAGhB,cAAc,CAAC,CAAC;EAE9B,oBACEpD,OAAA,CAACd,GAAG;IAAAuD,QAAA,GACD2B,KAAK,iBACJpE,OAAA,CAACd,GAAG;MAACoD,EAAE,EAAE;QAAE+B,EAAE,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE,kBAAkB;QAAEC,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAE,CAAE;MAAAhC,QAAA,gBACnFzC,OAAA,CAACb,UAAU;QAAC4C,OAAO,EAAC,WAAW;QAAC2C,YAAY;QAAAjC,QAAA,GAAC,eAC9B,EAAChC,cAAc,CAAC4C,MAAM,EAAC,uBACtC;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbnC,OAAA,CAACd,GAAG;QAACoD,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEoC,QAAQ,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAnC,QAAA,gBACrDzC,OAAA,CAACd,GAAG;UAAAuD,QAAA,gBACFzC,OAAA,CAACb,UAAU;YAAC4C,OAAO,EAAC,OAAO;YAACJ,KAAK,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAAQ;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxEnC,OAAA,CAACb,UAAU;YAAC4C,OAAO,EAAC,IAAI;YAAAU,QAAA,GAAE2B,KAAK,CAACH,mBAAmB,EAAC,GAAC;UAAA;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACNnC,OAAA,CAACd,GAAG;UAAAuD,QAAA,gBACFzC,OAAA,CAACb,UAAU;YAAC4C,OAAO,EAAC,OAAO;YAACJ,KAAK,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAAW;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC3EnC,OAAA,CAACb,UAAU;YAAC4C,OAAO,EAAC,IAAI;YAACJ,KAAK,EAAC,cAAc;YAAAc,QAAA,EAAE2B,KAAK,CAACb;UAAW;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eACNnC,OAAA,CAACd,GAAG;UAAAuD,QAAA,gBACFzC,OAAA,CAACb,UAAU;YAAC4C,OAAO,EAAC,OAAO;YAACJ,KAAK,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtEnC,OAAA,CAACb,UAAU;YAAC4C,OAAO,EAAC,IAAI;YAACJ,KAAK,EAAC,cAAc;YAAAc,QAAA,EAAE2B,KAAK,CAACV;UAAK;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACNnC,OAAA,CAACd,GAAG;UAAAuD,QAAA,gBACFzC,OAAA,CAACb,UAAU;YAAC4C,OAAO,EAAC,OAAO;YAACJ,KAAK,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAAQ;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxEnC,OAAA,CAACb,UAAU;YAAC4C,OAAO,EAAC,IAAI;YAACJ,KAAK,EAAC,YAAY;YAAAc,QAAA,EAAE2B,KAAK,CAACT;UAAQ;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,eACNnC,OAAA,CAACd,GAAG;UAAAuD,QAAA,gBACFzC,OAAA,CAACb,UAAU;YAAC4C,OAAO,EAAC,OAAO;YAACJ,KAAK,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAAY;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5EnC,OAAA,CAACb,UAAU;YAAC4C,OAAO,EAAC,IAAI;YAAAU,QAAA,GAAE2B,KAAK,CAACR,WAAW,CAACnC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACNnC,OAAA,CAACd,GAAG;UAAAuD,QAAA,gBACFzC,OAAA,CAACb,UAAU;YAAC4C,OAAO,EAAC,OAAO;YAACJ,KAAK,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAAa;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7EnC,OAAA,CAACb,UAAU;YAAC4C,OAAO,EAAC,IAAI;YAAAU,QAAA,GAAE2B,KAAK,CAACL,YAAY,CAACtC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eACNnC,OAAA,CAACd,GAAG;UAAAuD,QAAA,gBACFzC,OAAA,CAACb,UAAU;YAAC4C,OAAO,EAAC,OAAO;YAACJ,KAAK,EAAC,gBAAgB;YAAAc,QAAA,EAAC;UAAgB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChFnC,OAAA,CAACb,UAAU;YAAC4C,OAAO,EAAC,IAAI;YAAAU,QAAA,GAAE2B,KAAK,CAACJ,eAAe,CAACvC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDnC,OAAA,CAACF,eAAe;MACdc,IAAI,EAAEV,MAAO;MACbW,OAAO,EAAEA,OAAQ;MACjBT,oBAAoB,EAAEO,wBAAyB;MAC/CR,OAAO,EAAEA,OAAQ;MACjB0E,YAAY,EAAC,4BAA4B;MACzC/B,SAAS,EAAEA;IAAU;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC3B,EAAA,CAvQIP,qBAAqB;AAAA6E,EAAA,GAArB7E,qBAAqB;AAyQ3B,eAAeA,qBAAqB;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}