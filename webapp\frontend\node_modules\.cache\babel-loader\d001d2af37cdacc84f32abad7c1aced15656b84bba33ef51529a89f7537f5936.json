{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Alert, CircularProgress, Tooltip, Grid, List, ListItem, ListItemText, Divider } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Visibility as ViewIcon, Assignment as AssignIcon, Refresh as RefreshIcon, People as PeopleIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport GestioneResponsabili from '../responsabili/GestioneResponsabili';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ComandeList = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  const [comande, setComande] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [dialogMode, setDialogMode] = useState('edit'); // 'edit', 'view', 'assign'\n  const [formData, setFormData] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n  const [statistiche, setStatistiche] = useState(null);\n  const [caviAssegnazione, setCaviAssegnazione] = useState('');\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n  const [openGestioneResponsabili, setOpenGestioneResponsabili] = useState(false);\n\n  // Carica le comande al mount del componente\n  useEffect(() => {\n    if (cantiereId) {\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]);\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const response = await comandeService.getComande(cantiereId);\n      setComande(response.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n  const handleOpenDialog = (mode, comanda = null) => {\n    setDialogMode(mode);\n    setSelectedComanda(comanda);\n    if (mode === 'edit' && comanda) {\n      setFormData({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        priorita: comanda.priorita || 'NORMALE',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n    setOpenDialog(true);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedComanda(null);\n    setCaviAssegnazione('');\n    setFormData({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n  };\n  const handleSubmit = async () => {\n    try {\n      if (dialogMode === 'edit') {\n        // Modifica comanda esistente\n        const response = await comandeService.updateComanda(selectedComanda.codice_comanda, formData);\n        console.log('Comanda aggiornata:', response);\n      } else if (dialogMode === 'assign') {\n        // Assegnazione cavi\n        if (!caviAssegnazione.trim()) {\n          setError('Inserisci almeno un ID cavo');\n          return;\n        }\n        const listaIdCavi = caviAssegnazione.split(',').map(id => id.trim()).filter(id => id);\n        await comandeService.assegnaCavi(selectedComanda.codice_comanda, listaIdCavi);\n      }\n      handleCloseDialog();\n      loadComande();\n      loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n  const handleDelete = async codiceComanda => {\n    if (window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      try {\n        await comandeService.deleteComanda(codiceComanda);\n        loadComande();\n        loadStatistiche();\n      } catch (err) {\n        console.error('Errore nell\\'eliminazione:', err);\n        setError('Errore nell\\'eliminazione della comanda');\n      }\n    }\n  };\n  const getStatoColor = stato => {\n    switch (stato) {\n      case 'CREATA':\n        return 'default';\n      case 'ASSEGNATA':\n        return 'primary';\n      case 'IN_CORSO':\n        return 'warning';\n      case 'COMPLETATA':\n        return 'success';\n      case 'ANNULLATA':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE':\n        return 'Certificazione';\n      case 'TESTING':\n        return 'Testing/Certificazione';\n      default:\n        return tipo;\n    }\n  };\n  const getPrioritaColor = priorita => {\n    switch (priorita) {\n      case 'BASSA':\n        return 'default';\n      case 'NORMALE':\n        return 'primary';\n      case 'ALTA':\n        return 'warning';\n      case 'URGENTE':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: statistiche && /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Totale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.totale_comande\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Create\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_create\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Assegnate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_assegnate\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"In Corso\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_in_corso\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Completate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_completate\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"% Completamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: [statistiche.percentuale_completamento_medio.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 2,\n      flexWrap: \"wrap\",\n      gap: 1,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 1,\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 24\n          }, this),\n          onClick: () => setOpenCreaConCavi(true),\n          color: \"primary\",\n          children: \"Nuova Comanda\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 24\n          }, this),\n          onClick: () => setOpenGestioneResponsabili(true),\n          color: \"secondary\",\n          children: \"Gestisci Responsabili\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 24\n          }, this),\n          onClick: () => {\n            if (comande.length === 0) {\n              setError('Nessuna comanda disponibile per l\\'assegnazione');\n              return;\n            }\n            // Apri dialog per selezionare comanda\n            setError('Seleziona una comanda dalla tabella e clicca sull\\'icona \"Assegna Cavi\"');\n          },\n          disabled: comande.length === 0,\n          children: \"Assegna Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 22\n        }, this),\n        onClick: () => {\n          loadComande();\n          loadStatistiche();\n        },\n        children: \"Aggiorna\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Codice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Tipo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Priorit\\xE0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Data Creazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Cavi Assegnati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: comande.map(comanda => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"bold\",\n                children: comanda.codice_comanda\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: getTipoComandaLabel(comanda.tipo_comanda),\n                size: \"small\",\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: comanda.priorita || 'NORMALE',\n                size: \"small\",\n                color: getPrioritaColor(comanda.priorita || 'NORMALE')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.responsabile\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: new Date(comanda.data_creazione).toLocaleDateString('it-IT')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: comanda.stato,\n                color: getStatoColor(comanda.stato),\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.numero_cavi_assegnati || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.percentuale_completamento ? `${comanda.percentuale_completamento.toFixed(1)}%` : '0%'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Visualizza\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('view', comanda),\n                  children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Modifica\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('edit', comanda),\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Assegna Cavi\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('assign', comanda),\n                  color: \"primary\",\n                  children: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Elimina\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleDelete(comanda.codice_comanda),\n                  color: \"error\",\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 17\n            }, this)]\n          }, comanda.codice_comanda, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), comande.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Box, {\n      textAlign: \"center\",\n      py: 4,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"textSecondary\",\n        children: \"Nessuna comanda trovata\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"textSecondary\",\n        children: \"Clicca su \\\"Nuova Comanda\\\" per iniziare\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: handleCloseDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [dialogMode === 'edit' && 'Modifica Comanda', dialogMode === 'view' && 'Dettagli Comanda', dialogMode === 'assign' && `Assegna Cavi - ${selectedComanda === null || selectedComanda === void 0 ? void 0 : selectedComanda.codice_comanda}`]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 1\n          },\n          children: dialogMode === 'assign' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mb: 2\n              },\n              children: \"Inserisci gli ID dei cavi da assegnare alla comanda, separati da virgola.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavi (separati da virgola)\",\n              value: caviAssegnazione,\n              onChange: e => setCaviAssegnazione(e.target.value),\n              margin: \"normal\",\n              placeholder: \"es: CAVO001, CAVO002, CAVO003\",\n              helperText: \"Esempio: CAVO001, CAVO002, CAVO003\",\n              multiline: true,\n              rows: 3\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : dialogMode === 'view' && selectedComanda ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(List, {\n              children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Codice Comanda\",\n                  secondary: selectedComanda.codice_comanda\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Tipo\",\n                  secondary: getTipoComandaLabel(selectedComanda.tipo_comanda)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Stato\",\n                  secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedComanda.stato,\n                    color: getStatoColor(selectedComanda.stato),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Descrizione\",\n                  secondary: selectedComanda.descrizione || 'Nessuna descrizione'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Priorit\\xE0\",\n                  secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedComanda.priorita || 'NORMALE',\n                    color: getPrioritaColor(selectedComanda.priorita || 'NORMALE'),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Responsabile\",\n                  secondary: selectedComanda.responsabile || 'Non assegnato'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 19\n              }, this), selectedComanda.note_capo_cantiere && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Note Capo Cantiere\",\n                    secondary: selectedComanda.note_capo_cantiere\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Data Creazione\",\n                  secondary: new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 19\n              }, this), selectedComanda.data_scadenza && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Data Scadenza\",\n                    secondary: new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Cavi Assegnati\",\n                  secondary: selectedComanda.numero_cavi_assegnati || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Completamento\",\n                  secondary: `${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 574,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 17\n            }, this)\n          }, void 0, false) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Tipo Comanda\",\n              value: formData.tipo_comanda,\n              onChange: e => setFormData({\n                ...formData,\n                tipo_comanda: e.target.value\n              }),\n              margin: \"normal\",\n              disabled: dialogMode === 'view',\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"POSA\",\n                children: \"Posa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_PARTENZA\",\n                children: \"Collegamento Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_ARRIVO\",\n                children: \"Collegamento Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"CERTIFICAZIONE\",\n                children: \"Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"TESTING\",\n                children: \"Testing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Priorit\\xE0\",\n              value: formData.priorita,\n              onChange: e => setFormData({\n                ...formData,\n                priorita: e.target.value\n              }),\n              margin: \"normal\",\n              disabled: dialogMode === 'view',\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BASSA\",\n                children: \"Bassa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NORMALE\",\n                children: \"Normale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"ALTA\",\n                children: \"Alta\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"URGENTE\",\n                children: \"Urgente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione\",\n              value: formData.descrizione,\n              onChange: e => setFormData({\n                ...formData,\n                descrizione: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 3,\n              disabled: dialogMode === 'view'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Responsabile\",\n              value: formData.responsabile,\n              onChange: e => setFormData({\n                ...formData,\n                responsabile: e.target.value\n              }),\n              margin: \"normal\",\n              disabled: dialogMode === 'view',\n              required: true,\n              helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note Capo Cantiere\",\n              value: formData.note_capo_cantiere,\n              onChange: e => setFormData({\n                ...formData,\n                note_capo_cantiere: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 2,\n              disabled: dialogMode === 'view',\n              helperText: \"Istruzioni specifiche per il responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Data Scadenza\",\n              type: \"date\",\n              value: formData.data_scadenza,\n              onChange: e => setFormData({\n                ...formData,\n                data_scadenza: e.target.value\n              }),\n              margin: \"normal\",\n              InputLabelProps: {\n                shrink: true\n              },\n              disabled: dialogMode === 'view'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: dialogMode === 'view' ? 'Chiudi' : 'Annulla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 11\n        }, this), dialogMode !== 'view' && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          children: dialogMode === 'edit' ? 'Salva' : dialogMode === 'assign' ? 'Assegna Cavi' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: () => {\n        loadComande();\n        loadStatistiche();\n        setOpenCreaConCavi(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 208,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeList, \"L+z/eZtQezZrUmjJMoAl89S0paY=\");\n_c = ComandeList;\nexport default ComandeList;\nvar _c;\n$RefreshReg$(_c, \"ComandeList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON><PERSON>", "Grid", "List", "ListItem", "ListItemText", "Divider", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Visibility", "ViewIcon", "Assignment", "AssignIcon", "Refresh", "RefreshIcon", "People", "PeopleIcon", "comandeService", "CreaComandaConCavi", "GestioneResponsabili", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ComandeList", "cantiereId", "cantiereName", "_s", "comande", "setComande", "loading", "setLoading", "error", "setError", "openDialog", "setOpenDialog", "selectedComanda", "setSelectedComanda", "dialogMode", "setDialogMode", "formData", "setFormData", "tipo_comanda", "descrizione", "responsabile", "data_scadenza", "priorita", "note_capo_cantiere", "statistiche", "setStatistiche", "caviAssegnazione", "setCaviAssegnazione", "openCreaConCavi", "setOpenCreaConCavi", "openGestioneResponsabili", "setOpenGestioneResponsabili", "loadComande", "loadStatistiche", "response", "getComande", "err", "console", "stats", "getStatisticheComande", "handleOpenDialog", "mode", "comanda", "handleCloseDialog", "handleSubmit", "updateComanda", "codice_comanda", "log", "trim", "listaIdCavi", "split", "map", "id", "filter", "assegnaCavi", "handleDelete", "codiceComanda", "window", "confirm", "deleteComanda", "getStatoColor", "stato", "getTipoComandaLabel", "tipo", "getPrioritaColor", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "container", "spacing", "item", "xs", "sm", "md", "color", "gutterBottom", "variant", "totale_comande", "comande_create", "comande_assegnate", "comande_in_corso", "comande_completate", "percentuale_completamento_medio", "toFixed", "flexWrap", "gap", "startIcon", "onClick", "length", "disabled", "severity", "sx", "component", "fontWeight", "label", "size", "Date", "data_creazione", "toLocaleDateString", "numero_cavi_assegnati", "percentuale_completamento", "title", "textAlign", "py", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "value", "onChange", "e", "target", "margin", "placeholder", "helperText", "multiline", "rows", "primary", "secondary", "select", "required", "type", "InputLabelProps", "shrink", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  CardContent,\n  Typo<PERSON>,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  Tooltip,\n  Grid,\n  List,\n  ListItem,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Visibility as ViewIcon,\n  Assignment as AssignIcon,\n  Refresh as RefreshIcon,\n  People as PeopleIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport GestioneResponsabili from '../responsabili/GestioneResponsabili';\n\nconst ComandeList = ({ cantiereId, cantiereName }) => {\n  const [comande, setComande] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [dialogMode, setDialogMode] = useState('edit'); // 'edit', 'view', 'assign'\n  const [formData, setFormData] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n  const [statistiche, setStatistiche] = useState(null);\n  const [caviAssegnazione, setCaviAssegnazione] = useState('');\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n  const [openGestioneResponsabili, setOpenGestioneResponsabili] = useState(false);\n\n  // Carica le comande al mount del componente\n  useEffect(() => {\n    if (cantiereId) {\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]);\n\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const response = await comandeService.getComande(cantiereId);\n      setComande(response.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n\n  const handleOpenDialog = (mode, comanda = null) => {\n    setDialogMode(mode);\n    setSelectedComanda(comanda);\n\n    if (mode === 'edit' && comanda) {\n      setFormData({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        priorita: comanda.priorita || 'NORMALE',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n\n    setOpenDialog(true);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedComanda(null);\n    setCaviAssegnazione('');\n    setFormData({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n  };\n\n  const handleSubmit = async () => {\n    try {\n      if (dialogMode === 'edit') {\n        // Modifica comanda esistente\n        const response = await comandeService.updateComanda(selectedComanda.codice_comanda, formData);\n        console.log('Comanda aggiornata:', response);\n      } else if (dialogMode === 'assign') {\n        // Assegnazione cavi\n        if (!caviAssegnazione.trim()) {\n          setError('Inserisci almeno un ID cavo');\n          return;\n        }\n\n        const listaIdCavi = caviAssegnazione.split(',').map(id => id.trim()).filter(id => id);\n        await comandeService.assegnaCavi(selectedComanda.codice_comanda, listaIdCavi);\n      }\n\n      handleCloseDialog();\n      loadComande();\n      loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n\n  const handleDelete = async (codiceComanda) => {\n    if (window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      try {\n        await comandeService.deleteComanda(codiceComanda);\n        loadComande();\n        loadStatistiche();\n      } catch (err) {\n        console.error('Errore nell\\'eliminazione:', err);\n        setError('Errore nell\\'eliminazione della comanda');\n      }\n    }\n  };\n\n  const getStatoColor = (stato) => {\n    switch (stato) {\n      case 'CREATA': return 'default';\n      case 'ASSEGNATA': return 'primary';\n      case 'IN_CORSO': return 'warning';\n      case 'COMPLETATA': return 'success';\n      case 'ANNULLATA': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE': return 'Certificazione';\n      case 'TESTING': return 'Testing/Certificazione';\n      default: return tipo;\n    }\n  };\n\n  const getPrioritaColor = (priorita) => {\n    switch (priorita) {\n      case 'BASSA': return 'default';\n      case 'NORMALE': return 'primary';\n      case 'ALTA': return 'warning';\n      case 'URGENTE': return 'error';\n      default: return 'default';\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Header con statistiche */}\n      <Box mb={3}>\n        \n        {statistiche && (\n          <Grid container spacing={2} mb={2}>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Totale\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.totale_comande}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Create\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_create}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Assegnate\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_assegnate}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    In Corso\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_in_corso}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Completate\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_completate}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    % Completamento\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.percentuale_completamento_medio.toFixed(1)}%\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        )}\n      </Box>\n\n      {/* Toolbar */}\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2} flexWrap=\"wrap\" gap={1}>\n        <Box display=\"flex\" gap={1} flexWrap=\"wrap\">\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={() => setOpenCreaConCavi(true)}\n            color=\"primary\"\n          >\n            Nuova Comanda\n          </Button>\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<PeopleIcon />}\n            onClick={() => setOpenGestioneResponsabili(true)}\n            color=\"secondary\"\n          >\n            Gestisci Responsabili\n          </Button>\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<AssignIcon />}\n            onClick={() => {\n              if (comande.length === 0) {\n                setError('Nessuna comanda disponibile per l\\'assegnazione');\n                return;\n              }\n              // Apri dialog per selezionare comanda\n              setError('Seleziona una comanda dalla tabella e clicca sull\\'icona \"Assegna Cavi\"');\n            }}\n            disabled={comande.length === 0}\n          >\n            Assegna Cavi\n          </Button>\n        </Box>\n\n        <Button\n          variant=\"outlined\"\n          startIcon={<RefreshIcon />}\n          onClick={() => {\n            loadComande();\n            loadStatistiche();\n          }}\n        >\n          Aggiorna\n        </Button>\n      </Box>\n\n      {/* Messaggio di errore */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Tabella comande */}\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>Codice</TableCell>\n              <TableCell>Tipo</TableCell>\n              <TableCell>Priorità</TableCell>\n              <TableCell>Responsabile</TableCell>\n              <TableCell>Data Creazione</TableCell>\n              <TableCell>Stato</TableCell>\n              <TableCell>Cavi Assegnati</TableCell>\n              <TableCell>Completamento</TableCell>\n              <TableCell>Azioni</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {comande.map((comanda) => (\n              <TableRow key={comanda.codice_comanda}>\n                <TableCell>\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    {comanda.codice_comanda}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={getTipoComandaLabel(comanda.tipo_comanda)}\n                    size=\"small\"\n                    variant=\"outlined\"\n                  />\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={comanda.priorita || 'NORMALE'}\n                    size=\"small\"\n                    color={getPrioritaColor(comanda.priorita || 'NORMALE')}\n                  />\n                </TableCell>\n                <TableCell>{comanda.responsabile}</TableCell>\n                <TableCell>\n                  {new Date(comanda.data_creazione).toLocaleDateString('it-IT')}\n                </TableCell>\n                <TableCell>\n                  <Chip \n                    label={comanda.stato}\n                    color={getStatoColor(comanda.stato)}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell>{comanda.numero_cavi_assegnati || 0}</TableCell>\n                <TableCell>\n                  {comanda.percentuale_completamento ? \n                    `${comanda.percentuale_completamento.toFixed(1)}%` : '0%'}\n                </TableCell>\n                <TableCell>\n                  <Tooltip title=\"Visualizza\">\n                    <IconButton \n                      size=\"small\"\n                      onClick={() => handleOpenDialog('view', comanda)}\n                    >\n                      <ViewIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Modifica\">\n                    <IconButton \n                      size=\"small\"\n                      onClick={() => handleOpenDialog('edit', comanda)}\n                    >\n                      <EditIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Assegna Cavi\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleOpenDialog('assign', comanda)}\n                      color=\"primary\"\n                    >\n                      <AssignIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Elimina\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleDelete(comanda.codice_comanda)}\n                      color=\"error\"\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  </Tooltip>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {comande.length === 0 && !loading && (\n        <Box textAlign=\"center\" py={4}>\n          <Typography variant=\"h6\" color=\"textSecondary\">\n            Nessuna comanda trovata\n          </Typography>\n          <Typography variant=\"body2\" color=\"textSecondary\">\n            Clicca su \"Nuova Comanda\" per iniziare\n          </Typography>\n        </Box>\n      )}\n\n      {/* Dialog per modifica/assegnazione */}\n      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>\n          {dialogMode === 'edit' && 'Modifica Comanda'}\n          {dialogMode === 'view' && 'Dettagli Comanda'}\n          {dialogMode === 'assign' && `Assegna Cavi - ${selectedComanda?.codice_comanda}`}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 1 }}>\n            {dialogMode === 'assign' ? (\n              <>\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  Inserisci gli ID dei cavi da assegnare alla comanda, separati da virgola.\n                </Alert>\n                <TextField\n                  fullWidth\n                  label=\"ID Cavi (separati da virgola)\"\n                  value={caviAssegnazione}\n                  onChange={(e) => setCaviAssegnazione(e.target.value)}\n                  margin=\"normal\"\n                  placeholder=\"es: CAVO001, CAVO002, CAVO003\"\n                  helperText=\"Esempio: CAVO001, CAVO002, CAVO003\"\n                  multiline\n                  rows={3}\n                />\n              </>\n            ) : dialogMode === 'view' && selectedComanda ? (\n              <>\n                <List>\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Codice Comanda\"\n                      secondary={selectedComanda.codice_comanda}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Tipo\"\n                      secondary={getTipoComandaLabel(selectedComanda.tipo_comanda)}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Stato\"\n                      secondary={\n                        <Chip\n                          label={selectedComanda.stato}\n                          color={getStatoColor(selectedComanda.stato)}\n                          size=\"small\"\n                        />\n                      }\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Descrizione\"\n                      secondary={selectedComanda.descrizione || 'Nessuna descrizione'}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Priorità\"\n                      secondary={\n                        <Chip\n                          label={selectedComanda.priorita || 'NORMALE'}\n                          color={getPrioritaColor(selectedComanda.priorita || 'NORMALE')}\n                          size=\"small\"\n                        />\n                      }\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Responsabile\"\n                      secondary={selectedComanda.responsabile || 'Non assegnato'}\n                    />\n                  </ListItem>\n                  {selectedComanda.note_capo_cantiere && (\n                    <>\n                      <Divider />\n                      <ListItem>\n                        <ListItemText\n                          primary=\"Note Capo Cantiere\"\n                          secondary={selectedComanda.note_capo_cantiere}\n                        />\n                      </ListItem>\n                    </>\n                  )}\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Data Creazione\"\n                      secondary={new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')}\n                    />\n                  </ListItem>\n                  {selectedComanda.data_scadenza && (\n                    <>\n                      <Divider />\n                      <ListItem>\n                        <ListItemText\n                          primary=\"Data Scadenza\"\n                          secondary={new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')}\n                        />\n                      </ListItem>\n                    </>\n                  )}\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Cavi Assegnati\"\n                      secondary={selectedComanda.numero_cavi_assegnati || 0}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Completamento\"\n                      secondary={`${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`}\n                    />\n                  </ListItem>\n                </List>\n              </>\n            ) : (\n              <>\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Tipo Comanda\"\n                  value={formData.tipo_comanda}\n                  onChange={(e) => setFormData({ ...formData, tipo_comanda: e.target.value })}\n                  margin=\"normal\"\n                  disabled={dialogMode === 'view'}\n                >\n                  <MenuItem value=\"POSA\">Posa</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n                  <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n                  <MenuItem value=\"TESTING\">Testing</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Priorità\"\n                  value={formData.priorita}\n                  onChange={(e) => setFormData({ ...formData, priorita: e.target.value })}\n                  margin=\"normal\"\n                  disabled={dialogMode === 'view'}\n                >\n                  <MenuItem value=\"BASSA\">Bassa</MenuItem>\n                  <MenuItem value=\"NORMALE\">Normale</MenuItem>\n                  <MenuItem value=\"ALTA\">Alta</MenuItem>\n                  <MenuItem value=\"URGENTE\">Urgente</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  label=\"Descrizione\"\n                  value={formData.descrizione}\n                  onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={3}\n                  disabled={dialogMode === 'view'}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Responsabile\"\n                  value={formData.responsabile}\n                  onChange={(e) => setFormData({ ...formData, responsabile: e.target.value })}\n                  margin=\"normal\"\n                  disabled={dialogMode === 'view'}\n                  required\n                  helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Note Capo Cantiere\"\n                  value={formData.note_capo_cantiere}\n                  onChange={(e) => setFormData({ ...formData, note_capo_cantiere: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={2}\n                  disabled={dialogMode === 'view'}\n                  helperText=\"Istruzioni specifiche per il responsabile\"\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Data Scadenza\"\n                  type=\"date\"\n                  value={formData.data_scadenza}\n                  onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}\n                  margin=\"normal\"\n                  InputLabelProps={{ shrink: true }}\n                  disabled={dialogMode === 'view'}\n                />\n              </>\n            )}\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>\n            {dialogMode === 'view' ? 'Chiudi' : 'Annulla'}\n          </Button>\n          {dialogMode !== 'view' && (\n            <Button onClick={handleSubmit} variant=\"contained\">\n              {dialogMode === 'edit' ? 'Salva' :\n               dialogMode === 'assign' ? 'Assegna Cavi' : 'Salva'}\n            </Button>\n          )}\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per creazione comanda con cavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={() => {\n          loadComande();\n          loadStatistiche();\n          setOpenCreaConCavi(false);\n        }}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,QAAQ,EACtBC,UAAU,IAAIC,UAAU,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,oBAAoB,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExE,MAAMC,WAAW,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyD,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2D,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+D,eAAe,EAAEC,kBAAkB,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACtD,MAAM,CAACmE,QAAQ,EAAEC,WAAW,CAAC,GAAGpE,QAAQ,CAAC;IACvCqE,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,SAAS;IACnBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC6E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC+E,eAAe,EAAEC,kBAAkB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiF,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;;EAE/E;EACAC,SAAS,CAAC,MAAM;IACd,IAAImD,UAAU,EAAE;MACd+B,WAAW,CAAC,CAAC;MACbC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAChC,UAAU,CAAC,CAAC;EAEhB,MAAM+B,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFzB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM2B,QAAQ,GAAG,MAAMzC,cAAc,CAAC0C,UAAU,CAAClC,UAAU,CAAC;MAC5DI,UAAU,CAAC6B,QAAQ,CAAC9B,OAAO,IAAI,EAAE,CAAC;MAClCK,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO2B,GAAG,EAAE;MACZC,OAAO,CAAC7B,KAAK,CAAC,uCAAuC,EAAE4B,GAAG,CAAC;MAC3D3B,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMK,KAAK,GAAG,MAAM7C,cAAc,CAAC8C,qBAAqB,CAACtC,UAAU,CAAC;MACpEwB,cAAc,CAACa,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAAC7B,KAAK,CAAC,2CAA2C,EAAE4B,GAAG,CAAC;IACjE;EACF,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,OAAO,GAAG,IAAI,KAAK;IACjD3B,aAAa,CAAC0B,IAAI,CAAC;IACnB5B,kBAAkB,CAAC6B,OAAO,CAAC;IAE3B,IAAID,IAAI,KAAK,MAAM,IAAIC,OAAO,EAAE;MAC9BzB,WAAW,CAAC;QACVC,YAAY,EAAEwB,OAAO,CAACxB,YAAY;QAClCC,WAAW,EAAEuB,OAAO,CAACvB,WAAW,IAAI,EAAE;QACtCC,YAAY,EAAEsB,OAAO,CAACtB,YAAY,IAAI,EAAE;QACxCC,aAAa,EAAEqB,OAAO,CAACrB,aAAa,IAAI,EAAE;QAC1CC,QAAQ,EAAEoB,OAAO,CAACpB,QAAQ,IAAI,SAAS;QACvCC,kBAAkB,EAAEmB,OAAO,CAACnB,kBAAkB,IAAI;MACpD,CAAC,CAAC;IACJ;IAEAZ,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMgC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhC,aAAa,CAAC,KAAK,CAAC;IACpBE,kBAAkB,CAAC,IAAI,CAAC;IACxBc,mBAAmB,CAAC,EAAE,CAAC;IACvBV,WAAW,CAAC;MACVC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,SAAS;MACnBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMqB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,IAAI9B,UAAU,KAAK,MAAM,EAAE;QACzB;QACA,MAAMoB,QAAQ,GAAG,MAAMzC,cAAc,CAACoD,aAAa,CAACjC,eAAe,CAACkC,cAAc,EAAE9B,QAAQ,CAAC;QAC7FqB,OAAO,CAACU,GAAG,CAAC,qBAAqB,EAAEb,QAAQ,CAAC;MAC9C,CAAC,MAAM,IAAIpB,UAAU,KAAK,QAAQ,EAAE;QAClC;QACA,IAAI,CAACY,gBAAgB,CAACsB,IAAI,CAAC,CAAC,EAAE;UAC5BvC,QAAQ,CAAC,6BAA6B,CAAC;UACvC;QACF;QAEA,MAAMwC,WAAW,GAAGvB,gBAAgB,CAACwB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACJ,IAAI,CAAC,CAAC,CAAC,CAACK,MAAM,CAACD,EAAE,IAAIA,EAAE,CAAC;QACrF,MAAM3D,cAAc,CAAC6D,WAAW,CAAC1C,eAAe,CAACkC,cAAc,EAAEG,WAAW,CAAC;MAC/E;MAEAN,iBAAiB,CAAC,CAAC;MACnBX,WAAW,CAAC,CAAC;MACbC,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAAC7B,KAAK,CAAC,yBAAyB,EAAE4B,GAAG,CAAC;MAC7C3B,QAAQ,CAAC,sCAAsC,CAAC;IAClD;EACF,CAAC;EAED,MAAM8C,YAAY,GAAG,MAAOC,aAAa,IAAK;IAC5C,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnE,IAAI;QACF,MAAMjE,cAAc,CAACkE,aAAa,CAACH,aAAa,CAAC;QACjDxB,WAAW,CAAC,CAAC;QACbC,eAAe,CAAC,CAAC;MACnB,CAAC,CAAC,OAAOG,GAAG,EAAE;QACZC,OAAO,CAAC7B,KAAK,CAAC,4BAA4B,EAAE4B,GAAG,CAAC;QAChD3B,QAAQ,CAAC,yCAAyC,CAAC;MACrD;IACF;EACF,CAAC;EAED,MAAMmD,aAAa,GAAIC,KAAK,IAAK;IAC/B,QAAQA,KAAK;MACX,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,WAAW;QAAE,OAAO,OAAO;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,uBAAuB;QAAE,OAAO,uBAAuB;MAC5D,KAAK,qBAAqB;QAAE,OAAO,qBAAqB;MACxD,KAAK,gBAAgB;QAAE,OAAO,gBAAgB;MAC9C,KAAK,SAAS;QAAE,OAAO,wBAAwB;MAC/C;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAI1C,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,IAAIhB,OAAO,EAAE;IACX,oBACET,OAAA,CAAC9C,GAAG;MAACkH,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/ExE,OAAA,CAACzB,gBAAgB;QAAAkG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACE5E,OAAA,CAAC9C,GAAG;IAAAsH,QAAA,gBAEFxE,OAAA,CAAC9C,GAAG;MAAC2H,EAAE,EAAE,CAAE;MAAAL,QAAA,EAER7C,WAAW,iBACV3B,OAAA,CAACvB,IAAI;QAACqG,SAAS;QAACC,OAAO,EAAE,CAAE;QAACF,EAAE,EAAE,CAAE;QAAAL,QAAA,gBAChCxE,OAAA,CAACvB,IAAI;UAACuG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BxE,OAAA,CAAC7C,IAAI;YAAAqH,QAAA,eACHxE,OAAA,CAAC5C,WAAW;cAAAoH,QAAA,gBACVxE,OAAA,CAAC3C,UAAU;gBAAC+H,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5E,OAAA,CAAC3C,UAAU;gBAACiI,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB7C,WAAW,CAAC4D;cAAc;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP5E,OAAA,CAACvB,IAAI;UAACuG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BxE,OAAA,CAAC7C,IAAI;YAAAqH,QAAA,eACHxE,OAAA,CAAC5C,WAAW;cAAAoH,QAAA,gBACVxE,OAAA,CAAC3C,UAAU;gBAAC+H,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5E,OAAA,CAAC3C,UAAU;gBAACiI,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB7C,WAAW,CAAC6D;cAAc;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP5E,OAAA,CAACvB,IAAI;UAACuG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BxE,OAAA,CAAC7C,IAAI;YAAAqH,QAAA,eACHxE,OAAA,CAAC5C,WAAW;cAAAoH,QAAA,gBACVxE,OAAA,CAAC3C,UAAU;gBAAC+H,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5E,OAAA,CAAC3C,UAAU;gBAACiI,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB7C,WAAW,CAAC8D;cAAiB;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP5E,OAAA,CAACvB,IAAI;UAACuG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BxE,OAAA,CAAC7C,IAAI;YAAAqH,QAAA,eACHxE,OAAA,CAAC5C,WAAW;cAAAoH,QAAA,gBACVxE,OAAA,CAAC3C,UAAU;gBAAC+H,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5E,OAAA,CAAC3C,UAAU;gBAACiI,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB7C,WAAW,CAAC+D;cAAgB;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP5E,OAAA,CAACvB,IAAI;UAACuG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BxE,OAAA,CAAC7C,IAAI;YAAAqH,QAAA,eACHxE,OAAA,CAAC5C,WAAW;cAAAoH,QAAA,gBACVxE,OAAA,CAAC3C,UAAU;gBAAC+H,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5E,OAAA,CAAC3C,UAAU;gBAACiI,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB7C,WAAW,CAACgE;cAAkB;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP5E,OAAA,CAACvB,IAAI;UAACuG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BxE,OAAA,CAAC7C,IAAI;YAAAqH,QAAA,eACHxE,OAAA,CAAC5C,WAAW;cAAAoH,QAAA,gBACVxE,OAAA,CAAC3C,UAAU;gBAAC+H,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb5E,OAAA,CAAC3C,UAAU;gBAACiI,OAAO,EAAC,IAAI;gBAAAd,QAAA,GACrB7C,WAAW,CAACiE,+BAA+B,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,GAC1D;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACP;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN5E,OAAA,CAAC9C,GAAG;MAACkH,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACO,EAAE,EAAE,CAAE;MAACiB,QAAQ,EAAC,MAAM;MAACC,GAAG,EAAE,CAAE;MAAAvB,QAAA,gBACnGxE,OAAA,CAAC9C,GAAG;QAACkH,OAAO,EAAC,MAAM;QAAC2B,GAAG,EAAE,CAAE;QAACD,QAAQ,EAAC,MAAM;QAAAtB,QAAA,gBACzCxE,OAAA,CAAC1C,MAAM;UACLgI,OAAO,EAAC,WAAW;UACnBU,SAAS,eAAEhG,OAAA,CAACjB,OAAO;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBqB,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,IAAI,CAAE;UACxCoD,KAAK,EAAC,SAAS;UAAAZ,QAAA,EAChB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET5E,OAAA,CAAC1C,MAAM;UACLgI,OAAO,EAAC,UAAU;UAClBU,SAAS,eAAEhG,OAAA,CAACL,UAAU;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BqB,OAAO,EAAEA,CAAA,KAAM/D,2BAA2B,CAAC,IAAI,CAAE;UACjDkD,KAAK,EAAC,WAAW;UAAAZ,QAAA,EAClB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET5E,OAAA,CAAC1C,MAAM;UACLgI,OAAO,EAAC,UAAU;UAClBU,SAAS,eAAEhG,OAAA,CAACT,UAAU;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BqB,OAAO,EAAEA,CAAA,KAAM;YACb,IAAI1F,OAAO,CAAC2F,MAAM,KAAK,CAAC,EAAE;cACxBtF,QAAQ,CAAC,iDAAiD,CAAC;cAC3D;YACF;YACA;YACAA,QAAQ,CAAC,yEAAyE,CAAC;UACrF,CAAE;UACFuF,QAAQ,EAAE5F,OAAO,CAAC2F,MAAM,KAAK,CAAE;UAAA1B,QAAA,EAChC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN5E,OAAA,CAAC1C,MAAM;QACLgI,OAAO,EAAC,UAAU;QAClBU,SAAS,eAAEhG,OAAA,CAACP,WAAW;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BqB,OAAO,EAAEA,CAAA,KAAM;UACb9D,WAAW,CAAC,CAAC;UACbC,eAAe,CAAC,CAAC;QACnB,CAAE;QAAAoC,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLjE,KAAK,iBACJX,OAAA,CAAC1B,KAAK;MAAC8H,QAAQ,EAAC,OAAO;MAACC,EAAE,EAAE;QAAExB,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EACnC7D;IAAK;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGD5E,OAAA,CAACtC,cAAc;MAAC4I,SAAS,EAAEzI,KAAM;MAAA2G,QAAA,eAC/BxE,OAAA,CAACzC,KAAK;QAAAiH,QAAA,gBACJxE,OAAA,CAACrC,SAAS;UAAA6G,QAAA,eACRxE,OAAA,CAACpC,QAAQ;YAAA4G,QAAA,gBACPxE,OAAA,CAACvC,SAAS;cAAA+G,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7B5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3B5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/B5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACnC5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrC5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5B5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrC5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpC5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZ5E,OAAA,CAACxC,SAAS;UAAAgH,QAAA,EACPjE,OAAO,CAAC+C,GAAG,CAAET,OAAO,iBACnB7C,OAAA,CAACpC,QAAQ;YAAA4G,QAAA,gBACPxE,OAAA,CAACvC,SAAS;cAAA+G,QAAA,eACRxE,OAAA,CAAC3C,UAAU;gBAACiI,OAAO,EAAC,OAAO;gBAACiB,UAAU,EAAC,MAAM;gBAAA/B,QAAA,EAC1C3B,OAAO,CAACI;cAAc;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZ5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,eACRxE,OAAA,CAAClC,IAAI;gBACH0I,KAAK,EAAEvC,mBAAmB,CAACpB,OAAO,CAACxB,YAAY,CAAE;gBACjDoF,IAAI,EAAC,OAAO;gBACZnB,OAAO,EAAC;cAAU;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,eACRxE,OAAA,CAAClC,IAAI;gBACH0I,KAAK,EAAE3D,OAAO,CAACpB,QAAQ,IAAI,SAAU;gBACrCgF,IAAI,EAAC,OAAO;gBACZrB,KAAK,EAAEjB,gBAAgB,CAACtB,OAAO,CAACpB,QAAQ,IAAI,SAAS;cAAE;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,EAAE3B,OAAO,CAACtB;YAAY;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7C5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,EACP,IAAIkC,IAAI,CAAC7D,OAAO,CAAC8D,cAAc,CAAC,CAACC,kBAAkB,CAAC,OAAO;YAAC;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACZ5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,eACRxE,OAAA,CAAClC,IAAI;gBACH0I,KAAK,EAAE3D,OAAO,CAACmB,KAAM;gBACrBoB,KAAK,EAAErB,aAAa,CAAClB,OAAO,CAACmB,KAAK,CAAE;gBACpCyC,IAAI,EAAC;cAAO;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,EAAE3B,OAAO,CAACgE,qBAAqB,IAAI;YAAC;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3D5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,EACP3B,OAAO,CAACiE,yBAAyB,GAChC,GAAGjE,OAAO,CAACiE,yBAAyB,CAACjB,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAAI;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACZ5E,OAAA,CAACvC,SAAS;cAAA+G,QAAA,gBACRxE,OAAA,CAACxB,OAAO;gBAACuI,KAAK,EAAC,YAAY;gBAAAvC,QAAA,eACzBxE,OAAA,CAACjC,UAAU;kBACT0I,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,MAAM,EAAEE,OAAO,CAAE;kBAAA2B,QAAA,eAEjDxE,OAAA,CAACX,QAAQ;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACV5E,OAAA,CAACxB,OAAO;gBAACuI,KAAK,EAAC,UAAU;gBAAAvC,QAAA,eACvBxE,OAAA,CAACjC,UAAU;kBACT0I,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,MAAM,EAAEE,OAAO,CAAE;kBAAA2B,QAAA,eAEjDxE,OAAA,CAACf,QAAQ;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACV5E,OAAA,CAACxB,OAAO;gBAACuI,KAAK,EAAC,cAAc;gBAAAvC,QAAA,eAC3BxE,OAAA,CAACjC,UAAU;kBACT0I,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,QAAQ,EAAEE,OAAO,CAAE;kBACnDuC,KAAK,EAAC,SAAS;kBAAAZ,QAAA,eAEfxE,OAAA,CAACT,UAAU;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACV5E,OAAA,CAACxB,OAAO;gBAACuI,KAAK,EAAC,SAAS;gBAAAvC,QAAA,eACtBxE,OAAA,CAACjC,UAAU;kBACT0I,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAMvC,YAAY,CAACb,OAAO,CAACI,cAAc,CAAE;kBACpDmC,KAAK,EAAC,OAAO;kBAAAZ,QAAA,eAEbxE,OAAA,CAACb,UAAU;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GAvEC/B,OAAO,CAACI,cAAc;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwE3B,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,EAEhBrE,OAAO,CAAC2F,MAAM,KAAK,CAAC,IAAI,CAACzF,OAAO,iBAC/BT,OAAA,CAAC9C,GAAG;MAAC8J,SAAS,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAzC,QAAA,gBAC5BxE,OAAA,CAAC3C,UAAU;QAACiI,OAAO,EAAC,IAAI;QAACF,KAAK,EAAC,eAAe;QAAAZ,QAAA,EAAC;MAE/C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5E,OAAA,CAAC3C,UAAU;QAACiI,OAAO,EAAC,OAAO;QAACF,KAAK,EAAC,eAAe;QAAAZ,QAAA,EAAC;MAElD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAGD5E,OAAA,CAAChC,MAAM;MAACkJ,IAAI,EAAErG,UAAW;MAACsG,OAAO,EAAErE,iBAAkB;MAACsE,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAA7C,QAAA,gBAC3ExE,OAAA,CAAC/B,WAAW;QAAAuG,QAAA,GACTvD,UAAU,KAAK,MAAM,IAAI,kBAAkB,EAC3CA,UAAU,KAAK,MAAM,IAAI,kBAAkB,EAC3CA,UAAU,KAAK,QAAQ,IAAI,kBAAkBF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkC,cAAc,EAAE;MAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACd5E,OAAA,CAAC9B,aAAa;QAAAsG,QAAA,eACZxE,OAAA,CAAC9C,GAAG;UAACmJ,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,EAChBvD,UAAU,KAAK,QAAQ,gBACtBjB,OAAA,CAAAE,SAAA;YAAAsE,QAAA,gBACExE,OAAA,CAAC1B,KAAK;cAAC8H,QAAQ,EAAC,MAAM;cAACC,EAAE,EAAE;gBAAExB,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,EAAC;YAEtC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5E,OAAA,CAAC5B,SAAS;cACRiJ,SAAS;cACTb,KAAK,EAAC,+BAA+B;cACrCe,KAAK,EAAE1F,gBAAiB;cACxB2F,QAAQ,EAAGC,CAAC,IAAK3F,mBAAmB,CAAC2F,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACrDI,MAAM,EAAC,QAAQ;cACfC,WAAW,EAAC,+BAA+B;cAC3CC,UAAU,EAAC,oCAAoC;cAC/CC,SAAS;cACTC,IAAI,EAAE;YAAE;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA,eACF,CAAC,GACD3D,UAAU,KAAK,MAAM,IAAIF,eAAe,gBAC1Cf,OAAA,CAAAE,SAAA;YAAAsE,QAAA,eACExE,OAAA,CAACtB,IAAI;cAAA8F,QAAA,gBACHxE,OAAA,CAACrB,QAAQ;gBAAA6F,QAAA,eACPxE,OAAA,CAACpB,YAAY;kBACXoJ,OAAO,EAAC,gBAAgB;kBACxBC,SAAS,EAAElH,eAAe,CAACkC;gBAAe;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX5E,OAAA,CAACnB,OAAO;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX5E,OAAA,CAACrB,QAAQ;gBAAA6F,QAAA,eACPxE,OAAA,CAACpB,YAAY;kBACXoJ,OAAO,EAAC,MAAM;kBACdC,SAAS,EAAEhE,mBAAmB,CAAClD,eAAe,CAACM,YAAY;gBAAE;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX5E,OAAA,CAACnB,OAAO;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX5E,OAAA,CAACrB,QAAQ;gBAAA6F,QAAA,eACPxE,OAAA,CAACpB,YAAY;kBACXoJ,OAAO,EAAC,OAAO;kBACfC,SAAS,eACPjI,OAAA,CAAClC,IAAI;oBACH0I,KAAK,EAAEzF,eAAe,CAACiD,KAAM;oBAC7BoB,KAAK,EAAErB,aAAa,CAAChD,eAAe,CAACiD,KAAK,CAAE;oBAC5CyC,IAAI,EAAC;kBAAO;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX5E,OAAA,CAACnB,OAAO;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX5E,OAAA,CAACrB,QAAQ;gBAAA6F,QAAA,eACPxE,OAAA,CAACpB,YAAY;kBACXoJ,OAAO,EAAC,aAAa;kBACrBC,SAAS,EAAElH,eAAe,CAACO,WAAW,IAAI;gBAAsB;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX5E,OAAA,CAACnB,OAAO;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX5E,OAAA,CAACrB,QAAQ;gBAAA6F,QAAA,eACPxE,OAAA,CAACpB,YAAY;kBACXoJ,OAAO,EAAC,aAAU;kBAClBC,SAAS,eACPjI,OAAA,CAAClC,IAAI;oBACH0I,KAAK,EAAEzF,eAAe,CAACU,QAAQ,IAAI,SAAU;oBAC7C2D,KAAK,EAAEjB,gBAAgB,CAACpD,eAAe,CAACU,QAAQ,IAAI,SAAS,CAAE;oBAC/DgF,IAAI,EAAC;kBAAO;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX5E,OAAA,CAACnB,OAAO;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX5E,OAAA,CAACrB,QAAQ;gBAAA6F,QAAA,eACPxE,OAAA,CAACpB,YAAY;kBACXoJ,OAAO,EAAC,cAAc;kBACtBC,SAAS,EAAElH,eAAe,CAACQ,YAAY,IAAI;gBAAgB;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,EACV7D,eAAe,CAACW,kBAAkB,iBACjC1B,OAAA,CAAAE,SAAA;gBAAAsE,QAAA,gBACExE,OAAA,CAACnB,OAAO;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACX5E,OAAA,CAACrB,QAAQ;kBAAA6F,QAAA,eACPxE,OAAA,CAACpB,YAAY;oBACXoJ,OAAO,EAAC,oBAAoB;oBAC5BC,SAAS,EAAElH,eAAe,CAACW;kBAAmB;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC;cAAA,eACX,CACH,eACD5E,OAAA,CAACnB,OAAO;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX5E,OAAA,CAACrB,QAAQ;gBAAA6F,QAAA,eACPxE,OAAA,CAACpB,YAAY;kBACXoJ,OAAO,EAAC,gBAAgB;kBACxBC,SAAS,EAAE,IAAIvB,IAAI,CAAC3F,eAAe,CAAC4F,cAAc,CAAC,CAACC,kBAAkB,CAAC,OAAO;gBAAE;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,EACV7D,eAAe,CAACS,aAAa,iBAC5BxB,OAAA,CAAAE,SAAA;gBAAAsE,QAAA,gBACExE,OAAA,CAACnB,OAAO;kBAAA4F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACX5E,OAAA,CAACrB,QAAQ;kBAAA6F,QAAA,eACPxE,OAAA,CAACpB,YAAY;oBACXoJ,OAAO,EAAC,eAAe;oBACvBC,SAAS,EAAE,IAAIvB,IAAI,CAAC3F,eAAe,CAACS,aAAa,CAAC,CAACoF,kBAAkB,CAAC,OAAO;kBAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC;cAAA,eACX,CACH,eACD5E,OAAA,CAACnB,OAAO;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX5E,OAAA,CAACrB,QAAQ;gBAAA6F,QAAA,eACPxE,OAAA,CAACpB,YAAY;kBACXoJ,OAAO,EAAC,gBAAgB;kBACxBC,SAAS,EAAElH,eAAe,CAAC8F,qBAAqB,IAAI;gBAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX5E,OAAA,CAACnB,OAAO;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX5E,OAAA,CAACrB,QAAQ;gBAAA6F,QAAA,eACPxE,OAAA,CAACpB,YAAY;kBACXoJ,OAAO,EAAC,eAAe;kBACvBC,SAAS,EAAE,GAAG,CAAClH,eAAe,CAAC+F,yBAAyB,IAAI,CAAC,EAAEjB,OAAO,CAAC,CAAC,CAAC;gBAAI;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC,gBACP,CAAC,gBAEH5E,OAAA,CAAAE,SAAA;YAAAsE,QAAA,gBACExE,OAAA,CAAC5B,SAAS;cACRiJ,SAAS;cACTa,MAAM;cACN1B,KAAK,EAAC,cAAc;cACpBe,KAAK,EAAEpG,QAAQ,CAACE,YAAa;cAC7BmG,QAAQ,EAAGC,CAAC,IAAKrG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,YAAY,EAAEoG,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC5EI,MAAM,EAAC,QAAQ;cACfxB,QAAQ,EAAElF,UAAU,KAAK,MAAO;cAAAuD,QAAA,gBAEhCxE,OAAA,CAAC3B,QAAQ;gBAACkJ,KAAK,EAAC,MAAM;gBAAA/C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtC5E,OAAA,CAAC3B,QAAQ;gBAACkJ,KAAK,EAAC,uBAAuB;gBAAA/C,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxE5E,OAAA,CAAC3B,QAAQ;gBAACkJ,KAAK,EAAC,qBAAqB;gBAAA/C,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpE5E,OAAA,CAAC3B,QAAQ;gBAACkJ,KAAK,EAAC,gBAAgB;gBAAA/C,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1D5E,OAAA,CAAC3B,QAAQ;gBAACkJ,KAAK,EAAC,SAAS;gBAAA/C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEZ5E,OAAA,CAAC5B,SAAS;cACRiJ,SAAS;cACTa,MAAM;cACN1B,KAAK,EAAC,aAAU;cAChBe,KAAK,EAAEpG,QAAQ,CAACM,QAAS;cACzB+F,QAAQ,EAAGC,CAAC,IAAKrG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEM,QAAQ,EAAEgG,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACxEI,MAAM,EAAC,QAAQ;cACfxB,QAAQ,EAAElF,UAAU,KAAK,MAAO;cAAAuD,QAAA,gBAEhCxE,OAAA,CAAC3B,QAAQ;gBAACkJ,KAAK,EAAC,OAAO;gBAAA/C,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxC5E,OAAA,CAAC3B,QAAQ;gBAACkJ,KAAK,EAAC,SAAS;gBAAA/C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5C5E,OAAA,CAAC3B,QAAQ;gBAACkJ,KAAK,EAAC,MAAM;gBAAA/C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtC5E,OAAA,CAAC3B,QAAQ;gBAACkJ,KAAK,EAAC,SAAS;gBAAA/C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEZ5E,OAAA,CAAC5B,SAAS;cACRiJ,SAAS;cACTb,KAAK,EAAC,aAAa;cACnBe,KAAK,EAAEpG,QAAQ,CAACG,WAAY;cAC5BkG,QAAQ,EAAGC,CAAC,IAAKrG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAEmG,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC3EI,MAAM,EAAC,QAAQ;cACfG,SAAS;cACTC,IAAI,EAAE,CAAE;cACR5B,QAAQ,EAAElF,UAAU,KAAK;YAAO;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eAEF5E,OAAA,CAAC5B,SAAS;cACRiJ,SAAS;cACTb,KAAK,EAAC,cAAc;cACpBe,KAAK,EAAEpG,QAAQ,CAACI,YAAa;cAC7BiG,QAAQ,EAAGC,CAAC,IAAKrG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,YAAY,EAAEkG,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC5EI,MAAM,EAAC,QAAQ;cACfxB,QAAQ,EAAElF,UAAU,KAAK,MAAO;cAChCkH,QAAQ;cACRN,UAAU,EAAC;YAAuC;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eAEF5E,OAAA,CAAC5B,SAAS;cACRiJ,SAAS;cACTb,KAAK,EAAC,oBAAoB;cAC1Be,KAAK,EAAEpG,QAAQ,CAACO,kBAAmB;cACnC8F,QAAQ,EAAGC,CAAC,IAAKrG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEO,kBAAkB,EAAE+F,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAClFI,MAAM,EAAC,QAAQ;cACfG,SAAS;cACTC,IAAI,EAAE,CAAE;cACR5B,QAAQ,EAAElF,UAAU,KAAK,MAAO;cAChC4G,UAAU,EAAC;YAA2C;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eAEF5E,OAAA,CAAC5B,SAAS;cACRiJ,SAAS;cACTb,KAAK,EAAC,eAAe;cACrB4B,IAAI,EAAC,MAAM;cACXb,KAAK,EAAEpG,QAAQ,CAACK,aAAc;cAC9BgG,QAAQ,EAAGC,CAAC,IAAKrG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEK,aAAa,EAAEiG,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC7EI,MAAM,EAAC,QAAQ;cACfU,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK,CAAE;cAClCnC,QAAQ,EAAElF,UAAU,KAAK;YAAO;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA,eACF;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChB5E,OAAA,CAAC7B,aAAa;QAAAqG,QAAA,gBACZxE,OAAA,CAAC1C,MAAM;UAAC2I,OAAO,EAAEnD,iBAAkB;UAAA0B,QAAA,EAChCvD,UAAU,KAAK,MAAM,GAAG,QAAQ,GAAG;QAAS;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,EACR3D,UAAU,KAAK,MAAM,iBACpBjB,OAAA,CAAC1C,MAAM;UAAC2I,OAAO,EAAElD,YAAa;UAACuC,OAAO,EAAC,WAAW;UAAAd,QAAA,EAC/CvD,UAAU,KAAK,MAAM,GAAG,OAAO,GAC/BA,UAAU,KAAK,QAAQ,GAAG,cAAc,GAAG;QAAO;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT5E,OAAA,CAACH,kBAAkB;MACjBO,UAAU,EAAEA,UAAW;MACvB8G,IAAI,EAAEnF,eAAgB;MACtBoF,OAAO,EAAEA,CAAA,KAAMnF,kBAAkB,CAAC,KAAK,CAAE;MACzCuG,SAAS,EAAEA,CAAA,KAAM;QACfpG,WAAW,CAAC,CAAC;QACbC,eAAe,CAAC,CAAC;QACjBJ,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IAAE;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACtE,EAAA,CAnoBIH,WAAW;AAAAqI,EAAA,GAAXrI,WAAW;AAqoBjB,eAAeA,WAAW;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}