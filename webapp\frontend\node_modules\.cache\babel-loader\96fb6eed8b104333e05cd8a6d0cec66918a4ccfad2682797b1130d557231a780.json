{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nconst API_URL = config.API_URL;\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst parcoCaviService = {\n  // Ottiene la lista delle bobine di un cantiere\n  getBobine: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea una nuova bobina\n  createBobina: async (cantiereId, bobinaData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/parco-cavi/${cantiereIdNum}`, bobinaData);\n      return response.data;\n    } catch (error) {\n      console.error('Create bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna una bobina esistente\n  updateBobina: async (cantiereId, numeroBobina, bobinaData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Estrai solo il numero della bobina dalla stringa completa (se necessario)\n      // Il formato dell'ID bobina è C{id_cantiere}_B{numero_bobina}\n      let bobinaPart = numeroBobina;\n      if (numeroBobina.includes('_B')) {\n        bobinaPart = numeroBobina.split('_B')[1];\n      }\n      const response = await axiosInstance.put(`/parco-cavi/${cantiereIdNum}/${bobinaPart}`, bobinaData);\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina una bobina\n  deleteBobina: async (cantiereId, numeroBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Estrai solo il numero della bobina dalla stringa completa (se necessario)\n      // Il formato dell'ID bobina è C{id_cantiere}_B{numero_bobina}\n      let bobinaPart = numeroBobina;\n      if (numeroBobina.includes('_B')) {\n        bobinaPart = numeroBobina.split('_B')[1];\n      }\n      const response = await axiosInstance.delete(`/parco-cavi/${cantiereIdNum}/${bobinaPart}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Verifica se è il primo inserimento di una bobina per un cantiere\n  isFirstBobinaInsertion: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}/is-first-insertion`);\n      return response.data.is_first_insertion;\n    } catch (error) {\n      console.error('Check first insertion error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene lo storico utilizzo delle bobine\n  getStoricoUtilizzo: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}/storico`);\n      return response.data;\n    } catch (error) {\n      console.error('Get storico utilizzo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default parcoCaviService;", "map": {"version": 3, "names": ["axios", "config", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "parcoCaviService", "getBobine", "cantiereId", "cantiereIdNum", "parseInt", "isNaN", "Error", "response", "get", "data", "console", "createBobina", "bobina<PERSON><PERSON>", "post", "updateBobina", "numeroBobina", "b<PERSON><PERSON><PERSON><PERSON>", "includes", "split", "put", "deleteBobina", "delete", "isFirstBobinaInsertion", "is_first_insertion", "getStoricoUtilizzo"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/parcoCaviService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\n// Crea un'istanza di axios con configurazione personalizzata\r\nconst axiosInstance = axios.create({\r\n  baseURL: API_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json'\r\n  }\r\n});\r\n\r\n// Configura axios per includere il token in tutte le richieste\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem('token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nconst parcoCaviService = {\r\n  // Ottiene la lista delle bobine di un cantiere\r\n  getBobine: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get bobine error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea una nuova bobina\r\n  createBobina: async (cantiereId, bobinaData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.post(`/parco-cavi/${cantiereIdNum}`, bobinaData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Create bobina error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna una bobina esistente\r\n  updateBobina: async (cantiereId, numeroBobina, bobinaData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Estrai solo il numero della bobina dalla stringa completa (se necessario)\r\n      // Il formato dell'ID bobina è C{id_cantiere}_B{numero_bobina}\r\n      let bobinaPart = numeroBobina;\r\n      if (numeroBobina.includes('_B')) {\r\n        bobinaPart = numeroBobina.split('_B')[1];\r\n      }\r\n\r\n      const response = await axiosInstance.put(`/parco-cavi/${cantiereIdNum}/${bobinaPart}`, bobinaData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Update bobina error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Elimina una bobina\r\n  deleteBobina: async (cantiereId, numeroBobina) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Estrai solo il numero della bobina dalla stringa completa (se necessario)\r\n      // Il formato dell'ID bobina è C{id_cantiere}_B{numero_bobina}\r\n      let bobinaPart = numeroBobina;\r\n      if (numeroBobina.includes('_B')) {\r\n        bobinaPart = numeroBobina.split('_B')[1];\r\n      }\r\n\r\n      const response = await axiosInstance.delete(`/parco-cavi/${cantiereIdNum}/${bobinaPart}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Delete bobina error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Verifica se è il primo inserimento di una bobina per un cantiere\r\n  isFirstBobinaInsertion: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}/is-first-insertion`);\r\n      return response.data.is_first_insertion;\r\n    } catch (error) {\r\n      console.error('Check first insertion error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene lo storico utilizzo delle bobine\r\n  getStoricoUtilizzo: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}/storico`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get storico utilizzo error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default parcoCaviService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAE9B,MAAMC,OAAO,GAAGD,MAAM,CAACC,OAAO;;AAE9B;AACA,MAAMC,aAAa,GAAGH,KAAK,CAACI,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCR,MAAM,IAAK;EACV,MAAMS,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTT,MAAM,CAACK,OAAO,CAACO,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOT,MAAM;AACf,CAAC,EACAa,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,gBAAgB,GAAG;EACvB;EACAC,SAAS,EAAE,MAAOC,UAAU,IAAK;IAC/B,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMK,QAAQ,GAAG,MAAMrB,aAAa,CAACsB,GAAG,CAAC,eAAeL,aAAa,EAAE,CAAC;MACxE,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF,CAAC;EAED;EACAc,YAAY,EAAE,MAAAA,CAAOT,UAAU,EAAEU,UAAU,KAAK;IAC9C,IAAI;MACF;MACA,MAAMT,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMK,QAAQ,GAAG,MAAMrB,aAAa,CAAC2B,IAAI,CAAC,eAAeV,aAAa,EAAE,EAAES,UAAU,CAAC;MACrF,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF,CAAC;EAED;EACAiB,YAAY,EAAE,MAAAA,CAAOZ,UAAU,EAAEa,YAAY,EAAEH,UAAU,KAAK;IAC5D,IAAI;MACF;MACA,MAAMT,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA;MACA,IAAIc,UAAU,GAAGD,YAAY;MAC7B,IAAIA,YAAY,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC/BD,UAAU,GAAGD,YAAY,CAACG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAC1C;MAEA,MAAMX,QAAQ,GAAG,MAAMrB,aAAa,CAACiC,GAAG,CAAC,eAAehB,aAAa,IAAIa,UAAU,EAAE,EAAEJ,UAAU,CAAC;MAClG,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF,CAAC;EAED;EACAuB,YAAY,EAAE,MAAAA,CAAOlB,UAAU,EAAEa,YAAY,KAAK;IAChD,IAAI;MACF;MACA,MAAMZ,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA;MACA,IAAIc,UAAU,GAAGD,YAAY;MAC7B,IAAIA,YAAY,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC/BD,UAAU,GAAGD,YAAY,CAACG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAC1C;MAEA,MAAMX,QAAQ,GAAG,MAAMrB,aAAa,CAACmC,MAAM,CAAC,eAAelB,aAAa,IAAIa,UAAU,EAAE,CAAC;MACzF,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF,CAAC;EAED;EACAyB,sBAAsB,EAAE,MAAOpB,UAAU,IAAK;IAC5C,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMK,QAAQ,GAAG,MAAMrB,aAAa,CAACsB,GAAG,CAAC,eAAeL,aAAa,qBAAqB,CAAC;MAC3F,OAAOI,QAAQ,CAACE,IAAI,CAACc,kBAAkB;IACzC,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF,CAAC;EAED;EACA2B,kBAAkB,EAAE,MAAOtB,UAAU,IAAK;IACxC,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMK,QAAQ,GAAG,MAAMrB,aAAa,CAACsB,GAAG,CAAC,eAAeL,aAAa,UAAU,CAAC;MAChF,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}