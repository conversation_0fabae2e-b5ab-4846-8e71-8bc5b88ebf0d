{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'bir saniyədən az',\n    other: '{{count}} bir saniyədən az'\n  },\n  xSeconds: {\n    one: '1 saniyə',\n    other: '{{count}} saniyə'\n  },\n  halfAMinute: 'yarım dəqiqə',\n  lessThanXMinutes: {\n    one: 'bir dəqiqədən az',\n    other: '{{count}} bir dəqiqədən az'\n  },\n  xMinutes: {\n    one: 'bir dəqiqə',\n    other: '{{count}} dəqiqə'\n  },\n  aboutXHours: {\n    one: 'təxminən 1 saat',\n    other: 'təxminən {{count}} saat'\n  },\n  xHours: {\n    one: '1 saat',\n    other: '{{count}} saat'\n  },\n  xDays: {\n    one: '1 gün',\n    other: '{{count}} gün'\n  },\n  aboutXWeeks: {\n    one: 'təxminən 1 həftə',\n    other: 'təxminən {{count}} həftə'\n  },\n  xWeeks: {\n    one: '1 həftə',\n    other: '{{count}} həftə'\n  },\n  aboutXMonths: {\n    one: 'təxminən 1 ay',\n    other: 'təxminən {{count}} ay'\n  },\n  xMonths: {\n    one: '1 ay',\n    other: '{{count}} ay'\n  },\n  aboutXYears: {\n    one: 'təxminən 1 il',\n    other: 'təxminən {{count}} il'\n  },\n  xYears: {\n    one: '1 il',\n    other: '{{count}} il'\n  },\n  overXYears: {\n    one: '1 ildən çox',\n    other: '{{count}} ildən çox'\n  },\n  almostXYears: {\n    one: 'demək olar ki 1 il',\n    other: 'demək olar ki {{count}} il'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' sonra';\n    } else {\n      return result + ' əvvəl';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/az/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'bir saniyədən az',\n    other: '{{count}} bir saniyədən az'\n  },\n  xSeconds: {\n    one: '1 saniyə',\n    other: '{{count}} saniyə'\n  },\n  halfAMinute: 'yarım dəqiqə',\n  lessThanXMinutes: {\n    one: 'bir dəqiqədən az',\n    other: '{{count}} bir dəqiqədən az'\n  },\n  xMinutes: {\n    one: 'bir dəqiqə',\n    other: '{{count}} dəqiqə'\n  },\n  aboutXHours: {\n    one: 'təxminən 1 saat',\n    other: 'təxminən {{count}} saat'\n  },\n  xHours: {\n    one: '1 saat',\n    other: '{{count}} saat'\n  },\n  xDays: {\n    one: '1 gün',\n    other: '{{count}} gün'\n  },\n  aboutXWeeks: {\n    one: 'təxminən 1 həftə',\n    other: 'təxminən {{count}} həftə'\n  },\n  xWeeks: {\n    one: '1 həftə',\n    other: '{{count}} həftə'\n  },\n  aboutXMonths: {\n    one: 'təxminən 1 ay',\n    other: 'təxminən {{count}} ay'\n  },\n  xMonths: {\n    one: '1 ay',\n    other: '{{count}} ay'\n  },\n  aboutXYears: {\n    one: 'təxminən 1 il',\n    other: 'təxminən {{count}} il'\n  },\n  xYears: {\n    one: '1 il',\n    other: '{{count}} il'\n  },\n  overXYears: {\n    one: '1 ildən çox',\n    other: '{{count}} ildən çox'\n  },\n  almostXYears: {\n    one: 'demək olar ki 1 il',\n    other: 'demək olar ki {{count}} il'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + ' sonra';\n    } else {\n      return result + ' əvvəl';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,cAAc;EAC3BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,QAAQ;IAC1B,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}