{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst userService = {\n  // Ottiene la lista di tutti gli utenti\n  getUsers: async () => {\n    try {\n      const response = await axiosInstance.get('/users');\n      return response.data;\n    } catch (error) {\n      console.error('Get users error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea un nuovo utente\n  createUser: async userData => {\n    try {\n      const response = await axiosInstance.post('/users', userData);\n      return response.data;\n    } catch (error) {\n      console.error('Create user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna un utente esistente\n  updateUser: async (userId, userData) => {\n    try {\n      const response = await axiosInstance.put(`/users/${userId}`, userData);\n      return response.data;\n    } catch (error) {\n      console.error('Update user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un utente\n  deleteUser: async userId => {\n    try {\n      const response = await axiosInstance.delete(`/users/${userId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Abilita/disabilita un utente\n  toggleUserStatus: async userId => {\n    try {\n      const response = await axiosInstance.get(`/users/toggle/${userId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Toggle user status error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene una visualizzazione raw del database\n  getDbRaw: async () => {\n    try {\n      const response = await axiosInstance.get('/users/db-raw');\n      return response.data;\n    } catch (error) {\n      console.error('Get DB raw error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Verifica e disabilita gli utenti scaduti\n  checkExpiredUsers: async () => {\n    try {\n      const response = await axiosInstance.post('/users/check-expired');\n      return response.data;\n    } catch (error) {\n      console.error('Check expired users error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default userService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "userService", "getUsers", "response", "get", "data", "console", "createUser", "userData", "post", "updateUser", "userId", "put", "deleteUser", "delete", "toggleUserStatus", "getDbRaw", "checkExpiredUsers"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/userService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\nconst userService = {\n  // Ottiene la lista di tutti gli utenti\n  getUsers: async () => {\n    try {\n      const response = await axiosInstance.get('/users');\n      return response.data;\n    } catch (error) {\n      console.error('Get users error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea un nuovo utente\n  createUser: async (userData) => {\n    try {\n      const response = await axiosInstance.post('/users', userData);\n      return response.data;\n    } catch (error) {\n      console.error('Create user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna un utente esistente\n  updateUser: async (userId, userData) => {\n    try {\n      const response = await axiosInstance.put(`/users/${userId}`, userData);\n      return response.data;\n    } catch (error) {\n      console.error('Update user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina un utente\n  deleteUser: async (userId) => {\n    try {\n      const response = await axiosInstance.delete(`/users/${userId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Abilita/disabilita un utente\n  toggleUserStatus: async (userId) => {\n    try {\n      const response = await axiosInstance.get(`/users/toggle/${userId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Toggle user status error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene una visualizzazione raw del database\n  getDbRaw: async () => {\n    try {\n      const response = await axiosInstance.get('/users/db-raw');\n      return response.data;\n    } catch (error) {\n      console.error('Get DB raw error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Verifica e disabilita gli utenti scaduti\n  checkExpiredUsers: async () => {\n    try {\n      const response = await axiosInstance.post('/users/check-expired');\n      return response.data;\n    } catch (error) {\n      console.error('Check expired users error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default userService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,WAAW,GAAG;EAClB;EACAC,QAAQ,EAAE,MAAAA,CAAA,KAAY;IACpB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMjB,aAAa,CAACkB,GAAG,CAAC,QAAQ,CAAC;MAClD,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,GAAGP,KAAK;IACpD;EACF,CAAC;EAED;EACAS,UAAU,EAAE,MAAOC,QAAQ,IAAK;IAC9B,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMjB,aAAa,CAACuB,IAAI,CAAC,QAAQ,EAAED,QAAQ,CAAC;MAC7D,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,GAAGP,KAAK;IACpD;EACF,CAAC;EAED;EACAY,UAAU,EAAE,MAAAA,CAAOC,MAAM,EAAEH,QAAQ,KAAK;IACtC,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMjB,aAAa,CAAC0B,GAAG,CAAC,UAAUD,MAAM,EAAE,EAAEH,QAAQ,CAAC;MACtE,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,GAAGP,KAAK;IACpD;EACF,CAAC;EAED;EACAe,UAAU,EAAE,MAAOF,MAAM,IAAK;IAC5B,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMjB,aAAa,CAAC4B,MAAM,CAAC,UAAUH,MAAM,EAAE,CAAC;MAC/D,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,GAAGP,KAAK;IACpD;EACF,CAAC;EAED;EACAiB,gBAAgB,EAAE,MAAOJ,MAAM,IAAK;IAClC,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMjB,aAAa,CAACkB,GAAG,CAAC,iBAAiBO,MAAM,EAAE,CAAC;MACnE,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,GAAGP,KAAK;IACpD;EACF,CAAC;EAED;EACAkB,QAAQ,EAAE,MAAAA,CAAA,KAAY;IACpB,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMjB,aAAa,CAACkB,GAAG,CAAC,eAAe,CAAC;MACzD,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,GAAGP,KAAK;IACpD;EACF,CAAC;EAED;EACAmB,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMjB,aAAa,CAACuB,IAAI,CAAC,sBAAsB,CAAC;MACjE,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,GAAGP,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}