{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link interval} function options.\n */\n\n/**\n * @name interval\n * @category Interval Helpers\n * @summary Creates an interval object and validates its values.\n *\n * @description\n * Creates a normalized interval object and validates its values. If the interval is invalid, an exception is thrown.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param start - The start of the interval.\n * @param end - The end of the interval.\n * @param options - The options object.\n *\n * @throws `Start date is invalid` when `start` is invalid.\n * @throws `End date is invalid` when `end` is invalid.\n * @throws `End date must be after start date` when end is before `start` and `options.assertPositive` is true.\n *\n * @returns The normalized and validated interval object.\n */\nexport function interval(start, end, options) {\n  const _start = toDate(start);\n  if (isNaN(+_start)) throw new TypeError(\"Start date is invalid\");\n  const _end = toDate(end);\n  if (isNaN(+_end)) throw new TypeError(\"End date is invalid\");\n  if (options?.assertPositive && +_start > +_end) throw new TypeError(\"End date must be after start date\");\n  return {\n    start: _start,\n    end: _end\n  };\n}\n\n// Fallback for modularized imports:\nexport default interval;", "map": {"version": 3, "names": ["toDate", "interval", "start", "end", "options", "_start", "isNaN", "TypeError", "_end", "assertPositive"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/interval.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link interval} function options.\n */\n\n/**\n * @name interval\n * @category Interval Helpers\n * @summary Creates an interval object and validates its values.\n *\n * @description\n * Creates a normalized interval object and validates its values. If the interval is invalid, an exception is thrown.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param start - The start of the interval.\n * @param end - The end of the interval.\n * @param options - The options object.\n *\n * @throws `Start date is invalid` when `start` is invalid.\n * @throws `End date is invalid` when `end` is invalid.\n * @throws `End date must be after start date` when end is before `start` and `options.assertPositive` is true.\n *\n * @returns The normalized and validated interval object.\n */\nexport function interval(start, end, options) {\n  const _start = toDate(start);\n  if (isNaN(+_start)) throw new TypeError(\"Start date is invalid\");\n\n  const _end = toDate(end);\n  if (isNaN(+_end)) throw new TypeError(\"End date is invalid\");\n\n  if (options?.assertPositive && +_start > +_end)\n    throw new TypeError(\"End date must be after start date\");\n\n  return { start: _start, end: _end };\n}\n\n// Fallback for modularized imports:\nexport default interval;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAE;EAC5C,MAAMC,MAAM,GAAGL,MAAM,CAACE,KAAK,CAAC;EAC5B,IAAII,KAAK,CAAC,CAACD,MAAM,CAAC,EAAE,MAAM,IAAIE,SAAS,CAAC,uBAAuB,CAAC;EAEhE,MAAMC,IAAI,GAAGR,MAAM,CAACG,GAAG,CAAC;EACxB,IAAIG,KAAK,CAAC,CAACE,IAAI,CAAC,EAAE,MAAM,IAAID,SAAS,CAAC,qBAAqB,CAAC;EAE5D,IAAIH,OAAO,EAAEK,cAAc,IAAI,CAACJ,MAAM,GAAG,CAACG,IAAI,EAC5C,MAAM,IAAID,SAAS,CAAC,mCAAmC,CAAC;EAE1D,OAAO;IAAEL,KAAK,EAAEG,MAAM;IAAEF,GAAG,EAAEK;EAAK,CAAC;AACrC;;AAEA;AACA,eAAeP,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}