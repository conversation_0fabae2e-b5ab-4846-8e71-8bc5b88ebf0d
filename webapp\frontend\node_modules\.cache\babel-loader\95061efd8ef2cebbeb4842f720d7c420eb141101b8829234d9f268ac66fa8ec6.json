{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\CaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, CardActions, Tabs, Tab, Alert, Snackbar, IconButton } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport caviService from '../services/caviService';\n\n// Componente per il pannello delle tab\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TabPanel(props) {\n  const {\n    children,\n    value,\n    index,\n    ...other\n  } = props;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    role: \"tabpanel\",\n    hidden: value !== index,\n    id: `cavi-tabpanel-${index}`,\n    \"aria-labelledby\": `cavi-tab-${index}`,\n    ...other,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_c = TabPanel;\nconst CaviPage = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n  const [tabValue, setTabValue] = useState(0);\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    const fetchData = async () => {\n      // Recupera l'ID del cantiere selezionato dal localStorage\n      const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n      const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n      if (!selectedCantiereId) {\n        setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n        setLoading(false);\n        return;\n      }\n      setCantiereId(selectedCantiereId);\n      setCantiereName(selectedCantiereName || `Cantiere ${selectedCantiereId}`);\n      try {\n        setLoading(true);\n        // Carica i cavi attivi\n        const attivi = await caviService.getCavi(selectedCantiereId, 0);\n        setCaviAttivi(attivi);\n\n        // Carica i cavi spare\n        const spare = await caviService.getCavi(selectedCantiereId, 3);\n        setCaviSpare(spare);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        setError('Impossibile caricare i cavi. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n\n  // Gestione del cambio di tab\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n  };\n\n  // Torna alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = cavi => {\n    if (cavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Nessun cavo trovato in questa categoria.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"div\",\n              children: cavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Sistema: \", cavo.sistema || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Tipologia: \", cavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Partenza: \", cavo.ubicazione_partenza || 'N/A', \" - \", cavo.utenza_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Arrivo: \", cavo.ubicazione_arrivo || 'N/A', \" - \", cavo.utenza_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Metratura reale: \", cavo.metratura_reale || '0']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Stato: \", cavo.stato_installazione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 49\n              }, this),\n              children: \"Modifica\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: \"error\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 63\n              }, this),\n              children: \"Elimina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this)\n      }, cavo.id_cavo, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: handleBackToCantieri,\n        sx: {\n          mr: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        children: \"Gestione Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: [\"Cantiere: \", cantiereName, \" (ID: \", cantiereId, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Caricamento cavi...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: tabValue,\n          onChange: handleTabChange,\n          indicatorColor: \"primary\",\n          textColor: \"primary\",\n          variant: \"scrollable\",\n          scrollButtons: \"auto\",\n          allowScrollButtonsMobile: true,\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Visualizza Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Posa Cavi e Collegamenti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Parco Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Gestione Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Certificazione Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Gestione Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 0,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            children: \"Cavi Attivi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this), renderCaviTable(caviAttivi)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            children: \"Cavi Spare\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this), renderCaviTable(caviSpare)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 1,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Posa Cavi e Collegamenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Questa sezione \\xE8 in fase di implementazione.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 26\n          }, this),\n          sx: {\n            mt: 2\n          },\n          children: \"Aggiungi Nuovo Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Parco Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Questa sezione \\xE8 in fase di implementazione.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Gestione Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Questa sezione \\xE8 in fase di implementazione.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 4,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Questa sezione \\xE8 in fase di implementazione.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 5,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Certificazione Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Questa sezione \\xE8 in fase di implementazione.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 6,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Gestione Comande\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Questa sezione \\xE8 in fase di implementazione.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: notification.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseNotification,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseNotification,\n        severity: notification.severity,\n        sx: {\n          width: '100%'\n        },\n        children: notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n};\n_s(CaviPage, \"H0YWk9Gm3BVs1UeJwTZtBK+Wkek=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c2 = CaviPage;\nexport default CaviPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"TabPanel\");\n$RefreshReg$(_c2, \"CaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Tabs", "Tab", "<PERSON><PERSON>", "Snackbar", "IconButton", "ArrowBack", "ArrowBackIcon", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "useNavigate", "useAuth", "caviService", "jsxDEV", "_jsxDEV", "TabPanel", "props", "children", "value", "index", "other", "role", "hidden", "id", "sx", "p", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "CaviPage", "_s", "user", "navigate", "tabValue", "setTabValue", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "notification", "setNotification", "open", "message", "severity", "fetchData", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "attivi", "get<PERSON><PERSON>", "spare", "err", "console", "handleTabChange", "event", "newValue", "handleBackToCantieri", "handleCloseNotification", "renderCaviTable", "cavi", "length", "container", "spacing", "map", "cavo", "item", "xs", "sm", "md", "variant", "component", "id_cavo", "color", "sistema", "tipologia", "ubicazione_partenza", "utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "stato_installazione", "size", "startIcon", "mb", "display", "alignItems", "onClick", "mr", "width", "borderBottom", "borderColor", "onChange", "indicatorColor", "textColor", "scrollButtons", "allowScrollButtonsMobile", "label", "gutterBottom", "mt", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/CaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Box, \n  Typography, \n  Paper, \n  Button, \n  Grid, \n  Card, \n  CardContent, \n  CardActions,\n  Tabs,\n  Tab,\n  Alert,\n  Snackbar,\n  IconButton\n} from '@mui/material';\nimport { \n  ArrowBack as ArrowBackIcon,\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport caviService from '../services/caviService';\n\n// Componente per il pannello delle tab\nfunction TabPanel(props) {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`cavi-tabpanel-${index}`}\n      aria-labelledby={`cavi-tab-${index}`}\n      {...other}\n    >\n      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}\n    </div>\n  );\n}\n\nconst CaviPage = () => {\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  const [tabValue, setTabValue] = useState(0);\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    const fetchData = async () => {\n      // Recupera l'ID del cantiere selezionato dal localStorage\n      const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n      const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n      \n      if (!selectedCantiereId) {\n        setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n        setLoading(false);\n        return;\n      }\n      \n      setCantiereId(selectedCantiereId);\n      setCantiereName(selectedCantiereName || `Cantiere ${selectedCantiereId}`);\n      \n      try {\n        setLoading(true);\n        // Carica i cavi attivi\n        const attivi = await caviService.getCavi(selectedCantiereId, 0);\n        setCaviAttivi(attivi);\n        \n        // Carica i cavi spare\n        const spare = await caviService.getCavi(selectedCantiereId, 3);\n        setCaviSpare(spare);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        setError('Impossibile caricare i cavi. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  // Gestione del cambio di tab\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n  };\n\n  // Torna alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = (cavi) => {\n    if (cavi.length === 0) {\n      return (\n        <Alert severity=\"info\">Nessun cavo trovato in questa categoria.</Alert>\n      );\n    }\n\n    return (\n      <Grid container spacing={2}>\n        {cavi.map((cavo) => (\n          <Grid item xs={12} sm={6} md={4} key={cavo.id_cavo}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" component=\"div\">\n                  {cavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Sistema: {cavo.sistema || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Tipologia: {cavo.tipologia || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Partenza: {cavo.ubicazione_partenza || 'N/A'} - {cavo.utenza_partenza || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Arrivo: {cavo.ubicazione_arrivo || 'N/A'} - {cavo.utenza_arrivo || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Metri teorici: {cavo.metri_teorici || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Metratura reale: {cavo.metratura_reale || '0'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Stato: {cavo.stato_installazione || 'N/A'}\n                </Typography>\n              </CardContent>\n              <CardActions>\n                <Button size=\"small\" startIcon={<EditIcon />}>\n                  Modifica\n                </Button>\n                <Button size=\"small\" color=\"error\" startIcon={<DeleteIcon />}>\n                  Elimina\n                </Button>\n              </CardActions>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n    );\n  };\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>\n        <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n          <ArrowBackIcon />\n        </IconButton>\n        <Typography variant=\"h4\">\n          Gestione Cavi\n        </Typography>\n      </Box>\n\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Typography variant=\"h6\">\n          Cantiere: {cantiereName} (ID: {cantiereId})\n        </Typography>\n      </Paper>\n\n      {loading ? (\n        <Typography>Caricamento cavi...</Typography>\n      ) : error ? (\n        <Alert severity=\"error\">{error}</Alert>\n      ) : (\n        <Box sx={{ width: '100%' }}>\n          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\n            <Tabs\n              value={tabValue}\n              onChange={handleTabChange}\n              indicatorColor=\"primary\"\n              textColor=\"primary\"\n              variant=\"scrollable\"\n              scrollButtons=\"auto\"\n              allowScrollButtonsMobile\n            >\n              <Tab label=\"Visualizza Cavi\" />\n              <Tab label=\"Posa Cavi e Collegamenti\" />\n              <Tab label=\"Parco Cavi\" />\n              <Tab label=\"Gestione Excel\" />\n              <Tab label=\"Report\" />\n              <Tab label=\"Certificazione Cavi\" />\n              <Tab label=\"Gestione Comande\" />\n            </Tabs>\n          </Box>\n\n          {/* Tab Visualizza Cavi */}\n          <TabPanel value={tabValue} index={0}>\n            <Box sx={{ mb: 3 }}>\n              <Typography variant=\"h5\" gutterBottom>\n                Cavi Attivi\n              </Typography>\n              {renderCaviTable(caviAttivi)}\n            </Box>\n            \n            <Box sx={{ mt: 4 }}>\n              <Typography variant=\"h5\" gutterBottom>\n                Cavi Spare\n              </Typography>\n              {renderCaviTable(caviSpare)}\n            </Box>\n          </TabPanel>\n\n          {/* Tab Posa Cavi e Collegamenti */}\n          <TabPanel value={tabValue} index={1}>\n            <Typography variant=\"h5\" gutterBottom>\n              Posa Cavi e Collegamenti\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              Questa sezione è in fase di implementazione.\n            </Typography>\n            <Button \n              variant=\"contained\" \n              color=\"primary\" \n              startIcon={<AddIcon />}\n              sx={{ mt: 2 }}\n            >\n              Aggiungi Nuovo Cavo\n            </Button>\n          </TabPanel>\n\n          {/* Tab Parco Cavi */}\n          <TabPanel value={tabValue} index={2}>\n            <Typography variant=\"h5\" gutterBottom>\n              Parco Cavi\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              Questa sezione è in fase di implementazione.\n            </Typography>\n          </TabPanel>\n\n          {/* Tab Gestione Excel */}\n          <TabPanel value={tabValue} index={3}>\n            <Typography variant=\"h5\" gutterBottom>\n              Gestione Excel\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              Questa sezione è in fase di implementazione.\n            </Typography>\n          </TabPanel>\n\n          {/* Tab Report */}\n          <TabPanel value={tabValue} index={4}>\n            <Typography variant=\"h5\" gutterBottom>\n              Report\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              Questa sezione è in fase di implementazione.\n            </Typography>\n          </TabPanel>\n\n          {/* Tab Certificazione Cavi */}\n          <TabPanel value={tabValue} index={5}>\n            <Typography variant=\"h5\" gutterBottom>\n              Certificazione Cavi\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              Questa sezione è in fase di implementazione.\n            </Typography>\n          </TabPanel>\n\n          {/* Tab Gestione Comande */}\n          <TabPanel value={tabValue} index={6}>\n            <Typography variant=\"h5\" gutterBottom>\n              Gestione Comande\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              Questa sezione è in fase di implementazione.\n            </Typography>\n          </TabPanel>\n        </Box>\n      )}\n\n      {/* Notifica */}\n      <Snackbar\n        open={notification.open}\n        autoHideDuration={6000}\n        onClose={handleCloseNotification}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n          {notification.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default CaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,QAAQ,EACRC,UAAU,QACL,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,WAAW,MAAM,yBAAyB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,MAAM;IAAEC,QAAQ;IAAEC,KAAK;IAAEC,KAAK;IAAE,GAAGC;EAAM,CAAC,GAAGJ,KAAK;EAElD,oBACEF,OAAA;IACEO,IAAI,EAAC,UAAU;IACfC,MAAM,EAAEJ,KAAK,KAAKC,KAAM;IACxBI,EAAE,EAAE,iBAAiBJ,KAAK,EAAG;IAC7B,mBAAiB,YAAYA,KAAK,EAAG;IAAA,GACjCC,KAAK;IAAAH,QAAA,EAERC,KAAK,KAAKC,KAAK,iBAAIL,OAAA,CAACzB,GAAG;MAACmC,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAEA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpD,CAAC;AAEV;AAACC,EAAA,GAdQf,QAAQ;AAgBjB,MAAMgB,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAK,CAAC,GAAGtB,OAAO,CAAC,CAAC;EAC1B,MAAMuB,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwD,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0D,OAAO,EAAEC,UAAU,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4D,KAAK,EAAEC,QAAQ,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC;IAC/CgE,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACAjE,SAAS,CAAC,MAAM;IACd,MAAMkE,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B;MACA,MAAMC,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;MACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;MAEzE,IAAI,CAACF,kBAAkB,EAAE;QACvBP,QAAQ,CAAC,8DAA8D,CAAC;QACxEF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEAR,aAAa,CAACiB,kBAAkB,CAAC;MACjCf,eAAe,CAACkB,oBAAoB,IAAI,YAAYH,kBAAkB,EAAE,CAAC;MAEzE,IAAI;QACFT,UAAU,CAAC,IAAI,CAAC;QAChB;QACA,MAAMa,MAAM,GAAG,MAAM/C,WAAW,CAACgD,OAAO,CAACL,kBAAkB,EAAE,CAAC,CAAC;QAC/Db,aAAa,CAACiB,MAAM,CAAC;;QAErB;QACA,MAAME,KAAK,GAAG,MAAMjD,WAAW,CAACgD,OAAO,CAACL,kBAAkB,EAAE,CAAC,CAAC;QAC9DX,YAAY,CAACiB,KAAK,CAAC;MACrB,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAAChB,KAAK,CAAC,kCAAkC,EAAEe,GAAG,CAAC;QACtDd,QAAQ,CAAC,iDAAiD,CAAC;MAC7D,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDQ,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3C9B,WAAW,CAAC8B,QAAQ,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCjC,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAED;EACA,MAAMkC,uBAAuB,GAAGA,CAAA,KAAM;IACpClB,eAAe,CAAC;MACd,GAAGD,YAAY;MACfE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMkB,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;MACrB,oBACEzD,OAAA,CAACf,KAAK;QAACsD,QAAQ,EAAC,MAAM;QAAApC,QAAA,EAAC;MAAwC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAE3E;IAEA,oBACEf,OAAA,CAACrB,IAAI;MAAC+E,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAxD,QAAA,EACxBqD,IAAI,CAACI,GAAG,CAAEC,IAAI,iBACb7D,OAAA,CAACrB,IAAI;QAACmF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA9D,QAAA,eAC9BH,OAAA,CAACpB,IAAI;UAAAuB,QAAA,gBACHH,OAAA,CAACnB,WAAW;YAAAsB,QAAA,gBACVH,OAAA,CAACxB,UAAU;cAAC0F,OAAO,EAAC,IAAI;cAACC,SAAS,EAAC,KAAK;cAAAhE,QAAA,EACrC0D,IAAI,CAACO;YAAO;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACbf,OAAA,CAACxB,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAlE,QAAA,GAAC,WACxC,EAAC0D,IAAI,CAACS,OAAO,IAAI,KAAK;YAAA;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACbf,OAAA,CAACxB,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAlE,QAAA,GAAC,aACtC,EAAC0D,IAAI,CAACU,SAAS,IAAI,KAAK;YAAA;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACbf,OAAA,CAACxB,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAlE,QAAA,GAAC,YACvC,EAAC0D,IAAI,CAACW,mBAAmB,IAAI,KAAK,EAAC,KAAG,EAACX,IAAI,CAACY,eAAe,IAAI,KAAK;YAAA;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACbf,OAAA,CAACxB,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAlE,QAAA,GAAC,UACzC,EAAC0D,IAAI,CAACa,iBAAiB,IAAI,KAAK,EAAC,KAAG,EAACb,IAAI,CAACc,aAAa,IAAI,KAAK;YAAA;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACbf,OAAA,CAACxB,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAlE,QAAA,GAAC,iBAClC,EAAC0D,IAAI,CAACe,aAAa,IAAI,KAAK;YAAA;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACbf,OAAA,CAACxB,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAlE,QAAA,GAAC,mBAChC,EAAC0D,IAAI,CAACgB,eAAe,IAAI,GAAG;YAAA;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACbf,OAAA,CAACxB,UAAU;cAAC0F,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAAlE,QAAA,GAAC,SAC1C,EAAC0D,IAAI,CAACiB,mBAAmB,IAAI,KAAK;YAAA;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdf,OAAA,CAAClB,WAAW;YAAAqB,QAAA,gBACVH,OAAA,CAACtB,MAAM;cAACqG,IAAI,EAAC,OAAO;cAACC,SAAS,eAAEhF,OAAA,CAACP,QAAQ;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAZ,QAAA,EAAC;YAE9C;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTf,OAAA,CAACtB,MAAM;cAACqG,IAAI,EAAC,OAAO;cAACV,KAAK,EAAC,OAAO;cAACW,SAAS,eAAEhF,OAAA,CAACL,UAAU;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAZ,QAAA,EAAC;YAE9D;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GApC6B8C,IAAI,CAACO,OAAO;QAAAxD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqC5C,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX,CAAC;EAED,oBACEf,OAAA,CAACzB,GAAG;IAAA4B,QAAA,gBACFH,OAAA,CAACzB,GAAG;MAACmC,EAAE,EAAE;QAAEuE,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAhF,QAAA,gBACxDH,OAAA,CAACb,UAAU;QAACiG,OAAO,EAAE/B,oBAAqB;QAAC3C,EAAE,EAAE;UAAE2E,EAAE,EAAE;QAAE,CAAE;QAAAlF,QAAA,eACvDH,OAAA,CAACX,aAAa;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eACbf,OAAA,CAACxB,UAAU;QAAC0F,OAAO,EAAC,IAAI;QAAA/D,QAAA,EAAC;MAEzB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAENf,OAAA,CAACvB,KAAK;MAACiC,EAAE,EAAE;QAAEuE,EAAE,EAAE,CAAC;QAAEtE,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,eACzBH,OAAA,CAACxB,UAAU;QAAC0F,OAAO,EAAC,IAAI;QAAA/D,QAAA,GAAC,YACb,EAACsB,YAAY,EAAC,QAAM,EAACF,UAAU,EAAC,GAC5C;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAEPgB,OAAO,gBACN/B,OAAA,CAACxB,UAAU;MAAA2B,QAAA,EAAC;IAAmB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,GAC1CkB,KAAK,gBACPjC,OAAA,CAACf,KAAK;MAACsD,QAAQ,EAAC,OAAO;MAAApC,QAAA,EAAE8B;IAAK;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,gBAEvCf,OAAA,CAACzB,GAAG;MAACmC,EAAE,EAAE;QAAE4E,KAAK,EAAE;MAAO,CAAE;MAAAnF,QAAA,gBACzBH,OAAA,CAACzB,GAAG;QAACmC,EAAE,EAAE;UAAE6E,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAU,CAAE;QAAArF,QAAA,eACnDH,OAAA,CAACjB,IAAI;UACHqB,KAAK,EAAEiB,QAAS;UAChBoE,QAAQ,EAAEvC,eAAgB;UAC1BwC,cAAc,EAAC,SAAS;UACxBC,SAAS,EAAC,SAAS;UACnBzB,OAAO,EAAC,YAAY;UACpB0B,aAAa,EAAC,MAAM;UACpBC,wBAAwB;UAAA1F,QAAA,gBAExBH,OAAA,CAAChB,GAAG;YAAC8G,KAAK,EAAC;UAAiB;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/Bf,OAAA,CAAChB,GAAG;YAAC8G,KAAK,EAAC;UAA0B;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxCf,OAAA,CAAChB,GAAG;YAAC8G,KAAK,EAAC;UAAY;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1Bf,OAAA,CAAChB,GAAG;YAAC8G,KAAK,EAAC;UAAgB;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9Bf,OAAA,CAAChB,GAAG;YAAC8G,KAAK,EAAC;UAAQ;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtBf,OAAA,CAAChB,GAAG;YAAC8G,KAAK,EAAC;UAAqB;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnCf,OAAA,CAAChB,GAAG;YAAC8G,KAAK,EAAC;UAAkB;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEiB,QAAS;QAAChB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAACzB,GAAG;UAACmC,EAAE,EAAE;YAAEuE,EAAE,EAAE;UAAE,CAAE;UAAA9E,QAAA,gBACjBH,OAAA,CAACxB,UAAU;YAAC0F,OAAO,EAAC,IAAI;YAAC6B,YAAY;YAAA5F,QAAA,EAAC;UAEtC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZwC,eAAe,CAAC5B,UAAU,CAAC;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAENf,OAAA,CAACzB,GAAG;UAACmC,EAAE,EAAE;YAAEsF,EAAE,EAAE;UAAE,CAAE;UAAA7F,QAAA,gBACjBH,OAAA,CAACxB,UAAU;YAAC0F,OAAO,EAAC,IAAI;YAAC6B,YAAY;YAAA5F,QAAA,EAAC;UAEtC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZwC,eAAe,CAAC1B,SAAS,CAAC;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEiB,QAAS;QAAChB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAACxB,UAAU;UAAC0F,OAAO,EAAC,IAAI;UAAC6B,YAAY;UAAA5F,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAACxB,UAAU;UAAC0F,OAAO,EAAC,OAAO;UAAC6B,YAAY;UAAA5F,QAAA,EAAC;QAEzC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAACtB,MAAM;UACLwF,OAAO,EAAC,WAAW;UACnBG,KAAK,EAAC,SAAS;UACfW,SAAS,eAAEhF,OAAA,CAACT,OAAO;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBL,EAAE,EAAE;YAAEsF,EAAE,EAAE;UAAE,CAAE;UAAA7F,QAAA,EACf;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEiB,QAAS;QAAChB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAACxB,UAAU;UAAC0F,OAAO,EAAC,IAAI;UAAC6B,YAAY;UAAA5F,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAACxB,UAAU;UAAC0F,OAAO,EAAC,OAAO;UAAC6B,YAAY;UAAA5F,QAAA,EAAC;QAEzC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEiB,QAAS;QAAChB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAACxB,UAAU;UAAC0F,OAAO,EAAC,IAAI;UAAC6B,YAAY;UAAA5F,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAACxB,UAAU;UAAC0F,OAAO,EAAC,OAAO;UAAC6B,YAAY;UAAA5F,QAAA,EAAC;QAEzC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEiB,QAAS;QAAChB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAACxB,UAAU;UAAC0F,OAAO,EAAC,IAAI;UAAC6B,YAAY;UAAA5F,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAACxB,UAAU;UAAC0F,OAAO,EAAC,OAAO;UAAC6B,YAAY;UAAA5F,QAAA,EAAC;QAEzC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEiB,QAAS;QAAChB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAACxB,UAAU;UAAC0F,OAAO,EAAC,IAAI;UAAC6B,YAAY;UAAA5F,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAACxB,UAAU;UAAC0F,OAAO,EAAC,OAAO;UAAC6B,YAAY;UAAA5F,QAAA,EAAC;QAEzC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEiB,QAAS;QAAChB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAACxB,UAAU;UAAC0F,OAAO,EAAC,IAAI;UAAC6B,YAAY;UAAA5F,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAACxB,UAAU;UAAC0F,OAAO,EAAC,OAAO;UAAC6B,YAAY;UAAA5F,QAAA,EAAC;QAEzC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACN,eAGDf,OAAA,CAACd,QAAQ;MACPmD,IAAI,EAAEF,YAAY,CAACE,IAAK;MACxB4D,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAE5C,uBAAwB;MACjC6C,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAlG,QAAA,eAE3DH,OAAA,CAACf,KAAK;QAACiH,OAAO,EAAE5C,uBAAwB;QAACf,QAAQ,EAAEJ,YAAY,CAACI,QAAS;QAAC7B,EAAE,EAAE;UAAE4E,KAAK,EAAE;QAAO,CAAE;QAAAnF,QAAA,EAC7FgC,YAAY,CAACG;MAAO;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACG,EAAA,CA3QID,QAAQ;EAAA,QACKpB,OAAO,EACPD,WAAW;AAAA;AAAA0G,GAAA,GAFxBrF,QAAQ;AA6Qd,eAAeA,QAAQ;AAAC,IAAAD,EAAA,EAAAsF,GAAA;AAAAC,YAAA,CAAAvF,EAAA;AAAAuF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}