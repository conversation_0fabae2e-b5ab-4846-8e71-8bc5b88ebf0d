{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"пр.н.е.\", \"н.е.\"],\n  abbreviated: [\"пред н. е.\", \"н. е.\"],\n  wide: [\"пред нашата ера\", \"нашата ера\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-ви кв.\", \"2-ри кв.\", \"3-ти кв.\", \"4-ти кв.\"],\n  wide: [\"1-ви квартал\", \"2-ри квартал\", \"3-ти квартал\", \"4-ти квартал\"]\n};\nconst monthValues = {\n  abbreviated: [\"јан\", \"фев\", \"мар\", \"апр\", \"мај\", \"јун\", \"јул\", \"авг\", \"септ\", \"окт\", \"ноем\", \"дек\"],\n  wide: [\"јануари\", \"февруари\", \"март\", \"април\", \"мај\", \"јуни\", \"јули\", \"август\", \"септември\", \"октомври\", \"ноември\", \"декември\"]\n};\nconst dayValues = {\n  narrow: [\"Н\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"не\", \"по\", \"вт\", \"ср\", \"че\", \"пе\", \"са\"],\n  abbreviated: [\"нед\", \"пон\", \"вто\", \"сре\", \"чет\", \"пет\", \"саб\"],\n  wide: [\"недела\", \"понеделник\", \"вторник\", \"среда\", \"четврток\", \"петок\", \"сабота\"]\n};\nconst dayPeriodValues = {\n  wide: {\n    am: \"претпладне\",\n    pm: \"попладне\",\n    midnight: \"полноќ\",\n    noon: \"напладне\",\n    morning: \"наутро\",\n    afternoon: \"попладне\",\n    evening: \"навечер\",\n    night: \"ноќе\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  const rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + \"-ви\";\n      case 2:\n        return number + \"-ри\";\n      case 7:\n      case 8:\n        return number + \"-ми\";\n    }\n  }\n  return number + \"-ти\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "rem100", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/mk/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"пр.н.е.\", \"н.е.\"],\n  abbreviated: [\"пред н. е.\", \"н. е.\"],\n  wide: [\"пред нашата ера\", \"нашата ера\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-ви кв.\", \"2-ри кв.\", \"3-ти кв.\", \"4-ти кв.\"],\n  wide: [\"1-ви квартал\", \"2-ри квартал\", \"3-ти квартал\", \"4-ти квартал\"],\n};\n\nconst monthValues = {\n  abbreviated: [\n    \"јан\",\n    \"фев\",\n    \"мар\",\n    \"апр\",\n    \"мај\",\n    \"јун\",\n    \"јул\",\n    \"авг\",\n    \"септ\",\n    \"окт\",\n    \"ноем\",\n    \"дек\",\n  ],\n\n  wide: [\n    \"јануари\",\n    \"февруари\",\n    \"март\",\n    \"април\",\n    \"мај\",\n    \"јуни\",\n    \"јули\",\n    \"август\",\n    \"септември\",\n    \"октомври\",\n    \"ноември\",\n    \"декември\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"Н\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"не\", \"по\", \"вт\", \"ср\", \"че\", \"пе\", \"са\"],\n  abbreviated: [\"нед\", \"пон\", \"вто\", \"сре\", \"чет\", \"пет\", \"саб\"],\n  wide: [\n    \"недела\",\n    \"понеделник\",\n    \"вторник\",\n    \"среда\",\n    \"четврток\",\n    \"петок\",\n    \"сабота\",\n  ],\n};\n\nconst dayPeriodValues = {\n  wide: {\n    am: \"претпладне\",\n    pm: \"попладне\",\n    midnight: \"полноќ\",\n    noon: \"напладне\",\n    morning: \"наутро\",\n    afternoon: \"попладне\",\n    evening: \"навечер\",\n    night: \"ноќе\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n\n  const rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + \"-ви\";\n      case 2:\n        return number + \"-ри\";\n      case 7:\n      case 8:\n        return number + \"-ми\";\n    }\n  }\n  return number + \"-ти\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;EAC3BC,WAAW,EAAE,CAAC,YAAY,EAAE,OAAO,CAAC;EACpCC,IAAI,EAAE,CAAC,iBAAiB,EAAE,YAAY;AACxC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;EAC7DC,IAAI,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc;AACvE,CAAC;AAED,MAAME,WAAW,GAAG;EAClBH,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,SAAS,EACT,UAAU,EACV,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,MAAM,EACN,QAAQ,EACR,WAAW,EACX,UAAU,EACV,SAAS,EACT,UAAU;AAEd,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CACJ,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,OAAO,EACP,UAAU,EACV,OAAO,EACP,QAAQ;AAEZ,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBL,IAAI,EAAE;IACJM,EAAE,EAAE,YAAY;IAChBC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAElC,MAAMI,MAAM,GAAGF,MAAM,GAAG,GAAG;EAC3B,IAAIE,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,EAAE,EAAE;IAC9B,QAAQA,MAAM,GAAG,EAAE;MACjB,KAAK,CAAC;QACJ,OAAOF,MAAM,GAAG,KAAK;MACvB,KAAK,CAAC;QACJ,OAAOA,MAAM,GAAG,KAAK;MACvB,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAOA,MAAM,GAAG,KAAK;IACzB;EACF;EACA,OAAOA,MAAM,GAAG,KAAK;AACvB,CAAC;AAED,OAAO,MAAMG,QAAQ,GAAG;EACtBN,aAAa;EAEbO,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE;EAChB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}