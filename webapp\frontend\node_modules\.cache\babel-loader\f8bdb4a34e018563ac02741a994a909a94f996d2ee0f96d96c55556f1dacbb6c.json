{"ast": null, "code": "import { formatDistance } from \"./ar-EG/_lib/formatDistance.js\";\nimport { formatLong } from \"./ar-EG/_lib/formatLong.js\";\nimport { formatRelative } from \"./ar-EG/_lib/formatRelative.js\";\nimport { localize } from \"./ar-EG/_lib/localize.js\";\nimport { match } from \"./ar-EG/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Arabic locale (Egypt).\n * @language Arabic\n * @iso-639-2 ara\n * <AUTHOR> [@AbdAllahAbdElFattah13](https://github.com/AbdAllahAbdElFattah13)\n */\nexport const arEG = {\n  code: \"ar-EG\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default arEG;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "arEG", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/ar-EG.js"], "sourcesContent": ["import { formatDistance } from \"./ar-EG/_lib/formatDistance.js\";\nimport { formatLong } from \"./ar-EG/_lib/formatLong.js\";\nimport { formatRelative } from \"./ar-EG/_lib/formatRelative.js\";\nimport { localize } from \"./ar-EG/_lib/localize.js\";\nimport { match } from \"./ar-EG/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Arabic locale (Egypt).\n * @language Arabic\n * @iso-639-2 ara\n * <AUTHOR> [@AbdAllahAbdElFattah13](https://github.com/AbdAllahAbdElFattah13)\n */\nexport const arEG = {\n  code: \"ar-EG\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default arEG;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,KAAK,QAAQ,uBAAuB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}