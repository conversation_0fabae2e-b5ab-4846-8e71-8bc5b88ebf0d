{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\CaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, CardActions, Tabs, Tab, Alert, Snackbar, IconButton } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Home as HomeIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport caviService from '../services/caviService';\n\n// Componente per il pannello delle tab\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TabPanel(props) {\n  const {\n    children,\n    value,\n    index,\n    ...other\n  } = props;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    role: \"tabpanel\",\n    hidden: value !== index,\n    id: `cavi-tabpanel-${index}`,\n    \"aria-labelledby\": `cavi-tab-${index}`,\n    ...other,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n}\n_c = TabPanel;\nconst CaviPage = () => {\n  _s();\n  const {\n    user,\n    isImpersonating\n  } = useAuth();\n  const navigate = useNavigate();\n  const [tabValue, setTabValue] = useState(0);\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    const fetchData = async () => {\n      // Recupera l'ID del cantiere selezionato dal localStorage\n      const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n      const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n      if (!selectedCantiereId) {\n        setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n        setLoading(false);\n        return;\n      }\n      setCantiereId(selectedCantiereId);\n      setCantiereName(selectedCantiereName || `Cantiere ${selectedCantiereId}`);\n      try {\n        setLoading(true);\n        // Carica i cavi attivi\n        const attivi = await caviService.getCavi(selectedCantiereId, 0);\n        setCaviAttivi(attivi);\n\n        // Carica i cavi spare\n        const spare = await caviService.getCavi(selectedCantiereId, 3);\n        setCaviSpare(spare);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        setError('Impossibile caricare i cavi. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n\n  // Gestione del cambio di tab\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n  };\n\n  // Torna alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Torna al menu amministratore (solo per admin che impersonano un utente)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = cavi => {\n    if (cavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Nessun cavo trovato in questa categoria.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"div\",\n              children: cavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Sistema: \", cavo.sistema || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Tipologia: \", cavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Partenza: \", cavo.ubicazione_partenza || 'N/A', \" - \", cavo.utenza_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Arrivo: \", cavo.ubicazione_arrivo || 'N/A', \" - \", cavo.utenza_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Metratura reale: \", cavo.metratura_reale || '0']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Stato: \", cavo.stato_installazione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 49\n              }, this),\n              children: \"Modifica\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: \"error\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 63\n              }, this),\n              children: \"Elimina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this)\n      }, cavo.id_cavo, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToCantieri,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          children: \"Gestione Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), isImpersonating && /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 24\n        }, this),\n        onClick: handleBackToAdmin,\n        children: \"Torna al Menu Admin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: [\"Cantiere: \", cantiereName, \" (ID: \", cantiereId, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Caricamento cavi...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: tabValue,\n          onChange: handleTabChange,\n          indicatorColor: \"primary\",\n          textColor: \"primary\",\n          variant: \"scrollable\",\n          scrollButtons: \"auto\",\n          allowScrollButtonsMobile: true,\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Visualizza Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Posa Cavi e Collegamenti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Parco Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Gestione Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Certificazione Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Gestione Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 0,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            children: \"Cavi Attivi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this), renderCaviTable(caviAttivi)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            children: \"Cavi Spare\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this), renderCaviTable(caviSpare)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 1,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Posa Cavi e Collegamenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Questa sezione \\xE8 in fase di implementazione.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 26\n          }, this),\n          sx: {\n            mt: 2\n          },\n          children: \"Aggiungi Nuovo Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Parco Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Questa sezione \\xE8 in fase di implementazione.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Gestione Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Questa sezione \\xE8 in fase di implementazione.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 4,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Questa sezione \\xE8 in fase di implementazione.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 5,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Certificazione Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Questa sezione \\xE8 in fase di implementazione.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 6,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Gestione Comande\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Questa sezione \\xE8 in fase di implementazione.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: notification.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseNotification,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseNotification,\n        severity: notification.severity,\n        sx: {\n          width: '100%'\n        },\n        children: notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this);\n};\n_s(CaviPage, \"ChGZgydcMIVCPaSyRuVIqEX804c=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c2 = CaviPage;\nexport default CaviPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"TabPanel\");\n$RefreshReg$(_c2, \"CaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Tabs", "Tab", "<PERSON><PERSON>", "Snackbar", "IconButton", "ArrowBack", "ArrowBackIcon", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Home", "HomeIcon", "useNavigate", "useAuth", "caviService", "jsxDEV", "_jsxDEV", "TabPanel", "props", "children", "value", "index", "other", "role", "hidden", "id", "sx", "p", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "CaviPage", "_s", "user", "isImpersonating", "navigate", "tabValue", "setTabValue", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "notification", "setNotification", "open", "message", "severity", "fetchData", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "attivi", "get<PERSON><PERSON>", "spare", "err", "console", "handleTabChange", "event", "newValue", "handleBackToCantieri", "handleBackToAdmin", "handleCloseNotification", "renderCaviTable", "cavi", "length", "container", "spacing", "map", "cavo", "item", "xs", "sm", "md", "variant", "component", "id_cavo", "color", "sistema", "tipologia", "ubicazione_partenza", "utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "stato_installazione", "size", "startIcon", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "width", "borderBottom", "borderColor", "onChange", "indicatorColor", "textColor", "scrollButtons", "allowScrollButtonsMobile", "label", "gutterBottom", "mt", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/CaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Tabs,\n  Tab,\n  Alert,\n  Snackbar,\n  IconButton\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Home as HomeIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport caviService from '../services/caviService';\n\n// Componente per il pannello delle tab\nfunction TabPanel(props) {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`cavi-tabpanel-${index}`}\n      aria-labelledby={`cavi-tab-${index}`}\n      {...other}\n    >\n      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}\n    </div>\n  );\n}\n\nconst CaviPage = () => {\n  const { user, isImpersonating } = useAuth();\n  const navigate = useNavigate();\n  const [tabValue, setTabValue] = useState(0);\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    const fetchData = async () => {\n      // Recupera l'ID del cantiere selezionato dal localStorage\n      const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n      const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n      if (!selectedCantiereId) {\n        setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n        setLoading(false);\n        return;\n      }\n\n      setCantiereId(selectedCantiereId);\n      setCantiereName(selectedCantiereName || `Cantiere ${selectedCantiereId}`);\n\n      try {\n        setLoading(true);\n        // Carica i cavi attivi\n        const attivi = await caviService.getCavi(selectedCantiereId, 0);\n        setCaviAttivi(attivi);\n\n        // Carica i cavi spare\n        const spare = await caviService.getCavi(selectedCantiereId, 3);\n        setCaviSpare(spare);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        setError('Impossibile caricare i cavi. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  // Gestione del cambio di tab\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n  };\n\n  // Torna alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Torna al menu amministratore (solo per admin che impersonano un utente)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = (cavi) => {\n    if (cavi.length === 0) {\n      return (\n        <Alert severity=\"info\">Nessun cavo trovato in questa categoria.</Alert>\n      );\n    }\n\n    return (\n      <Grid container spacing={2}>\n        {cavi.map((cavo) => (\n          <Grid item xs={12} sm={6} md={4} key={cavo.id_cavo}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" component=\"div\">\n                  {cavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Sistema: {cavo.sistema || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Tipologia: {cavo.tipologia || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Partenza: {cavo.ubicazione_partenza || 'N/A'} - {cavo.utenza_partenza || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Arrivo: {cavo.ubicazione_arrivo || 'N/A'} - {cavo.utenza_arrivo || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Metri teorici: {cavo.metri_teorici || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Metratura reale: {cavo.metratura_reale || '0'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Stato: {cavo.stato_installazione || 'N/A'}\n                </Typography>\n              </CardContent>\n              <CardActions>\n                <Button size=\"small\" startIcon={<EditIcon />}>\n                  Modifica\n                </Button>\n                <Button size=\"small\" color=\"error\" startIcon={<DeleteIcon />}>\n                  Elimina\n                </Button>\n              </CardActions>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n    );\n  };\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Gestione Cavi\n          </Typography>\n        </Box>\n        {isImpersonating && (\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<HomeIcon />}\n            onClick={handleBackToAdmin}\n          >\n            Torna al Menu Admin\n          </Button>\n        )}\n      </Box>\n\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Typography variant=\"h6\">\n          Cantiere: {cantiereName} (ID: {cantiereId})\n        </Typography>\n      </Paper>\n\n      {loading ? (\n        <Typography>Caricamento cavi...</Typography>\n      ) : error ? (\n        <Alert severity=\"error\">{error}</Alert>\n      ) : (\n        <Box sx={{ width: '100%' }}>\n          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\n            <Tabs\n              value={tabValue}\n              onChange={handleTabChange}\n              indicatorColor=\"primary\"\n              textColor=\"primary\"\n              variant=\"scrollable\"\n              scrollButtons=\"auto\"\n              allowScrollButtonsMobile\n            >\n              <Tab label=\"Visualizza Cavi\" />\n              <Tab label=\"Posa Cavi e Collegamenti\" />\n              <Tab label=\"Parco Cavi\" />\n              <Tab label=\"Gestione Excel\" />\n              <Tab label=\"Certificazione Cavi\" />\n              <Tab label=\"Gestione Comande\" />\n            </Tabs>\n          </Box>\n\n          {/* Tab Visualizza Cavi */}\n          <TabPanel value={tabValue} index={0}>\n            <Box sx={{ mb: 3 }}>\n              <Typography variant=\"h5\" gutterBottom>\n                Cavi Attivi\n              </Typography>\n              {renderCaviTable(caviAttivi)}\n            </Box>\n\n            <Box sx={{ mt: 4 }}>\n              <Typography variant=\"h5\" gutterBottom>\n                Cavi Spare\n              </Typography>\n              {renderCaviTable(caviSpare)}\n            </Box>\n          </TabPanel>\n\n          {/* Tab Posa Cavi e Collegamenti */}\n          <TabPanel value={tabValue} index={1}>\n            <Typography variant=\"h5\" gutterBottom>\n              Posa Cavi e Collegamenti\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              Questa sezione è in fase di implementazione.\n            </Typography>\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              startIcon={<AddIcon />}\n              sx={{ mt: 2 }}\n            >\n              Aggiungi Nuovo Cavo\n            </Button>\n          </TabPanel>\n\n          {/* Tab Parco Cavi */}\n          <TabPanel value={tabValue} index={2}>\n            <Typography variant=\"h5\" gutterBottom>\n              Parco Cavi\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              Questa sezione è in fase di implementazione.\n            </Typography>\n          </TabPanel>\n\n          {/* Tab Gestione Excel */}\n          <TabPanel value={tabValue} index={3}>\n            <Typography variant=\"h5\" gutterBottom>\n              Gestione Excel\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              Questa sezione è in fase di implementazione.\n            </Typography>\n          </TabPanel>\n\n          {/* Tab Report */}\n          <TabPanel value={tabValue} index={4}>\n            <Typography variant=\"h5\" gutterBottom>\n              Report\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              Questa sezione è in fase di implementazione.\n            </Typography>\n          </TabPanel>\n\n          {/* Tab Certificazione Cavi */}\n          <TabPanel value={tabValue} index={5}>\n            <Typography variant=\"h5\" gutterBottom>\n              Certificazione Cavi\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              Questa sezione è in fase di implementazione.\n            </Typography>\n          </TabPanel>\n\n          {/* Tab Gestione Comande */}\n          <TabPanel value={tabValue} index={6}>\n            <Typography variant=\"h5\" gutterBottom>\n              Gestione Comande\n            </Typography>\n            <Typography variant=\"body1\" gutterBottom>\n              Questa sezione è in fase di implementazione.\n            </Typography>\n          </TabPanel>\n        </Box>\n      )}\n\n      {/* Notifica */}\n      <Snackbar\n        open={notification.open}\n        autoHideDuration={6000}\n        onClose={handleCloseNotification}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n          {notification.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default CaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,QAAQ,EACRC,UAAU,QACL,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,WAAW,MAAM,yBAAyB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,MAAM;IAAEC,QAAQ;IAAEC,KAAK;IAAEC,KAAK;IAAE,GAAGC;EAAM,CAAC,GAAGJ,KAAK;EAElD,oBACEF,OAAA;IACEO,IAAI,EAAC,UAAU;IACfC,MAAM,EAAEJ,KAAK,KAAKC,KAAM;IACxBI,EAAE,EAAE,iBAAiBJ,KAAK,EAAG;IAC7B,mBAAiB,YAAYA,KAAK,EAAG;IAAA,GACjCC,KAAK;IAAAH,QAAA,EAERC,KAAK,KAAKC,KAAK,iBAAIL,OAAA,CAAC3B,GAAG;MAACqC,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAEA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpD,CAAC;AAEV;AAACC,EAAA,GAdQf,QAAQ;AAgBjB,MAAMgB,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGvB,OAAO,CAAC,CAAC;EAC3C,MAAMwB,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyD,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2D,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+D,KAAK,EAAEC,QAAQ,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiE,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAAC;IAC/CmE,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACApE,SAAS,CAAC,MAAM;IACd,MAAMqE,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B;MACA,MAAMC,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;MACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;MAEzE,IAAI,CAACF,kBAAkB,EAAE;QACvBP,QAAQ,CAAC,8DAA8D,CAAC;QACxEF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEAR,aAAa,CAACiB,kBAAkB,CAAC;MACjCf,eAAe,CAACkB,oBAAoB,IAAI,YAAYH,kBAAkB,EAAE,CAAC;MAEzE,IAAI;QACFT,UAAU,CAAC,IAAI,CAAC;QAChB;QACA,MAAMa,MAAM,GAAG,MAAMhD,WAAW,CAACiD,OAAO,CAACL,kBAAkB,EAAE,CAAC,CAAC;QAC/Db,aAAa,CAACiB,MAAM,CAAC;;QAErB;QACA,MAAME,KAAK,GAAG,MAAMlD,WAAW,CAACiD,OAAO,CAACL,kBAAkB,EAAE,CAAC,CAAC;QAC9DX,YAAY,CAACiB,KAAK,CAAC;MACrB,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAAChB,KAAK,CAAC,kCAAkC,EAAEe,GAAG,CAAC;QACtDd,QAAQ,CAAC,iDAAiD,CAAC;MAC7D,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDQ,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3C9B,WAAW,CAAC8B,QAAQ,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCjC,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAED;EACA,MAAMkC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BlC,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMmC,uBAAuB,GAAGA,CAAA,KAAM;IACpCnB,eAAe,CAAC;MACd,GAAGD,YAAY;MACfE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMmB,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;MACrB,oBACE3D,OAAA,CAACjB,KAAK;QAACyD,QAAQ,EAAC,MAAM;QAAArC,QAAA,EAAC;MAAwC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAE3E;IAEA,oBACEf,OAAA,CAACvB,IAAI;MAACmF,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA1D,QAAA,EACxBuD,IAAI,CAACI,GAAG,CAAEC,IAAI,iBACb/D,OAAA,CAACvB,IAAI;QAACuF,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAhE,QAAA,eAC9BH,OAAA,CAACtB,IAAI;UAAAyB,QAAA,gBACHH,OAAA,CAACrB,WAAW;YAAAwB,QAAA,gBACVH,OAAA,CAAC1B,UAAU;cAAC8F,OAAO,EAAC,IAAI;cAACC,SAAS,EAAC,KAAK;cAAAlE,QAAA,EACrC4D,IAAI,CAACO;YAAO;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACbf,OAAA,CAAC1B,UAAU;cAAC8F,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAApE,QAAA,GAAC,WACxC,EAAC4D,IAAI,CAACS,OAAO,IAAI,KAAK;YAAA;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACbf,OAAA,CAAC1B,UAAU;cAAC8F,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAApE,QAAA,GAAC,aACtC,EAAC4D,IAAI,CAACU,SAAS,IAAI,KAAK;YAAA;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACbf,OAAA,CAAC1B,UAAU;cAAC8F,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAApE,QAAA,GAAC,YACvC,EAAC4D,IAAI,CAACW,mBAAmB,IAAI,KAAK,EAAC,KAAG,EAACX,IAAI,CAACY,eAAe,IAAI,KAAK;YAAA;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACbf,OAAA,CAAC1B,UAAU;cAAC8F,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAApE,QAAA,GAAC,UACzC,EAAC4D,IAAI,CAACa,iBAAiB,IAAI,KAAK,EAAC,KAAG,EAACb,IAAI,CAACc,aAAa,IAAI,KAAK;YAAA;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACbf,OAAA,CAAC1B,UAAU;cAAC8F,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAApE,QAAA,GAAC,iBAClC,EAAC4D,IAAI,CAACe,aAAa,IAAI,KAAK;YAAA;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACbf,OAAA,CAAC1B,UAAU;cAAC8F,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAApE,QAAA,GAAC,mBAChC,EAAC4D,IAAI,CAACgB,eAAe,IAAI,GAAG;YAAA;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACbf,OAAA,CAAC1B,UAAU;cAAC8F,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAApE,QAAA,GAAC,SAC1C,EAAC4D,IAAI,CAACiB,mBAAmB,IAAI,KAAK;YAAA;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdf,OAAA,CAACpB,WAAW;YAAAuB,QAAA,gBACVH,OAAA,CAACxB,MAAM;cAACyG,IAAI,EAAC,OAAO;cAACC,SAAS,eAAElF,OAAA,CAACT,QAAQ;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAZ,QAAA,EAAC;YAE9C;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTf,OAAA,CAACxB,MAAM;cAACyG,IAAI,EAAC,OAAO;cAACV,KAAK,EAAC,OAAO;cAACW,SAAS,eAAElF,OAAA,CAACP,UAAU;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAZ,QAAA,EAAC;YAE9D;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GApC6BgD,IAAI,CAACO,OAAO;QAAA1D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqC5C,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX,CAAC;EAED,oBACEf,OAAA,CAAC3B,GAAG;IAAA8B,QAAA,gBACFH,OAAA,CAAC3B,GAAG;MAACqC,EAAE,EAAE;QAAEyE,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAnF,QAAA,gBACzFH,OAAA,CAAC3B,GAAG;QAACqC,EAAE,EAAE;UAAE0E,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAlF,QAAA,gBACjDH,OAAA,CAACf,UAAU;UAACsG,OAAO,EAAEjC,oBAAqB;UAAC5C,EAAE,EAAE;YAAE8E,EAAE,EAAE;UAAE,CAAE;UAAArF,QAAA,eACvDH,OAAA,CAACb,aAAa;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACbf,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,IAAI;UAAAjE,QAAA,EAAC;QAEzB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EACLK,eAAe,iBACdpB,OAAA,CAACxB,MAAM;QACL4F,OAAO,EAAC,WAAW;QACnBG,KAAK,EAAC,SAAS;QACfW,SAAS,eAAElF,OAAA,CAACL,QAAQ;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBwE,OAAO,EAAEhC,iBAAkB;QAAApD,QAAA,EAC5B;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENf,OAAA,CAACzB,KAAK;MAACmC,EAAE,EAAE;QAAEyE,EAAE,EAAE,CAAC;QAAExE,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,eACzBH,OAAA,CAAC1B,UAAU;QAAC8F,OAAO,EAAC,IAAI;QAAAjE,QAAA,GAAC,YACb,EAACuB,YAAY,EAAC,QAAM,EAACF,UAAU,EAAC,GAC5C;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAEPiB,OAAO,gBACNhC,OAAA,CAAC1B,UAAU;MAAA6B,QAAA,EAAC;IAAmB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,GAC1CmB,KAAK,gBACPlC,OAAA,CAACjB,KAAK;MAACyD,QAAQ,EAAC,OAAO;MAAArC,QAAA,EAAE+B;IAAK;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,gBAEvCf,OAAA,CAAC3B,GAAG;MAACqC,EAAE,EAAE;QAAE+E,KAAK,EAAE;MAAO,CAAE;MAAAtF,QAAA,gBACzBH,OAAA,CAAC3B,GAAG;QAACqC,EAAE,EAAE;UAAEgF,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAU,CAAE;QAAAxF,QAAA,eACnDH,OAAA,CAACnB,IAAI;UACHuB,KAAK,EAAEkB,QAAS;UAChBsE,QAAQ,EAAEzC,eAAgB;UAC1B0C,cAAc,EAAC,SAAS;UACxBC,SAAS,EAAC,SAAS;UACnB1B,OAAO,EAAC,YAAY;UACpB2B,aAAa,EAAC,MAAM;UACpBC,wBAAwB;UAAA7F,QAAA,gBAExBH,OAAA,CAAClB,GAAG;YAACmH,KAAK,EAAC;UAAiB;YAAArF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/Bf,OAAA,CAAClB,GAAG;YAACmH,KAAK,EAAC;UAA0B;YAAArF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxCf,OAAA,CAAClB,GAAG;YAACmH,KAAK,EAAC;UAAY;YAAArF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1Bf,OAAA,CAAClB,GAAG;YAACmH,KAAK,EAAC;UAAgB;YAAArF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9Bf,OAAA,CAAClB,GAAG;YAACmH,KAAK,EAAC;UAAqB;YAAArF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnCf,OAAA,CAAClB,GAAG;YAACmH,KAAK,EAAC;UAAkB;YAAArF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEkB,QAAS;QAACjB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAAC3B,GAAG;UAACqC,EAAE,EAAE;YAAEyE,EAAE,EAAE;UAAE,CAAE;UAAAhF,QAAA,gBACjBH,OAAA,CAAC1B,UAAU;YAAC8F,OAAO,EAAC,IAAI;YAAC8B,YAAY;YAAA/F,QAAA,EAAC;UAEtC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ0C,eAAe,CAAC7B,UAAU,CAAC;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAENf,OAAA,CAAC3B,GAAG;UAACqC,EAAE,EAAE;YAAEyF,EAAE,EAAE;UAAE,CAAE;UAAAhG,QAAA,gBACjBH,OAAA,CAAC1B,UAAU;YAAC8F,OAAO,EAAC,IAAI;YAAC8B,YAAY;YAAA/F,QAAA,EAAC;UAEtC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ0C,eAAe,CAAC3B,SAAS,CAAC;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEkB,QAAS;QAACjB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,IAAI;UAAC8B,YAAY;UAAA/F,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,OAAO;UAAC8B,YAAY;UAAA/F,QAAA,EAAC;QAEzC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAACxB,MAAM;UACL4F,OAAO,EAAC,WAAW;UACnBG,KAAK,EAAC,SAAS;UACfW,SAAS,eAAElF,OAAA,CAACX,OAAO;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBL,EAAE,EAAE;YAAEyF,EAAE,EAAE;UAAE,CAAE;UAAAhG,QAAA,EACf;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEkB,QAAS;QAACjB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,IAAI;UAAC8B,YAAY;UAAA/F,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,OAAO;UAAC8B,YAAY;UAAA/F,QAAA,EAAC;QAEzC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEkB,QAAS;QAACjB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,IAAI;UAAC8B,YAAY;UAAA/F,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,OAAO;UAAC8B,YAAY;UAAA/F,QAAA,EAAC;QAEzC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEkB,QAAS;QAACjB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,IAAI;UAAC8B,YAAY;UAAA/F,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,OAAO;UAAC8B,YAAY;UAAA/F,QAAA,EAAC;QAEzC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEkB,QAAS;QAACjB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,IAAI;UAAC8B,YAAY;UAAA/F,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,OAAO;UAAC8B,YAAY;UAAA/F,QAAA,EAAC;QAEzC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEkB,QAAS;QAACjB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,IAAI;UAAC8B,YAAY;UAAA/F,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAAC1B,UAAU;UAAC8F,OAAO,EAAC,OAAO;UAAC8B,YAAY;UAAA/F,QAAA,EAAC;QAEzC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACN,eAGDf,OAAA,CAAChB,QAAQ;MACPsD,IAAI,EAAEF,YAAY,CAACE,IAAK;MACxB8D,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAE7C,uBAAwB;MACjC8C,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAArG,QAAA,eAE3DH,OAAA,CAACjB,KAAK;QAACsH,OAAO,EAAE7C,uBAAwB;QAAChB,QAAQ,EAAEJ,YAAY,CAACI,QAAS;QAAC9B,EAAE,EAAE;UAAE+E,KAAK,EAAE;QAAO,CAAE;QAAAtF,QAAA,EAC7FiC,YAAY,CAACG;MAAO;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACG,EAAA,CA3RID,QAAQ;EAAA,QACsBpB,OAAO,EACxBD,WAAW;AAAA;AAAA6G,GAAA,GAFxBxF,QAAQ;AA6Rd,eAAeA,QAAQ;AAAC,IAAAD,EAAA,EAAAyF,GAAA;AAAAC,YAAA,CAAA1F,EAAA;AAAA0F,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}