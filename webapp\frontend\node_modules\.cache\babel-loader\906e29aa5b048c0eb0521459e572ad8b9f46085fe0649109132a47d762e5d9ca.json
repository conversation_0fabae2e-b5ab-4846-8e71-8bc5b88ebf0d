{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,TextField,Button,Grid,FormControl,InputLabel,Select,MenuItem,Alert,CircularProgress,Typography,Paper,Dialog,DialogTitle,DialogContent,DialogActions,DialogContentText}from'@mui/material';import{Cancel as CancelIcon}from'@mui/icons-material';import{useNavigate}from'react-router-dom';import caviService from'../../services/caviService';import{redirectToVisualizzaCavi}from'../../utils/navigationUtils';import CavoForm from'./CavoForm';/**\n * Componente per la selezione e modifica di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.isDialog - Indica se il componente è in un dialog\n * @param {Function} props.onCancel - Funzione chiamata all'annullamento dell'operazione\n */import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const SelezionaCavoForm=_ref=>{let{cantiereId,onSuccess,onError,isDialog=false,onCancel}=_ref;const navigate=useNavigate();const[loading,setLoading]=useState(false);const[caviLoading,setCaviLoading]=useState(true);const[cavi,setCavi]=useState([]);const[selectedCavoId,setSelectedCavoId]=useState('');const[cavoSelectionStep,setCavoSelectionStep]=useState(true);const[redirectDialog,setRedirectDialog]=useState(false);const[selectedCavoData,setSelectedCavoData]=useState(null);// Carica la lista dei cavi all'avvio\nuseEffect(()=>{const loadCavi=async()=>{try{setCaviLoading(true);const caviData=await caviService.getCavi(cantiereId);// Filtra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\nconst caviNonPosati=caviData.filter(cavo=>parseFloat(cavo.metratura_reale)===0&&cavo.stato_installazione!=='Installato');setCavi(caviNonPosati);}catch(error){console.error('Errore nel caricamento dei cavi:',error);onError('Errore nel caricamento dei cavi');}finally{setCaviLoading(false);}};loadCavi();},[cantiereId,onError]);// Gestisce la selezione di un cavo\nconst handleCavoSelection=async()=>{if(!selectedCavoId){onError('Seleziona un cavo da modificare');return;}try{setLoading(true);const cavoData=await caviService.getCavoById(cantiereId,selectedCavoId);// Verifica se il cavo è già posato\nif(parseFloat(cavoData.metratura_reale)>0||cavoData.stato_installazione==='Installato'){// Mostra dialog per reindirizzare a modifica_bobina_cavo_posato\nsetRedirectDialog(true);return;}// Imposta i dati del cavo nel form\nsetSelectedCavoData(cavoData);setCavoSelectionStep(false);}catch(error){console.error('Errore nel caricamento dei dettagli del cavo:',error);onError('Errore nel caricamento dei dettagli del cavo');}finally{setLoading(false);}};// Gestisce l'annullamento dell'operazione\nconst handleCancel=()=>{if(isDialog){// Se è in un dialog, chiama la funzione onCancel passata come prop\nif(onCancel){onCancel();}return;}// Reindirizza alla visualizzazione dei cavi\nredirectToVisualizzaCavi(navigate);};// Gestisce il reindirizzamento a modifica_bobina_cavo_posato\nconst handleRedirect=()=>{setRedirectDialog(false);navigate(`/dashboard/cantieri/${cantiereId}/cavi/posa/modifica-bobina`);};// Gestisce il ritorno alla selezione del cavo\nconst handleBackToSelection=()=>{setCavoSelectionStep(true);setSelectedCavoData(null);};// Renderizza il form di selezione del cavo\nif(cavoSelectionStep){return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Paper,{sx:{p:isDialog?1.5:3,mb:isDialog?0.75:2,boxShadow:isDialog?0:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontSize:isDialog?'0.9rem':'1.25rem',mb:isDialog?1:1.5,mt:isDialog?0.5:1,fontWeight:'bold'},children:\"Seleziona un cavo da modificare\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",paragraph:true,children:\"Puoi modificare solo i cavi non ancora posati. Se il cavo \\xE8 gi\\xE0 posato, verrai reindirizzato alla funzione di modifica bobina.\"}),caviLoading?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',my:4},children:/*#__PURE__*/_jsx(CircularProgress,{})}):/*#__PURE__*/_jsx(_Fragment,{children:cavi.length===0?/*#__PURE__*/_jsx(Alert,{severity:\"info\",sx:{mt:2},children:\"Non ci sono cavi non posati disponibili per la modifica.\"}):/*#__PURE__*/_jsxs(Grid,{container:true,spacing:isDialog?1.5:3,sx:{mt:1},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,children:[/*#__PURE__*/_jsx(InputLabel,{id:\"cavo-select-label\",style:{marginBottom:'8px'},children:\"Seleziona Cavo\"}),/*#__PURE__*/_jsx(Select,{labelId:\"cavo-select-label\",id:\"cavo-select\",value:selectedCavoId,label:\"Seleziona Cavo\",onChange:e=>setSelectedCavoId(e.target.value),sx:{mb:1},children:cavi.map(cavo=>/*#__PURE__*/_jsxs(MenuItem,{value:cavo.id_cavo,children:[cavo.id_cavo,\" - \",cavo.sistema||'N/A',\" - \",cavo.utility||'N/A']},cavo.id_cavo))})]})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sx:{display:'flex',justifyContent:'space-between',mt:2},children:[/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"secondary\",onClick:handleCancel,startIcon:/*#__PURE__*/_jsx(CancelIcon,{}),children:\"Annulla\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",onClick:handleCavoSelection,disabled:!selectedCavoId||loading,children:loading?/*#__PURE__*/_jsx(CircularProgress,{size:24}):'Seleziona'})]})]})})]}),/*#__PURE__*/_jsxs(Dialog,{open:redirectDialog,onClose:()=>setRedirectDialog(false),children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Cavo gi\\xE0 posato\"}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsx(DialogContentText,{children:\"Il cavo selezionato \\xE8 gi\\xE0 stato posato. Verrai reindirizzato alla funzione di modifica bobina per cavi posati.\"})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setRedirectDialog(false),color:\"secondary\",children:\"Annulla\"}),/*#__PURE__*/_jsx(Button,{onClick:handleRedirect,color:\"primary\",autoFocus:true,children:\"Vai a Modifica Bobina\"})]})]})]});}// Renderizza il form di modifica del cavo utilizzando CavoForm\nreturn/*#__PURE__*/_jsx(Box,{children:selectedCavoData&&/*#__PURE__*/_jsx(CavoForm,{mode:\"edit\",initialData:selectedCavoData,cantiereId:cantiereId,onSubmit:async validatedData=>{try{// Rimuovi i campi di sistema che non devono essere modificati\nconst dataToSend={...validatedData};delete dataToSend.id_bobina;// Rimuovi id_bobina perché è un campo di sistema\ndelete dataToSend.metratura_reale;// Rimuovi metratura_reale perché è un campo di sistema\ndelete dataToSend.modificato_manualmente;// Rimuovi modificato_manualmente perché è un campo di sistema\ndelete dataToSend.timestamp;// Rimuovi timestamp perché è un campo di sistema\ndelete dataToSend.stato_installazione;// Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n// Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\ndataToSend.modificato_manualmente=1;await caviService.updateCavo(cantiereId,dataToSend.id_cavo,dataToSend);return true;}catch(error){throw error;}},onSuccess:message=>{onSuccess(message);if(isDialog){if(onCancel){onCancel();}}else{redirectToVisualizzaCavi(navigate);}},onError:onError,isDialog:isDialog,onCancel:isDialog?onCancel:handleBackToSelection})});};export default SelezionaCavoForm;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "Typography", "Paper", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "DialogContentText", "Cancel", "CancelIcon", "useNavigate", "caviService", "redirectToVisualizzaCavi", "CavoForm", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "SelezionaCavoForm", "_ref", "cantiereId", "onSuccess", "onError", "isDialog", "onCancel", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "cavi", "<PERSON><PERSON><PERSON>", "selectedCavoId", "setSelectedCavoId", "cavoSelectionStep", "setCavoSelectionStep", "redirectDialog", "setRedirectDialog", "selectedCavoData", "setSelectedCavoData", "loadCavi", "caviData", "get<PERSON><PERSON>", "cavi<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "cavo", "parseFloat", "metratura_reale", "stato_installazione", "error", "console", "handleCavoSelection", "cavoData", "getCavoById", "handleCancel", "handleRedirect", "handleBackToSelection", "children", "sx", "p", "mb", "boxShadow", "variant", "fontSize", "mt", "fontWeight", "color", "paragraph", "display", "justifyContent", "my", "length", "severity", "container", "spacing", "item", "xs", "fullWidth", "id", "style", "marginBottom", "labelId", "value", "label", "onChange", "e", "target", "map", "id_cavo", "sistema", "utility", "onClick", "startIcon", "disabled", "size", "open", "onClose", "autoFocus", "mode", "initialData", "onSubmit", "validatedData", "dataToSend", "id_bobina", "modificato_manualmente", "timestamp", "updateCavo", "message"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/SelezionaCavoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  Typography,\n  Paper,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  DialogContentText\n} from '@mui/material';\nimport { Cancel as CancelIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport CavoForm from './CavoForm';\n\n/**\n * Componente per la selezione e modifica di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.isDialog - Indica se il componente è in un dialog\n * @param {Function} props.onCancel - Funzione chiamata all'annullamento dell'operazione\n */\nconst SelezionaCavoForm = ({ cantiereId, onSuccess, onError, isDialog = false, onCancel }) => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(true);\n  const [cavi, setCavi] = useState([]);\n  const [selectedCavoId, setSelectedCavoId] = useState('');\n  const [cavoSelectionStep, setCavoSelectionStep] = useState(true);\n  const [redirectDialog, setRedirectDialog] = useState(false);\n  const [selectedCavoData, setSelectedCavoData] = useState(null);\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    const loadCavi = async () => {\n      try {\n        setCaviLoading(true);\n        const caviData = await caviService.getCavi(cantiereId);\n        // Filtra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\n        const caviNonPosati = caviData.filter(cavo =>\n          parseFloat(cavo.metratura_reale) === 0 &&\n          cavo.stato_installazione !== 'Installato'\n        );\n        setCavi(caviNonPosati);\n      } catch (error) {\n        console.error('Errore nel caricamento dei cavi:', error);\n        onError('Errore nel caricamento dei cavi');\n      } finally {\n        setCaviLoading(false);\n      }\n    };\n\n    loadCavi();\n  }, [cantiereId, onError]);\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelection = async () => {\n    if (!selectedCavoId) {\n      onError('Seleziona un cavo da modificare');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      const cavoData = await caviService.getCavoById(cantiereId, selectedCavoId);\n\n      // Verifica se il cavo è già posato\n      if (parseFloat(cavoData.metratura_reale) > 0 || cavoData.stato_installazione === 'Installato') {\n        // Mostra dialog per reindirizzare a modifica_bobina_cavo_posato\n        setRedirectDialog(true);\n        return;\n      }\n\n      // Imposta i dati del cavo nel form\n      setSelectedCavoData(cavoData);\n      setCavoSelectionStep(false);\n    } catch (error) {\n      console.error('Errore nel caricamento dei dettagli del cavo:', error);\n      onError('Errore nel caricamento dei dettagli del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, chiama la funzione onCancel passata come prop\n      if (onCancel) {\n        onCancel();\n      }\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n  // Gestisce il reindirizzamento a modifica_bobina_cavo_posato\n  const handleRedirect = () => {\n    setRedirectDialog(false);\n    navigate(`/dashboard/cantieri/${cantiereId}/cavi/posa/modifica-bobina`);\n  };\n\n  // Gestisce il ritorno alla selezione del cavo\n  const handleBackToSelection = () => {\n    setCavoSelectionStep(true);\n    setSelectedCavoData(null);\n  };\n\n  // Renderizza il form di selezione del cavo\n  if (cavoSelectionStep) {\n    return (\n      <Box>\n        <Paper sx={{ p: isDialog ? 1.5 : 3, mb: isDialog ? 0.75 : 2, boxShadow: isDialog ? 0 : 1 }}>\n          <Typography variant=\"h6\" sx={{ fontSize: isDialog ? '0.9rem' : '1.25rem', mb: isDialog ? 1 : 1.5, mt: isDialog ? 0.5 : 1, fontWeight: 'bold' }}>\n            Seleziona un cavo da modificare\n          </Typography>\n          <Typography variant=\"body2\" color=\"textSecondary\" paragraph>\n            Puoi modificare solo i cavi non ancora posati. Se il cavo è già posato, verrai reindirizzato alla funzione di modifica bobina.\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <>\n              {cavi.length === 0 ? (\n                <Alert severity=\"info\" sx={{ mt: 2 }}>\n                  Non ci sono cavi non posati disponibili per la modifica.\n                </Alert>\n              ) : (\n                <Grid container spacing={isDialog ? 1.5 : 3} sx={{ mt: 1 }}>\n                  <Grid item xs={12}>\n                    <FormControl fullWidth>\n                      <InputLabel id=\"cavo-select-label\" style={{ marginBottom: '8px' }}>Seleziona Cavo</InputLabel>\n                      <Select\n                        labelId=\"cavo-select-label\"\n                        id=\"cavo-select\"\n                        value={selectedCavoId}\n                        label=\"Seleziona Cavo\"\n                        onChange={(e) => setSelectedCavoId(e.target.value)}\n                        sx={{ mb: 1 }}\n                      >\n                        {cavi.map((cavo) => (\n                          <MenuItem key={cavo.id_cavo} value={cavo.id_cavo}>\n                            {cavo.id_cavo} - {cavo.sistema || 'N/A'} - {cavo.utility || 'N/A'}\n                          </MenuItem>\n                        ))}\n                      </Select>\n                    </FormControl>\n                  </Grid>\n                  <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>\n                    <Button\n                      variant=\"outlined\"\n                      color=\"secondary\"\n                      onClick={handleCancel}\n                      startIcon={<CancelIcon />}\n                    >\n                      Annulla\n                    </Button>\n                    <Button\n                      variant=\"contained\"\n                      color=\"primary\"\n                      onClick={handleCavoSelection}\n                      disabled={!selectedCavoId || loading}\n                    >\n                      {loading ? <CircularProgress size={24} /> : 'Seleziona'}\n                    </Button>\n                  </Grid>\n                </Grid>\n              )}\n            </>\n          )}\n        </Paper>\n\n        {/* Dialog per reindirizzamento */}\n        <Dialog\n          open={redirectDialog}\n          onClose={() => setRedirectDialog(false)}\n        >\n          <DialogTitle>Cavo già posato</DialogTitle>\n          <DialogContent>\n            <DialogContentText>\n              Il cavo selezionato è già stato posato. Verrai reindirizzato alla funzione di modifica bobina per cavi posati.\n            </DialogContentText>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={() => setRedirectDialog(false)} color=\"secondary\">\n              Annulla\n            </Button>\n            <Button onClick={handleRedirect} color=\"primary\" autoFocus>\n              Vai a Modifica Bobina\n            </Button>\n          </DialogActions>\n        </Dialog>\n      </Box>\n    );\n  }\n\n  // Renderizza il form di modifica del cavo utilizzando CavoForm\n  return (\n    <Box>\n      {selectedCavoData && (\n        <CavoForm\n          mode=\"edit\"\n          initialData={selectedCavoData}\n          cantiereId={cantiereId}\n          onSubmit={async (validatedData) => {\n            try {\n              // Rimuovi i campi di sistema che non devono essere modificati\n              const dataToSend = { ...validatedData };\n              delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n              delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n              delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n              delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n              delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n              // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n              dataToSend.modificato_manualmente = 1;\n\n              await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n              return true;\n            } catch (error) {\n              throw error;\n            }\n          }}\n          onSuccess={(message) => {\n            onSuccess(message);\n            if (isDialog) {\n              if (onCancel) {\n                onCancel();\n              }\n            } else {\n              redirectToVisualizzaCavi(navigate);\n            }\n          }}\n          onError={onError}\n          isDialog={isDialog}\n          onCancel={isDialog ? onCancel : handleBackToSelection}\n        />\n      )}\n    </Box>\n  );\n};\n\nexport default SelezionaCavoForm;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,SAAS,CACTC,MAAM,CACNC,IAAI,CACJC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,CACRC,KAAK,CACLC,gBAAgB,CAChBC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,iBAAiB,KACZ,eAAe,CACtB,OAASC,MAAM,GAAI,CAAAC,UAAU,KAAQ,qBAAqB,CAC1D,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CACpD,OAASC,wBAAwB,KAAQ,6BAA6B,CACtE,MAAO,CAAAC,QAAQ,KAAM,YAAY,CAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GATA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAUA,KAAM,CAAAC,iBAAiB,CAAGC,IAAA,EAAoE,IAAnE,CAAEC,UAAU,CAAEC,SAAS,CAAEC,OAAO,CAAEC,QAAQ,CAAG,KAAK,CAAEC,QAAS,CAAC,CAAAL,IAAA,CACvF,KAAM,CAAAM,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGxC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACyC,WAAW,CAAEC,cAAc,CAAC,CAAG1C,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAAC2C,IAAI,CAAEC,OAAO,CAAC,CAAG5C,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAC6C,cAAc,CAAEC,iBAAiB,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC+C,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGhD,QAAQ,CAAC,IAAI,CAAC,CAChE,KAAM,CAACiD,cAAc,CAAEC,iBAAiB,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACmD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpD,QAAQ,CAAC,IAAI,CAAC,CAE9D;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAoD,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAI,CACFX,cAAc,CAAC,IAAI,CAAC,CACpB,KAAM,CAAAY,QAAQ,CAAG,KAAM,CAAAhC,WAAW,CAACiC,OAAO,CAACtB,UAAU,CAAC,CACtD;AACA,KAAM,CAAAuB,aAAa,CAAGF,QAAQ,CAACG,MAAM,CAACC,IAAI,EACxCC,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,GAAK,CAAC,EACtCF,IAAI,CAACG,mBAAmB,GAAK,YAC/B,CAAC,CACDjB,OAAO,CAACY,aAAa,CAAC,CACxB,CAAE,MAAOM,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxD3B,OAAO,CAAC,iCAAiC,CAAC,CAC5C,CAAC,OAAS,CACRO,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAAC,CAEDW,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,CAACpB,UAAU,CAAEE,OAAO,CAAC,CAAC,CAEzB;AACA,KAAM,CAAA6B,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CAACnB,cAAc,CAAE,CACnBV,OAAO,CAAC,iCAAiC,CAAC,CAC1C,OACF,CAEA,GAAI,CACFK,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAyB,QAAQ,CAAG,KAAM,CAAA3C,WAAW,CAAC4C,WAAW,CAACjC,UAAU,CAAEY,cAAc,CAAC,CAE1E;AACA,GAAIc,UAAU,CAACM,QAAQ,CAACL,eAAe,CAAC,CAAG,CAAC,EAAIK,QAAQ,CAACJ,mBAAmB,GAAK,YAAY,CAAE,CAC7F;AACAX,iBAAiB,CAAC,IAAI,CAAC,CACvB,OACF,CAEA;AACAE,mBAAmB,CAACa,QAAQ,CAAC,CAC7BjB,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAE,MAAOc,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+CAA+C,CAAEA,KAAK,CAAC,CACrE3B,OAAO,CAAC,8CAA8C,CAAC,CACzD,CAAC,OAAS,CACRK,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA2B,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI/B,QAAQ,CAAE,CACZ;AACA,GAAIC,QAAQ,CAAE,CACZA,QAAQ,CAAC,CAAC,CACZ,CACA,OACF,CACA;AACAd,wBAAwB,CAACe,QAAQ,CAAC,CACpC,CAAC,CAED;AACA,KAAM,CAAA8B,cAAc,CAAGA,CAAA,GAAM,CAC3BlB,iBAAiB,CAAC,KAAK,CAAC,CACxBZ,QAAQ,CAAC,uBAAuBL,UAAU,4BAA4B,CAAC,CACzE,CAAC,CAED;AACA,KAAM,CAAAoC,qBAAqB,CAAGA,CAAA,GAAM,CAClCrB,oBAAoB,CAAC,IAAI,CAAC,CAC1BI,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED;AACA,GAAIL,iBAAiB,CAAE,CACrB,mBACEnB,KAAA,CAAC1B,GAAG,EAAAoE,QAAA,eACF1C,KAAA,CAACf,KAAK,EAAC0D,EAAE,CAAE,CAAEC,CAAC,CAAEpC,QAAQ,CAAG,GAAG,CAAG,CAAC,CAAEqC,EAAE,CAAErC,QAAQ,CAAG,IAAI,CAAG,CAAC,CAAEsC,SAAS,CAAEtC,QAAQ,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAkC,QAAA,eACzF5C,IAAA,CAACd,UAAU,EAAC+D,OAAO,CAAC,IAAI,CAACJ,EAAE,CAAE,CAAEK,QAAQ,CAAExC,QAAQ,CAAG,QAAQ,CAAG,SAAS,CAAEqC,EAAE,CAAErC,QAAQ,CAAG,CAAC,CAAG,GAAG,CAAEyC,EAAE,CAAEzC,QAAQ,CAAG,GAAG,CAAG,CAAC,CAAE0C,UAAU,CAAE,MAAO,CAAE,CAAAR,QAAA,CAAC,iCAEhJ,CAAY,CAAC,cACb5C,IAAA,CAACd,UAAU,EAAC+D,OAAO,CAAC,OAAO,CAACI,KAAK,CAAC,eAAe,CAACC,SAAS,MAAAV,QAAA,CAAC,sIAE5D,CAAY,CAAC,CAEZ7B,WAAW,cACVf,IAAA,CAACxB,GAAG,EAACqE,EAAE,CAAE,CAAEU,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,cAC5D5C,IAAA,CAACf,gBAAgB,GAAE,CAAC,CACjB,CAAC,cAENe,IAAA,CAAAI,SAAA,EAAAwC,QAAA,CACG3B,IAAI,CAACyC,MAAM,GAAK,CAAC,cAChB1D,IAAA,CAAChB,KAAK,EAAC2E,QAAQ,CAAC,MAAM,CAACd,EAAE,CAAE,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,CAAC,0DAEtC,CAAO,CAAC,cAER1C,KAAA,CAACvB,IAAI,EAACiF,SAAS,MAACC,OAAO,CAAEnD,QAAQ,CAAG,GAAG,CAAG,CAAE,CAACmC,EAAE,CAAE,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,eACzD5C,IAAA,CAACrB,IAAI,EAACmF,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAnB,QAAA,cAChB1C,KAAA,CAACtB,WAAW,EAACoF,SAAS,MAAApB,QAAA,eACpB5C,IAAA,CAACnB,UAAU,EAACoF,EAAE,CAAC,mBAAmB,CAACC,KAAK,CAAE,CAAEC,YAAY,CAAE,KAAM,CAAE,CAAAvB,QAAA,CAAC,gBAAc,CAAY,CAAC,cAC9F5C,IAAA,CAAClB,MAAM,EACLsF,OAAO,CAAC,mBAAmB,CAC3BH,EAAE,CAAC,aAAa,CAChBI,KAAK,CAAElD,cAAe,CACtBmD,KAAK,CAAC,gBAAgB,CACtBC,QAAQ,CAAGC,CAAC,EAAKpD,iBAAiB,CAACoD,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE,CACnDxB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,CAEb3B,IAAI,CAACyD,GAAG,CAAE1C,IAAI,eACb9B,KAAA,CAACnB,QAAQ,EAAoBsF,KAAK,CAAErC,IAAI,CAAC2C,OAAQ,CAAA/B,QAAA,EAC9CZ,IAAI,CAAC2C,OAAO,CAAC,KAAG,CAAC3C,IAAI,CAAC4C,OAAO,EAAI,KAAK,CAAC,KAAG,CAAC5C,IAAI,CAAC6C,OAAO,EAAI,KAAK,GADpD7C,IAAI,CAAC2C,OAEV,CACX,CAAC,CACI,CAAC,EACE,CAAC,CACV,CAAC,cACPzE,KAAA,CAACvB,IAAI,EAACmF,IAAI,MAACC,EAAE,CAAE,EAAG,CAAClB,EAAE,CAAE,CAAEU,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEL,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,eACjF5C,IAAA,CAACtB,MAAM,EACLuE,OAAO,CAAC,UAAU,CAClBI,KAAK,CAAC,WAAW,CACjByB,OAAO,CAAErC,YAAa,CACtBsC,SAAS,cAAE/E,IAAA,CAACN,UAAU,GAAE,CAAE,CAAAkD,QAAA,CAC3B,SAED,CAAQ,CAAC,cACT5C,IAAA,CAACtB,MAAM,EACLuE,OAAO,CAAC,WAAW,CACnBI,KAAK,CAAC,SAAS,CACfyB,OAAO,CAAExC,mBAAoB,CAC7B0C,QAAQ,CAAE,CAAC7D,cAAc,EAAIN,OAAQ,CAAA+B,QAAA,CAEpC/B,OAAO,cAAGb,IAAA,CAACf,gBAAgB,EAACgG,IAAI,CAAE,EAAG,CAAE,CAAC,CAAG,WAAW,CACjD,CAAC,EACL,CAAC,EACH,CACP,CACD,CACH,EACI,CAAC,cAGR/E,KAAA,CAACd,MAAM,EACL8F,IAAI,CAAE3D,cAAe,CACrB4D,OAAO,CAAEA,CAAA,GAAM3D,iBAAiB,CAAC,KAAK,CAAE,CAAAoB,QAAA,eAExC5C,IAAA,CAACX,WAAW,EAAAuD,QAAA,CAAC,oBAAe,CAAa,CAAC,cAC1C5C,IAAA,CAACV,aAAa,EAAAsD,QAAA,cACZ5C,IAAA,CAACR,iBAAiB,EAAAoD,QAAA,CAAC,sHAEnB,CAAmB,CAAC,CACP,CAAC,cAChB1C,KAAA,CAACX,aAAa,EAAAqD,QAAA,eACZ5C,IAAA,CAACtB,MAAM,EAACoG,OAAO,CAAEA,CAAA,GAAMtD,iBAAiB,CAAC,KAAK,CAAE,CAAC6B,KAAK,CAAC,WAAW,CAAAT,QAAA,CAAC,SAEnE,CAAQ,CAAC,cACT5C,IAAA,CAACtB,MAAM,EAACoG,OAAO,CAAEpC,cAAe,CAACW,KAAK,CAAC,SAAS,CAAC+B,SAAS,MAAAxC,QAAA,CAAC,uBAE3D,CAAQ,CAAC,EACI,CAAC,EACV,CAAC,EACN,CAAC,CAEV,CAEA;AACA,mBACE5C,IAAA,CAACxB,GAAG,EAAAoE,QAAA,CACDnB,gBAAgB,eACfzB,IAAA,CAACF,QAAQ,EACPuF,IAAI,CAAC,MAAM,CACXC,WAAW,CAAE7D,gBAAiB,CAC9BlB,UAAU,CAAEA,UAAW,CACvBgF,QAAQ,CAAE,KAAO,CAAAC,aAAa,EAAK,CACjC,GAAI,CACF;AACA,KAAM,CAAAC,UAAU,CAAG,CAAE,GAAGD,aAAc,CAAC,CACvC,MAAO,CAAAC,UAAU,CAACC,SAAS,CAAE;AAC7B,MAAO,CAAAD,UAAU,CAACvD,eAAe,CAAE;AACnC,MAAO,CAAAuD,UAAU,CAACE,sBAAsB,CAAE;AAC1C,MAAO,CAAAF,UAAU,CAACG,SAAS,CAAE;AAC7B,MAAO,CAAAH,UAAU,CAACtD,mBAAmB,CAAE;AAEvC;AACAsD,UAAU,CAACE,sBAAsB,CAAG,CAAC,CAErC,KAAM,CAAA/F,WAAW,CAACiG,UAAU,CAACtF,UAAU,CAAEkF,UAAU,CAACd,OAAO,CAAEc,UAAU,CAAC,CACxE,MAAO,KAAI,CACb,CAAE,MAAOrD,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CACb,CACF,CAAE,CACF5B,SAAS,CAAGsF,OAAO,EAAK,CACtBtF,SAAS,CAACsF,OAAO,CAAC,CAClB,GAAIpF,QAAQ,CAAE,CACZ,GAAIC,QAAQ,CAAE,CACZA,QAAQ,CAAC,CAAC,CACZ,CACF,CAAC,IAAM,CACLd,wBAAwB,CAACe,QAAQ,CAAC,CACpC,CACF,CAAE,CACFH,OAAO,CAAEA,OAAQ,CACjBC,QAAQ,CAAEA,QAAS,CACnBC,QAAQ,CAAED,QAAQ,CAAGC,QAAQ,CAAGgC,qBAAsB,CACvD,CACF,CACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}