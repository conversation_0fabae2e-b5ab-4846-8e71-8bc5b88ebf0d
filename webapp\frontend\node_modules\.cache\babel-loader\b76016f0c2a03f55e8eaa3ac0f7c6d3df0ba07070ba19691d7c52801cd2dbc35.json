{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\CaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, CardActions, Tabs, Tab, Alert, Snackbar, IconButton } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Home as HomeIcon, Refresh as RefreshIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport caviService from '../services/caviService';\n\n// Importa i componenti per le diverse sezioni\nimport PosaCaviCollegamenti from '../components/cavi/PosaCaviCollegamenti';\nimport ParcoCavi from '../components/cavi/ParcoCavi';\nimport GestioneExcel from '../components/cavi/GestioneExcel';\nimport ReportCavi from '../components/cavi/ReportCavi';\nimport CertificazioneCavi from '../components/cavi/CertificazioneCavi';\nimport GestioneComande from '../components/cavi/GestioneComande';\n\n// Componente per il pannello delle tab\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction TabPanel(props) {\n  const {\n    children,\n    value,\n    index,\n    ...other\n  } = props;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    role: \"tabpanel\",\n    hidden: value !== index,\n    id: `cavi-tabpanel-${index}`,\n    \"aria-labelledby\": `cavi-tab-${index}`,\n    ...other,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 27\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n}\n_c = TabPanel;\nconst CaviPage = () => {\n  _s();\n  const {\n    user,\n    isImpersonating\n  } = useAuth();\n  const navigate = useNavigate();\n  const [tabValue, setTabValue] = useState(0);\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n        setCantiereId(selectedCantiereId);\n        setCantiereName(selectedCantiereName || `Cantiere ${selectedCantiereId}`);\n        setLoading(true);\n        // Carica i cavi attivi\n        console.log('Caricamento cavi attivi per cantiere:', selectedCantiereId);\n        const attivi = await caviService.getCavi(selectedCantiereId, 0);\n        console.log('Cavi attivi caricati:', attivi);\n        setCaviAttivi(attivi);\n\n        // Carica i cavi spare\n        console.log('Caricamento cavi spare per cantiere:', selectedCantiereId);\n        const spare = await caviService.getCavi(selectedCantiereId, 3);\n        console.log('Cavi spare caricati:', spare);\n        setCaviSpare(spare);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        setError('Impossibile caricare i cavi. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n\n  // Gestione del cambio di tab\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n  };\n\n  // Torna alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Torna al menu amministratore (solo per admin che impersonano un utente)\n  const handleBackToAdmin = () => {\n    // Naviga direttamente al menu amministratore\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = cavi => {\n    if (cavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Nessun cavo trovato in questa categoria.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"div\",\n              children: cavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Sistema: \", cavo.sistema || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Tipologia: \", cavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Partenza: \", cavo.ubicazione_partenza || 'N/A', \" - \", cavo.utenza_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Arrivo: \", cavo.ubicazione_arrivo || 'N/A', \" - \", cavo.utenza_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Metratura reale: \", cavo.metratura_reale || '0']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Stato: \", cavo.stato_installazione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 49\n              }, this),\n              children: \"Modifica\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: \"error\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 63\n              }, this),\n              children: \"Elimina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this)\n      }, cavo.id_cavo, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToCantieri,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          children: \"Gestione Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), isImpersonating && /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 24\n        }, this),\n        onClick: handleBackToAdmin,\n        children: \"Torna al Menu Admin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: [\"Cantiere: \", cantiereName, \" (ID: \", cantiereId, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Caricamento cavi...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 24\n        }, this),\n        onClick: handleBackToCantieri,\n        children: \"Torna ai Cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: tabValue,\n          onChange: handleTabChange,\n          indicatorColor: \"primary\",\n          textColor: \"primary\",\n          variant: \"scrollable\",\n          scrollButtons: \"auto\",\n          allowScrollButtonsMobile: true,\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Visualizza Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Posa Cavi e Collegamenti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Parco Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Gestione Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Certificazione Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: \"Gestione Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 0,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            children: \"Cavi Attivi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), renderCaviTable(caviAttivi)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            children: \"Cavi Spare\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this), renderCaviTable(caviSpare)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 1,\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            setNotification({\n              open: true,\n              message,\n              severity: 'success'\n            });\n          },\n          onError: message => {\n            setNotification({\n              open: true,\n              message,\n              severity: 'error'\n            });\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 2,\n        children: /*#__PURE__*/_jsxDEV(ParcoCavi, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            setNotification({\n              open: true,\n              message,\n              severity: 'success'\n            });\n          },\n          onError: message => {\n            setNotification({\n              open: true,\n              message,\n              severity: 'error'\n            });\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 3,\n        children: /*#__PURE__*/_jsxDEV(GestioneExcel, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            setNotification({\n              open: true,\n              message,\n              severity: 'success'\n            });\n          },\n          onError: message => {\n            setNotification({\n              open: true,\n              message,\n              severity: 'error'\n            });\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 4,\n        children: /*#__PURE__*/_jsxDEV(ReportCavi, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            setNotification({\n              open: true,\n              message,\n              severity: 'success'\n            });\n          },\n          onError: message => {\n            setNotification({\n              open: true,\n              message,\n              severity: 'error'\n            });\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 5,\n        children: /*#__PURE__*/_jsxDEV(CertificazioneCavi, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            setNotification({\n              open: true,\n              message,\n              severity: 'success'\n            });\n          },\n          onError: message => {\n            setNotification({\n              open: true,\n              message,\n              severity: 'error'\n            });\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n        value: tabValue,\n        index: 6,\n        children: /*#__PURE__*/_jsxDEV(GestioneComande, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            setNotification({\n              open: true,\n              message,\n              severity: 'success'\n            });\n          },\n          onError: message => {\n            setNotification({\n              open: true,\n              message,\n              severity: 'error'\n            });\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: notification.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseNotification,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseNotification,\n        severity: notification.severity,\n        sx: {\n          width: '100%'\n        },\n        children: notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 191,\n    columnNumber: 5\n  }, this);\n};\n_s(CaviPage, \"ChGZgydcMIVCPaSyRuVIqEX804c=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c2 = CaviPage;\nexport default CaviPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"TabPanel\");\n$RefreshReg$(_c2, \"CaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Tabs", "Tab", "<PERSON><PERSON>", "Snackbar", "IconButton", "ArrowBack", "ArrowBackIcon", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Home", "HomeIcon", "Refresh", "RefreshIcon", "useNavigate", "useAuth", "caviService", "PosaCaviCollegamenti", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "GestioneExcel", "ReportCavi", "CertificazioneCavi", "GestioneComande", "jsxDEV", "_jsxDEV", "TabPanel", "props", "children", "value", "index", "other", "role", "hidden", "id", "sx", "p", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "CaviPage", "_s", "user", "isImpersonating", "navigate", "tabValue", "setTabValue", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "notification", "setNotification", "open", "message", "severity", "fetchData", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "console", "log", "attivi", "get<PERSON><PERSON>", "spare", "err", "handleTabChange", "event", "newValue", "handleBackToCantieri", "handleBackToAdmin", "handleCloseNotification", "renderCaviTable", "cavi", "length", "container", "spacing", "map", "cavo", "item", "xs", "sm", "md", "variant", "component", "id_cavo", "color", "sistema", "tipologia", "ubicazione_partenza", "utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "stato_installazione", "size", "startIcon", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "window", "location", "reload", "ml", "title", "width", "borderBottom", "borderColor", "onChange", "indicatorColor", "textColor", "scrollButtons", "allowScrollButtonsMobile", "label", "gutterBottom", "mt", "onSuccess", "onError", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/CaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Tabs,\n  Tab,\n  Alert,\n  Snackbar,\n  IconButton\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Home as HomeIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport caviService from '../services/caviService';\n\n// Importa i componenti per le diverse sezioni\nimport PosaCaviCollegamenti from '../components/cavi/PosaCaviCollegamenti';\nimport ParcoCavi from '../components/cavi/ParcoCavi';\nimport GestioneExcel from '../components/cavi/GestioneExcel';\nimport ReportCavi from '../components/cavi/ReportCavi';\nimport CertificazioneCavi from '../components/cavi/CertificazioneCavi';\nimport GestioneComande from '../components/cavi/GestioneComande';\n\n// Componente per il pannello delle tab\nfunction TabPanel(props) {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`cavi-tabpanel-${index}`}\n      aria-labelledby={`cavi-tab-${index}`}\n      {...other}\n    >\n      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}\n    </div>\n  );\n}\n\nconst CaviPage = () => {\n  const { user, isImpersonating } = useAuth();\n  const navigate = useNavigate();\n  const [tabValue, setTabValue] = useState(0);\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato:', { selectedCantiereId, selectedCantiereName });\n\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        setCantiereId(selectedCantiereId);\n        setCantiereName(selectedCantiereName || `Cantiere ${selectedCantiereId}`);\n\n        setLoading(true);\n        // Carica i cavi attivi\n        console.log('Caricamento cavi attivi per cantiere:', selectedCantiereId);\n        const attivi = await caviService.getCavi(selectedCantiereId, 0);\n        console.log('Cavi attivi caricati:', attivi);\n        setCaviAttivi(attivi);\n\n        // Carica i cavi spare\n        console.log('Caricamento cavi spare per cantiere:', selectedCantiereId);\n        const spare = await caviService.getCavi(selectedCantiereId, 3);\n        console.log('Cavi spare caricati:', spare);\n        setCaviSpare(spare);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        setError('Impossibile caricare i cavi. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  // Gestione del cambio di tab\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n  };\n\n  // Torna alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Torna al menu amministratore (solo per admin che impersonano un utente)\n  const handleBackToAdmin = () => {\n    // Naviga direttamente al menu amministratore\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = (cavi) => {\n    if (cavi.length === 0) {\n      return (\n        <Alert severity=\"info\">Nessun cavo trovato in questa categoria.</Alert>\n      );\n    }\n\n    return (\n      <Grid container spacing={2}>\n        {cavi.map((cavo) => (\n          <Grid item xs={12} sm={6} md={4} key={cavo.id_cavo}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" component=\"div\">\n                  {cavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Sistema: {cavo.sistema || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Tipologia: {cavo.tipologia || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Partenza: {cavo.ubicazione_partenza || 'N/A'} - {cavo.utenza_partenza || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Arrivo: {cavo.ubicazione_arrivo || 'N/A'} - {cavo.utenza_arrivo || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Metri teorici: {cavo.metri_teorici || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Metratura reale: {cavo.metratura_reale || '0'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Stato: {cavo.stato_installazione || 'N/A'}\n                </Typography>\n              </CardContent>\n              <CardActions>\n                <Button size=\"small\" startIcon={<EditIcon />}>\n                  Modifica\n                </Button>\n                <Button size=\"small\" color=\"error\" startIcon={<DeleteIcon />}>\n                  Elimina\n                </Button>\n              </CardActions>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n    );\n  };\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Gestione Cavi\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        {isImpersonating && (\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<HomeIcon />}\n            onClick={handleBackToAdmin}\n          >\n            Torna al Menu Admin\n          </Button>\n        )}\n      </Box>\n\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Typography variant=\"h6\">\n          Cantiere: {cantiereName} (ID: {cantiereId})\n        </Typography>\n      </Paper>\n\n      {loading ? (\n        <Typography>Caricamento cavi...</Typography>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>\n          <Button\n            variant=\"contained\"\n            startIcon={<ArrowBackIcon />}\n            onClick={handleBackToCantieri}\n          >\n            Torna ai Cantieri\n          </Button>\n        </Box>\n      ) : (\n        <Box sx={{ width: '100%' }}>\n          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\n            <Tabs\n              value={tabValue}\n              onChange={handleTabChange}\n              indicatorColor=\"primary\"\n              textColor=\"primary\"\n              variant=\"scrollable\"\n              scrollButtons=\"auto\"\n              allowScrollButtonsMobile\n            >\n              <Tab label=\"Visualizza Cavi\" />\n              <Tab label=\"Posa Cavi e Collegamenti\" />\n              <Tab label=\"Parco Cavi\" />\n              <Tab label=\"Gestione Excel\" />\n              <Tab label=\"Report\" />\n              <Tab label=\"Certificazione Cavi\" />\n              <Tab label=\"Gestione Comande\" />\n            </Tabs>\n          </Box>\n\n          {/* Tab Visualizza Cavi */}\n          <TabPanel value={tabValue} index={0}>\n            <Box sx={{ mb: 3 }}>\n              <Typography variant=\"h5\" gutterBottom>\n                Cavi Attivi\n              </Typography>\n              {renderCaviTable(caviAttivi)}\n            </Box>\n\n            <Box sx={{ mt: 4 }}>\n              <Typography variant=\"h5\" gutterBottom>\n                Cavi Spare\n              </Typography>\n              {renderCaviTable(caviSpare)}\n            </Box>\n          </TabPanel>\n\n          {/* Tab Posa Cavi e Collegamenti */}\n          <TabPanel value={tabValue} index={1}>\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                setNotification({\n                  open: true,\n                  message,\n                  severity: 'success'\n                });\n              }}\n              onError={(message) => {\n                setNotification({\n                  open: true,\n                  message,\n                  severity: 'error'\n                });\n              }}\n            />\n          </TabPanel>\n\n          {/* Tab Parco Cavi */}\n          <TabPanel value={tabValue} index={2}>\n            <ParcoCavi\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                setNotification({\n                  open: true,\n                  message,\n                  severity: 'success'\n                });\n              }}\n              onError={(message) => {\n                setNotification({\n                  open: true,\n                  message,\n                  severity: 'error'\n                });\n              }}\n            />\n          </TabPanel>\n\n          {/* Tab Gestione Excel */}\n          <TabPanel value={tabValue} index={3}>\n            <GestioneExcel\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                setNotification({\n                  open: true,\n                  message,\n                  severity: 'success'\n                });\n              }}\n              onError={(message) => {\n                setNotification({\n                  open: true,\n                  message,\n                  severity: 'error'\n                });\n              }}\n            />\n          </TabPanel>\n\n          {/* Tab Report */}\n          <TabPanel value={tabValue} index={4}>\n            <ReportCavi\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                setNotification({\n                  open: true,\n                  message,\n                  severity: 'success'\n                });\n              }}\n              onError={(message) => {\n                setNotification({\n                  open: true,\n                  message,\n                  severity: 'error'\n                });\n              }}\n            />\n          </TabPanel>\n\n          {/* Tab Certificazione Cavi */}\n          <TabPanel value={tabValue} index={5}>\n            <CertificazioneCavi\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                setNotification({\n                  open: true,\n                  message,\n                  severity: 'success'\n                });\n              }}\n              onError={(message) => {\n                setNotification({\n                  open: true,\n                  message,\n                  severity: 'error'\n                });\n              }}\n            />\n          </TabPanel>\n\n          {/* Tab Gestione Comande */}\n          <TabPanel value={tabValue} index={6}>\n            <GestioneComande\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                setNotification({\n                  open: true,\n                  message,\n                  severity: 'success'\n                });\n              }}\n              onError={(message) => {\n                setNotification({\n                  open: true,\n                  message,\n                  severity: 'error'\n                });\n              }}\n            />\n          </TabPanel>\n        </Box>\n      )}\n\n      {/* Notifica */}\n      <Snackbar\n        open={notification.open}\n        autoHideDuration={6000}\n        onClose={handleCloseNotification}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n          {notification.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default CaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,IAAI,EACJC,GAAG,EACHC,KAAK,EACLC,QAAQ,EACRC,UAAU,QACL,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,WAAW,MAAM,yBAAyB;;AAEjD;AACA,OAAOC,oBAAoB,MAAM,yCAAyC;AAC1E,OAAOC,SAAS,MAAM,8BAA8B;AACpD,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,UAAU,MAAM,+BAA+B;AACtD,OAAOC,kBAAkB,MAAM,uCAAuC;AACtE,OAAOC,eAAe,MAAM,oCAAoC;;AAEhE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,MAAM;IAAEC,QAAQ;IAAEC,KAAK;IAAEC,KAAK;IAAE,GAAGC;EAAM,CAAC,GAAGJ,KAAK;EAElD,oBACEF,OAAA;IACEO,IAAI,EAAC,UAAU;IACfC,MAAM,EAAEJ,KAAK,KAAKC,KAAM;IACxBI,EAAE,EAAE,iBAAiBJ,KAAK,EAAG;IAC7B,mBAAiB,YAAYA,KAAK,EAAG;IAAA,GACjCC,KAAK;IAAAH,QAAA,EAERC,KAAK,KAAKC,KAAK,iBAAIL,OAAA,CAACnC,GAAG;MAAC6C,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAEA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpD,CAAC;AAEV;AAACC,EAAA,GAdQf,QAAQ;AAgBjB,MAAMgB,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAG7B,OAAO,CAAC,CAAC;EAC3C,MAAM8B,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAG5D,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmE,SAAS,EAAEC,YAAY,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqE,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuE,KAAK,EAAEC,QAAQ,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC;IAC/C2E,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA5E,SAAS,CAAC,MAAM;IACd,MAAM6E,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF;QACA,MAAMC,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEzEE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;UAAEL,kBAAkB;UAAEG;QAAqB,CAAC,CAAC;QAElF,IAAI,CAACH,kBAAkB,EAAE;UACvBP,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEAR,aAAa,CAACiB,kBAAkB,CAAC;QACjCf,eAAe,CAACkB,oBAAoB,IAAI,YAAYH,kBAAkB,EAAE,CAAC;QAEzET,UAAU,CAAC,IAAI,CAAC;QAChB;QACAa,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEL,kBAAkB,CAAC;QACxE,MAAMM,MAAM,GAAG,MAAMxD,WAAW,CAACyD,OAAO,CAACP,kBAAkB,EAAE,CAAC,CAAC;QAC/DI,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,MAAM,CAAC;QAC5CnB,aAAa,CAACmB,MAAM,CAAC;;QAErB;QACAF,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEL,kBAAkB,CAAC;QACvE,MAAMQ,KAAK,GAAG,MAAM1D,WAAW,CAACyD,OAAO,CAACP,kBAAkB,EAAE,CAAC,CAAC;QAC9DI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEG,KAAK,CAAC;QAC1CnB,YAAY,CAACmB,KAAK,CAAC;MACrB,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZL,OAAO,CAACZ,KAAK,CAAC,kCAAkC,EAAEiB,GAAG,CAAC;QACtDhB,QAAQ,CAAC,iDAAiD,CAAC;MAC7D,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDQ,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3C/B,WAAW,CAAC+B,QAAQ,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjClC,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAED;EACA,MAAMmC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACAnC,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMoC,uBAAuB,GAAGA,CAAA,KAAM;IACpCpB,eAAe,CAAC;MACd,GAAGD,YAAY;MACfE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMoB,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;MACrB,oBACE5D,OAAA,CAACzB,KAAK;QAACiE,QAAQ,EAAC,MAAM;QAAArC,QAAA,EAAC;MAAwC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAE3E;IAEA,oBACEf,OAAA,CAAC/B,IAAI;MAAC4F,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA3D,QAAA,EACxBwD,IAAI,CAACI,GAAG,CAAEC,IAAI,iBACbhE,OAAA,CAAC/B,IAAI;QAACgG,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAjE,QAAA,eAC9BH,OAAA,CAAC9B,IAAI;UAAAiC,QAAA,gBACHH,OAAA,CAAC7B,WAAW;YAAAgC,QAAA,gBACVH,OAAA,CAAClC,UAAU;cAACuG,OAAO,EAAC,IAAI;cAACC,SAAS,EAAC,KAAK;cAAAnE,QAAA,EACrC6D,IAAI,CAACO;YAAO;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACbf,OAAA,CAAClC,UAAU;cAACuG,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAArE,QAAA,GAAC,WACxC,EAAC6D,IAAI,CAACS,OAAO,IAAI,KAAK;YAAA;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACbf,OAAA,CAAClC,UAAU;cAACuG,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAArE,QAAA,GAAC,aACtC,EAAC6D,IAAI,CAACU,SAAS,IAAI,KAAK;YAAA;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACbf,OAAA,CAAClC,UAAU;cAACuG,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAArE,QAAA,GAAC,YACvC,EAAC6D,IAAI,CAACW,mBAAmB,IAAI,KAAK,EAAC,KAAG,EAACX,IAAI,CAACY,eAAe,IAAI,KAAK;YAAA;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eACbf,OAAA,CAAClC,UAAU;cAACuG,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAArE,QAAA,GAAC,UACzC,EAAC6D,IAAI,CAACa,iBAAiB,IAAI,KAAK,EAAC,KAAG,EAACb,IAAI,CAACc,aAAa,IAAI,KAAK;YAAA;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eACbf,OAAA,CAAClC,UAAU;cAACuG,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAArE,QAAA,GAAC,iBAClC,EAAC6D,IAAI,CAACe,aAAa,IAAI,KAAK;YAAA;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACbf,OAAA,CAAClC,UAAU;cAACuG,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAArE,QAAA,GAAC,mBAChC,EAAC6D,IAAI,CAACgB,eAAe,IAAI,GAAG;YAAA;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACbf,OAAA,CAAClC,UAAU;cAACuG,OAAO,EAAC,OAAO;cAACG,KAAK,EAAC,gBAAgB;cAAArE,QAAA,GAAC,SAC1C,EAAC6D,IAAI,CAACiB,mBAAmB,IAAI,KAAK;YAAA;cAAArE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdf,OAAA,CAAC5B,WAAW;YAAA+B,QAAA,gBACVH,OAAA,CAAChC,MAAM;cAACkH,IAAI,EAAC,OAAO;cAACC,SAAS,eAAEnF,OAAA,CAACjB,QAAQ;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAZ,QAAA,EAAC;YAE9C;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTf,OAAA,CAAChC,MAAM;cAACkH,IAAI,EAAC,OAAO;cAACV,KAAK,EAAC,OAAO;cAACW,SAAS,eAAEnF,OAAA,CAACf,UAAU;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAZ,QAAA,EAAC;YAE9D;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GApC6BiD,IAAI,CAACO,OAAO;QAAA3D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqC5C,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX,CAAC;EAED,oBACEf,OAAA,CAACnC,GAAG;IAAAsC,QAAA,gBACFH,OAAA,CAACnC,GAAG;MAAC6C,EAAE,EAAE;QAAE0E,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAApF,QAAA,gBACzFH,OAAA,CAACnC,GAAG;QAAC6C,EAAE,EAAE;UAAE2E,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAnF,QAAA,gBACjDH,OAAA,CAACvB,UAAU;UAAC+G,OAAO,EAAEjC,oBAAqB;UAAC7C,EAAE,EAAE;YAAE+E,EAAE,EAAE;UAAE,CAAE;UAAAtF,QAAA,eACvDH,OAAA,CAACrB,aAAa;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACbf,OAAA,CAAClC,UAAU;UAACuG,OAAO,EAAC,IAAI;UAAAlE,QAAA,EAAC;QAEzB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAACvB,UAAU;UACT+G,OAAO,EAAEA,CAAA,KAAME,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxClF,EAAE,EAAE;YAAEmF,EAAE,EAAE;UAAE,CAAE;UACdrB,KAAK,EAAC,SAAS;UACfsB,KAAK,EAAC,oBAAoB;UAAA3F,QAAA,eAE1BH,OAAA,CAACX,WAAW;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EACLK,eAAe,iBACdpB,OAAA,CAAChC,MAAM;QACLqG,OAAO,EAAC,WAAW;QACnBG,KAAK,EAAC,SAAS;QACfW,SAAS,eAAEnF,OAAA,CAACb,QAAQ;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxByE,OAAO,EAAEhC,iBAAkB;QAAArD,QAAA,EAC5B;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENf,OAAA,CAACjC,KAAK;MAAC2C,EAAE,EAAE;QAAE0E,EAAE,EAAE,CAAC;QAAEzE,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,eACzBH,OAAA,CAAClC,UAAU;QAACuG,OAAO,EAAC,IAAI;QAAAlE,QAAA,GAAC,YACb,EAACuB,YAAY,EAAC,QAAM,EAACF,UAAU,EAAC,GAC5C;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAEPiB,OAAO,gBACNhC,OAAA,CAAClC,UAAU;MAAAqC,QAAA,EAAC;IAAmB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,GAC1CmB,KAAK,gBACPlC,OAAA,CAACnC,GAAG;MAAAsC,QAAA,gBACFH,OAAA,CAACzB,KAAK;QAACiE,QAAQ,EAAC,OAAO;QAAC9B,EAAE,EAAE;UAAE0E,EAAE,EAAE;QAAE,CAAE;QAAAjF,QAAA,EAAE+B;MAAK;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACtDf,OAAA,CAAChC,MAAM;QACLqG,OAAO,EAAC,WAAW;QACnBc,SAAS,eAAEnF,OAAA,CAACrB,aAAa;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7ByE,OAAO,EAAEjC,oBAAqB;QAAApD,QAAA,EAC/B;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,gBAENf,OAAA,CAACnC,GAAG;MAAC6C,EAAE,EAAE;QAAEqF,KAAK,EAAE;MAAO,CAAE;MAAA5F,QAAA,gBACzBH,OAAA,CAACnC,GAAG;QAAC6C,EAAE,EAAE;UAAEsF,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAU,CAAE;QAAA9F,QAAA,eACnDH,OAAA,CAAC3B,IAAI;UACH+B,KAAK,EAAEkB,QAAS;UAChB4E,QAAQ,EAAE9C,eAAgB;UAC1B+C,cAAc,EAAC,SAAS;UACxBC,SAAS,EAAC,SAAS;UACnB/B,OAAO,EAAC,YAAY;UACpBgC,aAAa,EAAC,MAAM;UACpBC,wBAAwB;UAAAnG,QAAA,gBAExBH,OAAA,CAAC1B,GAAG;YAACiI,KAAK,EAAC;UAAiB;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/Bf,OAAA,CAAC1B,GAAG;YAACiI,KAAK,EAAC;UAA0B;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxCf,OAAA,CAAC1B,GAAG;YAACiI,KAAK,EAAC;UAAY;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1Bf,OAAA,CAAC1B,GAAG;YAACiI,KAAK,EAAC;UAAgB;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9Bf,OAAA,CAAC1B,GAAG;YAACiI,KAAK,EAAC;UAAQ;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtBf,OAAA,CAAC1B,GAAG;YAACiI,KAAK,EAAC;UAAqB;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnCf,OAAA,CAAC1B,GAAG;YAACiI,KAAK,EAAC;UAAkB;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEkB,QAAS;QAACjB,KAAK,EAAE,CAAE;QAAAF,QAAA,gBAClCH,OAAA,CAACnC,GAAG;UAAC6C,EAAE,EAAE;YAAE0E,EAAE,EAAE;UAAE,CAAE;UAAAjF,QAAA,gBACjBH,OAAA,CAAClC,UAAU;YAACuG,OAAO,EAAC,IAAI;YAACmC,YAAY;YAAArG,QAAA,EAAC;UAEtC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ2C,eAAe,CAAC9B,UAAU,CAAC;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAENf,OAAA,CAACnC,GAAG;UAAC6C,EAAE,EAAE;YAAE+F,EAAE,EAAE;UAAE,CAAE;UAAAtG,QAAA,gBACjBH,OAAA,CAAClC,UAAU;YAACuG,OAAO,EAAC,IAAI;YAACmC,YAAY;YAAArG,QAAA,EAAC;UAEtC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ2C,eAAe,CAAC5B,SAAS,CAAC;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEkB,QAAS;QAACjB,KAAK,EAAE,CAAE;QAAAF,QAAA,eAClCH,OAAA,CAACP,oBAAoB;UACnB+B,UAAU,EAAEA,UAAW;UACvBkF,SAAS,EAAGnE,OAAO,IAAK;YACtBF,eAAe,CAAC;cACdC,IAAI,EAAE,IAAI;cACVC,OAAO;cACPC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ,CAAE;UACFmE,OAAO,EAAGpE,OAAO,IAAK;YACpBF,eAAe,CAAC;cACdC,IAAI,EAAE,IAAI;cACVC,OAAO;cACPC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QAAE;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEkB,QAAS;QAACjB,KAAK,EAAE,CAAE;QAAAF,QAAA,eAClCH,OAAA,CAACN,SAAS;UACR8B,UAAU,EAAEA,UAAW;UACvBkF,SAAS,EAAGnE,OAAO,IAAK;YACtBF,eAAe,CAAC;cACdC,IAAI,EAAE,IAAI;cACVC,OAAO;cACPC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ,CAAE;UACFmE,OAAO,EAAGpE,OAAO,IAAK;YACpBF,eAAe,CAAC;cACdC,IAAI,EAAE,IAAI;cACVC,OAAO;cACPC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QAAE;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEkB,QAAS;QAACjB,KAAK,EAAE,CAAE;QAAAF,QAAA,eAClCH,OAAA,CAACL,aAAa;UACZ6B,UAAU,EAAEA,UAAW;UACvBkF,SAAS,EAAGnE,OAAO,IAAK;YACtBF,eAAe,CAAC;cACdC,IAAI,EAAE,IAAI;cACVC,OAAO;cACPC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ,CAAE;UACFmE,OAAO,EAAGpE,OAAO,IAAK;YACpBF,eAAe,CAAC;cACdC,IAAI,EAAE,IAAI;cACVC,OAAO;cACPC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QAAE;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEkB,QAAS;QAACjB,KAAK,EAAE,CAAE;QAAAF,QAAA,eAClCH,OAAA,CAACJ,UAAU;UACT4B,UAAU,EAAEA,UAAW;UACvBkF,SAAS,EAAGnE,OAAO,IAAK;YACtBF,eAAe,CAAC;cACdC,IAAI,EAAE,IAAI;cACVC,OAAO;cACPC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ,CAAE;UACFmE,OAAO,EAAGpE,OAAO,IAAK;YACpBF,eAAe,CAAC;cACdC,IAAI,EAAE,IAAI;cACVC,OAAO;cACPC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QAAE;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEkB,QAAS;QAACjB,KAAK,EAAE,CAAE;QAAAF,QAAA,eAClCH,OAAA,CAACH,kBAAkB;UACjB2B,UAAU,EAAEA,UAAW;UACvBkF,SAAS,EAAGnE,OAAO,IAAK;YACtBF,eAAe,CAAC;cACdC,IAAI,EAAE,IAAI;cACVC,OAAO;cACPC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ,CAAE;UACFmE,OAAO,EAAGpE,OAAO,IAAK;YACpBF,eAAe,CAAC;cACdC,IAAI,EAAE,IAAI;cACVC,OAAO;cACPC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QAAE;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGXf,OAAA,CAACC,QAAQ;QAACG,KAAK,EAAEkB,QAAS;QAACjB,KAAK,EAAE,CAAE;QAAAF,QAAA,eAClCH,OAAA,CAACF,eAAe;UACd0B,UAAU,EAAEA,UAAW;UACvBkF,SAAS,EAAGnE,OAAO,IAAK;YACtBF,eAAe,CAAC;cACdC,IAAI,EAAE,IAAI;cACVC,OAAO;cACPC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ,CAAE;UACFmE,OAAO,EAAGpE,OAAO,IAAK;YACpBF,eAAe,CAAC;cACdC,IAAI,EAAE,IAAI;cACVC,OAAO;cACPC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QAAE;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACN,eAGDf,OAAA,CAACxB,QAAQ;MACP8D,IAAI,EAAEF,YAAY,CAACE,IAAK;MACxBsE,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEpD,uBAAwB;MACjCqD,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA7G,QAAA,eAE3DH,OAAA,CAACzB,KAAK;QAACsI,OAAO,EAAEpD,uBAAwB;QAACjB,QAAQ,EAAEJ,YAAY,CAACI,QAAS;QAAC9B,EAAE,EAAE;UAAEqF,KAAK,EAAE;QAAO,CAAE;QAAA5F,QAAA,EAC7FiC,YAAY,CAACG;MAAO;QAAA3B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACG,EAAA,CA9WID,QAAQ;EAAA,QACsB1B,OAAO,EACxBD,WAAW;AAAA;AAAA2H,GAAA,GAFxBhG,QAAQ;AAgXd,eAAeA,QAAQ;AAAC,IAAAD,EAAA,EAAAiG,GAAA;AAAAC,YAAA,CAAAlG,EAAA;AAAAkG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}