{"ast": null, "code": "import React,{useState}from'react';import{Box,Typography,Paper,IconButton,Alert,Snackbar}from'@mui/material';import{ArrowBack as ArrowBackIcon,Refresh as RefreshIcon}from'@mui/icons-material';import{useNavigate}from'react-router-dom';import AdminHomeButton from'../../../components/common/AdminHomeButton';import MetriPosatiSemplificatoForm from'../../../components/cavi/MetriPosatiSemplificatoForm';import SelectedCantiereDisplay from'../../../components/common/SelectedCantiereDisplay';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MetriPosatiSemplificatoPage=()=>{const navigate=useNavigate();const[alertMessage,setAlertMessage]=useState(null);const[alertSeverity,setAlertSeverity]=useState('success');const[openSnackbar,setOpenSnackbar]=useState(false);// Recupera l'ID del cantiere selezionato dal localStorage\nconst cantiereId=localStorage.getItem('selectedCantiereId');const cantiereName=localStorage.getItem('selectedCantiereName');// Gestisce il ritorno alla pagina principale di posa cavi\nconst handleBackToPosa=()=>{navigate('/dashboard/cavi/posa');};// Gestisce il successo di un'operazione\nconst handleSuccess=message=>{setAlertMessage(message);setAlertSeverity('success');setOpenSnackbar(true);};// Gestisce un errore\nconst handleError=message=>{setAlertMessage(message);setAlertSeverity('error');setOpenSnackbar(true);};// Gestisce la chiusura dello snackbar\nconst handleCloseSnackbar=()=>{setOpenSnackbar(false);};return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:3,display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(IconButton,{onClick:handleBackToPosa,sx:{mr:1},children:/*#__PURE__*/_jsx(ArrowBackIcon,{})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",children:\"Inserimento Metri Posati (Semplificato)\"}),/*#__PURE__*/_jsx(IconButton,{onClick:()=>window.location.reload(),sx:{ml:2},color:\"primary\",title:\"Ricarica la pagina\",children:/*#__PURE__*/_jsx(RefreshIcon,{})})]}),/*#__PURE__*/_jsx(AdminHomeButton,{})]}),/*#__PURE__*/_jsx(Paper,{sx:{mb:3,p:2},children:/*#__PURE__*/_jsx(SelectedCantiereDisplay,{})}),/*#__PURE__*/_jsx(Paper,{sx:{p:3},children:/*#__PURE__*/_jsx(MetriPosatiSemplificatoForm,{cantiereId:cantiereId,onSuccess:handleSuccess,onError:handleError})}),/*#__PURE__*/_jsx(Snackbar,{open:openSnackbar,autoHideDuration:6000,onClose:handleCloseSnackbar,anchorOrigin:{vertical:'bottom',horizontal:'center'},children:/*#__PURE__*/_jsx(Alert,{onClose:handleCloseSnackbar,severity:alertSeverity,sx:{width:'100%'},children:alertMessage})})]});};export default MetriPosatiSemplificatoPage;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "IconButton", "<PERSON><PERSON>", "Snackbar", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "useNavigate", "AdminHomeButton", "MetriPosatiSemplificatoForm", "SelectedCantiereDisplay", "jsx", "_jsx", "jsxs", "_jsxs", "MetriPosatiSemplificatoPage", "navigate", "alertMessage", "setAlertMessage", "alertSeverity", "setAlertSeverity", "openSnackbar", "setOpenSnackbar", "cantiereId", "localStorage", "getItem", "cantiereName", "handleBackToPosa", "handleSuccess", "message", "handleError", "handleCloseSnackbar", "children", "sx", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "variant", "window", "location", "reload", "ml", "color", "title", "p", "onSuccess", "onError", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "width"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/posa/MetriPosatiSemplificatoPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  IconButton,\n  Alert,\n  Snackbar\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport MetriPosatiSemplificatoForm from '../../../components/cavi/MetriPosatiSemplificatoForm';\nimport SelectedCantiereDisplay from '../../../components/common/SelectedCantiereDisplay';\n\nconst MetriPosatiSemplificatoPage = () => {\n  const navigate = useNavigate();\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  const cantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Gestisce il ritorno alla pagina principale di posa cavi\n  const handleBackToPosa = () => {\n    navigate('/dashboard/cavi/posa');\n  };\n\n  // Gestisce il successo di un'operazione\n  const handleSuccess = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce un errore\n  const handleError = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce la chiusura dello snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToPosa} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Inserimento Metri Posati (Semplificato)\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <SelectedCantiereDisplay />\n      </Paper>\n\n      <Paper sx={{ p: 3 }}>\n        <MetriPosatiSemplificatoForm\n          cantiereId={cantiereId}\n          onSuccess={handleSuccess}\n          onError={handleError}\n        />\n      </Paper>\n\n      <Snackbar\n        open={openSnackbar}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>\n          {alertMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default MetriPosatiSemplificatoPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,UAAU,CACVC,KAAK,CACLC,QAAQ,KACH,eAAe,CACtB,OACEC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,OAAO,GAAI,CAAAC,WAAW,KACjB,qBAAqB,CAC5B,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,eAAe,KAAM,4CAA4C,CACxE,MAAO,CAAAC,2BAA2B,KAAM,sDAAsD,CAC9F,MAAO,CAAAC,uBAAuB,KAAM,oDAAoD,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzF,KAAM,CAAAC,2BAA2B,CAAGA,CAAA,GAAM,CACxC,KAAM,CAAAC,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACU,YAAY,CAAEC,eAAe,CAAC,CAAGtB,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACuB,aAAa,CAAEC,gBAAgB,CAAC,CAAGxB,QAAQ,CAAC,SAAS,CAAC,CAC7D,KAAM,CAACyB,YAAY,CAAEC,eAAe,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAEvD;AACA,KAAM,CAAA2B,UAAU,CAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAC7D,KAAM,CAAAC,YAAY,CAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAEjE;AACA,KAAM,CAAAE,gBAAgB,CAAGA,CAAA,GAAM,CAC7BX,QAAQ,CAAC,sBAAsB,CAAC,CAClC,CAAC,CAED;AACA,KAAM,CAAAY,aAAa,CAAIC,OAAO,EAAK,CACjCX,eAAe,CAACW,OAAO,CAAC,CACxBT,gBAAgB,CAAC,SAAS,CAAC,CAC3BE,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAQ,WAAW,CAAID,OAAO,EAAK,CAC/BX,eAAe,CAACW,OAAO,CAAC,CACxBT,gBAAgB,CAAC,OAAO,CAAC,CACzBE,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAS,mBAAmB,CAAGA,CAAA,GAAM,CAChCT,eAAe,CAAC,KAAK,CAAC,CACxB,CAAC,CAED,mBACER,KAAA,CAACjB,GAAG,EAAAmC,QAAA,eACFlB,KAAA,CAACjB,GAAG,EAACoC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,eAAgB,CAAE,CAAAL,QAAA,eACzFlB,KAAA,CAACjB,GAAG,EAACoC,EAAE,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAJ,QAAA,eACjDpB,IAAA,CAACZ,UAAU,EAACsC,OAAO,CAAEX,gBAAiB,CAACM,EAAE,CAAE,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,cACnDpB,IAAA,CAACR,aAAa,GAAE,CAAC,CACP,CAAC,cACbQ,IAAA,CAACd,UAAU,EAAC0C,OAAO,CAAC,IAAI,CAAAR,QAAA,CAAC,yCAEzB,CAAY,CAAC,cACbpB,IAAA,CAACZ,UAAU,EACTsC,OAAO,CAAEA,CAAA,GAAMG,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE,CACxCV,EAAE,CAAE,CAAEW,EAAE,CAAE,CAAE,CAAE,CACdC,KAAK,CAAC,SAAS,CACfC,KAAK,CAAC,oBAAoB,CAAAd,QAAA,cAE1BpB,IAAA,CAACN,WAAW,GAAE,CAAC,CACL,CAAC,EACV,CAAC,cACNM,IAAA,CAACJ,eAAe,GAAE,CAAC,EAChB,CAAC,cAENI,IAAA,CAACb,KAAK,EAACkC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEa,CAAC,CAAE,CAAE,CAAE,CAAAf,QAAA,cACzBpB,IAAA,CAACF,uBAAuB,GAAE,CAAC,CACtB,CAAC,cAERE,IAAA,CAACb,KAAK,EAACkC,EAAE,CAAE,CAAEc,CAAC,CAAE,CAAE,CAAE,CAAAf,QAAA,cAClBpB,IAAA,CAACH,2BAA2B,EAC1Bc,UAAU,CAAEA,UAAW,CACvByB,SAAS,CAAEpB,aAAc,CACzBqB,OAAO,CAAEnB,WAAY,CACtB,CAAC,CACG,CAAC,cAERlB,IAAA,CAACV,QAAQ,EACPgD,IAAI,CAAE7B,YAAa,CACnB8B,gBAAgB,CAAE,IAAK,CACvBC,OAAO,CAAErB,mBAAoB,CAC7BsB,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAvB,QAAA,cAE3DpB,IAAA,CAACX,KAAK,EAACmD,OAAO,CAAErB,mBAAoB,CAACyB,QAAQ,CAAErC,aAAc,CAACc,EAAE,CAAE,CAAEwB,KAAK,CAAE,MAAO,CAAE,CAAAzB,QAAA,CACjFf,YAAY,CACR,CAAC,CACA,CAAC,EACR,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}