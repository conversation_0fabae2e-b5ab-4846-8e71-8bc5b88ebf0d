{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'أقل من ثانية واحدة',\n    two: 'أقل من ثانتين',\n    threeToTen: 'أقل من {{count}} ثواني',\n    other: 'أقل من {{count}} ثانية'\n  },\n  xSeconds: {\n    one: 'ثانية واحدة',\n    two: 'ثانتين',\n    threeToTen: '{{count}} ثواني',\n    other: '{{count}} ثانية'\n  },\n  halfAMinute: 'نصف دقيقة',\n  lessThanXMinutes: {\n    one: 'أقل من دقيقة',\n    two: 'أقل من دقيقتين',\n    threeToTen: 'أقل من {{count}} دقائق',\n    other: 'أقل من {{count}} دقيقة'\n  },\n  xMinutes: {\n    one: 'دقيقة واحدة',\n    two: 'دقيقتين',\n    threeToTen: '{{count}} دقائق',\n    other: '{{count}} دقيقة'\n  },\n  aboutXHours: {\n    one: 'ساعة واحدة تقريباً',\n    two: 'ساعتين تقريباً',\n    threeToTen: '{{count}} ساعات تقريباً',\n    other: '{{count}} ساعة تقريباً'\n  },\n  xHours: {\n    one: 'ساعة واحدة',\n    two: 'ساعتين',\n    threeToTen: '{{count}} ساعات',\n    other: '{{count}} ساعة'\n  },\n  xDays: {\n    one: 'يوم واحد',\n    two: 'يومين',\n    threeToTen: '{{count}} أيام',\n    other: '{{count}} يوم'\n  },\n  aboutXWeeks: {\n    one: 'أسبوع واحد تقريباً',\n    two: 'أسبوعين تقريباً',\n    threeToTen: '{{count}} أسابيع تقريباً',\n    other: '{{count}} أسبوع تقريباً'\n  },\n  xWeeks: {\n    one: 'أسبوع واحد',\n    two: 'أسبوعين',\n    threeToTen: '{{count}} أسابيع',\n    other: '{{count}} أسبوع'\n  },\n  aboutXMonths: {\n    one: 'شهر واحد تقريباً',\n    two: 'شهرين تقريباً',\n    threeToTen: '{{count}} أشهر تقريباً',\n    other: '{{count}} شهر تقريباً'\n  },\n  xMonths: {\n    one: 'شهر واحد',\n    two: 'شهرين',\n    threeToTen: '{{count}} أشهر',\n    other: '{{count}} شهر'\n  },\n  aboutXYears: {\n    one: 'عام واحد تقريباً',\n    two: 'عامين تقريباً',\n    threeToTen: '{{count}} أعوام تقريباً',\n    other: '{{count}} عام تقريباً'\n  },\n  xYears: {\n    one: 'عام واحد',\n    two: 'عامين',\n    threeToTen: '{{count}} أعوام',\n    other: '{{count}} عام'\n  },\n  overXYears: {\n    one: 'أكثر من عام',\n    two: 'أكثر من عامين',\n    threeToTen: 'أكثر من {{count}} أعوام',\n    other: 'أكثر من {{count}} عام'\n  },\n  almostXYears: {\n    one: 'عام واحد تقريباً',\n    two: 'عامين تقريباً',\n    threeToTen: '{{count}} أعوام تقريباً',\n    other: '{{count}} عام تقريباً'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  options = options || {};\n  var usageGroup = formatDistanceLocale[token];\n  var result;\n  if (typeof usageGroup === 'string') {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else if (count === 2) {\n    result = usageGroup.two;\n  } else if (count <= 10) {\n    result = usageGroup.threeToTen.replace('{{count}}', String(count));\n  } else {\n    result = usageGroup.other.replace('{{count}}', String(count));\n  }\n  if (options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'في خلال ' + result;\n    } else {\n      return 'منذ ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "two", "threeToTen", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "usageGroup", "result", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/ar-DZ/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'أقل من ثانية واحدة',\n    two: 'أقل من ثانتين',\n    threeToTen: 'أقل من {{count}} ثواني',\n    other: 'أقل من {{count}} ثانية'\n  },\n  xSeconds: {\n    one: 'ثانية واحدة',\n    two: 'ثانتين',\n    threeToTen: '{{count}} ثواني',\n    other: '{{count}} ثانية'\n  },\n  halfAMinute: 'نصف دقيقة',\n  lessThanXMinutes: {\n    one: 'أقل من دقيقة',\n    two: 'أقل من دقيقتين',\n    threeToTen: 'أقل من {{count}} دقائق',\n    other: 'أقل من {{count}} دقيقة'\n  },\n  xMinutes: {\n    one: 'دقيقة واحدة',\n    two: 'دقيقتين',\n    threeToTen: '{{count}} دقائق',\n    other: '{{count}} دقيقة'\n  },\n  aboutXHours: {\n    one: 'ساعة واحدة تقريباً',\n    two: 'ساعتين تقريباً',\n    threeToTen: '{{count}} ساعات تقريباً',\n    other: '{{count}} ساعة تقريباً'\n  },\n  xHours: {\n    one: 'ساعة واحدة',\n    two: 'ساعتين',\n    threeToTen: '{{count}} ساعات',\n    other: '{{count}} ساعة'\n  },\n  xDays: {\n    one: 'يوم واحد',\n    two: 'يومين',\n    threeToTen: '{{count}} أيام',\n    other: '{{count}} يوم'\n  },\n  aboutXWeeks: {\n    one: 'أسبوع واحد تقريباً',\n    two: 'أسبوعين تقريباً',\n    threeToTen: '{{count}} أسابيع تقريباً',\n    other: '{{count}} أسبوع تقريباً'\n  },\n  xWeeks: {\n    one: 'أسبوع واحد',\n    two: 'أسبوعين',\n    threeToTen: '{{count}} أسابيع',\n    other: '{{count}} أسبوع'\n  },\n  aboutXMonths: {\n    one: 'شهر واحد تقريباً',\n    two: 'شهرين تقريباً',\n    threeToTen: '{{count}} أشهر تقريباً',\n    other: '{{count}} شهر تقريباً'\n  },\n  xMonths: {\n    one: 'شهر واحد',\n    two: 'شهرين',\n    threeToTen: '{{count}} أشهر',\n    other: '{{count}} شهر'\n  },\n  aboutXYears: {\n    one: 'عام واحد تقريباً',\n    two: 'عامين تقريباً',\n    threeToTen: '{{count}} أعوام تقريباً',\n    other: '{{count}} عام تقريباً'\n  },\n  xYears: {\n    one: 'عام واحد',\n    two: 'عامين',\n    threeToTen: '{{count}} أعوام',\n    other: '{{count}} عام'\n  },\n  overXYears: {\n    one: 'أكثر من عام',\n    two: 'أكثر من عامين',\n    threeToTen: 'أكثر من {{count}} أعوام',\n    other: 'أكثر من {{count}} عام'\n  },\n  almostXYears: {\n    one: 'عام واحد تقريباً',\n    two: 'عامين تقريباً',\n    threeToTen: '{{count}} أعوام تقريباً',\n    other: '{{count}} عام تقريباً'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  options = options || {};\n  var usageGroup = formatDistanceLocale[token];\n  var result;\n  if (typeof usageGroup === 'string') {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else if (count === 2) {\n    result = usageGroup.two;\n  } else if (count <= 10) {\n    result = usageGroup.threeToTen.replace('{{count}}', String(count));\n  } else {\n    result = usageGroup.other.replace('{{count}}', String(count));\n  }\n  if (options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'في خلال ' + result;\n    } else {\n      return 'منذ ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,oBAAoB;IACzBC,GAAG,EAAE,eAAe;IACpBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRJ,GAAG,EAAE,aAAa;IAClBC,GAAG,EAAE,QAAQ;IACbC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,WAAW;EACxBC,gBAAgB,EAAE;IAChBN,GAAG,EAAE,cAAc;IACnBC,GAAG,EAAE,gBAAgB;IACrBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRP,GAAG,EAAE,aAAa;IAClBC,GAAG,EAAE,SAAS;IACdC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXR,GAAG,EAAE,oBAAoB;IACzBC,GAAG,EAAE,gBAAgB;IACrBC,UAAU,EAAE,yBAAyB;IACrCC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNT,GAAG,EAAE,YAAY;IACjBC,GAAG,EAAE,QAAQ;IACbC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLV,GAAG,EAAE,UAAU;IACfC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,gBAAgB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXX,GAAG,EAAE,oBAAoB;IACzBC,GAAG,EAAE,iBAAiB;IACtBC,UAAU,EAAE,0BAA0B;IACtCC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNZ,GAAG,EAAE,YAAY;IACjBC,GAAG,EAAE,SAAS;IACdC,UAAU,EAAE,kBAAkB;IAC9BC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZb,GAAG,EAAE,kBAAkB;IACvBC,GAAG,EAAE,eAAe;IACpBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPd,GAAG,EAAE,UAAU;IACfC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,gBAAgB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXf,GAAG,EAAE,kBAAkB;IACvBC,GAAG,EAAE,eAAe;IACpBC,UAAU,EAAE,yBAAyB;IACrCC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNhB,GAAG,EAAE,UAAU;IACfC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVjB,GAAG,EAAE,aAAa;IAClBC,GAAG,EAAE,eAAe;IACpBC,UAAU,EAAE,yBAAyB;IACrCC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZlB,GAAG,EAAE,kBAAkB;IACvBC,GAAG,EAAE,eAAe;IACpBC,UAAU,EAAE,yBAAyB;IACrCC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClEA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIC,UAAU,GAAGzB,oBAAoB,CAACsB,KAAK,CAAC;EAC5C,IAAII,MAAM;EACV,IAAI,OAAOD,UAAU,KAAK,QAAQ,EAAE;IAClCC,MAAM,GAAGD,UAAU;EACrB,CAAC,MAAM,IAAIF,KAAK,KAAK,CAAC,EAAE;IACtBG,MAAM,GAAGD,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM,IAAIqB,KAAK,KAAK,CAAC,EAAE;IACtBG,MAAM,GAAGD,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM,IAAIoB,KAAK,IAAI,EAAE,EAAE;IACtBG,MAAM,GAAGD,UAAU,CAACrB,UAAU,CAACuB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EACpE,CAAC,MAAM;IACLG,MAAM,GAAGD,UAAU,CAACpB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,CAACK,SAAS,EAAE;IACrB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,UAAU,GAAGJ,MAAM;IAC5B,CAAC,MAAM;MACL,OAAO,MAAM,GAAGA,MAAM;IACxB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}