{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\SelezionaCavoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Alert, CircularProgress, Typography, Paper, Dialog, DialogTitle, DialogContent, DialogActions, DialogContentText } from '@mui/material';\nimport { Cancel as CancelIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport CavoForm from './CavoForm';\n\n/**\n * Componente per la selezione e modifica di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.isDialog - Indica se il componente è in un dialog\n * @param {Function} props.onCancel - Funzione chiamata all'annullamento dell'operazione\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SelezionaCavoForm = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  isDialog = false,\n  onCancel\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(true);\n  const [cavi, setCavi] = useState([]);\n  const [selectedCavoId, setSelectedCavoId] = useState('');\n  const [cavoSelectionStep, setCavoSelectionStep] = useState(true);\n  const [redirectDialog, setRedirectDialog] = useState(false);\n  const [selectedCavoData, setSelectedCavoData] = useState(null);\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    const loadCavi = async () => {\n      try {\n        setCaviLoading(true);\n        const caviData = await caviService.getCavi(cantiereId);\n        // Filtra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\n        const caviNonPosati = caviData.filter(cavo => parseFloat(cavo.metratura_reale) === 0 && cavo.stato_installazione !== 'Installato');\n        setCavi(caviNonPosati);\n      } catch (error) {\n        console.error('Errore nel caricamento dei cavi:', error);\n        onError('Errore nel caricamento dei cavi');\n      } finally {\n        setCaviLoading(false);\n      }\n    };\n    loadCavi();\n  }, [cantiereId, onError]);\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelection = async () => {\n    if (!selectedCavoId) {\n      onError('Seleziona un cavo da modificare');\n      return;\n    }\n    try {\n      setLoading(true);\n      const cavoData = await caviService.getCavoById(cantiereId, selectedCavoId);\n\n      // Verifica se il cavo è già posato\n      if (parseFloat(cavoData.metratura_reale) > 0 || cavoData.stato_installazione === 'Installato') {\n        // Mostra dialog per reindirizzare a modifica_bobina_cavo_posato\n        setRedirectDialog(true);\n        return;\n      }\n\n      // Imposta i dati del cavo nel form\n      setSelectedCavoData(cavoData);\n      setCavoSelectionStep(false);\n    } catch (error) {\n      console.error('Errore nel caricamento dei dettagli del cavo:', error);\n      onError('Errore nel caricamento dei dettagli del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, chiama la funzione onCancel passata come prop\n      if (onCancel) {\n        onCancel();\n      }\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n  // Gestisce il reindirizzamento a modifica_bobina_cavo_posato\n  const handleRedirect = () => {\n    setRedirectDialog(false);\n    navigate(`/dashboard/cantieri/${cantiereId}/cavi/posa/modifica-bobina`);\n  };\n\n  // Gestisce il ritorno alla selezione del cavo\n  const handleBackToSelection = () => {\n    setCavoSelectionStep(true);\n    setSelectedCavoData(null);\n  };\n\n  // Renderizza il form di selezione del cavo\n  if (cavoSelectionStep) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 1.5 : 3,\n          mb: isDialog ? 1 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontSize: isDialog ? '1rem' : '1.25rem',\n            mb: isDialog ? 1 : 1.5,\n            mt: isDialog ? 1 : 2\n          },\n          children: \"Seleziona un cavo da modificare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          paragraph: true,\n          children: \"Puoi modificare solo i cavi non ancora posati. Se il cavo \\xE8 gi\\xE0 posato, verrai reindirizzato alla funzione di modifica bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2\n            },\n            children: \"Non ci sono cavi non posati disponibili per la modifica.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"cavo-select-label\",\n                  children: \"Seleziona Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: \"cavo-select-label\",\n                  id: \"cavo-select\",\n                  value: selectedCavoId,\n                  label: \"Seleziona Cavo\",\n                  onChange: e => setSelectedCavoId(e.target.value),\n                  children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: cavo.id_cavo,\n                    children: [cavo.id_cavo, \" - \", cavo.sistema || 'N/A', \" - \", cavo.utility || 'N/A']\n                  }, cavo.id_cavo, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"secondary\",\n                onClick: handleCancel,\n                startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 34\n                }, this),\n                children: \"Annulla\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: handleCavoSelection,\n                disabled: !selectedCavoId || loading,\n                children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 34\n                }, this) : 'Seleziona'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 17\n          }, this)\n        }, void 0, false)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: redirectDialog,\n        onClose: () => setRedirectDialog(false),\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Cavo gi\\xE0 posato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n            children: \"Il cavo selezionato \\xE8 gi\\xE0 stato posato. Verrai reindirizzato alla funzione di modifica bobina per cavi posati.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setRedirectDialog(false),\n            color: \"secondary\",\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleRedirect,\n            color: \"primary\",\n            autoFocus: true,\n            children: \"Vai a Modifica Bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Renderizza il form di modifica del cavo utilizzando CavoForm\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: selectedCavoData && /*#__PURE__*/_jsxDEV(CavoForm, {\n      mode: \"edit\",\n      initialData: selectedCavoData,\n      cantiereId: cantiereId,\n      onSubmit: async validatedData => {\n        try {\n          // Rimuovi i campi di sistema che non devono essere modificati\n          const dataToSend = {\n            ...validatedData\n          };\n          delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n          delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n          delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n          delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n          delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n          // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n          dataToSend.modificato_manualmente = 1;\n          await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n          return true;\n        } catch (error) {\n          throw error;\n        }\n      },\n      onSuccess: message => {\n        onSuccess(message);\n        if (isDialog) {\n          if (onCancel) {\n            onCancel();\n          }\n        } else {\n          redirectToVisualizzaCavi(navigate);\n        }\n      },\n      onError: onError,\n      isDialog: isDialog,\n      onCancel: isDialog ? onCancel : handleBackToSelection\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 216,\n    columnNumber: 5\n  }, this);\n};\n_s(SelezionaCavoForm, \"18g1nbSmkz525aceSmyWhtBPkzU=\", false, function () {\n  return [useNavigate];\n});\n_c = SelezionaCavoForm;\nexport default SelezionaCavoForm;\nvar _c;\n$RefreshReg$(_c, \"SelezionaCavoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "Typography", "Paper", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "DialogContentText", "Cancel", "CancelIcon", "useNavigate", "caviService", "redirectToVisualizzaCavi", "CavoForm", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SelezionaCavoForm", "cantiereId", "onSuccess", "onError", "isDialog", "onCancel", "_s", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "cavi", "<PERSON><PERSON><PERSON>", "selectedCavoId", "setSelectedCavoId", "cavoSelectionStep", "setCavoSelectionStep", "redirectDialog", "setRedirectDialog", "selectedCavoData", "setSelectedCavoData", "loadCavi", "caviData", "get<PERSON><PERSON>", "cavi<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "cavo", "parseFloat", "metratura_reale", "stato_installazione", "error", "console", "handleCavoSelection", "cavoData", "getCavoById", "handleCancel", "handleRedirect", "handleBackToSelection", "children", "sx", "p", "mb", "boxShadow", "variant", "fontSize", "mt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "paragraph", "display", "justifyContent", "my", "length", "severity", "container", "spacing", "item", "xs", "fullWidth", "id", "labelId", "value", "label", "onChange", "e", "target", "map", "id_cavo", "sistema", "utility", "onClick", "startIcon", "disabled", "size", "open", "onClose", "autoFocus", "mode", "initialData", "onSubmit", "validatedData", "dataToSend", "id_bobina", "modificato_manualmente", "timestamp", "updateCavo", "message", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/SelezionaCavoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  Typography,\n  Paper,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  DialogContentText\n} from '@mui/material';\nimport { Cancel as CancelIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport CavoForm from './CavoForm';\n\n/**\n * Componente per la selezione e modifica di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.isDialog - Indica se il componente è in un dialog\n * @param {Function} props.onCancel - Funzione chiamata all'annullamento dell'operazione\n */\nconst SelezionaCavoForm = ({ cantiereId, onSuccess, onError, isDialog = false, onCancel }) => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(true);\n  const [cavi, setCavi] = useState([]);\n  const [selectedCavoId, setSelectedCavoId] = useState('');\n  const [cavoSelectionStep, setCavoSelectionStep] = useState(true);\n  const [redirectDialog, setRedirectDialog] = useState(false);\n  const [selectedCavoData, setSelectedCavoData] = useState(null);\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    const loadCavi = async () => {\n      try {\n        setCaviLoading(true);\n        const caviData = await caviService.getCavi(cantiereId);\n        // Filtra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\n        const caviNonPosati = caviData.filter(cavo =>\n          parseFloat(cavo.metratura_reale) === 0 &&\n          cavo.stato_installazione !== 'Installato'\n        );\n        setCavi(caviNonPosati);\n      } catch (error) {\n        console.error('Errore nel caricamento dei cavi:', error);\n        onError('Errore nel caricamento dei cavi');\n      } finally {\n        setCaviLoading(false);\n      }\n    };\n\n    loadCavi();\n  }, [cantiereId, onError]);\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelection = async () => {\n    if (!selectedCavoId) {\n      onError('Seleziona un cavo da modificare');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      const cavoData = await caviService.getCavoById(cantiereId, selectedCavoId);\n\n      // Verifica se il cavo è già posato\n      if (parseFloat(cavoData.metratura_reale) > 0 || cavoData.stato_installazione === 'Installato') {\n        // Mostra dialog per reindirizzare a modifica_bobina_cavo_posato\n        setRedirectDialog(true);\n        return;\n      }\n\n      // Imposta i dati del cavo nel form\n      setSelectedCavoData(cavoData);\n      setCavoSelectionStep(false);\n    } catch (error) {\n      console.error('Errore nel caricamento dei dettagli del cavo:', error);\n      onError('Errore nel caricamento dei dettagli del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, chiama la funzione onCancel passata come prop\n      if (onCancel) {\n        onCancel();\n      }\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n  // Gestisce il reindirizzamento a modifica_bobina_cavo_posato\n  const handleRedirect = () => {\n    setRedirectDialog(false);\n    navigate(`/dashboard/cantieri/${cantiereId}/cavi/posa/modifica-bobina`);\n  };\n\n  // Gestisce il ritorno alla selezione del cavo\n  const handleBackToSelection = () => {\n    setCavoSelectionStep(true);\n    setSelectedCavoData(null);\n  };\n\n  // Renderizza il form di selezione del cavo\n  if (cavoSelectionStep) {\n    return (\n      <Box>\n        <Paper sx={{ p: isDialog ? 1.5 : 3, mb: isDialog ? 1 : 3, boxShadow: isDialog ? 0 : 1 }}>\n          <Typography variant=\"h6\" sx={{ fontSize: isDialog ? '1rem' : '1.25rem', mb: isDialog ? 1 : 1.5, mt: isDialog ? 1 : 2 }}>\n            Seleziona un cavo da modificare\n          </Typography>\n          <Typography variant=\"body2\" color=\"textSecondary\" paragraph>\n            Puoi modificare solo i cavi non ancora posati. Se il cavo è già posato, verrai reindirizzato alla funzione di modifica bobina.\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <>\n              {cavi.length === 0 ? (\n                <Alert severity=\"info\" sx={{ mt: 2 }}>\n                  Non ci sono cavi non posati disponibili per la modifica.\n                </Alert>\n              ) : (\n                <Grid container spacing={2} sx={{ mt: 1 }}>\n                  <Grid item xs={12}>\n                    <FormControl fullWidth>\n                      <InputLabel id=\"cavo-select-label\">Seleziona Cavo</InputLabel>\n                      <Select\n                        labelId=\"cavo-select-label\"\n                        id=\"cavo-select\"\n                        value={selectedCavoId}\n                        label=\"Seleziona Cavo\"\n                        onChange={(e) => setSelectedCavoId(e.target.value)}\n                      >\n                        {cavi.map((cavo) => (\n                          <MenuItem key={cavo.id_cavo} value={cavo.id_cavo}>\n                            {cavo.id_cavo} - {cavo.sistema || 'N/A'} - {cavo.utility || 'N/A'}\n                          </MenuItem>\n                        ))}\n                      </Select>\n                    </FormControl>\n                  </Grid>\n                  <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>\n                    <Button\n                      variant=\"outlined\"\n                      color=\"secondary\"\n                      onClick={handleCancel}\n                      startIcon={<CancelIcon />}\n                    >\n                      Annulla\n                    </Button>\n                    <Button\n                      variant=\"contained\"\n                      color=\"primary\"\n                      onClick={handleCavoSelection}\n                      disabled={!selectedCavoId || loading}\n                    >\n                      {loading ? <CircularProgress size={24} /> : 'Seleziona'}\n                    </Button>\n                  </Grid>\n                </Grid>\n              )}\n            </>\n          )}\n        </Paper>\n\n        {/* Dialog per reindirizzamento */}\n        <Dialog\n          open={redirectDialog}\n          onClose={() => setRedirectDialog(false)}\n        >\n          <DialogTitle>Cavo già posato</DialogTitle>\n          <DialogContent>\n            <DialogContentText>\n              Il cavo selezionato è già stato posato. Verrai reindirizzato alla funzione di modifica bobina per cavi posati.\n            </DialogContentText>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={() => setRedirectDialog(false)} color=\"secondary\">\n              Annulla\n            </Button>\n            <Button onClick={handleRedirect} color=\"primary\" autoFocus>\n              Vai a Modifica Bobina\n            </Button>\n          </DialogActions>\n        </Dialog>\n      </Box>\n    );\n  }\n\n  // Renderizza il form di modifica del cavo utilizzando CavoForm\n  return (\n    <Box>\n      {selectedCavoData && (\n        <CavoForm\n          mode=\"edit\"\n          initialData={selectedCavoData}\n          cantiereId={cantiereId}\n          onSubmit={async (validatedData) => {\n            try {\n              // Rimuovi i campi di sistema che non devono essere modificati\n              const dataToSend = { ...validatedData };\n              delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n              delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n              delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n              delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n              delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n              // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n              dataToSend.modificato_manualmente = 1;\n\n              await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n              return true;\n            } catch (error) {\n              throw error;\n            }\n          }}\n          onSuccess={(message) => {\n            onSuccess(message);\n            if (isDialog) {\n              if (onCancel) {\n                onCancel();\n              }\n            } else {\n              redirectToVisualizzaCavi(navigate);\n            }\n          }}\n          onError={onError}\n          isDialog={isDialog}\n          onCancel={isDialog ? onCancel : handleBackToSelection}\n        />\n      )}\n    </Box>\n  );\n};\n\nexport default SelezionaCavoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,iBAAiB,QACZ,eAAe;AACtB,SAASC,MAAM,IAAIC,UAAU,QAAQ,qBAAqB;AAC1D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,wBAAwB,QAAQ,6BAA6B;AACtE,OAAOC,QAAQ,MAAM,YAAY;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAUA,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,QAAQ,GAAG,KAAK;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5F,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyC,IAAI,EAAEC,OAAO,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC2C,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkD,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACFX,cAAc,CAAC,IAAI,CAAC;QACpB,MAAMY,QAAQ,GAAG,MAAM9B,WAAW,CAAC+B,OAAO,CAACvB,UAAU,CAAC;QACtD;QACA,MAAMwB,aAAa,GAAGF,QAAQ,CAACG,MAAM,CAACC,IAAI,IACxCC,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,KAAK,CAAC,IACtCF,IAAI,CAACG,mBAAmB,KAAK,YAC/B,CAAC;QACDjB,OAAO,CAACY,aAAa,CAAC;MACxB,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD5B,OAAO,CAAC,iCAAiC,CAAC;MAC5C,CAAC,SAAS;QACRQ,cAAc,CAAC,KAAK,CAAC;MACvB;IACF,CAAC;IAEDW,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACrB,UAAU,EAAEE,OAAO,CAAC,CAAC;;EAEzB;EACA,MAAM8B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACnB,cAAc,EAAE;MACnBX,OAAO,CAAC,iCAAiC,CAAC;MAC1C;IACF;IAEA,IAAI;MACFM,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMyB,QAAQ,GAAG,MAAMzC,WAAW,CAAC0C,WAAW,CAAClC,UAAU,EAAEa,cAAc,CAAC;;MAE1E;MACA,IAAIc,UAAU,CAACM,QAAQ,CAACL,eAAe,CAAC,GAAG,CAAC,IAAIK,QAAQ,CAACJ,mBAAmB,KAAK,YAAY,EAAE;QAC7F;QACAX,iBAAiB,CAAC,IAAI,CAAC;QACvB;MACF;;MAEA;MACAE,mBAAmB,CAACa,QAAQ,CAAC;MAC7BjB,oBAAoB,CAAC,KAAK,CAAC;IAC7B,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE5B,OAAO,CAAC,8CAA8C,CAAC;IACzD,CAAC,SAAS;MACRM,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2B,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIhC,QAAQ,EAAE;MACZ;MACA,IAAIC,QAAQ,EAAE;QACZA,QAAQ,CAAC,CAAC;MACZ;MACA;IACF;IACA;IACAX,wBAAwB,CAACa,QAAQ,CAAC;EACpC,CAAC;;EAED;EACA,MAAM8B,cAAc,GAAGA,CAAA,KAAM;IAC3BlB,iBAAiB,CAAC,KAAK,CAAC;IACxBZ,QAAQ,CAAC,uBAAuBN,UAAU,4BAA4B,CAAC;EACzE,CAAC;;EAED;EACA,MAAMqC,qBAAqB,GAAGA,CAAA,KAAM;IAClCrB,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,IAAIL,iBAAiB,EAAE;IACrB,oBACEnB,OAAA,CAACxB,GAAG;MAAAkE,QAAA,gBACF1C,OAAA,CAACb,KAAK;QAACwD,EAAE,EAAE;UAAEC,CAAC,EAAErC,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAEsC,EAAE,EAAEtC,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEuC,SAAS,EAAEvC,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAmC,QAAA,gBACtF1C,OAAA,CAACd,UAAU;UAAC6D,OAAO,EAAC,IAAI;UAACJ,EAAE,EAAE;YAAEK,QAAQ,EAAEzC,QAAQ,GAAG,MAAM,GAAG,SAAS;YAAEsC,EAAE,EAAEtC,QAAQ,GAAG,CAAC,GAAG,GAAG;YAAE0C,EAAE,EAAE1C,QAAQ,GAAG,CAAC,GAAG;UAAE,CAAE;UAAAmC,QAAA,EAAC;QAExH;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrD,OAAA,CAACd,UAAU;UAAC6D,OAAO,EAAC,OAAO;UAACO,KAAK,EAAC,eAAe;UAACC,SAAS;UAAAb,QAAA,EAAC;QAE5D;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZxC,WAAW,gBACVb,OAAA,CAACxB,GAAG;UAACmE,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAhB,QAAA,eAC5D1C,OAAA,CAACf,gBAAgB;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAENrD,OAAA,CAAAE,SAAA;UAAAwC,QAAA,EACG3B,IAAI,CAAC4C,MAAM,KAAK,CAAC,gBAChB3D,OAAA,CAAChB,KAAK;YAAC4E,QAAQ,EAAC,MAAM;YAACjB,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAAC;UAEtC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAERrD,OAAA,CAACrB,IAAI;YAACkF,SAAS;YAACC,OAAO,EAAE,CAAE;YAACnB,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBACxC1C,OAAA,CAACrB,IAAI;cAACoF,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAtB,QAAA,eAChB1C,OAAA,CAACpB,WAAW;gBAACqF,SAAS;gBAAAvB,QAAA,gBACpB1C,OAAA,CAACnB,UAAU;kBAACqF,EAAE,EAAC,mBAAmB;kBAAAxB,QAAA,EAAC;gBAAc;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9DrD,OAAA,CAAClB,MAAM;kBACLqF,OAAO,EAAC,mBAAmB;kBAC3BD,EAAE,EAAC,aAAa;kBAChBE,KAAK,EAAEnD,cAAe;kBACtBoD,KAAK,EAAC,gBAAgB;kBACtBC,QAAQ,EAAGC,CAAC,IAAKrD,iBAAiB,CAACqD,CAAC,CAACC,MAAM,CAACJ,KAAK,CAAE;kBAAA1B,QAAA,EAElD3B,IAAI,CAAC0D,GAAG,CAAE3C,IAAI,iBACb9B,OAAA,CAACjB,QAAQ;oBAAoBqF,KAAK,EAAEtC,IAAI,CAAC4C,OAAQ;oBAAAhC,QAAA,GAC9CZ,IAAI,CAAC4C,OAAO,EAAC,KAAG,EAAC5C,IAAI,CAAC6C,OAAO,IAAI,KAAK,EAAC,KAAG,EAAC7C,IAAI,CAAC8C,OAAO,IAAI,KAAK;kBAAA,GADpD9C,IAAI,CAAC4C,OAAO;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACPrD,OAAA,CAACrB,IAAI;cAACoF,IAAI;cAACC,EAAE,EAAE,EAAG;cAACrB,EAAE,EAAE;gBAAEa,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAER,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBACjF1C,OAAA,CAACtB,MAAM;gBACLqE,OAAO,EAAC,UAAU;gBAClBO,KAAK,EAAC,WAAW;gBACjBuB,OAAO,EAAEtC,YAAa;gBACtBuC,SAAS,eAAE9E,OAAA,CAACN,UAAU;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAX,QAAA,EAC3B;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTrD,OAAA,CAACtB,MAAM;gBACLqE,OAAO,EAAC,WAAW;gBACnBO,KAAK,EAAC,SAAS;gBACfuB,OAAO,EAAEzC,mBAAoB;gBAC7B2C,QAAQ,EAAE,CAAC9D,cAAc,IAAIN,OAAQ;gBAAA+B,QAAA,EAEpC/B,OAAO,gBAAGX,OAAA,CAACf,gBAAgB;kBAAC+F,IAAI,EAAE;gBAAG;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG;cAAW;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACP,gBACD,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGRrD,OAAA,CAACZ,MAAM;QACL6F,IAAI,EAAE5D,cAAe;QACrB6D,OAAO,EAAEA,CAAA,KAAM5D,iBAAiB,CAAC,KAAK,CAAE;QAAAoB,QAAA,gBAExC1C,OAAA,CAACX,WAAW;UAAAqD,QAAA,EAAC;QAAe;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC1CrD,OAAA,CAACV,aAAa;UAAAoD,QAAA,eACZ1C,OAAA,CAACR,iBAAiB;YAAAkD,QAAA,EAAC;UAEnB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAChBrD,OAAA,CAACT,aAAa;UAAAmD,QAAA,gBACZ1C,OAAA,CAACtB,MAAM;YAACmG,OAAO,EAAEA,CAAA,KAAMvD,iBAAiB,CAAC,KAAK,CAAE;YAACgC,KAAK,EAAC,WAAW;YAAAZ,QAAA,EAAC;UAEnE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrD,OAAA,CAACtB,MAAM;YAACmG,OAAO,EAAErC,cAAe;YAACc,KAAK,EAAC,SAAS;YAAC6B,SAAS;YAAAzC,QAAA,EAAC;UAE3D;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;;EAEA;EACA,oBACErD,OAAA,CAACxB,GAAG;IAAAkE,QAAA,EACDnB,gBAAgB,iBACfvB,OAAA,CAACF,QAAQ;MACPsF,IAAI,EAAC,MAAM;MACXC,WAAW,EAAE9D,gBAAiB;MAC9BnB,UAAU,EAAEA,UAAW;MACvBkF,QAAQ,EAAE,MAAOC,aAAa,IAAK;QACjC,IAAI;UACF;UACA,MAAMC,UAAU,GAAG;YAAE,GAAGD;UAAc,CAAC;UACvC,OAAOC,UAAU,CAACC,SAAS,CAAC,CAAC;UAC7B,OAAOD,UAAU,CAACxD,eAAe,CAAC,CAAC;UACnC,OAAOwD,UAAU,CAACE,sBAAsB,CAAC,CAAC;UAC1C,OAAOF,UAAU,CAACG,SAAS,CAAC,CAAC;UAC7B,OAAOH,UAAU,CAACvD,mBAAmB,CAAC,CAAC;;UAEvC;UACAuD,UAAU,CAACE,sBAAsB,GAAG,CAAC;UAErC,MAAM9F,WAAW,CAACgG,UAAU,CAACxF,UAAU,EAAEoF,UAAU,CAACd,OAAO,EAAEc,UAAU,CAAC;UACxE,OAAO,IAAI;QACb,CAAC,CAAC,OAAOtD,KAAK,EAAE;UACd,MAAMA,KAAK;QACb;MACF,CAAE;MACF7B,SAAS,EAAGwF,OAAO,IAAK;QACtBxF,SAAS,CAACwF,OAAO,CAAC;QAClB,IAAItF,QAAQ,EAAE;UACZ,IAAIC,QAAQ,EAAE;YACZA,QAAQ,CAAC,CAAC;UACZ;QACF,CAAC,MAAM;UACLX,wBAAwB,CAACa,QAAQ,CAAC;QACpC;MACF,CAAE;MACFJ,OAAO,EAAEA,OAAQ;MACjBC,QAAQ,EAAEA,QAAS;MACnBC,QAAQ,EAAED,QAAQ,GAAGC,QAAQ,GAAGiC;IAAsB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD;EACF;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5C,EAAA,CA7NIN,iBAAiB;EAAA,QACJR,WAAW;AAAA;AAAAmG,EAAA,GADxB3F,iBAAiB;AA+NvB,eAAeA,iBAAiB;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}