{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\TopNavbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { AppBar, Toolbar, Box, Button, Menu, MenuItem, Typography, IconButton, Divider, Avatar, Tooltip } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon, Logout as LogoutIcon, KeyboardArrowDown as ArrowDownIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport './TopNavbar.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TopNavbar = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    logout,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  const [posaAnchorEl, setPosaAnchorEl] = useState(null);\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n  const [reportAnchorEl, setReportAnchorEl] = useState(null);\n  const [certificazioneAnchorEl, setCertificazioneAnchorEl] = useState(null);\n  const [comandeAnchorEl, setComandeAnchorEl] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = setAnchorEl => {\n    setAnchorEl(null);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    handleMenuClose(setPosaAnchorEl);\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n    handleMenuClose(setReportAnchorEl);\n    handleMenuClose(setCertificazioneAnchorEl);\n    handleMenuClose(setComandeAnchorEl);\n  };\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = path => {\n    return location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/_jsxDEV(AppBar, {\n    position: \"static\",\n    color: \"default\",\n    elevation: 1,\n    sx: {\n      zIndex: 1100\n    },\n    className: \"excel-style-menu\",\n    children: /*#__PURE__*/_jsxDEV(Toolbar, {\n      variant: \"dense\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        color: \"inherit\",\n        onClick: () => navigateTo('/dashboard'),\n        startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 22\n        }, this),\n        sx: {\n          mr: 1\n        },\n        className: isActive('/dashboard') ? 'active-button' : '',\n        children: \"CMS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        orientation: \"vertical\",\n        flexItem: true,\n        sx: {\n          mx: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), (user === null || user === void 0 ? void 0 : user.role) === 'owner' && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          \"aria-controls\": \"admin-menu\",\n          \"aria-haspopup\": \"true\",\n          onClick: e => handleMenuOpen(e, setAdminAnchorEl),\n          endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 24\n          }, this),\n          sx: {\n            mr: 1\n          },\n          className: isPartOfActive('/dashboard/admin') ? 'active-button' : '',\n          children: \"Amministrazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Menu, {\n          id: \"admin-menu\",\n          anchorEl: adminAnchorEl,\n          keepMounted: true,\n          open: Boolean(adminAnchorEl),\n          onClose: () => handleMenuClose(setAdminAnchorEl),\n          children: /*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: () => navigateTo('/dashboard/admin'),\n            children: \"Pannello Admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), ((user === null || user === void 0 ? void 0 : user.role) !== 'owner' || (user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          \"aria-controls\": \"cantieri-menu\",\n          \"aria-haspopup\": \"true\",\n          onClick: e => handleMenuOpen(e, setCantieriAnchorEl),\n          endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 24\n          }, this),\n          sx: {\n            mr: 1\n          },\n          children: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Menu, {\n          id: \"cantieri-menu\",\n          anchorEl: cantieriAnchorEl,\n          keepMounted: true,\n          open: Boolean(cantieriAnchorEl),\n          onClose: () => handleMenuClose(setCantieriAnchorEl),\n          children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: () => navigateTo('/dashboard/cantieri'),\n            className: isActive('/dashboard/cantieri') ? 'active-item' : '',\n            children: \"Lista Cantieri\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(MenuItem, {\n            onClick: () => navigateTo(`/dashboard/cantieri/${selectedCantiereId}`),\n            className: isActive(`/dashboard/cantieri/${selectedCantiereId}`) ? 'active-item' : '',\n            children: [\"Cantiere: \", selectedCantiereName || selectedCantiereId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"cavi-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setCaviAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            children: \"Gestione Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"cavi-menu\",\n            anchorEl: caviAnchorEl,\n            keepMounted: true,\n            open: Boolean(caviAnchorEl),\n            onClose: () => handleMenuClose(setCaviAnchorEl),\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/visualizza'),\n              children: \"Visualizza Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: e => {\n                e.stopPropagation();\n                handleMenuOpen(e, setPosaAnchorEl);\n              },\n              sx: {\n                position: 'relative'\n              },\n              children: [\"Posa e Collegamenti \", /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n                fontSize: \"small\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: e => {\n                e.stopPropagation();\n                handleMenuOpen(e, setParcoAnchorEl);\n              },\n              sx: {\n                position: 'relative'\n              },\n              children: [\"Parco Cavi \", /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n                fontSize: \"small\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 32\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: e => {\n                e.stopPropagation();\n                handleMenuOpen(e, setExcelAnchorEl);\n              },\n              sx: {\n                position: 'relative'\n              },\n              children: [\"Gestione Excel \", /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n                fontSize: \"small\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 36\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: e => {\n                e.stopPropagation();\n                handleMenuOpen(e, setReportAnchorEl);\n              },\n              sx: {\n                position: 'relative'\n              },\n              children: [\"Report \", /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n                fontSize: \"small\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 28\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: e => {\n                e.stopPropagation();\n                handleMenuOpen(e, setCertificazioneAnchorEl);\n              },\n              sx: {\n                position: 'relative'\n              },\n              children: [\"Certificazione Cavi \", /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n                fontSize: \"small\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: e => {\n                e.stopPropagation();\n                handleMenuOpen(e, setComandeAnchorEl);\n              },\n              sx: {\n                position: 'relative'\n              },\n              children: [\"Gestione Comande \", /*#__PURE__*/_jsxDEV(ArrowDownIcon, {\n                fontSize: \"small\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 38\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"posa-menu\",\n            anchorEl: posaAnchorEl,\n            keepMounted: true,\n            open: Boolean(posaAnchorEl),\n            onClose: () => handleMenuClose(setPosaAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/inserisci-metri'),\n              children: \"Inserisci metri posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/modifica-cavo'),\n              children: \"Modifica cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/aggiungi-cavo'),\n              children: \"Aggiungi nuovo cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/elimina-cavo'),\n              children: \"Elimina cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/modifica-bobina'),\n              children: \"Modifica bobina cavo posato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/collegamenti'),\n              children: \"Gestisci collegamenti cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"parco-menu\",\n            anchorEl: parcoAnchorEl,\n            keepMounted: true,\n            open: Boolean(parcoAnchorEl),\n            onClose: () => handleMenuClose(setParcoAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/visualizza'),\n              children: \"Visualizza Bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/crea'),\n              children: \"Crea Nuova Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/modifica'),\n              children: \"Modifica Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/elimina'),\n              children: \"Elimina Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/storico'),\n              children: \"Storico Utilizzo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"excel-menu\",\n            anchorEl: excelAnchorEl,\n            keepMounted: true,\n            open: Boolean(excelAnchorEl),\n            onClose: () => handleMenuClose(setExcelAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/importa-cavi'),\n              children: \"Importa cavi da Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/importa-bobine'),\n              children: \"Importa parco bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/esporta-cavi'),\n              children: \"Esporta cavi in Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/esporta-bobine'),\n              children: \"Esporta parco bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"report-menu\",\n            anchorEl: reportAnchorEl,\n            keepMounted: true,\n            open: Boolean(reportAnchorEl),\n            onClose: () => handleMenuClose(setReportAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/avanzamento'),\n              children: \"Report Avanzamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/boq'),\n              children: \"Bill of Quantities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/utilizzo-bobine'),\n              children: \"Report Utilizzo Bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/statistiche'),\n              children: \"Statistiche Cantiere\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"certificazione-menu\",\n            anchorEl: certificazioneAnchorEl,\n            keepMounted: true,\n            open: Boolean(certificazioneAnchorEl),\n            onClose: () => handleMenuClose(setCertificazioneAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/visualizza'),\n              children: \"Visualizza certificazioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/filtra'),\n              children: \"Filtra per cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/crea'),\n              children: \"Crea certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/modifica'),\n              children: \"Modifica certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/elimina'),\n              children: \"Elimina certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/strumenti'),\n              children: \"Gestione strumenti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"comande-menu\",\n            anchorEl: comandeAnchorEl,\n            keepMounted: true,\n            open: Boolean(comandeAnchorEl),\n            onClose: () => handleMenuClose(setComandeAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/visualizza'),\n              children: \"Visualizza comande\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/crea'),\n              children: \"Crea nuova comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/modifica'),\n              children: \"Modifica comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/elimina'),\n              children: \"Elimina comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/stampa'),\n              children: \"Stampa comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/assegna'),\n              children: \"Assegna comanda a cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flexGrow: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [isImpersonating && impersonatedUser && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          sx: {\n            mr: 2\n          },\n          children: [\"Accesso come: \", /*#__PURE__*/_jsxDEV(\"b\", {\n            children: impersonatedUser.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mr: 2\n          },\n          children: (user === null || user === void 0 ? void 0 : user.username) || ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Logout\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            color: \"inherit\",\n            onClick: handleLogout,\n            edge: \"end\",\n            children: /*#__PURE__*/_jsxDEV(LogoutIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n};\n_s(TopNavbar, \"KcFPmHsEUY/2zSYWn487+PtaWNk=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = TopNavbar;\nexport default TopNavbar;\nvar _c;\n$RefreshReg$(_c, \"TopNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "AppBar", "<PERSON><PERSON><PERSON>", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Typography", "IconButton", "Divider", "Avatar", "<PERSON><PERSON><PERSON>", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "Logout", "LogoutIcon", "KeyboardArrowDown", "ArrowDownIcon", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TopNavbar", "_s", "navigate", "location", "user", "logout", "isImpersonating", "impersonated<PERSON><PERSON>", "homeAnchorEl", "setHomeAnchorEl", "adminAnchorEl", "setAdminAnchorEl", "cantieriAnchorEl", "setCantieriAnchorEl", "caviAnchorEl", "setCaviAnchorEl", "posaAnchorEl", "setPosaAnchorEl", "parcoAnchorEl", "setParcoAnchorEl", "excelAnchorEl", "setExcelAnchorEl", "reportAnchorEl", "setReportAnchorEl", "certificazioneAnchorEl", "setCertificazioneAnchorEl", "comandeAnchorEl", "setComandeAnchorEl", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "handleMenuOpen", "event", "setAnchorEl", "currentTarget", "handleMenuClose", "navigateTo", "path", "handleLogout", "isActive", "pathname", "isPartOfActive", "startsWith", "position", "color", "elevation", "sx", "zIndex", "className", "children", "variant", "onClick", "startIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mr", "orientation", "flexItem", "mx", "role", "e", "endIcon", "id", "anchorEl", "keepMounted", "open", "Boolean", "onClose", "username", "stopPropagation", "fontSize", "ml", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "flexGrow", "display", "alignItems", "title", "edge", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/TopNavbar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  AppBar,\n  Toolbar,\n  Box,\n  Button,\n  Menu,\n  MenuItem,\n  Typography,\n  IconButton,\n  Divider,\n  Avatar,\n  Tooltip\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon,\n  Logout as LogoutIcon,\n  KeyboardArrowDown as ArrowDownIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport './TopNavbar.css';\n\nconst TopNavbar = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, logout, isImpersonating, impersonatedUser } = useAuth();\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  const [posaAnchorEl, setPosaAnchorEl] = useState(null);\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n  const [reportAnchorEl, setReportAnchorEl] = useState(null);\n  const [certificazioneAnchorEl, setCertificazioneAnchorEl] = useState(null);\n  const [comandeAnchorEl, setComandeAnchorEl] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = (setAnchorEl) => {\n    setAnchorEl(null);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    handleMenuClose(setPosaAnchorEl);\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n    handleMenuClose(setReportAnchorEl);\n    handleMenuClose(setCertificazioneAnchorEl);\n    handleMenuClose(setComandeAnchorEl);\n  };\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  return (\n    <AppBar position=\"static\" color=\"default\" elevation={1} sx={{ zIndex: 1100 }} className=\"excel-style-menu\">\n      <Toolbar variant=\"dense\">\n        {/* Logo/Home */}\n        <Button\n          color=\"inherit\"\n          onClick={() => navigateTo('/dashboard')}\n          startIcon={<HomeIcon />}\n          sx={{ mr: 1 }}\n          className={isActive('/dashboard') ? 'active-button' : ''}\n        >\n          CMS\n        </Button>\n        <Divider orientation=\"vertical\" flexItem sx={{ mx: 0.5 }} />\n\n        {/* Menu Amministratore (solo per admin) */}\n        {user?.role === 'owner' && (\n          <>\n            <Button\n              color=\"inherit\"\n              aria-controls=\"admin-menu\"\n              aria-haspopup=\"true\"\n              onClick={(e) => handleMenuOpen(e, setAdminAnchorEl)}\n              endIcon={<ArrowDownIcon />}\n              sx={{ mr: 1 }}\n              className={isPartOfActive('/dashboard/admin') ? 'active-button' : ''}\n            >\n              Amministrazione\n            </Button>\n            <Menu\n              id=\"admin-menu\"\n              anchorEl={adminAnchorEl}\n              keepMounted\n              open={Boolean(adminAnchorEl)}\n              onClose={() => handleMenuClose(setAdminAnchorEl)}\n            >\n              <MenuItem onClick={() => navigateTo('/dashboard/admin')}>Pannello Admin</MenuItem>\n            </Menu>\n          </>\n        )}\n\n        {/* Menu per utenti standard e cantieri */}\n        {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (\n          <>\n            <Button\n              color=\"inherit\"\n              aria-controls=\"cantieri-menu\"\n              aria-haspopup=\"true\"\n              onClick={(e) => handleMenuOpen(e, setCantieriAnchorEl)}\n              endIcon={<ArrowDownIcon />}\n              sx={{ mr: 1 }}\n            >\n              {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"}\n            </Button>\n            <Menu\n              id=\"cantieri-menu\"\n              anchorEl={cantieriAnchorEl}\n              keepMounted\n              open={Boolean(cantieriAnchorEl)}\n              onClose={() => handleMenuClose(setCantieriAnchorEl)}\n            >\n              <MenuItem\n                onClick={() => navigateTo('/dashboard/cantieri')}\n                className={isActive('/dashboard/cantieri') ? 'active-item' : ''}\n              >Lista Cantieri</MenuItem>\n              {selectedCantiereId && (\n                <MenuItem\n                  onClick={() => navigateTo(`/dashboard/cantieri/${selectedCantiereId}`)}\n                  className={isActive(`/dashboard/cantieri/${selectedCantiereId}`) ? 'active-item' : ''}\n                >\n                  Cantiere: {selectedCantiereName || selectedCantiereId}\n                </MenuItem>\n              )}\n            </Menu>\n\n            {/* Menu Cavi con sottomenu - visibile solo se un cantiere è selezionato */}\n            {selectedCantiereId && (\n              <>\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"cavi-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setCaviAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                >\n                  Gestione Cavi\n                </Button>\n                <Menu\n                  id=\"cavi-menu\"\n                  anchorEl={caviAnchorEl}\n                  keepMounted\n                  open={Boolean(caviAnchorEl)}\n                  onClose={() => handleMenuClose(setCaviAnchorEl)}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/visualizza')}>Visualizza Cavi</MenuItem>\n\n                  {/* Posa e Collegamenti */}\n                  <MenuItem\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleMenuOpen(e, setPosaAnchorEl);\n                    }}\n                    sx={{ position: 'relative' }}\n                  >\n                    Posa e Collegamenti <ArrowDownIcon fontSize=\"small\" sx={{ ml: 1 }} />\n                  </MenuItem>\n\n                  {/* Parco Cavi */}\n                  <MenuItem\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleMenuOpen(e, setParcoAnchorEl);\n                    }}\n                    sx={{ position: 'relative' }}\n                  >\n                    Parco Cavi <ArrowDownIcon fontSize=\"small\" sx={{ ml: 1 }} />\n                  </MenuItem>\n\n                  {/* Gestione Excel */}\n                  <MenuItem\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleMenuOpen(e, setExcelAnchorEl);\n                    }}\n                    sx={{ position: 'relative' }}\n                  >\n                    Gestione Excel <ArrowDownIcon fontSize=\"small\" sx={{ ml: 1 }} />\n                  </MenuItem>\n\n                  {/* Report */}\n                  <MenuItem\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleMenuOpen(e, setReportAnchorEl);\n                    }}\n                    sx={{ position: 'relative' }}\n                  >\n                    Report <ArrowDownIcon fontSize=\"small\" sx={{ ml: 1 }} />\n                  </MenuItem>\n\n                  {/* Certificazione Cavi */}\n                  <MenuItem\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleMenuOpen(e, setCertificazioneAnchorEl);\n                    }}\n                    sx={{ position: 'relative' }}\n                  >\n                    Certificazione Cavi <ArrowDownIcon fontSize=\"small\" sx={{ ml: 1 }} />\n                  </MenuItem>\n\n                  {/* Gestione Comande */}\n                  <MenuItem\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleMenuOpen(e, setComandeAnchorEl);\n                    }}\n                    sx={{ position: 'relative' }}\n                  >\n                    Gestione Comande <ArrowDownIcon fontSize=\"small\" sx={{ ml: 1 }} />\n                  </MenuItem>\n                </Menu>\n\n                {/* Sottomenu Posa e Collegamenti */}\n                <Menu\n                  id=\"posa-menu\"\n                  anchorEl={posaAnchorEl}\n                  keepMounted\n                  open={Boolean(posaAnchorEl)}\n                  onClose={() => handleMenuClose(setPosaAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/inserisci-metri')}>Inserisci metri posati</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/modifica-cavo')}>Modifica cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/aggiungi-cavo')}>Aggiungi nuovo cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/elimina-cavo')}>Elimina cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/modifica-bobina')}>Modifica bobina cavo posato</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/collegamenti')}>Gestisci collegamenti cavo</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Parco Cavi */}\n                <Menu\n                  id=\"parco-menu\"\n                  anchorEl={parcoAnchorEl}\n                  keepMounted\n                  open={Boolean(parcoAnchorEl)}\n                  onClose={() => handleMenuClose(setParcoAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/visualizza')}>Visualizza Bobine</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/crea')}>Crea Nuova Bobina</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/modifica')}>Modifica Bobina</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/elimina')}>Elimina Bobina</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/storico')}>Storico Utilizzo</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Gestione Excel */}\n                <Menu\n                  id=\"excel-menu\"\n                  anchorEl={excelAnchorEl}\n                  keepMounted\n                  open={Boolean(excelAnchorEl)}\n                  onClose={() => handleMenuClose(setExcelAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/importa-cavi')}>Importa cavi da Excel</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/importa-bobine')}>Importa parco bobine</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/esporta-cavi')}>Esporta cavi in Excel</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/esporta-bobine')}>Esporta parco bobine</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Report */}\n                <Menu\n                  id=\"report-menu\"\n                  anchorEl={reportAnchorEl}\n                  keepMounted\n                  open={Boolean(reportAnchorEl)}\n                  onClose={() => handleMenuClose(setReportAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/avanzamento')}>Report Avanzamento</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/boq')}>Bill of Quantities</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/utilizzo-bobine')}>Report Utilizzo Bobine</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/statistiche')}>Statistiche Cantiere</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Certificazione Cavi */}\n                <Menu\n                  id=\"certificazione-menu\"\n                  anchorEl={certificazioneAnchorEl}\n                  keepMounted\n                  open={Boolean(certificazioneAnchorEl)}\n                  onClose={() => handleMenuClose(setCertificazioneAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/visualizza')}>Visualizza certificazioni</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/filtra')}>Filtra per cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/crea')}>Crea certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/modifica')}>Modifica certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/elimina')}>Elimina certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/strumenti')}>Gestione strumenti</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Gestione Comande */}\n                <Menu\n                  id=\"comande-menu\"\n                  anchorEl={comandeAnchorEl}\n                  keepMounted\n                  open={Boolean(comandeAnchorEl)}\n                  onClose={() => handleMenuClose(setComandeAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/visualizza')}>Visualizza comande</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/crea')}>Crea nuova comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/modifica')}>Modifica comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/elimina')}>Elimina comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/stampa')}>Stampa comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/assegna')}>Assegna comanda a cavo</MenuItem>\n                </Menu>\n              </>\n            )}\n          </>\n        )}\n\n        {/* Spacer */}\n        <Box sx={{ flexGrow: 1 }} />\n\n        {/* Informazioni utente e logout */}\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          {isImpersonating && impersonatedUser && (\n            <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mr: 2 }}>\n              Accesso come: <b>{impersonatedUser.username}</b>\n            </Typography>\n          )}\n          <Typography variant=\"body2\" sx={{ mr: 2 }}>\n            {user?.username || ''}\n          </Typography>\n          <Tooltip title=\"Logout\">\n            <IconButton color=\"inherit\" onClick={handleLogout} edge=\"end\">\n              <LogoutIcon />\n            </IconButton>\n          </Tooltip>\n        </Box>\n      </Toolbar>\n    </AppBar>\n  );\n};\n\nexport default TopNavbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,MAAM,IAAIC,UAAU,EACpBC,iBAAiB,IAAIC,aAAa,QAC7B,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAMmC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmC,IAAI;IAAEC,MAAM;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGZ,OAAO,CAAC,CAAC;;EAErE;EACA,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2C,aAAa,EAAEC,gBAAgB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC6C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACyD,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC1E,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM6D,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEzE;EACA,MAAME,cAAc,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;IAC7CA,WAAW,CAACD,KAAK,CAACE,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAIF,WAAW,IAAK;IACvCA,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAIC,IAAI,IAAK;IAC3B;IACA;IACA,IAAIA,IAAI,KAAK,YAAY,IAAIhC,eAAe,EAAE;MAC5CJ,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM;MACLA,QAAQ,CAACoC,IAAI,CAAC;IAChB;;IAEA;IACAF,eAAe,CAAC3B,eAAe,CAAC;IAChC2B,eAAe,CAACzB,gBAAgB,CAAC;IACjCyB,eAAe,CAACvB,mBAAmB,CAAC;IACpCuB,eAAe,CAACrB,eAAe,CAAC;IAChCqB,eAAe,CAACnB,eAAe,CAAC;IAChCmB,eAAe,CAACjB,gBAAgB,CAAC;IACjCiB,eAAe,CAACf,gBAAgB,CAAC;IACjCe,eAAe,CAACb,iBAAiB,CAAC;IAClCa,eAAe,CAACX,yBAAyB,CAAC;IAC1CW,eAAe,CAACT,kBAAkB,CAAC;EACrC,CAAC;EAED,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzBlC,MAAM,CAAC,CAAC;EACV,CAAC;;EAED;EACA,MAAMmC,QAAQ,GAAIF,IAAI,IAAK;IACzB,OAAOnC,QAAQ,CAACsC,QAAQ,KAAKH,IAAI;EACnC,CAAC;;EAED;EACA,MAAMI,cAAc,GAAIJ,IAAI,IAAK;IAC/B,OAAOnC,QAAQ,CAACsC,QAAQ,CAACE,UAAU,CAACL,IAAI,CAAC;EAC3C,CAAC;EAED,oBACEzC,OAAA,CAAC3B,MAAM;IAAC0E,QAAQ,EAAC,QAAQ;IAACC,KAAK,EAAC,SAAS;IAACC,SAAS,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAK,CAAE;IAACC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eACxGrD,OAAA,CAAC1B,OAAO;MAACgF,OAAO,EAAC,OAAO;MAAAD,QAAA,gBAEtBrD,OAAA,CAACxB,MAAM;QACLwE,KAAK,EAAC,SAAS;QACfO,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,YAAY,CAAE;QACxCgB,SAAS,eAAExD,OAAA,CAACf,QAAQ;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBV,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QACdT,SAAS,EAAET,QAAQ,CAAC,YAAY,CAAC,GAAG,eAAe,GAAG,EAAG;QAAAU,QAAA,EAC1D;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT5D,OAAA,CAACnB,OAAO;QAACiF,WAAW,EAAC,UAAU;QAACC,QAAQ;QAACb,EAAE,EAAE;UAAEc,EAAE,EAAE;QAAI;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAG3D,CAAArD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,IAAI,MAAK,OAAO,iBACrBjE,OAAA,CAAAE,SAAA;QAAAmD,QAAA,gBACErD,OAAA,CAACxB,MAAM;UACLwE,KAAK,EAAC,SAAS;UACf,iBAAc,YAAY;UAC1B,iBAAc,MAAM;UACpBO,OAAO,EAAGW,CAAC,IAAK/B,cAAc,CAAC+B,CAAC,EAAEpD,gBAAgB,CAAE;UACpDqD,OAAO,eAAEnE,OAAA,CAACH,aAAa;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BV,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UACdT,SAAS,EAAEP,cAAc,CAAC,kBAAkB,CAAC,GAAG,eAAe,GAAG,EAAG;UAAAQ,QAAA,EACtE;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5D,OAAA,CAACvB,IAAI;UACH2F,EAAE,EAAC,YAAY;UACfC,QAAQ,EAAExD,aAAc;UACxByD,WAAW;UACXC,IAAI,EAAEC,OAAO,CAAC3D,aAAa,CAAE;UAC7B4D,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAACzB,gBAAgB,CAAE;UAAAuC,QAAA,eAEjDrD,OAAA,CAACtB,QAAQ;YAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,kBAAkB,CAAE;YAAAa,QAAA,EAAC;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC;MAAA,eACP,CACH,EAGA,CAAC,CAAArD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,IAAI,MAAK,OAAO,IAAK,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,IAAI,MAAK,OAAO,IAAIxD,eAAe,IAAIC,gBAAiB,kBACzFV,OAAA,CAAAE,SAAA;QAAAmD,QAAA,gBACErD,OAAA,CAACxB,MAAM;UACLwE,KAAK,EAAC,SAAS;UACf,iBAAc,eAAe;UAC7B,iBAAc,MAAM;UACpBO,OAAO,EAAGW,CAAC,IAAK/B,cAAc,CAAC+B,CAAC,EAAElD,mBAAmB,CAAE;UACvDmD,OAAO,eAAEnE,OAAA,CAACH,aAAa;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BV,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EAEb5C,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAACgE,QAAQ,EAAE,GAAG;QAAiB;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC,eACT5D,OAAA,CAACvB,IAAI;UACH2F,EAAE,EAAC,eAAe;UAClBC,QAAQ,EAAEtD,gBAAiB;UAC3BuD,WAAW;UACXC,IAAI,EAAEC,OAAO,CAACzD,gBAAgB,CAAE;UAChC0D,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAACvB,mBAAmB,CAAE;UAAAqC,QAAA,gBAEpDrD,OAAA,CAACtB,QAAQ;YACP6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,qBAAqB,CAAE;YACjDY,SAAS,EAAET,QAAQ,CAAC,qBAAqB,CAAC,GAAG,aAAa,GAAG,EAAG;YAAAU,QAAA,EACjE;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,EACzB7B,kBAAkB,iBACjB/B,OAAA,CAACtB,QAAQ;YACP6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,uBAAuBT,kBAAkB,EAAE,CAAE;YACvEqB,SAAS,EAAET,QAAQ,CAAC,uBAAuBZ,kBAAkB,EAAE,CAAC,GAAG,aAAa,GAAG,EAAG;YAAAsB,QAAA,GACvF,YACW,EAACnB,oBAAoB,IAAIH,kBAAkB;UAAA;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CACX;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,EAGN7B,kBAAkB,iBACjB/B,OAAA,CAAAE,SAAA;UAAAmD,QAAA,gBACErD,OAAA,CAACxB,MAAM;YACLwE,KAAK,EAAC,SAAS;YACf,iBAAc,WAAW;YACzB,iBAAc,MAAM;YACpBO,OAAO,EAAGW,CAAC,IAAK/B,cAAc,CAAC+B,CAAC,EAAEhD,eAAe,CAAE;YACnDiD,OAAO,eAAEnE,OAAA,CAACH,aAAa;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BV,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,EACf;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5D,OAAA,CAACvB,IAAI;YACH2F,EAAE,EAAC,WAAW;YACdC,QAAQ,EAAEpD,YAAa;YACvBqD,WAAW;YACXC,IAAI,EAAEC,OAAO,CAACvD,YAAY,CAAE;YAC5BwD,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAACrB,eAAe,CAAE;YAAAmC,QAAA,gBAEhDrD,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,4BAA4B,CAAE;cAAAa,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAG7F5D,OAAA,CAACtB,QAAQ;cACP6E,OAAO,EAAGW,CAAC,IAAK;gBACdA,CAAC,CAACS,eAAe,CAAC,CAAC;gBACnBxC,cAAc,CAAC+B,CAAC,EAAE9C,eAAe,CAAC;cACpC,CAAE;cACF8B,EAAE,EAAE;gBAAEH,QAAQ,EAAE;cAAW,CAAE;cAAAM,QAAA,GAC9B,sBACqB,eAAArD,OAAA,CAACH,aAAa;gBAAC+E,QAAQ,EAAC,OAAO;gBAAC1B,EAAE,EAAE;kBAAE2B,EAAE,EAAE;gBAAE;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eAGX5D,OAAA,CAACtB,QAAQ;cACP6E,OAAO,EAAGW,CAAC,IAAK;gBACdA,CAAC,CAACS,eAAe,CAAC,CAAC;gBACnBxC,cAAc,CAAC+B,CAAC,EAAE5C,gBAAgB,CAAC;cACrC,CAAE;cACF4B,EAAE,EAAE;gBAAEH,QAAQ,EAAE;cAAW,CAAE;cAAAM,QAAA,GAC9B,aACY,eAAArD,OAAA,CAACH,aAAa;gBAAC+E,QAAQ,EAAC,OAAO;gBAAC1B,EAAE,EAAE;kBAAE2B,EAAE,EAAE;gBAAE;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eAGX5D,OAAA,CAACtB,QAAQ;cACP6E,OAAO,EAAGW,CAAC,IAAK;gBACdA,CAAC,CAACS,eAAe,CAAC,CAAC;gBACnBxC,cAAc,CAAC+B,CAAC,EAAE1C,gBAAgB,CAAC;cACrC,CAAE;cACF0B,EAAE,EAAE;gBAAEH,QAAQ,EAAE;cAAW,CAAE;cAAAM,QAAA,GAC9B,iBACgB,eAAArD,OAAA,CAACH,aAAa;gBAAC+E,QAAQ,EAAC,OAAO;gBAAC1B,EAAE,EAAE;kBAAE2B,EAAE,EAAE;gBAAE;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eAGX5D,OAAA,CAACtB,QAAQ;cACP6E,OAAO,EAAGW,CAAC,IAAK;gBACdA,CAAC,CAACS,eAAe,CAAC,CAAC;gBACnBxC,cAAc,CAAC+B,CAAC,EAAExC,iBAAiB,CAAC;cACtC,CAAE;cACFwB,EAAE,EAAE;gBAAEH,QAAQ,EAAE;cAAW,CAAE;cAAAM,QAAA,GAC9B,SACQ,eAAArD,OAAA,CAACH,aAAa;gBAAC+E,QAAQ,EAAC,OAAO;gBAAC1B,EAAE,EAAE;kBAAE2B,EAAE,EAAE;gBAAE;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eAGX5D,OAAA,CAACtB,QAAQ;cACP6E,OAAO,EAAGW,CAAC,IAAK;gBACdA,CAAC,CAACS,eAAe,CAAC,CAAC;gBACnBxC,cAAc,CAAC+B,CAAC,EAAEtC,yBAAyB,CAAC;cAC9C,CAAE;cACFsB,EAAE,EAAE;gBAAEH,QAAQ,EAAE;cAAW,CAAE;cAAAM,QAAA,GAC9B,sBACqB,eAAArD,OAAA,CAACH,aAAa;gBAAC+E,QAAQ,EAAC,OAAO;gBAAC1B,EAAE,EAAE;kBAAE2B,EAAE,EAAE;gBAAE;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eAGX5D,OAAA,CAACtB,QAAQ;cACP6E,OAAO,EAAGW,CAAC,IAAK;gBACdA,CAAC,CAACS,eAAe,CAAC,CAAC;gBACnBxC,cAAc,CAAC+B,CAAC,EAAEpC,kBAAkB,CAAC;cACvC,CAAE;cACFoB,EAAE,EAAE;gBAAEH,QAAQ,EAAE;cAAW,CAAE;cAAAM,QAAA,GAC9B,mBACkB,eAAArD,OAAA,CAACH,aAAa;gBAAC+E,QAAQ,EAAC,OAAO;gBAAC1B,EAAE,EAAE;kBAAE2B,EAAE,EAAE;gBAAE;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAGP5D,OAAA,CAACvB,IAAI;YACH2F,EAAE,EAAC,WAAW;YACdC,QAAQ,EAAElD,YAAa;YACvBmD,WAAW;YACXC,IAAI,EAAEC,OAAO,CAACrD,YAAY,CAAE;YAC5BsD,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAACnB,eAAe,CAAE;YAChD0D,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACF5B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BrD,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,sCAAsC,CAAE;cAAAa,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC9G5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,oCAAoC,CAAE;cAAAa,QAAA,EAAC;YAAa;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACnG5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,oCAAoC,CAAE;cAAAa,QAAA,EAAC;YAAmB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACzG5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,mCAAmC,CAAE;cAAAa,QAAA,EAAC;YAAY;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACjG5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,sCAAsC,CAAE;cAAAa,QAAA,EAAC;YAA2B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACnH5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,mCAAmC,CAAE;cAAAa,QAAA,EAAC;YAA0B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G,CAAC,eAGP5D,OAAA,CAACvB,IAAI;YACH2F,EAAE,EAAC,YAAY;YACfC,QAAQ,EAAEhD,aAAc;YACxBiD,WAAW;YACXC,IAAI,EAAEC,OAAO,CAACnD,aAAa,CAAE;YAC7BoD,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAACjB,gBAAgB,CAAE;YACjDwD,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACF5B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BrD,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,kCAAkC,CAAE;cAAAa,QAAA,EAAC;YAAiB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACrG5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,4BAA4B,CAAE;cAAAa,QAAA,EAAC;YAAiB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC/F5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,gCAAgC,CAAE;cAAAa,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACjG5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,+BAA+B,CAAE;cAAAa,QAAA,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC/F5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,+BAA+B,CAAE;cAAAa,QAAA,EAAC;YAAgB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,eAGP5D,OAAA,CAACvB,IAAI;YACH2F,EAAE,EAAC,YAAY;YACfC,QAAQ,EAAE9C,aAAc;YACxB+C,WAAW;YACXC,IAAI,EAAEC,OAAO,CAACjD,aAAa,CAAE;YAC7BkD,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAACf,gBAAgB,CAAE;YACjDsD,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACF5B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BrD,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,oCAAoC,CAAE;cAAAa,QAAA,EAAC;YAAqB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC3G5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,sCAAsC,CAAE;cAAAa,QAAA,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC5G5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,oCAAoC,CAAE;cAAAa,QAAA,EAAC;YAAqB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC3G5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,sCAAsC,CAAE;cAAAa,QAAA,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxG,CAAC,eAGP5D,OAAA,CAACvB,IAAI;YACH2F,EAAE,EAAC,aAAa;YAChBC,QAAQ,EAAE5C,cAAe;YACzB6C,WAAW;YACXC,IAAI,EAAEC,OAAO,CAAC/C,cAAc,CAAE;YAC9BgD,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAACb,iBAAiB,CAAE;YAClDoD,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACF5B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BrD,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,oCAAoC,CAAE;cAAAa,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxG5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,4BAA4B,CAAE;cAAAa,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChG5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,wCAAwC,CAAE;cAAAa,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChH5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,oCAAoC,CAAE;cAAAa,QAAA,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC,eAGP5D,OAAA,CAACvB,IAAI;YACH2F,EAAE,EAAC,qBAAqB;YACxBC,QAAQ,EAAE1C,sBAAuB;YACjC2C,WAAW;YACXC,IAAI,EAAEC,OAAO,CAAC7C,sBAAsB,CAAE;YACtC8C,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAACX,yBAAyB,CAAE;YAC1DkD,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACF5B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BrD,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,2CAA2C,CAAE;cAAAa,QAAA,EAAC;YAAyB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtH5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,uCAAuC,CAAE;cAAAa,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxG5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,qCAAqC,CAAE;cAAAa,QAAA,EAAC;YAAmB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1G5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,yCAAyC,CAAE;cAAAa,QAAA,EAAC;YAAuB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClH5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,wCAAwC,CAAE;cAAAa,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChH5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,0CAA0C,CAAE;cAAAa,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CAAC,eAGP5D,OAAA,CAACvB,IAAI;YACH2F,EAAE,EAAC,cAAc;YACjBC,QAAQ,EAAExC,eAAgB;YAC1ByC,WAAW;YACXC,IAAI,EAAEC,OAAO,CAAC3C,eAAe,CAAE;YAC/B4C,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAACT,kBAAkB,CAAE;YACnDgD,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACF5B,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BrD,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,oCAAoC,CAAE;cAAAa,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxG5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,8BAA8B,CAAE;cAAAa,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClG5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,kCAAkC,CAAE;cAAAa,QAAA,EAAC;YAAgB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpG5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,iCAAiC,CAAE;cAAAa,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClG5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,gCAAgC,CAAE;cAAAa,QAAA,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChG5D,OAAA,CAACtB,QAAQ;cAAC6E,OAAO,EAAEA,CAAA,KAAMf,UAAU,CAAC,iCAAiC,CAAE;cAAAa,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC;QAAA,eACP,CACH;MAAA,eACD,CACH,eAGD5D,OAAA,CAACzB,GAAG;QAAC2E,EAAE,EAAE;UAAEgC,QAAQ,EAAE;QAAE;MAAE;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG5B5D,OAAA,CAACzB,GAAG;QAAC2E,EAAE,EAAE;UAAEiC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAA/B,QAAA,GAChD5C,eAAe,IAAIC,gBAAgB,iBAClCV,OAAA,CAACrB,UAAU;UAAC2E,OAAO,EAAC,OAAO;UAACN,KAAK,EAAC,eAAe;UAACE,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,GAAC,gBACjD,eAAArD,OAAA;YAAAqD,QAAA,EAAI3C,gBAAgB,CAACgE;UAAQ;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACb,eACD5D,OAAA,CAACrB,UAAU;UAAC2E,OAAO,EAAC,OAAO;UAACJ,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EACvC,CAAA9C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,QAAQ,KAAI;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACb5D,OAAA,CAACjB,OAAO;UAACsG,KAAK,EAAC,QAAQ;UAAAhC,QAAA,eACrBrD,OAAA,CAACpB,UAAU;YAACoE,KAAK,EAAC,SAAS;YAACO,OAAO,EAAEb,YAAa;YAAC4C,IAAI,EAAC,KAAK;YAAAjC,QAAA,eAC3DrD,OAAA,CAACL,UAAU;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEb,CAAC;AAACxD,EAAA,CAlZID,SAAS;EAAA,QACIhC,WAAW,EACXC,WAAW,EACgC0B,OAAO;AAAA;AAAAyF,EAAA,GAH/DpF,SAAS;AAoZf,eAAeA,SAAS;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}