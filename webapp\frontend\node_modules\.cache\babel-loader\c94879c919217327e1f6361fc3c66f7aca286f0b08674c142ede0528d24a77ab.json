{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"icon\", \"open\", \"openIcon\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport AddIcon from '../internal/svg-icons/Add';\nimport speedDialIconClasses, { getSpeedDialIconUtilityClass } from './speedDialIconClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    open,\n    openIcon\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    icon: ['icon', open && 'iconOpen', openIcon && open && 'iconWithOpenIconOpen'],\n    openIcon: ['openIcon', open && 'openIconOpen']\n  };\n  return composeClasses(slots, getSpeedDialIconUtilityClass, classes);\n};\nconst SpeedDialIconRoot = styled('span', {\n  name: 'MuiSpeedDialIcon',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${speedDialIconClasses.icon}`]: styles.icon\n    }, {\n      [`& .${speedDialIconClasses.icon}`]: ownerState.open && styles.iconOpen\n    }, {\n      [`& .${speedDialIconClasses.icon}`]: ownerState.open && ownerState.openIcon && styles.iconWithOpenIconOpen\n    }, {\n      [`& .${speedDialIconClasses.openIcon}`]: styles.openIcon\n    }, {\n      [`& .${speedDialIconClasses.openIcon}`]: ownerState.open && styles.openIconOpen\n    }, styles.root];\n  }\n})(({\n  theme,\n  ownerState\n}) => ({\n  height: 24,\n  [`& .${speedDialIconClasses.icon}`]: _extends({\n    transition: theme.transitions.create(['transform', 'opacity'], {\n      duration: theme.transitions.duration.short\n    })\n  }, ownerState.open && _extends({\n    transform: 'rotate(45deg)'\n  }, ownerState.openIcon && {\n    opacity: 0\n  })),\n  [`& .${speedDialIconClasses.openIcon}`]: _extends({\n    position: 'absolute',\n    transition: theme.transitions.create(['transform', 'opacity'], {\n      duration: theme.transitions.duration.short\n    }),\n    opacity: 0,\n    transform: 'rotate(-45deg)'\n  }, ownerState.open && {\n    transform: 'rotate(0deg)',\n    opacity: 1\n  })\n}));\nconst SpeedDialIcon = /*#__PURE__*/React.forwardRef(function SpeedDialIcon(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSpeedDialIcon'\n  });\n  const {\n      className,\n      icon: iconProp,\n      openIcon: openIconProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  function formatIcon(icon, newClassName) {\n    if (/*#__PURE__*/React.isValidElement(icon)) {\n      return /*#__PURE__*/React.cloneElement(icon, {\n        className: newClassName\n      });\n    }\n    return icon;\n  }\n  return /*#__PURE__*/_jsxs(SpeedDialIconRoot, _extends({\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: [openIconProp ? formatIcon(openIconProp, classes.openIcon) : null, iconProp ? formatIcon(iconProp, classes.icon) : /*#__PURE__*/_jsx(AddIcon, {\n      className: classes.icon\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? SpeedDialIcon.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon to display.\n   */\n  icon: PropTypes.node,\n  /**\n   * @ignore\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * The icon to display in the SpeedDial Floating Action Button when the SpeedDial is open.\n   */\n  openIcon: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nSpeedDialIcon.muiName = 'SpeedDialIcon';\nexport default SpeedDialIcon;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "AddIcon", "speedDialIconClasses", "getSpeedDialIconUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "open", "openIcon", "slots", "root", "icon", "SpeedDialIconRoot", "name", "slot", "overridesResolver", "props", "styles", "iconOpen", "iconWithOpenIconOpen", "openIconOpen", "theme", "height", "transition", "transitions", "create", "duration", "short", "transform", "opacity", "position", "SpeedDialIcon", "forwardRef", "inProps", "ref", "className", "iconProp", "openIconProp", "other", "formatIcon", "newClassName", "isValidElement", "cloneElement", "children", "process", "env", "NODE_ENV", "propTypes", "object", "string", "node", "bool", "sx", "oneOfType", "arrayOf", "func", "mui<PERSON><PERSON>"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/material/SpeedDialIcon/SpeedDialIcon.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"icon\", \"open\", \"openIcon\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport AddIcon from '../internal/svg-icons/Add';\nimport speedDialIconClasses, { getSpeedDialIconUtilityClass } from './speedDialIconClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    open,\n    openIcon\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    icon: ['icon', open && 'iconOpen', openIcon && open && 'iconWithOpenIconOpen'],\n    openIcon: ['openIcon', open && 'openIconOpen']\n  };\n  return composeClasses(slots, getSpeedDialIconUtilityClass, classes);\n};\nconst SpeedDialIconRoot = styled('span', {\n  name: 'MuiSpeedDialIcon',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${speedDialIconClasses.icon}`]: styles.icon\n    }, {\n      [`& .${speedDialIconClasses.icon}`]: ownerState.open && styles.iconOpen\n    }, {\n      [`& .${speedDialIconClasses.icon}`]: ownerState.open && ownerState.openIcon && styles.iconWithOpenIconOpen\n    }, {\n      [`& .${speedDialIconClasses.openIcon}`]: styles.openIcon\n    }, {\n      [`& .${speedDialIconClasses.openIcon}`]: ownerState.open && styles.openIconOpen\n    }, styles.root];\n  }\n})(({\n  theme,\n  ownerState\n}) => ({\n  height: 24,\n  [`& .${speedDialIconClasses.icon}`]: _extends({\n    transition: theme.transitions.create(['transform', 'opacity'], {\n      duration: theme.transitions.duration.short\n    })\n  }, ownerState.open && _extends({\n    transform: 'rotate(45deg)'\n  }, ownerState.openIcon && {\n    opacity: 0\n  })),\n  [`& .${speedDialIconClasses.openIcon}`]: _extends({\n    position: 'absolute',\n    transition: theme.transitions.create(['transform', 'opacity'], {\n      duration: theme.transitions.duration.short\n    }),\n    opacity: 0,\n    transform: 'rotate(-45deg)'\n  }, ownerState.open && {\n    transform: 'rotate(0deg)',\n    opacity: 1\n  })\n}));\nconst SpeedDialIcon = /*#__PURE__*/React.forwardRef(function SpeedDialIcon(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSpeedDialIcon'\n  });\n  const {\n      className,\n      icon: iconProp,\n      openIcon: openIconProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  function formatIcon(icon, newClassName) {\n    if ( /*#__PURE__*/React.isValidElement(icon)) {\n      return /*#__PURE__*/React.cloneElement(icon, {\n        className: newClassName\n      });\n    }\n    return icon;\n  }\n  return /*#__PURE__*/_jsxs(SpeedDialIconRoot, _extends({\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: [openIconProp ? formatIcon(openIconProp, classes.openIcon) : null, iconProp ? formatIcon(iconProp, classes.icon) : /*#__PURE__*/_jsx(AddIcon, {\n      className: classes.icon\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? SpeedDialIcon.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon to display.\n   */\n  icon: PropTypes.node,\n  /**\n   * @ignore\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * The icon to display in the SpeedDial Floating Action Button when the SpeedDial is open.\n   */\n  openIcon: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nSpeedDialIcon.muiName = 'SpeedDialIcon';\nexport default SpeedDialIcon;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC;AAC3D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,OAAO,MAAM,2BAA2B;AAC/C,OAAOC,oBAAoB,IAAIC,4BAA4B,QAAQ,wBAAwB;AAC3F,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,IAAI;IACJC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,IAAI,EAAE,CAAC,MAAM,EAAEJ,IAAI,IAAI,UAAU,EAAEC,QAAQ,IAAID,IAAI,IAAI,sBAAsB,CAAC;IAC9EC,QAAQ,EAAE,CAAC,UAAU,EAAED,IAAI,IAAI,cAAc;EAC/C,CAAC;EACD,OAAOb,cAAc,CAACe,KAAK,EAAEV,4BAA4B,EAAEO,OAAO,CAAC;AACrE,CAAC;AACD,MAAMM,iBAAiB,GAAGjB,MAAM,CAAC,MAAM,EAAE;EACvCkB,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJZ;IACF,CAAC,GAAGW,KAAK;IACT,OAAO,CAAC;MACN,CAAC,MAAMlB,oBAAoB,CAACa,IAAI,EAAE,GAAGM,MAAM,CAACN;IAC9C,CAAC,EAAE;MACD,CAAC,MAAMb,oBAAoB,CAACa,IAAI,EAAE,GAAGN,UAAU,CAACE,IAAI,IAAIU,MAAM,CAACC;IACjE,CAAC,EAAE;MACD,CAAC,MAAMpB,oBAAoB,CAACa,IAAI,EAAE,GAAGN,UAAU,CAACE,IAAI,IAAIF,UAAU,CAACG,QAAQ,IAAIS,MAAM,CAACE;IACxF,CAAC,EAAE;MACD,CAAC,MAAMrB,oBAAoB,CAACU,QAAQ,EAAE,GAAGS,MAAM,CAACT;IAClD,CAAC,EAAE;MACD,CAAC,MAAMV,oBAAoB,CAACU,QAAQ,EAAE,GAAGH,UAAU,CAACE,IAAI,IAAIU,MAAM,CAACG;IACrE,CAAC,EAAEH,MAAM,CAACP,IAAI,CAAC;EACjB;AACF,CAAC,CAAC,CAAC,CAAC;EACFW,KAAK;EACLhB;AACF,CAAC,MAAM;EACLiB,MAAM,EAAE,EAAE;EACV,CAAC,MAAMxB,oBAAoB,CAACa,IAAI,EAAE,GAAGtB,QAAQ,CAAC;IAC5CkC,UAAU,EAAEF,KAAK,CAACG,WAAW,CAACC,MAAM,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC,EAAE;MAC7DC,QAAQ,EAAEL,KAAK,CAACG,WAAW,CAACE,QAAQ,CAACC;IACvC,CAAC;EACH,CAAC,EAAEtB,UAAU,CAACE,IAAI,IAAIlB,QAAQ,CAAC;IAC7BuC,SAAS,EAAE;EACb,CAAC,EAAEvB,UAAU,CAACG,QAAQ,IAAI;IACxBqB,OAAO,EAAE;EACX,CAAC,CAAC,CAAC;EACH,CAAC,MAAM/B,oBAAoB,CAACU,QAAQ,EAAE,GAAGnB,QAAQ,CAAC;IAChDyC,QAAQ,EAAE,UAAU;IACpBP,UAAU,EAAEF,KAAK,CAACG,WAAW,CAACC,MAAM,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC,EAAE;MAC7DC,QAAQ,EAAEL,KAAK,CAACG,WAAW,CAACE,QAAQ,CAACC;IACvC,CAAC,CAAC;IACFE,OAAO,EAAE,CAAC;IACVD,SAAS,EAAE;EACb,CAAC,EAAEvB,UAAU,CAACE,IAAI,IAAI;IACpBqB,SAAS,EAAE,cAAc;IACzBC,OAAO,EAAE;EACX,CAAC;AACH,CAAC,CAAC,CAAC;AACH,MAAME,aAAa,GAAG,aAAaxC,KAAK,CAACyC,UAAU,CAAC,SAASD,aAAaA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvF,MAAMlB,KAAK,GAAGpB,eAAe,CAAC;IAC5BoB,KAAK,EAAEiB,OAAO;IACdpB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFsB,SAAS;MACTxB,IAAI,EAAEyB,QAAQ;MACd5B,QAAQ,EAAE6B;IACZ,CAAC,GAAGrB,KAAK;IACTsB,KAAK,GAAGlD,6BAA6B,CAAC4B,KAAK,EAAE1B,SAAS,CAAC;EACzD,MAAMe,UAAU,GAAGW,KAAK;EACxB,MAAMV,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,SAASkC,UAAUA,CAAC5B,IAAI,EAAE6B,YAAY,EAAE;IACtC,IAAK,aAAajD,KAAK,CAACkD,cAAc,CAAC9B,IAAI,CAAC,EAAE;MAC5C,OAAO,aAAapB,KAAK,CAACmD,YAAY,CAAC/B,IAAI,EAAE;QAC3CwB,SAAS,EAAEK;MACb,CAAC,CAAC;IACJ;IACA,OAAO7B,IAAI;EACb;EACA,OAAO,aAAaR,KAAK,CAACS,iBAAiB,EAAEvB,QAAQ,CAAC;IACpD8C,SAAS,EAAE1C,IAAI,CAACa,OAAO,CAACI,IAAI,EAAEyB,SAAS,CAAC;IACxCD,GAAG,EAAEA,GAAG;IACR7B,UAAU,EAAEA;EACd,CAAC,EAAEiC,KAAK,EAAE;IACRK,QAAQ,EAAE,CAACN,YAAY,GAAGE,UAAU,CAACF,YAAY,EAAE/B,OAAO,CAACE,QAAQ,CAAC,GAAG,IAAI,EAAE4B,QAAQ,GAAGG,UAAU,CAACH,QAAQ,EAAE9B,OAAO,CAACK,IAAI,CAAC,GAAG,aAAaV,IAAI,CAACJ,OAAO,EAAE;MACtJsC,SAAS,EAAE7B,OAAO,CAACK;IACrB,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFiC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGf,aAAa,CAACgB,SAAS,CAAC,yBAAyB;EACvF;EACA;EACA;EACA;EACA;AACF;AACA;EACEzC,OAAO,EAAEd,SAAS,CAACwD,MAAM;EACzB;AACF;AACA;EACEb,SAAS,EAAE3C,SAAS,CAACyD,MAAM;EAC3B;AACF;AACA;EACEtC,IAAI,EAAEnB,SAAS,CAAC0D,IAAI;EACpB;AACF;AACA;AACA;EACE3C,IAAI,EAAEf,SAAS,CAAC2D,IAAI;EACpB;AACF;AACA;EACE3C,QAAQ,EAAEhB,SAAS,CAAC0D,IAAI;EACxB;AACF;AACA;EACEE,EAAE,EAAE5D,SAAS,CAAC6D,SAAS,CAAC,CAAC7D,SAAS,CAAC8D,OAAO,CAAC9D,SAAS,CAAC6D,SAAS,CAAC,CAAC7D,SAAS,CAAC+D,IAAI,EAAE/D,SAAS,CAACwD,MAAM,EAAExD,SAAS,CAAC2D,IAAI,CAAC,CAAC,CAAC,EAAE3D,SAAS,CAAC+D,IAAI,EAAE/D,SAAS,CAACwD,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACVjB,aAAa,CAACyB,OAAO,GAAG,eAAe;AACvC,eAAezB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}