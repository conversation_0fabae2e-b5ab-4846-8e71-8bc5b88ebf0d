{"ast": null, "code": "import { startOfISOWeekYear } from \"./startOfISOWeekYear.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link isSameISOWeekYear} function options.\n */\n\n/**\n * @name isSameISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Are the given dates in the same ISO week-numbering year?\n *\n * @description\n * Are the given dates in the same ISO week-numbering year?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same ISO week-numbering year\n *\n * @example\n * // Are 29 December 2003 and 2 January 2005 in the same ISO week-numbering year?\n * const result = isSameISOWeekYear(new Date(2003, 11, 29), new Date(2005, 0, 2))\n * //=> true\n */\nexport function isSameISOWeekYear(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  return +startOfISOWeekYear(laterDate_) === +startOfISOWeekYear(earlierDate_);\n}\n\n// Fallback for modularized imports:\nexport default isSameISOWeekYear;", "map": {"version": 3, "names": ["startOfISOWeekYear", "normalizeDates", "isSameISOWeekYear", "laterDate", "earlierDate", "options", "laterDate_", "earlierDate_", "in"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/isSameISOWeekYear.js"], "sourcesContent": ["import { startOfISOWeekYear } from \"./startOfISOWeekYear.js\";\n\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link isSameISOWeekYear} function options.\n */\n\n/**\n * @name isSameISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Are the given dates in the same ISO week-numbering year?\n *\n * @description\n * Are the given dates in the same ISO week-numbering year?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same ISO week-numbering year\n *\n * @example\n * // Are 29 December 2003 and 2 January 2005 in the same ISO week-numbering year?\n * const result = isSameISOWeekYear(new Date(2003, 11, 29), new Date(2005, 0, 2))\n * //=> true\n */\nexport function isSameISOWeekYear(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return +startOfISOWeekYear(laterDate_) === +startOfISOWeekYear(earlierDate_);\n}\n\n// Fallback for modularized imports:\nexport default isSameISOWeekYear;\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yBAAyB;AAE5D,SAASC,cAAc,QAAQ,0BAA0B;;AAEzD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACjE,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGN,cAAc,CAC/CI,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EACD,OAAO,CAACJ,kBAAkB,CAACM,UAAU,CAAC,KAAK,CAACN,kBAAkB,CAACO,YAAY,CAAC;AAC9E;;AAEA;AACA,eAAeL,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}