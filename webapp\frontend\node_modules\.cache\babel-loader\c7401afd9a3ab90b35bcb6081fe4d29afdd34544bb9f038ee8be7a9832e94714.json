{"ast": null, "code": "var overArg = require('./_overArg');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeKeys = overArg(Object.keys, Object);\nmodule.exports = nativeKeys;", "map": {"version": 3, "names": ["overArg", "require", "nativeKeys", "Object", "keys", "module", "exports"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/lodash/_nativeKeys.js"], "sourcesContent": ["var overArg = require('./_overArg');\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeKeys = overArg(Object.keys, Object);\n\nmodule.exports = nativeKeys;\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEnC;AACA,IAAIC,UAAU,GAAGF,OAAO,CAACG,MAAM,CAACC,IAAI,EAAED,MAAM,CAAC;AAE7CE,MAAM,CAACC,OAAO,GAAGJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}