{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Gestione degli errori di autenticazione\naxiosInstance.interceptors.response.use(response => response, error => {\n  console.error('Errore nella risposta API:', error);\n  if (error.response && error.response.status === 401) {\n    // Se la risposta è 401 Unauthorized, effettua il logout\n    console.log('Errore 401 rilevato, rimozione token e reindirizzamento a /login');\n    localStorage.removeItem('token');\n    // Usa setTimeout per evitare loop di reindirizzamento\n    setTimeout(() => {\n      window.location.href = '/login';\n    }, 100);\n  }\n  return Promise.reject(error);\n});\nconst authService = {\n  // Login standard (admin o utente standard)\n  login: async (credentials, loginType) => {\n    try {\n      if (loginType === 'standard') {\n        // Converti le credenziali nel formato richiesto da OAuth2\n        const formData = new FormData();\n        formData.append('username', credentials.username);\n        formData.append('password', credentials.password);\n\n        // Usa axios direttamente per il login perché richiede FormData\n        const response = await axios.post(`${API_URL}/auth/login`, formData);\n        return response.data;\n      } else if (loginType === 'cantiere') {\n        // Login cantiere\n        const response = await axiosInstance.post('/auth/login/cantiere', {\n          codice_univoco: credentials.codice_univoco,\n          password: credentials.password\n        });\n        return response.data;\n      } else {\n        throw new Error('Tipo di login non valido');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Verifica la validità del token\n  checkToken: async () => {\n    try {\n      console.log('Verifica token in corso...');\n      const response = await axiosInstance.post('/auth/test-token');\n      console.log('Risposta verifica token:', response.data);\n      return {\n        id: response.data.user_id,\n        username: response.data.username,\n        role: response.data.role\n      };\n    } catch (error) {\n      console.error('Check token error:', error);\n      // Pulisci il localStorage per evitare loop\n      localStorage.removeItem('token');\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Impersona un altro utente (solo per admin)\n  impersonateUser: async userId => {\n    try {\n      console.log('Richiesta impersonificazione per userId:', userId);\n      const response = await axiosInstance.post('/auth/impersonate', {\n        user_id: userId\n      });\n      console.log('Risposta impersonificazione:', response.data);\n\n      // Pulisci eventuali dati di sessione precedenti\n      // prima di impostare il nuovo token\n      localStorage.removeItem('token');\n\n      // Piccolo ritardo per assicurarsi che il token precedente sia rimosso\n      await new Promise(resolve => setTimeout(resolve, 100));\n      return response.data;\n    } catch (error) {\n      console.error('Impersonate user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "console", "status", "log", "removeItem", "setTimeout", "window", "location", "href", "authService", "login", "credentials", "loginType", "formData", "FormData", "append", "username", "password", "post", "data", "codice_univoco", "Error", "checkToken", "id", "user_id", "role", "impersonate<PERSON><PERSON>", "userId", "resolve"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/authService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Gestione degli errori di autenticazione\naxiosInstance.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    console.error('Errore nella risposta API:', error);\n    if (error.response && error.response.status === 401) {\n      // Se la risposta è 401 Unauthorized, effettua il logout\n      console.log('Errore 401 rilevato, rimozione token e reindirizzamento a /login');\n      localStorage.removeItem('token');\n      // Usa setTimeout per evitare loop di reindirizzamento\n      setTimeout(() => {\n        window.location.href = '/login';\n      }, 100);\n    }\n    return Promise.reject(error);\n  }\n);\n\nconst authService = {\n  // Login standard (admin o utente standard)\n  login: async (credentials, loginType) => {\n    try {\n      if (loginType === 'standard') {\n        // Converti le credenziali nel formato richiesto da OAuth2\n        const formData = new FormData();\n        formData.append('username', credentials.username);\n        formData.append('password', credentials.password);\n\n        // Usa axios direttamente per il login perché richiede FormData\n        const response = await axios.post(`${API_URL}/auth/login`, formData);\n        return response.data;\n      } else if (loginType === 'cantiere') {\n        // Login cantiere\n        const response = await axiosInstance.post('/auth/login/cantiere', {\n          codice_univoco: credentials.codice_univoco,\n          password: credentials.password\n        });\n        return response.data;\n      } else {\n        throw new Error('Tipo di login non valido');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n\n\n  // Verifica la validità del token\n  checkToken: async () => {\n    try {\n      console.log('Verifica token in corso...');\n      const response = await axiosInstance.post('/auth/test-token');\n      console.log('Risposta verifica token:', response.data);\n      return {\n        id: response.data.user_id,\n        username: response.data.username,\n        role: response.data.role\n      };\n    } catch (error) {\n      console.error('Check token error:', error);\n      // Pulisci il localStorage per evitare loop\n      localStorage.removeItem('token');\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Impersona un altro utente (solo per admin)\n  impersonateUser: async (userId) => {\n    try {\n      console.log('Richiesta impersonificazione per userId:', userId);\n      const response = await axiosInstance.post('/auth/impersonate', {\n        user_id: userId\n      });\n      console.log('Risposta impersonificazione:', response.data);\n\n      // Pulisci eventuali dati di sessione precedenti\n      // prima di impostare il nuovo token\n      localStorage.removeItem('token');\n\n      // Piccolo ritardo per assicurarsi che il token precedente sia rimosso\n      await new Promise(resolve => setTimeout(resolve, 100));\n\n      return response.data;\n    } catch (error) {\n      console.error('Impersonate user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default authService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,aAAa,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CACpCS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EACTI,OAAO,CAACJ,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;EAClD,IAAIA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;IACnD;IACAD,OAAO,CAACE,GAAG,CAAC,kEAAkE,CAAC;IAC/ET,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;IAChC;IACAC,UAAU,CAAC,MAAM;MACfC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC,CAAC,EAAE,GAAG,CAAC;EACT;EACA,OAAOV,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMY,WAAW,GAAG;EAClB;EACAC,KAAK,EAAE,MAAAA,CAAOC,WAAW,EAAEC,SAAS,KAAK;IACvC,IAAI;MACF,IAAIA,SAAS,KAAK,UAAU,EAAE;QAC5B;QACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,WAAW,CAACK,QAAQ,CAAC;QACjDH,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,WAAW,CAACM,QAAQ,CAAC;;QAEjD;QACA,MAAMjB,QAAQ,GAAG,MAAMjB,KAAK,CAACmC,IAAI,CAAC,GAAGlC,OAAO,aAAa,EAAE6B,QAAQ,CAAC;QACpE,OAAOb,QAAQ,CAACmB,IAAI;MACtB,CAAC,MAAM,IAAIP,SAAS,KAAK,UAAU,EAAE;QACnC;QACA,MAAMZ,QAAQ,GAAG,MAAMf,aAAa,CAACiC,IAAI,CAAC,sBAAsB,EAAE;UAChEE,cAAc,EAAET,WAAW,CAACS,cAAc;UAC1CH,QAAQ,EAAEN,WAAW,CAACM;QACxB,CAAC,CAAC;QACF,OAAOjB,QAAQ,CAACmB,IAAI;MACtB,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAAC,0BAA0B,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,MAAMA,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACmB,IAAI,GAAGtB,KAAK;IACpD;EACF,CAAC;EAID;EACAyB,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,IAAI;MACFrB,OAAO,CAACE,GAAG,CAAC,4BAA4B,CAAC;MACzC,MAAMH,QAAQ,GAAG,MAAMf,aAAa,CAACiC,IAAI,CAAC,kBAAkB,CAAC;MAC7DjB,OAAO,CAACE,GAAG,CAAC,0BAA0B,EAAEH,QAAQ,CAACmB,IAAI,CAAC;MACtD,OAAO;QACLI,EAAE,EAAEvB,QAAQ,CAACmB,IAAI,CAACK,OAAO;QACzBR,QAAQ,EAAEhB,QAAQ,CAACmB,IAAI,CAACH,QAAQ;QAChCS,IAAI,EAAEzB,QAAQ,CAACmB,IAAI,CAACM;MACtB,CAAC;IACH,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C;MACAH,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;MAChC,MAAMP,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACmB,IAAI,GAAGtB,KAAK;IACpD;EACF,CAAC;EAED;EACA6B,eAAe,EAAE,MAAOC,MAAM,IAAK;IACjC,IAAI;MACF1B,OAAO,CAACE,GAAG,CAAC,0CAA0C,EAAEwB,MAAM,CAAC;MAC/D,MAAM3B,QAAQ,GAAG,MAAMf,aAAa,CAACiC,IAAI,CAAC,mBAAmB,EAAE;QAC7DM,OAAO,EAAEG;MACX,CAAC,CAAC;MACF1B,OAAO,CAACE,GAAG,CAAC,8BAA8B,EAAEH,QAAQ,CAACmB,IAAI,CAAC;;MAE1D;MACA;MACAzB,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;;MAEhC;MACA,MAAM,IAAIN,OAAO,CAAC8B,OAAO,IAAIvB,UAAU,CAACuB,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtD,OAAO5B,QAAQ,CAACmB,IAAI;IACtB,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACmB,IAAI,GAAGtB,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeY,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}