{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst certificazioneService = {\n  // Ottiene la lista delle certificazioni di un cantiere\n  getCertificazioni: async (cantiereId, filtroCavo = '') => {\n    try {\n      let url = `/certificazioni/${cantiereId}`;\n      if (filtroCavo) {\n        url += `?cavo=${filtroCavo}`;\n      }\n      const response = await axiosInstance.get(url);\n      return response.data;\n    } catch (error) {\n      console.error('Get certificazioni error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea una nuova certificazione\n  createCertificazione: async (cantiereId, certificazioneData) => {\n    try {\n      const response = await axiosInstance.post(`/certificazioni/${cantiereId}`, certificazioneData);\n      return response.data;\n    } catch (error) {\n      console.error('Create certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i dettagli di una certificazione\n  getCertificazione: async (cantiereId, idCertificazione) => {\n    try {\n      const response = await axiosInstance.get(`/certificazioni/${cantiereId}/${idCertificazione}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina una certificazione\n  deleteCertificazione: async (cantiereId, idCertificazione) => {\n    try {\n      const response = await axiosInstance.delete(`/certificazioni/${cantiereId}/${idCertificazione}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Genera PDF di una certificazione\n  generatePdf: async (cantiereId, idCertificazione) => {\n    try {\n      const response = await axiosInstance.get(`/certificazioni/${cantiereId}/${idCertificazione}/pdf`);\n      return response.data;\n    } catch (error) {\n      console.error('Generate PDF error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene la lista degli strumenti certificati\n  getStrumenti: async cantiereId => {\n    try {\n      const response = await axiosInstance.get(`/strumenti/${cantiereId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get strumenti error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea un nuovo strumento certificato\n  createStrumento: async (cantiereId, strumentoData) => {\n    try {\n      const response = await axiosInstance.post(`/strumenti/${cantiereId}`, strumentoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna uno strumento certificato\n  updateStrumento: async (cantiereId, idStrumento, strumentoData) => {\n    try {\n      const response = await axiosInstance.put(`/strumenti/${cantiereId}/${idStrumento}`, strumentoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina uno strumento certificato\n  deleteStrumento: async (cantiereId, idStrumento) => {\n    try {\n      const response = await axiosInstance.delete(`/strumenti/${cantiereId}/${idStrumento}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default certificazioneService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "certificazioneService", "getCertificazioni", "cantiereId", "filtroCavo", "url", "response", "get", "data", "console", "createCertificazione", "certificazioneData", "post", "getCertificazione", "idCertificazione", "deleteCertificazione", "delete", "generatePdf", "getStrumenti", "createStrumento", "strumentoData", "updateStrumento", "idStrumento", "put", "deleteStrumento"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/certificazioneService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\nconst certificazioneService = {\n  // Ottiene la lista delle certificazioni di un cantiere\n  getCertificazioni: async (cantiereId, filtroCavo = '') => {\n    try {\n      let url = `/certificazioni/${cantiereId}`;\n      if (filtroCavo) {\n        url += `?cavo=${filtroCavo}`;\n      }\n      const response = await axiosInstance.get(url);\n      return response.data;\n    } catch (error) {\n      console.error('Get certificazioni error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea una nuova certificazione\n  createCertificazione: async (cantiereId, certificazioneData) => {\n    try {\n      const response = await axiosInstance.post(`/certificazioni/${cantiereId}`, certificazioneData);\n      return response.data;\n    } catch (error) {\n      console.error('Create certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene i dettagli di una certificazione\n  getCertificazione: async (cantiereId, idCertificazione) => {\n    try {\n      const response = await axiosInstance.get(`/certificazioni/${cantiereId}/${idCertificazione}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina una certificazione\n  deleteCertificazione: async (cantiereId, idCertificazione) => {\n    try {\n      const response = await axiosInstance.delete(`/certificazioni/${cantiereId}/${idCertificazione}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete certificazione error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Genera PDF di una certificazione\n  generatePdf: async (cantiereId, idCertificazione) => {\n    try {\n      const response = await axiosInstance.get(`/certificazioni/${cantiereId}/${idCertificazione}/pdf`);\n      return response.data;\n    } catch (error) {\n      console.error('Generate PDF error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene la lista degli strumenti certificati\n  getStrumenti: async (cantiereId) => {\n    try {\n      const response = await axiosInstance.get(`/strumenti/${cantiereId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get strumenti error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea un nuovo strumento certificato\n  createStrumento: async (cantiereId, strumentoData) => {\n    try {\n      const response = await axiosInstance.post(`/strumenti/${cantiereId}`, strumentoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna uno strumento certificato\n  updateStrumento: async (cantiereId, idStrumento, strumentoData) => {\n    try {\n      const response = await axiosInstance.put(`/strumenti/${cantiereId}/${idStrumento}`, strumentoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina uno strumento certificato\n  deleteStrumento: async (cantiereId, idStrumento) => {\n    try {\n      const response = await axiosInstance.delete(`/strumenti/${cantiereId}/${idStrumento}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete strumento error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default certificazioneService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,qBAAqB,GAAG;EAC5B;EACAC,iBAAiB,EAAE,MAAAA,CAAOC,UAAU,EAAEC,UAAU,GAAG,EAAE,KAAK;IACxD,IAAI;MACF,IAAIC,GAAG,GAAG,mBAAmBF,UAAU,EAAE;MACzC,IAAIC,UAAU,EAAE;QACdC,GAAG,IAAI,SAASD,UAAU,EAAE;MAC9B;MACA,MAAME,QAAQ,GAAG,MAAMpB,aAAa,CAACqB,GAAG,CAACF,GAAG,CAAC;MAC7C,OAAOC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF,CAAC;EAED;EACAY,oBAAoB,EAAE,MAAAA,CAAOP,UAAU,EAAEQ,kBAAkB,KAAK;IAC9D,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMpB,aAAa,CAAC0B,IAAI,CAAC,mBAAmBT,UAAU,EAAE,EAAEQ,kBAAkB,CAAC;MAC9F,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF,CAAC;EAED;EACAe,iBAAiB,EAAE,MAAAA,CAAOV,UAAU,EAAEW,gBAAgB,KAAK;IACzD,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMpB,aAAa,CAACqB,GAAG,CAAC,mBAAmBJ,UAAU,IAAIW,gBAAgB,EAAE,CAAC;MAC7F,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF,CAAC;EAED;EACAiB,oBAAoB,EAAE,MAAAA,CAAOZ,UAAU,EAAEW,gBAAgB,KAAK;IAC5D,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMpB,aAAa,CAAC8B,MAAM,CAAC,mBAAmBb,UAAU,IAAIW,gBAAgB,EAAE,CAAC;MAChG,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF,CAAC;EAED;EACAmB,WAAW,EAAE,MAAAA,CAAOd,UAAU,EAAEW,gBAAgB,KAAK;IACnD,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMpB,aAAa,CAACqB,GAAG,CAAC,mBAAmBJ,UAAU,IAAIW,gBAAgB,MAAM,CAAC;MACjG,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF,CAAC;EAED;EACAoB,YAAY,EAAE,MAAOf,UAAU,IAAK;IAClC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMpB,aAAa,CAACqB,GAAG,CAAC,cAAcJ,UAAU,EAAE,CAAC;MACpE,OAAOG,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF,CAAC;EAED;EACAqB,eAAe,EAAE,MAAAA,CAAOhB,UAAU,EAAEiB,aAAa,KAAK;IACpD,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMpB,aAAa,CAAC0B,IAAI,CAAC,cAAcT,UAAU,EAAE,EAAEiB,aAAa,CAAC;MACpF,OAAOd,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF,CAAC;EAED;EACAuB,eAAe,EAAE,MAAAA,CAAOlB,UAAU,EAAEmB,WAAW,EAAEF,aAAa,KAAK;IACjE,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMpB,aAAa,CAACqC,GAAG,CAAC,cAAcpB,UAAU,IAAImB,WAAW,EAAE,EAAEF,aAAa,CAAC;MAClG,OAAOd,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF,CAAC;EAED;EACA0B,eAAe,EAAE,MAAAA,CAAOrB,UAAU,EAAEmB,WAAW,KAAK;IAClD,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMpB,aAAa,CAAC8B,MAAM,CAAC,cAAcb,UAAU,IAAImB,WAAW,EAAE,CAAC;MACtF,OAAOhB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}