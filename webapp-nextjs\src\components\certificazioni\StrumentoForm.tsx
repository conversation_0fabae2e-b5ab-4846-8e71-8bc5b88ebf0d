'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Save, X, Settings } from 'lucide-react'
import { StrumentoCertificato, StrumentoCertificatoCreate } from '@/types/certificazioni'
import { strumentiApi } from '@/lib/api'

interface StrumentoFormProps {
  cantiereId: number
  strumento?: StrumentoCertificato | null
  onSuccess: () => void
  onCancel: () => void
}

export default function StrumentoForm({
  cantiereId,
  strumento,
  onSuccess,
  onCancel
}: StrumentoFormProps) {
  const [formData, setFormData] = useState<StrumentoCertificatoCreate>({
    nome: '',
    marca: '',
    modello: '',
    numero_serie: '',
    data_calibrazione: '',
    data_scadenza_calibrazione: '',
    note: '',
    tipo_strumento: 'MEGGER',
    ente_certificatore: '',
    numero_certificato_calibrazione: '',
    range_misura: '',
    precisione: '',
    stato_strumento: 'ATTIVO'
  })

  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState('')
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  const isEdit = !!strumento

  useEffect(() => {
    if (strumento) {
      setFormData({
        nome: strumento.nome,
        marca: strumento.marca,
        modello: strumento.modello,
        numero_serie: strumento.numero_serie,
        data_calibrazione: strumento.data_calibrazione.split('T')[0], // Format for input[type="date"]
        data_scadenza_calibrazione: strumento.data_scadenza_calibrazione.split('T')[0],
        note: strumento.note || '',
        tipo_strumento: strumento.tipo_strumento || 'MEGGER',
        ente_certificatore: strumento.ente_certificatore || '',
        numero_certificato_calibrazione: strumento.numero_certificato_calibrazione || '',
        range_misura: strumento.range_misura || '',
        precisione: strumento.precisione || '',
        stato_strumento: strumento.stato_strumento || 'ATTIVO'
      })
    }
  }, [strumento])

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}

    if (!formData.nome.trim()) {
      errors.nome = 'Il nome è obbligatorio'
    }

    if (!formData.marca.trim()) {
      errors.marca = 'La marca è obbligatoria'
    }

    if (!formData.modello.trim()) {
      errors.modello = 'Il modello è obbligatorio'
    }

    if (!formData.numero_serie.trim()) {
      errors.numero_serie = 'Il numero di serie è obbligatorio'
    }

    if (!formData.data_calibrazione) {
      errors.data_calibrazione = 'La data di calibrazione è obbligatoria'
    }

    if (!formData.data_scadenza_calibrazione) {
      errors.data_scadenza_calibrazione = 'La data di scadenza è obbligatoria'
    }

    if (formData.data_calibrazione && formData.data_scadenza_calibrazione) {
      const calibrazione = new Date(formData.data_calibrazione)
      const scadenza = new Date(formData.data_scadenza_calibrazione)
      
      if (scadenza <= calibrazione) {
        errors.data_scadenza_calibrazione = 'La data di scadenza deve essere successiva alla calibrazione'
      }
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      setIsSaving(true)
      setError('')

      if (isEdit && strumento) {
        await strumentiApi.updateStrumento(cantiereId, strumento.id_strumento, formData)
      } else {
        await strumentiApi.createStrumento(cantiereId, formData)
      }

      onSuccess()
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante il salvataggio')
    } finally {
      setIsSaving(false)
    }
  }

  const handleInputChange = (field: keyof StrumentoCertificatoCreate, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Rimuovi errore di validazione se presente
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  // Calcola automaticamente la data di scadenza (1 anno dalla calibrazione)
  const handleCalibrazioneChange = (value: string) => {
    handleInputChange('data_calibrazione', value)
    
    if (value && !formData.data_scadenza_calibrazione) {
      const calibrazione = new Date(value)
      const scadenza = new Date(calibrazione)
      scadenza.setFullYear(scadenza.getFullYear() + 1)
      handleInputChange('data_scadenza_calibrazione', scadenza.toISOString().split('T')[0])
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900 flex items-center gap-3">
            <Settings className="h-6 w-6 text-blue-600" />
            {isEdit ? 'Modifica Strumento' : 'Nuovo Strumento'}
          </h2>
          <p className="text-slate-600 mt-1">
            {isEdit ? 'Modifica i dati dello strumento esistente' : 'Aggiungi un nuovo strumento di misura'}
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={onCancel}>
            <X className="h-4 w-4 mr-2" />
            Annulla
          </Button>
          <Button onClick={handleSubmit} disabled={isSaving}>
            {isSaving ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Save className="h-4 w-4 mr-2" />}
            {isEdit ? 'Aggiorna' : 'Salva'}
          </Button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Informazioni Base */}
        <Card>
          <CardHeader>
            <CardTitle>Informazioni Base</CardTitle>
            <CardDescription>Dati identificativi dello strumento</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="nome">Nome Strumento *</Label>
                <Input
                  id="nome"
                  value={formData.nome}
                  onChange={(e) => handleInputChange('nome', e.target.value)}
                  className={validationErrors.nome ? 'border-red-500' : ''}
                  placeholder="es. Megger MFT1741"
                />
                {validationErrors.nome && (
                  <p className="text-sm text-red-600">{validationErrors.nome}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="tipo_strumento">Tipo Strumento</Label>
                <Select
                  value={formData.tipo_strumento}
                  onValueChange={(value) => handleInputChange('tipo_strumento', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MEGGER">Megger</SelectItem>
                    <SelectItem value="MULTIMETRO">Multimetro</SelectItem>
                    <SelectItem value="OSCILLOSCOPIO">Oscilloscopio</SelectItem>
                    <SelectItem value="ALTRO">Altro</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="marca">Marca *</Label>
                <Input
                  id="marca"
                  value={formData.marca}
                  onChange={(e) => handleInputChange('marca', e.target.value)}
                  className={validationErrors.marca ? 'border-red-500' : ''}
                  placeholder="es. Fluke, Megger, Keysight"
                />
                {validationErrors.marca && (
                  <p className="text-sm text-red-600">{validationErrors.marca}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="modello">Modello *</Label>
                <Input
                  id="modello"
                  value={formData.modello}
                  onChange={(e) => handleInputChange('modello', e.target.value)}
                  className={validationErrors.modello ? 'border-red-500' : ''}
                  placeholder="es. MFT1741, 87V"
                />
                {validationErrors.modello && (
                  <p className="text-sm text-red-600">{validationErrors.modello}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="numero_serie">Numero Serie *</Label>
                <Input
                  id="numero_serie"
                  value={formData.numero_serie}
                  onChange={(e) => handleInputChange('numero_serie', e.target.value)}
                  className={validationErrors.numero_serie ? 'border-red-500' : ''}
                  placeholder="Numero di serie univoco"
                />
                {validationErrors.numero_serie && (
                  <p className="text-sm text-red-600">{validationErrors.numero_serie}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="stato_strumento">Stato</Label>
                <Select
                  value={formData.stato_strumento}
                  onValueChange={(value) => handleInputChange('stato_strumento', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ATTIVO">Attivo</SelectItem>
                    <SelectItem value="SCADUTO">Scaduto</SelectItem>
                    <SelectItem value="FUORI_SERVIZIO">Fuori Servizio</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Calibrazione */}
        <Card>
          <CardHeader>
            <CardTitle>Calibrazione</CardTitle>
            <CardDescription>Informazioni sulla calibrazione dello strumento</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="data_calibrazione">Data Calibrazione *</Label>
                <Input
                  id="data_calibrazione"
                  type="date"
                  value={formData.data_calibrazione}
                  onChange={(e) => handleCalibrazioneChange(e.target.value)}
                  className={validationErrors.data_calibrazione ? 'border-red-500' : ''}
                />
                {validationErrors.data_calibrazione && (
                  <p className="text-sm text-red-600">{validationErrors.data_calibrazione}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="data_scadenza_calibrazione">Data Scadenza *</Label>
                <Input
                  id="data_scadenza_calibrazione"
                  type="date"
                  value={formData.data_scadenza_calibrazione}
                  onChange={(e) => handleInputChange('data_scadenza_calibrazione', e.target.value)}
                  className={validationErrors.data_scadenza_calibrazione ? 'border-red-500' : ''}
                />
                {validationErrors.data_scadenza_calibrazione && (
                  <p className="text-sm text-red-600">{validationErrors.data_scadenza_calibrazione}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="ente_certificatore">Ente Certificatore</Label>
                <Input
                  id="ente_certificatore"
                  value={formData.ente_certificatore}
                  onChange={(e) => handleInputChange('ente_certificatore', e.target.value)}
                  placeholder="es. LAT 123, ACCREDIA"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="numero_certificato_calibrazione">Numero Certificato</Label>
                <Input
                  id="numero_certificato_calibrazione"
                  value={formData.numero_certificato_calibrazione}
                  onChange={(e) => handleInputChange('numero_certificato_calibrazione', e.target.value)}
                  placeholder="Numero del certificato di calibrazione"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Specifiche Tecniche */}
        <Card>
          <CardHeader>
            <CardTitle>Specifiche Tecniche</CardTitle>
            <CardDescription>Caratteristiche tecniche dello strumento</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="range_misura">Range di Misura</Label>
                <Input
                  id="range_misura"
                  value={formData.range_misura}
                  onChange={(e) => handleInputChange('range_misura', e.target.value)}
                  placeholder="es. 0-1000V, 0-20A"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="precisione">Precisione</Label>
                <Input
                  id="precisione"
                  value={formData.precisione}
                  onChange={(e) => handleInputChange('precisione', e.target.value)}
                  placeholder="es. ±0.1%, ±2 digit"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="note">Note</Label>
              <Textarea
                id="note"
                value={formData.note}
                onChange={(e) => handleInputChange('note', e.target.value)}
                placeholder="Note aggiuntive sullo strumento..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">{error}</AlertDescription>
          </Alert>
        )}
      </form>
    </div>
  )
}
