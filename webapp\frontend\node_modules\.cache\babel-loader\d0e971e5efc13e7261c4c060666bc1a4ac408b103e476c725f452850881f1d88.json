{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\InserimentoMetriDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Grid, Typography, Box, Alert, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Tooltip, Autocomplete, FormControl, InputLabel, Select, MenuItem, ListItemText, ListItemIcon, Checkbox, Card, CardContent, CardHeader, Divider, Stack, useTheme, useMediaQuery, Accordion, AccordionSummary, AccordionDetails, Collapse } from '@mui/material';\nimport { Save as SaveIcon, Cancel as CancelIcon, Cable as CableIcon, CheckCircle as CheckCircleIcon, Warning as WarningIcon, Storage as BobinaIcon, Error as ErrorIcon, Info as InfoIcon, ExpandMore as ExpandMoreIcon, Person as PersonIcon, Build as BuildIcon, Link as LinkIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserimentoMetriDialog = ({\n  open,\n  onClose,\n  comanda,\n  onSuccess\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [cavi, setCavi] = useState([]);\n  const [datiPosa, setDatiPosa] = useState({});\n  const [validationErrors, setValidationErrors] = useState({});\n  const [validationWarnings, setValidationWarnings] = useState({});\n  const [bobineDisponibili, setBobineDisponibili] = useState([]);\n  const [loadingBobine, setLoadingBobine] = useState(false);\n\n  // Responsive design hooks\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));\n  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));\n  useEffect(() => {\n    if (open && comanda) {\n      loadCaviComanda();\n      loadBobineDisponibili();\n    }\n  }, [open, comanda]);\n  const loadCaviComanda = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const caviData = await comandeService.getCaviComanda(comanda.codice_comanda);\n      setCavi(caviData);\n\n      // Inizializza i dati di posa per ogni cavo\n      const initialDati = {};\n      caviData.forEach(cavo => {\n        initialDati[cavo.id_cavo] = {\n          // Per comande POSA: metri reali devono essere 0 (azzerati)\n          // Per altre comande: mantieni il valore esistente\n          metratura_reale: comanda.tipo_comanda === 'POSA' ? 0 : cavo.metratura_reale || 0,\n          data_posa: new Date().toISOString().split('T')[0],\n          responsabile_posa: comanda.responsabile || '',\n          numero_persone_impiegate: comanda.numero_componenti_squadra || 1,\n          sistemazione: false,\n          fascettatura: false,\n          id_bobina: cavo.id_bobina || '',\n          // Bobina attualmente associata\n          force_over: false // Flag per forzare associazione anche se metri insufficienti\n        };\n      });\n      setDatiPosa(initialDati);\n    } catch (err) {\n      console.error('Errore nel caricamento cavi:', err);\n      setError('Errore nel caricamento dei cavi della comanda');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadBobineDisponibili = async () => {\n    try {\n      setLoadingBobine(true);\n\n      // Verifica che ci sia un cantiere valido\n      if (!(comanda !== null && comanda !== void 0 && comanda.id_cantiere)) {\n        console.warn('ID cantiere non disponibile per il caricamento bobine');\n        return;\n      }\n      console.log(`Caricamento bobine per cantiere ${comanda.id_cantiere}`);\n\n      // Carica tutte le bobine disponibili per il cantiere\n      const bobineData = await caviService.getBobineDisponibili(comanda.id_cantiere);\n      setBobineDisponibili(bobineData);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n    } catch (err) {\n      console.error('Errore nel caricamento bobine:', err);\n\n      // Gestione specifica per errori di autenticazione\n      if (err.status === 401) {\n        console.warn('Errore 401: Sessione scaduta o non autenticato');\n        setError('Sessione scaduta. Effettua nuovamente il login per caricare le bobine.');\n      } else if (err.isNetworkError) {\n        console.warn('Errore di rete nel caricamento bobine');\n        setError('Impossibile connettersi al server per caricare le bobine.');\n      } else {\n        console.warn('Errore generico nel caricamento bobine:', err);\n        setError('Errore nel caricamento delle bobine. Puoi comunque usare BOBINA_VUOTA.');\n      }\n\n      // Imposta array vuoto per evitare errori nell'interfaccia\n      setBobineDisponibili([]);\n    } finally {\n      setLoadingBobine(false);\n    }\n  };\n  const handleMetriChange = (idCavo, value) => {\n    const numericValue = parseFloat(value) || 0;\n\n    // Aggiorna i dati di posa\n    setDatiPosa(prev => {\n      const newDatiPosa = {\n        ...prev,\n        [idCavo]: {\n          ...prev[idCavo],\n          metratura_reale: numericValue\n        }\n      };\n\n      // Validazione metri vs teorico e bobina\n      const cavo = cavi.find(c => c.id_cavo === idCavo);\n      let errors = [];\n      let warnings = [];\n\n      // WARNING: Metratura superiore del 10% (non bloccante)\n      if (cavo && numericValue > cavo.metratura_teorica * 1.1) {\n        warnings.push('Metratura superiore del 10% rispetto al teorico');\n      }\n\n      // ERROR: Validazione metri vs bobina selezionata (bloccante)\n      const datiCavo = newDatiPosa[idCavo];\n      if (datiCavo !== null && datiCavo !== void 0 && datiCavo.id_bobina && datiCavo.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobineDisponibili.find(b => b.id_bobina === datiCavo.id_bobina);\n        if (bobina && numericValue > bobina.metri_residui && !datiCavo.force_over) {\n          errors.push(`Bobina ha solo ${bobina.metri_residui}m residui (richiesti ${numericValue}m)`);\n        }\n      }\n\n      // Aggiorna gli errori di validazione (solo errori bloccanti)\n      setValidationErrors(prevErrors => {\n        if (errors.length > 0) {\n          return {\n            ...prevErrors,\n            [idCavo]: errors.join('; ')\n          };\n        } else {\n          const newErrors = {\n            ...prevErrors\n          };\n          delete newErrors[idCavo];\n          return newErrors;\n        }\n      });\n\n      // Aggiorna i warning di validazione (non bloccanti)\n      setValidationWarnings(prevWarnings => {\n        if (warnings.length > 0) {\n          return {\n            ...prevWarnings,\n            [idCavo]: warnings.join('; ')\n          };\n        } else {\n          const newWarnings = {\n            ...prevWarnings\n          };\n          delete newWarnings[idCavo];\n          return newWarnings;\n        }\n      });\n      return newDatiPosa;\n    });\n  };\n  const handlePersoneChange = (idCavo, value) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        numero_persone_impiegate: parseInt(value) || 1\n      }\n    }));\n  };\n  const handleSistemazioneChange = (idCavo, checked) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        sistemazione: checked\n      }\n    }));\n  };\n  const handleFascettaturaChange = (idCavo, checked) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        fascettatura: checked\n      }\n    }));\n  };\n  const handleBobinaChange = (idCavo, bobinaId) => {\n    setDatiPosa(prev => {\n      const newDatiPosa = {\n        ...prev,\n        [idCavo]: {\n          ...prev[idCavo],\n          id_bobina: bobinaId,\n          force_over: false // Reset force_over quando cambia bobina\n        }\n      };\n\n      // Rivalidazione dopo cambio bobina\n      const datiCavo = newDatiPosa[idCavo];\n      if (datiCavo !== null && datiCavo !== void 0 && datiCavo.metratura_reale) {\n        // Usa setTimeout per evitare problemi di stato asincrono\n        setTimeout(() => {\n          handleMetriChange(idCavo, datiCavo.metratura_reale);\n        }, 0);\n      }\n      return newDatiPosa;\n    });\n  };\n  const handleForceOverChange = (idCavo, forceOver) => {\n    setDatiPosa(prev => {\n      const newDatiPosa = {\n        ...prev,\n        [idCavo]: {\n          ...prev[idCavo],\n          force_over: forceOver\n        }\n      };\n\n      // Rivalidazione dopo cambio force_over\n      const datiCavo = newDatiPosa[idCavo];\n      if (datiCavo !== null && datiCavo !== void 0 && datiCavo.metratura_reale) {\n        // Usa setTimeout per evitare problemi di stato asincrono\n        setTimeout(() => {\n          handleMetriChange(idCavo, datiCavo.metratura_reale);\n        }, 0);\n      }\n      return newDatiPosa;\n    });\n  };\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Validazione finale\n      const hasErrors = Object.keys(validationErrors).length > 0;\n      if (hasErrors) {\n        setError('Correggere gli errori di validazione prima di salvare');\n        return;\n      }\n\n      // Filtra solo i cavi con metri > 0 per l'installazione\n      const datiPosaFiltrati = {};\n      Object.keys(datiPosa).forEach(idCavo => {\n        var _datiPosa$idCavo;\n        const metri = parseFloat(((_datiPosa$idCavo = datiPosa[idCavo]) === null || _datiPosa$idCavo === void 0 ? void 0 : _datiPosa$idCavo.metratura_reale) || 0);\n        if (metri > 0) {\n          datiPosaFiltrati[idCavo] = datiPosa[idCavo];\n        }\n      });\n      console.log('Dati posa originali:', datiPosa);\n      console.log('Dati posa filtrati (solo metri > 0):', datiPosaFiltrati);\n      const caviDaInstallare = Object.keys(datiPosaFiltrati).length;\n      const caviTotali = Object.keys(datiPosa).length;\n      if (caviDaInstallare === 0) {\n        setError('Nessun cavo da installare. Inserire metri > 0 per almeno un cavo.');\n        return;\n      }\n      console.log(`Installando ${caviDaInstallare} cavi su ${caviTotali} totali`);\n\n      // Salva i dati di posa con associazioni bobine (solo cavi con metri > 0)\n      await comandeService.aggiornaDatiPosaConBobine(comanda.codice_comanda, datiPosaFiltrati);\n      const messaggioSuccesso = caviDaInstallare === caviTotali ? `Tutti i ${caviDaInstallare} cavi installati con successo` : `${caviDaInstallare} cavi installati su ${caviTotali}. I rimanenti ${caviTotali - caviDaInstallare} cavi rimangono in comanda.`;\n      onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(messaggioSuccesso);\n      onClose();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.message || 'Errore nel salvataggio dei dati');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getTipoComandaColor = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'primary';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'warning';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'info';\n      case 'CERTIFICAZIONE':\n        return 'success';\n      default:\n        return 'default';\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Coll. Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Coll. Arrivo';\n      case 'CERTIFICAZIONE':\n        return 'Certificazione';\n      default:\n        return tipo;\n    }\n  };\n\n  // Filtra bobine compatibili per un cavo specifico\n  const getBobineCompatibili = cavo => {\n    if (!bobineDisponibili || bobineDisponibili.length === 0) {\n      console.log('Nessuna bobina disponibile per il filtro');\n      return [];\n    }\n    console.log('Filtro compatibilità per cavo:', {\n      id_cavo: cavo.id_cavo,\n      tipologia: cavo.tipologia,\n      formazione: cavo.formazione\n    });\n    console.log('Bobine disponibili:', bobineDisponibili.map(b => ({\n      id_bobina: b.id_bobina,\n      tipologia: b.tipologia,\n      sezione: b.sezione,\n      metri_residui: b.metri_residui\n    })));\n    const bobineCompatibili = bobineDisponibili.filter(bobina => {\n      // Confronta tipologia\n      const tipologiaMatch = bobina.tipologia === cavo.tipologia;\n\n      // Confronta sezione - il backend usa 'sezione'\n      const sezioneMatch = bobina.sezione === cavo.formazione;\n\n      // Verifica metri residui\n      const metriOk = bobina.metri_residui > 0;\n      console.log(`Bobina ${bobina.id_bobina}:`, {\n        tipologiaMatch,\n        sezioneMatch,\n        metriOk,\n        bobina_tipologia: bobina.tipologia,\n        cavo_tipologia: cavo.tipologia,\n        bobina_sezione: bobina.sezione,\n        cavo_formazione: cavo.formazione\n      });\n      return tipologiaMatch && sezioneMatch && metriOk;\n    });\n    console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`, bobineCompatibili);\n    return bobineCompatibili;\n  };\n\n  // Verifica se una bobina è compatibile con un cavo\n  const isBobinaCompatibile = (bobina, cavo) => {\n    return bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.formazione;\n  };\n\n  // Ottiene il colore per lo stato della bobina\n  const getBobinaStatusColor = (bobina, metriRichiesti) => {\n    if (!bobina || bobina.id_bobina === 'BOBINA_VUOTA') return 'default';\n    if (metriRichiesti > bobina.metri_residui) return 'error';\n    if (bobina.metri_residui < bobina.metri_totali * 0.1) return 'warning';\n    return 'success';\n  };\n\n  // Componente per la vista mobile/responsive di ogni cavo\n  const CavoCardMobile = ({\n    cavo\n  }) => {\n    var _cavo$metratura_teori, _datiPosa$cavo$id_cav, _datiPosa$cavo$id_cav2, _datiPosa$cavo$id_cav4, _datiPosa$cavo$id_cav5, _datiPosa$cavo$id_cav8, _datiPosa$cavo$id_cav0, _datiPosa$cavo$id_cav1, _datiPosa$cavo$id_cav10;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      variant: \"outlined\",\n      sx: {\n        mb: 2,\n        border: validationErrors[cavo.id_cavo] ? '2px solid #f44336' : validationWarnings[cavo.id_cavo] ? '2px solid #ff9800' : '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n        title: /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 1,\n          children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n            color: \"primary\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            component: \"span\",\n            children: cavo.id_cavo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: cavo.stato_installazione === 'Installato' ? 'Installato' : 'Da Installare',\n            color: cavo.stato_installazione === 'Installato' ? 'success' : 'warning',\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this),\n        subheader: `${cavo.tipologia || 'N/A'} - ${cavo.formazione || 'N/A'}`,\n        sx: {\n          pb: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          pt: 0\n        },\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              gutterBottom: true,\n              children: \"Metri Teorici\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              fontWeight: \"bold\",\n              children: [((_cavo$metratura_teori = cavo.metratura_teorica) === null || _cavo$metratura_teori === void 0 ? void 0 : _cavo$metratura_teori.toFixed(1)) || '0.0', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              gutterBottom: true,\n              children: \"Metri Posati *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              type: \"number\",\n              size: \"small\",\n              fullWidth: true,\n              value: ((_datiPosa$cavo$id_cav = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav === void 0 ? void 0 : _datiPosa$cavo$id_cav.metratura_reale) || 0,\n              onChange: e => handleMetriChange(cavo.id_cavo, e.target.value),\n              error: !!validationErrors[cavo.id_cavo],\n              helperText: validationErrors[cavo.id_cavo] || validationWarnings[cavo.id_cavo],\n              color: validationWarnings[cavo.id_cavo] && !validationErrors[cavo.id_cavo] ? 'warning' : 'primary',\n              inputProps: {\n                min: 0,\n                step: 0.1,\n                style: {\n                  textAlign: 'right'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              gutterBottom: true,\n              children: \"Bobina Associata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Autocomplete, {\n              size: \"small\",\n              fullWidth: true,\n              value: ((_datiPosa$cavo$id_cav2 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav2 === void 0 ? void 0 : _datiPosa$cavo$id_cav2.id_bobina) === 'BOBINA_VUOTA' ? {\n                id_bobina: 'BOBINA_VUOTA',\n                tipologia: 'Vuota',\n                metri_residui: '∞'\n              } : bobineDisponibili.find(b => {\n                var _datiPosa$cavo$id_cav3;\n                return b.id_bobina === ((_datiPosa$cavo$id_cav3 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav3 === void 0 ? void 0 : _datiPosa$cavo$id_cav3.id_bobina);\n              }) || null,\n              onChange: (event, newValue) => {\n                handleBobinaChange(cavo.id_cavo, (newValue === null || newValue === void 0 ? void 0 : newValue.id_bobina) || '');\n              },\n              options: [{\n                id_bobina: 'BOBINA_VUOTA',\n                tipologia: 'Vuota',\n                metri_residui: '∞'\n              }, ...getBobineCompatibili(cavo)],\n              getOptionLabel: option => {\n                if (option.id_bobina === 'BOBINA_VUOTA') return 'BOBINA_VUOTA';\n                return `${option.id_bobina} (${option.metri_residui}m)`;\n              },\n              renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                ...params,\n                placeholder: \"Seleziona bobina...\",\n                variant: \"outlined\",\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 17\n              }, this),\n              loading: loadingBobine,\n              disabled: loadingBobine\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 13\n            }, this), ((_datiPosa$cavo$id_cav4 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav4 === void 0 ? void 0 : _datiPosa$cavo$id_cav4.id_bobina) && ((_datiPosa$cavo$id_cav5 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav5 === void 0 ? void 0 : _datiPosa$cavo$id_cav5.id_bobina) !== 'BOBINA_VUOTA' && (_datiPosa$cavo$id_cav7 => {\n              const bobina = bobineDisponibili.find(b => {\n                var _datiPosa$cavo$id_cav6;\n                return b.id_bobina === ((_datiPosa$cavo$id_cav6 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav6 === void 0 ? void 0 : _datiPosa$cavo$id_cav6.id_bobina);\n              });\n              const metriRichiesti = ((_datiPosa$cavo$id_cav7 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav7 === void 0 ? void 0 : _datiPosa$cavo$id_cav7.metratura_reale) || 0;\n              return bobina && metriRichiesti > bobina.metri_residui;\n            })() && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                variant: (_datiPosa$cavo$id_cav8 = datiPosa[cavo.id_cavo]) !== null && _datiPosa$cavo$id_cav8 !== void 0 && _datiPosa$cavo$id_cav8.force_over ? \"contained\" : \"outlined\",\n                color: \"warning\",\n                onClick: () => {\n                  var _datiPosa$cavo$id_cav9;\n                  return handleForceOverChange(cavo.id_cavo, !((_datiPosa$cavo$id_cav9 = datiPosa[cavo.id_cavo]) !== null && _datiPosa$cavo$id_cav9 !== void 0 && _datiPosa$cavo$id_cav9.force_over));\n                },\n                startIcon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 30\n                }, this),\n                fullWidth: true,\n                children: \"Forza Over\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              gutterBottom: true,\n              children: \"Dettagli Lavoro\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 4,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              gutterBottom: true,\n              children: \"N. Persone\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              type: \"number\",\n              size: \"small\",\n              fullWidth: true,\n              value: ((_datiPosa$cavo$id_cav0 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav0 === void 0 ? void 0 : _datiPosa$cavo$id_cav0.numero_persone_impiegate) || 1,\n              onChange: e => handlePersoneChange(cavo.id_cavo, e.target.value),\n              inputProps: {\n                min: 1,\n                max: 20\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 4,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              gutterBottom: true,\n              children: \"Sistemazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"center\",\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                checked: ((_datiPosa$cavo$id_cav1 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav1 === void 0 ? void 0 : _datiPosa$cavo$id_cav1.sistemazione) || false,\n                onChange: e => handleSistemazioneChange(cavo.id_cavo, e.target.checked),\n                color: \"primary\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 4,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              gutterBottom: true,\n              children: \"Fascettatura\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              justifyContent: \"center\",\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                checked: ((_datiPosa$cavo$id_cav10 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav10 === void 0 ? void 0 : _datiPosa$cavo$id_cav10.fascettatura) || false,\n                onChange: e => handleFascettaturaChange(cavo.id_cavo, e.target.checked),\n                color: \"primary\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 5\n    }, this);\n  };\n  if (!comanda) return null;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: isMobile ? \"sm\" : \"lg\",\n    fullWidth: true,\n    fullScreen: isSmallScreen,\n    PaperProps: {\n      sx: {\n        minHeight: isSmallScreen ? '100vh' : '70vh',\n        margin: isSmallScreen ? 0 : 2,\n        borderRadius: isSmallScreen ? 0 : 2\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        pb: 1,\n        borderBottom: '1px solid #e0e0e0'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 2,\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          flex: 1,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: isSmallScreen ? \"h6\" : \"h5\",\n            children: \"Inserimento Metri Posati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: isSmallScreen ? \"column\" : \"row\",\n            alignItems: isSmallScreen ? \"flex-start\" : \"center\",\n            gap: 1,\n            mt: 1,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: [\"Comanda: \", comanda.codice_comanda]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: getTipoComandaLabel(comanda.tipo_comanda),\n              color: getTipoComandaColor(comanda.tipo_comanda),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: [\"Responsabile: \", comanda.responsabile]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 656,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 636,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 632,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      sx: {\n        p: isSmallScreen ? 1 : 2,\n        maxHeight: isSmallScreen ? 'calc(100vh - 140px)' : 'none',\n        overflowY: 'auto'\n      },\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"center\",\n        p: 3,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: \"Caricamento cavi...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 676,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: isMobile ? /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"\\u2022 Inserire metri > 0 per installare il cavo\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 67\n              }, this), \"\\u2022 I cavi con 0 metri rimangono in comanda\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 62\n              }, this), \"\\u2022 I cavi gi\\xE0 installati non vengono mostrati\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 65\n              }, this), \"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Warning arancione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 23\n              }, this), \": metri > 10% teorico (non bloccante)\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 97\n              }, this), \"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Errore rosso\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 23\n              }, this), \": metri > bobina disponibile (bloccante)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 17\n          }, this), cavi.map(cavo => /*#__PURE__*/_jsxDEV(CavoCardMobile, {\n            cavo: cavo\n          }, cavo.id_cavo, false, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 19\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 15\n        }, this) :\n        /*#__PURE__*/\n        /* Vista Desktop - Tabella */\n        _jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"\\u2022 Inserire metri > 0 per installare il cavo\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 67\n              }, this), \"\\u2022 I cavi con 0 metri rimangono in comanda\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 703,\n                columnNumber: 62\n              }, this), \"\\u2022 I cavi gi\\xE0 installati non vengono mostrati\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 700,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            variant: \"outlined\",\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"ID Cavo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 711,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Tipologia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 712,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Formazione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 713,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Teorici\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 714,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Reali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Bobina\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"N. Persone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 717,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Sistemazione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 718,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Fascettatura\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 710,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: cavi.map(cavo => {\n                  var _cavo$metratura_teori2, _datiPosa$cavo$id_cav11, _datiPosa$cavo$id_cav12, _datiPosa$cavo$id_cav15, _datiPosa$cavo$id_cav16, _datiPosa$cavo$id_cav19, _datiPosa$cavo$id_cav21, _datiPosa$cavo$id_cav22, _datiPosa$cavo$id_cav23;\n                  return /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        fontWeight: \"bold\",\n                        children: cavo.id_cavo\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 726,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 725,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: cavo.tipologia || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 730,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: cavo.formazione || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 731,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        children: [((_cavo$metratura_teori2 = cavo.metratura_teorica) === null || _cavo$metratura_teori2 === void 0 ? void 0 : _cavo$metratura_teori2.toFixed(1)) || '0.0', \" m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 733,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 732,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(TextField, {\n                        type: \"number\",\n                        size: \"small\",\n                        value: ((_datiPosa$cavo$id_cav11 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav11 === void 0 ? void 0 : _datiPosa$cavo$id_cav11.metratura_reale) || 0,\n                        onChange: e => handleMetriChange(cavo.id_cavo, e.target.value),\n                        error: !!validationErrors[cavo.id_cavo],\n                        helperText: validationErrors[cavo.id_cavo] || validationWarnings[cavo.id_cavo],\n                        color: validationWarnings[cavo.id_cavo] && !validationErrors[cavo.id_cavo] ? 'warning' : 'primary',\n                        inputProps: {\n                          min: 0,\n                          step: 0.1,\n                          style: {\n                            textAlign: 'right'\n                          }\n                        },\n                        sx: {\n                          width: 100\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 738,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 737,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          minWidth: 200\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Autocomplete, {\n                          size: \"small\",\n                          value: ((_datiPosa$cavo$id_cav12 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav12 === void 0 ? void 0 : _datiPosa$cavo$id_cav12.id_bobina) === 'BOBINA_VUOTA' ? {\n                            id_bobina: 'BOBINA_VUOTA',\n                            tipologia: 'Vuota',\n                            metri_residui: '∞'\n                          } : bobineDisponibili.find(b => {\n                            var _datiPosa$cavo$id_cav13;\n                            return b.id_bobina === ((_datiPosa$cavo$id_cav13 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav13 === void 0 ? void 0 : _datiPosa$cavo$id_cav13.id_bobina);\n                          }) || null,\n                          onChange: (event, newValue) => {\n                            handleBobinaChange(cavo.id_cavo, (newValue === null || newValue === void 0 ? void 0 : newValue.id_bobina) || '');\n                          },\n                          options: [{\n                            id_bobina: 'BOBINA_VUOTA',\n                            tipologia: 'Vuota',\n                            metri_residui: '∞'\n                          }, ...getBobineCompatibili(cavo)],\n                          noOptionsText: loadingBobine ? \"Caricamento bobine...\" : bobineDisponibili.length === 0 ? \"Bobine non disponibili - usa BOBINA_VUOTA\" : \"Nessuna bobina compatibile - usa BOBINA_VUOTA\",\n                          getOptionLabel: option => {\n                            if (option.id_bobina === 'BOBINA_VUOTA') return 'BOBINA_VUOTA';\n                            return `${option.id_bobina} (${option.metri_residui}m)`;\n                          },\n                          renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                            ...params,\n                            placeholder: \"Seleziona bobina...\",\n                            variant: \"outlined\",\n                            size: \"small\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 782,\n                            columnNumber: 29\n                          }, this),\n                          renderOption: (props, option) => {\n                            var _datiPosa$cavo$id_cav14;\n                            return /*#__PURE__*/_jsxDEV(Box, {\n                              component: \"li\",\n                              ...props,\n                              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                                children: /*#__PURE__*/_jsxDEV(BobinaIcon, {\n                                  fontSize: \"small\",\n                                  color: option.id_bobina === 'BOBINA_VUOTA' ? 'default' : getBobinaStatusColor(option, ((_datiPosa$cavo$id_cav14 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav14 === void 0 ? void 0 : _datiPosa$cavo$id_cav14.metratura_reale) || 0)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 792,\n                                  columnNumber: 33\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 791,\n                                columnNumber: 31\n                              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                                primary: option.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA_VUOTA' : option.id_bobina,\n                                secondary: option.id_bobina === 'BOBINA_VUOTA' ? 'Nessuna bobina associata' : `${option.tipologia} ${option.sezione} - ${option.metri_residui}m residui`\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 801,\n                                columnNumber: 31\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 790,\n                              columnNumber: 29\n                            }, this);\n                          },\n                          loading: loadingBobine,\n                          disabled: loadingBobine,\n                          sx: {\n                            width: '100%'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 756,\n                          columnNumber: 25\n                        }, this), ((_datiPosa$cavo$id_cav15 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav15 === void 0 ? void 0 : _datiPosa$cavo$id_cav15.id_bobina) && ((_datiPosa$cavo$id_cav16 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav16 === void 0 ? void 0 : _datiPosa$cavo$id_cav16.id_bobina) !== 'BOBINA_VUOTA' && (_datiPosa$cavo$id_cav18 => {\n                          const bobina = bobineDisponibili.find(b => {\n                            var _datiPosa$cavo$id_cav17;\n                            return b.id_bobina === ((_datiPosa$cavo$id_cav17 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav17 === void 0 ? void 0 : _datiPosa$cavo$id_cav17.id_bobina);\n                          });\n                          const metriRichiesti = ((_datiPosa$cavo$id_cav18 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav18 === void 0 ? void 0 : _datiPosa$cavo$id_cav18.metratura_reale) || 0;\n                          return bobina && metriRichiesti > bobina.metri_residui;\n                        })() && /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            mt: 1\n                          },\n                          children: /*#__PURE__*/_jsxDEV(Button, {\n                            size: \"small\",\n                            variant: (_datiPosa$cavo$id_cav19 = datiPosa[cavo.id_cavo]) !== null && _datiPosa$cavo$id_cav19 !== void 0 && _datiPosa$cavo$id_cav19.force_over ? \"contained\" : \"outlined\",\n                            color: \"warning\",\n                            onClick: () => {\n                              var _datiPosa$cavo$id_cav20;\n                              return handleForceOverChange(cavo.id_cavo, !((_datiPosa$cavo$id_cav20 = datiPosa[cavo.id_cavo]) !== null && _datiPosa$cavo$id_cav20 !== void 0 && _datiPosa$cavo$id_cav20.force_over));\n                            },\n                            startIcon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 829,\n                              columnNumber: 42\n                            }, this),\n                            children: \"Forza Over\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 824,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 823,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 755,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 754,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(TextField, {\n                        type: \"number\",\n                        size: \"small\",\n                        value: ((_datiPosa$cavo$id_cav21 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav21 === void 0 ? void 0 : _datiPosa$cavo$id_cav21.numero_persone_impiegate) || 1,\n                        onChange: e => handlePersoneChange(cavo.id_cavo, e.target.value),\n                        inputProps: {\n                          min: 1,\n                          max: 20\n                        },\n                        sx: {\n                          width: 80\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 838,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 837,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                        checked: ((_datiPosa$cavo$id_cav22 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav22 === void 0 ? void 0 : _datiPosa$cavo$id_cav22.sistemazione) || false,\n                        onChange: e => handleSistemazioneChange(cavo.id_cavo, e.target.checked),\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 848,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 847,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                        checked: ((_datiPosa$cavo$id_cav23 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav23 === void 0 ? void 0 : _datiPosa$cavo$id_cav23.fascettatura) || false,\n                        onChange: e => handleFascettaturaChange(cavo.id_cavo, e.target.checked),\n                        color: \"primary\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 855,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 854,\n                      columnNumber: 21\n                    }, this)]\n                  }, cavo.id_cavo, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 724,\n                    columnNumber: 19\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 15\n        }, this)\n      }, void 0, false), cavi.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: \"Nessun cavo assegnato a questa comanda.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 872,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        p: 2,\n        gap: 1,\n        flexDirection: isSmallScreen ? 'column' : 'row',\n        borderTop: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        startIcon: !isSmallScreen && /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 886,\n          columnNumber: 40\n        }, this),\n        disabled: loading,\n        fullWidth: isSmallScreen,\n        size: isSmallScreen ? \"large\" : \"medium\",\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 884,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSave,\n        variant: \"contained\",\n        startIcon: !isSmallScreen && /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 896,\n          columnNumber: 40\n        }, this),\n        disabled: loading || cavi.length === 0,\n        fullWidth: isSmallScreen,\n        size: isSmallScreen ? \"large\" : \"medium\",\n        children: \"Salva Dati Posa\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 893,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 878,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 618,\n    columnNumber: 5\n  }, this);\n};\n_s(InserimentoMetriDialog, \"JO3WIJpTRx/CL8VhjdoIbx7Kzmc=\", false, function () {\n  return [useTheme, useMediaQuery, useMediaQuery, useMediaQuery];\n});\n_c = InserimentoMetriDialog;\nexport default InserimentoMetriDialog;\nvar _c;\n$RefreshReg$(_c, \"InserimentoMetriDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Grid", "Typography", "Box", "<PERSON><PERSON>", "Chip", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "IconButton", "<PERSON><PERSON><PERSON>", "Autocomplete", "FormControl", "InputLabel", "Select", "MenuItem", "ListItemText", "ListItemIcon", "Checkbox", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "<PERSON><PERSON>", "useTheme", "useMediaQuery", "Accordion", "AccordionSummary", "AccordionDetails", "Collapse", "Save", "SaveIcon", "Cancel", "CancelIcon", "Cable", "CableIcon", "CheckCircle", "CheckCircleIcon", "Warning", "WarningIcon", "Storage", "BobinaIcon", "Error", "ErrorIcon", "Info", "InfoIcon", "ExpandMore", "ExpandMoreIcon", "Person", "PersonIcon", "Build", "BuildIcon", "Link", "LinkIcon", "comandeService", "caviService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserimentoMetriDialog", "open", "onClose", "comanda", "onSuccess", "_s", "loading", "setLoading", "error", "setError", "cavi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setDatiPosa", "validationErrors", "setValidationErrors", "validationWarnings", "setValidationWarnings", "bobine<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setBobineDisponibili", "loadingBobine", "setLoadingBob<PERSON>", "theme", "isMobile", "breakpoints", "down", "isTablet", "isSmallScreen", "loadCaviComanda", "loadBobineDisponibili", "caviData", "getCaviComanda", "codice_comanda", "initialDati", "for<PERSON>ach", "cavo", "id_cavo", "metratura_reale", "tipo_comanda", "data_posa", "Date", "toISOString", "split", "responsabile_posa", "responsabile", "numero_persone_impiegate", "numero_componenti_squadra", "sistemazione", "fascettatura", "id_bobina", "force_over", "err", "console", "id_cantiere", "warn", "log", "bobine<PERSON><PERSON>", "getBobineDisponibili", "length", "status", "isNetworkError", "handleMetriChange", "idCavo", "value", "numericValue", "parseFloat", "prev", "newDatiPosa", "find", "c", "errors", "warnings", "metratura_teorica", "push", "datiCavo", "bobina", "b", "metri_residui", "prevErrors", "join", "newErrors", "prevWarnings", "newWarnings", "handlePersoneChange", "parseInt", "handleSistemazioneChange", "checked", "handleFascettaturaChange", "handleBobinaChange", "bobina<PERSON>d", "setTimeout", "handleForceOverChange", "forceOver", "handleSave", "hasErrors", "Object", "keys", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_datiPosa$idCavo", "metri", "caviDaInstallare", "caviTotali", "aggiornaDatiPosaConBobine", "messaggioSuccesso", "message", "getTipoComandaColor", "tipo", "getTipoComandaLabel", "getBobineCompatibili", "tipologia", "formazione", "map", "sezione", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "filter", "tipologiaMatch", "sezioneMatch", "metriOk", "bobina_tipologia", "cavo_tipologia", "bobina_sezione", "cavo_formazione", "isBobinaCompatibile", "getBobinaStatusColor", "metriRichiesti", "metri_totali", "CavoCardMobile", "_cavo$metratura_teori", "_datiPosa$cavo$id_cav", "_datiPosa$cavo$id_cav2", "_datiPosa$cavo$id_cav4", "_datiPosa$cavo$id_cav5", "_datiPosa$cavo$id_cav8", "_datiPosa$cavo$id_cav0", "_datiPosa$cavo$id_cav1", "_datiPosa$cavo$id_cav10", "variant", "sx", "mb", "border", "children", "title", "display", "alignItems", "gap", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "label", "stato_installazione", "size", "subheader", "pb", "pt", "container", "spacing", "item", "xs", "gutterBottom", "fontWeight", "toFixed", "type", "fullWidth", "onChange", "e", "target", "helperText", "inputProps", "min", "step", "style", "textAlign", "_datiPosa$cavo$id_cav3", "event", "newValue", "options", "getOptionLabel", "option", "renderInput", "params", "placeholder", "disabled", "_datiPosa$cavo$id_cav7", "_datiPosa$cavo$id_cav6", "mt", "onClick", "_datiPosa$cavo$id_cav9", "startIcon", "my", "max", "justifyContent", "max<PERSON><PERSON><PERSON>", "fullScreen", "PaperProps", "minHeight", "margin", "borderRadius", "borderBottom", "flex", "direction", "p", "maxHeight", "overflowY", "severity", "_cavo$metratura_teori2", "_datiPosa$cavo$id_cav11", "_datiPosa$cavo$id_cav12", "_datiPosa$cavo$id_cav15", "_datiPosa$cavo$id_cav16", "_datiPosa$cavo$id_cav19", "_datiPosa$cavo$id_cav21", "_datiPosa$cavo$id_cav22", "_datiPosa$cavo$id_cav23", "width", "min<PERSON><PERSON><PERSON>", "_datiPosa$cavo$id_cav13", "noOptionsText", "renderOption", "props", "_datiPosa$cavo$id_cav14", "primary", "secondary", "_datiPosa$cavo$id_cav18", "_datiPosa$cavo$id_cav17", "_datiPosa$cavo$id_cav20", "flexDirection", "borderTop", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/InserimentoMetriDialog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  TextField,\n  Grid,\n  Typography,\n  Box,\n  Alert,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  IconButton,\n  Tooltip,\n  Autocomplete,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  ListItemText,\n  ListItemIcon,\n  Checkbox,\n  Card,\n  CardContent,\n  CardHeader,\n  Divider,\n  Stack,\n  useTheme,\n  useMediaQuery,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Collapse\n} from '@mui/material';\nimport {\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Cable as CableIcon,\n  CheckCircle as CheckCircleIcon,\n  Warning as WarningIcon,\n  Storage as BobinaIcon,\n  Error as ErrorIcon,\n  Info as InfoIcon,\n  ExpandMore as ExpandMoreIcon,\n  Person as PersonIcon,\n  Build as BuildIcon,\n  Link as LinkIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport caviService from '../../services/caviService';\n\nconst InserimentoMetriDialog = ({\n  open,\n  onClose,\n  comanda,\n  onSuccess\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [cavi, setCavi] = useState([]);\n  const [datiPosa, setDatiPosa] = useState({});\n  const [validationErrors, setValidationErrors] = useState({});\n  const [validationWarnings, setValidationWarnings] = useState({});\n  const [bobineDisponibili, setBobineDisponibili] = useState([]);\n  const [loadingBobine, setLoadingBobine] = useState(false);\n\n  // Responsive design hooks\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));\n  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));\n\n  useEffect(() => {\n    if (open && comanda) {\n      loadCaviComanda();\n      loadBobineDisponibili();\n    }\n  }, [open, comanda]);\n\n  const loadCaviComanda = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const caviData = await comandeService.getCaviComanda(comanda.codice_comanda);\n      setCavi(caviData);\n      \n      // Inizializza i dati di posa per ogni cavo\n      const initialDati = {};\n      caviData.forEach(cavo => {\n        initialDati[cavo.id_cavo] = {\n          // Per comande POSA: metri reali devono essere 0 (azzerati)\n          // Per altre comande: mantieni il valore esistente\n          metratura_reale: comanda.tipo_comanda === 'POSA' ? 0 : (cavo.metratura_reale || 0),\n          data_posa: new Date().toISOString().split('T')[0],\n          responsabile_posa: comanda.responsabile || '',\n          numero_persone_impiegate: comanda.numero_componenti_squadra || 1,\n          sistemazione: false,\n          fascettatura: false,\n          id_bobina: cavo.id_bobina || '', // Bobina attualmente associata\n          force_over: false // Flag per forzare associazione anche se metri insufficienti\n        };\n      });\n      setDatiPosa(initialDati);\n      \n    } catch (err) {\n      console.error('Errore nel caricamento cavi:', err);\n      setError('Errore nel caricamento dei cavi della comanda');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadBobineDisponibili = async () => {\n    try {\n      setLoadingBobine(true);\n\n      // Verifica che ci sia un cantiere valido\n      if (!comanda?.id_cantiere) {\n        console.warn('ID cantiere non disponibile per il caricamento bobine');\n        return;\n      }\n\n      console.log(`Caricamento bobine per cantiere ${comanda.id_cantiere}`);\n\n      // Carica tutte le bobine disponibili per il cantiere\n      const bobineData = await caviService.getBobineDisponibili(comanda.id_cantiere);\n      setBobineDisponibili(bobineData);\n\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n    } catch (err) {\n      console.error('Errore nel caricamento bobine:', err);\n\n      // Gestione specifica per errori di autenticazione\n      if (err.status === 401) {\n        console.warn('Errore 401: Sessione scaduta o non autenticato');\n        setError('Sessione scaduta. Effettua nuovamente il login per caricare le bobine.');\n      } else if (err.isNetworkError) {\n        console.warn('Errore di rete nel caricamento bobine');\n        setError('Impossibile connettersi al server per caricare le bobine.');\n      } else {\n        console.warn('Errore generico nel caricamento bobine:', err);\n        setError('Errore nel caricamento delle bobine. Puoi comunque usare BOBINA_VUOTA.');\n      }\n\n      // Imposta array vuoto per evitare errori nell'interfaccia\n      setBobineDisponibili([]);\n    } finally {\n      setLoadingBobine(false);\n    }\n  };\n\n  const handleMetriChange = (idCavo, value) => {\n    const numericValue = parseFloat(value) || 0;\n\n    // Aggiorna i dati di posa\n    setDatiPosa(prev => {\n      const newDatiPosa = {\n        ...prev,\n        [idCavo]: {\n          ...prev[idCavo],\n          metratura_reale: numericValue\n        }\n      };\n\n      // Validazione metri vs teorico e bobina\n      const cavo = cavi.find(c => c.id_cavo === idCavo);\n      let errors = [];\n      let warnings = [];\n\n      // WARNING: Metratura superiore del 10% (non bloccante)\n      if (cavo && numericValue > cavo.metratura_teorica * 1.1) {\n        warnings.push('Metratura superiore del 10% rispetto al teorico');\n      }\n\n      // ERROR: Validazione metri vs bobina selezionata (bloccante)\n      const datiCavo = newDatiPosa[idCavo];\n      if (datiCavo?.id_bobina && datiCavo.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobineDisponibili.find(b => b.id_bobina === datiCavo.id_bobina);\n        if (bobina && numericValue > bobina.metri_residui && !datiCavo.force_over) {\n          errors.push(`Bobina ha solo ${bobina.metri_residui}m residui (richiesti ${numericValue}m)`);\n        }\n      }\n\n      // Aggiorna gli errori di validazione (solo errori bloccanti)\n      setValidationErrors(prevErrors => {\n        if (errors.length > 0) {\n          return {\n            ...prevErrors,\n            [idCavo]: errors.join('; ')\n          };\n        } else {\n          const newErrors = { ...prevErrors };\n          delete newErrors[idCavo];\n          return newErrors;\n        }\n      });\n\n      // Aggiorna i warning di validazione (non bloccanti)\n      setValidationWarnings(prevWarnings => {\n        if (warnings.length > 0) {\n          return {\n            ...prevWarnings,\n            [idCavo]: warnings.join('; ')\n          };\n        } else {\n          const newWarnings = { ...prevWarnings };\n          delete newWarnings[idCavo];\n          return newWarnings;\n        }\n      });\n\n      return newDatiPosa;\n    });\n  };\n\n  const handlePersoneChange = (idCavo, value) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        numero_persone_impiegate: parseInt(value) || 1\n      }\n    }));\n  };\n\n  const handleSistemazioneChange = (idCavo, checked) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        sistemazione: checked\n      }\n    }));\n  };\n\n  const handleFascettaturaChange = (idCavo, checked) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        fascettatura: checked\n      }\n    }));\n  };\n\n  const handleBobinaChange = (idCavo, bobinaId) => {\n    setDatiPosa(prev => {\n      const newDatiPosa = {\n        ...prev,\n        [idCavo]: {\n          ...prev[idCavo],\n          id_bobina: bobinaId,\n          force_over: false // Reset force_over quando cambia bobina\n        }\n      };\n\n      // Rivalidazione dopo cambio bobina\n      const datiCavo = newDatiPosa[idCavo];\n      if (datiCavo?.metratura_reale) {\n        // Usa setTimeout per evitare problemi di stato asincrono\n        setTimeout(() => {\n          handleMetriChange(idCavo, datiCavo.metratura_reale);\n        }, 0);\n      }\n\n      return newDatiPosa;\n    });\n  };\n\n  const handleForceOverChange = (idCavo, forceOver) => {\n    setDatiPosa(prev => {\n      const newDatiPosa = {\n        ...prev,\n        [idCavo]: {\n          ...prev[idCavo],\n          force_over: forceOver\n        }\n      };\n\n      // Rivalidazione dopo cambio force_over\n      const datiCavo = newDatiPosa[idCavo];\n      if (datiCavo?.metratura_reale) {\n        // Usa setTimeout per evitare problemi di stato asincrono\n        setTimeout(() => {\n          handleMetriChange(idCavo, datiCavo.metratura_reale);\n        }, 0);\n      }\n\n      return newDatiPosa;\n    });\n  };\n\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Validazione finale\n      const hasErrors = Object.keys(validationErrors).length > 0;\n      if (hasErrors) {\n        setError('Correggere gli errori di validazione prima di salvare');\n        return;\n      }\n\n      // Filtra solo i cavi con metri > 0 per l'installazione\n      const datiPosaFiltrati = {};\n      Object.keys(datiPosa).forEach(idCavo => {\n        const metri = parseFloat(datiPosa[idCavo]?.metratura_reale || 0);\n        if (metri > 0) {\n          datiPosaFiltrati[idCavo] = datiPosa[idCavo];\n        }\n      });\n\n      console.log('Dati posa originali:', datiPosa);\n      console.log('Dati posa filtrati (solo metri > 0):', datiPosaFiltrati);\n\n      const caviDaInstallare = Object.keys(datiPosaFiltrati).length;\n      const caviTotali = Object.keys(datiPosa).length;\n\n      if (caviDaInstallare === 0) {\n        setError('Nessun cavo da installare. Inserire metri > 0 per almeno un cavo.');\n        return;\n      }\n\n      console.log(`Installando ${caviDaInstallare} cavi su ${caviTotali} totali`);\n\n      // Salva i dati di posa con associazioni bobine (solo cavi con metri > 0)\n      await comandeService.aggiornaDatiPosaConBobine(comanda.codice_comanda, datiPosaFiltrati);\n\n      const messaggioSuccesso = caviDaInstallare === caviTotali\n        ? `Tutti i ${caviDaInstallare} cavi installati con successo`\n        : `${caviDaInstallare} cavi installati su ${caviTotali}. I rimanenti ${caviTotali - caviDaInstallare} cavi rimangono in comanda.`;\n\n      onSuccess?.(messaggioSuccesso);\n      onClose();\n      \n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.message || 'Errore nel salvataggio dei dati');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getTipoComandaColor = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'primary';\n      case 'COLLEGAMENTO_PARTENZA': return 'warning';\n      case 'COLLEGAMENTO_ARRIVO': return 'info';\n      case 'CERTIFICAZIONE': return 'success';\n      default: return 'default';\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA': return 'Coll. Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Coll. Arrivo';\n      case 'CERTIFICAZIONE': return 'Certificazione';\n      default: return tipo;\n    }\n  };\n\n  // Filtra bobine compatibili per un cavo specifico\n  const getBobineCompatibili = (cavo) => {\n    if (!bobineDisponibili || bobineDisponibili.length === 0) {\n      console.log('Nessuna bobina disponibile per il filtro');\n      return [];\n    }\n\n    console.log('Filtro compatibilità per cavo:', {\n      id_cavo: cavo.id_cavo,\n      tipologia: cavo.tipologia,\n      formazione: cavo.formazione\n    });\n\n    console.log('Bobine disponibili:', bobineDisponibili.map(b => ({\n      id_bobina: b.id_bobina,\n      tipologia: b.tipologia,\n      sezione: b.sezione,\n      metri_residui: b.metri_residui\n    })));\n\n    const bobineCompatibili = bobineDisponibili.filter(bobina => {\n      // Confronta tipologia\n      const tipologiaMatch = bobina.tipologia === cavo.tipologia;\n\n      // Confronta sezione - il backend usa 'sezione'\n      const sezioneMatch = bobina.sezione === cavo.formazione;\n\n      // Verifica metri residui\n      const metriOk = bobina.metri_residui > 0;\n\n      console.log(`Bobina ${bobina.id_bobina}:`, {\n        tipologiaMatch,\n        sezioneMatch,\n        metriOk,\n        bobina_tipologia: bobina.tipologia,\n        cavo_tipologia: cavo.tipologia,\n        bobina_sezione: bobina.sezione,\n        cavo_formazione: cavo.formazione\n      });\n\n      return tipologiaMatch && sezioneMatch && metriOk;\n    });\n\n    console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`, bobineCompatibili);\n    return bobineCompatibili;\n  };\n\n  // Verifica se una bobina è compatibile con un cavo\n  const isBobinaCompatibile = (bobina, cavo) => {\n    return bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.formazione;\n  };\n\n  // Ottiene il colore per lo stato della bobina\n  const getBobinaStatusColor = (bobina, metriRichiesti) => {\n    if (!bobina || bobina.id_bobina === 'BOBINA_VUOTA') return 'default';\n    if (metriRichiesti > bobina.metri_residui) return 'error';\n    if (bobina.metri_residui < bobina.metri_totali * 0.1) return 'warning';\n    return 'success';\n  };\n\n  // Componente per la vista mobile/responsive di ogni cavo\n  const CavoCardMobile = ({ cavo }) => (\n    <Card\n      variant=\"outlined\"\n      sx={{\n        mb: 2,\n        border: validationErrors[cavo.id_cavo]\n          ? '2px solid #f44336'\n          : validationWarnings[cavo.id_cavo]\n            ? '2px solid #ff9800'\n            : '1px solid #e0e0e0'\n      }}\n    >\n      <CardHeader\n        title={\n          <Box display=\"flex\" alignItems=\"center\" gap={1}>\n            <CableIcon color=\"primary\" fontSize=\"small\" />\n            <Typography variant=\"h6\" component=\"span\">\n              {cavo.id_cavo}\n            </Typography>\n            <Chip\n              label={cavo.stato_installazione === 'Installato' ? 'Installato' : 'Da Installare'}\n              color={cavo.stato_installazione === 'Installato' ? 'success' : 'warning'}\n              size=\"small\"\n            />\n          </Box>\n        }\n        subheader={`${cavo.tipologia || 'N/A'} - ${cavo.formazione || 'N/A'}`}\n        sx={{ pb: 1 }}\n      />\n      <CardContent sx={{ pt: 0 }}>\n        <Grid container spacing={2}>\n          {/* Metri Teorici */}\n          <Grid item xs={6}>\n            <Typography variant=\"body2\" color=\"textSecondary\" gutterBottom>\n              Metri Teorici\n            </Typography>\n            <Typography variant=\"body1\" fontWeight=\"bold\">\n              {cavo.metratura_teorica?.toFixed(1) || '0.0'} m\n            </Typography>\n          </Grid>\n\n          {/* Metri Reali */}\n          <Grid item xs={6}>\n            <Typography variant=\"body2\" color=\"textSecondary\" gutterBottom>\n              Metri Posati *\n            </Typography>\n            <TextField\n              type=\"number\"\n              size=\"small\"\n              fullWidth\n              value={datiPosa[cavo.id_cavo]?.metratura_reale || 0}\n              onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}\n              error={!!validationErrors[cavo.id_cavo]}\n              helperText={validationErrors[cavo.id_cavo] || validationWarnings[cavo.id_cavo]}\n              color={validationWarnings[cavo.id_cavo] && !validationErrors[cavo.id_cavo] ? 'warning' : 'primary'}\n              inputProps={{\n                min: 0,\n                step: 0.1,\n                style: { textAlign: 'right' }\n              }}\n            />\n          </Grid>\n\n          {/* Bobina */}\n          <Grid item xs={12}>\n            <Typography variant=\"body2\" color=\"textSecondary\" gutterBottom>\n              Bobina Associata\n            </Typography>\n            <Autocomplete\n              size=\"small\"\n              fullWidth\n              value={\n                datiPosa[cavo.id_cavo]?.id_bobina === 'BOBINA_VUOTA'\n                  ? { id_bobina: 'BOBINA_VUOTA', tipologia: 'Vuota', metri_residui: '∞' }\n                  : bobineDisponibili.find(b => b.id_bobina === datiPosa[cavo.id_cavo]?.id_bobina) || null\n              }\n              onChange={(event, newValue) => {\n                handleBobinaChange(cavo.id_cavo, newValue?.id_bobina || '');\n              }}\n              options={[\n                { id_bobina: 'BOBINA_VUOTA', tipologia: 'Vuota', metri_residui: '∞' },\n                ...getBobineCompatibili(cavo)\n              ]}\n              getOptionLabel={(option) => {\n                if (option.id_bobina === 'BOBINA_VUOTA') return 'BOBINA_VUOTA';\n                return `${option.id_bobina} (${option.metri_residui}m)`;\n              }}\n              renderInput={(params) => (\n                <TextField\n                  {...params}\n                  placeholder=\"Seleziona bobina...\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n              )}\n              loading={loadingBobine}\n              disabled={loadingBobine}\n            />\n\n            {/* Pulsante Force Over se necessario */}\n            {datiPosa[cavo.id_cavo]?.id_bobina &&\n             datiPosa[cavo.id_cavo]?.id_bobina !== 'BOBINA_VUOTA' &&\n             (() => {\n               const bobina = bobineDisponibili.find(b => b.id_bobina === datiPosa[cavo.id_cavo]?.id_bobina);\n               const metriRichiesti = datiPosa[cavo.id_cavo]?.metratura_reale || 0;\n               return bobina && metriRichiesti > bobina.metri_residui;\n             })() && (\n              <Box sx={{ mt: 1 }}>\n                <Button\n                  size=\"small\"\n                  variant={datiPosa[cavo.id_cavo]?.force_over ? \"contained\" : \"outlined\"}\n                  color=\"warning\"\n                  onClick={() => handleForceOverChange(cavo.id_cavo, !datiPosa[cavo.id_cavo]?.force_over)}\n                  startIcon={<WarningIcon />}\n                  fullWidth\n                >\n                  Forza Over\n                </Button>\n              </Box>\n            )}\n          </Grid>\n\n          {/* Sezione Dettagli Lavoro */}\n          <Grid item xs={12}>\n            <Divider sx={{ my: 1 }} />\n            <Typography variant=\"body2\" color=\"textSecondary\" gutterBottom>\n              Dettagli Lavoro\n            </Typography>\n          </Grid>\n\n          {/* Numero Persone */}\n          <Grid item xs={4}>\n            <Typography variant=\"body2\" color=\"textSecondary\" gutterBottom>\n              N. Persone\n            </Typography>\n            <TextField\n              type=\"number\"\n              size=\"small\"\n              fullWidth\n              value={datiPosa[cavo.id_cavo]?.numero_persone_impiegate || 1}\n              onChange={(e) => handlePersoneChange(cavo.id_cavo, e.target.value)}\n              inputProps={{ min: 1, max: 20 }}\n            />\n          </Grid>\n\n          {/* Sistemazione */}\n          <Grid item xs={4}>\n            <Typography variant=\"body2\" color=\"textSecondary\" gutterBottom>\n              Sistemazione\n            </Typography>\n            <Box display=\"flex\" justifyContent=\"center\">\n              <Checkbox\n                checked={datiPosa[cavo.id_cavo]?.sistemazione || false}\n                onChange={(e) => handleSistemazioneChange(cavo.id_cavo, e.target.checked)}\n                color=\"primary\"\n                size=\"large\"\n              />\n            </Box>\n          </Grid>\n\n          {/* Fascettatura */}\n          <Grid item xs={4}>\n            <Typography variant=\"body2\" color=\"textSecondary\" gutterBottom>\n              Fascettatura\n            </Typography>\n            <Box display=\"flex\" justifyContent=\"center\">\n              <Checkbox\n                checked={datiPosa[cavo.id_cavo]?.fascettatura || false}\n                onChange={(e) => handleFascettaturaChange(cavo.id_cavo, e.target.checked)}\n                color=\"primary\"\n                size=\"large\"\n              />\n            </Box>\n          </Grid>\n        </Grid>\n      </CardContent>\n    </Card>\n  );\n\n  if (!comanda) return null;\n\n  return (\n    <Dialog\n      open={open}\n      onClose={onClose}\n      maxWidth={isMobile ? \"sm\" : \"lg\"}\n      fullWidth\n      fullScreen={isSmallScreen}\n      PaperProps={{\n        sx: {\n          minHeight: isSmallScreen ? '100vh' : '70vh',\n          margin: isSmallScreen ? 0 : 2,\n          borderRadius: isSmallScreen ? 0 : 2\n        }\n      }}\n    >\n      <DialogTitle sx={{\n        pb: 1,\n        borderBottom: '1px solid #e0e0e0'\n      }}>\n        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n          <CableIcon color=\"primary\" />\n          <Box flex={1}>\n            <Typography variant={isSmallScreen ? \"h6\" : \"h5\"}>\n              Inserimento Metri Posati\n            </Typography>\n            <Stack\n              direction={isSmallScreen ? \"column\" : \"row\"}\n              alignItems={isSmallScreen ? \"flex-start\" : \"center\"}\n              gap={1}\n              mt={1}\n            >\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Comanda: {comanda.codice_comanda}\n              </Typography>\n              <Chip\n                label={getTipoComandaLabel(comanda.tipo_comanda)}\n                color={getTipoComandaColor(comanda.tipo_comanda)}\n                size=\"small\"\n              />\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Responsabile: {comanda.responsabile}\n              </Typography>\n            </Stack>\n          </Box>\n        </Box>\n      </DialogTitle>\n\n      <DialogContent sx={{\n        p: isSmallScreen ? 1 : 2,\n        maxHeight: isSmallScreen ? 'calc(100vh - 140px)' : 'none',\n        overflowY: 'auto'\n      }}>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        {loading ? (\n          <Box display=\"flex\" justifyContent=\"center\" p={3}>\n            <Typography>Caricamento cavi...</Typography>\n          </Box>\n        ) : (\n          <>\n            {/* Vista Mobile/Tablet - Cards */}\n            {isMobile ? (\n              <Box>\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  <Typography variant=\"body2\">\n                    • Inserire metri &gt; 0 per installare il cavo<br/>\n                    • I cavi con 0 metri rimangono in comanda<br/>\n                    • I cavi già installati non vengono mostrati<br/>\n                    • <strong>Warning arancione</strong>: metri &gt; 10% teorico (non bloccante)<br/>\n                    • <strong>Errore rosso</strong>: metri &gt; bobina disponibile (bloccante)\n                  </Typography>\n                </Alert>\n                {cavi.map((cavo) => (\n                  <CavoCardMobile key={cavo.id_cavo} cavo={cavo} />\n                ))}\n              </Box>\n            ) : (\n              /* Vista Desktop - Tabella */\n              <Box>\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  <Typography variant=\"body2\">\n                    • Inserire metri &gt; 0 per installare il cavo<br/>\n                    • I cavi con 0 metri rimangono in comanda<br/>\n                    • I cavi già installati non vengono mostrati\n                  </Typography>\n                </Alert>\n                <TableContainer component={Paper} variant=\"outlined\">\n                  <Table size=\"small\">\n                    <TableHead>\n                      <TableRow>\n                        <TableCell>ID Cavo</TableCell>\n                        <TableCell>Tipologia</TableCell>\n                        <TableCell>Formazione</TableCell>\n                        <TableCell>Metri Teorici</TableCell>\n                        <TableCell>Metri Reali</TableCell>\n                        <TableCell>Bobina</TableCell>\n                        <TableCell>N. Persone</TableCell>\n                        <TableCell>Sistemazione</TableCell>\n                        <TableCell>Fascettatura</TableCell>\n                      </TableRow>\n                    </TableHead>\n              <TableBody>\n                {cavi.map((cavo) => (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>\n                      <Typography variant=\"body2\" fontWeight=\"bold\">\n                        {cavo.id_cavo}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.formazione || 'N/A'}</TableCell>\n                    <TableCell>\n                      <Typography variant=\"body2\">\n                        {cavo.metratura_teorica?.toFixed(1) || '0.0'} m\n                      </Typography>\n                    </TableCell>\n                    <TableCell>\n                      <TextField\n                        type=\"number\"\n                        size=\"small\"\n                        value={datiPosa[cavo.id_cavo]?.metratura_reale || 0}\n                        onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}\n                        error={!!validationErrors[cavo.id_cavo]}\n                        helperText={validationErrors[cavo.id_cavo] || validationWarnings[cavo.id_cavo]}\n                        color={validationWarnings[cavo.id_cavo] && !validationErrors[cavo.id_cavo] ? 'warning' : 'primary'}\n                        inputProps={{\n                          min: 0,\n                          step: 0.1,\n                          style: { textAlign: 'right' }\n                        }}\n                        sx={{ width: 100 }}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Box sx={{ minWidth: 200 }}>\n                        <Autocomplete\n                          size=\"small\"\n                          value={\n                            datiPosa[cavo.id_cavo]?.id_bobina === 'BOBINA_VUOTA'\n                              ? { id_bobina: 'BOBINA_VUOTA', tipologia: 'Vuota', metri_residui: '∞' }\n                              : bobineDisponibili.find(b => b.id_bobina === datiPosa[cavo.id_cavo]?.id_bobina) || null\n                          }\n                          onChange={(event, newValue) => {\n                            handleBobinaChange(cavo.id_cavo, newValue?.id_bobina || '');\n                          }}\n                          options={[\n                            { id_bobina: 'BOBINA_VUOTA', tipologia: 'Vuota', metri_residui: '∞' },\n                            ...getBobineCompatibili(cavo)\n                          ]}\n                          noOptionsText={\n                            loadingBobine\n                              ? \"Caricamento bobine...\"\n                              : bobineDisponibili.length === 0\n                                ? \"Bobine non disponibili - usa BOBINA_VUOTA\"\n                                : \"Nessuna bobina compatibile - usa BOBINA_VUOTA\"\n                          }\n                          getOptionLabel={(option) => {\n                            if (option.id_bobina === 'BOBINA_VUOTA') return 'BOBINA_VUOTA';\n                            return `${option.id_bobina} (${option.metri_residui}m)`;\n                          }}\n                          renderInput={(params) => (\n                            <TextField\n                              {...params}\n                              placeholder=\"Seleziona bobina...\"\n                              variant=\"outlined\"\n                              size=\"small\"\n                            />\n                          )}\n                          renderOption={(props, option) => (\n                            <Box component=\"li\" {...props}>\n                              <ListItemIcon>\n                                <BobinaIcon\n                                  fontSize=\"small\"\n                                  color={\n                                    option.id_bobina === 'BOBINA_VUOTA'\n                                      ? 'default'\n                                      : getBobinaStatusColor(option, datiPosa[cavo.id_cavo]?.metratura_reale || 0)\n                                  }\n                                />\n                              </ListItemIcon>\n                              <ListItemText\n                                primary={option.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA_VUOTA' : option.id_bobina}\n                                secondary={\n                                  option.id_bobina === 'BOBINA_VUOTA'\n                                    ? 'Nessuna bobina associata'\n                                    : `${option.tipologia} ${option.sezione} - ${option.metri_residui}m residui`\n                                }\n                              />\n                            </Box>\n                          )}\n                          loading={loadingBobine}\n                          disabled={loadingBobine}\n                          sx={{ width: '100%' }}\n                        />\n                        {/* Checkbox per force_over se necessario */}\n                        {datiPosa[cavo.id_cavo]?.id_bobina &&\n                         datiPosa[cavo.id_cavo]?.id_bobina !== 'BOBINA_VUOTA' &&\n                         (() => {\n                           const bobina = bobineDisponibili.find(b => b.id_bobina === datiPosa[cavo.id_cavo]?.id_bobina);\n                           const metriRichiesti = datiPosa[cavo.id_cavo]?.metratura_reale || 0;\n                           return bobina && metriRichiesti > bobina.metri_residui;\n                         })() && (\n                          <Box sx={{ mt: 1 }}>\n                            <Button\n                              size=\"small\"\n                              variant={datiPosa[cavo.id_cavo]?.force_over ? \"contained\" : \"outlined\"}\n                              color=\"warning\"\n                              onClick={() => handleForceOverChange(cavo.id_cavo, !datiPosa[cavo.id_cavo]?.force_over)}\n                              startIcon={<WarningIcon />}\n                            >\n                              Forza Over\n                            </Button>\n                          </Box>\n                        )}\n                      </Box>\n                    </TableCell>\n                    <TableCell>\n                      <TextField\n                        type=\"number\"\n                        size=\"small\"\n                        value={datiPosa[cavo.id_cavo]?.numero_persone_impiegate || 1}\n                        onChange={(e) => handlePersoneChange(cavo.id_cavo, e.target.value)}\n                        inputProps={{ min: 1, max: 20 }}\n                        sx={{ width: 80 }}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Checkbox\n                        checked={datiPosa[cavo.id_cavo]?.sistemazione || false}\n                        onChange={(e) => handleSistemazioneChange(cavo.id_cavo, e.target.checked)}\n                        color=\"primary\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Checkbox\n                        checked={datiPosa[cavo.id_cavo]?.fascettatura || false}\n                        onChange={(e) => handleFascettaturaChange(cavo.id_cavo, e.target.checked)}\n                        color=\"primary\"\n                      />\n                    </TableCell>\n                  </TableRow>\n                ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n              </Box>\n            )}\n          </>\n        )}\n\n        {cavi.length === 0 && !loading && (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Nessun cavo assegnato a questa comanda.\n          </Alert>\n        )}\n      </DialogContent>\n\n      <DialogActions sx={{\n        p: 2,\n        gap: 1,\n        flexDirection: isSmallScreen ? 'column' : 'row',\n        borderTop: '1px solid #e0e0e0'\n      }}>\n        <Button\n          onClick={onClose}\n          startIcon={!isSmallScreen && <CancelIcon />}\n          disabled={loading}\n          fullWidth={isSmallScreen}\n          size={isSmallScreen ? \"large\" : \"medium\"}\n        >\n          Annulla\n        </Button>\n        <Button\n          onClick={handleSave}\n          variant=\"contained\"\n          startIcon={!isSmallScreen && <SaveIcon />}\n          disabled={loading || cavi.length === 0}\n          fullWidth={isSmallScreen}\n          size={isSmallScreen ? \"large\" : \"medium\"}\n        >\n          Salva Dati Posa\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default InserimentoMetriDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,QAAQ,EACRC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,aAAa,EACbC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,QAAQ,QACH,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,OAAO,IAAIC,WAAW,EACtBC,OAAO,IAAIC,UAAU,EACrBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,cAAc,EAC5BC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,sBAAsB,GAAGA,CAAC;EAC9BC,IAAI;EACJC,OAAO;EACPC,OAAO;EACPC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+E,KAAK,EAAEC,QAAQ,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiF,IAAI,EAAEC,OAAO,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACmF,QAAQ,EAAEC,WAAW,CAAC,GAAGpF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACqF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACuF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChE,MAAM,CAACyF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC2F,aAAa,EAAEC,gBAAgB,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM6F,KAAK,GAAG1D,QAAQ,CAAC,CAAC;EACxB,MAAM2D,QAAQ,GAAG1D,aAAa,CAACyD,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAMC,QAAQ,GAAG7D,aAAa,CAACyD,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5D,MAAME,aAAa,GAAG9D,aAAa,CAACyD,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAEjE/F,SAAS,CAAC,MAAM;IACd,IAAIuE,IAAI,IAAIE,OAAO,EAAE;MACnByB,eAAe,CAAC,CAAC;MACjBC,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAAC5B,IAAI,EAAEE,OAAO,CAAC,CAAC;EAEnB,MAAMyB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFrB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMqB,QAAQ,GAAG,MAAMpC,cAAc,CAACqC,cAAc,CAAC5B,OAAO,CAAC6B,cAAc,CAAC;MAC5ErB,OAAO,CAACmB,QAAQ,CAAC;;MAEjB;MACA,MAAMG,WAAW,GAAG,CAAC,CAAC;MACtBH,QAAQ,CAACI,OAAO,CAACC,IAAI,IAAI;QACvBF,WAAW,CAACE,IAAI,CAACC,OAAO,CAAC,GAAG;UAC1B;UACA;UACAC,eAAe,EAAElC,OAAO,CAACmC,YAAY,KAAK,MAAM,GAAG,CAAC,GAAIH,IAAI,CAACE,eAAe,IAAI,CAAE;UAClFE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACjDC,iBAAiB,EAAExC,OAAO,CAACyC,YAAY,IAAI,EAAE;UAC7CC,wBAAwB,EAAE1C,OAAO,CAAC2C,yBAAyB,IAAI,CAAC;UAChEC,YAAY,EAAE,KAAK;UACnBC,YAAY,EAAE,KAAK;UACnBC,SAAS,EAAEd,IAAI,CAACc,SAAS,IAAI,EAAE;UAAE;UACjCC,UAAU,EAAE,KAAK,CAAC;QACpB,CAAC;MACH,CAAC,CAAC;MACFrC,WAAW,CAACoB,WAAW,CAAC;IAE1B,CAAC,CAAC,OAAOkB,GAAG,EAAE;MACZC,OAAO,CAAC5C,KAAK,CAAC,8BAA8B,EAAE2C,GAAG,CAAC;MAClD1C,QAAQ,CAAC,+CAA+C,CAAC;IAC3D,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACFR,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,IAAI,EAAClB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEkD,WAAW,GAAE;QACzBD,OAAO,CAACE,IAAI,CAAC,uDAAuD,CAAC;QACrE;MACF;MAEAF,OAAO,CAACG,GAAG,CAAC,mCAAmCpD,OAAO,CAACkD,WAAW,EAAE,CAAC;;MAErE;MACA,MAAMG,UAAU,GAAG,MAAM7D,WAAW,CAAC8D,oBAAoB,CAACtD,OAAO,CAACkD,WAAW,CAAC;MAC9ElC,oBAAoB,CAACqC,UAAU,CAAC;MAEhCJ,OAAO,CAACG,GAAG,CAAC,oBAAoBC,UAAU,CAACE,MAAM,EAAE,CAAC;IAEtD,CAAC,CAAC,OAAOP,GAAG,EAAE;MACZC,OAAO,CAAC5C,KAAK,CAAC,gCAAgC,EAAE2C,GAAG,CAAC;;MAEpD;MACA,IAAIA,GAAG,CAACQ,MAAM,KAAK,GAAG,EAAE;QACtBP,OAAO,CAACE,IAAI,CAAC,gDAAgD,CAAC;QAC9D7C,QAAQ,CAAC,wEAAwE,CAAC;MACpF,CAAC,MAAM,IAAI0C,GAAG,CAACS,cAAc,EAAE;QAC7BR,OAAO,CAACE,IAAI,CAAC,uCAAuC,CAAC;QACrD7C,QAAQ,CAAC,2DAA2D,CAAC;MACvE,CAAC,MAAM;QACL2C,OAAO,CAACE,IAAI,CAAC,yCAAyC,EAAEH,GAAG,CAAC;QAC5D1C,QAAQ,CAAC,wEAAwE,CAAC;MACpF;;MAEA;MACAU,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,SAAS;MACRE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMwC,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;IAC3C,MAAMC,YAAY,GAAGC,UAAU,CAACF,KAAK,CAAC,IAAI,CAAC;;IAE3C;IACAlD,WAAW,CAACqD,IAAI,IAAI;MAClB,MAAMC,WAAW,GAAG;QAClB,GAAGD,IAAI;QACP,CAACJ,MAAM,GAAG;UACR,GAAGI,IAAI,CAACJ,MAAM,CAAC;UACfzB,eAAe,EAAE2B;QACnB;MACF,CAAC;;MAED;MACA,MAAM7B,IAAI,GAAGzB,IAAI,CAAC0D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,OAAO,KAAK0B,MAAM,CAAC;MACjD,IAAIQ,MAAM,GAAG,EAAE;MACf,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,IAAIpC,IAAI,IAAI6B,YAAY,GAAG7B,IAAI,CAACqC,iBAAiB,GAAG,GAAG,EAAE;QACvDD,QAAQ,CAACE,IAAI,CAAC,iDAAiD,CAAC;MAClE;;MAEA;MACA,MAAMC,QAAQ,GAAGP,WAAW,CAACL,MAAM,CAAC;MACpC,IAAIY,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEzB,SAAS,IAAIyB,QAAQ,CAACzB,SAAS,KAAK,cAAc,EAAE;QAChE,MAAM0B,MAAM,GAAGzD,iBAAiB,CAACkD,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAAC3B,SAAS,KAAKyB,QAAQ,CAACzB,SAAS,CAAC;QAC9E,IAAI0B,MAAM,IAAIX,YAAY,GAAGW,MAAM,CAACE,aAAa,IAAI,CAACH,QAAQ,CAACxB,UAAU,EAAE;UACzEoB,MAAM,CAACG,IAAI,CAAC,kBAAkBE,MAAM,CAACE,aAAa,wBAAwBb,YAAY,IAAI,CAAC;QAC7F;MACF;;MAEA;MACAjD,mBAAmB,CAAC+D,UAAU,IAAI;QAChC,IAAIR,MAAM,CAACZ,MAAM,GAAG,CAAC,EAAE;UACrB,OAAO;YACL,GAAGoB,UAAU;YACb,CAAChB,MAAM,GAAGQ,MAAM,CAACS,IAAI,CAAC,IAAI;UAC5B,CAAC;QACH,CAAC,MAAM;UACL,MAAMC,SAAS,GAAG;YAAE,GAAGF;UAAW,CAAC;UACnC,OAAOE,SAAS,CAAClB,MAAM,CAAC;UACxB,OAAOkB,SAAS;QAClB;MACF,CAAC,CAAC;;MAEF;MACA/D,qBAAqB,CAACgE,YAAY,IAAI;QACpC,IAAIV,QAAQ,CAACb,MAAM,GAAG,CAAC,EAAE;UACvB,OAAO;YACL,GAAGuB,YAAY;YACf,CAACnB,MAAM,GAAGS,QAAQ,CAACQ,IAAI,CAAC,IAAI;UAC9B,CAAC;QACH,CAAC,MAAM;UACL,MAAMG,WAAW,GAAG;YAAE,GAAGD;UAAa,CAAC;UACvC,OAAOC,WAAW,CAACpB,MAAM,CAAC;UAC1B,OAAOoB,WAAW;QACpB;MACF,CAAC,CAAC;MAEF,OAAOf,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgB,mBAAmB,GAAGA,CAACrB,MAAM,EAAEC,KAAK,KAAK;IAC7ClD,WAAW,CAACqD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,MAAM,GAAG;QACR,GAAGI,IAAI,CAACJ,MAAM,CAAC;QACfjB,wBAAwB,EAAEuC,QAAQ,CAACrB,KAAK,CAAC,IAAI;MAC/C;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMsB,wBAAwB,GAAGA,CAACvB,MAAM,EAAEwB,OAAO,KAAK;IACpDzE,WAAW,CAACqD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,MAAM,GAAG;QACR,GAAGI,IAAI,CAACJ,MAAM,CAAC;QACff,YAAY,EAAEuC;MAChB;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,wBAAwB,GAAGA,CAACzB,MAAM,EAAEwB,OAAO,KAAK;IACpDzE,WAAW,CAACqD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,MAAM,GAAG;QACR,GAAGI,IAAI,CAACJ,MAAM,CAAC;QACfd,YAAY,EAAEsC;MAChB;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAAC1B,MAAM,EAAE2B,QAAQ,KAAK;IAC/C5E,WAAW,CAACqD,IAAI,IAAI;MAClB,MAAMC,WAAW,GAAG;QAClB,GAAGD,IAAI;QACP,CAACJ,MAAM,GAAG;UACR,GAAGI,IAAI,CAACJ,MAAM,CAAC;UACfb,SAAS,EAAEwC,QAAQ;UACnBvC,UAAU,EAAE,KAAK,CAAC;QACpB;MACF,CAAC;;MAED;MACA,MAAMwB,QAAQ,GAAGP,WAAW,CAACL,MAAM,CAAC;MACpC,IAAIY,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAErC,eAAe,EAAE;QAC7B;QACAqD,UAAU,CAAC,MAAM;UACf7B,iBAAiB,CAACC,MAAM,EAAEY,QAAQ,CAACrC,eAAe,CAAC;QACrD,CAAC,EAAE,CAAC,CAAC;MACP;MAEA,OAAO8B,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMwB,qBAAqB,GAAGA,CAAC7B,MAAM,EAAE8B,SAAS,KAAK;IACnD/E,WAAW,CAACqD,IAAI,IAAI;MAClB,MAAMC,WAAW,GAAG;QAClB,GAAGD,IAAI;QACP,CAACJ,MAAM,GAAG;UACR,GAAGI,IAAI,CAACJ,MAAM,CAAC;UACfZ,UAAU,EAAE0C;QACd;MACF,CAAC;;MAED;MACA,MAAMlB,QAAQ,GAAGP,WAAW,CAACL,MAAM,CAAC;MACpC,IAAIY,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAErC,eAAe,EAAE;QAC7B;QACAqD,UAAU,CAAC,MAAM;UACf7B,iBAAiB,CAACC,MAAM,EAAEY,QAAQ,CAACrC,eAAe,CAAC;QACrD,CAAC,EAAE,CAAC,CAAC;MACP;MAEA,OAAO8B,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM0B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFtF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMqF,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAClF,gBAAgB,CAAC,CAAC4C,MAAM,GAAG,CAAC;MAC1D,IAAIoC,SAAS,EAAE;QACbrF,QAAQ,CAAC,uDAAuD,CAAC;QACjE;MACF;;MAEA;MACA,MAAMwF,gBAAgB,GAAG,CAAC,CAAC;MAC3BF,MAAM,CAACC,IAAI,CAACpF,QAAQ,CAAC,CAACsB,OAAO,CAAC4B,MAAM,IAAI;QAAA,IAAAoC,gBAAA;QACtC,MAAMC,KAAK,GAAGlC,UAAU,CAAC,EAAAiC,gBAAA,GAAAtF,QAAQ,CAACkD,MAAM,CAAC,cAAAoC,gBAAA,uBAAhBA,gBAAA,CAAkB7D,eAAe,KAAI,CAAC,CAAC;QAChE,IAAI8D,KAAK,GAAG,CAAC,EAAE;UACbF,gBAAgB,CAACnC,MAAM,CAAC,GAAGlD,QAAQ,CAACkD,MAAM,CAAC;QAC7C;MACF,CAAC,CAAC;MAEFV,OAAO,CAACG,GAAG,CAAC,sBAAsB,EAAE3C,QAAQ,CAAC;MAC7CwC,OAAO,CAACG,GAAG,CAAC,sCAAsC,EAAE0C,gBAAgB,CAAC;MAErE,MAAMG,gBAAgB,GAAGL,MAAM,CAACC,IAAI,CAACC,gBAAgB,CAAC,CAACvC,MAAM;MAC7D,MAAM2C,UAAU,GAAGN,MAAM,CAACC,IAAI,CAACpF,QAAQ,CAAC,CAAC8C,MAAM;MAE/C,IAAI0C,gBAAgB,KAAK,CAAC,EAAE;QAC1B3F,QAAQ,CAAC,mEAAmE,CAAC;QAC7E;MACF;MAEA2C,OAAO,CAACG,GAAG,CAAC,eAAe6C,gBAAgB,YAAYC,UAAU,SAAS,CAAC;;MAE3E;MACA,MAAM3G,cAAc,CAAC4G,yBAAyB,CAACnG,OAAO,CAAC6B,cAAc,EAAEiE,gBAAgB,CAAC;MAExF,MAAMM,iBAAiB,GAAGH,gBAAgB,KAAKC,UAAU,GACrD,WAAWD,gBAAgB,+BAA+B,GAC1D,GAAGA,gBAAgB,uBAAuBC,UAAU,iBAAiBA,UAAU,GAAGD,gBAAgB,6BAA6B;MAEnIhG,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGmG,iBAAiB,CAAC;MAC9BrG,OAAO,CAAC,CAAC;IAEX,CAAC,CAAC,OAAOiD,GAAG,EAAE;MACZC,OAAO,CAAC5C,KAAK,CAAC,yBAAyB,EAAE2C,GAAG,CAAC;MAC7C1C,QAAQ,CAAC0C,GAAG,CAACqD,OAAO,IAAI,iCAAiC,CAAC;IAC5D,CAAC,SAAS;MACRjG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkG,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,uBAAuB;QAAE,OAAO,SAAS;MAC9C,KAAK,qBAAqB;QAAE,OAAO,MAAM;MACzC,KAAK,gBAAgB;QAAE,OAAO,SAAS;MACvC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAID,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,uBAAuB;QAAE,OAAO,gBAAgB;MACrD,KAAK,qBAAqB;QAAE,OAAO,cAAc;MACjD,KAAK,gBAAgB;QAAE,OAAO,gBAAgB;MAC9C;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAIzE,IAAI,IAAK;IACrC,IAAI,CAACjB,iBAAiB,IAAIA,iBAAiB,CAACwC,MAAM,KAAK,CAAC,EAAE;MACxDN,OAAO,CAACG,GAAG,CAAC,0CAA0C,CAAC;MACvD,OAAO,EAAE;IACX;IAEAH,OAAO,CAACG,GAAG,CAAC,gCAAgC,EAAE;MAC5CnB,OAAO,EAAED,IAAI,CAACC,OAAO;MACrByE,SAAS,EAAE1E,IAAI,CAAC0E,SAAS;MACzBC,UAAU,EAAE3E,IAAI,CAAC2E;IACnB,CAAC,CAAC;IAEF1D,OAAO,CAACG,GAAG,CAAC,qBAAqB,EAAErC,iBAAiB,CAAC6F,GAAG,CAACnC,CAAC,KAAK;MAC7D3B,SAAS,EAAE2B,CAAC,CAAC3B,SAAS;MACtB4D,SAAS,EAAEjC,CAAC,CAACiC,SAAS;MACtBG,OAAO,EAAEpC,CAAC,CAACoC,OAAO;MAClBnC,aAAa,EAAED,CAAC,CAACC;IACnB,CAAC,CAAC,CAAC,CAAC;IAEJ,MAAMoC,iBAAiB,GAAG/F,iBAAiB,CAACgG,MAAM,CAACvC,MAAM,IAAI;MAC3D;MACA,MAAMwC,cAAc,GAAGxC,MAAM,CAACkC,SAAS,KAAK1E,IAAI,CAAC0E,SAAS;;MAE1D;MACA,MAAMO,YAAY,GAAGzC,MAAM,CAACqC,OAAO,KAAK7E,IAAI,CAAC2E,UAAU;;MAEvD;MACA,MAAMO,OAAO,GAAG1C,MAAM,CAACE,aAAa,GAAG,CAAC;MAExCzB,OAAO,CAACG,GAAG,CAAC,UAAUoB,MAAM,CAAC1B,SAAS,GAAG,EAAE;QACzCkE,cAAc;QACdC,YAAY;QACZC,OAAO;QACPC,gBAAgB,EAAE3C,MAAM,CAACkC,SAAS;QAClCU,cAAc,EAAEpF,IAAI,CAAC0E,SAAS;QAC9BW,cAAc,EAAE7C,MAAM,CAACqC,OAAO;QAC9BS,eAAe,EAAEtF,IAAI,CAAC2E;MACxB,CAAC,CAAC;MAEF,OAAOK,cAAc,IAAIC,YAAY,IAAIC,OAAO;IAClD,CAAC,CAAC;IAEFjE,OAAO,CAACG,GAAG,CAAC,+BAA+B0D,iBAAiB,CAACvD,MAAM,EAAE,EAAEuD,iBAAiB,CAAC;IACzF,OAAOA,iBAAiB;EAC1B,CAAC;;EAED;EACA,MAAMS,mBAAmB,GAAGA,CAAC/C,MAAM,EAAExC,IAAI,KAAK;IAC5C,OAAOwC,MAAM,CAACkC,SAAS,KAAK1E,IAAI,CAAC0E,SAAS,IAAIlC,MAAM,CAACqC,OAAO,KAAK7E,IAAI,CAAC2E,UAAU;EAClF,CAAC;;EAED;EACA,MAAMa,oBAAoB,GAAGA,CAAChD,MAAM,EAAEiD,cAAc,KAAK;IACvD,IAAI,CAACjD,MAAM,IAAIA,MAAM,CAAC1B,SAAS,KAAK,cAAc,EAAE,OAAO,SAAS;IACpE,IAAI2E,cAAc,GAAGjD,MAAM,CAACE,aAAa,EAAE,OAAO,OAAO;IACzD,IAAIF,MAAM,CAACE,aAAa,GAAGF,MAAM,CAACkD,YAAY,GAAG,GAAG,EAAE,OAAO,SAAS;IACtE,OAAO,SAAS;EAClB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAC;IAAE3F;EAAK,CAAC;IAAA,IAAA4F,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA;IAAA,oBAC9B1I,OAAA,CAACtC,IAAI;MACHiL,OAAO,EAAC,UAAU;MAClBC,EAAE,EAAE;QACFC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE7H,gBAAgB,CAACqB,IAAI,CAACC,OAAO,CAAC,GAClC,mBAAmB,GACnBpB,kBAAkB,CAACmB,IAAI,CAACC,OAAO,CAAC,GAC9B,mBAAmB,GACnB;MACR,CAAE;MAAAwG,QAAA,gBAEF/I,OAAA,CAACpC,UAAU;QACToL,KAAK,eACHhJ,OAAA,CAAC1D,GAAG;UAAC2M,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAAAJ,QAAA,gBAC7C/I,OAAA,CAACtB,SAAS;YAAC0K,KAAK,EAAC,SAAS;YAACC,QAAQ,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9CzJ,OAAA,CAAC3D,UAAU;YAACsM,OAAO,EAAC,IAAI;YAACe,SAAS,EAAC,MAAM;YAAAX,QAAA,EACtCzG,IAAI,CAACC;UAAO;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACbzJ,OAAA,CAACxD,IAAI;YACHmN,KAAK,EAAErH,IAAI,CAACsH,mBAAmB,KAAK,YAAY,GAAG,YAAY,GAAG,eAAgB;YAClFR,KAAK,EAAE9G,IAAI,CAACsH,mBAAmB,KAAK,YAAY,GAAG,SAAS,GAAG,SAAU;YACzEC,IAAI,EAAC;UAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;QACDK,SAAS,EAAE,GAAGxH,IAAI,CAAC0E,SAAS,IAAI,KAAK,MAAM1E,IAAI,CAAC2E,UAAU,IAAI,KAAK,EAAG;QACtE2B,EAAE,EAAE;UAAEmB,EAAE,EAAE;QAAE;MAAE;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eACFzJ,OAAA,CAACrC,WAAW;QAACiL,EAAE,EAAE;UAAEoB,EAAE,EAAE;QAAE,CAAE;QAAAjB,QAAA,eACzB/I,OAAA,CAAC5D,IAAI;UAAC6N,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAnB,QAAA,gBAEzB/I,OAAA,CAAC5D,IAAI;YAAC+N,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACf/I,OAAA,CAAC3D,UAAU;cAACsM,OAAO,EAAC,OAAO;cAACS,KAAK,EAAC,eAAe;cAACiB,YAAY;cAAAtB,QAAA,EAAC;YAE/D;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzJ,OAAA,CAAC3D,UAAU;cAACsM,OAAO,EAAC,OAAO;cAAC2B,UAAU,EAAC,MAAM;cAAAvB,QAAA,GAC1C,EAAAb,qBAAA,GAAA5F,IAAI,CAACqC,iBAAiB,cAAAuD,qBAAA,uBAAtBA,qBAAA,CAAwBqC,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,IAC/C;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAGPzJ,OAAA,CAAC5D,IAAI;YAAC+N,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACf/I,OAAA,CAAC3D,UAAU;cAACsM,OAAO,EAAC,OAAO;cAACS,KAAK,EAAC,eAAe;cAACiB,YAAY;cAAAtB,QAAA,EAAC;YAE/D;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzJ,OAAA,CAAC7D,SAAS;cACRqO,IAAI,EAAC,QAAQ;cACbX,IAAI,EAAC,OAAO;cACZY,SAAS;cACTvG,KAAK,EAAE,EAAAiE,qBAAA,GAAApH,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAA4F,qBAAA,uBAAtBA,qBAAA,CAAwB3F,eAAe,KAAI,CAAE;cACpDkI,QAAQ,EAAGC,CAAC,IAAK3G,iBAAiB,CAAC1B,IAAI,CAACC,OAAO,EAAEoI,CAAC,CAACC,MAAM,CAAC1G,KAAK,CAAE;cACjEvD,KAAK,EAAE,CAAC,CAACM,gBAAgB,CAACqB,IAAI,CAACC,OAAO,CAAE;cACxCsI,UAAU,EAAE5J,gBAAgB,CAACqB,IAAI,CAACC,OAAO,CAAC,IAAIpB,kBAAkB,CAACmB,IAAI,CAACC,OAAO,CAAE;cAC/E6G,KAAK,EAAEjI,kBAAkB,CAACmB,IAAI,CAACC,OAAO,CAAC,IAAI,CAACtB,gBAAgB,CAACqB,IAAI,CAACC,OAAO,CAAC,GAAG,SAAS,GAAG,SAAU;cACnGuI,UAAU,EAAE;gBACVC,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,GAAG;gBACTC,KAAK,EAAE;kBAAEC,SAAS,EAAE;gBAAQ;cAC9B;YAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGPzJ,OAAA,CAAC5D,IAAI;YAAC+N,IAAI;YAACC,EAAE,EAAE,EAAG;YAAArB,QAAA,gBAChB/I,OAAA,CAAC3D,UAAU;cAACsM,OAAO,EAAC,OAAO;cAACS,KAAK,EAAC,eAAe;cAACiB,YAAY;cAAAtB,QAAA,EAAC;YAE/D;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzJ,OAAA,CAAC9C,YAAY;cACX2M,IAAI,EAAC,OAAO;cACZY,SAAS;cACTvG,KAAK,EACH,EAAAkE,sBAAA,GAAArH,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAA6F,sBAAA,uBAAtBA,sBAAA,CAAwBhF,SAAS,MAAK,cAAc,GAChD;gBAAEA,SAAS,EAAE,cAAc;gBAAE4D,SAAS,EAAE,OAAO;gBAAEhC,aAAa,EAAE;cAAI,CAAC,GACrE3D,iBAAiB,CAACkD,IAAI,CAACQ,CAAC;gBAAA,IAAAoG,sBAAA;gBAAA,OAAIpG,CAAC,CAAC3B,SAAS,OAAA+H,sBAAA,GAAKpK,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAA4I,sBAAA,uBAAtBA,sBAAA,CAAwB/H,SAAS;cAAA,EAAC,IAAI,IACvF;cACDsH,QAAQ,EAAEA,CAACU,KAAK,EAAEC,QAAQ,KAAK;gBAC7B1F,kBAAkB,CAACrD,IAAI,CAACC,OAAO,EAAE,CAAA8I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEjI,SAAS,KAAI,EAAE,CAAC;cAC7D,CAAE;cACFkI,OAAO,EAAE,CACP;gBAAElI,SAAS,EAAE,cAAc;gBAAE4D,SAAS,EAAE,OAAO;gBAAEhC,aAAa,EAAE;cAAI,CAAC,EACrE,GAAG+B,oBAAoB,CAACzE,IAAI,CAAC,CAC7B;cACFiJ,cAAc,EAAGC,MAAM,IAAK;gBAC1B,IAAIA,MAAM,CAACpI,SAAS,KAAK,cAAc,EAAE,OAAO,cAAc;gBAC9D,OAAO,GAAGoI,MAAM,CAACpI,SAAS,KAAKoI,MAAM,CAACxG,aAAa,IAAI;cACzD,CAAE;cACFyG,WAAW,EAAGC,MAAM,iBAClB1L,OAAA,CAAC7D,SAAS;gBAAA,GACJuP,MAAM;gBACVC,WAAW,EAAC,qBAAqB;gBACjChD,OAAO,EAAC,UAAU;gBAClBkB,IAAI,EAAC;cAAO;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACD;cACFhJ,OAAO,EAAEc,aAAc;cACvBqK,QAAQ,EAAErK;YAAc;cAAA+H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,EAGD,EAAApB,sBAAA,GAAAtH,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAA8F,sBAAA,uBAAtBA,sBAAA,CAAwBjF,SAAS,KACjC,EAAAkF,sBAAA,GAAAvH,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAA+F,sBAAA,uBAAtBA,sBAAA,CAAwBlF,SAAS,MAAK,cAAc,IACpD,CAACyI,sBAAA,IAAM;cACL,MAAM/G,MAAM,GAAGzD,iBAAiB,CAACkD,IAAI,CAACQ,CAAC;gBAAA,IAAA+G,sBAAA;gBAAA,OAAI/G,CAAC,CAAC3B,SAAS,OAAA0I,sBAAA,GAAK/K,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAAuJ,sBAAA,uBAAtBA,sBAAA,CAAwB1I,SAAS;cAAA,EAAC;cAC7F,MAAM2E,cAAc,GAAG,EAAA8D,sBAAA,GAAA9K,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAAsJ,sBAAA,uBAAtBA,sBAAA,CAAwBrJ,eAAe,KAAI,CAAC;cACnE,OAAOsC,MAAM,IAAIiD,cAAc,GAAGjD,MAAM,CAACE,aAAa;YACxD,CAAC,EAAE,CAAC,iBACHhF,OAAA,CAAC1D,GAAG;cAACsM,EAAE,EAAE;gBAAEmD,EAAE,EAAE;cAAE,CAAE;cAAAhD,QAAA,eACjB/I,OAAA,CAAC9D,MAAM;gBACL2N,IAAI,EAAC,OAAO;gBACZlB,OAAO,EAAE,CAAAJ,sBAAA,GAAAxH,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAAgG,sBAAA,eAAtBA,sBAAA,CAAwBlF,UAAU,GAAG,WAAW,GAAG,UAAW;gBACvE+F,KAAK,EAAC,SAAS;gBACf4C,OAAO,EAAEA,CAAA;kBAAA,IAAAC,sBAAA;kBAAA,OAAMnG,qBAAqB,CAACxD,IAAI,CAACC,OAAO,EAAE,GAAA0J,sBAAA,GAAClL,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAA0J,sBAAA,eAAtBA,sBAAA,CAAwB5I,UAAU,EAAC;gBAAA,CAAC;gBACxF6I,SAAS,eAAElM,OAAA,CAAClB,WAAW;kBAAAwK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC3BgB,SAAS;gBAAA1B,QAAA,EACV;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAGPzJ,OAAA,CAAC5D,IAAI;YAAC+N,IAAI;YAACC,EAAE,EAAE,EAAG;YAAArB,QAAA,gBAChB/I,OAAA,CAACnC,OAAO;cAAC+K,EAAE,EAAE;gBAAEuD,EAAE,EAAE;cAAE;YAAE;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1BzJ,OAAA,CAAC3D,UAAU;cAACsM,OAAO,EAAC,OAAO;cAACS,KAAK,EAAC,eAAe;cAACiB,YAAY;cAAAtB,QAAA,EAAC;YAE/D;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAGPzJ,OAAA,CAAC5D,IAAI;YAAC+N,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACf/I,OAAA,CAAC3D,UAAU;cAACsM,OAAO,EAAC,OAAO;cAACS,KAAK,EAAC,eAAe;cAACiB,YAAY;cAAAtB,QAAA,EAAC;YAE/D;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzJ,OAAA,CAAC7D,SAAS;cACRqO,IAAI,EAAC,QAAQ;cACbX,IAAI,EAAC,OAAO;cACZY,SAAS;cACTvG,KAAK,EAAE,EAAAsE,sBAAA,GAAAzH,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAAiG,sBAAA,uBAAtBA,sBAAA,CAAwBxF,wBAAwB,KAAI,CAAE;cAC7D0H,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAChD,IAAI,CAACC,OAAO,EAAEoI,CAAC,CAACC,MAAM,CAAC1G,KAAK,CAAE;cACnE4G,UAAU,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEqB,GAAG,EAAE;cAAG;YAAE;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGPzJ,OAAA,CAAC5D,IAAI;YAAC+N,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACf/I,OAAA,CAAC3D,UAAU;cAACsM,OAAO,EAAC,OAAO;cAACS,KAAK,EAAC,eAAe;cAACiB,YAAY;cAAAtB,QAAA,EAAC;YAE/D;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzJ,OAAA,CAAC1D,GAAG;cAAC2M,OAAO,EAAC,MAAM;cAACoD,cAAc,EAAC,QAAQ;cAAAtD,QAAA,eACzC/I,OAAA,CAACvC,QAAQ;gBACPgI,OAAO,EAAE,EAAAgD,sBAAA,GAAA1H,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAAkG,sBAAA,uBAAtBA,sBAAA,CAAwBvF,YAAY,KAAI,KAAM;gBACvDwH,QAAQ,EAAGC,CAAC,IAAKnF,wBAAwB,CAAClD,IAAI,CAACC,OAAO,EAAEoI,CAAC,CAACC,MAAM,CAACnF,OAAO,CAAE;gBAC1E2D,KAAK,EAAC,SAAS;gBACfS,IAAI,EAAC;cAAO;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGPzJ,OAAA,CAAC5D,IAAI;YAAC+N,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACf/I,OAAA,CAAC3D,UAAU;cAACsM,OAAO,EAAC,OAAO;cAACS,KAAK,EAAC,eAAe;cAACiB,YAAY;cAAAtB,QAAA,EAAC;YAE/D;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzJ,OAAA,CAAC1D,GAAG;cAAC2M,OAAO,EAAC,MAAM;cAACoD,cAAc,EAAC,QAAQ;cAAAtD,QAAA,eACzC/I,OAAA,CAACvC,QAAQ;gBACPgI,OAAO,EAAE,EAAAiD,uBAAA,GAAA3H,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAAmG,uBAAA,uBAAtBA,uBAAA,CAAwBvF,YAAY,KAAI,KAAM;gBACvDuH,QAAQ,EAAGC,CAAC,IAAKjF,wBAAwB,CAACpD,IAAI,CAACC,OAAO,EAAEoI,CAAC,CAACC,MAAM,CAACnF,OAAO,CAAE;gBAC1E2D,KAAK,EAAC,SAAS;gBACfS,IAAI,EAAC;cAAO;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA,CACR;EAED,IAAI,CAACnJ,OAAO,EAAE,OAAO,IAAI;EAEzB,oBACEN,OAAA,CAAClE,MAAM;IACLsE,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEA,OAAQ;IACjBiM,QAAQ,EAAE5K,QAAQ,GAAG,IAAI,GAAG,IAAK;IACjC+I,SAAS;IACT8B,UAAU,EAAEzK,aAAc;IAC1B0K,UAAU,EAAE;MACV5D,EAAE,EAAE;QACF6D,SAAS,EAAE3K,aAAa,GAAG,OAAO,GAAG,MAAM;QAC3C4K,MAAM,EAAE5K,aAAa,GAAG,CAAC,GAAG,CAAC;QAC7B6K,YAAY,EAAE7K,aAAa,GAAG,CAAC,GAAG;MACpC;IACF,CAAE;IAAAiH,QAAA,gBAEF/I,OAAA,CAACjE,WAAW;MAAC6M,EAAE,EAAE;QACfmB,EAAE,EAAE,CAAC;QACL6C,YAAY,EAAE;MAChB,CAAE;MAAA7D,QAAA,eACA/I,OAAA,CAAC1D,GAAG;QAAC2M,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAAAJ,QAAA,gBAC7C/I,OAAA,CAACtB,SAAS;UAAC0K,KAAK,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7BzJ,OAAA,CAAC1D,GAAG;UAACuQ,IAAI,EAAE,CAAE;UAAA9D,QAAA,gBACX/I,OAAA,CAAC3D,UAAU;YAACsM,OAAO,EAAE7G,aAAa,GAAG,IAAI,GAAG,IAAK;YAAAiH,QAAA,EAAC;UAElD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzJ,OAAA,CAAClC,KAAK;YACJgP,SAAS,EAAEhL,aAAa,GAAG,QAAQ,GAAG,KAAM;YAC5CoH,UAAU,EAAEpH,aAAa,GAAG,YAAY,GAAG,QAAS;YACpDqH,GAAG,EAAE,CAAE;YACP4C,EAAE,EAAE,CAAE;YAAAhD,QAAA,gBAEN/I,OAAA,CAAC3D,UAAU;cAACsM,OAAO,EAAC,OAAO;cAACS,KAAK,EAAC,eAAe;cAAAL,QAAA,GAAC,WACvC,EAACzI,OAAO,CAAC6B,cAAc;YAAA;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACbzJ,OAAA,CAACxD,IAAI;cACHmN,KAAK,EAAE7C,mBAAmB,CAACxG,OAAO,CAACmC,YAAY,CAAE;cACjD2G,KAAK,EAAExC,mBAAmB,CAACtG,OAAO,CAACmC,YAAY,CAAE;cACjDoH,IAAI,EAAC;YAAO;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACFzJ,OAAA,CAAC3D,UAAU;cAACsM,OAAO,EAAC,OAAO;cAACS,KAAK,EAAC,eAAe;cAAAL,QAAA,GAAC,gBAClC,EAACzI,OAAO,CAACyC,YAAY;YAAA;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdzJ,OAAA,CAAChE,aAAa;MAAC4M,EAAE,EAAE;QACjBmE,CAAC,EAAEjL,aAAa,GAAG,CAAC,GAAG,CAAC;QACxBkL,SAAS,EAAElL,aAAa,GAAG,qBAAqB,GAAG,MAAM;QACzDmL,SAAS,EAAE;MACb,CAAE;MAAAlE,QAAA,GACCpI,KAAK,iBACJX,OAAA,CAACzD,KAAK;QAAC2Q,QAAQ,EAAC,OAAO;QAACtE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,EACnCpI;MAAK;QAAA2I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAEAhJ,OAAO,gBACNT,OAAA,CAAC1D,GAAG;QAAC2M,OAAO,EAAC,MAAM;QAACoD,cAAc,EAAC,QAAQ;QAACU,CAAC,EAAE,CAAE;QAAAhE,QAAA,eAC/C/I,OAAA,CAAC3D,UAAU;UAAA0M,QAAA,EAAC;QAAmB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,gBAENzJ,OAAA,CAAAE,SAAA;QAAA6I,QAAA,EAEGrH,QAAQ,gBACP1B,OAAA,CAAC1D,GAAG;UAAAyM,QAAA,gBACF/I,OAAA,CAACzD,KAAK;YAAC2Q,QAAQ,EAAC,MAAM;YAACtE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAE,QAAA,eACnC/I,OAAA,CAAC3D,UAAU;cAACsM,OAAO,EAAC,OAAO;cAAAI,QAAA,GAAC,kDACoB,eAAA/I,OAAA;gBAAAsJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kDACV,eAAAzJ,OAAA;gBAAAsJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,wDACF,eAAAzJ,OAAA;gBAAAsJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,WAC/C,eAAAzJ,OAAA;gBAAA+I,QAAA,EAAQ;cAAiB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,yCAAwC,eAAAzJ,OAAA;gBAAAsJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,WAC/E,eAAAzJ,OAAA;gBAAA+I,QAAA,EAAQ;cAAY;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,4CACjC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,EACP5I,IAAI,CAACqG,GAAG,CAAE5E,IAAI,iBACbtC,OAAA,CAACiI,cAAc;YAAoB3F,IAAI,EAAEA;UAAK,GAAzBA,IAAI,CAACC,OAAO;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;QAAA;QAEN;QACAzJ,OAAA,CAAC1D,GAAG;UAAAyM,QAAA,gBACF/I,OAAA,CAACzD,KAAK;YAAC2Q,QAAQ,EAAC,MAAM;YAACtE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAE,QAAA,eACnC/I,OAAA,CAAC3D,UAAU;cAACsM,OAAO,EAAC,OAAO;cAAAI,QAAA,GAAC,kDACoB,eAAA/I,OAAA;gBAAAsJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kDACV,eAAAzJ,OAAA;gBAAAsJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,wDAEhD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACRzJ,OAAA,CAACpD,cAAc;YAAC8M,SAAS,EAAE3M,KAAM;YAAC4L,OAAO,EAAC,UAAU;YAAAI,QAAA,eAClD/I,OAAA,CAACvD,KAAK;cAACoN,IAAI,EAAC,OAAO;cAAAd,QAAA,gBACjB/I,OAAA,CAACnD,SAAS;gBAAAkM,QAAA,eACR/I,OAAA,CAAClD,QAAQ;kBAAAiM,QAAA,gBACP/I,OAAA,CAACrD,SAAS;oBAAAoM,QAAA,EAAC;kBAAO;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BzJ,OAAA,CAACrD,SAAS;oBAAAoM,QAAA,EAAC;kBAAS;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAChCzJ,OAAA,CAACrD,SAAS;oBAAAoM,QAAA,EAAC;kBAAU;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACjCzJ,OAAA,CAACrD,SAAS;oBAAAoM,QAAA,EAAC;kBAAa;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpCzJ,OAAA,CAACrD,SAAS;oBAAAoM,QAAA,EAAC;kBAAW;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAClCzJ,OAAA,CAACrD,SAAS;oBAAAoM,QAAA,EAAC;kBAAM;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7BzJ,OAAA,CAACrD,SAAS;oBAAAoM,QAAA,EAAC;kBAAU;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACjCzJ,OAAA,CAACrD,SAAS;oBAAAoM,QAAA,EAAC;kBAAY;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnCzJ,OAAA,CAACrD,SAAS;oBAAAoM,QAAA,EAAC;kBAAY;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAClBzJ,OAAA,CAACtD,SAAS;gBAAAqM,QAAA,EACPlI,IAAI,CAACqG,GAAG,CAAE5E,IAAI;kBAAA,IAAA6K,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;kBAAA,oBACb3N,OAAA,CAAClD,QAAQ;oBAAAiM,QAAA,gBACP/I,OAAA,CAACrD,SAAS;sBAAAoM,QAAA,eACR/I,OAAA,CAAC3D,UAAU;wBAACsM,OAAO,EAAC,OAAO;wBAAC2B,UAAU,EAAC,MAAM;wBAAAvB,QAAA,EAC1CzG,IAAI,CAACC;sBAAO;wBAAA+G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACZzJ,OAAA,CAACrD,SAAS;sBAAAoM,QAAA,EAAEzG,IAAI,CAAC0E,SAAS,IAAI;oBAAK;sBAAAsC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAChDzJ,OAAA,CAACrD,SAAS;sBAAAoM,QAAA,EAAEzG,IAAI,CAAC2E,UAAU,IAAI;oBAAK;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACjDzJ,OAAA,CAACrD,SAAS;sBAAAoM,QAAA,eACR/I,OAAA,CAAC3D,UAAU;wBAACsM,OAAO,EAAC,OAAO;wBAAAI,QAAA,GACxB,EAAAoE,sBAAA,GAAA7K,IAAI,CAACqC,iBAAiB,cAAAwI,sBAAA,uBAAtBA,sBAAA,CAAwB5C,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,IAC/C;sBAAA;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACZzJ,OAAA,CAACrD,SAAS;sBAAAoM,QAAA,eACR/I,OAAA,CAAC7D,SAAS;wBACRqO,IAAI,EAAC,QAAQ;wBACbX,IAAI,EAAC,OAAO;wBACZ3F,KAAK,EAAE,EAAAkJ,uBAAA,GAAArM,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAA6K,uBAAA,uBAAtBA,uBAAA,CAAwB5K,eAAe,KAAI,CAAE;wBACpDkI,QAAQ,EAAGC,CAAC,IAAK3G,iBAAiB,CAAC1B,IAAI,CAACC,OAAO,EAAEoI,CAAC,CAACC,MAAM,CAAC1G,KAAK,CAAE;wBACjEvD,KAAK,EAAE,CAAC,CAACM,gBAAgB,CAACqB,IAAI,CAACC,OAAO,CAAE;wBACxCsI,UAAU,EAAE5J,gBAAgB,CAACqB,IAAI,CAACC,OAAO,CAAC,IAAIpB,kBAAkB,CAACmB,IAAI,CAACC,OAAO,CAAE;wBAC/E6G,KAAK,EAAEjI,kBAAkB,CAACmB,IAAI,CAACC,OAAO,CAAC,IAAI,CAACtB,gBAAgB,CAACqB,IAAI,CAACC,OAAO,CAAC,GAAG,SAAS,GAAG,SAAU;wBACnGuI,UAAU,EAAE;0BACVC,GAAG,EAAE,CAAC;0BACNC,IAAI,EAAE,GAAG;0BACTC,KAAK,EAAE;4BAAEC,SAAS,EAAE;0BAAQ;wBAC9B,CAAE;wBACFtC,EAAE,EAAE;0BAAEgF,KAAK,EAAE;wBAAI;sBAAE;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC,eACZzJ,OAAA,CAACrD,SAAS;sBAAAoM,QAAA,eACR/I,OAAA,CAAC1D,GAAG;wBAACsM,EAAE,EAAE;0BAAEiF,QAAQ,EAAE;wBAAI,CAAE;wBAAA9E,QAAA,gBACzB/I,OAAA,CAAC9C,YAAY;0BACX2M,IAAI,EAAC,OAAO;0BACZ3F,KAAK,EACH,EAAAmJ,uBAAA,GAAAtM,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAA8K,uBAAA,uBAAtBA,uBAAA,CAAwBjK,SAAS,MAAK,cAAc,GAChD;4BAAEA,SAAS,EAAE,cAAc;4BAAE4D,SAAS,EAAE,OAAO;4BAAEhC,aAAa,EAAE;0BAAI,CAAC,GACrE3D,iBAAiB,CAACkD,IAAI,CAACQ,CAAC;4BAAA,IAAA+I,uBAAA;4BAAA,OAAI/I,CAAC,CAAC3B,SAAS,OAAA0K,uBAAA,GAAK/M,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAAuL,uBAAA,uBAAtBA,uBAAA,CAAwB1K,SAAS;0BAAA,EAAC,IAAI,IACvF;0BACDsH,QAAQ,EAAEA,CAACU,KAAK,EAAEC,QAAQ,KAAK;4BAC7B1F,kBAAkB,CAACrD,IAAI,CAACC,OAAO,EAAE,CAAA8I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEjI,SAAS,KAAI,EAAE,CAAC;0BAC7D,CAAE;0BACFkI,OAAO,EAAE,CACP;4BAAElI,SAAS,EAAE,cAAc;4BAAE4D,SAAS,EAAE,OAAO;4BAAEhC,aAAa,EAAE;0BAAI,CAAC,EACrE,GAAG+B,oBAAoB,CAACzE,IAAI,CAAC,CAC7B;0BACFyL,aAAa,EACXxM,aAAa,GACT,uBAAuB,GACvBF,iBAAiB,CAACwC,MAAM,KAAK,CAAC,GAC5B,2CAA2C,GAC3C,+CACP;0BACD0H,cAAc,EAAGC,MAAM,IAAK;4BAC1B,IAAIA,MAAM,CAACpI,SAAS,KAAK,cAAc,EAAE,OAAO,cAAc;4BAC9D,OAAO,GAAGoI,MAAM,CAACpI,SAAS,KAAKoI,MAAM,CAACxG,aAAa,IAAI;0BACzD,CAAE;0BACFyG,WAAW,EAAGC,MAAM,iBAClB1L,OAAA,CAAC7D,SAAS;4BAAA,GACJuP,MAAM;4BACVC,WAAW,EAAC,qBAAqB;4BACjChD,OAAO,EAAC,UAAU;4BAClBkB,IAAI,EAAC;0BAAO;4BAAAP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACb,CACD;0BACFuE,YAAY,EAAEA,CAACC,KAAK,EAAEzC,MAAM;4BAAA,IAAA0C,uBAAA;4BAAA,oBAC1BlO,OAAA,CAAC1D,GAAG;8BAACoN,SAAS,EAAC,IAAI;8BAAA,GAAKuE,KAAK;8BAAAlF,QAAA,gBAC3B/I,OAAA,CAACxC,YAAY;gCAAAuL,QAAA,eACX/I,OAAA,CAAChB,UAAU;kCACTqK,QAAQ,EAAC,OAAO;kCAChBD,KAAK,EACHoC,MAAM,CAACpI,SAAS,KAAK,cAAc,GAC/B,SAAS,GACT0E,oBAAoB,CAAC0D,MAAM,EAAE,EAAA0C,uBAAA,GAAAnN,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAA2L,uBAAA,uBAAtBA,uBAAA,CAAwB1L,eAAe,KAAI,CAAC;gCAC9E;kCAAA8G,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACF;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACU,CAAC,eACfzJ,OAAA,CAACzC,YAAY;gCACX4Q,OAAO,EAAE3C,MAAM,CAACpI,SAAS,KAAK,cAAc,GAAG,cAAc,GAAGoI,MAAM,CAACpI,SAAU;gCACjFgL,SAAS,EACP5C,MAAM,CAACpI,SAAS,KAAK,cAAc,GAC/B,0BAA0B,GAC1B,GAAGoI,MAAM,CAACxE,SAAS,IAAIwE,MAAM,CAACrE,OAAO,MAAMqE,MAAM,CAACxG,aAAa;8BACpE;gCAAAsE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACF,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC;0BAAA,CACN;0BACFhJ,OAAO,EAAEc,aAAc;0BACvBqK,QAAQ,EAAErK,aAAc;0BACxBqH,EAAE,EAAE;4BAAEgF,KAAK,EAAE;0BAAO;wBAAE;0BAAAtE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvB,CAAC,EAED,EAAA6D,uBAAA,GAAAvM,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAA+K,uBAAA,uBAAtBA,uBAAA,CAAwBlK,SAAS,KACjC,EAAAmK,uBAAA,GAAAxM,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAAgL,uBAAA,uBAAtBA,uBAAA,CAAwBnK,SAAS,MAAK,cAAc,IACpD,CAACiL,uBAAA,IAAM;0BACL,MAAMvJ,MAAM,GAAGzD,iBAAiB,CAACkD,IAAI,CAACQ,CAAC;4BAAA,IAAAuJ,uBAAA;4BAAA,OAAIvJ,CAAC,CAAC3B,SAAS,OAAAkL,uBAAA,GAAKvN,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAA+L,uBAAA,uBAAtBA,uBAAA,CAAwBlL,SAAS;0BAAA,EAAC;0BAC7F,MAAM2E,cAAc,GAAG,EAAAsG,uBAAA,GAAAtN,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAA8L,uBAAA,uBAAtBA,uBAAA,CAAwB7L,eAAe,KAAI,CAAC;0BACnE,OAAOsC,MAAM,IAAIiD,cAAc,GAAGjD,MAAM,CAACE,aAAa;wBACxD,CAAC,EAAE,CAAC,iBACHhF,OAAA,CAAC1D,GAAG;0BAACsM,EAAE,EAAE;4BAAEmD,EAAE,EAAE;0BAAE,CAAE;0BAAAhD,QAAA,eACjB/I,OAAA,CAAC9D,MAAM;4BACL2N,IAAI,EAAC,OAAO;4BACZlB,OAAO,EAAE,CAAA6E,uBAAA,GAAAzM,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAAiL,uBAAA,eAAtBA,uBAAA,CAAwBnK,UAAU,GAAG,WAAW,GAAG,UAAW;4BACvE+F,KAAK,EAAC,SAAS;4BACf4C,OAAO,EAAEA,CAAA;8BAAA,IAAAuC,uBAAA;8BAAA,OAAMzI,qBAAqB,CAACxD,IAAI,CAACC,OAAO,EAAE,GAAAgM,uBAAA,GAACxN,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAAgM,uBAAA,eAAtBA,uBAAA,CAAwBlL,UAAU,EAAC;4BAAA,CAAC;4BACxF6I,SAAS,eAAElM,OAAA,CAAClB,WAAW;8BAAAwK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAE;4BAAAV,QAAA,EAC5B;0BAED;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CACN;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG,CAAC,eACZzJ,OAAA,CAACrD,SAAS;sBAAAoM,QAAA,eACR/I,OAAA,CAAC7D,SAAS;wBACRqO,IAAI,EAAC,QAAQ;wBACbX,IAAI,EAAC,OAAO;wBACZ3F,KAAK,EAAE,EAAAuJ,uBAAA,GAAA1M,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAAkL,uBAAA,uBAAtBA,uBAAA,CAAwBzK,wBAAwB,KAAI,CAAE;wBAC7D0H,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAAChD,IAAI,CAACC,OAAO,EAAEoI,CAAC,CAACC,MAAM,CAAC1G,KAAK,CAAE;wBACnE4G,UAAU,EAAE;0BAAEC,GAAG,EAAE,CAAC;0BAAEqB,GAAG,EAAE;wBAAG,CAAE;wBAChCxD,EAAE,EAAE;0BAAEgF,KAAK,EAAE;wBAAG;sBAAE;wBAAAtE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC,eACZzJ,OAAA,CAACrD,SAAS;sBAAAoM,QAAA,eACR/I,OAAA,CAACvC,QAAQ;wBACPgI,OAAO,EAAE,EAAAiI,uBAAA,GAAA3M,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAAmL,uBAAA,uBAAtBA,uBAAA,CAAwBxK,YAAY,KAAI,KAAM;wBACvDwH,QAAQ,EAAGC,CAAC,IAAKnF,wBAAwB,CAAClD,IAAI,CAACC,OAAO,EAAEoI,CAAC,CAACC,MAAM,CAACnF,OAAO,CAAE;wBAC1E2D,KAAK,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC,eACZzJ,OAAA,CAACrD,SAAS;sBAAAoM,QAAA,eACR/I,OAAA,CAACvC,QAAQ;wBACPgI,OAAO,EAAE,EAAAkI,uBAAA,GAAA5M,QAAQ,CAACuB,IAAI,CAACC,OAAO,CAAC,cAAAoL,uBAAA,uBAAtBA,uBAAA,CAAwBxK,YAAY,KAAI,KAAM;wBACvDuH,QAAQ,EAAGC,CAAC,IAAKjF,wBAAwB,CAACpD,IAAI,CAACC,OAAO,EAAEoI,CAAC,CAACC,MAAM,CAACnF,OAAO,CAAE;wBAC1E2D,KAAK,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC;kBAAA,GAxICnH,IAAI,CAACC,OAAO;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyIjB,CAAC;gBAAA,CACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MACN,gBACD,CACH,EAEA5I,IAAI,CAACgD,MAAM,KAAK,CAAC,IAAI,CAACpD,OAAO,iBAC5BT,OAAA,CAACzD,KAAK;QAAC2Q,QAAQ,EAAC,MAAM;QAACtE,EAAE,EAAE;UAAEmD,EAAE,EAAE;QAAE,CAAE;QAAAhD,QAAA,EAAC;MAEtC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAEhBzJ,OAAA,CAAC/D,aAAa;MAAC2M,EAAE,EAAE;QACjBmE,CAAC,EAAE,CAAC;QACJ5D,GAAG,EAAE,CAAC;QACNqF,aAAa,EAAE1M,aAAa,GAAG,QAAQ,GAAG,KAAK;QAC/C2M,SAAS,EAAE;MACb,CAAE;MAAA1F,QAAA,gBACA/I,OAAA,CAAC9D,MAAM;QACL8P,OAAO,EAAE3L,OAAQ;QACjB6L,SAAS,EAAE,CAACpK,aAAa,iBAAI9B,OAAA,CAACxB,UAAU;UAAA8K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC5CmC,QAAQ,EAAEnL,OAAQ;QAClBgK,SAAS,EAAE3I,aAAc;QACzB+H,IAAI,EAAE/H,aAAa,GAAG,OAAO,GAAG,QAAS;QAAAiH,QAAA,EAC1C;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzJ,OAAA,CAAC9D,MAAM;QACL8P,OAAO,EAAEhG,UAAW;QACpB2C,OAAO,EAAC,WAAW;QACnBuD,SAAS,EAAE,CAACpK,aAAa,iBAAI9B,OAAA,CAAC1B,QAAQ;UAAAgL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC1CmC,QAAQ,EAAEnL,OAAO,IAAII,IAAI,CAACgD,MAAM,KAAK,CAAE;QACvC4G,SAAS,EAAE3I,aAAc;QACzB+H,IAAI,EAAE/H,aAAa,GAAG,OAAO,GAAG,QAAS;QAAAiH,QAAA,EAC1C;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACjJ,EAAA,CA90BIL,sBAAsB;EAAA,QAgBZpC,QAAQ,EACLC,aAAa,EACbA,aAAa,EACRA,aAAa;AAAA;AAAA0Q,EAAA,GAnB/BvO,sBAAsB;AAg1B5B,eAAeA,sBAAsB;AAAC,IAAAuO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}