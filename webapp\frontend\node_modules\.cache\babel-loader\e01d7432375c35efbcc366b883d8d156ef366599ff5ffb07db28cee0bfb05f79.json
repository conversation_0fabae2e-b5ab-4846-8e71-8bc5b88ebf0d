{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\PosaCaviCollegamenti.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Paper, Divider, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Grid, Alert, CircularProgress, FormHelperText } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Cable as CableIcon, Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PosaCaviCollegamenti = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      setCavi(caviData);\n    } catch (error) {\n      onError('Errore nel caricamento dei cavi');\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi();\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      setDialogType('aggiungiCavo');\n      setFormData({\n        id_cavo: '',\n        revisione_ufficiale: '0',\n        sistema: '',\n        utility: '',\n        colore_cavo: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        SH: '',\n        ubicazione_partenza: '',\n        utenza_partenza: '',\n        descrizione_utenza_partenza: '',\n        ubicazione_arrivo: '',\n        utenza_arrivo: '',\n        descrizione_utenza_arrivo: '',\n        metri_teorici: '',\n        metratura_reale: '0',\n        responsabile_posa: '',\n        id_bobina: '',\n        stato_installazione: 'DA POSARE'\n      });\n      setOpenDialog(true);\n    } else if (option === 'modificaCavo') {\n      loadCavi();\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi();\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    } else if (option === 'collegamentoCavo') {\n      // Implementazione futura per i collegamenti\n      onError('Funzionalità in fase di implementazione');\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'aggiungiCavo' || dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({\n            metri_posati: 'Inserire un valore numerico valido'\n          });\n          setLoading(false);\n          return;\n        }\n        await caviService.updateMetriPosati(cantiereId, formData.id_cavo, parseFloat(formData.metri_posati));\n        onSuccess('Metri posati aggiornati con successo');\n      } else if (dialogType === 'modificaBobina') {\n        await caviService.updateBobina(cantiereId, formData.id_cavo, formData.id_bobina);\n        onSuccess('Bobina aggiornata con successo');\n      } else if (dialogType === 'aggiungiCavo' || dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n        if (dialogType === 'aggiungiCavo') {\n          await caviService.createCavo(cantiereId, validatedData);\n          onSuccess('Cavo aggiunto con successo');\n        } else {\n          await caviService.updateCavo(cantiereId, validatedData.id_cavo, validatedData);\n          onSuccess('Cavo modificato con successo');\n        }\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo);\n        onSuccess('Cavo eliminato con successo');\n      }\n      handleCloseDialog();\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'inserisciMetri') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Inserisci Metri Posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Seleziona un cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                button: true,\n                onClick: () => handleCavoSelect(cavo),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: cavo.id_cavo,\n                  secondary: `${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 23\n                }, this)\n              }, cavo.id_cavo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metri teorici: \", selectedCavo.metri_teorici || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metratura attuale: \", selectedCavo.metratura_reale || '0']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"dense\",\n              name: \"metri_posati\",\n              label: \"Metri posati da aggiungere\",\n              type: \"number\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_posati,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_posati,\n              helperText: formErrors.metri_posati,\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || !formData.metri_posati,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 71\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'modificaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Bobina Cavo Posato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Seleziona un cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                button: true,\n                onClick: () => handleCavoSelect(cavo),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: cavo.id_cavo,\n                  secondary: `Bobina attuale: ${cavo.id_bobina || 'Non assegnata'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 23\n                }, this)\n              }, cavo.id_cavo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Bobina attuale: \", selectedCavo.id_bobina || 'Non assegnata']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"dense\",\n              name: \"id_bobina\",\n              label: \"ID Bobina\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_bobina,\n              onChange: handleFormChange,\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 71\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'aggiungiCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Aggiungi Nuovo Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"id_cavo\",\n                label: \"ID Cavo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.id_cavo,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.id_cavo,\n                helperText: formErrors.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"revisione_ufficiale\",\n                label: \"Revisione Ufficiale\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.revisione_ufficiale,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.revisione_ufficiale,\n                helperText: formErrors.revisione_ufficiale\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sistema\",\n                label: \"Sistema\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sistema,\n                onChange: handleFormChange,\n                error: !!formErrors.sistema,\n                helperText: formErrors.sistema\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this), formWarnings.sistema && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                sx: {\n                  color: 'warning.main',\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                  fontSize: \"small\",\n                  sx: {\n                    mr: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 21\n                }, this), formWarnings.sistema]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utility\",\n                label: \"Utility\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utility,\n                onChange: handleFormChange,\n                error: !!formErrors.utility,\n                helperText: formErrors.utility\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 17\n              }, this), formWarnings.utility && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                sx: {\n                  color: 'warning.main',\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                  fontSize: \"small\",\n                  sx: {\n                    mr: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 21\n                }, this), formWarnings.utility]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                name: \"colore_cavo\",\n                label: \"Colore Cavo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.colore_cavo,\n                onChange: handleFormChange,\n                error: !!formErrors.colore_cavo,\n                helperText: formErrors.colore_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 17\n              }, this), formWarnings.colore_cavo && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                sx: {\n                  color: 'warning.main',\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                  fontSize: \"small\",\n                  sx: {\n                    mr: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 21\n                }, this), formWarnings.colore_cavo]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                name: \"tipologia\",\n                label: \"Tipologia\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.tipologia,\n                onChange: handleFormChange,\n                error: !!formErrors.tipologia,\n                helperText: formErrors.tipologia\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this), formWarnings.tipologia && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                sx: {\n                  color: 'warning.main',\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                  fontSize: \"small\",\n                  sx: {\n                    mr: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 21\n                }, this), formWarnings.tipologia]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_conduttori\",\n                label: \"Numero Conduttori\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_conduttori,\n                onChange: handleFormChange,\n                error: !!formErrors.n_conduttori,\n                helperText: formErrors.n_conduttori\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 17\n              }, this), formWarnings.n_conduttori && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                sx: {\n                  color: 'warning.main',\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                  fontSize: \"small\",\n                  sx: {\n                    mr: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 21\n                }, this), formWarnings.n_conduttori]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sezione\",\n                label: \"Sezione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sezione,\n                onChange: handleFormChange,\n                error: !!formErrors.sezione,\n                helperText: formErrors.sezione\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 17\n              }, this), formWarnings.sezione && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                sx: {\n                  color: 'warning.main',\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                  fontSize: \"small\",\n                  sx: {\n                    mr: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 21\n                }, this), formWarnings.sezione]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sh\",\n                label: \"SH (S/N)\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sh || formData.SH,\n                onChange: handleFormChange,\n                error: !!formErrors.sh,\n                helperText: formErrors.sh || 'Inserire S o N'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 17\n              }, this), formWarnings.sh && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                sx: {\n                  color: 'warning.main',\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                  fontSize: \"small\",\n                  sx: {\n                    mr: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 21\n                }, this), formWarnings.sh]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_partenza\",\n                label: \"Ubicazione Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utenza_partenza\",\n                label: \"Utenza Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utenza_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"descrizione_utenza_partenza\",\n                label: \"Descrizione Utenza Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.descrizione_utenza_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_arrivo\",\n                label: \"Ubicazione Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utenza_arrivo\",\n                label: \"Utenza Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utenza_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"descrizione_utenza_arrivo\",\n                label: \"Descrizione Utenza Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.descrizione_utenza_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_teorici\",\n                label: \"Metri Teorici\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_teorici,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.metri_teorici,\n                helperText: formErrors.metri_teorici\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 17\n              }, this), formWarnings.metri_teorici && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                sx: {\n                  color: 'warning.main',\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                  fontSize: \"small\",\n                  sx: {\n                    mr: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 21\n                }, this), formWarnings.metri_teorici]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"id_bobina\",\n                label: \"ID Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.id_bobina,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Stato Installazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"stato_installazione\",\n                  value: formData.stato_installazione,\n                  label: \"Stato Installazione\",\n                  onChange: handleFormChange,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"DA POSARE\",\n                    children: \"DA POSARE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"IN POSA\",\n                    children: \"IN POSA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"POSATO\",\n                    children: \"POSATO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"SPARE\",\n                    children: \"SPARE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 650,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || !formData.id_cavo,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 69\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaCavo' || dialogType === 'eliminaCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: dialogType === 'selezionaCavo' ? 'Seleziona Cavo da Modificare' : 'Seleziona Cavo da Eliminare'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: cavo.id_cavo,\n                secondary: `${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 21\n              }, this)\n            }, cavo.id_cavo, false, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 13\n          }, this), dialogType === 'eliminaCavo' && selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 71\n            }, this),\n            children: \"Elimina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'modificaCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"id_cavo\",\n                label: \"ID Cavo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.id_cavo,\n                disabled: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"revisione_ufficiale\",\n                label: \"Revisione Ufficiale\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.revisione_ufficiale,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sistema\",\n                label: \"Sistema\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sistema,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utility\",\n                label: \"Utility\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utility,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"colore_cavo\",\n                label: \"Colore Cavo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.colore_cavo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"tipologia\",\n                label: \"Tipologia\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.tipologia,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 768,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_conduttori\",\n                label: \"Numero Conduttori\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_conduttori,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sezione\",\n                label: \"Sezione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sezione,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 788,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"SH\",\n                label: \"SH\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.SH,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 798,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 797,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_partenza\",\n                label: \"Ubicazione Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 808,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utenza_partenza\",\n                label: \"Utenza Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utenza_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 818,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"descrizione_utenza_partenza\",\n                label: \"Descrizione Utenza Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.descrizione_utenza_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_arrivo\",\n                label: \"Ubicazione Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 838,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utenza_arrivo\",\n                label: \"Utenza Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utenza_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"descrizione_utenza_arrivo\",\n                label: \"Descrizione Utenza Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.descrizione_utenza_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_teorici\",\n                label: \"Metri Teorici\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_teorici,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 868,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"id_bobina\",\n                label: \"ID Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.id_bobina,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 878,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Stato Installazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 890,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"stato_installazione\",\n                  value: formData.stato_installazione,\n                  label: \"Stato Installazione\",\n                  onChange: handleFormChange,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"DA POSARE\",\n                    children: \"DA POSARE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 897,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"IN POSA\",\n                    children: \"IN POSA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 898,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"POSATO\",\n                    children: \"POSATO\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 899,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"SPARE\",\n                    children: \"SPARE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 900,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 891,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 889,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 716,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 715,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 907,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 69\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 908,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 906,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 713,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '280px',\n        mr: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Posa Cavi e Collegamenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 928,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 931,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          dense: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('inserisciMetri'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 935,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 934,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"1. Inserisci metri posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 937,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 933,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('modificaCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 942,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 941,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"2. Modifica cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 944,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 940,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('aggiungiCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"3. Aggiungi nuovo cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 951,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 947,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('eliminaCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 956,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"4. Elimina cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 954,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('modificaBobina'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 963,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"5. Modifica bobina cavo posato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 961,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('collegamentoCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"6. Gestisci collegamenti cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 972,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 968,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 932,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 927,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 926,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flexGrow: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          minHeight: '300px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: selectedOption ? /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          children: \"Seleziona un'opzione dal menu a sinistra per iniziare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 982,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          children: \"Seleziona un'opzione dal menu a sinistra per iniziare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 986,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 980,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 979,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 924,\n    columnNumber: 5\n  }, this);\n};\n_s(PosaCaviCollegamenti, \"RAoIbsJly088OQNJQPHjtSaiJFU=\");\n_c = PosaCaviCollegamenti;\nexport default PosaCaviCollegamenti;\nvar _c;\n$RefreshReg$(_c, \"PosaCaviCollegamenti\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Divider", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Grid", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Cable", "CableIcon", "Save", "SaveIcon", "Warning", "WarningIcon", "caviService", "validateCavoData", "validateField", "isEmpty", "jsxDEV", "_jsxDEV", "PosaCaviCollegamenti", "cantiereId", "onSuccess", "onError", "_s", "loading", "setLoading", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "cavi", "<PERSON><PERSON><PERSON>", "caviLoading", "setCaviLoading", "loadCavi", "caviData", "get<PERSON><PERSON>", "error", "console", "handleOptionSelect", "option", "revisione_ufficiale", "sistema", "utility", "colore_cavo", "tipologia", "n_conduttori", "sezione", "SH", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "responsabile_posa", "stato_installazione", "handleCloseDialog", "handleCavoSelect", "cavo", "handleFormChange", "e", "name", "value", "target", "additionalParams", "metriTeorici", "parseFloat", "result", "prev", "valid", "message", "warning", "handleSave", "isNaN", "updateMetri<PERSON><PERSON><PERSON>", "updateBobina", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "validatedData", "createCavo", "updateCavo", "Object", "keys", "length", "warningMessages", "values", "join", "warn", "deleteCavo", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "variant", "gutterBottom", "map", "button", "onClick", "primary", "secondary", "sx", "mt", "margin", "label", "type", "onChange", "required", "helperText", "disabled", "startIcon", "size", "container", "spacing", "item", "xs", "sm", "color", "display", "alignItems", "fontSize", "mr", "sh", "width", "p", "mb", "component", "dense", "flexGrow", "minHeight", "justifyContent", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/PosaCaviCollegamenti.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Grid,\n  Alert,\n  CircularProgress,\n  FormHelperText\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Cable as CableIcon,\n  Save as SaveIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\n\nconst PosaCaviCollegamenti = ({ cantiereId, onSuccess, onError }) => {\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      setCavi(caviData);\n    } catch (error) {\n      onError('Errore nel caricamento dei cavi');\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi();\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      setDialogType('aggiungiCavo');\n      setFormData({\n        id_cavo: '',\n        revisione_ufficiale: '0',\n        sistema: '',\n        utility: '',\n        colore_cavo: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        SH: '',\n        ubicazione_partenza: '',\n        utenza_partenza: '',\n        descrizione_utenza_partenza: '',\n        ubicazione_arrivo: '',\n        utenza_arrivo: '',\n        descrizione_utenza_arrivo: '',\n        metri_teorici: '',\n        metratura_reale: '0',\n        responsabile_posa: '',\n        id_bobina: '',\n        stato_installazione: 'DA POSARE'\n      });\n      setOpenDialog(true);\n    } else if (option === 'modificaCavo') {\n      loadCavi();\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi();\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    } else if (option === 'collegamentoCavo') {\n      // Implementazione futura per i collegamenti\n      onError('Funzionalità in fase di implementazione');\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'aggiungiCavo' || dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({ metri_posati: 'Inserire un valore numerico valido' });\n          setLoading(false);\n          return;\n        }\n\n        await caviService.updateMetriPosati(\n          cantiereId,\n          formData.id_cavo,\n          parseFloat(formData.metri_posati)\n        );\n        onSuccess('Metri posati aggiornati con successo');\n      } else if (dialogType === 'modificaBobina') {\n        await caviService.updateBobina(\n          cantiereId,\n          formData.id_cavo,\n          formData.id_bobina\n        );\n        onSuccess('Bobina aggiornata con successo');\n      } else if (dialogType === 'aggiungiCavo' || dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n\n        if (dialogType === 'aggiungiCavo') {\n          await caviService.createCavo(cantiereId, validatedData);\n          onSuccess('Cavo aggiunto con successo');\n        } else {\n          await caviService.updateCavo(cantiereId, validatedData.id_cavo, validatedData);\n          onSuccess('Cavo modificato con successo');\n        }\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo);\n        onSuccess('Cavo eliminato con successo');\n      }\n\n      handleCloseDialog();\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'inserisciMetri') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Inserisci Metri Posati</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metri teorici: {selectedCavo.metri_teorici || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metratura attuale: {selectedCavo.metratura_reale || '0'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"metri_posati\"\n                  label=\"Metri posati da aggiungere\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_posati}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_posati}\n                  helperText={formErrors.metri_posati}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading || !formData.metri_posati}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Bobina Cavo Posato</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`Bobina attuale: ${cavo.id_bobina || 'Non assegnata'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Bobina attuale: {selectedCavo.id_bobina || 'Non assegnata'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"id_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_bobina}\n                  onChange={handleFormChange}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'aggiungiCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Aggiungi Nuovo Cavo</DialogTitle>\n          <DialogContent>\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.id_cavo}\n                  helperText={formErrors.id_cavo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"revisione_ufficiale\"\n                  label=\"Revisione Ufficiale\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.revisione_ufficiale}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.revisione_ufficiale}\n                  helperText={formErrors.revisione_ufficiale}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sistema\"\n                  label=\"Sistema\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sistema}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sistema}\n                  helperText={formErrors.sistema}\n                />\n                {formWarnings.sistema && (\n                  <FormHelperText sx={{ color: 'warning.main', display: 'flex', alignItems: 'center' }}>\n                    <WarningIcon fontSize=\"small\" sx={{ mr: 0.5 }} />\n                    {formWarnings.sistema}\n                  </FormHelperText>\n                )}\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility}\n                />\n                {formWarnings.utility && (\n                  <FormHelperText sx={{ color: 'warning.main', display: 'flex', alignItems: 'center' }}>\n                    <WarningIcon fontSize=\"small\" sx={{ mr: 0.5 }} />\n                    {formWarnings.utility}\n                  </FormHelperText>\n                )}\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"colore_cavo\"\n                  label=\"Colore Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.colore_cavo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.colore_cavo}\n                  helperText={formErrors.colore_cavo}\n                />\n                {formWarnings.colore_cavo && (\n                  <FormHelperText sx={{ color: 'warning.main', display: 'flex', alignItems: 'center' }}>\n                    <WarningIcon fontSize=\"small\" sx={{ mr: 0.5 }} />\n                    {formWarnings.colore_cavo}\n                  </FormHelperText>\n                )}\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia}\n                />\n                {formWarnings.tipologia && (\n                  <FormHelperText sx={{ color: 'warning.main', display: 'flex', alignItems: 'center' }}>\n                    <WarningIcon fontSize=\"small\" sx={{ mr: 0.5 }} />\n                    {formWarnings.tipologia}\n                  </FormHelperText>\n                )}\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"Numero Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_conduttori}\n                  helperText={formErrors.n_conduttori}\n                />\n                {formWarnings.n_conduttori && (\n                  <FormHelperText sx={{ color: 'warning.main', display: 'flex', alignItems: 'center' }}>\n                    <WarningIcon fontSize=\"small\" sx={{ mr: 0.5 }} />\n                    {formWarnings.n_conduttori}\n                  </FormHelperText>\n                )}\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione}\n                />\n                {formWarnings.sezione && (\n                  <FormHelperText sx={{ color: 'warning.main', display: 'flex', alignItems: 'center' }}>\n                    <WarningIcon fontSize=\"small\" sx={{ mr: 0.5 }} />\n                    {formWarnings.sezione}\n                  </FormHelperText>\n                )}\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sh\"\n                  label=\"SH (S/N)\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sh || formData.SH}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sh}\n                  helperText={formErrors.sh || 'Inserire S o N'}\n                />\n                {formWarnings.sh && (\n                  <FormHelperText sx={{ color: 'warning.main', display: 'flex', alignItems: 'center' }}>\n                    <WarningIcon fontSize=\"small\" sx={{ mr: 0.5 }} />\n                    {formWarnings.sh}\n                  </FormHelperText>\n                )}\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_partenza\"\n                  label=\"Ubicazione Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utenza_partenza\"\n                  label=\"Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"descrizione_utenza_partenza\"\n                  label=\"Descrizione Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_arrivo\"\n                  label=\"Ubicazione Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utenza_arrivo\"\n                  label=\"Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"descrizione_utenza_arrivo\"\n                  label=\"Descrizione Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_teorici\"\n                  label=\"Metri Teorici\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_teorici}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_teorici}\n                  helperText={formErrors.metri_teorici}\n                />\n                {formWarnings.metri_teorici && (\n                  <FormHelperText sx={{ color: 'warning.main', display: 'flex', alignItems: 'center' }}>\n                    <WarningIcon fontSize=\"small\" sx={{ mr: 0.5 }} />\n                    {formWarnings.metri_teorici}\n                  </FormHelperText>\n                )}\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_bobina}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Stato Installazione</InputLabel>\n                  <Select\n                    name=\"stato_installazione\"\n                    value={formData.stato_installazione}\n                    label=\"Stato Installazione\"\n                    onChange={handleFormChange}\n                  >\n                    <MenuItem value=\"DA POSARE\">DA POSARE</MenuItem>\n                    <MenuItem value=\"IN POSA\">IN POSA</MenuItem>\n                    <MenuItem value=\"POSATO\">POSATO</MenuItem>\n                    <MenuItem value=\"SPARE\">SPARE</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading || !formData.id_cavo}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaCavo' || dialogType === 'eliminaCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {dialogType === 'selezionaCavo' ? 'Seleziona Cavo da Modificare' : 'Seleziona Cavo da Eliminare'}\n          </DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : (\n              <List>\n                {cavi.map((cavo) => (\n                  <ListItem\n                    button\n                    key={cavo.id_cavo}\n                    onClick={() => handleCavoSelect(cavo)}\n                  >\n                    <ListItemText\n                      primary={cavo.id_cavo}\n                      secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {dialogType === 'eliminaCavo' && selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color=\"error\"\n                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}\n              >\n                Elimina\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Cavo</DialogTitle>\n          <DialogContent>\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  disabled\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"revisione_ufficiale\"\n                  label=\"Revisione Ufficiale\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.revisione_ufficiale}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sistema\"\n                  label=\"Sistema\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sistema}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"colore_cavo\"\n                  label=\"Colore Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.colore_cavo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"Numero Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"SH\"\n                  label=\"SH\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.SH}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_partenza\"\n                  label=\"Ubicazione Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utenza_partenza\"\n                  label=\"Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"descrizione_utenza_partenza\"\n                  label=\"Descrizione Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_arrivo\"\n                  label=\"Ubicazione Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utenza_arrivo\"\n                  label=\"Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"descrizione_utenza_arrivo\"\n                  label=\"Descrizione Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_teorici\"\n                  label=\"Metri Teorici\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_teorici}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_bobina}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Stato Installazione</InputLabel>\n                  <Select\n                    name=\"stato_installazione\"\n                    value={formData.stato_installazione}\n                    label=\"Stato Installazione\"\n                    onChange={handleFormChange}\n                  >\n                    <MenuItem value=\"DA POSARE\">DA POSARE</MenuItem>\n                    <MenuItem value=\"IN POSA\">IN POSA</MenuItem>\n                    <MenuItem value=\"POSATO\">POSATO</MenuItem>\n                    <MenuItem value=\"SPARE\">SPARE</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      {/* Menu a cascata nella sidebar */}\n      <Box sx={{ width: '280px', mr: 3 }}>\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Posa Cavi e Collegamenti\n          </Typography>\n          <Divider sx={{ mb: 2 }} />\n          <List component=\"nav\" dense>\n            <ListItemButton onClick={() => handleOptionSelect('inserisciMetri')}>\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"1. Inserisci metri posati\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaCavo')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"2. Modifica cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('aggiungiCavo')}>\n              <ListItemIcon>\n                <AddIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"3. Aggiungi nuovo cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('eliminaCavo')}>\n              <ListItemIcon>\n                <DeleteIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"4. Elimina cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaBobina')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"5. Modifica bobina cavo posato\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('collegamentoCavo')}>\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"6. Gestisci collegamenti cavo\" />\n            </ListItemButton>\n          </List>\n        </Paper>\n      </Box>\n\n      {/* Area principale per il contenuto */}\n      <Box sx={{ flexGrow: 1 }}>\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {selectedOption ? (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu a sinistra per iniziare.\n            </Typography>\n          ) : (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu a sinistra per iniziare.\n            </Typography>\n          )}\n        </Paper>\n      </Box>\n\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default PosaCaviCollegamenti;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,QACT,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvF,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACnE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmD,UAAU,EAAEC,aAAa,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC;IACvC2D,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACkE,IAAI,EAAEC,OAAO,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACoE,WAAW,EAAEC,cAAc,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAMsE,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFD,cAAc,CAAC,IAAI,CAAC;MACpB,MAAME,QAAQ,GAAG,MAAMnC,WAAW,CAACoC,OAAO,CAAC7B,UAAU,EAAE,CAAC,CAAC;MACzDwB,OAAO,CAACI,QAAQ,CAAC;IACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd5B,OAAO,CAAC,iCAAiC,CAAC;MAC1C6B,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D,CAAC,SAAS;MACRJ,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMM,kBAAkB,GAAIC,MAAM,IAAK;IACrC1B,iBAAiB,CAAC0B,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,gBAAgB,IAAIA,MAAM,KAAK,gBAAgB,EAAE;MAC9DN,QAAQ,CAAC,CAAC;MACVhB,aAAa,CAACsB,MAAM,CAAC;MACrBxB,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIwB,MAAM,KAAK,cAAc,EAAE;MACpCtB,aAAa,CAAC,cAAc,CAAC;MAC7BI,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXkB,mBAAmB,EAAE,GAAG;QACxBC,OAAO,EAAE,EAAE;QACXC,OAAO,EAAE,EAAE;QACXC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,EAAE;QACXC,EAAE,EAAE,EAAE;QACNC,mBAAmB,EAAE,EAAE;QACvBC,eAAe,EAAE,EAAE;QACnBC,2BAA2B,EAAE,EAAE;QAC/BC,iBAAiB,EAAE,EAAE;QACrBC,aAAa,EAAE,EAAE;QACjBC,yBAAyB,EAAE,EAAE;QAC7BC,aAAa,EAAE,EAAE;QACjBC,eAAe,EAAE,GAAG;QACpBC,iBAAiB,EAAE,EAAE;QACrBhC,SAAS,EAAE,EAAE;QACbiC,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACF1C,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIwB,MAAM,KAAK,cAAc,EAAE;MACpCN,QAAQ,CAAC,CAAC;MACVhB,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIwB,MAAM,KAAK,aAAa,EAAE;MACnCN,QAAQ,CAAC,CAAC;MACVhB,aAAa,CAAC,aAAa,CAAC;MAC5BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIwB,MAAM,KAAK,kBAAkB,EAAE;MACxC;MACA/B,OAAO,CAAC,yCAAyC,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMkD,iBAAiB,GAAGA,CAAA,KAAM;IAC9B3C,aAAa,CAAC,KAAK,CAAC;IACpBI,eAAe,CAAC,IAAI,CAAC;IACrBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAM+B,gBAAgB,GAAIC,IAAI,IAAK;IACjCzC,eAAe,CAACyC,IAAI,CAAC;IACrB,IAAI5C,UAAU,KAAK,gBAAgB,EAAE;MACnCK,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEsC,IAAI,CAACtC,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIP,UAAU,KAAK,gBAAgB,EAAE;MAC1CK,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEsC,IAAI,CAACtC,OAAO;QACrBE,SAAS,EAAEoC,IAAI,CAACpC,SAAS,IAAI;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIR,UAAU,KAAK,eAAe,EAAE;MACzCC,aAAa,CAAC,cAAc,CAAC;MAC7BI,WAAW,CAAC;QACV,GAAGuC,IAAI;QACPN,aAAa,EAAEM,IAAI,CAACN,aAAa,IAAI,EAAE;QACvCC,eAAe,EAAEK,IAAI,CAACL,eAAe,IAAI;MAC3C,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMM,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA5C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC2C,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,IAAIhD,UAAU,KAAK,cAAc,IAAIA,UAAU,KAAK,cAAc,EAAE;MAClE,MAAMkD,gBAAgB,GAAG,CAAC,CAAC;MAC3B,IAAIH,IAAI,KAAK,iBAAiB,EAAE;QAC9BG,gBAAgB,CAACC,YAAY,GAAGC,UAAU,CAAChD,QAAQ,CAACkC,aAAa,IAAI,CAAC,CAAC;MACzE;MAEA,MAAMe,MAAM,GAAGpE,aAAa,CAAC8D,IAAI,EAAEC,KAAK,EAAEE,gBAAgB,CAAC;;MAE3D;MACAxC,aAAa,CAAC4C,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACP,IAAI,GAAG,CAACM,MAAM,CAACE,KAAK,GAAGF,MAAM,CAACG,OAAO,GAAG;MAC3C,CAAC,CAAC,CAAC;;MAEH;MACA5C,eAAe,CAAC0C,IAAI,KAAK;QACvB,GAAGA,IAAI;QACP,CAACP,IAAI,GAAGM,MAAM,CAACI,OAAO,GAAGJ,MAAM,CAACG,OAAO,GAAG;MAC5C,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF/D,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIK,UAAU,KAAK,gBAAgB,EAAE;QACnC;QACA,IAAId,OAAO,CAACkB,QAAQ,CAACG,YAAY,CAAC,IAAIoD,KAAK,CAACP,UAAU,CAAChD,QAAQ,CAACG,YAAY,CAAC,CAAC,EAAE;UAC9EG,aAAa,CAAC;YAAEH,YAAY,EAAE;UAAqC,CAAC,CAAC;UACrEZ,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEA,MAAMZ,WAAW,CAAC6E,iBAAiB,CACjCtE,UAAU,EACVc,QAAQ,CAACE,OAAO,EAChB8C,UAAU,CAAChD,QAAQ,CAACG,YAAY,CAClC,CAAC;QACDhB,SAAS,CAAC,sCAAsC,CAAC;MACnD,CAAC,MAAM,IAAIS,UAAU,KAAK,gBAAgB,EAAE;QAC1C,MAAMjB,WAAW,CAAC8E,YAAY,CAC5BvE,UAAU,EACVc,QAAQ,CAACE,OAAO,EAChBF,QAAQ,CAACI,SACX,CAAC;QACDjB,SAAS,CAAC,gCAAgC,CAAC;MAC7C,CAAC,MAAM,IAAIS,UAAU,KAAK,cAAc,IAAIA,UAAU,KAAK,cAAc,EAAE;QACzE;QACA,MAAM8D,UAAU,GAAG9E,gBAAgB,CAACoB,QAAQ,CAAC;QAE7C,IAAI,CAAC0D,UAAU,CAACC,OAAO,EAAE;UACvBrD,aAAa,CAACoD,UAAU,CAACE,MAAM,CAAC;UAChCpD,eAAe,CAACkD,UAAU,CAACG,QAAQ,CAAC;UACpCtE,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMuE,aAAa,GAAGJ,UAAU,CAACI,aAAa;QAE9C,IAAIlE,UAAU,KAAK,cAAc,EAAE;UACjC,MAAMjB,WAAW,CAACoF,UAAU,CAAC7E,UAAU,EAAE4E,aAAa,CAAC;UACvD3E,SAAS,CAAC,4BAA4B,CAAC;QACzC,CAAC,MAAM;UACL,MAAMR,WAAW,CAACqF,UAAU,CAAC9E,UAAU,EAAE4E,aAAa,CAAC5D,OAAO,EAAE4D,aAAa,CAAC;UAC9E3E,SAAS,CAAC,8BAA8B,CAAC;QAC3C;;QAEA;QACA,IAAI8E,MAAM,CAACC,IAAI,CAACR,UAAU,CAACG,QAAQ,CAAC,CAACM,MAAM,GAAG,CAAC,EAAE;UAC/C,MAAMC,eAAe,GAAGH,MAAM,CAACI,MAAM,CAACX,UAAU,CAACG,QAAQ,CAAC,CAACS,IAAI,CAAC,IAAI,CAAC;UACrErD,OAAO,CAACsD,IAAI,CAAC,gCAAgC,EAAEH,eAAe,CAAC;QACjE;MACF,CAAC,MAAM,IAAIxE,UAAU,KAAK,aAAa,EAAE;QACvC,MAAMjB,WAAW,CAAC6F,UAAU,CAACtF,UAAU,EAAEY,YAAY,CAACI,OAAO,CAAC;QAC9Df,SAAS,CAAC,6BAA6B,CAAC;MAC1C;MAEAmD,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACd5B,OAAO,CAAC,gCAAgC,IAAI4B,KAAK,CAACoC,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACnFnC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkF,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI7E,UAAU,KAAK,gBAAgB,EAAE;MACnC,oBACEZ,OAAA,CAAC9B,MAAM;QAACwH,IAAI,EAAEhF,UAAW;QAACiF,OAAO,EAAErC,iBAAkB;QAACsC,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E9F,OAAA,CAAC7B,WAAW;UAAA2H,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACjDlG,OAAA,CAAC5B,aAAa;UAAA0H,QAAA,EACXnE,WAAW,gBACV3B,OAAA,CAACnB,gBAAgB;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBzE,IAAI,CAAC0D,MAAM,KAAK,CAAC,gBACnBnF,OAAA,CAACpB,KAAK;YAACuH,QAAQ,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAACpF,YAAY,gBACfd,OAAA,CAACxC,GAAG;YAAAsI,QAAA,gBACF9F,OAAA,CAACvC,UAAU;cAAC2I,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblG,OAAA,CAACnC,IAAI;cAAAiI,QAAA,EACFrE,IAAI,CAAC6E,GAAG,CAAE9C,IAAI,iBACbxD,OAAA,CAAClC,QAAQ;gBACPyI,MAAM;gBAENC,OAAO,EAAEA,CAAA,KAAMjD,gBAAgB,CAACC,IAAI,CAAE;gBAAAsC,QAAA,eAEtC9F,OAAA,CAACjC,YAAY;kBACX0I,OAAO,EAAEjD,IAAI,CAACtC,OAAQ;kBACtBwF,SAAS,EAAE,GAAGlD,IAAI,CAAChB,SAAS,IAAI,KAAK,UAAUgB,IAAI,CAACZ,mBAAmB,IAAI,KAAK,OAAOY,IAAI,CAACT,iBAAiB,IAAI,KAAK;gBAAG;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC,GANG1C,IAAI,CAACtC,OAAO;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAENlG,OAAA,CAACxC,GAAG;YAACmJ,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAd,QAAA,gBACjB9F,OAAA,CAACvC,UAAU;cAAC2I,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,GAAC,oBACzB,EAAChF,YAAY,CAACI,OAAO;YAAA;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACblG,OAAA,CAACvC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAP,QAAA,GAAC,iBACxB,EAAChF,YAAY,CAACoC,aAAa,IAAI,KAAK;YAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACblG,OAAA,CAACvC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAP,QAAA,GAAC,qBACpB,EAAChF,YAAY,CAACqC,eAAe,IAAI,GAAG;YAAA;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACblG,OAAA,CAAC1B,SAAS;cACRuI,MAAM,EAAC,OAAO;cACdlD,IAAI,EAAC,cAAc;cACnBmD,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,QAAQ;cACblB,SAAS;cACTO,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAE5C,QAAQ,CAACG,YAAa;cAC7B6F,QAAQ,EAAEvD,gBAAiB;cAC3BwD,QAAQ;cACRjF,KAAK,EAAE,CAAC,CAACX,UAAU,CAACF,YAAa;cACjC+F,UAAU,EAAE7F,UAAU,CAACF,YAAa;cACpCwF,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE;YAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBlG,OAAA,CAAC3B,aAAa;UAAAyH,QAAA,gBACZ9F,OAAA,CAACtC,MAAM;YAAC8I,OAAO,EAAElD,iBAAkB;YAAAwC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDpF,YAAY,iBACXd,OAAA,CAACtC,MAAM;YACL8I,OAAO,EAAElC,UAAW;YACpB6C,QAAQ,EAAE7G,OAAO,IAAI,CAACU,QAAQ,CAACG,YAAa;YAC5CiG,SAAS,EAAE9G,OAAO,gBAAGN,OAAA,CAACnB,gBAAgB;cAACwI,IAAI,EAAE;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlG,OAAA,CAACR,QAAQ;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAItF,UAAU,KAAK,gBAAgB,EAAE;MAC1C,oBACEZ,OAAA,CAAC9B,MAAM;QAACwH,IAAI,EAAEhF,UAAW;QAACiF,OAAO,EAAErC,iBAAkB;QAACsC,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E9F,OAAA,CAAC7B,WAAW;UAAA2H,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACtDlG,OAAA,CAAC5B,aAAa;UAAA0H,QAAA,EACXnE,WAAW,gBACV3B,OAAA,CAACnB,gBAAgB;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBzE,IAAI,CAAC0D,MAAM,KAAK,CAAC,gBACnBnF,OAAA,CAACpB,KAAK;YAACuH,QAAQ,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAACpF,YAAY,gBACfd,OAAA,CAACxC,GAAG;YAAAsI,QAAA,gBACF9F,OAAA,CAACvC,UAAU;cAAC2I,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblG,OAAA,CAACnC,IAAI;cAAAiI,QAAA,EACFrE,IAAI,CAAC6E,GAAG,CAAE9C,IAAI,iBACbxD,OAAA,CAAClC,QAAQ;gBACPyI,MAAM;gBAENC,OAAO,EAAEA,CAAA,KAAMjD,gBAAgB,CAACC,IAAI,CAAE;gBAAAsC,QAAA,eAEtC9F,OAAA,CAACjC,YAAY;kBACX0I,OAAO,EAAEjD,IAAI,CAACtC,OAAQ;kBACtBwF,SAAS,EAAE,mBAAmBlD,IAAI,CAACpC,SAAS,IAAI,eAAe;gBAAG;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC,GANG1C,IAAI,CAACtC,OAAO;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAENlG,OAAA,CAACxC,GAAG;YAACmJ,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAd,QAAA,gBACjB9F,OAAA,CAACvC,UAAU;cAAC2I,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,GAAC,oBACzB,EAAChF,YAAY,CAACI,OAAO;YAAA;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACblG,OAAA,CAACvC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAP,QAAA,GAAC,kBACvB,EAAChF,YAAY,CAACM,SAAS,IAAI,eAAe;YAAA;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACblG,OAAA,CAAC1B,SAAS;cACRuI,MAAM,EAAC,OAAO;cACdlD,IAAI,EAAC,WAAW;cAChBmD,KAAK,EAAC,WAAW;cACjBjB,SAAS;cACTO,OAAO,EAAC,UAAU;cAClBxC,KAAK,EAAE5C,QAAQ,CAACI,SAAU;cAC1B4F,QAAQ,EAAEvD,gBAAiB;cAC3BkD,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE;YAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBlG,OAAA,CAAC3B,aAAa;UAAAyH,QAAA,gBACZ9F,OAAA,CAACtC,MAAM;YAAC8I,OAAO,EAAElD,iBAAkB;YAAAwC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDpF,YAAY,iBACXd,OAAA,CAACtC,MAAM;YACL8I,OAAO,EAAElC,UAAW;YACpB6C,QAAQ,EAAE7G,OAAQ;YAClB8G,SAAS,EAAE9G,OAAO,gBAAGN,OAAA,CAACnB,gBAAgB;cAACwI,IAAI,EAAE;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlG,OAAA,CAACR,QAAQ;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAItF,UAAU,KAAK,cAAc,EAAE;MACxC,oBACEZ,OAAA,CAAC9B,MAAM;QAACwH,IAAI,EAAEhF,UAAW;QAACiF,OAAO,EAAErC,iBAAkB;QAACsC,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E9F,OAAA,CAAC7B,WAAW;UAAA2H,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC9ClG,OAAA,CAAC5B,aAAa;UAAA0H,QAAA,eACZ9F,OAAA,CAACrB,IAAI;YAAC2I,SAAS;YAACC,OAAO,EAAE,CAAE;YAACZ,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAd,QAAA,gBACxC9F,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,SAAS;gBACdmD,KAAK,EAAC,SAAS;gBACfjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACE,OAAQ;gBACxB8F,QAAQ,EAAEvD,gBAAiB;gBAC3BwD,QAAQ;gBACRjF,KAAK,EAAE,CAAC,CAACX,UAAU,CAACH,OAAQ;gBAC5BgG,UAAU,EAAE7F,UAAU,CAACH;cAAQ;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,qBAAqB;gBAC1BmD,KAAK,EAAC,qBAAqB;gBAC3BjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACoB,mBAAoB;gBACpC4E,QAAQ,EAAEvD,gBAAiB;gBAC3BwD,QAAQ;gBACRjF,KAAK,EAAE,CAAC,CAACX,UAAU,CAACe,mBAAoB;gBACxC8E,UAAU,EAAE7F,UAAU,CAACe;cAAoB;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,gBACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,SAAS;gBACdmD,KAAK,EAAC,SAAS;gBACfjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACqB,OAAQ;gBACxB2E,QAAQ,EAAEvD,gBAAiB;gBAC3BzB,KAAK,EAAE,CAAC,CAACX,UAAU,CAACgB,OAAQ;gBAC5B6E,UAAU,EAAE7F,UAAU,CAACgB;cAAQ;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,EACD3E,YAAY,CAACc,OAAO,iBACnBrC,OAAA,CAAClB,cAAc;gBAAC6H,EAAE,EAAE;kBAAEgB,KAAK,EAAE,cAAc;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAA/B,QAAA,gBACnF9F,OAAA,CAACN,WAAW;kBAACoI,QAAQ,EAAC,OAAO;kBAACnB,EAAE,EAAE;oBAAEoB,EAAE,EAAE;kBAAI;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChD3E,YAAY,CAACc,OAAO;cAAA;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CACjB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,gBACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,SAAS;gBACdmD,KAAK,EAAC,SAAS;gBACfjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACsB,OAAQ;gBACxB0E,QAAQ,EAAEvD,gBAAiB;gBAC3BzB,KAAK,EAAE,CAAC,CAACX,UAAU,CAACiB,OAAQ;gBAC5B4E,UAAU,EAAE7F,UAAU,CAACiB;cAAQ;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,EACD3E,YAAY,CAACe,OAAO,iBACnBtC,OAAA,CAAClB,cAAc;gBAAC6H,EAAE,EAAE;kBAAEgB,KAAK,EAAE,cAAc;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAA/B,QAAA,gBACnF9F,OAAA,CAACN,WAAW;kBAACoI,QAAQ,EAAC,OAAO;kBAACnB,EAAE,EAAE;oBAAEoB,EAAE,EAAE;kBAAI;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChD3E,YAAY,CAACe,OAAO;cAAA;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CACjB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,gBACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,aAAa;gBAClBmD,KAAK,EAAC,aAAa;gBACnBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACuB,WAAY;gBAC5ByE,QAAQ,EAAEvD,gBAAiB;gBAC3BzB,KAAK,EAAE,CAAC,CAACX,UAAU,CAACkB,WAAY;gBAChC2E,UAAU,EAAE7F,UAAU,CAACkB;cAAY;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,EACD3E,YAAY,CAACgB,WAAW,iBACvBvC,OAAA,CAAClB,cAAc;gBAAC6H,EAAE,EAAE;kBAAEgB,KAAK,EAAE,cAAc;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAA/B,QAAA,gBACnF9F,OAAA,CAACN,WAAW;kBAACoI,QAAQ,EAAC,OAAO;kBAACnB,EAAE,EAAE;oBAAEoB,EAAE,EAAE;kBAAI;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChD3E,YAAY,CAACgB,WAAW;cAAA;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACjB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,gBACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,WAAW;gBAChBmD,KAAK,EAAC,WAAW;gBACjBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACwB,SAAU;gBAC1BwE,QAAQ,EAAEvD,gBAAiB;gBAC3BzB,KAAK,EAAE,CAAC,CAACX,UAAU,CAACmB,SAAU;gBAC9B0E,UAAU,EAAE7F,UAAU,CAACmB;cAAU;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,EACD3E,YAAY,CAACiB,SAAS,iBACrBxC,OAAA,CAAClB,cAAc;gBAAC6H,EAAE,EAAE;kBAAEgB,KAAK,EAAE,cAAc;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAA/B,QAAA,gBACnF9F,OAAA,CAACN,WAAW;kBAACoI,QAAQ,EAAC,OAAO;kBAACnB,EAAE,EAAE;oBAAEoB,EAAE,EAAE;kBAAI;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChD3E,YAAY,CAACiB,SAAS;cAAA;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CACjB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,gBACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,cAAc;gBACnBmD,KAAK,EAAC,mBAAmB;gBACzBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACyB,YAAa;gBAC7BuE,QAAQ,EAAEvD,gBAAiB;gBAC3BzB,KAAK,EAAE,CAAC,CAACX,UAAU,CAACoB,YAAa;gBACjCyE,UAAU,EAAE7F,UAAU,CAACoB;cAAa;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,EACD3E,YAAY,CAACkB,YAAY,iBACxBzC,OAAA,CAAClB,cAAc;gBAAC6H,EAAE,EAAE;kBAAEgB,KAAK,EAAE,cAAc;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAA/B,QAAA,gBACnF9F,OAAA,CAACN,WAAW;kBAACoI,QAAQ,EAAC,OAAO;kBAACnB,EAAE,EAAE;oBAAEoB,EAAE,EAAE;kBAAI;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChD3E,YAAY,CAACkB,YAAY;cAAA;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CACjB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,gBACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,SAAS;gBACdmD,KAAK,EAAC,SAAS;gBACfjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAAC0B,OAAQ;gBACxBsE,QAAQ,EAAEvD,gBAAiB;gBAC3BzB,KAAK,EAAE,CAAC,CAACX,UAAU,CAACqB,OAAQ;gBAC5BwE,UAAU,EAAE7F,UAAU,CAACqB;cAAQ;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,EACD3E,YAAY,CAACmB,OAAO,iBACnB1C,OAAA,CAAClB,cAAc;gBAAC6H,EAAE,EAAE;kBAAEgB,KAAK,EAAE,cAAc;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAA/B,QAAA,gBACnF9F,OAAA,CAACN,WAAW;kBAACoI,QAAQ,EAAC,OAAO;kBAACnB,EAAE,EAAE;oBAAEoB,EAAE,EAAE;kBAAI;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChD3E,YAAY,CAACmB,OAAO;cAAA;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CACjB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,gBACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,IAAI;gBACTmD,KAAK,EAAC,UAAU;gBAChBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACgH,EAAE,IAAIhH,QAAQ,CAAC2B,EAAG;gBAClCqE,QAAQ,EAAEvD,gBAAiB;gBAC3BzB,KAAK,EAAE,CAAC,CAACX,UAAU,CAAC2G,EAAG;gBACvBd,UAAU,EAAE7F,UAAU,CAAC2G,EAAE,IAAI;cAAiB;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,EACD3E,YAAY,CAACyG,EAAE,iBACdhI,OAAA,CAAClB,cAAc;gBAAC6H,EAAE,EAAE;kBAAEgB,KAAK,EAAE,cAAc;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAA/B,QAAA,gBACnF9F,OAAA,CAACN,WAAW;kBAACoI,QAAQ,EAAC,OAAO;kBAACnB,EAAE,EAAE;oBAAEoB,EAAE,EAAE;kBAAI;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChD3E,YAAY,CAACyG,EAAE;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACjB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,qBAAqB;gBAC1BmD,KAAK,EAAC,qBAAqB;gBAC3BjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAAC4B,mBAAoB;gBACpCoE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,iBAAiB;gBACtBmD,KAAK,EAAC,iBAAiB;gBACvBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAAC6B,eAAgB;gBAChCmE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,6BAA6B;gBAClCmD,KAAK,EAAC,6BAA6B;gBACnCjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAAC8B,2BAA4B;gBAC5CkE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,mBAAmB;gBACxBmD,KAAK,EAAC,mBAAmB;gBACzBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAAC+B,iBAAkB;gBAClCiE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,eAAe;gBACpBmD,KAAK,EAAC,eAAe;gBACrBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACgC,aAAc;gBAC9BgE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,2BAA2B;gBAChCmD,KAAK,EAAC,2BAA2B;gBACjCjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACiC,yBAA0B;gBAC1C+D,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,gBACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,eAAe;gBACpBmD,KAAK,EAAC,eAAe;gBACrBC,IAAI,EAAC,QAAQ;gBACblB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACkC,aAAc;gBAC9B8D,QAAQ,EAAEvD,gBAAiB;gBAC3BwD,QAAQ;gBACRjF,KAAK,EAAE,CAAC,CAACX,UAAU,CAAC6B,aAAc;gBAClCgE,UAAU,EAAE7F,UAAU,CAAC6B;cAAc;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACD3E,YAAY,CAAC2B,aAAa,iBACzBlD,OAAA,CAAClB,cAAc;gBAAC6H,EAAE,EAAE;kBAAEgB,KAAK,EAAE,cAAc;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAA/B,QAAA,gBACnF9F,OAAA,CAACN,WAAW;kBAACoI,QAAQ,EAAC,OAAO;kBAACnB,EAAE,EAAE;oBAAEoB,EAAE,EAAE;kBAAI;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAChD3E,YAAY,CAAC2B,aAAa;cAAA;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACjB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,WAAW;gBAChBmD,KAAK,EAAC,WAAW;gBACjBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACI,SAAU;gBAC1B4F,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAACzB,WAAW;gBAACsH,SAAS;gBAAAC,QAAA,gBACpB9F,OAAA,CAACxB,UAAU;kBAAAsH,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5ClG,OAAA,CAACvB,MAAM;kBACLkF,IAAI,EAAC,qBAAqB;kBAC1BC,KAAK,EAAE5C,QAAQ,CAACqC,mBAAoB;kBACpCyD,KAAK,EAAC,qBAAqB;kBAC3BE,QAAQ,EAAEvD,gBAAiB;kBAAAqC,QAAA,gBAE3B9F,OAAA,CAACtB,QAAQ;oBAACkF,KAAK,EAAC,WAAW;oBAAAkC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChDlG,OAAA,CAACtB,QAAQ;oBAACkF,KAAK,EAAC,SAAS;oBAAAkC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5ClG,OAAA,CAACtB,QAAQ;oBAACkF,KAAK,EAAC,QAAQ;oBAAAkC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1ClG,OAAA,CAACtB,QAAQ;oBAACkF,KAAK,EAAC,OAAO;oBAAAkC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAChBlG,OAAA,CAAC3B,aAAa;UAAAyH,QAAA,gBACZ9F,OAAA,CAACtC,MAAM;YAAC8I,OAAO,EAAElD,iBAAkB;YAAAwC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDlG,OAAA,CAACtC,MAAM;YACL8I,OAAO,EAAElC,UAAW;YACpB6C,QAAQ,EAAE7G,OAAO,IAAI,CAACU,QAAQ,CAACE,OAAQ;YACvCkG,SAAS,EAAE9G,OAAO,gBAAGN,OAAA,CAACnB,gBAAgB;cAACwI,IAAI,EAAE;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlG,OAAA,CAACR,QAAQ;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAItF,UAAU,KAAK,eAAe,IAAIA,UAAU,KAAK,aAAa,EAAE;MACzE,oBACEZ,OAAA,CAAC9B,MAAM;QAACwH,IAAI,EAAEhF,UAAW;QAACiF,OAAO,EAAErC,iBAAkB;QAACsC,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E9F,OAAA,CAAC7B,WAAW;UAAA2H,QAAA,EACTlF,UAAU,KAAK,eAAe,GAAG,8BAA8B,GAAG;QAA6B;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC,eACdlG,OAAA,CAAC5B,aAAa;UAAA0H,QAAA,EACXnE,WAAW,gBACV3B,OAAA,CAACnB,gBAAgB;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBzE,IAAI,CAAC0D,MAAM,KAAK,CAAC,gBACnBnF,OAAA,CAACpB,KAAK;YAACuH,QAAQ,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEtDlG,OAAA,CAACnC,IAAI;YAAAiI,QAAA,EACFrE,IAAI,CAAC6E,GAAG,CAAE9C,IAAI,iBACbxD,OAAA,CAAClC,QAAQ;cACPyI,MAAM;cAENC,OAAO,EAAEA,CAAA,KAAMjD,gBAAgB,CAACC,IAAI,CAAE;cAAAsC,QAAA,eAEtC9F,OAAA,CAACjC,YAAY;gBACX0I,OAAO,EAAEjD,IAAI,CAACtC,OAAQ;gBACtBwF,SAAS,EAAE,GAAGlD,IAAI,CAAChB,SAAS,IAAI,KAAK,UAAUgB,IAAI,CAACZ,mBAAmB,IAAI,KAAK,OAAOY,IAAI,CAACT,iBAAiB,IAAI,KAAK;cAAG;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1H;YAAC,GANG1C,IAAI,CAACtC,OAAO;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOT,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBlG,OAAA,CAAC3B,aAAa;UAAAyH,QAAA,gBACZ9F,OAAA,CAACtC,MAAM;YAAC8I,OAAO,EAAElD,iBAAkB;YAAAwC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDtF,UAAU,KAAK,aAAa,IAAIE,YAAY,iBAC3Cd,OAAA,CAACtC,MAAM;YACL8I,OAAO,EAAElC,UAAW;YACpB6C,QAAQ,EAAE7G,OAAQ;YAClBqH,KAAK,EAAC,OAAO;YACbP,SAAS,EAAE9G,OAAO,gBAAGN,OAAA,CAACnB,gBAAgB;cAACwI,IAAI,EAAE;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlG,OAAA,CAACZ,UAAU;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACtE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAItF,UAAU,KAAK,cAAc,EAAE;MACxC,oBACEZ,OAAA,CAAC9B,MAAM;QAACwH,IAAI,EAAEhF,UAAW;QAACiF,OAAO,EAAErC,iBAAkB;QAACsC,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E9F,OAAA,CAAC7B,WAAW;UAAA2H,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxClG,OAAA,CAAC5B,aAAa;UAAA0H,QAAA,eACZ9F,OAAA,CAACrB,IAAI;YAAC2I,SAAS;YAACC,OAAO,EAAE,CAAE;YAACZ,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAd,QAAA,gBACxC9F,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,SAAS;gBACdmD,KAAK,EAAC,SAAS;gBACfjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACE,OAAQ;gBACxBiG,QAAQ;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,qBAAqB;gBAC1BmD,KAAK,EAAC,qBAAqB;gBAC3BjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACoB,mBAAoB;gBACpC4E,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,SAAS;gBACdmD,KAAK,EAAC,SAAS;gBACfjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACqB,OAAQ;gBACxB2E,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,SAAS;gBACdmD,KAAK,EAAC,SAAS;gBACfjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACsB,OAAQ;gBACxB0E,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,aAAa;gBAClBmD,KAAK,EAAC,aAAa;gBACnBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACuB,WAAY;gBAC5ByE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,WAAW;gBAChBmD,KAAK,EAAC,WAAW;gBACjBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACwB,SAAU;gBAC1BwE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,cAAc;gBACnBmD,KAAK,EAAC,mBAAmB;gBACzBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACyB,YAAa;gBAC7BuE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,SAAS;gBACdmD,KAAK,EAAC,SAAS;gBACfjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAAC0B,OAAQ;gBACxBsE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,IAAI;gBACTmD,KAAK,EAAC,IAAI;gBACVjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAAC2B,EAAG;gBACnBqE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,qBAAqB;gBAC1BmD,KAAK,EAAC,qBAAqB;gBAC3BjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAAC4B,mBAAoB;gBACpCoE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,iBAAiB;gBACtBmD,KAAK,EAAC,iBAAiB;gBACvBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAAC6B,eAAgB;gBAChCmE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,6BAA6B;gBAClCmD,KAAK,EAAC,6BAA6B;gBACnCjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAAC8B,2BAA4B;gBAC5CkE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,mBAAmB;gBACxBmD,KAAK,EAAC,mBAAmB;gBACzBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAAC+B,iBAAkB;gBAClCiE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,eAAe;gBACpBmD,KAAK,EAAC,eAAe;gBACrBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACgC,aAAc;gBAC9BgE,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,2BAA2B;gBAChCmD,KAAK,EAAC,2BAA2B;gBACjCjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACiC,yBAA0B;gBAC1C+D,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,eAAe;gBACpBmD,KAAK,EAAC,eAAe;gBACrBC,IAAI,EAAC,QAAQ;gBACblB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACkC,aAAc;gBAC9B8D,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAAC1B,SAAS;gBACRqF,IAAI,EAAC,WAAW;gBAChBmD,KAAK,EAAC,WAAW;gBACjBjB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClBxC,KAAK,EAAE5C,QAAQ,CAACI,SAAU;gBAC1B4F,QAAQ,EAAEvD;cAAiB;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAACrB,IAAI;cAAC6I,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5B,QAAA,eACvB9F,OAAA,CAACzB,WAAW;gBAACsH,SAAS;gBAAAC,QAAA,gBACpB9F,OAAA,CAACxB,UAAU;kBAAAsH,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5ClG,OAAA,CAACvB,MAAM;kBACLkF,IAAI,EAAC,qBAAqB;kBAC1BC,KAAK,EAAE5C,QAAQ,CAACqC,mBAAoB;kBACpCyD,KAAK,EAAC,qBAAqB;kBAC3BE,QAAQ,EAAEvD,gBAAiB;kBAAAqC,QAAA,gBAE3B9F,OAAA,CAACtB,QAAQ;oBAACkF,KAAK,EAAC,WAAW;oBAAAkC,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChDlG,OAAA,CAACtB,QAAQ;oBAACkF,KAAK,EAAC,SAAS;oBAAAkC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5ClG,OAAA,CAACtB,QAAQ;oBAACkF,KAAK,EAAC,QAAQ;oBAAAkC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1ClG,OAAA,CAACtB,QAAQ;oBAACkF,KAAK,EAAC,OAAO;oBAAAkC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAChBlG,OAAA,CAAC3B,aAAa;UAAAyH,QAAA,gBACZ9F,OAAA,CAACtC,MAAM;YAAC8I,OAAO,EAAElD,iBAAkB;YAAAwC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDlG,OAAA,CAACtC,MAAM;YACL8I,OAAO,EAAElC,UAAW;YACpB6C,QAAQ,EAAE7G,OAAQ;YAClB8G,SAAS,EAAE9G,OAAO,gBAAGN,OAAA,CAACnB,gBAAgB;cAACwI,IAAI,EAAE;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGlG,OAAA,CAACR,QAAQ;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACElG,OAAA,CAACxC,GAAG;IAACmJ,EAAE,EAAE;MAAEiB,OAAO,EAAE;IAAO,CAAE;IAAA9B,QAAA,gBAE3B9F,OAAA,CAACxC,GAAG;MAACmJ,EAAE,EAAE;QAAEsB,KAAK,EAAE,OAAO;QAAEF,EAAE,EAAE;MAAE,CAAE;MAAAjC,QAAA,eACjC9F,OAAA,CAACrC,KAAK;QAACgJ,EAAE,EAAE;UAAEuB,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAArC,QAAA,gBACzB9F,OAAA,CAACvC,UAAU;UAAC2I,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAP,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblG,OAAA,CAACpC,OAAO;UAAC+I,EAAE,EAAE;YAAEwB,EAAE,EAAE;UAAE;QAAE;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BlG,OAAA,CAACnC,IAAI;UAACuK,SAAS,EAAC,KAAK;UAACC,KAAK;UAAAvC,QAAA,gBACzB9F,OAAA,CAAC/B,cAAc;YAACuI,OAAO,EAAEA,CAAA,KAAMtE,kBAAkB,CAAC,gBAAgB,CAAE;YAAA4D,QAAA,gBAClE9F,OAAA,CAAChC,YAAY;cAAA8H,QAAA,eACX9F,OAAA,CAACV,SAAS;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACflG,OAAA,CAACjC,YAAY;cAAC0I,OAAO,EAAC;YAA2B;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAEjBlG,OAAA,CAAC/B,cAAc;YAACuI,OAAO,EAAEA,CAAA,KAAMtE,kBAAkB,CAAC,cAAc,CAAE;YAAA4D,QAAA,gBAChE9F,OAAA,CAAChC,YAAY;cAAA8H,QAAA,eACX9F,OAAA,CAACd,QAAQ;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACflG,OAAA,CAACjC,YAAY;cAAC0I,OAAO,EAAC;YAAkB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAEjBlG,OAAA,CAAC/B,cAAc;YAACuI,OAAO,EAAEA,CAAA,KAAMtE,kBAAkB,CAAC,cAAc,CAAE;YAAA4D,QAAA,gBAChE9F,OAAA,CAAChC,YAAY;cAAA8H,QAAA,eACX9F,OAAA,CAAChB,OAAO;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACflG,OAAA,CAACjC,YAAY;cAAC0I,OAAO,EAAC;YAAwB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAEjBlG,OAAA,CAAC/B,cAAc;YAACuI,OAAO,EAAEA,CAAA,KAAMtE,kBAAkB,CAAC,aAAa,CAAE;YAAA4D,QAAA,gBAC/D9F,OAAA,CAAChC,YAAY;cAAA8H,QAAA,eACX9F,OAAA,CAACZ,UAAU;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACflG,OAAA,CAACjC,YAAY;cAAC0I,OAAO,EAAC;YAAiB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eAEjBlG,OAAA,CAAC/B,cAAc;YAACuI,OAAO,EAAEA,CAAA,KAAMtE,kBAAkB,CAAC,gBAAgB,CAAE;YAAA4D,QAAA,gBAClE9F,OAAA,CAAChC,YAAY;cAAA8H,QAAA,eACX9F,OAAA,CAACd,QAAQ;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACflG,OAAA,CAACjC,YAAY;cAAC0I,OAAO,EAAC;YAAgC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEjBlG,OAAA,CAAC/B,cAAc;YAACuI,OAAO,EAAEA,CAAA,KAAMtE,kBAAkB,CAAC,kBAAkB,CAAE;YAAA4D,QAAA,gBACpE9F,OAAA,CAAChC,YAAY;cAAA8H,QAAA,eACX9F,OAAA,CAACV,SAAS;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACflG,OAAA,CAACjC,YAAY;cAAC0I,OAAO,EAAC;YAA+B;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNlG,OAAA,CAACxC,GAAG;MAACmJ,EAAE,EAAE;QAAE2B,QAAQ,EAAE;MAAE,CAAE;MAAAxC,QAAA,eACvB9F,OAAA,CAACrC,KAAK;QAACgJ,EAAE,EAAE;UAAEuB,CAAC,EAAE,CAAC;UAAEK,SAAS,EAAE,OAAO;UAAEX,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEW,cAAc,EAAE;QAAS,CAAE;QAAA1C,QAAA,EACtGtF,cAAc,gBACbR,OAAA,CAACvC,UAAU;UAAC2I,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAE5B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,gBAEblG,OAAA,CAACvC,UAAU;UAAC2I,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAE5B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELT,YAAY,CAAC,CAAC;EAAA;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC7F,EAAA,CA97BIJ,oBAAoB;AAAAwI,EAAA,GAApBxI,oBAAoB;AAg8B1B,eAAeA,oBAAoB;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}