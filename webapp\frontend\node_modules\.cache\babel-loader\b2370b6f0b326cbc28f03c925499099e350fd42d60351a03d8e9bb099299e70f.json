{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.61 5.64 5.36 8.04 2.35 8.36 0 10.9 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96M19 18H6.17c-2.09 0-3.95-1.53-4.15-3.61C1.79 12.01 3.66 10 6 10c1.92 0 3.53 1.36 3.91 3.17.1.48.5.83.98.83.61 0 1.11-.55.99-1.15-.43-2.24-2.11-4.03-4.29-4.63 1.1-1.46 2.89-2.37 4.89-2.2 2.88.25 5.01 2.82 5.01 5.71V12h1.37c1.45 0 2.79.97 3.07 2.4.39 1.91-1.08 3.6-2.93 3.6\"\n}), 'FilterDramaRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/icons-material/esm/FilterDramaRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.61 5.64 5.36 8.04 2.35 8.36 0 10.9 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96M19 18H6.17c-2.09 0-3.95-1.53-4.15-3.61C1.79 12.01 3.66 10 6 10c1.92 0 3.53 1.36 3.91 3.17.1.48.5.83.98.83.61 0 1.11-.55.99-1.15-.43-2.24-2.11-4.03-4.29-4.63 1.1-1.46 2.89-2.37 4.89-2.2 2.88.25 5.01 2.82 5.01 5.71V12h1.37c1.45 0 2.79.97 3.07 2.4.39 1.91-1.08 3.6-2.93 3.6\"\n}), 'FilterDramaRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}