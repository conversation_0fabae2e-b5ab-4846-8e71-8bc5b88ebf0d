{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Grid, Card, CardContent, CardActions, Button, Chip, Alert, CircularProgress, Divider, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';\nimport { Assessment as AssessmentIcon, BarChart as BarChartIcon, PieChart as PieChartIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon, ArrowBack as ArrowBackIcon, DateRange as DateRangeIcon, Cable as CableIcon, Inventory as InventoryIcon, ExpandMore as ExpandMoreIcon } from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    cantiereId\n  } = useParams();\n  const {\n    user\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // Configurazione dei report disponibili\n  const reportTypes = [{\n    id: 'progress',\n    title: 'Report Avanzamento',\n    description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n    icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 13\n    }, this),\n    color: 'primary',\n    features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n  }, {\n    id: 'boq',\n    title: 'Bill of Quantities',\n    description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n    icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 13\n    }, this),\n    color: 'secondary',\n    features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n  }, {\n    id: 'bobine',\n    title: 'Report Utilizzo Bobine',\n    description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n    icon: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 13\n    }, this),\n    color: 'success',\n    features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n  }, {\n    id: 'bobina-specifica',\n    title: 'Report Bobina Specifica',\n    description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n    icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this),\n    color: 'info',\n    features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n  }, {\n    id: 'posa-periodo',\n    title: 'Report Posa per Periodo',\n    description: 'Analisi temporale della posa con trend e pattern di lavoro',\n    icon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this),\n    color: 'warning',\n    features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n  }, {\n    id: 'cavi-stato',\n    title: 'Report Cavi per Stato',\n    description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n    icon: /*#__PURE__*/_jsxDEV(BarChartIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this),\n    color: 'error',\n    features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n  }];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReportSelect = reportType => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderReportContent = () => {\n    if (!reportData) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title, \" - \", reportData.nome_cantiere]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'pdf'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"primary\",\n            children: \"PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'excel'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"success\",\n            children: \"Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 26\n            }, this),\n            onClick: () => setReportData(null),\n            variant: \"outlined\",\n            size: \"small\",\n            children: \"Nuovo Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), dialogType === 'progress' && renderProgressReport(), dialogType === 'boq' && renderBoqReport(), dialogType === 'bobine' && renderBobineReport(), dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(), dialogType === 'posa-periodo' && renderPosaPeriodoReport(), dialogType === 'cavi-stato' && renderCaviStatoReport()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this);\n  };\n  const renderProgressReport = () => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Avanzamento Generale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Metri Totali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [reportData.metri_totali, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Metri Posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [reportData.metri_posati, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Metri Rimanenti: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [reportData.metri_da_posare, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 42\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Avanzamento: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [reportData.percentuale_avanzamento, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Totale Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: reportData.totale_cavi\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Cavi Posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: reportData.cavi_posati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Cavi Rimanenti: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: reportData.cavi_rimanenti\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Percentuale Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [reportData.percentuale_cavi, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 43\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Media Giornaliera: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [reportData.media_giornaliera, \"m/giorno\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 44\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), reportData.giorni_stimati && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Giorni Stimati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [reportData.giorni_stimati, \" giorni\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Data Completamento: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: reportData.data_completamento\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 49\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this), reportData.posa_recente && reportData.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Posa Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this), reportData.posa_recente.slice(0, 5).map((posa, index) => /*#__PURE__*/_jsxDEV(Typography, {\n            children: [posa.data, \": \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [posa.metri, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 32\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 276,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = () => {\n    var _reportData$cavi_per_, _reportData$bobine_pe;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Cavi per Tipologia\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: (_reportData$cavi_per_ = reportData.cavi_per_tipo) === null || _reportData$cavi_per_ === void 0 ? void 0 : _reportData$cavi_per_.map((cavo, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  children: cavo.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"Sezione: \", cavo.sezione]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Cavi: \", cavo.num_cavi]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Teorici: \", cavo.metri_teorici, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Reali: \", cavo.metri_reali, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Da Posare: \", cavo.metri_da_posare, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Bobine Disponibili\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: (_reportData$bobine_pe = reportData.bobine_per_tipo) === null || _reportData$bobine_pe === void 0 ? void 0 : _reportData$bobine_pe.map((bobina, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  children: bobina.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"Sezione: \", bobina.sezione]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Bobine: \", bobina.num_bobine]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Disponibili: \", bobina.metri_disponibili, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 5\n    }, this);\n  };\n  const renderBobineReport = () => {\n    var _reportData$bobine;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [\"Bobine del Cantiere (\", reportData.totale_bobine, \" totali)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: (_reportData$bobine = reportData.bobine) === null || _reportData$bobine === void 0 ? void 0 : _reportData$bobine.map((bobina, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  children: bobina.id_bobina\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [bobina.tipologia, \" - \", bobina.sezione]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: bobina.stato,\n                  color: bobina.stato === 'DISPONIBILE' ? 'success' : 'warning',\n                  size: \"small\",\n                  sx: {\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Totali: \", bobina.metri_totali, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Residui: \", bobina.metri_residui, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Utilizzati: \", bobina.metri_utilizzati, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Utilizzo: \", bobina.percentuale_utilizzo, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 5\n    }, this);\n  };\n  const renderBobinaSpecificaReport = () => {\n    var _reportData$bobina, _reportData$bobina2, _reportData$bobina3, _reportData$bobina4, _reportData$bobina5, _reportData$bobina6, _reportData$bobina7, _reportData$bobina8, _reportData$bobina9, _reportData$cavi_asso;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Dettagli Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"ID: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: (_reportData$bobina = reportData.bobina) === null || _reportData$bobina === void 0 ? void 0 : _reportData$bobina.id_bobina\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 410,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Tipologia: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: (_reportData$bobina2 = reportData.bobina) === null || _reportData$bobina2 === void 0 ? void 0 : _reportData$bobina2.tipologia\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 36\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Sezione: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: (_reportData$bobina3 = reportData.bobina) === null || _reportData$bobina3 === void 0 ? void 0 : _reportData$bobina3.sezione\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: (_reportData$bobina4 = reportData.bobina) === null || _reportData$bobina4 === void 0 ? void 0 : _reportData$bobina4.stato,\n              color: ((_reportData$bobina5 = reportData.bobina) === null || _reportData$bobina5 === void 0 ? void 0 : _reportData$bobina5.stato) === 'DISPONIBILE' ? 'success' : 'warning',\n              sx: {\n                my: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Metri Totali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [(_reportData$bobina6 = reportData.bobina) === null || _reportData$bobina6 === void 0 ? void 0 : _reportData$bobina6.metri_totali, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 39\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Metri Residui: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [(_reportData$bobina7 = reportData.bobina) === null || _reportData$bobina7 === void 0 ? void 0 : _reportData$bobina7.metri_residui, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 40\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Metri Utilizzati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [(_reportData$bobina8 = reportData.bobina) === null || _reportData$bobina8 === void 0 ? void 0 : _reportData$bobina8.metri_utilizzati, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 43\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Utilizzo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [(_reportData$bobina9 = reportData.bobina) === null || _reportData$bobina9 === void 0 ? void 0 : _reportData$bobina9.percentuale_utilizzo, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 35\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: [\"Cavi Associati (\", reportData.totale_cavi, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                maxHeight: 300,\n                overflow: 'auto'\n              },\n              children: (_reportData$cavi_asso = reportData.cavi_associati) === null || _reportData$cavi_asso === void 0 ? void 0 : _reportData$cavi_asso.map((cavo, index) => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 1,\n                  p: 1,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 47\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: [cavo.sistema, \" - \", cavo.utility, \" - \", cavo.tipologia]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  children: [\"Teorici: \", cavo.metri_teorici, \"m | Reali: \", cavo.metri_reali, \"m | Stato: \", cavo.stato]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 19\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 5\n    }, this);\n  };\n  const renderPosaPeriodoReport = () => {\n    var _reportData$posa_gior;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Statistiche Periodo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Periodo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [reportData.data_inizio, \" - \", reportData.data_fine]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Totale Metri: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [reportData.totale_metri_periodo, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 39\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Giorni Attivi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: reportData.giorni_attivi\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 40\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Media Giornaliera: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [reportData.media_giornaliera, \"m/giorno\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 44\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Posa Giornaliera\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                maxHeight: 300,\n                overflow: 'auto'\n              },\n              children: (_reportData$posa_gior = reportData.posa_giornaliera) === null || _reportData$posa_gior === void 0 ? void 0 : _reportData$posa_gior.map((posa, index) => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  py: 0.5\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: posa.data\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [posa.metri, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 31\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 19\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 5\n    }, this);\n  };\n  const renderCaviStatoReport = () => {\n    var _reportData$cavi_per_2;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Cavi per Stato di Installazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: (_reportData$cavi_per_2 = reportData.cavi_per_stato) === null || _reportData$cavi_per_2 === void 0 ? void 0 : _reportData$cavi_per_2.map((stato, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: stato.stato,\n                    color: stato.stato === 'Installato' ? 'success' : 'warning',\n                    sx: {\n                      mb: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 493,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Numero Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: stato.num_cavi\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 44\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Teorici: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [stato.metri_teorici, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 500,\n                    columnNumber: 46\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Reali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [stato.metri_reali, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 44\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 5\n    }, this);\n  };\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 513,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 11\n        }, this), dialogType === 'bobina-specifica' && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Bobina\",\n            value: formData.id_bobina,\n            onChange: e => setFormData({\n              ...formData,\n              id_bobina: e.target.value\n            }),\n            placeholder: \"Es: 1, 2, A, B...\",\n            helperText: \"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 13\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 578,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 512,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => navigate(-1),\n          color: \"primary\",\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Report e Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 596,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 604,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 595,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 3\n      },\n      children: \"Seleziona il tipo di report che desideri generare. Ogni report offre analisi specifiche per monitorare l'avanzamento del progetto e ottimizzare le operazioni.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: reportTypes.map(report => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            cursor: 'pointer',\n            transition: 'transform 0.2s, box-shadow 0.2s',\n            '&:hover': {\n              transform: 'translateY(-4px)',\n              boxShadow: 4\n            }\n          },\n          onClick: () => handleReportSelect(report),\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  color: `${report.color}.main`,\n                  mr: 2\n                },\n                children: report.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"h2\",\n                children: report.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 2\n              },\n              children: report.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: report.features.map((feature, index) => /*#__PURE__*/_jsxDEV(Chip, {\n                label: feature,\n                size: \"small\",\n                variant: \"outlined\",\n                sx: {\n                  mr: 0.5,\n                  mb: 0.5\n                }\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: report.color,\n              startIcon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 30\n              }, this),\n              fullWidth: true,\n              children: \"Genera Report\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this)\n      }, report.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 7\n    }, this), renderReportContent(), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 593,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageNew, \"HffKKuo4NWxac8n/kfIDDf02L24=\", false, function () {\n  return [useNavigate, useParams, useAuth];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Assessment", "AssessmentIcon", "<PERSON><PERSON><PERSON>", "BarChartIcon", "<PERSON><PERSON><PERSON>", "PieChartIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "ArrowBack", "ArrowBackIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "useNavigate", "useParams", "useAuth", "AdminHomeButton", "reportService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s", "navigate", "cantiereId", "user", "loading", "setLoading", "error", "setError", "reportData", "setReportData", "selectedReport", "setSelectedReport", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportTypes", "id", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "features", "generateReportWithFormat", "reportType", "format", "response", "getProgressReport", "getBillOfQuantities", "getBobineReport", "getCaviStatoReport", "getBobinaReport", "getPosaPerPeriodoReport", "Error", "content", "file_url", "window", "open", "err", "console", "detail", "message", "handleReportSelect", "today", "Date", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "handleGenerateReport", "handleCloseDialog", "renderReportContent", "sx", "p", "mt", "children", "display", "justifyContent", "alignItems", "mb", "variant", "nome_cantiere", "gap", "startIcon", "onClick", "size", "renderProgressReport", "renderBoqReport", "renderBobineReport", "renderBobinaSpecificaReport", "renderPosaPeriodoReport", "renderCaviStatoReport", "container", "spacing", "item", "xs", "md", "gutterBottom", "metri_totali", "metri_posati", "metri_da_posare", "percentuale_avanzamento", "totale_cavi", "cavi_posati", "cavi_rimanenti", "percentuale_cavi", "media_giornaliera", "giorni_stimati", "data_completamento", "posa_recente", "length", "slice", "map", "posa", "index", "data", "metri", "_reportData$cavi_per_", "_reportData$bobine_pe", "cavi_per_tipo", "cavo", "lg", "tipologia", "sezione", "num_cavi", "metri_te<PERSON>ci", "metri_reali", "bobine_per_tipo", "bobina", "num_bobine", "metri_disponibili", "_reportData$bobine", "totale_bobine", "bobine", "label", "stato", "metri_residui", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "percentuale_utilizzo", "_reportData$bobina", "_reportData$bobina2", "_reportData$bobina3", "_reportData$bobina4", "_reportData$bobina5", "_reportData$bobina6", "_reportData$bobina7", "_reportData$bobina8", "_reportData$bobina9", "_reportData$cavi_asso", "my", "maxHeight", "overflow", "cavi_associati", "border", "borderRadius", "id_cavo", "sistema", "utility", "_reportData$posa_gior", "totale_metri_periodo", "giorni_attivi", "posa_giornal<PERSON>", "py", "_reportData$cavi_per_2", "cavi_per_stato", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "value", "onChange", "e", "target", "placeholder", "helperText", "type", "InputLabelProps", "shrink", "disabled", "component", "report", "height", "flexDirection", "cursor", "transition", "transform", "boxShadow", "flexGrow", "mr", "feature", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n  IconButton,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n  Bar<PERSON>hart as BarChartIcon,\n  <PERSON>Chart as PieChartIcon,\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n  ArrowBack as ArrowBackIcon,\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon\n} from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\n\nconst ReportCaviPageNew = () => {\n  const navigate = useNavigate();\n  const { cantiereId } = useParams();\n  const { user } = useAuth();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // Configurazione dei report disponibili\n  const reportTypes = [\n    {\n      id: 'progress',\n      title: 'Report Avanzamento',\n      description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n      icon: <AssessmentIcon />,\n      color: 'primary',\n      features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n    },\n    {\n      id: 'boq',\n      title: 'Bill of Quantities',\n      description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n      icon: <ListIcon />,\n      color: 'secondary',\n      features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n    },\n    {\n      id: 'bobine',\n      title: 'Report Utilizzo Bobine',\n      description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n      icon: <InventoryIcon />,\n      color: 'success',\n      features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n    },\n    {\n      id: 'bobina-specifica',\n      title: 'Report Bobina Specifica',\n      description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n      icon: <CableIcon />,\n      color: 'info',\n      features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n    },\n    {\n      id: 'posa-periodo',\n      title: 'Report Posa per Periodo',\n      description: 'Analisi temporale della posa con trend e pattern di lavoro',\n      icon: <TimelineIcon />,\n      color: 'warning',\n      features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n    },\n    {\n      id: 'cavi-stato',\n      title: 'Report Cavi per Stato',\n      description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n      icon: <BarChartIcon />,\n      color: 'error',\n      features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n    }\n  ];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReportSelect = (reportType) => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n  const renderReportContent = () => {\n    if (!reportData) return null;\n\n    return (\n      <Paper sx={{ p: 3, mt: 3 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\">\n            {selectedReport?.title} - {reportData.nome_cantiere}\n          </Typography>\n          <Box sx={{ display: 'flex', gap: 1 }}>\n            {/* Export buttons */}\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'pdf')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"primary\"\n            >\n              PDF\n            </Button>\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'excel')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"success\"\n            >\n              Excel\n            </Button>\n            <Button\n              startIcon={<RefreshIcon />}\n              onClick={() => setReportData(null)}\n              variant=\"outlined\"\n              size=\"small\"\n            >\n              Nuovo Report\n            </Button>\n          </Box>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Renderizza il contenuto specifico del report */}\n        {dialogType === 'progress' && renderProgressReport()}\n        {dialogType === 'boq' && renderBoqReport()}\n        {dialogType === 'bobine' && renderBobineReport()}\n        {dialogType === 'bobina-specifica' && renderBobinaSpecificaReport()}\n        {dialogType === 'posa-periodo' && renderPosaPeriodoReport()}\n        {dialogType === 'cavi-stato' && renderCaviStatoReport()}\n      </Paper>\n    );\n  };\n\n  const renderProgressReport = () => (\n    <Grid container spacing={3}>\n      <Grid item xs={12} md={6}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>Avanzamento Generale</Typography>\n            <Typography>Metri Totali: <strong>{reportData.metri_totali}m</strong></Typography>\n            <Typography>Metri Posati: <strong>{reportData.metri_posati}m</strong></Typography>\n            <Typography>Metri Rimanenti: <strong>{reportData.metri_da_posare}m</strong></Typography>\n            <Typography>Avanzamento: <strong>{reportData.percentuale_avanzamento}%</strong></Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n      <Grid item xs={12} md={6}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>Cavi</Typography>\n            <Typography>Totale Cavi: <strong>{reportData.totale_cavi}</strong></Typography>\n            <Typography>Cavi Posati: <strong>{reportData.cavi_posati}</strong></Typography>\n            <Typography>Cavi Rimanenti: <strong>{reportData.cavi_rimanenti}</strong></Typography>\n            <Typography>Percentuale Cavi: <strong>{reportData.percentuale_cavi}%</strong></Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n      <Grid item xs={12} md={6}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>Performance</Typography>\n            <Typography>Media Giornaliera: <strong>{reportData.media_giornaliera}m/giorno</strong></Typography>\n            {reportData.giorni_stimati && (\n              <>\n                <Typography>Giorni Stimati: <strong>{reportData.giorni_stimati} giorni</strong></Typography>\n                <Typography>Data Completamento: <strong>{reportData.data_completamento}</strong></Typography>\n              </>\n            )}\n          </CardContent>\n        </Card>\n      </Grid>\n      {reportData.posa_recente && reportData.posa_recente.length > 0 && (\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>Posa Recente</Typography>\n              {reportData.posa_recente.slice(0, 5).map((posa, index) => (\n                <Typography key={index}>\n                  {posa.data}: <strong>{posa.metri}m</strong>\n                </Typography>\n              ))}\n            </CardContent>\n          </Card>\n        </Grid>\n      )}\n    </Grid>\n  );\n\n  const renderBoqReport = () => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Typography variant=\"h6\" gutterBottom>Cavi per Tipologia</Typography>\n        <Grid container spacing={2}>\n          {reportData.cavi_per_tipo?.map((cavo, index) => (\n            <Grid item xs={12} md={6} lg={4} key={index}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"subtitle1\">{cavo.tipologia}</Typography>\n                  <Typography variant=\"body2\">Sezione: {cavo.sezione}</Typography>\n                  <Typography>Cavi: {cavo.num_cavi}</Typography>\n                  <Typography>Metri Teorici: {cavo.metri_teorici}m</Typography>\n                  <Typography>Metri Reali: {cavo.metri_reali}m</Typography>\n                  <Typography>Da Posare: {cavo.metri_da_posare}m</Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Typography variant=\"h6\" gutterBottom>Bobine Disponibili</Typography>\n        <Grid container spacing={2}>\n          {reportData.bobine_per_tipo?.map((bobina, index) => (\n            <Grid item xs={12} md={6} lg={4} key={index}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"subtitle1\">{bobina.tipologia}</Typography>\n                  <Typography variant=\"body2\">Sezione: {bobina.sezione}</Typography>\n                  <Typography>Bobine: {bobina.num_bobine}</Typography>\n                  <Typography>Metri Disponibili: {bobina.metri_disponibili}m</Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobineReport = () => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Typography variant=\"h6\" gutterBottom>\n          Bobine del Cantiere ({reportData.totale_bobine} totali)\n        </Typography>\n        <Grid container spacing={2}>\n          {reportData.bobine?.map((bobina, index) => (\n            <Grid item xs={12} md={6} lg={4} key={index}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"subtitle1\">{bobina.id_bobina}</Typography>\n                  <Typography variant=\"body2\">{bobina.tipologia} - {bobina.sezione}</Typography>\n                  <Chip\n                    label={bobina.stato}\n                    color={bobina.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n                    size=\"small\"\n                    sx={{ mb: 1 }}\n                  />\n                  <Typography>Metri Totali: {bobina.metri_totali}m</Typography>\n                  <Typography>Metri Residui: {bobina.metri_residui}m</Typography>\n                  <Typography>Metri Utilizzati: {bobina.metri_utilizzati}m</Typography>\n                  <Typography>Utilizzo: {bobina.percentuale_utilizzo}%</Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobinaSpecificaReport = () => (\n    <Grid container spacing={3}>\n      <Grid item xs={12} md={6}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>Dettagli Bobina</Typography>\n            <Typography>ID: <strong>{reportData.bobina?.id_bobina}</strong></Typography>\n            <Typography>Tipologia: <strong>{reportData.bobina?.tipologia}</strong></Typography>\n            <Typography>Sezione: <strong>{reportData.bobina?.sezione}</strong></Typography>\n            <Chip\n              label={reportData.bobina?.stato}\n              color={reportData.bobina?.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n              sx={{ my: 1 }}\n            />\n            <Typography>Metri Totali: <strong>{reportData.bobina?.metri_totali}m</strong></Typography>\n            <Typography>Metri Residui: <strong>{reportData.bobina?.metri_residui}m</strong></Typography>\n            <Typography>Metri Utilizzati: <strong>{reportData.bobina?.metri_utilizzati}m</strong></Typography>\n            <Typography>Utilizzo: <strong>{reportData.bobina?.percentuale_utilizzo}%</strong></Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={6}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Cavi Associati ({reportData.totale_cavi})\n            </Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              {reportData.cavi_associati?.map((cavo, index) => (\n                <Box key={index} sx={{ mb: 1, p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"body2\"><strong>{cavo.id_cavo}</strong></Typography>\n                  <Typography variant=\"caption\">\n                    {cavo.sistema} - {cavo.utility} - {cavo.tipologia}\n                  </Typography>\n                  <Typography variant=\"caption\" display=\"block\">\n                    Teorici: {cavo.metri_teorici}m | Reali: {cavo.metri_reali}m | Stato: {cavo.stato}\n                  </Typography>\n                </Box>\n              ))}\n            </Box>\n          </CardContent>\n        </Card>\n      </Grid>\n    </Grid>\n  );\n\n  const renderPosaPeriodoReport = () => (\n    <Grid container spacing={3}>\n      <Grid item xs={12} md={4}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>Statistiche Periodo</Typography>\n            <Typography>Periodo: <strong>{reportData.data_inizio} - {reportData.data_fine}</strong></Typography>\n            <Typography>Totale Metri: <strong>{reportData.totale_metri_periodo}m</strong></Typography>\n            <Typography>Giorni Attivi: <strong>{reportData.giorni_attivi}</strong></Typography>\n            <Typography>Media Giornaliera: <strong>{reportData.media_giornaliera}m/giorno</strong></Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={8}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>Posa Giornaliera</Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              {reportData.posa_giornaliera?.map((posa, index) => (\n                <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>\n                  <Typography>{posa.data}</Typography>\n                  <Typography><strong>{posa.metri}m</strong></Typography>\n                </Box>\n              ))}\n            </Box>\n          </CardContent>\n        </Card>\n      </Grid>\n    </Grid>\n  );\n\n  const renderCaviStatoReport = () => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Typography variant=\"h6\" gutterBottom>Cavi per Stato di Installazione</Typography>\n        <Grid container spacing={2}>\n          {reportData.cavi_per_stato?.map((stato, index) => (\n            <Grid item xs={12} md={6} lg={3} key={index}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    <Chip\n                      label={stato.stato}\n                      color={stato.stato === 'Installato' ? 'success' : 'warning'}\n                      sx={{ mb: 1 }}\n                    />\n                  </Typography>\n                  <Typography>Numero Cavi: <strong>{stato.num_cavi}</strong></Typography>\n                  <Typography>Metri Teorici: <strong>{stato.metri_teorici}m</strong></Typography>\n                  <Typography>Metri Reali: <strong>{stato.metri_reali}m</strong></Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Grid>\n    </Grid>\n  );\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {selectedReport?.title}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n          {dialogType === 'bobina-specifica' && (\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"ID Bobina\"\n                value={formData.id_bobina}\n                onChange={(e) => setFormData({ ...formData, id_bobina: e.target.value })}\n                placeholder=\"Es: 1, 2, A, B...\"\n                helperText=\"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n              />\n            </Grid>\n          )}\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <IconButton onClick={() => navigate(-1)} color=\"primary\">\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\" component=\"h1\">\n            Report e Analytics\n          </Typography>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Descrizione */}\n      <Alert severity=\"info\" sx={{ mb: 3 }}>\n        Seleziona il tipo di report che desideri generare. Ogni report offre analisi specifiche\n        per monitorare l'avanzamento del progetto e ottimizzare le operazioni.\n      </Alert>\n\n      {/* Griglia dei report */}\n      <Grid container spacing={3}>\n        {reportTypes.map((report) => (\n          <Grid item xs={12} md={6} lg={4} key={report.id}>\n            <Card\n              sx={{\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                cursor: 'pointer',\n                transition: 'transform 0.2s, box-shadow 0.2s',\n                '&:hover': {\n                  transform: 'translateY(-4px)',\n                  boxShadow: 4\n                }\n              }}\n              onClick={() => handleReportSelect(report)}\n            >\n              <CardContent sx={{ flexGrow: 1 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                  <Box sx={{ color: `${report.color}.main`, mr: 2 }}>\n                    {report.icon}\n                  </Box>\n                  <Typography variant=\"h6\" component=\"h2\">\n                    {report.title}\n                  </Typography>\n                </Box>\n\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                  {report.description}\n                </Typography>\n\n                <Box>\n                  {report.features.map((feature, index) => (\n                    <Chip\n                      key={index}\n                      label={feature}\n                      size=\"small\"\n                      variant=\"outlined\"\n                      sx={{ mr: 0.5, mb: 0.5 }}\n                    />\n                  ))}\n                </Box>\n              </CardContent>\n\n              <CardActions>\n                <Button\n                  size=\"small\"\n                  color={report.color}\n                  startIcon={<AssessmentIcon />}\n                  fullWidth\n                >\n                  Genera Report\n                </Button>\n              </CardActions>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n\n      {/* Contenuto del report */}\n      {renderReportContent()}\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY;EAAW,CAAC,GAAGX,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEY;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAE1B,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsE,KAAK,EAAEC,QAAQ,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwE,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC0E,cAAc,EAAEC,iBAAiB,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4E,UAAU,EAAEC,aAAa,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8E,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgF,QAAQ,EAAEC,WAAW,CAAC,GAAGjF,QAAQ,CAAC;IACvCkF,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAMC,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,2FAA2F;IACxGC,IAAI,eAAE9B,OAAA,CAAC/B,cAAc;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,yBAAyB;EACrH,CAAC,EACD;IACET,EAAE,EAAE,KAAK;IACTC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,wEAAwE;IACrFC,IAAI,eAAE9B,OAAA,CAACvB,QAAQ;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,0BAA0B,EAAE,qBAAqB,EAAE,eAAe;EAC1G,CAAC,EACD;IACET,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,uEAAuE;IACpFC,IAAI,eAAE9B,OAAA,CAACT,aAAa;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,iBAAiB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,yEAAyE;IACtFC,IAAI,eAAE9B,OAAA,CAACX,SAAS;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,oBAAoB;EAC7F,CAAC,EACD;IACET,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,eAAE9B,OAAA,CAACzB,YAAY;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,mBAAmB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,iFAAiF;IAC9FC,IAAI,eAAE9B,OAAA,CAAC7B,YAAY;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,2BAA2B,EAAE,eAAe,EAAE,kBAAkB;EAC/F,CAAC,CACF;;EAED;EACA,MAAMC,wBAAwB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,MAAM,KAAK;IAC7D,IAAI;MACF9B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI6B,QAAQ;MAEZ,QAAQF,UAAU;QAChB,KAAK,UAAU;UACbE,QAAQ,GAAG,MAAM1C,aAAa,CAAC2C,iBAAiB,CAACnC,UAAU,EAAEiC,MAAM,CAAC;UACpE;QACF,KAAK,KAAK;UACRC,QAAQ,GAAG,MAAM1C,aAAa,CAAC4C,mBAAmB,CAACpC,UAAU,EAAEiC,MAAM,CAAC;UACtE;QACF,KAAK,QAAQ;UACXC,QAAQ,GAAG,MAAM1C,aAAa,CAAC6C,eAAe,CAACrC,UAAU,EAAEiC,MAAM,CAAC;UAClE;QACF,KAAK,YAAY;UACfC,QAAQ,GAAG,MAAM1C,aAAa,CAAC8C,kBAAkB,CAACtC,UAAU,EAAEiC,MAAM,CAAC;UACrE;QACF,KAAK,kBAAkB;UACrB,IAAI,CAACnB,QAAQ,CAACK,SAAS,EAAE;YACvBd,QAAQ,CAAC,8BAA8B,CAAC;YACxC;UACF;UACA6B,QAAQ,GAAG,MAAM1C,aAAa,CAAC+C,eAAe,CAACvC,UAAU,EAAEc,QAAQ,CAACK,SAAS,EAAEc,MAAM,CAAC;UACtF;QACF,KAAK,cAAc;UACjB,IAAI,CAACnB,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDb,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACA6B,QAAQ,GAAG,MAAM1C,aAAa,CAACgD,uBAAuB,CACpDxC,UAAU,EACVc,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClBe,MACF,CAAC;UACD;QACF;UACE,MAAM,IAAIQ,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIR,MAAM,KAAK,OAAO,EAAE;QACtB1B,aAAa,CAAC2B,QAAQ,CAACQ,OAAO,CAAC;MACjC,CAAC,MAAM;QACL;QACA,IAAIR,QAAQ,CAACS,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAACX,QAAQ,CAACS,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;IACF,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAAC3C,KAAK,CAAC,sCAAsC,EAAE0C,GAAG,CAAC;MAC1DzC,QAAQ,CAACyC,GAAG,CAACE,MAAM,IAAIF,GAAG,CAACG,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACR9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+C,kBAAkB,GAAIlB,UAAU,IAAK;IACzCvB,iBAAiB,CAACuB,UAAU,CAAC;IAC7BnB,aAAa,CAACmB,UAAU,CAACX,EAAE,CAAC;;IAE5B;IACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,IAAIW,UAAU,CAACX,EAAE,KAAK,kBAAkB,EAAE;MAC5E;MACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,EAAE;QACpC,MAAM8B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;QACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;QAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAExCxC,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXG,WAAW,EAAEoC,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAClDvC,SAAS,EAAEiC,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC;MACJ;MAEA9C,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACL;MACAoB,wBAAwB,CAACC,UAAU,CAACX,EAAE,EAAE,OAAO,CAAC;IAClD;EACF,CAAC;EAED,MAAMqC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAM3B,wBAAwB,CAACnB,UAAU,EAAEE,QAAQ,CAACE,OAAO,CAAC;IAC5DL,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMgD,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhD,aAAa,CAAC,KAAK,CAAC;IACpBN,QAAQ,CAAC,IAAI,CAAC;IACdU,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMyC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACtD,UAAU,EAAE,OAAO,IAAI;IAE5B,oBACEZ,OAAA,CAACxD,KAAK;MAAC2H,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzBtE,OAAA,CAAC1D,GAAG;QAAC6H,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzFtE,OAAA,CAACzD,UAAU;UAACoI,OAAO,EAAC,IAAI;UAAAL,QAAA,GACrBxD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEc,KAAK,EAAC,KAAG,EAAChB,UAAU,CAACgE,aAAa;QAAA;UAAA7C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACblC,OAAA,CAAC1D,GAAG;UAAC6H,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE;UAAE,CAAE;UAAAP,QAAA,gBAEnCtE,OAAA,CAACnD,MAAM;YACLiI,SAAS,eAAE9E,OAAA,CAACrB,YAAY;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5B6C,OAAO,EAAEA,CAAA,KAAM1C,wBAAwB,CAACnB,UAAU,EAAE,KAAK,CAAE;YAC3DyD,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZ7C,KAAK,EAAC,SAAS;YAAAmC,QAAA,EAChB;UAED;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlC,OAAA,CAACnD,MAAM;YACLiI,SAAS,eAAE9E,OAAA,CAACrB,YAAY;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5B6C,OAAO,EAAEA,CAAA,KAAM1C,wBAAwB,CAACnB,UAAU,EAAE,OAAO,CAAE;YAC7DyD,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZ7C,KAAK,EAAC,SAAS;YAAAmC,QAAA,EAChB;UAED;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlC,OAAA,CAACnD,MAAM;YACLiI,SAAS,eAAE9E,OAAA,CAACjB,WAAW;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3B6C,OAAO,EAAEA,CAAA,KAAMlE,aAAa,CAAC,IAAI,CAAE;YACnC8D,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YAAAV,QAAA,EACb;UAED;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlC,OAAA,CAAC/C,OAAO;QAACkH,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE;MAAE;QAAA3C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGzBhB,UAAU,KAAK,UAAU,IAAI+D,oBAAoB,CAAC,CAAC,EACnD/D,UAAU,KAAK,KAAK,IAAIgE,eAAe,CAAC,CAAC,EACzChE,UAAU,KAAK,QAAQ,IAAIiE,kBAAkB,CAAC,CAAC,EAC/CjE,UAAU,KAAK,kBAAkB,IAAIkE,2BAA2B,CAAC,CAAC,EAClElE,UAAU,KAAK,cAAc,IAAImE,uBAAuB,CAAC,CAAC,EAC1DnE,UAAU,KAAK,YAAY,IAAIoE,qBAAqB,CAAC,CAAC;IAAA;MAAAvD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAEZ,CAAC;EAED,MAAM+C,oBAAoB,GAAGA,CAAA,kBAC3BjF,OAAA,CAACvD,IAAI;IAAC8I,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAlB,QAAA,gBACzBtE,OAAA,CAACvD,IAAI;MAACgJ,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAArB,QAAA,eACvBtE,OAAA,CAACtD,IAAI;QAAA4H,QAAA,eACHtE,OAAA,CAACrD,WAAW;UAAA2H,QAAA,gBACVtE,OAAA,CAACzD,UAAU;YAACoI,OAAO,EAAC,IAAI;YAACiB,YAAY;YAAAtB,QAAA,EAAC;UAAoB;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACvElC,OAAA,CAACzD,UAAU;YAAA+H,QAAA,GAAC,gBAAc,eAAAtE,OAAA;cAAAsE,QAAA,GAAS1D,UAAU,CAACiF,YAAY,EAAC,GAAC;YAAA;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClFlC,OAAA,CAACzD,UAAU;YAAA+H,QAAA,GAAC,gBAAc,eAAAtE,OAAA;cAAAsE,QAAA,GAAS1D,UAAU,CAACkF,YAAY,EAAC,GAAC;YAAA;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClFlC,OAAA,CAACzD,UAAU;YAAA+H,QAAA,GAAC,mBAAiB,eAAAtE,OAAA;cAAAsE,QAAA,GAAS1D,UAAU,CAACmF,eAAe,EAAC,GAAC;YAAA;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxFlC,OAAA,CAACzD,UAAU;YAAA+H,QAAA,GAAC,eAAa,eAAAtE,OAAA;cAAAsE,QAAA,GAAS1D,UAAU,CAACoF,uBAAuB,EAAC,GAAC;YAAA;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACPlC,OAAA,CAACvD,IAAI;MAACgJ,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAArB,QAAA,eACvBtE,OAAA,CAACtD,IAAI;QAAA4H,QAAA,eACHtE,OAAA,CAACrD,WAAW;UAAA2H,QAAA,gBACVtE,OAAA,CAACzD,UAAU;YAACoI,OAAO,EAAC,IAAI;YAACiB,YAAY;YAAAtB,QAAA,EAAC;UAAI;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACvDlC,OAAA,CAACzD,UAAU;YAAA+H,QAAA,GAAC,eAAa,eAAAtE,OAAA;cAAAsE,QAAA,EAAS1D,UAAU,CAACqF;YAAW;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/ElC,OAAA,CAACzD,UAAU;YAAA+H,QAAA,GAAC,eAAa,eAAAtE,OAAA;cAAAsE,QAAA,EAAS1D,UAAU,CAACsF;YAAW;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/ElC,OAAA,CAACzD,UAAU;YAAA+H,QAAA,GAAC,kBAAgB,eAAAtE,OAAA;cAAAsE,QAAA,EAAS1D,UAAU,CAACuF;YAAc;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrFlC,OAAA,CAACzD,UAAU;YAAA+H,QAAA,GAAC,oBAAkB,eAAAtE,OAAA;cAAAsE,QAAA,GAAS1D,UAAU,CAACwF,gBAAgB,EAAC,GAAC;YAAA;cAAArE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACPlC,OAAA,CAACvD,IAAI;MAACgJ,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAArB,QAAA,eACvBtE,OAAA,CAACtD,IAAI;QAAA4H,QAAA,eACHtE,OAAA,CAACrD,WAAW;UAAA2H,QAAA,gBACVtE,OAAA,CAACzD,UAAU;YAACoI,OAAO,EAAC,IAAI;YAACiB,YAAY;YAAAtB,QAAA,EAAC;UAAW;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9DlC,OAAA,CAACzD,UAAU;YAAA+H,QAAA,GAAC,qBAAmB,eAAAtE,OAAA;cAAAsE,QAAA,GAAS1D,UAAU,CAACyF,iBAAiB,EAAC,UAAQ;YAAA;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAClGtB,UAAU,CAAC0F,cAAc,iBACxBtG,OAAA,CAAAE,SAAA;YAAAoE,QAAA,gBACEtE,OAAA,CAACzD,UAAU;cAAA+H,QAAA,GAAC,kBAAgB,eAAAtE,OAAA;gBAAAsE,QAAA,GAAS1D,UAAU,CAAC0F,cAAc,EAAC,SAAO;cAAA;gBAAAvE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5FlC,OAAA,CAACzD,UAAU;cAAA+H,QAAA,GAAC,sBAAoB,eAAAtE,OAAA;gBAAAsE,QAAA,EAAS1D,UAAU,CAAC2F;cAAkB;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA,eAC7F,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACNtB,UAAU,CAAC4F,YAAY,IAAI5F,UAAU,CAAC4F,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC5DzG,OAAA,CAACvD,IAAI;MAACgJ,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAArB,QAAA,eACvBtE,OAAA,CAACtD,IAAI;QAAA4H,QAAA,eACHtE,OAAA,CAACrD,WAAW;UAAA2H,QAAA,gBACVtE,OAAA,CAACzD,UAAU;YAACoI,OAAO,EAAC,IAAI;YAACiB,YAAY;YAAAtB,QAAA,EAAC;UAAY;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAC9DtB,UAAU,CAAC4F,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACnD7G,OAAA,CAACzD,UAAU;YAAA+H,QAAA,GACRsC,IAAI,CAACE,IAAI,EAAC,IAAE,eAAA9G,OAAA;cAAAsE,QAAA,GAASsC,IAAI,CAACG,KAAK,EAAC,GAAC;YAAA;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,GAD5B2E,KAAK;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CACP;EAED,MAAMgD,eAAe,GAAGA,CAAA;IAAA,IAAA8B,qBAAA,EAAAC,qBAAA;IAAA,oBACtBjH,OAAA,CAACvD,IAAI;MAAC8I,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAlB,QAAA,gBACzBtE,OAAA,CAACvD,IAAI;QAACgJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAAApB,QAAA,gBAChBtE,OAAA,CAACzD,UAAU;UAACoI,OAAO,EAAC,IAAI;UAACiB,YAAY;UAAAtB,QAAA,EAAC;QAAkB;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACrElC,OAAA,CAACvD,IAAI;UAAC8I,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAlB,QAAA,GAAA0C,qBAAA,GACxBpG,UAAU,CAACsG,aAAa,cAAAF,qBAAA,uBAAxBA,qBAAA,CAA0BL,GAAG,CAAC,CAACQ,IAAI,EAAEN,KAAK,kBACzC7G,OAAA,CAACvD,IAAI;YAACgJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACyB,EAAE,EAAE,CAAE;YAAA9C,QAAA,eAC9BtE,OAAA,CAACtD,IAAI;cAAA4H,QAAA,eACHtE,OAAA,CAACrD,WAAW;gBAAA2H,QAAA,gBACVtE,OAAA,CAACzD,UAAU;kBAACoI,OAAO,EAAC,WAAW;kBAAAL,QAAA,EAAE6C,IAAI,CAACE;gBAAS;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC7DlC,OAAA,CAACzD,UAAU;kBAACoI,OAAO,EAAC,OAAO;kBAAAL,QAAA,GAAC,WAAS,EAAC6C,IAAI,CAACG,OAAO;gBAAA;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAChElC,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,GAAC,QAAM,EAAC6C,IAAI,CAACI,QAAQ;gBAAA;kBAAAxF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC9ClC,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,GAAC,iBAAe,EAAC6C,IAAI,CAACK,aAAa,EAAC,GAAC;gBAAA;kBAAAzF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7DlC,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,GAAC,eAAa,EAAC6C,IAAI,CAACM,WAAW,EAAC,GAAC;gBAAA;kBAAA1F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzDlC,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,GAAC,aAAW,EAAC6C,IAAI,CAACpB,eAAe,EAAC,GAAC;gBAAA;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAV6B2E,KAAK;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPlC,OAAA,CAACvD,IAAI;QAACgJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAAApB,QAAA,gBAChBtE,OAAA,CAACzD,UAAU;UAACoI,OAAO,EAAC,IAAI;UAACiB,YAAY;UAAAtB,QAAA,EAAC;QAAkB;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACrElC,OAAA,CAACvD,IAAI;UAAC8I,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAlB,QAAA,GAAA2C,qBAAA,GACxBrG,UAAU,CAAC8G,eAAe,cAAAT,qBAAA,uBAA1BA,qBAAA,CAA4BN,GAAG,CAAC,CAACgB,MAAM,EAAEd,KAAK,kBAC7C7G,OAAA,CAACvD,IAAI;YAACgJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACyB,EAAE,EAAE,CAAE;YAAA9C,QAAA,eAC9BtE,OAAA,CAACtD,IAAI;cAAA4H,QAAA,eACHtE,OAAA,CAACrD,WAAW;gBAAA2H,QAAA,gBACVtE,OAAA,CAACzD,UAAU;kBAACoI,OAAO,EAAC,WAAW;kBAAAL,QAAA,EAAEqD,MAAM,CAACN;gBAAS;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC/DlC,OAAA,CAACzD,UAAU;kBAACoI,OAAO,EAAC,OAAO;kBAAAL,QAAA,GAAC,WAAS,EAACqD,MAAM,CAACL,OAAO;gBAAA;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAClElC,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,GAAC,UAAQ,EAACqD,MAAM,CAACC,UAAU;gBAAA;kBAAA7F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpDlC,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,GAAC,qBAAmB,EAACqD,MAAM,CAACE,iBAAiB,EAAC,GAAC;gBAAA;kBAAA9F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAR6B2E,KAAK;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAMiD,kBAAkB,GAAGA,CAAA;IAAA,IAAA2C,kBAAA;IAAA,oBACzB9H,OAAA,CAACvD,IAAI;MAAC8I,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAlB,QAAA,eACzBtE,OAAA,CAACvD,IAAI;QAACgJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAAApB,QAAA,gBAChBtE,OAAA,CAACzD,UAAU;UAACoI,OAAO,EAAC,IAAI;UAACiB,YAAY;UAAAtB,QAAA,GAAC,uBACf,EAAC1D,UAAU,CAACmH,aAAa,EAAC,UACjD;QAAA;UAAAhG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblC,OAAA,CAACvD,IAAI;UAAC8I,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAlB,QAAA,GAAAwD,kBAAA,GACxBlH,UAAU,CAACoH,MAAM,cAAAF,kBAAA,uBAAjBA,kBAAA,CAAmBnB,GAAG,CAAC,CAACgB,MAAM,EAAEd,KAAK,kBACpC7G,OAAA,CAACvD,IAAI;YAACgJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACyB,EAAE,EAAE,CAAE;YAAA9C,QAAA,eAC9BtE,OAAA,CAACtD,IAAI;cAAA4H,QAAA,eACHtE,OAAA,CAACrD,WAAW;gBAAA2H,QAAA,gBACVtE,OAAA,CAACzD,UAAU;kBAACoI,OAAO,EAAC,WAAW;kBAAAL,QAAA,EAAEqD,MAAM,CAAClG;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC/DlC,OAAA,CAACzD,UAAU;kBAACoI,OAAO,EAAC,OAAO;kBAAAL,QAAA,GAAEqD,MAAM,CAACN,SAAS,EAAC,KAAG,EAACM,MAAM,CAACL,OAAO;gBAAA;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC9ElC,OAAA,CAAClD,IAAI;kBACHmL,KAAK,EAAEN,MAAM,CAACO,KAAM;kBACpB/F,KAAK,EAAEwF,MAAM,CAACO,KAAK,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;kBAC9DlD,IAAI,EAAC,OAAO;kBACZb,EAAE,EAAE;oBAAEO,EAAE,EAAE;kBAAE;gBAAE;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACFlC,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,GAAC,gBAAc,EAACqD,MAAM,CAAC9B,YAAY,EAAC,GAAC;gBAAA;kBAAA9D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7DlC,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,GAAC,iBAAe,EAACqD,MAAM,CAACQ,aAAa,EAAC,GAAC;gBAAA;kBAAApG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/DlC,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,GAAC,oBAAkB,EAACqD,MAAM,CAACS,gBAAgB,EAAC,GAAC;gBAAA;kBAAArG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrElC,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,GAAC,YAAU,EAACqD,MAAM,CAACU,oBAAoB,EAAC,GAAC;gBAAA;kBAAAtG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAhB6B2E,KAAK;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAMkD,2BAA2B,GAAGA,CAAA;IAAA,IAAAkD,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,qBAAA;IAAA,oBAClC/I,OAAA,CAACvD,IAAI;MAAC8I,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAlB,QAAA,gBACzBtE,OAAA,CAACvD,IAAI;QAACgJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eACvBtE,OAAA,CAACtD,IAAI;UAAA4H,QAAA,eACHtE,OAAA,CAACrD,WAAW;YAAA2H,QAAA,gBACVtE,OAAA,CAACzD,UAAU;cAACoI,OAAO,EAAC,IAAI;cAACiB,YAAY;cAAAtB,QAAA,EAAC;YAAe;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClElC,OAAA,CAACzD,UAAU;cAAA+H,QAAA,GAAC,MAAI,eAAAtE,OAAA;gBAAAsE,QAAA,GAAAgE,kBAAA,GAAS1H,UAAU,CAAC+G,MAAM,cAAAW,kBAAA,uBAAjBA,kBAAA,CAAmB7G;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5ElC,OAAA,CAACzD,UAAU;cAAA+H,QAAA,GAAC,aAAW,eAAAtE,OAAA;gBAAAsE,QAAA,GAAAiE,mBAAA,GAAS3H,UAAU,CAAC+G,MAAM,cAAAY,mBAAA,uBAAjBA,mBAAA,CAAmBlB;cAAS;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnFlC,OAAA,CAACzD,UAAU;cAAA+H,QAAA,GAAC,WAAS,eAAAtE,OAAA;gBAAAsE,QAAA,GAAAkE,mBAAA,GAAS5H,UAAU,CAAC+G,MAAM,cAAAa,mBAAA,uBAAjBA,mBAAA,CAAmBlB;cAAO;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/ElC,OAAA,CAAClD,IAAI;cACHmL,KAAK,GAAAQ,mBAAA,GAAE7H,UAAU,CAAC+G,MAAM,cAAAc,mBAAA,uBAAjBA,mBAAA,CAAmBP,KAAM;cAChC/F,KAAK,EAAE,EAAAuG,mBAAA,GAAA9H,UAAU,CAAC+G,MAAM,cAAAe,mBAAA,uBAAjBA,mBAAA,CAAmBR,KAAK,MAAK,aAAa,GAAG,SAAS,GAAG,SAAU;cAC1E/D,EAAE,EAAE;gBAAE6E,EAAE,EAAE;cAAE;YAAE;cAAAjH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACFlC,OAAA,CAACzD,UAAU;cAAA+H,QAAA,GAAC,gBAAc,eAAAtE,OAAA;gBAAAsE,QAAA,IAAAqE,mBAAA,GAAS/H,UAAU,CAAC+G,MAAM,cAAAgB,mBAAA,uBAAjBA,mBAAA,CAAmB9C,YAAY,EAAC,GAAC;cAAA;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1FlC,OAAA,CAACzD,UAAU;cAAA+H,QAAA,GAAC,iBAAe,eAAAtE,OAAA;gBAAAsE,QAAA,IAAAsE,mBAAA,GAAShI,UAAU,CAAC+G,MAAM,cAAAiB,mBAAA,uBAAjBA,mBAAA,CAAmBT,aAAa,EAAC,GAAC;cAAA;gBAAApG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5FlC,OAAA,CAACzD,UAAU;cAAA+H,QAAA,GAAC,oBAAkB,eAAAtE,OAAA;gBAAAsE,QAAA,IAAAuE,mBAAA,GAASjI,UAAU,CAAC+G,MAAM,cAAAkB,mBAAA,uBAAjBA,mBAAA,CAAmBT,gBAAgB,EAAC,GAAC;cAAA;gBAAArG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClGlC,OAAA,CAACzD,UAAU;cAAA+H,QAAA,GAAC,YAAU,eAAAtE,OAAA;gBAAAsE,QAAA,IAAAwE,mBAAA,GAASlI,UAAU,CAAC+G,MAAM,cAAAmB,mBAAA,uBAAjBA,mBAAA,CAAmBT,oBAAoB,EAAC,GAAC;cAAA;gBAAAtG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPlC,OAAA,CAACvD,IAAI;QAACgJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eACvBtE,OAAA,CAACtD,IAAI;UAAA4H,QAAA,eACHtE,OAAA,CAACrD,WAAW;YAAA2H,QAAA,gBACVtE,OAAA,CAACzD,UAAU;cAACoI,OAAO,EAAC,IAAI;cAACiB,YAAY;cAAAtB,QAAA,GAAC,kBACpB,EAAC1D,UAAU,CAACqF,WAAW,EAAC,GAC1C;YAAA;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblC,OAAA,CAAC1D,GAAG;cAAC6H,EAAE,EAAE;gBAAE8E,SAAS,EAAE,GAAG;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAA5E,QAAA,GAAAyE,qBAAA,GAC3CnI,UAAU,CAACuI,cAAc,cAAAJ,qBAAA,uBAAzBA,qBAAA,CAA2BpC,GAAG,CAAC,CAACQ,IAAI,EAAEN,KAAK,kBAC1C7G,OAAA,CAAC1D,GAAG;gBAAa6H,EAAE,EAAE;kBAAEO,EAAE,EAAE,CAAC;kBAAEN,CAAC,EAAE,CAAC;kBAAEgF,MAAM,EAAE,mBAAmB;kBAAEC,YAAY,EAAE;gBAAE,CAAE;gBAAA/E,QAAA,gBACjFtE,OAAA,CAACzD,UAAU;kBAACoI,OAAO,EAAC,OAAO;kBAAAL,QAAA,eAACtE,OAAA;oBAAAsE,QAAA,EAAS6C,IAAI,CAACmC;kBAAO;oBAAAvH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxElC,OAAA,CAACzD,UAAU;kBAACoI,OAAO,EAAC,SAAS;kBAAAL,QAAA,GAC1B6C,IAAI,CAACoC,OAAO,EAAC,KAAG,EAACpC,IAAI,CAACqC,OAAO,EAAC,KAAG,EAACrC,IAAI,CAACE,SAAS;gBAAA;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACblC,OAAA,CAACzD,UAAU;kBAACoI,OAAO,EAAC,SAAS;kBAACJ,OAAO,EAAC,OAAO;kBAAAD,QAAA,GAAC,WACnC,EAAC6C,IAAI,CAACK,aAAa,EAAC,aAAW,EAACL,IAAI,CAACM,WAAW,EAAC,aAAW,EAACN,IAAI,CAACe,KAAK;gBAAA;kBAAAnG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC;cAAA,GAPL2E,KAAK;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAMmD,uBAAuB,GAAGA,CAAA;IAAA,IAAAoE,qBAAA;IAAA,oBAC9BzJ,OAAA,CAACvD,IAAI;MAAC8I,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAlB,QAAA,gBACzBtE,OAAA,CAACvD,IAAI;QAACgJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eACvBtE,OAAA,CAACtD,IAAI;UAAA4H,QAAA,eACHtE,OAAA,CAACrD,WAAW;YAAA2H,QAAA,gBACVtE,OAAA,CAACzD,UAAU;cAACoI,OAAO,EAAC,IAAI;cAACiB,YAAY;cAAAtB,QAAA,EAAC;YAAmB;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACtElC,OAAA,CAACzD,UAAU;cAAA+H,QAAA,GAAC,WAAS,eAAAtE,OAAA;gBAAAsE,QAAA,GAAS1D,UAAU,CAACW,WAAW,EAAC,KAAG,EAACX,UAAU,CAACY,SAAS;cAAA;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpGlC,OAAA,CAACzD,UAAU;cAAA+H,QAAA,GAAC,gBAAc,eAAAtE,OAAA;gBAAAsE,QAAA,GAAS1D,UAAU,CAAC8I,oBAAoB,EAAC,GAAC;cAAA;gBAAA3H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1FlC,OAAA,CAACzD,UAAU;cAAA+H,QAAA,GAAC,iBAAe,eAAAtE,OAAA;gBAAAsE,QAAA,EAAS1D,UAAU,CAAC+I;cAAa;gBAAA5H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnFlC,OAAA,CAACzD,UAAU;cAAA+H,QAAA,GAAC,qBAAmB,eAAAtE,OAAA;gBAAAsE,QAAA,GAAS1D,UAAU,CAACyF,iBAAiB,EAAC,UAAQ;cAAA;gBAAAtE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPlC,OAAA,CAACvD,IAAI;QAACgJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAArB,QAAA,eACvBtE,OAAA,CAACtD,IAAI;UAAA4H,QAAA,eACHtE,OAAA,CAACrD,WAAW;YAAA2H,QAAA,gBACVtE,OAAA,CAACzD,UAAU;cAACoI,OAAO,EAAC,IAAI;cAACiB,YAAY;cAAAtB,QAAA,EAAC;YAAgB;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnElC,OAAA,CAAC1D,GAAG;cAAC6H,EAAE,EAAE;gBAAE8E,SAAS,EAAE,GAAG;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAA5E,QAAA,GAAAmF,qBAAA,GAC3C7I,UAAU,CAACgJ,gBAAgB,cAAAH,qBAAA,uBAA3BA,qBAAA,CAA6B9C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC5C7G,OAAA,CAAC1D,GAAG;gBAAa6H,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEqF,EAAE,EAAE;gBAAI,CAAE;gBAAAvF,QAAA,gBACjFtE,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,EAAEsC,IAAI,CAACE;gBAAI;kBAAA/E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpClC,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,eAACtE,OAAA;oBAAAsE,QAAA,GAASsC,IAAI,CAACG,KAAK,EAAC,GAAC;kBAAA;oBAAAhF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA,GAF/C2E,KAAK;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAMoD,qBAAqB,GAAGA,CAAA;IAAA,IAAAwE,sBAAA;IAAA,oBAC5B9J,OAAA,CAACvD,IAAI;MAAC8I,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAlB,QAAA,eACzBtE,OAAA,CAACvD,IAAI;QAACgJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAAApB,QAAA,gBAChBtE,OAAA,CAACzD,UAAU;UAACoI,OAAO,EAAC,IAAI;UAACiB,YAAY;UAAAtB,QAAA,EAAC;QAA+B;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAClFlC,OAAA,CAACvD,IAAI;UAAC8I,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAlB,QAAA,GAAAwF,sBAAA,GACxBlJ,UAAU,CAACmJ,cAAc,cAAAD,sBAAA,uBAAzBA,sBAAA,CAA2BnD,GAAG,CAAC,CAACuB,KAAK,EAAErB,KAAK,kBAC3C7G,OAAA,CAACvD,IAAI;YAACgJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACyB,EAAE,EAAE,CAAE;YAAA9C,QAAA,eAC9BtE,OAAA,CAACtD,IAAI;cAAA4H,QAAA,eACHtE,OAAA,CAACrD,WAAW;gBAAA2H,QAAA,gBACVtE,OAAA,CAACzD,UAAU;kBAACoI,OAAO,EAAC,IAAI;kBAACiB,YAAY;kBAAAtB,QAAA,eACnCtE,OAAA,CAAClD,IAAI;oBACHmL,KAAK,EAAEC,KAAK,CAACA,KAAM;oBACnB/F,KAAK,EAAE+F,KAAK,CAACA,KAAK,KAAK,YAAY,GAAG,SAAS,GAAG,SAAU;oBAC5D/D,EAAE,EAAE;sBAAEO,EAAE,EAAE;oBAAE;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACblC,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,GAAC,eAAa,eAAAtE,OAAA;oBAAAsE,QAAA,EAAS4D,KAAK,CAACX;kBAAQ;oBAAAxF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvElC,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,GAAC,iBAAe,eAAAtE,OAAA;oBAAAsE,QAAA,GAAS4D,KAAK,CAACV,aAAa,EAAC,GAAC;kBAAA;oBAAAzF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/ElC,OAAA,CAACzD,UAAU;kBAAA+H,QAAA,GAAC,eAAa,eAAAtE,OAAA;oBAAAsE,QAAA,GAAS4D,KAAK,CAACT,WAAW,EAAC,GAAC;kBAAA;oBAAA1F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAd6B2E,KAAK;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAerC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM8H,YAAY,GAAGA,CAAA,kBACnBhK,OAAA,CAAC5C,MAAM;IAAC+F,IAAI,EAAEnC,UAAW;IAACiJ,OAAO,EAAEhG,iBAAkB;IAACiG,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAA7F,QAAA,gBAC3EtE,OAAA,CAAC3C,WAAW;MAAAiH,QAAA,EACTxD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEc;IAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACdlC,OAAA,CAAC1C,aAAa;MAAAgH,QAAA,GACX5D,KAAK,iBACJV,OAAA,CAACjD,KAAK;QAACqN,QAAQ,EAAC,OAAO;QAACjG,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EACnC5D;MAAK;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDlC,OAAA,CAACvD,IAAI;QAAC8I,SAAS;QAACC,OAAO,EAAE,CAAE;QAACrB,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACxCtE,OAAA,CAACvD,IAAI;UAACgJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAAApB,QAAA,eAChBtE,OAAA,CAACxC,WAAW;YAAC2M,SAAS;YAAA7F,QAAA,gBACpBtE,OAAA,CAACvC,UAAU;cAAA6G,QAAA,EAAC;YAAO;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChClC,OAAA,CAACtC,MAAM;cACL2M,KAAK,EAAEjJ,QAAQ,CAACE,OAAQ;cACxB2G,KAAK,EAAC,SAAS;cACfqC,QAAQ,EAAGC,CAAC,IAAKlJ,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAEiJ,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAAA/F,QAAA,gBAEvEtE,OAAA,CAACrC,QAAQ;gBAAC0M,KAAK,EAAC,OAAO;gBAAA/F,QAAA,EAAC;cAAoB;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvDlC,OAAA,CAACrC,QAAQ;gBAAC0M,KAAK,EAAC,KAAK;gBAAA/F,QAAA,EAAC;cAAY;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7ClC,OAAA,CAACrC,QAAQ;gBAAC0M,KAAK,EAAC,OAAO;gBAAA/F,QAAA,EAAC;cAAc;gBAAAvC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAENhB,UAAU,KAAK,kBAAkB,iBAChClB,OAAA,CAACvD,IAAI;UAACgJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAAApB,QAAA,eAChBtE,OAAA,CAACpC,SAAS;YACRuM,SAAS;YACTlC,KAAK,EAAC,WAAW;YACjBoC,KAAK,EAAEjJ,QAAQ,CAACK,SAAU;YAC1B6I,QAAQ,EAAGC,CAAC,IAAKlJ,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEK,SAAS,EAAE8I,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzEI,WAAW,EAAC,mBAAmB;YAC/BC,UAAU,EAAC;UAA0D;YAAA3I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP,EAEAhB,UAAU,KAAK,cAAc,iBAC5BlB,OAAA,CAAAE,SAAA;UAAAoE,QAAA,gBACEtE,OAAA,CAACvD,IAAI;YAACgJ,IAAI;YAACC,EAAE,EAAE,CAAE;YAAApB,QAAA,eACftE,OAAA,CAACpC,SAAS;cACRuM,SAAS;cACTQ,IAAI,EAAC,MAAM;cACX1C,KAAK,EAAC,aAAa;cACnBoC,KAAK,EAAEjJ,QAAQ,CAACG,WAAY;cAC5B+I,QAAQ,EAAGC,CAAC,IAAKlJ,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAEgJ,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC3EO,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA9I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPlC,OAAA,CAACvD,IAAI;YAACgJ,IAAI;YAACC,EAAE,EAAE,CAAE;YAAApB,QAAA,eACftE,OAAA,CAACpC,SAAS;cACRuM,SAAS;cACTQ,IAAI,EAAC,MAAM;cACX1C,KAAK,EAAC,WAAW;cACjBoC,KAAK,EAAEjJ,QAAQ,CAACI,SAAU;cAC1B8I,QAAQ,EAAGC,CAAC,IAAKlJ,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAE+I,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACzEO,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA9I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChBlC,OAAA,CAACzC,aAAa;MAAA+G,QAAA,gBACZtE,OAAA,CAACnD,MAAM;QAACkI,OAAO,EAAEd,iBAAkB;QAAAK,QAAA,EAAC;MAAO;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpDlC,OAAA,CAACnD,MAAM;QACLkI,OAAO,EAAEf,oBAAqB;QAC9BW,OAAO,EAAC,WAAW;QACnBmG,QAAQ,EAAEtK,OAAQ;QAClBsE,SAAS,EAAEtE,OAAO,gBAAGR,OAAA,CAAChD,gBAAgB;UAACgI,IAAI,EAAE;QAAG;UAAAjD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlC,OAAA,CAACnB,cAAc;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAoC,QAAA,EAExE9D,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACElC,OAAA,CAAC1D,GAAG;IAAC6H,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAE,QAAA,gBAEhBtE,OAAA,CAAC1D,GAAG;MAAC6H,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFtE,OAAA,CAAC1D,GAAG;QAAC6H,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEI,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACzDtE,OAAA,CAAC9C,UAAU;UAAC6H,OAAO,EAAEA,CAAA,KAAM1E,QAAQ,CAAC,CAAC,CAAC,CAAE;UAAC8B,KAAK,EAAC,SAAS;UAAAmC,QAAA,eACtDtE,OAAA,CAACf,aAAa;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACblC,OAAA,CAACzD,UAAU;UAACoI,OAAO,EAAC,IAAI;UAACoG,SAAS,EAAC,IAAI;UAAAzG,QAAA,EAAC;QAExC;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNlC,OAAA,CAACH,eAAe;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAGNlC,OAAA,CAACjD,KAAK;MAACqN,QAAQ,EAAC,MAAM;MAACjG,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,EAAC;IAGtC;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAGRlC,OAAA,CAACvD,IAAI;MAAC8I,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAlB,QAAA,EACxB5C,WAAW,CAACiF,GAAG,CAAEqE,MAAM,iBACtBhL,OAAA,CAACvD,IAAI;QAACgJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACyB,EAAE,EAAE,CAAE;QAAA9C,QAAA,eAC9BtE,OAAA,CAACtD,IAAI;UACHyH,EAAE,EAAE;YACF8G,MAAM,EAAE,MAAM;YACd1G,OAAO,EAAE,MAAM;YACf2G,aAAa,EAAE,QAAQ;YACvBC,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE,iCAAiC;YAC7C,SAAS,EAAE;cACTC,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb;UACF,CAAE;UACFvG,OAAO,EAAEA,CAAA,KAAMvB,kBAAkB,CAACwH,MAAM,CAAE;UAAA1G,QAAA,gBAE1CtE,OAAA,CAACrD,WAAW;YAACwH,EAAE,EAAE;cAAEoH,QAAQ,EAAE;YAAE,CAAE;YAAAjH,QAAA,gBAC/BtE,OAAA,CAAC1D,GAAG;cAAC6H,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACxDtE,OAAA,CAAC1D,GAAG;gBAAC6H,EAAE,EAAE;kBAAEhC,KAAK,EAAE,GAAG6I,MAAM,CAAC7I,KAAK,OAAO;kBAAEqJ,EAAE,EAAE;gBAAE,CAAE;gBAAAlH,QAAA,EAC/C0G,MAAM,CAAClJ;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNlC,OAAA,CAACzD,UAAU;gBAACoI,OAAO,EAAC,IAAI;gBAACoG,SAAS,EAAC,IAAI;gBAAAzG,QAAA,EACpC0G,MAAM,CAACpJ;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENlC,OAAA,CAACzD,UAAU;cAACoI,OAAO,EAAC,OAAO;cAACxC,KAAK,EAAC,gBAAgB;cAACgC,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAC9D0G,MAAM,CAACnJ;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEblC,OAAA,CAAC1D,GAAG;cAAAgI,QAAA,EACD0G,MAAM,CAAC5I,QAAQ,CAACuE,GAAG,CAAC,CAAC8E,OAAO,EAAE5E,KAAK,kBAClC7G,OAAA,CAAClD,IAAI;gBAEHmL,KAAK,EAAEwD,OAAQ;gBACfzG,IAAI,EAAC,OAAO;gBACZL,OAAO,EAAC,UAAU;gBAClBR,EAAE,EAAE;kBAAEqH,EAAE,EAAE,GAAG;kBAAE9G,EAAE,EAAE;gBAAI;cAAE,GAJpBmC,KAAK;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAEdlC,OAAA,CAACpD,WAAW;YAAA0H,QAAA,eACVtE,OAAA,CAACnD,MAAM;cACLmI,IAAI,EAAC,OAAO;cACZ7C,KAAK,EAAE6I,MAAM,CAAC7I,KAAM;cACpB2C,SAAS,eAAE9E,OAAA,CAAC/B,cAAc;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC9BiI,SAAS;cAAA7F,QAAA,EACV;YAED;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GApD6B8I,MAAM,CAACrJ,EAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqDzC,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGNgC,mBAAmB,CAAC,CAAC,EAGrB8F,YAAY,CAAC,CAAC;EAAA;IAAAjI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAtnBID,iBAAiB;EAAA,QACJT,WAAW,EACLC,SAAS,EACfC,OAAO;AAAA;AAAA8L,EAAA,GAHpBvL,iBAAiB;AAwnBvB,eAAeA,iBAAiB;AAAC,IAAAuL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}