{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\nconst API_URL = config.API_URL;\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null, filters = {}) => {\n    try {\n      console.log('getCavi chiamato con:', {\n        cantiereId,\n        tipoCavo,\n        filters\n      });\n      console.log('Tipo di cantiereId:', typeof cantiereId);\n\n      // Verifica che cantiereId sia definito\n      if (cantiereId === undefined || cantiereId === null) {\n        console.error('cantiereId è undefined o null');\n        throw new Error('ID cantiere mancante');\n      }\n\n      // Assicurati che cantiereId sia un numero\n      let cantiereIdNum = cantiereId;\n      if (typeof cantiereId === 'string') {\n        cantiereIdNum = parseInt(cantiereId, 10);\n        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);\n      }\n      if (isNaN(cantiereIdNum)) {\n        console.error('ID cantiere non è un numero valido:', cantiereId);\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log(`Caricamento cavi per cantiere ${cantiereIdNum} con tipo_cavo=${tipoCavo}`);\n\n      // Soluzione alternativa per i cavi SPARE\n      if (tipoCavo === 3) {\n        console.log('Caricamento cavi SPARE con query diretta...');\n        try {\n          // Usa una query SQL diretta per ottenere i cavi SPARE\n          const response = await axios.get(`${API_URL}/cavi/spare/${cantiereIdNum}`, {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          });\n          console.log('Risposta cavi SPARE:', response.data);\n          return response.data;\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi SPARE:', spareError);\n          // Se fallisce, continua con il metodo standard\n        }\n      }\n\n      // Costruisci l'URL con i parametri di query\n      let url = `/cavi/${cantiereIdNum}`;\n      const queryParams = [];\n      if (tipoCavo !== null) {\n        queryParams.push(`tipo_cavo=${tipoCavo}`);\n      }\n\n      // Aggiungi filtri aggiuntivi se presenti\n      if (filters.stato_installazione) {\n        queryParams.push(`stato_installazione=${encodeURIComponent(filters.stato_installazione)}`);\n      }\n      if (filters.tipologia) {\n        queryParams.push(`tipologia=${encodeURIComponent(filters.tipologia)}`);\n      }\n      if (filters.sort_by) {\n        queryParams.push(`sort_by=${encodeURIComponent(filters.sort_by)}`);\n        if (filters.sort_order) {\n          queryParams.push(`sort_order=${encodeURIComponent(filters.sort_order)}`);\n        }\n      }\n\n      // Aggiungi i parametri di query all'URL\n      if (queryParams.length > 0) {\n        url += `?${queryParams.join('&')}`;\n      }\n\n      // Log dettagliato dell'URL e dei parametri\n      console.log('URL API completo:', url);\n      console.log('Parametri di query:', queryParams);\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');\n      console.log('URL completo:', `${API_URL}${url}`);\n      try {\n        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);\n        console.log('Headers della richiesta:', {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        });\n\n        // Aggiungi un timeout più lungo per la richiesta\n        const response = await axiosInstance.get(url, {\n          timeout: 60000\n        });\n        console.log(`Risposta API: ${url}`, response.data);\n        console.log('Status della risposta:', response.status);\n        console.log('Headers della risposta:', response.headers);\n        if (Array.isArray(response.data)) {\n          console.log(`Numero di cavi ricevuti: ${response.data.length}`);\n          if (response.data.length > 0) {\n            console.log('Primo cavo ricevuto:', response.data[0]);\n          } else {\n            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);\n          }\n        } else {\n          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);\n        }\n        return response.data;\n      } catch (apiError) {\n        var _apiError$response, _apiError$response2, _apiError$response3, _apiError$response4;\n        console.error(`Errore nella chiamata API GET ${url}:`, apiError);\n        console.error('Dettagli errore API:', {\n          message: apiError.message,\n          status: (_apiError$response = apiError.response) === null || _apiError$response === void 0 ? void 0 : _apiError$response.status,\n          statusText: (_apiError$response2 = apiError.response) === null || _apiError$response2 === void 0 ? void 0 : _apiError$response2.statusText,\n          data: (_apiError$response3 = apiError.response) === null || _apiError$response3 === void 0 ? void 0 : _apiError$response3.data,\n          headers: (_apiError$response4 = apiError.response) === null || _apiError$response4 === void 0 ? void 0 : _apiError$response4.headers,\n          code: apiError.code,\n          isAxiosError: apiError.isAxiosError,\n          config: apiError.config ? {\n            url: apiError.config.url,\n            method: apiError.config.method,\n            timeout: apiError.config.timeout,\n            headers: apiError.config.headers\n          } : 'No config'\n        });\n\n        // Gestione specifica per errori di rete\n        if (apiError.code === 'ERR_NETWORK') {\n          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');\n          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile\n          try {\n            console.log('Tentativo di test di connessione al backend...');\n            const testResponse = await fetch(API_URL);\n            console.log('Test di connessione al backend:', testResponse.status);\n          } catch (testError) {\n            console.error('Test di connessione al backend fallito:', testError);\n          }\n        }\n        throw apiError;\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$response4, _error$response4$data, _error$response5, _error$response6;\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,\n        stack: error.stack\n      });\n\n      // Verifica se l'errore è dovuto a un problema di connessione\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout') || error.message.includes('Network Error')) {\n        console.error('Errore di connessione o timeout');\n        // Ritorna un array vuoto invece di lanciare un errore\n        console.log('Ritorno array vuoto come fallback');\n        return [];\n      }\n\n      // Crea un errore più informativo\n      const enhancedError = new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || error.message || 'Errore sconosciuto');\n      enhancedError.status = (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status;\n      enhancedError.data = (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data;\n      enhancedError.response = error.response;\n      enhancedError.originalError = error;\n      enhancedError.code = error.code;\n      enhancedError.isAxiosError = error.isAxiosError;\n      throw enhancedError;\n    }\n  },\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Tentativo di creazione cavo per cantiere ${cantiereIdNum}`);\n      console.log('Dati inviati:', JSON.stringify(cavoData, null, 2));\n\n      // Invia la richiesta al server\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}`, cavoData, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta del server:', response.status, response.statusText);\n      console.log('Dati ricevuti:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError || !error.response || error.code === 'ECONNABORTED' || error.message && error.message.includes('Network Error')) {\n        console.log('Errore di rete o timeout, verifica se il cavo è stato creato...');\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste nel database\n          const token = localStorage.getItem('token');\n          const checkResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/check/${cavoData.id_cavo}`, {\n            headers: {\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 5000\n          });\n          if (checkResponse.data && checkResponse.data.exists) {\n            console.log('Il cavo risulta creato nonostante l\\'errore di comunicazione');\n            // Recupera i dati del cavo\n            const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/${cavoData.id_cavo}`, {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 5000\n            });\n            if (cavoResponse.data) {\n              console.log('Dati del cavo recuperati:', cavoResponse.data);\n              return cavoResponse.data;\n            }\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o il cavo non esiste\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      if (error.response) {\n        console.error('Dettagli errore:', error.response.data);\n        console.error('Status errore:', error.response.status);\n        console.error('Headers errore:', error.response.headers);\n\n        // Formatta il messaggio di errore in modo più leggibile\n        const errorDetail = error.response.data.detail || 'Errore sconosciuto';\n        throw {\n          detail: errorDetail,\n          status: error.response.status\n        };\n      }\n      // Se è un errore di validazione locale, formatta il messaggio\n      if (error instanceof Error) {\n        throw {\n          detail: error.message,\n          status: 400\n        };\n      }\n      throw error;\n    }\n  },\n  // Ottiene un cavo specifico per ID\n  getCavoById: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/${cavoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavo by ID error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta PUT a /cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Dati inviati:', cavoData);\n\n      // Verifica che il backend sia raggiungibile\n      try {\n        console.log('Verifica connessione al backend...');\n        const pingResponse = await fetch(`${API_URL}/health`, {\n          method: 'GET'\n        });\n        console.log('Ping al backend:', pingResponse.status, pingResponse.statusText);\n        if (!pingResponse.ok) {\n          console.error('Il server non risponde correttamente:', pingResponse.status);\n          throw new Error('Il server non risponde correttamente. Riprova più tardi.');\n        }\n      } catch (pingError) {\n        console.error('Errore durante il ping al backend:', pingError);\n        throw new Error('Impossibile connettersi al server. Verifica la connessione di rete e riprova.');\n      }\n\n      // Imposta un timeout più lungo per la richiesta\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData, {\n        timeout: 90000 // 90 secondi (timeout esteso)\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError || !error.response || error.code === 'ECONNABORTED' || error.message && error.message.includes('Network Error') || error.request) {\n        console.log('Errore di rete o timeout, verifica se il cavo è stato aggiornato...');\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se è stato aggiornato\n          const token = localStorage.getItem('token');\n          const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n            headers: {\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 5000\n          });\n          if (cavoResponse.data) {\n            console.log('Cavo trovato nel database, verifica se è stato aggiornato');\n\n            // Verifica se almeno uno dei campi è stato aggiornato\n            let isUpdated = false;\n            for (const key in cavoData) {\n              if (cavoData[key] !== undefined && JSON.stringify(cavoData[key]) === JSON.stringify(cavoResponse.data[key])) {\n                console.log(`Campo ${key} risulta aggiornato: ${cavoData[key]}`);\n                isUpdated = true;\n                break;\n              }\n            }\n            if (isUpdated) {\n              console.log('Il cavo risulta aggiornato nonostante l\\'errore di comunicazione');\n              return cavoResponse.data;\n            } else {\n              console.log('Il cavo esiste ma non risulta aggiornato');\n            }\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o il cavo non è stato aggiornato\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: 'Impossibile verificare se la modifica è stata salvata. Controlla lo stato del cavo prima di riprovare.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n\n      // Gestione più dettagliata dell'errore\n      if (error.response) {\n        // Il server ha risposto con un codice di stato diverso da 2xx\n        console.error('Errore dal server:', error.response.status, error.response.statusText);\n        console.error('Dati errore:', error.response.data);\n        throw error.response.data;\n      } else {\n        // Si è verificato un errore durante l'impostazione della richiesta\n        console.error('Errore durante l\\'impostazione della richiesta:', error.message);\n        throw {\n          detail: error.message,\n          status: 500\n        };\n      }\n    }\n  },\n  // Ottiene la revisione corrente del cantiere\n  getRevisioneCorrente: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);\n      return response.data.revisione_corrente;\n    } catch (error) {\n      console.error('Get revisione corrente error:', error);\n      return '00'; // Valore di default in caso di errore\n    }\n  },\n  // Marca un cavo come SPARE\n  markCavoAsSpare: async (cantiereId, cavoId, force = false) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di marcare cavo come SPARE:', {\n        cantiereId: cantiereIdNum,\n        cavoId,\n        force\n      });\n\n      // Prova prima con l'endpoint POST specifico\n      console.log('URL API (POST):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`);\n      try {\n        const postResponse = await axios.post(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`, {\n          force: force\n        }, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Risposta markCavoAsSpare (POST):', postResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE...');\n        const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Stato del cavo dopo marcatura:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n        return cavoResponse.data;\n      } catch (postError) {\n        // Se fallisce il POST, prova con DELETE mode=spare\n        console.error('Errore con endpoint POST, tentativo con DELETE mode=spare:', postError);\n        console.log('URL API (DELETE):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}?mode=spare`);\n        const deleteResponse = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000,\n          params: {\n            mode: 'spare'\n          }\n        });\n        console.log('Risposta markCavoAsSpare (DELETE mode=spare):', deleteResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE con DELETE...');\n        const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Stato del cavo dopo marcatura con DELETE:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n        return cavoResponse.data;\n      }\n    } catch (error) {\n      var _error$response7, _error$response8, _error$response9;\n      console.error('Mark cavo as SPARE error:', error);\n      console.error('Dettagli errore:', {\n        message: error.message,\n        status: (_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : _error$response7.status,\n        statusText: (_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : _error$response8.statusText,\n        data: (_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : _error$response9.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un cavo o lo marca come SPARE\n  deleteCavo: async (cantiereId, cavoId, mode = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di eliminare/marcare cavo:', {\n        cantiereId: cantiereIdNum,\n        cavoId,\n        mode\n      });\n\n      // Se è specificata la modalità, aggiungi il parametro alla richiesta\n      const requestConfig = {\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        timeout: 30000,\n        // Timeout aumentato a 30 secondi\n        params: mode ? {\n          mode\n        } : {}\n      };\n      console.log('URL API:', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Config:', requestConfig);\n\n      // Usa axios direttamente invece di axiosInstance per avere più controllo\n      const response = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, requestConfig);\n      console.log('Risposta deleteCavo:', response.data);\n      return response.data;\n    } catch (error) {\n      var _error$response0, _error$response1, _error$response10;\n      console.error('Delete cavo error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: (_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : _error$response0.status,\n        statusText: (_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : _error$response1.statusText,\n        data: (_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : _error$response10.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n\n      // Crea un errore più informativo\n      if (error.response && error.response.data) {\n        throw error.response.data;\n      } else if (error.message) {\n        throw new Error(error.message);\n      } else {\n        throw new Error('Errore durante l\\'eliminazione del cavo');\n      }\n    }\n  },\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/metri-posati`);\n      console.log('Metri posati:', metriPosati);\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      }, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError || !error.response || error.code === 'ECONNABORTED' || error.message && error.message.includes('Network Error')) {\n        console.log('Errore di rete o timeout, verifica se i metri posati sono stati aggiornati...');\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se i metri posati sono stati aggiornati\n          const token = localStorage.getItem('token');\n          const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n            headers: {\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 5000\n          });\n          if (cavoResponse.data && cavoResponse.data.metratura_reale === metriPosati) {\n            console.log('I metri posati risultano aggiornati nonostante l\\'errore di comunicazione');\n            return cavoResponse.data;\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o i metri posati non sono stati aggiornati\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: 'Impossibile verificare se i metri posati sono stati aggiornati. Controlla lo stato del cavo prima di riprovare.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      throw error.response ? error.response.data : {\n        detail: error.message,\n        status: 500\n      };\n    }\n  },\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/bobina`);\n      console.log('ID Bobina:', idBobina);\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      }, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError || !error.response || error.code === 'ECONNABORTED' || error.message && error.message.includes('Network Error')) {\n        console.log('Errore di rete o timeout, verifica se la bobina è stata aggiornata...');\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se la bobina è stata aggiornata\n          const token = localStorage.getItem('token');\n          const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n            headers: {\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 5000\n          });\n          if (cavoResponse.data && cavoResponse.data.id_bobina === idBobina) {\n            console.log('La bobina risulta aggiornata nonostante l\\'errore di comunicazione');\n            return cavoResponse.data;\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o la bobina non è stata aggiornata\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: 'Impossibile verificare se la bobina è stata aggiornata. Controlla lo stato del cavo prima di riprovare.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      throw error.response ? error.response.data : {\n        detail: error.message,\n        status: 500\n      };\n    }\n  },\n  // Ottiene la lista dei cavi installati di un cantiere\n  getCaviInstallati: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene le statistiche dei cavi di un cantiere\n  getCaviStats: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/stats`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi stats error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene direttamente i cavi SPARE\n  getCaviSpare: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log('Caricamento cavi SPARE...');\n\n      // Prova prima con l'endpoint standard con tipo_cavo=3\n      console.log('URL API (standard):', `${API_URL}/cavi/${cantiereIdNum}?tipo_cavo=3`);\n      try {\n        const response = await axios.get(`${API_URL}/cavi/${cantiereIdNum}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000,\n          params: {\n            tipo_cavo: 3\n          }\n        });\n        console.log('Risposta getCaviSpare (standard):', response.data ? response.data.length : 0, 'cavi SPARE trovati');\n        if (response.data && response.data.length > 0) {\n          console.log('Primo cavo SPARE:', response.data[0]);\n        }\n        return response.data;\n      } catch (standardError) {\n        console.error('Errore con endpoint standard, tentativo con endpoint dedicato:', standardError);\n\n        // Se fallisce, prova con l'endpoint dedicato\n        console.log('URL API (dedicato):', `${API_URL}/cavi/spare/${cantiereIdNum}`);\n        const dedicatedResponse = await axios.get(`${API_URL}/cavi/spare/${cantiereIdNum}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Risposta getCaviSpare (dedicato):', dedicatedResponse.data ? dedicatedResponse.data.length : 0, 'cavi SPARE trovati');\n        if (dedicatedResponse.data && dedicatedResponse.data.length > 0) {\n          console.log('Primo cavo SPARE (dedicato):', dedicatedResponse.data[0]);\n        }\n        return dedicatedResponse.data;\n      }\n    } catch (error) {\n      console.error('Get cavi SPARE error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Collega un lato di un cavo\n  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/collegamento`);\n      console.log('Dati:', {\n        lato,\n        responsabile\n      });\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {\n        lato: lato,\n        responsabile: responsabile || 'cantiere'\n      }, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Collega cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      throw error.response ? error.response.data : {\n        detail: error.message,\n        status: 500\n      };\n    }\n  },\n  // Scollega un lato di un cavo\n  scollegaCavo: async (cantiereId, cavoId, lato) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta DELETE a /cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Scollega cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      throw error.response ? error.response.data : {\n        detail: error.message,\n        status: 500\n      };\n    }\n  },\n  // Verifica lo stato di un cavo specifico (debug)\n  debugCavo: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Ottieni i cavi attivi\n      console.log('Verificando cavo tra i cavi attivi...');\n      const attivi = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=0`, {\n        timeout: 60000 // 60 secondi\n      });\n      const cavoAttivo = attivi.data.find(c => c.id_cavo === cavoId);\n\n      // Ottieni i cavi SPARE\n      console.log('Verificando cavo tra i cavi SPARE...');\n      const spare = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=3`, {\n        timeout: 60000 // 60 secondi\n      });\n      const cavoSpare = spare.data.find(c => c.id_cavo === cavoId);\n      return {\n        trovato_tra_attivi: !!cavoAttivo,\n        trovato_tra_spare: !!cavoSpare,\n        cavo_attivo: cavoAttivo,\n        cavo_spare: cavoSpare\n      };\n    } catch (error) {\n      console.error('Debug cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      throw error.response ? error.response.data : {\n        detail: error.message,\n        status: 500\n      };\n    }\n  }\n};\nexport default caviService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "caviService", "get<PERSON><PERSON>", "cantiereId", "tipoCavo", "filters", "console", "log", "undefined", "error", "Error", "cantiereIdNum", "parseInt", "isNaN", "response", "get", "headers", "localStorage", "getItem", "timeout", "data", "spareError", "url", "queryParams", "push", "stato_installazione", "encodeURIComponent", "tipologia", "sort_by", "sort_order", "length", "join", "status", "Array", "isArray", "warn", "apiError", "_apiError$response", "_apiError$response2", "_apiError$response3", "_apiError$response4", "message", "statusText", "code", "isAxiosError", "method", "testResponse", "fetch", "testError", "_error$response", "_error$response2", "_error$response3", "_error$response4", "_error$response4$data", "_error$response5", "_error$response6", "stack", "includes", "enhancedError", "detail", "originalError", "createCavo", "cavoData", "JSON", "stringify", "post", "isNetworkError", "isTimeoutError", "Promise", "resolve", "setTimeout", "token", "checkResponse", "id_cavo", "exists", "cavoResponse", "verifyError", "customMessage", "errorDetail", "getCavoById", "cavoId", "updateCavo", "pingResponse", "ok", "pingError", "put", "request", "isUpdated", "key", "getRevisioneCorrente", "revisione_corrente", "markCavoAsSpare", "force", "postResponse", "modificato_manualmente", "postError", "deleteResponse", "delete", "params", "mode", "_error$response7", "_error$response8", "_error$response9", "deleteCavo", "requestConfig", "_error$response0", "_error$response1", "_error$response10", "updateMetri<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_posati", "metratura_reale", "updateBobina", "idBobina", "id_bobina", "getCaviInstallati", "getCaviStats", "getCaviSpare", "tipo_cavo", "standardError", "dedicatedResponse", "collegaCavo", "lato", "responsabile", "scollegaCavo", "debugCavo", "attivi", "cavoAttivo", "find", "c", "spare", "cavoSpare", "trovato_tra_attivi", "trovato_tra_spare", "cavo_attivo", "cavo_spare"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/caviService.js"], "sourcesContent": ["import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\n\nconst API_URL = config.API_URL;\n\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null, filters = {}) => {\n    try {\n      console.log('getCavi chiamato con:', { cantiereId, tipoCavo, filters });\n      console.log('Tipo di cantiereId:', typeof cantiereId);\n\n      // Verifica che cantiereId sia definito\n      if (cantiereId === undefined || cantiereId === null) {\n        console.error('cantiereId è undefined o null');\n        throw new Error('ID cantiere mancante');\n      }\n\n      // Assicurati che cantiereId sia un numero\n      let cantiereIdNum = cantiereId;\n      if (typeof cantiereId === 'string') {\n        cantiereIdNum = parseInt(cantiereId, 10);\n        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);\n      }\n\n      if (isNaN(cantiereIdNum)) {\n        console.error('ID cantiere non è un numero valido:', cantiereId);\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log(`Caricamento cavi per cantiere ${cantiereIdNum} con tipo_cavo=${tipoCavo}`);\n\n      // Soluzione alternativa per i cavi SPARE\n      if (tipoCavo === 3) {\n        console.log('Caricamento cavi SPARE con query diretta...');\n        try {\n          // Usa una query SQL diretta per ottenere i cavi SPARE\n          const response = await axios.get(\n            `${API_URL}/cavi/spare/${cantiereIdNum}`,\n            {\n              headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${localStorage.getItem('token')}`\n              },\n              timeout: 30000\n            }\n          );\n\n          console.log('Risposta cavi SPARE:', response.data);\n          return response.data;\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi SPARE:', spareError);\n          // Se fallisce, continua con il metodo standard\n        }\n      }\n\n      // Costruisci l'URL con i parametri di query\n      let url = `/cavi/${cantiereIdNum}`;\n      const queryParams = [];\n\n      if (tipoCavo !== null) {\n        queryParams.push(`tipo_cavo=${tipoCavo}`);\n      }\n\n      // Aggiungi filtri aggiuntivi se presenti\n      if (filters.stato_installazione) {\n        queryParams.push(`stato_installazione=${encodeURIComponent(filters.stato_installazione)}`);\n      }\n\n      if (filters.tipologia) {\n        queryParams.push(`tipologia=${encodeURIComponent(filters.tipologia)}`);\n      }\n\n      if (filters.sort_by) {\n        queryParams.push(`sort_by=${encodeURIComponent(filters.sort_by)}`);\n        if (filters.sort_order) {\n          queryParams.push(`sort_order=${encodeURIComponent(filters.sort_order)}`);\n        }\n      }\n\n      // Aggiungi i parametri di query all'URL\n      if (queryParams.length > 0) {\n        url += `?${queryParams.join('&')}`;\n      }\n\n      // Log dettagliato dell'URL e dei parametri\n      console.log('URL API completo:', url);\n      console.log('Parametri di query:', queryParams);\n\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');\n      console.log('URL completo:', `${API_URL}${url}`);\n\n      try {\n        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);\n        console.log('Headers della richiesta:', {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        });\n\n        // Aggiungi un timeout più lungo per la richiesta\n        const response = await axiosInstance.get(url, { timeout: 60000 });\n\n        console.log(`Risposta API: ${url}`, response.data);\n        console.log('Status della risposta:', response.status);\n        console.log('Headers della risposta:', response.headers);\n\n        if (Array.isArray(response.data)) {\n          console.log(`Numero di cavi ricevuti: ${response.data.length}`);\n          if (response.data.length > 0) {\n            console.log('Primo cavo ricevuto:', response.data[0]);\n          } else {\n            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);\n          }\n        } else {\n          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);\n        }\n\n        return response.data;\n      } catch (apiError) {\n        console.error(`Errore nella chiamata API GET ${url}:`, apiError);\n        console.error('Dettagli errore API:', {\n          message: apiError.message,\n          status: apiError.response?.status,\n          statusText: apiError.response?.statusText,\n          data: apiError.response?.data,\n          headers: apiError.response?.headers,\n          code: apiError.code,\n          isAxiosError: apiError.isAxiosError,\n          config: apiError.config ? {\n            url: apiError.config.url,\n            method: apiError.config.method,\n            timeout: apiError.config.timeout,\n            headers: apiError.config.headers\n          } : 'No config'\n        });\n\n        // Gestione specifica per errori di rete\n        if (apiError.code === 'ERR_NETWORK') {\n          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');\n          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile\n          try {\n            console.log('Tentativo di test di connessione al backend...');\n            const testResponse = await fetch(API_URL);\n            console.log('Test di connessione al backend:', testResponse.status);\n          } catch (testError) {\n            console.error('Test di connessione al backend fallito:', testError);\n          }\n        }\n\n        throw apiError;\n      }\n    } catch (error) {\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,\n        stack: error.stack\n      });\n\n      // Verifica se l'errore è dovuto a un problema di connessione\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout') || error.message.includes('Network Error')) {\n        console.error('Errore di connessione o timeout');\n        // Ritorna un array vuoto invece di lanciare un errore\n        console.log('Ritorno array vuoto come fallback');\n        return [];\n      }\n\n      // Crea un errore più informativo\n      const enhancedError = new Error(error.response?.data?.detail || error.message || 'Errore sconosciuto');\n      enhancedError.status = error.response?.status;\n      enhancedError.data = error.response?.data;\n      enhancedError.response = error.response;\n      enhancedError.originalError = error;\n      enhancedError.code = error.code;\n      enhancedError.isAxiosError = error.isAxiosError;\n\n      throw enhancedError;\n    }\n  },\n\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Tentativo di creazione cavo per cantiere ${cantiereIdNum}`);\n      console.log('Dati inviati:', JSON.stringify(cavoData, null, 2));\n\n      // Invia la richiesta al server\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}`, cavoData, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta del server:', response.status, response.statusText);\n      console.log('Dati ricevuti:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError ||\n          !error.response || error.code === 'ECONNABORTED' ||\n          (error.message && error.message.includes('Network Error'))) {\n\n        console.log('Errore di rete o timeout, verifica se il cavo è stato creato...');\n\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste nel database\n          const token = localStorage.getItem('token');\n          const checkResponse = await axios.get(\n            `${API_URL}/cavi/${cantiereIdNum}/check/${cavoData.id_cavo}`,\n            {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 5000\n            }\n          );\n\n          if (checkResponse.data && checkResponse.data.exists) {\n            console.log('Il cavo risulta creato nonostante l\\'errore di comunicazione');\n            // Recupera i dati del cavo\n            const cavoResponse = await axios.get(\n              `${API_URL}/cavi/${cantiereIdNum}/${cavoData.id_cavo}`,\n              {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 5000\n              }\n            );\n\n            if (cavoResponse.data) {\n              console.log('Dati del cavo recuperati:', cavoResponse.data);\n              return cavoResponse.data;\n            }\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o il cavo non esiste\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\n      }\n\n      if (error.response) {\n        console.error('Dettagli errore:', error.response.data);\n        console.error('Status errore:', error.response.status);\n        console.error('Headers errore:', error.response.headers);\n\n        // Formatta il messaggio di errore in modo più leggibile\n        const errorDetail = error.response.data.detail || 'Errore sconosciuto';\n        throw { detail: errorDetail, status: error.response.status };\n      }\n      // Se è un errore di validazione locale, formatta il messaggio\n      if (error instanceof Error) {\n        throw { detail: error.message, status: 400 };\n      }\n      throw error;\n    }\n  },\n\n  // Ottiene un cavo specifico per ID\n  getCavoById: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/${cavoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavo by ID error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta PUT a /cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Dati inviati:', cavoData);\n\n      // Verifica che il backend sia raggiungibile\n      try {\n        console.log('Verifica connessione al backend...');\n        const pingResponse = await fetch(`${API_URL}/health`, { method: 'GET' });\n        console.log('Ping al backend:', pingResponse.status, pingResponse.statusText);\n        if (!pingResponse.ok) {\n          console.error('Il server non risponde correttamente:', pingResponse.status);\n          throw new Error('Il server non risponde correttamente. Riprova più tardi.');\n        }\n      } catch (pingError) {\n        console.error('Errore durante il ping al backend:', pingError);\n        throw new Error('Impossibile connettersi al server. Verifica la connessione di rete e riprova.');\n      }\n\n      // Imposta un timeout più lungo per la richiesta\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData, {\n        timeout: 90000, // 90 secondi (timeout esteso)\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError ||\n          !error.response || error.code === 'ECONNABORTED' ||\n          (error.message && error.message.includes('Network Error')) ||\n          error.request) {\n\n        console.log('Errore di rete o timeout, verifica se il cavo è stato aggiornato...');\n\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se è stato aggiornato\n          const token = localStorage.getItem('token');\n          const cavoResponse = await axios.get(\n            `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n            {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 5000\n            }\n          );\n\n          if (cavoResponse.data) {\n            console.log('Cavo trovato nel database, verifica se è stato aggiornato');\n\n            // Verifica se almeno uno dei campi è stato aggiornato\n            let isUpdated = false;\n            for (const key in cavoData) {\n              if (cavoData[key] !== undefined &&\n                  JSON.stringify(cavoData[key]) === JSON.stringify(cavoResponse.data[key])) {\n                console.log(`Campo ${key} risulta aggiornato: ${cavoData[key]}`);\n                isUpdated = true;\n                break;\n              }\n            }\n\n            if (isUpdated) {\n              console.log('Il cavo risulta aggiornato nonostante l\\'errore di comunicazione');\n              return cavoResponse.data;\n            } else {\n              console.log('Il cavo esiste ma non risulta aggiornato');\n            }\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o il cavo non è stato aggiornato\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: 'Impossibile verificare se la modifica è stata salvata. Controlla lo stato del cavo prima di riprovare.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n\n      // Gestione più dettagliata dell'errore\n      if (error.response) {\n        // Il server ha risposto con un codice di stato diverso da 2xx\n        console.error('Errore dal server:', error.response.status, error.response.statusText);\n        console.error('Dati errore:', error.response.data);\n        throw error.response.data;\n      } else {\n        // Si è verificato un errore durante l'impostazione della richiesta\n        console.error('Errore durante l\\'impostazione della richiesta:', error.message);\n        throw { detail: error.message, status: 500 };\n      }\n    }\n  },\n\n  // Ottiene la revisione corrente del cantiere\n  getRevisioneCorrente: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);\n      return response.data.revisione_corrente;\n    } catch (error) {\n      console.error('Get revisione corrente error:', error);\n      return '00'; // Valore di default in caso di errore\n    }\n  },\n\n  // Marca un cavo come SPARE\n  markCavoAsSpare: async (cantiereId, cavoId, force = false) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di marcare cavo come SPARE:', { cantiereId: cantiereIdNum, cavoId, force });\n\n      // Prova prima con l'endpoint POST specifico\n      console.log('URL API (POST):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`);\n\n      try {\n        const postResponse = await axios.post(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`,\n          { force: force },\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Risposta markCavoAsSpare (POST):', postResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE...');\n        const cavoResponse = await axios.get(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Stato del cavo dopo marcatura:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n\n        return cavoResponse.data;\n      } catch (postError) {\n        // Se fallisce il POST, prova con DELETE mode=spare\n        console.error('Errore con endpoint POST, tentativo con DELETE mode=spare:', postError);\n        console.log('URL API (DELETE):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}?mode=spare`);\n\n        const deleteResponse = await axios.delete(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000,\n            params: { mode: 'spare' }\n          }\n        );\n\n        console.log('Risposta markCavoAsSpare (DELETE mode=spare):', deleteResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE con DELETE...');\n        const cavoResponse = await axios.get(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Stato del cavo dopo marcatura con DELETE:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n\n        return cavoResponse.data;\n      }\n    } catch (error) {\n      console.error('Mark cavo as SPARE error:', error);\n      console.error('Dettagli errore:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina un cavo o lo marca come SPARE\n  deleteCavo: async (cantiereId, cavoId, mode = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di eliminare/marcare cavo:', { cantiereId: cantiereIdNum, cavoId, mode });\n\n      // Se è specificata la modalità, aggiungi il parametro alla richiesta\n      const requestConfig = {\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        timeout: 30000, // Timeout aumentato a 30 secondi\n        params: mode ? { mode } : {}\n      };\n\n      console.log('URL API:', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Config:', requestConfig);\n\n      // Usa axios direttamente invece di axiosInstance per avere più controllo\n      const response = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, requestConfig);\n      console.log('Risposta deleteCavo:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cavo error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n\n      // Crea un errore più informativo\n      if (error.response && error.response.data) {\n        throw error.response.data;\n      } else if (error.message) {\n        throw new Error(error.message);\n      } else {\n        throw new Error('Errore durante l\\'eliminazione del cavo');\n      }\n    }\n  },\n\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/metri-posati`);\n      console.log('Metri posati:', metriPosati);\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      }, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError ||\n          !error.response || error.code === 'ECONNABORTED' ||\n          (error.message && error.message.includes('Network Error'))) {\n\n        console.log('Errore di rete o timeout, verifica se i metri posati sono stati aggiornati...');\n\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se i metri posati sono stati aggiornati\n          const token = localStorage.getItem('token');\n          const cavoResponse = await axios.get(\n            `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n            {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 5000\n            }\n          );\n\n          if (cavoResponse.data && cavoResponse.data.metratura_reale === metriPosati) {\n            console.log('I metri posati risultano aggiornati nonostante l\\'errore di comunicazione');\n            return cavoResponse.data;\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o i metri posati non sono stati aggiornati\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: 'Impossibile verificare se i metri posati sono stati aggiornati. Controlla lo stato del cavo prima di riprovare.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n\n      throw error.response ? error.response.data : { detail: error.message, status: 500 };\n    }\n  },\n\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/bobina`);\n      console.log('ID Bobina:', idBobina);\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      }, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n\n      // Verifica se è un errore di rete o timeout\n      if (error.isNetworkError || error.isTimeoutError ||\n          !error.response || error.code === 'ECONNABORTED' ||\n          (error.message && error.message.includes('Network Error'))) {\n\n        console.log('Errore di rete o timeout, verifica se la bobina è stata aggiornata...');\n\n        try {\n          // Attendi un secondo prima di verificare\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // Verifica se il cavo esiste e se la bobina è stata aggiornata\n          const token = localStorage.getItem('token');\n          const cavoResponse = await axios.get(\n            `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n            {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 5000\n            }\n          );\n\n          if (cavoResponse.data && cavoResponse.data.id_bobina === idBobina) {\n            console.log('La bobina risulta aggiornata nonostante l\\'errore di comunicazione');\n            return cavoResponse.data;\n          }\n        } catch (verifyError) {\n          console.error('Errore durante la verifica post-errore:', verifyError);\n        }\n\n        // Se arriviamo qui, non siamo riusciti a verificare o la bobina non è stata aggiornata\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: 'Impossibile verificare se la bobina è stata aggiornata. Controlla lo stato del cavo prima di riprovare.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n\n      throw error.response ? error.response.data : { detail: error.message, status: 500 };\n    }\n  },\n\n  // Ottiene la lista dei cavi installati di un cantiere\n  getCaviInstallati: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene le statistiche dei cavi di un cantiere\n  getCaviStats: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/stats`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi stats error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene direttamente i cavi SPARE\n  getCaviSpare: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log('Caricamento cavi SPARE...');\n\n      // Prova prima con l'endpoint standard con tipo_cavo=3\n      console.log('URL API (standard):', `${API_URL}/cavi/${cantiereIdNum}?tipo_cavo=3`);\n\n      try {\n        const response = await axios.get(\n          `${API_URL}/cavi/${cantiereIdNum}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000,\n            params: { tipo_cavo: 3 }\n          }\n        );\n\n        console.log('Risposta getCaviSpare (standard):', response.data ? response.data.length : 0, 'cavi SPARE trovati');\n        if (response.data && response.data.length > 0) {\n          console.log('Primo cavo SPARE:', response.data[0]);\n        }\n\n        return response.data;\n      } catch (standardError) {\n        console.error('Errore con endpoint standard, tentativo con endpoint dedicato:', standardError);\n\n        // Se fallisce, prova con l'endpoint dedicato\n        console.log('URL API (dedicato):', `${API_URL}/cavi/spare/${cantiereIdNum}`);\n\n        const dedicatedResponse = await axios.get(\n          `${API_URL}/cavi/spare/${cantiereIdNum}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Risposta getCaviSpare (dedicato):', dedicatedResponse.data ? dedicatedResponse.data.length : 0, 'cavi SPARE trovati');\n        if (dedicatedResponse.data && dedicatedResponse.data.length > 0) {\n          console.log('Primo cavo SPARE (dedicato):', dedicatedResponse.data[0]);\n        }\n\n        return dedicatedResponse.data;\n      }\n    } catch (error) {\n      console.error('Get cavi SPARE error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Collega un lato di un cavo\n  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/collegamento`);\n      console.log('Dati:', { lato, responsabile });\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {\n        lato: lato,\n        responsabile: responsabile || 'cantiere'\n      }, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Collega cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\n      }\n\n      throw error.response ? error.response.data : { detail: error.message, status: 500 };\n    }\n  },\n\n  // Scollega un lato di un cavo\n  scollegaCavo: async (cantiereId, cavoId, lato) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta DELETE a /cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);\n\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Scollega cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\n      }\n\n      throw error.response ? error.response.data : { detail: error.message, status: 500 };\n    }\n  },\n\n  // Verifica lo stato di un cavo specifico (debug)\n  debugCavo: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Ottieni i cavi attivi\n      console.log('Verificando cavo tra i cavi attivi...');\n      const attivi = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=0`, {\n        timeout: 60000, // 60 secondi\n      });\n      const cavoAttivo = attivi.data.find(c => c.id_cavo === cavoId);\n\n      // Ottieni i cavi SPARE\n      console.log('Verificando cavo tra i cavi SPARE...');\n      const spare = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=3`, {\n        timeout: 60000, // 60 secondi\n      });\n      const cavoSpare = spare.data.find(c => c.id_cavo === cavoId);\n\n      return {\n        trovato_tra_attivi: !!cavoAttivo,\n        trovato_tra_spare: !!cavoSpare,\n        cavo_attivo: cavoAttivo,\n        cavo_spare: cavoSpare\n      };\n    } catch (error) {\n      console.error('Debug cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\n      }\n\n      throw error.response ? error.response.data : { detail: error.message, status: 500 };\n    }\n  }\n};\n\nexport default caviService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;AAEzC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;AAE9B,MAAMC,WAAW,GAAG;EAClB;EACAC,OAAO,EAAE,MAAAA,CAAOC,UAAU,EAAEC,QAAQ,GAAG,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC5D,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QAAEJ,UAAU;QAAEC,QAAQ;QAAEC;MAAQ,CAAC,CAAC;MACvEC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,OAAOJ,UAAU,CAAC;;MAErD;MACA,IAAIA,UAAU,KAAKK,SAAS,IAAIL,UAAU,KAAK,IAAI,EAAE;QACnDG,OAAO,CAACG,KAAK,CAAC,+BAA+B,CAAC;QAC9C,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;MACzC;;MAEA;MACA,IAAIC,aAAa,GAAGR,UAAU;MAC9B,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;QAClCQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;QACxCG,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEI,aAAa,CAAC;MAC1E;MAEA,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;QACxBL,OAAO,CAACG,KAAK,CAAC,qCAAqC,EAAEN,UAAU,CAAC;QAChE,MAAM,IAAIO,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,iCAAiCI,aAAa,kBAAkBP,QAAQ,EAAE,CAAC;;MAEvF;MACA,IAAIA,QAAQ,KAAK,CAAC,EAAE;QAClBE,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1D,IAAI;UACF;UACA,MAAMO,QAAQ,GAAG,MAAMjB,KAAK,CAACkB,GAAG,CAC9B,GAAGf,OAAO,eAAeW,aAAa,EAAE,EACxC;YACEK,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC1D,CAAC;YACDC,OAAO,EAAE;UACX,CACF,CAAC;UAEDb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,QAAQ,CAACM,IAAI,CAAC;UAClD,OAAON,QAAQ,CAACM,IAAI;QACtB,CAAC,CAAC,OAAOC,UAAU,EAAE;UACnBf,OAAO,CAACG,KAAK,CAAC,wCAAwC,EAAEY,UAAU,CAAC;UACnE;QACF;MACF;;MAEA;MACA,IAAIC,GAAG,GAAG,SAASX,aAAa,EAAE;MAClC,MAAMY,WAAW,GAAG,EAAE;MAEtB,IAAInB,QAAQ,KAAK,IAAI,EAAE;QACrBmB,WAAW,CAACC,IAAI,CAAC,aAAapB,QAAQ,EAAE,CAAC;MAC3C;;MAEA;MACA,IAAIC,OAAO,CAACoB,mBAAmB,EAAE;QAC/BF,WAAW,CAACC,IAAI,CAAC,uBAAuBE,kBAAkB,CAACrB,OAAO,CAACoB,mBAAmB,CAAC,EAAE,CAAC;MAC5F;MAEA,IAAIpB,OAAO,CAACsB,SAAS,EAAE;QACrBJ,WAAW,CAACC,IAAI,CAAC,aAAaE,kBAAkB,CAACrB,OAAO,CAACsB,SAAS,CAAC,EAAE,CAAC;MACxE;MAEA,IAAItB,OAAO,CAACuB,OAAO,EAAE;QACnBL,WAAW,CAACC,IAAI,CAAC,WAAWE,kBAAkB,CAACrB,OAAO,CAACuB,OAAO,CAAC,EAAE,CAAC;QAClE,IAAIvB,OAAO,CAACwB,UAAU,EAAE;UACtBN,WAAW,CAACC,IAAI,CAAC,cAAcE,kBAAkB,CAACrB,OAAO,CAACwB,UAAU,CAAC,EAAE,CAAC;QAC1E;MACF;;MAEA;MACA,IAAIN,WAAW,CAACO,MAAM,GAAG,CAAC,EAAE;QAC1BR,GAAG,IAAI,IAAIC,WAAW,CAACQ,IAAI,CAAC,GAAG,CAAC,EAAE;MACpC;;MAEA;MACAzB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEe,GAAG,CAAC;MACrChB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEgB,WAAW,CAAC;MAE/CjB,OAAO,CAACC,GAAG,CAAC,qBAAqBe,GAAG,EAAE,CAAC;MACvChB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEU,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,UAAU,CAAC;MAC9EZ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,GAAGP,OAAO,GAAGsB,GAAG,EAAE,CAAC;MAEhD,IAAI;QACFhB,OAAO,CAACC,GAAG,CAAC,kCAAkCe,GAAG,eAAeL,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,UAAU,EAAE,CAAC;QAC1HZ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;UACtC,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUU,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC,CAAC;;QAEF;QACA,MAAMJ,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAACO,GAAG,EAAE;UAAEH,OAAO,EAAE;QAAM,CAAC,CAAC;QAEjEb,OAAO,CAACC,GAAG,CAAC,iBAAiBe,GAAG,EAAE,EAAER,QAAQ,CAACM,IAAI,CAAC;QAClDd,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEO,QAAQ,CAACkB,MAAM,CAAC;QACtD1B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEO,QAAQ,CAACE,OAAO,CAAC;QAExD,IAAIiB,KAAK,CAACC,OAAO,CAACpB,QAAQ,CAACM,IAAI,CAAC,EAAE;UAChCd,OAAO,CAACC,GAAG,CAAC,4BAA4BO,QAAQ,CAACM,IAAI,CAACU,MAAM,EAAE,CAAC;UAC/D,IAAIhB,QAAQ,CAACM,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;YAC5BxB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,QAAQ,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,MAAM;YACLd,OAAO,CAAC6B,IAAI,CAAC,uCAAuCxB,aAAa,aAAaP,QAAQ,EAAE,CAAC;UAC3F;QACF,CAAC,MAAM;UACLE,OAAO,CAAC6B,IAAI,CAAC,4BAA4B,OAAOrB,QAAQ,CAACM,IAAI,EAAE,EAAEN,QAAQ,CAACM,IAAI,CAAC;QACjF;QAEA,OAAON,QAAQ,CAACM,IAAI;MACtB,CAAC,CAAC,OAAOgB,QAAQ,EAAE;QAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;QACjBlC,OAAO,CAACG,KAAK,CAAC,iCAAiCa,GAAG,GAAG,EAAEc,QAAQ,CAAC;QAChE9B,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAE;UACpCgC,OAAO,EAAEL,QAAQ,CAACK,OAAO;UACzBT,MAAM,GAAAK,kBAAA,GAAED,QAAQ,CAACtB,QAAQ,cAAAuB,kBAAA,uBAAjBA,kBAAA,CAAmBL,MAAM;UACjCU,UAAU,GAAAJ,mBAAA,GAAEF,QAAQ,CAACtB,QAAQ,cAAAwB,mBAAA,uBAAjBA,mBAAA,CAAmBI,UAAU;UACzCtB,IAAI,GAAAmB,mBAAA,GAAEH,QAAQ,CAACtB,QAAQ,cAAAyB,mBAAA,uBAAjBA,mBAAA,CAAmBnB,IAAI;UAC7BJ,OAAO,GAAAwB,mBAAA,GAAEJ,QAAQ,CAACtB,QAAQ,cAAA0B,mBAAA,uBAAjBA,mBAAA,CAAmBxB,OAAO;UACnC2B,IAAI,EAAEP,QAAQ,CAACO,IAAI;UACnBC,YAAY,EAAER,QAAQ,CAACQ,YAAY;UACnC9C,MAAM,EAAEsC,QAAQ,CAACtC,MAAM,GAAG;YACxBwB,GAAG,EAAEc,QAAQ,CAACtC,MAAM,CAACwB,GAAG;YACxBuB,MAAM,EAAET,QAAQ,CAACtC,MAAM,CAAC+C,MAAM;YAC9B1B,OAAO,EAAEiB,QAAQ,CAACtC,MAAM,CAACqB,OAAO;YAChCH,OAAO,EAAEoB,QAAQ,CAACtC,MAAM,CAACkB;UAC3B,CAAC,GAAG;QACN,CAAC,CAAC;;QAEF;QACA,IAAIoB,QAAQ,CAACO,IAAI,KAAK,aAAa,EAAE;UACnCrC,OAAO,CAACG,KAAK,CAAC,0EAA0E,CAAC;UACzF;UACA,IAAI;YACFH,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;YAC7D,MAAMuC,YAAY,GAAG,MAAMC,KAAK,CAAC/C,OAAO,CAAC;YACzCM,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEuC,YAAY,CAACd,MAAM,CAAC;UACrE,CAAC,CAAC,OAAOgB,SAAS,EAAE;YAClB1C,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEuC,SAAS,CAAC;UACrE;QACF;QAEA,MAAMZ,QAAQ;MAChB;IACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MAAA,IAAAwC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdjD,OAAO,CAACG,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCH,OAAO,CAACG,KAAK,CAAC,gBAAgB,EAAE;QAC9BgC,OAAO,EAAEhC,KAAK,CAACgC,OAAO;QACtBT,MAAM,GAAAiB,eAAA,GAAExC,KAAK,CAACK,QAAQ,cAAAmC,eAAA,uBAAdA,eAAA,CAAgBjB,MAAM;QAC9BU,UAAU,GAAAQ,gBAAA,GAAEzC,KAAK,CAACK,QAAQ,cAAAoC,gBAAA,uBAAdA,gBAAA,CAAgBR,UAAU;QACtCtB,IAAI,GAAA+B,gBAAA,GAAE1C,KAAK,CAACK,QAAQ,cAAAqC,gBAAA,uBAAdA,gBAAA,CAAgB/B,IAAI;QAC1BE,GAAG,EAAE,SAASnB,UAAU,GAAGC,QAAQ,KAAK,IAAI,GAAG,cAAcA,QAAQ,EAAE,GAAG,EAAE,EAAE;QAC9EoD,KAAK,EAAE/C,KAAK,CAAC+C;MACf,CAAC,CAAC;;MAEF;MACA,IAAI/C,KAAK,CAACkC,IAAI,KAAK,cAAc,IAAIlC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,SAAS,CAAC,IAAIhD,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAC,EAAE;QACjHnD,OAAO,CAACG,KAAK,CAAC,iCAAiC,CAAC;QAChD;QACAH,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,OAAO,EAAE;MACX;;MAEA;MACA,MAAMmD,aAAa,GAAG,IAAIhD,KAAK,CAAC,EAAA0C,gBAAA,GAAA3C,KAAK,CAACK,QAAQ,cAAAsC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsBM,MAAM,KAAIlD,KAAK,CAACgC,OAAO,IAAI,oBAAoB,CAAC;MACtGiB,aAAa,CAAC1B,MAAM,IAAAsB,gBAAA,GAAG7C,KAAK,CAACK,QAAQ,cAAAwC,gBAAA,uBAAdA,gBAAA,CAAgBtB,MAAM;MAC7C0B,aAAa,CAACtC,IAAI,IAAAmC,gBAAA,GAAG9C,KAAK,CAACK,QAAQ,cAAAyC,gBAAA,uBAAdA,gBAAA,CAAgBnC,IAAI;MACzCsC,aAAa,CAAC5C,QAAQ,GAAGL,KAAK,CAACK,QAAQ;MACvC4C,aAAa,CAACE,aAAa,GAAGnD,KAAK;MACnCiD,aAAa,CAACf,IAAI,GAAGlC,KAAK,CAACkC,IAAI;MAC/Be,aAAa,CAACd,YAAY,GAAGnC,KAAK,CAACmC,YAAY;MAE/C,MAAMc,aAAa;IACrB;EACF,CAAC;EAED;EACAG,UAAU,EAAE,MAAAA,CAAO1D,UAAU,EAAE2D,QAAQ,KAAK;IAC1C,IAAI;MACF;MACA,MAAMnD,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,4CAA4CI,aAAa,EAAE,CAAC;MACxEL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEwD,IAAI,CAACC,SAAS,CAACF,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAE/D;MACA,MAAMhD,QAAQ,GAAG,MAAMf,aAAa,CAACkE,IAAI,CAAC,SAAStD,aAAa,EAAE,EAAEmD,QAAQ,EAAE;QAC5E3C,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,QAAQ,CAACkB,MAAM,EAAElB,QAAQ,CAAC4B,UAAU,CAAC;MACzEpC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAC5C,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;;MAE1C;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,IAC5C,CAAC1D,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACkC,IAAI,KAAK,cAAc,IAC/ClC,KAAK,CAACgC,OAAO,IAAIhC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAE,EAAE;QAE9DnD,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;QAE9E,IAAI;UACF;UACA,MAAM,IAAI6D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEvD;UACA,MAAME,KAAK,GAAGtD,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,MAAMsD,aAAa,GAAG,MAAM3E,KAAK,CAACkB,GAAG,CACnC,GAAGf,OAAO,SAASW,aAAa,UAAUmD,QAAQ,CAACW,OAAO,EAAE,EAC5D;YACEzD,OAAO,EAAE;cACP,eAAe,EAAE,UAAUuD,KAAK;YAClC,CAAC;YACDpD,OAAO,EAAE;UACX,CACF,CAAC;UAED,IAAIqD,aAAa,CAACpD,IAAI,IAAIoD,aAAa,CAACpD,IAAI,CAACsD,MAAM,EAAE;YACnDpE,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;YAC3E;YACA,MAAMoE,YAAY,GAAG,MAAM9E,KAAK,CAACkB,GAAG,CAClC,GAAGf,OAAO,SAASW,aAAa,IAAImD,QAAQ,CAACW,OAAO,EAAE,EACtD;cACEzD,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUuD,KAAK;cAClC,CAAC;cACDpD,OAAO,EAAE;YACX,CACF,CAAC;YAED,IAAIwD,YAAY,CAACvD,IAAI,EAAE;cACrBd,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEoE,YAAY,CAACvD,IAAI,CAAC;cAC3D,OAAOuD,YAAY,CAACvD,IAAI;YAC1B;UACF;QACF,CAAC,CAAC,OAAOwD,WAAW,EAAE;UACpBtE,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEmE,WAAW,CAAC;QACvE;;QAEA;QACAtE,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACoE,aAAa,IAAIpE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACoE,aAAa,IAAI,+EAA+E;UAAE7C,MAAM,EAAE,CAAC;UAAEkC,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,IAAIzD,KAAK,CAACK,QAAQ,EAAE;QAClBR,OAAO,CAACG,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAACK,QAAQ,CAACM,IAAI,CAAC;QACtDd,OAAO,CAACG,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACK,QAAQ,CAACkB,MAAM,CAAC;QACtD1B,OAAO,CAACG,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAACK,QAAQ,CAACE,OAAO,CAAC;;QAExD;QACA,MAAM8D,WAAW,GAAGrE,KAAK,CAACK,QAAQ,CAACM,IAAI,CAACuC,MAAM,IAAI,oBAAoB;QACtE,MAAM;UAAEA,MAAM,EAAEmB,WAAW;UAAE9C,MAAM,EAAEvB,KAAK,CAACK,QAAQ,CAACkB;QAAO,CAAC;MAC9D;MACA;MACA,IAAIvB,KAAK,YAAYC,KAAK,EAAE;QAC1B,MAAM;UAAEiD,MAAM,EAAElD,KAAK,CAACgC,OAAO;UAAET,MAAM,EAAE;QAAI,CAAC;MAC9C;MACA,MAAMvB,KAAK;IACb;EACF,CAAC;EAED;EACAsE,WAAW,EAAE,MAAAA,CAAO5E,UAAU,EAAE6E,MAAM,KAAK;IACzC,IAAI;MACF;MACA,MAAMrE,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMW,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,IAAIqE,MAAM,EAAE,CAAC;MAC5E,OAAOlE,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACAwE,UAAU,EAAE,MAAAA,CAAO9E,UAAU,EAAE6E,MAAM,EAAElB,QAAQ,KAAK;IAClD,IAAI;MACF;MACA,MAAMnD,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,kCAAkCI,aAAa,IAAIqE,MAAM,EAAE,CAAC;MACxE1E,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEuD,QAAQ,CAAC;;MAEtC;MACA,IAAI;QACFxD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD,MAAM2E,YAAY,GAAG,MAAMnC,KAAK,CAAC,GAAG/C,OAAO,SAAS,EAAE;UAAE6C,MAAM,EAAE;QAAM,CAAC,CAAC;QACxEvC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE2E,YAAY,CAAClD,MAAM,EAAEkD,YAAY,CAACxC,UAAU,CAAC;QAC7E,IAAI,CAACwC,YAAY,CAACC,EAAE,EAAE;UACpB7E,OAAO,CAACG,KAAK,CAAC,uCAAuC,EAAEyE,YAAY,CAAClD,MAAM,CAAC;UAC3E,MAAM,IAAItB,KAAK,CAAC,0DAA0D,CAAC;QAC7E;MACF,CAAC,CAAC,OAAO0E,SAAS,EAAE;QAClB9E,OAAO,CAACG,KAAK,CAAC,oCAAoC,EAAE2E,SAAS,CAAC;QAC9D,MAAM,IAAI1E,KAAK,CAAC,+EAA+E,CAAC;MAClG;;MAEA;MACA,MAAMI,QAAQ,GAAG,MAAMf,aAAa,CAACsF,GAAG,CAAC,SAAS1E,aAAa,IAAIqE,MAAM,EAAE,EAAElB,QAAQ,EAAE;QACrF3C,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;;MAE1C;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,IAC5C,CAAC1D,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACkC,IAAI,KAAK,cAAc,IAC/ClC,KAAK,CAACgC,OAAO,IAAIhC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAE,IAC1DhD,KAAK,CAAC6E,OAAO,EAAE;QAEjBhF,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;QAElF,IAAI;UACF;UACA,MAAM,IAAI6D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEvD;UACA,MAAME,KAAK,GAAGtD,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,MAAMyD,YAAY,GAAG,MAAM9E,KAAK,CAACkB,GAAG,CAClC,GAAGf,OAAO,SAASW,aAAa,IAAIqE,MAAM,EAAE,EAC5C;YACEhE,OAAO,EAAE;cACP,eAAe,EAAE,UAAUuD,KAAK;YAClC,CAAC;YACDpD,OAAO,EAAE;UACX,CACF,CAAC;UAED,IAAIwD,YAAY,CAACvD,IAAI,EAAE;YACrBd,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;;YAExE;YACA,IAAIgF,SAAS,GAAG,KAAK;YACrB,KAAK,MAAMC,GAAG,IAAI1B,QAAQ,EAAE;cAC1B,IAAIA,QAAQ,CAAC0B,GAAG,CAAC,KAAKhF,SAAS,IAC3BuD,IAAI,CAACC,SAAS,CAACF,QAAQ,CAAC0B,GAAG,CAAC,CAAC,KAAKzB,IAAI,CAACC,SAAS,CAACW,YAAY,CAACvD,IAAI,CAACoE,GAAG,CAAC,CAAC,EAAE;gBAC5ElF,OAAO,CAACC,GAAG,CAAC,SAASiF,GAAG,wBAAwB1B,QAAQ,CAAC0B,GAAG,CAAC,EAAE,CAAC;gBAChED,SAAS,GAAG,IAAI;gBAChB;cACF;YACF;YAEA,IAAIA,SAAS,EAAE;cACbjF,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;cAC/E,OAAOoE,YAAY,CAACvD,IAAI;YAC1B,CAAC,MAAM;cACLd,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;YACzD;UACF;QACF,CAAC,CAAC,OAAOqE,WAAW,EAAE;UACpBtE,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEmE,WAAW,CAAC;QACvE;;QAEA;QACAtE,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACoE,aAAa,IAAIpE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UACJkB,MAAM,EAAE,wGAAwG;UAChH3B,MAAM,EAAE,CAAC;UACTkC,cAAc,EAAE;QAClB,CAAC;MACH;;MAEA;MACA,IAAIzD,KAAK,CAACK,QAAQ,EAAE;QAClB;QACAR,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAACK,QAAQ,CAACkB,MAAM,EAAEvB,KAAK,CAACK,QAAQ,CAAC4B,UAAU,CAAC;QACrFpC,OAAO,CAACG,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACK,QAAQ,CAACM,IAAI,CAAC;QAClD,MAAMX,KAAK,CAACK,QAAQ,CAACM,IAAI;MAC3B,CAAC,MAAM;QACL;QACAd,OAAO,CAACG,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAACgC,OAAO,CAAC;QAC/E,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACgC,OAAO;UAAET,MAAM,EAAE;QAAI,CAAC;MAC9C;IACF;EACF,CAAC;EAED;EACAyD,oBAAoB,EAAE,MAAOtF,UAAU,IAAK;IAC1C,IAAI;MACF;MACA,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMW,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,qBAAqB,CAAC;MACrF,OAAOG,QAAQ,CAACM,IAAI,CAACsE,kBAAkB;IACzC,CAAC,CAAC,OAAOjF,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC;EAED;EACAkF,eAAe,EAAE,MAAAA,CAAOxF,UAAU,EAAE6E,MAAM,EAAEY,KAAK,GAAG,KAAK,KAAK;IAC5D,IAAI;MACF;MACA,MAAMjF,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QAAEJ,UAAU,EAAEQ,aAAa;QAAEqE,MAAM;QAAEY;MAAM,CAAC,CAAC;;MAElG;MACAtF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,GAAGP,OAAO,SAASW,aAAa,IAAIqE,MAAM,gBAAgB,CAAC;MAE1F,IAAI;QACF,MAAMa,YAAY,GAAG,MAAMhG,KAAK,CAACoE,IAAI,CACnC,GAAGjE,OAAO,SAASW,aAAa,IAAIqE,MAAM,gBAAgB,EAC1D;UAAEY,KAAK,EAAEA;QAAM,CAAC,EAChB;UACE5E,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE;QACX,CACF,CAAC;QAEDb,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEsF,YAAY,CAACzE,IAAI,CAAC;;QAElE;QACA,MAAM,IAAIgD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;QAEvD;QACA/D,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE,MAAMoE,YAAY,GAAG,MAAM9E,KAAK,CAACkB,GAAG,CAClC,GAAGf,OAAO,SAASW,aAAa,IAAIqE,MAAM,EAAE,EAC5C;UACEhE,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE;QACX,CACF,CAAC;QAEDb,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEoE,YAAY,CAACvD,IAAI,CAAC;;QAEhE;QACA,IAAIuD,YAAY,CAACvD,IAAI,CAAC0E,sBAAsB,KAAK,CAAC,EAAE;UAClDxF,OAAO,CAACG,KAAK,CAAC,8EAA8E,CAAC;UAC7F,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;QAC3D;QAEA,OAAOiE,YAAY,CAACvD,IAAI;MAC1B,CAAC,CAAC,OAAO2E,SAAS,EAAE;QAClB;QACAzF,OAAO,CAACG,KAAK,CAAC,4DAA4D,EAAEsF,SAAS,CAAC;QACtFzF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,GAAGP,OAAO,SAASW,aAAa,IAAIqE,MAAM,aAAa,CAAC;QAEzF,MAAMgB,cAAc,GAAG,MAAMnG,KAAK,CAACoG,MAAM,CACvC,GAAGjG,OAAO,SAASW,aAAa,IAAIqE,MAAM,EAAE,EAC5C;UACEhE,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE,KAAK;UACd+E,MAAM,EAAE;YAAEC,IAAI,EAAE;UAAQ;QAC1B,CACF,CAAC;QAED7F,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEyF,cAAc,CAAC5E,IAAI,CAAC;;QAEjF;QACA,MAAM,IAAIgD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;QAEvD;QACA/D,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;QAC/E,MAAMoE,YAAY,GAAG,MAAM9E,KAAK,CAACkB,GAAG,CAClC,GAAGf,OAAO,SAASW,aAAa,IAAIqE,MAAM,EAAE,EAC5C;UACEhE,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE;QACX,CACF,CAAC;QAEDb,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEoE,YAAY,CAACvD,IAAI,CAAC;;QAE3E;QACA,IAAIuD,YAAY,CAACvD,IAAI,CAAC0E,sBAAsB,KAAK,CAAC,EAAE;UAClDxF,OAAO,CAACG,KAAK,CAAC,8EAA8E,CAAC;UAC7F,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;QAC3D;QAEA,OAAOiE,YAAY,CAACvD,IAAI;MAC1B;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA,IAAA2F,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdhG,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDH,OAAO,CAACG,KAAK,CAAC,kBAAkB,EAAE;QAChCgC,OAAO,EAAEhC,KAAK,CAACgC,OAAO;QACtBT,MAAM,GAAAoE,gBAAA,GAAE3F,KAAK,CAACK,QAAQ,cAAAsF,gBAAA,uBAAdA,gBAAA,CAAgBpE,MAAM;QAC9BU,UAAU,GAAA2D,gBAAA,GAAE5F,KAAK,CAACK,QAAQ,cAAAuF,gBAAA,uBAAdA,gBAAA,CAAgB3D,UAAU;QACtCtB,IAAI,GAAAkF,gBAAA,GAAE7F,KAAK,CAACK,QAAQ,cAAAwF,gBAAA,uBAAdA,gBAAA,CAAgBlF,IAAI;QAC1BE,GAAG,EAAE,GAAGtB,OAAO,SAASG,UAAU,IAAI6E,MAAM,EAAE;QAC9ClF,MAAM,EAAEW,KAAK,CAACX;MAChB,CAAC,CAAC;MACF,MAAMW,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACA8F,UAAU,EAAE,MAAAA,CAAOpG,UAAU,EAAE6E,MAAM,EAAEmB,IAAI,GAAG,IAAI,KAAK;IACrD,IAAI;MACF;MACA,MAAMxF,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAAEJ,UAAU,EAAEQ,aAAa;QAAEqE,MAAM;QAAEmB;MAAK,CAAC,CAAC;;MAEhG;MACA,MAAMK,aAAa,GAAG;QACpBxF,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACDC,OAAO,EAAE,KAAK;QAAE;QAChB+E,MAAM,EAAEC,IAAI,GAAG;UAAEA;QAAK,CAAC,GAAG,CAAC;MAC7B,CAAC;MAED7F,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,GAAGP,OAAO,SAASW,aAAa,IAAIqE,MAAM,EAAE,CAAC;MACrE1E,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEiG,aAAa,CAAC;;MAErC;MACA,MAAM1F,QAAQ,GAAG,MAAMjB,KAAK,CAACoG,MAAM,CAAC,GAAGjG,OAAO,SAASW,aAAa,IAAIqE,MAAM,EAAE,EAAEwB,aAAa,CAAC;MAChGlG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAClD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA,IAAAgG,gBAAA,EAAAC,gBAAA,EAAAC,iBAAA;MACdrG,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CH,OAAO,CAACG,KAAK,CAAC,gBAAgB,EAAE;QAC9BgC,OAAO,EAAEhC,KAAK,CAACgC,OAAO;QACtBT,MAAM,GAAAyE,gBAAA,GAAEhG,KAAK,CAACK,QAAQ,cAAA2F,gBAAA,uBAAdA,gBAAA,CAAgBzE,MAAM;QAC9BU,UAAU,GAAAgE,gBAAA,GAAEjG,KAAK,CAACK,QAAQ,cAAA4F,gBAAA,uBAAdA,gBAAA,CAAgBhE,UAAU;QACtCtB,IAAI,GAAAuF,iBAAA,GAAElG,KAAK,CAACK,QAAQ,cAAA6F,iBAAA,uBAAdA,iBAAA,CAAgBvF,IAAI;QAC1BE,GAAG,EAAE,GAAGtB,OAAO,SAASG,UAAU,IAAI6E,MAAM,EAAE;QAC9ClF,MAAM,EAAEW,KAAK,CAACX;MAChB,CAAC,CAAC;;MAEF;MACA,IAAIW,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACK,QAAQ,CAACM,IAAI,EAAE;QACzC,MAAMX,KAAK,CAACK,QAAQ,CAACM,IAAI;MAC3B,CAAC,MAAM,IAAIX,KAAK,CAACgC,OAAO,EAAE;QACxB,MAAM,IAAI/B,KAAK,CAACD,KAAK,CAACgC,OAAO,CAAC;MAChC,CAAC,MAAM;QACL,MAAM,IAAI/B,KAAK,CAAC,yCAAyC,CAAC;MAC5D;IACF;EACF,CAAC;EAED;EACAkG,iBAAiB,EAAE,MAAAA,CAAOzG,UAAU,EAAE6E,MAAM,EAAE6B,WAAW,KAAK;IAC5D,IAAI;MACF;MACA,MAAMlG,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,mCAAmCI,aAAa,IAAIqE,MAAM,eAAe,CAAC;MACtF1E,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEsG,WAAW,CAAC;MAEzC,MAAM/F,QAAQ,GAAG,MAAMf,aAAa,CAACkE,IAAI,CAAC,SAAStD,aAAa,IAAIqE,MAAM,eAAe,EAAE;QACzF8B,YAAY,EAAED;MAChB,CAAC,EAAE;QACD1F,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;MAElD;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,IAC5C,CAAC1D,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACkC,IAAI,KAAK,cAAc,IAC/ClC,KAAK,CAACgC,OAAO,IAAIhC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAE,EAAE;QAE9DnD,OAAO,CAACC,GAAG,CAAC,+EAA+E,CAAC;QAE5F,IAAI;UACF;UACA,MAAM,IAAI6D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEvD;UACA,MAAME,KAAK,GAAGtD,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,MAAMyD,YAAY,GAAG,MAAM9E,KAAK,CAACkB,GAAG,CAClC,GAAGf,OAAO,SAASW,aAAa,IAAIqE,MAAM,EAAE,EAC5C;YACEhE,OAAO,EAAE;cACP,eAAe,EAAE,UAAUuD,KAAK;YAClC,CAAC;YACDpD,OAAO,EAAE;UACX,CACF,CAAC;UAED,IAAIwD,YAAY,CAACvD,IAAI,IAAIuD,YAAY,CAACvD,IAAI,CAAC2F,eAAe,KAAKF,WAAW,EAAE;YAC1EvG,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;YACxF,OAAOoE,YAAY,CAACvD,IAAI;UAC1B;QACF,CAAC,CAAC,OAAOwD,WAAW,EAAE;UACpBtE,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEmE,WAAW,CAAC;QACvE;;QAEA;QACAtE,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACoE,aAAa,IAAIpE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UACJkB,MAAM,EAAE,iHAAiH;UACzH3B,MAAM,EAAE,CAAC;UACTkC,cAAc,EAAE;QAClB,CAAC;MACH;MAEA,MAAMzD,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAG;QAAEuC,MAAM,EAAElD,KAAK,CAACgC,OAAO;QAAET,MAAM,EAAE;MAAI,CAAC;IACrF;EACF,CAAC;EAED;EACAgF,YAAY,EAAE,MAAAA,CAAO7G,UAAU,EAAE6E,MAAM,EAAEiC,QAAQ,KAAK;IACpD,IAAI;MACF;MACA,MAAMtG,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,mCAAmCI,aAAa,IAAIqE,MAAM,SAAS,CAAC;MAChF1E,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE0G,QAAQ,CAAC;MAEnC,MAAMnG,QAAQ,GAAG,MAAMf,aAAa,CAACkE,IAAI,CAAC,SAAStD,aAAa,IAAIqE,MAAM,SAAS,EAAE;QACnFkC,SAAS,EAAED;MACb,CAAC,EAAE;QACD9F,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;MAE5C;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,IAC5C,CAAC1D,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACkC,IAAI,KAAK,cAAc,IAC/ClC,KAAK,CAACgC,OAAO,IAAIhC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAE,EAAE;QAE9DnD,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC;QAEpF,IAAI;UACF;UACA,MAAM,IAAI6D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEvD;UACA,MAAME,KAAK,GAAGtD,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,MAAMyD,YAAY,GAAG,MAAM9E,KAAK,CAACkB,GAAG,CAClC,GAAGf,OAAO,SAASW,aAAa,IAAIqE,MAAM,EAAE,EAC5C;YACEhE,OAAO,EAAE;cACP,eAAe,EAAE,UAAUuD,KAAK;YAClC,CAAC;YACDpD,OAAO,EAAE;UACX,CACF,CAAC;UAED,IAAIwD,YAAY,CAACvD,IAAI,IAAIuD,YAAY,CAACvD,IAAI,CAAC8F,SAAS,KAAKD,QAAQ,EAAE;YACjE3G,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;YACjF,OAAOoE,YAAY,CAACvD,IAAI;UAC1B;QACF,CAAC,CAAC,OAAOwD,WAAW,EAAE;UACpBtE,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEmE,WAAW,CAAC;QACvE;;QAEA;QACAtE,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACoE,aAAa,IAAIpE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UACJkB,MAAM,EAAE,yGAAyG;UACjH3B,MAAM,EAAE,CAAC;UACTkC,cAAc,EAAE;QAClB,CAAC;MACH;MAEA,MAAMzD,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAG;QAAEuC,MAAM,EAAElD,KAAK,CAACgC,OAAO;QAAET,MAAM,EAAE;MAAI,CAAC;IACrF;EACF,CAAC;EAED;EACAmF,iBAAiB,EAAE,MAAOhH,UAAU,IAAK;IACvC,IAAI;MACF;MACA,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMW,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,aAAa,CAAC;MAC7E,OAAOG,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACA2G,YAAY,EAAE,MAAOjH,UAAU,IAAK;IAClC,IAAI;MACF;MACA,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMW,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,QAAQ,CAAC;MACxE,OAAOG,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACA4G,YAAY,EAAE,MAAOlH,UAAU,IAAK;IAClC,IAAI;MACF;MACA,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACAD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,GAAGP,OAAO,SAASW,aAAa,cAAc,CAAC;MAElF,IAAI;QACF,MAAMG,QAAQ,GAAG,MAAMjB,KAAK,CAACkB,GAAG,CAC9B,GAAGf,OAAO,SAASW,aAAa,EAAE,EAClC;UACEK,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE,KAAK;UACd+E,MAAM,EAAE;YAAEoB,SAAS,EAAE;UAAE;QACzB,CACF,CAAC;QAEDhH,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEO,QAAQ,CAACM,IAAI,GAAGN,QAAQ,CAACM,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE,oBAAoB,CAAC;QAChH,IAAIhB,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;UAC7CxB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEO,QAAQ,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC;QACpD;QAEA,OAAON,QAAQ,CAACM,IAAI;MACtB,CAAC,CAAC,OAAOmG,aAAa,EAAE;QACtBjH,OAAO,CAACG,KAAK,CAAC,gEAAgE,EAAE8G,aAAa,CAAC;;QAE9F;QACAjH,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,GAAGP,OAAO,eAAeW,aAAa,EAAE,CAAC;QAE5E,MAAM6G,iBAAiB,GAAG,MAAM3H,KAAK,CAACkB,GAAG,CACvC,GAAGf,OAAO,eAAeW,aAAa,EAAE,EACxC;UACEK,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE;QACX,CACF,CAAC;QAEDb,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEiH,iBAAiB,CAACpG,IAAI,GAAGoG,iBAAiB,CAACpG,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE,oBAAoB,CAAC;QAClI,IAAI0F,iBAAiB,CAACpG,IAAI,IAAIoG,iBAAiB,CAACpG,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;UAC/DxB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEiH,iBAAiB,CAACpG,IAAI,CAAC,CAAC,CAAC,CAAC;QACxE;QAEA,OAAOoG,iBAAiB,CAACpG,IAAI;MAC/B;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACAgH,WAAW,EAAE,MAAAA,CAAOtH,UAAU,EAAE6E,MAAM,EAAE0C,IAAI,EAAEC,YAAY,KAAK;IAC7D,IAAI;MACF;MACA,MAAMhH,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,mCAAmCI,aAAa,IAAIqE,MAAM,eAAe,CAAC;MACtF1E,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;QAAEmH,IAAI;QAAEC;MAAa,CAAC,CAAC;MAE5C,MAAM7G,QAAQ,GAAG,MAAMf,aAAa,CAACkE,IAAI,CAAC,SAAStD,aAAa,IAAIqE,MAAM,eAAe,EAAE;QACzF0C,IAAI,EAAEA,IAAI;QACVC,YAAY,EAAEA,YAAY,IAAI;MAChC,CAAC,EAAE;QACDxG,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;;MAE3C;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,EAAE;QAChD7D,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACoE,aAAa,IAAIpE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACoE,aAAa,IAAI,+EAA+E;UAAE7C,MAAM,EAAE,CAAC;UAAEkC,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,MAAMzD,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAG;QAAEuC,MAAM,EAAElD,KAAK,CAACgC,OAAO;QAAET,MAAM,EAAE;MAAI,CAAC;IACrF;EACF,CAAC;EAED;EACA4F,YAAY,EAAE,MAAAA,CAAOzH,UAAU,EAAE6E,MAAM,EAAE0C,IAAI,KAAK;IAChD,IAAI;MACF;MACA,MAAM/G,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,qCAAqCI,aAAa,IAAIqE,MAAM,iBAAiB0C,IAAI,EAAE,CAAC;MAEhG,MAAM5G,QAAQ,GAAG,MAAMf,aAAa,CAACkG,MAAM,CAAC,SAAStF,aAAa,IAAIqE,MAAM,iBAAiB0C,IAAI,EAAE,EAAE;QACnGvG,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;MAE5C;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,EAAE;QAChD7D,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACoE,aAAa,IAAIpE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACoE,aAAa,IAAI,+EAA+E;UAAE7C,MAAM,EAAE,CAAC;UAAEkC,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,MAAMzD,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAG;QAAEuC,MAAM,EAAElD,KAAK,CAACgC,OAAO;QAAET,MAAM,EAAE;MAAI,CAAC;IACrF;EACF,CAAC;EAED;EACA6F,SAAS,EAAE,MAAAA,CAAO1H,UAAU,EAAE6E,MAAM,KAAK;IACvC,IAAI;MACF;MACA,MAAMrE,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,MAAMuH,MAAM,GAAG,MAAM/H,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,cAAc,EAAE;QAC3EQ,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MACF,MAAM4G,UAAU,GAAGD,MAAM,CAAC1G,IAAI,CAAC4G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxD,OAAO,KAAKO,MAAM,CAAC;;MAE9D;MACA1E,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,MAAM2H,KAAK,GAAG,MAAMnI,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,cAAc,EAAE;QAC1EQ,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MACF,MAAMgH,SAAS,GAAGD,KAAK,CAAC9G,IAAI,CAAC4G,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxD,OAAO,KAAKO,MAAM,CAAC;MAE5D,OAAO;QACLoD,kBAAkB,EAAE,CAAC,CAACL,UAAU;QAChCM,iBAAiB,EAAE,CAAC,CAACF,SAAS;QAC9BG,WAAW,EAAEP,UAAU;QACvBQ,UAAU,EAAEJ;MACd,CAAC;IACH,CAAC,CAAC,OAAO1H,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;;MAEzC;MACA,IAAIA,KAAK,CAACyD,cAAc,IAAIzD,KAAK,CAAC0D,cAAc,EAAE;QAChD7D,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACoE,aAAa,IAAIpE,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACoE,aAAa,IAAI,+EAA+E;UAAE7C,MAAM,EAAE,CAAC;UAAEkC,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,MAAMzD,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAG;QAAEuC,MAAM,EAAElD,KAAK,CAACgC,OAAO;QAAET,MAAM,EAAE;MAAI,CAAC;IACrF;EACF;AACF,CAAC;AAED,eAAe/B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}