{"ast": null, "code": "import React,{useState,useEffect,useRef}from'react';import{Box,Popover,Typography,TextField,Checkbox,FormControlLabel,Button,Divider,List,ListItem,ListItemText,IconButton,InputAdornment,MenuItem,Select,FormControl,InputLabel,RadioGroup,Radio}from'@mui/material';import{FilterList as FilterIcon,Search as SearchIcon,ArrowUpward as ArrowUpwardIcon,ArrowDownward as ArrowDownwardIcon,Clear as ClearIcon}from'@mui/icons-material';/**\n * Componente di filtro in stile Excel\n * \n * @param {Object} props - Proprietà del componente\n * @param {Array} props.data - <PERSON>ti da filtrare\n * @param {string} props.columnName - Nome della colonna da filtrare\n * @param {Function} props.onFilterChange - Funzione chiamata quando il filtro cambia\n * @param {string} props.dataType - Tipo di dati ('text', 'number', 'date')\n * @param {Array} props.uniqueValues - Valori unici per la colonna (opzionale)\n */import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ExcelLikeFilter=_ref=>{let{data,columnName,onFilterChange,dataType='text',uniqueValues:propUniqueValues=null}=_ref;const[anchorEl,setAnchorEl]=useState(null);const[searchTerm,setSearchTerm]=useState('');const[uniqueValues,setUniqueValues]=useState([]);const[selectedValues,setSelectedValues]=useState([]);const[selectAll,setSelectAll]=useState(true);const[filterType,setFilterType]=useState('equals');// equals, contains, greaterThan, lessThan, between\nconst[rangeValues,setRangeValues]=useState({min:'',max:''});const[sortDirection,setSortDirection]=useState(null);// null, 'asc', 'desc'\nconst inputRef=useRef(null);// Calcola i valori unici per la colonna\nuseEffect(()=>{if(propUniqueValues){setUniqueValues(propUniqueValues);setSelectedValues(propUniqueValues);return;}if(data&&data.length>0){const values=[...new Set(data.map(item=>item[columnName]))].filter(Boolean);values.sort((a,b)=>{if(dataType==='number'){return parseFloat(a)-parseFloat(b);}return String(a).localeCompare(String(b));});setUniqueValues(values);setSelectedValues(values);}},[data,columnName,propUniqueValues,dataType]);const handleClick=event=>{setAnchorEl(event.currentTarget);};const handleClose=()=>{setAnchorEl(null);};const handleSearch=event=>{setSearchTerm(event.target.value);};const handleSelectAll=event=>{const checked=event.target.checked;setSelectAll(checked);if(checked){setSelectedValues(uniqueValues);}else{setSelectedValues([]);}};const handleValueSelect=value=>{const newSelectedValues=selectedValues.includes(value)?selectedValues.filter(v=>v!==value):[...selectedValues,value];setSelectedValues(newSelectedValues);setSelectAll(newSelectedValues.length===uniqueValues.length);};const handleFilterTypeChange=event=>{setFilterType(event.target.value);};const handleRangeChange=(field,value)=>{setRangeValues(prev=>({...prev,[field]:value}));};const handleSort=direction=>{setSortDirection(direction);applyFilter(selectedValues,filterType,rangeValues,direction);handleClose();};const applyFilter=function(){let values=arguments.length>0&&arguments[0]!==undefined?arguments[0]:selectedValues;let type=arguments.length>1&&arguments[1]!==undefined?arguments[1]:filterType;let range=arguments.length>2&&arguments[2]!==undefined?arguments[2]:rangeValues;let sort=arguments.length>3&&arguments[3]!==undefined?arguments[3]:sortDirection;let filteredData=[...data];// Applica il filtro in base al tipo\nif(type==='equals'&&values.length<uniqueValues.length){filteredData=filteredData.filter(item=>values.includes(item[columnName]));}else if(type==='contains'){filteredData=filteredData.filter(item=>item[columnName]&&String(item[columnName]).toLowerCase().includes(searchTerm.toLowerCase()));}else if(type==='greaterThan'&&range.min!==''){filteredData=filteredData.filter(item=>parseFloat(item[columnName])>parseFloat(range.min));}else if(type==='lessThan'&&range.max!==''){filteredData=filteredData.filter(item=>parseFloat(item[columnName])<parseFloat(range.max));}else if(type==='between'&&range.min!==''&&range.max!==''){filteredData=filteredData.filter(item=>parseFloat(item[columnName])>=parseFloat(range.min)&&parseFloat(item[columnName])<=parseFloat(range.max));}// Applica l'ordinamento\nif(sort){filteredData.sort((a,b)=>{let valueA=a[columnName];let valueB=b[columnName];if(dataType==='number'){valueA=parseFloat(valueA)||0;valueB=parseFloat(valueB)||0;}else{valueA=String(valueA||'');valueB=String(valueB||'');}if(sort==='asc'){return valueA>valueB?1:-1;}else{return valueA<valueB?1:-1;}});}onFilterChange(filteredData,{columnName,filterType:type,selectedValues:values,searchTerm,rangeValues:range,sortDirection:sort});};const handleApply=()=>{applyFilter();handleClose();};const handleClear=()=>{setSelectedValues(uniqueValues);setSelectAll(true);setFilterType('equals');setSearchTerm('');setRangeValues({min:'',max:''});setSortDirection(null);onFilterChange(data,{columnName,filterType:'equals',selectedValues:uniqueValues,searchTerm:'',rangeValues:{min:'',max:''},sortDirection:null});handleClose();};const open=Boolean(anchorEl);const id=open?`filter-popover-${columnName}`:undefined;// Filtra i valori unici in base al termine di ricerca\nconst filteredUniqueValues=uniqueValues.filter(value=>String(value).toLowerCase().includes(searchTerm.toLowerCase()));return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(IconButton,{\"aria-describedby\":id,onClick:handleClick,size:\"small\",color:selectedValues.length<uniqueValues.length||filterType!=='equals'||sortDirection?'primary':'default',sx:{position:'relative','&::after':selectedValues.length<uniqueValues.length||filterType!=='equals'||sortDirection?{content:'\"\"',position:'absolute',top:2,right:2,width:8,height:8,borderRadius:'50%',backgroundColor:'#f44336'}:{}},children:/*#__PURE__*/_jsx(FilterIcon,{fontSize:\"small\"})}),/*#__PURE__*/_jsx(Popover,{id:id,open:open,anchorEl:anchorEl,onClose:handleClose,anchorOrigin:{vertical:'bottom',horizontal:'left'},transformOrigin:{vertical:'top',horizontal:'left'},PaperProps:{style:{width:'300px',maxHeight:'500px'}},children:/*#__PURE__*/_jsxs(Box,{sx:{p:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,children:[\"Filtro: \",columnName]}),/*#__PURE__*/_jsxs(Box,{sx:{mb:2,display:'flex',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleSort('asc'),color:sortDirection==='asc'?'primary':'default',title:\"Ordina crescente (A-Z)\",children:/*#__PURE__*/_jsx(ArrowUpwardIcon,{fontSize:\"small\"})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleSort('desc'),color:sortDirection==='desc'?'primary':'default',title:\"Ordina decrescente (Z-A)\",children:/*#__PURE__*/_jsx(ArrowDownwardIcon,{fontSize:\"small\"})}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:handleClear,title:\"Cancella filtri\",children:/*#__PURE__*/_jsx(ClearIcon,{fontSize:\"small\"})})]}),/*#__PURE__*/_jsx(Divider,{sx:{mb:2}}),dataType==='text'&&/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,size:\"small\",sx:{mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mb:1},children:\"Tipo di filtro:\"}),/*#__PURE__*/_jsxs(RadioGroup,{value:filterType,onChange:handleFilterTypeChange,row:true,children:[/*#__PURE__*/_jsx(FormControlLabel,{value:\"equals\",control:/*#__PURE__*/_jsx(Radio,{size:\"small\"}),label:\"\\xC8 uguale a\"}),/*#__PURE__*/_jsx(FormControlLabel,{value:\"contains\",control:/*#__PURE__*/_jsx(Radio,{size:\"small\"}),label:\"Contiene\"})]})]}),dataType==='number'&&/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,size:\"small\",sx:{mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mb:1},children:\"Tipo di filtro:\"}),/*#__PURE__*/_jsxs(RadioGroup,{value:filterType,onChange:handleFilterTypeChange,sx:{display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsx(FormControlLabel,{value:\"equals\",control:/*#__PURE__*/_jsx(Radio,{size:\"small\"}),label:\"\\xC8 uguale a\"}),/*#__PURE__*/_jsx(FormControlLabel,{value:\"greaterThan\",control:/*#__PURE__*/_jsx(Radio,{size:\"small\"}),label:\"Maggiore di\"}),/*#__PURE__*/_jsx(FormControlLabel,{value:\"lessThan\",control:/*#__PURE__*/_jsx(Radio,{size:\"small\"}),label:\"Minore di\"}),/*#__PURE__*/_jsx(FormControlLabel,{value:\"between\",control:/*#__PURE__*/_jsx(Radio,{size:\"small\"}),label:\"Compreso tra\"})]})]}),filterType==='contains'&&/*#__PURE__*/_jsx(TextField,{fullWidth:true,size:\"small\",label:\"Cerca\",variant:\"outlined\",value:searchTerm,onChange:handleSearch,sx:{mb:2},InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(SearchIcon,{fontSize:\"small\"})})}}),filterType==='greaterThan'&&/*#__PURE__*/_jsx(TextField,{fullWidth:true,size:\"small\",label:\"Valore minimo\",variant:\"outlined\",type:\"number\",value:rangeValues.min,onChange:e=>handleRangeChange('min',e.target.value),sx:{mb:2}}),filterType==='lessThan'&&/*#__PURE__*/_jsx(TextField,{fullWidth:true,size:\"small\",label:\"Valore massimo\",variant:\"outlined\",type:\"number\",value:rangeValues.max,onChange:e=>handleRangeChange('max',e.target.value),sx:{mb:2}}),filterType==='between'&&/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1,mb:2},children:[/*#__PURE__*/_jsx(TextField,{fullWidth:true,size:\"small\",label:\"Da\",variant:\"outlined\",type:\"number\",value:rangeValues.min,onChange:e=>handleRangeChange('min',e.target.value)}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,size:\"small\",label:\"A\",variant:\"outlined\",type:\"number\",value:rangeValues.max,onChange:e=>handleRangeChange('max',e.target.value)})]}),filterType==='equals'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(TextField,{fullWidth:true,size:\"small\",label:\"Cerca valori\",variant:\"outlined\",value:searchTerm,onChange:handleSearch,sx:{mb:2},InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(SearchIcon,{fontSize:\"small\"})})},inputRef:inputRef}),/*#__PURE__*/_jsx(FormControlLabel,{control:/*#__PURE__*/_jsx(Checkbox,{checked:selectAll,onChange:handleSelectAll,size:\"small\"}),label:\"Seleziona tutti\",sx:{mb:1}}),/*#__PURE__*/_jsx(Divider,{sx:{mb:1}}),/*#__PURE__*/_jsxs(List,{sx:{maxHeight:'200px',overflow:'auto',mb:2},children:[filteredUniqueValues.map((value,index)=>/*#__PURE__*/_jsx(ListItem,{dense:true,disablePadding:true,children:/*#__PURE__*/_jsx(FormControlLabel,{control:/*#__PURE__*/_jsx(Checkbox,{checked:selectedValues.includes(value),onChange:()=>handleValueSelect(value),size:\"small\"}),label:/*#__PURE__*/_jsx(ListItemText,{primary:value||'(Vuoto)',primaryTypographyProps:{variant:'body2',style:{overflow:'hidden',textOverflow:'ellipsis',whiteSpace:'nowrap'}}}),sx:{m:0,width:'100%'}})},index)),filteredUniqueValues.length===0&&/*#__PURE__*/_jsx(ListItem,{children:/*#__PURE__*/_jsx(ListItemText,{primary:\"Nessun valore trovato\",primaryTypographyProps:{variant:'body2'}})})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'flex-end',gap:1},children:[/*#__PURE__*/_jsx(Button,{variant:\"outlined\",size:\"small\",onClick:handleClose,children:\"Annulla\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",size:\"small\",onClick:handleApply,color:\"primary\",children:\"Applica\"})]})]})})]});};export default ExcelLikeFilter;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Popover", "Typography", "TextField", "Checkbox", "FormControlLabel", "<PERSON><PERSON>", "Divider", "List", "ListItem", "ListItemText", "IconButton", "InputAdornment", "MenuItem", "Select", "FormControl", "InputLabel", "RadioGroup", "Radio", "FilterList", "FilterIcon", "Search", "SearchIcon", "ArrowUpward", "ArrowUpwardIcon", "ArrowDownward", "ArrowDownwardIcon", "Clear", "ClearIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ExcelLikeFilter", "_ref", "data", "columnName", "onFilterChange", "dataType", "uniqueValues", "propUniqueValues", "anchorEl", "setAnchorEl", "searchTerm", "setSearchTerm", "setUniqueValues", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedValues", "selectAll", "setSelectAll", "filterType", "setFilterType", "rangeValues", "setRangeV<PERSON>ues", "min", "max", "sortDirection", "setSortDirection", "inputRef", "length", "values", "Set", "map", "item", "filter", "Boolean", "sort", "a", "b", "parseFloat", "String", "localeCompare", "handleClick", "event", "currentTarget", "handleClose", "handleSearch", "target", "value", "handleSelectAll", "checked", "handleValueSelect", "newSelectedValues", "includes", "v", "handleFilterTypeChange", "handleRangeChange", "field", "prev", "handleSort", "direction", "applyFilter", "arguments", "undefined", "type", "range", "filteredData", "toLowerCase", "valueA", "valueB", "handleApply", "handleClear", "open", "id", "filteredUniqueValues", "children", "onClick", "size", "color", "sx", "position", "content", "top", "right", "width", "height", "borderRadius", "backgroundColor", "fontSize", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "PaperProps", "style", "maxHeight", "p", "variant", "gutterBottom", "mb", "display", "justifyContent", "title", "fullWidth", "onChange", "row", "control", "label", "flexDirection", "InputProps", "startAdornment", "e", "gap", "overflow", "index", "dense", "disablePadding", "primary", "primaryTypographyProps", "textOverflow", "whiteSpace", "m"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/common/ExcelLikeFilter.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Box,\n  Popover,\n  Typography,\n  TextField,\n  Checkbox,\n  FormControlLabel,\n  Button,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  IconButton,\n  InputAdornment,\n  MenuItem,\n  Select,\n  FormControl,\n  InputLabel,\n  RadioGroup,\n  Radio\n} from '@mui/material';\nimport {\n  FilterList as FilterIcon,\n  Search as SearchIcon,\n  ArrowUpward as ArrowUpwardIcon,\n  ArrowDownward as ArrowDownwardIcon,\n  Clear as ClearIcon\n} from '@mui/icons-material';\n\n/**\n * Componente di filtro in stile Excel\n * \n * @param {Object} props - Proprietà del componente\n * @param {Array} props.data - <PERSON>ti da filtrare\n * @param {string} props.columnName - Nome della colonna da filtrare\n * @param {Function} props.onFilterChange - Funzione chiamata quando il filtro cambia\n * @param {string} props.dataType - Tipo di dati ('text', 'number', 'date')\n * @param {Array} props.uniqueValues - Valori unici per la colonna (opzionale)\n */\nconst ExcelLikeFilter = ({ \n  data, \n  columnName, \n  onFilterChange, \n  dataType = 'text',\n  uniqueValues: propUniqueValues = null\n}) => {\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [uniqueValues, setUniqueValues] = useState([]);\n  const [selectedValues, setSelectedValues] = useState([]);\n  const [selectAll, setSelectAll] = useState(true);\n  const [filterType, setFilterType] = useState('equals'); // equals, contains, greaterThan, lessThan, between\n  const [rangeValues, setRangeValues] = useState({ min: '', max: '' });\n  const [sortDirection, setSortDirection] = useState(null); // null, 'asc', 'desc'\n\n  const inputRef = useRef(null);\n\n  // Calcola i valori unici per la colonna\n  useEffect(() => {\n    if (propUniqueValues) {\n      setUniqueValues(propUniqueValues);\n      setSelectedValues(propUniqueValues);\n      return;\n    }\n\n    if (data && data.length > 0) {\n      const values = [...new Set(data.map(item => item[columnName]))].filter(Boolean);\n      values.sort((a, b) => {\n        if (dataType === 'number') {\n          return parseFloat(a) - parseFloat(b);\n        }\n        return String(a).localeCompare(String(b));\n      });\n      setUniqueValues(values);\n      setSelectedValues(values);\n    }\n  }, [data, columnName, propUniqueValues, dataType]);\n\n  const handleClick = (event) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n\n  const handleSearch = (event) => {\n    setSearchTerm(event.target.value);\n  };\n\n  const handleSelectAll = (event) => {\n    const checked = event.target.checked;\n    setSelectAll(checked);\n    if (checked) {\n      setSelectedValues(uniqueValues);\n    } else {\n      setSelectedValues([]);\n    }\n  };\n\n  const handleValueSelect = (value) => {\n    const newSelectedValues = selectedValues.includes(value)\n      ? selectedValues.filter(v => v !== value)\n      : [...selectedValues, value];\n\n    setSelectedValues(newSelectedValues);\n    setSelectAll(newSelectedValues.length === uniqueValues.length);\n  };\n\n  const handleFilterTypeChange = (event) => {\n    setFilterType(event.target.value);\n  };\n\n  const handleRangeChange = (field, value) => {\n    setRangeValues(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleSort = (direction) => {\n    setSortDirection(direction);\n    applyFilter(selectedValues, filterType, rangeValues, direction);\n    handleClose();\n  };\n\n  const applyFilter = (values = selectedValues, type = filterType, range = rangeValues, sort = sortDirection) => {\n    let filteredData = [...data];\n\n    // Applica il filtro in base al tipo\n    if (type === 'equals' && values.length < uniqueValues.length) {\n      filteredData = filteredData.filter(item => values.includes(item[columnName]));\n    } else if (type === 'contains') {\n      filteredData = filteredData.filter(item => \n        item[columnName] && String(item[columnName]).toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    } else if (type === 'greaterThan' && range.min !== '') {\n      filteredData = filteredData.filter(item => \n        parseFloat(item[columnName]) > parseFloat(range.min)\n      );\n    } else if (type === 'lessThan' && range.max !== '') {\n      filteredData = filteredData.filter(item => \n        parseFloat(item[columnName]) < parseFloat(range.max)\n      );\n    } else if (type === 'between' && range.min !== '' && range.max !== '') {\n      filteredData = filteredData.filter(item => \n        parseFloat(item[columnName]) >= parseFloat(range.min) && \n        parseFloat(item[columnName]) <= parseFloat(range.max)\n      );\n    }\n\n    // Applica l'ordinamento\n    if (sort) {\n      filteredData.sort((a, b) => {\n        let valueA = a[columnName];\n        let valueB = b[columnName];\n\n        if (dataType === 'number') {\n          valueA = parseFloat(valueA) || 0;\n          valueB = parseFloat(valueB) || 0;\n        } else {\n          valueA = String(valueA || '');\n          valueB = String(valueB || '');\n        }\n\n        if (sort === 'asc') {\n          return valueA > valueB ? 1 : -1;\n        } else {\n          return valueA < valueB ? 1 : -1;\n        }\n      });\n    }\n\n    onFilterChange(filteredData, {\n      columnName,\n      filterType: type,\n      selectedValues: values,\n      searchTerm,\n      rangeValues: range,\n      sortDirection: sort\n    });\n  };\n\n  const handleApply = () => {\n    applyFilter();\n    handleClose();\n  };\n\n  const handleClear = () => {\n    setSelectedValues(uniqueValues);\n    setSelectAll(true);\n    setFilterType('equals');\n    setSearchTerm('');\n    setRangeValues({ min: '', max: '' });\n    setSortDirection(null);\n\n    onFilterChange(data, {\n      columnName,\n      filterType: 'equals',\n      selectedValues: uniqueValues,\n      searchTerm: '',\n      rangeValues: { min: '', max: '' },\n      sortDirection: null\n    });\n\n    handleClose();\n  };\n\n  const open = Boolean(anchorEl);\n  const id = open ? `filter-popover-${columnName}` : undefined;\n\n  // Filtra i valori unici in base al termine di ricerca\n  const filteredUniqueValues = uniqueValues.filter(value => \n    String(value).toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  return (\n    <Box>\n      <IconButton \n        aria-describedby={id} \n        onClick={handleClick}\n        size=\"small\"\n        color={\n          (selectedValues.length < uniqueValues.length || \n           filterType !== 'equals' || \n           sortDirection) ? 'primary' : 'default'\n        }\n        sx={{\n          position: 'relative',\n          '&::after': (selectedValues.length < uniqueValues.length || \n                       filterType !== 'equals' || \n                       sortDirection) ? {\n            content: '\"\"',\n            position: 'absolute',\n            top: 2,\n            right: 2,\n            width: 8,\n            height: 8,\n            borderRadius: '50%',\n            backgroundColor: '#f44336',\n          } : {}\n        }}\n      >\n        <FilterIcon fontSize=\"small\" />\n      </IconButton>\n\n      <Popover\n        id={id}\n        open={open}\n        anchorEl={anchorEl}\n        onClose={handleClose}\n        anchorOrigin={{\n          vertical: 'bottom',\n          horizontal: 'left',\n        }}\n        transformOrigin={{\n          vertical: 'top',\n          horizontal: 'left',\n        }}\n        PaperProps={{\n          style: { width: '300px', maxHeight: '500px' }\n        }}\n      >\n        <Box sx={{ p: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Filtro: {columnName}\n          </Typography>\n\n          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between' }}>\n            <IconButton \n              size=\"small\" \n              onClick={() => handleSort('asc')}\n              color={sortDirection === 'asc' ? 'primary' : 'default'}\n              title=\"Ordina crescente (A-Z)\"\n            >\n              <ArrowUpwardIcon fontSize=\"small\" />\n            </IconButton>\n            <IconButton \n              size=\"small\" \n              onClick={() => handleSort('desc')}\n              color={sortDirection === 'desc' ? 'primary' : 'default'}\n              title=\"Ordina decrescente (Z-A)\"\n            >\n              <ArrowDownwardIcon fontSize=\"small\" />\n            </IconButton>\n            <IconButton \n              size=\"small\" \n              onClick={handleClear}\n              title=\"Cancella filtri\"\n            >\n              <ClearIcon fontSize=\"small\" />\n            </IconButton>\n          </Box>\n\n          <Divider sx={{ mb: 2 }} />\n\n          {dataType === 'text' && (\n            <FormControl fullWidth size=\"small\" sx={{ mb: 2 }}>\n              <Typography variant=\"body2\" sx={{ mb: 1 }}>Tipo di filtro:</Typography>\n              <RadioGroup\n                value={filterType}\n                onChange={handleFilterTypeChange}\n                row\n              >\n                <FormControlLabel \n                  value=\"equals\" \n                  control={<Radio size=\"small\" />} \n                  label=\"È uguale a\" \n                />\n                <FormControlLabel \n                  value=\"contains\" \n                  control={<Radio size=\"small\" />} \n                  label=\"Contiene\" \n                />\n              </RadioGroup>\n            </FormControl>\n          )}\n\n          {dataType === 'number' && (\n            <FormControl fullWidth size=\"small\" sx={{ mb: 2 }}>\n              <Typography variant=\"body2\" sx={{ mb: 1 }}>Tipo di filtro:</Typography>\n              <RadioGroup\n                value={filterType}\n                onChange={handleFilterTypeChange}\n                sx={{ display: 'flex', flexDirection: 'column' }}\n              >\n                <FormControlLabel \n                  value=\"equals\" \n                  control={<Radio size=\"small\" />} \n                  label=\"È uguale a\" \n                />\n                <FormControlLabel \n                  value=\"greaterThan\" \n                  control={<Radio size=\"small\" />} \n                  label=\"Maggiore di\" \n                />\n                <FormControlLabel \n                  value=\"lessThan\" \n                  control={<Radio size=\"small\" />} \n                  label=\"Minore di\" \n                />\n                <FormControlLabel \n                  value=\"between\" \n                  control={<Radio size=\"small\" />} \n                  label=\"Compreso tra\" \n                />\n              </RadioGroup>\n            </FormControl>\n          )}\n\n          {filterType === 'contains' && (\n            <TextField\n              fullWidth\n              size=\"small\"\n              label=\"Cerca\"\n              variant=\"outlined\"\n              value={searchTerm}\n              onChange={handleSearch}\n              sx={{ mb: 2 }}\n              InputProps={{\n                startAdornment: (\n                  <InputAdornment position=\"start\">\n                    <SearchIcon fontSize=\"small\" />\n                  </InputAdornment>\n                ),\n              }}\n            />\n          )}\n\n          {filterType === 'greaterThan' && (\n            <TextField\n              fullWidth\n              size=\"small\"\n              label=\"Valore minimo\"\n              variant=\"outlined\"\n              type=\"number\"\n              value={rangeValues.min}\n              onChange={(e) => handleRangeChange('min', e.target.value)}\n              sx={{ mb: 2 }}\n            />\n          )}\n\n          {filterType === 'lessThan' && (\n            <TextField\n              fullWidth\n              size=\"small\"\n              label=\"Valore massimo\"\n              variant=\"outlined\"\n              type=\"number\"\n              value={rangeValues.max}\n              onChange={(e) => handleRangeChange('max', e.target.value)}\n              sx={{ mb: 2 }}\n            />\n          )}\n\n          {filterType === 'between' && (\n            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>\n              <TextField\n                fullWidth\n                size=\"small\"\n                label=\"Da\"\n                variant=\"outlined\"\n                type=\"number\"\n                value={rangeValues.min}\n                onChange={(e) => handleRangeChange('min', e.target.value)}\n              />\n              <TextField\n                fullWidth\n                size=\"small\"\n                label=\"A\"\n                variant=\"outlined\"\n                type=\"number\"\n                value={rangeValues.max}\n                onChange={(e) => handleRangeChange('max', e.target.value)}\n              />\n            </Box>\n          )}\n\n          {filterType === 'equals' && (\n            <>\n              <TextField\n                fullWidth\n                size=\"small\"\n                label=\"Cerca valori\"\n                variant=\"outlined\"\n                value={searchTerm}\n                onChange={handleSearch}\n                sx={{ mb: 2 }}\n                InputProps={{\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <SearchIcon fontSize=\"small\" />\n                    </InputAdornment>\n                  ),\n                }}\n                inputRef={inputRef}\n              />\n\n              <FormControlLabel\n                control={\n                  <Checkbox\n                    checked={selectAll}\n                    onChange={handleSelectAll}\n                    size=\"small\"\n                  />\n                }\n                label=\"Seleziona tutti\"\n                sx={{ mb: 1 }}\n              />\n\n              <Divider sx={{ mb: 1 }} />\n\n              <List sx={{ maxHeight: '200px', overflow: 'auto', mb: 2 }}>\n                {filteredUniqueValues.map((value, index) => (\n                  <ListItem key={index} dense disablePadding>\n                    <FormControlLabel\n                      control={\n                        <Checkbox\n                          checked={selectedValues.includes(value)}\n                          onChange={() => handleValueSelect(value)}\n                          size=\"small\"\n                        />\n                      }\n                      label={\n                        <ListItemText \n                          primary={value || '(Vuoto)'} \n                          primaryTypographyProps={{ \n                            variant: 'body2',\n                            style: { \n                              overflow: 'hidden',\n                              textOverflow: 'ellipsis',\n                              whiteSpace: 'nowrap'\n                            }\n                          }}\n                        />\n                      }\n                      sx={{ m: 0, width: '100%' }}\n                    />\n                  </ListItem>\n                ))}\n                {filteredUniqueValues.length === 0 && (\n                  <ListItem>\n                    <ListItemText \n                      primary=\"Nessun valore trovato\" \n                      primaryTypographyProps={{ variant: 'body2' }}\n                    />\n                  </ListItem>\n                )}\n              </List>\n            </>\n          )}\n\n          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>\n            <Button \n              variant=\"outlined\" \n              size=\"small\" \n              onClick={handleClose}\n            >\n              Annulla\n            </Button>\n            <Button \n              variant=\"contained\" \n              size=\"small\" \n              onClick={handleApply}\n              color=\"primary\"\n            >\n              Applica\n            </Button>\n          </Box>\n        </Box>\n      </Popover>\n    </Box>\n  );\n};\n\nexport default ExcelLikeFilter;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OACEC,GAAG,CACHC,OAAO,CACPC,UAAU,CACVC,SAAS,CACTC,QAAQ,CACRC,gBAAgB,CAChBC,MAAM,CACNC,OAAO,CACPC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,UAAU,CACVC,cAAc,CACdC,QAAQ,CACRC,MAAM,CACNC,WAAW,CACXC,UAAU,CACVC,UAAU,CACVC,KAAK,KACA,eAAe,CACtB,OACEC,UAAU,GAAI,CAAAC,UAAU,CACxBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,WAAW,GAAI,CAAAC,eAAe,CAC9BC,aAAa,GAAI,CAAAC,iBAAiB,CAClCC,KAAK,GAAI,CAAAC,SAAS,KACb,qBAAqB,CAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GATA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAUA,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAMlB,IANmB,CACvBC,IAAI,CACJC,UAAU,CACVC,cAAc,CACdC,QAAQ,CAAG,MAAM,CACjBC,YAAY,CAAEC,gBAAgB,CAAG,IACnC,CAAC,CAAAN,IAAA,CACC,KAAM,CAACO,QAAQ,CAAEC,WAAW,CAAC,CAAG/C,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAACgD,UAAU,CAAEC,aAAa,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC4C,YAAY,CAAEM,eAAe,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACmD,cAAc,CAAEC,iBAAiB,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACqD,SAAS,CAAEC,YAAY,CAAC,CAAGtD,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACuD,UAAU,CAAEC,aAAa,CAAC,CAAGxD,QAAQ,CAAC,QAAQ,CAAC,CAAE;AACxD,KAAM,CAACyD,WAAW,CAAEC,cAAc,CAAC,CAAG1D,QAAQ,CAAC,CAAE2D,GAAG,CAAE,EAAE,CAAEC,GAAG,CAAE,EAAG,CAAC,CAAC,CACpE,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG9D,QAAQ,CAAC,IAAI,CAAC,CAAE;AAE1D,KAAM,CAAA+D,QAAQ,CAAG7D,MAAM,CAAC,IAAI,CAAC,CAE7B;AACAD,SAAS,CAAC,IAAM,CACd,GAAI4C,gBAAgB,CAAE,CACpBK,eAAe,CAACL,gBAAgB,CAAC,CACjCO,iBAAiB,CAACP,gBAAgB,CAAC,CACnC,OACF,CAEA,GAAIL,IAAI,EAAIA,IAAI,CAACwB,MAAM,CAAG,CAAC,CAAE,CAC3B,KAAM,CAAAC,MAAM,CAAG,CAAC,GAAG,GAAI,CAAAC,GAAG,CAAC1B,IAAI,CAAC2B,GAAG,CAACC,IAAI,EAAIA,IAAI,CAAC3B,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC4B,MAAM,CAACC,OAAO,CAAC,CAC/EL,MAAM,CAACM,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CACpB,GAAI9B,QAAQ,GAAK,QAAQ,CAAE,CACzB,MAAO,CAAA+B,UAAU,CAACF,CAAC,CAAC,CAAGE,UAAU,CAACD,CAAC,CAAC,CACtC,CACA,MAAO,CAAAE,MAAM,CAACH,CAAC,CAAC,CAACI,aAAa,CAACD,MAAM,CAACF,CAAC,CAAC,CAAC,CAC3C,CAAC,CAAC,CACFvB,eAAe,CAACe,MAAM,CAAC,CACvBb,iBAAiB,CAACa,MAAM,CAAC,CAC3B,CACF,CAAC,CAAE,CAACzB,IAAI,CAAEC,UAAU,CAAEI,gBAAgB,CAAEF,QAAQ,CAAC,CAAC,CAElD,KAAM,CAAAkC,WAAW,CAAIC,KAAK,EAAK,CAC7B/B,WAAW,CAAC+B,KAAK,CAACC,aAAa,CAAC,CAClC,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxBjC,WAAW,CAAC,IAAI,CAAC,CACnB,CAAC,CAED,KAAM,CAAAkC,YAAY,CAAIH,KAAK,EAAK,CAC9B7B,aAAa,CAAC6B,KAAK,CAACI,MAAM,CAACC,KAAK,CAAC,CACnC,CAAC,CAED,KAAM,CAAAC,eAAe,CAAIN,KAAK,EAAK,CACjC,KAAM,CAAAO,OAAO,CAAGP,KAAK,CAACI,MAAM,CAACG,OAAO,CACpC/B,YAAY,CAAC+B,OAAO,CAAC,CACrB,GAAIA,OAAO,CAAE,CACXjC,iBAAiB,CAACR,YAAY,CAAC,CACjC,CAAC,IAAM,CACLQ,iBAAiB,CAAC,EAAE,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAAkC,iBAAiB,CAAIH,KAAK,EAAK,CACnC,KAAM,CAAAI,iBAAiB,CAAGpC,cAAc,CAACqC,QAAQ,CAACL,KAAK,CAAC,CACpDhC,cAAc,CAACkB,MAAM,CAACoB,CAAC,EAAIA,CAAC,GAAKN,KAAK,CAAC,CACvC,CAAC,GAAGhC,cAAc,CAAEgC,KAAK,CAAC,CAE9B/B,iBAAiB,CAACmC,iBAAiB,CAAC,CACpCjC,YAAY,CAACiC,iBAAiB,CAACvB,MAAM,GAAKpB,YAAY,CAACoB,MAAM,CAAC,CAChE,CAAC,CAED,KAAM,CAAA0B,sBAAsB,CAAIZ,KAAK,EAAK,CACxCtB,aAAa,CAACsB,KAAK,CAACI,MAAM,CAACC,KAAK,CAAC,CACnC,CAAC,CAED,KAAM,CAAAQ,iBAAiB,CAAGA,CAACC,KAAK,CAAET,KAAK,GAAK,CAC1CzB,cAAc,CAACmC,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,KAAK,EAAGT,KAAM,CAAC,CAAC,CAAC,CACvD,CAAC,CAED,KAAM,CAAAW,UAAU,CAAIC,SAAS,EAAK,CAChCjC,gBAAgB,CAACiC,SAAS,CAAC,CAC3BC,WAAW,CAAC7C,cAAc,CAAEI,UAAU,CAAEE,WAAW,CAAEsC,SAAS,CAAC,CAC/Df,WAAW,CAAC,CAAC,CACf,CAAC,CAED,KAAM,CAAAgB,WAAW,CAAG,QAAAA,CAAA,CAA2F,IAA1F,CAAA/B,MAAM,CAAAgC,SAAA,CAAAjC,MAAA,IAAAiC,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG9C,cAAc,IAAE,CAAAgD,IAAI,CAAAF,SAAA,CAAAjC,MAAA,IAAAiC,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG1C,UAAU,IAAE,CAAA6C,KAAK,CAAAH,SAAA,CAAAjC,MAAA,IAAAiC,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAGxC,WAAW,IAAE,CAAAc,IAAI,CAAA0B,SAAA,CAAAjC,MAAA,IAAAiC,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAGpC,aAAa,CACxG,GAAI,CAAAwC,YAAY,CAAG,CAAC,GAAG7D,IAAI,CAAC,CAE5B;AACA,GAAI2D,IAAI,GAAK,QAAQ,EAAIlC,MAAM,CAACD,MAAM,CAAGpB,YAAY,CAACoB,MAAM,CAAE,CAC5DqC,YAAY,CAAGA,YAAY,CAAChC,MAAM,CAACD,IAAI,EAAIH,MAAM,CAACuB,QAAQ,CAACpB,IAAI,CAAC3B,UAAU,CAAC,CAAC,CAAC,CAC/E,CAAC,IAAM,IAAI0D,IAAI,GAAK,UAAU,CAAE,CAC9BE,YAAY,CAAGA,YAAY,CAAChC,MAAM,CAACD,IAAI,EACrCA,IAAI,CAAC3B,UAAU,CAAC,EAAIkC,MAAM,CAACP,IAAI,CAAC3B,UAAU,CAAC,CAAC,CAAC6D,WAAW,CAAC,CAAC,CAACd,QAAQ,CAACxC,UAAU,CAACsD,WAAW,CAAC,CAAC,CAC9F,CAAC,CACH,CAAC,IAAM,IAAIH,IAAI,GAAK,aAAa,EAAIC,KAAK,CAACzC,GAAG,GAAK,EAAE,CAAE,CACrD0C,YAAY,CAAGA,YAAY,CAAChC,MAAM,CAACD,IAAI,EACrCM,UAAU,CAACN,IAAI,CAAC3B,UAAU,CAAC,CAAC,CAAGiC,UAAU,CAAC0B,KAAK,CAACzC,GAAG,CACrD,CAAC,CACH,CAAC,IAAM,IAAIwC,IAAI,GAAK,UAAU,EAAIC,KAAK,CAACxC,GAAG,GAAK,EAAE,CAAE,CAClDyC,YAAY,CAAGA,YAAY,CAAChC,MAAM,CAACD,IAAI,EACrCM,UAAU,CAACN,IAAI,CAAC3B,UAAU,CAAC,CAAC,CAAGiC,UAAU,CAAC0B,KAAK,CAACxC,GAAG,CACrD,CAAC,CACH,CAAC,IAAM,IAAIuC,IAAI,GAAK,SAAS,EAAIC,KAAK,CAACzC,GAAG,GAAK,EAAE,EAAIyC,KAAK,CAACxC,GAAG,GAAK,EAAE,CAAE,CACrEyC,YAAY,CAAGA,YAAY,CAAChC,MAAM,CAACD,IAAI,EACrCM,UAAU,CAACN,IAAI,CAAC3B,UAAU,CAAC,CAAC,EAAIiC,UAAU,CAAC0B,KAAK,CAACzC,GAAG,CAAC,EACrDe,UAAU,CAACN,IAAI,CAAC3B,UAAU,CAAC,CAAC,EAAIiC,UAAU,CAAC0B,KAAK,CAACxC,GAAG,CACtD,CAAC,CACH,CAEA;AACA,GAAIW,IAAI,CAAE,CACR8B,YAAY,CAAC9B,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAC1B,GAAI,CAAA8B,MAAM,CAAG/B,CAAC,CAAC/B,UAAU,CAAC,CAC1B,GAAI,CAAA+D,MAAM,CAAG/B,CAAC,CAAChC,UAAU,CAAC,CAE1B,GAAIE,QAAQ,GAAK,QAAQ,CAAE,CACzB4D,MAAM,CAAG7B,UAAU,CAAC6B,MAAM,CAAC,EAAI,CAAC,CAChCC,MAAM,CAAG9B,UAAU,CAAC8B,MAAM,CAAC,EAAI,CAAC,CAClC,CAAC,IAAM,CACLD,MAAM,CAAG5B,MAAM,CAAC4B,MAAM,EAAI,EAAE,CAAC,CAC7BC,MAAM,CAAG7B,MAAM,CAAC6B,MAAM,EAAI,EAAE,CAAC,CAC/B,CAEA,GAAIjC,IAAI,GAAK,KAAK,CAAE,CAClB,MAAO,CAAAgC,MAAM,CAAGC,MAAM,CAAG,CAAC,CAAG,CAAC,CAAC,CACjC,CAAC,IAAM,CACL,MAAO,CAAAD,MAAM,CAAGC,MAAM,CAAG,CAAC,CAAG,CAAC,CAAC,CACjC,CACF,CAAC,CAAC,CACJ,CAEA9D,cAAc,CAAC2D,YAAY,CAAE,CAC3B5D,UAAU,CACVc,UAAU,CAAE4C,IAAI,CAChBhD,cAAc,CAAEc,MAAM,CACtBjB,UAAU,CACVS,WAAW,CAAE2C,KAAK,CAClBvC,aAAa,CAAEU,IACjB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAkC,WAAW,CAAGA,CAAA,GAAM,CACxBT,WAAW,CAAC,CAAC,CACbhB,WAAW,CAAC,CAAC,CACf,CAAC,CAED,KAAM,CAAA0B,WAAW,CAAGA,CAAA,GAAM,CACxBtD,iBAAiB,CAACR,YAAY,CAAC,CAC/BU,YAAY,CAAC,IAAI,CAAC,CAClBE,aAAa,CAAC,QAAQ,CAAC,CACvBP,aAAa,CAAC,EAAE,CAAC,CACjBS,cAAc,CAAC,CAAEC,GAAG,CAAE,EAAE,CAAEC,GAAG,CAAE,EAAG,CAAC,CAAC,CACpCE,gBAAgB,CAAC,IAAI,CAAC,CAEtBpB,cAAc,CAACF,IAAI,CAAE,CACnBC,UAAU,CACVc,UAAU,CAAE,QAAQ,CACpBJ,cAAc,CAAEP,YAAY,CAC5BI,UAAU,CAAE,EAAE,CACdS,WAAW,CAAE,CAAEE,GAAG,CAAE,EAAE,CAAEC,GAAG,CAAE,EAAG,CAAC,CACjCC,aAAa,CAAE,IACjB,CAAC,CAAC,CAEFmB,WAAW,CAAC,CAAC,CACf,CAAC,CAED,KAAM,CAAA2B,IAAI,CAAGrC,OAAO,CAACxB,QAAQ,CAAC,CAC9B,KAAM,CAAA8D,EAAE,CAAGD,IAAI,CAAG,kBAAkBlE,UAAU,EAAE,CAAGyD,SAAS,CAE5D;AACA,KAAM,CAAAW,oBAAoB,CAAGjE,YAAY,CAACyB,MAAM,CAACc,KAAK,EACpDR,MAAM,CAACQ,KAAK,CAAC,CAACmB,WAAW,CAAC,CAAC,CAACd,QAAQ,CAACxC,UAAU,CAACsD,WAAW,CAAC,CAAC,CAC/D,CAAC,CAED,mBACEnE,KAAA,CAAChC,GAAG,EAAA2G,QAAA,eACF7E,IAAA,CAACnB,UAAU,EACT,mBAAkB8F,EAAG,CACrBG,OAAO,CAAElC,WAAY,CACrBmC,IAAI,CAAC,OAAO,CACZC,KAAK,CACF9D,cAAc,CAACa,MAAM,CAAGpB,YAAY,CAACoB,MAAM,EAC3CT,UAAU,GAAK,QAAQ,EACvBM,aAAa,CAAI,SAAS,CAAG,SAC/B,CACDqD,EAAE,CAAE,CACFC,QAAQ,CAAE,UAAU,CACpB,UAAU,CAAGhE,cAAc,CAACa,MAAM,CAAGpB,YAAY,CAACoB,MAAM,EAC3CT,UAAU,GAAK,QAAQ,EACvBM,aAAa,CAAI,CAC5BuD,OAAO,CAAE,IAAI,CACbD,QAAQ,CAAE,UAAU,CACpBE,GAAG,CAAE,CAAC,CACNC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,KAAK,CACnBC,eAAe,CAAE,SACnB,CAAC,CAAG,CAAC,CACP,CAAE,CAAAZ,QAAA,cAEF7E,IAAA,CAACV,UAAU,EAACoG,QAAQ,CAAC,OAAO,CAAE,CAAC,CACrB,CAAC,cAEb1F,IAAA,CAAC7B,OAAO,EACNwG,EAAE,CAAEA,EAAG,CACPD,IAAI,CAAEA,IAAK,CACX7D,QAAQ,CAAEA,QAAS,CACnB8E,OAAO,CAAE5C,WAAY,CACrB6C,YAAY,CAAE,CACZC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,MACd,CAAE,CACFC,eAAe,CAAE,CACfF,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,MACd,CAAE,CACFE,UAAU,CAAE,CACVC,KAAK,CAAE,CAAEX,KAAK,CAAE,OAAO,CAAEY,SAAS,CAAE,OAAQ,CAC9C,CAAE,CAAArB,QAAA,cAEF3E,KAAA,CAAChC,GAAG,EAAC+G,EAAE,CAAE,CAAEkB,CAAC,CAAE,CAAE,CAAE,CAAAtB,QAAA,eAChB3E,KAAA,CAAC9B,UAAU,EAACgI,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAxB,QAAA,EAAC,UACnC,CAACrE,UAAU,EACT,CAAC,cAEbN,KAAA,CAAChC,GAAG,EAAC+G,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAgB,CAAE,CAAA3B,QAAA,eACnE7E,IAAA,CAACnB,UAAU,EACTkG,IAAI,CAAC,OAAO,CACZD,OAAO,CAAEA,CAAA,GAAMjB,UAAU,CAAC,KAAK,CAAE,CACjCmB,KAAK,CAAEpD,aAAa,GAAK,KAAK,CAAG,SAAS,CAAG,SAAU,CACvD6E,KAAK,CAAC,wBAAwB,CAAA5B,QAAA,cAE9B7E,IAAA,CAACN,eAAe,EAACgG,QAAQ,CAAC,OAAO,CAAE,CAAC,CAC1B,CAAC,cACb1F,IAAA,CAACnB,UAAU,EACTkG,IAAI,CAAC,OAAO,CACZD,OAAO,CAAEA,CAAA,GAAMjB,UAAU,CAAC,MAAM,CAAE,CAClCmB,KAAK,CAAEpD,aAAa,GAAK,MAAM,CAAG,SAAS,CAAG,SAAU,CACxD6E,KAAK,CAAC,0BAA0B,CAAA5B,QAAA,cAEhC7E,IAAA,CAACJ,iBAAiB,EAAC8F,QAAQ,CAAC,OAAO,CAAE,CAAC,CAC5B,CAAC,cACb1F,IAAA,CAACnB,UAAU,EACTkG,IAAI,CAAC,OAAO,CACZD,OAAO,CAAEL,WAAY,CACrBgC,KAAK,CAAC,iBAAiB,CAAA5B,QAAA,cAEvB7E,IAAA,CAACF,SAAS,EAAC4F,QAAQ,CAAC,OAAO,CAAE,CAAC,CACpB,CAAC,EACV,CAAC,cAEN1F,IAAA,CAACvB,OAAO,EAACwG,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAEzB5F,QAAQ,GAAK,MAAM,eAClBR,KAAA,CAACjB,WAAW,EAACyH,SAAS,MAAC3B,IAAI,CAAC,OAAO,CAACE,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,eAChD7E,IAAA,CAAC5B,UAAU,EAACgI,OAAO,CAAC,OAAO,CAACnB,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,CAAC,iBAAe,CAAY,CAAC,cACvE3E,KAAA,CAACf,UAAU,EACT+D,KAAK,CAAE5B,UAAW,CAClBqF,QAAQ,CAAElD,sBAAuB,CACjCmD,GAAG,MAAA/B,QAAA,eAEH7E,IAAA,CAACzB,gBAAgB,EACf2E,KAAK,CAAC,QAAQ,CACd2D,OAAO,cAAE7G,IAAA,CAACZ,KAAK,EAAC2F,IAAI,CAAC,OAAO,CAAE,CAAE,CAChC+B,KAAK,CAAC,eAAY,CACnB,CAAC,cACF9G,IAAA,CAACzB,gBAAgB,EACf2E,KAAK,CAAC,UAAU,CAChB2D,OAAO,cAAE7G,IAAA,CAACZ,KAAK,EAAC2F,IAAI,CAAC,OAAO,CAAE,CAAE,CAChC+B,KAAK,CAAC,UAAU,CACjB,CAAC,EACQ,CAAC,EACF,CACd,CAEApG,QAAQ,GAAK,QAAQ,eACpBR,KAAA,CAACjB,WAAW,EAACyH,SAAS,MAAC3B,IAAI,CAAC,OAAO,CAACE,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,eAChD7E,IAAA,CAAC5B,UAAU,EAACgI,OAAO,CAAC,OAAO,CAACnB,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,CAAC,iBAAe,CAAY,CAAC,cACvE3E,KAAA,CAACf,UAAU,EACT+D,KAAK,CAAE5B,UAAW,CAClBqF,QAAQ,CAAElD,sBAAuB,CACjCwB,EAAE,CAAE,CAAEsB,OAAO,CAAE,MAAM,CAAEQ,aAAa,CAAE,QAAS,CAAE,CAAAlC,QAAA,eAEjD7E,IAAA,CAACzB,gBAAgB,EACf2E,KAAK,CAAC,QAAQ,CACd2D,OAAO,cAAE7G,IAAA,CAACZ,KAAK,EAAC2F,IAAI,CAAC,OAAO,CAAE,CAAE,CAChC+B,KAAK,CAAC,eAAY,CACnB,CAAC,cACF9G,IAAA,CAACzB,gBAAgB,EACf2E,KAAK,CAAC,aAAa,CACnB2D,OAAO,cAAE7G,IAAA,CAACZ,KAAK,EAAC2F,IAAI,CAAC,OAAO,CAAE,CAAE,CAChC+B,KAAK,CAAC,aAAa,CACpB,CAAC,cACF9G,IAAA,CAACzB,gBAAgB,EACf2E,KAAK,CAAC,UAAU,CAChB2D,OAAO,cAAE7G,IAAA,CAACZ,KAAK,EAAC2F,IAAI,CAAC,OAAO,CAAE,CAAE,CAChC+B,KAAK,CAAC,WAAW,CAClB,CAAC,cACF9G,IAAA,CAACzB,gBAAgB,EACf2E,KAAK,CAAC,SAAS,CACf2D,OAAO,cAAE7G,IAAA,CAACZ,KAAK,EAAC2F,IAAI,CAAC,OAAO,CAAE,CAAE,CAChC+B,KAAK,CAAC,cAAc,CACrB,CAAC,EACQ,CAAC,EACF,CACd,CAEAxF,UAAU,GAAK,UAAU,eACxBtB,IAAA,CAAC3B,SAAS,EACRqI,SAAS,MACT3B,IAAI,CAAC,OAAO,CACZ+B,KAAK,CAAC,OAAO,CACbV,OAAO,CAAC,UAAU,CAClBlD,KAAK,CAAEnC,UAAW,CAClB4F,QAAQ,CAAE3D,YAAa,CACvBiC,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CACdU,UAAU,CAAE,CACVC,cAAc,cACZjH,IAAA,CAAClB,cAAc,EAACoG,QAAQ,CAAC,OAAO,CAAAL,QAAA,cAC9B7E,IAAA,CAACR,UAAU,EAACkG,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjB,CAEpB,CAAE,CACH,CACF,CAEApE,UAAU,GAAK,aAAa,eAC3BtB,IAAA,CAAC3B,SAAS,EACRqI,SAAS,MACT3B,IAAI,CAAC,OAAO,CACZ+B,KAAK,CAAC,eAAe,CACrBV,OAAO,CAAC,UAAU,CAClBlC,IAAI,CAAC,QAAQ,CACbhB,KAAK,CAAE1B,WAAW,CAACE,GAAI,CACvBiF,QAAQ,CAAGO,CAAC,EAAKxD,iBAAiB,CAAC,KAAK,CAAEwD,CAAC,CAACjE,MAAM,CAACC,KAAK,CAAE,CAC1D+B,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CACf,CACF,CAEAhF,UAAU,GAAK,UAAU,eACxBtB,IAAA,CAAC3B,SAAS,EACRqI,SAAS,MACT3B,IAAI,CAAC,OAAO,CACZ+B,KAAK,CAAC,gBAAgB,CACtBV,OAAO,CAAC,UAAU,CAClBlC,IAAI,CAAC,QAAQ,CACbhB,KAAK,CAAE1B,WAAW,CAACG,GAAI,CACvBgF,QAAQ,CAAGO,CAAC,EAAKxD,iBAAiB,CAAC,KAAK,CAAEwD,CAAC,CAACjE,MAAM,CAACC,KAAK,CAAE,CAC1D+B,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CACf,CACF,CAEAhF,UAAU,GAAK,SAAS,eACvBpB,KAAA,CAAChC,GAAG,EAAC+G,EAAE,CAAE,CAAEsB,OAAO,CAAE,MAAM,CAAEY,GAAG,CAAE,CAAC,CAAEb,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,eAC1C7E,IAAA,CAAC3B,SAAS,EACRqI,SAAS,MACT3B,IAAI,CAAC,OAAO,CACZ+B,KAAK,CAAC,IAAI,CACVV,OAAO,CAAC,UAAU,CAClBlC,IAAI,CAAC,QAAQ,CACbhB,KAAK,CAAE1B,WAAW,CAACE,GAAI,CACvBiF,QAAQ,CAAGO,CAAC,EAAKxD,iBAAiB,CAAC,KAAK,CAAEwD,CAAC,CAACjE,MAAM,CAACC,KAAK,CAAE,CAC3D,CAAC,cACFlD,IAAA,CAAC3B,SAAS,EACRqI,SAAS,MACT3B,IAAI,CAAC,OAAO,CACZ+B,KAAK,CAAC,GAAG,CACTV,OAAO,CAAC,UAAU,CAClBlC,IAAI,CAAC,QAAQ,CACbhB,KAAK,CAAE1B,WAAW,CAACG,GAAI,CACvBgF,QAAQ,CAAGO,CAAC,EAAKxD,iBAAiB,CAAC,KAAK,CAAEwD,CAAC,CAACjE,MAAM,CAACC,KAAK,CAAE,CAC3D,CAAC,EACC,CACN,CAEA5B,UAAU,GAAK,QAAQ,eACtBpB,KAAA,CAAAE,SAAA,EAAAyE,QAAA,eACE7E,IAAA,CAAC3B,SAAS,EACRqI,SAAS,MACT3B,IAAI,CAAC,OAAO,CACZ+B,KAAK,CAAC,cAAc,CACpBV,OAAO,CAAC,UAAU,CAClBlD,KAAK,CAAEnC,UAAW,CAClB4F,QAAQ,CAAE3D,YAAa,CACvBiC,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CACdU,UAAU,CAAE,CACVC,cAAc,cACZjH,IAAA,CAAClB,cAAc,EAACoG,QAAQ,CAAC,OAAO,CAAAL,QAAA,cAC9B7E,IAAA,CAACR,UAAU,EAACkG,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjB,CAEpB,CAAE,CACF5D,QAAQ,CAAEA,QAAS,CACpB,CAAC,cAEF9B,IAAA,CAACzB,gBAAgB,EACfsI,OAAO,cACL7G,IAAA,CAAC1B,QAAQ,EACP8E,OAAO,CAAEhC,SAAU,CACnBuF,QAAQ,CAAExD,eAAgB,CAC1B4B,IAAI,CAAC,OAAO,CACb,CACF,CACD+B,KAAK,CAAC,iBAAiB,CACvB7B,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cAEFtG,IAAA,CAACvB,OAAO,EAACwG,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAE1BpG,KAAA,CAACxB,IAAI,EAACuG,EAAE,CAAE,CAAEiB,SAAS,CAAE,OAAO,CAAEkB,QAAQ,CAAE,MAAM,CAAEd,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,EACvDD,oBAAoB,CAAC1C,GAAG,CAAC,CAACgB,KAAK,CAAEmE,KAAK,gBACrCrH,IAAA,CAACrB,QAAQ,EAAa2I,KAAK,MAACC,cAAc,MAAA1C,QAAA,cACxC7E,IAAA,CAACzB,gBAAgB,EACfsI,OAAO,cACL7G,IAAA,CAAC1B,QAAQ,EACP8E,OAAO,CAAElC,cAAc,CAACqC,QAAQ,CAACL,KAAK,CAAE,CACxCyD,QAAQ,CAAEA,CAAA,GAAMtD,iBAAiB,CAACH,KAAK,CAAE,CACzC6B,IAAI,CAAC,OAAO,CACb,CACF,CACD+B,KAAK,cACH9G,IAAA,CAACpB,YAAY,EACX4I,OAAO,CAAEtE,KAAK,EAAI,SAAU,CAC5BuE,sBAAsB,CAAE,CACtBrB,OAAO,CAAE,OAAO,CAChBH,KAAK,CAAE,CACLmB,QAAQ,CAAE,QAAQ,CAClBM,YAAY,CAAE,UAAU,CACxBC,UAAU,CAAE,QACd,CACF,CAAE,CACH,CACF,CACD1C,EAAE,CAAE,CAAE2C,CAAC,CAAE,CAAC,CAAEtC,KAAK,CAAE,MAAO,CAAE,CAC7B,CAAC,EAvBW+B,KAwBL,CACX,CAAC,CACDzC,oBAAoB,CAAC7C,MAAM,GAAK,CAAC,eAChC/B,IAAA,CAACrB,QAAQ,EAAAkG,QAAA,cACP7E,IAAA,CAACpB,YAAY,EACX4I,OAAO,CAAC,uBAAuB,CAC/BC,sBAAsB,CAAE,CAAErB,OAAO,CAAE,OAAQ,CAAE,CAC9C,CAAC,CACM,CACX,EACG,CAAC,EACP,CACH,cAEDlG,KAAA,CAAChC,GAAG,EAAC+G,EAAE,CAAE,CAAEsB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,UAAU,CAAEW,GAAG,CAAE,CAAE,CAAE,CAAAtC,QAAA,eAC/D7E,IAAA,CAACxB,MAAM,EACL4H,OAAO,CAAC,UAAU,CAClBrB,IAAI,CAAC,OAAO,CACZD,OAAO,CAAE/B,WAAY,CAAA8B,QAAA,CACtB,SAED,CAAQ,CAAC,cACT7E,IAAA,CAACxB,MAAM,EACL4H,OAAO,CAAC,WAAW,CACnBrB,IAAI,CAAC,OAAO,CACZD,OAAO,CAAEN,WAAY,CACrBQ,KAAK,CAAC,SAAS,CAAAH,QAAA,CAChB,SAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACC,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}