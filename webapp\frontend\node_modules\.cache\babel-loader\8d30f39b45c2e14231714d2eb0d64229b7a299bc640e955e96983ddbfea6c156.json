{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\PosaCaviCollegamenti.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Paper, Divider, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Grid, Alert, CircularProgress, FormHelperText, Radio, RadioGroup, FormControlLabel, FormLabel } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Cable as CableIcon, Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PosaCaviCollegamenti = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  initialOption = null\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n\n  // Inizializza il componente con l'opzione iniziale se specificata\n  React.useEffect(() => {\n    if (initialOption) {\n      // Imposta direttamente le opzioni invece di chiamare handleOptionSelect\n      // per evitare dipendenze circolari\n      setSelectedOption(initialOption);\n      if (initialOption === 'eliminaCavo') {\n        loadCavi();\n        setDialogType('eliminaCavo');\n        setOpenDialog(true);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [initialOption]);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      setCavi(caviData);\n    } catch (error) {\n      onError('Errore nel caricamento dei cavi');\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi();\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      // Reindirizza alla pagina di aggiunta cavo\n      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/posa/aggiungi-cavo`;\n    } else if (option === 'modificaCavo') {\n      loadCavi();\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi();\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    } else if (option === 'collegamentoCavo') {\n      // Reindirizza alla pagina di gestione collegamenti\n      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/collegamenti`;\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n\n    // Se è stato specificato un initialOption, notifica il genitore che il dialogo è stato chiuso\n    if (initialOption && onSuccess) {\n      onSuccess('Operazione annullata');\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({\n            metri_posati: 'Inserire un valore numerico valido'\n          });\n          setLoading(false);\n          return;\n        }\n        await caviService.updateMetriPosati(cantiereId, formData.id_cavo, parseFloat(formData.metri_posati));\n        onSuccess('Metri posati aggiornati con successo');\n      } else if (dialogType === 'modificaBobina') {\n        await caviService.updateBobina(cantiereId, formData.id_cavo, formData.id_bobina);\n        onSuccess('Bobina aggiornata con successo');\n      } else if (dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n        await caviService.updateCavo(cantiereId, validatedData.id_cavo, validatedData);\n        onSuccess('Cavo modificato con successo');\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        // Verifica se il cavo è installato\n        const isInstalled = selectedCavo.stato_installazione === 'Installato' || selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0;\n        if (isInstalled) {\n          // Se è installato, marca solo come SPARE\n          await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo);\n          onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n        } else {\n          // Se non è installato, usa la modalità selezionata (SPARE o DELETE)\n          await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n          onSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n        }\n      }\n      handleCloseDialog();\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'inserisciMetri') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Inserisci Metri Posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Seleziona un cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                button: true,\n                onClick: () => handleCavoSelect(cavo),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: cavo.id_cavo,\n                  secondary: `${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 23\n                }, this)\n              }, cavo.id_cavo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metri teorici: \", selectedCavo.metri_teorici || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metratura attuale: \", selectedCavo.metratura_reale || '0']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"dense\",\n              name: \"metri_posati\",\n              label: \"Metri posati da aggiungere\",\n              type: \"number\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_posati,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_posati,\n              helperText: formErrors.metri_posati,\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || !formData.metri_posati,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 71\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'modificaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Bobina Cavo Posato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Seleziona un cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                button: true,\n                onClick: () => handleCavoSelect(cavo),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: cavo.id_cavo,\n                  secondary: `Bobina attuale: ${cavo.id_bobina || 'Non assegnata'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 23\n                }, this)\n              }, cavo.id_cavo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Bobina attuale: \", selectedCavo.id_bobina || 'Non assegnata']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"dense\",\n              name: \"id_bobina\",\n              label: \"ID Bobina\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_bobina,\n              onChange: handleFormChange,\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 71\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaCavo' || dialogType === 'eliminaCavo') {\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = selectedCavo && (selectedCavo.stato_installazione === 'Installato' || selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0);\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: dialogType === 'selezionaCavo' ? 'Seleziona Cavo da Modificare' : !selectedCavo ? 'Seleziona Cavo da Eliminare' : isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(List, {\n            children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: cavo.id_cavo,\n                secondary: `${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 21\n              }, this)\n            }, cavo.id_cavo, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 15\n          }, this) : dialogType === 'eliminaCavo' && isInstalled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n              children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 27\n              }, this), \" risulta installato o parzialmente posato.\", selectedCavo.metratura_reale > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [\" Metri posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [selectedCavo.metratura_reale, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 38\n                }, this), \".\"]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DialogContentText, {\n              sx: {\n                mt: 2\n              },\n              children: \"Non \\xE8 possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : dialogType === 'eliminaCavo' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n              children: [\"Stai per eliminare il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 46\n              }, this), \".\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              component: \"fieldset\",\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n                component: \"legend\",\n                children: \"Scegli l'operazione da eseguire:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n                value: deleteMode,\n                onChange: e => setDeleteMode(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  value: \"spare\",\n                  control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 32\n                  }, this),\n                  label: \"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  value: \"delete\",\n                  control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 32\n                  }, this),\n                  label: \"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this), dialogType === 'eliminaCavo' && selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: isInstalled ? \"warning\" : \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 38\n            }, this) : isInstalled ? /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 85\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 103\n            }, this),\n            children: isInstalled ? \"Marca come SPARE\" : deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'modificaCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"id_cavo\",\n                label: \"ID Cavo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.id_cavo,\n                disabled: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"revisione_ufficiale\",\n                label: \"Revisione Ufficiale\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.revisione_ufficiale,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sistema\",\n                label: \"Sistema\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sistema,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utility\",\n                label: \"Utility\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utility,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"colore_cavo\",\n                label: \"Colore Cavo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.colore_cavo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"tipologia\",\n                label: \"Tipologia\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.tipologia,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_conduttori\",\n                label: \"Numero Conduttori\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_conduttori,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sezione\",\n                label: \"Sezione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sezione,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sh\",\n                label: \"SH\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sh || formData.SH,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_partenza\",\n                label: \"Ubicazione Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utenza_partenza\",\n                label: \"Utenza Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utenza_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"descrizione_utenza_partenza\",\n                label: \"Descrizione Utenza Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.descrizione_utenza_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_arrivo\",\n                label: \"Ubicazione Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utenza_arrivo\",\n                label: \"Utenza Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utenza_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"descrizione_utenza_arrivo\",\n                label: \"Descrizione Utenza Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.descrizione_utenza_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_teorici\",\n                label: \"Metri Teorici\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_teorici,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"id_bobina\",\n                label: \"ID Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.id_bobina,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Stato Installazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"stato_installazione\",\n                  value: formData.stato_installazione,\n                  label: \"Stato Installazione\",\n                  onChange: handleFormChange,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Da installare\",\n                    children: \"Da installare\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 671,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"In corso\",\n                    children: \"In corso\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Installato\",\n                    children: \"Installato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 673,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"SPARE\",\n                    children: \"SPARE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 674,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 69\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 680,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '280px',\n        mr: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Posa Cavi e Collegamenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          dense: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('inserisciMetri'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"1. Inserisci metri posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('modificaBobina'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 716,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"2. Modifica bobina cavo posato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('aggiungiCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"3. Aggiungi nuovo cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('modificaCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"4. Modifica cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('eliminaCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"5. Elimina cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('collegamentoCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"6. Collegamento cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 701,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 700,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flexGrow: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          minHeight: '300px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: [!selectedOption && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          children: \"Seleziona un'opzione dal menu a sinistra per iniziare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 756,\n          columnNumber: 13\n        }, this), selectedOption && !openDialog && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: [selectedOption === 'inserisciMetri' && 'Inserisci metri posati', selectedOption === 'modificaCavo' && 'Modifica cavo', selectedOption === 'aggiungiCavo' && 'Aggiungi nuovo cavo', selectedOption === 'eliminaCavo' && 'Elimina cavo', selectedOption === 'modificaBobina' && 'Modifica bobina cavo posato', selectedOption === 'collegamentoCavo' && 'Collegamento cavo']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 762,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Caricamento in corso...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 770,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n            sx: {\n              mt: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 773,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 754,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 753,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 698,\n    columnNumber: 5\n  }, this);\n};\n_s(PosaCaviCollegamenti, \"rJgpKvklhCAha+37suBEac3Umps=\");\n_c = PosaCaviCollegamenti;\nexport default PosaCaviCollegamenti;\nvar _c;\n$RefreshReg$(_c, \"PosaCaviCollegamenti\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Divider", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Grid", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "Radio", "RadioGroup", "FormControlLabel", "FormLabel", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Cable", "CableIcon", "Save", "SaveIcon", "Warning", "WarningIcon", "caviService", "validateCavoData", "validateField", "isEmpty", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PosaCaviCollegamenti", "cantiereId", "onSuccess", "onError", "initialOption", "_s", "loading", "setLoading", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "cavoIdInput", "setCavoIdInput", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "cavi", "<PERSON><PERSON><PERSON>", "caviLoading", "setCaviLoading", "deleteMode", "setDeleteMode", "useEffect", "loadCavi", "caviData", "get<PERSON><PERSON>", "error", "console", "handleOptionSelect", "option", "window", "location", "href", "handleCloseDialog", "handleCavoSelect", "cavo", "metri_te<PERSON>ci", "metratura_reale", "handleFormChange", "e", "name", "value", "target", "additionalParams", "metriTeorici", "parseFloat", "result", "prev", "valid", "message", "warning", "handleSave", "isNaN", "updateMetri<PERSON><PERSON><PERSON>", "updateBobina", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "validatedData", "updateCavo", "Object", "keys", "length", "warningMessages", "values", "join", "warn", "isInstalled", "stato_installazione", "markCavoAsSpare", "deleteCavo", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "variant", "gutterBottom", "map", "button", "onClick", "primary", "secondary", "tipologia", "ubicazione_partenza", "ubicazione_arrivo", "sx", "mt", "margin", "label", "type", "onChange", "required", "helperText", "disabled", "startIcon", "size", "component", "control", "color", "container", "spacing", "item", "xs", "sm", "revisione_ufficiale", "sistema", "utility", "colore_cavo", "n_conduttori", "sezione", "sh", "SH", "utenza_partenza", "descrizione_utenza_partenza", "utenza_arrivo", "descrizione_utenza_arrivo", "display", "width", "mr", "p", "mb", "dense", "flexGrow", "minHeight", "alignItems", "justifyContent", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/PosaCaviCollegamenti.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogContentText,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Grid,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  FormLabel\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Cable as CableIcon,\n  Save as SaveIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\n\nconst PosaCaviCollegamenti = ({ cantiereId, onSuccess, onError, initialOption = null }) => {\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n\n  // Inizializza il componente con l'opzione iniziale se specificata\n  React.useEffect(() => {\n    if (initialOption) {\n      // Imposta direttamente le opzioni invece di chiamare handleOptionSelect\n      // per evitare dipendenze circolari\n      setSelectedOption(initialOption);\n\n      if (initialOption === 'eliminaCavo') {\n        loadCavi();\n        setDialogType('eliminaCavo');\n        setOpenDialog(true);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [initialOption]);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      setCavi(caviData);\n    } catch (error) {\n      onError('Errore nel caricamento dei cavi');\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi();\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      // Reindirizza alla pagina di aggiunta cavo\n      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/posa/aggiungi-cavo`;\n    } else if (option === 'modificaCavo') {\n      loadCavi();\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi();\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    } else if (option === 'collegamentoCavo') {\n      // Reindirizza alla pagina di gestione collegamenti\n      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/collegamenti`;\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n\n    // Se è stato specificato un initialOption, notifica il genitore che il dialogo è stato chiuso\n    if (initialOption && onSuccess) {\n      onSuccess('Operazione annullata');\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({ metri_posati: 'Inserire un valore numerico valido' });\n          setLoading(false);\n          return;\n        }\n\n        await caviService.updateMetriPosati(\n          cantiereId,\n          formData.id_cavo,\n          parseFloat(formData.metri_posati)\n        );\n        onSuccess('Metri posati aggiornati con successo');\n      } else if (dialogType === 'modificaBobina') {\n        await caviService.updateBobina(\n          cantiereId,\n          formData.id_cavo,\n          formData.id_bobina\n        );\n        onSuccess('Bobina aggiornata con successo');\n      } else if (dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n        await caviService.updateCavo(cantiereId, validatedData.id_cavo, validatedData);\n        onSuccess('Cavo modificato con successo');\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        // Verifica se il cavo è installato\n        const isInstalled = selectedCavo.stato_installazione === 'Installato' || (selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0);\n\n        if (isInstalled) {\n          // Se è installato, marca solo come SPARE\n          await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo);\n          onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n        } else {\n          // Se non è installato, usa la modalità selezionata (SPARE o DELETE)\n          await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n          onSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n        }\n      }\n\n      handleCloseDialog();\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'inserisciMetri') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Inserisci Metri Posati</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metri teorici: {selectedCavo.metri_teorici || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metratura attuale: {selectedCavo.metratura_reale || '0'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"metri_posati\"\n                  label=\"Metri posati da aggiungere\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_posati}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_posati}\n                  helperText={formErrors.metri_posati}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading || !formData.metri_posati}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Bobina Cavo Posato</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`Bobina attuale: ${cavo.id_bobina || 'Non assegnata'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Bobina attuale: {selectedCavo.id_bobina || 'Non assegnata'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"id_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_bobina}\n                  onChange={handleFormChange}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaCavo' || dialogType === 'eliminaCavo') {\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = selectedCavo && (selectedCavo.stato_installazione === 'Installato' || (selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0));\n\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {dialogType === 'selezionaCavo' ? 'Seleziona Cavo da Modificare' :\n             !selectedCavo ? 'Seleziona Cavo da Eliminare' :\n             isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'}\n          </DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <List>\n                {cavi.map((cavo) => (\n                  <ListItem\n                    button\n                    key={cavo.id_cavo}\n                    onClick={() => handleCavoSelect(cavo)}\n                  >\n                    <ListItemText\n                      primary={cavo.id_cavo}\n                      secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            ) : dialogType === 'eliminaCavo' && isInstalled ? (\n              <>\n                <DialogContentText>\n                  Il cavo <strong>{selectedCavo.id_cavo}</strong> risulta installato o parzialmente posato.\n                  {selectedCavo.metratura_reale > 0 && (\n                    <> Metri posati: <strong>{selectedCavo.metratura_reale} m</strong>.</>\n                  )}\n                </DialogContentText>\n                <DialogContentText sx={{ mt: 2 }}>\n                  Non è possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\n                </DialogContentText>\n              </>\n            ) : dialogType === 'eliminaCavo' ? (\n              <>\n                <DialogContentText>\n                  Stai per eliminare il cavo <strong>{selectedCavo.id_cavo}</strong>.\n                </DialogContentText>\n\n                <FormControl component=\"fieldset\" sx={{ mt: 2 }}>\n                  <FormLabel component=\"legend\">Scegli l'operazione da eseguire:</FormLabel>\n                  <RadioGroup\n                    value={deleteMode}\n                    onChange={(e) => setDeleteMode(e.target.value)}\n                  >\n                    <FormControlLabel\n                      value=\"spare\"\n                      control={<Radio />}\n                      label=\"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                    />\n                    <FormControlLabel\n                      value=\"delete\"\n                      control={<Radio />}\n                      label=\"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                    />\n                  </RadioGroup>\n                </FormControl>\n              </>\n            ) : null}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {dialogType === 'eliminaCavo' && selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color={isInstalled ? \"warning\" : \"error\"}\n                startIcon={loading ? <CircularProgress size={20} /> : isInstalled ? <WarningIcon /> : <DeleteIcon />}\n              >\n                {isInstalled ? \"Marca come SPARE\" : (deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\")}\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Cavo</DialogTitle>\n          <DialogContent>\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  disabled\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"revisione_ufficiale\"\n                  label=\"Revisione Ufficiale\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.revisione_ufficiale}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sistema\"\n                  label=\"Sistema\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sistema}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"colore_cavo\"\n                  label=\"Colore Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.colore_cavo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"Numero Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sh\"\n                  label=\"SH\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sh || formData.SH}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_partenza\"\n                  label=\"Ubicazione Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utenza_partenza\"\n                  label=\"Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"descrizione_utenza_partenza\"\n                  label=\"Descrizione Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_arrivo\"\n                  label=\"Ubicazione Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utenza_arrivo\"\n                  label=\"Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"descrizione_utenza_arrivo\"\n                  label=\"Descrizione Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_teorici\"\n                  label=\"Metri Teorici\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_teorici}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_bobina}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Stato Installazione</InputLabel>\n                  <Select\n                    name=\"stato_installazione\"\n                    value={formData.stato_installazione}\n                    label=\"Stato Installazione\"\n                    onChange={handleFormChange}\n                  >\n                    <MenuItem value=\"Da installare\">Da installare</MenuItem>\n                    <MenuItem value=\"In corso\">In corso</MenuItem>\n                    <MenuItem value=\"Installato\">Installato</MenuItem>\n                    <MenuItem value=\"SPARE\">SPARE</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      {/* Menu a cascata nella sidebar */}\n      <Box sx={{ width: '280px', mr: 3 }}>\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Posa Cavi e Collegamenti\n          </Typography>\n          <Divider sx={{ mb: 2 }} />\n          <List component=\"nav\" dense>\n            <ListItemButton onClick={() => handleOptionSelect('inserisciMetri')}>\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"1. Inserisci metri posati\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaBobina')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"2. Modifica bobina cavo posato\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('aggiungiCavo')}>\n              <ListItemIcon>\n                <AddIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"3. Aggiungi nuovo cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaCavo')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"4. Modifica cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('eliminaCavo')}>\n              <ListItemIcon>\n                <DeleteIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"5. Elimina cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('collegamentoCavo')}>\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"6. Collegamento cavo\" />\n            </ListItemButton>\n          </List>\n        </Paper>\n      </Box>\n\n      {/* Area principale per il contenuto */}\n      <Box sx={{ flexGrow: 1 }}>\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption && (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu a sinistra per iniziare.\n            </Typography>\n          )}\n          {selectedOption && !openDialog && (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'inserisciMetri' && 'Inserisci metri posati'}\n                {selectedOption === 'modificaCavo' && 'Modifica cavo'}\n                {selectedOption === 'aggiungiCavo' && 'Aggiungi nuovo cavo'}\n                {selectedOption === 'eliminaCavo' && 'Elimina cavo'}\n                {selectedOption === 'modificaBobina' && 'Modifica bobina cavo posato'}\n                {selectedOption === 'collegamentoCavo' && 'Collegamento cavo'}\n              </Typography>\n              <Typography variant=\"body1\">\n                Caricamento in corso...\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      </Box>\n\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default PosaCaviCollegamenti;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,SAAS,QACJ,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvF,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EACzF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2D,UAAU,EAAEC,aAAa,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiE,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAAC;IACvCmE,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwE,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC4E,IAAI,EAAEC,OAAO,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC8E,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgF,UAAU,EAAEC,aAAa,CAAC,GAAGjF,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEvD;EACAD,KAAK,CAACmF,SAAS,CAAC,MAAM;IACpB,IAAI7B,aAAa,EAAE;MACjB;MACA;MACAK,iBAAiB,CAACL,aAAa,CAAC;MAEhC,IAAIA,aAAa,KAAK,aAAa,EAAE;QACnC8B,QAAQ,CAAC,CAAC;QACVrB,aAAa,CAAC,aAAa,CAAC;QAC5BF,aAAa,CAAC,IAAI,CAAC;MACrB;IACF;IACA;EACF,CAAC,EAAE,CAACP,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM8B,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFJ,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMK,QAAQ,GAAG,MAAM3C,WAAW,CAAC4C,OAAO,CAACnC,UAAU,EAAE,CAAC,CAAC;MACzD2B,OAAO,CAACO,QAAQ,CAAC;IACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdlC,OAAO,CAAC,iCAAiC,CAAC;MAC1CmC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D,CAAC,SAAS;MACRP,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMS,kBAAkB,GAAIC,MAAM,IAAK;IACrC/B,iBAAiB,CAAC+B,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,gBAAgB,IAAIA,MAAM,KAAK,gBAAgB,EAAE;MAC9DN,QAAQ,CAAC,CAAC;MACVrB,aAAa,CAAC2B,MAAM,CAAC;MACrB7B,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI6B,MAAM,KAAK,cAAc,EAAE;MACpC;MACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuB1C,UAAU,0BAA0B;IACpF,CAAC,MAAM,IAAIuC,MAAM,KAAK,cAAc,EAAE;MACpCN,QAAQ,CAAC,CAAC;MACVrB,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI6B,MAAM,KAAK,aAAa,EAAE;MACnCN,QAAQ,CAAC,CAAC;MACVrB,aAAa,CAAC,aAAa,CAAC;MAC5BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI6B,MAAM,KAAK,kBAAkB,EAAE;MACxC;MACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuB1C,UAAU,oBAAoB;IAC9E;EACF,CAAC;;EAED;EACA,MAAM2C,iBAAiB,GAAGA,CAAA,KAAM;IAC9BjC,aAAa,CAAC,KAAK,CAAC;IACpBI,eAAe,CAAC,IAAI,CAAC;IACrBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFI,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;;IAEnB;IACA,IAAItB,aAAa,IAAIF,SAAS,EAAE;MAC9BA,SAAS,CAAC,sBAAsB,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAM2C,gBAAgB,GAAIC,IAAI,IAAK;IACjC/B,eAAe,CAAC+B,IAAI,CAAC;IACrB,IAAIlC,UAAU,KAAK,gBAAgB,EAAE;MACnCK,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAE4B,IAAI,CAAC5B,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIP,UAAU,KAAK,gBAAgB,EAAE;MAC1CK,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAE4B,IAAI,CAAC5B,OAAO;QACrBE,SAAS,EAAE0B,IAAI,CAAC1B,SAAS,IAAI;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIR,UAAU,KAAK,eAAe,EAAE;MACzCC,aAAa,CAAC,cAAc,CAAC;MAC7BI,WAAW,CAAC;QACV,GAAG6B,IAAI;QACPC,aAAa,EAAED,IAAI,CAACC,aAAa,IAAI,EAAE;QACvCC,eAAe,EAAEF,IAAI,CAACE,eAAe,IAAI;MAC3C,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACApC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACmC,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,IAAIxC,UAAU,KAAK,cAAc,EAAE;MACjC,MAAM0C,gBAAgB,GAAG,CAAC,CAAC;MAC3B,IAAIH,IAAI,KAAK,iBAAiB,EAAE;QAC9BG,gBAAgB,CAACC,YAAY,GAAGC,UAAU,CAACxC,QAAQ,CAAC+B,aAAa,IAAI,CAAC,CAAC;MACzE;MAEA,MAAMU,MAAM,GAAG/D,aAAa,CAACyD,IAAI,EAAEC,KAAK,EAAEE,gBAAgB,CAAC;;MAE3D;MACA9B,aAAa,CAACkC,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACP,IAAI,GAAG,CAACM,MAAM,CAACE,KAAK,GAAGF,MAAM,CAACG,OAAO,GAAG;MAC3C,CAAC,CAAC,CAAC;;MAEH;MACAlC,eAAe,CAACgC,IAAI,KAAK;QACvB,GAAGA,IAAI;QACP,CAACP,IAAI,GAAGM,MAAM,CAACI,OAAO,GAAGJ,MAAM,CAACG,OAAO,GAAG;MAC5C,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFvD,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIK,UAAU,KAAK,gBAAgB,EAAE;QACnC;QACA,IAAIjB,OAAO,CAACqB,QAAQ,CAACG,YAAY,CAAC,IAAI4C,KAAK,CAACP,UAAU,CAACxC,QAAQ,CAACG,YAAY,CAAC,CAAC,EAAE;UAC9EK,aAAa,CAAC;YAAEL,YAAY,EAAE;UAAqC,CAAC,CAAC;UACrEZ,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEA,MAAMf,WAAW,CAACwE,iBAAiB,CACjC/D,UAAU,EACVe,QAAQ,CAACE,OAAO,EAChBsC,UAAU,CAACxC,QAAQ,CAACG,YAAY,CAClC,CAAC;QACDjB,SAAS,CAAC,sCAAsC,CAAC;MACnD,CAAC,MAAM,IAAIU,UAAU,KAAK,gBAAgB,EAAE;QAC1C,MAAMpB,WAAW,CAACyE,YAAY,CAC5BhE,UAAU,EACVe,QAAQ,CAACE,OAAO,EAChBF,QAAQ,CAACI,SACX,CAAC;QACDlB,SAAS,CAAC,gCAAgC,CAAC;MAC7C,CAAC,MAAM,IAAIU,UAAU,KAAK,cAAc,EAAE;QACxC;QACA,MAAMsD,UAAU,GAAGzE,gBAAgB,CAACuB,QAAQ,CAAC;QAE7C,IAAI,CAACkD,UAAU,CAACC,OAAO,EAAE;UACvB3C,aAAa,CAAC0C,UAAU,CAACE,MAAM,CAAC;UAChC1C,eAAe,CAACwC,UAAU,CAACG,QAAQ,CAAC;UACpC9D,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAM+D,aAAa,GAAGJ,UAAU,CAACI,aAAa;QAC9C,MAAM9E,WAAW,CAAC+E,UAAU,CAACtE,UAAU,EAAEqE,aAAa,CAACpD,OAAO,EAAEoD,aAAa,CAAC;QAC9EpE,SAAS,CAAC,8BAA8B,CAAC;;QAEzC;QACA,IAAIsE,MAAM,CAACC,IAAI,CAACP,UAAU,CAACG,QAAQ,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;UAC/C,MAAMC,eAAe,GAAGH,MAAM,CAACI,MAAM,CAACV,UAAU,CAACG,QAAQ,CAAC,CAACQ,IAAI,CAAC,IAAI,CAAC;UACrEvC,OAAO,CAACwC,IAAI,CAAC,gCAAgC,EAAEH,eAAe,CAAC;QACjE;MACF,CAAC,MAAM,IAAI/D,UAAU,KAAK,aAAa,EAAE;QACvC;QACA,MAAMmE,WAAW,GAAGjE,YAAY,CAACkE,mBAAmB,KAAK,YAAY,IAAKlE,YAAY,CAACkC,eAAe,IAAIlC,YAAY,CAACkC,eAAe,GAAG,CAAE;QAE3I,IAAI+B,WAAW,EAAE;UACf;UACA,MAAMvF,WAAW,CAACyF,eAAe,CAAChF,UAAU,EAAEa,YAAY,CAACI,OAAO,CAAC;UACnEhB,SAAS,CAAC,QAAQY,YAAY,CAACI,OAAO,kCAAkC,CAAC;QAC3E,CAAC,MAAM;UACL;UACA,MAAM1B,WAAW,CAAC0F,UAAU,CAACjF,UAAU,EAAEa,YAAY,CAACI,OAAO,EAAEa,UAAU,CAAC;UAC1E7B,SAAS,CAAC,QAAQY,YAAY,CAACI,OAAO,IAAIa,UAAU,KAAK,OAAO,GAAG,oBAAoB,GAAG,WAAW,eAAe,CAAC;QACvH;MACF;MAEAa,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdlC,OAAO,CAAC,gCAAgC,IAAIkC,KAAK,CAACuB,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACnFtB,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4E,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIvE,UAAU,KAAK,gBAAgB,EAAE;MACnC,oBACEf,OAAA,CAACnC,MAAM;QAAC0H,IAAI,EAAE1E,UAAW;QAAC2E,OAAO,EAAEzC,iBAAkB;QAAC0C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E3F,OAAA,CAAClC,WAAW;UAAA6H,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACjD/F,OAAA,CAACjC,aAAa;UAAA4H,QAAA,EACX3D,WAAW,gBACVhC,OAAA,CAACvB,gBAAgB;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBjE,IAAI,CAAC+C,MAAM,KAAK,CAAC,gBACnB7E,OAAA,CAACxB,KAAK;YAACwH,QAAQ,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAAC9E,YAAY,gBACfjB,OAAA,CAAC7C,GAAG;YAAAwI,QAAA,gBACF3F,OAAA,CAAC5C,UAAU;cAAC6I,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/F,OAAA,CAACxC,IAAI;cAAAmI,QAAA,EACF7D,IAAI,CAACqE,GAAG,CAAElD,IAAI,iBACbjD,OAAA,CAACvC,QAAQ;gBACP2I,MAAM;gBAENC,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAACC,IAAI,CAAE;gBAAA0C,QAAA,eAEtC3F,OAAA,CAACtC,YAAY;kBACX4I,OAAO,EAAErD,IAAI,CAAC5B,OAAQ;kBACtBkF,SAAS,EAAE,GAAGtD,IAAI,CAACuD,SAAS,IAAI,KAAK,UAAUvD,IAAI,CAACwD,mBAAmB,IAAI,KAAK,OAAOxD,IAAI,CAACyD,iBAAiB,IAAI,KAAK;gBAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC,GANG9C,IAAI,CAAC5B,OAAO;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAEN/F,OAAA,CAAC7C,GAAG;YAACwJ,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBACjB3F,OAAA,CAAC5C,UAAU;cAAC6I,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,GAAC,oBACzB,EAAC1E,YAAY,CAACI,OAAO;YAAA;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACb/F,OAAA,CAAC5C,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAP,QAAA,GAAC,iBACxB,EAAC1E,YAAY,CAACiC,aAAa,IAAI,KAAK;YAAA;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACb/F,OAAA,CAAC5C,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAP,QAAA,GAAC,qBACpB,EAAC1E,YAAY,CAACkC,eAAe,IAAI,GAAG;YAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACb/F,OAAA,CAAC9B,SAAS;cACR2I,MAAM,EAAC,OAAO;cACdvD,IAAI,EAAC,cAAc;cACnBwD,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,QAAQ;cACbrB,SAAS;cACTO,OAAO,EAAC,UAAU;cAClB1C,KAAK,EAAEpC,QAAQ,CAACG,YAAa;cAC7B0F,QAAQ,EAAE5D,gBAAiB;cAC3B6D,QAAQ;cACRzE,KAAK,EAAE,CAAC,CAACd,UAAU,CAACJ,YAAa;cACjC4F,UAAU,EAAExF,UAAU,CAACJ,YAAa;cACpCqF,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB/F,OAAA,CAAC/B,aAAa;UAAA0H,QAAA,gBACZ3F,OAAA,CAAC3C,MAAM;YAACgJ,OAAO,EAAEtD,iBAAkB;YAAA4C,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnD9E,YAAY,iBACXjB,OAAA,CAAC3C,MAAM;YACLgJ,OAAO,EAAEpC,UAAW;YACpBkD,QAAQ,EAAE1G,OAAO,IAAI,CAACU,QAAQ,CAACG,YAAa;YAC5C8F,SAAS,EAAE3G,OAAO,gBAAGT,OAAA,CAACvB,gBAAgB;cAAC4I,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG/F,OAAA,CAACR,QAAQ;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIhF,UAAU,KAAK,gBAAgB,EAAE;MAC1C,oBACEf,OAAA,CAACnC,MAAM;QAAC0H,IAAI,EAAE1E,UAAW;QAAC2E,OAAO,EAAEzC,iBAAkB;QAAC0C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E3F,OAAA,CAAClC,WAAW;UAAA6H,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACtD/F,OAAA,CAACjC,aAAa;UAAA4H,QAAA,EACX3D,WAAW,gBACVhC,OAAA,CAACvB,gBAAgB;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBjE,IAAI,CAAC+C,MAAM,KAAK,CAAC,gBACnB7E,OAAA,CAACxB,KAAK;YAACwH,QAAQ,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAAC9E,YAAY,gBACfjB,OAAA,CAAC7C,GAAG;YAAAwI,QAAA,gBACF3F,OAAA,CAAC5C,UAAU;cAAC6I,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/F,OAAA,CAACxC,IAAI;cAAAmI,QAAA,EACF7D,IAAI,CAACqE,GAAG,CAAElD,IAAI,iBACbjD,OAAA,CAACvC,QAAQ;gBACP2I,MAAM;gBAENC,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAACC,IAAI,CAAE;gBAAA0C,QAAA,eAEtC3F,OAAA,CAACtC,YAAY;kBACX4I,OAAO,EAAErD,IAAI,CAAC5B,OAAQ;kBACtBkF,SAAS,EAAE,mBAAmBtD,IAAI,CAAC1B,SAAS,IAAI,eAAe;gBAAG;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC,GANG9C,IAAI,CAAC5B,OAAO;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAEN/F,OAAA,CAAC7C,GAAG;YAACwJ,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBACjB3F,OAAA,CAAC5C,UAAU;cAAC6I,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,GAAC,oBACzB,EAAC1E,YAAY,CAACI,OAAO;YAAA;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACb/F,OAAA,CAAC5C,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAP,QAAA,GAAC,kBACvB,EAAC1E,YAAY,CAACM,SAAS,IAAI,eAAe;YAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACb/F,OAAA,CAAC9B,SAAS;cACR2I,MAAM,EAAC,OAAO;cACdvD,IAAI,EAAC,WAAW;cAChBwD,KAAK,EAAC,WAAW;cACjBpB,SAAS;cACTO,OAAO,EAAC,UAAU;cAClB1C,KAAK,EAAEpC,QAAQ,CAACI,SAAU;cAC1ByF,QAAQ,EAAE5D,gBAAiB;cAC3BuD,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB/F,OAAA,CAAC/B,aAAa;UAAA0H,QAAA,gBACZ3F,OAAA,CAAC3C,MAAM;YAACgJ,OAAO,EAAEtD,iBAAkB;YAAA4C,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnD9E,YAAY,iBACXjB,OAAA,CAAC3C,MAAM;YACLgJ,OAAO,EAAEpC,UAAW;YACpBkD,QAAQ,EAAE1G,OAAQ;YAClB2G,SAAS,EAAE3G,OAAO,gBAAGT,OAAA,CAACvB,gBAAgB;cAAC4I,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG/F,OAAA,CAACR,QAAQ;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIhF,UAAU,KAAK,eAAe,IAAIA,UAAU,KAAK,aAAa,EAAE;MACzE;MACA,MAAMmE,WAAW,GAAGjE,YAAY,KAAKA,YAAY,CAACkE,mBAAmB,KAAK,YAAY,IAAKlE,YAAY,CAACkC,eAAe,IAAIlC,YAAY,CAACkC,eAAe,GAAG,CAAE,CAAC;MAE7J,oBACEnD,OAAA,CAACnC,MAAM;QAAC0H,IAAI,EAAE1E,UAAW;QAAC2E,OAAO,EAAEzC,iBAAkB;QAAC0C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E3F,OAAA,CAAClC,WAAW;UAAA6H,QAAA,EACT5E,UAAU,KAAK,eAAe,GAAG,8BAA8B,GAC/D,CAACE,YAAY,GAAG,6BAA6B,GAC7CiE,WAAW,GAAG,uBAAuB,GAAG;QAAc;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACd/F,OAAA,CAACjC,aAAa;UAAA4H,QAAA,EACX3D,WAAW,gBACVhC,OAAA,CAACvB,gBAAgB;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBjE,IAAI,CAAC+C,MAAM,KAAK,CAAC,gBACnB7E,OAAA,CAACxB,KAAK;YAACwH,QAAQ,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAAC9E,YAAY,gBACfjB,OAAA,CAACxC,IAAI;YAAAmI,QAAA,EACF7D,IAAI,CAACqE,GAAG,CAAElD,IAAI,iBACbjD,OAAA,CAACvC,QAAQ;cACP2I,MAAM;cAENC,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAACC,IAAI,CAAE;cAAA0C,QAAA,eAEtC3F,OAAA,CAACtC,YAAY;gBACX4I,OAAO,EAAErD,IAAI,CAAC5B,OAAQ;gBACtBkF,SAAS,EAAE,GAAGtD,IAAI,CAACuD,SAAS,IAAI,KAAK,UAAUvD,IAAI,CAACwD,mBAAmB,IAAI,KAAK,OAAOxD,IAAI,CAACyD,iBAAiB,IAAI,KAAK;cAAG;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1H;YAAC,GANG9C,IAAI,CAAC5B,OAAO;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOT,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,GACLhF,UAAU,KAAK,aAAa,IAAImE,WAAW,gBAC7ClF,OAAA,CAAAE,SAAA;YAAAyF,QAAA,gBACE3F,OAAA,CAAChC,iBAAiB;cAAA2H,QAAA,GAAC,UACT,eAAA3F,OAAA;gBAAA2F,QAAA,EAAS1E,YAAY,CAACI;cAAO;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,8CAC/C,EAAC9E,YAAY,CAACkC,eAAe,GAAG,CAAC,iBAC/BnD,OAAA,CAAAE,SAAA;gBAAAyF,QAAA,GAAE,iBAAe,eAAA3F,OAAA;kBAAA2F,QAAA,GAAS1E,YAAY,CAACkC,eAAe,EAAC,IAAE;gBAAA;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC;cAAA,eAAE,CACtE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACgB,CAAC,eACpB/F,OAAA,CAAChC,iBAAiB;cAAC2I,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC;UAAA,eACpB,CAAC,GACDhF,UAAU,KAAK,aAAa,gBAC9Bf,OAAA,CAAAE,SAAA;YAAAyF,QAAA,gBACE3F,OAAA,CAAChC,iBAAiB;cAAA2H,QAAA,GAAC,6BACU,eAAA3F,OAAA;gBAAA2F,QAAA,EAAS1E,YAAY,CAACI;cAAO;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KACpE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAEpB/F,OAAA,CAAC7B,WAAW;cAACmJ,SAAS,EAAC,UAAU;cAACX,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBAC9C3F,OAAA,CAAClB,SAAS;gBAACwI,SAAS,EAAC,QAAQ;gBAAA3B,QAAA,EAAC;cAAgC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1E/F,OAAA,CAACpB,UAAU;gBACT2E,KAAK,EAAErB,UAAW;gBAClB8E,QAAQ,EAAG3D,CAAC,IAAKlB,aAAa,CAACkB,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;gBAAAoC,QAAA,gBAE/C3F,OAAA,CAACnB,gBAAgB;kBACf0E,KAAK,EAAC,OAAO;kBACbgE,OAAO,eAAEvH,OAAA,CAACrB,KAAK;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBe,KAAK,EAAC;gBAAqF;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC,eACF/F,OAAA,CAACnB,gBAAgB;kBACf0E,KAAK,EAAC,QAAQ;kBACdgE,OAAO,eAAEvH,OAAA,CAACrB,KAAK;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBe,KAAK,EAAC;gBAAsE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA,eACd,CAAC,GACD;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAChB/F,OAAA,CAAC/B,aAAa;UAAA0H,QAAA,gBACZ3F,OAAA,CAAC3C,MAAM;YAACgJ,OAAO,EAAEtD,iBAAkB;YAAA4C,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDhF,UAAU,KAAK,aAAa,IAAIE,YAAY,iBAC3CjB,OAAA,CAAC3C,MAAM;YACLgJ,OAAO,EAAEpC,UAAW;YACpBkD,QAAQ,EAAE1G,OAAQ;YAClB+G,KAAK,EAAEtC,WAAW,GAAG,SAAS,GAAG,OAAQ;YACzCkC,SAAS,EAAE3G,OAAO,gBAAGT,OAAA,CAACvB,gBAAgB;cAAC4I,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAGb,WAAW,gBAAGlF,OAAA,CAACN,WAAW;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG/F,OAAA,CAACZ,UAAU;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAEpGT,WAAW,GAAG,kBAAkB,GAAIhD,UAAU,KAAK,OAAO,GAAG,kBAAkB,GAAG;UAA0B;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIhF,UAAU,KAAK,cAAc,EAAE;MACxC,oBACEf,OAAA,CAACnC,MAAM;QAAC0H,IAAI,EAAE1E,UAAW;QAAC2E,OAAO,EAAEzC,iBAAkB;QAAC0C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3E3F,OAAA,CAAClC,WAAW;UAAA6H,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxC/F,OAAA,CAACjC,aAAa;UAAA4H,QAAA,eACZ3F,OAAA,CAACzB,IAAI;YAACkJ,SAAS;YAACC,OAAO,EAAE,CAAE;YAACf,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBACxC3F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,SAAS;gBACdwD,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAACE,OAAQ;gBACxB8F,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,qBAAqB;gBAC1BwD,KAAK,EAAC,qBAAqB;gBAC3BpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAAC2G,mBAAoB;gBACpCd,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,SAAS;gBACdwD,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAAC4G,OAAQ;gBACxBf,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,SAAS;gBACdwD,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAAC6G,OAAQ;gBACxBhB,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,aAAa;gBAClBwD,KAAK,EAAC,aAAa;gBACnBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAAC8G,WAAY;gBAC5BjB,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,WAAW;gBAChBwD,KAAK,EAAC,WAAW;gBACjBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAACqF,SAAU;gBAC1BQ,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,cAAc;gBACnBwD,KAAK,EAAC,mBAAmB;gBACzBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAAC+G,YAAa;gBAC7BlB,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,SAAS;gBACdwD,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAACgH,OAAQ;gBACxBnB,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,IAAI;gBACTwD,KAAK,EAAC,IAAI;gBACVpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAACiH,EAAE,IAAIjH,QAAQ,CAACkH,EAAG;gBAClCrB,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,qBAAqB;gBAC1BwD,KAAK,EAAC,qBAAqB;gBAC3BpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAACsF,mBAAoB;gBACpCO,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,iBAAiB;gBACtBwD,KAAK,EAAC,iBAAiB;gBACvBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAACmH,eAAgB;gBAChCtB,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,6BAA6B;gBAClCwD,KAAK,EAAC,6BAA6B;gBACnCpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAACoH,2BAA4B;gBAC5CvB,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,mBAAmB;gBACxBwD,KAAK,EAAC,mBAAmB;gBACzBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAACuF,iBAAkB;gBAClCM,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,eAAe;gBACpBwD,KAAK,EAAC,eAAe;gBACrBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAACqH,aAAc;gBAC9BxB,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,2BAA2B;gBAChCwD,KAAK,EAAC,2BAA2B;gBACjCpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAACsH,yBAA0B;gBAC1CzB,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,eAAe;gBACpBwD,KAAK,EAAC,eAAe;gBACrBC,IAAI,EAAC,QAAQ;gBACbrB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAAC+B,aAAc;gBAC9B8D,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC9B,SAAS;gBACRoF,IAAI,EAAC,WAAW;gBAChBwD,KAAK,EAAC,WAAW;gBACjBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB1C,KAAK,EAAEpC,QAAQ,CAACI,SAAU;gBAC1ByF,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP/F,OAAA,CAACzB,IAAI;cAACoJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlC,QAAA,eACvB3F,OAAA,CAAC7B,WAAW;gBAACuH,SAAS;gBAAAC,QAAA,gBACpB3F,OAAA,CAAC5B,UAAU;kBAAAuH,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5C/F,OAAA,CAAC3B,MAAM;kBACLiF,IAAI,EAAC,qBAAqB;kBAC1BC,KAAK,EAAEpC,QAAQ,CAACgE,mBAAoB;kBACpC2B,KAAK,EAAC,qBAAqB;kBAC3BE,QAAQ,EAAE5D,gBAAiB;kBAAAuC,QAAA,gBAE3B3F,OAAA,CAAC1B,QAAQ;oBAACiF,KAAK,EAAC,eAAe;oBAAAoC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACxD/F,OAAA,CAAC1B,QAAQ;oBAACiF,KAAK,EAAC,UAAU;oBAAAoC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9C/F,OAAA,CAAC1B,QAAQ;oBAACiF,KAAK,EAAC,YAAY;oBAAAoC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClD/F,OAAA,CAAC1B,QAAQ;oBAACiF,KAAK,EAAC,OAAO;oBAAAoC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAChB/F,OAAA,CAAC/B,aAAa;UAAA0H,QAAA,gBACZ3F,OAAA,CAAC3C,MAAM;YAACgJ,OAAO,EAAEtD,iBAAkB;YAAA4C,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpD/F,OAAA,CAAC3C,MAAM;YACLgJ,OAAO,EAAEpC,UAAW;YACpBkD,QAAQ,EAAE1G,OAAQ;YAClB2G,SAAS,EAAE3G,OAAO,gBAAGT,OAAA,CAACvB,gBAAgB;cAAC4I,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG/F,OAAA,CAACR,QAAQ;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACE/F,OAAA,CAAC7C,GAAG;IAACwJ,EAAE,EAAE;MAAE+B,OAAO,EAAE;IAAO,CAAE;IAAA/C,QAAA,gBAE3B3F,OAAA,CAAC7C,GAAG;MAACwJ,EAAE,EAAE;QAAEgC,KAAK,EAAE,OAAO;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAjD,QAAA,eACjC3F,OAAA,CAAC1C,KAAK;QAACqJ,EAAE,EAAE;UAAEkC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAnD,QAAA,gBACzB3F,OAAA,CAAC5C,UAAU;UAAC6I,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAP,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/F,OAAA,CAACzC,OAAO;UAACoJ,EAAE,EAAE;YAAEmC,EAAE,EAAE;UAAE;QAAE;UAAAlD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1B/F,OAAA,CAACxC,IAAI;UAAC8J,SAAS,EAAC,KAAK;UAACyB,KAAK;UAAApD,QAAA,gBACzB3F,OAAA,CAACpC,cAAc;YAACyI,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAAC,gBAAgB,CAAE;YAAAiD,QAAA,gBAClE3F,OAAA,CAACrC,YAAY;cAAAgI,QAAA,eACX3F,OAAA,CAACV,SAAS;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACf/F,OAAA,CAACtC,YAAY;cAAC4I,OAAO,EAAC;YAA2B;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAEjB/F,OAAA,CAACpC,cAAc;YAACyI,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAAC,gBAAgB,CAAE;YAAAiD,QAAA,gBAClE3F,OAAA,CAACrC,YAAY;cAAAgI,QAAA,eACX3F,OAAA,CAACd,QAAQ;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACf/F,OAAA,CAACtC,YAAY;cAAC4I,OAAO,EAAC;YAAgC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEjB/F,OAAA,CAACpC,cAAc;YAACyI,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAAC,cAAc,CAAE;YAAAiD,QAAA,gBAChE3F,OAAA,CAACrC,YAAY;cAAAgI,QAAA,eACX3F,OAAA,CAAChB,OAAO;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACf/F,OAAA,CAACtC,YAAY;cAAC4I,OAAO,EAAC;YAAwB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAEjB/F,OAAA,CAACpC,cAAc;YAACyI,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAAC,cAAc,CAAE;YAAAiD,QAAA,gBAChE3F,OAAA,CAACrC,YAAY;cAAAgI,QAAA,eACX3F,OAAA,CAACd,QAAQ;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACf/F,OAAA,CAACtC,YAAY;cAAC4I,OAAO,EAAC;YAAkB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAEjB/F,OAAA,CAACpC,cAAc;YAACyI,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAAC,aAAa,CAAE;YAAAiD,QAAA,gBAC/D3F,OAAA,CAACrC,YAAY;cAAAgI,QAAA,eACX3F,OAAA,CAACZ,UAAU;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACf/F,OAAA,CAACtC,YAAY;cAAC4I,OAAO,EAAC;YAAiB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eAEjB/F,OAAA,CAACpC,cAAc;YAACyI,OAAO,EAAEA,CAAA,KAAM3D,kBAAkB,CAAC,kBAAkB,CAAE;YAAAiD,QAAA,gBACpE3F,OAAA,CAACrC,YAAY;cAAAgI,QAAA,eACX3F,OAAA,CAACV,SAAS;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACf/F,OAAA,CAACtC,YAAY;cAAC4I,OAAO,EAAC;YAAsB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN/F,OAAA,CAAC7C,GAAG;MAACwJ,EAAE,EAAE;QAAEqC,QAAQ,EAAE;MAAE,CAAE;MAAArD,QAAA,eACvB3F,OAAA,CAAC1C,KAAK;QAACqJ,EAAE,EAAE;UAAEkC,CAAC,EAAE,CAAC;UAAEI,SAAS,EAAE,OAAO;UAAEP,OAAO,EAAE,MAAM;UAAEQ,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAS,CAAE;QAAAxD,QAAA,GACtG,CAAChF,cAAc,iBACdX,OAAA,CAAC5C,UAAU;UAAC6I,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAE5B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb,EACApF,cAAc,IAAI,CAACE,UAAU,iBAC5Bb,OAAA,CAAC7C,GAAG;UAACwJ,EAAE,EAAE;YAAEyC,SAAS,EAAE;UAAS,CAAE;UAAAzD,QAAA,gBAC/B3F,OAAA,CAAC5C,UAAU;YAAC6I,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAP,QAAA,GAClChF,cAAc,KAAK,gBAAgB,IAAI,wBAAwB,EAC/DA,cAAc,KAAK,cAAc,IAAI,eAAe,EACpDA,cAAc,KAAK,cAAc,IAAI,qBAAqB,EAC1DA,cAAc,KAAK,aAAa,IAAI,cAAc,EAClDA,cAAc,KAAK,gBAAgB,IAAI,6BAA6B,EACpEA,cAAc,KAAK,kBAAkB,IAAI,mBAAmB;UAAA;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACb/F,OAAA,CAAC5C,UAAU;YAAC6I,OAAO,EAAC,OAAO;YAAAN,QAAA,EAAC;UAE5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/F,OAAA,CAACvB,gBAAgB;YAACkI,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELT,YAAY,CAAC,CAAC;EAAA;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAACvF,EAAA,CAnuBIL,oBAAoB;AAAAkJ,EAAA,GAApBlJ,oBAAoB;AAquB1B,eAAeA,oBAAoB;AAAC,IAAAkJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}