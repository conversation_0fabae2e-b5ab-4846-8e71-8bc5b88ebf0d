{"ast": null, "code": "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "map": {"version": 3, "names": ["auto", "getBasePlacement", "placement", "split"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@popperjs/core/lib/utils/getBasePlacement.js"], "sourcesContent": ["import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}"], "mappings": "AAAA,SAASA,IAAI,QAAQ,aAAa;AAClC,eAAe,SAASC,gBAAgBA,CAACC,SAAS,EAAE;EAClD,OAAOA,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}