{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getBadgeUtilityClass(slot) {\n  return generateUtilityClass('MuiBadge', slot);\n}\nconst badgeClasses = generateUtilityClasses('MuiBadge', ['root', 'badge', 'dot', 'standard', 'anchorOriginTopRight', 'anchorOriginBottomRight', 'anchorOriginTopLeft', 'anchorOriginBottomLeft', 'invisible', 'colorError', 'colorInfo', 'colorPrimary', 'colorSecondary', 'colorSuccess', 'colorWarning', 'overlapRectangular', 'overlapCircular',\n// TODO: v6 remove the overlap value from these class keys\n'anchorOriginTopLeftCircular', 'anchorOriginTopLeftRectangular', 'anchorOriginTopRightCircular', 'anchorOriginTopRightRectangular', 'anchorOriginBottomLeftCircular', 'anchorOriginBottomLeftRectangular', 'anchorOriginBottomRightCircular', 'anchorOriginBottomRightRectangular']);\nexport default badgeClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getBadgeUtilityClass", "slot", "badgeClasses"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/Badge/badgeClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getBadgeUtilityClass(slot) {\n  return generateUtilityClass('MuiBadge', slot);\n}\nconst badgeClasses = generateUtilityClasses('MuiBadge', ['root', 'badge', 'dot', 'standard', 'anchorOriginTopRight', 'anchorOriginBottomRight', 'anchorOriginTopLeft', 'anchorOriginBottomLeft', 'invisible', 'colorError', 'colorInfo', 'colorPrimary', 'colorSecondary', 'colorSuccess', 'colorWarning', 'overlapRectangular', 'overlapCircular',\n// TODO: v6 remove the overlap value from these class keys\n'anchorOriginTopLeftCircular', 'anchorOriginTopLeftRectangular', 'anchorOriginTopRightCircular', 'anchorOriginTopRightRectangular', 'anchorOriginBottomLeftCircular', 'anchorOriginBottomLeftRectangular', 'anchorOriginBottomRightCircular', 'anchorOriginBottomRightRectangular']);\nexport default badgeClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOF,oBAAoB,CAAC,UAAU,EAAEE,IAAI,CAAC;AAC/C;AACA,MAAMC,YAAY,GAAGJ,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,sBAAsB,EAAE,yBAAyB,EAAE,qBAAqB,EAAE,wBAAwB,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,cAAc,EAAE,oBAAoB,EAAE,iBAAiB;AAClV;AACA,6BAA6B,EAAE,gCAAgC,EAAE,8BAA8B,EAAE,iCAAiC,EAAE,gCAAgC,EAAE,mCAAmC,EAAE,iCAAiC,EAAE,oCAAoC,CAAC,CAAC;AACpR,eAAeI,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}