{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nconst API_URL = config.API_URL;\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst adminService = {\n  // Reset del database\n  resetDatabase: async () => {\n    try {\n      const response = await axiosInstance.post('/admin/reset-database');\n      return response.data;\n    } catch (error) {\n      console.error('Reset database error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default adminService;", "map": {"version": 3, "names": ["axios", "config", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "adminService", "resetDatabase", "response", "post", "data", "console"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/adminService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\n// Crea un'istanza di axios con configurazione personalizzata\r\nconst axiosInstance = axios.create({\r\n  baseURL: API_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json'\r\n  }\r\n});\r\n\r\n// Configura axios per includere il token in tutte le richieste\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem('token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nconst adminService = {\r\n  // Reset del database\r\n  resetDatabase: async () => {\r\n    try {\r\n      const response = await axiosInstance.post('/admin/reset-database');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Reset database error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default adminService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAE9B,MAAMC,OAAO,GAAGD,MAAM,CAACC,OAAO;;AAE9B;AACA,MAAMC,aAAa,GAAGH,KAAK,CAACI,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCR,MAAM,IAAK;EACV,MAAMS,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTT,MAAM,CAACK,OAAO,CAACO,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOT,MAAM;AACf,CAAC,EACAa,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,YAAY,GAAG;EACnB;EACAC,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMhB,aAAa,CAACiB,IAAI,CAAC,uBAAuB,CAAC;MAClE,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,GAAGP,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}