{"ast": null, "code": "import axios from'axios';import config from'../config';import axiosInstance from'./axiosConfig';const API_URL=config.API_URL;const userService={// Ottiene la lista di tutti gli utenti\ngetUsers:async()=>{try{const response=await axiosInstance.get('/users');return response.data;}catch(error){console.error('Get users error:',error);throw error.response?error.response.data:error;}},// Crea un nuovo utente\ncreateUser:async userData=>{try{const response=await axiosInstance.post('/users',userData);return response.data;}catch(error){console.error('Create user error:',error);throw error.response?error.response.data:error;}},// Aggiorna un utente esistente\nupdateUser:async(userId,userData)=>{try{const response=await axiosInstance.put(`/users/${userId}`,userData);return response.data;}catch(error){console.error('Update user error:',error);throw error.response?error.response.data:error;}},// Elimina un utente\ndeleteUser:async userId=>{try{const response=await axiosInstance.delete(`/users/${userId}`);return response.data;}catch(error){console.error('Delete user error:',error);throw error.response?error.response.data:error;}},// Abilita/disabilita un utente\ntoggleUserStatus:async userId=>{try{const response=await axiosInstance.get(`/users/toggle/${userId}`);return response.data;}catch(error){console.error('Toggle user status error:',error);throw error.response?error.response.data:error;}},// Ottiene una visualizzazione raw del database\ngetDbRaw:async()=>{try{console.log('Chiamata API a /users/db-raw');const response=await axiosInstance.get('/users/db-raw');console.log('Risposta API ricevuta:',response);return response.data;}catch(error){console.error('Get DB raw error:',error);console.error('Dettagli errore:',error.response?error.response.data:'Nessun dettaglio disponibile');throw error.response?error.response.data:error;}},// Verifica e disabilita gli utenti scaduti\ncheckExpiredUsers:async()=>{try{const response=await axiosInstance.post('/users/check-expired');return response.data;}catch(error){console.error('Check expired users error:',error);throw error.response?error.response.data:error;}}};export default userService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "userService", "getUsers", "response", "get", "data", "error", "console", "createUser", "userData", "post", "updateUser", "userId", "put", "deleteUser", "delete", "toggleUserStatus", "getDbRaw", "log", "checkExpiredUsers"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/userService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\nconst userService = {\r\n  // Ottiene la lista di tutti gli utenti\r\n  getUsers: async () => {\r\n    try {\r\n      const response = await axiosInstance.get('/users');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get users error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea un nuovo utente\r\n  createUser: async (userData) => {\r\n    try {\r\n      const response = await axiosInstance.post('/users', userData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Create user error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna un utente esistente\r\n  updateUser: async (userId, userData) => {\r\n    try {\r\n      const response = await axiosInstance.put(`/users/${userId}`, userData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Update user error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Elimina un utente\r\n  deleteUser: async (userId) => {\r\n    try {\r\n      const response = await axiosInstance.delete(`/users/${userId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Delete user error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Abilita/disabilita un utente\r\n  toggleUserStatus: async (userId) => {\r\n    try {\r\n      const response = await axiosInstance.get(`/users/toggle/${userId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Toggle user status error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene una visualizzazione raw del database\r\n  getDbRaw: async () => {\r\n    try {\r\n      console.log('Chiamata API a /users/db-raw');\r\n      const response = await axiosInstance.get('/users/db-raw');\r\n      console.log('Risposta API ricevuta:', response);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get DB raw error:', error);\r\n      console.error('Dettagli errore:', error.response ? error.response.data : 'Nessun dettaglio disponibile');\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Verifica e disabilita gli utenti scaduti\r\n  checkExpiredUsers: async () => {\r\n    try {\r\n      const response = await axiosInstance.post('/users/check-expired');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Check expired users error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default userService;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,WAAW,CAC9B,MAAO,CAAAC,aAAa,KAAM,eAAe,CAEzC,KAAM,CAAAC,OAAO,CAAGF,MAAM,CAACE,OAAO,CAE9B,KAAM,CAAAC,WAAW,CAAG,CAClB;AACAC,QAAQ,CAAE,KAAAA,CAAA,GAAY,CACpB,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAJ,aAAa,CAACK,GAAG,CAAC,QAAQ,CAAC,CAClD,MAAO,CAAAD,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CACxC,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAE,UAAU,CAAE,KAAO,CAAAC,QAAQ,EAAK,CAC9B,GAAI,CACF,KAAM,CAAAN,QAAQ,CAAG,KAAM,CAAAJ,aAAa,CAACW,IAAI,CAAC,QAAQ,CAAED,QAAQ,CAAC,CAC7D,MAAO,CAAAN,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAK,UAAU,CAAE,KAAAA,CAAOC,MAAM,CAAEH,QAAQ,GAAK,CACtC,GAAI,CACF,KAAM,CAAAN,QAAQ,CAAG,KAAM,CAAAJ,aAAa,CAACc,GAAG,CAAC,UAAUD,MAAM,EAAE,CAAEH,QAAQ,CAAC,CACtE,MAAO,CAAAN,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAQ,UAAU,CAAE,KAAO,CAAAF,MAAM,EAAK,CAC5B,GAAI,CACF,KAAM,CAAAT,QAAQ,CAAG,KAAM,CAAAJ,aAAa,CAACgB,MAAM,CAAC,UAAUH,MAAM,EAAE,CAAC,CAC/D,MAAO,CAAAT,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAU,gBAAgB,CAAE,KAAO,CAAAJ,MAAM,EAAK,CAClC,GAAI,CACF,KAAM,CAAAT,QAAQ,CAAG,KAAM,CAAAJ,aAAa,CAACK,GAAG,CAAC,iBAAiBQ,MAAM,EAAE,CAAC,CACnE,MAAO,CAAAT,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAW,QAAQ,CAAE,KAAAA,CAAA,GAAY,CACpB,GAAI,CACFV,OAAO,CAACW,GAAG,CAAC,8BAA8B,CAAC,CAC3C,KAAM,CAAAf,QAAQ,CAAG,KAAM,CAAAJ,aAAa,CAACK,GAAG,CAAC,eAAe,CAAC,CACzDG,OAAO,CAACW,GAAG,CAAC,wBAAwB,CAAEf,QAAQ,CAAC,CAC/C,MAAO,CAAAA,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CACzCC,OAAO,CAACD,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAG,8BAA8B,CAAC,CACxG,KAAM,CAAAC,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAa,iBAAiB,CAAE,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACF,KAAM,CAAAhB,QAAQ,CAAG,KAAM,CAAAJ,aAAa,CAACW,IAAI,CAAC,sBAAsB,CAAC,CACjE,MAAO,CAAAP,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CACF,CAAC,CAED,cAAe,CAAAL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}