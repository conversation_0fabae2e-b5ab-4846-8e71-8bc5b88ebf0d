{"ast": null, "code": "import { getRoundingMethod } from \"./_lib/getRoundingMethod.mjs\";\nimport { constructFrom } from \"./constructFrom.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link roundToNearestMinutes} function options.\n */\n\n/**\n * @name roundToNearestMinutes\n * @category Minute Helpers\n * @summary Rounds the given date to the nearest minute\n *\n * @description\n * Rounds the given date to the nearest minute (or number of minutes).\n * Rounds up when the given date is exactly between the nearest round minutes.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to round\n * @param options - An object with options.\n *\n * @returns The new date rounded to the closest minute\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest minute:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34))\n * //=> Thu Jul 10 2014 12:13:00\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest quarter hour:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { nearestTo: 15 })\n * //=> Thu Jul 10 2014 12:15:00\n *\n * @example\n * // Floor (rounds down) 10 July 2014 12:12:34 to nearest minute:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { roundingMethod: 'floor' })\n * //=> Thu Jul 10 2014 12:12:00\n *\n * @example\n * // Ceil (rounds up) 10 July 2014 12:12:34 to nearest half hour:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { roundingMethod: 'ceil', nearestTo: 30 })\n * //=> Thu Jul 10 2014 12:30:00\n */\nexport function roundToNearestMinutes(date, options) {\n  const nearestTo = options?.nearestTo ?? 1;\n  if (nearestTo < 1 || nearestTo > 30) return constructFrom(date, NaN);\n  const _date = toDate(date);\n  const fractionalSeconds = _date.getSeconds() / 60;\n  const fractionalMilliseconds = _date.getMilliseconds() / 1000 / 60;\n  const minutes = _date.getMinutes() + fractionalSeconds + fractionalMilliseconds;\n\n  // Unlike the `differenceIn*` functions, the default rounding behavior is `round` and not 'trunc'\n  const method = options?.roundingMethod ?? \"round\";\n  const roundingMethod = getRoundingMethod(method);\n  const roundedMinutes = roundingMethod(minutes / nearestTo) * nearestTo;\n  const result = constructFrom(date, _date);\n  result.setMinutes(roundedMinutes, 0, 0);\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default roundToNearestMinutes;", "map": {"version": 3, "names": ["getRoundingMethod", "constructFrom", "toDate", "roundToNearestMinutes", "date", "options", "nearestTo", "NaN", "_date", "fractionalSeconds", "getSeconds", "fractionalMilliseconds", "getMilliseconds", "minutes", "getMinutes", "method", "roundingMethod", "roundedMinutes", "result", "setMinutes"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/roundToNearestMinutes.mjs"], "sourcesContent": ["import { getRoundingMethod } from \"./_lib/getRoundingMethod.mjs\";\nimport { constructFrom } from \"./constructFrom.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link roundToNearestMinutes} function options.\n */\n\n/**\n * @name roundToNearestMinutes\n * @category Minute Helpers\n * @summary Rounds the given date to the nearest minute\n *\n * @description\n * Rounds the given date to the nearest minute (or number of minutes).\n * Rounds up when the given date is exactly between the nearest round minutes.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to round\n * @param options - An object with options.\n *\n * @returns The new date rounded to the closest minute\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest minute:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34))\n * //=> Thu Jul 10 2014 12:13:00\n *\n * @example\n * // Round 10 July 2014 12:12:34 to nearest quarter hour:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { nearestTo: 15 })\n * //=> Thu Jul 10 2014 12:15:00\n *\n * @example\n * // Floor (rounds down) 10 July 2014 12:12:34 to nearest minute:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { roundingMethod: 'floor' })\n * //=> Thu Jul 10 2014 12:12:00\n *\n * @example\n * // Ceil (rounds up) 10 July 2014 12:12:34 to nearest half hour:\n * const result = roundToNearestMinutes(new Date(2014, 6, 10, 12, 12, 34), { roundingMethod: 'ceil', nearestTo: 30 })\n * //=> Thu Jul 10 2014 12:30:00\n */\nexport function roundToNearestMinutes(date, options) {\n  const nearestTo = options?.nearestTo ?? 1;\n\n  if (nearestTo < 1 || nearestTo > 30) return constructFrom(date, NaN);\n\n  const _date = toDate(date);\n  const fractionalSeconds = _date.getSeconds() / 60;\n  const fractionalMilliseconds = _date.getMilliseconds() / 1000 / 60;\n  const minutes =\n    _date.getMinutes() + fractionalSeconds + fractionalMilliseconds;\n\n  // Unlike the `differenceIn*` functions, the default rounding behavior is `round` and not 'trunc'\n  const method = options?.roundingMethod ?? \"round\";\n  const roundingMethod = getRoundingMethod(method);\n\n  const roundedMinutes = roundingMethod(minutes / nearestTo) * nearestTo;\n\n  const result = constructFrom(date, _date);\n  result.setMinutes(roundedMinutes, 0, 0);\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default roundToNearestMinutes;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,8BAA8B;AAChE,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACnD,MAAMC,SAAS,GAAGD,OAAO,EAAEC,SAAS,IAAI,CAAC;EAEzC,IAAIA,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAG,EAAE,EAAE,OAAOL,aAAa,CAACG,IAAI,EAAEG,GAAG,CAAC;EAEpE,MAAMC,KAAK,GAAGN,MAAM,CAACE,IAAI,CAAC;EAC1B,MAAMK,iBAAiB,GAAGD,KAAK,CAACE,UAAU,CAAC,CAAC,GAAG,EAAE;EACjD,MAAMC,sBAAsB,GAAGH,KAAK,CAACI,eAAe,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE;EAClE,MAAMC,OAAO,GACXL,KAAK,CAACM,UAAU,CAAC,CAAC,GAAGL,iBAAiB,GAAGE,sBAAsB;;EAEjE;EACA,MAAMI,MAAM,GAAGV,OAAO,EAAEW,cAAc,IAAI,OAAO;EACjD,MAAMA,cAAc,GAAGhB,iBAAiB,CAACe,MAAM,CAAC;EAEhD,MAAME,cAAc,GAAGD,cAAc,CAACH,OAAO,GAAGP,SAAS,CAAC,GAAGA,SAAS;EAEtE,MAAMY,MAAM,GAAGjB,aAAa,CAACG,IAAI,EAAEI,KAAK,CAAC;EACzCU,MAAM,CAACC,UAAU,CAACF,cAAc,EAAE,CAAC,EAAE,CAAC,CAAC;EACvC,OAAOC,MAAM;AACf;;AAEA;AACA,eAAef,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}