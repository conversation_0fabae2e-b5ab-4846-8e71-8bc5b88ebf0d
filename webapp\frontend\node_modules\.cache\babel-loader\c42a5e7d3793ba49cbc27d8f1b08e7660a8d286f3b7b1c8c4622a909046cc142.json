{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\common\\\\FilterableTableHeader.js\";\nimport React from 'react';\nimport { Box, TableCell, TableSortLabel, Typography } from '@mui/material';\nimport ExcelLikeFilter from './ExcelLikeFilter';\n\n/**\n * Componente per l'intestazione di una tabella con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.columnName - Nome della colonna\n * @param {string} props.label - Etichetta da visualizzare\n * @param {Array} props.data - Dati della tabella\n * @param {Function} props.onFilterChange - Funzione chiamata quando il filtro cambia\n * @param {string} props.dataType - Tipo di dati ('text', 'number', 'date')\n * @param {string} props.sortDirection - Direzione di ordinamento ('asc', 'desc', null)\n * @param {Function} props.onSortChange - Funzione chiamata quando l'ordinamento cambia\n * @param {boolean} props.disableFilter - Disabilita il filtro\n * @param {boolean} props.disableSort - Disabilita l'ordinamento\n * @param {Function} props.renderHeader - Funzione per renderizzare un header personalizzato\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FilterableTableHeader = ({\n  columnName,\n  label,\n  data,\n  onFilterChange,\n  dataType = 'text',\n  sortDirection = null,\n  onSortChange = null,\n  disableFilter = false,\n  disableSort = false,\n  renderHeader = null,\n  ...cellProps\n}) => {\n  const handleSortClick = () => {\n    if (onSortChange && !disableSort) {\n      const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';\n      onSortChange(columnName, newDirection);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(TableCell, {\n    ...cellProps,\n    children: renderHeader ?\n    // Usa il renderHeader personalizzato se fornito\n    renderHeader() :\n    /*#__PURE__*/\n    // Usa il layout standard\n    _jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [!disableSort && onSortChange ? /*#__PURE__*/_jsxDEV(TableSortLabel, {\n        active: Boolean(sortDirection),\n        direction: sortDirection || 'asc',\n        onClick: handleSortClick,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          component: \"span\",\n          children: label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        component: \"span\",\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 13\n      }, this), !disableFilter && /*#__PURE__*/_jsxDEV(ExcelLikeFilter, {\n        data: data,\n        columnName: columnName,\n        onFilterChange: onFilterChange,\n        dataType: dataType\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_c = FilterableTableHeader;\nexport default FilterableTableHeader;\nvar _c;\n$RefreshReg$(_c, \"FilterableTableHeader\");", "map": {"version": 3, "names": ["React", "Box", "TableCell", "TableSortLabel", "Typography", "ExcelLikeFilter", "jsxDEV", "_jsxDEV", "FilterableTableHeader", "columnName", "label", "data", "onFilterChange", "dataType", "sortDirection", "onSortChange", "disableFilter", "disableSort", "renderHeader", "cellProps", "handleSortClick", "newDirection", "children", "sx", "display", "alignItems", "justifyContent", "active", "Boolean", "direction", "onClick", "variant", "component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/common/FilterableTableHeader.js"], "sourcesContent": ["import React from 'react';\nimport { Box, TableCell, TableSortLabel, Typography } from '@mui/material';\nimport ExcelLikeFilter from './ExcelLikeFilter';\n\n/**\n * Componente per l'intestazione di una tabella con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.columnName - Nome della colonna\n * @param {string} props.label - Etichetta da visualizzare\n * @param {Array} props.data - Dati della tabella\n * @param {Function} props.onFilterChange - Funzione chiamata quando il filtro cambia\n * @param {string} props.dataType - Tipo di dati ('text', 'number', 'date')\n * @param {string} props.sortDirection - Direzione di ordinamento ('asc', 'desc', null)\n * @param {Function} props.onSortChange - Funzione chiamata quando l'ordinamento cambia\n * @param {boolean} props.disableFilter - Disabilita il filtro\n * @param {boolean} props.disableSort - Disabilita l'ordinamento\n * @param {Function} props.renderHeader - Funzione per renderizzare un header personalizzato\n */\nconst FilterableTableHeader = ({\n  columnName,\n  label,\n  data,\n  onFilterChange,\n  dataType = 'text',\n  sortDirection = null,\n  onSortChange = null,\n  disableFilter = false,\n  disableSort = false,\n  renderHeader = null,\n  ...cellProps\n}) => {\n  const handleSortClick = () => {\n    if (onSortChange && !disableSort) {\n      const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';\n      onSortChange(columnName, newDirection);\n    }\n  };\n\n  return (\n    <TableCell {...cellProps}>\n      {renderHeader ? (\n        // Usa il renderHeader personalizzato se fornito\n        renderHeader()\n      ) : (\n        // Usa il layout standard\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          {!disableSort && onSortChange ? (\n            <TableSortLabel\n              active={Boolean(sortDirection)}\n              direction={sortDirection || 'asc'}\n              onClick={handleSortClick}\n            >\n              <Typography variant=\"subtitle2\" component=\"span\">\n                {label}\n              </Typography>\n            </TableSortLabel>\n          ) : (\n            <Typography variant=\"subtitle2\" component=\"span\">\n              {label}\n            </Typography>\n          )}\n\n          {!disableFilter && (\n            <ExcelLikeFilter\n              data={data}\n              columnName={columnName}\n              onFilterChange={onFilterChange}\n              dataType={dataType}\n            />\n          )}\n        </Box>\n      )}\n    </TableCell>\n  );\n};\n\nexport default FilterableTableHeader;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,SAAS,EAAEC,cAAc,EAAEC,UAAU,QAAQ,eAAe;AAC1E,OAAOC,eAAe,MAAM,mBAAmB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,SAAAC,MAAA,IAAAC,OAAA;AAeA,MAAMC,qBAAqB,GAAGA,CAAC;EAC7BC,UAAU;EACVC,KAAK;EACLC,IAAI;EACJC,cAAc;EACdC,QAAQ,GAAG,MAAM;EACjBC,aAAa,GAAG,IAAI;EACpBC,YAAY,GAAG,IAAI;EACnBC,aAAa,GAAG,KAAK;EACrBC,WAAW,GAAG,KAAK;EACnBC,YAAY,GAAG,IAAI;EACnB,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIL,YAAY,IAAI,CAACE,WAAW,EAAE;MAChC,MAAMI,YAAY,GAAGP,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;MAC7DC,YAAY,CAACN,UAAU,EAAEY,YAAY,CAAC;IACxC;EACF,CAAC;EAED,oBACEd,OAAA,CAACL,SAAS;IAAA,GAAKiB,SAAS;IAAAG,QAAA,EACrBJ,YAAY;IACX;IACAA,YAAY,CAAC,CAAC;IAAA;IAEd;IACAX,OAAA,CAACN,GAAG;MAACsB,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAJ,QAAA,GACjF,CAACL,WAAW,IAAIF,YAAY,gBAC3BR,OAAA,CAACJ,cAAc;QACbwB,MAAM,EAAEC,OAAO,CAACd,aAAa,CAAE;QAC/Be,SAAS,EAAEf,aAAa,IAAI,KAAM;QAClCgB,OAAO,EAAEV,eAAgB;QAAAE,QAAA,eAEzBf,OAAA,CAACH,UAAU;UAAC2B,OAAO,EAAC,WAAW;UAACC,SAAS,EAAC,MAAM;UAAAV,QAAA,EAC7CZ;QAAK;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAEjB7B,OAAA,CAACH,UAAU;QAAC2B,OAAO,EAAC,WAAW;QAACC,SAAS,EAAC,MAAM;QAAAV,QAAA,EAC7CZ;MAAK;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,EAEA,CAACpB,aAAa,iBACbT,OAAA,CAACF,eAAe;QACdM,IAAI,EAAEA,IAAK;QACXF,UAAU,EAAEA,UAAW;QACvBG,cAAc,EAAEA,cAAe;QAC/BC,QAAQ,EAAEA;MAAS;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC;AAACC,EAAA,GAxDI7B,qBAAqB;AA0D3B,eAAeA,qBAAqB;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}