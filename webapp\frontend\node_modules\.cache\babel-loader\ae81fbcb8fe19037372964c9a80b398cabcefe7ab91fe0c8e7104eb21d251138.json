{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\QuickAddCablesDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, TextField, Checkbox, FormControlLabel, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, CircularProgress, Alert, IconButton, Chip, Tooltip, Tabs, Tab, List, ListItem, ListItemText, ListItemSecondaryAction, Divider, Card, CardContent, CardHeader, Badge } from '@mui/material';\nimport { Add as AddIcon, Delete as DeleteIcon, Save as SaveIcon, Info as InfoIcon, Warning as WarningIcon, Search as SearchIcon, Cable as CableIcon, CheckCircle as CheckCircleIcon, Error as ErrorIcon } from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { determineCableState, getCableStateColor, isCableInstalled } from '../../utils/stateUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n// Utility per verificare compatibilità tra cavo e bobina\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst isCompatible = (cavo, bobina) => {\n  return cavo.tipologia === bobina.tipologia && String(cavo.sezione) === String(bobina.sezione);\n};\n\n// Utility per ottenere il numero della bobina dall'ID\nconst getBobinaNumber = idBobina => {\n  if (!idBobina) return '';\n  const parts = idBobina.split('-');\n  return parts.length > 1 ? parts[1] : idBobina;\n};\n\n/**\n * Componente per aggiungere rapidamente più cavi a una bobina\n * Implementa sistema di doppia lista per cavi compatibili/incompatibili\n * con gestione intelligente delle incompatibilità e dialog moderni\n */\nconst QuickAddCablesDialog = ({\n  open,\n  onClose,\n  bobina,\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  var _bobina$metri_residui, _bobina$metri_residui2;\n  // Stati per la gestione dei dati\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per i dati separati\n  const [allCavi, setAllCavi] = useState([]);\n  const [caviCompatibili, setCaviCompatibili] = useState([]);\n  const [caviIncompatibili, setCaviIncompatibili] = useState([]);\n  const [selectedCavi, setSelectedCavi] = useState([]);\n  const [caviMetri, setCaviMetri] = useState({});\n\n  // Stati per la UI\n  const [activeTab, setActiveTab] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showIncompatibleDialog, setShowIncompatibleDialog] = useState(false);\n  const [incompatibleSelection, setIncompatibleSelection] = useState(null);\n  const [showOverDialog, setShowOverDialog] = useState(false);\n  const [overDialogData, setOverDialogData] = useState(null);\n\n  // Stati per la validazione\n  const [errors, setErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n\n  // Carica i cavi disponibili quando il dialog viene aperto\n  useEffect(() => {\n    if (open && bobina && cantiereId) {\n      loadCavi();\n      // Reset stati quando si apre il dialog\n      resetDialogState();\n    }\n  }, [open, bobina, cantiereId]);\n\n  // Reset dello stato del dialog\n  const resetDialogState = () => {\n    setSelectedCavi([]);\n    setCaviMetri({});\n    setErrors({});\n    setWarnings({});\n    setSearchTerm('');\n    setActiveTab(0);\n    setShowIncompatibleDialog(false);\n    setIncompatibleSelection(null);\n    setShowOverDialog(false);\n    setOverDialogData(null);\n  };\n\n  // Funzione per caricare i cavi con sistema di doppia lista\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi disponibili (non installati e non SPARE)\n      const caviDisponibili = caviData.filter(cavo => !isCableInstalled(cavo) && cavo.modificato_manualmente !== 3);\n\n      // Separa cavi compatibili e incompatibili (CORREZIONE: rimosso n_conduttori)\n      const compatibili = caviDisponibili.filter(cavo => isCompatible(cavo, bobina));\n      const incompatibili = caviDisponibili.filter(cavo => !isCompatible(cavo, bobina));\n      setAllCavi(caviDisponibili);\n      setCaviCompatibili(compatibili);\n      setCaviIncompatibili(incompatibili);\n      console.log(`Cavi caricati: ${compatibili.length} compatibili, ${incompatibili.length} incompatibili`);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo compatibile\n  const handleCompatibleCavoSelect = cavo => {\n    setSelectedCavi(prev => {\n      const isSelected = prev.some(c => c.id_cavo === cavo.id_cavo);\n      if (isSelected) {\n        // Conferma prima di rimuovere se ci sono metri inseriti\n        const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';\n        if (hasMetri) {\n          if (!window.confirm(`Rimuovere il cavo ${cavo.id_cavo} dalla selezione? I metri inseriti (${caviMetri[cavo.id_cavo]}m) andranno persi.`)) {\n            return prev;\n          }\n        }\n\n        // Rimuovi il cavo dalla selezione\n        const newSelected = prev.filter(c => c.id_cavo !== cavo.id_cavo);\n\n        // Rimuovi anche i metri associati\n        const newCaviMetri = {\n          ...caviMetri\n        };\n        delete newCaviMetri[cavo.id_cavo];\n        setCaviMetri(newCaviMetri);\n\n        // Rimuovi errori e warning\n        setErrors(prevErrors => {\n          const newErrors = {\n            ...prevErrors\n          };\n          delete newErrors[cavo.id_cavo];\n          return newErrors;\n        });\n        setWarnings(prevWarnings => {\n          const newWarnings = {\n            ...prevWarnings\n          };\n          delete newWarnings[cavo.id_cavo];\n          return newWarnings;\n        });\n        return newSelected;\n      } else {\n        // Aggiungi il cavo alla selezione\n        return [...prev, cavo];\n      }\n    });\n  };\n\n  // Gestisce la selezione di un cavo incompatibile\n  const handleIncompatibleCavoSelect = cavo => {\n    setIncompatibleSelection({\n      cavo,\n      bobina\n    });\n    setShowIncompatibleDialog(true);\n  };\n\n  // Gestisce l'uso di una bobina incompatibile\n  const handleUseIncompatibleReel = () => {\n    if (incompatibleSelection) {\n      const {\n        cavo\n      } = incompatibleSelection;\n\n      // Aggiungi il cavo alla selezione con flag di incompatibilità\n      setSelectedCavi(prev => [...prev, {\n        ...cavo,\n        _isIncompatible: true\n      }]);\n      setShowIncompatibleDialog(false);\n      setIncompatibleSelection(null);\n      onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(`Cavo incompatibile ${cavo.id_cavo} aggiunto alla selezione. Sarà utilizzato con force_over.`);\n    }\n  };\n\n  // Gestisce l'input dei metri posati per un cavo\n  const handleMetriChange = (cavoId, value) => {\n    // Aggiorna i metri per il cavo\n    setCaviMetri(prev => ({\n      ...prev,\n      [cavoId]: value\n    }));\n\n    // Valida il valore inserito in tempo reale\n    validateMetri(cavoId, value);\n  };\n\n  // Valida i metri inseriti per un cavo con feedback migliorato\n  const validateMetri = (cavoId, value) => {\n    const cavo = allCavi.find(c => c.id_cavo === cavoId);\n    if (!cavo) return;\n\n    // Resetta gli errori e gli avvisi per questo cavo\n    setErrors(prev => {\n      const newErrors = {\n        ...prev\n      };\n      delete newErrors[cavoId];\n      return newErrors;\n    });\n    setWarnings(prev => {\n      const newWarnings = {\n        ...prev\n      };\n      delete newWarnings[cavoId];\n      return newWarnings;\n    });\n\n    // Controllo input vuoto\n    if (!value || value.trim() === '') {\n      return true; // Non mostrare errore per input vuoto durante la digitazione\n    }\n\n    // Controllo formato numerico\n    if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore numerico positivo'\n      }));\n      return false;\n    }\n    const metriPosati = parseFloat(value);\n\n    // Controllo metri teorici cavo\n    if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n      }));\n    }\n\n    // Controllo metri residui bobina con calcolo in tempo reale\n    const metriTotaliRichiesti = Object.entries(caviMetri).filter(([id, _]) => id !== cavoId) // Escludi il cavo corrente\n    .reduce((sum, [_, metri]) => sum + parseFloat(metri || 0), 0) + metriPosati;\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `Totale richiesto: ${metriTotaliRichiesti.toFixed(1)}m > Residui bobina: ${bobina.metri_residui.toFixed(1)}m (OVER)`\n      }));\n    }\n    return true;\n  };\n\n  // Valida tutti i metri inseriti con dialog moderno per OVER\n  const validateAllMetri = () => {\n    let isValid = true;\n    const newErrors = {};\n    const newWarnings = {};\n\n    // Verifica che ci siano cavi selezionati\n    if (selectedCavi.length === 0) {\n      onError('Seleziona almeno un cavo');\n      return false;\n    }\n\n    // Verifica che tutti i cavi selezionati abbiano metri inseriti\n    for (const cavo of selectedCavi) {\n      const metri = caviMetri[cavo.id_cavo];\n\n      // Controllo input vuoto\n      if (!metri || metri.trim() === '') {\n        newErrors[cavo.id_cavo] = 'Inserire un valore per i metri posati';\n        isValid = false;\n        continue;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(metri)) || parseFloat(metri) <= 0) {\n        newErrors[cavo.id_cavo] = 'Inserire un valore numerico positivo';\n        isValid = false;\n        continue;\n      }\n      const metriPosati = parseFloat(metri);\n\n      // Controllo metri teorici cavo\n      if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n        newWarnings[cavo.id_cavo] = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`;\n      }\n    }\n    setErrors(newErrors);\n    setWarnings(newWarnings);\n\n    // Se ci sono errori, non procedere\n    if (!isValid) {\n      return false;\n    }\n\n    // Verifica stato OVER della bobina\n    const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => sum + parseFloat(metri || 0), 0);\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      // Mostra dialog moderno invece di window.confirm\n      setOverDialogData({\n        metriTotaliRichiesti,\n        metriResiduiBobina: bobina.metri_residui,\n        selectedCavi: selectedCavi.length\n      });\n      setShowOverDialog(true);\n      return false; // Interrompi qui, il salvataggio continuerà dal dialog\n    }\n    return true;\n  };\n\n  // Gestisce il salvataggio dei dati con gestione migliorata\n  const handleSave = async (forceOver = false) => {\n    try {\n      // Validazione solo se non è un force over\n      if (!forceOver && !validateAllMetri()) {\n        return;\n      }\n      setSaving(true);\n\n      // Aggiorna ogni cavo selezionato\n      const results = [];\n      let errors = [];\n      for (const cavo of selectedCavi) {\n        try {\n          const metriPosati = parseFloat(caviMetri[cavo.id_cavo]);\n\n          // Determina se è necessario forzare lo stato OVER della bobina o incompatibilità\n          const metriGiàUtilizzati = results.reduce((sum, r) => sum + r.metriPosati, 0);\n          const needsForceOver = forceOver || metriGiàUtilizzati + metriPosati > bobina.metri_residui || cavo._isIncompatible;\n\n          // Aggiorna i metri posati del cavo\n          const result = await caviService.updateMetriPosati(cantiereId, cavo.id_cavo, metriPosati, bobina.id_bobina, needsForceOver);\n          results.push({\n            cavo: cavo.id_cavo,\n            metriPosati,\n            success: true,\n            wasIncompatible: cavo._isIncompatible\n          });\n        } catch (error) {\n          console.error(`Errore nell'aggiornamento del cavo ${cavo.id_cavo}:`, error);\n          errors.push({\n            cavo: cavo.id_cavo,\n            error: error.message || 'Errore sconosciuto'\n          });\n        }\n      }\n\n      // Gestione del risultato con messaggi migliorati\n      if (errors.length === 0) {\n        const incompatibleCount = results.filter(r => r.wasIncompatible).length;\n        let message = `${results.length} cavi aggiornati con successo`;\n        if (incompatibleCount > 0) {\n          message += ` (${incompatibleCount} incompatibili con force_over)`;\n        }\n        onSuccess(message);\n        onClose();\n      } else if (results.length > 0) {\n        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`);\n        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n        onClose();\n      } else {\n        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n      }\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la conferma del dialog OVER\n  const handleOverDialogConfirm = () => {\n    setShowOverDialog(false);\n    setOverDialogData(null);\n    handleSave(true); // Procedi con force_over\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filterCavi = caviList => {\n    if (!searchTerm) return caviList;\n    return caviList.filter(cavo => cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchTerm.toLowerCase()));\n  };\n  const filteredCompatibili = filterCavi(caviCompatibili);\n  const filteredIncompatibili = filterCavi(caviIncompatibili);\n\n  // Calcola statistiche\n  const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => {\n    const value = parseFloat(metri || 0);\n    return isNaN(value) ? sum : sum + value;\n  }, 0);\n  const isOverState = metriTotaliRichiesti > bobina.metri_residui;\n\n  // Componente per renderizzare una lista di cavi\n  const renderCaviList = (caviList, isCompatible = true) => {\n    if (caviLoading) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 9\n      }, this);\n    }\n    if (caviList.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          my: 2\n        },\n        children: isCompatible ? 'Nessun cavo compatibile disponibile.' : 'Nessun cavo incompatibile trovato.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(List, {\n      dense: true,\n      children: caviList.map(cavo => {\n        const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);\n        const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';\n        const hasError = errors[cavo.id_cavo];\n        const hasWarning = warnings[cavo.id_cavo];\n        return /*#__PURE__*/_jsxDEV(ListItem, {\n          sx: {\n            border: '1px solid #e0e0e0',\n            borderRadius: 1,\n            mb: 1,\n            bgcolor: isSelected ? 'rgba(33, 150, 243, 0.1)' : '#f5f7fa',\n            '&:hover': {\n              bgcolor: isSelected ? 'rgba(33, 150, 243, 0.2)' : 'rgba(33, 150, 243, 0.05)'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n                fontSize: \"small\",\n                color: isCompatible ? 'success' : 'warning'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                size: \"small\",\n                label: cavo.tipologia || 'N/A',\n                color: isCompatible ? 'success' : 'warning',\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 21\n              }, this), !isCompatible && /*#__PURE__*/_jsxDEV(Chip, {\n                size: \"small\",\n                label: \"INCOMPATIBILE\",\n                color: \"error\",\n                variant: \"filled\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 19\n            }, this),\n            secondary: /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [cavo.ubicazione_partenza || 'N/A', \" \\u2192 \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Formazione: \", cavo.sezione || 'N/A', \" | Metri teorici: \", cavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 21\n              }, this), isSelected && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  size: \"small\",\n                  type: \"number\",\n                  label: \"Metri posati\",\n                  value: caviMetri[cavo.id_cavo] || '',\n                  onChange: e => handleMetriChange(cavo.id_cavo, e.target.value),\n                  error: !!hasError,\n                  helperText: hasError || hasWarning,\n                  FormHelperTextProps: {\n                    sx: {\n                      color: hasWarning && !hasError ? 'warning.main' : 'error.main'\n                    }\n                  },\n                  sx: {\n                    width: '200px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              variant: isSelected ? 'contained' : 'outlined',\n              color: isCompatible ? 'primary' : 'warning',\n              onClick: () => isCompatible ? handleCompatibleCavoSelect(cavo) : handleIncompatibleCavoSelect(cavo),\n              startIcon: isSelected ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 43\n              }, this) : /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 65\n              }, this),\n              children: isSelected ? 'Selezionato' : 'Seleziona'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 15\n          }, this)]\n        }, cavo.id_cavo, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"xl\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Aggiungi cavi alla bobina \", getBobinaNumber(bobina === null || bobina === void 0 ? void 0 : bobina.id_bobina)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 549,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: !bobina ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: \"Nessuna bobina selezionata\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            title: \"Dettagli bobina\",\n            titleTypographyProps: {\n              variant: 'subtitle1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              pt: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexWrap: 'wrap',\n                gap: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"ID Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: bobina.id_bobina\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Tipologia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: bobina.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 576,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Formazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: bobina.sezione || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Metri residui\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  color: isOverState ? 'error.main' : 'text.primary',\n                  sx: {\n                    fontWeight: isOverState ? 'bold' : 'normal'\n                  },\n                  children: [((_bobina$metri_residui = bobina.metri_residui) === null || _bobina$metri_residui === void 0 ? void 0 : _bobina$metri_residui.toFixed(1)) || '0', \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: bobina.stato_bobina || 'N/D',\n                  size: \"small\",\n                  color: bobina.stato_bobina === 'Disponibile' ? 'success' : bobina.stato_bobina === 'In uso' ? 'primary' : bobina.stato_bobina === 'Over' ? 'error' : bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Cerca cavi\",\n          variant: \"outlined\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          placeholder: \"Cerca per ID, tipologia, ubicazione...\",\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 13\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 15\n        }, this) : filteredCavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Nessun cavo compatibile disponibile per questa bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  bgcolor: '#f5f5f5'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  padding: \"checkbox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"ID Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Tipologia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Ubicazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri Teorici\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri Posati\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: filteredCavi.map(cavo => {\n                const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  hover: true,\n                  selected: isSelected,\n                  onClick: () => handleCavoSelect(cavo),\n                  sx: {\n                    cursor: 'pointer'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    padding: \"checkbox\",\n                    children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                      checked: isSelected,\n                      onChange: e => {\n                        e.stopPropagation();\n                        handleCavoSelect(cavo);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 655,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 663,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.tipologia || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 664,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: `Da: ${cavo.ubicazione_partenza || 'N/A'} - A: ${cavo.ubicazione_arrivo || 'N/A'}`,\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          maxWidth: 150,\n                          overflow: 'hidden',\n                          textOverflow: 'ellipsis',\n                          whiteSpace: 'nowrap'\n                        },\n                        children: [cavo.ubicazione_partenza || 'N/A', \" \\u2192 \", cavo.ubicazione_arrivo || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 667,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 666,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 665,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.metri_teorici || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: isSelected ? /*#__PURE__*/_jsxDEV(TextField, {\n                      size: \"small\",\n                      type: \"number\",\n                      value: caviMetri[cavo.id_cavo] || '',\n                      onChange: e => {\n                        e.stopPropagation();\n                        handleMetriChange(cavo.id_cavo, e.target.value);\n                      },\n                      onClick: e => e.stopPropagation(),\n                      error: !!errors[cavo.id_cavo],\n                      helperText: errors[cavo.id_cavo] || warnings[cavo.id_cavo],\n                      FormHelperTextProps: {\n                        sx: {\n                          color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main'\n                        }\n                      },\n                      InputProps: {\n                        endAdornment: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                          title: warnings[cavo.id_cavo],\n                          children: /*#__PURE__*/_jsxDEV(WarningIcon, {\n                            color: \"warning\",\n                            fontSize: \"small\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 692,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 691,\n                          columnNumber: 37\n                        }, this) : null\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 675,\n                      columnNumber: 31\n                    }, this) : 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 673,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: cavo.stato_installazione || 'Da installare',\n                      color: getCableStateColor(cavo.stato_installazione)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 702,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 27\n                  }, this)]\n                }, cavo.id_cavo, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 15\n        }, this), selectedCavi.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: [\"Riepilogo selezione (\", selectedCavi.length, \" cavi)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  sx: {\n                    bgcolor: '#f5f5f5'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"ID Cavo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 726,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Posati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 727,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Azioni\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: [selectedCavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      size: \"small\",\n                      type: \"number\",\n                      value: caviMetri[cavo.id_cavo] || '',\n                      onChange: e => handleMetriChange(cavo.id_cavo, e.target.value),\n                      error: !!errors[cavo.id_cavo],\n                      helperText: errors[cavo.id_cavo] || warnings[cavo.id_cavo],\n                      FormHelperTextProps: {\n                        sx: {\n                          color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main'\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 736,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleCavoSelect(cavo),\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 754,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 749,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 748,\n                    columnNumber: 27\n                  }, this)]\n                }, cavo.id_cavo, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 25\n                }, this)), /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: /*#__PURE__*/_jsxDEV(TableCell, {\n                    colSpan: 3,\n                    align: \"right\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri totali richiesti:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 762,\n                        columnNumber: 29\n                      }, this), \" \", Object.values(caviMetri).reduce((sum, metri) => {\n                        const value = parseFloat(metri || 0);\n                        return isNaN(value) ? sum : sum + value;\n                      }, 0).toFixed(1), \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 761,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri residui bobina:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 770,\n                        columnNumber: 29\n                      }, this), \" \", ((_bobina$metri_residui2 = bobina.metri_residui) === null || _bobina$metri_residui2 === void 0 ? void 0 : _bobina$metri_residui2.toFixed(1)) || '0', \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 769,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 760,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 15\n        }, this), Object.keys(warnings).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: Object.entries(warnings).map(([cavoId, warning]) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [cavoId, \": \", warning]\n            }, cavoId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 784,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 782,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: \"Seleziona i cavi che vuoi associare a questa bobina e inserisci i metri posati per ciascuno. I metri posati verranno sottratti dai metri residui della bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 793,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        disabled: saving,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 801,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSave,\n        color: \"primary\",\n        variant: \"contained\",\n        disabled: saving || selectedCavi.length === 0 || Object.keys(errors).length > 0,\n        startIcon: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 64\n        }, this),\n        children: \"Salva\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 804,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 800,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 548,\n    columnNumber: 5\n  }, this);\n};\n_s(QuickAddCablesDialog, \"AbIIuLGdIuO/w2uebXwSQvCbijs=\");\n_c = QuickAddCablesDialog;\nexport default QuickAddCablesDialog;\nvar _c;\n$RefreshReg$(_c, \"QuickAddCablesDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "Box", "TextField", "Checkbox", "FormControlLabel", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "CircularProgress", "<PERSON><PERSON>", "IconButton", "Chip", "<PERSON><PERSON><PERSON>", "Tabs", "Tab", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Divider", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Badge", "Add", "AddIcon", "Delete", "DeleteIcon", "Save", "SaveIcon", "Info", "InfoIcon", "Warning", "WarningIcon", "Search", "SearchIcon", "Cable", "CableIcon", "CheckCircle", "CheckCircleIcon", "Error", "ErrorIcon", "caviService", "determineCableState", "getCableStateColor", "isCableInstalled", "IncompatibleReelDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "isCompatible", "cavo", "bobina", "tipologia", "String", "sezione", "getBobinaNumber", "idBobina", "parts", "split", "length", "QuickAddCablesDialog", "open", "onClose", "cantiereId", "onSuccess", "onError", "_s", "_bobina$metri_residui", "_bobina$metri_residui2", "loading", "setLoading", "caviLoading", "setCaviLoading", "saving", "setSaving", "allCavi", "set<PERSON><PERSON><PERSON><PERSON>", "caviCompatibili", "setCaviCompatibili", "caviIncompatibili", "setCaviIncompatibili", "<PERSON><PERSON><PERSON>", "setSelectedCavi", "caviMetri", "setCaviMetri", "activeTab", "setActiveTab", "searchTerm", "setSearchTerm", "showIncompatibleDialog", "setShowIncompatibleDialog", "incompatibleSelection", "setIncompatibleSelection", "showOverDialog", "setShowOverDialog", "overDialogData", "setOverDialogData", "errors", "setErrors", "warnings", "setWarnings", "loadCavi", "resetDialogState", "caviData", "get<PERSON><PERSON>", "caviDisponibili", "filter", "modificato_manualmente", "compatibili", "incompatibili", "console", "log", "error", "message", "handleCompatibleCavoSelect", "prev", "isSelected", "some", "c", "id_cavo", "has<PERSON><PERSON>ri", "trim", "window", "confirm", "newSelected", "newCaviMetri", "prevErrors", "newErrors", "prevWarnings", "newWarnings", "handleIncompatibleCavoSelect", "handleUseIncompatibleReel", "_isIncompatible", "handleMetriChange", "cavoId", "value", "validate<PERSON>etri", "find", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "metriTotaliRichiesti", "Object", "entries", "id", "_", "reduce", "sum", "metri", "metri_residui", "toFixed", "validateAllMetri", "<PERSON><PERSON><PERSON><PERSON>", "values", "metriResiduiBobina", "handleSave", "forceOver", "results", "metriGiàUtilizzati", "r", "needsForceOver", "result", "updateMetri<PERSON><PERSON><PERSON>", "id_bobina", "push", "success", "wasIncompatible", "incompatibleCount", "map", "e", "join", "handleOverDialogConfirm", "filterCavi", "caviList", "toLowerCase", "includes", "ubicazione_partenza", "ubicazione_arrivo", "filteredCompatibili", "filteredIncompatibili", "isOverState", "renderCaviList", "sx", "display", "justifyContent", "my", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "dense", "<PERSON><PERSON><PERSON><PERSON>", "hasWarning", "border", "borderRadius", "mb", "bgcolor", "primary", "alignItems", "gap", "fontSize", "color", "variant", "size", "label", "secondary", "mt", "type", "onChange", "target", "helperText", "FormHelperTextProps", "width", "onClick", "startIcon", "max<PERSON><PERSON><PERSON>", "fullWidth", "title", "titleTypographyProps", "pt", "flexWrap", "fontWeight", "stato_bobina", "placeholder", "filteredCavi", "component", "padding", "hover", "selected", "handleCavoSelect", "cursor", "checked", "stopPropagation", "overflow", "textOverflow", "whiteSpace", "InputProps", "endAdornment", "stato_installazione", "gutterBottom", "colSpan", "align", "keys", "warning", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/QuickAddCablesDialog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  Box,\n  TextField,\n  Checkbox,\n  FormControlLabel,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  CircularProgress,\n  Alert,\n  IconButton,\n  Chip,\n  Tooltip,\n  Tabs,\n  Tab,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Divider,\n  Card,\n  CardContent,\n  CardHeader,\n  Badge\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Delete as DeleteIcon,\n  Save as SaveIcon,\n  Info as InfoIcon,\n  Warning as WarningIcon,\n  Search as SearchIcon,\n  Cable as CableIcon,\n  CheckCircle as CheckCircleIcon,\n  Error as ErrorIcon\n} from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { determineCableState, getCableStateColor, isCableInstalled } from '../../utils/stateUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n// Utility per verificare compatibilità tra cavo e bobina\nconst isCompatible = (cavo, bobina) => {\n  return cavo.tipologia === bobina.tipologia &&\n         String(cavo.sezione) === String(bobina.sezione);\n};\n\n// Utility per ottenere il numero della bobina dall'ID\nconst getBobinaNumber = (idBobina) => {\n  if (!idBobina) return '';\n  const parts = idBobina.split('-');\n  return parts.length > 1 ? parts[1] : idBobina;\n};\n\n/**\n * Componente per aggiungere rapidamente più cavi a una bobina\n * Implementa sistema di doppia lista per cavi compatibili/incompatibili\n * con gestione intelligente delle incompatibilità e dialog moderni\n */\nconst QuickAddCablesDialog = ({ open, onClose, bobina, cantiereId, onSuccess, onError }) => {\n  // Stati per la gestione dei dati\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per i dati separati\n  const [allCavi, setAllCavi] = useState([]);\n  const [caviCompatibili, setCaviCompatibili] = useState([]);\n  const [caviIncompatibili, setCaviIncompatibili] = useState([]);\n  const [selectedCavi, setSelectedCavi] = useState([]);\n  const [caviMetri, setCaviMetri] = useState({});\n\n  // Stati per la UI\n  const [activeTab, setActiveTab] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showIncompatibleDialog, setShowIncompatibleDialog] = useState(false);\n  const [incompatibleSelection, setIncompatibleSelection] = useState(null);\n  const [showOverDialog, setShowOverDialog] = useState(false);\n  const [overDialogData, setOverDialogData] = useState(null);\n\n  // Stati per la validazione\n  const [errors, setErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n\n  // Carica i cavi disponibili quando il dialog viene aperto\n  useEffect(() => {\n    if (open && bobina && cantiereId) {\n      loadCavi();\n      // Reset stati quando si apre il dialog\n      resetDialogState();\n    }\n  }, [open, bobina, cantiereId]);\n\n  // Reset dello stato del dialog\n  const resetDialogState = () => {\n    setSelectedCavi([]);\n    setCaviMetri({});\n    setErrors({});\n    setWarnings({});\n    setSearchTerm('');\n    setActiveTab(0);\n    setShowIncompatibleDialog(false);\n    setIncompatibleSelection(null);\n    setShowOverDialog(false);\n    setOverDialogData(null);\n  };\n\n  // Funzione per caricare i cavi con sistema di doppia lista\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi disponibili (non installati e non SPARE)\n      const caviDisponibili = caviData.filter(cavo =>\n        !isCableInstalled(cavo) &&\n        cavo.modificato_manualmente !== 3\n      );\n\n      // Separa cavi compatibili e incompatibili (CORREZIONE: rimosso n_conduttori)\n      const compatibili = caviDisponibili.filter(cavo => isCompatible(cavo, bobina));\n      const incompatibili = caviDisponibili.filter(cavo => !isCompatible(cavo, bobina));\n\n      setAllCavi(caviDisponibili);\n      setCaviCompatibili(compatibili);\n      setCaviIncompatibili(incompatibili);\n\n      console.log(`Cavi caricati: ${compatibili.length} compatibili, ${incompatibili.length} incompatibili`);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo compatibile\n  const handleCompatibleCavoSelect = (cavo) => {\n    setSelectedCavi(prev => {\n      const isSelected = prev.some(c => c.id_cavo === cavo.id_cavo);\n\n      if (isSelected) {\n        // Conferma prima di rimuovere se ci sono metri inseriti\n        const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';\n        if (hasMetri) {\n          if (!window.confirm(`Rimuovere il cavo ${cavo.id_cavo} dalla selezione? I metri inseriti (${caviMetri[cavo.id_cavo]}m) andranno persi.`)) {\n            return prev;\n          }\n        }\n\n        // Rimuovi il cavo dalla selezione\n        const newSelected = prev.filter(c => c.id_cavo !== cavo.id_cavo);\n\n        // Rimuovi anche i metri associati\n        const newCaviMetri = { ...caviMetri };\n        delete newCaviMetri[cavo.id_cavo];\n        setCaviMetri(newCaviMetri);\n\n        // Rimuovi errori e warning\n        setErrors(prevErrors => {\n          const newErrors = { ...prevErrors };\n          delete newErrors[cavo.id_cavo];\n          return newErrors;\n        });\n        setWarnings(prevWarnings => {\n          const newWarnings = { ...prevWarnings };\n          delete newWarnings[cavo.id_cavo];\n          return newWarnings;\n        });\n\n        return newSelected;\n      } else {\n        // Aggiungi il cavo alla selezione\n        return [...prev, cavo];\n      }\n    });\n  };\n\n  // Gestisce la selezione di un cavo incompatibile\n  const handleIncompatibleCavoSelect = (cavo) => {\n    setIncompatibleSelection({ cavo, bobina });\n    setShowIncompatibleDialog(true);\n  };\n\n  // Gestisce l'uso di una bobina incompatibile\n  const handleUseIncompatibleReel = () => {\n    if (incompatibleSelection) {\n      const { cavo } = incompatibleSelection;\n\n      // Aggiungi il cavo alla selezione con flag di incompatibilità\n      setSelectedCavi(prev => [...prev, { ...cavo, _isIncompatible: true }]);\n\n      setShowIncompatibleDialog(false);\n      setIncompatibleSelection(null);\n\n      onSuccess?.(`Cavo incompatibile ${cavo.id_cavo} aggiunto alla selezione. Sarà utilizzato con force_over.`);\n    }\n  };\n\n  // Gestisce l'input dei metri posati per un cavo\n  const handleMetriChange = (cavoId, value) => {\n    // Aggiorna i metri per il cavo\n    setCaviMetri(prev => ({\n      ...prev,\n      [cavoId]: value\n    }));\n\n    // Valida il valore inserito in tempo reale\n    validateMetri(cavoId, value);\n  };\n\n  // Valida i metri inseriti per un cavo con feedback migliorato\n  const validateMetri = (cavoId, value) => {\n    const cavo = allCavi.find(c => c.id_cavo === cavoId);\n    if (!cavo) return;\n\n    // Resetta gli errori e gli avvisi per questo cavo\n    setErrors(prev => {\n      const newErrors = { ...prev };\n      delete newErrors[cavoId];\n      return newErrors;\n    });\n\n    setWarnings(prev => {\n      const newWarnings = { ...prev };\n      delete newWarnings[cavoId];\n      return newWarnings;\n    });\n\n    // Controllo input vuoto\n    if (!value || value.trim() === '') {\n      return true; // Non mostrare errore per input vuoto durante la digitazione\n    }\n\n    // Controllo formato numerico\n    if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore numerico positivo'\n      }));\n      return false;\n    }\n\n    const metriPosati = parseFloat(value);\n\n    // Controllo metri teorici cavo\n    if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n      }));\n    }\n\n    // Controllo metri residui bobina con calcolo in tempo reale\n    const metriTotaliRichiesti = Object.entries(caviMetri)\n      .filter(([id, _]) => id !== cavoId) // Escludi il cavo corrente\n      .reduce((sum, [_, metri]) => sum + parseFloat(metri || 0), 0) + metriPosati;\n\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `Totale richiesto: ${metriTotaliRichiesti.toFixed(1)}m > Residui bobina: ${bobina.metri_residui.toFixed(1)}m (OVER)`\n      }));\n    }\n\n    return true;\n  };\n\n  // Valida tutti i metri inseriti con dialog moderno per OVER\n  const validateAllMetri = () => {\n    let isValid = true;\n    const newErrors = {};\n    const newWarnings = {};\n\n    // Verifica che ci siano cavi selezionati\n    if (selectedCavi.length === 0) {\n      onError('Seleziona almeno un cavo');\n      return false;\n    }\n\n    // Verifica che tutti i cavi selezionati abbiano metri inseriti\n    for (const cavo of selectedCavi) {\n      const metri = caviMetri[cavo.id_cavo];\n\n      // Controllo input vuoto\n      if (!metri || metri.trim() === '') {\n        newErrors[cavo.id_cavo] = 'Inserire un valore per i metri posati';\n        isValid = false;\n        continue;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(metri)) || parseFloat(metri) <= 0) {\n        newErrors[cavo.id_cavo] = 'Inserire un valore numerico positivo';\n        isValid = false;\n        continue;\n      }\n\n      const metriPosati = parseFloat(metri);\n\n      // Controllo metri teorici cavo\n      if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n        newWarnings[cavo.id_cavo] = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`;\n      }\n    }\n\n    setErrors(newErrors);\n    setWarnings(newWarnings);\n\n    // Se ci sono errori, non procedere\n    if (!isValid) {\n      return false;\n    }\n\n    // Verifica stato OVER della bobina\n    const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => sum + parseFloat(metri || 0), 0);\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      // Mostra dialog moderno invece di window.confirm\n      setOverDialogData({\n        metriTotaliRichiesti,\n        metriResiduiBobina: bobina.metri_residui,\n        selectedCavi: selectedCavi.length\n      });\n      setShowOverDialog(true);\n      return false; // Interrompi qui, il salvataggio continuerà dal dialog\n    }\n\n    return true;\n  };\n\n  // Gestisce il salvataggio dei dati con gestione migliorata\n  const handleSave = async (forceOver = false) => {\n    try {\n      // Validazione solo se non è un force over\n      if (!forceOver && !validateAllMetri()) {\n        return;\n      }\n\n      setSaving(true);\n\n      // Aggiorna ogni cavo selezionato\n      const results = [];\n      let errors = [];\n\n      for (const cavo of selectedCavi) {\n        try {\n          const metriPosati = parseFloat(caviMetri[cavo.id_cavo]);\n\n          // Determina se è necessario forzare lo stato OVER della bobina o incompatibilità\n          const metriGiàUtilizzati = results.reduce((sum, r) => sum + r.metriPosati, 0);\n          const needsForceOver = forceOver || (metriGiàUtilizzati + metriPosati) > bobina.metri_residui || cavo._isIncompatible;\n\n          // Aggiorna i metri posati del cavo\n          const result = await caviService.updateMetriPosati(\n            cantiereId,\n            cavo.id_cavo,\n            metriPosati,\n            bobina.id_bobina,\n            needsForceOver\n          );\n\n          results.push({\n            cavo: cavo.id_cavo,\n            metriPosati,\n            success: true,\n            wasIncompatible: cavo._isIncompatible\n          });\n        } catch (error) {\n          console.error(`Errore nell'aggiornamento del cavo ${cavo.id_cavo}:`, error);\n          errors.push({\n            cavo: cavo.id_cavo,\n            error: error.message || 'Errore sconosciuto'\n          });\n        }\n      }\n\n      // Gestione del risultato con messaggi migliorati\n      if (errors.length === 0) {\n        const incompatibleCount = results.filter(r => r.wasIncompatible).length;\n        let message = `${results.length} cavi aggiornati con successo`;\n        if (incompatibleCount > 0) {\n          message += ` (${incompatibleCount} incompatibili con force_over)`;\n        }\n        onSuccess(message);\n        onClose();\n      } else if (results.length > 0) {\n        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`);\n        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n        onClose();\n      } else {\n        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n      }\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la conferma del dialog OVER\n  const handleOverDialogConfirm = () => {\n    setShowOverDialog(false);\n    setOverDialogData(null);\n    handleSave(true); // Procedi con force_over\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filterCavi = (caviList) => {\n    if (!searchTerm) return caviList;\n\n    return caviList.filter(cavo =>\n      cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase())) ||\n      (cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchTerm.toLowerCase())) ||\n      (cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchTerm.toLowerCase()))\n    );\n  };\n\n  const filteredCompatibili = filterCavi(caviCompatibili);\n  const filteredIncompatibili = filterCavi(caviIncompatibili);\n\n  // Calcola statistiche\n  const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => {\n    const value = parseFloat(metri || 0);\n    return isNaN(value) ? sum : sum + value;\n  }, 0);\n\n  const isOverState = metriTotaliRichiesti > bobina.metri_residui;\n\n  // Componente per renderizzare una lista di cavi\n  const renderCaviList = (caviList, isCompatible = true) => {\n    if (caviLoading) {\n      return (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      );\n    }\n\n    if (caviList.length === 0) {\n      return (\n        <Alert severity=\"info\" sx={{ my: 2 }}>\n          {isCompatible ? 'Nessun cavo compatibile disponibile.' : 'Nessun cavo incompatibile trovato.'}\n        </Alert>\n      );\n    }\n\n    return (\n      <List dense>\n        {caviList.map((cavo) => {\n          const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);\n          const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';\n          const hasError = errors[cavo.id_cavo];\n          const hasWarning = warnings[cavo.id_cavo];\n\n          return (\n            <ListItem\n              key={cavo.id_cavo}\n              sx={{\n                border: '1px solid #e0e0e0',\n                borderRadius: 1,\n                mb: 1,\n                bgcolor: isSelected ? 'rgba(33, 150, 243, 0.1)' : '#f5f7fa',\n                '&:hover': {\n                  bgcolor: isSelected ? 'rgba(33, 150, 243, 0.2)' : 'rgba(33, 150, 243, 0.05)'\n                }\n              }}\n            >\n              <ListItemText\n                primary={\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <CableIcon fontSize=\"small\" color={isCompatible ? 'success' : 'warning'} />\n                    <Typography variant=\"subtitle2\">{cavo.id_cavo}</Typography>\n                    <Chip\n                      size=\"small\"\n                      label={cavo.tipologia || 'N/A'}\n                      color={isCompatible ? 'success' : 'warning'}\n                      variant=\"outlined\"\n                    />\n                    {!isCompatible && (\n                      <Chip\n                        size=\"small\"\n                        label=\"INCOMPATIBILE\"\n                        color=\"error\"\n                        variant=\"filled\"\n                      />\n                    )}\n                  </Box>\n                }\n                secondary={\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {cavo.ubicazione_partenza || 'N/A'} → {cavo.ubicazione_arrivo || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Formazione: {cavo.sezione || 'N/A'} | Metri teorici: {cavo.metri_teorici || 'N/A'}\n                    </Typography>\n                    {isSelected && (\n                      <Box sx={{ mt: 1 }}>\n                        <TextField\n                          size=\"small\"\n                          type=\"number\"\n                          label=\"Metri posati\"\n                          value={caviMetri[cavo.id_cavo] || ''}\n                          onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}\n                          error={!!hasError}\n                          helperText={hasError || hasWarning}\n                          FormHelperTextProps={{\n                            sx: { color: hasWarning && !hasError ? 'warning.main' : 'error.main' }\n                          }}\n                          sx={{ width: '200px' }}\n                        />\n                      </Box>\n                    )}\n                  </Box>\n                }\n              />\n              <ListItemSecondaryAction>\n                <Button\n                  size=\"small\"\n                  variant={isSelected ? 'contained' : 'outlined'}\n                  color={isCompatible ? 'primary' : 'warning'}\n                  onClick={() => isCompatible ? handleCompatibleCavoSelect(cavo) : handleIncompatibleCavoSelect(cavo)}\n                  startIcon={isSelected ? <CheckCircleIcon /> : <AddIcon />}\n                >\n                  {isSelected ? 'Selezionato' : 'Seleziona'}\n                </Button>\n              </ListItemSecondaryAction>\n            </ListItem>\n          );\n        })}\n      </List>\n    );\n  };\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"xl\" fullWidth>\n      <DialogTitle>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <CableIcon />\n          <Typography variant=\"h6\">\n            Aggiungi cavi alla bobina {getBobinaNumber(bobina?.id_bobina)}\n          </Typography>\n        </Box>\n      </DialogTitle>\n      <DialogContent>\n        {!bobina ? (\n          <Alert severity=\"error\">Nessuna bobina selezionata</Alert>\n        ) : (\n          <>\n            {/* Informazioni sulla bobina */}\n            <Card sx={{ mb: 3 }}>\n              <CardHeader\n                title=\"Dettagli bobina\"\n                titleTypographyProps={{ variant: 'subtitle1' }}\n              />\n              <CardContent sx={{ pt: 0 }}>\n                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">ID Bobina</Typography>\n                    <Typography variant=\"body1\">{bobina.id_bobina}</Typography>\n                  </Box>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">Tipologia</Typography>\n                    <Typography variant=\"body1\">{bobina.tipologia || 'N/A'}</Typography>\n                  </Box>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">Formazione</Typography>\n                    <Typography variant=\"body1\">{bobina.sezione || 'N/A'}</Typography>\n                  </Box>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">Metri residui</Typography>\n                    <Typography\n                      variant=\"body1\"\n                      color={isOverState ? 'error.main' : 'text.primary'}\n                      sx={{ fontWeight: isOverState ? 'bold' : 'normal' }}\n                    >\n                      {bobina.metri_residui?.toFixed(1) || '0'} m\n                    </Typography>\n                  </Box>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">Stato</Typography>\n                    <Chip\n                      label={bobina.stato_bobina || 'N/D'}\n                      size=\"small\"\n                      color={\n                        bobina.stato_bobina === 'Disponibile' ? 'success' :\n                        bobina.stato_bobina === 'In uso' ? 'primary' :\n                        bobina.stato_bobina === 'Over' ? 'error' :\n                        bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n                      }\n                    />\n                  </Box>\n                </Box>\n              </CardContent>\n            </Card>\n\n            {/* Ricerca cavi */}\n            <TextField\n              fullWidth\n              label=\"Cerca cavi\"\n              variant=\"outlined\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder=\"Cerca per ID, tipologia, ubicazione...\"\n              sx={{ mb: 2 }}\n            />\n\n            {/* Tabella cavi */}\n            {caviLoading ? (\n              <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n                <CircularProgress />\n              </Box>\n            ) : filteredCavi.length === 0 ? (\n              <Alert severity=\"info\">\n                Nessun cavo compatibile disponibile per questa bobina.\n              </Alert>\n            ) : (\n              <TableContainer component={Paper} sx={{ mb: 3 }}>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                      <TableCell padding=\"checkbox\"></TableCell>\n                      <TableCell>ID Cavo</TableCell>\n                      <TableCell>Tipologia</TableCell>\n                      <TableCell>Ubicazione</TableCell>\n                      <TableCell>Metri Teorici</TableCell>\n                      <TableCell>Metri Posati</TableCell>\n                      <TableCell>Stato</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {filteredCavi.map((cavo) => {\n                      const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);\n                      return (\n                        <TableRow\n                          key={cavo.id_cavo}\n                          hover\n                          selected={isSelected}\n                          onClick={() => handleCavoSelect(cavo)}\n                          sx={{ cursor: 'pointer' }}\n                        >\n                          <TableCell padding=\"checkbox\">\n                            <Checkbox\n                              checked={isSelected}\n                              onChange={(e) => {\n                                e.stopPropagation();\n                                handleCavoSelect(cavo);\n                              }}\n                            />\n                          </TableCell>\n                          <TableCell>{cavo.id_cavo}</TableCell>\n                          <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                          <TableCell>\n                            <Tooltip title={`Da: ${cavo.ubicazione_partenza || 'N/A'} - A: ${cavo.ubicazione_arrivo || 'N/A'}`}>\n                              <Box sx={{ maxWidth: 150, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>\n                                {cavo.ubicazione_partenza || 'N/A'} → {cavo.ubicazione_arrivo || 'N/A'}\n                              </Box>\n                            </Tooltip>\n                          </TableCell>\n                          <TableCell>{cavo.metri_teorici || 'N/A'}</TableCell>\n                          <TableCell>\n                            {isSelected ? (\n                              <TextField\n                                size=\"small\"\n                                type=\"number\"\n                                value={caviMetri[cavo.id_cavo] || ''}\n                                onChange={(e) => {\n                                  e.stopPropagation();\n                                  handleMetriChange(cavo.id_cavo, e.target.value);\n                                }}\n                                onClick={(e) => e.stopPropagation()}\n                                error={!!errors[cavo.id_cavo]}\n                                helperText={errors[cavo.id_cavo] || warnings[cavo.id_cavo]}\n                                FormHelperTextProps={{\n                                  sx: { color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main' }\n                                }}\n                                InputProps={{\n                                  endAdornment: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? (\n                                    <Tooltip title={warnings[cavo.id_cavo]}>\n                                      <WarningIcon color=\"warning\" fontSize=\"small\" />\n                                    </Tooltip>\n                                  ) : null\n                                }}\n                              />\n                            ) : (\n                              'N/A'\n                            )}\n                          </TableCell>\n                          <TableCell>\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione || 'Da installare'}\n                              color={getCableStateColor(cavo.stato_installazione)}\n                            />\n                          </TableCell>\n                        </TableRow>\n                      );\n                    })}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            )}\n\n            {/* Riepilogo selezione */}\n            {selectedCavi.length > 0 && (\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Riepilogo selezione ({selectedCavi.length} cavi)\n                </Typography>\n                <TableContainer component={Paper}>\n                  <Table size=\"small\">\n                    <TableHead>\n                      <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                        <TableCell>ID Cavo</TableCell>\n                        <TableCell>Metri Posati</TableCell>\n                        <TableCell>Azioni</TableCell>\n                      </TableRow>\n                    </TableHead>\n                    <TableBody>\n                      {selectedCavi.map((cavo) => (\n                        <TableRow key={cavo.id_cavo}>\n                          <TableCell>{cavo.id_cavo}</TableCell>\n                          <TableCell>\n                            <TextField\n                              size=\"small\"\n                              type=\"number\"\n                              value={caviMetri[cavo.id_cavo] || ''}\n                              onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}\n                              error={!!errors[cavo.id_cavo]}\n                              helperText={errors[cavo.id_cavo] || warnings[cavo.id_cavo]}\n                              FormHelperTextProps={{\n                                sx: { color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main' }\n                              }}\n                            />\n                          </TableCell>\n                          <TableCell>\n                            <IconButton\n                              size=\"small\"\n                              color=\"error\"\n                              onClick={() => handleCavoSelect(cavo)}\n                            >\n                              <DeleteIcon fontSize=\"small\" />\n                            </IconButton>\n                          </TableCell>\n                        </TableRow>\n                      ))}\n                      <TableRow>\n                        <TableCell colSpan={3} align=\"right\">\n                          <Typography variant=\"body2\">\n                            <strong>Metri totali richiesti:</strong> {\n                              Object.values(caviMetri).reduce((sum, metri) => {\n                                const value = parseFloat(metri || 0);\n                                return isNaN(value) ? sum : sum + value;\n                              }, 0).toFixed(1)\n                            } m\n                          </Typography>\n                          <Typography variant=\"body2\">\n                            <strong>Metri residui bobina:</strong> {bobina.metri_residui?.toFixed(1) || '0'} m\n                          </Typography>\n                        </TableCell>\n                      </TableRow>\n                    </TableBody>\n                  </Table>\n                </TableContainer>\n              </Box>\n            )}\n\n            {/* Avvisi */}\n            {Object.keys(warnings).length > 0 && (\n              <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                <Typography variant=\"subtitle2\">Attenzione:</Typography>\n                <ul>\n                  {Object.entries(warnings).map(([cavoId, warning]) => (\n                    <li key={cavoId}>{cavoId}: {warning}</li>\n                  ))}\n                </ul>\n              </Alert>\n            )}\n\n            {/* Istruzioni */}\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Seleziona i cavi che vuoi associare a questa bobina e inserisci i metri posati per ciascuno.\n              I metri posati verranno sottratti dai metri residui della bobina.\n            </Alert>\n          </>\n        )}\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={onClose} disabled={saving}>\n          Annulla\n        </Button>\n        <Button\n          onClick={handleSave}\n          color=\"primary\"\n          variant=\"contained\"\n          disabled={saving || selectedCavi.length === 0 || Object.keys(errors).length > 0}\n          startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}\n        >\n          Salva\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default QuickAddCablesDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,mBAAmB,EAAEC,kBAAkB,EAAEC,gBAAgB,QAAQ,wBAAwB;AAClG,OAAOC,sBAAsB,MAAM,0BAA0B;;AAE7D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,GAAGA,CAACC,IAAI,EAAEC,MAAM,KAAK;EACrC,OAAOD,IAAI,CAACE,SAAS,KAAKD,MAAM,CAACC,SAAS,IACnCC,MAAM,CAACH,IAAI,CAACI,OAAO,CAAC,KAAKD,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC;AACxD,CAAC;;AAED;AACA,MAAMC,eAAe,GAAIC,QAAQ,IAAK;EACpC,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;EACxB,MAAMC,KAAK,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;EACjC,OAAOD,KAAK,CAACE,MAAM,GAAG,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC,GAAGD,QAAQ;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMI,oBAAoB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEX,MAAM;EAAEY,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC1F;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsF,MAAM,EAAEC,SAAS,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAACwF,OAAO,EAAEC,UAAU,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC4F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8F,YAAY,EAAEC,eAAe,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgG,SAAS,EAAEC,YAAY,CAAC,GAAGjG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACkG,SAAS,EAAEC,YAAY,CAAC,GAAGnG,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACoG,UAAU,EAAEC,aAAa,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACwG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAAC0G,cAAc,EAAEC,iBAAiB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4G,cAAc,EAAEC,iBAAiB,CAAC,GAAG7G,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM,CAAC8G,MAAM,EAAEC,SAAS,CAAC,GAAG/G,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACgH,QAAQ,EAAEC,WAAW,CAAC,GAAGjH,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,IAAIyE,IAAI,IAAIV,MAAM,IAAIY,UAAU,EAAE;MAChCsC,QAAQ,CAAC,CAAC;MACV;MACAC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACzC,IAAI,EAAEV,MAAM,EAAEY,UAAU,CAAC,CAAC;;EAE9B;EACA,MAAMuC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpB,eAAe,CAAC,EAAE,CAAC;IACnBE,YAAY,CAAC,CAAC,CAAC,CAAC;IAChBc,SAAS,CAAC,CAAC,CAAC,CAAC;IACbE,WAAW,CAAC,CAAC,CAAC,CAAC;IACfZ,aAAa,CAAC,EAAE,CAAC;IACjBF,YAAY,CAAC,CAAC,CAAC;IACfI,yBAAyB,CAAC,KAAK,CAAC;IAChCE,wBAAwB,CAAC,IAAI,CAAC;IAC9BE,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMK,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF7B,cAAc,CAAC,IAAI,CAAC;MACpB,MAAM+B,QAAQ,GAAG,MAAM/D,WAAW,CAACgE,OAAO,CAACzC,UAAU,CAAC;;MAEtD;MACA,MAAM0C,eAAe,GAAGF,QAAQ,CAACG,MAAM,CAACxD,IAAI,IAC1C,CAACP,gBAAgB,CAACO,IAAI,CAAC,IACvBA,IAAI,CAACyD,sBAAsB,KAAK,CAClC,CAAC;;MAED;MACA,MAAMC,WAAW,GAAGH,eAAe,CAACC,MAAM,CAACxD,IAAI,IAAID,YAAY,CAACC,IAAI,EAAEC,MAAM,CAAC,CAAC;MAC9E,MAAM0D,aAAa,GAAGJ,eAAe,CAACC,MAAM,CAACxD,IAAI,IAAI,CAACD,YAAY,CAACC,IAAI,EAAEC,MAAM,CAAC,CAAC;MAEjFyB,UAAU,CAAC6B,eAAe,CAAC;MAC3B3B,kBAAkB,CAAC8B,WAAW,CAAC;MAC/B5B,oBAAoB,CAAC6B,aAAa,CAAC;MAEnCC,OAAO,CAACC,GAAG,CAAC,kBAAkBH,WAAW,CAACjD,MAAM,iBAAiBkD,aAAa,CAAClD,MAAM,gBAAgB,CAAC;IACxG,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD/C,OAAO,CAAC,mCAAmC,IAAI+C,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACRzC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM0C,0BAA0B,GAAIhE,IAAI,IAAK;IAC3CgC,eAAe,CAACiC,IAAI,IAAI;MACtB,MAAMC,UAAU,GAAGD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKrE,IAAI,CAACqE,OAAO,CAAC;MAE7D,IAAIH,UAAU,EAAE;QACd;QACA,MAAMI,QAAQ,GAAGrC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,IAAIpC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE;QACjF,IAAID,QAAQ,EAAE;UACZ,IAAI,CAACE,MAAM,CAACC,OAAO,CAAC,qBAAqBzE,IAAI,CAACqE,OAAO,uCAAuCpC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,oBAAoB,CAAC,EAAE;YACxI,OAAOJ,IAAI;UACb;QACF;;QAEA;QACA,MAAMS,WAAW,GAAGT,IAAI,CAACT,MAAM,CAACY,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKrE,IAAI,CAACqE,OAAO,CAAC;;QAEhE;QACA,MAAMM,YAAY,GAAG;UAAE,GAAG1C;QAAU,CAAC;QACrC,OAAO0C,YAAY,CAAC3E,IAAI,CAACqE,OAAO,CAAC;QACjCnC,YAAY,CAACyC,YAAY,CAAC;;QAE1B;QACA3B,SAAS,CAAC4B,UAAU,IAAI;UACtB,MAAMC,SAAS,GAAG;YAAE,GAAGD;UAAW,CAAC;UACnC,OAAOC,SAAS,CAAC7E,IAAI,CAACqE,OAAO,CAAC;UAC9B,OAAOQ,SAAS;QAClB,CAAC,CAAC;QACF3B,WAAW,CAAC4B,YAAY,IAAI;UAC1B,MAAMC,WAAW,GAAG;YAAE,GAAGD;UAAa,CAAC;UACvC,OAAOC,WAAW,CAAC/E,IAAI,CAACqE,OAAO,CAAC;UAChC,OAAOU,WAAW;QACpB,CAAC,CAAC;QAEF,OAAOL,WAAW;MACpB,CAAC,MAAM;QACL;QACA,OAAO,CAAC,GAAGT,IAAI,EAAEjE,IAAI,CAAC;MACxB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMgF,4BAA4B,GAAIhF,IAAI,IAAK;IAC7C0C,wBAAwB,CAAC;MAAE1C,IAAI;MAAEC;IAAO,CAAC,CAAC;IAC1CuC,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMyC,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAIxC,qBAAqB,EAAE;MACzB,MAAM;QAAEzC;MAAK,CAAC,GAAGyC,qBAAqB;;MAEtC;MACAT,eAAe,CAACiC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAE,GAAGjE,IAAI;QAAEkF,eAAe,EAAE;MAAK,CAAC,CAAC,CAAC;MAEtE1C,yBAAyB,CAAC,KAAK,CAAC;MAChCE,wBAAwB,CAAC,IAAI,CAAC;MAE9B5B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,sBAAsBd,IAAI,CAACqE,OAAO,2DAA2D,CAAC;IAC5G;EACF,CAAC;;EAED;EACA,MAAMc,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;IAC3C;IACAnD,YAAY,CAAC+B,IAAI,KAAK;MACpB,GAAGA,IAAI;MACP,CAACmB,MAAM,GAAGC;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAC,aAAa,CAACF,MAAM,EAAEC,KAAK,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACF,MAAM,EAAEC,KAAK,KAAK;IACvC,MAAMrF,IAAI,GAAGyB,OAAO,CAAC8D,IAAI,CAACnB,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKe,MAAM,CAAC;IACpD,IAAI,CAACpF,IAAI,EAAE;;IAEX;IACAgD,SAAS,CAACiB,IAAI,IAAI;MAChB,MAAMY,SAAS,GAAG;QAAE,GAAGZ;MAAK,CAAC;MAC7B,OAAOY,SAAS,CAACO,MAAM,CAAC;MACxB,OAAOP,SAAS;IAClB,CAAC,CAAC;IAEF3B,WAAW,CAACe,IAAI,IAAI;MAClB,MAAMc,WAAW,GAAG;QAAE,GAAGd;MAAK,CAAC;MAC/B,OAAOc,WAAW,CAACK,MAAM,CAAC;MAC1B,OAAOL,WAAW;IACpB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACM,KAAK,IAAIA,KAAK,CAACd,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjC,OAAO,IAAI,CAAC,CAAC;IACf;;IAEA;IACA,IAAIiB,KAAK,CAACC,UAAU,CAACJ,KAAK,CAAC,CAAC,IAAII,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC,EAAE;MACtDrC,SAAS,CAACiB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACmB,MAAM,GAAG;MACZ,CAAC,CAAC,CAAC;MACH,OAAO,KAAK;IACd;IAEA,MAAMM,WAAW,GAAGD,UAAU,CAACJ,KAAK,CAAC;;IAErC;IACA,IAAIrF,IAAI,CAAC2F,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACzF,IAAI,CAAC2F,aAAa,CAAC,EAAE;MACtEzC,WAAW,CAACe,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACmB,MAAM,GAAG,mBAAmBM,WAAW,yCAAyC1F,IAAI,CAAC2F,aAAa;MACrG,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,MAAMC,oBAAoB,GAAGC,MAAM,CAACC,OAAO,CAAC7D,SAAS,CAAC,CACnDuB,MAAM,CAAC,CAAC,CAACuC,EAAE,EAAEC,CAAC,CAAC,KAAKD,EAAE,KAAKX,MAAM,CAAC,CAAC;IAAA,CACnCa,MAAM,CAAC,CAACC,GAAG,EAAE,CAACF,CAAC,EAAEG,KAAK,CAAC,KAAKD,GAAG,GAAGT,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGT,WAAW;IAE7E,IAAIE,oBAAoB,GAAG3F,MAAM,CAACmG,aAAa,EAAE;MAC/ClD,WAAW,CAACe,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACmB,MAAM,GAAG,qBAAqBQ,oBAAoB,CAACS,OAAO,CAAC,CAAC,CAAC,uBAAuBpG,MAAM,CAACmG,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC;MACtH,CAAC,CAAC,CAAC;IACL;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAM1B,SAAS,GAAG,CAAC,CAAC;IACpB,MAAME,WAAW,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAIhD,YAAY,CAACtB,MAAM,KAAK,CAAC,EAAE;MAC7BM,OAAO,CAAC,0BAA0B,CAAC;MACnC,OAAO,KAAK;IACd;;IAEA;IACA,KAAK,MAAMf,IAAI,IAAI+B,YAAY,EAAE;MAC/B,MAAMoE,KAAK,GAAGlE,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC;;MAErC;MACA,IAAI,CAAC8B,KAAK,IAAIA,KAAK,CAAC5B,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCM,SAAS,CAAC7E,IAAI,CAACqE,OAAO,CAAC,GAAG,uCAAuC;QACjEkC,OAAO,GAAG,KAAK;QACf;MACF;;MAEA;MACA,IAAIf,KAAK,CAACC,UAAU,CAACU,KAAK,CAAC,CAAC,IAAIV,UAAU,CAACU,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDtB,SAAS,CAAC7E,IAAI,CAACqE,OAAO,CAAC,GAAG,sCAAsC;QAChEkC,OAAO,GAAG,KAAK;QACf;MACF;MAEA,MAAMb,WAAW,GAAGD,UAAU,CAACU,KAAK,CAAC;;MAErC;MACA,IAAInG,IAAI,CAAC2F,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACzF,IAAI,CAAC2F,aAAa,CAAC,EAAE;QACtEZ,WAAW,CAAC/E,IAAI,CAACqE,OAAO,CAAC,GAAG,mBAAmBqB,WAAW,yCAAyC1F,IAAI,CAAC2F,aAAa,IAAI;MAC3H;IACF;IAEA3C,SAAS,CAAC6B,SAAS,CAAC;IACpB3B,WAAW,CAAC6B,WAAW,CAAC;;IAExB;IACA,IAAI,CAACwB,OAAO,EAAE;MACZ,OAAO,KAAK;IACd;;IAEA;IACA,MAAMX,oBAAoB,GAAGC,MAAM,CAACW,MAAM,CAACvE,SAAS,CAAC,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGT,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7G,IAAIP,oBAAoB,GAAG3F,MAAM,CAACmG,aAAa,EAAE;MAC/C;MACAtD,iBAAiB,CAAC;QAChB8C,oBAAoB;QACpBa,kBAAkB,EAAExG,MAAM,CAACmG,aAAa;QACxCrE,YAAY,EAAEA,YAAY,CAACtB;MAC7B,CAAC,CAAC;MACFmC,iBAAiB,CAAC,IAAI,CAAC;MACvB,OAAO,KAAK,CAAC,CAAC;IAChB;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAM8D,UAAU,GAAG,MAAAA,CAAOC,SAAS,GAAG,KAAK,KAAK;IAC9C,IAAI;MACF;MACA,IAAI,CAACA,SAAS,IAAI,CAACL,gBAAgB,CAAC,CAAC,EAAE;QACrC;MACF;MAEA9E,SAAS,CAAC,IAAI,CAAC;;MAEf;MACA,MAAMoF,OAAO,GAAG,EAAE;MAClB,IAAI7D,MAAM,GAAG,EAAE;MAEf,KAAK,MAAM/C,IAAI,IAAI+B,YAAY,EAAE;QAC/B,IAAI;UACF,MAAM2D,WAAW,GAAGD,UAAU,CAACxD,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,CAAC;;UAEvD;UACA,MAAMwC,kBAAkB,GAAGD,OAAO,CAACX,MAAM,CAAC,CAACC,GAAG,EAAEY,CAAC,KAAKZ,GAAG,GAAGY,CAAC,CAACpB,WAAW,EAAE,CAAC,CAAC;UAC7E,MAAMqB,cAAc,GAAGJ,SAAS,IAAKE,kBAAkB,GAAGnB,WAAW,GAAIzF,MAAM,CAACmG,aAAa,IAAIpG,IAAI,CAACkF,eAAe;;UAErH;UACA,MAAM8B,MAAM,GAAG,MAAM1H,WAAW,CAAC2H,iBAAiB,CAChDpG,UAAU,EACVb,IAAI,CAACqE,OAAO,EACZqB,WAAW,EACXzF,MAAM,CAACiH,SAAS,EAChBH,cACF,CAAC;UAEDH,OAAO,CAACO,IAAI,CAAC;YACXnH,IAAI,EAAEA,IAAI,CAACqE,OAAO;YAClBqB,WAAW;YACX0B,OAAO,EAAE,IAAI;YACbC,eAAe,EAAErH,IAAI,CAACkF;UACxB,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOpB,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,sCAAsC9D,IAAI,CAACqE,OAAO,GAAG,EAAEP,KAAK,CAAC;UAC3Ef,MAAM,CAACoE,IAAI,CAAC;YACVnH,IAAI,EAAEA,IAAI,CAACqE,OAAO;YAClBP,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;UAC1B,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,IAAIhB,MAAM,CAACtC,MAAM,KAAK,CAAC,EAAE;QACvB,MAAM6G,iBAAiB,GAAGV,OAAO,CAACpD,MAAM,CAACsD,CAAC,IAAIA,CAAC,CAACO,eAAe,CAAC,CAAC5G,MAAM;QACvE,IAAIsD,OAAO,GAAG,GAAG6C,OAAO,CAACnG,MAAM,+BAA+B;QAC9D,IAAI6G,iBAAiB,GAAG,CAAC,EAAE;UACzBvD,OAAO,IAAI,KAAKuD,iBAAiB,gCAAgC;QACnE;QACAxG,SAAS,CAACiD,OAAO,CAAC;QAClBnD,OAAO,CAAC,CAAC;MACX,CAAC,MAAM,IAAIgG,OAAO,CAACnG,MAAM,GAAG,CAAC,EAAE;QAC7BK,SAAS,CAAC,GAAG8F,OAAO,CAACnG,MAAM,kCAAkCsC,MAAM,CAACtC,MAAM,UAAU,CAAC;QACrFM,OAAO,CAAC,WAAWgC,MAAM,CAACwE,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAACxH,IAAI,KAAKwH,CAAC,CAAC1D,KAAK,EAAE,CAAC,CAAC2D,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACzE7G,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACLG,OAAO,CAAC,mCAAmCgC,MAAM,CAACwE,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAACxH,IAAI,KAAKwH,CAAC,CAAC1D,KAAK,EAAE,CAAC,CAAC2D,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MACnG;IACF,CAAC,CAAC,OAAO3D,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD/C,OAAO,CAAC,iCAAiC,IAAI+C,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACtF,CAAC,SAAS;MACRvC,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMkG,uBAAuB,GAAGA,CAAA,KAAM;IACpC9E,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,IAAI,CAAC;IACvB4D,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMiB,UAAU,GAAIC,QAAQ,IAAK;IAC/B,IAAI,CAACvF,UAAU,EAAE,OAAOuF,QAAQ;IAEhC,OAAOA,QAAQ,CAACpE,MAAM,CAACxD,IAAI,IACzBA,IAAI,CAACqE,OAAO,CAACwD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,UAAU,CAACwF,WAAW,CAAC,CAAC,CAAC,IAC5D7H,IAAI,CAACE,SAAS,IAAIF,IAAI,CAACE,SAAS,CAAC2H,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,UAAU,CAACwF,WAAW,CAAC,CAAC,CAAE,IAClF7H,IAAI,CAAC+H,mBAAmB,IAAI/H,IAAI,CAAC+H,mBAAmB,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,UAAU,CAACwF,WAAW,CAAC,CAAC,CAAE,IACtG7H,IAAI,CAACgI,iBAAiB,IAAIhI,IAAI,CAACgI,iBAAiB,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,UAAU,CAACwF,WAAW,CAAC,CAAC,CACnG,CAAC;EACH,CAAC;EAED,MAAMI,mBAAmB,GAAGN,UAAU,CAAChG,eAAe,CAAC;EACvD,MAAMuG,qBAAqB,GAAGP,UAAU,CAAC9F,iBAAiB,CAAC;;EAE3D;EACA,MAAM+D,oBAAoB,GAAGC,MAAM,CAACW,MAAM,CAACvE,SAAS,CAAC,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC3E,MAAMd,KAAK,GAAGI,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC;IACpC,OAAOX,KAAK,CAACH,KAAK,CAAC,GAAGa,GAAG,GAAGA,GAAG,GAAGb,KAAK;EACzC,CAAC,EAAE,CAAC,CAAC;EAEL,MAAM8C,WAAW,GAAGvC,oBAAoB,GAAG3F,MAAM,CAACmG,aAAa;;EAE/D;EACA,MAAMgC,cAAc,GAAGA,CAACR,QAAQ,EAAE7H,YAAY,GAAG,IAAI,KAAK;IACxD,IAAIsB,WAAW,EAAE;MACf,oBACEzB,OAAA,CAACnD,GAAG;QAAC4L,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,eAC5D7I,OAAA,CAACxC,gBAAgB;UAAAsL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAEV;IAEA,IAAIjB,QAAQ,CAACnH,MAAM,KAAK,CAAC,EAAE;MACzB,oBACEb,OAAA,CAACvC,KAAK;QAACyL,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAClC1I,YAAY,GAAG,sCAAsC,GAAG;MAAoC;QAAA2I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF,CAAC;IAEZ;IAEA,oBACEjJ,OAAA,CAACjC,IAAI;MAACoL,KAAK;MAAAN,QAAA,EACRb,QAAQ,CAACL,GAAG,CAAEvH,IAAI,IAAK;QACtB,MAAMkE,UAAU,GAAGnC,YAAY,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKrE,IAAI,CAACqE,OAAO,CAAC;QACrE,MAAMC,QAAQ,GAAGrC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,IAAIpC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE;QACjF,MAAMyE,QAAQ,GAAGjG,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC;QACrC,MAAM4E,UAAU,GAAGhG,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAC;QAEzC,oBACEzE,OAAA,CAAChC,QAAQ;UAEPyK,EAAE,EAAE;YACFa,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,CAAC;YACfC,EAAE,EAAE,CAAC;YACLC,OAAO,EAAEnF,UAAU,GAAG,yBAAyB,GAAG,SAAS;YAC3D,SAAS,EAAE;cACTmF,OAAO,EAAEnF,UAAU,GAAG,yBAAyB,GAAG;YACpD;UACF,CAAE;UAAAuE,QAAA,gBAEF7I,OAAA,CAAC/B,YAAY;YACXyL,OAAO,eACL1J,OAAA,CAACnD,GAAG;cAAC4L,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEiB,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAE,CAAE;cAAAf,QAAA,gBACzD7I,OAAA,CAACX,SAAS;gBAACwK,QAAQ,EAAC,OAAO;gBAACC,KAAK,EAAE3J,YAAY,GAAG,SAAS,GAAG;cAAU;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3EjJ,OAAA,CAACpD,UAAU;gBAACmN,OAAO,EAAC,WAAW;gBAAAlB,QAAA,EAAEzI,IAAI,CAACqE;cAAO;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC3DjJ,OAAA,CAACrC,IAAI;gBACHqM,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAE7J,IAAI,CAACE,SAAS,IAAI,KAAM;gBAC/BwJ,KAAK,EAAE3J,YAAY,GAAG,SAAS,GAAG,SAAU;gBAC5C4J,OAAO,EAAC;cAAU;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,EACD,CAAC9I,YAAY,iBACZH,OAAA,CAACrC,IAAI;gBACHqM,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAC,eAAe;gBACrBH,KAAK,EAAC,OAAO;gBACbC,OAAO,EAAC;cAAQ;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;YACDiB,SAAS,eACPlK,OAAA,CAACnD,GAAG;cAAAgM,QAAA,gBACF7I,OAAA,CAACpD,UAAU;gBAACmN,OAAO,EAAC,OAAO;gBAACD,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,GAC/CzI,IAAI,CAAC+H,mBAAmB,IAAI,KAAK,EAAC,UAAG,EAAC/H,IAAI,CAACgI,iBAAiB,IAAI,KAAK;cAAA;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACbjJ,OAAA,CAACpD,UAAU;gBAACmN,OAAO,EAAC,OAAO;gBAACD,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,GAAC,cACrC,EAACzI,IAAI,CAACI,OAAO,IAAI,KAAK,EAAC,oBAAkB,EAACJ,IAAI,CAAC2F,aAAa,IAAI,KAAK;cAAA;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC,EACZ3E,UAAU,iBACTtE,OAAA,CAACnD,GAAG;gBAAC4L,EAAE,EAAE;kBAAE0B,EAAE,EAAE;gBAAE,CAAE;gBAAAtB,QAAA,eACjB7I,OAAA,CAAClD,SAAS;kBACRkN,IAAI,EAAC,OAAO;kBACZI,IAAI,EAAC,QAAQ;kBACbH,KAAK,EAAC,cAAc;kBACpBxE,KAAK,EAAEpD,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,IAAI,EAAG;kBACrC4F,QAAQ,EAAGzC,CAAC,IAAKrC,iBAAiB,CAACnF,IAAI,CAACqE,OAAO,EAAEmD,CAAC,CAAC0C,MAAM,CAAC7E,KAAK,CAAE;kBACjEvB,KAAK,EAAE,CAAC,CAACkF,QAAS;kBAClBmB,UAAU,EAAEnB,QAAQ,IAAIC,UAAW;kBACnCmB,mBAAmB,EAAE;oBACnB/B,EAAE,EAAE;sBAAEqB,KAAK,EAAET,UAAU,IAAI,CAACD,QAAQ,GAAG,cAAc,GAAG;oBAAa;kBACvE,CAAE;kBACFX,EAAE,EAAE;oBAAEgC,KAAK,EAAE;kBAAQ;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFjJ,OAAA,CAAC9B,uBAAuB;YAAA2K,QAAA,eACtB7I,OAAA,CAACrD,MAAM;cACLqN,IAAI,EAAC,OAAO;cACZD,OAAO,EAAEzF,UAAU,GAAG,WAAW,GAAG,UAAW;cAC/CwF,KAAK,EAAE3J,YAAY,GAAG,SAAS,GAAG,SAAU;cAC5CuK,OAAO,EAAEA,CAAA,KAAMvK,YAAY,GAAGiE,0BAA0B,CAAChE,IAAI,CAAC,GAAGgF,4BAA4B,CAAChF,IAAI,CAAE;cACpGuK,SAAS,EAAErG,UAAU,gBAAGtE,OAAA,CAACT,eAAe;gBAAAuJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGjJ,OAAA,CAACvB,OAAO;gBAAAqK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EAEzDvE,UAAU,GAAG,aAAa,GAAG;YAAW;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACc,CAAC;QAAA,GAtErB7I,IAAI,CAACqE,OAAO;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuET,CAAC;MAEf,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX,CAAC;EAED,oBACEjJ,OAAA,CAACzD,MAAM;IAACwE,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAAC4J,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAhC,QAAA,gBAC3D7I,OAAA,CAACxD,WAAW;MAAAqM,QAAA,eACV7I,OAAA,CAACnD,GAAG;QAAC4L,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEiB,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAf,QAAA,gBACzD7I,OAAA,CAACX,SAAS;UAAAyJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACbjJ,OAAA,CAACpD,UAAU;UAACmN,OAAO,EAAC,IAAI;UAAAlB,QAAA,GAAC,4BACG,EAACpI,eAAe,CAACJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEiH,SAAS,CAAC;QAAA;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACdjJ,OAAA,CAACvD,aAAa;MAAAoM,QAAA,EACX,CAACxI,MAAM,gBACNL,OAAA,CAACvC,KAAK;QAACyL,QAAQ,EAAC,OAAO;QAAAL,QAAA,EAAC;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,gBAE1DjJ,OAAA,CAAAE,SAAA;QAAA2I,QAAA,gBAEE7I,OAAA,CAAC5B,IAAI;UAACqK,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,gBAClB7I,OAAA,CAAC1B,UAAU;YACTwM,KAAK,EAAC,iBAAiB;YACvBC,oBAAoB,EAAE;cAAEhB,OAAO,EAAE;YAAY;UAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACFjJ,OAAA,CAAC3B,WAAW;YAACoK,EAAE,EAAE;cAAEuC,EAAE,EAAE;YAAE,CAAE;YAAAnC,QAAA,eACzB7I,OAAA,CAACnD,GAAG;cAAC4L,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEuC,QAAQ,EAAE,MAAM;gBAAErB,GAAG,EAAE;cAAE,CAAE;cAAAf,QAAA,gBACrD7I,OAAA,CAACnD,GAAG;gBAAAgM,QAAA,gBACF7I,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAACD,KAAK,EAAC,gBAAgB;kBAAAjB,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzEjJ,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAAAlB,QAAA,EAAExI,MAAM,CAACiH;gBAAS;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACNjJ,OAAA,CAACnD,GAAG;gBAAAgM,QAAA,gBACF7I,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAACD,KAAK,EAAC,gBAAgB;kBAAAjB,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzEjJ,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAAAlB,QAAA,EAAExI,MAAM,CAACC,SAAS,IAAI;gBAAK;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNjJ,OAAA,CAACnD,GAAG;gBAAAgM,QAAA,gBACF7I,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAACD,KAAK,EAAC,gBAAgB;kBAAAjB,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1EjJ,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAAAlB,QAAA,EAAExI,MAAM,CAACG,OAAO,IAAI;gBAAK;kBAAAsI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNjJ,OAAA,CAACnD,GAAG;gBAAAgM,QAAA,gBACF7I,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAACD,KAAK,EAAC,gBAAgB;kBAAAjB,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7EjJ,OAAA,CAACpD,UAAU;kBACTmN,OAAO,EAAC,OAAO;kBACfD,KAAK,EAAEvB,WAAW,GAAG,YAAY,GAAG,cAAe;kBACnDE,EAAE,EAAE;oBAAEyC,UAAU,EAAE3C,WAAW,GAAG,MAAM,GAAG;kBAAS,CAAE;kBAAAM,QAAA,GAEnD,EAAAxH,qBAAA,GAAAhB,MAAM,CAACmG,aAAa,cAAAnF,qBAAA,uBAApBA,qBAAA,CAAsBoF,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,IAC3C;gBAAA;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjJ,OAAA,CAACnD,GAAG;gBAAAgM,QAAA,gBACF7I,OAAA,CAACpD,UAAU;kBAACmN,OAAO,EAAC,OAAO;kBAACD,KAAK,EAAC,gBAAgB;kBAAAjB,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrEjJ,OAAA,CAACrC,IAAI;kBACHsM,KAAK,EAAE5J,MAAM,CAAC8K,YAAY,IAAI,KAAM;kBACpCnB,IAAI,EAAC,OAAO;kBACZF,KAAK,EACHzJ,MAAM,CAAC8K,YAAY,KAAK,aAAa,GAAG,SAAS,GACjD9K,MAAM,CAAC8K,YAAY,KAAK,QAAQ,GAAG,SAAS,GAC5C9K,MAAM,CAAC8K,YAAY,KAAK,MAAM,GAAG,OAAO,GACxC9K,MAAM,CAAC8K,YAAY,KAAK,WAAW,GAAG,SAAS,GAAG;gBACnD;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGPjJ,OAAA,CAAClD,SAAS;UACR+N,SAAS;UACTZ,KAAK,EAAC,YAAY;UAClBF,OAAO,EAAC,UAAU;UAClBtE,KAAK,EAAEhD,UAAW;UAClB4H,QAAQ,EAAGzC,CAAC,IAAKlF,aAAa,CAACkF,CAAC,CAAC0C,MAAM,CAAC7E,KAAK,CAAE;UAC/C2F,WAAW,EAAC,wCAAwC;UACpD3C,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,EAGDxH,WAAW,gBACVzB,OAAA,CAACnD,GAAG;UAAC4L,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAC,QAAA,eAC5D7I,OAAA,CAACxC,gBAAgB;YAAAsL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJoC,YAAY,CAACxK,MAAM,KAAK,CAAC,gBAC3Bb,OAAA,CAACvC,KAAK;UAACyL,QAAQ,EAAC,MAAM;UAAAL,QAAA,EAAC;QAEvB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAERjJ,OAAA,CAAC5C,cAAc;UAACkO,SAAS,EAAE/N,KAAM;UAACkL,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,eAC9C7I,OAAA,CAAC/C,KAAK;YAAC+M,IAAI,EAAC,OAAO;YAAAnB,QAAA,gBACjB7I,OAAA,CAAC3C,SAAS;cAAAwL,QAAA,eACR7I,OAAA,CAAC1C,QAAQ;gBAACmL,EAAE,EAAE;kBAAEgB,OAAO,EAAE;gBAAU,CAAE;gBAAAZ,QAAA,gBACnC7I,OAAA,CAAC7C,SAAS;kBAACoO,OAAO,EAAC;gBAAU;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1CjJ,OAAA,CAAC7C,SAAS;kBAAA0L,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9BjJ,OAAA,CAAC7C,SAAS;kBAAA0L,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChCjJ,OAAA,CAAC7C,SAAS;kBAAA0L,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjCjJ,OAAA,CAAC7C,SAAS;kBAAA0L,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACpCjJ,OAAA,CAAC7C,SAAS;kBAAA0L,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACnCjJ,OAAA,CAAC7C,SAAS;kBAAA0L,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZjJ,OAAA,CAAC9C,SAAS;cAAA2L,QAAA,EACPwC,YAAY,CAAC1D,GAAG,CAAEvH,IAAI,IAAK;gBAC1B,MAAMkE,UAAU,GAAGnC,YAAY,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKrE,IAAI,CAACqE,OAAO,CAAC;gBACrE,oBACEzE,OAAA,CAAC1C,QAAQ;kBAEPkO,KAAK;kBACLC,QAAQ,EAAEnH,UAAW;kBACrBoG,OAAO,EAAEA,CAAA,KAAMgB,gBAAgB,CAACtL,IAAI,CAAE;kBACtCqI,EAAE,EAAE;oBAAEkD,MAAM,EAAE;kBAAU,CAAE;kBAAA9C,QAAA,gBAE1B7I,OAAA,CAAC7C,SAAS;oBAACoO,OAAO,EAAC,UAAU;oBAAA1C,QAAA,eAC3B7I,OAAA,CAACjD,QAAQ;sBACP6O,OAAO,EAAEtH,UAAW;sBACpB+F,QAAQ,EAAGzC,CAAC,IAAK;wBACfA,CAAC,CAACiE,eAAe,CAAC,CAAC;wBACnBH,gBAAgB,CAACtL,IAAI,CAAC;sBACxB;oBAAE;sBAAA0I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZjJ,OAAA,CAAC7C,SAAS;oBAAA0L,QAAA,EAAEzI,IAAI,CAACqE;kBAAO;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrCjJ,OAAA,CAAC7C,SAAS;oBAAA0L,QAAA,EAAEzI,IAAI,CAACE,SAAS,IAAI;kBAAK;oBAAAwI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAChDjJ,OAAA,CAAC7C,SAAS;oBAAA0L,QAAA,eACR7I,OAAA,CAACpC,OAAO;sBAACkN,KAAK,EAAE,OAAO1K,IAAI,CAAC+H,mBAAmB,IAAI,KAAK,SAAS/H,IAAI,CAACgI,iBAAiB,IAAI,KAAK,EAAG;sBAAAS,QAAA,eACjG7I,OAAA,CAACnD,GAAG;wBAAC4L,EAAE,EAAE;0BAAEmC,QAAQ,EAAE,GAAG;0BAAEkB,QAAQ,EAAE,QAAQ;0BAAEC,YAAY,EAAE,UAAU;0BAAEC,UAAU,EAAE;wBAAS,CAAE;wBAAAnD,QAAA,GAC5FzI,IAAI,CAAC+H,mBAAmB,IAAI,KAAK,EAAC,UAAG,EAAC/H,IAAI,CAACgI,iBAAiB,IAAI,KAAK;sBAAA;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACZjJ,OAAA,CAAC7C,SAAS;oBAAA0L,QAAA,EAAEzI,IAAI,CAAC2F,aAAa,IAAI;kBAAK;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACpDjJ,OAAA,CAAC7C,SAAS;oBAAA0L,QAAA,EACPvE,UAAU,gBACTtE,OAAA,CAAClD,SAAS;sBACRkN,IAAI,EAAC,OAAO;sBACZI,IAAI,EAAC,QAAQ;sBACb3E,KAAK,EAAEpD,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,IAAI,EAAG;sBACrC4F,QAAQ,EAAGzC,CAAC,IAAK;wBACfA,CAAC,CAACiE,eAAe,CAAC,CAAC;wBACnBtG,iBAAiB,CAACnF,IAAI,CAACqE,OAAO,EAAEmD,CAAC,CAAC0C,MAAM,CAAC7E,KAAK,CAAC;sBACjD,CAAE;sBACFiF,OAAO,EAAG9C,CAAC,IAAKA,CAAC,CAACiE,eAAe,CAAC,CAAE;sBACpC3H,KAAK,EAAE,CAAC,CAACf,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAE;sBAC9B8F,UAAU,EAAEpH,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,IAAIpB,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAE;sBAC3D+F,mBAAmB,EAAE;wBACnB/B,EAAE,EAAE;0BAAEqB,KAAK,EAAEzG,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAC,IAAI,CAACtB,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,GAAG,cAAc,GAAG;wBAAa;sBAC/F,CAAE;sBACFwH,UAAU,EAAE;wBACVC,YAAY,EAAE7I,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAC,IAAI,CAACtB,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,gBAC3DzE,OAAA,CAACpC,OAAO;0BAACkN,KAAK,EAAEzH,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAE;0BAAAoE,QAAA,eACrC7I,OAAA,CAACf,WAAW;4BAAC6K,KAAK,EAAC,SAAS;4BAACD,QAAQ,EAAC;0BAAO;4BAAAf,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC,GACR;sBACN;oBAAE;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,GAEF;kBACD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ,CAAC,eACZjJ,OAAA,CAAC7C,SAAS;oBAAA0L,QAAA,eACR7I,OAAA,CAACrC,IAAI;sBACHqM,IAAI,EAAC,OAAO;sBACZC,KAAK,EAAE7J,IAAI,CAAC+L,mBAAmB,IAAI,eAAgB;sBACnDrC,KAAK,EAAElK,kBAAkB,CAACQ,IAAI,CAAC+L,mBAAmB;oBAAE;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC;gBAAA,GA3DP7I,IAAI,CAACqE,OAAO;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4DT,CAAC;cAEf,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACjB,EAGA9G,YAAY,CAACtB,MAAM,GAAG,CAAC,iBACtBb,OAAA,CAACnD,GAAG;UAAC4L,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,gBACjB7I,OAAA,CAACpD,UAAU;YAACmN,OAAO,EAAC,WAAW;YAACqC,YAAY;YAAAvD,QAAA,GAAC,uBACtB,EAAC1G,YAAY,CAACtB,MAAM,EAAC,QAC5C;UAAA;YAAAiI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjJ,OAAA,CAAC5C,cAAc;YAACkO,SAAS,EAAE/N,KAAM;YAAAsL,QAAA,eAC/B7I,OAAA,CAAC/C,KAAK;cAAC+M,IAAI,EAAC,OAAO;cAAAnB,QAAA,gBACjB7I,OAAA,CAAC3C,SAAS;gBAAAwL,QAAA,eACR7I,OAAA,CAAC1C,QAAQ;kBAACmL,EAAE,EAAE;oBAAEgB,OAAO,EAAE;kBAAU,CAAE;kBAAAZ,QAAA,gBACnC7I,OAAA,CAAC7C,SAAS;oBAAA0L,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BjJ,OAAA,CAAC7C,SAAS;oBAAA0L,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnCjJ,OAAA,CAAC7C,SAAS;oBAAA0L,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZjJ,OAAA,CAAC9C,SAAS;gBAAA2L,QAAA,GACP1G,YAAY,CAACwF,GAAG,CAAEvH,IAAI,iBACrBJ,OAAA,CAAC1C,QAAQ;kBAAAuL,QAAA,gBACP7I,OAAA,CAAC7C,SAAS;oBAAA0L,QAAA,EAAEzI,IAAI,CAACqE;kBAAO;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrCjJ,OAAA,CAAC7C,SAAS;oBAAA0L,QAAA,eACR7I,OAAA,CAAClD,SAAS;sBACRkN,IAAI,EAAC,OAAO;sBACZI,IAAI,EAAC,QAAQ;sBACb3E,KAAK,EAAEpD,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,IAAI,EAAG;sBACrC4F,QAAQ,EAAGzC,CAAC,IAAKrC,iBAAiB,CAACnF,IAAI,CAACqE,OAAO,EAAEmD,CAAC,CAAC0C,MAAM,CAAC7E,KAAK,CAAE;sBACjEvB,KAAK,EAAE,CAAC,CAACf,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAE;sBAC9B8F,UAAU,EAAEpH,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,IAAIpB,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAE;sBAC3D+F,mBAAmB,EAAE;wBACnB/B,EAAE,EAAE;0BAAEqB,KAAK,EAAEzG,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAC,IAAI,CAACtB,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,GAAG,cAAc,GAAG;wBAAa;sBAC/F;oBAAE;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZjJ,OAAA,CAAC7C,SAAS;oBAAA0L,QAAA,eACR7I,OAAA,CAACtC,UAAU;sBACTsM,IAAI,EAAC,OAAO;sBACZF,KAAK,EAAC,OAAO;sBACbY,OAAO,EAAEA,CAAA,KAAMgB,gBAAgB,CAACtL,IAAI,CAAE;sBAAAyI,QAAA,eAEtC7I,OAAA,CAACrB,UAAU;wBAACkL,QAAQ,EAAC;sBAAO;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAvBC7I,IAAI,CAACqE,OAAO;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwBjB,CACX,CAAC,eACFjJ,OAAA,CAAC1C,QAAQ;kBAAAuL,QAAA,eACP7I,OAAA,CAAC7C,SAAS;oBAACkP,OAAO,EAAE,CAAE;oBAACC,KAAK,EAAC,OAAO;oBAAAzD,QAAA,gBAClC7I,OAAA,CAACpD,UAAU;sBAACmN,OAAO,EAAC,OAAO;sBAAAlB,QAAA,gBACzB7I,OAAA;wBAAA6I,QAAA,EAAQ;sBAAuB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EACvChD,MAAM,CAACW,MAAM,CAACvE,SAAS,CAAC,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;wBAC9C,MAAMd,KAAK,GAAGI,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC;wBACpC,OAAOX,KAAK,CAACH,KAAK,CAAC,GAAGa,GAAG,GAAGA,GAAG,GAAGb,KAAK;sBACzC,CAAC,EAAE,CAAC,CAAC,CAACgB,OAAO,CAAC,CAAC,CAAC,EACjB,IACH;oBAAA;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbjJ,OAAA,CAACpD,UAAU;sBAACmN,OAAO,EAAC,OAAO;sBAAAlB,QAAA,gBACzB7I,OAAA;wBAAA6I,QAAA,EAAQ;sBAAqB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAA3H,sBAAA,GAAAjB,MAAM,CAACmG,aAAa,cAAAlF,sBAAA,uBAApBA,sBAAA,CAAsBmF,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,IAClF;oBAAA;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACN,EAGAhD,MAAM,CAACsG,IAAI,CAAClJ,QAAQ,CAAC,CAACxC,MAAM,GAAG,CAAC,iBAC/Bb,OAAA,CAACvC,KAAK;UAACyL,QAAQ,EAAC,SAAS;UAACT,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,gBACtC7I,OAAA,CAACpD,UAAU;YAACmN,OAAO,EAAC,WAAW;YAAAlB,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxDjJ,OAAA;YAAA6I,QAAA,EACG5C,MAAM,CAACC,OAAO,CAAC7C,QAAQ,CAAC,CAACsE,GAAG,CAAC,CAAC,CAACnC,MAAM,EAAEgH,OAAO,CAAC,kBAC9CxM,OAAA;cAAA6I,QAAA,GAAkBrD,MAAM,EAAC,IAAE,EAACgH,OAAO;YAAA,GAA1BhH,MAAM;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAyB,CACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACR,eAGDjJ,OAAA,CAACvC,KAAK;UAACyL,QAAQ,EAAC,MAAM;UAACT,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,EAAC;QAGtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,eACR;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAChBjJ,OAAA,CAACtD,aAAa;MAAAmM,QAAA,gBACZ7I,OAAA,CAACrD,MAAM;QAAC+N,OAAO,EAAE1J,OAAQ;QAACyL,QAAQ,EAAE9K,MAAO;QAAAkH,QAAA,EAAC;MAE5C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjJ,OAAA,CAACrD,MAAM;QACL+N,OAAO,EAAE5D,UAAW;QACpBgD,KAAK,EAAC,SAAS;QACfC,OAAO,EAAC,WAAW;QACnB0C,QAAQ,EAAE9K,MAAM,IAAIQ,YAAY,CAACtB,MAAM,KAAK,CAAC,IAAIoF,MAAM,CAACsG,IAAI,CAACpJ,MAAM,CAAC,CAACtC,MAAM,GAAG,CAAE;QAChF8J,SAAS,EAAEhJ,MAAM,gBAAG3B,OAAA,CAACxC,gBAAgB;UAACwM,IAAI,EAAE;QAAG;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGjJ,OAAA,CAACnB,QAAQ;UAAAiK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EACnE;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAC7H,EAAA,CA1uBIN,oBAAoB;AAAA4L,EAAA,GAApB5L,oBAAoB;AA4uB1B,eAAeA,oBAAoB;AAAC,IAAA4L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}