{"ast": null, "code": "import { daysInWeek } from \"./constants.mjs\";\n\n/**\n * @name daysToWeeks\n * @category Conversion Helpers\n * @summary Convert days to weeks.\n *\n * @description\n * Convert a number of days to a full number of weeks.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param days - The number of days to be converted\n *\n * @returns The number of days converted in weeks\n *\n * @example\n * // Convert 14 days to weeks:\n * const result = daysToWeeks(14)\n * //=> 2\n *\n * @example\n * // It uses trunc rounding:\n * const result = daysToWeeks(13)\n * //=> 1\n */\nexport function daysToWeeks(days) {\n  const weeks = days / daysInWeek;\n  const result = Math.trunc(weeks);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default daysToWeeks;", "map": {"version": 3, "names": ["daysInWeek", "daysToWeeks", "days", "weeks", "result", "Math", "trunc"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/daysToWeeks.mjs"], "sourcesContent": ["import { daysInWeek } from \"./constants.mjs\";\n\n/**\n * @name daysToWeeks\n * @category Conversion Helpers\n * @summary Convert days to weeks.\n *\n * @description\n * Convert a number of days to a full number of weeks.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param days - The number of days to be converted\n *\n * @returns The number of days converted in weeks\n *\n * @example\n * // Convert 14 days to weeks:\n * const result = daysToWeeks(14)\n * //=> 2\n *\n * @example\n * // It uses trunc rounding:\n * const result = daysToWeeks(13)\n * //=> 1\n */\nexport function daysToWeeks(days) {\n  const weeks = days / daysInWeek;\n  const result = Math.trunc(weeks);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default daysToWeeks;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAE;EAChC,MAAMC,KAAK,GAAGD,IAAI,GAAGF,UAAU;EAC/B,MAAMI,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAAC;EAChC;EACA,OAAOC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;AAClC;;AAEA;AACA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}