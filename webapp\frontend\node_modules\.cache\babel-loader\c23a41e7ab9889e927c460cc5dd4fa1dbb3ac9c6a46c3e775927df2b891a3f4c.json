{"ast": null, "code": "import React from'react';import{<PERSON><PERSON><PERSON>,<PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,CartesianG<PERSON>,<PERSON><PERSON><PERSON>,Legend,ResponsiveContainer,<PERSON><PERSON><PERSON>,Pie,Cell,ComposedChart,Line,LineChart}from'recharts';import{Box,Typography,Grid,Paper,Chip}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const COLORS={primary:'#1976d2',secondary:'#dc004e',success:'#2e7d32',warning:'#ed6c02',info:'#0288d1',error:'#d32f2f',purple:'#9c27b0',teal:'#00695c'};const BoqChart=_ref=>{var _data$cavi_per_tipo,_data$bobine_per_tipo;let{data}=_ref;if(!data)return null;// Prepara dati per grafici cavi per tipologia\nconst caviData=((_data$cavi_per_tipo=data.cavi_per_tipo)===null||_data$cavi_per_tipo===void 0?void 0:_data$cavi_per_tipo.map((cavo,index)=>{var _cavo$tipologia;return{...cavo,tipologia_short:((_cavo$tipologia=cavo.tipologia)===null||_cavo$tipologia===void 0?void 0:_cavo$tipologia.length)>8?cavo.tipologia.substring(0,8)+'...':cavo.tipologia,color:Object.values(COLORS)[index%Object.values(COLORS).length],deficit:Math.max(0,cavo.metri_da_posare-(cavo.metri_reali||0)),surplus:Math.max(0,(cavo.metri_reali||0)-cavo.metri_da_posare)};}))||[];// Prepara dati per grafici bobine disponibili\nconst bobineData=((_data$bobine_per_tipo=data.bobine_per_tipo)===null||_data$bobine_per_tipo===void 0?void 0:_data$bobine_per_tipo.map((bobina,index)=>{var _bobina$tipologia;return{...bobina,tipologia_short:((_bobina$tipologia=bobina.tipologia)===null||_bobina$tipologia===void 0?void 0:_bobina$tipologia.length)>8?bobina.tipologia.substring(0,8)+'...':bobina.tipologia,color:Object.values(COLORS)[index%Object.values(COLORS).length]};}))||[];// Calcola totali per grafici a torta\nconst totaliCavi=caviData.reduce((acc,cavo)=>{acc.teorici+=cavo.metri_teorici||0;acc.reali+=cavo.metri_reali||0;acc.da_posare+=cavo.metri_da_posare||0;return acc;},{teorici:0,reali:0,da_posare:0});const totaliData=[{name:'Metri Teorici',value:totaliCavi.teorici,color:COLORS.primary},{name:'Metri Reali',value:totaliCavi.reali,color:COLORS.success},{name:'Metri da Posare',value:totaliCavi.da_posare,color:COLORS.warning}];// Analisi deficit/surplus\nconst analisiData=caviData.map(cavo=>({tipologia:cavo.tipologia_short,tipologia_full:cavo.tipologia,deficit:cavo.deficit,surplus:cavo.surplus,necessita_acquisto:cavo.deficit>0}));const CustomTooltip=_ref2=>{let{active,payload,label}=_ref2;if(active&&payload&&payload.length){return/*#__PURE__*/_jsxs(Paper,{sx:{p:1,border:'1px solid #ccc'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:`${label}`}),payload.map((entry,index)=>/*#__PURE__*/_jsx(Typography,{variant:\"body2\",style:{color:entry.color},children:`${entry.name}: ${typeof entry.value==='number'?entry.value.toFixed(2):entry.value}`},index))]});}return null;};const renderCustomizedLabel=_ref3=>{let{cx,cy,midAngle,innerRadius,outerRadius,percent}=_ref3;if(percent<0.05)return null;const RADIAN=Math.PI/180;const radius=innerRadius+(outerRadius-innerRadius)*0.5;const x=cx+radius*Math.cos(-midAngle*RADIAN);const y=cy+radius*Math.sin(-midAngle*RADIAN);return/*#__PURE__*/_jsx(\"text\",{x:x,y:y,fill:\"white\",textAnchor:x>cx?'start':'end',dominantBaseline:\"central\",fontSize:\"12\",fontWeight:\"bold\",children:`${(percent*100).toFixed(0)}%`});};return/*#__PURE__*/_jsxs(Box,{sx:{mt:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Analisi Bill of Quantities\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:350},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,align:\"center\",children:\"Distribuzione Metri Totali\"}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:280,children:/*#__PURE__*/_jsxs(PieChart,{children:[/*#__PURE__*/_jsx(Pie,{data:totaliData,cx:\"50%\",cy:\"50%\",labelLine:false,label:renderCustomizedLabel,outerRadius:80,fill:\"#8884d8\",dataKey:\"value\",children:totaliData.map((entry,index)=>/*#__PURE__*/_jsx(Cell,{fill:entry.color},`cell-${index}`))}),/*#__PURE__*/_jsx(Tooltip,{content:/*#__PURE__*/_jsx(CustomTooltip,{})}),/*#__PURE__*/_jsx(Legend,{})]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:350},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,align:\"center\",children:\"Metri per Tipologia Cavo\"}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:280,children:/*#__PURE__*/_jsxs(BarChart,{data:caviData,margin:{top:20,right:30,left:20,bottom:5},children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"tipologia_short\",angle:-45,textAnchor:\"end\",height:80}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{content:/*#__PURE__*/_jsx(CustomTooltip,{})}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Bar,{dataKey:\"metri_teorici\",fill:COLORS.primary,name:\"Metri Teorici\"}),/*#__PURE__*/_jsx(Bar,{dataKey:\"metri_reali\",fill:COLORS.success,name:\"Metri Reali\"}),/*#__PURE__*/_jsx(Bar,{dataKey:\"metri_da_posare\",fill:COLORS.warning,name:\"Metri da Posare\"})]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:350},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,align:\"center\",children:\"Bobine Disponibili per Tipologia\"}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:280,children:/*#__PURE__*/_jsxs(BarChart,{data:bobineData,margin:{top:20,right:30,left:20,bottom:5},children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"tipologia_short\",angle:-45,textAnchor:\"end\",height:80}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{content:/*#__PURE__*/_jsx(CustomTooltip,{})}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Bar,{dataKey:\"num_bobine\",fill:COLORS.info,name:\"Numero Bobine\"}),/*#__PURE__*/_jsx(Bar,{dataKey:\"metri_disponibili\",fill:COLORS.teal,name:\"Metri Disponibili\"})]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:350},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,align:\"center\",children:\"Analisi Deficit/Surplus Materiali\"}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:280,children:/*#__PURE__*/_jsxs(BarChart,{data:analisiData,margin:{top:20,right:30,left:20,bottom:5},children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"tipologia\",angle:-45,textAnchor:\"end\",height:80}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{content:/*#__PURE__*/_jsx(CustomTooltip,{})}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Bar,{dataKey:\"deficit\",fill:COLORS.error,name:\"Deficit (da acquistare)\"}),/*#__PURE__*/_jsx(Bar,{dataKey:\"surplus\",fill:COLORS.success,name:\"Surplus (disponibile)\"})]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Statistiche Riassuntive per Tipologia\"}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:2,children:caviData.map((cavo,index)=>{var _cavo$metri_teorici,_cavo$metri_reali,_cavo$metri_da_posare;return/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,lg:3,children:/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',p:2,border:'1px solid #e0e0e0',borderRadius:1,borderLeft:`4px solid ${cavo.color}`},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",gutterBottom:true,children:cavo.tipologia}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Sezione: \",/*#__PURE__*/_jsx(\"strong\",{children:cavo.sezione})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Cavi: \",/*#__PURE__*/_jsx(\"strong\",{children:cavo.num_cavi})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Teorici: \",/*#__PURE__*/_jsxs(\"strong\",{children:[(_cavo$metri_teorici=cavo.metri_teorici)===null||_cavo$metri_teorici===void 0?void 0:_cavo$metri_teorici.toFixed(0),\"m\"]})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Reali: \",/*#__PURE__*/_jsxs(\"strong\",{children:[(_cavo$metri_reali=cavo.metri_reali)===null||_cavo$metri_reali===void 0?void 0:_cavo$metri_reali.toFixed(0),\"m\"]})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Da Posare: \",/*#__PURE__*/_jsxs(\"strong\",{children:[(_cavo$metri_da_posare=cavo.metri_da_posare)===null||_cavo$metri_da_posare===void 0?void 0:_cavo$metri_da_posare.toFixed(0),\"m\"]})]}),cavo.deficit>0&&/*#__PURE__*/_jsx(Chip,{label:`Deficit: ${cavo.deficit.toFixed(0)}m`,color:\"error\",size:\"small\",sx:{mt:1}}),cavo.surplus>0&&/*#__PURE__*/_jsx(Chip,{label:`Surplus: ${cavo.surplus.toFixed(0)}m`,color:\"success\",size:\"small\",sx:{mt:1}})]})},index);})})]})}),bobineData.length>0&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Riepilogo Bobine Disponibili\"}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:2,children:bobineData.map((bobina,index)=>{var _bobina$metri_disponi;return/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,lg:3,children:/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',p:2,border:'1px solid #e0e0e0',borderRadius:1,borderLeft:`4px solid ${bobina.color}`},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",gutterBottom:true,children:bobina.tipologia}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Sezione: \",/*#__PURE__*/_jsx(\"strong\",{children:bobina.sezione})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Bobine: \",/*#__PURE__*/_jsx(\"strong\",{children:bobina.num_bobine})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Disponibili: \",/*#__PURE__*/_jsxs(\"strong\",{children:[(_bobina$metri_disponi=bobina.metri_disponibili)===null||_bobina$metri_disponi===void 0?void 0:_bobina$metri_disponi.toFixed(0),\"m\"]})]}),/*#__PURE__*/_jsx(Chip,{label:\"Disponibile\",color:\"success\",size:\"small\",sx:{mt:1}})]})},index);})})]})})]})]});};export default BoqChart;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "ComposedChart", "Line", "Line<PERSON>hart", "Box", "Typography", "Grid", "Paper", "Chip", "jsx", "_jsx", "jsxs", "_jsxs", "COLORS", "primary", "secondary", "success", "warning", "info", "error", "purple", "teal", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "_data$cavi_per_tipo", "_data$bobine_per_tipo", "data", "caviData", "cavi_per_tipo", "map", "cavo", "index", "_cavo$tipologia", "tipologia_short", "tipologia", "length", "substring", "color", "Object", "values", "deficit", "Math", "max", "metri_da_posare", "metri_reali", "surplus", "bobine<PERSON><PERSON>", "bobine_per_tipo", "bobina", "_bobina$tipologia", "totaliCavi", "reduce", "acc", "<PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "reali", "da_posare", "totaliData", "name", "value", "analisiData", "tipologia_full", "necessita_acquisto", "CustomTooltip", "_ref2", "active", "payload", "label", "sx", "p", "border", "children", "variant", "entry", "style", "toFixed", "renderCustomizedLabel", "_ref3", "cx", "cy", "midAngle", "innerRadius", "outerRadius", "percent", "RADIAN", "PI", "radius", "x", "cos", "y", "sin", "fill", "textAnchor", "dominantBaseline", "fontSize", "fontWeight", "mt", "gutterBottom", "container", "spacing", "item", "xs", "md", "height", "align", "width", "labelLine", "dataKey", "content", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angle", "_cavo$metri_teorici", "_cavo$metri_reali", "_cavo$metri_da_posare", "sm", "lg", "textAlign", "borderRadius", "borderLeft", "sezione", "num_cavi", "size", "_bobina$metri_disponi", "num_bobine", "metri_disponibili"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/charts/BoqChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON>Axis,\n  <PERSON><PERSON><PERSON><PERSON>,\n  CartesianGrid,\n  <PERSON>ltip,\n  Legend,\n  ResponsiveContainer,\n  <PERSON><PERSON>hart,\n  Pie,\n  Cell,\n  ComposedChart,\n  Line,\n  LineChart\n} from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\n\nconst COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f',\n  purple: '#9c27b0',\n  teal: '#00695c'\n};\n\nconst BoqChart = ({ data }) => {\n  if (!data) return null;\n\n  // Prepara dati per grafici cavi per tipologia\n  const caviData = data.cavi_per_tipo?.map((cavo, index) => ({\n    ...cavo,\n    tipologia_short: cavo.tipologia?.length > 8 ? cavo.tipologia.substring(0, 8) + '...' : cavo.tipologia,\n    color: Object.values(COLORS)[index % Object.values(COLORS).length],\n    deficit: Math.max(0, cavo.metri_da_posare - (cavo.metri_reali || 0)),\n    surplus: Math.max(0, (cavo.metri_reali || 0) - cavo.metri_da_posare)\n  })) || [];\n\n  // Prepara dati per grafici bobine disponibili\n  const bobineData = data.bobine_per_tipo?.map((bobina, index) => ({\n    ...bobina,\n    tipologia_short: bobina.tipologia?.length > 8 ? bobina.tipologia.substring(0, 8) + '...' : bobina.tipologia,\n    color: Object.values(COLORS)[index % Object.values(COLORS).length]\n  })) || [];\n\n  // Calcola totali per grafici a torta\n  const totaliCavi = caviData.reduce((acc, cavo) => {\n    acc.teorici += cavo.metri_teorici || 0;\n    acc.reali += cavo.metri_reali || 0;\n    acc.da_posare += cavo.metri_da_posare || 0;\n    return acc;\n  }, { teorici: 0, reali: 0, da_posare: 0 });\n\n  const totaliData = [\n    { name: 'Metri Teorici', value: totaliCavi.teorici, color: COLORS.primary },\n    { name: 'Metri Reali', value: totaliCavi.reali, color: COLORS.success },\n    { name: 'Metri da Posare', value: totaliCavi.da_posare, color: COLORS.warning }\n  ];\n\n  // Analisi deficit/surplus\n  const analisiData = caviData.map(cavo => ({\n    tipologia: cavo.tipologia_short,\n    tipologia_full: cavo.tipologia,\n    deficit: cavo.deficit,\n    surplus: cavo.surplus,\n    necessita_acquisto: cavo.deficit > 0\n  }));\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {\n    if (percent < 0.05) return null;\n    \n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n\n    return (\n      <text \n        x={x} \n        y={y} \n        fill=\"white\" \n        textAnchor={x > cx ? 'start' : 'end'} \n        dominantBaseline=\"central\"\n        fontSize=\"12\"\n        fontWeight=\"bold\"\n      >\n        {`${(percent * 100).toFixed(0)}%`}\n      </text>\n    );\n  };\n\n  return (\n    <Box sx={{ mt: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        Analisi Bill of Quantities\n      </Typography>\n      \n      <Grid container spacing={3}>\n        {/* Grafico a torta - Distribuzione Totali */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Distribuzione Metri Totali\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <PieChart>\n                <Pie\n                  data={totaliData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={renderCustomizedLabel}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                >\n                  {totaliData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n              </PieChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico a barre - Cavi per Tipologia */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Metri per Tipologia Cavo\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <BarChart data={caviData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"tipologia_short\" angle={-45} textAnchor=\"end\" height={80} />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"metri_teorici\" fill={COLORS.primary} name=\"Metri Teorici\" />\n                <Bar dataKey=\"metri_reali\" fill={COLORS.success} name=\"Metri Reali\" />\n                <Bar dataKey=\"metri_da_posare\" fill={COLORS.warning} name=\"Metri da Posare\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico a barre - Bobine Disponibili */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Bobine Disponibili per Tipologia\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <BarChart data={bobineData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"tipologia_short\" angle={-45} textAnchor=\"end\" height={80} />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"num_bobine\" fill={COLORS.info} name=\"Numero Bobine\" />\n                <Bar dataKey=\"metri_disponibili\" fill={COLORS.teal} name=\"Metri Disponibili\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Analisi Deficit/Surplus */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Analisi Deficit/Surplus Materiali\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <BarChart data={analisiData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"tipologia\" angle={-45} textAnchor=\"end\" height={80} />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"deficit\" fill={COLORS.error} name=\"Deficit (da acquistare)\" />\n                <Bar dataKey=\"surplus\" fill={COLORS.success} name=\"Surplus (disponibile)\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Statistiche Riassuntive */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Statistiche Riassuntive per Tipologia\n            </Typography>\n            <Grid container spacing={2}>\n              {caviData.map((cavo, index) => (\n                <Grid item xs={12} sm={6} md={4} lg={3} key={index}>\n                  <Box sx={{ \n                    textAlign: 'center', \n                    p: 2, \n                    border: '1px solid #e0e0e0', \n                    borderRadius: 1,\n                    borderLeft: `4px solid ${cavo.color}`\n                  }}>\n                    <Typography variant=\"subtitle2\" gutterBottom>\n                      {cavo.tipologia}\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Sezione: <strong>{cavo.sezione}</strong>\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Cavi: <strong>{cavo.num_cavi}</strong>\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Teorici: <strong>{cavo.metri_teorici?.toFixed(0)}m</strong>\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Reali: <strong>{cavo.metri_reali?.toFixed(0)}m</strong>\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Da Posare: <strong>{cavo.metri_da_posare?.toFixed(0)}m</strong>\n                    </Typography>\n                    {cavo.deficit > 0 && (\n                      <Chip \n                        label={`Deficit: ${cavo.deficit.toFixed(0)}m`}\n                        color=\"error\"\n                        size=\"small\"\n                        sx={{ mt: 1 }}\n                      />\n                    )}\n                    {cavo.surplus > 0 && (\n                      <Chip \n                        label={`Surplus: ${cavo.surplus.toFixed(0)}m`}\n                        color=\"success\"\n                        size=\"small\"\n                        sx={{ mt: 1 }}\n                      />\n                    )}\n                  </Box>\n                </Grid>\n              ))}\n            </Grid>\n          </Paper>\n        </Grid>\n\n        {/* Riepilogo Bobine Disponibili */}\n        {bobineData.length > 0 && (\n          <Grid item xs={12}>\n            <Paper sx={{ p: 2 }}>\n              <Typography variant=\"subtitle1\" gutterBottom>\n                Riepilogo Bobine Disponibili\n              </Typography>\n              <Grid container spacing={2}>\n                {bobineData.map((bobina, index) => (\n                  <Grid item xs={12} sm={6} md={4} lg={3} key={index}>\n                    <Box sx={{ \n                      textAlign: 'center', \n                      p: 2, \n                      border: '1px solid #e0e0e0', \n                      borderRadius: 1,\n                      borderLeft: `4px solid ${bobina.color}`\n                    }}>\n                      <Typography variant=\"subtitle2\" gutterBottom>\n                        {bobina.tipologia}\n                      </Typography>\n                      <Typography variant=\"body2\">\n                        Sezione: <strong>{bobina.sezione}</strong>\n                      </Typography>\n                      <Typography variant=\"body2\">\n                        Bobine: <strong>{bobina.num_bobine}</strong>\n                      </Typography>\n                      <Typography variant=\"body2\">\n                        Disponibili: <strong>{bobina.metri_disponibili?.toFixed(0)}m</strong>\n                      </Typography>\n                      <Chip \n                        label=\"Disponibile\"\n                        color=\"success\"\n                        size=\"small\"\n                        sx={{ mt: 1 }}\n                      />\n                    </Box>\n                  </Grid>\n                ))}\n              </Grid>\n            </Paper>\n          </Grid>\n        )}\n      </Grid>\n    </Box>\n  );\n};\n\nexport default BoqChart;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,QAAQ,CACRC,GAAG,CACHC,KAAK,CACLC,KAAK,CACLC,aAAa,CACbC,OAAO,CACPC,MAAM,CACNC,mBAAmB,CACnBC,QAAQ,CACRC,GAAG,CACHC,IAAI,CACJC,aAAa,CACbC,IAAI,CACJC,SAAS,KACJ,UAAU,CACjB,OAASC,GAAG,CAAEC,UAAU,CAAEC,IAAI,CAAEC,KAAK,CAAEC,IAAI,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,KAAM,CAAAC,MAAM,CAAG,CACbC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,SAAS,CACpBC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,MAAM,CAAE,SAAS,CACjBC,IAAI,CAAE,SACR,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGC,IAAA,EAAc,KAAAC,mBAAA,CAAAC,qBAAA,IAAb,CAAEC,IAAK,CAAC,CAAAH,IAAA,CACxB,GAAI,CAACG,IAAI,CAAE,MAAO,KAAI,CAEtB;AACA,KAAM,CAAAC,QAAQ,CAAG,EAAAH,mBAAA,CAAAE,IAAI,CAACE,aAAa,UAAAJ,mBAAA,iBAAlBA,mBAAA,CAAoBK,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,QAAAC,eAAA,OAAM,CACzD,GAAGF,IAAI,CACPG,eAAe,CAAE,EAAAD,eAAA,CAAAF,IAAI,CAACI,SAAS,UAAAF,eAAA,iBAAdA,eAAA,CAAgBG,MAAM,EAAG,CAAC,CAAGL,IAAI,CAACI,SAAS,CAACE,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAG,KAAK,CAAGN,IAAI,CAACI,SAAS,CACrGG,KAAK,CAAEC,MAAM,CAACC,MAAM,CAAC1B,MAAM,CAAC,CAACkB,KAAK,CAAGO,MAAM,CAACC,MAAM,CAAC1B,MAAM,CAAC,CAACsB,MAAM,CAAC,CAClEK,OAAO,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEZ,IAAI,CAACa,eAAe,EAAIb,IAAI,CAACc,WAAW,EAAI,CAAC,CAAC,CAAC,CACpEC,OAAO,CAAEJ,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE,CAACZ,IAAI,CAACc,WAAW,EAAI,CAAC,EAAId,IAAI,CAACa,eAAe,CACrE,CAAC,EAAC,CAAC,GAAI,EAAE,CAET;AACA,KAAM,CAAAG,UAAU,CAAG,EAAArB,qBAAA,CAAAC,IAAI,CAACqB,eAAe,UAAAtB,qBAAA,iBAApBA,qBAAA,CAAsBI,GAAG,CAAC,CAACmB,MAAM,CAAEjB,KAAK,QAAAkB,iBAAA,OAAM,CAC/D,GAAGD,MAAM,CACTf,eAAe,CAAE,EAAAgB,iBAAA,CAAAD,MAAM,CAACd,SAAS,UAAAe,iBAAA,iBAAhBA,iBAAA,CAAkBd,MAAM,EAAG,CAAC,CAAGa,MAAM,CAACd,SAAS,CAACE,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAG,KAAK,CAAGY,MAAM,CAACd,SAAS,CAC3GG,KAAK,CAAEC,MAAM,CAACC,MAAM,CAAC1B,MAAM,CAAC,CAACkB,KAAK,CAAGO,MAAM,CAACC,MAAM,CAAC1B,MAAM,CAAC,CAACsB,MAAM,CACnE,CAAC,EAAC,CAAC,GAAI,EAAE,CAET;AACA,KAAM,CAAAe,UAAU,CAAGvB,QAAQ,CAACwB,MAAM,CAAC,CAACC,GAAG,CAAEtB,IAAI,GAAK,CAChDsB,GAAG,CAACC,OAAO,EAAIvB,IAAI,CAACwB,aAAa,EAAI,CAAC,CACtCF,GAAG,CAACG,KAAK,EAAIzB,IAAI,CAACc,WAAW,EAAI,CAAC,CAClCQ,GAAG,CAACI,SAAS,EAAI1B,IAAI,CAACa,eAAe,EAAI,CAAC,CAC1C,MAAO,CAAAS,GAAG,CACZ,CAAC,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEE,KAAK,CAAE,CAAC,CAAEC,SAAS,CAAE,CAAE,CAAC,CAAC,CAE1C,KAAM,CAAAC,UAAU,CAAG,CACjB,CAAEC,IAAI,CAAE,eAAe,CAAEC,KAAK,CAAET,UAAU,CAACG,OAAO,CAAEhB,KAAK,CAAExB,MAAM,CAACC,OAAQ,CAAC,CAC3E,CAAE4C,IAAI,CAAE,aAAa,CAAEC,KAAK,CAAET,UAAU,CAACK,KAAK,CAAElB,KAAK,CAAExB,MAAM,CAACG,OAAQ,CAAC,CACvE,CAAE0C,IAAI,CAAE,iBAAiB,CAAEC,KAAK,CAAET,UAAU,CAACM,SAAS,CAAEnB,KAAK,CAAExB,MAAM,CAACI,OAAQ,CAAC,CAChF,CAED;AACA,KAAM,CAAA2C,WAAW,CAAGjC,QAAQ,CAACE,GAAG,CAACC,IAAI,GAAK,CACxCI,SAAS,CAAEJ,IAAI,CAACG,eAAe,CAC/B4B,cAAc,CAAE/B,IAAI,CAACI,SAAS,CAC9BM,OAAO,CAAEV,IAAI,CAACU,OAAO,CACrBK,OAAO,CAAEf,IAAI,CAACe,OAAO,CACrBiB,kBAAkB,CAAEhC,IAAI,CAACU,OAAO,CAAG,CACrC,CAAC,CAAC,CAAC,CAEH,KAAM,CAAAuB,aAAa,CAAGC,KAAA,EAAgC,IAA/B,CAAEC,MAAM,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAAH,KAAA,CAC/C,GAAIC,MAAM,EAAIC,OAAO,EAAIA,OAAO,CAAC/B,MAAM,CAAE,CACvC,mBACEvB,KAAA,CAACL,KAAK,EAAC6D,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,gBAAiB,CAAE,CAAAC,QAAA,eAC5C7D,IAAA,CAACL,UAAU,EAACmE,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAE,GAAGJ,KAAK,EAAE,CAAa,CAAC,CACpDD,OAAO,CAACrC,GAAG,CAAC,CAAC4C,KAAK,CAAE1C,KAAK,gBACxBrB,IAAA,CAACL,UAAU,EAAamE,OAAO,CAAC,OAAO,CAACE,KAAK,CAAE,CAAErC,KAAK,CAAEoC,KAAK,CAACpC,KAAM,CAAE,CAAAkC,QAAA,CACnE,GAAGE,KAAK,CAACf,IAAI,KAAK,MAAO,CAAAe,KAAK,CAACd,KAAK,GAAK,QAAQ,CAAGc,KAAK,CAACd,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC,CAAGF,KAAK,CAACd,KAAK,EAAE,EAD5E5B,KAEL,CACb,CAAC,EACG,CAAC,CAEZ,CACA,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAA6C,qBAAqB,CAAGC,KAAA,EAA6D,IAA5D,CAAEC,EAAE,CAAEC,EAAE,CAAEC,QAAQ,CAAEC,WAAW,CAAEC,WAAW,CAAEC,OAAQ,CAAC,CAAAN,KAAA,CACpF,GAAIM,OAAO,CAAG,IAAI,CAAE,MAAO,KAAI,CAE/B,KAAM,CAAAC,MAAM,CAAG3C,IAAI,CAAC4C,EAAE,CAAG,GAAG,CAC5B,KAAM,CAAAC,MAAM,CAAGL,WAAW,CAAG,CAACC,WAAW,CAAGD,WAAW,EAAI,GAAG,CAC9D,KAAM,CAAAM,CAAC,CAAGT,EAAE,CAAGQ,MAAM,CAAG7C,IAAI,CAAC+C,GAAG,CAAC,CAACR,QAAQ,CAAGI,MAAM,CAAC,CACpD,KAAM,CAAAK,CAAC,CAAGV,EAAE,CAAGO,MAAM,CAAG7C,IAAI,CAACiD,GAAG,CAAC,CAACV,QAAQ,CAAGI,MAAM,CAAC,CAEpD,mBACE1E,IAAA,SACE6E,CAAC,CAAEA,CAAE,CACLE,CAAC,CAAEA,CAAE,CACLE,IAAI,CAAC,OAAO,CACZC,UAAU,CAAEL,CAAC,CAAGT,EAAE,CAAG,OAAO,CAAG,KAAM,CACrCe,gBAAgB,CAAC,SAAS,CAC1BC,QAAQ,CAAC,IAAI,CACbC,UAAU,CAAC,MAAM,CAAAxB,QAAA,CAEhB,GAAG,CAACY,OAAO,CAAG,GAAG,EAAER,OAAO,CAAC,CAAC,CAAC,GAAG,CAC7B,CAAC,CAEX,CAAC,CAED,mBACE/D,KAAA,CAACR,GAAG,EAACgE,EAAE,CAAE,CAAE4B,EAAE,CAAE,CAAE,CAAE,CAAAzB,QAAA,eACjB7D,IAAA,CAACL,UAAU,EAACmE,OAAO,CAAC,IAAI,CAACyB,YAAY,MAAA1B,QAAA,CAAC,4BAEtC,CAAY,CAAC,cAEb3D,KAAA,CAACN,IAAI,EAAC4F,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA5B,QAAA,eAEzB7D,IAAA,CAACJ,IAAI,EAAC8F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA/B,QAAA,cACvB3D,KAAA,CAACL,KAAK,EAAC6D,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEkC,MAAM,CAAE,GAAI,CAAE,CAAAhC,QAAA,eAC/B7D,IAAA,CAACL,UAAU,EAACmE,OAAO,CAAC,WAAW,CAACyB,YAAY,MAACO,KAAK,CAAC,QAAQ,CAAAjC,QAAA,CAAC,4BAE5D,CAAY,CAAC,cACb7D,IAAA,CAACb,mBAAmB,EAAC4G,KAAK,CAAC,MAAM,CAACF,MAAM,CAAE,GAAI,CAAAhC,QAAA,cAC5C3D,KAAA,CAACd,QAAQ,EAAAyE,QAAA,eACP7D,IAAA,CAACX,GAAG,EACF2B,IAAI,CAAE+B,UAAW,CACjBqB,EAAE,CAAC,KAAK,CACRC,EAAE,CAAC,KAAK,CACR2B,SAAS,CAAE,KAAM,CACjBvC,KAAK,CAAES,qBAAsB,CAC7BM,WAAW,CAAE,EAAG,CAChBS,IAAI,CAAC,SAAS,CACdgB,OAAO,CAAC,OAAO,CAAApC,QAAA,CAEdd,UAAU,CAAC5B,GAAG,CAAC,CAAC4C,KAAK,CAAE1C,KAAK,gBAC3BrB,IAAA,CAACV,IAAI,EAAuB2F,IAAI,CAAElB,KAAK,CAACpC,KAAM,EAAnC,QAAQN,KAAK,EAAwB,CACjD,CAAC,CACC,CAAC,cACNrB,IAAA,CAACf,OAAO,EAACiH,OAAO,cAAElG,IAAA,CAACqD,aAAa,GAAE,CAAE,CAAE,CAAC,cACvCrD,IAAA,CAACd,MAAM,GAAE,CAAC,EACF,CAAC,CACQ,CAAC,EACjB,CAAC,CACJ,CAAC,cAGPc,IAAA,CAACJ,IAAI,EAAC8F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA/B,QAAA,cACvB3D,KAAA,CAACL,KAAK,EAAC6D,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEkC,MAAM,CAAE,GAAI,CAAE,CAAAhC,QAAA,eAC/B7D,IAAA,CAACL,UAAU,EAACmE,OAAO,CAAC,WAAW,CAACyB,YAAY,MAACO,KAAK,CAAC,QAAQ,CAAAjC,QAAA,CAAC,0BAE5D,CAAY,CAAC,cACb7D,IAAA,CAACb,mBAAmB,EAAC4G,KAAK,CAAC,MAAM,CAACF,MAAM,CAAE,GAAI,CAAAhC,QAAA,cAC5C3D,KAAA,CAACtB,QAAQ,EAACoC,IAAI,CAAEC,QAAS,CAACkF,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,MAAM,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAC5E7D,IAAA,CAAChB,aAAa,EAACwH,eAAe,CAAC,KAAK,CAAE,CAAC,cACvCxG,IAAA,CAAClB,KAAK,EAACmH,OAAO,CAAC,iBAAiB,CAACQ,KAAK,CAAE,CAAC,EAAG,CAACvB,UAAU,CAAC,KAAK,CAACW,MAAM,CAAE,EAAG,CAAE,CAAC,cAC5E7F,IAAA,CAACjB,KAAK,GAAE,CAAC,cACTiB,IAAA,CAACf,OAAO,EAACiH,OAAO,cAAElG,IAAA,CAACqD,aAAa,GAAE,CAAE,CAAE,CAAC,cACvCrD,IAAA,CAACd,MAAM,GAAE,CAAC,cACVc,IAAA,CAACnB,GAAG,EAACoH,OAAO,CAAC,eAAe,CAAChB,IAAI,CAAE9E,MAAM,CAACC,OAAQ,CAAC4C,IAAI,CAAC,eAAe,CAAE,CAAC,cAC1EhD,IAAA,CAACnB,GAAG,EAACoH,OAAO,CAAC,aAAa,CAAChB,IAAI,CAAE9E,MAAM,CAACG,OAAQ,CAAC0C,IAAI,CAAC,aAAa,CAAE,CAAC,cACtEhD,IAAA,CAACnB,GAAG,EAACoH,OAAO,CAAC,iBAAiB,CAAChB,IAAI,CAAE9E,MAAM,CAACI,OAAQ,CAACyC,IAAI,CAAC,iBAAiB,CAAE,CAAC,EACtE,CAAC,CACQ,CAAC,EACjB,CAAC,CACJ,CAAC,cAGPhD,IAAA,CAACJ,IAAI,EAAC8F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA/B,QAAA,cACvB3D,KAAA,CAACL,KAAK,EAAC6D,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEkC,MAAM,CAAE,GAAI,CAAE,CAAAhC,QAAA,eAC/B7D,IAAA,CAACL,UAAU,EAACmE,OAAO,CAAC,WAAW,CAACyB,YAAY,MAACO,KAAK,CAAC,QAAQ,CAAAjC,QAAA,CAAC,kCAE5D,CAAY,CAAC,cACb7D,IAAA,CAACb,mBAAmB,EAAC4G,KAAK,CAAC,MAAM,CAACF,MAAM,CAAE,GAAI,CAAAhC,QAAA,cAC5C3D,KAAA,CAACtB,QAAQ,EAACoC,IAAI,CAAEoB,UAAW,CAAC+D,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,MAAM,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAC9E7D,IAAA,CAAChB,aAAa,EAACwH,eAAe,CAAC,KAAK,CAAE,CAAC,cACvCxG,IAAA,CAAClB,KAAK,EAACmH,OAAO,CAAC,iBAAiB,CAACQ,KAAK,CAAE,CAAC,EAAG,CAACvB,UAAU,CAAC,KAAK,CAACW,MAAM,CAAE,EAAG,CAAE,CAAC,cAC5E7F,IAAA,CAACjB,KAAK,GAAE,CAAC,cACTiB,IAAA,CAACf,OAAO,EAACiH,OAAO,cAAElG,IAAA,CAACqD,aAAa,GAAE,CAAE,CAAE,CAAC,cACvCrD,IAAA,CAACd,MAAM,GAAE,CAAC,cACVc,IAAA,CAACnB,GAAG,EAACoH,OAAO,CAAC,YAAY,CAAChB,IAAI,CAAE9E,MAAM,CAACK,IAAK,CAACwC,IAAI,CAAC,eAAe,CAAE,CAAC,cACpEhD,IAAA,CAACnB,GAAG,EAACoH,OAAO,CAAC,mBAAmB,CAAChB,IAAI,CAAE9E,MAAM,CAACQ,IAAK,CAACqC,IAAI,CAAC,mBAAmB,CAAE,CAAC,EACvE,CAAC,CACQ,CAAC,EACjB,CAAC,CACJ,CAAC,cAGPhD,IAAA,CAACJ,IAAI,EAAC8F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA/B,QAAA,cACvB3D,KAAA,CAACL,KAAK,EAAC6D,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEkC,MAAM,CAAE,GAAI,CAAE,CAAAhC,QAAA,eAC/B7D,IAAA,CAACL,UAAU,EAACmE,OAAO,CAAC,WAAW,CAACyB,YAAY,MAACO,KAAK,CAAC,QAAQ,CAAAjC,QAAA,CAAC,mCAE5D,CAAY,CAAC,cACb7D,IAAA,CAACb,mBAAmB,EAAC4G,KAAK,CAAC,MAAM,CAACF,MAAM,CAAE,GAAI,CAAAhC,QAAA,cAC5C3D,KAAA,CAACtB,QAAQ,EAACoC,IAAI,CAAEkC,WAAY,CAACiD,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,MAAM,CAAE,CAAE,CAAE,CAAA1C,QAAA,eAC/E7D,IAAA,CAAChB,aAAa,EAACwH,eAAe,CAAC,KAAK,CAAE,CAAC,cACvCxG,IAAA,CAAClB,KAAK,EAACmH,OAAO,CAAC,WAAW,CAACQ,KAAK,CAAE,CAAC,EAAG,CAACvB,UAAU,CAAC,KAAK,CAACW,MAAM,CAAE,EAAG,CAAE,CAAC,cACtE7F,IAAA,CAACjB,KAAK,GAAE,CAAC,cACTiB,IAAA,CAACf,OAAO,EAACiH,OAAO,cAAElG,IAAA,CAACqD,aAAa,GAAE,CAAE,CAAE,CAAC,cACvCrD,IAAA,CAACd,MAAM,GAAE,CAAC,cACVc,IAAA,CAACnB,GAAG,EAACoH,OAAO,CAAC,SAAS,CAAChB,IAAI,CAAE9E,MAAM,CAACM,KAAM,CAACuC,IAAI,CAAC,yBAAyB,CAAE,CAAC,cAC5EhD,IAAA,CAACnB,GAAG,EAACoH,OAAO,CAAC,SAAS,CAAChB,IAAI,CAAE9E,MAAM,CAACG,OAAQ,CAAC0C,IAAI,CAAC,uBAAuB,CAAE,CAAC,EACpE,CAAC,CACQ,CAAC,EACjB,CAAC,CACJ,CAAC,cAGPhD,IAAA,CAACJ,IAAI,EAAC8F,IAAI,MAACC,EAAE,CAAE,EAAG,CAAA9B,QAAA,cAChB3D,KAAA,CAACL,KAAK,EAAC6D,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAE,QAAA,eAClB7D,IAAA,CAACL,UAAU,EAACmE,OAAO,CAAC,WAAW,CAACyB,YAAY,MAAA1B,QAAA,CAAC,uCAE7C,CAAY,CAAC,cACb7D,IAAA,CAACJ,IAAI,EAAC4F,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA5B,QAAA,CACxB5C,QAAQ,CAACE,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,QAAAqF,mBAAA,CAAAC,iBAAA,CAAAC,qBAAA,oBACxB5G,IAAA,CAACJ,IAAI,EAAC8F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACkB,EAAE,CAAE,CAAE,CAACjB,EAAE,CAAE,CAAE,CAACkB,EAAE,CAAE,CAAE,CAAAjD,QAAA,cACrC3D,KAAA,CAACR,GAAG,EAACgE,EAAE,CAAE,CACPqD,SAAS,CAAE,QAAQ,CACnBpD,CAAC,CAAE,CAAC,CACJC,MAAM,CAAE,mBAAmB,CAC3BoD,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,aAAa7F,IAAI,CAACO,KAAK,EACrC,CAAE,CAAAkC,QAAA,eACA7D,IAAA,CAACL,UAAU,EAACmE,OAAO,CAAC,WAAW,CAACyB,YAAY,MAAA1B,QAAA,CACzCzC,IAAI,CAACI,SAAS,CACL,CAAC,cACbtB,KAAA,CAACP,UAAU,EAACmE,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,WACjB,cAAA7D,IAAA,WAAA6D,QAAA,CAASzC,IAAI,CAAC8F,OAAO,CAAS,CAAC,EAC9B,CAAC,cACbhH,KAAA,CAACP,UAAU,EAACmE,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,QACpB,cAAA7D,IAAA,WAAA6D,QAAA,CAASzC,IAAI,CAAC+F,QAAQ,CAAS,CAAC,EAC5B,CAAC,cACbjH,KAAA,CAACP,UAAU,EAACmE,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,WACjB,cAAA3D,KAAA,WAAA2D,QAAA,GAAA6C,mBAAA,CAAStF,IAAI,CAACwB,aAAa,UAAA8D,mBAAA,iBAAlBA,mBAAA,CAAoBzC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAQ,CAAC,EACjD,CAAC,cACb/D,KAAA,CAACP,UAAU,EAACmE,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,SACnB,cAAA3D,KAAA,WAAA2D,QAAA,GAAA8C,iBAAA,CAASvF,IAAI,CAACc,WAAW,UAAAyE,iBAAA,iBAAhBA,iBAAA,CAAkB1C,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAQ,CAAC,EAC7C,CAAC,cACb/D,KAAA,CAACP,UAAU,EAACmE,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,aACf,cAAA3D,KAAA,WAAA2D,QAAA,GAAA+C,qBAAA,CAASxF,IAAI,CAACa,eAAe,UAAA2E,qBAAA,iBAApBA,qBAAA,CAAsB3C,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAQ,CAAC,EACrD,CAAC,CACZ7C,IAAI,CAACU,OAAO,CAAG,CAAC,eACf9B,IAAA,CAACF,IAAI,EACH2D,KAAK,CAAE,YAAYrC,IAAI,CAACU,OAAO,CAACmC,OAAO,CAAC,CAAC,CAAC,GAAI,CAC9CtC,KAAK,CAAC,OAAO,CACbyF,IAAI,CAAC,OAAO,CACZ1D,EAAE,CAAE,CAAE4B,EAAE,CAAE,CAAE,CAAE,CACf,CACF,CACAlE,IAAI,CAACe,OAAO,CAAG,CAAC,eACfnC,IAAA,CAACF,IAAI,EACH2D,KAAK,CAAE,YAAYrC,IAAI,CAACe,OAAO,CAAC8B,OAAO,CAAC,CAAC,CAAC,GAAI,CAC9CtC,KAAK,CAAC,SAAS,CACfyF,IAAI,CAAC,OAAO,CACZ1D,EAAE,CAAE,CAAE4B,EAAE,CAAE,CAAE,CAAE,CACf,CACF,EACE,CAAC,EA1CqCjE,KA2CvC,CAAC,EACR,CAAC,CACE,CAAC,EACF,CAAC,CACJ,CAAC,CAGNe,UAAU,CAACX,MAAM,CAAG,CAAC,eACpBzB,IAAA,CAACJ,IAAI,EAAC8F,IAAI,MAACC,EAAE,CAAE,EAAG,CAAA9B,QAAA,cAChB3D,KAAA,CAACL,KAAK,EAAC6D,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAE,QAAA,eAClB7D,IAAA,CAACL,UAAU,EAACmE,OAAO,CAAC,WAAW,CAACyB,YAAY,MAAA1B,QAAA,CAAC,8BAE7C,CAAY,CAAC,cACb7D,IAAA,CAACJ,IAAI,EAAC4F,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA5B,QAAA,CACxBzB,UAAU,CAACjB,GAAG,CAAC,CAACmB,MAAM,CAAEjB,KAAK,QAAAgG,qBAAA,oBAC5BrH,IAAA,CAACJ,IAAI,EAAC8F,IAAI,MAACC,EAAE,CAAE,EAAG,CAACkB,EAAE,CAAE,CAAE,CAACjB,EAAE,CAAE,CAAE,CAACkB,EAAE,CAAE,CAAE,CAAAjD,QAAA,cACrC3D,KAAA,CAACR,GAAG,EAACgE,EAAE,CAAE,CACPqD,SAAS,CAAE,QAAQ,CACnBpD,CAAC,CAAE,CAAC,CACJC,MAAM,CAAE,mBAAmB,CAC3BoD,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,aAAa3E,MAAM,CAACX,KAAK,EACvC,CAAE,CAAAkC,QAAA,eACA7D,IAAA,CAACL,UAAU,EAACmE,OAAO,CAAC,WAAW,CAACyB,YAAY,MAAA1B,QAAA,CACzCvB,MAAM,CAACd,SAAS,CACP,CAAC,cACbtB,KAAA,CAACP,UAAU,EAACmE,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,WACjB,cAAA7D,IAAA,WAAA6D,QAAA,CAASvB,MAAM,CAAC4E,OAAO,CAAS,CAAC,EAChC,CAAC,cACbhH,KAAA,CAACP,UAAU,EAACmE,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,UAClB,cAAA7D,IAAA,WAAA6D,QAAA,CAASvB,MAAM,CAACgF,UAAU,CAAS,CAAC,EAClC,CAAC,cACbpH,KAAA,CAACP,UAAU,EAACmE,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,eACb,cAAA3D,KAAA,WAAA2D,QAAA,GAAAwD,qBAAA,CAAS/E,MAAM,CAACiF,iBAAiB,UAAAF,qBAAA,iBAAxBA,qBAAA,CAA0BpD,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAQ,CAAC,EAC3D,CAAC,cACbjE,IAAA,CAACF,IAAI,EACH2D,KAAK,CAAC,aAAa,CACnB9B,KAAK,CAAC,SAAS,CACfyF,IAAI,CAAC,OAAO,CACZ1D,EAAE,CAAE,CAAE4B,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,EACC,CAAC,EA1BqCjE,KA2BvC,CAAC,EACR,CAAC,CACE,CAAC,EACF,CAAC,CACJ,CACP,EACG,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAT,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}