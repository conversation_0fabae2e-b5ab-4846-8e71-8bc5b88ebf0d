{"ast": null, "code": "import { formatDistance } from \"./uz-Cyrl/_lib/formatDistance.js\";\nimport { formatLong } from \"./uz-Cyrl/_lib/formatLong.js\";\nimport { formatRelative } from \"./uz-Cyrl/_lib/formatRelative.js\";\nimport { localize } from \"./uz-Cyrl/_lib/localize.js\";\nimport { match } from \"./uz-Cyrl/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Uzbek Cyrillic locale.\n * @language Uzbek\n * @iso-639-2 uzb\n * <AUTHOR> [@kamronbek28](https://github.com/kamronbek28)\n */\nexport const uzCyrl = {\n  code: \"uz-Cyrl\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default uzCyrl;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "uzCyrl", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/uz-Cyrl.js"], "sourcesContent": ["import { formatDistance } from \"./uz-Cyrl/_lib/formatDistance.js\";\nimport { formatLong } from \"./uz-Cyrl/_lib/formatLong.js\";\nimport { formatRelative } from \"./uz-Cyrl/_lib/formatRelative.js\";\nimport { localize } from \"./uz-Cyrl/_lib/localize.js\";\nimport { match } from \"./uz-Cyrl/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Uzbek Cyrillic locale.\n * @language Uzbek\n * @iso-639-2 uzb\n * <AUTHOR> [@kamronbek28](https://github.com/kamronbek28)\n */\nexport const uzCyrl = {\n  code: \"uz-Cyrl\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default uzCyrl;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kCAAkC;AACjE,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,KAAK,QAAQ,yBAAyB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,MAAM,GAAG;EACpBC,IAAI,EAAE,SAAS;EACfN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}