{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\MainMenu.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { List, ListItem, ListItemIcon, ListItemText, Divider, Box, Typography, Collapse, ListItemButton } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon, ExpandLess, ExpandMore, ViewList as ViewListIcon, Engineering as EngineeringIcon, Inventory as InventoryIcon, TableChart as TableChartIcon, Assessment as AssessmentIcon, VerifiedUser as VerifiedUserIcon, ShoppingCart as ShoppingCartIcon, Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MainMenu = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n\n  // Funzione di utilità per creare ListItemText con dimensione del testo più ampia (stile PyCharm)\n  const createListItemText = (primary, level = 1) => {\n    // Dimensioni del testo in base al livello del menu - più grandi per uno stile simile a PyCharm\n    const fontSize = level === 1 ? '0.9rem' : level === 2 ? '0.85rem' : '0.8rem';\n    return /*#__PURE__*/_jsxDEV(ListItemText, {\n      primary: primary,\n      primaryTypographyProps: {\n        fontSize,\n        fontWeight: level === 1 ? 500 : 400\n      },\n      sx: {\n        my: 0.3,\n        py: 0.3\n      } // Aumenta leggermente il margine e padding verticale\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Stati per i menu a cascata - per utenti cantiere, il menu cavi è aperto di default\n  const [openCaviMenu, setOpenCaviMenu] = useState((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user');\n  const [openCantieriMenu, setOpenCantieriMenu] = useState(false);\n  const [openAdminMenu, setOpenAdminMenu] = useState(false);\n  const [openPosaMenu, setOpenPosaMenu] = useState(false);\n  const [openParcoMenu, setOpenParcoMenu] = useState(false);\n  const [openExcelMenu, setOpenExcelMenu] = useState(false);\n  const [openReportMenu, setOpenReportMenu] = useState(false);\n  const [openCertificazioneMenu, setOpenCertificazioneMenu] = useState(false);\n  const [openComandeMenu, setOpenComandeMenu] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = path => {\n    return location.pathname.startsWith(path);\n  };\n\n  // Gestisce l'apertura/chiusura dei menu a cascata\n  const handleToggleCaviMenu = () => {\n    setOpenCaviMenu(!openCaviMenu);\n  };\n  const handleToggleCantieriMenu = () => {\n    setOpenCantieriMenu(!openCantieriMenu);\n  };\n  const handleToggleAdminMenu = () => {\n    setOpenAdminMenu(!openAdminMenu);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      console.log('Utente impersonato, reindirizzamento al menu amministratore');\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(List, {\n    sx: {\n      width: '240px',\n      // Menu più ampio, simile a PyCharm\n      '& .MuiListItemButton-root': {\n        py: 0.5,\n        minHeight: '36px',\n        // Altezza maggiore per ogni voce di menu\n        '&:hover': {\n          backgroundColor: '#e9ecef'\n        }\n      },\n      '& .MuiListItemIcon-root': {\n        minWidth: '36px',\n        // Icone più distanziate\n        color: '#6c757d' // Colore delle icone più scuro\n      },\n      '& .Mui-selected': {\n        backgroundColor: '#e3f2fd',\n        // Colore di sfondo per l'elemento selezionato (stile PyCharm)\n        '&:hover': {\n          backgroundColor: '#bbdefb'\n        }\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n      selected: isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard'),\n      onClick: () => navigateTo('/dashboard'),\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(HomeIcon, {\n          fontSize: \"medium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), createListItemText(isImpersonating ? \"Torna al Menu Admin\" : \"Home\", 1)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), (user === null || user === void 0 ? void 0 : user.role) === 'owner' && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: handleToggleAdminMenu,\n        selected: isPartOfActive('/dashboard/admin'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(AdminIcon, {\n            fontSize: \"medium\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this), createListItemText(\"Amministrazione\", 1), openAdminMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 30\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 64\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n        in: openAdminMenu,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            selected: isActive('/dashboard/admin'),\n            onClick: () => navigateTo('/dashboard/admin'),\n            children: createListItemText(\"Pannello Admin\", 2)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), ((user === null || user === void 0 ? void 0 : user.role) !== 'owner' || (user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this), isImpersonating && impersonatedUser && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 1,\n          bgcolor: 'rgba(255, 165, 0, 0.1)',\n          borderLeft: '4px solid orange'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"textSecondary\",\n          children: \"Accesso come utente:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          fontWeight: \"bold\",\n          children: impersonatedUser.username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 13\n      }, this), ((user === null || user === void 0 ? void 0 : user.role) === 'user' || isImpersonating) && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n          onClick: handleToggleCantieriMenu,\n          selected: isPartOfActive('/dashboard/cantieri'),\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(ConstructionIcon, {\n              fontSize: \"medium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 17\n          }, this), createListItemText(isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\", 1), openCantieriMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 37\n          }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 71\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n          in: openCantieriMenu,\n          timeout: \"auto\",\n          unmountOnExit: true,\n          children: /*#__PURE__*/_jsxDEV(List, {\n            component: \"div\",\n            disablePadding: true,\n            children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n              sx: {\n                pl: 2\n              },\n              selected: isActive('/dashboard/cantieri'),\n              onClick: () => navigateTo('/dashboard/cantieri'),\n              children: createListItemText(\"Lista Cantieri\", 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 19\n            }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 2\n                },\n                selected: isActive(`/dashboard/cantieri/${selectedCantiereId}`),\n                onClick: () => navigateTo(`/dashboard/cantieri/${selectedCantiereId}`),\n                children: createListItemText(`Cantiere: ${selectedCantiereName || selectedCantiereId}`, 2)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 23\n              }, this)\n            }, void 0, false)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true), selectedCantiereId && /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: handleToggleCaviMenu,\n        selected: isPartOfActive('/dashboard/cavi'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(CableIcon, {\n            fontSize: \"medium\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 15\n        }, this), createListItemText(`Gestione Cavi (${selectedCantiereName || selectedCantiereId})`, 1), openCaviMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 65\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 13\n      }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(Collapse, {\n        in: openCaviMenu,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: [(user === null || user === void 0 ? void 0 : user.role) !== 'cantieri_user' && /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            selected: isActive('/dashboard/cavi/visualizza'),\n            onClick: () => navigateTo('/dashboard/cavi/visualizza'),\n            children: createListItemText(\"Visualizza Cavi\", 2)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            onClick: () => setOpenPosaMenu(!openPosaMenu),\n            selected: isPartOfActive('/dashboard/cavi/posa'),\n            children: [createListItemText(\"Posa e Collegamenti\", 2), openPosaMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 35\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 69\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openPosaMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/posa/inserisci-metri'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/inserisci-metri'),\n                children: createListItemText(\"Inserisci metri posati\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/posa/modifica-cavo'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/modifica-cavo'),\n                children: createListItemText(\"Modifica cavo\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/posa/aggiungi-cavo'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/aggiungi-cavo'),\n                children: createListItemText(\"Aggiungi nuovo cavo\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/posa/elimina-cavo'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/elimina-cavo'),\n                children: createListItemText(\"Elimina cavo\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/posa/modifica-bobina'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/modifica-bobina'),\n                children: createListItemText(\"Modifica bobina cavo posato\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/posa/collegamenti'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/collegamenti'),\n                children: createListItemText(\"Gestisci collegamenti cavo\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            onClick: () => setOpenParcoMenu(!openParcoMenu),\n            selected: isPartOfActive('/dashboard/cavi/parco'),\n            children: [createListItemText(\"Parco Cavi\", 2), openParcoMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 70\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openParcoMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/parco/visualizza'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/visualizza'),\n                children: createListItemText(\"Visualizza Bobine\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/parco/crea'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/crea'),\n                children: createListItemText(\"Crea Nuova Bobina\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/parco/modifica'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/modifica'),\n                children: createListItemText(\"Modifica Bobina\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/parco/elimina'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/elimina'),\n                children: createListItemText(\"Elimina Bobina\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/parco/storico'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/storico'),\n                children: createListItemText(\"Storico Utilizzo\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            onClick: () => setOpenExcelMenu(!openExcelMenu),\n            selected: isPartOfActive('/dashboard/cavi/excel'),\n            children: [createListItemText(\"Gestione Excel\", 2), openExcelMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 70\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openExcelMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/excel/importa-cavi'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/importa-cavi'),\n                children: createListItemText(\"Importa cavi da Excel\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/excel/importa-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/importa-bobine'),\n                children: createListItemText(\"Importa parco bobine\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/excel/template-cavi'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/template-cavi'),\n                children: createListItemText(\"Template Excel per cavi\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/excel/template-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/template-bobine'),\n                children: createListItemText(\"Template Excel per bobine\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/excel/esporta-cavi'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/esporta-cavi'),\n                children: createListItemText(\"Esporta cavi in Excel\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/excel/esporta-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/esporta-bobine'),\n                children: createListItemText(\"Esporta bobine in Excel\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            onClick: () => setOpenReportMenu(!openReportMenu),\n            selected: isPartOfActive('/dashboard/cavi/report'),\n            children: [createListItemText(\"Report\", 2), openReportMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 37\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 71\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openReportMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/report/avanzamento'),\n                onClick: () => navigateTo('/dashboard/cavi/report/avanzamento'),\n                children: createListItemText(\"Report Avanzamento\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/report/boq'),\n                onClick: () => navigateTo('/dashboard/cavi/report/boq'),\n                children: createListItemText(\"Bill of Quantities\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/report/utilizzo-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/report/utilizzo-bobine'),\n                children: createListItemText(\"Report Utilizzo Bobine\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/report/posa-periodo'),\n                onClick: () => navigateTo('/dashboard/cavi/report/posa-periodo'),\n                children: createListItemText(\"Report Posa per Periodo\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            onClick: () => setOpenCertificazioneMenu(!openCertificazioneMenu),\n            selected: isPartOfActive('/dashboard/cavi/certificazione'),\n            children: [createListItemText(\"Certificazione Cavi\", 2), openCertificazioneMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 45\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 79\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openCertificazioneMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/certificazione/visualizza'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/visualizza'),\n                children: createListItemText(\"Visualizza certificazioni\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/certificazione/filtra'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/filtra'),\n                children: createListItemText(\"Filtra per cavo\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/certificazione/crea'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/crea'),\n                children: createListItemText(\"Crea certificazione\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/certificazione/dettagli'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/dettagli'),\n                children: createListItemText(\"Dettagli certificazione\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/certificazione/pdf'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/pdf'),\n                children: createListItemText(\"Genera PDF\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/certificazione/elimina'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/elimina'),\n                children: createListItemText(\"Elimina certificazione\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/certificazione/strumenti'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/strumenti'),\n                children: createListItemText(\"Gestione strumenti\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            onClick: () => setOpenComandeMenu(!openComandeMenu),\n            selected: isPartOfActive('/dashboard/cavi/comande'),\n            children: [createListItemText(\"Gestione Comande\", 2), openComandeMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 72\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openComandeMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/comande/visualizza'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/visualizza'),\n                children: createListItemText(\"Visualizza comande\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/comande/crea'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/crea'),\n                children: createListItemText(\"Crea nuova comanda\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/comande/modifica'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/modifica'),\n                children: createListItemText(\"Modifica comanda\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/comande/elimina'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/elimina'),\n                children: createListItemText(\"Elimina comanda\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/comande/stampa'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/stampa'),\n                children: createListItemText(\"Stampa comanda\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/comande/assegna'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/assegna'),\n                children: createListItemText(\"Assegna comanda a cavo\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n};\n_s(MainMenu, \"yENlsUR6j4JhgNx/BsM06dt4tLE=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = MainMenu;\nexport default MainMenu;\nvar _c;\n$RefreshReg$(_c, \"MainMenu\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "Box", "Typography", "Collapse", "ListItemButton", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "ExpandLess", "ExpandMore", "ViewList", "ViewListIcon", "Engineering", "EngineeringIcon", "Inventory", "InventoryIcon", "Table<PERSON>hart", "TableChartIcon", "Assessment", "AssessmentIcon", "VerifiedUser", "VerifiedUserIcon", "ShoppingCart", "ShoppingCartIcon", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MainMenu", "_s", "navigate", "location", "user", "isImpersonating", "impersonated<PERSON><PERSON>", "createListItemText", "primary", "level", "fontSize", "primaryTypographyProps", "fontWeight", "sx", "my", "py", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "openCaviMenu", "setOpenCaviMenu", "role", "openCantieriMenu", "setOpenCantieriMenu", "openAdminMenu", "setOpenAdminMenu", "openPosaMenu", "setOpenPosaMenu", "openParcoMenu", "setOpenParcoMenu", "openExcelMenu", "setOpenExcelMenu", "openReportMenu", "setOpenReportMenu", "openCertificazioneMenu", "setOpenCertificazioneMenu", "openComandeMenu", "setOpenComandeMenu", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "isActive", "path", "pathname", "isPartOfActive", "startsWith", "handleToggleCaviMenu", "handleToggleCantieriMenu", "handleToggleAdminMenu", "navigateTo", "console", "log", "width", "minHeight", "backgroundColor", "min<PERSON><PERSON><PERSON>", "color", "children", "selected", "onClick", "in", "timeout", "unmountOnExit", "component", "disablePadding", "pl", "p", "bgcolor", "borderLeft", "variant", "username", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/MainMenu.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Box,\n  Typography,\n  Collapse,\n  ListItemButton\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon,\n  ExpandLess,\n  ExpandMore,\n  ViewList as ViewListIcon,\n  Engineering as EngineeringIcon,\n  Inventory as InventoryIcon,\n  TableChart as TableChartIcon,\n  Assessment as AssessmentIcon,\n  VerifiedUser as VerifiedUserIcon,\n  ShoppingCart as ShoppingCartIcon,\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\n\nconst MainMenu = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, isImpersonating, impersonatedUser } = useAuth();\n\n  // Funzione di utilità per creare ListItemText con dimensione del testo più ampia (stile PyCharm)\n  const createListItemText = (primary, level = 1) => {\n    // Dimensioni del testo in base al livello del menu - più grandi per uno stile simile a PyCharm\n    const fontSize = level === 1 ? '0.9rem' : level === 2 ? '0.85rem' : '0.8rem';\n    return (\n      <ListItemText\n        primary={primary}\n        primaryTypographyProps={{ fontSize, fontWeight: level === 1 ? 500 : 400 }}\n        sx={{ my: 0.3, py: 0.3 }} // Aumenta leggermente il margine e padding verticale\n      />\n    );\n  };\n\n  // Stati per i menu a cascata - per utenti cantiere, il menu cavi è aperto di default\n  const [openCaviMenu, setOpenCaviMenu] = useState(user?.role === 'cantieri_user');\n  const [openCantieriMenu, setOpenCantieriMenu] = useState(false);\n  const [openAdminMenu, setOpenAdminMenu] = useState(false);\n  const [openPosaMenu, setOpenPosaMenu] = useState(false);\n  const [openParcoMenu, setOpenParcoMenu] = useState(false);\n  const [openExcelMenu, setOpenExcelMenu] = useState(false);\n  const [openReportMenu, setOpenReportMenu] = useState(false);\n  const [openCertificazioneMenu, setOpenCertificazioneMenu] = useState(false);\n  const [openComandeMenu, setOpenComandeMenu] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  // Gestisce l'apertura/chiusura dei menu a cascata\n  const handleToggleCaviMenu = () => {\n    setOpenCaviMenu(!openCaviMenu);\n  };\n\n  const handleToggleCantieriMenu = () => {\n    setOpenCantieriMenu(!openCantieriMenu);\n  };\n\n  const handleToggleAdminMenu = () => {\n    setOpenAdminMenu(!openAdminMenu);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      console.log('Utente impersonato, reindirizzamento al menu amministratore');\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n  };\n\n  return (\n    <List sx={{\n      width: '240px', // Menu più ampio, simile a PyCharm\n      '& .MuiListItemButton-root': {\n        py: 0.5,\n        minHeight: '36px', // Altezza maggiore per ogni voce di menu\n        '&:hover': {\n          backgroundColor: '#e9ecef'\n        }\n      },\n      '& .MuiListItemIcon-root': {\n        minWidth: '36px', // Icone più distanziate\n        color: '#6c757d' // Colore delle icone più scuro\n      },\n      '& .Mui-selected': {\n        backgroundColor: '#e3f2fd', // Colore di sfondo per l'elemento selezionato (stile PyCharm)\n        '&:hover': {\n          backgroundColor: '#bbdefb'\n        }\n      }\n    }}>\n      {/* Home - Se l'utente è un amministratore che sta impersonando un utente, mostra \"Torna al Menu Admin\" */}\n      <ListItemButton\n        selected={isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard')}\n        onClick={() => navigateTo('/dashboard')}\n      >\n        <ListItemIcon>\n          <HomeIcon fontSize=\"medium\" />\n        </ListItemIcon>\n        {createListItemText(isImpersonating ? \"Torna al Menu Admin\" : \"Home\", 1)}\n      </ListItemButton>\n\n      {/* Menu Amministratore (solo per admin) */}\n      {user?.role === 'owner' && (\n        <>\n          <Divider />\n          <ListItemButton\n            onClick={handleToggleAdminMenu}\n            selected={isPartOfActive('/dashboard/admin')}\n          >\n            <ListItemIcon>\n              <AdminIcon fontSize=\"medium\" />\n            </ListItemIcon>\n            {createListItemText(\"Amministrazione\", 1)}\n            {openAdminMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n          </ListItemButton>\n          <Collapse in={openAdminMenu} timeout=\"auto\" unmountOnExit>\n            <List component=\"div\" disablePadding>\n              <ListItemButton\n                sx={{ pl: 2 }}\n                selected={isActive('/dashboard/admin')}\n                onClick={() => navigateTo('/dashboard/admin')}\n              >\n\n                {createListItemText(\"Pannello Admin\", 2)}\n              </ListItemButton>\n              {/* Altri sottomenu admin possono essere aggiunti qui */}\n            </List>\n          </Collapse>\n        </>\n      )}\n\n      {/* Menu per utenti standard e cantieri */}\n      {/* Mostra per utenti standard/cantiere o per admin che sta impersonando un utente */}\n      {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (\n        <>\n          <Divider />\n          {isImpersonating && impersonatedUser && (\n            <Box sx={{ p: 1, bgcolor: 'rgba(255, 165, 0, 0.1)', borderLeft: '4px solid orange' }}>\n              <Typography variant=\"caption\" color=\"textSecondary\">\n                Accesso come utente:\n              </Typography>\n              <Typography variant=\"caption\" fontWeight=\"bold\">\n                {impersonatedUser.username}\n              </Typography>\n            </Box>\n          )}\n\n          {/* Menu Cantieri con sottomenu - visibile solo per utenti standard o admin che impersonano */}\n          {(user?.role === 'user' || isImpersonating) && (\n            <>\n              <ListItemButton\n                onClick={handleToggleCantieriMenu}\n                selected={isPartOfActive('/dashboard/cantieri')}\n              >\n                <ListItemIcon>\n                  <ConstructionIcon fontSize=\"medium\" />\n                </ListItemIcon>\n                {createListItemText(isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\", 1)}\n                {openCantieriMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n              </ListItemButton>\n              <Collapse in={openCantieriMenu} timeout=\"auto\" unmountOnExit>\n                <List component=\"div\" disablePadding>\n                  <ListItemButton\n                    sx={{ pl: 2 }}\n                    selected={isActive('/dashboard/cantieri')}\n                    onClick={() => navigateTo('/dashboard/cantieri')}\n                  >\n                    {createListItemText(\"Lista Cantieri\", 2)}\n                  </ListItemButton>\n\n                  {/* Mostra il cantiere selezionato se presente */}\n                  {selectedCantiereId && (\n                    <>\n                      <ListItemButton\n                        sx={{ pl: 2 }}\n                        selected={isActive(`/dashboard/cantieri/${selectedCantiereId}`)}\n                        onClick={() => navigateTo(`/dashboard/cantieri/${selectedCantiereId}`)}\n                      >\n\n                        {createListItemText(`Cantiere: ${selectedCantiereName || selectedCantiereId}`, 2)}\n                      </ListItemButton>\n                    </>\n                  )}\n                </List>\n              </Collapse>\n            </>\n          )}\n\n          {/* Menu Cavi con sottomenu - visibile solo se un cantiere è selezionato */}\n          {selectedCantiereId && (\n            <ListItemButton\n              onClick={handleToggleCaviMenu}\n              selected={isPartOfActive('/dashboard/cavi')}\n            >\n              <ListItemIcon>\n                <CableIcon fontSize=\"medium\" />\n              </ListItemIcon>\n              {createListItemText(`Gestione Cavi (${selectedCantiereName || selectedCantiereId})`, 1)}\n              {openCaviMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n            </ListItemButton>\n          )}\n\n          {selectedCantiereId && (\n            <Collapse in={openCaviMenu} timeout=\"auto\" unmountOnExit>\n              <List component=\"div\" disablePadding>\n                {/* Visualizza Cavi - nascosto per utenti cantiere perché ridondante con il tasto Home */}\n                {user?.role !== 'cantieri_user' && (\n                  <ListItemButton\n                    sx={{ pl: 2 }}\n                    selected={isActive('/dashboard/cavi/visualizza')}\n                    onClick={() => navigateTo('/dashboard/cavi/visualizza')}\n                  >\n                    {createListItemText(\"Visualizza Cavi\", 2)}\n                  </ListItemButton>\n                )}\n\n                {/* Posa e Collegamenti con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 2 }}\n                  onClick={() => setOpenPosaMenu(!openPosaMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/posa')}\n                >\n\n                  {createListItemText(\"Posa e Collegamenti\", 2)}\n                  {openPosaMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n                </ListItemButton>\n\n                <Collapse in={openPosaMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/posa/inserisci-metri')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/inserisci-metri')}\n                    >\n\n                      {createListItemText(\"Inserisci metri posati\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/posa/modifica-cavo')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/modifica-cavo')}\n                    >\n                      {createListItemText(\"Modifica cavo\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/posa/aggiungi-cavo')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/aggiungi-cavo')}\n                    >\n                      {createListItemText(\"Aggiungi nuovo cavo\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/posa/elimina-cavo')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/elimina-cavo')}\n                    >\n                      {createListItemText(\"Elimina cavo\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/posa/modifica-bobina')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/modifica-bobina')}\n                    >\n                      {createListItemText(\"Modifica bobina cavo posato\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/posa/collegamenti')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/collegamenti')}\n                    >\n                      {createListItemText(\"Gestisci collegamenti cavo\", 3)}\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Parco Cavi con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 2 }}\n                  onClick={() => setOpenParcoMenu(!openParcoMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/parco')}\n                >\n\n                  {createListItemText(\"Parco Cavi\", 2)}\n                  {openParcoMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n                </ListItemButton>\n\n                <Collapse in={openParcoMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/parco/visualizza')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/visualizza')}\n                    >\n                      {createListItemText(\"Visualizza Bobine\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/parco/crea')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/crea')}\n                    >\n                      {createListItemText(\"Crea Nuova Bobina\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/parco/modifica')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/modifica')}\n                    >\n                      {createListItemText(\"Modifica Bobina\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/parco/elimina')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/elimina')}\n                    >\n                      {createListItemText(\"Elimina Bobina\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/parco/storico')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/storico')}\n                    >\n                      {createListItemText(\"Storico Utilizzo\", 3)}\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Gestione Excel con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 2 }}\n                  onClick={() => setOpenExcelMenu(!openExcelMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/excel')}\n                >\n\n                  {createListItemText(\"Gestione Excel\", 2)}\n                  {openExcelMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n                </ListItemButton>\n\n                <Collapse in={openExcelMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/excel/importa-cavi')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/importa-cavi')}\n                    >\n                      {createListItemText(\"Importa cavi da Excel\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/excel/importa-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/importa-bobine')}\n                    >\n                      {createListItemText(\"Importa parco bobine\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/excel/template-cavi')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/template-cavi')}\n                    >\n                      {createListItemText(\"Template Excel per cavi\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/excel/template-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/template-bobine')}\n                    >\n                      {createListItemText(\"Template Excel per bobine\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/excel/esporta-cavi')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/esporta-cavi')}\n                    >\n                      {createListItemText(\"Esporta cavi in Excel\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/excel/esporta-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/esporta-bobine')}\n                    >\n                      {createListItemText(\"Esporta bobine in Excel\", 3)}\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Report con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 2 }}\n                  onClick={() => setOpenReportMenu(!openReportMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/report')}\n                >\n\n                  {createListItemText(\"Report\", 2)}\n                  {openReportMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n                </ListItemButton>\n\n                <Collapse in={openReportMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/report/avanzamento')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/avanzamento')}\n                    >\n                      {createListItemText(\"Report Avanzamento\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/report/boq')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/boq')}\n                    >\n                      {createListItemText(\"Bill of Quantities\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/report/utilizzo-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/utilizzo-bobine')}\n                    >\n                      {createListItemText(\"Report Utilizzo Bobine\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/report/posa-periodo')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/posa-periodo')}\n                    >\n                      {createListItemText(\"Report Posa per Periodo\", 3)}\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Certificazione Cavi con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 2 }}\n                  onClick={() => setOpenCertificazioneMenu(!openCertificazioneMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/certificazione')}\n                >\n\n                  {createListItemText(\"Certificazione Cavi\", 2)}\n                  {openCertificazioneMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n                </ListItemButton>\n\n                <Collapse in={openCertificazioneMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/certificazione/visualizza')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/visualizza')}\n                    >\n                      {createListItemText(\"Visualizza certificazioni\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/certificazione/filtra')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/filtra')}\n                    >\n                      {createListItemText(\"Filtra per cavo\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/certificazione/crea')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/crea')}\n                    >\n                      {createListItemText(\"Crea certificazione\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/certificazione/dettagli')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/dettagli')}\n                    >\n                      {createListItemText(\"Dettagli certificazione\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/certificazione/pdf')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/pdf')}\n                    >\n                      {createListItemText(\"Genera PDF\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/certificazione/elimina')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/elimina')}\n                    >\n                      {createListItemText(\"Elimina certificazione\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/certificazione/strumenti')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/strumenti')}\n                    >\n                      {createListItemText(\"Gestione strumenti\", 3)}\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Gestione Comande con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 2 }}\n                  onClick={() => setOpenComandeMenu(!openComandeMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/comande')}\n                >\n\n                  {createListItemText(\"Gestione Comande\", 2)}\n                  {openComandeMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n                </ListItemButton>\n\n                <Collapse in={openComandeMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/comande/visualizza')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/visualizza')}\n                    >\n                      {createListItemText(\"Visualizza comande\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/comande/crea')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/crea')}\n                    >\n                      {createListItemText(\"Crea nuova comanda\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/comande/modifica')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/modifica')}\n                    >\n                      {createListItemText(\"Modifica comanda\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/comande/elimina')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/elimina')}\n                    >\n                      {createListItemText(\"Elimina comanda\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/comande/stampa')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/stampa')}\n                    >\n                      {createListItemText(\"Stampa comanda\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/comande/assegna')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/assegna')}\n                    >\n                      {createListItemText(\"Assegna comanda a cavo\", 3)}\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n              </List>\n            </Collapse>\n          )}\n        </>\n      )}\n    </List>\n  );\n};\n\nexport default MainMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,UAAU,EACVC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,UAAU,EACVC,UAAU,EACVC,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,eAAe,EAC9BC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,YAAY,IAAIC,gBAAgB,EAChCC,YAAY,IAAIC,gBAAgB,EAChCC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAC9B,MAAMmD,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmD,IAAI;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGX,OAAO,CAAC,CAAC;;EAE7D;EACA,MAAMY,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,KAAK,GAAG,CAAC,KAAK;IACjD;IACA,MAAMC,QAAQ,GAAGD,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,QAAQ;IAC5E,oBACEZ,OAAA,CAACxC,YAAY;MACXmD,OAAO,EAAEA,OAAQ;MACjBG,sBAAsB,EAAE;QAAED,QAAQ;QAAEE,UAAU,EAAEH,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG;MAAI,CAAE;MAC1EI,EAAE,EAAE;QAAEC,EAAE,EAAE,GAAG;QAAEC,EAAE,EAAE;MAAI,CAAE,CAAC;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC;EAEN,CAAC;;EAED;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAC,CAAAqD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,MAAK,eAAe,CAAC;EAChF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0E,aAAa,EAAEC,gBAAgB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4E,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8E,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgF,aAAa,EAAEC,gBAAgB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACkF,cAAc,EAAEC,iBAAiB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACsF,eAAe,EAAEC,kBAAkB,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAMwF,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEzE;EACA,MAAME,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOzC,QAAQ,CAAC0C,QAAQ,KAAKD,IAAI;EACnC,CAAC;;EAED;EACA,MAAME,cAAc,GAAIF,IAAI,IAAK;IAC/B,OAAOzC,QAAQ,CAAC0C,QAAQ,CAACE,UAAU,CAACH,IAAI,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjC3B,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,MAAM6B,wBAAwB,GAAGA,CAAA,KAAM;IACrCzB,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,MAAM2B,qBAAqB,GAAGA,CAAA,KAAM;IAClCxB,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAM0B,UAAU,GAAIP,IAAI,IAAK;IAC3BQ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAET,IAAI,EAAE,kBAAkB,EAAEvC,eAAe,EAAE,OAAO,EAAED,IAAI,CAAC;IACvF;IACA;IACA,IAAIwC,IAAI,KAAK,YAAY,IAAIvC,eAAe,EAAE;MAC5C+C,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1EnD,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM;MACLA,QAAQ,CAAC0C,IAAI,CAAC;IAChB;EACF,CAAC;EAED,oBACE/C,OAAA,CAAC3C,IAAI;IAAC2D,EAAE,EAAE;MACRyC,KAAK,EAAE,OAAO;MAAE;MAChB,2BAA2B,EAAE;QAC3BvC,EAAE,EAAE,GAAG;QACPwC,SAAS,EAAE,MAAM;QAAE;QACnB,SAAS,EAAE;UACTC,eAAe,EAAE;QACnB;MACF,CAAC;MACD,yBAAyB,EAAE;QACzBC,QAAQ,EAAE,MAAM;QAAE;QAClBC,KAAK,EAAE,SAAS,CAAC;MACnB,CAAC;MACD,iBAAiB,EAAE;QACjBF,eAAe,EAAE,SAAS;QAAE;QAC5B,SAAS,EAAE;UACTA,eAAe,EAAE;QACnB;MACF;IACF,CAAE;IAAAG,QAAA,gBAEA9D,OAAA,CAACnC,cAAc;MACbkG,QAAQ,EAAEvD,eAAe,GAAGsC,QAAQ,CAAC,kBAAkB,CAAC,GAAGA,QAAQ,CAAC,YAAY,CAAE;MAClFkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,YAAY,CAAE;MAAAQ,QAAA,gBAExC9D,OAAA,CAACzC,YAAY;QAAAuG,QAAA,eACX9D,OAAA,CAACjC,QAAQ;UAAC8C,QAAQ,EAAC;QAAQ;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,EACdZ,kBAAkB,CAACF,eAAe,GAAG,qBAAqB,GAAG,MAAM,EAAE,CAAC,CAAC;IAAA;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,EAGhB,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,MAAK,OAAO,iBACrBzB,OAAA,CAAAE,SAAA;MAAA4D,QAAA,gBACE9D,OAAA,CAACvC,OAAO;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXtB,OAAA,CAACnC,cAAc;QACbmG,OAAO,EAAEX,qBAAsB;QAC/BU,QAAQ,EAAEd,cAAc,CAAC,kBAAkB,CAAE;QAAAa,QAAA,gBAE7C9D,OAAA,CAACzC,YAAY;UAAAuG,QAAA,eACX9D,OAAA,CAAC/B,SAAS;YAAC4C,QAAQ,EAAC;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,EACdZ,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,CAAC,EACxCkB,aAAa,gBAAG5B,OAAA,CAACxB,UAAU;UAACqC,QAAQ,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGtB,OAAA,CAACvB,UAAU;UAACoC,QAAQ,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACjBtB,OAAA,CAACpC,QAAQ;QAACqG,EAAE,EAAErC,aAAc;QAACsC,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAL,QAAA,eACvD9D,OAAA,CAAC3C,IAAI;UAAC+G,SAAS,EAAC,KAAK;UAACC,cAAc;UAAAP,QAAA,eAClC9D,OAAA,CAACnC,cAAc;YACbmD,EAAE,EAAE;cAAEsD,EAAE,EAAE;YAAE,CAAE;YACdP,QAAQ,EAAEjB,QAAQ,CAAC,kBAAkB,CAAE;YACvCkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,kBAAkB,CAAE;YAAAQ,QAAA,EAG7CpD,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;UAAC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA,eACX,CACH,EAIA,CAAC,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,MAAK,OAAO,IAAK,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,MAAK,OAAO,IAAIjB,eAAe,IAAIC,gBAAiB,kBACzFT,OAAA,CAAAE,SAAA;MAAA4D,QAAA,gBACE9D,OAAA,CAACvC,OAAO;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACVd,eAAe,IAAIC,gBAAgB,iBAClCT,OAAA,CAACtC,GAAG;QAACsD,EAAE,EAAE;UAAEuD,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE,wBAAwB;UAAEC,UAAU,EAAE;QAAmB,CAAE;QAAAX,QAAA,gBACnF9D,OAAA,CAACrC,UAAU;UAAC+G,OAAO,EAAC,SAAS;UAACb,KAAK,EAAC,eAAe;UAAAC,QAAA,EAAC;QAEpD;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtB,OAAA,CAACrC,UAAU;UAAC+G,OAAO,EAAC,SAAS;UAAC3D,UAAU,EAAC,MAAM;UAAA+C,QAAA,EAC5CrD,gBAAgB,CAACkE;QAAQ;UAAAxD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN,EAGA,CAAC,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,MAAK,MAAM,IAAIjB,eAAe,kBACxCR,OAAA,CAAAE,SAAA;QAAA4D,QAAA,gBACE9D,OAAA,CAACnC,cAAc;UACbmG,OAAO,EAAEZ,wBAAyB;UAClCW,QAAQ,EAAEd,cAAc,CAAC,qBAAqB,CAAE;UAAAa,QAAA,gBAEhD9D,OAAA,CAACzC,YAAY;YAAAuG,QAAA,eACX9D,OAAA,CAAC7B,gBAAgB;cAAC0C,QAAQ,EAAC;YAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,EACdZ,kBAAkB,CAACF,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAACkE,QAAQ,EAAE,GAAG,iBAAiB,EAAE,CAAC,CAAC,EAC3HjD,gBAAgB,gBAAG1B,OAAA,CAACxB,UAAU;YAACqC,QAAQ,EAAC;UAAO;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGtB,OAAA,CAACvB,UAAU;YAACoC,QAAQ,EAAC;UAAO;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eACjBtB,OAAA,CAACpC,QAAQ;UAACqG,EAAE,EAAEvC,gBAAiB;UAACwC,OAAO,EAAC,MAAM;UAACC,aAAa;UAAAL,QAAA,eAC1D9D,OAAA,CAAC3C,IAAI;YAAC+G,SAAS,EAAC,KAAK;YAACC,cAAc;YAAAP,QAAA,gBAClC9D,OAAA,CAACnC,cAAc;cACbmD,EAAE,EAAE;gBAAEsD,EAAE,EAAE;cAAE,CAAE;cACdP,QAAQ,EAAEjB,QAAQ,CAAC,qBAAqB,CAAE;cAC1CkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,qBAAqB,CAAE;cAAAQ,QAAA,EAEhDpD,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;YAAC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,EAGhBoB,kBAAkB,iBACjB1C,OAAA,CAAAE,SAAA;cAAA4D,QAAA,eACE9D,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,uBAAuBJ,kBAAkB,EAAE,CAAE;gBAChEsB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,uBAAuBZ,kBAAkB,EAAE,CAAE;gBAAAoB,QAAA,EAGtEpD,kBAAkB,CAAC,aAAamC,oBAAoB,IAAIH,kBAAkB,EAAE,EAAE,CAAC;cAAC;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE;YAAC,gBACjB,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,eACX,CACH,EAGAoB,kBAAkB,iBACjB1C,OAAA,CAACnC,cAAc;QACbmG,OAAO,EAAEb,oBAAqB;QAC9BY,QAAQ,EAAEd,cAAc,CAAC,iBAAiB,CAAE;QAAAa,QAAA,gBAE5C9D,OAAA,CAACzC,YAAY;UAAAuG,QAAA,eACX9D,OAAA,CAAC3B,SAAS;YAACwC,QAAQ,EAAC;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC,EACdZ,kBAAkB,CAAC,kBAAkBmC,oBAAoB,IAAIH,kBAAkB,GAAG,EAAE,CAAC,CAAC,EACtFnB,YAAY,gBAAGvB,OAAA,CAACxB,UAAU;UAACqC,QAAQ,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGtB,OAAA,CAACvB,UAAU;UAACoC,QAAQ,EAAC;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CACjB,EAEAoB,kBAAkB,iBACjB1C,OAAA,CAACpC,QAAQ;QAACqG,EAAE,EAAE1C,YAAa;QAAC2C,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAL,QAAA,eACtD9D,OAAA,CAAC3C,IAAI;UAAC+G,SAAS,EAAC,KAAK;UAACC,cAAc;UAAAP,QAAA,GAEjC,CAAAvD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,MAAK,eAAe,iBAC7BzB,OAAA,CAACnC,cAAc;YACbmD,EAAE,EAAE;cAAEsD,EAAE,EAAE;YAAE,CAAE;YACdP,QAAQ,EAAEjB,QAAQ,CAAC,4BAA4B,CAAE;YACjDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,4BAA4B,CAAE;YAAAQ,QAAA,EAEvDpD,kBAAkB,CAAC,iBAAiB,EAAE,CAAC;UAAC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CACjB,eAGDtB,OAAA,CAACnC,cAAc;YACbmD,EAAE,EAAE;cAAEsD,EAAE,EAAE;YAAE,CAAE;YACdN,OAAO,EAAEA,CAAA,KAAMjC,eAAe,CAAC,CAACD,YAAY,CAAE;YAC9CiC,QAAQ,EAAEd,cAAc,CAAC,sBAAsB,CAAE;YAAAa,QAAA,GAGhDpD,kBAAkB,CAAC,qBAAqB,EAAE,CAAC,CAAC,EAC5CoB,YAAY,gBAAG9B,OAAA,CAACxB,UAAU;cAACqC,QAAQ,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGtB,OAAA,CAACvB,UAAU;cAACoC,QAAQ,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAEjBtB,OAAA,CAACpC,QAAQ;YAACqG,EAAE,EAAEnC,YAAa;YAACoC,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAL,QAAA,eACtD9D,OAAA,CAAC3C,IAAI;cAAC+G,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAP,QAAA,gBAClC9D,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,sCAAsC,CAAE;gBAC3DkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,sCAAsC,CAAE;gBAAAQ,QAAA,EAGjEpD,kBAAkB,CAAC,wBAAwB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,oCAAoC,CAAE;gBACzDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,oCAAoC,CAAE;gBAAAQ,QAAA,EAE/DpD,kBAAkB,CAAC,eAAe,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,oCAAoC,CAAE;gBACzDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,oCAAoC,CAAE;gBAAAQ,QAAA,EAE/DpD,kBAAkB,CAAC,qBAAqB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,mCAAmC,CAAE;gBACxDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,mCAAmC,CAAE;gBAAAQ,QAAA,EAE9DpD,kBAAkB,CAAC,cAAc,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,sCAAsC,CAAE;gBAC3DkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,sCAAsC,CAAE;gBAAAQ,QAAA,EAEjEpD,kBAAkB,CAAC,6BAA6B,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,mCAAmC,CAAE;gBACxDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,mCAAmC,CAAE;gBAAAQ,QAAA,EAE9DpD,kBAAkB,CAAC,4BAA4B,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXtB,OAAA,CAACnC,cAAc;YACbmD,EAAE,EAAE;cAAEsD,EAAE,EAAE;YAAE,CAAE;YACdN,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAAC,CAACD,aAAa,CAAE;YAChD+B,QAAQ,EAAEd,cAAc,CAAC,uBAAuB,CAAE;YAAAa,QAAA,GAGjDpD,kBAAkB,CAAC,YAAY,EAAE,CAAC,CAAC,EACnCsB,aAAa,gBAAGhC,OAAA,CAACxB,UAAU;cAACqC,QAAQ,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGtB,OAAA,CAACvB,UAAU;cAACoC,QAAQ,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEjBtB,OAAA,CAACpC,QAAQ;YAACqG,EAAE,EAAEjC,aAAc;YAACkC,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAL,QAAA,eACvD9D,OAAA,CAAC3C,IAAI;cAAC+G,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAP,QAAA,gBAClC9D,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,kCAAkC,CAAE;gBACvDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,kCAAkC,CAAE;gBAAAQ,QAAA,EAE7DpD,kBAAkB,CAAC,mBAAmB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,4BAA4B,CAAE;gBACjDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,4BAA4B,CAAE;gBAAAQ,QAAA,EAEvDpD,kBAAkB,CAAC,mBAAmB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,gCAAgC,CAAE;gBACrDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,gCAAgC,CAAE;gBAAAQ,QAAA,EAE3DpD,kBAAkB,CAAC,iBAAiB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,+BAA+B,CAAE;gBACpDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,+BAA+B,CAAE;gBAAAQ,QAAA,EAE1DpD,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,+BAA+B,CAAE;gBACpDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,+BAA+B,CAAE;gBAAAQ,QAAA,EAE1DpD,kBAAkB,CAAC,kBAAkB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXtB,OAAA,CAACnC,cAAc;YACbmD,EAAE,EAAE;cAAEsD,EAAE,EAAE;YAAE,CAAE;YACdN,OAAO,EAAEA,CAAA,KAAM7B,gBAAgB,CAAC,CAACD,aAAa,CAAE;YAChD6B,QAAQ,EAAEd,cAAc,CAAC,uBAAuB,CAAE;YAAAa,QAAA,GAGjDpD,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,CAAC,EACvCwB,aAAa,gBAAGlC,OAAA,CAACxB,UAAU;cAACqC,QAAQ,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGtB,OAAA,CAACvB,UAAU;cAACoC,QAAQ,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEjBtB,OAAA,CAACpC,QAAQ;YAACqG,EAAE,EAAE/B,aAAc;YAACgC,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAL,QAAA,eACvD9D,OAAA,CAAC3C,IAAI;cAAC+G,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAP,QAAA,gBAClC9D,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,oCAAoC,CAAE;gBACzDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,oCAAoC,CAAE;gBAAAQ,QAAA,EAE/DpD,kBAAkB,CAAC,uBAAuB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,sCAAsC,CAAE;gBAC3DkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,sCAAsC,CAAE;gBAAAQ,QAAA,EAEjEpD,kBAAkB,CAAC,sBAAsB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,qCAAqC,CAAE;gBAC1DkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,qCAAqC,CAAE;gBAAAQ,QAAA,EAEhEpD,kBAAkB,CAAC,yBAAyB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,uCAAuC,CAAE;gBAC5DkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,uCAAuC,CAAE;gBAAAQ,QAAA,EAElEpD,kBAAkB,CAAC,2BAA2B,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,oCAAoC,CAAE;gBACzDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,oCAAoC,CAAE;gBAAAQ,QAAA,EAE/DpD,kBAAkB,CAAC,uBAAuB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,sCAAsC,CAAE;gBAC3DkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,sCAAsC,CAAE;gBAAAQ,QAAA,EAEjEpD,kBAAkB,CAAC,yBAAyB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXtB,OAAA,CAACnC,cAAc;YACbmD,EAAE,EAAE;cAAEsD,EAAE,EAAE;YAAE,CAAE;YACdN,OAAO,EAAEA,CAAA,KAAM3B,iBAAiB,CAAC,CAACD,cAAc,CAAE;YAClD2B,QAAQ,EAAEd,cAAc,CAAC,wBAAwB,CAAE;YAAAa,QAAA,GAGlDpD,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,EAC/B0B,cAAc,gBAAGpC,OAAA,CAACxB,UAAU;cAACqC,QAAQ,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGtB,OAAA,CAACvB,UAAU;cAACoC,QAAQ,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eAEjBtB,OAAA,CAACpC,QAAQ;YAACqG,EAAE,EAAE7B,cAAe;YAAC8B,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAL,QAAA,eACxD9D,OAAA,CAAC3C,IAAI;cAAC+G,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAP,QAAA,gBAClC9D,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,oCAAoC,CAAE;gBACzDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,oCAAoC,CAAE;gBAAAQ,QAAA,EAE/DpD,kBAAkB,CAAC,oBAAoB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,4BAA4B,CAAE;gBACjDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,4BAA4B,CAAE;gBAAAQ,QAAA,EAEvDpD,kBAAkB,CAAC,oBAAoB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,wCAAwC,CAAE;gBAC7DkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,wCAAwC,CAAE;gBAAAQ,QAAA,EAEnEpD,kBAAkB,CAAC,wBAAwB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,qCAAqC,CAAE;gBAC1DkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,qCAAqC,CAAE;gBAAAQ,QAAA,EAEhEpD,kBAAkB,CAAC,yBAAyB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXtB,OAAA,CAACnC,cAAc;YACbmD,EAAE,EAAE;cAAEsD,EAAE,EAAE;YAAE,CAAE;YACdN,OAAO,EAAEA,CAAA,KAAMzB,yBAAyB,CAAC,CAACD,sBAAsB,CAAE;YAClEyB,QAAQ,EAAEd,cAAc,CAAC,gCAAgC,CAAE;YAAAa,QAAA,GAG1DpD,kBAAkB,CAAC,qBAAqB,EAAE,CAAC,CAAC,EAC5C4B,sBAAsB,gBAAGtC,OAAA,CAACxB,UAAU;cAACqC,QAAQ,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGtB,OAAA,CAACvB,UAAU;cAACoC,QAAQ,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eAEjBtB,OAAA,CAACpC,QAAQ;YAACqG,EAAE,EAAE3B,sBAAuB;YAAC4B,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAL,QAAA,eAChE9D,OAAA,CAAC3C,IAAI;cAAC+G,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAP,QAAA,gBAClC9D,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,2CAA2C,CAAE;gBAChEkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,2CAA2C,CAAE;gBAAAQ,QAAA,EAEtEpD,kBAAkB,CAAC,2BAA2B,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,uCAAuC,CAAE;gBAC5DkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,uCAAuC,CAAE;gBAAAQ,QAAA,EAElEpD,kBAAkB,CAAC,iBAAiB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,qCAAqC,CAAE;gBAC1DkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,qCAAqC,CAAE;gBAAAQ,QAAA,EAEhEpD,kBAAkB,CAAC,qBAAqB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,yCAAyC,CAAE;gBAC9DkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,yCAAyC,CAAE;gBAAAQ,QAAA,EAEpEpD,kBAAkB,CAAC,yBAAyB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,oCAAoC,CAAE;gBACzDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,oCAAoC,CAAE;gBAAAQ,QAAA,EAE/DpD,kBAAkB,CAAC,YAAY,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,wCAAwC,CAAE;gBAC7DkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,wCAAwC,CAAE;gBAAAQ,QAAA,EAEnEpD,kBAAkB,CAAC,wBAAwB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,0CAA0C,CAAE;gBAC/DkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,0CAA0C,CAAE;gBAAAQ,QAAA,EAErEpD,kBAAkB,CAAC,oBAAoB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXtB,OAAA,CAACnC,cAAc;YACbmD,EAAE,EAAE;cAAEsD,EAAE,EAAE;YAAE,CAAE;YACdN,OAAO,EAAEA,CAAA,KAAMvB,kBAAkB,CAAC,CAACD,eAAe,CAAE;YACpDuB,QAAQ,EAAEd,cAAc,CAAC,yBAAyB,CAAE;YAAAa,QAAA,GAGnDpD,kBAAkB,CAAC,kBAAkB,EAAE,CAAC,CAAC,EACzC8B,eAAe,gBAAGxC,OAAA,CAACxB,UAAU;cAACqC,QAAQ,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGtB,OAAA,CAACvB,UAAU;cAACoC,QAAQ,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eAEjBtB,OAAA,CAACpC,QAAQ;YAACqG,EAAE,EAAEzB,eAAgB;YAAC0B,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAL,QAAA,eACzD9D,OAAA,CAAC3C,IAAI;cAAC+G,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAP,QAAA,gBAClC9D,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,oCAAoC,CAAE;gBACzDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,oCAAoC,CAAE;gBAAAQ,QAAA,EAE/DpD,kBAAkB,CAAC,oBAAoB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,8BAA8B,CAAE;gBACnDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,8BAA8B,CAAE;gBAAAQ,QAAA,EAEzDpD,kBAAkB,CAAC,oBAAoB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,kCAAkC,CAAE;gBACvDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,kCAAkC,CAAE;gBAAAQ,QAAA,EAE7DpD,kBAAkB,CAAC,kBAAkB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,iCAAiC,CAAE;gBACtDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,iCAAiC,CAAE;gBAAAQ,QAAA,EAE5DpD,kBAAkB,CAAC,iBAAiB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,gCAAgC,CAAE;gBACrDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,gCAAgC,CAAE;gBAAAQ,QAAA,EAE3DpD,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eAEjBtB,OAAA,CAACnC,cAAc;gBACbmD,EAAE,EAAE;kBAAEsD,EAAE,EAAE;gBAAE,CAAE;gBACdP,QAAQ,EAAEjB,QAAQ,CAAC,iCAAiC,CAAE;gBACtDkB,OAAO,EAAEA,CAAA,KAAMV,UAAU,CAAC,iCAAiC,CAAE;gBAAAQ,QAAA,EAE5DpD,kBAAkB,CAAC,wBAAwB,EAAE,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACX;IAAA,eACD,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAAClB,EAAA,CAzkBID,QAAQ;EAAA,QACKhD,WAAW,EACXC,WAAW,EACwB0C,OAAO;AAAA;AAAA8E,EAAA,GAHvDzE,QAAQ;AA2kBd,eAAeA,QAAQ;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}