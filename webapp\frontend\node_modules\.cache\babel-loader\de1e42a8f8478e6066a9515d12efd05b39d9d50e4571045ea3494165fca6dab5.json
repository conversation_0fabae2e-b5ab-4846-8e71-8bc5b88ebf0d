{"ast": null, "code": "import { startOfMinute } from \"./startOfMinute.js\";\n\n/**\n * @name isSameMinute\n * @category Minute Helpers\n * @summary Are the given dates in the same minute (and hour and day)?\n *\n * @description\n * Are the given dates in the same minute (and hour and day)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n *\n * @returns The dates are in the same minute (and hour and day)\n *\n * @example\n * // Are 4 September 2014 06:30:00 and 4 September 2014 06:30:15 in the same minute?\n * const result = isSameMinute(\n *   new Date(2014, 8, 4, 6, 30),\n *   new Date(2014, 8, 4, 6, 30, 15)\n * )\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:30:00 and 5 September 2014 06:30:00 in the same minute?\n * const result = isSameMinute(\n *   new Date(2014, 8, 4, 6, 30),\n *   new Date(2014, 8, 5, 6, 30)\n * )\n * //=> false\n */\nexport function isSameMinute(laterDate, earlierDate) {\n  return +startOfMinute(laterDate) === +startOfMinute(earlierDate);\n}\n\n// Fallback for modularized imports:\nexport default isSameMinute;", "map": {"version": 3, "names": ["startOfMinute", "isSameMinute", "laterDate", "earlierDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/isSameMinute.js"], "sourcesContent": ["import { startOfMinute } from \"./startOfMinute.js\";\n\n/**\n * @name isSameMinute\n * @category Minute Helpers\n * @summary Are the given dates in the same minute (and hour and day)?\n *\n * @description\n * Are the given dates in the same minute (and hour and day)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n *\n * @returns The dates are in the same minute (and hour and day)\n *\n * @example\n * // Are 4 September 2014 06:30:00 and 4 September 2014 06:30:15 in the same minute?\n * const result = isSameMinute(\n *   new Date(2014, 8, 4, 6, 30),\n *   new Date(2014, 8, 4, 6, 30, 15)\n * )\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:30:00 and 5 September 2014 06:30:00 in the same minute?\n * const result = isSameMinute(\n *   new Date(2014, 8, 4, 6, 30),\n *   new Date(2014, 8, 5, 6, 30)\n * )\n * //=> false\n */\nexport function isSameMinute(laterDate, earlierDate) {\n  return +startOfMinute(laterDate) === +startOfMinute(earlierDate);\n}\n\n// Fallback for modularized imports:\nexport default isSameMinute;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,SAAS,EAAEC,WAAW,EAAE;EACnD,OAAO,CAACH,aAAa,CAACE,SAAS,CAAC,KAAK,CAACF,aAAa,CAACG,WAAW,CAAC;AAClE;;AAEA;AACA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}