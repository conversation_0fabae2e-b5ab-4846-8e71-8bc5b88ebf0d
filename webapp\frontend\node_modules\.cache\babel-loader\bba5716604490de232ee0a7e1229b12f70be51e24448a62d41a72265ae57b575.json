{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Grid, Card, CardContent, CardActions, Button, Chip, Alert, CircularProgress, Divider, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails, Switch, FormControlLabel } from '@mui/material';\nimport { Assessment as AssessmentIcon, BarChart as BarChartIcon, PieChart as PieChartIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon, ArrowBack as ArrowBackIcon, DateRange as DateRangeIcon, Cable as CableIcon, Inventory as InventoryIcon, ExpandMore as ExpandMoreIcon, ShowChart as ShowChartIcon } from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    cantiereId\n  } = useParams();\n  const {\n    user\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading progress report:', err);\n          return {\n            content: null\n          };\n        });\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video').catch(err => {\n          console.error('Error loading BOQ report:', err);\n          return {\n            content: null\n          };\n        });\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading bobine report:', err);\n          return {\n            content: null\n          };\n        });\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading cavi stato report:', err);\n          return {\n            content: null\n          };\n        });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([progressPromise, boqPromise, bobinePromise, caviStatoPromise]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [{\n    id: 'progress',\n    title: 'Report Avanzamento',\n    description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n    icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 13\n    }, this),\n    color: 'primary',\n    features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n  }, {\n    id: 'boq',\n    title: 'Bill of Quantities',\n    description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n    icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 13\n    }, this),\n    color: 'secondary',\n    features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n  }, {\n    id: 'bobine',\n    title: 'Report Utilizzo Bobine',\n    description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n    icon: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 13\n    }, this),\n    color: 'success',\n    features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n  }, {\n    id: 'bobina-specifica',\n    title: 'Report Bobina Specifica',\n    description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n    icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 13\n    }, this),\n    color: 'info',\n    features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n  }, {\n    id: 'posa-periodo',\n    title: 'Report Posa per Periodo',\n    description: 'Analisi temporale della posa con trend e pattern di lavoro',\n    icon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 13\n    }, this),\n    color: 'warning',\n    features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n  }, {\n    id: 'cavi-stato',\n    title: 'Report Cavi per Stato',\n    description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n    icon: /*#__PURE__*/_jsxDEV(BarChartIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 13\n    }, this),\n    color: 'error',\n    features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n  }];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReportSelect = reportType => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderReportContent = () => {\n    if (!reportData) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title, \" - \", reportData.nome_cantiere]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'pdf'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"primary\",\n            children: \"PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'excel'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"success\",\n            children: \"Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 26\n            }, this),\n            onClick: () => setReportData(null),\n            variant: \"outlined\",\n            size: \"small\",\n            children: \"Nuovo Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), dialogType === 'progress' && renderProgressReport(reportData), dialogType === 'boq' && renderBoqReport(reportData), dialogType === 'bobine' && renderBobineReport(reportData), dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData), dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData), dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this);\n  };\n  const renderProgressReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: 'primary.main'\n        },\n        children: [\"Report Avanzamento - \", data.nome_cantiere || 'Cantiere']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 381,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'primary.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.metri_totali, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Metri Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'success.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.metri_posati, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Metri Posati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: [\"(\", data.percentuale_avanzamento, \"%)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'warning.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.metri_da_posare, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Metri Rimanenti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: [\"(\", 100 - data.percentuale_avanzamento, \"%)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'info.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.media_giornaliera, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Giorno\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this), data.giorni_stimati && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: [\"(\", data.giorni_stimati, \" giorni rimasti)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(ProgressChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2,\n              fontWeight: 600\n            },\n            children: \"Dettagli Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: \"Totale Cavi:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                fontWeight: 600\n              },\n              children: data.totale_cavi\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: \"Cavi Posati:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                fontWeight: 600,\n                color: 'success.main'\n              },\n              children: [data.cavi_posati, \" (\", data.percentuale_cavi, \"%)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: \"Cavi Rimanenti:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                fontWeight: 600,\n                color: 'warning.main'\n              },\n              children: [data.cavi_rimanenti, \" (\", 100 - data.percentuale_cavi, \"%)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2,\n              fontWeight: 600\n            },\n            children: \"Performance e Previsioni\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: \"Media Giornaliera:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                fontWeight: 600\n              },\n              children: [data.media_giornaliera, \"m/giorno\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this), data.giorni_stimati && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: \"Giorni Stimati:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                sx: {\n                  fontWeight: 600\n                },\n                children: [data.giorni_stimati, \" giorni\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: \"Data Completamento:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                sx: {\n                  fontWeight: 600\n                },\n                children: data.data_completamento\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this), data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2,\n              fontWeight: 600\n            },\n            children: \"Attivit\\xE0 Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.posa_recente.map(posa => ({\n              data: posa.data,\n              metri: `${posa.metri}m`\n            })),\n            columns: [{\n              field: 'data',\n              headerName: 'Data',\n              width: 200\n            }, {\n              field: 'metri',\n              headerName: 'Metri Posati',\n              width: 150,\n              align: 'right'\n            }],\n            pagination: data.posa_recente.length > 6,\n            pageSize: 6\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 375,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = data => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [showCharts && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(BoqChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavi per Tipologia\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.cavi_per_tipo || [],\n            columns: [{\n              field: 'tipologia',\n              headerName: 'Tipologia',\n              width: 150\n            }, {\n              field: 'sezione',\n              headerName: 'Sezione',\n              width: 100\n            }, {\n              field: 'num_cavi',\n              headerName: 'Cavi',\n              width: 80,\n              align: 'right',\n              dataType: 'number'\n            }, {\n              field: 'metri_teorici',\n              headerName: 'Metri Teorici',\n              width: 120,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_teorici}m`\n            }, {\n              field: 'metri_reali',\n              headerName: 'Metri Reali',\n              width: 120,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_reali}m`\n            }, {\n              field: 'metri_da_posare',\n              headerName: 'Da Posare',\n              width: 120,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_da_posare}m`\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 534,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Bobine Disponibili\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.bobine_per_tipo || [],\n            columns: [{\n              field: 'tipologia',\n              headerName: 'Tipologia',\n              width: 150\n            }, {\n              field: 'sezione',\n              headerName: 'Sezione',\n              width: 100\n            }, {\n              field: 'num_bobine',\n              headerName: 'Bobine',\n              width: 100,\n              align: 'right',\n              dataType: 'number'\n            }, {\n              field: 'metri_disponibili',\n              headerName: 'Metri Disponibili',\n              width: 150,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_disponibili}m`\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 525,\n    columnNumber: 5\n  }, this);\n  const renderBobineReport = data => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [showCharts && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(BobineChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [\"Bobine del Cantiere (\", data.totale_bobine, \" totali)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.bobine || [],\n            columns: [{\n              field: 'id_bobina',\n              headerName: 'ID Bobina',\n              width: 120\n            }, {\n              field: 'tipologia',\n              headerName: 'Tipologia',\n              width: 150\n            }, {\n              field: 'sezione',\n              headerName: 'Sezione',\n              width: 100\n            }, {\n              field: 'stato',\n              headerName: 'Stato',\n              width: 120,\n              renderCell: row => /*#__PURE__*/_jsxDEV(Chip, {\n                label: row.stato,\n                color: row.stato === 'DISPONIBILE' ? 'success' : 'warning',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 21\n              }, this)\n            }, {\n              field: 'metri_totali',\n              headerName: 'Metri Totali',\n              width: 120,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_totali}m`\n            }, {\n              field: 'metri_residui',\n              headerName: 'Metri Residui',\n              width: 120,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_residui}m`\n            }, {\n              field: 'metri_utilizzati',\n              headerName: 'Metri Utilizzati',\n              width: 140,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_utilizzati}m`\n            }, {\n              field: 'percentuale_utilizzo',\n              headerName: 'Utilizzo',\n              width: 100,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.percentuale_utilizzo}%`\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 582,\n    columnNumber: 5\n  }, this);\n  const renderBobinaSpecificaReport = data => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Dettagli Bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.bobina ? [{\n              proprietà: 'ID Bobina',\n              valore: data.bobina.id_bobina\n            }, {\n              proprietà: 'Tipologia',\n              valore: data.bobina.tipologia\n            }, {\n              proprietà: 'Sezione',\n              valore: data.bobina.sezione\n            }, {\n              proprietà: 'Stato',\n              valore: data.bobina.stato,\n              renderSpecial: true\n            }, {\n              proprietà: 'Metri Totali',\n              valore: `${data.bobina.metri_totali}m`\n            }, {\n              proprietà: 'Metri Residui',\n              valore: `${data.bobina.metri_residui}m`\n            }, {\n              proprietà: 'Metri Utilizzati',\n              valore: `${data.bobina.metri_utilizzati}m`\n            }, {\n              proprietà: 'Percentuale Utilizzo',\n              valore: `${data.bobina.percentuale_utilizzo}%`\n            }] : [],\n            columns: [{\n              field: 'proprietà',\n              headerName: 'Proprietà',\n              width: 200\n            }, {\n              field: 'valore',\n              headerName: 'Valore',\n              width: 250,\n              renderCell: row => row.renderSpecial ? /*#__PURE__*/_jsxDEV(Chip, {\n                label: row.valore,\n                color: row.valore === 'DISPONIBILE' ? 'success' : 'warning',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 679,\n                columnNumber: 21\n              }, this) : row.valore\n            }],\n            pagination: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 634,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 633,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [\"Cavi Associati (\", data.totale_cavi, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.cavi_associati || [],\n            columns: [{\n              field: 'id_cavo',\n              headerName: 'ID Cavo',\n              width: 120\n            }, {\n              field: 'sistema',\n              headerName: 'Sistema',\n              width: 120\n            }, {\n              field: 'utility',\n              headerName: 'Utility',\n              width: 120\n            }, {\n              field: 'tipologia',\n              headerName: 'Tipologia',\n              width: 150\n            }, {\n              field: 'metri_teorici',\n              headerName: 'Metri Teorici',\n              width: 120,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_teorici}m`\n            }, {\n              field: 'metri_reali',\n              headerName: 'Metri Reali',\n              width: 120,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_reali}m`\n            }, {\n              field: 'stato',\n              headerName: 'Stato',\n              width: 120,\n              renderCell: row => /*#__PURE__*/_jsxDEV(Chip, {\n                label: row.stato,\n                color: row.stato === 'POSATO' ? 'success' : 'warning',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 21\n              }, this)\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 695,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 694,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 631,\n    columnNumber: 5\n  }, this);\n  const renderPosaPeriodoReport = data => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [showCharts && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(TimelineChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 735,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 734,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Statistiche Periodo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: [{\n              statistica: 'Periodo',\n              valore: `${data.data_inizio} - ${data.data_fine}`\n            }, {\n              statistica: 'Totale Metri',\n              valore: `${data.totale_metri_periodo}m`\n            }, {\n              statistica: 'Giorni Attivi',\n              valore: data.giorni_attivi\n            }, {\n              statistica: 'Media Giornaliera',\n              valore: `${data.media_giornaliera}m/giorno`\n            }],\n            columns: [{\n              field: 'statistica',\n              headerName: 'Statistica',\n              width: 200\n            }, {\n              field: 'valore',\n              headerName: 'Valore',\n              width: 250,\n              align: 'right'\n            }],\n            pagination: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 741,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 740,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Posa Giornaliera\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 779,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 778,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.posa_giornaliera || [],\n            columns: [{\n              field: 'data',\n              headerName: 'Data',\n              width: 200\n            }, {\n              field: 'metri',\n              headerName: 'Metri Posati',\n              width: 150,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri}m`\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 782,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 781,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 777,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 776,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 731,\n    columnNumber: 5\n  }, this);\n  const renderCaviStatoReport = data => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [showCharts && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(CaviStatoChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 801,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 800,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavi per Stato di Installazione\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 808,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n            data: data.cavi_per_stato || [],\n            columns: [{\n              field: 'stato',\n              headerName: 'Stato',\n              width: 150,\n              renderCell: row => /*#__PURE__*/_jsxDEV(Chip, {\n                label: row.stato,\n                color: row.stato === 'Installato' ? 'success' : 'warning',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 21\n              }, this)\n            }, {\n              field: 'num_cavi',\n              headerName: 'Numero Cavi',\n              width: 120,\n              align: 'right',\n              dataType: 'number'\n            }, {\n              field: 'metri_teorici',\n              headerName: 'Metri Teorici',\n              width: 150,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_teorici}m`\n            }, {\n              field: 'metri_reali',\n              headerName: 'Metri Reali',\n              width: 150,\n              align: 'right',\n              dataType: 'number',\n              renderCell: row => `${row.metri_reali}m`\n            }],\n            pagination: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 812,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 807,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 806,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 797,\n    columnNumber: 5\n  }, this);\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 840,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 845,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 854,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 852,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 851,\n          columnNumber: 11\n        }, this), dialogType === 'bobina-specifica' && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Bobina\",\n            value: formData.id_bobina,\n            onChange: e => setFormData({\n              ...formData,\n              id_bobina: e.target.value\n            }),\n            placeholder: \"Es: 1, 2, A, B...\",\n            helperText: \"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 868,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 13\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 881,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 892,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 891,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 850,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 843,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 906,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 911,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 911,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 907,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 905,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 839,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => navigate(-1),\n          color: \"primary\",\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 924,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Report e Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 927,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 923,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 931,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 922,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 937,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 936,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 945,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n              sx: {\n                mr: 1,\n                color: 'primary.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 947,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Avanzamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 946,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 945,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.progress ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                control: /*#__PURE__*/_jsxDEV(Switch, {\n                  checked: showCharts,\n                  onChange: e => setShowCharts(e.target.checked),\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 957,\n                  columnNumber: 23\n                }, this),\n                label: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n                    sx: {\n                      mr: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 965,\n                    columnNumber: 25\n                  }, this), \"Mostra Grafici\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 964,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 955,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 972,\n                    columnNumber: 34\n                  }, this),\n                  onClick: () => generateReportWithFormat('progress', 'pdf'),\n                  variant: \"outlined\",\n                  size: \"small\",\n                  color: \"primary\",\n                  sx: {\n                    mr: 1\n                  },\n                  children: \"PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 971,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 982,\n                    columnNumber: 34\n                  }, this),\n                  onClick: () => generateReportWithFormat('progress', 'excel'),\n                  variant: \"outlined\",\n                  size: \"small\",\n                  color: \"success\",\n                  children: \"Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 981,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 954,\n              columnNumber: 17\n            }, this), renderProgressReport(reportsData.progress)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 953,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 996,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 997,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 995,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1007,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getProgressReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    progress: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying progress report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1004,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1000,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 951,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 944,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1034,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n              sx: {\n                mr: 1,\n                color: 'secondary.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Bill of Quantities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1035,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1034,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.boq ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1045,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1044,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1055,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1054,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1043,\n              columnNumber: 17\n            }, this), renderBoqReport(reportsData.boq)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1042,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1068,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1069,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1067,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1073,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1079,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getBillOfQuantities(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    boq: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying BOQ report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1076,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1072,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1040,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1033,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1106,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(InventoryIcon, {\n              sx: {\n                mr: 1,\n                color: 'success.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Utilizzo Bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1107,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.bobine ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1117,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobine', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1116,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1127,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobine', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1126,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1115,\n              columnNumber: 17\n            }, this), renderBobineReport(reportsData.bobine)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1114,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1140,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1141,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1139,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1145,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1151,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getBobineReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    bobine: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying bobine report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1148,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1144,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1178,\n            columnNumber: 41\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(BarChartIcon, {\n              sx: {\n                mr: 1,\n                color: 'error.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Cavi per Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1181,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1179,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: reportsData.caviStato ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1189,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('cavi-stato', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1199,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('cavi-stato', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1198,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1187,\n              columnNumber: 17\n            }, this), renderCaviStatoReport(reportsData.caviStato)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1186,\n            columnNumber: 15\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              my: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1212,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1213,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1211,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1217,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1223,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getCaviStatoReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    caviStato: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying cavi stato report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1220,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1216,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1184,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Report Speciali\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: \"Questi report richiedono parametri aggiuntivi per essere generati.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Report Bobina Specifica\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1260,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: \"Dettaglio approfondito di una singola bobina con tutti i cavi associati.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1261,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  color: \"info\",\n                  onClick: () => {\n                    setDialogType('bobina-specifica');\n                    setOpenDialog(true);\n                  },\n                  children: \"Genera Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1266,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1258,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: \"Report Posa per Periodo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1285,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    mb: 2\n                  },\n                  children: \"Analisi temporale della posa con trend e pattern di lavoro.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1286,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1284,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  color: \"warning\",\n                  onClick: () => {\n                    setDialogType('posa-periodo');\n                    // Set default date range (last month to today)\n                    const today = new Date();\n                    const lastMonth = new Date();\n                    lastMonth.setMonth(today.getMonth() - 1);\n                    setFormData({\n                      ...formData,\n                      data_inizio: lastMonth.toISOString().split('T')[0],\n                      data_fine: today.toISOString().split('T')[0]\n                    });\n                    setOpenDialog(true);\n                  },\n                  children: \"Genera Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1291,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1290,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1283,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1282,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1249,\n        columnNumber: 9\n      }, this), reportsData.bobinaSpecifica && /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2,\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1321,\n            columnNumber: 43\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n              sx: {\n                mr: 1,\n                color: 'info.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1323,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Bobina Specifica\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1324,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1322,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1321,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1331,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobina-specifica', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1330,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1341,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('bobina-specifica', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1340,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1329,\n              columnNumber: 17\n            }, this), renderBobinaSpecificaReport(reportsData.bobinaSpecifica)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1328,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1327,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1320,\n        columnNumber: 11\n      }, this), reportsData.posaPeriodo && /*#__PURE__*/_jsxDEV(Accordion, {\n        defaultExpanded: true,\n        sx: {\n          mb: 2,\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n          expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1358,\n            columnNumber: 43\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n              sx: {\n                mr: 1,\n                color: 'warning.main'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1360,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: \"Report Posa per Periodo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1361,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1359,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1358,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1368,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1367,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1378,\n                  columnNumber: 32\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1377,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1366,\n              columnNumber: 17\n            }, this), renderPosaPeriodoReport(reportsData.posaPeriodo)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1365,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1364,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1357,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 942,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 920,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageNew, \"Qo68i3NRMGbRWcOk2D5QM9YbhYQ=\", false, function () {\n  return [useNavigate, useParams, useAuth];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Switch", "FormControlLabel", "Assessment", "AssessmentIcon", "<PERSON><PERSON><PERSON>", "BarChartIcon", "<PERSON><PERSON><PERSON>", "PieChartIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "ArrowBack", "ArrowBackIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "ShowChart", "ShowChartIcon", "useNavigate", "useParams", "useAuth", "AdminHomeButton", "reportService", "FilterableTable", "ProgressChart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TimelineChart", "Cavi<PERSON>tato<PERSON>hart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s", "navigate", "cantiereId", "user", "loading", "setLoading", "error", "setError", "reportData", "setReportData", "selectedReport", "setSelectedReport", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportsData", "setReportsData", "progress", "boq", "bobine", "caviStato", "bobinaSpecifica", "posaPeriodo", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAllReports", "progressPromise", "getProgressReport", "catch", "err", "console", "content", "boq<PERSON><PERSON><PERSON>", "getBillOfQuantities", "bob<PERSON><PERSON><PERSON><PERSON>", "getBobineReport", "caviStatoPromise", "getCaviStatoReport", "progressData", "boqData", "bobine<PERSON><PERSON>", "caviStatoData", "Promise", "all", "reportTypes", "id", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "features", "generateReportWithFormat", "reportType", "format", "response", "getBobinaReport", "getPosaPerPeriodoReport", "Error", "prev", "file_url", "window", "open", "detail", "message", "handleReportSelect", "today", "Date", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "handleGenerateReport", "handleCloseDialog", "renderReportContent", "sx", "p", "mt", "children", "display", "justifyContent", "alignItems", "mb", "variant", "nome_cantiere", "gap", "startIcon", "onClick", "size", "renderProgressReport", "renderBoqReport", "renderBobineReport", "renderBobinaSpecificaReport", "renderPosaPeriodoReport", "renderCaviStatoReport", "data", "fontWeight", "control", "checked", "onChange", "e", "target", "label", "mr", "container", "spacing", "item", "xs", "md", "textAlign", "bgcolor", "metri_totali", "metri_posati", "percentuale_avanzamento", "metri_da_posare", "media_giornaliera", "giorni_stimati", "totale_cavi", "cavi_posati", "percentuale_cavi", "cavi_rimanenti", "data_completamento", "posa_recente", "length", "map", "posa", "metri", "columns", "field", "headerName", "width", "align", "pagination", "pageSize", "defaultExpanded", "expandIcon", "cavi_per_tipo", "dataType", "renderCell", "row", "metri_te<PERSON>ci", "metri_reali", "bobine_per_tipo", "metri_disponibili", "totale_bobine", "stato", "metri_residui", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "percentuale_utilizzo", "bobina", "proprietà", "valore", "tipologia", "sezione", "renderSpecial", "cavi_associati", "statistica", "totale_metri_periodo", "giorni_attivi", "posa_giornal<PERSON>", "cavi_per_stato", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "value", "placeholder", "helperText", "type", "InputLabelProps", "shrink", "disabled", "component", "my", "ml", "then", "finally", "gutterBottom", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n  IconButton,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n  BarChart as BarChartIcon,\n  <PERSON><PERSON><PERSON> as PieChartIcon,\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n  ArrowBack as ArrowBackIcon,\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon,\n  ShowChart as ShowChartIcon\n} from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\n\nconst ReportCaviPageNew = () => {\n  const navigate = useNavigate();\n  const { cantiereId } = useParams();\n  const { user } = useAuth();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading progress report:', err);\n            return { content: null };\n          });\n\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading BOQ report:', err);\n            return { content: null };\n          });\n\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading bobine report:', err);\n            return { content: null };\n          });\n\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading cavi stato report:', err);\n            return { content: null };\n          });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          bobinePromise,\n          caviStatoPromise\n        ]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [\n    {\n      id: 'progress',\n      title: 'Report Avanzamento',\n      description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n      icon: <AssessmentIcon />,\n      color: 'primary',\n      features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n    },\n    {\n      id: 'boq',\n      title: 'Bill of Quantities',\n      description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n      icon: <ListIcon />,\n      color: 'secondary',\n      features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n    },\n    {\n      id: 'bobine',\n      title: 'Report Utilizzo Bobine',\n      description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n      icon: <InventoryIcon />,\n      color: 'success',\n      features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n    },\n    {\n      id: 'bobina-specifica',\n      title: 'Report Bobina Specifica',\n      description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n      icon: <CableIcon />,\n      color: 'info',\n      features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n    },\n    {\n      id: 'posa-periodo',\n      title: 'Report Posa per Periodo',\n      description: 'Analisi temporale della posa con trend e pattern di lavoro',\n      icon: <TimelineIcon />,\n      color: 'warning',\n      features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n    },\n    {\n      id: 'cavi-stato',\n      title: 'Report Cavi per Stato',\n      description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n      icon: <BarChartIcon />,\n      color: 'error',\n      features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n    }\n  ];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReportSelect = (reportType) => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n  const renderReportContent = () => {\n    if (!reportData) return null;\n\n    return (\n      <Paper sx={{ p: 3, mt: 3 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\">\n            {selectedReport?.title} - {reportData.nome_cantiere}\n          </Typography>\n          <Box sx={{ display: 'flex', gap: 1 }}>\n            {/* Export buttons */}\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'pdf')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"primary\"\n            >\n              PDF\n            </Button>\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'excel')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"success\"\n            >\n              Excel\n            </Button>\n            <Button\n              startIcon={<RefreshIcon />}\n              onClick={() => setReportData(null)}\n              variant=\"outlined\"\n              size=\"small\"\n            >\n              Nuovo Report\n            </Button>\n          </Box>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Renderizza il contenuto specifico del report */}\n        {dialogType === 'progress' && renderProgressReport(reportData)}\n        {dialogType === 'boq' && renderBoqReport(reportData)}\n        {dialogType === 'bobine' && renderBobineReport(reportData)}\n        {dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData)}\n        {dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData)}\n        {dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)}\n      </Paper>\n    );\n  };\n\n  const renderProgressReport = (data) => (\n    <Box>\n      {/* Header con controlli */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n          Report Avanzamento - {data.nome_cantiere || 'Cantiere'}\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Metriche Principali - Layout Orizzontale */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.metri_totali}m\n            </Typography>\n            <Typography variant=\"body1\">Metri Totali</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'success.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.metri_posati}m\n            </Typography>\n            <Typography variant=\"body1\">Metri Posati</Typography>\n            <Typography variant=\"caption\">({data.percentuale_avanzamento}%)</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'warning.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.metri_da_posare}m\n            </Typography>\n            <Typography variant=\"body1\">Metri Rimanenti</Typography>\n            <Typography variant=\"caption\">({100 - data.percentuale_avanzamento}%)</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'info.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.media_giornaliera}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Giorno</Typography>\n            {data.giorni_stimati && (\n              <Typography variant=\"caption\">({data.giorni_stimati} giorni rimasti)</Typography>\n            )}\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <ProgressChart data={data} />\n        </Box>\n      )}\n\n      {/* Dettagli Cavi e Performance */}\n      <Grid container spacing={3}>\n        {/* Dettagli Cavi */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n              Dettagli Cavi\n            </Typography>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>\n              <Typography variant=\"body1\">Totale Cavi:</Typography>\n              <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{data.totale_cavi}</Typography>\n            </Box>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>\n              <Typography variant=\"body1\">Cavi Posati:</Typography>\n              <Typography variant=\"body1\" sx={{ fontWeight: 600, color: 'success.main' }}>\n                {data.cavi_posati} ({data.percentuale_cavi}%)\n              </Typography>\n            </Box>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n              <Typography variant=\"body1\">Cavi Rimanenti:</Typography>\n              <Typography variant=\"body1\" sx={{ fontWeight: 600, color: 'warning.main' }}>\n                {data.cavi_rimanenti} ({100 - data.percentuale_cavi}%)\n              </Typography>\n            </Box>\n          </Paper>\n        </Grid>\n\n        {/* Performance e Previsioni */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n              Performance e Previsioni\n            </Typography>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>\n              <Typography variant=\"body1\">Media Giornaliera:</Typography>\n              <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{data.media_giornaliera}m/giorno</Typography>\n            </Box>\n            {data.giorni_stimati && (\n              <>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>\n                  <Typography variant=\"body1\">Giorni Stimati:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{data.giorni_stimati} giorni</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body1\">Data Completamento:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{data.data_completamento}</Typography>\n                </Box>\n              </>\n            )}\n          </Paper>\n        </Grid>\n\n        {/* Posa Recente */}\n        {data.posa_recente && data.posa_recente.length > 0 && (\n          <Grid item xs={12}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n                Attività Recente\n              </Typography>\n              <FilterableTable\n                data={data.posa_recente.map(posa => ({\n                  data: posa.data,\n                  metri: `${posa.metri}m`\n                }))}\n                columns={[\n                  { field: 'data', headerName: 'Data', width: 200 },\n                  { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }\n                ]}\n                pagination={data.posa_recente.length > 6}\n                pageSize={6}\n              />\n            </Paper>\n          </Grid>\n        )}\n      </Grid>\n    </Box>\n  );\n\n  const renderBoqReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <BoqChart data={data} />\n        </Grid>\n      )}\n\n      {/* Cavi per Tipologia - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi per Tipologia</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.cavi_per_tipo || []}\n              columns={[\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'sezione', headerName: 'Sezione', width: 100 },\n                { field: 'num_cavi', headerName: 'Cavi', width: 80, align: 'right', dataType: 'number' },\n                { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_teorici}m` },\n                { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_reali}m` },\n                { field: 'metri_da_posare', headerName: 'Da Posare', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_da_posare}m` }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Bobine Disponibili - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Bobine Disponibili</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.bobine_per_tipo || []}\n              columns={[\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'sezione', headerName: 'Sezione', width: 100 },\n                { field: 'num_bobine', headerName: 'Bobine', width: 100, align: 'right', dataType: 'number' },\n                { field: 'metri_disponibili', headerName: 'Metri Disponibili', width: 150, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_disponibili}m` }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobineReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <BobineChart data={data} />\n        </Grid>\n      )}\n\n      {/* Bobine del Cantiere - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">\n              Bobine del Cantiere ({data.totale_bobine} totali)\n            </Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.bobine || []}\n              columns={[\n                { field: 'id_bobina', headerName: 'ID Bobina', width: 120 },\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'sezione', headerName: 'Sezione', width: 100 },\n                { field: 'stato', headerName: 'Stato', width: 120,\n                  renderCell: (row) => (\n                    <Chip\n                      label={row.stato}\n                      color={row.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  )\n                },\n                { field: 'metri_totali', headerName: 'Metri Totali', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_totali}m` },\n                { field: 'metri_residui', headerName: 'Metri Residui', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_residui}m` },\n                { field: 'metri_utilizzati', headerName: 'Metri Utilizzati', width: 140, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_utilizzati}m` },\n                { field: 'percentuale_utilizzo', headerName: 'Utilizzo', width: 100, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.percentuale_utilizzo}%` }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobinaSpecificaReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Dettagli Bobina - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Dettagli Bobina</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.bobina ? [\n                {\n                  proprietà: 'ID Bobina',\n                  valore: data.bobina.id_bobina\n                },\n                {\n                  proprietà: 'Tipologia',\n                  valore: data.bobina.tipologia\n                },\n                {\n                  proprietà: 'Sezione',\n                  valore: data.bobina.sezione\n                },\n                {\n                  proprietà: 'Stato',\n                  valore: data.bobina.stato,\n                  renderSpecial: true\n                },\n                {\n                  proprietà: 'Metri Totali',\n                  valore: `${data.bobina.metri_totali}m`\n                },\n                {\n                  proprietà: 'Metri Residui',\n                  valore: `${data.bobina.metri_residui}m`\n                },\n                {\n                  proprietà: 'Metri Utilizzati',\n                  valore: `${data.bobina.metri_utilizzati}m`\n                },\n                {\n                  proprietà: 'Percentuale Utilizzo',\n                  valore: `${data.bobina.percentuale_utilizzo}%`\n                }\n              ] : []}\n              columns={[\n                { field: 'proprietà', headerName: 'Proprietà', width: 200 },\n                { field: 'valore', headerName: 'Valore', width: 250,\n                  renderCell: (row) => row.renderSpecial ? (\n                    <Chip\n                      label={row.valore}\n                      color={row.valore === 'DISPONIBILE' ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  ) : row.valore\n                }\n              ]}\n              pagination={false}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Cavi Associati - Tabella invece di elenco */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">\n              Cavi Associati ({data.totale_cavi})\n            </Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.cavi_associati || []}\n              columns={[\n                { field: 'id_cavo', headerName: 'ID Cavo', width: 120 },\n                { field: 'sistema', headerName: 'Sistema', width: 120 },\n                { field: 'utility', headerName: 'Utility', width: 120 },\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_teorici}m` },\n                { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_reali}m` },\n                { field: 'stato', headerName: 'Stato', width: 120,\n                  renderCell: (row) => (\n                    <Chip\n                      label={row.stato}\n                      color={row.stato === 'POSATO' ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  )\n                }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderPosaPeriodoReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <TimelineChart data={data} />\n        </Grid>\n      )}\n\n      {/* Statistiche Periodo - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Statistiche Periodo</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={[\n                {\n                  statistica: 'Periodo',\n                  valore: `${data.data_inizio} - ${data.data_fine}`\n                },\n                {\n                  statistica: 'Totale Metri',\n                  valore: `${data.totale_metri_periodo}m`\n                },\n                {\n                  statistica: 'Giorni Attivi',\n                  valore: data.giorni_attivi\n                },\n                {\n                  statistica: 'Media Giornaliera',\n                  valore: `${data.media_giornaliera}m/giorno`\n                }\n              ]}\n              columns={[\n                { field: 'statistica', headerName: 'Statistica', width: 200 },\n                { field: 'valore', headerName: 'Valore', width: 250, align: 'right' }\n              ]}\n              pagination={false}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Posa Giornaliera - Tabella invece di elenco */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Posa Giornaliera</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.posa_giornaliera || []}\n              columns={[\n                { field: 'data', headerName: 'Data', width: 200 },\n                { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri}m` }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderCaviStatoReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <CaviStatoChart data={data} />\n        </Grid>\n      )}\n\n      {/* Cavi per Stato di Installazione - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi per Stato di Installazione</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.cavi_per_stato || []}\n              columns={[\n                { field: 'stato', headerName: 'Stato', width: 150,\n                  renderCell: (row) => (\n                    <Chip\n                      label={row.stato}\n                      color={row.stato === 'Installato' ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  )\n                },\n                { field: 'num_cavi', headerName: 'Numero Cavi', width: 120, align: 'right', dataType: 'number' },\n                { field: 'metri_teorici', headerName: 'Metri Teorici', width: 150, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_teorici}m` },\n                { field: 'metri_reali', headerName: 'Metri Reali', width: 150, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_reali}m` }\n              ]}\n              pagination={false}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {selectedReport?.title}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n          {dialogType === 'bobina-specifica' && (\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"ID Bobina\"\n                value={formData.id_bobina}\n                onChange={(e) => setFormData({ ...formData, id_bobina: e.target.value })}\n                placeholder=\"Es: 1, 2, A, B...\"\n                helperText=\"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n              />\n            </Grid>\n          )}\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <IconButton onClick={() => navigate(-1)} color=\"primary\">\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\" component=\"h1\">\n            Report e Analytics\n          </Typography>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Loading indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Reports */}\n      <Box sx={{ mt: 3 }}>\n        {/* Progress Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <AssessmentIcon sx={{ mr: 1, color: 'primary.main' }} />\n              <Typography variant=\"h6\">Report Avanzamento</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.progress ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n                  <FormControlLabel\n                    control={\n                      <Switch\n                        checked={showCharts}\n                        onChange={(e) => setShowCharts(e.target.checked)}\n                        color=\"primary\"\n                      />\n                    }\n                    label={\n                      <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                        <ShowChartIcon sx={{ mr: 1 }} />\n                        Mostra Grafici\n                      </Box>\n                    }\n                  />\n                  <Box>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                </Box>\n                {renderProgressReport(reportsData.progress)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getProgressReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          progress: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying progress report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Bill of Quantities */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ListIcon sx={{ mr: 1, color: 'secondary.main' }} />\n              <Typography variant=\"h6\">Bill of Quantities</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.boq ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('boq', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('boq', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBoqReport(reportsData.boq)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getBillOfQuantities(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          boq: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying BOQ report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Bobine Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <InventoryIcon sx={{ mr: 1, color: 'success.main' }} />\n              <Typography variant=\"h6\">Report Utilizzo Bobine</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.bobine ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobine', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobine', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBobineReport(reportsData.bobine)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getBobineReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          bobine: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying bobine report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Cavi Stato Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <BarChartIcon sx={{ mr: 1, color: 'error.main' }} />\n              <Typography variant=\"h6\">Report Cavi per Stato</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.caviStato ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('cavi-stato', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('cavi-stato', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderCaviStatoReport(reportsData.caviStato)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getCaviStatoReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          caviStato: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying cavi stato report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Special Reports Section */}\n        <Paper sx={{ p: 3, mt: 4 }}>\n          <Typography variant=\"h6\" gutterBottom>Report Speciali</Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n            Questi report richiedono parametri aggiuntivi per essere generati.\n          </Typography>\n\n          <Grid container spacing={3}>\n            {/* Bobina Specifica Report */}\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\">Report Bobina Specifica</Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    Dettaglio approfondito di una singola bobina con tutti i cavi associati.\n                  </Typography>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    fullWidth\n                    variant=\"outlined\"\n                    color=\"info\"\n                    onClick={() => {\n                      setDialogType('bobina-specifica');\n                      setOpenDialog(true);\n                    }}\n                  >\n                    Genera Report\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n\n            {/* Posa per Periodo Report */}\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\">Report Posa per Periodo</Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    Analisi temporale della posa con trend e pattern di lavoro.\n                  </Typography>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    fullWidth\n                    variant=\"outlined\"\n                    color=\"warning\"\n                    onClick={() => {\n                      setDialogType('posa-periodo');\n                      // Set default date range (last month to today)\n                      const today = new Date();\n                      const lastMonth = new Date();\n                      lastMonth.setMonth(today.getMonth() - 1);\n\n                      setFormData({\n                        ...formData,\n                        data_inizio: lastMonth.toISOString().split('T')[0],\n                        data_fine: today.toISOString().split('T')[0]\n                      });\n                      setOpenDialog(true);\n                    }}\n                  >\n                    Genera Report\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Display special reports if they exist */}\n        {reportsData.bobinaSpecifica && (\n          <Accordion defaultExpanded sx={{ mb: 2, mt: 2 }}>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <CableIcon sx={{ mr: 1, color: 'info.main' }} />\n                <Typography variant=\"h6\">Report Bobina Specifica</Typography>\n              </Box>\n            </AccordionSummary>\n            <AccordionDetails>\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobina-specifica', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobina-specifica', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBobinaSpecificaReport(reportsData.bobinaSpecifica)}\n              </Box>\n            </AccordionDetails>\n          </Accordion>\n        )}\n\n        {reportsData.posaPeriodo && (\n          <Accordion defaultExpanded sx={{ mb: 2, mt: 2 }}>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <TimelineIcon sx={{ mr: 1, color: 'warning.main' }} />\n                <Typography variant=\"h6\">Report Posa per Periodo</Typography>\n              </Box>\n            </AccordionSummary>\n            <AccordionDetails>\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('posa-periodo', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderPosaPeriodoReport(reportsData.posaPeriodo)}\n              </Box>\n            </AccordionDetails>\n          </Accordion>\n        )}\n      </Box>\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,eAAe,MAAM,yCAAyC;;AAErE;AACA,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,cAAc,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB;EAAW,CAAC,GAAGjB,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEkB;EAAK,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAE1B,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgF,KAAK,EAAEC,QAAQ,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkF,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACoF,cAAc,EAAEC,iBAAiB,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsF,UAAU,EAAEC,aAAa,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwF,UAAU,EAAEC,aAAa,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0F,QAAQ,EAAEC,WAAW,CAAC,GAAG3F,QAAQ,CAAC;IACvC4F,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjG,QAAQ,CAAC;IAC7CkG,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,IAAI;IACrBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMyG,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC3B,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAM4B,eAAe,GAAG7C,aAAa,CAAC8C,iBAAiB,CAAChC,UAAU,EAAE,OAAO,CAAC,CACzEiC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,gCAAgC,EAAE8B,GAAG,CAAC;UACpD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMC,UAAU,GAAGnD,aAAa,CAACoD,mBAAmB,CAACtC,UAAU,EAAE,OAAO,CAAC,CACtEiC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,2BAA2B,EAAE8B,GAAG,CAAC;UAC/C,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMG,aAAa,GAAGrD,aAAa,CAACsD,eAAe,CAACxC,UAAU,EAAE,OAAO,CAAC,CACrEiC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,8BAA8B,EAAE8B,GAAG,CAAC;UAClD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMK,gBAAgB,GAAGvD,aAAa,CAACwD,kBAAkB,CAAC1C,UAAU,EAAE,OAAO,CAAC,CAC3EiC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAAC/B,KAAK,CAAC,kCAAkC,EAAE8B,GAAG,CAAC;UACtD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;;QAEJ;QACA,MAAM,CAACO,YAAY,EAAEC,OAAO,EAAEC,UAAU,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3EjB,eAAe,EACfM,UAAU,EACVE,aAAa,EACbE,gBAAgB,CACjB,CAAC;;QAEF;QACApB,cAAc,CAAC;UACbC,QAAQ,EAAEqB,YAAY,CAACP,OAAO;UAC9Bb,GAAG,EAAEqB,OAAO,CAACR,OAAO;UACpBZ,MAAM,EAAEqB,UAAU,CAACT,OAAO;UAC1BX,SAAS,EAAEqB,aAAa,CAACV,OAAO;UAChCV,eAAe,EAAE,IAAI;UACrBC,WAAW,EAAE;QACf,CAAC,CAAC;;QAEF;QACA,IAAIgB,YAAY,CAACP,OAAO,IAAIQ,OAAO,CAACR,OAAO,IAAIS,UAAU,CAACT,OAAO,IAAIU,aAAa,CAACV,OAAO,EAAE;UAC1F/B,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,MAAM;UACLA,QAAQ,CAAC,uDAAuD,CAAC;QACnE;MACF,CAAC,CAAC,OAAO6B,GAAG,EAAE;QACZ;QACAC,OAAO,CAAC/B,KAAK,CAAC,mCAAmC,EAAE8B,GAAG,CAAC;QACvD7B,QAAQ,CAAC,uDAAuD,CAAC;MACnE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIH,UAAU,EAAE;MACd8B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC9B,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMiD,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,2FAA2F;IACxGC,IAAI,eAAE3D,OAAA,CAACvC,cAAc;MAAAmG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,yBAAyB;EACrH,CAAC,EACD;IACET,EAAE,EAAE,KAAK;IACTC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,wEAAwE;IACrFC,IAAI,eAAE3D,OAAA,CAAC/B,QAAQ;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,0BAA0B,EAAE,qBAAqB,EAAE,eAAe;EAC1G,CAAC,EACD;IACET,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,uEAAuE;IACpFC,IAAI,eAAE3D,OAAA,CAACjB,aAAa;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,iBAAiB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,yEAAyE;IACtFC,IAAI,eAAE3D,OAAA,CAACnB,SAAS;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,oBAAoB;EAC7F,CAAC,EACD;IACET,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,eAAE3D,OAAA,CAACjC,YAAY;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,mBAAmB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,iFAAiF;IAC9FC,IAAI,eAAE3D,OAAA,CAACrC,YAAY;MAAAiG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,2BAA2B,EAAE,eAAe,EAAE,kBAAkB;EAC/F,CAAC,CACF;;EAED;EACA,MAAMC,wBAAwB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,MAAM,KAAK;IAC7D,IAAI;MACF3D,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI0D,QAAQ;MAEZ,QAAQF,UAAU;QAChB,KAAK,UAAU;UACbE,QAAQ,GAAG,MAAM7E,aAAa,CAAC8C,iBAAiB,CAAChC,UAAU,EAAE8D,MAAM,CAAC;UACpE;QACF,KAAK,KAAK;UACRC,QAAQ,GAAG,MAAM7E,aAAa,CAACoD,mBAAmB,CAACtC,UAAU,EAAE8D,MAAM,CAAC;UACtE;QACF,KAAK,QAAQ;UACXC,QAAQ,GAAG,MAAM7E,aAAa,CAACsD,eAAe,CAACxC,UAAU,EAAE8D,MAAM,CAAC;UAClE;QACF,KAAK,YAAY;UACfC,QAAQ,GAAG,MAAM7E,aAAa,CAACwD,kBAAkB,CAAC1C,UAAU,EAAE8D,MAAM,CAAC;UACrE;QACF,KAAK,kBAAkB;UACrB,IAAI,CAAChD,QAAQ,CAACK,SAAS,EAAE;YACvBd,QAAQ,CAAC,8BAA8B,CAAC;YACxC;UACF;UACA0D,QAAQ,GAAG,MAAM7E,aAAa,CAAC8E,eAAe,CAAChE,UAAU,EAAEc,QAAQ,CAACK,SAAS,EAAE2C,MAAM,CAAC;UACtF;QACF,KAAK,cAAc;UACjB,IAAI,CAAChD,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDb,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACA0D,QAAQ,GAAG,MAAM7E,aAAa,CAAC+E,uBAAuB,CACpDjE,UAAU,EACVc,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClB4C,MACF,CAAC;UACD;QACF;UACE,MAAM,IAAII,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIJ,MAAM,KAAK,OAAO,EAAE;QACtB;QACA,IAAID,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,cAAc,EAAE;UACtExC,cAAc,CAAC8C,IAAI,KAAK;YACtB,GAAGA,IAAI;YACP,CAACN,UAAU,KAAK,kBAAkB,GAAG,iBAAiB,GAAG,aAAa,GAAGE,QAAQ,CAAC3B;UACpF,CAAC,CAAC,CAAC;QACL;QACA7B,aAAa,CAACwD,QAAQ,CAAC3B,OAAO,CAAC;MACjC,CAAC,MAAM;QACL;QACA,IAAI2B,QAAQ,CAACK,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAACP,QAAQ,CAACK,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;IACF,CAAC,CAAC,OAAOlC,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,sCAAsC,EAAE8B,GAAG,CAAC;MAC1D7B,QAAQ,CAAC6B,GAAG,CAACqC,MAAM,IAAIrC,GAAG,CAACsC,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACRrE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsE,kBAAkB,GAAIZ,UAAU,IAAK;IACzCpD,iBAAiB,CAACoD,UAAU,CAAC;IAC7BhD,aAAa,CAACgD,UAAU,CAACX,EAAE,CAAC;;IAE5B;IACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,IAAIW,UAAU,CAACX,EAAE,KAAK,kBAAkB,EAAE;MAC5E;MACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,EAAE;QACpC,MAAMwB,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;QACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;QAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAExC/D,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXG,WAAW,EAAE2D,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAClD9D,SAAS,EAAEwD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC;MACJ;MAEArE,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACL;MACAiD,wBAAwB,CAACC,UAAU,CAACX,EAAE,EAAE,OAAO,CAAC;IAClD;EACF,CAAC;EAED,MAAM+B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMrB,wBAAwB,CAAChD,UAAU,EAAEE,QAAQ,CAACE,OAAO,CAAC;IAC5DL,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMuE,iBAAiB,GAAGA,CAAA,KAAM;IAC9BvE,aAAa,CAAC,KAAK,CAAC;IACpBN,QAAQ,CAAC,IAAI,CAAC;IACdU,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgE,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC7E,UAAU,EAAE,OAAO,IAAI;IAE5B,oBACEZ,OAAA,CAAClE,KAAK;MAAC4J,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzB7F,OAAA,CAACpE,GAAG;QAAC8J,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzF7F,OAAA,CAACnE,UAAU;UAACqK,OAAO,EAAC,IAAI;UAAAL,QAAA,GACrB/E,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE2C,KAAK,EAAC,KAAG,EAAC7C,UAAU,CAACuF,aAAa;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACb/D,OAAA,CAACpE,GAAG;UAAC8J,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE;UAAE,CAAE;UAAAP,QAAA,gBAEnC7F,OAAA,CAAC7D,MAAM;YACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAChD,UAAU,EAAE,KAAK,CAAE;YAC3DgF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZvC,KAAK,EAAC,SAAS;YAAA6B,QAAA,EAChB;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;YACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAChD,UAAU,EAAE,OAAO,CAAE;YAC7DgF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZvC,KAAK,EAAC,SAAS;YAAA6B,QAAA,EAChB;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;YACLkK,SAAS,eAAErG,OAAA,CAACzB,WAAW;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BuC,OAAO,EAAEA,CAAA,KAAMzF,aAAa,CAAC,IAAI,CAAE;YACnCqF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YAAAV,QAAA,EACb;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/D,OAAA,CAACzD,OAAO;QAACmJ,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE;MAAE;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGzB7C,UAAU,KAAK,UAAU,IAAIsF,oBAAoB,CAAC5F,UAAU,CAAC,EAC7DM,UAAU,KAAK,KAAK,IAAIuF,eAAe,CAAC7F,UAAU,CAAC,EACnDM,UAAU,KAAK,QAAQ,IAAIwF,kBAAkB,CAAC9F,UAAU,CAAC,EACzDM,UAAU,KAAK,kBAAkB,IAAIyF,2BAA2B,CAAC/F,UAAU,CAAC,EAC5EM,UAAU,KAAK,cAAc,IAAI0F,uBAAuB,CAAChG,UAAU,CAAC,EACpEM,UAAU,KAAK,YAAY,IAAI2F,qBAAqB,CAACjG,UAAU,CAAC;IAAA;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC;EAEZ,CAAC;EAED,MAAMyC,oBAAoB,GAAIM,IAAI,iBAChC9G,OAAA,CAACpE,GAAG;IAAAiK,QAAA,gBAEF7F,OAAA,CAACpE,GAAG;MAAC8J,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF7F,OAAA,CAACnE,UAAU;QAACqK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEqB,UAAU,EAAE,GAAG;UAAE/C,KAAK,EAAE;QAAe,CAAE;QAAA6B,QAAA,GAAC,uBAClD,EAACiB,IAAI,CAACX,aAAa,IAAI,UAAU;MAAA;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACb/D,OAAA,CAACzC,gBAAgB;QACfyJ,OAAO,eACLhH,OAAA,CAAC1C,MAAM;UACL2J,OAAO,EAAE/E,UAAW;UACpBgF,QAAQ,EAAGC,CAAC,IAAKhF,aAAa,CAACgF,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDjD,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDsD,KAAK,eACHrH,OAAA,CAACpE,GAAG;UAAC8J,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjD7F,OAAA,CAACb,aAAa;YAACuG,EAAE,EAAE;cAAE4B,EAAE,EAAE;YAAE;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN/D,OAAA,CAACjE,IAAI;MAACwL,SAAS;MAACC,OAAO,EAAE,CAAE;MAAC9B,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxC7F,OAAA,CAACjE,IAAI;QAAC0L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACvB7F,OAAA,CAAClE,KAAK;UAAC4J,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEiC,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE,cAAc;YAAE7D,KAAK,EAAE;UAAQ,CAAE;UAAA6B,QAAA,gBAChF7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEqB,UAAU,EAAE,MAAM;cAAEd,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxDiB,IAAI,CAACgB,YAAY,EAAC,GACrB;UAAA;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/D,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAY;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACP/D,OAAA,CAACjE,IAAI;QAAC0L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACvB7F,OAAA,CAAClE,KAAK;UAAC4J,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEiC,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE,cAAc;YAAE7D,KAAK,EAAE;UAAQ,CAAE;UAAA6B,QAAA,gBAChF7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEqB,UAAU,EAAE,MAAM;cAAEd,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxDiB,IAAI,CAACiB,YAAY,EAAC,GACrB;UAAA;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/D,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAY;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrD/D,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,SAAS;YAAAL,QAAA,GAAC,GAAC,EAACiB,IAAI,CAACkB,uBAAuB,EAAC,IAAE;UAAA;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACP/D,OAAA,CAACjE,IAAI;QAAC0L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACvB7F,OAAA,CAAClE,KAAK;UAAC4J,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEiC,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE,cAAc;YAAE7D,KAAK,EAAE;UAAQ,CAAE;UAAA6B,QAAA,gBAChF7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEqB,UAAU,EAAE,MAAM;cAAEd,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxDiB,IAAI,CAACmB,eAAe,EAAC,GACxB;UAAA;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/D,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAe;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxD/D,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,SAAS;YAAAL,QAAA,GAAC,GAAC,EAAC,GAAG,GAAGiB,IAAI,CAACkB,uBAAuB,EAAC,IAAE;UAAA;YAAApE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACP/D,OAAA,CAACjE,IAAI;QAAC0L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACvB7F,OAAA,CAAClE,KAAK;UAAC4J,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEiC,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE,WAAW;YAAE7D,KAAK,EAAE;UAAQ,CAAE;UAAA6B,QAAA,gBAC7E7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEqB,UAAU,EAAE,MAAM;cAAEd,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxDiB,IAAI,CAACoB,iBAAiB,EAAC,GAC1B;UAAA;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/D,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAY;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACpD+C,IAAI,CAACqB,cAAc,iBAClBnI,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,SAAS;YAAAL,QAAA,GAAC,GAAC,EAACiB,IAAI,CAACqB,cAAc,EAAC,kBAAgB;UAAA;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACjF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGN7B,UAAU,iBACTlC,OAAA,CAACpE,GAAG;MAAC8J,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACjB7F,OAAA,CAACN,aAAa;QAACoH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGD/D,OAAA,CAACjE,IAAI;MAACwL,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA3B,QAAA,gBAEzB7F,OAAA,CAACjE,IAAI;QAAC0L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACvB7F,OAAA,CAAClE,KAAK;UAAC4J,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,gBAClB7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEO,EAAE,EAAE,CAAC;cAAEc,UAAU,EAAE;YAAI,CAAE;YAAAlB,QAAA,EAAC;UAEzD;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/D,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,eAAe;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACnE7F,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,OAAO;cAAAL,QAAA,EAAC;YAAY;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,OAAO;cAACR,EAAE,EAAE;gBAAEqB,UAAU,EAAE;cAAI,CAAE;cAAAlB,QAAA,EAAEiB,IAAI,CAACsB;YAAW;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjF,CAAC,eACN/D,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,eAAe;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACnE7F,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,OAAO;cAAAL,QAAA,EAAC;YAAY;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,OAAO;cAACR,EAAE,EAAE;gBAAEqB,UAAU,EAAE,GAAG;gBAAE/C,KAAK,EAAE;cAAe,CAAE;cAAA6B,QAAA,GACxEiB,IAAI,CAACuB,WAAW,EAAC,IAAE,EAACvB,IAAI,CAACwB,gBAAgB,EAAC,IAC7C;YAAA;cAAA1E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN/D,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE;YAAgB,CAAE;YAAAF,QAAA,gBAC5D7F,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,OAAO;cAAAL,QAAA,EAAC;YAAe;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,OAAO;cAACR,EAAE,EAAE;gBAAEqB,UAAU,EAAE,GAAG;gBAAE/C,KAAK,EAAE;cAAe,CAAE;cAAA6B,QAAA,GACxEiB,IAAI,CAACyB,cAAc,EAAC,IAAE,EAAC,GAAG,GAAGzB,IAAI,CAACwB,gBAAgB,EAAC,IACtD;YAAA;cAAA1E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP/D,OAAA,CAACjE,IAAI;QAAC0L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACvB7F,OAAA,CAAClE,KAAK;UAAC4J,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,gBAClB7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEO,EAAE,EAAE,CAAC;cAAEc,UAAU,EAAE;YAAI,CAAE;YAAAlB,QAAA,EAAC;UAEzD;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/D,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,eAAe;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACnE7F,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,OAAO;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3D/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,OAAO;cAACR,EAAE,EAAE;gBAAEqB,UAAU,EAAE;cAAI,CAAE;cAAAlB,QAAA,GAAEiB,IAAI,CAACoB,iBAAiB,EAAC,UAAQ;YAAA;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC,EACL+C,IAAI,CAACqB,cAAc,iBAClBnI,OAAA,CAAAE,SAAA;YAAA2F,QAAA,gBACE7F,OAAA,CAACpE,GAAG;cAAC8J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACnE7F,OAAA,CAACnE,UAAU;gBAACqK,OAAO,EAAC,OAAO;gBAAAL,QAAA,EAAC;cAAe;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxD/D,OAAA,CAACnE,UAAU;gBAACqK,OAAO,EAAC,OAAO;gBAACR,EAAE,EAAE;kBAAEqB,UAAU,EAAE;gBAAI,CAAE;gBAAAlB,QAAA,GAAEiB,IAAI,CAACqB,cAAc,EAAC,SAAO;cAAA;gBAAAvE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC,eACN/D,OAAA,CAACpE,GAAG;cAAC8J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAgB,CAAE;cAAAF,QAAA,gBAC5D7F,OAAA,CAACnE,UAAU;gBAACqK,OAAO,EAAC,OAAO;gBAAAL,QAAA,EAAC;cAAmB;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5D/D,OAAA,CAACnE,UAAU;gBAACqK,OAAO,EAAC,OAAO;gBAACR,EAAE,EAAE;kBAAEqB,UAAU,EAAE;gBAAI,CAAE;gBAAAlB,QAAA,EAAEiB,IAAI,CAAC0B;cAAkB;gBAAA5E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC;UAAA,eACN,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAGN+C,IAAI,CAAC2B,YAAY,IAAI3B,IAAI,CAAC2B,YAAY,CAACC,MAAM,GAAG,CAAC,iBAChD1I,OAAA,CAACjE,IAAI;QAAC0L,IAAI;QAACC,EAAE,EAAE,EAAG;QAAA7B,QAAA,eAChB7F,OAAA,CAAClE,KAAK;UAAC4J,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,gBAClB7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEO,EAAE,EAAE,CAAC;cAAEc,UAAU,EAAE;YAAI,CAAE;YAAAlB,QAAA,EAAC;UAEzD;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/D,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAAC2B,YAAY,CAACE,GAAG,CAACC,IAAI,KAAK;cACnC9B,IAAI,EAAE8B,IAAI,CAAC9B,IAAI;cACf+B,KAAK,EAAE,GAAGD,IAAI,CAACC,KAAK;YACtB,CAAC,CAAC,CAAE;YACJC,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAI,CAAC,EACjD;cAAEF,KAAK,EAAE,OAAO;cAAEC,UAAU,EAAE,cAAc;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAQ,CAAC,CAC1E;YACFC,UAAU,EAAErC,IAAI,CAAC2B,YAAY,CAACC,MAAM,GAAG,CAAE;YACzCU,QAAQ,EAAE;UAAE;YAAAxF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAED,MAAM0C,eAAe,GAAIK,IAAI,iBAC3B9G,OAAA,CAACjE,IAAI;IAACwL,SAAS;IAACC,OAAO,EAAE,CAAE;IAAA3B,QAAA,GAExB3D,UAAU,iBACTlC,OAAA,CAACjE,IAAI;MAAC0L,IAAI;MAACC,EAAE,EAAE,EAAG;MAAA7B,QAAA,eAChB7F,OAAA,CAACJ,QAAQ;QAACkH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CACP,eAGD/D,OAAA,CAACjE,IAAI;MAAC0L,IAAI;MAACC,EAAE,EAAE,EAAG;MAAA7B,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACkM,eAAe;QAAAxD,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACkM,UAAU,eAAEtJ,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAkB;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAACyC,aAAa,IAAI,EAAG;YAC/BT,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,WAAW;cAAEC,UAAU,EAAE,WAAW;cAAEC,KAAK,EAAE;YAAI,CAAC,EAC3D;cAAEF,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,UAAU;cAAEC,UAAU,EAAE,MAAM;cAAEC,KAAK,EAAE,EAAE;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE;YAAS,CAAC,EACxF;cAAET,KAAK,EAAE,eAAe;cAAEC,UAAU,EAAE,eAAe;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE,QAAQ;cACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;YAAI,CAAC,EAChD;cAAEZ,KAAK,EAAE,aAAa;cAAEC,UAAU,EAAE,aAAa;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE,QAAQ;cAC/FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;YAAI,CAAC,EAC9C;cAAEb,KAAK,EAAE,iBAAiB;cAAEC,UAAU,EAAE,WAAW;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE,QAAQ;cACjGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACzB,eAAe;YAAI,CAAC;UAClD;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGP/D,OAAA,CAACjE,IAAI;MAAC0L,IAAI;MAACC,EAAE,EAAE,EAAG;MAAA7B,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACkM,eAAe;QAAAxD,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACkM,UAAU,eAAEtJ,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAkB;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAAC+C,eAAe,IAAI,EAAG;YACjCf,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,WAAW;cAAEC,UAAU,EAAE,WAAW;cAAEC,KAAK,EAAE;YAAI,CAAC,EAC3D;cAAEF,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,YAAY;cAAEC,UAAU,EAAE,QAAQ;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE;YAAS,CAAC,EAC7F;cAAET,KAAK,EAAE,mBAAmB;cAAEC,UAAU,EAAE,mBAAmB;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE,QAAQ;cAC3GC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACI,iBAAiB;YAAI,CAAC;UACpD;YAAAlG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;EAED,MAAM2C,kBAAkB,GAAII,IAAI,iBAC9B9G,OAAA,CAACjE,IAAI;IAACwL,SAAS;IAACC,OAAO,EAAE,CAAE;IAAA3B,QAAA,GAExB3D,UAAU,iBACTlC,OAAA,CAACjE,IAAI;MAAC0L,IAAI;MAACC,EAAE,EAAE,EAAG;MAAA7B,QAAA,eAChB7F,OAAA,CAACL,WAAW;QAACmH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CACP,eAGD/D,OAAA,CAACjE,IAAI;MAAC0L,IAAI;MAACC,EAAE,EAAE,EAAG;MAAA7B,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACkM,eAAe;QAAAxD,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACkM,UAAU,eAAEtJ,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,GAAC,uBACF,EAACiB,IAAI,CAACiD,aAAa,EAAC,UAC3C;UAAA;YAAAnG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAAChF,MAAM,IAAI,EAAG;YACxBgH,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,WAAW;cAAEC,UAAU,EAAE,WAAW;cAAEC,KAAK,EAAE;YAAI,CAAC,EAC3D;cAAEF,KAAK,EAAE,WAAW;cAAEC,UAAU,EAAE,WAAW;cAAEC,KAAK,EAAE;YAAI,CAAC,EAC3D;cAAEF,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,OAAO;cAAEC,UAAU,EAAE,OAAO;cAAEC,KAAK,EAAE,GAAG;cAC/CQ,UAAU,EAAGC,GAAG,iBACd1J,OAAA,CAAC5D,IAAI;gBACHiL,KAAK,EAAEqC,GAAG,CAACM,KAAM;gBACjBhG,KAAK,EAAE0F,GAAG,CAACM,KAAK,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;gBAC3DzD,IAAI,EAAC;cAAO;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAEL,CAAC,EACD;cAAEgF,KAAK,EAAE,cAAc;cAAEC,UAAU,EAAE,cAAc;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE,QAAQ;cACjGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAAC5B,YAAY;YAAI,CAAC,EAC/C;cAAEiB,KAAK,EAAE,eAAe;cAAEC,UAAU,EAAE,eAAe;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE,QAAQ;cACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACO,aAAa;YAAI,CAAC,EAChD;cAAElB,KAAK,EAAE,kBAAkB;cAAEC,UAAU,EAAE,kBAAkB;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE,QAAQ;cACzGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACQ,gBAAgB;YAAI,CAAC,EACnD;cAAEnB,KAAK,EAAE,sBAAsB;cAAEC,UAAU,EAAE,UAAU;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE,QAAQ;cACrGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACS,oBAAoB;YAAI,CAAC;UACvD;YAAAvG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;EAED,MAAM4C,2BAA2B,GAAIG,IAAI,iBACvC9G,OAAA,CAACjE,IAAI;IAACwL,SAAS;IAACC,OAAO,EAAE,CAAE;IAAA3B,QAAA,gBAEzB7F,OAAA,CAACjE,IAAI;MAAC0L,IAAI;MAACC,EAAE,EAAE,EAAG;MAAA7B,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACkM,eAAe;QAAAxD,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACkM,UAAU,eAAEtJ,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAe;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAACsD,MAAM,GAAG,CAClB;cACEC,SAAS,EAAE,WAAW;cACtBC,MAAM,EAAExD,IAAI,CAACsD,MAAM,CAAC3I;YACtB,CAAC,EACD;cACE4I,SAAS,EAAE,WAAW;cACtBC,MAAM,EAAExD,IAAI,CAACsD,MAAM,CAACG;YACtB,CAAC,EACD;cACEF,SAAS,EAAE,SAAS;cACpBC,MAAM,EAAExD,IAAI,CAACsD,MAAM,CAACI;YACtB,CAAC,EACD;cACEH,SAAS,EAAE,OAAO;cAClBC,MAAM,EAAExD,IAAI,CAACsD,MAAM,CAACJ,KAAK;cACzBS,aAAa,EAAE;YACjB,CAAC,EACD;cACEJ,SAAS,EAAE,cAAc;cACzBC,MAAM,EAAE,GAAGxD,IAAI,CAACsD,MAAM,CAACtC,YAAY;YACrC,CAAC,EACD;cACEuC,SAAS,EAAE,eAAe;cAC1BC,MAAM,EAAE,GAAGxD,IAAI,CAACsD,MAAM,CAACH,aAAa;YACtC,CAAC,EACD;cACEI,SAAS,EAAE,kBAAkB;cAC7BC,MAAM,EAAE,GAAGxD,IAAI,CAACsD,MAAM,CAACF,gBAAgB;YACzC,CAAC,EACD;cACEG,SAAS,EAAE,sBAAsB;cACjCC,MAAM,EAAE,GAAGxD,IAAI,CAACsD,MAAM,CAACD,oBAAoB;YAC7C,CAAC,CACF,GAAG,EAAG;YACPrB,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,WAAW;cAAEC,UAAU,EAAE,WAAW;cAAEC,KAAK,EAAE;YAAI,CAAC,EAC3D;cAAEF,KAAK,EAAE,QAAQ;cAAEC,UAAU,EAAE,QAAQ;cAAEC,KAAK,EAAE,GAAG;cACjDQ,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACe,aAAa,gBACpCzK,OAAA,CAAC5D,IAAI;gBACHiL,KAAK,EAAEqC,GAAG,CAACY,MAAO;gBAClBtG,KAAK,EAAE0F,GAAG,CAACY,MAAM,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;gBAC5D/D,IAAI,EAAC;cAAO;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,GACA2F,GAAG,CAACY;YACV,CAAC,CACD;YACFnB,UAAU,EAAE;UAAM;YAAAvF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGP/D,OAAA,CAACjE,IAAI;MAAC0L,IAAI;MAACC,EAAE,EAAE,EAAG;MAAA7B,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACkM,eAAe;QAAAxD,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACkM,UAAU,eAAEtJ,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,GAAC,kBACP,EAACiB,IAAI,CAACsB,WAAW,EAAC,GACpC;UAAA;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAAC4D,cAAc,IAAI,EAAG;YAChC5B,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAI,CAAC,EACvD;cAAEF,KAAK,EAAE,WAAW;cAAEC,UAAU,EAAE,WAAW;cAAEC,KAAK,EAAE;YAAI,CAAC,EAC3D;cAAEF,KAAK,EAAE,eAAe;cAAEC,UAAU,EAAE,eAAe;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE,QAAQ;cACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;YAAI,CAAC,EAChD;cAAEZ,KAAK,EAAE,aAAa;cAAEC,UAAU,EAAE,aAAa;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE,QAAQ;cAC/FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;YAAI,CAAC,EAC9C;cAAEb,KAAK,EAAE,OAAO;cAAEC,UAAU,EAAE,OAAO;cAAEC,KAAK,EAAE,GAAG;cAC/CQ,UAAU,EAAGC,GAAG,iBACd1J,OAAA,CAAC5D,IAAI;gBACHiL,KAAK,EAAEqC,GAAG,CAACM,KAAM;gBACjBhG,KAAK,EAAE0F,GAAG,CAACM,KAAK,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAU;gBACtDzD,IAAI,EAAC;cAAO;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAEL,CAAC;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;EAED,MAAM6C,uBAAuB,GAAIE,IAAI,iBACnC9G,OAAA,CAACjE,IAAI;IAACwL,SAAS;IAACC,OAAO,EAAE,CAAE;IAAA3B,QAAA,GAExB3D,UAAU,iBACTlC,OAAA,CAACjE,IAAI;MAAC0L,IAAI;MAACC,EAAE,EAAE,EAAG;MAAA7B,QAAA,eAChB7F,OAAA,CAACH,aAAa;QAACiH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CACP,eAGD/D,OAAA,CAACjE,IAAI;MAAC0L,IAAI;MAACC,EAAE,EAAE,EAAG;MAAA7B,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACkM,eAAe;QAAAxD,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACkM,UAAU,eAAEtJ,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAmB;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAE,CACJ;cACE6D,UAAU,EAAE,SAAS;cACrBL,MAAM,EAAE,GAAGxD,IAAI,CAACvF,WAAW,MAAMuF,IAAI,CAACtF,SAAS;YACjD,CAAC,EACD;cACEmJ,UAAU,EAAE,cAAc;cAC1BL,MAAM,EAAE,GAAGxD,IAAI,CAAC8D,oBAAoB;YACtC,CAAC,EACD;cACED,UAAU,EAAE,eAAe;cAC3BL,MAAM,EAAExD,IAAI,CAAC+D;YACf,CAAC,EACD;cACEF,UAAU,EAAE,mBAAmB;cAC/BL,MAAM,EAAE,GAAGxD,IAAI,CAACoB,iBAAiB;YACnC,CAAC,CACD;YACFY,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,YAAY;cAAEC,UAAU,EAAE,YAAY;cAAEC,KAAK,EAAE;YAAI,CAAC,EAC7D;cAAEF,KAAK,EAAE,QAAQ;cAAEC,UAAU,EAAE,QAAQ;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAQ,CAAC,CACrE;YACFC,UAAU,EAAE;UAAM;YAAAvF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGP/D,OAAA,CAACjE,IAAI;MAAC0L,IAAI;MAACC,EAAE,EAAE,EAAG;MAAA7B,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACkM,eAAe;QAAAxD,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACkM,UAAU,eAAEtJ,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAAgB;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAACgE,gBAAgB,IAAI,EAAG;YAClChC,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,MAAM;cAAEC,UAAU,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAI,CAAC,EACjD;cAAEF,KAAK,EAAE,OAAO;cAAEC,UAAU,EAAE,cAAc;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE,QAAQ;cAC1FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACb,KAAK;YAAI,CAAC;UACxC;YAAAjF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;EAED,MAAM8C,qBAAqB,GAAIC,IAAI,iBACjC9G,OAAA,CAACjE,IAAI;IAACwL,SAAS;IAACC,OAAO,EAAE,CAAE;IAAA3B,QAAA,GAExB3D,UAAU,iBACTlC,OAAA,CAACjE,IAAI;MAAC0L,IAAI;MAACC,EAAE,EAAE,EAAG;MAAA7B,QAAA,eAChB7F,OAAA,CAACF,cAAc;QAACgH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACP,eAGD/D,OAAA,CAACjE,IAAI;MAAC0L,IAAI;MAACC,EAAE,EAAE,EAAG;MAAA7B,QAAA,eAChB7F,OAAA,CAAC7C,SAAS;QAACkM,eAAe;QAAAxD,QAAA,gBACxB7F,OAAA,CAAC5C,gBAAgB;UAACkM,UAAU,eAAEtJ,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACnE,UAAU;YAACqK,OAAO,EAAC,IAAI;YAAAL,QAAA,EAAC;UAA+B;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACP,eAAe;YACdqH,IAAI,EAAEA,IAAI,CAACiE,cAAc,IAAI,EAAG;YAChCjC,OAAO,EAAE,CACP;cAAEC,KAAK,EAAE,OAAO;cAAEC,UAAU,EAAE,OAAO;cAAEC,KAAK,EAAE,GAAG;cAC/CQ,UAAU,EAAGC,GAAG,iBACd1J,OAAA,CAAC5D,IAAI;gBACHiL,KAAK,EAAEqC,GAAG,CAACM,KAAM;gBACjBhG,KAAK,EAAE0F,GAAG,CAACM,KAAK,KAAK,YAAY,GAAG,SAAS,GAAG,SAAU;gBAC1DzD,IAAI,EAAC;cAAO;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAEL,CAAC,EACD;cAAEgF,KAAK,EAAE,UAAU;cAAEC,UAAU,EAAE,aAAa;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE;YAAS,CAAC,EAChG;cAAET,KAAK,EAAE,eAAe;cAAEC,UAAU,EAAE,eAAe;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE,QAAQ;cACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;YAAI,CAAC,EAChD;cAAEZ,KAAK,EAAE,aAAa;cAAEC,UAAU,EAAE,aAAa;cAAEC,KAAK,EAAE,GAAG;cAAEC,KAAK,EAAE,OAAO;cAAEM,QAAQ,EAAE,QAAQ;cAC/FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;YAAI,CAAC,CAC9C;YACFT,UAAU,EAAE;UAAM;YAAAvF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACc,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACP;EAED,MAAMiH,YAAY,GAAGA,CAAA,kBACnBhL,OAAA,CAACtD,MAAM;IAACkI,IAAI,EAAE5D,UAAW;IAACiK,OAAO,EAAEzF,iBAAkB;IAAC0F,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAtF,QAAA,gBAC3E7F,OAAA,CAACrD,WAAW;MAAAkJ,QAAA,EACT/E,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE2C;IAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACd/D,OAAA,CAACpD,aAAa;MAAAiJ,QAAA,GACXnF,KAAK,iBACJV,OAAA,CAAC3D,KAAK;QAAC+O,QAAQ,EAAC,OAAO;QAAC1F,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EACnCnF;MAAK;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAED/D,OAAA,CAACjE,IAAI;QAACwL,SAAS;QAACC,OAAO,EAAE,CAAE;QAAC9B,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACxC7F,OAAA,CAACjE,IAAI;UAAC0L,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA7B,QAAA,eAChB7F,OAAA,CAAClD,WAAW;YAACqO,SAAS;YAAAtF,QAAA,gBACpB7F,OAAA,CAACjD,UAAU;cAAA8I,QAAA,EAAC;YAAO;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChC/D,OAAA,CAAChD,MAAM;cACLqO,KAAK,EAAEjK,QAAQ,CAACE,OAAQ;cACxB+F,KAAK,EAAC,SAAS;cACfH,QAAQ,EAAGC,CAAC,IAAK9F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAE6F,CAAC,CAACC,MAAM,CAACiE;cAAM,CAAC,CAAE;cAAAxF,QAAA,gBAEvE7F,OAAA,CAAC/C,QAAQ;gBAACoO,KAAK,EAAC,OAAO;gBAAAxF,QAAA,EAAC;cAAoB;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvD/D,OAAA,CAAC/C,QAAQ;gBAACoO,KAAK,EAAC,KAAK;gBAAAxF,QAAA,EAAC;cAAY;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7C/D,OAAA,CAAC/C,QAAQ;gBAACoO,KAAK,EAAC,OAAO;gBAAAxF,QAAA,EAAC;cAAc;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAEN7C,UAAU,KAAK,kBAAkB,iBAChClB,OAAA,CAACjE,IAAI;UAAC0L,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA7B,QAAA,eAChB7F,OAAA,CAAC9C,SAAS;YACRiO,SAAS;YACT9D,KAAK,EAAC,WAAW;YACjBgE,KAAK,EAAEjK,QAAQ,CAACK,SAAU;YAC1ByF,QAAQ,EAAGC,CAAC,IAAK9F,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEK,SAAS,EAAE0F,CAAC,CAACC,MAAM,CAACiE;YAAM,CAAC,CAAE;YACzEC,WAAW,EAAC,mBAAmB;YAC/BC,UAAU,EAAC;UAA0D;YAAA3H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP,EAEA7C,UAAU,KAAK,cAAc,iBAC5BlB,OAAA,CAAAE,SAAA;UAAA2F,QAAA,gBACE7F,OAAA,CAACjE,IAAI;YAAC0L,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACf7F,OAAA,CAAC9C,SAAS;cACRiO,SAAS;cACTK,IAAI,EAAC,MAAM;cACXnE,KAAK,EAAC,aAAa;cACnBgE,KAAK,EAAEjK,QAAQ,CAACG,WAAY;cAC5B2F,QAAQ,EAAGC,CAAC,IAAK9F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAE4F,CAAC,CAACC,MAAM,CAACiE;cAAM,CAAC,CAAE;cAC3EI,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA9H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP/D,OAAA,CAACjE,IAAI;YAAC0L,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACf7F,OAAA,CAAC9C,SAAS;cACRiO,SAAS;cACTK,IAAI,EAAC,MAAM;cACXnE,KAAK,EAAC,WAAW;cACjBgE,KAAK,EAAEjK,QAAQ,CAACI,SAAU;cAC1B0F,QAAQ,EAAGC,CAAC,IAAK9F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAE2F,CAAC,CAACC,MAAM,CAACiE;cAAM,CAAC,CAAE;cACzEI,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA9H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChB/D,OAAA,CAACnD,aAAa;MAAAgJ,QAAA,gBACZ7F,OAAA,CAAC7D,MAAM;QAACmK,OAAO,EAAEd,iBAAkB;QAAAK,QAAA,EAAC;MAAO;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpD/D,OAAA,CAAC7D,MAAM;QACLmK,OAAO,EAAEf,oBAAqB;QAC9BW,OAAO,EAAC,WAAW;QACnByF,QAAQ,EAAEnL,OAAQ;QAClB6F,SAAS,EAAE7F,OAAO,gBAAGR,OAAA,CAAC1D,gBAAgB;UAACiK,IAAI,EAAE;QAAG;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG/D,OAAA,CAAC3B,cAAc;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAA8B,QAAA,EAExErF,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACE/D,OAAA,CAACpE,GAAG;IAAC8J,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAE,QAAA,gBAEhB7F,OAAA,CAACpE,GAAG;MAAC8J,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF7F,OAAA,CAACpE,GAAG;QAAC8J,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEI,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACzD7F,OAAA,CAACxD,UAAU;UAAC8J,OAAO,EAAEA,CAAA,KAAMjG,QAAQ,CAAC,CAAC,CAAC,CAAE;UAAC2D,KAAK,EAAC,SAAS;UAAA6B,QAAA,eACtD7F,OAAA,CAACvB,aAAa;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACb/D,OAAA,CAACnE,UAAU;UAACqK,OAAO,EAAC,IAAI;UAAC0F,SAAS,EAAC,IAAI;UAAA/F,QAAA,EAAC;QAExC;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN/D,OAAA,CAACT,eAAe;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGLvD,OAAO,iBACNR,OAAA,CAACpE,GAAG;MAAC8J,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAE8F,EAAE,EAAE;MAAE,CAAE;MAAAhG,QAAA,eAC5D7F,OAAA,CAAC1D,gBAAgB;QAAAsH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGD/D,OAAA,CAACpE,GAAG;MAAC8J,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAEjB7F,OAAA,CAAC7C,SAAS;QAACkM,eAAe;QAAC3D,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC7F,OAAA,CAAC5C,gBAAgB;UAACkM,UAAU,eAAEtJ,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAACvC,cAAc;cAACiI,EAAE,EAAE;gBAAE4B,EAAE,EAAE,CAAC;gBAAEtD,KAAK,EAAE;cAAe;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,EACdnE,WAAW,CAACE,QAAQ,gBACnB5B,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAACpE,GAAG;cAAC8J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACzF7F,OAAA,CAACzC,gBAAgB;gBACfyJ,OAAO,eACLhH,OAAA,CAAC1C,MAAM;kBACL2J,OAAO,EAAE/E,UAAW;kBACpBgF,QAAQ,EAAGC,CAAC,IAAKhF,aAAa,CAACgF,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;kBACjDjD,KAAK,EAAC;gBAAS;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CACF;gBACDsD,KAAK,eACHrH,OAAA,CAACpE,GAAG;kBAAC8J,EAAE,EAAE;oBAAEI,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAH,QAAA,gBACjD7F,OAAA,CAACb,aAAa;oBAACuG,EAAE,EAAE;sBAAE4B,EAAE,EAAE;oBAAE;kBAAE;oBAAA1D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,kBAElC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF/D,OAAA,CAACpE,GAAG;gBAAAiK,QAAA,gBACF7F,OAAA,CAAC7D,MAAM;kBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,UAAU,EAAE,KAAK,CAAE;kBAC3DgC,OAAO,EAAC,UAAU;kBAClBK,IAAI,EAAC,OAAO;kBACZvC,KAAK,EAAC,SAAS;kBACf0B,EAAE,EAAE;oBAAE4B,EAAE,EAAE;kBAAE,CAAE;kBAAAzB,QAAA,EACf;gBAED;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;kBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAE;kBAC7DgC,OAAO,EAAC,UAAU;kBAClBK,IAAI,EAAC,OAAO;kBACZvC,KAAK,EAAC,SAAS;kBAAA6B,QAAA,EAChB;gBAED;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLyC,oBAAoB,CAAC9E,WAAW,CAACE,QAAQ,CAAC;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,GACJvD,OAAO,gBACTR,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE6F,EAAE,EAAE;YAAE,CAAE;YAAAhG,QAAA,gBACxD7F,OAAA,CAAC1D,gBAAgB;cAACiK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B/D,OAAA,CAACnE,UAAU;cAAC6J,EAAE,EAAE;gBAAEoG,EAAE,EAAE;cAAE,CAAE;cAAAjG,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN/D,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAAC3D,KAAK;cAAC+O,QAAQ,EAAC,OAAO;cAAC1F,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA,CAAC7D,MAAM;cACL+J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAErG,OAAA,CAACzB,WAAW;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb7F,UAAU,CAAC,IAAI,CAAC;gBAChBjB,aAAa,CAAC8C,iBAAiB,CAAChC,UAAU,EAAE,OAAO,CAAC,CACjDyL,IAAI,CAACjF,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP7C,QAAQ,EAAEkF,IAAI,CAACpE;kBACjB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC/B,KAAK,CAAC,iCAAiC,EAAE8B,GAAG,CAAC;gBACvD,CAAC,CAAC,CACDwJ,OAAO,CAAC,MAAM;kBACbvL,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAoF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ/D,OAAA,CAAC7C,SAAS;QAACkM,eAAe;QAAC3D,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC7F,OAAA,CAAC5C,gBAAgB;UAACkM,UAAU,eAAEtJ,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAAC/B,QAAQ;cAACyH,EAAE,EAAE;gBAAE4B,EAAE,EAAE,CAAC;gBAAEtD,KAAK,EAAE;cAAiB;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAkB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,EACdnE,WAAW,CAACG,GAAG,gBACd7B,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAACpE,GAAG;cAAC8J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAE;gBACtDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE4B,EAAE,EAAE;gBAAE,CAAE;gBAAAzB,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAE;gBACxDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL0C,eAAe,CAAC/E,WAAW,CAACG,GAAG,CAAC;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,GACJvD,OAAO,gBACTR,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE6F,EAAE,EAAE;YAAE,CAAE;YAAAhG,QAAA,gBACxD7F,OAAA,CAAC1D,gBAAgB;cAACiK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B/D,OAAA,CAACnE,UAAU;cAAC6J,EAAE,EAAE;gBAAEoG,EAAE,EAAE;cAAE,CAAE;cAAAjG,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN/D,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAAC3D,KAAK;cAAC+O,QAAQ,EAAC,OAAO;cAAC1F,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA,CAAC7D,MAAM;cACL+J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAErG,OAAA,CAACzB,WAAW;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb7F,UAAU,CAAC,IAAI,CAAC;gBAChBjB,aAAa,CAACoD,mBAAmB,CAACtC,UAAU,EAAE,OAAO,CAAC,CACnDyL,IAAI,CAACjF,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP5C,GAAG,EAAEiF,IAAI,CAACpE;kBACZ,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,EAAE8B,GAAG,CAAC;gBAClD,CAAC,CAAC,CACDwJ,OAAO,CAAC,MAAM;kBACbvL,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAoF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ/D,OAAA,CAAC7C,SAAS;QAACkM,eAAe;QAAC3D,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC7F,OAAA,CAAC5C,gBAAgB;UAACkM,UAAU,eAAEtJ,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAACjB,aAAa;cAAC2G,EAAE,EAAE;gBAAE4B,EAAE,EAAE,CAAC;gBAAEtD,KAAK,EAAE;cAAe;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAsB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,EACdnE,WAAW,CAACI,MAAM,gBACjB9B,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAACpE,GAAG;cAAC8J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,QAAQ,EAAE,KAAK,CAAE;gBACzDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE4B,EAAE,EAAE;gBAAE,CAAE;gBAAAzB,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,QAAQ,EAAE,OAAO,CAAE;gBAC3DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL2C,kBAAkB,CAAChF,WAAW,CAACI,MAAM,CAAC;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,GACJvD,OAAO,gBACTR,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE6F,EAAE,EAAE;YAAE,CAAE;YAAAhG,QAAA,gBACxD7F,OAAA,CAAC1D,gBAAgB;cAACiK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B/D,OAAA,CAACnE,UAAU;cAAC6J,EAAE,EAAE;gBAAEoG,EAAE,EAAE;cAAE,CAAE;cAAAjG,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN/D,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAAC3D,KAAK;cAAC+O,QAAQ,EAAC,OAAO;cAAC1F,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA,CAAC7D,MAAM;cACL+J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAErG,OAAA,CAACzB,WAAW;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb7F,UAAU,CAAC,IAAI,CAAC;gBAChBjB,aAAa,CAACsD,eAAe,CAACxC,UAAU,EAAE,OAAO,CAAC,CAC/CyL,IAAI,CAACjF,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP3C,MAAM,EAAEgF,IAAI,CAACpE;kBACf,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC/B,KAAK,CAAC,+BAA+B,EAAE8B,GAAG,CAAC;gBACrD,CAAC,CAAC,CACDwJ,OAAO,CAAC,MAAM;kBACbvL,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAoF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ/D,OAAA,CAAC7C,SAAS;QAACkM,eAAe;QAAC3D,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACvC7F,OAAA,CAAC5C,gBAAgB;UAACkM,UAAU,eAAEtJ,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAACrC,YAAY;cAAC+H,EAAE,EAAE;gBAAE4B,EAAE,EAAE,CAAC;gBAAEtD,KAAK,EAAE;cAAa;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAqB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,EACdnE,WAAW,CAACK,SAAS,gBACpB/B,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAACpE,GAAG;cAAC8J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,YAAY,EAAE,KAAK,CAAE;gBAC7DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE4B,EAAE,EAAE;gBAAE,CAAE;gBAAAzB,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,YAAY,EAAE,OAAO,CAAE;gBAC/DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL8C,qBAAqB,CAACnF,WAAW,CAACK,SAAS,CAAC;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,GACJvD,OAAO,gBACTR,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAE6F,EAAE,EAAE;YAAE,CAAE;YAAAhG,QAAA,gBACxD7F,OAAA,CAAC1D,gBAAgB;cAACiK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B/D,OAAA,CAACnE,UAAU;cAAC6J,EAAE,EAAE;gBAAEoG,EAAE,EAAE;cAAE,CAAE;cAAAjG,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAEN/D,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAAC3D,KAAK;cAAC+O,QAAQ,EAAC,OAAO;cAAC1F,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR/D,OAAA,CAAC7D,MAAM;cACL+J,OAAO,EAAC,UAAU;cAClBK,IAAI,EAAC,OAAO;cACZF,SAAS,eAAErG,OAAA,CAACzB,WAAW;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb7F,UAAU,CAAC,IAAI,CAAC;gBAChBjB,aAAa,CAACwD,kBAAkB,CAAC1C,UAAU,EAAE,OAAO,CAAC,CAClDyL,IAAI,CAACjF,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP1C,SAAS,EAAE+E,IAAI,CAACpE;kBAClB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAAC/B,KAAK,CAAC,mCAAmC,EAAE8B,GAAG,CAAC;gBACzD,CAAC,CAAC,CACDwJ,OAAO,CAAC,MAAM;kBACbvL,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAoF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGZ/D,OAAA,CAAClE,KAAK;QAAC4J,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACzB7F,OAAA,CAACnE,UAAU;UAACqK,OAAO,EAAC,IAAI;UAAC+F,YAAY;UAAApG,QAAA,EAAC;QAAe;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAClE/D,OAAA,CAACnE,UAAU;UAACqK,OAAO,EAAC,OAAO;UAAClC,KAAK,EAAC,gBAAgB;UAAC0B,EAAE,EAAE;YAAEO,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAC;QAElE;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb/D,OAAA,CAACjE,IAAI;UAACwL,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA3B,QAAA,gBAEzB7F,OAAA,CAACjE,IAAI;YAAC0L,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvB7F,OAAA,CAAChE,IAAI;cAAA6J,QAAA,gBACH7F,OAAA,CAAC/D,WAAW;gBAAA4J,QAAA,gBACV7F,OAAA,CAACnE,UAAU;kBAACqK,OAAO,EAAC,IAAI;kBAAAL,QAAA,EAAC;gBAAuB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7D/D,OAAA,CAACnE,UAAU;kBAACqK,OAAO,EAAC,OAAO;kBAAClC,KAAK,EAAC,gBAAgB;kBAAC0B,EAAE,EAAE;oBAAEO,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAElE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACd/D,OAAA,CAAC9D,WAAW;gBAAA2J,QAAA,eACV7F,OAAA,CAAC7D,MAAM;kBACLgP,SAAS;kBACTjF,OAAO,EAAC,UAAU;kBAClBlC,KAAK,EAAC,MAAM;kBACZsC,OAAO,EAAEA,CAAA,KAAM;oBACbnF,aAAa,CAAC,kBAAkB,CAAC;oBACjCF,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBAAA4E,QAAA,EACH;gBAED;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGP/D,OAAA,CAACjE,IAAI;YAAC0L,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eACvB7F,OAAA,CAAChE,IAAI;cAAA6J,QAAA,gBACH7F,OAAA,CAAC/D,WAAW;gBAAA4J,QAAA,gBACV7F,OAAA,CAACnE,UAAU;kBAACqK,OAAO,EAAC,IAAI;kBAAAL,QAAA,EAAC;gBAAuB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7D/D,OAAA,CAACnE,UAAU;kBAACqK,OAAO,EAAC,OAAO;kBAAClC,KAAK,EAAC,gBAAgB;kBAAC0B,EAAE,EAAE;oBAAEO,EAAE,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAElE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACd/D,OAAA,CAAC9D,WAAW;gBAAA2J,QAAA,eACV7F,OAAA,CAAC7D,MAAM;kBACLgP,SAAS;kBACTjF,OAAO,EAAC,UAAU;kBAClBlC,KAAK,EAAC,SAAS;kBACfsC,OAAO,EAAEA,CAAA,KAAM;oBACbnF,aAAa,CAAC,cAAc,CAAC;oBAC7B;oBACA,MAAM6D,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;oBACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;oBAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;oBAExC/D,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXG,WAAW,EAAE2D,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;sBAClD9D,SAAS,EAAEwD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC7C,CAAC,CAAC;oBACFrE,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBAAA4E,QAAA,EACH;gBAED;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGPrC,WAAW,CAACM,eAAe,iBAC1BhC,OAAA,CAAC7C,SAAS;QAACkM,eAAe;QAAC3D,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAEL,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBAC9C7F,OAAA,CAAC5C,gBAAgB;UAACkM,UAAU,eAAEtJ,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAACnB,SAAS;cAAC6G,EAAE,EAAE;gBAAE4B,EAAE,EAAE,CAAC;gBAAEtD,KAAK,EAAE;cAAY;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAACpE,GAAG;cAAC8J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,kBAAkB,EAAE,KAAK,CAAE;gBACnEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE4B,EAAE,EAAE;gBAAE,CAAE;gBAAAzB,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,kBAAkB,EAAE,OAAO,CAAE;gBACrEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL4C,2BAA2B,CAACjF,WAAW,CAACM,eAAe,CAAC;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACZ,EAEArC,WAAW,CAACO,WAAW,iBACtBjC,OAAA,CAAC7C,SAAS;QAACkM,eAAe;QAAC3D,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAEL,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBAC9C7F,OAAA,CAAC5C,gBAAgB;UAACkM,UAAU,eAAEtJ,OAAA,CAACf,cAAc;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAA8B,QAAA,eAC/C7F,OAAA,CAACpE,GAAG;YAAC8J,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjD7F,OAAA,CAACjC,YAAY;cAAC2H,EAAE,EAAE;gBAAE4B,EAAE,EAAE,CAAC;gBAAEtD,KAAK,EAAE;cAAe;YAAE;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtD/D,OAAA,CAACnE,UAAU;cAACqK,OAAO,EAAC,IAAI;cAAAL,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACnB/D,OAAA,CAAC3C,gBAAgB;UAAAwI,QAAA,eACf7F,OAAA,CAACpE,GAAG;YAAAiK,QAAA,gBACF7F,OAAA,CAACpE,GAAG;cAAC8J,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D7F,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,cAAc,EAAE,KAAK,CAAE;gBAC/DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE4B,EAAE,EAAE;gBAAE,CAAE;gBAAAzB,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT/D,OAAA,CAAC7D,MAAM;gBACLkK,SAAS,eAAErG,OAAA,CAAC7B,YAAY;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAE;gBACjEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL6C,uBAAuB,CAAClF,WAAW,CAACO,WAAW,CAAC;UAAA;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACZ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLiH,YAAY,CAAC,CAAC;EAAA;IAAApH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC3D,EAAA,CAzzCID,iBAAiB;EAAA,QACJf,WAAW,EACLC,SAAS,EACfC,OAAO;AAAA;AAAA4M,EAAA,GAHpB/L,iBAAiB;AA2zCvB,eAAeA,iBAAiB;AAAC,IAAA+L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}