{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\posa\\\\ModificaCavoPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, IconButton, Alert, Snackbar, Paper // Aggiunto Paper\n} from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport SelezionaCavoForm from '../../../components/cavi/SelezionaCavoForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModificaCavoPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    isImpersonating\n  } = useAuth();\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  const cantiereName = localStorage.getItem('selectedCantiereName');\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n  const handleSuccess = message => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n  const handleError = message => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n  if (!cantiereId) {\n    navigate('/dashboard/cantieri');\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToCantieri,\n          sx: {\n            mr: 0.5\n          },\n          size: \"small\",\n          children: [\" \", /*#__PURE__*/_jsxDEV(ArrowBackIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontSize: '1.1rem'\n          },\n          children: [\" \", \"Modifica Cavo\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 1\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          size: \"small\" // Ridotto size\n          ,\n          children: [/*#__PURE__*/_jsxDEV(RefreshIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(SelezionaCavoForm, {\n        cantiereId: cantiereId,\n        onSuccess: handleSuccess,\n        onError: handleError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openSnackbar,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: alertSeverity,\n        sx: {\n          width: '100%'\n        },\n        children: alertMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(ModificaCavoPage, \"OwbUx2RsBYDYpTx8dGW8kzIcrXM=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = ModificaCavoPage;\nexport default ModificaCavoPage;\nvar _c;\n$RefreshReg$(_c, \"ModificaCavoPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "IconButton", "<PERSON><PERSON>", "Snackbar", "Paper", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "useNavigate", "useAuth", "AdminHomeButton", "SelezionaCavoForm", "jsxDEV", "_jsxDEV", "ModificaCavoPage", "_s", "navigate", "isImpersonating", "alertMessage", "setAlertMessage", "alertSeverity", "setAlertSeverity", "openSnackbar", "setOpenSnackbar", "cantiereId", "localStorage", "getItem", "cantiereName", "handleBackToCantieri", "handleBackToAdmin", "handleSuccess", "message", "handleError", "handleCloseSnackbar", "children", "sx", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "size", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "window", "location", "reload", "ml", "color", "title", "p", "onSuccess", "onError", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/posa/ModificaCavoPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n  Alert,\n  Snackbar,\n  Paper // Aggiunto Paper\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport SelezionaCavoForm from '../../../components/cavi/SelezionaCavoForm';\n\nconst ModificaCavoPage = () => {\n  const navigate = useNavigate();\n  const { isImpersonating } = useAuth();\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  const cantiereName = localStorage.getItem('selectedCantiereName');\n\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  const handleSuccess = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  const handleError = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  if (!cantiereId) {\n    navigate('/dashboard/cantieri');\n    return null;\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 0.5 }} size=\"small\"> {/* Ridotto size */}\n            <ArrowBackIcon fontSize=\"small\" /> {/* Ridotto fontSize */}\n          </IconButton>\n          <Typography variant=\"h6\" sx={{ fontSize: '1.1rem' }}> {/* Ridotto font */}\n            Modifica Cavo\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 1 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n            size=\"small\" // Ridotto size\n          >\n            <RefreshIcon fontSize=\"small\" /> {/* Ridotto fontSize */}\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Aggiunto Paper per uniformare lo stile */}\n      <Paper sx={{ p: 3 }}>\n        <SelezionaCavoForm\n          cantiereId={cantiereId}\n          onSuccess={handleSuccess}\n          onError={handleError}\n        />\n      </Paper>\n\n      <Snackbar\n        open={openSnackbar}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>\n          {alertMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default ModificaCavoPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,KAAK,EACLC,QAAQ,EACRC,KAAK,CAAC;AAAA,OACD,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,iBAAiB,MAAM,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3E,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES;EAAgB,CAAC,GAAGR,OAAO,CAAC,CAAC;EACrC,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAM2B,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EAC7D,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;EAEjE,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjCZ,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;EAED,MAAMa,iBAAiB,GAAGA,CAAA,KAAM;IAC9Bb,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;EAED,MAAMc,aAAa,GAAIC,OAAO,IAAK;IACjCZ,eAAe,CAACY,OAAO,CAAC;IACxBV,gBAAgB,CAAC,SAAS,CAAC;IAC3BE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMS,WAAW,GAAID,OAAO,IAAK;IAC/BZ,eAAe,CAACY,OAAO,CAAC;IACxBV,gBAAgB,CAAC,OAAO,CAAC;IACzBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMU,mBAAmB,GAAGA,CAAA,KAAM;IAChCV,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,IAAI,CAACC,UAAU,EAAE;IACfR,QAAQ,CAAC,qBAAqB,CAAC;IAC/B,OAAO,IAAI;EACb;EAEA,oBACEH,OAAA,CAACf,GAAG;IAAAoC,QAAA,gBACFrB,OAAA,CAACf,GAAG;MAACqC,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAL,QAAA,gBACzFrB,OAAA,CAACf,GAAG;QAACqC,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBACjDrB,OAAA,CAACb,UAAU;UAACwC,OAAO,EAAEZ,oBAAqB;UAACO,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAI,CAAE;UAACC,IAAI,EAAC,OAAO;UAAAR,QAAA,GAAC,GAAC,eACxErB,OAAA,CAACR,aAAa;YAACsC,QAAQ,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,KAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eACblC,OAAA,CAACd,UAAU;UAACiD,OAAO,EAAC,IAAI;UAACb,EAAE,EAAE;YAAEQ,QAAQ,EAAE;UAAS,CAAE;UAAAT,QAAA,GAAC,GAAC,EAAoB,eAE1E;QAAA;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblC,OAAA,CAACb,UAAU;UACTwC,OAAO,EAAEA,CAAA,KAAMS,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxChB,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAC,oBAAoB;UAC1BZ,IAAI,EAAC,OAAO,CAAC;UAAA;UAAAR,QAAA,gBAEbrB,OAAA,CAACN,WAAW;YAACoC,QAAQ,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,KAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNlC,OAAA,CAACH,eAAe;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAGNlC,OAAA,CAACV,KAAK;MAACgC,EAAE,EAAE;QAAEoB,CAAC,EAAE;MAAE,CAAE;MAAArB,QAAA,eAClBrB,OAAA,CAACF,iBAAiB;QAChBa,UAAU,EAAEA,UAAW;QACvBgC,SAAS,EAAE1B,aAAc;QACzB2B,OAAO,EAAEzB;MAAY;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAERlC,OAAA,CAACX,QAAQ;MACPwD,IAAI,EAAEpC,YAAa;MACnBqC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAE3B,mBAAoB;MAC7B4B,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA7B,QAAA,eAE3DrB,OAAA,CAACZ,KAAK;QAAC2D,OAAO,EAAE3B,mBAAoB;QAAC+B,QAAQ,EAAE5C,aAAc;QAACe,EAAE,EAAE;UAAE8B,KAAK,EAAE;QAAO,CAAE;QAAA/B,QAAA,EACjFhB;MAAY;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAChC,EAAA,CAnFID,gBAAgB;EAAA,QACHN,WAAW,EACAC,OAAO;AAAA;AAAAyD,EAAA,GAF/BpD,gBAAgB;AAqFtB,eAAeA,gBAAgB;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}