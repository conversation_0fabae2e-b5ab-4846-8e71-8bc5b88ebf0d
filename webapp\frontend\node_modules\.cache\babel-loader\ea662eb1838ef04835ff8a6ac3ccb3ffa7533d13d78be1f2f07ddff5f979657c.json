{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\AggiungiCavoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, FormHelperText, Alert, CircularProgress, Typography, Paper } from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport config from '../../config';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AggiungiCavoForm = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  isDialog = false\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    revisione_ufficiale: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Carica la revisione corrente all'avvio\n  useEffect(() => {\n    const loadRevisioneCorrente = async () => {\n      try {\n        setLoadingRevisione(true);\n        const revisione = await caviService.getRevisioneCorrente(cantiereId);\n        setFormData(prev => ({\n          ...prev,\n          revisione_ufficiale: revisione\n        }));\n      } catch (error) {\n        console.error('Errore nel caricamento della revisione corrente:', error);\n        onError('Errore nel caricamento della revisione corrente');\n      } finally {\n        setLoadingRevisione(false);\n      }\n    };\n    loadRevisioneCorrente();\n  }, [cantiereId, onError]);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, non fare nulla (il dialog ha il suo pulsante di annullamento)\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n  // Verifica se il cavo esiste già nel database\n  const checkCavoExists = async () => {\n    if (!formData.id_cavo) {\n      onError('Inserisci un ID cavo prima di verificare');\n      return;\n    }\n    setLoading(true);\n    try {\n      // Usa il servizio per verificare se il cavo esiste già\n      const result = await caviService.debugCavo(cantiereId, formData.id_cavo.toUpperCase());\n      if (result.trovato_tra_attivi) {\n        onError(`Il cavo ${formData.id_cavo.toUpperCase()} esiste già tra i cavi attivi. Usa un ID diverso.`);\n      } else if (result.trovato_tra_spare) {\n        onError(`Il cavo ${formData.id_cavo.toUpperCase()} esiste già tra i cavi SPARE. Usa un ID diverso.`);\n      } else {\n        onSuccess(`Il cavo ${formData.id_cavo.toUpperCase()} non esiste nel database. Puoi procedere con l'inserimento.`);\n      }\n    } catch (error) {\n      console.error('Errore durante la verifica del cavo:', error);\n      onError('Errore durante la verifica del cavo. Riprova più tardi.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      // Validazione completa dei dati del cavo\n      console.log('Dati del form prima della validazione:', formData);\n      const validation = validateCavoData(formData);\n      console.log('Risultato validazione:', validation);\n      if (!validation.isValid) {\n        console.error('Errori di validazione:', validation.errors);\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        onError('Ci sono errori nel form. Controlla i campi evidenziati in rosso.');\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Assicurati che i campi obbligatori siano presenti (basati sulla definizione della tabella)\n      // Campi obbligatori: id_cavo, id_cantiere, utility, tipologia, n_conduttori, sezione, metri_teorici,\n      // ubicazione_partenza, ubicazione_arrivo, stato_installazione\n\n      // Verifica che i campi obbligatori siano presenti\n      const requiredFields = ['id_cavo', 'utility', 'tipologia', 'n_conduttori', 'sezione', 'metri_teorici', 'ubicazione_partenza', 'ubicazione_arrivo', 'stato_installazione'];\n      const missingFields = requiredFields.filter(field => !validatedData[field]);\n      if (missingFields.length > 0) {\n        throw new Error(`Campi obbligatori mancanti: ${missingFields.join(', ')}`);\n      }\n\n      // Prepara i dati da inviare\n      const dataToSend = {\n        ...validatedData,\n        // Assicurati che i campi obbligatori siano presenti\n        id_cavo: validatedData.id_cavo.toUpperCase(),\n        utility: validatedData.utility,\n        tipologia: validatedData.tipologia,\n        n_conduttori: validatedData.n_conduttori ? validatedData.n_conduttori.toString() : \"0\",\n        // Invia come stringa\n        sezione: validatedData.sezione ? validatedData.sezione.toString() : \"0\",\n        // Invia come stringa\n        metri_teorici: parseFloat(validatedData.metri_teorici) || 0,\n        ubicazione_partenza: validatedData.ubicazione_partenza,\n        ubicazione_arrivo: validatedData.ubicazione_arrivo,\n        stato_installazione: validatedData.stato_installazione || 'Da installare',\n        // Campi opzionali\n        metratura_reale: validatedData.metratura_reale ? parseFloat(validatedData.metratura_reale) : null,\n        id_bobina: validatedData.id_bobina || null,\n        // Altri campi che potrebbero essere utili\n        sistema: validatedData.sistema || null,\n        colore_cavo: validatedData.colore_cavo || null,\n        utenza_partenza: validatedData.utenza_partenza || null,\n        utenza_arrivo: validatedData.utenza_arrivo || null,\n        descrizione_utenza_partenza: validatedData.descrizione_utenza_partenza || null,\n        descrizione_utenza_arrivo: validatedData.descrizione_utenza_arrivo || null,\n        sh: validatedData.sh || 'N',\n        responsabile_posa: validatedData.responsabile_posa || null,\n        note: validatedData.note || null\n      };\n      console.log('Dati da inviare al server dopo la validazione:', dataToSend);\n      try {\n        // Invia i dati al server\n        console.log('Tentativo di invio dati al server...');\n\n        // Verifica che cantiereId sia valido\n        if (!cantiereId) {\n          throw new Error('ID cantiere non valido o mancante');\n        }\n\n        // Usa direttamente axios per avere più controllo\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('Token di autenticazione mancante. Effettua nuovamente il login.');\n        }\n\n        // VERIFICA AUTOMATICA: Controlla se il cavo esiste già prima di inviare la richiesta\n        console.log(`Verifica automatica se il cavo ${dataToSend.id_cavo} esiste già...`);\n        try {\n          const checkResponse = await axios.get(`${config.API_URL}/cavi/${cantiereId}/check/${dataToSend.id_cavo}`, {\n            headers: {\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 10000 // 10 secondi per la verifica\n          });\n          if (checkResponse.data && checkResponse.data.exists) {\n            throw new Error(`Il cavo ${dataToSend.id_cavo} esiste già nel database. Usa un ID diverso.`);\n          }\n          console.log(`Verifica completata: il cavo ${dataToSend.id_cavo} non esiste nel database.`);\n        } catch (checkError) {\n          // Se l'errore è dovuto al fatto che l'endpoint non esiste, continuiamo\n          // altrimenti, se è un errore di risposta con dati, lo gestiamo\n          if (checkError.response && checkError.response.data) {\n            if (checkError.response.data.exists) {\n              throw new Error(`Il cavo ${dataToSend.id_cavo} esiste già nel database. Usa un ID diverso.`);\n            }\n          } else if (!checkError.response || checkError.response.status !== 404) {\n            // Se non è un errore 404 (endpoint non trovato), lo logghiamo ma continuiamo\n            console.warn('Errore durante la verifica del cavo:', checkError);\n            console.log('Continuiamo con l\\'inserimento del cavo...');\n          }\n        }\n        console.log(`Invio richiesta POST a /cavi/${cantiereId}`);\n        console.log('Dati inviati:', JSON.stringify(dataToSend, null, 2));\n        const response = await axios.post(`${config.API_URL}/cavi/${cantiereId}`, dataToSend, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          },\n          timeout: 60000,\n          // 60 secondi (aumentato per risolvere problemi di timeout)\n          validateStatus: function (status) {\n            // Considera validi tutti gli status code per poter vedere l'errore\n            return true;\n          }\n        });\n\n        // Log dettagliato della risposta\n        console.log('Status code:', response.status);\n        console.log('Headers:', response.headers);\n        console.log('Risposta completa:', response.data);\n\n        // Se lo status code non è di successo, lancia un errore\n        if (response.status >= 400) {\n          let errorDetail = response.data.detail || `Errore ${response.status}: ${response.statusText}`;\n          console.error('Errore dal server:', errorDetail);\n\n          // Gestione specifica per errori comuni\n          if (response.status === 422) {\n            // Errore di validazione\n            console.error('Errore di validazione:', response.data);\n\n            // Verifica se l'errore è relativo a un ID duplicato\n            if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('duplicate')) {\n              errorDetail = `Esiste già un cavo con ID ${dataToSend.id_cavo}. Usa un ID diverso.`;\n            } else if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('unique')) {\n              errorDetail = `Esiste già un cavo con ID ${dataToSend.id_cavo}. Usa un ID diverso.`;\n            } else if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('already exists')) {\n              errorDetail = `Esiste già un cavo con ID ${dataToSend.id_cavo}. Usa un ID diverso.`;\n            } else if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('constraint')) {\n              errorDetail = `Errore di vincolo nel database. Potrebbe esserci un cavo con lo stesso ID o un altro problema di vincolo.`;\n            }\n\n            // Verifica se ci sono errori di validazione specifici nei campi\n            if (response.data && response.data.detail && Array.isArray(response.data.detail)) {\n              const validationErrors = response.data.detail;\n              const errorMessages = validationErrors.map(err => {\n                const field = err.loc && err.loc.length > 1 ? err.loc[1] : 'campo sconosciuto';\n                return `${field}: ${err.msg}`;\n              }).join('\\n');\n              errorDetail = `Errori di validazione:\\n${errorMessages}`;\n            }\n          }\n          throw new Error(errorDetail);\n        }\n        console.log('Risposta dal server:', response.data);\n        onSuccess('Cavo aggiunto con successo');\n\n        // Reindirizza alla visualizzazione dei cavi solo se non è in un dialog\n        if (!isDialog) {\n          console.log('Reindirizzamento a visualizza cavi...');\n          // Usa setTimeout per dare tempo al browser di mostrare il messaggio di successo\n          setTimeout(() => {\n            try {\n              window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n            } catch (navError) {\n              console.error('Errore durante il reindirizzamento:', navError);\n              // Fallback: ricarica la pagina\n              window.location.reload();\n            }\n          }, 1000);\n        }\n      } catch (error) {\n        console.error('Errore durante l\\'invio dei dati al server:', error);\n        let errorMessage = 'Errore durante l\\'aggiunta del cavo';\n        let isNetworkError = false;\n\n        // Gestione specifica per errori di rete\n        if (error.message && (error.message.includes('Network Error') || error.message.includes('Failed to fetch'))) {\n          isNetworkError = true;\n          console.error('Errore di connessione al server:', error);\n\n          // Mostra un messaggio di attesa all'utente\n          onError('Impossibile connettersi al server. Verifica in corso...');\n\n          // Attendiamo 3 secondi per dare tempo al server di completare l'operazione\n          setTimeout(async () => {\n            try {\n              // Verifica automatica se il cavo è stato inserito nonostante l'errore di rete\n              console.log(`Verifica automatica se il cavo ${dataToSend.id_cavo} è stato inserito...`);\n              const token = localStorage.getItem('token');\n              const checkResponse = await axios.get(`${config.API_URL}/cavi/${cantiereId}/check/${dataToSend.id_cavo}`, {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 10000 // 10 secondi per la verifica\n              });\n              if (checkResponse.data && checkResponse.data.exists) {\n                // Il cavo è stato inserito con successo nonostante l'errore di rete\n                onSuccess(`Il cavo ${dataToSend.id_cavo} è stato inserito con successo nonostante l'errore di rete.`);\n\n                // Resetta il form perché l'inserimento è avvenuto con successo\n                setFormData({\n                  id_cavo: '',\n                  revisione_ufficiale: formData.revisione_ufficiale,\n                  // Mantieni la revisione\n                  sistema: '',\n                  utility: '',\n                  colore_cavo: '',\n                  tipologia: '',\n                  n_conduttori: '',\n                  sezione: '',\n                  sh: '',\n                  ubicazione_partenza: '',\n                  utenza_partenza: '',\n                  descrizione_utenza_partenza: '',\n                  ubicazione_arrivo: '',\n                  utenza_arrivo: '',\n                  descrizione_utenza_arrivo: '',\n                  metri_teorici: '',\n                  metratura_reale: '0',\n                  responsabile_posa: '',\n                  id_bobina: '',\n                  stato_installazione: 'Da installare'\n                });\n                setFormErrors({});\n                setFormWarnings({});\n              } else {\n                // Il cavo non è stato inserito\n                onError('Impossibile connettersi al server. La richiesta non è stata completata. Riprova.');\n              }\n            } catch (checkError) {\n              // Se non riusciamo a verificare, mostriamo un messaggio di errore\n              console.error('Errore durante la verifica automatica:', checkError);\n              onError('Impossibile verificare se il cavo è stato inserito. Riprova l\\'operazione.');\n            } finally {\n              setLoading(false);\n            }\n          }, 3000); // Attendi 3 secondi prima di verificare\n\n          return; // Usciamo dalla funzione per evitare di resettare il form in caso di errore di rete\n        }\n        // Estrai il messaggio di errore per altri tipi di errori\n        else if (error && typeof error === 'object') {\n          if (error.response && error.response.data) {\n            // Errore dal server con risposta\n            const responseData = error.response.data;\n            if (responseData.detail) {\n              errorMessage = responseData.detail;\n            } else if (typeof responseData === 'string') {\n              errorMessage = responseData;\n            } else {\n              errorMessage = JSON.stringify(responseData);\n            }\n          } else if (error.detail) {\n            errorMessage = error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          } else if (error.error) {\n            errorMessage = error.error;\n          }\n        } else if (typeof error === 'string') {\n          errorMessage = error;\n        }\n\n        // Mostra l'errore all'utente\n        onError(errorMessage);\n        setLoading(false);\n\n        // Resetta il form per errori non di rete\n        setFormData({\n          id_cavo: '',\n          revisione_ufficiale: formData.revisione_ufficiale,\n          // Mantieni la revisione\n          sistema: '',\n          utility: '',\n          colore_cavo: '',\n          tipologia: '',\n          n_conduttori: '',\n          sezione: '',\n          sh: '',\n          ubicazione_partenza: '',\n          utenza_partenza: '',\n          descrizione_utenza_partenza: '',\n          ubicazione_arrivo: '',\n          utenza_arrivo: '',\n          descrizione_utenza_arrivo: '',\n          metri_teorici: '',\n          metratura_reale: '0',\n          responsabile_posa: '',\n          id_bobina: '',\n          stato_installazione: 'Da installare'\n        });\n        setFormErrors({});\n        setFormWarnings({});\n      }\n\n      // Resetta il form solo in caso di successo\n      setFormData({\n        id_cavo: '',\n        revisione_ufficiale: formData.revisione_ufficiale,\n        // Mantieni la revisione\n        sistema: '',\n        utility: '',\n        colore_cavo: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        sh: '',\n        ubicazione_partenza: '',\n        utenza_partenza: '',\n        descrizione_utenza_partenza: '',\n        ubicazione_arrivo: '',\n        utenza_arrivo: '',\n        descrizione_utenza_arrivo: '',\n        metri_teorici: '',\n        metratura_reale: '0',\n        responsabile_posa: '',\n        id_bobina: '',\n        stato_installazione: 'Da installare'\n      });\n      setFormErrors({});\n      setFormWarnings({});\n    } catch (error) {\n      console.error('Errore durante l\\'aggiunta del cavo:', error);\n      onError(error.detail || 'Errore durante l\\'aggiunta del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleSubmit,\n    noValidate: true,\n    children: loadingRevisione ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [hasWarnings && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 23\n          }, this),\n          sx: {\n            mb: isDialog ? 1 : 2,\n            py: isDialog ? 0.5 : 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontSize: isDialog ? '0.8rem' : '0.875rem'\n            },\n            children: \"Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 15\n        }, this), formWarnings.network_error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: isDialog ? 2 : 3,\n            py: isDialog ? 0.5 : 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: formWarnings.network_error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Informazioni Generali\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"id_cavo\",\n              label: \"ID Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_cavo,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.id_cavo,\n              helperText: formErrors.id_cavo,\n              inputProps: {\n                style: {\n                  textTransform: 'uppercase'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"revisione_ufficiale\",\n              label: \"Revisione Ufficiale\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.revisione_ufficiale,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.revisione_ufficiale,\n              helperText: formErrors.revisione_ufficiale\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sistema\",\n              label: \"Sistema\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sistema,\n              onChange: handleFormChange,\n              error: !!formErrors.sistema,\n              helperText: formErrors.sistema\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utility\",\n              label: \"Utility\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utility,\n              onChange: handleFormChange,\n              error: !!formErrors.utility,\n              helperText: formErrors.utility\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Caratteristiche Tecniche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"colore_cavo\",\n              label: \"Colore Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.colore_cavo,\n              onChange: handleFormChange,\n              error: !!formErrors.colore_cavo,\n              helperText: formErrors.colore_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"tipologia\",\n              label: \"Tipologia\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.tipologia,\n              onChange: handleFormChange,\n              error: !!formErrors.tipologia,\n              helperText: formErrors.tipologia\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"n_conduttori\",\n              label: \"Numero Conduttori\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.n_conduttori,\n              onChange: handleFormChange,\n              error: !!formErrors.n_conduttori,\n              helperText: formErrors.n_conduttori || formWarnings.n_conduttori,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.n_conduttori ? 'orange' : undefined\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sezione\",\n              label: \"Sezione\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sezione,\n              onChange: handleFormChange,\n              error: !!formErrors.sezione,\n              helperText: formErrors.sezione || formWarnings.sezione,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.sezione ? 'orange' : undefined\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              error: !!formErrors.sh,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"sh-label\",\n                children: \"Schermato (S/N)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"sh-label\",\n                name: \"sh\",\n                value: formData.sh,\n                label: \"Schermato (S/N)\",\n                onChange: handleFormChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"S\",\n                  children: \"S\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"N\",\n                  children: \"N\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 19\n              }, this), formErrors.sh && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: formErrors.sh\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Ubicazione Partenza\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_partenza\",\n              label: \"Ubicazione Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_partenza,\n              helperText: formErrors.ubicazione_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 667,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_partenza\",\n              label: \"Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_partenza,\n              helperText: formErrors.utenza_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_partenza\",\n              label: \"Descrizione Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_partenza,\n              helperText: formErrors.descrizione_utenza_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Ubicazione Arrivo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_arrivo\",\n              label: \"Ubicazione Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_arrivo,\n              helperText: formErrors.ubicazione_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_arrivo\",\n              label: \"Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_arrivo,\n              helperText: formErrors.utenza_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_arrivo\",\n              label: \"Descrizione Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_arrivo,\n              helperText: formErrors.descrizione_utenza_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 734,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 705,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Metratura\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"metri_teorici\",\n              label: \"Metri Teorici\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_teorici,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_teorici,\n              helperText: formErrors.metri_teorici\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 753,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 749,\n        columnNumber: 11\n      }, this), !isDialog && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          size: \"large\",\n          onClick: handleCancel,\n          disabled: loading,\n          sx: {\n            minWidth: 150\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 773,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"large\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 28\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 150\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 792,\n            columnNumber: 28\n          }, this) : 'Salva Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 783,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 772,\n        columnNumber: 13\n      }, this), isDialog && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"medium\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 28\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 120\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 28\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 797,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 491,\n    columnNumber: 5\n  }, this);\n};\n_s(AggiungiCavoForm, \"Muxm1Wb8jDN/87yWsjtE8U4F0S8=\", false, function () {\n  return [useNavigate];\n});\n_c = AggiungiCavoForm;\nexport default AggiungiCavoForm;\nvar _c;\n$RefreshReg$(_c, \"AggiungiCavoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "FormHelperText", "<PERSON><PERSON>", "CircularProgress", "Typography", "Paper", "Save", "SaveIcon", "Warning", "WarningIcon", "useNavigate", "axios", "config", "caviService", "validateCavoData", "validateField", "isEmpty", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AggiungiCavoForm", "cantiereId", "onSuccess", "onError", "isDialog", "_s", "navigate", "loading", "setLoading", "loadingRevisione", "setLoadingRevisione", "formData", "setFormData", "id_cavo", "revisione_ufficiale", "sistema", "utility", "colore_cavo", "tipologia", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "responsabile_posa", "id_bobina", "stato_installazione", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "loadRevisioneCorrente", "revisione", "getRevisioneCorrente", "prev", "error", "console", "handleFormChange", "e", "name", "value", "target", "additionalParams", "metriTeorici", "parseFloat", "result", "valid", "message", "warning", "handleCancel", "checkCavoExists", "debugCavo", "toUpperCase", "trovato_tra_attivi", "trovato_tra_spare", "handleSubmit", "preventDefault", "log", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "validatedData", "requiredFields", "missingFields", "filter", "field", "length", "Error", "join", "dataToSend", "toString", "note", "token", "localStorage", "getItem", "checkResponse", "get", "API_URL", "headers", "timeout", "data", "exists", "checkError", "response", "status", "warn", "JSON", "stringify", "post", "validateStatus", "errorDetail", "detail", "statusText", "toLowerCase", "includes", "Array", "isArray", "validationErrors", "errorMessages", "map", "err", "loc", "msg", "setTimeout", "window", "location", "href", "navError", "reload", "errorMessage", "isNetworkError", "responseData", "hasWarnings", "Object", "keys", "component", "onSubmit", "noValidate", "children", "sx", "display", "justifyContent", "my", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "icon", "mb", "py", "variant", "fontSize", "network_error", "fontWeight", "p", "boxShadow", "gutterBottom", "container", "spacing", "item", "xs", "sm", "label", "fullWidth", "onChange", "required", "helperText", "inputProps", "style", "textTransform", "FormHelperTextProps", "color", "undefined", "id", "labelId", "mt", "gap", "size", "onClick", "disabled", "min<PERSON><PERSON><PERSON>", "type", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/AggiungiCavoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormHelperText,\n  Alert,\n  CircularProgress,\n  Typography,\n  Paper\n} from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport config from '../../config';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\nconst AggiungiCavoForm = ({ cantiereId, onSuccess, onError, isDialog = false }) => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    revisione_ufficiale: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Carica la revisione corrente all'avvio\n  useEffect(() => {\n    const loadRevisioneCorrente = async () => {\n      try {\n        setLoadingRevisione(true);\n        const revisione = await caviService.getRevisioneCorrente(cantiereId);\n        setFormData(prev => ({ ...prev, revisione_ufficiale: revisione }));\n      } catch (error) {\n        console.error('Errore nel caricamento della revisione corrente:', error);\n        onError('Errore nel caricamento della revisione corrente');\n      } finally {\n        setLoadingRevisione(false);\n      }\n    };\n\n    loadRevisioneCorrente();\n  }, [cantiereId, onError]);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, non fare nulla (il dialog ha il suo pulsante di annullamento)\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n  // Verifica se il cavo esiste già nel database\n  const checkCavoExists = async () => {\n    if (!formData.id_cavo) {\n      onError('Inserisci un ID cavo prima di verificare');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Usa il servizio per verificare se il cavo esiste già\n      const result = await caviService.debugCavo(cantiereId, formData.id_cavo.toUpperCase());\n\n      if (result.trovato_tra_attivi) {\n        onError(`Il cavo ${formData.id_cavo.toUpperCase()} esiste già tra i cavi attivi. Usa un ID diverso.`);\n      } else if (result.trovato_tra_spare) {\n        onError(`Il cavo ${formData.id_cavo.toUpperCase()} esiste già tra i cavi SPARE. Usa un ID diverso.`);\n      } else {\n        onSuccess(`Il cavo ${formData.id_cavo.toUpperCase()} non esiste nel database. Puoi procedere con l'inserimento.`);\n      }\n    } catch (error) {\n      console.error('Errore durante la verifica del cavo:', error);\n      onError('Errore durante la verifica del cavo. Riprova più tardi.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      // Validazione completa dei dati del cavo\n      console.log('Dati del form prima della validazione:', formData);\n      const validation = validateCavoData(formData);\n      console.log('Risultato validazione:', validation);\n\n      if (!validation.isValid) {\n        console.error('Errori di validazione:', validation.errors);\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        onError('Ci sono errori nel form. Controlla i campi evidenziati in rosso.');\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Assicurati che i campi obbligatori siano presenti (basati sulla definizione della tabella)\n      // Campi obbligatori: id_cavo, id_cantiere, utility, tipologia, n_conduttori, sezione, metri_teorici,\n      // ubicazione_partenza, ubicazione_arrivo, stato_installazione\n\n      // Verifica che i campi obbligatori siano presenti\n      const requiredFields = [\n        'id_cavo', 'utility', 'tipologia', 'n_conduttori', 'sezione', 'metri_teorici',\n        'ubicazione_partenza', 'ubicazione_arrivo', 'stato_installazione'\n      ];\n\n      const missingFields = requiredFields.filter(field => !validatedData[field]);\n      if (missingFields.length > 0) {\n        throw new Error(`Campi obbligatori mancanti: ${missingFields.join(', ')}`);\n      }\n\n      // Prepara i dati da inviare\n      const dataToSend = {\n        ...validatedData,\n        // Assicurati che i campi obbligatori siano presenti\n        id_cavo: validatedData.id_cavo.toUpperCase(),\n        utility: validatedData.utility,\n        tipologia: validatedData.tipologia,\n        n_conduttori: validatedData.n_conduttori ? validatedData.n_conduttori.toString() : \"0\", // Invia come stringa\n        sezione: validatedData.sezione ? validatedData.sezione.toString() : \"0\", // Invia come stringa\n        metri_teorici: parseFloat(validatedData.metri_teorici) || 0,\n        ubicazione_partenza: validatedData.ubicazione_partenza,\n        ubicazione_arrivo: validatedData.ubicazione_arrivo,\n        stato_installazione: validatedData.stato_installazione || 'Da installare',\n\n        // Campi opzionali\n        metratura_reale: validatedData.metratura_reale ? parseFloat(validatedData.metratura_reale) : null,\n        id_bobina: validatedData.id_bobina || null,\n\n        // Altri campi che potrebbero essere utili\n        sistema: validatedData.sistema || null,\n        colore_cavo: validatedData.colore_cavo || null,\n        utenza_partenza: validatedData.utenza_partenza || null,\n        utenza_arrivo: validatedData.utenza_arrivo || null,\n        descrizione_utenza_partenza: validatedData.descrizione_utenza_partenza || null,\n        descrizione_utenza_arrivo: validatedData.descrizione_utenza_arrivo || null,\n        sh: validatedData.sh || 'N',\n        responsabile_posa: validatedData.responsabile_posa || null,\n        note: validatedData.note || null\n      };\n\n      console.log('Dati da inviare al server dopo la validazione:', dataToSend);\n\n      try {\n        // Invia i dati al server\n        console.log('Tentativo di invio dati al server...');\n\n        // Verifica che cantiereId sia valido\n        if (!cantiereId) {\n          throw new Error('ID cantiere non valido o mancante');\n        }\n\n        // Usa direttamente axios per avere più controllo\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('Token di autenticazione mancante. Effettua nuovamente il login.');\n        }\n\n        // VERIFICA AUTOMATICA: Controlla se il cavo esiste già prima di inviare la richiesta\n        console.log(`Verifica automatica se il cavo ${dataToSend.id_cavo} esiste già...`);\n        try {\n          const checkResponse = await axios.get(`${config.API_URL}/cavi/${cantiereId}/check/${dataToSend.id_cavo}`, {\n            headers: {\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 10000 // 10 secondi per la verifica\n          });\n\n          if (checkResponse.data && checkResponse.data.exists) {\n            throw new Error(`Il cavo ${dataToSend.id_cavo} esiste già nel database. Usa un ID diverso.`);\n          }\n          console.log(`Verifica completata: il cavo ${dataToSend.id_cavo} non esiste nel database.`);\n        } catch (checkError) {\n          // Se l'errore è dovuto al fatto che l'endpoint non esiste, continuiamo\n          // altrimenti, se è un errore di risposta con dati, lo gestiamo\n          if (checkError.response && checkError.response.data) {\n            if (checkError.response.data.exists) {\n              throw new Error(`Il cavo ${dataToSend.id_cavo} esiste già nel database. Usa un ID diverso.`);\n            }\n          } else if (!checkError.response || checkError.response.status !== 404) {\n            // Se non è un errore 404 (endpoint non trovato), lo logghiamo ma continuiamo\n            console.warn('Errore durante la verifica del cavo:', checkError);\n            console.log('Continuiamo con l\\'inserimento del cavo...');\n          }\n        }\n\n        console.log(`Invio richiesta POST a /cavi/${cantiereId}`);\n        console.log('Dati inviati:', JSON.stringify(dataToSend, null, 2));\n\n        const response = await axios.post(`${config.API_URL}/cavi/${cantiereId}`, dataToSend, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`\n          },\n          timeout: 60000, // 60 secondi (aumentato per risolvere problemi di timeout)\n          validateStatus: function (status) {\n            // Considera validi tutti gli status code per poter vedere l'errore\n            return true;\n          }\n        });\n\n        // Log dettagliato della risposta\n        console.log('Status code:', response.status);\n        console.log('Headers:', response.headers);\n        console.log('Risposta completa:', response.data);\n\n        // Se lo status code non è di successo, lancia un errore\n        if (response.status >= 400) {\n          let errorDetail = response.data.detail || `Errore ${response.status}: ${response.statusText}`;\n          console.error('Errore dal server:', errorDetail);\n\n          // Gestione specifica per errori comuni\n          if (response.status === 422) {\n            // Errore di validazione\n            console.error('Errore di validazione:', response.data);\n\n            // Verifica se l'errore è relativo a un ID duplicato\n            if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('duplicate')) {\n              errorDetail = `Esiste già un cavo con ID ${dataToSend.id_cavo}. Usa un ID diverso.`;\n            } else if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('unique')) {\n              errorDetail = `Esiste già un cavo con ID ${dataToSend.id_cavo}. Usa un ID diverso.`;\n            } else if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('already exists')) {\n              errorDetail = `Esiste già un cavo con ID ${dataToSend.id_cavo}. Usa un ID diverso.`;\n            } else if (errorDetail && typeof errorDetail === 'string' && errorDetail.toLowerCase().includes('constraint')) {\n              errorDetail = `Errore di vincolo nel database. Potrebbe esserci un cavo con lo stesso ID o un altro problema di vincolo.`;\n            }\n\n            // Verifica se ci sono errori di validazione specifici nei campi\n            if (response.data && response.data.detail && Array.isArray(response.data.detail)) {\n              const validationErrors = response.data.detail;\n              const errorMessages = validationErrors.map(err => {\n                const field = err.loc && err.loc.length > 1 ? err.loc[1] : 'campo sconosciuto';\n                return `${field}: ${err.msg}`;\n              }).join('\\n');\n\n              errorDetail = `Errori di validazione:\\n${errorMessages}`;\n            }\n          }\n\n          throw new Error(errorDetail);\n        }\n\n        console.log('Risposta dal server:', response.data);\n        onSuccess('Cavo aggiunto con successo');\n\n        // Reindirizza alla visualizzazione dei cavi solo se non è in un dialog\n        if (!isDialog) {\n          console.log('Reindirizzamento a visualizza cavi...');\n          // Usa setTimeout per dare tempo al browser di mostrare il messaggio di successo\n          setTimeout(() => {\n            try {\n              window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n            } catch (navError) {\n              console.error('Errore durante il reindirizzamento:', navError);\n              // Fallback: ricarica la pagina\n              window.location.reload();\n            }\n          }, 1000);\n        }\n      } catch (error) {\n        console.error('Errore durante l\\'invio dei dati al server:', error);\n        let errorMessage = 'Errore durante l\\'aggiunta del cavo';\n        let isNetworkError = false;\n\n        // Gestione specifica per errori di rete\n        if (error.message && (error.message.includes('Network Error') || error.message.includes('Failed to fetch'))) {\n          isNetworkError = true;\n          console.error('Errore di connessione al server:', error);\n\n          // Mostra un messaggio di attesa all'utente\n          onError('Impossibile connettersi al server. Verifica in corso...');\n\n          // Attendiamo 3 secondi per dare tempo al server di completare l'operazione\n          setTimeout(async () => {\n            try {\n              // Verifica automatica se il cavo è stato inserito nonostante l'errore di rete\n              console.log(`Verifica automatica se il cavo ${dataToSend.id_cavo} è stato inserito...`);\n              const token = localStorage.getItem('token');\n\n              const checkResponse = await axios.get(`${config.API_URL}/cavi/${cantiereId}/check/${dataToSend.id_cavo}`, {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 10000 // 10 secondi per la verifica\n              });\n\n              if (checkResponse.data && checkResponse.data.exists) {\n                // Il cavo è stato inserito con successo nonostante l'errore di rete\n                onSuccess(`Il cavo ${dataToSend.id_cavo} è stato inserito con successo nonostante l'errore di rete.`);\n\n                // Resetta il form perché l'inserimento è avvenuto con successo\n                setFormData({\n                  id_cavo: '',\n                  revisione_ufficiale: formData.revisione_ufficiale, // Mantieni la revisione\n                  sistema: '',\n                  utility: '',\n                  colore_cavo: '',\n                  tipologia: '',\n                  n_conduttori: '',\n                  sezione: '',\n                  sh: '',\n                  ubicazione_partenza: '',\n                  utenza_partenza: '',\n                  descrizione_utenza_partenza: '',\n                  ubicazione_arrivo: '',\n                  utenza_arrivo: '',\n                  descrizione_utenza_arrivo: '',\n                  metri_teorici: '',\n                  metratura_reale: '0',\n                  responsabile_posa: '',\n                  id_bobina: '',\n                  stato_installazione: 'Da installare'\n                });\n                setFormErrors({});\n                setFormWarnings({});\n              } else {\n                // Il cavo non è stato inserito\n                onError('Impossibile connettersi al server. La richiesta non è stata completata. Riprova.');\n              }\n            } catch (checkError) {\n              // Se non riusciamo a verificare, mostriamo un messaggio di errore\n              console.error('Errore durante la verifica automatica:', checkError);\n              onError('Impossibile verificare se il cavo è stato inserito. Riprova l\\'operazione.');\n            } finally {\n              setLoading(false);\n            }\n          }, 3000); // Attendi 3 secondi prima di verificare\n\n          return; // Usciamo dalla funzione per evitare di resettare il form in caso di errore di rete\n        }\n        // Estrai il messaggio di errore per altri tipi di errori\n        else if (error && typeof error === 'object') {\n          if (error.response && error.response.data) {\n            // Errore dal server con risposta\n            const responseData = error.response.data;\n            if (responseData.detail) {\n              errorMessage = responseData.detail;\n            } else if (typeof responseData === 'string') {\n              errorMessage = responseData;\n            } else {\n              errorMessage = JSON.stringify(responseData);\n            }\n          } else if (error.detail) {\n            errorMessage = error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          } else if (error.error) {\n            errorMessage = error.error;\n          }\n        } else if (typeof error === 'string') {\n          errorMessage = error;\n        }\n\n        // Mostra l'errore all'utente\n        onError(errorMessage);\n        setLoading(false);\n\n        // Resetta il form per errori non di rete\n        setFormData({\n          id_cavo: '',\n          revisione_ufficiale: formData.revisione_ufficiale, // Mantieni la revisione\n          sistema: '',\n          utility: '',\n          colore_cavo: '',\n          tipologia: '',\n          n_conduttori: '',\n          sezione: '',\n          sh: '',\n          ubicazione_partenza: '',\n          utenza_partenza: '',\n          descrizione_utenza_partenza: '',\n          ubicazione_arrivo: '',\n          utenza_arrivo: '',\n          descrizione_utenza_arrivo: '',\n          metri_teorici: '',\n          metratura_reale: '0',\n          responsabile_posa: '',\n          id_bobina: '',\n          stato_installazione: 'Da installare'\n        });\n        setFormErrors({});\n        setFormWarnings({});\n      }\n\n      // Resetta il form solo in caso di successo\n      setFormData({\n        id_cavo: '',\n        revisione_ufficiale: formData.revisione_ufficiale, // Mantieni la revisione\n        sistema: '',\n        utility: '',\n        colore_cavo: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        sh: '',\n        ubicazione_partenza: '',\n        utenza_partenza: '',\n        descrizione_utenza_partenza: '',\n        ubicazione_arrivo: '',\n        utenza_arrivo: '',\n        descrizione_utenza_arrivo: '',\n        metri_teorici: '',\n        metratura_reale: '0',\n        responsabile_posa: '',\n        id_bobina: '',\n        stato_installazione: 'Da installare'\n      });\n      setFormErrors({});\n      setFormWarnings({});\n    } catch (error) {\n      console.error('Errore durante l\\'aggiunta del cavo:', error);\n      onError(error.detail || 'Errore durante l\\'aggiunta del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n\n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n      {loadingRevisione ? (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      ) : (\n        <>\n          {hasWarnings && (\n            <>\n              <Alert\n                severity=\"warning\"\n                icon={<WarningIcon />}\n                sx={{ mb: isDialog ? 1 : 2, py: isDialog ? 0.5 : 1 }}\n              >\n                <Typography variant=\"subtitle2\" sx={{ fontSize: isDialog ? '0.8rem' : '0.875rem' }}>\n                  Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\n                </Typography>\n              </Alert>\n\n              {/* Mostra avvisi specifici */}\n              {formWarnings.network_error && (\n                <Alert\n                  severity=\"error\"\n                  sx={{ mb: isDialog ? 2 : 3, py: isDialog ? 0.5 : 1 }}\n                >\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold' }}>\n                    {formWarnings.network_error}\n                  </Typography>\n                </Alert>\n              )}\n            </>\n          )}\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Informazioni Generali\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.id_cavo}\n                  helperText={formErrors.id_cavo}\n                  inputProps={{ style: { textTransform: 'uppercase' } }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"revisione_ufficiale\"\n                  label=\"Revisione Ufficiale\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.revisione_ufficiale}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.revisione_ufficiale}\n                  helperText={formErrors.revisione_ufficiale}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sistema\"\n                  label=\"Sistema\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sistema}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sistema}\n                  helperText={formErrors.sistema}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: 3, mb: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Caratteristiche Tecniche\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"colore_cavo\"\n                  label=\"Colore Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.colore_cavo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.colore_cavo}\n                  helperText={formErrors.colore_cavo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"Numero Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_conduttori}\n                  helperText={formErrors.n_conduttori || formWarnings.n_conduttori}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.n_conduttori ? 'orange' : undefined }\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione || formWarnings.sezione}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.sezione ? 'orange' : undefined }\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <FormControl fullWidth error={!!formErrors.sh}>\n                  <InputLabel id=\"sh-label\">Schermato (S/N)</InputLabel>\n                  <Select\n                    labelId=\"sh-label\"\n                    name=\"sh\"\n                    value={formData.sh}\n                    label=\"Schermato (S/N)\"\n                    onChange={handleFormChange}\n                  >\n                    <MenuItem value=\"S\">S</MenuItem>\n                    <MenuItem value=\"N\">N</MenuItem>\n                  </Select>\n                  {formErrors.sh && <FormHelperText>{formErrors.sh}</FormHelperText>}\n                </FormControl>\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Ubicazione Partenza\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_partenza\"\n                  label=\"Ubicazione Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_partenza}\n                  helperText={formErrors.ubicazione_partenza}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_partenza\"\n                  label=\"Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_partenza}\n                  helperText={formErrors.utenza_partenza}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_partenza\"\n                  label=\"Descrizione Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_partenza}\n                  helperText={formErrors.descrizione_utenza_partenza}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Ubicazione Arrivo\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_arrivo\"\n                  label=\"Ubicazione Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_arrivo}\n                  helperText={formErrors.ubicazione_arrivo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_arrivo\"\n                  label=\"Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_arrivo}\n                  helperText={formErrors.utenza_arrivo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_arrivo\"\n                  label=\"Descrizione Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_arrivo}\n                  helperText={formErrors.descrizione_utenza_arrivo}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Metratura\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_teorici\"\n                  label=\"Metri Teorici\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_teorici}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_teorici}\n                  helperText={formErrors.metri_teorici}\n                />\n              </Grid>\n\n            </Grid>\n          </Paper>\n\n          {!isDialog && (\n            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 2 }}>\n              <Button\n                variant=\"outlined\"\n                color=\"secondary\"\n                size=\"large\"\n                onClick={handleCancel}\n                disabled={loading}\n                sx={{ minWidth: 150 }}\n              >\n                Annulla\n              </Button>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                color=\"primary\"\n                size=\"large\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n                sx={{ minWidth: 150 }}\n              >\n                {loading ? <CircularProgress size={24} /> : 'Salva Cavo'}\n              </Button>\n            </Box>\n          )}\n          {isDialog && (\n            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                color=\"primary\"\n                size=\"medium\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n                sx={{ minWidth: 120 }}\n              >\n                {loading ? <CircularProgress size={20} /> : 'Salva'}\n              </Button>\n            </Box>\n          )}\n        </>\n      )}\n    </Box>\n  );\n};\n\nexport default AggiungiCavoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SAASC,IAAI,IAAIC,QAAQ,EAAEC,OAAO,IAAIC,WAAW,QAAQ,qBAAqB;AAC9E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AACtF,SAASC,wBAAwB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvE,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,QAAQ,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACjF,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC;IACvC4C,OAAO,EAAE,EAAE;IACXC,mBAAmB,EAAE,EAAE;IACvBC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,EAAE,EAAE,EAAE;IACNC,mBAAmB,EAAE,EAAE;IACvBC,eAAe,EAAE,EAAE;IACnBC,2BAA2B,EAAE,EAAE;IAC/BC,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE,EAAE;IACjBC,yBAAyB,EAAE,EAAE;IAC7BC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,GAAG;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,mBAAmB,EAAE;EACvB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmE,qBAAqB,GAAG,MAAAA,CAAA,KAAY;MACxC,IAAI;QACF3B,mBAAmB,CAAC,IAAI,CAAC;QACzB,MAAM4B,SAAS,GAAG,MAAM/C,WAAW,CAACgD,oBAAoB,CAACtC,UAAU,CAAC;QACpEW,WAAW,CAAC4B,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE1B,mBAAmB,EAAEwB;QAAU,CAAC,CAAC,CAAC;MACpE,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;QACxEtC,OAAO,CAAC,iDAAiD,CAAC;MAC5D,CAAC,SAAS;QACRO,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAED2B,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,CAACpC,UAAU,EAAEE,OAAO,CAAC,CAAC;;EAEzB;EACA,MAAMwC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACAnC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACkC,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,MAAME,gBAAgB,GAAG,CAAC,CAAC;IAC3B,IAAIH,IAAI,KAAK,iBAAiB,EAAE;MAC9BG,gBAAgB,CAACC,YAAY,GAAGC,UAAU,CAACvC,QAAQ,CAACiB,aAAa,IAAI,CAAC,CAAC;IACzE;IAEA,MAAMuB,MAAM,GAAG1D,aAAa,CAACoD,IAAI,EAAEC,KAAK,EAAEE,gBAAgB,CAAC;;IAE3D;IACAd,aAAa,CAACM,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACK,IAAI,GAAG,CAACM,MAAM,CAACC,KAAK,GAAGD,MAAM,CAACE,OAAO,GAAG;IAC3C,CAAC,CAAC,CAAC;;IAEH;IACAjB,eAAe,CAACI,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACK,IAAI,GAAGM,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACE,OAAO,GAAG;IAC5C,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInD,QAAQ,EAAE;MACZ;MACA;IACF;IACA;IACAT,wBAAwB,CAACW,QAAQ,CAAC;EACpC,CAAC;;EAED;EACA,MAAMkD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAC7C,QAAQ,CAACE,OAAO,EAAE;MACrBV,OAAO,CAAC,0CAA0C,CAAC;MACnD;IACF;IAEAK,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAM2C,MAAM,GAAG,MAAM5D,WAAW,CAACkE,SAAS,CAACxD,UAAU,EAAEU,QAAQ,CAACE,OAAO,CAAC6C,WAAW,CAAC,CAAC,CAAC;MAEtF,IAAIP,MAAM,CAACQ,kBAAkB,EAAE;QAC7BxD,OAAO,CAAC,WAAWQ,QAAQ,CAACE,OAAO,CAAC6C,WAAW,CAAC,CAAC,mDAAmD,CAAC;MACvG,CAAC,MAAM,IAAIP,MAAM,CAACS,iBAAiB,EAAE;QACnCzD,OAAO,CAAC,WAAWQ,QAAQ,CAACE,OAAO,CAAC6C,WAAW,CAAC,CAAC,kDAAkD,CAAC;MACtG,CAAC,MAAM;QACLxD,SAAS,CAAC,WAAWS,QAAQ,CAACE,OAAO,CAAC6C,WAAW,CAAC,CAAC,6DAA6D,CAAC;MACnH;IACF,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DtC,OAAO,CAAC,yDAAyD,CAAC;IACpE,CAAC,SAAS;MACRK,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqD,YAAY,GAAG,MAAOjB,CAAC,IAAK;IAChCA,CAAC,CAACkB,cAAc,CAAC,CAAC;IAClBtD,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACAkC,OAAO,CAACqB,GAAG,CAAC,wCAAwC,EAAEpD,QAAQ,CAAC;MAC/D,MAAMqD,UAAU,GAAGxE,gBAAgB,CAACmB,QAAQ,CAAC;MAC7C+B,OAAO,CAACqB,GAAG,CAAC,wBAAwB,EAAEC,UAAU,CAAC;MAEjD,IAAI,CAACA,UAAU,CAACC,OAAO,EAAE;QACvBvB,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEuB,UAAU,CAACE,MAAM,CAAC;QAC1DhC,aAAa,CAAC8B,UAAU,CAACE,MAAM,CAAC;QAChC9B,eAAe,CAAC4B,UAAU,CAACG,QAAQ,CAAC;QACpC3D,UAAU,CAAC,KAAK,CAAC;QACjBL,OAAO,CAAC,kEAAkE,CAAC;QAC3E;MACF;;MAEA;MACA,MAAMiE,aAAa,GAAGJ,UAAU,CAACI,aAAa;;MAE9C;MACAA,aAAa,CAACvD,OAAO,GAAGuD,aAAa,CAACvD,OAAO,CAAC6C,WAAW,CAAC,CAAC;;MAE3D;MACA;MACA;;MAEA;MACA,MAAMW,cAAc,GAAG,CACrB,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,EAC7E,qBAAqB,EAAE,mBAAmB,EAAE,qBAAqB,CAClE;MAED,MAAMC,aAAa,GAAGD,cAAc,CAACE,MAAM,CAACC,KAAK,IAAI,CAACJ,aAAa,CAACI,KAAK,CAAC,CAAC;MAC3E,IAAIF,aAAa,CAACG,MAAM,GAAG,CAAC,EAAE;QAC5B,MAAM,IAAIC,KAAK,CAAC,+BAA+BJ,aAAa,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAC5E;;MAEA;MACA,MAAMC,UAAU,GAAG;QACjB,GAAGR,aAAa;QAChB;QACAvD,OAAO,EAAEuD,aAAa,CAACvD,OAAO,CAAC6C,WAAW,CAAC,CAAC;QAC5C1C,OAAO,EAAEoD,aAAa,CAACpD,OAAO;QAC9BE,SAAS,EAAEkD,aAAa,CAAClD,SAAS;QAClCC,YAAY,EAAEiD,aAAa,CAACjD,YAAY,GAAGiD,aAAa,CAACjD,YAAY,CAAC0D,QAAQ,CAAC,CAAC,GAAG,GAAG;QAAE;QACxFzD,OAAO,EAAEgD,aAAa,CAAChD,OAAO,GAAGgD,aAAa,CAAChD,OAAO,CAACyD,QAAQ,CAAC,CAAC,GAAG,GAAG;QAAE;QACzEjD,aAAa,EAAEsB,UAAU,CAACkB,aAAa,CAACxC,aAAa,CAAC,IAAI,CAAC;QAC3DN,mBAAmB,EAAE8C,aAAa,CAAC9C,mBAAmB;QACtDG,iBAAiB,EAAE2C,aAAa,CAAC3C,iBAAiB;QAClDO,mBAAmB,EAAEoC,aAAa,CAACpC,mBAAmB,IAAI,eAAe;QAEzE;QACAH,eAAe,EAAEuC,aAAa,CAACvC,eAAe,GAAGqB,UAAU,CAACkB,aAAa,CAACvC,eAAe,CAAC,GAAG,IAAI;QACjGE,SAAS,EAAEqC,aAAa,CAACrC,SAAS,IAAI,IAAI;QAE1C;QACAhB,OAAO,EAAEqD,aAAa,CAACrD,OAAO,IAAI,IAAI;QACtCE,WAAW,EAAEmD,aAAa,CAACnD,WAAW,IAAI,IAAI;QAC9CM,eAAe,EAAE6C,aAAa,CAAC7C,eAAe,IAAI,IAAI;QACtDG,aAAa,EAAE0C,aAAa,CAAC1C,aAAa,IAAI,IAAI;QAClDF,2BAA2B,EAAE4C,aAAa,CAAC5C,2BAA2B,IAAI,IAAI;QAC9EG,yBAAyB,EAAEyC,aAAa,CAACzC,yBAAyB,IAAI,IAAI;QAC1EN,EAAE,EAAE+C,aAAa,CAAC/C,EAAE,IAAI,GAAG;QAC3BS,iBAAiB,EAAEsC,aAAa,CAACtC,iBAAiB,IAAI,IAAI;QAC1DgD,IAAI,EAAEV,aAAa,CAACU,IAAI,IAAI;MAC9B,CAAC;MAEDpC,OAAO,CAACqB,GAAG,CAAC,gDAAgD,EAAEa,UAAU,CAAC;MAEzE,IAAI;QACF;QACAlC,OAAO,CAACqB,GAAG,CAAC,sCAAsC,CAAC;;QAEnD;QACA,IAAI,CAAC9D,UAAU,EAAE;UACf,MAAM,IAAIyE,KAAK,CAAC,mCAAmC,CAAC;QACtD;;QAEA;QACA,MAAMK,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI,CAACF,KAAK,EAAE;UACV,MAAM,IAAIL,KAAK,CAAC,iEAAiE,CAAC;QACpF;;QAEA;QACAhC,OAAO,CAACqB,GAAG,CAAC,kCAAkCa,UAAU,CAAC/D,OAAO,gBAAgB,CAAC;QACjF,IAAI;UACF,MAAMqE,aAAa,GAAG,MAAM7F,KAAK,CAAC8F,GAAG,CAAC,GAAG7F,MAAM,CAAC8F,OAAO,SAASnF,UAAU,UAAU2E,UAAU,CAAC/D,OAAO,EAAE,EAAE;YACxGwE,OAAO,EAAE;cACP,eAAe,EAAE,UAAUN,KAAK;YAClC,CAAC;YACDO,OAAO,EAAE,KAAK,CAAC;UACjB,CAAC,CAAC;UAEF,IAAIJ,aAAa,CAACK,IAAI,IAAIL,aAAa,CAACK,IAAI,CAACC,MAAM,EAAE;YACnD,MAAM,IAAId,KAAK,CAAC,WAAWE,UAAU,CAAC/D,OAAO,8CAA8C,CAAC;UAC9F;UACA6B,OAAO,CAACqB,GAAG,CAAC,gCAAgCa,UAAU,CAAC/D,OAAO,2BAA2B,CAAC;QAC5F,CAAC,CAAC,OAAO4E,UAAU,EAAE;UACnB;UACA;UACA,IAAIA,UAAU,CAACC,QAAQ,IAAID,UAAU,CAACC,QAAQ,CAACH,IAAI,EAAE;YACnD,IAAIE,UAAU,CAACC,QAAQ,CAACH,IAAI,CAACC,MAAM,EAAE;cACnC,MAAM,IAAId,KAAK,CAAC,WAAWE,UAAU,CAAC/D,OAAO,8CAA8C,CAAC;YAC9F;UACF,CAAC,MAAM,IAAI,CAAC4E,UAAU,CAACC,QAAQ,IAAID,UAAU,CAACC,QAAQ,CAACC,MAAM,KAAK,GAAG,EAAE;YACrE;YACAjD,OAAO,CAACkD,IAAI,CAAC,sCAAsC,EAAEH,UAAU,CAAC;YAChE/C,OAAO,CAACqB,GAAG,CAAC,4CAA4C,CAAC;UAC3D;QACF;QAEArB,OAAO,CAACqB,GAAG,CAAC,gCAAgC9D,UAAU,EAAE,CAAC;QACzDyC,OAAO,CAACqB,GAAG,CAAC,eAAe,EAAE8B,IAAI,CAACC,SAAS,CAAClB,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAEjE,MAAMc,QAAQ,GAAG,MAAMrG,KAAK,CAAC0G,IAAI,CAAC,GAAGzG,MAAM,CAAC8F,OAAO,SAASnF,UAAU,EAAE,EAAE2E,UAAU,EAAE;UACpFS,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUN,KAAK;UAClC,CAAC;UACDO,OAAO,EAAE,KAAK;UAAE;UAChBU,cAAc,EAAE,SAAAA,CAAUL,MAAM,EAAE;YAChC;YACA,OAAO,IAAI;UACb;QACF,CAAC,CAAC;;QAEF;QACAjD,OAAO,CAACqB,GAAG,CAAC,cAAc,EAAE2B,QAAQ,CAACC,MAAM,CAAC;QAC5CjD,OAAO,CAACqB,GAAG,CAAC,UAAU,EAAE2B,QAAQ,CAACL,OAAO,CAAC;QACzC3C,OAAO,CAACqB,GAAG,CAAC,oBAAoB,EAAE2B,QAAQ,CAACH,IAAI,CAAC;;QAEhD;QACA,IAAIG,QAAQ,CAACC,MAAM,IAAI,GAAG,EAAE;UAC1B,IAAIM,WAAW,GAAGP,QAAQ,CAACH,IAAI,CAACW,MAAM,IAAI,UAAUR,QAAQ,CAACC,MAAM,KAAKD,QAAQ,CAACS,UAAU,EAAE;UAC7FzD,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEwD,WAAW,CAAC;;UAEhD;UACA,IAAIP,QAAQ,CAACC,MAAM,KAAK,GAAG,EAAE;YAC3B;YACAjD,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEiD,QAAQ,CAACH,IAAI,CAAC;;YAEtD;YACA,IAAIU,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAIA,WAAW,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;cACrGJ,WAAW,GAAG,6BAA6BrB,UAAU,CAAC/D,OAAO,sBAAsB;YACrF,CAAC,MAAM,IAAIoF,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAIA,WAAW,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;cACzGJ,WAAW,GAAG,6BAA6BrB,UAAU,CAAC/D,OAAO,sBAAsB;YACrF,CAAC,MAAM,IAAIoF,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAIA,WAAW,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;cACjHJ,WAAW,GAAG,6BAA6BrB,UAAU,CAAC/D,OAAO,sBAAsB;YACrF,CAAC,MAAM,IAAIoF,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,IAAIA,WAAW,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;cAC7GJ,WAAW,GAAG,2GAA2G;YAC3H;;YAEA;YACA,IAAIP,QAAQ,CAACH,IAAI,IAAIG,QAAQ,CAACH,IAAI,CAACW,MAAM,IAAII,KAAK,CAACC,OAAO,CAACb,QAAQ,CAACH,IAAI,CAACW,MAAM,CAAC,EAAE;cAChF,MAAMM,gBAAgB,GAAGd,QAAQ,CAACH,IAAI,CAACW,MAAM;cAC7C,MAAMO,aAAa,GAAGD,gBAAgB,CAACE,GAAG,CAACC,GAAG,IAAI;gBAChD,MAAMnC,KAAK,GAAGmC,GAAG,CAACC,GAAG,IAAID,GAAG,CAACC,GAAG,CAACnC,MAAM,GAAG,CAAC,GAAGkC,GAAG,CAACC,GAAG,CAAC,CAAC,CAAC,GAAG,mBAAmB;gBAC9E,OAAO,GAAGpC,KAAK,KAAKmC,GAAG,CAACE,GAAG,EAAE;cAC/B,CAAC,CAAC,CAAClC,IAAI,CAAC,IAAI,CAAC;cAEbsB,WAAW,GAAG,2BAA2BQ,aAAa,EAAE;YAC1D;UACF;UAEA,MAAM,IAAI/B,KAAK,CAACuB,WAAW,CAAC;QAC9B;QAEAvD,OAAO,CAACqB,GAAG,CAAC,sBAAsB,EAAE2B,QAAQ,CAACH,IAAI,CAAC;QAClDrF,SAAS,CAAC,4BAA4B,CAAC;;QAEvC;QACA,IAAI,CAACE,QAAQ,EAAE;UACbsC,OAAO,CAACqB,GAAG,CAAC,uCAAuC,CAAC;UACpD;UACA+C,UAAU,CAAC,MAAM;YACf,IAAI;cACFC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuBhH,UAAU,kBAAkB;YAC5E,CAAC,CAAC,OAAOiH,QAAQ,EAAE;cACjBxE,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEyE,QAAQ,CAAC;cAC9D;cACAH,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC;YAC1B;UACF,CAAC,EAAE,IAAI,CAAC;QACV;MACF,CAAC,CAAC,OAAO1E,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;QACnE,IAAI2E,YAAY,GAAG,qCAAqC;QACxD,IAAIC,cAAc,GAAG,KAAK;;QAE1B;QACA,IAAI5E,KAAK,CAACY,OAAO,KAAKZ,KAAK,CAACY,OAAO,CAACgD,QAAQ,CAAC,eAAe,CAAC,IAAI5D,KAAK,CAACY,OAAO,CAACgD,QAAQ,CAAC,iBAAiB,CAAC,CAAC,EAAE;UAC3GgB,cAAc,GAAG,IAAI;UACrB3E,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;UAExD;UACAtC,OAAO,CAAC,yDAAyD,CAAC;;UAElE;UACA2G,UAAU,CAAC,YAAY;YACrB,IAAI;cACF;cACApE,OAAO,CAACqB,GAAG,CAAC,kCAAkCa,UAAU,CAAC/D,OAAO,sBAAsB,CAAC;cACvF,MAAMkE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAE3C,MAAMC,aAAa,GAAG,MAAM7F,KAAK,CAAC8F,GAAG,CAAC,GAAG7F,MAAM,CAAC8F,OAAO,SAASnF,UAAU,UAAU2E,UAAU,CAAC/D,OAAO,EAAE,EAAE;gBACxGwE,OAAO,EAAE;kBACP,eAAe,EAAE,UAAUN,KAAK;gBAClC,CAAC;gBACDO,OAAO,EAAE,KAAK,CAAC;cACjB,CAAC,CAAC;cAEF,IAAIJ,aAAa,CAACK,IAAI,IAAIL,aAAa,CAACK,IAAI,CAACC,MAAM,EAAE;gBACnD;gBACAtF,SAAS,CAAC,WAAW0E,UAAU,CAAC/D,OAAO,6DAA6D,CAAC;;gBAErG;gBACAD,WAAW,CAAC;kBACVC,OAAO,EAAE,EAAE;kBACXC,mBAAmB,EAAEH,QAAQ,CAACG,mBAAmB;kBAAE;kBACnDC,OAAO,EAAE,EAAE;kBACXC,OAAO,EAAE,EAAE;kBACXC,WAAW,EAAE,EAAE;kBACfC,SAAS,EAAE,EAAE;kBACbC,YAAY,EAAE,EAAE;kBAChBC,OAAO,EAAE,EAAE;kBACXC,EAAE,EAAE,EAAE;kBACNC,mBAAmB,EAAE,EAAE;kBACvBC,eAAe,EAAE,EAAE;kBACnBC,2BAA2B,EAAE,EAAE;kBAC/BC,iBAAiB,EAAE,EAAE;kBACrBC,aAAa,EAAE,EAAE;kBACjBC,yBAAyB,EAAE,EAAE;kBAC7BC,aAAa,EAAE,EAAE;kBACjBC,eAAe,EAAE,GAAG;kBACpBC,iBAAiB,EAAE,EAAE;kBACrBC,SAAS,EAAE,EAAE;kBACbC,mBAAmB,EAAE;gBACvB,CAAC,CAAC;gBACFE,aAAa,CAAC,CAAC,CAAC,CAAC;gBACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;cACrB,CAAC,MAAM;gBACL;gBACAjC,OAAO,CAAC,kFAAkF,CAAC;cAC7F;YACF,CAAC,CAAC,OAAOsF,UAAU,EAAE;cACnB;cACA/C,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEgD,UAAU,CAAC;cACnEtF,OAAO,CAAC,4EAA4E,CAAC;YACvF,CAAC,SAAS;cACRK,UAAU,CAAC,KAAK,CAAC;YACnB;UACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;UAEV,OAAO,CAAC;QACV;QACA;QAAA,KACK,IAAIiC,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC3C,IAAIA,KAAK,CAACiD,QAAQ,IAAIjD,KAAK,CAACiD,QAAQ,CAACH,IAAI,EAAE;YACzC;YACA,MAAM+B,YAAY,GAAG7E,KAAK,CAACiD,QAAQ,CAACH,IAAI;YACxC,IAAI+B,YAAY,CAACpB,MAAM,EAAE;cACvBkB,YAAY,GAAGE,YAAY,CAACpB,MAAM;YACpC,CAAC,MAAM,IAAI,OAAOoB,YAAY,KAAK,QAAQ,EAAE;cAC3CF,YAAY,GAAGE,YAAY;YAC7B,CAAC,MAAM;cACLF,YAAY,GAAGvB,IAAI,CAACC,SAAS,CAACwB,YAAY,CAAC;YAC7C;UACF,CAAC,MAAM,IAAI7E,KAAK,CAACyD,MAAM,EAAE;YACvBkB,YAAY,GAAG3E,KAAK,CAACyD,MAAM;UAC7B,CAAC,MAAM,IAAIzD,KAAK,CAACY,OAAO,EAAE;YACxB+D,YAAY,GAAG3E,KAAK,CAACY,OAAO;UAC9B,CAAC,MAAM,IAAIZ,KAAK,CAACA,KAAK,EAAE;YACtB2E,YAAY,GAAG3E,KAAK,CAACA,KAAK;UAC5B;QACF,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UACpC2E,YAAY,GAAG3E,KAAK;QACtB;;QAEA;QACAtC,OAAO,CAACiH,YAAY,CAAC;QACrB5G,UAAU,CAAC,KAAK,CAAC;;QAEjB;QACAI,WAAW,CAAC;UACVC,OAAO,EAAE,EAAE;UACXC,mBAAmB,EAAEH,QAAQ,CAACG,mBAAmB;UAAE;UACnDC,OAAO,EAAE,EAAE;UACXC,OAAO,EAAE,EAAE;UACXC,WAAW,EAAE,EAAE;UACfC,SAAS,EAAE,EAAE;UACbC,YAAY,EAAE,EAAE;UAChBC,OAAO,EAAE,EAAE;UACXC,EAAE,EAAE,EAAE;UACNC,mBAAmB,EAAE,EAAE;UACvBC,eAAe,EAAE,EAAE;UACnBC,2BAA2B,EAAE,EAAE;UAC/BC,iBAAiB,EAAE,EAAE;UACrBC,aAAa,EAAE,EAAE;UACjBC,yBAAyB,EAAE,EAAE;UAC7BC,aAAa,EAAE,EAAE;UACjBC,eAAe,EAAE,GAAG;UACpBC,iBAAiB,EAAE,EAAE;UACrBC,SAAS,EAAE,EAAE;UACbC,mBAAmB,EAAE;QACvB,CAAC,CAAC;QACFE,aAAa,CAAC,CAAC,CAAC,CAAC;QACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;MACrB;;MAEA;MACAxB,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,mBAAmB,EAAEH,QAAQ,CAACG,mBAAmB;QAAE;QACnDC,OAAO,EAAE,EAAE;QACXC,OAAO,EAAE,EAAE;QACXC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,EAAE;QACXC,EAAE,EAAE,EAAE;QACNC,mBAAmB,EAAE,EAAE;QACvBC,eAAe,EAAE,EAAE;QACnBC,2BAA2B,EAAE,EAAE;QAC/BC,iBAAiB,EAAE,EAAE;QACrBC,aAAa,EAAE,EAAE;QACjBC,yBAAyB,EAAE,EAAE;QAC7BC,aAAa,EAAE,EAAE;QACjBC,eAAe,EAAE,GAAG;QACpBC,iBAAiB,EAAE,EAAE;QACrBC,SAAS,EAAE,EAAE;QACbC,mBAAmB,EAAE;MACvB,CAAC,CAAC;MACFE,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DtC,OAAO,CAACsC,KAAK,CAACyD,MAAM,IAAI,qCAAqC,CAAC;IAChE,CAAC,SAAS;MACR1F,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+G,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACtF,YAAY,CAAC,CAACsC,MAAM,GAAG,CAAC;EAExD,oBACE5E,OAAA,CAAC1B,GAAG;IAACuJ,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAE9D,YAAa;IAAC+D,UAAU;IAAAC,QAAA,EACrDpH,gBAAgB,gBACfZ,OAAA,CAAC1B,GAAG;MAAC2J,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAC5DhI,OAAA,CAAChB,gBAAgB;QAAAqJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,gBAENxI,OAAA,CAAAE,SAAA;MAAA8H,QAAA,GACGN,WAAW,iBACV1H,OAAA,CAAAE,SAAA;QAAA8H,QAAA,gBACEhI,OAAA,CAACjB,KAAK;UACJ0J,QAAQ,EAAC,SAAS;UAClBC,IAAI,eAAE1I,OAAA,CAACV,WAAW;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBP,EAAE,EAAE;YAAEU,EAAE,EAAEpI,QAAQ,GAAG,CAAC,GAAG,CAAC;YAAEqI,EAAE,EAAErI,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAyH,QAAA,eAErDhI,OAAA,CAACf,UAAU;YAAC4J,OAAO,EAAC,WAAW;YAACZ,EAAE,EAAE;cAAEa,QAAQ,EAAEvI,QAAQ,GAAG,QAAQ,GAAG;YAAW,CAAE;YAAAyH,QAAA,EAAC;UAEpF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAGPlG,YAAY,CAACyG,aAAa,iBACzB/I,OAAA,CAACjB,KAAK;UACJ0J,QAAQ,EAAC,OAAO;UAChBR,EAAE,EAAE;YAAEU,EAAE,EAAEpI,QAAQ,GAAG,CAAC,GAAG,CAAC;YAAEqI,EAAE,EAAErI,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAyH,QAAA,eAErDhI,OAAA,CAACf,UAAU;YAAC4J,OAAO,EAAC,WAAW;YAACZ,EAAE,EAAE;cAAEe,UAAU,EAAE;YAAO,CAAE;YAAAhB,QAAA,EACxD1F,YAAY,CAACyG;UAAa;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR;MAAA,eACD,CACH,eAEDxI,OAAA,CAACd,KAAK;QAAC+I,EAAE,EAAE;UAAEgB,CAAC,EAAE1I,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEoI,EAAE,EAAEpI,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAE2I,SAAS,EAAE3I,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAyH,QAAA,gBACpFhI,OAAA,CAACf,UAAU;UAAC4J,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAEvI,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAyH,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxI,OAAA,CAACvB,IAAI;UAAC2K,SAAS;UAACC,OAAO,EAAE9I,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAyH,QAAA,gBACxChI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACdyG,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACE,OAAQ;cACxB2I,QAAQ,EAAE7G,gBAAiB;cAC3B8G,QAAQ;cACRhH,KAAK,EAAE,CAAC,CAACR,UAAU,CAACpB,OAAQ;cAC5B6I,UAAU,EAAEzH,UAAU,CAACpB,OAAQ;cAC/B8I,UAAU,EAAE;gBAAEC,KAAK,EAAE;kBAAEC,aAAa,EAAE;gBAAY;cAAE;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,qBAAqB;cAC1ByG,KAAK,EAAC,qBAAqB;cAC3BC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACG,mBAAoB;cACpC0I,QAAQ,EAAE7G,gBAAiB;cAC3B8G,QAAQ;cACRhH,KAAK,EAAE,CAAC,CAACR,UAAU,CAACnB,mBAAoB;cACxC4I,UAAU,EAAEzH,UAAU,CAACnB;YAAoB;cAAAoH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACdyG,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACI,OAAQ;cACxByI,QAAQ,EAAE7G,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAAClB,OAAQ;cAC5B2I,UAAU,EAAEzH,UAAU,CAAClB;YAAQ;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACdyG,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACK,OAAQ;cACxBwI,QAAQ,EAAE7G,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACjB,OAAQ;cAC5B0I,UAAU,EAAEzH,UAAU,CAACjB;YAAQ;cAAAkH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERxI,OAAA,CAACd,KAAK;QAAC+I,EAAE,EAAE;UAAEgB,CAAC,EAAE,CAAC;UAAEN,EAAE,EAAE;QAAE,CAAE;QAAAX,QAAA,gBACzBhI,OAAA,CAACf,UAAU;UAAC4J,OAAO,EAAC,IAAI;UAACM,YAAY;UAAAnB,QAAA,EAAC;QAEtC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxI,OAAA,CAACvB,IAAI;UAAC2K,SAAS;UAACC,OAAO,EAAE,CAAE;UAAArB,QAAA,gBACzBhI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,aAAa;cAClByG,KAAK,EAAC,aAAa;cACnBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACM,WAAY;cAC5BuI,QAAQ,EAAE7G,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAAChB,WAAY;cAChCyI,UAAU,EAAEzH,UAAU,CAAChB;YAAY;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,WAAW;cAChByG,KAAK,EAAC,WAAW;cACjBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACO,SAAU;cAC1BsI,QAAQ,EAAE7G,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACf,SAAU;cAC9BwI,UAAU,EAAEzH,UAAU,CAACf;YAAU;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,cAAc;cACnByG,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACQ,YAAa;cAC7BqI,QAAQ,EAAE7G,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACd,YAAa;cACjCuI,UAAU,EAAEzH,UAAU,CAACd,YAAY,IAAIgB,YAAY,CAAChB,YAAa;cACjE2I,mBAAmB,EAAE;gBACnBF,KAAK,EAAE;kBAAEG,KAAK,EAAE5H,YAAY,CAAChB,YAAY,GAAG,QAAQ,GAAG6I;gBAAU;cACnE;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACdyG,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACS,OAAQ;cACxBoI,QAAQ,EAAE7G,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACb,OAAQ;cAC5BsI,UAAU,EAAEzH,UAAU,CAACb,OAAO,IAAIe,YAAY,CAACf,OAAQ;cACvD0I,mBAAmB,EAAE;gBACnBF,KAAK,EAAE;kBAAEG,KAAK,EAAE5H,YAAY,CAACf,OAAO,GAAG,QAAQ,GAAG4I;gBAAU;cAC9D;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACtB,WAAW;cAACgL,SAAS;cAAC9G,KAAK,EAAE,CAAC,CAACR,UAAU,CAACZ,EAAG;cAAAwG,QAAA,gBAC5ChI,OAAA,CAACrB,UAAU;gBAACyL,EAAE,EAAC,UAAU;gBAAApC,QAAA,EAAC;cAAe;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtDxI,OAAA,CAACpB,MAAM;gBACLyL,OAAO,EAAC,UAAU;gBAClBrH,IAAI,EAAC,IAAI;gBACTC,KAAK,EAAEnC,QAAQ,CAACU,EAAG;gBACnBiI,KAAK,EAAC,iBAAiB;gBACvBE,QAAQ,EAAE7G,gBAAiB;gBAAAkF,QAAA,gBAE3BhI,OAAA,CAACnB,QAAQ;kBAACoE,KAAK,EAAC,GAAG;kBAAA+E,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAChCxI,OAAA,CAACnB,QAAQ;kBAACoE,KAAK,EAAC,GAAG;kBAAA+E,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,EACRpG,UAAU,CAACZ,EAAE,iBAAIxB,OAAA,CAAClB,cAAc;gBAAAkJ,QAAA,EAAE5F,UAAU,CAACZ;cAAE;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERxI,OAAA,CAACd,KAAK;QAAC+I,EAAE,EAAE;UAAEgB,CAAC,EAAE1I,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEoI,EAAE,EAAEpI,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAE2I,SAAS,EAAE3I,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAyH,QAAA,gBACpFhI,OAAA,CAACf,UAAU;UAAC4J,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAEvI,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAyH,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxI,OAAA,CAACvB,IAAI;UAAC2K,SAAS;UAACC,OAAO,EAAE9I,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAyH,QAAA,gBACxChI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,qBAAqB;cAC1ByG,KAAK,EAAC,qBAAqB;cAC3BC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACW,mBAAoB;cACpCkI,QAAQ,EAAE7G,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACX,mBAAoB;cACxCoI,UAAU,EAAEzH,UAAU,CAACX;YAAoB;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,iBAAiB;cACtByG,KAAK,EAAC,iBAAiB;cACvBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACY,eAAgB;cAChCiI,QAAQ,EAAE7G,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACV,eAAgB;cACpCmI,UAAU,EAAEzH,UAAU,CAACV;YAAgB;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,6BAA6B;cAClCyG,KAAK,EAAC,6BAA6B;cACnCC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACa,2BAA4B;cAC5CgI,QAAQ,EAAE7G,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACT,2BAA4B;cAChDkI,UAAU,EAAEzH,UAAU,CAACT;YAA4B;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERxI,OAAA,CAACd,KAAK;QAAC+I,EAAE,EAAE;UAAEgB,CAAC,EAAE1I,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEoI,EAAE,EAAEpI,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAE2I,SAAS,EAAE3I,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAyH,QAAA,gBACpFhI,OAAA,CAACf,UAAU;UAAC4J,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAEvI,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAyH,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxI,OAAA,CAACvB,IAAI;UAAC2K,SAAS;UAACC,OAAO,EAAE9I,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAyH,QAAA,gBACxChI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,mBAAmB;cACxByG,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACc,iBAAkB;cAClC+H,QAAQ,EAAE7G,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACR,iBAAkB;cACtCiI,UAAU,EAAEzH,UAAU,CAACR;YAAkB;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,eAAe;cACpByG,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACe,aAAc;cAC9B8H,QAAQ,EAAE7G,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACP,aAAc;cAClCgI,UAAU,EAAEzH,UAAU,CAACP;YAAc;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPxI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,2BAA2B;cAChCyG,KAAK,EAAC,2BAA2B;cACjCC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACgB,yBAA0B;cAC1C6H,QAAQ,EAAE7G,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACN,yBAA0B;cAC9C+H,UAAU,EAAEzH,UAAU,CAACN;YAA0B;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERxI,OAAA,CAACd,KAAK;QAAC+I,EAAE,EAAE;UAAEgB,CAAC,EAAE1I,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEoI,EAAE,EAAEpI,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAE2I,SAAS,EAAE3I,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAyH,QAAA,gBACpFhI,OAAA,CAACf,UAAU;UAAC4J,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAEvI,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAyH,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbxI,OAAA,CAACvB,IAAI;UAAC2K,SAAS;UAACC,OAAO,EAAE9I,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAyH,QAAA,eACxChI,OAAA,CAACvB,IAAI;YAAC6K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBhI,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,eAAe;cACpByG,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5F,KAAK,EAAEnC,QAAQ,CAACiB,aAAc;cAC9B4H,QAAQ,EAAE7G,gBAAiB;cAC3B8G,QAAQ;cACRhH,KAAK,EAAE,CAAC,CAACR,UAAU,CAACL,aAAc;cAClC8H,UAAU,EAAEzH,UAAU,CAACL;YAAc;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAEP,CAACjI,QAAQ,iBACRP,OAAA,CAAC1B,GAAG;QAAC2J,EAAE,EAAE;UAAEqC,EAAE,EAAE,CAAC;UAAEpC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEoC,GAAG,EAAE;QAAE,CAAE;QAAAvC,QAAA,gBACpEhI,OAAA,CAACxB,MAAM;UACLqK,OAAO,EAAC,UAAU;UAClBqB,KAAK,EAAC,WAAW;UACjBM,IAAI,EAAC,OAAO;UACZC,OAAO,EAAE/G,YAAa;UACtBgH,QAAQ,EAAEhK,OAAQ;UAClBuH,EAAE,EAAE;YAAE0C,QAAQ,EAAE;UAAI,CAAE;UAAA3C,QAAA,EACvB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxI,OAAA,CAACxB,MAAM;UACLoM,IAAI,EAAC,QAAQ;UACb/B,OAAO,EAAC,WAAW;UACnBqB,KAAK,EAAC,SAAS;UACfM,IAAI,EAAC,OAAO;UACZK,SAAS,eAAE7K,OAAA,CAACZ,QAAQ;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBkC,QAAQ,EAAEhK,OAAQ;UAClBuH,EAAE,EAAE;YAAE0C,QAAQ,EAAE;UAAI,CAAE;UAAA3C,QAAA,EAErBtH,OAAO,gBAAGV,OAAA,CAAChB,gBAAgB;YAACwL,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAY;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EACAjI,QAAQ,iBACPP,OAAA,CAAC1B,GAAG;QAAC2J,EAAE,EAAE;UAAEqC,EAAE,EAAE,CAAC;UAAEpC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEoC,GAAG,EAAE;QAAE,CAAE;QAAAvC,QAAA,eACtEhI,OAAA,CAACxB,MAAM;UACLoM,IAAI,EAAC,QAAQ;UACb/B,OAAO,EAAC,WAAW;UACnBqB,KAAK,EAAC,SAAS;UACfM,IAAI,EAAC,QAAQ;UACbK,SAAS,eAAE7K,OAAA,CAACZ,QAAQ;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBkC,QAAQ,EAAEhK,OAAQ;UAClBuH,EAAE,EAAE;YAAE0C,QAAQ,EAAE;UAAI,CAAE;UAAA3C,QAAA,EAErBtH,OAAO,gBAAGV,OAAA,CAAChB,gBAAgB;YAACwL,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA,eACD;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChI,EAAA,CAtxBIL,gBAAgB;EAAA,QACHZ,WAAW;AAAA;AAAAuL,EAAA,GADxB3K,gBAAgB;AAwxBtB,eAAeA,gBAAgB;AAAC,IAAA2K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}