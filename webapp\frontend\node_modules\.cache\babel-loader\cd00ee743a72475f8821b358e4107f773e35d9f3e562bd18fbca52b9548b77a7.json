{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\CertificazioniPageDebug.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Container, Typography, Box, Paper, Alert } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CertificazioniPageDebug() {\n  _s();\n  const {\n    cantiereId\n  } = useParams();\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [debugInfo, setDebugInfo] = useState([]);\n  useEffect(() => {\n    const debugSteps = async () => {\n      const steps = [];\n      try {\n        steps.push('✅ Componente montato correttamente');\n        steps.push(`✅ cantiereId ricevuto: ${cantiereId}`);\n\n        // Test import servizi\n        try {\n          const {\n            apiService\n          } = await import('../services/apiService');\n          steps.push('✅ apiService importato correttamente');\n\n          // Test metodi apiService\n          if (typeof apiService.getCantiere === 'function') {\n            steps.push('✅ apiService.getCantiere esiste');\n          } else {\n            steps.push('❌ apiService.getCantiere non esiste');\n          }\n          if (typeof apiService.getCertificazioni === 'function') {\n            steps.push('✅ apiService.getCertificazioni esiste');\n          } else {\n            steps.push('❌ apiService.getCertificazioni non esiste');\n          }\n          if (typeof apiService.getStrumenti === 'function') {\n            steps.push('✅ apiService.getStrumenti esiste');\n          } else {\n            steps.push('❌ apiService.getStrumenti non esiste');\n          }\n        } catch (importError) {\n          steps.push(`❌ Errore import apiService: ${importError.message}`);\n        }\n\n        // Test import contesto auth\n        try {\n          const {\n            useAuth\n          } = await import('../context/AuthContext');\n          steps.push('✅ useAuth importato correttamente');\n        } catch (authError) {\n          steps.push(`❌ Errore import useAuth: ${authError.message}`);\n        }\n\n        // Test import componenti\n        try {\n          await import('../components/certificazioni/CertificazioniList');\n          steps.push('✅ CertificazioniList importato correttamente');\n        } catch (listError) {\n          steps.push(`❌ Errore import CertificazioniList: ${listError.message}`);\n        }\n        try {\n          await import('../components/certificazioni/CertificazioneForm');\n          steps.push('✅ CertificazioneForm importato correttamente');\n        } catch (formError) {\n          steps.push(`❌ Errore import CertificazioneForm: ${formError.message}`);\n        }\n        try {\n          await import('../components/certificazioni/StrumentiList');\n          steps.push('✅ StrumentiList importato correttamente');\n        } catch (strListError) {\n          steps.push(`❌ Errore import StrumentiList: ${strListError.message}`);\n        }\n        try {\n          await import('../components/certificazioni/StrumentoForm');\n          steps.push('✅ StrumentoForm importato correttamente');\n        } catch (strFormError) {\n          steps.push(`❌ Errore import StrumentoForm: ${strFormError.message}`);\n        }\n        setDebugInfo(steps);\n        setLoading(false);\n      } catch (error) {\n        steps.push(`❌ Errore generale: ${error.message}`);\n        setError(error.message);\n        setDebugInfo(steps);\n        setLoading(false);\n      }\n    };\n    debugSteps();\n  }, [cantiereId]);\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      mt: 4,\n      mb: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        children: \"Debug Certificazioni Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: [\"Cantiere ID: \", cantiereId]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: [\"Errore: \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Risultati Debug\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"Esecuzione test...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        children: debugInfo.map((step, index) => /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mb: 1,\n            fontFamily: 'monospace'\n          },\n          children: step\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n}\n_s(CertificazioniPageDebug, \"E5uYehBL5NprDo1plSZGnYVWORQ=\", false, function () {\n  return [useParams];\n});\n_c = CertificazioniPageDebug;\nexport default CertificazioniPageDebug;\nvar _c;\n$RefreshReg$(_c, \"CertificazioniPageDebug\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Container", "Typography", "Box", "Paper", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "CertificazioniPageDebug", "_s", "cantiereId", "error", "setError", "loading", "setLoading", "debugInfo", "setDebugInfo", "debugSteps", "steps", "push", "apiService", "getCantiere", "getCertificazioni", "getStrumenti", "importError", "message", "useAuth", "authError", "listError", "formError", "strListError", "strFormError", "max<PERSON><PERSON><PERSON>", "sx", "mt", "mb", "children", "variant", "component", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "severity", "p", "map", "step", "index", "fontFamily", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/CertificazioniPageDebug.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Container, Typography, Box, Paper, Alert } from '@mui/material';\n\nfunction CertificazioniPageDebug() {\n  const { cantiereId } = useParams();\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [debugInfo, setDebugInfo] = useState([]);\n\n  useEffect(() => {\n    const debugSteps = async () => {\n      const steps = [];\n      \n      try {\n        steps.push('✅ Componente montato correttamente');\n        steps.push(`✅ cantiereId ricevuto: ${cantiereId}`);\n        \n        // Test import servizi\n        try {\n          const { apiService } = await import('../services/apiService');\n          steps.push('✅ apiService importato correttamente');\n          \n          // Test metodi apiService\n          if (typeof apiService.getCantiere === 'function') {\n            steps.push('✅ apiService.getCantiere esiste');\n          } else {\n            steps.push('❌ apiService.getCantiere non esiste');\n          }\n          \n          if (typeof apiService.getCertificazioni === 'function') {\n            steps.push('✅ apiService.getCertificazioni esiste');\n          } else {\n            steps.push('❌ apiService.getCertificazioni non esiste');\n          }\n          \n          if (typeof apiService.getStrumenti === 'function') {\n            steps.push('✅ apiService.getStrumenti esiste');\n          } else {\n            steps.push('❌ apiService.getStrumenti non esiste');\n          }\n          \n        } catch (importError) {\n          steps.push(`❌ Errore import apiService: ${importError.message}`);\n        }\n        \n        // Test import contesto auth\n        try {\n          const { useAuth } = await import('../context/AuthContext');\n          steps.push('✅ useAuth importato correttamente');\n        } catch (authError) {\n          steps.push(`❌ Errore import useAuth: ${authError.message}`);\n        }\n        \n        // Test import componenti\n        try {\n          await import('../components/certificazioni/CertificazioniList');\n          steps.push('✅ CertificazioniList importato correttamente');\n        } catch (listError) {\n          steps.push(`❌ Errore import CertificazioniList: ${listError.message}`);\n        }\n        \n        try {\n          await import('../components/certificazioni/CertificazioneForm');\n          steps.push('✅ CertificazioneForm importato correttamente');\n        } catch (formError) {\n          steps.push(`❌ Errore import CertificazioneForm: ${formError.message}`);\n        }\n        \n        try {\n          await import('../components/certificazioni/StrumentiList');\n          steps.push('✅ StrumentiList importato correttamente');\n        } catch (strListError) {\n          steps.push(`❌ Errore import StrumentiList: ${strListError.message}`);\n        }\n        \n        try {\n          await import('../components/certificazioni/StrumentoForm');\n          steps.push('✅ StrumentoForm importato correttamente');\n        } catch (strFormError) {\n          steps.push(`❌ Errore import StrumentoForm: ${strFormError.message}`);\n        }\n        \n        setDebugInfo(steps);\n        setLoading(false);\n        \n      } catch (error) {\n        steps.push(`❌ Errore generale: ${error.message}`);\n        setError(error.message);\n        setDebugInfo(steps);\n        setLoading(false);\n      }\n    };\n    \n    debugSteps();\n  }, [cantiereId]);\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ mt: 4, mb: 4 }}>\n      <Box sx={{ mb: 3 }}>\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n          Debug Certificazioni Cavi\n        </Typography>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Cantiere ID: {cantiereId}\n        </Typography>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          Errore: {error}\n        </Alert>\n      )}\n\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Risultati Debug\n        </Typography>\n        \n        {loading ? (\n          <Typography>Esecuzione test...</Typography>\n        ) : (\n          <Box>\n            {debugInfo.map((step, index) => (\n              <Typography key={index} variant=\"body2\" sx={{ mb: 1, fontFamily: 'monospace' }}>\n                {step}\n              </Typography>\n            ))}\n          </Box>\n        )}\n      </Paper>\n    </Container>\n  );\n}\n\nexport default CertificazioniPageDebug;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,SAAS,EAAEC,UAAU,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,SAASC,uBAAuBA,CAAA,EAAG;EAAAC,EAAA;EACjC,MAAM;IAAEC;EAAW,CAAC,GAAGV,SAAS,CAAC,CAAC;EAClC,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAE9CC,SAAS,CAAC,MAAM;IACd,MAAMkB,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,MAAMC,KAAK,GAAG,EAAE;MAEhB,IAAI;QACFA,KAAK,CAACC,IAAI,CAAC,oCAAoC,CAAC;QAChDD,KAAK,CAACC,IAAI,CAAC,0BAA0BT,UAAU,EAAE,CAAC;;QAElD;QACA,IAAI;UACF,MAAM;YAAEU;UAAW,CAAC,GAAG,MAAM,MAAM,CAAC,wBAAwB,CAAC;UAC7DF,KAAK,CAACC,IAAI,CAAC,sCAAsC,CAAC;;UAElD;UACA,IAAI,OAAOC,UAAU,CAACC,WAAW,KAAK,UAAU,EAAE;YAChDH,KAAK,CAACC,IAAI,CAAC,iCAAiC,CAAC;UAC/C,CAAC,MAAM;YACLD,KAAK,CAACC,IAAI,CAAC,qCAAqC,CAAC;UACnD;UAEA,IAAI,OAAOC,UAAU,CAACE,iBAAiB,KAAK,UAAU,EAAE;YACtDJ,KAAK,CAACC,IAAI,CAAC,uCAAuC,CAAC;UACrD,CAAC,MAAM;YACLD,KAAK,CAACC,IAAI,CAAC,2CAA2C,CAAC;UACzD;UAEA,IAAI,OAAOC,UAAU,CAACG,YAAY,KAAK,UAAU,EAAE;YACjDL,KAAK,CAACC,IAAI,CAAC,kCAAkC,CAAC;UAChD,CAAC,MAAM;YACLD,KAAK,CAACC,IAAI,CAAC,sCAAsC,CAAC;UACpD;QAEF,CAAC,CAAC,OAAOK,WAAW,EAAE;UACpBN,KAAK,CAACC,IAAI,CAAC,+BAA+BK,WAAW,CAACC,OAAO,EAAE,CAAC;QAClE;;QAEA;QACA,IAAI;UACF,MAAM;YAAEC;UAAQ,CAAC,GAAG,MAAM,MAAM,CAAC,wBAAwB,CAAC;UAC1DR,KAAK,CAACC,IAAI,CAAC,mCAAmC,CAAC;QACjD,CAAC,CAAC,OAAOQ,SAAS,EAAE;UAClBT,KAAK,CAACC,IAAI,CAAC,4BAA4BQ,SAAS,CAACF,OAAO,EAAE,CAAC;QAC7D;;QAEA;QACA,IAAI;UACF,MAAM,MAAM,CAAC,iDAAiD,CAAC;UAC/DP,KAAK,CAACC,IAAI,CAAC,8CAA8C,CAAC;QAC5D,CAAC,CAAC,OAAOS,SAAS,EAAE;UAClBV,KAAK,CAACC,IAAI,CAAC,uCAAuCS,SAAS,CAACH,OAAO,EAAE,CAAC;QACxE;QAEA,IAAI;UACF,MAAM,MAAM,CAAC,iDAAiD,CAAC;UAC/DP,KAAK,CAACC,IAAI,CAAC,8CAA8C,CAAC;QAC5D,CAAC,CAAC,OAAOU,SAAS,EAAE;UAClBX,KAAK,CAACC,IAAI,CAAC,uCAAuCU,SAAS,CAACJ,OAAO,EAAE,CAAC;QACxE;QAEA,IAAI;UACF,MAAM,MAAM,CAAC,4CAA4C,CAAC;UAC1DP,KAAK,CAACC,IAAI,CAAC,yCAAyC,CAAC;QACvD,CAAC,CAAC,OAAOW,YAAY,EAAE;UACrBZ,KAAK,CAACC,IAAI,CAAC,kCAAkCW,YAAY,CAACL,OAAO,EAAE,CAAC;QACtE;QAEA,IAAI;UACF,MAAM,MAAM,CAAC,4CAA4C,CAAC;UAC1DP,KAAK,CAACC,IAAI,CAAC,yCAAyC,CAAC;QACvD,CAAC,CAAC,OAAOY,YAAY,EAAE;UACrBb,KAAK,CAACC,IAAI,CAAC,kCAAkCY,YAAY,CAACN,OAAO,EAAE,CAAC;QACtE;QAEAT,YAAY,CAACE,KAAK,CAAC;QACnBJ,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAOH,KAAK,EAAE;QACdO,KAAK,CAACC,IAAI,CAAC,sBAAsBR,KAAK,CAACc,OAAO,EAAE,CAAC;QACjDb,QAAQ,CAACD,KAAK,CAACc,OAAO,CAAC;QACvBT,YAAY,CAACE,KAAK,CAAC;QACnBJ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACP,UAAU,CAAC,CAAC;EAEhB,oBACEH,OAAA,CAACN,SAAS;IAAC+B,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAC5C7B,OAAA,CAACJ,GAAG;MAAC8B,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACjB7B,OAAA,CAACL,UAAU;QAACmC,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAAAH,QAAA,EAAC;MAErD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbpC,OAAA,CAACL,UAAU;QAACmC,OAAO,EAAC,IAAI;QAACO,KAAK,EAAC,gBAAgB;QAAAR,QAAA,GAAC,eACjC,EAAC1B,UAAU;MAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAELhC,KAAK,iBACJJ,OAAA,CAACF,KAAK;MAACwC,QAAQ,EAAC,OAAO;MAACZ,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,GAAC,UAC7B,EAACzB,KAAK;IAAA;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CACR,eAEDpC,OAAA,CAACH,KAAK;MAAC6B,EAAE,EAAE;QAAEa,CAAC,EAAE;MAAE,CAAE;MAAAV,QAAA,gBAClB7B,OAAA,CAACL,UAAU;QAACmC,OAAO,EAAC,IAAI;QAACE,YAAY;QAAAH,QAAA,EAAC;MAEtC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZ9B,OAAO,gBACNN,OAAA,CAACL,UAAU;QAAAkC,QAAA,EAAC;MAAkB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,gBAE3CpC,OAAA,CAACJ,GAAG;QAAAiC,QAAA,EACDrB,SAAS,CAACgC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzB1C,OAAA,CAACL,UAAU;UAAamC,OAAO,EAAC,OAAO;UAACJ,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAEe,UAAU,EAAE;UAAY,CAAE;UAAAd,QAAA,EAC5EY;QAAI,GADUC,KAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB;AAAClC,EAAA,CAjIQD,uBAAuB;EAAA,QACPR,SAAS;AAAA;AAAAmD,EAAA,GADzB3C,uBAAuB;AAmIhC,eAAeA,uBAAuB;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}