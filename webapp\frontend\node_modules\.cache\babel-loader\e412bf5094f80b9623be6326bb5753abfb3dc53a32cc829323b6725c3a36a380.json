{"ast": null, "code": "import superset from \"./superset.js\";\nexport default function subset(values, other) {\n  return superset(other, values);\n}", "map": {"version": 3, "names": ["superset", "subset", "values", "other"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/d3-array/src/subset.js"], "sourcesContent": ["import superset from \"./superset.js\";\n\nexport default function subset(values, other) {\n  return superset(other, values);\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,eAAe,SAASC,MAAMA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC5C,OAAOH,QAAQ,CAACG,KAAK,EAAED,MAAM,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}