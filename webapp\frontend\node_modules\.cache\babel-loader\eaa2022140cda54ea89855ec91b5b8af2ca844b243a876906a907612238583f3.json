{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { getQuarter } from \"./getQuarter.js\";\n\n/**\n * The {@link differenceInCalendarQuarters} function options.\n */\n\n/**\n * @name differenceInCalendarQuarters\n * @category Quarter Helpers\n * @summary Get the number of calendar quarters between the given dates.\n *\n * @description\n * Get the number of calendar quarters between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of calendar quarters\n *\n * @example\n * // How many calendar quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInCalendarQuarters(\n *   new Date(2014, 6, 2),\n *   new Date(2013, 11, 31)\n * )\n * //=> 3\n */\nexport function differenceInCalendarQuarters(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  const yearsDiff = laterDate_.getFullYear() - earlierDate_.getFullYear();\n  const quartersDiff = getQuarter(laterDate_) - getQuarter(earlierDate_);\n  return yearsDiff * 4 + quartersDiff;\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarQuarters;", "map": {"version": 3, "names": ["normalizeDates", "getQuarter", "differenceInCalendarQuarters", "laterDate", "earlierDate", "options", "laterDate_", "earlierDate_", "in", "yearsDiff", "getFullYear", "quartersDiff"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/differenceInCalendarQuarters.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { getQuarter } from \"./getQuarter.js\";\n\n/**\n * The {@link differenceInCalendarQuarters} function options.\n */\n\n/**\n * @name differenceInCalendarQuarters\n * @category Quarter Helpers\n * @summary Get the number of calendar quarters between the given dates.\n *\n * @description\n * Get the number of calendar quarters between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of calendar quarters\n *\n * @example\n * // How many calendar quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInCalendarQuarters(\n *   new Date(2014, 6, 2),\n *   new Date(2013, 11, 31)\n * )\n * //=> 3\n */\nexport function differenceInCalendarQuarters(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const yearsDiff = laterDate_.getFullYear() - earlierDate_.getFullYear();\n  const quartersDiff = getQuarter(laterDate_) - getQuarter(earlierDate_);\n\n  return yearsDiff * 4 + quartersDiff;\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarQuarters;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SAASC,UAAU,QAAQ,iBAAiB;;AAE5C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,4BAA4BA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAC5E,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGP,cAAc,CAC/CK,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EAED,MAAMK,SAAS,GAAGH,UAAU,CAACI,WAAW,CAAC,CAAC,GAAGH,YAAY,CAACG,WAAW,CAAC,CAAC;EACvE,MAAMC,YAAY,GAAGV,UAAU,CAACK,UAAU,CAAC,GAAGL,UAAU,CAACM,YAAY,CAAC;EAEtE,OAAOE,SAAS,GAAG,CAAC,GAAGE,YAAY;AACrC;;AAEA;AACA,eAAeT,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}