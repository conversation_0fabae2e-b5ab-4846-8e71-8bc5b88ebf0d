{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { quartersInYear } from \"../constants/index.js\";\n/**\n * @name quartersToYears\n * @category Conversion Helpers\n * @summary Convert number of quarters to years.\n *\n * @description\n * Convert a number of quarters to a full number of years.\n *\n * @param {number} quarters - number of quarters to be converted\n *\n * @returns {number} the number of quarters converted in years\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 8 quarters to years\n * const result = quartersToYears(8)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = quartersToYears(11)\n * //=> 2\n */\nexport default function quartersToYears(quarters) {\n  requiredArgs(1, arguments);\n  var years = quarters / quartersInYear;\n  return Math.floor(years);\n}", "map": {"version": 3, "names": ["requiredArgs", "quartersInYear", "quartersToYears", "quarters", "arguments", "years", "Math", "floor"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/quartersToYears/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { quartersInYear } from \"../constants/index.js\";\n/**\n * @name quartersToYears\n * @category Conversion Helpers\n * @summary Convert number of quarters to years.\n *\n * @description\n * Convert a number of quarters to a full number of years.\n *\n * @param {number} quarters - number of quarters to be converted\n *\n * @returns {number} the number of quarters converted in years\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 8 quarters to years\n * const result = quartersToYears(8)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = quartersToYears(11)\n * //=> 2\n */\nexport default function quartersToYears(quarters) {\n  requiredArgs(1, arguments);\n  var years = quarters / quartersInYear;\n  return Math.floor(years);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,cAAc,QAAQ,uBAAuB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,QAAQ,EAAE;EAChDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,KAAK,GAAGF,QAAQ,GAAGF,cAAc;EACrC,OAAOK,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}