{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\n// Ref: https://www.unicode.org/cldr/charts/32/summary/ta.html\n\nconst eraValues = {\n  narrow: [\"கி.மு.\", \"கி.பி.\"],\n  abbreviated: [\"கி.மு.\", \"கி.பி.\"],\n  // CLDR #1624, #1626\n  wide: [\"கிறிஸ்துவுக்கு முன்\", \"அன்னோ டோமினி\"] // CLDR #1620, #1622\n};\nconst quarterValues = {\n  // CLDR #1644 - #1647\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  // CLDR #1636 - #1639\n  abbreviated: [\"காலா.1\", \"காலா.2\", \"காலா.3\", \"காலா.4\"],\n  // CLDR #1628 - #1631\n  wide: [\"ஒன்றாம் காலாண்டு\", \"இரண்டாம் காலாண்டு\", \"மூன்றாம் காலாண்டு\", \"நான்காம் காலாண்டு\"]\n};\nconst monthValues = {\n  // CLDR #700 - #711\n  narrow: [\"ஜ\", \"பி\", \"மா\", \"ஏ\", \"மே\", \"ஜூ\", \"ஜூ\", \"ஆ\", \"செ\", \"அ\", \"ந\", \"டி\"],\n  // CLDR #1676 - #1687\n  abbreviated: [\"ஜன.\", \"பிப்.\", \"மார்.\", \"ஏப்.\", \"மே\", \"ஜூன்\", \"ஜூலை\", \"ஆக.\", \"செப்.\", \"அக்.\", \"நவ.\", \"டிச.\"],\n  // CLDR #1652 - #1663\n  wide: [\"ஜனவரி\",\n  // January\n  \"பிப்ரவரி\",\n  // February\n  \"மார்ச்\",\n  // March\n  \"ஏப்ரல்\",\n  // April\n  \"மே\",\n  // May\n  \"ஜூன்\",\n  // June\n  \"ஜூலை\",\n  // July\n  \"ஆகஸ்ட்\",\n  // August\n  \"செப்டம்பர்\",\n  // September\n  \"அக்டோபர்\",\n  // October\n  \"நவம்பர்\",\n  // November\n  \"டிசம்பர்\" // December\n  ]\n};\nconst dayValues = {\n  // CLDR #1766 - #1772\n  narrow: [\"ஞா\", \"தி\", \"செ\", \"பு\", \"வி\", \"வெ\", \"ச\"],\n  // CLDR #1752 - #1758\n  short: [\"ஞா\", \"தி\", \"செ\", \"பு\", \"வி\", \"வெ\", \"ச\"],\n  // CLDR #1738 - #1744\n  abbreviated: [\"ஞாயி.\", \"திங்.\", \"செவ்.\", \"புத.\", \"வியா.\", \"வெள்.\", \"சனி\"],\n  // CLDR #1724 - #1730\n  wide: [\"ஞாயிறு\",\n  // Sunday\n  \"திங்கள்\",\n  // Monday\n  \"செவ்வாய்\",\n  // Tuesday\n  \"புதன்\",\n  // Wednesday\n  \"வியாழன்\",\n  // Thursday\n  \"வெள்ளி\",\n  // Friday\n  \"சனி\" // Saturday\n  ]\n};\n\n// CLDR #1780 - #1845\nconst dayPeriodValues = {\n  narrow: {\n    am: \"மு.ப\",\n    pm: \"பி.ப\",\n    midnight: \"நள்.\",\n    noon: \"நண்.\",\n    morning: \"கா.\",\n    afternoon: \"மதி.\",\n    evening: \"மா.\",\n    night: \"இர.\"\n  },\n  abbreviated: {\n    am: \"முற்பகல்\",\n    pm: \"பிற்பகல்\",\n    midnight: \"நள்ளிரவு\",\n    noon: \"நண்பகல்\",\n    morning: \"காலை\",\n    afternoon: \"மதியம்\",\n    evening: \"மாலை\",\n    night: \"இரவு\"\n  },\n  wide: {\n    am: \"முற்பகல்\",\n    pm: \"பிற்பகல்\",\n    midnight: \"நள்ளிரவு\",\n    noon: \"நண்பகல்\",\n    morning: \"காலை\",\n    afternoon: \"மதியம்\",\n    evening: \"மாலை\",\n    night: \"இரவு\"\n  }\n};\n\n// CLDR #1780 - #1845\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"மு.ப\",\n    pm: \"பி.ப\",\n    midnight: \"நள்.\",\n    noon: \"நண்.\",\n    morning: \"கா.\",\n    afternoon: \"மதி.\",\n    evening: \"மா.\",\n    night: \"இர.\"\n  },\n  abbreviated: {\n    am: \"முற்பகல்\",\n    pm: \"பிற்பகல்\",\n    midnight: \"நள்ளிரவு\",\n    noon: \"நண்பகல்\",\n    morning: \"காலை\",\n    afternoon: \"மதியம்\",\n    evening: \"மாலை\",\n    night: \"இரவு\"\n  },\n  wide: {\n    am: \"முற்பகல்\",\n    pm: \"பிற்பகல்\",\n    midnight: \"நள்ளிரவு\",\n    noon: \"நண்பகல்\",\n    morning: \"காலை\",\n    afternoon: \"மதியம்\",\n    evening: \"மாலை\",\n    night: \"இரவு\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ta/_lib/localize.mjs"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\n// Ref: https://www.unicode.org/cldr/charts/32/summary/ta.html\n\nconst eraValues = {\n  narrow: [\"கி.மு.\", \"கி.பி.\"],\n  abbreviated: [\"கி.மு.\", \"கி.பி.\"], // CLDR #1624, #1626\n  wide: [\"கிறிஸ்துவுக்கு முன்\", \"அன்னோ டோமினி\"], // CLDR #1620, #1622\n};\n\nconst quarterValues = {\n  // CLDR #1644 - #1647\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  // CLDR #1636 - #1639\n  abbreviated: [\"காலா.1\", \"காலா.2\", \"காலா.3\", \"காலா.4\"],\n  // CLDR #1628 - #1631\n  wide: [\n    \"ஒன்றாம் காலாண்டு\",\n    \"இரண்டாம் காலாண்டு\",\n    \"மூன்றாம் காலாண்டு\",\n    \"நான்காம் காலாண்டு\",\n  ],\n};\n\nconst monthValues = {\n  // CLDR #700 - #711\n  narrow: [\"ஜ\", \"பி\", \"மா\", \"ஏ\", \"மே\", \"ஜூ\", \"ஜூ\", \"ஆ\", \"செ\", \"அ\", \"ந\", \"டி\"],\n\n  // CLDR #1676 - #1687\n  abbreviated: [\n    \"ஜன.\",\n    \"பிப்.\",\n    \"மார்.\",\n    \"ஏப்.\",\n    \"மே\",\n    \"ஜூன்\",\n    \"ஜூலை\",\n    \"ஆக.\",\n    \"செப்.\",\n    \"அக்.\",\n    \"நவ.\",\n    \"டிச.\",\n  ],\n\n  // CLDR #1652 - #1663\n  wide: [\n    \"ஜனவரி\", // January\n    \"பிப்ரவரி\", // February\n    \"மார்ச்\", // March\n    \"ஏப்ரல்\", // April\n    \"மே\", // May\n    \"ஜூன்\", // June\n    \"ஜூலை\", // July\n    \"ஆகஸ்ட்\", // August\n    \"செப்டம்பர்\", // September\n    \"அக்டோபர்\", // October\n    \"நவம்பர்\", // November\n    \"டிசம்பர்\", // December\n  ],\n};\n\nconst dayValues = {\n  // CLDR #1766 - #1772\n  narrow: [\"ஞா\", \"தி\", \"செ\", \"பு\", \"வி\", \"வெ\", \"ச\"],\n  // CLDR #1752 - #1758\n  short: [\"ஞா\", \"தி\", \"செ\", \"பு\", \"வி\", \"வெ\", \"ச\"],\n  // CLDR #1738 - #1744\n  abbreviated: [\"ஞாயி.\", \"திங்.\", \"செவ்.\", \"புத.\", \"வியா.\", \"வெள்.\", \"சனி\"],\n\n  // CLDR #1724 - #1730\n  wide: [\n    \"ஞாயிறு\", // Sunday\n    \"திங்கள்\", // Monday\n    \"செவ்வாய்\", // Tuesday\n    \"புதன்\", // Wednesday\n    \"வியாழன்\", // Thursday\n    \"வெள்ளி\", // Friday\n    \"சனி\", // Saturday\n  ],\n};\n\n// CLDR #1780 - #1845\nconst dayPeriodValues = {\n  narrow: {\n    am: \"மு.ப\",\n    pm: \"பி.ப\",\n    midnight: \"நள்.\",\n    noon: \"நண்.\",\n    morning: \"கா.\",\n    afternoon: \"மதி.\",\n    evening: \"மா.\",\n    night: \"இர.\",\n  },\n  abbreviated: {\n    am: \"முற்பகல்\",\n    pm: \"பிற்பகல்\",\n    midnight: \"நள்ளிரவு\",\n    noon: \"நண்பகல்\",\n    morning: \"காலை\",\n    afternoon: \"மதியம்\",\n    evening: \"மாலை\",\n    night: \"இரவு\",\n  },\n  wide: {\n    am: \"முற்பகல்\",\n    pm: \"பிற்பகல்\",\n    midnight: \"நள்ளிரவு\",\n    noon: \"நண்பகல்\",\n    morning: \"காலை\",\n    afternoon: \"மதியம்\",\n    evening: \"மாலை\",\n    night: \"இரவு\",\n  },\n};\n\n// CLDR #1780 - #1845\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"மு.ப\",\n    pm: \"பி.ப\",\n    midnight: \"நள்.\",\n    noon: \"நண்.\",\n    morning: \"கா.\",\n    afternoon: \"மதி.\",\n    evening: \"மா.\",\n    night: \"இர.\",\n  },\n  abbreviated: {\n    am: \"முற்பகல்\",\n    pm: \"பிற்பகல்\",\n    midnight: \"நள்ளிரவு\",\n    noon: \"நண்பகல்\",\n    morning: \"காலை\",\n    afternoon: \"மதியம்\",\n    evening: \"மாலை\",\n    night: \"இரவு\",\n  },\n  wide: {\n    am: \"முற்பகல்\",\n    pm: \"பிற்பகல்\",\n    midnight: \"நள்ளிரவு\",\n    noon: \"நண்பகல்\",\n    morning: \"காலை\",\n    afternoon: \"மதியம்\",\n    evening: \"மாலை\",\n    night: \"இரவு\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gCAAgC;;AAEhE;;AAEA,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC5BC,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAAE;EACnCC,IAAI,EAAE,CAAC,qBAAqB,EAAE,cAAc,CAAC,CAAE;AACjD,CAAC;AAED,MAAMC,aAAa,GAAG;EACpB;EACAH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5B;EACAC,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACrD;EACAC,IAAI,EAAE,CACJ,kBAAkB,EAClB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB;AAEvB,CAAC;AAED,MAAME,WAAW,GAAG;EAClB;EACAJ,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EAE3E;EACAC,WAAW,EAAE,CACX,KAAK,EACL,OAAO,EACP,OAAO,EACP,MAAM,EACN,IAAI,EACJ,MAAM,EACN,MAAM,EACN,KAAK,EACL,OAAO,EACP,MAAM,EACN,KAAK,EACL,MAAM,CACP;EAED;EACAC,IAAI,EAAE,CACJ,OAAO;EAAE;EACT,UAAU;EAAE;EACZ,QAAQ;EAAE;EACV,QAAQ;EAAE;EACV,IAAI;EAAE;EACN,MAAM;EAAE;EACR,MAAM;EAAE;EACR,QAAQ;EAAE;EACV,YAAY;EAAE;EACd,UAAU;EAAE;EACZ,SAAS;EAAE;EACX,UAAU,CAAE;EAAA;AAEhB,CAAC;AAED,MAAMG,SAAS,GAAG;EAChB;EACAL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EACjD;EACAM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EAChD;EACAL,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC;EAEzE;EACAC,IAAI,EAAE,CACJ,QAAQ;EAAE;EACV,SAAS;EAAE;EACX,UAAU;EAAE;EACZ,OAAO;EAAE;EACT,SAAS;EAAE;EACX,QAAQ;EAAE;EACV,KAAK,CAAE;EAAA;AAEX,CAAC;;AAED;AACA,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;;AAED;AACA,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMG,QAAQ,GAAG;EACtBJ,aAAa;EAEbK,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}