{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name closestIndexTo\n * @category Common Helpers\n * @summary Return an index of the closest date from the array comparing to the given date.\n *\n * @description\n * Return an index of the closest date from the array comparing to the given date.\n *\n * @param {Date | Number} dateToCompare - the date to compare with\n * @param {Array<Date> | Array<number>} datesArray - the array to search\n * @returns {Number | undefined} an index of the date closest to the given date or undefined if no valid value is given\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Which date is closer to 6 September 2015?\n * const dateToCompare = new Date(2015, 8, 6)\n * const datesArray = [\n *   new Date(2015, 0, 1),\n *   new Date(2016, 0, 1),\n *   new Date(2017, 0, 1)\n * ]\n * const result = closestIndexTo(dateToCompare, datesArray)\n * //=> 1\n */\nexport default function closestIndexTo(dirtyDateToCompare, dirtyDatesArray) {\n  requiredArgs(2, arguments);\n  var dateToCompare = toDate(dirtyDateToCompare);\n  if (isNaN(Number(dateToCompare))) return NaN;\n  var timeToCompare = dateToCompare.getTime();\n  var datesArray;\n  // `dirtyDatesArray` is undefined or null\n  if (dirtyDatesArray == null) {\n    datesArray = [];\n\n    // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n  } else if (typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray;\n\n    // If `dirtyDatesArray` is Array-like Object, convert to Array. Otherwise, make it empty Array\n  } else {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  }\n  var result;\n  var minDistance;\n  datesArray.forEach(function (dirtyDate, index) {\n    var currentDate = toDate(dirtyDate);\n    if (isNaN(Number(currentDate))) {\n      result = NaN;\n      minDistance = NaN;\n      return;\n    }\n    var distance = Math.abs(timeToCompare - currentDate.getTime());\n    if (result == null || distance < Number(minDistance)) {\n      result = index;\n      minDistance = distance;\n    }\n  });\n  return result;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "closestIndexTo", "dirtyDateToCompare", "dirtyDatesArray", "arguments", "dateToCompare", "isNaN", "Number", "NaN", "timeToCompare", "getTime", "datesArray", "for<PERSON>ach", "Array", "prototype", "slice", "call", "result", "minDistance", "dirtyDate", "index", "currentDate", "distance", "Math", "abs"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/closestIndexTo/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name closestIndexTo\n * @category Common Helpers\n * @summary Return an index of the closest date from the array comparing to the given date.\n *\n * @description\n * Return an index of the closest date from the array comparing to the given date.\n *\n * @param {Date | Number} dateToCompare - the date to compare with\n * @param {Array<Date> | Array<number>} datesArray - the array to search\n * @returns {Number | undefined} an index of the date closest to the given date or undefined if no valid value is given\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Which date is closer to 6 September 2015?\n * const dateToCompare = new Date(2015, 8, 6)\n * const datesArray = [\n *   new Date(2015, 0, 1),\n *   new Date(2016, 0, 1),\n *   new Date(2017, 0, 1)\n * ]\n * const result = closestIndexTo(dateToCompare, datesArray)\n * //=> 1\n */\nexport default function closestIndexTo(dirtyDateToCompare, dirtyDatesArray) {\n  requiredArgs(2, arguments);\n  var dateToCompare = toDate(dirtyDateToCompare);\n  if (isNaN(Number(dateToCompare))) return NaN;\n  var timeToCompare = dateToCompare.getTime();\n  var datesArray;\n  // `dirtyDatesArray` is undefined or null\n  if (dirtyDatesArray == null) {\n    datesArray = [];\n\n    // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n  } else if (typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray;\n\n    // If `dirtyDatesArray` is Array-like Object, convert to Array. Otherwise, make it empty Array\n  } else {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  }\n  var result;\n  var minDistance;\n  datesArray.forEach(function (dirtyDate, index) {\n    var currentDate = toDate(dirtyDate);\n    if (isNaN(Number(currentDate))) {\n      result = NaN;\n      minDistance = NaN;\n      return;\n    }\n    var distance = Math.abs(timeToCompare - currentDate.getTime());\n    if (result == null || distance < Number(minDistance)) {\n      result = index;\n      minDistance = distance;\n    }\n  });\n  return result;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,kBAAkB,EAAEC,eAAe,EAAE;EAC1EH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,aAAa,GAAGN,MAAM,CAACG,kBAAkB,CAAC;EAC9C,IAAII,KAAK,CAACC,MAAM,CAACF,aAAa,CAAC,CAAC,EAAE,OAAOG,GAAG;EAC5C,IAAIC,aAAa,GAAGJ,aAAa,CAACK,OAAO,CAAC,CAAC;EAC3C,IAAIC,UAAU;EACd;EACA,IAAIR,eAAe,IAAI,IAAI,EAAE;IAC3BQ,UAAU,GAAG,EAAE;;IAEf;EACF,CAAC,MAAM,IAAI,OAAOR,eAAe,CAACS,OAAO,KAAK,UAAU,EAAE;IACxDD,UAAU,GAAGR,eAAe;;IAE5B;EACF,CAAC,MAAM;IACLQ,UAAU,GAAGE,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACb,eAAe,CAAC;EAC1D;EACA,IAAIc,MAAM;EACV,IAAIC,WAAW;EACfP,UAAU,CAACC,OAAO,CAAC,UAAUO,SAAS,EAAEC,KAAK,EAAE;IAC7C,IAAIC,WAAW,GAAGtB,MAAM,CAACoB,SAAS,CAAC;IACnC,IAAIb,KAAK,CAACC,MAAM,CAACc,WAAW,CAAC,CAAC,EAAE;MAC9BJ,MAAM,GAAGT,GAAG;MACZU,WAAW,GAAGV,GAAG;MACjB;IACF;IACA,IAAIc,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACf,aAAa,GAAGY,WAAW,CAACX,OAAO,CAAC,CAAC,CAAC;IAC9D,IAAIO,MAAM,IAAI,IAAI,IAAIK,QAAQ,GAAGf,MAAM,CAACW,WAAW,CAAC,EAAE;MACpDD,MAAM,GAAGG,KAAK;MACdF,WAAW,GAAGI,QAAQ;IACxB;EACF,CAAC,CAAC;EACF,OAAOL,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}