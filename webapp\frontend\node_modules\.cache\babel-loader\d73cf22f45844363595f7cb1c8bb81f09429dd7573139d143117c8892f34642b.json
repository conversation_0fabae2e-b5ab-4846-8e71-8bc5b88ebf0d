{"ast": null, "code": "'use client';\n\nexport { default, createFilterOptions } from './useAutocomplete';", "map": {"version": 3, "names": ["default", "createFilterOptions"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/useAutocomplete/index.js"], "sourcesContent": ["'use client';\n\nexport { default, createFilterOptions } from './useAutocomplete';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,EAAEC,mBAAmB,QAAQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}