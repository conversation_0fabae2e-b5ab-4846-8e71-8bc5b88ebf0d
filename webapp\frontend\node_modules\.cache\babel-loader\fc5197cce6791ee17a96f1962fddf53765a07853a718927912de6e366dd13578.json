{"ast": null, "code": "import { differenceInCalendarWeeks } from \"./differenceInCalendarWeeks.js\";\nimport { lastDayOfMonth } from \"./lastDayOfMonth.js\";\nimport { startOfMonth } from \"./startOfMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getWeeksInMonth} function options.\n */\n\n/**\n * @name getWeeksInMonth\n * @category Week Helpers\n * @summary Get the number of calendar weeks a month spans.\n *\n * @description\n * Get the number of calendar weeks the month in the given date spans.\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The number of calendar weeks\n *\n * @example\n * // How many calendar weeks does February 2015 span?\n * const result = getWeeksInMonth(new Date(2015, 1, 8))\n * //=> 4\n *\n * @example\n * // If the week starts on Monday,\n * // how many calendar weeks does July 2017 span?\n * const result = getWeeksInMonth(new Date(2017, 6, 5), { weekStartsOn: 1 })\n * //=> 6\n */\nexport function getWeeksInMonth(date, options) {\n  const contextDate = toDate(date, options?.in);\n  return differenceInCalendarWeeks(lastDayOfMonth(contextDate, options), startOfMonth(contextDate, options), options) + 1;\n}\n\n// Fallback for modularized imports:\nexport default getWeeksInMonth;", "map": {"version": 3, "names": ["differenceInCalendarWeeks", "lastDayOfMonth", "startOfMonth", "toDate", "getWeeksInMonth", "date", "options", "contextDate", "in"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/getWeeksInMonth.js"], "sourcesContent": ["import { differenceInCalendarWeeks } from \"./differenceInCalendarWeeks.js\";\nimport { lastDayOfMonth } from \"./lastDayOfMonth.js\";\nimport { startOfMonth } from \"./startOfMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getWeeksInMonth} function options.\n */\n\n/**\n * @name getWeeksInMonth\n * @category Week Helpers\n * @summary Get the number of calendar weeks a month spans.\n *\n * @description\n * Get the number of calendar weeks the month in the given date spans.\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The number of calendar weeks\n *\n * @example\n * // How many calendar weeks does February 2015 span?\n * const result = getWeeksInMonth(new Date(2015, 1, 8))\n * //=> 4\n *\n * @example\n * // If the week starts on Monday,\n * // how many calendar weeks does July 2017 span?\n * const result = getWeeksInMonth(new Date(2017, 6, 5), { weekStartsOn: 1 })\n * //=> 6\n */\nexport function getWeeksInMonth(date, options) {\n  const contextDate = toDate(date, options?.in);\n  return (\n    differenceInCalendarWeeks(\n      lastDayOfMonth(contextDate, options),\n      startOfMonth(contextDate, options),\n      options,\n    ) + 1\n  );\n}\n\n// Fallback for modularized imports:\nexport default getWeeksInMonth;\n"], "mappings": "AAAA,SAASA,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC7C,MAAMC,WAAW,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EAC7C,OACER,yBAAyB,CACvBC,cAAc,CAACM,WAAW,EAAED,OAAO,CAAC,EACpCJ,YAAY,CAACK,WAAW,EAAED,OAAO,CAAC,EAClCA,OACF,CAAC,GAAG,CAAC;AAET;;AAEA;AACA,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}