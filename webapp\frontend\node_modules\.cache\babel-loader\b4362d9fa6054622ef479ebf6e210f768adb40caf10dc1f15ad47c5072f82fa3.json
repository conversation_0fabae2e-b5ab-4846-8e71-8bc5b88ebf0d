{"ast": null, "code": "import React,{useState}from'react';import{Box,Typography,Button,Paper,Divider,List,ListItem,ListItemText,ListItemIcon,ListItemButton,Dialog,DialogTitle,DialogContent,DialogContentText,DialogActions,TextField,FormControl,InputLabel,Select,MenuItem,Grid,Alert,CircularProgress,FormHelperText,Radio,RadioGroup,FormControlLabel,FormLabel}from'@mui/material';import{Add as AddIcon,Edit as EditIcon,Delete as DeleteIcon,Cable as CableIcon,Save as SaveIcon,Warning as WarningIcon}from'@mui/icons-material';import{useNavigate}from'react-router-dom';import caviService from'../../services/caviService';import{validateCavoData,validateField,isEmpty}from'../../utils/validationUtils';import{redirectToVisualizzaCavi}from'../../utils/navigationUtils';import CavoForm from'./CavoForm';import SelezionaCavoForm from'./SelezionaCavoForm';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const PosaCaviCollegamenti=_ref=>{let{cantiereId:propCantiereId,onSuccess,onError,initialOption=null}=_ref;// Log del cantiereId all'avvio\nconsole.log('PosaCaviCollegamenti - cantiereId da props:',propCantiereId);// Aggiungi navigate per la navigazione programmatica\nconst navigate=useNavigate();// Se cantiereId non è definito nelle props, prova a recuperarlo dal localStorage\nconst cantiereId=propCantiereId||parseInt(localStorage.getItem('selectedCantiereId'),10);console.log('PosaCaviCollegamenti - cantiereId effettivo:',cantiereId);const[loading,setLoading]=useState(false);const[selectedOption,setSelectedOption]=useState(null);const[openDialog,setOpenDialog]=useState(false);const[dialogType,setDialogType]=useState('');const[selectedCavo,setSelectedCavo]=useState(null);const[formData,setFormData]=useState({id_cavo:'',metri_posati:'',id_bobina:''});const[cavoIdInput,setCavoIdInput]=useState('');const[formErrors,setFormErrors]=useState({});const[formWarnings,setFormWarnings]=useState({});const[cavi,setCavi]=useState([]);const[caviLoading,setCaviLoading]=useState(false);const[deleteMode,setDeleteMode]=useState('spare');// 'spare' o 'delete'\n// Inizializza il componente con l'opzione iniziale se specificata\nReact.useEffect(()=>{if(initialOption){// Imposta direttamente le opzioni invece di chiamare handleOptionSelect\n// per evitare dipendenze circolari\nsetSelectedOption(initialOption);if(initialOption==='eliminaCavo'){loadCavi('eliminaCavo');setDialogType('eliminaCavo');setOpenDialog(true);}else if(initialOption==='modificaCavo'){loadCavi('modificaCavo');setDialogType('selezionaCavo');setOpenDialog(true);}else if(initialOption==='aggiungiCavo'){setDialogType('aggiungiCavo');setOpenDialog(true);}}// eslint-disable-next-line react-hooks/exhaustive-deps\n},[initialOption]);// Carica i cavi attivi per la selezione\nconst loadCavi=async operationType=>{try{setCaviLoading(true);// Verifica che cantiereId sia valido\nif(!cantiereId){throw new Error('ID cantiere non valido o mancante');}console.log('Caricamento cavi per cantiere:',cantiereId);const caviData=await caviService.getCavi(cantiereId,0);// Filtra i cavi in base al tipo di operazione\nif(operationType==='modificaCavo'){// Per modifica cavo, mostra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\nconst caviNonPosati=caviData.filter(cavo=>parseFloat(cavo.metratura_reale)===0&&cavo.stato_installazione!=='Installato');setCavi(caviNonPosati);}else{// Per altre operazioni, mostra tutti i cavi\nsetCavi(caviData);}}catch(error){console.error('Errore nel caricamento dei cavi:',error);// Gestione più dettagliata dell'errore\nlet errorMessage='Errore nel caricamento dei cavi';if(error.response){// Errore dal server con risposta\nerrorMessage+=`: ${error.response.status} ${error.response.statusText}`;if(error.response.data&&error.response.data.detail){errorMessage+=` - ${error.response.data.detail}`;}}else if(error.request){// Errore di rete senza risposta dal server\nerrorMessage+=': Nessuna risposta dal server. Verifica la connessione di rete.';}else if(error.message){// Errore con messaggio\nerrorMessage+=`: ${error.message}`;}onError(errorMessage);}finally{setCaviLoading(false);}};// Gestisce la selezione di un'opzione dal menu\nconst handleOptionSelect=option=>{setSelectedOption(option);if(option==='inserisciMetri'||option==='modificaBobina'){loadCavi(option);setDialogType(option);setOpenDialog(true);}else if(option==='aggiungiCavo'){// Apri il dialog per aggiungere un nuovo cavo\nsetDialogType('aggiungiCavo');setOpenDialog(true);}else if(option==='modificaCavo'){loadCavi('modificaCavo');setDialogType('selezionaCavo');setOpenDialog(true);}else if(option==='eliminaCavo'){loadCavi('eliminaCavo');setDialogType('eliminaCavo');setOpenDialog(true);}else if(option==='collegamentoCavo'){// Reindirizza alla pagina di gestione collegamenti usando navigate\nnavigate(`/dashboard/cantieri/${cantiereId}/cavi/posa/collegamenti`);}};// Gestisce la chiusura del dialog\nconst handleCloseDialog=()=>{console.log('Chiusura dialog...');// Reset dello stato del componente\nsetOpenDialog(false);setSelectedCavo(null);setFormData({id_cavo:'',metri_posati:'',id_bobina:''});setCavoIdInput('');setFormErrors({});setFormWarnings({});setDeleteMode('spare');// Reset alla modalità predefinita\nsetLoading(false);// Assicurati che loading sia false quando chiudi il dialog\n// Se è stato specificato un initialOption, notifica il genitore che il dialogo è stato chiuso\n// ma senza messaggio di errore\nif(initialOption&&onSuccess){// Chiama onSuccess ma senza messaggio per evitare l'alert\nonSuccess(null);}};// Gestisce la selezione di un cavo\nconst handleCavoSelect=cavo=>{setSelectedCavo(cavo);if(dialogType==='inserisciMetri'){setFormData({...formData,id_cavo:cavo.id_cavo,metri_posati:''});}else if(dialogType==='modificaBobina'){setFormData({...formData,id_cavo:cavo.id_cavo,id_bobina:cavo.id_bobina||''});}else if(dialogType==='selezionaCavo'){setDialogType('modificaCavo');setSelectedCavo(cavo);setFormData({...cavo,metri_teorici:cavo.metri_teorici||'',metratura_reale:cavo.metratura_reale||'0'});}};// Gestisce la ricerca di un cavo per ID\nconst handleSearchCavoById=async()=>{if(!cavoIdInput.trim()){onError('Inserisci un ID cavo valido');return;}try{setCaviLoading(true);// Verifica che cantiereId sia valido\nif(!cantiereId){throw new Error('ID cantiere non valido o mancante');}console.log('Ricerca cavo con ID:',cavoIdInput,'per cantiere:',cantiereId);const caviData=await caviService.getCavi(cantiereId,0);const cavo=caviData.find(c=>c.id_cavo===cavoIdInput.trim());if(!cavo){onError(`Cavo con ID ${cavoIdInput} non trovato`);return;}// Verifica se stiamo cercando un cavo per modificarlo\nif(dialogType==='selezionaCavo'){// Verifica che il cavo non sia già posato\nif(parseFloat(cavo.metratura_reale)>0||cavo.stato_installazione==='Installato'){onError(`Il cavo ${cavo.id_cavo} risulta già posato. Utilizza l'opzione \"Modifica bobina cavo posato\" per modificarlo.`);return;}}// Seleziona il cavo trovato\nhandleCavoSelect(cavo);}catch(error){console.error('Errore nel caricamento dei cavi:',error);// Gestione più dettagliata dell'errore\nlet errorMessage='Errore nel caricamento dei cavi';if(error.response){// Errore dal server con risposta\nerrorMessage+=`: ${error.response.status} ${error.response.statusText}`;if(error.response.data&&error.response.data.detail){errorMessage+=` - ${error.response.data.detail}`;}}else if(error.request){// Errore di rete senza risposta dal server\nerrorMessage+=': Nessuna risposta dal server. Verifica la connessione di rete.';}else if(error.message){// Errore con messaggio\nerrorMessage+=`: ${error.message}`;}onError(errorMessage);}finally{setCaviLoading(false);}};// Gestisce il cambio dell'input dell'ID cavo\nconst handleCavoIdInputChange=e=>{setCavoIdInput(e.target.value);};// Gestisce il cambio dei valori nel form con validazione\nconst handleFormChange=e=>{const{name,value}=e.target;// Aggiorna il valore nel form\nsetFormData({...formData,[name]:value});// Valida il campo\nif(dialogType==='modificaCavo'){const additionalParams={};if(name==='metratura_reale'){additionalParams.metriTeorici=parseFloat(formData.metri_teorici||0);}const result=validateField(name,value,additionalParams);// Aggiorna gli errori\nsetFormErrors(prev=>({...prev,[name]:!result.valid?result.message:null}));// Aggiorna gli avvisi\nsetFormWarnings(prev=>({...prev,[name]:result.warning?result.message:null}));}};// Gestisce il salvataggio del form con validazione\nconst handleSave=async()=>{try{setLoading(true);if(dialogType==='inserisciMetri'){// Valida i metri posati\nif(isEmpty(formData.metri_posati)||isNaN(parseFloat(formData.metri_posati))){setFormErrors({metri_posati:'Inserire un valore numerico valido'});setLoading(false);return;}try{await caviService.updateMetriPosati(cantiereId,formData.id_cavo,parseFloat(formData.metri_posati));// Prima chiama onSuccess, poi chiudi il dialog\nonSuccess('Metri posati aggiornati con successo');// Chiudi il dialog\nhandleCloseDialog();// Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\nsetTimeout(()=>{redirectToVisualizzaCavi(navigate,1000);},500);}catch(error){console.error('Errore durante l\\'aggiornamento dei metri posati:',error);onError('Errore durante l\\'aggiornamento dei metri posati: '+(error.message||'Errore sconosciuto'));setLoading(false);}}else if(dialogType==='modificaBobina'){try{await caviService.updateBobina(cantiereId,formData.id_cavo,formData.id_bobina);// Prima chiama onSuccess, poi chiudi il dialog\nonSuccess('Bobina aggiornata con successo');// Chiudi il dialog\nhandleCloseDialog();// Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\nsetTimeout(()=>{redirectToVisualizzaCavi(navigate,1000);},500);}catch(error){console.error('Errore durante l\\'aggiornamento della bobina:',error);onError('Errore durante l\\'aggiornamento della bobina: '+(error.message||'Errore sconosciuto'));setLoading(false);}}else if(dialogType==='modificaCavo'){// Validazione completa dei dati del cavo\nconst validation=validateCavoData(formData);if(!validation.isValid){setFormErrors(validation.errors);setFormWarnings(validation.warnings);setLoading(false);return;}// Usa i dati validati\nconst validatedData=validation.validatedData;// Rimuovi i campi di sistema che non devono essere modificati\nconst dataToSend={...validatedData};delete dataToSend.id_bobina;// Rimuovi id_bobina perché è un campo di sistema\ndelete dataToSend.metratura_reale;// Rimuovi metratura_reale perché è un campo di sistema\ndelete dataToSend.modificato_manualmente;// Rimuovi modificato_manualmente perché è un campo di sistema\ndelete dataToSend.timestamp;// Rimuovi timestamp perché è un campo di sistema\ndelete dataToSend.stato_installazione;// Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n// Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\ndataToSend.modificato_manualmente=1;console.log('Dati inviati al server:',dataToSend);try{console.log('Invio dati al server per aggiornamento cavo:',dataToSend);const result=await caviService.updateCavo(cantiereId,dataToSend.id_cavo,dataToSend);console.log('Risposta dal server dopo aggiornamento cavo:',result);// Prima chiama onSuccess, poi chiudi il dialog\nonSuccess('Cavo modificato con successo');// Chiudi il dialog\nhandleCloseDialog();// Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\nsetTimeout(()=>{redirectToVisualizzaCavi(navigate,1000);},500);return;}catch(error){console.error('Errore durante l\\'aggiornamento del cavo:',error);// Gestione più dettagliata dell'errore\nlet errorMessage='Errore durante l\\'aggiornamento del cavo';if(error.response){// Errore dal server con risposta\nerrorMessage+=`: ${error.response.status} ${error.response.statusText}`;if(error.response.data&&error.response.data.detail){errorMessage+=` - ${error.response.data.detail}`;}}else if(error.request){// Errore di rete senza risposta dal server\nerrorMessage+=': Nessuna risposta dal server. Verifica la connessione di rete.';// Anche se c'è un errore di rete, la modifica potrebbe essere stata salvata\n// Quindi consideriamo l'operazione come riuscita\nconsole.log('Considerando l\\'operazione come riuscita nonostante l\\'errore di rete');// Prima chiama onSuccess, poi chiudi il dialog\nonSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');// Chiudi il dialog\nhandleCloseDialog();// Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\nsetTimeout(()=>{redirectToVisualizzaCavi(navigate,1000);},500);return;}else if(error.message){// Errore con messaggio\nerrorMessage+=`: ${error.message}`;// Se il messaggio indica che la modifica potrebbe essere stata salvata comunque\nif(error.message.includes('La modifica potrebbe essere stata salvata')){console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore');handleCloseDialog();onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');// Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\nredirectToVisualizzaCavi(navigate,1000);return;}}onError(errorMessage);setLoading(false);return;}// Mostra avvisi se presenti\nif(Object.keys(validation.warnings).length>0){const warningMessages=Object.values(validation.warnings).join('\\n');console.warn('Avvisi durante il salvataggio:',warningMessages);}}else if(dialogType==='eliminaCavo'){// Verifica se il cavo è installato\nconst isInstalled=selectedCavo.stato_installazione==='Installato'||selectedCavo.metratura_reale&&selectedCavo.metratura_reale>0;if(isInstalled){// Se è installato, marca solo come SPARE\nconsole.log('Marcando cavo installato come SPARE:',selectedCavo.id_cavo);try{// Prima prova con markCavoAsSpare\nconst result=await caviService.markCavoAsSpare(cantiereId,selectedCavo.id_cavo);console.log('Risultato marcatura SPARE:',result);console.log('Nuovo valore modificato_manualmente:',result.modificato_manualmente);// Chiudi il dialog prima di chiamare onSuccess\nhandleCloseDialog();onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);// Reindirizza alla pagina di visualizzazione cavi con un ritardo\nredirectToVisualizzaCavi(navigate,500);}catch(markError){console.error('Errore con markCavoAsSpare, tentativo con deleteCavo mode=spare:',markError);// Se fallisce, prova con deleteCavo mode=spare\nconst result=await caviService.deleteCavo(cantiereId,selectedCavo.id_cavo,'spare');console.log('Risultato marcatura SPARE con deleteCavo:',result);// Chiudi il dialog prima di chiamare onSuccess\nhandleCloseDialog();onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);}}else{// Se non è installato, usa la modalità selezionata (SPARE o DELETE)\nconsole.log('Eliminando cavo non installato con modalità:',deleteMode);const result=await caviService.deleteCavo(cantiereId,selectedCavo.id_cavo,deleteMode);console.log('Risultato eliminazione/marcatura:',result);// Chiudi il dialog prima di chiamare onSuccess\nhandleCloseDialog();onSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode==='spare'?'marcato come SPARE':'eliminato'} con successo`);}}// Non chiamare handleCloseDialog() qui, perché il dialog verrà chiuso dal genitore\n// quando viene chiamato onSuccess()\n}catch(error){console.error('Errore durante l\\'operazione:',error);// Gestione più dettagliata dell'errore\nlet errorMessage='Errore sconosciuto';if(error.detail){// Errore dal backend con dettaglio\nerrorMessage=error.detail;}else if(error.message){// Errore con messaggio\nerrorMessage=error.message;}else if(typeof error==='string'){// Errore come stringa\nerrorMessage=error;}onError('Errore durante l\\'operazione: '+errorMessage);}finally{setLoading(false);}};// Renderizza il dialog in base al tipo\nconst renderDialog=()=>{if(dialogType==='aggiungiCavo'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"md\",fullWidth:true,PaperProps:{sx:{width:'80%',// Allargato di 2cm (rispetto a sm che è circa 60%)\nminHeight:'80vh',// Allungato di 1cm (aggiungendo altezza minima)\nmaxHeight:'90vh',overflow:'auto'}},children:[/*#__PURE__*/_jsx(DialogTitle,{sx:{pb:1},children:\"Aggiungi Nuovo Cavo\"}),/*#__PURE__*/_jsx(DialogContent,{sx:{pt:0,pb:1},children:/*#__PURE__*/_jsx(Box,{sx:{mt:0},children:/*#__PURE__*/_jsx(CavoForm,{mode:\"add\",cantiereId:cantiereId,onSubmit:async validatedData=>{try{await caviService.createCavo(cantiereId,validatedData);return true;}catch(error){throw error;}},onSuccess:message=>{onSuccess(message);handleCloseDialog();},onError:onError,isDialog:true,onCancel:handleCloseDialog})})})]});}else if(dialogType==='inserisciMetri'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Inserisci Metri Posati\"}),/*#__PURE__*/_jsx(DialogContent,{children:caviLoading?/*#__PURE__*/_jsx(CircularProgress,{}):cavi.length===0?/*#__PURE__*/_jsx(Alert,{severity:\"info\",children:\"Nessun cavo disponibile\"}):!selectedCavo?/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Seleziona un cavo:\"}),/*#__PURE__*/_jsx(List,{children:cavi.map(cavo=>/*#__PURE__*/_jsx(ListItem,{button:true,onClick:()=>handleCavoSelect(cavo),children:/*#__PURE__*/_jsx(ListItemText,{primary:cavo.id_cavo,secondary:`${cavo.tipologia||'N/A'} - Da: ${cavo.ubicazione_partenza||'N/A'} A: ${cavo.ubicazione_arrivo||'N/A'}`})},cavo.id_cavo))})]}):/*#__PURE__*/_jsxs(Box,{sx:{mt:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,children:[\"Cavo selezionato: \",selectedCavo.id_cavo]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,children:[\"Metri teorici: \",selectedCavo.metri_teorici||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,children:[\"Metratura attuale: \",selectedCavo.metratura_reale||'0']}),/*#__PURE__*/_jsx(TextField,{margin:\"dense\",name:\"metri_posati\",label:\"Metri posati da aggiungere\",type:\"number\",fullWidth:true,variant:\"outlined\",value:formData.metri_posati,onChange:handleFormChange,required:true,error:!!formErrors.metri_posati,helperText:formErrors.metri_posati,sx:{mt:2}})]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Annulla\"}),selectedCavo&&/*#__PURE__*/_jsx(Button,{onClick:handleSave,disabled:loading||!formData.metri_posati,startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(SaveIcon,{}),children:\"Salva\"})]})]});}else if(dialogType==='modificaBobina'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Modifica Bobina Cavo Posato\"}),/*#__PURE__*/_jsx(DialogContent,{children:caviLoading?/*#__PURE__*/_jsx(CircularProgress,{}):cavi.length===0?/*#__PURE__*/_jsx(Alert,{severity:\"info\",children:\"Nessun cavo disponibile\"}):!selectedCavo?/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Seleziona un cavo:\"}),/*#__PURE__*/_jsx(List,{children:cavi.map(cavo=>/*#__PURE__*/_jsx(ListItem,{button:true,onClick:()=>handleCavoSelect(cavo),children:/*#__PURE__*/_jsx(ListItemText,{primary:cavo.id_cavo,secondary:`Bobina attuale: ${cavo.id_bobina?cavo.id_bobina==='BOBINA_VUOTA'?'BOBINA VUOTA':cavo.id_bobina:'BOBINA VUOTA'}`})},cavo.id_cavo))})]}):/*#__PURE__*/_jsxs(Box,{sx:{mt:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,children:[\"Cavo selezionato: \",selectedCavo.id_cavo]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,children:[\"Bobina attuale: \",selectedCavo.id_bobina?selectedCavo.id_bobina==='BOBINA_VUOTA'?'BOBINA VUOTA':selectedCavo.id_bobina:'BOBINA VUOTA']}),/*#__PURE__*/_jsx(TextField,{margin:\"dense\",name:\"id_bobina\",label:\"ID Bobina\",fullWidth:true,variant:\"outlined\",value:formData.id_bobina,onChange:handleFormChange,sx:{mt:2}})]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Annulla\"}),selectedCavo&&/*#__PURE__*/_jsx(Button,{onClick:handleSave,disabled:loading,startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(SaveIcon,{}),children:\"Salva\"})]})]});}else if(dialogType==='selezionaCavo'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Modifica Cavo\"}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(Alert,{severity:\"info\",sx:{mb:2},children:\"Puoi modificare solo i cavi non ancora posati (metratura = 0 e stato diverso da \\\"Installato\\\"). Per modificare cavi gi\\xE0 posati, utilizzare l'opzione \\\"Modifica bobina cavo posato\\\".\"}),caviLoading?/*#__PURE__*/_jsx(CircularProgress,{}):!selectedCavo?/*#__PURE__*/_jsxs(Box,{sx:{p:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Inserisci l'ID del cavo da modificare:\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mt:2},children:[/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"ID Cavo\",variant:\"outlined\",value:cavoIdInput,onChange:handleCavoIdInputChange,placeholder:\"Inserisci l'ID del cavo\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",onClick:handleSearchCavoById,disabled:caviLoading||!cavoIdInput.trim(),sx:{ml:2,minWidth:'120px'},children:caviLoading?/*#__PURE__*/_jsx(CircularProgress,{size:24}):\"Cerca\"})]})]}):null]}),/*#__PURE__*/_jsx(DialogActions,{children:/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Annulla\"})})]});}else if(dialogType==='eliminaCavo'){// Verifica se il cavo selezionato è installato\nconst isInstalled=selectedCavo&&(selectedCavo.stato_installazione==='Installato'||selectedCavo.metratura_reale&&selectedCavo.metratura_reale>0);return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:!selectedCavo?'Elimina Cavo':isInstalled?'Marca Cavo come SPARE':'Elimina Cavo'}),/*#__PURE__*/_jsx(DialogContent,{children:caviLoading?/*#__PURE__*/_jsx(CircularProgress,{}):!selectedCavo?/*#__PURE__*/_jsxs(Box,{sx:{p:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Inserisci l'ID del cavo da eliminare:\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mt:2},children:[/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"ID Cavo\",variant:\"outlined\",value:cavoIdInput,onChange:handleCavoIdInputChange,placeholder:\"Inserisci l'ID del cavo\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",onClick:handleSearchCavoById,disabled:caviLoading||!cavoIdInput.trim(),sx:{ml:2,minWidth:'120px'},children:caviLoading?/*#__PURE__*/_jsx(CircularProgress,{size:24}):\"Cerca\"})]})]}):dialogType==='eliminaCavo'&&isInstalled?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(DialogContentText,{children:[\"Il cavo \",/*#__PURE__*/_jsx(\"strong\",{children:selectedCavo.id_cavo}),\" risulta installato o parzialmente posato.\",selectedCavo.metratura_reale>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[\" Metri posati: \",/*#__PURE__*/_jsxs(\"strong\",{children:[selectedCavo.metratura_reale,\" m\"]}),\".\"]})]}),/*#__PURE__*/_jsx(DialogContentText,{sx:{mt:2},children:\"Non \\xE8 possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\"})]}):dialogType==='eliminaCavo'?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(DialogContentText,{children:[\"Stai per eliminare il cavo \",/*#__PURE__*/_jsx(\"strong\",{children:selectedCavo.id_cavo}),\".\"]}),/*#__PURE__*/_jsxs(FormControl,{component:\"fieldset\",sx:{mt:2},children:[/*#__PURE__*/_jsx(FormLabel,{component:\"legend\",children:\"Scegli l'operazione da eseguire:\"}),/*#__PURE__*/_jsxs(RadioGroup,{value:deleteMode,onChange:e=>setDeleteMode(e.target.value),children:[/*#__PURE__*/_jsx(FormControlLabel,{value:\"spare\",control:/*#__PURE__*/_jsx(Radio,{}),label:\"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"}),/*#__PURE__*/_jsx(FormControlLabel,{value:\"delete\",control:/*#__PURE__*/_jsx(Radio,{}),label:\"Elimina definitivamente (rimuove completamente il cavo dal database)\"})]})]})]}):null}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Annulla\"}),dialogType==='eliminaCavo'&&selectedCavo&&/*#__PURE__*/_jsx(Button,{onClick:handleSave,disabled:loading,color:isInstalled?\"warning\":\"error\",startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):isInstalled?/*#__PURE__*/_jsx(WarningIcon,{}):/*#__PURE__*/_jsx(DeleteIcon,{}),children:isInstalled?\"Marca come SPARE\":deleteMode==='spare'?\"Marca come SPARE\":\"Elimina definitivamente\"})]})]});}else if(dialogType==='modificaCavo'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"md\",fullWidth:true,PaperProps:{sx:{width:'80%',// Allargato di 2cm (rispetto a sm che è circa 60%)\nminHeight:'80vh',// Allungato di 1cm (aggiungendo altezza minima)\nmaxHeight:'90vh',overflow:'auto'}},children:[/*#__PURE__*/_jsx(DialogTitle,{sx:{pb:1},children:\"Modifica Cavo\"}),/*#__PURE__*/_jsx(DialogContent,{sx:{pt:0,pb:1},children:/*#__PURE__*/_jsx(Box,{sx:{mt:0},children:selectedCavo?/*#__PURE__*/_jsx(CavoForm,{mode:\"edit\",initialData:formData,cantiereId:cantiereId,onSubmit:async validatedData=>{try{// Rimuovi i campi di sistema che non devono essere modificati\nconst dataToSend={...validatedData};delete dataToSend.id_bobina;// Rimuovi id_bobina perché è un campo di sistema\ndelete dataToSend.metratura_reale;// Rimuovi metratura_reale perché è un campo di sistema\ndelete dataToSend.modificato_manualmente;// Rimuovi modificato_manualmente perché è un campo di sistema\ndelete dataToSend.timestamp;// Rimuovi timestamp perché è un campo di sistema\ndelete dataToSend.stato_installazione;// Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n// Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\ndataToSend.modificato_manualmente=1;await caviService.updateCavo(cantiereId,dataToSend.id_cavo,dataToSend);return true;}catch(error){throw error;}},onSuccess:message=>{onSuccess(message);handleCloseDialog();},onError:onError,isDialog:true,onCancel:handleCloseDialog}):/*#__PURE__*/_jsx(SelezionaCavoForm,{cantiereId:cantiereId,onSuccess:message=>{onSuccess(message);handleCloseDialog();},onError:onError,isDialog:true,onCancel:handleCloseDialog})})})]});}return null;};return/*#__PURE__*/_jsxs(Box,{sx:{display:'flex'},children:[/*#__PURE__*/_jsx(Box,{sx:{width:'280px',mr:3},children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Posa Cavi e Collegamenti\"}),/*#__PURE__*/_jsx(Divider,{sx:{mb:2}}),/*#__PURE__*/_jsxs(List,{component:\"nav\",dense:true,children:[/*#__PURE__*/_jsxs(ListItemButton,{onClick:()=>handleOptionSelect('inserisciMetri'),children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(CableIcon,{})}),/*#__PURE__*/_jsx(ListItemText,{primary:\"1. Inserisci metri posati\"})]}),/*#__PURE__*/_jsxs(ListItemButton,{onClick:()=>handleOptionSelect('modificaBobina'),children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(EditIcon,{})}),/*#__PURE__*/_jsx(ListItemText,{primary:\"2. Modifica bobina cavo posato\"})]}),/*#__PURE__*/_jsxs(ListItemButton,{onClick:()=>handleOptionSelect('aggiungiCavo'),children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(AddIcon,{})}),/*#__PURE__*/_jsx(ListItemText,{primary:\"3. Aggiungi nuovo cavo\"})]}),/*#__PURE__*/_jsxs(ListItemButton,{onClick:()=>handleOptionSelect('modificaCavo'),children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(EditIcon,{})}),/*#__PURE__*/_jsx(ListItemText,{primary:\"4. Modifica cavo\"})]}),/*#__PURE__*/_jsxs(ListItemButton,{onClick:()=>handleOptionSelect('eliminaCavo'),children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(DeleteIcon,{})}),/*#__PURE__*/_jsx(ListItemText,{primary:\"5. Elimina cavo\"})]}),/*#__PURE__*/_jsxs(ListItemButton,{onClick:()=>handleOptionSelect('collegamentoCavo'),children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(CableIcon,{})}),/*#__PURE__*/_jsx(ListItemText,{primary:\"6. Collegamento cavo\"})]})]})]})}),/*#__PURE__*/_jsx(Box,{sx:{flexGrow:1},children:/*#__PURE__*/_jsxs(Paper,{sx:{p:3,minHeight:'300px',display:'flex',alignItems:'center',justifyContent:'center'},children:[!selectedOption&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:\"Seleziona un'opzione dal menu a sinistra per iniziare.\"}),selectedOption&&!openDialog&&/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,children:[selectedOption==='inserisciMetri'&&'Inserisci metri posati',selectedOption==='modificaCavo'&&'Modifica cavo',selectedOption==='aggiungiCavo'&&'Aggiungi nuovo cavo',selectedOption==='eliminaCavo'&&'Elimina cavo',selectedOption==='modificaBobina'&&'Modifica bobina cavo posato',selectedOption==='collegamentoCavo'&&'Collegamento cavo']}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:\"Caricamento in corso...\"}),/*#__PURE__*/_jsx(CircularProgress,{sx:{mt:2}})]})]})}),renderDialog()]});};export default PosaCaviCollegamenti;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Divider", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Grid", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "Radio", "RadioGroup", "FormControlLabel", "FormLabel", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Cable", "CableIcon", "Save", "SaveIcon", "Warning", "WarningIcon", "useNavigate", "caviService", "validateCavoData", "validateField", "isEmpty", "redirectToVisualizzaCavi", "CavoForm", "SelezionaCavoForm", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "PosaCaviCollegamenti", "_ref", "cantiereId", "propCantiereId", "onSuccess", "onError", "initialOption", "console", "log", "navigate", "parseInt", "localStorage", "getItem", "loading", "setLoading", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "cavoIdInput", "setCavoIdInput", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "cavi", "<PERSON><PERSON><PERSON>", "caviLoading", "setCaviLoading", "deleteMode", "setDeleteMode", "useEffect", "loadCavi", "operationType", "Error", "caviData", "get<PERSON><PERSON>", "cavi<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "cavo", "parseFloat", "metratura_reale", "stato_installazione", "error", "errorMessage", "response", "status", "statusText", "data", "detail", "request", "message", "handleOptionSelect", "option", "handleCloseDialog", "handleCavoSelect", "metri_te<PERSON>ci", "handleSearchCavoById", "trim", "find", "c", "handleCavoIdInputChange", "e", "target", "value", "handleFormChange", "name", "additionalParams", "metriTeorici", "result", "prev", "valid", "warning", "handleSave", "isNaN", "updateMetri<PERSON><PERSON><PERSON>", "setTimeout", "updateBobina", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "validatedData", "dataToSend", "modificato_manualmente", "timestamp", "updateCavo", "includes", "Object", "keys", "length", "warningMessages", "values", "join", "warn", "isInstalled", "markCavoAsSpare", "<PERSON><PERSON><PERSON><PERSON>", "deleteCavo", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "sx", "width", "minHeight", "maxHeight", "overflow", "children", "pb", "pt", "mt", "mode", "onSubmit", "createCavo", "isDialog", "onCancel", "severity", "variant", "gutterBottom", "map", "button", "onClick", "primary", "secondary", "tipologia", "ubicazione_partenza", "ubicazione_arrivo", "margin", "label", "type", "onChange", "required", "helperText", "disabled", "startIcon", "size", "mb", "p", "display", "alignItems", "placeholder", "color", "ml", "min<PERSON><PERSON><PERSON>", "component", "control", "initialData", "mr", "dense", "flexGrow", "justifyContent", "textAlign"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/PosaCaviCollegamenti.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogContentText,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Grid,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  FormLabel\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Cable as CableIcon,\n  Save as SaveIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport CavoForm from './CavoForm';\nimport SelezionaCavoForm from './SelezionaCavoForm';\n\nconst PosaCaviCollegamenti = ({ cantiereId: propCantiereId, onSuccess, onError, initialOption = null }) => {\n  // Log del cantiereId all'avvio\n  console.log('PosaCaviCollegamenti - cantiereId da props:', propCantiereId);\n\n  // Aggiungi navigate per la navigazione programmatica\n  const navigate = useNavigate();\n\n  // Se cantiereId non è definito nelle props, prova a recuperarlo dal localStorage\n  const cantiereId = propCantiereId || parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  console.log('PosaCaviCollegamenti - cantiereId effettivo:', cantiereId);\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n\n  // Inizializza il componente con l'opzione iniziale se specificata\n  React.useEffect(() => {\n    if (initialOption) {\n      // Imposta direttamente le opzioni invece di chiamare handleOptionSelect\n      // per evitare dipendenze circolari\n      setSelectedOption(initialOption);\n\n      if (initialOption === 'eliminaCavo') {\n        loadCavi('eliminaCavo');\n        setDialogType('eliminaCavo');\n        setOpenDialog(true);\n      } else if (initialOption === 'modificaCavo') {\n        loadCavi('modificaCavo');\n        setDialogType('selezionaCavo');\n        setOpenDialog(true);\n      } else if (initialOption === 'aggiungiCavo') {\n        setDialogType('aggiungiCavo');\n        setOpenDialog(true);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [initialOption]);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async (operationType) => {\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n\n      // Filtra i cavi in base al tipo di operazione\n      if (operationType === 'modificaCavo') {\n        // Per modifica cavo, mostra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\n        const caviNonPosati = caviData.filter(cavo =>\n          parseFloat(cavo.metratura_reale) === 0 &&\n          cavo.stato_installazione !== 'Installato'\n        );\n        setCavi(caviNonPosati);\n      } else {\n        // Per altre operazioni, mostra tutti i cavi\n        setCavi(caviData);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi(option);\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      // Apri il dialog per aggiungere un nuovo cavo\n      setDialogType('aggiungiCavo');\n      setOpenDialog(true);\n    } else if (option === 'modificaCavo') {\n      loadCavi('modificaCavo');\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi('eliminaCavo');\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    } else if (option === 'collegamentoCavo') {\n      // Reindirizza alla pagina di gestione collegamenti usando navigate\n      navigate(`/dashboard/cantieri/${cantiereId}/cavi/posa/collegamenti`);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    console.log('Chiusura dialog...');\n    // Reset dello stato del componente\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setCavoIdInput('');\n    setFormErrors({});\n    setFormWarnings({});\n    setDeleteMode('spare'); // Reset alla modalità predefinita\n    setLoading(false); // Assicurati che loading sia false quando chiudi il dialog\n\n    // Se è stato specificato un initialOption, notifica il genitore che il dialogo è stato chiuso\n    // ma senza messaggio di errore\n    if (initialOption && onSuccess) {\n      // Chiama onSuccess ma senza messaggio per evitare l'alert\n      onSuccess(null);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setSelectedCavo(cavo);\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n\n      console.log('Ricerca cavo con ID:', cavoIdInput, 'per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      const cavo = caviData.find(c => c.id_cavo === cavoIdInput.trim());\n\n      if (!cavo) {\n        onError(`Cavo con ID ${cavoIdInput} non trovato`);\n        return;\n      }\n\n      // Verifica se stiamo cercando un cavo per modificarlo\n      if (dialogType === 'selezionaCavo') {\n        // Verifica che il cavo non sia già posato\n        if (parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato') {\n          onError(`Il cavo ${cavo.id_cavo} risulta già posato. Utilizza l'opzione \"Modifica bobina cavo posato\" per modificarlo.`);\n          return;\n        }\n      }\n\n      // Seleziona il cavo trovato\n      handleCavoSelect(cavo);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce il cambio dell'input dell'ID cavo\n  const handleCavoIdInputChange = (e) => {\n    setCavoIdInput(e.target.value);\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({ metri_posati: 'Inserire un valore numerico valido' });\n          setLoading(false);\n          return;\n        }\n\n        try {\n          await caviService.updateMetriPosati(\n            cantiereId,\n            formData.id_cavo,\n            parseFloat(formData.metri_posati)\n          );\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Metri posati aggiornati con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n          onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n          setLoading(false);\n        }\n      } else if (dialogType === 'modificaBobina') {\n        try {\n          await caviService.updateBobina(\n            cantiereId,\n            formData.id_cavo,\n            formData.id_bobina\n          );\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Bobina aggiornata con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento della bobina:', error);\n          onError('Errore durante l\\'aggiornamento della bobina: ' + (error.message || 'Errore sconosciuto'));\n          setLoading(false);\n        }\n      } else if (dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n\n        // Rimuovi i campi di sistema che non devono essere modificati\n        const dataToSend = { ...validatedData };\n        delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n        delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n        delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n        delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n        delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n        // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n        dataToSend.modificato_manualmente = 1;\n\n        console.log('Dati inviati al server:', dataToSend);\n\n        try {\n          console.log('Invio dati al server per aggiornamento cavo:', dataToSend);\n          const result = await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n          console.log('Risposta dal server dopo aggiornamento cavo:', result);\n\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Cavo modificato con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n          return;\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento del cavo:', error);\n\n          // Gestione più dettagliata dell'errore\n          let errorMessage = 'Errore durante l\\'aggiornamento del cavo';\n\n          if (error.response) {\n            // Errore dal server con risposta\n            errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n            if (error.response.data && error.response.data.detail) {\n              errorMessage += ` - ${error.response.data.detail}`;\n            }\n          } else if (error.request) {\n            // Errore di rete senza risposta dal server\n            errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n\n            // Anche se c'è un errore di rete, la modifica potrebbe essere stata salvata\n            // Quindi consideriamo l'operazione come riuscita\n            console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore di rete');\n            // Prima chiama onSuccess, poi chiudi il dialog\n            onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');\n            // Chiudi il dialog\n            handleCloseDialog();\n            // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n            setTimeout(() => {\n              redirectToVisualizzaCavi(navigate, 1000);\n            }, 500);\n            return;\n          } else if (error.message) {\n            // Errore con messaggio\n            errorMessage += `: ${error.message}`;\n\n            // Se il messaggio indica che la modifica potrebbe essere stata salvata comunque\n            if (error.message.includes('La modifica potrebbe essere stata salvata')) {\n              console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore');\n              handleCloseDialog();\n              onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');\n              // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n              redirectToVisualizzaCavi(navigate, 1000);\n              return;\n            }\n          }\n\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        // Verifica se il cavo è installato\n        const isInstalled = selectedCavo.stato_installazione === 'Installato' || (selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0);\n\n        if (isInstalled) {\n          // Se è installato, marca solo come SPARE\n          console.log('Marcando cavo installato come SPARE:', selectedCavo.id_cavo);\n          try {\n            // Prima prova con markCavoAsSpare\n            const result = await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo);\n            console.log('Risultato marcatura SPARE:', result);\n            console.log('Nuovo valore modificato_manualmente:', result.modificato_manualmente);\n            // Chiudi il dialog prima di chiamare onSuccess\n            handleCloseDialog();\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n            // Reindirizza alla pagina di visualizzazione cavi con un ritardo\n            redirectToVisualizzaCavi(navigate, 500);\n          } catch (markError) {\n            console.error('Errore con markCavoAsSpare, tentativo con deleteCavo mode=spare:', markError);\n            // Se fallisce, prova con deleteCavo mode=spare\n            const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, 'spare');\n            console.log('Risultato marcatura SPARE con deleteCavo:', result);\n            // Chiudi il dialog prima di chiamare onSuccess\n            handleCloseDialog();\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n          }\n        } else {\n          // Se non è installato, usa la modalità selezionata (SPARE o DELETE)\n          console.log('Eliminando cavo non installato con modalità:', deleteMode);\n          const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n          console.log('Risultato eliminazione/marcatura:', result);\n          // Chiudi il dialog prima di chiamare onSuccess\n          handleCloseDialog();\n          onSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n        }\n      }\n\n      // Non chiamare handleCloseDialog() qui, perché il dialog verrà chiuso dal genitore\n      // quando viene chiamato onSuccess()\n    } catch (error) {\n      console.error('Errore durante l\\'operazione:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore sconosciuto';\n\n      if (error.detail) {\n        // Errore dal backend con dettaglio\n        errorMessage = error.detail;\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage = error.message;\n      } else if (typeof error === 'string') {\n        // Errore come stringa\n        errorMessage = error;\n      }\n\n      onError('Errore durante l\\'operazione: ' + errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'aggiungiCavo') {\n      return (\n        <Dialog\n          open={openDialog}\n          onClose={handleCloseDialog}\n          maxWidth=\"md\"\n          fullWidth\n          PaperProps={{\n            sx: {\n              width: '80%',  // Allargato di 2cm (rispetto a sm che è circa 60%)\n              minHeight: '80vh', // Allungato di 1cm (aggiungendo altezza minima)\n              maxHeight: '90vh',\n              overflow: 'auto'\n            }\n          }}\n        >\n          <DialogTitle sx={{ pb: 1 }}>Aggiungi Nuovo Cavo</DialogTitle>\n          <DialogContent sx={{ pt: 0, pb: 1 }}>\n            <Box sx={{ mt: 0 }}>\n              <CavoForm\n                mode=\"add\"\n                cantiereId={cantiereId}\n                onSubmit={async (validatedData) => {\n                  try {\n                    await caviService.createCavo(cantiereId, validatedData);\n                    return true;\n                  } catch (error) {\n                    throw error;\n                  }\n                }}\n                onSuccess={(message) => {\n                  onSuccess(message);\n                  handleCloseDialog();\n                }}\n                onError={onError}\n                isDialog={true}\n                onCancel={handleCloseDialog}\n              />\n            </Box>\n          </DialogContent>\n          {/* No DialogActions needed here as CavoForm has its own buttons */}\n        </Dialog>\n      );\n    } else if (dialogType === 'inserisciMetri') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Inserisci Metri Posati</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metri teorici: {selectedCavo.metri_teorici || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metratura attuale: {selectedCavo.metratura_reale || '0'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"metri_posati\"\n                  label=\"Metri posati da aggiungere\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_posati}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_posati}\n                  helperText={formErrors.metri_posati}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading || !formData.metri_posati}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Bobina Cavo Posato</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`Bobina attuale: ${cavo.id_bobina ? (cavo.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : cavo.id_bobina) : 'BOBINA VUOTA'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Bobina attuale: {selectedCavo.id_bobina ? (selectedCavo.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : selectedCavo.id_bobina) : 'BOBINA VUOTA'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"id_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_bobina}\n                  onChange={handleFormChange}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Modifica Cavo</DialogTitle>\n          <DialogContent>\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Puoi modificare solo i cavi non ancora posati (metratura = 0 e stato diverso da \"Installato\").\n              Per modificare cavi già posati, utilizzare l'opzione \"Modifica bobina cavo posato\".\n            </Alert>\n\n            {caviLoading ? (\n              <CircularProgress />\n            ) : !selectedCavo ? (\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Inserisci l'ID del cavo da modificare:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>\n                  <TextField\n                    fullWidth\n                    label=\"ID Cavo\"\n                    variant=\"outlined\"\n                    value={cavoIdInput}\n                    onChange={handleCavoIdInputChange}\n                    placeholder=\"Inserisci l'ID del cavo\"\n                  />\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    onClick={handleSearchCavoById}\n                    disabled={caviLoading || !cavoIdInput.trim()}\n                    sx={{ ml: 2, minWidth: '120px' }}\n                  >\n                    {caviLoading ? <CircularProgress size={24} /> : \"Cerca\"}\n                  </Button>\n                </Box>\n              </Box>\n            ) : null}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaCavo') {\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = selectedCavo && (selectedCavo.stato_installazione === 'Installato' || (selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0));\n\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>\n            {!selectedCavo ? 'Elimina Cavo' :\n             isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'}\n          </DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : !selectedCavo ? (\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Inserisci l'ID del cavo da eliminare:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>\n                  <TextField\n                    fullWidth\n                    label=\"ID Cavo\"\n                    variant=\"outlined\"\n                    value={cavoIdInput}\n                    onChange={handleCavoIdInputChange}\n                    placeholder=\"Inserisci l'ID del cavo\"\n                  />\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    onClick={handleSearchCavoById}\n                    disabled={caviLoading || !cavoIdInput.trim()}\n                    sx={{ ml: 2, minWidth: '120px' }}\n                  >\n                    {caviLoading ? <CircularProgress size={24} /> : \"Cerca\"}\n                  </Button>\n                </Box>\n              </Box>\n            ) : dialogType === 'eliminaCavo' && isInstalled ? (\n              <>\n                <DialogContentText>\n                  Il cavo <strong>{selectedCavo.id_cavo}</strong> risulta installato o parzialmente posato.\n                  {selectedCavo.metratura_reale > 0 && (\n                    <> Metri posati: <strong>{selectedCavo.metratura_reale} m</strong>.</>\n                  )}\n                </DialogContentText>\n                <DialogContentText sx={{ mt: 2 }}>\n                  Non è possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\n                </DialogContentText>\n              </>\n            ) : dialogType === 'eliminaCavo' ? (\n              <>\n                <DialogContentText>\n                  Stai per eliminare il cavo <strong>{selectedCavo.id_cavo}</strong>.\n                </DialogContentText>\n\n                <FormControl component=\"fieldset\" sx={{ mt: 2 }}>\n                  <FormLabel component=\"legend\">Scegli l'operazione da eseguire:</FormLabel>\n                  <RadioGroup\n                    value={deleteMode}\n                    onChange={(e) => setDeleteMode(e.target.value)}\n                  >\n                    <FormControlLabel\n                      value=\"spare\"\n                      control={<Radio />}\n                      label=\"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                    />\n                    <FormControlLabel\n                      value=\"delete\"\n                      control={<Radio />}\n                      label=\"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                    />\n                  </RadioGroup>\n                </FormControl>\n              </>\n            ) : null}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {dialogType === 'eliminaCavo' && selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color={isInstalled ? \"warning\" : \"error\"}\n                startIcon={loading ? <CircularProgress size={20} /> : isInstalled ? <WarningIcon /> : <DeleteIcon />}\n              >\n                {isInstalled ? \"Marca come SPARE\" : (deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\")}\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaCavo') {\n      return (\n        <Dialog\n          open={openDialog}\n          onClose={handleCloseDialog}\n          maxWidth=\"md\"\n          fullWidth\n          PaperProps={{\n            sx: {\n              width: '80%',  // Allargato di 2cm (rispetto a sm che è circa 60%)\n              minHeight: '80vh', // Allungato di 1cm (aggiungendo altezza minima)\n              maxHeight: '90vh',\n              overflow: 'auto'\n            }\n          }}\n        >\n          <DialogTitle sx={{ pb: 1 }}>Modifica Cavo</DialogTitle>\n          <DialogContent sx={{ pt: 0, pb: 1 }}>\n            <Box sx={{ mt: 0 }}>\n              {selectedCavo ? (\n                <CavoForm\n                  mode=\"edit\"\n                  initialData={formData}\n                  cantiereId={cantiereId}\n                  onSubmit={async (validatedData) => {\n                    try {\n                      // Rimuovi i campi di sistema che non devono essere modificati\n                      const dataToSend = { ...validatedData };\n                      delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n                      delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n                      delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n                      delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n                      delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n                      // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n                      dataToSend.modificato_manualmente = 1;\n\n                      await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n                      return true;\n                    } catch (error) {\n                      throw error;\n                    }\n                  }}\n                  onSuccess={(message) => {\n                    onSuccess(message);\n                    handleCloseDialog();\n                  }}\n                  onError={onError}\n                  isDialog={true}\n                  onCancel={handleCloseDialog}\n                />\n              ) : (\n                <SelezionaCavoForm\n                  cantiereId={cantiereId}\n                  onSuccess={(message) => {\n                    onSuccess(message);\n                    handleCloseDialog();\n                  }}\n                  onError={onError}\n                  isDialog={true}\n                  onCancel={handleCloseDialog}\n                />\n              )}\n            </Box>\n          </DialogContent>\n          {/* No DialogActions needed here as CavoForm has its own buttons */}\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      {/* Menu a cascata nella sidebar */}\n      <Box sx={{ width: '280px', mr: 3 }}>\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Posa Cavi e Collegamenti\n          </Typography>\n          <Divider sx={{ mb: 2 }} />\n          <List component=\"nav\" dense>\n            <ListItemButton onClick={() => handleOptionSelect('inserisciMetri')}>\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"1. Inserisci metri posati\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaBobina')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"2. Modifica bobina cavo posato\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('aggiungiCavo')}>\n              <ListItemIcon>\n                <AddIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"3. Aggiungi nuovo cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaCavo')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"4. Modifica cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('eliminaCavo')}>\n              <ListItemIcon>\n                <DeleteIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"5. Elimina cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('collegamentoCavo')}>\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"6. Collegamento cavo\" />\n            </ListItemButton>\n          </List>\n        </Paper>\n      </Box>\n\n      {/* Area principale per il contenuto */}\n      <Box sx={{ flexGrow: 1 }}>\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption && (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu a sinistra per iniziare.\n            </Typography>\n          )}\n          {selectedOption && !openDialog && (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'inserisciMetri' && 'Inserisci metri posati'}\n                {selectedOption === 'modificaCavo' && 'Modifica cavo'}\n                {selectedOption === 'aggiungiCavo' && 'Aggiungi nuovo cavo'}\n                {selectedOption === 'eliminaCavo' && 'Elimina cavo'}\n                {selectedOption === 'modificaBobina' && 'Modifica bobina cavo posato'}\n                {selectedOption === 'collegamentoCavo' && 'Collegamento cavo'}\n              </Typography>\n              <Typography variant=\"body1\">\n                Caricamento in corso...\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      </Box>\n\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default PosaCaviCollegamenti;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,GAAG,CACHC,UAAU,CACVC,MAAM,CACNC,KAAK,CACLC,OAAO,CACPC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,iBAAiB,CACjBC,aAAa,CACbC,SAAS,CACTC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,CACRC,IAAI,CACJC,KAAK,CACLC,gBAAgB,CAChBC,cAAc,CACdC,KAAK,CACLC,UAAU,CACVC,gBAAgB,CAChBC,SAAS,KACJ,eAAe,CACtB,OACEC,GAAG,GAAI,CAAAC,OAAO,CACdC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,KAAK,GAAI,CAAAC,SAAS,CAClBC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,OAAO,GAAI,CAAAC,WAAW,KACjB,qBAAqB,CAC5B,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CACpD,OAASC,gBAAgB,CAAEC,aAAa,CAAEC,OAAO,KAAQ,6BAA6B,CACtF,OAASC,wBAAwB,KAAQ,6BAA6B,CACtE,MAAO,CAAAC,QAAQ,KAAM,YAAY,CACjC,MAAO,CAAAC,iBAAiB,KAAM,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEpD,KAAM,CAAAC,oBAAoB,CAAGC,IAAA,EAA8E,IAA7E,CAAEC,UAAU,CAAEC,cAAc,CAAEC,SAAS,CAAEC,OAAO,CAAEC,aAAa,CAAG,IAAK,CAAC,CAAAL,IAAA,CACpG;AACAM,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAEL,cAAc,CAAC,CAE1E;AACA,KAAM,CAAAM,QAAQ,CAAGvB,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAgB,UAAU,CAAGC,cAAc,EAAIO,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAE,EAAE,CAAC,CAC7FL,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAEN,UAAU,CAAC,CACvE,KAAM,CAACW,OAAO,CAAEC,UAAU,CAAC,CAAGrE,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACsE,cAAc,CAAEC,iBAAiB,CAAC,CAAGvE,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACwE,UAAU,CAAEC,aAAa,CAAC,CAAGzE,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC0E,UAAU,CAAEC,aAAa,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC4E,YAAY,CAAEC,eAAe,CAAC,CAAG7E,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAC8E,QAAQ,CAAEC,WAAW,CAAC,CAAG/E,QAAQ,CAAC,CACvCgF,OAAO,CAAE,EAAE,CACXC,YAAY,CAAE,EAAE,CAChBC,SAAS,CAAE,EACb,CAAC,CAAC,CACF,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGpF,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACqF,UAAU,CAAEC,aAAa,CAAC,CAAGtF,QAAQ,CAAC,CAAC,CAAC,CAAC,CAChD,KAAM,CAACuF,YAAY,CAAEC,eAAe,CAAC,CAAGxF,QAAQ,CAAC,CAAC,CAAC,CAAC,CACpD,KAAM,CAACyF,IAAI,CAAEC,OAAO,CAAC,CAAG1F,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAC2F,WAAW,CAAEC,cAAc,CAAC,CAAG5F,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAC6F,UAAU,CAAEC,aAAa,CAAC,CAAG9F,QAAQ,CAAC,OAAO,CAAC,CAAE;AAEvD;AACAD,KAAK,CAACgG,SAAS,CAAC,IAAM,CACpB,GAAIlC,aAAa,CAAE,CACjB;AACA;AACAU,iBAAiB,CAACV,aAAa,CAAC,CAEhC,GAAIA,aAAa,GAAK,aAAa,CAAE,CACnCmC,QAAQ,CAAC,aAAa,CAAC,CACvBrB,aAAa,CAAC,aAAa,CAAC,CAC5BF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,IAAIZ,aAAa,GAAK,cAAc,CAAE,CAC3CmC,QAAQ,CAAC,cAAc,CAAC,CACxBrB,aAAa,CAAC,eAAe,CAAC,CAC9BF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,IAAIZ,aAAa,GAAK,cAAc,CAAE,CAC3Cc,aAAa,CAAC,cAAc,CAAC,CAC7BF,aAAa,CAAC,IAAI,CAAC,CACrB,CACF,CACA;AACF,CAAC,CAAE,CAACZ,aAAa,CAAC,CAAC,CAEnB;AACA,KAAM,CAAAmC,QAAQ,CAAG,KAAO,CAAAC,aAAa,EAAK,CACxC,GAAI,CACFL,cAAc,CAAC,IAAI,CAAC,CAEpB;AACA,GAAI,CAACnC,UAAU,CAAE,CACf,KAAM,IAAI,CAAAyC,KAAK,CAAC,mCAAmC,CAAC,CACtD,CAEApC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEN,UAAU,CAAC,CACzD,KAAM,CAAA0C,QAAQ,CAAG,KAAM,CAAAzD,WAAW,CAAC0D,OAAO,CAAC3C,UAAU,CAAE,CAAC,CAAC,CAEzD;AACA,GAAIwC,aAAa,GAAK,cAAc,CAAE,CACpC;AACA,KAAM,CAAAI,aAAa,CAAGF,QAAQ,CAACG,MAAM,CAACC,IAAI,EACxCC,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,GAAK,CAAC,EACtCF,IAAI,CAACG,mBAAmB,GAAK,YAC/B,CAAC,CACDhB,OAAO,CAACW,aAAa,CAAC,CACxB,CAAC,IAAM,CACL;AACAX,OAAO,CAACS,QAAQ,CAAC,CACnB,CACF,CAAE,MAAOQ,KAAK,CAAE,CACd7C,OAAO,CAAC6C,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CAExD;AACA,GAAI,CAAAC,YAAY,CAAG,iCAAiC,CAEpD,GAAID,KAAK,CAACE,QAAQ,CAAE,CAClB;AACAD,YAAY,EAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE,CACzE,GAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,EAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,CAAE,CACrDL,YAAY,EAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE,CACpD,CACF,CAAC,IAAM,IAAIN,KAAK,CAACO,OAAO,CAAE,CACxB;AACAN,YAAY,EAAI,iEAAiE,CACnF,CAAC,IAAM,IAAID,KAAK,CAACQ,OAAO,CAAE,CACxB;AACAP,YAAY,EAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE,CACtC,CAEAvD,OAAO,CAACgD,YAAY,CAAC,CACvB,CAAC,OAAS,CACRhB,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAAC,CAED;AACA,KAAM,CAAAwB,kBAAkB,CAAIC,MAAM,EAAK,CACrC9C,iBAAiB,CAAC8C,MAAM,CAAC,CAEzB,GAAIA,MAAM,GAAK,gBAAgB,EAAIA,MAAM,GAAK,gBAAgB,CAAE,CAC9DrB,QAAQ,CAACqB,MAAM,CAAC,CAChB1C,aAAa,CAAC0C,MAAM,CAAC,CACrB5C,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,IAAI4C,MAAM,GAAK,cAAc,CAAE,CACpC;AACA1C,aAAa,CAAC,cAAc,CAAC,CAC7BF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,IAAI4C,MAAM,GAAK,cAAc,CAAE,CACpCrB,QAAQ,CAAC,cAAc,CAAC,CACxBrB,aAAa,CAAC,eAAe,CAAC,CAC9BF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,IAAI4C,MAAM,GAAK,aAAa,CAAE,CACnCrB,QAAQ,CAAC,aAAa,CAAC,CACvBrB,aAAa,CAAC,aAAa,CAAC,CAC5BF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,IAAI4C,MAAM,GAAK,kBAAkB,CAAE,CACxC;AACArD,QAAQ,CAAC,uBAAuBP,UAAU,yBAAyB,CAAC,CACtE,CACF,CAAC,CAED;AACA,KAAM,CAAA6D,iBAAiB,CAAGA,CAAA,GAAM,CAC9BxD,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjC;AACAU,aAAa,CAAC,KAAK,CAAC,CACpBI,eAAe,CAAC,IAAI,CAAC,CACrBE,WAAW,CAAC,CACVC,OAAO,CAAE,EAAE,CACXC,YAAY,CAAE,EAAE,CAChBC,SAAS,CAAE,EACb,CAAC,CAAC,CACFE,cAAc,CAAC,EAAE,CAAC,CAClBE,aAAa,CAAC,CAAC,CAAC,CAAC,CACjBE,eAAe,CAAC,CAAC,CAAC,CAAC,CACnBM,aAAa,CAAC,OAAO,CAAC,CAAE;AACxBzB,UAAU,CAAC,KAAK,CAAC,CAAE;AAEnB;AACA;AACA,GAAIR,aAAa,EAAIF,SAAS,CAAE,CAC9B;AACAA,SAAS,CAAC,IAAI,CAAC,CACjB,CACF,CAAC,CAED;AACA,KAAM,CAAA4D,gBAAgB,CAAIhB,IAAI,EAAK,CACjC1B,eAAe,CAAC0B,IAAI,CAAC,CACrB,GAAI7B,UAAU,GAAK,gBAAgB,CAAE,CACnCK,WAAW,CAAC,CACV,GAAGD,QAAQ,CACXE,OAAO,CAAEuB,IAAI,CAACvB,OAAO,CACrBC,YAAY,CAAE,EAChB,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIP,UAAU,GAAK,gBAAgB,CAAE,CAC1CK,WAAW,CAAC,CACV,GAAGD,QAAQ,CACXE,OAAO,CAAEuB,IAAI,CAACvB,OAAO,CACrBE,SAAS,CAAEqB,IAAI,CAACrB,SAAS,EAAI,EAC/B,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIR,UAAU,GAAK,eAAe,CAAE,CACzCC,aAAa,CAAC,cAAc,CAAC,CAC7BE,eAAe,CAAC0B,IAAI,CAAC,CACrBxB,WAAW,CAAC,CACV,GAAGwB,IAAI,CACPiB,aAAa,CAAEjB,IAAI,CAACiB,aAAa,EAAI,EAAE,CACvCf,eAAe,CAAEF,IAAI,CAACE,eAAe,EAAI,GAC3C,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAgB,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CAACtC,WAAW,CAACuC,IAAI,CAAC,CAAC,CAAE,CACvB9D,OAAO,CAAC,6BAA6B,CAAC,CACtC,OACF,CAEA,GAAI,CACFgC,cAAc,CAAC,IAAI,CAAC,CAEpB;AACA,GAAI,CAACnC,UAAU,CAAE,CACf,KAAM,IAAI,CAAAyC,KAAK,CAAC,mCAAmC,CAAC,CACtD,CAEApC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAEoB,WAAW,CAAE,eAAe,CAAE1B,UAAU,CAAC,CAC7E,KAAM,CAAA0C,QAAQ,CAAG,KAAM,CAAAzD,WAAW,CAAC0D,OAAO,CAAC3C,UAAU,CAAE,CAAC,CAAC,CACzD,KAAM,CAAA8C,IAAI,CAAGJ,QAAQ,CAACwB,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAC5C,OAAO,GAAKG,WAAW,CAACuC,IAAI,CAAC,CAAC,CAAC,CAEjE,GAAI,CAACnB,IAAI,CAAE,CACT3C,OAAO,CAAC,eAAeuB,WAAW,cAAc,CAAC,CACjD,OACF,CAEA;AACA,GAAIT,UAAU,GAAK,eAAe,CAAE,CAClC;AACA,GAAI8B,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,CAAG,CAAC,EAAIF,IAAI,CAACG,mBAAmB,GAAK,YAAY,CAAE,CACrF9C,OAAO,CAAC,WAAW2C,IAAI,CAACvB,OAAO,wFAAwF,CAAC,CACxH,OACF,CACF,CAEA;AACAuC,gBAAgB,CAAChB,IAAI,CAAC,CACxB,CAAE,MAAOI,KAAK,CAAE,CACd7C,OAAO,CAAC6C,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CAExD;AACA,GAAI,CAAAC,YAAY,CAAG,iCAAiC,CAEpD,GAAID,KAAK,CAACE,QAAQ,CAAE,CAClB;AACAD,YAAY,EAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE,CACzE,GAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,EAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,CAAE,CACrDL,YAAY,EAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE,CACpD,CACF,CAAC,IAAM,IAAIN,KAAK,CAACO,OAAO,CAAE,CACxB;AACAN,YAAY,EAAI,iEAAiE,CACnF,CAAC,IAAM,IAAID,KAAK,CAACQ,OAAO,CAAE,CACxB;AACAP,YAAY,EAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE,CACtC,CAEAvD,OAAO,CAACgD,YAAY,CAAC,CACvB,CAAC,OAAS,CACRhB,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAAC,CAED;AACA,KAAM,CAAAiC,uBAAuB,CAAIC,CAAC,EAAK,CACrC1C,cAAc,CAAC0C,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAChC,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAIH,CAAC,EAAK,CAC9B,KAAM,CAAEI,IAAI,CAAEF,KAAM,CAAC,CAAGF,CAAC,CAACC,MAAM,CAEhC;AACAhD,WAAW,CAAC,CACV,GAAGD,QAAQ,CACX,CAACoD,IAAI,EAAGF,KACV,CAAC,CAAC,CAEF;AACA,GAAItD,UAAU,GAAK,cAAc,CAAE,CACjC,KAAM,CAAAyD,gBAAgB,CAAG,CAAC,CAAC,CAC3B,GAAID,IAAI,GAAK,iBAAiB,CAAE,CAC9BC,gBAAgB,CAACC,YAAY,CAAG5B,UAAU,CAAC1B,QAAQ,CAAC0C,aAAa,EAAI,CAAC,CAAC,CACzE,CAEA,KAAM,CAAAa,MAAM,CAAGzF,aAAa,CAACsF,IAAI,CAAEF,KAAK,CAAEG,gBAAgB,CAAC,CAE3D;AACA7C,aAAa,CAACgD,IAAI,GAAK,CACrB,GAAGA,IAAI,CACP,CAACJ,IAAI,EAAG,CAACG,MAAM,CAACE,KAAK,CAAGF,MAAM,CAAClB,OAAO,CAAG,IAC3C,CAAC,CAAC,CAAC,CAEH;AACA3B,eAAe,CAAC8C,IAAI,GAAK,CACvB,GAAGA,IAAI,CACP,CAACJ,IAAI,EAAGG,MAAM,CAACG,OAAO,CAAGH,MAAM,CAAClB,OAAO,CAAG,IAC5C,CAAC,CAAC,CAAC,CACL,CACF,CAAC,CAED;AACA,KAAM,CAAAsB,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACFpE,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAIK,UAAU,GAAK,gBAAgB,CAAE,CACnC;AACA,GAAI7B,OAAO,CAACiC,QAAQ,CAACG,YAAY,CAAC,EAAIyD,KAAK,CAAClC,UAAU,CAAC1B,QAAQ,CAACG,YAAY,CAAC,CAAC,CAAE,CAC9EK,aAAa,CAAC,CAAEL,YAAY,CAAE,oCAAqC,CAAC,CAAC,CACrEZ,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA,GAAI,CACF,KAAM,CAAA3B,WAAW,CAACiG,iBAAiB,CACjClF,UAAU,CACVqB,QAAQ,CAACE,OAAO,CAChBwB,UAAU,CAAC1B,QAAQ,CAACG,YAAY,CAClC,CAAC,CACD;AACAtB,SAAS,CAAC,sCAAsC,CAAC,CACjD;AACA2D,iBAAiB,CAAC,CAAC,CACnB;AACAsB,UAAU,CAAC,IAAM,CACf9F,wBAAwB,CAACkB,QAAQ,CAAE,IAAI,CAAC,CAC1C,CAAC,CAAE,GAAG,CAAC,CACT,CAAE,MAAO2C,KAAK,CAAE,CACd7C,OAAO,CAAC6C,KAAK,CAAC,mDAAmD,CAAEA,KAAK,CAAC,CACzE/C,OAAO,CAAC,oDAAoD,EAAI+C,KAAK,CAACQ,OAAO,EAAI,oBAAoB,CAAC,CAAC,CACvG9C,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,IAAM,IAAIK,UAAU,GAAK,gBAAgB,CAAE,CAC1C,GAAI,CACF,KAAM,CAAAhC,WAAW,CAACmG,YAAY,CAC5BpF,UAAU,CACVqB,QAAQ,CAACE,OAAO,CAChBF,QAAQ,CAACI,SACX,CAAC,CACD;AACAvB,SAAS,CAAC,gCAAgC,CAAC,CAC3C;AACA2D,iBAAiB,CAAC,CAAC,CACnB;AACAsB,UAAU,CAAC,IAAM,CACf9F,wBAAwB,CAACkB,QAAQ,CAAE,IAAI,CAAC,CAC1C,CAAC,CAAE,GAAG,CAAC,CACT,CAAE,MAAO2C,KAAK,CAAE,CACd7C,OAAO,CAAC6C,KAAK,CAAC,+CAA+C,CAAEA,KAAK,CAAC,CACrE/C,OAAO,CAAC,gDAAgD,EAAI+C,KAAK,CAACQ,OAAO,EAAI,oBAAoB,CAAC,CAAC,CACnG9C,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,IAAM,IAAIK,UAAU,GAAK,cAAc,CAAE,CACxC;AACA,KAAM,CAAAoE,UAAU,CAAGnG,gBAAgB,CAACmC,QAAQ,CAAC,CAE7C,GAAI,CAACgE,UAAU,CAACC,OAAO,CAAE,CACvBzD,aAAa,CAACwD,UAAU,CAACE,MAAM,CAAC,CAChCxD,eAAe,CAACsD,UAAU,CAACG,QAAQ,CAAC,CACpC5E,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA;AACA,KAAM,CAAA6E,aAAa,CAAGJ,UAAU,CAACI,aAAa,CAE9C;AACA,KAAM,CAAAC,UAAU,CAAG,CAAE,GAAGD,aAAc,CAAC,CACvC,MAAO,CAAAC,UAAU,CAACjE,SAAS,CAAE;AAC7B,MAAO,CAAAiE,UAAU,CAAC1C,eAAe,CAAE;AACnC,MAAO,CAAA0C,UAAU,CAACC,sBAAsB,CAAE;AAC1C,MAAO,CAAAD,UAAU,CAACE,SAAS,CAAE;AAC7B,MAAO,CAAAF,UAAU,CAACzC,mBAAmB,CAAE;AAEvC;AACAyC,UAAU,CAACC,sBAAsB,CAAG,CAAC,CAErCtF,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAEoF,UAAU,CAAC,CAElD,GAAI,CACFrF,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAEoF,UAAU,CAAC,CACvE,KAAM,CAAAd,MAAM,CAAG,KAAM,CAAA3F,WAAW,CAAC4G,UAAU,CAAC7F,UAAU,CAAE0F,UAAU,CAACnE,OAAO,CAAEmE,UAAU,CAAC,CACvFrF,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAEsE,MAAM,CAAC,CAEnE;AACA1E,SAAS,CAAC,8BAA8B,CAAC,CACzC;AACA2D,iBAAiB,CAAC,CAAC,CACnB;AACAsB,UAAU,CAAC,IAAM,CACf9F,wBAAwB,CAACkB,QAAQ,CAAE,IAAI,CAAC,CAC1C,CAAC,CAAE,GAAG,CAAC,CACP,OACF,CAAE,MAAO2C,KAAK,CAAE,CACd7C,OAAO,CAAC6C,KAAK,CAAC,2CAA2C,CAAEA,KAAK,CAAC,CAEjE;AACA,GAAI,CAAAC,YAAY,CAAG,0CAA0C,CAE7D,GAAID,KAAK,CAACE,QAAQ,CAAE,CAClB;AACAD,YAAY,EAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE,CACzE,GAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,EAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,CAAE,CACrDL,YAAY,EAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE,CACpD,CACF,CAAC,IAAM,IAAIN,KAAK,CAACO,OAAO,CAAE,CACxB;AACAN,YAAY,EAAI,iEAAiE,CAEjF;AACA;AACA9C,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC,CACpF;AACAJ,SAAS,CAAC,yEAAyE,CAAC,CACpF;AACA2D,iBAAiB,CAAC,CAAC,CACnB;AACAsB,UAAU,CAAC,IAAM,CACf9F,wBAAwB,CAACkB,QAAQ,CAAE,IAAI,CAAC,CAC1C,CAAC,CAAE,GAAG,CAAC,CACP,OACF,CAAC,IAAM,IAAI2C,KAAK,CAACQ,OAAO,CAAE,CACxB;AACAP,YAAY,EAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE,CAEpC;AACA,GAAIR,KAAK,CAACQ,OAAO,CAACoC,QAAQ,CAAC,2CAA2C,CAAC,CAAE,CACvEzF,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC,CAC5EuD,iBAAiB,CAAC,CAAC,CACnB3D,SAAS,CAAC,yEAAyE,CAAC,CACpF;AACAb,wBAAwB,CAACkB,QAAQ,CAAE,IAAI,CAAC,CACxC,OACF,CACF,CAEAJ,OAAO,CAACgD,YAAY,CAAC,CACrBvC,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA;AACA,GAAImF,MAAM,CAACC,IAAI,CAACX,UAAU,CAACG,QAAQ,CAAC,CAACS,MAAM,CAAG,CAAC,CAAE,CAC/C,KAAM,CAAAC,eAAe,CAAGH,MAAM,CAACI,MAAM,CAACd,UAAU,CAACG,QAAQ,CAAC,CAACY,IAAI,CAAC,IAAI,CAAC,CACrE/F,OAAO,CAACgG,IAAI,CAAC,gCAAgC,CAAEH,eAAe,CAAC,CACjE,CACF,CAAC,IAAM,IAAIjF,UAAU,GAAK,aAAa,CAAE,CACvC;AACA,KAAM,CAAAqF,WAAW,CAAGnF,YAAY,CAAC8B,mBAAmB,GAAK,YAAY,EAAK9B,YAAY,CAAC6B,eAAe,EAAI7B,YAAY,CAAC6B,eAAe,CAAG,CAAE,CAE3I,GAAIsD,WAAW,CAAE,CACf;AACAjG,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAEa,YAAY,CAACI,OAAO,CAAC,CACzE,GAAI,CACF;AACA,KAAM,CAAAqD,MAAM,CAAG,KAAM,CAAA3F,WAAW,CAACsH,eAAe,CAACvG,UAAU,CAAEmB,YAAY,CAACI,OAAO,CAAC,CAClFlB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEsE,MAAM,CAAC,CACjDvE,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAEsE,MAAM,CAACe,sBAAsB,CAAC,CAClF;AACA9B,iBAAiB,CAAC,CAAC,CACnB3D,SAAS,CAAC,QAAQiB,YAAY,CAACI,OAAO,kCAAkC,CAAC,CACzE;AACAlC,wBAAwB,CAACkB,QAAQ,CAAE,GAAG,CAAC,CACzC,CAAE,MAAOiG,SAAS,CAAE,CAClBnG,OAAO,CAAC6C,KAAK,CAAC,kEAAkE,CAAEsD,SAAS,CAAC,CAC5F;AACA,KAAM,CAAA5B,MAAM,CAAG,KAAM,CAAA3F,WAAW,CAACwH,UAAU,CAACzG,UAAU,CAAEmB,YAAY,CAACI,OAAO,CAAE,OAAO,CAAC,CACtFlB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAEsE,MAAM,CAAC,CAChE;AACAf,iBAAiB,CAAC,CAAC,CACnB3D,SAAS,CAAC,QAAQiB,YAAY,CAACI,OAAO,kCAAkC,CAAC,CAC3E,CACF,CAAC,IAAM,CACL;AACAlB,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAE8B,UAAU,CAAC,CACvE,KAAM,CAAAwC,MAAM,CAAG,KAAM,CAAA3F,WAAW,CAACwH,UAAU,CAACzG,UAAU,CAAEmB,YAAY,CAACI,OAAO,CAAEa,UAAU,CAAC,CACzF/B,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAEsE,MAAM,CAAC,CACxD;AACAf,iBAAiB,CAAC,CAAC,CACnB3D,SAAS,CAAC,QAAQiB,YAAY,CAACI,OAAO,IAAIa,UAAU,GAAK,OAAO,CAAG,oBAAoB,CAAG,WAAW,eAAe,CAAC,CACvH,CACF,CAEA;AACA;AACF,CAAE,MAAOc,KAAK,CAAE,CACd7C,OAAO,CAAC6C,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CAErD;AACA,GAAI,CAAAC,YAAY,CAAG,oBAAoB,CAEvC,GAAID,KAAK,CAACM,MAAM,CAAE,CAChB;AACAL,YAAY,CAAGD,KAAK,CAACM,MAAM,CAC7B,CAAC,IAAM,IAAIN,KAAK,CAACQ,OAAO,CAAE,CACxB;AACAP,YAAY,CAAGD,KAAK,CAACQ,OAAO,CAC9B,CAAC,IAAM,IAAI,MAAO,CAAAR,KAAK,GAAK,QAAQ,CAAE,CACpC;AACAC,YAAY,CAAGD,KAAK,CACtB,CAEA/C,OAAO,CAAC,gCAAgC,CAAGgD,YAAY,CAAC,CAC1D,CAAC,OAAS,CACRvC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA8F,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIzF,UAAU,GAAK,cAAc,CAAE,CACjC,mBACEtB,KAAA,CAACzC,MAAM,EACLyJ,IAAI,CAAE5F,UAAW,CACjB6F,OAAO,CAAE/C,iBAAkB,CAC3BgD,QAAQ,CAAC,IAAI,CACbC,SAAS,MACTC,UAAU,CAAE,CACVC,EAAE,CAAE,CACFC,KAAK,CAAE,KAAK,CAAG;AACfC,SAAS,CAAE,MAAM,CAAE;AACnBC,SAAS,CAAE,MAAM,CACjBC,QAAQ,CAAE,MACZ,CACF,CAAE,CAAAC,QAAA,eAEF5H,IAAA,CAACtC,WAAW,EAAC6J,EAAE,CAAE,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAAD,QAAA,CAAC,qBAAmB,CAAa,CAAC,cAC7D5H,IAAA,CAACrC,aAAa,EAAC4J,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAC,CAAED,EAAE,CAAE,CAAE,CAAE,CAAAD,QAAA,cAClC5H,IAAA,CAACjD,GAAG,EAACwK,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,cACjB5H,IAAA,CAACH,QAAQ,EACPmI,IAAI,CAAC,KAAK,CACVzH,UAAU,CAAEA,UAAW,CACvB0H,QAAQ,CAAE,KAAO,CAAAjC,aAAa,EAAK,CACjC,GAAI,CACF,KAAM,CAAAxG,WAAW,CAAC0I,UAAU,CAAC3H,UAAU,CAAEyF,aAAa,CAAC,CACvD,MAAO,KAAI,CACb,CAAE,MAAOvC,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CACb,CACF,CAAE,CACFhD,SAAS,CAAGwD,OAAO,EAAK,CACtBxD,SAAS,CAACwD,OAAO,CAAC,CAClBG,iBAAiB,CAAC,CAAC,CACrB,CAAE,CACF1D,OAAO,CAAEA,OAAQ,CACjByH,QAAQ,CAAE,IAAK,CACfC,QAAQ,CAAEhE,iBAAkB,CAC7B,CAAC,CACC,CAAC,CACO,CAAC,EAEV,CAAC,CAEb,CAAC,IAAM,IAAI5C,UAAU,GAAK,gBAAgB,CAAE,CAC1C,mBACEtB,KAAA,CAACzC,MAAM,EAACyJ,IAAI,CAAE5F,UAAW,CAAC6F,OAAO,CAAE/C,iBAAkB,CAACgD,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAO,QAAA,eAC3E5H,IAAA,CAACtC,WAAW,EAAAkK,QAAA,CAAC,wBAAsB,CAAa,CAAC,cACjD5H,IAAA,CAACrC,aAAa,EAAAiK,QAAA,CACXnF,WAAW,cACVzC,IAAA,CAAC3B,gBAAgB,GAAE,CAAC,CAClBkE,IAAI,CAACiE,MAAM,GAAK,CAAC,cACnBxG,IAAA,CAAC5B,KAAK,EAACiK,QAAQ,CAAC,MAAM,CAAAT,QAAA,CAAC,yBAAuB,CAAO,CAAC,CACpD,CAAClG,YAAY,cACfxB,KAAA,CAACnD,GAAG,EAAA6K,QAAA,eACF5H,IAAA,CAAChD,UAAU,EAACsL,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAX,QAAA,CAAC,oBAE7C,CAAY,CAAC,cACb5H,IAAA,CAAC5C,IAAI,EAAAwK,QAAA,CACFrF,IAAI,CAACiG,GAAG,CAAEnF,IAAI,eACbrD,IAAA,CAAC3C,QAAQ,EACPoL,MAAM,MAENC,OAAO,CAAEA,CAAA,GAAMrE,gBAAgB,CAAChB,IAAI,CAAE,CAAAuE,QAAA,cAEtC5H,IAAA,CAAC1C,YAAY,EACXqL,OAAO,CAAEtF,IAAI,CAACvB,OAAQ,CACtB8G,SAAS,CAAE,GAAGvF,IAAI,CAACwF,SAAS,EAAI,KAAK,UAAUxF,IAAI,CAACyF,mBAAmB,EAAI,KAAK,OAAOzF,IAAI,CAAC0F,iBAAiB,EAAI,KAAK,EAAG,CAC1H,CAAC,EANG1F,IAAI,CAACvB,OAOF,CACX,CAAC,CACE,CAAC,EACJ,CAAC,cAEN5B,KAAA,CAACnD,GAAG,EAACwK,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACjB1H,KAAA,CAAClD,UAAU,EAACsL,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAX,QAAA,EAAC,oBACzB,CAAClG,YAAY,CAACI,OAAO,EAC7B,CAAC,cACb5B,KAAA,CAAClD,UAAU,EAACsL,OAAO,CAAC,OAAO,CAACC,YAAY,MAAAX,QAAA,EAAC,iBACxB,CAAClG,YAAY,CAAC4C,aAAa,EAAI,KAAK,EACzC,CAAC,cACbpE,KAAA,CAAClD,UAAU,EAACsL,OAAO,CAAC,OAAO,CAACC,YAAY,MAAAX,QAAA,EAAC,qBACpB,CAAClG,YAAY,CAAC6B,eAAe,EAAI,GAAG,EAC7C,CAAC,cACbvD,IAAA,CAAClC,SAAS,EACRkL,MAAM,CAAC,OAAO,CACdhE,IAAI,CAAC,cAAc,CACnBiE,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,QAAQ,CACb7B,SAAS,MACTiB,OAAO,CAAC,UAAU,CAClBxD,KAAK,CAAElD,QAAQ,CAACG,YAAa,CAC7BoH,QAAQ,CAAEpE,gBAAiB,CAC3BqE,QAAQ,MACR3F,KAAK,CAAE,CAAC,CAACtB,UAAU,CAACJ,YAAa,CACjCsH,UAAU,CAAElH,UAAU,CAACJ,YAAa,CACpCwF,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,EACC,CACN,CACY,CAAC,cAChB7H,KAAA,CAACrC,aAAa,EAAA+J,QAAA,eACZ5H,IAAA,CAAC/C,MAAM,EAACyL,OAAO,CAAEtE,iBAAkB,CAAAwD,QAAA,CAAC,SAAO,CAAQ,CAAC,CACnDlG,YAAY,eACX1B,IAAA,CAAC/C,MAAM,EACLyL,OAAO,CAAEnD,UAAW,CACpB+D,QAAQ,CAAEpI,OAAO,EAAI,CAACU,QAAQ,CAACG,YAAa,CAC5CwH,SAAS,CAAErI,OAAO,cAAGlB,IAAA,CAAC3B,gBAAgB,EAACmL,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGxJ,IAAA,CAACZ,QAAQ,GAAE,CAAE,CAAAwI,QAAA,CACpE,OAED,CAAQ,CACT,EACY,CAAC,EACV,CAAC,CAEb,CAAC,IAAM,IAAIpG,UAAU,GAAK,gBAAgB,CAAE,CAC1C,mBACEtB,KAAA,CAACzC,MAAM,EAACyJ,IAAI,CAAE5F,UAAW,CAAC6F,OAAO,CAAE/C,iBAAkB,CAACgD,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAO,QAAA,eAC3E5H,IAAA,CAACtC,WAAW,EAAAkK,QAAA,CAAC,6BAA2B,CAAa,CAAC,cACtD5H,IAAA,CAACrC,aAAa,EAAAiK,QAAA,CACXnF,WAAW,cACVzC,IAAA,CAAC3B,gBAAgB,GAAE,CAAC,CAClBkE,IAAI,CAACiE,MAAM,GAAK,CAAC,cACnBxG,IAAA,CAAC5B,KAAK,EAACiK,QAAQ,CAAC,MAAM,CAAAT,QAAA,CAAC,yBAAuB,CAAO,CAAC,CACpD,CAAClG,YAAY,cACfxB,KAAA,CAACnD,GAAG,EAAA6K,QAAA,eACF5H,IAAA,CAAChD,UAAU,EAACsL,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAX,QAAA,CAAC,oBAE7C,CAAY,CAAC,cACb5H,IAAA,CAAC5C,IAAI,EAAAwK,QAAA,CACFrF,IAAI,CAACiG,GAAG,CAAEnF,IAAI,eACbrD,IAAA,CAAC3C,QAAQ,EACPoL,MAAM,MAENC,OAAO,CAAEA,CAAA,GAAMrE,gBAAgB,CAAChB,IAAI,CAAE,CAAAuE,QAAA,cAEtC5H,IAAA,CAAC1C,YAAY,EACXqL,OAAO,CAAEtF,IAAI,CAACvB,OAAQ,CACtB8G,SAAS,CAAE,mBAAmBvF,IAAI,CAACrB,SAAS,CAAIqB,IAAI,CAACrB,SAAS,GAAK,cAAc,CAAG,cAAc,CAAGqB,IAAI,CAACrB,SAAS,CAAI,cAAc,EAAG,CACzI,CAAC,EANGqB,IAAI,CAACvB,OAOF,CACX,CAAC,CACE,CAAC,EACJ,CAAC,cAEN5B,KAAA,CAACnD,GAAG,EAACwK,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACjB1H,KAAA,CAAClD,UAAU,EAACsL,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAX,QAAA,EAAC,oBACzB,CAAClG,YAAY,CAACI,OAAO,EAC7B,CAAC,cACb5B,KAAA,CAAClD,UAAU,EAACsL,OAAO,CAAC,OAAO,CAACC,YAAY,MAAAX,QAAA,EAAC,kBACvB,CAAClG,YAAY,CAACM,SAAS,CAAIN,YAAY,CAACM,SAAS,GAAK,cAAc,CAAG,cAAc,CAAGN,YAAY,CAACM,SAAS,CAAI,cAAc,EACtI,CAAC,cACbhC,IAAA,CAAClC,SAAS,EACRkL,MAAM,CAAC,OAAO,CACdhE,IAAI,CAAC,WAAW,CAChBiE,KAAK,CAAC,WAAW,CACjB5B,SAAS,MACTiB,OAAO,CAAC,UAAU,CAClBxD,KAAK,CAAElD,QAAQ,CAACI,SAAU,CAC1BmH,QAAQ,CAAEpE,gBAAiB,CAC3BwC,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,EACC,CACN,CACY,CAAC,cAChB7H,KAAA,CAACrC,aAAa,EAAA+J,QAAA,eACZ5H,IAAA,CAAC/C,MAAM,EAACyL,OAAO,CAAEtE,iBAAkB,CAAAwD,QAAA,CAAC,SAAO,CAAQ,CAAC,CACnDlG,YAAY,eACX1B,IAAA,CAAC/C,MAAM,EACLyL,OAAO,CAAEnD,UAAW,CACpB+D,QAAQ,CAAEpI,OAAQ,CAClBqI,SAAS,CAAErI,OAAO,cAAGlB,IAAA,CAAC3B,gBAAgB,EAACmL,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGxJ,IAAA,CAACZ,QAAQ,GAAE,CAAE,CAAAwI,QAAA,CACpE,OAED,CAAQ,CACT,EACY,CAAC,EACV,CAAC,CAEb,CAAC,IAAM,IAAIpG,UAAU,GAAK,eAAe,CAAE,CACzC,mBACEtB,KAAA,CAACzC,MAAM,EAACyJ,IAAI,CAAE5F,UAAW,CAAC6F,OAAO,CAAE/C,iBAAkB,CAACgD,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAO,QAAA,eAC3E5H,IAAA,CAACtC,WAAW,EAAAkK,QAAA,CAAC,eAAa,CAAa,CAAC,cACxC1H,KAAA,CAACvC,aAAa,EAAAiK,QAAA,eACZ5H,IAAA,CAAC5B,KAAK,EAACiK,QAAQ,CAAC,MAAM,CAACd,EAAE,CAAE,CAAEkC,EAAE,CAAE,CAAE,CAAE,CAAA7B,QAAA,CAAC,2LAGtC,CAAO,CAAC,CAEPnF,WAAW,cACVzC,IAAA,CAAC3B,gBAAgB,GAAE,CAAC,CAClB,CAACqD,YAAY,cACfxB,KAAA,CAACnD,GAAG,EAACwK,EAAE,CAAE,CAAEmC,CAAC,CAAE,CAAE,CAAE,CAAA9B,QAAA,eAChB5H,IAAA,CAAChD,UAAU,EAACsL,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAX,QAAA,CAAC,wCAE7C,CAAY,CAAC,cACb1H,KAAA,CAACnD,GAAG,EAACwK,EAAE,CAAE,CAAEoC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAE7B,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACxD5H,IAAA,CAAClC,SAAS,EACRuJ,SAAS,MACT4B,KAAK,CAAC,SAAS,CACfX,OAAO,CAAC,UAAU,CAClBxD,KAAK,CAAE7C,WAAY,CACnBkH,QAAQ,CAAExE,uBAAwB,CAClCkF,WAAW,CAAC,yBAAyB,CACtC,CAAC,cACF7J,IAAA,CAAC/C,MAAM,EACLqL,OAAO,CAAC,WAAW,CACnBwB,KAAK,CAAC,SAAS,CACfpB,OAAO,CAAEnE,oBAAqB,CAC9B+E,QAAQ,CAAE7G,WAAW,EAAI,CAACR,WAAW,CAACuC,IAAI,CAAC,CAAE,CAC7C+C,EAAE,CAAE,CAAEwC,EAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAApC,QAAA,CAEhCnF,WAAW,cAAGzC,IAAA,CAAC3B,gBAAgB,EAACmL,IAAI,CAAE,EAAG,CAAE,CAAC,CAAG,OAAO,CACjD,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,EACK,CAAC,cAChBxJ,IAAA,CAACnC,aAAa,EAAA+J,QAAA,cACZ5H,IAAA,CAAC/C,MAAM,EAACyL,OAAO,CAAEtE,iBAAkB,CAAAwD,QAAA,CAAC,SAAO,CAAQ,CAAC,CACvC,CAAC,EACV,CAAC,CAEb,CAAC,IAAM,IAAIpG,UAAU,GAAK,aAAa,CAAE,CACvC;AACA,KAAM,CAAAqF,WAAW,CAAGnF,YAAY,GAAKA,YAAY,CAAC8B,mBAAmB,GAAK,YAAY,EAAK9B,YAAY,CAAC6B,eAAe,EAAI7B,YAAY,CAAC6B,eAAe,CAAG,CAAE,CAAC,CAE7J,mBACErD,KAAA,CAACzC,MAAM,EAACyJ,IAAI,CAAE5F,UAAW,CAAC6F,OAAO,CAAE/C,iBAAkB,CAACgD,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAO,QAAA,eAC3E5H,IAAA,CAACtC,WAAW,EAAAkK,QAAA,CACT,CAAClG,YAAY,CAAG,cAAc,CAC9BmF,WAAW,CAAG,uBAAuB,CAAG,cAAc,CAC5C,CAAC,cACd7G,IAAA,CAACrC,aAAa,EAAAiK,QAAA,CACXnF,WAAW,cACVzC,IAAA,CAAC3B,gBAAgB,GAAE,CAAC,CAClB,CAACqD,YAAY,cACfxB,KAAA,CAACnD,GAAG,EAACwK,EAAE,CAAE,CAAEmC,CAAC,CAAE,CAAE,CAAE,CAAA9B,QAAA,eAChB5H,IAAA,CAAChD,UAAU,EAACsL,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAX,QAAA,CAAC,uCAE7C,CAAY,CAAC,cACb1H,KAAA,CAACnD,GAAG,EAACwK,EAAE,CAAE,CAAEoC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAE7B,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACxD5H,IAAA,CAAClC,SAAS,EACRuJ,SAAS,MACT4B,KAAK,CAAC,SAAS,CACfX,OAAO,CAAC,UAAU,CAClBxD,KAAK,CAAE7C,WAAY,CACnBkH,QAAQ,CAAExE,uBAAwB,CAClCkF,WAAW,CAAC,yBAAyB,CACtC,CAAC,cACF7J,IAAA,CAAC/C,MAAM,EACLqL,OAAO,CAAC,WAAW,CACnBwB,KAAK,CAAC,SAAS,CACfpB,OAAO,CAAEnE,oBAAqB,CAC9B+E,QAAQ,CAAE7G,WAAW,EAAI,CAACR,WAAW,CAACuC,IAAI,CAAC,CAAE,CAC7C+C,EAAE,CAAE,CAAEwC,EAAE,CAAE,CAAC,CAAEC,QAAQ,CAAE,OAAQ,CAAE,CAAApC,QAAA,CAEhCnF,WAAW,cAAGzC,IAAA,CAAC3B,gBAAgB,EAACmL,IAAI,CAAE,EAAG,CAAE,CAAC,CAAG,OAAO,CACjD,CAAC,EACN,CAAC,EACH,CAAC,CACJhI,UAAU,GAAK,aAAa,EAAIqF,WAAW,cAC7C3G,KAAA,CAAAE,SAAA,EAAAwH,QAAA,eACE1H,KAAA,CAACtC,iBAAiB,EAAAgK,QAAA,EAAC,UACT,cAAA5H,IAAA,WAAA4H,QAAA,CAASlG,YAAY,CAACI,OAAO,CAAS,CAAC,6CAC/C,CAACJ,YAAY,CAAC6B,eAAe,CAAG,CAAC,eAC/BrD,KAAA,CAAAE,SAAA,EAAAwH,QAAA,EAAE,iBAAe,cAAA1H,KAAA,WAAA0H,QAAA,EAASlG,YAAY,CAAC6B,eAAe,CAAC,IAAE,EAAQ,CAAC,IAAC,EAAE,CACtE,EACgB,CAAC,cACpBvD,IAAA,CAACpC,iBAAiB,EAAC2J,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,CAAC,oFAElC,CAAmB,CAAC,EACpB,CAAC,CACDpG,UAAU,GAAK,aAAa,cAC9BtB,KAAA,CAAAE,SAAA,EAAAwH,QAAA,eACE1H,KAAA,CAACtC,iBAAiB,EAAAgK,QAAA,EAAC,6BACU,cAAA5H,IAAA,WAAA4H,QAAA,CAASlG,YAAY,CAACI,OAAO,CAAS,CAAC,IACpE,EAAmB,CAAC,cAEpB5B,KAAA,CAACnC,WAAW,EAACkM,SAAS,CAAC,UAAU,CAAC1C,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eAC9C5H,IAAA,CAACtB,SAAS,EAACuL,SAAS,CAAC,QAAQ,CAAArC,QAAA,CAAC,kCAAgC,CAAW,CAAC,cAC1E1H,KAAA,CAAC1B,UAAU,EACTsG,KAAK,CAAEnC,UAAW,CAClBwG,QAAQ,CAAGvE,CAAC,EAAKhC,aAAa,CAACgC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE,CAAA8C,QAAA,eAE/C5H,IAAA,CAACvB,gBAAgB,EACfqG,KAAK,CAAC,OAAO,CACboF,OAAO,cAAElK,IAAA,CAACzB,KAAK,GAAE,CAAE,CACnB0K,KAAK,CAAC,qFAAqF,CAC5F,CAAC,cACFjJ,IAAA,CAACvB,gBAAgB,EACfqG,KAAK,CAAC,QAAQ,CACdoF,OAAO,cAAElK,IAAA,CAACzB,KAAK,GAAE,CAAE,CACnB0K,KAAK,CAAC,sEAAsE,CAC7E,CAAC,EACQ,CAAC,EACF,CAAC,EACd,CAAC,CACD,IAAI,CACK,CAAC,cAChB/I,KAAA,CAACrC,aAAa,EAAA+J,QAAA,eACZ5H,IAAA,CAAC/C,MAAM,EAACyL,OAAO,CAAEtE,iBAAkB,CAAAwD,QAAA,CAAC,SAAO,CAAQ,CAAC,CACnDpG,UAAU,GAAK,aAAa,EAAIE,YAAY,eAC3C1B,IAAA,CAAC/C,MAAM,EACLyL,OAAO,CAAEnD,UAAW,CACpB+D,QAAQ,CAAEpI,OAAQ,CAClB4I,KAAK,CAAEjD,WAAW,CAAG,SAAS,CAAG,OAAQ,CACzC0C,SAAS,CAAErI,OAAO,cAAGlB,IAAA,CAAC3B,gBAAgB,EAACmL,IAAI,CAAE,EAAG,CAAE,CAAC,CAAG3C,WAAW,cAAG7G,IAAA,CAACV,WAAW,GAAE,CAAC,cAAGU,IAAA,CAAChB,UAAU,GAAE,CAAE,CAAA4I,QAAA,CAEpGf,WAAW,CAAG,kBAAkB,CAAIlE,UAAU,GAAK,OAAO,CAAG,kBAAkB,CAAG,yBAA0B,CACvG,CACT,EACY,CAAC,EACV,CAAC,CAEb,CAAC,IAAM,IAAInB,UAAU,GAAK,cAAc,CAAE,CACxC,mBACEtB,KAAA,CAACzC,MAAM,EACLyJ,IAAI,CAAE5F,UAAW,CACjB6F,OAAO,CAAE/C,iBAAkB,CAC3BgD,QAAQ,CAAC,IAAI,CACbC,SAAS,MACTC,UAAU,CAAE,CACVC,EAAE,CAAE,CACFC,KAAK,CAAE,KAAK,CAAG;AACfC,SAAS,CAAE,MAAM,CAAE;AACnBC,SAAS,CAAE,MAAM,CACjBC,QAAQ,CAAE,MACZ,CACF,CAAE,CAAAC,QAAA,eAEF5H,IAAA,CAACtC,WAAW,EAAC6J,EAAE,CAAE,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAAD,QAAA,CAAC,eAAa,CAAa,CAAC,cACvD5H,IAAA,CAACrC,aAAa,EAAC4J,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAC,CAAED,EAAE,CAAE,CAAE,CAAE,CAAAD,QAAA,cAClC5H,IAAA,CAACjD,GAAG,EAACwK,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,CAChBlG,YAAY,cACX1B,IAAA,CAACH,QAAQ,EACPmI,IAAI,CAAC,MAAM,CACXmC,WAAW,CAAEvI,QAAS,CACtBrB,UAAU,CAAEA,UAAW,CACvB0H,QAAQ,CAAE,KAAO,CAAAjC,aAAa,EAAK,CACjC,GAAI,CACF;AACA,KAAM,CAAAC,UAAU,CAAG,CAAE,GAAGD,aAAc,CAAC,CACvC,MAAO,CAAAC,UAAU,CAACjE,SAAS,CAAE;AAC7B,MAAO,CAAAiE,UAAU,CAAC1C,eAAe,CAAE;AACnC,MAAO,CAAA0C,UAAU,CAACC,sBAAsB,CAAE;AAC1C,MAAO,CAAAD,UAAU,CAACE,SAAS,CAAE;AAC7B,MAAO,CAAAF,UAAU,CAACzC,mBAAmB,CAAE;AAEvC;AACAyC,UAAU,CAACC,sBAAsB,CAAG,CAAC,CAErC,KAAM,CAAA1G,WAAW,CAAC4G,UAAU,CAAC7F,UAAU,CAAE0F,UAAU,CAACnE,OAAO,CAAEmE,UAAU,CAAC,CACxE,MAAO,KAAI,CACb,CAAE,MAAOxC,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CACb,CACF,CAAE,CACFhD,SAAS,CAAGwD,OAAO,EAAK,CACtBxD,SAAS,CAACwD,OAAO,CAAC,CAClBG,iBAAiB,CAAC,CAAC,CACrB,CAAE,CACF1D,OAAO,CAAEA,OAAQ,CACjByH,QAAQ,CAAE,IAAK,CACfC,QAAQ,CAAEhE,iBAAkB,CAC7B,CAAC,cAEFpE,IAAA,CAACF,iBAAiB,EAChBS,UAAU,CAAEA,UAAW,CACvBE,SAAS,CAAGwD,OAAO,EAAK,CACtBxD,SAAS,CAACwD,OAAO,CAAC,CAClBG,iBAAiB,CAAC,CAAC,CACrB,CAAE,CACF1D,OAAO,CAAEA,OAAQ,CACjByH,QAAQ,CAAE,IAAK,CACfC,QAAQ,CAAEhE,iBAAkB,CAC7B,CACF,CACE,CAAC,CACO,CAAC,EAEV,CAAC,CAEb,CAEA,MAAO,KAAI,CACb,CAAC,CAED,mBACElE,KAAA,CAACnD,GAAG,EAACwK,EAAE,CAAE,CAAEoC,OAAO,CAAE,MAAO,CAAE,CAAA/B,QAAA,eAE3B5H,IAAA,CAACjD,GAAG,EAACwK,EAAE,CAAE,CAAEC,KAAK,CAAE,OAAO,CAAE4C,EAAE,CAAE,CAAE,CAAE,CAAAxC,QAAA,cACjC1H,KAAA,CAAChD,KAAK,EAACqK,EAAE,CAAE,CAAEmC,CAAC,CAAE,CAAC,CAAED,EAAE,CAAE,CAAE,CAAE,CAAA7B,QAAA,eACzB5H,IAAA,CAAChD,UAAU,EAACsL,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAX,QAAA,CAAC,0BAEtC,CAAY,CAAC,cACb5H,IAAA,CAAC7C,OAAO,EAACoK,EAAE,CAAE,CAAEkC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC1BvJ,KAAA,CAAC9C,IAAI,EAAC6M,SAAS,CAAC,KAAK,CAACI,KAAK,MAAAzC,QAAA,eACzB1H,KAAA,CAAC1C,cAAc,EAACkL,OAAO,CAAEA,CAAA,GAAMxE,kBAAkB,CAAC,gBAAgB,CAAE,CAAA0D,QAAA,eAClE5H,IAAA,CAACzC,YAAY,EAAAqK,QAAA,cACX5H,IAAA,CAACd,SAAS,GAAE,CAAC,CACD,CAAC,cACfc,IAAA,CAAC1C,YAAY,EAACqL,OAAO,CAAC,2BAA2B,CAAE,CAAC,EACtC,CAAC,cAEjBzI,KAAA,CAAC1C,cAAc,EAACkL,OAAO,CAAEA,CAAA,GAAMxE,kBAAkB,CAAC,gBAAgB,CAAE,CAAA0D,QAAA,eAClE5H,IAAA,CAACzC,YAAY,EAAAqK,QAAA,cACX5H,IAAA,CAAClB,QAAQ,GAAE,CAAC,CACA,CAAC,cACfkB,IAAA,CAAC1C,YAAY,EAACqL,OAAO,CAAC,gCAAgC,CAAE,CAAC,EAC3C,CAAC,cAEjBzI,KAAA,CAAC1C,cAAc,EAACkL,OAAO,CAAEA,CAAA,GAAMxE,kBAAkB,CAAC,cAAc,CAAE,CAAA0D,QAAA,eAChE5H,IAAA,CAACzC,YAAY,EAAAqK,QAAA,cACX5H,IAAA,CAACpB,OAAO,GAAE,CAAC,CACC,CAAC,cACfoB,IAAA,CAAC1C,YAAY,EAACqL,OAAO,CAAC,wBAAwB,CAAE,CAAC,EACnC,CAAC,cAEjBzI,KAAA,CAAC1C,cAAc,EAACkL,OAAO,CAAEA,CAAA,GAAMxE,kBAAkB,CAAC,cAAc,CAAE,CAAA0D,QAAA,eAChE5H,IAAA,CAACzC,YAAY,EAAAqK,QAAA,cACX5H,IAAA,CAAClB,QAAQ,GAAE,CAAC,CACA,CAAC,cACfkB,IAAA,CAAC1C,YAAY,EAACqL,OAAO,CAAC,kBAAkB,CAAE,CAAC,EAC7B,CAAC,cAEjBzI,KAAA,CAAC1C,cAAc,EAACkL,OAAO,CAAEA,CAAA,GAAMxE,kBAAkB,CAAC,aAAa,CAAE,CAAA0D,QAAA,eAC/D5H,IAAA,CAACzC,YAAY,EAAAqK,QAAA,cACX5H,IAAA,CAAChB,UAAU,GAAE,CAAC,CACF,CAAC,cACfgB,IAAA,CAAC1C,YAAY,EAACqL,OAAO,CAAC,iBAAiB,CAAE,CAAC,EAC5B,CAAC,cAEjBzI,KAAA,CAAC1C,cAAc,EAACkL,OAAO,CAAEA,CAAA,GAAMxE,kBAAkB,CAAC,kBAAkB,CAAE,CAAA0D,QAAA,eACpE5H,IAAA,CAACzC,YAAY,EAAAqK,QAAA,cACX5H,IAAA,CAACd,SAAS,GAAE,CAAC,CACD,CAAC,cACfc,IAAA,CAAC1C,YAAY,EAACqL,OAAO,CAAC,sBAAsB,CAAE,CAAC,EACjC,CAAC,EACb,CAAC,EACF,CAAC,CACL,CAAC,cAGN3I,IAAA,CAACjD,GAAG,EAACwK,EAAE,CAAE,CAAE+C,QAAQ,CAAE,CAAE,CAAE,CAAA1C,QAAA,cACvB1H,KAAA,CAAChD,KAAK,EAACqK,EAAE,CAAE,CAAEmC,CAAC,CAAE,CAAC,CAAEjC,SAAS,CAAE,OAAO,CAAEkC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEW,cAAc,CAAE,QAAS,CAAE,CAAA3C,QAAA,EACtG,CAACxG,cAAc,eACdpB,IAAA,CAAChD,UAAU,EAACsL,OAAO,CAAC,OAAO,CAAAV,QAAA,CAAC,wDAE5B,CAAY,CACb,CACAxG,cAAc,EAAI,CAACE,UAAU,eAC5BpB,KAAA,CAACnD,GAAG,EAACwK,EAAE,CAAE,CAAEiD,SAAS,CAAE,QAAS,CAAE,CAAA5C,QAAA,eAC/B1H,KAAA,CAAClD,UAAU,EAACsL,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAX,QAAA,EAClCxG,cAAc,GAAK,gBAAgB,EAAI,wBAAwB,CAC/DA,cAAc,GAAK,cAAc,EAAI,eAAe,CACpDA,cAAc,GAAK,cAAc,EAAI,qBAAqB,CAC1DA,cAAc,GAAK,aAAa,EAAI,cAAc,CAClDA,cAAc,GAAK,gBAAgB,EAAI,6BAA6B,CACpEA,cAAc,GAAK,kBAAkB,EAAI,mBAAmB,EACnD,CAAC,cACbpB,IAAA,CAAChD,UAAU,EAACsL,OAAO,CAAC,OAAO,CAAAV,QAAA,CAAC,yBAE5B,CAAY,CAAC,cACb5H,IAAA,CAAC3B,gBAAgB,EAACkJ,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,EAChC,CACN,EACI,CAAC,CACL,CAAC,CAELd,YAAY,CAAC,CAAC,EACZ,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5G,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}