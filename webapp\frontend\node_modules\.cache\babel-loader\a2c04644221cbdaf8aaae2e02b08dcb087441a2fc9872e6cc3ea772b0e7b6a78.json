{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Box, Typography, Grid, Card, CardContent, CardActionArea, Avatar, CircularProgress } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport { AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const {\n    user,\n    isImpersonating\n  } = useAuth();\n  const navigate = useNavigate();\n\n  // Reindirizza automaticamente in base al tipo di utente\n  useEffect(() => {\n    // Breve timeout per evitare reindirizzamenti troppo rapidi\n    const redirectTimer = setTimeout(() => {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n        // Recupera l'ID del cantiere dal localStorage\n        const cantiereId = localStorage.getItem('selectedCantiereId');\n        if (cantiereId) {\n          navigate(`/dashboard/cantieri/${cantiereId}`);\n        } else {\n          navigate('/dashboard/cantieri');\n        }\n      }\n    }, 300);\n    return () => clearTimeout(redirectTimer);\n  }, [user, isImpersonating, navigate]);\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    navigate(path);\n  };\n\n  // Mostra un indicatore di caricamento durante il reindirizzamento\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minHeight: '50vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Benvenuto nel Sistema di Gestione Cantieri\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      paragraph: true,\n      children: \"Reindirizzamento in corso...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n      sx: {\n        mt: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mt: 2\n      },\n      children: [(user === null || user === void 0 ? void 0 : user.role) === 'owner' && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardActionArea, {\n            onClick: () => navigateTo('/dashboard/admin'),\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                p: 2,\n                bgcolor: '#f5f5f5',\n                height: 140\n              },\n              children: /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  width: 80,\n                  height: 80,\n                  bgcolor: 'primary.main',\n                  mt: 2\n                },\n                children: /*#__PURE__*/_jsxDEV(AdminIcon, {\n                  sx: {\n                    fontSize: 50\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                gutterBottom: true,\n                variant: \"h5\",\n                component: \"div\",\n                children: \"Amministrazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Gestisci utenti, visualizza il database e amministra il sistema.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 11\n      }, this), (user === null || user === void 0 ? void 0 : user.role) !== 'owner' && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardActionArea, {\n              onClick: () => navigateTo('/dashboard/cantieri'),\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'center',\n                  p: 2,\n                  bgcolor: '#f5f5f5',\n                  height: 140\n                },\n                children: /*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    width: 80,\n                    height: 80,\n                    bgcolor: 'secondary.main',\n                    mt: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(ConstructionIcon, {\n                    sx: {\n                      fontSize: 50\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 98,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  gutterBottom: true,\n                  variant: \"h5\",\n                  component: \"div\",\n                  children: \"I Miei Cantieri\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Visualizza e gestisci i tuoi cantieri.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardActionArea, {\n              onClick: () => navigateTo('/dashboard/cavi'),\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'center',\n                  p: 2,\n                  bgcolor: '#f5f5f5',\n                  height: 140\n                },\n                children: /*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    width: 80,\n                    height: 80,\n                    bgcolor: 'info.main',\n                    mt: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(CableIcon, {\n                    sx: {\n                      fontSize: 50\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  gutterBottom: true,\n                  variant: \"h5\",\n                  component: \"div\",\n                  children: \"Gestione Cavi\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Gestisci i cavi, le bobine e le certificazioni.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardActionArea, {\n              onClick: () => navigateTo('/dashboard/report'),\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'center',\n                  p: 2,\n                  bgcolor: '#f5f5f5',\n                  height: 140\n                },\n                children: /*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    width: 80,\n                    height: 80,\n                    bgcolor: 'success.main',\n                    mt: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(ReportIcon, {\n                    sx: {\n                      fontSize: 50\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  gutterBottom: true,\n                  variant: \"h5\",\n                  component: \"div\",\n                  children: \"Report\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Genera e visualizza report sui cantieri e sui cavi.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"/AY/SwGQ07WHXzlP57+ceNb+c+g=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useEffect", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActionArea", "Avatar", "CircularProgress", "useNavigate", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HomePage", "_s", "user", "isImpersonating", "navigate", "redirectTimer", "setTimeout", "role", "cantiereId", "localStorage", "getItem", "clearTimeout", "navigateTo", "path", "sx", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "paragraph", "mt", "container", "spacing", "item", "xs", "sm", "md", "onClick", "p", "bgcolor", "height", "width", "fontSize", "component", "color", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Box, Typography, Grid, Card, CardContent, CardActionArea, Avatar, CircularProgress } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\n\nconst HomePage = () => {\n  const { user, isImpersonating } = useAuth();\n  const navigate = useNavigate();\n\n  // Reindirizza automaticamente in base al tipo di utente\n  useEffect(() => {\n    // Breve timeout per evitare reindirizzamenti troppo rapidi\n    const redirectTimer = setTimeout(() => {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if (user?.role === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if (user?.role === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if (user?.role === 'cantieri_user') {\n        // Recupera l'ID del cantiere dal localStorage\n        const cantiereId = localStorage.getItem('selectedCantiereId');\n        if (cantiereId) {\n          navigate(`/dashboard/cantieri/${cantiereId}`);\n        } else {\n          navigate('/dashboard/cantieri');\n        }\n      }\n    }, 300);\n\n    return () => clearTimeout(redirectTimer);\n  }, [user, isImpersonating, navigate]);\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    navigate(path);\n  };\n\n  // Mostra un indicatore di caricamento durante il reindirizzamento\n  return (\n    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: '50vh' }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Benvenuto nel Sistema di Gestione Cantieri\n      </Typography>\n\n      <Typography variant=\"body1\" paragraph>\n        Reindirizzamento in corso...\n      </Typography>\n\n      <CircularProgress sx={{ mt: 3 }} />\n\n      <Grid container spacing={3} sx={{ mt: 2 }}>\n        {/* Card per Amministrazione (solo per admin) */}\n        {user?.role === 'owner' && (\n          <Grid item xs={12} sm={6} md={4}>\n            <Card>\n              <CardActionArea onClick={() => navigateTo('/dashboard/admin')}>\n                <Box sx={{ display: 'flex', justifyContent: 'center', p: 2, bgcolor: '#f5f5f5', height: 140 }}>\n                  <Avatar sx={{ width: 80, height: 80, bgcolor: 'primary.main', mt: 2 }}>\n                    <AdminIcon sx={{ fontSize: 50 }} />\n                  </Avatar>\n                </Box>\n                <CardContent>\n                  <Typography gutterBottom variant=\"h5\" component=\"div\">\n                    Amministrazione\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Gestisci utenti, visualizza il database e amministra il sistema.\n                  </Typography>\n                </CardContent>\n              </CardActionArea>\n            </Card>\n          </Grid>\n        )}\n\n        {/* Card per I Miei Cantieri, Gestione Cavi e Report (solo per utenti non admin) */}\n        {user?.role !== 'owner' && (\n          <>\n            {/* Card per I Miei Cantieri */}\n            <Grid item xs={12} sm={6} md={4}>\n              <Card>\n                <CardActionArea onClick={() => navigateTo('/dashboard/cantieri')}>\n                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 2, bgcolor: '#f5f5f5', height: 140 }}>\n                    <Avatar sx={{ width: 80, height: 80, bgcolor: 'secondary.main', mt: 2 }}>\n                      <ConstructionIcon sx={{ fontSize: 50 }} />\n                    </Avatar>\n                  </Box>\n                  <CardContent>\n                    <Typography gutterBottom variant=\"h5\" component=\"div\">\n                      I Miei Cantieri\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Visualizza e gestisci i tuoi cantieri.\n                    </Typography>\n                  </CardContent>\n                </CardActionArea>\n              </Card>\n            </Grid>\n\n            {/* Card per Gestione Cavi */}\n            <Grid item xs={12} sm={6} md={4}>\n              <Card>\n                <CardActionArea onClick={() => navigateTo('/dashboard/cavi')}>\n                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 2, bgcolor: '#f5f5f5', height: 140 }}>\n                    <Avatar sx={{ width: 80, height: 80, bgcolor: 'info.main', mt: 2 }}>\n                      <CableIcon sx={{ fontSize: 50 }} />\n                    </Avatar>\n                  </Box>\n                  <CardContent>\n                    <Typography gutterBottom variant=\"h5\" component=\"div\">\n                      Gestione Cavi\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Gestisci i cavi, le bobine e le certificazioni.\n                    </Typography>\n                  </CardContent>\n                </CardActionArea>\n              </Card>\n            </Grid>\n\n            {/* Card per Report */}\n            <Grid item xs={12} sm={6} md={4}>\n              <Card>\n                <CardActionArea onClick={() => navigateTo('/dashboard/report')}>\n                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 2, bgcolor: '#f5f5f5', height: 140 }}>\n                    <Avatar sx={{ width: 80, height: 80, bgcolor: 'success.main', mt: 2 }}>\n                      <ReportIcon sx={{ fontSize: 50 }} />\n                    </Avatar>\n                  </Box>\n                  <CardContent>\n                    <Typography gutterBottom variant=\"h5\" component=\"div\">\n                      Report\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Genera e visualizza report sui cantieri e sui cavi.\n                    </Typography>\n                  </CardContent>\n                </CardActionArea>\n              </Card>\n            </Grid>\n          </>\n        )}\n      </Grid>\n    </Box>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAEC,cAAc,EAAEC,MAAM,EAAEC,gBAAgB,QAAQ,eAAe;AAClH,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,QACpB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC3C,MAAMS,QAAQ,GAAGlB,WAAW,CAAC,CAAC;;EAE9B;EACAT,SAAS,CAAC,MAAM;IACd;IACA,MAAM4B,aAAa,GAAGC,UAAU,CAAC,MAAM;MACrC;MACA,IAAIH,eAAe,EAAE;QACnBC,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,IAAI,MAAK,OAAO,EAAE;QAC/BH,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,IAAI,MAAK,MAAM,EAAE;QAC9BH,QAAQ,CAAC,qBAAqB,CAAC;MACjC;MACA;MAAA,KACK,IAAI,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,IAAI,MAAK,eAAe,EAAE;QACvC;QACA,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QAC7D,IAAIF,UAAU,EAAE;UACdJ,QAAQ,CAAC,uBAAuBI,UAAU,EAAE,CAAC;QAC/C,CAAC,MAAM;UACLJ,QAAQ,CAAC,qBAAqB,CAAC;QACjC;MACF;IACF,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMO,YAAY,CAACN,aAAa,CAAC;EAC1C,CAAC,EAAE,CAACH,IAAI,EAAEC,eAAe,EAAEC,QAAQ,CAAC,CAAC;;EAErC;EACA,MAAMQ,UAAU,GAAIC,IAAI,IAAK;IAC3BT,QAAQ,CAACS,IAAI,CAAC;EAChB,CAAC;;EAED;EACA,oBACEhB,OAAA,CAACnB,GAAG;IAACoC,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,UAAU,EAAE,QAAQ;MAAEC,cAAc,EAAE,QAAQ;MAAEC,SAAS,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACvHvB,OAAA,CAAClB,UAAU;MAAC0C,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb7B,OAAA,CAAClB,UAAU;MAAC0C,OAAO,EAAC,OAAO;MAACM,SAAS;MAAAP,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb7B,OAAA,CAACZ,gBAAgB;MAAC6B,EAAE,EAAE;QAAEc,EAAE,EAAE;MAAE;IAAE;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEnC7B,OAAA,CAACjB,IAAI;MAACiD,SAAS;MAACC,OAAO,EAAE,CAAE;MAAChB,EAAE,EAAE;QAAEc,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,GAEvC,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,IAAI,MAAK,OAAO,iBACrBV,OAAA,CAACjB,IAAI;QAACmD,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAd,QAAA,eAC9BvB,OAAA,CAAChB,IAAI;UAAAuC,QAAA,eACHvB,OAAA,CAACd,cAAc;YAACoD,OAAO,EAAEA,CAAA,KAAMvB,UAAU,CAAC,kBAAkB,CAAE;YAAAQ,QAAA,gBAC5DvB,OAAA,CAACnB,GAAG;cAACoC,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEG,cAAc,EAAE,QAAQ;gBAAEkB,CAAC,EAAE,CAAC;gBAAEC,OAAO,EAAE,SAAS;gBAAEC,MAAM,EAAE;cAAI,CAAE;cAAAlB,QAAA,eAC5FvB,OAAA,CAACb,MAAM;gBAAC8B,EAAE,EAAE;kBAAEyB,KAAK,EAAE,EAAE;kBAAED,MAAM,EAAE,EAAE;kBAAED,OAAO,EAAE,cAAc;kBAAET,EAAE,EAAE;gBAAE,CAAE;gBAAAR,QAAA,eACpEvB,OAAA,CAACT,SAAS;kBAAC0B,EAAE,EAAE;oBAAE0B,QAAQ,EAAE;kBAAG;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN7B,OAAA,CAACf,WAAW;cAAAsC,QAAA,gBACVvB,OAAA,CAAClB,UAAU;gBAAC2C,YAAY;gBAACD,OAAO,EAAC,IAAI;gBAACoB,SAAS,EAAC,KAAK;gBAAArB,QAAA,EAAC;cAEtD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb7B,OAAA,CAAClB,UAAU;gBAAC0C,OAAO,EAAC,OAAO;gBAACqB,KAAK,EAAC,gBAAgB;gBAAAtB,QAAA,EAAC;cAEnD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP,EAGA,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,IAAI,MAAK,OAAO,iBACrBV,OAAA,CAAAE,SAAA;QAAAqB,QAAA,gBAEEvB,OAAA,CAACjB,IAAI;UAACmD,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAd,QAAA,eAC9BvB,OAAA,CAAChB,IAAI;YAAAuC,QAAA,eACHvB,OAAA,CAACd,cAAc;cAACoD,OAAO,EAAEA,CAAA,KAAMvB,UAAU,CAAC,qBAAqB,CAAE;cAAAQ,QAAA,gBAC/DvB,OAAA,CAACnB,GAAG;gBAACoC,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEG,cAAc,EAAE,QAAQ;kBAAEkB,CAAC,EAAE,CAAC;kBAAEC,OAAO,EAAE,SAAS;kBAAEC,MAAM,EAAE;gBAAI,CAAE;gBAAAlB,QAAA,eAC5FvB,OAAA,CAACb,MAAM;kBAAC8B,EAAE,EAAE;oBAAEyB,KAAK,EAAE,EAAE;oBAAED,MAAM,EAAE,EAAE;oBAAED,OAAO,EAAE,gBAAgB;oBAAET,EAAE,EAAE;kBAAE,CAAE;kBAAAR,QAAA,eACtEvB,OAAA,CAACP,gBAAgB;oBAACwB,EAAE,EAAE;sBAAE0B,QAAQ,EAAE;oBAAG;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN7B,OAAA,CAACf,WAAW;gBAAAsC,QAAA,gBACVvB,OAAA,CAAClB,UAAU;kBAAC2C,YAAY;kBAACD,OAAO,EAAC,IAAI;kBAACoB,SAAS,EAAC,KAAK;kBAAArB,QAAA,EAAC;gBAEtD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7B,OAAA,CAAClB,UAAU;kBAAC0C,OAAO,EAAC,OAAO;kBAACqB,KAAK,EAAC,gBAAgB;kBAAAtB,QAAA,EAAC;gBAEnD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP7B,OAAA,CAACjB,IAAI;UAACmD,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAd,QAAA,eAC9BvB,OAAA,CAAChB,IAAI;YAAAuC,QAAA,eACHvB,OAAA,CAACd,cAAc;cAACoD,OAAO,EAAEA,CAAA,KAAMvB,UAAU,CAAC,iBAAiB,CAAE;cAAAQ,QAAA,gBAC3DvB,OAAA,CAACnB,GAAG;gBAACoC,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEG,cAAc,EAAE,QAAQ;kBAAEkB,CAAC,EAAE,CAAC;kBAAEC,OAAO,EAAE,SAAS;kBAAEC,MAAM,EAAE;gBAAI,CAAE;gBAAAlB,QAAA,eAC5FvB,OAAA,CAACb,MAAM;kBAAC8B,EAAE,EAAE;oBAAEyB,KAAK,EAAE,EAAE;oBAAED,MAAM,EAAE,EAAE;oBAAED,OAAO,EAAE,WAAW;oBAAET,EAAE,EAAE;kBAAE,CAAE;kBAAAR,QAAA,eACjEvB,OAAA,CAACL,SAAS;oBAACsB,EAAE,EAAE;sBAAE0B,QAAQ,EAAE;oBAAG;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN7B,OAAA,CAACf,WAAW;gBAAAsC,QAAA,gBACVvB,OAAA,CAAClB,UAAU;kBAAC2C,YAAY;kBAACD,OAAO,EAAC,IAAI;kBAACoB,SAAS,EAAC,KAAK;kBAAArB,QAAA,EAAC;gBAEtD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7B,OAAA,CAAClB,UAAU;kBAAC0C,OAAO,EAAC,OAAO;kBAACqB,KAAK,EAAC,gBAAgB;kBAAAtB,QAAA,EAAC;gBAEnD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP7B,OAAA,CAACjB,IAAI;UAACmD,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAd,QAAA,eAC9BvB,OAAA,CAAChB,IAAI;YAAAuC,QAAA,eACHvB,OAAA,CAACd,cAAc;cAACoD,OAAO,EAAEA,CAAA,KAAMvB,UAAU,CAAC,mBAAmB,CAAE;cAAAQ,QAAA,gBAC7DvB,OAAA,CAACnB,GAAG;gBAACoC,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEG,cAAc,EAAE,QAAQ;kBAAEkB,CAAC,EAAE,CAAC;kBAAEC,OAAO,EAAE,SAAS;kBAAEC,MAAM,EAAE;gBAAI,CAAE;gBAAAlB,QAAA,eAC5FvB,OAAA,CAACb,MAAM;kBAAC8B,EAAE,EAAE;oBAAEyB,KAAK,EAAE,EAAE;oBAAED,MAAM,EAAE,EAAE;oBAAED,OAAO,EAAE,cAAc;oBAAET,EAAE,EAAE;kBAAE,CAAE;kBAAAR,QAAA,eACpEvB,OAAA,CAACH,UAAU;oBAACoB,EAAE,EAAE;sBAAE0B,QAAQ,EAAE;oBAAG;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN7B,OAAA,CAACf,WAAW;gBAAAsC,QAAA,gBACVvB,OAAA,CAAClB,UAAU;kBAAC2C,YAAY;kBAACD,OAAO,EAAC,IAAI;kBAACoB,SAAS,EAAC,KAAK;kBAAArB,QAAA,EAAC;gBAEtD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7B,OAAA,CAAClB,UAAU;kBAAC0C,OAAO,EAAC,OAAO;kBAACqB,KAAK,EAAC,gBAAgB;kBAAAtB,QAAA,EAAC;gBAEnD;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,eACP,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACzB,EAAA,CAnJID,QAAQ;EAAA,QACsBL,OAAO,EACxBT,WAAW;AAAA;AAAAyD,EAAA,GAFxB3C,QAAQ;AAqJd,eAAeA,QAAQ;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}