{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Chip, CircularProgress, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions, Snackbar, FormControl, InputLabel, Select, MenuItem } from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport { Cable as CableIcon, CheckCircle as CheckCircleIcon, Schedule as ScheduleIcon, Link as LinkIcon, LinkOff as LinkOffIcon, Timeline as TimelineIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport './CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  var _caviAttivi$, _caviAttivi$2, _caviAttivi$3;\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const {\n    openEliminaCavoDialog,\n    setOpenEliminaCavoDialog,\n    openModificaCavoDialog,\n    setOpenModificaCavoDialog,\n    openAggiungiCavoDialog,\n    setOpenAggiungiCavoDialog\n  } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stati per statistiche avanzate\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviInstallati: 0,\n    caviDaInstallare: 0,\n    caviInCorso: 0,\n    caviCollegati: 0,\n    caviNonCollegati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriRimanenti: 0\n  });\n\n  // Stato per la gestione delle revisioni\n  const [revisioniDisponibili, setRevisioniDisponibili] = useState([]);\n  const [revisioneSelezionata, setRevisioneSelezionata] = useState('');\n  const [revisioneCorrente, setRevisioneCorrente] = useState('');\n\n  // Rimosso stato per il debug\n\n  // Funzione per calcolare le statistiche avanzate\n  const calculateStatistics = (caviAttiviData, caviSpareData) => {\n    const tuttiCavi = [...(caviAttiviData || []), ...(caviSpareData || [])];\n    if (tuttiCavi.length === 0) {\n      console.log('Nessun cavo disponibile per il calcolo delle statistiche');\n      return;\n    }\n    console.log('Calcolo statistiche con dati:', {\n      caviAttivi: (caviAttiviData === null || caviAttiviData === void 0 ? void 0 : caviAttiviData.length) || 0,\n      caviSpare: (caviSpareData === null || caviSpareData === void 0 ? void 0 : caviSpareData.length) || 0,\n      totale: tuttiCavi.length\n    });\n    const totaleCavi = tuttiCavi.length;\n\n    // Calcola stati di installazione\n    const caviInstallati = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO').length;\n    const caviDaInstallare = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Da installare' || cavo.stato_installazione === 'DA_INSTALLARE').length;\n    const caviInCorso = tuttiCavi.filter(cavo => cavo.stato_installazione === 'In corso' || cavo.stato_installazione === 'IN_CORSO').length;\n\n    // Calcola stati di collegamento\n    const caviCollegati = tuttiCavi.filter(cavo => cavo.collegamenti === 3 && cavo.responsabile_partenza && cavo.responsabile_arrivo).length;\n    const caviNonCollegati = totaleCavi - caviCollegati;\n\n    // Calcola percentuali\n    const percentualeInstallazione = totaleCavi > 0 ? Math.round(caviInstallati / totaleCavi * 100) : 0;\n    const percentualeCollegamento = totaleCavi > 0 ? Math.round(caviCollegati / totaleCavi * 100) : 0;\n\n    // Calcola metri\n    const metriTotali = tuttiCavi.reduce((sum, cavo) => sum + (parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriInstallati = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO').reduce((sum, cavo) => sum + (parseFloat(cavo.metratura_reale) || parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriRimanenti = metriTotali - metriInstallati;\n    const newStatistics = {\n      totaleCavi,\n      caviInstallati,\n      caviDaInstallare,\n      caviInCorso,\n      caviCollegati,\n      caviNonCollegati,\n      percentualeInstallazione,\n      percentualeCollegamento,\n      metriTotali: Math.round(metriTotali),\n      metriInstallati: Math.round(metriInstallati),\n      metriRimanenti: Math.round(metriRimanenti)\n    };\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  };\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Funzione per caricare le revisioni disponibili\n  const loadRevisioni = async cantiereIdToUse => {\n    try {\n      console.log('Caricamento revisioni per cantiere:', cantiereIdToUse);\n\n      // Carica la revisione corrente\n      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);\n      console.log('Revisione corrente:', revisioneCorrenteData);\n      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);\n\n      // Carica tutte le revisioni disponibili\n      const revisioniData = await caviService.getRevisioniDisponibili(cantiereIdToUse);\n      console.log('Revisioni disponibili:', revisioniData);\n      setRevisioniDisponibili(revisioniData.revisioni || []);\n\n      // LOGICA REVISIONI: La revisione corrente è quella di default\n      // Non impostiamo una revisione selezionata, così il sistema usa automaticamente la corrente\n      console.log('Logica revisioni: usando revisione corrente di default');\n    } catch (error) {\n      console.error('Errore nel caricamento delle revisioni:', error);\n    }\n  };\n\n  // Funzione per gestire il cambio di revisione\n  const handleRevisioneChange = event => {\n    const nuovaRevisione = event.target.value;\n\n    // LOGICA REVISIONI:\n    // - Se vuoto o \"corrente\" -> usa revisione corrente (non specificare parametro)\n    // - Se specifica -> usa quella revisione per visualizzazione storica\n    if (nuovaRevisione === '' || nuovaRevisione === 'corrente') {\n      setRevisioneSelezionata('');\n      console.log('Passaggio a revisione corrente (default)');\n    } else {\n      setRevisioneSelezionata(nuovaRevisione);\n      console.log('Passaggio a revisione storica:', nuovaRevisione);\n    }\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le revisioni disponibili\n        await loadRevisioni(cantiereIdNum);\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi attivi\n          calculateStatistics(attivi || [], caviSpare);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi spare\n          calculateStatistics(caviAttivi, spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = cavo => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({\n      open: true,\n      message,\n      severity\n    });\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Dashboard con statistiche avanzate simile a quello della certificazione\n  const renderDashboard = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3,\n      bgcolor: 'grey.50'\n    },\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 6,\n        sm: 3,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: 'primary.main',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n            sx: {\n              fontSize: 32,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 0.5\n            },\n            children: statistics.totaleCavi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Totale Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 6,\n        sm: 3,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: 'success.main',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            sx: {\n              fontSize: 32,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 0.5\n            },\n            children: statistics.caviInstallati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Installati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 6,\n        sm: 3,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: 'warning.main',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ScheduleIcon, {\n            sx: {\n              fontSize: 32,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 0.5\n            },\n            children: statistics.caviDaInstallare + statistics.caviInCorso\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Da Installare\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 6,\n        sm: 3,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: statistics.percentualeInstallazione >= 80 ? 'success.main' : statistics.percentualeInstallazione >= 50 ? 'warning.main' : 'error.main',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n            sx: {\n              fontSize: 32,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 0.5\n            },\n            children: [statistics.percentualeInstallazione, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Completamento\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 636,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 6,\n        sm: 3,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: 'info.main',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(LinkIcon, {\n            sx: {\n              fontSize: 32,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 0.5\n            },\n            children: statistics.caviCollegati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Collegati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 655,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 654,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 6,\n        sm: 3,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: 'grey.600',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(LinkOffIcon, {\n            sx: {\n              fontSize: 32,\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 0.5\n            },\n            children: statistics.caviNonCollegati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Non Collegati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 664,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 6,\n        sm: 3,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: 'secondary.main',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 0.5\n            },\n            children: [statistics.metriInstallati, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Metri Installati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 674,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 6,\n        sm: 3,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 2,\n            bgcolor: 'grey.400',\n            borderRadius: 1,\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 0.5\n            },\n            children: [statistics.metriRimanenti, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Metri Rimanenti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 683,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 604,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 603,\n    columnNumber: 5\n  }, this);\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: handleCloseDetails,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Cavo: \", selectedCavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 705,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sistema:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 713,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utility:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utility || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Colore:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 716,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.colore_cavo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 716,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Formazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 718,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 735,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 737,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 738,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metratura Reale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 746,\n                  columnNumber: 45\n                }, this), \" \", normalizeInstallationStatus(selectedCavo.stato_installazione)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Collegamenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.collegamenti || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 747,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.id_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ultimo Aggiornamento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 751,\n                  columnNumber: 45\n                }, this), \" \", new Date(selectedCavo.timestamp).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 732,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 708,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDetails,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 757,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 756,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 704,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 771,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 772,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 773,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 770,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 787,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 784,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 794,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 783,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [revisioniDisponibili.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Visualizzazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 810,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            size: \"small\",\n            sx: {\n              minWidth: 250\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Revisione da Visualizzare\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: revisioneSelezionata || 'corrente',\n              onChange: handleRevisioneChange,\n              label: \"Revisione da Visualizzare\",\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"corrente\",\n                children: [\"\\uD83D\\uDCCB Revisione Corrente \", revisioneCorrente && `(${revisioneCorrente})`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 818,\n                columnNumber: 21\n              }, this), revisioniDisponibili.map(rev => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: rev.revisione,\n                children: [\"\\uD83D\\uDCDA \", rev.revisione, \" (\", rev.cavi_count, \" cavi)\", rev.revisione === revisioneCorrente && ' - Attuale']\n              }, rev.revisione, true, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 23\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 811,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: revisioneSelezionata ? `Storico: ${revisioneSelezionata}` : `Corrente: ${revisioneCorrente || 'N/A'}`,\n            color: revisioneSelezionata ? \"secondary\" : \"primary\",\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 808,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 845,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [process.env.NODE_ENV === 'development' && caviAttivi.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2,\n            p: 1,\n            bgcolor: '#f0f0f0',\n            borderRadius: 1,\n            fontSize: '0.8rem',\n            fontFamily: 'monospace',\n            display: 'none'\n          },\n          children: Object.keys(caviAttivi[0]).map(key => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [key, \": \", JSON.stringify(caviAttivi[0][key])]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 851,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviAttivi,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi attivi filtrati:', filteredData.length),\n          revisioneCorrente: ((_caviAttivi$ = caviAttivi[0]) === null || _caviAttivi$ === void 0 ? void 0 : _caviAttivi$.revisione_ufficiale) || ((_caviAttivi$2 = caviAttivi[0]) === null || _caviAttivi$2 === void 0 ? void 0 : _caviAttivi$2.revisione) || ((_caviAttivi$3 = caviAttivi[0]) === null || _caviAttivi$3 === void 0 ? void 0 : _caviAttivi$3.rev)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 857,\n          columnNumber: 13\n        }, this), caviAttivi.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Nessun cavo attivo trovato. I cavi attivi appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 848,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Spare \", caviSpare.length > 0 ? `(${caviSpare.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 873,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 872,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviSpare,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi spare filtrati:', filteredData.length)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 13\n        }, this), caviSpare.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 883,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 871,\n        columnNumber: 11\n      }, this), renderDetailsDialog(), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openEliminaCavoDialog,\n        onClose: () => setOpenEliminaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"md\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio di successo con Snackbar\n              showNotification(message, 'success');\n              // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                console.log('Ricaricamento dati dopo operazione...');\n                try {\n                  // Ricarica i dati invece di ricaricare la pagina\n                  fetchCavi(true);\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante l\\'eliminazione del cavo:', message);\n            // Mostra un alert all'utente\n            alert(`Errore: ${message}`);\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            fetchCavi();\n          },\n          initialOption: \"eliminaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 901,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 895,\n        columnNumber: 11\n      }, this), openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openModificaCavoDialog,\n        onClose: () => setOpenModificaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"sm\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenModificaCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio di successo con Snackbar\n              showNotification(message, 'success');\n              // Ricarica i dati immediatamente\n              console.log('Ricaricamento dati dopo operazione...');\n              // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                try {\n                  fetchCavi(true);\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante la modifica del cavo:', message);\n            // Mostra un alert all'utente\n            alert(`Errore: ${message}`);\n            // Chiudi il dialogo\n            setOpenModificaCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            console.log('Ricaricamento dati dopo errore...');\n            fetchCavi(true);\n          },\n          initialOption: \"modificaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 954,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 948,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openAggiungiCavoDialog,\n        onClose: () => setOpenAggiungiCavoDialog(false),\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenAggiungiCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio di successo con Snackbar\n              showNotification(message, 'success');\n              // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                console.log('Ricaricamento dati dopo operazione...');\n                try {\n                  // Ricarica i dati in modalità silenziosa per evitare il \"blink\" della pagina\n                  fetchCavi(true);\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina immediatamente\n                  console.log('Tentativo di ricaricamento della pagina...');\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante l\\'aggiunta del cavo:', message);\n            // Mostra un messaggio di errore con Snackbar\n            showNotification(`Errore: ${message}`, 'error');\n            // Chiudi il dialogo\n            setOpenAggiungiCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            fetchCavi(true);\n          },\n          initialOption: \"aggiungiCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1000,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 999,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: notification.open,\n        autoHideDuration: 4000,\n        onClose: handleCloseNotification,\n        anchorOrigin: {\n          vertical: 'bottom',\n          horizontal: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseNotification,\n          severity: notification.severity,\n          sx: {\n            width: '100%'\n          },\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1051,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1045,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 805,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 768,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"oGVFQcaWlDv9J2vAxLyiTXbq98w=\", false, function () {\n  return [useAuth, useGlobalContext, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Chip", "CircularProgress", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Snackbar", "FormControl", "InputLabel", "Select", "MenuItem", "InfoIcon", "Cable", "CableIcon", "CheckCircle", "CheckCircleIcon", "Schedule", "ScheduleIcon", "Link", "LinkIcon", "<PERSON><PERSON><PERSON>", "LinkOffIcon", "Timeline", "TimelineIcon", "useNavigate", "useAuth", "useGlobalContext", "PosaCaviCollegamenti", "caviService", "CavoForm", "normalizeInstallationStatus", "CaviFilterableTable", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "_caviAttivi$", "_caviAttivi$2", "_caviAttivi$3", "isImpersonating", "user", "openEliminaCavoDialog", "setOpenEliminaCavoDialog", "openModificaCavoDialog", "setOpenModificaCavoDialog", "openAggiungiCavoDialog", "setOpenAggiungiCavoDialog", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "notification", "setNotification", "open", "message", "severity", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "detailsDialogOpen", "setDetailsDialogOpen", "statistics", "setStatistics", "totaleCavi", "caviInstallati", "caviDaInstallare", "caviInCorso", "caviCollegati", "caviNonCollegati", "percentualeInstallazione", "percentualeCollegamento", "metriTotali", "metriInstallati", "metriR<PERSON><PERSON><PERSON>", "revisioniDisponibili", "setRevisioniDisponibili", "revisioneSelezionata", "setRevisioneSelezionata", "revisioneCorrente", "setRevisioneCorrente", "calculateStatistics", "caviAttiviData", "caviSpareData", "tuttiCavi", "length", "console", "log", "totale", "filter", "cavo", "stato_installazione", "colle<PERSON>nti", "responsabile_partenza", "responsabile_arrivo", "Math", "round", "reduce", "sum", "parseFloat", "metri_te<PERSON>ci", "metratura_reale", "newStatistics", "loadStatiInstallazione", "setStatiInstallazione", "loadRevisioni", "cantiereIdToUse", "revisioneCorrenteData", "getRevisioneCorrente", "revisione_corrente", "revisioniData", "getRevisioniDisponibili", "revisioni", "handleRevisioneChange", "event", "nuovaRevisione", "target", "value", "filters", "setFilters", "tipologia", "sort_by", "sort_order", "statiInstallazione", "tipologieCavi", "setTipologieCavi", "<PERSON><PERSON><PERSON>", "silentLoading", "localStorage", "getItem", "attivi", "get<PERSON><PERSON>", "attiviError", "caviSpareTra<PERSON>ttivi", "modificato_manualmente", "spare", "getCaviSpare", "spareError", "standardError", "setTimeout", "document", "body", "textContent", "includes", "window", "location", "reload", "fetchData", "token", "selectedCantiereId", "selectedCantiereName", "i", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "timeoutPromise", "Promise", "_", "reject", "Error", "caviPromise", "race", "caviError", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "detail", "handleOpenDetails", "handleCloseDetails", "handleCloseNotification", "prev", "showNotification", "renderDashboard", "sx", "p", "mb", "bgcolor", "children", "container", "spacing", "item", "xs", "sm", "textAlign", "borderRadius", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "renderDetailsDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "id_cavo", "dividers", "md", "gutterBottom", "sistema", "utility", "colore_cavo", "sezione", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "comanda_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "comanda_arrivo", "id_bobina", "responsabile_posa", "comanda_posa", "Date", "timestamp", "toLocaleString", "onClick", "className", "display", "flexDirection", "alignItems", "mt", "size", "gap", "min<PERSON><PERSON><PERSON>", "onChange", "label", "rev", "revisione", "cavi_count", "process", "env", "NODE_ENV", "fontFamily", "Object", "keys", "stringify", "cavi", "onFilteredDataChange", "filteredData", "revisione_ufficiale", "justifyContent", "onSuccess", "onError", "alert", "initialOption", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Chip,\n  CircularProgress,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Snackbar,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem\n} from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport {\n  Cable as CableIcon,\n  CheckCircle as CheckCircleIcon,\n  Schedule as ScheduleIcon,\n  Link as LinkIcon,\n  LinkOff as LinkOffIcon,\n  Timeline as TimelineIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog, openAggiungiCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stati per statistiche avanzate\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviInstallati: 0,\n    caviDaInstallare: 0,\n    caviInCorso: 0,\n    caviCollegati: 0,\n    caviNonCollegati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriRimanenti: 0\n  });\n\n\n\n  // Stato per la gestione delle revisioni\n  const [revisioniDisponibili, setRevisioniDisponibili] = useState([]);\n  const [revisioneSelezionata, setRevisioneSelezionata] = useState('');\n  const [revisioneCorrente, setRevisioneCorrente] = useState('');\n\n  // Rimosso stato per il debug\n\n  // Funzione per calcolare le statistiche avanzate\n  const calculateStatistics = (caviAttiviData, caviSpareData) => {\n    const tuttiCavi = [...(caviAttiviData || []), ...(caviSpareData || [])];\n\n    if (tuttiCavi.length === 0) {\n      console.log('Nessun cavo disponibile per il calcolo delle statistiche');\n      return;\n    }\n\n    console.log('Calcolo statistiche con dati:', {\n      caviAttivi: caviAttiviData?.length || 0,\n      caviSpare: caviSpareData?.length || 0,\n      totale: tuttiCavi.length\n    });\n\n    const totaleCavi = tuttiCavi.length;\n\n    // Calcola stati di installazione\n    const caviInstallati = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'Installato' ||\n      cavo.stato_installazione === 'INSTALLATO' ||\n      cavo.stato_installazione === 'POSATO'\n    ).length;\n\n    const caviDaInstallare = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'Da installare' ||\n      cavo.stato_installazione === 'DA_INSTALLARE'\n    ).length;\n\n    const caviInCorso = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'In corso' ||\n      cavo.stato_installazione === 'IN_CORSO'\n    ).length;\n\n    // Calcola stati di collegamento\n    const caviCollegati = tuttiCavi.filter(cavo =>\n      cavo.collegamenti === 3 &&\n      cavo.responsabile_partenza &&\n      cavo.responsabile_arrivo\n    ).length;\n\n    const caviNonCollegati = totaleCavi - caviCollegati;\n\n    // Calcola percentuali\n    const percentualeInstallazione = totaleCavi > 0 ? Math.round((caviInstallati / totaleCavi) * 100) : 0;\n    const percentualeCollegamento = totaleCavi > 0 ? Math.round((caviCollegati / totaleCavi) * 100) : 0;\n\n    // Calcola metri\n    const metriTotali = tuttiCavi.reduce((sum, cavo) => sum + (parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriInstallati = tuttiCavi\n      .filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO')\n      .reduce((sum, cavo) => sum + (parseFloat(cavo.metratura_reale) || parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriRimanenti = metriTotali - metriInstallati;\n\n    const newStatistics = {\n      totaleCavi,\n      caviInstallati,\n      caviDaInstallare,\n      caviInCorso,\n      caviCollegati,\n      caviNonCollegati,\n      percentualeInstallazione,\n      percentualeCollegamento,\n      metriTotali: Math.round(metriTotali),\n      metriInstallati: Math.round(metriInstallati),\n      metriRimanenti: Math.round(metriRimanenti)\n    };\n\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  };\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Funzione per caricare le revisioni disponibili\n  const loadRevisioni = async (cantiereIdToUse) => {\n    try {\n      console.log('Caricamento revisioni per cantiere:', cantiereIdToUse);\n\n      // Carica la revisione corrente\n      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);\n      console.log('Revisione corrente:', revisioneCorrenteData);\n      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);\n\n      // Carica tutte le revisioni disponibili\n      const revisioniData = await caviService.getRevisioniDisponibili(cantiereIdToUse);\n      console.log('Revisioni disponibili:', revisioniData);\n      setRevisioniDisponibili(revisioniData.revisioni || []);\n\n      // LOGICA REVISIONI: La revisione corrente è quella di default\n      // Non impostiamo una revisione selezionata, così il sistema usa automaticamente la corrente\n      console.log('Logica revisioni: usando revisione corrente di default');\n    } catch (error) {\n      console.error('Errore nel caricamento delle revisioni:', error);\n    }\n  };\n\n  // Funzione per gestire il cambio di revisione\n  const handleRevisioneChange = (event) => {\n    const nuovaRevisione = event.target.value;\n\n    // LOGICA REVISIONI:\n    // - Se vuoto o \"corrente\" -> usa revisione corrente (non specificare parametro)\n    // - Se specifica -> usa quella revisione per visualizzazione storica\n    if (nuovaRevisione === '' || nuovaRevisione === 'corrente') {\n      setRevisioneSelezionata('');\n      console.log('Passaggio a revisione corrente (default)');\n    } else {\n      setRevisioneSelezionata(nuovaRevisione);\n      console.log('Passaggio a revisione storica:', nuovaRevisione);\n    }\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le revisioni disponibili\n        await loadRevisioni(cantiereIdNum);\n\n\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi attivi\n          calculateStatistics(attivi || [], caviSpare);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi spare\n          calculateStatistics(caviAttivi, spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = (cavo) => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({ ...prev, open: false }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({ open: true, message, severity });\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Dashboard con statistiche avanzate simile a quello della certificazione\n  const renderDashboard = () => (\n    <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n      <Grid container spacing={2}>\n        {/* Prima riga - Statistiche principali */}\n        <Grid item xs={6} sm={3}>\n          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'primary.main', borderRadius: 1, color: 'white' }}>\n            <CableIcon sx={{ fontSize: 32, mb: 1 }} />\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 0.5 }}>\n              {statistics.totaleCavi}\n            </Typography>\n            <Typography variant=\"body2\">Totale Cavi</Typography>\n          </Box>\n        </Grid>\n\n        <Grid item xs={6} sm={3}>\n          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'success.main', borderRadius: 1, color: 'white' }}>\n            <CheckCircleIcon sx={{ fontSize: 32, mb: 1 }} />\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 0.5 }}>\n              {statistics.caviInstallati}\n            </Typography>\n            <Typography variant=\"body2\">Installati</Typography>\n          </Box>\n        </Grid>\n\n        <Grid item xs={6} sm={3}>\n          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'warning.main', borderRadius: 1, color: 'white' }}>\n            <ScheduleIcon sx={{ fontSize: 32, mb: 1 }} />\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 0.5 }}>\n              {statistics.caviDaInstallare + statistics.caviInCorso}\n            </Typography>\n            <Typography variant=\"body2\">Da Installare</Typography>\n          </Box>\n        </Grid>\n\n        <Grid item xs={6} sm={3}>\n          <Box sx={{\n            textAlign: 'center',\n            p: 2,\n            bgcolor: statistics.percentualeInstallazione >= 80 ? 'success.main' :\n                     statistics.percentualeInstallazione >= 50 ? 'warning.main' : 'error.main',\n            borderRadius: 1,\n            color: 'white'\n          }}>\n            <TimelineIcon sx={{ fontSize: 32, mb: 1 }} />\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 0.5 }}>\n              {statistics.percentualeInstallazione}%\n            </Typography>\n            <Typography variant=\"body2\">Completamento</Typography>\n          </Box>\n        </Grid>\n\n        {/* Seconda riga - Statistiche collegamenti */}\n        <Grid item xs={6} sm={3}>\n          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'info.main', borderRadius: 1, color: 'white' }}>\n            <LinkIcon sx={{ fontSize: 32, mb: 1 }} />\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 0.5 }}>\n              {statistics.caviCollegati}\n            </Typography>\n            <Typography variant=\"body2\">Collegati</Typography>\n          </Box>\n        </Grid>\n\n        <Grid item xs={6} sm={3}>\n          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'grey.600', borderRadius: 1, color: 'white' }}>\n            <LinkOffIcon sx={{ fontSize: 32, mb: 1 }} />\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 0.5 }}>\n              {statistics.caviNonCollegati}\n            </Typography>\n            <Typography variant=\"body2\">Non Collegati</Typography>\n          </Box>\n        </Grid>\n\n        <Grid item xs={6} sm={3}>\n          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'secondary.main', borderRadius: 1, color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 0.5 }}>\n              {statistics.metriInstallati}m\n            </Typography>\n            <Typography variant=\"body2\">Metri Installati</Typography>\n          </Box>\n        </Grid>\n\n        <Grid item xs={6} sm={3}>\n          <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'grey.400', borderRadius: 1, color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 0.5 }}>\n              {statistics.metriRimanenti}m\n            </Typography>\n            <Typography variant=\"body2\">Metri Rimanenti</Typography>\n          </Box>\n        </Grid>\n      </Grid>\n    </Paper>\n  );\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Cavo: {selectedCavo.id_cavo}\n        </DialogTitle>\n        <DialogContent dividers>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Informazioni Generali</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>\n                {/* n_conduttori field is now a spare field (kept in DB but hidden in UI) */}\n                <Typography variant=\"body2\"><strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                {/* sh field is now a spare field (kept in DB but hidden in UI) */}\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Partenza</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Arrivo</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Installazione</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>\n                <Typography variant=\"body2\"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDetails}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n\n\n  return (\n    <Box className=\"cavi-page\">\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <CircularProgress size={40} />\n          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          {/* Selettore Revisione */}\n          {revisioniDisponibili.length > 0 && (\n            <Paper sx={{ p: 2, mb: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                <Typography variant=\"h6\">Visualizzazione:</Typography>\n                <FormControl size=\"small\" sx={{ minWidth: 250 }}>\n                  <InputLabel>Revisione da Visualizzare</InputLabel>\n                  <Select\n                    value={revisioneSelezionata || 'corrente'}\n                    onChange={handleRevisioneChange}\n                    label=\"Revisione da Visualizzare\"\n                  >\n                    <MenuItem value=\"corrente\">\n                      📋 Revisione Corrente {revisioneCorrente && `(${revisioneCorrente})`}\n                    </MenuItem>\n                    {revisioniDisponibili.map((rev) => (\n                      <MenuItem key={rev.revisione} value={rev.revisione}>\n                        📚 {rev.revisione} ({rev.cavi_count} cavi)\n                        {rev.revisione === revisioneCorrente && ' - Attuale'}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n                <Chip\n                  label={\n                    revisioneSelezionata\n                      ? `Storico: ${revisioneSelezionata}`\n                      : `Corrente: ${revisioneCorrente || 'N/A'}`\n                  }\n                  color={revisioneSelezionata ? \"secondary\" : \"primary\"}\n                  variant=\"outlined\"\n                />\n              </Box>\n            </Paper>\n          )}\n\n\n\n          {/* Sezione Cavi */}\n          <Box sx={{ mt: 4 }}>\n          </Box>\n\n          <Box sx={{ mb: 2 }}>\n            {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}\n            {process.env.NODE_ENV === 'development' && caviAttivi.length > 0 && (\n              <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>\n                {Object.keys(caviAttivi[0]).map(key => (\n                  <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>\n                ))}\n              </Box>\n            )}\n            <CaviFilterableTable\n              cavi={caviAttivi}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}\n              revisioneCorrente={caviAttivi[0]?.revisione_ufficiale || caviAttivi[0]?.revisione || caviAttivi[0]?.rev}\n            />\n            {caviAttivi.length === 0 && !loading && (\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                Nessun cavo attivo trovato. I cavi attivi appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Sezione Cavi Spare */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}\n              </Typography>\n            </Box>\n            <CaviFilterableTable\n              cavi={caviSpare}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}\n            />\n            {caviSpare.length === 0 && !loading && (\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Rimossa sezione Debug */}\n\n          {/* Dialogo dei dettagli del cavo */}\n          {renderDetailsDialog()}\n\n          {/* Dialogo per l'eliminazione dei cavi */}\n          <Dialog\n            open={openEliminaCavoDialog}\n            onClose={() => setOpenEliminaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio di successo con Snackbar\n                  showNotification(message, 'success');\n                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    console.log('Ricaricamento dati dopo operazione...');\n                    try {\n                      // Ricarica i dati invece di ricaricare la pagina\n                      fetchCavi(true);\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'eliminazione del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                fetchCavi();\n              }}\n              initialOption=\"eliminaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per la modifica dei cavi */}\n          {/* Log del cantiereId prima di aprire il dialog */}\n          {openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId)}\n\n          <Dialog\n            open={openModificaCavoDialog}\n            onClose={() => setOpenModificaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"sm\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio di successo con Snackbar\n                  showNotification(message, 'success');\n                  // Ricarica i dati immediatamente\n                  console.log('Ricaricamento dati dopo operazione...');\n                  // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    try {\n                      fetchCavi(true);\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante la modifica del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                console.log('Ricaricamento dati dopo errore...');\n                fetchCavi(true);\n              }}\n              initialOption=\"modificaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per l'aggiunta di un nuovo cavo */}\n          <Dialog open={openAggiungiCavoDialog} onClose={() => setOpenAggiungiCavoDialog(false)} maxWidth=\"sm\" fullWidth>\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenAggiungiCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio di successo con Snackbar\n                  showNotification(message, 'success');\n                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    console.log('Ricaricamento dati dopo operazione...');\n                    try {\n                      // Ricarica i dati in modalità silenziosa per evitare il \"blink\" della pagina\n                      fetchCavi(true);\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina immediatamente\n                      console.log('Tentativo di ricaricamento della pagina...');\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'aggiunta del cavo:', message);\n                // Mostra un messaggio di errore con Snackbar\n                showNotification(`Errore: ${message}`, 'error');\n                // Chiudi il dialogo\n                setOpenAggiungiCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                fetchCavi(true);\n              }}\n              initialOption=\"aggiungiCavo\"\n            />\n          </Dialog>\n\n          {/* Snackbar per le notifiche */}\n          <Snackbar\n            open={notification.open}\n            autoHideDuration={4000}\n            onClose={handleCloseNotification}\n            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n          >\n            <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n              {notification.message}\n            </Alert>\n          </Snackbar>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,gBAAgB,EAChBC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,QACH,eAAe;AACtB,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,SACEC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,SAASC,2BAA2B,QAAQ,6BAA6B;AACzE,OAAOC,mBAAmB,MAAM,2CAA2C;AAC3E,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEgB,qBAAqB;IAAEC,wBAAwB;IAAEC,sBAAsB;IAAEC,yBAAyB;IAAEC,sBAAsB;IAAEC;EAA0B,CAAC,GAAGpB,gBAAgB,CAAC,CAAC;EACpL,MAAMqB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgE,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkE,SAAS,EAAEC,YAAY,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoE,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsE,KAAK,EAAEC,QAAQ,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC;IAAE0E,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EACnG;;EAEA;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACiF,UAAU,EAAEC,aAAa,CAAC,GAAGlF,QAAQ,CAAC;IAC3CmF,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,gBAAgB,EAAE,CAAC;IACnBC,wBAAwB,EAAE,CAAC;IAC3BC,uBAAuB,EAAE,CAAC;IAC1BC,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAIF;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACgG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACkG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;;EAE9D;;EAEA;EACA,MAAMoG,mBAAmB,GAAGA,CAACC,cAAc,EAAEC,aAAa,KAAK;IAC7D,MAAMC,SAAS,GAAG,CAAC,IAAIF,cAAc,IAAI,EAAE,CAAC,EAAE,IAAIC,aAAa,IAAI,EAAE,CAAC,CAAC;IAEvE,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MAC1BC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE;IACF;IAEAD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;MAC3C1C,UAAU,EAAE,CAAAqC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEG,MAAM,KAAI,CAAC;MACvCtC,SAAS,EAAE,CAAAoC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEE,MAAM,KAAI,CAAC;MACrCG,MAAM,EAAEJ,SAAS,CAACC;IACpB,CAAC,CAAC;IAEF,MAAMrB,UAAU,GAAGoB,SAAS,CAACC,MAAM;;IAEnC;IACA,MAAMpB,cAAc,GAAGmB,SAAS,CAACK,MAAM,CAACC,IAAI,IAC1CA,IAAI,CAACC,mBAAmB,KAAK,YAAY,IACzCD,IAAI,CAACC,mBAAmB,KAAK,YAAY,IACzCD,IAAI,CAACC,mBAAmB,KAAK,QAC/B,CAAC,CAACN,MAAM;IAER,MAAMnB,gBAAgB,GAAGkB,SAAS,CAACK,MAAM,CAACC,IAAI,IAC5CA,IAAI,CAACC,mBAAmB,KAAK,eAAe,IAC5CD,IAAI,CAACC,mBAAmB,KAAK,eAC/B,CAAC,CAACN,MAAM;IAER,MAAMlB,WAAW,GAAGiB,SAAS,CAACK,MAAM,CAACC,IAAI,IACvCA,IAAI,CAACC,mBAAmB,KAAK,UAAU,IACvCD,IAAI,CAACC,mBAAmB,KAAK,UAC/B,CAAC,CAACN,MAAM;;IAER;IACA,MAAMjB,aAAa,GAAGgB,SAAS,CAACK,MAAM,CAACC,IAAI,IACzCA,IAAI,CAACE,YAAY,KAAK,CAAC,IACvBF,IAAI,CAACG,qBAAqB,IAC1BH,IAAI,CAACI,mBACP,CAAC,CAACT,MAAM;IAER,MAAMhB,gBAAgB,GAAGL,UAAU,GAAGI,aAAa;;IAEnD;IACA,MAAME,wBAAwB,GAAGN,UAAU,GAAG,CAAC,GAAG+B,IAAI,CAACC,KAAK,CAAE/B,cAAc,GAAGD,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;IACrG,MAAMO,uBAAuB,GAAGP,UAAU,GAAG,CAAC,GAAG+B,IAAI,CAACC,KAAK,CAAE5B,aAAa,GAAGJ,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;IAEnG;IACA,MAAMQ,WAAW,GAAGY,SAAS,CAACa,MAAM,CAAC,CAACC,GAAG,EAAER,IAAI,KAAKQ,GAAG,IAAIC,UAAU,CAACT,IAAI,CAACU,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACnG,MAAM3B,eAAe,GAAGW,SAAS,CAC9BK,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,mBAAmB,KAAK,YAAY,IAAID,IAAI,CAACC,mBAAmB,KAAK,YAAY,IAAID,IAAI,CAACC,mBAAmB,KAAK,QAAQ,CAAC,CAC/IM,MAAM,CAAC,CAACC,GAAG,EAAER,IAAI,KAAKQ,GAAG,IAAIC,UAAU,CAACT,IAAI,CAACW,eAAe,CAAC,IAAIF,UAAU,CAACT,IAAI,CAACU,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5G,MAAM1B,cAAc,GAAGF,WAAW,GAAGC,eAAe;IAEpD,MAAM6B,aAAa,GAAG;MACpBtC,UAAU;MACVC,cAAc;MACdC,gBAAgB;MAChBC,WAAW;MACXC,aAAa;MACbC,gBAAgB;MAChBC,wBAAwB;MACxBC,uBAAuB;MACvBC,WAAW,EAAEuB,IAAI,CAACC,KAAK,CAACxB,WAAW,CAAC;MACpCC,eAAe,EAAEsB,IAAI,CAACC,KAAK,CAACvB,eAAe,CAAC;MAC5CC,cAAc,EAAEqB,IAAI,CAACC,KAAK,CAACtB,cAAc;IAC3C,CAAC;IAEDY,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEe,aAAa,CAAC;IAC1DvC,aAAa,CAACuC,aAAa,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAC,qBAAqB,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAOC,eAAe,IAAK;IAC/C,IAAI;MACFpB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEmB,eAAe,CAAC;;MAEnE;MACA,MAAMC,qBAAqB,GAAG,MAAMtF,WAAW,CAACuF,oBAAoB,CAACF,eAAe,CAAC;MACrFpB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoB,qBAAqB,CAAC;MACzD3B,oBAAoB,CAAC2B,qBAAqB,CAACE,kBAAkB,CAAC;;MAE9D;MACA,MAAMC,aAAa,GAAG,MAAMzF,WAAW,CAAC0F,uBAAuB,CAACL,eAAe,CAAC;MAChFpB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEuB,aAAa,CAAC;MACpDlC,uBAAuB,CAACkC,aAAa,CAACE,SAAS,IAAI,EAAE,CAAC;;MAEtD;MACA;MACA1B,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IACvE,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACjE;EACF,CAAC;;EAED;EACA,MAAM8D,qBAAqB,GAAIC,KAAK,IAAK;IACvC,MAAMC,cAAc,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;;IAEzC;IACA;IACA;IACA,IAAIF,cAAc,KAAK,EAAE,IAAIA,cAAc,KAAK,UAAU,EAAE;MAC1DrC,uBAAuB,CAAC,EAAE,CAAC;MAC3BQ,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,MAAM;MACLT,uBAAuB,CAACqC,cAAc,CAAC;MACvC7B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE4B,cAAc,CAAC;IAC/D;EACF,CAAC;;EAED;EACA,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAG1I,QAAQ,CAAC;IACrC8G,mBAAmB,EAAE,EAAE;IACvB6B,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEnB,qBAAqB,CAAC,GAAG3H,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC+I,aAAa,EAAEC,gBAAgB,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;;EAEtD;;EAEA;EACA;EACA,MAAMiJ,SAAS,GAAG,MAAAA,CAAOC,aAAa,GAAG,KAAK,KAAK;IACjD,IAAI;MACF,IAAI,CAACA,aAAa,EAAE;QAClB7E,UAAU,CAAC,IAAI,CAAC;MAClB;MACAoC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE9C,UAAU,CAAC;;MAEzD;MACA,IAAI,CAACA,UAAU,EAAE;QACf6C,OAAO,CAACnC,KAAK,CAAC,mCAAmC,EAAEV,UAAU,CAAC;QAC9DW,QAAQ,CAAC,wDAAwD,CAAC;QAClEF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAIwD,eAAe,GAAGjE,UAAU;MAChC,IAAI,CAACiE,eAAe,EAAE;QACpBA,eAAe,GAAGsB,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QAC5D3C,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEmB,eAAe,CAAC;QACnE,IAAI,CAACA,eAAe,EAAE;UACpBpB,OAAO,CAACnC,KAAK,CAAC,2CAA2C,CAAC;UAC1DC,QAAQ,CAAC,8CAA8C,CAAC;UACxDF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACAoC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,IAAI2C,MAAM,GAAG,EAAE;MACf,IAAI;QACFA,MAAM,GAAG,MAAM7G,WAAW,CAAC8G,OAAO,CAACzB,eAAe,EAAE,CAAC,EAAEY,OAAO,CAAC;QAC/DhC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE2C,MAAM,GAAGA,MAAM,CAAC7C,MAAM,GAAG,CAAC,CAAC;MAClE,CAAC,CAAC,OAAO+C,WAAW,EAAE;QACpB9C,OAAO,CAACnC,KAAK,CAAC,yCAAyC,EAAEiF,WAAW,CAAC;QACrE;QACAF,MAAM,GAAG,EAAE;MACb;;MAEA;MACA,IAAIA,MAAM,IAAIA,MAAM,CAAC7C,MAAM,GAAG,CAAC,EAAE;QAC/B,MAAMgD,kBAAkB,GAAGH,MAAM,CAACzC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC4C,sBAAsB,KAAK,CAAC,CAAC;QACnF,IAAID,kBAAkB,CAAChD,MAAM,GAAG,CAAC,EAAE;UACjCC,OAAO,CAACnC,KAAK,CAAC,wEAAwE,EAAEkF,kBAAkB,CAAC;QAC7G;MACF;MAEAvF,aAAa,CAACoF,MAAM,IAAI,EAAE,CAAC;;MAE3B;MACA,IAAIK,KAAK,GAAG,EAAE;MACd,IAAI;QACFjD,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9DgD,KAAK,GAAG,MAAMlH,WAAW,CAACmH,YAAY,CAAC9B,eAAe,CAAC;QACvDpB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEgD,KAAK,GAAGA,KAAK,CAAClD,MAAM,GAAG,CAAC,CAAC;QACnF,IAAIkD,KAAK,IAAIA,KAAK,CAAClD,MAAM,GAAG,CAAC,EAAE;UAC7BC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEgD,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOE,UAAU,EAAE;QACnBnD,OAAO,CAACnC,KAAK,CAAC,8DAA8D,EAAEsF,UAAU,CAAC;QACzF;QACA,IAAI;UACFnD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/CgD,KAAK,GAAG,MAAMlH,WAAW,CAAC8G,OAAO,CAACzB,eAAe,EAAE,CAAC,CAAC;UACrDpB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEgD,KAAK,GAAGA,KAAK,CAAClD,MAAM,GAAG,CAAC,CAAC;QACnF,CAAC,CAAC,OAAOqD,aAAa,EAAE;UACtBpD,OAAO,CAACnC,KAAK,CAAC,mCAAmC,EAAEuF,aAAa,CAAC;UACjE;UACAH,KAAK,GAAG,EAAE;QACZ;MACF;MACAvF,YAAY,CAACuF,KAAK,IAAI,EAAE,CAAC;;MAIzB;MACAnF,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdmC,OAAO,CAACnC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjEC,QAAQ,CAAC,oCAAoCD,KAAK,CAACK,OAAO,IAAI,oBAAoB,EAAE,CAAC;;MAErF;MACAmF,UAAU,CAAC,MAAM;QACf;QACA,IAAIC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACC,QAAQ,CAAC,iCAAiC,CAAC,EAAE;UACzEzD,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;UAC7EyD,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC1B;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,SAAS;MACR,IAAI,CAACnB,aAAa,EAAE;QAClB7E,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;;EAED;EACApE,SAAS,CAAC,MAAM;IACd;IACAyH,sBAAsB,CAAC,CAAC;IAExB,MAAM4C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF7D,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAM6D,KAAK,GAAGpB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C3C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC6D,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACVhG,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAImG,kBAAkB,GAAGrB,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAIqB,oBAAoB,GAAGtB,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvE3C,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAE8D,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnGhE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEtD,IAAI,CAAC;;QAEjC;QACAqD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,YAAY,CAAC3C,MAAM,EAAEkE,CAAC,EAAE,EAAE;UAC5C,MAAMC,GAAG,GAAGxB,YAAY,CAACwB,GAAG,CAACD,CAAC,CAAC;UAC/BjE,OAAO,CAACC,GAAG,CAAC,GAAGiE,GAAG,KAAKxB,YAAY,CAACC,OAAO,CAACuB,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAAvH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwH,IAAI,MAAK,eAAe,EAAE;UAClCnE,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAItD,IAAI,CAACyH,WAAW,EAAE;YACpBpE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEtD,IAAI,CAACyH,WAAW,CAAC;YACrEL,kBAAkB,GAAGpH,IAAI,CAACyH,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDL,oBAAoB,GAAGrH,IAAI,CAAC2H,aAAa,IAAI,YAAY3H,IAAI,CAACyH,WAAW,EAAE;;YAE3E;YACA1B,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;YAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;YAClEhE,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE8D,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACF/D,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAM6D,KAAK,GAAGpB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAImB,KAAK,EAAE;gBACT;gBACA,MAAMU,SAAS,GAAGV,KAAK,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvC5E,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmF,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvBpE,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEmF,OAAO,CAAChB,WAAW,CAAC;kBACtEL,kBAAkB,GAAGqB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAL,oBAAoB,GAAG,YAAYoB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACA1B,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;kBAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;kBAClEhE,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE8D,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOwB,CAAC,EAAE;cACVvF,OAAO,CAACnC,KAAK,CAAC,6CAA6C,EAAE0H,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACxB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9F/D,OAAO,CAACwF,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACAzB,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACAtB,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;UAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;UAClEhE,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE8D,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvBjG,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAM6H,aAAa,GAAGC,QAAQ,CAAC3B,kBAAkB,EAAE,EAAE,CAAC;QACtD/D,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEwF,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxB3H,QAAQ,CAAC,2BAA2BiG,kBAAkB,mCAAmC,CAAC;UAC1FnG,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAACqI,aAAa,CAAC;QAC5BnI,eAAe,CAAC0G,oBAAoB,IAAI,YAAYyB,aAAa,EAAE,CAAC;;QAEpE;QACA,MAAMtE,aAAa,CAACsE,aAAa,CAAC;;QAIlC;QACAzF,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEwF,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD1C,UAAU,CAAC,MAAM0C,MAAM,CAAC,IAAIC,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACAhG,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE+B,OAAO,CAAC;UAC1E,MAAMiE,WAAW,GAAGlK,WAAW,CAAC8G,OAAO,CAAC4C,aAAa,EAAE,CAAC,EAAEzD,OAAO,CAAC;UAClE,MAAMY,MAAM,GAAG,MAAMiD,OAAO,CAACK,IAAI,CAAC,CAACD,WAAW,EAAEL,cAAc,CAAC,CAAC;UAEhE5F,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE2C,MAAM,CAAC;UAC5C5C,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE2C,MAAM,GAAGA,MAAM,CAAC7C,MAAM,GAAG,CAAC,CAAC;UACzE,IAAI6C,MAAM,IAAIA,MAAM,CAAC7C,MAAM,GAAG,CAAC,EAAE;YAC/BC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE2C,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,MAAM;YACL5C,OAAO,CAACwF,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;UACAjI,aAAa,CAACoF,MAAM,IAAI,EAAE,CAAC;;UAE3B;UACAjD,mBAAmB,CAACiD,MAAM,IAAI,EAAE,EAAEnF,SAAS,CAAC;QAC9C,CAAC,CAAC,OAAO0I,SAAS,EAAE;UAClBnG,OAAO,CAACnC,KAAK,CAAC,yCAAyC,EAAEsI,SAAS,CAAC;UACnEnG,OAAO,CAACnC,KAAK,CAAC,8BAA8B,EAAE;YAC5CK,OAAO,EAAEiI,SAAS,CAACjI,OAAO;YAC1BkI,MAAM,EAAED,SAAS,CAACC,MAAM;YACxBC,IAAI,EAAEF,SAAS,CAACE,IAAI;YACpBC,KAAK,EAAEH,SAAS,CAACG,KAAK;YACtBC,IAAI,EAAEJ,SAAS,CAACI,IAAI;YACpBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,QAAQ,EAAEN,SAAS,CAACM,QAAQ,GAAG;cAC7BL,MAAM,EAAED,SAAS,CAACM,QAAQ,CAACL,MAAM;cACjCM,UAAU,EAAEP,SAAS,CAACM,QAAQ,CAACC,UAAU;cACzCL,IAAI,EAAEF,SAAS,CAACM,QAAQ,CAACJ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA7I,aAAa,CAAC,EAAE,CAAC;UACjBwC,OAAO,CAACwF,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACA1H,QAAQ,CAAC,2CAA2CqI,SAAS,CAACjI,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACA8B,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEwF,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD1C,UAAU,CAAC,MAAM0C,MAAM,CAAC,IAAIC,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACAhG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD;UACA,MAAM0G,YAAY,GAAG5K,WAAW,CAAC8G,OAAO,CAAC4C,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAMxC,KAAK,GAAG,MAAM4C,OAAO,CAACK,IAAI,CAAC,CAACS,YAAY,EAAEf,cAAc,CAAC,CAAC;UAEhE5F,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgD,KAAK,CAAC;UAC1CjD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEgD,KAAK,GAAGA,KAAK,CAAClD,MAAM,GAAG,CAAC,CAAC;UACtE,IAAIkD,KAAK,IAAIA,KAAK,CAAClD,MAAM,GAAG,CAAC,EAAE;YAC7BC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEgD,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACLjD,OAAO,CAACwF,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACA/H,YAAY,CAACuF,KAAK,IAAI,EAAE,CAAC;;UAEzB;UACAtD,mBAAmB,CAACpC,UAAU,EAAE0F,KAAK,IAAI,EAAE,CAAC;QAC9C,CAAC,CAAC,OAAOE,UAAU,EAAE;UACnBnD,OAAO,CAACnC,KAAK,CAAC,wCAAwC,EAAEsF,UAAU,CAAC;UACnEnD,OAAO,CAACnC,KAAK,CAAC,6BAA6B,EAAE;YAC3CK,OAAO,EAAEiF,UAAU,CAACjF,OAAO;YAC3BkI,MAAM,EAAEjD,UAAU,CAACiD,MAAM;YACzBC,IAAI,EAAElD,UAAU,CAACkD,IAAI;YACrBC,KAAK,EAAEnD,UAAU,CAACmD,KAAK;YACvBC,IAAI,EAAEpD,UAAU,CAACoD,IAAI;YACrBC,IAAI,EAAErD,UAAU,CAACqD,IAAI;YACrBC,QAAQ,EAAEtD,UAAU,CAACsD,QAAQ,GAAG;cAC9BL,MAAM,EAAEjD,UAAU,CAACsD,QAAQ,CAACL,MAAM;cAClCM,UAAU,EAAEvD,UAAU,CAACsD,QAAQ,CAACC,UAAU;cAC1CL,IAAI,EAAElD,UAAU,CAACsD,QAAQ,CAACJ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA3I,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0CqF,UAAU,CAACjF,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACAN,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAOgJ,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZlH,OAAO,CAACnC,KAAK,CAAC,kCAAkC,EAAE+I,GAAG,CAAC;QACtD5G,OAAO,CAACnC,KAAK,CAAC,2BAA2B,EAAE;UACzCK,OAAO,EAAE0I,GAAG,CAAC1I,OAAO;UACpBkI,MAAM,EAAEQ,GAAG,CAACR,MAAM,MAAAS,aAAA,GAAID,GAAG,CAACH,QAAQ,cAAAI,aAAA,uBAAZA,aAAA,CAAcT,MAAM;UAC1CC,IAAI,EAAEO,GAAG,CAACP,IAAI,MAAAS,cAAA,GAAIF,GAAG,CAACH,QAAQ,cAAAK,cAAA,uBAAZA,cAAA,CAAcT,IAAI;UACpCC,KAAK,EAAEM,GAAG,CAACN;QACb,CAAC,CAAC;;QAEF;QACA,IAAIa,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAAC1I,OAAO,IAAI0I,GAAG,CAAC1I,OAAO,CAACuF,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjE0D,YAAY,GAAGP,GAAG,CAAC1I,OAAO;QAC5B,CAAC,MAAM,IAAI0I,GAAG,CAACR,MAAM,KAAK,GAAG,IAAIQ,GAAG,CAACR,MAAM,KAAK,GAAG,IACzC,EAAAW,cAAA,GAAAH,GAAG,CAACH,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcX,MAAM,MAAK,GAAG,IAAI,EAAAY,cAAA,GAAAJ,GAAG,CAACH,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcZ,MAAM,MAAK,GAAG,EAAE;UACtEe,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAACH,QAAQ,cAAAQ,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,eAAlBA,mBAAA,CAAoBE,MAAM,EAAE;UACrC;UACAD,YAAY,GAAG,eAAeP,GAAG,CAACH,QAAQ,CAACJ,IAAI,CAACe,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIR,GAAG,CAACL,IAAI,KAAK,aAAa,EAAE;UACrC;UACAY,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAAC1I,OAAO,EAAE;UACtBiJ,YAAY,GAAGP,GAAG,CAAC1I,OAAO;QAC5B;QAEAJ,QAAQ,CAAC,gCAAgCqJ,YAAY,sBAAsB,CAAC;;QAE5E;QACA3J,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDiG,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC7B,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf;;EAEA;EACA,MAAMqF,iBAAiB,GAAIjH,IAAI,IAAK;IAClC/B,eAAe,CAAC+B,IAAI,CAAC;IACrB7B,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM+I,kBAAkB,GAAGA,CAAA,KAAM;IAC/B/I,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMkJ,uBAAuB,GAAGA,CAAA,KAAM;IACpCvJ,eAAe,CAACwJ,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEvJ,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;;EAED;EACA,MAAMwJ,gBAAgB,GAAGA,CAACvJ,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IAC1DH,eAAe,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EACpD,CAAC;;EAED;;EAEA;EACA,MAAMuJ,eAAe,GAAGA,CAAA,kBACtBtL,OAAA,CAACzC,KAAK;IAACgO,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAU,CAAE;IAAAC,QAAA,eAC7C3L,OAAA,CAACvC,IAAI;MAACmO,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAF,QAAA,gBAEzB3L,OAAA,CAACvC,IAAI;QAACqO,IAAI;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACtB3L,OAAA,CAAC3C,GAAG;UAACkO,EAAE,EAAE;YAAEU,SAAS,EAAE,QAAQ;YAAET,CAAC,EAAE,CAAC;YAAEE,OAAO,EAAE,cAAc;YAAEQ,YAAY,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAQ,CAAE;UAAAR,QAAA,gBAC/F3L,OAAA,CAACpB,SAAS;YAAC2M,EAAE,EAAE;cAAEa,QAAQ,EAAE,EAAE;cAAEX,EAAE,EAAE;YAAE;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CxM,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,IAAI;YAAClB,EAAE,EAAE;cAAEmB,UAAU,EAAE,MAAM;cAAEjB,EAAE,EAAE;YAAI,CAAE;YAAAE,QAAA,EAC1DvJ,UAAU,CAACE;UAAU;YAAA+J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACbxM,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,OAAO;YAAAd,QAAA,EAAC;UAAW;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEPxM,OAAA,CAACvC,IAAI;QAACqO,IAAI;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACtB3L,OAAA,CAAC3C,GAAG;UAACkO,EAAE,EAAE;YAAEU,SAAS,EAAE,QAAQ;YAAET,CAAC,EAAE,CAAC;YAAEE,OAAO,EAAE,cAAc;YAAEQ,YAAY,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAQ,CAAE;UAAAR,QAAA,gBAC/F3L,OAAA,CAAClB,eAAe;YAACyM,EAAE,EAAE;cAAEa,QAAQ,EAAE,EAAE;cAAEX,EAAE,EAAE;YAAE;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChDxM,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,IAAI;YAAClB,EAAE,EAAE;cAAEmB,UAAU,EAAE,MAAM;cAAEjB,EAAE,EAAE;YAAI,CAAE;YAAAE,QAAA,EAC1DvJ,UAAU,CAACG;UAAc;YAAA8J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACbxM,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,OAAO;YAAAd,QAAA,EAAC;UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEPxM,OAAA,CAACvC,IAAI;QAACqO,IAAI;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACtB3L,OAAA,CAAC3C,GAAG;UAACkO,EAAE,EAAE;YAAEU,SAAS,EAAE,QAAQ;YAAET,CAAC,EAAE,CAAC;YAAEE,OAAO,EAAE,cAAc;YAAEQ,YAAY,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAQ,CAAE;UAAAR,QAAA,gBAC/F3L,OAAA,CAAChB,YAAY;YAACuM,EAAE,EAAE;cAAEa,QAAQ,EAAE,EAAE;cAAEX,EAAE,EAAE;YAAE;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CxM,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,IAAI;YAAClB,EAAE,EAAE;cAAEmB,UAAU,EAAE,MAAM;cAAEjB,EAAE,EAAE;YAAI,CAAE;YAAAE,QAAA,EAC1DvJ,UAAU,CAACI,gBAAgB,GAAGJ,UAAU,CAACK;UAAW;YAAA4J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACbxM,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,OAAO;YAAAd,QAAA,EAAC;UAAa;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEPxM,OAAA,CAACvC,IAAI;QAACqO,IAAI;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACtB3L,OAAA,CAAC3C,GAAG;UAACkO,EAAE,EAAE;YACPU,SAAS,EAAE,QAAQ;YACnBT,CAAC,EAAE,CAAC;YACJE,OAAO,EAAEtJ,UAAU,CAACQ,wBAAwB,IAAI,EAAE,GAAG,cAAc,GAC1DR,UAAU,CAACQ,wBAAwB,IAAI,EAAE,GAAG,cAAc,GAAG,YAAY;YAClFsJ,YAAY,EAAE,CAAC;YACfC,KAAK,EAAE;UACT,CAAE;UAAAR,QAAA,gBACA3L,OAAA,CAACV,YAAY;YAACiM,EAAE,EAAE;cAAEa,QAAQ,EAAE,EAAE;cAAEX,EAAE,EAAE;YAAE;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CxM,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,IAAI;YAAClB,EAAE,EAAE;cAAEmB,UAAU,EAAE,MAAM;cAAEjB,EAAE,EAAE;YAAI,CAAE;YAAAE,QAAA,GAC1DvJ,UAAU,CAACQ,wBAAwB,EAAC,GACvC;UAAA;YAAAyJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxM,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,OAAO;YAAAd,QAAA,EAAC;UAAa;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGPxM,OAAA,CAACvC,IAAI;QAACqO,IAAI;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACtB3L,OAAA,CAAC3C,GAAG;UAACkO,EAAE,EAAE;YAAEU,SAAS,EAAE,QAAQ;YAAET,CAAC,EAAE,CAAC;YAAEE,OAAO,EAAE,WAAW;YAAEQ,YAAY,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAQ,CAAE;UAAAR,QAAA,gBAC5F3L,OAAA,CAACd,QAAQ;YAACqM,EAAE,EAAE;cAAEa,QAAQ,EAAE,EAAE;cAAEX,EAAE,EAAE;YAAE;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCxM,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,IAAI;YAAClB,EAAE,EAAE;cAAEmB,UAAU,EAAE,MAAM;cAAEjB,EAAE,EAAE;YAAI,CAAE;YAAAE,QAAA,EAC1DvJ,UAAU,CAACM;UAAa;YAAA2J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACbxM,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,OAAO;YAAAd,QAAA,EAAC;UAAS;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEPxM,OAAA,CAACvC,IAAI;QAACqO,IAAI;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACtB3L,OAAA,CAAC3C,GAAG;UAACkO,EAAE,EAAE;YAAEU,SAAS,EAAE,QAAQ;YAAET,CAAC,EAAE,CAAC;YAAEE,OAAO,EAAE,UAAU;YAAEQ,YAAY,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAQ,CAAE;UAAAR,QAAA,gBAC3F3L,OAAA,CAACZ,WAAW;YAACmM,EAAE,EAAE;cAAEa,QAAQ,EAAE,EAAE;cAAEX,EAAE,EAAE;YAAE;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CxM,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,IAAI;YAAClB,EAAE,EAAE;cAAEmB,UAAU,EAAE,MAAM;cAAEjB,EAAE,EAAE;YAAI,CAAE;YAAAE,QAAA,EAC1DvJ,UAAU,CAACO;UAAgB;YAAA0J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACbxM,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,OAAO;YAAAd,QAAA,EAAC;UAAa;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEPxM,OAAA,CAACvC,IAAI;QAACqO,IAAI;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACtB3L,OAAA,CAAC3C,GAAG;UAACkO,EAAE,EAAE;YAAEU,SAAS,EAAE,QAAQ;YAAET,CAAC,EAAE,CAAC;YAAEE,OAAO,EAAE,gBAAgB;YAAEQ,YAAY,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAQ,CAAE;UAAAR,QAAA,gBACjG3L,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,IAAI;YAAClB,EAAE,EAAE;cAAEmB,UAAU,EAAE,MAAM;cAAEjB,EAAE,EAAE;YAAI,CAAE;YAAAE,QAAA,GAC1DvJ,UAAU,CAACW,eAAe,EAAC,GAC9B;UAAA;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxM,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,OAAO;YAAAd,QAAA,EAAC;UAAgB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEPxM,OAAA,CAACvC,IAAI;QAACqO,IAAI;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACtB3L,OAAA,CAAC3C,GAAG;UAACkO,EAAE,EAAE;YAAEU,SAAS,EAAE,QAAQ;YAAET,CAAC,EAAE,CAAC;YAAEE,OAAO,EAAE,UAAU;YAAEQ,YAAY,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAQ,CAAE;UAAAR,QAAA,gBAC3F3L,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,IAAI;YAAClB,EAAE,EAAE;cAAEmB,UAAU,EAAE,MAAM;cAAEjB,EAAE,EAAE;YAAI,CAAE;YAAAE,QAAA,GAC1DvJ,UAAU,CAACY,cAAc,EAAC,GAC7B;UAAA;YAAAqJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxM,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,OAAO;YAAAd,QAAA,EAAC;UAAe;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CACR;;EAED;;EAEA;;EAEA;EACA,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC3K,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEhC,OAAA,CAAC/B,MAAM;MAAC4D,IAAI,EAAEK,iBAAkB;MAAC0K,OAAO,EAAE1B,kBAAmB;MAAC2B,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAnB,QAAA,gBACnF3L,OAAA,CAAC9B,WAAW;QAAAyN,QAAA,GAAC,iBACI,EAAC3J,YAAY,CAAC+K,OAAO;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACdxM,OAAA,CAAC7B,aAAa;QAAC6O,QAAQ;QAAArB,QAAA,eACrB3L,OAAA,CAACvC,IAAI;UAACmO,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAF,QAAA,gBACzB3L,OAAA,CAACvC,IAAI;YAACqO,IAAI;YAACC,EAAE,EAAE,EAAG;YAACkB,EAAE,EAAE,CAAE;YAAAtB,QAAA,gBACvB3L,OAAA,CAAC1C,UAAU;cAACmP,OAAO,EAAC,WAAW;cAACS,YAAY;cAAAvB,QAAA,EAAC;YAAqB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/ExM,OAAA,CAAC3C,GAAG;cAACkO,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjB3L,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAQ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAACmL,OAAO,IAAI,KAAK;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAQ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAACoL,OAAO,IAAI,KAAK;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAU;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAAC8D,SAAS,IAAI,KAAK;cAAA;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtGxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAACqL,WAAW,IAAI,KAAK;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAErGxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAW;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAACsL,OAAO,IAAI,KAAK;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAElG,CAAC,eAENxM,OAAA,CAAC1C,UAAU;cAACmP,OAAO,EAAC,WAAW;cAACS,YAAY;cAAAvB,QAAA,EAAC;YAAQ;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClExM,OAAA,CAAC3C,GAAG;cAACkO,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjB3L,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAW;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAACuL,mBAAmB,IAAI,KAAK;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjHxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAACwL,eAAe,IAAI,KAAK;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzGxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAY;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAACyL,2BAA2B,IAAI,KAAK;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1HxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAa;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAACmC,qBAAqB,IAAI,KAAK;cAAA;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrHxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAQ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAAC0L,gBAAgB,IAAI,KAAK;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPxM,OAAA,CAACvC,IAAI;YAACqO,IAAI;YAACC,EAAE,EAAE,EAAG;YAACkB,EAAE,EAAE,CAAE;YAAAtB,QAAA,gBACvB3L,OAAA,CAAC1C,UAAU;cAACmP,OAAO,EAAC,WAAW;cAACS,YAAY;cAAAvB,QAAA,EAAC;YAAM;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChExM,OAAA,CAAC3C,GAAG;cAACkO,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjB3L,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAW;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAAC2L,iBAAiB,IAAI,KAAK;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/GxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAAC4L,aAAa,IAAI,KAAK;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvGxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAY;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAAC6L,yBAAyB,IAAI,KAAK;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxHxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAa;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAACoC,mBAAmB,IAAI,KAAK;cAAA;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnHxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAQ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAAC8L,cAAc,IAAI,KAAK;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CAAC,eAENxM,OAAA,CAAC1C,UAAU;cAACmP,OAAO,EAAC,WAAW;cAACS,YAAY;cAAAvB,QAAA,EAAC;YAAa;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvExM,OAAA,CAAC3C,GAAG;cAACkO,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjB3L,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAc;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAAC0C,aAAa,IAAI,KAAK;cAAA;gBAAA2H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9GxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAgB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAAC2C,eAAe,IAAI,GAAG;cAAA;gBAAA0H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChHxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3M,2BAA2B,CAACmC,YAAY,CAACiC,mBAAmB,CAAC;cAAA;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChIxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAa;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAACkC,YAAY,IAAI,GAAG;cAAA;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1GxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAAC+L,SAAS,IAAI,KAAK;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnGxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAkB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAACgM,iBAAiB,IAAI,KAAK;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtHxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAa;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,YAAY,CAACiM,YAAY,IAAI,KAAK;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC5GxM,OAAA,CAAC1C,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAd,QAAA,gBAAC3L,OAAA;kBAAA2L,QAAA,EAAQ;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI0B,IAAI,CAAClM,YAAY,CAACmM,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBxM,OAAA,CAAC5B,aAAa;QAAAuN,QAAA,eACZ3L,OAAA,CAACxC,MAAM;UAAC6Q,OAAO,EAAEnD,kBAAmB;UAAAS,QAAA,EAAC;QAAM;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;;EAIA,oBACExM,OAAA,CAAC3C,GAAG;IAACiR,SAAS,EAAC,WAAW;IAAA3C,QAAA,EACvBpK,OAAO,gBACNvB,OAAA,CAAC3C,GAAG;MAACkO,EAAE,EAAE;QAAEgD,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA/C,QAAA,gBACjF3L,OAAA,CAACjC,gBAAgB;QAAC4Q,IAAI,EAAE;MAAG;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BxM,OAAA,CAAC1C,UAAU;QAACiO,EAAE,EAAE;UAAEmD,EAAE,EAAE;QAAE,CAAE;QAAA/C,QAAA,EAAC;MAAmB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3DxM,OAAA,CAACxC,MAAM;QACLiP,OAAO,EAAC,UAAU;QAClBN,KAAK,EAAC,SAAS;QACfkC,OAAO,EAAEA,CAAA,KAAM/G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxC+D,EAAE,EAAE;UAAEmD,EAAE,EAAE;QAAE,CAAE;QAAA/C,QAAA,EACf;MAED;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJ/K,KAAK,gBACPzB,OAAA,CAAC3C,GAAG;MAAAsO,QAAA,gBACF3L,OAAA,CAACpC,KAAK;QAACmE,QAAQ,EAAC,OAAO;QAACwJ,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,GACnClK,KAAK,EACLA,KAAK,CAAC4F,QAAQ,CAAC,eAAe,CAAC,iBAC9BrH,OAAA,CAAC1C,UAAU;UAACmP,OAAO,EAAC,OAAO;UAAClB,EAAE,EAAE;YAAEmD,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBACxC3L,OAAA;YAAA2L,QAAA,EAAQ;UAAa;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAAxM,OAAA;YAAAqM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAAxM,OAAA;YAAA2L,QAAA,EAAM;UAAa;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACRxM,OAAA,CAAC3C,GAAG;QAACkO,EAAE,EAAE;UAAEgD,OAAO,EAAE,MAAM;UAAEK,GAAG,EAAE;QAAE,CAAE;QAAAjD,QAAA,eACnC3L,OAAA,CAACxC,MAAM;UACLiP,OAAO,EAAC,WAAW;UACnB6B,SAAS,EAAC,gBAAgB;UAC1BD,OAAO,EAAEA,CAAA,KAAM/G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAmE,QAAA,EACzC;QAED;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENxM,OAAA,CAAC3C,GAAG;MAAAsO,QAAA,GAED1I,oBAAoB,CAACU,MAAM,GAAG,CAAC,iBAC9B3D,OAAA,CAACzC,KAAK;QAACgO,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,eACzB3L,OAAA,CAAC3C,GAAG;UAACkO,EAAE,EAAE;YAAEgD,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEG,GAAG,EAAE;UAAE,CAAE;UAAAjD,QAAA,gBACzD3L,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,IAAI;YAAAd,QAAA,EAAC;UAAgB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtDxM,OAAA,CAAC1B,WAAW;YAACqQ,IAAI,EAAC,OAAO;YAACpD,EAAE,EAAE;cAAEsD,QAAQ,EAAE;YAAI,CAAE;YAAAlD,QAAA,gBAC9C3L,OAAA,CAACzB,UAAU;cAAAoN,QAAA,EAAC;YAAyB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClDxM,OAAA,CAACxB,MAAM;cACLmH,KAAK,EAAExC,oBAAoB,IAAI,UAAW;cAC1C2L,QAAQ,EAAEvJ,qBAAsB;cAChCwJ,KAAK,EAAC,2BAA2B;cAAApD,QAAA,gBAEjC3L,OAAA,CAACvB,QAAQ;gBAACkH,KAAK,EAAC,UAAU;gBAAAgG,QAAA,GAAC,kCACH,EAACtI,iBAAiB,IAAI,IAAIA,iBAAiB,GAAG;cAAA;gBAAAgJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,EACVvJ,oBAAoB,CAAC0F,GAAG,CAAEqG,GAAG,iBAC5BhP,OAAA,CAACvB,QAAQ;gBAAqBkH,KAAK,EAAEqJ,GAAG,CAACC,SAAU;gBAAAtD,QAAA,GAAC,eAC/C,EAACqD,GAAG,CAACC,SAAS,EAAC,IAAE,EAACD,GAAG,CAACE,UAAU,EAAC,QACpC,EAACF,GAAG,CAACC,SAAS,KAAK5L,iBAAiB,IAAI,YAAY;cAAA,GAFvC2L,GAAG,CAACC,SAAS;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGlB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACdxM,OAAA,CAAClC,IAAI;YACHiR,KAAK,EACH5L,oBAAoB,GAChB,YAAYA,oBAAoB,EAAE,GAClC,aAAaE,iBAAiB,IAAI,KAAK,EAC5C;YACD8I,KAAK,EAAEhJ,oBAAoB,GAAG,WAAW,GAAG,SAAU;YACtDsJ,OAAO,EAAC;UAAU;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAKDxM,OAAA,CAAC3C,GAAG;QAACkO,EAAE,EAAE;UAAEmD,EAAE,EAAE;QAAE;MAAE;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eAENxM,OAAA,CAAC3C,GAAG;QAACkO,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,GAEhBwD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIlO,UAAU,CAACwC,MAAM,GAAG,CAAC,iBAC9D3D,OAAA,CAAC3C,GAAG;UAACkO,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;YAAEE,OAAO,EAAE,SAAS;YAAEQ,YAAY,EAAE,CAAC;YAAEE,QAAQ,EAAE,QAAQ;YAAEkD,UAAU,EAAE,WAAW;YAAEf,OAAO,EAAE;UAAO,CAAE;UAAA5C,QAAA,EACzH4D,MAAM,CAACC,IAAI,CAACrO,UAAU,CAAC,CAAC,CAAC,CAAC,CAACwH,GAAG,CAACb,GAAG,iBACjC9H,OAAA;YAAA2L,QAAA,GAAgB7D,GAAG,EAAC,IAAE,EAACmB,IAAI,CAACwG,SAAS,CAACtO,UAAU,CAAC,CAAC,CAAC,CAAC2G,GAAG,CAAC,CAAC;UAAA,GAA/CA,GAAG;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkD,CAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eACDxM,OAAA,CAACF,mBAAmB;UAClB4P,IAAI,EAAEvO,UAAW;UACjBI,OAAO,EAAEA,OAAQ;UACjBoO,oBAAoB,EAAGC,YAAY,IAAKhM,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE+L,YAAY,CAACjM,MAAM,CAAE;UAClGN,iBAAiB,EAAE,EAAAlD,YAAA,GAAAgB,UAAU,CAAC,CAAC,CAAC,cAAAhB,YAAA,uBAAbA,YAAA,CAAe0P,mBAAmB,OAAAzP,aAAA,GAAIe,UAAU,CAAC,CAAC,CAAC,cAAAf,aAAA,uBAAbA,aAAA,CAAe6O,SAAS,OAAA5O,aAAA,GAAIc,UAAU,CAAC,CAAC,CAAC,cAAAd,aAAA,uBAAbA,aAAA,CAAe2O,GAAG;QAAC;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG,CAAC,EACDrL,UAAU,CAACwC,MAAM,KAAK,CAAC,IAAI,CAACpC,OAAO,iBAClCvB,OAAA,CAACpC,KAAK;UAACmE,QAAQ,EAAC,MAAM;UAACwJ,EAAE,EAAE;YAAEmD,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,EAAC;QAEtC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNxM,OAAA,CAAC3C,GAAG;QAACkO,EAAE,EAAE;UAAEmD,EAAE,EAAE;QAAE,CAAE;QAAA/C,QAAA,gBACjB3L,OAAA,CAAC3C,GAAG;UAACkO,EAAE,EAAE;YAAEgD,OAAO,EAAE,MAAM;YAAEuB,cAAc,EAAE,eAAe;YAAErB,UAAU,EAAE,QAAQ;YAAEhD,EAAE,EAAE;UAAE,CAAE;UAAAE,QAAA,eACzF3L,OAAA,CAAC1C,UAAU;YAACmP,OAAO,EAAC,IAAI;YAAAd,QAAA,GAAC,aACZ,EAACtK,SAAS,CAACsC,MAAM,GAAG,CAAC,GAAG,IAAItC,SAAS,CAACsC,MAAM,GAAG,GAAG,EAAE;UAAA;YAAA0I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNxM,OAAA,CAACF,mBAAmB;UAClB4P,IAAI,EAAErO,SAAU;UAChBE,OAAO,EAAEA,OAAQ;UACjBoO,oBAAoB,EAAGC,YAAY,IAAKhM,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+L,YAAY,CAACjM,MAAM;QAAE;UAAA0I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC,EACDnL,SAAS,CAACsC,MAAM,KAAK,CAAC,IAAI,CAACpC,OAAO,iBACjCvB,OAAA,CAACpC,KAAK;UAACmE,QAAQ,EAAC,MAAM;UAACwJ,EAAE,EAAE;YAAEmD,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,EAAC;QAEtC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAKLG,mBAAmB,CAAC,CAAC,eAGtB3M,OAAA,CAAC/B,MAAM;QACL4D,IAAI,EAAErB,qBAAsB;QAC5BoM,OAAO,EAAEA,CAAA,KAAMnM,wBAAwB,CAAC,KAAK,CAAE;QAC/CqM,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAlB,QAAA,eAEb3L,OAAA,CAACN,oBAAoB;UACnBqB,UAAU,EAAEA,UAAW;UACvBgP,SAAS,EAAGjO,OAAO,IAAK;YACtB;YACArB,wBAAwB,CAAC,KAAK,CAAC;;YAE/B;YACA,IAAIqB,OAAO,EAAE;cACX;cACA8B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE/B,OAAO,CAAC;cAC9C;cACAuJ,gBAAgB,CAACvJ,OAAO,EAAE,SAAS,CAAC;cACpC;cACAmF,UAAU,CAAC,MAAM;gBACfrD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD,IAAI;kBACF;kBACAuC,SAAS,CAAC,IAAI,CAAC;gBACjB,CAAC,CAAC,OAAO3E,KAAK,EAAE;kBACdmC,OAAO,CAACnC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACA6F,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA5D,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFmM,OAAO,EAAGlO,OAAO,IAAK;YACpB;YACA8B,OAAO,CAACnC,KAAK,CAAC,0CAA0C,EAAEK,OAAO,CAAC;YAClE;YACAmO,KAAK,CAAC,WAAWnO,OAAO,EAAE,CAAC;YAC3B;YACArB,wBAAwB,CAAC,KAAK,CAAC;YAC/B;YACA2F,SAAS,CAAC,CAAC;UACb,CAAE;UACF8J,aAAa,EAAC;QAAa;UAAA7D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAIR9L,sBAAsB,IAAIkD,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAE9C,UAAU,CAAC,eAEhHf,OAAA,CAAC/B,MAAM;QACL4D,IAAI,EAAEnB,sBAAuB;QAC7BkM,OAAO,EAAEA,CAAA,KAAMjM,yBAAyB,CAAC,KAAK,CAAE;QAChDmM,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAlB,QAAA,eAEb3L,OAAA,CAACN,oBAAoB;UACnBqB,UAAU,EAAEA,UAAW;UACvBgP,SAAS,EAAGjO,OAAO,IAAK;YACtB;YACAnB,yBAAyB,CAAC,KAAK,CAAC;;YAEhC;YACA,IAAImB,OAAO,EAAE;cACX;cACA8B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE/B,OAAO,CAAC;cAC9C;cACAuJ,gBAAgB,CAACvJ,OAAO,EAAE,SAAS,CAAC;cACpC;cACA8B,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;cACpD;cACAoD,UAAU,CAAC,MAAM;gBACf,IAAI;kBACFb,SAAS,CAAC,IAAI,CAAC;gBACjB,CAAC,CAAC,OAAO3E,KAAK,EAAE;kBACdmC,OAAO,CAACnC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACA6F,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA5D,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFmM,OAAO,EAAGlO,OAAO,IAAK;YACpB;YACA8B,OAAO,CAACnC,KAAK,CAAC,sCAAsC,EAAEK,OAAO,CAAC;YAC9D;YACAmO,KAAK,CAAC,WAAWnO,OAAO,EAAE,CAAC;YAC3B;YACAnB,yBAAyB,CAAC,KAAK,CAAC;YAChC;YACAiD,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAChDuC,SAAS,CAAC,IAAI,CAAC;UACjB,CAAE;UACF8J,aAAa,EAAC;QAAc;UAAA7D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGTxM,OAAA,CAAC/B,MAAM;QAAC4D,IAAI,EAAEjB,sBAAuB;QAACgM,OAAO,EAAEA,CAAA,KAAM/L,yBAAyB,CAAC,KAAK,CAAE;QAACgM,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAnB,QAAA,eAC5G3L,OAAA,CAACN,oBAAoB;UACnBqB,UAAU,EAAEA,UAAW;UACvBgP,SAAS,EAAGjO,OAAO,IAAK;YACtB;YACAjB,yBAAyB,CAAC,KAAK,CAAC;;YAEhC;YACA,IAAIiB,OAAO,EAAE;cACX;cACA8B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE/B,OAAO,CAAC;cAC9C;cACAuJ,gBAAgB,CAACvJ,OAAO,EAAE,SAAS,CAAC;cACpC;cACAmF,UAAU,CAAC,MAAM;gBACfrD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD,IAAI;kBACF;kBACAuC,SAAS,CAAC,IAAI,CAAC;gBACjB,CAAC,CAAC,OAAO3E,KAAK,EAAE;kBACdmC,OAAO,CAACnC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACAmC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;kBACzDyD,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA5D,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFmM,OAAO,EAAGlO,OAAO,IAAK;YACpB;YACA8B,OAAO,CAACnC,KAAK,CAAC,sCAAsC,EAAEK,OAAO,CAAC;YAC9D;YACAuJ,gBAAgB,CAAC,WAAWvJ,OAAO,EAAE,EAAE,OAAO,CAAC;YAC/C;YACAjB,yBAAyB,CAAC,KAAK,CAAC;YAChC;YACAuF,SAAS,CAAC,IAAI,CAAC;UACjB,CAAE;UACF8J,aAAa,EAAC;QAAc;UAAA7D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGTxM,OAAA,CAAC3B,QAAQ;QACPwD,IAAI,EAAEF,YAAY,CAACE,IAAK;QACxBsO,gBAAgB,EAAE,IAAK;QACvBvD,OAAO,EAAEzB,uBAAwB;QACjCiF,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAA3E,QAAA,eAE3D3L,OAAA,CAACpC,KAAK;UAACgP,OAAO,EAAEzB,uBAAwB;UAACpJ,QAAQ,EAAEJ,YAAY,CAACI,QAAS;UAACwJ,EAAE,EAAE;YAAEgF,KAAK,EAAE;UAAO,CAAE;UAAA5E,QAAA,EAC7FhK,YAAY,CAACG;QAAO;UAAAuK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtM,EAAA,CAv/BID,kBAAkB;EAAA,QACYT,OAAO,EACyHC,gBAAgB,EACjKF,WAAW;AAAA;AAAAiR,EAAA,GAHxBvQ,kBAAkB;AAy/BxB,eAAeA,kBAAkB;AAAC,IAAAuQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}