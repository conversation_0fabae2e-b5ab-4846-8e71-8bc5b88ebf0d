{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Typography,Paper,Grid,Card,CardContent,CardActions,Button,Chip,Alert,CircularProgress,Divider,IconButton,Tooltip,Dialog,DialogTitle,DialogContent,DialogActions,FormControl,InputLabel,Select,MenuItem,TextField,Accordion,AccordionSummary,AccordionDetails,Switch,FormControlLabel}from'@mui/material';import{Assessment as AssessmentIcon,BarChart as BarChartIcon,<PERSON><PERSON><PERSON> as PieChartIcon,Timeline as TimelineIcon,List as ListIcon,Download as DownloadIcon,Visibility as VisibilityIcon,Refresh as RefreshIcon,ArrowBack as ArrowBackIcon,DateRange as DateRangeIcon,Cable as CableIcon,Inventory as InventoryIcon,ExpandMore as ExpandMoreIcon,ShowChart as ShowChartIcon}from'@mui/icons-material';import{useNavigate,useParams}from'react-router-dom';import{useAuth}from'../../context/AuthContext';import AdminHomeButton from'../../components/common/AdminHomeButton';import reportService from'../../services/reportService';import FilterableTable from'../../components/common/FilterableTable';// Import dei componenti grafici\nimport ProgressChart from'../../components/charts/ProgressChart';import BobineChart from'../../components/charts/BobineChart';import BoqChart from'../../components/charts/BoqChart';import TimelineChart from'../../components/charts/TimelineChart';import CaviStatoChart from'../../components/charts/CaviStatoChart';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ReportCaviPageNew=()=>{const navigate=useNavigate();const{cantiereId}=useParams();const{user}=useAuth();const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const[reportData,setReportData]=useState(null);const[selectedReport,setSelectedReport]=useState(null);const[openDialog,setOpenDialog]=useState(false);const[dialogType,setDialogType]=useState('');const[formData,setFormData]=useState({formato:'video',data_inizio:'',data_fine:'',id_bobina:''});// New state to store all report data\nconst[reportsData,setReportsData]=useState({progress:null,boq:null,bobine:null,caviStato:null,bobinaSpecifica:null,posaPeriodo:null});// State per controllo visualizzazione grafici\nconst[showCharts,setShowCharts]=useState(true);// Load all basic reports on component mount\nuseEffect(()=>{const loadAllReports=async()=>{setLoading(true);try{// Create individual promises that handle their own errors\nconst progressPromise=reportService.getProgressReport(cantiereId,'video').catch(err=>{console.error('Error loading progress report:',err);return{content:null};});const boqPromise=reportService.getBillOfQuantities(cantiereId,'video').catch(err=>{console.error('Error loading BOQ report:',err);return{content:null};});const bobinePromise=reportService.getBobineReport(cantiereId,'video').catch(err=>{console.error('Error loading bobine report:',err);return{content:null};});const caviStatoPromise=reportService.getCaviStatoReport(cantiereId,'video').catch(err=>{console.error('Error loading cavi stato report:',err);return{content:null};});// Wait for all promises to resolve (they won't reject due to the catch handlers)\nconst[progressData,boqData,bobineData,caviStatoData]=await Promise.all([progressPromise,boqPromise,bobinePromise,caviStatoPromise]);// Set the data for each report, even if some are null\nsetReportsData({progress:progressData.content,boq:boqData.content,bobine:bobineData.content,caviStato:caviStatoData.content,bobinaSpecifica:null,posaPeriodo:null});// Only set error to null if we successfully loaded at least one report\nif(progressData.content||boqData.content||bobineData.content||caviStatoData.content){setError(null);}else{setError('Errore nel caricamento dei report. Riprova più tardi.');}}catch(err){// This catch block should rarely be hit due to the individual error handling above\nconsole.error('Unexpected error loading reports:',err);setError('Errore nel caricamento dei report. Riprova più tardi.');}finally{setLoading(false);}};if(cantiereId){loadAllReports();}},[cantiereId]);// Configurazione dei report disponibili\nconst reportTypes=[{id:'progress',title:'Report Avanzamento',description:'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',icon:/*#__PURE__*/_jsx(AssessmentIcon,{}),color:'primary',features:['Metri posati vs teorici','Percentuale completamento','Previsioni timeline','Performance giornaliera']},{id:'boq',title:'Bill of Quantities',description:'Distinta materiali dettagliata con analisi dei consumi e disponibilità',icon:/*#__PURE__*/_jsx(ListIcon,{}),color:'secondary',features:['Materiali per tipologia','Consumi vs disponibilità','Previsioni acquisti','Analisi costi']},{id:'bobine',title:'Report Utilizzo Bobine',description:'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',icon:/*#__PURE__*/_jsx(InventoryIcon,{}),color:'success',features:['Utilizzo per bobina','Efficienza materiali','Bobine disponibili','Analisi sprechi']},{id:'bobina-specifica',title:'Report Bobina Specifica',description:'Dettaglio approfondito di una singola bobina con tutti i cavi associati',icon:/*#__PURE__*/_jsx(CableIcon,{}),color:'info',features:['Dettaglio bobina','Cavi associati','Utilizzo specifico','Storico operazioni']},{id:'posa-periodo',title:'Report Posa per Periodo',description:'Analisi temporale della posa con trend e pattern di lavoro',icon:/*#__PURE__*/_jsx(TimelineIcon,{}),color:'warning',features:['Trend temporali','Performance periodiche','Analisi stagionali','Produttività team']},{id:'cavi-stato',title:'Report Cavi per Stato',description:'Classificazione dei cavi per stato di installazione con statistiche dettagliate',icon:/*#__PURE__*/_jsx(BarChartIcon,{}),color:'error',features:['Cavi per stato','Statistiche installazione','Problematiche','Azioni richieste']}];// Nuova funzione per generare report con formato specificato\nconst generateReportWithFormat=async(reportType,format)=>{try{setLoading(true);setError(null);let response;switch(reportType){case'progress':response=await reportService.getProgressReport(cantiereId,format);break;case'boq':response=await reportService.getBillOfQuantities(cantiereId,format);break;case'bobine':response=await reportService.getBobineReport(cantiereId,format);break;case'cavi-stato':response=await reportService.getCaviStatoReport(cantiereId,format);break;case'bobina-specifica':if(!formData.id_bobina){setError('Inserisci l\\'ID della bobina');return;}response=await reportService.getBobinaReport(cantiereId,formData.id_bobina,format);break;case'posa-periodo':if(!formData.data_inizio||!formData.data_fine){setError('Seleziona le date di inizio e fine periodo');return;}response=await reportService.getPosaPerPeriodoReport(cantiereId,formData.data_inizio,formData.data_fine,format);break;default:throw new Error('Tipo di report non riconosciuto');}if(format==='video'){// For special reports, update the specific report data\nif(reportType==='bobina-specifica'||reportType==='posa-periodo'){setReportsData(prev=>({...prev,[reportType==='bobina-specifica'?'bobinaSpecifica':'posaPeriodo']:response.content}));}setReportData(response.content);}else{// Per PDF/Excel, apri il link di download\nif(response.file_url){window.open(response.file_url,'_blank');}}}catch(err){console.error('Errore nella generazione del report:',err);setError(err.detail||err.message||'Errore durante la generazione del report');}finally{setLoading(false);}};const handleReportSelect=reportType=>{setSelectedReport(reportType);setDialogType(reportType.id);// Per report che necessitano di parametri aggiuntivi, mostra il dialog\nif(reportType.id==='posa-periodo'||reportType.id==='bobina-specifica'){// Imposta valori di default per alcuni report\nif(reportType.id==='posa-periodo'){const today=new Date();const lastMonth=new Date();lastMonth.setMonth(today.getMonth()-1);setFormData({...formData,data_inizio:lastMonth.toISOString().split('T')[0],data_fine:today.toISOString().split('T')[0]});}setOpenDialog(true);}else{// Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\ngenerateReportWithFormat(reportType.id,'video');}};const handleGenerateReport=async()=>{await generateReportWithFormat(dialogType,formData.formato);setOpenDialog(false);};const handleCloseDialog=()=>{setOpenDialog(false);setError(null);setFormData({formato:'video',data_inizio:'',data_fine:'',id_bobina:''});};const renderReportContent=()=>{if(!reportData)return null;return/*#__PURE__*/_jsxs(Paper,{sx:{p:3,mt:3},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[selectedReport===null||selectedReport===void 0?void 0:selectedReport.title,\" - \",reportData.nome_cantiere]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1},children:[/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),onClick:()=>generateReportWithFormat(dialogType,'pdf'),variant:\"outlined\",size:\"small\",color:\"primary\",children:\"PDF\"}),/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),onClick:()=>generateReportWithFormat(dialogType,'excel'),variant:\"outlined\",size:\"small\",color:\"success\",children:\"Excel\"}),/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(RefreshIcon,{}),onClick:()=>setReportData(null),variant:\"outlined\",size:\"small\",children:\"Nuovo Report\"})]})]}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),dialogType==='progress'&&renderProgressReport(reportData),dialogType==='boq'&&renderBoqReport(reportData),dialogType==='bobine'&&renderBobineReport(reportData),dialogType==='bobina-specifica'&&renderBobinaSpecificaReport(reportData),dialogType==='posa-periodo'&&renderPosaPeriodoReport(reportData),dialogType==='cavi-stato'&&renderCaviStatoReport(reportData)]});};const renderProgressReport=data=>/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'flex-end',mb:2},children:/*#__PURE__*/_jsx(FormControlLabel,{control:/*#__PURE__*/_jsx(Switch,{checked:showCharts,onChange:e=>setShowCharts(e.target.checked),color:\"primary\"}),label:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(ShowChartIcon,{sx:{mr:1}}),\"Mostra Grafici\"]})})})}),showCharts&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(ProgressChart,{data:data})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Avanzamento Generale\"})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsx(FilterableTable,{data:[{metrica:'Metri Totali',valore:`${data.metri_totali}m`,percentuale:'100%'},{metrica:'Metri Posati',valore:`${data.metri_posati}m`,percentuale:`${data.percentuale_avanzamento}%`},{metrica:'Metri Rimanenti',valore:`${data.metri_da_posare}m`,percentuale:`${100-data.percentuale_avanzamento}%`}],columns:[{field:'metrica',headerName:'Metrica',width:200},{field:'valore',headerName:'Valore',width:150,align:'right'},{field:'percentuale',headerName:'Percentuale',width:150,align:'right'}],pagination:false})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Cavi\"})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsx(FilterableTable,{data:[{metrica:'Totale Cavi',valore:data.totale_cavi,percentuale:'100%'},{metrica:'Cavi Posati',valore:data.cavi_posati,percentuale:`${data.percentuale_cavi}%`},{metrica:'Cavi Rimanenti',valore:data.cavi_rimanenti,percentuale:`${100-data.percentuale_cavi}%`}],columns:[{field:'metrica',headerName:'Metrica',width:200},{field:'valore',headerName:'Valore',width:150,align:'right'},{field:'percentuale',headerName:'Percentuale',width:150,align:'right'}],pagination:false})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Performance\"})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsx(FilterableTable,{data:[{metrica:'Media Giornaliera',valore:`${data.media_giornaliera}m/giorno`},...(data.giorni_stimati?[{metrica:'Giorni Stimati',valore:`${data.giorni_stimati} giorni`},{metrica:'Data Completamento',valore:data.data_completamento}]:[])],columns:[{field:'metrica',headerName:'Metrica',width:200},{field:'valore',headerName:'Valore',width:250,align:'right'}],pagination:false})})]})}),data.posa_recente&&data.posa_recente.length>0&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Posa Recente\"})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsx(FilterableTable,{data:data.posa_recente.map(posa=>({data:posa.data,metri:`${posa.metri}m`})),columns:[{field:'data',headerName:'Data',width:200},{field:'metri',headerName:'Metri Posati',width:150,align:'right'}],pagination:data.posa_recente.length>10})})]})})]});const renderBoqReport=data=>/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[showCharts&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(BoqChart,{data:data})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Cavi per Tipologia\"})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsx(FilterableTable,{data:data.cavi_per_tipo||[],columns:[{field:'tipologia',headerName:'Tipologia',width:150},{field:'sezione',headerName:'Sezione',width:100},{field:'num_cavi',headerName:'Cavi',width:80,align:'right',dataType:'number'},{field:'metri_teorici',headerName:'Metri Teorici',width:120,align:'right',dataType:'number',renderCell:row=>`${row.metri_teorici}m`},{field:'metri_reali',headerName:'Metri Reali',width:120,align:'right',dataType:'number',renderCell:row=>`${row.metri_reali}m`},{field:'metri_da_posare',headerName:'Da Posare',width:120,align:'right',dataType:'number',renderCell:row=>`${row.metri_da_posare}m`}]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Bobine Disponibili\"})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsx(FilterableTable,{data:data.bobine_per_tipo||[],columns:[{field:'tipologia',headerName:'Tipologia',width:150},{field:'sezione',headerName:'Sezione',width:100},{field:'num_bobine',headerName:'Bobine',width:100,align:'right',dataType:'number'},{field:'metri_disponibili',headerName:'Metri Disponibili',width:150,align:'right',dataType:'number',renderCell:row=>`${row.metri_disponibili}m`}]})})]})})]});const renderBobineReport=data=>/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[showCharts&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(BobineChart,{data:data})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[\"Bobine del Cantiere (\",data.totale_bobine,\" totali)\"]})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsx(FilterableTable,{data:data.bobine||[],columns:[{field:'id_bobina',headerName:'ID Bobina',width:120},{field:'tipologia',headerName:'Tipologia',width:150},{field:'sezione',headerName:'Sezione',width:100},{field:'stato',headerName:'Stato',width:120,renderCell:row=>/*#__PURE__*/_jsx(Chip,{label:row.stato,color:row.stato==='DISPONIBILE'?'success':'warning',size:\"small\"})},{field:'metri_totali',headerName:'Metri Totali',width:120,align:'right',dataType:'number',renderCell:row=>`${row.metri_totali}m`},{field:'metri_residui',headerName:'Metri Residui',width:120,align:'right',dataType:'number',renderCell:row=>`${row.metri_residui}m`},{field:'metri_utilizzati',headerName:'Metri Utilizzati',width:140,align:'right',dataType:'number',renderCell:row=>`${row.metri_utilizzati}m`},{field:'percentuale_utilizzo',headerName:'Utilizzo',width:100,align:'right',dataType:'number',renderCell:row=>`${row.percentuale_utilizzo}%`}]})})]})})]});const renderBobinaSpecificaReport=data=>/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Dettagli Bobina\"})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsx(FilterableTable,{data:data.bobina?[{proprietà:'ID Bobina',valore:data.bobina.id_bobina},{proprietà:'Tipologia',valore:data.bobina.tipologia},{proprietà:'Sezione',valore:data.bobina.sezione},{proprietà:'Stato',valore:data.bobina.stato,renderSpecial:true},{proprietà:'Metri Totali',valore:`${data.bobina.metri_totali}m`},{proprietà:'Metri Residui',valore:`${data.bobina.metri_residui}m`},{proprietà:'Metri Utilizzati',valore:`${data.bobina.metri_utilizzati}m`},{proprietà:'Percentuale Utilizzo',valore:`${data.bobina.percentuale_utilizzo}%`}]:[],columns:[{field:'proprietà',headerName:'Proprietà',width:200},{field:'valore',headerName:'Valore',width:250,renderCell:row=>row.renderSpecial?/*#__PURE__*/_jsx(Chip,{label:row.valore,color:row.valore==='DISPONIBILE'?'success':'warning',size:\"small\"}):row.valore}],pagination:false})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[\"Cavi Associati (\",data.totale_cavi,\")\"]})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsx(FilterableTable,{data:data.cavi_associati||[],columns:[{field:'id_cavo',headerName:'ID Cavo',width:120},{field:'sistema',headerName:'Sistema',width:120},{field:'utility',headerName:'Utility',width:120},{field:'tipologia',headerName:'Tipologia',width:150},{field:'metri_teorici',headerName:'Metri Teorici',width:120,align:'right',dataType:'number',renderCell:row=>`${row.metri_teorici}m`},{field:'metri_reali',headerName:'Metri Reali',width:120,align:'right',dataType:'number',renderCell:row=>`${row.metri_reali}m`},{field:'stato',headerName:'Stato',width:120,renderCell:row=>/*#__PURE__*/_jsx(Chip,{label:row.stato,color:row.stato==='POSATO'?'success':'warning',size:\"small\"})}]})})]})})]});const renderPosaPeriodoReport=data=>/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[showCharts&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(TimelineChart,{data:data})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Statistiche Periodo\"})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsx(FilterableTable,{data:[{statistica:'Periodo',valore:`${data.data_inizio} - ${data.data_fine}`},{statistica:'Totale Metri',valore:`${data.totale_metri_periodo}m`},{statistica:'Giorni Attivi',valore:data.giorni_attivi},{statistica:'Media Giornaliera',valore:`${data.media_giornaliera}m/giorno`}],columns:[{field:'statistica',headerName:'Statistica',width:200},{field:'valore',headerName:'Valore',width:250,align:'right'}],pagination:false})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Posa Giornaliera\"})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsx(FilterableTable,{data:data.posa_giornaliera||[],columns:[{field:'data',headerName:'Data',width:200},{field:'metri',headerName:'Metri Posati',width:150,align:'right',dataType:'number',renderCell:row=>`${row.metri}m`}]})})]})})]});const renderCaviStatoReport=data=>/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[showCharts&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(CaviStatoChart,{data:data})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Cavi per Stato di Installazione\"})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsx(FilterableTable,{data:data.cavi_per_stato||[],columns:[{field:'stato',headerName:'Stato',width:150,renderCell:row=>/*#__PURE__*/_jsx(Chip,{label:row.stato,color:row.stato==='Installato'?'success':'warning',size:\"small\"})},{field:'num_cavi',headerName:'Numero Cavi',width:120,align:'right',dataType:'number'},{field:'metri_teorici',headerName:'Metri Teorici',width:150,align:'right',dataType:'number',renderCell:row=>`${row.metri_teorici}m`},{field:'metri_reali',headerName:'Metri Reali',width:150,align:'right',dataType:'number',renderCell:row=>`${row.metri_reali}m`}],pagination:false})})]})})]});const renderDialog=()=>/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:selectedReport===null||selectedReport===void 0?void 0:selectedReport.title}),/*#__PURE__*/_jsxs(DialogContent,{children:[error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:error}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,sx:{mt:1},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,children:[/*#__PURE__*/_jsx(InputLabel,{children:\"Formato\"}),/*#__PURE__*/_jsxs(Select,{value:formData.formato,label:\"Formato\",onChange:e=>setFormData({...formData,formato:e.target.value}),children:[/*#__PURE__*/_jsx(MenuItem,{value:\"video\",children:\"Visualizza a schermo\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"pdf\",children:\"Download PDF\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"excel\",children:\"Download Excel\"})]})]})}),dialogType==='bobina-specifica'&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"ID Bobina\",value:formData.id_bobina,onChange:e=>setFormData({...formData,id_bobina:e.target.value}),placeholder:\"Es: 1, 2, A, B...\",helperText:\"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"})}),dialogType==='posa-periodo'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,type:\"date\",label:\"Data Inizio\",value:formData.data_inizio,onChange:e=>setFormData({...formData,data_inizio:e.target.value}),InputLabelProps:{shrink:true}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,type:\"date\",label:\"Data Fine\",value:formData.data_fine,onChange:e=>setFormData({...formData,data_fine:e.target.value}),InputLabelProps:{shrink:true}})})]})]})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Annulla\"}),/*#__PURE__*/_jsx(Button,{onClick:handleGenerateReport,variant:\"contained\",disabled:loading,startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(VisibilityIcon,{}),children:loading?'Generazione...':'Genera Report'})]})]});return/*#__PURE__*/_jsxs(Box,{sx:{p:3},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(IconButton,{onClick:()=>navigate(-1),color:\"primary\",children:/*#__PURE__*/_jsx(ArrowBackIcon,{})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",children:\"Report e Analytics\"})]}),/*#__PURE__*/_jsx(AdminHomeButton,{})]}),loading&&/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',my:4},children:/*#__PURE__*/_jsx(CircularProgress,{})}),/*#__PURE__*/_jsxs(Box,{sx:{mt:3},children:[/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,sx:{mb:2},children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(AssessmentIcon,{sx:{mr:1,color:'primary.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Report Avanzamento\"})]})}),/*#__PURE__*/_jsx(AccordionDetails,{children:reportsData.progress?/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(FormControlLabel,{control:/*#__PURE__*/_jsx(Switch,{checked:showCharts,onChange:e=>setShowCharts(e.target.checked),color:\"primary\"}),label:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(ShowChartIcon,{sx:{mr:1}}),\"Mostra Grafici\"]})}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),onClick:()=>generateReportWithFormat('progress','pdf'),variant:\"outlined\",size:\"small\",color:\"primary\",sx:{mr:1},children:\"PDF\"}),/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),onClick:()=>generateReportWithFormat('progress','excel'),variant:\"outlined\",size:\"small\",color:\"success\",children:\"Excel\"})]})]}),renderProgressReport(reportsData.progress)]}):loading?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',my:2},children:[/*#__PURE__*/_jsx(CircularProgress,{size:24}),/*#__PURE__*/_jsx(Typography,{sx:{ml:2},children:\"Caricamento in corso...\"})]}):/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:\"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",size:\"small\",startIcon:/*#__PURE__*/_jsx(RefreshIcon,{}),onClick:()=>{setLoading(true);reportService.getProgressReport(cantiereId,'video').then(data=>{setReportsData(prev=>({...prev,progress:data.content}));}).catch(err=>{console.error('Error retrying progress report:',err);}).finally(()=>{setLoading(false);});},children:\"Riprova\"})]})})]}),/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,sx:{mb:2},children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(ListIcon,{sx:{mr:1,color:'secondary.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Bill of Quantities\"})]})}),/*#__PURE__*/_jsx(AccordionDetails,{children:reportsData.boq?/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'flex-end',mb:2},children:[/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),onClick:()=>generateReportWithFormat('boq','pdf'),variant:\"outlined\",size:\"small\",color:\"primary\",sx:{mr:1},children:\"PDF\"}),/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),onClick:()=>generateReportWithFormat('boq','excel'),variant:\"outlined\",size:\"small\",color:\"success\",children:\"Excel\"})]}),renderBoqReport(reportsData.boq)]}):loading?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',my:2},children:[/*#__PURE__*/_jsx(CircularProgress,{size:24}),/*#__PURE__*/_jsx(Typography,{sx:{ml:2},children:\"Caricamento in corso...\"})]}):/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:\"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",size:\"small\",startIcon:/*#__PURE__*/_jsx(RefreshIcon,{}),onClick:()=>{setLoading(true);reportService.getBillOfQuantities(cantiereId,'video').then(data=>{setReportsData(prev=>({...prev,boq:data.content}));}).catch(err=>{console.error('Error retrying BOQ report:',err);}).finally(()=>{setLoading(false);});},children:\"Riprova\"})]})})]}),/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,sx:{mb:2},children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(InventoryIcon,{sx:{mr:1,color:'success.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Report Utilizzo Bobine\"})]})}),/*#__PURE__*/_jsx(AccordionDetails,{children:reportsData.bobine?/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'flex-end',mb:2},children:[/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),onClick:()=>generateReportWithFormat('bobine','pdf'),variant:\"outlined\",size:\"small\",color:\"primary\",sx:{mr:1},children:\"PDF\"}),/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),onClick:()=>generateReportWithFormat('bobine','excel'),variant:\"outlined\",size:\"small\",color:\"success\",children:\"Excel\"})]}),renderBobineReport(reportsData.bobine)]}):loading?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',my:2},children:[/*#__PURE__*/_jsx(CircularProgress,{size:24}),/*#__PURE__*/_jsx(Typography,{sx:{ml:2},children:\"Caricamento in corso...\"})]}):/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:\"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",size:\"small\",startIcon:/*#__PURE__*/_jsx(RefreshIcon,{}),onClick:()=>{setLoading(true);reportService.getBobineReport(cantiereId,'video').then(data=>{setReportsData(prev=>({...prev,bobine:data.content}));}).catch(err=>{console.error('Error retrying bobine report:',err);}).finally(()=>{setLoading(false);});},children:\"Riprova\"})]})})]}),/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,sx:{mb:2},children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(BarChartIcon,{sx:{mr:1,color:'error.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Report Cavi per Stato\"})]})}),/*#__PURE__*/_jsx(AccordionDetails,{children:reportsData.caviStato?/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'flex-end',mb:2},children:[/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),onClick:()=>generateReportWithFormat('cavi-stato','pdf'),variant:\"outlined\",size:\"small\",color:\"primary\",sx:{mr:1},children:\"PDF\"}),/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),onClick:()=>generateReportWithFormat('cavi-stato','excel'),variant:\"outlined\",size:\"small\",color:\"success\",children:\"Excel\"})]}),renderCaviStatoReport(reportsData.caviStato)]}):loading?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',my:2},children:[/*#__PURE__*/_jsx(CircularProgress,{size:24}),/*#__PURE__*/_jsx(Typography,{sx:{ml:2},children:\"Caricamento in corso...\"})]}):/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:\"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",size:\"small\",startIcon:/*#__PURE__*/_jsx(RefreshIcon,{}),onClick:()=>{setLoading(true);reportService.getCaviStatoReport(cantiereId,'video').then(data=>{setReportsData(prev=>({...prev,caviStato:data.content}));}).catch(err=>{console.error('Error retrying cavi stato report:',err);}).finally(()=>{setLoading(false);});},children:\"Riprova\"})]})})]}),/*#__PURE__*/_jsxs(Paper,{sx:{p:3,mt:4},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Report Speciali\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:2},children:\"Questi report richiedono parametri aggiuntivi per essere generati.\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Report Bobina Specifica\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:2},children:\"Dettaglio approfondito di una singola bobina con tutti i cavi associati.\"})]}),/*#__PURE__*/_jsx(CardActions,{children:/*#__PURE__*/_jsx(Button,{fullWidth:true,variant:\"outlined\",color:\"info\",onClick:()=>{setDialogType('bobina-specifica');setOpenDialog(true);},children:\"Genera Report\"})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Report Posa per Periodo\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:2},children:\"Analisi temporale della posa con trend e pattern di lavoro.\"})]}),/*#__PURE__*/_jsx(CardActions,{children:/*#__PURE__*/_jsx(Button,{fullWidth:true,variant:\"outlined\",color:\"warning\",onClick:()=>{setDialogType('posa-periodo');// Set default date range (last month to today)\nconst today=new Date();const lastMonth=new Date();lastMonth.setMonth(today.getMonth()-1);setFormData({...formData,data_inizio:lastMonth.toISOString().split('T')[0],data_fine:today.toISOString().split('T')[0]});setOpenDialog(true);},children:\"Genera Report\"})})]})})]})]}),reportsData.bobinaSpecifica&&/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,sx:{mb:2,mt:2},children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(CableIcon,{sx:{mr:1,color:'info.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Report Bobina Specifica\"})]})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'flex-end',mb:2},children:[/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),onClick:()=>generateReportWithFormat('bobina-specifica','pdf'),variant:\"outlined\",size:\"small\",color:\"primary\",sx:{mr:1},children:\"PDF\"}),/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),onClick:()=>generateReportWithFormat('bobina-specifica','excel'),variant:\"outlined\",size:\"small\",color:\"success\",children:\"Excel\"})]}),renderBobinaSpecificaReport(reportsData.bobinaSpecifica)]})})]}),reportsData.posaPeriodo&&/*#__PURE__*/_jsxs(Accordion,{defaultExpanded:true,sx:{mb:2,mt:2},children:[/*#__PURE__*/_jsx(AccordionSummary,{expandIcon:/*#__PURE__*/_jsx(ExpandMoreIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(TimelineIcon,{sx:{mr:1,color:'warning.main'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Report Posa per Periodo\"})]})}),/*#__PURE__*/_jsx(AccordionDetails,{children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'flex-end',mb:2},children:[/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),onClick:()=>generateReportWithFormat('posa-periodo','pdf'),variant:\"outlined\",size:\"small\",color:\"primary\",sx:{mr:1},children:\"PDF\"}),/*#__PURE__*/_jsx(Button,{startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),onClick:()=>generateReportWithFormat('posa-periodo','excel'),variant:\"outlined\",size:\"small\",color:\"success\",children:\"Excel\"})]}),renderPosaPeriodoReport(reportsData.posaPeriodo)]})})]})]}),renderDialog()]});};export default ReportCaviPageNew;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Switch", "FormControlLabel", "Assessment", "AssessmentIcon", "<PERSON><PERSON><PERSON>", "BarChartIcon", "<PERSON><PERSON><PERSON>", "PieChartIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "ArrowBack", "ArrowBackIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "ShowChart", "ShowChartIcon", "useNavigate", "useParams", "useAuth", "AdminHomeButton", "reportService", "FilterableTable", "ProgressChart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TimelineChart", "Cavi<PERSON>tato<PERSON>hart", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ReportCaviPageNew", "navigate", "cantiereId", "user", "loading", "setLoading", "error", "setError", "reportData", "setReportData", "selectedReport", "setSelectedReport", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportsData", "setReportsData", "progress", "boq", "bobine", "caviStato", "bobinaSpecifica", "posaPeriodo", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAllReports", "progressPromise", "getProgressReport", "catch", "err", "console", "content", "boq<PERSON><PERSON><PERSON>", "getBillOfQuantities", "bob<PERSON><PERSON><PERSON><PERSON>", "getBobineReport", "caviStatoPromise", "getCaviStatoReport", "progressData", "boqData", "bobine<PERSON><PERSON>", "caviStatoData", "Promise", "all", "reportTypes", "id", "title", "description", "icon", "color", "features", "generateReportWithFormat", "reportType", "format", "response", "getBobinaReport", "getPosaPerPeriodoReport", "Error", "prev", "file_url", "window", "open", "detail", "message", "handleReportSelect", "today", "Date", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "handleGenerateReport", "handleCloseDialog", "renderReportContent", "sx", "p", "mt", "children", "display", "justifyContent", "alignItems", "mb", "variant", "nome_cantiere", "gap", "startIcon", "onClick", "size", "renderProgressReport", "renderBoqReport", "renderBobineReport", "renderBobinaSpecificaReport", "renderPosaPeriodoReport", "renderCaviStatoReport", "data", "container", "spacing", "item", "xs", "control", "checked", "onChange", "e", "target", "label", "mr", "defaultExpanded", "expandIcon", "metrica", "valore", "metri_totali", "percentuale", "metri_posati", "percentuale_avanzamento", "metri_da_posare", "columns", "field", "headerName", "width", "align", "pagination", "totale_cavi", "cavi_posati", "percentuale_cavi", "cavi_rimanenti", "media_giornaliera", "giorni_stimati", "data_completamento", "posa_recente", "length", "map", "posa", "metri", "cavi_per_tipo", "dataType", "renderCell", "row", "metri_te<PERSON>ci", "metri_reali", "bobine_per_tipo", "metri_disponibili", "totale_bobine", "stato", "metri_residui", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "percentuale_utilizzo", "bobina", "proprietà", "tipologia", "sezione", "renderSpecial", "cavi_associati", "statistica", "totale_metri_periodo", "giorni_attivi", "posa_giornal<PERSON>", "cavi_per_stato", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "value", "placeholder", "helperText", "type", "InputLabelProps", "shrink", "disabled", "component", "my", "ml", "then", "finally", "gutterBottom", "md"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n  IconButton,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n  BarChart as BarChartIcon,\n  <PERSON><PERSON><PERSON> as PieChartIcon,\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n  ArrowBack as ArrowBackIcon,\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon,\n  ShowChart as ShowChartIcon\n} from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\n\nconst ReportCaviPageNew = () => {\n  const navigate = useNavigate();\n  const { cantiereId } = useParams();\n  const { user } = useAuth();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading progress report:', err);\n            return { content: null };\n          });\n\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading BOQ report:', err);\n            return { content: null };\n          });\n\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading bobine report:', err);\n            return { content: null };\n          });\n\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading cavi stato report:', err);\n            return { content: null };\n          });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          bobinePromise,\n          caviStatoPromise\n        ]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [\n    {\n      id: 'progress',\n      title: 'Report Avanzamento',\n      description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n      icon: <AssessmentIcon />,\n      color: 'primary',\n      features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n    },\n    {\n      id: 'boq',\n      title: 'Bill of Quantities',\n      description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n      icon: <ListIcon />,\n      color: 'secondary',\n      features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n    },\n    {\n      id: 'bobine',\n      title: 'Report Utilizzo Bobine',\n      description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n      icon: <InventoryIcon />,\n      color: 'success',\n      features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n    },\n    {\n      id: 'bobina-specifica',\n      title: 'Report Bobina Specifica',\n      description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n      icon: <CableIcon />,\n      color: 'info',\n      features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n    },\n    {\n      id: 'posa-periodo',\n      title: 'Report Posa per Periodo',\n      description: 'Analisi temporale della posa con trend e pattern di lavoro',\n      icon: <TimelineIcon />,\n      color: 'warning',\n      features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n    },\n    {\n      id: 'cavi-stato',\n      title: 'Report Cavi per Stato',\n      description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n      icon: <BarChartIcon />,\n      color: 'error',\n      features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n    }\n  ];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReportSelect = (reportType) => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n  const renderReportContent = () => {\n    if (!reportData) return null;\n\n    return (\n      <Paper sx={{ p: 3, mt: 3 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\">\n            {selectedReport?.title} - {reportData.nome_cantiere}\n          </Typography>\n          <Box sx={{ display: 'flex', gap: 1 }}>\n            {/* Export buttons */}\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'pdf')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"primary\"\n            >\n              PDF\n            </Button>\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'excel')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"success\"\n            >\n              Excel\n            </Button>\n            <Button\n              startIcon={<RefreshIcon />}\n              onClick={() => setReportData(null)}\n              variant=\"outlined\"\n              size=\"small\"\n            >\n              Nuovo Report\n            </Button>\n          </Box>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Renderizza il contenuto specifico del report */}\n        {dialogType === 'progress' && renderProgressReport(reportData)}\n        {dialogType === 'boq' && renderBoqReport(reportData)}\n        {dialogType === 'bobine' && renderBobineReport(reportData)}\n        {dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData)}\n        {dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData)}\n        {dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)}\n      </Paper>\n    );\n  };\n\n  const renderProgressReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Controllo visualizzazione grafici */}\n      <Grid item xs={12}>\n        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n          <FormControlLabel\n            control={\n              <Switch\n                checked={showCharts}\n                onChange={(e) => setShowCharts(e.target.checked)}\n                color=\"primary\"\n              />\n            }\n            label={\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <ShowChartIcon sx={{ mr: 1 }} />\n                Mostra Grafici\n              </Box>\n            }\n          />\n        </Box>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <ProgressChart data={data} />\n        </Grid>\n      )}\n\n      {/* Avanzamento Generale - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Avanzamento Generale</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={[\n                {\n                  metrica: 'Metri Totali',\n                  valore: `${data.metri_totali}m`,\n                  percentuale: '100%'\n                },\n                {\n                  metrica: 'Metri Posati',\n                  valore: `${data.metri_posati}m`,\n                  percentuale: `${data.percentuale_avanzamento}%`\n                },\n                {\n                  metrica: 'Metri Rimanenti',\n                  valore: `${data.metri_da_posare}m`,\n                  percentuale: `${100 - data.percentuale_avanzamento}%`\n                }\n              ]}\n              columns={[\n                { field: 'metrica', headerName: 'Metrica', width: 200 },\n                { field: 'valore', headerName: 'Valore', width: 150, align: 'right' },\n                { field: 'percentuale', headerName: 'Percentuale', width: 150, align: 'right' }\n              ]}\n              pagination={false}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Cavi - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={[\n                {\n                  metrica: 'Totale Cavi',\n                  valore: data.totale_cavi,\n                  percentuale: '100%'\n                },\n                {\n                  metrica: 'Cavi Posati',\n                  valore: data.cavi_posati,\n                  percentuale: `${data.percentuale_cavi}%`\n                },\n                {\n                  metrica: 'Cavi Rimanenti',\n                  valore: data.cavi_rimanenti,\n                  percentuale: `${100 - data.percentuale_cavi}%`\n                }\n              ]}\n              columns={[\n                { field: 'metrica', headerName: 'Metrica', width: 200 },\n                { field: 'valore', headerName: 'Valore', width: 150, align: 'right' },\n                { field: 'percentuale', headerName: 'Percentuale', width: 150, align: 'right' }\n              ]}\n              pagination={false}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Performance - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Performance</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={[\n                {\n                  metrica: 'Media Giornaliera',\n                  valore: `${data.media_giornaliera}m/giorno`\n                },\n                ...(data.giorni_stimati ? [\n                  {\n                    metrica: 'Giorni Stimati',\n                    valore: `${data.giorni_stimati} giorni`\n                  },\n                  {\n                    metrica: 'Data Completamento',\n                    valore: data.data_completamento\n                  }\n                ] : [])\n              ]}\n              columns={[\n                { field: 'metrica', headerName: 'Metrica', width: 200 },\n                { field: 'valore', headerName: 'Valore', width: 250, align: 'right' }\n              ]}\n              pagination={false}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Posa Recente - Tabella invece di elenco */}\n      {data.posa_recente && data.posa_recente.length > 0 && (\n        <Grid item xs={12}>\n          <Accordion defaultExpanded>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Typography variant=\"h6\">Posa Recente</Typography>\n            </AccordionSummary>\n            <AccordionDetails>\n              <FilterableTable\n                data={data.posa_recente.map(posa => ({\n                  data: posa.data,\n                  metri: `${posa.metri}m`\n                }))}\n                columns={[\n                  { field: 'data', headerName: 'Data', width: 200 },\n                  { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }\n                ]}\n                pagination={data.posa_recente.length > 10}\n              />\n            </AccordionDetails>\n          </Accordion>\n        </Grid>\n      )}\n    </Grid>\n  );\n\n  const renderBoqReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <BoqChart data={data} />\n        </Grid>\n      )}\n\n      {/* Cavi per Tipologia - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi per Tipologia</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.cavi_per_tipo || []}\n              columns={[\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'sezione', headerName: 'Sezione', width: 100 },\n                { field: 'num_cavi', headerName: 'Cavi', width: 80, align: 'right', dataType: 'number' },\n                { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_teorici}m` },\n                { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_reali}m` },\n                { field: 'metri_da_posare', headerName: 'Da Posare', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_da_posare}m` }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Bobine Disponibili - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Bobine Disponibili</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.bobine_per_tipo || []}\n              columns={[\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'sezione', headerName: 'Sezione', width: 100 },\n                { field: 'num_bobine', headerName: 'Bobine', width: 100, align: 'right', dataType: 'number' },\n                { field: 'metri_disponibili', headerName: 'Metri Disponibili', width: 150, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_disponibili}m` }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobineReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <BobineChart data={data} />\n        </Grid>\n      )}\n\n      {/* Bobine del Cantiere - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">\n              Bobine del Cantiere ({data.totale_bobine} totali)\n            </Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.bobine || []}\n              columns={[\n                { field: 'id_bobina', headerName: 'ID Bobina', width: 120 },\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'sezione', headerName: 'Sezione', width: 100 },\n                { field: 'stato', headerName: 'Stato', width: 120,\n                  renderCell: (row) => (\n                    <Chip\n                      label={row.stato}\n                      color={row.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  )\n                },\n                { field: 'metri_totali', headerName: 'Metri Totali', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_totali}m` },\n                { field: 'metri_residui', headerName: 'Metri Residui', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_residui}m` },\n                { field: 'metri_utilizzati', headerName: 'Metri Utilizzati', width: 140, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_utilizzati}m` },\n                { field: 'percentuale_utilizzo', headerName: 'Utilizzo', width: 100, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.percentuale_utilizzo}%` }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobinaSpecificaReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Dettagli Bobina - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Dettagli Bobina</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.bobina ? [\n                {\n                  proprietà: 'ID Bobina',\n                  valore: data.bobina.id_bobina\n                },\n                {\n                  proprietà: 'Tipologia',\n                  valore: data.bobina.tipologia\n                },\n                {\n                  proprietà: 'Sezione',\n                  valore: data.bobina.sezione\n                },\n                {\n                  proprietà: 'Stato',\n                  valore: data.bobina.stato,\n                  renderSpecial: true\n                },\n                {\n                  proprietà: 'Metri Totali',\n                  valore: `${data.bobina.metri_totali}m`\n                },\n                {\n                  proprietà: 'Metri Residui',\n                  valore: `${data.bobina.metri_residui}m`\n                },\n                {\n                  proprietà: 'Metri Utilizzati',\n                  valore: `${data.bobina.metri_utilizzati}m`\n                },\n                {\n                  proprietà: 'Percentuale Utilizzo',\n                  valore: `${data.bobina.percentuale_utilizzo}%`\n                }\n              ] : []}\n              columns={[\n                { field: 'proprietà', headerName: 'Proprietà', width: 200 },\n                { field: 'valore', headerName: 'Valore', width: 250, \n                  renderCell: (row) => row.renderSpecial ? (\n                    <Chip\n                      label={row.valore}\n                      color={row.valore === 'DISPONIBILE' ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  ) : row.valore\n                }\n              ]}\n              pagination={false}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Cavi Associati - Tabella invece di elenco */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">\n              Cavi Associati ({data.totale_cavi})\n            </Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.cavi_associati || []}\n              columns={[\n                { field: 'id_cavo', headerName: 'ID Cavo', width: 120 },\n                { field: 'sistema', headerName: 'Sistema', width: 120 },\n                { field: 'utility', headerName: 'Utility', width: 120 },\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_teorici}m` },\n                { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_reali}m` },\n                { field: 'stato', headerName: 'Stato', width: 120,\n                  renderCell: (row) => (\n                    <Chip\n                      label={row.stato}\n                      color={row.stato === 'POSATO' ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  )\n                }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderPosaPeriodoReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <TimelineChart data={data} />\n        </Grid>\n      )}\n\n      {/* Statistiche Periodo - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Statistiche Periodo</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={[\n                {\n                  statistica: 'Periodo',\n                  valore: `${data.data_inizio} - ${data.data_fine}`\n                },\n                {\n                  statistica: 'Totale Metri',\n                  valore: `${data.totale_metri_periodo}m`\n                },\n                {\n                  statistica: 'Giorni Attivi',\n                  valore: data.giorni_attivi\n                },\n                {\n                  statistica: 'Media Giornaliera',\n                  valore: `${data.media_giornaliera}m/giorno`\n                }\n              ]}\n              columns={[\n                { field: 'statistica', headerName: 'Statistica', width: 200 },\n                { field: 'valore', headerName: 'Valore', width: 250, align: 'right' }\n              ]}\n              pagination={false}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n\n      {/* Posa Giornaliera - Tabella invece di elenco */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Posa Giornaliera</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.posa_giornaliera || []}\n              columns={[\n                { field: 'data', headerName: 'Data', width: 200 },\n                { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri}m` }\n              ]}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderCaviStatoReport = (data) => (\n    <Grid container spacing={3}>\n      {/* Grafici */}\n      {showCharts && (\n        <Grid item xs={12}>\n          <CaviStatoChart data={data} />\n        </Grid>\n      )}\n\n      {/* Cavi per Stato di Installazione - Tabella invece di card */}\n      <Grid item xs={12}>\n        <Accordion defaultExpanded>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Typography variant=\"h6\">Cavi per Stato di Installazione</Typography>\n          </AccordionSummary>\n          <AccordionDetails>\n            <FilterableTable\n              data={data.cavi_per_stato || []}\n              columns={[\n                { field: 'stato', headerName: 'Stato', width: 150,\n                  renderCell: (row) => (\n                    <Chip\n                      label={row.stato}\n                      color={row.stato === 'Installato' ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  )\n                },\n                { field: 'num_cavi', headerName: 'Numero Cavi', width: 120, align: 'right', dataType: 'number' },\n                { field: 'metri_teorici', headerName: 'Metri Teorici', width: 150, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_teorici}m` },\n                { field: 'metri_reali', headerName: 'Metri Reali', width: 150, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_reali}m` }\n              ]}\n              pagination={false}\n            />\n          </AccordionDetails>\n        </Accordion>\n      </Grid>\n    </Grid>\n  );\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {selectedReport?.title}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n          {dialogType === 'bobina-specifica' && (\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"ID Bobina\"\n                value={formData.id_bobina}\n                onChange={(e) => setFormData({ ...formData, id_bobina: e.target.value })}\n                placeholder=\"Es: 1, 2, A, B...\"\n                helperText=\"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n              />\n            </Grid>\n          )}\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <IconButton onClick={() => navigate(-1)} color=\"primary\">\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\" component=\"h1\">\n            Report e Analytics\n          </Typography>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Loading indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Reports */}\n      <Box sx={{ mt: 3 }}>\n        {/* Progress Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <AssessmentIcon sx={{ mr: 1, color: 'primary.main' }} />\n              <Typography variant=\"h6\">Report Avanzamento</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.progress ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n                  <FormControlLabel\n                    control={\n                      <Switch\n                        checked={showCharts}\n                        onChange={(e) => setShowCharts(e.target.checked)}\n                        color=\"primary\"\n                      />\n                    }\n                    label={\n                      <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                        <ShowChartIcon sx={{ mr: 1 }} />\n                        Mostra Grafici\n                      </Box>\n                    }\n                  />\n                  <Box>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                </Box>\n                {renderProgressReport(reportsData.progress)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getProgressReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          progress: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying progress report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Bill of Quantities */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ListIcon sx={{ mr: 1, color: 'secondary.main' }} />\n              <Typography variant=\"h6\">Bill of Quantities</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.boq ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('boq', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('boq', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBoqReport(reportsData.boq)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getBillOfQuantities(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          boq: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying BOQ report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Bobine Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <InventoryIcon sx={{ mr: 1, color: 'success.main' }} />\n              <Typography variant=\"h6\">Report Utilizzo Bobine</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.bobine ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobine', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobine', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBobineReport(reportsData.bobine)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getBobineReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          bobine: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying bobine report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Cavi Stato Report */}\n        <Accordion defaultExpanded sx={{ mb: 2 }}>\n          <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <BarChartIcon sx={{ mr: 1, color: 'error.main' }} />\n              <Typography variant=\"h6\">Report Cavi per Stato</Typography>\n            </Box>\n          </AccordionSummary>\n          <AccordionDetails>\n            {reportsData.caviStato ? (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('cavi-stato', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('cavi-stato', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderCaviStatoReport(reportsData.caviStato)}\n              </Box>\n            ) : loading ? (\n              <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>\n                <CircularProgress size={24} />\n                <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n              </Box>\n            ) : (\n              <Box>\n                <Alert severity=\"error\" sx={{ mb: 2 }}>\n                  Impossibile caricare il report. Riprova più tardi.\n                </Alert>\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<RefreshIcon />}\n                  onClick={() => {\n                    setLoading(true);\n                    reportService.getCaviStatoReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          caviStato: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying cavi stato report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                >\n                  Riprova\n                </Button>\n              </Box>\n            )}\n          </AccordionDetails>\n        </Accordion>\n\n        {/* Special Reports Section */}\n        <Paper sx={{ p: 3, mt: 4 }}>\n          <Typography variant=\"h6\" gutterBottom>Report Speciali</Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n            Questi report richiedono parametri aggiuntivi per essere generati.\n          </Typography>\n\n          <Grid container spacing={3}>\n            {/* Bobina Specifica Report */}\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\">Report Bobina Specifica</Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    Dettaglio approfondito di una singola bobina con tutti i cavi associati.\n                  </Typography>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    fullWidth\n                    variant=\"outlined\"\n                    color=\"info\"\n                    onClick={() => {\n                      setDialogType('bobina-specifica');\n                      setOpenDialog(true);\n                    }}\n                  >\n                    Genera Report\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n\n            {/* Posa per Periodo Report */}\n            <Grid item xs={12} md={6}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\">Report Posa per Periodo</Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    Analisi temporale della posa con trend e pattern di lavoro.\n                  </Typography>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    fullWidth\n                    variant=\"outlined\"\n                    color=\"warning\"\n                    onClick={() => {\n                      setDialogType('posa-periodo');\n                      // Set default date range (last month to today)\n                      const today = new Date();\n                      const lastMonth = new Date();\n                      lastMonth.setMonth(today.getMonth() - 1);\n\n                      setFormData({\n                        ...formData,\n                        data_inizio: lastMonth.toISOString().split('T')[0],\n                        data_fine: today.toISOString().split('T')[0]\n                      });\n                      setOpenDialog(true);\n                    }}\n                  >\n                    Genera Report\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Display special reports if they exist */}\n        {reportsData.bobinaSpecifica && (\n          <Accordion defaultExpanded sx={{ mb: 2, mt: 2 }}>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <CableIcon sx={{ mr: 1, color: 'info.main' }} />\n                <Typography variant=\"h6\">Report Bobina Specifica</Typography>\n              </Box>\n            </AccordionSummary>\n            <AccordionDetails>\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobina-specifica', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('bobina-specifica', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderBobinaSpecificaReport(reportsData.bobinaSpecifica)}\n              </Box>\n            </AccordionDetails>\n          </Accordion>\n        )}\n\n        {reportsData.posaPeriodo && (\n          <Accordion defaultExpanded sx={{ mb: 2, mt: 2 }}>\n            <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <TimelineIcon sx={{ mr: 1, color: 'warning.main' }} />\n                <Typography variant=\"h6\">Report Posa per Periodo</Typography>\n              </Box>\n            </AccordionSummary>\n            <AccordionDetails>\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"primary\"\n                    sx={{ mr: 1 }}\n                  >\n                    PDF\n                  </Button>\n                  <Button\n                    startIcon={<DownloadIcon />}\n                    onClick={() => generateReportWithFormat('posa-periodo', 'excel')}\n                    variant=\"outlined\"\n                    size=\"small\"\n                    color=\"success\"\n                  >\n                    Excel\n                  </Button>\n                </Box>\n                {renderPosaPeriodoReport(reportsData.posaPeriodo)}\n              </Box>\n            </AccordionDetails>\n          </Accordion>\n        )}\n      </Box>\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,MAAM,CACNC,IAAI,CACJC,KAAK,CACLC,gBAAgB,CAChBC,OAAO,CACPC,UAAU,CACVC,OAAO,CACPC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,CACRC,SAAS,CACTC,SAAS,CACTC,gBAAgB,CAChBC,gBAAgB,CAChBC,MAAM,CACNC,gBAAgB,KACX,eAAe,CACtB,OACEC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,OAAO,GAAI,CAAAC,WAAW,CACtBC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,KAAK,GAAI,CAAAC,SAAS,CAClBC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,SAAS,GAAI,CAAAC,aAAa,KACrB,qBAAqB,CAC5B,OAASC,WAAW,CAAEC,SAAS,KAAQ,kBAAkB,CACzD,OAASC,OAAO,KAAQ,2BAA2B,CACnD,MAAO,CAAAC,eAAe,KAAM,yCAAyC,CACrE,MAAO,CAAAC,aAAa,KAAM,8BAA8B,CACxD,MAAO,CAAAC,eAAe,KAAM,yCAAyC,CAErE;AACA,MAAO,CAAAC,aAAa,KAAM,uCAAuC,CACjE,MAAO,CAAAC,WAAW,KAAM,qCAAqC,CAC7D,MAAO,CAAAC,QAAQ,KAAM,kCAAkC,CACvD,MAAO,CAAAC,aAAa,KAAM,uCAAuC,CACjE,MAAO,CAAAC,cAAc,KAAM,wCAAwC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEpE,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAC,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEmB,UAAW,CAAC,CAAGlB,SAAS,CAAC,CAAC,CAClC,KAAM,CAAEmB,IAAK,CAAC,CAAGlB,OAAO,CAAC,CAAC,CAE1B,KAAM,CAACmB,OAAO,CAAEC,UAAU,CAAC,CAAGhF,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACiF,KAAK,CAAEC,QAAQ,CAAC,CAAGlF,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACmF,UAAU,CAAEC,aAAa,CAAC,CAAGpF,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACqF,cAAc,CAAEC,iBAAiB,CAAC,CAAGtF,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACuF,UAAU,CAAEC,aAAa,CAAC,CAAGxF,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACyF,UAAU,CAAEC,aAAa,CAAC,CAAG1F,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC2F,QAAQ,CAAEC,WAAW,CAAC,CAAG5F,QAAQ,CAAC,CACvC6F,OAAO,CAAE,OAAO,CAChBC,WAAW,CAAE,EAAE,CACfC,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,EACb,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGlG,QAAQ,CAAC,CAC7CmG,QAAQ,CAAE,IAAI,CACdC,GAAG,CAAE,IAAI,CACTC,MAAM,CAAE,IAAI,CACZC,SAAS,CAAE,IAAI,CACfC,eAAe,CAAE,IAAI,CACrBC,WAAW,CAAE,IACf,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG1G,QAAQ,CAAC,IAAI,CAAC,CAElD;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAA0G,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjC3B,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACA,KAAM,CAAA4B,eAAe,CAAG9C,aAAa,CAAC+C,iBAAiB,CAAChC,UAAU,CAAE,OAAO,CAAC,CACzEiC,KAAK,CAACC,GAAG,EAAI,CACZC,OAAO,CAAC/B,KAAK,CAAC,gCAAgC,CAAE8B,GAAG,CAAC,CACpD,MAAO,CAAEE,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAC,CAAC,CAEJ,KAAM,CAAAC,UAAU,CAAGpD,aAAa,CAACqD,mBAAmB,CAACtC,UAAU,CAAE,OAAO,CAAC,CACtEiC,KAAK,CAACC,GAAG,EAAI,CACZC,OAAO,CAAC/B,KAAK,CAAC,2BAA2B,CAAE8B,GAAG,CAAC,CAC/C,MAAO,CAAEE,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAC,CAAC,CAEJ,KAAM,CAAAG,aAAa,CAAGtD,aAAa,CAACuD,eAAe,CAACxC,UAAU,CAAE,OAAO,CAAC,CACrEiC,KAAK,CAACC,GAAG,EAAI,CACZC,OAAO,CAAC/B,KAAK,CAAC,8BAA8B,CAAE8B,GAAG,CAAC,CAClD,MAAO,CAAEE,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAC,CAAC,CAEJ,KAAM,CAAAK,gBAAgB,CAAGxD,aAAa,CAACyD,kBAAkB,CAAC1C,UAAU,CAAE,OAAO,CAAC,CAC3EiC,KAAK,CAACC,GAAG,EAAI,CACZC,OAAO,CAAC/B,KAAK,CAAC,kCAAkC,CAAE8B,GAAG,CAAC,CACtD,MAAO,CAAEE,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAC,CAAC,CAEJ;AACA,KAAM,CAACO,YAAY,CAAEC,OAAO,CAAEC,UAAU,CAAEC,aAAa,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CAC3EjB,eAAe,CACfM,UAAU,CACVE,aAAa,CACbE,gBAAgB,CACjB,CAAC,CAEF;AACApB,cAAc,CAAC,CACbC,QAAQ,CAAEqB,YAAY,CAACP,OAAO,CAC9Bb,GAAG,CAAEqB,OAAO,CAACR,OAAO,CACpBZ,MAAM,CAAEqB,UAAU,CAACT,OAAO,CAC1BX,SAAS,CAAEqB,aAAa,CAACV,OAAO,CAChCV,eAAe,CAAE,IAAI,CACrBC,WAAW,CAAE,IACf,CAAC,CAAC,CAEF;AACA,GAAIgB,YAAY,CAACP,OAAO,EAAIQ,OAAO,CAACR,OAAO,EAAIS,UAAU,CAACT,OAAO,EAAIU,aAAa,CAACV,OAAO,CAAE,CAC1F/B,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAC,IAAM,CACLA,QAAQ,CAAC,uDAAuD,CAAC,CACnE,CACF,CAAE,MAAO6B,GAAG,CAAE,CACZ;AACAC,OAAO,CAAC/B,KAAK,CAAC,mCAAmC,CAAE8B,GAAG,CAAC,CACvD7B,QAAQ,CAAC,uDAAuD,CAAC,CACnE,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAIH,UAAU,CAAE,CACd8B,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAAC9B,UAAU,CAAC,CAAC,CAEhB;AACA,KAAM,CAAAiD,WAAW,CAAG,CAClB,CACEC,EAAE,CAAE,UAAU,CACdC,KAAK,CAAE,oBAAoB,CAC3BC,WAAW,CAAE,2FAA2F,CACxGC,IAAI,cAAE5D,IAAA,CAACvC,cAAc,GAAE,CAAC,CACxBoG,KAAK,CAAE,SAAS,CAChBC,QAAQ,CAAE,CAAC,yBAAyB,CAAE,2BAA2B,CAAE,qBAAqB,CAAE,yBAAyB,CACrH,CAAC,CACD,CACEL,EAAE,CAAE,KAAK,CACTC,KAAK,CAAE,oBAAoB,CAC3BC,WAAW,CAAE,wEAAwE,CACrFC,IAAI,cAAE5D,IAAA,CAAC/B,QAAQ,GAAE,CAAC,CAClB4F,KAAK,CAAE,WAAW,CAClBC,QAAQ,CAAE,CAAC,yBAAyB,CAAE,0BAA0B,CAAE,qBAAqB,CAAE,eAAe,CAC1G,CAAC,CACD,CACEL,EAAE,CAAE,QAAQ,CACZC,KAAK,CAAE,wBAAwB,CAC/BC,WAAW,CAAE,uEAAuE,CACpFC,IAAI,cAAE5D,IAAA,CAACjB,aAAa,GAAE,CAAC,CACvB8E,KAAK,CAAE,SAAS,CAChBC,QAAQ,CAAE,CAAC,qBAAqB,CAAE,sBAAsB,CAAE,oBAAoB,CAAE,iBAAiB,CACnG,CAAC,CACD,CACEL,EAAE,CAAE,kBAAkB,CACtBC,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CAAE,yEAAyE,CACtFC,IAAI,cAAE5D,IAAA,CAACnB,SAAS,GAAE,CAAC,CACnBgF,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,CAAC,kBAAkB,CAAE,gBAAgB,CAAE,oBAAoB,CAAE,oBAAoB,CAC7F,CAAC,CACD,CACEL,EAAE,CAAE,cAAc,CAClBC,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CAAE,4DAA4D,CACzEC,IAAI,cAAE5D,IAAA,CAACjC,YAAY,GAAE,CAAC,CACtB8F,KAAK,CAAE,SAAS,CAChBC,QAAQ,CAAE,CAAC,iBAAiB,CAAE,wBAAwB,CAAE,oBAAoB,CAAE,mBAAmB,CACnG,CAAC,CACD,CACEL,EAAE,CAAE,YAAY,CAChBC,KAAK,CAAE,uBAAuB,CAC9BC,WAAW,CAAE,iFAAiF,CAC9FC,IAAI,cAAE5D,IAAA,CAACrC,YAAY,GAAE,CAAC,CACtBkG,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAE,CAAC,gBAAgB,CAAE,2BAA2B,CAAE,eAAe,CAAE,kBAAkB,CAC/F,CAAC,CACF,CAED;AACA,KAAM,CAAAC,wBAAwB,CAAG,KAAAA,CAAOC,UAAU,CAAEC,MAAM,GAAK,CAC7D,GAAI,CACFvD,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CAAAsD,QAAQ,CAEZ,OAAQF,UAAU,EAChB,IAAK,UAAU,CACbE,QAAQ,CAAG,KAAM,CAAA1E,aAAa,CAAC+C,iBAAiB,CAAChC,UAAU,CAAE0D,MAAM,CAAC,CACpE,MACF,IAAK,KAAK,CACRC,QAAQ,CAAG,KAAM,CAAA1E,aAAa,CAACqD,mBAAmB,CAACtC,UAAU,CAAE0D,MAAM,CAAC,CACtE,MACF,IAAK,QAAQ,CACXC,QAAQ,CAAG,KAAM,CAAA1E,aAAa,CAACuD,eAAe,CAACxC,UAAU,CAAE0D,MAAM,CAAC,CAClE,MACF,IAAK,YAAY,CACfC,QAAQ,CAAG,KAAM,CAAA1E,aAAa,CAACyD,kBAAkB,CAAC1C,UAAU,CAAE0D,MAAM,CAAC,CACrE,MACF,IAAK,kBAAkB,CACrB,GAAI,CAAC5C,QAAQ,CAACK,SAAS,CAAE,CACvBd,QAAQ,CAAC,8BAA8B,CAAC,CACxC,OACF,CACAsD,QAAQ,CAAG,KAAM,CAAA1E,aAAa,CAAC2E,eAAe,CAAC5D,UAAU,CAAEc,QAAQ,CAACK,SAAS,CAAEuC,MAAM,CAAC,CACtF,MACF,IAAK,cAAc,CACjB,GAAI,CAAC5C,QAAQ,CAACG,WAAW,EAAI,CAACH,QAAQ,CAACI,SAAS,CAAE,CAChDb,QAAQ,CAAC,4CAA4C,CAAC,CACtD,OACF,CACAsD,QAAQ,CAAG,KAAM,CAAA1E,aAAa,CAAC4E,uBAAuB,CACpD7D,UAAU,CACVc,QAAQ,CAACG,WAAW,CACpBH,QAAQ,CAACI,SAAS,CAClBwC,MACF,CAAC,CACD,MACF,QACE,KAAM,IAAI,CAAAI,KAAK,CAAC,iCAAiC,CAAC,CACtD,CAEA,GAAIJ,MAAM,GAAK,OAAO,CAAE,CACtB;AACA,GAAID,UAAU,GAAK,kBAAkB,EAAIA,UAAU,GAAK,cAAc,CAAE,CACtEpC,cAAc,CAAC0C,IAAI,GAAK,CACtB,GAAGA,IAAI,CACP,CAACN,UAAU,GAAK,kBAAkB,CAAG,iBAAiB,CAAG,aAAa,EAAGE,QAAQ,CAACvB,OACpF,CAAC,CAAC,CAAC,CACL,CACA7B,aAAa,CAACoD,QAAQ,CAACvB,OAAO,CAAC,CACjC,CAAC,IAAM,CACL;AACA,GAAIuB,QAAQ,CAACK,QAAQ,CAAE,CACrBC,MAAM,CAACC,IAAI,CAACP,QAAQ,CAACK,QAAQ,CAAE,QAAQ,CAAC,CAC1C,CACF,CACF,CAAE,MAAO9B,GAAG,CAAE,CACZC,OAAO,CAAC/B,KAAK,CAAC,sCAAsC,CAAE8B,GAAG,CAAC,CAC1D7B,QAAQ,CAAC6B,GAAG,CAACiC,MAAM,EAAIjC,GAAG,CAACkC,OAAO,EAAI,0CAA0C,CAAC,CACnF,CAAC,OAAS,CACRjE,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAkE,kBAAkB,CAAIZ,UAAU,EAAK,CACzChD,iBAAiB,CAACgD,UAAU,CAAC,CAC7B5C,aAAa,CAAC4C,UAAU,CAACP,EAAE,CAAC,CAE5B;AACA,GAAIO,UAAU,CAACP,EAAE,GAAK,cAAc,EAAIO,UAAU,CAACP,EAAE,GAAK,kBAAkB,CAAE,CAC5E;AACA,GAAIO,UAAU,CAACP,EAAE,GAAK,cAAc,CAAE,CACpC,KAAM,CAAAoB,KAAK,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACxB,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAD,IAAI,CAAC,CAAC,CAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAExC3D,WAAW,CAAC,CACV,GAAGD,QAAQ,CACXG,WAAW,CAAEuD,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAClD1D,SAAS,CAAEoD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAC7C,CAAC,CAAC,CACJ,CAEAjE,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACL;AACA6C,wBAAwB,CAACC,UAAU,CAACP,EAAE,CAAE,OAAO,CAAC,CAClD,CACF,CAAC,CAED,KAAM,CAAA2B,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,KAAM,CAAArB,wBAAwB,CAAC5C,UAAU,CAAEE,QAAQ,CAACE,OAAO,CAAC,CAC5DL,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,CAED,KAAM,CAAAmE,iBAAiB,CAAGA,CAAA,GAAM,CAC9BnE,aAAa,CAAC,KAAK,CAAC,CACpBN,QAAQ,CAAC,IAAI,CAAC,CACdU,WAAW,CAAC,CACVC,OAAO,CAAE,OAAO,CAChBC,WAAW,CAAE,EAAE,CACfC,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,EACb,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAA4D,mBAAmB,CAAGA,CAAA,GAAM,CAChC,GAAI,CAACzE,UAAU,CAAE,MAAO,KAAI,CAE5B,mBACEX,KAAA,CAACpE,KAAK,EAACyJ,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eACzBxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACzFxF,KAAA,CAACrE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,EACrB3E,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE2C,KAAK,CAAC,KAAG,CAAC7C,UAAU,CAACmF,aAAa,EACzC,CAAC,cACb9F,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEM,GAAG,CAAE,CAAE,CAAE,CAAAP,QAAA,eAEnC1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAAC7B,YAAY,GAAE,CAAE,CAC5BgI,OAAO,CAAEA,CAAA,GAAMpC,wBAAwB,CAAC5C,UAAU,CAAE,KAAK,CAAE,CAC3D4E,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZvC,KAAK,CAAC,SAAS,CAAA6B,QAAA,CAChB,KAED,CAAQ,CAAC,cACT1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAAC7B,YAAY,GAAE,CAAE,CAC5BgI,OAAO,CAAEA,CAAA,GAAMpC,wBAAwB,CAAC5C,UAAU,CAAE,OAAO,CAAE,CAC7D4E,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZvC,KAAK,CAAC,SAAS,CAAA6B,QAAA,CAChB,OAED,CAAQ,CAAC,cACT1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAACzB,WAAW,GAAE,CAAE,CAC3B4H,OAAO,CAAEA,CAAA,GAAMrF,aAAa,CAAC,IAAI,CAAE,CACnCiF,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CAAAV,QAAA,CACb,cAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAEN1F,IAAA,CAACzD,OAAO,EAACgJ,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAGzB3E,UAAU,GAAK,UAAU,EAAIkF,oBAAoB,CAACxF,UAAU,CAAC,CAC7DM,UAAU,GAAK,KAAK,EAAImF,eAAe,CAACzF,UAAU,CAAC,CACnDM,UAAU,GAAK,QAAQ,EAAIoF,kBAAkB,CAAC1F,UAAU,CAAC,CACzDM,UAAU,GAAK,kBAAkB,EAAIqF,2BAA2B,CAAC3F,UAAU,CAAC,CAC5EM,UAAU,GAAK,cAAc,EAAIsF,uBAAuB,CAAC5F,UAAU,CAAC,CACpEM,UAAU,GAAK,YAAY,EAAIuF,qBAAqB,CAAC7F,UAAU,CAAC,EAC5D,CAAC,CAEZ,CAAC,CAED,KAAM,CAAAwF,oBAAoB,CAAIM,IAAI,eAChCzG,KAAA,CAACnE,IAAI,EAAC6K,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAnB,QAAA,eAEzB1F,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChB1F,IAAA,CAACpE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,UAAU,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cAC9D1F,IAAA,CAACzC,gBAAgB,EACfyJ,OAAO,cACLhH,IAAA,CAAC1C,MAAM,EACL2J,OAAO,CAAE9E,UAAW,CACpB+E,QAAQ,CAAGC,CAAC,EAAK/E,aAAa,CAAC+E,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE,CACjDpD,KAAK,CAAC,SAAS,CAChB,CACF,CACDwD,KAAK,cACHnH,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eACjD1F,IAAA,CAACb,aAAa,EAACoG,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,iBAElC,EAAK,CACN,CACF,CAAC,CACC,CAAC,CACF,CAAC,CAGNnF,UAAU,eACTnC,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChB1F,IAAA,CAACN,aAAa,EAACiH,IAAI,CAAEA,IAAK,CAAE,CAAC,CACzB,CACP,cAGD3G,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChBxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAA7B,QAAA,eACxB1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/C1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,sBAAoB,CAAY,CAAC,CAC1C,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,cACf1F,IAAA,CAACP,eAAe,EACdkH,IAAI,CAAE,CACJ,CACEc,OAAO,CAAE,cAAc,CACvBC,MAAM,CAAE,GAAGf,IAAI,CAACgB,YAAY,GAAG,CAC/BC,WAAW,CAAE,MACf,CAAC,CACD,CACEH,OAAO,CAAE,cAAc,CACvBC,MAAM,CAAE,GAAGf,IAAI,CAACkB,YAAY,GAAG,CAC/BD,WAAW,CAAE,GAAGjB,IAAI,CAACmB,uBAAuB,GAC9C,CAAC,CACD,CACEL,OAAO,CAAE,iBAAiB,CAC1BC,MAAM,CAAE,GAAGf,IAAI,CAACoB,eAAe,GAAG,CAClCH,WAAW,CAAE,GAAG,GAAG,CAAGjB,IAAI,CAACmB,uBAAuB,GACpD,CAAC,CACD,CACFE,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,SAAS,CAAEC,UAAU,CAAE,SAAS,CAAEC,KAAK,CAAE,GAAI,CAAC,CACvD,CAAEF,KAAK,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAQ,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAQ,CAAC,CACrE,CAAEH,KAAK,CAAE,aAAa,CAAEC,UAAU,CAAE,aAAa,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAQ,CAAC,CAC/E,CACFC,UAAU,CAAE,KAAM,CACnB,CAAC,CACc,CAAC,EACV,CAAC,CACR,CAAC,cAGPrI,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChBxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAA7B,QAAA,eACxB1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/C1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,MAAI,CAAY,CAAC,CAC1B,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,cACf1F,IAAA,CAACP,eAAe,EACdkH,IAAI,CAAE,CACJ,CACEc,OAAO,CAAE,aAAa,CACtBC,MAAM,CAAEf,IAAI,CAAC2B,WAAW,CACxBV,WAAW,CAAE,MACf,CAAC,CACD,CACEH,OAAO,CAAE,aAAa,CACtBC,MAAM,CAAEf,IAAI,CAAC4B,WAAW,CACxBX,WAAW,CAAE,GAAGjB,IAAI,CAAC6B,gBAAgB,GACvC,CAAC,CACD,CACEf,OAAO,CAAE,gBAAgB,CACzBC,MAAM,CAAEf,IAAI,CAAC8B,cAAc,CAC3Bb,WAAW,CAAE,GAAG,GAAG,CAAGjB,IAAI,CAAC6B,gBAAgB,GAC7C,CAAC,CACD,CACFR,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,SAAS,CAAEC,UAAU,CAAE,SAAS,CAAEC,KAAK,CAAE,GAAI,CAAC,CACvD,CAAEF,KAAK,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAQ,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAQ,CAAC,CACrE,CAAEH,KAAK,CAAE,aAAa,CAAEC,UAAU,CAAE,aAAa,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAQ,CAAC,CAC/E,CACFC,UAAU,CAAE,KAAM,CACnB,CAAC,CACc,CAAC,EACV,CAAC,CACR,CAAC,cAGPrI,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChBxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAA7B,QAAA,eACxB1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/C1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,aAAW,CAAY,CAAC,CACjC,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,cACf1F,IAAA,CAACP,eAAe,EACdkH,IAAI,CAAE,CACJ,CACEc,OAAO,CAAE,mBAAmB,CAC5BC,MAAM,CAAE,GAAGf,IAAI,CAAC+B,iBAAiB,UACnC,CAAC,CACD,IAAI/B,IAAI,CAACgC,cAAc,CAAG,CACxB,CACElB,OAAO,CAAE,gBAAgB,CACzBC,MAAM,CAAE,GAAGf,IAAI,CAACgC,cAAc,SAChC,CAAC,CACD,CACElB,OAAO,CAAE,oBAAoB,CAC7BC,MAAM,CAAEf,IAAI,CAACiC,kBACf,CAAC,CACF,CAAG,EAAE,CAAC,CACP,CACFZ,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,SAAS,CAAEC,UAAU,CAAE,SAAS,CAAEC,KAAK,CAAE,GAAI,CAAC,CACvD,CAAEF,KAAK,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAQ,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAQ,CAAC,CACrE,CACFC,UAAU,CAAE,KAAM,CACnB,CAAC,CACc,CAAC,EACV,CAAC,CACR,CAAC,CAGN1B,IAAI,CAACkC,YAAY,EAAIlC,IAAI,CAACkC,YAAY,CAACC,MAAM,CAAG,CAAC,eAChD9I,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChBxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAA7B,QAAA,eACxB1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/C1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,cAAY,CAAY,CAAC,CAClC,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,cACf1F,IAAA,CAACP,eAAe,EACdkH,IAAI,CAAEA,IAAI,CAACkC,YAAY,CAACE,GAAG,CAACC,IAAI,GAAK,CACnCrC,IAAI,CAAEqC,IAAI,CAACrC,IAAI,CACfsC,KAAK,CAAE,GAAGD,IAAI,CAACC,KAAK,GACtB,CAAC,CAAC,CAAE,CACJjB,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAM,CAAEC,KAAK,CAAE,GAAI,CAAC,CACjD,CAAEF,KAAK,CAAE,OAAO,CAAEC,UAAU,CAAE,cAAc,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAQ,CAAC,CAC1E,CACFC,UAAU,CAAE1B,IAAI,CAACkC,YAAY,CAACC,MAAM,CAAG,EAAG,CAC3C,CAAC,CACc,CAAC,EACV,CAAC,CACR,CACP,EACG,CACP,CAED,KAAM,CAAAxC,eAAe,CAAIK,IAAI,eAC3BzG,KAAA,CAACnE,IAAI,EAAC6K,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAnB,QAAA,EAExBvD,UAAU,eACTnC,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChB1F,IAAA,CAACJ,QAAQ,EAAC+G,IAAI,CAAEA,IAAK,CAAE,CAAC,CACpB,CACP,cAGD3G,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChBxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAA7B,QAAA,eACxB1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/C1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,oBAAkB,CAAY,CAAC,CACxC,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,cACf1F,IAAA,CAACP,eAAe,EACdkH,IAAI,CAAEA,IAAI,CAACuC,aAAa,EAAI,EAAG,CAC/BlB,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,WAAW,CAAEC,UAAU,CAAE,WAAW,CAAEC,KAAK,CAAE,GAAI,CAAC,CAC3D,CAAEF,KAAK,CAAE,SAAS,CAAEC,UAAU,CAAE,SAAS,CAAEC,KAAK,CAAE,GAAI,CAAC,CACvD,CAAEF,KAAK,CAAE,UAAU,CAAEC,UAAU,CAAE,MAAM,CAAEC,KAAK,CAAE,EAAE,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAS,CAAC,CACxF,CAAElB,KAAK,CAAE,eAAe,CAAEC,UAAU,CAAE,eAAe,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAQ,CACnGC,UAAU,CAAGC,GAAG,EAAK,GAAGA,GAAG,CAACC,aAAa,GAAI,CAAC,CAChD,CAAErB,KAAK,CAAE,aAAa,CAAEC,UAAU,CAAE,aAAa,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAQ,CAC/FC,UAAU,CAAGC,GAAG,EAAK,GAAGA,GAAG,CAACE,WAAW,GAAI,CAAC,CAC9C,CAAEtB,KAAK,CAAE,iBAAiB,CAAEC,UAAU,CAAE,WAAW,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAQ,CACjGC,UAAU,CAAGC,GAAG,EAAK,GAAGA,GAAG,CAACtB,eAAe,GAAI,CAAC,CAClD,CACH,CAAC,CACc,CAAC,EACV,CAAC,CACR,CAAC,cAGP/H,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChBxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAA7B,QAAA,eACxB1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/C1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,oBAAkB,CAAY,CAAC,CACxC,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,cACf1F,IAAA,CAACP,eAAe,EACdkH,IAAI,CAAEA,IAAI,CAAC6C,eAAe,EAAI,EAAG,CACjCxB,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,WAAW,CAAEC,UAAU,CAAE,WAAW,CAAEC,KAAK,CAAE,GAAI,CAAC,CAC3D,CAAEF,KAAK,CAAE,SAAS,CAAEC,UAAU,CAAE,SAAS,CAAEC,KAAK,CAAE,GAAI,CAAC,CACvD,CAAEF,KAAK,CAAE,YAAY,CAAEC,UAAU,CAAE,QAAQ,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAS,CAAC,CAC7F,CAAElB,KAAK,CAAE,mBAAmB,CAAEC,UAAU,CAAE,mBAAmB,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAQ,CAC3GC,UAAU,CAAGC,GAAG,EAAK,GAAGA,GAAG,CAACI,iBAAiB,GAAI,CAAC,CACpD,CACH,CAAC,CACc,CAAC,EACV,CAAC,CACR,CAAC,EACH,CACP,CAED,KAAM,CAAAlD,kBAAkB,CAAII,IAAI,eAC9BzG,KAAA,CAACnE,IAAI,EAAC6K,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAnB,QAAA,EAExBvD,UAAU,eACTnC,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChB1F,IAAA,CAACL,WAAW,EAACgH,IAAI,CAAEA,IAAK,CAAE,CAAC,CACvB,CACP,cAGD3G,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChBxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAA7B,QAAA,eACxB1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/CxF,KAAA,CAACrE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,EAAC,uBACF,CAACiB,IAAI,CAAC+C,aAAa,CAAC,UAC3C,EAAY,CAAC,CACG,CAAC,cACnB1J,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,cACf1F,IAAA,CAACP,eAAe,EACdkH,IAAI,CAAEA,IAAI,CAAC5E,MAAM,EAAI,EAAG,CACxBiG,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,WAAW,CAAEC,UAAU,CAAE,WAAW,CAAEC,KAAK,CAAE,GAAI,CAAC,CAC3D,CAAEF,KAAK,CAAE,WAAW,CAAEC,UAAU,CAAE,WAAW,CAAEC,KAAK,CAAE,GAAI,CAAC,CAC3D,CAAEF,KAAK,CAAE,SAAS,CAAEC,UAAU,CAAE,SAAS,CAAEC,KAAK,CAAE,GAAI,CAAC,CACvD,CAAEF,KAAK,CAAE,OAAO,CAAEC,UAAU,CAAE,OAAO,CAAEC,KAAK,CAAE,GAAG,CAC/CiB,UAAU,CAAGC,GAAG,eACdrJ,IAAA,CAAC5D,IAAI,EACHiL,KAAK,CAAEgC,GAAG,CAACM,KAAM,CACjB9F,KAAK,CAAEwF,GAAG,CAACM,KAAK,GAAK,aAAa,CAAG,SAAS,CAAG,SAAU,CAC3DvD,IAAI,CAAC,OAAO,CACb,CAEL,CAAC,CACD,CAAE6B,KAAK,CAAE,cAAc,CAAEC,UAAU,CAAE,cAAc,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAQ,CACjGC,UAAU,CAAGC,GAAG,EAAK,GAAGA,GAAG,CAAC1B,YAAY,GAAI,CAAC,CAC/C,CAAEM,KAAK,CAAE,eAAe,CAAEC,UAAU,CAAE,eAAe,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAQ,CACnGC,UAAU,CAAGC,GAAG,EAAK,GAAGA,GAAG,CAACO,aAAa,GAAI,CAAC,CAChD,CAAE3B,KAAK,CAAE,kBAAkB,CAAEC,UAAU,CAAE,kBAAkB,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAQ,CACzGC,UAAU,CAAGC,GAAG,EAAK,GAAGA,GAAG,CAACQ,gBAAgB,GAAI,CAAC,CACnD,CAAE5B,KAAK,CAAE,sBAAsB,CAAEC,UAAU,CAAE,UAAU,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAQ,CACrGC,UAAU,CAAGC,GAAG,EAAK,GAAGA,GAAG,CAACS,oBAAoB,GAAI,CAAC,CACvD,CACH,CAAC,CACc,CAAC,EACV,CAAC,CACR,CAAC,EACH,CACP,CAED,KAAM,CAAAtD,2BAA2B,CAAIG,IAAI,eACvCzG,KAAA,CAACnE,IAAI,EAAC6K,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAnB,QAAA,eAEzB1F,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChBxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAA7B,QAAA,eACxB1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/C1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,iBAAe,CAAY,CAAC,CACrC,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,cACf1F,IAAA,CAACP,eAAe,EACdkH,IAAI,CAAEA,IAAI,CAACoD,MAAM,CAAG,CAClB,CACEC,SAAS,CAAE,WAAW,CACtBtC,MAAM,CAAEf,IAAI,CAACoD,MAAM,CAACrI,SACtB,CAAC,CACD,CACEsI,SAAS,CAAE,WAAW,CACtBtC,MAAM,CAAEf,IAAI,CAACoD,MAAM,CAACE,SACtB,CAAC,CACD,CACED,SAAS,CAAE,SAAS,CACpBtC,MAAM,CAAEf,IAAI,CAACoD,MAAM,CAACG,OACtB,CAAC,CACD,CACEF,SAAS,CAAE,OAAO,CAClBtC,MAAM,CAAEf,IAAI,CAACoD,MAAM,CAACJ,KAAK,CACzBQ,aAAa,CAAE,IACjB,CAAC,CACD,CACEH,SAAS,CAAE,cAAc,CACzBtC,MAAM,CAAE,GAAGf,IAAI,CAACoD,MAAM,CAACpC,YAAY,GACrC,CAAC,CACD,CACEqC,SAAS,CAAE,eAAe,CAC1BtC,MAAM,CAAE,GAAGf,IAAI,CAACoD,MAAM,CAACH,aAAa,GACtC,CAAC,CACD,CACEI,SAAS,CAAE,kBAAkB,CAC7BtC,MAAM,CAAE,GAAGf,IAAI,CAACoD,MAAM,CAACF,gBAAgB,GACzC,CAAC,CACD,CACEG,SAAS,CAAE,sBAAsB,CACjCtC,MAAM,CAAE,GAAGf,IAAI,CAACoD,MAAM,CAACD,oBAAoB,GAC7C,CAAC,CACF,CAAG,EAAG,CACP9B,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,WAAW,CAAEC,UAAU,CAAE,WAAW,CAAEC,KAAK,CAAE,GAAI,CAAC,CAC3D,CAAEF,KAAK,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAQ,CAAEC,KAAK,CAAE,GAAG,CACjDiB,UAAU,CAAGC,GAAG,EAAKA,GAAG,CAACc,aAAa,cACpCnK,IAAA,CAAC5D,IAAI,EACHiL,KAAK,CAAEgC,GAAG,CAAC3B,MAAO,CAClB7D,KAAK,CAAEwF,GAAG,CAAC3B,MAAM,GAAK,aAAa,CAAG,SAAS,CAAG,SAAU,CAC5DtB,IAAI,CAAC,OAAO,CACb,CAAC,CACAiD,GAAG,CAAC3B,MACV,CAAC,CACD,CACFW,UAAU,CAAE,KAAM,CACnB,CAAC,CACc,CAAC,EACV,CAAC,CACR,CAAC,cAGPrI,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChBxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAA7B,QAAA,eACxB1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/CxF,KAAA,CAACrE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,EAAC,kBACP,CAACiB,IAAI,CAAC2B,WAAW,CAAC,GACpC,EAAY,CAAC,CACG,CAAC,cACnBtI,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,cACf1F,IAAA,CAACP,eAAe,EACdkH,IAAI,CAAEA,IAAI,CAACyD,cAAc,EAAI,EAAG,CAChCpC,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,SAAS,CAAEC,UAAU,CAAE,SAAS,CAAEC,KAAK,CAAE,GAAI,CAAC,CACvD,CAAEF,KAAK,CAAE,SAAS,CAAEC,UAAU,CAAE,SAAS,CAAEC,KAAK,CAAE,GAAI,CAAC,CACvD,CAAEF,KAAK,CAAE,SAAS,CAAEC,UAAU,CAAE,SAAS,CAAEC,KAAK,CAAE,GAAI,CAAC,CACvD,CAAEF,KAAK,CAAE,WAAW,CAAEC,UAAU,CAAE,WAAW,CAAEC,KAAK,CAAE,GAAI,CAAC,CAC3D,CAAEF,KAAK,CAAE,eAAe,CAAEC,UAAU,CAAE,eAAe,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAQ,CACnGC,UAAU,CAAGC,GAAG,EAAK,GAAGA,GAAG,CAACC,aAAa,GAAI,CAAC,CAChD,CAAErB,KAAK,CAAE,aAAa,CAAEC,UAAU,CAAE,aAAa,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAQ,CAC/FC,UAAU,CAAGC,GAAG,EAAK,GAAGA,GAAG,CAACE,WAAW,GAAI,CAAC,CAC9C,CAAEtB,KAAK,CAAE,OAAO,CAAEC,UAAU,CAAE,OAAO,CAAEC,KAAK,CAAE,GAAG,CAC/CiB,UAAU,CAAGC,GAAG,eACdrJ,IAAA,CAAC5D,IAAI,EACHiL,KAAK,CAAEgC,GAAG,CAACM,KAAM,CACjB9F,KAAK,CAAEwF,GAAG,CAACM,KAAK,GAAK,QAAQ,CAAG,SAAS,CAAG,SAAU,CACtDvD,IAAI,CAAC,OAAO,CACb,CAEL,CAAC,CACD,CACH,CAAC,CACc,CAAC,EACV,CAAC,CACR,CAAC,EACH,CACP,CAED,KAAM,CAAAK,uBAAuB,CAAIE,IAAI,eACnCzG,KAAA,CAACnE,IAAI,EAAC6K,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAnB,QAAA,EAExBvD,UAAU,eACTnC,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChB1F,IAAA,CAACH,aAAa,EAAC8G,IAAI,CAAEA,IAAK,CAAE,CAAC,CACzB,CACP,cAGD3G,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChBxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAA7B,QAAA,eACxB1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/C1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,qBAAmB,CAAY,CAAC,CACzC,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,cACf1F,IAAA,CAACP,eAAe,EACdkH,IAAI,CAAE,CACJ,CACE0D,UAAU,CAAE,SAAS,CACrB3C,MAAM,CAAE,GAAGf,IAAI,CAACnF,WAAW,MAAMmF,IAAI,CAAClF,SAAS,EACjD,CAAC,CACD,CACE4I,UAAU,CAAE,cAAc,CAC1B3C,MAAM,CAAE,GAAGf,IAAI,CAAC2D,oBAAoB,GACtC,CAAC,CACD,CACED,UAAU,CAAE,eAAe,CAC3B3C,MAAM,CAAEf,IAAI,CAAC4D,aACf,CAAC,CACD,CACEF,UAAU,CAAE,mBAAmB,CAC/B3C,MAAM,CAAE,GAAGf,IAAI,CAAC+B,iBAAiB,UACnC,CAAC,CACD,CACFV,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,YAAY,CAAEC,UAAU,CAAE,YAAY,CAAEC,KAAK,CAAE,GAAI,CAAC,CAC7D,CAAEF,KAAK,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAQ,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAQ,CAAC,CACrE,CACFC,UAAU,CAAE,KAAM,CACnB,CAAC,CACc,CAAC,EACV,CAAC,CACR,CAAC,cAGPrI,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChBxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAA7B,QAAA,eACxB1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/C1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,kBAAgB,CAAY,CAAC,CACtC,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,cACf1F,IAAA,CAACP,eAAe,EACdkH,IAAI,CAAEA,IAAI,CAAC6D,gBAAgB,EAAI,EAAG,CAClCxC,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAM,CAAEC,KAAK,CAAE,GAAI,CAAC,CACjD,CAAEF,KAAK,CAAE,OAAO,CAAEC,UAAU,CAAE,cAAc,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAQ,CAC1FC,UAAU,CAAGC,GAAG,EAAK,GAAGA,GAAG,CAACJ,KAAK,GAAI,CAAC,CACxC,CACH,CAAC,CACc,CAAC,EACV,CAAC,CACR,CAAC,EACH,CACP,CAED,KAAM,CAAAvC,qBAAqB,CAAIC,IAAI,eACjCzG,KAAA,CAACnE,IAAI,EAAC6K,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAnB,QAAA,EAExBvD,UAAU,eACTnC,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChB1F,IAAA,CAACF,cAAc,EAAC6G,IAAI,CAAEA,IAAK,CAAE,CAAC,CAC1B,CACP,cAGD3G,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChBxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAA7B,QAAA,eACxB1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/C1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,iCAA+B,CAAY,CAAC,CACrD,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,cACf1F,IAAA,CAACP,eAAe,EACdkH,IAAI,CAAEA,IAAI,CAAC8D,cAAc,EAAI,EAAG,CAChCzC,OAAO,CAAE,CACP,CAAEC,KAAK,CAAE,OAAO,CAAEC,UAAU,CAAE,OAAO,CAAEC,KAAK,CAAE,GAAG,CAC/CiB,UAAU,CAAGC,GAAG,eACdrJ,IAAA,CAAC5D,IAAI,EACHiL,KAAK,CAAEgC,GAAG,CAACM,KAAM,CACjB9F,KAAK,CAAEwF,GAAG,CAACM,KAAK,GAAK,YAAY,CAAG,SAAS,CAAG,SAAU,CAC1DvD,IAAI,CAAC,OAAO,CACb,CAEL,CAAC,CACD,CAAE6B,KAAK,CAAE,UAAU,CAAEC,UAAU,CAAE,aAAa,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAS,CAAC,CAChG,CAAElB,KAAK,CAAE,eAAe,CAAEC,UAAU,CAAE,eAAe,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAQ,CACnGC,UAAU,CAAGC,GAAG,EAAK,GAAGA,GAAG,CAACC,aAAa,GAAI,CAAC,CAChD,CAAErB,KAAK,CAAE,aAAa,CAAEC,UAAU,CAAE,aAAa,CAAEC,KAAK,CAAE,GAAG,CAAEC,KAAK,CAAE,OAAO,CAAEe,QAAQ,CAAE,QAAQ,CAC/FC,UAAU,CAAGC,GAAG,EAAK,GAAGA,GAAG,CAACE,WAAW,GAAI,CAAC,CAC9C,CACFlB,UAAU,CAAE,KAAM,CACnB,CAAC,CACc,CAAC,EACV,CAAC,CACR,CAAC,EACH,CACP,CAED,KAAM,CAAAqC,YAAY,CAAGA,CAAA,gBACnBxK,KAAA,CAACxD,MAAM,EAAC+H,IAAI,CAAExD,UAAW,CAAC0J,OAAO,CAAEtF,iBAAkB,CAACuF,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAnF,QAAA,eAC3E1F,IAAA,CAACrD,WAAW,EAAA+I,QAAA,CACT3E,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE2C,KAAK,CACX,CAAC,cACdxD,KAAA,CAACtD,aAAa,EAAA8I,QAAA,EACX/E,KAAK,eACJX,IAAA,CAAC3D,KAAK,EAACyO,QAAQ,CAAC,OAAO,CAACvF,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CACnC/E,KAAK,CACD,CACR,cAEDT,KAAA,CAACnE,IAAI,EAAC6K,SAAS,MAACC,OAAO,CAAE,CAAE,CAACtB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eACxC1F,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChBxF,KAAA,CAACpD,WAAW,EAAC+N,SAAS,MAAAnF,QAAA,eACpB1F,IAAA,CAACjD,UAAU,EAAA2I,QAAA,CAAC,SAAO,CAAY,CAAC,cAChCxF,KAAA,CAAClD,MAAM,EACL+N,KAAK,CAAE1J,QAAQ,CAACE,OAAQ,CACxB8F,KAAK,CAAC,SAAS,CACfH,QAAQ,CAAGC,CAAC,EAAK7F,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEE,OAAO,CAAE4F,CAAC,CAACC,MAAM,CAAC2D,KAAM,CAAC,CAAE,CAAArF,QAAA,eAEvE1F,IAAA,CAAC/C,QAAQ,EAAC8N,KAAK,CAAC,OAAO,CAAArF,QAAA,CAAC,sBAAoB,CAAU,CAAC,cACvD1F,IAAA,CAAC/C,QAAQ,EAAC8N,KAAK,CAAC,KAAK,CAAArF,QAAA,CAAC,cAAY,CAAU,CAAC,cAC7C1F,IAAA,CAAC/C,QAAQ,EAAC8N,KAAK,CAAC,OAAO,CAAArF,QAAA,CAAC,gBAAc,CAAU,CAAC,EAC3C,CAAC,EACE,CAAC,CACV,CAAC,CAENvE,UAAU,GAAK,kBAAkB,eAChCnB,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAArB,QAAA,cAChB1F,IAAA,CAAC9C,SAAS,EACR2N,SAAS,MACTxD,KAAK,CAAC,WAAW,CACjB0D,KAAK,CAAE1J,QAAQ,CAACK,SAAU,CAC1BwF,QAAQ,CAAGC,CAAC,EAAK7F,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEK,SAAS,CAAEyF,CAAC,CAACC,MAAM,CAAC2D,KAAM,CAAC,CAAE,CACzEC,WAAW,CAAC,mBAAmB,CAC/BC,UAAU,CAAC,0DAA0D,CACtE,CAAC,CACE,CACP,CAEA9J,UAAU,GAAK,cAAc,eAC5BjB,KAAA,CAAAE,SAAA,EAAAsF,QAAA,eACE1F,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,CAAE,CAAArB,QAAA,cACf1F,IAAA,CAAC9C,SAAS,EACR2N,SAAS,MACTK,IAAI,CAAC,MAAM,CACX7D,KAAK,CAAC,aAAa,CACnB0D,KAAK,CAAE1J,QAAQ,CAACG,WAAY,CAC5B0F,QAAQ,CAAGC,CAAC,EAAK7F,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEG,WAAW,CAAE2F,CAAC,CAACC,MAAM,CAAC2D,KAAM,CAAC,CAAE,CAC3EI,eAAe,CAAE,CAAEC,MAAM,CAAE,IAAK,CAAE,CACnC,CAAC,CACE,CAAC,cACPpL,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,CAAE,CAAArB,QAAA,cACf1F,IAAA,CAAC9C,SAAS,EACR2N,SAAS,MACTK,IAAI,CAAC,MAAM,CACX7D,KAAK,CAAC,WAAW,CACjB0D,KAAK,CAAE1J,QAAQ,CAACI,SAAU,CAC1ByF,QAAQ,CAAGC,CAAC,EAAK7F,WAAW,CAAC,CAAE,GAAGD,QAAQ,CAAEI,SAAS,CAAE0F,CAAC,CAACC,MAAM,CAAC2D,KAAM,CAAC,CAAE,CACzEI,eAAe,CAAE,CAAEC,MAAM,CAAE,IAAK,CAAE,CACnC,CAAC,CACE,CAAC,EACP,CACH,EACG,CAAC,EACM,CAAC,cAChBlL,KAAA,CAACrD,aAAa,EAAA6I,QAAA,eACZ1F,IAAA,CAAC7D,MAAM,EAACgK,OAAO,CAAEd,iBAAkB,CAAAK,QAAA,CAAC,SAAO,CAAQ,CAAC,cACpD1F,IAAA,CAAC7D,MAAM,EACLgK,OAAO,CAAEf,oBAAqB,CAC9BW,OAAO,CAAC,WAAW,CACnBsF,QAAQ,CAAE5K,OAAQ,CAClByF,SAAS,CAAEzF,OAAO,cAAGT,IAAA,CAAC1D,gBAAgB,EAAC8J,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGpG,IAAA,CAAC3B,cAAc,GAAE,CAAE,CAAAqH,QAAA,CAExEjF,OAAO,CAAG,gBAAgB,CAAG,eAAe,CACvC,CAAC,EACI,CAAC,EACV,CACT,CAED,mBACEP,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAE,QAAA,eAEhBxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACzFxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEI,GAAG,CAAE,CAAE,CAAE,CAAAP,QAAA,eACzD1F,IAAA,CAACxD,UAAU,EAAC2J,OAAO,CAAEA,CAAA,GAAM7F,QAAQ,CAAC,CAAC,CAAC,CAAE,CAACuD,KAAK,CAAC,SAAS,CAAA6B,QAAA,cACtD1F,IAAA,CAACvB,aAAa,GAAE,CAAC,CACP,CAAC,cACbuB,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAACuF,SAAS,CAAC,IAAI,CAAA5F,QAAA,CAAC,oBAExC,CAAY,CAAC,EACV,CAAC,cACN1F,IAAA,CAACT,eAAe,GAAE,CAAC,EAChB,CAAC,CAGLkB,OAAO,eACNT,IAAA,CAACpE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAE2F,EAAE,CAAE,CAAE,CAAE,CAAA7F,QAAA,cAC5D1F,IAAA,CAAC1D,gBAAgB,GAAE,CAAC,CACjB,CACN,cAGD4D,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eAEjBxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAChC,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACvC1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/CxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eACjD1F,IAAA,CAACvC,cAAc,EAAC8H,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAC,CAAEzD,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,cACxD7D,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,oBAAkB,CAAY,CAAC,EACrD,CAAC,CACU,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,CACd/D,WAAW,CAACE,QAAQ,cACnB3B,KAAA,CAACtE,GAAG,EAAA8J,QAAA,eACFxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACzF1F,IAAA,CAACzC,gBAAgB,EACfyJ,OAAO,cACLhH,IAAA,CAAC1C,MAAM,EACL2J,OAAO,CAAE9E,UAAW,CACpB+E,QAAQ,CAAGC,CAAC,EAAK/E,aAAa,CAAC+E,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE,CACjDpD,KAAK,CAAC,SAAS,CAChB,CACF,CACDwD,KAAK,cACHnH,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eACjD1F,IAAA,CAACb,aAAa,EAACoG,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,iBAElC,EAAK,CACN,CACF,CAAC,cACFpH,KAAA,CAACtE,GAAG,EAAA8J,QAAA,eACF1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAAC7B,YAAY,GAAE,CAAE,CAC5BgI,OAAO,CAAEA,CAAA,GAAMpC,wBAAwB,CAAC,UAAU,CAAE,KAAK,CAAE,CAC3DgC,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZvC,KAAK,CAAC,SAAS,CACf0B,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAA5B,QAAA,CACf,KAED,CAAQ,CAAC,cACT1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAAC7B,YAAY,GAAE,CAAE,CAC5BgI,OAAO,CAAEA,CAAA,GAAMpC,wBAAwB,CAAC,UAAU,CAAE,OAAO,CAAE,CAC7DgC,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZvC,KAAK,CAAC,SAAS,CAAA6B,QAAA,CAChB,OAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACLW,oBAAoB,CAAC1E,WAAW,CAACE,QAAQ,CAAC,EACxC,CAAC,CACJpB,OAAO,cACTP,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAE0F,EAAE,CAAE,CAAE,CAAE,CAAA7F,QAAA,eACxD1F,IAAA,CAAC1D,gBAAgB,EAAC8J,IAAI,CAAE,EAAG,CAAE,CAAC,cAC9BpG,IAAA,CAACnE,UAAU,EAAC0J,EAAE,CAAE,CAAEiG,EAAE,CAAE,CAAE,CAAE,CAAA9F,QAAA,CAAC,yBAAuB,CAAY,CAAC,EAC5D,CAAC,cAENxF,KAAA,CAACtE,GAAG,EAAA8J,QAAA,eACF1F,IAAA,CAAC3D,KAAK,EAACyO,QAAQ,CAAC,OAAO,CAACvF,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CAAC,uDAEvC,CAAO,CAAC,cACR1F,IAAA,CAAC7D,MAAM,EACL4J,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZF,SAAS,cAAElG,IAAA,CAACzB,WAAW,GAAE,CAAE,CAC3B4H,OAAO,CAAEA,CAAA,GAAM,CACbzF,UAAU,CAAC,IAAI,CAAC,CAChBlB,aAAa,CAAC+C,iBAAiB,CAAChC,UAAU,CAAE,OAAO,CAAC,CACjDkL,IAAI,CAAC9E,IAAI,EAAI,CACZ/E,cAAc,CAAC0C,IAAI,GAAK,CACtB,GAAGA,IAAI,CACPzC,QAAQ,CAAE8E,IAAI,CAAChE,OACjB,CAAC,CAAC,CAAC,CACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,EAAI,CACZC,OAAO,CAAC/B,KAAK,CAAC,iCAAiC,CAAE8B,GAAG,CAAC,CACvD,CAAC,CAAC,CACDiJ,OAAO,CAAC,IAAM,CACbhL,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACN,CAAE,CAAAgF,QAAA,CACH,SAED,CAAQ,CAAC,EACN,CACN,CACe,CAAC,EACV,CAAC,cAGZxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAChC,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACvC1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/CxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eACjD1F,IAAA,CAAC/B,QAAQ,EAACsH,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAC,CAAEzD,KAAK,CAAE,gBAAiB,CAAE,CAAE,CAAC,cACpD7D,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,oBAAkB,CAAY,CAAC,EACrD,CAAC,CACU,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,CACd/D,WAAW,CAACG,GAAG,cACd5B,KAAA,CAACtE,GAAG,EAAA8J,QAAA,eACFxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,UAAU,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eAC9D1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAAC7B,YAAY,GAAE,CAAE,CAC5BgI,OAAO,CAAEA,CAAA,GAAMpC,wBAAwB,CAAC,KAAK,CAAE,KAAK,CAAE,CACtDgC,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZvC,KAAK,CAAC,SAAS,CACf0B,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAA5B,QAAA,CACf,KAED,CAAQ,CAAC,cACT1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAAC7B,YAAY,GAAE,CAAE,CAC5BgI,OAAO,CAAEA,CAAA,GAAMpC,wBAAwB,CAAC,KAAK,CAAE,OAAO,CAAE,CACxDgC,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZvC,KAAK,CAAC,SAAS,CAAA6B,QAAA,CAChB,OAED,CAAQ,CAAC,EACN,CAAC,CACLY,eAAe,CAAC3E,WAAW,CAACG,GAAG,CAAC,EAC9B,CAAC,CACJrB,OAAO,cACTP,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAE0F,EAAE,CAAE,CAAE,CAAE,CAAA7F,QAAA,eACxD1F,IAAA,CAAC1D,gBAAgB,EAAC8J,IAAI,CAAE,EAAG,CAAE,CAAC,cAC9BpG,IAAA,CAACnE,UAAU,EAAC0J,EAAE,CAAE,CAAEiG,EAAE,CAAE,CAAE,CAAE,CAAA9F,QAAA,CAAC,yBAAuB,CAAY,CAAC,EAC5D,CAAC,cAENxF,KAAA,CAACtE,GAAG,EAAA8J,QAAA,eACF1F,IAAA,CAAC3D,KAAK,EAACyO,QAAQ,CAAC,OAAO,CAACvF,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CAAC,uDAEvC,CAAO,CAAC,cACR1F,IAAA,CAAC7D,MAAM,EACL4J,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZF,SAAS,cAAElG,IAAA,CAACzB,WAAW,GAAE,CAAE,CAC3B4H,OAAO,CAAEA,CAAA,GAAM,CACbzF,UAAU,CAAC,IAAI,CAAC,CAChBlB,aAAa,CAACqD,mBAAmB,CAACtC,UAAU,CAAE,OAAO,CAAC,CACnDkL,IAAI,CAAC9E,IAAI,EAAI,CACZ/E,cAAc,CAAC0C,IAAI,GAAK,CACtB,GAAGA,IAAI,CACPxC,GAAG,CAAE6E,IAAI,CAAChE,OACZ,CAAC,CAAC,CAAC,CACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,EAAI,CACZC,OAAO,CAAC/B,KAAK,CAAC,4BAA4B,CAAE8B,GAAG,CAAC,CAClD,CAAC,CAAC,CACDiJ,OAAO,CAAC,IAAM,CACbhL,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACN,CAAE,CAAAgF,QAAA,CACH,SAED,CAAQ,CAAC,EACN,CACN,CACe,CAAC,EACV,CAAC,cAGZxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAChC,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACvC1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/CxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eACjD1F,IAAA,CAACjB,aAAa,EAACwG,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAC,CAAEzD,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,cACvD7D,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,wBAAsB,CAAY,CAAC,EACzD,CAAC,CACU,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,CACd/D,WAAW,CAACI,MAAM,cACjB7B,KAAA,CAACtE,GAAG,EAAA8J,QAAA,eACFxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,UAAU,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eAC9D1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAAC7B,YAAY,GAAE,CAAE,CAC5BgI,OAAO,CAAEA,CAAA,GAAMpC,wBAAwB,CAAC,QAAQ,CAAE,KAAK,CAAE,CACzDgC,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZvC,KAAK,CAAC,SAAS,CACf0B,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAA5B,QAAA,CACf,KAED,CAAQ,CAAC,cACT1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAAC7B,YAAY,GAAE,CAAE,CAC5BgI,OAAO,CAAEA,CAAA,GAAMpC,wBAAwB,CAAC,QAAQ,CAAE,OAAO,CAAE,CAC3DgC,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZvC,KAAK,CAAC,SAAS,CAAA6B,QAAA,CAChB,OAED,CAAQ,CAAC,EACN,CAAC,CACLa,kBAAkB,CAAC5E,WAAW,CAACI,MAAM,CAAC,EACpC,CAAC,CACJtB,OAAO,cACTP,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAE0F,EAAE,CAAE,CAAE,CAAE,CAAA7F,QAAA,eACxD1F,IAAA,CAAC1D,gBAAgB,EAAC8J,IAAI,CAAE,EAAG,CAAE,CAAC,cAC9BpG,IAAA,CAACnE,UAAU,EAAC0J,EAAE,CAAE,CAAEiG,EAAE,CAAE,CAAE,CAAE,CAAA9F,QAAA,CAAC,yBAAuB,CAAY,CAAC,EAC5D,CAAC,cAENxF,KAAA,CAACtE,GAAG,EAAA8J,QAAA,eACF1F,IAAA,CAAC3D,KAAK,EAACyO,QAAQ,CAAC,OAAO,CAACvF,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CAAC,uDAEvC,CAAO,CAAC,cACR1F,IAAA,CAAC7D,MAAM,EACL4J,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZF,SAAS,cAAElG,IAAA,CAACzB,WAAW,GAAE,CAAE,CAC3B4H,OAAO,CAAEA,CAAA,GAAM,CACbzF,UAAU,CAAC,IAAI,CAAC,CAChBlB,aAAa,CAACuD,eAAe,CAACxC,UAAU,CAAE,OAAO,CAAC,CAC/CkL,IAAI,CAAC9E,IAAI,EAAI,CACZ/E,cAAc,CAAC0C,IAAI,GAAK,CACtB,GAAGA,IAAI,CACPvC,MAAM,CAAE4E,IAAI,CAAChE,OACf,CAAC,CAAC,CAAC,CACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,EAAI,CACZC,OAAO,CAAC/B,KAAK,CAAC,+BAA+B,CAAE8B,GAAG,CAAC,CACrD,CAAC,CAAC,CACDiJ,OAAO,CAAC,IAAM,CACbhL,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACN,CAAE,CAAAgF,QAAA,CACH,SAED,CAAQ,CAAC,EACN,CACN,CACe,CAAC,EACV,CAAC,cAGZxF,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAChC,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACvC1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/CxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eACjD1F,IAAA,CAACrC,YAAY,EAAC4H,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAC,CAAEzD,KAAK,CAAE,YAAa,CAAE,CAAE,CAAC,cACpD7D,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,uBAAqB,CAAY,CAAC,EACxD,CAAC,CACU,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,CACd/D,WAAW,CAACK,SAAS,cACpB9B,KAAA,CAACtE,GAAG,EAAA8J,QAAA,eACFxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,UAAU,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eAC9D1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAAC7B,YAAY,GAAE,CAAE,CAC5BgI,OAAO,CAAEA,CAAA,GAAMpC,wBAAwB,CAAC,YAAY,CAAE,KAAK,CAAE,CAC7DgC,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZvC,KAAK,CAAC,SAAS,CACf0B,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAA5B,QAAA,CACf,KAED,CAAQ,CAAC,cACT1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAAC7B,YAAY,GAAE,CAAE,CAC5BgI,OAAO,CAAEA,CAAA,GAAMpC,wBAAwB,CAAC,YAAY,CAAE,OAAO,CAAE,CAC/DgC,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZvC,KAAK,CAAC,SAAS,CAAA6B,QAAA,CAChB,OAED,CAAQ,CAAC,EACN,CAAC,CACLgB,qBAAqB,CAAC/E,WAAW,CAACK,SAAS,CAAC,EAC1C,CAAC,CACJvB,OAAO,cACTP,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAE0F,EAAE,CAAE,CAAE,CAAE,CAAA7F,QAAA,eACxD1F,IAAA,CAAC1D,gBAAgB,EAAC8J,IAAI,CAAE,EAAG,CAAE,CAAC,cAC9BpG,IAAA,CAACnE,UAAU,EAAC0J,EAAE,CAAE,CAAEiG,EAAE,CAAE,CAAE,CAAE,CAAA9F,QAAA,CAAC,yBAAuB,CAAY,CAAC,EAC5D,CAAC,cAENxF,KAAA,CAACtE,GAAG,EAAA8J,QAAA,eACF1F,IAAA,CAAC3D,KAAK,EAACyO,QAAQ,CAAC,OAAO,CAACvF,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CAAC,uDAEvC,CAAO,CAAC,cACR1F,IAAA,CAAC7D,MAAM,EACL4J,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZF,SAAS,cAAElG,IAAA,CAACzB,WAAW,GAAE,CAAE,CAC3B4H,OAAO,CAAEA,CAAA,GAAM,CACbzF,UAAU,CAAC,IAAI,CAAC,CAChBlB,aAAa,CAACyD,kBAAkB,CAAC1C,UAAU,CAAE,OAAO,CAAC,CAClDkL,IAAI,CAAC9E,IAAI,EAAI,CACZ/E,cAAc,CAAC0C,IAAI,GAAK,CACtB,GAAGA,IAAI,CACPtC,SAAS,CAAE2E,IAAI,CAAChE,OAClB,CAAC,CAAC,CAAC,CACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,EAAI,CACZC,OAAO,CAAC/B,KAAK,CAAC,mCAAmC,CAAE8B,GAAG,CAAC,CACzD,CAAC,CAAC,CACDiJ,OAAO,CAAC,IAAM,CACbhL,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACN,CAAE,CAAAgF,QAAA,CACH,SAED,CAAQ,CAAC,EACN,CACN,CACe,CAAC,EACV,CAAC,cAGZxF,KAAA,CAACpE,KAAK,EAACyJ,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eACzB1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAC4F,YAAY,MAAAjG,QAAA,CAAC,iBAAe,CAAY,CAAC,cAClE1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAAC0B,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CAAC,oEAElE,CAAY,CAAC,cAEbxF,KAAA,CAACnE,IAAI,EAAC6K,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAnB,QAAA,eAEzB1F,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAC6E,EAAE,CAAE,CAAE,CAAAlG,QAAA,cACvBxF,KAAA,CAAClE,IAAI,EAAA0J,QAAA,eACHxF,KAAA,CAACjE,WAAW,EAAAyJ,QAAA,eACV1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,yBAAuB,CAAY,CAAC,cAC7D1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAAC0B,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CAAC,0EAElE,CAAY,CAAC,EACF,CAAC,cACd1F,IAAA,CAAC9D,WAAW,EAAAwJ,QAAA,cACV1F,IAAA,CAAC7D,MAAM,EACL0O,SAAS,MACT9E,OAAO,CAAC,UAAU,CAClBlC,KAAK,CAAC,MAAM,CACZsC,OAAO,CAAEA,CAAA,GAAM,CACb/E,aAAa,CAAC,kBAAkB,CAAC,CACjCF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CAAAwE,QAAA,CACH,eAED,CAAQ,CAAC,CACE,CAAC,EACV,CAAC,CACH,CAAC,cAGP1F,IAAA,CAACjE,IAAI,EAAC+K,IAAI,MAACC,EAAE,CAAE,EAAG,CAAC6E,EAAE,CAAE,CAAE,CAAAlG,QAAA,cACvBxF,KAAA,CAAClE,IAAI,EAAA0J,QAAA,eACHxF,KAAA,CAACjE,WAAW,EAAAyJ,QAAA,eACV1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,yBAAuB,CAAY,CAAC,cAC7D1F,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAAC0B,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CAAC,6DAElE,CAAY,CAAC,EACF,CAAC,cACd1F,IAAA,CAAC9D,WAAW,EAAAwJ,QAAA,cACV1F,IAAA,CAAC7D,MAAM,EACL0O,SAAS,MACT9E,OAAO,CAAC,UAAU,CAClBlC,KAAK,CAAC,SAAS,CACfsC,OAAO,CAAEA,CAAA,GAAM,CACb/E,aAAa,CAAC,cAAc,CAAC,CAC7B;AACA,KAAM,CAAAyD,KAAK,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACxB,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAD,IAAI,CAAC,CAAC,CAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAExC3D,WAAW,CAAC,CACV,GAAGD,QAAQ,CACXG,WAAW,CAAEuD,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAClD1D,SAAS,CAAEoD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAC7C,CAAC,CAAC,CACFjE,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CAAAwE,QAAA,CACH,eAED,CAAQ,CAAC,CACE,CAAC,EACV,CAAC,CACH,CAAC,EACH,CAAC,EACF,CAAC,CAGP/D,WAAW,CAACM,eAAe,eAC1B/B,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAChC,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAC,CAAEL,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eAC9C1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/CxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eACjD1F,IAAA,CAACnB,SAAS,EAAC0G,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAC,CAAEzD,KAAK,CAAE,WAAY,CAAE,CAAE,CAAC,cAChD7D,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,yBAAuB,CAAY,CAAC,EAC1D,CAAC,CACU,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,cACfxF,KAAA,CAACtE,GAAG,EAAA8J,QAAA,eACFxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,UAAU,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eAC9D1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAAC7B,YAAY,GAAE,CAAE,CAC5BgI,OAAO,CAAEA,CAAA,GAAMpC,wBAAwB,CAAC,kBAAkB,CAAE,KAAK,CAAE,CACnEgC,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZvC,KAAK,CAAC,SAAS,CACf0B,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAA5B,QAAA,CACf,KAED,CAAQ,CAAC,cACT1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAAC7B,YAAY,GAAE,CAAE,CAC5BgI,OAAO,CAAEA,CAAA,GAAMpC,wBAAwB,CAAC,kBAAkB,CAAE,OAAO,CAAE,CACrEgC,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZvC,KAAK,CAAC,SAAS,CAAA6B,QAAA,CAChB,OAED,CAAQ,CAAC,EACN,CAAC,CACLc,2BAA2B,CAAC7E,WAAW,CAACM,eAAe,CAAC,EACtD,CAAC,CACU,CAAC,EACV,CACZ,CAEAN,WAAW,CAACO,WAAW,eACtBhC,KAAA,CAAC/C,SAAS,EAACoK,eAAe,MAAChC,EAAE,CAAE,CAAEO,EAAE,CAAE,CAAC,CAAEL,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eAC9C1F,IAAA,CAAC5C,gBAAgB,EAACoK,UAAU,cAAExH,IAAA,CAACf,cAAc,GAAE,CAAE,CAAAyG,QAAA,cAC/CxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAH,QAAA,eACjD1F,IAAA,CAACjC,YAAY,EAACwH,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAC,CAAEzD,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,cACtD7D,IAAA,CAACnE,UAAU,EAACkK,OAAO,CAAC,IAAI,CAAAL,QAAA,CAAC,yBAAuB,CAAY,CAAC,EAC1D,CAAC,CACU,CAAC,cACnB1F,IAAA,CAAC3C,gBAAgB,EAAAqI,QAAA,cACfxF,KAAA,CAACtE,GAAG,EAAA8J,QAAA,eACFxF,KAAA,CAACtE,GAAG,EAAC2J,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,UAAU,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eAC9D1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAAC7B,YAAY,GAAE,CAAE,CAC5BgI,OAAO,CAAEA,CAAA,GAAMpC,wBAAwB,CAAC,cAAc,CAAE,KAAK,CAAE,CAC/DgC,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZvC,KAAK,CAAC,SAAS,CACf0B,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAA5B,QAAA,CACf,KAED,CAAQ,CAAC,cACT1F,IAAA,CAAC7D,MAAM,EACL+J,SAAS,cAAElG,IAAA,CAAC7B,YAAY,GAAE,CAAE,CAC5BgI,OAAO,CAAEA,CAAA,GAAMpC,wBAAwB,CAAC,cAAc,CAAE,OAAO,CAAE,CACjEgC,OAAO,CAAC,UAAU,CAClBK,IAAI,CAAC,OAAO,CACZvC,KAAK,CAAC,SAAS,CAAA6B,QAAA,CAChB,OAED,CAAQ,CAAC,EACN,CAAC,CACLe,uBAAuB,CAAC9E,WAAW,CAACO,WAAW,CAAC,EAC9C,CAAC,CACU,CAAC,EACV,CACZ,EACE,CAAC,CAGLwI,YAAY,CAAC,CAAC,EACZ,CAAC,CAEV,CAAC,CAED,cAAe,CAAArK,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}