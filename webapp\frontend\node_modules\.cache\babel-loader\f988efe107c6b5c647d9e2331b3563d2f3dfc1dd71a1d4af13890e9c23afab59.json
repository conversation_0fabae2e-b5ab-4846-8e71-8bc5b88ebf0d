{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\MainMenu.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { List, ListItem, ListItemIcon, ListItemText, Divider, Box, Typography, Collapse, ListItemButton } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon, ExpandLess, ExpandMore, ViewList as ViewListIcon, Engineering as EngineeringIcon, Inventory as InventoryIcon, TableChart as TableChartIcon, Assessment as AssessmentIcon, VerifiedUser as VerifiedUserIcon, ShoppingCart as ShoppingCartIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MainMenu = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n\n  // Stati per i menu a cascata\n  const [openCaviMenu, setOpenCaviMenu] = useState(false);\n  const [openCantieriMenu, setOpenCantieriMenu] = useState(false);\n  const [openAdminMenu, setOpenAdminMenu] = useState(false);\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = path => {\n    return location.pathname.startsWith(path);\n  };\n\n  // Gestisce l'apertura/chiusura dei menu a cascata\n  const handleToggleCaviMenu = () => {\n    setOpenCaviMenu(!openCaviMenu);\n  };\n  const handleToggleCantieriMenu = () => {\n    setOpenCantieriMenu(!openCantieriMenu);\n  };\n  const handleToggleAdminMenu = () => {\n    setOpenAdminMenu(!openAdminMenu);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      console.log('Utente impersonato, reindirizzamento al menu amministratore');\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(List, {\n    children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n      selected: isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard'),\n      onClick: () => navigateTo('/dashboard'),\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: isImpersonating ? \"Torna al Menu Admin\" : \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), (user === null || user === void 0 ? void 0 : user.role) === 'owner' && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: handleToggleAdminMenu,\n        selected: isPartOfActive('/dashboard/admin'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(AdminIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: \"Amministrazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this), openAdminMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 30\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 47\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n        in: openAdminMenu,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            selected: isActive('/dashboard/admin'),\n            onClick: () => navigateTo('/dashboard/admin'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AdminIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Pannello Admin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), ((user === null || user === void 0 ? void 0 : user.role) !== 'owner' || (user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this), isImpersonating && impersonatedUser && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          bgcolor: 'rgba(255, 165, 0, 0.1)',\n          borderLeft: '4px solid orange'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          color: \"textSecondary\",\n          children: \"Accesso come utente:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"bold\",\n          children: impersonatedUser.username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: handleToggleCantieriMenu,\n        selected: isPartOfActive('/dashboard/cantieri'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(ConstructionIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this), openCantieriMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 33\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 50\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n        in: openCantieriMenu,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            selected: isActive('/dashboard/cantieri'),\n            onClick: () => navigateTo('/dashboard/cantieri'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(ViewListIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Lista Cantieri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: handleToggleCaviMenu,\n        selected: isPartOfActive('/dashboard/cavi'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: \"Gestione Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this), openCaviMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 29\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 46\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n        in: openCaviMenu,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            selected: isActive('/dashboard/cavi'),\n            onClick: () => navigateTo('/dashboard/cavi'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(ViewListIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Visualizza Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => {\n              navigateTo('/dashboard/cavi');\n              // Imposta il tab index a 1 (Posa Cavi e Collegamenti) nel localStorage\n              localStorage.setItem('caviTabIndex', '1');\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EngineeringIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Posa e Collegamenti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => {\n              navigateTo('/dashboard/cavi');\n              // Imposta il tab index a 2 (Parco Cavi) nel localStorage\n              localStorage.setItem('caviTabIndex', '2');\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Parco Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => {\n              navigateTo('/dashboard/cavi');\n              // Imposta il tab index a 3 (Gestione Excel) nel localStorage\n              localStorage.setItem('caviTabIndex', '3');\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(TableChartIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Gestione Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => {\n              navigateTo('/dashboard/cavi');\n              // Imposta il tab index a 4 (Report) nel localStorage\n              localStorage.setItem('caviTabIndex', '4');\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Report\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => {\n              navigateTo('/dashboard/cavi');\n              // Imposta il tab index a 5 (Certificazione Cavi) nel localStorage\n              localStorage.setItem('caviTabIndex', '5');\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(VerifiedUserIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Certificazione Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => {\n              navigateTo('/dashboard/cavi');\n              // Imposta il tab index a 6 (Gestione Comande) nel localStorage\n              localStorage.setItem('caviTabIndex', '6');\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(ShoppingCartIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Gestione Comande\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n_s(MainMenu, \"dBrY2PyCs0CkQwvBOMf3l11IK38=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = MainMenu;\nexport default MainMenu;\nvar _c;\n$RefreshReg$(_c, \"MainMenu\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "Box", "Typography", "Collapse", "ListItemButton", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "ExpandLess", "ExpandMore", "ViewList", "ViewListIcon", "Engineering", "EngineeringIcon", "Inventory", "InventoryIcon", "Table<PERSON>hart", "TableChartIcon", "Assessment", "AssessmentIcon", "VerifiedUser", "VerifiedUserIcon", "ShoppingCart", "ShoppingCartIcon", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MainMenu", "_s", "navigate", "location", "user", "isImpersonating", "impersonated<PERSON><PERSON>", "openCaviMenu", "setOpenCaviMenu", "openCantieriMenu", "setOpenCantieriMenu", "openAdminMenu", "setOpenAdminMenu", "isActive", "path", "pathname", "isPartOfActive", "startsWith", "handleToggleCaviMenu", "handleToggleCantieriMenu", "handleToggleAdminMenu", "navigateTo", "console", "log", "children", "selected", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "primary", "role", "in", "timeout", "unmountOnExit", "component", "disablePadding", "sx", "pl", "p", "bgcolor", "borderLeft", "variant", "color", "fontWeight", "username", "localStorage", "setItem", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/MainMenu.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Box,\n  Typography,\n  Collapse,\n  ListItemButton\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon,\n  ExpandLess,\n  ExpandMore,\n  ViewList as ViewListIcon,\n  Engineering as EngineeringIcon,\n  Inventory as InventoryIcon,\n  TableChart as TableChartIcon,\n  Assessment as AssessmentIcon,\n  VerifiedUser as VerifiedUserIcon,\n  ShoppingCart as ShoppingCartIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\n\nconst MainMenu = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, isImpersonating, impersonatedUser } = useAuth();\n\n  // Stati per i menu a cascata\n  const [openCaviMenu, setOpenCaviMenu] = useState(false);\n  const [openCantieriMenu, setOpenCantieriMenu] = useState(false);\n  const [openAdminMenu, setOpenAdminMenu] = useState(false);\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  // Gestisce l'apertura/chiusura dei menu a cascata\n  const handleToggleCaviMenu = () => {\n    setOpenCaviMenu(!openCaviMenu);\n  };\n\n  const handleToggleCantieriMenu = () => {\n    setOpenCantieriMenu(!openCantieriMenu);\n  };\n\n  const handleToggleAdminMenu = () => {\n    setOpenAdminMenu(!openAdminMenu);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      console.log('Utente impersonato, reindirizzamento al menu amministratore');\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n  };\n\n  return (\n    <List>\n      {/* Home - Se l'utente è un amministratore che sta impersonando un utente, mostra \"Torna al Menu Admin\" */}\n      <ListItemButton\n        selected={isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard')}\n        onClick={() => navigateTo('/dashboard')}\n      >\n        <ListItemIcon>\n          <HomeIcon />\n        </ListItemIcon>\n        <ListItemText primary={isImpersonating ? \"Torna al Menu Admin\" : \"Home\"} />\n      </ListItemButton>\n\n      {/* Menu Amministratore (solo per admin) */}\n      {user?.role === 'owner' && (\n        <>\n          <Divider />\n          <ListItemButton\n            onClick={handleToggleAdminMenu}\n            selected={isPartOfActive('/dashboard/admin')}\n          >\n            <ListItemIcon>\n              <AdminIcon />\n            </ListItemIcon>\n            <ListItemText primary=\"Amministrazione\" />\n            {openAdminMenu ? <ExpandLess /> : <ExpandMore />}\n          </ListItemButton>\n          <Collapse in={openAdminMenu} timeout=\"auto\" unmountOnExit>\n            <List component=\"div\" disablePadding>\n              <ListItemButton\n                sx={{ pl: 4 }}\n                selected={isActive('/dashboard/admin')}\n                onClick={() => navigateTo('/dashboard/admin')}\n              >\n                <ListItemIcon>\n                  <AdminIcon />\n                </ListItemIcon>\n                <ListItemText primary=\"Pannello Admin\" />\n              </ListItemButton>\n              {/* Altri sottomenu admin possono essere aggiunti qui */}\n            </List>\n          </Collapse>\n        </>\n      )}\n\n      {/* Menu per utenti standard e cantieri */}\n      {/* Mostra per utenti standard/cantiere o per admin che sta impersonando un utente */}\n      {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (\n        <>\n          <Divider />\n          {isImpersonating && impersonatedUser && (\n            <Box sx={{ p: 2, bgcolor: 'rgba(255, 165, 0, 0.1)', borderLeft: '4px solid orange' }}>\n              <Typography variant=\"subtitle2\" color=\"textSecondary\">\n                Accesso come utente:\n              </Typography>\n              <Typography variant=\"body2\" fontWeight=\"bold\">\n                {impersonatedUser.username}\n              </Typography>\n            </Box>\n          )}\n\n          {/* Menu Cantieri con sottomenu */}\n          <ListItemButton\n            onClick={handleToggleCantieriMenu}\n            selected={isPartOfActive('/dashboard/cantieri')}\n          >\n            <ListItemIcon>\n              <ConstructionIcon />\n            </ListItemIcon>\n            <ListItemText primary={isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"} />\n            {openCantieriMenu ? <ExpandLess /> : <ExpandMore />}\n          </ListItemButton>\n          <Collapse in={openCantieriMenu} timeout=\"auto\" unmountOnExit>\n            <List component=\"div\" disablePadding>\n              <ListItemButton\n                sx={{ pl: 4 }}\n                selected={isActive('/dashboard/cantieri')}\n                onClick={() => navigateTo('/dashboard/cantieri')}\n              >\n                <ListItemIcon>\n                  <ViewListIcon />\n                </ListItemIcon>\n                <ListItemText primary=\"Lista Cantieri\" />\n              </ListItemButton>\n              {/* Altri sottomenu cantieri possono essere aggiunti qui */}\n            </List>\n          </Collapse>\n\n          {/* Menu Cavi con sottomenu */}\n          <ListItemButton\n            onClick={handleToggleCaviMenu}\n            selected={isPartOfActive('/dashboard/cavi')}\n          >\n            <ListItemIcon>\n              <CableIcon />\n            </ListItemIcon>\n            <ListItemText primary=\"Gestione Cavi\" />\n            {openCaviMenu ? <ExpandLess /> : <ExpandMore />}\n          </ListItemButton>\n          <Collapse in={openCaviMenu} timeout=\"auto\" unmountOnExit>\n            <List component=\"div\" disablePadding>\n              <ListItemButton\n                sx={{ pl: 4 }}\n                selected={isActive('/dashboard/cavi')}\n                onClick={() => navigateTo('/dashboard/cavi')}\n              >\n                <ListItemIcon>\n                  <ViewListIcon />\n                </ListItemIcon>\n                <ListItemText primary=\"Visualizza Cavi\" />\n              </ListItemButton>\n\n              <ListItemButton\n                sx={{ pl: 4 }}\n                onClick={() => {\n                  navigateTo('/dashboard/cavi');\n                  // Imposta il tab index a 1 (Posa Cavi e Collegamenti) nel localStorage\n                  localStorage.setItem('caviTabIndex', '1');\n                }}\n              >\n                <ListItemIcon>\n                  <EngineeringIcon />\n                </ListItemIcon>\n                <ListItemText primary=\"Posa e Collegamenti\" />\n              </ListItemButton>\n\n              <ListItemButton\n                sx={{ pl: 4 }}\n                onClick={() => {\n                  navigateTo('/dashboard/cavi');\n                  // Imposta il tab index a 2 (Parco Cavi) nel localStorage\n                  localStorage.setItem('caviTabIndex', '2');\n                }}\n              >\n                <ListItemIcon>\n                  <InventoryIcon />\n                </ListItemIcon>\n                <ListItemText primary=\"Parco Cavi\" />\n              </ListItemButton>\n\n              <ListItemButton\n                sx={{ pl: 4 }}\n                onClick={() => {\n                  navigateTo('/dashboard/cavi');\n                  // Imposta il tab index a 3 (Gestione Excel) nel localStorage\n                  localStorage.setItem('caviTabIndex', '3');\n                }}\n              >\n                <ListItemIcon>\n                  <TableChartIcon />\n                </ListItemIcon>\n                <ListItemText primary=\"Gestione Excel\" />\n              </ListItemButton>\n\n              <ListItemButton\n                sx={{ pl: 4 }}\n                onClick={() => {\n                  navigateTo('/dashboard/cavi');\n                  // Imposta il tab index a 4 (Report) nel localStorage\n                  localStorage.setItem('caviTabIndex', '4');\n                }}\n              >\n                <ListItemIcon>\n                  <AssessmentIcon />\n                </ListItemIcon>\n                <ListItemText primary=\"Report\" />\n              </ListItemButton>\n\n              <ListItemButton\n                sx={{ pl: 4 }}\n                onClick={() => {\n                  navigateTo('/dashboard/cavi');\n                  // Imposta il tab index a 5 (Certificazione Cavi) nel localStorage\n                  localStorage.setItem('caviTabIndex', '5');\n                }}\n              >\n                <ListItemIcon>\n                  <VerifiedUserIcon />\n                </ListItemIcon>\n                <ListItemText primary=\"Certificazione Cavi\" />\n              </ListItemButton>\n\n              <ListItemButton\n                sx={{ pl: 4 }}\n                onClick={() => {\n                  navigateTo('/dashboard/cavi');\n                  // Imposta il tab index a 6 (Gestione Comande) nel localStorage\n                  localStorage.setItem('caviTabIndex', '6');\n                }}\n              >\n                <ListItemIcon>\n                  <ShoppingCartIcon />\n                </ListItemIcon>\n                <ListItemText primary=\"Gestione Comande\" />\n              </ListItemButton>\n            </List>\n          </Collapse>\n        </>\n      )}\n    </List>\n  );\n};\n\nexport default MainMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,UAAU,EACVC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,UAAU,EACVC,UAAU,EACVC,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,eAAe,EAC9BC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,YAAY,IAAIC,gBAAgB,EAChCC,YAAY,IAAIC,gBAAgB,QAC3B,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM6C,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6C,IAAI;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGX,OAAO,CAAC,CAAC;;EAE7D;EACA,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAMwD,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOX,QAAQ,CAACY,QAAQ,KAAKD,IAAI;EACnC,CAAC;;EAED;EACA,MAAME,cAAc,GAAIF,IAAI,IAAK;IAC/B,OAAOX,QAAQ,CAACY,QAAQ,CAACE,UAAU,CAACH,IAAI,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjCV,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,MAAMY,wBAAwB,GAAGA,CAAA,KAAM;IACrCT,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,MAAMW,qBAAqB,GAAGA,CAAA,KAAM;IAClCR,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAMU,UAAU,GAAIP,IAAI,IAAK;IAC3BQ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAET,IAAI,EAAE,kBAAkB,EAAET,eAAe,EAAE,OAAO,EAAED,IAAI,CAAC;IACvF;IACA;IACA,IAAIU,IAAI,KAAK,YAAY,IAAIT,eAAe,EAAE;MAC5CiB,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ErB,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM;MACLA,QAAQ,CAACY,IAAI,CAAC;IAChB;EACF,CAAC;EAED,oBACEjB,OAAA,CAACrC,IAAI;IAAAgE,QAAA,gBAEH3B,OAAA,CAAC7B,cAAc;MACbyD,QAAQ,EAAEpB,eAAe,GAAGQ,QAAQ,CAAC,kBAAkB,CAAC,GAAGA,QAAQ,CAAC,YAAY,CAAE;MAClFa,OAAO,EAAEA,CAAA,KAAML,UAAU,CAAC,YAAY,CAAE;MAAAG,QAAA,gBAExC3B,OAAA,CAACnC,YAAY;QAAA8D,QAAA,eACX3B,OAAA,CAAC3B,QAAQ;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACfjC,OAAA,CAAClC,YAAY;QAACoE,OAAO,EAAE1B,eAAe,GAAG,qBAAqB,GAAG;MAAO;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,EAGhB,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,IAAI,MAAK,OAAO,iBACrBnC,OAAA,CAAAE,SAAA;MAAAyB,QAAA,gBACE3B,OAAA,CAACjC,OAAO;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXjC,OAAA,CAAC7B,cAAc;QACb0D,OAAO,EAAEN,qBAAsB;QAC/BK,QAAQ,EAAET,cAAc,CAAC,kBAAkB,CAAE;QAAAQ,QAAA,gBAE7C3B,OAAA,CAACnC,YAAY;UAAA8D,QAAA,eACX3B,OAAA,CAACzB,SAAS;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACfjC,OAAA,CAAClC,YAAY;UAACoE,OAAO,EAAC;QAAiB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACzCnB,aAAa,gBAAGd,OAAA,CAAClB,UAAU;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGjC,OAAA,CAACjB,UAAU;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACjBjC,OAAA,CAAC9B,QAAQ;QAACkE,EAAE,EAAEtB,aAAc;QAACuB,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAX,QAAA,eACvD3B,OAAA,CAACrC,IAAI;UAAC4E,SAAS,EAAC,KAAK;UAACC,cAAc;UAAAb,QAAA,eAClC3B,OAAA,CAAC7B,cAAc;YACbsE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdd,QAAQ,EAAEZ,QAAQ,CAAC,kBAAkB,CAAE;YACvCa,OAAO,EAAEA,CAAA,KAAML,UAAU,CAAC,kBAAkB,CAAE;YAAAG,QAAA,gBAE9C3B,OAAA,CAACnC,YAAY;cAAA8D,QAAA,eACX3B,OAAA,CAACzB,SAAS;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACfjC,OAAA,CAAClC,YAAY;cAACoE,OAAO,EAAC;YAAgB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA,eACX,CACH,EAIA,CAAC,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,IAAI,MAAK,OAAO,IAAK,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,IAAI,MAAK,OAAO,IAAI3B,eAAe,IAAIC,gBAAiB,kBACzFT,OAAA,CAAAE,SAAA;MAAAyB,QAAA,gBACE3B,OAAA,CAACjC,OAAO;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACVzB,eAAe,IAAIC,gBAAgB,iBAClCT,OAAA,CAAChC,GAAG;QAACyE,EAAE,EAAE;UAAEE,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE,wBAAwB;UAAEC,UAAU,EAAE;QAAmB,CAAE;QAAAlB,QAAA,gBACnF3B,OAAA,CAAC/B,UAAU;UAAC6E,OAAO,EAAC,WAAW;UAACC,KAAK,EAAC,eAAe;UAAApB,QAAA,EAAC;QAEtD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjC,OAAA,CAAC/B,UAAU;UAAC6E,OAAO,EAAC,OAAO;UAACE,UAAU,EAAC,MAAM;UAAArB,QAAA,EAC1ClB,gBAAgB,CAACwC;QAAQ;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN,eAGDjC,OAAA,CAAC7B,cAAc;QACb0D,OAAO,EAAEP,wBAAyB;QAClCM,QAAQ,EAAET,cAAc,CAAC,qBAAqB,CAAE;QAAAQ,QAAA,gBAEhD3B,OAAA,CAACnC,YAAY;UAAA8D,QAAA,eACX3B,OAAA,CAACvB,gBAAgB;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACfjC,OAAA,CAAClC,YAAY;UAACoE,OAAO,EAAE1B,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAACwC,QAAQ,EAAE,GAAG;QAAkB;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC9HrB,gBAAgB,gBAAGZ,OAAA,CAAClB,UAAU;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGjC,OAAA,CAACjB,UAAU;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC,eACjBjC,OAAA,CAAC9B,QAAQ;QAACkE,EAAE,EAAExB,gBAAiB;QAACyB,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAX,QAAA,eAC1D3B,OAAA,CAACrC,IAAI;UAAC4E,SAAS,EAAC,KAAK;UAACC,cAAc;UAAAb,QAAA,eAClC3B,OAAA,CAAC7B,cAAc;YACbsE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdd,QAAQ,EAAEZ,QAAQ,CAAC,qBAAqB,CAAE;YAC1Ca,OAAO,EAAEA,CAAA,KAAML,UAAU,CAAC,qBAAqB,CAAE;YAAAG,QAAA,gBAEjD3B,OAAA,CAACnC,YAAY;cAAA8D,QAAA,eACX3B,OAAA,CAACf,YAAY;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACfjC,OAAA,CAAClC,YAAY;cAACoE,OAAO,EAAC;YAAgB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGXjC,OAAA,CAAC7B,cAAc;QACb0D,OAAO,EAAER,oBAAqB;QAC9BO,QAAQ,EAAET,cAAc,CAAC,iBAAiB,CAAE;QAAAQ,QAAA,gBAE5C3B,OAAA,CAACnC,YAAY;UAAA8D,QAAA,eACX3B,OAAA,CAACrB,SAAS;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACfjC,OAAA,CAAClC,YAAY;UAACoE,OAAO,EAAC;QAAe;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACvCvB,YAAY,gBAAGV,OAAA,CAAClB,UAAU;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGjC,OAAA,CAACjB,UAAU;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACjBjC,OAAA,CAAC9B,QAAQ;QAACkE,EAAE,EAAE1B,YAAa;QAAC2B,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAX,QAAA,eACtD3B,OAAA,CAACrC,IAAI;UAAC4E,SAAS,EAAC,KAAK;UAACC,cAAc;UAAAb,QAAA,gBAClC3B,OAAA,CAAC7B,cAAc;YACbsE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdd,QAAQ,EAAEZ,QAAQ,CAAC,iBAAiB,CAAE;YACtCa,OAAO,EAAEA,CAAA,KAAML,UAAU,CAAC,iBAAiB,CAAE;YAAAG,QAAA,gBAE7C3B,OAAA,CAACnC,YAAY;cAAA8D,QAAA,eACX3B,OAAA,CAACf,YAAY;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACfjC,OAAA,CAAClC,YAAY;cAACoE,OAAO,EAAC;YAAiB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eAEjBjC,OAAA,CAAC7B,cAAc;YACbsE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdb,OAAO,EAAEA,CAAA,KAAM;cACbL,UAAU,CAAC,iBAAiB,CAAC;cAC7B;cACA0B,YAAY,CAACC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;YAC3C,CAAE;YAAAxB,QAAA,gBAEF3B,OAAA,CAACnC,YAAY;cAAA8D,QAAA,eACX3B,OAAA,CAACb,eAAe;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACfjC,OAAA,CAAClC,YAAY;cAACoE,OAAO,EAAC;YAAqB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAEjBjC,OAAA,CAAC7B,cAAc;YACbsE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdb,OAAO,EAAEA,CAAA,KAAM;cACbL,UAAU,CAAC,iBAAiB,CAAC;cAC7B;cACA0B,YAAY,CAACC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;YAC3C,CAAE;YAAAxB,QAAA,gBAEF3B,OAAA,CAACnC,YAAY;cAAA8D,QAAA,eACX3B,OAAA,CAACX,aAAa;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACfjC,OAAA,CAAClC,YAAY;cAACoE,OAAO,EAAC;YAAY;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eAEjBjC,OAAA,CAAC7B,cAAc;YACbsE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdb,OAAO,EAAEA,CAAA,KAAM;cACbL,UAAU,CAAC,iBAAiB,CAAC;cAC7B;cACA0B,YAAY,CAACC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;YAC3C,CAAE;YAAAxB,QAAA,gBAEF3B,OAAA,CAACnC,YAAY;cAAA8D,QAAA,eACX3B,OAAA,CAACT,cAAc;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACfjC,OAAA,CAAClC,YAAY;cAACoE,OAAO,EAAC;YAAgB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAEjBjC,OAAA,CAAC7B,cAAc;YACbsE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdb,OAAO,EAAEA,CAAA,KAAM;cACbL,UAAU,CAAC,iBAAiB,CAAC;cAC7B;cACA0B,YAAY,CAACC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;YAC3C,CAAE;YAAAxB,QAAA,gBAEF3B,OAAA,CAACnC,YAAY;cAAA8D,QAAA,eACX3B,OAAA,CAACP,cAAc;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACfjC,OAAA,CAAClC,YAAY;cAACoE,OAAO,EAAC;YAAQ;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAEjBjC,OAAA,CAAC7B,cAAc;YACbsE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdb,OAAO,EAAEA,CAAA,KAAM;cACbL,UAAU,CAAC,iBAAiB,CAAC;cAC7B;cACA0B,YAAY,CAACC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;YAC3C,CAAE;YAAAxB,QAAA,gBAEF3B,OAAA,CAACnC,YAAY;cAAA8D,QAAA,eACX3B,OAAA,CAACL,gBAAgB;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACfjC,OAAA,CAAClC,YAAY;cAACoE,OAAO,EAAC;YAAqB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAEjBjC,OAAA,CAAC7B,cAAc;YACbsE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YACdb,OAAO,EAAEA,CAAA,KAAM;cACbL,UAAU,CAAC,iBAAiB,CAAC;cAC7B;cACA0B,YAAY,CAACC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;YAC3C,CAAE;YAAAxB,QAAA,gBAEF3B,OAAA,CAACnC,YAAY;cAAA8D,QAAA,eACX3B,OAAA,CAACH,gBAAgB;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACfjC,OAAA,CAAClC,YAAY;cAACoE,OAAO,EAAC;YAAkB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA,eACX,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAAC7B,EAAA,CAvPID,QAAQ;EAAA,QACK1C,WAAW,EACXC,WAAW,EACwBoC,OAAO;AAAA;AAAAsD,EAAA,GAHvDjD,QAAQ;AAyPd,eAAeA,QAAQ;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}