{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Box, Paper, Typography, TextField, Button, Stepper, Step, StepLabel, Grid, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, ListItemSecondaryAction, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, ArrowBack as ArrowBackIcon, ArrowForward as ArrowForwardIcon, Cancel as CancelIcon, CheckCircle as CheckCircleIcon, Warning as WarningIcon, Info as InfoIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form multi-step\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null);\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Definizione dei passi del form\n  const steps = ['Seleziona Cavo', 'Associa Bobina', 'Inserisci Metri', 'Conferma'];\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica la lista delle bobine quando necessario\n  useEffect(() => {\n    if (activeStep === 1) {\n      // Carica le bobine quando si passa al passo \"Associa Bobina\"\n      loadBobine();\n    }\n  }, [activeStep, cantiereId]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response || loadError.code === 'ECONNABORTED' || loadError.message && loadError.message.includes('Network Error')) {\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(`${API_URL}/cavi/${cantiereId}`, {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 30000 // 30 secondi\n            });\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Carica solo le bobine disponibili o in uso\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina => (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') && bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata');\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina => bobina.tipologia === selectedCavo.tipologia && bobina.n_conduttori === selectedCavo.n_conduttori && bobina.sezione === selectedCavo.sezione);\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n          } else {\n            console.log('Nessuna bobina compatibile trovata, mostro tutte le bobine disponibili');\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con ID: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Primo tentativo di ricerca\n      let cavoData;\n      try {\n        cavoData = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n        console.log('Cavo trovato con successo:', cavoData);\n      } catch (searchError) {\n        console.error('Errore nel primo tentativo di ricerca:', searchError);\n\n        // Se è un errore di rete, prova a cercare il cavo nella lista locale\n        if (searchError.isNetworkError) {\n          console.log('Tentativo di ricerca nella lista locale dei cavi...');\n          const cavoLocale = cavi.find(c => c.id_cavo === cavoIdInput.trim());\n          if (cavoLocale) {\n            console.log('Cavo trovato nella lista locale:', cavoLocale);\n            cavoData = cavoLocale;\n          } else {\n            console.error('Cavo non trovato nemmeno nella lista locale');\n            throw searchError; // Rilancia l'errore originale\n          }\n        } else {\n          throw searchError; // Rilancia l'errore se non è di rete\n        }\n      }\n\n      // Verifica se il cavo è già installato\n      if (cavoData.stato_installazione === 'Installato' || cavoData.metratura_reale && cavoData.metratura_reale > 0) {\n        console.log('Cavo già installato, mostra dialogo:', cavoData);\n        // Mostra il dialogo per cavi già posati\n        setAlreadyLaidCavo(cavoData);\n        setShowAlreadyLaidDialog(true);\n        setCaviLoading(false);\n        return;\n      }\n\n      // Verifica se il cavo è SPARE\n      if (cavoData.modificato_manualmente === 3) {\n        console.log('Cavo SPARE trovato:', cavoData);\n        // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n      }\n\n      // Seleziona il cavo e passa al passo successivo\n      console.log('Cavo selezionato, passaggio al passo successivo');\n      handleCavoSelect(cavoData);\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Cavo non trovato o errore nella ricerca';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Passa al passo successivo (Associa Bobina)\n          setActiveStep(1);\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Passa al passo successivo (Associa Bobina)\n      setActiveStep(1);\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Variabile per tenere traccia se è già stata mostrata una notifica\n  const [notificationShown, setNotificationShown] = useState(false);\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    } else {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n        // Chiedi conferma all'utente\n        if (!window.confirm(`ATTENZIONE: I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m).\\n\\nVuoi continuare comunque?`)) {\n          isValid = false;\n        }\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Chiedi conferma all'utente\n          if (!window.confirm(`ATTENZIONE: I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m).\\n\\nQuesto porterà la bobina in stato OVER.\\n\\nVuoi continuare?`)) {\n            isValid = false;\n          }\n        }\n      }\n    }\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      let idBobina = formData.id_bobina;\n      // Se non è selezionata alcuna bobina, imposta esplicitamente a null\n      if (!idBobina || idBobina === '') {\n        idBobina = null;\n        console.log('Nessuna bobina selezionata, impostando a null');\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      const statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      let forceOver = false;\n\n      // Se si usa BOBINA_VUOTA, imposta sempre forceOver a true\n      if (idBobina === 'BOBINA_VUOTA') {\n        forceOver = true;\n        console.log('Forzando operazione per BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        // Anche in questo caso forziamo l'operazione\n        forceOver = true;\n        console.log(`Forzando operazione per metri posati (${metriPosati}) > metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n        if (!window.confirm(confirmMessage)) {\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, forceOver);\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response) {\n        var _error$response$data;\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message;\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          errorMessage = `Cavo o bobina non trovati: ${detail}`;\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || 'Errore sconosciuto';\n      }\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Cerca cavo per ID\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 9,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavo\",\n              variant: \"outlined\",\n              value: cavoIdInput,\n              onChange: e => setCavoIdInput(e.target.value),\n              placeholder: \"Inserisci l'ID del cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: handleSearchCavoById,\n              disabled: caviLoading || !cavoIdInput.trim(),\n              startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 42\n              }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 75\n              }, this),\n              children: \"Cerca\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 682,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Non ci sono cavi disponibili da installare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '400px',\n            overflow: 'auto'\n          },\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 734,\n                    columnNumber: 27\n                  }, this), isCableSpare(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"SPARE\",\n                    color: \"error\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 736,\n                    columnNumber: 29\n                  }, this) : isCableInstalled(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"Installato\",\n                    color: \"success\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 743,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: getCableStateColor(cavo.stato_installazione),\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 750,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [cavo.tipologia || 'N/A', \" - \", cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 761,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 764,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', \" - A: \", cavo.ubicazione_arrivo || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 765,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 768,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A', \" - Metri posati: \", cavo.metratura_reale || '0']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 769,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  edge: \"end\",\n                  onClick: e => {\n                    e.stopPropagation(); // Prevent triggering the ListItem click\n                    setSelectedCavo(cavo);\n                    setShowCavoDetailsDialog(true);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 713,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n        cavo: selectedCavo,\n        compact: true,\n        title: \"Dettagli del cavo selezionato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 814,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold'\n          },\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 821,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                bgcolor: '#f5f5f5',\n                borderRadius: 1,\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold',\n                  color: 'primary.main'\n                },\n                children: \"Informazioni cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 1,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium'\n                    },\n                    children: \"Metri teorici:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 834,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium'\n                    },\n                    children: \"Stato attuale:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 840,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 839,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 6,\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedCavo.stato_installazione || 'N/D',\n                    size: \"small\",\n                    color: getCableStateColor(selectedCavo.stato_installazione),\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 843,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 842,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 828,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                bgcolor: '#f5f5f5',\n                borderRadius: 1,\n                height: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold',\n                  color: 'secondary.main'\n                },\n                children: \"Informazioni bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 17\n              }, this), formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                return bobina ? /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"ID Bobina:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 864,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 863,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: getBobinaNumber(bobina.id_bobina)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 867,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 870,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 873,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 872,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium'\n                      },\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 876,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 875,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 6,\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      label: bobina.stato_bobina || 'N/D',\n                      size: \"small\",\n                      color: getReelStateColor(bobina.stato_bobina),\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 879,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 878,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 862,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Bobina non trovata\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 21\n                }, this);\n              })() : /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: formData.id_bobina === 'BOBINA_VUOTA' ? \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" : \"Nessuna bobina selezionata\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 854,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 826,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 902,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            fullWidth: true,\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 905,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 901,\n          columnNumber: 11\n        }, this), formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: formWarnings.metri_posati\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 924,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 930,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 929,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 820,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 809,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = e => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo && (bobinaEsistente.tipologia !== selectedCavo.tipologia || String(bobinaEsistente.n_conduttori) !== String(selectedCavo.n_conduttori) || String(bobinaEsistente.sezione) !== String(selectedCavo.sezione))) {\n            // Mostra il dialogo per bobine incompatibili\n            setIncompatibleReel(bobinaEsistente);\n            setShowIncompatibleReelDialog(true);\n            return;\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Associa bobina (opzionale)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1039,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          paragraph: true,\n          children: \"Puoi associare una bobina al cavo selezionato oppure utilizzare l'opzione \\\"BOBINA VUOTA\\\" per registrare i metri posati senza associare una bobina specifica.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1044,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1050,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1049,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"Inserimento diretto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1057,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                size: \"small\",\n                fullWidth: true,\n                label: \"Numero bobina\",\n                variant: \"outlined\",\n                placeholder: \"Solo il numero (Y)\",\n                helperText: formErrors.id_bobina_input || \"Inserisci solo il numero della bobina\",\n                error: !!formErrors.id_bobina_input,\n                onBlur: handleBobinaNumberInput,\n                sx: {\n                  mb: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1060,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [\"ID Bobina: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: formData.id_bobina || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1072,\n                  columnNumber: 32\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1071,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1056,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"Selezione dalla lista\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1078,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                size: \"small\",\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"bobina-select-label\",\n                  children: \"Seleziona bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: \"bobina-select-label\",\n                  id: \"bobina-select\",\n                  name: \"id_bobina\",\n                  value: formData.id_bobina,\n                  label: \"Seleziona bobina\",\n                  onChange: handleFormChange,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"BOBINA_VUOTA\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"BOBINA VUOTA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1092,\n                      columnNumber: 25\n                    }, this), \" (nessuna bobina associata)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1091,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1094,\n                    columnNumber: 23\n                  }, this), bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: bobina.id_bobina,\n                    disabled: bobina.metri_residui < parseFloat(formData.metri_posati),\n                    children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A', \" - \", bobina.metri_residui || 0, \" m\"]\n                  }, bobina.id_bobina, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1096,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1083,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: \"Seleziona una bobina o \\\"BOBINA VUOTA\\\"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1105,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1081,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1077,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1054,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Nota\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1114,\n                columnNumber: 19\n              }, this), \": Se selezioni \\\"BOBINA VUOTA\\\", potrai associare una bobina specifica in un secondo momento.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1113,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1112,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1053,\n          columnNumber: 13\n        }, this), !bobineLoading && formData.id_bobina && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: 'background.paper',\n            borderRadius: 1,\n            border: '1px solid #e0e0e0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Dettagli bobina selezionata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1123,\n            columnNumber: 15\n          }, this), (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            if (bobina) {\n              return /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Numero:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1133,\n                      columnNumber: 27\n                    }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1132,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tipologia:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1136,\n                      columnNumber: 27\n                    }, this), \" \", bobina.tipologia || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1135,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Conduttori:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1139,\n                      columnNumber: 27\n                    }, this), \" \", bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1138,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1131,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri totali:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1144,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_totali || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1143,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1147,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1146,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1150,\n                      columnNumber: 27\n                    }, this), \" \", bobina.stato_bobina || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1149,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1142,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1130,\n                columnNumber: 21\n              }, this);\n            }\n            return /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"error\",\n              children: \"Bobina non trovata nel database\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1157,\n              columnNumber: 19\n            }, this);\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1122,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1166,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1043,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1038,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = idBobina => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo,\n          compact: true,\n          title: \"Dettagli del cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Informazioni sull'operazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1227,\n                  columnNumber: 19\n                }, this), \" \", formData.metri_posati, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato Installazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1230,\n                  columnNumber: 19\n                }, this), \" \", statoInstallazione]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1229,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina Associata:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1235,\n                  columnNumber: 19\n                }, this), \" \", numeroBobina]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1234,\n                columnNumber: 17\n              }, this), bobinaInfo && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Residui Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1239,\n                  columnNumber: 21\n                }, this), \" \", bobinaInfo.metri_residui, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1238,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1220,\n          columnNumber: 11\n        }, this), bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1248,\n            columnNumber: 15\n          }, this), \" I metri posati (\", formData.metri_posati, \"m) superano i metri residui della bobina (\", bobinaInfo.metri_residui, \"m). Questo porter\\xE0 la bobina in stato OVER.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1247,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1202,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      // Seleziona Cavo\n      case 1:\n        return renderStep3();\n      // Associa Bobina\n      case 2:\n        return renderStep2();\n      // Inserisci Metri\n      case 3:\n        return renderStep4();\n      // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReel(null);\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    if (!selectedCavo || !incompatibleReel) return;\n    try {\n      setLoading(true);\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoToMatchReel(cantiereId, selectedCavo.id_cavo, incompatibleReel);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, selectedCavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: incompatibleReel.id_bobina\n      });\n      onSuccess(`Caratteristiche del cavo ${selectedCavo.id_cavo} aggiornate per corrispondere alla bobina ${incompatibleReel.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Stepper, {\n      activeStep: activeStep,\n      sx: {\n        mb: 4\n      },\n      children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1351,\n          columnNumber: 13\n        }, this)\n      }, label, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1350,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1348,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2,\n        mb: 4\n      },\n      children: getStepContent(activeStep)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1356,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"secondary\",\n        onClick: activeStep === 0 ? () => navigate('/dashboard/cavi/posa') : handleBack,\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1365,\n          columnNumber: 22\n        }, this),\n        disabled: loading,\n        children: activeStep === 0 ? 'Annulla' : 'Indietro'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: activeStep === steps.length - 1 ? handleSubmit : handleNext,\n        endIcon: activeStep === steps.length - 1 ? /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1375,\n          columnNumber: 54\n        }, this) : /*#__PURE__*/_jsxDEV(ArrowForwardIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1375,\n          columnNumber: 69\n        }, this),\n        disabled: loading || activeStep === 0 && !selectedCavo,\n        children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1379,\n          columnNumber: 13\n        }, this) : activeStep === steps.length - 1 ? 'Salva' : 'Avanti'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1371,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1360,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1392,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavo gi\\xE0 posato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1393,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1391,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1390,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: alreadyLaidCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: alreadyLaidCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1400,\n              columnNumber: 25\n            }, this), \" risulta gi\\xE0 posato (\", alreadyLaidCavo.metratura_reale || 0, \"m).\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1399,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: \"Puoi scegliere di:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1402,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"ul\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata al cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1406,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1408,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1405,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1398,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1418,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1421,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1417,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1413,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1389,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: handleCloseIncompatibleReelDialog,\n      cavo: selectedCavo,\n      bobina: incompatibleReel,\n      onUpdateCavo: handleUpdateCavoToMatchReel,\n      onSelectAnotherReel: handleSelectAnotherReel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1429,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1447,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Dettagli Cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1448,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1446,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1445,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1452,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1451,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          color: \"primary\",\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1455,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1454,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1439,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1347,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"eOv1Es7aHOEmD09DB6icR70NdkA=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Search", "SearchIcon", "Save", "SaveIcon", "ArrowBack", "ArrowBackIcon", "ArrowForward", "ArrowForwardIcon", "Cancel", "CancelIcon", "CheckCircle", "CheckCircleIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "useNavigate", "caviService", "axiosInstance", "IncompatibleReelDialog", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "activeStep", "setActiveStep", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReel", "setIncompatibleReel", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "alreadyLaidCavo", "setAlreadyLaidCavo", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "steps", "loadCavi", "loadBobine", "console", "log", "caviData", "get<PERSON><PERSON>", "length", "loadError", "error", "isNetworkError", "response", "code", "message", "includes", "Promise", "resolve", "setTimeout", "token", "localStorage", "getItem", "API_URL", "defaults", "baseURL", "retryResponse", "get", "headers", "timeout", "data", "retryError", "errorMessage", "detail", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "bobina", "stato_bobina", "tipologia", "n_conduttori", "sezione", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "sort", "a", "b", "metri_residui", "handleSearchCavoById", "trim", "cavoData", "getCavoById", "searchError", "cavoLocale", "find", "c", "stato_installazione", "metratura_reale", "modificato_manualmente", "handleCavoSelect", "status", "cavo", "window", "confirm", "reactivateSpare", "then", "updatedCavo", "catch", "cavoId", "handleFormChange", "e", "name", "value", "target", "validateField", "warning", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "prev", "notificationShown", "setNotificationShown", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "handleNext", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "metriTeorici", "handleSubmit", "idBobina", "statoInstallazione", "forceOver", "confirmMessage", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "_error$response$data", "request", "renderStep1", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "container", "spacing", "alignItems", "item", "xs", "fullWidth", "label", "onChange", "placeholder", "color", "onClick", "disabled", "startIcon", "size", "display", "justifyContent", "my", "severity", "maxHeight", "overflow", "map", "button", "primary", "ml", "secondary", "component", "ubicazione_partenza", "ubicazione_arrivo", "edge", "stopPropagation", "getBobinaNumber", "split", "renderStep2", "compact", "title", "fontWeight", "md", "bgcolor", "borderRadius", "height", "mt", "type", "helperText", "FormHelperTextProps", "renderStep3", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "handleBobinaNumberInput", "toLowerCase", "id_bobina_input", "idBobinaCompleto", "<PERSON><PERSON><PERSON>", "String", "paragraph", "onBlur", "id", "labelId", "border", "metri_totali", "renderStep4", "bobinaInfo", "getStepContent", "step", "handleCloseAlreadyLaidDialog", "handleModifyReel", "handleSelectAnotherCable", "handleCloseIncompatibleReelDialog", "handleUpdateCavoToMatchReel", "updateCavoToMatchReel", "handleSelectAnotherReel", "endIcon", "open", "onClose", "max<PERSON><PERSON><PERSON>", "gap", "mr", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Stepper,\n  Step,\n  StepLabel,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  ArrowBack as ArrowBackIcon,\n  ArrowForward as ArrowForwardIcon,\n  Cancel as CancelIcon,\n  CheckCircle as CheckCircleIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form multi-step\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null);\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Definizione dei passi del form\n  const steps = ['Seleziona Cavo', 'Associa Bobina', 'Inserisci Metri', 'Conferma'];\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica la lista delle bobine quando necessario\n  useEffect(() => {\n    if (activeStep === 1) {\n      // Carica le bobine quando si passa al passo \"Associa Bobina\"\n      loadBobine();\n    }\n  }, [activeStep, cantiereId]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response ||\n            loadError.code === 'ECONNABORTED' ||\n            (loadError.message && loadError.message.includes('Network Error'))) {\n\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(\n              `${API_URL}/cavi/${cantiereId}`,\n              {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 30000 // 30 secondi\n              }\n            );\n\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Carica solo le bobine disponibili o in uso\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina =>\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') &&\n        bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata'\n      );\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina =>\n            bobina.tipologia === selectedCavo.tipologia &&\n            bobina.n_conduttori === selectedCavo.n_conduttori &&\n            bobina.sezione === selectedCavo.sezione\n          );\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n          } else {\n            console.log('Nessuna bobina compatibile trovata, mostro tutte le bobine disponibili');\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con ID: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Primo tentativo di ricerca\n      let cavoData;\n      try {\n        cavoData = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n        console.log('Cavo trovato con successo:', cavoData);\n      } catch (searchError) {\n        console.error('Errore nel primo tentativo di ricerca:', searchError);\n\n        // Se è un errore di rete, prova a cercare il cavo nella lista locale\n        if (searchError.isNetworkError) {\n          console.log('Tentativo di ricerca nella lista locale dei cavi...');\n          const cavoLocale = cavi.find(c => c.id_cavo === cavoIdInput.trim());\n\n          if (cavoLocale) {\n            console.log('Cavo trovato nella lista locale:', cavoLocale);\n            cavoData = cavoLocale;\n          } else {\n            console.error('Cavo non trovato nemmeno nella lista locale');\n            throw searchError; // Rilancia l'errore originale\n          }\n        } else {\n          throw searchError; // Rilancia l'errore se non è di rete\n        }\n      }\n\n      // Verifica se il cavo è già installato\n      if (cavoData.stato_installazione === 'Installato' || (cavoData.metratura_reale && cavoData.metratura_reale > 0)) {\n        console.log('Cavo già installato, mostra dialogo:', cavoData);\n        // Mostra il dialogo per cavi già posati\n        setAlreadyLaidCavo(cavoData);\n        setShowAlreadyLaidDialog(true);\n        setCaviLoading(false);\n        return;\n      }\n\n      // Verifica se il cavo è SPARE\n      if (cavoData.modificato_manualmente === 3) {\n        console.log('Cavo SPARE trovato:', cavoData);\n        // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n      }\n\n      // Seleziona il cavo e passa al passo successivo\n      console.log('Cavo selezionato, passaggio al passo successivo');\n      handleCavoSelect(cavoData);\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Cavo non trovato o errore nella ricerca';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Passa al passo successivo (Associa Bobina)\n          setActiveStep(1);\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Passa al passo successivo (Associa Bobina)\n      setActiveStep(1);\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Variabile per tenere traccia se è già stata mostrata una notifica\n  const [notificationShown, setNotificationShown] = useState(false);\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    } else {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n        // Chiedi conferma all'utente\n        if (!window.confirm(`ATTENZIONE: I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m).\\n\\nVuoi continuare comunque?`)) {\n          isValid = false;\n        }\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Chiedi conferma all'utente\n          if (!window.confirm(`ATTENZIONE: I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m).\\n\\nQuesto porterà la bobina in stato OVER.\\n\\nVuoi continuare?`)) {\n            isValid = false;\n          }\n        }\n      }\n    }\n\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      let idBobina = formData.id_bobina;\n      // Se non è selezionata alcuna bobina, imposta esplicitamente a null\n      if (!idBobina || idBobina === '') {\n        idBobina = null;\n        console.log('Nessuna bobina selezionata, impostando a null');\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      const statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      let forceOver = false;\n\n      // Se si usa BOBINA_VUOTA, imposta sempre forceOver a true\n      if (idBobina === 'BOBINA_VUOTA') {\n        forceOver = true;\n        console.log('Forzando operazione per BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        // Anche in questo caso forziamo l'operazione\n        forceOver = true;\n        console.log(`Forzando operazione per metri posati (${metriPosati}) > metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n        if (!window.confirm(confirmMessage)) {\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n      if (error.response) {\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = error.response.data?.detail || error.message;\n\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          errorMessage = `Cavo o bobina non trovati: ${detail}`;\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || 'Errore sconosciuto';\n      }\n\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID */}\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Cerca cavo per ID\n          </Typography>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={9}>\n              <TextField\n                fullWidth\n                label=\"ID Cavo\"\n                variant=\"outlined\"\n                value={cavoIdInput}\n                onChange={(e) => setCavoIdInput(e.target.value)}\n                placeholder=\"Inserisci l'ID del cavo\"\n              />\n            </Grid>\n            <Grid item xs={3}>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={handleSearchCavoById}\n                disabled={caviLoading || !cavoIdInput.trim()}\n                startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n              >\n                Cerca\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Lista cavi */}\n        <Paper sx={{ p: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\">\n              Non ci sono cavi disponibili da installare.\n            </Alert>\n          ) : (\n            <List sx={{ maxHeight: '400px', overflow: 'auto' }}>\n              {cavi.map((cavo) => (\n                <React.Fragment key={cavo.id_cavo}>\n                  <ListItem button onClick={() => handleCavoSelect(cavo)}>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle1\">{cavo.id_cavo}</Typography>\n                          {isCableSpare(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"SPARE\"\n                              color=\"error\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : isCableInstalled(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"Installato\"\n                              color=\"success\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : (\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione}\n                              color={getCableStateColor(cavo.stato_installazione)}\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <>\n                          <Typography variant=\"body2\" component=\"span\">\n                            {cavo.tipologia || 'N/A'} - {cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Da: {cavo.ubicazione_partenza || 'N/A'} - A: {cavo.ubicazione_arrivo || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Metri teorici: {cavo.metri_teorici || 'N/A'} - Metri posati: {cavo.metratura_reale || '0'}\n                          </Typography>\n                        </>\n                      }\n                    />\n                    <ListItemSecondaryAction>\n                      <IconButton edge=\"end\" onClick={(e) => {\n                        e.stopPropagation(); // Prevent triggering the ListItem click\n                        setSelectedCavo(cavo);\n                        setShowCavoDetailsDialog(true);\n                      }}>\n                        <InfoIcon />\n                      </IconButton>\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                  <Divider />\n                </React.Fragment>\n              ))}\n            </List>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserisci metri posati\n        </Typography>\n\n        <CavoDetailsView\n          cavo={selectedCavo}\n          compact={true}\n          title=\"Dettagli del cavo selezionato\"\n        />\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom sx={{ fontWeight: 'bold' }}>\n            Inserisci i metri posati\n          </Typography>\n\n          {/* Informazioni sul cavo e sulla bobina in una griglia */}\n          <Grid container spacing={2} sx={{ mb: 3 }}>\n            <Grid item xs={12} md={6}>\n              <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, height: '100%' }}>\n                <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>\n                  Informazioni cavo\n                </Typography>\n                <Grid container spacing={1}>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Metri teorici:</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\">{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Stato attuale:</Typography>\n                  </Grid>\n                  <Grid item xs={6}>\n                    <Chip\n                      label={selectedCavo.stato_installazione || 'N/D'}\n                      size=\"small\"\n                      color={getCableStateColor(selectedCavo.stato_installazione)}\n                      variant=\"outlined\"\n                    />\n                  </Grid>\n                </Grid>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, height: '100%' }}>\n                <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'secondary.main' }}>\n                  Informazioni bobina\n                </Typography>\n                {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n                  const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                  return bobina ? (\n                    <Grid container spacing={1}>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>ID Bobina:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\">{getBobinaNumber(bobina.id_bobina)}</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Metri residui:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\">{bobina.metri_residui || 0} m</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Typography variant=\"body2\" sx={{ fontWeight: 'medium' }}>Stato:</Typography>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Chip\n                          label={bobina.stato_bobina || 'N/D'}\n                          size=\"small\"\n                          color={getReelStateColor(bobina.stato_bobina)}\n                          variant=\"outlined\"\n                        />\n                      </Grid>\n                    </Grid>\n                  ) : (\n                    <Typography variant=\"body2\">Bobina non trovata</Typography>\n                  );\n                })() : (\n                  <Typography variant=\"body2\">\n                    {formData.id_bobina === 'BOBINA_VUOTA' ?\n                      \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" :\n                      \"Nessuna bobina selezionata\"}\n                  </Typography>\n                )}\n              </Box>\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, mb: 3 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Metratura posata\n            </Typography>\n            <TextField\n              size=\"small\"\n              fullWidth\n              label=\"Metri posati\"\n              variant=\"outlined\"\n              name=\"metri_posati\"\n              type=\"number\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              sx={{ mb: 1 }}\n            />\n          </Box>\n\n          {formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              {formWarnings.metri_posati}\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\">\n              Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n            </Typography>\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = (e) => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo && (\n              bobinaEsistente.tipologia !== selectedCavo.tipologia ||\n              String(bobinaEsistente.n_conduttori) !== String(selectedCavo.n_conduttori) ||\n              String(bobinaEsistente.sezione) !== String(selectedCavo.sezione)\n          )) {\n            // Mostra il dialogo per bobine incompatibili\n            setIncompatibleReel(bobinaEsistente);\n            setShowIncompatibleReelDialog(true);\n            return;\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Associa bobina (opzionale)\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"body1\" paragraph>\n            Puoi associare una bobina al cavo selezionato oppure utilizzare l'opzione \"BOBINA VUOTA\" per registrare i metri posati senza associare una bobina specifica.\n          </Typography>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              <Grid container spacing={3}>\n                {/* Colonna sinistra: Input diretto */}\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                    Inserimento diretto\n                  </Typography>\n                  <TextField\n                    size=\"small\"\n                    fullWidth\n                    label=\"Numero bobina\"\n                    variant=\"outlined\"\n                    placeholder=\"Solo il numero (Y)\"\n                    helperText={formErrors.id_bobina_input || \"Inserisci solo il numero della bobina\"}\n                    error={!!formErrors.id_bobina_input}\n                    onBlur={handleBobinaNumberInput}\n                    sx={{ mb: 1 }}\n                  />\n                  <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                    ID Bobina: <strong>{formData.id_bobina || '-'}</strong>\n                  </Typography>\n                </Grid>\n\n                {/* Colonna destra: Selezione dalla lista */}\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                    Selezione dalla lista\n                  </Typography>\n                  <FormControl fullWidth size=\"small\">\n                    <InputLabel id=\"bobina-select-label\">Seleziona bobina</InputLabel>\n                    <Select\n                      labelId=\"bobina-select-label\"\n                      id=\"bobina-select\"\n                      name=\"id_bobina\"\n                      value={formData.id_bobina}\n                      label=\"Seleziona bobina\"\n                      onChange={handleFormChange}\n                    >\n                      <MenuItem value=\"BOBINA_VUOTA\">\n                        <strong>BOBINA VUOTA</strong> (nessuna bobina associata)\n                      </MenuItem>\n                      <Divider />\n                      {bobine.map((bobina) => (\n                        <MenuItem\n                          key={bobina.id_bobina}\n                          value={bobina.id_bobina}\n                          disabled={bobina.metri_residui < parseFloat(formData.metri_posati)}\n                        >\n                          {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'} - {bobina.metri_residui || 0} m\n                        </MenuItem>\n                      ))}\n                    </Select>\n                    <FormHelperText>\n                      Seleziona una bobina o \"BOBINA VUOTA\"\n                    </FormHelperText>\n                  </FormControl>\n                </Grid>\n              </Grid>\n\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                <Typography variant=\"body2\">\n                  <strong>Nota</strong>: Se selezioni \"BOBINA VUOTA\", potrai associare una bobina specifica in un secondo momento.\n                </Typography>\n              </Alert>\n            </Box>\n          )}\n\n          {/* Mostra dettagli della bobina selezionata */}\n          {!bobineLoading && formData.id_bobina && (\n            <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n              <Typography variant=\"subtitle2\" gutterBottom>\n                Dettagli bobina selezionata\n              </Typography>\n              {(() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                if (bobina) {\n                  return (\n                    <Grid container spacing={2}>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Numero:</strong> {getBobinaNumber(bobina.id_bobina)}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Tipologia:</strong> {bobina.tipologia || 'N/A'}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Conduttori:</strong> {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Metri totali:</strong> {bobina.metri_totali || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Metri residui:</strong> {bobina.metri_residui || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Stato:</strong> {bobina.stato_bobina || 'N/A'}\n                        </Typography>\n                      </Grid>\n                    </Grid>\n                  );\n                }\n                return (\n                  <Typography variant=\"body2\" color=\"error\">\n                    Bobina non trovata nel database\n                  </Typography>\n                );\n              })()}\n            </Box>\n          )}\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = (idBobina) => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          {/* Dettagli del cavo */}\n          <CavoDetailsView\n            cavo={selectedCavo}\n            compact={true}\n            title=\"Dettagli del cavo\"\n          />\n\n          {/* Informazioni sull'operazione */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Informazioni sull'operazione:\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Metri Posati:</strong> {formData.metri_posati} m\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato Installazione:</strong> {statoInstallazione}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Bobina Associata:</strong> {numeroBobina}\n                </Typography>\n                {bobinaInfo && (\n                  <Typography variant=\"body2\">\n                    <strong>Metri Residui Bobina:</strong> {bobinaInfo.metri_residui} m\n                  </Typography>\n                )}\n              </Grid>\n            </Grid>\n          </Box>\n\n          {bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mt: 3 }}>\n              <strong>Attenzione:</strong> I metri posati ({formData.metri_posati}m) superano i metri residui della bobina ({bobinaInfo.metri_residui}m).\n              Questo porterà la bobina in stato OVER.\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1(); // Seleziona Cavo\n      case 1:\n        return renderStep3(); // Associa Bobina\n      case 2:\n        return renderStep2(); // Inserisci Metri\n      case 3:\n        return renderStep4(); // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReel(null);\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    if (!selectedCavo || !incompatibleReel) return;\n\n    try {\n      setLoading(true);\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoToMatchReel(cantiereId, selectedCavo.id_cavo, incompatibleReel);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, selectedCavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: incompatibleReel.id_bobina\n      });\n\n      onSuccess(`Caratteristiche del cavo ${selectedCavo.id_cavo} aggiornate per corrispondere alla bobina ${incompatibleReel.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n\n  return (\n    <Box>\n      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>\n        {steps.map((label) => (\n          <Step key={label}>\n            <StepLabel>{label}</StepLabel>\n          </Step>\n        ))}\n      </Stepper>\n\n      <Box sx={{ mt: 2, mb: 4 }}>\n        {getStepContent(activeStep)}\n      </Box>\n\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\n        <Button\n          variant=\"outlined\"\n          color=\"secondary\"\n          onClick={activeStep === 0 ? () => navigate('/dashboard/cavi/posa') : handleBack}\n          startIcon={<ArrowBackIcon />}\n          disabled={loading}\n        >\n          {activeStep === 0 ? 'Annulla' : 'Indietro'}\n        </Button>\n\n        <Button\n          variant=\"contained\"\n          color=\"primary\"\n          onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}\n          endIcon={activeStep === steps.length - 1 ? <SaveIcon /> : <ArrowForwardIcon />}\n          disabled={loading || (activeStep === 0 && !selectedCavo)}\n        >\n          {loading ? (\n            <CircularProgress size={24} />\n          ) : activeStep === steps.length - 1 ? (\n            'Salva'\n          ) : (\n            'Avanti'\n          )}\n        </Button>\n      </Box>\n\n      {/* Dialogo per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">Cavo già posato</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          {alreadyLaidCavo && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body1\" paragraph>\n                Il cavo <strong>{alreadyLaidCavo.id_cavo}</strong> risulta già posato ({alreadyLaidCavo.metratura_reale || 0}m).\n              </Typography>\n              <Typography variant=\"body1\" paragraph>\n                Puoi scegliere di:\n              </Typography>\n              <Typography variant=\"body2\" component=\"ul\">\n                <li>Modificare la bobina associata al cavo</li>\n                <li>Selezionare un altro cavo</li>\n                <li>Annullare l'operazione</li>\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={handleCloseIncompatibleReelDialog}\n        cavo={selectedCavo}\n        bobina={incompatibleReel}\n        onUpdateCavo={handleUpdateCavoToMatchReel}\n        onSelectAnotherReel={handleSelectAnotherReel}\n      />\n\n      {/* Dialogo per visualizzare i dettagli del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <InfoIcon color=\"primary\" />\n            <Typography variant=\"h6\">Dettagli Cavo</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <CavoDetailsView cavo={selectedCavo} />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)} color=\"primary\">\n            Chiudi\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,aAAa,EAC1BC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC0E,OAAO,EAAEC,UAAU,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8E,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAACgF,IAAI,EAAEC,OAAO,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACkF,MAAM,EAAEC,SAAS,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACoF,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsF,WAAW,EAAEC,cAAc,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACwF,QAAQ,EAAEC,WAAW,CAAC,GAAGzF,QAAQ,CAAC;IACvC0F,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC+F,YAAY,EAAEC,eAAe,CAAC,GAAGhG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAACiG,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGlG,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACmG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACqG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGtG,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACuG,eAAe,EAAEC,kBAAkB,CAAC,GAAGxG,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1G,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACA,MAAM2G,KAAK,GAAG,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,UAAU,CAAC;;EAEjF;EACA1G,SAAS,CAAC,MAAM;IACd2G,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACzC,UAAU,CAAC,CAAC;;EAEhB;EACAlE,SAAS,CAAC,MAAM;IACd,IAAIuE,UAAU,KAAK,CAAC,EAAE;MACpB;MACAqC,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACrC,UAAU,EAAEL,UAAU,CAAC,CAAC;;EAE5B;EACA,MAAMyC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF/B,cAAc,CAAC,IAAI,CAAC;MACpBiC,OAAO,CAACC,GAAG,CAAC,oCAAoC5C,UAAU,KAAK,CAAC;;MAEhE;MACA,IAAI;QACF,MAAM6C,QAAQ,GAAG,MAAMjE,WAAW,CAACkE,OAAO,CAAC9C,UAAU,CAAC;QACtD2C,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACE,MAAM,OAAO,CAAC;;QAE/C;QACA;QACAjC,OAAO,CAAC+B,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOG,SAAS,EAAE;QAClBL,OAAO,CAACM,KAAK,CAAC,qDAAqD,EAAED,SAAS,CAAC;;QAE/E;QACA,IAAIA,SAAS,CAACE,cAAc,IAAI,CAACF,SAAS,CAACG,QAAQ,IAC/CH,SAAS,CAACI,IAAI,KAAK,cAAc,IAChCJ,SAAS,CAACK,OAAO,IAAIL,SAAS,CAACK,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAE,EAAE;UAEtEX,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtE,MAAM,IAAIW,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvD,IAAI;YACF;YACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC3C,MAAMC,OAAO,GAAGhF,aAAa,CAACiF,QAAQ,CAACC,OAAO;;YAE9C;YACA,MAAMC,aAAa,GAAG,MAAMjI,KAAK,CAACkI,GAAG,CACnC,GAAGJ,OAAO,SAAS7D,UAAU,EAAE,EAC/B;cACEkE,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUR,KAAK;cAClC,CAAC;cACDS,OAAO,EAAE,KAAK,CAAC;YACjB,CACF,CAAC;YAEDxB,OAAO,CAACC,GAAG,CAAC,2CAA2CoB,aAAa,CAACI,IAAI,CAACrB,MAAM,OAAO,CAAC;YACxFjC,OAAO,CAACkD,aAAa,CAACI,IAAI,CAAC;UAC7B,CAAC,CAAC,OAAOC,UAAU,EAAE;YACnB1B,OAAO,CAACM,KAAK,CAAC,uCAAuC,EAAEoB,UAAU,CAAC;YAClE,MAAMA,UAAU;UAClB;QACF,CAAC,MAAM;UACL,MAAMrB,SAAS;QACjB;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIqB,YAAY,GAAG,iCAAiC;MAEpD,IAAIrB,KAAK,CAACC,cAAc,EAAE;QACxBoB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIrB,KAAK,CAACsB,MAAM,EAAE;QACvBD,YAAY,GAAGrB,KAAK,CAACsB,MAAM;MAC7B,CAAC,MAAM,IAAItB,KAAK,CAACI,OAAO,EAAE;QACxBiB,YAAY,GAAGrB,KAAK,CAACI,OAAO;MAC9B;MAEAnD,OAAO,CAACoE,YAAY,CAAC;IACvB,CAAC,SAAS;MACR5D,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMgC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF9B,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAM4D,UAAU,GAAG,MAAM/E,gBAAgB,CAACgF,SAAS,CAACzE,UAAU,CAAC;;MAE/D;MACA,IAAI0E,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM,IAC/C,CAACA,MAAM,CAACC,YAAY,KAAK,aAAa,IAAID,MAAM,CAACC,YAAY,KAAK,QAAQ,KAC1ED,MAAM,CAACC,YAAY,KAAK,MAAM,IAAID,MAAM,CAACC,YAAY,KAAK,WAC5D,CAAC;;MAED;MACA,IAAI5D,YAAY,EAAE;QAChB;QACA,IAAIA,YAAY,CAAC6D,SAAS,IAAI7D,YAAY,CAAC8D,YAAY,IAAI9D,YAAY,CAAC+D,OAAO,EAAE;UAC/E,MAAMC,iBAAiB,GAAGP,kBAAkB,CAACC,MAAM,CAACC,MAAM,IACxDA,MAAM,CAACE,SAAS,KAAK7D,YAAY,CAAC6D,SAAS,IAC3CF,MAAM,CAACG,YAAY,KAAK9D,YAAY,CAAC8D,YAAY,IACjDH,MAAM,CAACI,OAAO,KAAK/D,YAAY,CAAC+D,OAClC,CAAC;;UAED;UACA,IAAIC,iBAAiB,CAAClC,MAAM,GAAG,CAAC,EAAE;YAChC2B,kBAAkB,GAAGO,iBAAiB;UACxC,CAAC,MAAM;YACLtC,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;UACvF;QACF;;QAEA;QACA8B,kBAAkB,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;MACtE;MAEArE,SAAS,CAAC0D,kBAAkB,CAAC;IAC/B,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D/C,OAAO,CAAC,uCAAuC,IAAI+C,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRzC,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM0E,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACnE,WAAW,CAACoE,IAAI,CAAC,CAAC,EAAE;MACvBrF,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFQ,cAAc,CAAC,IAAI,CAAC;MACpBiC,OAAO,CAACC,GAAG,CAAC,wBAAwBzB,WAAW,CAACoE,IAAI,CAAC,CAAC,iBAAiBvF,UAAU,EAAE,CAAC;;MAEpF;MACA,IAAIwF,QAAQ;MACZ,IAAI;QACFA,QAAQ,GAAG,MAAM5G,WAAW,CAAC6G,WAAW,CAACzF,UAAU,EAAEmB,WAAW,CAACoE,IAAI,CAAC,CAAC,CAAC;QACxE5C,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4C,QAAQ,CAAC;MACrD,CAAC,CAAC,OAAOE,WAAW,EAAE;QACpB/C,OAAO,CAACM,KAAK,CAAC,wCAAwC,EAAEyC,WAAW,CAAC;;QAEpE;QACA,IAAIA,WAAW,CAACxC,cAAc,EAAE;UAC9BP,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE,MAAM+C,UAAU,GAAG9E,IAAI,CAAC+E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtE,OAAO,KAAKJ,WAAW,CAACoE,IAAI,CAAC,CAAC,CAAC;UAEnE,IAAII,UAAU,EAAE;YACdhD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE+C,UAAU,CAAC;YAC3DH,QAAQ,GAAGG,UAAU;UACvB,CAAC,MAAM;YACLhD,OAAO,CAACM,KAAK,CAAC,6CAA6C,CAAC;YAC5D,MAAMyC,WAAW,CAAC,CAAC;UACrB;QACF,CAAC,MAAM;UACL,MAAMA,WAAW,CAAC,CAAC;QACrB;MACF;;MAEA;MACA,IAAIF,QAAQ,CAACM,mBAAmB,KAAK,YAAY,IAAKN,QAAQ,CAACO,eAAe,IAAIP,QAAQ,CAACO,eAAe,GAAG,CAAE,EAAE;QAC/GpD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE4C,QAAQ,CAAC;QAC7D;QACAnD,kBAAkB,CAACmD,QAAQ,CAAC;QAC5BrD,wBAAwB,CAAC,IAAI,CAAC;QAC9BzB,cAAc,CAAC,KAAK,CAAC;QACrB;MACF;;MAEA;MACA,IAAI8E,QAAQ,CAACQ,sBAAsB,KAAK,CAAC,EAAE;QACzCrD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE4C,QAAQ,CAAC;QAC5C;MACF;;MAEA;MACA7C,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9DqD,gBAAgB,CAACT,QAAQ,CAAC;IAC5B,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;MAEtD;MACA,IAAIqB,YAAY,GAAG,yCAAyC;MAE5D,IAAIrB,KAAK,CAACC,cAAc,EAAE;QACxBoB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIrB,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;QAC/B5B,YAAY,GAAG,gBAAgBnD,WAAW,CAACoE,IAAI,CAAC,CAAC,8BAA8BvF,UAAU,EAAE;MAC7F,CAAC,MAAM,IAAIiD,KAAK,CAACsB,MAAM,EAAE;QACvBD,YAAY,GAAGrB,KAAK,CAACsB,MAAM;MAC7B,CAAC,MAAM,IAAItB,KAAK,CAACI,OAAO,EAAE;QACxBiB,YAAY,GAAGrB,KAAK,CAACI,OAAO;MAC9B;MAEAnD,OAAO,CAACoE,YAAY,CAAC;IACvB,CAAC,SAAS;MACR5D,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMuF,gBAAgB,GAAIE,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACL,mBAAmB,KAAK,YAAY,IAAKK,IAAI,CAACJ,eAAe,IAAII,IAAI,CAACJ,eAAe,GAAG,CAAE,EAAE;MACnG;MACA1D,kBAAkB,CAAC8D,IAAI,CAAC;MACxBhE,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IACA;IAAA,KACK,IAAIgE,IAAI,CAACH,sBAAsB,KAAK,CAAC,EAAE;MAC1C;MACA,IAAII,MAAM,CAACC,OAAO,CAAC,WAAWF,IAAI,CAAC5E,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACA+E,eAAe,CAACH,IAAI,CAAC5E,OAAO,CAAC,CAACgF,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAGL,IAAI;YAAEH,sBAAsB,EAAE;UAAE,CAAC;UAC1D9E,eAAe,CAACsF,WAAW,CAAC;UAC5BlF,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAEiF,WAAW,CAACjF,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAlB,aAAa,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAACmG,KAAK,CAACxD,KAAK,IAAI;UAChBN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvE/C,OAAO,CAAC,kDAAkD,IAAI+C,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACAnC,eAAe,CAACiF,IAAI,CAAC;MACrB7E,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAE4E,IAAI,CAAC5E,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAlB,aAAa,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMgG,eAAe,GAAG,MAAOI,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAM9H,WAAW,CAAC0H,eAAe,CAACtG,UAAU,EAAE0G,MAAM,CAAC;MACrDzG,SAAS,CAAC,QAAQyG,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOzD,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvE/C,OAAO,CAAC,kDAAkD,IAAI+C,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMJ,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM0D,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCzF,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACwF,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACAE,aAAa,CAACH,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAACH,IAAI,EAAEC,KAAK,KAAK;IACrC,IAAI7D,KAAK,GAAG,IAAI;IAChB,IAAIgE,OAAO,GAAG,IAAI;IAElB,IAAIJ,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACvB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCtC,KAAK,GAAG,uCAAuC;QAC/C,OAAO,KAAK;MACd;;MAEA;MACA,IAAIiE,KAAK,CAACC,UAAU,CAACL,KAAK,CAAC,CAAC,IAAIK,UAAU,CAACL,KAAK,CAAC,IAAI,CAAC,EAAE;QACtD7D,KAAK,GAAG,sCAAsC;QAC9C,OAAO,KAAK;MACd;MAEA,MAAMmE,WAAW,GAAGD,UAAU,CAACL,KAAK,CAAC;;MAErC;MACA,IAAI7F,YAAY,IAAIA,YAAY,CAACoG,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAClG,YAAY,CAACoG,aAAa,CAAC,EAAE;QACtGJ,OAAO,GAAG,mBAAmBG,WAAW,yCAAyCnG,YAAY,CAACoG,aAAa,IAAI;MACjH;;MAEA;MACA,IAAIhG,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC/D,MAAMmD,MAAM,GAAG7D,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAImD,MAAM,IAAIwC,WAAW,GAAGD,UAAU,CAACvC,MAAM,CAACS,aAAa,CAAC,EAAE;UAC5D4B,OAAO,GAAG,mBAAmBG,WAAW,6CAA6CxC,MAAM,CAACS,aAAa,oCAAoC;QAC/I;MACF;IACF;;IAEA;IACA1D,aAAa,CAAC2F,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACT,IAAI,GAAG5D;IACV,CAAC,CAAC,CAAC;;IAEH;IACApB,eAAe,CAACyF,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACT,IAAI,GAAGI;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAAChE,KAAK;EACf,CAAC;;EAED;EACA,MAAM,CAACsE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3L,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM4L,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;;IAEnB;IACAJ,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACA,IAAI,CAACnG,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAAC+D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEoC,MAAM,CAACnG,YAAY,GAAG,uCAAuC;MAC7DkG,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAIR,KAAK,CAACC,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAI2F,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7FmG,MAAM,CAACnG,YAAY,GAAG,sCAAsC;MAC5DkG,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM;MACL,MAAMN,WAAW,GAAGD,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAIP,YAAY,IAAIA,YAAY,CAACoG,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAClG,YAAY,CAACoG,aAAa,CAAC,EAAE;QACtGO,QAAQ,CAACpG,YAAY,GAAG,mBAAmB4F,WAAW,yCAAyCnG,YAAY,CAACoG,aAAa,IAAI;QAC7HG,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;;QAE5B;QACA,IAAI,CAACpB,MAAM,CAACC,OAAO,CAAC,+BAA+Be,WAAW,yCAAyCnG,YAAY,CAACoG,aAAa,kCAAkC,CAAC,EAAE;UACpKK,OAAO,GAAG,KAAK;QACjB;MACF;;MAEA;MACA,IAAIA,OAAO,IAAIrG,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC1E,MAAMmD,MAAM,GAAG7D,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAImD,MAAM,IAAIwC,WAAW,GAAGD,UAAU,CAACvC,MAAM,CAACS,aAAa,CAAC,EAAE;UAC5DuC,QAAQ,CAACpG,YAAY,GAAG,mBAAmB4F,WAAW,6CAA6CxC,MAAM,CAACS,aAAa,oCAAoC;UAC3JmC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;;UAE5B;UACA,IAAI,CAACpB,MAAM,CAACC,OAAO,CAAC,+BAA+Be,WAAW,6CAA6CxC,MAAM,CAACS,aAAa,oEAAoE,CAAC,EAAE;YACpMqC,OAAO,GAAG,KAAK;UACjB;QACF;MACF;IACF;IAEA/F,aAAa,CAACgG,MAAM,CAAC;IACrB9F,eAAe,CAAC+F,QAAQ,CAAC;IACzB,OAAOF,OAAO;EAChB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIxH,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAACoH,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF,CAAC,MAAM,IAAIpH,UAAU,KAAK,CAAC,EAAE;MAC3B;MACAqC,UAAU,CAAC,CAAC;IACd;IAEApC,aAAa,CAAEwH,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBzH,aAAa,CAAEwH,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxB1H,aAAa,CAAC,CAAC,CAAC;IAChBY,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA;EACA,MAAMoG,2BAA2B,GAAGA,CAACb,WAAW,EAAEc,YAAY,KAAK;IACjE,OAAOhJ,mBAAmB,CAACkI,WAAW,EAAEc,YAAY,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF3H,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAACiH,YAAY,CAAC,CAAC,EAAE;QACnBjH,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAM4G,WAAW,GAAGD,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAI4G,QAAQ,GAAG/G,QAAQ,CAACI,SAAS;MACjC;MACA,IAAI,CAAC2G,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;QAChCA,QAAQ,GAAG,IAAI;QACfzF,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC9D,CAAC,MAAM,IAAIwF,QAAQ,KAAK,cAAc,EAAE;QACtCzF,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAwF,QAAQ,GAAG,cAAc;MAC3B,CAAC,MAAM;QACLzF,OAAO,CAACC,GAAG,CAAC,wBAAwBwF,QAAQ,EAAE,CAAC;MACjD;;MAEA;MACA,MAAMC,kBAAkB,GAAGJ,2BAA2B,CAACb,WAAW,EAAEnG,YAAY,CAACoG,aAAa,CAAC;;MAE/F;MACA,IAAIiB,SAAS,GAAG,KAAK;;MAErB;MACA,IAAIF,QAAQ,KAAK,cAAc,EAAE;QAC/BE,SAAS,GAAG,IAAI;QAChB3F,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACrD;MACA;MAAA,KACK,IAAIwF,QAAQ,IAAIA,QAAQ,KAAK,cAAc,EAAE;QAChD,MAAMxD,MAAM,GAAG7D,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAK2G,QAAQ,CAAC;QACzD,IAAIxD,MAAM,IAAIwC,WAAW,GAAGD,UAAU,CAACvC,MAAM,CAACS,aAAa,CAAC,EAAE;UAC5DiD,SAAS,GAAG,IAAI;UAChB3F,OAAO,CAACC,GAAG,CAAC,qCAAqCwF,QAAQ,iCAAiC,CAAC;QAC7F;MACF;;MAEA;MACA,IAAInH,YAAY,IAAIA,YAAY,CAACoG,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAClG,YAAY,CAACoG,aAAa,CAAC,EAAE;QACtG;QACAiB,SAAS,GAAG,IAAI;QAChB3F,OAAO,CAACC,GAAG,CAAC,yCAAyCwE,WAAW,sBAAsBnG,YAAY,CAACoG,aAAa,GAAG,CAAC;MACtH;;MAEA;MACA1E,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzB5C,UAAU;QACV0G,MAAM,EAAErF,QAAQ,CAACE,OAAO;QACxB6F,WAAW;QACXgB,QAAQ;QACRE,SAAS;QACTD;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACd,iBAAiB,EAAE;QACtB,MAAMgB,cAAc,GAAG,qCAAqClH,QAAQ,CAACE,OAAO,QAAQ6F,WAAW,WAAW;QAC1G,IAAI,CAAChB,MAAM,CAACC,OAAO,CAACkC,cAAc,CAAC,EAAE;UACnC/H,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACAmC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE5C,UAAU,CAAC;MACxC2C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEvB,QAAQ,CAACE,OAAO,CAAC;MAC3CoB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEwE,WAAW,CAAC;MAC3CzE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEwF,QAAQ,EAAE,OAAOA,QAAQ,CAAC;MACtDzF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE0F,SAAS,CAAC;MAEtC,MAAM1J,WAAW,CAAC4J,iBAAiB,CACjCxI,UAAU,EACVqB,QAAQ,CAACE,OAAO,EAChB6F,WAAW,EACXgB,QAAQ,EACRE,SACF,CAAC;;MAED;MACA,IAAIG,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;MAC9F,IAAID,QAAQ,KAAK,cAAc,EAAE;QAC/BK,cAAc,IAAI,iCAAiC;MACrD,CAAC,MAAM,IAAIL,QAAQ,EAAE;QACnB,MAAMxD,MAAM,GAAG7D,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAK2G,QAAQ,CAAC;QACzD,IAAIxD,MAAM,EAAE;UACV6D,cAAc,IAAI,gCAAgCL,QAAQ,EAAE;QAC9D;MACF;;MAEA;MACAnI,SAAS,CAACwI,cAAc,CAAC;;MAEzB;MACAT,WAAW,CAAC,CAAC;;MAEb;MACAvF,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAIqB,YAAY,GAAG,kDAAkD;MAErE,IAAIrB,KAAK,CAACE,QAAQ,EAAE;QAAA,IAAAuF,oBAAA;QAClB;QACA,MAAMxC,MAAM,GAAGjD,KAAK,CAACE,QAAQ,CAAC+C,MAAM;QACpC,MAAM3B,MAAM,GAAG,EAAAmE,oBAAA,GAAAzF,KAAK,CAACE,QAAQ,CAACiB,IAAI,cAAAsE,oBAAA,uBAAnBA,oBAAA,CAAqBnE,MAAM,KAAItB,KAAK,CAACI,OAAO;QAE3D,IAAI6C,MAAM,KAAK,GAAG,EAAE;UAClB;UACA,IAAI3B,MAAM,CAACjB,QAAQ,CAAC,eAAe,CAAC,EAAE;YACpCgB,YAAY,GAAG,uGAAuG;UACxH,CAAC,MAAM,IAAIC,MAAM,CAACjB,QAAQ,CAAC,YAAY,CAAC,EAAE;YACxCgB,YAAY,GAAG,4EAA4E;UAC7F,CAAC,MAAM;YACLA,YAAY,GAAGC,MAAM;UACvB;QACF,CAAC,MAAM,IAAI2B,MAAM,KAAK,GAAG,EAAE;UACzB5B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;QACvD,CAAC,MAAM;UACLD,YAAY,GAAG,sBAAsB4B,MAAM,MAAM3B,MAAM,EAAE;QAC3D;MACF,CAAC,MAAM,IAAItB,KAAK,CAAC0F,OAAO,EAAE;QACxB;QACArE,YAAY,GAAG,+DAA+D;MAChF,CAAC,MAAM;QACL;QACAA,YAAY,GAAGrB,KAAK,CAACI,OAAO,IAAI,oBAAoB;MACtD;MAEAnD,OAAO,CAACoE,YAAY,CAAC;IACvB,CAAC,SAAS;MACR9D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoI,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACEhJ,OAAA,CAAC5D,GAAG;MAAA6M,QAAA,gBACFjJ,OAAA,CAAC1D,UAAU;QAAC4M,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbvJ,OAAA,CAAC3D,KAAK;QAACmN,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzBjJ,OAAA,CAAC1D,UAAU;UAAC4M,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvJ,OAAA,CAACpD,IAAI;UAAC+M,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAZ,QAAA,gBAC7CjJ,OAAA,CAACpD,IAAI;YAACkN,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACfjJ,OAAA,CAACzD,SAAS;cACRyN,SAAS;cACTC,KAAK,EAAC,SAAS;cACff,OAAO,EAAC,UAAU;cAClBhC,KAAK,EAAE3F,WAAY;cACnB2I,QAAQ,EAAGlD,CAAC,IAAKxF,cAAc,CAACwF,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAChDiD,WAAW,EAAC;YAAyB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPvJ,OAAA,CAACpD,IAAI;YAACkN,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACfjJ,OAAA,CAACxD,MAAM;cACLwN,SAAS;cACTd,OAAO,EAAC,WAAW;cACnBkB,KAAK,EAAC,SAAS;cACfC,OAAO,EAAE3E,oBAAqB;cAC9B4E,QAAQ,EAAEzJ,WAAW,IAAI,CAACU,WAAW,CAACoE,IAAI,CAAC,CAAE;cAC7C4E,SAAS,EAAE1J,WAAW,gBAAGb,OAAA,CAACzC,gBAAgB;gBAACiN,IAAI,EAAE;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGvJ,OAAA,CAAChC,UAAU;gBAAAoL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAN,QAAA,EAC1E;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGRvJ,OAAA,CAAC3D,KAAK;QAACmN,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBjJ,OAAA,CAAC1D,UAAU;UAAC4M,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZ1I,WAAW,gBACVb,OAAA,CAAC5D,GAAG;UAACoN,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DjJ,OAAA,CAACzC,gBAAgB;YAAA6L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJtI,IAAI,CAACkC,MAAM,KAAK,CAAC,gBACnBnD,OAAA,CAAC1C,KAAK;UAACsN,QAAQ,EAAC,MAAM;UAAA3B,QAAA,EAAC;QAEvB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAERvJ,OAAA,CAAC/C,IAAI;UAACuM,EAAE,EAAE;YAAEqB,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA7B,QAAA,EAChDhI,IAAI,CAAC8J,GAAG,CAAExE,IAAI,iBACbvG,OAAA,CAAChE,KAAK,CAACiE,QAAQ;YAAAgJ,QAAA,gBACbjJ,OAAA,CAAC9C,QAAQ;cAAC8N,MAAM;cAACX,OAAO,EAAEA,CAAA,KAAMhE,gBAAgB,CAACE,IAAI,CAAE;cAAA0C,QAAA,gBACrDjJ,OAAA,CAAC7C,YAAY;gBACX8N,OAAO,eACLjL,OAAA,CAAC5D,GAAG;kBAACoN,EAAE,EAAE;oBAAEiB,OAAO,EAAE,MAAM;oBAAEZ,UAAU,EAAE;kBAAS,CAAE;kBAAAZ,QAAA,gBACjDjJ,OAAA,CAAC1D,UAAU;oBAAC4M,OAAO,EAAC,WAAW;oBAAAD,QAAA,EAAE1C,IAAI,CAAC5E;kBAAO;oBAAAyH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,EAC1D9J,YAAY,CAAC8G,IAAI,CAAC,gBACjBvG,OAAA,CAACtC,IAAI;oBACH8M,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,OAAO;oBACbG,KAAK,EAAC,OAAO;oBACbZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,GACA7J,gBAAgB,CAAC6G,IAAI,CAAC,gBACxBvG,OAAA,CAACtC,IAAI;oBACH8M,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,YAAY;oBAClBG,KAAK,EAAC,SAAS;oBACfZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,gBAEFvJ,OAAA,CAACtC,IAAI;oBACH8M,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAE1D,IAAI,CAACL,mBAAoB;oBAChCkE,KAAK,EAAEzK,kBAAkB,CAAC4G,IAAI,CAACL,mBAAmB,CAAE;oBACpDsD,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACD4B,SAAS,eACPnL,OAAA,CAAAE,SAAA;kBAAA+I,QAAA,gBACEjJ,OAAA,CAAC1D,UAAU;oBAAC4M,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GACzC1C,IAAI,CAACrB,SAAS,IAAI,KAAK,EAAC,KAAG,EAACqB,IAAI,CAACpB,YAAY,IAAI,KAAK,EAAC,KAAG,EAACoB,IAAI,CAACnB,OAAO,IAAI,KAAK;kBAAA;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACbvJ,OAAA;oBAAAoJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNvJ,OAAA,CAAC1D,UAAU;oBAAC4M,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,MACvC,EAAC1C,IAAI,CAAC8E,mBAAmB,IAAI,KAAK,EAAC,QAAM,EAAC9E,IAAI,CAAC+E,iBAAiB,IAAI,KAAK;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACbvJ,OAAA;oBAAAoJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNvJ,OAAA,CAAC1D,UAAU;oBAAC4M,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,iBAC5B,EAAC1C,IAAI,CAACkB,aAAa,IAAI,KAAK,EAAC,mBAAiB,EAAClB,IAAI,CAACJ,eAAe,IAAI,GAAG;kBAAA;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC;gBAAA,eACb;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFvJ,OAAA,CAAC5C,uBAAuB;gBAAA6L,QAAA,eACtBjJ,OAAA,CAACvC,UAAU;kBAAC8N,IAAI,EAAC,KAAK;kBAAClB,OAAO,EAAGrD,CAAC,IAAK;oBACrCA,CAAC,CAACwE,eAAe,CAAC,CAAC,CAAC,CAAC;oBACrBlK,eAAe,CAACiF,IAAI,CAAC;oBACrB5D,wBAAwB,CAAC,IAAI,CAAC;kBAChC,CAAE;kBAAAsG,QAAA,eACAjJ,OAAA,CAAClB,QAAQ;oBAAAsK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACXvJ,OAAA,CAAC3C,OAAO;cAAA+L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAxDQhD,IAAI,CAAC5E,OAAO;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyDjB,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMkC,eAAe,GAAIjD,QAAQ,IAAK;IACpC;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAAC9E,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAO8E,QAAQ,CAACkD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOlD,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMmD,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACtK,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACErB,OAAA,CAAC5D,GAAG;MAAA6M,QAAA,gBACFjJ,OAAA,CAAC1D,UAAU;QAAC4M,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbvJ,OAAA,CAACb,eAAe;QACdoH,IAAI,EAAElF,YAAa;QACnBuK,OAAO,EAAE,IAAK;QACdC,KAAK,EAAC;MAA+B;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAEFvJ,OAAA,CAAC3D,KAAK;QAACmN,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBjJ,OAAA,CAAC1D,UAAU;UAAC4M,OAAO,EAAC,WAAW;UAACC,YAAY;UAACK,EAAE,EAAE;YAAEsC,UAAU,EAAE;UAAO,CAAE;UAAA7C,QAAA,EAAC;QAEzE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGbvJ,OAAA,CAACpD,IAAI;UAAC+M,SAAS;UAACC,OAAO,EAAE,CAAE;UAACJ,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACxCjJ,OAAA,CAACpD,IAAI;YAACkN,IAAI;YAACC,EAAE,EAAE,EAAG;YAACgC,EAAE,EAAE,CAAE;YAAA9C,QAAA,eACvBjJ,OAAA,CAAC5D,GAAG;cAACoN,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEuC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAO,CAAE;cAAAjD,QAAA,gBACrEjJ,OAAA,CAAC1D,UAAU;gBAAC4M,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEsC,UAAU,EAAE,MAAM;kBAAE1B,KAAK,EAAE;gBAAe,CAAE;gBAAAnB,QAAA,EAAC;cAEhG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvJ,OAAA,CAACpD,IAAI;gBAAC+M,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAX,QAAA,gBACzBjJ,OAAA,CAACpD,IAAI;kBAACkN,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfjJ,OAAA,CAAC1D,UAAU;oBAAC4M,OAAO,EAAC,OAAO;oBAACM,EAAE,EAAE;sBAAEsC,UAAU,EAAE;oBAAS,CAAE;oBAAA7C,QAAA,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACPvJ,OAAA,CAACpD,IAAI;kBAACkN,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfjJ,OAAA,CAAC1D,UAAU;oBAAC4M,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAE5H,YAAY,CAACoG,aAAa,IAAI,KAAK,EAAC,IAAE;kBAAA;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC,eACPvJ,OAAA,CAACpD,IAAI;kBAACkN,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfjJ,OAAA,CAAC1D,UAAU;oBAAC4M,OAAO,EAAC,OAAO;oBAACM,EAAE,EAAE;sBAAEsC,UAAU,EAAE;oBAAS,CAAE;oBAAA7C,QAAA,EAAC;kBAAc;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF,CAAC,eACPvJ,OAAA,CAACpD,IAAI;kBAACkN,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAd,QAAA,eACfjJ,OAAA,CAACtC,IAAI;oBACHuM,KAAK,EAAE5I,YAAY,CAAC6E,mBAAmB,IAAI,KAAM;oBACjDsE,IAAI,EAAC,OAAO;oBACZJ,KAAK,EAAEzK,kBAAkB,CAAC0B,YAAY,CAAC6E,mBAAmB,CAAE;oBAC5DgD,OAAO,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPvJ,OAAA,CAACpD,IAAI;YAACkN,IAAI;YAACC,EAAE,EAAE,EAAG;YAACgC,EAAE,EAAE,CAAE;YAAA9C,QAAA,eACvBjJ,OAAA,CAAC5D,GAAG;cAACoN,EAAE,EAAE;gBAAEC,CAAC,EAAE,CAAC;gBAAEuC,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE,CAAC;gBAAEC,MAAM,EAAE;cAAO,CAAE;cAAAjD,QAAA,gBACrEjJ,OAAA,CAAC1D,UAAU;gBAAC4M,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEsC,UAAU,EAAE,MAAM;kBAAE1B,KAAK,EAAE;gBAAiB,CAAE;gBAAAnB,QAAA,EAAC;cAElG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZ9H,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,CAAC,MAAM;gBACpE,MAAMmD,MAAM,GAAG7D,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;gBACnE,OAAOmD,MAAM,gBACXhF,OAAA,CAACpD,IAAI;kBAAC+M,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAX,QAAA,gBACzBjJ,OAAA,CAACpD,IAAI;oBAACkN,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfjJ,OAAA,CAAC1D,UAAU;sBAAC4M,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEsC,UAAU,EAAE;sBAAS,CAAE;sBAAA7C,QAAA,EAAC;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC,eACPvJ,OAAA,CAACpD,IAAI;oBAACkN,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfjJ,OAAA,CAAC1D,UAAU;sBAAC4M,OAAO,EAAC,OAAO;sBAAAD,QAAA,EAAEwC,eAAe,CAACzG,MAAM,CAACnD,SAAS;oBAAC;sBAAAuH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC,eACPvJ,OAAA,CAACpD,IAAI;oBAACkN,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfjJ,OAAA,CAAC1D,UAAU;sBAAC4M,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEsC,UAAU,EAAE;sBAAS,CAAE;sBAAA7C,QAAA,EAAC;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC,eACPvJ,OAAA,CAACpD,IAAI;oBAACkN,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfjJ,OAAA,CAAC1D,UAAU;sBAAC4M,OAAO,EAAC,OAAO;sBAAAD,QAAA,GAAEjE,MAAM,CAACS,aAAa,IAAI,CAAC,EAAC,IAAE;oBAAA;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACPvJ,OAAA,CAACpD,IAAI;oBAACkN,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfjJ,OAAA,CAAC1D,UAAU;sBAAC4M,OAAO,EAAC,OAAO;sBAACM,EAAE,EAAE;wBAAEsC,UAAU,EAAE;sBAAS,CAAE;sBAAA7C,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eACPvJ,OAAA,CAACpD,IAAI;oBAACkN,IAAI;oBAACC,EAAE,EAAE,CAAE;oBAAAd,QAAA,eACfjJ,OAAA,CAACtC,IAAI;sBACHuM,KAAK,EAAEjF,MAAM,CAACC,YAAY,IAAI,KAAM;sBACpCuF,IAAI,EAAC,OAAO;sBACZJ,KAAK,EAAExK,iBAAiB,CAACoF,MAAM,CAACC,YAAY,CAAE;sBAC9CiE,OAAO,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,gBAEPvJ,OAAA,CAAC1D,UAAU;kBAAC4M,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAkB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAC3D;cACH,CAAC,EAAE,CAAC,gBACFvJ,OAAA,CAAC1D,UAAU;gBAAC4M,OAAO,EAAC,OAAO;gBAAAD,QAAA,EACxBxH,QAAQ,CAACI,SAAS,KAAK,cAAc,GACpC,kDAAkD,GAClD;cAA4B;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPvJ,OAAA,CAAC5D,GAAG;UAACoN,EAAE,EAAE;YAAE2C,EAAE,EAAE,CAAC;YAAEzC,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACxBjJ,OAAA,CAAC1D,UAAU;YAAC4M,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEsC,UAAU,EAAE;YAAO,CAAE;YAAA7C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvJ,OAAA,CAACzD,SAAS;YACRiO,IAAI,EAAC,OAAO;YACZR,SAAS;YACTC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClBjC,IAAI,EAAC,cAAc;YACnBmF,IAAI,EAAC,QAAQ;YACblF,KAAK,EAAEzF,QAAQ,CAACG,YAAa;YAC7BsI,QAAQ,EAAEnD,gBAAiB;YAC3B1D,KAAK,EAAE,CAAC,CAACvB,UAAU,CAACF,YAAa;YACjCyK,UAAU,EAAEvK,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjE0K,mBAAmB,EAAE;cACnB9C,EAAE,EAAE;gBAAEY,KAAK,EAAEpI,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACF4H,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELvH,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,IAAI,CAAC+F,iBAAiB,iBAC1E3H,OAAA,CAAC1C,KAAK;UAACsN,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EACrCjH,YAAY,CAACJ;QAAY;UAAAwH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACR,eAEDvJ,OAAA,CAAC1C,KAAK;UAACsN,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAE2C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,eACnCjJ,OAAA,CAAC1D,UAAU;YAAC4M,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAC;UAE5B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMgD,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,MAAMC,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAIrM,UAAU,KAAKqM,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAI1H,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAACvD,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAO2F,UAAU,CAACvC,MAAM,CAACS,aAAa,CAAC,IAAI8B,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAM+K,uBAAuB,GAAI3F,CAAC,IAAK;MACrC,MAAMyF,YAAY,GAAGzF,CAAC,CAACG,MAAM,CAACD,KAAK,CAACvB,IAAI,CAAC,CAAC;;MAE1C;MACA,IAAI8G,YAAY,CAACG,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtClL,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb+K,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIJ,YAAY,EAAE;QAChB;QACA,MAAMK,gBAAgB,GAAGN,iBAAiB,CAACC,YAAY,CAAC;;QAExD;QACA,MAAMM,eAAe,GAAG5L,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAKiL,gBAAgB,CAAC;QAE1E,IAAIC,eAAe,EAAE;UACnB;UACA,IAAIA,eAAe,CAAC9H,YAAY,KAAK,MAAM,IAAI8H,eAAe,CAAC9H,YAAY,KAAK,WAAW,EAAE;YAC3FlD,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb+K,eAAe,EAAE,aAAaJ,YAAY,eAAeM,eAAe,CAAC9H,YAAY;YACvF,CAAC,CAAC;YACF;UACF;;UAEA;UACA,IAAI5D,YAAY,KACZ0L,eAAe,CAAC7H,SAAS,KAAK7D,YAAY,CAAC6D,SAAS,IACpD8H,MAAM,CAACD,eAAe,CAAC5H,YAAY,CAAC,KAAK6H,MAAM,CAAC3L,YAAY,CAAC8D,YAAY,CAAC,IAC1E6H,MAAM,CAACD,eAAe,CAAC3H,OAAO,CAAC,KAAK4H,MAAM,CAAC3L,YAAY,CAAC+D,OAAO,CAAC,CACnE,EAAE;YACD;YACA/C,mBAAmB,CAAC0K,eAAe,CAAC;YACpC5K,6BAA6B,CAAC,IAAI,CAAC;YACnC;UACF;;UAEA;UACA,IAAIuK,mBAAmB,CAACK,eAAe,CAAC,EAAE;YACxC;YACArL,WAAW,CAAC;cACV,GAAGD,QAAQ;cACXI,SAAS,EAAEiL;YACb,CAAC,CAAC;YACF/K,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb+K,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACA9K,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb+K,eAAe,EAAE,aAAaJ,YAAY,sCAAsCM,eAAe,CAACtH,aAAa;YAC/G,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACA1D,aAAa,CAAC;YACZ,GAAGD,UAAU;YACb+K,eAAe,EAAE,UAAUJ,YAAY;UACzC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACA/K,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb+K,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,oBACE7M,OAAA,CAAC5D,GAAG;MAAA6M,QAAA,gBACFjJ,OAAA,CAAC1D,UAAU;QAAC4M,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbvJ,OAAA,CAAC3D,KAAK;QAACmN,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBjJ,OAAA,CAAC1D,UAAU;UAAC4M,OAAO,EAAC,OAAO;UAAC+D,SAAS;UAAAhE,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZxI,aAAa,gBACZf,OAAA,CAAC5D,GAAG;UAACoN,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DjJ,OAAA,CAACzC,gBAAgB;YAAA6L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAENvJ,OAAA,CAAC5D,GAAG;UAAA6M,QAAA,gBACFjJ,OAAA,CAACpD,IAAI;YAAC+M,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBAEzBjJ,OAAA,CAACpD,IAAI;cAACkN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACgC,EAAE,EAAE,CAAE;cAAA9C,QAAA,gBACvBjJ,OAAA,CAAC1D,UAAU;gBAAC4M,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEsC,UAAU,EAAE;gBAAO,CAAE;gBAAA7C,QAAA,EAAC;cAEzE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvJ,OAAA,CAACzD,SAAS;gBACRiO,IAAI,EAAC,OAAO;gBACZR,SAAS;gBACTC,KAAK,EAAC,eAAe;gBACrBf,OAAO,EAAC,UAAU;gBAClBiB,WAAW,EAAC,oBAAoB;gBAChCkC,UAAU,EAAEvK,UAAU,CAAC+K,eAAe,IAAI,uCAAwC;gBAClFxJ,KAAK,EAAE,CAAC,CAACvB,UAAU,CAAC+K,eAAgB;gBACpCK,MAAM,EAAEP,uBAAwB;gBAChCnD,EAAE,EAAE;kBAAEE,EAAE,EAAE;gBAAE;cAAE;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACFvJ,OAAA,CAAC1D,UAAU;gBAAC4M,OAAO,EAAC,OAAO;gBAACM,EAAE,EAAE;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAT,QAAA,GAAC,aAC9B,eAAAjJ,OAAA;kBAAAiJ,QAAA,EAASxH,QAAQ,CAACI,SAAS,IAAI;gBAAG;kBAAAuH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAGPvJ,OAAA,CAACpD,IAAI;cAACkN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACgC,EAAE,EAAE,CAAE;cAAA9C,QAAA,gBACvBjJ,OAAA,CAAC1D,UAAU;gBAAC4M,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAACK,EAAE,EAAE;kBAAEsC,UAAU,EAAE;gBAAO,CAAE;gBAAA7C,QAAA,EAAC;cAEzE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvJ,OAAA,CAACnD,WAAW;gBAACmN,SAAS;gBAACQ,IAAI,EAAC,OAAO;gBAAAvB,QAAA,gBACjCjJ,OAAA,CAAClD,UAAU;kBAACqQ,EAAE,EAAC,qBAAqB;kBAAAlE,QAAA,EAAC;gBAAgB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClEvJ,OAAA,CAACjD,MAAM;kBACLqQ,OAAO,EAAC,qBAAqB;kBAC7BD,EAAE,EAAC,eAAe;kBAClBlG,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAEzF,QAAQ,CAACI,SAAU;kBAC1BoI,KAAK,EAAC,kBAAkB;kBACxBC,QAAQ,EAAEnD,gBAAiB;kBAAAkC,QAAA,gBAE3BjJ,OAAA,CAAChD,QAAQ;oBAACkK,KAAK,EAAC,cAAc;oBAAA+B,QAAA,gBAC5BjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAY;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,+BAC/B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACXvJ,OAAA,CAAC3C,OAAO;oBAAA+L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACVpI,MAAM,CAAC4J,GAAG,CAAE/F,MAAM,iBACjBhF,OAAA,CAAChD,QAAQ;oBAEPkK,KAAK,EAAElC,MAAM,CAACnD,SAAU;oBACxByI,QAAQ,EAAEtF,MAAM,CAACS,aAAa,GAAG8B,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAE;oBAAAqH,QAAA,GAElEwC,eAAe,CAACzG,MAAM,CAACnD,SAAS,CAAC,EAAC,KAAG,EAACmD,MAAM,CAACE,SAAS,IAAI,KAAK,EAAC,KAAG,EAACF,MAAM,CAACS,aAAa,IAAI,CAAC,EAAC,IACjG;kBAAA,GALOT,MAAM,CAACnD,SAAS;oBAAAuH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAKb,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACTvJ,OAAA,CAACxC,cAAc;kBAAAyL,QAAA,EAAC;gBAEhB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPvJ,OAAA,CAAC1C,KAAK;YAACsN,QAAQ,EAAC,MAAM;YAACpB,EAAE,EAAE;cAAE2C,EAAE,EAAE;YAAE,CAAE;YAAAlD,QAAA,eACnCjJ,OAAA,CAAC1D,UAAU;cAAC4M,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBjJ,OAAA;gBAAAiJ,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,iGACvB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,EAGA,CAACxI,aAAa,IAAIU,QAAQ,CAACI,SAAS,iBACnC7B,OAAA,CAAC5D,GAAG;UAACoN,EAAE,EAAE;YAAE2C,EAAE,EAAE,CAAC;YAAE1C,CAAC,EAAE,CAAC;YAAEuC,OAAO,EAAE,kBAAkB;YAAEC,YAAY,EAAE,CAAC;YAAEoB,MAAM,EAAE;UAAoB,CAAE;UAAApE,QAAA,gBAClGjJ,OAAA,CAAC1D,UAAU;YAAC4M,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAF,QAAA,EAAC;UAE7C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ,CAAC,MAAM;YACN,MAAMvE,MAAM,GAAG7D,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,IAAImD,MAAM,EAAE;cACV,oBACEhF,OAAA,CAACpD,IAAI;gBAAC+M,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAX,QAAA,gBACzBjJ,OAAA,CAACpD,IAAI;kBAACkN,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACgC,EAAE,EAAE,CAAE;kBAAA9C,QAAA,gBACvBjJ,OAAA,CAAC1D,UAAU;oBAAC4M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACkC,eAAe,CAACzG,MAAM,CAACnD,SAAS,CAAC;kBAAA;oBAAAuH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACbvJ,OAAA,CAAC1D,UAAU;oBAAC4M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvE,MAAM,CAACE,SAAS,IAAI,KAAK;kBAAA;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACbvJ,OAAA,CAAC1D,UAAU;oBAAC4M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAW;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvE,MAAM,CAACG,YAAY,IAAI,KAAK,EAAC,KAAG,EAACH,MAAM,CAACI,OAAO,IAAI,KAAK;kBAAA;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACPvJ,OAAA,CAACpD,IAAI;kBAACkN,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACgC,EAAE,EAAE,CAAE;kBAAA9C,QAAA,gBACvBjJ,OAAA,CAAC1D,UAAU;oBAAC4M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAa;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvE,MAAM,CAACsI,YAAY,IAAI,CAAC,EAAC,IAC3D;kBAAA;oBAAAlE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbvJ,OAAA,CAAC1D,UAAU;oBAAC4M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAc;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvE,MAAM,CAACS,aAAa,IAAI,CAAC,EAAC,IAC7D;kBAAA;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbvJ,OAAA,CAAC1D,UAAU;oBAAC4M,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBjJ,OAAA;sBAAAiJ,QAAA,EAAQ;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvE,MAAM,CAACC,YAAY,IAAI,KAAK;kBAAA;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAEX;YACA,oBACEvJ,OAAA,CAAC1D,UAAU;cAAC4M,OAAO,EAAC,OAAO;cAACkB,KAAK,EAAC,OAAO;cAAAnB,QAAA,EAAC;YAE1C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAEjB,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEApI,MAAM,CAACgC,MAAM,KAAK,CAAC,IAAI,CAACpC,aAAa,iBACpCf,OAAA,CAAC1C,KAAK;UAACsN,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAE2C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,EAAC;QAEzC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMgE,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAM9B,eAAe,GAAIjD,QAAQ,IAAK;MACpC;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAAC9E,QAAQ,CAAC,IAAI,CAAC,EAAE;QACvC,OAAO8E,QAAQ,CAACkD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAChC;MACA,OAAOlD,QAAQ;IACjB,CAAC;;IAED;IACA,IAAIiE,YAAY,GAAG,SAAS;IAC5B,IAAIe,UAAU,GAAG,IAAI;IAErB,IAAI/L,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzC4K,YAAY,GAAG,cAAc;IAC/B,CAAC,MAAM,IAAIhL,QAAQ,CAACI,SAAS,EAAE;MAC7B4K,YAAY,GAAGhB,eAAe,CAAChK,QAAQ,CAACI,SAAS,CAAC;MAClD;MACA2L,UAAU,GAAGrM,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IACnE;;IAEA;IACA,MAAM4G,kBAAkB,GAAGJ,2BAA2B,CAACd,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAC,EAAEP,YAAY,CAACoG,aAAa,CAAC;IAErH,oBACEzH,OAAA,CAAC5D,GAAG;MAAA6M,QAAA,gBACFjJ,OAAA,CAAC1D,UAAU;QAAC4M,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbvJ,OAAA,CAAC3D,KAAK;QAACmN,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBjJ,OAAA,CAAC1D,UAAU;UAAC4M,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGbvJ,OAAA,CAACb,eAAe;UACdoH,IAAI,EAAElF,YAAa;UACnBuK,OAAO,EAAE,IAAK;UACdC,KAAK,EAAC;QAAmB;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGFvJ,OAAA,CAAC5D,GAAG;UAACoN,EAAE,EAAE;YAAE2C,EAAE,EAAE,CAAC;YAAE1C,CAAC,EAAE,CAAC;YAAEuC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAhD,QAAA,gBAC5DjJ,OAAA,CAAC1D,UAAU;YAAC4M,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEsC,UAAU,EAAE;YAAO,CAAE;YAAA7C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvJ,OAAA,CAACpD,IAAI;YAAC+M,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBACzBjJ,OAAA,CAACpD,IAAI;cAACkN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACgC,EAAE,EAAE,CAAE;cAAA9C,QAAA,gBACvBjJ,OAAA,CAAC1D,UAAU;gBAAC4M,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBjJ,OAAA;kBAAAiJ,QAAA,EAAQ;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9H,QAAQ,CAACG,YAAY,EAAC,IACxD;cAAA;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvJ,OAAA,CAAC1D,UAAU;gBAAC4M,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBjJ,OAAA;kBAAAiJ,QAAA,EAAQ;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACd,kBAAkB;cAAA;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACPvJ,OAAA,CAACpD,IAAI;cAACkN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACgC,EAAE,EAAE,CAAE;cAAA9C,QAAA,gBACvBjJ,OAAA,CAAC1D,UAAU;gBAAC4M,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBjJ,OAAA;kBAAAiJ,QAAA,EAAQ;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACkD,YAAY;cAAA;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACZiE,UAAU,iBACTxN,OAAA,CAAC1D,UAAU;gBAAC4M,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBjJ,OAAA;kBAAAiJ,QAAA,EAAQ;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACiE,UAAU,CAAC/H,aAAa,EAAC,IACnE;cAAA;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAELiE,UAAU,IAAIjG,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAC,GAAG2F,UAAU,CAACiG,UAAU,CAAC/H,aAAa,CAAC,IAAI,CAACkC,iBAAiB,iBAC3G3H,OAAA,CAAC1C,KAAK;UAACsN,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAE2C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACtCjJ,OAAA;YAAAiJ,QAAA,EAAQ;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qBAAiB,EAAC9H,QAAQ,CAACG,YAAY,EAAC,4CAA0C,EAAC4L,UAAU,CAAC/H,aAAa,EAAC,gDAE1I;QAAA;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAEDvJ,OAAA,CAAC1C,KAAK;UAACsN,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAE2C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,GAAC,8EAEpC,EAACxH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,gFAAgF;QAAA;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMkE,cAAc,GAAIC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAO1E,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOuD,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOZ,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAO4B,WAAW,CAAC,CAAC;MAAE;MACxB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;;EAED;EACA,MAAMI,4BAA4B,GAAGA,CAAA,KAAM;IACzCpL,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMmL,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIpL,eAAe,EAAE;MACnBhC,QAAQ,CAAC,mCAAmCJ,UAAU,IAAIoC,eAAe,CAACb,OAAO,EAAE,CAAC;IACtF;IACAgM,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,4BAA4B,CAAC,CAAC;IAC9B;IACAjN,aAAa,CAAC,CAAC,CAAC;IAChBY,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;;EAED;EACA,MAAMsM,iCAAiC,GAAGA,CAAA,KAAM;IAC9C3L,6BAA6B,CAAC,KAAK,CAAC;IACpCE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM0L,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI,CAAC1M,YAAY,IAAI,CAACe,gBAAgB,EAAE;IAExC,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAM5B,WAAW,CAACgP,qBAAqB,CAAC5N,UAAU,EAAEiB,YAAY,CAACM,OAAO,EAAES,gBAAgB,CAAC;;MAE3F;MACA,MAAMwE,WAAW,GAAG,MAAM5H,WAAW,CAAC6G,WAAW,CAACzF,UAAU,EAAEiB,YAAY,CAACM,OAAO,CAAC;MACnFL,eAAe,CAACsF,WAAW,CAAC;;MAE5B;MACAlF,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXI,SAAS,EAAEO,gBAAgB,CAACP;MAC9B,CAAC,CAAC;MAEFxB,SAAS,CAAC,4BAA4BgB,YAAY,CAACM,OAAO,6CAA6CS,gBAAgB,CAACP,SAAS,EAAE,CAAC;MACpIiM,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOzK,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iEAAiE,EAAEA,KAAK,CAAC;MACvF/C,OAAO,CAAC,kEAAkE,IAAI+C,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACvH,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqN,uBAAuB,GAAGA,CAAA,KAAM;IACpCH,iCAAiC,CAAC,CAAC;IACnC;IACApM,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,oBACE7B,OAAA,CAAC5D,GAAG;IAAA6M,QAAA,gBACFjJ,OAAA,CAACvD,OAAO;MAACgE,UAAU,EAAEA,UAAW;MAAC+I,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EAC5CrG,KAAK,CAACmI,GAAG,CAAEd,KAAK,iBACfjK,OAAA,CAACtD,IAAI;QAAAuM,QAAA,eACHjJ,OAAA,CAACrD,SAAS;UAAAsM,QAAA,EAAEgB;QAAK;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC,GADrBU,KAAK;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEVvJ,OAAA,CAAC5D,GAAG;MAACoN,EAAE,EAAE;QAAE2C,EAAE,EAAE,CAAC;QAAEzC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EACvBwE,cAAc,CAAChN,UAAU;IAAC;MAAA2I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAENvJ,OAAA,CAAC5D,GAAG;MAACoN,EAAE,EAAE;QAAEiB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEyB,EAAE,EAAE;MAAE,CAAE;MAAAlD,QAAA,gBACnEjJ,OAAA,CAACxD,MAAM;QACL0M,OAAO,EAAC,UAAU;QAClBkB,KAAK,EAAC,WAAW;QACjBC,OAAO,EAAE5J,UAAU,KAAK,CAAC,GAAG,MAAMD,QAAQ,CAAC,sBAAsB,CAAC,GAAG2H,UAAW;QAChFoC,SAAS,eAAEvK,OAAA,CAAC5B,aAAa;UAAAgL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7Be,QAAQ,EAAE3J,OAAQ;QAAAsI,QAAA,EAEjBxI,UAAU,KAAK,CAAC,GAAG,SAAS,GAAG;MAAU;QAAA2I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAETvJ,OAAA,CAACxD,MAAM;QACL0M,OAAO,EAAC,WAAW;QACnBkB,KAAK,EAAC,SAAS;QACfC,OAAO,EAAE5J,UAAU,KAAKmC,KAAK,CAACO,MAAM,GAAG,CAAC,GAAGoF,YAAY,GAAGN,UAAW;QACrEiG,OAAO,EAAEzN,UAAU,KAAKmC,KAAK,CAACO,MAAM,GAAG,CAAC,gBAAGnD,OAAA,CAAC9B,QAAQ;UAAAkL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGvJ,OAAA,CAAC1B,gBAAgB;UAAA8K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC/Ee,QAAQ,EAAE3J,OAAO,IAAKF,UAAU,KAAK,CAAC,IAAI,CAACY,YAAc;QAAA4H,QAAA,EAExDtI,OAAO,gBACNX,OAAA,CAACzC,gBAAgB;UAACiN,IAAI,EAAE;QAAG;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAC5B9I,UAAU,KAAKmC,KAAK,CAACO,MAAM,GAAG,CAAC,GACjC,OAAO,GAEP;MACD;QAAAiG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNvJ,OAAA,CAACrC,MAAM;MAACwQ,IAAI,EAAE7L,qBAAsB;MAAC8L,OAAO,EAAET,4BAA6B;MAACU,QAAQ,EAAC,IAAI;MAACrE,SAAS;MAAAf,QAAA,gBACjGjJ,OAAA,CAACpC,WAAW;QAAC4L,EAAE,EAAE;UAAEwC,OAAO,EAAE;QAAgB,CAAE;QAAA/C,QAAA,eAC5CjJ,OAAA,CAAC5D,GAAG;UAACoN,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEyE,GAAG,EAAE;UAAE,CAAE;UAAArF,QAAA,gBACzDjJ,OAAA,CAACpB,WAAW;YAACwL,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/BvJ,OAAA,CAAC1D,UAAU;YAAC4M,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdvJ,OAAA,CAACnC,aAAa;QAAAoL,QAAA,EACXzG,eAAe,iBACdxC,OAAA,CAAC5D,GAAG;UAACoN,EAAE,EAAE;YAAE2C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACjBjJ,OAAA,CAAC1D,UAAU;YAAC4M,OAAO,EAAC,OAAO;YAAC+D,SAAS;YAAAhE,QAAA,GAAC,UAC5B,eAAAjJ,OAAA;cAAAiJ,QAAA,EAASzG,eAAe,CAACb;YAAO;cAAAyH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,4BAAqB,EAAC/G,eAAe,CAAC2D,eAAe,IAAI,CAAC,EAAC,KAC/G;UAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvJ,OAAA,CAAC1D,UAAU;YAAC4M,OAAO,EAAC,OAAO;YAAC+D,SAAS;YAAAhE,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvJ,OAAA,CAAC1D,UAAU;YAAC4M,OAAO,EAAC,OAAO;YAACkC,SAAS,EAAC,IAAI;YAAAnC,QAAA,gBACxCjJ,OAAA;cAAAiJ,QAAA,EAAI;YAAsC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/CvJ,OAAA;cAAAiJ,QAAA,EAAI;YAAyB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCvJ,OAAA;cAAAiJ,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBvJ,OAAA,CAAClC,aAAa;QAAC0L,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEiB,cAAc,EAAE;QAAgB,CAAE;QAAAzB,QAAA,gBAC3DjJ,OAAA,CAACxD,MAAM;UAAC6N,OAAO,EAAEsD,4BAA6B;UAACvD,KAAK,EAAC,WAAW;UAAAnB,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvJ,OAAA,CAAC5D,GAAG;UAAA6M,QAAA,gBACFjJ,OAAA,CAACxD,MAAM;YAAC6N,OAAO,EAAEwD,wBAAyB;YAACzD,KAAK,EAAC,SAAS;YAACZ,EAAE,EAAE;cAAE+E,EAAE,EAAE;YAAE,CAAE;YAAAtF,QAAA,EAAC;UAE1E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvJ,OAAA,CAACxD,MAAM;YAAC6N,OAAO,EAAEuD,gBAAiB;YAAC1E,OAAO,EAAC,WAAW;YAACkB,KAAK,EAAC,SAAS;YAAAnB,QAAA,EAAC;UAEvE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTvJ,OAAA,CAACd,sBAAsB;MACrBiP,IAAI,EAAEjM,0BAA2B;MACjCkM,OAAO,EAAEN,iCAAkC;MAC3CvH,IAAI,EAAElF,YAAa;MACnB2D,MAAM,EAAE5C,gBAAiB;MACzBoM,YAAY,EAAET,2BAA4B;MAC1CU,mBAAmB,EAAER;IAAwB;MAAA7E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAGFvJ,OAAA,CAACrC,MAAM;MACLwQ,IAAI,EAAEzL,qBAAsB;MAC5B0L,OAAO,EAAEA,CAAA,KAAMzL,wBAAwB,CAAC,KAAK,CAAE;MAC/C0L,QAAQ,EAAC,IAAI;MACbrE,SAAS;MAAAf,QAAA,gBAETjJ,OAAA,CAACpC,WAAW;QAAAqL,QAAA,eACVjJ,OAAA,CAAC5D,GAAG;UAACoN,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEyE,GAAG,EAAE;UAAE,CAAE;UAAArF,QAAA,gBACzDjJ,OAAA,CAAClB,QAAQ;YAACsL,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BvJ,OAAA,CAAC1D,UAAU;YAAC4M,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdvJ,OAAA,CAACnC,aAAa;QAAAoL,QAAA,eACZjJ,OAAA,CAACb,eAAe;UAACoH,IAAI,EAAElF;QAAa;UAAA+H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAChBvJ,OAAA,CAAClC,aAAa;QAAAmL,QAAA,eACZjJ,OAAA,CAACxD,MAAM;UAAC6N,OAAO,EAAEA,CAAA,KAAM1H,wBAAwB,CAAC,KAAK,CAAE;UAACyH,KAAK,EAAC,SAAS;UAAAnB,QAAA,EAAC;QAExE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAChJ,EAAA,CAj3CIJ,kBAAkB;EAAA,QACLpB,WAAW;AAAA;AAAA2P,EAAA,GADxBvO,kBAAkB;AAm3CxB,eAAeA,kBAAkB;AAAC,IAAAuO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}