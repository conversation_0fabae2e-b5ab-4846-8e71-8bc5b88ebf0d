{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\charts\\\\BoqChart.js\";\nimport React from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell, ComposedChart, Line, LineChart } from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f',\n  purple: '#9c27b0',\n  teal: '#00695c'\n};\nconst BoqChart = ({\n  data\n}) => {\n  var _data$distinta_materi, _data$distinta_materi2, _data$riepilogo, _data$riepilogo$total, _data$riepilogo2, _data$riepilogo2$tota, _data$riepilogo3, _data$riepilogo3$tota, _data$riepilogo4, _data$riepilogo4$perc, _data$riepilogo5, _data$riepilogo5$tota, _data$riepilogo6, _data$riepilogo6$tota, _data$riepilogo7, _data$riepilogo7$tota, _data$riepilogo8, _data$riepilogo8$tota;\n  if (!data) return null;\n\n  // Prepara dati per grafici dalla nuova struttura distinta_materiali\n  const caviData = ((_data$distinta_materi = data.distinta_materiali) === null || _data$distinta_materi === void 0 ? void 0 : _data$distinta_materi.map((cavo, index) => {\n    var _cavo$tipologia;\n    return {\n      ...cavo,\n      // Mappa i nuovi campi ai vecchi per compatibilità\n      metri_teorici: cavo.metri_teorici_totali,\n      metri_reali: cavo.metri_reali_posati,\n      tipologia_short: ((_cavo$tipologia = cavo.tipologia) === null || _cavo$tipologia === void 0 ? void 0 : _cavo$tipologia.length) > 8 ? cavo.tipologia.substring(0, 8) + '...' : cavo.tipologia,\n      color: Object.values(COLORS)[index % Object.values(COLORS).length],\n      deficit: Math.max(0, cavo.metri_da_posare - (cavo.metri_reali_posati || 0)),\n      surplus: Math.max(0, (cavo.metri_reali_posati || 0) - cavo.metri_da_posare)\n    };\n  })) || [];\n\n  // Prepara dati per grafici bobine (ora inclusi nella distinta_materiali)\n  const bobineData = ((_data$distinta_materi2 = data.distinta_materiali) === null || _data$distinta_materi2 === void 0 ? void 0 : _data$distinta_materi2.filter(item => item.num_bobine > 0).map((bobina, index) => {\n    var _bobina$tipologia;\n    return {\n      tipologia: bobina.tipologia,\n      formazione: bobina.formazione,\n      // Aggiornato da sezione a formazione\n      num_bobine: bobina.num_bobine,\n      metri_disponibili: bobina.metri_disponibili,\n      tipologia_short: ((_bobina$tipologia = bobina.tipologia) === null || _bobina$tipologia === void 0 ? void 0 : _bobina$tipologia.length) > 8 ? bobina.tipologia.substring(0, 8) + '...' : bobina.tipologia,\n      color: Object.values(COLORS)[index % Object.values(COLORS).length]\n    };\n  })) || [];\n\n  // Calcola totali per grafici a torta\n  const totaliCavi = caviData.reduce((acc, cavo) => {\n    acc.teorici += cavo.metri_teorici || 0;\n    acc.reali += cavo.metri_reali || 0;\n    acc.da_posare += cavo.metri_da_posare || 0;\n    return acc;\n  }, {\n    teorici: 0,\n    reali: 0,\n    da_posare: 0\n  });\n  const totaliData = [{\n    name: 'Metri Teorici',\n    value: totaliCavi.teorici,\n    color: COLORS.primary\n  }, {\n    name: 'Metri Reali',\n    value: totaliCavi.reali,\n    color: COLORS.success\n  }, {\n    name: 'Metri da Posare',\n    value: totaliCavi.da_posare,\n    color: COLORS.warning\n  }];\n\n  // Analisi deficit/surplus\n  const analisiData = caviData.map(cavo => ({\n    tipologia: cavo.tipologia_short,\n    tipologia_full: cavo.tipologia,\n    deficit: cavo.deficit,\n    surplus: cavo.surplus,\n    necessita_acquisto: cavo.deficit > 0\n  }));\n  const CustomTooltip = ({\n    active,\n    payload,\n    label\n  }) => {\n    if (active && payload && payload.length) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1,\n          border: '1px solid #ccc'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `${label}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), payload.map((entry, index) => /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          style: {\n            color: entry.color\n          },\n          children: `${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderCustomizedLabel = ({\n    cx,\n    cy,\n    midAngle,\n    innerRadius,\n    outerRadius,\n    percent\n  }) => {\n    if (percent < 0.05) return null;\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n    return /*#__PURE__*/_jsxDEV(\"text\", {\n      x: x,\n      y: y,\n      fill: \"white\",\n      textAnchor: x > cx ? 'start' : 'end',\n      dominantBaseline: \"central\",\n      fontSize: \"12\",\n      fontWeight: \"bold\",\n      children: `${(percent * 100).toFixed(0)}%`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mt: 2\n    },\n    children: [data.metri_orfani && data.metri_orfani.metri_orfani_totali > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 0,\n            border: '2px solid #d32f2f',\n            borderRadius: 2,\n            bgcolor: '#ffebee'\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600,\n                color: '#d32f2f',\n                mb: 2\n              },\n              children: \"\\uD83D\\uDEA8 METRI POSATI SENZA TRACCIABILIT\\xC0 BOBINA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                color: '#c62828',\n                fontWeight: 500,\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [data.metri_orfani.metri_orfani_totali, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 19\n              }, this), \" installati con BOBINA_VUOTA (\", data.metri_orfani.num_cavi_orfani, \" cavi)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this), data.metri_orfani.dettaglio_per_categoria && Object.keys(data.metri_orfani.dettaglio_per_categoria).length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                sx: {\n                  color: '#d32f2f',\n                  fontWeight: 600,\n                  mb: 1\n                },\n                children: \"Dettaglio per tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 21\n              }, this), Object.values(data.metri_orfani.dettaglio_per_categoria).map((categoria, index) => /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#8d6e63',\n                  ml: 2\n                },\n                children: [\"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [categoria.tipologia, \" \", categoria.formazione]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 27\n                }, this), \": \", categoria.metri_orfani, \"m (\", categoria.num_cavi_orfani, \" cavi)\"]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 23\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                bgcolor: '#ffcdd2',\n                p: 2,\n                borderRadius: 1,\n                border: '1px solid #ef5350'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#b71c1c',\n                  fontWeight: 500,\n                  mb: 1\n                },\n                children: [\"\\u26A0\\uFE0F \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"ATTENZIONE ACQUISTI:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 24\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#8d6e63'\n                },\n                children: [\"Prima di acquistare nuovo materiale, associare questi metri a bobine esistenti o registrare nuove bobine.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Rischio:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this), \" Sovra-acquisto di materiale se non gestito correttamente.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 0,\n            border: '1px solid #e0e0e0',\n            borderRadius: 2,\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: '#f8f9fa',\n              p: 2,\n              borderBottom: '1px solid #e0e0e0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontWeight: 600,\n                color: '#2c3e50'\n              },\n              children: \"\\uD83D\\uDCCB Bill of Quantities - Distinta Materiali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: '#666',\n                mt: 0.5\n              },\n              children: \"Riepilogo completo dei materiali per tipologia di cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              overflow: 'auto',\n              width: '100%'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              style: {\n                width: '100%',\n                borderCollapse: 'collapse',\n                minWidth: '1200px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  style: {\n                    backgroundColor: '#f8f9fa'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'left',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Tipologia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Formazione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"N\\xB0 Cavi\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"N\\xB0 Cavi Rimanenti\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Teorici\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Posati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Rimanenti\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Acquistati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Residui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri da Posare\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    },\n                    children: \"Metri Mancanti\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0'\n                    },\n                    children: \"Necessita Acquisto\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: caviData.map((cavo, index) => {\n                  const percentuale = cavo.metri_teorici > 0 ? (cavo.metri_reali || 0) / cavo.metri_teorici * 100 : 0;\n                  const isCompleto = percentuale >= 100;\n                  const isInCorso = percentuale > 0 && percentuale < 100;\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    style: {\n                      backgroundColor: cavo.ha_bobina_vuota ? '#ffebee' : index % 2 === 0 ? '#ffffff' : '#fafafa',\n                      borderLeft: cavo.ha_bobina_vuota ? '4px solid #d32f2f' : 'none'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        fontSize: '13px',\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0',\n                        fontWeight: cavo.ha_bobina_vuota ? 600 : 500\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            width: '4px',\n                            height: '20px',\n                            backgroundColor: cavo.ha_bobina_vuota ? '#d32f2f' : cavo.color,\n                            borderRadius: '2px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 317,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: cavo.ha_bobina_vuota ? '#d32f2f' : 'inherit'\n                          },\n                          children: [cavo.ha_bobina_vuota && '⚠️ ', cavo.tipologia]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 323,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 316,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 309,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'center',\n                        fontSize: '13px',\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: cavo.formazione || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 329,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'center',\n                        fontSize: '13px',\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: cavo.num_cavi\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'center',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: cavo.num_cavi_rimanenti > 0 ? COLORS.warning : COLORS.success,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'inline-flex',\n                          alignItems: 'center',\n                          gap: 0.5,\n                          px: 1,\n                          py: 0.5,\n                          borderRadius: '12px',\n                          fontSize: '12px',\n                          fontWeight: 600,\n                          backgroundColor: cavo.num_cavi_rimanenti > 0 ? '#fff3cd' : '#e8f5e8',\n                          color: cavo.num_cavi_rimanenti > 0 ? '#856404' : '#2e7d32'\n                        },\n                        children: cavo.num_cavi_rimanenti || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 352,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 343,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_teorici || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 367,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: COLORS.success,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_reali || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: COLORS.warning,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_da_posare || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 384,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: COLORS.info,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_acquistati || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 393,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: cavo.metri_residui > 0 ? COLORS.success : COLORS.error,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_residui || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: COLORS.warning,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_da_posare || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 411,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'right',\n                        fontSize: '13px',\n                        fontWeight: 600,\n                        color: cavo.metri_mancanti > 0 ? COLORS.error : COLORS.success,\n                        borderBottom: '1px solid #f0f0f0',\n                        borderRight: '1px solid #f0f0f0'\n                      },\n                      children: [(cavo.metri_mancanti || 0).toFixed(1), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '12px 16px',\n                        textAlign: 'center',\n                        fontSize: '13px',\n                        borderBottom: '1px solid #f0f0f0'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'inline-flex',\n                          alignItems: 'center',\n                          gap: 0.5,\n                          px: 1,\n                          py: 0.5,\n                          borderRadius: '12px',\n                          fontSize: '11px',\n                          fontWeight: 600,\n                          backgroundColor: cavo.necessita_acquisto ? '#ffebee' : '#e8f5e8',\n                          color: cavo.necessita_acquisto ? '#c62828' : '#2e7d32'\n                        },\n                        children: cavo.necessita_acquisto ? '🛒 Sì' : '✅ No'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 435,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 429,\n                      columnNumber: 25\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: '#f8f9fa',\n              p: 2,\n              borderTop: '1px solid #e0e0e0'\n            },\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      color: COLORS.primary,\n                      fontWeight: 600\n                    },\n                    children: [((_data$riepilogo = data.riepilogo) === null || _data$riepilogo === void 0 ? void 0 : (_data$riepilogo$total = _data$riepilogo.totale_metri_teorici) === null || _data$riepilogo$total === void 0 ? void 0 : _data$riepilogo$total.toFixed(1)) || totaliCavi.teorici.toFixed(1), \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Metri Teorici Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      color: COLORS.success,\n                      fontWeight: 600\n                    },\n                    children: [((_data$riepilogo2 = data.riepilogo) === null || _data$riepilogo2 === void 0 ? void 0 : (_data$riepilogo2$tota = _data$riepilogo2.totale_metri_posati) === null || _data$riepilogo2$tota === void 0 ? void 0 : _data$riepilogo2$tota.toFixed(1)) || totaliCavi.reali.toFixed(1), \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Metri Posati Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      color: COLORS.warning,\n                      fontWeight: 600\n                    },\n                    children: [((_data$riepilogo3 = data.riepilogo) === null || _data$riepilogo3 === void 0 ? void 0 : (_data$riepilogo3$tota = _data$riepilogo3.totale_metri_da_posare) === null || _data$riepilogo3$tota === void 0 ? void 0 : _data$riepilogo3$tota.toFixed(1)) || totaliCavi.da_posare.toFixed(1), \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Metri Rimanenti\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      color: COLORS.primary,\n                      fontWeight: 600\n                    },\n                    children: [((_data$riepilogo4 = data.riepilogo) === null || _data$riepilogo4 === void 0 ? void 0 : (_data$riepilogo4$perc = _data$riepilogo4.percentuale_completamento) === null || _data$riepilogo4$perc === void 0 ? void 0 : _data$riepilogo4$perc.toFixed(1)) || (totaliCavi.teorici > 0 ? (totaliCavi.reali / totaliCavi.teorici * 100).toFixed(1) : 0), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Completamento Totale\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: '#e8f5e8',\n              p: 3,\n              borderTop: '1px solid #e0e0e0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: '#2e7d32',\n                fontWeight: 600,\n                mb: 2\n              },\n              children: [\"\\uD83D\\uDCCA \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"LOGICA CALCOLO BILL OF QUANTITIES\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 20\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: '#388e3c',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri da acquistare per completamento progetto:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 17\n              }, this), \" \", ((_data$riepilogo5 = data.riepilogo) === null || _data$riepilogo5 === void 0 ? void 0 : (_data$riepilogo5$tota = _data$riepilogo5.totale_metri_mancanti) === null || _data$riepilogo5$tota === void 0 ? void 0 : _data$riepilogo5$tota.toFixed(1)) || 0, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: '#4caf50',\n                mb: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Dettaglio calcolo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: '#66bb6a',\n                ml: 2\n              },\n              children: [\"\\u2022 Metri ancora da posare: \", ((_data$riepilogo6 = data.riepilogo) === null || _data$riepilogo6 === void 0 ? void 0 : (_data$riepilogo6$tota = _data$riepilogo6.totale_metri_da_posare) === null || _data$riepilogo6$tota === void 0 ? void 0 : _data$riepilogo6$tota.toFixed(1)) || 0, \"m\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 17\n              }, this), \"\\u2022 Metri residui in magazzino: \", ((_data$riepilogo7 = data.riepilogo) === null || _data$riepilogo7 === void 0 ? void 0 : (_data$riepilogo7$tota = _data$riepilogo7.totale_metri_residui) === null || _data$riepilogo7$tota === void 0 ? void 0 : _data$riepilogo7$tota.toFixed(1)) || 0, \"m\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 17\n              }, this), \"\\u2022 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Fabbisogno netto:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this), \" \", ((_data$riepilogo8 = data.riepilogo) === null || _data$riepilogo8 === void 0 ? void 0 : (_data$riepilogo8$tota = _data$riepilogo8.totale_metri_mancanti) === null || _data$riepilogo8$tota === void 0 ? void 0 : _data$riepilogo8$tota.toFixed(1)) || 0, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 15\n            }, this), data.metri_orfani && data.metri_orfani.metri_orfani_totali > 0 && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2,\n                p: 2,\n                bgcolor: '#fff3e0',\n                borderRadius: 1,\n                border: '1px solid #ffb74d'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#f57c00',\n                  fontWeight: 500,\n                  mb: 1\n                },\n                children: [\"\\u26A0\\uFE0F \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"NOTA IMPORTANTE:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 24\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#ef6c00'\n                },\n                children: [\"I \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [data.metri_orfani.metri_orfani_totali, \"m posati con BOBINA_VUOTA\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 23\n                }, this), \" NON sono inclusi nel calcolo acquisti.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 21\n                }, this), \"Prima di acquistare, verificare se questi metri possono essere associati a bobine esistenti.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_c = BoqChart;\nexport default BoqChart;\nvar _c;\n$RefreshReg$(_c, \"BoqChart\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "ComposedChart", "Line", "Line<PERSON>hart", "Box", "Typography", "Grid", "Paper", "Chip", "jsxDEV", "_jsxDEV", "COLORS", "primary", "secondary", "success", "warning", "info", "error", "purple", "teal", "<PERSON><PERSON><PERSON><PERSON>", "data", "_data$distinta_materi", "_data$distinta_materi2", "_data$riepilogo", "_data$riepilogo$total", "_data$riepilogo2", "_data$riepilogo2$tota", "_data$riepilogo3", "_data$riepilogo3$tota", "_data$riepilogo4", "_data$riepilogo4$perc", "_data$riepilogo5", "_data$riepilogo5$tota", "_data$riepilogo6", "_data$riepilogo6$tota", "_data$riepilogo7", "_data$riepilogo7$tota", "_data$riepilogo8", "_data$riepilogo8$tota", "caviData", "distinta_materiali", "map", "cavo", "index", "_cavo$tipologia", "metri_te<PERSON>ci", "metri_teorici_totali", "metri_reali", "metri_reali_posati", "tipologia_short", "tipologia", "length", "substring", "color", "Object", "values", "deficit", "Math", "max", "metri_da_posare", "surplus", "bobine<PERSON><PERSON>", "filter", "item", "num_bobine", "bobina", "_bobina$tipologia", "formazione", "metri_disponibili", "totaliCavi", "reduce", "acc", "<PERSON><PERSON><PERSON>", "reali", "da_posare", "totaliData", "name", "value", "analisiData", "tipologia_full", "necessita_acquisto", "CustomTooltip", "active", "payload", "label", "sx", "p", "border", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entry", "style", "toFixed", "renderCustomizedLabel", "cx", "cy", "midAngle", "innerRadius", "outerRadius", "percent", "RADIAN", "PI", "radius", "x", "cos", "y", "sin", "fill", "textAnchor", "dominantBaseline", "fontSize", "fontWeight", "mt", "<PERSON><PERSON>_<PERSON><PERSON>i", "metri_orfani_totali", "container", "spacing", "mb", "xs", "borderRadius", "bgcolor", "num_cavi_orfani", "dettaglio_per_categoria", "keys", "categoria", "ml", "width", "borderBottom", "overflow", "borderCollapse", "min<PERSON><PERSON><PERSON>", "backgroundColor", "padding", "textAlign", "borderRight", "percentuale", "isCompleto", "isInCorso", "ha_bobina_vuota", "borderLeft", "display", "alignItems", "gap", "height", "num_cavi", "num_cavi_rimanenti", "px", "py", "<PERSON><PERSON>_a<PERSON><PERSON><PERSON>", "metri_residui", "<PERSON><PERSON>_man<PERSON>ti", "borderTop", "sm", "riepilogo", "totale_metri_teorici", "totale_metri_posati", "totale_metri_da_posare", "percentuale_completamento", "totale_metri_mancanti", "totale_metri_residui", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/charts/BoqChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Bar,\n  XAxis,\n  <PERSON>A<PERSON><PERSON>,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer,\n  <PERSON><PERSON>hart,\n  Pie,\n  Cell,\n  ComposedChart,\n  Line,\n  LineChart\n} from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\n\nconst COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f',\n  purple: '#9c27b0',\n  teal: '#00695c'\n};\n\nconst BoqChart = ({ data }) => {\n  if (!data) return null;\n\n  // Prepara dati per grafici dalla nuova struttura distinta_materiali\n  const caviData = data.distinta_materiali?.map((cavo, index) => ({\n    ...cavo,\n    // Mappa i nuovi campi ai vecchi per compatibilità\n    metri_teorici: cavo.metri_teorici_totali,\n    metri_reali: cavo.metri_reali_posati,\n    tipologia_short: cavo.tipologia?.length > 8 ? cavo.tipologia.substring(0, 8) + '...' : cavo.tipologia,\n    color: Object.values(COLORS)[index % Object.values(COLORS).length],\n    deficit: Math.max(0, cavo.metri_da_posare - (cavo.metri_reali_posati || 0)),\n    surplus: Math.max(0, (cavo.metri_reali_posati || 0) - cavo.metri_da_posare)\n  })) || [];\n\n  // Prepara dati per grafici bobine (ora inclusi nella distinta_materiali)\n  const bobineData = data.distinta_materiali?.filter(item => item.num_bobine > 0).map((bobina, index) => ({\n    tipologia: bobina.tipologia,\n    formazione: bobina.formazione,  // Aggiornato da sezione a formazione\n    num_bobine: bobina.num_bobine,\n    metri_disponibili: bobina.metri_disponibili,\n    tipologia_short: bobina.tipologia?.length > 8 ? bobina.tipologia.substring(0, 8) + '...' : bobina.tipologia,\n    color: Object.values(COLORS)[index % Object.values(COLORS).length]\n  })) || [];\n\n  // Calcola totali per grafici a torta\n  const totaliCavi = caviData.reduce((acc, cavo) => {\n    acc.teorici += cavo.metri_teorici || 0;\n    acc.reali += cavo.metri_reali || 0;\n    acc.da_posare += cavo.metri_da_posare || 0;\n    return acc;\n  }, { teorici: 0, reali: 0, da_posare: 0 });\n\n  const totaliData = [\n    { name: 'Metri Teorici', value: totaliCavi.teorici, color: COLORS.primary },\n    { name: 'Metri Reali', value: totaliCavi.reali, color: COLORS.success },\n    { name: 'Metri da Posare', value: totaliCavi.da_posare, color: COLORS.warning }\n  ];\n\n  // Analisi deficit/surplus\n  const analisiData = caviData.map(cavo => ({\n    tipologia: cavo.tipologia_short,\n    tipologia_full: cavo.tipologia,\n    deficit: cavo.deficit,\n    surplus: cavo.surplus,\n    necessita_acquisto: cavo.deficit > 0\n  }));\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {\n    if (percent < 0.05) return null;\n\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n\n    return (\n      <text\n        x={x}\n        y={y}\n        fill=\"white\"\n        textAnchor={x > cx ? 'start' : 'end'}\n        dominantBaseline=\"central\"\n        fontSize=\"12\"\n        fontWeight=\"bold\"\n      >\n        {`${(percent * 100).toFixed(0)}%`}\n      </text>\n    );\n  };\n\n  return (\n    <Box sx={{ mt: 2 }}>\n      {/* Alert per Metri Orfani - Versione Migliorata */}\n      {data.metri_orfani && data.metri_orfani.metri_orfani_totali > 0 && (\n        <Grid container spacing={3} sx={{ mb: 4 }}>\n          <Grid item xs={12}>\n            <Paper sx={{ p: 0, border: '2px solid #d32f2f', borderRadius: 2, bgcolor: '#ffebee' }}>\n              <Box sx={{ p: 3 }}>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#d32f2f', mb: 2 }}>\n                  🚨 METRI POSATI SENZA TRACCIABILITÀ BOBINA\n                </Typography>\n\n                <Typography variant=\"body1\" sx={{ color: '#c62828', fontWeight: 500, mb: 2 }}>\n                  <strong>{data.metri_orfani.metri_orfani_totali}m</strong> installati con BOBINA_VUOTA\n                  ({data.metri_orfani.num_cavi_orfani} cavi)\n                </Typography>\n\n                {/* Dettaglio per tipologia se disponibile */}\n                {data.metri_orfani.dettaglio_per_categoria && Object.keys(data.metri_orfani.dettaglio_per_categoria).length > 0 && (\n                  <Box sx={{ mb: 2 }}>\n                    <Typography variant=\"subtitle2\" sx={{ color: '#d32f2f', fontWeight: 600, mb: 1 }}>\n                      Dettaglio per tipologia:\n                    </Typography>\n                    {Object.values(data.metri_orfani.dettaglio_per_categoria).map((categoria, index) => (\n                      <Typography key={index} variant=\"body2\" sx={{ color: '#8d6e63', ml: 2 }}>\n                        • <strong>{categoria.tipologia} {categoria.formazione}</strong>: {categoria.metri_orfani}m ({categoria.num_cavi_orfani} cavi)\n                      </Typography>\n                    ))}\n                  </Box>\n                )}\n\n                <Box sx={{ bgcolor: '#ffcdd2', p: 2, borderRadius: 1, border: '1px solid #ef5350' }}>\n                  <Typography variant=\"body2\" sx={{ color: '#b71c1c', fontWeight: 500, mb: 1 }}>\n                    ⚠️ <strong>ATTENZIONE ACQUISTI:</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#8d6e63' }}>\n                    Prima di acquistare nuovo materiale, associare questi metri a bobine esistenti o registrare nuove bobine.\n                    <br />\n                    <strong>Rischio:</strong> Sovra-acquisto di materiale se non gestito correttamente.\n                  </Typography>\n                </Box>\n              </Box>\n            </Paper>\n          </Grid>\n        </Grid>\n      )}\n\n      {/* Tabella Bill of Quantities Unificata */}\n      <Grid container spacing={3}>\n        <Grid item xs={12}>\n          <Paper sx={{ p: 0, border: '1px solid #e0e0e0', borderRadius: 2, width: '100%' }}>\n            <Box sx={{\n              bgcolor: '#f8f9fa',\n              p: 2,\n              borderBottom: '1px solid #e0e0e0'\n            }}>\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                📋 Bill of Quantities - Distinta Materiali\n              </Typography>\n              <Typography variant=\"body2\" sx={{ color: '#666', mt: 0.5 }}>\n                Riepilogo completo dei materiali per tipologia di cavo\n              </Typography>\n            </Box>\n\n            <Box sx={{ overflow: 'auto', width: '100%' }}>\n              <table style={{ width: '100%', borderCollapse: 'collapse', minWidth: '1200px' }}>\n                <thead>\n                  <tr style={{ backgroundColor: '#f8f9fa' }}>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'left',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Tipologia</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Formazione</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>N° Cavi</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>N° Cavi Rimanenti</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri Teorici</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri Posati</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri Rimanenti</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri Acquistati</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri Residui</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'right',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri da Posare</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0',\n                      borderRight: '1px solid #f0f0f0'\n                    }}>Metri Mancanti</th>\n                    <th style={{\n                      padding: '12px 16px',\n                      textAlign: 'center',\n                      fontSize: '13px',\n                      fontWeight: 600,\n                      color: '#2c3e50',\n                      borderBottom: '1px solid #e0e0e0'\n                    }}>Necessita Acquisto</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {caviData.map((cavo, index) => {\n                    const percentuale = cavo.metri_teorici > 0 ?\n                      ((cavo.metri_reali || 0) / cavo.metri_teorici * 100) : 0;\n                    const isCompleto = percentuale >= 100;\n                    const isInCorso = percentuale > 0 && percentuale < 100;\n\n                    return (\n                      <tr key={index} style={{\n                        backgroundColor: cavo.ha_bobina_vuota\n                          ? '#ffebee'\n                          : (index % 2 === 0 ? '#ffffff' : '#fafafa'),\n                        borderLeft: cavo.ha_bobina_vuota ? '4px solid #d32f2f' : 'none'\n                      }}>\n                        <td style={{\n                          padding: '12px 16px',\n                          fontSize: '13px',\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0',\n                          fontWeight: cavo.ha_bobina_vuota ? 600 : 500\n                        }}>\n                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                            <Box sx={{\n                              width: '4px',\n                              height: '20px',\n                              backgroundColor: cavo.ha_bobina_vuota ? '#d32f2f' : cavo.color,\n                              borderRadius: '2px'\n                            }} />\n                            <span style={{ color: cavo.ha_bobina_vuota ? '#d32f2f' : 'inherit' }}>\n                              {cavo.ha_bobina_vuota && '⚠️ '}\n                              {cavo.tipologia}\n                            </span>\n                          </Box>\n                        </td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'center',\n                          fontSize: '13px',\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{cavo.formazione || 'N/A'}</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'center',\n                          fontSize: '13px',\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{cavo.num_cavi}</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'center',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: cavo.num_cavi_rimanenti > 0 ? COLORS.warning : COLORS.success,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>\n                          <Box sx={{\n                            display: 'inline-flex',\n                            alignItems: 'center',\n                            gap: 0.5,\n                            px: 1,\n                            py: 0.5,\n                            borderRadius: '12px',\n                            fontSize: '12px',\n                            fontWeight: 600,\n                            backgroundColor: cavo.num_cavi_rimanenti > 0 ? '#fff3cd' : '#e8f5e8',\n                            color: cavo.num_cavi_rimanenti > 0 ? '#856404' : '#2e7d32'\n                          }}>\n                            {cavo.num_cavi_rimanenti || 0}\n                          </Box>\n                        </td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_teorici || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: COLORS.success,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_reali || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: COLORS.warning,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_da_posare || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: COLORS.info,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_acquistati || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: cavo.metri_residui > 0 ? COLORS.success : COLORS.error,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_residui || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: COLORS.warning,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_da_posare || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'right',\n                          fontSize: '13px',\n                          fontWeight: 600,\n                          color: cavo.metri_mancanti > 0 ? COLORS.error : COLORS.success,\n                          borderBottom: '1px solid #f0f0f0',\n                          borderRight: '1px solid #f0f0f0'\n                        }}>{(cavo.metri_mancanti || 0).toFixed(1)}m</td>\n                        <td style={{\n                          padding: '12px 16px',\n                          textAlign: 'center',\n                          fontSize: '13px',\n                          borderBottom: '1px solid #f0f0f0'\n                        }}>\n                          <Box sx={{\n                            display: 'inline-flex',\n                            alignItems: 'center',\n                            gap: 0.5,\n                            px: 1,\n                            py: 0.5,\n                            borderRadius: '12px',\n                            fontSize: '11px',\n                            fontWeight: 600,\n                            backgroundColor: cavo.necessita_acquisto ? '#ffebee' : '#e8f5e8',\n                            color: cavo.necessita_acquisto ? '#c62828' : '#2e7d32'\n                          }}>\n                            {cavo.necessita_acquisto ? '🛒 Sì' : '✅ No'}\n                          </Box>\n                        </td>\n                      </tr>\n                    );\n                  })}\n                </tbody>\n              </table>\n            </Box>\n\n            {/* Totali in fondo */}\n            <Box sx={{\n              bgcolor: '#f8f9fa',\n              p: 2,\n              borderTop: '1px solid #e0e0e0'\n            }}>\n              <Grid container spacing={3}>\n                <Grid item xs={12} sm={3}>\n                  <Box sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"h6\" sx={{ color: COLORS.primary, fontWeight: 600 }}>\n                      {data.riepilogo?.totale_metri_teorici?.toFixed(1) || totaliCavi.teorici.toFixed(1)}m\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                      Metri Teorici Totali\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={12} sm={3}>\n                  <Box sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"h6\" sx={{ color: COLORS.success, fontWeight: 600 }}>\n                      {data.riepilogo?.totale_metri_posati?.toFixed(1) || totaliCavi.reali.toFixed(1)}m\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                      Metri Posati Totali\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={12} sm={3}>\n                  <Box sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"h6\" sx={{ color: COLORS.warning, fontWeight: 600 }}>\n                      {data.riepilogo?.totale_metri_da_posare?.toFixed(1) || totaliCavi.da_posare.toFixed(1)}m\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                      Metri Rimanenti\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={12} sm={3}>\n                  <Box sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"h6\" sx={{ color: COLORS.primary, fontWeight: 600 }}>\n                      {data.riepilogo?.percentuale_completamento?.toFixed(1) || (totaliCavi.teorici > 0 ? ((totaliCavi.reali / totaliCavi.teorici) * 100).toFixed(1) : 0)}%\n                    </Typography>\n                    <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                      Completamento Totale\n                    </Typography>\n                  </Box>\n                </Grid>\n              </Grid>\n            </Box>\n\n            {/* Spiegazione Logica BOQ Migliorata */}\n            <Box sx={{\n              bgcolor: '#e8f5e8',\n              p: 3,\n              borderTop: '1px solid #e0e0e0'\n            }}>\n              <Typography variant=\"body2\" sx={{ color: '#2e7d32', fontWeight: 600, mb: 2 }}>\n                📊 <strong>LOGICA CALCOLO BILL OF QUANTITIES</strong>\n              </Typography>\n\n              <Typography variant=\"body2\" sx={{ color: '#388e3c', mb: 2 }}>\n                <strong>Metri da acquistare per completamento progetto:</strong> {data.riepilogo?.totale_metri_mancanti?.toFixed(1) || 0}m\n              </Typography>\n\n              <Typography variant=\"body2\" sx={{ color: '#4caf50', mb: 1 }}>\n                <strong>Dettaglio calcolo:</strong>\n              </Typography>\n              <Typography variant=\"body2\" sx={{ color: '#66bb6a', ml: 2 }}>\n                • Metri ancora da posare: {data.riepilogo?.totale_metri_da_posare?.toFixed(1) || 0}m\n                <br />\n                • Metri residui in magazzino: {data.riepilogo?.totale_metri_residui?.toFixed(1) || 0}m\n                <br />\n                • <strong>Fabbisogno netto:</strong> {data.riepilogo?.totale_metri_mancanti?.toFixed(1) || 0}m\n              </Typography>\n\n              {data.metri_orfani && data.metri_orfani.metri_orfani_totali > 0 && (\n                <Box sx={{ mt: 2, p: 2, bgcolor: '#fff3e0', borderRadius: 1, border: '1px solid #ffb74d' }}>\n                  <Typography variant=\"body2\" sx={{ color: '#f57c00', fontWeight: 500, mb: 1 }}>\n                    ⚠️ <strong>NOTA IMPORTANTE:</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#ef6c00' }}>\n                    I <strong>{data.metri_orfani.metri_orfani_totali}m posati con BOBINA_VUOTA</strong> NON sono inclusi nel calcolo acquisti.\n                    <br />\n                    Prima di acquistare, verificare se questi metri possono essere associati a bobine esistenti.\n                  </Typography>\n                </Box>\n              )}\n            </Box>\n          </Paper>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default BoqChart;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,aAAa,EACbC,IAAI,EACJC,SAAS,QACJ,UAAU;AACjB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,MAAM,GAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE,SAAS;EACjBC,IAAI,EAAE;AACR,CAAC;AAED,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;EAC7B,IAAI,CAAClB,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA,MAAMmB,QAAQ,GAAG,EAAAlB,qBAAA,GAAAD,IAAI,CAACoB,kBAAkB,cAAAnB,qBAAA,uBAAvBA,qBAAA,CAAyBoB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK;IAAA,IAAAC,eAAA;IAAA,OAAM;MAC9D,GAAGF,IAAI;MACP;MACAG,aAAa,EAAEH,IAAI,CAACI,oBAAoB;MACxCC,WAAW,EAAEL,IAAI,CAACM,kBAAkB;MACpCC,eAAe,EAAE,EAAAL,eAAA,GAAAF,IAAI,CAACQ,SAAS,cAAAN,eAAA,uBAAdA,eAAA,CAAgBO,MAAM,IAAG,CAAC,GAAGT,IAAI,CAACQ,SAAS,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGV,IAAI,CAACQ,SAAS;MACrGG,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC7C,MAAM,CAAC,CAACiC,KAAK,GAAGW,MAAM,CAACC,MAAM,CAAC7C,MAAM,CAAC,CAACyC,MAAM,CAAC;MAClEK,OAAO,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEhB,IAAI,CAACiB,eAAe,IAAIjB,IAAI,CAACM,kBAAkB,IAAI,CAAC,CAAC,CAAC;MAC3EY,OAAO,EAAEH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAChB,IAAI,CAACM,kBAAkB,IAAI,CAAC,IAAIN,IAAI,CAACiB,eAAe;IAC5E,CAAC;EAAA,CAAC,CAAC,KAAI,EAAE;;EAET;EACA,MAAME,UAAU,GAAG,EAAAvC,sBAAA,GAAAF,IAAI,CAACoB,kBAAkB,cAAAlB,sBAAA,uBAAvBA,sBAAA,CAAyBwC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC,CAACvB,GAAG,CAAC,CAACwB,MAAM,EAAEtB,KAAK;IAAA,IAAAuB,iBAAA;IAAA,OAAM;MACtGhB,SAAS,EAAEe,MAAM,CAACf,SAAS;MAC3BiB,UAAU,EAAEF,MAAM,CAACE,UAAU;MAAG;MAChCH,UAAU,EAAEC,MAAM,CAACD,UAAU;MAC7BI,iBAAiB,EAAEH,MAAM,CAACG,iBAAiB;MAC3CnB,eAAe,EAAE,EAAAiB,iBAAA,GAAAD,MAAM,CAACf,SAAS,cAAAgB,iBAAA,uBAAhBA,iBAAA,CAAkBf,MAAM,IAAG,CAAC,GAAGc,MAAM,CAACf,SAAS,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGa,MAAM,CAACf,SAAS;MAC3GG,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC7C,MAAM,CAAC,CAACiC,KAAK,GAAGW,MAAM,CAACC,MAAM,CAAC7C,MAAM,CAAC,CAACyC,MAAM;IACnE,CAAC;EAAA,CAAC,CAAC,KAAI,EAAE;;EAET;EACA,MAAMkB,UAAU,GAAG9B,QAAQ,CAAC+B,MAAM,CAAC,CAACC,GAAG,EAAE7B,IAAI,KAAK;IAChD6B,GAAG,CAACC,OAAO,IAAI9B,IAAI,CAACG,aAAa,IAAI,CAAC;IACtC0B,GAAG,CAACE,KAAK,IAAI/B,IAAI,CAACK,WAAW,IAAI,CAAC;IAClCwB,GAAG,CAACG,SAAS,IAAIhC,IAAI,CAACiB,eAAe,IAAI,CAAC;IAC1C,OAAOY,GAAG;EACZ,CAAC,EAAE;IAAEC,OAAO,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAE,CAAC,CAAC;EAE1C,MAAMC,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAER,UAAU,CAACG,OAAO;IAAEnB,KAAK,EAAE3C,MAAM,CAACC;EAAQ,CAAC,EAC3E;IAAEiE,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAER,UAAU,CAACI,KAAK;IAAEpB,KAAK,EAAE3C,MAAM,CAACG;EAAQ,CAAC,EACvE;IAAE+D,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAER,UAAU,CAACK,SAAS;IAAErB,KAAK,EAAE3C,MAAM,CAACI;EAAQ,CAAC,CAChF;;EAED;EACA,MAAMgE,WAAW,GAAGvC,QAAQ,CAACE,GAAG,CAACC,IAAI,KAAK;IACxCQ,SAAS,EAAER,IAAI,CAACO,eAAe;IAC/B8B,cAAc,EAAErC,IAAI,CAACQ,SAAS;IAC9BM,OAAO,EAAEd,IAAI,CAACc,OAAO;IACrBI,OAAO,EAAElB,IAAI,CAACkB,OAAO;IACrBoB,kBAAkB,EAAEtC,IAAI,CAACc,OAAO,GAAG;EACrC,CAAC,CAAC,CAAC;EAEH,MAAMyB,aAAa,GAAGA,CAAC;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAM,CAAC,KAAK;IACpD,IAAIF,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAAChC,MAAM,EAAE;MACvC,oBACE1C,OAAA,CAACH,KAAK;QAAC+E,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAiB,CAAE;QAAAC,QAAA,gBAC5C/E,OAAA,CAACL,UAAU;UAACqF,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,GAAGJ,KAAK;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,EACpDV,OAAO,CAAC1C,GAAG,CAAC,CAACqD,KAAK,EAAEnD,KAAK,kBACxBlC,OAAA,CAACL,UAAU;UAAaqF,OAAO,EAAC,OAAO;UAACM,KAAK,EAAE;YAAE1C,KAAK,EAAEyC,KAAK,CAACzC;UAAM,CAAE;UAAAmC,QAAA,EACnE,GAAGM,KAAK,CAAClB,IAAI,KAAK,OAAOkB,KAAK,CAACjB,KAAK,KAAK,QAAQ,GAAGiB,KAAK,CAACjB,KAAK,CAACmB,OAAO,CAAC,CAAC,CAAC,GAAGF,KAAK,CAACjB,KAAK;QAAE,GAD5ElC,KAAK;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMI,qBAAqB,GAAGA,CAAC;IAAEC,EAAE;IAAEC,EAAE;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,WAAW;IAAEC;EAAQ,CAAC,KAAK;IACzF,IAAIA,OAAO,GAAG,IAAI,EAAE,OAAO,IAAI;IAE/B,MAAMC,MAAM,GAAG/C,IAAI,CAACgD,EAAE,GAAG,GAAG;IAC5B,MAAMC,MAAM,GAAGL,WAAW,GAAG,CAACC,WAAW,GAAGD,WAAW,IAAI,GAAG;IAC9D,MAAMM,CAAC,GAAGT,EAAE,GAAGQ,MAAM,GAAGjD,IAAI,CAACmD,GAAG,CAAC,CAACR,QAAQ,GAAGI,MAAM,CAAC;IACpD,MAAMK,CAAC,GAAGV,EAAE,GAAGO,MAAM,GAAGjD,IAAI,CAACqD,GAAG,CAAC,CAACV,QAAQ,GAAGI,MAAM,CAAC;IAEpD,oBACE/F,OAAA;MACEkG,CAAC,EAAEA,CAAE;MACLE,CAAC,EAAEA,CAAE;MACLE,IAAI,EAAC,OAAO;MACZC,UAAU,EAAEL,CAAC,GAAGT,EAAE,GAAG,OAAO,GAAG,KAAM;MACrCe,gBAAgB,EAAC,SAAS;MAC1BC,QAAQ,EAAC,IAAI;MACbC,UAAU,EAAC,MAAM;MAAA3B,QAAA,EAEhB,GAAG,CAACe,OAAO,GAAG,GAAG,EAAEP,OAAO,CAAC,CAAC,CAAC;IAAG;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEX,CAAC;EAED,oBACEpF,OAAA,CAACN,GAAG;IAACkF,EAAE,EAAE;MAAE+B,EAAE,EAAE;IAAE,CAAE;IAAA5B,QAAA,GAEhBpE,IAAI,CAACiG,YAAY,IAAIjG,IAAI,CAACiG,YAAY,CAACC,mBAAmB,GAAG,CAAC,iBAC7D7G,OAAA,CAACJ,IAAI;MAACkH,SAAS;MAACC,OAAO,EAAE,CAAE;MAACnC,EAAE,EAAE;QAAEoC,EAAE,EAAE;MAAE,CAAE;MAAAjC,QAAA,eACxC/E,OAAA,CAACJ,IAAI;QAAC0D,IAAI;QAAC2D,EAAE,EAAE,EAAG;QAAAlC,QAAA,eAChB/E,OAAA,CAACH,KAAK;UAAC+E,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEC,MAAM,EAAE,mBAAmB;YAAEoC,YAAY,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAApC,QAAA,eACpF/E,OAAA,CAACN,GAAG;YAACkF,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBAChB/E,OAAA,CAACL,UAAU;cAACqF,OAAO,EAAC,IAAI;cAACJ,EAAE,EAAE;gBAAE8B,UAAU,EAAE,GAAG;gBAAE9D,KAAK,EAAE,SAAS;gBAAEoE,EAAE,EAAE;cAAE,CAAE;cAAAjC,QAAA,EAAC;YAE3E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbpF,OAAA,CAACL,UAAU;cAACqF,OAAO,EAAC,OAAO;cAACJ,EAAE,EAAE;gBAAEhC,KAAK,EAAE,SAAS;gBAAE8D,UAAU,EAAE,GAAG;gBAAEM,EAAE,EAAE;cAAE,CAAE;cAAAjC,QAAA,gBAC3E/E,OAAA;gBAAA+E,QAAA,GAASpE,IAAI,CAACiG,YAAY,CAACC,mBAAmB,EAAC,GAAC;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,kCACxD,EAACzE,IAAI,CAACiG,YAAY,CAACQ,eAAe,EAAC,QACtC;YAAA;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAGZzE,IAAI,CAACiG,YAAY,CAACS,uBAAuB,IAAIxE,MAAM,CAACyE,IAAI,CAAC3G,IAAI,CAACiG,YAAY,CAACS,uBAAuB,CAAC,CAAC3E,MAAM,GAAG,CAAC,iBAC7G1C,OAAA,CAACN,GAAG;cAACkF,EAAE,EAAE;gBAAEoC,EAAE,EAAE;cAAE,CAAE;cAAAjC,QAAA,gBACjB/E,OAAA,CAACL,UAAU;gBAACqF,OAAO,EAAC,WAAW;gBAACJ,EAAE,EAAE;kBAAEhC,KAAK,EAAE,SAAS;kBAAE8D,UAAU,EAAE,GAAG;kBAAEM,EAAE,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,EAAC;cAElF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZvC,MAAM,CAACC,MAAM,CAACnC,IAAI,CAACiG,YAAY,CAACS,uBAAuB,CAAC,CAACrF,GAAG,CAAC,CAACuF,SAAS,EAAErF,KAAK,kBAC7ElC,OAAA,CAACL,UAAU;gBAAaqF,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAEhC,KAAK,EAAE,SAAS;kBAAE4E,EAAE,EAAE;gBAAE,CAAE;gBAAAzC,QAAA,GAAC,SACrE,eAAA/E,OAAA;kBAAA+E,QAAA,GAASwC,SAAS,CAAC9E,SAAS,EAAC,GAAC,EAAC8E,SAAS,CAAC7D,UAAU;gBAAA;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,MAAE,EAACmC,SAAS,CAACX,YAAY,EAAC,KAAG,EAACW,SAAS,CAACH,eAAe,EAAC,QACzH;cAAA,GAFiBlF,KAAK;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDpF,OAAA,CAACN,GAAG;cAACkF,EAAE,EAAE;gBAAEuC,OAAO,EAAE,SAAS;gBAAEtC,CAAC,EAAE,CAAC;gBAAEqC,YAAY,EAAE,CAAC;gBAAEpC,MAAM,EAAE;cAAoB,CAAE;cAAAC,QAAA,gBAClF/E,OAAA,CAACL,UAAU;gBAACqF,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAEhC,KAAK,EAAE,SAAS;kBAAE8D,UAAU,EAAE,GAAG;kBAAEM,EAAE,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,GAAC,eACzE,eAAA/E,OAAA;kBAAA+E,QAAA,EAAQ;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACbpF,OAAA,CAACL,UAAU;gBAACqF,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAEhC,KAAK,EAAE;gBAAU,CAAE;gBAAAmC,QAAA,GAAC,2GAEpD,eAAA/E,OAAA;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNpF,OAAA;kBAAA+E,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,8DAC3B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACP,eAGDpF,OAAA,CAACJ,IAAI;MAACkH,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAhC,QAAA,eACzB/E,OAAA,CAACJ,IAAI;QAAC0D,IAAI;QAAC2D,EAAE,EAAE,EAAG;QAAAlC,QAAA,eAChB/E,OAAA,CAACH,KAAK;UAAC+E,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEC,MAAM,EAAE,mBAAmB;YAAEoC,YAAY,EAAE,CAAC;YAAEO,KAAK,EAAE;UAAO,CAAE;UAAA1C,QAAA,gBAC/E/E,OAAA,CAACN,GAAG;YAACkF,EAAE,EAAE;cACPuC,OAAO,EAAE,SAAS;cAClBtC,CAAC,EAAE,CAAC;cACJ6C,YAAY,EAAE;YAChB,CAAE;YAAA3C,QAAA,gBACA/E,OAAA,CAACL,UAAU;cAACqF,OAAO,EAAC,IAAI;cAACJ,EAAE,EAAE;gBAAE8B,UAAU,EAAE,GAAG;gBAAE9D,KAAK,EAAE;cAAU,CAAE;cAAAmC,QAAA,EAAC;YAEpE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpF,OAAA,CAACL,UAAU;cAACqF,OAAO,EAAC,OAAO;cAACJ,EAAE,EAAE;gBAAEhC,KAAK,EAAE,MAAM;gBAAE+D,EAAE,EAAE;cAAI,CAAE;cAAA5B,QAAA,EAAC;YAE5D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENpF,OAAA,CAACN,GAAG;YAACkF,EAAE,EAAE;cAAE+C,QAAQ,EAAE,MAAM;cAAEF,KAAK,EAAE;YAAO,CAAE;YAAA1C,QAAA,eAC3C/E,OAAA;cAAOsF,KAAK,EAAE;gBAAEmC,KAAK,EAAE,MAAM;gBAAEG,cAAc,EAAE,UAAU;gBAAEC,QAAQ,EAAE;cAAS,CAAE;cAAA9C,QAAA,gBAC9E/E,OAAA;gBAAA+E,QAAA,eACE/E,OAAA;kBAAIsF,KAAK,EAAE;oBAAEwC,eAAe,EAAE;kBAAU,CAAE;kBAAA/C,QAAA,gBACxC/E,OAAA;oBAAIsF,KAAK,EAAE;sBACTyC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,MAAM;sBACjBvB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChB8E,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAlD,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjBpF,OAAA;oBAAIsF,KAAK,EAAE;sBACTyC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,QAAQ;sBACnBvB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChB8E,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAlD,QAAA,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBpF,OAAA;oBAAIsF,KAAK,EAAE;sBACTyC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,QAAQ;sBACnBvB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChB8E,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAlD,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfpF,OAAA;oBAAIsF,KAAK,EAAE;sBACTyC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,QAAQ;sBACnBvB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChB8E,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAlD,QAAA,EAAC;kBAAiB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzBpF,OAAA;oBAAIsF,KAAK,EAAE;sBACTyC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBvB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChB8E,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAlD,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrBpF,OAAA;oBAAIsF,KAAK,EAAE;sBACTyC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBvB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChB8E,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAlD,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpBpF,OAAA;oBAAIsF,KAAK,EAAE;sBACTyC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBvB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChB8E,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAlD,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBpF,OAAA;oBAAIsF,KAAK,EAAE;sBACTyC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBvB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChB8E,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAlD,QAAA,EAAC;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxBpF,OAAA;oBAAIsF,KAAK,EAAE;sBACTyC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBvB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChB8E,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAlD,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrBpF,OAAA;oBAAIsF,KAAK,EAAE;sBACTyC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,OAAO;sBAClBvB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChB8E,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAlD,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBpF,OAAA;oBAAIsF,KAAK,EAAE;sBACTyC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,QAAQ;sBACnBvB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChB8E,YAAY,EAAE,mBAAmB;sBACjCO,WAAW,EAAE;oBACf,CAAE;oBAAAlD,QAAA,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtBpF,OAAA;oBAAIsF,KAAK,EAAE;sBACTyC,OAAO,EAAE,WAAW;sBACpBC,SAAS,EAAE,QAAQ;sBACnBvB,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,GAAG;sBACf9D,KAAK,EAAE,SAAS;sBAChB8E,YAAY,EAAE;oBAChB,CAAE;oBAAA3C,QAAA,EAAC;kBAAkB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRpF,OAAA;gBAAA+E,QAAA,EACGjD,QAAQ,CAACE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;kBAC7B,MAAMgG,WAAW,GAAGjG,IAAI,CAACG,aAAa,GAAG,CAAC,GACvC,CAACH,IAAI,CAACK,WAAW,IAAI,CAAC,IAAIL,IAAI,CAACG,aAAa,GAAG,GAAG,GAAI,CAAC;kBAC1D,MAAM+F,UAAU,GAAGD,WAAW,IAAI,GAAG;kBACrC,MAAME,SAAS,GAAGF,WAAW,GAAG,CAAC,IAAIA,WAAW,GAAG,GAAG;kBAEtD,oBACElI,OAAA;oBAAgBsF,KAAK,EAAE;sBACrBwC,eAAe,EAAE7F,IAAI,CAACoG,eAAe,GACjC,SAAS,GACRnG,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,SAAU;sBAC7CoG,UAAU,EAAErG,IAAI,CAACoG,eAAe,GAAG,mBAAmB,GAAG;oBAC3D,CAAE;oBAAAtD,QAAA,gBACA/E,OAAA;sBAAIsF,KAAK,EAAE;wBACTyC,OAAO,EAAE,WAAW;wBACpBtB,QAAQ,EAAE,MAAM;wBAChBiB,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE,mBAAmB;wBAChCvB,UAAU,EAAEzE,IAAI,CAACoG,eAAe,GAAG,GAAG,GAAG;sBAC3C,CAAE;sBAAAtD,QAAA,eACA/E,OAAA,CAACN,GAAG;wBAACkF,EAAE,EAAE;0BAAE2D,OAAO,EAAE,MAAM;0BAAEC,UAAU,EAAE,QAAQ;0BAAEC,GAAG,EAAE;wBAAE,CAAE;wBAAA1D,QAAA,gBACzD/E,OAAA,CAACN,GAAG;0BAACkF,EAAE,EAAE;4BACP6C,KAAK,EAAE,KAAK;4BACZiB,MAAM,EAAE,MAAM;4BACdZ,eAAe,EAAE7F,IAAI,CAACoG,eAAe,GAAG,SAAS,GAAGpG,IAAI,CAACW,KAAK;4BAC9DsE,YAAY,EAAE;0BAChB;wBAAE;0BAAAjC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACLpF,OAAA;0BAAMsF,KAAK,EAAE;4BAAE1C,KAAK,EAAEX,IAAI,CAACoG,eAAe,GAAG,SAAS,GAAG;0BAAU,CAAE;0BAAAtD,QAAA,GAClE9C,IAAI,CAACoG,eAAe,IAAI,KAAK,EAC7BpG,IAAI,CAACQ,SAAS;wBAAA;0BAAAwC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACX,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLpF,OAAA;sBAAIsF,KAAK,EAAE;wBACTyC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,QAAQ;wBACnBvB,QAAQ,EAAE,MAAM;wBAChBiB,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAlD,QAAA,EAAE9C,IAAI,CAACyB,UAAU,IAAI;oBAAK;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAClCpF,OAAA;sBAAIsF,KAAK,EAAE;wBACTyC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,QAAQ;wBACnBvB,QAAQ,EAAE,MAAM;wBAChBiB,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAlD,QAAA,EAAE9C,IAAI,CAAC0G;oBAAQ;sBAAA1D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvBpF,OAAA;sBAAIsF,KAAK,EAAE;wBACTyC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,QAAQ;wBACnBvB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAEX,IAAI,CAAC2G,kBAAkB,GAAG,CAAC,GAAG3I,MAAM,CAACI,OAAO,GAAGJ,MAAM,CAACG,OAAO;wBACpEsH,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAlD,QAAA,eACA/E,OAAA,CAACN,GAAG;wBAACkF,EAAE,EAAE;0BACP2D,OAAO,EAAE,aAAa;0BACtBC,UAAU,EAAE,QAAQ;0BACpBC,GAAG,EAAE,GAAG;0BACRI,EAAE,EAAE,CAAC;0BACLC,EAAE,EAAE,GAAG;0BACP5B,YAAY,EAAE,MAAM;0BACpBT,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE,GAAG;0BACfoB,eAAe,EAAE7F,IAAI,CAAC2G,kBAAkB,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;0BACpEhG,KAAK,EAAEX,IAAI,CAAC2G,kBAAkB,GAAG,CAAC,GAAG,SAAS,GAAG;wBACnD,CAAE;wBAAA7D,QAAA,EACC9C,IAAI,CAAC2G,kBAAkB,IAAI;sBAAC;wBAAA3D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACLpF,OAAA;sBAAIsF,KAAK,EAAE;wBACTyC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBvB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACfgB,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAlD,QAAA,GAAE,CAAC9C,IAAI,CAACG,aAAa,IAAI,CAAC,EAAEmD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/CpF,OAAA;sBAAIsF,KAAK,EAAE;wBACTyC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBvB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAE3C,MAAM,CAACG,OAAO;wBACrBsH,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAlD,QAAA,GAAE,CAAC9C,IAAI,CAACK,WAAW,IAAI,CAAC,EAAEiD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC7CpF,OAAA;sBAAIsF,KAAK,EAAE;wBACTyC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBvB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAE3C,MAAM,CAACI,OAAO;wBACrBqH,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAlD,QAAA,GAAE,CAAC9C,IAAI,CAACiB,eAAe,IAAI,CAAC,EAAEqC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjDpF,OAAA;sBAAIsF,KAAK,EAAE;wBACTyC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBvB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAE3C,MAAM,CAACK,IAAI;wBAClBoH,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAlD,QAAA,GAAE,CAAC9C,IAAI,CAAC8G,gBAAgB,IAAI,CAAC,EAAExD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClDpF,OAAA;sBAAIsF,KAAK,EAAE;wBACTyC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBvB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAEX,IAAI,CAAC+G,aAAa,GAAG,CAAC,GAAG/I,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACM,KAAK;wBAC7DmH,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAlD,QAAA,GAAE,CAAC9C,IAAI,CAAC+G,aAAa,IAAI,CAAC,EAAEzD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC/CpF,OAAA;sBAAIsF,KAAK,EAAE;wBACTyC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBvB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAE3C,MAAM,CAACI,OAAO;wBACrBqH,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAlD,QAAA,GAAE,CAAC9C,IAAI,CAACiB,eAAe,IAAI,CAAC,EAAEqC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjDpF,OAAA;sBAAIsF,KAAK,EAAE;wBACTyC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,OAAO;wBAClBvB,QAAQ,EAAE,MAAM;wBAChBC,UAAU,EAAE,GAAG;wBACf9D,KAAK,EAAEX,IAAI,CAACgH,cAAc,GAAG,CAAC,GAAGhJ,MAAM,CAACM,KAAK,GAAGN,MAAM,CAACG,OAAO;wBAC9DsH,YAAY,EAAE,mBAAmB;wBACjCO,WAAW,EAAE;sBACf,CAAE;sBAAAlD,QAAA,GAAE,CAAC9C,IAAI,CAACgH,cAAc,IAAI,CAAC,EAAE1D,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChDpF,OAAA;sBAAIsF,KAAK,EAAE;wBACTyC,OAAO,EAAE,WAAW;wBACpBC,SAAS,EAAE,QAAQ;wBACnBvB,QAAQ,EAAE,MAAM;wBAChBiB,YAAY,EAAE;sBAChB,CAAE;sBAAA3C,QAAA,eACA/E,OAAA,CAACN,GAAG;wBAACkF,EAAE,EAAE;0BACP2D,OAAO,EAAE,aAAa;0BACtBC,UAAU,EAAE,QAAQ;0BACpBC,GAAG,EAAE,GAAG;0BACRI,EAAE,EAAE,CAAC;0BACLC,EAAE,EAAE,GAAG;0BACP5B,YAAY,EAAE,MAAM;0BACpBT,QAAQ,EAAE,MAAM;0BAChBC,UAAU,EAAE,GAAG;0BACfoB,eAAe,EAAE7F,IAAI,CAACsC,kBAAkB,GAAG,SAAS,GAAG,SAAS;0BAChE3B,KAAK,EAAEX,IAAI,CAACsC,kBAAkB,GAAG,SAAS,GAAG;wBAC/C,CAAE;wBAAAQ,QAAA,EACC9C,IAAI,CAACsC,kBAAkB,GAAG,OAAO,GAAG;sBAAM;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA,GAlJElD,KAAK;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmJV,CAAC;gBAET,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGNpF,OAAA,CAACN,GAAG;YAACkF,EAAE,EAAE;cACPuC,OAAO,EAAE,SAAS;cAClBtC,CAAC,EAAE,CAAC;cACJqE,SAAS,EAAE;YACb,CAAE;YAAAnE,QAAA,eACA/E,OAAA,CAACJ,IAAI;cAACkH,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAhC,QAAA,gBACzB/E,OAAA,CAACJ,IAAI;gBAAC0D,IAAI;gBAAC2D,EAAE,EAAE,EAAG;gBAACkC,EAAE,EAAE,CAAE;gBAAApE,QAAA,eACvB/E,OAAA,CAACN,GAAG;kBAACkF,EAAE,EAAE;oBAAEoD,SAAS,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,gBAC/B/E,OAAA,CAACL,UAAU;oBAACqF,OAAO,EAAC,IAAI;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE3C,MAAM,CAACC,OAAO;sBAAEwG,UAAU,EAAE;oBAAI,CAAE;oBAAA3B,QAAA,GACrE,EAAAjE,eAAA,GAAAH,IAAI,CAACyI,SAAS,cAAAtI,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBuI,oBAAoB,cAAAtI,qBAAA,uBAApCA,qBAAA,CAAsCwE,OAAO,CAAC,CAAC,CAAC,KAAI3B,UAAU,CAACG,OAAO,CAACwB,OAAO,CAAC,CAAC,CAAC,EAAC,GACrF;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpF,OAAA,CAACL,UAAU;oBAACqF,OAAO,EAAC,SAAS;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE;oBAAO,CAAE;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACPpF,OAAA,CAACJ,IAAI;gBAAC0D,IAAI;gBAAC2D,EAAE,EAAE,EAAG;gBAACkC,EAAE,EAAE,CAAE;gBAAApE,QAAA,eACvB/E,OAAA,CAACN,GAAG;kBAACkF,EAAE,EAAE;oBAAEoD,SAAS,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,gBAC/B/E,OAAA,CAACL,UAAU;oBAACqF,OAAO,EAAC,IAAI;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE3C,MAAM,CAACG,OAAO;sBAAEsG,UAAU,EAAE;oBAAI,CAAE;oBAAA3B,QAAA,GACrE,EAAA/D,gBAAA,GAAAL,IAAI,CAACyI,SAAS,cAAApI,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBsI,mBAAmB,cAAArI,qBAAA,uBAAnCA,qBAAA,CAAqCsE,OAAO,CAAC,CAAC,CAAC,KAAI3B,UAAU,CAACI,KAAK,CAACuB,OAAO,CAAC,CAAC,CAAC,EAAC,GAClF;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpF,OAAA,CAACL,UAAU;oBAACqF,OAAO,EAAC,SAAS;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE;oBAAO,CAAE;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACPpF,OAAA,CAACJ,IAAI;gBAAC0D,IAAI;gBAAC2D,EAAE,EAAE,EAAG;gBAACkC,EAAE,EAAE,CAAE;gBAAApE,QAAA,eACvB/E,OAAA,CAACN,GAAG;kBAACkF,EAAE,EAAE;oBAAEoD,SAAS,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,gBAC/B/E,OAAA,CAACL,UAAU;oBAACqF,OAAO,EAAC,IAAI;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE3C,MAAM,CAACI,OAAO;sBAAEqG,UAAU,EAAE;oBAAI,CAAE;oBAAA3B,QAAA,GACrE,EAAA7D,gBAAA,GAAAP,IAAI,CAACyI,SAAS,cAAAlI,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBqI,sBAAsB,cAAApI,qBAAA,uBAAtCA,qBAAA,CAAwCoE,OAAO,CAAC,CAAC,CAAC,KAAI3B,UAAU,CAACK,SAAS,CAACsB,OAAO,CAAC,CAAC,CAAC,EAAC,GACzF;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpF,OAAA,CAACL,UAAU;oBAACqF,OAAO,EAAC,SAAS;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE;oBAAO,CAAE;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACPpF,OAAA,CAACJ,IAAI;gBAAC0D,IAAI;gBAAC2D,EAAE,EAAE,EAAG;gBAACkC,EAAE,EAAE,CAAE;gBAAApE,QAAA,eACvB/E,OAAA,CAACN,GAAG;kBAACkF,EAAE,EAAE;oBAAEoD,SAAS,EAAE;kBAAS,CAAE;kBAAAjD,QAAA,gBAC/B/E,OAAA,CAACL,UAAU;oBAACqF,OAAO,EAAC,IAAI;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE3C,MAAM,CAACC,OAAO;sBAAEwG,UAAU,EAAE;oBAAI,CAAE;oBAAA3B,QAAA,GACrE,EAAA3D,gBAAA,GAAAT,IAAI,CAACyI,SAAS,cAAAhI,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBoI,yBAAyB,cAAAnI,qBAAA,uBAAzCA,qBAAA,CAA2CkE,OAAO,CAAC,CAAC,CAAC,MAAK3B,UAAU,CAACG,OAAO,GAAG,CAAC,GAAG,CAAEH,UAAU,CAACI,KAAK,GAAGJ,UAAU,CAACG,OAAO,GAAI,GAAG,EAAEwB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAC,GACtJ;kBAAA;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpF,OAAA,CAACL,UAAU;oBAACqF,OAAO,EAAC,SAAS;oBAACJ,EAAE,EAAE;sBAAEhC,KAAK,EAAE;oBAAO,CAAE;oBAAAmC,QAAA,EAAC;kBAErD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNpF,OAAA,CAACN,GAAG;YAACkF,EAAE,EAAE;cACPuC,OAAO,EAAE,SAAS;cAClBtC,CAAC,EAAE,CAAC;cACJqE,SAAS,EAAE;YACb,CAAE;YAAAnE,QAAA,gBACA/E,OAAA,CAACL,UAAU;cAACqF,OAAO,EAAC,OAAO;cAACJ,EAAE,EAAE;gBAAEhC,KAAK,EAAE,SAAS;gBAAE8D,UAAU,EAAE,GAAG;gBAAEM,EAAE,EAAE;cAAE,CAAE;cAAAjC,QAAA,GAAC,eACzE,eAAA/E,OAAA;gBAAA+E,QAAA,EAAQ;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eAEbpF,OAAA,CAACL,UAAU;cAACqF,OAAO,EAAC,OAAO;cAACJ,EAAE,EAAE;gBAAEhC,KAAK,EAAE,SAAS;gBAAEoE,EAAE,EAAE;cAAE,CAAE;cAAAjC,QAAA,gBAC1D/E,OAAA;gBAAA+E,QAAA,EAAQ;cAA+C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,EAAA9D,gBAAA,GAAAX,IAAI,CAACyI,SAAS,cAAA9H,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBmI,qBAAqB,cAAAlI,qBAAA,uBAArCA,qBAAA,CAAuCgE,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,GAC3H;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbpF,OAAA,CAACL,UAAU;cAACqF,OAAO,EAAC,OAAO;cAACJ,EAAE,EAAE;gBAAEhC,KAAK,EAAE,SAAS;gBAAEoE,EAAE,EAAE;cAAE,CAAE;cAAAjC,QAAA,eAC1D/E,OAAA;gBAAA+E,QAAA,EAAQ;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACbpF,OAAA,CAACL,UAAU;cAACqF,OAAO,EAAC,OAAO;cAACJ,EAAE,EAAE;gBAAEhC,KAAK,EAAE,SAAS;gBAAE4E,EAAE,EAAE;cAAE,CAAE;cAAAzC,QAAA,GAAC,iCACjC,EAAC,EAAAvD,gBAAA,GAAAb,IAAI,CAACyI,SAAS,cAAA5H,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB+H,sBAAsB,cAAA9H,qBAAA,uBAAtCA,qBAAA,CAAwC8D,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,GACnF,eAAAvF,OAAA;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,uCACwB,EAAC,EAAA1D,gBAAA,GAAAf,IAAI,CAACyI,SAAS,cAAA1H,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBgI,oBAAoB,cAAA/H,qBAAA,uBAApCA,qBAAA,CAAsC4D,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,GACrF,eAAAvF,OAAA;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,WACJ,eAAApF,OAAA;gBAAA+E,QAAA,EAAQ;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,EAAAxD,gBAAA,GAAAjB,IAAI,CAACyI,SAAS,cAAAxH,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB6H,qBAAqB,cAAA5H,qBAAA,uBAArCA,qBAAA,CAAuC0D,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,EAAC,GAC/F;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEZzE,IAAI,CAACiG,YAAY,IAAIjG,IAAI,CAACiG,YAAY,CAACC,mBAAmB,GAAG,CAAC,iBAC7D7G,OAAA,CAACN,GAAG;cAACkF,EAAE,EAAE;gBAAE+B,EAAE,EAAE,CAAC;gBAAE9B,CAAC,EAAE,CAAC;gBAAEsC,OAAO,EAAE,SAAS;gBAAED,YAAY,EAAE,CAAC;gBAAEpC,MAAM,EAAE;cAAoB,CAAE;cAAAC,QAAA,gBACzF/E,OAAA,CAACL,UAAU;gBAACqF,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAEhC,KAAK,EAAE,SAAS;kBAAE8D,UAAU,EAAE,GAAG;kBAAEM,EAAE,EAAE;gBAAE,CAAE;gBAAAjC,QAAA,GAAC,eACzE,eAAA/E,OAAA;kBAAA+E,QAAA,EAAQ;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACbpF,OAAA,CAACL,UAAU;gBAACqF,OAAO,EAAC,OAAO;gBAACJ,EAAE,EAAE;kBAAEhC,KAAK,EAAE;gBAAU,CAAE;gBAAAmC,QAAA,GAAC,IAClD,eAAA/E,OAAA;kBAAA+E,QAAA,GAASpE,IAAI,CAACiG,YAAY,CAACC,mBAAmB,EAAC,2BAAyB;gBAAA;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,2CACnF,eAAApF,OAAA;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,gGAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACuE,EAAA,GAvgBIjJ,QAAQ;AAygBd,eAAeA,QAAQ;AAAC,IAAAiJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}