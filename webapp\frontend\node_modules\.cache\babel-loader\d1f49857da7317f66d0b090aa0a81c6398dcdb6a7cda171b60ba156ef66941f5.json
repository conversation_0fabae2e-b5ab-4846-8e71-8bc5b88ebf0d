{"ast": null, "code": "import { formatDistance } from \"./nl-BE/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./nl-BE/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./nl-BE/_lib/formatRelative.mjs\";\nimport { localize } from \"./nl-BE/_lib/localize.mjs\";\nimport { match } from \"./nl-BE/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Dutch locale.\n * @language Dutch\n * @iso-639-2 nld\n * <AUTHOR> [@jtangelder](https://github.com/jtangelder)\n * <AUTHOR> [@rubenstolk](https://github.com/rubenstolk)\n * <AUTHOR> [@bitcrumb](https://github.com/bitcrumb)\n * <AUTHOR> [@dcbn](https://github.com/dcbn)\n */\nexport const nlBE = {\n  code: \"nl-BE\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default nlBE;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "nlBE", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/nl-BE.mjs"], "sourcesContent": ["import { formatDistance } from \"./nl-BE/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./nl-BE/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./nl-BE/_lib/formatRelative.mjs\";\nimport { localize } from \"./nl-BE/_lib/localize.mjs\";\nimport { match } from \"./nl-BE/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Dutch locale.\n * @language Dutch\n * @iso-639-2 nld\n * <AUTHOR> [@jtangelder](https://github.com/jtangelder)\n * <AUTHOR> [@rubenstolk](https://github.com/rubenstolk)\n * <AUTHOR> [@bitcrumb](https://github.com/bitcrumb)\n * <AUTHOR> [@dcbn](https://github.com/dcbn)\n */\nexport const nlBE = {\n  code: \"nl-BE\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default nlBE;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,iCAAiC;AAChE,SAASC,UAAU,QAAQ,6BAA6B;AACxD,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,KAAK,QAAQ,wBAAwB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}