{"ast": null, "code": "import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsxs(React.Fragment, {\n  children: [/*#__PURE__*/_jsx(\"path\", {\n    d: \"M14.47 13.5L11 20v-5.5H9l.53-1H7V22h10v-8.5h-2.53z\"\n  }), /*#__PURE__*/_jsx(\"path\", {\n    fillOpacity: \".3\",\n    d: \"M17 4h-3V2h-4v2H7v9.5h2.53L13 7v5.5h2l-.53 1H17V4z\"\n  })]\n}), 'BatteryCharging50Sharp');", "map": {"version": 3, "names": ["React", "createSvgIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "children", "d", "fillOpacity"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/icons-material/esm/BatteryCharging50Sharp.js"], "sourcesContent": ["import * as React from 'react';\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsxs(React.Fragment, {\n  children: [/*#__PURE__*/_jsx(\"path\", {\n    d: \"M14.47 13.5L11 20v-5.5H9l.53-1H7V22h10v-8.5h-2.53z\"\n  }), /*#__PURE__*/_jsx(\"path\", {\n    fillOpacity: \".3\",\n    d: \"M17 4h-3V2h-4v2H7v9.5h2.53L13 7v5.5h2l-.53 1H17V4z\"\n  })]\n}), 'BatteryCharging50Sharp');"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,eAAeJ,aAAa,CAAE,aAAaI,KAAK,CAACL,KAAK,CAACM,QAAQ,EAAE;EAC/DC,QAAQ,EAAE,CAAC,aAAaJ,IAAI,CAAC,MAAM,EAAE;IACnCK,CAAC,EAAE;EACL,CAAC,CAAC,EAAE,aAAaL,IAAI,CAAC,MAAM,EAAE;IAC5BM,WAAW,EAAE,IAAI;IACjBD,CAAC,EAAE;EACL,CAAC,CAAC;AACJ,CAAC,CAAC,EAAE,wBAAwB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}