{"ast": null, "code": "/**\n * Utility per la validazione dei campi dei cavi\n * Implementa le stesse regole di validazione della CLI\n */\n\n// Costanti\nexport const TBD = \"TBD\";\nexport const STATI_INSTALLAZIONE = [\"Da installare\", \"In corso\", \"Installato\", \"SPARE\"];\n\n/**\n * Verifica se un valore è vuoto\n * @param {string|number|null} value - Valore da verificare\n * @returns {boolean} - True se il valore è vuoto, false altrimenti\n */\nexport const isEmpty = value => {\n  return value === null || value === undefined || typeof value === 'string' && value.trim() === '' || typeof value === 'number' && isNaN(value);\n};\n\n/**\n * Converte un valore in float\n * @param {string|number|null} value - Valore da convertire\n * @returns {number} - Valore convertito in float\n */\nexport const convertToFloat = value => {\n  if (isEmpty(value)) return 0;\n  if (typeof value === 'number') return value;\n  if (typeof value === 'string') {\n    const normalized = value.trim().replace(',', '.');\n    try {\n      return parseFloat(normalized);\n    } catch (e) {\n      console.warn(`Errore conversione float: '${value}' non è un numero valido.`);\n      return 0;\n    }\n  }\n  console.warn(`Tipo di valore non supportato: ${typeof value}`);\n  return 0;\n};\n\n/**\n * Valida un campo numerico\n * @param {string|number} value - Valore da validare\n * @param {string} fieldName - Nome del campo\n * @returns {Object} - Risultato della validazione\n */\nexport const validateNumber = (value, fieldName) => {\n  try {\n    // Gestione campi vuoti\n    if (isEmpty(value)) {\n      if (fieldName === \"Numero conduttori\" || fieldName === \"Sezione\" || fieldName === \"Metri teorici\") {\n        return {\n          valid: true,\n          message: \"\",\n          value: \"0\"\n        };\n      }\n      return {\n        valid: false,\n        message: `${fieldName} non può essere vuoto`,\n        value: null\n      };\n    }\n\n    // Normalizzazione input se è stringa\n    let normalizedValue = value;\n    if (typeof value === 'string') {\n      normalizedValue = value.trim().replace(',', '.');\n      if (normalizedValue === '.') {\n        return {\n          valid: false,\n          message: `${fieldName} non valido`,\n          value: null\n        };\n      }\n    }\n\n    // Conversione\n    const numero = parseFloat(normalizedValue);\n\n    // Validazione numero negativo\n    if (numero < 0) {\n      return {\n        valid: false,\n        message: `${fieldName} non può essere negativo`,\n        value: null\n      };\n    }\n\n    // Validazione limiti specifici\n    if (fieldName === \"Numero conduttori\" && numero > 24) {\n      return {\n        valid: true,\n        message: `ATTENZIONE: Il numero di conduttori (${numero}) supera il limite standard di 24`,\n        value: numero.toString(),\n        warning: true\n      };\n    }\n    if (fieldName === \"Sezione\" && numero > 1000) {\n      return {\n        valid: true,\n        message: `ATTENZIONE: La sezione (${numero}) supera il limite standard di 1000`,\n        value: numero.toString(),\n        warning: true\n      };\n    }\n    return {\n      valid: true,\n      message: \"\",\n      value: numero.toString()\n    };\n  } catch (e) {\n    return {\n      valid: false,\n      message: `Il valore inserito per ${fieldName} non è un numero valido`,\n      value: null\n    };\n  }\n};\n\n/**\n * Normalizza lo stato di installazione per utilizzare i valori dell'enum StatoInstallazione\n * @param {string} stato - Stato di installazione da normalizzare\n * @returns {string} - Stato normalizzato\n */\nexport const normalizeInstallationStatus = stato => {\n  if (isEmpty(stato)) return 'Da installare'; // Valore predefinito dall'enum\n\n  // Mappa gli stati usati nel backend agli stati dell'enum\n  const statoLower = stato.toLowerCase();\n  if (statoLower.includes('posato') || statoLower.includes('installato')) {\n    return 'Installato';\n  } else if (statoLower.includes('da posare') || statoLower.includes('da installare')) {\n    return 'Da installare';\n  } else if (statoLower.includes('in corso') || statoLower.includes('in posa')) {\n    return 'In corso';\n  }\n  return stato; // Ritorna lo stato originale se non corrisponde a nessuno dei casi\n};\n\n/**\n * Valida lo stato di installazione\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateStatoInstallazione = value => {\n  if (isEmpty(value)) {\n    return {\n      valid: true,\n      message: \"\",\n      value: TBD\n    };\n  }\n\n  // Normalizza lo stato prima di validarlo\n  const normalizedValue = normalizeInstallationStatus(value);\n  if (STATI_INSTALLAZIONE.includes(normalizedValue)) {\n    return {\n      valid: true,\n      message: \"\",\n      value: normalizedValue\n    };\n  }\n  return {\n    valid: false,\n    message: `Stato di installazione non valido. Stati ammessi: ${STATI_INSTALLAZIONE.join(', ')}`,\n    value: null\n  };\n};\n\n/**\n * Valida il campo SH (schermato)\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateSH = value => {\n  // Se il valore è vuoto, restituisci 'N' come valore di default\n  if (isEmpty(value)) {\n    return {\n      valid: true,\n      message: \"\",\n      value: \"N\"\n    };\n  }\n  const normalizedValue = value.trim().toUpperCase();\n\n  // Valori positivi\n  if (['S', 'SI', 'Y', 'YES'].includes(normalizedValue)) {\n    return {\n      valid: true,\n      message: \"\",\n      value: \"S\"\n    };\n  }\n\n  // Valori negativi\n  if (['N', 'NO'].includes(normalizedValue)) {\n    return {\n      valid: true,\n      message: \"\",\n      value: \"N\"\n    };\n  }\n  return {\n    valid: false,\n    message: \"Valore non valido per SH. Inserire 'S' o 'N'.\",\n    value: null\n  };\n};\n\n/**\n * Valida un campo di testo base\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBaseField = value => {\n  if (isEmpty(value)) {\n    return {\n      valid: true,\n      message: \"Campo vuoto\",\n      value: TBD\n    };\n  }\n  return {\n    valid: true,\n    message: \"Campo valido\",\n    value: value.trim()\n  };\n};\n\n/**\n * Valida un campo di testo obbligatorio\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateRequiredTextField = value => {\n  if (isEmpty(value)) {\n    return {\n      valid: false,\n      message: \"Il campo non può essere vuoto\",\n      value: null\n    };\n  }\n  return {\n    valid: true,\n    message: \"Campo valido\",\n    value: value.trim()\n  };\n};\n\n/**\n * Valida i metri teorici\n * @param {string|number} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateMetriTeorici = value => {\n  try {\n    if (isEmpty(value)) {\n      return {\n        valid: false,\n        message: \"I metri teorici sono obbligatori\",\n        value: null\n      };\n    }\n    const val = convertToFloat(value);\n    if (val <= 0) {\n      return {\n        valid: false,\n        message: \"I metri teorici devono essere maggiori di zero\",\n        value: null\n      };\n    }\n    if (val > 100000) {\n      // 100km come limite ragionevole\n      return {\n        valid: false,\n        message: \"I metri teorici non possono superare 100.000\",\n        value: null\n      };\n    }\n    return {\n      valid: true,\n      message: \"Valore valido\",\n      value: val\n    };\n  } catch (e) {\n    return {\n      valid: false,\n      message: \"Il valore deve essere un numero valido\",\n      value: null\n    };\n  }\n};\n\n/**\n * Valida la metratura reale\n * @param {string|number} value - Valore da validare\n * @param {number} metriTeorici - Metri teorici di riferimento\n * @returns {Object} - Risultato della validazione\n */\nexport const validateMetraturaReale = (value, metriTeorici) => {\n  try {\n    if (isEmpty(value)) {\n      return {\n        valid: true,\n        message: \"\",\n        value: 0\n      };\n    }\n    const val = convertToFloat(value);\n    if (val < 0) {\n      return {\n        valid: false,\n        message: \"La metratura reale non può essere negativa\",\n        value: null\n      };\n    }\n    if (val > metriTeorici * 1.1) {\n      // 10% di tolleranza\n      return {\n        valid: true,\n        message: `ATTENZIONE: La metratura reale (${val}m) supera del ${(val / metriTeorici - 1) * 100}% i metri teorici (${metriTeorici}m)`,\n        value: val,\n        warning: true\n      };\n    }\n    return {\n      valid: true,\n      message: \"\",\n      value: val\n    };\n  } catch (e) {\n    return {\n      valid: false,\n      message: \"Inserire un numero valido per la metratura reale\",\n      value: null\n    };\n  }\n};\n\n/**\n * Valida un campo in base al suo tipo\n * @param {string} fieldName - Nome del campo\n * @param {string|number} value - Valore da validare\n * @param {Object} additionalParams - Parametri aggiuntivi per la validazione\n * @returns {Object} - Risultato della validazione\n */\nexport const validateField = (fieldName, value, additionalParams = {}) => {\n  // Campi che richiedono validazione speciale\n  const specialValidations = {\n    'metri_teorici': () => validateMetriTeorici(value),\n    'metratura_reale': () => validateMetraturaReale(value, additionalParams.metriTeorici || 0),\n    'stato_installazione': () => validateStatoInstallazione(value),\n    'n_conduttori': () => validateNumber(value, \"Numero conduttori\"),\n    'sezione': () => validateNumber(value, \"Sezione\"),\n    'sh': () => validateSH(value)\n  };\n\n  // Campi che devono avere \"TBD\" come valore predefinito quando vuoti\n  const tbdFields = ['sistema', 'utility', 'colore_cavo', 'tipologia', 'ubicazione_partenza', 'utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_partenza', 'descrizione_utenza_arrivo'];\n\n  // Se il campo richiede validazione speciale, usala\n  if (fieldName in specialValidations) {\n    return specialValidations[fieldName]();\n  }\n\n  // Se il campo deve avere TBD come valore predefinito\n  if (tbdFields.includes(fieldName)) {\n    if (isEmpty(value)) {\n      return {\n        valid: true,\n        message: \"\",\n        value: TBD\n      };\n    }\n    return {\n      valid: true,\n      message: \"\",\n      value: value.trim()\n    };\n  }\n\n  // Per tutti gli altri campi, usa la validazione base\n  return validateBaseField(value);\n};\n\n/**\n * Valida tutti i campi di un cavo\n * @param {Object} cavoData - Dati del cavo\n * @returns {Object} - Risultato della validazione\n */\nexport const validateCavoData = cavoData => {\n  const errors = {};\n  const warnings = {};\n  const validatedData = {\n    ...cavoData\n  };\n\n  // Campi da validare\n  const fieldsToValidate = ['id_cavo', 'revisione_ufficiale', 'sistema', 'utility', 'colore_cavo', 'tipologia', 'n_conduttori', 'sezione', 'sh', 'ubicazione_partenza', 'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici'];\n\n  // Validazione speciale per id_cavo\n  if (isEmpty(cavoData.id_cavo)) {\n    errors.id_cavo = \"L'ID cavo è obbligatorio\";\n  }\n\n  // Validazione degli altri campi\n  for (const field of fieldsToValidate) {\n    if (field === 'id_cavo') continue; // Già validato\n\n    const additionalParams = {};\n    if (field === 'metratura_reale') {\n      additionalParams.metriTeorici = convertToFloat(cavoData.metri_teorici);\n    }\n    const result = validateField(field, cavoData[field], additionalParams);\n    if (!result.valid) {\n      errors[field] = result.message;\n    } else {\n      validatedData[field] = result.value;\n      if (result.warning) {\n        warnings[field] = result.message;\n      }\n    }\n  }\n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors,\n    warnings,\n    validatedData\n  };\n};", "map": {"version": 3, "names": ["TBD", "STATI_INSTALLAZIONE", "isEmpty", "value", "undefined", "trim", "isNaN", "convertToFloat", "normalized", "replace", "parseFloat", "e", "console", "warn", "validateNumber", "fieldName", "valid", "message", "normalizedValue", "numero", "toString", "warning", "normalizeInstallationStatus", "stato", "statoLower", "toLowerCase", "includes", "validateStatoInstallazione", "join", "validateSH", "toUpperCase", "validateBaseField", "validateRequiredTextField", "validateMetriTeorici", "val", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "metriTeorici", "validateField", "additionalParams", "specialValidations", "metri_te<PERSON>ci", "metratura_reale", "stato_installazione", "n_conduttori", "sezione", "sh", "tbdFields", "validateCavoData", "cavoData", "errors", "warnings", "validatedData", "fieldsToValidate", "id_cavo", "field", "result", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/utils/validationUtils.js"], "sourcesContent": ["/**\n * Utility per la validazione dei campi dei cavi\n * Implementa le stesse regole di validazione della CLI\n */\n\n// Costanti\nexport const TBD = \"TBD\";\nexport const STATI_INSTALLAZIONE = [\"Da installare\", \"In corso\", \"Installato\", \"SPARE\"];\n\n/**\n * Verifica se un valore è vuoto\n * @param {string|number|null} value - Valore da verificare\n * @returns {boolean} - True se il valore è vuoto, false altrimenti\n */\nexport const isEmpty = (value) => {\n  return value === null || value === undefined ||\n         (typeof value === 'string' && value.trim() === '') ||\n         (typeof value === 'number' && isNaN(value));\n};\n\n/**\n * Converte un valore in float\n * @param {string|number|null} value - Valore da convertire\n * @returns {number} - Valore convertito in float\n */\nexport const convertToFloat = (value) => {\n  if (isEmpty(value)) return 0;\n\n  if (typeof value === 'number') return value;\n\n  if (typeof value === 'string') {\n    const normalized = value.trim().replace(',', '.');\n    try {\n      return parseFloat(normalized);\n    } catch (e) {\n      console.warn(`Errore conversione float: '${value}' non è un numero valido.`);\n      return 0;\n    }\n  }\n\n  console.warn(`Tipo di valore non supportato: ${typeof value}`);\n  return 0;\n};\n\n/**\n * Valida un campo numerico\n * @param {string|number} value - Valore da validare\n * @param {string} fieldName - Nome del campo\n * @returns {Object} - Risultato della validazione\n */\nexport const validateNumber = (value, fieldName) => {\n  try {\n    // Gestione campi vuoti\n    if (isEmpty(value)) {\n      if (fieldName === \"Numero conduttori\" || fieldName === \"Sezione\" || fieldName === \"Metri teorici\") {\n        return { valid: true, message: \"\", value: \"0\" };\n      }\n      return { valid: false, message: `${fieldName} non può essere vuoto`, value: null };\n    }\n\n    // Normalizzazione input se è stringa\n    let normalizedValue = value;\n    if (typeof value === 'string') {\n      normalizedValue = value.trim().replace(',', '.');\n      if (normalizedValue === '.') {\n        return { valid: false, message: `${fieldName} non valido`, value: null };\n      }\n    }\n\n    // Conversione\n    const numero = parseFloat(normalizedValue);\n\n    // Validazione numero negativo\n    if (numero < 0) {\n      return { valid: false, message: `${fieldName} non può essere negativo`, value: null };\n    }\n\n    // Validazione limiti specifici\n    if (fieldName === \"Numero conduttori\" && numero > 24) {\n      return {\n        valid: true,\n        message: `ATTENZIONE: Il numero di conduttori (${numero}) supera il limite standard di 24`,\n        value: numero.toString(),\n        warning: true\n      };\n    }\n\n    if (fieldName === \"Sezione\" && numero > 1000) {\n      return {\n        valid: true,\n        message: `ATTENZIONE: La sezione (${numero}) supera il limite standard di 1000`,\n        value: numero.toString(),\n        warning: true\n      };\n    }\n\n    return { valid: true, message: \"\", value: numero.toString() };\n  } catch (e) {\n    return { valid: false, message: `Il valore inserito per ${fieldName} non è un numero valido`, value: null };\n  }\n};\n\n/**\n * Normalizza lo stato di installazione per utilizzare i valori dell'enum StatoInstallazione\n * @param {string} stato - Stato di installazione da normalizzare\n * @returns {string} - Stato normalizzato\n */\nexport const normalizeInstallationStatus = (stato) => {\n  if (isEmpty(stato)) return 'Da installare'; // Valore predefinito dall'enum\n\n  // Mappa gli stati usati nel backend agli stati dell'enum\n  const statoLower = stato.toLowerCase();\n\n  if (statoLower.includes('posato') || statoLower.includes('installato')) {\n    return 'Installato';\n  } else if (statoLower.includes('da posare') || statoLower.includes('da installare')) {\n    return 'Da installare';\n  } else if (statoLower.includes('in corso') || statoLower.includes('in posa')) {\n    return 'In corso';\n  }\n\n  return stato; // Ritorna lo stato originale se non corrisponde a nessuno dei casi\n};\n\n/**\n * Valida lo stato di installazione\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateStatoInstallazione = (value) => {\n  if (isEmpty(value)) {\n    return { valid: true, message: \"\", value: TBD };\n  }\n\n  // Normalizza lo stato prima di validarlo\n  const normalizedValue = normalizeInstallationStatus(value);\n  if (STATI_INSTALLAZIONE.includes(normalizedValue)) {\n    return { valid: true, message: \"\", value: normalizedValue };\n  }\n\n  return {\n    valid: false,\n    message: `Stato di installazione non valido. Stati ammessi: ${STATI_INSTALLAZIONE.join(', ')}`,\n    value: null\n  };\n};\n\n/**\n * Valida il campo SH (schermato)\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateSH = (value) => {\n  // Se il valore è vuoto, restituisci 'N' come valore di default\n  if (isEmpty(value)) {\n    return { valid: true, message: \"\", value: \"N\" };\n  }\n\n  const normalizedValue = value.trim().toUpperCase();\n\n  // Valori positivi\n  if (['S', 'SI', 'Y', 'YES'].includes(normalizedValue)) {\n    return { valid: true, message: \"\", value: \"S\" };\n  }\n\n  // Valori negativi\n  if (['N', 'NO'].includes(normalizedValue)) {\n    return { valid: true, message: \"\", value: \"N\" };\n  }\n\n  return { valid: false, message: \"Valore non valido per SH. Inserire 'S' o 'N'.\", value: null };\n};\n\n/**\n * Valida un campo di testo base\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBaseField = (value) => {\n  if (isEmpty(value)) {\n    return { valid: true, message: \"Campo vuoto\", value: TBD };\n  }\n  return { valid: true, message: \"Campo valido\", value: value.trim() };\n};\n\n/**\n * Valida un campo di testo obbligatorio\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateRequiredTextField = (value) => {\n  if (isEmpty(value)) {\n    return { valid: false, message: \"Il campo non può essere vuoto\", value: null };\n  }\n  return { valid: true, message: \"Campo valido\", value: value.trim() };\n};\n\n/**\n * Valida i metri teorici\n * @param {string|number} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateMetriTeorici = (value) => {\n  try {\n    if (isEmpty(value)) {\n      return { valid: false, message: \"I metri teorici sono obbligatori\", value: null };\n    }\n\n    const val = convertToFloat(value);\n\n    if (val <= 0) {\n      return { valid: false, message: \"I metri teorici devono essere maggiori di zero\", value: null };\n    }\n\n    if (val > 100000) {  // 100km come limite ragionevole\n      return { valid: false, message: \"I metri teorici non possono superare 100.000\", value: null };\n    }\n\n    return { valid: true, message: \"Valore valido\", value: val };\n  } catch (e) {\n    return { valid: false, message: \"Il valore deve essere un numero valido\", value: null };\n  }\n};\n\n/**\n * Valida la metratura reale\n * @param {string|number} value - Valore da validare\n * @param {number} metriTeorici - Metri teorici di riferimento\n * @returns {Object} - Risultato della validazione\n */\nexport const validateMetraturaReale = (value, metriTeorici) => {\n  try {\n    if (isEmpty(value)) {\n      return { valid: true, message: \"\", value: 0 };\n    }\n\n    const val = convertToFloat(value);\n\n    if (val < 0) {\n      return { valid: false, message: \"La metratura reale non può essere negativa\", value: null };\n    }\n\n    if (val > metriTeorici * 1.1) {  // 10% di tolleranza\n      return {\n        valid: true,\n        message: `ATTENZIONE: La metratura reale (${val}m) supera del ${((val / metriTeorici) - 1) * 100}% i metri teorici (${metriTeorici}m)`,\n        value: val,\n        warning: true\n      };\n    }\n\n    return { valid: true, message: \"\", value: val };\n  } catch (e) {\n    return { valid: false, message: \"Inserire un numero valido per la metratura reale\", value: null };\n  }\n};\n\n/**\n * Valida un campo in base al suo tipo\n * @param {string} fieldName - Nome del campo\n * @param {string|number} value - Valore da validare\n * @param {Object} additionalParams - Parametri aggiuntivi per la validazione\n * @returns {Object} - Risultato della validazione\n */\nexport const validateField = (fieldName, value, additionalParams = {}) => {\n  // Campi che richiedono validazione speciale\n  const specialValidations = {\n    'metri_teorici': () => validateMetriTeorici(value),\n    'metratura_reale': () => validateMetraturaReale(value, additionalParams.metriTeorici || 0),\n    'stato_installazione': () => validateStatoInstallazione(value),\n    'n_conduttori': () => validateNumber(value, \"Numero conduttori\"),\n    'sezione': () => validateNumber(value, \"Sezione\"),\n    'sh': () => validateSH(value),\n  };\n\n  // Campi che devono avere \"TBD\" come valore predefinito quando vuoti\n  const tbdFields = [\n    'sistema', 'utility', 'colore_cavo', 'tipologia',\n    'ubicazione_partenza', 'utenza_partenza', 'ubicazione_arrivo', 'utenza_arrivo',\n    'descrizione_utenza_partenza', 'descrizione_utenza_arrivo'\n  ];\n\n  // Se il campo richiede validazione speciale, usala\n  if (fieldName in specialValidations) {\n    return specialValidations[fieldName]();\n  }\n\n  // Se il campo deve avere TBD come valore predefinito\n  if (tbdFields.includes(fieldName)) {\n    if (isEmpty(value)) {\n      return { valid: true, message: \"\", value: TBD };\n    }\n    return { valid: true, message: \"\", value: value.trim() };\n  }\n\n  // Per tutti gli altri campi, usa la validazione base\n  return validateBaseField(value);\n};\n\n/**\n * Valida tutti i campi di un cavo\n * @param {Object} cavoData - Dati del cavo\n * @returns {Object} - Risultato della validazione\n */\nexport const validateCavoData = (cavoData) => {\n  const errors = {};\n  const warnings = {};\n  const validatedData = { ...cavoData };\n\n  // Campi da validare\n  const fieldsToValidate = [\n    'id_cavo', 'revisione_ufficiale', 'sistema', 'utility', 'colore_cavo',\n    'tipologia', 'n_conduttori', 'sezione', 'sh', 'ubicazione_partenza',\n    'utenza_partenza', 'descrizione_utenza_partenza', 'ubicazione_arrivo',\n    'utenza_arrivo', 'descrizione_utenza_arrivo', 'metri_teorici'\n  ];\n\n  // Validazione speciale per id_cavo\n  if (isEmpty(cavoData.id_cavo)) {\n    errors.id_cavo = \"L'ID cavo è obbligatorio\";\n  }\n\n  // Validazione degli altri campi\n  for (const field of fieldsToValidate) {\n    if (field === 'id_cavo') continue; // Già validato\n\n    const additionalParams = {};\n    if (field === 'metratura_reale') {\n      additionalParams.metriTeorici = convertToFloat(cavoData.metri_teorici);\n    }\n\n    const result = validateField(field, cavoData[field], additionalParams);\n\n    if (!result.valid) {\n      errors[field] = result.message;\n    } else {\n      validatedData[field] = result.value;\n      if (result.warning) {\n        warnings[field] = result.message;\n      }\n    }\n  }\n\n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors,\n    warnings,\n    validatedData\n  };\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,GAAG,GAAG,KAAK;AACxB,OAAO,MAAMC,mBAAmB,GAAG,CAAC,eAAe,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,CAAC;;AAEvF;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,OAAO,GAAIC,KAAK,IAAK;EAChC,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IACpC,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,IAAI,CAAC,CAAC,KAAK,EAAG,IACjD,OAAOF,KAAK,KAAK,QAAQ,IAAIG,KAAK,CAACH,KAAK,CAAE;AACpD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,cAAc,GAAIJ,KAAK,IAAK;EACvC,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE,OAAO,CAAC;EAE5B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;EAE3C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAMK,UAAU,GAAGL,KAAK,CAACE,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IACjD,IAAI;MACF,OAAOC,UAAU,CAACF,UAAU,CAAC;IAC/B,CAAC,CAAC,OAAOG,CAAC,EAAE;MACVC,OAAO,CAACC,IAAI,CAAC,8BAA8BV,KAAK,2BAA2B,CAAC;MAC5E,OAAO,CAAC;IACV;EACF;EAEAS,OAAO,CAACC,IAAI,CAAC,kCAAkC,OAAOV,KAAK,EAAE,CAAC;EAC9D,OAAO,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,cAAc,GAAGA,CAACX,KAAK,EAAEY,SAAS,KAAK;EAClD,IAAI;IACF;IACA,IAAIb,OAAO,CAACC,KAAK,CAAC,EAAE;MAClB,IAAIY,SAAS,KAAK,mBAAmB,IAAIA,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,eAAe,EAAE;QACjG,OAAO;UAAEC,KAAK,EAAE,IAAI;UAAEC,OAAO,EAAE,EAAE;UAAEd,KAAK,EAAE;QAAI,CAAC;MACjD;MACA,OAAO;QAAEa,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,GAAGF,SAAS,uBAAuB;QAAEZ,KAAK,EAAE;MAAK,CAAC;IACpF;;IAEA;IACA,IAAIe,eAAe,GAAGf,KAAK;IAC3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7Be,eAAe,GAAGf,KAAK,CAACE,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;MAChD,IAAIS,eAAe,KAAK,GAAG,EAAE;QAC3B,OAAO;UAAEF,KAAK,EAAE,KAAK;UAAEC,OAAO,EAAE,GAAGF,SAAS,aAAa;UAAEZ,KAAK,EAAE;QAAK,CAAC;MAC1E;IACF;;IAEA;IACA,MAAMgB,MAAM,GAAGT,UAAU,CAACQ,eAAe,CAAC;;IAE1C;IACA,IAAIC,MAAM,GAAG,CAAC,EAAE;MACd,OAAO;QAAEH,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,GAAGF,SAAS,0BAA0B;QAAEZ,KAAK,EAAE;MAAK,CAAC;IACvF;;IAEA;IACA,IAAIY,SAAS,KAAK,mBAAmB,IAAII,MAAM,GAAG,EAAE,EAAE;MACpD,OAAO;QACLH,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,wCAAwCE,MAAM,mCAAmC;QAC1FhB,KAAK,EAAEgB,MAAM,CAACC,QAAQ,CAAC,CAAC;QACxBC,OAAO,EAAE;MACX,CAAC;IACH;IAEA,IAAIN,SAAS,KAAK,SAAS,IAAII,MAAM,GAAG,IAAI,EAAE;MAC5C,OAAO;QACLH,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,2BAA2BE,MAAM,qCAAqC;QAC/EhB,KAAK,EAAEgB,MAAM,CAACC,QAAQ,CAAC,CAAC;QACxBC,OAAO,EAAE;MACX,CAAC;IACH;IAEA,OAAO;MAAEL,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEd,KAAK,EAAEgB,MAAM,CAACC,QAAQ,CAAC;IAAE,CAAC;EAC/D,CAAC,CAAC,OAAOT,CAAC,EAAE;IACV,OAAO;MAAEK,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,0BAA0BF,SAAS,yBAAyB;MAAEZ,KAAK,EAAE;IAAK,CAAC;EAC7G;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMmB,2BAA2B,GAAIC,KAAK,IAAK;EACpD,IAAIrB,OAAO,CAACqB,KAAK,CAAC,EAAE,OAAO,eAAe,CAAC,CAAC;;EAE5C;EACA,MAAMC,UAAU,GAAGD,KAAK,CAACE,WAAW,CAAC,CAAC;EAEtC,IAAID,UAAU,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE;IACtE,OAAO,YAAY;EACrB,CAAC,MAAM,IAAIF,UAAU,CAACE,QAAQ,CAAC,WAAW,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,eAAe,CAAC,EAAE;IACnF,OAAO,eAAe;EACxB,CAAC,MAAM,IAAIF,UAAU,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,UAAU,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;IAC5E,OAAO,UAAU;EACnB;EAEA,OAAOH,KAAK,CAAC,CAAC;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,0BAA0B,GAAIxB,KAAK,IAAK;EACnD,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE;IAClB,OAAO;MAAEa,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEd,KAAK,EAAEH;IAAI,CAAC;EACjD;;EAEA;EACA,MAAMkB,eAAe,GAAGI,2BAA2B,CAACnB,KAAK,CAAC;EAC1D,IAAIF,mBAAmB,CAACyB,QAAQ,CAACR,eAAe,CAAC,EAAE;IACjD,OAAO;MAAEF,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEd,KAAK,EAAEe;IAAgB,CAAC;EAC7D;EAEA,OAAO;IACLF,KAAK,EAAE,KAAK;IACZC,OAAO,EAAE,qDAAqDhB,mBAAmB,CAAC2B,IAAI,CAAC,IAAI,CAAC,EAAE;IAC9FzB,KAAK,EAAE;EACT,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM0B,UAAU,GAAI1B,KAAK,IAAK;EACnC;EACA,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE;IAClB,OAAO;MAAEa,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEd,KAAK,EAAE;IAAI,CAAC;EACjD;EAEA,MAAMe,eAAe,GAAGf,KAAK,CAACE,IAAI,CAAC,CAAC,CAACyB,WAAW,CAAC,CAAC;;EAElD;EACA,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAACJ,QAAQ,CAACR,eAAe,CAAC,EAAE;IACrD,OAAO;MAAEF,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEd,KAAK,EAAE;IAAI,CAAC;EACjD;;EAEA;EACA,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAACuB,QAAQ,CAACR,eAAe,CAAC,EAAE;IACzC,OAAO;MAAEF,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEd,KAAK,EAAE;IAAI,CAAC;EACjD;EAEA,OAAO;IAAEa,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE,+CAA+C;IAAEd,KAAK,EAAE;EAAK,CAAC;AAChG,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM4B,iBAAiB,GAAI5B,KAAK,IAAK;EAC1C,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE;IAClB,OAAO;MAAEa,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,aAAa;MAAEd,KAAK,EAAEH;IAAI,CAAC;EAC5D;EACA,OAAO;IAAEgB,KAAK,EAAE,IAAI;IAAEC,OAAO,EAAE,cAAc;IAAEd,KAAK,EAAEA,KAAK,CAACE,IAAI,CAAC;EAAE,CAAC;AACtE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM2B,yBAAyB,GAAI7B,KAAK,IAAK;EAClD,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE;IAClB,OAAO;MAAEa,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,+BAA+B;MAAEd,KAAK,EAAE;IAAK,CAAC;EAChF;EACA,OAAO;IAAEa,KAAK,EAAE,IAAI;IAAEC,OAAO,EAAE,cAAc;IAAEd,KAAK,EAAEA,KAAK,CAACE,IAAI,CAAC;EAAE,CAAC;AACtE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM4B,oBAAoB,GAAI9B,KAAK,IAAK;EAC7C,IAAI;IACF,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE;MAClB,OAAO;QAAEa,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,kCAAkC;QAAEd,KAAK,EAAE;MAAK,CAAC;IACnF;IAEA,MAAM+B,GAAG,GAAG3B,cAAc,CAACJ,KAAK,CAAC;IAEjC,IAAI+B,GAAG,IAAI,CAAC,EAAE;MACZ,OAAO;QAAElB,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,gDAAgD;QAAEd,KAAK,EAAE;MAAK,CAAC;IACjG;IAEA,IAAI+B,GAAG,GAAG,MAAM,EAAE;MAAG;MACnB,OAAO;QAAElB,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,8CAA8C;QAAEd,KAAK,EAAE;MAAK,CAAC;IAC/F;IAEA,OAAO;MAAEa,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,eAAe;MAAEd,KAAK,EAAE+B;IAAI,CAAC;EAC9D,CAAC,CAAC,OAAOvB,CAAC,EAAE;IACV,OAAO;MAAEK,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,wCAAwC;MAAEd,KAAK,EAAE;IAAK,CAAC;EACzF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgC,sBAAsB,GAAGA,CAAChC,KAAK,EAAEiC,YAAY,KAAK;EAC7D,IAAI;IACF,IAAIlC,OAAO,CAACC,KAAK,CAAC,EAAE;MAClB,OAAO;QAAEa,KAAK,EAAE,IAAI;QAAEC,OAAO,EAAE,EAAE;QAAEd,KAAK,EAAE;MAAE,CAAC;IAC/C;IAEA,MAAM+B,GAAG,GAAG3B,cAAc,CAACJ,KAAK,CAAC;IAEjC,IAAI+B,GAAG,GAAG,CAAC,EAAE;MACX,OAAO;QAAElB,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,4CAA4C;QAAEd,KAAK,EAAE;MAAK,CAAC;IAC7F;IAEA,IAAI+B,GAAG,GAAGE,YAAY,GAAG,GAAG,EAAE;MAAG;MAC/B,OAAO;QACLpB,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE,mCAAmCiB,GAAG,iBAAiB,CAAEA,GAAG,GAAGE,YAAY,GAAI,CAAC,IAAI,GAAG,sBAAsBA,YAAY,IAAI;QACtIjC,KAAK,EAAE+B,GAAG;QACVb,OAAO,EAAE;MACX,CAAC;IACH;IAEA,OAAO;MAAEL,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEd,KAAK,EAAE+B;IAAI,CAAC;EACjD,CAAC,CAAC,OAAOvB,CAAC,EAAE;IACV,OAAO;MAAEK,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,kDAAkD;MAAEd,KAAK,EAAE;IAAK,CAAC;EACnG;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMkC,aAAa,GAAGA,CAACtB,SAAS,EAAEZ,KAAK,EAAEmC,gBAAgB,GAAG,CAAC,CAAC,KAAK;EACxE;EACA,MAAMC,kBAAkB,GAAG;IACzB,eAAe,EAAEC,CAAA,KAAMP,oBAAoB,CAAC9B,KAAK,CAAC;IAClD,iBAAiB,EAAEsC,CAAA,KAAMN,sBAAsB,CAAChC,KAAK,EAAEmC,gBAAgB,CAACF,YAAY,IAAI,CAAC,CAAC;IAC1F,qBAAqB,EAAEM,CAAA,KAAMf,0BAA0B,CAACxB,KAAK,CAAC;IAC9D,cAAc,EAAEwC,CAAA,KAAM7B,cAAc,CAACX,KAAK,EAAE,mBAAmB,CAAC;IAChE,SAAS,EAAEyC,CAAA,KAAM9B,cAAc,CAACX,KAAK,EAAE,SAAS,CAAC;IACjD,IAAI,EAAE0C,CAAA,KAAMhB,UAAU,CAAC1B,KAAK;EAC9B,CAAC;;EAED;EACA,MAAM2C,SAAS,GAAG,CAChB,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAChD,qBAAqB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,eAAe,EAC9E,6BAA6B,EAAE,2BAA2B,CAC3D;;EAED;EACA,IAAI/B,SAAS,IAAIwB,kBAAkB,EAAE;IACnC,OAAOA,kBAAkB,CAACxB,SAAS,CAAC,CAAC,CAAC;EACxC;;EAEA;EACA,IAAI+B,SAAS,CAACpB,QAAQ,CAACX,SAAS,CAAC,EAAE;IACjC,IAAIb,OAAO,CAACC,KAAK,CAAC,EAAE;MAClB,OAAO;QAAEa,KAAK,EAAE,IAAI;QAAEC,OAAO,EAAE,EAAE;QAAEd,KAAK,EAAEH;MAAI,CAAC;IACjD;IACA,OAAO;MAAEgB,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEd,KAAK,EAAEA,KAAK,CAACE,IAAI,CAAC;IAAE,CAAC;EAC1D;;EAEA;EACA,OAAO0B,iBAAiB,CAAC5B,KAAK,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM4C,gBAAgB,GAAIC,QAAQ,IAAK;EAC5C,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;EACnB,MAAMC,aAAa,GAAG;IAAE,GAAGH;EAAS,CAAC;;EAErC;EACA,MAAMI,gBAAgB,GAAG,CACvB,SAAS,EAAE,qBAAqB,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EACrE,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,IAAI,EAAE,qBAAqB,EACnE,iBAAiB,EAAE,6BAA6B,EAAE,mBAAmB,EACrE,eAAe,EAAE,2BAA2B,EAAE,eAAe,CAC9D;;EAED;EACA,IAAIlD,OAAO,CAAC8C,QAAQ,CAACK,OAAO,CAAC,EAAE;IAC7BJ,MAAM,CAACI,OAAO,GAAG,0BAA0B;EAC7C;;EAEA;EACA,KAAK,MAAMC,KAAK,IAAIF,gBAAgB,EAAE;IACpC,IAAIE,KAAK,KAAK,SAAS,EAAE,SAAS,CAAC;;IAEnC,MAAMhB,gBAAgB,GAAG,CAAC,CAAC;IAC3B,IAAIgB,KAAK,KAAK,iBAAiB,EAAE;MAC/BhB,gBAAgB,CAACF,YAAY,GAAG7B,cAAc,CAACyC,QAAQ,CAACR,aAAa,CAAC;IACxE;IAEA,MAAMe,MAAM,GAAGlB,aAAa,CAACiB,KAAK,EAAEN,QAAQ,CAACM,KAAK,CAAC,EAAEhB,gBAAgB,CAAC;IAEtE,IAAI,CAACiB,MAAM,CAACvC,KAAK,EAAE;MACjBiC,MAAM,CAACK,KAAK,CAAC,GAAGC,MAAM,CAACtC,OAAO;IAChC,CAAC,MAAM;MACLkC,aAAa,CAACG,KAAK,CAAC,GAAGC,MAAM,CAACpD,KAAK;MACnC,IAAIoD,MAAM,CAAClC,OAAO,EAAE;QAClB6B,QAAQ,CAACI,KAAK,CAAC,GAAGC,MAAM,CAACtC,OAAO;MAClC;IACF;EACF;EAEA,OAAO;IACLuC,OAAO,EAAEC,MAAM,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,MAAM,KAAK,CAAC;IACzCV,MAAM;IACNC,QAAQ;IACRC;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}