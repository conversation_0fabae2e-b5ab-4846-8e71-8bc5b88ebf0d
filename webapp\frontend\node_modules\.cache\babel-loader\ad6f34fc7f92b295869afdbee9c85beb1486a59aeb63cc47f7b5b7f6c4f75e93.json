{"ast": null, "code": "'use client';\n\nimport { useLocalizationContext } from \"../internals/hooks/useUtils.js\";\nexport const usePickerTranslations = () => useLocalizationContext().localeText;", "map": {"version": 3, "names": ["useLocalizationContext", "usePickerTranslations", "localeText"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/hooks/usePickerTranslations.js"], "sourcesContent": ["'use client';\n\nimport { useLocalizationContext } from \"../internals/hooks/useUtils.js\";\nexport const usePickerTranslations = () => useLocalizationContext().localeText;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,sBAAsB,QAAQ,gCAAgC;AACvE,OAAO,MAAMC,qBAAqB,GAAGA,CAAA,KAAMD,sBAAsB,CAAC,CAAC,CAACE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}