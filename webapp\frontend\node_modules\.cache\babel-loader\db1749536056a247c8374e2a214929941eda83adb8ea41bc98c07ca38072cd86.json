{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nconst API_URL = config.API_URL;\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 15000,\n  // Timeout aumentato a 15 secondi\n  withCredentials: false // Modificato per risolvere problemi CORS\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null, filters = {}) => {\n    try {\n      console.log('getCavi chiamato con:', {\n        cantiereId,\n        tipoCavo,\n        filters\n      });\n      console.log('Tipo di cantiereId:', typeof cantiereId);\n\n      // Assicurati che cantiereId sia un numero\n      let cantiereIdNum = cantiereId;\n      if (typeof cantiereId === 'string') {\n        cantiereIdNum = parseInt(cantiereId, 10);\n        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);\n      }\n      if (isNaN(cantiereIdNum)) {\n        console.error('ID cantiere non è un numero valido:', cantiereId);\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log(`Caricamento cavi per cantiere ${cantiereIdNum} con tipo_cavo=${tipoCavo}`);\n\n      // Soluzione alternativa per i cavi SPARE\n      if (tipoCavo === 3) {\n        console.log('Caricamento cavi SPARE con query diretta...');\n        try {\n          // Usa una query SQL diretta per ottenere i cavi SPARE\n          const response = await axios.get(`${API_URL}/cavi/spare/${cantiereIdNum}`, {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          });\n          console.log('Risposta cavi SPARE:', response.data);\n          return response.data;\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi SPARE:', spareError);\n          // Se fallisce, continua con il metodo standard\n        }\n      }\n\n      // Costruisci l'URL con i parametri di query\n      let url = `/cavi/${cantiereIdNum}`;\n      const queryParams = [];\n      if (tipoCavo !== null) {\n        queryParams.push(`tipo_cavo=${tipoCavo}`);\n      }\n\n      // Aggiungi filtri aggiuntivi se presenti\n      if (filters.stato_installazione) {\n        queryParams.push(`stato_installazione=${encodeURIComponent(filters.stato_installazione)}`);\n      }\n      if (filters.tipologia) {\n        queryParams.push(`tipologia=${encodeURIComponent(filters.tipologia)}`);\n      }\n      if (filters.sort_by) {\n        queryParams.push(`sort_by=${encodeURIComponent(filters.sort_by)}`);\n        if (filters.sort_order) {\n          queryParams.push(`sort_order=${encodeURIComponent(filters.sort_order)}`);\n        }\n      }\n\n      // Aggiungi i parametri di query all'URL\n      if (queryParams.length > 0) {\n        url += `?${queryParams.join('&')}`;\n      }\n\n      // Log dettagliato dell'URL e dei parametri\n      console.log('URL API completo:', url);\n      console.log('Parametri di query:', queryParams);\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');\n      console.log('URL completo:', `${API_URL}${url}`);\n      try {\n        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);\n        console.log('Headers della richiesta:', {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        });\n\n        // Aggiungi un timeout più lungo per la richiesta\n        const response = await axiosInstance.get(url, {\n          timeout: 30000\n        });\n        console.log(`Risposta API: ${url}`, response.data);\n        console.log('Status della risposta:', response.status);\n        console.log('Headers della risposta:', response.headers);\n        if (Array.isArray(response.data)) {\n          console.log(`Numero di cavi ricevuti: ${response.data.length}`);\n          if (response.data.length > 0) {\n            console.log('Primo cavo ricevuto:', response.data[0]);\n          } else {\n            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);\n          }\n        } else {\n          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);\n        }\n        return response.data;\n      } catch (apiError) {\n        var _apiError$response, _apiError$response2, _apiError$response3, _apiError$response4;\n        console.error(`Errore nella chiamata API GET ${url}:`, apiError);\n        console.error('Dettagli errore API:', {\n          message: apiError.message,\n          status: (_apiError$response = apiError.response) === null || _apiError$response === void 0 ? void 0 : _apiError$response.status,\n          statusText: (_apiError$response2 = apiError.response) === null || _apiError$response2 === void 0 ? void 0 : _apiError$response2.statusText,\n          data: (_apiError$response3 = apiError.response) === null || _apiError$response3 === void 0 ? void 0 : _apiError$response3.data,\n          headers: (_apiError$response4 = apiError.response) === null || _apiError$response4 === void 0 ? void 0 : _apiError$response4.headers,\n          code: apiError.code,\n          isAxiosError: apiError.isAxiosError,\n          config: apiError.config ? {\n            url: apiError.config.url,\n            method: apiError.config.method,\n            timeout: apiError.config.timeout,\n            headers: apiError.config.headers\n          } : 'No config'\n        });\n\n        // Gestione specifica per errori di rete\n        if (apiError.code === 'ERR_NETWORK') {\n          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');\n          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile\n          try {\n            console.log('Tentativo di test di connessione al backend...');\n            const testResponse = await fetch(API_URL);\n            console.log('Test di connessione al backend:', testResponse.status);\n          } catch (testError) {\n            console.error('Test di connessione al backend fallito:', testError);\n          }\n        }\n        throw apiError;\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$response4, _error$response4$data, _error$response5, _error$response6;\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,\n        stack: error.stack\n      });\n\n      // Crea un errore più informativo\n      const enhancedError = new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || error.message || 'Errore sconosciuto');\n      enhancedError.status = (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status;\n      enhancedError.data = (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data;\n      enhancedError.response = error.response;\n      enhancedError.originalError = error;\n      enhancedError.code = error.code;\n      enhancedError.isAxiosError = error.isAxiosError;\n      throw enhancedError;\n    }\n  },\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene la revisione corrente del cantiere\n  getRevisioneCorrente: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);\n      return response.data.revisione_corrente;\n    } catch (error) {\n      console.error('Get revisione corrente error:', error);\n      return '00'; // Valore di default in caso di errore\n    }\n  },\n  // Marca un cavo come SPARE\n  markCavoAsSpare: async (cantiereId, cavoId, force = false) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di marcare cavo come SPARE:', {\n        cantiereId: cantiereIdNum,\n        cavoId,\n        force\n      });\n\n      // Usa direttamente l'endpoint delete con mode=spare che sappiamo funzionare\n      console.log('URL API:', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}?mode=spare`);\n      const response = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        timeout: 30000,\n        params: {\n          mode: 'spare'\n        }\n      });\n      console.log('Risposta markCavoAsSpare (delete con mode=spare):', response.data);\n\n      // Verifica che il cavo sia stato effettivamente marcato come SPARE\n      try {\n        // Attendi un po' per dare tempo al database di aggiornarsi\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE...');\n        const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Stato del cavo dopo marcatura:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n        }\n      } catch (verifyError) {\n        console.error('Errore durante la verifica dello stato del cavo:', verifyError);\n      }\n      return {\n        message: response.data.message || 'Cavo marcato come SPARE con successo',\n        modificato_manualmente: 3,\n        stato_installazione: 'SPARE'\n      };\n    } catch (error) {\n      var _error$response7, _error$response8, _error$response9;\n      console.error('Mark cavo as SPARE error:', error);\n      console.error('Dettagli errore:', {\n        message: error.message,\n        status: (_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : _error$response7.status,\n        statusText: (_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : _error$response8.statusText,\n        data: (_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : _error$response9.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un cavo o lo marca come SPARE\n  deleteCavo: async (cantiereId, cavoId, mode = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di eliminare/marcare cavo:', {\n        cantiereId: cantiereIdNum,\n        cavoId,\n        mode\n      });\n\n      // Se è specificata la modalità, aggiungi il parametro alla richiesta\n      const requestConfig = {\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        timeout: 30000,\n        // Timeout aumentato a 30 secondi\n        params: mode ? {\n          mode\n        } : {}\n      };\n      console.log('URL API:', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Config:', requestConfig);\n\n      // Usa axios direttamente invece di axiosInstance per avere più controllo\n      const response = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, requestConfig);\n      console.log('Risposta deleteCavo:', response.data);\n      return response.data;\n    } catch (error) {\n      var _error$response0, _error$response1, _error$response10;\n      console.error('Delete cavo error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: (_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : _error$response0.status,\n        statusText: (_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : _error$response1.statusText,\n        data: (_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : _error$response10.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n\n      // Crea un errore più informativo\n      if (error.response && error.response.data) {\n        throw error.response.data;\n      } else if (error.message) {\n        throw new Error(error.message);\n      } else {\n        throw new Error('Errore durante l\\'eliminazione del cavo');\n      }\n    }\n  },\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene la lista dei cavi installati di un cantiere\n  getCaviInstallati: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene le statistiche dei cavi di un cantiere\n  getCaviStats: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/stats`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi stats error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene direttamente i cavi SPARE con una query SQL\n  getCaviSpare: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log('Caricamento cavi SPARE con query SQL diretta...');\n      console.log('URL API:', `${API_URL}/cavi/spare/${cantiereIdNum}`);\n\n      // Usa axios direttamente invece di axiosInstance per avere più controllo\n      const response = await axios.get(`${API_URL}/cavi/spare/${cantiereIdNum}`, {\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        timeout: 30000 // Timeout aumentato a 30 secondi\n      });\n      console.log('Risposta getCaviSpare:', response.data ? response.data.length : 0, 'cavi SPARE trovati');\n      if (response.data && response.data.length > 0) {\n        console.log('Primo cavo SPARE:', response.data[0]);\n      }\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi SPARE error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Collega un lato di un cavo\n  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {\n        lato: lato,\n        responsabile: responsabile || 'cantiere'\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Collega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Scollega un lato di un cavo\n  scollegaCavo: async (cantiereId, cavoId, lato) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Scollega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Verifica lo stato di un cavo specifico (debug)\n  debugCavo: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Ottieni i cavi attivi\n      console.log('Verificando cavo tra i cavi attivi...');\n      const attivi = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=0`);\n      const cavoAttivo = attivi.data.find(c => c.id_cavo === cavoId);\n\n      // Ottieni i cavi SPARE\n      console.log('Verificando cavo tra i cavi SPARE...');\n      const spare = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=3`);\n      const cavoSpare = spare.data.find(c => c.id_cavo === cavoId);\n      return {\n        trovato_tra_attivi: !!cavoAttivo,\n        trovato_tra_spare: !!cavoSpare,\n        cavo_attivo: cavoAttivo,\n        cavo_spare: cavoSpare\n      };\n    } catch (error) {\n      console.error('Debug cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default caviService;", "map": {"version": 3, "names": ["axios", "config", "API_URL", "axiosInstance", "create", "baseURL", "headers", "timeout", "withCredentials", "interceptors", "request", "use", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "caviService", "get<PERSON><PERSON>", "cantiereId", "tipoCavo", "filters", "console", "log", "cantiereIdNum", "parseInt", "isNaN", "Error", "response", "get", "data", "spareError", "url", "queryParams", "push", "stato_installazione", "encodeURIComponent", "tipologia", "sort_by", "sort_order", "length", "join", "status", "Array", "isArray", "warn", "apiError", "_apiError$response", "_apiError$response2", "_apiError$response3", "_apiError$response4", "message", "statusText", "code", "isAxiosError", "method", "testResponse", "fetch", "testError", "_error$response", "_error$response2", "_error$response3", "_error$response4", "_error$response4$data", "_error$response5", "_error$response6", "stack", "enhancedError", "detail", "originalError", "createCavo", "cavoData", "post", "updateCavo", "cavoId", "put", "getRevisioneCorrente", "revisione_corrente", "markCavoAsSpare", "force", "delete", "params", "mode", "resolve", "setTimeout", "cavoResponse", "modificato_manualmente", "verifyError", "_error$response7", "_error$response8", "_error$response9", "deleteCavo", "requestConfig", "_error$response0", "_error$response1", "_error$response10", "updateMetri<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_posati", "updateBobina", "idBobina", "id_bobina", "getCaviInstallati", "getCaviStats", "getCaviSpare", "collegaCavo", "lato", "responsabile", "scollegaCavo", "debugCavo", "attivi", "cavoAttivo", "find", "c", "id_cavo", "spare", "cavoSpare", "trovato_tra_attivi", "trovato_tra_spare", "cavo_attivo", "cavo_spare"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/caviService.js"], "sourcesContent": ["import axios from 'axios';\nimport config from '../config';\n\nconst API_URL = config.API_URL;\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 15000, // Timeout aumentato a 15 secondi\n  withCredentials: false // Modificato per risolvere problemi CORS\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null, filters = {}) => {\n    try {\n      console.log('getCavi chiamato con:', { cantiereId, tipoCavo, filters });\n      console.log('Tipo di cantiereId:', typeof cantiereId);\n\n      // Assicurati che cantiereId sia un numero\n      let cantiereIdNum = cantiereId;\n      if (typeof cantiereId === 'string') {\n        cantiereIdNum = parseInt(cantiereId, 10);\n        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);\n      }\n\n      if (isNaN(cantiereIdNum)) {\n        console.error('ID cantiere non è un numero valido:', cantiereId);\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log(`Caricamento cavi per cantiere ${cantiereIdNum} con tipo_cavo=${tipoCavo}`);\n\n      // Soluzione alternativa per i cavi SPARE\n      if (tipoCavo === 3) {\n        console.log('Caricamento cavi SPARE con query diretta...');\n        try {\n          // Usa una query SQL diretta per ottenere i cavi SPARE\n          const response = await axios.get(\n            `${API_URL}/cavi/spare/${cantiereIdNum}`,\n            {\n              headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${localStorage.getItem('token')}`\n              },\n              timeout: 30000\n            }\n          );\n\n          console.log('Risposta cavi SPARE:', response.data);\n          return response.data;\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi SPARE:', spareError);\n          // Se fallisce, continua con il metodo standard\n        }\n      }\n\n      // Costruisci l'URL con i parametri di query\n      let url = `/cavi/${cantiereIdNum}`;\n      const queryParams = [];\n\n      if (tipoCavo !== null) {\n        queryParams.push(`tipo_cavo=${tipoCavo}`);\n      }\n\n      // Aggiungi filtri aggiuntivi se presenti\n      if (filters.stato_installazione) {\n        queryParams.push(`stato_installazione=${encodeURIComponent(filters.stato_installazione)}`);\n      }\n\n      if (filters.tipologia) {\n        queryParams.push(`tipologia=${encodeURIComponent(filters.tipologia)}`);\n      }\n\n      if (filters.sort_by) {\n        queryParams.push(`sort_by=${encodeURIComponent(filters.sort_by)}`);\n        if (filters.sort_order) {\n          queryParams.push(`sort_order=${encodeURIComponent(filters.sort_order)}`);\n        }\n      }\n\n      // Aggiungi i parametri di query all'URL\n      if (queryParams.length > 0) {\n        url += `?${queryParams.join('&')}`;\n      }\n\n      // Log dettagliato dell'URL e dei parametri\n      console.log('URL API completo:', url);\n      console.log('Parametri di query:', queryParams);\n\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');\n      console.log('URL completo:', `${API_URL}${url}`);\n\n      try {\n        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);\n        console.log('Headers della richiesta:', {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        });\n\n        // Aggiungi un timeout più lungo per la richiesta\n        const response = await axiosInstance.get(url, { timeout: 30000 });\n\n        console.log(`Risposta API: ${url}`, response.data);\n        console.log('Status della risposta:', response.status);\n        console.log('Headers della risposta:', response.headers);\n\n        if (Array.isArray(response.data)) {\n          console.log(`Numero di cavi ricevuti: ${response.data.length}`);\n          if (response.data.length > 0) {\n            console.log('Primo cavo ricevuto:', response.data[0]);\n          } else {\n            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);\n          }\n        } else {\n          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);\n        }\n\n        return response.data;\n      } catch (apiError) {\n        console.error(`Errore nella chiamata API GET ${url}:`, apiError);\n        console.error('Dettagli errore API:', {\n          message: apiError.message,\n          status: apiError.response?.status,\n          statusText: apiError.response?.statusText,\n          data: apiError.response?.data,\n          headers: apiError.response?.headers,\n          code: apiError.code,\n          isAxiosError: apiError.isAxiosError,\n          config: apiError.config ? {\n            url: apiError.config.url,\n            method: apiError.config.method,\n            timeout: apiError.config.timeout,\n            headers: apiError.config.headers\n          } : 'No config'\n        });\n\n        // Gestione specifica per errori di rete\n        if (apiError.code === 'ERR_NETWORK') {\n          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');\n          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile\n          try {\n            console.log('Tentativo di test di connessione al backend...');\n            const testResponse = await fetch(API_URL);\n            console.log('Test di connessione al backend:', testResponse.status);\n          } catch (testError) {\n            console.error('Test di connessione al backend fallito:', testError);\n          }\n        }\n\n        throw apiError;\n      }\n    } catch (error) {\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,\n        stack: error.stack\n      });\n\n      // Crea un errore più informativo\n      const enhancedError = new Error(error.response?.data?.detail || error.message || 'Errore sconosciuto');\n      enhancedError.status = error.response?.status;\n      enhancedError.data = error.response?.data;\n      enhancedError.response = error.response;\n      enhancedError.originalError = error;\n      enhancedError.code = error.code;\n      enhancedError.isAxiosError = error.isAxiosError;\n\n      throw enhancedError;\n    }\n  },\n\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene la revisione corrente del cantiere\n  getRevisioneCorrente: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);\n      return response.data.revisione_corrente;\n    } catch (error) {\n      console.error('Get revisione corrente error:', error);\n      return '00'; // Valore di default in caso di errore\n    }\n  },\n\n  // Marca un cavo come SPARE\n  markCavoAsSpare: async (cantiereId, cavoId, force = false) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di marcare cavo come SPARE:', { cantiereId: cantiereIdNum, cavoId, force });\n\n      // Usa direttamente l'endpoint delete con mode=spare che sappiamo funzionare\n      console.log('URL API:', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}?mode=spare`);\n\n      const response = await axios.delete(\n        `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n        {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000,\n          params: { mode: 'spare' }\n        }\n      );\n\n      console.log('Risposta markCavoAsSpare (delete con mode=spare):', response.data);\n\n      // Verifica che il cavo sia stato effettivamente marcato come SPARE\n      try {\n        // Attendi un po' per dare tempo al database di aggiornarsi\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE...');\n        const cavoResponse = await axios.get(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Stato del cavo dopo marcatura:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n        }\n      } catch (verifyError) {\n        console.error('Errore durante la verifica dello stato del cavo:', verifyError);\n      }\n\n      return {\n        message: response.data.message || 'Cavo marcato come SPARE con successo',\n        modificato_manualmente: 3,\n        stato_installazione: 'SPARE'\n      };\n    } catch (error) {\n      console.error('Mark cavo as SPARE error:', error);\n      console.error('Dettagli errore:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina un cavo o lo marca come SPARE\n  deleteCavo: async (cantiereId, cavoId, mode = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di eliminare/marcare cavo:', { cantiereId: cantiereIdNum, cavoId, mode });\n\n      // Se è specificata la modalità, aggiungi il parametro alla richiesta\n      const requestConfig = {\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        timeout: 30000, // Timeout aumentato a 30 secondi\n        params: mode ? { mode } : {}\n      };\n\n      console.log('URL API:', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Config:', requestConfig);\n\n      // Usa axios direttamente invece di axiosInstance per avere più controllo\n      const response = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, requestConfig);\n      console.log('Risposta deleteCavo:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cavo error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n\n      // Crea un errore più informativo\n      if (error.response && error.response.data) {\n        throw error.response.data;\n      } else if (error.message) {\n        throw new Error(error.message);\n      } else {\n        throw new Error('Errore durante l\\'eliminazione del cavo');\n      }\n    }\n  },\n\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene la lista dei cavi installati di un cantiere\n  getCaviInstallati: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene le statistiche dei cavi di un cantiere\n  getCaviStats: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/stats`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi stats error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene direttamente i cavi SPARE con una query SQL\n  getCaviSpare: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log('Caricamento cavi SPARE con query SQL diretta...');\n      console.log('URL API:', `${API_URL}/cavi/spare/${cantiereIdNum}`);\n\n      // Usa axios direttamente invece di axiosInstance per avere più controllo\n      const response = await axios.get(\n        `${API_URL}/cavi/spare/${cantiereIdNum}`,\n        {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000 // Timeout aumentato a 30 secondi\n        }\n      );\n\n      console.log('Risposta getCaviSpare:', response.data ? response.data.length : 0, 'cavi SPARE trovati');\n      if (response.data && response.data.length > 0) {\n        console.log('Primo cavo SPARE:', response.data[0]);\n      }\n\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi SPARE error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Collega un lato di un cavo\n  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {\n        lato: lato,\n        responsabile: responsabile || 'cantiere'\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Collega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Scollega un lato di un cavo\n  scollegaCavo: async (cantiereId, cavoId, lato) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Scollega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Verifica lo stato di un cavo specifico (debug)\n  debugCavo: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Ottieni i cavi attivi\n      console.log('Verificando cavo tra i cavi attivi...');\n      const attivi = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=0`);\n      const cavoAttivo = attivi.data.find(c => c.id_cavo === cavoId);\n\n      // Ottieni i cavi SPARE\n      console.log('Verificando cavo tra i cavi SPARE...');\n      const spare = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=3`);\n      const cavoSpare = spare.data.find(c => c.id_cavo === cavoId);\n\n      return {\n        trovato_tra_attivi: !!cavoAttivo,\n        trovato_tra_spare: !!cavoSpare,\n        cavo_attivo: cavoAttivo,\n        cavo_spare: cavoSpare\n      };\n    } catch (error) {\n      console.error('Debug cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default caviService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAE9B,MAAMC,OAAO,GAAGD,MAAM,CAACC,OAAO;;AAE9B;AACA,MAAMC,aAAa,GAAGH,KAAK,CAACI,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,OAAO,EAAE,KAAK;EAAE;EAChBC,eAAe,EAAE,KAAK,CAAC;AACzB,CAAC,CAAC;;AAEF;AACAL,aAAa,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCV,MAAM,IAAK;EACV,MAAMW,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTX,MAAM,CAACK,OAAO,CAACS,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOX,MAAM;AACf,CAAC,EACAe,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,WAAW,GAAG;EAClB;EACAC,OAAO,EAAE,MAAAA,CAAOC,UAAU,EAAEC,QAAQ,GAAG,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC5D,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QAAEJ,UAAU;QAAEC,QAAQ;QAAEC;MAAQ,CAAC,CAAC;MACvEC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,OAAOJ,UAAU,CAAC;;MAErD;MACA,IAAIK,aAAa,GAAGL,UAAU;MAC9B,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;QAClCK,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;QACxCG,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEC,aAAa,CAAC;MAC1E;MAEA,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;QACxBF,OAAO,CAACR,KAAK,CAAC,qCAAqC,EAAEK,UAAU,CAAC;QAChE,MAAM,IAAIQ,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,iCAAiCC,aAAa,kBAAkBJ,QAAQ,EAAE,CAAC;;MAEvF;MACA,IAAIA,QAAQ,KAAK,CAAC,EAAE;QAClBE,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1D,IAAI;UACF;UACA,MAAMK,QAAQ,GAAG,MAAM9B,KAAK,CAAC+B,GAAG,CAC9B,GAAG7B,OAAO,eAAewB,aAAa,EAAE,EACxC;YACEpB,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClC,eAAe,EAAE,UAAUO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC1D,CAAC;YACDP,OAAO,EAAE;UACX,CACF,CAAC;UAEDiB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEK,QAAQ,CAACE,IAAI,CAAC;UAClD,OAAOF,QAAQ,CAACE,IAAI;QACtB,CAAC,CAAC,OAAOC,UAAU,EAAE;UACnBT,OAAO,CAACR,KAAK,CAAC,wCAAwC,EAAEiB,UAAU,CAAC;UACnE;QACF;MACF;;MAEA;MACA,IAAIC,GAAG,GAAG,SAASR,aAAa,EAAE;MAClC,MAAMS,WAAW,GAAG,EAAE;MAEtB,IAAIb,QAAQ,KAAK,IAAI,EAAE;QACrBa,WAAW,CAACC,IAAI,CAAC,aAAad,QAAQ,EAAE,CAAC;MAC3C;;MAEA;MACA,IAAIC,OAAO,CAACc,mBAAmB,EAAE;QAC/BF,WAAW,CAACC,IAAI,CAAC,uBAAuBE,kBAAkB,CAACf,OAAO,CAACc,mBAAmB,CAAC,EAAE,CAAC;MAC5F;MAEA,IAAId,OAAO,CAACgB,SAAS,EAAE;QACrBJ,WAAW,CAACC,IAAI,CAAC,aAAaE,kBAAkB,CAACf,OAAO,CAACgB,SAAS,CAAC,EAAE,CAAC;MACxE;MAEA,IAAIhB,OAAO,CAACiB,OAAO,EAAE;QACnBL,WAAW,CAACC,IAAI,CAAC,WAAWE,kBAAkB,CAACf,OAAO,CAACiB,OAAO,CAAC,EAAE,CAAC;QAClE,IAAIjB,OAAO,CAACkB,UAAU,EAAE;UACtBN,WAAW,CAACC,IAAI,CAAC,cAAcE,kBAAkB,CAACf,OAAO,CAACkB,UAAU,CAAC,EAAE,CAAC;QAC1E;MACF;;MAEA;MACA,IAAIN,WAAW,CAACO,MAAM,GAAG,CAAC,EAAE;QAC1BR,GAAG,IAAI,IAAIC,WAAW,CAACQ,IAAI,CAAC,GAAG,CAAC,EAAE;MACpC;;MAEA;MACAnB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAES,GAAG,CAAC;MACrCV,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEU,WAAW,CAAC;MAE/CX,OAAO,CAACC,GAAG,CAAC,qBAAqBS,GAAG,EAAE,CAAC;MACvCV,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,UAAU,CAAC;MAC9EU,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,GAAGvB,OAAO,GAAGgC,GAAG,EAAE,CAAC;MAEhD,IAAI;QACFV,OAAO,CAACC,GAAG,CAAC,kCAAkCS,GAAG,eAAerB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,UAAU,EAAE,CAAC;QAC1HU,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;UACtC,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC,CAAC;;QAEF;QACA,MAAMgB,QAAQ,GAAG,MAAM3B,aAAa,CAAC4B,GAAG,CAACG,GAAG,EAAE;UAAE3B,OAAO,EAAE;QAAM,CAAC,CAAC;QAEjEiB,OAAO,CAACC,GAAG,CAAC,iBAAiBS,GAAG,EAAE,EAAEJ,QAAQ,CAACE,IAAI,CAAC;QAClDR,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEK,QAAQ,CAACc,MAAM,CAAC;QACtDpB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEK,QAAQ,CAACxB,OAAO,CAAC;QAExD,IAAIuC,KAAK,CAACC,OAAO,CAAChB,QAAQ,CAACE,IAAI,CAAC,EAAE;UAChCR,OAAO,CAACC,GAAG,CAAC,4BAA4BK,QAAQ,CAACE,IAAI,CAACU,MAAM,EAAE,CAAC;UAC/D,IAAIZ,QAAQ,CAACE,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;YAC5BlB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEK,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,MAAM;YACLR,OAAO,CAACuB,IAAI,CAAC,uCAAuCrB,aAAa,aAAaJ,QAAQ,EAAE,CAAC;UAC3F;QACF,CAAC,MAAM;UACLE,OAAO,CAACuB,IAAI,CAAC,4BAA4B,OAAOjB,QAAQ,CAACE,IAAI,EAAE,EAAEF,QAAQ,CAACE,IAAI,CAAC;QACjF;QAEA,OAAOF,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOgB,QAAQ,EAAE;QAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;QACjB5B,OAAO,CAACR,KAAK,CAAC,iCAAiCkB,GAAG,GAAG,EAAEc,QAAQ,CAAC;QAChExB,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAE;UACpCqC,OAAO,EAAEL,QAAQ,CAACK,OAAO;UACzBT,MAAM,GAAAK,kBAAA,GAAED,QAAQ,CAAClB,QAAQ,cAAAmB,kBAAA,uBAAjBA,kBAAA,CAAmBL,MAAM;UACjCU,UAAU,GAAAJ,mBAAA,GAAEF,QAAQ,CAAClB,QAAQ,cAAAoB,mBAAA,uBAAjBA,mBAAA,CAAmBI,UAAU;UACzCtB,IAAI,GAAAmB,mBAAA,GAAEH,QAAQ,CAAClB,QAAQ,cAAAqB,mBAAA,uBAAjBA,mBAAA,CAAmBnB,IAAI;UAC7B1B,OAAO,GAAA8C,mBAAA,GAAEJ,QAAQ,CAAClB,QAAQ,cAAAsB,mBAAA,uBAAjBA,mBAAA,CAAmB9C,OAAO;UACnCiD,IAAI,EAAEP,QAAQ,CAACO,IAAI;UACnBC,YAAY,EAAER,QAAQ,CAACQ,YAAY;UACnCvD,MAAM,EAAE+C,QAAQ,CAAC/C,MAAM,GAAG;YACxBiC,GAAG,EAAEc,QAAQ,CAAC/C,MAAM,CAACiC,GAAG;YACxBuB,MAAM,EAAET,QAAQ,CAAC/C,MAAM,CAACwD,MAAM;YAC9BlD,OAAO,EAAEyC,QAAQ,CAAC/C,MAAM,CAACM,OAAO;YAChCD,OAAO,EAAE0C,QAAQ,CAAC/C,MAAM,CAACK;UAC3B,CAAC,GAAG;QACN,CAAC,CAAC;;QAEF;QACA,IAAI0C,QAAQ,CAACO,IAAI,KAAK,aAAa,EAAE;UACnC/B,OAAO,CAACR,KAAK,CAAC,0EAA0E,CAAC;UACzF;UACA,IAAI;YACFQ,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;YAC7D,MAAMiC,YAAY,GAAG,MAAMC,KAAK,CAACzD,OAAO,CAAC;YACzCsB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEiC,YAAY,CAACd,MAAM,CAAC;UACrE,CAAC,CAAC,OAAOgB,SAAS,EAAE;YAClBpC,OAAO,CAACR,KAAK,CAAC,yCAAyC,EAAE4C,SAAS,CAAC;UACrE;QACF;QAEA,MAAMZ,QAAQ;MAChB;IACF,CAAC,CAAC,OAAOhC,KAAK,EAAE;MAAA,IAAA6C,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACd3C,OAAO,CAACR,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCQ,OAAO,CAACR,KAAK,CAAC,gBAAgB,EAAE;QAC9BqC,OAAO,EAAErC,KAAK,CAACqC,OAAO;QACtBT,MAAM,GAAAiB,eAAA,GAAE7C,KAAK,CAACc,QAAQ,cAAA+B,eAAA,uBAAdA,eAAA,CAAgBjB,MAAM;QAC9BU,UAAU,GAAAQ,gBAAA,GAAE9C,KAAK,CAACc,QAAQ,cAAAgC,gBAAA,uBAAdA,gBAAA,CAAgBR,UAAU;QACtCtB,IAAI,GAAA+B,gBAAA,GAAE/C,KAAK,CAACc,QAAQ,cAAAiC,gBAAA,uBAAdA,gBAAA,CAAgB/B,IAAI;QAC1BE,GAAG,EAAE,SAASb,UAAU,GAAGC,QAAQ,KAAK,IAAI,GAAG,cAAcA,QAAQ,EAAE,GAAG,EAAE,EAAE;QAC9E8C,KAAK,EAAEpD,KAAK,CAACoD;MACf,CAAC,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAG,IAAIxC,KAAK,CAAC,EAAAmC,gBAAA,GAAAhD,KAAK,CAACc,QAAQ,cAAAkC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsBK,MAAM,KAAItD,KAAK,CAACqC,OAAO,IAAI,oBAAoB,CAAC;MACtGgB,aAAa,CAACzB,MAAM,IAAAsB,gBAAA,GAAGlD,KAAK,CAACc,QAAQ,cAAAoC,gBAAA,uBAAdA,gBAAA,CAAgBtB,MAAM;MAC7CyB,aAAa,CAACrC,IAAI,IAAAmC,gBAAA,GAAGnD,KAAK,CAACc,QAAQ,cAAAqC,gBAAA,uBAAdA,gBAAA,CAAgBnC,IAAI;MACzCqC,aAAa,CAACvC,QAAQ,GAAGd,KAAK,CAACc,QAAQ;MACvCuC,aAAa,CAACE,aAAa,GAAGvD,KAAK;MACnCqD,aAAa,CAACd,IAAI,GAAGvC,KAAK,CAACuC,IAAI;MAC/Bc,aAAa,CAACb,YAAY,GAAGxC,KAAK,CAACwC,YAAY;MAE/C,MAAMa,aAAa;IACrB;EACF,CAAC;EAED;EACAG,UAAU,EAAE,MAAAA,CAAOnD,UAAU,EAAEoD,QAAQ,KAAK;IAC1C,IAAI;MACF;MACA,MAAM/C,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAM3B,aAAa,CAACuE,IAAI,CAAC,SAAShD,aAAa,EAAE,EAAE+C,QAAQ,CAAC;MAC7E,OAAO3C,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACA2D,UAAU,EAAE,MAAAA,CAAOtD,UAAU,EAAEuD,MAAM,EAAEH,QAAQ,KAAK;IAClD,IAAI;MACF;MACA,MAAM/C,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAM3B,aAAa,CAAC0E,GAAG,CAAC,SAASnD,aAAa,IAAIkD,MAAM,EAAE,EAAEH,QAAQ,CAAC;MACtF,OAAO3C,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACA8D,oBAAoB,EAAE,MAAOzD,UAAU,IAAK;IAC1C,IAAI;MACF;MACA,MAAMK,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAM3B,aAAa,CAAC4B,GAAG,CAAC,SAASL,aAAa,qBAAqB,CAAC;MACrF,OAAOI,QAAQ,CAACE,IAAI,CAAC+C,kBAAkB;IACzC,CAAC,CAAC,OAAO/D,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC;EAED;EACAgE,eAAe,EAAE,MAAAA,CAAO3D,UAAU,EAAEuD,MAAM,EAAEK,KAAK,GAAG,KAAK,KAAK;IAC5D,IAAI;MACF;MACA,MAAMvD,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QAAEJ,UAAU,EAAEK,aAAa;QAAEkD,MAAM;QAAEK;MAAM,CAAC,CAAC;;MAElG;MACAzD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,GAAGvB,OAAO,SAASwB,aAAa,IAAIkD,MAAM,aAAa,CAAC;MAEhF,MAAM9C,QAAQ,GAAG,MAAM9B,KAAK,CAACkF,MAAM,CACjC,GAAGhF,OAAO,SAASwB,aAAa,IAAIkD,MAAM,EAAE,EAC5C;QACEtE,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACDP,OAAO,EAAE,KAAK;QACd4E,MAAM,EAAE;UAAEC,IAAI,EAAE;QAAQ;MAC1B,CACF,CAAC;MAED5D,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEK,QAAQ,CAACE,IAAI,CAAC;;MAE/E;MACA,IAAI;QACF;QACA,MAAM,IAAIf,OAAO,CAACoE,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;QAEvD;QACA7D,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE,MAAM8D,YAAY,GAAG,MAAMvF,KAAK,CAAC+B,GAAG,CAClC,GAAG7B,OAAO,SAASwB,aAAa,IAAIkD,MAAM,EAAE,EAC5C;UACEtE,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDP,OAAO,EAAE;QACX,CACF,CAAC;QAEDiB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE8D,YAAY,CAACvD,IAAI,CAAC;;QAEhE;QACA,IAAIuD,YAAY,CAACvD,IAAI,CAACwD,sBAAsB,KAAK,CAAC,EAAE;UAClDhE,OAAO,CAACR,KAAK,CAAC,8EAA8E,CAAC;QAC/F;MACF,CAAC,CAAC,OAAOyE,WAAW,EAAE;QACpBjE,OAAO,CAACR,KAAK,CAAC,kDAAkD,EAAEyE,WAAW,CAAC;MAChF;MAEA,OAAO;QACLpC,OAAO,EAAEvB,QAAQ,CAACE,IAAI,CAACqB,OAAO,IAAI,sCAAsC;QACxEmC,sBAAsB,EAAE,CAAC;QACzBnD,mBAAmB,EAAE;MACvB,CAAC;IACH,CAAC,CAAC,OAAOrB,KAAK,EAAE;MAAA,IAAA0E,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdpE,OAAO,CAACR,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDQ,OAAO,CAACR,KAAK,CAAC,kBAAkB,EAAE;QAChCqC,OAAO,EAAErC,KAAK,CAACqC,OAAO;QACtBT,MAAM,GAAA8C,gBAAA,GAAE1E,KAAK,CAACc,QAAQ,cAAA4D,gBAAA,uBAAdA,gBAAA,CAAgB9C,MAAM;QAC9BU,UAAU,GAAAqC,gBAAA,GAAE3E,KAAK,CAACc,QAAQ,cAAA6D,gBAAA,uBAAdA,gBAAA,CAAgBrC,UAAU;QACtCtB,IAAI,GAAA4D,gBAAA,GAAE5E,KAAK,CAACc,QAAQ,cAAA8D,gBAAA,uBAAdA,gBAAA,CAAgB5D,IAAI;QAC1BE,GAAG,EAAE,GAAGhC,OAAO,SAASmB,UAAU,IAAIuD,MAAM,EAAE;QAC9C3E,MAAM,EAAEe,KAAK,CAACf;MAChB,CAAC,CAAC;MACF,MAAMe,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACA6E,UAAU,EAAE,MAAAA,CAAOxE,UAAU,EAAEuD,MAAM,EAAEQ,IAAI,GAAG,IAAI,KAAK;IACrD,IAAI;MACF;MACA,MAAM1D,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAAEJ,UAAU,EAAEK,aAAa;QAAEkD,MAAM;QAAEQ;MAAK,CAAC,CAAC;;MAEhG;MACA,MAAMU,aAAa,GAAG;QACpBxF,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACDP,OAAO,EAAE,KAAK;QAAE;QAChB4E,MAAM,EAAEC,IAAI,GAAG;UAAEA;QAAK,CAAC,GAAG,CAAC;MAC7B,CAAC;MAED5D,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,GAAGvB,OAAO,SAASwB,aAAa,IAAIkD,MAAM,EAAE,CAAC;MACrEpD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEqE,aAAa,CAAC;;MAErC;MACA,MAAMhE,QAAQ,GAAG,MAAM9B,KAAK,CAACkF,MAAM,CAAC,GAAGhF,OAAO,SAASwB,aAAa,IAAIkD,MAAM,EAAE,EAAEkB,aAAa,CAAC;MAChGtE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEK,QAAQ,CAACE,IAAI,CAAC;MAClD,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MAAA,IAAA+E,gBAAA,EAAAC,gBAAA,EAAAC,iBAAA;MACdzE,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CQ,OAAO,CAACR,KAAK,CAAC,gBAAgB,EAAE;QAC9BqC,OAAO,EAAErC,KAAK,CAACqC,OAAO;QACtBT,MAAM,GAAAmD,gBAAA,GAAE/E,KAAK,CAACc,QAAQ,cAAAiE,gBAAA,uBAAdA,gBAAA,CAAgBnD,MAAM;QAC9BU,UAAU,GAAA0C,gBAAA,GAAEhF,KAAK,CAACc,QAAQ,cAAAkE,gBAAA,uBAAdA,gBAAA,CAAgB1C,UAAU;QACtCtB,IAAI,GAAAiE,iBAAA,GAAEjF,KAAK,CAACc,QAAQ,cAAAmE,iBAAA,uBAAdA,iBAAA,CAAgBjE,IAAI;QAC1BE,GAAG,EAAE,GAAGhC,OAAO,SAASmB,UAAU,IAAIuD,MAAM,EAAE;QAC9C3E,MAAM,EAAEe,KAAK,CAACf;MAChB,CAAC,CAAC;;MAEF;MACA,IAAIe,KAAK,CAACc,QAAQ,IAAId,KAAK,CAACc,QAAQ,CAACE,IAAI,EAAE;QACzC,MAAMhB,KAAK,CAACc,QAAQ,CAACE,IAAI;MAC3B,CAAC,MAAM,IAAIhB,KAAK,CAACqC,OAAO,EAAE;QACxB,MAAM,IAAIxB,KAAK,CAACb,KAAK,CAACqC,OAAO,CAAC;MAChC,CAAC,MAAM;QACL,MAAM,IAAIxB,KAAK,CAAC,yCAAyC,CAAC;MAC5D;IACF;EACF,CAAC;EAED;EACAqE,iBAAiB,EAAE,MAAAA,CAAO7E,UAAU,EAAEuD,MAAM,EAAEuB,WAAW,KAAK;IAC5D,IAAI;MACF;MACA,MAAMzE,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAM3B,aAAa,CAACuE,IAAI,CAAC,SAAShD,aAAa,IAAIkD,MAAM,eAAe,EAAE;QACzFwB,YAAY,EAAED;MAChB,CAAC,CAAC;MACF,OAAOrE,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACAqF,YAAY,EAAE,MAAAA,CAAOhF,UAAU,EAAEuD,MAAM,EAAE0B,QAAQ,KAAK;IACpD,IAAI;MACF;MACA,MAAM5E,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAM3B,aAAa,CAACuE,IAAI,CAAC,SAAShD,aAAa,IAAIkD,MAAM,SAAS,EAAE;QACnF2B,SAAS,EAAED;MACb,CAAC,CAAC;MACF,OAAOxE,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACAwF,iBAAiB,EAAE,MAAOnF,UAAU,IAAK;IACvC,IAAI;MACF;MACA,MAAMK,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAM3B,aAAa,CAAC4B,GAAG,CAAC,SAASL,aAAa,aAAa,CAAC;MAC7E,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACAyF,YAAY,EAAE,MAAOpF,UAAU,IAAK;IAClC,IAAI;MACF;MACA,MAAMK,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAM3B,aAAa,CAAC4B,GAAG,CAAC,SAASL,aAAa,QAAQ,CAAC;MACxE,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACA0F,YAAY,EAAE,MAAOrF,UAAU,IAAK;IAClC,IAAI;MACF;MACA,MAAMK,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9DD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,GAAGvB,OAAO,eAAewB,aAAa,EAAE,CAAC;;MAEjE;MACA,MAAMI,QAAQ,GAAG,MAAM9B,KAAK,CAAC+B,GAAG,CAC9B,GAAG7B,OAAO,eAAewB,aAAa,EAAE,EACxC;QACEpB,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACDP,OAAO,EAAE,KAAK,CAAC;MACjB,CACF,CAAC;MAEDiB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEK,QAAQ,CAACE,IAAI,GAAGF,QAAQ,CAACE,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE,oBAAoB,CAAC;MACrG,IAAIZ,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;QAC7ClB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEK,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;MACpD;MAEA,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACA2F,WAAW,EAAE,MAAAA,CAAOtF,UAAU,EAAEuD,MAAM,EAAEgC,IAAI,EAAEC,YAAY,KAAK;IAC7D,IAAI;MACF;MACA,MAAMnF,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAM3B,aAAa,CAACuE,IAAI,CAAC,SAAShD,aAAa,IAAIkD,MAAM,eAAe,EAAE;QACzFgC,IAAI,EAAEA,IAAI;QACVC,YAAY,EAAEA,YAAY,IAAI;MAChC,CAAC,CAAC;MACF,OAAO/E,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACA8F,YAAY,EAAE,MAAAA,CAAOzF,UAAU,EAAEuD,MAAM,EAAEgC,IAAI,KAAK;IAChD,IAAI;MACF;MACA,MAAMlF,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAM3B,aAAa,CAAC+E,MAAM,CAAC,SAASxD,aAAa,IAAIkD,MAAM,iBAAiBgC,IAAI,EAAE,CAAC;MACpG,OAAO9E,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF,CAAC;EAED;EACA+F,SAAS,EAAE,MAAAA,CAAO1F,UAAU,EAAEuD,MAAM,KAAK;IACvC,IAAI;MACF;MACA,MAAMlD,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,MAAMuF,MAAM,GAAG,MAAM7G,aAAa,CAAC4B,GAAG,CAAC,SAASL,aAAa,cAAc,CAAC;MAC5E,MAAMuF,UAAU,GAAGD,MAAM,CAAChF,IAAI,CAACkF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKxC,MAAM,CAAC;;MAE9D;MACApD,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,MAAM4F,KAAK,GAAG,MAAMlH,aAAa,CAAC4B,GAAG,CAAC,SAASL,aAAa,cAAc,CAAC;MAC3E,MAAM4F,SAAS,GAAGD,KAAK,CAACrF,IAAI,CAACkF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKxC,MAAM,CAAC;MAE5D,OAAO;QACL2C,kBAAkB,EAAE,CAAC,CAACN,UAAU;QAChCO,iBAAiB,EAAE,CAAC,CAACF,SAAS;QAC9BG,WAAW,EAAER,UAAU;QACvBS,UAAU,EAAEJ;MACd,CAAC;IACH,CAAC,CAAC,OAAOtG,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,MAAMA,KAAK,CAACc,QAAQ,GAAGd,KAAK,CAACc,QAAQ,CAACE,IAAI,GAAGhB,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}