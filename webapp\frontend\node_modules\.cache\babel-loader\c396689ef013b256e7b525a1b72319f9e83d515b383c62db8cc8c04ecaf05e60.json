{"ast": null, "code": "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\n// Reference: https://www.unicode.org/cldr/charts/32/summary/kn.html\n\nconst dateFormats = {\n  full: \"EEEE, MMMM d, y\",\n  // CLDR 1816\n  long: \"MMMM d, y\",\n  // CLDR 1817\n  medium: \"MMM d, y\",\n  // CLDR 1818\n  short: \"d/M/yy\" // CLDR 1819\n};\nconst timeFormats = {\n  full: \"hh:mm:ss a zzzz\",\n  // CLDR 1820\n  long: \"hh:mm:ss a z\",\n  // CLDR 1821\n  medium: \"hh:mm:ss a\",\n  // CLDR 1822\n  short: \"hh:mm a\" // CLDR 1823\n};\nconst dateTimeFormats = {\n  full: \"{{date}} {{time}}\",\n  // CLDR 1824\n  long: \"{{date}} {{time}}\",\n  // CLDR 1825\n  medium: \"{{date}} {{time}}\",\n  // CLDR 1826\n  short: \"{{date}} {{time}}\" // CLDR 1827\n};\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};", "map": {"version": 3, "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/kn/_lib/formatLong.js"], "sourcesContent": ["import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\n// Reference: https://www.unicode.org/cldr/charts/32/summary/kn.html\n\nconst dateFormats = {\n  full: \"EEEE, MMMM d, y\", // CLDR 1816\n  long: \"MMMM d, y\", // CLDR 1817\n  medium: \"MMM d, y\", // CLDR 1818\n  short: \"d/M/yy\", // CLDR 1819\n};\n\nconst timeFormats = {\n  full: \"hh:mm:ss a zzzz\", // CLDR 1820\n  long: \"hh:mm:ss a z\", // CLDR 1821\n  medium: \"hh:mm:ss a\", // CLDR 1822\n  short: \"hh:mm a\", // CLDR 1823\n};\n\nconst dateTimeFormats = {\n  full: \"{{date}} {{time}}\", // CLDR 1824\n  long: \"{{date}} {{time}}\", // CLDR 1825\n  medium: \"{{date}} {{time}}\", // CLDR 1826\n  short: \"{{date}} {{time}}\", // CLDR 1827\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,iCAAiC;;AAEnE;;AAEA,MAAMC,WAAW,GAAG;EAClBC,IAAI,EAAE,iBAAiB;EAAE;EACzBC,IAAI,EAAE,WAAW;EAAE;EACnBC,MAAM,EAAE,UAAU;EAAE;EACpBC,KAAK,EAAE,QAAQ,CAAE;AACnB,CAAC;AAED,MAAMC,WAAW,GAAG;EAClBJ,IAAI,EAAE,iBAAiB;EAAE;EACzBC,IAAI,EAAE,cAAc;EAAE;EACtBC,MAAM,EAAE,YAAY;EAAE;EACtBC,KAAK,EAAE,SAAS,CAAE;AACpB,CAAC;AAED,MAAME,eAAe,GAAG;EACtBL,IAAI,EAAE,mBAAmB;EAAE;EAC3BC,IAAI,EAAE,mBAAmB;EAAE;EAC3BC,MAAM,EAAE,mBAAmB;EAAE;EAC7BC,KAAK,EAAE,mBAAmB,CAAE;AAC9B,CAAC;AAED,OAAO,MAAMG,UAAU,GAAG;EACxBC,IAAI,EAAET,iBAAiB,CAAC;IACtBU,OAAO,EAAET,WAAW;IACpBU,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,IAAI,EAAEZ,iBAAiB,CAAC;IACtBU,OAAO,EAAEJ,WAAW;IACpBK,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFE,QAAQ,EAAEb,iBAAiB,CAAC;IAC1BU,OAAO,EAAEH,eAAe;IACxBI,YAAY,EAAE;EAChB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}