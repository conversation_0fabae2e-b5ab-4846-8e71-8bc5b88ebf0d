{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"av. J.-C\", \"ap. J.-C\"],\n  abbreviated: [\"av. J.-C\", \"ap. J.-C\"],\n  wide: [\"avant Jésus-Christ\", \"après J<PERSON>us-Christ\"]\n};\nconst quarterValues = {\n  narrow: [\"T1\", \"T2\", \"T3\", \"T4\"],\n  abbreviated: [\"1er trim.\", \"2ème trim.\", \"3ème trim.\", \"4ème trim.\"],\n  wide: [\"1er trimestre\", \"2ème trimestre\", \"3ème trimestre\", \"4ème trimestre\"]\n};\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"janv.\", \"févr.\", \"mars\", \"avr.\", \"mai\", \"juin\", \"juil.\", \"août\", \"sept.\", \"oct.\", \"nov.\", \"déc.\"],\n  wide: [\"janvier\", \"février\", \"mars\", \"avril\", \"mai\", \"juin\", \"juillet\", \"août\", \"septembre\", \"octobre\", \"novembre\", \"décembre\"]\n};\nconst dayValues = {\n  narrow: [\"D\", \"L\", \"M\", \"M\", \"J\", \"V\", \"S\"],\n  short: [\"di\", \"lu\", \"ma\", \"me\", \"je\", \"ve\", \"sa\"],\n  abbreviated: [\"dim.\", \"lun.\", \"mar.\", \"mer.\", \"jeu.\", \"ven.\", \"sam.\"],\n  wide: [\"dimanche\", \"lundi\", \"mardi\", \"mercredi\", \"jeudi\", \"vendredi\", \"samedi\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"minuit\",\n    noon: \"midi\",\n    morning: \"mat.\",\n    afternoon: \"ap.m.\",\n    evening: \"soir\",\n    night: \"mat.\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"minuit\",\n    noon: \"midi\",\n    morning: \"matin\",\n    afternoon: \"après-midi\",\n    evening: \"soir\",\n    night: \"matin\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"minuit\",\n    noon: \"midi\",\n    morning: \"du matin\",\n    afternoon: \"de l’après-midi\",\n    evening: \"du soir\",\n    night: \"du matin\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n  if (number === 0) return \"0\";\n  const feminineUnits = [\"year\", \"week\", \"hour\", \"minute\", \"second\"];\n  let suffix;\n  if (number === 1) {\n    suffix = unit && feminineUnits.includes(unit) ? \"ère\" : \"er\";\n  } else {\n    suffix = \"ème\";\n  }\n  return number + suffix;\n};\nconst LONG_MONTHS_TOKENS = [\"MMM\", \"MMMM\"];\nexport const localize = {\n  preprocessor: (date, parts) => {\n    // Replaces the `do` tokens with `d` when used with long month tokens and the day of the month is greater than one.\n    // Use case \"do MMMM\" => 1er août, 29 août\n    // see https://github.com/date-fns/date-fns/issues/1391\n\n    if (date.getDate() === 1) return parts;\n    const hasLongMonthToken = parts.some(part => part.isToken && LONG_MONTHS_TOKENS.includes(part.value));\n    if (!hasLongMonthToken) return parts;\n    return parts.map(part => part.isToken && part.value === \"do\" ? {\n      isToken: true,\n      value: \"d\"\n    } : part);\n  },\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "feminineUnits", "suffix", "includes", "LONG_MONTHS_TOKENS", "localize", "preprocessor", "date", "parts", "getDate", "hasLongMonthToken", "some", "part", "isToken", "value", "map", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/fr/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"av. J.-C\", \"ap. J.-C\"],\n  abbreviated: [\"av. J.-C\", \"ap. J.-C\"],\n  wide: [\"avant Jésus-Christ\", \"après J<PERSON>us-Christ\"],\n};\n\nconst quarterValues = {\n  narrow: [\"T1\", \"T2\", \"T3\", \"T4\"],\n  abbreviated: [\"1er trim.\", \"2ème trim.\", \"3ème trim.\", \"4ème trim.\"],\n  wide: [\"1er trimestre\", \"2ème trimestre\", \"3ème trimestre\", \"4ème trimestre\"],\n};\n\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\n    \"janv.\",\n    \"févr.\",\n    \"mars\",\n    \"avr.\",\n    \"mai\",\n    \"juin\",\n    \"juil.\",\n    \"août\",\n    \"sept.\",\n    \"oct.\",\n    \"nov.\",\n    \"déc.\",\n  ],\n\n  wide: [\n    \"janvier\",\n    \"février\",\n    \"mars\",\n    \"avril\",\n    \"mai\",\n    \"juin\",\n    \"juillet\",\n    \"août\",\n    \"septembre\",\n    \"octobre\",\n    \"novembre\",\n    \"décembre\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"D\", \"L\", \"M\", \"M\", \"J\", \"V\", \"S\"],\n  short: [\"di\", \"lu\", \"ma\", \"me\", \"je\", \"ve\", \"sa\"],\n  abbreviated: [\"dim.\", \"lun.\", \"mar.\", \"mer.\", \"jeu.\", \"ven.\", \"sam.\"],\n\n  wide: [\n    \"dimanche\",\n    \"lundi\",\n    \"mardi\",\n    \"mercredi\",\n    \"jeudi\",\n    \"vendredi\",\n    \"samedi\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"minuit\",\n    noon: \"midi\",\n    morning: \"mat.\",\n    afternoon: \"ap.m.\",\n    evening: \"soir\",\n    night: \"mat.\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"minuit\",\n    noon: \"midi\",\n    morning: \"matin\",\n    afternoon: \"après-midi\",\n    evening: \"soir\",\n    night: \"matin\",\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"minuit\",\n    noon: \"midi\",\n    morning: \"du matin\",\n    afternoon: \"de l’après-midi\",\n    evening: \"du soir\",\n    night: \"du matin\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n\n  if (number === 0) return \"0\";\n\n  const feminineUnits = [\"year\", \"week\", \"hour\", \"minute\", \"second\"];\n  let suffix;\n\n  if (number === 1) {\n    suffix = unit && feminineUnits.includes(unit) ? \"ère\" : \"er\";\n  } else {\n    suffix = \"ème\";\n  }\n\n  return number + suffix;\n};\n\nconst LONG_MONTHS_TOKENS = [\"MMM\", \"MMMM\"];\n\nexport const localize = {\n  preprocessor: (date, parts) => {\n    // Replaces the `do` tokens with `d` when used with long month tokens and the day of the month is greater than one.\n    // Use case \"do MMMM\" => 1er août, 29 août\n    // see https://github.com/date-fns/date-fns/issues/1391\n\n    if (date.getDate() === 1) return parts;\n\n    const hasLongMonthToken = parts.some(\n      (part) => part.isToken && LONG_MONTHS_TOKENS.includes(part.value),\n    );\n\n    if (!hasLongMonthToken) return parts;\n\n    return parts.map((part) =>\n      part.isToken && part.value === \"do\"\n        ? { isToken: true, value: \"d\" }\n        : part,\n    );\n  },\n\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;EAChCC,WAAW,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;EACrCC,IAAI,EAAE,CAAC,oBAAoB,EAAE,oBAAoB;AACnD,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAChCC,WAAW,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC;EACpEC,IAAI,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB;AAC9E,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,CACP;EAEDC,IAAI,EAAE,CACJ,SAAS,EACT,SAAS,EACT,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,SAAS,EACT,MAAM,EACN,WAAW,EACX,SAAS,EACT,UAAU,EACV,UAAU;AAEd,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAErEC,IAAI,EAAE,CACJ,UAAU,EACV,OAAO,EACP,OAAO,EACP,UAAU,EACV,OAAO,EACP,UAAU,EACV,QAAQ;AAEZ,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,OAAO;IAClBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,iBAAiB;IAC5BC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC9C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,MAAMI,IAAI,GAAGH,OAAO,EAAEG,IAAI;EAE1B,IAAIF,MAAM,KAAK,CAAC,EAAE,OAAO,GAAG;EAE5B,MAAMG,aAAa,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC;EAClE,IAAIC,MAAM;EAEV,IAAIJ,MAAM,KAAK,CAAC,EAAE;IAChBI,MAAM,GAAGF,IAAI,IAAIC,aAAa,CAACE,QAAQ,CAACH,IAAI,CAAC,GAAG,KAAK,GAAG,IAAI;EAC9D,CAAC,MAAM;IACLE,MAAM,GAAG,KAAK;EAChB;EAEA,OAAOJ,MAAM,GAAGI,MAAM;AACxB,CAAC;AAED,MAAME,kBAAkB,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;AAE1C,OAAO,MAAMC,QAAQ,GAAG;EACtBC,YAAY,EAAEA,CAACC,IAAI,EAAEC,KAAK,KAAK;IAC7B;IACA;IACA;;IAEA,IAAID,IAAI,CAACE,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,OAAOD,KAAK;IAEtC,MAAME,iBAAiB,GAAGF,KAAK,CAACG,IAAI,CACjCC,IAAI,IAAKA,IAAI,CAACC,OAAO,IAAIT,kBAAkB,CAACD,QAAQ,CAACS,IAAI,CAACE,KAAK,CAClE,CAAC;IAED,IAAI,CAACJ,iBAAiB,EAAE,OAAOF,KAAK;IAEpC,OAAOA,KAAK,CAACO,GAAG,CAAEH,IAAI,IACpBA,IAAI,CAACC,OAAO,IAAID,IAAI,CAACE,KAAK,KAAK,IAAI,GAC/B;MAAED,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAI,CAAC,GAC7BF,IACN,CAAC;EACH,CAAC;EAEDjB,aAAa;EAEbqB,GAAG,EAAEvC,eAAe,CAAC;IACnBwC,MAAM,EAAEvC,SAAS;IACjBwC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE1C,eAAe,CAAC;IACvBwC,MAAM,EAAEnC,aAAa;IACrBoC,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE5C,eAAe,CAAC;IACrBwC,MAAM,EAAElC,WAAW;IACnBmC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE7C,eAAe,CAAC;IACnBwC,MAAM,EAAEjC,SAAS;IACjBkC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAE9C,eAAe,CAAC;IACzBwC,MAAM,EAAE/B,eAAe;IACvBgC,YAAY,EAAE;EAChB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}