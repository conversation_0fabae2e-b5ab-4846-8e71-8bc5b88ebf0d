{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\MetriPosatiSemplificatoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, TextField, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, CircularProgress, Alert, Chip, Divider, Grid, FormControl, InputLabel, Select, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, DialogContentText } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione ultra-semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MetriPosatiSemplificatoForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per il caricamento\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReelData, setIncompatibleReelData] = useState({\n    cavo: null,\n    bobina: null\n  });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n\n  // Carica la lista dei cavi e delle bobine all'avvio\n  useEffect(() => {\n    loadCavi();\n    loadBobine();\n  }, [cantiereId]);\n\n  // Carica la lista dei cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi che non sono SPARE\n      const caviAttivi = caviData.filter(cavo => !isCableSpare(cavo));\n      setCavi(caviAttivi);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Carica la lista delle bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine per cantiere:', cantiereId);\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log('Bobine caricate:', bobineData);\n      console.log('Dettaglio bobine:');\n      bobineData.forEach(bobina => {\n        console.log(`Bobina ${bobina.id_bobina}:`, {\n          tipologia: bobina.tipologia,\n          sezione: bobina.sezione,\n          metri_residui: bobina.metri_residui,\n          stato_bobina: bobina.stato_bobina\n        });\n      });\n      console.log('IMPORTANTE: Impostazione delle bobine nello stato...');\n      setBobine(bobineData);\n      console.log('Bobine impostate nello stato:', bobineData.length);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    console.log('Cavo selezionato:', cavo);\n    console.log('Dettaglio cavo:', {\n      id_cavo: cavo.id_cavo,\n      tipologia: cavo.tipologia,\n      sezione: cavo.sezione,\n      metri_teorici: cavo.metri_teorici,\n      stato_installazione: cavo.stato_installazione\n    });\n\n    // Verifica se il cavo è già posato\n    if (isCableInstalled(cavo)) {\n      console.log('Cavo già posato, mostro dialog');\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    setSelectedCavo(cavo);\n    setFormData({\n      id_cavo: cavo.id_cavo,\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n\n    // Log per debug - verifica se ci sono bobine compatibili\n    console.log('Verifica bobine compatibili per il cavo selezionato...');\n  };\n\n  // Gestisce la modifica dei campi del form\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un singolo campo\n  const validateField = (name, value) => {\n    const newErrors = {\n      ...formErrors\n    };\n    const newWarnings = {\n      ...formWarnings\n    };\n    if (name === 'metri_posati') {\n      // Validazione metri posati\n      if (value === '') {\n        newErrors.metri_posati = 'I metri posati sono obbligatori';\n      } else if (isNaN(value) || parseFloat(value) < 0) {\n        newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n      } else {\n        delete newErrors.metri_posati;\n\n        // Avvisi sui metri posati\n        const metriPosati = parseFloat(value);\n        if (selectedCavo && metriPosati > selectedCavo.metri_teorici) {\n          newWarnings.metri_posati = `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`;\n        } else {\n          delete newWarnings.metri_posati;\n        }\n\n        // Avvisi sulla bobina selezionata\n        if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n          const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n    if (name === 'id_bobina') {\n      // Validazione bobina\n      if (value === '') {\n        newErrors.id_bobina = 'La bobina è obbligatoria';\n      } else {\n        delete newErrors.id_bobina;\n\n        // Avvisi sulla bobina selezionata\n        if (value !== 'BOBINA_VUOTA' && formData.metri_posati) {\n          const metriPosati = parseFloat(formData.metri_posati);\n          const selectedBobina = bobine.find(b => b.id_bobina === value);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n    setFormErrors(newErrors);\n    setFormWarnings(newWarnings);\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati) {\n      newErrors.metri_posati = 'I metri posati sono obbligatori';\n    } else if (isNaN(formData.metri_posati) || parseFloat(formData.metri_posati) < 0) {\n      newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina) {\n      newErrors.id_bobina = 'La bobina è obbligatoria';\n    }\n    setFormErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Verifica la compatibilità tra cavo e bobina\n  const checkCompatibility = () => {\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      return true; // BOBINA_VUOTA è sempre compatibile\n    }\n    const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n    if (!selectedBobina) {\n      return false;\n    }\n\n    // Verifica compatibilità tipologia\n    const tipologiaCompatibile = selectedCavo.tipologia === selectedBobina.tipologia;\n\n    // Verifica compatibilità sezione\n    const sezioneCompatibile = String(selectedCavo.sezione) === String(selectedBobina.sezione);\n    return tipologiaCompatibile && sezioneCompatibile;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n    if (bobine.length === 0 && !bobineLoading) {\n      if (!formData.metri_posati || isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n        setFormErrors({\n          ...formErrors,\n          metri_posati: 'I metri posati sono obbligatori e devono essere maggiori di zero'\n        });\n        return;\n      }\n\n      // Imposta BOBINA_VUOTA e procedi con il salvataggio\n      formData.id_bobina = 'BOBINA_VUOTA';\n    } else {\n      // Validazione completa\n      if (!validateForm()) {\n        return;\n      }\n\n      // Verifica compatibilità\n      if (!checkCompatibility()) {\n        // Mostra dialog per incompatibilità\n        const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        setIncompatibleReelData({\n          cavo: selectedCavo,\n          bobina: selectedBobina\n        });\n        setShowIncompatibleReelDialog(true);\n        return;\n      }\n    }\n\n    // Procedi con il salvataggio\n    try {\n      setSaving(true);\n\n      // Converti metri posati in numero\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', formData.id_bobina);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, formData.id_bobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Metri posati aggiornati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce l'aggiornamento del cavo per compatibilità\n  const handleUpdateCavoForCompatibility = async () => {\n    try {\n      setSaving(true);\n      setShowIncompatibleReelDialog(false);\n      const {\n        cavo,\n        bobina\n      } = incompatibleReelData;\n\n      // Aggiorna il cavo per renderlo compatibile con la bobina\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, {\n        id_bobina: bobina.id_bobina,\n        tipologia: bobina.tipologia,\n        sezione: bobina.sezione\n      });\n\n      // Procedi con l'aggiornamento dei metri posati\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, parseFloat(formData.metri_posati), formData.id_bobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Cavo aggiornato e metri posati registrati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento del cavo:', error);\n      onError('Errore durante l\\'aggiornamento del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce la selezione di un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/posa/modifica-bobina?cavoId=${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Stato per le bobine compatibili\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n\n  // Aggiorna le bobine compatibili quando viene selezionato un cavo o cambiano le bobine disponibili\n  useEffect(() => {\n    if (selectedCavo) {\n      // Filtra le bobine compatibili localmente\n      const filtered = filterCompatibleBobine(selectedCavo);\n      setCompatibleBobine(filtered);\n    } else {\n      setCompatibleBobine([]);\n    }\n  }, [selectedCavo, bobine]);\n\n  // Filtra le bobine compatibili localmente\n  const filterCompatibleBobine = cavo => {\n    if (!cavo) return [];\n    console.log('Filtrando bobine compatibili per cavo:', cavo);\n    console.log('Bobine disponibili:', bobine);\n    console.log('Numero di bobine disponibili:', bobine.length);\n\n    // Verifica se ci sono bobine disponibili\n    if (bobine.length === 0) {\n      console.log('ATTENZIONE: Nessuna bobina disponibile nel cantiere!');\n      return [];\n    }\n\n    // Filtra le bobine compatibili con il cavo\n    const filtered = bobine.filter(bobina => {\n      // Normalizza i valori per un confronto più robusto\n      // 1. Normalizza tipologia (trim e lowercase)\n      // 2. Normalizza sezione (trim e conversione in stringa)\n      // 3. Verifica stato bobina e metri residui\n      const cavoTipologiaNorm = String(cavo.tipologia || '').trim().toLowerCase();\n      const cavoSezioneNorm = String(cavo.sezione || '').trim();\n      const bobinaTipologiaNorm = String(bobina.tipologia || '').trim().toLowerCase();\n      const bobinaSezioneNorm = String(bobina.sezione || '').trim();\n\n      // Verifica compatibilità\n      const tipologiaMatch = bobinaTipologiaNorm === cavoTipologiaNorm;\n      const sezioneMatch = bobinaSezioneNorm === cavoSezioneNorm;\n\n      // Verifica stato bobina direttamente invece di usare determineReelState\n      // Questo è più affidabile e corrisponde alla logica del backend\n      const stateOk = bobina.stato_bobina !== 'Terminata' && bobina.stato_bobina !== 'Over';\n\n      // Verifica che i metri residui siano positivi\n      const metriOk = bobina.metri_residui > 0;\n      const isCompatible = tipologiaMatch && sezioneMatch && stateOk && metriOk;\n\n      // Log dettagliati per debug\n      console.log(`Confronto dettagliato per bobina ${bobina.id_bobina}:`);\n      console.log(`- Tipologia bobina (originale): \"${bobina.tipologia}\", tipo: ${typeof bobina.tipologia}`);\n      console.log(`- Tipologia cavo (originale): \"${cavo.tipologia}\", tipo: ${typeof cavo.tipologia}`);\n      console.log(`- Tipologia bobina (normalizzata): \"${bobinaTipologiaNorm}\"`);\n      console.log(`- Tipologia cavo (normalizzata): \"${cavoTipologiaNorm}\"`);\n      console.log(`- Sezione bobina (originale): \"${bobina.sezione}\", tipo: ${typeof bobina.sezione}`);\n      console.log(`- Sezione cavo (originale): \"${cavo.sezione}\", tipo: ${typeof cavo.sezione}`);\n      console.log(`- Sezione bobina (normalizzata): \"${bobinaSezioneNorm}\"`);\n      console.log(`- Sezione cavo (normalizzata): \"${cavoSezioneNorm}\"`);\n      console.log(`- Stato bobina: ${bobina.stato_bobina}`);\n      console.log(`- Metri residui: ${bobina.metri_residui}`);\n      console.log(`- Stato OK? ${stateOk}`);\n      console.log(`- Metri OK? ${metriOk}`);\n\n      // Log di riepilogo\n      console.log(`Bobina ${bobina.id_bobina}:`, {\n        'Tipologia bobina': `\"${bobina.tipologia}\"`,\n        'Tipologia cavo': `\"${cavo.tipologia}\"`,\n        'Tipologie uguali?': tipologiaMatch,\n        'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n        'Sezione cavo': `\"${String(cavo.sezione)}\"`,\n        'Sezioni uguali?': sezioneMatch,\n        'Stato bobina': bobina.stato_bobina,\n        'Metri residui': bobina.metri_residui,\n        'Stato OK?': stateOk,\n        'Metri OK?': metriOk,\n        'Compatibile?': isCompatible\n      });\n      return isCompatible;\n    });\n    console.log('Bobine compatibili trovate:', filtered.length);\n    if (filtered.length > 0) {\n      console.log('Prima bobina compatibile:', filtered[0]);\n    } else {\n      console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n    }\n    return filtered;\n  };\n\n  // Funzione di utilità per ottenere le bobine compatibili (usata nel rendering)\n  const getCompatibleBobine = () => {\n    return compatibleBobine;\n  };\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    if (caviLoading) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 9\n      }, this);\n    }\n    if (cavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          my: 2\n        },\n        children: \"Nessun cavo disponibile per questo cantiere.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 2\n        },\n        children: \"Seleziona un cavo dalla tabella per inserire i metri posati. I cavi gi\\xE0 installati sono disabilitati.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                bgcolor: '#e3f2fd'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => {\n              const isInstalled = isCableInstalled(cavo);\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  bgcolor: isInstalled ? '#f5f5f5' : 'inherit',\n                  '&:hover': {\n                    bgcolor: isInstalled ? '#f5f5f5' : '#f1f8e9'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 32\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 71\n                  }, this), \"A: \", cavo.ubicazione_arrivo || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [cavo.metri_teorici || 'N/A', \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: cavo.stato_installazione || 'N/D',\n                    size: \"small\",\n                    color: getCableStateColor(cavo.stato_installazione),\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    variant: \"contained\",\n                    color: \"primary\",\n                    onClick: () => handleCavoSelect(cavo),\n                    disabled: isInstalled,\n                    children: isInstalled ? 'Già installato' : 'Seleziona'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 582,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 581,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 546,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza il form per inserimento metri e selezione bobina\n  const renderForm = () => {\n    if (!selectedCavo) return null;\n\n    // Log per debug - verifica le bobine compatibili nel rendering\n    const compatibleBobineList = getCompatibleBobine();\n    console.log('Rendering form - Bobine compatibili:', compatibleBobineList);\n    console.log('Rendering form - Numero di bobine compatibili:', compatibleBobineList.length);\n\n    // Verifica se ci sono bobine disponibili\n    if (bobine.length === 0 && !bobineLoading) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Inserimento metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 3\n          },\n          children: \"Non ci sono bobine disponibili nel cantiere. Puoi comunque registrare i metri posati utilizzando l'opzione \\\"BOBINA VUOTA\\\".\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 630,\n                  columnNumber: 19\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Formazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 19\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 19\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Metri posati\",\n              name: \"metri_posati\",\n              value: formData.metri_posati,\n              onChange: handleInputChange,\n              type: \"number\",\n              InputProps: {\n                inputProps: {\n                  min: 0,\n                  step: 0.1\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                name: \"id_bobina\",\n                value: \"BOBINA_VUOTA\",\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"BOBINA_VUOTA\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: '#2e7d32',\n                    bgcolor: '#f1f8e9'\n                  },\n                  children: \"BOBINA VUOTA (Cavo posato senza bobina)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                sx: {\n                  mt: 1\n                },\n                children: \"Non ci sono bobine disponibili. Verr\\xE0 utilizzata l'opzione BOBINA VUOTA.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            display: 'flex',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"secondary\",\n            onClick: () => {\n              setSelectedCavo(null);\n              setFormData({\n                id_cavo: '',\n                metri_posati: '',\n                id_bobina: ''\n              });\n            },\n            disabled: saving,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: () => handleSave(),\n            disabled: saving || !formData.metri_posati,\n            children: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 25\n            }, this) : 'Salva'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 695,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 614,\n        columnNumber: 9\n      }, this);\n    }\n    const compatibleBobine = getCompatibleBobine();\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserimento metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 712,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 3\n        },\n        children: [\"Inserisci i metri posati per il cavo selezionato e associa una bobina. Se il cavo \\xE8 stato posato senza una bobina specifica, seleziona \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"BOBINA VUOTA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 717,\n          columnNumber: 146\n        }, this), \".\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 716,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3,\n          p: 2,\n          bgcolor: '#f5f5f5',\n          borderRadius: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          color: \"primary\",\n          children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 721,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Formazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione partenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione arrivo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 747,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: selectedCavo.stato_installazione || 'N/D',\n                size: \"small\",\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                variant: \"outlined\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 720,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 765,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Metri posati\",\n            name: \"metri_posati\",\n            value: formData.metri_posati,\n            onChange: handleInputChange,\n            type: \"number\",\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: 'orange'\n              },\n              children: formWarnings.metri_posati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 778,\n              columnNumber: 17\n            }, this),\n            disabled: saving,\n            InputProps: {\n              inputProps: {\n                min: 0,\n                step: 0.1\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 768,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            error: !!formErrors.id_bobina,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              name: \"id_bobina\",\n              value: formData.id_bobina,\n              onChange: handleInputChange,\n              disabled: saving || bobineLoading,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BOBINA_VUOTA\",\n                sx: {\n                  fontWeight: 'bold',\n                  color: '#2e7d32',\n                  bgcolor: '#f1f8e9'\n                },\n                children: \"BOBINA VUOTA (Cavo posato senza bobina)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 796,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 17\n              }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 20,\n                    sx: {\n                      mr: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 807,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    children: \"Caricamento bobine...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 808,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 19\n              }, this) : compatibleBobine.length === 0 ? /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: \"Nessuna bobina compatibile disponibile. Utilizzare BOBINA VUOTA.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: [\"Bobine compatibili (\", compatibleBobine.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 821,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 19\n              }, this), !bobineLoading && (() => {\n                console.log('Rendering Select - Bobine compatibili:', compatibleBobine);\n                console.log('Rendering Select - Numero di bobine compatibili:', compatibleBobine.length);\n                return compatibleBobine.map(bobina => {\n                  console.log('Rendering bobina compatibile:', bobina);\n                  return /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: bobina.id_bobina,\n                    children: [bobina.id_bobina, \" - \", bobina.tipologia, \" - \", bobina.metri_residui, \"m\"]\n                  }, bobina.id_bobina, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 835,\n                    columnNumber: 23\n                  }, this);\n                });\n              })(), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: '#ff9800'\n                  },\n                  children: \"TUTTE LE BOBINE DISPONIBILI (Ignora compatibilit\\xE0)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 847,\n                columnNumber: 17\n              }, this), bobine.filter(bobina => bobina.stato_bobina !== 'Terminata').map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: bobina.id_bobina,\n                children: [bobina.id_bobina, \" - \", bobina.tipologia, \" - \", bobina.sezione, \" - \", bobina.metri_residui, \"m\"]\n              }, bobina.id_bobina, true, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 15\n            }, this), formErrors.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"error\",\n              children: formErrors.id_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 17\n            }, this), formWarnings.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'orange'\n              },\n              children: formWarnings.id_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              sx: {\n                mt: 1\n              },\n              children: \"Seleziona una bobina o usa BOBINA VUOTA se il cavo \\xE8 stato posato senza una bobina specifica.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 786,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 767,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          onClick: () => {\n            setSelectedCavo(null);\n            setFormData({\n              id_cavo: '',\n              metri_posati: '',\n              id_bobina: ''\n            });\n          },\n          disabled: saving,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 879,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSave,\n          disabled: saving || Object.keys(formErrors).length > 0,\n          children: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 23\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 894,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 878,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 711,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [!selectedCavo && renderCaviTable(), renderForm(), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: () => setShowIncompatibleReelDialog(false),\n      cavo: incompatibleReelData.cavo,\n      bobina: incompatibleReelData.bobina,\n      onUpdateCavo: handleUpdateCavoForCompatibility,\n      onSelectAnotherReel: () => {\n        setShowIncompatibleReelDialog(false);\n        setFormData(prev => ({\n          ...prev,\n          id_bobina: ''\n        }));\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 916,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Cavo gi\\xE0 posato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 930,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"Il cavo \", alreadyLaidCavo === null || alreadyLaidCavo === void 0 ? void 0 : alreadyLaidCavo.id_cavo, \" \\xE8 gi\\xE0 stato posato.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 932,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            gutterBottom: true,\n            children: \"Puoi:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"ul\",\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 940,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 941,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 942,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 939,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 935,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 931,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 947,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 951,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 954,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 950,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 946,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 929,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 908,\n    columnNumber: 5\n  }, this);\n};\n_s(MetriPosatiSemplificatoForm, \"tIpFGuuJh3Bk4ST3cUZrN28YjYA=\", false, function () {\n  return [useNavigate];\n});\n_c = MetriPosatiSemplificatoForm;\nexport default MetriPosatiSemplificatoForm;\nvar _c;\n$RefreshReg$(_c, \"MetriPosatiSemplificatoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "CircularProgress", "<PERSON><PERSON>", "Chip", "Divider", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "DialogContentText", "useNavigate", "caviService", "parcoCaviService", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "redirectToVisualizzaCavi", "IncompatibleReelDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MetriPosatiSemplificatoForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "saving", "setSaving", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReelData", "setIncompatibleReelData", "cavo", "bobina", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "alreadyLaidCavo", "setAlreadyLaidCavo", "loadCavi", "loadBobine", "caviData", "get<PERSON><PERSON>", "caviAttivi", "filter", "error", "console", "message", "log", "bobine<PERSON><PERSON>", "getBobine", "for<PERSON>ach", "tipologia", "sezione", "metri_residui", "stato_bobina", "length", "handleCavoSelect", "metri_te<PERSON>ci", "stato_installazione", "handleInputChange", "e", "name", "value", "target", "prev", "validateField", "newErrors", "newWarnings", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "find", "b", "validateForm", "Object", "keys", "checkCompatibility", "tipologiaCompatibile", "sezioneCompatibile", "String", "handleSave", "updateMetri<PERSON><PERSON><PERSON>", "handleUpdateCavoForCompatibility", "updateCavoForCompatibility", "handleCloseAlreadyLaidDialog", "handleSelectAnotherCable", "handleModifyReel", "compatibleBobine", "setCompatibleBobine", "filtered", "filterCompatibleBobine", "cavoTipologiaNorm", "trim", "toLowerCase", "cavoSezioneNorm", "bobinaTipologiaNorm", "bobinaSezioneNorm", "tipologiaMatch", "sezioneMatch", "stateOk", "metriOk", "isCompatible", "getCompatibleBobine", "renderCaviTable", "sx", "display", "justifyContent", "my", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "mb", "component", "size", "bgcolor", "map", "isInstalled", "ubicazione_partenza", "ubicazione_arrivo", "label", "color", "variant", "onClick", "disabled", "renderForm", "compatibleBobineList", "p", "gutterBottom", "borderRadius", "fontWeight", "container", "spacing", "item", "xs", "md", "fullWidth", "onChange", "type", "InputProps", "inputProps", "min", "step", "mt", "ml", "helperText", "style", "alignItems", "mr", "open", "onClose", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/MetriPosatiSemplificatoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  TextField,\n  Button,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  CircularProgress,\n  Alert,\n  Chip,\n  Divider,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  DialogContentText\n} from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione ultra-semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst MetriPosatiSemplificatoForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per il caricamento\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n\n  // Carica la lista dei cavi e delle bobine all'avvio\n  useEffect(() => {\n    loadCavi();\n    loadBobine();\n  }, [cantiereId]);\n\n  // Carica la lista dei cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi che non sono SPARE\n      const caviAttivi = caviData.filter(cavo => !isCableSpare(cavo));\n\n      setCavi(caviAttivi);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Carica la lista delle bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine per cantiere:', cantiereId);\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log('Bobine caricate:', bobineData);\n      console.log('Dettaglio bobine:');\n      bobineData.forEach(bobina => {\n        console.log(`Bobina ${bobina.id_bobina}:`, {\n          tipologia: bobina.tipologia,\n          sezione: bobina.sezione,\n          metri_residui: bobina.metri_residui,\n          stato_bobina: bobina.stato_bobina\n        });\n      });\n      console.log('IMPORTANTE: Impostazione delle bobine nello stato...');\n      setBobine(bobineData);\n      console.log('Bobine impostate nello stato:', bobineData.length);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    console.log('Cavo selezionato:', cavo);\n    console.log('Dettaglio cavo:', {\n      id_cavo: cavo.id_cavo,\n      tipologia: cavo.tipologia,\n      sezione: cavo.sezione,\n      metri_teorici: cavo.metri_teorici,\n      stato_installazione: cavo.stato_installazione\n    });\n\n    // Verifica se il cavo è già posato\n    if (isCableInstalled(cavo)) {\n      console.log('Cavo già posato, mostro dialog');\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n\n    setSelectedCavo(cavo);\n    setFormData({\n      id_cavo: cavo.id_cavo,\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n\n    // Log per debug - verifica se ci sono bobine compatibili\n    console.log('Verifica bobine compatibili per il cavo selezionato...');\n  };\n\n  // Gestisce la modifica dei campi del form\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un singolo campo\n  const validateField = (name, value) => {\n    const newErrors = { ...formErrors };\n    const newWarnings = { ...formWarnings };\n\n    if (name === 'metri_posati') {\n      // Validazione metri posati\n      if (value === '') {\n        newErrors.metri_posati = 'I metri posati sono obbligatori';\n      } else if (isNaN(value) || parseFloat(value) < 0) {\n        newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n      } else {\n        delete newErrors.metri_posati;\n\n        // Avvisi sui metri posati\n        const metriPosati = parseFloat(value);\n        if (selectedCavo && metriPosati > selectedCavo.metri_teorici) {\n          newWarnings.metri_posati = `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`;\n        } else {\n          delete newWarnings.metri_posati;\n        }\n\n        // Avvisi sulla bobina selezionata\n        if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n          const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n\n    if (name === 'id_bobina') {\n      // Validazione bobina\n      if (value === '') {\n        newErrors.id_bobina = 'La bobina è obbligatoria';\n      } else {\n        delete newErrors.id_bobina;\n\n        // Avvisi sulla bobina selezionata\n        if (value !== 'BOBINA_VUOTA' && formData.metri_posati) {\n          const metriPosati = parseFloat(formData.metri_posati);\n          const selectedBobina = bobine.find(b => b.id_bobina === value);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n\n    setFormErrors(newErrors);\n    setFormWarnings(newWarnings);\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati) {\n      newErrors.metri_posati = 'I metri posati sono obbligatori';\n    } else if (isNaN(formData.metri_posati) || parseFloat(formData.metri_posati) < 0) {\n      newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina) {\n      newErrors.id_bobina = 'La bobina è obbligatoria';\n    }\n\n    setFormErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Verifica la compatibilità tra cavo e bobina\n  const checkCompatibility = () => {\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      return true; // BOBINA_VUOTA è sempre compatibile\n    }\n\n    const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n    if (!selectedBobina) {\n      return false;\n    }\n\n    // Verifica compatibilità tipologia\n    const tipologiaCompatibile = selectedCavo.tipologia === selectedBobina.tipologia;\n\n    // Verifica compatibilità sezione\n    const sezioneCompatibile = String(selectedCavo.sezione) === String(selectedBobina.sezione);\n\n    return tipologiaCompatibile && sezioneCompatibile;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n    if (bobine.length === 0 && !bobineLoading) {\n      if (!formData.metri_posati || isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n        setFormErrors({\n          ...formErrors,\n          metri_posati: 'I metri posati sono obbligatori e devono essere maggiori di zero'\n        });\n        return;\n      }\n\n      // Imposta BOBINA_VUOTA e procedi con il salvataggio\n      formData.id_bobina = 'BOBINA_VUOTA';\n    } else {\n      // Validazione completa\n      if (!validateForm()) {\n        return;\n      }\n\n      // Verifica compatibilità\n      if (!checkCompatibility()) {\n        // Mostra dialog per incompatibilità\n        const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        setIncompatibleReelData({\n          cavo: selectedCavo,\n          bobina: selectedBobina\n        });\n        setShowIncompatibleReelDialog(true);\n        return;\n      }\n    }\n\n    // Procedi con il salvataggio\n    try {\n      setSaving(true);\n\n      // Converti metri posati in numero\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', formData.id_bobina);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        formData.id_bobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Metri posati aggiornati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce l'aggiornamento del cavo per compatibilità\n  const handleUpdateCavoForCompatibility = async () => {\n    try {\n      setSaving(true);\n      setShowIncompatibleReelDialog(false);\n\n      const { cavo, bobina } = incompatibleReelData;\n\n      // Aggiorna il cavo per renderlo compatibile con la bobina\n      await caviService.updateCavoForCompatibility(\n        cantiereId,\n        cavo.id_cavo,\n        {\n          id_bobina: bobina.id_bobina,\n          tipologia: bobina.tipologia,\n          sezione: bobina.sezione\n        }\n      );\n\n      // Procedi con l'aggiornamento dei metri posati\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        parseFloat(formData.metri_posati),\n        formData.id_bobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Cavo aggiornato e metri posati registrati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento del cavo:', error);\n      onError('Errore durante l\\'aggiornamento del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce la selezione di un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/posa/modifica-bobina?cavoId=${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Stato per le bobine compatibili\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n\n  // Aggiorna le bobine compatibili quando viene selezionato un cavo o cambiano le bobine disponibili\n  useEffect(() => {\n    if (selectedCavo) {\n      // Filtra le bobine compatibili localmente\n      const filtered = filterCompatibleBobine(selectedCavo);\n      setCompatibleBobine(filtered);\n    } else {\n      setCompatibleBobine([]);\n    }\n  }, [selectedCavo, bobine]);\n\n  // Filtra le bobine compatibili localmente\n  const filterCompatibleBobine = (cavo) => {\n    if (!cavo) return [];\n\n    console.log('Filtrando bobine compatibili per cavo:', cavo);\n    console.log('Bobine disponibili:', bobine);\n    console.log('Numero di bobine disponibili:', bobine.length);\n\n    // Verifica se ci sono bobine disponibili\n    if (bobine.length === 0) {\n      console.log('ATTENZIONE: Nessuna bobina disponibile nel cantiere!');\n      return [];\n    }\n\n    // Filtra le bobine compatibili con il cavo\n    const filtered = bobine.filter(bobina => {\n      // Normalizza i valori per un confronto più robusto\n      // 1. Normalizza tipologia (trim e lowercase)\n      // 2. Normalizza sezione (trim e conversione in stringa)\n      // 3. Verifica stato bobina e metri residui\n      const cavoTipologiaNorm = String(cavo.tipologia || '').trim().toLowerCase();\n      const cavoSezioneNorm = String(cavo.sezione || '').trim();\n\n      const bobinaTipologiaNorm = String(bobina.tipologia || '').trim().toLowerCase();\n      const bobinaSezioneNorm = String(bobina.sezione || '').trim();\n\n      // Verifica compatibilità\n      const tipologiaMatch = bobinaTipologiaNorm === cavoTipologiaNorm;\n      const sezioneMatch = bobinaSezioneNorm === cavoSezioneNorm;\n\n      // Verifica stato bobina direttamente invece di usare determineReelState\n      // Questo è più affidabile e corrisponde alla logica del backend\n      const stateOk = bobina.stato_bobina !== 'Terminata' && bobina.stato_bobina !== 'Over';\n\n      // Verifica che i metri residui siano positivi\n      const metriOk = bobina.metri_residui > 0;\n\n      const isCompatible = tipologiaMatch && sezioneMatch && stateOk && metriOk;\n\n      // Log dettagliati per debug\n      console.log(`Confronto dettagliato per bobina ${bobina.id_bobina}:`);\n      console.log(`- Tipologia bobina (originale): \"${bobina.tipologia}\", tipo: ${typeof bobina.tipologia}`);\n      console.log(`- Tipologia cavo (originale): \"${cavo.tipologia}\", tipo: ${typeof cavo.tipologia}`);\n      console.log(`- Tipologia bobina (normalizzata): \"${bobinaTipologiaNorm}\"`);\n      console.log(`- Tipologia cavo (normalizzata): \"${cavoTipologiaNorm}\"`);\n      console.log(`- Sezione bobina (originale): \"${bobina.sezione}\", tipo: ${typeof bobina.sezione}`);\n      console.log(`- Sezione cavo (originale): \"${cavo.sezione}\", tipo: ${typeof cavo.sezione}`);\n      console.log(`- Sezione bobina (normalizzata): \"${bobinaSezioneNorm}\"`);\n      console.log(`- Sezione cavo (normalizzata): \"${cavoSezioneNorm}\"`);\n      console.log(`- Stato bobina: ${bobina.stato_bobina}`);\n      console.log(`- Metri residui: ${bobina.metri_residui}`);\n      console.log(`- Stato OK? ${stateOk}`);\n      console.log(`- Metri OK? ${metriOk}`);\n\n      // Log di riepilogo\n      console.log(`Bobina ${bobina.id_bobina}:`, {\n        'Tipologia bobina': `\"${bobina.tipologia}\"`,\n        'Tipologia cavo': `\"${cavo.tipologia}\"`,\n        'Tipologie uguali?': tipologiaMatch,\n        'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n        'Sezione cavo': `\"${String(cavo.sezione)}\"`,\n        'Sezioni uguali?': sezioneMatch,\n        'Stato bobina': bobina.stato_bobina,\n        'Metri residui': bobina.metri_residui,\n        'Stato OK?': stateOk,\n        'Metri OK?': metriOk,\n        'Compatibile?': isCompatible\n      });\n\n      return isCompatible;\n    });\n\n    console.log('Bobine compatibili trovate:', filtered.length);\n    if (filtered.length > 0) {\n      console.log('Prima bobina compatibile:', filtered[0]);\n    } else {\n      console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n    }\n\n    return filtered;\n  };\n\n  // Funzione di utilità per ottenere le bobine compatibili (usata nel rendering)\n  const getCompatibleBobine = () => {\n    return compatibleBobine;\n  };\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    if (caviLoading) {\n      return (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      );\n    }\n\n    if (cavi.length === 0) {\n      return (\n        <Alert severity=\"info\" sx={{ my: 2 }}>\n          Nessun cavo disponibile per questo cantiere.\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          Seleziona un cavo dalla tabella per inserire i metri posati. I cavi già installati sono disabilitati.\n        </Alert>\n\n        <TableContainer component={Paper} sx={{ mb: 3 }}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow sx={{ bgcolor: '#e3f2fd' }}>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Ubicazione</TableCell>\n                <TableCell>Metri Teorici</TableCell>\n                <TableCell>Stato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {cavi.map((cavo) => {\n                const isInstalled = isCableInstalled(cavo);\n                return (\n                  <TableRow\n                    key={cavo.id_cavo}\n                    sx={{\n                      bgcolor: isInstalled ? '#f5f5f5' : 'inherit',\n                      '&:hover': { bgcolor: isInstalled ? '#f5f5f5' : '#f1f8e9' }\n                    }}\n                  >\n                    <TableCell><strong>{cavo.id_cavo}</strong></TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={cavo.stato_installazione || 'N/D'}\n                        size=\"small\"\n                        color={getCableStateColor(cavo.stato_installazione)}\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Button\n                        size=\"small\"\n                        variant=\"contained\"\n                        color=\"primary\"\n                        onClick={() => handleCavoSelect(cavo)}\n                        disabled={isInstalled}\n                      >\n                        {isInstalled ? 'Già installato' : 'Seleziona'}\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </>\n    );\n  };\n\n  // Renderizza il form per inserimento metri e selezione bobina\n  const renderForm = () => {\n    if (!selectedCavo) return null;\n\n    // Log per debug - verifica le bobine compatibili nel rendering\n    const compatibleBobineList = getCompatibleBobine();\n    console.log('Rendering form - Bobine compatibili:', compatibleBobineList);\n    console.log('Rendering form - Numero di bobine compatibili:', compatibleBobineList.length);\n\n    // Verifica se ci sono bobine disponibili\n    if (bobine.length === 0 && !bobineLoading) {\n      return (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Inserimento metri posati\n          </Typography>\n\n          <Alert severity=\"warning\" sx={{ mb: 3 }}>\n            Non ci sono bobine disponibili nel cantiere. Puoi comunque registrare i metri posati utilizzando l'opzione \"BOBINA VUOTA\".\n          </Alert>\n\n          <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle1\" gutterBottom fontWeight=\"bold\" color=\"primary\">\n              Cavo selezionato: {selectedCavo.id_cavo}\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={4}>\n                <Typography variant=\"body2\">\n                  <strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={4}>\n                <Typography variant=\"body2\">\n                  <strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={4}>\n                <Typography variant=\"body2\">\n                  <strong>Metri teorici:</strong> {selectedCavo.metri_teorici || 'N/A'} m\n                </Typography>\n              </Grid>\n            </Grid>\n          </Box>\n\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Metri posati\"\n                name=\"metri_posati\"\n                value={formData.metri_posati}\n                onChange={handleInputChange}\n                type=\"number\"\n                InputProps={{\n                  inputProps: { min: 0, step: 0.1 }\n                }}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth>\n                <InputLabel>Bobina</InputLabel>\n                <Select\n                  name=\"id_bobina\"\n                  value=\"BOBINA_VUOTA\"\n                  disabled\n                >\n                  <MenuItem value=\"BOBINA_VUOTA\" sx={{ fontWeight: 'bold', color: '#2e7d32', bgcolor: '#f1f8e9' }}>\n                    BOBINA VUOTA (Cavo posato senza bobina)\n                  </MenuItem>\n                </Select>\n                <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                  Non ci sono bobine disponibili. Verrà utilizzata l'opzione BOBINA VUOTA.\n                </Typography>\n              </FormControl>\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>\n            <Button\n              variant=\"outlined\"\n              color=\"secondary\"\n              onClick={() => {\n                setSelectedCavo(null);\n                setFormData({\n                  id_cavo: '',\n                  metri_posati: '',\n                  id_bobina: ''\n                });\n              }}\n              disabled={saving}\n            >\n              Annulla\n            </Button>\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={() => handleSave()}\n              disabled={saving || !formData.metri_posati}\n            >\n              {saving ? <CircularProgress size={24} /> : 'Salva'}\n            </Button>\n          </Box>\n        </Paper>\n      );\n    }\n\n    const compatibleBobine = getCompatibleBobine();\n\n    return (\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserimento metri posati\n        </Typography>\n\n        <Alert severity=\"info\" sx={{ mb: 3 }}>\n          Inserisci i metri posati per il cavo selezionato e associa una bobina. Se il cavo è stato posato senza una bobina specifica, seleziona <strong>BOBINA VUOTA</strong>.\n        </Alert>\n\n        <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n          <Typography variant=\"subtitle1\" gutterBottom fontWeight=\"bold\" color=\"primary\">\n            Cavo selezionato: {selectedCavo.id_cavo}\n          </Typography>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"body2\">\n                <strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"body2\">\n                <strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"body2\">\n                <strong>Metri teorici:</strong> {selectedCavo.metri_teorici || 'N/A'} m\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body2\">\n                <strong>Ubicazione partenza:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body2\">\n                <strong>Ubicazione arrivo:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12}>\n              <Typography variant=\"body2\">\n                <strong>Stato:</strong>\n                <Chip\n                  label={selectedCavo.stato_installazione || 'N/D'}\n                  size=\"small\"\n                  color={getCableStateColor(selectedCavo.stato_installazione)}\n                  variant=\"outlined\"\n                  sx={{ ml: 1 }}\n                />\n              </Typography>\n            </Grid>\n          </Grid>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={6}>\n            <TextField\n              fullWidth\n              label=\"Metri posati\"\n              name=\"metri_posati\"\n              value={formData.metri_posati}\n              onChange={handleInputChange}\n              type=\"number\"\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || (formWarnings.metri_posati && (\n                <span style={{ color: 'orange' }}>{formWarnings.metri_posati}</span>\n              ))}\n              disabled={saving}\n              InputProps={{\n                inputProps: { min: 0, step: 0.1 }\n              }}\n            />\n          </Grid>\n          <Grid item xs={12} md={6}>\n            <FormControl fullWidth error={!!formErrors.id_bobina}>\n              <InputLabel>Bobina</InputLabel>\n              <Select\n                name=\"id_bobina\"\n                value={formData.id_bobina}\n                onChange={handleInputChange}\n                disabled={saving || bobineLoading}\n              >\n                {/* Opzione BOBINA VUOTA sempre disponibile e in evidenza */}\n                <MenuItem value=\"BOBINA_VUOTA\" sx={{ fontWeight: 'bold', color: '#2e7d32', bgcolor: '#f1f8e9' }}>\n                  BOBINA VUOTA (Cavo posato senza bobina)\n                </MenuItem>\n\n                {/* Separatore */}\n                <Divider />\n\n                {/* Messaggio informativo */}\n                {bobineLoading ? (\n                  <MenuItem disabled>\n                    <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                      <CircularProgress size={20} sx={{ mr: 1 }} />\n                      <Typography variant=\"caption\">\n                        Caricamento bobine...\n                      </Typography>\n                    </Box>\n                  </MenuItem>\n                ) : compatibleBobine.length === 0 ? (\n                  <MenuItem disabled>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Nessuna bobina compatibile disponibile. Utilizzare BOBINA VUOTA.\n                    </Typography>\n                  </MenuItem>\n                ) : (\n                  <MenuItem disabled>\n                    <Typography variant=\"caption\">\n                      Bobine compatibili ({compatibleBobine.length})\n                    </Typography>\n                  </MenuItem>\n                )}\n\n                {/* Bobine compatibili */}\n                {!bobineLoading && (() => {\n                  console.log('Rendering Select - Bobine compatibili:', compatibleBobine);\n                  console.log('Rendering Select - Numero di bobine compatibili:', compatibleBobine.length);\n\n                  return compatibleBobine.map((bobina) => {\n                    console.log('Rendering bobina compatibile:', bobina);\n                    return (\n                      <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                        {bobina.id_bobina} - {bobina.tipologia} - {bobina.metri_residui}m\n                      </MenuItem>\n                    );\n                  });\n                })()\n                }\n\n                {/* Separatore per tutte le bobine */}\n                <Divider />\n\n                {/* Titolo per tutte le bobine */}\n                <MenuItem disabled>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: '#ff9800' }}>\n                    TUTTE LE BOBINE DISPONIBILI (Ignora compatibilità)\n                  </Typography>\n                </MenuItem>\n\n                {/* Mostra tutte le bobine disponibili */}\n                {bobine.filter(bobina => bobina.stato_bobina !== 'Terminata').map((bobina) => (\n                  <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                    {bobina.id_bobina} - {bobina.tipologia} - {bobina.sezione} - {bobina.metri_residui}m\n                  </MenuItem>\n                ))}\n              </Select>\n              {formErrors.id_bobina && (\n                <Typography variant=\"caption\" color=\"error\">\n                  {formErrors.id_bobina}\n                </Typography>\n              )}\n              {formWarnings.id_bobina && (\n                <Typography variant=\"caption\" sx={{ color: 'orange' }}>\n                  {formWarnings.id_bobina}\n                </Typography>\n              )}\n              {/* Messaggio informativo sotto il campo */}\n              <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                Seleziona una bobina o usa BOBINA VUOTA se il cavo è stato posato senza una bobina specifica.\n              </Typography>\n            </FormControl>\n          </Grid>\n        </Grid>\n\n        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>\n          <Button\n            variant=\"outlined\"\n            color=\"secondary\"\n            onClick={() => {\n              setSelectedCavo(null);\n              setFormData({\n                id_cavo: '',\n                metri_posati: '',\n                id_bobina: ''\n              });\n            }}\n            disabled={saving}\n          >\n            Annulla\n          </Button>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={handleSave}\n            disabled={saving || Object.keys(formErrors).length > 0}\n          >\n            {saving ? <CircularProgress size={24} /> : 'Salva'}\n          </Button>\n        </Box>\n      </Paper>\n    );\n  };\n\n  return (\n    <Box>\n      {/* Tabella cavi */}\n      {!selectedCavo && renderCaviTable()}\n\n      {/* Form per inserimento metri e selezione bobina */}\n      {renderForm()}\n\n      {/* Dialog per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={() => setShowIncompatibleReelDialog(false)}\n        cavo={incompatibleReelData.cavo}\n        bobina={incompatibleReelData.bobina}\n        onUpdateCavo={handleUpdateCavoForCompatibility}\n        onSelectAnotherReel={() => {\n          setShowIncompatibleReelDialog(false);\n          setFormData(prev => ({ ...prev, id_bobina: '' }));\n        }}\n      />\n\n      {/* Dialog per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog}>\n        <DialogTitle>Cavo già posato</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Il cavo {alreadyLaidCavo?.id_cavo} è già stato posato.\n          </DialogContentText>\n          <Box sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" gutterBottom>\n              Puoi:\n            </Typography>\n            <Typography component=\"ul\" variant=\"body2\">\n              <li>Modificare la bobina associata</li>\n              <li>Selezionare un altro cavo</li>\n              <li>Annullare l'operazione</li>\n            </Typography>\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default MetriPosatiSemplificatoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,iBAAiB,QACZ,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,SAASC,wBAAwB,QAAQ,6BAA6B;AACtE,OAAOC,sBAAsB,MAAM,0BAA0B;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,2BAA2B,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACwB,IAAI,EAAEC,OAAO,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACqD,MAAM,EAAEC,SAAS,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC;IACvC2D,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoE,MAAM,EAAEC,SAAS,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAAC0E,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAAC4E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7E,QAAQ,CAAC;IAAE8E,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC;EAC9F,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACkF,eAAe,EAAEC,kBAAkB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACdmF,QAAQ,CAAC,CAAC;IACVC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACvC,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMsC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFnB,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMqB,QAAQ,GAAG,MAAM1D,WAAW,CAAC2D,OAAO,CAACzC,UAAU,CAAC;;MAEtD;MACA,MAAM0C,UAAU,GAAGF,QAAQ,CAACG,MAAM,CAACX,IAAI,IAAI,CAAC3C,YAAY,CAAC2C,IAAI,CAAC,CAAC;MAE/D1B,OAAO,CAACoC,UAAU,CAAC;IACrB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD1C,OAAO,CAAC,mCAAmC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACR3B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMoB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFlB,gBAAgB,CAAC,IAAI,CAAC;MACtBwB,OAAO,CAACE,GAAG,CAAC,kCAAkC,EAAE/C,UAAU,CAAC;MAC3D,MAAMgD,UAAU,GAAG,MAAMjE,gBAAgB,CAACkE,SAAS,CAACjD,UAAU,CAAC;MAC/D6C,OAAO,CAACE,GAAG,CAAC,kBAAkB,EAAEC,UAAU,CAAC;MAC3CH,OAAO,CAACE,GAAG,CAAC,mBAAmB,CAAC;MAChCC,UAAU,CAACE,OAAO,CAACjB,MAAM,IAAI;QAC3BY,OAAO,CAACE,GAAG,CAAC,UAAUd,MAAM,CAAClB,SAAS,GAAG,EAAE;UACzCoC,SAAS,EAAElB,MAAM,CAACkB,SAAS;UAC3BC,OAAO,EAAEnB,MAAM,CAACmB,OAAO;UACvBC,aAAa,EAAEpB,MAAM,CAACoB,aAAa;UACnCC,YAAY,EAAErB,MAAM,CAACqB;QACvB,CAAC,CAAC;MACJ,CAAC,CAAC;MACFT,OAAO,CAACE,GAAG,CAAC,sDAAsD,CAAC;MACnEvC,SAAS,CAACwC,UAAU,CAAC;MACrBH,OAAO,CAACE,GAAG,CAAC,+BAA+B,EAAEC,UAAU,CAACO,MAAM,CAAC;IACjE,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D1C,OAAO,CAAC,uCAAuC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRzB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMmC,gBAAgB,GAAIxB,IAAI,IAAK;IACjCa,OAAO,CAACE,GAAG,CAAC,mBAAmB,EAAEf,IAAI,CAAC;IACtCa,OAAO,CAACE,GAAG,CAAC,iBAAiB,EAAE;MAC7BlC,OAAO,EAAEmB,IAAI,CAACnB,OAAO;MACrBsC,SAAS,EAAEnB,IAAI,CAACmB,SAAS;MACzBC,OAAO,EAAEpB,IAAI,CAACoB,OAAO;MACrBK,aAAa,EAAEzB,IAAI,CAACyB,aAAa;MACjCC,mBAAmB,EAAE1B,IAAI,CAAC0B;IAC5B,CAAC,CAAC;;IAEF;IACA,IAAIpE,gBAAgB,CAAC0C,IAAI,CAAC,EAAE;MAC1Ba,OAAO,CAACE,GAAG,CAAC,gCAAgC,CAAC;MAC7CV,kBAAkB,CAACL,IAAI,CAAC;MACxBG,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IAEAzB,eAAe,CAACsB,IAAI,CAAC;IACrBpB,WAAW,CAAC;MACVC,OAAO,EAAEmB,IAAI,CAACnB,OAAO;MACrBC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFU,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;;IAEnB;IACAkB,OAAO,CAACE,GAAG,CAAC,wDAAwD,CAAC;EACvE,CAAC;;EAED;EACA,MAAMY,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCnD,WAAW,CAACoD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACAG,aAAa,CAACJ,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMG,aAAa,GAAGA,CAACJ,IAAI,EAAEC,KAAK,KAAK;IACrC,MAAMI,SAAS,GAAG;MAAE,GAAG1C;IAAW,CAAC;IACnC,MAAM2C,WAAW,GAAG;MAAE,GAAGzC;IAAa,CAAC;IAEvC,IAAImC,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAIC,KAAK,KAAK,EAAE,EAAE;QAChBI,SAAS,CAACpD,YAAY,GAAG,iCAAiC;MAC5D,CAAC,MAAM,IAAIsD,KAAK,CAACN,KAAK,CAAC,IAAIO,UAAU,CAACP,KAAK,CAAC,GAAG,CAAC,EAAE;QAChDI,SAAS,CAACpD,YAAY,GAAG,iDAAiD;MAC5E,CAAC,MAAM;QACL,OAAOoD,SAAS,CAACpD,YAAY;;QAE7B;QACA,MAAMwD,WAAW,GAAGD,UAAU,CAACP,KAAK,CAAC;QACrC,IAAIrD,YAAY,IAAI6D,WAAW,GAAG7D,YAAY,CAACgD,aAAa,EAAE;UAC5DU,WAAW,CAACrD,YAAY,GAAG,mBAAmBwD,WAAW,+BAA+B7D,YAAY,CAACgD,aAAa,GAAG;QACvH,CAAC,MAAM;UACL,OAAOU,WAAW,CAACrD,YAAY;QACjC;;QAEA;QACA,IAAIH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;UAC/D,MAAMwD,cAAc,GAAGhE,MAAM,CAACiE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;UAC3E,IAAIwD,cAAc,IAAID,WAAW,GAAGC,cAAc,CAAClB,aAAa,EAAE;YAChEc,WAAW,CAACpD,SAAS,GAAG,mBAAmBuD,WAAW,4CAA4CC,cAAc,CAAClB,aAAa,GAAG;UACnI,CAAC,MAAM;YACL,OAAOc,WAAW,CAACpD,SAAS;UAC9B;QACF;MACF;IACF;IAEA,IAAI8C,IAAI,KAAK,WAAW,EAAE;MACxB;MACA,IAAIC,KAAK,KAAK,EAAE,EAAE;QAChBI,SAAS,CAACnD,SAAS,GAAG,0BAA0B;MAClD,CAAC,MAAM;QACL,OAAOmD,SAAS,CAACnD,SAAS;;QAE1B;QACA,IAAI+C,KAAK,KAAK,cAAc,IAAInD,QAAQ,CAACG,YAAY,EAAE;UACrD,MAAMwD,WAAW,GAAGD,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC;UACrD,MAAMyD,cAAc,GAAGhE,MAAM,CAACiE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1D,SAAS,KAAK+C,KAAK,CAAC;UAC9D,IAAIS,cAAc,IAAID,WAAW,GAAGC,cAAc,CAAClB,aAAa,EAAE;YAChEc,WAAW,CAACpD,SAAS,GAAG,mBAAmBuD,WAAW,4CAA4CC,cAAc,CAAClB,aAAa,GAAG;UACnI,CAAC,MAAM;YACL,OAAOc,WAAW,CAACpD,SAAS;UAC9B;QACF;MACF;IACF;IAEAU,aAAa,CAACyC,SAAS,CAAC;IACxBvC,eAAe,CAACwC,WAAW,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMR,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAACvD,QAAQ,CAACG,YAAY,EAAE;MAC1BoD,SAAS,CAACpD,YAAY,GAAG,iCAAiC;IAC5D,CAAC,MAAM,IAAIsD,KAAK,CAACzD,QAAQ,CAACG,YAAY,CAAC,IAAIuD,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC,GAAG,CAAC,EAAE;MAChFoD,SAAS,CAACpD,YAAY,GAAG,iDAAiD;IAC5E;;IAEA;IACA,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;MACvBmD,SAAS,CAACnD,SAAS,GAAG,0BAA0B;IAClD;IAEAU,aAAa,CAACyC,SAAS,CAAC;IACxB,OAAOS,MAAM,CAACC,IAAI,CAACV,SAAS,CAAC,CAACX,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMsB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIlE,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzC,OAAO,IAAI,CAAC,CAAC;IACf;IAEA,MAAMwD,cAAc,GAAGhE,MAAM,CAACiE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IAC3E,IAAI,CAACwD,cAAc,EAAE;MACnB,OAAO,KAAK;IACd;;IAEA;IACA,MAAMO,oBAAoB,GAAGrE,YAAY,CAAC0C,SAAS,KAAKoB,cAAc,CAACpB,SAAS;;IAEhF;IACA,MAAM4B,kBAAkB,GAAGC,MAAM,CAACvE,YAAY,CAAC2C,OAAO,CAAC,KAAK4B,MAAM,CAACT,cAAc,CAACnB,OAAO,CAAC;IAE1F,OAAO0B,oBAAoB,IAAIC,kBAAkB;EACnD,CAAC;;EAED;EACA,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B;IACA,IAAI1E,MAAM,CAACgD,MAAM,KAAK,CAAC,IAAI,CAACnC,aAAa,EAAE;MACzC,IAAI,CAACT,QAAQ,CAACG,YAAY,IAAIsD,KAAK,CAACC,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAIuD,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;QAChHW,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbV,YAAY,EAAE;QAChB,CAAC,CAAC;QACF;MACF;;MAEA;MACAH,QAAQ,CAACI,SAAS,GAAG,cAAc;IACrC,CAAC,MAAM;MACL;MACA,IAAI,CAAC2D,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;;MAEA;MACA,IAAI,CAACG,kBAAkB,CAAC,CAAC,EAAE;QACzB;QACA,MAAMN,cAAc,GAAGhE,MAAM,CAACiE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QAC3EgB,uBAAuB,CAAC;UACtBC,IAAI,EAAEvB,YAAY;UAClBwB,MAAM,EAAEsC;QACV,CAAC,CAAC;QACF1C,6BAA6B,CAAC,IAAI,CAAC;QACnC;MACF;IACF;;IAEA;IACA,IAAI;MACFN,SAAS,CAAC,IAAI,CAAC;;MAEf;MACA,MAAM+C,WAAW,GAAGD,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA+B,OAAO,CAACE,GAAG,CAAC,6DAA6D,CAAC;MAC1EF,OAAO,CAACE,GAAG,CAAC,eAAe,EAAE/C,UAAU,CAAC;MACxC6C,OAAO,CAACE,GAAG,CAAC,YAAY,EAAEpC,QAAQ,CAACE,OAAO,CAAC;MAC3CgC,OAAO,CAACE,GAAG,CAAC,iBAAiB,EAAEuB,WAAW,CAAC;MAC3CzB,OAAO,CAACE,GAAG,CAAC,cAAc,EAAEpC,QAAQ,CAACI,SAAS,CAAC;;MAE/C;MACA,MAAMjC,WAAW,CAACoG,iBAAiB,CACjClF,UAAU,EACVW,QAAQ,CAACE,OAAO,EAChByD,WAAW,EACX3D,QAAQ,CAACI,SAAS,EAClB,IAAI,CAAC;MACP,CAAC;;MAED;MACAd,SAAS,CAAC,sCAAsC,CAAC;;MAEjD;MACAS,eAAe,CAAC,IAAI,CAAC;MACrBE,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACAuB,QAAQ,CAAC,CAAC;MACVC,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD1C,OAAO,CAAC,iCAAiC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACtF,CAAC,SAAS;MACRvB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM4D,gCAAgC,GAAG,MAAAA,CAAA,KAAY;IACnD,IAAI;MACF5D,SAAS,CAAC,IAAI,CAAC;MACfM,6BAA6B,CAAC,KAAK,CAAC;MAEpC,MAAM;QAAEG,IAAI;QAAEC;MAAO,CAAC,GAAGH,oBAAoB;;MAE7C;MACA,MAAMhD,WAAW,CAACsG,0BAA0B,CAC1CpF,UAAU,EACVgC,IAAI,CAACnB,OAAO,EACZ;QACEE,SAAS,EAAEkB,MAAM,CAAClB,SAAS;QAC3BoC,SAAS,EAAElB,MAAM,CAACkB,SAAS;QAC3BC,OAAO,EAAEnB,MAAM,CAACmB;MAClB,CACF,CAAC;;MAED;MACA,MAAMtE,WAAW,CAACoG,iBAAiB,CACjClF,UAAU,EACVW,QAAQ,CAACE,OAAO,EAChBwD,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC,EACjCH,QAAQ,CAACI,SAAS,EAClB,IAAI,CAAC;MACP,CAAC;;MAED;MACAd,SAAS,CAAC,wDAAwD,CAAC;;MAEnE;MACAS,eAAe,CAAC,IAAI,CAAC;MACrBE,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACAuB,QAAQ,CAAC,CAAC;MACVC,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE1C,OAAO,CAAC,4CAA4C,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACjG,CAAC,SAAS;MACRvB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM8D,4BAA4B,GAAGA,CAAA,KAAM;IACzClD,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMiD,wBAAwB,GAAGA,CAAA,KAAM;IACrCD,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAInD,eAAe,EAAE;MACnBhC,QAAQ,CAAC,+CAA+CgC,eAAe,CAACvB,OAAO,EAAE,CAAC;IACpF;IACAwE,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAM,CAACG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvI,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACd,IAAIsD,YAAY,EAAE;MAChB;MACA,MAAMiF,QAAQ,GAAGC,sBAAsB,CAAClF,YAAY,CAAC;MACrDgF,mBAAmB,CAACC,QAAQ,CAAC;IAC/B,CAAC,MAAM;MACLD,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC,EAAE,CAAChF,YAAY,EAAEF,MAAM,CAAC,CAAC;;EAE1B;EACA,MAAMoF,sBAAsB,GAAI3D,IAAI,IAAK;IACvC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpBa,OAAO,CAACE,GAAG,CAAC,wCAAwC,EAAEf,IAAI,CAAC;IAC3Da,OAAO,CAACE,GAAG,CAAC,qBAAqB,EAAExC,MAAM,CAAC;IAC1CsC,OAAO,CAACE,GAAG,CAAC,+BAA+B,EAAExC,MAAM,CAACgD,MAAM,CAAC;;IAE3D;IACA,IAAIhD,MAAM,CAACgD,MAAM,KAAK,CAAC,EAAE;MACvBV,OAAO,CAACE,GAAG,CAAC,sDAAsD,CAAC;MACnE,OAAO,EAAE;IACX;;IAEA;IACA,MAAM2C,QAAQ,GAAGnF,MAAM,CAACoC,MAAM,CAACV,MAAM,IAAI;MACvC;MACA;MACA;MACA;MACA,MAAM2D,iBAAiB,GAAGZ,MAAM,CAAChD,IAAI,CAACmB,SAAS,IAAI,EAAE,CAAC,CAAC0C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC3E,MAAMC,eAAe,GAAGf,MAAM,CAAChD,IAAI,CAACoB,OAAO,IAAI,EAAE,CAAC,CAACyC,IAAI,CAAC,CAAC;MAEzD,MAAMG,mBAAmB,GAAGhB,MAAM,CAAC/C,MAAM,CAACkB,SAAS,IAAI,EAAE,CAAC,CAAC0C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC/E,MAAMG,iBAAiB,GAAGjB,MAAM,CAAC/C,MAAM,CAACmB,OAAO,IAAI,EAAE,CAAC,CAACyC,IAAI,CAAC,CAAC;;MAE7D;MACA,MAAMK,cAAc,GAAGF,mBAAmB,KAAKJ,iBAAiB;MAChE,MAAMO,YAAY,GAAGF,iBAAiB,KAAKF,eAAe;;MAE1D;MACA;MACA,MAAMK,OAAO,GAAGnE,MAAM,CAACqB,YAAY,KAAK,WAAW,IAAIrB,MAAM,CAACqB,YAAY,KAAK,MAAM;;MAErF;MACA,MAAM+C,OAAO,GAAGpE,MAAM,CAACoB,aAAa,GAAG,CAAC;MAExC,MAAMiD,YAAY,GAAGJ,cAAc,IAAIC,YAAY,IAAIC,OAAO,IAAIC,OAAO;;MAEzE;MACAxD,OAAO,CAACE,GAAG,CAAC,oCAAoCd,MAAM,CAAClB,SAAS,GAAG,CAAC;MACpE8B,OAAO,CAACE,GAAG,CAAC,oCAAoCd,MAAM,CAACkB,SAAS,YAAY,OAAOlB,MAAM,CAACkB,SAAS,EAAE,CAAC;MACtGN,OAAO,CAACE,GAAG,CAAC,kCAAkCf,IAAI,CAACmB,SAAS,YAAY,OAAOnB,IAAI,CAACmB,SAAS,EAAE,CAAC;MAChGN,OAAO,CAACE,GAAG,CAAC,uCAAuCiD,mBAAmB,GAAG,CAAC;MAC1EnD,OAAO,CAACE,GAAG,CAAC,qCAAqC6C,iBAAiB,GAAG,CAAC;MACtE/C,OAAO,CAACE,GAAG,CAAC,kCAAkCd,MAAM,CAACmB,OAAO,YAAY,OAAOnB,MAAM,CAACmB,OAAO,EAAE,CAAC;MAChGP,OAAO,CAACE,GAAG,CAAC,gCAAgCf,IAAI,CAACoB,OAAO,YAAY,OAAOpB,IAAI,CAACoB,OAAO,EAAE,CAAC;MAC1FP,OAAO,CAACE,GAAG,CAAC,qCAAqCkD,iBAAiB,GAAG,CAAC;MACtEpD,OAAO,CAACE,GAAG,CAAC,mCAAmCgD,eAAe,GAAG,CAAC;MAClElD,OAAO,CAACE,GAAG,CAAC,mBAAmBd,MAAM,CAACqB,YAAY,EAAE,CAAC;MACrDT,OAAO,CAACE,GAAG,CAAC,oBAAoBd,MAAM,CAACoB,aAAa,EAAE,CAAC;MACvDR,OAAO,CAACE,GAAG,CAAC,eAAeqD,OAAO,EAAE,CAAC;MACrCvD,OAAO,CAACE,GAAG,CAAC,eAAesD,OAAO,EAAE,CAAC;;MAErC;MACAxD,OAAO,CAACE,GAAG,CAAC,UAAUd,MAAM,CAAClB,SAAS,GAAG,EAAE;QACzC,kBAAkB,EAAE,IAAIkB,MAAM,CAACkB,SAAS,GAAG;QAC3C,gBAAgB,EAAE,IAAInB,IAAI,CAACmB,SAAS,GAAG;QACvC,mBAAmB,EAAE+C,cAAc;QACnC,gBAAgB,EAAE,IAAIlB,MAAM,CAAC/C,MAAM,CAACmB,OAAO,CAAC,GAAG;QAC/C,cAAc,EAAE,IAAI4B,MAAM,CAAChD,IAAI,CAACoB,OAAO,CAAC,GAAG;QAC3C,iBAAiB,EAAE+C,YAAY;QAC/B,cAAc,EAAElE,MAAM,CAACqB,YAAY;QACnC,eAAe,EAAErB,MAAM,CAACoB,aAAa;QACrC,WAAW,EAAE+C,OAAO;QACpB,WAAW,EAAEC,OAAO;QACpB,cAAc,EAAEC;MAClB,CAAC,CAAC;MAEF,OAAOA,YAAY;IACrB,CAAC,CAAC;IAEFzD,OAAO,CAACE,GAAG,CAAC,6BAA6B,EAAE2C,QAAQ,CAACnC,MAAM,CAAC;IAC3D,IAAImC,QAAQ,CAACnC,MAAM,GAAG,CAAC,EAAE;MACvBV,OAAO,CAACE,GAAG,CAAC,2BAA2B,EAAE2C,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC,MAAM;MACL7C,OAAO,CAACE,GAAG,CAAC,iDAAiD,CAAC;IAChE;IAEA,OAAO2C,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMa,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OAAOf,gBAAgB;EACzB,CAAC;;EAED;EACA,MAAMgB,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAItF,WAAW,EAAE;MACf,oBACEtB,OAAA,CAACxC,GAAG;QAACqJ,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,eAC5DjH,OAAA,CAAC7B,gBAAgB;UAAA+I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAEV;IAEA,IAAI5G,IAAI,CAACkD,MAAM,KAAK,CAAC,EAAE;MACrB,oBACE3D,OAAA,CAAC5B,KAAK;QAACkJ,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IAEA,oBACErH,OAAA,CAAAE,SAAA;MAAA+G,QAAA,gBACEjH,OAAA,CAAC5B,KAAK;QAACkJ,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERrH,OAAA,CAAChC,cAAc;QAACwJ,SAAS,EAAE5J,KAAM;QAACiJ,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,eAC9CjH,OAAA,CAACnC,KAAK;UAAC4J,IAAI,EAAC,OAAO;UAAAR,QAAA,gBACjBjH,OAAA,CAAC/B,SAAS;YAAAgJ,QAAA,eACRjH,OAAA,CAAC9B,QAAQ;cAAC2I,EAAE,EAAE;gBAAEa,OAAO,EAAE;cAAU,CAAE;cAAAT,QAAA,gBACnCjH,OAAA,CAACjC,SAAS;gBAAAkJ,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BrH,OAAA,CAACjC,SAAS;gBAAAkJ,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCrH,OAAA,CAACjC,SAAS;gBAAAkJ,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjCrH,OAAA,CAACjC,SAAS;gBAAAkJ,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpCrH,OAAA,CAACjC,SAAS;gBAAAkJ,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BrH,OAAA,CAACjC,SAAS;gBAAAkJ,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZrH,OAAA,CAAClC,SAAS;YAAAmJ,QAAA,EACPxG,IAAI,CAACkH,GAAG,CAAEvF,IAAI,IAAK;cAClB,MAAMwF,WAAW,GAAGlI,gBAAgB,CAAC0C,IAAI,CAAC;cAC1C,oBACEpC,OAAA,CAAC9B,QAAQ;gBAEP2I,EAAE,EAAE;kBACFa,OAAO,EAAEE,WAAW,GAAG,SAAS,GAAG,SAAS;kBAC5C,SAAS,EAAE;oBAAEF,OAAO,EAAEE,WAAW,GAAG,SAAS,GAAG;kBAAU;gBAC5D,CAAE;gBAAAX,QAAA,gBAEFjH,OAAA,CAACjC,SAAS;kBAAAkJ,QAAA,eAACjH,OAAA;oBAAAiH,QAAA,EAAS7E,IAAI,CAACnB;kBAAO;oBAAAiG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACtDrH,OAAA,CAACjC,SAAS;kBAAAkJ,QAAA,EAAE7E,IAAI,CAACmB,SAAS,IAAI;gBAAK;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChDrH,OAAA,CAACjC,SAAS;kBAAAkJ,QAAA,GAAC,MAAI,EAAC7E,IAAI,CAACyF,mBAAmB,IAAI,KAAK,eAAC7H,OAAA;oBAAAkH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,OAAG,EAACjF,IAAI,CAAC0F,iBAAiB,IAAI,KAAK;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvGrH,OAAA,CAACjC,SAAS;kBAAAkJ,QAAA,GAAE7E,IAAI,CAACyB,aAAa,IAAI,KAAK,EAAC,IAAE;gBAAA;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACtDrH,OAAA,CAACjC,SAAS;kBAAAkJ,QAAA,eACRjH,OAAA,CAAC3B,IAAI;oBACH0J,KAAK,EAAE3F,IAAI,CAAC0B,mBAAmB,IAAI,KAAM;oBACzC2D,IAAI,EAAC,OAAO;oBACZO,KAAK,EAAErI,kBAAkB,CAACyC,IAAI,CAAC0B,mBAAmB,CAAE;oBACpDmE,OAAO,EAAC;kBAAU;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZrH,OAAA,CAACjC,SAAS;kBAAAkJ,QAAA,eACRjH,OAAA,CAACrC,MAAM;oBACL8J,IAAI,EAAC,OAAO;oBACZQ,OAAO,EAAC,WAAW;oBACnBD,KAAK,EAAC,SAAS;oBACfE,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAACxB,IAAI,CAAE;oBACtC+F,QAAQ,EAAEP,WAAY;oBAAAX,QAAA,EAErBW,WAAW,GAAG,gBAAgB,GAAG;kBAAW;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,GA5BPjF,IAAI,CAACnB,OAAO;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6BT,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA,eACjB,CAAC;EAEP,CAAC;;EAED;EACA,MAAMe,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACvH,YAAY,EAAE,OAAO,IAAI;;IAE9B;IACA,MAAMwH,oBAAoB,GAAG1B,mBAAmB,CAAC,CAAC;IAClD1D,OAAO,CAACE,GAAG,CAAC,sCAAsC,EAAEkF,oBAAoB,CAAC;IACzEpF,OAAO,CAACE,GAAG,CAAC,gDAAgD,EAAEkF,oBAAoB,CAAC1E,MAAM,CAAC;;IAE1F;IACA,IAAIhD,MAAM,CAACgD,MAAM,KAAK,CAAC,IAAI,CAACnC,aAAa,EAAE;MACzC,oBACExB,OAAA,CAACpC,KAAK;QAACiJ,EAAE,EAAE;UAAEyB,CAAC,EAAE;QAAE,CAAE;QAAArB,QAAA,gBAClBjH,OAAA,CAACvC,UAAU;UAACwK,OAAO,EAAC,IAAI;UAACM,YAAY;UAAAtB,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbrH,OAAA,CAAC5B,KAAK;UAACkJ,QAAQ,EAAC,SAAS;UAACT,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,EAAC;QAEzC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAERrH,OAAA,CAACxC,GAAG;UAACqJ,EAAE,EAAE;YAAEU,EAAE,EAAE,CAAC;YAAEe,CAAC,EAAE,CAAC;YAAEZ,OAAO,EAAE,SAAS;YAAEc,YAAY,EAAE;UAAE,CAAE;UAAAvB,QAAA,gBAC5DjH,OAAA,CAACvC,UAAU;YAACwK,OAAO,EAAC,WAAW;YAACM,YAAY;YAACE,UAAU,EAAC,MAAM;YAACT,KAAK,EAAC,SAAS;YAAAf,QAAA,GAAC,oBAC3D,EAACpG,YAAY,CAACI,OAAO;UAAA;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACbrH,OAAA,CAACzB,IAAI;YAACmK,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA1B,QAAA,gBACzBjH,OAAA,CAACzB,IAAI;cAACqK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACvBjH,OAAA,CAACvC,UAAU;gBAACwK,OAAO,EAAC,OAAO;gBAAAhB,QAAA,gBACzBjH,OAAA;kBAAAiH,QAAA,EAAQ;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxG,YAAY,CAAC0C,SAAS,IAAI,KAAK;cAAA;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACPrH,OAAA,CAACzB,IAAI;cAACqK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACvBjH,OAAA,CAACvC,UAAU;gBAACwK,OAAO,EAAC,OAAO;gBAAAhB,QAAA,gBACzBjH,OAAA;kBAAAiH,QAAA,EAAQ;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxG,YAAY,CAAC2C,OAAO,IAAI,KAAK;cAAA;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACPrH,OAAA,CAACzB,IAAI;cAACqK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACvBjH,OAAA,CAACvC,UAAU;gBAACwK,OAAO,EAAC,OAAO;gBAAAhB,QAAA,gBACzBjH,OAAA;kBAAAiH,QAAA,EAAQ;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxG,YAAY,CAACgD,aAAa,IAAI,KAAK,EAAC,IACvE;cAAA;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENrH,OAAA,CAACzB,IAAI;UAACmK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA1B,QAAA,gBACzBjH,OAAA,CAACzB,IAAI;YAACqK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBjH,OAAA,CAACtC,SAAS;cACRqL,SAAS;cACThB,KAAK,EAAC,cAAc;cACpB9D,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAEnD,QAAQ,CAACG,YAAa;cAC7B8H,QAAQ,EAAEjF,iBAAkB;cAC5BkF,IAAI,EAAC,QAAQ;cACbC,UAAU,EAAE;gBACVC,UAAU,EAAE;kBAAEC,GAAG,EAAE,CAAC;kBAAEC,IAAI,EAAE;gBAAI;cAClC;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrH,OAAA,CAACzB,IAAI;YAACqK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBjH,OAAA,CAACxB,WAAW;cAACuK,SAAS;cAAA9B,QAAA,gBACpBjH,OAAA,CAACvB,UAAU;gBAAAwI,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/BrH,OAAA,CAACtB,MAAM;gBACLuF,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAC,cAAc;gBACpBiE,QAAQ;gBAAAlB,QAAA,eAERjH,OAAA,CAACrB,QAAQ;kBAACuF,KAAK,EAAC,cAAc;kBAAC2C,EAAE,EAAE;oBAAE4B,UAAU,EAAE,MAAM;oBAAET,KAAK,EAAE,SAAS;oBAAEN,OAAO,EAAE;kBAAU,CAAE;kBAAAT,QAAA,EAAC;gBAEjG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACTrH,OAAA,CAACvC,UAAU;gBAACwK,OAAO,EAAC,SAAS;gBAACD,KAAK,EAAC,gBAAgB;gBAACnB,EAAE,EAAE;kBAAEyC,EAAE,EAAE;gBAAE,CAAE;gBAAArC,QAAA,EAAC;cAEpE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPrH,OAAA,CAACxC,GAAG;UAACqJ,EAAE,EAAE;YAAEyC,EAAE,EAAE,CAAC;YAAExC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAgB,CAAE;UAAAE,QAAA,gBACnEjH,OAAA,CAACrC,MAAM;YACLsK,OAAO,EAAC,UAAU;YAClBD,KAAK,EAAC,WAAW;YACjBE,OAAO,EAAEA,CAAA,KAAM;cACbpH,eAAe,CAAC,IAAI,CAAC;cACrBE,WAAW,CAAC;gBACVC,OAAO,EAAE,EAAE;gBACXC,YAAY,EAAE,EAAE;gBAChBC,SAAS,EAAE;cACb,CAAC,CAAC;YACJ,CAAE;YACFgH,QAAQ,EAAEzG,MAAO;YAAAuF,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrH,OAAA,CAACrC,MAAM;YACLsK,OAAO,EAAC,WAAW;YACnBD,KAAK,EAAC,SAAS;YACfE,OAAO,EAAEA,CAAA,KAAM7C,UAAU,CAAC,CAAE;YAC5B8C,QAAQ,EAAEzG,MAAM,IAAI,CAACX,QAAQ,CAACG,YAAa;YAAA+F,QAAA,EAE1CvF,MAAM,gBAAG1B,OAAA,CAAC7B,gBAAgB;cAACsJ,IAAI,EAAE;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAEZ;IAEA,MAAMzB,gBAAgB,GAAGe,mBAAmB,CAAC,CAAC;IAE9C,oBACE3G,OAAA,CAACpC,KAAK;MAACiJ,EAAE,EAAE;QAAEyB,CAAC,EAAE;MAAE,CAAE;MAAArB,QAAA,gBAClBjH,OAAA,CAACvC,UAAU;QAACwK,OAAO,EAAC,IAAI;QAACM,YAAY;QAAAtB,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbrH,OAAA,CAAC5B,KAAK;QAACkJ,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,GAAC,4IACmG,eAAAjH,OAAA;UAAAiH,QAAA,EAAQ;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KACtK;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERrH,OAAA,CAACxC,GAAG;QAACqJ,EAAE,EAAE;UAAEU,EAAE,EAAE,CAAC;UAAEe,CAAC,EAAE,CAAC;UAAEZ,OAAO,EAAE,SAAS;UAAEc,YAAY,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBAC5DjH,OAAA,CAACvC,UAAU;UAACwK,OAAO,EAAC,WAAW;UAACM,YAAY;UAACE,UAAU,EAAC,MAAM;UAACT,KAAK,EAAC,SAAS;UAAAf,QAAA,GAAC,oBAC3D,EAACpG,YAAY,CAACI,OAAO;QAAA;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACbrH,OAAA,CAACzB,IAAI;UAACmK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA1B,QAAA,gBACzBjH,OAAA,CAACzB,IAAI;YAACqK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBjH,OAAA,CAACvC,UAAU;cAACwK,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBjH,OAAA;gBAAAiH,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxG,YAAY,CAAC0C,SAAS,IAAI,KAAK;YAAA;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPrH,OAAA,CAACzB,IAAI;YAACqK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBjH,OAAA,CAACvC,UAAU;cAACwK,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBjH,OAAA;gBAAAiH,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxG,YAAY,CAAC2C,OAAO,IAAI,KAAK;YAAA;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPrH,OAAA,CAACzB,IAAI;YAACqK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBjH,OAAA,CAACvC,UAAU;cAACwK,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBjH,OAAA;gBAAAiH,QAAA,EAAQ;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxG,YAAY,CAACgD,aAAa,IAAI,KAAK,EAAC,IACvE;YAAA;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPrH,OAAA,CAACzB,IAAI;YAACqK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBjH,OAAA,CAACvC,UAAU;cAACwK,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBjH,OAAA;gBAAAiH,QAAA,EAAQ;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxG,YAAY,CAACgH,mBAAmB,IAAI,KAAK;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPrH,OAAA,CAACzB,IAAI;YAACqK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBjH,OAAA,CAACvC,UAAU;cAACwK,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBjH,OAAA;gBAAAiH,QAAA,EAAQ;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxG,YAAY,CAACiH,iBAAiB,IAAI,KAAK;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPrH,OAAA,CAACzB,IAAI;YAACqK,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA5B,QAAA,eAChBjH,OAAA,CAACvC,UAAU;cAACwK,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBjH,OAAA;gBAAAiH,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvBrH,OAAA,CAAC3B,IAAI;gBACH0J,KAAK,EAAElH,YAAY,CAACiD,mBAAmB,IAAI,KAAM;gBACjD2D,IAAI,EAAC,OAAO;gBACZO,KAAK,EAAErI,kBAAkB,CAACkB,YAAY,CAACiD,mBAAmB,CAAE;gBAC5DmE,OAAO,EAAC,UAAU;gBAClBpB,EAAE,EAAE;kBAAE0C,EAAE,EAAE;gBAAE;cAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENrH,OAAA,CAAC1B,OAAO;QAACuI,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1BrH,OAAA,CAACzB,IAAI;QAACmK,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA1B,QAAA,gBACzBjH,OAAA,CAACzB,IAAI;UAACqK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA7B,QAAA,eACvBjH,OAAA,CAACtC,SAAS;YACRqL,SAAS;YACThB,KAAK,EAAC,cAAc;YACpB9D,IAAI,EAAC,cAAc;YACnBC,KAAK,EAAEnD,QAAQ,CAACG,YAAa;YAC7B8H,QAAQ,EAAEjF,iBAAkB;YAC5BkF,IAAI,EAAC,QAAQ;YACbjG,KAAK,EAAE,CAAC,CAACpB,UAAU,CAACV,YAAa;YACjCsI,UAAU,EAAE5H,UAAU,CAACV,YAAY,IAAKY,YAAY,CAACZ,YAAY,iBAC/DlB,OAAA;cAAMyJ,KAAK,EAAE;gBAAEzB,KAAK,EAAE;cAAS,CAAE;cAAAf,QAAA,EAAEnF,YAAY,CAACZ;YAAY;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAClE;YACHc,QAAQ,EAAEzG,MAAO;YACjBwH,UAAU,EAAE;cACVC,UAAU,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,IAAI,EAAE;cAAI;YAClC;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPrH,OAAA,CAACzB,IAAI;UAACqK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA7B,QAAA,eACvBjH,OAAA,CAACxB,WAAW;YAACuK,SAAS;YAAC/F,KAAK,EAAE,CAAC,CAACpB,UAAU,CAACT,SAAU;YAAA8F,QAAA,gBACnDjH,OAAA,CAACvB,UAAU;cAAAwI,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/BrH,OAAA,CAACtB,MAAM;cACLuF,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAEnD,QAAQ,CAACI,SAAU;cAC1B6H,QAAQ,EAAEjF,iBAAkB;cAC5BoE,QAAQ,EAAEzG,MAAM,IAAIF,aAAc;cAAAyF,QAAA,gBAGlCjH,OAAA,CAACrB,QAAQ;gBAACuF,KAAK,EAAC,cAAc;gBAAC2C,EAAE,EAAE;kBAAE4B,UAAU,EAAE,MAAM;kBAAET,KAAK,EAAE,SAAS;kBAAEN,OAAO,EAAE;gBAAU,CAAE;gBAAAT,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAGXrH,OAAA,CAAC1B,OAAO;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAGV7F,aAAa,gBACZxB,OAAA,CAACrB,QAAQ;gBAACwJ,QAAQ;gBAAAlB,QAAA,eAChBjH,OAAA,CAACxC,GAAG;kBAACqJ,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAE4C,UAAU,EAAE;kBAAS,CAAE;kBAAAzC,QAAA,gBACjDjH,OAAA,CAAC7B,gBAAgB;oBAACsJ,IAAI,EAAE,EAAG;oBAACZ,EAAE,EAAE;sBAAE8C,EAAE,EAAE;oBAAE;kBAAE;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7CrH,OAAA,CAACvC,UAAU;oBAACwK,OAAO,EAAC,SAAS;oBAAAhB,QAAA,EAAC;kBAE9B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,GACTzB,gBAAgB,CAACjC,MAAM,KAAK,CAAC,gBAC/B3D,OAAA,CAACrB,QAAQ;gBAACwJ,QAAQ;gBAAAlB,QAAA,eAChBjH,OAAA,CAACvC,UAAU;kBAACwK,OAAO,EAAC,SAAS;kBAACD,KAAK,EAAC,gBAAgB;kBAAAf,QAAA,EAAC;gBAErD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,gBAEXrH,OAAA,CAACrB,QAAQ;gBAACwJ,QAAQ;gBAAAlB,QAAA,eAChBjH,OAAA,CAACvC,UAAU;kBAACwK,OAAO,EAAC,SAAS;kBAAAhB,QAAA,GAAC,sBACR,EAACrB,gBAAgB,CAACjC,MAAM,EAAC,GAC/C;gBAAA;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACX,EAGA,CAAC7F,aAAa,IAAI,CAAC,MAAM;gBACxByB,OAAO,CAACE,GAAG,CAAC,wCAAwC,EAAEyC,gBAAgB,CAAC;gBACvE3C,OAAO,CAACE,GAAG,CAAC,kDAAkD,EAAEyC,gBAAgB,CAACjC,MAAM,CAAC;gBAExF,OAAOiC,gBAAgB,CAAC+B,GAAG,CAAEtF,MAAM,IAAK;kBACtCY,OAAO,CAACE,GAAG,CAAC,+BAA+B,EAAEd,MAAM,CAAC;kBACpD,oBACErC,OAAA,CAACrB,QAAQ;oBAAwBuF,KAAK,EAAE7B,MAAM,CAAClB,SAAU;oBAAA8F,QAAA,GACtD5E,MAAM,CAAClB,SAAS,EAAC,KAAG,EAACkB,MAAM,CAACkB,SAAS,EAAC,KAAG,EAAClB,MAAM,CAACoB,aAAa,EAAC,GAClE;kBAAA,GAFepB,MAAM,CAAClB,SAAS;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAErB,CAAC;gBAEf,CAAC,CAAC;cACJ,CAAC,EAAE,CAAC,eAIJrH,OAAA,CAAC1B,OAAO;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGXrH,OAAA,CAACrB,QAAQ;gBAACwJ,QAAQ;gBAAAlB,QAAA,eAChBjH,OAAA,CAACvC,UAAU;kBAACwK,OAAO,EAAC,SAAS;kBAACpB,EAAE,EAAE;oBAAE4B,UAAU,EAAE,MAAM;oBAAET,KAAK,EAAE;kBAAU,CAAE;kBAAAf,QAAA,EAAC;gBAE5E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EAGV1G,MAAM,CAACoC,MAAM,CAACV,MAAM,IAAIA,MAAM,CAACqB,YAAY,KAAK,WAAW,CAAC,CAACiE,GAAG,CAAEtF,MAAM,iBACvErC,OAAA,CAACrB,QAAQ;gBAAwBuF,KAAK,EAAE7B,MAAM,CAAClB,SAAU;gBAAA8F,QAAA,GACtD5E,MAAM,CAAClB,SAAS,EAAC,KAAG,EAACkB,MAAM,CAACkB,SAAS,EAAC,KAAG,EAAClB,MAAM,CAACmB,OAAO,EAAC,KAAG,EAACnB,MAAM,CAACoB,aAAa,EAAC,GACrF;cAAA,GAFepB,MAAM,CAAClB,SAAS;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAErB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACRzF,UAAU,CAACT,SAAS,iBACnBnB,OAAA,CAACvC,UAAU;cAACwK,OAAO,EAAC,SAAS;cAACD,KAAK,EAAC,OAAO;cAAAf,QAAA,EACxCrF,UAAU,CAACT;YAAS;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACb,EACAvF,YAAY,CAACX,SAAS,iBACrBnB,OAAA,CAACvC,UAAU;cAACwK,OAAO,EAAC,SAAS;cAACpB,EAAE,EAAE;gBAAEmB,KAAK,EAAE;cAAS,CAAE;cAAAf,QAAA,EACnDnF,YAAY,CAACX;YAAS;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACb,eAEDrH,OAAA,CAACvC,UAAU;cAACwK,OAAO,EAAC,SAAS;cAACD,KAAK,EAAC,gBAAgB;cAACnB,EAAE,EAAE;gBAAEyC,EAAE,EAAE;cAAE,CAAE;cAAArC,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPrH,OAAA,CAACxC,GAAG;QAACqJ,EAAE,EAAE;UAAEyC,EAAE,EAAE,CAAC;UAAExC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAE,QAAA,gBACnEjH,OAAA,CAACrC,MAAM;UACLsK,OAAO,EAAC,UAAU;UAClBD,KAAK,EAAC,WAAW;UACjBE,OAAO,EAAEA,CAAA,KAAM;YACbpH,eAAe,CAAC,IAAI,CAAC;YACrBE,WAAW,CAAC;cACVC,OAAO,EAAE,EAAE;cACXC,YAAY,EAAE,EAAE;cAChBC,SAAS,EAAE;YACb,CAAC,CAAC;UACJ,CAAE;UACFgH,QAAQ,EAAEzG,MAAO;UAAAuF,QAAA,EAClB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrH,OAAA,CAACrC,MAAM;UACLsK,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACfE,OAAO,EAAE7C,UAAW;UACpB8C,QAAQ,EAAEzG,MAAM,IAAIqD,MAAM,CAACC,IAAI,CAACpD,UAAU,CAAC,CAAC+B,MAAM,GAAG,CAAE;UAAAsD,QAAA,EAEtDvF,MAAM,gBAAG1B,OAAA,CAAC7B,gBAAgB;YAACsJ,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEZ,CAAC;EAED,oBACErH,OAAA,CAACxC,GAAG;IAAAyJ,QAAA,GAED,CAACpG,YAAY,IAAI+F,eAAe,CAAC,CAAC,EAGlCwB,UAAU,CAAC,CAAC,eAGbpI,OAAA,CAACF,sBAAsB;MACrB8J,IAAI,EAAE5H,0BAA2B;MACjC6H,OAAO,EAAEA,CAAA,KAAM5H,6BAA6B,CAAC,KAAK,CAAE;MACpDG,IAAI,EAAEF,oBAAoB,CAACE,IAAK;MAChCC,MAAM,EAAEH,oBAAoB,CAACG,MAAO;MACpCyH,YAAY,EAAEvE,gCAAiC;MAC/CwE,mBAAmB,EAAEA,CAAA,KAAM;QACzB9H,6BAA6B,CAAC,KAAK,CAAC;QACpCjB,WAAW,CAACoD,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEjD,SAAS,EAAE;QAAG,CAAC,CAAC,CAAC;MACnD;IAAE;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFrH,OAAA,CAACpB,MAAM;MAACgL,IAAI,EAAEtH,qBAAsB;MAACuH,OAAO,EAAEpE,4BAA6B;MAAAwB,QAAA,gBACzEjH,OAAA,CAACnB,WAAW;QAAAoI,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC1CrH,OAAA,CAAClB,aAAa;QAAAmI,QAAA,gBACZjH,OAAA,CAAChB,iBAAiB;UAAAiI,QAAA,GAAC,UACT,EAACzE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEvB,OAAO,EAAC,4BACpC;QAAA;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBrH,OAAA,CAACxC,GAAG;UAACqJ,EAAE,EAAE;YAAEyC,EAAE,EAAE;UAAE,CAAE;UAAArC,QAAA,gBACjBjH,OAAA,CAACvC,UAAU;YAACwK,OAAO,EAAC,OAAO;YAACM,YAAY;YAAAtB,QAAA,EAAC;UAEzC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbrH,OAAA,CAACvC,UAAU;YAAC+J,SAAS,EAAC,IAAI;YAACS,OAAO,EAAC,OAAO;YAAAhB,QAAA,gBACxCjH,OAAA;cAAAiH,QAAA,EAAI;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvCrH,OAAA;cAAAiH,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCrH,OAAA;cAAAiH,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBrH,OAAA,CAACjB,aAAa;QAAC8H,EAAE,EAAE;UAAEyB,CAAC,EAAE,CAAC;UAAEvB,cAAc,EAAE;QAAgB,CAAE;QAAAE,QAAA,gBAC3DjH,OAAA,CAACrC,MAAM;UAACuK,OAAO,EAAEzC,4BAA6B;UAACuC,KAAK,EAAC,WAAW;UAAAf,QAAA,EAAC;QAEjE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrH,OAAA,CAACxC,GAAG;UAAAyJ,QAAA,gBACFjH,OAAA,CAACrC,MAAM;YAACuK,OAAO,EAAExC,wBAAyB;YAACsC,KAAK,EAAC,SAAS;YAACnB,EAAE,EAAE;cAAE8C,EAAE,EAAE;YAAE,CAAE;YAAA1C,QAAA,EAAC;UAE1E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrH,OAAA,CAACrC,MAAM;YAACuK,OAAO,EAAEvC,gBAAiB;YAACsC,OAAO,EAAC,WAAW;YAACD,KAAK,EAAC,SAAS;YAAAf,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC9G,EAAA,CA34BIJ,2BAA2B;EAAA,QACdlB,WAAW;AAAA;AAAA+K,EAAA,GADxB7J,2BAA2B;AA64BjC,eAAeA,2BAA2B;AAAC,IAAA6J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}