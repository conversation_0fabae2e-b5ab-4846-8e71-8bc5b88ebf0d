{"ast": null, "code": "export function buildMatchFn(args) {\n  return (string, options = {}) => {\n    const width = options.width;\n    const matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    const matchResult = string.match(matchPattern);\n    if (!matchResult) {\n      return null;\n    }\n    const matchedString = matchResult[0];\n    const parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    const key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, pattern => pattern.test(matchedString)) :\n    // [TODO] -- I challenge you to fix the type\n    findKey(parsePatterns, pattern => pattern.test(matchedString));\n    let value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ?\n    // [TODO] -- I challenge you to fix the type\n    options.valueCallback(value) : value;\n    const rest = string.slice(matchedString.length);\n    return {\n      value,\n      rest\n    };\n  };\n}\nfunction findKey(object, predicate) {\n  for (const key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}\nfunction findIndex(array, predicate) {\n  for (let key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}", "map": {"version": 3, "names": ["buildMatchFn", "args", "string", "options", "width", "matchPattern", "matchPatterns", "defaultMatchWidth", "matchResult", "match", "matchedString", "parsePatterns", "defaultParseWidth", "key", "Array", "isArray", "findIndex", "pattern", "test", "<PERSON><PERSON><PERSON>", "value", "valueCallback", "rest", "slice", "length", "object", "predicate", "Object", "prototype", "hasOwnProperty", "call", "undefined", "array"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/_lib/buildMatchFn.js"], "sourcesContent": ["export function buildMatchFn(args) {\n  return (string, options = {}) => {\n    const width = options.width;\n\n    const matchPattern =\n      (width && args.matchPatterns[width]) ||\n      args.matchPatterns[args.defaultMatchWidth];\n    const matchResult = string.match(matchPattern);\n\n    if (!matchResult) {\n      return null;\n    }\n    const matchedString = matchResult[0];\n\n    const parsePatterns =\n      (width && args.parsePatterns[width]) ||\n      args.parsePatterns[args.defaultParseWidth];\n\n    const key = Array.isArray(parsePatterns)\n      ? findIndex(parsePatterns, (pattern) => pattern.test(matchedString))\n      : // [TODO] -- I challenge you to fix the type\n        findKey(parsePatterns, (pattern) => pattern.test(matchedString));\n\n    let value;\n\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback\n      ? // [TODO] -- I challenge you to fix the type\n        options.valueCallback(value)\n      : value;\n\n    const rest = string.slice(matchedString.length);\n\n    return { value, rest };\n  };\n}\n\nfunction findKey(object, predicate) {\n  for (const key in object) {\n    if (\n      Object.prototype.hasOwnProperty.call(object, key) &&\n      predicate(object[key])\n    ) {\n      return key;\n    }\n  }\n  return undefined;\n}\n\nfunction findIndex(array, predicate) {\n  for (let key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}\n"], "mappings": "AAAA,OAAO,SAASA,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAO,CAACC,MAAM,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC/B,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;IAE3B,MAAMC,YAAY,GACfD,KAAK,IAAIH,IAAI,CAACK,aAAa,CAACF,KAAK,CAAC,IACnCH,IAAI,CAACK,aAAa,CAACL,IAAI,CAACM,iBAAiB,CAAC;IAC5C,MAAMC,WAAW,GAAGN,MAAM,CAACO,KAAK,CAACJ,YAAY,CAAC;IAE9C,IAAI,CAACG,WAAW,EAAE;MAChB,OAAO,IAAI;IACb;IACA,MAAME,aAAa,GAAGF,WAAW,CAAC,CAAC,CAAC;IAEpC,MAAMG,aAAa,GAChBP,KAAK,IAAIH,IAAI,CAACU,aAAa,CAACP,KAAK,CAAC,IACnCH,IAAI,CAACU,aAAa,CAACV,IAAI,CAACW,iBAAiB,CAAC;IAE5C,MAAMC,GAAG,GAAGC,KAAK,CAACC,OAAO,CAACJ,aAAa,CAAC,GACpCK,SAAS,CAACL,aAAa,EAAGM,OAAO,IAAKA,OAAO,CAACC,IAAI,CAACR,aAAa,CAAC,CAAC;IAClE;IACAS,OAAO,CAACR,aAAa,EAAGM,OAAO,IAAKA,OAAO,CAACC,IAAI,CAACR,aAAa,CAAC,CAAC;IAEpE,IAAIU,KAAK;IAETA,KAAK,GAAGnB,IAAI,CAACoB,aAAa,GAAGpB,IAAI,CAACoB,aAAa,CAACR,GAAG,CAAC,GAAGA,GAAG;IAC1DO,KAAK,GAAGjB,OAAO,CAACkB,aAAa;IACzB;IACAlB,OAAO,CAACkB,aAAa,CAACD,KAAK,CAAC,GAC5BA,KAAK;IAET,MAAME,IAAI,GAAGpB,MAAM,CAACqB,KAAK,CAACb,aAAa,CAACc,MAAM,CAAC;IAE/C,OAAO;MAAEJ,KAAK;MAAEE;IAAK,CAAC;EACxB,CAAC;AACH;AAEA,SAASH,OAAOA,CAACM,MAAM,EAAEC,SAAS,EAAE;EAClC,KAAK,MAAMb,GAAG,IAAIY,MAAM,EAAE;IACxB,IACEE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,MAAM,EAAEZ,GAAG,CAAC,IACjDa,SAAS,CAACD,MAAM,CAACZ,GAAG,CAAC,CAAC,EACtB;MACA,OAAOA,GAAG;IACZ;EACF;EACA,OAAOkB,SAAS;AAClB;AAEA,SAASf,SAASA,CAACgB,KAAK,EAAEN,SAAS,EAAE;EACnC,KAAK,IAAIb,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGmB,KAAK,CAACR,MAAM,EAAEX,GAAG,EAAE,EAAE;IAC3C,IAAIa,SAAS,CAACM,KAAK,CAACnB,GAAG,CAAC,CAAC,EAAE;MACzB,OAAOA,GAAG;IACZ;EACF;EACA,OAAOkB,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}