{"ast": null, "code": "/**\n * Google Chrome as of 67.0.3396.87 introduced timezones with offset that includes seconds.\n * They usually appear for dates that denote time before the timezones were introduced\n * (e.g. for 'Europe/Prague' timezone the offset is GMT+00:57:44 before 1 October 1891\n * and GMT+01:00:00 after that date)\n *\n * Date#getTimezoneOffset returns the offset in minutes and would return 57 for the example above,\n * which would lead to incorrect calculations.\n *\n * This function returns the timezone offset in milliseconds that takes seconds in account.\n */\nexport default function getTimezoneOffsetInMilliseconds(date) {\n  var utcDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()));\n  utcDate.setUTCFullYear(date.getFullYear());\n  return date.getTime() - utcDate.getTime();\n}", "map": {"version": 3, "names": ["getTimezoneOffsetInMilliseconds", "date", "utcDate", "Date", "UTC", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "setUTCFullYear", "getTime"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/_lib/getTimezoneOffsetInMilliseconds/index.js"], "sourcesContent": ["/**\n * Google Chrome as of 67.0.3396.87 introduced timezones with offset that includes seconds.\n * They usually appear for dates that denote time before the timezones were introduced\n * (e.g. for 'Europe/Prague' timezone the offset is GMT+00:57:44 before 1 October 1891\n * and GMT+01:00:00 after that date)\n *\n * Date#getTimezoneOffset returns the offset in minutes and would return 57 for the example above,\n * which would lead to incorrect calculations.\n *\n * This function returns the timezone offset in milliseconds that takes seconds in account.\n */\nexport default function getTimezoneOffsetInMilliseconds(date) {\n  var utcDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()));\n  utcDate.setUTCFullYear(date.getFullYear());\n  return date.getTime() - utcDate.getTime();\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,+BAA+BA,CAACC,IAAI,EAAE;EAC5D,IAAIC,OAAO,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAACH,IAAI,CAACI,WAAW,CAAC,CAAC,EAAEJ,IAAI,CAACK,QAAQ,CAAC,CAAC,EAAEL,IAAI,CAACM,OAAO,CAAC,CAAC,EAAEN,IAAI,CAACO,QAAQ,CAAC,CAAC,EAAEP,IAAI,CAACQ,UAAU,CAAC,CAAC,EAAER,IAAI,CAACS,UAAU,CAAC,CAAC,EAAET,IAAI,CAACU,eAAe,CAAC,CAAC,CAAC,CAAC;EACpKT,OAAO,CAACU,cAAc,CAACX,IAAI,CAACI,WAAW,CAAC,CAAC,CAAC;EAC1C,OAAOJ,IAAI,CAACY,OAAO,CAAC,CAAC,GAAGX,OAAO,CAACW,OAAO,CAAC,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}