{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Chip, CircularProgress, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport './CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  var _caviAttivi$, _caviAttivi$2, _caviAttivi$3;\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const {\n    openEliminaCavoDialog,\n    setOpenEliminaCavoDialog\n  } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState(null);\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Funzione per caricare i cavi\n  const fetchCavi = async () => {\n    try {\n      setLoading(true);\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      const attivi = await caviService.getCavi(cantiereId, 0, filters);\n      console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi spare\n      console.log('Caricamento cavi SPARE (tipo_cavo=3)...');\n      const spare = await caviService.getCavi(cantiereId, 3);\n      console.log('Cavi SPARE caricati:', spare ? spare.length : 0);\n      if (spare && spare.length > 0) {\n        console.log('Primo cavo SPARE:', spare[0]);\n      }\n      setCaviSpare(spare || []);\n\n      // Carica le statistiche\n      console.log('Caricamento statistiche...');\n      const statsData = await caviService.getCaviStats(cantiereId);\n      console.log('Statistiche caricate:', statsData);\n      setStats(statsData);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = cavo => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: handleCloseDetails,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Cavo: \", selectedCavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sistema:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utility:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utility || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Colore:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.colore_cavo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"N. Conduttori:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.n_conduttori || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sezione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"SH:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sh || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metratura Reale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 45\n                }, this), \" \", normalizeInstallationStatus(selectedCavo.stato_installazione)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Collegamenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.collegamenti || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.id_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ultimo Aggiornamento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 45\n                }, this), \" \", new Date(selectedCavo.timestamp).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDetails,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    if (!stats) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Statistiche\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 9\n      }, this), loadingStats ? /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Cavi Attivi: \", stats.totali.cavi_attivi]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Cavi Spare: \", stats.totali.cavi_spare]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Totale Cavi: \", stats.totali.cavi_totali]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Metrature\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Metri Teorici: \", stats.metrature.metri_teorici_totali.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Metri Posati: \", stats.metrature.metri_reali_totali.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: '100%',\n                mr: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"determinate\",\n                value: stats.metrature.percentuale_completamento,\n                sx: {\n                  height: 10,\n                  borderRadius: 5\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                minWidth: 35\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: `${stats.metrature.percentuale_completamento.toFixed(1)}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Stati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: stats.stati.map((stato, index) => /*#__PURE__*/_jsxDEV(Chip, {\n              label: `${stato.stato}: ${stato.count}`,\n              size: \"small\",\n              onClick: () => {\n                setFilters(prev => ({\n                  ...prev,\n                  stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                }));\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [renderStatsPanel(), caviAttivi.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2,\n            p: 1,\n            bgcolor: '#f0f0f0',\n            borderRadius: 1,\n            fontSize: '0.8rem',\n            fontFamily: 'monospace',\n            display: 'none'\n          },\n          children: Object.keys(caviAttivi[0]).map(key => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [key, \": \", JSON.stringify(caviAttivi[0][key])]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 21\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviAttivi,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi attivi filtrati:', filteredData.length),\n          revisioneCorrente: ((_caviAttivi$ = caviAttivi[0]) === null || _caviAttivi$ === void 0 ? void 0 : _caviAttivi$.revisione_ufficiale) || ((_caviAttivi$2 = caviAttivi[0]) === null || _caviAttivi$2 === void 0 ? void 0 : _caviAttivi$2.revisione) || ((_caviAttivi$3 = caviAttivi[0]) === null || _caviAttivi$3 === void 0 ? void 0 : _caviAttivi$3.rev)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Spare \", caviSpare.length > 0 ? `(${caviSpare.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: fetchCavi,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 71\n            }, this),\n            disabled: loading,\n            children: \"Aggiorna\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 13\n        }, this), caviSpare.length > 0 ? /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviSpare,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi spare filtrati:', filteredData.length)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: \"Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 11\n      }, this), renderDetailsDialog(), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openEliminaCavoDialog,\n        onClose: () => setOpenEliminaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"md\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Mostra un messaggio di successo\n            console.log(message);\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n            // Ricarica i dati\n            fetchCavi();\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error(message);\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n          },\n          initialOption: \"eliminaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 525,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"ctfurOnaQC7CR9UtOrSx9UYzbXM=\", false, function () {\n  return [useAuth, useGlobalContext, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Chip", "CircularProgress", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "InfoIcon", "RefreshIcon", "useNavigate", "useAuth", "useGlobalContext", "PosaCaviCollegamenti", "caviService", "normalizeInstallationStatus", "CaviFilterableTable", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "_caviAttivi$", "_caviAttivi$2", "_caviAttivi$3", "isImpersonating", "user", "openEliminaCavoDialog", "setOpenEliminaCavoDialog", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "detailsDialogOpen", "setDetailsDialogOpen", "stats", "setStats", "loadingStats", "setLoadingStats", "loadStatiInstallazione", "setStatiInstallazione", "filters", "setFilters", "stato_installazione", "tipologia", "sort_by", "sort_order", "statiInstallazione", "tipologieCavi", "setTipologieCavi", "<PERSON><PERSON><PERSON>", "console", "log", "attivi", "get<PERSON><PERSON>", "length", "spare", "statsData", "getCaviStats", "message", "fetchData", "token", "localStorage", "getItem", "selectedCantiereId", "selectedCantiereName", "i", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "stati", "item", "stato", "filter", "tipologie", "tipo", "statsError", "timeoutPromise", "Promise", "_", "reject", "setTimeout", "Error", "caviPromise", "race", "caviError", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "spareError", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "includes", "detail", "handleOpenDetails", "cavo", "handleCloseDetails", "renderDetailsDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "id_cavo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dividers", "container", "spacing", "xs", "md", "variant", "gutterBottom", "sx", "mb", "sistema", "utility", "colore_cavo", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "responsabile_partenza", "comanda_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "responsabile_arrivo", "comanda_arrivo", "metri_te<PERSON>ci", "metratura_reale", "colle<PERSON>nti", "id_bobina", "responsabile_posa", "comanda_posa", "Date", "timestamp", "toLocaleString", "onClick", "renderStatsPanel", "p", "totali", "cavi_attivi", "cavi_spare", "cavi_totali", "metrature", "metri_teorici_totali", "toFixed", "metri_reali_totali", "display", "alignItems", "mt", "width", "mr", "value", "percentuale_completamento", "height", "borderRadius", "min<PERSON><PERSON><PERSON>", "color", "flexWrap", "gap", "index", "label", "count", "size", "prev", "className", "flexDirection", "window", "location", "reload", "severity", "process", "env", "NODE_ENV", "bgcolor", "fontSize", "fontFamily", "Object", "keys", "stringify", "cavi", "onFilteredDataChange", "filteredData", "revisioneCorrente", "revisione_ufficiale", "revisione", "rev", "justifyContent", "startIcon", "disabled", "onSuccess", "onError", "initialOption", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Chip,\n  CircularProgress,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const { openEliminaCavoDialog, setOpenEliminaCavoDialog } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState(null);\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Funzione per caricare i cavi\n  const fetchCavi = async () => {\n    try {\n      setLoading(true);\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      const attivi = await caviService.getCavi(cantiereId, 0, filters);\n      console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi spare\n      console.log('Caricamento cavi SPARE (tipo_cavo=3)...');\n      const spare = await caviService.getCavi(cantiereId, 3);\n      console.log('Cavi SPARE caricati:', spare ? spare.length : 0);\n      if (spare && spare.length > 0) {\n        console.log('Primo cavo SPARE:', spare[0]);\n      }\n      setCaviSpare(spare || []);\n\n      // Carica le statistiche\n      console.log('Caricamento statistiche...');\n      const statsData = await caviService.getCaviStats(cantiereId);\n      console.log('Statistiche caricate:', statsData);\n      setStats(statsData);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = (cavo) => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Cavo: {selectedCavo.id_cavo}\n        </DialogTitle>\n        <DialogContent dividers>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Informazioni Generali</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>N. Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Sezione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>SH:</strong> {selectedCavo.sh || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Partenza</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Arrivo</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Installazione</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>\n                <Typography variant=\"body2\"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDetails}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    if (!stats) return null;\n\n    return (\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Typography variant=\"h6\" gutterBottom>Statistiche</Typography>\n        {loadingStats ? (\n          <LinearProgress />\n        ) : (\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Totali</Typography>\n              <Typography variant=\"body2\">Cavi Attivi: {stats.totali.cavi_attivi}</Typography>\n              <Typography variant=\"body2\">Cavi Spare: {stats.totali.cavi_spare}</Typography>\n              <Typography variant=\"body2\">Totale Cavi: {stats.totali.cavi_totali}</Typography>\n              {/* Rimossa visualizzazione della revisione da qui, spostata nel titolo delle statistiche della tabella */}\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Metrature</Typography>\n              <Typography variant=\"body2\">Metri Teorici: {stats.metrature.metri_teorici_totali.toFixed(2)}</Typography>\n              <Typography variant=\"body2\">Metri Posati: {stats.metrature.metri_reali_totali.toFixed(2)}</Typography>\n              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                <Box sx={{ width: '100%', mr: 1 }}>\n                  <LinearProgress\n                    variant=\"determinate\"\n                    value={stats.metrature.percentuale_completamento}\n                    sx={{ height: 10, borderRadius: 5 }}\n                  />\n                </Box>\n                <Box sx={{ minWidth: 35 }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">{`${stats.metrature.percentuale_completamento.toFixed(1)}%`}</Typography>\n                </Box>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Stati</Typography>\n              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                {stats.stati.map((stato, index) => (\n                  <Chip\n                    key={index}\n                    label={`${stato.stato}: ${stato.count}`}\n                    size=\"small\"\n                    onClick={() => {\n                      setFilters(prev => ({\n                        ...prev,\n                        stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                      }));\n                    }}\n                  />\n                ))}\n              </Box>\n            </Grid>\n          </Grid>\n        )}\n      </Paper>\n    );\n  };\n\n  return (\n    <Box className=\"cavi-page\">\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <CircularProgress size={40} />\n          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          {/* Rimosso il pulsante di refresh, gli utenti possono usare il refresh del browser */}\n\n          {/* Pannello delle statistiche */}\n          {renderStatsPanel()}\n\n          {caviAttivi.length > 0 && (\n            <Box sx={{ mb: 2 }}>\n              {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}\n              {process.env.NODE_ENV === 'development' && (\n                <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>\n                  {Object.keys(caviAttivi[0]).map(key => (\n                    <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>\n                  ))}\n                </Box>\n              )}\n              <CaviFilterableTable\n                cavi={caviAttivi}\n                loading={loading}\n                onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}\n                revisioneCorrente={caviAttivi[0]?.revisione_ufficiale || caviAttivi[0]?.revisione || caviAttivi[0]?.rev}\n              />\n            </Box>\n          )}\n\n          {/* Sezione Cavi Spare */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}\n              </Typography>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={fetchCavi}\n                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}\n                disabled={loading}\n              >\n                Aggiorna\n              </Button>\n            </Box>\n            {caviSpare.length > 0 ? (\n              <CaviFilterableTable\n                cavi={caviSpare}\n                loading={loading}\n                onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}\n              />\n            ) : (\n              <Alert severity=\"info\" sx={{ mb: 2 }}>\n                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Dialogo dei dettagli del cavo */}\n          {renderDetailsDialog()}\n\n          {/* Dialogo per l'eliminazione dei cavi */}\n          <Dialog\n            open={openEliminaCavoDialog}\n            onClose={() => setOpenEliminaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Mostra un messaggio di successo\n                console.log(message);\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n                // Ricarica i dati\n                fetchCavi();\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error(message);\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n              }}\n              initialOption=\"eliminaCavo\"\n            />\n          </Dialog>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,gBAAgB,EAChBC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,WAAW,MAAM,6BAA6B;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,2BAA2B,QAAQ,6BAA6B;AACzE,OAAOC,mBAAmB,MAAM,2CAA2C;AAC3E,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEe,qBAAqB;IAAEC;EAAyB,CAAC,GAAGf,gBAAgB,CAAC,CAAC;EAC9E,MAAMgB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiD,KAAK,EAAEC,QAAQ,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACxC;;EAEA;EACA,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM2D,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAC,qBAAqB,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC;IACrC+D,mBAAmB,EAAE,EAAE;IACvBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEP,qBAAqB,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACoE,aAAa,EAAEC,gBAAgB,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAMsE,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAChBuB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEjC,UAAU,CAAC;;MAEzD;MACAgC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,MAAMC,MAAM,GAAG,MAAMjD,WAAW,CAACkD,OAAO,CAACnC,UAAU,EAAE,CAAC,EAAEsB,OAAO,CAAC;MAChEU,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,MAAM,GAAGA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC;MAChE/B,aAAa,CAAC6B,MAAM,IAAI,EAAE,CAAC;;MAE3B;MACAF,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,MAAMI,KAAK,GAAG,MAAMpD,WAAW,CAACkD,OAAO,CAACnC,UAAU,EAAE,CAAC,CAAC;MACtDgC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEI,KAAK,GAAGA,KAAK,CAACD,MAAM,GAAG,CAAC,CAAC;MAC7D,IAAIC,KAAK,IAAIA,KAAK,CAACD,MAAM,GAAG,CAAC,EAAE;QAC7BJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEI,KAAK,CAAC,CAAC,CAAC,CAAC;MAC5C;MACA9B,YAAY,CAAC8B,KAAK,IAAI,EAAE,CAAC;;MAEzB;MACAL,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzC,MAAMK,SAAS,GAAG,MAAMrD,WAAW,CAACsD,YAAY,CAACvC,UAAU,CAAC;MAC5DgC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEK,SAAS,CAAC;MAC/CrB,QAAQ,CAACqB,SAAS,CAAC;IACrB,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdsB,OAAO,CAACtB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDC,QAAQ,CAAC,oCAAoCD,KAAK,CAAC8B,OAAO,IAAI,oBAAoB,EAAE,CAAC;IACvF,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA/C,SAAS,CAAC,MAAM;IACd;IACA0D,sBAAsB,CAAC,CAAC;IAExB,MAAMqB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFT,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMS,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CZ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAACS,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACV/B,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAIoC,kBAAkB,GAAGF,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAIE,oBAAoB,GAAGH,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvEZ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAEY,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnGd,OAAO,CAACC,GAAG,CAAC,cAAc,EAAErC,IAAI,CAAC;;QAEjC;QACAoC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,CAACP,MAAM,EAAEW,CAAC,EAAE,EAAE;UAC5C,MAAMC,GAAG,GAAGL,YAAY,CAACK,GAAG,CAACD,CAAC,CAAC;UAC/Bf,OAAO,CAACC,GAAG,CAAC,GAAGe,GAAG,KAAKL,YAAY,CAACC,OAAO,CAACI,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAApD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqD,IAAI,MAAK,eAAe,EAAE;UAClCjB,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAIrC,IAAI,CAACsD,WAAW,EAAE;YACpBlB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAErC,IAAI,CAACsD,WAAW,CAAC;YACrEL,kBAAkB,GAAGjD,IAAI,CAACsD,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDL,oBAAoB,GAAGlD,IAAI,CAACwD,aAAa,IAAI,YAAYxD,IAAI,CAACsD,WAAW,EAAE;;YAE3E;YACAP,YAAY,CAACU,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;YAC9DF,YAAY,CAACU,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;YAClEd,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEY,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACFb,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAMS,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAIF,KAAK,EAAE;gBACT;gBACA,MAAMY,SAAS,GAAGZ,KAAK,CAACa,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvC1B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEiC,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvBlB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEiC,OAAO,CAAChB,WAAW,CAAC;kBACtEL,kBAAkB,GAAGqB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAL,oBAAoB,GAAG,YAAYoB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACAP,YAAY,CAACU,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;kBAC9DF,YAAY,CAACU,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;kBAClEd,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEY,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOwB,CAAC,EAAE;cACVrC,OAAO,CAACtB,KAAK,CAAC,6CAA6C,EAAE2D,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACxB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9Fb,OAAO,CAACsC,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACAzB,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACAH,YAAY,CAACU,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;UAC9DF,YAAY,CAACU,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;UAClEd,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEY,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvBlC,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAM8D,aAAa,GAAGC,QAAQ,CAAC3B,kBAAkB,EAAE,EAAE,CAAC;QACtDb,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEsC,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxB5D,QAAQ,CAAC,2BAA2BkC,kBAAkB,mCAAmC,CAAC;UAC1FpC,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAACsE,aAAa,CAAC;QAC5BpE,eAAe,CAAC2C,oBAAoB,IAAI,YAAYyB,aAAa,EAAE,CAAC;;QAEpE;QACA,IAAI;UACFpD,eAAe,CAAC,IAAI,CAAC;UACrBa,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEsC,aAAa,CAAC;UACxE,MAAMjC,SAAS,GAAG,MAAMrD,WAAW,CAACsD,YAAY,CAACgC,aAAa,CAAC;UAC/DvC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEK,SAAS,CAAC;UACpDrB,QAAQ,CAACqB,SAAS,CAAC;;UAEnB;UACA,IAAIA,SAAS,IAAIA,SAAS,CAACoC,KAAK,EAAE;YAChC,MAAMA,KAAK,GAAGpC,SAAS,CAACoC,KAAK,CAACb,GAAG,CAACc,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,CAACC,MAAM,CAACD,KAAK,IAAIA,KAAK,KAAK,iBAAiB,CAAC;YAClGvD,qBAAqB,CAACqD,KAAK,CAAC;UAC9B;UAEA,IAAIpC,SAAS,IAAIA,SAAS,CAACwC,SAAS,EAAE;YACpC,MAAMA,SAAS,GAAGxC,SAAS,CAACwC,SAAS,CAACjB,GAAG,CAACc,IAAI,IAAIA,IAAI,CAAClD,SAAS,CAAC,CAACoD,MAAM,CAACE,IAAI,IAAIA,IAAI,KAAK,iBAAiB,CAAC;YAC5GjD,gBAAgB,CAACgD,SAAS,CAAC;UAC7B;UAEA3D,eAAe,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC,OAAO6D,UAAU,EAAE;UACnBhD,OAAO,CAACtB,KAAK,CAAC,2CAA2C,EAAEsE,UAAU,CAAC;UACtE7D,eAAe,CAAC,KAAK,CAAC;UACtB;QACF;;QAEA;QACAa,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEsC,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMU,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACAtD,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEX,OAAO,CAAC;UAC1E,MAAMiE,WAAW,GAAGtG,WAAW,CAACkD,OAAO,CAACoC,aAAa,EAAE,CAAC,EAAEjD,OAAO,CAAC;UAClE,MAAMY,MAAM,GAAG,MAAMgD,OAAO,CAACM,IAAI,CAAC,CAACD,WAAW,EAAEN,cAAc,CAAC,CAAC;UAEhEjD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,MAAM,CAAC;UAC5CF,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEC,MAAM,GAAGA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC;UACzE,IAAIF,MAAM,IAAIA,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;YAC/BJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,MAAM;YACLF,OAAO,CAACsC,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;UACAlE,aAAa,CAAC6B,MAAM,IAAI,EAAE,CAAC;QAC7B,CAAC,CAAC,OAAOuD,SAAS,EAAE;UAClBzD,OAAO,CAACtB,KAAK,CAAC,yCAAyC,EAAE+E,SAAS,CAAC;UACnEzD,OAAO,CAACtB,KAAK,CAAC,8BAA8B,EAAE;YAC5C8B,OAAO,EAAEiD,SAAS,CAACjD,OAAO;YAC1BkD,MAAM,EAAED,SAAS,CAACC,MAAM;YACxBC,IAAI,EAAEF,SAAS,CAACE,IAAI;YACpBC,KAAK,EAAEH,SAAS,CAACG,KAAK;YACtBC,IAAI,EAAEJ,SAAS,CAACI,IAAI;YACpBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,QAAQ,EAAEN,SAAS,CAACM,QAAQ,GAAG;cAC7BL,MAAM,EAAED,SAAS,CAACM,QAAQ,CAACL,MAAM;cACjCM,UAAU,EAAEP,SAAS,CAACM,QAAQ,CAACC,UAAU;cACzCL,IAAI,EAAEF,SAAS,CAACM,QAAQ,CAACJ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACAtF,aAAa,CAAC,EAAE,CAAC;UACjB2B,OAAO,CAACsC,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACA3D,QAAQ,CAAC,2CAA2C8E,SAAS,CAACjD,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACAR,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEsC,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMU,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACAtD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD;UACA,MAAMgE,YAAY,GAAGhH,WAAW,CAACkD,OAAO,CAACoC,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAMlC,KAAK,GAAG,MAAM6C,OAAO,CAACM,IAAI,CAAC,CAACS,YAAY,EAAEhB,cAAc,CAAC,CAAC;UAEhEjD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEI,KAAK,CAAC;UAC1CL,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEI,KAAK,GAAGA,KAAK,CAACD,MAAM,GAAG,CAAC,CAAC;UACtE,IAAIC,KAAK,IAAIA,KAAK,CAACD,MAAM,GAAG,CAAC,EAAE;YAC7BJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEI,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACLL,OAAO,CAACsC,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACAhE,YAAY,CAAC8B,KAAK,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,OAAO6D,UAAU,EAAE;UACnBlE,OAAO,CAACtB,KAAK,CAAC,wCAAwC,EAAEwF,UAAU,CAAC;UACnElE,OAAO,CAACtB,KAAK,CAAC,6BAA6B,EAAE;YAC3C8B,OAAO,EAAE0D,UAAU,CAAC1D,OAAO;YAC3BkD,MAAM,EAAEQ,UAAU,CAACR,MAAM;YACzBC,IAAI,EAAEO,UAAU,CAACP,IAAI;YACrBC,KAAK,EAAEM,UAAU,CAACN,KAAK;YACvBC,IAAI,EAAEK,UAAU,CAACL,IAAI;YACrBC,IAAI,EAAEI,UAAU,CAACJ,IAAI;YACrBC,QAAQ,EAAEG,UAAU,CAACH,QAAQ,GAAG;cAC9BL,MAAM,EAAEQ,UAAU,CAACH,QAAQ,CAACL,MAAM;cAClCM,UAAU,EAAEE,UAAU,CAACH,QAAQ,CAACC,UAAU;cAC1CL,IAAI,EAAEO,UAAU,CAACH,QAAQ,CAACJ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACApF,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0CuF,UAAU,CAAC1D,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACA/B,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAO0F,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZzE,OAAO,CAACtB,KAAK,CAAC,kCAAkC,EAAEyF,GAAG,CAAC;QACtDnE,OAAO,CAACtB,KAAK,CAAC,2BAA2B,EAAE;UACzC8B,OAAO,EAAE2D,GAAG,CAAC3D,OAAO;UACpBkD,MAAM,EAAES,GAAG,CAACT,MAAM,MAAAU,aAAA,GAAID,GAAG,CAACJ,QAAQ,cAAAK,aAAA,uBAAZA,aAAA,CAAcV,MAAM;UAC1CC,IAAI,EAAEQ,GAAG,CAACR,IAAI,MAAAU,cAAA,GAAIF,GAAG,CAACJ,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcV,IAAI;UACpCC,KAAK,EAAEO,GAAG,CAACP;QACb,CAAC,CAAC;;QAEF;QACA,IAAIc,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAAC3D,OAAO,IAAI2D,GAAG,CAAC3D,OAAO,CAACmE,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjED,YAAY,GAAGP,GAAG,CAAC3D,OAAO;QAC5B,CAAC,MAAM,IAAI2D,GAAG,CAACT,MAAM,KAAK,GAAG,IAAIS,GAAG,CAACT,MAAM,KAAK,GAAG,IACzC,EAAAY,cAAA,GAAAH,GAAG,CAACJ,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcZ,MAAM,MAAK,GAAG,IAAI,EAAAa,cAAA,GAAAJ,GAAG,CAACJ,QAAQ,cAAAQ,cAAA,uBAAZA,cAAA,CAAcb,MAAM,MAAK,GAAG,EAAE;UACtEgB,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAACJ,QAAQ,cAAAS,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcb,IAAI,cAAAc,mBAAA,eAAlBA,mBAAA,CAAoBG,MAAM,EAAE;UACrC;UACAF,YAAY,GAAG,eAAeP,GAAG,CAACJ,QAAQ,CAACJ,IAAI,CAACiB,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIT,GAAG,CAACN,IAAI,KAAK,aAAa,EAAE;UACrC;UACAa,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAAC3D,OAAO,EAAE;UACtBkE,YAAY,GAAGP,GAAG,CAAC3D,OAAO;QAC5B;QAEA7B,QAAQ,CAAC,gCAAgC+F,YAAY,sBAAsB,CAAC;;QAE5E;QACArG,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDgC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACnB,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf;;EAEA;EACA,MAAMuF,iBAAiB,GAAIC,IAAI,IAAK;IAClCjG,eAAe,CAACiG,IAAI,CAAC;IACrB/F,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMgG,kBAAkB,GAAGA,CAAA,KAAM;IAC/BhG,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;;EAEA;;EAEA;;EAEA;EACA,MAAMmG,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACpG,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEvB,OAAA,CAACd,MAAM;MAAC0I,IAAI,EAAEnG,iBAAkB;MAACoG,OAAO,EAAEH,kBAAmB;MAACI,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAC,QAAA,gBACnFhI,OAAA,CAACb,WAAW;QAAA6I,QAAA,GAAC,iBACI,EAACzG,YAAY,CAAC0G,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACdrI,OAAA,CAACZ,aAAa;QAACkJ,QAAQ;QAAAN,QAAA,eACrBhI,OAAA,CAACtB,IAAI;UAAC6J,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAR,QAAA,gBACzBhI,OAAA,CAACtB,IAAI;YAAC4G,IAAI;YAACmD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvBhI,OAAA,CAACzB,UAAU;cAACoK,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/ErI,OAAA,CAAC1B,GAAG;cAACuK,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBhI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAACwH,OAAO,IAAI,KAAK;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAACyH,OAAO,IAAI,KAAK;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAACa,SAAS,IAAI,KAAK;cAAA;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtGrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAAC0H,WAAW,IAAI,KAAK;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrGrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAAC2H,YAAY,IAAI,KAAK;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC7GrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAAC4H,OAAO,IAAI,KAAK;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAAC6H,EAAE,IAAI,KAAK;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eAENrI,OAAA,CAACzB,UAAU;cAACoK,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClErI,OAAA,CAAC1B,GAAG;cAACuK,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBhI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAAC8H,mBAAmB,IAAI,KAAK;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjHrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAAC+H,eAAe,IAAI,KAAK;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzGrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAACgI,2BAA2B,IAAI,KAAK;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1HrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAACiI,qBAAqB,IAAI,KAAK;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrHrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAACkI,gBAAgB,IAAI,KAAK;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPrI,OAAA,CAACtB,IAAI;YAAC4G,IAAI;YAACmD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvBhI,OAAA,CAACzB,UAAU;cAACoK,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChErI,OAAA,CAAC1B,GAAG;cAACuK,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBhI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAACmI,iBAAiB,IAAI,KAAK;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/GrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAACoI,aAAa,IAAI,KAAK;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvGrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAACqI,yBAAyB,IAAI,KAAK;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxHrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAACsI,mBAAmB,IAAI,KAAK;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnHrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAACuI,cAAc,IAAI,KAAK;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CAAC,eAENrI,OAAA,CAACzB,UAAU;cAACoK,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvErI,OAAA,CAAC1B,GAAG;cAACuK,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBhI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAACwI,aAAa,IAAI,KAAK;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9GrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAACyI,eAAe,IAAI,GAAG;cAAA;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChHrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxI,2BAA2B,CAAC0B,YAAY,CAACY,mBAAmB,CAAC;cAAA;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChIrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAAC0I,YAAY,IAAI,GAAG;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1GrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAAC2I,SAAS,IAAI,KAAK;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnGrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAAC4I,iBAAiB,IAAI,KAAK;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtHrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9G,YAAY,CAAC6I,YAAY,IAAI,KAAK;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC5GrI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChI,OAAA;kBAAAgI,QAAA,EAAQ;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIgC,IAAI,CAAC9I,YAAY,CAAC+I,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBrI,OAAA,CAACX,aAAa;QAAA2I,QAAA,eACZhI,OAAA,CAACvB,MAAM;UAAC+L,OAAO,EAAE9C,kBAAmB;UAAAM,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;;EAEA;EACA,MAAMoC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAC9I,KAAK,EAAE,OAAO,IAAI;IAEvB,oBACE3B,OAAA,CAACxB,KAAK;MAACqK,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAE4B,CAAC,EAAE;MAAE,CAAE;MAAA1C,QAAA,gBACzBhI,OAAA,CAACzB,UAAU;QAACoK,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAZ,QAAA,EAAC;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAC7DxG,YAAY,gBACX7B,OAAA,CAACf,cAAc;QAAAiJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAElBrI,OAAA,CAACtB,IAAI;QAAC6J,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAR,QAAA,gBACzBhI,OAAA,CAACtB,IAAI;UAAC4G,IAAI;UAACmD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBhI,OAAA,CAACzB,UAAU;YAACoK,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChErI,OAAA,CAACzB,UAAU;YAACoK,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,eAAa,EAACrG,KAAK,CAACgJ,MAAM,CAACC,WAAW;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAChFrI,OAAA,CAACzB,UAAU;YAACoK,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,cAAY,EAACrG,KAAK,CAACgJ,MAAM,CAACE,UAAU;UAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC9ErI,OAAA,CAACzB,UAAU;YAACoK,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,eAAa,EAACrG,KAAK,CAACgJ,MAAM,CAACG,WAAW;UAAA;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE5E,CAAC,eAEPrI,OAAA,CAACtB,IAAI;UAAC4G,IAAI;UAACmD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBhI,OAAA,CAACzB,UAAU;YAACoK,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnErI,OAAA,CAACzB,UAAU;YAACoK,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,iBAAe,EAACrG,KAAK,CAACoJ,SAAS,CAACC,oBAAoB,CAACC,OAAO,CAAC,CAAC,CAAC;UAAA;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzGrI,OAAA,CAACzB,UAAU;YAACoK,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,gBAAc,EAACrG,KAAK,CAACoJ,SAAS,CAACG,kBAAkB,CAACD,OAAO,CAAC,CAAC,CAAC;UAAA;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACtGrI,OAAA,CAAC1B,GAAG;YAACuK,EAAE,EAAE;cAAEsC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAArD,QAAA,gBACxDhI,OAAA,CAAC1B,GAAG;cAACuK,EAAE,EAAE;gBAAEyC,KAAK,EAAE,MAAM;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAvD,QAAA,eAChChI,OAAA,CAACf,cAAc;gBACb0J,OAAO,EAAC,aAAa;gBACrB6C,KAAK,EAAE7J,KAAK,CAACoJ,SAAS,CAACU,yBAA0B;gBACjD5C,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEC,YAAY,EAAE;gBAAE;cAAE;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrI,OAAA,CAAC1B,GAAG;cAACuK,EAAE,EAAE;gBAAE+C,QAAQ,EAAE;cAAG,CAAE;cAAA5D,QAAA,eACxBhI,OAAA,CAACzB,UAAU;gBAACoK,OAAO,EAAC,OAAO;gBAACkD,KAAK,EAAC,gBAAgB;gBAAA7D,QAAA,EAAE,GAAGrG,KAAK,CAACoJ,SAAS,CAACU,yBAAyB,CAACR,OAAO,CAAC,CAAC,CAAC;cAAG;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPrI,OAAA,CAACtB,IAAI;UAAC4G,IAAI;UAACmD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBhI,OAAA,CAACzB,UAAU;YAACoK,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/DrI,OAAA,CAAC1B,GAAG;YAACuK,EAAE,EAAE;cAAEsC,OAAO,EAAE,MAAM;cAAEW,QAAQ,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAA/D,QAAA,EACpDrG,KAAK,CAAC0D,KAAK,CAACb,GAAG,CAAC,CAACe,KAAK,EAAEyG,KAAK,kBAC5BhM,OAAA,CAACjB,IAAI;cAEHkN,KAAK,EAAE,GAAG1G,KAAK,CAACA,KAAK,KAAKA,KAAK,CAAC2G,KAAK,EAAG;cACxCC,IAAI,EAAC,OAAO;cACZ3B,OAAO,EAAEA,CAAA,KAAM;gBACbtI,UAAU,CAACkK,IAAI,KAAK;kBAClB,GAAGA,IAAI;kBACPjK,mBAAmB,EAAEoD,KAAK,CAACA,KAAK,KAAK,iBAAiB,GAAG,EAAE,GAAGA,KAAK,CAACA;gBACtE,CAAC,CAAC,CAAC;cACL;YAAE,GARGyG,KAAK;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEZ,CAAC;EAED,oBACErI,OAAA,CAAC1B,GAAG;IAAC+N,SAAS,EAAC,WAAW;IAAArE,QAAA,EACvB7G,OAAO,gBACNnB,OAAA,CAAC1B,GAAG;MAACuK,EAAE,EAAE;QAAEsC,OAAO,EAAE,MAAM;QAAEmB,aAAa,EAAE,QAAQ;QAAElB,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAArD,QAAA,gBACjFhI,OAAA,CAAChB,gBAAgB;QAACmN,IAAI,EAAE;MAAG;QAAAjE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BrI,OAAA,CAACzB,UAAU;QAACsK,EAAE,EAAE;UAAEwC,EAAE,EAAE;QAAE,CAAE;QAAArD,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3DrI,OAAA,CAACvB,MAAM;QACLkK,OAAO,EAAC,UAAU;QAClBkD,KAAK,EAAC,SAAS;QACfrB,OAAO,EAAEA,CAAA,KAAM+B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxC5D,EAAE,EAAE;UAAEwC,EAAE,EAAE;QAAE,CAAE;QAAArD,QAAA,EACf;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJhH,KAAK,gBACPrB,OAAA,CAAC1B,GAAG;MAAA0J,QAAA,gBACFhI,OAAA,CAACnB,KAAK;QAAC6N,QAAQ,EAAC,OAAO;QAAC7D,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,GACnC3G,KAAK,EACLA,KAAK,CAACiG,QAAQ,CAAC,eAAe,CAAC,iBAC9BtH,OAAA,CAACzB,UAAU;UAACoK,OAAO,EAAC,OAAO;UAACE,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAArD,QAAA,gBACxChI,OAAA;YAAAgI,QAAA,EAAQ;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAArI,OAAA;YAAAkI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAArI,OAAA;YAAAgI,QAAA,EAAM;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACRrI,OAAA,CAAC1B,GAAG;QAACuK,EAAE,EAAE;UAAEsC,OAAO,EAAE,MAAM;UAAEY,GAAG,EAAE;QAAE,CAAE;QAAA/D,QAAA,eACnChI,OAAA,CAACvB,MAAM;UACLkK,OAAO,EAAC,WAAW;UACnB0D,SAAS,EAAC,gBAAgB;UAC1B7B,OAAO,EAAEA,CAAA,KAAM+B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAzE,QAAA,EACzC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENrI,OAAA,CAAC1B,GAAG;MAAA0J,QAAA,GAIDyC,gBAAgB,CAAC,CAAC,EAElB1J,UAAU,CAACgC,MAAM,GAAG,CAAC,iBACpB/C,OAAA,CAAC1B,GAAG;QAACuK,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,GAEhB2E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrC7M,OAAA,CAAC1B,GAAG;UAACuK,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAE4B,CAAC,EAAE,CAAC;YAAEoC,OAAO,EAAE,SAAS;YAAEnB,YAAY,EAAE,CAAC;YAAEoB,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE,WAAW;YAAE7B,OAAO,EAAE;UAAO,CAAE;UAAAnD,QAAA,EACzHiF,MAAM,CAACC,IAAI,CAACnM,UAAU,CAAC,CAAC,CAAC,CAAC,CAACyD,GAAG,CAACb,GAAG,iBACjC3D,OAAA;YAAAgI,QAAA,GAAgBrE,GAAG,EAAC,IAAE,EAACmB,IAAI,CAACqI,SAAS,CAACpM,UAAU,CAAC,CAAC,CAAC,CAAC4C,GAAG,CAAC,CAAC;UAAA,GAA/CA,GAAG;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkD,CAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eACDrI,OAAA,CAACF,mBAAmB;UAClBsN,IAAI,EAAErM,UAAW;UACjBI,OAAO,EAAEA,OAAQ;UACjBkM,oBAAoB,EAAGC,YAAY,IAAK3K,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0K,YAAY,CAACvK,MAAM,CAAE;UAClGwK,iBAAiB,EAAE,EAAApN,YAAA,GAAAY,UAAU,CAAC,CAAC,CAAC,cAAAZ,YAAA,uBAAbA,YAAA,CAAeqN,mBAAmB,OAAApN,aAAA,GAAIW,UAAU,CAAC,CAAC,CAAC,cAAAX,aAAA,uBAAbA,aAAA,CAAeqN,SAAS,OAAApN,aAAA,GAAIU,UAAU,CAAC,CAAC,CAAC,cAAAV,aAAA,uBAAbA,aAAA,CAAeqN,GAAG;QAAC;UAAAxF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,eAGDrI,OAAA,CAAC1B,GAAG;QAACuK,EAAE,EAAE;UAAEwC,EAAE,EAAE;QAAE,CAAE;QAAArD,QAAA,gBACjBhI,OAAA,CAAC1B,GAAG;UAACuK,EAAE,EAAE;YAAEsC,OAAO,EAAE,MAAM;YAAEwC,cAAc,EAAE,eAAe;YAAEvC,UAAU,EAAE,QAAQ;YAAEtC,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACzFhI,OAAA,CAACzB,UAAU;YAACoK,OAAO,EAAC,IAAI;YAAAX,QAAA,GAAC,aACZ,EAAC/G,SAAS,CAAC8B,MAAM,GAAG,CAAC,GAAG,IAAI9B,SAAS,CAAC8B,MAAM,GAAG,GAAG,EAAE;UAAA;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACbrI,OAAA,CAACvB,MAAM;YACLkK,OAAO,EAAC,UAAU;YAClBwD,IAAI,EAAC,OAAO;YACZ3B,OAAO,EAAE9H,SAAU;YACnBkL,SAAS,EAAEzM,OAAO,gBAAGnB,OAAA,CAAChB,gBAAgB;cAACmN,IAAI,EAAE;YAAG;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGrI,OAAA,CAACT,WAAW;cAAA2I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtEwF,QAAQ,EAAE1M,OAAQ;YAAA6G,QAAA,EACnB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACLpH,SAAS,CAAC8B,MAAM,GAAG,CAAC,gBACnB/C,OAAA,CAACF,mBAAmB;UAClBsN,IAAI,EAAEnM,SAAU;UAChBE,OAAO,EAAEA,OAAQ;UACjBkM,oBAAoB,EAAGC,YAAY,IAAK3K,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE0K,YAAY,CAACvK,MAAM;QAAE;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC,gBAEFrI,OAAA,CAACnB,KAAK;UAAC6N,QAAQ,EAAC,MAAM;UAAC7D,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,EAAC;QAEtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLV,mBAAmB,CAAC,CAAC,eAGtB3H,OAAA,CAACd,MAAM;QACL0I,IAAI,EAAEpH,qBAAsB;QAC5BqH,OAAO,EAAEA,CAAA,KAAMpH,wBAAwB,CAAC,KAAK,CAAE;QAC/CsH,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAE,QAAA,eAEbhI,OAAA,CAACL,oBAAoB;UACnBgB,UAAU,EAAEA,UAAW;UACvBmN,SAAS,EAAG3K,OAAO,IAAK;YACtB;YACAR,OAAO,CAACC,GAAG,CAACO,OAAO,CAAC;YACpB;YACA1C,wBAAwB,CAAC,KAAK,CAAC;YAC/B;YACAiC,SAAS,CAAC,CAAC;UACb,CAAE;UACFqL,OAAO,EAAG5K,OAAO,IAAK;YACpB;YACAR,OAAO,CAACtB,KAAK,CAAC8B,OAAO,CAAC;YACtB;YACA1C,wBAAwB,CAAC,KAAK,CAAC;UACjC,CAAE;UACFuN,aAAa,EAAC;QAAa;UAAA9F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnI,EAAA,CA1mBID,kBAAkB;EAAA,QACYR,OAAO,EACmBC,gBAAgB,EAC3DF,WAAW;AAAA;AAAAyO,EAAA,GAHxBhO,kBAAkB;AA4mBxB,eAAeA,kBAAkB;AAAC,IAAAgO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}