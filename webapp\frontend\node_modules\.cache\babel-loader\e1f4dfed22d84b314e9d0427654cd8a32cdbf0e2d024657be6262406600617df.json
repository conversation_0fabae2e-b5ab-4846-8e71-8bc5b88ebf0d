{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\MetriPosatiSemplificatoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, TextField, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, CircularProgress, Alert, Chip, Divider, Grid, FormControl, InputLabel, Select, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, DialogContentText } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione ultra-semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MetriPosatiSemplificatoForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per il caricamento\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReelData, setIncompatibleReelData] = useState({\n    cavo: null,\n    bobina: null\n  });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n\n  // Carica la lista dei cavi e delle bobine all'avvio\n  useEffect(() => {\n    loadCavi();\n    loadBobine();\n  }, [cantiereId]);\n\n  // Carica la lista dei cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi che non sono SPARE\n      const caviAttivi = caviData.filter(cavo => !isCableSpare(cavo));\n      setCavi(caviAttivi);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Carica la lista delle bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine per cantiere:', cantiereId);\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log('Bobine caricate:', bobineData);\n      console.log('Dettaglio bobine:');\n      bobineData.forEach(bobina => {\n        console.log(`Bobina ${bobina.id_bobina}:`, {\n          tipologia: bobina.tipologia,\n          sezione: bobina.sezione,\n          metri_residui: bobina.metri_residui,\n          stato_bobina: bobina.stato_bobina\n        });\n      });\n      console.log('IMPORTANTE: Impostazione delle bobine nello stato...');\n      setBobine(bobineData);\n      console.log('Bobine impostate nello stato:', bobineData.length);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    console.log('Cavo selezionato:', cavo);\n    console.log('Dettaglio cavo:', {\n      id_cavo: cavo.id_cavo,\n      tipologia: cavo.tipologia,\n      sezione: cavo.sezione,\n      metri_teorici: cavo.metri_teorici,\n      stato_installazione: cavo.stato_installazione\n    });\n\n    // Verifica se il cavo è già posato\n    if (isCableInstalled(cavo)) {\n      console.log('Cavo già posato, mostro dialog');\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    console.log('Impostazione selectedCavo...');\n    setSelectedCavo(cavo);\n    console.log('selectedCavo impostato a:', cavo.id_cavo);\n    setFormData({\n      id_cavo: cavo.id_cavo,\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n\n    // Log per debug - verifica se ci sono bobine compatibili\n    console.log('Verifica bobine compatibili per il cavo selezionato...');\n\n    // Forza il filtraggio delle bobine compatibili\n    const compatibleBobineList = filterCompatibleBobine(cavo);\n    console.log('Bobine compatibili trovate (forzato):', compatibleBobineList.length);\n    setCompatibleBobine(compatibleBobineList);\n  };\n\n  // Gestisce la modifica dei campi del form\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un singolo campo\n  const validateField = (name, value) => {\n    const newErrors = {\n      ...formErrors\n    };\n    const newWarnings = {\n      ...formWarnings\n    };\n    if (name === 'metri_posati') {\n      // Validazione metri posati\n      if (value === '') {\n        newErrors.metri_posati = 'I metri posati sono obbligatori';\n      } else if (isNaN(value) || parseFloat(value) < 0) {\n        newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n      } else {\n        delete newErrors.metri_posati;\n\n        // Avvisi sui metri posati\n        const metriPosati = parseFloat(value);\n        if (selectedCavo && metriPosati > selectedCavo.metri_teorici) {\n          newWarnings.metri_posati = `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`;\n        } else {\n          delete newWarnings.metri_posati;\n        }\n\n        // Avvisi sulla bobina selezionata\n        if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n          const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n    if (name === 'id_bobina') {\n      // Validazione bobina\n      if (value === '') {\n        newErrors.id_bobina = 'La bobina è obbligatoria';\n      } else {\n        delete newErrors.id_bobina;\n\n        // Avvisi sulla bobina selezionata\n        if (value !== 'BOBINA_VUOTA' && formData.metri_posati) {\n          const metriPosati = parseFloat(formData.metri_posati);\n          const selectedBobina = bobine.find(b => b.id_bobina === value);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n    setFormErrors(newErrors);\n    setFormWarnings(newWarnings);\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati) {\n      newErrors.metri_posati = 'I metri posati sono obbligatori';\n    } else if (isNaN(formData.metri_posati) || parseFloat(formData.metri_posati) < 0) {\n      newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina) {\n      newErrors.id_bobina = 'La bobina è obbligatoria';\n    }\n    setFormErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Verifica la compatibilità tra cavo e bobina\n  const checkCompatibility = () => {\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      return true; // BOBINA_VUOTA è sempre compatibile\n    }\n    const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n    if (!selectedBobina) {\n      return false;\n    }\n\n    // Verifica compatibilità tipologia\n    const tipologiaCompatibile = selectedCavo.tipologia === selectedBobina.tipologia;\n\n    // Verifica compatibilità sezione\n    const sezioneCompatibile = String(selectedCavo.sezione) === String(selectedBobina.sezione);\n    return tipologiaCompatibile && sezioneCompatibile;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n    if (bobine.length === 0 && !bobineLoading) {\n      if (!formData.metri_posati || isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n        setFormErrors({\n          ...formErrors,\n          metri_posati: 'I metri posati sono obbligatori e devono essere maggiori di zero'\n        });\n        return;\n      }\n\n      // Imposta BOBINA_VUOTA e procedi con il salvataggio\n      formData.id_bobina = 'BOBINA_VUOTA';\n    } else {\n      // Validazione completa\n      if (!validateForm()) {\n        return;\n      }\n\n      // Verifica compatibilità\n      if (!checkCompatibility()) {\n        // Mostra dialog per incompatibilità\n        const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        setIncompatibleReelData({\n          cavo: selectedCavo,\n          bobina: selectedBobina\n        });\n        setShowIncompatibleReelDialog(true);\n        return;\n      }\n    }\n\n    // Procedi con il salvataggio\n    try {\n      setSaving(true);\n\n      // Converti metri posati in numero\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', formData.id_bobina);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, formData.id_bobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Metri posati aggiornati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce l'aggiornamento del cavo per compatibilità\n  const handleUpdateCavoForCompatibility = async () => {\n    try {\n      setSaving(true);\n      setShowIncompatibleReelDialog(false);\n      const {\n        cavo,\n        bobina\n      } = incompatibleReelData;\n\n      // Aggiorna il cavo per renderlo compatibile con la bobina\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, {\n        id_bobina: bobina.id_bobina,\n        tipologia: bobina.tipologia,\n        sezione: bobina.sezione\n      });\n\n      // Procedi con l'aggiornamento dei metri posati\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, parseFloat(formData.metri_posati), formData.id_bobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Cavo aggiornato e metri posati registrati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento del cavo:', error);\n      onError('Errore durante l\\'aggiornamento del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce la selezione di un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/posa/modifica-bobina?cavoId=${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Stato per le bobine compatibili\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n\n  // Aggiorna le bobine compatibili quando viene selezionato un cavo o cambiano le bobine disponibili\n  useEffect(() => {\n    console.log('useEffect per bobine compatibili - selectedCavo:', selectedCavo ? selectedCavo.id_cavo : 'nessuno');\n    console.log('useEffect per bobine compatibili - bobine:', bobine.length);\n    if (selectedCavo) {\n      // Filtra le bobine compatibili localmente\n      console.log('Chiamata a filterCompatibleBobine...');\n      const filtered = filterCompatibleBobine(selectedCavo);\n      console.log('Risultato di filterCompatibleBobine:', filtered.length);\n      setCompatibleBobine(filtered);\n      console.log('compatibleBobine impostato a:', filtered.length);\n    } else {\n      setCompatibleBobine([]);\n      console.log('compatibleBobine impostato a vuoto');\n    }\n  }, [selectedCavo, bobine]);\n\n  // Filtra le bobine compatibili localmente\n  const filterCompatibleBobine = cavo => {\n    if (!cavo) return [];\n    console.log('Filtrando bobine compatibili per cavo:', cavo);\n    console.log('Bobine disponibili:', bobine);\n    console.log('Numero di bobine disponibili:', bobine.length);\n\n    // Verifica se ci sono bobine disponibili\n    if (bobine.length === 0) {\n      console.log('ATTENZIONE: Nessuna bobina disponibile nel cantiere!');\n      return [];\n    }\n\n    // Filtra le bobine compatibili con il cavo\n    const filtered = bobine.filter(bobina => {\n      // Normalizza i valori per un confronto più robusto\n      // 1. Normalizza tipologia (trim e lowercase)\n      // 2. Normalizza sezione (trim e conversione in stringa)\n      // 3. Verifica stato bobina e metri residui\n      const cavoTipologiaNorm = String(cavo.tipologia || '').trim().toLowerCase();\n      const cavoSezioneNorm = String(cavo.sezione || '').trim();\n      const bobinaTipologiaNorm = String(bobina.tipologia || '').trim().toLowerCase();\n      const bobinaSezioneNorm = String(bobina.sezione || '').trim();\n\n      // Verifica compatibilità\n      const tipologiaMatch = bobinaTipologiaNorm === cavoTipologiaNorm;\n      const sezioneMatch = bobinaSezioneNorm === cavoSezioneNorm;\n\n      // Verifica stato bobina direttamente invece di usare determineReelState\n      // Questo è più affidabile e corrisponde alla logica del backend\n      const stateOk = bobina.stato_bobina !== 'Terminata' && bobina.stato_bobina !== 'Over';\n\n      // Verifica che i metri residui siano positivi\n      const metriOk = bobina.metri_residui > 0;\n      const isCompatible = tipologiaMatch && sezioneMatch && stateOk && metriOk;\n\n      // Log dettagliati per debug\n      console.log(`Confronto dettagliato per bobina ${bobina.id_bobina}:`);\n      console.log(`- Tipologia bobina (originale): \"${bobina.tipologia}\", tipo: ${typeof bobina.tipologia}`);\n      console.log(`- Tipologia cavo (originale): \"${cavo.tipologia}\", tipo: ${typeof cavo.tipologia}`);\n      console.log(`- Tipologia bobina (normalizzata): \"${bobinaTipologiaNorm}\"`);\n      console.log(`- Tipologia cavo (normalizzata): \"${cavoTipologiaNorm}\"`);\n      console.log(`- Sezione bobina (originale): \"${bobina.sezione}\", tipo: ${typeof bobina.sezione}`);\n      console.log(`- Sezione cavo (originale): \"${cavo.sezione}\", tipo: ${typeof cavo.sezione}`);\n      console.log(`- Sezione bobina (normalizzata): \"${bobinaSezioneNorm}\"`);\n      console.log(`- Sezione cavo (normalizzata): \"${cavoSezioneNorm}\"`);\n      console.log(`- Stato bobina: ${bobina.stato_bobina}`);\n      console.log(`- Metri residui: ${bobina.metri_residui}`);\n      console.log(`- Stato OK? ${stateOk}`);\n      console.log(`- Metri OK? ${metriOk}`);\n\n      // Log di riepilogo\n      console.log(`Bobina ${bobina.id_bobina}:`, {\n        'Tipologia bobina': `\"${bobina.tipologia}\"`,\n        'Tipologia cavo': `\"${cavo.tipologia}\"`,\n        'Tipologie uguali?': tipologiaMatch,\n        'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n        'Sezione cavo': `\"${String(cavo.sezione)}\"`,\n        'Sezioni uguali?': sezioneMatch,\n        'Stato bobina': bobina.stato_bobina,\n        'Metri residui': bobina.metri_residui,\n        'Stato OK?': stateOk,\n        'Metri OK?': metriOk,\n        'Compatibile?': isCompatible\n      });\n      return isCompatible;\n    });\n    console.log('Bobine compatibili trovate:', filtered.length);\n    if (filtered.length > 0) {\n      console.log('Prima bobina compatibile:', filtered[0]);\n    } else {\n      console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n    }\n    return filtered;\n  };\n\n  // Funzione di utilità per ottenere le bobine compatibili (usata nel rendering)\n  const getCompatibleBobine = () => {\n    return compatibleBobine;\n  };\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    if (caviLoading) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 9\n      }, this);\n    }\n    if (cavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          my: 2\n        },\n        children: \"Nessun cavo disponibile per questo cantiere.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 2\n        },\n        children: \"Seleziona un cavo dalla tabella per inserire i metri posati. I cavi gi\\xE0 installati sono disabilitati.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                bgcolor: '#e3f2fd'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => {\n              const isInstalled = isCableInstalled(cavo);\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  bgcolor: isInstalled ? '#f5f5f5' : 'inherit',\n                  '&:hover': {\n                    bgcolor: isInstalled ? '#f5f5f5' : '#f1f8e9'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 32\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 71\n                  }, this), \"A: \", cavo.ubicazione_arrivo || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [cavo.metri_teorici || 'N/A', \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: cavo.stato_installazione || 'N/D',\n                    size: \"small\",\n                    color: getCableStateColor(cavo.stato_installazione),\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 589,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    variant: \"contained\",\n                    color: \"primary\",\n                    onClick: () => handleCavoSelect(cavo),\n                    disabled: isInstalled,\n                    children: isInstalled ? 'Già installato' : 'Seleziona'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza il form per inserimento metri e selezione bobina\n  const renderForm = () => {\n    if (!selectedCavo) return null;\n\n    // Log per debug - verifica le bobine compatibili nel rendering\n    const compatibleBobineList = getCompatibleBobine();\n    console.log('Rendering form - Bobine compatibili:', compatibleBobineList);\n    console.log('Rendering form - Numero di bobine compatibili:', compatibleBobineList.length);\n\n    // Verifica se ci sono bobine disponibili\n    if (bobine.length === 0 && !bobineLoading) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Inserimento metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 3\n          },\n          children: \"Non ci sono bobine disponibili nel cantiere. Puoi comunque registrare i metri posati utilizzando l'opzione \\\"BOBINA VUOTA\\\".\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 1.5,\n            mb: 3,\n            width: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 'bold',\n                whiteSpace: 'nowrap',\n                mr: 2\n              },\n              children: [\"Cavo selezionato: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#1976d2'\n                },\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 35\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2,\n                flexWrap: 'wrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.85rem',\n                    mr: 0.5\n                  },\n                  children: \"Tipo:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.85rem'\n                  },\n                  children: selectedCavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.85rem',\n                    mr: 0.5\n                  },\n                  children: \"Form:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.85rem'\n                  },\n                  children: selectedCavo.sezione || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.85rem',\n                    mr: 0.5\n                  },\n                  children: \"Metri:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.85rem'\n                  },\n                  children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 655,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.85rem',\n                    mr: 0.5\n                  },\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: selectedCavo.stato_installazione || 'N/D',\n                  color: getCableStateColor(selectedCavo.stato_installazione),\n                  sx: {\n                    height: '20px',\n                    '& .MuiChip-label': {\n                      px: 1,\n                      py: 0,\n                      fontSize: '0.75rem'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Metri posati\",\n              name: \"metri_posati\",\n              value: formData.metri_posati,\n              onChange: handleInputChange,\n              type: \"number\",\n              InputProps: {\n                inputProps: {\n                  min: 0,\n                  step: 0.1\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                name: \"id_bobina\",\n                value: \"BOBINA_VUOTA\",\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"BOBINA_VUOTA\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: '#2e7d32',\n                    bgcolor: '#f1f8e9'\n                  },\n                  children: \"BOBINA VUOTA (Cavo posato senza bobina)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                sx: {\n                  mt: 1\n                },\n                children: \"Non ci sono bobine disponibili. Verr\\xE0 utilizzata l'opzione BOBINA VUOTA.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            display: 'flex',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"secondary\",\n            onClick: () => {\n              setSelectedCavo(null);\n              setFormData({\n                id_cavo: '',\n                metri_posati: '',\n                id_bobina: ''\n              });\n            },\n            disabled: saving,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 704,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: () => handleSave(),\n            disabled: saving || !formData.metri_posati,\n            children: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 25\n            }, this) : 'Salva'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 719,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 703,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 9\n      }, this);\n    }\n    const compatibleBobine = getCompatibleBobine();\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserimento metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 736,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 3\n        },\n        children: [\"Inserisci i metri posati per il cavo selezionato e associa una bobina. Se il cavo \\xE8 stato posato senza una bobina specifica, seleziona \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"BOBINA VUOTA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 146\n        }, this), \".\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 740,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1.5,\n          mb: 3,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 'bold',\n              whiteSpace: 'nowrap',\n              mr: 2,\n              fontSize: '0.9rem'\n            },\n            children: [\"Cavo selezionato: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#1976d2'\n              },\n              children: selectedCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 3,\n              flexWrap: 'wrap',\n              ml: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                whiteSpace: 'nowrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'medium',\n                  fontSize: '0.85rem',\n                  mr: 0.5\n                },\n                children: \"Tipo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontSize: '0.85rem'\n                },\n                children: selectedCavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                whiteSpace: 'nowrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'medium',\n                  fontSize: '0.85rem',\n                  mr: 0.5\n                },\n                children: \"Form:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontSize: '0.85rem'\n                },\n                children: selectedCavo.sezione || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 757,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                whiteSpace: 'nowrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'medium',\n                  fontSize: '0.85rem',\n                  mr: 0.5\n                },\n                children: \"Metri:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontSize: '0.85rem'\n                },\n                children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                whiteSpace: 'nowrap'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'medium',\n                  fontSize: '0.85rem',\n                  mr: 0.5\n                },\n                children: \"Stato:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                size: \"small\",\n                label: selectedCavo.stato_installazione || 'N/D',\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                sx: {\n                  height: '20px',\n                  '& .MuiChip-label': {\n                    px: 1,\n                    py: 0,\n                    fontSize: '0.8rem'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 776,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Metri posati\",\n            name: \"metri_posati\",\n            value: formData.metri_posati,\n            onChange: handleInputChange,\n            type: \"number\",\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: 'orange'\n              },\n              children: formWarnings.metri_posati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 789,\n              columnNumber: 17\n            }, this),\n            disabled: saving,\n            InputProps: {\n              inputProps: {\n                min: 0,\n                step: 0.1\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            error: !!formErrors.id_bobina,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              name: \"id_bobina\",\n              value: formData.id_bobina,\n              onChange: handleInputChange,\n              disabled: saving || bobineLoading,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BOBINA_VUOTA\",\n                sx: {\n                  fontWeight: 'bold',\n                  color: '#2e7d32',\n                  bgcolor: '#f1f8e9'\n                },\n                children: \"BOBINA VUOTA (Cavo posato senza bobina)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 807,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 17\n              }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 20,\n                    sx: {\n                      mr: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 818,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    children: \"Caricamento bobine...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 819,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 817,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 19\n              }, this) : compatibleBobine.length === 0 ? /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: \"Nessuna bobina compatibile disponibile. Utilizzare BOBINA VUOTA.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: [\"Bobine compatibili (\", compatibleBobine.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 832,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 19\n              }, this), !bobineLoading && (() => {\n                console.log('Rendering Select - Bobine compatibili:', compatibleBobine);\n                console.log('Rendering Select - Numero di bobine compatibili:', compatibleBobine.length);\n                return compatibleBobine.map(bobina => {\n                  console.log('Rendering bobina compatibile:', bobina);\n                  return /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: bobina.id_bobina,\n                    children: [bobina.id_bobina, \" - \", bobina.tipologia, \" - \", bobina.metri_residui, \"m\"]\n                  }, bobina.id_bobina, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 23\n                  }, this);\n                });\n              })(), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: '#ff9800'\n                  },\n                  children: \"TUTTE LE BOBINE DISPONIBILI (Ignora compatibilit\\xE0)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 859,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 17\n              }, this), bobine.filter(bobina => bobina.stato_bobina !== 'Terminata').map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: bobina.id_bobina,\n                children: [bobina.id_bobina, \" - \", bobina.tipologia, \" - \", bobina.sezione, \" - \", bobina.metri_residui, \"m\"]\n              }, bobina.id_bobina, true, {\n                fileName: _jsxFileName,\n                lineNumber: 866,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 800,\n              columnNumber: 15\n            }, this), formErrors.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"error\",\n              children: formErrors.id_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 17\n            }, this), formWarnings.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'orange'\n              },\n              children: formWarnings.id_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 877,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              sx: {\n                mt: 1\n              },\n              children: \"Seleziona una bobina o usa BOBINA VUOTA se il cavo \\xE8 stato posato senza una bobina specifica.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 778,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          onClick: () => {\n            setSelectedCavo(null);\n            setFormData({\n              id_cavo: '',\n              metri_posati: '',\n              id_bobina: ''\n            });\n          },\n          disabled: saving,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 890,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSave,\n          disabled: saving || Object.keys(formErrors).length > 0,\n          children: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 911,\n            columnNumber: 23\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 905,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 889,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 735,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [!selectedCavo && renderCaviTable(), renderForm(), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: () => setShowIncompatibleReelDialog(false),\n      cavo: incompatibleReelData.cavo,\n      bobina: incompatibleReelData.bobina,\n      onUpdateCavo: handleUpdateCavoForCompatibility,\n      onSelectAnotherReel: () => {\n        setShowIncompatibleReelDialog(false);\n        setFormData(prev => ({\n          ...prev,\n          id_bobina: ''\n        }));\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 927,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Cavo gi\\xE0 posato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 941,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"Il cavo \", alreadyLaidCavo === null || alreadyLaidCavo === void 0 ? void 0 : alreadyLaidCavo.id_cavo, \" \\xE8 gi\\xE0 stato posato.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 943,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            gutterBottom: true,\n            children: \"Puoi:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 947,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"ul\",\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 951,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 953,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 950,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 946,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 942,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 958,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 962,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 961,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 957,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 940,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 919,\n    columnNumber: 5\n  }, this);\n};\n_s(MetriPosatiSemplificatoForm, \"tIpFGuuJh3Bk4ST3cUZrN28YjYA=\", false, function () {\n  return [useNavigate];\n});\n_c = MetriPosatiSemplificatoForm;\nexport default MetriPosatiSemplificatoForm;\nvar _c;\n$RefreshReg$(_c, \"MetriPosatiSemplificatoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "CircularProgress", "<PERSON><PERSON>", "Chip", "Divider", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "DialogContentText", "useNavigate", "caviService", "parcoCaviService", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "redirectToVisualizzaCavi", "IncompatibleReelDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MetriPosatiSemplificatoForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "saving", "setSaving", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReelData", "setIncompatibleReelData", "cavo", "bobina", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "alreadyLaidCavo", "setAlreadyLaidCavo", "loadCavi", "loadBobine", "caviData", "get<PERSON><PERSON>", "caviAttivi", "filter", "error", "console", "message", "log", "bobine<PERSON><PERSON>", "getBobine", "for<PERSON>ach", "tipologia", "sezione", "metri_residui", "stato_bobina", "length", "handleCavoSelect", "metri_te<PERSON>ci", "stato_installazione", "compatibleBobineList", "filterCompatibleBobine", "setCompatibleBobine", "handleInputChange", "e", "name", "value", "target", "prev", "validateField", "newErrors", "newWarnings", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "find", "b", "validateForm", "Object", "keys", "checkCompatibility", "tipologiaCompatibile", "sezioneCompatibile", "String", "handleSave", "updateMetri<PERSON><PERSON><PERSON>", "handleUpdateCavoForCompatibility", "updateCavoForCompatibility", "handleCloseAlreadyLaidDialog", "handleSelectAnotherCable", "handleModifyReel", "compatibleBobine", "filtered", "cavoTipologiaNorm", "trim", "toLowerCase", "cavoSezioneNorm", "bobinaTipologiaNorm", "bobinaSezioneNorm", "tipologiaMatch", "sezioneMatch", "stateOk", "metriOk", "isCompatible", "getCompatibleBobine", "renderCaviTable", "sx", "display", "justifyContent", "my", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "mb", "component", "size", "bgcolor", "map", "isInstalled", "ubicazione_partenza", "ubicazione_arrivo", "label", "color", "variant", "onClick", "disabled", "renderForm", "p", "gutterBottom", "width", "alignItems", "fontWeight", "whiteSpace", "mr", "style", "gap", "flexWrap", "fontSize", "height", "px", "py", "container", "spacing", "item", "xs", "md", "fullWidth", "onChange", "type", "InputProps", "inputProps", "min", "step", "mt", "ml", "helperText", "open", "onClose", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/MetriPosatiSemplificatoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  TextField,\n  Button,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  CircularProgress,\n  Alert,\n  Chip,\n  Divider,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  DialogContentText\n} from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione ultra-semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst MetriPosatiSemplificatoForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per il caricamento\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n\n  // Carica la lista dei cavi e delle bobine all'avvio\n  useEffect(() => {\n    loadCavi();\n    loadBobine();\n  }, [cantiereId]);\n\n  // Carica la lista dei cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi che non sono SPARE\n      const caviAttivi = caviData.filter(cavo => !isCableSpare(cavo));\n\n      setCavi(caviAttivi);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Carica la lista delle bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine per cantiere:', cantiereId);\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log('Bobine caricate:', bobineData);\n      console.log('Dettaglio bobine:');\n      bobineData.forEach(bobina => {\n        console.log(`Bobina ${bobina.id_bobina}:`, {\n          tipologia: bobina.tipologia,\n          sezione: bobina.sezione,\n          metri_residui: bobina.metri_residui,\n          stato_bobina: bobina.stato_bobina\n        });\n      });\n      console.log('IMPORTANTE: Impostazione delle bobine nello stato...');\n      setBobine(bobineData);\n      console.log('Bobine impostate nello stato:', bobineData.length);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    console.log('Cavo selezionato:', cavo);\n    console.log('Dettaglio cavo:', {\n      id_cavo: cavo.id_cavo,\n      tipologia: cavo.tipologia,\n      sezione: cavo.sezione,\n      metri_teorici: cavo.metri_teorici,\n      stato_installazione: cavo.stato_installazione\n    });\n\n    // Verifica se il cavo è già posato\n    if (isCableInstalled(cavo)) {\n      console.log('Cavo già posato, mostro dialog');\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n\n    console.log('Impostazione selectedCavo...');\n    setSelectedCavo(cavo);\n    console.log('selectedCavo impostato a:', cavo.id_cavo);\n\n    setFormData({\n      id_cavo: cavo.id_cavo,\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n\n    // Log per debug - verifica se ci sono bobine compatibili\n    console.log('Verifica bobine compatibili per il cavo selezionato...');\n\n    // Forza il filtraggio delle bobine compatibili\n    const compatibleBobineList = filterCompatibleBobine(cavo);\n    console.log('Bobine compatibili trovate (forzato):', compatibleBobineList.length);\n    setCompatibleBobine(compatibleBobineList);\n  };\n\n  // Gestisce la modifica dei campi del form\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un singolo campo\n  const validateField = (name, value) => {\n    const newErrors = { ...formErrors };\n    const newWarnings = { ...formWarnings };\n\n    if (name === 'metri_posati') {\n      // Validazione metri posati\n      if (value === '') {\n        newErrors.metri_posati = 'I metri posati sono obbligatori';\n      } else if (isNaN(value) || parseFloat(value) < 0) {\n        newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n      } else {\n        delete newErrors.metri_posati;\n\n        // Avvisi sui metri posati\n        const metriPosati = parseFloat(value);\n        if (selectedCavo && metriPosati > selectedCavo.metri_teorici) {\n          newWarnings.metri_posati = `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`;\n        } else {\n          delete newWarnings.metri_posati;\n        }\n\n        // Avvisi sulla bobina selezionata\n        if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n          const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n\n    if (name === 'id_bobina') {\n      // Validazione bobina\n      if (value === '') {\n        newErrors.id_bobina = 'La bobina è obbligatoria';\n      } else {\n        delete newErrors.id_bobina;\n\n        // Avvisi sulla bobina selezionata\n        if (value !== 'BOBINA_VUOTA' && formData.metri_posati) {\n          const metriPosati = parseFloat(formData.metri_posati);\n          const selectedBobina = bobine.find(b => b.id_bobina === value);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n\n    setFormErrors(newErrors);\n    setFormWarnings(newWarnings);\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati) {\n      newErrors.metri_posati = 'I metri posati sono obbligatori';\n    } else if (isNaN(formData.metri_posati) || parseFloat(formData.metri_posati) < 0) {\n      newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina) {\n      newErrors.id_bobina = 'La bobina è obbligatoria';\n    }\n\n    setFormErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Verifica la compatibilità tra cavo e bobina\n  const checkCompatibility = () => {\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      return true; // BOBINA_VUOTA è sempre compatibile\n    }\n\n    const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n    if (!selectedBobina) {\n      return false;\n    }\n\n    // Verifica compatibilità tipologia\n    const tipologiaCompatibile = selectedCavo.tipologia === selectedBobina.tipologia;\n\n    // Verifica compatibilità sezione\n    const sezioneCompatibile = String(selectedCavo.sezione) === String(selectedBobina.sezione);\n\n    return tipologiaCompatibile && sezioneCompatibile;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n    if (bobine.length === 0 && !bobineLoading) {\n      if (!formData.metri_posati || isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n        setFormErrors({\n          ...formErrors,\n          metri_posati: 'I metri posati sono obbligatori e devono essere maggiori di zero'\n        });\n        return;\n      }\n\n      // Imposta BOBINA_VUOTA e procedi con il salvataggio\n      formData.id_bobina = 'BOBINA_VUOTA';\n    } else {\n      // Validazione completa\n      if (!validateForm()) {\n        return;\n      }\n\n      // Verifica compatibilità\n      if (!checkCompatibility()) {\n        // Mostra dialog per incompatibilità\n        const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        setIncompatibleReelData({\n          cavo: selectedCavo,\n          bobina: selectedBobina\n        });\n        setShowIncompatibleReelDialog(true);\n        return;\n      }\n    }\n\n    // Procedi con il salvataggio\n    try {\n      setSaving(true);\n\n      // Converti metri posati in numero\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', formData.id_bobina);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        formData.id_bobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Metri posati aggiornati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce l'aggiornamento del cavo per compatibilità\n  const handleUpdateCavoForCompatibility = async () => {\n    try {\n      setSaving(true);\n      setShowIncompatibleReelDialog(false);\n\n      const { cavo, bobina } = incompatibleReelData;\n\n      // Aggiorna il cavo per renderlo compatibile con la bobina\n      await caviService.updateCavoForCompatibility(\n        cantiereId,\n        cavo.id_cavo,\n        {\n          id_bobina: bobina.id_bobina,\n          tipologia: bobina.tipologia,\n          sezione: bobina.sezione\n        }\n      );\n\n      // Procedi con l'aggiornamento dei metri posati\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        parseFloat(formData.metri_posati),\n        formData.id_bobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Cavo aggiornato e metri posati registrati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento del cavo:', error);\n      onError('Errore durante l\\'aggiornamento del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce la selezione di un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/posa/modifica-bobina?cavoId=${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Stato per le bobine compatibili\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n\n  // Aggiorna le bobine compatibili quando viene selezionato un cavo o cambiano le bobine disponibili\n  useEffect(() => {\n    console.log('useEffect per bobine compatibili - selectedCavo:', selectedCavo ? selectedCavo.id_cavo : 'nessuno');\n    console.log('useEffect per bobine compatibili - bobine:', bobine.length);\n\n    if (selectedCavo) {\n      // Filtra le bobine compatibili localmente\n      console.log('Chiamata a filterCompatibleBobine...');\n      const filtered = filterCompatibleBobine(selectedCavo);\n      console.log('Risultato di filterCompatibleBobine:', filtered.length);\n      setCompatibleBobine(filtered);\n      console.log('compatibleBobine impostato a:', filtered.length);\n    } else {\n      setCompatibleBobine([]);\n      console.log('compatibleBobine impostato a vuoto');\n    }\n  }, [selectedCavo, bobine]);\n\n  // Filtra le bobine compatibili localmente\n  const filterCompatibleBobine = (cavo) => {\n    if (!cavo) return [];\n\n    console.log('Filtrando bobine compatibili per cavo:', cavo);\n    console.log('Bobine disponibili:', bobine);\n    console.log('Numero di bobine disponibili:', bobine.length);\n\n    // Verifica se ci sono bobine disponibili\n    if (bobine.length === 0) {\n      console.log('ATTENZIONE: Nessuna bobina disponibile nel cantiere!');\n      return [];\n    }\n\n    // Filtra le bobine compatibili con il cavo\n    const filtered = bobine.filter(bobina => {\n      // Normalizza i valori per un confronto più robusto\n      // 1. Normalizza tipologia (trim e lowercase)\n      // 2. Normalizza sezione (trim e conversione in stringa)\n      // 3. Verifica stato bobina e metri residui\n      const cavoTipologiaNorm = String(cavo.tipologia || '').trim().toLowerCase();\n      const cavoSezioneNorm = String(cavo.sezione || '').trim();\n\n      const bobinaTipologiaNorm = String(bobina.tipologia || '').trim().toLowerCase();\n      const bobinaSezioneNorm = String(bobina.sezione || '').trim();\n\n      // Verifica compatibilità\n      const tipologiaMatch = bobinaTipologiaNorm === cavoTipologiaNorm;\n      const sezioneMatch = bobinaSezioneNorm === cavoSezioneNorm;\n\n      // Verifica stato bobina direttamente invece di usare determineReelState\n      // Questo è più affidabile e corrisponde alla logica del backend\n      const stateOk = bobina.stato_bobina !== 'Terminata' && bobina.stato_bobina !== 'Over';\n\n      // Verifica che i metri residui siano positivi\n      const metriOk = bobina.metri_residui > 0;\n\n      const isCompatible = tipologiaMatch && sezioneMatch && stateOk && metriOk;\n\n      // Log dettagliati per debug\n      console.log(`Confronto dettagliato per bobina ${bobina.id_bobina}:`);\n      console.log(`- Tipologia bobina (originale): \"${bobina.tipologia}\", tipo: ${typeof bobina.tipologia}`);\n      console.log(`- Tipologia cavo (originale): \"${cavo.tipologia}\", tipo: ${typeof cavo.tipologia}`);\n      console.log(`- Tipologia bobina (normalizzata): \"${bobinaTipologiaNorm}\"`);\n      console.log(`- Tipologia cavo (normalizzata): \"${cavoTipologiaNorm}\"`);\n      console.log(`- Sezione bobina (originale): \"${bobina.sezione}\", tipo: ${typeof bobina.sezione}`);\n      console.log(`- Sezione cavo (originale): \"${cavo.sezione}\", tipo: ${typeof cavo.sezione}`);\n      console.log(`- Sezione bobina (normalizzata): \"${bobinaSezioneNorm}\"`);\n      console.log(`- Sezione cavo (normalizzata): \"${cavoSezioneNorm}\"`);\n      console.log(`- Stato bobina: ${bobina.stato_bobina}`);\n      console.log(`- Metri residui: ${bobina.metri_residui}`);\n      console.log(`- Stato OK? ${stateOk}`);\n      console.log(`- Metri OK? ${metriOk}`);\n\n      // Log di riepilogo\n      console.log(`Bobina ${bobina.id_bobina}:`, {\n        'Tipologia bobina': `\"${bobina.tipologia}\"`,\n        'Tipologia cavo': `\"${cavo.tipologia}\"`,\n        'Tipologie uguali?': tipologiaMatch,\n        'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n        'Sezione cavo': `\"${String(cavo.sezione)}\"`,\n        'Sezioni uguali?': sezioneMatch,\n        'Stato bobina': bobina.stato_bobina,\n        'Metri residui': bobina.metri_residui,\n        'Stato OK?': stateOk,\n        'Metri OK?': metriOk,\n        'Compatibile?': isCompatible\n      });\n\n      return isCompatible;\n    });\n\n    console.log('Bobine compatibili trovate:', filtered.length);\n    if (filtered.length > 0) {\n      console.log('Prima bobina compatibile:', filtered[0]);\n    } else {\n      console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n    }\n\n    return filtered;\n  };\n\n  // Funzione di utilità per ottenere le bobine compatibili (usata nel rendering)\n  const getCompatibleBobine = () => {\n    return compatibleBobine;\n  };\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    if (caviLoading) {\n      return (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      );\n    }\n\n    if (cavi.length === 0) {\n      return (\n        <Alert severity=\"info\" sx={{ my: 2 }}>\n          Nessun cavo disponibile per questo cantiere.\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          Seleziona un cavo dalla tabella per inserire i metri posati. I cavi già installati sono disabilitati.\n        </Alert>\n\n        <TableContainer component={Paper} sx={{ mb: 3 }}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow sx={{ bgcolor: '#e3f2fd' }}>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Ubicazione</TableCell>\n                <TableCell>Metri Teorici</TableCell>\n                <TableCell>Stato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {cavi.map((cavo) => {\n                const isInstalled = isCableInstalled(cavo);\n                return (\n                  <TableRow\n                    key={cavo.id_cavo}\n                    sx={{\n                      bgcolor: isInstalled ? '#f5f5f5' : 'inherit',\n                      '&:hover': { bgcolor: isInstalled ? '#f5f5f5' : '#f1f8e9' }\n                    }}\n                  >\n                    <TableCell><strong>{cavo.id_cavo}</strong></TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={cavo.stato_installazione || 'N/D'}\n                        size=\"small\"\n                        color={getCableStateColor(cavo.stato_installazione)}\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Button\n                        size=\"small\"\n                        variant=\"contained\"\n                        color=\"primary\"\n                        onClick={() => handleCavoSelect(cavo)}\n                        disabled={isInstalled}\n                      >\n                        {isInstalled ? 'Già installato' : 'Seleziona'}\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </>\n    );\n  };\n\n  // Renderizza il form per inserimento metri e selezione bobina\n  const renderForm = () => {\n    if (!selectedCavo) return null;\n\n    // Log per debug - verifica le bobine compatibili nel rendering\n    const compatibleBobineList = getCompatibleBobine();\n    console.log('Rendering form - Bobine compatibili:', compatibleBobineList);\n    console.log('Rendering form - Numero di bobine compatibili:', compatibleBobineList.length);\n\n    // Verifica se ci sono bobine disponibili\n    if (bobine.length === 0 && !bobineLoading) {\n      return (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Inserimento metri posati\n          </Typography>\n\n          <Alert severity=\"warning\" sx={{ mb: 3 }}>\n            Non ci sono bobine disponibili nel cantiere. Puoi comunque registrare i metri posati utilizzando l'opzione \"BOBINA VUOTA\".\n          </Alert>\n\n          <Paper sx={{ p: 1.5, mb: 3, width: '100%' }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\n              <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 2 }}>\n                Cavo selezionato: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>\n              </Typography>\n\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Tipo:</Typography>\n                  <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Form:</Typography>\n                  <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Metri:</Typography>\n                  <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Stato:</Typography>\n                  <Chip\n                    size=\"small\"\n                    label={selectedCavo.stato_installazione || 'N/D'}\n                    color={getCableStateColor(selectedCavo.stato_installazione)}\n                    sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.75rem' } }}\n                  />\n                </Box>\n              </Box>\n            </Box>\n          </Paper>\n\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Metri posati\"\n                name=\"metri_posati\"\n                value={formData.metri_posati}\n                onChange={handleInputChange}\n                type=\"number\"\n                InputProps={{\n                  inputProps: { min: 0, step: 0.1 }\n                }}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth>\n                <InputLabel>Bobina</InputLabel>\n                <Select\n                  name=\"id_bobina\"\n                  value=\"BOBINA_VUOTA\"\n                  disabled\n                >\n                  <MenuItem value=\"BOBINA_VUOTA\" sx={{ fontWeight: 'bold', color: '#2e7d32', bgcolor: '#f1f8e9' }}>\n                    BOBINA VUOTA (Cavo posato senza bobina)\n                  </MenuItem>\n                </Select>\n                <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                  Non ci sono bobine disponibili. Verrà utilizzata l'opzione BOBINA VUOTA.\n                </Typography>\n              </FormControl>\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>\n            <Button\n              variant=\"outlined\"\n              color=\"secondary\"\n              onClick={() => {\n                setSelectedCavo(null);\n                setFormData({\n                  id_cavo: '',\n                  metri_posati: '',\n                  id_bobina: ''\n                });\n              }}\n              disabled={saving}\n            >\n              Annulla\n            </Button>\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={() => handleSave()}\n              disabled={saving || !formData.metri_posati}\n            >\n              {saving ? <CircularProgress size={24} /> : 'Salva'}\n            </Button>\n          </Box>\n        </Paper>\n      );\n    }\n\n    const compatibleBobine = getCompatibleBobine();\n\n    return (\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserimento metri posati\n        </Typography>\n\n        <Alert severity=\"info\" sx={{ mb: 3 }}>\n          Inserisci i metri posati per il cavo selezionato e associa una bobina. Se il cavo è stato posato senza una bobina specifica, seleziona <strong>BOBINA VUOTA</strong>.\n        </Alert>\n\n        <Paper sx={{ p: 1.5, mb: 3, width: '100%' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\n            <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 2, fontSize: '0.9rem' }}>\n              Cavo selezionato: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>\n            </Typography>\n\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'wrap', ml: 2 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Tipo:</Typography>\n                <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>\n              </Box>\n              <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Form:</Typography>\n                <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>\n              </Box>\n              <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Metri:</Typography>\n                <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n              </Box>\n              <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Stato:</Typography>\n                <Chip\n                  size=\"small\"\n                  label={selectedCavo.stato_installazione || 'N/D'}\n                  color={getCableStateColor(selectedCavo.stato_installazione)}\n                  sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.8rem' } }}\n                />\n              </Box>\n            </Box>\n          </Box>\n        </Paper>\n\n        <Divider sx={{ mb: 3 }} />\n\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={6}>\n            <TextField\n              fullWidth\n              label=\"Metri posati\"\n              name=\"metri_posati\"\n              value={formData.metri_posati}\n              onChange={handleInputChange}\n              type=\"number\"\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || (formWarnings.metri_posati && (\n                <span style={{ color: 'orange' }}>{formWarnings.metri_posati}</span>\n              ))}\n              disabled={saving}\n              InputProps={{\n                inputProps: { min: 0, step: 0.1 }\n              }}\n            />\n          </Grid>\n          <Grid item xs={12} md={6}>\n            <FormControl fullWidth error={!!formErrors.id_bobina}>\n              <InputLabel>Bobina</InputLabel>\n              <Select\n                name=\"id_bobina\"\n                value={formData.id_bobina}\n                onChange={handleInputChange}\n                disabled={saving || bobineLoading}\n              >\n                {/* Opzione BOBINA VUOTA sempre disponibile e in evidenza */}\n                <MenuItem value=\"BOBINA_VUOTA\" sx={{ fontWeight: 'bold', color: '#2e7d32', bgcolor: '#f1f8e9' }}>\n                  BOBINA VUOTA (Cavo posato senza bobina)\n                </MenuItem>\n\n                {/* Separatore */}\n                <Divider />\n\n                {/* Messaggio informativo */}\n                {bobineLoading ? (\n                  <MenuItem disabled>\n                    <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                      <CircularProgress size={20} sx={{ mr: 1 }} />\n                      <Typography variant=\"caption\">\n                        Caricamento bobine...\n                      </Typography>\n                    </Box>\n                  </MenuItem>\n                ) : compatibleBobine.length === 0 ? (\n                  <MenuItem disabled>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Nessuna bobina compatibile disponibile. Utilizzare BOBINA VUOTA.\n                    </Typography>\n                  </MenuItem>\n                ) : (\n                  <MenuItem disabled>\n                    <Typography variant=\"caption\">\n                      Bobine compatibili ({compatibleBobine.length})\n                    </Typography>\n                  </MenuItem>\n                )}\n\n                {/* Bobine compatibili */}\n                {!bobineLoading && (() => {\n                  console.log('Rendering Select - Bobine compatibili:', compatibleBobine);\n                  console.log('Rendering Select - Numero di bobine compatibili:', compatibleBobine.length);\n\n                  return compatibleBobine.map((bobina) => {\n                    console.log('Rendering bobina compatibile:', bobina);\n                    return (\n                      <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                        {bobina.id_bobina} - {bobina.tipologia} - {bobina.metri_residui}m\n                      </MenuItem>\n                    );\n                  });\n                })()\n                }\n\n                {/* Separatore per tutte le bobine */}\n                <Divider />\n\n                {/* Titolo per tutte le bobine */}\n                <MenuItem disabled>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: '#ff9800' }}>\n                    TUTTE LE BOBINE DISPONIBILI (Ignora compatibilità)\n                  </Typography>\n                </MenuItem>\n\n                {/* Mostra tutte le bobine disponibili */}\n                {bobine.filter(bobina => bobina.stato_bobina !== 'Terminata').map((bobina) => (\n                  <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                    {bobina.id_bobina} - {bobina.tipologia} - {bobina.sezione} - {bobina.metri_residui}m\n                  </MenuItem>\n                ))}\n              </Select>\n              {formErrors.id_bobina && (\n                <Typography variant=\"caption\" color=\"error\">\n                  {formErrors.id_bobina}\n                </Typography>\n              )}\n              {formWarnings.id_bobina && (\n                <Typography variant=\"caption\" sx={{ color: 'orange' }}>\n                  {formWarnings.id_bobina}\n                </Typography>\n              )}\n              {/* Messaggio informativo sotto il campo */}\n              <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                Seleziona una bobina o usa BOBINA VUOTA se il cavo è stato posato senza una bobina specifica.\n              </Typography>\n            </FormControl>\n          </Grid>\n        </Grid>\n\n        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>\n          <Button\n            variant=\"outlined\"\n            color=\"secondary\"\n            onClick={() => {\n              setSelectedCavo(null);\n              setFormData({\n                id_cavo: '',\n                metri_posati: '',\n                id_bobina: ''\n              });\n            }}\n            disabled={saving}\n          >\n            Annulla\n          </Button>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={handleSave}\n            disabled={saving || Object.keys(formErrors).length > 0}\n          >\n            {saving ? <CircularProgress size={24} /> : 'Salva'}\n          </Button>\n        </Box>\n      </Paper>\n    );\n  };\n\n  return (\n    <Box>\n      {/* Tabella cavi */}\n      {!selectedCavo && renderCaviTable()}\n\n      {/* Form per inserimento metri e selezione bobina */}\n      {renderForm()}\n\n      {/* Dialog per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={() => setShowIncompatibleReelDialog(false)}\n        cavo={incompatibleReelData.cavo}\n        bobina={incompatibleReelData.bobina}\n        onUpdateCavo={handleUpdateCavoForCompatibility}\n        onSelectAnotherReel={() => {\n          setShowIncompatibleReelDialog(false);\n          setFormData(prev => ({ ...prev, id_bobina: '' }));\n        }}\n      />\n\n      {/* Dialog per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog}>\n        <DialogTitle>Cavo già posato</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Il cavo {alreadyLaidCavo?.id_cavo} è già stato posato.\n          </DialogContentText>\n          <Box sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" gutterBottom>\n              Puoi:\n            </Typography>\n            <Typography component=\"ul\" variant=\"body2\">\n              <li>Modificare la bobina associata</li>\n              <li>Selezionare un altro cavo</li>\n              <li>Annullare l'operazione</li>\n            </Typography>\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default MetriPosatiSemplificatoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,iBAAiB,QACZ,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,SAASC,wBAAwB,QAAQ,6BAA6B;AACtE,OAAOC,sBAAsB,MAAM,0BAA0B;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,2BAA2B,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACwB,IAAI,EAAEC,OAAO,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACqD,MAAM,EAAEC,SAAS,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC;IACvC2D,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoE,MAAM,EAAEC,SAAS,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAAC0E,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAAC4E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7E,QAAQ,CAAC;IAAE8E,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC;EAC9F,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACkF,eAAe,EAAEC,kBAAkB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACdmF,QAAQ,CAAC,CAAC;IACVC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACvC,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMsC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFnB,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMqB,QAAQ,GAAG,MAAM1D,WAAW,CAAC2D,OAAO,CAACzC,UAAU,CAAC;;MAEtD;MACA,MAAM0C,UAAU,GAAGF,QAAQ,CAACG,MAAM,CAACX,IAAI,IAAI,CAAC3C,YAAY,CAAC2C,IAAI,CAAC,CAAC;MAE/D1B,OAAO,CAACoC,UAAU,CAAC;IACrB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD1C,OAAO,CAAC,mCAAmC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACR3B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMoB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFlB,gBAAgB,CAAC,IAAI,CAAC;MACtBwB,OAAO,CAACE,GAAG,CAAC,kCAAkC,EAAE/C,UAAU,CAAC;MAC3D,MAAMgD,UAAU,GAAG,MAAMjE,gBAAgB,CAACkE,SAAS,CAACjD,UAAU,CAAC;MAC/D6C,OAAO,CAACE,GAAG,CAAC,kBAAkB,EAAEC,UAAU,CAAC;MAC3CH,OAAO,CAACE,GAAG,CAAC,mBAAmB,CAAC;MAChCC,UAAU,CAACE,OAAO,CAACjB,MAAM,IAAI;QAC3BY,OAAO,CAACE,GAAG,CAAC,UAAUd,MAAM,CAAClB,SAAS,GAAG,EAAE;UACzCoC,SAAS,EAAElB,MAAM,CAACkB,SAAS;UAC3BC,OAAO,EAAEnB,MAAM,CAACmB,OAAO;UACvBC,aAAa,EAAEpB,MAAM,CAACoB,aAAa;UACnCC,YAAY,EAAErB,MAAM,CAACqB;QACvB,CAAC,CAAC;MACJ,CAAC,CAAC;MACFT,OAAO,CAACE,GAAG,CAAC,sDAAsD,CAAC;MACnEvC,SAAS,CAACwC,UAAU,CAAC;MACrBH,OAAO,CAACE,GAAG,CAAC,+BAA+B,EAAEC,UAAU,CAACO,MAAM,CAAC;IACjE,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D1C,OAAO,CAAC,uCAAuC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRzB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMmC,gBAAgB,GAAIxB,IAAI,IAAK;IACjCa,OAAO,CAACE,GAAG,CAAC,mBAAmB,EAAEf,IAAI,CAAC;IACtCa,OAAO,CAACE,GAAG,CAAC,iBAAiB,EAAE;MAC7BlC,OAAO,EAAEmB,IAAI,CAACnB,OAAO;MACrBsC,SAAS,EAAEnB,IAAI,CAACmB,SAAS;MACzBC,OAAO,EAAEpB,IAAI,CAACoB,OAAO;MACrBK,aAAa,EAAEzB,IAAI,CAACyB,aAAa;MACjCC,mBAAmB,EAAE1B,IAAI,CAAC0B;IAC5B,CAAC,CAAC;;IAEF;IACA,IAAIpE,gBAAgB,CAAC0C,IAAI,CAAC,EAAE;MAC1Ba,OAAO,CAACE,GAAG,CAAC,gCAAgC,CAAC;MAC7CV,kBAAkB,CAACL,IAAI,CAAC;MACxBG,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IAEAU,OAAO,CAACE,GAAG,CAAC,8BAA8B,CAAC;IAC3CrC,eAAe,CAACsB,IAAI,CAAC;IACrBa,OAAO,CAACE,GAAG,CAAC,2BAA2B,EAAEf,IAAI,CAACnB,OAAO,CAAC;IAEtDD,WAAW,CAAC;MACVC,OAAO,EAAEmB,IAAI,CAACnB,OAAO;MACrBC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFU,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;;IAEnB;IACAkB,OAAO,CAACE,GAAG,CAAC,wDAAwD,CAAC;;IAErE;IACA,MAAMY,oBAAoB,GAAGC,sBAAsB,CAAC5B,IAAI,CAAC;IACzDa,OAAO,CAACE,GAAG,CAAC,uCAAuC,EAAEY,oBAAoB,CAACJ,MAAM,CAAC;IACjFM,mBAAmB,CAACF,oBAAoB,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCtD,WAAW,CAACuD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACAG,aAAa,CAACJ,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMG,aAAa,GAAGA,CAACJ,IAAI,EAAEC,KAAK,KAAK;IACrC,MAAMI,SAAS,GAAG;MAAE,GAAG7C;IAAW,CAAC;IACnC,MAAM8C,WAAW,GAAG;MAAE,GAAG5C;IAAa,CAAC;IAEvC,IAAIsC,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAIC,KAAK,KAAK,EAAE,EAAE;QAChBI,SAAS,CAACvD,YAAY,GAAG,iCAAiC;MAC5D,CAAC,MAAM,IAAIyD,KAAK,CAACN,KAAK,CAAC,IAAIO,UAAU,CAACP,KAAK,CAAC,GAAG,CAAC,EAAE;QAChDI,SAAS,CAACvD,YAAY,GAAG,iDAAiD;MAC5E,CAAC,MAAM;QACL,OAAOuD,SAAS,CAACvD,YAAY;;QAE7B;QACA,MAAM2D,WAAW,GAAGD,UAAU,CAACP,KAAK,CAAC;QACrC,IAAIxD,YAAY,IAAIgE,WAAW,GAAGhE,YAAY,CAACgD,aAAa,EAAE;UAC5Da,WAAW,CAACxD,YAAY,GAAG,mBAAmB2D,WAAW,+BAA+BhE,YAAY,CAACgD,aAAa,GAAG;QACvH,CAAC,MAAM;UACL,OAAOa,WAAW,CAACxD,YAAY;QACjC;;QAEA;QACA,IAAIH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;UAC/D,MAAM2D,cAAc,GAAGnE,MAAM,CAACoE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;UAC3E,IAAI2D,cAAc,IAAID,WAAW,GAAGC,cAAc,CAACrB,aAAa,EAAE;YAChEiB,WAAW,CAACvD,SAAS,GAAG,mBAAmB0D,WAAW,4CAA4CC,cAAc,CAACrB,aAAa,GAAG;UACnI,CAAC,MAAM;YACL,OAAOiB,WAAW,CAACvD,SAAS;UAC9B;QACF;MACF;IACF;IAEA,IAAIiD,IAAI,KAAK,WAAW,EAAE;MACxB;MACA,IAAIC,KAAK,KAAK,EAAE,EAAE;QAChBI,SAAS,CAACtD,SAAS,GAAG,0BAA0B;MAClD,CAAC,MAAM;QACL,OAAOsD,SAAS,CAACtD,SAAS;;QAE1B;QACA,IAAIkD,KAAK,KAAK,cAAc,IAAItD,QAAQ,CAACG,YAAY,EAAE;UACrD,MAAM2D,WAAW,GAAGD,UAAU,CAAC7D,QAAQ,CAACG,YAAY,CAAC;UACrD,MAAM4D,cAAc,GAAGnE,MAAM,CAACoE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7D,SAAS,KAAKkD,KAAK,CAAC;UAC9D,IAAIS,cAAc,IAAID,WAAW,GAAGC,cAAc,CAACrB,aAAa,EAAE;YAChEiB,WAAW,CAACvD,SAAS,GAAG,mBAAmB0D,WAAW,4CAA4CC,cAAc,CAACrB,aAAa,GAAG;UACnI,CAAC,MAAM;YACL,OAAOiB,WAAW,CAACvD,SAAS;UAC9B;QACF;MACF;IACF;IAEAU,aAAa,CAAC4C,SAAS,CAAC;IACxB1C,eAAe,CAAC2C,WAAW,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMR,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAAC1D,QAAQ,CAACG,YAAY,EAAE;MAC1BuD,SAAS,CAACvD,YAAY,GAAG,iCAAiC;IAC5D,CAAC,MAAM,IAAIyD,KAAK,CAAC5D,QAAQ,CAACG,YAAY,CAAC,IAAI0D,UAAU,CAAC7D,QAAQ,CAACG,YAAY,CAAC,GAAG,CAAC,EAAE;MAChFuD,SAAS,CAACvD,YAAY,GAAG,iDAAiD;IAC5E;;IAEA;IACA,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;MACvBsD,SAAS,CAACtD,SAAS,GAAG,0BAA0B;IAClD;IAEAU,aAAa,CAAC4C,SAAS,CAAC;IACxB,OAAOS,MAAM,CAACC,IAAI,CAACV,SAAS,CAAC,CAACd,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMyB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIrE,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzC,OAAO,IAAI,CAAC,CAAC;IACf;IAEA,MAAM2D,cAAc,GAAGnE,MAAM,CAACoE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IAC3E,IAAI,CAAC2D,cAAc,EAAE;MACnB,OAAO,KAAK;IACd;;IAEA;IACA,MAAMO,oBAAoB,GAAGxE,YAAY,CAAC0C,SAAS,KAAKuB,cAAc,CAACvB,SAAS;;IAEhF;IACA,MAAM+B,kBAAkB,GAAGC,MAAM,CAAC1E,YAAY,CAAC2C,OAAO,CAAC,KAAK+B,MAAM,CAACT,cAAc,CAACtB,OAAO,CAAC;IAE1F,OAAO6B,oBAAoB,IAAIC,kBAAkB;EACnD,CAAC;;EAED;EACA,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B;IACA,IAAI7E,MAAM,CAACgD,MAAM,KAAK,CAAC,IAAI,CAACnC,aAAa,EAAE;MACzC,IAAI,CAACT,QAAQ,CAACG,YAAY,IAAIyD,KAAK,CAACC,UAAU,CAAC7D,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAI0D,UAAU,CAAC7D,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;QAChHW,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbV,YAAY,EAAE;QAChB,CAAC,CAAC;QACF;MACF;;MAEA;MACAH,QAAQ,CAACI,SAAS,GAAG,cAAc;IACrC,CAAC,MAAM;MACL;MACA,IAAI,CAAC8D,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;;MAEA;MACA,IAAI,CAACG,kBAAkB,CAAC,CAAC,EAAE;QACzB;QACA,MAAMN,cAAc,GAAGnE,MAAM,CAACoE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QAC3EgB,uBAAuB,CAAC;UACtBC,IAAI,EAAEvB,YAAY;UAClBwB,MAAM,EAAEyC;QACV,CAAC,CAAC;QACF7C,6BAA6B,CAAC,IAAI,CAAC;QACnC;MACF;IACF;;IAEA;IACA,IAAI;MACFN,SAAS,CAAC,IAAI,CAAC;;MAEf;MACA,MAAMkD,WAAW,GAAGD,UAAU,CAAC7D,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA+B,OAAO,CAACE,GAAG,CAAC,6DAA6D,CAAC;MAC1EF,OAAO,CAACE,GAAG,CAAC,eAAe,EAAE/C,UAAU,CAAC;MACxC6C,OAAO,CAACE,GAAG,CAAC,YAAY,EAAEpC,QAAQ,CAACE,OAAO,CAAC;MAC3CgC,OAAO,CAACE,GAAG,CAAC,iBAAiB,EAAE0B,WAAW,CAAC;MAC3C5B,OAAO,CAACE,GAAG,CAAC,cAAc,EAAEpC,QAAQ,CAACI,SAAS,CAAC;;MAE/C;MACA,MAAMjC,WAAW,CAACuG,iBAAiB,CACjCrF,UAAU,EACVW,QAAQ,CAACE,OAAO,EAChB4D,WAAW,EACX9D,QAAQ,CAACI,SAAS,EAClB,IAAI,CAAC;MACP,CAAC;;MAED;MACAd,SAAS,CAAC,sCAAsC,CAAC;;MAEjD;MACAS,eAAe,CAAC,IAAI,CAAC;MACrBE,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACAuB,QAAQ,CAAC,CAAC;MACVC,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD1C,OAAO,CAAC,iCAAiC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACtF,CAAC,SAAS;MACRvB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM+D,gCAAgC,GAAG,MAAAA,CAAA,KAAY;IACnD,IAAI;MACF/D,SAAS,CAAC,IAAI,CAAC;MACfM,6BAA6B,CAAC,KAAK,CAAC;MAEpC,MAAM;QAAEG,IAAI;QAAEC;MAAO,CAAC,GAAGH,oBAAoB;;MAE7C;MACA,MAAMhD,WAAW,CAACyG,0BAA0B,CAC1CvF,UAAU,EACVgC,IAAI,CAACnB,OAAO,EACZ;QACEE,SAAS,EAAEkB,MAAM,CAAClB,SAAS;QAC3BoC,SAAS,EAAElB,MAAM,CAACkB,SAAS;QAC3BC,OAAO,EAAEnB,MAAM,CAACmB;MAClB,CACF,CAAC;;MAED;MACA,MAAMtE,WAAW,CAACuG,iBAAiB,CACjCrF,UAAU,EACVW,QAAQ,CAACE,OAAO,EAChB2D,UAAU,CAAC7D,QAAQ,CAACG,YAAY,CAAC,EACjCH,QAAQ,CAACI,SAAS,EAClB,IAAI,CAAC;MACP,CAAC;;MAED;MACAd,SAAS,CAAC,wDAAwD,CAAC;;MAEnE;MACAS,eAAe,CAAC,IAAI,CAAC;MACrBE,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACAuB,QAAQ,CAAC,CAAC;MACVC,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE1C,OAAO,CAAC,4CAA4C,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACjG,CAAC,SAAS;MACRvB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMiE,4BAA4B,GAAGA,CAAA,KAAM;IACzCrD,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMoD,wBAAwB,GAAGA,CAAA,KAAM;IACrCD,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAItD,eAAe,EAAE;MACnBhC,QAAQ,CAAC,+CAA+CgC,eAAe,CAACvB,OAAO,EAAE,CAAC;IACpF;IACA2E,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAM,CAACG,gBAAgB,EAAE9B,mBAAmB,CAAC,GAAG3G,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACd0F,OAAO,CAACE,GAAG,CAAC,kDAAkD,EAAEtC,YAAY,GAAGA,YAAY,CAACI,OAAO,GAAG,SAAS,CAAC;IAChHgC,OAAO,CAACE,GAAG,CAAC,4CAA4C,EAAExC,MAAM,CAACgD,MAAM,CAAC;IAExE,IAAI9C,YAAY,EAAE;MAChB;MACAoC,OAAO,CAACE,GAAG,CAAC,sCAAsC,CAAC;MACnD,MAAM6C,QAAQ,GAAGhC,sBAAsB,CAACnD,YAAY,CAAC;MACrDoC,OAAO,CAACE,GAAG,CAAC,sCAAsC,EAAE6C,QAAQ,CAACrC,MAAM,CAAC;MACpEM,mBAAmB,CAAC+B,QAAQ,CAAC;MAC7B/C,OAAO,CAACE,GAAG,CAAC,+BAA+B,EAAE6C,QAAQ,CAACrC,MAAM,CAAC;IAC/D,CAAC,MAAM;MACLM,mBAAmB,CAAC,EAAE,CAAC;MACvBhB,OAAO,CAACE,GAAG,CAAC,oCAAoC,CAAC;IACnD;EACF,CAAC,EAAE,CAACtC,YAAY,EAAEF,MAAM,CAAC,CAAC;;EAE1B;EACA,MAAMqD,sBAAsB,GAAI5B,IAAI,IAAK;IACvC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpBa,OAAO,CAACE,GAAG,CAAC,wCAAwC,EAAEf,IAAI,CAAC;IAC3Da,OAAO,CAACE,GAAG,CAAC,qBAAqB,EAAExC,MAAM,CAAC;IAC1CsC,OAAO,CAACE,GAAG,CAAC,+BAA+B,EAAExC,MAAM,CAACgD,MAAM,CAAC;;IAE3D;IACA,IAAIhD,MAAM,CAACgD,MAAM,KAAK,CAAC,EAAE;MACvBV,OAAO,CAACE,GAAG,CAAC,sDAAsD,CAAC;MACnE,OAAO,EAAE;IACX;;IAEA;IACA,MAAM6C,QAAQ,GAAGrF,MAAM,CAACoC,MAAM,CAACV,MAAM,IAAI;MACvC;MACA;MACA;MACA;MACA,MAAM4D,iBAAiB,GAAGV,MAAM,CAACnD,IAAI,CAACmB,SAAS,IAAI,EAAE,CAAC,CAAC2C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC3E,MAAMC,eAAe,GAAGb,MAAM,CAACnD,IAAI,CAACoB,OAAO,IAAI,EAAE,CAAC,CAAC0C,IAAI,CAAC,CAAC;MAEzD,MAAMG,mBAAmB,GAAGd,MAAM,CAAClD,MAAM,CAACkB,SAAS,IAAI,EAAE,CAAC,CAAC2C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC/E,MAAMG,iBAAiB,GAAGf,MAAM,CAAClD,MAAM,CAACmB,OAAO,IAAI,EAAE,CAAC,CAAC0C,IAAI,CAAC,CAAC;;MAE7D;MACA,MAAMK,cAAc,GAAGF,mBAAmB,KAAKJ,iBAAiB;MAChE,MAAMO,YAAY,GAAGF,iBAAiB,KAAKF,eAAe;;MAE1D;MACA;MACA,MAAMK,OAAO,GAAGpE,MAAM,CAACqB,YAAY,KAAK,WAAW,IAAIrB,MAAM,CAACqB,YAAY,KAAK,MAAM;;MAErF;MACA,MAAMgD,OAAO,GAAGrE,MAAM,CAACoB,aAAa,GAAG,CAAC;MAExC,MAAMkD,YAAY,GAAGJ,cAAc,IAAIC,YAAY,IAAIC,OAAO,IAAIC,OAAO;;MAEzE;MACAzD,OAAO,CAACE,GAAG,CAAC,oCAAoCd,MAAM,CAAClB,SAAS,GAAG,CAAC;MACpE8B,OAAO,CAACE,GAAG,CAAC,oCAAoCd,MAAM,CAACkB,SAAS,YAAY,OAAOlB,MAAM,CAACkB,SAAS,EAAE,CAAC;MACtGN,OAAO,CAACE,GAAG,CAAC,kCAAkCf,IAAI,CAACmB,SAAS,YAAY,OAAOnB,IAAI,CAACmB,SAAS,EAAE,CAAC;MAChGN,OAAO,CAACE,GAAG,CAAC,uCAAuCkD,mBAAmB,GAAG,CAAC;MAC1EpD,OAAO,CAACE,GAAG,CAAC,qCAAqC8C,iBAAiB,GAAG,CAAC;MACtEhD,OAAO,CAACE,GAAG,CAAC,kCAAkCd,MAAM,CAACmB,OAAO,YAAY,OAAOnB,MAAM,CAACmB,OAAO,EAAE,CAAC;MAChGP,OAAO,CAACE,GAAG,CAAC,gCAAgCf,IAAI,CAACoB,OAAO,YAAY,OAAOpB,IAAI,CAACoB,OAAO,EAAE,CAAC;MAC1FP,OAAO,CAACE,GAAG,CAAC,qCAAqCmD,iBAAiB,GAAG,CAAC;MACtErD,OAAO,CAACE,GAAG,CAAC,mCAAmCiD,eAAe,GAAG,CAAC;MAClEnD,OAAO,CAACE,GAAG,CAAC,mBAAmBd,MAAM,CAACqB,YAAY,EAAE,CAAC;MACrDT,OAAO,CAACE,GAAG,CAAC,oBAAoBd,MAAM,CAACoB,aAAa,EAAE,CAAC;MACvDR,OAAO,CAACE,GAAG,CAAC,eAAesD,OAAO,EAAE,CAAC;MACrCxD,OAAO,CAACE,GAAG,CAAC,eAAeuD,OAAO,EAAE,CAAC;;MAErC;MACAzD,OAAO,CAACE,GAAG,CAAC,UAAUd,MAAM,CAAClB,SAAS,GAAG,EAAE;QACzC,kBAAkB,EAAE,IAAIkB,MAAM,CAACkB,SAAS,GAAG;QAC3C,gBAAgB,EAAE,IAAInB,IAAI,CAACmB,SAAS,GAAG;QACvC,mBAAmB,EAAEgD,cAAc;QACnC,gBAAgB,EAAE,IAAIhB,MAAM,CAAClD,MAAM,CAACmB,OAAO,CAAC,GAAG;QAC/C,cAAc,EAAE,IAAI+B,MAAM,CAACnD,IAAI,CAACoB,OAAO,CAAC,GAAG;QAC3C,iBAAiB,EAAEgD,YAAY;QAC/B,cAAc,EAAEnE,MAAM,CAACqB,YAAY;QACnC,eAAe,EAAErB,MAAM,CAACoB,aAAa;QACrC,WAAW,EAAEgD,OAAO;QACpB,WAAW,EAAEC,OAAO;QACpB,cAAc,EAAEC;MAClB,CAAC,CAAC;MAEF,OAAOA,YAAY;IACrB,CAAC,CAAC;IAEF1D,OAAO,CAACE,GAAG,CAAC,6BAA6B,EAAE6C,QAAQ,CAACrC,MAAM,CAAC;IAC3D,IAAIqC,QAAQ,CAACrC,MAAM,GAAG,CAAC,EAAE;MACvBV,OAAO,CAACE,GAAG,CAAC,2BAA2B,EAAE6C,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC,MAAM;MACL/C,OAAO,CAACE,GAAG,CAAC,iDAAiD,CAAC;IAChE;IAEA,OAAO6C,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMY,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OAAOb,gBAAgB;EACzB,CAAC;;EAED;EACA,MAAMc,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIvF,WAAW,EAAE;MACf,oBACEtB,OAAA,CAACxC,GAAG;QAACsJ,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,eAC5DlH,OAAA,CAAC7B,gBAAgB;UAAAgJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAEV;IAEA,IAAI7G,IAAI,CAACkD,MAAM,KAAK,CAAC,EAAE;MACrB,oBACE3D,OAAA,CAAC5B,KAAK;QAACmJ,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IAEA,oBACEtH,OAAA,CAAAE,SAAA;MAAAgH,QAAA,gBACElH,OAAA,CAAC5B,KAAK;QAACmJ,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERtH,OAAA,CAAChC,cAAc;QAACyJ,SAAS,EAAE7J,KAAM;QAACkJ,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,eAC9ClH,OAAA,CAACnC,KAAK;UAAC6J,IAAI,EAAC,OAAO;UAAAR,QAAA,gBACjBlH,OAAA,CAAC/B,SAAS;YAAAiJ,QAAA,eACRlH,OAAA,CAAC9B,QAAQ;cAAC4I,EAAE,EAAE;gBAAEa,OAAO,EAAE;cAAU,CAAE;cAAAT,QAAA,gBACnClH,OAAA,CAACjC,SAAS;gBAAAmJ,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BtH,OAAA,CAACjC,SAAS;gBAAAmJ,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCtH,OAAA,CAACjC,SAAS;gBAAAmJ,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjCtH,OAAA,CAACjC,SAAS;gBAAAmJ,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpCtH,OAAA,CAACjC,SAAS;gBAAAmJ,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BtH,OAAA,CAACjC,SAAS;gBAAAmJ,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZtH,OAAA,CAAClC,SAAS;YAAAoJ,QAAA,EACPzG,IAAI,CAACmH,GAAG,CAAExF,IAAI,IAAK;cAClB,MAAMyF,WAAW,GAAGnI,gBAAgB,CAAC0C,IAAI,CAAC;cAC1C,oBACEpC,OAAA,CAAC9B,QAAQ;gBAEP4I,EAAE,EAAE;kBACFa,OAAO,EAAEE,WAAW,GAAG,SAAS,GAAG,SAAS;kBAC5C,SAAS,EAAE;oBAAEF,OAAO,EAAEE,WAAW,GAAG,SAAS,GAAG;kBAAU;gBAC5D,CAAE;gBAAAX,QAAA,gBAEFlH,OAAA,CAACjC,SAAS;kBAAAmJ,QAAA,eAAClH,OAAA;oBAAAkH,QAAA,EAAS9E,IAAI,CAACnB;kBAAO;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACtDtH,OAAA,CAACjC,SAAS;kBAAAmJ,QAAA,EAAE9E,IAAI,CAACmB,SAAS,IAAI;gBAAK;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChDtH,OAAA,CAACjC,SAAS;kBAAAmJ,QAAA,GAAC,MAAI,EAAC9E,IAAI,CAAC0F,mBAAmB,IAAI,KAAK,eAAC9H,OAAA;oBAAAmH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,OAAG,EAAClF,IAAI,CAAC2F,iBAAiB,IAAI,KAAK;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvGtH,OAAA,CAACjC,SAAS;kBAAAmJ,QAAA,GAAE9E,IAAI,CAACyB,aAAa,IAAI,KAAK,EAAC,IAAE;gBAAA;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACtDtH,OAAA,CAACjC,SAAS;kBAAAmJ,QAAA,eACRlH,OAAA,CAAC3B,IAAI;oBACH2J,KAAK,EAAE5F,IAAI,CAAC0B,mBAAmB,IAAI,KAAM;oBACzC4D,IAAI,EAAC,OAAO;oBACZO,KAAK,EAAEtI,kBAAkB,CAACyC,IAAI,CAAC0B,mBAAmB,CAAE;oBACpDoE,OAAO,EAAC;kBAAU;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZtH,OAAA,CAACjC,SAAS;kBAAAmJ,QAAA,eACRlH,OAAA,CAACrC,MAAM;oBACL+J,IAAI,EAAC,OAAO;oBACZQ,OAAO,EAAC,WAAW;oBACnBD,KAAK,EAAC,SAAS;oBACfE,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAACxB,IAAI,CAAE;oBACtCgG,QAAQ,EAAEP,WAAY;oBAAAX,QAAA,EAErBW,WAAW,GAAG,gBAAgB,GAAG;kBAAW;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,GA5BPlF,IAAI,CAACnB,OAAO;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6BT,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA,eACjB,CAAC;EAEP,CAAC;;EAED;EACA,MAAMe,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACxH,YAAY,EAAE,OAAO,IAAI;;IAE9B;IACA,MAAMkD,oBAAoB,GAAG6C,mBAAmB,CAAC,CAAC;IAClD3D,OAAO,CAACE,GAAG,CAAC,sCAAsC,EAAEY,oBAAoB,CAAC;IACzEd,OAAO,CAACE,GAAG,CAAC,gDAAgD,EAAEY,oBAAoB,CAACJ,MAAM,CAAC;;IAE1F;IACA,IAAIhD,MAAM,CAACgD,MAAM,KAAK,CAAC,IAAI,CAACnC,aAAa,EAAE;MACzC,oBACExB,OAAA,CAACpC,KAAK;QAACkJ,EAAE,EAAE;UAAEwB,CAAC,EAAE;QAAE,CAAE;QAAApB,QAAA,gBAClBlH,OAAA,CAACvC,UAAU;UAACyK,OAAO,EAAC,IAAI;UAACK,YAAY;UAAArB,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbtH,OAAA,CAAC5B,KAAK;UAACmJ,QAAQ,EAAC,SAAS;UAACT,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,EAAC;QAEzC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAERtH,OAAA,CAACpC,KAAK;UAACkJ,EAAE,EAAE;YAAEwB,CAAC,EAAE,GAAG;YAAEd,EAAE,EAAE,CAAC;YAAEgB,KAAK,EAAE;UAAO,CAAE;UAAAtB,QAAA,eAC1ClH,OAAA,CAACxC,GAAG;YAACsJ,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAE0B,UAAU,EAAE,QAAQ;cAAED,KAAK,EAAE;YAAO,CAAE;YAAAtB,QAAA,gBAChElH,OAAA,CAACvC,UAAU;cAACyK,OAAO,EAAC,WAAW;cAACpB,EAAE,EAAE;gBAAE4B,UAAU,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAA1B,QAAA,GAAC,oBACrE,eAAAlH,OAAA;gBAAM6I,KAAK,EAAE;kBAAEZ,KAAK,EAAE;gBAAU,CAAE;gBAAAf,QAAA,EAAErG,YAAY,CAACI;cAAO;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eAEbtH,OAAA,CAACxC,GAAG;cAACsJ,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAE0B,UAAU,EAAE,QAAQ;gBAAEK,GAAG,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAA7B,QAAA,gBAC3ElH,OAAA,CAACxC,GAAG;gBAACsJ,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAE0B,UAAU,EAAE,QAAQ;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAzB,QAAA,gBACvElH,OAAA,CAACvC,UAAU;kBAACyK,OAAO,EAAC,OAAO;kBAACpB,EAAE,EAAE;oBAAE4B,UAAU,EAAE,QAAQ;oBAAEM,QAAQ,EAAE,SAAS;oBAAEJ,EAAE,EAAE;kBAAI,CAAE;kBAAA1B,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1GtH,OAAA,CAACvC,UAAU;kBAACyK,OAAO,EAAC,OAAO;kBAACpB,EAAE,EAAE;oBAAEkC,QAAQ,EAAE;kBAAU,CAAE;kBAAA9B,QAAA,EAAErG,YAAY,CAAC0C,SAAS,IAAI;gBAAK;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpG,CAAC,eACNtH,OAAA,CAACxC,GAAG;gBAACsJ,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAE0B,UAAU,EAAE,QAAQ;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAzB,QAAA,gBACvElH,OAAA,CAACvC,UAAU;kBAACyK,OAAO,EAAC,OAAO;kBAACpB,EAAE,EAAE;oBAAE4B,UAAU,EAAE,QAAQ;oBAAEM,QAAQ,EAAE,SAAS;oBAAEJ,EAAE,EAAE;kBAAI,CAAE;kBAAA1B,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1GtH,OAAA,CAACvC,UAAU;kBAACyK,OAAO,EAAC,OAAO;kBAACpB,EAAE,EAAE;oBAAEkC,QAAQ,EAAE;kBAAU,CAAE;kBAAA9B,QAAA,EAAErG,YAAY,CAAC2C,OAAO,IAAI;gBAAK;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClG,CAAC,eACNtH,OAAA,CAACxC,GAAG;gBAACsJ,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAE0B,UAAU,EAAE,QAAQ;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAzB,QAAA,gBACvElH,OAAA,CAACvC,UAAU;kBAACyK,OAAO,EAAC,OAAO;kBAACpB,EAAE,EAAE;oBAAE4B,UAAU,EAAE,QAAQ;oBAAEM,QAAQ,EAAE,SAAS;oBAAEJ,EAAE,EAAE;kBAAI,CAAE;kBAAA1B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3GtH,OAAA,CAACvC,UAAU;kBAACyK,OAAO,EAAC,OAAO;kBAACpB,EAAE,EAAE;oBAAEkC,QAAQ,EAAE;kBAAU,CAAE;kBAAA9B,QAAA,GAAErG,YAAY,CAACgD,aAAa,IAAI,KAAK,EAAC,IAAE;gBAAA;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC,eACNtH,OAAA,CAACxC,GAAG;gBAACsJ,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAE0B,UAAU,EAAE,QAAQ;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAzB,QAAA,gBACvElH,OAAA,CAACvC,UAAU;kBAACyK,OAAO,EAAC,OAAO;kBAACpB,EAAE,EAAE;oBAAE4B,UAAU,EAAE,QAAQ;oBAAEM,QAAQ,EAAE,SAAS;oBAAEJ,EAAE,EAAE;kBAAI,CAAE;kBAAA1B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3GtH,OAAA,CAAC3B,IAAI;kBACHqJ,IAAI,EAAC,OAAO;kBACZM,KAAK,EAAEnH,YAAY,CAACiD,mBAAmB,IAAI,KAAM;kBACjDmE,KAAK,EAAEtI,kBAAkB,CAACkB,YAAY,CAACiD,mBAAmB,CAAE;kBAC5DgD,EAAE,EAAE;oBAAEmC,MAAM,EAAE,MAAM;oBAAE,kBAAkB,EAAE;sBAAEC,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE,CAAC;sBAAEH,QAAQ,EAAE;oBAAU;kBAAE;gBAAE;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAERtH,OAAA,CAACzB,IAAI;UAAC6K,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAnC,QAAA,gBACzBlH,OAAA,CAACzB,IAAI;YAAC+K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtC,QAAA,eACvBlH,OAAA,CAACtC,SAAS;cACR+L,SAAS;cACTzB,KAAK,EAAC,cAAc;cACpB5D,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAEtD,QAAQ,CAACG,YAAa;cAC7BwI,QAAQ,EAAExF,iBAAkB;cAC5ByF,IAAI,EAAC,QAAQ;cACbC,UAAU,EAAE;gBACVC,UAAU,EAAE;kBAAEC,GAAG,EAAE,CAAC;kBAAEC,IAAI,EAAE;gBAAI;cAClC;YAAE;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtH,OAAA,CAACzB,IAAI;YAAC+K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtC,QAAA,eACvBlH,OAAA,CAACxB,WAAW;cAACiL,SAAS;cAAAvC,QAAA,gBACpBlH,OAAA,CAACvB,UAAU;gBAAAyI,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/BtH,OAAA,CAACtB,MAAM;gBACL0F,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAC,cAAc;gBACpB+D,QAAQ;gBAAAlB,QAAA,eAERlH,OAAA,CAACrB,QAAQ;kBAAC0F,KAAK,EAAC,cAAc;kBAACyC,EAAE,EAAE;oBAAE4B,UAAU,EAAE,MAAM;oBAAET,KAAK,EAAE,SAAS;oBAAEN,OAAO,EAAE;kBAAU,CAAE;kBAAAT,QAAA,EAAC;gBAEjG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACTtH,OAAA,CAACvC,UAAU;gBAACyK,OAAO,EAAC,SAAS;gBAACD,KAAK,EAAC,gBAAgB;gBAACnB,EAAE,EAAE;kBAAEkD,EAAE,EAAE;gBAAE,CAAE;gBAAA9C,QAAA,EAAC;cAEpE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPtH,OAAA,CAACxC,GAAG;UAACsJ,EAAE,EAAE;YAAEkD,EAAE,EAAE,CAAC;YAAEjD,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAgB,CAAE;UAAAE,QAAA,gBACnElH,OAAA,CAACrC,MAAM;YACLuK,OAAO,EAAC,UAAU;YAClBD,KAAK,EAAC,WAAW;YACjBE,OAAO,EAAEA,CAAA,KAAM;cACbrH,eAAe,CAAC,IAAI,CAAC;cACrBE,WAAW,CAAC;gBACVC,OAAO,EAAE,EAAE;gBACXC,YAAY,EAAE,EAAE;gBAChBC,SAAS,EAAE;cACb,CAAC,CAAC;YACJ,CAAE;YACFiH,QAAQ,EAAE1G,MAAO;YAAAwF,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtH,OAAA,CAACrC,MAAM;YACLuK,OAAO,EAAC,WAAW;YACnBD,KAAK,EAAC,SAAS;YACfE,OAAO,EAAEA,CAAA,KAAM3C,UAAU,CAAC,CAAE;YAC5B4C,QAAQ,EAAE1G,MAAM,IAAI,CAACX,QAAQ,CAACG,YAAa;YAAAgG,QAAA,EAE1CxF,MAAM,gBAAG1B,OAAA,CAAC7B,gBAAgB;cAACuJ,IAAI,EAAE;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAEZ;IAEA,MAAMvB,gBAAgB,GAAGa,mBAAmB,CAAC,CAAC;IAE9C,oBACE5G,OAAA,CAACpC,KAAK;MAACkJ,EAAE,EAAE;QAAEwB,CAAC,EAAE;MAAE,CAAE;MAAApB,QAAA,gBAClBlH,OAAA,CAACvC,UAAU;QAACyK,OAAO,EAAC,IAAI;QAACK,YAAY;QAAArB,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbtH,OAAA,CAAC5B,KAAK;QAACmJ,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,GAAC,4IACmG,eAAAlH,OAAA;UAAAkH,QAAA,EAAQ;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KACtK;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERtH,OAAA,CAACpC,KAAK;QAACkJ,EAAE,EAAE;UAAEwB,CAAC,EAAE,GAAG;UAAEd,EAAE,EAAE,CAAC;UAAEgB,KAAK,EAAE;QAAO,CAAE;QAAAtB,QAAA,eAC1ClH,OAAA,CAACxC,GAAG;UAACsJ,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAE0B,UAAU,EAAE,QAAQ;YAAED,KAAK,EAAE;UAAO,CAAE;UAAAtB,QAAA,gBAChElH,OAAA,CAACvC,UAAU;YAACyK,OAAO,EAAC,WAAW;YAACpB,EAAE,EAAE;cAAE4B,UAAU,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE,CAAC;cAAEI,QAAQ,EAAE;YAAS,CAAE;YAAA9B,QAAA,GAAC,oBACzF,eAAAlH,OAAA;cAAM6I,KAAK,EAAE;gBAAEZ,KAAK,EAAE;cAAU,CAAE;cAAAf,QAAA,EAAErG,YAAY,CAACI;YAAO;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eAEbtH,OAAA,CAACxC,GAAG;YAACsJ,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAE0B,UAAU,EAAE,QAAQ;cAAEK,GAAG,EAAE,CAAC;cAAEC,QAAQ,EAAE,MAAM;cAAEkB,EAAE,EAAE;YAAE,CAAE;YAAA/C,QAAA,gBAClFlH,OAAA,CAACxC,GAAG;cAACsJ,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAE0B,UAAU,EAAE,QAAQ;gBAAEE,UAAU,EAAE;cAAS,CAAE;cAAAzB,QAAA,gBACvElH,OAAA,CAACvC,UAAU;gBAACyK,OAAO,EAAC,OAAO;gBAACpB,EAAE,EAAE;kBAAE4B,UAAU,EAAE,QAAQ;kBAAEM,QAAQ,EAAE,SAAS;kBAAEJ,EAAE,EAAE;gBAAI,CAAE;gBAAA1B,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1GtH,OAAA,CAACvC,UAAU;gBAACyK,OAAO,EAAC,OAAO;gBAACpB,EAAE,EAAE;kBAAEkC,QAAQ,EAAE;gBAAU,CAAE;gBAAA9B,QAAA,EAAErG,YAAY,CAAC0C,SAAS,IAAI;cAAK;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpG,CAAC,eACNtH,OAAA,CAACxC,GAAG;cAACsJ,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAE0B,UAAU,EAAE,QAAQ;gBAAEE,UAAU,EAAE;cAAS,CAAE;cAAAzB,QAAA,gBACvElH,OAAA,CAACvC,UAAU;gBAACyK,OAAO,EAAC,OAAO;gBAACpB,EAAE,EAAE;kBAAE4B,UAAU,EAAE,QAAQ;kBAAEM,QAAQ,EAAE,SAAS;kBAAEJ,EAAE,EAAE;gBAAI,CAAE;gBAAA1B,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1GtH,OAAA,CAACvC,UAAU;gBAACyK,OAAO,EAAC,OAAO;gBAACpB,EAAE,EAAE;kBAAEkC,QAAQ,EAAE;gBAAU,CAAE;gBAAA9B,QAAA,EAAErG,YAAY,CAAC2C,OAAO,IAAI;cAAK;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClG,CAAC,eACNtH,OAAA,CAACxC,GAAG;cAACsJ,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAE0B,UAAU,EAAE,QAAQ;gBAAEE,UAAU,EAAE;cAAS,CAAE;cAAAzB,QAAA,gBACvElH,OAAA,CAACvC,UAAU;gBAACyK,OAAO,EAAC,OAAO;gBAACpB,EAAE,EAAE;kBAAE4B,UAAU,EAAE,QAAQ;kBAAEM,QAAQ,EAAE,SAAS;kBAAEJ,EAAE,EAAE;gBAAI,CAAE;gBAAA1B,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC3GtH,OAAA,CAACvC,UAAU;gBAACyK,OAAO,EAAC,OAAO;gBAACpB,EAAE,EAAE;kBAAEkC,QAAQ,EAAE;gBAAU,CAAE;gBAAA9B,QAAA,GAAErG,YAAY,CAACgD,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1G,CAAC,eACNtH,OAAA,CAACxC,GAAG;cAACsJ,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAE0B,UAAU,EAAE,QAAQ;gBAAEE,UAAU,EAAE;cAAS,CAAE;cAAAzB,QAAA,gBACvElH,OAAA,CAACvC,UAAU;gBAACyK,OAAO,EAAC,OAAO;gBAACpB,EAAE,EAAE;kBAAE4B,UAAU,EAAE,QAAQ;kBAAEM,QAAQ,EAAE,SAAS;kBAAEJ,EAAE,EAAE;gBAAI,CAAE;gBAAA1B,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC3GtH,OAAA,CAAC3B,IAAI;gBACHqJ,IAAI,EAAC,OAAO;gBACZM,KAAK,EAAEnH,YAAY,CAACiD,mBAAmB,IAAI,KAAM;gBACjDmE,KAAK,EAAEtI,kBAAkB,CAACkB,YAAY,CAACiD,mBAAmB,CAAE;gBAC5DgD,EAAE,EAAE;kBAAEmC,MAAM,EAAE,MAAM;kBAAE,kBAAkB,EAAE;oBAAEC,EAAE,EAAE,CAAC;oBAAEC,EAAE,EAAE,CAAC;oBAAEH,QAAQ,EAAE;kBAAS;gBAAE;cAAE;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERtH,OAAA,CAAC1B,OAAO;QAACwI,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1BtH,OAAA,CAACzB,IAAI;QAAC6K,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAnC,QAAA,gBACzBlH,OAAA,CAACzB,IAAI;UAAC+K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAtC,QAAA,eACvBlH,OAAA,CAACtC,SAAS;YACR+L,SAAS;YACTzB,KAAK,EAAC,cAAc;YACpB5D,IAAI,EAAC,cAAc;YACnBC,KAAK,EAAEtD,QAAQ,CAACG,YAAa;YAC7BwI,QAAQ,EAAExF,iBAAkB;YAC5ByF,IAAI,EAAC,QAAQ;YACb3G,KAAK,EAAE,CAAC,CAACpB,UAAU,CAACV,YAAa;YACjCgJ,UAAU,EAAEtI,UAAU,CAACV,YAAY,IAAKY,YAAY,CAACZ,YAAY,iBAC/DlB,OAAA;cAAM6I,KAAK,EAAE;gBAAEZ,KAAK,EAAE;cAAS,CAAE;cAAAf,QAAA,EAAEpF,YAAY,CAACZ;YAAY;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAClE;YACHc,QAAQ,EAAE1G,MAAO;YACjBkI,UAAU,EAAE;cACVC,UAAU,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,IAAI,EAAE;cAAI;YAClC;UAAE;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPtH,OAAA,CAACzB,IAAI;UAAC+K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAtC,QAAA,eACvBlH,OAAA,CAACxB,WAAW;YAACiL,SAAS;YAACzG,KAAK,EAAE,CAAC,CAACpB,UAAU,CAACT,SAAU;YAAA+F,QAAA,gBACnDlH,OAAA,CAACvB,UAAU;cAAAyI,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/BtH,OAAA,CAACtB,MAAM;cACL0F,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAEtD,QAAQ,CAACI,SAAU;cAC1BuI,QAAQ,EAAExF,iBAAkB;cAC5BkE,QAAQ,EAAE1G,MAAM,IAAIF,aAAc;cAAA0F,QAAA,gBAGlClH,OAAA,CAACrB,QAAQ;gBAAC0F,KAAK,EAAC,cAAc;gBAACyC,EAAE,EAAE;kBAAE4B,UAAU,EAAE,MAAM;kBAAET,KAAK,EAAE,SAAS;kBAAEN,OAAO,EAAE;gBAAU,CAAE;gBAAAT,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAGXtH,OAAA,CAAC1B,OAAO;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAGV9F,aAAa,gBACZxB,OAAA,CAACrB,QAAQ;gBAACyJ,QAAQ;gBAAAlB,QAAA,eAChBlH,OAAA,CAACxC,GAAG;kBAACsJ,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAE0B,UAAU,EAAE;kBAAS,CAAE;kBAAAvB,QAAA,gBACjDlH,OAAA,CAAC7B,gBAAgB;oBAACuJ,IAAI,EAAE,EAAG;oBAACZ,EAAE,EAAE;sBAAE8B,EAAE,EAAE;oBAAE;kBAAE;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7CtH,OAAA,CAACvC,UAAU;oBAACyK,OAAO,EAAC,SAAS;oBAAAhB,QAAA,EAAC;kBAE9B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,GACTvB,gBAAgB,CAACpC,MAAM,KAAK,CAAC,gBAC/B3D,OAAA,CAACrB,QAAQ;gBAACyJ,QAAQ;gBAAAlB,QAAA,eAChBlH,OAAA,CAACvC,UAAU;kBAACyK,OAAO,EAAC,SAAS;kBAACD,KAAK,EAAC,gBAAgB;kBAAAf,QAAA,EAAC;gBAErD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,gBAEXtH,OAAA,CAACrB,QAAQ;gBAACyJ,QAAQ;gBAAAlB,QAAA,eAChBlH,OAAA,CAACvC,UAAU;kBAACyK,OAAO,EAAC,SAAS;kBAAAhB,QAAA,GAAC,sBACR,EAACnB,gBAAgB,CAACpC,MAAM,EAAC,GAC/C;gBAAA;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACX,EAGA,CAAC9F,aAAa,IAAI,CAAC,MAAM;gBACxByB,OAAO,CAACE,GAAG,CAAC,wCAAwC,EAAE4C,gBAAgB,CAAC;gBACvE9C,OAAO,CAACE,GAAG,CAAC,kDAAkD,EAAE4C,gBAAgB,CAACpC,MAAM,CAAC;gBAExF,OAAOoC,gBAAgB,CAAC6B,GAAG,CAAEvF,MAAM,IAAK;kBACtCY,OAAO,CAACE,GAAG,CAAC,+BAA+B,EAAEd,MAAM,CAAC;kBACpD,oBACErC,OAAA,CAACrB,QAAQ;oBAAwB0F,KAAK,EAAEhC,MAAM,CAAClB,SAAU;oBAAA+F,QAAA,GACtD7E,MAAM,CAAClB,SAAS,EAAC,KAAG,EAACkB,MAAM,CAACkB,SAAS,EAAC,KAAG,EAAClB,MAAM,CAACoB,aAAa,EAAC,GAClE;kBAAA,GAFepB,MAAM,CAAClB,SAAS;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAErB,CAAC;gBAEf,CAAC,CAAC;cACJ,CAAC,EAAE,CAAC,eAIJtH,OAAA,CAAC1B,OAAO;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGXtH,OAAA,CAACrB,QAAQ;gBAACyJ,QAAQ;gBAAAlB,QAAA,eAChBlH,OAAA,CAACvC,UAAU;kBAACyK,OAAO,EAAC,SAAS;kBAACpB,EAAE,EAAE;oBAAE4B,UAAU,EAAE,MAAM;oBAAET,KAAK,EAAE;kBAAU,CAAE;kBAAAf,QAAA,EAAC;gBAE5E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EAGV3G,MAAM,CAACoC,MAAM,CAACV,MAAM,IAAIA,MAAM,CAACqB,YAAY,KAAK,WAAW,CAAC,CAACkE,GAAG,CAAEvF,MAAM,iBACvErC,OAAA,CAACrB,QAAQ;gBAAwB0F,KAAK,EAAEhC,MAAM,CAAClB,SAAU;gBAAA+F,QAAA,GACtD7E,MAAM,CAAClB,SAAS,EAAC,KAAG,EAACkB,MAAM,CAACkB,SAAS,EAAC,KAAG,EAAClB,MAAM,CAACmB,OAAO,EAAC,KAAG,EAACnB,MAAM,CAACoB,aAAa,EAAC,GACrF;cAAA,GAFepB,MAAM,CAAClB,SAAS;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAErB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACR1F,UAAU,CAACT,SAAS,iBACnBnB,OAAA,CAACvC,UAAU;cAACyK,OAAO,EAAC,SAAS;cAACD,KAAK,EAAC,OAAO;cAAAf,QAAA,EACxCtF,UAAU,CAACT;YAAS;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACb,EACAxF,YAAY,CAACX,SAAS,iBACrBnB,OAAA,CAACvC,UAAU;cAACyK,OAAO,EAAC,SAAS;cAACpB,EAAE,EAAE;gBAAEmB,KAAK,EAAE;cAAS,CAAE;cAAAf,QAAA,EACnDpF,YAAY,CAACX;YAAS;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACb,eAEDtH,OAAA,CAACvC,UAAU;cAACyK,OAAO,EAAC,SAAS;cAACD,KAAK,EAAC,gBAAgB;cAACnB,EAAE,EAAE;gBAAEkD,EAAE,EAAE;cAAE,CAAE;cAAA9C,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPtH,OAAA,CAACxC,GAAG;QAACsJ,EAAE,EAAE;UAAEkD,EAAE,EAAE,CAAC;UAAEjD,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAE,QAAA,gBACnElH,OAAA,CAACrC,MAAM;UACLuK,OAAO,EAAC,UAAU;UAClBD,KAAK,EAAC,WAAW;UACjBE,OAAO,EAAEA,CAAA,KAAM;YACbrH,eAAe,CAAC,IAAI,CAAC;YACrBE,WAAW,CAAC;cACVC,OAAO,EAAE,EAAE;cACXC,YAAY,EAAE,EAAE;cAChBC,SAAS,EAAE;YACb,CAAC,CAAC;UACJ,CAAE;UACFiH,QAAQ,EAAE1G,MAAO;UAAAwF,QAAA,EAClB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtH,OAAA,CAACrC,MAAM;UACLuK,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACfE,OAAO,EAAE3C,UAAW;UACpB4C,QAAQ,EAAE1G,MAAM,IAAIwD,MAAM,CAACC,IAAI,CAACvD,UAAU,CAAC,CAAC+B,MAAM,GAAG,CAAE;UAAAuD,QAAA,EAEtDxF,MAAM,gBAAG1B,OAAA,CAAC7B,gBAAgB;YAACuJ,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEZ,CAAC;EAED,oBACEtH,OAAA,CAACxC,GAAG;IAAA0J,QAAA,GAED,CAACrG,YAAY,IAAIgG,eAAe,CAAC,CAAC,EAGlCwB,UAAU,CAAC,CAAC,eAGbrI,OAAA,CAACF,sBAAsB;MACrBqK,IAAI,EAAEnI,0BAA2B;MACjCoI,OAAO,EAAEA,CAAA,KAAMnI,6BAA6B,CAAC,KAAK,CAAE;MACpDG,IAAI,EAAEF,oBAAoB,CAACE,IAAK;MAChCC,MAAM,EAAEH,oBAAoB,CAACG,MAAO;MACpCgI,YAAY,EAAE3E,gCAAiC;MAC/C4E,mBAAmB,EAAEA,CAAA,KAAM;QACzBrI,6BAA6B,CAAC,KAAK,CAAC;QACpCjB,WAAW,CAACuD,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEpD,SAAS,EAAE;QAAG,CAAC,CAAC,CAAC;MACnD;IAAE;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFtH,OAAA,CAACpB,MAAM;MAACuL,IAAI,EAAE7H,qBAAsB;MAAC8H,OAAO,EAAExE,4BAA6B;MAAAsB,QAAA,gBACzElH,OAAA,CAACnB,WAAW;QAAAqI,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC1CtH,OAAA,CAAClB,aAAa;QAAAoI,QAAA,gBACZlH,OAAA,CAAChB,iBAAiB;UAAAkI,QAAA,GAAC,UACT,EAAC1E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEvB,OAAO,EAAC,4BACpC;QAAA;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBtH,OAAA,CAACxC,GAAG;UAACsJ,EAAE,EAAE;YAAEkD,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,gBACjBlH,OAAA,CAACvC,UAAU;YAACyK,OAAO,EAAC,OAAO;YAACK,YAAY;YAAArB,QAAA,EAAC;UAEzC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtH,OAAA,CAACvC,UAAU;YAACgK,SAAS,EAAC,IAAI;YAACS,OAAO,EAAC,OAAO;YAAAhB,QAAA,gBACxClH,OAAA;cAAAkH,QAAA,EAAI;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvCtH,OAAA;cAAAkH,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCtH,OAAA;cAAAkH,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBtH,OAAA,CAACjB,aAAa;QAAC+H,EAAE,EAAE;UAAEwB,CAAC,EAAE,CAAC;UAAEtB,cAAc,EAAE;QAAgB,CAAE;QAAAE,QAAA,gBAC3DlH,OAAA,CAACrC,MAAM;UAACwK,OAAO,EAAEvC,4BAA6B;UAACqC,KAAK,EAAC,WAAW;UAAAf,QAAA,EAAC;QAEjE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtH,OAAA,CAACxC,GAAG;UAAA0J,QAAA,gBACFlH,OAAA,CAACrC,MAAM;YAACwK,OAAO,EAAEtC,wBAAyB;YAACoC,KAAK,EAAC,SAAS;YAACnB,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAE,CAAE;YAAA1B,QAAA,EAAC;UAE1E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtH,OAAA,CAACrC,MAAM;YAACwK,OAAO,EAAErC,gBAAiB;YAACoC,OAAO,EAAC,WAAW;YAACD,KAAK,EAAC,SAAS;YAAAf,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC/G,EAAA,CAt5BIJ,2BAA2B;EAAA,QACdlB,WAAW;AAAA;AAAAsL,EAAA,GADxBpK,2BAA2B;AAw5BjC,eAAeA,2BAA2B;AAAC,IAAAoK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}