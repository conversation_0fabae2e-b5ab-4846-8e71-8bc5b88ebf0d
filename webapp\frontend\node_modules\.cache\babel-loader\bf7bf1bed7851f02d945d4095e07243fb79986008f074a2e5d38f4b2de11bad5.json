{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Typography, TextField, Button, Stepper, Step, StepLabel, Grid, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, ArrowBack as ArrowBackIcon, ArrowForward as ArrowForwardIcon, Cancel as CancelIcon, CheckCircle as CheckCircleIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form multi-step\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Definizione dei passi del form\n  const steps = ['Seleziona Cavo', 'Inserisci Metri', 'Associa Bobina', 'Conferma'];\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica la lista delle bobine quando necessario\n  useEffect(() => {\n    if (activeStep === 2) {\n      loadBobine();\n    }\n  }, [activeStep, cantiereId]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n      // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n      setCavi(caviData);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Carica solo le bobine disponibili o in uso\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina => (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') && bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata');\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina => bobina.tipologia === selectedCavo.tipologia && bobina.n_conduttori === selectedCavo.n_conduttori && bobina.sezione === selectedCavo.sezione);\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n          } else {\n            console.log('Nessuna bobina compatibile trovata, mostro tutte le bobine disponibili');\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      const cavoData = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica se il cavo è già installato\n      if (cavoData.stato_installazione === 'Installato' || cavoData.metratura_reale && cavoData.metratura_reale > 0) {\n        // Mostra un messaggio di conferma con opzioni\n        const message = `Il cavo ${cavoData.id_cavo} risulta già posato (${cavoData.metratura_reale || 0}m).\n\nVuoi:\n1. Modificare la bobina associata (usa la funzione \"Modifica bobina cavo posato\")\n2. Selezionare un altro cavo\n3. Annullare l'operazione`;\n        const userChoice = window.confirm(message);\n        if (userChoice) {\n          // L'utente ha scelto di modificare la bobina o selezionare un altro cavo\n          // Reindirizza alla pagina di modifica bobina\n          navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${cavoData.id_cavo}`);\n        }\n        setCaviLoading(false);\n        return;\n      }\n\n      // Verifica se il cavo è SPARE\n      if (cavoData.modificato_manualmente === 3) {\n        // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n      }\n\n      // Seleziona il cavo e passa al passo successivo\n      handleCavoSelect(cavoData);\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n      onError('Cavo non trovato o errore nella ricerca: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0) {\n      // Mostra un messaggio di conferma con opzioni\n      const message = `Il cavo ${cavo.id_cavo} risulta già posato (${cavo.metratura_reale || 0}m).\n\nVuoi:\n1. Modificare la bobina associata (usa la funzione \"Modifica bobina cavo posato\")\n2. Selezionare un altro cavo\n3. Annullare l'operazione`;\n      const userChoice = window.confirm(message);\n      if (userChoice) {\n        // L'utente ha scelto di modificare la bobina o selezionare un altro cavo\n        // Reindirizza alla pagina di modifica bobina\n        navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${cavo.id_cavo}`);\n      }\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Passa al passo successivo\n          setActiveStep(1);\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Passa al passo successivo\n      setActiveStep(1);\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n      } else if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n      } else if (selectedCavo && selectedCavo.metri_teorici && parseFloat(value) > parseFloat(selectedCavo.metri_teorici)) {\n        warning = 'I metri posati superano i metri teorici del cavo';\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n    setFormErrors(errors);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 1) {\n      // Validazione prima di passare al passo successivo\n      if (!validateForm()) {\n        return;\n      }\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    if (!metriPosati || parseFloat(metriPosati) <= 0) {\n      return 'Da installare';\n    }\n    if (parseFloat(metriPosati) >= parseFloat(metriTeorici)) {\n      return 'Installato';\n    }\n    return 'In corso';\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      let idBobina = formData.id_bobina || null;\n      if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Usando BOBINA_VUOTA per il cavo');\n      }\n\n      // Determina lo stato di installazione\n      const statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        statoInstallazione\n      });\n\n      // Chiamata API\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina);\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Cerca cavo per ID\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 9,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavo\",\n              variant: \"outlined\",\n              value: cavoIdInput,\n              onChange: e => setCavoIdInput(e.target.value),\n              placeholder: \"Inserisci l'ID del cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: handleSearchCavoById,\n              disabled: caviLoading || !cavoIdInput.trim(),\n              startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 42\n              }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 75\n              }, this),\n              children: \"Cerca\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Non ci sono cavi disponibili da installare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '400px',\n            overflow: 'auto'\n          },\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 27\n                  }, this), cavo.modificato_manualmente === 3 ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"SPARE\",\n                    color: \"error\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 29\n                  }, this) : cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0 ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"Installato\",\n                    color: \"success\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: cavo.stato_installazione === 'In corso' ? 'warning' : 'default',\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [cavo.tipologia || 'N/A', \" - \", cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 534,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', \" - A: \", cavo.ubicazione_arrivo || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A', \" - Metri posati: \", cavo.metratura_reale || '0']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 539,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Dettagli del cavo selezionato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"ID Cavo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Conduttori:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.n_conduttori || 'N/A', \" x \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione Partenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione Arrivo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri Teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Metri posati\",\n          variant: \"outlined\",\n          name: \"metri_posati\",\n          type: \"number\",\n          value: formData.metri_posati,\n          onChange: handleFormChange,\n          error: !!formErrors.metri_posati,\n          helperText: formErrors.metri_posati || formWarnings.metri_posati,\n          FormHelperTextProps: {\n            sx: {\n              color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n            }\n          },\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = idBobina => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = e => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione opzioni speciali\n      if (numeroBobina.toLowerCase() === 'v') {\n        // Opzione 'v' per bobina vuota\n        setFormData({\n          ...formData,\n          id_bobina: 'BOBINA_VUOTA'\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina.toLowerCase() === 'q') {\n        // Opzione 'q' per annullare\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Associa bobina (opzionale)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 736,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          paragraph: true,\n          children: \"Puoi associare una bobina al cavo selezionato. Questo \\xE8 opzionale, ma consigliato per tenere traccia dell'utilizzo delle bobine.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Inserisci numero bobina\",\n                variant: \"outlined\",\n                placeholder: \"Inserisci solo il numero (Y) o 'v' per bobina vuota\",\n                helperText: formErrors.id_bobina_input || \"Inserisci solo il numero della bobina (parte Y del codice Cx_By), 'v' per bobina vuota o 'q' per annullare\",\n                error: !!formErrors.id_bobina_input,\n                onBlur: handleBobinaNumberInput\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mt: 1\n                },\n                children: [\"ID Bobina completo: \", formData.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : formData.id_bobina || '-']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            sx: {\n              mt: 3,\n              fontWeight: 'bold'\n            },\n            children: \"Bobine disponibili compatibili con il cavo selezionato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 772,\n            columnNumber: 15\n          }, this), bobine.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              overflowX: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              style: {\n                width: '100%',\n                borderCollapse: 'collapse',\n                marginBottom: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  style: {\n                    borderBottom: '2px solid #ddd',\n                    backgroundColor: '#f5f5f5'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"Tipologia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 782,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"Cond.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"Sezione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 784,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'right'\n                    },\n                    children: \"Residui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 785,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"Stato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 786,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: bobine.map(bobina => {\n                  const isCompatible = selectedCavo && bobina.tipologia === selectedCavo.tipologia && bobina.n_conduttori === selectedCavo.n_conduttori && bobina.sezione === selectedCavo.sezione;\n                  const hasSufficient = bobina.metri_residui >= parseFloat(formData.metri_posati || 0);\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    style: {\n                      borderBottom: '1px solid #ddd',\n                      backgroundColor: isCompatible ? hasSufficient ? '#e8f5e9' : '#fff8e1' : 'transparent',\n                      cursor: 'pointer'\n                    },\n                    onClick: () => {\n                      if (hasSufficient) {\n                        setFormData({\n                          ...formData,\n                          id_bobina: bobina.id_bobina\n                        });\n                      }\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: getBobinaNumber(bobina.id_bobina)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 815,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: bobina.tipologia || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 816,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: bobina.n_conduttori || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 817,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: bobina.sezione || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 818,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px',\n                        textAlign: 'right'\n                      },\n                      children: [bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 819,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: bobina.stato_bobina,\n                        color: bobina.stato_bobina === 'Disponibile' ? 'success' : bobina.stato_bobina === 'In uso' ? 'primary' : bobina.stato_bobina === 'Over' ? 'error' : bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 821,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 820,\n                      columnNumber: 29\n                    }, this)]\n                  }, bobina.id_bobina, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 778,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 777,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2,\n              mb: 3\n            },\n            children: \"Non ci sono bobine disponibili compatibili con il cavo selezionato.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 839,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 3,\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              children: \"Opzioni speciali:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  onClick: () => {\n                    setFormData({\n                      ...formData,\n                      id_bobina: 'BOBINA_VUOTA'\n                    });\n                  },\n                  children: \"Bobina Vuota (v)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 851,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 850,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  color: \"secondary\",\n                  onClick: () => {\n                    setFormData({\n                      ...formData,\n                      id_bobina: ''\n                    });\n                  },\n                  children: \"Nessuna Bobina (q)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 864,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 863,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 849,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 845,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"bobina-select-label\",\n              children: \"Seleziona Bobina dalla lista\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"bobina-select-label\",\n              id: \"bobina-select\",\n              name: \"id_bobina\",\n              value: formData.id_bobina,\n              label: \"Seleziona Bobina dalla lista\",\n              onChange: handleFormChange,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: /*#__PURE__*/_jsxDEV(\"em\", {\n                  children: \"Nessuna bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BOBINA_VUOTA\",\n                children: /*#__PURE__*/_jsxDEV(\"em\", {\n                  children: \"BOBINA VUOTA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 895,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 19\n              }, this), bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: bobina.id_bobina,\n                disabled: bobina.metri_residui < parseFloat(formData.metri_posati),\n                children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A', \" - Residui: \", bobina.metri_residui || 0, \" m\"]\n              }, bobina.id_bobina, true, {\n                fileName: _jsxFileName,\n                lineNumber: 898,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n              children: \"Seleziona una bobina con metri residui sufficienti o lascia vuoto per non associare alcuna bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 907,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 881,\n            columnNumber: 15\n          }, this), formData.id_bobina && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 3,\n              p: 2,\n              bgcolor: 'background.paper',\n              borderRadius: 1,\n              border: '1px solid #e0e0e0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              children: \"Dettagli bobina selezionata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 915,\n              columnNumber: 19\n            }, this), (() => {\n              const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n              if (bobina) {\n                return /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Numero:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 925,\n                        columnNumber: 31\n                      }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 924,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Tipologia:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 928,\n                        columnNumber: 31\n                      }, this), \" \", bobina.tipologia || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 927,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Conduttori:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 931,\n                        columnNumber: 31\n                      }, this), \" \", bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 930,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 923,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri totali:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 936,\n                        columnNumber: 31\n                      }, this), \" \", bobina.metri_totali || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 935,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri residui:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 939,\n                        columnNumber: 31\n                      }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 938,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Stato:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 942,\n                        columnNumber: 31\n                      }, this), \" \", bobina.stato_bobina || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 941,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 934,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 922,\n                  columnNumber: 25\n                }, this);\n              }\n              return /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"error\",\n                children: \"Bobina non trovata nel database\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 23\n              }, this);\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 914,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 960,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 740,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 735,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = idBobina => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Ottieni il numero della bobina se presente\n    const numeroBobina = formData.id_bobina ? getBobinaNumber(formData.id_bobina) : 'Nessuna';\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 985,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 990,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"ID Cavo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 997,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 996,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Conduttori:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1003,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.n_conduttori || 'N/A', \" x \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1002,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 995,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri Posati:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1008,\n                columnNumber: 17\n              }, this), \" \", formData.metri_posati]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1007,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Bobina Associata:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1011,\n                columnNumber: 17\n              }, this), \" \", numeroBobina]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1010,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato Installazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1014,\n                columnNumber: 17\n              }, this), \" \", parseFloat(formData.metri_posati) >= parseFloat(selectedCavo.metri_teorici) ? 'Installato' : 'In corso']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1013,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1006,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 994,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1019,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 989,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 984,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      case 1:\n        return renderStep2();\n      case 2:\n        return renderStep3();\n      case 3:\n        return renderStep4();\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Stepper, {\n      activeStep: activeStep,\n      sx: {\n        mb: 4\n      },\n      children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1049,\n          columnNumber: 13\n        }, this)\n      }, label, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1048,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1046,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2,\n        mb: 4\n      },\n      children: getStepContent(activeStep)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1054,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"secondary\",\n        onClick: activeStep === 0 ? () => navigate('/dashboard/cavi/posa') : handleBack,\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1063,\n          columnNumber: 22\n        }, this),\n        disabled: loading,\n        children: activeStep === 0 ? 'Annulla' : 'Indietro'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1059,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: activeStep === steps.length - 1 ? handleSubmit : handleNext,\n        endIcon: activeStep === steps.length - 1 ? /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1073,\n          columnNumber: 54\n        }, this) : /*#__PURE__*/_jsxDEV(ArrowForwardIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1073,\n          columnNumber: 69\n        }, this),\n        disabled: loading || activeStep === 0 && !selectedCavo,\n        children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1077,\n          columnNumber: 13\n        }, this) : activeStep === steps.length - 1 ? 'Salva' : 'Avanti'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1069,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1058,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1045,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"sTdBCOBiexa/+X2SJsmiccxpsm4=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "Search", "SearchIcon", "Save", "SaveIcon", "ArrowBack", "ArrowBackIcon", "ArrowForward", "ArrowForwardIcon", "Cancel", "CancelIcon", "CheckCircle", "CheckCircleIcon", "useNavigate", "caviService", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "activeStep", "setActiveStep", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "steps", "loadCavi", "loadBobine", "caviData", "get<PERSON><PERSON>", "error", "console", "message", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "bobina", "stato_bobina", "tipologia", "n_conduttori", "sezione", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "length", "log", "sort", "a", "b", "metri_residui", "handleSearchCavoById", "trim", "cavoData", "getCavoById", "stato_installazione", "metratura_reale", "userChoice", "window", "confirm", "modificato_manualmente", "handleCavoSelect", "cavo", "reactivateSpare", "then", "updatedCavo", "catch", "cavoId", "handleFormChange", "e", "name", "value", "target", "validateField", "warning", "isNaN", "parseFloat", "metri_te<PERSON>ci", "prev", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "handleNext", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metriTeorici", "handleSubmit", "idBobina", "statoInstallazione", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "find", "renderStep1", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "container", "spacing", "alignItems", "item", "xs", "fullWidth", "label", "onChange", "placeholder", "color", "onClick", "disabled", "startIcon", "size", "display", "justifyContent", "my", "severity", "maxHeight", "overflow", "map", "button", "primary", "ml", "secondary", "component", "ubicazione_partenza", "ubicazione_arrivo", "renderStep2", "md", "type", "helperText", "FormHelperTextProps", "mt", "renderStep3", "getBobinaNumber", "includes", "split", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "handleBobinaNumberInput", "toLowerCase", "id_bobina_input", "idBobinaCompleto", "<PERSON><PERSON><PERSON>", "paragraph", "onBlur", "fontWeight", "overflowX", "style", "width", "borderCollapse", "marginBottom", "borderBottom", "backgroundColor", "padding", "textAlign", "isCompatible", "hasSufficient", "cursor", "id", "labelId", "bgcolor", "borderRadius", "border", "metri_totali", "renderStep4", "getStepContent", "step", "endIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Stepper,\n  Step,\n  StepLabel,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  ArrowBack as ArrowBackIcon,\n  ArrowForward as ArrowForwardIcon,\n  Cancel as CancelIcon,\n  CheckCircle as CheckCircleIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form multi-step\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Definizione dei passi del form\n  const steps = ['Seleziona Cavo', 'Inserisci Metri', 'Associa Bobina', 'Conferma'];\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica la lista delle bobine quando necessario\n  useEffect(() => {\n    if (activeStep === 2) {\n      loadBobine();\n    }\n  }, [activeStep, cantiereId]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n      // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n      setCavi(caviData);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Carica solo le bobine disponibili o in uso\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina =>\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') &&\n        bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata'\n      );\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina =>\n            bobina.tipologia === selectedCavo.tipologia &&\n            bobina.n_conduttori === selectedCavo.n_conduttori &&\n            bobina.sezione === selectedCavo.sezione\n          );\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n          } else {\n            console.log('Nessuna bobina compatibile trovata, mostro tutte le bobine disponibili');\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      const cavoData = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica se il cavo è già installato\n      if (cavoData.stato_installazione === 'Installato' || (cavoData.metratura_reale && cavoData.metratura_reale > 0)) {\n        // Mostra un messaggio di conferma con opzioni\n        const message = `Il cavo ${cavoData.id_cavo} risulta già posato (${cavoData.metratura_reale || 0}m).\n\nVuoi:\n1. Modificare la bobina associata (usa la funzione \"Modifica bobina cavo posato\")\n2. Selezionare un altro cavo\n3. Annullare l'operazione`;\n\n        const userChoice = window.confirm(message);\n\n        if (userChoice) {\n          // L'utente ha scelto di modificare la bobina o selezionare un altro cavo\n          // Reindirizza alla pagina di modifica bobina\n          navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${cavoData.id_cavo}`);\n        }\n\n        setCaviLoading(false);\n        return;\n      }\n\n      // Verifica se il cavo è SPARE\n      if (cavoData.modificato_manualmente === 3) {\n        // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n      }\n\n      // Seleziona il cavo e passa al passo successivo\n      handleCavoSelect(cavoData);\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n      onError('Cavo non trovato o errore nella ricerca: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {\n      // Mostra un messaggio di conferma con opzioni\n      const message = `Il cavo ${cavo.id_cavo} risulta già posato (${cavo.metratura_reale || 0}m).\n\nVuoi:\n1. Modificare la bobina associata (usa la funzione \"Modifica bobina cavo posato\")\n2. Selezionare un altro cavo\n3. Annullare l'operazione`;\n\n      const userChoice = window.confirm(message);\n\n      if (userChoice) {\n        // L'utente ha scelto di modificare la bobina o selezionare un altro cavo\n        // Reindirizza alla pagina di modifica bobina\n        navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${cavo.id_cavo}`);\n      }\n\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Passa al passo successivo\n          setActiveStep(1);\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Passa al passo successivo\n      setActiveStep(1);\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n      } else if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n      } else if (selectedCavo && selectedCavo.metri_teorici && parseFloat(value) > parseFloat(selectedCavo.metri_teorici)) {\n        warning = 'I metri posati superano i metri teorici del cavo';\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    setFormErrors(errors);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 1) {\n      // Validazione prima di passare al passo successivo\n      if (!validateForm()) {\n        return;\n      }\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    if (!metriPosati || parseFloat(metriPosati) <= 0) {\n      return 'Da installare';\n    }\n\n    if (parseFloat(metriPosati) >= parseFloat(metriTeorici)) {\n      return 'Installato';\n    }\n\n    return 'In corso';\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      let idBobina = formData.id_bobina || null;\n      if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Usando BOBINA_VUOTA per il cavo');\n      }\n\n      // Determina lo stato di installazione\n      const statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        statoInstallazione\n      });\n\n      // Chiamata API\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID */}\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Cerca cavo per ID\n          </Typography>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={9}>\n              <TextField\n                fullWidth\n                label=\"ID Cavo\"\n                variant=\"outlined\"\n                value={cavoIdInput}\n                onChange={(e) => setCavoIdInput(e.target.value)}\n                placeholder=\"Inserisci l'ID del cavo\"\n              />\n            </Grid>\n            <Grid item xs={3}>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={handleSearchCavoById}\n                disabled={caviLoading || !cavoIdInput.trim()}\n                startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n              >\n                Cerca\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Lista cavi */}\n        <Paper sx={{ p: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\">\n              Non ci sono cavi disponibili da installare.\n            </Alert>\n          ) : (\n            <List sx={{ maxHeight: '400px', overflow: 'auto' }}>\n              {cavi.map((cavo) => (\n                <React.Fragment key={cavo.id_cavo}>\n                  <ListItem button onClick={() => handleCavoSelect(cavo)}>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle1\">{cavo.id_cavo}</Typography>\n                          {cavo.modificato_manualmente === 3 ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"SPARE\"\n                              color=\"error\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"Installato\"\n                              color=\"success\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : (\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione}\n                              color={cavo.stato_installazione === 'In corso' ? 'warning' : 'default'}\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <>\n                          <Typography variant=\"body2\" component=\"span\">\n                            {cavo.tipologia || 'N/A'} - {cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Da: {cavo.ubicazione_partenza || 'N/A'} - A: {cavo.ubicazione_arrivo || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Metri teorici: {cavo.metri_teorici || 'N/A'} - Metri posati: {cavo.metratura_reale || '0'}\n                          </Typography>\n                        </>\n                      }\n                    />\n                  </ListItem>\n                  <Divider />\n                </React.Fragment>\n              ))}\n            </List>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserisci metri posati\n        </Typography>\n\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Dettagli del cavo selezionato\n          </Typography>\n\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body1\">\n                <strong>ID Cavo:</strong> {selectedCavo.id_cavo}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'} x {selectedCavo.sezione || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body1\">\n                <strong>Ubicazione Partenza:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Ubicazione Arrivo:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}\n              </Typography>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Inserisci i metri posati\n          </Typography>\n\n          <TextField\n            fullWidth\n            label=\"Metri posati\"\n            variant=\"outlined\"\n            name=\"metri_posati\"\n            type=\"number\"\n            value={formData.metri_posati}\n            onChange={handleFormChange}\n            error={!!formErrors.metri_posati}\n            helperText={formErrors.metri_posati || formWarnings.metri_posati}\n            FormHelperTextProps={{\n              sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n            }}\n            sx={{ mb: 2 }}\n          />\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = (idBobina) => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = (e) => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione opzioni speciali\n      if (numeroBobina.toLowerCase() === 'v') {\n        // Opzione 'v' per bobina vuota\n        setFormData({\n          ...formData,\n          id_bobina: 'BOBINA_VUOTA'\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina.toLowerCase() === 'q') {\n        // Opzione 'q' per annullare\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Associa bobina (opzionale)\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"body1\" paragraph>\n            Puoi associare una bobina al cavo selezionato. Questo è opzionale, ma consigliato per tenere traccia dell'utilizzo delle bobine.\n          </Typography>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              {/* Input diretto del numero della bobina */}\n              <Grid container spacing={2} sx={{ mb: 3 }}>\n                <Grid item xs={12} md={6}>\n                  <TextField\n                    fullWidth\n                    label=\"Inserisci numero bobina\"\n                    variant=\"outlined\"\n                    placeholder=\"Inserisci solo il numero (Y) o 'v' per bobina vuota\"\n                    helperText={formErrors.id_bobina_input || \"Inserisci solo il numero della bobina (parte Y del codice Cx_By), 'v' per bobina vuota o 'q' per annullare\"}\n                    error={!!formErrors.id_bobina_input}\n                    onBlur={handleBobinaNumberInput}\n                  />\n                </Grid>\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                    ID Bobina completo: {formData.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : formData.id_bobina || '-'}\n                  </Typography>\n                </Grid>\n              </Grid>\n\n              {/* Tabella delle bobine disponibili */}\n              <Typography variant=\"subtitle1\" gutterBottom sx={{ mt: 3, fontWeight: 'bold' }}>\n                Bobine disponibili compatibili con il cavo selezionato\n              </Typography>\n\n              {bobine.length > 0 ? (\n                <Box sx={{ overflowX: 'auto' }}>\n                  <table style={{ width: '100%', borderCollapse: 'collapse', marginBottom: '20px' }}>\n                    <thead>\n                      <tr style={{ borderBottom: '2px solid #ddd', backgroundColor: '#f5f5f5' }}>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>ID</th>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>Tipologia</th>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>Cond.</th>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>Sezione</th>\n                        <th style={{ padding: '8px', textAlign: 'right' }}>Residui</th>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>Stato</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {bobine.map((bobina) => {\n                        const isCompatible = selectedCavo &&\n                          bobina.tipologia === selectedCavo.tipologia &&\n                          bobina.n_conduttori === selectedCavo.n_conduttori &&\n                          bobina.sezione === selectedCavo.sezione;\n\n                        const hasSufficient = bobina.metri_residui >= parseFloat(formData.metri_posati || 0);\n\n                        return (\n                          <tr\n                            key={bobina.id_bobina}\n                            style={{\n                              borderBottom: '1px solid #ddd',\n                              backgroundColor: isCompatible ? (hasSufficient ? '#e8f5e9' : '#fff8e1') : 'transparent',\n                              cursor: 'pointer'\n                            }}\n                            onClick={() => {\n                              if (hasSufficient) {\n                                setFormData({\n                                  ...formData,\n                                  id_bobina: bobina.id_bobina\n                                });\n                              }\n                            }}\n                          >\n                            <td style={{ padding: '8px' }}>{getBobinaNumber(bobina.id_bobina)}</td>\n                            <td style={{ padding: '8px' }}>{bobina.tipologia || 'N/A'}</td>\n                            <td style={{ padding: '8px' }}>{bobina.n_conduttori || 'N/A'}</td>\n                            <td style={{ padding: '8px' }}>{bobina.sezione || 'N/A'}</td>\n                            <td style={{ padding: '8px', textAlign: 'right' }}>{bobina.metri_residui || 0} m</td>\n                            <td style={{ padding: '8px' }}>\n                              <Chip\n                                size=\"small\"\n                                label={bobina.stato_bobina}\n                                color={\n                                  bobina.stato_bobina === 'Disponibile' ? 'success' :\n                                  bobina.stato_bobina === 'In uso' ? 'primary' :\n                                  bobina.stato_bobina === 'Over' ? 'error' :\n                                  bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n                                }\n                              />\n                            </td>\n                          </tr>\n                        );\n                      })}\n                    </tbody>\n                  </table>\n                </Box>\n              ) : (\n                <Alert severity=\"info\" sx={{ mt: 2, mb: 3 }}>\n                  Non ci sono bobine disponibili compatibili con il cavo selezionato.\n                </Alert>\n              )}\n\n              {/* Opzioni speciali */}\n              <Box sx={{ mt: 3, mb: 3 }}>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  Opzioni speciali:\n                </Typography>\n                <Grid container spacing={2}>\n                  <Grid item>\n                    <Button\n                      variant=\"outlined\"\n                      onClick={() => {\n                        setFormData({\n                          ...formData,\n                          id_bobina: 'BOBINA_VUOTA'\n                        });\n                      }}\n                    >\n                      Bobina Vuota (v)\n                    </Button>\n                  </Grid>\n                  <Grid item>\n                    <Button\n                      variant=\"outlined\"\n                      color=\"secondary\"\n                      onClick={() => {\n                        setFormData({\n                          ...formData,\n                          id_bobina: ''\n                        });\n                      }}\n                    >\n                      Nessuna Bobina (q)\n                    </Button>\n                  </Grid>\n                </Grid>\n              </Box>\n\n              {/* Selezione dalla lista (manteniamo anche questa per compatibilità) */}\n              <FormControl fullWidth>\n                <InputLabel id=\"bobina-select-label\">Seleziona Bobina dalla lista</InputLabel>\n                <Select\n                  labelId=\"bobina-select-label\"\n                  id=\"bobina-select\"\n                  name=\"id_bobina\"\n                  value={formData.id_bobina}\n                  label=\"Seleziona Bobina dalla lista\"\n                  onChange={handleFormChange}\n                >\n                  <MenuItem value=\"\">\n                    <em>Nessuna bobina</em>\n                  </MenuItem>\n                  <MenuItem value=\"BOBINA_VUOTA\">\n                    <em>BOBINA VUOTA</em>\n                  </MenuItem>\n                  {bobine.map((bobina) => (\n                    <MenuItem\n                      key={bobina.id_bobina}\n                      value={bobina.id_bobina}\n                      disabled={bobina.metri_residui < parseFloat(formData.metri_posati)}\n                    >\n                      {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'} - Residui: {bobina.metri_residui || 0} m\n                    </MenuItem>\n                  ))}\n                </Select>\n                <FormHelperText>\n                  Seleziona una bobina con metri residui sufficienti o lascia vuoto per non associare alcuna bobina\n                </FormHelperText>\n              </FormControl>\n\n              {/* Mostra dettagli della bobina selezionata */}\n              {formData.id_bobina && (\n                <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n                  <Typography variant=\"subtitle2\" gutterBottom>\n                    Dettagli bobina selezionata\n                  </Typography>\n                  {(() => {\n                    const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                    if (bobina) {\n                      return (\n                        <Grid container spacing={2}>\n                          <Grid item xs={12} md={6}>\n                            <Typography variant=\"body2\">\n                              <strong>Numero:</strong> {getBobinaNumber(bobina.id_bobina)}\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Tipologia:</strong> {bobina.tipologia || 'N/A'}\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Conduttori:</strong> {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                            </Typography>\n                          </Grid>\n                          <Grid item xs={12} md={6}>\n                            <Typography variant=\"body2\">\n                              <strong>Metri totali:</strong> {bobina.metri_totali || 0} m\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Metri residui:</strong> {bobina.metri_residui || 0} m\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Stato:</strong> {bobina.stato_bobina || 'N/A'}\n                            </Typography>\n                          </Grid>\n                        </Grid>\n                      );\n                    }\n                    return (\n                      <Typography variant=\"body2\" color=\"error\">\n                        Bobina non trovata nel database\n                      </Typography>\n                    );\n                  })()}\n                </Box>\n              )}\n            </Box>\n          )}\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = (idBobina) => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Ottieni il numero della bobina se presente\n    const numeroBobina = formData.id_bobina ? getBobinaNumber(formData.id_bobina) : 'Nessuna';\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body1\">\n                <strong>ID Cavo:</strong> {selectedCavo.id_cavo}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'} x {selectedCavo.sezione || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body1\">\n                <strong>Metri Posati:</strong> {formData.metri_posati}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Bobina Associata:</strong> {numeroBobina}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Stato Installazione:</strong> {parseFloat(formData.metri_posati) >= parseFloat(selectedCavo.metri_teorici) ? 'Installato' : 'In corso'}\n              </Typography>\n            </Grid>\n          </Grid>\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      case 1:\n        return renderStep2();\n      case 2:\n        return renderStep3();\n      case 3:\n        return renderStep4();\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  return (\n    <Box>\n      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>\n        {steps.map((label) => (\n          <Step key={label}>\n            <StepLabel>{label}</StepLabel>\n          </Step>\n        ))}\n      </Stepper>\n\n      <Box sx={{ mt: 2, mb: 4 }}>\n        {getStepContent(activeStep)}\n      </Box>\n\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\n        <Button\n          variant=\"outlined\"\n          color=\"secondary\"\n          onClick={activeStep === 0 ? () => navigate('/dashboard/cavi/posa') : handleBack}\n          startIcon={<ArrowBackIcon />}\n          disabled={loading}\n        >\n          {activeStep === 0 ? 'Annulla' : 'Indietro'}\n        </Button>\n\n        <Button\n          variant=\"contained\"\n          color=\"primary\"\n          onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}\n          endIcon={activeStep === steps.length - 1 ? <SaveIcon /> : <ArrowForwardIcon />}\n          disabled={loading || (activeStep === 0 && !selectedCavo)}\n        >\n          {loading ? (\n            <CircularProgress size={24} />\n          ) : activeStep === steps.length - 1 ? (\n            'Salva'\n          ) : (\n            'Avanti'\n          )}\n        </Button>\n      </Box>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,QACC,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,aAAa,EAC1BC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsD,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwD,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAAC0D,IAAI,EAAEC,OAAO,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC4D,MAAM,EAAEC,SAAS,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACkE,QAAQ,EAAEC,WAAW,CAAC,GAAGnE,QAAQ,CAAC;IACvCoE,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM2E,KAAK,GAAG,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,UAAU,CAAC;;EAEjF;EACA1E,SAAS,CAAC,MAAM;IACd2E,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC/B,UAAU,CAAC,CAAC;;EAEhB;EACA5C,SAAS,CAAC,MAAM;IACd,IAAIiD,UAAU,KAAK,CAAC,EAAE;MACpB2B,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAC3B,UAAU,EAAEL,UAAU,CAAC,CAAC;;EAE5B;EACA,MAAM+B,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFrB,cAAc,CAAC,IAAI,CAAC;MACpB;MACA,MAAMuB,QAAQ,GAAG,MAAMzC,WAAW,CAAC0C,OAAO,CAAClC,UAAU,CAAC;;MAEtD;MACA;MACAc,OAAO,CAACmB,QAAQ,CAAC;IACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDjC,OAAO,CAAC,mCAAmC,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACR3B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMsB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFpB,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAM0B,UAAU,GAAG,MAAM7C,gBAAgB,CAAC8C,SAAS,CAACvC,UAAU,CAAC;;MAE/D;MACA,IAAIwC,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM,IAC/C,CAACA,MAAM,CAACC,YAAY,KAAK,aAAa,IAAID,MAAM,CAACC,YAAY,KAAK,QAAQ,KAC1ED,MAAM,CAACC,YAAY,KAAK,MAAM,IAAID,MAAM,CAACC,YAAY,KAAK,WAC5D,CAAC;;MAED;MACA,IAAI1B,YAAY,EAAE;QAChB;QACA,IAAIA,YAAY,CAAC2B,SAAS,IAAI3B,YAAY,CAAC4B,YAAY,IAAI5B,YAAY,CAAC6B,OAAO,EAAE;UAC/E,MAAMC,iBAAiB,GAAGP,kBAAkB,CAACC,MAAM,CAACC,MAAM,IACxDA,MAAM,CAACE,SAAS,KAAK3B,YAAY,CAAC2B,SAAS,IAC3CF,MAAM,CAACG,YAAY,KAAK5B,YAAY,CAAC4B,YAAY,IACjDH,MAAM,CAACI,OAAO,KAAK7B,YAAY,CAAC6B,OAClC,CAAC;;UAED;UACA,IAAIC,iBAAiB,CAACC,MAAM,GAAG,CAAC,EAAE;YAChCR,kBAAkB,GAAGO,iBAAiB;UACxC,CAAC,MAAM;YACLX,OAAO,CAACa,GAAG,CAAC,wEAAwE,CAAC;UACvF;QACF;;QAEA;QACAT,kBAAkB,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;MACtE;MAEArC,SAAS,CAACwB,kBAAkB,CAAC;IAC/B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DjC,OAAO,CAAC,uCAAuC,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRzB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM0C,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACnC,WAAW,CAACoC,IAAI,CAAC,CAAC,EAAE;MACvBrD,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFQ,cAAc,CAAC,IAAI,CAAC;MACpB,MAAM8C,QAAQ,GAAG,MAAMhE,WAAW,CAACiE,WAAW,CAACzD,UAAU,EAAEmB,WAAW,CAACoC,IAAI,CAAC,CAAC,CAAC;;MAE9E;MACA,IAAIC,QAAQ,CAACE,mBAAmB,KAAK,YAAY,IAAKF,QAAQ,CAACG,eAAe,IAAIH,QAAQ,CAACG,eAAe,GAAG,CAAE,EAAE;QAC/G;QACA,MAAMtB,OAAO,GAAG,WAAWmB,QAAQ,CAACjC,OAAO,wBAAwBiC,QAAQ,CAACG,eAAe,IAAI,CAAC;AACxG;AACA;AACA;AACA;AACA,0BAA0B;QAElB,MAAMC,UAAU,GAAGC,MAAM,CAACC,OAAO,CAACzB,OAAO,CAAC;QAE1C,IAAIuB,UAAU,EAAE;UACd;UACA;UACAxD,QAAQ,CAAC,mCAAmCJ,UAAU,IAAIwD,QAAQ,CAACjC,OAAO,EAAE,CAAC;QAC/E;QAEAb,cAAc,CAAC,KAAK,CAAC;QACrB;MACF;;MAEA;MACA,IAAI8C,QAAQ,CAACO,sBAAsB,KAAK,CAAC,EAAE;QACzC;MAAA;;MAGF;MACAC,gBAAgB,CAACR,QAAQ,CAAC;IAC5B,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDjC,OAAO,CAAC,2CAA2C,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAChG,CAAC,SAAS;MACR3B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMsD,gBAAgB,GAAIC,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACP,mBAAmB,KAAK,YAAY,IAAKO,IAAI,CAACN,eAAe,IAAIM,IAAI,CAACN,eAAe,GAAG,CAAE,EAAE;MACnG;MACA,MAAMtB,OAAO,GAAG,WAAW4B,IAAI,CAAC1C,OAAO,wBAAwB0C,IAAI,CAACN,eAAe,IAAI,CAAC;AAC9F;AACA;AACA;AACA;AACA,0BAA0B;MAEpB,MAAMC,UAAU,GAAGC,MAAM,CAACC,OAAO,CAACzB,OAAO,CAAC;MAE1C,IAAIuB,UAAU,EAAE;QACd;QACA;QACAxD,QAAQ,CAAC,mCAAmCJ,UAAU,IAAIiE,IAAI,CAAC1C,OAAO,EAAE,CAAC;MAC3E;MAEA;IACF;IACA;IAAA,KACK,IAAI0C,IAAI,CAACF,sBAAsB,KAAK,CAAC,EAAE;MAC1C;MACA,IAAIF,MAAM,CAACC,OAAO,CAAC,WAAWG,IAAI,CAAC1C,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACA2C,eAAe,CAACD,IAAI,CAAC1C,OAAO,CAAC,CAAC4C,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAGH,IAAI;YAAEF,sBAAsB,EAAE;UAAE,CAAC;UAC1D7C,eAAe,CAACkD,WAAW,CAAC;UAC5B9C,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAE6C,WAAW,CAAC7C,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAlB,aAAa,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC+D,KAAK,CAAClC,KAAK,IAAI;UAChBC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvEjC,OAAO,CAAC,kDAAkD,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACAnB,eAAe,CAAC+C,IAAI,CAAC;MACrB3C,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAE0C,IAAI,CAAC1C,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAlB,aAAa,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM4D,eAAe,GAAG,MAAOI,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAM9E,WAAW,CAAC0E,eAAe,CAAClE,UAAU,EAAEsE,MAAM,CAAC;MACrDrE,SAAS,CAAC,QAAQqE,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvEjC,OAAO,CAAC,kDAAkD,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMF,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMoC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCrD,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACoD,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACAE,aAAa,CAACH,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAACH,IAAI,EAAEC,KAAK,KAAK;IACrC,IAAIvC,KAAK,GAAG,IAAI;IAChB,IAAI0C,OAAO,GAAG,IAAI;IAElB,IAAIJ,IAAI,KAAK,cAAc,EAAE;MAC3B,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACnB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCpB,KAAK,GAAG,uCAAuC;MACjD,CAAC,MAAM,IAAI2C,KAAK,CAACC,UAAU,CAACL,KAAK,CAAC,CAAC,IAAIK,UAAU,CAACL,KAAK,CAAC,IAAI,CAAC,EAAE;QAC7DvC,KAAK,GAAG,sCAAsC;MAChD,CAAC,MAAM,IAAIlB,YAAY,IAAIA,YAAY,CAAC+D,aAAa,IAAID,UAAU,CAACL,KAAK,CAAC,GAAGK,UAAU,CAAC9D,YAAY,CAAC+D,aAAa,CAAC,EAAE;QACnHH,OAAO,GAAG,kDAAkD;MAC9D;IACF;;IAEA;IACAlD,aAAa,CAACsD,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACR,IAAI,GAAGtC;IACV,CAAC,CAAC,CAAC;;IAEH;IACAN,eAAe,CAACoD,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACR,IAAI,GAAGI;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC1C,KAAK;EACf,CAAC;;EAED;EACA,MAAM+C,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAC/D,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAAC+B,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjE6B,MAAM,CAAC5D,YAAY,GAAG,uCAAuC;MAC7D2D,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAIL,KAAK,CAACC,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAIuD,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7F4D,MAAM,CAAC5D,YAAY,GAAG,sCAAsC;MAC5D2D,OAAO,GAAG,KAAK;IACjB;IAEAxD,aAAa,CAACyD,MAAM,CAAC;IACrB,OAAOD,OAAO;EAChB,CAAC;;EAED;EACA,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIhF,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAAC6E,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF;IAEA5E,aAAa,CAAEgF,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBjF,aAAa,CAAEgF,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxBlF,aAAa,CAAC,CAAC,CAAC;IAChBY,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAM4D,2BAA2B,GAAGA,CAACC,WAAW,EAAEC,YAAY,KAAK;IACjE,IAAI,CAACD,WAAW,IAAIX,UAAU,CAACW,WAAW,CAAC,IAAI,CAAC,EAAE;MAChD,OAAO,eAAe;IACxB;IAEA,IAAIX,UAAU,CAACW,WAAW,CAAC,IAAIX,UAAU,CAACY,YAAY,CAAC,EAAE;MACvD,OAAO,YAAY;IACrB;IAEA,OAAO,UAAU;EACnB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFpF,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAAC0E,YAAY,CAAC,CAAC,EAAE;QACnB1E,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAMkF,WAAW,GAAGX,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAIqE,QAAQ,GAAGxE,QAAQ,CAACI,SAAS,IAAI,IAAI;MACzC,IAAIoE,QAAQ,KAAK,cAAc,EAAE;QAC/BzD,OAAO,CAACa,GAAG,CAAC,iCAAiC,CAAC;MAChD;;MAEA;MACA,MAAM6C,kBAAkB,GAAGL,2BAA2B,CAACC,WAAW,EAAEzE,YAAY,CAAC+D,aAAa,CAAC;;MAE/F;MACA5C,OAAO,CAACa,GAAG,CAAC,aAAa,EAAE;QACzBjD,UAAU;QACVsE,MAAM,EAAEjD,QAAQ,CAACE,OAAO;QACxBmE,WAAW;QACXG,QAAQ;QACRC;MACF,CAAC,CAAC;;MAEF;MACA,MAAMtG,WAAW,CAACuG,iBAAiB,CACjC/F,UAAU,EACVqB,QAAQ,CAACE,OAAO,EAChBmE,WAAW,EACXG,QACF,CAAC;;MAED;MACA,IAAIG,cAAc,GAAG,qDAAqDF,kBAAkB,EAAE;MAC9F,IAAID,QAAQ,KAAK,cAAc,EAAE;QAC/BG,cAAc,IAAI,iCAAiC;MACrD,CAAC,MAAM,IAAIH,QAAQ,EAAE;QACnB,MAAMnD,MAAM,GAAG3B,MAAM,CAACkF,IAAI,CAAC7C,CAAC,IAAIA,CAAC,CAAC3B,SAAS,KAAKoE,QAAQ,CAAC;QACzD,IAAInD,MAAM,EAAE;UACVsD,cAAc,IAAI,gCAAgCH,QAAQ,EAAE;QAC9D;MACF;;MAEA;MACA5F,SAAS,CAAC+F,cAAc,CAAC;;MAEzB;MACAR,WAAW,CAAC,CAAC;;MAEb;MACAzD,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;MACzEjC,OAAO,CAAC,oDAAoD,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACzG,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0F,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACEtG,OAAA,CAACvC,GAAG;MAAA8I,QAAA,gBACFvG,OAAA,CAACrC,UAAU;QAAC6I,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb7G,OAAA,CAACtC,KAAK;QAACoJ,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzBvG,OAAA,CAACrC,UAAU;UAAC6I,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb7G,OAAA,CAAC/B,IAAI;UAACgJ,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAZ,QAAA,gBAC7CvG,OAAA,CAAC/B,IAAI;YAACmJ,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACfvG,OAAA,CAACpC,SAAS;cACR0J,SAAS;cACTC,KAAK,EAAC,SAAS;cACff,OAAO,EAAC,UAAU;cAClB1B,KAAK,EAAEvD,WAAY;cACnBiG,QAAQ,EAAG5C,CAAC,IAAKpD,cAAc,CAACoD,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAChD2C,WAAW,EAAC;YAAyB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP7G,OAAA,CAAC/B,IAAI;YAACmJ,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACfvG,OAAA,CAACnC,MAAM;cACLyJ,SAAS;cACTd,OAAO,EAAC,WAAW;cACnBkB,KAAK,EAAC,SAAS;cACfC,OAAO,EAAEjE,oBAAqB;cAC9BkE,QAAQ,EAAE/G,WAAW,IAAI,CAACU,WAAW,CAACoC,IAAI,CAAC,CAAE;cAC7CkE,SAAS,EAAEhH,WAAW,gBAAGb,OAAA,CAACrB,gBAAgB;gBAACmJ,IAAI,EAAE;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG7G,OAAA,CAAChB,UAAU;gBAAA0H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAN,QAAA,EAC1E;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGR7G,OAAA,CAACtC,KAAK;QAACoJ,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBvG,OAAA,CAACrC,UAAU;UAAC6I,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZhG,WAAW,gBACVb,OAAA,CAACvC,GAAG;UAACqJ,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DvG,OAAA,CAACrB,gBAAgB;YAAA+H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJ5F,IAAI,CAACmC,MAAM,KAAK,CAAC,gBACnBpD,OAAA,CAACtB,KAAK;UAACwJ,QAAQ,EAAC,MAAM;UAAA3B,QAAA,EAAC;QAEvB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAER7G,OAAA,CAAC1B,IAAI;UAACwI,EAAE,EAAE;YAAEqB,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA7B,QAAA,EAChDtF,IAAI,CAACoH,GAAG,CAAEhE,IAAI,iBACbrE,OAAA,CAAC1C,KAAK,CAAC2C,QAAQ;YAAAsG,QAAA,gBACbvG,OAAA,CAACzB,QAAQ;cAAC+J,MAAM;cAACX,OAAO,EAAEA,CAAA,KAAMvD,gBAAgB,CAACC,IAAI,CAAE;cAAAkC,QAAA,eACrDvG,OAAA,CAACxB,YAAY;gBACX+J,OAAO,eACLvI,OAAA,CAACvC,GAAG;kBAACqJ,EAAE,EAAE;oBAAEiB,OAAO,EAAE,MAAM;oBAAEZ,UAAU,EAAE;kBAAS,CAAE;kBAAAZ,QAAA,gBACjDvG,OAAA,CAACrC,UAAU;oBAAC6I,OAAO,EAAC,WAAW;oBAAAD,QAAA,EAAElC,IAAI,CAAC1C;kBAAO;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,EAC1DxC,IAAI,CAACF,sBAAsB,KAAK,CAAC,gBAChCnE,OAAA,CAAClB,IAAI;oBACHgJ,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,OAAO;oBACbG,KAAK,EAAC,OAAO;oBACbZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,GACAxC,IAAI,CAACP,mBAAmB,KAAK,YAAY,IAAKO,IAAI,CAACN,eAAe,IAAIM,IAAI,CAACN,eAAe,GAAG,CAAE,gBACjG/D,OAAA,CAAClB,IAAI;oBACHgJ,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,YAAY;oBAClBG,KAAK,EAAC,SAAS;oBACfZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,gBAEF7G,OAAA,CAAClB,IAAI;oBACHgJ,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAElD,IAAI,CAACP,mBAAoB;oBAChC4D,KAAK,EAAErD,IAAI,CAACP,mBAAmB,KAAK,UAAU,GAAG,SAAS,GAAG,SAAU;oBACvEgD,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACD4B,SAAS,eACPzI,OAAA,CAAAE,SAAA;kBAAAqG,QAAA,gBACEvG,OAAA,CAACrC,UAAU;oBAAC6I,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GACzClC,IAAI,CAACrB,SAAS,IAAI,KAAK,EAAC,KAAG,EAACqB,IAAI,CAACpB,YAAY,IAAI,KAAK,EAAC,KAAG,EAACoB,IAAI,CAACnB,OAAO,IAAI,KAAK;kBAAA;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACb7G,OAAA;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN7G,OAAA,CAACrC,UAAU;oBAAC6I,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,MACvC,EAAClC,IAAI,CAACsE,mBAAmB,IAAI,KAAK,EAAC,QAAM,EAACtE,IAAI,CAACuE,iBAAiB,IAAI,KAAK;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACb7G,OAAA;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN7G,OAAA,CAACrC,UAAU;oBAAC6I,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,iBAC5B,EAAClC,IAAI,CAACe,aAAa,IAAI,KAAK,EAAC,mBAAiB,EAACf,IAAI,CAACN,eAAe,IAAI,GAAG;kBAAA;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC;gBAAA,eACb;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACX7G,OAAA,CAACvB,OAAO;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GA/CQxC,IAAI,CAAC1C,OAAO;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgDjB,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMgC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACxH,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACErB,OAAA,CAACvC,GAAG;MAAA8I,QAAA,gBACFvG,OAAA,CAACrC,UAAU;QAAC6I,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb7G,OAAA,CAACtC,KAAK;QAACoJ,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzBvG,OAAA,CAACrC,UAAU;UAAC6I,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7G,OAAA,CAAC/B,IAAI;UAACgJ,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAX,QAAA,gBACzBvG,OAAA,CAAC/B,IAAI;YAACmJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACyB,EAAE,EAAE,CAAE;YAAAvC,QAAA,gBACvBvG,OAAA,CAACrC,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBvG,OAAA;gBAAAuG,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxF,YAAY,CAACM,OAAO;YAAA;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACb7G,OAAA,CAACrC,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBvG,OAAA;gBAAAuG,QAAA,EAAQ;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxF,YAAY,CAAC2B,SAAS,IAAI,KAAK;YAAA;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACb7G,OAAA,CAACrC,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBvG,OAAA;gBAAAuG,QAAA,EAAQ;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxF,YAAY,CAAC4B,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC5B,YAAY,CAAC6B,OAAO,IAAI,KAAK;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACP7G,OAAA,CAAC/B,IAAI;YAACmJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACyB,EAAE,EAAE,CAAE;YAAAvC,QAAA,gBACvBvG,OAAA,CAACrC,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBvG,OAAA;gBAAAuG,QAAA,EAAQ;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxF,YAAY,CAACsH,mBAAmB,IAAI,KAAK;YAAA;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACb7G,OAAA,CAACrC,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBvG,OAAA;gBAAAuG,QAAA,EAAQ;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxF,YAAY,CAACuH,iBAAiB,IAAI,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACb7G,OAAA,CAACrC,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBvG,OAAA;gBAAAuG,QAAA,EAAQ;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxF,YAAY,CAAC+D,aAAa,IAAI,KAAK;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAER7G,OAAA,CAACtC,KAAK;QAACoJ,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBvG,OAAA,CAACrC,UAAU;UAAC6I,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7G,OAAA,CAACpC,SAAS;UACR0J,SAAS;UACTC,KAAK,EAAC,cAAc;UACpBf,OAAO,EAAC,UAAU;UAClB3B,IAAI,EAAC,cAAc;UACnBkE,IAAI,EAAC,QAAQ;UACbjE,KAAK,EAAErD,QAAQ,CAACG,YAAa;UAC7B4F,QAAQ,EAAE7C,gBAAiB;UAC3BpC,KAAK,EAAE,CAAC,CAACT,UAAU,CAACF,YAAa;UACjCoH,UAAU,EAAElH,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;UACjEqH,mBAAmB,EAAE;YACnBnC,EAAE,EAAE;cAAEY,KAAK,EAAE1F,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;YAAa;UACrG,CAAE;UACFkF,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAEF7G,OAAA,CAACtB,KAAK;UAACwJ,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAEoC,EAAE,EAAE;UAAE,CAAE;UAAA3C,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMsC,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAMC,eAAe,GAAInD,QAAQ,IAAK;MACpC;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACoD,QAAQ,CAAC,IAAI,CAAC,EAAE;QACvC,OAAOpD,QAAQ,CAACqD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAChC;MACA,OAAOrD,QAAQ;IACjB,CAAC;;IAED;IACA,MAAMsD,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAIpJ,UAAU,KAAKoJ,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAI3G,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAACrB,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAOuD,UAAU,CAACrC,MAAM,CAACW,aAAa,CAAC,IAAI0B,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAM8H,uBAAuB,GAAI9E,CAAC,IAAK;MACrC,MAAM4E,YAAY,GAAG5E,CAAC,CAACG,MAAM,CAACD,KAAK,CAACnB,IAAI,CAAC,CAAC;;MAE1C;MACA,IAAI6F,YAAY,CAACG,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtC;QACAjI,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb8H,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIJ,YAAY,CAACG,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtC;QACAjI,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb8H,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIJ,YAAY,EAAE;QAChB;QACA,MAAMK,gBAAgB,GAAGN,iBAAiB,CAACC,YAAY,CAAC;;QAExD;QACA,MAAMM,eAAe,GAAG3I,MAAM,CAACkF,IAAI,CAAC7C,CAAC,IAAIA,CAAC,CAAC3B,SAAS,KAAKgI,gBAAgB,CAAC;QAE1E,IAAIC,eAAe,EAAE;UACnB;UACA,IAAIA,eAAe,CAAC/G,YAAY,KAAK,MAAM,IAAI+G,eAAe,CAAC/G,YAAY,KAAK,WAAW,EAAE;YAC3FhB,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb8H,eAAe,EAAE,aAAaJ,YAAY,eAAeM,eAAe,CAAC/G,YAAY;YACvF,CAAC,CAAC;YACF;UACF;;UAEA;UACA,IAAI0G,mBAAmB,CAACK,eAAe,CAAC,EAAE;YACxC;YACApI,WAAW,CAAC;cACV,GAAGD,QAAQ;cACXI,SAAS,EAAEgI;YACb,CAAC,CAAC;YACF9H,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb8H,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACA7H,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb8H,eAAe,EAAE,aAAaJ,YAAY,sCAAsCM,eAAe,CAACrG,aAAa;YAC/G,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACA1B,aAAa,CAAC;YACZ,GAAGD,UAAU;YACb8H,eAAe,EAAE,UAAUJ,YAAY;UACzC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACA9H,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb8H,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,oBACE5J,OAAA,CAACvC,GAAG;MAAA8I,QAAA,gBACFvG,OAAA,CAACrC,UAAU;QAAC6I,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb7G,OAAA,CAACtC,KAAK;QAACoJ,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBvG,OAAA,CAACrC,UAAU;UAAC6I,OAAO,EAAC,OAAO;UAACuD,SAAS;UAAAxD,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZ9F,aAAa,gBACZf,OAAA,CAACvC,GAAG;UAACqJ,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DvG,OAAA,CAACrB,gBAAgB;YAAA+H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAEN7G,OAAA,CAACvC,GAAG;UAAA8I,QAAA,gBAEFvG,OAAA,CAAC/B,IAAI;YAACgJ,SAAS;YAACC,OAAO,EAAE,CAAE;YAACJ,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,gBACxCvG,OAAA,CAAC/B,IAAI;cAACmJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACyB,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBvG,OAAA,CAACpC,SAAS;gBACR0J,SAAS;gBACTC,KAAK,EAAC,yBAAyB;gBAC/Bf,OAAO,EAAC,UAAU;gBAClBiB,WAAW,EAAC,qDAAqD;gBACjEuB,UAAU,EAAElH,UAAU,CAAC8H,eAAe,IAAI,4GAA6G;gBACvJrH,KAAK,EAAE,CAAC,CAACT,UAAU,CAAC8H,eAAgB;gBACpCI,MAAM,EAAEN;cAAwB;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP7G,OAAA,CAAC/B,IAAI;cAACmJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACyB,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBvG,OAAA,CAACrC,UAAU;gBAAC6I,OAAO,EAAC,OAAO;gBAACM,EAAE,EAAE;kBAAEoC,EAAE,EAAE;gBAAE,CAAE;gBAAA3C,QAAA,GAAC,sBACrB,EAAC9E,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,cAAc,GAAGJ,QAAQ,CAACI,SAAS,IAAI,GAAG;cAAA;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGP7G,OAAA,CAACrC,UAAU;YAAC6I,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEoC,EAAE,EAAE,CAAC;cAAEe,UAAU,EAAE;YAAO,CAAE;YAAA1D,QAAA,EAAC;UAEhF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZ1F,MAAM,CAACiC,MAAM,GAAG,CAAC,gBAChBpD,OAAA,CAACvC,GAAG;YAACqJ,EAAE,EAAE;cAAEoD,SAAS,EAAE;YAAO,CAAE;YAAA3D,QAAA,eAC7BvG,OAAA;cAAOmK,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEC,YAAY,EAAE;cAAO,CAAE;cAAA/D,QAAA,gBAChFvG,OAAA;gBAAAuG,QAAA,eACEvG,OAAA;kBAAImK,KAAK,EAAE;oBAAEI,YAAY,EAAE,gBAAgB;oBAAEC,eAAe,EAAE;kBAAU,CAAE;kBAAAjE,QAAA,gBACxEvG,OAAA;oBAAImK,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAAnE,QAAA,EAAC;kBAAE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzD7G,OAAA;oBAAImK,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAAnE,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChE7G,OAAA;oBAAImK,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAAnE,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5D7G,OAAA;oBAAImK,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAAnE,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9D7G,OAAA;oBAAImK,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAQ,CAAE;oBAAAnE,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/D7G,OAAA;oBAAImK,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAAnE,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR7G,OAAA;gBAAAuG,QAAA,EACGpF,MAAM,CAACkH,GAAG,CAAEvF,MAAM,IAAK;kBACtB,MAAM6H,YAAY,GAAGtJ,YAAY,IAC/ByB,MAAM,CAACE,SAAS,KAAK3B,YAAY,CAAC2B,SAAS,IAC3CF,MAAM,CAACG,YAAY,KAAK5B,YAAY,CAAC4B,YAAY,IACjDH,MAAM,CAACI,OAAO,KAAK7B,YAAY,CAAC6B,OAAO;kBAEzC,MAAM0H,aAAa,GAAG9H,MAAM,CAACW,aAAa,IAAI0B,UAAU,CAAC1D,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC;kBAEpF,oBACE5B,OAAA;oBAEEmK,KAAK,EAAE;sBACLI,YAAY,EAAE,gBAAgB;sBAC9BC,eAAe,EAAEG,YAAY,GAAIC,aAAa,GAAG,SAAS,GAAG,SAAS,GAAI,aAAa;sBACvFC,MAAM,EAAE;oBACV,CAAE;oBACFlD,OAAO,EAAEA,CAAA,KAAM;sBACb,IAAIiD,aAAa,EAAE;wBACjBlJ,WAAW,CAAC;0BACV,GAAGD,QAAQ;0BACXI,SAAS,EAAEiB,MAAM,CAACjB;wBACpB,CAAC,CAAC;sBACJ;oBACF,CAAE;oBAAA0E,QAAA,gBAEFvG,OAAA;sBAAImK,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAlE,QAAA,EAAE6C,eAAe,CAACtG,MAAM,CAACjB,SAAS;oBAAC;sBAAA6E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvE7G,OAAA;sBAAImK,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAlE,QAAA,EAAEzD,MAAM,CAACE,SAAS,IAAI;oBAAK;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC/D7G,OAAA;sBAAImK,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAlE,QAAA,EAAEzD,MAAM,CAACG,YAAY,IAAI;oBAAK;sBAAAyD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAClE7G,OAAA;sBAAImK,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAlE,QAAA,EAAEzD,MAAM,CAACI,OAAO,IAAI;oBAAK;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7D7G,OAAA;sBAAImK,KAAK,EAAE;wBAAEM,OAAO,EAAE,KAAK;wBAAEC,SAAS,EAAE;sBAAQ,CAAE;sBAAAnE,QAAA,GAAEzD,MAAM,CAACW,aAAa,IAAI,CAAC,EAAC,IAAE;oBAAA;sBAAAiD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrF7G,OAAA;sBAAImK,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAlE,QAAA,eAC5BvG,OAAA,CAAClB,IAAI;wBACHgJ,IAAI,EAAC,OAAO;wBACZP,KAAK,EAAEzE,MAAM,CAACC,YAAa;wBAC3B2E,KAAK,EACH5E,MAAM,CAACC,YAAY,KAAK,aAAa,GAAG,SAAS,GACjDD,MAAM,CAACC,YAAY,KAAK,QAAQ,GAAG,SAAS,GAC5CD,MAAM,CAACC,YAAY,KAAK,MAAM,GAAG,OAAO,GACxCD,MAAM,CAACC,YAAY,KAAK,WAAW,GAAG,SAAS,GAAG;sBACnD;wBAAA2D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA,GA/BA/D,MAAM,CAACjB,SAAS;oBAAA6E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAgCnB,CAAC;gBAET,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,gBAEN7G,OAAA,CAACtB,KAAK;YAACwJ,QAAQ,EAAC,MAAM;YAACpB,EAAE,EAAE;cAAEoC,EAAE,EAAE,CAAC;cAAElC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,EAAC;UAE7C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR,eAGD7G,OAAA,CAACvC,GAAG;YAACqJ,EAAE,EAAE;cAAEoC,EAAE,EAAE,CAAC;cAAElC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,gBACxBvG,OAAA,CAACrC,UAAU;cAAC6I,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7G,OAAA,CAAC/B,IAAI;cAACgJ,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAX,QAAA,gBACzBvG,OAAA,CAAC/B,IAAI;gBAACmJ,IAAI;gBAAAb,QAAA,eACRvG,OAAA,CAACnC,MAAM;kBACL2I,OAAO,EAAC,UAAU;kBAClBmB,OAAO,EAAEA,CAAA,KAAM;oBACbjG,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXI,SAAS,EAAE;oBACb,CAAC,CAAC;kBACJ,CAAE;kBAAA0E,QAAA,EACH;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACP7G,OAAA,CAAC/B,IAAI;gBAACmJ,IAAI;gBAAAb,QAAA,eACRvG,OAAA,CAACnC,MAAM;kBACL2I,OAAO,EAAC,UAAU;kBAClBkB,KAAK,EAAC,WAAW;kBACjBC,OAAO,EAAEA,CAAA,KAAM;oBACbjG,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXI,SAAS,EAAE;oBACb,CAAC,CAAC;kBACJ,CAAE;kBAAA0E,QAAA,EACH;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGN7G,OAAA,CAAC9B,WAAW;YAACoJ,SAAS;YAAAf,QAAA,gBACpBvG,OAAA,CAAC7B,UAAU;cAAC2M,EAAE,EAAC,qBAAqB;cAAAvE,QAAA,EAAC;YAA4B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9E7G,OAAA,CAAC5B,MAAM;cACL2M,OAAO,EAAC,qBAAqB;cAC7BD,EAAE,EAAC,eAAe;cAClBjG,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAErD,QAAQ,CAACI,SAAU;cAC1B0F,KAAK,EAAC,8BAA8B;cACpCC,QAAQ,EAAE7C,gBAAiB;cAAA4B,QAAA,gBAE3BvG,OAAA,CAAC3B,QAAQ;gBAACyG,KAAK,EAAC,EAAE;gBAAAyB,QAAA,eAChBvG,OAAA;kBAAAuG,QAAA,EAAI;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACX7G,OAAA,CAAC3B,QAAQ;gBAACyG,KAAK,EAAC,cAAc;gBAAAyB,QAAA,eAC5BvG,OAAA;kBAAAuG,QAAA,EAAI;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,EACV1F,MAAM,CAACkH,GAAG,CAAEvF,MAAM,iBACjB9C,OAAA,CAAC3B,QAAQ;gBAEPyG,KAAK,EAAEhC,MAAM,CAACjB,SAAU;gBACxB+F,QAAQ,EAAE9E,MAAM,CAACW,aAAa,GAAG0B,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAE;gBAAA2E,QAAA,GAElE6C,eAAe,CAACtG,MAAM,CAACjB,SAAS,CAAC,EAAC,KAAG,EAACiB,MAAM,CAACE,SAAS,IAAI,KAAK,EAAC,cAAY,EAACF,MAAM,CAACW,aAAa,IAAI,CAAC,EAAC,IAC1G;cAAA,GALOX,MAAM,CAACjB,SAAS;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKb,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACT7G,OAAA,CAACpB,cAAc;cAAA2H,QAAA,EAAC;YAEhB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGbpF,QAAQ,CAACI,SAAS,iBACjB7B,OAAA,CAACvC,GAAG;YAACqJ,EAAE,EAAE;cAAEoC,EAAE,EAAE,CAAC;cAAEnC,CAAC,EAAE,CAAC;cAAEiE,OAAO,EAAE,kBAAkB;cAAEC,YAAY,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAoB,CAAE;YAAA3E,QAAA,gBAClGvG,OAAA,CAACrC,UAAU;cAAC6I,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZ,CAAC,MAAM;cACN,MAAM/D,MAAM,GAAG3B,MAAM,CAACkF,IAAI,CAAC7C,CAAC,IAAIA,CAAC,CAAC3B,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;cACnE,IAAIiB,MAAM,EAAE;gBACV,oBACE9C,OAAA,CAAC/B,IAAI;kBAACgJ,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAX,QAAA,gBACzBvG,OAAA,CAAC/B,IAAI;oBAACmJ,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACyB,EAAE,EAAE,CAAE;oBAAAvC,QAAA,gBACvBvG,OAAA,CAACrC,UAAU;sBAAC6I,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBvG,OAAA;wBAAAuG,QAAA,EAAQ;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACuC,eAAe,CAACtG,MAAM,CAACjB,SAAS,CAAC;oBAAA;sBAAA6E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACb7G,OAAA,CAACrC,UAAU;sBAAC6I,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBvG,OAAA;wBAAAuG,QAAA,EAAQ;sBAAU;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC/D,MAAM,CAACE,SAAS,IAAI,KAAK;oBAAA;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACb7G,OAAA,CAACrC,UAAU;sBAAC6I,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBvG,OAAA;wBAAAuG,QAAA,EAAQ;sBAAW;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC/D,MAAM,CAACG,YAAY,IAAI,KAAK,EAAC,KAAG,EAACH,MAAM,CAACI,OAAO,IAAI,KAAK;oBAAA;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACP7G,OAAA,CAAC/B,IAAI;oBAACmJ,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACyB,EAAE,EAAE,CAAE;oBAAAvC,QAAA,gBACvBvG,OAAA,CAACrC,UAAU;sBAAC6I,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBvG,OAAA;wBAAAuG,QAAA,EAAQ;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC/D,MAAM,CAACqI,YAAY,IAAI,CAAC,EAAC,IAC3D;oBAAA;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb7G,OAAA,CAACrC,UAAU;sBAAC6I,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBvG,OAAA;wBAAAuG,QAAA,EAAQ;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC/D,MAAM,CAACW,aAAa,IAAI,CAAC,EAAC,IAC7D;oBAAA;sBAAAiD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb7G,OAAA,CAACrC,UAAU;sBAAC6I,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBvG,OAAA;wBAAAuG,QAAA,EAAQ;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC/D,MAAM,CAACC,YAAY,IAAI,KAAK;oBAAA;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEX;cACA,oBACE7G,OAAA,CAACrC,UAAU;gBAAC6I,OAAO,EAAC,OAAO;gBAACkB,KAAK,EAAC,OAAO;gBAAAnB,QAAA,EAAC;cAE1C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAEjB,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAEA1F,MAAM,CAACiC,MAAM,KAAK,CAAC,IAAI,CAACrC,aAAa,iBACpCf,OAAA,CAACtB,KAAK;UAACwJ,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEoC,EAAE,EAAE;UAAE,CAAE;UAAA3C,QAAA,EAAC;QAEzC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMuE,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAMhC,eAAe,GAAInD,QAAQ,IAAK;MACpC;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACoD,QAAQ,CAAC,IAAI,CAAC,EAAE;QACvC,OAAOpD,QAAQ,CAACqD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAChC;MACA,OAAOrD,QAAQ;IACjB,CAAC;;IAED;IACA,MAAMuD,YAAY,GAAG/H,QAAQ,CAACI,SAAS,GAAGuH,eAAe,CAAC3H,QAAQ,CAACI,SAAS,CAAC,GAAG,SAAS;IAEzF,oBACE7B,OAAA,CAACvC,GAAG;MAAA8I,QAAA,gBACFvG,OAAA,CAACrC,UAAU;QAAC6I,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb7G,OAAA,CAACtC,KAAK;QAACoJ,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBvG,OAAA,CAACrC,UAAU;UAAC6I,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb7G,OAAA,CAAC/B,IAAI;UAACgJ,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAX,QAAA,gBACzBvG,OAAA,CAAC/B,IAAI;YAACmJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACyB,EAAE,EAAE,CAAE;YAAAvC,QAAA,gBACvBvG,OAAA,CAACrC,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBvG,OAAA;gBAAAuG,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxF,YAAY,CAACM,OAAO;YAAA;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACb7G,OAAA,CAACrC,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBvG,OAAA;gBAAAuG,QAAA,EAAQ;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxF,YAAY,CAAC2B,SAAS,IAAI,KAAK;YAAA;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACb7G,OAAA,CAACrC,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBvG,OAAA;gBAAAuG,QAAA,EAAQ;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxF,YAAY,CAAC4B,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC5B,YAAY,CAAC6B,OAAO,IAAI,KAAK;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACP7G,OAAA,CAAC/B,IAAI;YAACmJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACyB,EAAE,EAAE,CAAE;YAAAvC,QAAA,gBACvBvG,OAAA,CAACrC,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBvG,OAAA;gBAAAuG,QAAA,EAAQ;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACpF,QAAQ,CAACG,YAAY;YAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACb7G,OAAA,CAACrC,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBvG,OAAA;gBAAAuG,QAAA,EAAQ;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC2C,YAAY;YAAA;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACb7G,OAAA,CAACrC,UAAU;cAAC6I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBvG,OAAA;gBAAAuG,QAAA,EAAQ;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC1B,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC,IAAIuD,UAAU,CAAC9D,YAAY,CAAC+D,aAAa,CAAC,GAAG,YAAY,GAAG,UAAU;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEP7G,OAAA,CAACtB,KAAK;UAACwJ,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAEoC,EAAE,EAAE;UAAE,CAAE;UAAA3C,QAAA,GAAC,8EAEpC,EAAC9E,QAAQ,CAACI,SAAS,IAAI,gFAAgF;QAAA;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMwE,cAAc,GAAIC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAOhF,WAAW,CAAC,CAAC;MACtB,KAAK,CAAC;QACJ,OAAOuC,WAAW,CAAC,CAAC;MACtB,KAAK,CAAC;QACJ,OAAOM,WAAW,CAAC,CAAC;MACtB,KAAK,CAAC;QACJ,OAAOiC,WAAW,CAAC,CAAC;MACtB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;EAED,oBACEpL,OAAA,CAACvC,GAAG;IAAA8I,QAAA,gBACFvG,OAAA,CAAClC,OAAO;MAAC2C,UAAU,EAAEA,UAAW;MAACqG,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EAC5CrE,KAAK,CAACmG,GAAG,CAAEd,KAAK,iBACfvH,OAAA,CAACjC,IAAI;QAAAwI,QAAA,eACHvG,OAAA,CAAChC,SAAS;UAAAuI,QAAA,EAAEgB;QAAK;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC,GADrBU,KAAK;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEV7G,OAAA,CAACvC,GAAG;MAACqJ,EAAE,EAAE;QAAEoC,EAAE,EAAE,CAAC;QAAElC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EACvB8E,cAAc,CAAC5K,UAAU;IAAC;MAAAiG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAEN7G,OAAA,CAACvC,GAAG;MAACqJ,EAAE,EAAE;QAAEiB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEkB,EAAE,EAAE;MAAE,CAAE;MAAA3C,QAAA,gBACnEvG,OAAA,CAACnC,MAAM;QACL2I,OAAO,EAAC,UAAU;QAClBkB,KAAK,EAAC,WAAW;QACjBC,OAAO,EAAElH,UAAU,KAAK,CAAC,GAAG,MAAMD,QAAQ,CAAC,sBAAsB,CAAC,GAAGmF,UAAW;QAChFkC,SAAS,eAAE7H,OAAA,CAACZ,aAAa;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7Be,QAAQ,EAAEjH,OAAQ;QAAA4F,QAAA,EAEjB9F,UAAU,KAAK,CAAC,GAAG,SAAS,GAAG;MAAU;QAAAiG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAET7G,OAAA,CAACnC,MAAM;QACL2I,OAAO,EAAC,WAAW;QACnBkB,KAAK,EAAC,SAAS;QACfC,OAAO,EAAElH,UAAU,KAAKyB,KAAK,CAACkB,MAAM,GAAG,CAAC,GAAG4C,YAAY,GAAGP,UAAW;QACrE8F,OAAO,EAAE9K,UAAU,KAAKyB,KAAK,CAACkB,MAAM,GAAG,CAAC,gBAAGpD,OAAA,CAACd,QAAQ;UAAAwH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG7G,OAAA,CAACV,gBAAgB;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC/Ee,QAAQ,EAAEjH,OAAO,IAAKF,UAAU,KAAK,CAAC,IAAI,CAACY,YAAc;QAAAkF,QAAA,EAExD5F,OAAO,gBACNX,OAAA,CAACrB,gBAAgB;UAACmJ,IAAI,EAAE;QAAG;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAC5BpG,UAAU,KAAKyB,KAAK,CAACkB,MAAM,GAAG,CAAC,GACjC,OAAO,GAEP;MACD;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtG,EAAA,CAhhCIJ,kBAAkB;EAAA,QACLR,WAAW;AAAA;AAAA6L,EAAA,GADxBrL,kBAAkB;AAkhCxB,eAAeA,kBAAkB;AAAC,IAAAqL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}