{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport '../../styles/reports.css';\nimport { Box, Typography, Paper, Grid, Card, CardContent, CardActions, Button, Chip, Alert, CircularProgress, Divider, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails, Switch, FormControlLabel } from '@mui/material';\nimport { Assessment as AssessmentIcon, BarChart as BarChartIcon, PieChart as PieChartIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon, ArrowBack as ArrowBackIcon, DateRange as DateRangeIcon, Cable as CableIcon, Inventory as InventoryIcon, ExpandMore as ExpandMoreIcon, ShowChart as ShowChartIcon } from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\nimport EmptyState from '../../components/common/EmptyState';\nimport MetricCard from '../../components/common/MetricCard';\nimport ReportSection from '../../components/common/ReportSection';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    cantiereId\n  } = useParams();\n  const {\n    user\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading progress report:', err);\n          return {\n            content: null\n          };\n        });\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video').catch(err => {\n          console.error('Error loading BOQ report:', err);\n          return {\n            content: null\n          };\n        });\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading bobine report:', err);\n          return {\n            content: null\n          };\n        });\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading cavi stato report:', err);\n          return {\n            content: null\n          };\n        });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([progressPromise, boqPromise, bobinePromise, caviStatoPromise]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [{\n    id: 'progress',\n    title: 'Report Avanzamento',\n    description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n    icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 13\n    }, this),\n    color: 'primary',\n    features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n  }, {\n    id: 'boq',\n    title: 'Bill of Quantities',\n    description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n    icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 13\n    }, this),\n    color: 'secondary',\n    features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n  }, {\n    id: 'bobine',\n    title: 'Report Utilizzo Bobine',\n    description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n    icon: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 13\n    }, this),\n    color: 'success',\n    features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n  }, {\n    id: 'bobina-specifica',\n    title: 'Report Bobina Specifica',\n    description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n    icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 13\n    }, this),\n    color: 'info',\n    features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n  }, {\n    id: 'posa-periodo',\n    title: 'Report Posa per Periodo',\n    description: 'Analisi temporale della posa con trend e pattern di lavoro',\n    icon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 13\n    }, this),\n    color: 'warning',\n    features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n  }, {\n    id: 'cavi-stato',\n    title: 'Report Cavi per Stato',\n    description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n    icon: /*#__PURE__*/_jsxDEV(BarChartIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 13\n    }, this),\n    color: 'error',\n    features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n  }];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReportSelect = reportType => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderReportContent = () => {\n    if (!reportData) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title, \" - \", reportData.nome_cantiere]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'pdf'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"primary\",\n            children: \"PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'excel'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"success\",\n            children: \"Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 26\n            }, this),\n            onClick: () => setReportData(null),\n            variant: \"outlined\",\n            size: \"small\",\n            children: \"Nuovo Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), dialogType === 'progress' && renderProgressReport(reportData), dialogType === 'boq' && renderBoqReport(reportData), dialogType === 'bobine' && renderBobineReport(reportData), dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData), dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData), dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this);\n  };\n  const renderProgressReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: '#2c3e50'\n        },\n        children: \"\\uD83D\\uDCCA Report Avanzamento Lavori\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Totali\",\n          value: data.metri_totali,\n          unit: \"m\",\n          subtitle: \"Lunghezza complessiva del progetto\",\n          gradient: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n          size: \"medium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Posati\",\n          value: data.metri_posati,\n          unit: \"m\",\n          subtitle: `${data.percentuale_avanzamento}% completato`,\n          gradient: \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n          progress: data.percentuale_avanzamento,\n          trend: data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down',\n          trendValue: `${data.percentuale_avanzamento}%`,\n          size: \"medium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Rimanenti\",\n          value: data.metri_da_posare,\n          unit: \"m\",\n          subtitle: `${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`,\n          gradient: \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n          size: \"medium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Media/Giorno\",\n          value: data.media_giornaliera || 0,\n          unit: \"m\",\n          subtitle: data.giorni_stimati ? `${data.giorni_stimati} giorni lavorativi rimasti` : data.media_giornaliera > 0 ? 'Calcolo in corso' : 'Nessuna posa recente',\n          gradient: \"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\",\n          size: \"medium\",\n          tooltip: data.giorni_lavorativi_effettivi ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.` : 'Media giornaliera basata sui giorni di lavoro effettivo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(ProgressChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            border: '1px solid #e0e0e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n                sx: {\n                  color: '#3498db',\n                  mr: 1,\n                  fontSize: 28\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#2c3e50'\n                },\n                children: \"Stato Cavi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    bgcolor: '#f8f9fa',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: '#2c3e50',\n                      mb: 1\n                    },\n                    children: data.totale_cavi\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Cavi Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    bgcolor: '#e8f5e8',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: '#27ae60',\n                      mb: 1\n                    },\n                    children: data.cavi_posati\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: [\"Cavi Posati (\", data.percentuale_cavi, \"%)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Progresso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: [data.percentuale_cavi, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: `${data.percentuale_cavi}%`,\n                    height: '100%',\n                    bgcolor: '#27ae60',\n                    transition: 'width 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            border: '1px solid #e0e0e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n                sx: {\n                  color: '#e74c3c',\n                  mr: 1,\n                  fontSize: 28\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#2c3e50'\n                },\n                children: \"Timeline Progetto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#e74c3c',\n                  mb: 1\n                },\n                children: [data.media_giornaliera || 0, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666',\n                  mb: 1\n                },\n                children: \"Media Giornaliera\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this), data.giorni_lavorativi_effettivi && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: '#999',\n                  fontSize: '0.75rem'\n                },\n                children: [\"Basata su \", data.giorni_lavorativi_effettivi, \" giorni di lavoro effettivo\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this), data.giorni_stimati ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                p: 2,\n                bgcolor: '#fff3cd',\n                borderRadius: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#856404',\n                  mb: 0.5\n                },\n                children: data.data_completamento\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#856404'\n                },\n                children: [\"Completamento previsto in \", data.giorni_stimati, \" giorni\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                p: 2,\n                bgcolor: '#f8f9fa',\n                borderRadius: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: data.media_giornaliera > 0 ? 'Timeline in calcolo...' : 'Necessaria attività di posa per calcolare la timeline'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 7\n    }, this), data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        border: '1px solid #e0e0e0'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(DateRangeIcon, {\n            sx: {\n              color: '#9b59b6',\n              mr: 1,\n              fontSize: 28\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600,\n              color: '#2c3e50'\n            },\n            children: \"\\uD83D\\uDCC8 Attivit\\xE0 Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: data.posa_recente.slice(0, 5).map((posa, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                border: '1px solid #e0e0e0',\n                borderRadius: 2,\n                bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',\n                transition: 'all 0.2s',\n                '&:hover': {\n                  bgcolor: '#f5f5f5',\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666',\n                  mb: 1\n                },\n                children: posa.data\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#2c3e50'\n                },\n                children: [posa.metri, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 21\n              }, this), index === 0 && /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Pi\\xF9 recente\",\n                size: \"small\",\n                sx: {\n                  mt: 1,\n                  bgcolor: '#3498db',\n                  color: 'white',\n                  fontSize: '0.7rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 13\n        }, this), data.posa_recente.length > 5 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Accordion, {\n            children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n              expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 49\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#3498db'\n                },\n                children: [\"Mostra tutti i \", data.posa_recente.length, \" record\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n              children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n                data: data.posa_recente.map(posa => ({\n                  data: posa.data,\n                  metri: `${posa.metri}m`\n                })),\n                columns: [{\n                  field: 'data',\n                  headerName: 'Data',\n                  width: 200\n                }, {\n                  field: 'metri',\n                  headerName: 'Metri Posati',\n                  width: 150,\n                  align: 'right'\n                }],\n                pagination: true,\n                pageSize: 10\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 585,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 380,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n        sx: {\n          color: '#8e44ad',\n          mr: 1,\n          fontSize: 28\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 676,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: '#2c3e50'\n        },\n        children: \"\\uD83D\\uDCCB Bill of Quantities - Distinta Materiali\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 667,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(BoqChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 685,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 684,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3,\n        border: '1px solid #e0e0e0'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n            sx: {\n              color: '#e67e22',\n              mr: 1,\n              fontSize: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600,\n              color: '#2c3e50'\n            },\n            children: \"Cavi per Tipologia\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 692,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n          data: data.cavi_per_tipo || [],\n          columns: [{\n            field: 'tipologia',\n            headerName: 'Tipologia',\n            width: 150\n          }, {\n            field: 'sezione',\n            headerName: 'Sezione',\n            width: 100\n          }, {\n            field: 'num_cavi',\n            headerName: 'Cavi',\n            width: 80,\n            align: 'right',\n            dataType: 'number'\n          }, {\n            field: 'metri_teorici',\n            headerName: 'Metri Teorici',\n            width: 120,\n            align: 'right',\n            dataType: 'number',\n            renderCell: row => `${row.metri_teorici}m`\n          }, {\n            field: 'metri_reali',\n            headerName: 'Metri Reali',\n            width: 120,\n            align: 'right',\n            dataType: 'number',\n            renderCell: row => `${row.metri_reali}m`\n          }, {\n            field: 'metri_da_posare',\n            headerName: 'Da Posare',\n            width: 120,\n            align: 'right',\n            dataType: 'number',\n            renderCell: row => `${row.metri_da_posare}m`\n          }],\n          pageSize: 10\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 691,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 690,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        border: '1px solid #e0e0e0'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(InventoryIcon, {\n            sx: {\n              color: '#16a085',\n              mr: 1,\n              fontSize: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600,\n              color: '#2c3e50'\n            },\n            children: \"Bobine Disponibili\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n          data: data.bobine_per_tipo || [],\n          columns: [{\n            field: 'tipologia',\n            headerName: 'Tipologia',\n            width: 150\n          }, {\n            field: 'sezione',\n            headerName: 'Sezione',\n            width: 100\n          }, {\n            field: 'num_bobine',\n            headerName: 'Bobine',\n            width: 100,\n            align: 'right',\n            dataType: 'number'\n          }, {\n            field: 'metri_disponibili',\n            headerName: 'Metri Disponibili',\n            width: 150,\n            align: 'right',\n            dataType: 'number',\n            renderCell: row => `${row.metri_disponibili}m`\n          }],\n          pageSize: 10\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 718,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 717,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 665,\n    columnNumber: 5\n  }, this);\n  const renderBobineReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 747,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 754,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 745,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 744,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(BobineChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 765,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 764,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        sx: {\n          fontWeight: 500,\n          mb: 2,\n          color: '#2c3e50'\n        },\n        children: \"Utilizzo Bobine del Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 771,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.bobine || [],\n        columns: [{\n          field: 'id_bobina',\n          headerName: 'ID Bobina',\n          width: 120\n        }, {\n          field: 'tipologia',\n          headerName: 'Tipologia',\n          width: 150\n        }, {\n          field: 'sezione',\n          headerName: 'Sezione',\n          width: 100\n        }, {\n          field: 'stato',\n          headerName: 'Stato',\n          width: 120,\n          renderCell: row => /*#__PURE__*/_jsxDEV(Chip, {\n            label: row.stato,\n            color: row.stato === 'DISPONIBILE' ? 'primary' : 'default',\n            size: \"small\",\n            sx: {\n              bgcolor: row.stato === 'DISPONIBILE' ? '#3498db' : '#85929e',\n              color: 'white'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 782,\n            columnNumber: 17\n          }, this)\n        }, {\n          field: 'metri_totali',\n          headerName: 'Metri Totali',\n          width: 120,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri_totali}m`\n        }, {\n          field: 'metri_residui',\n          headerName: 'Metri Residui',\n          width: 120,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri_residui}m`\n        }, {\n          field: 'metri_utilizzati',\n          headerName: 'Metri Utilizzati',\n          width: 140,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri_utilizzati}m`\n        }, {\n          field: 'percentuale_utilizzo',\n          headerName: 'Utilizzo',\n          width: 100,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.percentuale_utilizzo}%`\n        }],\n        pageSize: 10\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 774,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 770,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 742,\n    columnNumber: 5\n  }, this);\n  const renderBobinaSpecificaReport = data => {\n    var _data$bobina;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: 'info.main',\n          mb: 3\n        },\n        children: [\"Report Bobina Specifica - \", (_data$bobina = data.bobina) === null || _data$bobina === void 0 ? void 0 : _data$bobina.id_bobina]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 811,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2,\n                fontWeight: 600\n              },\n              children: \"Dettagli Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 13\n            }, this), data.bobina && /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"ID Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 825,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: data.bobina.id_bobina\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 826,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 829,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: data.bobina.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Sezione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: data.bobina.sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 834,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 837,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: data.bobina.stato,\n                  color: data.bobina.stato === 'DISPONIBILE' ? 'success' : 'warning',\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 838,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 836,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2,\n                fontWeight: 600\n              },\n              children: \"Metriche Utilizzo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 13\n            }, this), data.bobina && /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Metri Totali:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 858,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: [data.bobina.metri_totali, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 859,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Metri Utilizzati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 862,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 600,\n                    color: 'success.main'\n                  },\n                  children: [data.bobina.metri_utilizzati, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 863,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Metri Residui:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 868,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 600,\n                    color: 'warning.main'\n                  },\n                  children: [data.bobina.metri_residui, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 869,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 867,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Percentuale Utilizzo:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: [data.bobina.percentuale_utilizzo, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 873,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 851,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 850,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2,\n                fontWeight: 600\n              },\n              children: [\"Cavi Associati (\", data.totale_cavi, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 885,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n              data: data.cavi_associati || [],\n              columns: [{\n                field: 'id_cavo',\n                headerName: 'ID Cavo',\n                width: 120\n              }, {\n                field: 'sistema',\n                headerName: 'Sistema',\n                width: 120\n              }, {\n                field: 'utility',\n                headerName: 'Utility',\n                width: 120\n              }, {\n                field: 'tipologia',\n                headerName: 'Tipologia',\n                width: 150\n              }, {\n                field: 'metri_teorici',\n                headerName: 'Metri Teorici',\n                width: 120,\n                align: 'right',\n                dataType: 'number',\n                renderCell: row => `${row.metri_teorici}m`\n              }, {\n                field: 'metri_reali',\n                headerName: 'Metri Reali',\n                width: 120,\n                align: 'right',\n                dataType: 'number',\n                renderCell: row => `${row.metri_reali}m`\n              }, {\n                field: 'stato',\n                headerName: 'Stato',\n                width: 120,\n                renderCell: row => /*#__PURE__*/_jsxDEV(Chip, {\n                  label: row.stato,\n                  color: row.stato === 'POSATO' ? 'success' : 'warning',\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 901,\n                  columnNumber: 21\n                }, this)\n              }],\n              pageSize: 10\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 883,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 815,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 809,\n      columnNumber: 5\n    }, this);\n  };\n  const renderPosaPeriodoReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: 'warning.main'\n        },\n        children: \"Report Posa per Periodo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 921,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 926,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 924,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 920,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'warning.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.totale_metri_periodo, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 945,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Metri Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 948,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: [data.data_inizio, \" - \", data.data_fine]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 949,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 944,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 943,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'info.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: data.giorni_attivi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 954,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Giorni Attivi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 953,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 952,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'success.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.media_giornaliera, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 962,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Giorno\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 961,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 960,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'primary.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [Math.round(data.totale_metri_periodo / data.giorni_attivi * 7), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 970,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Settimana\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 973,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 969,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 968,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 942,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(TimelineChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 981,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 980,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2,\n          fontWeight: 600\n        },\n        children: \"Dettaglio Posa Giornaliera\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 987,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.posa_giornaliera || [],\n        columns: [{\n          field: 'data',\n          headerName: 'Data',\n          width: 200\n        }, {\n          field: 'metri',\n          headerName: 'Metri Posati',\n          width: 150,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri}m`\n        }],\n        pageSize: 10\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 990,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 986,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 918,\n    columnNumber: 5\n  }, this);\n  const renderCaviStatoReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1009,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1017,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1016,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1007,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1006,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        sx: {\n          fontWeight: 500,\n          mb: 2,\n          color: '#2c3e50'\n        },\n        children: \"Distribuzione Stati Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1027,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          border: '1px solid #e0e0e0',\n          borderRadius: 1,\n          overflow: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          style: {\n            width: '100%',\n            borderCollapse: 'collapse'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              style: {\n                backgroundColor: '#f8f9fa'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '8px 12px',\n                  textAlign: 'left',\n                  fontSize: '12px',\n                  fontWeight: 600,\n                  color: '#2c3e50',\n                  borderBottom: '1px solid #e0e0e0',\n                  width: '25%'\n                },\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1038,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '8px 12px',\n                  textAlign: 'center',\n                  fontSize: '12px',\n                  fontWeight: 600,\n                  color: '#2c3e50',\n                  borderBottom: '1px solid #e0e0e0',\n                  width: '25%'\n                },\n                children: \"Numero Cavi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '8px 12px',\n                  textAlign: 'center',\n                  fontSize: '12px',\n                  fontWeight: 600,\n                  color: '#2c3e50',\n                  borderBottom: '1px solid #e0e0e0',\n                  width: '25%'\n                },\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1056,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '8px 12px',\n                  textAlign: 'center',\n                  fontSize: '12px',\n                  fontWeight: 600,\n                  color: '#2c3e50',\n                  borderBottom: '1px solid #e0e0e0',\n                  width: '25%'\n                },\n                children: \"Distribuzione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1036,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: (data.cavi_per_stato || []).map((item, index) => {\n              const totalCavi = (data.cavi_per_stato || []).reduce((sum, s) => sum + s.num_cavi, 0);\n              const percentage = totalCavi > 0 ? Math.round(item.num_cavi / totalCavi * 100) : 0;\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '12px',\n                    fontSize: '13px',\n                    borderBottom: index < data.cavi_per_stato.length - 1 ? '1px solid #f0f0f0' : 'none'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: item.stato,\n                    size: \"small\",\n                    sx: {\n                      bgcolor: item.stato === 'Installato' ? '#3498db' : '#85929e',\n                      color: 'white'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1087,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '12px',\n                    fontSize: '13px',\n                    textAlign: 'center',\n                    fontWeight: 600,\n                    borderBottom: index < data.cavi_per_stato.length - 1 ? '1px solid #f0f0f0' : 'none'\n                  },\n                  children: item.num_cavi\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1096,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '12px',\n                    fontSize: '13px',\n                    textAlign: 'center',\n                    fontWeight: 600,\n                    borderBottom: index < data.cavi_per_stato.length - 1 ? '1px solid #f0f0f0' : 'none'\n                  },\n                  children: [item.metri_teorici, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1103,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '12px',\n                    fontSize: '13px',\n                    textAlign: 'center',\n                    borderBottom: index < data.cavi_per_stato.length - 1 ? '1px solid #f0f0f0' : 'none'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '60px',\n                        height: '6px',\n                        bgcolor: '#e0e0e0',\n                        borderRadius: '3px',\n                        position: 'relative'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: `${percentage}%`,\n                          height: '100%',\n                          bgcolor: item.stato === 'Installato' ? '#3498db' : '#85929e',\n                          borderRadius: '3px'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1124,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1117,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontWeight: 600\n                      },\n                      children: [percentage, \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1131,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1116,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1110,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1081,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1076,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1035,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1030,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1026,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        sx: {\n          fontWeight: 500,\n          mb: 2,\n          color: '#2c3e50'\n        },\n        children: \"Cavi per Stato di Installazione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.cavi_per_stato || [],\n        columns: [{\n          field: 'stato',\n          headerName: 'Stato',\n          width: 150,\n          renderCell: row => /*#__PURE__*/_jsxDEV(Chip, {\n            label: row.stato,\n            size: \"small\",\n            sx: {\n              bgcolor: row.stato === 'Installato' ? '#3498db' : '#85929e',\n              color: 'white'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1155,\n            columnNumber: 17\n          }, this)\n        }, {\n          field: 'num_cavi',\n          headerName: 'Numero Cavi',\n          width: 120,\n          align: 'right',\n          dataType: 'number'\n        }, {\n          field: 'metri_teorici',\n          headerName: 'Metri Teorici',\n          width: 150,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri_teorici}m`\n        }, {\n          field: 'metri_reali',\n          headerName: 'Metri Reali',\n          width: 150,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri_reali}m`\n        }],\n        pagination: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1146,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1004,\n    columnNumber: 5\n  }, this);\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1184,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1199,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1200,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1193,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1191,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1190,\n          columnNumber: 11\n        }, this), dialogType === 'bobina-specifica' && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Bobina\",\n            value: formData.id_bobina,\n            onChange: e => setFormData({\n              ...formData,\n              id_bobina: e.target.value\n            }),\n            placeholder: \"Es: 1, 2, A, B...\",\n            helperText: \"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1207,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1206,\n          columnNumber: 13\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1221,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1231,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1230,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1189,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1250,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1250,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1244,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1178,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"report-main-container report-fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1262,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1261,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1268,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1267,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600,\n            color: '#2c3e50',\n            mb: 2,\n            textAlign: 'center'\n          },\n          children: \"\\uD83C\\uDFAF Seleziona il tipo di report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: `report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`,\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('progress'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#3498db',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1294,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Avanzamento\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1295,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Panoramica lavori\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1298,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1293,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1282,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1281,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('boq'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#8e44ad',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1318,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Bill of Quantities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1319,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Distinta materiali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1322,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1317,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1307,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'bobine' ? '2px solid #16a085' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'bobine' ? '#f0fff4' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('bobine'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(InventoryIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#16a085',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1342,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Report Bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1343,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Utilizzo bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1346,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1341,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1331,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1330,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'cavi-stato' ? '2px solid #e74c3c' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'cavi-stato' ? '#fff5f5' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('cavi-stato'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(BarChartIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#e74c3c',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1366,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Cavi per Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1367,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Stati installazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1370,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1365,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1355,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1354,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'bobina-specifica' ? '2px solid #f39c12' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'bobina-specifica' ? '#fffbf0' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('bobina-specifica'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#f39c12',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1390,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Bobina Specifica\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1391,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Dettaglio bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1394,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1389,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1379,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'posa-periodo' ? '2px solid #9b59b6' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'posa-periodo' ? '#f8f4ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('posa-periodo'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#9b59b6',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1414,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Posa per Periodo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1415,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Analisi temporale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1418,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1413,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1403,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1402,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minHeight: '400px'\n        },\n        children: [selectedReportType === 'progress' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.progress ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1436,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1435,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1446,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1445,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1434,\n              columnNumber: 19\n            }, this), renderProgressReport(reportsData.progress)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1433,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"progress\",\n            title: \"Caricamento Report Avanzamento...\",\n            description: \"Stiamo elaborando i dati dell'avanzamento dei lavori\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1458,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"progress\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare il report di avanzamento. Verifica la connessione e riprova.\",\n            onRetry: () => {\n              setLoading(true);\n              reportService.getProgressReport(cantiereId, 'video').then(data => {\n                setReportsData(prev => ({\n                  ...prev,\n                  progress: data.content\n                }));\n              }).catch(err => {\n                console.error('Error retrying progress report:', err);\n              }).finally(() => {\n                setLoading(false);\n              });\n            },\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1465,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1431,\n          columnNumber: 13\n        }, this), selectedReportType === 'boq' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.boq ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1499,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1498,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1509,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1508,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1497,\n              columnNumber: 19\n            }, this), renderBoqReport(reportsData.boq)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1496,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"boq\",\n            title: \"Caricamento Bill of Quantities...\",\n            description: \"Stiamo elaborando la distinta materiali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1521,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"boq\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare la distinta materiali. Verifica la connessione e riprova.\",\n            onRetry: () => {\n              setLoading(true);\n              reportService.getBillOfQuantities(cantiereId, 'video').then(data => {\n                setReportsData(prev => ({\n                  ...prev,\n                  boq: data.content\n                }));\n              }).catch(err => {\n                console.error('Error retrying BOQ report:', err);\n              }).finally(() => {\n                setLoading(false);\n              });\n            },\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1528,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1494,\n          columnNumber: 13\n        }, this), selectedReportType === 'bobine' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.bobine ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1562,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('bobine', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1561,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1572,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('bobine', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1571,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1560,\n              columnNumber: 19\n            }, this), renderBobineReport(reportsData.bobine)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1559,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              my: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1585,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1586,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1584,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              my: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1590,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1595,\n                columnNumber: 32\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getBobineReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    bobine: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying bobine report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1593,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1589,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1557,\n          columnNumber: 13\n        }, this), selectedReportType === 'cavi-stato' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.caviStato ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1627,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('cavi-stato', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1626,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1637,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('cavi-stato', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1636,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1625,\n              columnNumber: 19\n            }, this), renderCaviStatoReport(reportsData.caviStato)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1624,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              my: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1650,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1651,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1649,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              my: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1655,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1660,\n                columnNumber: 32\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getCaviStatoReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    caviStato: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying cavi stato report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1658,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1654,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1622,\n          columnNumber: 13\n        }, this), selectedReportType === 'bobina-specifica' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.bobinaSpecifica ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1692,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('bobina-specifica', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1691,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1702,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('bobina-specifica', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1701,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1690,\n              columnNumber: 19\n            }, this), renderBobinaSpecificaReport(reportsData.bobinaSpecifica)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1689,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"action-required\",\n            reportType: \"bobina-specifica\",\n            title: \"Seleziona una Bobina\",\n            description: \"Inserisci l'ID di una bobina per generare il report dettagliato con tutti i cavi associati e le metriche di utilizzo.\",\n            actionLabel: \"Seleziona Bobina\",\n            onAction: () => {\n              setDialogType('bobina-specifica');\n              setOpenDialog(true);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1714,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1687,\n          columnNumber: 13\n        }, this), selectedReportType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.posaPeriodo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1736,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1735,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1746,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1745,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1734,\n              columnNumber: 19\n            }, this), renderPosaPeriodoReport(reportsData.posaPeriodo)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1733,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"action-required\",\n            reportType: \"posa-periodo\",\n            title: \"Seleziona un Periodo\",\n            description: \"Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttivit\\xE0 del team.\",\n            actionLabel: \"Seleziona Periodo\",\n            onAction: () => {\n              setDialogType('posa-periodo');\n              // Set default date range (last month to today)\n              const today = new Date();\n              const lastMonth = new Date();\n              lastMonth.setMonth(today.getMonth() - 1);\n              setFormData({\n                ...formData,\n                data_inizio: lastMonth.toISOString().split('T')[0],\n                data_fine: today.toISOString().split('T')[0]\n              });\n              setOpenDialog(true);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1758,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1731,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1428,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1273,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1259,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageNew, \"A7Tak7ZNqlgfHityJ1Jz8L67TXo=\", false, function () {\n  return [useNavigate, useParams, useAuth];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Switch", "FormControlLabel", "Assessment", "AssessmentIcon", "<PERSON><PERSON><PERSON>", "BarChartIcon", "<PERSON><PERSON><PERSON>", "PieChartIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "ArrowBack", "ArrowBackIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "ShowChart", "ShowChartIcon", "useNavigate", "useParams", "useAuth", "AdminHomeButton", "reportService", "FilterableTable", "EmptyState", "MetricCard", "ReportSection", "ProgressChart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TimelineChart", "Cavi<PERSON>tato<PERSON>hart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s", "navigate", "cantiereId", "user", "loading", "setLoading", "error", "setError", "reportData", "setReportData", "selectedReport", "setSelectedReport", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedReportType", "setSelectedReportType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportsData", "setReportsData", "progress", "boq", "bobine", "caviStato", "bobinaSpecifica", "posaPeriodo", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAllReports", "progressPromise", "getProgressReport", "catch", "err", "console", "content", "boq<PERSON><PERSON><PERSON>", "getBillOfQuantities", "bob<PERSON><PERSON><PERSON><PERSON>", "getBobineReport", "caviStatoPromise", "getCaviStatoReport", "progressData", "boqData", "bobine<PERSON><PERSON>", "caviStatoData", "Promise", "all", "reportTypes", "id", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "features", "generateReportWithFormat", "reportType", "format", "response", "getBobinaReport", "getPosaPerPeriodoReport", "Error", "prev", "file_url", "window", "open", "detail", "message", "handleReportSelect", "today", "Date", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "handleGenerateReport", "handleCloseDialog", "renderReportContent", "sx", "p", "mt", "children", "display", "justifyContent", "alignItems", "mb", "variant", "nome_cantiere", "gap", "startIcon", "onClick", "size", "renderProgressReport", "renderBoqReport", "renderBobineReport", "renderBobinaSpecificaReport", "renderPosaPeriodoReport", "renderCaviStatoReport", "data", "bgcolor", "borderRadius", "border", "fontWeight", "control", "checked", "onChange", "e", "target", "label", "mr", "container", "spacing", "item", "xs", "sm", "md", "value", "metri_totali", "unit", "subtitle", "gradient", "metri_posati", "percentuale_avanzamento", "trend", "trendValue", "metri_da_posare", "toFixed", "media_giornaliera", "giorni_stimati", "tooltip", "giorni_lavorativi_effettivi", "height", "fontSize", "textAlign", "totale_cavi", "cavi_posati", "percentuale_cavi", "width", "overflow", "transition", "data_completamento", "posa_recente", "length", "slice", "map", "posa", "index", "transform", "boxShadow", "metri", "expandIcon", "columns", "field", "headerName", "align", "pagination", "pageSize", "cavi_per_tipo", "dataType", "renderCell", "row", "metri_te<PERSON>ci", "metri_reali", "bobine_per_tipo", "metri_disponibili", "stato", "metri_residui", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "percentuale_utilizzo", "_data$bobina", "bobina", "tipologia", "sezione", "cavi_associati", "totale_metri_periodo", "giorni_attivi", "Math", "round", "posa_giornal<PERSON>", "style", "borderCollapse", "backgroundColor", "padding", "borderBottom", "cavi_per_stato", "totalCavi", "reduce", "sum", "s", "num_cavi", "percentage", "position", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "placeholder", "helperText", "type", "InputLabelProps", "shrink", "disabled", "className", "my", "cursor", "flexDirection", "minHeight", "onRetry", "then", "finally", "ml", "actionLabel", "onAction", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport '../../styles/reports.css';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n  IconButton,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n  <PERSON><PERSON>hart as BarChartIcon,\n  PieChart as PieChartIcon,\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n  ArrowBack as ArrowBackIcon,\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon,\n  Show<PERSON>hart as ShowChartIcon\n} from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\nimport EmptyState from '../../components/common/EmptyState';\nimport MetricCard from '../../components/common/MetricCard';\nimport ReportSection from '../../components/common/ReportSection';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\n\nconst ReportCaviPageNew = () => {\n  const navigate = useNavigate();\n  const { cantiereId } = useParams();\n  const { user } = useAuth();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading progress report:', err);\n            return { content: null };\n          });\n\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading BOQ report:', err);\n            return { content: null };\n          });\n\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading bobine report:', err);\n            return { content: null };\n          });\n\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading cavi stato report:', err);\n            return { content: null };\n          });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          bobinePromise,\n          caviStatoPromise\n        ]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [\n    {\n      id: 'progress',\n      title: 'Report Avanzamento',\n      description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n      icon: <AssessmentIcon />,\n      color: 'primary',\n      features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n    },\n    {\n      id: 'boq',\n      title: 'Bill of Quantities',\n      description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n      icon: <ListIcon />,\n      color: 'secondary',\n      features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n    },\n    {\n      id: 'bobine',\n      title: 'Report Utilizzo Bobine',\n      description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n      icon: <InventoryIcon />,\n      color: 'success',\n      features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n    },\n    {\n      id: 'bobina-specifica',\n      title: 'Report Bobina Specifica',\n      description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n      icon: <CableIcon />,\n      color: 'info',\n      features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n    },\n    {\n      id: 'posa-periodo',\n      title: 'Report Posa per Periodo',\n      description: 'Analisi temporale della posa con trend e pattern di lavoro',\n      icon: <TimelineIcon />,\n      color: 'warning',\n      features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n    },\n    {\n      id: 'cavi-stato',\n      title: 'Report Cavi per Stato',\n      description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n      icon: <BarChartIcon />,\n      color: 'error',\n      features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n    }\n  ];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReportSelect = (reportType) => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n  const renderReportContent = () => {\n    if (!reportData) return null;\n\n    return (\n      <Paper sx={{ p: 3, mt: 3 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\">\n            {selectedReport?.title} - {reportData.nome_cantiere}\n          </Typography>\n          <Box sx={{ display: 'flex', gap: 1 }}>\n            {/* Export buttons */}\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'pdf')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"primary\"\n            >\n              PDF\n            </Button>\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'excel')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"success\"\n            >\n              Excel\n            </Button>\n            <Button\n              startIcon={<RefreshIcon />}\n              onClick={() => setReportData(null)}\n              variant=\"outlined\"\n              size=\"small\"\n            >\n              Nuovo Report\n            </Button>\n          </Box>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Renderizza il contenuto specifico del report */}\n        {dialogType === 'progress' && renderProgressReport(reportData)}\n        {dialogType === 'boq' && renderBoqReport(reportData)}\n        {dialogType === 'bobine' && renderBobineReport(reportData)}\n        {dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData)}\n        {dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData)}\n        {dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)}\n      </Paper>\n    );\n  };\n\n  const renderProgressReport = (data) => (\n    <Box>\n      {/* Header con controlli migliorato */}\n      <Box sx={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n          📊 Report Avanzamento Lavori\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Metriche Principali - Cards Moderne con MetricCard */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Totali\"\n            value={data.metri_totali}\n            unit=\"m\"\n            subtitle=\"Lunghezza complessiva del progetto\"\n            gradient=\"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\"\n            size=\"medium\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Posati\"\n            value={data.metri_posati}\n            unit=\"m\"\n            subtitle={`${data.percentuale_avanzamento}% completato`}\n            gradient=\"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\"\n            progress={data.percentuale_avanzamento}\n            trend={data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down'}\n            trendValue={`${data.percentuale_avanzamento}%`}\n            size=\"medium\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Rimanenti\"\n            value={data.metri_da_posare}\n            unit=\"m\"\n            subtitle={`${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`}\n            gradient=\"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\"\n            size=\"medium\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Media/Giorno\"\n            value={data.media_giornaliera || 0}\n            unit=\"m\"\n            subtitle={\n              data.giorni_stimati\n                ? `${data.giorni_stimati} giorni lavorativi rimasti`\n                : (data.media_giornaliera > 0\n                    ? 'Calcolo in corso'\n                    : 'Nessuna posa recente')\n            }\n            gradient=\"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\"\n            size=\"medium\"\n            tooltip={\n              data.giorni_lavorativi_effettivi\n                ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.`\n                : 'Media giornaliera basata sui giorni di lavoro effettivo'\n            }\n          />\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <ProgressChart data={data} />\n        </Box>\n      )}\n\n      {/* Dettagli Performance - Cards Informative */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <CableIcon sx={{ color: '#3498db', mr: 1, fontSize: 28 }} />\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Stato Cavi\n                </Typography>\n              </Box>\n              <Grid container spacing={2}>\n                <Grid item xs={6}>\n                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                      {data.totale_cavi}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      Cavi Totali\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={6}>\n                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>\n                      {data.cavi_posati}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      Cavi Posati ({data.percentuale_cavi}%)\n                    </Typography>\n                  </Box>\n                </Grid>\n              </Grid>\n              <Box sx={{ mt: 2 }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body2\">Progresso</Typography>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                    {data.percentuale_cavi}%\n                  </Typography>\n                </Box>\n                <Box sx={{\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                }}>\n                  <Box sx={{\n                    width: `${data.percentuale_cavi}%`,\n                    height: '100%',\n                    bgcolor: '#27ae60',\n                    transition: 'width 0.3s ease'\n                  }} />\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <TimelineIcon sx={{ color: '#e74c3c', mr: 1, fontSize: 28 }} />\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Timeline Progetto\n                </Typography>\n              </Box>\n              <Box sx={{ textAlign: 'center', mb: 2 }}>\n                <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#e74c3c', mb: 1 }}>\n                  {data.media_giornaliera || 0}m\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: '#666', mb: 1 }}>\n                  Media Giornaliera\n                </Typography>\n                {data.giorni_lavorativi_effettivi && (\n                  <Typography variant=\"caption\" sx={{ color: '#999', fontSize: '0.75rem' }}>\n                    Basata su {data.giorni_lavorativi_effettivi} giorni di lavoro effettivo\n                  </Typography>\n                )}\n              </Box>\n              {data.giorni_stimati ? (\n                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#fff3cd', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#856404', mb: 0.5 }}>\n                    {data.data_completamento}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#856404' }}>\n                    Completamento previsto in {data.giorni_stimati} giorni\n                  </Typography>\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>\n                  <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                    {data.media_giornaliera > 0 ? 'Timeline in calcolo...' : 'Necessaria attività di posa per calcolare la timeline'}\n                  </Typography>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Attività Recente - Design Migliorato */}\n      {data.posa_recente && data.posa_recente.length > 0 && (\n        <Card sx={{ border: '1px solid #e0e0e0' }}>\n          <CardContent sx={{ p: 3 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n              <DateRangeIcon sx={{ color: '#9b59b6', mr: 1, fontSize: 28 }} />\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                📈 Attività Recente\n              </Typography>\n            </Box>\n\n            {/* Mostra solo gli ultimi 5 record in formato card per mobile-friendly */}\n            <Grid container spacing={2}>\n              {data.posa_recente.slice(0, 5).map((posa, index) => (\n                <Grid item xs={12} sm={6} md={4} key={index}>\n                  <Box sx={{\n                    p: 2,\n                    border: '1px solid #e0e0e0',\n                    borderRadius: 2,\n                    bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',\n                    transition: 'all 0.2s',\n                    '&:hover': {\n                      bgcolor: '#f5f5f5',\n                      transform: 'translateY(-2px)',\n                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                    }\n                  }}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 1 }}>\n                      {posa.data}\n                    </Typography>\n                    <Typography variant=\"h5\" sx={{ fontWeight: 700, color: '#2c3e50' }}>\n                      {posa.metri}m\n                    </Typography>\n                    {index === 0 && (\n                      <Chip\n                        label=\"Più recente\"\n                        size=\"small\"\n                        sx={{\n                          mt: 1,\n                          bgcolor: '#3498db',\n                          color: 'white',\n                          fontSize: '0.7rem'\n                        }}\n                      />\n                    )}\n                  </Box>\n                </Grid>\n              ))}\n            </Grid>\n\n            {/* Link per vedere tutti i dati se ce ne sono di più */}\n            {data.posa_recente.length > 5 && (\n              <Box sx={{ mt: 3, textAlign: 'center' }}>\n                <Accordion>\n                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                    <Typography variant=\"body2\" sx={{ color: '#3498db' }}>\n                      Mostra tutti i {data.posa_recente.length} record\n                    </Typography>\n                  </AccordionSummary>\n                  <AccordionDetails>\n                    <FilterableTable\n                      data={data.posa_recente.map(posa => ({\n                        data: posa.data,\n                        metri: `${posa.metri}m`\n                      }))}\n                      columns={[\n                        { field: 'data', headerName: 'Data', width: 200 },\n                        { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }\n                      ]}\n                      pagination={true}\n                      pageSize={10}\n                    />\n                  </AccordionDetails>\n                </Accordion>\n              </Box>\n            )}\n          </CardContent>\n        </Card>\n      )}\n    </Box>\n  );\n\n  const renderBoqReport = (data) => (\n    <Box>\n      {/* Header migliorato */}\n      <Box sx={{\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      }}>\n        <ListIcon sx={{ color: '#8e44ad', mr: 1, fontSize: 28 }} />\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n          📋 Bill of Quantities - Distinta Materiali\n        </Typography>\n      </Box>\n\n      {/* Grafici BOQ se disponibili */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <BoqChart data={data} />\n        </Box>\n      )}\n\n      {/* Cavi per Tipologia - Design migliorato */}\n      <Card sx={{ mb: 3, border: '1px solid #e0e0e0' }}>\n        <CardContent sx={{ p: 3 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n            <CableIcon sx={{ color: '#e67e22', mr: 1, fontSize: 24 }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n              Cavi per Tipologia\n            </Typography>\n          </Box>\n          <FilterableTable\n            data={data.cavi_per_tipo || []}\n            columns={[\n              { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n              { field: 'sezione', headerName: 'Sezione', width: 100 },\n              { field: 'num_cavi', headerName: 'Cavi', width: 80, align: 'right', dataType: 'number' },\n              { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',\n                renderCell: (row) => `${row.metri_teorici}m` },\n              { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',\n                renderCell: (row) => `${row.metri_reali}m` },\n              { field: 'metri_da_posare', headerName: 'Da Posare', width: 120, align: 'right', dataType: 'number',\n                renderCell: (row) => `${row.metri_da_posare}m` }\n            ]}\n            pageSize={10}\n          />\n        </CardContent>\n      </Card>\n\n      {/* Bobine Disponibili - Design migliorato */}\n      <Card sx={{ border: '1px solid #e0e0e0' }}>\n        <CardContent sx={{ p: 3 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n            <InventoryIcon sx={{ color: '#16a085', mr: 1, fontSize: 24 }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n              Bobine Disponibili\n            </Typography>\n          </Box>\n          <FilterableTable\n            data={data.bobine_per_tipo || []}\n            columns={[\n              { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n              { field: 'sezione', headerName: 'Sezione', width: 100 },\n              { field: 'num_bobine', headerName: 'Bobine', width: 100, align: 'right', dataType: 'number' },\n              { field: 'metri_disponibili', headerName: 'Metri Disponibili', width: 150, align: 'right', dataType: 'number',\n                renderCell: (row) => `${row.metri_disponibili}m` }\n            ]}\n            pageSize={10}\n          />\n        </CardContent>\n      </Card>\n    </Box>\n  );\n\n  const renderBobineReport = (data) => (\n    <Box>\n      {/* Header con controlli */}\n      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 3 }}>\n          <BobineChart data={data} />\n        </Box>\n      )}\n\n      {/* Bobine del Cantiere */}\n      <Paper sx={{ p: 2, border: '1px solid #e0e0e0' }}>\n        <Typography variant=\"subtitle1\" sx={{ fontWeight: 500, mb: 2, color: '#2c3e50' }}>\n          Utilizzo Bobine del Cantiere\n        </Typography>\n        <FilterableTable\n          data={data.bobine || []}\n          columns={[\n            { field: 'id_bobina', headerName: 'ID Bobina', width: 120 },\n            { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n            { field: 'sezione', headerName: 'Sezione', width: 100 },\n            { field: 'stato', headerName: 'Stato', width: 120,\n              renderCell: (row) => (\n                <Chip\n                  label={row.stato}\n                  color={row.stato === 'DISPONIBILE' ? 'primary' : 'default'}\n                  size=\"small\"\n                  sx={{\n                    bgcolor: row.stato === 'DISPONIBILE' ? '#3498db' : '#85929e',\n                    color: 'white'\n                  }}\n                />\n              )\n            },\n            { field: 'metri_totali', headerName: 'Metri Totali', width: 120, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri_totali}m` },\n            { field: 'metri_residui', headerName: 'Metri Residui', width: 120, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri_residui}m` },\n            { field: 'metri_utilizzati', headerName: 'Metri Utilizzati', width: 140, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri_utilizzati}m` },\n            { field: 'percentuale_utilizzo', headerName: 'Utilizzo', width: 100, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.percentuale_utilizzo}%` }\n          ]}\n          pageSize={10}\n        />\n      </Paper>\n    </Box>\n  );\n\n  const renderBobinaSpecificaReport = (data) => (\n    <Box>\n      {/* Header */}\n      <Typography variant=\"h5\" sx={{ fontWeight: 600, color: 'info.main', mb: 3 }}>\n        Report Bobina Specifica - {data.bobina?.id_bobina}\n      </Typography>\n\n      <Grid container spacing={3}>\n        {/* Dettagli Bobina */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n              Dettagli Bobina\n            </Typography>\n            {data.bobina && (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\">ID Bobina:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{data.bobina.id_bobina}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\">Tipologia:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{data.bobina.tipologia}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\">Sezione:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{data.bobina.sezione}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\">Stato:</Typography>\n                  <Chip\n                    label={data.bobina.stato}\n                    color={data.bobina.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n                    size=\"small\"\n                  />\n                </Box>\n              </Box>\n            )}\n          </Paper>\n        </Grid>\n\n        {/* Metriche Utilizzo */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n              Metriche Utilizzo\n            </Typography>\n            {data.bobina && (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\">Metri Totali:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{data.bobina.metri_totali}m</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\">Metri Utilizzati:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600, color: 'success.main' }}>\n                    {data.bobina.metri_utilizzati}m\n                  </Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\">Metri Residui:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600, color: 'warning.main' }}>\n                    {data.bobina.metri_residui}m\n                  </Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body1\">Percentuale Utilizzo:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{data.bobina.percentuale_utilizzo}%</Typography>\n                </Box>\n              </Box>\n            )}\n          </Paper>\n        </Grid>\n\n        {/* Cavi Associati */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n              Cavi Associati ({data.totale_cavi})\n            </Typography>\n            <FilterableTable\n              data={data.cavi_associati || []}\n              columns={[\n                { field: 'id_cavo', headerName: 'ID Cavo', width: 120 },\n                { field: 'sistema', headerName: 'Sistema', width: 120 },\n                { field: 'utility', headerName: 'Utility', width: 120 },\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_teorici}m` },\n                { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_reali}m` },\n                { field: 'stato', headerName: 'Stato', width: 120,\n                  renderCell: (row) => (\n                    <Chip\n                      label={row.stato}\n                      color={row.stato === 'POSATO' ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  )\n                }\n              ]}\n              pageSize={10}\n            />\n          </Paper>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n\n  const renderPosaPeriodoReport = (data) => (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: 'warning.main' }}>\n          Report Posa per Periodo\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Statistiche Periodo - Layout Orizzontale */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'warning.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.totale_metri_periodo}m\n            </Typography>\n            <Typography variant=\"body1\">Metri Totali</Typography>\n            <Typography variant=\"caption\">{data.data_inizio} - {data.data_fine}</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'info.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.giorni_attivi}\n            </Typography>\n            <Typography variant=\"body1\">Giorni Attivi</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'success.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.media_giornaliera}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Giorno</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {Math.round(data.totale_metri_periodo / data.giorni_attivi * 7)}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Settimana</Typography>\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <TimelineChart data={data} />\n        </Box>\n      )}\n\n      {/* Posa Giornaliera */}\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n          Dettaglio Posa Giornaliera\n        </Typography>\n        <FilterableTable\n          data={data.posa_giornaliera || []}\n          columns={[\n            { field: 'data', headerName: 'Data', width: 200 },\n            { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri}m` }\n          ]}\n          pageSize={10}\n        />\n      </Paper>\n    </Box>\n  );\n\n  const renderCaviStatoReport = (data) => (\n    <Box>\n      {/* Header con controlli */}\n      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Grafici migliorati */}\n      {showCharts && (\n        <Paper sx={{ p: 2, mb: 3, border: '1px solid #e0e0e0' }}>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 500, mb: 2, color: '#2c3e50' }}>\n            Distribuzione Stati Cavi\n          </Typography>\n          <Box sx={{\n            border: '1px solid #e0e0e0',\n            borderRadius: 1,\n            overflow: 'hidden'\n          }}>\n            <table style={{ width: '100%', borderCollapse: 'collapse' }}>\n              <thead>\n                <tr style={{ backgroundColor: '#f8f9fa' }}>\n                  <th style={{\n                    padding: '8px 12px',\n                    textAlign: 'left',\n                    fontSize: '12px',\n                    fontWeight: 600,\n                    color: '#2c3e50',\n                    borderBottom: '1px solid #e0e0e0',\n                    width: '25%'\n                  }}>Stato</th>\n                  <th style={{\n                    padding: '8px 12px',\n                    textAlign: 'center',\n                    fontSize: '12px',\n                    fontWeight: 600,\n                    color: '#2c3e50',\n                    borderBottom: '1px solid #e0e0e0',\n                    width: '25%'\n                  }}>Numero Cavi</th>\n                  <th style={{\n                    padding: '8px 12px',\n                    textAlign: 'center',\n                    fontSize: '12px',\n                    fontWeight: 600,\n                    color: '#2c3e50',\n                    borderBottom: '1px solid #e0e0e0',\n                    width: '25%'\n                  }}>Metri Teorici</th>\n                  <th style={{\n                    padding: '8px 12px',\n                    textAlign: 'center',\n                    fontSize: '12px',\n                    fontWeight: 600,\n                    color: '#2c3e50',\n                    borderBottom: '1px solid #e0e0e0',\n                    width: '25%'\n                  }}>Distribuzione</th>\n                </tr>\n              </thead>\n              <tbody>\n                {(data.cavi_per_stato || []).map((item, index) => {\n                  const totalCavi = (data.cavi_per_stato || []).reduce((sum, s) => sum + s.num_cavi, 0);\n                  const percentage = totalCavi > 0 ? Math.round((item.num_cavi / totalCavi) * 100) : 0;\n                  return (\n                    <tr key={index}>\n                      <td style={{\n                        padding: '12px',\n                        fontSize: '13px',\n                        borderBottom: index < data.cavi_per_stato.length - 1 ? '1px solid #f0f0f0' : 'none'\n                      }}>\n                        <Chip\n                          label={item.stato}\n                          size=\"small\"\n                          sx={{\n                            bgcolor: item.stato === 'Installato' ? '#3498db' : '#85929e',\n                            color: 'white'\n                          }}\n                        />\n                      </td>\n                      <td style={{\n                        padding: '12px',\n                        fontSize: '13px',\n                        textAlign: 'center',\n                        fontWeight: 600,\n                        borderBottom: index < data.cavi_per_stato.length - 1 ? '1px solid #f0f0f0' : 'none'\n                      }}>{item.num_cavi}</td>\n                      <td style={{\n                        padding: '12px',\n                        fontSize: '13px',\n                        textAlign: 'center',\n                        fontWeight: 600,\n                        borderBottom: index < data.cavi_per_stato.length - 1 ? '1px solid #f0f0f0' : 'none'\n                      }}>{item.metri_teorici}m</td>\n                      <td style={{\n                        padding: '12px',\n                        fontSize: '13px',\n                        textAlign: 'center',\n                        borderBottom: index < data.cavi_per_stato.length - 1 ? '1px solid #f0f0f0' : 'none'\n                      }}>\n                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>\n                          <Box sx={{\n                            width: '60px',\n                            height: '6px',\n                            bgcolor: '#e0e0e0',\n                            borderRadius: '3px',\n                            position: 'relative'\n                          }}>\n                            <Box sx={{\n                              width: `${percentage}%`,\n                              height: '100%',\n                              bgcolor: item.stato === 'Installato' ? '#3498db' : '#85929e',\n                              borderRadius: '3px'\n                            }} />\n                          </Box>\n                          <Typography variant=\"caption\" sx={{ fontWeight: 600 }}>\n                            {percentage}%\n                          </Typography>\n                        </Box>\n                      </td>\n                    </tr>\n                  );\n                })}\n              </tbody>\n            </table>\n          </Box>\n        </Paper>\n      )}\n\n      {/* Tabella Dettagliata */}\n      <Paper sx={{ p: 2, border: '1px solid #e0e0e0' }}>\n        <Typography variant=\"subtitle1\" sx={{ fontWeight: 500, mb: 2, color: '#2c3e50' }}>\n          Cavi per Stato di Installazione\n        </Typography>\n        <FilterableTable\n          data={data.cavi_per_stato || []}\n          columns={[\n            { field: 'stato', headerName: 'Stato', width: 150,\n              renderCell: (row) => (\n                <Chip\n                  label={row.stato}\n                  size=\"small\"\n                  sx={{\n                    bgcolor: row.stato === 'Installato' ? '#3498db' : '#85929e',\n                    color: 'white'\n                  }}\n                />\n              )\n            },\n            { field: 'num_cavi', headerName: 'Numero Cavi', width: 120, align: 'right', dataType: 'number' },\n            { field: 'metri_teorici', headerName: 'Metri Teorici', width: 150, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri_teorici}m` },\n            { field: 'metri_reali', headerName: 'Metri Reali', width: 150, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri_reali}m` }\n          ]}\n          pagination={false}\n        />\n      </Paper>\n    </Box>\n  );\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {selectedReport?.title}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n          {dialogType === 'bobina-specifica' && (\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"ID Bobina\"\n                value={formData.id_bobina}\n                onChange={(e) => setFormData({ ...formData, id_bobina: e.target.value })}\n                placeholder=\"Es: 1, 2, A, B...\"\n                helperText=\"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n              />\n            </Grid>\n          )}\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box className=\"report-main-container report-fade-in\">\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Loading indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Reports Navigation */}\n      <Box sx={{ mt: 3 }}>\n        {/* Report Navigation - Design Compatto */}\n        <Box sx={{ mb: 3 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50', mb: 2, textAlign: 'center' }}>\n            🎯 Seleziona il tipo di report\n          </Typography>\n          <Grid container spacing={2}>\n            {/* Report Avanzamento */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                className={`report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`}\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('progress')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <AssessmentIcon sx={{ fontSize: 32, color: '#3498db', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Avanzamento\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Panoramica lavori\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {/* Bill of Quantities */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('boq')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <ListIcon sx={{ fontSize: 32, color: '#8e44ad', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Bill of Quantities\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Distinta materiali\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {/* Report Bobine */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'bobine' ? '2px solid #16a085' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'bobine' ? '#f0fff4' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('bobine')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <InventoryIcon sx={{ fontSize: 32, color: '#16a085', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Report Bobine\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Utilizzo bobine\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {/* Cavi per Stato */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'cavi-stato' ? '2px solid #e74c3c' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'cavi-stato' ? '#fff5f5' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('cavi-stato')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <BarChartIcon sx={{ fontSize: 32, color: '#e74c3c', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Cavi per Stato\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Stati installazione\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {/* Bobina Specifica */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'bobina-specifica' ? '2px solid #f39c12' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'bobina-specifica' ? '#fffbf0' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('bobina-specifica')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <CableIcon sx={{ fontSize: 32, color: '#f39c12', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Bobina Specifica\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Dettaglio bobina\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {/* Posa per Periodo */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'posa-periodo' ? '2px solid #9b59b6' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'posa-periodo' ? '#f8f4ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('posa-periodo')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <TimelineIcon sx={{ fontSize: 32, color: '#9b59b6', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Posa per Periodo\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Analisi temporale\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </Box>\n\n        {/* Report Content */}\n        <Box sx={{ minHeight: '400px' }}>\n          {/* Progress Report */}\n          {selectedReportType === 'progress' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.progress ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderProgressReport(reportsData.progress)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"progress\"\n                  title=\"Caricamento Report Avanzamento...\"\n                  description=\"Stiamo elaborando i dati dell'avanzamento dei lavori\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"progress\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare il report di avanzamento. Verifica la connessione e riprova.\"\n                  onRetry={() => {\n                    setLoading(true);\n                    reportService.getProgressReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          progress: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying progress report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n          {/* Bill of Quantities */}\n          {selectedReportType === 'boq' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.boq ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderBoqReport(reportsData.boq)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"boq\"\n                  title=\"Caricamento Bill of Quantities...\"\n                  description=\"Stiamo elaborando la distinta materiali\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"boq\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare la distinta materiali. Verifica la connessione e riprova.\"\n                  onRetry={() => {\n                    setLoading(true);\n                    reportService.getBillOfQuantities(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          boq: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying BOQ report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n          {/* Bobine Report */}\n          {selectedReportType === 'bobine' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.bobine ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('bobine', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('bobine', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderBobineReport(reportsData.bobine)}\n                </Box>\n              ) : loading ? (\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 4 }}>\n                  <CircularProgress size={24} />\n                  <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', my: 4 }}>\n                  <Alert severity=\"error\" sx={{ mb: 2 }}>\n                    Impossibile caricare il report. Riprova più tardi.\n                  </Alert>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<RefreshIcon />}\n                    onClick={() => {\n                      setLoading(true);\n                      reportService.getBobineReport(cantiereId, 'video')\n                        .then(data => {\n                          setReportsData(prev => ({\n                            ...prev,\n                            bobine: data.content\n                          }));\n                        })\n                        .catch(err => {\n                          console.error('Error retrying bobine report:', err);\n                        })\n                        .finally(() => {\n                          setLoading(false);\n                        });\n                    }}\n                  >\n                    Riprova\n                  </Button>\n                </Box>\n              )}\n            </Paper>\n          )}\n\n          {/* Cavi Stato Report */}\n          {selectedReportType === 'cavi-stato' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.caviStato ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('cavi-stato', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('cavi-stato', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderCaviStatoReport(reportsData.caviStato)}\n                </Box>\n              ) : loading ? (\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 4 }}>\n                  <CircularProgress size={24} />\n                  <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', my: 4 }}>\n                  <Alert severity=\"error\" sx={{ mb: 2 }}>\n                    Impossibile caricare il report. Riprova più tardi.\n                  </Alert>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<RefreshIcon />}\n                    onClick={() => {\n                      setLoading(true);\n                      reportService.getCaviStatoReport(cantiereId, 'video')\n                        .then(data => {\n                          setReportsData(prev => ({\n                            ...prev,\n                            caviStato: data.content\n                          }));\n                        })\n                        .catch(err => {\n                          console.error('Error retrying cavi stato report:', err);\n                        })\n                        .finally(() => {\n                          setLoading(false);\n                        });\n                    }}\n                  >\n                    Riprova\n                  </Button>\n                </Box>\n              )}\n            </Paper>\n          )}\n\n          {/* Bobina Specifica Report */}\n          {selectedReportType === 'bobina-specifica' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.bobinaSpecifica ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('bobina-specifica', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('bobina-specifica', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderBobinaSpecificaReport(reportsData.bobinaSpecifica)}\n                </Box>\n              ) : (\n                <EmptyState\n                  type=\"action-required\"\n                  reportType=\"bobina-specifica\"\n                  title=\"Seleziona una Bobina\"\n                  description=\"Inserisci l'ID di una bobina per generare il report dettagliato con tutti i cavi associati e le metriche di utilizzo.\"\n                  actionLabel=\"Seleziona Bobina\"\n                  onAction={() => {\n                    setDialogType('bobina-specifica');\n                    setOpenDialog(true);\n                  }}\n                />\n              )}\n            </Paper>\n          )}\n\n          {/* Posa per Periodo Report */}\n          {selectedReportType === 'posa-periodo' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.posaPeriodo ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderPosaPeriodoReport(reportsData.posaPeriodo)}\n                </Box>\n              ) : (\n                <EmptyState\n                  type=\"action-required\"\n                  reportType=\"posa-periodo\"\n                  title=\"Seleziona un Periodo\"\n                  description=\"Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttività del team.\"\n                  actionLabel=\"Seleziona Periodo\"\n                  onAction={() => {\n                    setDialogType('posa-periodo');\n                    // Set default date range (last month to today)\n                    const today = new Date();\n                    const lastMonth = new Date();\n                    lastMonth.setMonth(today.getMonth() - 1);\n\n                    setFormData({\n                      ...formData,\n                      data_inizio: lastMonth.toISOString().split('T')[0],\n                      data_fine: today.toISOString().split('T')[0]\n                    });\n                    setOpenDialog(true);\n                  }}\n                />\n              )}\n            </Paper>\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,0BAA0B;AACjC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,aAAa,MAAM,uCAAuC;;AAEjE;AACA,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,cAAc,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqB;EAAW,CAAC,GAAGpB,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEqB;EAAK,CAAC,GAAGpB,OAAO,CAAC,CAAC;EAE1B,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmF,KAAK,EAAEC,QAAQ,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqF,UAAU,EAAEC,aAAa,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACuF,cAAc,EAAEC,iBAAiB,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACyF,UAAU,EAAEC,aAAa,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2F,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9F,QAAQ,CAAC,UAAU,CAAC;EACxE,MAAM,CAAC+F,QAAQ,EAAEC,WAAW,CAAC,GAAGhG,QAAQ,CAAC;IACvCiG,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtG,QAAQ,CAAC;IAC7CuG,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,IAAI;IACrBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9G,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM8G,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC7B,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAM8B,eAAe,GAAGlD,aAAa,CAACmD,iBAAiB,CAAClC,UAAU,EAAE,OAAO,CAAC,CACzEmC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAACjC,KAAK,CAAC,gCAAgC,EAAEgC,GAAG,CAAC;UACpD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMC,UAAU,GAAGxD,aAAa,CAACyD,mBAAmB,CAACxC,UAAU,EAAE,OAAO,CAAC,CACtEmC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAACjC,KAAK,CAAC,2BAA2B,EAAEgC,GAAG,CAAC;UAC/C,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMG,aAAa,GAAG1D,aAAa,CAAC2D,eAAe,CAAC1C,UAAU,EAAE,OAAO,CAAC,CACrEmC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAACjC,KAAK,CAAC,8BAA8B,EAAEgC,GAAG,CAAC;UAClD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMK,gBAAgB,GAAG5D,aAAa,CAAC6D,kBAAkB,CAAC5C,UAAU,EAAE,OAAO,CAAC,CAC3EmC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAACjC,KAAK,CAAC,kCAAkC,EAAEgC,GAAG,CAAC;UACtD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;;QAEJ;QACA,MAAM,CAACO,YAAY,EAAEC,OAAO,EAAEC,UAAU,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3EjB,eAAe,EACfM,UAAU,EACVE,aAAa,EACbE,gBAAgB,CACjB,CAAC;;QAEF;QACApB,cAAc,CAAC;UACbC,QAAQ,EAAEqB,YAAY,CAACP,OAAO;UAC9Bb,GAAG,EAAEqB,OAAO,CAACR,OAAO;UACpBZ,MAAM,EAAEqB,UAAU,CAACT,OAAO;UAC1BX,SAAS,EAAEqB,aAAa,CAACV,OAAO;UAChCV,eAAe,EAAE,IAAI;UACrBC,WAAW,EAAE;QACf,CAAC,CAAC;;QAEF;QACA,IAAIgB,YAAY,CAACP,OAAO,IAAIQ,OAAO,CAACR,OAAO,IAAIS,UAAU,CAACT,OAAO,IAAIU,aAAa,CAACV,OAAO,EAAE;UAC1FjC,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,MAAM;UACLA,QAAQ,CAAC,uDAAuD,CAAC;QACnE;MACF,CAAC,CAAC,OAAO+B,GAAG,EAAE;QACZ;QACAC,OAAO,CAACjC,KAAK,CAAC,mCAAmC,EAAEgC,GAAG,CAAC;QACvD/B,QAAQ,CAAC,uDAAuD,CAAC;MACnE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIH,UAAU,EAAE;MACdgC,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAChC,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMmD,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,2FAA2F;IACxGC,IAAI,eAAE7D,OAAA,CAAC1C,cAAc;MAAAwG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,yBAAyB;EACrH,CAAC,EACD;IACET,EAAE,EAAE,KAAK;IACTC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,wEAAwE;IACrFC,IAAI,eAAE7D,OAAA,CAAClC,QAAQ;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,0BAA0B,EAAE,qBAAqB,EAAE,eAAe;EAC1G,CAAC,EACD;IACET,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,uEAAuE;IACpFC,IAAI,eAAE7D,OAAA,CAACpB,aAAa;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,iBAAiB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,yEAAyE;IACtFC,IAAI,eAAE7D,OAAA,CAACtB,SAAS;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,oBAAoB;EAC7F,CAAC,EACD;IACET,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,eAAE7D,OAAA,CAACpC,YAAY;MAAAkG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,mBAAmB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,iFAAiF;IAC9FC,IAAI,eAAE7D,OAAA,CAACxC,YAAY;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,2BAA2B,EAAE,eAAe,EAAE,kBAAkB;EAC/F,CAAC,CACF;;EAED;EACA,MAAMC,wBAAwB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,MAAM,KAAK;IAC7D,IAAI;MACF7D,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI4D,QAAQ;MAEZ,QAAQF,UAAU;QAChB,KAAK,UAAU;UACbE,QAAQ,GAAG,MAAMlF,aAAa,CAACmD,iBAAiB,CAAClC,UAAU,EAAEgE,MAAM,CAAC;UACpE;QACF,KAAK,KAAK;UACRC,QAAQ,GAAG,MAAMlF,aAAa,CAACyD,mBAAmB,CAACxC,UAAU,EAAEgE,MAAM,CAAC;UACtE;QACF,KAAK,QAAQ;UACXC,QAAQ,GAAG,MAAMlF,aAAa,CAAC2D,eAAe,CAAC1C,UAAU,EAAEgE,MAAM,CAAC;UAClE;QACF,KAAK,YAAY;UACfC,QAAQ,GAAG,MAAMlF,aAAa,CAAC6D,kBAAkB,CAAC5C,UAAU,EAAEgE,MAAM,CAAC;UACrE;QACF,KAAK,kBAAkB;UACrB,IAAI,CAAChD,QAAQ,CAACK,SAAS,EAAE;YACvBhB,QAAQ,CAAC,8BAA8B,CAAC;YACxC;UACF;UACA4D,QAAQ,GAAG,MAAMlF,aAAa,CAACmF,eAAe,CAAClE,UAAU,EAAEgB,QAAQ,CAACK,SAAS,EAAE2C,MAAM,CAAC;UACtF;QACF,KAAK,cAAc;UACjB,IAAI,CAAChD,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDf,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACA4D,QAAQ,GAAG,MAAMlF,aAAa,CAACoF,uBAAuB,CACpDnE,UAAU,EACVgB,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClB4C,MACF,CAAC;UACD;QACF;UACE,MAAM,IAAII,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIJ,MAAM,KAAK,OAAO,EAAE;QACtB;QACA,IAAID,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,cAAc,EAAE;UACtExC,cAAc,CAAC8C,IAAI,KAAK;YACtB,GAAGA,IAAI;YACP,CAACN,UAAU,KAAK,kBAAkB,GAAG,iBAAiB,GAAG,aAAa,GAAGE,QAAQ,CAAC3B;UACpF,CAAC,CAAC,CAAC;QACL;QACA/B,aAAa,CAAC0D,QAAQ,CAAC3B,OAAO,CAAC;MACjC,CAAC,MAAM;QACL;QACA,IAAI2B,QAAQ,CAACK,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAACP,QAAQ,CAACK,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;IACF,CAAC,CAAC,OAAOlC,GAAG,EAAE;MACZC,OAAO,CAACjC,KAAK,CAAC,sCAAsC,EAAEgC,GAAG,CAAC;MAC1D/B,QAAQ,CAAC+B,GAAG,CAACqC,MAAM,IAAIrC,GAAG,CAACsC,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACRvE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwE,kBAAkB,GAAIZ,UAAU,IAAK;IACzCtD,iBAAiB,CAACsD,UAAU,CAAC;IAC7BlD,aAAa,CAACkD,UAAU,CAACX,EAAE,CAAC;;IAE5B;IACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,IAAIW,UAAU,CAACX,EAAE,KAAK,kBAAkB,EAAE;MAC5E;MACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,EAAE;QACpC,MAAMwB,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;QACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;QAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAExC/D,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXG,WAAW,EAAE2D,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAClD9D,SAAS,EAAEwD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC;MACJ;MAEAvE,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACL;MACAmD,wBAAwB,CAACC,UAAU,CAACX,EAAE,EAAE,OAAO,CAAC;IAClD;EACF,CAAC;EAED,MAAM+B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMrB,wBAAwB,CAAClD,UAAU,EAAEI,QAAQ,CAACE,OAAO,CAAC;IAC5DP,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMyE,iBAAiB,GAAGA,CAAA,KAAM;IAC9BzE,aAAa,CAAC,KAAK,CAAC;IACpBN,QAAQ,CAAC,IAAI,CAAC;IACdY,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgE,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC/E,UAAU,EAAE,OAAO,IAAI;IAE5B,oBACEZ,OAAA,CAACrE,KAAK;MAACiK,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzB/F,OAAA,CAACvE,GAAG;QAACmK,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzF/F,OAAA,CAACtE,UAAU;UAAC0K,OAAO,EAAC,IAAI;UAAAL,QAAA,GACrBjF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6C,KAAK,EAAC,KAAG,EAAC/C,UAAU,CAACyF,aAAa;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACbjE,OAAA,CAACvE,GAAG;UAACmK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE;UAAE,CAAE;UAAAP,QAAA,gBAEnC/F,OAAA,CAAChE,MAAM;YACLuK,SAAS,eAAEvG,OAAA,CAAChC,YAAY;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAClD,UAAU,EAAE,KAAK,CAAE;YAC3DkF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZvC,KAAK,EAAC,SAAS;YAAA6B,QAAA,EAChB;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjE,OAAA,CAAChE,MAAM;YACLuK,SAAS,eAAEvG,OAAA,CAAChC,YAAY;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAClD,UAAU,EAAE,OAAO,CAAE;YAC7DkF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZvC,KAAK,EAAC,SAAS;YAAA6B,QAAA,EAChB;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjE,OAAA,CAAChE,MAAM;YACLuK,SAAS,eAAEvG,OAAA,CAAC5B,WAAW;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BuC,OAAO,EAAEA,CAAA,KAAM3F,aAAa,CAAC,IAAI,CAAE;YACnCuF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YAAAV,QAAA,EACb;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjE,OAAA,CAAC5D,OAAO;QAACwJ,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE;MAAE;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGzB/C,UAAU,KAAK,UAAU,IAAIwF,oBAAoB,CAAC9F,UAAU,CAAC,EAC7DM,UAAU,KAAK,KAAK,IAAIyF,eAAe,CAAC/F,UAAU,CAAC,EACnDM,UAAU,KAAK,QAAQ,IAAI0F,kBAAkB,CAAChG,UAAU,CAAC,EACzDM,UAAU,KAAK,kBAAkB,IAAI2F,2BAA2B,CAACjG,UAAU,CAAC,EAC5EM,UAAU,KAAK,cAAc,IAAI4F,uBAAuB,CAAClG,UAAU,CAAC,EACpEM,UAAU,KAAK,YAAY,IAAI6F,qBAAqB,CAACnG,UAAU,CAAC;IAAA;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC;EAEZ,CAAC;EAED,MAAMyC,oBAAoB,GAAIM,IAAI,iBAChChH,OAAA,CAACvE,GAAG;IAAAsK,QAAA,gBAEF/F,OAAA,CAACvE,GAAG;MAACmK,EAAE,EAAE;QACPI,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLN,CAAC,EAAE,CAAC;QACJoB,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE;MACV,CAAE;MAAApB,QAAA,gBACA/F,OAAA,CAACtE,UAAU;QAAC0K,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEwB,UAAU,EAAE,GAAG;UAAElD,KAAK,EAAE;QAAU,CAAE;QAAA6B,QAAA,EAAC;MAEpE;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAAC5C,gBAAgB;QACfiK,OAAO,eACLrH,OAAA,CAAC7C,MAAM;UACLmK,OAAO,EAAElF,UAAW;UACpBmF,QAAQ,EAAGC,CAAC,IAAKnF,aAAa,CAACmF,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDpD,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDyD,KAAK,eACH1H,OAAA,CAACvE,GAAG;UAACmK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjD/F,OAAA,CAAChB,aAAa;YAAC4G,EAAE,EAAE;cAAE+B,EAAE,EAAE;YAAE;UAAE;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNjE,OAAA,CAACpE,IAAI;MAACgM,SAAS;MAACC,OAAO,EAAE,CAAE;MAACjC,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxC/F,OAAA,CAACpE,IAAI;QAACkM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAlC,QAAA,eAC9B/F,OAAA,CAACR,UAAU;UACTmE,KAAK,EAAC,cAAc;UACpBuE,KAAK,EAAElB,IAAI,CAACmB,YAAa;UACzBC,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAC,oCAAoC;UAC7CC,QAAQ,EAAC,mDAAmD;UAC5D7B,IAAI,EAAC;QAAQ;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPjE,OAAA,CAACpE,IAAI;QAACkM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAlC,QAAA,eAC9B/F,OAAA,CAACR,UAAU;UACTmE,KAAK,EAAC,cAAc;UACpBuE,KAAK,EAAElB,IAAI,CAACuB,YAAa;UACzBH,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAE,GAAGrB,IAAI,CAACwB,uBAAuB,cAAe;UACxDF,QAAQ,EAAC,mDAAmD;UAC5DxG,QAAQ,EAAEkF,IAAI,CAACwB,uBAAwB;UACvCC,KAAK,EAAEzB,IAAI,CAACwB,uBAAuB,GAAG,EAAE,GAAG,IAAI,GAAGxB,IAAI,CAACwB,uBAAuB,GAAG,EAAE,GAAG,MAAM,GAAG,MAAO;UACtGE,UAAU,EAAE,GAAG1B,IAAI,CAACwB,uBAAuB,GAAI;UAC/C/B,IAAI,EAAC;QAAQ;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPjE,OAAA,CAACpE,IAAI;QAACkM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAlC,QAAA,eAC9B/F,OAAA,CAACR,UAAU;UACTmE,KAAK,EAAC,iBAAiB;UACvBuE,KAAK,EAAElB,IAAI,CAAC2B,eAAgB;UAC5BP,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAE,GAAG,CAAC,GAAG,GAAGrB,IAAI,CAACwB,uBAAuB,EAAEI,OAAO,CAAC,CAAC,CAAC,iBAAkB;UAC9EN,QAAQ,EAAC,mDAAmD;UAC5D7B,IAAI,EAAC;QAAQ;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPjE,OAAA,CAACpE,IAAI;QAACkM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAlC,QAAA,eAC9B/F,OAAA,CAACR,UAAU;UACTmE,KAAK,EAAC,cAAc;UACpBuE,KAAK,EAAElB,IAAI,CAAC6B,iBAAiB,IAAI,CAAE;UACnCT,IAAI,EAAC,GAAG;UACRC,QAAQ,EACNrB,IAAI,CAAC8B,cAAc,GACf,GAAG9B,IAAI,CAAC8B,cAAc,4BAA4B,GACjD9B,IAAI,CAAC6B,iBAAiB,GAAG,CAAC,GACvB,kBAAkB,GAClB,sBACT;UACDP,QAAQ,EAAC,mDAAmD;UAC5D7B,IAAI,EAAC,QAAQ;UACbsC,OAAO,EACL/B,IAAI,CAACgC,2BAA2B,GAC5B,gBAAgBhC,IAAI,CAACgC,2BAA2B,oFAAoF,GACpI;QACL;UAAAlF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGN7B,UAAU,iBACTpC,OAAA,CAACvE,GAAG;MAACmK,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACjB/F,OAAA,CAACN,aAAa;QAACsH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDjE,OAAA,CAACpE,IAAI;MAACgM,SAAS;MAACC,OAAO,EAAE,CAAE;MAACjC,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxC/F,OAAA,CAACpE,IAAI;QAACkM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAlC,QAAA,eACvB/F,OAAA,CAACnE,IAAI;UAAC+J,EAAE,EAAE;YAAEqD,MAAM,EAAE,MAAM;YAAE9B,MAAM,EAAE;UAAoB,CAAE;UAAApB,QAAA,eACxD/F,OAAA,CAAClE,WAAW;YAAC8J,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBACxB/F,OAAA,CAACvE,GAAG;cAACmK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACxD/F,OAAA,CAACtB,SAAS;gBAACkH,EAAE,EAAE;kBAAE1B,KAAK,EAAE,SAAS;kBAAEyD,EAAE,EAAE,CAAC;kBAAEuB,QAAQ,EAAE;gBAAG;cAAE;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5DjE,OAAA,CAACtE,UAAU;gBAAC0K,OAAO,EAAC,IAAI;gBAACR,EAAE,EAAE;kBAAEwB,UAAU,EAAE,GAAG;kBAAElD,KAAK,EAAE;gBAAU,CAAE;gBAAA6B,QAAA,EAAC;cAEpE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjE,OAAA,CAACpE,IAAI;cAACgM,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA9B,QAAA,gBACzB/F,OAAA,CAACpE,IAAI;gBAACkM,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAhC,QAAA,eACf/F,OAAA,CAACvE,GAAG;kBAACmK,EAAE,EAAE;oBAAEuD,SAAS,EAAE,QAAQ;oBAAEtD,CAAC,EAAE,CAAC;oBAAEoB,OAAO,EAAE,SAAS;oBAAEC,YAAY,EAAE;kBAAE,CAAE;kBAAAnB,QAAA,gBAC1E/F,OAAA,CAACtE,UAAU;oBAAC0K,OAAO,EAAC,IAAI;oBAACR,EAAE,EAAE;sBAAEwB,UAAU,EAAE,GAAG;sBAAElD,KAAK,EAAE,SAAS;sBAAEiC,EAAE,EAAE;oBAAE,CAAE;oBAAAJ,QAAA,EACvEiB,IAAI,CAACoC;kBAAW;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACbjE,OAAA,CAACtE,UAAU;oBAAC0K,OAAO,EAAC,OAAO;oBAACR,EAAE,EAAE;sBAAE1B,KAAK,EAAE;oBAAO,CAAE;oBAAA6B,QAAA,EAAC;kBAEnD;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACPjE,OAAA,CAACpE,IAAI;gBAACkM,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAAhC,QAAA,eACf/F,OAAA,CAACvE,GAAG;kBAACmK,EAAE,EAAE;oBAAEuD,SAAS,EAAE,QAAQ;oBAAEtD,CAAC,EAAE,CAAC;oBAAEoB,OAAO,EAAE,SAAS;oBAAEC,YAAY,EAAE;kBAAE,CAAE;kBAAAnB,QAAA,gBAC1E/F,OAAA,CAACtE,UAAU;oBAAC0K,OAAO,EAAC,IAAI;oBAACR,EAAE,EAAE;sBAAEwB,UAAU,EAAE,GAAG;sBAAElD,KAAK,EAAE,SAAS;sBAAEiC,EAAE,EAAE;oBAAE,CAAE;oBAAAJ,QAAA,EACvEiB,IAAI,CAACqC;kBAAW;oBAAAvF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACbjE,OAAA,CAACtE,UAAU;oBAAC0K,OAAO,EAAC,OAAO;oBAACR,EAAE,EAAE;sBAAE1B,KAAK,EAAE;oBAAO,CAAE;oBAAA6B,QAAA,GAAC,eACpC,EAACiB,IAAI,CAACsC,gBAAgB,EAAC,IACtC;kBAAA;oBAAAxF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACPjE,OAAA,CAACvE,GAAG;cAACmK,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAC,QAAA,gBACjB/F,OAAA,CAACvE,GAAG;gBAACmK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAS;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClDjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEwB,UAAU,EAAE;kBAAI,CAAE;kBAAArB,QAAA,GACjDiB,IAAI,CAACsC,gBAAgB,EAAC,GACzB;gBAAA;kBAAAxF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjE,OAAA,CAACvE,GAAG;gBAACmK,EAAE,EAAE;kBACP2D,KAAK,EAAE,MAAM;kBACbN,MAAM,EAAE,CAAC;kBACThC,OAAO,EAAE,SAAS;kBAClBC,YAAY,EAAE,CAAC;kBACfsC,QAAQ,EAAE;gBACZ,CAAE;gBAAAzD,QAAA,eACA/F,OAAA,CAACvE,GAAG;kBAACmK,EAAE,EAAE;oBACP2D,KAAK,EAAE,GAAGvC,IAAI,CAACsC,gBAAgB,GAAG;oBAClCL,MAAM,EAAE,MAAM;oBACdhC,OAAO,EAAE,SAAS;oBAClBwC,UAAU,EAAE;kBACd;gBAAE;kBAAA3F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPjE,OAAA,CAACpE,IAAI;QAACkM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAlC,QAAA,eACvB/F,OAAA,CAACnE,IAAI;UAAC+J,EAAE,EAAE;YAAEqD,MAAM,EAAE,MAAM;YAAE9B,MAAM,EAAE;UAAoB,CAAE;UAAApB,QAAA,eACxD/F,OAAA,CAAClE,WAAW;YAAC8J,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBACxB/F,OAAA,CAACvE,GAAG;cAACmK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACxD/F,OAAA,CAACpC,YAAY;gBAACgI,EAAE,EAAE;kBAAE1B,KAAK,EAAE,SAAS;kBAAEyD,EAAE,EAAE,CAAC;kBAAEuB,QAAQ,EAAE;gBAAG;cAAE;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/DjE,OAAA,CAACtE,UAAU;gBAAC0K,OAAO,EAAC,IAAI;gBAACR,EAAE,EAAE;kBAAEwB,UAAU,EAAE,GAAG;kBAAElD,KAAK,EAAE;gBAAU,CAAE;gBAAA6B,QAAA,EAAC;cAEpE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjE,OAAA,CAACvE,GAAG;cAACmK,EAAE,EAAE;gBAAEuD,SAAS,EAAE,QAAQ;gBAAEhD,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACtC/F,OAAA,CAACtE,UAAU;gBAAC0K,OAAO,EAAC,IAAI;gBAACR,EAAE,EAAE;kBAAEwB,UAAU,EAAE,GAAG;kBAAElD,KAAK,EAAE,SAAS;kBAAEiC,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,GACvEiB,IAAI,CAAC6B,iBAAiB,IAAI,CAAC,EAAC,GAC/B;cAAA;gBAAA/E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;gBAAC0K,OAAO,EAAC,OAAO;gBAACR,EAAE,EAAE;kBAAE1B,KAAK,EAAE,MAAM;kBAAEiC,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,EAAC;cAE1D;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZ+C,IAAI,CAACgC,2BAA2B,iBAC/BhJ,OAAA,CAACtE,UAAU;gBAAC0K,OAAO,EAAC,SAAS;gBAACR,EAAE,EAAE;kBAAE1B,KAAK,EAAE,MAAM;kBAAEgF,QAAQ,EAAE;gBAAU,CAAE;gBAAAnD,QAAA,GAAC,YAC9D,EAACiB,IAAI,CAACgC,2BAA2B,EAAC,6BAC9C;cAAA;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACL+C,IAAI,CAAC8B,cAAc,gBAClB9I,OAAA,CAACvE,GAAG;cAACmK,EAAE,EAAE;gBAAEuD,SAAS,EAAE,QAAQ;gBAAEtD,CAAC,EAAE,CAAC;gBAAEoB,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAnB,QAAA,gBAC1E/F,OAAA,CAACtE,UAAU;gBAAC0K,OAAO,EAAC,IAAI;gBAACR,EAAE,EAAE;kBAAEwB,UAAU,EAAE,GAAG;kBAAElD,KAAK,EAAE,SAAS;kBAAEiC,EAAE,EAAE;gBAAI,CAAE;gBAAAJ,QAAA,EACzEiB,IAAI,CAAC0C;cAAkB;gBAAA5F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACbjE,OAAA,CAACtE,UAAU;gBAAC0K,OAAO,EAAC,OAAO;gBAACR,EAAE,EAAE;kBAAE1B,KAAK,EAAE;gBAAU,CAAE;gBAAA6B,QAAA,GAAC,4BAC1B,EAACiB,IAAI,CAAC8B,cAAc,EAAC,SACjD;cAAA;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,gBAENjE,OAAA,CAACvE,GAAG;cAACmK,EAAE,EAAE;gBAAEuD,SAAS,EAAE,QAAQ;gBAAEtD,CAAC,EAAE,CAAC;gBAAEoB,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAnB,QAAA,eAC1E/F,OAAA,CAACtE,UAAU;gBAAC0K,OAAO,EAAC,OAAO;gBAACR,EAAE,EAAE;kBAAE1B,KAAK,EAAE;gBAAO,CAAE;gBAAA6B,QAAA,EAC/CiB,IAAI,CAAC6B,iBAAiB,GAAG,CAAC,GAAG,wBAAwB,GAAG;cAAuD;gBAAA/E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGN+C,IAAI,CAAC2C,YAAY,IAAI3C,IAAI,CAAC2C,YAAY,CAACC,MAAM,GAAG,CAAC,iBAChD5J,OAAA,CAACnE,IAAI;MAAC+J,EAAE,EAAE;QAAEuB,MAAM,EAAE;MAAoB,CAAE;MAAApB,QAAA,eACxC/F,OAAA,CAAClE,WAAW;QAAC8J,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAE,QAAA,gBACxB/F,OAAA,CAACvE,GAAG;UAACmK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,gBACxD/F,OAAA,CAACxB,aAAa;YAACoH,EAAE,EAAE;cAAE1B,KAAK,EAAE,SAAS;cAAEyD,EAAE,EAAE,CAAC;cAAEuB,QAAQ,EAAE;YAAG;UAAE;YAAApF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChEjE,OAAA,CAACtE,UAAU;YAAC0K,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEwB,UAAU,EAAE,GAAG;cAAElD,KAAK,EAAE;YAAU,CAAE;YAAA6B,QAAA,EAAC;UAEpE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNjE,OAAA,CAACpE,IAAI;UAACgM,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA9B,QAAA,EACxBiB,IAAI,CAAC2C,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7ChK,OAAA,CAACpE,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlC,QAAA,eAC9B/F,OAAA,CAACvE,GAAG;cAACmK,EAAE,EAAE;gBACPC,CAAC,EAAE,CAAC;gBACJsB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,CAAC;gBACfD,OAAO,EAAE+C,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;gBAC5CP,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE;kBACTxC,OAAO,EAAE,SAAS;kBAClBgD,SAAS,EAAE,kBAAkB;kBAC7BC,SAAS,EAAE;gBACb;cACF,CAAE;cAAAnE,QAAA,gBACA/F,OAAA,CAACtE,UAAU;gBAAC0K,OAAO,EAAC,OAAO;gBAACR,EAAE,EAAE;kBAAE1B,KAAK,EAAE,MAAM;kBAAEiC,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,EACtDgE,IAAI,CAAC/C;cAAI;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACbjE,OAAA,CAACtE,UAAU;gBAAC0K,OAAO,EAAC,IAAI;gBAACR,EAAE,EAAE;kBAAEwB,UAAU,EAAE,GAAG;kBAAElD,KAAK,EAAE;gBAAU,CAAE;gBAAA6B,QAAA,GAChEgE,IAAI,CAACI,KAAK,EAAC,GACd;cAAA;gBAAArG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZ+F,KAAK,KAAK,CAAC,iBACVhK,OAAA,CAAC/D,IAAI;gBACHyL,KAAK,EAAC,gBAAa;gBACnBjB,IAAI,EAAC,OAAO;gBACZb,EAAE,EAAE;kBACFE,EAAE,EAAE,CAAC;kBACLmB,OAAO,EAAE,SAAS;kBAClB/C,KAAK,EAAE,OAAO;kBACdgF,QAAQ,EAAE;gBACZ;cAAE;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GA/B8B+F,KAAK;YAAAlG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGN+C,IAAI,CAAC2C,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC3B5J,OAAA,CAACvE,GAAG;UAACmK,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAEqD,SAAS,EAAE;UAAS,CAAE;UAAApD,QAAA,eACtC/F,OAAA,CAAChD,SAAS;YAAA+I,QAAA,gBACR/F,OAAA,CAAC/C,gBAAgB;cAACmN,UAAU,eAAEpK,OAAA,CAAClB,cAAc;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAA8B,QAAA,eAC/C/F,OAAA,CAACtE,UAAU;gBAAC0K,OAAO,EAAC,OAAO;gBAACR,EAAE,EAAE;kBAAE1B,KAAK,EAAE;gBAAU,CAAE;gBAAA6B,QAAA,GAAC,iBACrC,EAACiB,IAAI,CAAC2C,YAAY,CAACC,MAAM,EAAC,SAC3C;cAAA;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACnBjE,OAAA,CAAC9C,gBAAgB;cAAA6I,QAAA,eACf/F,OAAA,CAACV,eAAe;gBACd0H,IAAI,EAAEA,IAAI,CAAC2C,YAAY,CAACG,GAAG,CAACC,IAAI,KAAK;kBACnC/C,IAAI,EAAE+C,IAAI,CAAC/C,IAAI;kBACfmD,KAAK,EAAE,GAAGJ,IAAI,CAACI,KAAK;gBACtB,CAAC,CAAC,CAAE;gBACJE,OAAO,EAAE,CACP;kBAAEC,KAAK,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEhB,KAAK,EAAE;gBAAI,CAAC,EACjD;kBAAEe,KAAK,EAAE,OAAO;kBAAEC,UAAU,EAAE,cAAc;kBAAEhB,KAAK,EAAE,GAAG;kBAAEiB,KAAK,EAAE;gBAAQ,CAAC,CAC1E;gBACFC,UAAU,EAAE,IAAK;gBACjBC,QAAQ,EAAE;cAAG;gBAAA5G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAM0C,eAAe,GAAIK,IAAI,iBAC3BhH,OAAA,CAACvE,GAAG;IAAAsK,QAAA,gBAEF/F,OAAA,CAACvE,GAAG;MAACmK,EAAE,EAAE;QACPI,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLN,CAAC,EAAE,CAAC;QACJoB,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE;MACV,CAAE;MAAApB,QAAA,gBACA/F,OAAA,CAAClC,QAAQ;QAAC8H,EAAE,EAAE;UAAE1B,KAAK,EAAE,SAAS;UAAEyD,EAAE,EAAE,CAAC;UAAEuB,QAAQ,EAAE;QAAG;MAAE;QAAApF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DjE,OAAA,CAACtE,UAAU;QAAC0K,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEwB,UAAU,EAAE,GAAG;UAAElD,KAAK,EAAE;QAAU,CAAE;QAAA6B,QAAA,EAAC;MAEpE;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGL7B,UAAU,iBACTpC,OAAA,CAACvE,GAAG;MAACmK,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACjB/F,OAAA,CAACJ,QAAQ;QAACoH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACN,eAGDjE,OAAA,CAACnE,IAAI;MAAC+J,EAAE,EAAE;QAAEO,EAAE,EAAE,CAAC;QAAEgB,MAAM,EAAE;MAAoB,CAAE;MAAApB,QAAA,eAC/C/F,OAAA,CAAClE,WAAW;QAAC8J,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAE,QAAA,gBACxB/F,OAAA,CAACvE,GAAG;UAACmK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,gBACxD/F,OAAA,CAACtB,SAAS;YAACkH,EAAE,EAAE;cAAE1B,KAAK,EAAE,SAAS;cAAEyD,EAAE,EAAE,CAAC;cAAEuB,QAAQ,EAAE;YAAG;UAAE;YAAApF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DjE,OAAA,CAACtE,UAAU;YAAC0K,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEwB,UAAU,EAAE,GAAG;cAAElD,KAAK,EAAE;YAAU,CAAE;YAAA6B,QAAA,EAAC;UAEpE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNjE,OAAA,CAACV,eAAe;UACd0H,IAAI,EAAEA,IAAI,CAAC2D,aAAa,IAAI,EAAG;UAC/BN,OAAO,EAAE,CACP;YAAEC,KAAK,EAAE,WAAW;YAAEC,UAAU,EAAE,WAAW;YAAEhB,KAAK,EAAE;UAAI,CAAC,EAC3D;YAAEe,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE,SAAS;YAAEhB,KAAK,EAAE;UAAI,CAAC,EACvD;YAAEe,KAAK,EAAE,UAAU;YAAEC,UAAU,EAAE,MAAM;YAAEhB,KAAK,EAAE,EAAE;YAAEiB,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE;UAAS,CAAC,EACxF;YAAEN,KAAK,EAAE,eAAe;YAAEC,UAAU,EAAE,eAAe;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE,QAAQ;YACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;UAAI,CAAC,EAChD;YAAET,KAAK,EAAE,aAAa;YAAEC,UAAU,EAAE,aAAa;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE,QAAQ;YAC/FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;UAAI,CAAC,EAC9C;YAAEV,KAAK,EAAE,iBAAiB;YAAEC,UAAU,EAAE,WAAW;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE,QAAQ;YACjGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACnC,eAAe;UAAI,CAAC,CAClD;UACF+B,QAAQ,EAAE;QAAG;UAAA5G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjE,OAAA,CAACnE,IAAI;MAAC+J,EAAE,EAAE;QAAEuB,MAAM,EAAE;MAAoB,CAAE;MAAApB,QAAA,eACxC/F,OAAA,CAAClE,WAAW;QAAC8J,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAE,QAAA,gBACxB/F,OAAA,CAACvE,GAAG;UAACmK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,gBACxD/F,OAAA,CAACpB,aAAa;YAACgH,EAAE,EAAE;cAAE1B,KAAK,EAAE,SAAS;cAAEyD,EAAE,EAAE,CAAC;cAAEuB,QAAQ,EAAE;YAAG;UAAE;YAAApF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChEjE,OAAA,CAACtE,UAAU;YAAC0K,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEwB,UAAU,EAAE,GAAG;cAAElD,KAAK,EAAE;YAAU,CAAE;YAAA6B,QAAA,EAAC;UAEpE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNjE,OAAA,CAACV,eAAe;UACd0H,IAAI,EAAEA,IAAI,CAACiE,eAAe,IAAI,EAAG;UACjCZ,OAAO,EAAE,CACP;YAAEC,KAAK,EAAE,WAAW;YAAEC,UAAU,EAAE,WAAW;YAAEhB,KAAK,EAAE;UAAI,CAAC,EAC3D;YAAEe,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE,SAAS;YAAEhB,KAAK,EAAE;UAAI,CAAC,EACvD;YAAEe,KAAK,EAAE,YAAY;YAAEC,UAAU,EAAE,QAAQ;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE;UAAS,CAAC,EAC7F;YAAEN,KAAK,EAAE,mBAAmB;YAAEC,UAAU,EAAE,mBAAmB;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE,QAAQ;YAC3GC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACI,iBAAiB;UAAI,CAAC,CACpD;UACFR,QAAQ,EAAE;QAAG;UAAA5G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAED,MAAM2C,kBAAkB,GAAII,IAAI,iBAC9BhH,OAAA,CAACvE,GAAG;IAAAsK,QAAA,gBAEF/F,OAAA,CAACvE,GAAG;MAACmK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,UAAU;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACpF/F,OAAA,CAAC5C,gBAAgB;QACfiK,OAAO,eACLrH,OAAA,CAAC7C,MAAM;UACLmK,OAAO,EAAElF,UAAW;UACpBmF,QAAQ,EAAGC,CAAC,IAAKnF,aAAa,CAACmF,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDpD,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDyD,KAAK,eACH1H,OAAA,CAACvE,GAAG;UAACmK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjD/F,OAAA,CAAChB,aAAa;YAAC4G,EAAE,EAAE;cAAE+B,EAAE,EAAE;YAAE;UAAE;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL7B,UAAU,iBACTpC,OAAA,CAACvE,GAAG;MAACmK,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACjB/F,OAAA,CAACL,WAAW;QAACqH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACN,eAGDjE,OAAA,CAACrE,KAAK;MAACiK,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEsB,MAAM,EAAE;MAAoB,CAAE;MAAApB,QAAA,gBAC/C/F,OAAA,CAACtE,UAAU;QAAC0K,OAAO,EAAC,WAAW;QAACR,EAAE,EAAE;UAAEwB,UAAU,EAAE,GAAG;UAAEjB,EAAE,EAAE,CAAC;UAAEjC,KAAK,EAAE;QAAU,CAAE;QAAA6B,QAAA,EAAC;MAElF;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACV,eAAe;QACd0H,IAAI,EAAEA,IAAI,CAAChF,MAAM,IAAI,EAAG;QACxBqI,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,WAAW;UAAEC,UAAU,EAAE,WAAW;UAAEhB,KAAK,EAAE;QAAI,CAAC,EAC3D;UAAEe,KAAK,EAAE,WAAW;UAAEC,UAAU,EAAE,WAAW;UAAEhB,KAAK,EAAE;QAAI,CAAC,EAC3D;UAAEe,KAAK,EAAE,SAAS;UAAEC,UAAU,EAAE,SAAS;UAAEhB,KAAK,EAAE;QAAI,CAAC,EACvD;UAAEe,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE,OAAO;UAAEhB,KAAK,EAAE,GAAG;UAC/CsB,UAAU,EAAGC,GAAG,iBACd9K,OAAA,CAAC/D,IAAI;YACHyL,KAAK,EAAEoD,GAAG,CAACK,KAAM;YACjBjH,KAAK,EAAE4G,GAAG,CAACK,KAAK,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;YAC3D1E,IAAI,EAAC,OAAO;YACZb,EAAE,EAAE;cACFqB,OAAO,EAAE6D,GAAG,CAACK,KAAK,KAAK,aAAa,GAAG,SAAS,GAAG,SAAS;cAC5DjH,KAAK,EAAE;YACT;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAEL,CAAC,EACD;UAAEqG,KAAK,EAAE,cAAc;UAAEC,UAAU,EAAE,cAAc;UAAEhB,KAAK,EAAE,GAAG;UAAEiB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UACjGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAAC3C,YAAY;QAAI,CAAC,EAC/C;UAAEmC,KAAK,EAAE,eAAe;UAAEC,UAAU,EAAE,eAAe;UAAEhB,KAAK,EAAE,GAAG;UAAEiB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACM,aAAa;QAAI,CAAC,EAChD;UAAEd,KAAK,EAAE,kBAAkB;UAAEC,UAAU,EAAE,kBAAkB;UAAEhB,KAAK,EAAE,GAAG;UAAEiB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UACzGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACO,gBAAgB;QAAI,CAAC,EACnD;UAAEf,KAAK,EAAE,sBAAsB;UAAEC,UAAU,EAAE,UAAU;UAAEhB,KAAK,EAAE,GAAG;UAAEiB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UACrGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACQ,oBAAoB;QAAI,CAAC,CACvD;QACFZ,QAAQ,EAAE;MAAG;QAAA5G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;EAED,MAAM4C,2BAA2B,GAAIG,IAAI;IAAA,IAAAuE,YAAA;IAAA,oBACvCvL,OAAA,CAACvE,GAAG;MAAAsK,QAAA,gBAEF/F,OAAA,CAACtE,UAAU;QAAC0K,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEwB,UAAU,EAAE,GAAG;UAAElD,KAAK,EAAE,WAAW;UAAEiC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,GAAC,4BACjD,GAAAwF,YAAA,GAACvE,IAAI,CAACwE,MAAM,cAAAD,YAAA,uBAAXA,YAAA,CAAa5J,SAAS;MAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAEbjE,OAAA,CAACpE,IAAI;QAACgM,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA9B,QAAA,gBAEzB/F,OAAA,CAACpE,IAAI;UAACkM,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAlC,QAAA,eACvB/F,OAAA,CAACrE,KAAK;YAACiK,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBAClB/F,OAAA,CAACtE,UAAU;cAAC0K,OAAO,EAAC,IAAI;cAACR,EAAE,EAAE;gBAAEO,EAAE,EAAE,CAAC;gBAAEiB,UAAU,EAAE;cAAI,CAAE;cAAArB,QAAA,EAAC;YAEzD;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZ+C,IAAI,CAACwE,MAAM,iBACVxL,OAAA,CAACvE,GAAG;cAAAsK,QAAA,gBACF/F,OAAA,CAACvE,GAAG;gBAACmK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAU;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnDjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEwB,UAAU,EAAE;kBAAI,CAAE;kBAAArB,QAAA,EAAEiB,IAAI,CAACwE,MAAM,CAAC7J;gBAAS;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eACNjE,OAAA,CAACvE,GAAG;gBAACmK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAU;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnDjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEwB,UAAU,EAAE;kBAAI,CAAE;kBAAArB,QAAA,EAAEiB,IAAI,CAACwE,MAAM,CAACC;gBAAS;kBAAA3H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eACNjE,OAAA,CAACvE,GAAG;gBAACmK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAQ;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjDjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEwB,UAAU,EAAE;kBAAI,CAAE;kBAAArB,QAAA,EAAEiB,IAAI,CAACwE,MAAM,CAACE;gBAAO;kBAAA5H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC,eACNjE,OAAA,CAACvE,GAAG;gBAACmK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAM;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/CjE,OAAA,CAAC/D,IAAI;kBACHyL,KAAK,EAAEV,IAAI,CAACwE,MAAM,CAACL,KAAM;kBACzBjH,KAAK,EAAE8C,IAAI,CAACwE,MAAM,CAACL,KAAK,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;kBACnE1E,IAAI,EAAC;gBAAO;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPjE,OAAA,CAACpE,IAAI;UAACkM,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAlC,QAAA,eACvB/F,OAAA,CAACrE,KAAK;YAACiK,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBAClB/F,OAAA,CAACtE,UAAU;cAAC0K,OAAO,EAAC,IAAI;cAACR,EAAE,EAAE;gBAAEO,EAAE,EAAE,CAAC;gBAAEiB,UAAU,EAAE;cAAI,CAAE;cAAArB,QAAA,EAAC;YAEzD;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZ+C,IAAI,CAACwE,MAAM,iBACVxL,OAAA,CAACvE,GAAG;cAAAsK,QAAA,gBACF/F,OAAA,CAACvE,GAAG;gBAACmK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAa;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtDjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEwB,UAAU,EAAE;kBAAI,CAAE;kBAAArB,QAAA,GAAEiB,IAAI,CAACwE,MAAM,CAACrD,YAAY,EAAC,GAAC;gBAAA;kBAAArE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1F,CAAC,eACNjE,OAAA,CAACvE,GAAG;gBAACmK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAiB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1DjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEwB,UAAU,EAAE,GAAG;oBAAElD,KAAK,EAAE;kBAAe,CAAE;kBAAA6B,QAAA,GACxEiB,IAAI,CAACwE,MAAM,CAACH,gBAAgB,EAAC,GAChC;gBAAA;kBAAAvH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjE,OAAA,CAACvE,GAAG;gBAACmK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAc;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvDjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEwB,UAAU,EAAE,GAAG;oBAAElD,KAAK,EAAE;kBAAe,CAAE;kBAAA6B,QAAA,GACxEiB,IAAI,CAACwE,MAAM,CAACJ,aAAa,EAAC,GAC7B;gBAAA;kBAAAtH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjE,OAAA,CAACvE,GAAG;gBAACmK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE;gBAAgB,CAAE;gBAAAF,QAAA,gBAC5D/F,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAqB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9DjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEwB,UAAU,EAAE;kBAAI,CAAE;kBAAArB,QAAA,GAAEiB,IAAI,CAACwE,MAAM,CAACF,oBAAoB,EAAC,GAAC;gBAAA;kBAAAxH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPjE,OAAA,CAACpE,IAAI;UAACkM,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAhC,QAAA,eAChB/F,OAAA,CAACrE,KAAK;YAACiK,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBAClB/F,OAAA,CAACtE,UAAU;cAAC0K,OAAO,EAAC,IAAI;cAACR,EAAE,EAAE;gBAAEO,EAAE,EAAE,CAAC;gBAAEiB,UAAU,EAAE;cAAI,CAAE;cAAArB,QAAA,GAAC,kBACvC,EAACiB,IAAI,CAACoC,WAAW,EAAC,GACpC;YAAA;cAAAtF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjE,OAAA,CAACV,eAAe;cACd0H,IAAI,EAAEA,IAAI,CAAC2E,cAAc,IAAI,EAAG;cAChCtB,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,SAAS;gBAAEC,UAAU,EAAE,SAAS;gBAAEhB,KAAK,EAAE;cAAI,CAAC,EACvD;gBAAEe,KAAK,EAAE,SAAS;gBAAEC,UAAU,EAAE,SAAS;gBAAEhB,KAAK,EAAE;cAAI,CAAC,EACvD;gBAAEe,KAAK,EAAE,SAAS;gBAAEC,UAAU,EAAE,SAAS;gBAAEhB,KAAK,EAAE;cAAI,CAAC,EACvD;gBAAEe,KAAK,EAAE,WAAW;gBAAEC,UAAU,EAAE,WAAW;gBAAEhB,KAAK,EAAE;cAAI,CAAC,EAC3D;gBAAEe,KAAK,EAAE,eAAe;gBAAEC,UAAU,EAAE,eAAe;gBAAEhB,KAAK,EAAE,GAAG;gBAAEiB,KAAK,EAAE,OAAO;gBAAEI,QAAQ,EAAE,QAAQ;gBACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;cAAI,CAAC,EAChD;gBAAET,KAAK,EAAE,aAAa;gBAAEC,UAAU,EAAE,aAAa;gBAAEhB,KAAK,EAAE,GAAG;gBAAEiB,KAAK,EAAE,OAAO;gBAAEI,QAAQ,EAAE,QAAQ;gBAC/FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;cAAI,CAAC,EAC9C;gBAAEV,KAAK,EAAE,OAAO;gBAAEC,UAAU,EAAE,OAAO;gBAAEhB,KAAK,EAAE,GAAG;gBAC/CsB,UAAU,EAAGC,GAAG,iBACd9K,OAAA,CAAC/D,IAAI;kBACHyL,KAAK,EAAEoD,GAAG,CAACK,KAAM;kBACjBjH,KAAK,EAAE4G,GAAG,CAACK,KAAK,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAU;kBACtD1E,IAAI,EAAC;gBAAO;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAEL,CAAC,CACD;cACFyG,QAAQ,EAAE;YAAG;cAAA5G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,CACP;EAED,MAAM6C,uBAAuB,GAAIE,IAAI,iBACnChH,OAAA,CAACvE,GAAG;IAAAsK,QAAA,gBAEF/F,OAAA,CAACvE,GAAG;MAACmK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF/F,OAAA,CAACtE,UAAU;QAAC0K,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEwB,UAAU,EAAE,GAAG;UAAElD,KAAK,EAAE;QAAe,CAAE;QAAA6B,QAAA,EAAC;MAEzE;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAAC5C,gBAAgB;QACfiK,OAAO,eACLrH,OAAA,CAAC7C,MAAM;UACLmK,OAAO,EAAElF,UAAW;UACpBmF,QAAQ,EAAGC,CAAC,IAAKnF,aAAa,CAACmF,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDpD,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDyD,KAAK,eACH1H,OAAA,CAACvE,GAAG;UAACmK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjD/F,OAAA,CAAChB,aAAa;YAAC4G,EAAE,EAAE;cAAE+B,EAAE,EAAE;YAAE;UAAE;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNjE,OAAA,CAACpE,IAAI;MAACgM,SAAS;MAACC,OAAO,EAAE,CAAE;MAACjC,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxC/F,OAAA,CAACpE,IAAI;QAACkM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAlC,QAAA,eACvB/F,OAAA,CAACrE,KAAK;UAACiK,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEsD,SAAS,EAAE,QAAQ;YAAElC,OAAO,EAAE,cAAc;YAAE/C,KAAK,EAAE;UAAQ,CAAE;UAAA6B,QAAA,gBAChF/F,OAAA,CAACtE,UAAU;YAAC0K,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEwB,UAAU,EAAE,MAAM;cAAEjB,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxDiB,IAAI,CAAC4E,oBAAoB,EAAC,GAC7B;UAAA;YAAA9H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;YAAC0K,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAY;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrDjE,OAAA,CAACtE,UAAU;YAAC0K,OAAO,EAAC,SAAS;YAAAL,QAAA,GAAEiB,IAAI,CAACvF,WAAW,EAAC,KAAG,EAACuF,IAAI,CAACtF,SAAS;UAAA;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPjE,OAAA,CAACpE,IAAI;QAACkM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAlC,QAAA,eACvB/F,OAAA,CAACrE,KAAK;UAACiK,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEsD,SAAS,EAAE,QAAQ;YAAElC,OAAO,EAAE,WAAW;YAAE/C,KAAK,EAAE;UAAQ,CAAE;UAAA6B,QAAA,gBAC7E/F,OAAA,CAACtE,UAAU;YAAC0K,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEwB,UAAU,EAAE,MAAM;cAAEjB,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,EACxDiB,IAAI,CAAC6E;UAAa;YAAA/H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACbjE,OAAA,CAACtE,UAAU;YAAC0K,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAa;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPjE,OAAA,CAACpE,IAAI;QAACkM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAlC,QAAA,eACvB/F,OAAA,CAACrE,KAAK;UAACiK,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEsD,SAAS,EAAE,QAAQ;YAAElC,OAAO,EAAE,cAAc;YAAE/C,KAAK,EAAE;UAAQ,CAAE;UAAA6B,QAAA,gBAChF/F,OAAA,CAACtE,UAAU;YAAC0K,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEwB,UAAU,EAAE,MAAM;cAAEjB,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxDiB,IAAI,CAAC6B,iBAAiB,EAAC,GAC1B;UAAA;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;YAAC0K,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAY;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPjE,OAAA,CAACpE,IAAI;QAACkM,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAlC,QAAA,eACvB/F,OAAA,CAACrE,KAAK;UAACiK,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEsD,SAAS,EAAE,QAAQ;YAAElC,OAAO,EAAE,cAAc;YAAE/C,KAAK,EAAE;UAAQ,CAAE;UAAA6B,QAAA,gBAChF/F,OAAA,CAACtE,UAAU;YAAC0K,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEwB,UAAU,EAAE,MAAM;cAAEjB,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxD+F,IAAI,CAACC,KAAK,CAAC/E,IAAI,CAAC4E,oBAAoB,GAAG5E,IAAI,CAAC6E,aAAa,GAAG,CAAC,CAAC,EAAC,GAClE;UAAA;YAAA/H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;YAAC0K,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAe;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGN7B,UAAU,iBACTpC,OAAA,CAACvE,GAAG;MAACmK,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACjB/F,OAAA,CAACH,aAAa;QAACmH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDjE,OAAA,CAACrE,KAAK;MAACiK,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAE,QAAA,gBAClB/F,OAAA,CAACtE,UAAU;QAAC0K,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAEiB,UAAU,EAAE;QAAI,CAAE;QAAArB,QAAA,EAAC;MAEzD;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACV,eAAe;QACd0H,IAAI,EAAEA,IAAI,CAACgF,gBAAgB,IAAI,EAAG;QAClC3B,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,MAAM;UAAEC,UAAU,EAAE,MAAM;UAAEhB,KAAK,EAAE;QAAI,CAAC,EACjD;UAAEe,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE,cAAc;UAAEhB,KAAK,EAAE,GAAG;UAAEiB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UAC1FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACX,KAAK;QAAI,CAAC,CACxC;QACFO,QAAQ,EAAE;MAAG;QAAA5G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;EAED,MAAM8C,qBAAqB,GAAIC,IAAI,iBACjChH,OAAA,CAACvE,GAAG;IAAAsK,QAAA,gBAEF/F,OAAA,CAACvE,GAAG;MAACmK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,UAAU;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACpF/F,OAAA,CAAC5C,gBAAgB;QACfiK,OAAO,eACLrH,OAAA,CAAC7C,MAAM;UACLmK,OAAO,EAAElF,UAAW;UACpBmF,QAAQ,EAAGC,CAAC,IAAKnF,aAAa,CAACmF,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDpD,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDyD,KAAK,eACH1H,OAAA,CAACvE,GAAG;UAACmK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjD/F,OAAA,CAAChB,aAAa;YAAC4G,EAAE,EAAE;cAAE+B,EAAE,EAAE;YAAE;UAAE;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL7B,UAAU,iBACTpC,OAAA,CAACrE,KAAK;MAACiK,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEM,EAAE,EAAE,CAAC;QAAEgB,MAAM,EAAE;MAAoB,CAAE;MAAApB,QAAA,gBACtD/F,OAAA,CAACtE,UAAU;QAAC0K,OAAO,EAAC,WAAW;QAACR,EAAE,EAAE;UAAEwB,UAAU,EAAE,GAAG;UAAEjB,EAAE,EAAE,CAAC;UAAEjC,KAAK,EAAE;QAAU,CAAE;QAAA6B,QAAA,EAAC;MAElF;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACvE,GAAG;QAACmK,EAAE,EAAE;UACPuB,MAAM,EAAE,mBAAmB;UAC3BD,YAAY,EAAE,CAAC;UACfsC,QAAQ,EAAE;QACZ,CAAE;QAAAzD,QAAA,eACA/F,OAAA;UAAOiM,KAAK,EAAE;YAAE1C,KAAK,EAAE,MAAM;YAAE2C,cAAc,EAAE;UAAW,CAAE;UAAAnG,QAAA,gBAC1D/F,OAAA;YAAA+F,QAAA,eACE/F,OAAA;cAAIiM,KAAK,EAAE;gBAAEE,eAAe,EAAE;cAAU,CAAE;cAAApG,QAAA,gBACxC/F,OAAA;gBAAIiM,KAAK,EAAE;kBACTG,OAAO,EAAE,UAAU;kBACnBjD,SAAS,EAAE,MAAM;kBACjBD,QAAQ,EAAE,MAAM;kBAChB9B,UAAU,EAAE,GAAG;kBACflD,KAAK,EAAE,SAAS;kBAChBmI,YAAY,EAAE,mBAAmB;kBACjC9C,KAAK,EAAE;gBACT,CAAE;gBAAAxD,QAAA,EAAC;cAAK;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbjE,OAAA;gBAAIiM,KAAK,EAAE;kBACTG,OAAO,EAAE,UAAU;kBACnBjD,SAAS,EAAE,QAAQ;kBACnBD,QAAQ,EAAE,MAAM;kBAChB9B,UAAU,EAAE,GAAG;kBACflD,KAAK,EAAE,SAAS;kBAChBmI,YAAY,EAAE,mBAAmB;kBACjC9C,KAAK,EAAE;gBACT,CAAE;gBAAAxD,QAAA,EAAC;cAAW;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBjE,OAAA;gBAAIiM,KAAK,EAAE;kBACTG,OAAO,EAAE,UAAU;kBACnBjD,SAAS,EAAE,QAAQ;kBACnBD,QAAQ,EAAE,MAAM;kBAChB9B,UAAU,EAAE,GAAG;kBACflD,KAAK,EAAE,SAAS;kBAChBmI,YAAY,EAAE,mBAAmB;kBACjC9C,KAAK,EAAE;gBACT,CAAE;gBAAAxD,QAAA,EAAC;cAAa;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBjE,OAAA;gBAAIiM,KAAK,EAAE;kBACTG,OAAO,EAAE,UAAU;kBACnBjD,SAAS,EAAE,QAAQ;kBACnBD,QAAQ,EAAE,MAAM;kBAChB9B,UAAU,EAAE,GAAG;kBACflD,KAAK,EAAE,SAAS;kBAChBmI,YAAY,EAAE,mBAAmB;kBACjC9C,KAAK,EAAE;gBACT,CAAE;gBAAAxD,QAAA,EAAC;cAAa;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRjE,OAAA;YAAA+F,QAAA,EACG,CAACiB,IAAI,CAACsF,cAAc,IAAI,EAAE,EAAExC,GAAG,CAAC,CAAChC,IAAI,EAAEkC,KAAK,KAAK;cAChD,MAAMuC,SAAS,GAAG,CAACvF,IAAI,CAACsF,cAAc,IAAI,EAAE,EAAEE,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,GAAGC,CAAC,CAACC,QAAQ,EAAE,CAAC,CAAC;cACrF,MAAMC,UAAU,GAAGL,SAAS,GAAG,CAAC,GAAGT,IAAI,CAACC,KAAK,CAAEjE,IAAI,CAAC6E,QAAQ,GAAGJ,SAAS,GAAI,GAAG,CAAC,GAAG,CAAC;cACpF,oBACEvM,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA;kBAAIiM,KAAK,EAAE;oBACTG,OAAO,EAAE,MAAM;oBACflD,QAAQ,EAAE,MAAM;oBAChBmD,YAAY,EAAErC,KAAK,GAAGhD,IAAI,CAACsF,cAAc,CAAC1C,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG;kBAC/E,CAAE;kBAAA7D,QAAA,eACA/F,OAAA,CAAC/D,IAAI;oBACHyL,KAAK,EAAEI,IAAI,CAACqD,KAAM;oBAClB1E,IAAI,EAAC,OAAO;oBACZb,EAAE,EAAE;sBACFqB,OAAO,EAAEa,IAAI,CAACqD,KAAK,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS;sBAC5DjH,KAAK,EAAE;oBACT;kBAAE;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACLjE,OAAA;kBAAIiM,KAAK,EAAE;oBACTG,OAAO,EAAE,MAAM;oBACflD,QAAQ,EAAE,MAAM;oBAChBC,SAAS,EAAE,QAAQ;oBACnB/B,UAAU,EAAE,GAAG;oBACfiF,YAAY,EAAErC,KAAK,GAAGhD,IAAI,CAACsF,cAAc,CAAC1C,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG;kBAC/E,CAAE;kBAAA7D,QAAA,EAAE+B,IAAI,CAAC6E;gBAAQ;kBAAA7I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvBjE,OAAA;kBAAIiM,KAAK,EAAE;oBACTG,OAAO,EAAE,MAAM;oBACflD,QAAQ,EAAE,MAAM;oBAChBC,SAAS,EAAE,QAAQ;oBACnB/B,UAAU,EAAE,GAAG;oBACfiF,YAAY,EAAErC,KAAK,GAAGhD,IAAI,CAACsF,cAAc,CAAC1C,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG;kBAC/E,CAAE;kBAAA7D,QAAA,GAAE+B,IAAI,CAACiD,aAAa,EAAC,GAAC;gBAAA;kBAAAjH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BjE,OAAA;kBAAIiM,KAAK,EAAE;oBACTG,OAAO,EAAE,MAAM;oBACflD,QAAQ,EAAE,MAAM;oBAChBC,SAAS,EAAE,QAAQ;oBACnBkD,YAAY,EAAErC,KAAK,GAAGhD,IAAI,CAACsF,cAAc,CAAC1C,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG;kBAC/E,CAAE;kBAAA7D,QAAA,eACA/F,OAAA,CAACvE,GAAG;oBAACmK,EAAE,EAAE;sBAAEI,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAED,cAAc,EAAE,QAAQ;sBAAEK,GAAG,EAAE;oBAAE,CAAE;oBAAAP,QAAA,gBACnF/F,OAAA,CAACvE,GAAG;sBAACmK,EAAE,EAAE;wBACP2D,KAAK,EAAE,MAAM;wBACbN,MAAM,EAAE,KAAK;wBACbhC,OAAO,EAAE,SAAS;wBAClBC,YAAY,EAAE,KAAK;wBACnB2F,QAAQ,EAAE;sBACZ,CAAE;sBAAA9G,QAAA,eACA/F,OAAA,CAACvE,GAAG;wBAACmK,EAAE,EAAE;0BACP2D,KAAK,EAAE,GAAGqD,UAAU,GAAG;0BACvB3D,MAAM,EAAE,MAAM;0BACdhC,OAAO,EAAEa,IAAI,CAACqD,KAAK,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS;0BAC5DjE,YAAY,EAAE;wBAChB;sBAAE;wBAAApD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACNjE,OAAA,CAACtE,UAAU;sBAAC0K,OAAO,EAAC,SAAS;sBAACR,EAAE,EAAE;wBAAEwB,UAAU,EAAE;sBAAI,CAAE;sBAAArB,QAAA,GACnD6G,UAAU,EAAC,GACd;oBAAA;sBAAA9I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAtDE+F,KAAK;gBAAAlG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuDV,CAAC;YAET,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGDjE,OAAA,CAACrE,KAAK;MAACiK,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEsB,MAAM,EAAE;MAAoB,CAAE;MAAApB,QAAA,gBAC/C/F,OAAA,CAACtE,UAAU;QAAC0K,OAAO,EAAC,WAAW;QAACR,EAAE,EAAE;UAAEwB,UAAU,EAAE,GAAG;UAAEjB,EAAE,EAAE,CAAC;UAAEjC,KAAK,EAAE;QAAU,CAAE;QAAA6B,QAAA,EAAC;MAElF;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACV,eAAe;QACd0H,IAAI,EAAEA,IAAI,CAACsF,cAAc,IAAI,EAAG;QAChCjC,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE,OAAO;UAAEhB,KAAK,EAAE,GAAG;UAC/CsB,UAAU,EAAGC,GAAG,iBACd9K,OAAA,CAAC/D,IAAI;YACHyL,KAAK,EAAEoD,GAAG,CAACK,KAAM;YACjB1E,IAAI,EAAC,OAAO;YACZb,EAAE,EAAE;cACFqB,OAAO,EAAE6D,GAAG,CAACK,KAAK,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS;cAC3DjH,KAAK,EAAE;YACT;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAEL,CAAC,EACD;UAAEqG,KAAK,EAAE,UAAU;UAAEC,UAAU,EAAE,aAAa;UAAEhB,KAAK,EAAE,GAAG;UAAEiB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE;QAAS,CAAC,EAChG;UAAEN,KAAK,EAAE,eAAe;UAAEC,UAAU,EAAE,eAAe;UAAEhB,KAAK,EAAE,GAAG;UAAEiB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;QAAI,CAAC,EAChD;UAAET,KAAK,EAAE,aAAa;UAAEC,UAAU,EAAE,aAAa;UAAEhB,KAAK,EAAE,GAAG;UAAEiB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UAC/FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;QAAI,CAAC,CAC9C;QACFP,UAAU,EAAE;MAAM;QAAA3G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;EAED,MAAM6I,YAAY,GAAGA,CAAA,kBACnB9M,OAAA,CAACzD,MAAM;IAACuI,IAAI,EAAE9D,UAAW;IAAC+L,OAAO,EAAErH,iBAAkB;IAACsH,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAlH,QAAA,gBAC3E/F,OAAA,CAACxD,WAAW;MAAAuJ,QAAA,EACTjF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6C;IAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACdjE,OAAA,CAACvD,aAAa;MAAAsJ,QAAA,GACXrF,KAAK,iBACJV,OAAA,CAAC9D,KAAK;QAACgR,QAAQ,EAAC,OAAO;QAACtH,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EACnCrF;MAAK;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDjE,OAAA,CAACpE,IAAI;QAACgM,SAAS;QAACC,OAAO,EAAE,CAAE;QAACjC,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACxC/F,OAAA,CAACpE,IAAI;UAACkM,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAhC,QAAA,eAChB/F,OAAA,CAACrD,WAAW;YAACsQ,SAAS;YAAAlH,QAAA,gBACpB/F,OAAA,CAACpD,UAAU;cAAAmJ,QAAA,EAAC;YAAO;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChCjE,OAAA,CAACnD,MAAM;cACLqL,KAAK,EAAE5G,QAAQ,CAACE,OAAQ;cACxBkG,KAAK,EAAC,SAAS;cACfH,QAAQ,EAAGC,CAAC,IAAKjG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAEgG,CAAC,CAACC,MAAM,CAACS;cAAM,CAAC,CAAE;cAAAnC,QAAA,gBAEvE/F,OAAA,CAAClD,QAAQ;gBAACoL,KAAK,EAAC,OAAO;gBAAAnC,QAAA,EAAC;cAAoB;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvDjE,OAAA,CAAClD,QAAQ;gBAACoL,KAAK,EAAC,KAAK;gBAAAnC,QAAA,EAAC;cAAY;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7CjE,OAAA,CAAClD,QAAQ;gBAACoL,KAAK,EAAC,OAAO;gBAAAnC,QAAA,EAAC;cAAc;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAEN/C,UAAU,KAAK,kBAAkB,iBAChClB,OAAA,CAACpE,IAAI;UAACkM,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAhC,QAAA,eAChB/F,OAAA,CAACjD,SAAS;YACRkQ,SAAS;YACTvF,KAAK,EAAC,WAAW;YACjBQ,KAAK,EAAE5G,QAAQ,CAACK,SAAU;YAC1B4F,QAAQ,EAAGC,CAAC,IAAKjG,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEK,SAAS,EAAE6F,CAAC,CAACC,MAAM,CAACS;YAAM,CAAC,CAAE;YACzEiF,WAAW,EAAC,mBAAmB;YAC/BC,UAAU,EAAC;UAA0D;YAAAtJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP,EAEA/C,UAAU,KAAK,cAAc,iBAC5BlB,OAAA,CAAAE,SAAA;UAAA6F,QAAA,gBACE/F,OAAA,CAACpE,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAhC,QAAA,eACf/F,OAAA,CAACjD,SAAS;cACRkQ,SAAS;cACTI,IAAI,EAAC,MAAM;cACX3F,KAAK,EAAC,aAAa;cACnBQ,KAAK,EAAE5G,QAAQ,CAACG,WAAY;cAC5B8F,QAAQ,EAAGC,CAAC,IAAKjG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAE+F,CAAC,CAACC,MAAM,CAACS;cAAM,CAAC,CAAE;cAC3EoF,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAzJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjE,OAAA,CAACpE,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAhC,QAAA,eACf/F,OAAA,CAACjD,SAAS;cACRkQ,SAAS;cACTI,IAAI,EAAC,MAAM;cACX3F,KAAK,EAAC,WAAW;cACjBQ,KAAK,EAAE5G,QAAQ,CAACI,SAAU;cAC1B6F,QAAQ,EAAGC,CAAC,IAAKjG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAE8F,CAAC,CAACC,MAAM,CAACS;cAAM,CAAC,CAAE;cACzEoF,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAAzJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChBjE,OAAA,CAACtD,aAAa;MAAAqJ,QAAA,gBACZ/F,OAAA,CAAChE,MAAM;QAACwK,OAAO,EAAEd,iBAAkB;QAAAK,QAAA,EAAC;MAAO;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpDjE,OAAA,CAAChE,MAAM;QACLwK,OAAO,EAAEf,oBAAqB;QAC9BW,OAAO,EAAC,WAAW;QACnBoH,QAAQ,EAAEhN,OAAQ;QAClB+F,SAAS,EAAE/F,OAAO,gBAAGR,OAAA,CAAC7D,gBAAgB;UAACsK,IAAI,EAAE;QAAG;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGjE,OAAA,CAAC9B,cAAc;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAA8B,QAAA,EAExEvF,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACEjE,OAAA,CAACvE,GAAG;IAACgS,SAAS,EAAC,sCAAsC;IAAA1H,QAAA,gBAEnD/F,OAAA,CAACvE,GAAG;MAACmK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,UAAU;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACpF/F,OAAA,CAACZ,eAAe;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGLzD,OAAO,iBACNR,OAAA,CAACvE,GAAG;MAACmK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEyH,EAAE,EAAE;MAAE,CAAE;MAAA3H,QAAA,eAC5D/F,OAAA,CAAC7D,gBAAgB;QAAA2H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGDjE,OAAA,CAACvE,GAAG;MAACmK,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAEjB/F,OAAA,CAACvE,GAAG;QAACmK,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACjB/F,OAAA,CAACtE,UAAU;UAAC0K,OAAO,EAAC,IAAI;UAACR,EAAE,EAAE;YAAEwB,UAAU,EAAE,GAAG;YAAElD,KAAK,EAAE,SAAS;YAAEiC,EAAE,EAAE,CAAC;YAAEgD,SAAS,EAAE;UAAS,CAAE;UAAApD,QAAA,EAAC;QAEhG;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjE,OAAA,CAACpE,IAAI;UAACgM,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA9B,QAAA,gBAEzB/F,OAAA,CAACpE,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlC,QAAA,eAC7B/F,OAAA,CAACnE,IAAI;cACH4R,SAAS,EAAE,eAAerM,kBAAkB,KAAK,UAAU,GAAG,sBAAsB,GAAG,EAAE,EAAG;cAC5FwE,EAAE,EAAE;gBACFqD,MAAM,EAAE,OAAO;gBACf0E,MAAM,EAAE,SAAS;gBACjBxG,MAAM,EAAE/F,kBAAkB,KAAK,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;gBACrF6F,OAAO,EAAE7F,kBAAkB,KAAK,UAAU,GAAG,SAAS,GAAG,OAAO;gBAChEqI,UAAU,EAAE;cACd,CAAE;cACFjD,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,UAAU,CAAE;cAAA0E,QAAA,eAEjD/F,OAAA,CAAClE,WAAW;gBAAC8J,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEsD,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAEjD,OAAO,EAAE,MAAM;kBAAE4H,aAAa,EAAE,QAAQ;kBAAE3H,cAAc,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBACjI/F,OAAA,CAAC1C,cAAc;kBAACsI,EAAE,EAAE;oBAAEsD,QAAQ,EAAE,EAAE;oBAAEhF,KAAK,EAAE,SAAS;oBAAEiC,EAAE,EAAE;kBAAE;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjEjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,WAAW;kBAACR,EAAE,EAAE;oBAAEwB,UAAU,EAAE,GAAG;oBAAEjB,EAAE,EAAE,GAAG;oBAAE+C,QAAQ,EAAE;kBAAS,CAAE;kBAAAnD,QAAA,EAAC;gBAEtF;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAE1B,KAAK,EAAE,MAAM;oBAAEgF,QAAQ,EAAE;kBAAS,CAAE;kBAAAnD,QAAA,EAAC;gBAEvE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPjE,OAAA,CAACpE,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlC,QAAA,eAC7B/F,OAAA,CAACnE,IAAI;cACH+J,EAAE,EAAE;gBACFqD,MAAM,EAAE,OAAO;gBACf0E,MAAM,EAAE,SAAS;gBACjBxG,MAAM,EAAE/F,kBAAkB,KAAK,KAAK,GAAG,mBAAmB,GAAG,mBAAmB;gBAChF6F,OAAO,EAAE7F,kBAAkB,KAAK,KAAK,GAAG,SAAS,GAAG,OAAO;gBAC3DqI,UAAU,EAAE;cACd,CAAE;cACFjD,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,KAAK,CAAE;cAAA0E,QAAA,eAE5C/F,OAAA,CAAClE,WAAW;gBAAC8J,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEsD,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAEjD,OAAO,EAAE,MAAM;kBAAE4H,aAAa,EAAE,QAAQ;kBAAE3H,cAAc,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBACjI/F,OAAA,CAAClC,QAAQ;kBAAC8H,EAAE,EAAE;oBAAEsD,QAAQ,EAAE,EAAE;oBAAEhF,KAAK,EAAE,SAAS;oBAAEiC,EAAE,EAAE;kBAAE;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3DjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,WAAW;kBAACR,EAAE,EAAE;oBAAEwB,UAAU,EAAE,GAAG;oBAAEjB,EAAE,EAAE,GAAG;oBAAE+C,QAAQ,EAAE;kBAAS,CAAE;kBAAAnD,QAAA,EAAC;gBAEtF;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAE1B,KAAK,EAAE,MAAM;oBAAEgF,QAAQ,EAAE;kBAAS,CAAE;kBAAAnD,QAAA,EAAC;gBAEvE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPjE,OAAA,CAACpE,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlC,QAAA,eAC7B/F,OAAA,CAACnE,IAAI;cACH+J,EAAE,EAAE;gBACFqD,MAAM,EAAE,OAAO;gBACf0E,MAAM,EAAE,SAAS;gBACjBxG,MAAM,EAAE/F,kBAAkB,KAAK,QAAQ,GAAG,mBAAmB,GAAG,mBAAmB;gBACnF6F,OAAO,EAAE7F,kBAAkB,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAO;gBAC9DqI,UAAU,EAAE;cACd,CAAE;cACFjD,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,QAAQ,CAAE;cAAA0E,QAAA,eAE/C/F,OAAA,CAAClE,WAAW;gBAAC8J,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEsD,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAEjD,OAAO,EAAE,MAAM;kBAAE4H,aAAa,EAAE,QAAQ;kBAAE3H,cAAc,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBACjI/F,OAAA,CAACpB,aAAa;kBAACgH,EAAE,EAAE;oBAAEsD,QAAQ,EAAE,EAAE;oBAAEhF,KAAK,EAAE,SAAS;oBAAEiC,EAAE,EAAE;kBAAE;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChEjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,WAAW;kBAACR,EAAE,EAAE;oBAAEwB,UAAU,EAAE,GAAG;oBAAEjB,EAAE,EAAE,GAAG;oBAAE+C,QAAQ,EAAE;kBAAS,CAAE;kBAAAnD,QAAA,EAAC;gBAEtF;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAE1B,KAAK,EAAE,MAAM;oBAAEgF,QAAQ,EAAE;kBAAS,CAAE;kBAAAnD,QAAA,EAAC;gBAEvE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPjE,OAAA,CAACpE,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlC,QAAA,eAC7B/F,OAAA,CAACnE,IAAI;cACH+J,EAAE,EAAE;gBACFqD,MAAM,EAAE,OAAO;gBACf0E,MAAM,EAAE,SAAS;gBACjBxG,MAAM,EAAE/F,kBAAkB,KAAK,YAAY,GAAG,mBAAmB,GAAG,mBAAmB;gBACvF6F,OAAO,EAAE7F,kBAAkB,KAAK,YAAY,GAAG,SAAS,GAAG,OAAO;gBAClEqI,UAAU,EAAE;cACd,CAAE;cACFjD,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,YAAY,CAAE;cAAA0E,QAAA,eAEnD/F,OAAA,CAAClE,WAAW;gBAAC8J,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEsD,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAEjD,OAAO,EAAE,MAAM;kBAAE4H,aAAa,EAAE,QAAQ;kBAAE3H,cAAc,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBACjI/F,OAAA,CAACxC,YAAY;kBAACoI,EAAE,EAAE;oBAAEsD,QAAQ,EAAE,EAAE;oBAAEhF,KAAK,EAAE,SAAS;oBAAEiC,EAAE,EAAE;kBAAE;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/DjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,WAAW;kBAACR,EAAE,EAAE;oBAAEwB,UAAU,EAAE,GAAG;oBAAEjB,EAAE,EAAE,GAAG;oBAAE+C,QAAQ,EAAE;kBAAS,CAAE;kBAAAnD,QAAA,EAAC;gBAEtF;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAE1B,KAAK,EAAE,MAAM;oBAAEgF,QAAQ,EAAE;kBAAS,CAAE;kBAAAnD,QAAA,EAAC;gBAEvE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPjE,OAAA,CAACpE,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlC,QAAA,eAC7B/F,OAAA,CAACnE,IAAI;cACH+J,EAAE,EAAE;gBACFqD,MAAM,EAAE,OAAO;gBACf0E,MAAM,EAAE,SAAS;gBACjBxG,MAAM,EAAE/F,kBAAkB,KAAK,kBAAkB,GAAG,mBAAmB,GAAG,mBAAmB;gBAC7F6F,OAAO,EAAE7F,kBAAkB,KAAK,kBAAkB,GAAG,SAAS,GAAG,OAAO;gBACxEqI,UAAU,EAAE;cACd,CAAE;cACFjD,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,kBAAkB,CAAE;cAAA0E,QAAA,eAEzD/F,OAAA,CAAClE,WAAW;gBAAC8J,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEsD,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAEjD,OAAO,EAAE,MAAM;kBAAE4H,aAAa,EAAE,QAAQ;kBAAE3H,cAAc,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBACjI/F,OAAA,CAACtB,SAAS;kBAACkH,EAAE,EAAE;oBAAEsD,QAAQ,EAAE,EAAE;oBAAEhF,KAAK,EAAE,SAAS;oBAAEiC,EAAE,EAAE;kBAAE;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5DjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,WAAW;kBAACR,EAAE,EAAE;oBAAEwB,UAAU,EAAE,GAAG;oBAAEjB,EAAE,EAAE,GAAG;oBAAE+C,QAAQ,EAAE;kBAAS,CAAE;kBAAAnD,QAAA,EAAC;gBAEtF;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAE1B,KAAK,EAAE,MAAM;oBAAEgF,QAAQ,EAAE;kBAAS,CAAE;kBAAAnD,QAAA,EAAC;gBAEvE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPjE,OAAA,CAACpE,IAAI;YAACkM,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlC,QAAA,eAC7B/F,OAAA,CAACnE,IAAI;cACH+J,EAAE,EAAE;gBACFqD,MAAM,EAAE,OAAO;gBACf0E,MAAM,EAAE,SAAS;gBACjBxG,MAAM,EAAE/F,kBAAkB,KAAK,cAAc,GAAG,mBAAmB,GAAG,mBAAmB;gBACzF6F,OAAO,EAAE7F,kBAAkB,KAAK,cAAc,GAAG,SAAS,GAAG,OAAO;gBACpEqI,UAAU,EAAE;cACd,CAAE;cACFjD,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,cAAc,CAAE;cAAA0E,QAAA,eAErD/F,OAAA,CAAClE,WAAW;gBAAC8J,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEsD,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAEjD,OAAO,EAAE,MAAM;kBAAE4H,aAAa,EAAE,QAAQ;kBAAE3H,cAAc,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBACjI/F,OAAA,CAACpC,YAAY;kBAACgI,EAAE,EAAE;oBAAEsD,QAAQ,EAAE,EAAE;oBAAEhF,KAAK,EAAE,SAAS;oBAAEiC,EAAE,EAAE;kBAAE;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/DjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,WAAW;kBAACR,EAAE,EAAE;oBAAEwB,UAAU,EAAE,GAAG;oBAAEjB,EAAE,EAAE,GAAG;oBAAE+C,QAAQ,EAAE;kBAAS,CAAE;kBAAAnD,QAAA,EAAC;gBAEtF;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;kBAAC0K,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAE1B,KAAK,EAAE,MAAM;oBAAEgF,QAAQ,EAAE;kBAAS,CAAE;kBAAAnD,QAAA,EAAC;gBAEvE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNjE,OAAA,CAACvE,GAAG;QAACmK,EAAE,EAAE;UAAEiI,SAAS,EAAE;QAAQ,CAAE;QAAA9H,QAAA,GAE7B3E,kBAAkB,KAAK,UAAU,iBAChCpB,OAAA,CAACrE,KAAK;UAACiK,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBnE,WAAW,CAACE,QAAQ,gBACnB9B,OAAA,CAACvE,GAAG;YAAAsK,QAAA,gBACF/F,OAAA,CAACvE,GAAG;cAACmK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D/F,OAAA,CAAChE,MAAM;gBACLuK,SAAS,eAAEvG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,UAAU,EAAE,KAAK,CAAE;gBAC3DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAChE,MAAM;gBACLuK,SAAS,eAAEvG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAE;gBAC7DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLyC,oBAAoB,CAAC9E,WAAW,CAACE,QAAQ,CAAC;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,GACJzD,OAAO,gBACTR,OAAA,CAACT,UAAU;YACT8N,IAAI,EAAC,SAAS;YACdhJ,UAAU,EAAC,UAAU;YACrBV,KAAK,EAAC,mCAAmC;YACzCC,WAAW,EAAC;UAAsD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,gBAEFjE,OAAA,CAACT,UAAU;YACT8N,IAAI,EAAC,OAAO;YACZhJ,UAAU,EAAC,UAAU;YACrBV,KAAK,EAAC,wBAAwB;YAC9BC,WAAW,EAAC,mFAAmF;YAC/FkK,OAAO,EAAEA,CAAA,KAAM;cACbrN,UAAU,CAAC,IAAI,CAAC;cAChBpB,aAAa,CAACmD,iBAAiB,CAAClC,UAAU,EAAE,OAAO,CAAC,CACjDyN,IAAI,CAAC/G,IAAI,IAAI;gBACZnF,cAAc,CAAC8C,IAAI,KAAK;kBACtB,GAAGA,IAAI;kBACP7C,QAAQ,EAAEkF,IAAI,CAACpE;gBACjB,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;gBACZC,OAAO,CAACjC,KAAK,CAAC,iCAAiC,EAAEgC,GAAG,CAAC;cACvD,CAAC,CAAC,CACDsL,OAAO,CAAC,MAAM;gBACbvN,UAAU,CAAC,KAAK,CAAC;cACnB,CAAC,CAAC;YACN,CAAE;YACFD,OAAO,EAAEA;UAAQ;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA7C,kBAAkB,KAAK,KAAK,iBAC3BpB,OAAA,CAACrE,KAAK;UAACiK,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBnE,WAAW,CAACG,GAAG,gBACd/B,OAAA,CAACvE,GAAG;YAAAsK,QAAA,gBACF/F,OAAA,CAACvE,GAAG;cAACmK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D/F,OAAA,CAAChE,MAAM;gBACLuK,SAAS,eAAEvG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAE;gBACtDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAChE,MAAM;gBACLuK,SAAS,eAAEvG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAE;gBACxDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL0C,eAAe,CAAC/E,WAAW,CAACG,GAAG,CAAC;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,GACJzD,OAAO,gBACTR,OAAA,CAACT,UAAU;YACT8N,IAAI,EAAC,SAAS;YACdhJ,UAAU,EAAC,KAAK;YAChBV,KAAK,EAAC,mCAAmC;YACzCC,WAAW,EAAC;UAAyC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,gBAEFjE,OAAA,CAACT,UAAU;YACT8N,IAAI,EAAC,OAAO;YACZhJ,UAAU,EAAC,KAAK;YAChBV,KAAK,EAAC,wBAAwB;YAC9BC,WAAW,EAAC,gFAAgF;YAC5FkK,OAAO,EAAEA,CAAA,KAAM;cACbrN,UAAU,CAAC,IAAI,CAAC;cAChBpB,aAAa,CAACyD,mBAAmB,CAACxC,UAAU,EAAE,OAAO,CAAC,CACnDyN,IAAI,CAAC/G,IAAI,IAAI;gBACZnF,cAAc,CAAC8C,IAAI,KAAK;kBACtB,GAAGA,IAAI;kBACP5C,GAAG,EAAEiF,IAAI,CAACpE;gBACZ,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;gBACZC,OAAO,CAACjC,KAAK,CAAC,4BAA4B,EAAEgC,GAAG,CAAC;cAClD,CAAC,CAAC,CACDsL,OAAO,CAAC,MAAM;gBACbvN,UAAU,CAAC,KAAK,CAAC;cACnB,CAAC,CAAC;YACN,CAAE;YACFD,OAAO,EAAEA;UAAQ;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA7C,kBAAkB,KAAK,QAAQ,iBAC9BpB,OAAA,CAACrE,KAAK;UAACiK,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBnE,WAAW,CAACI,MAAM,gBACjBhC,OAAA,CAACvE,GAAG;YAAAsK,QAAA,gBACF/F,OAAA,CAACvE,GAAG;cAACmK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D/F,OAAA,CAAChE,MAAM;gBACLuK,SAAS,eAAEvG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,QAAQ,EAAE,KAAK,CAAE;gBACzDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAChE,MAAM;gBACLuK,SAAS,eAAEvG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,QAAQ,EAAE,OAAO,CAAE;gBAC3DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL2C,kBAAkB,CAAChF,WAAW,CAACI,MAAM,CAAC;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,GACJzD,OAAO,gBACTR,OAAA,CAACvE,GAAG;YAACmK,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE,QAAQ;cAAEyH,EAAE,EAAE;YAAE,CAAE;YAAA3H,QAAA,gBAClF/F,OAAA,CAAC7D,gBAAgB;cAACsK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BjE,OAAA,CAACtE,UAAU;cAACkK,EAAE,EAAE;gBAAEqI,EAAE,EAAE;cAAE,CAAE;cAAAlI,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAENjE,OAAA,CAACvE,GAAG;YAACmK,EAAE,EAAE;cAAEuD,SAAS,EAAE,QAAQ;cAAEuE,EAAE,EAAE;YAAE,CAAE;YAAA3H,QAAA,gBACtC/F,OAAA,CAAC9D,KAAK;cAACgR,QAAQ,EAAC,OAAO;cAACtH,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjE,OAAA,CAAChE,MAAM;cACLoK,OAAO,EAAC,UAAU;cAClBG,SAAS,eAAEvG,OAAA,CAAC5B,WAAW;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb/F,UAAU,CAAC,IAAI,CAAC;gBAChBpB,aAAa,CAAC2D,eAAe,CAAC1C,UAAU,EAAE,OAAO,CAAC,CAC/CyN,IAAI,CAAC/G,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP3C,MAAM,EAAEgF,IAAI,CAACpE;kBACf,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAACjC,KAAK,CAAC,+BAA+B,EAAEgC,GAAG,CAAC;gBACrD,CAAC,CAAC,CACDsL,OAAO,CAAC,MAAM;kBACbvN,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAsF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA7C,kBAAkB,KAAK,YAAY,iBAClCpB,OAAA,CAACrE,KAAK;UAACiK,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBnE,WAAW,CAACK,SAAS,gBACpBjC,OAAA,CAACvE,GAAG;YAAAsK,QAAA,gBACF/F,OAAA,CAACvE,GAAG;cAACmK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D/F,OAAA,CAAChE,MAAM;gBACLuK,SAAS,eAAEvG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,YAAY,EAAE,KAAK,CAAE;gBAC7DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAChE,MAAM;gBACLuK,SAAS,eAAEvG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,YAAY,EAAE,OAAO,CAAE;gBAC/DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL8C,qBAAqB,CAACnF,WAAW,CAACK,SAAS,CAAC;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,GACJzD,OAAO,gBACTR,OAAA,CAACvE,GAAG;YAACmK,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE,QAAQ;cAAEyH,EAAE,EAAE;YAAE,CAAE;YAAA3H,QAAA,gBAClF/F,OAAA,CAAC7D,gBAAgB;cAACsK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BjE,OAAA,CAACtE,UAAU;cAACkK,EAAE,EAAE;gBAAEqI,EAAE,EAAE;cAAE,CAAE;cAAAlI,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAENjE,OAAA,CAACvE,GAAG;YAACmK,EAAE,EAAE;cAAEuD,SAAS,EAAE,QAAQ;cAAEuE,EAAE,EAAE;YAAE,CAAE;YAAA3H,QAAA,gBACtC/F,OAAA,CAAC9D,KAAK;cAACgR,QAAQ,EAAC,OAAO;cAACtH,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjE,OAAA,CAAChE,MAAM;cACLoK,OAAO,EAAC,UAAU;cAClBG,SAAS,eAAEvG,OAAA,CAAC5B,WAAW;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb/F,UAAU,CAAC,IAAI,CAAC;gBAChBpB,aAAa,CAAC6D,kBAAkB,CAAC5C,UAAU,EAAE,OAAO,CAAC,CAClDyN,IAAI,CAAC/G,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP1C,SAAS,EAAE+E,IAAI,CAACpE;kBAClB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAACjC,KAAK,CAAC,mCAAmC,EAAEgC,GAAG,CAAC;gBACzD,CAAC,CAAC,CACDsL,OAAO,CAAC,MAAM;kBACbvN,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAsF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA7C,kBAAkB,KAAK,kBAAkB,iBACxCpB,OAAA,CAACrE,KAAK;UAACiK,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBnE,WAAW,CAACM,eAAe,gBAC1BlC,OAAA,CAACvE,GAAG;YAAAsK,QAAA,gBACF/F,OAAA,CAACvE,GAAG;cAACmK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D/F,OAAA,CAAChE,MAAM;gBACLuK,SAAS,eAAEvG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,kBAAkB,EAAE,KAAK,CAAE;gBACnEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAChE,MAAM;gBACLuK,SAAS,eAAEvG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,kBAAkB,EAAE,OAAO,CAAE;gBACrEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL4C,2BAA2B,CAACjF,WAAW,CAACM,eAAe,CAAC;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,gBAENjE,OAAA,CAACT,UAAU;YACT8N,IAAI,EAAC,iBAAiB;YACtBhJ,UAAU,EAAC,kBAAkB;YAC7BV,KAAK,EAAC,sBAAsB;YAC5BC,WAAW,EAAC,uHAAuH;YACnIsK,WAAW,EAAC,kBAAkB;YAC9BC,QAAQ,EAAEA,CAAA,KAAM;cACdhN,aAAa,CAAC,kBAAkB,CAAC;cACjCF,aAAa,CAAC,IAAI,CAAC;YACrB;UAAE;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA7C,kBAAkB,KAAK,cAAc,iBACpCpB,OAAA,CAACrE,KAAK;UAACiK,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBnE,WAAW,CAACO,WAAW,gBACtBnC,OAAA,CAACvE,GAAG;YAAAsK,QAAA,gBACF/F,OAAA,CAACvE,GAAG;cAACmK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D/F,OAAA,CAAChE,MAAM;gBACLuK,SAAS,eAAEvG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,cAAc,EAAE,KAAK,CAAE;gBAC/DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAChE,MAAM;gBACLuK,SAAS,eAAEvG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAE;gBACjEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL6C,uBAAuB,CAAClF,WAAW,CAACO,WAAW,CAAC;UAAA;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,gBAENjE,OAAA,CAACT,UAAU;YACT8N,IAAI,EAAC,iBAAiB;YACtBhJ,UAAU,EAAC,cAAc;YACzBV,KAAK,EAAC,sBAAsB;YAC5BC,WAAW,EAAC,8GAA2G;YACvHsK,WAAW,EAAC,mBAAmB;YAC/BC,QAAQ,EAAEA,CAAA,KAAM;cACdhN,aAAa,CAAC,cAAc,CAAC;cAC7B;cACA,MAAM+D,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;cACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;cAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;cAExC/D,WAAW,CAAC;gBACV,GAAGD,QAAQ;gBACXG,WAAW,EAAE2D,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAClD9D,SAAS,EAAEwD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC;cACFvE,aAAa,CAAC,IAAI,CAAC;YACrB;UAAE;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL6I,YAAY,CAAC,CAAC;EAAA;IAAAhJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC7D,EAAA,CA5rDID,iBAAiB;EAAA,QACJlB,WAAW,EACLC,SAAS,EACfC,OAAO;AAAA;AAAAiP,EAAA,GAHpBjO,iBAAiB;AA8rDvB,eAAeA,iBAAiB;AAAC,IAAAiO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}