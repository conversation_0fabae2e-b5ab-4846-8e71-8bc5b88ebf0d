{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\admin\\\\UsersList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Tooltip, Typography, Chip, Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle } from '@mui/material';\nimport { Delete as DeleteIcon, Edit as EditIcon, Block as BlockIcon, CheckCircle as CheckCircleIcon, Refresh as RefreshIcon } from '@mui/icons-material';\nimport { format } from 'date-fns';\nimport userService from '../../services/userService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UsersList = ({\n  onEditUser\n}) => {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [userToDelete, setUserToDelete] = useState(null);\n\n  // Carica gli utenti\n  const loadUsers = async () => {\n    setLoading(true);\n    try {\n      const data = await userService.getUsers();\n      setUsers(data);\n      setError('');\n    } catch (err) {\n      setError(err.detail || 'Errore durante il caricamento degli utenti');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica gli utenti all'avvio del componente\n  useEffect(() => {\n    loadUsers();\n  }, []);\n\n  // Gestisce l'abilitazione/disabilitazione di un utente\n  const handleToggleStatus = async userId => {\n    try {\n      await userService.toggleUserStatus(userId);\n      loadUsers(); // Ricarica gli utenti\n    } catch (err) {\n      setError(err.detail || 'Errore durante la modifica dello stato dell\\'utente');\n    }\n  };\n\n  // Apre il dialog di conferma per l'eliminazione\n  const handleOpenDeleteDialog = user => {\n    setUserToDelete(user);\n    setDeleteDialogOpen(true);\n  };\n\n  // Chiude il dialog di conferma per l'eliminazione\n  const handleCloseDeleteDialog = () => {\n    setDeleteDialogOpen(false);\n    setUserToDelete(null);\n  };\n\n  // Gestisce l'eliminazione di un utente\n  const handleDeleteUser = async () => {\n    if (!userToDelete) return;\n    try {\n      await userService.deleteUser(userToDelete.id_utente);\n      loadUsers(); // Ricarica gli utenti\n      handleCloseDeleteDialog();\n    } catch (err) {\n      setError(err.detail || 'Errore durante l\\'eliminazione dell\\'utente');\n      handleCloseDeleteDialog();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Lista Utenti\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 22\n        }, this),\n        onClick: loadUsers,\n        disabled: loading,\n        children: \"Aggiorna\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Typography, {\n      color: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"ID\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Username\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Ruolo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Ragione Sociale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"VAT\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Nazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Referente\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Scadenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: loading ? /*#__PURE__*/_jsxDEV(TableRow, {\n            children: /*#__PURE__*/_jsxDEV(TableCell, {\n              colSpan: 12,\n              align: \"center\",\n              children: \"Caricamento...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 15\n          }, this) : users.length === 0 ? /*#__PURE__*/_jsxDEV(TableRow, {\n            children: /*#__PURE__*/_jsxDEV(TableCell, {\n              colSpan: 12,\n              align: \"center\",\n              children: \"Nessun utente trovato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this) : users.map(user => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: user.id_utente\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: user.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: user.password_plain || '********'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: user.ruolo === 'owner' ? 'Admin' : user.ruolo === 'user' ? 'Standard' : 'Cantiere',\n                color: user.ruolo === 'owner' ? 'primary' : user.ruolo === 'user' ? 'secondary' : 'default',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: user.ragione_sociale || '-'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: user.email || '-'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: user.vat || '-'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: user.nazione || '-'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: user.referente_aziendale || '-'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: user.data_scadenza ? format(new Date(user.data_scadenza), 'dd/MM/yyyy') : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: user.abilitato ? 'Attivo' : 'Disabilitato',\n                color: user.abilitato ? 'success' : 'error',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Modifica\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  color: \"primary\",\n                  onClick: () => onEditUser(user),\n                  disabled: false /* Abilitato per tutti gli utenti */,\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: user.abilitato ? 'Disabilita' : 'Abilita',\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  color: user.abilitato ? 'error' : 'success',\n                  onClick: () => handleToggleStatus(user.id_utente),\n                  disabled: user.ruolo === 'owner',\n                  children: user.abilitato ? /*#__PURE__*/_jsxDEV(BlockIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 43\n                  }, this) : /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 59\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Elimina\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  color: \"error\",\n                  onClick: () => handleOpenDeleteDialog(user),\n                  disabled: user.ruolo === 'owner',\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 19\n            }, this)]\n          }, user.id_utente, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteDialogOpen,\n      onClose: handleCloseDeleteDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Conferma eliminazione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"Sei sicuro di voler eliminare l'utente \", userToDelete === null || userToDelete === void 0 ? void 0 : userToDelete.username, \"? Questa azione non pu\\xF2 essere annullata.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDeleteDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteUser,\n          color: \"error\",\n          autoFocus: true,\n          children: \"Elimina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(UsersList, \"K9BMX8XWeMPoU51T1pJwjzIe8mQ=\");\n_c = UsersList;\nexport default UsersList;\nvar _c;\n$RefreshReg$(_c, \"UsersList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "IconButton", "<PERSON><PERSON><PERSON>", "Typography", "Chip", "<PERSON><PERSON>", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "Delete", "DeleteIcon", "Edit", "EditIcon", "Block", "BlockIcon", "CheckCircle", "CheckCircleIcon", "Refresh", "RefreshIcon", "format", "userService", "jsxDEV", "_jsxDEV", "UsersList", "onEditUser", "_s", "users", "setUsers", "loading", "setLoading", "error", "setError", "deleteDialogOpen", "setDeleteDialogOpen", "userToDelete", "setUserToDelete", "loadUsers", "data", "getUsers", "err", "detail", "handleToggleStatus", "userId", "toggleUserStatus", "handleOpenDeleteDialog", "user", "handleCloseDeleteDialog", "handleDeleteUser", "deleteUser", "id_utente", "children", "sx", "display", "justifyContent", "alignItems", "mb", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "disabled", "color", "component", "colSpan", "align", "length", "map", "username", "password_plain", "label", "ruolo", "size", "ragione_sociale", "email", "vat", "nazione", "referente_aziendale", "data_scadenza", "Date", "abilitato", "title", "open", "onClose", "autoFocus", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/admin/UsersList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  IconButton,\n  Tooltip,\n  Typography,\n  Chip,\n  Button,\n  Dialog,\n  DialogActions,\n  DialogContent,\n  DialogContentText,\n  DialogTitle\n} from '@mui/material';\nimport {\n  Delete as DeleteIcon,\n  Edit as EditIcon,\n  Block as BlockIcon,\n  CheckCircle as CheckCircleIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport { format } from 'date-fns';\nimport userService from '../../services/userService';\n\nconst UsersList = ({ onEditUser }) => {\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [userToDelete, setUserToDelete] = useState(null);\n\n  // Carica gli utenti\n  const loadUsers = async () => {\n    setLoading(true);\n    try {\n      const data = await userService.getUsers();\n      setUsers(data);\n      setError('');\n    } catch (err) {\n      setError(err.detail || 'Errore durante il caricamento degli utenti');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica gli utenti all'avvio del componente\n  useEffect(() => {\n    loadUsers();\n  }, []);\n\n  // Gestisce l'abilitazione/disabilitazione di un utente\n  const handleToggleStatus = async (userId) => {\n    try {\n      await userService.toggleUserStatus(userId);\n      loadUsers(); // Ricarica gli utenti\n    } catch (err) {\n      setError(err.detail || 'Errore durante la modifica dello stato dell\\'utente');\n    }\n  };\n\n  // Apre il dialog di conferma per l'eliminazione\n  const handleOpenDeleteDialog = (user) => {\n    setUserToDelete(user);\n    setDeleteDialogOpen(true);\n  };\n\n  // Chiude il dialog di conferma per l'eliminazione\n  const handleCloseDeleteDialog = () => {\n    setDeleteDialogOpen(false);\n    setUserToDelete(null);\n  };\n\n  // Gestisce l'eliminazione di un utente\n  const handleDeleteUser = async () => {\n    if (!userToDelete) return;\n\n    try {\n      await userService.deleteUser(userToDelete.id_utente);\n      loadUsers(); // Ricarica gli utenti\n      handleCloseDeleteDialog();\n    } catch (err) {\n      setError(err.detail || 'Errore durante l\\'eliminazione dell\\'utente');\n      handleCloseDeleteDialog();\n    }\n  };\n\n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n        <Typography variant=\"h6\">Lista Utenti</Typography>\n        <Button\n          variant=\"outlined\"\n          startIcon={<RefreshIcon />}\n          onClick={loadUsers}\n          disabled={loading}\n        >\n          Aggiorna\n        </Button>\n      </Box>\n\n      {error && (\n        <Typography color=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Typography>\n      )}\n\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>ID</TableCell>\n              <TableCell>Username</TableCell>\n              <TableCell>Password</TableCell>\n              <TableCell>Ruolo</TableCell>\n              <TableCell>Ragione Sociale</TableCell>\n              <TableCell>Email</TableCell>\n              <TableCell>VAT</TableCell>\n              <TableCell>Nazione</TableCell>\n              <TableCell>Referente</TableCell>\n              <TableCell>Scadenza</TableCell>\n              <TableCell>Stato</TableCell>\n              <TableCell>Azioni</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {loading ? (\n              <TableRow>\n                <TableCell colSpan={12} align=\"center\">\n                  Caricamento...\n                </TableCell>\n              </TableRow>\n            ) : users.length === 0 ? (\n              <TableRow>\n                <TableCell colSpan={12} align=\"center\">\n                  Nessun utente trovato\n                </TableCell>\n              </TableRow>\n            ) : (\n              users.map((user) => (\n                <TableRow key={user.id_utente}>\n                  <TableCell>{user.id_utente}</TableCell>\n                  <TableCell>{user.username}</TableCell>\n                  <TableCell>\n                    {/* Mostra la password in chiaro per l'amministratore */}\n                    {user.password_plain || '********'}\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      label={user.ruolo === 'owner' ? 'Admin' : user.ruolo === 'user' ? 'Standard' : 'Cantiere'}\n                      color={user.ruolo === 'owner' ? 'primary' : user.ruolo === 'user' ? 'secondary' : 'default'}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>{user.ragione_sociale || '-'}</TableCell>\n                  <TableCell>{user.email || '-'}</TableCell>\n                  <TableCell>{user.vat || '-'}</TableCell>\n                  <TableCell>{user.nazione || '-'}</TableCell>\n                  <TableCell>{user.referente_aziendale || '-'}</TableCell>\n                  <TableCell>\n                    {user.data_scadenza ? format(new Date(user.data_scadenza), 'dd/MM/yyyy') : 'N/A'}\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      label={user.abilitato ? 'Attivo' : 'Disabilitato'}\n                      color={user.abilitato ? 'success' : 'error'}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Tooltip title=\"Modifica\">\n                      <IconButton\n                        color=\"primary\"\n                        onClick={() => onEditUser(user)}\n                        disabled={false} /* Abilitato per tutti gli utenti */\n                      >\n                        <EditIcon />\n                      </IconButton>\n                    </Tooltip>\n\n                    <Tooltip title={user.abilitato ? 'Disabilita' : 'Abilita'}>\n                      <IconButton\n                        color={user.abilitato ? 'error' : 'success'}\n                        onClick={() => handleToggleStatus(user.id_utente)}\n                        disabled={user.ruolo === 'owner'}\n                      >\n                        {user.abilitato ? <BlockIcon /> : <CheckCircleIcon />}\n                      </IconButton>\n                    </Tooltip>\n\n                    <Tooltip title=\"Elimina\">\n                      <IconButton\n                        color=\"error\"\n                        onClick={() => handleOpenDeleteDialog(user)}\n                        disabled={user.ruolo === 'owner'}\n                      >\n                        <DeleteIcon />\n                      </IconButton>\n                    </Tooltip>\n                  </TableCell>\n                </TableRow>\n              ))\n            )}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* Dialog di conferma eliminazione */}\n      <Dialog\n        open={deleteDialogOpen}\n        onClose={handleCloseDeleteDialog}\n      >\n        <DialogTitle>Conferma eliminazione</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Sei sicuro di voler eliminare l'utente {userToDelete?.username}?\n            Questa azione non può essere annullata.\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDeleteDialog}>Annulla</Button>\n          <Button onClick={handleDeleteUser} color=\"error\" autoFocus>\n            Elimina\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default UsersList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,MAAM,EACNC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,WAAW,QACN,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,UAAU;AACjC,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,SAAS,GAAGA,CAAC;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAM+C,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BP,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMQ,IAAI,GAAG,MAAMjB,WAAW,CAACkB,QAAQ,CAAC,CAAC;MACzCX,QAAQ,CAACU,IAAI,CAAC;MACdN,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZR,QAAQ,CAACQ,GAAG,CAACC,MAAM,IAAI,4CAA4C,CAAC;IACtE,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAvC,SAAS,CAAC,MAAM;IACd8C,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,kBAAkB,GAAG,MAAOC,MAAM,IAAK;IAC3C,IAAI;MACF,MAAMtB,WAAW,CAACuB,gBAAgB,CAACD,MAAM,CAAC;MAC1CN,SAAS,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZR,QAAQ,CAACQ,GAAG,CAACC,MAAM,IAAI,qDAAqD,CAAC;IAC/E;EACF,CAAC;;EAED;EACA,MAAMI,sBAAsB,GAAIC,IAAI,IAAK;IACvCV,eAAe,CAACU,IAAI,CAAC;IACrBZ,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMa,uBAAuB,GAAGA,CAAA,KAAM;IACpCb,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMY,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACb,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMd,WAAW,CAAC4B,UAAU,CAACd,YAAY,CAACe,SAAS,CAAC;MACpDb,SAAS,CAAC,CAAC,CAAC,CAAC;MACbU,uBAAuB,CAAC,CAAC;IAC3B,CAAC,CAAC,OAAOP,GAAG,EAAE;MACZR,QAAQ,CAACQ,GAAG,CAACC,MAAM,IAAI,6CAA6C,CAAC;MACrEM,uBAAuB,CAAC,CAAC;IAC3B;EACF,CAAC;EAED,oBACExB,OAAA,CAAC/B,GAAG;IAAA2D,QAAA,gBACF5B,OAAA,CAAC/B,GAAG;MAAC4D,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACzF5B,OAAA,CAACrB,UAAU;QAACuD,OAAO,EAAC,IAAI;QAAAN,QAAA,EAAC;MAAY;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAClDtC,OAAA,CAACnB,MAAM;QACLqD,OAAO,EAAC,UAAU;QAClBK,SAAS,eAAEvC,OAAA,CAACJ,WAAW;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BE,OAAO,EAAE1B,SAAU;QACnB2B,QAAQ,EAAEnC,OAAQ;QAAAsB,QAAA,EACnB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL9B,KAAK,iBACJR,OAAA,CAACrB,UAAU;MAAC+D,KAAK,EAAC,OAAO;MAACb,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EACrCpB;IAAK;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,eAEDtC,OAAA,CAAC3B,cAAc;MAACsE,SAAS,EAAEnE,KAAM;MAAAoD,QAAA,eAC/B5B,OAAA,CAAC9B,KAAK;QAAA0D,QAAA,gBACJ5B,OAAA,CAAC1B,SAAS;UAAAsD,QAAA,eACR5B,OAAA,CAACzB,QAAQ;YAAAqD,QAAA,gBACP5B,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAC;YAAE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACzBtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAC;YAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/BtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAC;YAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/BtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAC;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5BtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAC;YAAe;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtCtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAC;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5BtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAC;YAAG;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC1BtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAC;YAAO;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9BtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAC;YAAS;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChCtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAC;YAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/BtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAC;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5BtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAC;YAAM;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZtC,OAAA,CAAC7B,SAAS;UAAAyD,QAAA,EACPtB,OAAO,gBACNN,OAAA,CAACzB,QAAQ;YAAAqD,QAAA,eACP5B,OAAA,CAAC5B,SAAS;cAACwE,OAAO,EAAE,EAAG;cAACC,KAAK,EAAC,QAAQ;cAAAjB,QAAA,EAAC;YAEvC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,GACTlC,KAAK,CAAC0C,MAAM,KAAK,CAAC,gBACpB9C,OAAA,CAACzB,QAAQ;YAAAqD,QAAA,eACP5B,OAAA,CAAC5B,SAAS;cAACwE,OAAO,EAAE,EAAG;cAACC,KAAK,EAAC,QAAQ;cAAAjB,QAAA,EAAC;YAEvC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,GAEXlC,KAAK,CAAC2C,GAAG,CAAExB,IAAI,iBACbvB,OAAA,CAACzB,QAAQ;YAAAqD,QAAA,gBACP5B,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAEL,IAAI,CAACI;YAAS;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvCtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAEL,IAAI,CAACyB;YAAQ;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACtCtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAEPL,IAAI,CAAC0B,cAAc,IAAI;YAAU;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACZtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,eACR5B,OAAA,CAACpB,IAAI;gBACHsE,KAAK,EAAE3B,IAAI,CAAC4B,KAAK,KAAK,OAAO,GAAG,OAAO,GAAG5B,IAAI,CAAC4B,KAAK,KAAK,MAAM,GAAG,UAAU,GAAG,UAAW;gBAC1FT,KAAK,EAAEnB,IAAI,CAAC4B,KAAK,KAAK,OAAO,GAAG,SAAS,GAAG5B,IAAI,CAAC4B,KAAK,KAAK,MAAM,GAAG,WAAW,GAAG,SAAU;gBAC5FC,IAAI,EAAC;cAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAEL,IAAI,CAAC8B,eAAe,IAAI;YAAG;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpDtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAEL,IAAI,CAAC+B,KAAK,IAAI;YAAG;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1CtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAEL,IAAI,CAACgC,GAAG,IAAI;YAAG;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxCtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAEL,IAAI,CAACiC,OAAO,IAAI;YAAG;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5CtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EAAEL,IAAI,CAACkC,mBAAmB,IAAI;YAAG;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxDtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,EACPL,IAAI,CAACmC,aAAa,GAAG7D,MAAM,CAAC,IAAI8D,IAAI,CAACpC,IAAI,CAACmC,aAAa,CAAC,EAAE,YAAY,CAAC,GAAG;YAAK;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACZtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,eACR5B,OAAA,CAACpB,IAAI;gBACHsE,KAAK,EAAE3B,IAAI,CAACqC,SAAS,GAAG,QAAQ,GAAG,cAAe;gBAClDlB,KAAK,EAAEnB,IAAI,CAACqC,SAAS,GAAG,SAAS,GAAG,OAAQ;gBAC5CR,IAAI,EAAC;cAAO;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZtC,OAAA,CAAC5B,SAAS;cAAAwD,QAAA,gBACR5B,OAAA,CAACtB,OAAO;gBAACmF,KAAK,EAAC,UAAU;gBAAAjC,QAAA,eACvB5B,OAAA,CAACvB,UAAU;kBACTiE,KAAK,EAAC,SAAS;kBACfF,OAAO,EAAEA,CAAA,KAAMtC,UAAU,CAACqB,IAAI,CAAE;kBAChCkB,QAAQ,EAAE,KAAM,CAAC;kBAAAb,QAAA,eAEjB5B,OAAA,CAACV,QAAQ;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEVtC,OAAA,CAACtB,OAAO;gBAACmF,KAAK,EAAEtC,IAAI,CAACqC,SAAS,GAAG,YAAY,GAAG,SAAU;gBAAAhC,QAAA,eACxD5B,OAAA,CAACvB,UAAU;kBACTiE,KAAK,EAAEnB,IAAI,CAACqC,SAAS,GAAG,OAAO,GAAG,SAAU;kBAC5CpB,OAAO,EAAEA,CAAA,KAAMrB,kBAAkB,CAACI,IAAI,CAACI,SAAS,CAAE;kBAClDc,QAAQ,EAAElB,IAAI,CAAC4B,KAAK,KAAK,OAAQ;kBAAAvB,QAAA,EAEhCL,IAAI,CAACqC,SAAS,gBAAG5D,OAAA,CAACR,SAAS;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGtC,OAAA,CAACN,eAAe;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEVtC,OAAA,CAACtB,OAAO;gBAACmF,KAAK,EAAC,SAAS;gBAAAjC,QAAA,eACtB5B,OAAA,CAACvB,UAAU;kBACTiE,KAAK,EAAC,OAAO;kBACbF,OAAO,EAAEA,CAAA,KAAMlB,sBAAsB,CAACC,IAAI,CAAE;kBAC5CkB,QAAQ,EAAElB,IAAI,CAAC4B,KAAK,KAAK,OAAQ;kBAAAvB,QAAA,eAEjC5B,OAAA,CAACZ,UAAU;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GA3DCf,IAAI,CAACI,SAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4DnB,CACX;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAGjBtC,OAAA,CAAClB,MAAM;MACLgF,IAAI,EAAEpD,gBAAiB;MACvBqD,OAAO,EAAEvC,uBAAwB;MAAAI,QAAA,gBAEjC5B,OAAA,CAACd,WAAW;QAAA0C,QAAA,EAAC;MAAqB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChDtC,OAAA,CAAChB,aAAa;QAAA4C,QAAA,eACZ5B,OAAA,CAACf,iBAAiB;UAAA2C,QAAA,GAAC,yCACsB,EAAChB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEoC,QAAQ,EAAC,8CAEjE;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChBtC,OAAA,CAACjB,aAAa;QAAA6C,QAAA,gBACZ5B,OAAA,CAACnB,MAAM;UAAC2D,OAAO,EAAEhB,uBAAwB;UAAAI,QAAA,EAAC;QAAO;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC1DtC,OAAA,CAACnB,MAAM;UAAC2D,OAAO,EAAEf,gBAAiB;UAACiB,KAAK,EAAC,OAAO;UAACsB,SAAS;UAAApC,QAAA,EAAC;QAE3D;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACnC,EAAA,CA3MIF,SAAS;AAAAgE,EAAA,GAAThE,SAAS;AA6Mf,eAAeA,SAAS;AAAC,IAAAgE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}