{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { minutesInHour } from \"../constants/index.js\";\n/**\n * @name minutesToHours\n * @category Conversion Helpers\n * @summary Convert minutes to hours.\n *\n * @description\n * Convert a number of minutes to a full number of hours.\n *\n * @param {number} minutes - number of minutes to be converted\n *\n * @returns {number} the number of minutes converted in hours\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 140 minutes to hours:\n * const result = minutesToHours(120)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = minutesToHours(179)\n * //=> 2\n */\nexport default function minutesToHours(minutes) {\n  requiredArgs(1, arguments);\n  var hours = minutes / minutesInHour;\n  return Math.floor(hours);\n}", "map": {"version": 3, "names": ["requiredArgs", "minutesInHour", "minutesToHours", "minutes", "arguments", "hours", "Math", "floor"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/minutesToHours/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { minutesInHour } from \"../constants/index.js\";\n/**\n * @name minutesToHours\n * @category Conversion Helpers\n * @summary Convert minutes to hours.\n *\n * @description\n * Convert a number of minutes to a full number of hours.\n *\n * @param {number} minutes - number of minutes to be converted\n *\n * @returns {number} the number of minutes converted in hours\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 140 minutes to hours:\n * const result = minutesToHours(120)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = minutesToHours(179)\n * //=> 2\n */\nexport default function minutesToHours(minutes) {\n  requiredArgs(1, arguments);\n  var hours = minutes / minutesInHour;\n  return Math.floor(hours);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,aAAa,QAAQ,uBAAuB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC9CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,KAAK,GAAGF,OAAO,GAAGF,aAAa;EACnC,OAAOK,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}