{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\AggiungiCavoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, FormHelperText, Alert, CircularProgress, Typography, Paper } from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport config from '../../config';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AggiungiCavoForm = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  isDialog = false\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Imposta loadingRevisione a false all'avvio\n  useEffect(() => {\n    setLoadingRevisione(false);\n  }, []);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, non fare nulla (il dialog ha il suo pulsante di annullamento)\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      // Validazione completa dei dati del cavo\n      console.log('Dati del form prima della validazione:', formData);\n      const validation = validateCavoData(formData);\n      console.log('Risultato validazione:', validation);\n      if (!validation.isValid) {\n        console.error('Errori di validazione:', validation.errors);\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        onError('Ci sono errori nel form. Controlla i campi evidenziati in rosso.');\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Assicurati che i campi obbligatori siano presenti (basati sulla definizione della tabella)\n      // Campi obbligatori: id_cavo, id_cantiere, utility, tipologia, n_conduttori, sezione, metri_teorici,\n      // ubicazione_partenza, ubicazione_arrivo, stato_installazione\n\n      // Verifica che i campi obbligatori siano presenti\n      const requiredFields = ['id_cavo', 'utility', 'tipologia', 'n_conduttori', 'sezione', 'metri_teorici', 'ubicazione_partenza', 'ubicazione_arrivo', 'stato_installazione'];\n      const missingFields = requiredFields.filter(field => !validatedData[field]);\n      if (missingFields.length > 0) {\n        throw new Error(`Campi obbligatori mancanti: ${missingFields.join(', ')}`);\n      }\n\n      // Prepara i dati da inviare\n      const dataToSend = {\n        ...validatedData,\n        // Assicurati che i campi obbligatori siano presenti\n        id_cavo: validatedData.id_cavo.toUpperCase(),\n        utility: validatedData.utility,\n        tipologia: validatedData.tipologia,\n        n_conduttori: validatedData.n_conduttori ? validatedData.n_conduttori.toString() : \"0\",\n        // Invia come stringa\n        sezione: validatedData.sezione ? validatedData.sezione.toString() : \"0\",\n        // Invia come stringa\n        metri_teorici: parseFloat(validatedData.metri_teorici) || 0,\n        ubicazione_partenza: validatedData.ubicazione_partenza,\n        ubicazione_arrivo: validatedData.ubicazione_arrivo,\n        stato_installazione: validatedData.stato_installazione || 'Da installare',\n        // Campi opzionali\n        metratura_reale: validatedData.metratura_reale ? parseFloat(validatedData.metratura_reale) : null,\n        id_bobina: validatedData.id_bobina || null,\n        // Altri campi che potrebbero essere utili\n        sistema: validatedData.sistema || null,\n        colore_cavo: validatedData.colore_cavo || null,\n        utenza_partenza: validatedData.utenza_partenza || null,\n        utenza_arrivo: validatedData.utenza_arrivo || null,\n        descrizione_utenza_partenza: validatedData.descrizione_utenza_partenza || null,\n        descrizione_utenza_arrivo: validatedData.descrizione_utenza_arrivo || null,\n        sh: validatedData.sh || 'N',\n        responsabile_posa: validatedData.responsabile_posa || null,\n        note: validatedData.note || null\n      };\n      console.log('Dati da inviare al server dopo la validazione:', dataToSend);\n      try {\n        // Invia i dati al server\n        console.log('Tentativo di invio dati al server...');\n\n        // Verifica che cantiereId sia valido\n        if (!cantiereId) {\n          throw new Error('ID cantiere non valido o mancante');\n        }\n\n        // Usa axios direttamente per avere più controllo\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('Token di autenticazione mancante. Effettua nuovamente il login.');\n        }\n        console.log(`Invio richiesta POST a ${config.API_URL}/cavi/${cantiereId}`);\n        console.log('Dati inviati:', JSON.stringify(dataToSend, null, 2));\n        try {\n          // Tenta di inviare la richiesta\n          const response = await axios.post(`${config.API_URL}/cavi/${cantiereId}`, dataToSend, {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 30000 // Aumentato a 30 secondi per dare più tempo al server\n          });\n          console.log('Risposta dal server:', response.data);\n\n          // Imposta loading a false prima di chiudere il dialog\n          setLoading(false);\n\n          // Notifica il successo per chiudere il dialog o reindirizzare\n          onSuccess('Cavo aggiunto con successo');\n\n          // Reindirizza solo se non è in un dialog\n          if (!isDialog) {\n            console.log('Reindirizzamento a visualizza cavi...');\n            try {\n              // Usa navigate invece di window.location per un reindirizzamento più pulito\n              redirectToVisualizzaCavi(navigate);\n            } catch (navError) {\n              console.error('Errore durante il reindirizzamento:', navError);\n              // Fallback: usa window.location solo se navigate fallisce\n              window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n            }\n          }\n        } catch (error) {\n          console.error('Errore durante l\\'invio dei dati al server:', error);\n\n          // Se è un errore di rete, verifica se il cavo è stato inserito senza mostrare subito un errore\n          if (!error.response || error.code === 'ECONNABORTED' || error.message.includes('Network Error')) {\n            // Manteniamo lo stato di caricamento attivo senza mostrare errori\n            console.log('Rilevato possibile errore di rete, verifica in corso...');\n\n            // Attendi un secondo e poi verifica se il cavo è stato inserito\n            setTimeout(async () => {\n              try {\n                // Verifica se il cavo esiste\n                const checkResponse = await axios.get(`${config.API_URL}/cavi/${cantiereId}/check/${dataToSend.id_cavo}`, {\n                  headers: {\n                    'Authorization': `Bearer ${token}`\n                  },\n                  timeout: 5000\n                });\n                if (checkResponse.data && checkResponse.data.exists) {\n                  // Il cavo è stato inserito con successo\n\n                  // Imposta loading a false prima di chiudere il dialog\n                  setLoading(false);\n\n                  // Notifica il successo per chiudere il dialog o reindirizzare\n                  onSuccess('Cavo aggiunto con successo');\n\n                  // Reindirizza solo se non è in un dialog\n                  if (!isDialog) {\n                    console.log('Reindirizzamento a visualizza cavi...');\n                    try {\n                      // Usa navigate invece di window.location per un reindirizzamento più pulito\n                      redirectToVisualizzaCavi(navigate);\n                    } catch (navError) {\n                      console.error('Errore durante il reindirizzamento:', navError);\n                      // Fallback: usa window.location solo se navigate fallisce\n                      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n                    }\n                  }\n                } else {\n                  // Il cavo non è stato inserito, ora mostriamo l'errore\n                  onError('Il cavo non è stato inserito a causa di un problema di connessione. Riprova.');\n                  setLoading(false);\n                }\n              } catch (checkError) {\n                console.error('Errore durante la verifica:', checkError);\n                onError('Impossibile verificare se il cavo è stato inserito. Riprova.');\n                setLoading(false);\n              }\n            }, 1000);\n          } else {\n            // Per altri tipi di errori, mostra il messaggio di errore\n            let errorMessage = 'Errore durante l\\'aggiunta del cavo';\n            if (error.response && error.response.data) {\n              const responseData = error.response.data;\n              if (responseData.detail) {\n                errorMessage = responseData.detail;\n              } else if (typeof responseData === 'string') {\n                errorMessage = responseData;\n              } else {\n                errorMessage = JSON.stringify(responseData);\n              }\n            } else if (error.message) {\n              errorMessage = error.message;\n            }\n            onError(errorMessage);\n            setLoading(false);\n          }\n        }\n      } catch (error) {\n        console.error('Errore durante la preparazione della richiesta:', error);\n        let errorMessage = error.message || 'Errore durante l\\'aggiunta del cavo';\n        onError(errorMessage);\n        setLoading(false);\n      }\n    } catch (error) {\n      console.error('Errore durante l\\'aggiunta del cavo:', error);\n      onError(error.detail || 'Errore durante l\\'aggiunta del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleSubmit,\n    noValidate: true,\n    children: loadingRevisione ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [hasWarnings && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 23\n          }, this),\n          sx: {\n            mb: isDialog ? 0.5 : 2,\n            py: isDialog ? 0.3 : 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontSize: isDialog ? '0.8rem' : '0.875rem'\n            },\n            children: \"Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 15\n        }, this), formWarnings.network_error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: isDialog ? 0.5 : 3,\n            py: isDialog ? 0.3 : 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: formWarnings.network_error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 0.5 : 3,\n          mb: isDialog ? 0.5 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '0.8rem' : '1.25rem',\n            mb: isDialog ? 0.3 : 1\n          },\n          children: \"Informazioni Generali\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 0.5 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"id_cavo\",\n              label: \"ID Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_cavo,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.id_cavo,\n              helperText: formErrors.id_cavo,\n              inputProps: {\n                style: {\n                  textTransform: 'uppercase'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sistema\",\n              label: \"Sistema\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sistema,\n              onChange: handleFormChange,\n              error: !!formErrors.sistema,\n              helperText: formErrors.sistema\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utility\",\n              label: \"Utility\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utility,\n              onChange: handleFormChange,\n              error: !!formErrors.utility,\n              helperText: formErrors.utility\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 0.5 : 3,\n          mb: isDialog ? 0.5 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '0.8rem' : '1.25rem',\n            mb: isDialog ? 0.3 : 1\n          },\n          children: \"Caratteristiche Tecniche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 0.5 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"colore_cavo\",\n              label: \"Colore Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.colore_cavo,\n              onChange: handleFormChange,\n              error: !!formErrors.colore_cavo,\n              helperText: formErrors.colore_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"tipologia\",\n              label: \"Tipologia\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.tipologia,\n              onChange: handleFormChange,\n              error: !!formErrors.tipologia,\n              helperText: formErrors.tipologia\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"n_conduttori\",\n              label: \"Numero Conduttori\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.n_conduttori,\n              onChange: handleFormChange,\n              error: !!formErrors.n_conduttori,\n              helperText: formErrors.n_conduttori || formWarnings.n_conduttori,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.n_conduttori ? 'orange' : undefined\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sezione\",\n              label: \"Sezione\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sezione,\n              onChange: handleFormChange,\n              error: !!formErrors.sezione,\n              helperText: formErrors.sezione || formWarnings.sezione,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.sezione ? 'orange' : undefined\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              error: !!formErrors.sh,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"sh-label\",\n                children: \"Schermato (S/N)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"sh-label\",\n                name: \"sh\",\n                value: formData.sh || 'N',\n                label: \"Schermato (S/N)\",\n                onChange: handleFormChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"S\",\n                  children: \"S\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"N\",\n                  children: \"N\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: \"Opzionale (default: N)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 19\n              }, this), formErrors.sh && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                error: true,\n                children: formErrors.sh\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 0.5 : 3,\n          mb: isDialog ? 0.5 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '0.8rem' : '1.25rem',\n            mb: isDialog ? 0.3 : 1\n          },\n          children: \"Ubicazione Partenza\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 0.5 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_partenza\",\n              label: \"Ubicazione Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_partenza,\n              helperText: formErrors.ubicazione_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_partenza\",\n              label: \"Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_partenza,\n              helperText: formErrors.utenza_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_partenza\",\n              label: \"Descrizione Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_partenza,\n              helperText: formErrors.descrizione_utenza_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 0.5 : 3,\n          mb: isDialog ? 0.5 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '0.8rem' : '1.25rem',\n            mb: isDialog ? 0.3 : 1\n          },\n          children: \"Ubicazione Arrivo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 0.5 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_arrivo\",\n              label: \"Ubicazione Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_arrivo,\n              helperText: formErrors.ubicazione_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_arrivo\",\n              label: \"Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_arrivo,\n              helperText: formErrors.utenza_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_arrivo\",\n              label: \"Descrizione Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_arrivo,\n              helperText: formErrors.descrizione_utenza_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 0.5 : 3,\n          mb: isDialog ? 0.5 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '0.8rem' : '1.25rem',\n            mb: isDialog ? 0.3 : 1\n          },\n          children: \"Metratura\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"metri_teorici\",\n              label: \"Metri Teorici\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_teorici,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_teorici,\n              helperText: formErrors.metri_teorici\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 11\n      }, this), !isDialog && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          size: \"large\",\n          onClick: handleCancel,\n          disabled: loading,\n          sx: {\n            minWidth: 150\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"large\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 28\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 150\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 28\n          }, this) : 'Salva Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 13\n      }, this), isDialog && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 1,\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"medium\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 28\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 120\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 28\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 313,\n    columnNumber: 5\n  }, this);\n};\n_s(AggiungiCavoForm, \"IrgaV03YC0j+XmJlNQCyM0v6I/g=\", false, function () {\n  return [useNavigate];\n});\n_c = AggiungiCavoForm;\nexport default AggiungiCavoForm;\nvar _c;\n$RefreshReg$(_c, \"AggiungiCavoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "FormHelperText", "<PERSON><PERSON>", "CircularProgress", "Typography", "Paper", "Save", "SaveIcon", "Warning", "WarningIcon", "useNavigate", "axios", "config", "caviService", "validateCavoData", "validateField", "isEmpty", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AggiungiCavoForm", "cantiereId", "onSuccess", "onError", "isDialog", "_s", "navigate", "loading", "setLoading", "loadingRevisione", "setLoadingRevisione", "formData", "setFormData", "id_cavo", "sistema", "utility", "colore_cavo", "tipologia", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "responsabile_posa", "id_bobina", "stato_installazione", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "handleFormChange", "e", "name", "value", "target", "additionalParams", "metriTeorici", "parseFloat", "result", "prev", "valid", "message", "warning", "handleCancel", "handleSubmit", "preventDefault", "console", "log", "validation", "<PERSON><PERSON><PERSON><PERSON>", "error", "errors", "warnings", "validatedData", "toUpperCase", "requiredFields", "missingFields", "filter", "field", "length", "Error", "join", "dataToSend", "toString", "note", "token", "localStorage", "getItem", "API_URL", "JSON", "stringify", "response", "post", "headers", "timeout", "data", "navError", "window", "location", "href", "code", "includes", "setTimeout", "checkResponse", "get", "exists", "checkError", "errorMessage", "responseData", "detail", "hasWarnings", "Object", "keys", "component", "onSubmit", "noValidate", "children", "sx", "display", "justifyContent", "my", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "icon", "mb", "py", "variant", "fontSize", "network_error", "fontWeight", "p", "boxShadow", "gutterBottom", "container", "spacing", "item", "xs", "sm", "label", "fullWidth", "onChange", "required", "helperText", "inputProps", "style", "textTransform", "FormHelperTextProps", "color", "undefined", "id", "labelId", "mt", "gap", "size", "onClick", "disabled", "min<PERSON><PERSON><PERSON>", "type", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/AggiungiCavoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormHelperText,\n  Alert,\n  CircularProgress,\n  Typography,\n  Paper\n} from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport config from '../../config';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\nconst AggiungiCavoForm = ({ cantiereId, onSuccess, onError, isDialog = false }) => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Imposta loadingRevisione a false all'avvio\n  useEffect(() => {\n    setLoadingRevisione(false);\n  }, []);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, non fare nulla (il dialog ha il suo pulsante di annullamento)\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n\n\n  // Gestisce l'invio del form\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      // Validazione completa dei dati del cavo\n      console.log('Dati del form prima della validazione:', formData);\n      const validation = validateCavoData(formData);\n      console.log('Risultato validazione:', validation);\n\n      if (!validation.isValid) {\n        console.error('Errori di validazione:', validation.errors);\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        onError('Ci sono errori nel form. Controlla i campi evidenziati in rosso.');\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Assicurati che i campi obbligatori siano presenti (basati sulla definizione della tabella)\n      // Campi obbligatori: id_cavo, id_cantiere, utility, tipologia, n_conduttori, sezione, metri_teorici,\n      // ubicazione_partenza, ubicazione_arrivo, stato_installazione\n\n      // Verifica che i campi obbligatori siano presenti\n      const requiredFields = [\n        'id_cavo', 'utility', 'tipologia', 'n_conduttori', 'sezione', 'metri_teorici',\n        'ubicazione_partenza', 'ubicazione_arrivo', 'stato_installazione'\n      ];\n\n      const missingFields = requiredFields.filter(field => !validatedData[field]);\n      if (missingFields.length > 0) {\n        throw new Error(`Campi obbligatori mancanti: ${missingFields.join(', ')}`);\n      }\n\n      // Prepara i dati da inviare\n      const dataToSend = {\n        ...validatedData,\n        // Assicurati che i campi obbligatori siano presenti\n        id_cavo: validatedData.id_cavo.toUpperCase(),\n        utility: validatedData.utility,\n        tipologia: validatedData.tipologia,\n        n_conduttori: validatedData.n_conduttori ? validatedData.n_conduttori.toString() : \"0\", // Invia come stringa\n        sezione: validatedData.sezione ? validatedData.sezione.toString() : \"0\", // Invia come stringa\n        metri_teorici: parseFloat(validatedData.metri_teorici) || 0,\n        ubicazione_partenza: validatedData.ubicazione_partenza,\n        ubicazione_arrivo: validatedData.ubicazione_arrivo,\n        stato_installazione: validatedData.stato_installazione || 'Da installare',\n\n        // Campi opzionali\n        metratura_reale: validatedData.metratura_reale ? parseFloat(validatedData.metratura_reale) : null,\n        id_bobina: validatedData.id_bobina || null,\n\n        // Altri campi che potrebbero essere utili\n        sistema: validatedData.sistema || null,\n        colore_cavo: validatedData.colore_cavo || null,\n        utenza_partenza: validatedData.utenza_partenza || null,\n        utenza_arrivo: validatedData.utenza_arrivo || null,\n        descrizione_utenza_partenza: validatedData.descrizione_utenza_partenza || null,\n        descrizione_utenza_arrivo: validatedData.descrizione_utenza_arrivo || null,\n        sh: validatedData.sh || 'N',\n        responsabile_posa: validatedData.responsabile_posa || null,\n        note: validatedData.note || null\n      };\n\n      console.log('Dati da inviare al server dopo la validazione:', dataToSend);\n\n      try {\n        // Invia i dati al server\n        console.log('Tentativo di invio dati al server...');\n\n        // Verifica che cantiereId sia valido\n        if (!cantiereId) {\n          throw new Error('ID cantiere non valido o mancante');\n        }\n\n        // Usa axios direttamente per avere più controllo\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('Token di autenticazione mancante. Effettua nuovamente il login.');\n        }\n\n        console.log(`Invio richiesta POST a ${config.API_URL}/cavi/${cantiereId}`);\n        console.log('Dati inviati:', JSON.stringify(dataToSend, null, 2));\n\n        try {\n          // Tenta di inviare la richiesta\n          const response = await axios.post(`${config.API_URL}/cavi/${cantiereId}`, dataToSend, {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 30000 // Aumentato a 30 secondi per dare più tempo al server\n          });\n\n          console.log('Risposta dal server:', response.data);\n\n          // Imposta loading a false prima di chiudere il dialog\n          setLoading(false);\n\n          // Notifica il successo per chiudere il dialog o reindirizzare\n          onSuccess('Cavo aggiunto con successo');\n\n          // Reindirizza solo se non è in un dialog\n          if (!isDialog) {\n            console.log('Reindirizzamento a visualizza cavi...');\n            try {\n              // Usa navigate invece di window.location per un reindirizzamento più pulito\n              redirectToVisualizzaCavi(navigate);\n            } catch (navError) {\n              console.error('Errore durante il reindirizzamento:', navError);\n              // Fallback: usa window.location solo se navigate fallisce\n              window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n            }\n          }\n        } catch (error) {\n          console.error('Errore durante l\\'invio dei dati al server:', error);\n\n          // Se è un errore di rete, verifica se il cavo è stato inserito senza mostrare subito un errore\n          if (!error.response || error.code === 'ECONNABORTED' || error.message.includes('Network Error')) {\n            // Manteniamo lo stato di caricamento attivo senza mostrare errori\n            console.log('Rilevato possibile errore di rete, verifica in corso...');\n\n            // Attendi un secondo e poi verifica se il cavo è stato inserito\n            setTimeout(async () => {\n              try {\n                // Verifica se il cavo esiste\n                const checkResponse = await axios.get(`${config.API_URL}/cavi/${cantiereId}/check/${dataToSend.id_cavo}`, {\n                  headers: {\n                    'Authorization': `Bearer ${token}`\n                  },\n                  timeout: 5000\n                });\n\n                if (checkResponse.data && checkResponse.data.exists) {\n                  // Il cavo è stato inserito con successo\n\n                  // Imposta loading a false prima di chiudere il dialog\n                  setLoading(false);\n\n                  // Notifica il successo per chiudere il dialog o reindirizzare\n                  onSuccess('Cavo aggiunto con successo');\n\n                  // Reindirizza solo se non è in un dialog\n                  if (!isDialog) {\n                    console.log('Reindirizzamento a visualizza cavi...');\n                    try {\n                      // Usa navigate invece di window.location per un reindirizzamento più pulito\n                      redirectToVisualizzaCavi(navigate);\n                    } catch (navError) {\n                      console.error('Errore durante il reindirizzamento:', navError);\n                      // Fallback: usa window.location solo se navigate fallisce\n                      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n                    }\n                  }\n                } else {\n                  // Il cavo non è stato inserito, ora mostriamo l'errore\n                  onError('Il cavo non è stato inserito a causa di un problema di connessione. Riprova.');\n                  setLoading(false);\n                }\n              } catch (checkError) {\n                console.error('Errore durante la verifica:', checkError);\n                onError('Impossibile verificare se il cavo è stato inserito. Riprova.');\n                setLoading(false);\n              }\n            }, 1000);\n          } else {\n            // Per altri tipi di errori, mostra il messaggio di errore\n            let errorMessage = 'Errore durante l\\'aggiunta del cavo';\n\n            if (error.response && error.response.data) {\n              const responseData = error.response.data;\n              if (responseData.detail) {\n                errorMessage = responseData.detail;\n              } else if (typeof responseData === 'string') {\n                errorMessage = responseData;\n              } else {\n                errorMessage = JSON.stringify(responseData);\n              }\n            } else if (error.message) {\n              errorMessage = error.message;\n            }\n\n            onError(errorMessage);\n            setLoading(false);\n          }\n        }\n      } catch (error) {\n        console.error('Errore durante la preparazione della richiesta:', error);\n        let errorMessage = error.message || 'Errore durante l\\'aggiunta del cavo';\n        onError(errorMessage);\n        setLoading(false);\n      }\n    } catch (error) {\n      console.error('Errore durante l\\'aggiunta del cavo:', error);\n      onError(error.detail || 'Errore durante l\\'aggiunta del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n\n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n      {loadingRevisione ? (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      ) : (\n        <>\n          {hasWarnings && (\n            <>\n              <Alert\n                severity=\"warning\"\n                icon={<WarningIcon />}\n                sx={{ mb: isDialog ? 0.5 : 2, py: isDialog ? 0.3 : 1 }}\n              >\n                <Typography variant=\"subtitle2\" sx={{ fontSize: isDialog ? '0.8rem' : '0.875rem' }}>\n                  Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\n                </Typography>\n              </Alert>\n\n              {/* Mostra avvisi specifici */}\n              {formWarnings.network_error && (\n                <Alert\n                  severity=\"error\"\n                  sx={{ mb: isDialog ? 0.5 : 3, py: isDialog ? 0.3 : 1 }}\n                >\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold' }}>\n                    {formWarnings.network_error}\n                  </Typography>\n                </Alert>\n              )}\n            </>\n          )}\n\n          <Paper sx={{ p: isDialog ? 0.5 : 3, mb: isDialog ? 0.5 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '0.8rem' : '1.25rem', mb: isDialog ? 0.3 : 1 }}>\n              Informazioni Generali\n            </Typography>\n            <Grid container spacing={isDialog ? 0.5 : 2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.id_cavo}\n                  helperText={formErrors.id_cavo}\n                  inputProps={{ style: { textTransform: 'uppercase' } }}\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sistema\"\n                  label=\"Sistema\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sistema}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sistema}\n                  helperText={formErrors.sistema}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 0.5 : 3, mb: isDialog ? 0.5 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '0.8rem' : '1.25rem', mb: isDialog ? 0.3 : 1 }}>\n              Caratteristiche Tecniche\n            </Typography>\n            <Grid container spacing={isDialog ? 0.5 : 2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"colore_cavo\"\n                  label=\"Colore Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.colore_cavo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.colore_cavo}\n                  helperText={formErrors.colore_cavo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"Numero Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_conduttori}\n                  helperText={formErrors.n_conduttori || formWarnings.n_conduttori}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.n_conduttori ? 'orange' : undefined }\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione || formWarnings.sezione}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.sezione ? 'orange' : undefined }\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <FormControl fullWidth error={!!formErrors.sh}>\n                  <InputLabel id=\"sh-label\">Schermato (S/N)</InputLabel>\n                  <Select\n                    labelId=\"sh-label\"\n                    name=\"sh\"\n                    value={formData.sh || 'N'}\n                    label=\"Schermato (S/N)\"\n                    onChange={handleFormChange}\n                  >\n                    <MenuItem value=\"S\">S</MenuItem>\n                    <MenuItem value=\"N\">N</MenuItem>\n                  </Select>\n                  <FormHelperText>Opzionale (default: N)</FormHelperText>\n                  {formErrors.sh && <FormHelperText error>{formErrors.sh}</FormHelperText>}\n                </FormControl>\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 0.5 : 3, mb: isDialog ? 0.5 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '0.8rem' : '1.25rem', mb: isDialog ? 0.3 : 1 }}>\n              Ubicazione Partenza\n            </Typography>\n            <Grid container spacing={isDialog ? 0.5 : 2}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_partenza\"\n                  label=\"Ubicazione Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_partenza}\n                  helperText={formErrors.ubicazione_partenza}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_partenza\"\n                  label=\"Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_partenza}\n                  helperText={formErrors.utenza_partenza}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_partenza\"\n                  label=\"Descrizione Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_partenza}\n                  helperText={formErrors.descrizione_utenza_partenza}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 0.5 : 3, mb: isDialog ? 0.5 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '0.8rem' : '1.25rem', mb: isDialog ? 0.3 : 1 }}>\n              Ubicazione Arrivo\n            </Typography>\n            <Grid container spacing={isDialog ? 0.5 : 2}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_arrivo\"\n                  label=\"Ubicazione Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_arrivo}\n                  helperText={formErrors.ubicazione_arrivo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_arrivo\"\n                  label=\"Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_arrivo}\n                  helperText={formErrors.utenza_arrivo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_arrivo\"\n                  label=\"Descrizione Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_arrivo}\n                  helperText={formErrors.descrizione_utenza_arrivo}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 0.5 : 3, mb: isDialog ? 0.5 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '0.8rem' : '1.25rem', mb: isDialog ? 0.3 : 1 }}>\n              Metratura\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_teorici\"\n                  label=\"Metri Teorici\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_teorici}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_teorici}\n                  helperText={formErrors.metri_teorici}\n                />\n              </Grid>\n\n            </Grid>\n          </Paper>\n\n          {!isDialog && (\n            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 2 }}>\n              <Button\n                variant=\"outlined\"\n                color=\"secondary\"\n                size=\"large\"\n                onClick={handleCancel}\n                disabled={loading}\n                sx={{ minWidth: 150 }}\n              >\n                Annulla\n              </Button>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                color=\"primary\"\n                size=\"large\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n                sx={{ minWidth: 150 }}\n              >\n                {loading ? <CircularProgress size={24} /> : 'Salva Cavo'}\n              </Button>\n            </Box>\n          )}\n          {isDialog && (\n            <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                color=\"primary\"\n                size=\"medium\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n                sx={{ minWidth: 120 }}\n              >\n                {loading ? <CircularProgress size={20} /> : 'Salva'}\n              </Button>\n            </Box>\n          )}\n        </>\n      )}\n    </Box>\n  );\n};\n\nexport default AggiungiCavoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SAASC,IAAI,IAAIC,QAAQ,EAAEC,OAAO,IAAIC,WAAW,QAAQ,qBAAqB;AAC9E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AACtF,SAASC,wBAAwB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvE,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,QAAQ,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACjF,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC;IACvC4C,OAAO,EAAE,EAAE;IAEXC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,EAAE,EAAE,EAAE;IACNC,mBAAmB,EAAE,EAAE;IACvBC,eAAe,EAAE,EAAE;IACnBC,2BAA2B,EAAE,EAAE;IAC/BC,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE,EAAE;IACjBC,yBAAyB,EAAE,EAAE;IAC7BC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,GAAG;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,mBAAmB,EAAE;EACvB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACiE,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACdwC,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0B,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA5B,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC2B,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,MAAME,gBAAgB,GAAG,CAAC,CAAC;IAC3B,IAAIH,IAAI,KAAK,iBAAiB,EAAE;MAC9BG,gBAAgB,CAACC,YAAY,GAAGC,UAAU,CAAChC,QAAQ,CAACgB,aAAa,IAAI,CAAC,CAAC;IACzE;IAEA,MAAMiB,MAAM,GAAGnD,aAAa,CAAC6C,IAAI,EAAEC,KAAK,EAAEE,gBAAgB,CAAC;;IAE3D;IACAR,aAAa,CAACY,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACP,IAAI,GAAG,CAACM,MAAM,CAACE,KAAK,GAAGF,MAAM,CAACG,OAAO,GAAG;IAC3C,CAAC,CAAC,CAAC;;IAEH;IACAZ,eAAe,CAACU,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACP,IAAI,GAAGM,MAAM,CAACI,OAAO,GAAGJ,MAAM,CAACG,OAAO,GAAG;IAC5C,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI7C,QAAQ,EAAE;MACZ;MACA;IACF;IACA;IACAT,wBAAwB,CAACW,QAAQ,CAAC;EACpC,CAAC;;EAID;EACA,MAAM4C,YAAY,GAAG,MAAOb,CAAC,IAAK;IAChCA,CAAC,CAACc,cAAc,CAAC,CAAC;IAClB3C,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA4C,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE1C,QAAQ,CAAC;MAC/D,MAAM2C,UAAU,GAAG9D,gBAAgB,CAACmB,QAAQ,CAAC;MAC7CyC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,UAAU,CAAC;MAEjD,IAAI,CAACA,UAAU,CAACC,OAAO,EAAE;QACvBH,OAAO,CAACI,KAAK,CAAC,wBAAwB,EAAEF,UAAU,CAACG,MAAM,CAAC;QAC1DxB,aAAa,CAACqB,UAAU,CAACG,MAAM,CAAC;QAChCtB,eAAe,CAACmB,UAAU,CAACI,QAAQ,CAAC;QACpClD,UAAU,CAAC,KAAK,CAAC;QACjBL,OAAO,CAAC,kEAAkE,CAAC;QAC3E;MACF;;MAEA;MACA,MAAMwD,aAAa,GAAGL,UAAU,CAACK,aAAa;;MAE9C;MACAA,aAAa,CAAC9C,OAAO,GAAG8C,aAAa,CAAC9C,OAAO,CAAC+C,WAAW,CAAC,CAAC;;MAE3D;MACA;MACA;;MAEA;MACA,MAAMC,cAAc,GAAG,CACrB,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,EAC7E,qBAAqB,EAAE,mBAAmB,EAAE,qBAAqB,CAClE;MAED,MAAMC,aAAa,GAAGD,cAAc,CAACE,MAAM,CAACC,KAAK,IAAI,CAACL,aAAa,CAACK,KAAK,CAAC,CAAC;MAC3E,IAAIF,aAAa,CAACG,MAAM,GAAG,CAAC,EAAE;QAC5B,MAAM,IAAIC,KAAK,CAAC,+BAA+BJ,aAAa,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAC5E;;MAEA;MACA,MAAMC,UAAU,GAAG;QACjB,GAAGT,aAAa;QAChB;QACA9C,OAAO,EAAE8C,aAAa,CAAC9C,OAAO,CAAC+C,WAAW,CAAC,CAAC;QAC5C7C,OAAO,EAAE4C,aAAa,CAAC5C,OAAO;QAC9BE,SAAS,EAAE0C,aAAa,CAAC1C,SAAS;QAClCC,YAAY,EAAEyC,aAAa,CAACzC,YAAY,GAAGyC,aAAa,CAACzC,YAAY,CAACmD,QAAQ,CAAC,CAAC,GAAG,GAAG;QAAE;QACxFlD,OAAO,EAAEwC,aAAa,CAACxC,OAAO,GAAGwC,aAAa,CAACxC,OAAO,CAACkD,QAAQ,CAAC,CAAC,GAAG,GAAG;QAAE;QACzE1C,aAAa,EAAEgB,UAAU,CAACgB,aAAa,CAAChC,aAAa,CAAC,IAAI,CAAC;QAC3DN,mBAAmB,EAAEsC,aAAa,CAACtC,mBAAmB;QACtDG,iBAAiB,EAAEmC,aAAa,CAACnC,iBAAiB;QAClDO,mBAAmB,EAAE4B,aAAa,CAAC5B,mBAAmB,IAAI,eAAe;QAEzE;QACAH,eAAe,EAAE+B,aAAa,CAAC/B,eAAe,GAAGe,UAAU,CAACgB,aAAa,CAAC/B,eAAe,CAAC,GAAG,IAAI;QACjGE,SAAS,EAAE6B,aAAa,CAAC7B,SAAS,IAAI,IAAI;QAE1C;QACAhB,OAAO,EAAE6C,aAAa,CAAC7C,OAAO,IAAI,IAAI;QACtCE,WAAW,EAAE2C,aAAa,CAAC3C,WAAW,IAAI,IAAI;QAC9CM,eAAe,EAAEqC,aAAa,CAACrC,eAAe,IAAI,IAAI;QACtDG,aAAa,EAAEkC,aAAa,CAAClC,aAAa,IAAI,IAAI;QAClDF,2BAA2B,EAAEoC,aAAa,CAACpC,2BAA2B,IAAI,IAAI;QAC9EG,yBAAyB,EAAEiC,aAAa,CAACjC,yBAAyB,IAAI,IAAI;QAC1EN,EAAE,EAAEuC,aAAa,CAACvC,EAAE,IAAI,GAAG;QAC3BS,iBAAiB,EAAE8B,aAAa,CAAC9B,iBAAiB,IAAI,IAAI;QAC1DyC,IAAI,EAAEX,aAAa,CAACW,IAAI,IAAI;MAC9B,CAAC;MAEDlB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEe,UAAU,CAAC;MAEzE,IAAI;QACF;QACAhB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;;QAEnD;QACA,IAAI,CAACpD,UAAU,EAAE;UACf,MAAM,IAAIiE,KAAK,CAAC,mCAAmC,CAAC;QACtD;;QAEA;QACA,MAAMK,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI,CAACF,KAAK,EAAE;UACV,MAAM,IAAIL,KAAK,CAAC,iEAAiE,CAAC;QACpF;QAEAd,OAAO,CAACC,GAAG,CAAC,0BAA0B/D,MAAM,CAACoF,OAAO,SAASzE,UAAU,EAAE,CAAC;QAC1EmD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEsB,IAAI,CAACC,SAAS,CAACR,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAEjE,IAAI;UACF;UACA,MAAMS,QAAQ,GAAG,MAAMxF,KAAK,CAACyF,IAAI,CAAC,GAAGxF,MAAM,CAACoF,OAAO,SAASzE,UAAU,EAAE,EAAEmE,UAAU,EAAE;YACpFW,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClC,eAAe,EAAE,UAAUR,KAAK;YAClC,CAAC;YACDS,OAAO,EAAE,KAAK,CAAC;UACjB,CAAC,CAAC;UAEF5B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEwB,QAAQ,CAACI,IAAI,CAAC;;UAElD;UACAzE,UAAU,CAAC,KAAK,CAAC;;UAEjB;UACAN,SAAS,CAAC,4BAA4B,CAAC;;UAEvC;UACA,IAAI,CAACE,QAAQ,EAAE;YACbgD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;YACpD,IAAI;cACF;cACA1D,wBAAwB,CAACW,QAAQ,CAAC;YACpC,CAAC,CAAC,OAAO4E,QAAQ,EAAE;cACjB9B,OAAO,CAACI,KAAK,CAAC,qCAAqC,EAAE0B,QAAQ,CAAC;cAC9D;cACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuBpF,UAAU,kBAAkB;YAC5E;UACF;QACF,CAAC,CAAC,OAAOuD,KAAK,EAAE;UACdJ,OAAO,CAACI,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;;UAEnE;UACA,IAAI,CAACA,KAAK,CAACqB,QAAQ,IAAIrB,KAAK,CAAC8B,IAAI,KAAK,cAAc,IAAI9B,KAAK,CAACT,OAAO,CAACwC,QAAQ,CAAC,eAAe,CAAC,EAAE;YAC/F;YACAnC,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;;YAEtE;YACAmC,UAAU,CAAC,YAAY;cACrB,IAAI;gBACF;gBACA,MAAMC,aAAa,GAAG,MAAMpG,KAAK,CAACqG,GAAG,CAAC,GAAGpG,MAAM,CAACoF,OAAO,SAASzE,UAAU,UAAUmE,UAAU,CAACvD,OAAO,EAAE,EAAE;kBACxGkE,OAAO,EAAE;oBACP,eAAe,EAAE,UAAUR,KAAK;kBAClC,CAAC;kBACDS,OAAO,EAAE;gBACX,CAAC,CAAC;gBAEF,IAAIS,aAAa,CAACR,IAAI,IAAIQ,aAAa,CAACR,IAAI,CAACU,MAAM,EAAE;kBACnD;;kBAEA;kBACAnF,UAAU,CAAC,KAAK,CAAC;;kBAEjB;kBACAN,SAAS,CAAC,4BAA4B,CAAC;;kBAEvC;kBACA,IAAI,CAACE,QAAQ,EAAE;oBACbgD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;oBACpD,IAAI;sBACF;sBACA1D,wBAAwB,CAACW,QAAQ,CAAC;oBACpC,CAAC,CAAC,OAAO4E,QAAQ,EAAE;sBACjB9B,OAAO,CAACI,KAAK,CAAC,qCAAqC,EAAE0B,QAAQ,CAAC;sBAC9D;sBACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuBpF,UAAU,kBAAkB;oBAC5E;kBACF;gBACF,CAAC,MAAM;kBACL;kBACAE,OAAO,CAAC,8EAA8E,CAAC;kBACvFK,UAAU,CAAC,KAAK,CAAC;gBACnB;cACF,CAAC,CAAC,OAAOoF,UAAU,EAAE;gBACnBxC,OAAO,CAACI,KAAK,CAAC,6BAA6B,EAAEoC,UAAU,CAAC;gBACxDzF,OAAO,CAAC,8DAA8D,CAAC;gBACvEK,UAAU,CAAC,KAAK,CAAC;cACnB;YACF,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,MAAM;YACL;YACA,IAAIqF,YAAY,GAAG,qCAAqC;YAExD,IAAIrC,KAAK,CAACqB,QAAQ,IAAIrB,KAAK,CAACqB,QAAQ,CAACI,IAAI,EAAE;cACzC,MAAMa,YAAY,GAAGtC,KAAK,CAACqB,QAAQ,CAACI,IAAI;cACxC,IAAIa,YAAY,CAACC,MAAM,EAAE;gBACvBF,YAAY,GAAGC,YAAY,CAACC,MAAM;cACpC,CAAC,MAAM,IAAI,OAAOD,YAAY,KAAK,QAAQ,EAAE;gBAC3CD,YAAY,GAAGC,YAAY;cAC7B,CAAC,MAAM;gBACLD,YAAY,GAAGlB,IAAI,CAACC,SAAS,CAACkB,YAAY,CAAC;cAC7C;YACF,CAAC,MAAM,IAAItC,KAAK,CAACT,OAAO,EAAE;cACxB8C,YAAY,GAAGrC,KAAK,CAACT,OAAO;YAC9B;YAEA5C,OAAO,CAAC0F,YAAY,CAAC;YACrBrF,UAAU,CAAC,KAAK,CAAC;UACnB;QACF;MACF,CAAC,CAAC,OAAOgD,KAAK,EAAE;QACdJ,OAAO,CAACI,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;QACvE,IAAIqC,YAAY,GAAGrC,KAAK,CAACT,OAAO,IAAI,qCAAqC;QACzE5C,OAAO,CAAC0F,YAAY,CAAC;QACrBrF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DrD,OAAO,CAACqD,KAAK,CAACuC,MAAM,IAAI,qCAAqC,CAAC;IAChE,CAAC,SAAS;MACRvF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwF,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAChE,YAAY,CAAC,CAAC+B,MAAM,GAAG,CAAC;EAExD,oBACEpE,OAAA,CAAC1B,GAAG;IAACgI,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAElD,YAAa;IAACmD,UAAU;IAAAC,QAAA,EACrD7F,gBAAgB,gBACfZ,OAAA,CAAC1B,GAAG;MAACoI,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAC5DzG,OAAA,CAAChB,gBAAgB;QAAA8H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,gBAENjH,OAAA,CAAAE,SAAA;MAAAuG,QAAA,GACGN,WAAW,iBACVnG,OAAA,CAAAE,SAAA;QAAAuG,QAAA,gBACEzG,OAAA,CAACjB,KAAK;UACJmI,QAAQ,EAAC,SAAS;UAClBC,IAAI,eAAEnH,OAAA,CAACV,WAAW;YAAAwH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBP,EAAE,EAAE;YAAEU,EAAE,EAAE7G,QAAQ,GAAG,GAAG,GAAG,CAAC;YAAE8G,EAAE,EAAE9G,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAkG,QAAA,eAEvDzG,OAAA,CAACf,UAAU;YAACqI,OAAO,EAAC,WAAW;YAACZ,EAAE,EAAE;cAAEa,QAAQ,EAAEhH,QAAQ,GAAG,QAAQ,GAAG;YAAW,CAAE;YAAAkG,QAAA,EAAC;UAEpF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAGP5E,YAAY,CAACmF,aAAa,iBACzBxH,OAAA,CAACjB,KAAK;UACJmI,QAAQ,EAAC,OAAO;UAChBR,EAAE,EAAE;YAAEU,EAAE,EAAE7G,QAAQ,GAAG,GAAG,GAAG,CAAC;YAAE8G,EAAE,EAAE9G,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAkG,QAAA,eAEvDzG,OAAA,CAACf,UAAU;YAACqI,OAAO,EAAC,WAAW;YAACZ,EAAE,EAAE;cAAEe,UAAU,EAAE;YAAO,CAAE;YAAAhB,QAAA,EACxDpE,YAAY,CAACmF;UAAa;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR;MAAA,eACD,CACH,eAEDjH,OAAA,CAACd,KAAK;QAACwH,EAAE,EAAE;UAAEgB,CAAC,EAAEnH,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAE6G,EAAE,EAAE7G,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAEoH,SAAS,EAAEpH,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAkG,QAAA,gBACxFzG,OAAA,CAACf,UAAU;UAACqI,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAEhH,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAE6G,EAAE,EAAE7G,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAkG,QAAA,EAAC;QAEjH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjH,OAAA,CAACvB,IAAI;UAACoJ,SAAS;UAACC,OAAO,EAAEvH,QAAQ,GAAG,GAAG,GAAG,CAAE;UAAAkG,QAAA,gBAC1CzG,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACzB,SAAS;cACRkE,IAAI,EAAC,SAAS;cACdyF,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5E,KAAK,EAAE5B,QAAQ,CAACE,OAAQ;cACxBoH,QAAQ,EAAE7F,gBAAiB;cAC3B8F,QAAQ;cACR1E,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACnB,OAAQ;cAC5BsH,UAAU,EAAEnG,UAAU,CAACnB,OAAQ;cAC/BuH,UAAU,EAAE;gBAAEC,KAAK,EAAE;kBAAEC,aAAa,EAAE;gBAAY;cAAE;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEPjH,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACzB,SAAS;cACRkE,IAAI,EAAC,SAAS;cACdyF,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5E,KAAK,EAAE5B,QAAQ,CAACG,OAAQ;cACxBmH,QAAQ,EAAE7F,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAAClB,OAAQ;cAC5BqH,UAAU,EAAEnG,UAAU,CAAClB;YAAQ;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjH,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACzB,SAAS;cACRkE,IAAI,EAAC,SAAS;cACdyF,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5E,KAAK,EAAE5B,QAAQ,CAACI,OAAQ;cACxBkH,QAAQ,EAAE7F,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACjB,OAAQ;cAC5BoH,UAAU,EAAEnG,UAAU,CAACjB;YAAQ;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERjH,OAAA,CAACd,KAAK;QAACwH,EAAE,EAAE;UAAEgB,CAAC,EAAEnH,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAE6G,EAAE,EAAE7G,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAEoH,SAAS,EAAEpH,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAkG,QAAA,gBACxFzG,OAAA,CAACf,UAAU;UAACqI,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAEhH,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAE6G,EAAE,EAAE7G,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAkG,QAAA,EAAC;QAEjH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjH,OAAA,CAACvB,IAAI;UAACoJ,SAAS;UAACC,OAAO,EAAEvH,QAAQ,GAAG,GAAG,GAAG,CAAE;UAAAkG,QAAA,gBAC1CzG,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACzB,SAAS;cACRkE,IAAI,EAAC,aAAa;cAClByF,KAAK,EAAC,aAAa;cACnBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5E,KAAK,EAAE5B,QAAQ,CAACK,WAAY;cAC5BiH,QAAQ,EAAE7F,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAAChB,WAAY;cAChCmH,UAAU,EAAEnG,UAAU,CAAChB;YAAY;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjH,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACzB,SAAS;cACRkE,IAAI,EAAC,WAAW;cAChByF,KAAK,EAAC,WAAW;cACjBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5E,KAAK,EAAE5B,QAAQ,CAACM,SAAU;cAC1BgH,QAAQ,EAAE7F,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACf,SAAU;cAC9BkH,UAAU,EAAEnG,UAAU,CAACf;YAAU;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjH,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACzB,SAAS;cACRkE,IAAI,EAAC,cAAc;cACnByF,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5E,KAAK,EAAE5B,QAAQ,CAACO,YAAa;cAC7B+G,QAAQ,EAAE7F,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACd,YAAa;cACjCiH,UAAU,EAAEnG,UAAU,CAACd,YAAY,IAAIgB,YAAY,CAAChB,YAAa;cACjEqH,mBAAmB,EAAE;gBACnBF,KAAK,EAAE;kBAAEG,KAAK,EAAEtG,YAAY,CAAChB,YAAY,GAAG,QAAQ,GAAGuH;gBAAU;cACnE;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjH,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACzB,SAAS;cACRkE,IAAI,EAAC,SAAS;cACdyF,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5E,KAAK,EAAE5B,QAAQ,CAACQ,OAAQ;cACxB8G,QAAQ,EAAE7F,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACb,OAAQ;cAC5BgH,UAAU,EAAEnG,UAAU,CAACb,OAAO,IAAIe,YAAY,CAACf,OAAQ;cACvDoH,mBAAmB,EAAE;gBACnBF,KAAK,EAAE;kBAAEG,KAAK,EAAEtG,YAAY,CAACf,OAAO,GAAG,QAAQ,GAAGsH;gBAAU;cAC9D;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjH,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACtB,WAAW;cAACyJ,SAAS;cAACxE,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACZ,EAAG;cAAAkF,QAAA,gBAC5CzG,OAAA,CAACrB,UAAU;gBAACkK,EAAE,EAAC,UAAU;gBAAApC,QAAA,EAAC;cAAe;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtDjH,OAAA,CAACpB,MAAM;gBACLkK,OAAO,EAAC,UAAU;gBAClBrG,IAAI,EAAC,IAAI;gBACTC,KAAK,EAAE5B,QAAQ,CAACS,EAAE,IAAI,GAAI;gBAC1B2G,KAAK,EAAC,iBAAiB;gBACvBE,QAAQ,EAAE7F,gBAAiB;gBAAAkE,QAAA,gBAE3BzG,OAAA,CAACnB,QAAQ;kBAAC6D,KAAK,EAAC,GAAG;kBAAA+D,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAChCjH,OAAA,CAACnB,QAAQ;kBAAC6D,KAAK,EAAC,GAAG;kBAAA+D,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACTjH,OAAA,CAAClB,cAAc;gBAAA2H,QAAA,EAAC;cAAsB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAgB,CAAC,EACtD9E,UAAU,CAACZ,EAAE,iBAAIvB,OAAA,CAAClB,cAAc;gBAAC6E,KAAK;gBAAA8C,QAAA,EAAEtE,UAAU,CAACZ;cAAE;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERjH,OAAA,CAACd,KAAK;QAACwH,EAAE,EAAE;UAAEgB,CAAC,EAAEnH,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAE6G,EAAE,EAAE7G,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAEoH,SAAS,EAAEpH,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAkG,QAAA,gBACxFzG,OAAA,CAACf,UAAU;UAACqI,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAEhH,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAE6G,EAAE,EAAE7G,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAkG,QAAA,EAAC;QAEjH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjH,OAAA,CAACvB,IAAI;UAACoJ,SAAS;UAACC,OAAO,EAAEvH,QAAQ,GAAG,GAAG,GAAG,CAAE;UAAAkG,QAAA,gBAC1CzG,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACzB,SAAS;cACRkE,IAAI,EAAC,qBAAqB;cAC1ByF,KAAK,EAAC,qBAAqB;cAC3BC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5E,KAAK,EAAE5B,QAAQ,CAACU,mBAAoB;cACpC4G,QAAQ,EAAE7F,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACX,mBAAoB;cACxC8G,UAAU,EAAEnG,UAAU,CAACX;YAAoB;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjH,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACzB,SAAS;cACRkE,IAAI,EAAC,iBAAiB;cACtByF,KAAK,EAAC,iBAAiB;cACvBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5E,KAAK,EAAE5B,QAAQ,CAACW,eAAgB;cAChC2G,QAAQ,EAAE7F,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACV,eAAgB;cACpC6G,UAAU,EAAEnG,UAAU,CAACV;YAAgB;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjH,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACzB,SAAS;cACRkE,IAAI,EAAC,6BAA6B;cAClCyF,KAAK,EAAC,6BAA6B;cACnCC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5E,KAAK,EAAE5B,QAAQ,CAACY,2BAA4B;cAC5C0G,QAAQ,EAAE7F,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACT,2BAA4B;cAChD4G,UAAU,EAAEnG,UAAU,CAACT;YAA4B;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERjH,OAAA,CAACd,KAAK;QAACwH,EAAE,EAAE;UAAEgB,CAAC,EAAEnH,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAE6G,EAAE,EAAE7G,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAEoH,SAAS,EAAEpH,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAkG,QAAA,gBACxFzG,OAAA,CAACf,UAAU;UAACqI,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAEhH,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAE6G,EAAE,EAAE7G,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAkG,QAAA,EAAC;QAEjH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjH,OAAA,CAACvB,IAAI;UAACoJ,SAAS;UAACC,OAAO,EAAEvH,QAAQ,GAAG,GAAG,GAAG,CAAE;UAAAkG,QAAA,gBAC1CzG,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACzB,SAAS;cACRkE,IAAI,EAAC,mBAAmB;cACxByF,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5E,KAAK,EAAE5B,QAAQ,CAACa,iBAAkB;cAClCyG,QAAQ,EAAE7F,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACR,iBAAkB;cACtC2G,UAAU,EAAEnG,UAAU,CAACR;YAAkB;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjH,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACzB,SAAS;cACRkE,IAAI,EAAC,eAAe;cACpByF,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5E,KAAK,EAAE5B,QAAQ,CAACc,aAAc;cAC9BwG,QAAQ,EAAE7F,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACP,aAAc;cAClC0G,UAAU,EAAEnG,UAAU,CAACP;YAAc;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjH,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACzB,SAAS;cACRkE,IAAI,EAAC,2BAA2B;cAChCyF,KAAK,EAAC,2BAA2B;cACjCC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5E,KAAK,EAAE5B,QAAQ,CAACe,yBAA0B;cAC1CuG,QAAQ,EAAE7F,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACN,yBAA0B;cAC9CyG,UAAU,EAAEnG,UAAU,CAACN;YAA0B;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERjH,OAAA,CAACd,KAAK;QAACwH,EAAE,EAAE;UAAEgB,CAAC,EAAEnH,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAE6G,EAAE,EAAE7G,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAEoH,SAAS,EAAEpH,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAkG,QAAA,gBACxFzG,OAAA,CAACf,UAAU;UAACqI,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAEhH,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAE6G,EAAE,EAAE7G,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAkG,QAAA,EAAC;QAEjH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjH,OAAA,CAACvB,IAAI;UAACoJ,SAAS;UAACC,OAAO,EAAEvH,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAkG,QAAA,eACxCzG,OAAA,CAACvB,IAAI;YAACsJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvBzG,OAAA,CAACzB,SAAS;cACRkE,IAAI,EAAC,eAAe;cACpByF,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClB5E,KAAK,EAAE5B,QAAQ,CAACgB,aAAc;cAC9BsG,QAAQ,EAAE7F,gBAAiB;cAC3B8F,QAAQ;cACR1E,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACL,aAAc;cAClCwG,UAAU,EAAEnG,UAAU,CAACL;YAAc;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAEP,CAAC1G,QAAQ,iBACRP,OAAA,CAAC1B,GAAG;QAACoI,EAAE,EAAE;UAAEqC,EAAE,EAAE,CAAC;UAAEpC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEoC,GAAG,EAAE;QAAE,CAAE;QAAAvC,QAAA,gBACpEzG,OAAA,CAACxB,MAAM;UACL8I,OAAO,EAAC,UAAU;UAClBqB,KAAK,EAAC,WAAW;UACjBM,IAAI,EAAC,OAAO;UACZC,OAAO,EAAE9F,YAAa;UACtB+F,QAAQ,EAAEzI,OAAQ;UAClBgG,EAAE,EAAE;YAAE0C,QAAQ,EAAE;UAAI,CAAE;UAAA3C,QAAA,EACvB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjH,OAAA,CAACxB,MAAM;UACL6K,IAAI,EAAC,QAAQ;UACb/B,OAAO,EAAC,WAAW;UACnBqB,KAAK,EAAC,SAAS;UACfM,IAAI,EAAC,OAAO;UACZK,SAAS,eAAEtJ,OAAA,CAACZ,QAAQ;YAAA0H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBkC,QAAQ,EAAEzI,OAAQ;UAClBgG,EAAE,EAAE;YAAE0C,QAAQ,EAAE;UAAI,CAAE;UAAA3C,QAAA,EAErB/F,OAAO,gBAAGV,OAAA,CAAChB,gBAAgB;YAACiK,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAY;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EACA1G,QAAQ,iBACPP,OAAA,CAAC1B,GAAG;QAACoI,EAAE,EAAE;UAAEqC,EAAE,EAAE,CAAC;UAAEpC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEoC,GAAG,EAAE;QAAE,CAAE;QAAAvC,QAAA,eACtEzG,OAAA,CAACxB,MAAM;UACL6K,IAAI,EAAC,QAAQ;UACb/B,OAAO,EAAC,WAAW;UACnBqB,KAAK,EAAC,SAAS;UACfM,IAAI,EAAC,QAAQ;UACbK,SAAS,eAAEtJ,OAAA,CAACZ,QAAQ;YAAA0H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBkC,QAAQ,EAAEzI,OAAQ;UAClBgG,EAAE,EAAE;YAAE0C,QAAQ,EAAE;UAAI,CAAE;UAAA3C,QAAA,EAErB/F,OAAO,gBAAGV,OAAA,CAAChB,gBAAgB;YAACiK,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA,eACD;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzG,EAAA,CAzlBIL,gBAAgB;EAAA,QACHZ,WAAW;AAAA;AAAAgK,EAAA,GADxBpJ,gBAAgB;AA2lBtB,eAAeA,gBAAgB;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}