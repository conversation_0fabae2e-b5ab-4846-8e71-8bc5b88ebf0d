{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"помалку од секунда\",\n    other: \"помалку од {{count}} секунди\"\n  },\n  xSeconds: {\n    one: \"1 секунда\",\n    other: \"{{count}} секунди\"\n  },\n  halfAMinute: \"половина минута\",\n  lessThanXMinutes: {\n    one: \"помалку од минута\",\n    other: \"помалку од {{count}} минути\"\n  },\n  xMinutes: {\n    one: \"1 минута\",\n    other: \"{{count}} минути\"\n  },\n  aboutXHours: {\n    one: \"околу 1 час\",\n    other: \"околу {{count}} часа\"\n  },\n  xHours: {\n    one: \"1 час\",\n    other: \"{{count}} часа\"\n  },\n  xDays: {\n    one: \"1 ден\",\n    other: \"{{count}} дена\"\n  },\n  aboutXWeeks: {\n    one: \"околу 1 недела\",\n    other: \"околу {{count}} месеци\"\n  },\n  xWeeks: {\n    one: \"1 недела\",\n    other: \"{{count}} недели\"\n  },\n  aboutXMonths: {\n    one: \"околу 1 месец\",\n    other: \"околу {{count}} недели\"\n  },\n  xMonths: {\n    one: \"1 месец\",\n    other: \"{{count}} месеци\"\n  },\n  aboutXYears: {\n    one: \"околу 1 година\",\n    other: \"околу {{count}} години\"\n  },\n  xYears: {\n    one: \"1 година\",\n    other: \"{{count}} години\"\n  },\n  overXYears: {\n    one: \"повеќе од 1 година\",\n    other: \"повеќе од {{count}} години\"\n  },\n  almostXYears: {\n    one: \"безмалку 1 година\",\n    other: \"безмалку {{count}} години\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"за \" + result;\n    } else {\n      return \"пред \" + result;\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/mk/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"помалку од секунда\",\n    other: \"помалку од {{count}} секунди\",\n  },\n\n  xSeconds: {\n    one: \"1 секунда\",\n    other: \"{{count}} секунди\",\n  },\n\n  halfAMinute: \"половина минута\",\n\n  lessThanXMinutes: {\n    one: \"помалку од минута\",\n    other: \"помалку од {{count}} минути\",\n  },\n\n  xMinutes: {\n    one: \"1 минута\",\n    other: \"{{count}} минути\",\n  },\n\n  aboutXHours: {\n    one: \"околу 1 час\",\n    other: \"околу {{count}} часа\",\n  },\n\n  xHours: {\n    one: \"1 час\",\n    other: \"{{count}} часа\",\n  },\n\n  xDays: {\n    one: \"1 ден\",\n    other: \"{{count}} дена\",\n  },\n\n  aboutXWeeks: {\n    one: \"околу 1 недела\",\n    other: \"околу {{count}} месеци\",\n  },\n\n  xWeeks: {\n    one: \"1 недела\",\n    other: \"{{count}} недели\",\n  },\n\n  aboutXMonths: {\n    one: \"околу 1 месец\",\n    other: \"околу {{count}} недели\",\n  },\n\n  xMonths: {\n    one: \"1 месец\",\n    other: \"{{count}} месеци\",\n  },\n\n  aboutXYears: {\n    one: \"околу 1 година\",\n    other: \"околу {{count}} години\",\n  },\n\n  xYears: {\n    one: \"1 година\",\n    other: \"{{count}} години\",\n  },\n\n  overXYears: {\n    one: \"повеќе од 1 година\",\n    other: \"повеќе од {{count}} години\",\n  },\n\n  almostXYears: {\n    one: \"безмалку 1 година\",\n    other: \"безмалку {{count}} години\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"за \" + result;\n    } else {\n      return \"пред \" + result;\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,iBAAiB;EAE9BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAO,OAAO,GAAGA,MAAM;IACzB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}