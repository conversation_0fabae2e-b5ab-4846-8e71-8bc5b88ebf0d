{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'Praėjusį' eeee p\",\n  yesterday: \"'<PERSON><PERSON><PERSON>' p\",\n  today: \"'Šiandien' p\",\n  tomorrow: \"'Rytoj' p\",\n  nextWeek: 'eeee p',\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/lt/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'Praėjusį' eeee p\",\n  yesterday: \"'<PERSON><PERSON><PERSON>' p\",\n  today: \"'Šiandien' p\",\n  tomorrow: \"'Rytoj' p\",\n  nextWeek: 'eeee p',\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,mBAAmB;EAC7BC,SAAS,EAAE,WAAW;EACtBC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,WAAW;EACrBC,QAAQ,EAAE,QAAQ;EAClBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}