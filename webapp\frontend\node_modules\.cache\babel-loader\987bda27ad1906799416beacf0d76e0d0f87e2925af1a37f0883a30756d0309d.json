{"ast": null, "code": "import { formatDistance } from \"./ar-MA/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./ar-MA/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./ar-MA/_lib/formatRelative.mjs\";\nimport { localize } from \"./ar-MA/_lib/localize.mjs\";\nimport { match } from \"./ar-MA/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Arabic locale (Moroccan Arabic).\n * @language Moroccan Arabic\n * @iso-639-2 ara\n * <AUTHOR> [@rramiachraf](https://github.com/rramiachraf)\n */\nexport const arMA = {\n  code: \"ar-MA\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    // Monday is 1\n    weekStartsOn: 1,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default arMA;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "arMA", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ar-MA.mjs"], "sourcesContent": ["import { formatDistance } from \"./ar-MA/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./ar-MA/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./ar-MA/_lib/formatRelative.mjs\";\nimport { localize } from \"./ar-MA/_lib/localize.mjs\";\nimport { match } from \"./ar-MA/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Arabic locale (Moroccan Arabic).\n * @language Moroccan Arabic\n * @iso-639-2 ara\n * <AUTHOR> [@rramiachraf](https://github.com/rramiachraf)\n */\nexport const arMA = {\n  code: \"ar-MA\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    // Monday is 1\n    weekStartsOn: 1,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default arMA;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,iCAAiC;AAChE,SAASC,UAAU,QAAQ,6BAA6B;AACxD,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,KAAK,QAAQ,wBAAwB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACP;IACAC,YAAY,EAAE,CAAC;IACfC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}