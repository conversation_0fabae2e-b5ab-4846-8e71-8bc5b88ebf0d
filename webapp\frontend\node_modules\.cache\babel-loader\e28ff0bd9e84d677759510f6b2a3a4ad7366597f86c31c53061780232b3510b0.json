{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\PosaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Paper, Button, IconButton, Alert, Grid, Card, CardContent, CardActions, Snackbar, Dialog } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon, Home as HomeIcon, Cable as CableIcon, Edit as EditIcon, Add as AddIcon, Delete as DeleteIcon, Engineering as EngineeringIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PosaCaviPage = () => {\n  _s();\n  const {\n    isImpersonating\n  } = useAuth();\n  const {\n    openEliminaCavoDialog,\n    setOpenEliminaCavoDialog\n  } = useGlobalContext();\n  const navigate = useNavigate();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Stato per le notifiche\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n\n  // Gestisce le notifiche\n  const handleSuccess = message => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n  const handleError = message => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  // Naviga alle sottopagine\n  const navigateToSubpage = path => {\n    navigate(`/dashboard/cavi/posa/${path}`);\n  };\n  if (!cantiereId || isNaN(cantiereId)) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: \"Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 22\n        }, this),\n        onClick: handleBackToCantieri,\n        children: \"Torna ai Cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToCantieri,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          children: \"Posa Cavi e Collegamenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Cantiere: \", cantiereName, \" (ID: \", cantiereId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 24\n          }, this),\n          onClick: handleBackToCantieri,\n          children: \"Torna ai Cantieri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                children: \"Inserisci metri posati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Registra i metri di cavo posati durante l'installazione. Aggiorna lo stato del cavo e la bobina associata.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: \"primary\",\n              onClick: () => navigateToSubpage('inserisci-metri'),\n              children: \"Apri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(EditIcon, {\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                children: \"Modifica cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Modifica le caratteristiche di un cavo esistente nel cantiere.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: \"primary\",\n              onClick: () => navigateToSubpage('modifica-cavo'),\n              children: \"Apri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(AddIcon, {\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                children: \"Aggiungi nuovo cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Aggiungi un nuovo cavo al cantiere con tutte le sue caratteristiche.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: \"primary\",\n              onClick: () => navigateToSubpage('aggiungi-cavo'),\n              children: \"Apri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(DeleteIcon, {\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                children: \"Elimina cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Elimina un cavo dal cantiere o marcalo come SPARE se gi\\xE0 posato.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: \"primary\",\n              onClick: () => setOpenEliminaCavoDialog(true),\n              children: \"Apri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(EditIcon, {\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                children: \"Modifica bobina cavo posato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Modifica la bobina associata a un cavo gi\\xE0 posato.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: \"primary\",\n              onClick: () => navigateToSubpage('modifica-bobina'),\n              children: \"Apri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(EngineeringIcon, {\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                children: \"Gestisci collegamenti cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Gestisci i collegamenti di partenza e arrivo dei cavi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: \"primary\",\n              onClick: () => navigateToSubpage('collegamenti'),\n              children: \"Apri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openEliminaCavoDialog,\n      onClose: () => setOpenEliminaCavoDialog(false),\n      fullWidth: true,\n      maxWidth: \"md\",\n      children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n        cantiereId: cantiereId,\n        onSuccess: message => {\n          handleSuccess(message);\n          setOpenEliminaCavoDialog(false);\n        },\n        onError: handleError,\n        initialOption: \"eliminaCavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openSnackbar,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: alertSeverity,\n        sx: {\n          width: '100%'\n        },\n        children: alertMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s(PosaCaviPage, \"2U4PvY1dNRUn+g1PnAbOVNa29OQ=\", false, function () {\n  return [useAuth, useGlobalContext, useNavigate];\n});\n_c = PosaCaviPage;\nexport default PosaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"PosaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Snackbar", "Dialog", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Home", "HomeIcon", "Cable", "CableIcon", "Edit", "EditIcon", "Add", "AddIcon", "Delete", "DeleteIcon", "Engineering", "EngineeringIcon", "useNavigate", "useAuth", "useGlobalContext", "AdminHomeButton", "PosaCaviCollegamenti", "jsxDEV", "_jsxDEV", "PosaCaviPage", "_s", "isImpersonating", "openEliminaCavoDialog", "setOpenEliminaCavoDialog", "navigate", "cantiereId", "parseInt", "localStorage", "getItem", "cantiereName", "handleBackToCantieri", "alertMessage", "setAlertMessage", "alertSeverity", "setAlertSeverity", "openSnackbar", "setOpenSnackbar", "handleSuccess", "message", "handleError", "handleCloseSnackbar", "navigateToSubpage", "path", "isNaN", "children", "severity", "sx", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "startIcon", "onClick", "display", "alignItems", "justifyContent", "mr", "window", "location", "reload", "ml", "color", "title", "p", "container", "spacing", "item", "xs", "md", "lg", "height", "flexDirection", "flexGrow", "component", "size", "open", "onClose", "fullWidth", "max<PERSON><PERSON><PERSON>", "onSuccess", "onError", "initialOption", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/PosaCaviPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Snackbar,\n  Dialog\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Home as HomeIcon,\n  Cable as CableIcon,\n  Edit as EditIcon,\n  Add as AddIcon,\n  Delete as DeleteIcon,\n  Engineering as EngineeringIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\n\nconst PosaCaviPage = () => {\n  const { isImpersonating } = useAuth();\n  const { openEliminaCavoDialog, setOpenEliminaCavoDialog } = useGlobalContext();\n  const navigate = useNavigate();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Stato per le notifiche\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n\n  // Gestisce le notifiche\n  const handleSuccess = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  const handleError = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  // Naviga alle sottopagine\n  const navigateToSubpage = (path) => {\n    navigate(`/dashboard/cavi/posa/${path}`);\n  };\n\n  if (!cantiereId || isNaN(cantiereId)) {\n    return (\n      <Box>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\n        </Alert>\n        <Button\n          variant=\"contained\"\n          startIcon={<ArrowBackIcon />}\n          onClick={handleBackToCantieri}\n        >\n          Torna ai Cantieri\n        </Button>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Posa Cavi e Collegamenti\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h6\">\n            Cantiere: {cantiereName} (ID: {cantiereId})\n          </Typography>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<ArrowBackIcon />}\n            onClick={handleBackToCantieri}\n          >\n            Torna ai Cantieri\n          </Button>\n        </Box>\n      </Paper>\n\n      <Grid container spacing={3}>\n        <Grid item xs={12} md={6} lg={4}>\n          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n            <CardContent sx={{ flexGrow: 1 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <CableIcon color=\"primary\" sx={{ mr: 1 }} />\n                <Typography variant=\"h6\" component=\"div\">\n                  Inserisci metri posati\n                </Typography>\n              </Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Registra i metri di cavo posati durante l'installazione. Aggiorna lo stato del cavo e la bobina associata.\n              </Typography>\n            </CardContent>\n            <CardActions>\n              <Button\n                size=\"small\"\n                color=\"primary\"\n                onClick={() => navigateToSubpage('inserisci-metri')}\n              >\n                Apri\n              </Button>\n            </CardActions>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6} lg={4}>\n          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n            <CardContent sx={{ flexGrow: 1 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <EditIcon color=\"primary\" sx={{ mr: 1 }} />\n                <Typography variant=\"h6\" component=\"div\">\n                  Modifica cavo\n                </Typography>\n              </Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Modifica le caratteristiche di un cavo esistente nel cantiere.\n              </Typography>\n            </CardContent>\n            <CardActions>\n              <Button\n                size=\"small\"\n                color=\"primary\"\n                onClick={() => navigateToSubpage('modifica-cavo')}\n              >\n                Apri\n              </Button>\n            </CardActions>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6} lg={4}>\n          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n            <CardContent sx={{ flexGrow: 1 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <AddIcon color=\"primary\" sx={{ mr: 1 }} />\n                <Typography variant=\"h6\" component=\"div\">\n                  Aggiungi nuovo cavo\n                </Typography>\n              </Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Aggiungi un nuovo cavo al cantiere con tutte le sue caratteristiche.\n              </Typography>\n            </CardContent>\n            <CardActions>\n              <Button\n                size=\"small\"\n                color=\"primary\"\n                onClick={() => navigateToSubpage('aggiungi-cavo')}\n              >\n                Apri\n              </Button>\n            </CardActions>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6} lg={4}>\n          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n            <CardContent sx={{ flexGrow: 1 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <DeleteIcon color=\"primary\" sx={{ mr: 1 }} />\n                <Typography variant=\"h6\" component=\"div\">\n                  Elimina cavo\n                </Typography>\n              </Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Elimina un cavo dal cantiere o marcalo come SPARE se già posato.\n              </Typography>\n            </CardContent>\n            <CardActions>\n              <Button\n                size=\"small\"\n                color=\"primary\"\n                onClick={() => setOpenEliminaCavoDialog(true)}\n              >\n                Apri\n              </Button>\n            </CardActions>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6} lg={4}>\n          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n            <CardContent sx={{ flexGrow: 1 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <EditIcon color=\"primary\" sx={{ mr: 1 }} />\n                <Typography variant=\"h6\" component=\"div\">\n                  Modifica bobina cavo posato\n                </Typography>\n              </Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Modifica la bobina associata a un cavo già posato.\n              </Typography>\n            </CardContent>\n            <CardActions>\n              <Button\n                size=\"small\"\n                color=\"primary\"\n                onClick={() => navigateToSubpage('modifica-bobina')}\n              >\n                Apri\n              </Button>\n            </CardActions>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6} lg={4}>\n          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n            <CardContent sx={{ flexGrow: 1 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <EngineeringIcon color=\"primary\" sx={{ mr: 1 }} />\n                <Typography variant=\"h6\" component=\"div\">\n                  Gestisci collegamenti cavo\n                </Typography>\n              </Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Gestisci i collegamenti di partenza e arrivo dei cavi.\n              </Typography>\n            </CardContent>\n            <CardActions>\n              <Button\n                size=\"small\"\n                color=\"primary\"\n                onClick={() => navigateToSubpage('collegamenti')}\n              >\n                Apri\n              </Button>\n            </CardActions>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Dialogo per l'eliminazione dei cavi */}\n      <Dialog\n        open={openEliminaCavoDialog}\n        onClose={() => setOpenEliminaCavoDialog(false)}\n        fullWidth\n        maxWidth=\"md\"\n      >\n        <PosaCaviCollegamenti\n          cantiereId={cantiereId}\n          onSuccess={(message) => {\n            handleSuccess(message);\n            setOpenEliminaCavoDialog(false);\n          }}\n          onError={handleError}\n          initialOption=\"eliminaCavo\"\n        />\n      </Dialog>\n\n      <Snackbar\n        open={openSnackbar}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>\n          {alertMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default PosaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,QAAQ,EACRC,MAAM,QACD,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,oBAAoB,MAAM,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAgB,CAAC,GAAGR,OAAO,CAAC,CAAC;EACrC,MAAM;IAAES,qBAAqB;IAAEC;EAAyB,CAAC,GAAGT,gBAAgB,CAAC,CAAC;EAC9E,MAAMU,QAAQ,GAAGZ,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMa,UAAU,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;EAC3E,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAAI,YAAYH,UAAU,EAAE;;EAE7F;EACA,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IACjCN,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAED;EACA,MAAM,CAACO,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkD,aAAa,EAAEC,gBAAgB,CAAC,GAAGnD,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAMsD,aAAa,GAAIC,OAAO,IAAK;IACjCN,eAAe,CAACM,OAAO,CAAC;IACxBJ,gBAAgB,CAAC,SAAS,CAAC;IAC3BE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMG,WAAW,GAAID,OAAO,IAAK;IAC/BN,eAAe,CAACM,OAAO,CAAC;IACxBJ,gBAAgB,CAAC,OAAO,CAAC;IACzBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMI,mBAAmB,GAAGA,CAAA,KAAM;IAChCJ,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAMK,iBAAiB,GAAIC,IAAI,IAAK;IAClClB,QAAQ,CAAC,wBAAwBkB,IAAI,EAAE,CAAC;EAC1C,CAAC;EAED,IAAI,CAACjB,UAAU,IAAIkB,KAAK,CAAClB,UAAU,CAAC,EAAE;IACpC,oBACEP,OAAA,CAAClC,GAAG;MAAA4D,QAAA,gBACF1B,OAAA,CAAC7B,KAAK;QAACwD,QAAQ,EAAC,OAAO;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAEvC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRjC,OAAA,CAAC/B,MAAM;QACLiE,OAAO,EAAC,WAAW;QACnBC,SAAS,eAAEnC,OAAA,CAACrB,aAAa;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BG,OAAO,EAAExB,oBAAqB;QAAAc,QAAA,EAC/B;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEjC,OAAA,CAAClC,GAAG;IAAA4D,QAAA,gBACF1B,OAAA,CAAClC,GAAG;MAAC8D,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEQ,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAb,QAAA,gBACzF1B,OAAA,CAAClC,GAAG;QAAC8D,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAZ,QAAA,gBACjD1B,OAAA,CAAC9B,UAAU;UAACkE,OAAO,EAAExB,oBAAqB;UAACgB,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,eACvD1B,OAAA,CAACrB,aAAa;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACbjC,OAAA,CAACjC,UAAU;UAACmE,OAAO,EAAC,IAAI;UAAAR,QAAA,EAAC;QAEzB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjC,OAAA,CAAC9B,UAAU;UACTkE,OAAO,EAAEA,CAAA,KAAMK,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCf,EAAE,EAAE;YAAEgB,EAAE,EAAE;UAAE,CAAE;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAC,oBAAoB;UAAApB,QAAA,eAE1B1B,OAAA,CAACnB,WAAW;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNjC,OAAA,CAACH,eAAe;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAENjC,OAAA,CAAChC,KAAK;MAAC4D,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEkB,CAAC,EAAE;MAAE,CAAE;MAAArB,QAAA,eACzB1B,OAAA,CAAClC,GAAG;QAAC8D,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,eAAe;UAAED,UAAU,EAAE;QAAS,CAAE;QAAAZ,QAAA,gBAClF1B,OAAA,CAACjC,UAAU;UAACmE,OAAO,EAAC,IAAI;UAAAR,QAAA,GAAC,YACb,EAACf,YAAY,EAAC,QAAM,EAACJ,UAAU,EAAC,GAC5C;QAAA;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjC,OAAA,CAAC/B,MAAM;UACLiE,OAAO,EAAC,WAAW;UACnBW,KAAK,EAAC,SAAS;UACfV,SAAS,eAAEnC,OAAA,CAACrB,aAAa;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BG,OAAO,EAAExB,oBAAqB;UAAAc,QAAA,EAC/B;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAERjC,OAAA,CAAC5B,IAAI;MAAC4E,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAvB,QAAA,gBACzB1B,OAAA,CAAC5B,IAAI;QAAC8E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3B,QAAA,eAC9B1B,OAAA,CAAC3B,IAAI;UAACuD,EAAE,EAAE;YAAE0B,MAAM,EAAE,MAAM;YAAEjB,OAAO,EAAE,MAAM;YAAEkB,aAAa,EAAE;UAAS,CAAE;UAAA7B,QAAA,gBACrE1B,OAAA,CAAC1B,WAAW;YAACsD,EAAE,EAAE;cAAE4B,QAAQ,EAAE;YAAE,CAAE;YAAA9B,QAAA,gBAC/B1B,OAAA,CAAClC,GAAG;cAAC8D,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAET,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACxD1B,OAAA,CAACf,SAAS;gBAAC4D,KAAK,EAAC,SAAS;gBAACjB,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5CjC,OAAA,CAACjC,UAAU;gBAACmE,OAAO,EAAC,IAAI;gBAACuB,SAAS,EAAC,KAAK;gBAAA/B,QAAA,EAAC;cAEzC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjC,OAAA,CAACjC,UAAU;cAACmE,OAAO,EAAC,OAAO;cAACW,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdjC,OAAA,CAACzB,WAAW;YAAAmD,QAAA,eACV1B,OAAA,CAAC/B,MAAM;cACLyF,IAAI,EAAC,OAAO;cACZb,KAAK,EAAC,SAAS;cACfT,OAAO,EAAEA,CAAA,KAAMb,iBAAiB,CAAC,iBAAiB,CAAE;cAAAG,QAAA,EACrD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPjC,OAAA,CAAC5B,IAAI;QAAC8E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3B,QAAA,eAC9B1B,OAAA,CAAC3B,IAAI;UAACuD,EAAE,EAAE;YAAE0B,MAAM,EAAE,MAAM;YAAEjB,OAAO,EAAE,MAAM;YAAEkB,aAAa,EAAE;UAAS,CAAE;UAAA7B,QAAA,gBACrE1B,OAAA,CAAC1B,WAAW;YAACsD,EAAE,EAAE;cAAE4B,QAAQ,EAAE;YAAE,CAAE;YAAA9B,QAAA,gBAC/B1B,OAAA,CAAClC,GAAG;cAAC8D,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAET,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACxD1B,OAAA,CAACb,QAAQ;gBAAC0D,KAAK,EAAC,SAAS;gBAACjB,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CjC,OAAA,CAACjC,UAAU;gBAACmE,OAAO,EAAC,IAAI;gBAACuB,SAAS,EAAC,KAAK;gBAAA/B,QAAA,EAAC;cAEzC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjC,OAAA,CAACjC,UAAU;cAACmE,OAAO,EAAC,OAAO;cAACW,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdjC,OAAA,CAACzB,WAAW;YAAAmD,QAAA,eACV1B,OAAA,CAAC/B,MAAM;cACLyF,IAAI,EAAC,OAAO;cACZb,KAAK,EAAC,SAAS;cACfT,OAAO,EAAEA,CAAA,KAAMb,iBAAiB,CAAC,eAAe,CAAE;cAAAG,QAAA,EACnD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPjC,OAAA,CAAC5B,IAAI;QAAC8E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3B,QAAA,eAC9B1B,OAAA,CAAC3B,IAAI;UAACuD,EAAE,EAAE;YAAE0B,MAAM,EAAE,MAAM;YAAEjB,OAAO,EAAE,MAAM;YAAEkB,aAAa,EAAE;UAAS,CAAE;UAAA7B,QAAA,gBACrE1B,OAAA,CAAC1B,WAAW;YAACsD,EAAE,EAAE;cAAE4B,QAAQ,EAAE;YAAE,CAAE;YAAA9B,QAAA,gBAC/B1B,OAAA,CAAClC,GAAG;cAAC8D,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAET,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACxD1B,OAAA,CAACX,OAAO;gBAACwD,KAAK,EAAC,SAAS;gBAACjB,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1CjC,OAAA,CAACjC,UAAU;gBAACmE,OAAO,EAAC,IAAI;gBAACuB,SAAS,EAAC,KAAK;gBAAA/B,QAAA,EAAC;cAEzC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjC,OAAA,CAACjC,UAAU;cAACmE,OAAO,EAAC,OAAO;cAACW,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdjC,OAAA,CAACzB,WAAW;YAAAmD,QAAA,eACV1B,OAAA,CAAC/B,MAAM;cACLyF,IAAI,EAAC,OAAO;cACZb,KAAK,EAAC,SAAS;cACfT,OAAO,EAAEA,CAAA,KAAMb,iBAAiB,CAAC,eAAe,CAAE;cAAAG,QAAA,EACnD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPjC,OAAA,CAAC5B,IAAI;QAAC8E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3B,QAAA,eAC9B1B,OAAA,CAAC3B,IAAI;UAACuD,EAAE,EAAE;YAAE0B,MAAM,EAAE,MAAM;YAAEjB,OAAO,EAAE,MAAM;YAAEkB,aAAa,EAAE;UAAS,CAAE;UAAA7B,QAAA,gBACrE1B,OAAA,CAAC1B,WAAW;YAACsD,EAAE,EAAE;cAAE4B,QAAQ,EAAE;YAAE,CAAE;YAAA9B,QAAA,gBAC/B1B,OAAA,CAAClC,GAAG;cAAC8D,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAET,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACxD1B,OAAA,CAACT,UAAU;gBAACsD,KAAK,EAAC,SAAS;gBAACjB,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7CjC,OAAA,CAACjC,UAAU;gBAACmE,OAAO,EAAC,IAAI;gBAACuB,SAAS,EAAC,KAAK;gBAAA/B,QAAA,EAAC;cAEzC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjC,OAAA,CAACjC,UAAU;cAACmE,OAAO,EAAC,OAAO;cAACW,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdjC,OAAA,CAACzB,WAAW;YAAAmD,QAAA,eACV1B,OAAA,CAAC/B,MAAM;cACLyF,IAAI,EAAC,OAAO;cACZb,KAAK,EAAC,SAAS;cACfT,OAAO,EAAEA,CAAA,KAAM/B,wBAAwB,CAAC,IAAI,CAAE;cAAAqB,QAAA,EAC/C;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPjC,OAAA,CAAC5B,IAAI;QAAC8E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3B,QAAA,eAC9B1B,OAAA,CAAC3B,IAAI;UAACuD,EAAE,EAAE;YAAE0B,MAAM,EAAE,MAAM;YAAEjB,OAAO,EAAE,MAAM;YAAEkB,aAAa,EAAE;UAAS,CAAE;UAAA7B,QAAA,gBACrE1B,OAAA,CAAC1B,WAAW;YAACsD,EAAE,EAAE;cAAE4B,QAAQ,EAAE;YAAE,CAAE;YAAA9B,QAAA,gBAC/B1B,OAAA,CAAClC,GAAG;cAAC8D,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAET,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACxD1B,OAAA,CAACb,QAAQ;gBAAC0D,KAAK,EAAC,SAAS;gBAACjB,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CjC,OAAA,CAACjC,UAAU;gBAACmE,OAAO,EAAC,IAAI;gBAACuB,SAAS,EAAC,KAAK;gBAAA/B,QAAA,EAAC;cAEzC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjC,OAAA,CAACjC,UAAU;cAACmE,OAAO,EAAC,OAAO;cAACW,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdjC,OAAA,CAACzB,WAAW;YAAAmD,QAAA,eACV1B,OAAA,CAAC/B,MAAM;cACLyF,IAAI,EAAC,OAAO;cACZb,KAAK,EAAC,SAAS;cACfT,OAAO,EAAEA,CAAA,KAAMb,iBAAiB,CAAC,iBAAiB,CAAE;cAAAG,QAAA,EACrD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPjC,OAAA,CAAC5B,IAAI;QAAC8E,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA3B,QAAA,eAC9B1B,OAAA,CAAC3B,IAAI;UAACuD,EAAE,EAAE;YAAE0B,MAAM,EAAE,MAAM;YAAEjB,OAAO,EAAE,MAAM;YAAEkB,aAAa,EAAE;UAAS,CAAE;UAAA7B,QAAA,gBACrE1B,OAAA,CAAC1B,WAAW;YAACsD,EAAE,EAAE;cAAE4B,QAAQ,EAAE;YAAE,CAAE;YAAA9B,QAAA,gBAC/B1B,OAAA,CAAClC,GAAG;cAAC8D,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAET,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACxD1B,OAAA,CAACP,eAAe;gBAACoD,KAAK,EAAC,SAAS;gBAACjB,EAAE,EAAE;kBAAEY,EAAE,EAAE;gBAAE;cAAE;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClDjC,OAAA,CAACjC,UAAU;gBAACmE,OAAO,EAAC,IAAI;gBAACuB,SAAS,EAAC,KAAK;gBAAA/B,QAAA,EAAC;cAEzC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjC,OAAA,CAACjC,UAAU;cAACmE,OAAO,EAAC,OAAO;cAACW,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAAC;YAEnD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdjC,OAAA,CAACzB,WAAW;YAAAmD,QAAA,eACV1B,OAAA,CAAC/B,MAAM;cACLyF,IAAI,EAAC,OAAO;cACZb,KAAK,EAAC,SAAS;cACfT,OAAO,EAAEA,CAAA,KAAMb,iBAAiB,CAAC,cAAc,CAAE;cAAAG,QAAA,EAClD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPjC,OAAA,CAACvB,MAAM;MACLkF,IAAI,EAAEvD,qBAAsB;MAC5BwD,OAAO,EAAEA,CAAA,KAAMvD,wBAAwB,CAAC,KAAK,CAAE;MAC/CwD,SAAS;MACTC,QAAQ,EAAC,IAAI;MAAApC,QAAA,eAEb1B,OAAA,CAACF,oBAAoB;QACnBS,UAAU,EAAEA,UAAW;QACvBwD,SAAS,EAAG3C,OAAO,IAAK;UACtBD,aAAa,CAACC,OAAO,CAAC;UACtBf,wBAAwB,CAAC,KAAK,CAAC;QACjC,CAAE;QACF2D,OAAO,EAAE3C,WAAY;QACrB4C,aAAa,EAAC;MAAa;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAETjC,OAAA,CAACxB,QAAQ;MACPmF,IAAI,EAAE1C,YAAa;MACnBiD,gBAAgB,EAAE,IAAK;MACvBN,OAAO,EAAEtC,mBAAoB;MAC7B6C,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA3C,QAAA,eAE3D1B,OAAA,CAAC7B,KAAK;QAACyF,OAAO,EAAEtC,mBAAoB;QAACK,QAAQ,EAAEZ,aAAc;QAACa,EAAE,EAAE;UAAE0C,KAAK,EAAE;QAAO,CAAE;QAAA5C,QAAA,EACjFb;MAAY;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAvRID,YAAY;EAAA,QACYN,OAAO,EACyBC,gBAAgB,EAC3DF,WAAW;AAAA;AAAA6E,EAAA,GAHxBtE,YAAY;AAyRlB,eAAeA,YAAY;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}