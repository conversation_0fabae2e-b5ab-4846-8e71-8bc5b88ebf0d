{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\charts\\\\BoqChart.js\";\nimport React from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell, ComposedChart, Line, LineChart } from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f',\n  purple: '#9c27b0',\n  teal: '#00695c'\n};\nconst BoqChart = ({\n  data\n}) => {\n  var _data$cavi_per_tipo, _data$bobine_per_tipo;\n  if (!data) return null;\n\n  // Prepara dati per grafici cavi per tipologia\n  const caviData = ((_data$cavi_per_tipo = data.cavi_per_tipo) === null || _data$cavi_per_tipo === void 0 ? void 0 : _data$cavi_per_tipo.map((cavo, index) => {\n    var _cavo$tipologia;\n    return {\n      ...cavo,\n      tipologia_short: ((_cavo$tipologia = cavo.tipologia) === null || _cavo$tipologia === void 0 ? void 0 : _cavo$tipologia.length) > 8 ? cavo.tipologia.substring(0, 8) + '...' : cavo.tipologia,\n      color: Object.values(COLORS)[index % Object.values(COLORS).length],\n      deficit: Math.max(0, cavo.metri_da_posare - (cavo.metri_reali || 0)),\n      surplus: Math.max(0, (cavo.metri_reali || 0) - cavo.metri_da_posare)\n    };\n  })) || [];\n\n  // Prepara dati per grafici bobine disponibili\n  const bobineData = ((_data$bobine_per_tipo = data.bobine_per_tipo) === null || _data$bobine_per_tipo === void 0 ? void 0 : _data$bobine_per_tipo.map((bobina, index) => {\n    var _bobina$tipologia;\n    return {\n      ...bobina,\n      tipologia_short: ((_bobina$tipologia = bobina.tipologia) === null || _bobina$tipologia === void 0 ? void 0 : _bobina$tipologia.length) > 8 ? bobina.tipologia.substring(0, 8) + '...' : bobina.tipologia,\n      color: Object.values(COLORS)[index % Object.values(COLORS).length]\n    };\n  })) || [];\n\n  // Calcola totali per grafici a torta\n  const totaliCavi = caviData.reduce((acc, cavo) => {\n    acc.teorici += cavo.metri_teorici || 0;\n    acc.reali += cavo.metri_reali || 0;\n    acc.da_posare += cavo.metri_da_posare || 0;\n    return acc;\n  }, {\n    teorici: 0,\n    reali: 0,\n    da_posare: 0\n  });\n  const totaliData = [{\n    name: 'Metri Teorici',\n    value: totaliCavi.teorici,\n    color: COLORS.primary\n  }, {\n    name: 'Metri Reali',\n    value: totaliCavi.reali,\n    color: COLORS.success\n  }, {\n    name: 'Metri da Posare',\n    value: totaliCavi.da_posare,\n    color: COLORS.warning\n  }];\n\n  // Analisi deficit/surplus\n  const analisiData = caviData.map(cavo => ({\n    tipologia: cavo.tipologia_short,\n    tipologia_full: cavo.tipologia,\n    deficit: cavo.deficit,\n    surplus: cavo.surplus,\n    necessita_acquisto: cavo.deficit > 0\n  }));\n  const CustomTooltip = ({\n    active,\n    payload,\n    label\n  }) => {\n    if (active && payload && payload.length) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1,\n          border: '1px solid #ccc'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `${label}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), payload.map((entry, index) => /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          style: {\n            color: entry.color\n          },\n          children: `${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderCustomizedLabel = ({\n    cx,\n    cy,\n    midAngle,\n    innerRadius,\n    outerRadius,\n    percent\n  }) => {\n    if (percent < 0.05) return null;\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n    return /*#__PURE__*/_jsxDEV(\"text\", {\n      x: x,\n      y: y,\n      fill: \"white\",\n      textAnchor: x > cx ? 'start' : 'end',\n      dominantBaseline: \"central\",\n      fontSize: \"12\",\n      fontWeight: \"bold\",\n      children: `${(percent * 100).toFixed(0)}%`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mt: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Analisi Bill of Quantities\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: 175\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            align: \"center\",\n            children: \"Distribuzione Metri Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 140,\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: totaliData,\n                cx: \"50%\",\n                cy: \"50%\",\n                labelLine: false,\n                label: renderCustomizedLabel,\n                outerRadius: 80,\n                fill: \"#8884d8\",\n                dataKey: \"value\",\n                children: totaliData.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: entry.color\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: 350\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            align: \"center\",\n            children: \"Metri per Tipologia Cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 280,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: caviData,\n              margin: {\n                top: 20,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"tipologia_short\",\n                angle: -45,\n                textAnchor: \"end\",\n                height: 80\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"metri_teorici\",\n                fill: COLORS.primary,\n                name: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"metri_reali\",\n                fill: COLORS.success,\n                name: \"Metri Reali\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"metri_da_posare\",\n                fill: COLORS.warning,\n                name: \"Metri da Posare\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: 350\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            align: \"center\",\n            children: \"Bobine Disponibili per Tipologia\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 280,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: bobineData,\n              margin: {\n                top: 20,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"tipologia_short\",\n                angle: -45,\n                textAnchor: \"end\",\n                height: 80\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"num_bobine\",\n                fill: COLORS.info,\n                name: \"Numero Bobine\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"metri_disponibili\",\n                fill: COLORS.teal,\n                name: \"Metri Disponibili\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: 350\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            align: \"center\",\n            children: \"Analisi Deficit/Surplus Materiali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 280,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: analisiData,\n              margin: {\n                top: 20,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"tipologia\",\n                angle: -45,\n                textAnchor: \"end\",\n                height: 80\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"deficit\",\n                fill: COLORS.error,\n                name: \"Deficit (da acquistare)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"surplus\",\n                fill: COLORS.success,\n                name: \"Surplus (disponibile)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Statistiche Riassuntive per Tipologia\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: caviData.map((cavo, index) => {\n              var _cavo$metri_teorici, _cavo$metri_reali, _cavo$metri_da_posare;\n              return /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 4,\n                lg: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    border: '1px solid #e0e0e0',\n                    borderRadius: 1,\n                    borderLeft: `4px solid ${cavo.color}`\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    gutterBottom: true,\n                    children: cavo.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [\"Sezione: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: cavo.sezione\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 32\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [\"Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: cavo.num_cavi\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 232,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [\"Teorici: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [(_cavo$metri_teorici = cavo.metri_teorici) === null || _cavo$metri_teorici === void 0 ? void 0 : _cavo$metri_teorici.toFixed(0), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 32\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [\"Reali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [(_cavo$metri_reali = cavo.metri_reali) === null || _cavo$metri_reali === void 0 ? void 0 : _cavo$metri_reali.toFixed(0), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 30\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [\"Da Posare: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [(_cavo$metri_da_posare = cavo.metri_da_posare) === null || _cavo$metri_da_posare === void 0 ? void 0 : _cavo$metri_da_posare.toFixed(0), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 34\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 21\n                  }, this), cavo.deficit > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `Deficit: ${cavo.deficit.toFixed(0)}m`,\n                    color: \"error\",\n                    size: \"small\",\n                    sx: {\n                      mt: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 23\n                  }, this), cavo.surplus > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n                    label: `Surplus: ${cavo.surplus.toFixed(0)}m`,\n                    color: \"success\",\n                    size: \"small\",\n                    sx: {\n                      mt: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), bobineData.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Riepilogo Bobine Disponibili\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: bobineData.map((bobina, index) => {\n              var _bobina$metri_disponi;\n              return /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 4,\n                lg: 3,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    border: '1px solid #e0e0e0',\n                    borderRadius: 1,\n                    borderLeft: `4px solid ${bobina.color}`\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    gutterBottom: true,\n                    children: bobina.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [\"Sezione: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: bobina.sezione\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 34\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [\"Bobine: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: bobina.num_bobine\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 290,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [\"Disponibili: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [(_bobina$metri_disponi = bobina.metri_disponibili) === null || _bobina$metri_disponi === void 0 ? void 0 : _bobina$metri_disponi.toFixed(0), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 38\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: \"Disponibile\",\n                    color: \"success\",\n                    size: \"small\",\n                    sx: {\n                      mt: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n};\n_c = BoqChart;\nexport default BoqChart;\nvar _c;\n$RefreshReg$(_c, \"BoqChart\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "ComposedChart", "Line", "Line<PERSON>hart", "Box", "Typography", "Grid", "Paper", "Chip", "jsxDEV", "_jsxDEV", "COLORS", "primary", "secondary", "success", "warning", "info", "error", "purple", "teal", "<PERSON><PERSON><PERSON><PERSON>", "data", "_data$cavi_per_tipo", "_data$bobine_per_tipo", "caviData", "cavi_per_tipo", "map", "cavo", "index", "_cavo$tipologia", "tipologia_short", "tipologia", "length", "substring", "color", "Object", "values", "deficit", "Math", "max", "metri_da_posare", "metri_reali", "surplus", "bobine<PERSON><PERSON>", "bobine_per_tipo", "bobina", "_bobina$tipologia", "totaliCavi", "reduce", "acc", "<PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "reali", "da_posare", "totaliData", "name", "value", "analisiData", "tipologia_full", "necessita_acquisto", "CustomTooltip", "active", "payload", "label", "sx", "p", "border", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entry", "style", "toFixed", "renderCustomizedLabel", "cx", "cy", "midAngle", "innerRadius", "outerRadius", "percent", "RADIAN", "PI", "radius", "x", "cos", "y", "sin", "fill", "textAnchor", "dominantBaseline", "fontSize", "fontWeight", "mt", "gutterBottom", "container", "spacing", "item", "xs", "md", "height", "align", "width", "labelLine", "dataKey", "content", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angle", "_cavo$metri_teorici", "_cavo$metri_reali", "_cavo$metri_da_posare", "sm", "lg", "textAlign", "borderRadius", "borderLeft", "sezione", "num_cavi", "size", "_bobina$metri_disponi", "num_bobine", "metri_disponibili", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/charts/BoqChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON>Axis,\n  <PERSON><PERSON><PERSON><PERSON>,\n  CartesianGrid,\n  <PERSON>ltip,\n  Legend,\n  ResponsiveContainer,\n  <PERSON><PERSON>hart,\n  Pie,\n  Cell,\n  ComposedChart,\n  Line,\n  LineChart\n} from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\n\nconst COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f',\n  purple: '#9c27b0',\n  teal: '#00695c'\n};\n\nconst BoqChart = ({ data }) => {\n  if (!data) return null;\n\n  // Prepara dati per grafici cavi per tipologia\n  const caviData = data.cavi_per_tipo?.map((cavo, index) => ({\n    ...cavo,\n    tipologia_short: cavo.tipologia?.length > 8 ? cavo.tipologia.substring(0, 8) + '...' : cavo.tipologia,\n    color: Object.values(COLORS)[index % Object.values(COLORS).length],\n    deficit: Math.max(0, cavo.metri_da_posare - (cavo.metri_reali || 0)),\n    surplus: Math.max(0, (cavo.metri_reali || 0) - cavo.metri_da_posare)\n  })) || [];\n\n  // Prepara dati per grafici bobine disponibili\n  const bobineData = data.bobine_per_tipo?.map((bobina, index) => ({\n    ...bobina,\n    tipologia_short: bobina.tipologia?.length > 8 ? bobina.tipologia.substring(0, 8) + '...' : bobina.tipologia,\n    color: Object.values(COLORS)[index % Object.values(COLORS).length]\n  })) || [];\n\n  // Calcola totali per grafici a torta\n  const totaliCavi = caviData.reduce((acc, cavo) => {\n    acc.teorici += cavo.metri_teorici || 0;\n    acc.reali += cavo.metri_reali || 0;\n    acc.da_posare += cavo.metri_da_posare || 0;\n    return acc;\n  }, { teorici: 0, reali: 0, da_posare: 0 });\n\n  const totaliData = [\n    { name: 'Metri Teorici', value: totaliCavi.teorici, color: COLORS.primary },\n    { name: 'Metri Reali', value: totaliCavi.reali, color: COLORS.success },\n    { name: 'Metri da Posare', value: totaliCavi.da_posare, color: COLORS.warning }\n  ];\n\n  // Analisi deficit/surplus\n  const analisiData = caviData.map(cavo => ({\n    tipologia: cavo.tipologia_short,\n    tipologia_full: cavo.tipologia,\n    deficit: cavo.deficit,\n    surplus: cavo.surplus,\n    necessita_acquisto: cavo.deficit > 0\n  }));\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {\n    if (percent < 0.05) return null;\n\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n\n    return (\n      <text \n        x={x} \n        y={y} \n        fill=\"white\" \n        textAnchor={x > cx ? 'start' : 'end'} \n        dominantBaseline=\"central\"\n        fontSize=\"12\"\n        fontWeight=\"bold\"\n      >\n        {`${(percent * 100).toFixed(0)}%`}\n      </text>\n    );\n  };\n\n  return (\n    <Box sx={{ mt: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        Analisi Bill of Quantities\n      </Typography>\n\n      <Grid container spacing={3}>\n        {/* Grafico a torta - Distribuzione Totali */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 175 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Distribuzione Metri Totali\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={140}>\n              <PieChart>\n                <Pie\n                  data={totaliData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={renderCustomizedLabel}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                >\n                  {totaliData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n              </PieChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico a barre - Cavi per Tipologia */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Metri per Tipologia Cavo\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <BarChart data={caviData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"tipologia_short\" angle={-45} textAnchor=\"end\" height={80} />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"metri_teorici\" fill={COLORS.primary} name=\"Metri Teorici\" />\n                <Bar dataKey=\"metri_reali\" fill={COLORS.success} name=\"Metri Reali\" />\n                <Bar dataKey=\"metri_da_posare\" fill={COLORS.warning} name=\"Metri da Posare\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico a barre - Bobine Disponibili */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Bobine Disponibili per Tipologia\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <BarChart data={bobineData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"tipologia_short\" angle={-45} textAnchor=\"end\" height={80} />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"num_bobine\" fill={COLORS.info} name=\"Numero Bobine\" />\n                <Bar dataKey=\"metri_disponibili\" fill={COLORS.teal} name=\"Metri Disponibili\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Analisi Deficit/Surplus */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Analisi Deficit/Surplus Materiali\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <BarChart data={analisiData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"tipologia\" angle={-45} textAnchor=\"end\" height={80} />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"deficit\" fill={COLORS.error} name=\"Deficit (da acquistare)\" />\n                <Bar dataKey=\"surplus\" fill={COLORS.success} name=\"Surplus (disponibile)\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Statistiche Riassuntive */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Statistiche Riassuntive per Tipologia\n            </Typography>\n            <Grid container spacing={2}>\n              {caviData.map((cavo, index) => (\n                <Grid item xs={12} sm={6} md={4} lg={3} key={index}>\n                  <Box sx={{ \n                    textAlign: 'center', \n                    p: 2, \n                    border: '1px solid #e0e0e0', \n                    borderRadius: 1,\n                    borderLeft: `4px solid ${cavo.color}`\n                  }}>\n                    <Typography variant=\"subtitle2\" gutterBottom>\n                      {cavo.tipologia}\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Sezione: <strong>{cavo.sezione}</strong>\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Cavi: <strong>{cavo.num_cavi}</strong>\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Teorici: <strong>{cavo.metri_teorici?.toFixed(0)}m</strong>\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Reali: <strong>{cavo.metri_reali?.toFixed(0)}m</strong>\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Da Posare: <strong>{cavo.metri_da_posare?.toFixed(0)}m</strong>\n                    </Typography>\n                    {cavo.deficit > 0 && (\n                      <Chip \n                        label={`Deficit: ${cavo.deficit.toFixed(0)}m`}\n                        color=\"error\"\n                        size=\"small\"\n                        sx={{ mt: 1 }}\n                      />\n                    )}\n                    {cavo.surplus > 0 && (\n                      <Chip \n                        label={`Surplus: ${cavo.surplus.toFixed(0)}m`}\n                        color=\"success\"\n                        size=\"small\"\n                        sx={{ mt: 1 }}\n                      />\n                    )}\n                  </Box>\n                </Grid>\n              ))}\n            </Grid>\n          </Paper>\n        </Grid>\n\n        {/* Riepilogo Bobine Disponibili */}\n        {bobineData.length > 0 && (\n          <Grid item xs={12}>\n            <Paper sx={{ p: 2 }}>\n              <Typography variant=\"subtitle1\" gutterBottom>\n                Riepilogo Bobine Disponibili\n              </Typography>\n              <Grid container spacing={2}>\n                {bobineData.map((bobina, index) => (\n                  <Grid item xs={12} sm={6} md={4} lg={3} key={index}>\n                    <Box sx={{ \n                      textAlign: 'center', \n                      p: 2, \n                      border: '1px solid #e0e0e0', \n                      borderRadius: 1,\n                      borderLeft: `4px solid ${bobina.color}`\n                    }}>\n                      <Typography variant=\"subtitle2\" gutterBottom>\n                        {bobina.tipologia}\n                      </Typography>\n                      <Typography variant=\"body2\">\n                        Sezione: <strong>{bobina.sezione}</strong>\n                      </Typography>\n                      <Typography variant=\"body2\">\n                        Bobine: <strong>{bobina.num_bobine}</strong>\n                      </Typography>\n                      <Typography variant=\"body2\">\n                        Disponibili: <strong>{bobina.metri_disponibili?.toFixed(0)}m</strong>\n                      </Typography>\n                      <Chip \n                        label=\"Disponibile\"\n                        color=\"success\"\n                        size=\"small\"\n                        sx={{ mt: 1 }}\n                      />\n                    </Box>\n                  </Grid>\n                ))}\n              </Grid>\n            </Paper>\n          </Grid>\n        )}\n      </Grid>\n    </Box>\n  );\n};\n\nexport default BoqChart;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,aAAa,EACbC,IAAI,EACJC,SAAS,QACJ,UAAU;AACjB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,MAAM,GAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE,SAAS;EACjBC,IAAI,EAAE;AACR,CAAC;AAED,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAA,IAAAC,mBAAA,EAAAC,qBAAA;EAC7B,IAAI,CAACF,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA,MAAMG,QAAQ,GAAG,EAAAF,mBAAA,GAAAD,IAAI,CAACI,aAAa,cAAAH,mBAAA,uBAAlBA,mBAAA,CAAoBI,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK;IAAA,IAAAC,eAAA;IAAA,OAAM;MACzD,GAAGF,IAAI;MACPG,eAAe,EAAE,EAAAD,eAAA,GAAAF,IAAI,CAACI,SAAS,cAAAF,eAAA,uBAAdA,eAAA,CAAgBG,MAAM,IAAG,CAAC,GAAGL,IAAI,CAACI,SAAS,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGN,IAAI,CAACI,SAAS;MACrGG,KAAK,EAAEC,MAAM,CAACC,MAAM,CAACzB,MAAM,CAAC,CAACiB,KAAK,GAAGO,MAAM,CAACC,MAAM,CAACzB,MAAM,CAAC,CAACqB,MAAM,CAAC;MAClEK,OAAO,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEZ,IAAI,CAACa,eAAe,IAAIb,IAAI,CAACc,WAAW,IAAI,CAAC,CAAC,CAAC;MACpEC,OAAO,EAAEJ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACZ,IAAI,CAACc,WAAW,IAAI,CAAC,IAAId,IAAI,CAACa,eAAe;IACrE,CAAC;EAAA,CAAC,CAAC,KAAI,EAAE;;EAET;EACA,MAAMG,UAAU,GAAG,EAAApB,qBAAA,GAAAF,IAAI,CAACuB,eAAe,cAAArB,qBAAA,uBAApBA,qBAAA,CAAsBG,GAAG,CAAC,CAACmB,MAAM,EAAEjB,KAAK;IAAA,IAAAkB,iBAAA;IAAA,OAAM;MAC/D,GAAGD,MAAM;MACTf,eAAe,EAAE,EAAAgB,iBAAA,GAAAD,MAAM,CAACd,SAAS,cAAAe,iBAAA,uBAAhBA,iBAAA,CAAkBd,MAAM,IAAG,CAAC,GAAGa,MAAM,CAACd,SAAS,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAGY,MAAM,CAACd,SAAS;MAC3GG,KAAK,EAAEC,MAAM,CAACC,MAAM,CAACzB,MAAM,CAAC,CAACiB,KAAK,GAAGO,MAAM,CAACC,MAAM,CAACzB,MAAM,CAAC,CAACqB,MAAM;IACnE,CAAC;EAAA,CAAC,CAAC,KAAI,EAAE;;EAET;EACA,MAAMe,UAAU,GAAGvB,QAAQ,CAACwB,MAAM,CAAC,CAACC,GAAG,EAAEtB,IAAI,KAAK;IAChDsB,GAAG,CAACC,OAAO,IAAIvB,IAAI,CAACwB,aAAa,IAAI,CAAC;IACtCF,GAAG,CAACG,KAAK,IAAIzB,IAAI,CAACc,WAAW,IAAI,CAAC;IAClCQ,GAAG,CAACI,SAAS,IAAI1B,IAAI,CAACa,eAAe,IAAI,CAAC;IAC1C,OAAOS,GAAG;EACZ,CAAC,EAAE;IAAEC,OAAO,EAAE,CAAC;IAAEE,KAAK,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAE,CAAC,CAAC;EAE1C,MAAMC,UAAU,GAAG,CACjB;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAET,UAAU,CAACG,OAAO;IAAEhB,KAAK,EAAEvB,MAAM,CAACC;EAAQ,CAAC,EAC3E;IAAE2C,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAET,UAAU,CAACK,KAAK;IAAElB,KAAK,EAAEvB,MAAM,CAACG;EAAQ,CAAC,EACvE;IAAEyC,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAET,UAAU,CAACM,SAAS;IAAEnB,KAAK,EAAEvB,MAAM,CAACI;EAAQ,CAAC,CAChF;;EAED;EACA,MAAM0C,WAAW,GAAGjC,QAAQ,CAACE,GAAG,CAACC,IAAI,KAAK;IACxCI,SAAS,EAAEJ,IAAI,CAACG,eAAe;IAC/B4B,cAAc,EAAE/B,IAAI,CAACI,SAAS;IAC9BM,OAAO,EAAEV,IAAI,CAACU,OAAO;IACrBK,OAAO,EAAEf,IAAI,CAACe,OAAO;IACrBiB,kBAAkB,EAAEhC,IAAI,CAACU,OAAO,GAAG;EACrC,CAAC,CAAC,CAAC;EAEH,MAAMuB,aAAa,GAAGA,CAAC;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAM,CAAC,KAAK;IACpD,IAAIF,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAAC9B,MAAM,EAAE;MACvC,oBACEtB,OAAA,CAACH,KAAK;QAACyD,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAiB,CAAE;QAAAC,QAAA,gBAC5CzD,OAAA,CAACL,UAAU;UAAC+D,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,GAAGJ,KAAK;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,EACpDV,OAAO,CAACpC,GAAG,CAAC,CAAC+C,KAAK,EAAE7C,KAAK,kBACxBlB,OAAA,CAACL,UAAU;UAAa+D,OAAO,EAAC,OAAO;UAACM,KAAK,EAAE;YAAExC,KAAK,EAAEuC,KAAK,CAACvC;UAAM,CAAE;UAAAiC,QAAA,EACnE,GAAGM,KAAK,CAAClB,IAAI,KAAK,OAAOkB,KAAK,CAACjB,KAAK,KAAK,QAAQ,GAAGiB,KAAK,CAACjB,KAAK,CAACmB,OAAO,CAAC,CAAC,CAAC,GAAGF,KAAK,CAACjB,KAAK;QAAE,GAD5E5B,KAAK;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMI,qBAAqB,GAAGA,CAAC;IAAEC,EAAE;IAAEC,EAAE;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,WAAW;IAAEC;EAAQ,CAAC,KAAK;IACzF,IAAIA,OAAO,GAAG,IAAI,EAAE,OAAO,IAAI;IAE/B,MAAMC,MAAM,GAAG7C,IAAI,CAAC8C,EAAE,GAAG,GAAG;IAC5B,MAAMC,MAAM,GAAGL,WAAW,GAAG,CAACC,WAAW,GAAGD,WAAW,IAAI,GAAG;IAC9D,MAAMM,CAAC,GAAGT,EAAE,GAAGQ,MAAM,GAAG/C,IAAI,CAACiD,GAAG,CAAC,CAACR,QAAQ,GAAGI,MAAM,CAAC;IACpD,MAAMK,CAAC,GAAGV,EAAE,GAAGO,MAAM,GAAG/C,IAAI,CAACmD,GAAG,CAAC,CAACV,QAAQ,GAAGI,MAAM,CAAC;IAEpD,oBACEzE,OAAA;MACE4E,CAAC,EAAEA,CAAE;MACLE,CAAC,EAAEA,CAAE;MACLE,IAAI,EAAC,OAAO;MACZC,UAAU,EAAEL,CAAC,GAAGT,EAAE,GAAG,OAAO,GAAG,KAAM;MACrCe,gBAAgB,EAAC,SAAS;MAC1BC,QAAQ,EAAC,IAAI;MACbC,UAAU,EAAC,MAAM;MAAA3B,QAAA,EAEhB,GAAG,CAACe,OAAO,GAAG,GAAG,EAAEP,OAAO,CAAC,CAAC,CAAC;IAAG;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEX,CAAC;EAED,oBACE9D,OAAA,CAACN,GAAG;IAAC4D,EAAE,EAAE;MAAE+B,EAAE,EAAE;IAAE,CAAE;IAAA5B,QAAA,gBACjBzD,OAAA,CAACL,UAAU;MAAC+D,OAAO,EAAC,IAAI;MAAC4B,YAAY;MAAA7B,QAAA,EAAC;IAEtC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb9D,OAAA,CAACJ,IAAI;MAAC2F,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA/B,QAAA,gBAEzBzD,OAAA,CAACJ,IAAI;QAAC6F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlC,QAAA,eACvBzD,OAAA,CAACH,KAAK;UAACyD,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEqC,MAAM,EAAE;UAAI,CAAE;UAAAnC,QAAA,gBAC/BzD,OAAA,CAACL,UAAU;YAAC+D,OAAO,EAAC,WAAW;YAAC4B,YAAY;YAACO,KAAK,EAAC,QAAQ;YAAApC,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9D,OAAA,CAACb,mBAAmB;YAAC2G,KAAK,EAAC,MAAM;YAACF,MAAM,EAAE,GAAI;YAAAnC,QAAA,eAC5CzD,OAAA,CAACZ,QAAQ;cAAAqE,QAAA,gBACPzD,OAAA,CAACX,GAAG;gBACFsB,IAAI,EAAEiC,UAAW;gBACjBuB,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACR2B,SAAS,EAAE,KAAM;gBACjB1C,KAAK,EAAEa,qBAAsB;gBAC7BK,WAAW,EAAE,EAAG;gBAChBS,IAAI,EAAC,SAAS;gBACdgB,OAAO,EAAC,OAAO;gBAAAvC,QAAA,EAEdb,UAAU,CAAC5B,GAAG,CAAC,CAAC+C,KAAK,EAAE7C,KAAK,kBAC3BlB,OAAA,CAACV,IAAI;kBAAuB0F,IAAI,EAAEjB,KAAK,CAACvC;gBAAM,GAAnC,QAAQN,KAAK,EAAE;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAsB,CACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9D,OAAA,CAACf,OAAO;gBAACgH,OAAO,eAAEjG,OAAA,CAACkD,aAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC9D,OAAA,CAACd,MAAM;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP9D,OAAA,CAACJ,IAAI;QAAC6F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlC,QAAA,eACvBzD,OAAA,CAACH,KAAK;UAACyD,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEqC,MAAM,EAAE;UAAI,CAAE;UAAAnC,QAAA,gBAC/BzD,OAAA,CAACL,UAAU;YAAC+D,OAAO,EAAC,WAAW;YAAC4B,YAAY;YAACO,KAAK,EAAC,QAAQ;YAAApC,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9D,OAAA,CAACb,mBAAmB;YAAC2G,KAAK,EAAC,MAAM;YAACF,MAAM,EAAE,GAAI;YAAAnC,QAAA,eAC5CzD,OAAA,CAACpB,QAAQ;cAAC+B,IAAI,EAAEG,QAAS;cAACoF,MAAM,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAA7C,QAAA,gBAC5EzD,OAAA,CAAChB,aAAa;gBAACuH,eAAe,EAAC;cAAK;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC9D,OAAA,CAAClB,KAAK;gBAACkH,OAAO,EAAC,iBAAiB;gBAACQ,KAAK,EAAE,CAAC,EAAG;gBAACvB,UAAU,EAAC,KAAK;gBAACW,MAAM,EAAE;cAAG;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5E9D,OAAA,CAACjB,KAAK;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT9D,OAAA,CAACf,OAAO;gBAACgH,OAAO,eAAEjG,OAAA,CAACkD,aAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC9D,OAAA,CAACd,MAAM;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACV9D,OAAA,CAACnB,GAAG;gBAACmH,OAAO,EAAC,eAAe;gBAAChB,IAAI,EAAE/E,MAAM,CAACC,OAAQ;gBAAC2C,IAAI,EAAC;cAAe;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1E9D,OAAA,CAACnB,GAAG;gBAACmH,OAAO,EAAC,aAAa;gBAAChB,IAAI,EAAE/E,MAAM,CAACG,OAAQ;gBAACyC,IAAI,EAAC;cAAa;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtE9D,OAAA,CAACnB,GAAG;gBAACmH,OAAO,EAAC,iBAAiB;gBAAChB,IAAI,EAAE/E,MAAM,CAACI,OAAQ;gBAACwC,IAAI,EAAC;cAAiB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP9D,OAAA,CAACJ,IAAI;QAAC6F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlC,QAAA,eACvBzD,OAAA,CAACH,KAAK;UAACyD,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEqC,MAAM,EAAE;UAAI,CAAE;UAAAnC,QAAA,gBAC/BzD,OAAA,CAACL,UAAU;YAAC+D,OAAO,EAAC,WAAW;YAAC4B,YAAY;YAACO,KAAK,EAAC,QAAQ;YAAApC,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9D,OAAA,CAACb,mBAAmB;YAAC2G,KAAK,EAAC,MAAM;YAACF,MAAM,EAAE,GAAI;YAAAnC,QAAA,eAC5CzD,OAAA,CAACpB,QAAQ;cAAC+B,IAAI,EAAEsB,UAAW;cAACiE,MAAM,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAA7C,QAAA,gBAC9EzD,OAAA,CAAChB,aAAa;gBAACuH,eAAe,EAAC;cAAK;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC9D,OAAA,CAAClB,KAAK;gBAACkH,OAAO,EAAC,iBAAiB;gBAACQ,KAAK,EAAE,CAAC,EAAG;gBAACvB,UAAU,EAAC,KAAK;gBAACW,MAAM,EAAE;cAAG;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5E9D,OAAA,CAACjB,KAAK;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT9D,OAAA,CAACf,OAAO;gBAACgH,OAAO,eAAEjG,OAAA,CAACkD,aAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC9D,OAAA,CAACd,MAAM;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACV9D,OAAA,CAACnB,GAAG;gBAACmH,OAAO,EAAC,YAAY;gBAAChB,IAAI,EAAE/E,MAAM,CAACK,IAAK;gBAACuC,IAAI,EAAC;cAAe;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpE9D,OAAA,CAACnB,GAAG;gBAACmH,OAAO,EAAC,mBAAmB;gBAAChB,IAAI,EAAE/E,MAAM,CAACQ,IAAK;gBAACoC,IAAI,EAAC;cAAmB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP9D,OAAA,CAACJ,IAAI;QAAC6F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAlC,QAAA,eACvBzD,OAAA,CAACH,KAAK;UAACyD,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEqC,MAAM,EAAE;UAAI,CAAE;UAAAnC,QAAA,gBAC/BzD,OAAA,CAACL,UAAU;YAAC+D,OAAO,EAAC,WAAW;YAAC4B,YAAY;YAACO,KAAK,EAAC,QAAQ;YAAApC,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9D,OAAA,CAACb,mBAAmB;YAAC2G,KAAK,EAAC,MAAM;YAACF,MAAM,EAAE,GAAI;YAAAnC,QAAA,eAC5CzD,OAAA,CAACpB,QAAQ;cAAC+B,IAAI,EAAEoC,WAAY;cAACmD,MAAM,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAA7C,QAAA,gBAC/EzD,OAAA,CAAChB,aAAa;gBAACuH,eAAe,EAAC;cAAK;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC9D,OAAA,CAAClB,KAAK;gBAACkH,OAAO,EAAC,WAAW;gBAACQ,KAAK,EAAE,CAAC,EAAG;gBAACvB,UAAU,EAAC,KAAK;gBAACW,MAAM,EAAE;cAAG;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtE9D,OAAA,CAACjB,KAAK;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACT9D,OAAA,CAACf,OAAO;gBAACgH,OAAO,eAAEjG,OAAA,CAACkD,aAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvC9D,OAAA,CAACd,MAAM;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACV9D,OAAA,CAACnB,GAAG;gBAACmH,OAAO,EAAC,SAAS;gBAAChB,IAAI,EAAE/E,MAAM,CAACM,KAAM;gBAACsC,IAAI,EAAC;cAAyB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5E9D,OAAA,CAACnB,GAAG;gBAACmH,OAAO,EAAC,SAAS;gBAAChB,IAAI,EAAE/E,MAAM,CAACG,OAAQ;gBAACyC,IAAI,EAAC;cAAuB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGP9D,OAAA,CAACJ,IAAI;QAAC6F,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAjC,QAAA,eAChBzD,OAAA,CAACH,KAAK;UAACyD,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,gBAClBzD,OAAA,CAACL,UAAU;YAAC+D,OAAO,EAAC,WAAW;YAAC4B,YAAY;YAAA7B,QAAA,EAAC;UAE7C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9D,OAAA,CAACJ,IAAI;YAAC2F,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA/B,QAAA,EACxB3C,QAAQ,CAACE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK;cAAA,IAAAuF,mBAAA,EAAAC,iBAAA,EAAAC,qBAAA;cAAA,oBACxB3G,OAAA,CAACJ,IAAI;gBAAC6F,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACkB,EAAE,EAAE,CAAE;gBAACjB,EAAE,EAAE,CAAE;gBAACkB,EAAE,EAAE,CAAE;gBAAApD,QAAA,eACrCzD,OAAA,CAACN,GAAG;kBAAC4D,EAAE,EAAE;oBACPwD,SAAS,EAAE,QAAQ;oBACnBvD,CAAC,EAAE,CAAC;oBACJC,MAAM,EAAE,mBAAmB;oBAC3BuD,YAAY,EAAE,CAAC;oBACfC,UAAU,EAAE,aAAa/F,IAAI,CAACO,KAAK;kBACrC,CAAE;kBAAAiC,QAAA,gBACAzD,OAAA,CAACL,UAAU;oBAAC+D,OAAO,EAAC,WAAW;oBAAC4B,YAAY;oBAAA7B,QAAA,EACzCxC,IAAI,CAACI;kBAAS;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACb9D,OAAA,CAACL,UAAU;oBAAC+D,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAC,WACjB,eAAAzD,OAAA;sBAAAyD,QAAA,EAASxC,IAAI,CAACgG;oBAAO;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACb9D,OAAA,CAACL,UAAU;oBAAC+D,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAC,QACpB,eAAAzD,OAAA;sBAAAyD,QAAA,EAASxC,IAAI,CAACiG;oBAAQ;sBAAAvD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACb9D,OAAA,CAACL,UAAU;oBAAC+D,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAC,WACjB,eAAAzD,OAAA;sBAAAyD,QAAA,IAAAgD,mBAAA,GAASxF,IAAI,CAACwB,aAAa,cAAAgE,mBAAA,uBAAlBA,mBAAA,CAAoBxC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACb9D,OAAA,CAACL,UAAU;oBAAC+D,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAC,SACnB,eAAAzD,OAAA;sBAAAyD,QAAA,IAAAiD,iBAAA,GAASzF,IAAI,CAACc,WAAW,cAAA2E,iBAAA,uBAAhBA,iBAAA,CAAkBzC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACb9D,OAAA,CAACL,UAAU;oBAAC+D,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAC,aACf,eAAAzD,OAAA;sBAAAyD,QAAA,IAAAkD,qBAAA,GAAS1F,IAAI,CAACa,eAAe,cAAA6E,qBAAA,uBAApBA,qBAAA,CAAsB1C,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC,EACZ7C,IAAI,CAACU,OAAO,GAAG,CAAC,iBACf3B,OAAA,CAACF,IAAI;oBACHuD,KAAK,EAAE,YAAYpC,IAAI,CAACU,OAAO,CAACsC,OAAO,CAAC,CAAC,CAAC,GAAI;oBAC9CzC,KAAK,EAAC,OAAO;oBACb2F,IAAI,EAAC,OAAO;oBACZ7D,EAAE,EAAE;sBAAE+B,EAAE,EAAE;oBAAE;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF,EACA7C,IAAI,CAACe,OAAO,GAAG,CAAC,iBACfhC,OAAA,CAACF,IAAI;oBACHuD,KAAK,EAAE,YAAYpC,IAAI,CAACe,OAAO,CAACiC,OAAO,CAAC,CAAC,CAAC,GAAI;oBAC9CzC,KAAK,EAAC,SAAS;oBACf2F,IAAI,EAAC,OAAO;oBACZ7D,EAAE,EAAE;sBAAE+B,EAAE,EAAE;oBAAE;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC,GA1CqC5C,KAAK;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA2C5C,CAAC;YAAA,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAGN7B,UAAU,CAACX,MAAM,GAAG,CAAC,iBACpBtB,OAAA,CAACJ,IAAI;QAAC6F,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAjC,QAAA,eAChBzD,OAAA,CAACH,KAAK;UAACyD,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,gBAClBzD,OAAA,CAACL,UAAU;YAAC+D,OAAO,EAAC,WAAW;YAAC4B,YAAY;YAAA7B,QAAA,EAAC;UAE7C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9D,OAAA,CAACJ,IAAI;YAAC2F,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA/B,QAAA,EACxBxB,UAAU,CAACjB,GAAG,CAAC,CAACmB,MAAM,EAAEjB,KAAK;cAAA,IAAAkG,qBAAA;cAAA,oBAC5BpH,OAAA,CAACJ,IAAI;gBAAC6F,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACkB,EAAE,EAAE,CAAE;gBAACjB,EAAE,EAAE,CAAE;gBAACkB,EAAE,EAAE,CAAE;gBAAApD,QAAA,eACrCzD,OAAA,CAACN,GAAG;kBAAC4D,EAAE,EAAE;oBACPwD,SAAS,EAAE,QAAQ;oBACnBvD,CAAC,EAAE,CAAC;oBACJC,MAAM,EAAE,mBAAmB;oBAC3BuD,YAAY,EAAE,CAAC;oBACfC,UAAU,EAAE,aAAa7E,MAAM,CAACX,KAAK;kBACvC,CAAE;kBAAAiC,QAAA,gBACAzD,OAAA,CAACL,UAAU;oBAAC+D,OAAO,EAAC,WAAW;oBAAC4B,YAAY;oBAAA7B,QAAA,EACzCtB,MAAM,CAACd;kBAAS;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACb9D,OAAA,CAACL,UAAU;oBAAC+D,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAC,WACjB,eAAAzD,OAAA;sBAAAyD,QAAA,EAAStB,MAAM,CAAC8E;oBAAO;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eACb9D,OAAA,CAACL,UAAU;oBAAC+D,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAC,UAClB,eAAAzD,OAAA;sBAAAyD,QAAA,EAAStB,MAAM,CAACkF;oBAAU;sBAAA1D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC,eACb9D,OAAA,CAACL,UAAU;oBAAC+D,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAC,eACb,eAAAzD,OAAA;sBAAAyD,QAAA,IAAA2D,qBAAA,GAASjF,MAAM,CAACmF,iBAAiB,cAAAF,qBAAA,uBAAxBA,qBAAA,CAA0BnD,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;oBAAA;sBAAAN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eACb9D,OAAA,CAACF,IAAI;oBACHuD,KAAK,EAAC,aAAa;oBACnB7B,KAAK,EAAC,SAAS;oBACf2F,IAAI,EAAC,OAAO;oBACZ7D,EAAE,EAAE;sBAAE+B,EAAE,EAAE;oBAAE;kBAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC,GA1BqC5C,KAAK;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA2B5C,CAAC;YAAA,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACyD,EAAA,GAxRI7G,QAAQ;AA0Rd,eAAeA,QAAQ;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}