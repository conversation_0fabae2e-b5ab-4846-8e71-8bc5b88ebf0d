{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Typography,Paper,Button,Grid,Card,CardContent,Alert,IconButton,Chip,CircularProgress,LinearProgress,Dialog,DialogTitle,DialogContent,DialogActions,Snackbar}from'@mui/material';import InfoIcon from'@mui/icons-material/Info';import RefreshIcon from'@mui/icons-material/Refresh';import{useNavigate}from'react-router-dom';import{useAuth}from'../../context/AuthContext';import{useGlobalContext}from'../../context/GlobalContext';import PosaCaviCollegamenti from'../../components/cavi/PosaCaviCollegamenti';import caviService from'../../services/caviService';import CavoForm from'../../components/cavi/CavoForm';import{normalizeInstallationStatus}from'../../utils/validationUtils';import CaviFilterableTable from'../../components/cavi/CaviFilterableTable';import'./CaviPage.css';import{jsxs as _jsxs,jsx as _jsx}from\"react/jsx-runtime\";const VisualizzaCaviPage=()=>{var _caviAttivi$,_caviAttivi$2,_caviAttivi$3;const{isImpersonating,user}=useAuth();const{openEliminaCavoDialog,setOpenEliminaCavoDialog,openModificaCavoDialog,setOpenModificaCavoDialog,openAggiungiCavoDialog,setOpenAggiungiCavoDialog}=useGlobalContext();const navigate=useNavigate();const[cantiereId,setCantiereId]=useState(null);const[cantiereName,setCantiereName]=useState('');const[caviAttivi,setCaviAttivi]=useState([]);const[caviSpare,setCaviSpare]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);// Stato per le notifiche\nconst[notification,setNotification]=useState({open:false,message:'',severity:'success'});// Rimosso stato viewMode\n// Stato per il dialogo dei dettagli del cavo\nconst[selectedCavo,setSelectedCavo]=useState(null);const[detailsDialogOpen,setDetailsDialogOpen]=useState(false);// Stato per le statistiche\nconst[stats,setStats]=useState({totali:{cavi_attivi:0,cavi_spare:0,cavi_totali:0},metrature:{metri_teorici_totali:0,metri_reali_totali:0,percentuale_completamento:0},stati:[]});const[loadingStats,setLoadingStats]=useState(false);// Rimosso stato per il debug\n// Funzione per caricare gli stati di installazione disponibili\nconst loadStatiInstallazione=()=>{// Usa i valori dell'enum StatoInstallazione\nsetStatiInstallazione(['Installato','Da installare','In corso']);};// Stato per filtri e ordinamento\nconst[filters,setFilters]=useState({stato_installazione:'',tipologia:'',sort_by:'',sort_order:'asc'});// Opzioni per i filtri\nconst[statiInstallazione,setStatiInstallazione]=useState([]);const[tipologieCavi,setTipologieCavi]=useState([]);// Rimossa funzione di debug\n// Funzione per caricare i cavi\n// Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\nconst fetchCavi=async function(){let silentLoading=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;try{if(!silentLoading){setLoading(true);}console.log('Caricamento cavi per cantiere:',cantiereId);// Verifica che cantiereId sia valido\nif(!cantiereId){console.error('fetchCavi: cantiereId non valido:',cantiereId);setError('ID cantiere non valido o mancante. Ricarica la pagina.');setLoading(false);return;}// Recupera il cantiereId dal localStorage come fallback\nlet cantiereIdToUse=cantiereId;if(!cantiereIdToUse){cantiereIdToUse=localStorage.getItem('selectedCantiereId');console.log('Usando cantiereId dal localStorage:',cantiereIdToUse);if(!cantiereIdToUse){console.error('Impossibile trovare un ID cantiere valido');setError('ID cantiere non trovato. Ricarica la pagina.');setLoading(false);return;}}// Carica i cavi attivi\nconsole.log('Caricamento cavi attivi (tipo_cavo=0)...');let attivi=[];try{attivi=await caviService.getCavi(cantiereIdToUse,0,filters);console.log('Cavi attivi caricati:',attivi?attivi.length:0);}catch(attiviError){console.error('Errore nel caricamento dei cavi attivi:',attiviError);// Continua con un array vuoto\nattivi=[];}// Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\nif(attivi&&attivi.length>0){const caviSpareTraAttivi=attivi.filter(cavo=>cavo.modificato_manualmente===3);if(caviSpareTraAttivi.length>0){console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:',caviSpareTraAttivi);}}setCaviAttivi(attivi||[]);// Carica i cavi SPARE con la nuova funzione dedicata\nlet spare=[];try{console.log('Caricamento cavi SPARE con funzione dedicata...');spare=await caviService.getCaviSpare(cantiereIdToUse);console.log('Cavi SPARE caricati con funzione dedicata:',spare?spare.length:0);if(spare&&spare.length>0){console.log('Primo cavo SPARE:',spare[0]);}}catch(spareError){console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:',spareError);// Se fallisce, prova con il metodo standard\ntry{console.log('Tentativo con metodo standard...');spare=await caviService.getCavi(cantiereIdToUse,3);console.log('Cavi SPARE caricati con metodo standard:',spare?spare.length:0);}catch(standardError){console.error('Errore anche con metodo standard:',standardError);// Continua con un array vuoto\nspare=[];}}setCaviSpare(spare||[]);// Carica le statistiche\ntry{console.log('Caricamento statistiche...');const statsData=await caviService.getCaviStats(cantiereIdToUse);console.log('Statistiche caricate:',statsData);// Verifica che statsData abbia la struttura attesa\nif(!statsData||typeof statsData!=='object'){console.error('Statistiche non valide:',statsData);// Imposta un oggetto stats con struttura valida ma vuota\nsetStats({totali:{cavi_attivi:0,cavi_spare:0,cavi_totali:0},metrature:{metri_teorici_totali:0,metri_reali_totali:0,percentuale_completamento:0},stati:[]});}else{// Assicurati che tutte le proprietà necessarie siano presenti\nconst validStats={totali:statsData.totali||{cavi_attivi:0,cavi_spare:0,cavi_totali:0},metrature:statsData.metrature||{metri_teorici_totali:0,metri_reali_totali:0,percentuale_completamento:0},stati:statsData.stati||[]};setStats(validStats);}}catch(statsError){console.error('Errore nel caricamento delle statistiche:',statsError);// Continua con statistiche vuote ma con struttura valida\nsetStats({totali:{cavi_attivi:0,cavi_spare:0,cavi_totali:0},metrature:{metri_teorici_totali:0,metri_reali_totali:0,percentuale_completamento:0},stati:[]});}// Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\nsetError('');}catch(error){console.error('Errore generale nel caricamento dei cavi:',error);setError(`Errore nel caricamento dei cavi: ${error.message||'Errore sconosciuto'}`);// Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\nsetTimeout(()=>{// Verifica se siamo ancora in errore\nif(document.body.textContent.includes('Errore nel caricamento dei cavi')){console.log('Errore persistente, tentativo di ricaricamento della pagina...');window.location.reload();}},5000);// 5 secondi di ritardo\n}finally{if(!silentLoading){setLoading(false);}}};// Carica i dati del cantiere e dei cavi\nuseEffect(()=>{// Carica gli stati di installazione all'avvio\nloadStatiInstallazione();const fetchData=async()=>{try{console.log('Inizializzazione VisualizzaCaviPage...');// Verifica che l'utente sia autenticato\nconst token=localStorage.getItem('token');console.log('Token presente:',!!token);if(!token){setError('Sessione scaduta. Effettua nuovamente il login.');setLoading(false);return;}// Recupera l'ID del cantiere selezionato dal localStorage\nlet selectedCantiereId=localStorage.getItem('selectedCantiereId');let selectedCantiereName=localStorage.getItem('selectedCantiereName');console.log('Cantiere selezionato dal localStorage:',{selectedCantiereId,selectedCantiereName});console.log('Dati utente:',user);// Stampa tutti i dati nel localStorage per debug\nconsole.log('DEBUG - Tutti i dati nel localStorage:');for(let i=0;i<localStorage.length;i++){const key=localStorage.key(i);console.log(`${key}: ${localStorage.getItem(key)}`);}// SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\nif((user===null||user===void 0?void 0:user.role)==='cantieri_user'){console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');// Verifica se l'utente ha un ID cantiere nei dati utente\nif(user.cantiere_id){console.log('Trovato ID cantiere nei dati utente:',user.cantiere_id);selectedCantiereId=user.cantiere_id.toString();selectedCantiereName=user.cantiere_name||`Cantiere ${user.cantiere_id}`;// Salva l'ID e il nome del cantiere nel localStorage\nlocalStorage.setItem('selectedCantiereId',selectedCantiereId);localStorage.setItem('selectedCantiereName',selectedCantiereName);console.log('Salvato ID cantiere nel localStorage:',selectedCantiereId);}else{// Tentativo di recupero dal token JWT\ntry{console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');const token=localStorage.getItem('token');if(token){// Decodifica il token JWT (senza verifica della firma)\nconst base64Url=token.split('.')[1];const base64=base64Url.replace(/-/g,'+').replace(/_/g,'/');const jsonPayload=decodeURIComponent(atob(base64).split('').map(c=>{return'%'+('00'+c.charCodeAt(0).toString(16)).slice(-2);}).join(''));const payload=JSON.parse(jsonPayload);console.log('Payload del token JWT:',payload);if(payload.cantiere_id){console.log('Trovato ID cantiere nel token JWT:',payload.cantiere_id);selectedCantiereId=payload.cantiere_id.toString();// Usa un nome generico se non disponibile\nselectedCantiereName=`Cantiere ${payload.cantiere_id}`;// Salva l'ID e il nome del cantiere nel localStorage\nlocalStorage.setItem('selectedCantiereId',selectedCantiereId);localStorage.setItem('selectedCantiereName',selectedCantiereName);console.log('Salvato ID cantiere nel localStorage:',selectedCantiereId);}}}catch(e){console.error('Errore durante la decodifica del token JWT:',e);}}}// SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\nif(!selectedCantiereId||selectedCantiereId==='undefined'||selectedCantiereId==='null'){console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');// Usa il primo cantiere disponibile (questo è solo per debug)\nselectedCantiereId='1';// Sostituisci con un ID cantiere valido nel tuo database\nselectedCantiereName='Cantiere Debug';// Salva l'ID e il nome del cantiere nel localStorage\nlocalStorage.setItem('selectedCantiereId',selectedCantiereId);localStorage.setItem('selectedCantiereName',selectedCantiereName);console.log('Salvato ID cantiere hardcoded nel localStorage:',selectedCantiereId);}// Verifica finale\nif(!selectedCantiereId){setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');setLoading(false);return;}// Verifica che l'ID del cantiere sia un numero valido\nconst cantiereIdNum=parseInt(selectedCantiereId,10);console.log('ID cantiere convertito a numero:',cantiereIdNum);if(isNaN(cantiereIdNum)){setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);setLoading(false);return;}// Usa il numero convertito, non la stringa\nsetCantiereId(cantiereIdNum);setCantiereName(selectedCantiereName||`Cantiere ${cantiereIdNum}`);// Carica le statistiche dei cavi\ntry{setLoadingStats(true);console.log('Caricamento statistiche cavi per cantiere:',cantiereIdNum);const statsData=await caviService.getCaviStats(cantiereIdNum);console.log('Statistiche cavi caricate:',statsData);setStats(statsData);// Estrai gli stati di installazione e le tipologie per i filtri\nif(statsData&&statsData.stati){const stati=statsData.stati.map(item=>item.stato).filter(stato=>stato!=='Non specificato');setStatiInstallazione(stati);}if(statsData&&statsData.tipologie){const tipologie=statsData.tipologie.map(item=>item.tipologia).filter(tipo=>tipo!=='Non specificata');setTipologieCavi(tipologie);}setLoadingStats(false);}catch(statsError){console.error('Errore nel caricamento delle statistiche:',statsError);setLoadingStats(false);// Non interrompere il flusso se le statistiche falliscono\n}// Carica i cavi attivi con gestione degli errori migliorata\nconsole.log('Caricamento cavi attivi per cantiere:',cantiereIdNum);try{// Imposta un timeout per evitare che la richiesta rimanga bloccata\nconst timeoutPromise=new Promise((_,reject)=>{setTimeout(()=>reject(new Error('Timeout durante il caricamento dei cavi attivi')),30000);// Aumentato a 30 secondi\n});// Esegui la richiesta con un timeout di sicurezza e applica i filtri\nconsole.log('Iniziando chiamata API per cavi attivi con filtri:',filters);const caviPromise=caviService.getCavi(cantiereIdNum,0,filters);const attivi=await Promise.race([caviPromise,timeoutPromise]);console.log('Cavi attivi caricati:',attivi);console.log('Numero di cavi attivi trovati:',attivi?attivi.length:0);if(attivi&&attivi.length>0){console.log('Primo cavo attivo:',attivi[0]);}else{console.warn('Nessun cavo attivo trovato per il cantiere',cantiereIdNum);}setCaviAttivi(attivi||[]);}catch(caviError){console.error('Errore nel caricamento dei cavi attivi:',caviError);console.error('Dettagli errore cavi attivi:',{message:caviError.message,status:caviError.status,data:caviError.data,stack:caviError.stack,code:caviError.code,name:caviError.name,response:caviError.response?{status:caviError.response.status,statusText:caviError.response.statusText,data:caviError.response.data}:'No response'});// Non interrompere il flusso, continua con i cavi spare\nsetCaviAttivi([]);console.warn('Continuazione del flusso dopo errore nei cavi attivi');// Aggiungi un messaggio di errore visibile all'utente\nsetError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);}// Carica i cavi spare con gestione degli errori migliorata\nconsole.log('Caricamento cavi spare per cantiere:',cantiereIdNum);try{// Imposta un timeout per evitare che la richiesta rimanga bloccata\nconst timeoutPromise=new Promise((_,reject)=>{setTimeout(()=>reject(new Error('Timeout durante il caricamento dei cavi spare')),30000);// Aumentato a 30 secondi\n});// Esegui la richiesta con un timeout di sicurezza\nconsole.log('Iniziando chiamata API per cavi spare...');// Non applichiamo i filtri ai cavi spare, solo agli attivi\nconst sparePromise=caviService.getCavi(cantiereIdNum,3);const spare=await Promise.race([sparePromise,timeoutPromise]);console.log('Cavi spare caricati:',spare);console.log('Numero di cavi spare trovati:',spare?spare.length:0);if(spare&&spare.length>0){console.log('Primo cavo spare:',spare[0]);}else{console.warn('Nessun cavo spare trovato per il cantiere',cantiereIdNum);}setCaviSpare(spare||[]);}catch(spareError){console.error('Errore nel caricamento dei cavi spare:',spareError);console.error('Dettagli errore cavi spare:',{message:spareError.message,status:spareError.status,data:spareError.data,stack:spareError.stack,code:spareError.code,name:spareError.name,response:spareError.response?{status:spareError.response.status,statusText:spareError.response.statusText,data:spareError.response.data}:'No response'});// Non interrompere il flusso, imposta un array vuoto\nsetCaviSpare([]);// Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\nif(!error){setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);}}// Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\nsetLoading(false);}catch(err){var _err$response,_err$response2,_err$response3,_err$response4,_err$response5,_err$response5$data;console.error('Errore nel caricamento dei cavi:',err);console.error('Dettagli errore generale:',{message:err.message,status:err.status||((_err$response=err.response)===null||_err$response===void 0?void 0:_err$response.status),data:err.data||((_err$response2=err.response)===null||_err$response2===void 0?void 0:_err$response2.data),stack:err.stack});// Estrai il messaggio di errore dettagliato\nlet errorMessage='Errore sconosciuto';if(err.message&&err.message.includes('ID cantiere non valido')){errorMessage=err.message;}else if(err.status===401||err.status===403||((_err$response3=err.response)===null||_err$response3===void 0?void 0:_err$response3.status)===401||((_err$response4=err.response)===null||_err$response4===void 0?void 0:_err$response4.status)===403){errorMessage='Sessione scaduta o non autorizzata. Effettua nuovamente il login.';}else if((_err$response5=err.response)!==null&&_err$response5!==void 0&&(_err$response5$data=_err$response5.data)!==null&&_err$response5$data!==void 0&&_err$response5$data.detail){// Estrai il messaggio di errore dettagliato dall'API\nerrorMessage=`Errore API: ${err.response.data.detail}`;}else if(err.code==='ERR_NETWORK'){// Errore di rete\nerrorMessage='Network Error. Verifica che il backend sia in esecuzione e accessibile.';}else if(err.message){errorMessage=err.message;}setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);// Imposta array vuoti per evitare errori di rendering\nsetCaviAttivi([]);setCaviSpare([]);}finally{setLoading(false);}};fetchData();},[filters]);// Ricarica i dati quando cambiano i filtri\n// I filtri sono ora gestiti dal componente CaviFilterableTable\n// Funzione per aprire il dialogo dei dettagli del cavo\nconst handleOpenDetails=cavo=>{setSelectedCavo(cavo);setDetailsDialogOpen(true);};// Funzione per chiudere il dialogo dei dettagli del cavo\nconst handleCloseDetails=()=>{setDetailsDialogOpen(false);setSelectedCavo(null);};// Funzione per chiudere la notifica\nconst handleCloseNotification=()=>{setNotification(prev=>({...prev,open:false}));};// Funzione per mostrare una notifica\nconst showNotification=function(message){let severity=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'success';setNotification({open:true,message,severity});};// Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n// La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n// Rimossa funzione handleViewModeChange\n// Renderizza il dialogo dei dettagli del cavo\nconst renderDetailsDialog=()=>{if(!selectedCavo)return null;return/*#__PURE__*/_jsxs(Dialog,{open:detailsDialogOpen,onClose:handleCloseDetails,maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsxs(DialogTitle,{children:[\"Dettagli Cavo: \",selectedCavo.id_cavo]}),/*#__PURE__*/_jsx(DialogContent,{dividers:true,children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Informazioni Generali\"}),/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Sistema:\"}),\" \",selectedCavo.sistema||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Utility:\"}),\" \",selectedCavo.utility||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Tipologia:\"}),\" \",selectedCavo.tipologia||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Colore:\"}),\" \",selectedCavo.colore_cavo||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Formazione:\"}),\" \",selectedCavo.sezione||'N/A']})]}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Partenza\"}),/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Ubicazione:\"}),\" \",selectedCavo.ubicazione_partenza||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Utenza:\"}),\" \",selectedCavo.utenza_partenza||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Descrizione:\"}),\" \",selectedCavo.descrizione_utenza_partenza||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Responsabile:\"}),\" \",selectedCavo.responsabile_partenza||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Comanda:\"}),\" \",selectedCavo.comanda_partenza||'N/A']})]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Arrivo\"}),/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Ubicazione:\"}),\" \",selectedCavo.ubicazione_arrivo||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Utenza:\"}),\" \",selectedCavo.utenza_arrivo||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Descrizione:\"}),\" \",selectedCavo.descrizione_utenza_arrivo||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Responsabile:\"}),\" \",selectedCavo.responsabile_arrivo||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Comanda:\"}),\" \",selectedCavo.comanda_arrivo||'N/A']})]}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Installazione\"}),/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Metri Teorici:\"}),\" \",selectedCavo.metri_teorici||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Metratura Reale:\"}),\" \",selectedCavo.metratura_reale||'0']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Stato:\"}),\" \",normalizeInstallationStatus(selectedCavo.stato_installazione)]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Collegamenti:\"}),\" \",selectedCavo.collegamenti||'0']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Bobina:\"}),\" \",selectedCavo.id_bobina||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Responsabile Posa:\"}),\" \",selectedCavo.responsabile_posa||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Comanda Posa:\"}),\" \",selectedCavo.comanda_posa||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Ultimo Aggiornamento:\"}),\" \",new Date(selectedCavo.timestamp).toLocaleString()]})]})]})]})}),/*#__PURE__*/_jsx(DialogActions,{children:/*#__PURE__*/_jsx(Button,{onClick:handleCloseDetails,children:\"Chiudi\"})})]});};// Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n// Renderizza il pannello delle statistiche\nconst renderStatsPanel=()=>{// Verifica che stats sia definito e abbia la struttura attesa\nif(!stats||!stats.totali||!stats.metrature||!stats.stati){return/*#__PURE__*/_jsxs(Paper,{sx:{mb:3,p:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Statistiche\"}),loadingStats?/*#__PURE__*/_jsx(LinearProgress,{}):/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Nessuna statistica disponibile\"})]});}// Valori predefiniti per evitare errori\nconst totali=stats.totali||{cavi_attivi:0,cavi_spare:0,cavi_totali:0};const metrature=stats.metrature||{metri_teorici_totali:0,metri_reali_totali:0,percentuale_completamento:0};const stati=stats.stati||[];return/*#__PURE__*/_jsxs(Paper,{sx:{mb:3,p:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Statistiche\"}),loadingStats?/*#__PURE__*/_jsx(LinearProgress,{}):/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:4,children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Totali\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Cavi Attivi: \",totali.cavi_attivi||0]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Cavi Spare: \",totali.cavi_spare||0]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Totale Cavi: \",totali.cavi_totali||0]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:4,children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Metrature\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Metri Teorici: \",(metrature.metri_teorici_totali||0).toFixed(2)]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Metri Posati: \",(metrature.metri_reali_totali||0).toFixed(2)]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mt:1},children:[/*#__PURE__*/_jsx(Box,{sx:{width:'100%',mr:1},children:/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:metrature.percentuale_completamento||0,sx:{height:10,borderRadius:5}})}),/*#__PURE__*/_jsx(Box,{sx:{minWidth:35},children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:`${(metrature.percentuale_completamento||0).toFixed(1)}%`})})]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:4,children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Stati\"}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexWrap:'wrap',gap:1},children:stati.length>0?stati.map((stato,index)=>/*#__PURE__*/_jsx(Chip,{label:`${stato.stato||'N/A'}: ${stato.count||0}`,size:\"small\",onClick:()=>{setFilters(prev=>({...prev,stato_installazione:stato.stato==='Non specificato'?'':stato.stato}));}},index)):/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Nessuno stato disponibile\"})})]})]})]});};return/*#__PURE__*/_jsx(Box,{className:\"cavi-page\",children:loading?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',mt:4},children:[/*#__PURE__*/_jsx(CircularProgress,{size:40}),/*#__PURE__*/_jsx(Typography,{sx:{mt:2},children:\"Caricamento cavi...\"}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"primary\",onClick:()=>window.location.reload(),sx:{mt:2},children:\"Ricarica la pagina\"})]}):error?/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Alert,{severity:\"error\",sx:{mb:2},children:[error,error.includes('Network Error')&&/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{mt:1},children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Suggerimento:\"}),\" Verifica che il server backend sia in esecuzione sulla porta 8001.\",/*#__PURE__*/_jsx(\"br\",{}),\"Puoi avviare il backend eseguendo il file \",/*#__PURE__*/_jsx(\"code\",{children:\"run_system.py\"}),\" nella cartella principale del progetto.\"]})]}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',gap:2},children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",className:\"primary-button\",onClick:()=>window.location.reload(),children:\"Ricarica la pagina\"})})]}):/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Box,{sx:{mt:4},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h5\",children:[\"Cavi Attivi \",caviAttivi.length>0?`(${caviAttivi.length})`:'']}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",size:\"small\",onClick:()=>window.location.reload(),startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(RefreshIcon,{}),disabled:loading,children:\"Aggiorna\"})]})}),caviAttivi.length>0?/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[process.env.NODE_ENV==='development'&&/*#__PURE__*/_jsx(Box,{sx:{mb:2,p:1,bgcolor:'#f0f0f0',borderRadius:1,fontSize:'0.8rem',fontFamily:'monospace',display:'none'},children:Object.keys(caviAttivi[0]).map(key=>/*#__PURE__*/_jsxs(\"div\",{children:[key,\": \",JSON.stringify(caviAttivi[0][key])]},key))}),/*#__PURE__*/_jsx(CaviFilterableTable,{cavi:caviAttivi,loading:loading,onFilteredDataChange:filteredData=>console.log('Cavi attivi filtrati:',filteredData.length),revisioneCorrente:((_caviAttivi$=caviAttivi[0])===null||_caviAttivi$===void 0?void 0:_caviAttivi$.revisione_ufficiale)||((_caviAttivi$2=caviAttivi[0])===null||_caviAttivi$2===void 0?void 0:_caviAttivi$2.revisione)||((_caviAttivi$3=caviAttivi[0])===null||_caviAttivi$3===void 0?void 0:_caviAttivi$3.rev)})]}):/*#__PURE__*/_jsx(Alert,{severity:\"info\",sx:{mb:2},children:\"Nessun cavo attivo trovato. I cavi attivi appariranno qui.\"}),/*#__PURE__*/_jsxs(Box,{sx:{mt:4},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h5\",children:[\"Cavi Spare \",caviSpare.length>0?`(${caviSpare.length})`:'']}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",size:\"small\",onClick:()=>window.location.reload(),startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(RefreshIcon,{}),disabled:loading,children:\"Aggiorna\"})]}),caviSpare.length>0?/*#__PURE__*/_jsx(CaviFilterableTable,{cavi:caviSpare,loading:loading,onFilteredDataChange:filteredData=>console.log('Cavi spare filtrati:',filteredData.length)}):/*#__PURE__*/_jsx(Alert,{severity:\"info\",sx:{mb:2},children:\"Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\"})]}),renderDetailsDialog(),/*#__PURE__*/_jsx(Dialog,{open:openEliminaCavoDialog,onClose:()=>setOpenEliminaCavoDialog(false),fullWidth:true,maxWidth:\"md\",children:/*#__PURE__*/_jsx(PosaCaviCollegamenti,{cantiereId:cantiereId,onSuccess:message=>{// Chiudi il dialogo\nsetOpenEliminaCavoDialog(false);// Se c'è un messaggio, è un'operazione completata con successo\nif(message){// Mostra un messaggio di successo\nconsole.log('Operazione completata:',message);// Mostra un messaggio di successo con Snackbar\nshowNotification(message,'success');// Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\nsetTimeout(()=>{console.log('Ricaricamento dati dopo operazione...');try{// Ricarica i dati invece di ricaricare la pagina\nfetchCavi(true);}catch(error){console.error('Errore durante il ricaricamento dei dati:',error);// Se fallisce, prova a ricaricare la pagina\nwindow.location.reload();}},1000);}else{// È un'operazione annullata, non mostrare messaggi\nconsole.log('Operazione annullata dall\\'utente');}},onError:message=>{// Mostra un messaggio di errore\nconsole.error('Errore durante l\\'eliminazione del cavo:',message);// Mostra un alert all'utente\nalert(`Errore: ${message}`);// Chiudi il dialogo\nsetOpenEliminaCavoDialog(false);// Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\nfetchCavi();},initialOption:\"eliminaCavo\"})}),openModificaCavoDialog&&console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:',cantiereId),/*#__PURE__*/_jsx(Dialog,{open:openModificaCavoDialog,onClose:()=>setOpenModificaCavoDialog(false),fullWidth:true,maxWidth:\"sm\",children:/*#__PURE__*/_jsx(PosaCaviCollegamenti,{cantiereId:cantiereId,onSuccess:message=>{// Chiudi il dialogo\nsetOpenModificaCavoDialog(false);// Se c'è un messaggio, è un'operazione completata con successo\nif(message){// Mostra un messaggio di successo\nconsole.log('Operazione completata:',message);// Mostra un messaggio di successo con Snackbar\nshowNotification(message,'success');// Ricarica i dati immediatamente\nconsole.log('Ricaricamento dati dopo operazione...');// Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi\nsetTimeout(()=>{try{fetchCavi(true);}catch(error){console.error('Errore durante il ricaricamento dei dati:',error);// Se fallisce, prova a ricaricare la pagina\nwindow.location.reload();}},1000);}else{// È un'operazione annullata, non mostrare messaggi\nconsole.log('Operazione annullata dall\\'utente');}},onError:message=>{// Mostra un messaggio di errore\nconsole.error('Errore durante la modifica del cavo:',message);// Mostra un alert all'utente\nalert(`Errore: ${message}`);// Chiudi il dialogo\nsetOpenModificaCavoDialog(false);// Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\nconsole.log('Ricaricamento dati dopo errore...');fetchCavi(true);},initialOption:\"modificaCavo\"})}),/*#__PURE__*/_jsx(Dialog,{open:openAggiungiCavoDialog,onClose:()=>setOpenAggiungiCavoDialog(false),maxWidth:\"sm\",fullWidth:true,children:/*#__PURE__*/_jsx(PosaCaviCollegamenti,{cantiereId:cantiereId,onSuccess:message=>{// Chiudi il dialogo\nsetOpenAggiungiCavoDialog(false);// Se c'è un messaggio, è un'operazione completata con successo\nif(message){// Mostra un messaggio di successo\nconsole.log('Operazione completata:',message);// Mostra un messaggio di successo con Snackbar\nshowNotification(message,'success');// Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\nsetTimeout(()=>{console.log('Ricaricamento dati dopo operazione...');try{// Ricarica i dati in modalità silenziosa per evitare il \"blink\" della pagina\nfetchCavi(true);}catch(error){console.error('Errore durante il ricaricamento dei dati:',error);// Se fallisce, prova a ricaricare la pagina immediatamente\nconsole.log('Tentativo di ricaricamento della pagina...');window.location.reload();}},1000);}else{// È un'operazione annullata, non mostrare messaggi\nconsole.log('Operazione annullata dall\\'utente');}},onError:message=>{// Mostra un messaggio di errore\nconsole.error('Errore durante l\\'aggiunta del cavo:',message);// Mostra un messaggio di errore con Snackbar\nshowNotification(`Errore: ${message}`,'error');// Chiudi il dialogo\nsetOpenAggiungiCavoDialog(false);// Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\nfetchCavi(true);},initialOption:\"aggiungiCavo\"})}),/*#__PURE__*/_jsx(Snackbar,{open:notification.open,autoHideDuration:4000,onClose:handleCloseNotification,anchorOrigin:{vertical:'bottom',horizontal:'center'},children:/*#__PURE__*/_jsx(Alert,{onClose:handleCloseNotification,severity:notification.severity,sx:{width:'100%'},children:notification.message})})]})});};export default VisualizzaCaviPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Chip", "CircularProgress", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Snackbar", "InfoIcon", "RefreshIcon", "useNavigate", "useAuth", "useGlobalContext", "PosaCaviCollegamenti", "caviService", "CavoForm", "normalizeInstallationStatus", "CaviFilterableTable", "jsxs", "_jsxs", "jsx", "_jsx", "VisualizzaCaviPage", "_caviAttivi$", "_caviAttivi$2", "_caviAttivi$3", "isImpersonating", "user", "openEliminaCavoDialog", "setOpenEliminaCavoDialog", "openModificaCavoDialog", "setOpenModificaCavoDialog", "openAggiungiCavoDialog", "setOpenAggiungiCavoDialog", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "notification", "setNotification", "open", "message", "severity", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "detailsDialogOpen", "setDetailsDialogOpen", "stats", "setStats", "totali", "cavi_attivi", "cavi_spare", "cavi_totali", "metrature", "metri_teorici_totali", "metri_reali_totali", "percentuale_completamento", "stati", "loadingStats", "setLoadingStats", "loadStatiInstallazione", "setStatiInstallazione", "filters", "setFilters", "stato_installazione", "tipologia", "sort_by", "sort_order", "statiInstallazione", "tipologieCavi", "setTipologieCavi", "<PERSON><PERSON><PERSON>", "silentLoading", "arguments", "length", "undefined", "console", "log", "cantiereIdToUse", "localStorage", "getItem", "attivi", "get<PERSON><PERSON>", "attiviError", "caviSpareTra<PERSON>ttivi", "filter", "cavo", "modificato_manualmente", "spare", "getCaviSpare", "spareError", "standardError", "statsData", "getCaviStats", "validStats", "statsError", "setTimeout", "document", "body", "textContent", "includes", "window", "location", "reload", "fetchData", "token", "selectedCantiereId", "selectedCantiereName", "i", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "item", "stato", "tipologie", "tipo", "timeoutPromise", "Promise", "_", "reject", "Error", "caviPromise", "race", "caviError", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "detail", "handleOpenDetails", "handleCloseDetails", "handleCloseNotification", "prev", "showNotification", "renderDetailsDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "id_cavo", "dividers", "container", "spacing", "xs", "md", "variant", "gutterBottom", "sx", "mb", "sistema", "utility", "colore_cavo", "sezione", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "responsabile_partenza", "comanda_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "responsabile_arrivo", "comanda_arrivo", "metri_te<PERSON>ci", "metratura_reale", "colle<PERSON>nti", "id_bobina", "responsabile_posa", "comanda_posa", "Date", "timestamp", "toLocaleString", "onClick", "renderStatsPanel", "p", "toFixed", "display", "alignItems", "mt", "width", "mr", "value", "height", "borderRadius", "min<PERSON><PERSON><PERSON>", "color", "flexWrap", "gap", "index", "label", "count", "size", "className", "flexDirection", "justifyContent", "startIcon", "disabled", "process", "env", "NODE_ENV", "bgcolor", "fontSize", "fontFamily", "Object", "keys", "stringify", "cavi", "onFilteredDataChange", "filteredData", "revisioneCorrente", "revisione_ufficiale", "revisione", "rev", "onSuccess", "onError", "alert", "initialOption", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Chip,\n  CircularProgress,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Snackbar\n} from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog, openAggiungiCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState({\n    totali: { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },\n    metrature: { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },\n    stati: []\n  });\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Rimosso stato per il debug\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n      // Carica le statistiche\n      try {\n        console.log('Caricamento statistiche...');\n        const statsData = await caviService.getCaviStats(cantiereIdToUse);\n        console.log('Statistiche caricate:', statsData);\n\n        // Verifica che statsData abbia la struttura attesa\n        if (!statsData || typeof statsData !== 'object') {\n          console.error('Statistiche non valide:', statsData);\n          // Imposta un oggetto stats con struttura valida ma vuota\n          setStats({\n            totali: { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },\n            metrature: { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },\n            stati: []\n          });\n        } else {\n          // Assicurati che tutte le proprietà necessarie siano presenti\n          const validStats = {\n            totali: statsData.totali || { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },\n            metrature: statsData.metrature || { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },\n            stati: statsData.stati || []\n          };\n          setStats(validStats);\n        }\n      } catch (statsError) {\n        console.error('Errore nel caricamento delle statistiche:', statsError);\n        // Continua con statistiche vuote ma con struttura valida\n        setStats({\n          totali: { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },\n          metrature: { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },\n          stati: []\n        });\n      }\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = (cavo) => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({ ...prev, open: false }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({ open: true, message, severity });\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Cavo: {selectedCavo.id_cavo}\n        </DialogTitle>\n        <DialogContent dividers>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Informazioni Generali</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>\n                {/* n_conduttori field is now a spare field (kept in DB but hidden in UI) */}\n                <Typography variant=\"body2\"><strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                {/* sh field is now a spare field (kept in DB but hidden in UI) */}\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Partenza</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Arrivo</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Installazione</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>\n                <Typography variant=\"body2\"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDetails}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    // Verifica che stats sia definito e abbia la struttura attesa\n    if (!stats || !stats.totali || !stats.metrature || !stats.stati) {\n      return (\n        <Paper sx={{ mb: 3, p: 2 }}>\n          <Typography variant=\"h6\" gutterBottom>Statistiche</Typography>\n          {loadingStats ? (\n            <LinearProgress />\n          ) : (\n            <Typography variant=\"body2\">Nessuna statistica disponibile</Typography>\n          )}\n        </Paper>\n      );\n    }\n\n    // Valori predefiniti per evitare errori\n    const totali = stats.totali || { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 };\n    const metrature = stats.metrature || { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 };\n    const stati = stats.stati || [];\n\n    return (\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Typography variant=\"h6\" gutterBottom>Statistiche</Typography>\n        {loadingStats ? (\n          <LinearProgress />\n        ) : (\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Totali</Typography>\n              <Typography variant=\"body2\">Cavi Attivi: {totali.cavi_attivi || 0}</Typography>\n              <Typography variant=\"body2\">Cavi Spare: {totali.cavi_spare || 0}</Typography>\n              <Typography variant=\"body2\">Totale Cavi: {totali.cavi_totali || 0}</Typography>\n              {/* Rimossa visualizzazione della revisione da qui, spostata nel titolo delle statistiche della tabella */}\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Metrature</Typography>\n              <Typography variant=\"body2\">Metri Teorici: {(metrature.metri_teorici_totali || 0).toFixed(2)}</Typography>\n              <Typography variant=\"body2\">Metri Posati: {(metrature.metri_reali_totali || 0).toFixed(2)}</Typography>\n              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                <Box sx={{ width: '100%', mr: 1 }}>\n                  <LinearProgress\n                    variant=\"determinate\"\n                    value={metrature.percentuale_completamento || 0}\n                    sx={{ height: 10, borderRadius: 5 }}\n                  />\n                </Box>\n                <Box sx={{ minWidth: 35 }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">{`${(metrature.percentuale_completamento || 0).toFixed(1)}%`}</Typography>\n                </Box>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Stati</Typography>\n              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                {stati.length > 0 ? stati.map((stato, index) => (\n                  <Chip\n                    key={index}\n                    label={`${stato.stato || 'N/A'}: ${stato.count || 0}`}\n                    size=\"small\"\n                    onClick={() => {\n                      setFilters(prev => ({\n                        ...prev,\n                        stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                      }));\n                    }}\n                  />\n                )) : (\n                  <Typography variant=\"body2\">Nessuno stato disponibile</Typography>\n                )}\n              </Box>\n            </Grid>\n          </Grid>\n        )}\n      </Paper>\n    );\n  };\n\n  return (\n    <Box className=\"cavi-page\">\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <CircularProgress size={40} />\n          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          {/* Rimosso il pulsante di refresh, gli utenti possono usare il refresh del browser */}\n\n          {/* Sezione Cavi Attivi */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Attivi {caviAttivi.length > 0 ? `(${caviAttivi.length})` : ''}\n              </Typography>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => window.location.reload()}\n                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}\n                disabled={loading}\n              >\n                Aggiorna\n              </Button>\n            </Box>\n          </Box>\n\n          {caviAttivi.length > 0 ? (\n            <Box sx={{ mb: 2 }}>\n              {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}\n              {process.env.NODE_ENV === 'development' && (\n                <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>\n                  {Object.keys(caviAttivi[0]).map(key => (\n                    <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>\n                  ))}\n                </Box>\n              )}\n              <CaviFilterableTable\n                cavi={caviAttivi}\n                loading={loading}\n                onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}\n                revisioneCorrente={caviAttivi[0]?.revisione_ufficiale || caviAttivi[0]?.revisione || caviAttivi[0]?.rev}\n              />\n            </Box>\n          ) : (\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Nessun cavo attivo trovato. I cavi attivi appariranno qui.\n            </Alert>\n          )}\n\n          {/* Sezione Cavi Spare */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}\n              </Typography>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => window.location.reload()}\n                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}\n                disabled={loading}\n              >\n                Aggiorna\n              </Button>\n            </Box>\n            {caviSpare.length > 0 ? (\n              <CaviFilterableTable\n                cavi={caviSpare}\n                loading={loading}\n                onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}\n              />\n            ) : (\n              <Alert severity=\"info\" sx={{ mb: 2 }}>\n                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Rimossa sezione Debug */}\n\n          {/* Dialogo dei dettagli del cavo */}\n          {renderDetailsDialog()}\n\n          {/* Dialogo per l'eliminazione dei cavi */}\n          <Dialog\n            open={openEliminaCavoDialog}\n            onClose={() => setOpenEliminaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio di successo con Snackbar\n                  showNotification(message, 'success');\n                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    console.log('Ricaricamento dati dopo operazione...');\n                    try {\n                      // Ricarica i dati invece di ricaricare la pagina\n                      fetchCavi(true);\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'eliminazione del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                fetchCavi();\n              }}\n              initialOption=\"eliminaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per la modifica dei cavi */}\n          {/* Log del cantiereId prima di aprire il dialog */}\n          {openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId)}\n\n          <Dialog\n            open={openModificaCavoDialog}\n            onClose={() => setOpenModificaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"sm\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio di successo con Snackbar\n                  showNotification(message, 'success');\n                  // Ricarica i dati immediatamente\n                  console.log('Ricaricamento dati dopo operazione...');\n                  // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    try {\n                      fetchCavi(true);\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante la modifica del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                console.log('Ricaricamento dati dopo errore...');\n                fetchCavi(true);\n              }}\n              initialOption=\"modificaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per l'aggiunta di un nuovo cavo */}\n          <Dialog open={openAggiungiCavoDialog} onClose={() => setOpenAggiungiCavoDialog(false)} maxWidth=\"sm\" fullWidth>\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenAggiungiCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio di successo con Snackbar\n                  showNotification(message, 'success');\n                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    console.log('Ricaricamento dati dopo operazione...');\n                    try {\n                      // Ricarica i dati in modalità silenziosa per evitare il \"blink\" della pagina\n                      fetchCavi(true);\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina immediatamente\n                      console.log('Tentativo di ricaricamento della pagina...');\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'aggiunta del cavo:', message);\n                // Mostra un messaggio di errore con Snackbar\n                showNotification(`Errore: ${message}`, 'error');\n                // Chiudi il dialogo\n                setOpenAggiungiCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                fetchCavi(true);\n              }}\n              initialOption=\"aggiungiCavo\"\n            />\n          </Dialog>\n\n          {/* Snackbar per le notifiche */}\n          <Snackbar\n            open={notification.open}\n            autoHideDuration={4000}\n            onClose={handleCloseNotification}\n            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n          >\n            <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n              {notification.message}\n            </Alert>\n          </Snackbar>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,KAAK,CACLC,UAAU,CACVC,IAAI,CACJC,gBAAgB,CAChBC,cAAc,CACdC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,QAAQ,KACH,eAAe,CACtB,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,OAAO,KAAQ,2BAA2B,CACnD,OAASC,gBAAgB,KAAQ,6BAA6B,CAC9D,MAAO,CAAAC,oBAAoB,KAAM,4CAA4C,CAC7E,MAAO,CAAAC,WAAW,KAAM,4BAA4B,CACpD,MAAO,CAAAC,QAAQ,KAAM,gCAAgC,CACrD,OAASC,2BAA2B,KAAQ,6BAA6B,CACzE,MAAO,CAAAC,mBAAmB,KAAM,2CAA2C,CAC3E,MAAO,gBAAgB,CAAC,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,yBAExB,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,KAAAC,YAAA,CAAAC,aAAA,CAAAC,aAAA,CAC/B,KAAM,CAAEC,eAAe,CAAEC,IAAK,CAAC,CAAGhB,OAAO,CAAC,CAAC,CAC3C,KAAM,CAAEiB,qBAAqB,CAAEC,wBAAwB,CAAEC,sBAAsB,CAAEC,yBAAyB,CAAEC,sBAAsB,CAAEC,yBAA0B,CAAC,CAAGrB,gBAAgB,CAAC,CAAC,CACpL,KAAM,CAAAsB,QAAQ,CAAGxB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACyB,UAAU,CAAEC,aAAa,CAAC,CAAG/C,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACgD,YAAY,CAAEC,eAAe,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACkD,UAAU,CAAEC,aAAa,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACoD,SAAS,CAAEC,YAAY,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACsD,OAAO,CAAEC,UAAU,CAAC,CAAGvD,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACwD,KAAK,CAAEC,QAAQ,CAAC,CAAGzD,QAAQ,CAAC,IAAI,CAAC,CACxC;AACA,KAAM,CAAC0D,YAAY,CAAEC,eAAe,CAAC,CAAG3D,QAAQ,CAAC,CAAE4D,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,EAAE,CAAEC,QAAQ,CAAE,SAAU,CAAC,CAAC,CACnG;AAEA;AACA,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGhE,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACiE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGlE,QAAQ,CAAC,KAAK,CAAC,CAEjE;AACA,KAAM,CAACmE,KAAK,CAAEC,QAAQ,CAAC,CAAGpE,QAAQ,CAAC,CACjCqE,MAAM,CAAE,CAAEC,WAAW,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CACzDC,SAAS,CAAE,CAAEC,oBAAoB,CAAE,CAAC,CAAEC,kBAAkB,CAAE,CAAC,CAAEC,yBAAyB,CAAE,CAAE,CAAC,CAC3FC,KAAK,CAAE,EACT,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG/E,QAAQ,CAAC,KAAK,CAAC,CAEvD;AAEA;AACA,KAAM,CAAAgF,sBAAsB,CAAGA,CAAA,GAAM,CACnC;AACAC,qBAAqB,CAAC,CAAC,YAAY,CAAE,eAAe,CAAE,UAAU,CAAC,CAAC,CACpE,CAAC,CAED;AACA,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGnF,QAAQ,CAAC,CACrCoF,mBAAmB,CAAE,EAAE,CACvBC,SAAS,CAAE,EAAE,CACbC,OAAO,CAAE,EAAE,CACXC,UAAU,CAAE,KACd,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,kBAAkB,CAAEP,qBAAqB,CAAC,CAAGjF,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CAACyF,aAAa,CAAEC,gBAAgB,CAAC,CAAG1F,QAAQ,CAAC,EAAE,CAAC,CAEtD;AAEA;AACA;AACA,KAAM,CAAA2F,SAAS,CAAG,cAAAA,CAAA,CAAiC,IAA1B,CAAAC,aAAa,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC5C,GAAI,CACF,GAAI,CAACD,aAAa,CAAE,CAClBrC,UAAU,CAAC,IAAI,CAAC,CAClB,CACAyC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEnD,UAAU,CAAC,CAEzD;AACA,GAAI,CAACA,UAAU,CAAE,CACfkD,OAAO,CAACxC,KAAK,CAAC,mCAAmC,CAAEV,UAAU,CAAC,CAC9DW,QAAQ,CAAC,wDAAwD,CAAC,CAClEF,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA;AACA,GAAI,CAAA2C,eAAe,CAAGpD,UAAU,CAChC,GAAI,CAACoD,eAAe,CAAE,CACpBA,eAAe,CAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAC5DJ,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAEC,eAAe,CAAC,CACnE,GAAI,CAACA,eAAe,CAAE,CACpBF,OAAO,CAACxC,KAAK,CAAC,2CAA2C,CAAC,CAC1DC,QAAQ,CAAC,8CAA8C,CAAC,CACxDF,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CACF,CAEA;AACAyC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CACvD,GAAI,CAAAI,MAAM,CAAG,EAAE,CACf,GAAI,CACFA,MAAM,CAAG,KAAM,CAAA5E,WAAW,CAAC6E,OAAO,CAACJ,eAAe,CAAE,CAAC,CAAEhB,OAAO,CAAC,CAC/Dc,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEI,MAAM,CAAGA,MAAM,CAACP,MAAM,CAAG,CAAC,CAAC,CAClE,CAAE,MAAOS,WAAW,CAAE,CACpBP,OAAO,CAACxC,KAAK,CAAC,yCAAyC,CAAE+C,WAAW,CAAC,CACrE;AACAF,MAAM,CAAG,EAAE,CACb,CAEA;AACA,GAAIA,MAAM,EAAIA,MAAM,CAACP,MAAM,CAAG,CAAC,CAAE,CAC/B,KAAM,CAAAU,kBAAkB,CAAGH,MAAM,CAACI,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACC,sBAAsB,GAAK,CAAC,CAAC,CACnF,GAAIH,kBAAkB,CAACV,MAAM,CAAG,CAAC,CAAE,CACjCE,OAAO,CAACxC,KAAK,CAAC,wEAAwE,CAAEgD,kBAAkB,CAAC,CAC7G,CACF,CAEArD,aAAa,CAACkD,MAAM,EAAI,EAAE,CAAC,CAE3B;AACA,GAAI,CAAAO,KAAK,CAAG,EAAE,CACd,GAAI,CACFZ,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC,CAC9DW,KAAK,CAAG,KAAM,CAAAnF,WAAW,CAACoF,YAAY,CAACX,eAAe,CAAC,CACvDF,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAEW,KAAK,CAAGA,KAAK,CAACd,MAAM,CAAG,CAAC,CAAC,CACnF,GAAIc,KAAK,EAAIA,KAAK,CAACd,MAAM,CAAG,CAAC,CAAE,CAC7BE,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAEW,KAAK,CAAC,CAAC,CAAC,CAAC,CAC5C,CACF,CAAE,MAAOE,UAAU,CAAE,CACnBd,OAAO,CAACxC,KAAK,CAAC,8DAA8D,CAAEsD,UAAU,CAAC,CACzF;AACA,GAAI,CACFd,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC,CAC/CW,KAAK,CAAG,KAAM,CAAAnF,WAAW,CAAC6E,OAAO,CAACJ,eAAe,CAAE,CAAC,CAAC,CACrDF,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAEW,KAAK,CAAGA,KAAK,CAACd,MAAM,CAAG,CAAC,CAAC,CACnF,CAAE,MAAOiB,aAAa,CAAE,CACtBf,OAAO,CAACxC,KAAK,CAAC,mCAAmC,CAAEuD,aAAa,CAAC,CACjE;AACAH,KAAK,CAAG,EAAE,CACZ,CACF,CACAvD,YAAY,CAACuD,KAAK,EAAI,EAAE,CAAC,CAEzB;AACA,GAAI,CACFZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC,CACzC,KAAM,CAAAe,SAAS,CAAG,KAAM,CAAAvF,WAAW,CAACwF,YAAY,CAACf,eAAe,CAAC,CACjEF,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEe,SAAS,CAAC,CAE/C;AACA,GAAI,CAACA,SAAS,EAAI,MAAO,CAAAA,SAAS,GAAK,QAAQ,CAAE,CAC/ChB,OAAO,CAACxC,KAAK,CAAC,yBAAyB,CAAEwD,SAAS,CAAC,CACnD;AACA5C,QAAQ,CAAC,CACPC,MAAM,CAAE,CAAEC,WAAW,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CACzDC,SAAS,CAAE,CAAEC,oBAAoB,CAAE,CAAC,CAAEC,kBAAkB,CAAE,CAAC,CAAEC,yBAAyB,CAAE,CAAE,CAAC,CAC3FC,KAAK,CAAE,EACT,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACA,KAAM,CAAAqC,UAAU,CAAG,CACjB7C,MAAM,CAAE2C,SAAS,CAAC3C,MAAM,EAAI,CAAEC,WAAW,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAC7EC,SAAS,CAAEuC,SAAS,CAACvC,SAAS,EAAI,CAAEC,oBAAoB,CAAE,CAAC,CAAEC,kBAAkB,CAAE,CAAC,CAAEC,yBAAyB,CAAE,CAAE,CAAC,CAClHC,KAAK,CAAEmC,SAAS,CAACnC,KAAK,EAAI,EAC5B,CAAC,CACDT,QAAQ,CAAC8C,UAAU,CAAC,CACtB,CACF,CAAE,MAAOC,UAAU,CAAE,CACnBnB,OAAO,CAACxC,KAAK,CAAC,2CAA2C,CAAE2D,UAAU,CAAC,CACtE;AACA/C,QAAQ,CAAC,CACPC,MAAM,CAAE,CAAEC,WAAW,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CACzDC,SAAS,CAAE,CAAEC,oBAAoB,CAAE,CAAC,CAAEC,kBAAkB,CAAE,CAAC,CAAEC,yBAAyB,CAAE,CAAE,CAAC,CAC3FC,KAAK,CAAE,EACT,CAAC,CAAC,CACJ,CAEA;AACApB,QAAQ,CAAC,EAAE,CAAC,CACd,CAAE,MAAOD,KAAK,CAAE,CACdwC,OAAO,CAACxC,KAAK,CAAC,2CAA2C,CAAEA,KAAK,CAAC,CACjEC,QAAQ,CAAC,oCAAoCD,KAAK,CAACK,OAAO,EAAI,oBAAoB,EAAE,CAAC,CAErF;AACAuD,UAAU,CAAC,IAAM,CACf;AACA,GAAIC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACC,QAAQ,CAAC,iCAAiC,CAAC,CAAE,CACzExB,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC,CAC7EwB,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAC1B,CACF,CAAC,CAAE,IAAI,CAAC,CAAE;AACZ,CAAC,OAAS,CACR,GAAI,CAAC/B,aAAa,CAAE,CAClBrC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CACF,CAAC,CAED;AACAtD,SAAS,CAAC,IAAM,CACd;AACA+E,sBAAsB,CAAC,CAAC,CAExB,KAAM,CAAA4C,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACF5B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CAErD;AACA,KAAM,CAAA4B,KAAK,CAAG1B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3CJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAE,CAAC,CAAC4B,KAAK,CAAC,CACvC,GAAI,CAACA,KAAK,CAAE,CACVpE,QAAQ,CAAC,iDAAiD,CAAC,CAC3DF,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA;AACA,GAAI,CAAAuE,kBAAkB,CAAG3B,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CACnE,GAAI,CAAA2B,oBAAoB,CAAG5B,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAEvEJ,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAE,CAAE6B,kBAAkB,CAAEC,oBAAqB,CAAC,CAAC,CACnG/B,OAAO,CAACC,GAAG,CAAC,cAAc,CAAE3D,IAAI,CAAC,CAEjC;AACA0D,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CACrD,IAAK,GAAI,CAAA+B,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG7B,YAAY,CAACL,MAAM,CAAEkC,CAAC,EAAE,CAAE,CAC5C,KAAM,CAAAC,GAAG,CAAG9B,YAAY,CAAC8B,GAAG,CAACD,CAAC,CAAC,CAC/BhC,OAAO,CAACC,GAAG,CAAC,GAAGgC,GAAG,KAAK9B,YAAY,CAACC,OAAO,CAAC6B,GAAG,CAAC,EAAE,CAAC,CACrD,CAEA;AACA,GAAI,CAAA3F,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE4F,IAAI,IAAK,eAAe,CAAE,CAClClC,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC,CAE1F;AACA,GAAI3D,IAAI,CAAC6F,WAAW,CAAE,CACpBnC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAE3D,IAAI,CAAC6F,WAAW,CAAC,CACrEL,kBAAkB,CAAGxF,IAAI,CAAC6F,WAAW,CAACC,QAAQ,CAAC,CAAC,CAChDL,oBAAoB,CAAGzF,IAAI,CAAC+F,aAAa,EAAI,YAAY/F,IAAI,CAAC6F,WAAW,EAAE,CAE3E;AACAhC,YAAY,CAACmC,OAAO,CAAC,oBAAoB,CAAER,kBAAkB,CAAC,CAC9D3B,YAAY,CAACmC,OAAO,CAAC,sBAAsB,CAAEP,oBAAoB,CAAC,CAClE/B,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAE6B,kBAAkB,CAAC,CAC1E,CAAC,IAAM,CACL;AACA,GAAI,CACF9B,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC,CAClF,KAAM,CAAA4B,KAAK,CAAG1B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAIyB,KAAK,CAAE,CACT;AACA,KAAM,CAAAU,SAAS,CAAGV,KAAK,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACrC,KAAM,CAAAC,MAAM,CAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,CAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,CAAE,GAAG,CAAC,CAC9D,KAAM,CAAAC,WAAW,CAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,EAAI,CACrE,MAAO,GAAG,CAAG,CAAC,IAAI,CAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC,CAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC,CAEZ,KAAM,CAAAC,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC,CACvC3C,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAEkD,OAAO,CAAC,CAE9C,GAAIA,OAAO,CAAChB,WAAW,CAAE,CACvBnC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAEkD,OAAO,CAAChB,WAAW,CAAC,CACtEL,kBAAkB,CAAGqB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC,CACnD;AACAL,oBAAoB,CAAG,YAAYoB,OAAO,CAAChB,WAAW,EAAE,CAExD;AACAhC,YAAY,CAACmC,OAAO,CAAC,oBAAoB,CAAER,kBAAkB,CAAC,CAC9D3B,YAAY,CAACmC,OAAO,CAAC,sBAAsB,CAAEP,oBAAoB,CAAC,CAClE/B,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAE6B,kBAAkB,CAAC,CAC1E,CACF,CACF,CAAE,MAAOwB,CAAC,CAAE,CACVtD,OAAO,CAACxC,KAAK,CAAC,6CAA6C,CAAE8F,CAAC,CAAC,CACjE,CACF,CACF,CAEA;AACA,GAAI,CAACxB,kBAAkB,EAAIA,kBAAkB,GAAK,WAAW,EAAIA,kBAAkB,GAAK,MAAM,CAAE,CAC9F9B,OAAO,CAACuD,IAAI,CAAC,6EAA6E,CAAC,CAC3F;AACAzB,kBAAkB,CAAG,GAAG,CAAE;AAC1BC,oBAAoB,CAAG,gBAAgB,CAEvC;AACA5B,YAAY,CAACmC,OAAO,CAAC,oBAAoB,CAAER,kBAAkB,CAAC,CAC9D3B,YAAY,CAACmC,OAAO,CAAC,sBAAsB,CAAEP,oBAAoB,CAAC,CAClE/B,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAE6B,kBAAkB,CAAC,CACpF,CAEA;AACA,GAAI,CAACA,kBAAkB,CAAE,CACvBrE,QAAQ,CAAC,8DAA8D,CAAC,CACxEF,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA;AACA,KAAM,CAAAiG,aAAa,CAAGC,QAAQ,CAAC3B,kBAAkB,CAAE,EAAE,CAAC,CACtD9B,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAEuD,aAAa,CAAC,CAC9D,GAAIE,KAAK,CAACF,aAAa,CAAC,CAAE,CACxB/F,QAAQ,CAAC,2BAA2BqE,kBAAkB,mCAAmC,CAAC,CAC1FvE,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA;AACAR,aAAa,CAACyG,aAAa,CAAC,CAC5BvG,eAAe,CAAC8E,oBAAoB,EAAI,YAAYyB,aAAa,EAAE,CAAC,CAEpE;AACA,GAAI,CACFzE,eAAe,CAAC,IAAI,CAAC,CACrBiB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAEuD,aAAa,CAAC,CACxE,KAAM,CAAAxC,SAAS,CAAG,KAAM,CAAAvF,WAAW,CAACwF,YAAY,CAACuC,aAAa,CAAC,CAC/DxD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEe,SAAS,CAAC,CACpD5C,QAAQ,CAAC4C,SAAS,CAAC,CAEnB;AACA,GAAIA,SAAS,EAAIA,SAAS,CAACnC,KAAK,CAAE,CAChC,KAAM,CAAAA,KAAK,CAAGmC,SAAS,CAACnC,KAAK,CAACiE,GAAG,CAACa,IAAI,EAAIA,IAAI,CAACC,KAAK,CAAC,CAACnD,MAAM,CAACmD,KAAK,EAAIA,KAAK,GAAK,iBAAiB,CAAC,CAClG3E,qBAAqB,CAACJ,KAAK,CAAC,CAC9B,CAEA,GAAImC,SAAS,EAAIA,SAAS,CAAC6C,SAAS,CAAE,CACpC,KAAM,CAAAA,SAAS,CAAG7C,SAAS,CAAC6C,SAAS,CAACf,GAAG,CAACa,IAAI,EAAIA,IAAI,CAACtE,SAAS,CAAC,CAACoB,MAAM,CAACqD,IAAI,EAAIA,IAAI,GAAK,iBAAiB,CAAC,CAC5GpE,gBAAgB,CAACmE,SAAS,CAAC,CAC7B,CAEA9E,eAAe,CAAC,KAAK,CAAC,CACxB,CAAE,MAAOoC,UAAU,CAAE,CACnBnB,OAAO,CAACxC,KAAK,CAAC,2CAA2C,CAAE2D,UAAU,CAAC,CACtEpC,eAAe,CAAC,KAAK,CAAC,CACtB;AACF,CAEA;AACAiB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAEuD,aAAa,CAAC,CACnE,GAAI,CACF;AACA,KAAM,CAAAO,cAAc,CAAG,GAAI,CAAAC,OAAO,CAAC,CAACC,CAAC,CAAEC,MAAM,GAAK,CAChD9C,UAAU,CAAC,IAAM8C,MAAM,CAAC,GAAI,CAAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC,CAAE,KAAK,CAAC,CAAE;AAChG,CAAC,CAAC,CAEF;AACAnE,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAEf,OAAO,CAAC,CAC1E,KAAM,CAAAkF,WAAW,CAAG3I,WAAW,CAAC6E,OAAO,CAACkD,aAAa,CAAE,CAAC,CAAEtE,OAAO,CAAC,CAClE,KAAM,CAAAmB,MAAM,CAAG,KAAM,CAAA2D,OAAO,CAACK,IAAI,CAAC,CAACD,WAAW,CAAEL,cAAc,CAAC,CAAC,CAEhE/D,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEI,MAAM,CAAC,CAC5CL,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEI,MAAM,CAAGA,MAAM,CAACP,MAAM,CAAG,CAAC,CAAC,CACzE,GAAIO,MAAM,EAAIA,MAAM,CAACP,MAAM,CAAG,CAAC,CAAE,CAC/BE,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAEI,MAAM,CAAC,CAAC,CAAC,CAAC,CAC9C,CAAC,IAAM,CACLL,OAAO,CAACuD,IAAI,CAAC,4CAA4C,CAAEC,aAAa,CAAC,CAC3E,CACArG,aAAa,CAACkD,MAAM,EAAI,EAAE,CAAC,CAC7B,CAAE,MAAOiE,SAAS,CAAE,CAClBtE,OAAO,CAACxC,KAAK,CAAC,yCAAyC,CAAE8G,SAAS,CAAC,CACnEtE,OAAO,CAACxC,KAAK,CAAC,8BAA8B,CAAE,CAC5CK,OAAO,CAAEyG,SAAS,CAACzG,OAAO,CAC1B0G,MAAM,CAAED,SAAS,CAACC,MAAM,CACxBC,IAAI,CAAEF,SAAS,CAACE,IAAI,CACpBC,KAAK,CAAEH,SAAS,CAACG,KAAK,CACtBC,IAAI,CAAEJ,SAAS,CAACI,IAAI,CACpBC,IAAI,CAAEL,SAAS,CAACK,IAAI,CACpBC,QAAQ,CAAEN,SAAS,CAACM,QAAQ,CAAG,CAC7BL,MAAM,CAAED,SAAS,CAACM,QAAQ,CAACL,MAAM,CACjCM,UAAU,CAAEP,SAAS,CAACM,QAAQ,CAACC,UAAU,CACzCL,IAAI,CAAEF,SAAS,CAACM,QAAQ,CAACJ,IAC3B,CAAC,CAAG,aACN,CAAC,CAAC,CAEF;AACArH,aAAa,CAAC,EAAE,CAAC,CACjB6C,OAAO,CAACuD,IAAI,CAAC,sDAAsD,CAAC,CAEpE;AACA9F,QAAQ,CAAC,2CAA2C6G,SAAS,CAACzG,OAAO,+CAA+C,CAAC,CACvH,CAEA;AACAmC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAEuD,aAAa,CAAC,CAClE,GAAI,CACF;AACA,KAAM,CAAAO,cAAc,CAAG,GAAI,CAAAC,OAAO,CAAC,CAACC,CAAC,CAAEC,MAAM,GAAK,CAChD9C,UAAU,CAAC,IAAM8C,MAAM,CAAC,GAAI,CAAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC,CAAE,KAAK,CAAC,CAAE;AAC/F,CAAC,CAAC,CAEF;AACAnE,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CACvD;AACA,KAAM,CAAA6E,YAAY,CAAGrJ,WAAW,CAAC6E,OAAO,CAACkD,aAAa,CAAE,CAAC,CAAC,CAC1D,KAAM,CAAA5C,KAAK,CAAG,KAAM,CAAAoD,OAAO,CAACK,IAAI,CAAC,CAACS,YAAY,CAAEf,cAAc,CAAC,CAAC,CAEhE/D,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAEW,KAAK,CAAC,CAC1CZ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAEW,KAAK,CAAGA,KAAK,CAACd,MAAM,CAAG,CAAC,CAAC,CACtE,GAAIc,KAAK,EAAIA,KAAK,CAACd,MAAM,CAAG,CAAC,CAAE,CAC7BE,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAEW,KAAK,CAAC,CAAC,CAAC,CAAC,CAC5C,CAAC,IAAM,CACLZ,OAAO,CAACuD,IAAI,CAAC,2CAA2C,CAAEC,aAAa,CAAC,CAC1E,CACAnG,YAAY,CAACuD,KAAK,EAAI,EAAE,CAAC,CAC3B,CAAE,MAAOE,UAAU,CAAE,CACnBd,OAAO,CAACxC,KAAK,CAAC,wCAAwC,CAAEsD,UAAU,CAAC,CACnEd,OAAO,CAACxC,KAAK,CAAC,6BAA6B,CAAE,CAC3CK,OAAO,CAAEiD,UAAU,CAACjD,OAAO,CAC3B0G,MAAM,CAAEzD,UAAU,CAACyD,MAAM,CACzBC,IAAI,CAAE1D,UAAU,CAAC0D,IAAI,CACrBC,KAAK,CAAE3D,UAAU,CAAC2D,KAAK,CACvBC,IAAI,CAAE5D,UAAU,CAAC4D,IAAI,CACrBC,IAAI,CAAE7D,UAAU,CAAC6D,IAAI,CACrBC,QAAQ,CAAE9D,UAAU,CAAC8D,QAAQ,CAAG,CAC9BL,MAAM,CAAEzD,UAAU,CAAC8D,QAAQ,CAACL,MAAM,CAClCM,UAAU,CAAE/D,UAAU,CAAC8D,QAAQ,CAACC,UAAU,CAC1CL,IAAI,CAAE1D,UAAU,CAAC8D,QAAQ,CAACJ,IAC5B,CAAC,CAAG,aACN,CAAC,CAAC,CAEF;AACAnH,YAAY,CAAC,EAAE,CAAC,CAEhB;AACA,GAAI,CAACG,KAAK,CAAE,CACVC,QAAQ,CAAC,0CAA0CqD,UAAU,CAACjD,OAAO,+CAA+C,CAAC,CACvH,CACF,CAEA;AACAN,UAAU,CAAC,KAAK,CAAC,CAEnB,CAAE,MAAOwH,GAAG,CAAE,KAAAC,aAAA,CAAAC,cAAA,CAAAC,cAAA,CAAAC,cAAA,CAAAC,cAAA,CAAAC,mBAAA,CACZrF,OAAO,CAACxC,KAAK,CAAC,kCAAkC,CAAEuH,GAAG,CAAC,CACtD/E,OAAO,CAACxC,KAAK,CAAC,2BAA2B,CAAE,CACzCK,OAAO,CAAEkH,GAAG,CAAClH,OAAO,CACpB0G,MAAM,CAAEQ,GAAG,CAACR,MAAM,IAAAS,aAAA,CAAID,GAAG,CAACH,QAAQ,UAAAI,aAAA,iBAAZA,aAAA,CAAcT,MAAM,EAC1CC,IAAI,CAAEO,GAAG,CAACP,IAAI,IAAAS,cAAA,CAAIF,GAAG,CAACH,QAAQ,UAAAK,cAAA,iBAAZA,cAAA,CAAcT,IAAI,EACpCC,KAAK,CAAEM,GAAG,CAACN,KACb,CAAC,CAAC,CAEF;AACA,GAAI,CAAAa,YAAY,CAAG,oBAAoB,CAEvC,GAAIP,GAAG,CAAClH,OAAO,EAAIkH,GAAG,CAAClH,OAAO,CAAC2D,QAAQ,CAAC,wBAAwB,CAAC,CAAE,CACjE8D,YAAY,CAAGP,GAAG,CAAClH,OAAO,CAC5B,CAAC,IAAM,IAAIkH,GAAG,CAACR,MAAM,GAAK,GAAG,EAAIQ,GAAG,CAACR,MAAM,GAAK,GAAG,EACzC,EAAAW,cAAA,CAAAH,GAAG,CAACH,QAAQ,UAAAM,cAAA,iBAAZA,cAAA,CAAcX,MAAM,IAAK,GAAG,EAAI,EAAAY,cAAA,CAAAJ,GAAG,CAACH,QAAQ,UAAAO,cAAA,iBAAZA,cAAA,CAAcZ,MAAM,IAAK,GAAG,CAAE,CACtEe,YAAY,CAAG,mEAAmE,CACpF,CAAC,IAAM,KAAAF,cAAA,CAAIL,GAAG,CAACH,QAAQ,UAAAQ,cAAA,YAAAC,mBAAA,CAAZD,cAAA,CAAcZ,IAAI,UAAAa,mBAAA,WAAlBA,mBAAA,CAAoBE,MAAM,CAAE,CACrC;AACAD,YAAY,CAAG,eAAeP,GAAG,CAACH,QAAQ,CAACJ,IAAI,CAACe,MAAM,EAAE,CAC1D,CAAC,IAAM,IAAIR,GAAG,CAACL,IAAI,GAAK,aAAa,CAAE,CACrC;AACAY,YAAY,CAAG,yEAAyE,CAC1F,CAAC,IAAM,IAAIP,GAAG,CAAClH,OAAO,CAAE,CACtByH,YAAY,CAAGP,GAAG,CAAClH,OAAO,CAC5B,CAEAJ,QAAQ,CAAC,gCAAgC6H,YAAY,sBAAsB,CAAC,CAE5E;AACAnI,aAAa,CAAC,EAAE,CAAC,CACjBE,YAAY,CAAC,EAAE,CAAC,CAClB,CAAC,OAAS,CACRE,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDqE,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,CAAC1C,OAAO,CAAC,CAAC,CAAE;AAEf;AAEA;AACA,KAAM,CAAAsG,iBAAiB,CAAI9E,IAAI,EAAK,CAClC1C,eAAe,CAAC0C,IAAI,CAAC,CACrBxC,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAuH,kBAAkB,CAAGA,CAAA,GAAM,CAC/BvH,oBAAoB,CAAC,KAAK,CAAC,CAC3BF,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAA0H,uBAAuB,CAAGA,CAAA,GAAM,CACpC/H,eAAe,CAACgI,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE/H,IAAI,CAAE,KAAM,CAAC,CAAC,CAAC,CACrD,CAAC,CAED;AACA,KAAM,CAAAgI,gBAAgB,CAAG,QAAAA,CAAC/H,OAAO,CAA2B,IAAzB,CAAAC,QAAQ,CAAA+B,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,SAAS,CACrDlC,eAAe,CAAC,CAAEC,IAAI,CAAE,IAAI,CAAEC,OAAO,CAAEC,QAAS,CAAC,CAAC,CACpD,CAAC,CAED;AAEA;AAEA;AAEA;AACA,KAAM,CAAA+H,mBAAmB,CAAGA,CAAA,GAAM,CAChC,GAAI,CAAC9H,YAAY,CAAE,MAAO,KAAI,CAE9B,mBACEjC,KAAA,CAAChB,MAAM,EAAC8C,IAAI,CAAEK,iBAAkB,CAAC6H,OAAO,CAAEL,kBAAmB,CAACM,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAC,QAAA,eACnFnK,KAAA,CAACf,WAAW,EAAAkL,QAAA,EAAC,iBACI,CAAClI,YAAY,CAACmI,OAAO,EACzB,CAAC,cACdlK,IAAA,CAAChB,aAAa,EAACmL,QAAQ,MAAAF,QAAA,cACrBnK,KAAA,CAACxB,IAAI,EAAC8L,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAJ,QAAA,eACzBnK,KAAA,CAACxB,IAAI,EAACqJ,IAAI,MAAC2C,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,eACvBjK,IAAA,CAAC7B,UAAU,EAACqM,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAR,QAAA,CAAC,uBAAqB,CAAY,CAAC,cAC/EnK,KAAA,CAAC5B,GAAG,EAACwM,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAV,QAAA,eACjBnK,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,UAAQ,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAAC6I,OAAO,EAAI,KAAK,EAAa,CAAC,cAClG9K,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,UAAQ,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAAC8I,OAAO,EAAI,KAAK,EAAa,CAAC,cAClG/K,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,YAAU,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAACsB,SAAS,EAAI,KAAK,EAAa,CAAC,cACtGvD,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAAC+I,WAAW,EAAI,KAAK,EAAa,CAAC,cAErGhL,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,aAAW,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAACgJ,OAAO,EAAI,KAAK,EAAa,CAAC,EAElG,CAAC,cAEN/K,IAAA,CAAC7B,UAAU,EAACqM,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAR,QAAA,CAAC,UAAQ,CAAY,CAAC,cAClEnK,KAAA,CAAC5B,GAAG,EAACwM,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAV,QAAA,eACjBnK,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,aAAW,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAACiJ,mBAAmB,EAAI,KAAK,EAAa,CAAC,cACjHlL,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAACkJ,eAAe,EAAI,KAAK,EAAa,CAAC,cACzGnL,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,cAAY,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAACmJ,2BAA2B,EAAI,KAAK,EAAa,CAAC,cAC1HpL,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,eAAa,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAACoJ,qBAAqB,EAAI,KAAK,EAAa,CAAC,cACrHrL,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,UAAQ,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAACqJ,gBAAgB,EAAI,KAAK,EAAa,CAAC,EACxG,CAAC,EACF,CAAC,cAEPtL,KAAA,CAACxB,IAAI,EAACqJ,IAAI,MAAC2C,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,eACvBjK,IAAA,CAAC7B,UAAU,EAACqM,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAR,QAAA,CAAC,QAAM,CAAY,CAAC,cAChEnK,KAAA,CAAC5B,GAAG,EAACwM,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAV,QAAA,eACjBnK,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,aAAW,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAACsJ,iBAAiB,EAAI,KAAK,EAAa,CAAC,cAC/GvL,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAACuJ,aAAa,EAAI,KAAK,EAAa,CAAC,cACvGxL,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,cAAY,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAACwJ,yBAAyB,EAAI,KAAK,EAAa,CAAC,cACxHzL,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,eAAa,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAACyJ,mBAAmB,EAAI,KAAK,EAAa,CAAC,cACnH1L,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,UAAQ,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAAC0J,cAAc,EAAI,KAAK,EAAa,CAAC,EACtG,CAAC,cAENzL,IAAA,CAAC7B,UAAU,EAACqM,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAR,QAAA,CAAC,eAAa,CAAY,CAAC,cACvEnK,KAAA,CAAC5B,GAAG,EAACwM,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAV,QAAA,eACjBnK,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,gBAAc,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAAC2J,aAAa,EAAI,KAAK,EAAa,CAAC,cAC9G5L,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,kBAAgB,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAAC4J,eAAe,EAAI,GAAG,EAAa,CAAC,cAChH7L,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,QAAM,CAAQ,CAAC,IAAC,CAACtK,2BAA2B,CAACoC,YAAY,CAACqB,mBAAmB,CAAC,EAAa,CAAC,cAChItD,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,eAAa,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAAC6J,YAAY,EAAI,GAAG,EAAa,CAAC,cAC1G9L,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAAC8J,SAAS,EAAI,KAAK,EAAa,CAAC,cACnG/L,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,oBAAkB,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAAC+J,iBAAiB,EAAI,KAAK,EAAa,CAAC,cACtHhM,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,eAAa,CAAQ,CAAC,IAAC,CAAClI,YAAY,CAACgK,YAAY,EAAI,KAAK,EAAa,CAAC,cAC5GjM,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,eAACjK,IAAA,WAAAiK,QAAA,CAAQ,uBAAqB,CAAQ,CAAC,IAAC,CAAC,GAAI,CAAA+B,IAAI,CAACjK,YAAY,CAACkK,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC,EAAa,CAAC,EAChI,CAAC,EACF,CAAC,EACH,CAAC,CACM,CAAC,cAChBlM,IAAA,CAACf,aAAa,EAAAgL,QAAA,cACZjK,IAAA,CAAC3B,MAAM,EAAC8N,OAAO,CAAE1C,kBAAmB,CAAAQ,QAAA,CAAC,QAAM,CAAQ,CAAC,CACvC,CAAC,EACV,CAAC,CAEb,CAAC,CAED;AAEA;AACA,KAAM,CAAAmC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B;AACA,GAAI,CAACjK,KAAK,EAAI,CAACA,KAAK,CAACE,MAAM,EAAI,CAACF,KAAK,CAACM,SAAS,EAAI,CAACN,KAAK,CAACU,KAAK,CAAE,CAC/D,mBACE/C,KAAA,CAAC1B,KAAK,EAACsM,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAE0B,CAAC,CAAE,CAAE,CAAE,CAAApC,QAAA,eACzBjK,IAAA,CAAC7B,UAAU,EAACqM,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAR,QAAA,CAAC,aAAW,CAAY,CAAC,CAC7DnH,YAAY,cACX9C,IAAA,CAACnB,cAAc,GAAE,CAAC,cAElBmB,IAAA,CAAC7B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,CAAC,gCAA8B,CAAY,CACvE,EACI,CAAC,CAEZ,CAEA;AACA,KAAM,CAAA5H,MAAM,CAAGF,KAAK,CAACE,MAAM,EAAI,CAAEC,WAAW,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAChF,KAAM,CAAAC,SAAS,CAAGN,KAAK,CAACM,SAAS,EAAI,CAAEC,oBAAoB,CAAE,CAAC,CAAEC,kBAAkB,CAAE,CAAC,CAAEC,yBAAyB,CAAE,CAAE,CAAC,CACrH,KAAM,CAAAC,KAAK,CAAGV,KAAK,CAACU,KAAK,EAAI,EAAE,CAE/B,mBACE/C,KAAA,CAAC1B,KAAK,EAACsM,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAE0B,CAAC,CAAE,CAAE,CAAE,CAAApC,QAAA,eACzBjK,IAAA,CAAC7B,UAAU,EAACqM,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAR,QAAA,CAAC,aAAW,CAAY,CAAC,CAC7DnH,YAAY,cACX9C,IAAA,CAACnB,cAAc,GAAE,CAAC,cAElBiB,KAAA,CAACxB,IAAI,EAAC8L,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAJ,QAAA,eACzBnK,KAAA,CAACxB,IAAI,EAACqJ,IAAI,MAAC2C,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,eACvBjK,IAAA,CAAC7B,UAAU,EAACqM,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAR,QAAA,CAAC,QAAM,CAAY,CAAC,cAChEnK,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,EAAC,eAAa,CAAC5H,MAAM,CAACC,WAAW,EAAI,CAAC,EAAa,CAAC,cAC/ExC,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,EAAC,cAAY,CAAC5H,MAAM,CAACE,UAAU,EAAI,CAAC,EAAa,CAAC,cAC7EzC,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,EAAC,eAAa,CAAC5H,MAAM,CAACG,WAAW,EAAI,CAAC,EAAa,CAAC,EAE3E,CAAC,cAEP1C,KAAA,CAACxB,IAAI,EAACqJ,IAAI,MAAC2C,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,eACvBjK,IAAA,CAAC7B,UAAU,EAACqM,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAR,QAAA,CAAC,WAAS,CAAY,CAAC,cACnEnK,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,EAAC,iBAAe,CAAC,CAACxH,SAAS,CAACC,oBAAoB,EAAI,CAAC,EAAE4J,OAAO,CAAC,CAAC,CAAC,EAAa,CAAC,cAC1GxM,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,EAAC,gBAAc,CAAC,CAACxH,SAAS,CAACE,kBAAkB,EAAI,CAAC,EAAE2J,OAAO,CAAC,CAAC,CAAC,EAAa,CAAC,cACvGxM,KAAA,CAAC5B,GAAG,EAACwM,EAAE,CAAE,CAAE6B,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAxC,QAAA,eACxDjK,IAAA,CAAC9B,GAAG,EAACwM,EAAE,CAAE,CAAEgC,KAAK,CAAE,MAAM,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAA1C,QAAA,cAChCjK,IAAA,CAACnB,cAAc,EACb2L,OAAO,CAAC,aAAa,CACrBoC,KAAK,CAAEnK,SAAS,CAACG,yBAAyB,EAAI,CAAE,CAChD8H,EAAE,CAAE,CAAEmC,MAAM,CAAE,EAAE,CAAEC,YAAY,CAAE,CAAE,CAAE,CACrC,CAAC,CACC,CAAC,cACN9M,IAAA,CAAC9B,GAAG,EAACwM,EAAE,CAAE,CAAEqC,QAAQ,CAAE,EAAG,CAAE,CAAA9C,QAAA,cACxBjK,IAAA,CAAC7B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAACwC,KAAK,CAAC,gBAAgB,CAAA/C,QAAA,CAAE,GAAG,CAACxH,SAAS,CAACG,yBAAyB,EAAI,CAAC,EAAE0J,OAAO,CAAC,CAAC,CAAC,GAAG,CAAa,CAAC,CAC1H,CAAC,EACH,CAAC,EACF,CAAC,cAEPxM,KAAA,CAACxB,IAAI,EAACqJ,IAAI,MAAC2C,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,eACvBjK,IAAA,CAAC7B,UAAU,EAACqM,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAR,QAAA,CAAC,OAAK,CAAY,CAAC,cAC/DjK,IAAA,CAAC9B,GAAG,EAACwM,EAAE,CAAE,CAAE6B,OAAO,CAAE,MAAM,CAAEU,QAAQ,CAAE,MAAM,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAjD,QAAA,CACpDpH,KAAK,CAACiB,MAAM,CAAG,CAAC,CAAGjB,KAAK,CAACiE,GAAG,CAAC,CAACc,KAAK,CAAEuF,KAAK,gBACzCnN,IAAA,CAACrB,IAAI,EAEHyO,KAAK,CAAE,GAAGxF,KAAK,CAACA,KAAK,EAAI,KAAK,KAAKA,KAAK,CAACyF,KAAK,EAAI,CAAC,EAAG,CACtDC,IAAI,CAAC,OAAO,CACZnB,OAAO,CAAEA,CAAA,GAAM,CACbhJ,UAAU,CAACwG,IAAI,GAAK,CAClB,GAAGA,IAAI,CACPvG,mBAAmB,CAAEwE,KAAK,CAACA,KAAK,GAAK,iBAAiB,CAAG,EAAE,CAAGA,KAAK,CAACA,KACtE,CAAC,CAAC,CAAC,CACL,CAAE,EARGuF,KASN,CACF,CAAC,cACAnN,IAAA,CAAC7B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAAAP,QAAA,CAAC,2BAAyB,CAAY,CAClE,CACE,CAAC,EACF,CAAC,EACH,CACP,EACI,CAAC,CAEZ,CAAC,CAED,mBACEjK,IAAA,CAAC9B,GAAG,EAACqP,SAAS,CAAC,WAAW,CAAAtD,QAAA,CACvB3I,OAAO,cACNxB,KAAA,CAAC5B,GAAG,EAACwM,EAAE,CAAE,CAAE6B,OAAO,CAAE,MAAM,CAAEiB,aAAa,CAAE,QAAQ,CAAEhB,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAxC,QAAA,eACjFjK,IAAA,CAACpB,gBAAgB,EAAC0O,IAAI,CAAE,EAAG,CAAE,CAAC,cAC9BtN,IAAA,CAAC7B,UAAU,EAACuM,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAAxC,QAAA,CAAC,qBAAmB,CAAY,CAAC,cAC3DjK,IAAA,CAAC3B,MAAM,EACLmM,OAAO,CAAC,UAAU,CAClBwC,KAAK,CAAC,SAAS,CACfb,OAAO,CAAEA,CAAA,GAAM1G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE,CACxC+E,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAAxC,QAAA,CACf,oBAED,CAAQ,CAAC,EACN,CAAC,CACJzI,KAAK,cACP1B,KAAA,CAAC5B,GAAG,EAAA+L,QAAA,eACFnK,KAAA,CAACrB,KAAK,EAACqD,QAAQ,CAAC,OAAO,CAAC4I,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAV,QAAA,EACnCzI,KAAK,CACLA,KAAK,CAACgE,QAAQ,CAAC,eAAe,CAAC,eAC9B1F,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,OAAO,CAACE,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAAxC,QAAA,eACxCjK,IAAA,WAAAiK,QAAA,CAAQ,eAAa,CAAQ,CAAC,sEAC9B,cAAAjK,IAAA,QAAK,CAAC,6CACoC,cAAAA,IAAA,SAAAiK,QAAA,CAAM,eAAa,CAAM,CAAC,2CACtE,EAAY,CACb,EACI,CAAC,cACRjK,IAAA,CAAC9B,GAAG,EAACwM,EAAE,CAAE,CAAE6B,OAAO,CAAE,MAAM,CAAEW,GAAG,CAAE,CAAE,CAAE,CAAAjD,QAAA,cACnCjK,IAAA,CAAC3B,MAAM,EACLmM,OAAO,CAAC,WAAW,CACnB+C,SAAS,CAAC,gBAAgB,CAC1BpB,OAAO,CAAEA,CAAA,GAAM1G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE,CAAAsE,QAAA,CACzC,oBAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,cAENnK,KAAA,CAAC5B,GAAG,EAAA+L,QAAA,eAIFjK,IAAA,CAAC9B,GAAG,EAACwM,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAAxC,QAAA,cACjBnK,KAAA,CAAC5B,GAAG,EAACwM,EAAE,CAAE,CAAE6B,OAAO,CAAE,MAAM,CAAEkB,cAAc,CAAE,eAAe,CAAEjB,UAAU,CAAE,QAAQ,CAAE7B,EAAE,CAAE,CAAE,CAAE,CAAAV,QAAA,eACzFnK,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,IAAI,CAAAP,QAAA,EAAC,cACX,CAAC/I,UAAU,CAAC4C,MAAM,CAAG,CAAC,CAAG,IAAI5C,UAAU,CAAC4C,MAAM,GAAG,CAAG,EAAE,EACxD,CAAC,cACb9D,IAAA,CAAC3B,MAAM,EACLmM,OAAO,CAAC,UAAU,CAClB8C,IAAI,CAAC,OAAO,CACZnB,OAAO,CAAEA,CAAA,GAAM1G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE,CACxC+H,SAAS,CAAEpM,OAAO,cAAGtB,IAAA,CAACpB,gBAAgB,EAAC0O,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGtN,IAAA,CAACZ,WAAW,GAAE,CAAE,CACtEuO,QAAQ,CAAErM,OAAQ,CAAA2I,QAAA,CACnB,UAED,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,CAEL/I,UAAU,CAAC4C,MAAM,CAAG,CAAC,cACpBhE,KAAA,CAAC5B,GAAG,EAACwM,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAV,QAAA,EAEhB2D,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,eACrC9N,IAAA,CAAC9B,GAAG,EAACwM,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAE0B,CAAC,CAAE,CAAC,CAAE0B,OAAO,CAAE,SAAS,CAAEjB,YAAY,CAAE,CAAC,CAAEkB,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,WAAW,CAAE1B,OAAO,CAAE,MAAO,CAAE,CAAAtC,QAAA,CACzHiE,MAAM,CAACC,IAAI,CAACjN,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC4F,GAAG,CAACb,GAAG,eACjCnG,KAAA,QAAAmK,QAAA,EAAgBhE,GAAG,CAAC,IAAE,CAACmB,IAAI,CAACgH,SAAS,CAAClN,UAAU,CAAC,CAAC,CAAC,CAAC+E,GAAG,CAAC,CAAC,GAA/CA,GAAqD,CAChE,CAAC,CACC,CACN,cACDjG,IAAA,CAACJ,mBAAmB,EAClByO,IAAI,CAAEnN,UAAW,CACjBI,OAAO,CAAEA,OAAQ,CACjBgN,oBAAoB,CAAGC,YAAY,EAAKvK,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEsK,YAAY,CAACzK,MAAM,CAAE,CAClG0K,iBAAiB,CAAE,EAAAtO,YAAA,CAAAgB,UAAU,CAAC,CAAC,CAAC,UAAAhB,YAAA,iBAAbA,YAAA,CAAeuO,mBAAmB,KAAAtO,aAAA,CAAIe,UAAU,CAAC,CAAC,CAAC,UAAAf,aAAA,iBAAbA,aAAA,CAAeuO,SAAS,KAAAtO,aAAA,CAAIc,UAAU,CAAC,CAAC,CAAC,UAAAd,aAAA,iBAAbA,aAAA,CAAeuO,GAAG,CAAC,CACzG,CAAC,EACC,CAAC,cAEN3O,IAAA,CAACvB,KAAK,EAACqD,QAAQ,CAAC,MAAM,CAAC4I,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAV,QAAA,CAAC,4DAEtC,CAAO,CACR,cAGDnK,KAAA,CAAC5B,GAAG,EAACwM,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAAxC,QAAA,eACjBnK,KAAA,CAAC5B,GAAG,EAACwM,EAAE,CAAE,CAAE6B,OAAO,CAAE,MAAM,CAAEkB,cAAc,CAAE,eAAe,CAAEjB,UAAU,CAAE,QAAQ,CAAE7B,EAAE,CAAE,CAAE,CAAE,CAAAV,QAAA,eACzFnK,KAAA,CAAC3B,UAAU,EAACqM,OAAO,CAAC,IAAI,CAAAP,QAAA,EAAC,aACZ,CAAC7I,SAAS,CAAC0C,MAAM,CAAG,CAAC,CAAG,IAAI1C,SAAS,CAAC0C,MAAM,GAAG,CAAG,EAAE,EACrD,CAAC,cACb9D,IAAA,CAAC3B,MAAM,EACLmM,OAAO,CAAC,UAAU,CAClB8C,IAAI,CAAC,OAAO,CACZnB,OAAO,CAAEA,CAAA,GAAM1G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE,CACxC+H,SAAS,CAAEpM,OAAO,cAAGtB,IAAA,CAACpB,gBAAgB,EAAC0O,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGtN,IAAA,CAACZ,WAAW,GAAE,CAAE,CACtEuO,QAAQ,CAAErM,OAAQ,CAAA2I,QAAA,CACnB,UAED,CAAQ,CAAC,EACN,CAAC,CACL7I,SAAS,CAAC0C,MAAM,CAAG,CAAC,cACnB9D,IAAA,CAACJ,mBAAmB,EAClByO,IAAI,CAAEjN,SAAU,CAChBE,OAAO,CAAEA,OAAQ,CACjBgN,oBAAoB,CAAGC,YAAY,EAAKvK,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAEsK,YAAY,CAACzK,MAAM,CAAE,CAClG,CAAC,cAEF9D,IAAA,CAACvB,KAAK,EAACqD,QAAQ,CAAC,MAAM,CAAC4I,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAV,QAAA,CAAC,uEAEtC,CAAO,CACR,EACE,CAAC,CAKLJ,mBAAmB,CAAC,CAAC,cAGtB7J,IAAA,CAAClB,MAAM,EACL8C,IAAI,CAAErB,qBAAsB,CAC5BuJ,OAAO,CAAEA,CAAA,GAAMtJ,wBAAwB,CAAC,KAAK,CAAE,CAC/CwJ,SAAS,MACTD,QAAQ,CAAC,IAAI,CAAAE,QAAA,cAEbjK,IAAA,CAACR,oBAAoB,EACnBsB,UAAU,CAAEA,UAAW,CACvB8N,SAAS,CAAG/M,OAAO,EAAK,CACtB;AACArB,wBAAwB,CAAC,KAAK,CAAC,CAE/B;AACA,GAAIqB,OAAO,CAAE,CACX;AACAmC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAEpC,OAAO,CAAC,CAC9C;AACA+H,gBAAgB,CAAC/H,OAAO,CAAE,SAAS,CAAC,CACpC;AACAuD,UAAU,CAAC,IAAM,CACfpB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC,CACpD,GAAI,CACF;AACAN,SAAS,CAAC,IAAI,CAAC,CACjB,CAAE,MAAOnC,KAAK,CAAE,CACdwC,OAAO,CAACxC,KAAK,CAAC,2CAA2C,CAAEA,KAAK,CAAC,CACjE;AACAiE,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAC1B,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,IAAM,CACL;AACA3B,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAClD,CACF,CAAE,CACF4K,OAAO,CAAGhN,OAAO,EAAK,CACpB;AACAmC,OAAO,CAACxC,KAAK,CAAC,0CAA0C,CAAEK,OAAO,CAAC,CAClE;AACAiN,KAAK,CAAC,WAAWjN,OAAO,EAAE,CAAC,CAC3B;AACArB,wBAAwB,CAAC,KAAK,CAAC,CAC/B;AACAmD,SAAS,CAAC,CAAC,CACb,CAAE,CACFoL,aAAa,CAAC,aAAa,CAC5B,CAAC,CACI,CAAC,CAIRtO,sBAAsB,EAAIuD,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAEnD,UAAU,CAAC,cAEhHd,IAAA,CAAClB,MAAM,EACL8C,IAAI,CAAEnB,sBAAuB,CAC7BqJ,OAAO,CAAEA,CAAA,GAAMpJ,yBAAyB,CAAC,KAAK,CAAE,CAChDsJ,SAAS,MACTD,QAAQ,CAAC,IAAI,CAAAE,QAAA,cAEbjK,IAAA,CAACR,oBAAoB,EACnBsB,UAAU,CAAEA,UAAW,CACvB8N,SAAS,CAAG/M,OAAO,EAAK,CACtB;AACAnB,yBAAyB,CAAC,KAAK,CAAC,CAEhC;AACA,GAAImB,OAAO,CAAE,CACX;AACAmC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAEpC,OAAO,CAAC,CAC9C;AACA+H,gBAAgB,CAAC/H,OAAO,CAAE,SAAS,CAAC,CACpC;AACAmC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC,CACpD;AACAmB,UAAU,CAAC,IAAM,CACf,GAAI,CACFzB,SAAS,CAAC,IAAI,CAAC,CACjB,CAAE,MAAOnC,KAAK,CAAE,CACdwC,OAAO,CAACxC,KAAK,CAAC,2CAA2C,CAAEA,KAAK,CAAC,CACjE;AACAiE,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAC1B,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,IAAM,CACL;AACA3B,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAClD,CACF,CAAE,CACF4K,OAAO,CAAGhN,OAAO,EAAK,CACpB;AACAmC,OAAO,CAACxC,KAAK,CAAC,sCAAsC,CAAEK,OAAO,CAAC,CAC9D;AACAiN,KAAK,CAAC,WAAWjN,OAAO,EAAE,CAAC,CAC3B;AACAnB,yBAAyB,CAAC,KAAK,CAAC,CAChC;AACAsD,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAChDN,SAAS,CAAC,IAAI,CAAC,CACjB,CAAE,CACFoL,aAAa,CAAC,cAAc,CAC7B,CAAC,CACI,CAAC,cAGT/O,IAAA,CAAClB,MAAM,EAAC8C,IAAI,CAAEjB,sBAAuB,CAACmJ,OAAO,CAAEA,CAAA,GAAMlJ,yBAAyB,CAAC,KAAK,CAAE,CAACmJ,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAC,QAAA,cAC5GjK,IAAA,CAACR,oBAAoB,EACnBsB,UAAU,CAAEA,UAAW,CACvB8N,SAAS,CAAG/M,OAAO,EAAK,CACtB;AACAjB,yBAAyB,CAAC,KAAK,CAAC,CAEhC;AACA,GAAIiB,OAAO,CAAE,CACX;AACAmC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAEpC,OAAO,CAAC,CAC9C;AACA+H,gBAAgB,CAAC/H,OAAO,CAAE,SAAS,CAAC,CACpC;AACAuD,UAAU,CAAC,IAAM,CACfpB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC,CACpD,GAAI,CACF;AACAN,SAAS,CAAC,IAAI,CAAC,CACjB,CAAE,MAAOnC,KAAK,CAAE,CACdwC,OAAO,CAACxC,KAAK,CAAC,2CAA2C,CAAEA,KAAK,CAAC,CACjE;AACAwC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC,CACzDwB,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAC1B,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,IAAM,CACL;AACA3B,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAClD,CACF,CAAE,CACF4K,OAAO,CAAGhN,OAAO,EAAK,CACpB;AACAmC,OAAO,CAACxC,KAAK,CAAC,sCAAsC,CAAEK,OAAO,CAAC,CAC9D;AACA+H,gBAAgB,CAAC,WAAW/H,OAAO,EAAE,CAAE,OAAO,CAAC,CAC/C;AACAjB,yBAAyB,CAAC,KAAK,CAAC,CAChC;AACA+C,SAAS,CAAC,IAAI,CAAC,CACjB,CAAE,CACFoL,aAAa,CAAC,cAAc,CAC7B,CAAC,CACI,CAAC,cAGT/O,IAAA,CAACd,QAAQ,EACP0C,IAAI,CAAEF,YAAY,CAACE,IAAK,CACxBoN,gBAAgB,CAAE,IAAK,CACvBlF,OAAO,CAAEJ,uBAAwB,CACjCuF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAlF,QAAA,cAE3DjK,IAAA,CAACvB,KAAK,EAACqL,OAAO,CAAEJ,uBAAwB,CAAC5H,QAAQ,CAAEJ,YAAY,CAACI,QAAS,CAAC4I,EAAE,CAAE,CAAEgC,KAAK,CAAE,MAAO,CAAE,CAAAzC,QAAA,CAC7FvI,YAAY,CAACG,OAAO,CAChB,CAAC,CACA,CAAC,EACR,CACN,CACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5B,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}