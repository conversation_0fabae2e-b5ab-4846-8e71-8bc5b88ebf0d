{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\nimport { constructFrom } from \"./constructFrom.mjs\";\n\n/**\n * @name addMilliseconds\n * @category Millisecond Helpers\n * @summary Add the specified number of milliseconds to the given date.\n *\n * @description\n * Add the specified number of milliseconds to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of milliseconds to be added.\n *\n * @returns The new date with the milliseconds added\n *\n * @example\n * // Add 750 milliseconds to 10 July 2014 12:45:30.000:\n * const result = addMilliseconds(new Date(2014, 6, 10, 12, 45, 30, 0), 750)\n * //=> Thu Jul 10 2014 12:45:30.750\n */\nexport function addMilliseconds(date, amount) {\n  const timestamp = +toDate(date);\n  return constructFrom(date, timestamp + amount);\n}\n\n// Fallback for modularized imports:\nexport default addMilliseconds;", "map": {"version": 3, "names": ["toDate", "constructFrom", "addMilliseconds", "date", "amount", "timestamp"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/addMilliseconds.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\nimport { constructFrom } from \"./constructFrom.mjs\";\n\n/**\n * @name addMilliseconds\n * @category Millisecond Helpers\n * @summary Add the specified number of milliseconds to the given date.\n *\n * @description\n * Add the specified number of milliseconds to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of milliseconds to be added.\n *\n * @returns The new date with the milliseconds added\n *\n * @example\n * // Add 750 milliseconds to 10 July 2014 12:45:30.000:\n * const result = addMilliseconds(new Date(2014, 6, 10, 12, 45, 30, 0), 750)\n * //=> Thu Jul 10 2014 12:45:30.750\n */\nexport function addMilliseconds(date, amount) {\n  const timestamp = +toDate(date);\n  return constructFrom(date, timestamp + amount);\n}\n\n// Fallback for modularized imports:\nexport default addMilliseconds;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;AACrC,SAASC,aAAa,QAAQ,qBAAqB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAC5C,MAAMC,SAAS,GAAG,CAACL,MAAM,CAACG,IAAI,CAAC;EAC/B,OAAOF,aAAa,CAACE,IAAI,EAAEE,SAAS,GAAGD,MAAM,CAAC;AAChD;;AAEA;AACA,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}