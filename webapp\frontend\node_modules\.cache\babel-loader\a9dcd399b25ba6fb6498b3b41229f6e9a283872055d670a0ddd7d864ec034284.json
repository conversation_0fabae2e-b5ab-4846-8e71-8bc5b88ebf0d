{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"πΧ\", \"μΧ\"],\n  abbreviated: [\"π.Χ.\", \"μ.Χ.\"],\n  wide: [\"προ Χριστού\", \"μετά Χριστόν\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Τ1\", \"Τ2\", \"Τ3\", \"Τ4\"],\n  wide: [\"1ο τρίμηνο\", \"2ο τρίμηνο\", \"3ο τρίμηνο\", \"4ο τρίμηνο\"]\n};\nconst monthValues = {\n  narrow: [\"Ι\", \"Φ\", \"Μ\", \"Α\", \"Μ\", \"Ι\", \"Ι\", \"Α\", \"Σ\", \"Ο\", \"Ν\", \"Δ\"],\n  abbreviated: [\"Ιαν\", \"Φεβ\", \"Μάρ\", \"Απρ\", \"Μά<PERSON>\", \"Ιού<PERSON>\", \"<PERSON>ού<PERSON>\", \"Αύγ\", \"Σε<PERSON>\", \"Οκτ\", \"Νοέ\", \"Δεκ\"],\n  wide: [\"Ιανουάριος\", \"Φεβρουάριος\", \"Μάρτιος\", \"Απρίλιος\", \"Μάιος\", \"Ιούνιος\", \"Ιούλιος\", \"Αύγουστος\", \"Σεπτέμβριος\", \"Οκτώβριος\", \"Νοέμβριος\", \"Δεκέμβριος\"]\n};\nconst formattingMonthValues = {\n  narrow: [\"Ι\", \"Φ\", \"Μ\", \"Α\", \"Μ\", \"Ι\", \"Ι\", \"Α\", \"Σ\", \"Ο\", \"Ν\", \"Δ\"],\n  abbreviated: [\"Ιαν\", \"Φεβ\", \"Μαρ\", \"Απρ\", \"Μαΐ\", \"Ιουν\", \"Ιουλ\", \"Αυγ\", \"Σεπ\", \"Οκτ\", \"Νοε\", \"Δεκ\"],\n  wide: [\"Ιανουαρίου\", \"Φεβρουαρίου\", \"Μαρτίου\", \"Απριλίου\", \"Μαΐου\", \"Ιουνίου\", \"Ιουλίου\", \"Αυγούστου\", \"Σεπτεμβρίου\", \"Οκτωβρίου\", \"Νοεμβρίου\", \"Δεκεμβρίου\"]\n};\nconst dayValues = {\n  narrow: [\"Κ\", \"Δ\", \"T\", \"Τ\", \"Π\", \"Π\", \"Σ\"],\n  short: [\"Κυ\", \"Δε\", \"Τρ\", \"Τε\", \"Πέ\", \"Πα\", \"Σά\"],\n  abbreviated: [\"Κυρ\", \"Δευ\", \"Τρί\", \"Τετ\", \"Πέμ\", \"Παρ\", \"Σάβ\"],\n  wide: [\"Κυριακή\", \"Δευτέρα\", \"Τρίτη\", \"Τετάρτη\", \"Πέμπτη\", \"Παρασκευή\", \"Σάββατο\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"πμ\",\n    pm: \"μμ\",\n    midnight: \"μεσάνυχτα\",\n    noon: \"μεσημέρι\",\n    morning: \"πρωί\",\n    afternoon: \"απόγευμα\",\n    evening: \"βράδυ\",\n    night: \"νύχτα\"\n  },\n  abbreviated: {\n    am: \"π.μ.\",\n    pm: \"μ.μ.\",\n    midnight: \"μεσάνυχτα\",\n    noon: \"μεσημέρι\",\n    morning: \"πρωί\",\n    afternoon: \"απόγευμα\",\n    evening: \"βράδυ\",\n    night: \"νύχτα\"\n  },\n  wide: {\n    am: \"π.μ.\",\n    pm: \"μ.μ.\",\n    midnight: \"μεσάνυχτα\",\n    noon: \"μεσημέρι\",\n    morning: \"πρωί\",\n    afternoon: \"απόγευμα\",\n    evening: \"βράδυ\",\n    night: \"νύχτα\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n  let suffix;\n  if (unit === \"year\" || unit === \"month\") {\n    suffix = \"ος\";\n  } else if (unit === \"week\" || unit === \"dayOfYear\" || unit === \"day\" || unit === \"hour\" || unit === \"date\") {\n    suffix = \"η\";\n  } else {\n    suffix = \"ο\";\n  }\n  return number + suffix;\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "suffix", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/el/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"πΧ\", \"μΧ\"],\n  abbreviated: [\"π.Χ.\", \"μ.Χ.\"],\n  wide: [\"προ Χριστού\", \"μετά Χριστόν\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Τ1\", \"Τ2\", \"Τ3\", \"Τ4\"],\n  wide: [\"1ο τρίμηνο\", \"2ο τρίμηνο\", \"3ο τρίμηνο\", \"4ο τρίμηνο\"],\n};\n\nconst monthValues = {\n  narrow: [\"Ι\", \"Φ\", \"Μ\", \"Α\", \"Μ\", \"Ι\", \"Ι\", \"Α\", \"Σ\", \"Ο\", \"Ν\", \"Δ\"],\n  abbreviated: [\n    \"Ιαν\",\n    \"Φεβ\",\n    \"Μάρ\",\n    \"Απρ\",\n    \"<PERSON>ά<PERSON>\",\n    \"Ιού<PERSON>\",\n    \"<PERSON>ούλ\",\n    \"Αύγ\",\n    \"Σε<PERSON>\",\n    \"Οκτ\",\n    \"Νοέ\",\n    \"Δεκ\",\n  ],\n\n  wide: [\n    \"Ιανουάριος\",\n    \"Φεβρουάριος\",\n    \"Μάρτιος\",\n    \"Απρίλιος\",\n    \"Μάιος\",\n    \"Ιούνιος\",\n    \"Ιούλιος\",\n    \"Αύγουστος\",\n    \"Σεπτέμβριος\",\n    \"Οκτώβριος\",\n    \"Νοέμβριος\",\n    \"Δεκέμβριος\",\n  ],\n};\n\nconst formattingMonthValues = {\n  narrow: [\"Ι\", \"Φ\", \"Μ\", \"Α\", \"Μ\", \"Ι\", \"Ι\", \"Α\", \"Σ\", \"Ο\", \"Ν\", \"Δ\"],\n  abbreviated: [\n    \"Ιαν\",\n    \"Φεβ\",\n    \"Μαρ\",\n    \"Απρ\",\n    \"Μαΐ\",\n    \"Ιουν\",\n    \"Ιουλ\",\n    \"Αυγ\",\n    \"Σεπ\",\n    \"Οκτ\",\n    \"Νοε\",\n    \"Δεκ\",\n  ],\n\n  wide: [\n    \"Ιανουαρίου\",\n    \"Φεβρουαρίου\",\n    \"Μαρτίου\",\n    \"Απριλίου\",\n    \"Μαΐου\",\n    \"Ιουνίου\",\n    \"Ιουλίου\",\n    \"Αυγούστου\",\n    \"Σεπτεμβρίου\",\n    \"Οκτωβρίου\",\n    \"Νοεμβρίου\",\n    \"Δεκεμβρίου\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"Κ\", \"Δ\", \"T\", \"Τ\", \"Π\", \"Π\", \"Σ\"],\n  short: [\"Κυ\", \"Δε\", \"Τρ\", \"Τε\", \"Πέ\", \"Πα\", \"Σά\"],\n  abbreviated: [\"Κυρ\", \"Δευ\", \"Τρί\", \"Τετ\", \"Πέμ\", \"Παρ\", \"Σάβ\"],\n  wide: [\n    \"Κυριακή\",\n    \"Δευτέρα\",\n    \"Τρίτη\",\n    \"Τετάρτη\",\n    \"Πέμπτη\",\n    \"Παρασκευή\",\n    \"Σάββατο\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"πμ\",\n    pm: \"μμ\",\n    midnight: \"μεσάνυχτα\",\n    noon: \"μεσημέρι\",\n    morning: \"πρωί\",\n    afternoon: \"απόγευμα\",\n    evening: \"βράδυ\",\n    night: \"νύχτα\",\n  },\n  abbreviated: {\n    am: \"π.μ.\",\n    pm: \"μ.μ.\",\n    midnight: \"μεσάνυχτα\",\n    noon: \"μεσημέρι\",\n    morning: \"πρωί\",\n    afternoon: \"απόγευμα\",\n    evening: \"βράδυ\",\n    night: \"νύχτα\",\n  },\n  wide: {\n    am: \"π.μ.\",\n    pm: \"μ.μ.\",\n    midnight: \"μεσάνυχτα\",\n    noon: \"μεσημέρι\",\n    morning: \"πρωί\",\n    afternoon: \"απόγευμα\",\n    evening: \"βράδυ\",\n    night: \"νύχτα\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  const unit = options?.unit;\n  let suffix;\n\n  if (unit === \"year\" || unit === \"month\") {\n    suffix = \"ος\";\n  } else if (\n    unit === \"week\" ||\n    unit === \"dayOfYear\" ||\n    unit === \"day\" ||\n    unit === \"hour\" ||\n    unit === \"date\"\n  ) {\n    suffix = \"η\";\n  } else {\n    suffix = \"ο\";\n  }\n\n  return number + suffix;\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EACpBC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EAC7BC,IAAI,EAAE,CAAC,aAAa,EAAE,cAAc;AACtC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY;AAC/D,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,YAAY,EACZ,aAAa,EACb,SAAS,EACT,UAAU,EACV,OAAO,EACP,SAAS,EACT,SAAS,EACT,WAAW,EACX,aAAa,EACb,WAAW,EACX,WAAW,EACX,YAAY;AAEhB,CAAC;AAED,MAAMG,qBAAqB,GAAG;EAC5BL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,YAAY,EACZ,aAAa,EACb,SAAS,EACT,UAAU,EACV,OAAO,EACP,SAAS,EACT,SAAS,EACT,WAAW,EACX,aAAa,EACb,WAAW,EACX,WAAW,EACX,YAAY;AAEhB,CAAC;AAED,MAAMI,SAAS,GAAG;EAChBN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CACJ,SAAS,EACT,SAAS,EACT,OAAO,EACP,SAAS,EACT,QAAQ,EACR,WAAW,EACX,SAAS;AAEb,CAAC;AAED,MAAMM,eAAe,GAAG;EACtBR,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC9C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,MAAMI,IAAI,GAAGH,OAAO,EAAEG,IAAI;EAC1B,IAAIC,MAAM;EAEV,IAAID,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,OAAO,EAAE;IACvCC,MAAM,GAAG,IAAI;EACf,CAAC,MAAM,IACLD,IAAI,KAAK,MAAM,IACfA,IAAI,KAAK,WAAW,IACpBA,IAAI,KAAK,KAAK,IACdA,IAAI,KAAK,MAAM,IACfA,IAAI,KAAK,MAAM,EACf;IACAC,MAAM,GAAG,GAAG;EACd,CAAC,MAAM;IACLA,MAAM,GAAG,GAAG;EACd;EAEA,OAAOH,MAAM,GAAGG,MAAM;AACxB,CAAC;AAED,OAAO,MAAMC,QAAQ,GAAG;EACtBP,aAAa;EAEbQ,GAAG,EAAE3B,eAAe,CAAC;IACnB4B,MAAM,EAAE3B,SAAS;IACjB4B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE9B,eAAe,CAAC;IACvB4B,MAAM,EAAEvB,aAAa;IACrBwB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAEhC,eAAe,CAAC;IACrB4B,MAAM,EAAEtB,WAAW;IACnBuB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAE1B,qBAAqB;IACvC2B,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EAEFC,GAAG,EAAEnC,eAAe,CAAC;IACnB4B,MAAM,EAAEpB,SAAS;IACjBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFO,SAAS,EAAEpC,eAAe,CAAC;IACzB4B,MAAM,EAAElB,eAAe;IACvBmB,YAAY,EAAE;EAChB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}