{"ast": null, "code": "import React from'react';import{Box,Typography,Grid,Card,CardContent,CardActionArea,Avatar}from'@mui/material';import{useNavigate}from'react-router-dom';import{AdminPanelSettings as AdminIcon,Construction as ConstructionIcon,Cable as CableIcon,Description as ReportIcon}from'@mui/icons-material';import{useAuth}from'../context/AuthContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const HomePage=()=>{const{user}=useAuth();const navigate=useNavigate();// Naviga a un percorso\nconst navigateTo=path=>{navigate(path);};return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,children:\"Benvenuto nel Sistema di Gestione Cantieri\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",paragraph:true,children:\"Seleziona una delle opzioni disponibili per iniziare a lavorare.\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,sx:{mt:2},children:[(user===null||user===void 0?void 0:user.role)==='owner'&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardActionArea,{onClick:()=>navigateTo('/dashboard/admin'),children:[/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',p:2,bgcolor:'#f5f5f5',height:140},children:/*#__PURE__*/_jsx(Avatar,{sx:{width:80,height:80,bgcolor:'primary.main',mt:2},children:/*#__PURE__*/_jsx(AdminIcon,{sx:{fontSize:50}})})}),/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{gutterBottom:true,variant:\"h5\",component:\"div\",children:\"Amministrazione\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Gestisci utenti, visualizza il database e amministra il sistema.\"})]})]})})}),(user===null||user===void 0?void 0:user.role)!=='owner'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardActionArea,{onClick:()=>navigateTo('/dashboard/cantieri'),children:[/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',p:2,bgcolor:'#f5f5f5',height:140},children:/*#__PURE__*/_jsx(Avatar,{sx:{width:80,height:80,bgcolor:'secondary.main',mt:2},children:/*#__PURE__*/_jsx(ConstructionIcon,{sx:{fontSize:50}})})}),/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{gutterBottom:true,variant:\"h5\",component:\"div\",children:\"I Miei Cantieri\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Visualizza e gestisci i tuoi cantieri.\"})]})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardActionArea,{onClick:()=>navigateTo('/dashboard/cavi'),children:[/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',p:2,bgcolor:'#f5f5f5',height:140},children:/*#__PURE__*/_jsx(Avatar,{sx:{width:80,height:80,bgcolor:'info.main',mt:2},children:/*#__PURE__*/_jsx(CableIcon,{sx:{fontSize:50}})})}),/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{gutterBottom:true,variant:\"h5\",component:\"div\",children:\"Gestione Cavi\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Gestisci i cavi, le bobine e le certificazioni.\"})]})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(CardActionArea,{onClick:()=>navigateTo('/dashboard/report'),children:[/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',p:2,bgcolor:'#f5f5f5',height:140},children:/*#__PURE__*/_jsx(Avatar,{sx:{width:80,height:80,bgcolor:'success.main',mt:2},children:/*#__PURE__*/_jsx(ReportIcon,{sx:{fontSize:50}})})}),/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{gutterBottom:true,variant:\"h5\",component:\"div\",children:\"Report\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Genera e visualizza report sui cantieri e sui cavi.\"})]})]})})})]})]})]});};export default HomePage;", "map": {"version": 3, "names": ["React", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActionArea", "Avatar", "useNavigate", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "HomePage", "user", "navigate", "navigateTo", "path", "children", "variant", "gutterBottom", "paragraph", "container", "spacing", "sx", "mt", "role", "item", "xs", "sm", "md", "onClick", "display", "justifyContent", "p", "bgcolor", "height", "width", "fontSize", "component", "color"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, Grid, Card, CardContent, CardActionArea, Avatar } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\n\nconst HomePage = () => {\n  const { user } = useAuth();\n  const navigate = useNavigate();\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    navigate(path);\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        Benvenuto nel Sistema di Gestione Cantieri\n      </Typography>\n\n      <Typography variant=\"body1\" paragraph>\n        Seleziona una delle opzioni disponibili per iniziare a lavorare.\n      </Typography>\n\n      <Grid container spacing={3} sx={{ mt: 2 }}>\n        {/* Card per Amministrazione (solo per admin) */}\n        {user?.role === 'owner' && (\n          <Grid item xs={12} sm={6} md={4}>\n            <Card>\n              <CardActionArea onClick={() => navigateTo('/dashboard/admin')}>\n                <Box sx={{ display: 'flex', justifyContent: 'center', p: 2, bgcolor: '#f5f5f5', height: 140 }}>\n                  <Avatar sx={{ width: 80, height: 80, bgcolor: 'primary.main', mt: 2 }}>\n                    <AdminIcon sx={{ fontSize: 50 }} />\n                  </Avatar>\n                </Box>\n                <CardContent>\n                  <Typography gutterBottom variant=\"h5\" component=\"div\">\n                    Amministrazione\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Gestisci utenti, visualizza il database e amministra il sistema.\n                  </Typography>\n                </CardContent>\n              </CardActionArea>\n            </Card>\n          </Grid>\n        )}\n\n        {/* Card per I Miei Cantieri, Gestione Cavi e Report (solo per utenti non admin) */}\n        {user?.role !== 'owner' && (\n          <>\n            {/* Card per I Miei Cantieri */}\n            <Grid item xs={12} sm={6} md={4}>\n              <Card>\n                <CardActionArea onClick={() => navigateTo('/dashboard/cantieri')}>\n                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 2, bgcolor: '#f5f5f5', height: 140 }}>\n                    <Avatar sx={{ width: 80, height: 80, bgcolor: 'secondary.main', mt: 2 }}>\n                      <ConstructionIcon sx={{ fontSize: 50 }} />\n                    </Avatar>\n                  </Box>\n                  <CardContent>\n                    <Typography gutterBottom variant=\"h5\" component=\"div\">\n                      I Miei Cantieri\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Visualizza e gestisci i tuoi cantieri.\n                    </Typography>\n                  </CardContent>\n                </CardActionArea>\n              </Card>\n            </Grid>\n\n            {/* Card per Gestione Cavi */}\n            <Grid item xs={12} sm={6} md={4}>\n              <Card>\n                <CardActionArea onClick={() => navigateTo('/dashboard/cavi')}>\n                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 2, bgcolor: '#f5f5f5', height: 140 }}>\n                    <Avatar sx={{ width: 80, height: 80, bgcolor: 'info.main', mt: 2 }}>\n                      <CableIcon sx={{ fontSize: 50 }} />\n                    </Avatar>\n                  </Box>\n                  <CardContent>\n                    <Typography gutterBottom variant=\"h5\" component=\"div\">\n                      Gestione Cavi\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Gestisci i cavi, le bobine e le certificazioni.\n                    </Typography>\n                  </CardContent>\n                </CardActionArea>\n              </Card>\n            </Grid>\n\n            {/* Card per Report */}\n            <Grid item xs={12} sm={6} md={4}>\n              <Card>\n                <CardActionArea onClick={() => navigateTo('/dashboard/report')}>\n                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 2, bgcolor: '#f5f5f5', height: 140 }}>\n                    <Avatar sx={{ width: 80, height: 80, bgcolor: 'success.main', mt: 2 }}>\n                      <ReportIcon sx={{ fontSize: 50 }} />\n                    </Avatar>\n                  </Box>\n                  <CardContent>\n                    <Typography gutterBottom variant=\"h5\" component=\"div\">\n                      Report\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Genera e visualizza report sui cantieri e sui cavi.\n                    </Typography>\n                  </CardContent>\n                </CardActionArea>\n              </Card>\n            </Grid>\n          </>\n        )}\n      </Grid>\n    </Box>\n  );\n};\n\nexport default HomePage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,GAAG,CAAEC,UAAU,CAAEC,IAAI,CAAEC,IAAI,CAAEC,WAAW,CAAEC,cAAc,CAAEC,MAAM,KAAQ,eAAe,CAChG,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,kBAAkB,GAAI,CAAAC,SAAS,CAC/BC,YAAY,GAAI,CAAAC,gBAAgB,CAChCC,KAAK,GAAI,CAAAC,SAAS,CAClBC,WAAW,GAAI,CAAAC,UAAU,KACpB,qBAAqB,CAC5B,OAASC,OAAO,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEjD,KAAM,CAAAC,QAAQ,CAAGA,CAAA,GAAM,CACrB,KAAM,CAAEC,IAAK,CAAC,CAAGR,OAAO,CAAC,CAAC,CAC1B,KAAM,CAAAS,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAmB,UAAU,CAAIC,IAAI,EAAK,CAC3BF,QAAQ,CAACE,IAAI,CAAC,CAChB,CAAC,CAED,mBACEP,KAAA,CAACpB,GAAG,EAAA4B,QAAA,eACFV,IAAA,CAACjB,UAAU,EAAC4B,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,4CAEtC,CAAY,CAAC,cAEbV,IAAA,CAACjB,UAAU,EAAC4B,OAAO,CAAC,OAAO,CAACE,SAAS,MAAAH,QAAA,CAAC,kEAEtC,CAAY,CAAC,cAEbR,KAAA,CAAClB,IAAI,EAAC8B,SAAS,MAACC,OAAO,CAAE,CAAE,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,EAEvC,CAAAJ,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEY,IAAI,IAAK,OAAO,eACrBlB,IAAA,CAAChB,IAAI,EAACmC,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cAC9BV,IAAA,CAACf,IAAI,EAAAyB,QAAA,cACHR,KAAA,CAACf,cAAc,EAACoC,OAAO,CAAEA,CAAA,GAAMf,UAAU,CAAC,kBAAkB,CAAE,CAAAE,QAAA,eAC5DV,IAAA,CAAClB,GAAG,EAACkC,EAAE,CAAE,CAAEQ,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEC,CAAC,CAAE,CAAC,CAAEC,OAAO,CAAE,SAAS,CAAEC,MAAM,CAAE,GAAI,CAAE,CAAAlB,QAAA,cAC5FV,IAAA,CAACZ,MAAM,EAAC4B,EAAE,CAAE,CAAEa,KAAK,CAAE,EAAE,CAAED,MAAM,CAAE,EAAE,CAAED,OAAO,CAAE,cAAc,CAAEV,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,cACpEV,IAAA,CAACT,SAAS,EAACyB,EAAE,CAAE,CAAEc,QAAQ,CAAE,EAAG,CAAE,CAAE,CAAC,CAC7B,CAAC,CACN,CAAC,cACN5B,KAAA,CAAChB,WAAW,EAAAwB,QAAA,eACVV,IAAA,CAACjB,UAAU,EAAC6B,YAAY,MAACD,OAAO,CAAC,IAAI,CAACoB,SAAS,CAAC,KAAK,CAAArB,QAAA,CAAC,iBAEtD,CAAY,CAAC,cACbV,IAAA,CAACjB,UAAU,EAAC4B,OAAO,CAAC,OAAO,CAACqB,KAAK,CAAC,gBAAgB,CAAAtB,QAAA,CAAC,kEAEnD,CAAY,CAAC,EACF,CAAC,EACA,CAAC,CACb,CAAC,CACH,CACP,CAGA,CAAAJ,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEY,IAAI,IAAK,OAAO,eACrBhB,KAAA,CAAAE,SAAA,EAAAM,QAAA,eAEEV,IAAA,CAAChB,IAAI,EAACmC,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cAC9BV,IAAA,CAACf,IAAI,EAAAyB,QAAA,cACHR,KAAA,CAACf,cAAc,EAACoC,OAAO,CAAEA,CAAA,GAAMf,UAAU,CAAC,qBAAqB,CAAE,CAAAE,QAAA,eAC/DV,IAAA,CAAClB,GAAG,EAACkC,EAAE,CAAE,CAAEQ,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEC,CAAC,CAAE,CAAC,CAAEC,OAAO,CAAE,SAAS,CAAEC,MAAM,CAAE,GAAI,CAAE,CAAAlB,QAAA,cAC5FV,IAAA,CAACZ,MAAM,EAAC4B,EAAE,CAAE,CAAEa,KAAK,CAAE,EAAE,CAAED,MAAM,CAAE,EAAE,CAAED,OAAO,CAAE,gBAAgB,CAAEV,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,cACtEV,IAAA,CAACP,gBAAgB,EAACuB,EAAE,CAAE,CAAEc,QAAQ,CAAE,EAAG,CAAE,CAAE,CAAC,CACpC,CAAC,CACN,CAAC,cACN5B,KAAA,CAAChB,WAAW,EAAAwB,QAAA,eACVV,IAAA,CAACjB,UAAU,EAAC6B,YAAY,MAACD,OAAO,CAAC,IAAI,CAACoB,SAAS,CAAC,KAAK,CAAArB,QAAA,CAAC,iBAEtD,CAAY,CAAC,cACbV,IAAA,CAACjB,UAAU,EAAC4B,OAAO,CAAC,OAAO,CAACqB,KAAK,CAAC,gBAAgB,CAAAtB,QAAA,CAAC,wCAEnD,CAAY,CAAC,EACF,CAAC,EACA,CAAC,CACb,CAAC,CACH,CAAC,cAGPV,IAAA,CAAChB,IAAI,EAACmC,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cAC9BV,IAAA,CAACf,IAAI,EAAAyB,QAAA,cACHR,KAAA,CAACf,cAAc,EAACoC,OAAO,CAAEA,CAAA,GAAMf,UAAU,CAAC,iBAAiB,CAAE,CAAAE,QAAA,eAC3DV,IAAA,CAAClB,GAAG,EAACkC,EAAE,CAAE,CAAEQ,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEC,CAAC,CAAE,CAAC,CAAEC,OAAO,CAAE,SAAS,CAAEC,MAAM,CAAE,GAAI,CAAE,CAAAlB,QAAA,cAC5FV,IAAA,CAACZ,MAAM,EAAC4B,EAAE,CAAE,CAAEa,KAAK,CAAE,EAAE,CAAED,MAAM,CAAE,EAAE,CAAED,OAAO,CAAE,WAAW,CAAEV,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,cACjEV,IAAA,CAACL,SAAS,EAACqB,EAAE,CAAE,CAAEc,QAAQ,CAAE,EAAG,CAAE,CAAE,CAAC,CAC7B,CAAC,CACN,CAAC,cACN5B,KAAA,CAAChB,WAAW,EAAAwB,QAAA,eACVV,IAAA,CAACjB,UAAU,EAAC6B,YAAY,MAACD,OAAO,CAAC,IAAI,CAACoB,SAAS,CAAC,KAAK,CAAArB,QAAA,CAAC,eAEtD,CAAY,CAAC,cACbV,IAAA,CAACjB,UAAU,EAAC4B,OAAO,CAAC,OAAO,CAACqB,KAAK,CAAC,gBAAgB,CAAAtB,QAAA,CAAC,iDAEnD,CAAY,CAAC,EACF,CAAC,EACA,CAAC,CACb,CAAC,CACH,CAAC,cAGPV,IAAA,CAAChB,IAAI,EAACmC,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAZ,QAAA,cAC9BV,IAAA,CAACf,IAAI,EAAAyB,QAAA,cACHR,KAAA,CAACf,cAAc,EAACoC,OAAO,CAAEA,CAAA,GAAMf,UAAU,CAAC,mBAAmB,CAAE,CAAAE,QAAA,eAC7DV,IAAA,CAAClB,GAAG,EAACkC,EAAE,CAAE,CAAEQ,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEC,CAAC,CAAE,CAAC,CAAEC,OAAO,CAAE,SAAS,CAAEC,MAAM,CAAE,GAAI,CAAE,CAAAlB,QAAA,cAC5FV,IAAA,CAACZ,MAAM,EAAC4B,EAAE,CAAE,CAAEa,KAAK,CAAE,EAAE,CAAED,MAAM,CAAE,EAAE,CAAED,OAAO,CAAE,cAAc,CAAEV,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,cACpEV,IAAA,CAACH,UAAU,EAACmB,EAAE,CAAE,CAAEc,QAAQ,CAAE,EAAG,CAAE,CAAE,CAAC,CAC9B,CAAC,CACN,CAAC,cACN5B,KAAA,CAAChB,WAAW,EAAAwB,QAAA,eACVV,IAAA,CAACjB,UAAU,EAAC6B,YAAY,MAACD,OAAO,CAAC,IAAI,CAACoB,SAAS,CAAC,KAAK,CAAArB,QAAA,CAAC,QAEtD,CAAY,CAAC,cACbV,IAAA,CAACjB,UAAU,EAAC4B,OAAO,CAAC,OAAO,CAACqB,KAAK,CAAC,gBAAgB,CAAAtB,QAAA,CAAC,qDAEnD,CAAY,CAAC,EACF,CAAC,EACA,CAAC,CACb,CAAC,CACH,CAAC,EACP,CACH,EACG,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}