{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"items\", \"changeImportance\"],\n  _excluded2 = [\"getValue\"];\nimport * as React from 'react';\nimport { styled } from '@mui/material/styles';\nimport PropTypes from 'prop-types';\nimport List from '@mui/material/List';\nimport ListItem from '@mui/material/ListItem';\nimport Chip from '@mui/material/Chip';\nimport { VIEW_HEIGHT } from \"../internals/constants/dimensions.js\";\nimport { useIsValidValue, usePickerActionsContext } from \"../hooks/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst PickersShortcutsRoot = styled(List, {\n  name: 'MuiPickersLayout',\n  slot: 'Shortcuts'\n})({});\n\n/**\n * Demos:\n *\n * - [Shortcuts](https://mui.com/x/react-date-pickers/shortcuts/)\n *\n * API:\n *\n * - [PickersShortcuts API](https://mui.com/x/api/date-pickers/pickers-shortcuts/)\n */\nfunction PickersShortcuts(props) {\n  const {\n      items,\n      changeImportance = 'accept'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    setValue\n  } = usePickerActionsContext();\n  const isValidValue = useIsValidValue();\n  if (items == null || items.length === 0) {\n    return null;\n  }\n  const resolvedItems = items.map(_ref => {\n    let {\n        getValue\n      } = _ref,\n      item = _objectWithoutPropertiesLoose(_ref, _excluded2);\n    const newValue = getValue({\n      isValid: isValidValue\n    });\n    return _extends({}, item, {\n      label: item.label,\n      onClick: () => {\n        setValue(newValue, {\n          changeImportance,\n          shortcut: item\n        });\n      },\n      disabled: !isValidValue(newValue)\n    });\n  });\n  return /*#__PURE__*/_jsx(PickersShortcutsRoot, _extends({\n    dense: true,\n    sx: [{\n      maxHeight: VIEW_HEIGHT,\n      maxWidth: 200,\n      overflow: 'auto'\n    }, ...(Array.isArray(other.sx) ? other.sx : [other.sx])]\n  }, other, {\n    children: resolvedItems.map(item => {\n      return /*#__PURE__*/_jsx(ListItem, {\n        children: /*#__PURE__*/_jsx(Chip, _extends({}, item))\n      }, item.id ?? item.label);\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? PickersShortcuts.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Importance of the change when picking a shortcut:\n   * - \"accept\": fires `onChange`, fires `onAccept` and closes the Picker.\n   * - \"set\": fires `onChange` but do not fire `onAccept` and does not close the Picker.\n   * @default \"accept\"\n   */\n  changeImportance: PropTypes.oneOf(['accept', 'set']),\n  className: PropTypes.string,\n  component: PropTypes.elementType,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used for\n   * the list and list items.\n   * The prop is available to descendant components as the `dense` context.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * If `true`, vertical padding is removed from the list.\n   * @default false\n   */\n  disablePadding: PropTypes.bool,\n  /**\n   * Ordered array of shortcuts to display.\n   * If empty, does not display the shortcuts.\n   * @default []\n   */\n  items: PropTypes.arrayOf(PropTypes.shape({\n    getValue: PropTypes.func.isRequired,\n    id: PropTypes.string,\n    label: PropTypes.string.isRequired\n  })),\n  style: PropTypes.object,\n  /**\n   * The content of the subheader, normally `ListSubheader`.\n   */\n  subheader: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { PickersShortcuts };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "React", "styled", "PropTypes", "List", "ListItem", "Chip", "VIEW_HEIGHT", "useIsValidValue", "usePickerActionsContext", "jsx", "_jsx", "PickersShortcutsRoot", "name", "slot", "PickersShortcuts", "props", "items", "changeImportance", "other", "setValue", "isValidValue", "length", "resolvedItems", "map", "_ref", "getValue", "item", "newValue", "<PERSON><PERSON><PERSON><PERSON>", "label", "onClick", "shortcut", "disabled", "dense", "sx", "maxHeight", "max<PERSON><PERSON><PERSON>", "overflow", "Array", "isArray", "children", "id", "process", "env", "NODE_ENV", "propTypes", "oneOf", "className", "string", "component", "elementType", "bool", "disablePadding", "arrayOf", "shape", "func", "isRequired", "style", "object", "subheader", "node", "oneOfType"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/PickersShortcuts/PickersShortcuts.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"items\", \"changeImportance\"],\n  _excluded2 = [\"getValue\"];\nimport * as React from 'react';\nimport { styled } from '@mui/material/styles';\nimport PropTypes from 'prop-types';\nimport List from '@mui/material/List';\nimport ListItem from '@mui/material/ListItem';\nimport Chip from '@mui/material/Chip';\nimport { VIEW_HEIGHT } from \"../internals/constants/dimensions.js\";\nimport { useIsValidValue, usePickerActionsContext } from \"../hooks/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst PickersShortcutsRoot = styled(List, {\n  name: 'MuiPickersLayout',\n  slot: 'Shortcuts'\n})({});\n\n/**\n * Demos:\n *\n * - [Shortcuts](https://mui.com/x/react-date-pickers/shortcuts/)\n *\n * API:\n *\n * - [PickersShortcuts API](https://mui.com/x/api/date-pickers/pickers-shortcuts/)\n */\nfunction PickersShortcuts(props) {\n  const {\n      items,\n      changeImportance = 'accept'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    setValue\n  } = usePickerActionsContext();\n  const isValidValue = useIsValidValue();\n  if (items == null || items.length === 0) {\n    return null;\n  }\n  const resolvedItems = items.map(_ref => {\n    let {\n        getValue\n      } = _ref,\n      item = _objectWithoutPropertiesLoose(_ref, _excluded2);\n    const newValue = getValue({\n      isValid: isValidValue\n    });\n    return _extends({}, item, {\n      label: item.label,\n      onClick: () => {\n        setValue(newValue, {\n          changeImportance,\n          shortcut: item\n        });\n      },\n      disabled: !isValidValue(newValue)\n    });\n  });\n  return /*#__PURE__*/_jsx(PickersShortcutsRoot, _extends({\n    dense: true,\n    sx: [{\n      maxHeight: VIEW_HEIGHT,\n      maxWidth: 200,\n      overflow: 'auto'\n    }, ...(Array.isArray(other.sx) ? other.sx : [other.sx])]\n  }, other, {\n    children: resolvedItems.map(item => {\n      return /*#__PURE__*/_jsx(ListItem, {\n        children: /*#__PURE__*/_jsx(Chip, _extends({}, item))\n      }, item.id ?? item.label);\n    })\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? PickersShortcuts.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Importance of the change when picking a shortcut:\n   * - \"accept\": fires `onChange`, fires `onAccept` and closes the Picker.\n   * - \"set\": fires `onChange` but do not fire `onAccept` and does not close the Picker.\n   * @default \"accept\"\n   */\n  changeImportance: PropTypes.oneOf(['accept', 'set']),\n  className: PropTypes.string,\n  component: PropTypes.elementType,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used for\n   * the list and list items.\n   * The prop is available to descendant components as the `dense` context.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * If `true`, vertical padding is removed from the list.\n   * @default false\n   */\n  disablePadding: PropTypes.bool,\n  /**\n   * Ordered array of shortcuts to display.\n   * If empty, does not display the shortcuts.\n   * @default []\n   */\n  items: PropTypes.arrayOf(PropTypes.shape({\n    getValue: PropTypes.func.isRequired,\n    id: PropTypes.string,\n    label: PropTypes.string.isRequired\n  })),\n  style: PropTypes.object,\n  /**\n   * The content of the subheader, normally `ListSubheader`.\n   */\n  subheader: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport { PickersShortcuts };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,kBAAkB,CAAC;EAC7CC,UAAU,GAAG,CAAC,UAAU,CAAC;AAC3B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,WAAW,QAAQ,sCAAsC;AAClE,SAASC,eAAe,EAAEC,uBAAuB,QAAQ,mBAAmB;AAC5E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,oBAAoB,GAAGV,MAAM,CAACE,IAAI,EAAE;EACxCS,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,MAAM;MACFC,KAAK;MACLC,gBAAgB,GAAG;IACrB,CAAC,GAAGF,KAAK;IACTG,KAAK,GAAGrB,6BAA6B,CAACkB,KAAK,EAAEjB,SAAS,CAAC;EACzD,MAAM;IACJqB;EACF,CAAC,GAAGX,uBAAuB,CAAC,CAAC;EAC7B,MAAMY,YAAY,GAAGb,eAAe,CAAC,CAAC;EACtC,IAAIS,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACK,MAAM,KAAK,CAAC,EAAE;IACvC,OAAO,IAAI;EACb;EACA,MAAMC,aAAa,GAAGN,KAAK,CAACO,GAAG,CAACC,IAAI,IAAI;IACtC,IAAI;QACAC;MACF,CAAC,GAAGD,IAAI;MACRE,IAAI,GAAG7B,6BAA6B,CAAC2B,IAAI,EAAEzB,UAAU,CAAC;IACxD,MAAM4B,QAAQ,GAAGF,QAAQ,CAAC;MACxBG,OAAO,EAAER;IACX,CAAC,CAAC;IACF,OAAOxB,QAAQ,CAAC,CAAC,CAAC,EAAE8B,IAAI,EAAE;MACxBG,KAAK,EAAEH,IAAI,CAACG,KAAK;MACjBC,OAAO,EAAEA,CAAA,KAAM;QACbX,QAAQ,CAACQ,QAAQ,EAAE;UACjBV,gBAAgB;UAChBc,QAAQ,EAAEL;QACZ,CAAC,CAAC;MACJ,CAAC;MACDM,QAAQ,EAAE,CAACZ,YAAY,CAACO,QAAQ;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,aAAajB,IAAI,CAACC,oBAAoB,EAAEf,QAAQ,CAAC;IACtDqC,KAAK,EAAE,IAAI;IACXC,EAAE,EAAE,CAAC;MACHC,SAAS,EAAE7B,WAAW;MACtB8B,QAAQ,EAAE,GAAG;MACbC,QAAQ,EAAE;IACZ,CAAC,EAAE,IAAIC,KAAK,CAACC,OAAO,CAACrB,KAAK,CAACgB,EAAE,CAAC,GAAGhB,KAAK,CAACgB,EAAE,GAAG,CAAChB,KAAK,CAACgB,EAAE,CAAC,CAAC;EACzD,CAAC,EAAEhB,KAAK,EAAE;IACRsB,QAAQ,EAAElB,aAAa,CAACC,GAAG,CAACG,IAAI,IAAI;MAClC,OAAO,aAAahB,IAAI,CAACN,QAAQ,EAAE;QACjCoC,QAAQ,EAAE,aAAa9B,IAAI,CAACL,IAAI,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAE8B,IAAI,CAAC;MACtD,CAAC,EAAEA,IAAI,CAACe,EAAE,IAAIf,IAAI,CAACG,KAAK,CAAC;IAC3B,CAAC;EACH,CAAC,CAAC,CAAC;AACL;AACAa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9B,gBAAgB,CAAC+B,SAAS,GAAG;EACnE;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;EACE5B,gBAAgB,EAAEf,SAAS,CAAC4C,KAAK,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;EACpDC,SAAS,EAAE7C,SAAS,CAAC8C,MAAM;EAC3BC,SAAS,EAAE/C,SAAS,CAACgD,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;EACEjB,KAAK,EAAE/B,SAAS,CAACiD,IAAI;EACrB;AACF;AACA;AACA;EACEC,cAAc,EAAElD,SAAS,CAACiD,IAAI;EAC9B;AACF;AACA;AACA;AACA;EACEnC,KAAK,EAAEd,SAAS,CAACmD,OAAO,CAACnD,SAAS,CAACoD,KAAK,CAAC;IACvC7B,QAAQ,EAAEvB,SAAS,CAACqD,IAAI,CAACC,UAAU;IACnCf,EAAE,EAAEvC,SAAS,CAAC8C,MAAM;IACpBnB,KAAK,EAAE3B,SAAS,CAAC8C,MAAM,CAACQ;EAC1B,CAAC,CAAC,CAAC;EACHC,KAAK,EAAEvD,SAAS,CAACwD,MAAM;EACvB;AACF;AACA;EACEC,SAAS,EAAEzD,SAAS,CAAC0D,IAAI;EACzB;AACF;AACA;EACE1B,EAAE,EAAEhC,SAAS,CAAC2D,SAAS,CAAC,CAAC3D,SAAS,CAACmD,OAAO,CAACnD,SAAS,CAAC2D,SAAS,CAAC,CAAC3D,SAAS,CAACqD,IAAI,EAAErD,SAAS,CAACwD,MAAM,EAAExD,SAAS,CAACiD,IAAI,CAAC,CAAC,CAAC,EAAEjD,SAAS,CAACqD,IAAI,EAAErD,SAAS,CAACwD,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,SAAS5C,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}