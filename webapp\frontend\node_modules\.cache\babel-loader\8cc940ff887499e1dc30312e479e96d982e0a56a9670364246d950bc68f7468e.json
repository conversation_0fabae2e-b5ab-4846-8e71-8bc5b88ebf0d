{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\nconst eraValues = {\n  narrow: [\"前\", \"公元\"],\n  abbreviated: [\"前\", \"公元\"],\n  wide: [\"公元前\", \"公元\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"第一刻\", \"第二刻\", \"第三刻\", \"第四刻\"],\n  wide: [\"第一刻鐘\", \"第二刻鐘\", \"第三刻鐘\", \"第四刻鐘\"]\n};\nconst monthValues = {\n  narrow: [\"一\", \"二\", \"三\", \"四\", \"五\", \"六\", \"七\", \"八\", \"九\", \"十\", \"十一\", \"十二\"],\n  abbreviated: [\"1月\", \"2月\", \"3月\", \"4月\", \"5月\", \"6月\", \"7月\", \"8月\", \"9月\", \"10月\", \"11月\", \"12月\"],\n  wide: [\"一月\", \"二月\", \"三月\", \"四月\", \"五月\", \"六月\", \"七月\", \"八月\", \"九月\", \"十月\", \"十一月\", \"十二月\"]\n};\nconst dayValues = {\n  narrow: [\"日\", \"一\", \"二\", \"三\", \"四\", \"五\", \"六\"],\n  short: [\"日\", \"一\", \"二\", \"三\", \"四\", \"五\", \"六\"],\n  abbreviated: [\"週日\", \"週一\", \"週二\", \"週三\", \"週四\", \"週五\", \"週六\"],\n  wide: [\"星期日\", \"星期一\", \"星期二\", \"星期三\", \"星期四\", \"星期五\", \"星期六\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"上\",\n    pm: \"下\",\n    midnight: \"凌晨\",\n    noon: \"午\",\n    morning: \"早\",\n    afternoon: \"下午\",\n    evening: \"晚\",\n    night: \"夜\"\n  },\n  abbreviated: {\n    am: \"上午\",\n    pm: \"下午\",\n    midnight: \"凌晨\",\n    noon: \"中午\",\n    morning: \"早晨\",\n    afternoon: \"中午\",\n    evening: \"晚上\",\n    night: \"夜間\"\n  },\n  wide: {\n    am: \"上午\",\n    pm: \"下午\",\n    midnight: \"凌晨\",\n    noon: \"中午\",\n    morning: \"早晨\",\n    afternoon: \"中午\",\n    evening: \"晚上\",\n    night: \"夜間\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"上\",\n    pm: \"下\",\n    midnight: \"凌晨\",\n    noon: \"午\",\n    morning: \"早\",\n    afternoon: \"下午\",\n    evening: \"晚\",\n    night: \"夜\"\n  },\n  abbreviated: {\n    am: \"上午\",\n    pm: \"下午\",\n    midnight: \"凌晨\",\n    noon: \"中午\",\n    morning: \"早晨\",\n    afternoon: \"中午\",\n    evening: \"晚上\",\n    night: \"夜間\"\n  },\n  wide: {\n    am: \"上午\",\n    pm: \"下午\",\n    midnight: \"凌晨\",\n    noon: \"中午\",\n    morning: \"早晨\",\n    afternoon: \"中午\",\n    evening: \"晚上\",\n    night: \"夜間\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n  switch (options?.unit) {\n    case \"date\":\n      return number + \"日\";\n    case \"hour\":\n      return number + \"時\";\n    case \"minute\":\n      return number + \"分\";\n    case \"second\":\n      return number + \"秒\";\n    default:\n      return \"第 \" + number;\n  }\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "number", "Number", "unit", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/zh-TW/_lib/localize.mjs"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\nconst eraValues = {\n  narrow: [\"前\", \"公元\"],\n  abbreviated: [\"前\", \"公元\"],\n  wide: [\"公元前\", \"公元\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"第一刻\", \"第二刻\", \"第三刻\", \"第四刻\"],\n  wide: [\"第一刻鐘\", \"第二刻鐘\", \"第三刻鐘\", \"第四刻鐘\"],\n};\n\nconst monthValues = {\n  narrow: [\n    \"一\",\n    \"二\",\n    \"三\",\n    \"四\",\n    \"五\",\n    \"六\",\n    \"七\",\n    \"八\",\n    \"九\",\n    \"十\",\n    \"十一\",\n    \"十二\",\n  ],\n\n  abbreviated: [\n    \"1月\",\n    \"2月\",\n    \"3月\",\n    \"4月\",\n    \"5月\",\n    \"6月\",\n    \"7月\",\n    \"8月\",\n    \"9月\",\n    \"10月\",\n    \"11月\",\n    \"12月\",\n  ],\n\n  wide: [\n    \"一月\",\n    \"二月\",\n    \"三月\",\n    \"四月\",\n    \"五月\",\n    \"六月\",\n    \"七月\",\n    \"八月\",\n    \"九月\",\n    \"十月\",\n    \"十一月\",\n    \"十二月\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"日\", \"一\", \"二\", \"三\", \"四\", \"五\", \"六\"],\n  short: [\"日\", \"一\", \"二\", \"三\", \"四\", \"五\", \"六\"],\n  abbreviated: [\"週日\", \"週一\", \"週二\", \"週三\", \"週四\", \"週五\", \"週六\"],\n\n  wide: [\"星期日\", \"星期一\", \"星期二\", \"星期三\", \"星期四\", \"星期五\", \"星期六\"],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"上\",\n    pm: \"下\",\n    midnight: \"凌晨\",\n    noon: \"午\",\n    morning: \"早\",\n    afternoon: \"下午\",\n    evening: \"晚\",\n    night: \"夜\",\n  },\n  abbreviated: {\n    am: \"上午\",\n    pm: \"下午\",\n    midnight: \"凌晨\",\n    noon: \"中午\",\n    morning: \"早晨\",\n    afternoon: \"中午\",\n    evening: \"晚上\",\n    night: \"夜間\",\n  },\n  wide: {\n    am: \"上午\",\n    pm: \"下午\",\n    midnight: \"凌晨\",\n    noon: \"中午\",\n    morning: \"早晨\",\n    afternoon: \"中午\",\n    evening: \"晚上\",\n    night: \"夜間\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"上\",\n    pm: \"下\",\n    midnight: \"凌晨\",\n    noon: \"午\",\n    morning: \"早\",\n    afternoon: \"下午\",\n    evening: \"晚\",\n    night: \"夜\",\n  },\n  abbreviated: {\n    am: \"上午\",\n    pm: \"下午\",\n    midnight: \"凌晨\",\n    noon: \"中午\",\n    morning: \"早晨\",\n    afternoon: \"中午\",\n    evening: \"晚上\",\n    night: \"夜間\",\n  },\n  wide: {\n    am: \"上午\",\n    pm: \"下午\",\n    midnight: \"凌晨\",\n    noon: \"中午\",\n    morning: \"早晨\",\n    afternoon: \"中午\",\n    evening: \"晚上\",\n    night: \"夜間\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const number = Number(dirtyNumber);\n\n  switch (options?.unit) {\n    case \"date\":\n      return number + \"日\";\n    case \"hour\":\n      return number + \"時\";\n    case \"minute\":\n      return number + \"分\";\n    case \"second\":\n      return number + \"秒\";\n    default:\n      return \"第 \" + number;\n  }\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAEhE,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC;EACnBC,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC;EACxBC,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI;AACpB,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACzCC,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;AACvC,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CACN,GAAG,EACH,GAAG,EACH,<PERSON>G,EACH,<PERSON>G,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,IAAI,EACJ,IAAI,CACL;EAEDC,WAAW,EAAE,CACX,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK;AAET,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC1CL,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAEvDC,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AACxD,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,IAAI;IACbC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC9C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAElC,QAAQC,OAAO,EAAEG,IAAI;IACnB,KAAK,MAAM;MACT,OAAOF,MAAM,GAAG,GAAG;IACrB,KAAK,MAAM;MACT,OAAOA,MAAM,GAAG,GAAG;IACrB,KAAK,QAAQ;MACX,OAAOA,MAAM,GAAG,GAAG;IACrB,KAAK,QAAQ;MACX,OAAOA,MAAM,GAAG,GAAG;IACrB;MACE,OAAO,IAAI,GAAGA,MAAM;EACxB;AACF,CAAC;AAED,OAAO,MAAMG,QAAQ,GAAG;EACtBN,aAAa;EAEbO,GAAG,EAAE1B,eAAe,CAAC;IACnB2B,MAAM,EAAE1B,SAAS;IACjB2B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE7B,eAAe,CAAC;IACvB2B,MAAM,EAAEtB,aAAa;IACrBuB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE/B,eAAe,CAAC;IACrB2B,MAAM,EAAErB,WAAW;IACnBsB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAEhC,eAAe,CAAC;IACnB2B,MAAM,EAAEpB,SAAS;IACjBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAEjC,eAAe,CAAC;IACzB2B,MAAM,EAAElB,eAAe;IACvBmB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEhB,yBAAyB;IAC3CiB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}