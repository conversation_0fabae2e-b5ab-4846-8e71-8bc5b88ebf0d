{"ast": null, "code": "import React from'react';import{But<PERSON>,Tooltip}from'@mui/material';import{Home as HomeIcon}from'@mui/icons-material';import{useNavigate}from'react-router-dom';import{useAuth}from'../../context/AuthContext';/**\n * Componente che mostra un pulsante per tornare al pannello admin\n * Visibile solo quando un amministratore sta impersonando un utente\n */import{jsx as _jsx}from\"react/jsx-runtime\";const AdminHomeButton=()=>{const{isImpersonating}=useAuth();const navigate=useNavigate();// Se l'utente non è un amministratore che sta impersonando un utente, non mostrare il pulsante\nif(!isImpersonating){return null;}// Naviga al pannello amministratore\nconst handleBackToAdmin=()=>{navigate('/dashboard/admin');};return/*#__PURE__*/_jsx(Tooltip,{title:\"Torna al pannello amministratore\",children:/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"primary\",startIcon:/*#__PURE__*/_jsx(HomeIcon,{}),onClick:handleBackToAdmin,size:\"small\",children:\"Pannello Admin\"})});};export default AdminHomeButton;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Home", "HomeIcon", "useNavigate", "useAuth", "jsx", "_jsx", "AdminHomeButton", "isImpersonating", "navigate", "handleBackToAdmin", "title", "children", "variant", "color", "startIcon", "onClick", "size"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/common/AdminHomeButton.js"], "sourcesContent": ["import React from 'react';\nimport { Button, Tooltip } from '@mui/material';\nimport { Home as HomeIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\n\n/**\n * Componente che mostra un pulsante per tornare al pannello admin\n * Visibile solo quando un amministratore sta impersonando un utente\n */\nconst AdminHomeButton = () => {\n  const { isImpersonating } = useAuth();\n  const navigate = useNavigate();\n\n  // Se l'utente non è un amministratore che sta impersonando un utente, non mostrare il pulsante\n  if (!isImpersonating) {\n    return null;\n  }\n\n  // Naviga al pannello amministratore\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  return (\n    <Tooltip title=\"Torna al pannello amministratore\">\n      <Button\n        variant=\"outlined\"\n        color=\"primary\"\n        startIcon={<HomeIcon />}\n        onClick={handleBackToAdmin}\n        size=\"small\"\n      >\n        Pannello Admin\n      </Button>\n    </Tooltip>\n  );\n};\n\nexport default AdminHomeButton;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,OAAO,KAAQ,eAAe,CAC/C,OAASC,IAAI,GAAI,CAAAC,QAAQ,KAAQ,qBAAqB,CACtD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,OAAO,KAAQ,2BAA2B,CAEnD;AACA;AACA;AACA,GAHA,OAAAC,GAAA,IAAAC,IAAA,yBAIA,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAEC,eAAgB,CAAC,CAAGJ,OAAO,CAAC,CAAC,CACrC,KAAM,CAAAK,QAAQ,CAAGN,WAAW,CAAC,CAAC,CAE9B;AACA,GAAI,CAACK,eAAe,CAAE,CACpB,MAAO,KAAI,CACb,CAEA;AACA,KAAM,CAAAE,iBAAiB,CAAGA,CAAA,GAAM,CAC9BD,QAAQ,CAAC,kBAAkB,CAAC,CAC9B,CAAC,CAED,mBACEH,IAAA,CAACN,OAAO,EAACW,KAAK,CAAC,kCAAkC,CAAAC,QAAA,cAC/CN,IAAA,CAACP,MAAM,EACLc,OAAO,CAAC,UAAU,CAClBC,KAAK,CAAC,SAAS,CACfC,SAAS,cAAET,IAAA,CAACJ,QAAQ,GAAE,CAAE,CACxBc,OAAO,CAAEN,iBAAkB,CAC3BO,IAAI,CAAC,OAAO,CAAAL,QAAA,CACb,gBAED,CAAQ,CAAC,CACF,CAAC,CAEd,CAAC,CAED,cAAe,CAAAL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}