{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ParcoCavi.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, CardActions, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Divider, Alert, CircularProgress, FormHelperText, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon, Save as SaveIcon, ViewList as ViewListIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport ConfigurazioneDialog from './ConfigurazioneDialog';\nimport BobineFilterableTable from './BobineFilterableTable';\nimport QuickAddCablesDialog from './QuickAddCablesDialog';\nimport { validateBobinaData, validateBobinaField, validateBobinaId, isEmpty } from '../../utils/bobinaValidationUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ParcoCavi = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  initialOption = null\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(initialOption);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [showQuickAddDialog, setShowQuickAddDialog] = useState(false);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'Disponibile',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n  const [openConfigDialog, setOpenConfigDialog] = useState(false);\n  const [isFirstInsertion, setIsFirstInsertion] = useState(false);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      let errorMessage = 'Errore nel caricamento delle bobine';\n      if (error.detail) {\n        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);\n      } else if (error.message) {\n        errorMessage += ': ' + error.message;\n      }\n      onError(errorMessage);\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica lo storico utilizzo bobine\n  const loadStoricoUtilizzo = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getStoricoUtilizzo(cantiereId);\n      setStoricoUtilizzo(data);\n    } catch (error) {\n      let errorMessage = 'Errore nel caricamento dello storico utilizzo';\n      if (error.detail) {\n        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);\n      } else if (error.message) {\n        errorMessage += ': ' + error.message;\n      }\n      onError(errorMessage);\n      console.error('Errore nel caricamento dello storico utilizzo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente e gestisce l'opzione iniziale\n  // Utilizziamo una ref per tenere traccia se l'effetto è già stato eseguito\n  const initialLoadDone = React.useRef(false);\n  useEffect(() => {\n    // Esegui solo una volta all'avvio del componente\n    if (!initialLoadDone.current) {\n      console.log('Primo caricamento del componente, initialOption:', initialOption);\n      initialLoadDone.current = true;\n      if (initialOption === 'creaBobina') {\n        console.log('Avvio processo creazione bobina');\n        // IMPORTANTE: Per il primo caricamento con creaBobina, forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE ALL\\'AVVIO');\n        setIsFirstInsertion(true);\n        setOpenConfigDialog(true);\n      } else if (initialOption) {\n        console.log('Eseguendo handleOptionSelect con:', initialOption);\n        handleOptionSelect(initialOption);\n      } else {\n        console.log('Caricando bobine');\n        loadBobine();\n      }\n    }\n  }, []); // Dipendenze vuote per eseguire solo al mount\n\n  // Verifica se è il primo inserimento di una bobina per un cantiere\n  const checkIfFirstInsertion = async () => {\n    // Variabile per memorizzare la configurazione\n    let configurazione = 's'; // Valore predefinito\n\n    try {\n      // Previene chiamate multiple\n      if (loading) {\n        console.log('Operazione già in corso, uscita');\n        return;\n      }\n\n      // Assicuriamoci che nessun dialog sia aperto\n      setOpenDialog(false);\n      setOpenConfigDialog(false); // Chiudi anche il dialog di configurazione se aperto\n\n      setLoading(true);\n      console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n\n      // Gestione caso in cui cantiereId non sia valido\n      if (!cantiereId || isNaN(parseInt(cantiereId))) {\n        onError('ID cantiere non valido');\n        console.error('ID cantiere non valido:', cantiereId);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API reale\n      let isFirst = false;\n      try {\n        console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n        const response = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\n        isFirst = response.is_first_insertion;\n\n        // Controlla se c'è una configurazione salvata in localStorage\n        const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`);\n\n        // Usa la configurazione salvata in localStorage se disponibile, altrimenti usa quella dal server\n        configurazione = savedConfig || response.configurazione || 's';\n        console.log('Configurazione da localStorage:', savedConfig);\n        console.log('Configurazione dal server:', response.configurazione);\n        console.log('Configurazione finale utilizzata:', configurazione);\n        setIsFirstInsertion(isFirst);\n        console.log('È il primo inserimento di una bobina?', isFirst);\n        console.log('Configurazione esistente:', configurazione);\n      } catch (error) {\n        console.error('Errore durante la verifica del primo inserimento:', error);\n        // In caso di errore, assumiamo che non sia il primo inserimento\n        setIsFirstInsertion(false);\n        onError('Errore durante la verifica del primo inserimento. Procedendo con inserimento standard.');\n      }\n      if (isFirst) {\n        // Se è il primo inserimento, mostra il dialog di configurazione\n        console.log('Mostrando il dialog di configurazione');\n        // Assicuriamoci che il dialog di creazione sia chiuso\n        setOpenDialog(false);\n\n        // IMPORTANTE: Forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE');\n        setOpenConfigDialog(true);\n      } else {\n        // Non è il primo inserimento, procedi con il form normale\n        console.log('Non è il primo inserimento, mostrando il form normale');\n\n        // Ottieni il prossimo numero di bobina solo se la configurazione è automatica\n        let nextBobinaNumber = '1';\n        if (configurazione === 's') {\n          try {\n            // Ottieni l'ultimo numero di bobina dal backend\n            const bobine = await parcoCaviService.getBobine(cantiereId);\n            if (bobine && bobine.length > 0) {\n              // Filtra solo le bobine con numero_bobina numerico\n              const numericBobine = bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina));\n\n              // Log per debug\n              console.log('Bobine totali:', bobine.length);\n              console.log('Bobine con numero numerico:', numericBobine.length);\n              console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));\n              if (numericBobine.length > 0) {\n                // Trova il numero massimo tra le bobine esistenti\n                const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));\n                console.log('Numero massimo trovato:', maxNumber);\n                nextBobinaNumber = String(maxNumber + 1);\n              }\n            }\n            console.log('Prossimo numero bobina:', nextBobinaNumber);\n          } catch (error) {\n            console.error('Errore nel recupero del prossimo numero bobina:', error);\n            // In caso di errore, usa 1 come default\n            nextBobinaNumber = '1';\n          }\n        }\n        setDialogType('creaBobina');\n        setFormData({\n          // In modalità automatica, imposta il numero progressivo\n          // In modalità manuale, lascia vuoto per far inserire all'utente\n          numero_bobina: configurazione === 's' ? nextBobinaNumber : '',\n          utility: '',\n          tipologia: '',\n          n_conduttori: '',\n          sezione: '',\n          metri_totali: '',\n          metri_residui: '',\n          stato_bobina: 'Disponibile',\n          ubicazione_bobina: '',\n          fornitore: '',\n          n_DDT: '',\n          data_DDT: '',\n          configurazione: configurazione // Usa la configurazione esistente\n        });\n        setOpenDialog(true);\n      }\n    } catch (error) {\n      // Gestione dettagliata dell'errore\n      let errorMessage = 'Errore nel controllo dell\\'inserimento della prima bobina';\n      if (error.response) {\n        var _error$response$data;\n        // Errore di risposta dal server\n        errorMessage += `: ${error.response.status} - ${((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Errore server'}`;\n        console.error('Dettagli errore API:', error.response);\n      } else if (error.request) {\n        // Errore di rete (nessuna risposta ricevuta)\n        errorMessage += ': Errore di connessione al server';\n      } else {\n        // Errore generico\n        errorMessage += `: ${error.message || 'Errore sconosciuto'}`;\n      }\n      onError(errorMessage);\n      console.error('Errore completo:', error);\n\n      // In caso di errore, mantieni la configurazione esistente o usa il default\n      // Non forzare il reset a 's' per evitare di perdere la configurazione manuale\n      if (!configurazione) {\n        configurazione = 's'; // Fallback al valore di default solo se non è già impostato\n      }\n      setDialogType('creaBobina');\n      setFormData({\n        numero_bobina: configurazione === 's' ? '1' : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configurazione // Usa la configurazione esistente o il default\n      });\n      setOpenDialog(true);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la conferma della configurazione\n  const handleConfigConfirm = async configValue => {\n    console.log('Configurazione selezionata:', configValue);\n    // Salva la configurazione selezionata in localStorage per persistenza\n    localStorage.setItem(`cantiere_${cantiereId}_config`, configValue);\n    setLoading(true);\n    try {\n      // Ottieni il prossimo numero di bobina se la configurazione è automatica\n      let nextBobinaNumber = '1';\n      if (configValue === 's') {\n        try {\n          // Ottieni l'ultimo numero di bobina dal backend\n          const bobine = await parcoCaviService.getBobine(cantiereId);\n          if (bobine && bobine.length > 0) {\n            // Filtra solo le bobine con numero_bobina numerico\n            const numericBobine = bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina));\n\n            // Log per debug\n            console.log('Bobine totali:', bobine.length);\n            console.log('Bobine con numero numerico:', numericBobine.length);\n            console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));\n            if (numericBobine.length > 0) {\n              // Trova il numero massimo tra le bobine esistenti\n              const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));\n              console.log('Numero massimo trovato:', maxNumber);\n              nextBobinaNumber = String(maxNumber + 1);\n            }\n          }\n          console.log('Prossimo numero bobina:', nextBobinaNumber);\n        } catch (error) {\n          console.error('Errore nel recupero del prossimo numero bobina:', error);\n          // In caso di errore, usa 1 come default\n          nextBobinaNumber = '1';\n        }\n      }\n\n      // Imposta i valori di default per la bobina\n      const defaultFormData = {\n        // In modalità automatica, imposta il numero progressivo\n        // In modalità manuale, lascia vuoto per far inserire all'utente\n        numero_bobina: configValue === 's' ? nextBobinaNumber : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configValue // Imposta la configurazione scelta\n      };\n      console.log('Impostando i dati del form con configurazione:', configValue, 'numero_bobina:', defaultFormData.numero_bobina);\n\n      // Importante: prima prepara il form, poi chiudi il dialog di configurazione\n      // e solo dopo apri il dialog di creazione\n      setFormData(defaultFormData);\n      setDialogType('creaBobina');\n\n      // Chiudi il dialog di configurazione\n      setOpenConfigDialog(false);\n\n      // Piccolo timeout per assicurarsi che il dialog di configurazione sia chiuso\n      // prima di aprire il dialog di creazione\n      setTimeout(() => {\n        setOpenDialog(true);\n        console.log('Dialog di creazione bobina aperto con numero_bobina:', defaultFormData.numero_bobina);\n      }, 300);\n    } catch (error) {\n      console.error('Errore durante la preparazione del form:', error);\n      onError('Errore durante la preparazione del form: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      checkIfFirstInsertion();\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n\n    // Recupera la configurazione salvata per questo cantiere\n    const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`) || 's';\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: savedConfig // Mantieni la configurazione salvata\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = bobina => {\n    console.log('Bobina selezionata:', bobina);\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n\n      // Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe\n      setFormData({\n        numero_bobina: bobina.numero_bobina || '',\n        utility: String(bobina.utility || ''),\n        tipologia: String(bobina.tipologia || ''),\n        n_conduttori: bobina.n_conduttori !== null && bobina.n_conduttori !== undefined ? String(bobina.n_conduttori) : '',\n        sezione: bobina.sezione !== null && bobina.sezione !== undefined ? String(bobina.sezione) : '',\n        metri_totali: bobina.metri_totali !== null && bobina.metri_totali !== undefined ? Number(bobina.metri_totali) : '',\n        metri_residui: bobina.metri_residui !== null && bobina.metri_residui !== undefined ? Number(bobina.metri_residui) : '',\n        stato_bobina: String(bobina.stato_bobina || 'Disponibile'),\n        ubicazione_bobina: String(bobina.ubicazione_bobina || ''),\n        fornitore: String(bobina.fornitore || ''),\n        n_DDT: String(bobina.n_DDT || ''),\n        data_DDT: bobina.data_DDT || '',\n        configurazione: String(bobina.configurazione || 's')\n      });\n      console.log('Form data impostati per la modifica:', {\n        numero_bobina: bobina.numero_bobina,\n        utility: bobina.utility,\n        tipologia: bobina.tipologia,\n        n_conduttori: bobina.n_conduttori,\n        sezione: bobina.sezione,\n        metri_totali: bobina.metri_totali,\n        metri_residui: bobina.metri_residui\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      // Validazione speciale per numero_bobina quando configurazione è 'n'\n      if (name === 'numero_bobina' && formData.configurazione === 'n') {\n        const idResult = validateBobinaId(value);\n        if (!idResult.valid) {\n          setFormErrors(prev => ({\n            ...prev,\n            [name]: idResult.message\n          }));\n        } else {\n          setFormErrors(prev => {\n            const newErrors = {\n              ...prev\n            };\n            delete newErrors[name];\n            return newErrors;\n          });\n        }\n        return;\n      }\n      const result = validateBobinaField(name, value);\n\n      // Aggiorna gli errori\n      if (!result.valid) {\n        setFormErrors(prev => ({\n          ...prev,\n          [name]: result.message\n        }));\n      } else {\n        setFormErrors(prev => {\n          const newErrors = {\n            ...prev\n          };\n          delete newErrors[name];\n          return newErrors;\n        });\n\n        // Aggiorna gli avvisi\n        if (result.warning) {\n          setFormWarnings(prev => ({\n            ...prev,\n            [name]: result.message\n          }));\n        } else {\n          setFormWarnings(prev => {\n            const newWarnings = {\n              ...prev\n            };\n            delete newWarnings[name];\n            return newWarnings;\n          });\n        }\n      }\n    }\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      // Validazione completa dei dati prima del salvataggio\n      if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n        const validation = validateBobinaData(formData);\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          onError('Correggi gli errori nel form prima di salvare');\n          return;\n        }\n\n        // Verifica aggiuntiva per il campo numero_bobina quando la configurazione è manuale\n        if (formData.configurazione === 'n' && (!formData.numero_bobina || formData.numero_bobina.trim() === '')) {\n          setFormErrors({\n            ...formErrors,\n            numero_bobina: 'L\\'ID della bobina è obbligatorio'\n          });\n          onError('L\\'ID della bobina è obbligatorio');\n          return;\n        }\n      }\n      setLoading(true);\n      console.log('Salvataggio dati bobina in corso...');\n      if (dialogType === 'creaBobina') {\n        // Prepara i dati per la creazione della bobina\n        const bobinaData = {\n          ...formData,\n          // Assicurati che tutti i campi siano nel formato corretto\n          numero_bobina: String(formData.numero_bobina || ''),\n          utility: String(formData.utility || ''),\n          tipologia: String(formData.tipologia || ''),\n          n_conduttori: String(formData.n_conduttori || ''),\n          sezione: String(formData.sezione || ''),\n          metri_totali: parseFloat(formData.metri_totali) || 0,\n          ubicazione_bobina: String(formData.ubicazione_bobina || 'TBD'),\n          fornitore: String(formData.fornitore || 'TBD'),\n          n_DDT: String(formData.n_DDT || 'TBD'),\n          data_DDT: formData.data_DDT || null,\n          // Assicurati che la configurazione sia impostata correttamente\n          configurazione: String(formData.configurazione || 's')\n        };\n        console.log('Dati bobina da inviare:', bobinaData);\n        try {\n          // Log dei dati che stiamo per inviare\n          console.log('Invio dati bobina al backend:', JSON.stringify(bobinaData, null, 2));\n\n          // Invia i dati al backend\n          await parcoCaviService.createBobina(cantiereId, bobinaData);\n          onSuccess('Bobina creata con successo');\n\n          // Chiudi il dialog\n          handleCloseDialog();\n\n          // Imposta l'opzione selezionata a visualizzaBobine e carica le bobine\n          setSelectedOption('visualizzaBobine');\n          loadBobine();\n\n          // Notifica al componente padre che dovrebbe reindirizzare alla pagina di visualizzazione bobine\n          if (typeof window !== 'undefined') {\n            // Usa un evento personalizzato per comunicare con il componente padre\n            const event = new CustomEvent('redirectToVisualizzaBobine', {\n              detail: {\n                cantiereId\n              }\n            });\n            window.dispatchEvent(event);\n          }\n        } catch (error) {\n          console.error('Errore durante la creazione della bobina:', error);\n\n          // Gestione dettagliata dell'errore\n          let errorMessage = 'Errore durante la creazione della bobina';\n          if (error.detail) {\n            errorMessage = typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n\n          // Log dettagliato dell'errore\n          console.error('Dettagli errore:', errorMessage);\n          console.error('Dati inviati:', JSON.stringify(bobinaData, null, 2));\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'modificaBobina') {\n        // Per la modifica, usa l'ID bobina completo o il numero bobina\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina || formData.numero_bobina;\n        console.log('Modifica bobina con ID:', bobinaId);\n        console.log('Dati da inviare:', formData);\n        try {\n          const response = await parcoCaviService.updateBobina(cantiereId, bobinaId, formData);\n          console.log('Risposta modifica bobina:', response);\n          onSuccess('Bobina modificata con successo');\n        } catch (error) {\n          console.error('Errore durante la modifica della bobina:', error);\n          let errorMessage = 'Errore durante la modifica della bobina';\n          if (error.detail) {\n            errorMessage = typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'eliminaBobina') {\n        // Per l'eliminazione, usa l'ID bobina completo\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina;\n        const response = await parcoCaviService.deleteBobina(cantiereId, bobinaId);\n\n        // Verifica se è stata eliminata l'ultima bobina\n        if (response.is_last_bobina) {\n          console.log(`Eliminata l'ultima bobina del cantiere ${cantiereId}. Il parco cavi è ora vuoto.`);\n          onSuccess('Bobina eliminata con successo. Il parco cavi è ora vuoto.');\n\n          // Forza il refresh della pagina per reinnescare il sistema del primo inserimento\n          setTimeout(() => {\n            // Imposta l'opzione selezionata a creaBobina per forzare il dialog di configurazione\n            setSelectedOption('creaBobina');\n            // Forza l'apertura del dialog di configurazione\n            setIsFirstInsertion(true);\n            setOpenConfigDialog(true);\n          }, 1000);\n        } else {\n          onSuccess('Bobina eliminata con successo');\n        }\n      }\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      let errorMessage = 'Errore durante l\\'operazione';\n      if (error.detail) {\n        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);\n      } else if (error.message) {\n        errorMessage += ': ' + error.message;\n      } else {\n        errorMessage += ': Errore sconosciuto';\n      }\n      onError(errorMessage);\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // La visualizzazione delle bobine è ora gestita dal componente BobineFilterableTable\n  const renderBobineCards = () => {\n    return /*#__PURE__*/_jsxDEV(BobineFilterableTable, {\n      bobine: bobine,\n      loading: loading,\n      onFilteredDataChange: filteredData => console.log('Bobine filtrate:', filteredData.length),\n      onEdit: bobina => {\n        setSelectedBobina(bobina);\n        setDialogType('modificaBobina');\n        setOpenDialog(true);\n      },\n      onDelete: bobina => {\n        setSelectedBobina(bobina);\n        setDialogType('eliminaBobina');\n        setOpenDialog(true);\n      },\n      onViewHistory: bobina => {\n        setSelectedBobina(bobina);\n        loadStoricoUtilizzo();\n        setDialogType('visualizzaStorico');\n        setOpenDialog(true);\n      },\n      onQuickAdd: bobina => {\n        setSelectedBobina(bobina);\n        setShowQuickAddDialog(true);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 698,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 731,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [dialogType === 'modificaBobina' && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mb: 2,\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              fontWeight: \"bold\",\n              children: \"Condizioni per la modifica:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"La bobina deve essere nello stato \\\"Disponibile\\\"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"La bobina non deve essere associata a nessun cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato attuale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 21\n                }, this), \" \", (selectedBobina === null || selectedBobina === void 0 ? void 0 : selectedBobina.stato_bobina) || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 15\n          }, this), Object.keys(formWarnings).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2,\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              children: \"Attenzione:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                margin: 0,\n                paddingLeft: '20px'\n              },\n              children: Object.values(formWarnings).map((warning, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: warning\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"numero_bobina\",\n                label: \"ID Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.numero_bobina,\n                onChange: handleFormChange,\n                disabled: dialogType === 'modificaBobina' || dialogType === 'creaBobina' && formData.configurazione === 's',\n                required: true,\n                error: !!formErrors.numero_bobina,\n                InputProps: {\n                  sx: {\n                    bgcolor: formData.configurazione === 's' ? '#f5f5f5' : 'transparent',\n                    fontWeight: 'bold'\n                  }\n                },\n                helperText: formErrors.numero_bobina || (dialogType === 'creaBobina' && formData.configurazione === 's' ? `ID completo: C${cantiereId}_B${formData.numero_bobina || ''} (generato automaticamente)` : dialogType === 'creaBobina' && formData.configurazione === 'n' ? `Inserisci l'ID della bobina (es. A123). ID completo sarà: C${cantiereId}_B{tuo input}` : ''),\n                type: formData.configurazione === 's' ? \"text\" : \"text\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utility\",\n                label: \"Utility\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utility,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.utility,\n                helperText: formErrors.utility || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 794,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 793,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"tipologia\",\n                label: \"Tipologia\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.tipologia,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.tipologia,\n                helperText: formErrors.tipologia || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 807,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 806,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_conduttori\",\n                label: \"N\\xB0 Conduttori\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_conduttori,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.n_conduttori,\n                helperText: formErrors.n_conduttori || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 820,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sezione\",\n                label: \"Sezione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sezione,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.sezione,\n                helperText: formErrors.sezione || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 833,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 832,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_totali\",\n                label: \"Metri Totali\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_totali,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.metri_totali,\n                helperText: formErrors.metri_totali || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 15\n            }, this), dialogType === 'modificaBobina' && /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_residui\",\n                label: \"Metri Residui\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_residui,\n                onChange: handleFormChange,\n                required: true,\n                disabled: true,\n                helperText: \"I metri residui non possono essere modificati direttamente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 860,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"stato-bobina-label\",\n                  children: \"Stato Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 877,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: \"stato-bobina-label\",\n                  name: \"stato_bobina\",\n                  value: formData.stato_bobina,\n                  label: \"Stato Bobina\",\n                  onChange: handleFormChange,\n                  disabled: dialogType === 'creaBobina',\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Disponibile\",\n                    children: \"Disponibile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 886,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"In uso\",\n                    children: \"In uso\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 887,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Terminata\",\n                    children: \"Terminata\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 888,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Danneggiata\",\n                    children: \"Danneggiata\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 889,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Over\",\n                    children: \"Over\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 878,\n                  columnNumber: 19\n                }, this), dialogType === 'creaBobina' && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: \"Per una nuova bobina, lo stato \\xE8 sempre \\\"Disponibile\\\"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 876,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 875,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_bobina\",\n                label: \"Ubicazione Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_bobina,\n                onChange: handleFormChange,\n                error: !!formErrors.ubicazione_bobina,\n                helperText: formErrors.ubicazione_bobina || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 898,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 897,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"fornitore\",\n                label: \"Fornitore\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.fornitore,\n                onChange: handleFormChange,\n                error: !!formErrors.fornitore,\n                helperText: formErrors.fornitore || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 910,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 909,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_DDT\",\n                label: \"Numero DDT\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_DDT,\n                onChange: handleFormChange,\n                error: !!formErrors.n_DDT,\n                helperText: formErrors.n_DDT || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 922,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"data_DDT\",\n                label: \"Data DDT (YYYY-MM-DD)\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.data_DDT,\n                onChange: handleFormChange,\n                placeholder: \"YYYY-MM-DD\",\n                error: !!formErrors.data_DDT,\n                helperText: formErrors.data_DDT || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 934,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"configurazione\",\n                label: \"Modalit\\xE0 Numerazione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.configurazione === 's' ? 'Automatica' : 'Manuale',\n                InputProps: {\n                  readOnly: true,\n                  sx: {\n                    bgcolor: '#f5f5f5'\n                  }\n                },\n                helperText: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    fontWeight: 'medium',\n                    color: formData.configurazione === 's' ? 'success.main' : 'info.main'\n                  },\n                  children: formData.configurazione === 's' ? 'Numerazione progressiva automatica (1, 2, 3, ...)' : 'Inserimento manuale dell\\'ID bobina (es. A123, SPEC01, ...)'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 960,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 946,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 764,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 971,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || Object.keys(formErrors).length > 0,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 975,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 975,\n              columnNumber: 69\n            }, this),\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 972,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 970,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 730,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Seleziona Bobina da Modificare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 987,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 990,\n            columnNumber: 15\n          }, this) : bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna bobina disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 992,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleBobinaSelect(bobina),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Bobina: ${bobina.numero_bobina}`,\n                secondary: `Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1001,\n                columnNumber: 21\n              }, this)\n            }, bobina.numero_bobina, false, {\n              fileName: _jsxFileName,\n              lineNumber: 996,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 994,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1011,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1010,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 986,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'eliminaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Elimina Bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1018,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: !selectedBobina ? loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1022,\n            columnNumber: 17\n          }, this) : bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna bobina disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1024,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => setSelectedBobina(bobina),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Bobina: ${bobina.numero_bobina}`,\n                secondary: `Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1033,\n                columnNumber: 23\n              }, this)\n            }, bobina.numero_bobina, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1026,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mb: 2\n              },\n              children: [\"Sei sicuro di voler eliminare la bobina \", selectedBobina.numero_bobina, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1043,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 2\n              },\n              children: \"Questa operazione non pu\\xF2 essere annullata.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1046,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                fontWeight: \"bold\",\n                children: \"Condizioni per l'eliminazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1050,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"La bobina deve essere completamente integra (metri residui = metri totali)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1054,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"La bobina deve essere nello stato \\\"Disponibile\\\"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1055,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"La bobina non deve essere associata a nessun cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1056,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1053,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1049,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: 1,\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato attuale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1061,\n                  columnNumber: 21\n                }, this), \" \", selectedBobina.stato_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1060,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri totali:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1064,\n                  columnNumber: 21\n                }, this), \" \", selectedBobina.metri_totali || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1063,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri residui:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1067,\n                  columnNumber: 21\n                }, this), \" \", selectedBobina.metri_residui || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1066,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1059,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1042,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1019,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1074,\n            columnNumber: 13\n          }, this), selectedBobina && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1080,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1080,\n              columnNumber: 71\n            }, this),\n            children: \"Elimina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1076,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1073,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1017,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'visualizzaStorico') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"lg\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Storico Utilizzo Bobine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1091,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1094,\n            columnNumber: 15\n          }, this) : storicoUtilizzo.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun dato storico disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1096,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Bobina\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1102,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Utility\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1103,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Tipologia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1104,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"N\\xB0 Conduttori\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1105,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Sezione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1106,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1107,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Residui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1108,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Cavi Associati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1109,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1101,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1100,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: storicoUtilizzo.map((record, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.numero_bobina\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1115,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.utility\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1116,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1117,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.n_conduttori\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1118,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.sezione\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1119,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.metri_totali\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1120,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.metri_residui\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1121,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.cavi.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1122,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1114,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1112,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1099,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1098,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1092,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Chiudi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1131,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1090,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [selectedOption === 'visualizzaBobine' && !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"inherit\",\n            startIcon: /*#__PURE__*/_jsxDEV(HistoryIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1149,\n              columnNumber: 28\n            }, this),\n            onClick: () => {\n              loadStoricoUtilizzo();\n              setDialogType('visualizzaStorico');\n              setOpenDialog(true);\n            },\n            sx: {\n              fontWeight: 'medium',\n              borderRadius: 0\n            },\n            children: \"Storico Utilizzo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"inherit\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1165,\n              columnNumber: 28\n            }, this),\n            onClick: () => checkIfFirstInsertion(),\n            sx: {\n              fontWeight: 'medium',\n              borderRadius: 0\n            },\n            children: \"Crea Nuova Bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1162,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1145,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1144,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1178,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1177,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(BobineFilterableTable, {\n        bobine: bobine,\n        loading: loading,\n        onFilteredDataChange: filteredData => console.log('Bobine filtrate:', filteredData.length),\n        onEdit: bobina => {\n          setSelectedBobina(bobina);\n          setDialogType('modificaBobina');\n          setOpenDialog(true);\n        },\n        onDelete: bobina => {\n          setSelectedBobina(bobina);\n          setDialogType('eliminaBobina');\n          setOpenDialog(true);\n        },\n        onViewHistory: bobina => {\n          setSelectedBobina(bobina);\n          loadStoricoUtilizzo();\n          setDialogType('visualizzaStorico');\n          setOpenDialog(true);\n        },\n        onQuickAdd: bobina => {\n          setSelectedBobina(bobina);\n          setShowQuickAddDialog(true);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1181,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1143,\n      columnNumber: 9\n    }, this) : !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        minHeight: '300px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: !selectedOption ? /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: \"Seleziona un'opzione dal menu principale per iniziare.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1211,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [selectedOption === 'creaBobina' && 'Crea Nuova Bobina', selectedOption === 'modificaBobina' && 'Modifica Bobina', selectedOption === 'eliminaBobina' && 'Elimina Bobina', selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1216,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n          sx: {\n            mt: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1222,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1215,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1209,\n      columnNumber: 9\n    }, this) : null, renderDialog(), /*#__PURE__*/_jsxDEV(ConfigurazioneDialog, {\n      open: openConfigDialog,\n      onClose: () => setOpenConfigDialog(false),\n      onConfirm: handleConfigConfirm\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1231,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(QuickAddCablesDialog, {\n      open: showQuickAddDialog,\n      onClose: () => {\n        setShowQuickAddDialog(false);\n        // Ricarica le bobine per riflettere i cambiamenti\n        loadBobine();\n      },\n      bobina: selectedBobina,\n      cantiereId: cantiereId,\n      onSuccess: onSuccess,\n      onError: onError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1238,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1141,\n    columnNumber: 5\n  }, this);\n};\n_s(ParcoCavi, \"ga5YNeegUrJJJABFc1/prRej0Vg=\");\n_c = ParcoCavi;\nexport default ParcoCavi;\nvar _c;\n$RefreshReg$(_c, \"ParcoCavi\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "Save", "SaveIcon", "ViewList", "ViewListIcon", "Warning", "WarningIcon", "parcoCaviService", "ConfigurazioneDialog", "BobineFilterableTable", "QuickAddCablesDialog", "validateBobinaData", "validateBob<PERSON>F<PERSON>", "validateBobinaId", "isEmpty", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cantiereId", "onSuccess", "onError", "initialOption", "_s", "loading", "setLoading", "bobine", "set<PERSON>ob<PERSON>", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedBobina", "showQuickAddDialog", "setShowQuickAddDialog", "formData", "setFormData", "numero_bobina", "utility", "tipologia", "n_conduttori", "sezione", "metri_totali", "metri_residui", "stato_bobina", "ubicazione_bobina", "fornitore", "n_DDT", "data_DDT", "configurazione", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "storico<PERSON><PERSON><PERSON><PERSON>", "setStoricoUtilizzo", "openConfigDialog", "setOpenConfigDialog", "isFirstInsertion", "setIsFirstInsertion", "loadBobine", "data", "getBobine", "error", "errorMessage", "detail", "JSON", "stringify", "message", "console", "loadStoricoUtilizzo", "getStoricoUtilizzo", "initialLoadDone", "useRef", "current", "log", "handleOptionSelect", "checkIfFirstInsertion", "isNaN", "parseInt", "<PERSON><PERSON><PERSON><PERSON>", "response", "isFirstBobinaInsertion", "is_first_insertion", "savedConfig", "localStorage", "getItem", "nextBobinaNumber", "length", "numericBobine", "filter", "b", "test", "map", "maxNumber", "Math", "max", "String", "_error$response$data", "status", "request", "handleConfigConfirm", "config<PERSON><PERSON><PERSON>", "setItem", "defaultFormData", "setTimeout", "option", "handleCloseDialog", "handleBobinaSelect", "bobina", "undefined", "Number", "handleFormChange", "e", "name", "value", "target", "idResult", "valid", "prev", "newErrors", "result", "warning", "newWarnings", "handleSave", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "trim", "bobina<PERSON><PERSON>", "parseFloat", "createBobina", "window", "event", "CustomEvent", "dispatchEvent", "bobina<PERSON>d", "id_bobina", "updateBobina", "deleteBobina", "is_last_bobina", "renderBobineCards", "onFilteredDataChange", "filteredData", "onEdit", "onDelete", "onViewHistory", "onQuickAdd", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "severity", "sx", "mb", "mt", "variant", "fontWeight", "Object", "keys", "style", "margin", "paddingLeft", "values", "index", "container", "spacing", "item", "xs", "sm", "label", "onChange", "disabled", "required", "InputProps", "bgcolor", "helperText", "type", "id", "labelId", "placeholder", "readOnly", "color", "onClick", "startIcon", "size", "button", "primary", "secondary", "display", "flexDirection", "gap", "component", "record", "cavi", "p", "justifyContent", "alignItems", "borderRadius", "my", "minHeight", "textAlign", "gutterBottom", "onConfirm", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ParcoCavi.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  History as HistoryIcon,\n  Save as SaveIcon,\n  ViewList as ViewListIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport ConfigurazioneDialog from './ConfigurazioneDialog';\nimport BobineFilterableTable from './BobineFilterableTable';\nimport QuickAddCablesDialog from './QuickAddCablesDialog';\nimport { validateBobinaData, validateBobinaField, validateBobinaId, isEmpty } from '../../utils/bobinaValidationUtils';\n\nconst ParcoCavi = ({ cantiereId, onSuccess, onError, initialOption = null }) => {\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(initialOption);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [showQuickAddDialog, setShowQuickAddDialog] = useState(false);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'Disponibile',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n  const [openConfigDialog, setOpenConfigDialog] = useState(false);\n  const [isFirstInsertion, setIsFirstInsertion] = useState(false);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      let errorMessage = 'Errore nel caricamento delle bobine';\n      if (error.detail) {\n        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);\n      } else if (error.message) {\n        errorMessage += ': ' + error.message;\n      }\n      onError(errorMessage);\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica lo storico utilizzo bobine\n  const loadStoricoUtilizzo = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getStoricoUtilizzo(cantiereId);\n      setStoricoUtilizzo(data);\n    } catch (error) {\n      let errorMessage = 'Errore nel caricamento dello storico utilizzo';\n      if (error.detail) {\n        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);\n      } else if (error.message) {\n        errorMessage += ': ' + error.message;\n      }\n      onError(errorMessage);\n      console.error('Errore nel caricamento dello storico utilizzo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente e gestisce l'opzione iniziale\n  // Utilizziamo una ref per tenere traccia se l'effetto è già stato eseguito\n  const initialLoadDone = React.useRef(false);\n\n  useEffect(() => {\n    // Esegui solo una volta all'avvio del componente\n    if (!initialLoadDone.current) {\n      console.log('Primo caricamento del componente, initialOption:', initialOption);\n      initialLoadDone.current = true;\n\n      if (initialOption === 'creaBobina') {\n        console.log('Avvio processo creazione bobina');\n        // IMPORTANTE: Per il primo caricamento con creaBobina, forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE ALL\\'AVVIO');\n        setIsFirstInsertion(true);\n        setOpenConfigDialog(true);\n      } else if (initialOption) {\n        console.log('Eseguendo handleOptionSelect con:', initialOption);\n        handleOptionSelect(initialOption);\n      } else {\n        console.log('Caricando bobine');\n        loadBobine();\n      }\n    }\n  }, []);  // Dipendenze vuote per eseguire solo al mount\n\n  // Verifica se è il primo inserimento di una bobina per un cantiere\n  const checkIfFirstInsertion = async () => {\n    // Variabile per memorizzare la configurazione\n    let configurazione = 's'; // Valore predefinito\n\n    try {\n      // Previene chiamate multiple\n      if (loading) {\n        console.log('Operazione già in corso, uscita');\n        return;\n      }\n\n      // Assicuriamoci che nessun dialog sia aperto\n      setOpenDialog(false);\n      setOpenConfigDialog(false); // Chiudi anche il dialog di configurazione se aperto\n\n      setLoading(true);\n      console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n\n      // Gestione caso in cui cantiereId non sia valido\n      if (!cantiereId || isNaN(parseInt(cantiereId))) {\n        onError('ID cantiere non valido');\n        console.error('ID cantiere non valido:', cantiereId);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API reale\n      let isFirst = false;\n\n      try {\n        console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n        const response = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\n        isFirst = response.is_first_insertion;\n\n        // Controlla se c'è una configurazione salvata in localStorage\n        const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`);\n\n        // Usa la configurazione salvata in localStorage se disponibile, altrimenti usa quella dal server\n        configurazione = savedConfig || response.configurazione || 's';\n\n        console.log('Configurazione da localStorage:', savedConfig);\n        console.log('Configurazione dal server:', response.configurazione);\n        console.log('Configurazione finale utilizzata:', configurazione);\n\n        setIsFirstInsertion(isFirst);\n        console.log('È il primo inserimento di una bobina?', isFirst);\n        console.log('Configurazione esistente:', configurazione);\n      } catch (error) {\n        console.error('Errore durante la verifica del primo inserimento:', error);\n        // In caso di errore, assumiamo che non sia il primo inserimento\n        setIsFirstInsertion(false);\n        onError('Errore durante la verifica del primo inserimento. Procedendo con inserimento standard.');\n      }\n\n      if (isFirst) {\n        // Se è il primo inserimento, mostra il dialog di configurazione\n        console.log('Mostrando il dialog di configurazione');\n        // Assicuriamoci che il dialog di creazione sia chiuso\n        setOpenDialog(false);\n\n        // IMPORTANTE: Forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE');\n        setOpenConfigDialog(true);\n      } else {\n        // Non è il primo inserimento, procedi con il form normale\n        console.log('Non è il primo inserimento, mostrando il form normale');\n\n        // Ottieni il prossimo numero di bobina solo se la configurazione è automatica\n        let nextBobinaNumber = '1';\n        if (configurazione === 's') {\n          try {\n            // Ottieni l'ultimo numero di bobina dal backend\n            const bobine = await parcoCaviService.getBobine(cantiereId);\n            if (bobine && bobine.length > 0) {\n              // Filtra solo le bobine con numero_bobina numerico\n              const numericBobine = bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina));\n\n              // Log per debug\n              console.log('Bobine totali:', bobine.length);\n              console.log('Bobine con numero numerico:', numericBobine.length);\n              console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));\n\n              if (numericBobine.length > 0) {\n                // Trova il numero massimo tra le bobine esistenti\n                const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));\n                console.log('Numero massimo trovato:', maxNumber);\n                nextBobinaNumber = String(maxNumber + 1);\n              }\n            }\n            console.log('Prossimo numero bobina:', nextBobinaNumber);\n          } catch (error) {\n            console.error('Errore nel recupero del prossimo numero bobina:', error);\n            // In caso di errore, usa 1 come default\n            nextBobinaNumber = '1';\n          }\n        }\n\n        setDialogType('creaBobina');\n        setFormData({\n          // In modalità automatica, imposta il numero progressivo\n          // In modalità manuale, lascia vuoto per far inserire all'utente\n          numero_bobina: configurazione === 's' ? nextBobinaNumber : '',\n          utility: '',\n          tipologia: '',\n          n_conduttori: '',\n          sezione: '',\n          metri_totali: '',\n          metri_residui: '',\n          stato_bobina: 'Disponibile',\n          ubicazione_bobina: '',\n          fornitore: '',\n          n_DDT: '',\n          data_DDT: '',\n          configurazione: configurazione  // Usa la configurazione esistente\n        });\n        setOpenDialog(true);\n      }\n    } catch (error) {\n      // Gestione dettagliata dell'errore\n      let errorMessage = 'Errore nel controllo dell\\'inserimento della prima bobina';\n\n      if (error.response) {\n        // Errore di risposta dal server\n        errorMessage += `: ${error.response.status} - ${error.response.data?.detail || 'Errore server'}`;\n        console.error('Dettagli errore API:', error.response);\n      } else if (error.request) {\n        // Errore di rete (nessuna risposta ricevuta)\n        errorMessage += ': Errore di connessione al server';\n      } else {\n        // Errore generico\n        errorMessage += `: ${error.message || 'Errore sconosciuto'}`;\n      }\n\n      onError(errorMessage);\n      console.error('Errore completo:', error);\n\n      // In caso di errore, mantieni la configurazione esistente o usa il default\n      // Non forzare il reset a 's' per evitare di perdere la configurazione manuale\n      if (!configurazione) {\n        configurazione = 's'; // Fallback al valore di default solo se non è già impostato\n      }\n\n      setDialogType('creaBobina');\n      setFormData({\n        numero_bobina: configurazione === 's' ? '1' : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configurazione  // Usa la configurazione esistente o il default\n      });\n      setOpenDialog(true);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la conferma della configurazione\n  const handleConfigConfirm = async (configValue) => {\n    console.log('Configurazione selezionata:', configValue);\n    // Salva la configurazione selezionata in localStorage per persistenza\n    localStorage.setItem(`cantiere_${cantiereId}_config`, configValue);\n    setLoading(true);\n\n    try {\n      // Ottieni il prossimo numero di bobina se la configurazione è automatica\n      let nextBobinaNumber = '1';\n      if (configValue === 's') {\n        try {\n          // Ottieni l'ultimo numero di bobina dal backend\n          const bobine = await parcoCaviService.getBobine(cantiereId);\n          if (bobine && bobine.length > 0) {\n            // Filtra solo le bobine con numero_bobina numerico\n            const numericBobine = bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina));\n\n            // Log per debug\n            console.log('Bobine totali:', bobine.length);\n            console.log('Bobine con numero numerico:', numericBobine.length);\n            console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));\n\n            if (numericBobine.length > 0) {\n              // Trova il numero massimo tra le bobine esistenti\n              const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));\n              console.log('Numero massimo trovato:', maxNumber);\n              nextBobinaNumber = String(maxNumber + 1);\n            }\n          }\n          console.log('Prossimo numero bobina:', nextBobinaNumber);\n        } catch (error) {\n          console.error('Errore nel recupero del prossimo numero bobina:', error);\n          // In caso di errore, usa 1 come default\n          nextBobinaNumber = '1';\n        }\n      }\n\n      // Imposta i valori di default per la bobina\n      const defaultFormData = {\n        // In modalità automatica, imposta il numero progressivo\n        // In modalità manuale, lascia vuoto per far inserire all'utente\n        numero_bobina: configValue === 's' ? nextBobinaNumber : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configValue // Imposta la configurazione scelta\n      };\n\n      console.log('Impostando i dati del form con configurazione:', configValue, 'numero_bobina:', defaultFormData.numero_bobina);\n\n      // Importante: prima prepara il form, poi chiudi il dialog di configurazione\n      // e solo dopo apri il dialog di creazione\n      setFormData(defaultFormData);\n      setDialogType('creaBobina');\n\n      // Chiudi il dialog di configurazione\n      setOpenConfigDialog(false);\n\n      // Piccolo timeout per assicurarsi che il dialog di configurazione sia chiuso\n      // prima di aprire il dialog di creazione\n      setTimeout(() => {\n        setOpenDialog(true);\n        console.log('Dialog di creazione bobina aperto con numero_bobina:', defaultFormData.numero_bobina);\n      }, 300);\n    } catch (error) {\n      console.error('Errore durante la preparazione del form:', error);\n      onError('Errore durante la preparazione del form: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      checkIfFirstInsertion();\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n\n    // Recupera la configurazione salvata per questo cantiere\n    const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`) || 's';\n\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: savedConfig // Mantieni la configurazione salvata\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = (bobina) => {\n    console.log('Bobina selezionata:', bobina);\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n\n      // Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe\n      setFormData({\n        numero_bobina: bobina.numero_bobina || '',\n        utility: String(bobina.utility || ''),\n        tipologia: String(bobina.tipologia || ''),\n        n_conduttori: bobina.n_conduttori !== null && bobina.n_conduttori !== undefined ? String(bobina.n_conduttori) : '',\n        sezione: bobina.sezione !== null && bobina.sezione !== undefined ? String(bobina.sezione) : '',\n        metri_totali: bobina.metri_totali !== null && bobina.metri_totali !== undefined ? Number(bobina.metri_totali) : '',\n        metri_residui: bobina.metri_residui !== null && bobina.metri_residui !== undefined ? Number(bobina.metri_residui) : '',\n        stato_bobina: String(bobina.stato_bobina || 'Disponibile'),\n        ubicazione_bobina: String(bobina.ubicazione_bobina || ''),\n        fornitore: String(bobina.fornitore || ''),\n        n_DDT: String(bobina.n_DDT || ''),\n        data_DDT: bobina.data_DDT || '',\n        configurazione: String(bobina.configurazione || 's')\n      });\n\n      console.log('Form data impostati per la modifica:', {\n        numero_bobina: bobina.numero_bobina,\n        utility: bobina.utility,\n        tipologia: bobina.tipologia,\n        n_conduttori: bobina.n_conduttori,\n        sezione: bobina.sezione,\n        metri_totali: bobina.metri_totali,\n        metri_residui: bobina.metri_residui\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      // Validazione speciale per numero_bobina quando configurazione è 'n'\n      if (name === 'numero_bobina' && formData.configurazione === 'n') {\n        const idResult = validateBobinaId(value);\n        if (!idResult.valid) {\n          setFormErrors(prev => ({\n            ...prev,\n            [name]: idResult.message\n          }));\n        } else {\n          setFormErrors(prev => {\n            const newErrors = { ...prev };\n            delete newErrors[name];\n            return newErrors;\n          });\n        }\n        return;\n      }\n\n      const result = validateBobinaField(name, value);\n\n      // Aggiorna gli errori\n      if (!result.valid) {\n        setFormErrors(prev => ({\n          ...prev,\n          [name]: result.message\n        }));\n      } else {\n        setFormErrors(prev => {\n          const newErrors = { ...prev };\n          delete newErrors[name];\n          return newErrors;\n        });\n\n        // Aggiorna gli avvisi\n        if (result.warning) {\n          setFormWarnings(prev => ({\n            ...prev,\n            [name]: result.message\n          }));\n        } else {\n          setFormWarnings(prev => {\n            const newWarnings = { ...prev };\n            delete newWarnings[name];\n            return newWarnings;\n          });\n        }\n      }\n    }\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      // Validazione completa dei dati prima del salvataggio\n      if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n        const validation = validateBobinaData(formData);\n\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          onError('Correggi gli errori nel form prima di salvare');\n          return;\n        }\n\n        // Verifica aggiuntiva per il campo numero_bobina quando la configurazione è manuale\n        if (formData.configurazione === 'n' && (!formData.numero_bobina || formData.numero_bobina.trim() === '')) {\n          setFormErrors({\n            ...formErrors,\n            numero_bobina: 'L\\'ID della bobina è obbligatorio'\n          });\n          onError('L\\'ID della bobina è obbligatorio');\n          return;\n        }\n      }\n\n      setLoading(true);\n      console.log('Salvataggio dati bobina in corso...');\n\n      if (dialogType === 'creaBobina') {\n        // Prepara i dati per la creazione della bobina\n        const bobinaData = {\n          ...formData,\n          // Assicurati che tutti i campi siano nel formato corretto\n          numero_bobina: String(formData.numero_bobina || ''),\n          utility: String(formData.utility || ''),\n          tipologia: String(formData.tipologia || ''),\n          n_conduttori: String(formData.n_conduttori || ''),\n          sezione: String(formData.sezione || ''),\n          metri_totali: parseFloat(formData.metri_totali) || 0,\n          ubicazione_bobina: String(formData.ubicazione_bobina || 'TBD'),\n          fornitore: String(formData.fornitore || 'TBD'),\n          n_DDT: String(formData.n_DDT || 'TBD'),\n          data_DDT: formData.data_DDT || null,\n          // Assicurati che la configurazione sia impostata correttamente\n          configurazione: String(formData.configurazione || 's')\n        };\n\n        console.log('Dati bobina da inviare:', bobinaData);\n\n        try {\n          // Log dei dati che stiamo per inviare\n          console.log('Invio dati bobina al backend:', JSON.stringify(bobinaData, null, 2));\n\n          // Invia i dati al backend\n          await parcoCaviService.createBobina(cantiereId, bobinaData);\n          onSuccess('Bobina creata con successo');\n\n          // Chiudi il dialog\n          handleCloseDialog();\n\n          // Imposta l'opzione selezionata a visualizzaBobine e carica le bobine\n          setSelectedOption('visualizzaBobine');\n          loadBobine();\n\n          // Notifica al componente padre che dovrebbe reindirizzare alla pagina di visualizzazione bobine\n          if (typeof window !== 'undefined') {\n            // Usa un evento personalizzato per comunicare con il componente padre\n            const event = new CustomEvent('redirectToVisualizzaBobine', { detail: { cantiereId } });\n            window.dispatchEvent(event);\n          }\n        } catch (error) {\n          console.error('Errore durante la creazione della bobina:', error);\n\n          // Gestione dettagliata dell'errore\n          let errorMessage = 'Errore durante la creazione della bobina';\n\n          if (error.detail) {\n            errorMessage = typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n\n          // Log dettagliato dell'errore\n          console.error('Dettagli errore:', errorMessage);\n          console.error('Dati inviati:', JSON.stringify(bobinaData, null, 2));\n\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'modificaBobina') {\n        // Per la modifica, usa l'ID bobina completo o il numero bobina\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina || formData.numero_bobina;\n\n        console.log('Modifica bobina con ID:', bobinaId);\n        console.log('Dati da inviare:', formData);\n\n        try {\n          const response = await parcoCaviService.updateBobina(cantiereId, bobinaId, formData);\n          console.log('Risposta modifica bobina:', response);\n          onSuccess('Bobina modificata con successo');\n        } catch (error) {\n          console.error('Errore durante la modifica della bobina:', error);\n          let errorMessage = 'Errore durante la modifica della bobina';\n          if (error.detail) {\n            errorMessage = typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'eliminaBobina') {\n        // Per l'eliminazione, usa l'ID bobina completo\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina;\n        const response = await parcoCaviService.deleteBobina(cantiereId, bobinaId);\n\n        // Verifica se è stata eliminata l'ultima bobina\n        if (response.is_last_bobina) {\n          console.log(`Eliminata l'ultima bobina del cantiere ${cantiereId}. Il parco cavi è ora vuoto.`);\n          onSuccess('Bobina eliminata con successo. Il parco cavi è ora vuoto.');\n\n          // Forza il refresh della pagina per reinnescare il sistema del primo inserimento\n          setTimeout(() => {\n            // Imposta l'opzione selezionata a creaBobina per forzare il dialog di configurazione\n            setSelectedOption('creaBobina');\n            // Forza l'apertura del dialog di configurazione\n            setIsFirstInsertion(true);\n            setOpenConfigDialog(true);\n          }, 1000);\n        } else {\n          onSuccess('Bobina eliminata con successo');\n        }\n      }\n\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      let errorMessage = 'Errore durante l\\'operazione';\n      if (error.detail) {\n        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);\n      } else if (error.message) {\n        errorMessage += ': ' + error.message;\n      } else {\n        errorMessage += ': Errore sconosciuto';\n      }\n      onError(errorMessage);\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // La visualizzazione delle bobine è ora gestita dal componente BobineFilterableTable\n  const renderBobineCards = () => {\n    return (\n      <BobineFilterableTable\n        bobine={bobine}\n        loading={loading}\n        onFilteredDataChange={(filteredData) => console.log('Bobine filtrate:', filteredData.length)}\n        onEdit={(bobina) => {\n          setSelectedBobina(bobina);\n          setDialogType('modificaBobina');\n          setOpenDialog(true);\n        }}\n        onDelete={(bobina) => {\n          setSelectedBobina(bobina);\n          setDialogType('eliminaBobina');\n          setOpenDialog(true);\n        }}\n        onViewHistory={(bobina) => {\n          setSelectedBobina(bobina);\n          loadStoricoUtilizzo();\n          setDialogType('visualizzaStorico');\n          setOpenDialog(true);\n        }}\n        onQuickAdd={(bobina) => {\n          setSelectedBobina(bobina);\n          setShowQuickAddDialog(true);\n        }}\n      />\n    );\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'}\n          </DialogTitle>\n          <DialogContent>\n            {dialogType === 'modificaBobina' && (\n              <Alert severity=\"info\" sx={{ mb: 2, mt: 1 }}>\n                <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                  Condizioni per la modifica:\n                </Typography>\n                <ul>\n                  <li>La bobina deve essere nello stato \"Disponibile\"</li>\n                  <li>La bobina non deve essere associata a nessun cavo</li>\n                  <li>Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente</li>\n                </ul>\n                <Box sx={{ mt: 1 }}>\n                  <Typography variant=\"body2\">\n                    <strong>Stato attuale:</strong> {selectedBobina?.stato_bobina || 'N/A'}\n                  </Typography>\n                </Box>\n              </Alert>\n            )}\n\n            {Object.keys(formWarnings).length > 0 && (\n              <Alert severity=\"warning\" sx={{ mb: 2, mt: 1 }}>\n                <Typography variant=\"subtitle2\">Attenzione:</Typography>\n                <ul style={{ margin: 0, paddingLeft: '20px' }}>\n                  {Object.values(formWarnings).map((warning, index) => (\n                    <li key={index}>{warning}</li>\n                  ))}\n                </ul>\n              </Alert>\n            )}\n\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"numero_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.numero_bobina}\n                  onChange={handleFormChange}\n                  disabled={dialogType === 'modificaBobina' || (dialogType === 'creaBobina' && formData.configurazione === 's')}\n                  required\n                  error={!!formErrors.numero_bobina}\n                  InputProps={{\n                    sx: {\n                      bgcolor: formData.configurazione === 's' ? '#f5f5f5' : 'transparent',\n                      fontWeight: 'bold',\n                    }\n                  }}\n                  helperText={\n                    formErrors.numero_bobina ||\n                    (dialogType === 'creaBobina' && formData.configurazione === 's'\n                      ? `ID completo: C${cantiereId}_B${formData.numero_bobina || ''} (generato automaticamente)`\n                      : dialogType === 'creaBobina' && formData.configurazione === 'n'\n                        ? `Inserisci l'ID della bobina (es. A123). ID completo sarà: C${cantiereId}_B{tuo input}`\n                        : '')\n                  }\n                  type={formData.configurazione === 's' ? \"text\" : \"text\"}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"N° Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.n_conduttori}\n                  helperText={formErrors.n_conduttori || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_totali\"\n                  label=\"Metri Totali\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_totali}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_totali}\n                  helperText={formErrors.metri_totali || ''}\n                />\n              </Grid>\n              {dialogType === 'modificaBobina' && (\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"metri_residui\"\n                    label=\"Metri Residui\"\n                    type=\"number\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.metri_residui}\n                    onChange={handleFormChange}\n                    required\n                    disabled={true}\n                    helperText=\"I metri residui non possono essere modificati direttamente\"\n                  />\n                </Grid>\n              )}\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel id=\"stato-bobina-label\">Stato Bobina</InputLabel>\n                  <Select\n                    labelId=\"stato-bobina-label\"\n                    name=\"stato_bobina\"\n                    value={formData.stato_bobina}\n                    label=\"Stato Bobina\"\n                    onChange={handleFormChange}\n                    disabled={dialogType === 'creaBobina'}\n                  >\n                    <MenuItem value=\"Disponibile\">Disponibile</MenuItem>\n                    <MenuItem value=\"In uso\">In uso</MenuItem>\n                    <MenuItem value=\"Terminata\">Terminata</MenuItem>\n                    <MenuItem value=\"Danneggiata\">Danneggiata</MenuItem>\n                    <MenuItem value=\"Over\">Over</MenuItem>\n                  </Select>\n                  {dialogType === 'creaBobina' && (\n                    <FormHelperText>Per una nuova bobina, lo stato è sempre \"Disponibile\"</FormHelperText>\n                  )}\n                </FormControl>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_bobina\"\n                  label=\"Ubicazione Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_bobina}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_bobina}\n                  helperText={formErrors.ubicazione_bobina || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"fornitore\"\n                  label=\"Fornitore\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.fornitore}\n                  onChange={handleFormChange}\n                  error={!!formErrors.fornitore}\n                  helperText={formErrors.fornitore || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_DDT\"\n                  label=\"Numero DDT\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_DDT}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_DDT}\n                  helperText={formErrors.n_DDT || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"data_DDT\"\n                  label=\"Data DDT (YYYY-MM-DD)\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.data_DDT}\n                  onChange={handleFormChange}\n                  placeholder=\"YYYY-MM-DD\"\n                  error={!!formErrors.data_DDT}\n                  helperText={formErrors.data_DDT || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"configurazione\"\n                  label=\"Modalità Numerazione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.configurazione === 's' ? 'Automatica' : 'Manuale'}\n                  InputProps={{\n                    readOnly: true,\n                    sx: {\n                      bgcolor: '#f5f5f5',\n                    }\n                  }}\n                  helperText={\n                    <Box sx={{ fontWeight: 'medium', color: formData.configurazione === 's' ? 'success.main' : 'info.main' }}>\n                      {formData.configurazione === 's'\n                        ? 'Numerazione progressiva automatica (1, 2, 3, ...)'\n                        : 'Inserimento manuale dell\\'ID bobina (es. A123, SPEC01, ...)'}\n                    </Box>\n                  }\n                />\n              </Grid>\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading || Object.keys(formErrors).length > 0}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              variant=\"contained\"\n              color=\"primary\"\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Bobina da Modificare</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : bobine.length === 0 ? (\n              <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n            ) : (\n              <List>\n                {bobine.map((bobina) => (\n                  <ListItem\n                    button\n                    key={bobina.numero_bobina}\n                    onClick={() => handleBobinaSelect(bobina)}\n                  >\n                    <ListItemText\n                      primary={`Bobina: ${bobina.numero_bobina}`}\n                      secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Elimina Bobina</DialogTitle>\n          <DialogContent>\n            {!selectedBobina ? (\n              loading ? (\n                <CircularProgress />\n              ) : bobine.length === 0 ? (\n                <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n              ) : (\n                <List>\n                  {bobine.map((bobina) => (\n                    <ListItem\n                      button\n                      key={bobina.numero_bobina}\n                      onClick={() => setSelectedBobina(bobina)}\n                    >\n                      <ListItemText\n                        primary={`Bobina: ${bobina.numero_bobina}`}\n                        secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              )\n            ) : (\n              <Box>\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  Sei sicuro di voler eliminare la bobina {selectedBobina.numero_bobina}?\n                </Alert>\n                <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                  Questa operazione non può essere annullata.\n                </Typography>\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                    Condizioni per l'eliminazione:\n                  </Typography>\n                  <ul>\n                    <li>La bobina deve essere completamente integra (metri residui = metri totali)</li>\n                    <li>La bobina deve essere nello stato \"Disponibile\"</li>\n                    <li>La bobina non deve essere associata a nessun cavo</li>\n                  </ul>\n                </Alert>\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 2 }}>\n                  <Typography variant=\"body2\">\n                    <strong>Stato attuale:</strong> {selectedBobina.stato_bobina || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\">\n                    <strong>Metri totali:</strong> {selectedBobina.metri_totali || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\">\n                    <strong>Metri residui:</strong> {selectedBobina.metri_residui || 'N/A'}\n                  </Typography>\n                </Box>\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedBobina && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color=\"error\"\n                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}\n              >\n                Elimina\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'visualizzaStorico') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"lg\" fullWidth>\n          <DialogTitle>Storico Utilizzo Bobine</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : storicoUtilizzo.length === 0 ? (\n              <Alert severity=\"info\">Nessun dato storico disponibile</Alert>\n            ) : (\n              <TableContainer component={Paper} sx={{ mt: 2 }}>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Bobina</TableCell>\n                      <TableCell>Utility</TableCell>\n                      <TableCell>Tipologia</TableCell>\n                      <TableCell>N° Conduttori</TableCell>\n                      <TableCell>Sezione</TableCell>\n                      <TableCell>Metri Totali</TableCell>\n                      <TableCell>Metri Residui</TableCell>\n                      <TableCell>Cavi Associati</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {storicoUtilizzo.map((record, index) => (\n                      <TableRow key={index}>\n                        <TableCell>{record.numero_bobina}</TableCell>\n                        <TableCell>{record.utility}</TableCell>\n                        <TableCell>{record.tipologia}</TableCell>\n                        <TableCell>{record.n_conduttori}</TableCell>\n                        <TableCell>{record.sezione}</TableCell>\n                        <TableCell>{record.metri_totali}</TableCell>\n                        <TableCell>{record.metri_residui}</TableCell>\n                        <TableCell>{record.cavi.length}</TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Chiudi</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box>\n      {selectedOption === 'visualizzaBobine' && !openDialog ? (\n        <Paper sx={{ p: 3 }}>\n          <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n            <Box sx={{ display: 'flex', gap: 2 }}>\n              <Button\n                variant=\"outlined\"\n                color=\"inherit\"\n                startIcon={<HistoryIcon />}\n                onClick={() => {\n                  loadStoricoUtilizzo();\n                  setDialogType('visualizzaStorico');\n                  setOpenDialog(true);\n                }}\n                sx={{\n                  fontWeight: 'medium',\n                  borderRadius: 0\n                }}\n              >\n                Storico Utilizzo\n              </Button>\n              <Button\n                variant=\"contained\"\n                color=\"inherit\"\n                startIcon={<AddIcon />}\n                onClick={() => checkIfFirstInsertion()}\n                sx={{\n                  fontWeight: 'medium',\n                  borderRadius: 0\n                }}\n              >\n                Crea Nuova Bobina\n              </Button>\n            </Box>\n          </Box>\n          {loading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <BobineFilterableTable\n              bobine={bobine}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Bobine filtrate:', filteredData.length)}\n              onEdit={(bobina) => {\n                setSelectedBobina(bobina);\n                setDialogType('modificaBobina');\n                setOpenDialog(true);\n              }}\n              onDelete={(bobina) => {\n                setSelectedBobina(bobina);\n                setDialogType('eliminaBobina');\n                setOpenDialog(true);\n              }}\n              onViewHistory={(bobina) => {\n                setSelectedBobina(bobina);\n                loadStoricoUtilizzo();\n                setDialogType('visualizzaStorico');\n                setOpenDialog(true);\n              }}\n              onQuickAdd={(bobina) => {\n                setSelectedBobina(bobina);\n                setShowQuickAddDialog(true);\n              }}\n            />\n          )}\n        </Paper>\n      ) : !openDialog ? (\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption ? (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu principale per iniziare.\n            </Typography>\n          ) : (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'creaBobina' && 'Crea Nuova Bobina'}\n                {selectedOption === 'modificaBobina' && 'Modifica Bobina'}\n                {selectedOption === 'eliminaBobina' && 'Elimina Bobina'}\n                {selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo'}\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      ) : null}\n\n      {renderDialog()}\n\n      {/* Dialog di configurazione per il primo inserimento */}\n      <ConfigurazioneDialog\n        open={openConfigDialog}\n        onClose={() => setOpenConfigDialog(false)}\n        onConfirm={handleConfigConfirm}\n      />\n\n      {/* Dialog per aggiungere rapidamente cavi a una bobina */}\n      <QuickAddCablesDialog\n        open={showQuickAddDialog}\n        onClose={() => {\n          setShowQuickAddDialog(false);\n          // Ricarica le bobine per riflettere i cambiamenti\n          loadBobine();\n        }}\n        bobina={selectedBobina}\n        cantiereId={cantiereId}\n        onSuccess={onSuccess}\n        onError={onError}\n      />\n    </Box>\n  );\n};\n\nexport default ParcoCavi;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,QACH,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,SAASC,kBAAkB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,OAAO,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvH,MAAMC,SAAS,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC9E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmE,MAAM,EAAEC,SAAS,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACqE,cAAc,EAAEC,iBAAiB,CAAC,GAAGtE,QAAQ,CAAC+D,aAAa,CAAC;EACnE,MAAM,CAACQ,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyE,UAAU,EAAEC,aAAa,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2E,cAAc,EAAEC,iBAAiB,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC6E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC+E,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAAC;IACvCiF,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,aAAa;IAC3BC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACgG,YAAY,EAAEC,eAAe,CAAC,GAAGjG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACkG,eAAe,EAAEC,kBAAkB,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAMwG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFtC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMuC,IAAI,GAAG,MAAMxD,gBAAgB,CAACyD,SAAS,CAAC9C,UAAU,CAAC;MACzDQ,SAAS,CAACqC,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,IAAIC,YAAY,GAAG,qCAAqC;MACxD,IAAID,KAAK,CAACE,MAAM,EAAE;QAChBD,YAAY,IAAI,IAAI,IAAI,OAAOD,KAAK,CAACE,MAAM,KAAK,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACE,MAAM,CAAC;MACzG,CAAC,MAAM,IAAIF,KAAK,CAACK,OAAO,EAAE;QACxBJ,YAAY,IAAI,IAAI,GAAGD,KAAK,CAACK,OAAO;MACtC;MACAlD,OAAO,CAAC8C,YAAY,CAAC;MACrBK,OAAO,CAACN,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D,CAAC,SAAS;MACRzC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFhD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMuC,IAAI,GAAG,MAAMxD,gBAAgB,CAACkE,kBAAkB,CAACvD,UAAU,CAAC;MAClEuC,kBAAkB,CAACM,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd,IAAIC,YAAY,GAAG,+CAA+C;MAClE,IAAID,KAAK,CAACE,MAAM,EAAE;QAChBD,YAAY,IAAI,IAAI,IAAI,OAAOD,KAAK,CAACE,MAAM,KAAK,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACE,MAAM,CAAC;MACzG,CAAC,MAAM,IAAIF,KAAK,CAACK,OAAO,EAAE;QACxBJ,YAAY,IAAI,IAAI,GAAGD,KAAK,CAACK,OAAO;MACtC;MACAlD,OAAO,CAAC8C,YAAY,CAAC;MACrBK,OAAO,CAACN,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;IACxE,CAAC,SAAS;MACRzC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA;EACA,MAAMkD,eAAe,GAAGrH,KAAK,CAACsH,MAAM,CAAC,KAAK,CAAC;EAE3CpH,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACmH,eAAe,CAACE,OAAO,EAAE;MAC5BL,OAAO,CAACM,GAAG,CAAC,kDAAkD,EAAExD,aAAa,CAAC;MAC9EqD,eAAe,CAACE,OAAO,GAAG,IAAI;MAE9B,IAAIvD,aAAa,KAAK,YAAY,EAAE;QAClCkD,OAAO,CAACM,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAN,OAAO,CAACM,GAAG,CAAC,oDAAoD,CAAC;QACjEhB,mBAAmB,CAAC,IAAI,CAAC;QACzBF,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM,IAAItC,aAAa,EAAE;QACxBkD,OAAO,CAACM,GAAG,CAAC,mCAAmC,EAAExD,aAAa,CAAC;QAC/DyD,kBAAkB,CAACzD,aAAa,CAAC;MACnC,CAAC,MAAM;QACLkD,OAAO,CAACM,GAAG,CAAC,kBAAkB,CAAC;QAC/Bf,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAE;;EAET;EACA,MAAMiB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC;IACA,IAAI5B,cAAc,GAAG,GAAG,CAAC,CAAC;;IAE1B,IAAI;MACF;MACA,IAAI5B,OAAO,EAAE;QACXgD,OAAO,CAACM,GAAG,CAAC,iCAAiC,CAAC;QAC9C;MACF;;MAEA;MACA/C,aAAa,CAAC,KAAK,CAAC;MACpB6B,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;;MAE5BnC,UAAU,CAAC,IAAI,CAAC;MAChB+C,OAAO,CAACM,GAAG,CAAC,wDAAwD,EAAE3D,UAAU,CAAC;;MAEjF;MACA,IAAI,CAACA,UAAU,IAAI8D,KAAK,CAACC,QAAQ,CAAC/D,UAAU,CAAC,CAAC,EAAE;QAC9CE,OAAO,CAAC,wBAAwB,CAAC;QACjCmD,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAE/C,UAAU,CAAC;QACpDM,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAI0D,OAAO,GAAG,KAAK;MAEnB,IAAI;QACFX,OAAO,CAACM,GAAG,CAAC,wDAAwD,EAAE3D,UAAU,CAAC;QACjF,MAAMiE,QAAQ,GAAG,MAAM5E,gBAAgB,CAAC6E,sBAAsB,CAAClE,UAAU,CAAC;QAC1EgE,OAAO,GAAGC,QAAQ,CAACE,kBAAkB;;QAErC;QACA,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAYtE,UAAU,SAAS,CAAC;;QAEzE;QACAiC,cAAc,GAAGmC,WAAW,IAAIH,QAAQ,CAAChC,cAAc,IAAI,GAAG;QAE9DoB,OAAO,CAACM,GAAG,CAAC,iCAAiC,EAAES,WAAW,CAAC;QAC3Df,OAAO,CAACM,GAAG,CAAC,4BAA4B,EAAEM,QAAQ,CAAChC,cAAc,CAAC;QAClEoB,OAAO,CAACM,GAAG,CAAC,mCAAmC,EAAE1B,cAAc,CAAC;QAEhEU,mBAAmB,CAACqB,OAAO,CAAC;QAC5BX,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAEK,OAAO,CAAC;QAC7DX,OAAO,CAACM,GAAG,CAAC,2BAA2B,EAAE1B,cAAc,CAAC;MAC1D,CAAC,CAAC,OAAOc,KAAK,EAAE;QACdM,OAAO,CAACN,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;QACzE;QACAJ,mBAAmB,CAAC,KAAK,CAAC;QAC1BzC,OAAO,CAAC,wFAAwF,CAAC;MACnG;MAEA,IAAI8D,OAAO,EAAE;QACX;QACAX,OAAO,CAACM,GAAG,CAAC,uCAAuC,CAAC;QACpD;QACA/C,aAAa,CAAC,KAAK,CAAC;;QAEpB;QACAyC,OAAO,CAACM,GAAG,CAAC,yCAAyC,CAAC;QACtDlB,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL;QACAY,OAAO,CAACM,GAAG,CAAC,uDAAuD,CAAC;;QAEpE;QACA,IAAIY,gBAAgB,GAAG,GAAG;QAC1B,IAAItC,cAAc,KAAK,GAAG,EAAE;UAC1B,IAAI;YACF;YACA,MAAM1B,MAAM,GAAG,MAAMlB,gBAAgB,CAACyD,SAAS,CAAC9C,UAAU,CAAC;YAC3D,IAAIO,MAAM,IAAIA,MAAM,CAACiE,MAAM,GAAG,CAAC,EAAE;cAC/B;cACA,MAAMC,aAAa,GAAGlE,MAAM,CAACmE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACtD,aAAa,IAAI,OAAO,CAACuD,IAAI,CAACD,CAAC,CAACtD,aAAa,CAAC,CAAC;;cAE1F;cACAgC,OAAO,CAACM,GAAG,CAAC,gBAAgB,EAAEpD,MAAM,CAACiE,MAAM,CAAC;cAC5CnB,OAAO,CAACM,GAAG,CAAC,6BAA6B,EAAEc,aAAa,CAACD,MAAM,CAAC;cAChEnB,OAAO,CAACM,GAAG,CAAC,mBAAmB,EAAEc,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIA,CAAC,CAACtD,aAAa,CAAC,CAAC;cAEzE,IAAIoD,aAAa,CAACD,MAAM,GAAG,CAAC,EAAE;gBAC5B;gBACA,MAAMM,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGP,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIZ,QAAQ,CAACY,CAAC,CAACtD,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;gBACpFgC,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEmB,SAAS,CAAC;gBACjDP,gBAAgB,GAAGU,MAAM,CAACH,SAAS,GAAG,CAAC,CAAC;cAC1C;YACF;YACAzB,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEY,gBAAgB,CAAC;UAC1D,CAAC,CAAC,OAAOxB,KAAK,EAAE;YACdM,OAAO,CAACN,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;YACvE;YACAwB,gBAAgB,GAAG,GAAG;UACxB;QACF;QAEAzD,aAAa,CAAC,YAAY,CAAC;QAC3BM,WAAW,CAAC;UACV;UACA;UACAC,aAAa,EAAEY,cAAc,KAAK,GAAG,GAAGsC,gBAAgB,GAAG,EAAE;UAC7DjD,OAAO,EAAE,EAAE;UACXC,SAAS,EAAE,EAAE;UACbC,YAAY,EAAE,EAAE;UAChBC,OAAO,EAAE,EAAE;UACXC,YAAY,EAAE,EAAE;UAChBC,aAAa,EAAE,EAAE;UACjBC,YAAY,EAAE,aAAa;UAC3BC,iBAAiB,EAAE,EAAE;UACrBC,SAAS,EAAE,EAAE;UACbC,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,cAAc,EAAEA,cAAc,CAAE;QAClC,CAAC,CAAC;QACFrB,aAAa,CAAC,IAAI,CAAC;MACrB;IACF,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACd;MACA,IAAIC,YAAY,GAAG,2DAA2D;MAE9E,IAAID,KAAK,CAACkB,QAAQ,EAAE;QAAA,IAAAiB,oBAAA;QAClB;QACAlC,YAAY,IAAI,KAAKD,KAAK,CAACkB,QAAQ,CAACkB,MAAM,MAAM,EAAAD,oBAAA,GAAAnC,KAAK,CAACkB,QAAQ,CAACpB,IAAI,cAAAqC,oBAAA,uBAAnBA,oBAAA,CAAqBjC,MAAM,KAAI,eAAe,EAAE;QAChGI,OAAO,CAACN,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAACkB,QAAQ,CAAC;MACvD,CAAC,MAAM,IAAIlB,KAAK,CAACqC,OAAO,EAAE;QACxB;QACApC,YAAY,IAAI,mCAAmC;MACrD,CAAC,MAAM;QACL;QACAA,YAAY,IAAI,KAAKD,KAAK,CAACK,OAAO,IAAI,oBAAoB,EAAE;MAC9D;MAEAlD,OAAO,CAAC8C,YAAY,CAAC;MACrBK,OAAO,CAACN,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACA;MACA,IAAI,CAACd,cAAc,EAAE;QACnBA,cAAc,GAAG,GAAG,CAAC,CAAC;MACxB;MAEAnB,aAAa,CAAC,YAAY,CAAC;MAC3BM,WAAW,CAAC;QACVC,aAAa,EAAEY,cAAc,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE;QAChDX,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,aAAa;QAC3BC,iBAAiB,EAAE,EAAE;QACrBC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAEA,cAAc,CAAE;MAClC,CAAC,CAAC;MACFrB,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+E,mBAAmB,GAAG,MAAOC,WAAW,IAAK;IACjDjC,OAAO,CAACM,GAAG,CAAC,6BAA6B,EAAE2B,WAAW,CAAC;IACvD;IACAjB,YAAY,CAACkB,OAAO,CAAC,YAAYvF,UAAU,SAAS,EAAEsF,WAAW,CAAC;IAClEhF,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,IAAIiE,gBAAgB,GAAG,GAAG;MAC1B,IAAIe,WAAW,KAAK,GAAG,EAAE;QACvB,IAAI;UACF;UACA,MAAM/E,MAAM,GAAG,MAAMlB,gBAAgB,CAACyD,SAAS,CAAC9C,UAAU,CAAC;UAC3D,IAAIO,MAAM,IAAIA,MAAM,CAACiE,MAAM,GAAG,CAAC,EAAE;YAC/B;YACA,MAAMC,aAAa,GAAGlE,MAAM,CAACmE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACtD,aAAa,IAAI,OAAO,CAACuD,IAAI,CAACD,CAAC,CAACtD,aAAa,CAAC,CAAC;;YAE1F;YACAgC,OAAO,CAACM,GAAG,CAAC,gBAAgB,EAAEpD,MAAM,CAACiE,MAAM,CAAC;YAC5CnB,OAAO,CAACM,GAAG,CAAC,6BAA6B,EAAEc,aAAa,CAACD,MAAM,CAAC;YAChEnB,OAAO,CAACM,GAAG,CAAC,mBAAmB,EAAEc,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIA,CAAC,CAACtD,aAAa,CAAC,CAAC;YAEzE,IAAIoD,aAAa,CAACD,MAAM,GAAG,CAAC,EAAE;cAC5B;cACA,MAAMM,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGP,aAAa,CAACI,GAAG,CAACF,CAAC,IAAIZ,QAAQ,CAACY,CAAC,CAACtD,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;cACpFgC,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEmB,SAAS,CAAC;cACjDP,gBAAgB,GAAGU,MAAM,CAACH,SAAS,GAAG,CAAC,CAAC;YAC1C;UACF;UACAzB,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEY,gBAAgB,CAAC;QAC1D,CAAC,CAAC,OAAOxB,KAAK,EAAE;UACdM,OAAO,CAACN,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvE;UACAwB,gBAAgB,GAAG,GAAG;QACxB;MACF;;MAEA;MACA,MAAMiB,eAAe,GAAG;QACtB;QACA;QACAnE,aAAa,EAAEiE,WAAW,KAAK,GAAG,GAAGf,gBAAgB,GAAG,EAAE;QAC1DjD,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,aAAa;QAC3BC,iBAAiB,EAAE,EAAE;QACrBC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAEqD,WAAW,CAAC;MAC9B,CAAC;MAEDjC,OAAO,CAACM,GAAG,CAAC,gDAAgD,EAAE2B,WAAW,EAAE,gBAAgB,EAAEE,eAAe,CAACnE,aAAa,CAAC;;MAE3H;MACA;MACAD,WAAW,CAACoE,eAAe,CAAC;MAC5B1E,aAAa,CAAC,YAAY,CAAC;;MAE3B;MACA2B,mBAAmB,CAAC,KAAK,CAAC;;MAE1B;MACA;MACAgD,UAAU,CAAC,MAAM;QACf7E,aAAa,CAAC,IAAI,CAAC;QACnByC,OAAO,CAACM,GAAG,CAAC,sDAAsD,EAAE6B,eAAe,CAACnE,aAAa,CAAC;MACpG,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE7C,OAAO,CAAC,2CAA2C,IAAI6C,KAAK,CAACK,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAChG,CAAC,SAAS;MACR9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsD,kBAAkB,GAAI8B,MAAM,IAAK;IACrChF,iBAAiB,CAACgF,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,kBAAkB,EAAE;MACjC9C,UAAU,CAAC,CAAC;IACd,CAAC,MAAM,IAAI8C,MAAM,KAAK,YAAY,EAAE;MAClC7B,qBAAqB,CAAC,CAAC;IACzB,CAAC,MAAM,IAAI6B,MAAM,KAAK,gBAAgB,EAAE;MACtC9C,UAAU,CAAC,CAAC;MACZ9B,aAAa,CAAC,iBAAiB,CAAC;MAChCF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI8E,MAAM,KAAK,eAAe,EAAE;MACrC9C,UAAU,CAAC,CAAC;MACZ9B,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI8E,MAAM,KAAK,mBAAmB,EAAE;MACzCpC,mBAAmB,CAAC,CAAC;MACrBxC,aAAa,CAAC,mBAAmB,CAAC;MAClCF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM+E,iBAAiB,GAAGA,CAAA,KAAM;IAC9B/E,aAAa,CAAC,KAAK,CAAC;IACpBI,iBAAiB,CAAC,IAAI,CAAC;;IAEvB;IACA,MAAMoD,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAYtE,UAAU,SAAS,CAAC,IAAI,GAAG;IAEhFoB,WAAW,CAAC;MACVC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,aAAa;MAC3BC,iBAAiB,EAAE,EAAE;MACrBC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAEmC,WAAW,CAAC;IAC9B,CAAC,CAAC;IACFjC,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMuD,kBAAkB,GAAIC,MAAM,IAAK;IACrCxC,OAAO,CAACM,GAAG,CAAC,qBAAqB,EAAEkC,MAAM,CAAC;IAC1C7E,iBAAiB,CAAC6E,MAAM,CAAC;IACzB,IAAIhF,UAAU,KAAK,iBAAiB,EAAE;MACpCC,aAAa,CAAC,gBAAgB,CAAC;;MAE/B;MACAM,WAAW,CAAC;QACVC,aAAa,EAAEwE,MAAM,CAACxE,aAAa,IAAI,EAAE;QACzCC,OAAO,EAAE2D,MAAM,CAACY,MAAM,CAACvE,OAAO,IAAI,EAAE,CAAC;QACrCC,SAAS,EAAE0D,MAAM,CAACY,MAAM,CAACtE,SAAS,IAAI,EAAE,CAAC;QACzCC,YAAY,EAAEqE,MAAM,CAACrE,YAAY,KAAK,IAAI,IAAIqE,MAAM,CAACrE,YAAY,KAAKsE,SAAS,GAAGb,MAAM,CAACY,MAAM,CAACrE,YAAY,CAAC,GAAG,EAAE;QAClHC,OAAO,EAAEoE,MAAM,CAACpE,OAAO,KAAK,IAAI,IAAIoE,MAAM,CAACpE,OAAO,KAAKqE,SAAS,GAAGb,MAAM,CAACY,MAAM,CAACpE,OAAO,CAAC,GAAG,EAAE;QAC9FC,YAAY,EAAEmE,MAAM,CAACnE,YAAY,KAAK,IAAI,IAAImE,MAAM,CAACnE,YAAY,KAAKoE,SAAS,GAAGC,MAAM,CAACF,MAAM,CAACnE,YAAY,CAAC,GAAG,EAAE;QAClHC,aAAa,EAAEkE,MAAM,CAAClE,aAAa,KAAK,IAAI,IAAIkE,MAAM,CAAClE,aAAa,KAAKmE,SAAS,GAAGC,MAAM,CAACF,MAAM,CAAClE,aAAa,CAAC,GAAG,EAAE;QACtHC,YAAY,EAAEqD,MAAM,CAACY,MAAM,CAACjE,YAAY,IAAI,aAAa,CAAC;QAC1DC,iBAAiB,EAAEoD,MAAM,CAACY,MAAM,CAAChE,iBAAiB,IAAI,EAAE,CAAC;QACzDC,SAAS,EAAEmD,MAAM,CAACY,MAAM,CAAC/D,SAAS,IAAI,EAAE,CAAC;QACzCC,KAAK,EAAEkD,MAAM,CAACY,MAAM,CAAC9D,KAAK,IAAI,EAAE,CAAC;QACjCC,QAAQ,EAAE6D,MAAM,CAAC7D,QAAQ,IAAI,EAAE;QAC/BC,cAAc,EAAEgD,MAAM,CAACY,MAAM,CAAC5D,cAAc,IAAI,GAAG;MACrD,CAAC,CAAC;MAEFoB,OAAO,CAACM,GAAG,CAAC,sCAAsC,EAAE;QAClDtC,aAAa,EAAEwE,MAAM,CAACxE,aAAa;QACnCC,OAAO,EAAEuE,MAAM,CAACvE,OAAO;QACvBC,SAAS,EAAEsE,MAAM,CAACtE,SAAS;QAC3BC,YAAY,EAAEqE,MAAM,CAACrE,YAAY;QACjCC,OAAO,EAAEoE,MAAM,CAACpE,OAAO;QACvBC,YAAY,EAAEmE,MAAM,CAACnE,YAAY;QACjCC,aAAa,EAAEkE,MAAM,CAAClE;MACxB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMqE,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACAhF,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC+E,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,IAAItF,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;MAClE;MACA,IAAIqF,IAAI,KAAK,eAAe,IAAI/E,QAAQ,CAACc,cAAc,KAAK,GAAG,EAAE;QAC/D,MAAMoE,QAAQ,GAAG1G,gBAAgB,CAACwG,KAAK,CAAC;QACxC,IAAI,CAACE,QAAQ,CAACC,KAAK,EAAE;UACnBnE,aAAa,CAACoE,IAAI,KAAK;YACrB,GAAGA,IAAI;YACP,CAACL,IAAI,GAAGG,QAAQ,CAACjD;UACnB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACLjB,aAAa,CAACoE,IAAI,IAAI;YACpB,MAAMC,SAAS,GAAG;cAAE,GAAGD;YAAK,CAAC;YAC7B,OAAOC,SAAS,CAACN,IAAI,CAAC;YACtB,OAAOM,SAAS;UAClB,CAAC,CAAC;QACJ;QACA;MACF;MAEA,MAAMC,MAAM,GAAG/G,mBAAmB,CAACwG,IAAI,EAAEC,KAAK,CAAC;;MAE/C;MACA,IAAI,CAACM,MAAM,CAACH,KAAK,EAAE;QACjBnE,aAAa,CAACoE,IAAI,KAAK;UACrB,GAAGA,IAAI;UACP,CAACL,IAAI,GAAGO,MAAM,CAACrD;QACjB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLjB,aAAa,CAACoE,IAAI,IAAI;UACpB,MAAMC,SAAS,GAAG;YAAE,GAAGD;UAAK,CAAC;UAC7B,OAAOC,SAAS,CAACN,IAAI,CAAC;UACtB,OAAOM,SAAS;QAClB,CAAC,CAAC;;QAEF;QACA,IAAIC,MAAM,CAACC,OAAO,EAAE;UAClBrE,eAAe,CAACkE,IAAI,KAAK;YACvB,GAAGA,IAAI;YACP,CAACL,IAAI,GAAGO,MAAM,CAACrD;UACjB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACLf,eAAe,CAACkE,IAAI,IAAI;YACtB,MAAMI,WAAW,GAAG;cAAE,GAAGJ;YAAK,CAAC;YAC/B,OAAOI,WAAW,CAACT,IAAI,CAAC;YACxB,OAAOS,WAAW;UACpB,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,IAAI/F,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;QAClE,MAAMgG,UAAU,GAAGpH,kBAAkB,CAAC0B,QAAQ,CAAC;QAE/C,IAAI,CAAC0F,UAAU,CAACC,OAAO,EAAE;UACvB3E,aAAa,CAAC0E,UAAU,CAACE,MAAM,CAAC;UAChC1E,eAAe,CAACwE,UAAU,CAACG,QAAQ,CAAC;UACpC9G,OAAO,CAAC,+CAA+C,CAAC;UACxD;QACF;;QAEA;QACA,IAAIiB,QAAQ,CAACc,cAAc,KAAK,GAAG,KAAK,CAACd,QAAQ,CAACE,aAAa,IAAIF,QAAQ,CAACE,aAAa,CAAC4F,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE;UACxG9E,aAAa,CAAC;YACZ,GAAGD,UAAU;YACbb,aAAa,EAAE;UACjB,CAAC,CAAC;UACFnB,OAAO,CAAC,mCAAmC,CAAC;UAC5C;QACF;MACF;MAEAI,UAAU,CAAC,IAAI,CAAC;MAChB+C,OAAO,CAACM,GAAG,CAAC,qCAAqC,CAAC;MAElD,IAAI9C,UAAU,KAAK,YAAY,EAAE;QAC/B;QACA,MAAMqG,UAAU,GAAG;UACjB,GAAG/F,QAAQ;UACX;UACAE,aAAa,EAAE4D,MAAM,CAAC9D,QAAQ,CAACE,aAAa,IAAI,EAAE,CAAC;UACnDC,OAAO,EAAE2D,MAAM,CAAC9D,QAAQ,CAACG,OAAO,IAAI,EAAE,CAAC;UACvCC,SAAS,EAAE0D,MAAM,CAAC9D,QAAQ,CAACI,SAAS,IAAI,EAAE,CAAC;UAC3CC,YAAY,EAAEyD,MAAM,CAAC9D,QAAQ,CAACK,YAAY,IAAI,EAAE,CAAC;UACjDC,OAAO,EAAEwD,MAAM,CAAC9D,QAAQ,CAACM,OAAO,IAAI,EAAE,CAAC;UACvCC,YAAY,EAAEyF,UAAU,CAAChG,QAAQ,CAACO,YAAY,CAAC,IAAI,CAAC;UACpDG,iBAAiB,EAAEoD,MAAM,CAAC9D,QAAQ,CAACU,iBAAiB,IAAI,KAAK,CAAC;UAC9DC,SAAS,EAAEmD,MAAM,CAAC9D,QAAQ,CAACW,SAAS,IAAI,KAAK,CAAC;UAC9CC,KAAK,EAAEkD,MAAM,CAAC9D,QAAQ,CAACY,KAAK,IAAI,KAAK,CAAC;UACtCC,QAAQ,EAAEb,QAAQ,CAACa,QAAQ,IAAI,IAAI;UACnC;UACAC,cAAc,EAAEgD,MAAM,CAAC9D,QAAQ,CAACc,cAAc,IAAI,GAAG;QACvD,CAAC;QAEDoB,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEuD,UAAU,CAAC;QAElD,IAAI;UACF;UACA7D,OAAO,CAACM,GAAG,CAAC,+BAA+B,EAAET,IAAI,CAACC,SAAS,CAAC+D,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;UAEjF;UACA,MAAM7H,gBAAgB,CAAC+H,YAAY,CAACpH,UAAU,EAAEkH,UAAU,CAAC;UAC3DjH,SAAS,CAAC,4BAA4B,CAAC;;UAEvC;UACA0F,iBAAiB,CAAC,CAAC;;UAEnB;UACAjF,iBAAiB,CAAC,kBAAkB,CAAC;UACrCkC,UAAU,CAAC,CAAC;;UAEZ;UACA,IAAI,OAAOyE,MAAM,KAAK,WAAW,EAAE;YACjC;YACA,MAAMC,KAAK,GAAG,IAAIC,WAAW,CAAC,4BAA4B,EAAE;cAAEtE,MAAM,EAAE;gBAAEjD;cAAW;YAAE,CAAC,CAAC;YACvFqH,MAAM,CAACG,aAAa,CAACF,KAAK,CAAC;UAC7B;QACF,CAAC,CAAC,OAAOvE,KAAK,EAAE;UACdM,OAAO,CAACN,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;;UAEjE;UACA,IAAIC,YAAY,GAAG,0CAA0C;UAE7D,IAAID,KAAK,CAACE,MAAM,EAAE;YAChBD,YAAY,GAAG,OAAOD,KAAK,CAACE,MAAM,KAAK,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACE,MAAM;UAC/F,CAAC,MAAM,IAAIF,KAAK,CAACK,OAAO,EAAE;YACxBJ,YAAY,GAAGD,KAAK,CAACK,OAAO;UAC9B;;UAEA;UACAC,OAAO,CAACN,KAAK,CAAC,kBAAkB,EAAEC,YAAY,CAAC;UAC/CK,OAAO,CAACN,KAAK,CAAC,eAAe,EAAEG,IAAI,CAACC,SAAS,CAAC+D,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEnEhH,OAAO,CAAC8C,YAAY,CAAC;UACrB1C,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,MAAM,IAAIO,UAAU,KAAK,gBAAgB,EAAE;QAC1C;QACA,MAAM4G,QAAQ,GAAG1G,cAAc,CAAC2G,SAAS,IAAI3G,cAAc,CAACM,aAAa,IAAIF,QAAQ,CAACE,aAAa;QAEnGgC,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAE8D,QAAQ,CAAC;QAChDpE,OAAO,CAACM,GAAG,CAAC,kBAAkB,EAAExC,QAAQ,CAAC;QAEzC,IAAI;UACF,MAAM8C,QAAQ,GAAG,MAAM5E,gBAAgB,CAACsI,YAAY,CAAC3H,UAAU,EAAEyH,QAAQ,EAAEtG,QAAQ,CAAC;UACpFkC,OAAO,CAACM,GAAG,CAAC,2BAA2B,EAAEM,QAAQ,CAAC;UAClDhE,SAAS,CAAC,gCAAgC,CAAC;QAC7C,CAAC,CAAC,OAAO8C,KAAK,EAAE;UACdM,OAAO,CAACN,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;UAChE,IAAIC,YAAY,GAAG,yCAAyC;UAC5D,IAAID,KAAK,CAACE,MAAM,EAAE;YAChBD,YAAY,GAAG,OAAOD,KAAK,CAACE,MAAM,KAAK,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACE,MAAM;UAC/F,CAAC,MAAM,IAAIF,KAAK,CAACK,OAAO,EAAE;YACxBJ,YAAY,GAAGD,KAAK,CAACK,OAAO;UAC9B;UACAlD,OAAO,CAAC8C,YAAY,CAAC;UACrB1C,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,MAAM,IAAIO,UAAU,KAAK,eAAe,EAAE;QACzC;QACA,MAAM4G,QAAQ,GAAG1G,cAAc,CAAC2G,SAAS,IAAI3G,cAAc,CAACM,aAAa;QACzE,MAAM4C,QAAQ,GAAG,MAAM5E,gBAAgB,CAACuI,YAAY,CAAC5H,UAAU,EAAEyH,QAAQ,CAAC;;QAE1E;QACA,IAAIxD,QAAQ,CAAC4D,cAAc,EAAE;UAC3BxE,OAAO,CAACM,GAAG,CAAC,0CAA0C3D,UAAU,8BAA8B,CAAC;UAC/FC,SAAS,CAAC,2DAA2D,CAAC;;UAEtE;UACAwF,UAAU,CAAC,MAAM;YACf;YACA/E,iBAAiB,CAAC,YAAY,CAAC;YAC/B;YACAiC,mBAAmB,CAAC,IAAI,CAAC;YACzBF,mBAAmB,CAAC,IAAI,CAAC;UAC3B,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACLxC,SAAS,CAAC,+BAA+B,CAAC;QAC5C;MACF;MAEA0F,iBAAiB,CAAC,CAAC;MACnB/C,UAAU,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,IAAIC,YAAY,GAAG,8BAA8B;MACjD,IAAID,KAAK,CAACE,MAAM,EAAE;QAChBD,YAAY,IAAI,IAAI,IAAI,OAAOD,KAAK,CAACE,MAAM,KAAK,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACE,MAAM,CAAC;MACzG,CAAC,MAAM,IAAIF,KAAK,CAACK,OAAO,EAAE;QACxBJ,YAAY,IAAI,IAAI,GAAGD,KAAK,CAACK,OAAO;MACtC,CAAC,MAAM;QACLJ,YAAY,IAAI,sBAAsB;MACxC;MACA9C,OAAO,CAAC8C,YAAY,CAAC;MACrBK,OAAO,CAACN,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRzC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwH,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,oBACEhI,OAAA,CAACP,qBAAqB;MACpBgB,MAAM,EAAEA,MAAO;MACfF,OAAO,EAAEA,OAAQ;MACjB0H,oBAAoB,EAAGC,YAAY,IAAK3E,OAAO,CAACM,GAAG,CAAC,kBAAkB,EAAEqE,YAAY,CAACxD,MAAM,CAAE;MAC7FyD,MAAM,EAAGpC,MAAM,IAAK;QAClB7E,iBAAiB,CAAC6E,MAAM,CAAC;QACzB/E,aAAa,CAAC,gBAAgB,CAAC;QAC/BF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAE;MACFsH,QAAQ,EAAGrC,MAAM,IAAK;QACpB7E,iBAAiB,CAAC6E,MAAM,CAAC;QACzB/E,aAAa,CAAC,eAAe,CAAC;QAC9BF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAE;MACFuH,aAAa,EAAGtC,MAAM,IAAK;QACzB7E,iBAAiB,CAAC6E,MAAM,CAAC;QACzBvC,mBAAmB,CAAC,CAAC;QACrBxC,aAAa,CAAC,mBAAmB,CAAC;QAClCF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAE;MACFwH,UAAU,EAAGvC,MAAM,IAAK;QACtB7E,iBAAiB,CAAC6E,MAAM,CAAC;QACzB3E,qBAAqB,CAAC,IAAI,CAAC;MAC7B;IAAE;MAAAmH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEN,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI5H,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;MAClE,oBACEf,OAAA,CAAChD,MAAM;QAAC4L,IAAI,EAAE/H,UAAW;QAACgI,OAAO,EAAEhD,iBAAkB;QAACiD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EhJ,OAAA,CAAC/C,WAAW;UAAA+L,QAAA,EACTjI,UAAU,KAAK,YAAY,GAAG,mBAAmB,GAAG;QAAiB;UAAAwH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACd1I,OAAA,CAAC9C,aAAa;UAAA8L,QAAA,GACXjI,UAAU,KAAK,gBAAgB,iBAC9Bf,OAAA,CAACjC,KAAK;YAACkL,QAAQ,EAAC,MAAM;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBAC1ChJ,OAAA,CAACvD,UAAU;cAAC4M,OAAO,EAAC,WAAW;cAACC,UAAU,EAAC,MAAM;cAAAN,QAAA,EAAC;YAElD;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1I,OAAA;cAAAgJ,QAAA,gBACEhJ,OAAA;gBAAAgJ,QAAA,EAAI;cAA+C;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxD1I,OAAA;gBAAAgJ,QAAA,EAAI;cAAiD;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1D1I,OAAA;gBAAAgJ,QAAA,EAAI;cAAgF;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eACL1I,OAAA,CAACxD,GAAG;cAAC0M,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,eACjBhJ,OAAA,CAACvD,UAAU;gBAAC4M,OAAO,EAAC,OAAO;gBAAAL,QAAA,gBACzBhJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAc;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,CAAAzH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEa,YAAY,KAAI,KAAK;cAAA;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,EAEAa,MAAM,CAACC,IAAI,CAAClH,YAAY,CAAC,CAACoC,MAAM,GAAG,CAAC,iBACnC1E,OAAA,CAACjC,KAAK;YAACkL,QAAQ,EAAC,SAAS;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBAC7ChJ,OAAA,CAACvD,UAAU;cAAC4M,OAAO,EAAC,WAAW;cAAAL,QAAA,EAAC;YAAW;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxD1I,OAAA;cAAIyJ,KAAK,EAAE;gBAAEC,MAAM,EAAE,CAAC;gBAAEC,WAAW,EAAE;cAAO,CAAE;cAAAX,QAAA,EAC3CO,MAAM,CAACK,MAAM,CAACtH,YAAY,CAAC,CAACyC,GAAG,CAAC,CAAC6B,OAAO,EAAEiD,KAAK,kBAC9C7J,OAAA;gBAAAgJ,QAAA,EAAiBpC;cAAO,GAAfiD,KAAK;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACR,eAED1I,OAAA,CAACpD,IAAI;YAACkN,SAAS;YAACC,OAAO,EAAE,CAAE;YAACb,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACxChJ,OAAA,CAACpD,IAAI;cAACoN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvBhJ,OAAA,CAAC5C,SAAS;gBACRgJ,IAAI,EAAC,eAAe;gBACpB+D,KAAK,EAAC,WAAW;gBACjBpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAEhF,QAAQ,CAACE,aAAc;gBAC9B6I,QAAQ,EAAElE,gBAAiB;gBAC3BmE,QAAQ,EAAEtJ,UAAU,KAAK,gBAAgB,IAAKA,UAAU,KAAK,YAAY,IAAIM,QAAQ,CAACc,cAAc,KAAK,GAAK;gBAC9GmI,QAAQ;gBACRrH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACb,aAAc;gBAClCgJ,UAAU,EAAE;kBACVrB,EAAE,EAAE;oBACFsB,OAAO,EAAEnJ,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,SAAS,GAAG,aAAa;oBACpEmH,UAAU,EAAE;kBACd;gBACF,CAAE;gBACFmB,UAAU,EACRrI,UAAU,CAACb,aAAa,KACvBR,UAAU,KAAK,YAAY,IAAIM,QAAQ,CAACc,cAAc,KAAK,GAAG,GAC3D,iBAAiBjC,UAAU,KAAKmB,QAAQ,CAACE,aAAa,IAAI,EAAE,6BAA6B,GACzFR,UAAU,KAAK,YAAY,IAAIM,QAAQ,CAACc,cAAc,KAAK,GAAG,GAC5D,8DAA8DjC,UAAU,eAAe,GACvF,EAAE,CACT;gBACDwK,IAAI,EAAErJ,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,MAAM,GAAG;cAAO;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP1I,OAAA,CAACpD,IAAI;cAACoN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvBhJ,OAAA,CAAC5C,SAAS;gBACRgJ,IAAI,EAAC,SAAS;gBACd+D,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAEhF,QAAQ,CAACG,OAAQ;gBACxB4I,QAAQ,EAAElE,gBAAiB;gBAC3BoE,QAAQ;gBACRrH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACZ,OAAQ;gBAC5BiJ,UAAU,EAAErI,UAAU,CAACZ,OAAO,IAAI;cAAG;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP1I,OAAA,CAACpD,IAAI;cAACoN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvBhJ,OAAA,CAAC5C,SAAS;gBACRgJ,IAAI,EAAC,WAAW;gBAChB+D,KAAK,EAAC,WAAW;gBACjBpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAEhF,QAAQ,CAACI,SAAU;gBAC1B2I,QAAQ,EAAElE,gBAAiB;gBAC3BoE,QAAQ;gBACRrH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACX,SAAU;gBAC9BgJ,UAAU,EAAErI,UAAU,CAACX,SAAS,IAAI;cAAG;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP1I,OAAA,CAACpD,IAAI;cAACoN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvBhJ,OAAA,CAAC5C,SAAS;gBACRgJ,IAAI,EAAC,cAAc;gBACnB+D,KAAK,EAAC,kBAAe;gBACrBpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAEhF,QAAQ,CAACK,YAAa;gBAC7B0I,QAAQ,EAAElE,gBAAiB;gBAC3BoE,QAAQ;gBACRrH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACV,YAAa;gBACjC+I,UAAU,EAAErI,UAAU,CAACV,YAAY,IAAI;cAAG;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP1I,OAAA,CAACpD,IAAI;cAACoN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvBhJ,OAAA,CAAC5C,SAAS;gBACRgJ,IAAI,EAAC,SAAS;gBACd+D,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAEhF,QAAQ,CAACM,OAAQ;gBACxByI,QAAQ,EAAElE,gBAAiB;gBAC3BoE,QAAQ;gBACRrH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACT,OAAQ;gBAC5B8I,UAAU,EAAErI,UAAU,CAACT,OAAO,IAAI;cAAG;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP1I,OAAA,CAACpD,IAAI;cAACoN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvBhJ,OAAA,CAAC5C,SAAS;gBACRgJ,IAAI,EAAC,cAAc;gBACnB+D,KAAK,EAAC,cAAc;gBACpBO,IAAI,EAAC,QAAQ;gBACb3B,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAEhF,QAAQ,CAACO,YAAa;gBAC7BwI,QAAQ,EAAElE,gBAAiB;gBAC3BoE,QAAQ;gBACRrH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACR,YAAa;gBACjC6I,UAAU,EAAErI,UAAU,CAACR,YAAY,IAAI;cAAG;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACN3H,UAAU,KAAK,gBAAgB,iBAC9Bf,OAAA,CAACpD,IAAI;cAACoN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvBhJ,OAAA,CAAC5C,SAAS;gBACRgJ,IAAI,EAAC,eAAe;gBACpB+D,KAAK,EAAC,eAAe;gBACrBO,IAAI,EAAC,QAAQ;gBACb3B,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAEhF,QAAQ,CAACQ,aAAc;gBAC9BuI,QAAQ,EAAElE,gBAAiB;gBAC3BoE,QAAQ;gBACRD,QAAQ,EAAE,IAAK;gBACfI,UAAU,EAAC;cAA4D;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACP,eACD1I,OAAA,CAACpD,IAAI;cAACoN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvBhJ,OAAA,CAAC3C,WAAW;gBAAC0L,SAAS;gBAAAC,QAAA,gBACpBhJ,OAAA,CAAC1C,UAAU;kBAACqN,EAAE,EAAC,oBAAoB;kBAAA3B,QAAA,EAAC;gBAAY;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7D1I,OAAA,CAACzC,MAAM;kBACLqN,OAAO,EAAC,oBAAoB;kBAC5BxE,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAEhF,QAAQ,CAACS,YAAa;kBAC7BqI,KAAK,EAAC,cAAc;kBACpBC,QAAQ,EAAElE,gBAAiB;kBAC3BmE,QAAQ,EAAEtJ,UAAU,KAAK,YAAa;kBAAAiI,QAAA,gBAEtChJ,OAAA,CAACxC,QAAQ;oBAAC6I,KAAK,EAAC,aAAa;oBAAA2C,QAAA,EAAC;kBAAW;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpD1I,OAAA,CAACxC,QAAQ;oBAAC6I,KAAK,EAAC,QAAQ;oBAAA2C,QAAA,EAAC;kBAAM;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1C1I,OAAA,CAACxC,QAAQ;oBAAC6I,KAAK,EAAC,WAAW;oBAAA2C,QAAA,EAAC;kBAAS;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChD1I,OAAA,CAACxC,QAAQ;oBAAC6I,KAAK,EAAC,aAAa;oBAAA2C,QAAA,EAAC;kBAAW;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpD1I,OAAA,CAACxC,QAAQ;oBAAC6I,KAAK,EAAC,MAAM;oBAAA2C,QAAA,EAAC;kBAAI;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,EACR3H,UAAU,KAAK,YAAY,iBAC1Bf,OAAA,CAAC/B,cAAc;kBAAA+K,QAAA,EAAC;gBAAqD;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgB,CACtF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACP1I,OAAA,CAACpD,IAAI;cAACoN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvBhJ,OAAA,CAAC5C,SAAS;gBACRgJ,IAAI,EAAC,mBAAmB;gBACxB+D,KAAK,EAAC,mBAAmB;gBACzBpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAEhF,QAAQ,CAACU,iBAAkB;gBAClCqI,QAAQ,EAAElE,gBAAiB;gBAC3BjD,KAAK,EAAE,CAAC,CAACb,UAAU,CAACL,iBAAkB;gBACtC0I,UAAU,EAAErI,UAAU,CAACL,iBAAiB,IAAI;cAAG;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP1I,OAAA,CAACpD,IAAI;cAACoN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvBhJ,OAAA,CAAC5C,SAAS;gBACRgJ,IAAI,EAAC,WAAW;gBAChB+D,KAAK,EAAC,WAAW;gBACjBpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAEhF,QAAQ,CAACW,SAAU;gBAC1BoI,QAAQ,EAAElE,gBAAiB;gBAC3BjD,KAAK,EAAE,CAAC,CAACb,UAAU,CAACJ,SAAU;gBAC9ByI,UAAU,EAAErI,UAAU,CAACJ,SAAS,IAAI;cAAG;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP1I,OAAA,CAACpD,IAAI;cAACoN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvBhJ,OAAA,CAAC5C,SAAS;gBACRgJ,IAAI,EAAC,OAAO;gBACZ+D,KAAK,EAAC,YAAY;gBAClBpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAEhF,QAAQ,CAACY,KAAM;gBACtBmI,QAAQ,EAAElE,gBAAiB;gBAC3BjD,KAAK,EAAE,CAAC,CAACb,UAAU,CAACH,KAAM;gBAC1BwI,UAAU,EAAErI,UAAU,CAACH,KAAK,IAAI;cAAG;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP1I,OAAA,CAACpD,IAAI;cAACoN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvBhJ,OAAA,CAAC5C,SAAS;gBACRgJ,IAAI,EAAC,UAAU;gBACf+D,KAAK,EAAC,uBAAuB;gBAC7BpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAEhF,QAAQ,CAACa,QAAS;gBACzBkI,QAAQ,EAAElE,gBAAiB;gBAC3B2E,WAAW,EAAC,YAAY;gBACxB5H,KAAK,EAAE,CAAC,CAACb,UAAU,CAACF,QAAS;gBAC7BuI,UAAU,EAAErI,UAAU,CAACF,QAAQ,IAAI;cAAG;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP1I,OAAA,CAACpD,IAAI;cAACoN,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACvBhJ,OAAA,CAAC5C,SAAS;gBACRgJ,IAAI,EAAC,gBAAgB;gBACrB+D,KAAK,EAAC,yBAAsB;gBAC5BpB,SAAS;gBACTM,OAAO,EAAC,UAAU;gBAClBhD,KAAK,EAAEhF,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,YAAY,GAAG,SAAU;gBAClEoI,UAAU,EAAE;kBACVO,QAAQ,EAAE,IAAI;kBACd5B,EAAE,EAAE;oBACFsB,OAAO,EAAE;kBACX;gBACF,CAAE;gBACFC,UAAU,eACRzK,OAAA,CAACxD,GAAG;kBAAC0M,EAAE,EAAE;oBAAEI,UAAU,EAAE,QAAQ;oBAAEyB,KAAK,EAAE1J,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,cAAc,GAAG;kBAAY,CAAE;kBAAA6G,QAAA,EACtG3H,QAAQ,CAACc,cAAc,KAAK,GAAG,GAC5B,mDAAmD,GACnD;gBAA6D;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAChB1I,OAAA,CAAC7C,aAAa;UAAA6L,QAAA,gBACZhJ,OAAA,CAACtD,MAAM;YAACsO,OAAO,EAAEnF,iBAAkB;YAAAmD,QAAA,EAAC;UAAO;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpD1I,OAAA,CAACtD,MAAM;YACLsO,OAAO,EAAElE,UAAW;YACpBuD,QAAQ,EAAE9J,OAAO,IAAIgJ,MAAM,CAACC,IAAI,CAACpH,UAAU,CAAC,CAACsC,MAAM,GAAG,CAAE;YACxDuG,SAAS,EAAE1K,OAAO,gBAAGP,OAAA,CAAChC,gBAAgB;cAACkN,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG1I,OAAA,CAACd,QAAQ;cAAAqJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnEW,OAAO,EAAC,WAAW;YACnB0B,KAAK,EAAC,SAAS;YAAA/B,QAAA,EAChB;UAED;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI3H,UAAU,KAAK,iBAAiB,EAAE;MAC3C,oBACEf,OAAA,CAAChD,MAAM;QAAC4L,IAAI,EAAE/H,UAAW;QAACgI,OAAO,EAAEhD,iBAAkB;QAACiD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EhJ,OAAA,CAAC/C,WAAW;UAAA+L,QAAA,EAAC;QAA8B;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzD1I,OAAA,CAAC9C,aAAa;UAAA8L,QAAA,EACXzI,OAAO,gBACNP,OAAA,CAAChC,gBAAgB;YAAAuK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBjI,MAAM,CAACiE,MAAM,KAAK,CAAC,gBACrB1E,OAAA,CAACjC,KAAK;YAACkL,QAAQ,EAAC,MAAM;YAAAD,QAAA,EAAC;UAA0B;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEzD1I,OAAA,CAACvC,IAAI;YAAAuL,QAAA,EACFvI,MAAM,CAACsE,GAAG,CAAEgB,MAAM,iBACjB/F,OAAA,CAACtC,QAAQ;cACPyN,MAAM;cAENH,OAAO,EAAEA,CAAA,KAAMlF,kBAAkB,CAACC,MAAM,CAAE;cAAAiD,QAAA,eAE1ChJ,OAAA,CAACrC,YAAY;gBACXyN,OAAO,EAAE,WAAWrF,MAAM,CAACxE,aAAa,EAAG;gBAC3C8J,SAAS,EAAE,cAActF,MAAM,CAACtE,SAAS,IAAI,KAAK,eAAesE,MAAM,CAACvE,OAAO,IAAI,KAAK,eAAeuE,MAAM,CAAClE,aAAa,IAAI,KAAK;cAAK;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC,GANG3C,MAAM,CAACxE,aAAa;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB1I,OAAA,CAAC7C,aAAa;UAAA6L,QAAA,eACZhJ,OAAA,CAACtD,MAAM;YAACsO,OAAO,EAAEnF,iBAAkB;YAAAmD,QAAA,EAAC;UAAO;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI3H,UAAU,KAAK,eAAe,EAAE;MACzC,oBACEf,OAAA,CAAChD,MAAM;QAAC4L,IAAI,EAAE/H,UAAW;QAACgI,OAAO,EAAEhD,iBAAkB;QAACiD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EhJ,OAAA,CAAC/C,WAAW;UAAA+L,QAAA,EAAC;QAAc;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzC1I,OAAA,CAAC9C,aAAa;UAAA8L,QAAA,EACX,CAAC/H,cAAc,GACdV,OAAO,gBACLP,OAAA,CAAChC,gBAAgB;YAAAuK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBjI,MAAM,CAACiE,MAAM,KAAK,CAAC,gBACrB1E,OAAA,CAACjC,KAAK;YAACkL,QAAQ,EAAC,MAAM;YAAAD,QAAA,EAAC;UAA0B;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEzD1I,OAAA,CAACvC,IAAI;YAAAuL,QAAA,EACFvI,MAAM,CAACsE,GAAG,CAAEgB,MAAM,iBACjB/F,OAAA,CAACtC,QAAQ;cACPyN,MAAM;cAENH,OAAO,EAAEA,CAAA,KAAM9J,iBAAiB,CAAC6E,MAAM,CAAE;cAAAiD,QAAA,eAEzChJ,OAAA,CAACrC,YAAY;gBACXyN,OAAO,EAAE,WAAWrF,MAAM,CAACxE,aAAa,EAAG;gBAC3C8J,SAAS,EAAE,cAActF,MAAM,CAACtE,SAAS,IAAI,KAAK,eAAesE,MAAM,CAACvE,OAAO,IAAI,KAAK,eAAeuE,MAAM,CAAClE,aAAa,IAAI,KAAK;cAAK;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC,GANG3C,MAAM,CAACxE,aAAa;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACP,gBAED1I,OAAA,CAACxD,GAAG;YAAAwM,QAAA,gBACFhJ,OAAA,CAACjC,KAAK;cAACkL,QAAQ,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,GAAC,0CACC,EAAC/H,cAAc,CAACM,aAAa,EAAC,GACxE;YAAA;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1I,OAAA,CAACvD,UAAU;cAAC4M,OAAO,EAAC,OAAO;cAACH,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,EAAC;YAE3C;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1I,OAAA,CAACjC,KAAK;cAACkL,QAAQ,EAAC,MAAM;cAACC,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACnChJ,OAAA,CAACvD,UAAU;gBAAC4M,OAAO,EAAC,WAAW;gBAACC,UAAU,EAAC,MAAM;gBAAAN,QAAA,EAAC;cAElD;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb1I,OAAA;gBAAAgJ,QAAA,gBACEhJ,OAAA;kBAAAgJ,QAAA,EAAI;gBAA0E;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnF1I,OAAA;kBAAAgJ,QAAA,EAAI;gBAA+C;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxD1I,OAAA;kBAAAgJ,QAAA,EAAI;gBAAiD;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR1I,OAAA,CAACxD,GAAG;cAAC0M,EAAE,EAAE;gBAAEoC,OAAO,EAAE,MAAM;gBAAEC,aAAa,EAAE,QAAQ;gBAAEC,GAAG,EAAE,CAAC;gBAAErC,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,gBACnEhJ,OAAA,CAACvD,UAAU;gBAAC4M,OAAO,EAAC,OAAO;gBAAAL,QAAA,gBACzBhJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAc;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACzH,cAAc,CAACa,YAAY,IAAI,KAAK;cAAA;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACb1I,OAAA,CAACvD,UAAU;gBAAC4M,OAAO,EAAC,OAAO;gBAAAL,QAAA,gBACzBhJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAa;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACzH,cAAc,CAACW,YAAY,IAAI,KAAK;cAAA;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACb1I,OAAA,CAACvD,UAAU;gBAAC4M,OAAO,EAAC,OAAO;gBAAAL,QAAA,gBACzBhJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAc;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACzH,cAAc,CAACY,aAAa,IAAI,KAAK;cAAA;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB1I,OAAA,CAAC7C,aAAa;UAAA6L,QAAA,gBACZhJ,OAAA,CAACtD,MAAM;YAACsO,OAAO,EAAEnF,iBAAkB;YAAAmD,QAAA,EAAC;UAAO;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDzH,cAAc,iBACbjB,OAAA,CAACtD,MAAM;YACLsO,OAAO,EAAElE,UAAW;YACpBuD,QAAQ,EAAE9J,OAAQ;YAClBwK,KAAK,EAAC,OAAO;YACbE,SAAS,EAAE1K,OAAO,gBAAGP,OAAA,CAAChC,gBAAgB;cAACkN,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG1I,OAAA,CAAClB,UAAU;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAM,QAAA,EACtE;UAED;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI3H,UAAU,KAAK,mBAAmB,EAAE;MAC7C,oBACEf,OAAA,CAAChD,MAAM;QAAC4L,IAAI,EAAE/H,UAAW;QAACgI,OAAO,EAAEhD,iBAAkB;QAACiD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EhJ,OAAA,CAAC/C,WAAW;UAAA+L,QAAA,EAAC;QAAuB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAClD1I,OAAA,CAAC9C,aAAa;UAAA8L,QAAA,EACXzI,OAAO,gBACNP,OAAA,CAAChC,gBAAgB;YAAAuK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBlG,eAAe,CAACkC,MAAM,KAAK,CAAC,gBAC9B1E,OAAA,CAACjC,KAAK;YAACkL,QAAQ,EAAC,MAAM;YAAAD,QAAA,EAAC;UAA+B;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE9D1I,OAAA,CAAC1B,cAAc;YAACmN,SAAS,EAAE9O,KAAM;YAACuM,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,eAC9ChJ,OAAA,CAAC7B,KAAK;cAAC+M,IAAI,EAAC,OAAO;cAAAlC,QAAA,gBACjBhJ,OAAA,CAACzB,SAAS;gBAAAyK,QAAA,eACRhJ,OAAA,CAACxB,QAAQ;kBAAAwK,QAAA,gBACPhJ,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAC;kBAAM;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7B1I,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAC;kBAAO;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9B1I,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAC;kBAAS;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAChC1I,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAC;kBAAa;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpC1I,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAC;kBAAO;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9B1I,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAC;kBAAY;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnC1I,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAC;kBAAa;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpC1I,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAC;kBAAc;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZ1I,OAAA,CAAC5B,SAAS;gBAAA4K,QAAA,EACPxG,eAAe,CAACuC,GAAG,CAAC,CAAC2G,MAAM,EAAE7B,KAAK,kBACjC7J,OAAA,CAACxB,QAAQ;kBAAAwK,QAAA,gBACPhJ,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAE0C,MAAM,CAACnK;kBAAa;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7C1I,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAE0C,MAAM,CAAClK;kBAAO;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvC1I,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAE0C,MAAM,CAACjK;kBAAS;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzC1I,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAE0C,MAAM,CAAChK;kBAAY;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5C1I,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAE0C,MAAM,CAAC/J;kBAAO;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvC1I,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAE0C,MAAM,CAAC9J;kBAAY;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5C1I,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAE0C,MAAM,CAAC7J;kBAAa;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7C1I,OAAA,CAAC3B,SAAS;oBAAA2K,QAAA,EAAE0C,MAAM,CAACC,IAAI,CAACjH;kBAAM;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA,GAR9BmB,KAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASV,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB1I,OAAA,CAAC7C,aAAa;UAAA6L,QAAA,eACZhJ,OAAA,CAACtD,MAAM;YAACsO,OAAO,EAAEnF,iBAAkB;YAAAmD,QAAA,EAAC;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACE1I,OAAA,CAACxD,GAAG;IAAAwM,QAAA,GACDrI,cAAc,KAAK,kBAAkB,IAAI,CAACE,UAAU,gBACnDb,OAAA,CAACrD,KAAK;MAACuM,EAAE,EAAE;QAAE0C,CAAC,EAAE;MAAE,CAAE;MAAA5C,QAAA,gBAClBhJ,OAAA,CAACxD,GAAG;QAAC0M,EAAE,EAAE;UAAEoC,OAAO,EAAE,MAAM;UAAEO,cAAc,EAAE,UAAU;UAAEC,UAAU,EAAE,QAAQ;UAAE3C,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,eACpFhJ,OAAA,CAACxD,GAAG;UAAC0M,EAAE,EAAE;YAAEoC,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAE,CAAE;UAAAxC,QAAA,gBACnChJ,OAAA,CAACtD,MAAM;YACL2M,OAAO,EAAC,UAAU;YAClB0B,KAAK,EAAC,SAAS;YACfE,SAAS,eAAEjL,OAAA,CAAChB,WAAW;cAAAuJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BsC,OAAO,EAAEA,CAAA,KAAM;cACbxH,mBAAmB,CAAC,CAAC;cACrBxC,aAAa,CAAC,mBAAmB,CAAC;cAClCF,aAAa,CAAC,IAAI,CAAC;YACrB,CAAE;YACFoI,EAAE,EAAE;cACFI,UAAU,EAAE,QAAQ;cACpByC,YAAY,EAAE;YAChB,CAAE;YAAA/C,QAAA,EACH;UAED;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1I,OAAA,CAACtD,MAAM;YACL2M,OAAO,EAAC,WAAW;YACnB0B,KAAK,EAAC,SAAS;YACfE,SAAS,eAAEjL,OAAA,CAACtB,OAAO;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBsC,OAAO,EAAEA,CAAA,KAAMjH,qBAAqB,CAAC,CAAE;YACvCmF,EAAE,EAAE;cACFI,UAAU,EAAE,QAAQ;cACpByC,YAAY,EAAE;YAChB,CAAE;YAAA/C,QAAA,EACH;UAED;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACLnI,OAAO,gBACNP,OAAA,CAACxD,GAAG;QAAC0M,EAAE,EAAE;UAAEoC,OAAO,EAAE,MAAM;UAAEO,cAAc,EAAE,QAAQ;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAhD,QAAA,eAC5DhJ,OAAA,CAAChC,gBAAgB;UAAAuK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,gBAEN1I,OAAA,CAACP,qBAAqB;QACpBgB,MAAM,EAAEA,MAAO;QACfF,OAAO,EAAEA,OAAQ;QACjB0H,oBAAoB,EAAGC,YAAY,IAAK3E,OAAO,CAACM,GAAG,CAAC,kBAAkB,EAAEqE,YAAY,CAACxD,MAAM,CAAE;QAC7FyD,MAAM,EAAGpC,MAAM,IAAK;UAClB7E,iBAAiB,CAAC6E,MAAM,CAAC;UACzB/E,aAAa,CAAC,gBAAgB,CAAC;UAC/BF,aAAa,CAAC,IAAI,CAAC;QACrB,CAAE;QACFsH,QAAQ,EAAGrC,MAAM,IAAK;UACpB7E,iBAAiB,CAAC6E,MAAM,CAAC;UACzB/E,aAAa,CAAC,eAAe,CAAC;UAC9BF,aAAa,CAAC,IAAI,CAAC;QACrB,CAAE;QACFuH,aAAa,EAAGtC,MAAM,IAAK;UACzB7E,iBAAiB,CAAC6E,MAAM,CAAC;UACzBvC,mBAAmB,CAAC,CAAC;UACrBxC,aAAa,CAAC,mBAAmB,CAAC;UAClCF,aAAa,CAAC,IAAI,CAAC;QACrB,CAAE;QACFwH,UAAU,EAAGvC,MAAM,IAAK;UACtB7E,iBAAiB,CAAC6E,MAAM,CAAC;UACzB3E,qBAAqB,CAAC,IAAI,CAAC;QAC7B;MAAE;QAAAmH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,CAAC7H,UAAU,gBACbb,OAAA,CAACrD,KAAK;MAACuM,EAAE,EAAE;QAAE0C,CAAC,EAAE,CAAC;QAAEK,SAAS,EAAE,OAAO;QAAEX,OAAO,EAAE,MAAM;QAAEQ,UAAU,EAAE,QAAQ;QAAED,cAAc,EAAE;MAAS,CAAE;MAAA7C,QAAA,EACtG,CAACrI,cAAc,gBACdX,OAAA,CAACvD,UAAU;QAAC4M,OAAO,EAAC,OAAO;QAAAL,QAAA,EAAC;MAE5B;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,gBAEb1I,OAAA,CAACxD,GAAG;QAAC0M,EAAE,EAAE;UAAEgD,SAAS,EAAE;QAAS,CAAE;QAAAlD,QAAA,gBAC/BhJ,OAAA,CAACvD,UAAU;UAAC4M,OAAO,EAAC,IAAI;UAAC8C,YAAY;UAAAnD,QAAA,GAClCrI,cAAc,KAAK,YAAY,IAAI,mBAAmB,EACtDA,cAAc,KAAK,gBAAgB,IAAI,iBAAiB,EACxDA,cAAc,KAAK,eAAe,IAAI,gBAAgB,EACtDA,cAAc,KAAK,mBAAmB,IAAI,6BAA6B;QAAA;UAAA4H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACb1I,OAAA,CAAChC,gBAAgB;UAACkL,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,IAAI,EAEPC,YAAY,CAAC,CAAC,eAGf3I,OAAA,CAACR,oBAAoB;MACnBoJ,IAAI,EAAElG,gBAAiB;MACvBmG,OAAO,EAAEA,CAAA,KAAMlG,mBAAmB,CAAC,KAAK,CAAE;MAC1CyJ,SAAS,EAAE7G;IAAoB;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,eAGF1I,OAAA,CAACN,oBAAoB;MACnBkJ,IAAI,EAAEzH,kBAAmB;MACzB0H,OAAO,EAAEA,CAAA,KAAM;QACbzH,qBAAqB,CAAC,KAAK,CAAC;QAC5B;QACA0B,UAAU,CAAC,CAAC;MACd,CAAE;MACFiD,MAAM,EAAE9E,cAAe;MACvBf,UAAU,EAAEA,UAAW;MACvBC,SAAS,EAAEA,SAAU;MACrBC,OAAO,EAAEA;IAAQ;MAAAmI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACpI,EAAA,CAhrCIL,SAAS;AAAAoM,EAAA,GAATpM,SAAS;AAkrCf,eAAeA,SAAS;AAAC,IAAAoM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}