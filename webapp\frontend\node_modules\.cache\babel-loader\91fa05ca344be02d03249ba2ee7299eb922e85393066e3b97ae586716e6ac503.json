{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\MainMenu.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { List, ListItem, ListItemIcon, ListItemText, Divider, Box, Typography, Collapse, ListItemButton } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon, ExpandLess, ExpandMore, ViewList as ViewListIcon, Engineering as EngineeringIcon, Inventory as InventoryIcon, TableChart as TableChartIcon, Assessment as AssessmentIcon, VerifiedUser as VerifiedUserIcon, ShoppingCart as ShoppingCartIcon, Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MainMenu = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n\n  // Funzione di utilità per creare ListItemText con dimensione del testo ridotta\n  const createListItemText = (primary, level = 1) => {\n    // Dimensioni del testo in base al livello del menu - ridotte ulteriormente\n    const fontSize = level === 1 ? '0.8rem' : level === 2 ? '0.75rem' : '0.7rem';\n    return /*#__PURE__*/_jsxDEV(ListItemText, {\n      primary: primary,\n      primaryTypographyProps: {\n        fontSize\n      },\n      sx: {\n        my: 0\n      } // Riduce il margine verticale\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Stati per i menu a cascata\n  const [openCaviMenu, setOpenCaviMenu] = useState(false);\n  const [openCantieriMenu, setOpenCantieriMenu] = useState(false);\n  const [openAdminMenu, setOpenAdminMenu] = useState(false);\n  const [openPosaMenu, setOpenPosaMenu] = useState(false);\n  const [openParcoMenu, setOpenParcoMenu] = useState(false);\n  const [openExcelMenu, setOpenExcelMenu] = useState(false);\n  const [openReportMenu, setOpenReportMenu] = useState(false);\n  const [openCertificazioneMenu, setOpenCertificazioneMenu] = useState(false);\n  const [openComandeMenu, setOpenComandeMenu] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = path => {\n    return location.pathname.startsWith(path);\n  };\n\n  // Gestisce l'apertura/chiusura dei menu a cascata\n  const handleToggleCaviMenu = () => {\n    setOpenCaviMenu(!openCaviMenu);\n  };\n  const handleToggleCantieriMenu = () => {\n    setOpenCantieriMenu(!openCantieriMenu);\n  };\n  const handleToggleAdminMenu = () => {\n    setOpenAdminMenu(!openAdminMenu);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      console.log('Utente impersonato, reindirizzamento al menu amministratore');\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(List, {\n    dense: true,\n    sx: {\n      '& .MuiListItemButton-root': {\n        py: 0.3,\n        minHeight: '32px'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n      selected: isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard'),\n      onClick: () => navigateTo('/dashboard'),\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(HomeIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), createListItemText(isImpersonating ? \"Torna al Menu Admin\" : \"Home\", 1)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), (user === null || user === void 0 ? void 0 : user.role) === 'owner' && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: handleToggleAdminMenu,\n        selected: isPartOfActive('/dashboard/admin'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(AdminIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 13\n        }, this), createListItemText(\"Amministrazione\", 1), openAdminMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 30\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 64\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n        in: openAdminMenu,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            selected: isActive('/dashboard/admin'),\n            onClick: () => navigateTo('/dashboard/admin'),\n            children: createListItemText(\"Pannello Admin\", 2)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), ((user === null || user === void 0 ? void 0 : user.role) !== 'owner' || (user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this), isImpersonating && impersonatedUser && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 1,\n          bgcolor: 'rgba(255, 165, 0, 0.1)',\n          borderLeft: '4px solid orange'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"textSecondary\",\n          children: \"Accesso come utente:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          fontWeight: \"bold\",\n          children: impersonatedUser.username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: handleToggleCantieriMenu,\n        selected: isPartOfActive('/dashboard/cantieri'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(ConstructionIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this), createListItemText(isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\", 1), openCantieriMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 33\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 67\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n        in: openCantieriMenu,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            selected: isActive('/dashboard/cantieri'),\n            onClick: () => navigateTo('/dashboard/cantieri'),\n            children: createListItemText(\"Lista Cantieri\", 2)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n              sx: {\n                pl: 2\n              },\n              selected: isActive(`/dashboard/cantieri/${selectedCantiereId}`),\n              onClick: () => navigateTo(`/dashboard/cantieri/${selectedCantiereId}`),\n              children: createListItemText(`Cantiere: ${selectedCantiereName || selectedCantiereId}`, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 19\n            }, this)\n          }, void 0, false)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(ListItemButton, {\n        onClick: handleToggleCaviMenu,\n        selected: isPartOfActive('/dashboard/cavi'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(CableIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 15\n        }, this), createListItemText(`Gestione Cavi (${selectedCantiereName || selectedCantiereId})`, 1), openCaviMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 65\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 13\n      }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(Collapse, {\n        in: openCaviMenu,\n        timeout: \"auto\",\n        unmountOnExit: true,\n        children: /*#__PURE__*/_jsxDEV(List, {\n          component: \"div\",\n          disablePadding: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            selected: isActive('/dashboard/cavi/visualizza'),\n            onClick: () => navigateTo('/dashboard/cavi/visualizza'),\n            children: createListItemText(\"Visualizza Cavi\", 2)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            onClick: () => setOpenPosaMenu(!openPosaMenu),\n            selected: isPartOfActive('/dashboard/cavi/posa'),\n            children: [createListItemText(\"Posa e Collegamenti\", 2), openPosaMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 35\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 69\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openPosaMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/posa/inserisci-metri'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/inserisci-metri'),\n                children: createListItemText(\"Inserisci metri posati\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/posa/modifica-cavo'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/modifica-cavo'),\n                children: createListItemText(\"Modifica cavo\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/posa/aggiungi-cavo'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/aggiungi-cavo'),\n                children: createListItemText(\"Aggiungi nuovo cavo\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/posa/elimina-cavo'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/elimina-cavo'),\n                children: createListItemText(\"Elimina cavo\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/posa/modifica-bobina'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/modifica-bobina'),\n                children: createListItemText(\"Modifica bobina cavo posato\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/posa/collegamenti'),\n                onClick: () => navigateTo('/dashboard/cavi/posa/collegamenti'),\n                children: createListItemText(\"Gestisci collegamenti cavo\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            onClick: () => setOpenParcoMenu(!openParcoMenu),\n            selected: isPartOfActive('/dashboard/cavi/parco'),\n            children: [createListItemText(\"Parco Cavi\", 2), openParcoMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 70\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openParcoMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/parco/visualizza'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/visualizza'),\n                children: createListItemText(\"Visualizza Bobine\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/parco/crea'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/crea'),\n                children: createListItemText(\"Crea Nuova Bobina\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/parco/modifica'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/modifica'),\n                children: createListItemText(\"Modifica Bobina\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/parco/elimina'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/elimina'),\n                children: createListItemText(\"Elimina Bobina\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/parco/storico'),\n                onClick: () => navigateTo('/dashboard/cavi/parco/storico'),\n                children: createListItemText(\"Storico Utilizzo\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            onClick: () => setOpenExcelMenu(!openExcelMenu),\n            selected: isPartOfActive('/dashboard/cavi/excel'),\n            children: [createListItemText(\"Gestione Excel\", 2), openExcelMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 70\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openExcelMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/excel/importa-cavi'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/importa-cavi'),\n                children: createListItemText(\"Importa cavi da Excel\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/importa-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/importa-bobine'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Importa parco bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/template-cavi'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/template-cavi'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(TableChartIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Template Excel per cavi\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/template-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/template-bobine'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(TableChartIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Template Excel per bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/esporta-cavi'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/esporta-cavi'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(TableChartIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Esporta cavi in Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/excel/esporta-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/excel/esporta-bobine'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(TableChartIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Esporta bobine in Excel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            onClick: () => setOpenReportMenu(!openReportMenu),\n            selected: isPartOfActive('/dashboard/cavi/report'),\n            children: [createListItemText(\"Report\", 2), openReportMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 37\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 71\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openReportMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/report/avanzamento'),\n                onClick: () => navigateTo('/dashboard/cavi/report/avanzamento'),\n                children: createListItemText(\"Report Avanzamento\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/report/boq'),\n                onClick: () => navigateTo('/dashboard/cavi/report/boq'),\n                children: createListItemText(\"Bill of Quantities\", 3)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/report/utilizzo-bobine'),\n                onClick: () => navigateTo('/dashboard/cavi/report/utilizzo-bobine'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Report Utilizzo Bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/report/posa-periodo'),\n                onClick: () => navigateTo('/dashboard/cavi/report/posa-periodo'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Report Posa per Periodo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 2\n            },\n            onClick: () => setOpenCertificazioneMenu(!openCertificazioneMenu),\n            selected: isPartOfActive('/dashboard/cavi/certificazione'),\n            children: [createListItemText(\"Certificazione Cavi\", 2), openCertificazioneMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 45\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 79\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openCertificazioneMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 3\n                },\n                selected: isActive('/dashboard/cavi/certificazione/visualizza'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/visualizza'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(ViewListIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 496,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Visualizza certificazioni\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/filtra'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/filtra'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(ViewListIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Filtra per cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/crea'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/crea'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Crea certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/dettagli'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/dettagli'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(ViewListIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Dettagli certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/pdf'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/pdf'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Genera PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/elimina'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/elimina'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Elimina certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/certificazione/strumenti'),\n                onClick: () => navigateTo('/dashboard/cavi/certificazione/strumenti'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 562,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Gestione strumenti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            sx: {\n              pl: 4\n            },\n            onClick: () => setOpenComandeMenu(!openComandeMenu),\n            selected: isPartOfActive('/dashboard/cavi/comande'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(ShoppingCartIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Gestione Comande\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 19\n            }, this), openComandeMenu ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 55\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: openComandeMenu,\n            timeout: \"auto\",\n            unmountOnExit: true,\n            children: /*#__PURE__*/_jsxDEV(List, {\n              component: \"div\",\n              disablePadding: true,\n              children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/visualizza'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/visualizza'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(ViewListIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 590,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Visualizza comande\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/crea'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/crea'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Crea nuova comanda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/modifica'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/modifica'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Modifica comanda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/elimina'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/elimina'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 623,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Elimina comanda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/stampa'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/stampa'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 634,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Stampa comanda\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n                sx: {\n                  pl: 6\n                },\n                selected: isActive('/dashboard/cavi/comande/assegna'),\n                onClick: () => navigateTo('/dashboard/cavi/comande/assegna'),\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  children: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Assegna comanda a cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n};\n_s(MainMenu, \"Udz8U7QCjEP5k56hCgUxMe5hqis=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = MainMenu;\nexport default MainMenu;\nvar _c;\n$RefreshReg$(_c, \"MainMenu\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "Box", "Typography", "Collapse", "ListItemButton", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "ExpandLess", "ExpandMore", "ViewList", "ViewListIcon", "Engineering", "EngineeringIcon", "Inventory", "InventoryIcon", "Table<PERSON>hart", "TableChartIcon", "Assessment", "AssessmentIcon", "VerifiedUser", "VerifiedUserIcon", "ShoppingCart", "ShoppingCartIcon", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MainMenu", "_s", "navigate", "location", "user", "isImpersonating", "impersonated<PERSON><PERSON>", "createListItemText", "primary", "level", "fontSize", "primaryTypographyProps", "sx", "my", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "openCaviMenu", "setOpenCaviMenu", "openCantieriMenu", "setOpenCantieriMenu", "openAdminMenu", "setOpenAdminMenu", "openPosaMenu", "setOpenPosaMenu", "openParcoMenu", "setOpenParcoMenu", "openExcelMenu", "setOpenExcelMenu", "openReportMenu", "setOpenReportMenu", "openCertificazioneMenu", "setOpenCertificazioneMenu", "openComandeMenu", "setOpenComandeMenu", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "isActive", "path", "pathname", "isPartOfActive", "startsWith", "handleToggleCaviMenu", "handleToggleCantieriMenu", "handleToggleAdminMenu", "navigateTo", "console", "log", "dense", "py", "minHeight", "children", "selected", "onClick", "role", "in", "timeout", "unmountOnExit", "component", "disablePadding", "pl", "p", "bgcolor", "borderLeft", "variant", "color", "fontWeight", "username", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/MainMenu.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Box,\n  Typography,\n  Collapse,\n  ListItemButton\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon,\n  ExpandLess,\n  ExpandMore,\n  ViewList as ViewListIcon,\n  Engineering as EngineeringIcon,\n  Inventory as InventoryIcon,\n  TableChart as TableChartIcon,\n  Assessment as AssessmentIcon,\n  VerifiedUser as VerifiedUserIcon,\n  ShoppingCart as ShoppingCartIcon,\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\n\nconst MainMenu = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, isImpersonating, impersonatedUser } = useAuth();\n\n  // Funzione di utilità per creare ListItemText con dimensione del testo ridotta\n  const createListItemText = (primary, level = 1) => {\n    // Dimensioni del testo in base al livello del menu - ridotte ulteriormente\n    const fontSize = level === 1 ? '0.8rem' : level === 2 ? '0.75rem' : '0.7rem';\n    return (\n      <ListItemText\n        primary={primary}\n        primaryTypographyProps={{ fontSize }}\n        sx={{ my: 0 }} // Riduce il margine verticale\n      />\n    );\n  };\n\n  // Stati per i menu a cascata\n  const [openCaviMenu, setOpenCaviMenu] = useState(false);\n  const [openCantieriMenu, setOpenCantieriMenu] = useState(false);\n  const [openAdminMenu, setOpenAdminMenu] = useState(false);\n  const [openPosaMenu, setOpenPosaMenu] = useState(false);\n  const [openParcoMenu, setOpenParcoMenu] = useState(false);\n  const [openExcelMenu, setOpenExcelMenu] = useState(false);\n  const [openReportMenu, setOpenReportMenu] = useState(false);\n  const [openCertificazioneMenu, setOpenCertificazioneMenu] = useState(false);\n  const [openComandeMenu, setOpenComandeMenu] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  // Gestisce l'apertura/chiusura dei menu a cascata\n  const handleToggleCaviMenu = () => {\n    setOpenCaviMenu(!openCaviMenu);\n  };\n\n  const handleToggleCantieriMenu = () => {\n    setOpenCantieriMenu(!openCantieriMenu);\n  };\n\n  const handleToggleAdminMenu = () => {\n    setOpenAdminMenu(!openAdminMenu);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      console.log('Utente impersonato, reindirizzamento al menu amministratore');\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n  };\n\n  return (\n    <List dense sx={{ '& .MuiListItemButton-root': { py: 0.3, minHeight: '32px' } }}>\n      {/* Home - Se l'utente è un amministratore che sta impersonando un utente, mostra \"Torna al Menu Admin\" */}\n      <ListItemButton\n        selected={isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard')}\n        onClick={() => navigateTo('/dashboard')}\n      >\n        <ListItemIcon>\n          <HomeIcon fontSize=\"small\" />\n        </ListItemIcon>\n        {createListItemText(isImpersonating ? \"Torna al Menu Admin\" : \"Home\", 1)}\n      </ListItemButton>\n\n      {/* Menu Amministratore (solo per admin) */}\n      {user?.role === 'owner' && (\n        <>\n          <Divider />\n          <ListItemButton\n            onClick={handleToggleAdminMenu}\n            selected={isPartOfActive('/dashboard/admin')}\n          >\n            <ListItemIcon>\n              <AdminIcon fontSize=\"small\" />\n            </ListItemIcon>\n            {createListItemText(\"Amministrazione\", 1)}\n            {openAdminMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n          </ListItemButton>\n          <Collapse in={openAdminMenu} timeout=\"auto\" unmountOnExit>\n            <List component=\"div\" disablePadding>\n              <ListItemButton\n                sx={{ pl: 2 }}\n                selected={isActive('/dashboard/admin')}\n                onClick={() => navigateTo('/dashboard/admin')}\n              >\n\n                {createListItemText(\"Pannello Admin\", 2)}\n              </ListItemButton>\n              {/* Altri sottomenu admin possono essere aggiunti qui */}\n            </List>\n          </Collapse>\n        </>\n      )}\n\n      {/* Menu per utenti standard e cantieri */}\n      {/* Mostra per utenti standard/cantiere o per admin che sta impersonando un utente */}\n      {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (\n        <>\n          <Divider />\n          {isImpersonating && impersonatedUser && (\n            <Box sx={{ p: 1, bgcolor: 'rgba(255, 165, 0, 0.1)', borderLeft: '4px solid orange' }}>\n              <Typography variant=\"caption\" color=\"textSecondary\">\n                Accesso come utente:\n              </Typography>\n              <Typography variant=\"caption\" fontWeight=\"bold\">\n                {impersonatedUser.username}\n              </Typography>\n            </Box>\n          )}\n\n          {/* Menu Cantieri con sottomenu */}\n          <ListItemButton\n            onClick={handleToggleCantieriMenu}\n            selected={isPartOfActive('/dashboard/cantieri')}\n          >\n            <ListItemIcon>\n              <ConstructionIcon fontSize=\"small\" />\n            </ListItemIcon>\n            {createListItemText(isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\", 1)}\n            {openCantieriMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n          </ListItemButton>\n          <Collapse in={openCantieriMenu} timeout=\"auto\" unmountOnExit>\n            <List component=\"div\" disablePadding>\n              <ListItemButton\n                sx={{ pl: 4 }}\n                selected={isActive('/dashboard/cantieri')}\n                onClick={() => navigateTo('/dashboard/cantieri')}\n              >\n\n                {createListItemText(\"Lista Cantieri\", 2)}\n              </ListItemButton>\n\n              {/* Mostra il cantiere selezionato se presente */}\n              {selectedCantiereId && (\n                <>\n                  <ListItemButton\n                    sx={{ pl: 2 }}\n                    selected={isActive(`/dashboard/cantieri/${selectedCantiereId}`)}\n                    onClick={() => navigateTo(`/dashboard/cantieri/${selectedCantiereId}`)}\n                  >\n\n                    {createListItemText(`Cantiere: ${selectedCantiereName || selectedCantiereId}`, 2)}\n                  </ListItemButton>\n                </>\n              )}\n            </List>\n          </Collapse>\n\n          {/* Menu Cavi con sottomenu - visibile solo se un cantiere è selezionato */}\n          {selectedCantiereId && (\n            <ListItemButton\n              onClick={handleToggleCaviMenu}\n              selected={isPartOfActive('/dashboard/cavi')}\n            >\n              <ListItemIcon>\n                <CableIcon fontSize=\"small\" />\n              </ListItemIcon>\n              {createListItemText(`Gestione Cavi (${selectedCantiereName || selectedCantiereId})`, 1)}\n              {openCaviMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n            </ListItemButton>\n          )}\n\n          {selectedCantiereId && (\n            <Collapse in={openCaviMenu} timeout=\"auto\" unmountOnExit>\n              <List component=\"div\" disablePadding>\n                <ListItemButton\n                  sx={{ pl: 4 }}\n                  selected={isActive('/dashboard/cavi/visualizza')}\n                  onClick={() => navigateTo('/dashboard/cavi/visualizza')}\n                >\n\n                  {createListItemText(\"Visualizza Cavi\", 2)}\n                </ListItemButton>\n\n                {/* Posa e Collegamenti con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 2 }}\n                  onClick={() => setOpenPosaMenu(!openPosaMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/posa')}\n                >\n\n                  {createListItemText(\"Posa e Collegamenti\", 2)}\n                  {openPosaMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n                </ListItemButton>\n\n                <Collapse in={openPosaMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/posa/inserisci-metri')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/inserisci-metri')}\n                    >\n\n                      {createListItemText(\"Inserisci metri posati\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/posa/modifica-cavo')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/modifica-cavo')}\n                    >\n                      {createListItemText(\"Modifica cavo\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/posa/aggiungi-cavo')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/aggiungi-cavo')}\n                    >\n                      {createListItemText(\"Aggiungi nuovo cavo\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/posa/elimina-cavo')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/elimina-cavo')}\n                    >\n                      {createListItemText(\"Elimina cavo\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/posa/modifica-bobina')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/modifica-bobina')}\n                    >\n                      {createListItemText(\"Modifica bobina cavo posato\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/posa/collegamenti')}\n                      onClick={() => navigateTo('/dashboard/cavi/posa/collegamenti')}\n                    >\n                      {createListItemText(\"Gestisci collegamenti cavo\", 3)}\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Parco Cavi con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 2 }}\n                  onClick={() => setOpenParcoMenu(!openParcoMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/parco')}\n                >\n\n                  {createListItemText(\"Parco Cavi\", 2)}\n                  {openParcoMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n                </ListItemButton>\n\n                <Collapse in={openParcoMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/parco/visualizza')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/visualizza')}\n                    >\n                      {createListItemText(\"Visualizza Bobine\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/parco/crea')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/crea')}\n                    >\n                      {createListItemText(\"Crea Nuova Bobina\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/parco/modifica')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/modifica')}\n                    >\n                      {createListItemText(\"Modifica Bobina\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/parco/elimina')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/elimina')}\n                    >\n                      {createListItemText(\"Elimina Bobina\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/parco/storico')}\n                      onClick={() => navigateTo('/dashboard/cavi/parco/storico')}\n                    >\n                      {createListItemText(\"Storico Utilizzo\", 3)}\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Gestione Excel con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 2 }}\n                  onClick={() => setOpenExcelMenu(!openExcelMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/excel')}\n                >\n\n                  {createListItemText(\"Gestione Excel\", 2)}\n                  {openExcelMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n                </ListItemButton>\n\n                <Collapse in={openExcelMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/excel/importa-cavi')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/importa-cavi')}\n                    >\n                      {createListItemText(\"Importa cavi da Excel\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/importa-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/importa-bobine')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Importa parco bobine\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/template-cavi')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/template-cavi')}\n                    >\n                      <ListItemIcon>\n                        <TableChartIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Template Excel per cavi\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/template-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/template-bobine')}\n                    >\n                      <ListItemIcon>\n                        <TableChartIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Template Excel per bobine\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/esporta-cavi')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/esporta-cavi')}\n                    >\n                      <ListItemIcon>\n                        <TableChartIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Esporta cavi in Excel\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/excel/esporta-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/excel/esporta-bobine')}\n                    >\n                      <ListItemIcon>\n                        <TableChartIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Esporta bobine in Excel\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Report con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 2 }}\n                  onClick={() => setOpenReportMenu(!openReportMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/report')}\n                >\n\n                  {createListItemText(\"Report\", 2)}\n                  {openReportMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n                </ListItemButton>\n\n                <Collapse in={openReportMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/report/avanzamento')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/avanzamento')}\n                    >\n                      {createListItemText(\"Report Avanzamento\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/report/boq')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/boq')}\n                    >\n                      {createListItemText(\"Bill of Quantities\", 3)}\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/report/utilizzo-bobine')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/utilizzo-bobine')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Report Utilizzo Bobine\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/report/posa-periodo')}\n                      onClick={() => navigateTo('/dashboard/cavi/report/posa-periodo')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Report Posa per Periodo\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Certificazione Cavi con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 2 }}\n                  onClick={() => setOpenCertificazioneMenu(!openCertificazioneMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/certificazione')}\n                >\n\n                  {createListItemText(\"Certificazione Cavi\", 2)}\n                  {openCertificazioneMenu ? <ExpandLess fontSize=\"small\" /> : <ExpandMore fontSize=\"small\" />}\n                </ListItemButton>\n\n                <Collapse in={openCertificazioneMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 3 }}\n                      selected={isActive('/dashboard/cavi/certificazione/visualizza')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/visualizza')}\n                    >\n                      <ListItemIcon>\n                        <ViewListIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Visualizza certificazioni\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/filtra')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/filtra')}\n                    >\n                      <ListItemIcon>\n                        <ViewListIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Filtra per cavo\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/crea')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/crea')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Crea certificazione\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/dettagli')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/dettagli')}\n                    >\n                      <ListItemIcon>\n                        <ViewListIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Dettagli certificazione\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/pdf')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/pdf')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Genera PDF\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/elimina')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/elimina')}\n                    >\n                      <ListItemIcon>\n                        <DeleteIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Elimina certificazione\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/certificazione/strumenti')}\n                      onClick={() => navigateTo('/dashboard/cavi/certificazione/strumenti')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Gestione strumenti\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n\n                {/* Gestione Comande con sottomenu */}\n                <ListItemButton\n                  sx={{ pl: 4 }}\n                  onClick={() => setOpenComandeMenu(!openComandeMenu)}\n                  selected={isPartOfActive('/dashboard/cavi/comande')}\n                >\n                  <ListItemIcon>\n                    <ShoppingCartIcon />\n                  </ListItemIcon>\n                  <ListItemText primary=\"Gestione Comande\" />\n                  {openComandeMenu ? <ExpandLess /> : <ExpandMore />}\n                </ListItemButton>\n\n                <Collapse in={openComandeMenu} timeout=\"auto\" unmountOnExit>\n                  <List component=\"div\" disablePadding>\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/visualizza')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/visualizza')}\n                    >\n                      <ListItemIcon>\n                        <ViewListIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Visualizza comande\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/crea')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/crea')}\n                    >\n                      <ListItemIcon>\n                        <AddIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Crea nuova comanda\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/modifica')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/modifica')}\n                    >\n                      <ListItemIcon>\n                        <EditIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Modifica comanda\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/elimina')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/elimina')}\n                    >\n                      <ListItemIcon>\n                        <DeleteIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Elimina comanda\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/stampa')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/stampa')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Stampa comanda\" />\n                    </ListItemButton>\n\n                    <ListItemButton\n                      sx={{ pl: 6 }}\n                      selected={isActive('/dashboard/cavi/comande/assegna')}\n                      onClick={() => navigateTo('/dashboard/cavi/comande/assegna')}\n                    >\n                      <ListItemIcon>\n                        <AssessmentIcon fontSize=\"small\" />\n                      </ListItemIcon>\n                      <ListItemText primary=\"Assegna comanda a cavo\" />\n                    </ListItemButton>\n                  </List>\n                </Collapse>\n              </List>\n            </Collapse>\n          )}\n        </>\n      )}\n    </List>\n  );\n};\n\nexport default MainMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,UAAU,EACVC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,UAAU,EACVC,UAAU,EACVC,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,eAAe,EAC9BC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,YAAY,IAAIC,gBAAgB,EAChCC,YAAY,IAAIC,gBAAgB,EAChCC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAC9B,MAAMmD,QAAQ,GAAGlD,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmD,IAAI;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGX,OAAO,CAAC,CAAC;;EAE7D;EACA,MAAMY,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,KAAK,GAAG,CAAC,KAAK;IACjD;IACA,MAAMC,QAAQ,GAAGD,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,QAAQ;IAC5E,oBACEZ,OAAA,CAACxC,YAAY;MACXmD,OAAO,EAAEA,OAAQ;MACjBG,sBAAsB,EAAE;QAAED;MAAS,CAAE;MACrCE,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE,CAAC;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAEN,CAAC;;EAED;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACuE,aAAa,EAAEC,gBAAgB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+E,cAAc,EAAEC,iBAAiB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACmF,eAAe,EAAEC,kBAAkB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAMqF,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEzE;EACA,MAAME,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOtC,QAAQ,CAACuC,QAAQ,KAAKD,IAAI;EACnC,CAAC;;EAED;EACA,MAAME,cAAc,GAAIF,IAAI,IAAK;IAC/B,OAAOtC,QAAQ,CAACuC,QAAQ,CAACE,UAAU,CAACH,IAAI,CAAC;EAC3C,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjC1B,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,MAAM4B,wBAAwB,GAAGA,CAAA,KAAM;IACrCzB,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;EAED,MAAM2B,qBAAqB,GAAGA,CAAA,KAAM;IAClCxB,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,MAAM0B,UAAU,GAAIP,IAAI,IAAK;IAC3BQ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAET,IAAI,EAAE,kBAAkB,EAAEpC,eAAe,EAAE,OAAO,EAAED,IAAI,CAAC;IACvF;IACA;IACA,IAAIqC,IAAI,KAAK,YAAY,IAAIpC,eAAe,EAAE;MAC5C4C,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1EhD,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM;MACLA,QAAQ,CAACuC,IAAI,CAAC;IAChB;EACF,CAAC;EAED,oBACE5C,OAAA,CAAC3C,IAAI;IAACiG,KAAK;IAACvC,EAAE,EAAE;MAAE,2BAA2B,EAAE;QAAEwC,EAAE,EAAE,GAAG;QAAEC,SAAS,EAAE;MAAO;IAAE,CAAE;IAAAC,QAAA,gBAE9EzD,OAAA,CAACnC,cAAc;MACb6F,QAAQ,EAAElD,eAAe,GAAGmC,QAAQ,CAAC,kBAAkB,CAAC,GAAGA,QAAQ,CAAC,YAAY,CAAE;MAClFgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,YAAY,CAAE;MAAAM,QAAA,gBAExCzD,OAAA,CAACzC,YAAY;QAAAkG,QAAA,eACXzD,OAAA,CAACjC,QAAQ;UAAC8C,QAAQ,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,EACdV,kBAAkB,CAACF,eAAe,GAAG,qBAAqB,GAAG,MAAM,EAAE,CAAC,CAAC;IAAA;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,EAGhB,CAAAb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqD,IAAI,MAAK,OAAO,iBACrB5D,OAAA,CAAAE,SAAA;MAAAuD,QAAA,gBACEzD,OAAA,CAACvC,OAAO;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXpB,OAAA,CAACnC,cAAc;QACb8F,OAAO,EAAET,qBAAsB;QAC/BQ,QAAQ,EAAEZ,cAAc,CAAC,kBAAkB,CAAE;QAAAW,QAAA,gBAE7CzD,OAAA,CAACzC,YAAY;UAAAkG,QAAA,eACXzD,OAAA,CAAC/B,SAAS;YAAC4C,QAAQ,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,EACdV,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,CAAC,EACxCe,aAAa,gBAAGzB,OAAA,CAACxB,UAAU;UAACqC,QAAQ,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGpB,OAAA,CAACvB,UAAU;UAACoC,QAAQ,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACjBpB,OAAA,CAACpC,QAAQ;QAACiG,EAAE,EAAEpC,aAAc;QAACqC,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAN,QAAA,eACvDzD,OAAA,CAAC3C,IAAI;UAAC2G,SAAS,EAAC,KAAK;UAACC,cAAc;UAAAR,QAAA,eAClCzD,OAAA,CAACnC,cAAc;YACbkD,EAAE,EAAE;cAAEmD,EAAE,EAAE;YAAE,CAAE;YACdR,QAAQ,EAAEf,QAAQ,CAAC,kBAAkB,CAAE;YACvCgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,kBAAkB,CAAE;YAAAM,QAAA,EAG7C/C,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;UAAC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA,eACX,CACH,EAIA,CAAC,CAAAb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqD,IAAI,MAAK,OAAO,IAAK,CAAArD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqD,IAAI,MAAK,OAAO,IAAIpD,eAAe,IAAIC,gBAAiB,kBACzFT,OAAA,CAAAE,SAAA;MAAAuD,QAAA,gBACEzD,OAAA,CAACvC,OAAO;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACVZ,eAAe,IAAIC,gBAAgB,iBAClCT,OAAA,CAACtC,GAAG;QAACqD,EAAE,EAAE;UAAEoD,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE,wBAAwB;UAAEC,UAAU,EAAE;QAAmB,CAAE;QAAAZ,QAAA,gBACnFzD,OAAA,CAACrC,UAAU;UAAC2G,OAAO,EAAC,SAAS;UAACC,KAAK,EAAC,eAAe;UAAAd,QAAA,EAAC;QAEpD;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpB,OAAA,CAACrC,UAAU;UAAC2G,OAAO,EAAC,SAAS;UAACE,UAAU,EAAC,MAAM;UAAAf,QAAA,EAC5ChD,gBAAgB,CAACgE;QAAQ;UAAAxD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN,eAGDpB,OAAA,CAACnC,cAAc;QACb8F,OAAO,EAAEV,wBAAyB;QAClCS,QAAQ,EAAEZ,cAAc,CAAC,qBAAqB,CAAE;QAAAW,QAAA,gBAEhDzD,OAAA,CAACzC,YAAY;UAAAkG,QAAA,eACXzD,OAAA,CAAC7B,gBAAgB;YAAC0C,QAAQ,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,EACdV,kBAAkB,CAACF,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAACgE,QAAQ,EAAE,GAAG,iBAAiB,EAAE,CAAC,CAAC,EAC3HlD,gBAAgB,gBAAGvB,OAAA,CAACxB,UAAU;UAACqC,QAAQ,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGpB,OAAA,CAACvB,UAAU;UAACoC,QAAQ,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC,eACjBpB,OAAA,CAACpC,QAAQ;QAACiG,EAAE,EAAEtC,gBAAiB;QAACuC,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAN,QAAA,eAC1DzD,OAAA,CAAC3C,IAAI;UAAC2G,SAAS,EAAC,KAAK;UAACC,cAAc;UAAAR,QAAA,gBAClCzD,OAAA,CAACnC,cAAc;YACbkD,EAAE,EAAE;cAAEmD,EAAE,EAAE;YAAE,CAAE;YACdR,QAAQ,EAAEf,QAAQ,CAAC,qBAAqB,CAAE;YAC1CgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,qBAAqB,CAAE;YAAAM,QAAA,EAGhD/C,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;UAAC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC,EAGhBmB,kBAAkB,iBACjBvC,OAAA,CAAAE,SAAA;YAAAuD,QAAA,eACEzD,OAAA,CAACnC,cAAc;cACbkD,EAAE,EAAE;gBAAEmD,EAAE,EAAE;cAAE,CAAE;cACdR,QAAQ,EAAEf,QAAQ,CAAC,uBAAuBJ,kBAAkB,EAAE,CAAE;cAChEoB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,uBAAuBZ,kBAAkB,EAAE,CAAE;cAAAkB,QAAA,EAGtE/C,kBAAkB,CAAC,aAAagC,oBAAoB,IAAIH,kBAAkB,EAAE,EAAE,CAAC;YAAC;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE;UAAC,gBACjB,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGVmB,kBAAkB,iBACjBvC,OAAA,CAACnC,cAAc;QACb8F,OAAO,EAAEX,oBAAqB;QAC9BU,QAAQ,EAAEZ,cAAc,CAAC,iBAAiB,CAAE;QAAAW,QAAA,gBAE5CzD,OAAA,CAACzC,YAAY;UAAAkG,QAAA,eACXzD,OAAA,CAAC3B,SAAS;YAACwC,QAAQ,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,EACdV,kBAAkB,CAAC,kBAAkBgC,oBAAoB,IAAIH,kBAAkB,GAAG,EAAE,CAAC,CAAC,EACtFlB,YAAY,gBAAGrB,OAAA,CAACxB,UAAU;UAACqC,QAAQ,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGpB,OAAA,CAACvB,UAAU;UAACoC,QAAQ,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CACjB,EAEAmB,kBAAkB,iBACjBvC,OAAA,CAACpC,QAAQ;QAACiG,EAAE,EAAExC,YAAa;QAACyC,OAAO,EAAC,MAAM;QAACC,aAAa;QAAAN,QAAA,eACtDzD,OAAA,CAAC3C,IAAI;UAAC2G,SAAS,EAAC,KAAK;UAACC,cAAc;UAAAR,QAAA,gBAClCzD,OAAA,CAACnC,cAAc;YACbkD,EAAE,EAAE;cAAEmD,EAAE,EAAE;YAAE,CAAE;YACdR,QAAQ,EAAEf,QAAQ,CAAC,4BAA4B,CAAE;YACjDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,4BAA4B,CAAE;YAAAM,QAAA,EAGvD/C,kBAAkB,CAAC,iBAAiB,EAAE,CAAC;UAAC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAGjBpB,OAAA,CAACnC,cAAc;YACbkD,EAAE,EAAE;cAAEmD,EAAE,EAAE;YAAE,CAAE;YACdP,OAAO,EAAEA,CAAA,KAAM/B,eAAe,CAAC,CAACD,YAAY,CAAE;YAC9C+B,QAAQ,EAAEZ,cAAc,CAAC,sBAAsB,CAAE;YAAAW,QAAA,GAGhD/C,kBAAkB,CAAC,qBAAqB,EAAE,CAAC,CAAC,EAC5CiB,YAAY,gBAAG3B,OAAA,CAACxB,UAAU;cAACqC,QAAQ,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpB,OAAA,CAACvB,UAAU;cAACoC,QAAQ,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAEjBpB,OAAA,CAACpC,QAAQ;YAACiG,EAAE,EAAElC,YAAa;YAACmC,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAN,QAAA,eACtDzD,OAAA,CAAC3C,IAAI;cAAC2G,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAR,QAAA,gBAClCzD,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,sCAAsC,CAAE;gBAC3DgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,sCAAsC,CAAE;gBAAAM,QAAA,EAGjE/C,kBAAkB,CAAC,wBAAwB,EAAE,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,oCAAoC,CAAE;gBACzDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,oCAAoC,CAAE;gBAAAM,QAAA,EAE/D/C,kBAAkB,CAAC,eAAe,EAAE,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,oCAAoC,CAAE;gBACzDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,oCAAoC,CAAE;gBAAAM,QAAA,EAE/D/C,kBAAkB,CAAC,qBAAqB,EAAE,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,mCAAmC,CAAE;gBACxDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,mCAAmC,CAAE;gBAAAM,QAAA,EAE9D/C,kBAAkB,CAAC,cAAc,EAAE,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,sCAAsC,CAAE;gBAC3DgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,sCAAsC,CAAE;gBAAAM,QAAA,EAEjE/C,kBAAkB,CAAC,6BAA6B,EAAE,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,mCAAmC,CAAE;gBACxDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,mCAAmC,CAAE;gBAAAM,QAAA,EAE9D/C,kBAAkB,CAAC,4BAA4B,EAAE,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXpB,OAAA,CAACnC,cAAc;YACbkD,EAAE,EAAE;cAAEmD,EAAE,EAAE;YAAE,CAAE;YACdP,OAAO,EAAEA,CAAA,KAAM7B,gBAAgB,CAAC,CAACD,aAAa,CAAE;YAChD6B,QAAQ,EAAEZ,cAAc,CAAC,uBAAuB,CAAE;YAAAW,QAAA,GAGjD/C,kBAAkB,CAAC,YAAY,EAAE,CAAC,CAAC,EACnCmB,aAAa,gBAAG7B,OAAA,CAACxB,UAAU;cAACqC,QAAQ,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpB,OAAA,CAACvB,UAAU;cAACoC,QAAQ,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEjBpB,OAAA,CAACpC,QAAQ;YAACiG,EAAE,EAAEhC,aAAc;YAACiC,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAN,QAAA,eACvDzD,OAAA,CAAC3C,IAAI;cAAC2G,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAR,QAAA,gBAClCzD,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,kCAAkC,CAAE;gBACvDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,kCAAkC,CAAE;gBAAAM,QAAA,EAE7D/C,kBAAkB,CAAC,mBAAmB,EAAE,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,4BAA4B,CAAE;gBACjDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,4BAA4B,CAAE;gBAAAM,QAAA,EAEvD/C,kBAAkB,CAAC,mBAAmB,EAAE,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,gCAAgC,CAAE;gBACrDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,gCAAgC,CAAE;gBAAAM,QAAA,EAE3D/C,kBAAkB,CAAC,iBAAiB,EAAE,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,+BAA+B,CAAE;gBACpDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,+BAA+B,CAAE;gBAAAM,QAAA,EAE1D/C,kBAAkB,CAAC,gBAAgB,EAAE,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,+BAA+B,CAAE;gBACpDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,+BAA+B,CAAE;gBAAAM,QAAA,EAE1D/C,kBAAkB,CAAC,kBAAkB,EAAE,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXpB,OAAA,CAACnC,cAAc;YACbkD,EAAE,EAAE;cAAEmD,EAAE,EAAE;YAAE,CAAE;YACdP,OAAO,EAAEA,CAAA,KAAM3B,gBAAgB,CAAC,CAACD,aAAa,CAAE;YAChD2B,QAAQ,EAAEZ,cAAc,CAAC,uBAAuB,CAAE;YAAAW,QAAA,GAGjD/C,kBAAkB,CAAC,gBAAgB,EAAE,CAAC,CAAC,EACvCqB,aAAa,gBAAG/B,OAAA,CAACxB,UAAU;cAACqC,QAAQ,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpB,OAAA,CAACvB,UAAU;cAACoC,QAAQ,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eAEjBpB,OAAA,CAACpC,QAAQ;YAACiG,EAAE,EAAE9B,aAAc;YAAC+B,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAN,QAAA,eACvDzD,OAAA,CAAC3C,IAAI;cAAC2G,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAR,QAAA,gBAClCzD,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,oCAAoC,CAAE;gBACzDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,oCAAoC,CAAE;gBAAAM,QAAA,EAE/D/C,kBAAkB,CAAC,uBAAuB,EAAE,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,sCAAsC,CAAE;gBAC3DgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,sCAAsC,CAAE;gBAAAM,QAAA,gBAElEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACP,OAAO;oBAACoB,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAsB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,qCAAqC,CAAE;gBAC1DgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,qCAAqC,CAAE;gBAAAM,QAAA,gBAEjEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACf,cAAc;oBAAC4B,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAyB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,uCAAuC,CAAE;gBAC5DgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,uCAAuC,CAAE;gBAAAM,QAAA,gBAEnEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACf,cAAc;oBAAC4B,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAA2B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,oCAAoC,CAAE;gBACzDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,oCAAoC,CAAE;gBAAAM,QAAA,gBAEhEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACf,cAAc;oBAAC4B,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAuB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,sCAAsC,CAAE;gBAC3DgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,sCAAsC,CAAE;gBAAAM,QAAA,gBAElEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACf,cAAc;oBAAC4B,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAyB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXpB,OAAA,CAACnC,cAAc;YACbkD,EAAE,EAAE;cAAEmD,EAAE,EAAE;YAAE,CAAE;YACdP,OAAO,EAAEA,CAAA,KAAMzB,iBAAiB,CAAC,CAACD,cAAc,CAAE;YAClDyB,QAAQ,EAAEZ,cAAc,CAAC,wBAAwB,CAAE;YAAAW,QAAA,GAGlD/C,kBAAkB,CAAC,QAAQ,EAAE,CAAC,CAAC,EAC/BuB,cAAc,gBAAGjC,OAAA,CAACxB,UAAU;cAACqC,QAAQ,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpB,OAAA,CAACvB,UAAU;cAACoC,QAAQ,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eAEjBpB,OAAA,CAACpC,QAAQ;YAACiG,EAAE,EAAE5B,cAAe;YAAC6B,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAN,QAAA,eACxDzD,OAAA,CAAC3C,IAAI;cAAC2G,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAR,QAAA,gBAClCzD,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,oCAAoC,CAAE;gBACzDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,oCAAoC,CAAE;gBAAAM,QAAA,EAE/D/C,kBAAkB,CAAC,oBAAoB,EAAE,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,4BAA4B,CAAE;gBACjDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,4BAA4B,CAAE;gBAAAM,QAAA,EAEvD/C,kBAAkB,CAAC,oBAAoB,EAAE,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,wCAAwC,CAAE;gBAC7DgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,wCAAwC,CAAE;gBAAAM,QAAA,gBAEpEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAwB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,qCAAqC,CAAE;gBAC1DgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,qCAAqC,CAAE;gBAAAM,QAAA,gBAEjEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAyB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXpB,OAAA,CAACnC,cAAc;YACbkD,EAAE,EAAE;cAAEmD,EAAE,EAAE;YAAE,CAAE;YACdP,OAAO,EAAEA,CAAA,KAAMvB,yBAAyB,CAAC,CAACD,sBAAsB,CAAE;YAClEuB,QAAQ,EAAEZ,cAAc,CAAC,gCAAgC,CAAE;YAAAW,QAAA,GAG1D/C,kBAAkB,CAAC,qBAAqB,EAAE,CAAC,CAAC,EAC5CyB,sBAAsB,gBAAGnC,OAAA,CAACxB,UAAU;cAACqC,QAAQ,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpB,OAAA,CAACvB,UAAU;cAACoC,QAAQ,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eAEjBpB,OAAA,CAACpC,QAAQ;YAACiG,EAAE,EAAE1B,sBAAuB;YAAC2B,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAN,QAAA,eAChEzD,OAAA,CAAC3C,IAAI;cAAC2G,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAR,QAAA,gBAClCzD,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,2CAA2C,CAAE;gBAChEgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,2CAA2C,CAAE;gBAAAM,QAAA,gBAEvEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACrB,YAAY;oBAACkC,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAA2B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,uCAAuC,CAAE;gBAC5DgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,uCAAuC,CAAE;gBAAAM,QAAA,gBAEnEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACrB,YAAY;oBAACkC,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAiB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,qCAAqC,CAAE;gBAC1DgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,qCAAqC,CAAE;gBAAAM,QAAA,gBAEjEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACP,OAAO;oBAACoB,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAqB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,yCAAyC,CAAE;gBAC9DgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,yCAAyC,CAAE;gBAAAM,QAAA,gBAErEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACrB,YAAY;oBAACkC,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAyB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,oCAAoC,CAAE;gBACzDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,oCAAoC,CAAE;gBAAAM,QAAA,gBAEhEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,wCAAwC,CAAE;gBAC7DgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,wCAAwC,CAAE;gBAAAM,QAAA,gBAEpEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACH,UAAU;oBAACgB,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAwB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,0CAA0C,CAAE;gBAC/DgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,0CAA0C,CAAE;gBAAAM,QAAA,gBAEtEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAoB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGXpB,OAAA,CAACnC,cAAc;YACbkD,EAAE,EAAE;cAAEmD,EAAE,EAAE;YAAE,CAAE;YACdP,OAAO,EAAEA,CAAA,KAAMrB,kBAAkB,CAAC,CAACD,eAAe,CAAE;YACpDqB,QAAQ,EAAEZ,cAAc,CAAC,yBAAyB,CAAE;YAAAW,QAAA,gBAEpDzD,OAAA,CAACzC,YAAY;cAAAkG,QAAA,eACXzD,OAAA,CAACT,gBAAgB;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACfpB,OAAA,CAACxC,YAAY;cAACmD,OAAO,EAAC;YAAkB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC1CiB,eAAe,gBAAGrC,OAAA,CAACxB,UAAU;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpB,OAAA,CAACvB,UAAU;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eAEjBpB,OAAA,CAACpC,QAAQ;YAACiG,EAAE,EAAExB,eAAgB;YAACyB,OAAO,EAAC,MAAM;YAACC,aAAa;YAAAN,QAAA,eACzDzD,OAAA,CAAC3C,IAAI;cAAC2G,SAAS,EAAC,KAAK;cAACC,cAAc;cAAAR,QAAA,gBAClCzD,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,oCAAoC,CAAE;gBACzDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,oCAAoC,CAAE;gBAAAM,QAAA,gBAEhEzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACrB,YAAY;oBAACkC,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAoB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,8BAA8B,CAAE;gBACnDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,8BAA8B,CAAE;gBAAAM,QAAA,gBAE1DzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACP,OAAO;oBAACoB,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAoB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,kCAAkC,CAAE;gBACvDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,kCAAkC,CAAE;gBAAAM,QAAA,gBAE9DzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACL,QAAQ;oBAACkB,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAkB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,iCAAiC,CAAE;gBACtDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,iCAAiC,CAAE;gBAAAM,QAAA,gBAE7DzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACH,UAAU;oBAACgB,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAiB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,gCAAgC,CAAE;gBACrDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,gCAAgC,CAAE;gBAAAM,QAAA,gBAE5DzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAgB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eAEjBpB,OAAA,CAACnC,cAAc;gBACbkD,EAAE,EAAE;kBAAEmD,EAAE,EAAE;gBAAE,CAAE;gBACdR,QAAQ,EAAEf,QAAQ,CAAC,iCAAiC,CAAE;gBACtDgB,OAAO,EAAEA,CAAA,KAAMR,UAAU,CAAC,iCAAiC,CAAE;gBAAAM,QAAA,gBAE7DzD,OAAA,CAACzC,YAAY;kBAAAkG,QAAA,eACXzD,OAAA,CAACb,cAAc;oBAAC0B,QAAQ,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACfpB,OAAA,CAACxC,YAAY;kBAACmD,OAAO,EAAC;gBAAwB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACX;IAAA,eACD,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAAChB,EAAA,CA/mBID,QAAQ;EAAA,QACKhD,WAAW,EACXC,WAAW,EACwB0C,OAAO;AAAA;AAAA4E,EAAA,GAHvDvE,QAAQ;AAinBd,eAAeA,QAAQ;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}