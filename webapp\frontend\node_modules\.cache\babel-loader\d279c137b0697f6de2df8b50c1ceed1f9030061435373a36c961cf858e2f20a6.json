{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'pasinta' eeee 'je' p\",\n  yesterday: \"'hieraŭ je' p\",\n  today: \"'hodiaŭ je' p\",\n  tomorrow: \"'morgaŭ je' p\",\n  nextWeek: \"eeee 'je' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/eo/_lib/formatRelative.mjs"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'pasinta' eeee 'je' p\",\n  yesterday: \"'hieraŭ je' p\",\n  today: \"'hodiaŭ je' p\",\n  tomorrow: \"'morgaŭ je' p\",\n  nextWeek: \"eeee 'je' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,uBAAuB;EACjCC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,eAAe;EACzBC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}