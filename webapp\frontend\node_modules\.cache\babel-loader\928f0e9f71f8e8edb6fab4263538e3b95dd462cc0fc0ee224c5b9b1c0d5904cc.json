{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Typography, TextField, Button, Stepper, Step, StepLabel, Grid, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, ArrowBack as ArrowBackIcon, ArrowForward as ArrowForwardIcon, Cancel as CancelIcon, CheckCircle as CheckCircleIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form multi-step\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Definizione dei passi del form\n  const steps = ['Seleziona Cavo', 'Inserisci Metri', 'Associa Bobina', 'Conferma'];\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica la lista delle bobine quando necessario\n  useEffect(() => {\n    if (activeStep === 2) {\n      loadBobine();\n    }\n  }, [activeStep, cantiereId]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n      // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n      setCavi(caviData);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      // Carica solo le bobine disponibili o in uso\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      const bobineUtilizzabili = bobineData.filter(bobina => bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso');\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      const cavoData = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica se il cavo è già installato\n      if (cavoData.stato_installazione === 'Installato' || cavoData.metratura_reale && cavoData.metratura_reale > 0) {\n        // Mostra un messaggio di conferma con opzioni\n        const message = `Il cavo ${cavoData.id_cavo} risulta già posato (${cavoData.metratura_reale || 0}m).\n\nVuoi:\n1. Modificare la bobina associata (usa la funzione \"Modifica bobina cavo posato\")\n2. Selezionare un altro cavo\n3. Annullare l'operazione`;\n        const userChoice = window.confirm(message);\n        if (userChoice) {\n          // L'utente ha scelto di modificare la bobina o selezionare un altro cavo\n          // Reindirizza alla pagina di modifica bobina\n          navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${cavoData.id_cavo}`);\n        }\n        setCaviLoading(false);\n        return;\n      }\n\n      // Verifica se il cavo è SPARE\n      if (cavoData.modificato_manualmente === 3) {\n        // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n      }\n\n      // Seleziona il cavo e passa al passo successivo\n      handleCavoSelect(cavoData);\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n      onError('Cavo non trovato o errore nella ricerca: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Passa al passo successivo\n          setActiveStep(1);\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Passa al passo successivo\n      setActiveStep(1);\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n      } else if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n      } else if (selectedCavo && selectedCavo.metri_teorici && parseFloat(value) > parseFloat(selectedCavo.metri_teorici)) {\n        warning = 'I metri posati superano i metri teorici del cavo';\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n    setFormErrors(errors);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 1) {\n      // Validazione prima di passare al passo successivo\n      if (!validateForm()) {\n        return;\n      }\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    if (!metriPosati || parseFloat(metriPosati) <= 0) {\n      return 'Da installare';\n    }\n    if (parseFloat(metriPosati) >= parseFloat(metriTeorici)) {\n      return 'Installato';\n    }\n    return 'In corso';\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      const metriPosati = parseFloat(formData.metri_posati);\n      const idBobina = formData.id_bobina || null;\n\n      // Determina lo stato di installazione\n      const statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        statoInstallazione\n      });\n\n      // Chiamata API\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina);\n\n      // Gestione successo\n      onSuccess(`Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Cerca cavo per ID\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 9,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavo\",\n              variant: \"outlined\",\n              value: cavoIdInput,\n              onChange: e => setCavoIdInput(e.target.value),\n              placeholder: \"Inserisci l'ID del cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: handleSearchCavoById,\n              disabled: caviLoading || !cavoIdInput.trim(),\n              startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 42\n              }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 75\n              }, this),\n              children: \"Cerca\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Non ci sono cavi disponibili da installare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '400px',\n            overflow: 'auto'\n          },\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 27\n                  }, this), cavo.modificato_manualmente === 3 ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"SPARE\",\n                    color: \"error\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 29\n                  }, this) : cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0 ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"Installato\",\n                    color: \"success\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: cavo.stato_installazione === 'In corso' ? 'warning' : 'default',\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [cavo.tipologia || 'N/A', \" - \", cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', \" - A: \", cavo.ubicazione_arrivo || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 473,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A', \" - Metri posati: \", cavo.metratura_reale || '0']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Dettagli del cavo selezionato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"ID Cavo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Conduttori:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.n_conduttori || 'N/A', \" x \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione Partenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione Arrivo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri Teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Metri posati\",\n          variant: \"outlined\",\n          name: \"metri_posati\",\n          type: \"number\",\n          value: formData.metri_posati,\n          onChange: handleFormChange,\n          error: !!formErrors.metri_posati,\n          helperText: formErrors.metri_posati || formWarnings.metri_posati,\n          FormHelperTextProps: {\n            sx: {\n              color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n            }\n          },\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = idBobina => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = e => {\n      const numeroBobina = e.target.value.trim();\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n        if (bobinaEsistente) {\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Associa bobina (opzionale)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 637,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          paragraph: true,\n          children: \"Puoi associare una bobina al cavo selezionato. Questo \\xE8 opzionale, ma consigliato per tenere traccia dell'utilizzo delle bobine.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 647,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Inserisci numero bobina\",\n                variant: \"outlined\",\n                placeholder: \"Inserisci solo il numero (Y)\",\n                helperText: formErrors.id_bobina_input || \"Inserisci solo il numero della bobina (parte Y del codice Cx_By)\",\n                error: !!formErrors.id_bobina_input,\n                onBlur: handleBobinaNumberInput\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mt: 1\n                },\n                children: [\"ID Bobina completo: \", formData.id_bobina || '-']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 653,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"bobina-select-label\",\n              children: \"Seleziona Bobina dalla lista\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"bobina-select-label\",\n              id: \"bobina-select\",\n              name: \"id_bobina\",\n              value: formData.id_bobina,\n              label: \"Seleziona Bobina dalla lista\",\n              onChange: handleFormChange,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: /*#__PURE__*/_jsxDEV(\"em\", {\n                  children: \"Nessuna bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 19\n              }, this), bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: bobina.id_bobina,\n                disabled: bobina.metri_residui < parseFloat(formData.metri_posati),\n                children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A', \" - Residui: \", bobina.metri_residui || 0, \" m\"]\n              }, bobina.id_bobina, true, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n              children: \"Seleziona una bobina con metri residui sufficienti o lascia vuoto per non associare alcuna bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 15\n          }, this), formData.id_bobina && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 3,\n              p: 2,\n              bgcolor: 'background.paper',\n              borderRadius: 1,\n              border: '1px solid #e0e0e0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              children: \"Dettagli bobina selezionata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 19\n            }, this), (() => {\n              const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n              if (bobina) {\n                return /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Numero:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 714,\n                        columnNumber: 31\n                      }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 713,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Tipologia:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 717,\n                        columnNumber: 31\n                      }, this), \" \", bobina.tipologia || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 716,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Conduttori:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 720,\n                        columnNumber: 31\n                      }, this), \" \", bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 719,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 712,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri totali:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 725,\n                        columnNumber: 31\n                      }, this), \" \", bobina.metri_totali || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 724,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri residui:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 728,\n                        columnNumber: 31\n                      }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 727,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Stato:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 731,\n                        columnNumber: 31\n                      }, this), \" \", bobina.stato_bobina || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 730,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 723,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 25\n                }, this);\n              }\n              return /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"error\",\n                children: \"Bobina non trovata nel database\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 23\n              }, this);\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 749,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 641,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 636,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = idBobina => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Ottieni il numero della bobina se presente\n    const numeroBobina = formData.id_bobina ? getBobinaNumber(formData.id_bobina) : 'Nessuna';\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 774,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"ID Cavo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 786,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Conduttori:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.n_conduttori || 'N/A', \" x \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 784,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri Posati:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 797,\n                columnNumber: 17\n              }, this), \" \", formData.metri_posati]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 796,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Bobina Associata:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 17\n              }, this), \" \", numeroBobina]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato Installazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 803,\n                columnNumber: 17\n              }, this), \" \", parseFloat(formData.metri_posati) >= parseFloat(selectedCavo.metri_teorici) ? 'Installato' : 'In corso']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 783,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 808,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 778,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 773,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      case 1:\n        return renderStep2();\n      case 2:\n        return renderStep3();\n      case 3:\n        return renderStep4();\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Stepper, {\n      activeStep: activeStep,\n      sx: {\n        mb: 4\n      },\n      children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 13\n        }, this)\n      }, label, false, {\n        fileName: _jsxFileName,\n        lineNumber: 837,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 835,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2,\n        mb: 4\n      },\n      children: getStepContent(activeStep)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 843,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"secondary\",\n        onClick: activeStep === 0 ? () => navigate('/dashboard/cavi/posa') : handleBack,\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 22\n        }, this),\n        disabled: loading,\n        children: activeStep === 0 ? 'Annulla' : 'Indietro'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 848,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: activeStep === steps.length - 1 ? handleSubmit : handleNext,\n        endIcon: activeStep === steps.length - 1 ? /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 862,\n          columnNumber: 54\n        }, this) : /*#__PURE__*/_jsxDEV(ArrowForwardIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 862,\n          columnNumber: 69\n        }, this),\n        disabled: loading || activeStep === 0 && !selectedCavo,\n        children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 866,\n          columnNumber: 13\n        }, this) : activeStep === steps.length - 1 ? 'Salva' : 'Avanti'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 858,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 847,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 834,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"sTdBCOBiexa/+X2SJsmiccxpsm4=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "Search", "SearchIcon", "Save", "SaveIcon", "ArrowBack", "ArrowBackIcon", "ArrowForward", "ArrowForwardIcon", "Cancel", "CancelIcon", "CheckCircle", "CheckCircleIcon", "useNavigate", "caviService", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "activeStep", "setActiveStep", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "steps", "loadCavi", "loadBobine", "caviData", "get<PERSON><PERSON>", "error", "console", "message", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "bobina", "stato_bobina", "handleSearchCavoById", "trim", "cavoData", "getCavoById", "stato_installazione", "metratura_reale", "userChoice", "window", "confirm", "modificato_manualmente", "handleCavoSelect", "cavo", "reactivateSpare", "then", "updatedCavo", "catch", "cavoId", "handleFormChange", "e", "name", "value", "target", "validateField", "warning", "isNaN", "parseFloat", "metri_te<PERSON>ci", "prev", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "handleNext", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metriTeorici", "handleSubmit", "idBobina", "statoInstallazione", "log", "updateMetri<PERSON><PERSON><PERSON>", "renderStep1", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "container", "spacing", "alignItems", "item", "xs", "fullWidth", "label", "onChange", "placeholder", "color", "onClick", "disabled", "startIcon", "size", "display", "justifyContent", "my", "length", "severity", "maxHeight", "overflow", "map", "button", "primary", "ml", "secondary", "component", "tipologia", "n_conduttori", "sezione", "ubicazione_partenza", "ubicazione_arrivo", "renderStep2", "md", "type", "helperText", "FormHelperTextProps", "mt", "renderStep3", "getBobinaNumber", "includes", "split", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "metri_residui", "handleBobinaNumberInput", "idBobinaCompleto", "<PERSON><PERSON><PERSON>", "find", "b", "id_bobina_input", "paragraph", "onBlur", "id", "labelId", "bgcolor", "borderRadius", "border", "metri_totali", "renderStep4", "getStepContent", "step", "endIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Stepper,\n  Step,\n  StepLabel,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  ArrowBack as ArrowBackIcon,\n  ArrowForward as ArrowForwardIcon,\n  Cancel as CancelIcon,\n  CheckCircle as CheckCircleIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form multi-step\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Definizione dei passi del form\n  const steps = ['Seleziona Cavo', 'Inserisci Metri', 'Associa Bobina', 'Conferma'];\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica la lista delle bobine quando necessario\n  useEffect(() => {\n    if (activeStep === 2) {\n      loadBobine();\n    }\n  }, [activeStep, cantiereId]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n      // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n      setCavi(caviData);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      // Carica solo le bobine disponibili o in uso\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      const bobineUtilizzabili = bobineData.filter(bobina =>\n        bobina.stato_bobina === 'Disponibile' ||\n        bobina.stato_bobina === 'In Uso'\n      );\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      const cavoData = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica se il cavo è già installato\n      if (cavoData.stato_installazione === 'Installato' || (cavoData.metratura_reale && cavoData.metratura_reale > 0)) {\n        // Mostra un messaggio di conferma con opzioni\n        const message = `Il cavo ${cavoData.id_cavo} risulta già posato (${cavoData.metratura_reale || 0}m).\n\nVuoi:\n1. Modificare la bobina associata (usa la funzione \"Modifica bobina cavo posato\")\n2. Selezionare un altro cavo\n3. Annullare l'operazione`;\n\n        const userChoice = window.confirm(message);\n\n        if (userChoice) {\n          // L'utente ha scelto di modificare la bobina o selezionare un altro cavo\n          // Reindirizza alla pagina di modifica bobina\n          navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${cavoData.id_cavo}`);\n        }\n\n        setCaviLoading(false);\n        return;\n      }\n\n      // Verifica se il cavo è SPARE\n      if (cavoData.modificato_manualmente === 3) {\n        // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n      }\n\n      // Seleziona il cavo e passa al passo successivo\n      handleCavoSelect(cavoData);\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n      onError('Cavo non trovato o errore nella ricerca: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Passa al passo successivo\n          setActiveStep(1);\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Passa al passo successivo\n      setActiveStep(1);\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n      } else if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n      } else if (selectedCavo && selectedCavo.metri_teorici && parseFloat(value) > parseFloat(selectedCavo.metri_teorici)) {\n        warning = 'I metri posati superano i metri teorici del cavo';\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    setFormErrors(errors);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 1) {\n      // Validazione prima di passare al passo successivo\n      if (!validateForm()) {\n        return;\n      }\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    if (!metriPosati || parseFloat(metriPosati) <= 0) {\n      return 'Da installare';\n    }\n\n    if (parseFloat(metriPosati) >= parseFloat(metriTeorici)) {\n      return 'Installato';\n    }\n\n    return 'In corso';\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      const metriPosati = parseFloat(formData.metri_posati);\n      const idBobina = formData.id_bobina || null;\n\n      // Determina lo stato di installazione\n      const statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        statoInstallazione\n      });\n\n      // Chiamata API\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina\n      );\n\n      // Gestione successo\n      onSuccess(`Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID */}\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Cerca cavo per ID\n          </Typography>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={9}>\n              <TextField\n                fullWidth\n                label=\"ID Cavo\"\n                variant=\"outlined\"\n                value={cavoIdInput}\n                onChange={(e) => setCavoIdInput(e.target.value)}\n                placeholder=\"Inserisci l'ID del cavo\"\n              />\n            </Grid>\n            <Grid item xs={3}>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={handleSearchCavoById}\n                disabled={caviLoading || !cavoIdInput.trim()}\n                startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n              >\n                Cerca\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Lista cavi */}\n        <Paper sx={{ p: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\">\n              Non ci sono cavi disponibili da installare.\n            </Alert>\n          ) : (\n            <List sx={{ maxHeight: '400px', overflow: 'auto' }}>\n              {cavi.map((cavo) => (\n                <React.Fragment key={cavo.id_cavo}>\n                  <ListItem button onClick={() => handleCavoSelect(cavo)}>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle1\">{cavo.id_cavo}</Typography>\n                          {cavo.modificato_manualmente === 3 ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"SPARE\"\n                              color=\"error\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"Installato\"\n                              color=\"success\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : (\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione}\n                              color={cavo.stato_installazione === 'In corso' ? 'warning' : 'default'}\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <>\n                          <Typography variant=\"body2\" component=\"span\">\n                            {cavo.tipologia || 'N/A'} - {cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Da: {cavo.ubicazione_partenza || 'N/A'} - A: {cavo.ubicazione_arrivo || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Metri teorici: {cavo.metri_teorici || 'N/A'} - Metri posati: {cavo.metratura_reale || '0'}\n                          </Typography>\n                        </>\n                      }\n                    />\n                  </ListItem>\n                  <Divider />\n                </React.Fragment>\n              ))}\n            </List>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserisci metri posati\n        </Typography>\n\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Dettagli del cavo selezionato\n          </Typography>\n\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body1\">\n                <strong>ID Cavo:</strong> {selectedCavo.id_cavo}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'} x {selectedCavo.sezione || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body1\">\n                <strong>Ubicazione Partenza:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Ubicazione Arrivo:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}\n              </Typography>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Inserisci i metri posati\n          </Typography>\n\n          <TextField\n            fullWidth\n            label=\"Metri posati\"\n            variant=\"outlined\"\n            name=\"metri_posati\"\n            type=\"number\"\n            value={formData.metri_posati}\n            onChange={handleFormChange}\n            error={!!formErrors.metri_posati}\n            helperText={formErrors.metri_posati || formWarnings.metri_posati}\n            FormHelperTextProps={{\n              sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n            }}\n            sx={{ mb: 2 }}\n          />\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = (idBobina) => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = (e) => {\n      const numeroBobina = e.target.value.trim();\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n\n        if (bobinaEsistente) {\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Associa bobina (opzionale)\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"body1\" paragraph>\n            Puoi associare una bobina al cavo selezionato. Questo è opzionale, ma consigliato per tenere traccia dell'utilizzo delle bobine.\n          </Typography>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              {/* Input diretto del numero della bobina */}\n              <Grid container spacing={2} sx={{ mb: 3 }}>\n                <Grid item xs={12} md={6}>\n                  <TextField\n                    fullWidth\n                    label=\"Inserisci numero bobina\"\n                    variant=\"outlined\"\n                    placeholder=\"Inserisci solo il numero (Y)\"\n                    helperText={formErrors.id_bobina_input || \"Inserisci solo il numero della bobina (parte Y del codice Cx_By)\"}\n                    error={!!formErrors.id_bobina_input}\n                    onBlur={handleBobinaNumberInput}\n                  />\n                </Grid>\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                    ID Bobina completo: {formData.id_bobina || '-'}\n                  </Typography>\n                </Grid>\n              </Grid>\n\n              {/* Selezione dalla lista */}\n              <FormControl fullWidth>\n                <InputLabel id=\"bobina-select-label\">Seleziona Bobina dalla lista</InputLabel>\n                <Select\n                  labelId=\"bobina-select-label\"\n                  id=\"bobina-select\"\n                  name=\"id_bobina\"\n                  value={formData.id_bobina}\n                  label=\"Seleziona Bobina dalla lista\"\n                  onChange={handleFormChange}\n                >\n                  <MenuItem value=\"\">\n                    <em>Nessuna bobina</em>\n                  </MenuItem>\n                  {bobine.map((bobina) => (\n                    <MenuItem\n                      key={bobina.id_bobina}\n                      value={bobina.id_bobina}\n                      disabled={bobina.metri_residui < parseFloat(formData.metri_posati)}\n                    >\n                      {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'} - Residui: {bobina.metri_residui || 0} m\n                    </MenuItem>\n                  ))}\n                </Select>\n                <FormHelperText>\n                  Seleziona una bobina con metri residui sufficienti o lascia vuoto per non associare alcuna bobina\n                </FormHelperText>\n              </FormControl>\n\n              {/* Mostra dettagli della bobina selezionata */}\n              {formData.id_bobina && (\n                <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n                  <Typography variant=\"subtitle2\" gutterBottom>\n                    Dettagli bobina selezionata\n                  </Typography>\n                  {(() => {\n                    const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                    if (bobina) {\n                      return (\n                        <Grid container spacing={2}>\n                          <Grid item xs={12} md={6}>\n                            <Typography variant=\"body2\">\n                              <strong>Numero:</strong> {getBobinaNumber(bobina.id_bobina)}\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Tipologia:</strong> {bobina.tipologia || 'N/A'}\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Conduttori:</strong> {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                            </Typography>\n                          </Grid>\n                          <Grid item xs={12} md={6}>\n                            <Typography variant=\"body2\">\n                              <strong>Metri totali:</strong> {bobina.metri_totali || 0} m\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Metri residui:</strong> {bobina.metri_residui || 0} m\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Stato:</strong> {bobina.stato_bobina || 'N/A'}\n                            </Typography>\n                          </Grid>\n                        </Grid>\n                      );\n                    }\n                    return (\n                      <Typography variant=\"body2\" color=\"error\">\n                        Bobina non trovata nel database\n                      </Typography>\n                    );\n                  })()}\n                </Box>\n              )}\n            </Box>\n          )}\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = (idBobina) => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Ottieni il numero della bobina se presente\n    const numeroBobina = formData.id_bobina ? getBobinaNumber(formData.id_bobina) : 'Nessuna';\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body1\">\n                <strong>ID Cavo:</strong> {selectedCavo.id_cavo}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'} x {selectedCavo.sezione || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body1\">\n                <strong>Metri Posati:</strong> {formData.metri_posati}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Bobina Associata:</strong> {numeroBobina}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Stato Installazione:</strong> {parseFloat(formData.metri_posati) >= parseFloat(selectedCavo.metri_teorici) ? 'Installato' : 'In corso'}\n              </Typography>\n            </Grid>\n          </Grid>\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      case 1:\n        return renderStep2();\n      case 2:\n        return renderStep3();\n      case 3:\n        return renderStep4();\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  return (\n    <Box>\n      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>\n        {steps.map((label) => (\n          <Step key={label}>\n            <StepLabel>{label}</StepLabel>\n          </Step>\n        ))}\n      </Stepper>\n\n      <Box sx={{ mt: 2, mb: 4 }}>\n        {getStepContent(activeStep)}\n      </Box>\n\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\n        <Button\n          variant=\"outlined\"\n          color=\"secondary\"\n          onClick={activeStep === 0 ? () => navigate('/dashboard/cavi/posa') : handleBack}\n          startIcon={<ArrowBackIcon />}\n          disabled={loading}\n        >\n          {activeStep === 0 ? 'Annulla' : 'Indietro'}\n        </Button>\n\n        <Button\n          variant=\"contained\"\n          color=\"primary\"\n          onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}\n          endIcon={activeStep === steps.length - 1 ? <SaveIcon /> : <ArrowForwardIcon />}\n          disabled={loading || (activeStep === 0 && !selectedCavo)}\n        >\n          {loading ? (\n            <CircularProgress size={24} />\n          ) : activeStep === steps.length - 1 ? (\n            'Salva'\n          ) : (\n            'Avanti'\n          )}\n        </Button>\n      </Box>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,QACC,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,aAAa,EAC1BC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsD,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwD,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAAC0D,IAAI,EAAEC,OAAO,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC4D,MAAM,EAAEC,SAAS,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACkE,QAAQ,EAAEC,WAAW,CAAC,GAAGnE,QAAQ,CAAC;IACvCoE,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM2E,KAAK,GAAG,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,UAAU,CAAC;;EAEjF;EACA1E,SAAS,CAAC,MAAM;IACd2E,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC/B,UAAU,CAAC,CAAC;;EAEhB;EACA5C,SAAS,CAAC,MAAM;IACd,IAAIiD,UAAU,KAAK,CAAC,EAAE;MACpB2B,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAC3B,UAAU,EAAEL,UAAU,CAAC,CAAC;;EAE5B;EACA,MAAM+B,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFrB,cAAc,CAAC,IAAI,CAAC;MACpB;MACA,MAAMuB,QAAQ,GAAG,MAAMzC,WAAW,CAAC0C,OAAO,CAAClC,UAAU,CAAC;;MAEtD;MACA;MACAc,OAAO,CAACmB,QAAQ,CAAC;IACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDjC,OAAO,CAAC,mCAAmC,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACR3B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMsB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFpB,gBAAgB,CAAC,IAAI,CAAC;MACtB;MACA,MAAM0B,UAAU,GAAG,MAAM7C,gBAAgB,CAAC8C,SAAS,CAACvC,UAAU,CAAC;MAC/D,MAAMwC,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM,IACjDA,MAAM,CAACC,YAAY,KAAK,aAAa,IACrCD,MAAM,CAACC,YAAY,KAAK,QAC1B,CAAC;MACD3B,SAAS,CAACwB,kBAAkB,CAAC;IAC/B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DjC,OAAO,CAAC,uCAAuC,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRzB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMgC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACzB,WAAW,CAAC0B,IAAI,CAAC,CAAC,EAAE;MACvB3C,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFQ,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMoC,QAAQ,GAAG,MAAMtD,WAAW,CAACuD,WAAW,CAAC/C,UAAU,EAAEmB,WAAW,CAAC0B,IAAI,CAAC,CAAC,CAAC;;MAE9E;MACA,IAAIC,QAAQ,CAACE,mBAAmB,KAAK,YAAY,IAAKF,QAAQ,CAACG,eAAe,IAAIH,QAAQ,CAACG,eAAe,GAAG,CAAE,EAAE;QAC/G;QACA,MAAMZ,OAAO,GAAG,WAAWS,QAAQ,CAACvB,OAAO,wBAAwBuB,QAAQ,CAACG,eAAe,IAAI,CAAC;AACxG;AACA;AACA;AACA;AACA,0BAA0B;QAElB,MAAMC,UAAU,GAAGC,MAAM,CAACC,OAAO,CAACf,OAAO,CAAC;QAE1C,IAAIa,UAAU,EAAE;UACd;UACA;UACA9C,QAAQ,CAAC,mCAAmCJ,UAAU,IAAI8C,QAAQ,CAACvB,OAAO,EAAE,CAAC;QAC/E;QAEAb,cAAc,CAAC,KAAK,CAAC;QACrB;MACF;;MAEA;MACA,IAAIoC,QAAQ,CAACO,sBAAsB,KAAK,CAAC,EAAE;QACzC;MAAA;;MAGF;MACAC,gBAAgB,CAACR,QAAQ,CAAC;IAC5B,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDjC,OAAO,CAAC,2CAA2C,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAChG,CAAC,SAAS;MACR3B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM4C,gBAAgB,GAAIC,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACF,sBAAsB,KAAK,CAAC,EAAE;MACrC;MACA,IAAIF,MAAM,CAACC,OAAO,CAAC,WAAWG,IAAI,CAAChC,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACAiC,eAAe,CAACD,IAAI,CAAChC,OAAO,CAAC,CAACkC,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAGH,IAAI;YAAEF,sBAAsB,EAAE;UAAE,CAAC;UAC1DnC,eAAe,CAACwC,WAAW,CAAC;UAC5BpC,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAEmC,WAAW,CAACnC,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAlB,aAAa,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAACqD,KAAK,CAACxB,KAAK,IAAI;UAChBC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvEjC,OAAO,CAAC,kDAAkD,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACAnB,eAAe,CAACqC,IAAI,CAAC;MACrBjC,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEgC,IAAI,CAAChC,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAlB,aAAa,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMkD,eAAe,GAAG,MAAOI,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAMpE,WAAW,CAACgE,eAAe,CAACxD,UAAU,EAAE4D,MAAM,CAAC;MACrD3D,SAAS,CAAC,QAAQ2D,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvEjC,OAAO,CAAC,kDAAkD,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMF,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM0B,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC3C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC0C,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACAE,aAAa,CAACH,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAACH,IAAI,EAAEC,KAAK,KAAK;IACrC,IAAI7B,KAAK,GAAG,IAAI;IAChB,IAAIgC,OAAO,GAAG,IAAI;IAElB,IAAIJ,IAAI,KAAK,cAAc,EAAE;MAC3B,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACnB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCV,KAAK,GAAG,uCAAuC;MACjD,CAAC,MAAM,IAAIiC,KAAK,CAACC,UAAU,CAACL,KAAK,CAAC,CAAC,IAAIK,UAAU,CAACL,KAAK,CAAC,IAAI,CAAC,EAAE;QAC7D7B,KAAK,GAAG,sCAAsC;MAChD,CAAC,MAAM,IAAIlB,YAAY,IAAIA,YAAY,CAACqD,aAAa,IAAID,UAAU,CAACL,KAAK,CAAC,GAAGK,UAAU,CAACpD,YAAY,CAACqD,aAAa,CAAC,EAAE;QACnHH,OAAO,GAAG,kDAAkD;MAC9D;IACF;;IAEA;IACAxC,aAAa,CAAC4C,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACR,IAAI,GAAG5B;IACV,CAAC,CAAC,CAAC;;IAEH;IACAN,eAAe,CAAC0C,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACR,IAAI,GAAGI;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAAChC,KAAK;EACf,CAAC;;EAED;EACA,MAAMqC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAACrD,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAACqB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjE6B,MAAM,CAAClD,YAAY,GAAG,uCAAuC;MAC7DiD,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAIL,KAAK,CAACC,UAAU,CAAChD,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAI6C,UAAU,CAAChD,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7FkD,MAAM,CAAClD,YAAY,GAAG,sCAAsC;MAC5DiD,OAAO,GAAG,KAAK;IACjB;IAEA9C,aAAa,CAAC+C,MAAM,CAAC;IACrB,OAAOD,OAAO;EAChB,CAAC;;EAED;EACA,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAItE,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAACmE,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF;IAEAlE,aAAa,CAAEsE,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBvE,aAAa,CAAEsE,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxBxE,aAAa,CAAC,CAAC,CAAC;IAChBY,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMkD,2BAA2B,GAAGA,CAACC,WAAW,EAAEC,YAAY,KAAK;IACjE,IAAI,CAACD,WAAW,IAAIX,UAAU,CAACW,WAAW,CAAC,IAAI,CAAC,EAAE;MAChD,OAAO,eAAe;IACxB;IAEA,IAAIX,UAAU,CAACW,WAAW,CAAC,IAAIX,UAAU,CAACY,YAAY,CAAC,EAAE;MACvD,OAAO,YAAY;IACrB;IAEA,OAAO,UAAU;EACnB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF1E,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAACgE,YAAY,CAAC,CAAC,EAAE;QACnBhE,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAMwE,WAAW,GAAGX,UAAU,CAAChD,QAAQ,CAACG,YAAY,CAAC;MACrD,MAAM2D,QAAQ,GAAG9D,QAAQ,CAACI,SAAS,IAAI,IAAI;;MAE3C;MACA,MAAM2D,kBAAkB,GAAGL,2BAA2B,CAACC,WAAW,EAAE/D,YAAY,CAACqD,aAAa,CAAC;;MAE/F;MACAlC,OAAO,CAACiD,GAAG,CAAC,aAAa,EAAE;QACzBrF,UAAU;QACV4D,MAAM,EAAEvC,QAAQ,CAACE,OAAO;QACxByD,WAAW;QACXG,QAAQ;QACRC;MACF,CAAC,CAAC;;MAEF;MACA,MAAM5F,WAAW,CAAC8F,iBAAiB,CACjCtF,UAAU,EACVqB,QAAQ,CAACE,OAAO,EAChByD,WAAW,EACXG,QACF,CAAC;;MAED;MACAlF,SAAS,CAAC,qDAAqDmF,kBAAkB,EAAE,CAAC;;MAEpF;MACAN,WAAW,CAAC,CAAC;;MAEb;MACA/C,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;MACzEjC,OAAO,CAAC,oDAAoD,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACzG,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+E,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACE3F,OAAA,CAACvC,GAAG;MAAAmI,QAAA,gBACF5F,OAAA,CAACrC,UAAU;QAACkI,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGblG,OAAA,CAACtC,KAAK;QAACyI,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzB5F,OAAA,CAACrC,UAAU;UAACkI,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblG,OAAA,CAAC/B,IAAI;UAACqI,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAZ,QAAA,gBAC7C5F,OAAA,CAAC/B,IAAI;YAACwI,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACf5F,OAAA,CAACpC,SAAS;cACR+I,SAAS;cACTC,KAAK,EAAC,SAAS;cACff,OAAO,EAAC,UAAU;cAClBzB,KAAK,EAAE7C,WAAY;cACnBsF,QAAQ,EAAG3C,CAAC,IAAK1C,cAAc,CAAC0C,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAChD0C,WAAW,EAAC;YAAyB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPlG,OAAA,CAAC/B,IAAI;YAACwI,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACf5F,OAAA,CAACnC,MAAM;cACL8I,SAAS;cACTd,OAAO,EAAC,WAAW;cACnBkB,KAAK,EAAC,SAAS;cACfC,OAAO,EAAEhE,oBAAqB;cAC9BiE,QAAQ,EAAEpG,WAAW,IAAI,CAACU,WAAW,CAAC0B,IAAI,CAAC,CAAE;cAC7CiE,SAAS,EAAErG,WAAW,gBAAGb,OAAA,CAACrB,gBAAgB;gBAACwI,IAAI,EAAE;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGlG,OAAA,CAAChB,UAAU;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAN,QAAA,EAC1E;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGRlG,OAAA,CAACtC,KAAK;QAACyI,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClB5F,OAAA,CAACrC,UAAU;UAACkI,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZrF,WAAW,gBACVb,OAAA,CAACvC,GAAG;UAAC0I,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5D5F,OAAA,CAACrB,gBAAgB;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJjF,IAAI,CAACsG,MAAM,KAAK,CAAC,gBACnBvH,OAAA,CAACtB,KAAK;UAAC8I,QAAQ,EAAC,MAAM;UAAA5B,QAAA,EAAC;QAEvB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAERlG,OAAA,CAAC1B,IAAI;UAAC6H,EAAE,EAAE;YAAEsB,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA9B,QAAA,EAChD3E,IAAI,CAAC0G,GAAG,CAAEhE,IAAI,iBACb3D,OAAA,CAAC1C,KAAK,CAAC2C,QAAQ;YAAA2F,QAAA,gBACb5F,OAAA,CAACzB,QAAQ;cAACqJ,MAAM;cAACZ,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAACC,IAAI,CAAE;cAAAiC,QAAA,eACrD5F,OAAA,CAACxB,YAAY;gBACXqJ,OAAO,eACL7H,OAAA,CAACvC,GAAG;kBAAC0I,EAAE,EAAE;oBAAEiB,OAAO,EAAE,MAAM;oBAAEZ,UAAU,EAAE;kBAAS,CAAE;kBAAAZ,QAAA,gBACjD5F,OAAA,CAACrC,UAAU;oBAACkI,OAAO,EAAC,WAAW;oBAAAD,QAAA,EAAEjC,IAAI,CAAChC;kBAAO;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,EAC1DvC,IAAI,CAACF,sBAAsB,KAAK,CAAC,gBAChCzD,OAAA,CAAClB,IAAI;oBACHqI,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,OAAO;oBACbG,KAAK,EAAC,OAAO;oBACbZ,EAAE,EAAE;sBAAE2B,EAAE,EAAE;oBAAE;kBAAE;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,GACAvC,IAAI,CAACP,mBAAmB,KAAK,YAAY,IAAKO,IAAI,CAACN,eAAe,IAAIM,IAAI,CAACN,eAAe,GAAG,CAAE,gBACjGrD,OAAA,CAAClB,IAAI;oBACHqI,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,YAAY;oBAClBG,KAAK,EAAC,SAAS;oBACfZ,EAAE,EAAE;sBAAE2B,EAAE,EAAE;oBAAE;kBAAE;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,gBAEFlG,OAAA,CAAClB,IAAI;oBACHqI,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAEjD,IAAI,CAACP,mBAAoB;oBAChC2D,KAAK,EAAEpD,IAAI,CAACP,mBAAmB,KAAK,UAAU,GAAG,SAAS,GAAG,SAAU;oBACvE+C,EAAE,EAAE;sBAAE2B,EAAE,EAAE;oBAAE;kBAAE;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACD6B,SAAS,eACP/H,OAAA,CAAAE,SAAA;kBAAA0F,QAAA,gBACE5F,OAAA,CAACrC,UAAU;oBAACkI,OAAO,EAAC,OAAO;oBAACmC,SAAS,EAAC,MAAM;oBAAApC,QAAA,GACzCjC,IAAI,CAACsE,SAAS,IAAI,KAAK,EAAC,KAAG,EAACtE,IAAI,CAACuE,YAAY,IAAI,KAAK,EAAC,KAAG,EAACvE,IAAI,CAACwE,OAAO,IAAI,KAAK;kBAAA;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACblG,OAAA;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNlG,OAAA,CAACrC,UAAU;oBAACkI,OAAO,EAAC,OAAO;oBAACmC,SAAS,EAAC,MAAM;oBAAApC,QAAA,GAAC,MACvC,EAACjC,IAAI,CAACyE,mBAAmB,IAAI,KAAK,EAAC,QAAM,EAACzE,IAAI,CAAC0E,iBAAiB,IAAI,KAAK;kBAAA;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACblG,OAAA;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNlG,OAAA,CAACrC,UAAU;oBAACkI,OAAO,EAAC,OAAO;oBAACmC,SAAS,EAAC,MAAM;oBAAApC,QAAA,GAAC,iBAC5B,EAACjC,IAAI,CAACe,aAAa,IAAI,KAAK,EAAC,mBAAiB,EAACf,IAAI,CAACN,eAAe,IAAI,GAAG;kBAAA;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC;gBAAA,eACb;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXlG,OAAA,CAACvB,OAAO;cAAAsH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GA/CQvC,IAAI,CAAChC,OAAO;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgDjB,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMoC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACjH,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACErB,OAAA,CAACvC,GAAG;MAAAmI,QAAA,gBACF5F,OAAA,CAACrC,UAAU;QAACkI,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEblG,OAAA,CAACtC,KAAK;QAACyI,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzB5F,OAAA,CAACrC,UAAU;UAACkI,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEblG,OAAA,CAAC/B,IAAI;UAACqI,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAX,QAAA,gBACzB5F,OAAA,CAAC/B,IAAI;YAACwI,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,gBACvB5F,OAAA,CAACrC,UAAU;cAACkI,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7E,YAAY,CAACM,OAAO;YAAA;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACblG,OAAA,CAACrC,UAAU;cAACkI,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7E,YAAY,CAAC4G,SAAS,IAAI,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACblG,OAAA,CAACrC,UAAU;cAACkI,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7E,YAAY,CAAC6G,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC7G,YAAY,CAAC8G,OAAO,IAAI,KAAK;YAAA;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPlG,OAAA,CAAC/B,IAAI;YAACwI,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,gBACvB5F,OAAA,CAACrC,UAAU;cAACkI,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7E,YAAY,CAAC+G,mBAAmB,IAAI,KAAK;YAAA;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACblG,OAAA,CAACrC,UAAU;cAACkI,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7E,YAAY,CAACgH,iBAAiB,IAAI,KAAK;YAAA;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACblG,OAAA,CAACrC,UAAU;cAACkI,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7E,YAAY,CAACqD,aAAa,IAAI,KAAK;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERlG,OAAA,CAACtC,KAAK;QAACyI,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClB5F,OAAA,CAACrC,UAAU;UAACkI,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEblG,OAAA,CAACpC,SAAS;UACR+I,SAAS;UACTC,KAAK,EAAC,cAAc;UACpBf,OAAO,EAAC,UAAU;UAClB1B,IAAI,EAAC,cAAc;UACnBqE,IAAI,EAAC,QAAQ;UACbpE,KAAK,EAAE3C,QAAQ,CAACG,YAAa;UAC7BiF,QAAQ,EAAE5C,gBAAiB;UAC3B1B,KAAK,EAAE,CAAC,CAACT,UAAU,CAACF,YAAa;UACjC6G,UAAU,EAAE3G,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;UACjE8G,mBAAmB,EAAE;YACnBvC,EAAE,EAAE;cAAEY,KAAK,EAAE/E,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;YAAa;UACrG,CAAE;UACFuE,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAEFlG,OAAA,CAACtB,KAAK;UAAC8I,QAAQ,EAAC,MAAM;UAACrB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM0C,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAMC,eAAe,GAAItD,QAAQ,IAAK;MACpC;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACuD,QAAQ,CAAC,IAAI,CAAC,EAAE;QACvC,OAAOvD,QAAQ,CAACwD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAChC;MACA,OAAOxD,QAAQ;IACjB,CAAC;;IAED;IACA,MAAMyD,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAI7I,UAAU,KAAK6I,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAIpG,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAACrB,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAO6C,UAAU,CAAC3B,MAAM,CAACqG,aAAa,CAAC,IAAI1E,UAAU,CAAChD,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAMwH,uBAAuB,GAAIlF,CAAC,IAAK;MACrC,MAAM+E,YAAY,GAAG/E,CAAC,CAACG,MAAM,CAACD,KAAK,CAACnB,IAAI,CAAC,CAAC;MAC1C,IAAIgG,YAAY,EAAE;QAChB;QACA,MAAMI,gBAAgB,GAAGL,iBAAiB,CAACC,YAAY,CAAC;;QAExD;QACA,MAAMK,eAAe,GAAGnI,MAAM,CAACoI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3H,SAAS,KAAKwH,gBAAgB,CAAC;QAE1E,IAAIC,eAAe,EAAE;UACnB;UACA,IAAIJ,mBAAmB,CAACI,eAAe,CAAC,EAAE;YACxC;YACA5H,WAAW,CAAC;cACV,GAAGD,QAAQ;cACXI,SAAS,EAAEwH;YACb,CAAC,CAAC;YACFtH,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb2H,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACA1H,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb2H,eAAe,EAAE,aAAaR,YAAY,sCAAsCK,eAAe,CAACH,aAAa;YAC/G,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACApH,aAAa,CAAC;YACZ,GAAGD,UAAU;YACb2H,eAAe,EAAE,UAAUR,YAAY;UACzC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACAvH,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb2H,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,oBACEzJ,OAAA,CAACvC,GAAG;MAAAmI,QAAA,gBACF5F,OAAA,CAACrC,UAAU;QAACkI,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEblG,OAAA,CAACtC,KAAK;QAACyI,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClB5F,OAAA,CAACrC,UAAU;UAACkI,OAAO,EAAC,OAAO;UAAC6D,SAAS;UAAA9D,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZnF,aAAa,gBACZf,OAAA,CAACvC,GAAG;UAAC0I,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5D5F,OAAA,CAACrB,gBAAgB;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAENlG,OAAA,CAACvC,GAAG;UAAAmI,QAAA,gBAEF5F,OAAA,CAAC/B,IAAI;YAACqI,SAAS;YAACC,OAAO,EAAE,CAAE;YAACJ,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,gBACxC5F,OAAA,CAAC/B,IAAI;cAACwI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAA3C,QAAA,eACvB5F,OAAA,CAACpC,SAAS;gBACR+I,SAAS;gBACTC,KAAK,EAAC,yBAAyB;gBAC/Bf,OAAO,EAAC,UAAU;gBAClBiB,WAAW,EAAC,8BAA8B;gBAC1C2B,UAAU,EAAE3G,UAAU,CAAC2H,eAAe,IAAI,kEAAmE;gBAC7GlH,KAAK,EAAE,CAAC,CAACT,UAAU,CAAC2H,eAAgB;gBACpCE,MAAM,EAAEP;cAAwB;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPlG,OAAA,CAAC/B,IAAI;cAACwI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAC6B,EAAE,EAAE,CAAE;cAAA3C,QAAA,eACvB5F,OAAA,CAACrC,UAAU;gBAACkI,OAAO,EAAC,OAAO;gBAACM,EAAE,EAAE;kBAAEwC,EAAE,EAAE;gBAAE,CAAE;gBAAA/C,QAAA,GAAC,sBACrB,EAACnE,QAAQ,CAACI,SAAS,IAAI,GAAG;cAAA;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPlG,OAAA,CAAC9B,WAAW;YAACyI,SAAS;YAAAf,QAAA,gBACpB5F,OAAA,CAAC7B,UAAU;cAACyL,EAAE,EAAC,qBAAqB;cAAAhE,QAAA,EAAC;YAA4B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9ElG,OAAA,CAAC5B,MAAM;cACLyL,OAAO,EAAC,qBAAqB;cAC7BD,EAAE,EAAC,eAAe;cAClBzF,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAE3C,QAAQ,CAACI,SAAU;cAC1B+E,KAAK,EAAC,8BAA8B;cACpCC,QAAQ,EAAE5C,gBAAiB;cAAA2B,QAAA,gBAE3B5F,OAAA,CAAC3B,QAAQ;gBAAC+F,KAAK,EAAC,EAAE;gBAAAwB,QAAA,eAChB5F,OAAA;kBAAA4F,QAAA,EAAI;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,EACV/E,MAAM,CAACwG,GAAG,CAAE7E,MAAM,iBACjB9C,OAAA,CAAC3B,QAAQ;gBAEP+F,KAAK,EAAEtB,MAAM,CAACjB,SAAU;gBACxBoF,QAAQ,EAAEnE,MAAM,CAACqG,aAAa,GAAG1E,UAAU,CAAChD,QAAQ,CAACG,YAAY,CAAE;gBAAAgE,QAAA,GAElEiD,eAAe,CAAC/F,MAAM,CAACjB,SAAS,CAAC,EAAC,KAAG,EAACiB,MAAM,CAACmF,SAAS,IAAI,KAAK,EAAC,cAAY,EAACnF,MAAM,CAACqG,aAAa,IAAI,CAAC,EAAC,IAC1G;cAAA,GALOrG,MAAM,CAACjB,SAAS;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKb,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTlG,OAAA,CAACpB,cAAc;cAAAgH,QAAA,EAAC;YAEhB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGbzE,QAAQ,CAACI,SAAS,iBACjB7B,OAAA,CAACvC,GAAG;YAAC0I,EAAE,EAAE;cAAEwC,EAAE,EAAE,CAAC;cAAEvC,CAAC,EAAE,CAAC;cAAE0D,OAAO,EAAE,kBAAkB;cAAEC,YAAY,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAoB,CAAE;YAAApE,QAAA,gBAClG5F,OAAA,CAACrC,UAAU;cAACkI,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZ,CAAC,MAAM;cACN,MAAMpD,MAAM,GAAG3B,MAAM,CAACoI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3H,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;cACnE,IAAIiB,MAAM,EAAE;gBACV,oBACE9C,OAAA,CAAC/B,IAAI;kBAACqI,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAX,QAAA,gBACzB5F,OAAA,CAAC/B,IAAI;oBAACwI,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAAC6B,EAAE,EAAE,CAAE;oBAAA3C,QAAA,gBACvB5F,OAAA,CAACrC,UAAU;sBAACkI,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzB5F,OAAA;wBAAA4F,QAAA,EAAQ;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC2C,eAAe,CAAC/F,MAAM,CAACjB,SAAS,CAAC;oBAAA;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACblG,OAAA,CAACrC,UAAU;sBAACkI,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzB5F,OAAA;wBAAA4F,QAAA,EAAQ;sBAAU;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACpD,MAAM,CAACmF,SAAS,IAAI,KAAK;oBAAA;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACblG,OAAA,CAACrC,UAAU;sBAACkI,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzB5F,OAAA;wBAAA4F,QAAA,EAAQ;sBAAW;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACpD,MAAM,CAACoF,YAAY,IAAI,KAAK,EAAC,KAAG,EAACpF,MAAM,CAACqF,OAAO,IAAI,KAAK;oBAAA;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACPlG,OAAA,CAAC/B,IAAI;oBAACwI,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAAC6B,EAAE,EAAE,CAAE;oBAAA3C,QAAA,gBACvB5F,OAAA,CAACrC,UAAU;sBAACkI,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzB5F,OAAA;wBAAA4F,QAAA,EAAQ;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACpD,MAAM,CAACmH,YAAY,IAAI,CAAC,EAAC,IAC3D;oBAAA;sBAAAlE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACblG,OAAA,CAACrC,UAAU;sBAACkI,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzB5F,OAAA;wBAAA4F,QAAA,EAAQ;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACpD,MAAM,CAACqG,aAAa,IAAI,CAAC,EAAC,IAC7D;oBAAA;sBAAApD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACblG,OAAA,CAACrC,UAAU;sBAACkI,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzB5F,OAAA;wBAAA4F,QAAA,EAAQ;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACpD,MAAM,CAACC,YAAY,IAAI,KAAK;oBAAA;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEX;cACA,oBACElG,OAAA,CAACrC,UAAU;gBAACkI,OAAO,EAAC,OAAO;gBAACkB,KAAK,EAAC,OAAO;gBAAAnB,QAAA,EAAC;cAE1C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAEjB,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAEA/E,MAAM,CAACoG,MAAM,KAAK,CAAC,IAAI,CAACxG,aAAa,iBACpCf,OAAA,CAACtB,KAAK;UAAC8I,QAAQ,EAAC,SAAS;UAACrB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,EAAC;QAEzC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMgE,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAMrB,eAAe,GAAItD,QAAQ,IAAK;MACpC;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACuD,QAAQ,CAAC,IAAI,CAAC,EAAE;QACvC,OAAOvD,QAAQ,CAACwD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAChC;MACA,OAAOxD,QAAQ;IACjB,CAAC;;IAED;IACA,MAAM0D,YAAY,GAAGxH,QAAQ,CAACI,SAAS,GAAGgH,eAAe,CAACpH,QAAQ,CAACI,SAAS,CAAC,GAAG,SAAS;IAEzF,oBACE7B,OAAA,CAACvC,GAAG;MAAAmI,QAAA,gBACF5F,OAAA,CAACrC,UAAU;QAACkI,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEblG,OAAA,CAACtC,KAAK;QAACyI,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClB5F,OAAA,CAACrC,UAAU;UAACkI,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEblG,OAAA,CAAC/B,IAAI;UAACqI,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAX,QAAA,gBACzB5F,OAAA,CAAC/B,IAAI;YAACwI,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,gBACvB5F,OAAA,CAACrC,UAAU;cAACkI,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7E,YAAY,CAACM,OAAO;YAAA;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACblG,OAAA,CAACrC,UAAU;cAACkI,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7E,YAAY,CAAC4G,SAAS,IAAI,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACblG,OAAA,CAACrC,UAAU;cAACkI,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7E,YAAY,CAAC6G,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC7G,YAAY,CAAC8G,OAAO,IAAI,KAAK;YAAA;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPlG,OAAA,CAAC/B,IAAI;YAACwI,IAAI;YAACC,EAAE,EAAE,EAAG;YAAC6B,EAAE,EAAE,CAAE;YAAA3C,QAAA,gBACvB5F,OAAA,CAACrC,UAAU;cAACkI,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACzE,QAAQ,CAACG,YAAY;YAAA;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACblG,OAAA,CAACrC,UAAU;cAACkI,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC+C,YAAY;YAAA;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACblG,OAAA,CAACrC,UAAU;cAACkI,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB5F,OAAA;gBAAA4F,QAAA,EAAQ;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACzB,UAAU,CAAChD,QAAQ,CAACG,YAAY,CAAC,IAAI6C,UAAU,CAACpD,YAAY,CAACqD,aAAa,CAAC,GAAG,YAAY,GAAG,UAAU;YAAA;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPlG,OAAA,CAACtB,KAAK;UAAC8I,QAAQ,EAAC,MAAM;UAACrB,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA/C,QAAA,GAAC,8EAEpC,EAACnE,QAAQ,CAACI,SAAS,IAAI,gFAAgF;QAAA;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMiE,cAAc,GAAIC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAOzE,WAAW,CAAC,CAAC;MACtB,KAAK,CAAC;QACJ,OAAO2C,WAAW,CAAC,CAAC;MACtB,KAAK,CAAC;QACJ,OAAOM,WAAW,CAAC,CAAC;MACtB,KAAK,CAAC;QACJ,OAAOsB,WAAW,CAAC,CAAC;MACtB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;EAED,oBACElK,OAAA,CAACvC,GAAG;IAAAmI,QAAA,gBACF5F,OAAA,CAAClC,OAAO;MAAC2C,UAAU,EAAEA,UAAW;MAAC0F,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EAC5C1D,KAAK,CAACyF,GAAG,CAAEf,KAAK,iBACf5G,OAAA,CAACjC,IAAI;QAAA6H,QAAA,eACH5F,OAAA,CAAChC,SAAS;UAAA4H,QAAA,EAAEgB;QAAK;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC,GADrBU,KAAK;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEVlG,OAAA,CAACvC,GAAG;MAAC0I,EAAE,EAAE;QAAEwC,EAAE,EAAE,CAAC;QAAEtC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EACvBuE,cAAc,CAAC1J,UAAU;IAAC;MAAAsF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAENlG,OAAA,CAACvC,GAAG;MAAC0I,EAAE,EAAE;QAAEiB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEsB,EAAE,EAAE;MAAE,CAAE;MAAA/C,QAAA,gBACnE5F,OAAA,CAACnC,MAAM;QACLgI,OAAO,EAAC,UAAU;QAClBkB,KAAK,EAAC,WAAW;QACjBC,OAAO,EAAEvG,UAAU,KAAK,CAAC,GAAG,MAAMD,QAAQ,CAAC,sBAAsB,CAAC,GAAGyE,UAAW;QAChFiC,SAAS,eAAElH,OAAA,CAACZ,aAAa;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7Be,QAAQ,EAAEtG,OAAQ;QAAAiF,QAAA,EAEjBnF,UAAU,KAAK,CAAC,GAAG,SAAS,GAAG;MAAU;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAETlG,OAAA,CAACnC,MAAM;QACLgI,OAAO,EAAC,WAAW;QACnBkB,KAAK,EAAC,SAAS;QACfC,OAAO,EAAEvG,UAAU,KAAKyB,KAAK,CAACqF,MAAM,GAAG,CAAC,GAAGjC,YAAY,GAAGP,UAAW;QACrEsF,OAAO,EAAE5J,UAAU,KAAKyB,KAAK,CAACqF,MAAM,GAAG,CAAC,gBAAGvH,OAAA,CAACd,QAAQ;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlG,OAAA,CAACV,gBAAgB;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC/Ee,QAAQ,EAAEtG,OAAO,IAAKF,UAAU,KAAK,CAAC,IAAI,CAACY,YAAc;QAAAuE,QAAA,EAExDjF,OAAO,gBACNX,OAAA,CAACrB,gBAAgB;UAACwI,IAAI,EAAE;QAAG;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAC5BzF,UAAU,KAAKyB,KAAK,CAACqF,MAAM,GAAG,CAAC,GACjC,OAAO,GAEP;MACD;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3F,EAAA,CA7zBIJ,kBAAkB;EAAA,QACLR,WAAW;AAAA;AAAA2K,EAAA,GADxBnK,kBAAkB;AA+zBxB,eAAeA,kBAAkB;AAAC,IAAAmK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}